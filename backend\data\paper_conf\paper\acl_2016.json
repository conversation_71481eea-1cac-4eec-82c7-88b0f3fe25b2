[{"primary_key": "4048484", "vector": [], "sparse_vector": [], "title": "Neural Summarization by Extracting Sentences and Words.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traditional approaches to extractive summarization rely heavily on humanengineered features.In this work we propose a data-driven approach based on neural networks and continuous sentence features.We develop a general framework for single-document summarization composed of a hierarchical document encoder and an attention-based extractor.This architecture allows us to develop different classes of summarization models which can extract sentences or words.We train our models on large scale corpora containing hundreds of thousands of document-summary pairs 1 .Experimental results on two summarization datasets demonstrate that our models obtain results comparable to the state of the art without any access to linguistic annotation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1046"}, {"primary_key": "4048485", "vector": [], "sparse_vector": [], "title": "Query Expansion with Locally-Trained Word Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Continuous space word embeddings have received a great deal of attention in the natural language processing and machine learning communities for their ability to model term similarity and other relationships.We study the use of term relatedness in the context of query expansion for ad hoc information retrieval.We demonstrate that word embeddings such as word2vec and GloVe, when trained globally, underperform corpus and query specific embeddings for retrieval tasks.These results suggest that other tasks benefiting from global embeddings may also benefit from local embeddings.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1035"}, {"primary_key": "4048486", "vector": [], "sparse_vector": [], "title": "TransG : A Generative Model for Knowledge Graph Embedding.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, knowledge graph embedding, which projects symbolic entities and relations into continuous vector space, has become a new, hot topic in artificial intelligence.This paper proposes a novel generative model (TransG) to address the issue of multiple relation semantics that a relation may have multiple meanings revealed by the entity pairs associated with the corresponding triples.The new model can discover latent semantics for a relation and leverage a mixture of relationspecific component vectors to embed a fact triple.To the best of our knowledge, this is the first generative model for knowledge graph embedding, and at the first time, the issue of multiple relation semantics is formally discussed.Extensive experiments show that the proposed model achieves substantial improvements against the state-of-the-art baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1219"}, {"primary_key": "4048487", "vector": [], "sparse_vector": [], "title": "Putting Sarcasm Detection into Context: The Effects of Class Imbalance and Manual Labelling on Supervised Machine Classification of Twitter Conversations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Sarcasm can radically alter or invert a phrase's meaning.Sarcasm detection can therefore help improve natural language processing (NLP) tasks.The majority of prior research has modeled sarcasm detection as classification, with two important limitations: 1. Balanced datasets, when sarcasm is actually rather rare.2. Using Twitter users' self-declarations in the form of hashtags to label data, when sarcasm can take many forms.To address these issues, we create an unbalanced corpus of manually annotated Twitter conversations.We compare human and machine ability to recognize sarcasm on this data under varying amounts of context.Our results indicate that both class imbalance and labelling method affect performance, and should both be considered when designing automatic sarcasm detection systems.We conclude that for progress to be made in real-world sarcasm detection, we will require a new class labelling scheme that is able to access the 'common ground' held between conversational parties.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3016"}, {"primary_key": "4048489", "vector": [], "sparse_vector": [], "title": "POLYGLOT: Multilingual Semantic Role Labeling with Unified Labels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Semantic role labeling (SRL) identifies the predicate-argument structure in text with semantic labels. It plays a key role in understanding natural language. In this paper, we present POLYGLOT, a multilingual semantic role labeling system capable of semantically parsing sentences in 9 different languages from 4 different language groups. The core of POLYGLOT are SRL models for individual languages trained with automatically generated Proposition Banks (Akbik et al., 2015). The key feature of the system is that it treats the semantic labels of the English Proposition Bank as “universal semantic labels”: Given a sentence in any of the supported languages, POLYGLOT applies the corresponding SRL and predicts English PropBank frame and role annotation. The results are then visualized to facilitate the understanding of multilingual SRL with this unified semantic representation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4001"}, {"primary_key": "4048490", "vector": [], "sparse_vector": [], "title": "AraSenTi: Large-Scale Twitter-Specific Arabic Sentiment Lexicons.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "Abdul<PERSON><PERSON>k <PERSON>"], "summary": "Sentiment Analysis (SA) is an active research area nowadays due to the tremendous interest in aggregating and evaluating opinions being disseminated by users on the Web.SA of English has been thoroughly researched; however research on SA of Arabic has just flourished.Twitter is considered a powerful tool for disseminating information and a rich resource for opinionated text containing views on many different topics.In this paper we attempt to bridge a gap in Arabic SA of Twitter which is the lack of sentiment lexicons that are tailored for the informal language of Twitter.We generate two lexicons extracted from a large dataset of tweets using two approaches and evaluate their use in a simple lexicon based method.The evaluation is performed on internal and external datasets.The performance of these automatically generated lexicons was very promising, albeit the simple method used for classification.The best F-score obtained was 89.58% on the internal dataset and 63.1-64.7% on the external datasets.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1066"}, {"primary_key": "4048491", "vector": [], "sparse_vector": [], "title": "Unsupervised Multi-Author Document Decomposition Based on Hidden Markov Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes an unsupervised approach for segmenting a multi-author document into authorial components. The key novelty is that we utilize the sequential patterns hidden among document elements when determining their authorships. For this purpose, we adopt Hidden Markov Model (HMM) and construct a sequential probabilistic model to capture the dependencies of sequential sentences and their authorships. An unsupervised learning method is developed to initialize the HMM parameters. Experimental results on benchmark datasets have demonstrated the signiﬁcant ben-eﬁt of our idea and our approach has outperformed the state-of-the-arts on all tests. As an example of its applications, the proposed approach is applied for attributing authorship of a document and has also shown promising re-sults.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1067"}, {"primary_key": "4048492", "vector": [], "sparse_vector": [], "title": "Automatic Text Scoring Using Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Automated Text Scoring (ATS) provides a cost-effective and consistent alternative to human marking. However, in order to achieve good performance, the predictive features of the system need to be manually engineered by human experts. We introduce a model that forms word representations by learning the extent to which specific words contribute to the text's score. Using Long-Short Term Memory networks to represent the meaning of texts, we demonstrate that a fully automated framework is able to achieve excellent results over similar approaches. In an attempt to make our results more interpretable, and inspired by recent advances in visualizing neural networks, we introduce a novel method for identifying the regions of the text that the model has found more discriminative.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1068"}, {"primary_key": "4048493", "vector": [], "sparse_vector": [], "title": "Learning Text Pair Similarity with Context-sensitive Autoencoders.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a pairwise context-sensitive Autoencoder for computing text pair similarity. Our model encodes input text into context-sensitive representations and uses them to compute similarity between text pairs. Our model outperforms the state-of-the-art models in two semantic retrieval tasks and a contextual word similarity task. For retrieval, our unsupervised approach that merely ranks inputs with respect to the cosine similarity between their hidden representations shows comparable performance with the state-of-the-art supervised models and in some cases outperforms them.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1177"}, {"primary_key": "4048494", "vector": [], "sparse_vector": [], "title": "Globally Normalized Transition-Based Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1231"}, {"primary_key": "4048495", "vector": [], "sparse_vector": [], "title": "Combining Natural Logic and Shallow Reasoning for Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>eha Nayak", "<PERSON>"], "summary": "Broad domain question answering is often difficult in the absence of structured knowledge bases, and can benefit from shallow lexical methods (broad coverage) and logical reasoning (high precision).We propose an approach for incorporating both of these signals in a unified framework based on natural logic.We extend the breadth of inferences afforded by natural logic to include relational entailment (e.g., buy → own) and meronymy (e.g., a person born in a city is born the city's country).Furthermore, we train an evaluation function -akin to gameplayingto evaluate the expected truth of candidate premises on the fly.We evaluate our approach on answering multiple choice science questions, achieving strong results on the dataset.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1042"}, {"primary_key": "4048496", "vector": [], "sparse_vector": [], "title": "Linguistic Benchmarks of Online News Article Quality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Berkant Barla Cambazoglu", "<PERSON>"], "summary": "Online news editors ask themselves the same question many times: what is missing in this news article to go online?This is not an easy question to be answered by computational linguistic methods.In this work, we address this important question and characterise the constituents of news article editorial quality.More specifically, we identify 14 aspects related to the content of news articles.Through a correlation analysis, we quantify their independence and relation to assessing an article's editorial quality.We also demonstrate that the identified aspects, when combined together, can be used effectively in quality control methods for online news.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1178"}, {"primary_key": "4048498", "vector": [], "sparse_vector": [], "title": "Alleviating Poor Context with Background Knowledge for Named Entity Disambiguation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Named Entity Disambiguation (NED) algorithms disambiguate mentions of named entities with respect to a knowledge-base, but sometimes the context might be poor or misleading.In this paper we introduce the acquisition of two kinds of background information to alleviate that problem: entity similarity and selectional preferences for syntactic positions.We show, using a generative Näive Bayes model for NED, that the additional sources of context are complementary, and improve results in the CoNLL 2003 and TAC KBP DEL 2014 datasets, yielding the third best and the best results, respectively.We provide examples and analysis which show the value of the acquired background information.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1179"}, {"primary_key": "4048499", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Part-of-speech Tagging Using Eye-tracking Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For many of the world’s languages, there are no or very few linguistically annotated resources. On the other hand, raw text, and often also dictionaries, can be harvested from the web for many of these languages, and part-of-speech taggers can be trained with these resources. At the same time, previous research shows that eye-tracking data, which can be obtained without explicit annotation, contains clues to part-of-speech information. In this work, we bring these two ideas together and show that given raw text, a dictionary, and eye-tracking data obtained from naive participants reading text, we can train a weakly supervised PoS tagger using a second-order HMM with maximum entropy emissions. The best model use type-level aggregates of eye-tracking data and signiﬁ-cantly outperforms a baseline that does not have access to eye-tracking data.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2094"}, {"primary_key": "4048500", "vector": [], "sparse_vector": [], "title": "Nonparametric Spherical Topic Modeling with Word Embeddings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Traditional topic models do not account for semantic regularities in language.Recent distributional representations of words exhibit semantic consistency over directional metrics such as cosine similarity.However, neither categorical nor Gaussian observational distributions used in existing topic models are appropriate to leverage such correlations.In this paper, we propose to use the von <PERSON>-<PERSON> distribution to model the density of words over a unit sphere.Such a representation is well-suited for directional data.We use a Hierarchical Dirichlet Process for our base topic model and propose an efficient inference algorithm based on Stochastic Variational Inference.This model enables us to naturally exploit the semantic structures of word embeddings while flexibly discovering the number of topics.Experiments demonstrate that our method outperforms competitive approaches in terms of topic coherence on two different text corpora while offering efficient inference.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2087"}, {"primary_key": "4048502", "vector": [], "sparse_vector": [], "title": "Improved Semantic Parsers For If-Then Statements.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Digital personal assistants are becoming both more common and more useful.The major NLP challenge for personal assistants is machine understanding: translating natural language user commands into an executable representation.This paper focuses on understanding rules written as If-Then statements, though the techniques should be portable to other semantic parsing tasks.We view understanding as structure prediction and show improved models using both conventional techniques and neural network models.We also discuss various ways to improve generalization and reduce overfitting: synthetic training data from paraphrase, grammar combinations, feature selection and ensembles of multiple systems.An ensemble of these techniques achieves a new state of the art result with 8% accuracy improvement.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1069"}, {"primary_key": "4048504", "vector": [], "sparse_vector": [], "title": "LexSemTm: A Semantic Dataset Based on All-words Unsupervised Sense Distribution Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There has recently been a lot of interest in unsupervised methods for learning sense distributions, particularly in applications where sense distinctions are needed. This paper analyses a state-of-the-art method for sense distribution learning, and op-timises it for application to the entire vocabulary of a given language. The optimised method is then used to produce L EX S EM TM: a sense frequency and semantic dataset of unprecedented size, spanning approximately 88% of polyse-mous, English simplex lemmas, which is released as a public resource to the community. Finally, the quality of this data is investigated, and the L EX S EM TM sense distributions are shown to be superior to those based on the W ORD N ET ﬁrst sense for lemmas missing from S EM C OR , and at least on par with S EM C OR -based distributions otherwise.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1143"}, {"primary_key": "4048505", "vector": [], "sparse_vector": [], "title": "Learning Multiview Embeddings of Twitter Users.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Low-dimensional vector representations are widely used as stand-ins for the text of words, sentences, and entire documents.These embeddings are used to identify similar words or make predictions about documents.In this work, we consider embeddings for social media users and demonstrate that these can be used to identify users who behave similarly or to predict attributes of users.In order to capture information from all aspects of a user's online life, we take a multiview approach, applying a weighted variant of Generalized Canonical Correlation Analysis (GCCA) to a collection of over 100,000 Twitter users.We demonstrate the utility of these multiview embeddings on three downstream tasks: user engagement, friend selection, and demographic attribute prediction.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2003"}, {"primary_key": "4048506", "vector": [], "sparse_vector": [], "title": "Identifying Potential Adverse Drug Events in Tweets Using Bootstrapped Lexicons.", "authors": ["<PERSON>"], "summary": "Adverse drug events (ADEs) are medical complications co-occurring with a period of drug usage.Identification of ADEs is a primary way of evaluating available quality of care.As more social media users begin discussing their drug experiences online, public data becomes available for researchers to expand existing electronic ADE reporting systems, though non-standard language inhibits ease of analysis.In this study, portions of a new corpus of approximately 160,000 tweets were used to create a lexicon-driven ADE detection system using semi-supervised, pattern-based bootstrapping.This method was able to identify misspellings, slang terms, and other non-standard language features of social media data to drive a competitive ADE detection system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3003"}, {"primary_key": "4048507", "vector": [], "sparse_vector": [], "title": "Universal Dependencies for Learner English.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1070"}, {"primary_key": "4048508", "vector": [], "sparse_vector": [], "title": "Cross-domain Text Classification with Multiple Domains and Disparate Label Sets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advances in transfer learning have let go the limitations of traditional supervised machine learning algorithms for being dependent on annotated training data for training new models for every new domain.However, several applications encounter scenarios where models need to transfer/adapt across domains when the label sets vary both in terms of count of labels as well as their connotations.This paper presents first-of-its-kind transfer learning algorithm for cross-domain classification with multiple source domains and disparate label sets.It starts with identifying transferable knowledge from across multiple domains that can be useful for learning the target domain task.This knowledge in the form of selective labeled instances from different domains is congregated to form an auxiliary training set which is used for learning the target domain task.Experimental results validate the efficacy of the proposed algorithm against strong baselines on a real world social media and the 20 Newsgroups datasets.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1155"}, {"primary_key": "4048509", "vector": [], "sparse_vector": [], "title": "Claim Synthesis via Predicate Recycling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Computational Argumentation has two main goals - the detection and analysis of arguments on the one hand, and the synthesis of arguments on the other. Much attention has been given to the former, but considerably less to the latter. A key component in synthesizing arguments is the synthesis of claims. One way to do so is by employing argumentation mining to detect claims within an appropriate corpus. In general, this appears to be a hard problem. Thus, it is interesting to explore if - for the sake of synthesis - there may be other ways to generate claims. Here we explore such a method: we extract the predicate of simple, manually-detected, claims, and attempt to generate novel claims from them. Surprisingly, this simple method yields fairly good results.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2085"}, {"primary_key": "4048510", "vector": [], "sparse_vector": [], "title": "Extracting token-level signals of syntactic processing from fMRI - with an application to PoS induction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neuro-imaging studies on reading different parts of speech (PoS) report somewhat mixed results, yet some of them indicate different activations with different PoS.This paper addresses the difficulty of using fMRI to discriminate between linguistic tokens in reading of running text because of low temporal resolution.We show that once we solve this problem, fMRI data contains a signal of PoS distinctions to the extent that it improves PoS induction with error reductions of more than 4%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1071"}, {"primary_key": "4048511", "vector": [], "sparse_vector": [], "title": "Text Simplification as Tree Labeling.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a new, structured approach to text simplification using conditional random fields over top-down traversals of dependency graphs that jointly predicts possible compressions and paraphrases.Our model reaches readability scores comparable to word-based compression approaches across a range of metrics and human judgements while maintaining more of the important information.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2055"}, {"primary_key": "4048512", "vector": [], "sparse_vector": [], "title": "Mining Paraphrasal Typed Templates from a Plain Text Corpus.", "authors": ["Or Biran", "Terra Blevins", "<PERSON>"], "summary": "Finding paraphrases in text is an important task with implications for generation, summarization and question answering, among other applications.Of particular interest to those applications is the specific formulation of the task where the paraphrases are templated, which provides an easy way to lexicalize one message in multiple ways by simply plugging in the relevant entities.Previous work has focused on mining paraphrases from parallel and comparable corpora, or mining very short sub-sentence synonyms and paraphrases.In this paper we present an approach which combines distributional and KB-driven methods to allow robust mining of sentence-level paraphrasal templates, utilizing a rich type system for the slots, from a plain text corpus.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1180"}, {"primary_key": "4048513", "vector": [], "sparse_vector": [], "title": "How to Train Dependency Parsers with Inexact Search for Joint Sentence Boundary Detection and Parsing of Entire Documents.", "authors": ["<PERSON>", "Agnieszka Falenska", "<PERSON>", "<PERSON>"], "summary": "We cast sentence boundary detection and syntactic parsing as a joint problem, so an entire text document forms a training instance for transition-based dependency parsing. When trained with an early update or max-violation strategy for inexact search, we observe that only a tiny part of these very long training instances is ever exploited. We demonstrate this effect by extending the ArcStandard transition system with swap for the joint prediction task. When we use an alternative update strategy, our models are considerably better on both tasks and train in substantially less time compared to models trained with early update/max-violation. A comparison between a standard pipeline and our joint model furthermore empirically shows the usefulness of syntactic information on the task of sentence boundary detection.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1181"}, {"primary_key": "4048514", "vector": [], "sparse_vector": [], "title": "MUTT: Metric Unit TesTing for Language Generation Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Anna Rumshisky"], "summary": "Precise evaluation metrics are important for assessing progress in high-level language generation tasks such as machine translation or image captioning.Historically, these metrics have been evaluated using correlation with human judgment.However, human-derived scores are often alarmingly inconsistent and are also limited in their ability to identify precise areas of weakness.In this paper, we perform a case study for metric evaluation by measuring the effect that systematic sentence transformations (e.g.active to passive voice) have on the automatic metric scores.These sentence \"corruptions\" serve as unit tests for precisely measuring the strengths and weaknesses of a given metric.We find that not only are human annotations heavily inconsistent in this study, but that the Metric Unit TesT analysis is able to capture precise shortcomings of particular metrics (e.g.comparing passive and active sentences) better than a simple correlation with human judgment can.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1182"}, {"primary_key": "4048515", "vector": [], "sparse_vector": [], "title": "N-gram language models for massively parallel devices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For many applications, the query speed of N -gram language models is a computational bottleneck.Although massively parallel hardware like GPUs offer a potential solution to this bottleneck, exploiting this hardware requires a careful rethinking of basic algorithms and data structures.We present the first language model designed for such hardware, using B-trees to maximize data parallelism and minimize memory footprint and latency.Compared with a single-threaded instance of KenLM (<PERSON><PERSON><PERSON>, 2011), a highly optimized CPUbased language model, our GPU implementation produces identical results with a smaller memory footprint and a sixfold increase in throughput on a batch query task.When we saturate both devices, the GPU delivers nearly twice the throughput per hardware dollar even when the CPU implementation uses faster data structures.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1183"}, {"primary_key": "4048516", "vector": [], "sparse_vector": [], "title": "Generalized Transition-based Dependency Parsing via Control Parameters.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present a generalized transition-based parsing framework where parsers are instantiated in terms of a set of control parameters that constrain transitions between parser states.This generalization provides a unified framework to describe and compare various transitionbased parsing approaches from both a theoretical and empirical perspective.This includes well-known transition systems, but also previously unstudied systems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1015"}, {"primary_key": "4048518", "vector": [], "sparse_vector": [], "title": "Learning Prototypical Event Structure from Photo Albums.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Activities and events in our lives are structural, be it a vacation, a camping trip, or a wedding.While individual details vary, there are characteristic patterns that are specific to each of these scenarios.For example, a wedding typically consists of a sequence of events such as walking down the aisle, exchanging vows, and dancing.In this paper, we present a data-driven approach to learning event knowledge from a large collection of photo albums.We formulate the task as constrained optimization to induce the prototypical temporal structure of an event, integrating both visual and textual cues.Comprehensive evaluation demonstrates that it is possible to learn multimodal knowledge of event structure from noisy web content.-Ring 'me.-Exchanging our rings.-Rings and promises. Kiss-Our first ever kiss.-You may kiss the bride.-Sealed with a kiss.Cut the cake -Cake cuBng.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1167"}, {"primary_key": "4048519", "vector": [], "sparse_vector": [], "title": "A Fast Unified Model for Parsing and Sentence Understanding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1139"}, {"primary_key": "4048520", "vector": [], "sparse_vector": [], "title": "Bootstrapped Text-level Named Entity Recognition for Literature.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a named entity recognition (NER) system for tagging fiction: LitNER.Relative to more traditional approaches, LitNER has two important properties: (1) it makes no use of handtagged data or gazetteers, instead it bootstraps a model from term clusters; and (2) it leverages multiple instances of the same name in a text.Our experiments show it to substantially outperform off-the-shelf supervised NER systems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2056"}, {"primary_key": "4048521", "vector": [], "sparse_vector": [], "title": "Prototype Synthesis for Model Laws.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "State legislatures often rely on existing text when drafting new bills.Resource and expertise constraints, which often drive this copying behavior, can be taken advantage of by lobbyists and special interest groups.These groups provide model bills, which encode policy agendas, with the intent that the models become actual law.Unfortunately, model legislation is often opaque to the public-both in source and content.In this paper we present LOBBYBACK, a system that reverse engineers model legislation from observed text.LOBBYBACK identifies clusters of bills which have text reuse and generates \"prototypes\" that represent a canonical version of the text shared between the documents.We demonstrate that LOBBY-BACK accurately reconstructs model legislation and apply it to a dataset of over 550k bills.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1149"}, {"primary_key": "4048522", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Morphological Tagging for Low-Resource Languages.", "authors": ["Jan Buys", "<PERSON>"], "summary": "Morphologically rich languages often lack the annotated linguistic resources required to develop accurate natural language processing tools.We propose models suitable for training morphological taggers with rich tagsets for low-resource languages without using direct supervision.Our approach extends existing approaches of projecting part-of-speech tags across languages, using bitext to infer constraints on the possible tags for a given word type or token.We propose a tagging model using Wsabie, a discriminative embeddingbased model with rank-based learning.In our evaluation on 11 languages, on average this model performs on par with a baseline weakly-supervised HMM, while being more scalable.Multilingual experiments show that the method performs best when projecting between related language pairs.Despite the inherently lossy projection, we show that the morphological tags predicted by our models improve the downstream performance of a parser by +0.6 LAS on average. 1 This extends, but is not fully consistent with, the set of 12 tags proposed by <PERSON><PERSON> et al. (2012).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1184"}, {"primary_key": "4048523", "vector": [], "sparse_vector": [], "title": "Neural Word Segmentation Learning for Chinese.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Most previous approaches to Chinese word segmentation formalize this problem as a character-based sequence labeling task so that only contextual information within fixed sized local windows and simple interactions between adjacent tags can be captured.In this paper, we propose a novel neural framework which thoroughly eliminates context windows and can utilize complete segmentation history.Our model employs a gated combination neural network over characters to produce distributed representations of word candidates, which are then given to a long shortterm memory (LSTM) language scoring model.Experiments on the benchmark datasets show that without the help of feature engineering as most existing approaches, our models achieve competitive or better performances with previous stateof-the-art methods.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1039"}, {"primary_key": "4048524", "vector": [], "sparse_vector": [], "title": "Bidirectional Recurrent Convolutional Neural Network for Relation Classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>"], "summary": "Relation classification is an important semantic processing task in the field of natural language processing (NLP).In this paper, we present a novel model BRCNN to classify the relation of two entities in a sentence.Some state-of-the-art systems concentrate on modeling the shortest dependency path (SDP) between two entities leveraging convolutional or recurrent neural networks.We further explore how to make full use of the dependency relations information in the SDP, by combining convolutional neural networks and twochannel recurrent neural networks with long short term memory (LSTM) units.We propose a bidirectional architecture to learn relation representations with directional information along the SDP forwards and backwards at the same time, which benefits classifying the direction of relations.Experimental results show that our method outperforms the state-of-theart approaches on the SemEval-2010 Task 8 dataset.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1072"}, {"primary_key": "4048525", "vector": [], "sparse_vector": [], "title": "How Much is 131 Million Dollars? Putting Numbers in Perspective with Compositional Descriptions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "How much is 131 million US dollars?To help readers put such numbers in context, we propose a new task of automatically generating short descriptions known as perspectives, e.g.\"$131 million is about the cost to employ everyone in Texas over a lunch period\".First, we collect a dataset of numeric mentions in news articles, where each mention is labeled with a set of rated perspectives.We then propose a system to generate these descriptions consisting of two steps: formula construction and description generation.In construction, we compose formulae from numeric facts in a knowledge base and rank the resulting formulas based on familiarity, numeric proximity and semantic compatibility.In generation, we convert a formula into natural language using a sequence-to-sequence recurrent neural network.Our system obtains a 15.2% F 1 improvement over a non-compositional baseline at formula construction and a 12.5 BLEU point improvement over a baseline description generation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1055"}, {"primary_key": "4048526", "vector": [], "sparse_vector": [], "title": "A Thorough Examination of the CNN/Daily Mail Reading Comprehension Task.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Enabling a computer to understand a document so that it can answer comprehension questions is a central, yet unsolved goal of NLP. A key factor impeding its solution by machine learned systems is the limited availability of human-annotated data. <PERSON> et al. (2015) seek to solve this problem by creating over a million training examples by pairing CNN and Daily Mail news articles with their summarized bullet points, and show that a neural network can then be trained to give good performance on this task. In this paper, we conduct a thorough examination of this new reading comprehension task. Our primary aim is to understand what depth of language understanding is required to do well on this task. We approach this from one side by doing a careful hand-analysis of a small subset of the problems and from the other by showing that simple, carefully designed systems can obtain accuracies of 73.6% and 76.6% on these two datasets, exceeding current state-of-the-art results by 7-10% and approaching what we believe is the ceiling for performance on this task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1223"}, {"primary_key": "4048527", "vector": [], "sparse_vector": [], "title": "Implicit Polarity and Implicit Aspect Recognition in Opinion Mining.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper deals with a double-implicit problem in opinion mining and sentiment analysis.We aim at identifying aspects and polarities of opinionated statements not consisting of opinion words and aspect terms.As a case study, opinion words and aspect terms are first extracted from Chinese hotel reviews, and then grouped into positive (negative) clusters and aspect term clusters.We observe that an implicit opinion and its neighbor explicit opinion tend to have the same aspect and polarity.Under the observation, we construct an implicit opinions corpus annotated with aspect class labels and polarity automatically.Aspect and polarity classifiers trained by using this corpus is used to recognize aspect and polarity of implicit opinions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2004"}, {"primary_key": "4048528", "vector": [], "sparse_vector": [], "title": "Strategies for Training Large Vocabulary Neural Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Training neural network language models over large vocabularies is computationally costly compared to count-based models such as Kneser-Ney.We present a systematic comparison of neural strategies to represent and train large vocabularies, including softmax, hierarchical softmax, target sampling, noise contrastive estimation and self normalization.We extend self normalization to be a proper estimator of likelihood and introduce an efficient variant of softmax.We evaluate each method on three popular benchmarks, examining performance on rare words, the speed/accuracy trade-off and complementarity to Kneser-Ney.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1186"}, {"primary_key": "4048529", "vector": [], "sparse_vector": [], "title": "Compressing Neural Language Models by Sparse Word Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ge Li", "<PERSON><PERSON>"], "summary": "Neural networks are among the state-ofthe-art techniques for language modeling.Existing neural language models typically map discrete words to distributed, dense vector representations.After information processing of the preceding context words by hidden layers, an output layer estimates the probability of the next word.Such approaches are time-and memory-intensive because of the large numbers of parameters for word embeddings and the output layer.In this paper, we propose to compress neural language models by sparse word representations.In the experiments, the number of parameters in our model increases very slowly with the growth of the vocabulary size, which is almost imperceptible.Moreover, our approach not only reduces the parameter space to a large extent, but also improves the performance in terms of the perplexity measure. 1", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1022"}, {"primary_key": "4048530", "vector": [], "sparse_vector": [], "title": "Chinese Zero Pronoun Resolution with Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While unsupervised anaphoric zero pronoun (AZP) resolvers have recently been shown to rival their supervised counterparts in performance, it is relatively difficult to scale them up to reach the next level of performance due to the large amount of feature engineering efforts involved and their ineffectiveness in exploiting lexical features.To address these weaknesses, we propose a supervised approach to AZP resolution based on deep neural networks, taking advantage of their ability to learn useful task-specific representations and effectively exploit lexical features via word embeddings.Our approach achieves stateof-the-art performance when resolving the Chinese AZPs in the OntoNotes corpus.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1074"}, {"primary_key": "4048531", "vector": [], "sparse_vector": [], "title": "Sentence Rewriting for Semantic Parsing.", "authors": ["<PERSON>", "Le Sun", "Xianpei Han", "<PERSON>"], "summary": "A major challenge of semantic parsing is the vocabulary mismatch problem between natural language and target ontology.In this paper, we propose a sentence rewriting based semantic parsing method, which can effectively resolve the mismatch problem by rewriting a sentence into a new form which has the same structure with its target logical form.Specifically, we propose two sentence-rewriting methods for two common types of mismatch: a dictionary-based method for 1-N mismatch and a template-based method for N-1 mismatch.We evaluate our sentence rewriting based semantic parser on the benchmark semantic parsing dataset -WEBQUESTIONS.Experimental results show that our system outperforms the base system with a 3.4% gain in F1, and generates logical forms more accurately and parses sentences more robustly.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1073"}, {"primary_key": "4048532", "vector": [], "sparse_vector": [], "title": "Implicit Discourse Relation Detection via a Deep Architecture with Gated Relevance Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Word pairs, which are one of the most easily accessible features between two text segments, have been proven to be very useful for detecting the discourse relations held between text segments.However, because of the data sparsity problem, the performance achieved by using word pair features is limited.In this paper, in order to overcome the data sparsity problem, we propose the use of word embeddings to replace the original words.Moreover, we adopt a gated relevance network to capture the semantic interaction between word pairs, and then aggregate those semantic interactions using a pooling layer to select the most informative interactions.Experimental results on Penn Discourse Tree Bank show that the proposed method without using manually designed features can achieve better performance on recognizing the discourse level relations in all of the relations.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1163"}, {"primary_key": "4048533", "vector": [], "sparse_vector": [], "title": "Semi-Supervised Learning for Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Maosong Sun", "<PERSON>"], "summary": "While end-to-end neural machine translation (NMT) has made remarkable progress recently, NMT systems only rely on parallel corpora for parameter estimation. Since parallel corpora are usually limited in quantity, quality, and coverage, especially for low-resource languages, it is appealing to exploit monolingual corpora to improve NMT. We propose a semi-supervised approach for training NMT models on the concatenation of labeled (parallel corpora) and unlabeled (monolingual corpora) data. The central idea is to reconstruct the monolingual corpora using an autoencoder, in which the source-to-target and target-to-source translation models serve as the encoder and decoder, respectively. Our approach can not only exploit the monolingual corpora of the target language, but also of the source language. Experiments on the Chinese-English dataset show that our approach achieves significant improvements over state-of-the-art SMT and NMT systems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1185"}, {"primary_key": "4048534", "vector": [], "sparse_vector": [], "title": "Online Information Retrieval for Language Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Detmar Meurers"], "summary": "The reading material used in a language learning classroom should ideally be rich in terms of the grammatical constructions and vocabulary to be taught and in line with the learner's interests.We developed an online Information Retrieval system that helps teachers search for texts appropriate in form, content, and reading level.It identifies the 87 grammatical constructions spelled out in the official English language curriculum of schools in Baden-Württemberg, Germany.The tool incorporates a classical efficient algorithm for reranking the results by assigning weights to selected constructions and prioritizing the documents containing them.Supplemented by an interactive visualization module, it allows for a multifaceted presentation and analysis of the retrieved documents.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4002"}, {"primary_key": "4048535", "vector": [], "sparse_vector": [], "title": "Document-level Sentiment Inference with Social, Faction, and Discourse Context.", "authors": ["Euns<PERSON> Choi", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a new approach for documentlevel sentiment inference, where the goal is to predict directed opinions (who feels positively or negatively towards whom) for all entities mentioned in a text.To encourage more complete and consistent predictions, we introduce an ILP that jointly models (1) sentence-and discourse-level sentiment cues, (2) factual evidence about entity factions, and (3) global constraints based on social science theories such as homophily, social balance, and reciprocity.Together, these cues allow for rich inference across groups of entities, including for example that CEOs and the companies they lead are likely to have similar sentiment towards others.We evaluate performance on new, densely labeled data that provides supervision for all pairs, complementing previous work that only labeled pairs mentioned in the same sentence.Experiments demonstrate that the global model outperforms sentence-level baselines, by providing more coherent predictions across sets of related entities.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1032"}, {"primary_key": "4048536", "vector": [], "sparse_vector": [], "title": "A Character-level Decoder without Explicit Segmentation for Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The existing machine translation systems, whether phrase-based or neural, have relied almost exclusively on word-level modelling with explicit segmentation.In this paper, we ask a fundamental question: can neural machine translation generate a character sequence without any explicit segmentation?To answer this question, we evaluate an attention-based encoderdecoder with a subword-level encoder and a character-level decoder on four language pairs-En-Cs, En-De, En-Ru and En-Fiusing the parallel corpora from WMT'15.Our experiments show that the models with a character-level decoder outperform the ones with a subword-level decoder on all of the four language pairs.Furthermore, the ensembles of neural models with a character-level decoder outperform the state-of-the-art non-neural machine translation systems on En-Cs, En-De and En-Fi and perform comparably on En-Ru.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1160"}, {"primary_key": "4048537", "vector": [], "sparse_vector": [], "title": "Improving Coreference Resolution by Learning Entity-Level Distributed Representations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A long-standing challenge in coreference resolution has been the incorporation of entity-level information - features defined over clusters of mentions instead of mention pairs. We present a neural network based coreference system that produces high-dimensional vector representations for pairs of coreference clusters. Using these representations, our system learns when combining clusters is desirable. We train the system with a learning-to-search algorithm that teaches it which local decisions (cluster merges) will lead to a high-scoring final coreference partition. The system substantially outperforms the current state-of-the-art on the English and Chinese portions of the CoNLL 2012 Shared Task dataset despite using few hand-engineered features.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1061"}, {"primary_key": "4048538", "vector": [], "sparse_vector": [], "title": "A Domain Adaptation Regularization for Denoising Autoencoders.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Finding domain invariant features is critical for successful domain adaptation and transfer learning. However, in the case of unsupervised adaptation, there is a significant risk of overfitting on source training data. Recently, a regularization for domain adaptation was proposed for deep models by (<PERSON><PERSON><PERSON> and <PERSON>, 2015). We build on their work by suggesting a more appropriate regularization for denoising autoencoders. Our model remains unsupervised and can be computed in a closed form. On standard text classification adaptation tasks, our approach yields the state of the art results, with an important reduction of the learning cost.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2005"}, {"primary_key": "4048539", "vector": [], "sparse_vector": [], "title": "Transductive Adaptation of Black Box Predictions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Access to data is critical to any machine learning component aimed at training an accurate predictive model. In reality, data is often a subject of technical and legal constraints. Data may contain sensitive topics and data owners are often reluctant to share them. Instead of access to data, they make available decision making procedures to enable predictions on new data. Under the black box classifier constraint, we build an effective domain adaptation technique which adapts classifier predictions in a transductive setting. We run experiments on text categorization datasets and show that significant gains can be achieved, especially in the unsupervised case where no labels are available in the target domain.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2053"}, {"primary_key": "4048540", "vector": [], "sparse_vector": [], "title": "Neural Greedy Constituent Parsing with Dynamic Oracles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Dynamic oracle training has shown substantial improvements for dependency parsing in various settings, but has not been explored for constituent parsing.The present article introduces a dynamic oracle for transition-based constituent parsing.Experiments on the 9 languages of the SPMRL dataset show that a neural greedy parser with morphological features, trained with a dynamic oracle, leads to accuracies comparable with the best non-reranking and non-ensemble parsers.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1017"}, {"primary_key": "4048541", "vector": [], "sparse_vector": [], "title": "A Transition-Based System for Joint Lexical and Syntactic Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a transition-based system that jointly predicts the syntactic structure and lexical units of a sentence by building two structures over the input words: a syntactic dependency tree and a forest of lexical units including multiword expressions (MWEs).This combined representation allows us to capture both the syntactic and semantic structure of MWEs, which in turn enables deeper downstream semantic analysis, especially for semicompositional MWEs.The proposed system extends the arc-standard transition system for dependency parsing with transitions for building complex lexical units.Experiments on two different data sets show that the approach significantly improves MWE identification accuracy (and sometimes syntactic accuracy) compared to existing joint approaches.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1016"}, {"primary_key": "4048542", "vector": [], "sparse_vector": [], "title": "Predicting the Compositionality of Nominal Compounds: Giving Word Embeddings a Hard Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Distributional semantic models (DSMs) are often evaluated on artificial similarity datasets containing single words or fully compositional phrases.We present a large-scale multilingual evaluation of DSMs for predicting the degree of semantic compositionality of nominal compounds on 4 datasets for English and French.We build a total of 816 DSMs and perform 2,856 evaluations using word2vec, GloVe, and PPMI-based models.In addition to the DSMs, we compare the impact of different parameters, such as level of corpus preprocessing, context window size and number of dimensions.The results obtained have a high correlation with human judgments, being comparable to or outperforming the state of the art for some datasets (<PERSON><PERSON><PERSON>'s ρ=.82 for the <PERSON> dataset).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1187"}, {"primary_key": "4048543", "vector": [], "sparse_vector": [], "title": "Dependency Parsing with Bounded Block Degree and Well-nestedness via Lagrangian Relaxation and Branch-and-Bound.", "authors": ["Caio <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1034"}, {"primary_key": "4048544", "vector": [], "sparse_vector": [], "title": "Character-based Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON> <PERSON><PERSON>"], "summary": "Neural Machine Translation (MT) has reached state-of-the-art results.However, one of the main challenges that neural MT still faces is dealing with very large vocabularies and morphologically rich languages.In this paper, we propose a neural MT system using character-based embeddings in combination with convolutional and highway layers to replace the standard lookup-based word representations.The resulting unlimited-vocabulary and affixaware source word embeddings are tested in a state-of-the-art neural MT based on an attention-based bidirectional recurrent neural network.The proposed MT scheme provides improved results even when the source language is not morphologically rich.Improvements up to 3 BLEU points are obtained in the German-English WMT task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2058"}, {"primary_key": "4048545", "vector": [], "sparse_vector": [], "title": "Morphological Smoothing and Extrapolation of Word Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Languages with rich inflectional morphology exhibit lexical data sparsity, since the word used to express a given concept will vary with the syntactic context.For instance, each count noun in Czech has 12 forms (where English uses only singular and plural).Even in large corpora, we are unlikely to observe all inflections of a given lemma.This reduces the vocabulary coverage of methods that induce continuous representations for words from distributional corpus information.We solve this problem by exploiting existing morphological resources that can enumerate a word's component morphemes.We present a latentvariable Gaussian graphical model that allows us to extrapolate continuous representations for words not observed in the training corpus, as well as smoothing the representations provided for the observed words.The latent variables represent embeddings of morphemes, which combine to create embeddings of words.Over several languages and training sizes, our model improves the embeddings for words, when evaluated on an analogy task, skip-gram predictive accuracy, and word similarity.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1156"}, {"primary_key": "4048546", "vector": [], "sparse_vector": [], "title": "Terminology Extraction with Term Variant Detection.", "authors": ["<PERSON>", "Béatrice <PERSON>"], "summary": "We introduce, TermSuite, a JAVA and UIMA-based toolkit to build terminologies from corpora.TermSuite follows the classic two steps of terminology extraction tools, the identification of term candidates and their ranking, but implements new features.It is multilingually designed, scalable, and handles term variants.We focus on the main components: UIMA Tokens Regex for defining term and variant patterns over word annotations, and the grouping component for clustering terms and variants that works both at morphological and syntactic levels.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4003"}, {"primary_key": "4048547", "vector": [], "sparse_vector": [], "title": "Incremental Parsing with Minimal Features Using Bi-Directional LSTM.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Recently, neural network approaches for parsing have largely automated the combination of individual features, but still rely on (often a larger number of) atomic features created from human linguistic intuition, and potentially omitting important global context.To further reduce feature engineering to the bare minimum, we use bi-directional LSTM sentence representations to model a parser state with only three sentence positions, which automatically identifies important aspects of the entire sentence.This model achieves state-of-the-art results among greedy dependency parsers for English.We also introduce a novel transition system for constituency parsing which does not require binarization, and together with the above architecture, achieves state-of-the-art results among greedy parsers for both English and Chinese.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2006"}, {"primary_key": "4048548", "vector": [], "sparse_vector": [], "title": "Constrained Multi-Task Learning for Automated Essay Scoring.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Supervised machine learning models for automated essay scoring (AES) usually require substantial task-specific training data in order to make accurate predictions for a particular writing task.This limitation hinders their utility, and consequently their deployment in real-world settings.In this paper, we overcome this shortcoming using a constrained multi-task pairwisepreference learning approach that enables the data from multiple tasks to be combined effectively.Furthermore, contrary to some recent research, we show that high performance AES systems can be built with little or no task-specific training data.We perform a detailed study of our approach on a publicly available dataset in scenarios where we have varying amounts of task-specific training data and in scenarios where the number of tasks increases.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1075"}, {"primary_key": "4048549", "vector": [], "sparse_vector": [], "title": "CFO: Conditional Focused Neural Question Answering with Large-scale Knowledge Bases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "How can we enable computers to automatically answer questions like \"Who created the character <PERSON>\"? Carefully built knowledge bases provide rich sources of facts.However, it remains a challenge to answer factoid questions raised in natural language due to numerous expressions of one question.In particular, we focus on the most common questions -ones that can be answered with a single fact in the knowledge base.We propose CFO, a Conditional Focused neuralnetwork-based approach to answering factoid questions with knowledge bases.Our approach first zooms in a question to find more probable candidate subject mentions, and infers the final answers with a unified conditional probabilistic framework.Powered by deep recurrent neural networks and neural embeddings, our proposed CFO achieves an accuracy of 75.7% on a dataset of 108k questions -the largest public one to date.It outperforms the current state of the art by an absolute margin of 11.8%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1076"}, {"primary_key": "4048550", "vector": [], "sparse_vector": [], "title": "Unsupervised Authorial Clustering Based on Syntactic Structure.", "authors": ["Alon Daks", "<PERSON>"], "summary": "This paper proposes a new unsupervised technique for clustering a collection of documents written by distinct individuals into authorial components. We high-light the importance of utilizing syntactic structure to cluster documents by author, and demonstrate experimental results that show the method we outline performs on par with state-of-the-art techniques. Additionally, we argue that this feature set out-performs previous methods in cases where authors consciously emulate each other’s style or are otherwise rhetorically similar.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3017"}, {"primary_key": "4048551", "vector": [], "sparse_vector": [], "title": "Improving Statistical Machine Translation Performance by Oracle-BLEU Model Re-estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel technique for training translation models for statistical machine translation by aligning source sentences to their oracle-BLEU translations.In contrast to previous approaches which are constrained to phrase training, our method also allows the re-estimation of reordering models along with the translation model.Experiments show an improvement of up to 0.8 BLEU for our approach over a competitive Arabic-English baseline trained directly on the word-aligned bitext using heuristic extraction.As an additional benefit, the phrase table size is reduced dramatically to only 3% of the original size.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2007"}, {"primary_key": "4048552", "vector": [], "sparse_vector": [], "title": "Together we stand: Siamese Networks for Similar Question Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Community Question Answering (cQA) services like Yahoo! Answers 1 , Baidu <PERSON>hi<PERSON> 2 , Quora 3 , StackOverflow 4 etc. provide a platform for interaction with experts and help users to obtain precise and accurate answers to their questions.The time lag between the user posting a question and receiving its answer could be reduced by retrieving similar historic questions from the cQA archives.The main challenge in this task is the \"lexicosyntactic\" gap between the current and the previous questions.In this paper, we propose a novel approach called \"Siamese Convolutional Neural Network for cQA (SCQA)\" to find the semantic similarity between the current and the archived questions.SCQA consist of twin convolutional neural networks with shared parameters and a contrastive loss function joining them.SCQA learns the similarity metric for question-question pairs by leveraging the question-answer pairs available in cQA forum archives.The model projects semantically similar question pairs nearer to each other and dissimilar question pairs farther away from each other in the semantic space.Experiments on large scale reallife \"Yahoo!Answers\" dataset reveals that SCQA outperforms current state-of-theart approaches based on translation models, topic models and deep neural network", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1036"}, {"primary_key": "4048553", "vector": [], "sparse_vector": [], "title": "Grapheme-to-Phoneme Models for (Almost) Any Language.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Grapheme-to-phoneme (g2p) models are rarely available in low-resource languages, as the creation of training and evaluation data is expensive and time-consuming.We use Wiktionary to obtain more than 650k word-pronunciation pairs in more than 500 languages.We then develop phoneme and language distance metrics based on phonological and linguistic knowledge; applying those, we adapt g2p models for highresource languages to create models for related low-resource languages.We provide results for models for 229 adapted languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1038"}, {"primary_key": "4048554", "vector": [], "sparse_vector": [], "title": "Tweet2Vec: Character-Based Distributed Representations for Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Text from social media provides a set of challenges that can cause traditional NLP approaches to fail.Informal language, spelling errors, abbreviations, and special characters are all commonplace in these posts, leading to a prohibitively large vocabulary size for word-level approaches.We propose a character composition model, tweet2vec, which finds vectorspace representations of whole tweets by learning complex, non-local dependencies in character sequences.The proposed model outperforms a word-level baseline at predicting user-annotated hashtags associated with the posts, doing significantly better when the input contains many outof-vocabulary words or unusual character sequences.Our tweet2vec encoder is publicly available 1 .", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2044"}, {"primary_key": "4048555", "vector": [], "sparse_vector": [], "title": "Language to Logical Form with Neural Attention.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Semantic parsing aims at mapping natural language to machine interpretable meaning representations.Traditional approaches rely on high-quality lexicons, manually-built templates, and linguistic features which are either domainor representation-specific.In this paper we present a general method based on an attention-enhanced encoder-decoder model.We encode input utterances into vector representations, and generate their logical forms by conditioning the output sequences or trees on the encoding vectors.Experimental results on four datasets show that our approach performs competitively without using hand-engineered features and is easy to adapt across domains and meaning representations.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1004"}, {"primary_key": "4048556", "vector": [], "sparse_vector": [], "title": "Investigating the Sources of Linguistic Alignment in Conversation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In conversation, speakers tend to \"accommodate\" or \"align\" to their partners, changing the style and substance of their communications to be more similar to their partners' utterances.We focus here on \"linguistic alignment,\" changes in word choice based on others' choices.Although linguistic alignment is observed across many different contexts and its degree correlates with important social factors such as power and likability, its sources are still uncertain.We build on a recent probabilistic model of alignment, using it to separate out alignment attributable to words versus word categories.We model alignment in two contexts: telephone conversations and microblog replies.Our results show evidence of alignment, but it is primarily lexical rather than categorical.Furthermore, we find that discourse acts modulate alignment substantially.This evidence supports the view that alignment is shaped by strategic communicative processes related to the ongoing discourse.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1050"}, {"primary_key": "4048557", "vector": [], "sparse_vector": [], "title": "Learning-Based Single-Document Summarization with Compression and Anaphoricity Constraints.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a discriminative model for single-document summarization that integrally combines compression and anaphoricity constraints.Our model selects textual units to include in the summary based on a rich set of sparse features whose weights are learned on a large corpus.We allow for the deletion of content within a sentence when that deletion is licensed by compression rules; in our framework, these are implemented as dependencies between subsentential units of text.Anaphoricity constraints then improve cross-sentence coherence by guaranteeing that, for each pronoun included in the summary, the pronoun's antecedent is included as well or the pronoun is rewritten as a full mention.When trained end-to-end, our final system 1 outperforms prior work on both ROUGE as well as on human judgments of linguistic quality.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1188"}, {"primary_key": "4048558", "vector": [], "sparse_vector": [], "title": "Sequence-to-Sequence Generation for Spoken Dialogue via Deep Syntax Trees and Strings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a natural language generator based on the sequence-to-sequence approach that can be trained to produce natural language strings as well as deep syntax dependency trees from input dialogue acts, and we use it to directly compare two-step generation with separate sentence planning and surface realization stages to a joint, one-step approach.We were able to train both setups successfully using very little training data.The joint setup offers better performance, surpassing state-of-the-art with regards to ngram-based scores while providing more relevant outputs.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2008"}, {"primary_key": "4048560", "vector": [], "sparse_vector": [], "title": "Verbs Taking Clausal and Non-Finite Arguments as Signals of Modality - Revisiting the Issue of Meaning Grounded in Syntax.", "authors": ["<PERSON>"], "summary": "We revisit <PERSON>'s theory about the correspondence of verb meaning and syntax and infer semantic classes from a large syntactic classification of more than 600 German verbs taking clausal and non-finite arguments.Grasping the meaning components of Levin-classes is known to be hard.We address this challenge by setting up a multi-perspective semantic characterization of the inferred classes.To this end, we link the inferred classes and their English translation to independently constructed semantic classes in three different lexicons -the German wordnet GermaNet, VerbNet and FrameNet -and perform a detailed analysis and evaluation of the resulting German-English classification (available at www.ukp.tu-darmstadt.de/modality-verbclasses/).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1077"}, {"primary_key": "4048562", "vector": [], "sparse_vector": [], "title": "On the Linearity of Semantic Change: Investigating Meaning Variation via Dynamic Graph Models.", "authors": ["Steffen Eger", "<PERSON>"], "summary": "We consider two graph models of semantic change.The first is a time-series model that relates embedding vectors from one time period to embedding vectors of previous time periods.In the second, we construct one graph for each word: nodes in this graph correspond to time points and edge weights to the similarity of the word's meaning across two time points.We apply our two models to corpora across three different languages.We find that semantic change is linear in two senses.Firstly, today's embedding vectors (= meaning) of words can be derived as linear combinations of embedding vectors of their neighbors in previous time periods.Secondly, self-similarity of words decays linearly in time.We consider both findings as new laws/hypotheses of semantic change.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2009"}, {"primary_key": "4048563", "vector": [], "sparse_vector": [], "title": "Learning Monolingual Compositional Representations via Bilingual Supervision.", "authors": ["<PERSON>", "Marine Carpuat"], "summary": "Bilingual models that capture the semantics of sentences are typically only evaluated on cross-lingual transfer tasks such as cross-lingual document categorization or machine translation.In this work, we evaluate the quality of the monolingual representations learned with a variant of the bilingual compositional model of <PERSON> (2014), when viewing translations in a second language as a semantic annotation as the original language text.We show that compositional objectives based on phrase translation pairs outperform compositional objectives based on bilingual sentences and on monolingual paraphrases.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2059"}, {"primary_key": "4048566", "vector": [], "sparse_vector": [], "title": "Joint Word Segmentation and Phonetic Category Induction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a model which jointly performs word segmentation and induces vowel categories from formant values.Vowel induction performance improves slightly over a baseline model which does not segment; segmentation performance decreases slightly from a baseline using entirely symbolic input.Our high joint performance in this idealized setting implies that problems in unsupervised speech recognition reflect the phonetic variability of real speech sounds in context.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2010"}, {"primary_key": "4048567", "vector": [], "sparse_vector": [], "title": "Tree-to-Sequence Attentional Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most of the existing Neural Machine Translation (NMT) models focus on the conversion of sequential data and do not directly use syntactic information.We propose a novel end-to-end syntactic NMT model, extending a sequenceto-sequence model with the source-side phrase structure.Our model has an attention mechanism that enables the decoder to generate a translated word while softly aligning it with phrases as well as words of the source sentence.Experimental results on the WAT'15 Englishto-Japanese dataset demonstrate that our proposed model considerably outperforms sequence-to-sequence attentional NMT models and compares favorably with the state-of-the-art tree-to-string SMT system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1078"}, {"primary_key": "4048568", "vector": [], "sparse_vector": [], "title": "DeepLife: An Entity-aware Search, Analytics and Exploration Platform for Health and Life Sciences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite the abundance of biomedical literature and health discussions in online communities, it is often tedious to retrieve informative contents for health-centric information needs.Users can query scholarly work in PubMed by keywords and MeSH terms, and resort to Google for everything else.This demo paper presents the DeepLife system, to overcome the limitations of existing search engines for life science and health topics.DeepLife integrates large knowledge bases and harnesses entity linking methods, to support search and exploration of scientific literature, newspaper feeds, and social media, in terms of keywords and phrases, biomedical entities, and taxonomic categories.It also provides functionality for entityaware text analytics over health-centric contents.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4004"}, {"primary_key": "4048569", "vector": [], "sparse_vector": [], "title": "Set-Theoretic Alignment for Comparable Corpora.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe and evaluate a simple method to extract parallel sentences from comparable corpora.The approach, termed STACC, is based on expanded lexical sets and the <PERSON><PERSON><PERSON> similarity coefficient.We evaluate our system against state-of-theart methods on a large range of datasets in different domains, for ten language pairs, showing that it either matches or outperforms current methods across the board and gives significantly better results on the noisiest datasets.STACC is a portable method, requiring no particular adaptation for new domains or language pairs, thus enabling the efficient mining of parallel sentences in comparable corpora.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1189"}, {"primary_key": "4048570", "vector": [], "sparse_vector": [], "title": "Neural Networks For Negation Scope Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic negation scope detection is a task that has been tackled using different classifiers and heuristics.Most systems are however 1) highly-engineered, 2) English-specific, and 3) only tested on the same genre they were trained on.We start by addressing 1) and 2) using a neural network architecture.Results obtained on data from the *SEM2012 shared task on negation scope detection show that even a simple feed-forward neural network using word-embedding features alone, performs on par with earlier classifiers, with a bi-directional LSTM outperforming all of them.We then address 3) by means of a specially-designed synthetic test set; in doing so, we explore the problem of detecting the negation scope more in depth and show that performance suffers from genre effects and differs with the type of negation considered.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1047"}, {"primary_key": "4048571", "vector": [], "sparse_vector": [], "title": "Improving Argument Overlap for Proposition-Based Summarisation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present improvements to our incremental proposition-based summariser, which is inspired by <PERSON><PERSON><PERSON> and <PERSON>'s (1978) text comprehension model.Argument overlap is a central concept in this summariser.Our new model replaces the old overlap method based on distributional similarity with one based on lexical chains.We evaluate on a new corpus of 124 summaries of educational texts, and show that our new system outperforms the old method and several stateof-the-art non-proposition-based summarisers.The experiment also verifies that the incremental nature of memory cycles is beneficial in itself, by comparing it to a non-incremental algorithm using the same underlying information.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2078"}, {"primary_key": "4048572", "vector": [], "sparse_vector": [], "title": "A Dataset for Joint Noun-Noun Compound Bracketing and Interpretation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a new, sizeable dataset of nounnoun compounds with their syntactic analysis (bracketing) and semantic relations.Derived from several established linguistic resources, such as the Penn Treebank, our dataset enables experimenting with new approaches towards a holistic analysis of noun-noun compounds, such as jointlearning of noun-noun compounds bracketing and interpretation, as well as integrating compound analysis with other tasks such as syntactic parsing.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3011"}, {"primary_key": "4048573", "vector": [], "sparse_vector": [], "title": "A Language-Independent Neural Network for Event Detection.", "authors": ["Xiaocheng Feng", "<PERSON><PERSON>", "Duyu Tang", "<PERSON>ng <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Event detection remains a challenge due to the difficulty at encoding the word semantics in various contexts.Previous approaches heavily depend on languagespecific knowledge and pre-existing natural language processing (NLP) tools.However, compared to English, not all languages have such resources and tools available.A more promising approach is to automatically learn effective features from data, without relying on languagespecific resources.In this paper, we develop a hybrid neural network to capture both sequence and chunk information from specific contexts, and use them to train an event detector for multiple languages without any manually encoded features.Experiments show that our approach can achieve robust, efficient and accurate results for multiple languages (English, Chinese and Spanish).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2011"}, {"primary_key": "4048574", "vector": [], "sparse_vector": [], "title": "Towards more variation in text generation: Developing and evaluating variation models for choice of referential form.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this study, we introduce a nondeterministic method for referring expression generation. We describe two models that account for individual variation in the choice of referential form in automatically generated text: a Naive Bayes model and a Recurrent Neural Network. Both are evaluated using the VaREG corpus. Then we select the best performing model to generate referential forms in texts from the GREC-2.0 corpus and conduct an evaluation experiment in which humans judge the coherence and comprehensibility of the generated texts, comparing them both with the original references and those produced by a random baseline model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1054"}, {"primary_key": "4048575", "vector": [], "sparse_vector": [], "title": "Jointly Learning to Embed and Predict with Multiple Languages.", "authors": ["<PERSON>", "André F. T<PERSON>", "Mariana S. C. Almeida"], "summary": "We propose a joint formulation for learning task-specific cross-lingual word embeddings, along with classifiers for that task.Unlike prior work, which first learns the embeddings from parallel data and then plugs them in a supervised learning problem, our approach is oneshot: a single optimization problem combines a co-regularizer for the multilingual embeddings with a task-specific loss.We present theoretical results showing the limitation of Euclidean co-regularizers to increase the embedding dimension, a limitation which does not exist for other co-regularizers (such as the 1distance).Despite its simplicity, our method achieves state-of-the-art accuracies on the RCV1/RCV2 dataset when transferring from English to German, with training times below 1 minute.On the TED Corpus, we obtain the highest reported scores on 10 out of 11 languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1190"}, {"primary_key": "4048576", "vector": [], "sparse_vector": [], "title": "Coordination Annotation Extension in the Penn Tree Bank.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Coordination is an important and common syntactic construction which is not handled well by state of the art parsers.Coordinations in the Penn Treebank are missing internal structure in many cases, do not include explicit marking of the conjuncts and contain various errors and inconsistencies.In this work, we initiated manual annotation process for solving these issues.We identify the different elements in a coordination phrase and label each element with its function.We add phrase boundaries when these are missing, unify inconsistencies, and fix errors.The outcome is an extension of the PTB that includes consistent and detailed structures for coordinations.We make the coordination annotation publicly available, in hope that they will facilitate further research into coordination disambiguation. 1", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1079"}, {"primary_key": "4048577", "vector": [], "sparse_vector": [], "title": "Improved Parsing for Argument-Clusters Coordination.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Syntactic parsers perform poorly in prediction of Argument-Cluster Coordination (ACC). We change the PTB representation of ACC to be more suitable for learning by a statistical PCFG parser, affecting 125 trees in the training set. Training on the modified trees yields a slight improvement in EVALB scores on sections 22 and 23. The main evaluation is on a corpus of 4th grade science exams, in which ACC structures are prevalent. On this corpus, we obtain an impressive x2.7 improvement in recovering ACC structures compared to a parser trained on the original PTB trees.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2012"}, {"primary_key": "4048578", "vector": [], "sparse_vector": [], "title": "Effects of Creativity and Cluster Tightness on Short Text Clustering Performance.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Drago<PERSON>"], "summary": "Properties of corpora, such as the diversity of vocabulary and how tightly related texts cluster together, impact the best way to cluster short texts.We examine several such properties in a variety of corpora and track their effects on various combinations of similarity metrics and clustering algorithms.We show that semantic similarity metrics outperform traditional n-gram and dependency similarity metrics for kmeans clustering of a linguistically creative dataset, but do not help with less creative texts.Yet the choice of similarity metric interacts with the choice of clustering method.We find that graphbased clustering methods perform well on tightly clustered data but poorly on loosely clustered data.Semantic similarity metrics generate loosely clustered output even when applied to a tightly clustered dataset.Thus, the best performing clustering systems could not use semantic metrics.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1062"}, {"primary_key": "4048579", "vector": [], "sparse_vector": [], "title": "Analyzing Biases in Human Perception of User Age and Gender from Text.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1080"}, {"primary_key": "4048580", "vector": [], "sparse_vector": [], "title": "Supersense Embeddings: A Unified Model for Supersense Interpretation, Prediction, and Utilization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-grained semantic categories such as supersenses have proven useful for a range of downstream tasks such as question answering or machine translation.To date, no effort has been put into integrating the supersenses into distributional word representations.We present a novel joint embedding model of words and supersenses, providing insights into the relationship between words and supersenses in the same vector space.Using these embeddings in a deep neural network model, we demonstrate that the supersense enrichment leads to a significant improvement in a range of downstream classification tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1191"}, {"primary_key": "4048581", "vector": [], "sparse_vector": [], "title": "Exploring Stylistic Variation with Age and Income on Twitter.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Writing style allows NLP tools to adjust to the traits of an author.In this paper, we explore the relation between stylistic and syntactic features and authors' age and income.We confirm our hypothesis that for numerous feature types writing style is predictive of income even beyond age.We analyze the predictive power of writing style features in a regression task on two data sets of around 5,000 Twitter users each.Additionally, we use our validated features to study daily variations in writing style of users from distinct income groups.Temporal stylistic patterns not only provide novel psychological insight into user behavior, but are useful for future research and applications in social media.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2051"}, {"primary_key": "4048582", "vector": [], "sparse_vector": [], "title": "Reference Bias in Monolingual Machine Translation Evaluation.", "authors": ["<PERSON>", "Lucia <PERSON>"], "summary": "In the translation industry, human translations are assessed by comparison with the source texts.In the Machine Translation (MT) research community, however, it is a common practice to perform quality assessment using a reference translation instead of the source text.In this paper we show that this practice has a serious issue -annotators are strongly biased by the reference translation provided, and this can have a negative impact on the assessment of MT quality.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2013"}, {"primary_key": "4048583", "vector": [], "sparse_vector": [], "title": "Discovery of Treatments from Text Corpora.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An extensive literature in computational social science examines how features of messages, advertisements, and other corpora affect individuals' decisions, but these analyses must specify the relevant features of the text before the experiment.Automated text analysis methods are able to discover features of text, but these methods cannot be used to obtain the estimates of causal effects-the quantity of interest for applied researchers.We introduce a new experimental design and statistical model to simultaneously discover treatments in a corpora and estimate causal effects for these discovered treatments.We prove the conditions to identify the treatment effects of texts and introduce the supervised Indian Buffet process to discover those treatments.Our method enables us to discover treatments in a training set using a collection of texts and individuals' responses to those texts, and then estimate the effects of these interventions in a test set of new texts and survey respondents.We apply the model to an experiment about candidate biographies, recovering intuitive features of voters' decisions and revealing a penalty for lawyers and a bonus for military service.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1151"}, {"primary_key": "4048584", "vector": [], "sparse_vector": [], "title": "Situation entity types: automatic classification of clause-level aspect.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper describes the first robust approach to automatically labeling clauses with their situation entity type (<PERSON>, 2003), capturing aspectual phenomena at the clause level which are relevant for interpreting both semantics at the clause level and discourse structure.Previous work on this task used a small data set from a limited domain, and relied mainly on words as features, an approach which is impractical in larger settings.We provide a new corpus of texts from 13 genres (40,000 clauses) annotated with situation entity types.We show that our sequence labeling approach using distributional information in the form of Brown clusters, as well as syntactic-semantic features targeted to the task, is robust across genres, reaching accuracies of up to 76%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1166"}, {"primary_key": "4048585", "vector": [], "sparse_vector": [], "title": "Physical Causality of Action Verbs in Grounded Language Understanding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Linguistics studies have shown that action verbs often denote some Change of State (CoS) as the result of an action.However, the causality of action verbs and its potential connection with the physical world has not been systematically explored.To address this limitation, this paper presents a study on physical causality of action verbs and their implied changes in the physical world.We first conducted a crowdsourcing experiment and identified eighteen categories of physical causality for action verbs.For a subset of these categories, we then defined a set of detectors that detect the corresponding change from visual perception of the physical environment.We further incorporated physical causality modeling and state detection in grounded language understanding.Our empirical studies have demonstrated the effectiveness of causality modeling in grounding language to perception.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1171"}, {"primary_key": "4048586", "vector": [], "sparse_vector": [], "title": "Visualizing and Curating Knowledge Graphs over Time and Space.", "authors": ["<PERSON>e", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>ofeng Li", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Publicly available knowledge repositories, such as Wikipedia and Freebase, benefit significantly from volunteers, whose contributions ensure that the knowledge keeps expanding and is kept up-to-date and accurate.User interactions are often limited to hypertext, tabular, or graph visualization interfaces.For spatio-temporal information, however, other interaction paradigms may be better-suited.We present an integrated system that combines crowdsourcing, automatic or semi-automatic knowledge harvesting from text, and visual analytics.It enables users to analyze large quantities of structured data and unstructured textual data from a spatio-temporal perspective and gain deep insights that are not easily observed in individual facts.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4005"}, {"primary_key": "4048587", "vector": [], "sparse_vector": [], "title": "A Web-framework for ODIN Annotation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The current release of the ODIN (Online Database of Interlinear Text) database contains over 150,000 linguistic examples, from nearly 1,500 languages, extracted from PDFs found on the web, representing a significant source of data for language research, particularly for low-resource languages.Errors introduced during PDF-totext conversion or poorly formatted examples can make the task of automatically analyzing the data more difficult, so we aim to clean and normalize the examples in order to maximize accuracy during analysis.In this paper we describe a system that allows users to automatically and manually correct errors in the source data in order to get the best possible analysis of the data.We also describe a RESTful service for managing collections of linguistic examples on the web.All software is distributed under an open-source license.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4006"}, {"primary_key": "4048588", "vector": [], "sparse_vector": [], "title": "Cross-lingual projection for class-based language models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a cross-lingual projection technique for training class-based language models.We borrow from previous success in projecting POS tags and NER mentions to that of a trained classbased language model.We use a CRF to train a model to predict when a sequence of words is a member of a given class and use this to label our language model training data.We show that we can successfully project the contextual cues for these classes across pairs of languages and retain a high quality class model in languages with no supervised class data.We present empirical results that show the quality of the projected models as well as their effect on the down-stream speech recognition objective.We are able to achieve over 70% of the WER reduction when using the projected class models as compared to models trained on human annotations.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2014"}, {"primary_key": "4048589", "vector": [], "sparse_vector": [], "title": "Event Nugget Detection with Forward-Backward Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Traditional event detection methods heavily rely on manually engineered rich features.Recent deep learning approaches alleviate this problem by automatic feature engineering.But such efforts, like tradition methods, have so far only focused on single-token event mentions, whereas in practice events can also be a phrase.We instead use forward-backward recurrent neural networks (FBRNNs) to detect events that can be either words or phrases.To the best our knowledge, this is one of the first efforts to handle multi-word events and also the first attempt to use RNNs for event detection.Experimental results demonstrate that FBRNN is competitive with the state-of-the-art methods on the ACE 2005 and the Rich ERE 2015 event detection tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2060"}, {"primary_key": "4048590", "vector": [], "sparse_vector": [], "title": "Coarse-grained Argumentation Features for Scoring Persuasive Essays.", "authors": ["<PERSON><PERSON><PERSON>", "Aquila Khanam", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Scoring the quality of persuasive essays is an important goal of discourse analysis, addressed most recently with highlevel persuasion-related features such as thesis clarity, or opinions and their targets.We investigate whether argumentation features derived from a coarse-grained argumentative structure of essays can help predict essays scores.We introduce a set of argumentation features related to argument components (e.g., the number of claims and premises), argument relations (e.g., the number of supported claims) and typology of argumentative structure (chains, trees).We show that these features are good predictors of human scores for TOEFL essays, both when the coarsegrained argumentative structure is manually annotated and automatically predicted.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2089"}, {"primary_key": "4048591", "vector": [], "sparse_vector": [], "title": "Natural Language Generation enhances human decision-making with uncertain information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Decision-making is often dependent on uncertain data, e.g.data associated with confidence scores or probabilities.We present a comparison of different information presentations for uncertain data and, for the first time, measure their effects on human decision-making.We show that the use of Natural Language Generation (NLG) improves decision-making under uncertainty, compared to state-of-theart graphical-based representation methods.In a task-based study with 442 adults, we found that presentations using NLG lead to 24% better decision-making on average than the graphical presentations, and to 44% better decision-making when NLG is combined with graphics.We also show that women achieve significantly better results when presented with NLG output (an 87% increase on average compared to graphical presentations).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2043"}, {"primary_key": "4048592", "vector": [], "sparse_vector": [], "title": "Collective Entity Resolution with Multi-Focal Attention.", "authors": ["<PERSON>", "Nevena Lazic", "Soumen Chakrabarti", "Amarnag Subramanya", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1059"}, {"primary_key": "4048593", "vector": [], "sparse_vector": [], "title": "Modeling Social Norms Evolution for Personalized Sentiment Classification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by the findings in social science that people's opinions are diverse and variable while together they are shaped by evolving social norms, we perform personalized sentiment classification via shared model adaptation over time.In our proposed solution, a global sentiment model is constantly updated to capture the homogeneity in which users express opinions, while personalized models are simultaneously adapted from the global model to recognize the heterogeneity of opinions from individuals.Global model sharing alleviates data sparsity issue, and individualized model adaptation enables efficient online model learning.Extensive experimentations are performed on two large review collections from Amazon and Yelp, and encouraging performance gain is achieved against several state-of-the-art transfer learning and multi-task learning based sentiment classification solutions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1081"}, {"primary_key": "4048594", "vector": [], "sparse_vector": [], "title": "Noise reduction and targeted exploration in imitation learning for Abstract Meaning Representation parsing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Semantic parsers map natural language statements into meaning representations, and must abstract over syntactic phenomena, resolve anaphora, and identify word senses to eliminate ambiguous interpretations.Abstract meaning representation (AMR) is a recent example of one such semantic formalism which, similar to a dependency parse, utilizes a graph to represent relationships between concepts (<PERSON><PERSON><PERSON> et al., 2013).As with dependency parsing, transition-based approaches are a common approach to this problem.However, when trained in the traditional manner these systems are susceptible to the accumulation of errors when they find undesirable states during greedy decoding.Imitation learning algorithms have been shown to help these systems recover from such errors.To effectively use these methods for AMR parsing we find it highly beneficial to introduce two novel extensions: noise reduction and targeted exploration.The former mitigates the noise in the feature representation, a result of the complexity of the task.The latter targets the exploration steps of imitation learning towards areas which are likely to provide the most information in the context of a large action-space.We achieve state-ofthe art results, and improve upon standard transition-based parsing by 4.7 F 1 points.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1001"}, {"primary_key": "4048595", "vector": [], "sparse_vector": [], "title": "Modeling Concept Dependencies in a Scientific Corpus.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Burns"], "summary": "Our goal is to generate reading lists for students that help them optimally learn technical material.Existing retrieval algorithms return items directly relevant to a query but do not return results to help users read about the concepts supporting their query.This is because the dependency structure of concepts that must be understood before reading material pertaining to a given query is never considered.Here we formulate an information-theoretic view of concept dependency and present methods to construct a \"concept graph\" automatically from a text corpus.We perform the first human evaluation of concept dependency edges (to be published as open data), and the results verify the feasibility of automatic approaches for inferring concepts and their dependency relations.This result can support search capabilities that may be tuned to help users learn a subject rather than retrieve documents based on a single query.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1082"}, {"primary_key": "4048596", "vector": [], "sparse_vector": [], "title": "IBC-C: A Dataset for Armed Conflict Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe the Iraq Body Count Corpus (IBC-C) dataset, the first substantial armed conflict-related dataset which can be used for conflict analysis.IBC-C provides a ground-truth dataset for conflict specific named entity recognition, slot filling, and event de-duplication.IBC-C is constructed using data collected by the Iraq Body Count project which has been recording incidents from the ongoing war in Iraq since 2003.We describe the dataset's creation, how it can be used for the above three tasks and provide initial baseline results for the first task (named entity recognition) using Hidden Markov Models, Conditional Random Fields, and Recursive Neural Networks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2061"}, {"primary_key": "4048597", "vector": [], "sparse_vector": [], "title": "Efficient techniques for parsing with tree automata.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parsing for a wide variety of grammar formalisms can be performed by intersecting finite tree automata.However, naive implementations of parsing by intersection are very inefficient.We present techniques that speed up tree-automata-based parsing, to the point that it becomes practically feasible on realistic data when applied to context-free, TAG, and graph parsing.For graph parsing, we obtain the best runtimes in the literature.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1192"}, {"primary_key": "4048598", "vector": [], "sparse_vector": [], "title": "Incorporating Copying Mechanism in Sequence-to-Sequence Learning.", "authors": ["Jiatao Gu", "Zhengdong Lu", "Hang Li", "<PERSON>"], "summary": "We address an important problem in sequence-to-sequence (Seq2Seq) learning referred to as copying, in which certain segments in the input sequence are selectively replicated in the output sequence. A similar phenomenon is observable in human language communication. For example, humans tend to repeat entity names or even long phrases in conversation. The challenge with regard to copying in Seq2Seq is that new machinery is needed to decide when to perform the operation. In this paper, we incorporate copying into neural network-based Seq2Seq learning and propose a new model called CopyNet with encoder-decoder structure. CopyNet can nicely integrate the regular way of word generation in the decoder with the new copying mechanism which can choose sub-sequences in the input sequence and put them at proper places in the output sequence. Our empirical study on both synthetic data sets and real world data sets demonstrates the efficacy of CopyNet. For example, CopyNet can outperform regular RNN-based model with remarkable margins on text summarization tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1154"}, {"primary_key": "4048599", "vector": [], "sparse_vector": [], "title": "A Fast Approach for Semantic Similar Short Texts Retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>u", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Retrieving semantic similar short texts is a crucial issue to many applications, e.g., web search, ads matching, question-answer system, and so forth. Most of the traditional methods concentrate on how to improve the precision of the similarity measurement, while current real applications need to efﬁciently explore the top similar short texts semantically re-lated to the query one. We address the efﬁciency issue in this paper by investigating the similarity strategies and incorporating them into the FAST framework (efﬁcient F r A mework for semantic similar S hort T exts retrieval). We conduct comprehensive performance evaluation on real-life data which shows that our proposed method outperforms the state-of-the-art techniques.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2015"}, {"primary_key": "4048600", "vector": [], "sparse_vector": [], "title": "Pointing the Unknown Words.", "authors": ["<PERSON><PERSON><PERSON>ülçeh<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of rare and unknown words is an important issue that can potentially effect the performance of many NLP systems, including traditional count-based and deep learning models.We propose a novel way to deal with the rare and unseen words for the neural network models using attention.Our model uses two softmax layers in order to predict the next word in conditional language models: one predicts the location of a word in the source sentence, and the other predicts a word in the shortlist vocabulary.At each timestep, the decision of which softmax layer to use is adaptively made by an MLP which is conditioned on the context.We motivate this work from a psychological evidence that humans naturally have a tendency to point towards objects in the context or the environment when the name of an object is not known.Using our proposed model, we observe improvements on two tasks, neural machine translation on the Europarl English to French parallel corpora and text summarization on the Gigaword dataset.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1014"}, {"primary_key": "4048601", "vector": [], "sparse_vector": [], "title": "Finding Non-Arbitrary Form-Meaning Systematicity Using String-Metric Learning for Kernel Regression.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Arbitrariness of the sign-the notion that the forms of words are unrelated to their meanings-is an underlying assumption of many linguistic theories.Two lines of research have recently challenged this assumption, but they produce differing characterizations of non-arbitrariness in language.Behavioral and corpus studies have confirmed the validity of localized form-meaning patterns manifested in limited subsets of the lexicon.Meanwhile, global (lexicon-wide) statistical analyses instead find diffuse form-meaning systematicity across the lexicon as a whole.We bridge the gap with an approach that can detect both local and global formmeaning systematicity in language.In the kernel regression formulation we introduce, form-meaning relationships can be used to predict words' distributional semantic vectors from their forms.Furthermore, we introduce a novel metric learning algorithm that can learn weighted edit distances that minimize kernel regression error.Our results suggest that the English lexicon exhibits far more global form-meaning systematicity than previously discovered, and that much of this systematicity is focused in localized formmeaning patterns.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1225"}, {"primary_key": "4048602", "vector": [], "sparse_vector": [], "title": "Literal and Metaphorical Senses in Compositional Distributional Semantic Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Metaphorical expressions are pervasive in natural language and pose a substantial challenge for computational semantics.The inherent compositionality of metaphor makes it an important test case for compositional distributional semantic models (CDSMs).This paper is the first to investigate whether metaphorical composition warrants a distinct treatment in the CDSM framework.We propose a method to learn metaphors as linear transformations in a vector space and find that, across a variety of semantic domains, explicitly modeling metaphor improves the resulting semantic representations.We then use these representations in a metaphor identification task, achieving a high performance of 0.82 in terms of F-score.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1018"}, {"primary_key": "4048603", "vector": [], "sparse_vector": [], "title": "Machine Translation Evaluation Meets Community Question Answering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We explore the applicability of machine translation evaluation (MTE) methods to a very different problem: answer ranking in community Question Answering.In particular, we adopt a pairwise neural network (NN) architecture, which incorporates MTE features, as well as rich syntactic and semantic embeddings, and which efficiently models complex non-linear interactions.The evaluation results show state-of-the-art performance, with sizeable contribution from both the MTE features and from the pairwise NN architecture.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2075"}, {"primary_key": "4048604", "vector": [], "sparse_vector": [], "title": "Singleton Detection using Word Embeddings and Neural Networks.", "authors": ["<PERSON><PERSON>"], "summary": "Singleton (or non-coreferential) mentions are a problem for coreference resolution systems, and identifying singletons before mentions are linked improves resolution performance.Here, a singleton detection system based on word embeddings and neural networks is presented, which achieves state-of-the-art performance (79.6% accuracy) on the CoNLL-2012 shared task development set.Extrinsic evaluation with the Stanford and Berkeley coreference resolution systems shows significant improvement for the first, but not for the latter.The results show the potential of using neural networks and word embeddings for improving both singleton detection and coreference resolution.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3010"}, {"primary_key": "4048605", "vector": [], "sparse_vector": [], "title": "Which argument is more convincing? Analyzing and predicting convincingness of Web arguments using bidirectional LSTM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new task in the field of computational argumentation in which we investigate qualitative properties of Web arguments, namely their convincingness.We cast the problem as relation classification, where a pair of arguments having the same stance to the same prompt is judged.We annotate a large datasets of 16k pairs of arguments over 32 topics and investigate whether the relation \"A is more convincing than B\" exhibits properties of total ordering; these findings are used as global constraints for cleaning the crowdsourced data.We propose two tasks: (1) predicting which argument from an argument pair is more convincing and (2) ranking all arguments to the topic based on their convincingness.We experiment with feature-rich SVM and bidirectional LSTM and obtain 0.76-0.78accuracy and 0.35-0.40<PERSON><PERSON><PERSON>'s correlation in a cross-topic evaluation.We release the newly created corpus UKPConvArg1 and the experimental software under open licenses.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1150"}, {"primary_key": "4048606", "vector": [], "sparse_vector": [], "title": "Diachronic Word Embeddings Reveal Statistical Laws of Semantic Change.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Understanding how words change their meanings over time is key to models of language and cultural evolution, but historical data on meaning is scarce, making theories hard to develop and test.Word embeddings show promise as a diachronic tool, but have not been carefully evaluated.We develop a robust methodology for quantifying semantic change by evaluating word embeddings (PPMI, SVD, word2vec) against known historical changes.We then use this methodology to reveal statistical laws of semantic evolution.Using six historical corpora spanning four languages and two centuries, we propose two quantitative laws of semantic change: (i) the law of conformity-the rate of semantic change scales with an inverse power-law of word frequency; (ii) the law of innovation-independent of frequency, words that are more polysemous have higher rates of semantic change.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1141"}, {"primary_key": "4048608", "vector": [], "sparse_vector": [], "title": "Adaptive Joint Learning of Compositional and Non-Compositional Phrase Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel method for jointly learning compositional and noncompositional phrase embeddings by adaptively weighting both types of embeddings using a compositionality scoring function.The scoring function is used to quantify the level of compositionality of each phrase, and the parameters of the function are jointly optimized with the objective for learning phrase embeddings.In experiments, we apply the adaptive joint learning method to the task of learning embeddings of transitive verb phrases, and show that the compositionality scores have strong correlation with human ratings for verb-object compositionality, substantially outperforming the previous state of the art.Moreover, our embeddings improve upon the previous best model on a transitive verb disambiguation task.We also show that a simple ensemble technique further improves the results for both tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1020"}, {"primary_key": "4048609", "vector": [], "sparse_vector": [], "title": "Empty element recovery by spinal parser operations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a spinal parsing algorithm that can jointly detect empty elements. This method achieves state-of-theart performance on English and Japanese empty element recovery problems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2016"}, {"primary_key": "4048610", "vector": [], "sparse_vector": [], "title": "Deep Reinforcement Learning with a Natural Language Action Space.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1153"}, {"primary_key": "4048611", "vector": [], "sparse_vector": [], "title": "Hidden Softmax Sequence Model for Dialogue Structure Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ping Lv", "<PERSON>"], "summary": "We propose a new unsupervised learning model, hidden softmax sequence model (HSSM), based on <PERSON><PERSON><PERSON> machine for dialogue structure analysis.The model employs three types of units in the hidden layer to discovery dialogue latent structures: softmax units which represent latent states of utterances; binary units which represent latent topics specified by dialogues; and a binary unit that represents the global general topic shared across the whole dialogue corpus.In addition, the model contains extra connections between adjacent hidden softmax units to formulate the dependency between latent states.Two different kinds of real world dialogue corpora, Twitter-Post and AirTicketBooking, are utilized for extensive comparing experiments, and the results illustrate that the proposed model outperforms sate-ofthe-art popular approaches.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1194"}, {"primary_key": "4048612", "vector": [], "sparse_vector": [], "title": "Normalized Log-Linear Interpolation of Backoff Language Models is Efficient.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that log-linearly interpolated backoff language models can be efficiently and exactly collapsed into a single normalized backoff model, contradicting <PERSON><PERSON> (2007).While prior work reported that log-linear interpolation yields lower perplexity than linear interpolation, normalizing at query time was impractical.We normalize the model offline in advance, which is efficient due to a recurrence relationship between the normalizing factors.To tune interpolation weights, we apply <PERSON>'s method to this convex problem and show that the derivatives can be computed efficiently in a batch process.These findings are combined in new open-source interpolation tool, which is distributed with KenLM.With 21 out-of-domain corpora, log-linear interpolation yields 72.58 perplexity on TED talks, compared to 75.91 for linear interpolation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1083"}, {"primary_key": "4048613", "vector": [], "sparse_vector": [], "title": "A Vector Space for Distributional Semantics for Entailment.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Distributional semantics creates vectorspace representations that capture many forms of semantic similarity, but their relation to semantic entailment has been less clear.We propose a vector-space model which provides a formal foundation for a distributional semantics of entailment.Using a mean-field approximation, we develop approximate inference procedures and entailment operators over vectors of probabilities of features being known (versus unknown).We use this framework to reinterpret an existing distributionalsemantic model (Word2Vec) as approximating an entailment-based model of the distributions of words in contexts, thereby predicting lexical entailment relations.In both unsupervised and semi-supervised experiments on hyponymy detection, we get substantial improvements over previous results.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1193"}, {"primary_key": "4048614", "vector": [], "sparse_vector": [], "title": "Very quaffable and great fun: Applying NLP to wine reviews.", "authors": ["<PERSON>", "<PERSON>s Lefever", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We automatically predict properties of wines on the basis of smell and flavor descriptions from experts' wine reviews.We show wine experts are capable of describing their smell and flavor experiences in wine reviews in a sufficiently consistent manner, such that we can use their descriptions to predict properties of a wine based solely on language.The experimental results show promising F-scores when using lexical and semantic information to predict the color, grape variety, country of origin, and price of a wine.This demonstrates, contrary to popular opinion, that wine experts' reviews really are informative.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2050"}, {"primary_key": "4048615", "vector": [], "sparse_vector": [], "title": "Real-Time Discovery and Geospatial Visualization of Mobility and Industry Events from Large-Scale, Heterogeneous Data Streams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of ACL-2016 System Demonstrations. 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4007"}, {"primary_key": "4048616", "vector": [], "sparse_vector": [], "title": "WikiReading: A Novel Large-scale Language Understanding Task over Wikipedia.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1145"}, {"primary_key": "4048617", "vector": [], "sparse_vector": [], "title": "Identifying Causal Relations Using Parallel Wikipedia Articles.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The automatic detection of causal relationships in text is important for natural language understanding. This task has proven to be difﬁcult, however, due to the need for world knowledge and inference. We focus on a sub-task of this problem where an open class set of linguistic markers can provide clues towards understanding causality. Unlike the explicit markers, a closed class, these markers vary signiﬁ-cantly in their linguistic forms. We leverage parallel Wikipedia corpora to identify new markers that are variations on known causal phrases, creating a training set via distant supervision. We also train a causal classiﬁer using features from the open class markers and semantic features providing contextual information. The results show that our features provide an 11.05 point absolute increase over the baseline on the task of identifying causality in text.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1135"}, {"primary_key": "4048618", "vector": [], "sparse_vector": [], "title": "Language Transfer Learning for Supervised Lexical Substitution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a framework for lexical substitution that is able to perform transfer learning across languages. Datasets for this task are available in at least three languages (English, Italian, and German). Previous work has addressed each of these tasks in isolation. In contrast, we regard the union of three shared tasks as a combined multilingual dataset. We show that a supervised system can be trained effectively, even if training and evaluation data are from different languages. Successful transfer learning between languages suggests that the learned model is in fact independent of the underlying language. We combine state-of-the-art unsupervised features obtained from syntactic word embeddings and distributional thesauri in a supervised delexicalized ranking system. Our system improves over state of the art in the full lexical substitution task in all three languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1012"}, {"primary_key": "4048619", "vector": [], "sparse_vector": [], "title": "Dependency Forest based Word Alignment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A hierarchical word alignment model that searches for k-best partial alignments on target constituent 1-best parse trees has been shown to outperform previous models. However, relying solely on 1-best parses trees might hinder the search for good alignments because 1-best trees are not necessarily the best for word alignment tasks in practice. This paper introduces a dependency forest based word alignment model, which utilizes target dependency forests in an attempt to minimize the impact on limitations attributable to 1-best parse trees. We present how k-best alignments are constructed over target-side dependency forests. Alignment experiments on the Japanese-English language pair show a relative error reduction of 4% of the alignment score compared to a model with 1-best parse trees.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3002"}, {"primary_key": "4048620", "vector": [], "sparse_vector": [], "title": "Multimodal Pivots for Image Caption Translation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an approach to improve statistical machine translation of image descriptions by multimodal pivots defined in visual space. The key idea is to perform image retrieval over a database of images that are captioned in the target language, and use the captions of the most similar images for crosslingual reranking of translation outputs. Our approach does not depend on the availability of large amounts of in-domain parallel data, but only relies on available large datasets of monolingually captioned images, and on state-of-the-art convolutional neural networks to compute image similarities. Our experimental evaluation shows improvements of 1 BLEU point over strong baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1227"}, {"primary_key": "4048622", "vector": [], "sparse_vector": [], "title": "The Enemy in Your Own Camp: How Well Can We Detect Statistically-Generated Fake Reviews - An Adversarial Study.", "authors": ["<PERSON>"], "summary": "Online reviews are a growing market, but it is struggling with fake reviews. They undermine both the value of reviews to the user, and their trust in the review sites. However, fake positive reviews can boost a business, and so a small industry producing fake reviews has developed. The two sides are facing an arms race that involves more and more natural language processing (NLP). So far, NLP has been used mostly for detection, and works well on human-generated reviews. But what happens if NLP techniques are used to generate fake reviews as well? We investigate the question in an adversarial setup, by assessing the detectability of different fake review generation strategies. We use generative models to produce reviews based on meta-information, and evaluate their effectiveness against deception-detection models. We find that meta-information helps detection, but that NLP-generated reviews conditioned on such information are also much harder to detect than conventional ones.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2057"}, {"primary_key": "4048623", "vector": [], "sparse_vector": [], "title": "The Social Impact of Natural Language Processing.", "authors": ["<PERSON>", "Shannon L. <PERSON>"], "summary": "Research in natural language processing (NLP) used to be mostly performed on anonymous corpora, with the goal of enriching linguistic analysis. Authors were either largely unknown or public figures. As we increasingly use more data from social media, this situation has changed: users are now individually identifiable, and the outcome of NLP experiments and applications can have a direct effect on their lives. This change should spawn a debate about the ethical implications of NLP, but until now, the internal discourse in the field has not followed the technological development. This position paper identifies a number of social implications that NLP research may have, and discusses their ethical significance, as well as ways to address them.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2096"}, {"primary_key": "4048624", "vector": [], "sparse_vector": [], "title": "Harnessing Deep Neural Networks with Logic Rules.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Combining deep neural networks with structured logic rules is desirable to harness flexibility and reduce uninterpretability of the neural models.We propose a general framework capable of enhancing various types of neural networks (e.g., CNNs and RNNs) with declarative first-order logic rules.Specifically, we develop an iterative distillation method that transfers the structured information of logic rules into the weights of neural networks.We deploy the framework on a CNN for sentiment analysis, and an RNN for named entity recognition.With a few highly intuitive rules, we obtain substantial improvements and achieve state-of-the-art or comparable results to previous best-performing systems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1228"}, {"primary_key": "4048625", "vector": [], "sparse_vector": [], "title": "A Latent Concept Topic Model for Robust Topic Inference Using Word Embeddings.", "authors": ["Weihua Hu", "Jun&apos;<PERSON><PERSON>"], "summary": "Uncovering thematic structures of SNS and blog posts is a crucial yet challenging task, because of the severe data sparsity induced by the short length of texts and diverse use of vocabulary.This hinders effective topic inference of traditional LDA because it infers topics based on document-level co-occurrence of words.To robustly infer topics in such contexts, we propose a latent concept topic model (LCTM).Unlike LDA, LCTM reveals topics via co-occurrence of latent concepts, which we introduce as latent variables to capture conceptual similarity of words.More specifically, LCTM models each topic as a distribution over the latent concepts, where each latent concept is a localized Gaussian distribution over the word embedding space.Since the number of unique concepts in a corpus is often much smaller than the number of unique words, LCTM is less susceptible to the data sparsity.Experiments on the 20Newsgroups show the effectiveness of LCTM in dealing with short texts as well as the capability of the model in handling held-out documents with a high degree of OOV words.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2062"}, {"primary_key": "4048626", "vector": [], "sparse_vector": [], "title": "Liberal Event Extraction and Event Schema Induction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Xiaocheng Feng", "<PERSON>ng <PERSON>", "<PERSON>", "Jiawei Han", "<PERSON><PERSON><PERSON>"], "summary": ".", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1025"}, {"primary_key": "4048627", "vector": [], "sparse_vector": [], "title": "How well do Computers Solve Math Word Problems? Large-Scale Dataset Construction and Evaluation.", "authors": ["<PERSON><PERSON>", "Shuming Shi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently a few systems for automatically solving math word problems have reported promising results. However, the datasets used for evaluation have limitations in both scale and diversity. In this paper, we build a large-scale dataset which is more than 9 times the size of previous ones, and contains many more problem types. Problems in the dataset are semi-automatically obtained from community question-answering (CQA) web pages. A ranking SVM model is trained to automatically extract problem answers from the answer text provided by CQA users, which significantly reduces human annotation cost. Experiments conducted on the new dataset lead to interesting and surprising results.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1084"}, {"primary_key": "4048629", "vector": [], "sparse_vector": [], "title": "Embeddings for Word Sense Disambiguation: An Evaluation Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent years have seen a dramatic growth in the popularity of word embeddings mainly owing to their ability to capture semantic information from massive amounts of textual content.As a result, many tasks in Natural Language Processing have tried to take advantage of the potential of these distributional models.In this work, we study how word embeddings can be used in Word Sense Disambiguation, one of the oldest tasks in Natural Language Processing and Artificial Intelligence.We propose different methods through which word embeddings can be leveraged in a state-of-the-art supervised WSD system architecture, and perform a deep analysis of how different parameters affect performance.We show how a WSD system that makes use of word embeddings alone, if designed properly, can provide significant performance improvement over a state-ofthe-art WSD system that incorporates several standard WSD features.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1085"}, {"primary_key": "4048630", "vector": [], "sparse_vector": [], "title": "Summarizing Source Code using a Neural Attention Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "High quality source code is often paired with high level summaries of the computation it performs, for example in code documentation or in descriptions posted in online forums.Such summaries are extremely useful for applications such as code search but are expensive to manually author, hence only done for a small fraction of all code that is produced.In this paper, we present the first completely datadriven approach for generating high level summaries of source code.Our model, CODE-NN , uses Long Short Term Memory (LSTM) networks with attention to produce sentences that describe C# code snippets and SQL queries.CODE-NN is trained on a new corpus that is automatically collected from StackOverflow, which we release.Experiments demonstrate strong performance on two tasks:(1) code summarization, where we establish the first end-to-end learning results and outperform strong baselines, and (2) code retrieval, where our learned model improves the state of the art on a recently introduced C# benchmark by a large margin.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1195"}, {"primary_key": "4048631", "vector": [], "sparse_vector": [], "title": "TranscRater: a Tool for Automatic Speech Recognition Quality Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present TranscRater, an open-source tool for automatic speech recognition (ASR) quality estimation (QE).The tool allows users to perform ASR evaluation bypassing the need of reference transcripts and confidence information, which is common to current assessment protocols.TranscRater includes: i) methods to extract a variety of quality indicators from (signal, transcription) pairs and ii) machine learning algorithms which make possible to build ASR QE models exploiting the extracted features.Confirming the positive results of previous evaluations, new experiments with TranscRater indicate its effectiveness both in WER prediction and transcription ranking tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4008"}, {"primary_key": "4048632", "vector": [], "sparse_vector": [], "title": "Met<PERSON>or Detection with Topic Transition, Emotion and Cognition in Context.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Metaphor is a common linguistic tool in communication, making its detection in discourse a crucial task for natural language understanding.One popular approach to this challenge is to capture semantic incohesion between a metaphor and the dominant topic of the surrounding text.While these methods are effective, they tend to overclassify target words as metaphorical when they deviate in meaning from its context.We present a new approach that (1) distinguishes literal and non-literal use of target words by examining sentence-level topic transitions and (2) captures the motivation of speakers to express emotions and abstract concepts metaphorically.Experiments on an online breast cancer discussion forum dataset demonstrate a significant improvement in metaphor detection over the state-of-theart.These experimental results also reveal a tendency toward metaphor usage in personal topics and certain emotional contexts.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1021"}, {"primary_key": "4048633", "vector": [], "sparse_vector": [], "title": "Finding the Middle Ground - A Model for Planning Satisficing Answers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To establish sophisticated dialogue systems, text planning needs to cope with congruent as well as incongruent interlocutor interests as given in everyday dialogues.Little attention has been given to this topic in text planning in contrast to dialogues that are fully aligned with anticipated user interests.When considering dialogues with congruent and incongruent interlocutor interests, dialogue partners are facing the constant challenge of finding a balance between cooperation and competition.We introduce the concept of fairness that operationalize an equal and adequate, i.e. equitable satisfaction of all interlocutors' interests.Focusing on Question-Answering (QA) settings, we describe an answer planning approach that support fair dialogues under congruent and incongruent interlocutor interests.Due to the fact that fairness is subjective per se, we present promising results from an empirical study (N=107) in which human subjects interacted with a QA system implementing the proposed approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1052"}, {"primary_key": "4048634", "vector": [], "sparse_vector": [], "title": "Building a Corpus for Japanese Wikification with Fine-Grained Entity Classes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this research, we build a Wikification corpus for advancing Japanese Entity Linking.This corpus consists of 340 Japanese newspaper articles with 25,675 entity mentions.All entity mentions are labeled by a fine-grained semantic classes (200 classes), and 19,121 mentions were successfully linked to Japanese Wikipedia articles.Even with the fine-grained semantic classes, we found it hard to define the target of entity linking annotations and to utilize the fine-grained semantic classes to improve the accuracy of entity linking.EL is useful for various NLP tasks, e.g.,", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3021"}, {"primary_key": "4048635", "vector": [], "sparse_vector": [], "title": "Tables as Semi-structured Knowledge for Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Question answering requires access to a knowledge base to check facts and reason about information.Knowledge in the form of natural language text is easy to acquire, but difficult for automated reasoning.Highly-structured knowledge bases can facilitate reasoning, but are difficult to acquire.In this paper we explore tables as a semi-structured formalism that provides a balanced compromise to this tradeoff.We first use the structure of tables to guide the construction of a dataset of over 9000 multiple-choice questions with rich alignment annotations, easily and efficiently via crowd-sourcing.We then use this annotated data to train a semistructured feature-driven model for question answering that uses tables as a knowledge base.In benchmark evaluations, we significantly outperform both a strong unstructured retrieval baseline and a highlystructured Markov Logic Network model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1045"}, {"primary_key": "4048636", "vector": [], "sparse_vector": [], "title": "Data Recombination for Neural Semantic Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Modeling crisp logical regularities is crucial in semantic parsing, making it difficult for neural models with no task-specific prior knowledge to achieve good results.In this paper, we introduce data recombination, a novel framework for injecting such prior knowledge into a model.From the training data, we induce a highprecision synchronous context-free grammar, which captures important conditional independence properties commonly found in semantic parsing.We then train a sequence-to-sequence recurrent network (RNN) model with a novel attention-based copying mechanism on datapoints sampled from this grammar, thereby teaching the model about these structural properties.Data recombination improves the accuracy of our RNN model on three semantic parsing datasets, leading to new state-of-the-art performance on the standard GeoQuery dataset for models with comparable supervision.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1002"}, {"primary_key": "4048637", "vector": [], "sparse_vector": [], "title": "A Personalized Markov Clustering and Deep Learning Approach for Arabic Text Categorization.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Text categorization has become a key research field in the NLP community. However, most works in this area are focused on Western languages ignoring other Semitic languages like Arabic. These languages are of immense political and social importance necessitating robust categorization techniques. In this paper, we present a novel three-stage technique to efficiently classify Arabic documents into different categories based on the words they contain. We leverage the significance of root-words in Arabic and incorporate a combination of Markov clustering and Deep Belief Networks to classify Arabic words into separate groups (clusters). Our approach is tested on two public datasets giving a F-Measure of 91.02%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3022"}, {"primary_key": "4048638", "vector": [], "sparse_vector": [], "title": "Joint part-of-speech and dependency projection from multiple sources.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most previous work on annotation projection has been limited to a subset of IndoEuropean languages, using only a single source language, and projecting annotation for one task at a time. In contrast, we present an Integer Linear Programming (ILP) algorithm that simultaneously projects annotation for multiple tasks from multiple source languages, relying on parallel corpora available for hundreds of languages. When training POS taggers and dependency parsers on jointly projected POS tags and syntactic dependencies using our algorithm, we obtain better performance than a standard approach on 20/23 languages using one parallel corpus; and 18/27 languages using another.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2091"}, {"primary_key": "4048639", "vector": [], "sparse_vector": [], "title": "Speech Act Modeling of Written Asynchronous Conversations with Task-Specific Embeddings and Conditional Structured Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Enamul <PERSON>"], "summary": "This paper addresses the problem of speech act recognition in written asynchronous conversations (e.g., fora, emails).We propose a class of conditional structured models defined over arbitrary graph structures to capture the conversational dependencies between sentences.Our models use sentence representations encoded by a long short term memory (LSTM) recurrent neural model.Empirical evaluation shows the effectiveness of our approach over existing ones: (i) LSTMs provide better task-specific representations, and (ii) the global joint model improves over local models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1165"}, {"primary_key": "4048640", "vector": [], "sparse_vector": [], "title": "Continuous Profile Models in ASL Syntactic Facial Expression Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To create accessible content for deaf users, we investigate automatically synthesizing animations of American Sign Language (ASL), including grammatically important facial expressions and head movements. Based on recordings of humans performing various types of syntactic face and head movements (which include idiosyncratic variation), we evaluate the efficacy of Continuous Profile Models (CPMs) at identifying an essential “latent trace” of the performance, for use in producing ASL animations. A metric-based evaluation and a study with deaf users indicated that this approach was more effective than a prior method for producing animations.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1196"}, {"primary_key": "4048641", "vector": [], "sparse_vector": [], "title": "Text Understanding with the Attention Sum Reader Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Several large cloze-style context-questionanswer datasets have been introduced recently: the CNN and Daily Mail news data and the Children's Book Test.Thanks to the size of these datasets, the associated text comprehension task is well suited for deep-learning techniques that currently seem to outperform all alternative approaches.We present a new, simple model that uses attention to directly pick the answer from the context as opposed to computing the answer using a blended representation of words in the document as is usual in similar models.This makes the model particularly suitable for questionanswering problems where the answer is a single word from the document.Ensemble of our models sets new state of the art on all evaluated datasets.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1086"}, {"primary_key": "4048642", "vector": [], "sparse_vector": [], "title": "Single-Model Encoder-Decoder with Explicit Morphological Representation for Reinflection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Morphological reinflection is the task of generating a target form given a source form, a source tag and a target tag.We propose a new way of modeling this task with neural encoder-decoder models.Our approach reduces the amount of required training data for this architecture and achieves state-of-the-art results, making encoder-decoder models applicable to morphological reinflection even for lowresource languages.We further present a new automatic correction method for the outputs based on edit trees.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2090"}, {"primary_key": "4048643", "vector": [], "sparse_vector": [], "title": "Investigating LSTMs for Joint Extraction of Opinion Entities and Relations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the use of deep bidirectional LSTMs for joint extraction of opinion entities and the IS-FROM and IS-ABOUT relations that connect them -the first such attempt using a deep learning approach.Perhaps surprisingly, we find that standard LSTMs are not competitive with a state-of-the-art CRF+ILP joint inference approach (<PERSON> and <PERSON>, 2013) to opinion entities extraction, performing below even the standalone sequencetagging CRF.Incorporating sentence-level and a novel relation-level optimization, however, allows the LSTM to identify opinion relations and to perform within 1-3% of the state-of-the-art joint model for opinion entities and the IS-FROM relation; and to perform as well as the state-of-theart for the IS-ABOUT relation -all without access to opinion lexicons, parsers and other preprocessing components required for the feature-rich CRF+ILP approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1087"}, {"primary_key": "4048644", "vector": [], "sparse_vector": [], "title": "Transition-Based Left-Corner Parsing for Identifying PTB-Style Nonlocal Dependencies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a left-corner parser which can identify nonlocal dependencies.Our parser integrates nonlocal dependency identification into a transition-based system.We use a structured perceptron which enables our parser to utilize global features captured by nonlocal dependencies.An experimental result demonstrates that our parser achieves a good balance between constituent parsing and nonlocal dependency identification.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1088"}, {"primary_key": "4048645", "vector": [], "sparse_vector": [], "title": "Evaluating Sentiment Analysis in the Context of Securities Trading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There are numerous studies suggesting that published news stories have an important effect on the direction of the stock market, its volatility, the volume of trades, and the value of individual stocks mentioned in the news.There is even some published research suggesting that automated sentiment analysis of news documents, quarterly reports, blogs and/or twitter data can be productively used as part of a trading strategy.This paper presents just such a family of trading strategies, and then uses this application to re-examine some of the tacit assumptions behind how sentiment analyzers are generally evaluated, in spite of the contexts of their application.This discrepancy comes at a cost.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1197"}, {"primary_key": "4048646", "vector": [], "sparse_vector": [], "title": "Siamese CBOW: Optimizing Word Embeddings for Sentence Representations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the Siamese Continuous Bag of Words (Siamese CBOW) model, a neural network for efficient estimation of highquality sentence embeddings. Averaging the embeddings of words in a sentence has proven to be a surprisingly successful and efficient way of obtaining sentence embeddings. However, word embeddings trained with the methods currently available are not optimized for the task of sentence representation, and, thus, likely to be suboptimal. Siamese CBOW handles this problem by training word embeddings directly for the purpose of being averaged. The underlying neural network learns word embeddings by predicting, from a sentence representation, its surrounding sentences. We show the robustness of the Siamese CBOW model by evaluating it on 20 datasets stemming from a wide variety of sources.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1089"}, {"primary_key": "4048647", "vector": [], "sparse_vector": [], "title": "Unanimous Prediction for 100% Precision with Application to Learning Semantic Mappings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Can we train a system that, on any new input, either says \"don't know\" or makes a prediction that is guaranteed to be correct?We answer the question in the affirmative provided our model family is wellspecified.Specifically, we introduce the unanimity principle: only predict when all models consistent with the training data predict the same output.We operationalize this principle for semantic parsing, the task of mapping utterances to logical forms.We develop a simple, efficient method that reasons over the infinite set of all consistent models by only checking two of the models.We prove that our method obtains 100% precision even with a modest amount of training data from a possibly adversarial distribution.Empirically, we demonstrate the effectiveness of our approach on the standard GeoQuery dataset.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1090"}, {"primary_key": "4048648", "vector": [], "sparse_vector": [], "title": "MMFeat: A Toolkit for Extracting Multi-Modal Features.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Research at the intersection of language and other modalities, most notably vision, is becoming increasingly important in nat-ural language processing. We introduce a toolkit that can be used to obtain feature representations for visual and auditory information. MMF EAT is an easy-to-use Python toolkit, which has been developed with the purpose of making non-linguistic modalities more accessible to natural language processing researchers.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4010"}, {"primary_key": "4048650", "vector": [], "sparse_vector": [], "title": "Exploring Convolutional and Recurrent Neural Networks in Sequential Labelling for Dialogue Topic Tracking.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Haizhou Li"], "summary": "Dialogue topic tracking is a sequential labelling problem of recognizing the topic state at each time step in given dialogue sequences. This paper presents various ar-tiﬁcial neural network models for dialogue topic tracking, including convolutional neural networks to account for semantics at each individual utterance, and recurrent neural networks to account for conversational contexts along multiple turns in the dialogue history. The experimental results demonstrate that our proposed models can signiﬁcantly improve the tracking performances in human-human conversations.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1091"}, {"primary_key": "4048651", "vector": [], "sparse_vector": [], "title": "Scalable Semi-Supervised Query Classification Using Matrix Sketching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The enormous scale of unlabeled text available today necessitates scalable schemes for representation learning in natural language processing.For instance, in this paper we are interested in classifying the intent of a user query.While our labeled data is quite limited, we have access to virtually an unlimited amount of unlabeled queries, which could be used to induce useful representations: for instance by principal component analysis (PCA).However, it is prohibitive to even store the data in memory due to its sheer size, let alone apply conventional batch algorithms.In this work, we apply the recently proposed matrix sketching algorithm to entirely obviate the problem with scalability (Liberty, 2013).This algorithm approximates the data within a specified memory bound while preserving the covariance structure necessary for PCA.Using matrix sketching, we significantly improve the user intent classification accuracy by leveraging large amounts of unlabeled queries.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2002"}, {"primary_key": "4048652", "vector": [], "sparse_vector": [], "title": "JEDI: Joint Entity and Relation Detection using Type Inference.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "FREEBASE contains entities and relation information but is highly incomplete.Relevant information is ubiquitous in web text, but extraction deems challenging.We present JEDI, an automated system to jointly extract typed named entities and FREEBASE relations using dependency pattern from text.An innovative method for constraint solving on entity types of multiple relations is used to disambiguate pattern.The high precision in the evaluation supports our claim that we can detect entities and relations together, alleviating the need to train a custom classifier for an entity type 1 .", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4011"}, {"primary_key": "4048653", "vector": [], "sparse_vector": [], "title": "Semantic classifications for detection of verb metaphors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2017"}, {"primary_key": "4048655", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Lexico-Semantic Transfer in Language Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Lexico-semantic knowledge of our native language provides an initial foundation for second language learning.In this paper, we investigate whether and to what extent the lexico-semantic models of the native language (L1) are transferred to the second language (L2).Specifically, we focus on the problem of lexical choice and investigate it in the context of three typologically diverse languages: Russian, Spanish and English.We show that a statistical semantic model learned from L1 data improves automatic error detection in L2 for the speakers of the respective L1.Finally, we investigate whether the semantic model learned from a particular L1 is portable to other, typologically related languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1092"}, {"primary_key": "4048656", "vector": [], "sparse_vector": [], "title": "Controlled and Balanced Dataset for Japanese Lexical Simplification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new dataset for evaluating a Japanese lexical simplification method.Previous datasets have several deficiencies.All of them substitute only a single target word, and some of them extract sentences only from newswire corpus.In addition, most of these datasets do not allow ties and integrate simplification ranking from all the annotators without considering the quality.In contrast, our dataset has the following advantages: (1) it is the first controlled and balanced dataset for Japanese lexical simplification with high correlation with human judgment and (2) the consistency of the simplification ranking is improved by allowing candidates to have ties and by considering the reliability of annotators.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3001"}, {"primary_key": "4048657", "vector": [], "sparse_vector": [], "title": "Transition-based dependency parsing with topological fields.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The topological field model is commonly used to describe the regularities in German word order.In this work, we show that topological fields can be predicted reliably using sequence labeling and that the predicted field labels can inform a transitionbased dependency parser.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2001"}, {"primary_key": "4048658", "vector": [], "sparse_vector": [], "title": "Automatic Semantic Classification of German Preposition Types: Comparing Hard and Soft Clustering Approaches across Features.", "authors": ["<PERSON>", "<PERSON> im Walde"], "summary": "This paper addresses an automatic classification of preposition types in German, comparing hard and soft clustering approaches and various window-and syntax-based co-occurrence features.We show that (i) the semantically most salient preposition features (i.e., subcategorised nouns) are the most successful, and that (ii) soft clustering approaches are required for the task but reveal quite different attitudes towards predicting ambiguity.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2042"}, {"primary_key": "4048660", "vector": [], "sparse_vector": [], "title": "Recognizing Salient Entities in Shopping Queries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over the past decade, e-Commerce has rapidly grown enabling customers to purchase products with the click of a button.But to be able to do so, one has to understand the semantics of a user query and identify that in digital lifestyle tv, digital lifestyle is a brand and tv is a product.In this paper, we develop a series of structured prediction algorithms for semantic tagging of shopping queries with the product, brand, model and product family types.We model wide variety of features and show an alternative way to capture knowledge base information using embeddings.We conduct an extensive study over 37, 000 manually annotated queries and report performance of 90.92 F 1 independent of the query length.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2018"}, {"primary_key": "4048661", "vector": [], "sparse_vector": [], "title": "Which Tumblr Post Should I Read Next?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Microblogging sites have emerged as major platforms for bloggers to create and consume posts as well as to follow other bloggers and get informed of their updates.Due to the large number of users, and the huge amount of posts they create, it becomes extremely difficult to identify relevant and interesting blog posts.In this paper, we propose a novel convex collective matrix completion (CCMC) method that effectively utilizes user-item matrix and incorporates additional user activity and topic-based signals to recommend relevant content.The key advantage of CCMC over existing methods is that it can obtain a globally optimal solution and can easily scale to large-scale matrices using <PERSON><PERSON>'s algorithm.To the best of our knowledge, this is the first work which applies and studies CCMC as a recommendation method in social media.We conduct a large scale study and show significant improvement over existing state-ofthe-art approaches.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2054"}, {"primary_key": "4048662", "vector": [], "sparse_vector": [], "title": "Improving Dependency Parsing Using Sentence Clause Charts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a method for improving the dependency parsing of complex sentences.This method assumes segmentation of input sentences into clauses and does not require to re-train a parser of one's choice.We represent a sentence clause structure using clause charts that provide a layer of embedding for each clause in the sentence.Then we formulate a parsing strategy as a two-stage process where (i) coordinated and subordinated clauses of the sentence are parsed separately with respect to the sentence clause chart and (ii) their dependency trees become subtrees of the final tree of the sentence.The object language is Czech and the parser used is a maximum spanning tree parser trained on the Prague Dependency Treebank.We have achieved an average 0.97% improvement in the unlabeled attachment score.Although the method has been designed for the dependency parsing of Czech, it is useful for other parsing techniques and languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3013"}, {"primary_key": "4048663", "vector": [], "sparse_vector": [], "title": "Incorporating Relational Knowledge into Word Representations using Subspace Regularization.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Incorporating lexical knowledge from semantic resources (e.g., WordNet ) has been shown to improve the quality of distributed word representations. This knowledge often comes in the form of relational triplets ( x, r, y ) where words x and y are connected by a relation type r . Existing methods either ignore the relation types, essentially treating the word pairs as generic related words, or employ rather restrictive assumptions to model the relational knowledge. We propose a novel approach to model relational knowledge based on low-rank subspace regularization, and conduct experiments on standard tasks to evaluate its effectiveness.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2082"}, {"primary_key": "4048664", "vector": [], "sparse_vector": [], "title": "Improving Twitter Community Detection through Contextual Sentiment Analysis.", "authors": ["<PERSON><PERSON>"], "summary": "Works on Twitter community detection have yielded new ways to extract valuable insights from social media. Through this technique, Twitter users can be grouped into different types of communities such as those who have the same interests, those who interact a lot, or those who have similar sentiments about certain topics. Computationally, information is represented as a graph, and community detection is the problem of partitioning the graph such that each community is more densely connected to each other than to the rest of the network. It has been shown that incorporating sentiment analysis can improve community detection when looking for sentiment-based communities. However, such works only perform sentiment analysis in isolation without considering the tweet’s various contextual information. Examples of these contextual information are social network structure, and conversational, author, and topic contexts. Disregarding these information poses a problem because at times, context is needed to clearly infer the sentiment of a tweet. Thus, this research aims to improve detection of sentiment-based communities on Twitter by performing contextual sentiment analysis.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3005"}, {"primary_key": "4048665", "vector": [], "sparse_vector": [], "title": "The red one!: On learning to refer to things based on discriminative properties.", "authors": ["<PERSON><PERSON>", "Nghia The Pham", "<PERSON>"], "summary": "As a first step towards agents learning to communicate about their visual environment, we propose a system that, given visual representations of a referent (CAT) and a context (SOFA), identifies their discriminative attributes, i.e., properties that distinguish them (has_tail).Moreover, although supervision is only provided in terms of discriminativeness of attributes for pairs, the model learns to assign plausible attributes to specific objects (SOFA-has_cushion).Finally, we present a preliminary experiment confirming the referential success of the predicted discriminative attributes.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2035"}, {"primary_key": "4048666", "vector": [], "sparse_vector": [], "title": "Personalized Exercises for Preposition Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a computer-assisted language learning (CALL) system that generates fill-in-the-blank items for preposition usage.The system takes a set of carrier sentences as input, chooses a preposition in each sentence as the key, and then automatically generates distractors.It personalizes item selection for the user in two ways.First, it logs items to which the user previously gave incorrect answers, and offers similar items in a future session as review.Second, it progresses from easier to harder sentences, to minimize any hindrance on preposition learning that might be posed by difficult vocabulary.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4020"}, {"primary_key": "4048667", "vector": [], "sparse_vector": [], "title": "QA-It: Classifying Non-Referential It for Question Answer Pairs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a new corpus, QA-It, for the classification of non-referential it. Our dataset is unique in a sense that it is annotated on question answer pairs collected from multiple genres, useful for developing advanced QA systems. Our annotation scheme makes clear distinctions between 4 types of it, providing guidelines for many erroneous cases. Several statistical models are built for the classification of it, showing encouraging results. To the best of our knowledge, this is the first time that such a corpus is created for question answering.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3020"}, {"primary_key": "4048668", "vector": [], "sparse_vector": [], "title": "A CALL System for Learning Preposition Usage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fill-in-the-blank items are commonly featured in computer-assisted language learning (CALL) systems. An item displays a sentence with a blank, and often proposes a number of choices for ﬁlling it. These choices should include one correct answer and several plausible distractors. We describe a system that, given an English corpus, automatically generates distractors to produce items for preposition usage. We report a comprehensive evaluation on this system, involving both experts and learners. First, we analyze the difﬁ-culty levels of machine-generated carrier sentences and distractors, comparing several methods that exploit learner error and learner revision patterns. We show that the quality of machine-generated items approaches that of human-crafted ones. Further, we investigate the extent to which mismatched L1 between the user and the learner corpora affects the quality of distractors. Finally, we measure the system’s impact on the user’s language proﬁciency in both the short and the long term.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1093"}, {"primary_key": "4048669", "vector": [], "sparse_vector": [], "title": "Deep Neural Networks for Syntactic Parsing of Morphologically Rich Languages.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Morphologically rich languages (MRL) are languages in which much of the structural information is contained at the wordlevel, leading to high level word-form variation.Historically, syntactic parsing has been mainly tackled using generative models.These models assume input features to be conditionally independent, making difficult to incorporate arbitrary features.In this paper, we investigate the greedy discriminative parser described in (<PERSON><PERSON> and <PERSON>, 2015), which relies on word embeddings, in the context of MRL.We propose to learn morphological embeddings and propagate morphological information through the tree using a recursive composition procedure.Experiments show that such embeddings can dramatically improve the average performance on different languages.Moreover, it yields state-of-the art performance for a majority of languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2093"}, {"primary_key": "4048670", "vector": [], "sparse_vector": [], "title": "Edge-Linear First-Order Dependency Parsing with Undirected Minimum Spanning Tree Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The run time complexity of state-of-theart inference algorithms in graph-based dependency parsing is super-linear in the number of input words (n).Recently, pruning algorithms for these models have shown to cut a large portion of the graph edges, with minimal damage to the resulting parse trees.Solving the inference problem in run time complexity determined solely by the number of edges (m) is hence of obvious importance.We propose such an inference algorithm for first-order models, which encodes the problem as a minimum spanning tree (MST) problem in an undirected graph.This allows us to utilize state-of-the-art undirected MST algorithms whose run time is O(m) at expectation and with a very high probability.A directed parse tree is then inferred from the undirected MST and is subsequently improved with respect to the directed parsing model through local greedy updates, both steps running in O(n) time.In experiments with 18 languages, a variant of the first-order MSTParser (<PERSON> et al., 2005b) that employs our algorithm performs very similarly to the original parser that runs an O(n 2 ) directed MST inference.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1198"}, {"primary_key": "4048671", "vector": [], "sparse_vector": [], "title": "Annotating Relation Inference in Context via Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a new annotation method for collecting data on relation inference in context.We convert the inference task to one of simple factoid question answering, allowing us to easily scale up to 16,000 high-quality examples.Our method corrects a major bias in previous evaluations, making our dataset much more realistic.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2041"}, {"primary_key": "4048672", "vector": [], "sparse_vector": [], "title": "Generative Topic Embedding: a Continuous Representation of Documents.", "authors": ["Shao<PERSON> Li", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Word embedding maps words into a lowdimensional continuous embedding space by exploiting the local word collocation patterns in a small context window.On the other hand, topic modeling maps documents onto a low-dimensional topic space, by utilizing the global word collocation patterns in the same document.These two types of patterns are complementary.In this paper, we propose a generative topic embedding model to combine the two types of patterns.In our model, topics are represented by embedding vectors, and are shared across documents.The probability of each word is influenced by both its local context and its topic.A variational inference method yields the topic embeddings as well as the topic mixing proportions for each document.Jointly they represent the document in a low-dimensional continuous space.In two document classification tasks, our method performs better than eight existing methods, with fewer features.In addition, we illustrate with an example that our method can generate coherent topics even based on only one document.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1063"}, {"primary_key": "4048673", "vector": [], "sparse_vector": [], "title": "A Persona-Based Neural Conversation Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1094"}, {"primary_key": "4048674", "vector": [], "sparse_vector": [], "title": "Topic Extraction from Microblog Posts Using Conversation Structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional topic models are ineffective for topic extraction from microblog messages since the lack of structure and context among the posts renders poor message-level word co-occurrence patterns.In this work, we organize microblog posts as conversation trees based on reposting and replying relations, which enrich context information to alleviate data sparseness.Our model generates words according to topic dependencies derived from the conversation structures.In specific, we differentiate messages as leader messages, which initiate key aspects of previously focused topics or shift the focus to different topics, and follower messages that do not introduce any new information but simply echo topics from the messages that they repost or reply.Our model captures the different extents that leader and follower messages may contain the key topical words, thus further enhances the quality of the induced topics.The results of thorough experiments demonstrate the effectiveness of our proposed model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1199"}, {"primary_key": "4048675", "vector": [], "sparse_vector": [], "title": "Commonsense Knowledge Base Completion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We enrich a curated resource of commonsense knowledge by formulating the problem as one of knowledge base completion (KBC). Most work in KBC focuses on knowledge bases like Freebase that relate entities drawn from a fixed set. However, the tuples in ConceptNet (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2012) define relations between an unbounded set of phrases. We develop neural network models for scoring tuples on arbitrary phrases and evaluate them by their ability to distinguish true held-out tuples from false ones. We find strong performance from a bilinear model using a simple additive architecture to model phrases. We manually evaluate our trained model’s ability to assign quality scores to novel tuples, finding that it can propose tuples at the same quality level as mediumconfidence tuples from ConceptNet.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1137"}, {"primary_key": "4048676", "vector": [], "sparse_vector": [], "title": "Graph-Based Translation Via Graph Segmentation.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "One major drawback of phrase-based translation is that it segments an input sentence into continuous phrases.To support linguistically informed source discontinuity, in this paper we construct graphs which combine bigram and dependency relations and propose a graph-based translation model.The model segments an input graph into connected subgraphs, each of which may cover a discontinuous phrase.We use beam search to combine translations of each subgraph left-to-right to produce a complete translation.Experiments on Chinese-English and German-English tasks show that our system is significantly better than the phrase-based model by up to +1.5/+0.5 BLEU scores.By explicitly modeling the graph segmentation, our system obtains further improvement, especially on German-English.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1010"}, {"primary_key": "4048677", "vector": [], "sparse_vector": [], "title": "Phrase-Level Combination of SMT and TM Using Constrained Word Lattice.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "Constrained translation has improved statistical machine translation (SMT) by combining it with translation memory (TM) at sentence-level.In this paper, we propose using a constrained word lattice, which encodes input phrases and TM constraints together, to combine SMT and TM at phrase-level.Experiments on English-Chinese and English-French show that our approach is significantly better than previous combination methods, including sentence-level constrained translation and a recent phrase-level combination.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2045"}, {"primary_key": "4048678", "vector": [], "sparse_vector": [], "title": "Discriminative Deep Random Walk for Network Classification.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON>", "<PERSON>"], "summary": "Deep Random Walk (DeepWalk) can learn a latent space representation for describing the topological structure of a network.However, for relational network classification, DeepWalk can be suboptimal as it lacks a mechanism to optimize the objective of the target task.In this paper, we present Discriminative Deep Random Walk (DDRW), a novel method for relational network classification.By solving a joint optimization problem, DDRW can learn the latent space representations that well capture the topological structure and meanwhile are discriminative for the network classification task.Our experimental results on several real social networks demonstrate that DDRW significantly outperforms DeepWalk on multilabel network classification tasks, while retaining the topological structure in the latent space.DDRW is stable and consistently outperforms the baseline methods by various percentages of labeled data.DDRW is also an online method that is scalable and can be naturally parallelized.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1095"}, {"primary_key": "4048679", "vector": [], "sparse_vector": [], "title": "Active Learning for Dependency Parsing with Partial Annotation.", "authors": ["Zheng<PERSON> Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1033"}, {"primary_key": "4048681", "vector": [], "sparse_vector": [], "title": "Normalising Medical Concepts in Social Media Texts by Learning Semantic Representation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Automatically recognising medical concepts mentioned in social media messages (e.g.tweets) enables several applications for enhancing health quality of people in a community, e.g.real-time monitoring of infectious diseases in population.However, the discrepancy between the type of language used in social media and medical ontologies poses a major challenge.Existing studies deal with this challenge by employing techniques, such as lexical term matching and statistical machine translation.In this work, we handle the medical concept normalisation at the semantic level.We investigate the use of neural networks to learn the transition between layman's language used in social media messages and formal medical language used in the descriptions of medical concepts in a standard ontology.We evaluate our approaches using three different datasets, where social media texts are extracted from Twitter messages and blog posts.Our experimental results show that our proposed approaches significantly and consistently outperform existing effective baselines, which achieved state-of-the-art performance on several medical concept normalisation tasks, by up to 44%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1096"}, {"primary_key": "4048682", "vector": [], "sparse_vector": [], "title": "Neural Relation Extraction with Selective Attention over Instances.", "authors": ["Yankai Lin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Distant supervised relation extraction has been widely used to find novel relational facts from text.However, distant supervision inevitably accompanies with the wrong labelling problem, and these noisy data will substantially hurt the performance of relation extraction.To alleviate this issue, we propose a sentence-level attention-based model for relation extraction.In this model, we employ convolutional neural networks to embed the semantics of sentences.Afterwards, we build sentence-level attention over multiple instances, which is expected to dynamically reduce the weights of those noisy instances.Experimental results on real-world datasets show that, our model can make full use of all informative sentences and effectively reduce the influence of wrong labelled instances.Our model achieves significant and consistent improvements on relation extraction as compared with baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1200"}, {"primary_key": "4048683", "vector": [], "sparse_vector": [], "title": "Latent Predictor Networks for Code Generation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1057"}, {"primary_key": "4048684", "vector": [], "sparse_vector": [], "title": "Word Embeddings with Limited Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies the effect of limited precision data representation and computation on word embeddings.We present a systematic evaluation of word embeddings with limited memory and discuss methods that directly train the limited precision representation with limited memory.Our results show that it is possible to use and train an 8-bit fixed-point value for word embedding without loss of performance in word/phrase similarity and dependency parsing tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2063"}, {"primary_key": "4048685", "vector": [], "sparse_vector": [], "title": "OpenDial: A Toolkit for Developing Spoken Dialogue Systems with Probabilistic Rules.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a new release of OpenDial, an open-source toolkit for building and evaluating spoken dialogue systems.The toolkit relies on an information-state architecture where the dialogue state is represented as a Bayesian network and acts as a shared memory for all system modules.The domain models are specified via probabilistic rules encoded in XML.Open-Dial has been deployed in several application domains such as human-robot interaction, intelligent tutoring systems and multi-modal in-car driver assistants.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4012"}, {"primary_key": "4048686", "vector": [], "sparse_vector": [], "title": "Using Sequence Similarity Networks to Identify Partial Cognates in Multilingual Wordlists.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Increasing amounts of digital data in historical linguistics necessitate the development of automatic methods for the detection of cognate words across languages. Recently developed methods work well on language families with moderate time depths, but they are not capable of identifying cognate morphemes in words which are only partially related. Partial cog-nacy, however, is a frequently recurring phenomenon, especially in language families with productive derivational morphology. This paper presents a pilot approach for partial cognate detection in which networks are used to represent similarities be-tween word parts and cognate morphemes are identified with help of state-of-the-art algorithms for network partitioning. The approach is tested on a newly created benchmark dataset with data from three sub-branches of Sino-Tibetan and yields very promising results, outperforming all algorithms which are not sensible to partial cognacy.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2097"}, {"primary_key": "4048687", "vector": [], "sparse_vector": [], "title": "MUSEEC: A Multilingual Text Summarization Tool.", "authors": ["Marina <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The MUSEEC (MUltilingual SEntence Extraction and Compression) summarization tool implements several extractive summarization techniques -at the level of complete and compressed sentences -that can be applied, with some minor adaptations, to documents in multiple languages.The current version of MUSEEC provides the following summarization methods: (1) MUSE -a supervised summarizer, based on a genetic algorithm (GA), that ranks document sentences and extracts top-ranking sentences into a summary, (2) POLY -an unsupervised summarizer, based on linear programming (LP), that selects the best extract of document sentences, and (3) WECOM -an unsupervised extension of POLY that compiles a document summary from compressed sentences.In this paper, we provide an overview of MUSEEC methods and its architecture in general.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4013"}, {"primary_key": "4048688", "vector": [], "sparse_vector": [], "title": "Leveraging FrameNet to Improve Automatic Event Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Frames defined in FrameNet (FN) share highly similar structures with events in ACE event extraction program.An event in ACE is composed of an event trigger and a set of arguments.Analogously, a frame in FN is composed of a lexical unit and a set of frame elements, which play similar roles as triggers and arguments of ACE events respectively.Besides having similar structures, many frames in FN actually express certain types of events.The above observations motivate us to explore whether there exists a good mapping from frames to event-types and if it is possible to improve event detection by using FN.In this paper, we propose a global inference approach to detect events in FN.Further, based on the detected results, we analyze possible mappings from frames to event-types.Finally, we improve the performance of event detection and achieve a new state-of-the-art result by using the events automatically detected from FN.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1201"}, {"primary_key": "4048689", "vector": [], "sparse_vector": [], "title": "A Sentence Interaction Network for Modeling Dependence between Sentences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modeling interactions between two sentences is crucial for a number of natural language processing tasks including Answer Selection, Dialogue Act Analysis, etc.While deep learning methods like Recurrent Neural Network or Convolutional Neural Network have been proved to be powerful for sentence modeling, prior studies paid less attention on interactions between sentences.In this work, we propose a Sentence Interaction Network (SIN) for modeling the complex interactions between two sentences.By introducing \"interaction states\" for word and phrase pairs, SIN is powerful and flexible in capturing sentence interactions for different tasks.We obtain significant improvements on Answer Selection and Dialogue Act Analysis without any feature engineering.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1053"}, {"primary_key": "4048690", "vector": [], "sparse_vector": [], "title": "Understanding Discourse on Work and Job-Related Well-Being in Public Social Media.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1099"}, {"primary_key": "4048691", "vector": [], "sparse_vector": [], "title": "Agreement-based Learning of Parallel Lexicons and Phrases from Non-Parallel Corpora.", "authors": ["<PERSON><PERSON> Liu", "<PERSON>", "Maosong Sun", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce an agreement-based approach to learning parallel lexicons and phrases from non-parallel corpora.The basic idea is to encourage two asymmetric latent-variable translation models (i.e., source-to-target and target-to-source) to agree on identifying latent phrase and word alignments.The agreement is defined at both word and phrase levels.We develop a Viterbi EM algorithm for jointly training the two unidirectional models efficiently.Experiments on the Chinese-English dataset show that agreementbased learning significantly improves both alignment and translation performance.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1097"}, {"primary_key": "4048692", "vector": [], "sparse_vector": [], "title": "Deep Fusion LSTMs for Text Semantic Matching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, there is rising interest in modelling the interactions of text pair with deep neural networks.In this paper, we propose a model of deep fusion LSTMs (DF-LSTMs) to model the strong interaction of text pair in a recursive matching way.Specifically, DF-LSTMs consist of two interdependent LSTMs, each of which models a sequence under the influence of another.We also use external memory to increase the capacity of LSTMs, thereby possibly capturing more complicated matching patterns.Experiments on two very large datasets demonstrate the efficacy of our proposed architecture.Furthermore, we present an elaborate qualitative analysis of our models, giving an intuitive understanding how our model worked.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1098"}, {"primary_key": "4048693", "vector": [], "sparse_vector": [], "title": "Improving Topic Model Clustering of Newspaper Comments for Summarisation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Online newspaper articles can accumulate comments at volumes that prevent close reading. Summarisation of the comments allows interaction at a higher level and can lead to an understanding of the over-all discussion. Comment summarisation requires topic clustering, comment ranking and extraction. Clustering must be robust as the subsequent extraction relies on a good set of clusters. Comment data, as with many social media datasets, contains very short documents and the number of words in the documents is a limiting factors on the performance of LDA clustering. We evaluate whether we can combine comments to form larger documents to improve the quality of clusters. We ﬁnd that combining comments with comments that reply to them produce the highest quality clusters.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3007"}, {"primary_key": "4048694", "vector": [], "sparse_vector": [], "title": "Metrics for Evaluation of Word-level Machine Translation Quality Estimation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lucia <PERSON>"], "summary": "The aim of this paper is to investigate suitable evaluation strategies for the task of word-level quality estimation of machine translation.We suggest various metrics to replace F 1 -score for the \"BAD\" class, which is currently used as main metric.We compare the metrics' performance on real system outputs and synthetically generated datasets and suggest a reliable alternative to the F 1 -BAD score -the multiplication of F 1 -scores for different classes.Other metrics have lower discriminative power and are biased by unfair labellings.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2095"}, {"primary_key": "4048695", "vector": [], "sparse_vector": [], "title": "Leveraging Lexical Resources for Learning Entity Embeddings in Multi-Relational Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Doina Precup"], "summary": "Recent work in learning vector-space embeddings for multi-relational data has focused on combining relational information derived from knowledge bases with distributional information derived from large text corpora. We propose a simple approach that leverages the descriptions of entities or phrases available in lexical resources, in conjunction with distributional semantics, in order to derive a better initialization for training relational models. Applying this initialization to the TransE model results in significant new state-of-the-art performances on the WordNet dataset, decreasing the mean rank from the previous best of 212 to 51. It also results in faster convergence of the entity representations. We find that there is a trade-off between improving the mean rank and the hits@10 with this approach. This illustrates that much remains to be understood regarding performance improvements in relational models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2019"}, {"primary_key": "4048696", "vector": [], "sparse_vector": [], "title": "Simpler Context-Dependent Logical Forms via Model Projections.", "authors": ["<PERSON>", "Panupong <PERSON>", "<PERSON>"], "summary": "We consider the task of learning a context-dependent mapping from utterances to denotations. With only denotations at training time, we must search over a combinatorially large space of logical forms, which is even larger with context-dependent utterances. To cope with this challenge, we perform successive projections of the full model onto simpler models that operate over equivalence classes of logical forms. Though less expressive, we find that these simpler models are much faster and can be surprisingly effective. Moreover, they can be used to bootstrap the full model. Finally, we collected three new context-dependent semantic parsing datasets, and develop a new left-to-right parser.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1138"}, {"primary_key": "4048697", "vector": [], "sparse_vector": [], "title": "A Multi-media Approach to Cross-lingual Entity Knowledge Transfer.", "authors": ["Di <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON>"], "summary": "When a large-scale incident or disaster occurs, there is often a great demand for rapidly developing a system to extract detailed and new information from lowresource languages (LLs).We propose a novel approach to discover comparable documents in high-resource languages (HLs), and project Entity Discovery and Linking results from HLs documents back to LLs.We leverage a wide variety of language-independent forms from multiple data modalities, including image processing (image-to-image retrieval, visual similarity and face recognition) and sound matching.We also propose novel methods to learn entity priors from a large-scale HL corpus and knowledge base.Using Hausa and Chinese as the LLs and English as the HL, experiments show that our approach achieves 36.1% higher Hausa name tagging F-score over a costly supervised model, and 9.4% higher Chineseto-English Entity Linking accuracy over state-of-the-art.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1006"}, {"primary_key": "4048698", "vector": [], "sparse_vector": [], "title": "Multiplicative Representations for Unsupervised Semantic Role Induction.", "authors": ["<PERSON>", "Yangfeng Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In unsupervised semantic role labeling, identifying the role of an argument is usually informed by its dependency relation with the predicate.In this work, we propose a neural model to learn argument embeddings from the context by explicitly incorporating dependency relations as multiplicative factors, which bias argument embeddings according to their dependency roles.Our model outperforms existing state-of-the-art embeddings in unsupervised semantic role induction on the CoNLL 2008 dataset and the SimLex999 word similarity task.Qualitative results demonstrate our model can effectively bias argument embeddings based on their dependency role.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2020"}, {"primary_key": "4048699", "vector": [], "sparse_vector": [], "title": "Hawkes Processes for Continuous Time Sequence Classification: an Application to Rumour Stance Classification in Twitter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2064"}, {"primary_key": "4048700", "vector": [], "sparse_vector": [], "title": "Achieving Open Vocabulary Neural Machine Translation with Hybrid Word-Character Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nearly all previous work on neural machine translation (NMT) has used quite restricted vocabularies, perhaps with a subsequent method to patch in unknown words.This paper presents a novel wordcharacter solution to achieving open vocabulary NMT.We build hybrid systems that translate mostly at the word level and consult the character components for rare words.Our character-level recurrent neural networks compute source word representations and recover unknown target words when needed.The twofold advantage of such a hybrid approach is that it is much faster and easier to train than character-based ones; at the same time, it never produces unknown words as in the case of word-based models.On the WMT'15 English to Czech translation task, this hybrid approach offers an addition boost of ****-11.4BLEU points over models that already handle unknown words.Our best system achieves a new state-of-the-art result with 20.7 BLEU score.We demonstrate that our character models can successfully learn to not only generate well-formed words for Czech, a highly-inflected language with a very complex vocabulary, but also build correct representations for English source words.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1100"}, {"primary_key": "4048701", "vector": [], "sparse_vector": [], "title": "End-to-end Sequence Labeling via Bi-directional LSTM-CNNs-CRF.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "State-of-the-art sequence labeling systems traditionally require large amounts of taskspecific knowledge in the form of handcrafted features and data pre-processing.In this paper, we introduce a novel neutral network architecture that benefits from both word-and character-level representations automatically, by using combination of bidirectional LSTM, CNN and CRF.Our system is truly end-to-end, requiring no feature engineering or data preprocessing, thus making it applicable to a wide range of sequence labeling tasks.We evaluate our system on two data sets for two sequence labeling tasks -Penn Treebank WSJ corpus for part-of-speech (POS) tagging and CoNLL 2003 corpus for named entity recognition (NER).We obtain state-of-the-art performance on both datasets -97.55% accuracy for POS tagging and 91.21% F1 for NER.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1101"}, {"primary_key": "4048702", "vector": [], "sparse_vector": [], "title": "Language Muse: Automated Linguistic Activity Generation for English Language Learners.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current education standards in the U.S. require school students to read and understand complex texts from different subject areas (e.g., social studies). However, such texts usually contain figurative language, complex phrases and sentences, as well as unfamiliar discourse relations. This may present an obstacle to students whose native language is not English — a growing sub-population in the US. 1 One way to help such students is to create classroom activities centered around linguistic elements found in subject area texts (DelliCarpini, 2008). We present a web-based tool that uses NLP algorithms to automatically generate customizable linguistic activities that are grounded in language learning research.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4014"}, {"primary_key": "4048703", "vector": [], "sparse_vector": [], "title": "Off-topic Response Detection for Spontaneous Spoken English Assessment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic spoken language assessment systems are becoming increasingly important to meet the demand for English second language learning.This is a challenging task due to the high error rates of, even state-of-the-art, non-native speech recognition.Consequently current systems primarily assess fluency and pronunciation.However, content assessment is essential for full automation.As a first stage it is important to judge whether the speaker responds on topic to test questions designed to elicit spontaneous speech.Standard approaches to off-topic response detection assess similarity between the response and question based on bag-of-words representations.An alternative framework based on Recurrent Neural Network Language Models (RNNLM) is proposed in this paper.The RNNLM is adapted to the topic of each test question.It learns to associate example responses to questions with points in a topic space constructed using these example responses.Classification is done by ranking the topic-conditional posterior probabilities of a response.The RNNLMs associate a broad range of responses with each topic, incorporate sequence information and scale better with additional training data, unlike standard methods.On experiments conducted on data from the Business Language Testing Service (BULATS) this approach outperforms standard approaches.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1102"}, {"primary_key": "4048705", "vector": [], "sparse_vector": [], "title": "Case and Cause in Icelandic: Reconstructing Causal Networks of Cascaded Language Changes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Linguistic drift is a process that produces slow irreversible changes in the grammar and function of a language's constructions.Importantly, changes in a part of a language can have trickle down effects, triggering changes elsewhere in that language.Although such causally triggered chains of changes have long been hypothesized by historical linguists, no explicit demonstration of the actual causality has been provided.In this study, we use cooccurrence statistics and machine learning to demonstrate that the functions of morphological cases experience a slow, irreversible drift along history, even in a language as conservative as is Icelandic.Crucially, we then move on to demonstrate -using the notion of Granger-causalitythat there are explicit causal connections between the changes in the functions of the different cases, which are consistent with documented processes in the history of Icelandic.Our technique provides a means for the quantitative reconstruction of connected networks of subtle linguistic changes.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1229"}, {"primary_key": "4048706", "vector": [], "sparse_vector": [], "title": "ccg2lambda: A Compositional Semantics System.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate a simple and easy-to-use system to produce logical semantic representations of sentences.Our software operates by composing semantic formulas bottom-up given a CCG parse tree.It uses flexible semantic templates to specify semantic patterns.Templates for English and Japanese accompany our software, and they are easy to understand, use and extend to cover other linguistic phenomena or languages.We also provide scripts to use our semantic representations in a textual entailment task, and a visualization tool to display semantically augmented CCG trees in HTML.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4015"}, {"primary_key": "4048707", "vector": [], "sparse_vector": [], "title": "MeTA: A Unified Toolkit for Text Retrieval and Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "M E TA is developed to unite machine learning, information retrieval, and natu-ral language processing in one easy-to-use toolkit. Its focus on indexing allows it to perform well on large datasets, supporting online classiﬁcation and other out-of-core algorithms. M E TA’s liberal open source license encourages contributions", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4016"}, {"primary_key": "4048708", "vector": [], "sparse_vector": [], "title": "Generating Natural Language Descriptions for Semantic Representations of Human Brain Activity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantitative analysis of human brain activity based on language representations, such as the semantic categories of words, have been actively studied in the ﬁeld of brain and neuroscience. Our study aims to generate natural language descriptions for human brain activation phenomena caused by visual stimulus by employing deep learning methods, which have gained interest as an effective approach to automatically describe natural language expressions for various type of multi-modal information, such as images. We employed an image-captioning system based on a deep learning framework as the basis for our method by learning the relationship between the brain activity data and the features of an intermediate expression of the deep neural network owing to lack of training brain data. We conducted three experiments and were able to generate nat-ural language sentences which enabled us to quantitatively interpret brain activity.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3004"}, {"primary_key": "4048709", "vector": [], "sparse_vector": [], "title": "Synthesizing Compound Words for Machine Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Most machine translation systems construct translations from a closed vocabulary of target word forms, posing problems for translating into languages that have productive compounding processes.We present a simple and effective approach that deals with this problem in two phases.First, we build a classifier that identifies spans of the input text that can be translated into a single compound word in the target language.Then, for each identified span, we generate a pool of possible compounds which are added to the translation model as \"synthetic\" phrase translations.Experiments reveal that (i) we can effectively predict what spans can be compounded; (ii) our compound generation model produces good compounds; and (iii) modest improvements are possible in end-to-end English-German and English-Finnish translation tasks.We additionally introduce KomposEval, a new multi-reference dataset of English phrases and their translations into German compounds.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1103"}, {"primary_key": "4048711", "vector": [], "sparse_vector": [], "title": "From Extractive to Abstractive Summarization: A Journey.", "authors": ["<PERSON><PERSON>"], "summary": "The availability of large documentsummary corpora have opened up new possibilities for using statistical text generation techniques for abstractive summarization.Progress in Extractive text summarization has become stagnant for a while now and in this work we compare the two possible alternates to it.We present an argument in favor of abstractive summarization compared to an ensemble of extractive techniques.Further we explore the possibility of using statistical machine translation as a generative text summarization technique and present possible research questions in this direction.We also report our initial findings and future direction of research.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3015"}, {"primary_key": "4048712", "vector": [], "sparse_vector": [], "title": "MDSWriter: Annotation Tool for Creating High-Quality Multi-Document Summarization Corpora.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present MDSWriter, a novel open-source annotation tool for creating multi-document summarization corpora.A major innovation of our tool is that we divide the complex summarization task into multiple steps which enables us to efficiently guide the annotators, to store all their intermediate results, and to record user-system interaction data.This allows for evaluating the individual components of a complex summarization system and learning from the human writing process.MDSWriter is highly flexible and can be adapted to various other tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4017"}, {"primary_key": "4048713", "vector": [], "sparse_vector": [], "title": "Vocabulary Manipulation for Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In order to capture rich language phenomena, neural machine translation models have to use a large vocabulary size, which requires high computing time and large memory usage.In this paper, we alleviate this issue by introducing a sentence-level or batch-level vocabulary, which is only a very small sub-set of the full output vocabulary.For each sentence or batch, we only predict the target words in its sentencelevel or batch-level vocabulary.Thus, we reduce both the computing time and the memory usage.Our method simply takes into account the translation options of each word or phrase in the source sentence, and picks a very small target vocabulary for each sentence based on a wordto-word translation model or a bilingual phrase library learned from a traditional machine translation model.Experimental results on the large-scale English-to-French task show that our method achieves better translation performance by 1 BLEU point over the large vocabulary neural machine translation system of <PERSON> et al. (2015).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2021"}, {"primary_key": "4048714", "vector": [], "sparse_vector": [], "title": "Hunting for Troll Comments in News Community Forums.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There are different definitions of what a troll is.Certainly, a troll can be somebody who teases people to make them angry, or somebody who offends people, or somebody who wants to dominate any single discussion, or somebody who tries to manipulate people's opinion (sometimes for money), etc.The last definition is the one that dominates the public discourse in Bulgaria and Eastern Europe, and this is our focus in this paper.In our work, we examine two types of opinion manipulation trolls: paid trolls that have been revealed from leaked \"reputation management contracts\" and \"mentioned trolls\" that have been called such by several different people.We show that these definitions are sensible: we build two classifiers that can distinguish a post by such a paid troll from one by a non-troll with 81-82% accuracy; the same classifier achieves 81-82% accuracy on so called mentioned troll vs. non-troll posts.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2065"}, {"primary_key": "4048715", "vector": [], "sparse_vector": [], "title": "Robust Co-occurrence Quantification for Lexical Distributional Semantics.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Me<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous optimisations of parameters affecting the word-context association measure used in distributional vector space models have focused either on highdimensional vectors with hundreds of thousands of dimensions, or dense vectors with dimensionality of a few hundreds; but dimensionality of a few thousands is often applied in compositional tasks as it is still computationally feasible and does not require the dimensionality reduction step.We present a systematic study of the interaction of the parameters of the association measure and vector dimensionality, and derive parameter selection heuristics that achieve performance across word similarity and relevance datasets competitive with the results previously reported in the literature achieved by highly dimensional or dense models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3009"}, {"primary_key": "4048717", "vector": [], "sparse_vector": [], "title": "Harnessing Cognitive Features for Sarcasm Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seema Nagar", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a novel mechanism for enriching the feature vector, for the task of sarcasm detection, with cognitive features extracted from eye-movement patterns of human readers.Sarcasm detection has been a challenging research problem, and its importance for NLP applications such as review summarization, dialog systems and sentiment analysis is well recognized.Sarcasm can often be traced to incongruity that becomes apparent as the full sentence unfolds.This presence of incongruity-implicit or explicit-affects the way readers eyes move through the text.We observe the difference in the behaviour of the eye, while reading sarcastic and non sarcastic sentences.Motivated by this observation, we augment traditional linguistic and stylistic features for sarcasm detection with the cognitive features obtained from readers eye movement data.We perform statistical classification using the enhanced feature set so obtained.The augmented cognitive features improve sarcasm detection by 3.7% (in terms of Fscore), over the performance of the best reported system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1104"}, {"primary_key": "4048718", "vector": [], "sparse_vector": [], "title": "Learning To Use Formulas To Solve Simple Arithmetic Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>tta Baral"], "summary": "Solving simple arithmetic word problems is one of the challenges in Natural Language Understanding.This paper presents a novel method to learn to use formulas to solve simple arithmetic word problems.Our system, analyzes each of the sentences to identify the variables and their attributes; and automatically maps this information into a higher level representation.It then uses that representation to recognize the presence of a formula along with its associated variables.An equation is then generated from the formal description of the formula.In the training phase, it learns to score the pair from the systematically generated higher level representation.It is able to solve 86.07% of the problems in a corpus of standard primary school test questions and beats the state-of-the-art by a margin of 8.07%.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1202"}, {"primary_key": "4048719", "vector": [], "sparse_vector": [], "title": "End-to-End Relation Extraction using LSTMs on Sequences and Tree Structures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel end-to-end neural model to extract entities and relations between them.Our recurrent neural network based model captures both word sequence and dependency tree substructure information by stacking bidirectional treestructured LSTM-RNNs on bidirectional sequential LSTM-RNNs.This allows our model to jointly represent both entities and relations with shared parameters in a single model.We further encourage detection of entities during training and use of entity information in relation extraction via entity pretraining and scheduled sampling.Our model improves over the stateof-the-art feature-based model on end-toend relation extraction, achieving 12.1% and 5.7% relative error reductions in F1score on ACE2005 and ACE2004, respectively.We also show that our LSTM-RNN based model compares favorably to the state-of-the-art CNN based model (in F1-score) on nominal relation classification (SemEval-2010 Task 8).Finally, we present an extensive ablation analysis of several model components.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1105"}, {"primary_key": "4048720", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Image Caption Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatically generating a natural language description of an image is a fundamental problem in artificial intelligence.This task involves both computer vision and natural language processing and is called \"image caption generation.\"Research on image caption generation has typically focused on taking in an image and generating a caption in English as existing image caption corpora are mostly in English.The lack of corpora in languages other than English is an issue, especially for morphologically rich languages such as Japanese.There is thus a need for corpora sufficiently large for image captioning in other languages.We have developed a Japanese version of the MS COCO caption dataset and a generative model based on a deep recurrent architecture that takes in an image and uses this Japanese version of the dataset to generate a caption in Japanese.As the Japanese portion of the corpus is small, our model was designed to transfer the knowledge representation obtained from the English portion into the Japanese portion.Experiments showed that the resulting bilingual comparable corpus has better performance than a monolingual corpus, indicating that image understanding using a resource-rich language benefits a resource-poor language.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1168"}, {"primary_key": "4048721", "vector": [], "sparse_vector": [], "title": "Which Coreference Evaluation Metric Do You Trust? A Proposal for a Link-based Entity Aware Metric.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "you trust?A proposal for a link-based entity aware metric.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1060"}, {"primary_key": "4048722", "vector": [], "sparse_vector": [], "title": "An Investigation on The Effectiveness of Employing Topic Modeling Techniques to Provide Topic Awareness For Conversational Agents.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The idea behind this proposal is to investigate the possibility of utilizing NLP tools, statistical topic modeling techniques and freely available online resources to pro-pose a system able to provide dialogue contribution suggestions which are relevant to the context, yet out of the main activity of the dialogue (i.e. off-activity talk ). The aim is to evaluate the effects of a tool that automatically suggests off-activity talk s in form of some sentences relevant to the dialogue context. The evaluation is to be done over two test-sets of open domain and closed-domain in a conversational quiz-like setting. The outcome of this work will be a satisfactory point of entry to investigate the hypothesis that adding automatically generated off-activity talks feature to a conversational agent can lead to building up engagement of the dialogue partner(s).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3012"}, {"primary_key": "4048723", "vector": [], "sparse_vector": [], "title": "A Novel Measure for Coherence in Statistical Topic Models.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Big data presents new challenges for understanding large text corpora.Topic modeling algorithms help understand the underlying patterns, or \"topics\", in data.Researchersaut<PERSON> often read these topics in order to gain an understanding of the underlying corpus.It is important to evaluate the interpretability of these automatically generated topics.Methods have previously been designed to use crowdsourcing platforms to measure interpretability.In this paper, we demonstrate the necessity of a key concept, coherence, when assessing the topics and propose an effective method for its measurement.We show that the proposed measure of coherence captures a different aspect of the topics than existing measures.We further study the automation of these topic measures for scalability and reproducibility, showing that these measures can be automated.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2088"}, {"primary_key": "4048724", "vector": [], "sparse_vector": [], "title": "Generating Natural Questions About an Image.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1170"}, {"primary_key": "4048725", "vector": [], "sparse_vector": [], "title": "Natural Language Inference by Tree-Based Convolution and Heuristic Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ge Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose the TBCNNpair model to recognize entailment and contradiction between two sentences.In our model, a tree-based convolutional neural network (TBCNN) captures sentencelevel semantics; then heuristic matching layers like concatenation, element-wise product/difference combine the information in individual sentences.Experimental results show that our model outperforms existing sentence encoding-based approaches by a large margin.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2022"}, {"primary_key": "4048727", "vector": [], "sparse_vector": [], "title": "Graph- and surface-level sentence chunking.", "authors": ["<PERSON><PERSON>"], "summary": "The computing cost of many NLP tasks increases faster than linearly with the length of the representation of a sentence. For parsing the representation is tokens, while for operations on syntax and semantics it will be more complex. In this paper we propose a new task of sentence chunking: splitting sentence representations into coherent substructures. Its aim is to make further processing of long sentences more tractable. We investigate this idea experimentally using the Dependency Minimal Recursion Semantics (DMRS) representation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3014"}, {"primary_key": "4048728", "vector": [], "sparse_vector": [], "title": "Phrase Structure Annotation and Parsing for Learner English.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learner English often contains grammatical errors with structural characteristics such as omissions, insertions, substitutions, and word order errors. These errors are not covered by the existing context-free grammar (CFG) rules. Therefore, it is not at all straightforward how to annotate learner English with phrase structures. Because of this limitation, there has been almost no work on phrase structure annotation for learner corpora despite its importance and usefulness. To address this issue, we propose a phrase structure annotation scheme for learner English, that consists of ﬁve principles. We apply the annotation scheme to two diﬀerent learner corpora and show (i) its eﬀectiveness at consistently annotating learner English with phrase structure (i.e., high inter-annotator agreement); (ii) the structural characteristics (CFG rules) of learner English obtained from the annotated corpora; and (iii) phrase structure parsing performance on learner English for the ﬁrst time. We also release the annotation guidelines, the annotated data, and the parser model to the public.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1173"}, {"primary_key": "4048729", "vector": [], "sparse_vector": [], "title": "Optimizing Spectral Learning for Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a search algorithm for optimizing the number of latent states when estimating latent-variable PCFGs with spectral methods.Our results show that contrary to the common belief that the number of latent states for each nonterminal in an L-PCFG can be decided in isolation with spectral methods, parsing results significantly improve if the number of latent states for each nonterminal is globally optimized, while taking into account interactions between the different nonterminals.In addition, we contribute an empirical analysis of spectral algorithms on eight morphologically rich languages: Basque, French, German, Hebrew, Hungarian, Korean, Polish and Swedish.Our results show that our estimation consistently performs better or close to coarse-to-fine expectation-maximization techniques for these languages.• We describe a search algorithm for optimiz-", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1146"}, {"primary_key": "4048730", "vector": [], "sparse_vector": [], "title": "A short proof that O_2 is an MCFL.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a new proof that $O_2$ is a multiple context-free language. It contrasts with a recent proof by <PERSON><PERSON><PERSON> (2015) in its avoidance of concepts that seem specific to two-dimensional geometry, such as the complex exponential function. Our simple proof creates realistic prospects of widening the results to higher dimensions. This finding is of central importance to the relation between extreme free word order and classes of grammars used to describe the syntax of natural language.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1106"}, {"primary_key": "4048731", "vector": [], "sparse_vector": [], "title": "Suggestion Mining from Opinionated Text.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In addition to the positive and negative sentiments expressed by speakers, opinions on the web also convey suggestions.Such text comprise of advice, recommendations and tips on a variety of points of interest.We propose that suggestions can be extracted from the available opinionated text and put to several use cases.The problem has been identified only recently as a viable task, and there is a lot of scope for research in the direction of problem definition, datasets, and methods.From an abstract view, standard algorithms for tasks like sentence classification and keyphrase extraction appear to be usable for suggestion mining.However, initial experiments reveal that there is a need for new methods, or variations in the existing ones for addressing the problem specific challenges.We present a research proposal which divides the problem into three main research questions; we walk through them, presenting our analysis, results, and future directions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3018"}, {"primary_key": "4048732", "vector": [], "sparse_vector": [], "title": "Context-aware Argumentative Relation Mining.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Context is crucial for identifying argumentative relations in text, but many argument mining methods make little use of contextual features.This paper presents contextaware argumentative relation mining that uses features extracted from writing topics as well as from windows of context sentences.Experiments on student essays demonstrate that the proposed features improve predictive performance in two argumentative relation classification tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1107"}, {"primary_key": "4048733", "vector": [], "sparse_vector": [], "title": "Integrating Distributional Lexical Contrast into Word Embeddings for Antonym-Synonym Distinction.", "authors": ["<PERSON>", "<PERSON> im Walde", "<PERSON><PERSON>"], "summary": "We propose a novel vector representation that integrates lexical contrast into distributional vectors and strengthens the most salient features for determining degrees of word similarity.The improved vectors significantly outperform standard models and distinguish antonyms from synonyms with an average precision of 0.66-0.76across word classes (adjectives, nouns, verbs).Moreover, we integrate the lexical contrast vectors into the objective function of a skip-gram model.The novel embedding outperforms state-of-the-art models on predicting word similarities in SimLex-999, and on distinguishing antonyms from synonyms.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2074"}, {"primary_key": "4048734", "vector": [], "sparse_vector": [], "title": "Leveraging Inflection Tables for Stemming and Lemmatization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present several methods for stemming and lemmatization based on discriminative string transduction. We exploit the paradigmatic regularity of semi-structured inflection tables to identify stems in an unsupervised manner with over 85% accuracy. Experiments on English, Dutch and German show that our stemmers substantially outperform <PERSON><PERSON> and Morfessor, and approach the accuracy of a supervised model. Furthermore, the generated stems are more consistent than those annotated by experts. Our direct lemmatization model is more accurate than <PERSON><PERSON><PERSON> and Le<PERSON> on most datasets. Finally, we test our methods on the data from the shared task on morphological reinflection.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1108"}, {"primary_key": "4048735", "vector": [], "sparse_vector": [], "title": "Phrase Table Pruning via Submodular Function Maximization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Phrase table pruning is the act of removing phrase pairs from a phrase table to make it smaller, ideally removing the least useful phrases first.We propose a phrase table pruning method that formulates the task as a submodular function maximization problem, and solves it by using a greedy heuristic algorithm.The proposed method can scale with input size and long phrases, and experiments show that it achieves higher BLEU scores than state-of-the-art pruning methods.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2066"}, {"primary_key": "4048736", "vector": [], "sparse_vector": [], "title": "Jigg: A Framework for an Easy Natural Language Processing Pipeline.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Jigg, a Scala (or JVMbased) NLP annotation pipeline framework, which is easy to use and is extensible.Jigg supports a very simple interface similar to Stanford CoreNLP, the most successful NLP pipeline toolkit, but has more flexibility to adapt to new types of annotation.On this framework, system developers can easily integrate their downstream system into a NLP pipeline from a raw text by just preparing a wrapper of it.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4018"}, {"primary_key": "4048737", "vector": [], "sparse_vector": [], "title": "Improving cross-domain n-gram language modelling with skipgrams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we improve over the hierarchical Pitman-Yor processes language model in a cross-domain setting by adding skipgrams as features. We find that adding skipgram features reduces the perplexity. This reduction is substantial when models are trained on a generic corpus and tested on domain-specific corpora. We also find that within-domain testing and cross-domain testing require different backoff strategies. We observe a 30-40% reduction in perplexity in a cross-domain language modelling task, and up to 6% reduction in a within-domain experiment, for both English and Flemish-Dutch.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2023"}, {"primary_key": "4048738", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Word Representations via Spectral Graph Embeddings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-lingual word embeddings are used for cross-lingual information retrieval or domain adaptations. In this paper, we extend Eigenwords, spectral monolingual word embeddings based on canonical correlation analysis (CCA), to crosslingual settings with sentence-alignment. For incorporating cross-lingual information, CCA is replaced with its generalization based on the spectral graph embeddings. The proposed method, which we refer to as Cross-Lingual Eigenwords (CL-Eigenwords), is fast and scalable for computing distributed representations of words via eigenvalue decomposition. Numerical experiments of English-Spanish word translation tasks show that CLEigenwords is competitive with stateof-the-art cross-lingual word embedding methods.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2080"}, {"primary_key": "4048739", "vector": [], "sparse_vector": [], "title": "A Neural Network based Approach to Automatic Post-Editing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a neural network based automatic post-editing (APE) system to improve raw machine translation (MT) output.Our neural model of APE (NNAPE) is based on a bidirectional recurrent neural network (RNN) model and consists of an encoder that encodes an MT output into a fixed-length vector from which a decoder provides a post-edited (PE) translation.APE translations produced by NNAPE show statistically significant improvements of 3.96, 2.68 and 1.35 BLEU points absolute over the original MT, phrase-based APE and hierarchical APE outputs, respectively.Furthermore, human evaluation shows that the NNAPE generated PE translations are much better than the original MT output.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2046"}, {"primary_key": "4048740", "vector": [], "sparse_vector": [], "title": "Unravelling Names of Fictional Characters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we explore the correlation between the sound of words and their meaning, by testing if the polarity ('good guy' or 'bad guy') of a character's role in a work of fiction can be predicted by the name of the character in the absence of any other context.Our approach is based on phonological and other features proposed in prior theoretical studies of fictional names.These features are used to construct a predictive model over a manually annotated corpus of characters from motion pictures.By experimenting with different mixtures of features, we identify phonological features as being the most discriminative by comparison to social and other types of features, and we delve into a discussion of specific phonological and phonotactic indicators of a character's role's polarity.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1203"}, {"primary_key": "4048741", "vector": [], "sparse_vector": [], "title": "The LAMBADA dataset: Word prediction requiring a broad discourse context.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1144"}, {"primary_key": "4048742", "vector": [], "sparse_vector": [], "title": "Inferring Logical Forms From Denotations.", "authors": ["Panupong <PERSON>", "<PERSON>"], "summary": "A core problem in learning semantic parsers from denotations is picking out consistent logical forms-those that yield the correct denotation-from a combinatorially large space.To control the search space, previous work relied on restricted set of rules, which limits expressivity.In this paper, we consider a much more expressive class of logical forms, and show how to use dynamic programming to efficiently represent the complete set of consistent logical forms.Expressivity also introduces many more spurious logical forms which are consistent with the correct denotation but do not represent the meaning of the utterance.To address this, we generate fictitious worlds and use crowdsourced denotations on these worlds to filter out spurious logical forms.On the WIKITABLEQUESTIONS dataset, we increase the coverage of answerable questions from 53.5% to 76%, and the additional crowdsourced supervision lets us rule out 92.1% of spurious logical forms.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1003"}, {"primary_key": "4048743", "vector": [], "sparse_vector": [], "title": "Most &quot;babies&quot; are &quot;little&quot; and most &quot;problems&quot; are &quot;huge&quot;: Compositional Entailment in Adjective-Nouns.", "authors": ["<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "We examine adjective-noun (AN) composition in the task of recognizing textual entailment (RTE).We analyze behavior of ANs in large corpora and show that, despite conventional wisdom, adjectives do not always restrict the denotation of the nouns they modify.We use natural logic to characterize the variety of entailment relations that can result from AN composition.Predicting these relations depends on context and on commonsense knowledge, making AN composition especially challenging for current RTE systems.We demonstrate the inability of current stateof-the-art systems to handle AN composition in a simplified RTE task which involves the insertion of only a single word.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1204"}, {"primary_key": "4048744", "vector": [], "sparse_vector": [], "title": "Simple PPDB: A Paraphrase Database for Simplification.", "authors": ["<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "We release the Simple Paraphrase Database, a subset of of the Paraphrase Database (PPDB) adapted for the task of text simplification.We train a supervised model to associate simplification scores with each phrase pair, producing rankings competitive with state-of-theart lexical simplification models.Our new simplification database contains 4.5 million paraphrase rules, making it the largest available resource for lexical simplification.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2024"}, {"primary_key": "4048746", "vector": [], "sparse_vector": [], "title": "Improving Named Entity Recognition for Chinese Social Media with Word Segmentation Representation Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Named entity recognition, and other information extraction tasks, frequently use linguistic features such as part of speech tags or chunkings.For languages where word boundaries are not readily identified in text, word segmentation is a key first step to generating features for an NER system.While using word boundary tags as features are helpful, the signals that aid in identifying these boundaries may provide richer information for an NER system.New state-of-the-art word segmentation systems use neural models to learn representations for predicting word boundaries.We show that these same representations, jointly trained with an NER system, yield significant improvements in NER for Chinese social media.In our experiments, jointly training NER and word segmentation with an LSTM-CRF model yields nearly 5% absolute improvement over previously published results.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2025"}, {"primary_key": "4048747", "vector": [], "sparse_vector": [], "title": "News Citation Recommendation with Implicit and Explicit Semantics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we focus on the problem of news citation recommendation. The task aims to recommend news citations for both authors and readers to create and search news references. Due to the sparsity issue of news citations and the engineering difficulty in obtaining information on authors, we focus on content similarity-based methods instead of collaborative filtering-based approaches. In this paper, we explore word embedding (i.e., implicit semantics) and grounded entities (i.e., explicit semantics) to address the variety and ambiguity issues of language. We formulate the problem as a reranking task and integrate different similarity measures under the learning to rank framework. We evaluate our approach on a real-world dataset. The experimental results show the efficacy of our method.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1037"}, {"primary_key": "4048748", "vector": [], "sparse_vector": [], "title": "Two Discourse Driven Language Models for Semantics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Natural language understanding often requires deep semantic knowledge.Expanding on previous proposals, we suggest that some important aspects of semantic knowledge can be modeled as a language model if done at an appropriate level of abstraction.We develop two distinct models that capture semantic frame chains and discourse information while abstracting over the specific mentions of predicates and entities.For each model, we investigate four implementations: a \"standard\" N-gram language model and three discriminatively trained \"neural\" language models that generate embeddings for semantic frames.The quality of the semantic language models (SemLM) is evaluated both intrinsically, using perplexity and a narrative cloze test and extrinsically -we show that our SemLM helps improve performance on semantic natural language processing tasks such as co-reference resolution and discourse parsing.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1028"}, {"primary_key": "4048749", "vector": [], "sparse_vector": [], "title": "Modeling <PERSON><PERSON> in Student Essays.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Essay stance classification, the task of determining how much an essay's author agrees with a given proposition, is an important yet under-investigated subtask in understanding an argumentative essay's overall content.We introduce a new corpus of argumentative student essays annotated with stance information and propose a computational model for automatically predicting essay stance.In an evaluation on 826 essays, our approach significantly outperforms four baselines, one of which relies on features previously developed specifically for stance classification in student essays, yielding relative error reductions of at least 11.3% and 5.3%, in micro and macro F-score, respectively.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1205"}, {"primary_key": "4048751", "vector": [], "sparse_vector": [], "title": "Exponentially Decaying Bag-of-Words Input Features for Feed-Forward Neural Network in Statistical Machine Translation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, neural network models have achieved consistent improvements in statistical machine translation.However, most networks only use one-hot encoded input vectors of words as their input.In this work, we investigated the exponentially decaying bag-of-words input features for feed-forward neural network translation models and proposed to train the decay rates along with other weight parameters.This novel bag-of-words model improved our phrase-based state-of-the-art system, which already includes a neural network translation model, by up to 0.5% BLEU and 0.6% TER on three different translation tasks and even achieved a similar performance to the bidirectional LSTM translation model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2048"}, {"primary_key": "4048752", "vector": [], "sparse_vector": [], "title": "Optimizing an Approximation of ROUGE - a Problem-Reduction Approach to Extractive Multi-Document Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a problem-reduction approach to extractive multi-document summarization: we propose a reduction to the problem of scoring individual sentences with their ROUGE scores based on supervised learning.For the summarization, we solve an optimization problem where the ROUGE score of the selected summary sentences is maximized.To this end, we derive an approximation of the ROUGE-N score of a set of sentences, and define a principled discrete optimization problem for sentence selection.Mathematical and empirical evidence suggests that the sentence selection step is solved almost exactly, thus reducing the problem to the sentence scoring task.We perform a detailed experimental evaluation on two DUC datasets to demonstrate the validity of our approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1172"}, {"primary_key": "4048754", "vector": [], "sparse_vector": [], "title": "Scaling a Natural Language Generation System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A key goal in natural language generation (NLG) is to enable fast generation even with large vocabularies, grammars and worlds.In this work, we build upon a recently proposed NLG system, Sentence Tree Realization with UCT (STRUCT).We describe four enhancements to this system: (i) pruning the grammar based on the world and the communicative goal, (ii) intelligently caching and pruning the combinatorial space of semantic bindings, (iii) reusing the lookahead search tree at different search depths, and (iv) learning and using a search control heuristic.We evaluate the resulting system on three datasets of increasing size and complexity, the largest of which has a vocabulary of about 10K words, a grammar of about 32K lexicalized trees and a world with about 11K entities and 23K relations between them.Our results show that the system has a median generation time of 8.5s and finds the best sentence on average within 25s.These results are based on a sequential, interpreted implementation and are significantly better than the state of the art for planningbased NLG systems.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1109"}, {"primary_key": "4048755", "vector": [], "sparse_vector": [], "title": "Using Sentence-Level LSTM Language Models for Script Inference.", "authors": ["<PERSON>", "<PERSON>"], "summary": "There is a small but growing body of research on statistical scripts, models of event sequences that allow probabilistic inference of implicit events from documents.These systems operate on structured verb-argument events produced by an NLP pipeline.We compare these systems with recent Recurrent Neural Net models that directly operate on raw tokens to predict sentences, finding the latter to be roughly comparable to the former in terms of predicting missing events in documents.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1027"}, {"primary_key": "4048756", "vector": [], "sparse_vector": [], "title": "Multilingual Part-of-Speech Tagging with Bidirectional Long Short-Term Memory Models and Auxiliary Loss.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Bidirectional long short-term memory (bi-LSTM) networks have recently proven successful for various NLP sequence modeling tasks, but little is known about their reliance to input representations, target languages, data set size, and label noise.We address these issues and evaluate bi-LSTMs with word, character, and unicode byte embeddings for POS tagging.We compare bi-LSTMs to traditional POS taggers across languages and data sizes.We also present a novel bi-LSTM model, which combines the POS tagging loss function with an auxiliary loss function that accounts for rare words.The model obtains state-of-the-art performance across 22 languages, and works especially well for morphologically complex languages.Our analysis suggests that bi-LSTMs are less sensitive to training data size and label corruptions (at small noise levels) than previously assumed.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2067"}, {"primary_key": "4048757", "vector": [], "sparse_vector": [], "title": "An Advanced Press Review System Combining Deep News Analysis and Machine Learning Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In our media-driven world the perception of companies and institutions in the media is of major importance.The creation of press reviews analyzing the media response to company-related events is a complex and time-consuming task.In this demo we present a system that combines advanced text mining and machine learning approaches in an extensible press review system.The system collects documents from heterogeneous sources and enriches the documents applying different mining, filtering, classification, and aggregation algorithms.We present a system tailored to the needs of the press department of a major German University.We explain how the different components have been trained and evaluated.The system enables us demonstrating the live analyzes of news and social media streams as well as the strengths of advanced text mining algorithms for creating a comprehensive media analysis.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4019"}, {"primary_key": "4048758", "vector": [], "sparse_vector": [], "title": "ALTO: Active Learning with Topic Overviews for Speeding Label Induction and Document Labeling.", "authors": ["Forough Poursabzi-Sang<PERSON>h", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Effective text classification requires experts to annotate data with labels; these training data are time-consuming and expensive to obtain.If you know what labels you want, active learning can reduce the number of labeled documents needed.However, establishing the label set remains difficult.Annotators often lack the global knowledge needed to induce a label set.We introduce ALTO: Active Learning with Topic Overviews, an interactive system to help humans annotate documents: topic models provide a global overview of what labels to create and active learning directs them to the right documents to label.Our forty-annotator user study shows that while active learning alone is best in extremely resource limited conditions, topic models (even by themselves) lead to better label sets, and ALTO's combination is best overall.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1110"}, {"primary_key": "4048759", "vector": [], "sparse_vector": [], "title": "Predicting the Rise and Fall of Scientific Topics from Trends in their Rhetorical Framing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Computationally modeling the evolution of science by tracking how scientific topics rise and fall over time has important implications for research funding and public policy.However, little is known about the mechanisms underlying topic growth and decline.We investigate the role of rhetorical framing: whether the rhetorical role or function that authors ascribe to topics (as methods, as goals, as results, etc.) relates to the historical trajectory of the topics.We train topic models and a rhetorical function classifier to map topic models onto their rhetorical roles in 2.4 million abstracts from the Web of Science from 1991-2010.We find that a topic's rhetorical function is highly predictive of its eventual growth or decline.For example, topics that are rhetorically described as results tend to be in decline, while topics that function as methods tend to be in early phases of growth.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1111"}, {"primary_key": "4048760", "vector": [], "sparse_vector": [], "title": "My Science Tutor - Learning Science with a Conversational Virtual Tutor.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a conversational, multimedia, virtual science tutor for elementary school students. It is built using state of the art speech recognition and spoken language understanding technology. This virtual science tutor is unique in that it elicits self-explanations from students for various science phenomena by engaging them in spoken dialogs and guided by illustrations, animations and interactive simulations. There is a lot of evidence that self-explanation works well as a tutorial paradigm, Summative evaluations indicate that students are highly engaged in the tutoring sessions, and achieve learning outcomes equivalent to expert human tutors. Tutorials are developed through a process of recording and annotating data from sessions with students, and then updating tutor models. It enthusiastically supported by students and teachers. Teachers report that it is feasible to integrate into their curriculum.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4021"}, {"primary_key": "4048761", "vector": [], "sparse_vector": [], "title": "Investigating Language Universal and Specific Properties in Word Embeddings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, many NLP tasks have benefited from distributed word representation. However, it remains unknown whether embedding models are really immune to the typological diversity of languages, despite the language-independent architecture. Here we investigate three representative models on a large set of language samples by mapping dense embedding to sparse linguistic property space. Experiment results reveal the language universal and specific properties encoded in various word representation. Additionally, strong evidence supports the utility of word form, especially for inflectional languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1140"}, {"primary_key": "4048762", "vector": [], "sparse_vector": [], "title": "A New Psychometric-inspired Evaluation Metric for Chinese Word Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Word segmentation is a fundamental task for Chinese language processing.However, with the successive improvements, the standard metric is becoming hard to distinguish state-of-the-art word segmentation systems.In this paper, we propose a new psychometric-inspired evaluation metric for Chinese word segmentation, which addresses to balance the very skewed word distribution at different levels of difficulty 1 .The performance on a real evaluation shows that the proposed metric gives more reasonable and distinguishable scores and correlates well with human judgement.In addition, the proposed metric can be easily extended to evaluate other sequence labelling based NLP tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1206"}, {"primary_key": "4048763", "vector": [], "sparse_vector": [], "title": "On the Similarities Between Native, Non-native and Translated Texts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a computational analysis of three language varieties: native, advanced non-native, and translation. Our goal is to investigate the similarities and differences between non-native language productions and translations, contrasting both with native language. Using a collection of computational methods we establish three main results: (1) the three types of texts are easily distinguishable; (2) nonnative language and translations are closer to each other than each of them is to native language; and (3) some of these characteristics depend on the source or native language, while others do not, reflecting, perhaps, unified principles that similarly affect translations and non-native language.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1176"}, {"primary_key": "4048764", "vector": [], "sparse_vector": [], "title": "pigeo: A Python Geotagging Tool.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present pigeo, a Python geolocation prediction tool that predicts a location for a given text input or Twitter user.We discuss the design, implementation and application of pigeo, and empirically evaluate it.pigeo is able to geolocate informal text and is a very useful tool for users who require a free and easy-to-use, yet accurate geolocation service based on pre-trained models.Additionally, users can train their own models easily using pigeo's API.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4022"}, {"primary_key": "4048766", "vector": [], "sparse_vector": [], "title": "How Naked is the Naked Truth? A Multilingual Lexicon of Nominal Compound Compositionality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2026"}, {"primary_key": "4048767", "vector": [], "sparse_vector": [], "title": "Connotation Frames: A Data-Driven Investigation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Through a particular choice of a predicate (e.g., \"x violated y\"), a writer can subtly connote a range of implied sentiment and presupposed facts about the entities x and y: (1) writer's perspective: projecting x as an \"antagonist\" and y as a \"victim\", (2) entities' perspective: y probably dislikes x, (3) effect: something bad happened to y, (4) value: y is something valuable, and (5) mental state: y is distressed by the event.We introduce connotation frames as a representation formalism to organize these rich dimensions of connotation using typed relations.First, we investigate the feasibility of obtaining connotative labels through crowdsourcing experiments.We then present models for predicting the connotation frames of verb predicates based on their distributional word representations and the interplay between different types of connotative relations.Empirical results confirm that connotation frames can be induced from various data sources that reflect how language is used in context.We conclude with analytical results that show the potential use of connotation frames for analyzing subtle biases in online news media.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1030"}, {"primary_key": "4048768", "vector": [], "sparse_vector": [], "title": "An Open Web Platform for Rule-Based Speech-to-Sign Translation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2027"}, {"primary_key": "4048769", "vector": [], "sparse_vector": [], "title": "Compositional Sequence Labeling Models for Error Detection in Learner Writing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present the first experiments using neural network models for the task of error detection in learner writing.We perform a systematic comparison of alternative compositional architectures and propose a framework for error detection based on bidirectional LSTMs.Experiments on the CoNLL-14 shared task dataset show the model is able to outperform other participants on detecting errors in learner writing.Finally, the model is integrated with a publicly deployed self-assessment system, leading to performance comparable to human annotators.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1112"}, {"primary_key": "4048770", "vector": [], "sparse_vector": [], "title": "Temporal Anchoring of Events for the TimeBank Corpus.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today's extraction of temporal information for events heavily depends on annotated temporal links.These so called TLINKs capture the relation between pairs of event mentions and time expressions.One problem is that the number of possible TLINKs grows quadratic with the number of event mentions, therefore most annotation studies concentrate on links for mentions in the same or in adjacent sentences.However, as our annotation study shows, this restriction results for 58% of the event mentions in a less precise information when the event took place.This paper proposes a new annotation scheme to anchor events in time.Not only is the annotation effort much lower as it scales linear with the number of events, it also gives a more precise anchoring when the events have happened as the complete document can be taken into account.Using this scheme, we annotated a subset of the TimeBank Corpus and compare our results to other annotation schemes.Additionally, we present some baseline experiments to automatically anchor events in time.Our annotation scheme, the automated system and the annotated corpus are publicly available.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1207"}, {"primary_key": "4048771", "vector": [], "sparse_vector": [], "title": "User Modeling in Language Learning with Macaronic Texts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Foreign language learners can acquire new vocabulary by using cognate and context clues when reading.To measure such incidental comprehension, we devise an experimental framework that involves reading mixed-language \"macaronic\" sentences.Using data collected via Amazon Mechanical Turk, we train a graphical model to simulate a human subject's comprehension of foreign words, based on cognate clues (edit distance to an English word), context clues (pointwise mutual information), and prior exposure.Our model does a reasonable job at predicting which words a user will be able to understand, which should facilitate the automatic construction of comprehensible text for personalized foreign language education.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1175"}, {"primary_key": "4048772", "vector": [], "sparse_vector": [], "title": "Creating Interactive Macaronic Interfaces for Language Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a prototype of a novel technology for second language instruction.Our learn-by-reading approach lets a human learner acquire new words and constructions by encountering them in context.To facilitate reading comprehension, our technology presents mixed native language (L1) and second language (L2) sentences to a learner and allows them to interact with the sentences to make the sentences easier (more L1-like) or harder (more L2-like) to read.Eventually, our system should continuously track a learner's knowledge and learning style by modeling their interactions, including performance on a pop quiz feature.This will allow our system to generate personalized mixed-language texts for learners.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4023"}, {"primary_key": "4048773", "vector": [], "sparse_vector": [], "title": "Semantics-Driven Recognition of Collocations Using Word Embeddings.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "L2 learners often produce “ungrammatical” word combinations such as, e.g., *give a suggestion or *make a walk. This is because of the “collocationality” of one of their items (the base) that limits the acceptance of collocates to express a specific meaning (‘perform’ above). We propose an algorithm that delivers, for a given base and the intended meaning of a collocate, the actual collocate lexeme(s) (make / take above). The algorithm exploits the linear mapping between bases and collocates from examples and generates a collocation transformation matrix which is then applied to novel unseen cases. The evaluation shows a promising line of research in collocation discovery.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2081"}, {"primary_key": "4048774", "vector": [], "sparse_vector": [], "title": "Neural Semantic Role Labeling with Dependency Path Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a novel model for semantic role labeling that makes use of neural sequence modeling techniques.Our approach is motivated by the observation that complex syntactic structures and related phenomena, such as nested subordinations and nominal predicates, are not handled well by existing models.Our model treats such instances as subsequences of lexicalized dependency paths and learns suitable embedding representations.We experimentally demonstrate that such embeddings can improve results over previous state-of-the-art semantic role labelers, and showcase qualitative improvements obtained by our method.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1113"}, {"primary_key": "4048775", "vector": [], "sparse_vector": [], "title": "Word Embedding Calculus in Meaningful Ultradense Subspaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We decompose a standard embedding space into interpretable orthogonal subspaces and a \"remainder\" subspace.We consider four interpretable subspaces in this paper: polarity, concreteness, frequency and part-of-speech (POS) subspaces.We introduce a new calculus for subspaces that supports operations like \"-1 × hate = love\" and \"give me a neutral word for greasy\" (i.e., oleaginous).This calculus extends analogy computations like \"king-man+woman = queen\".For the tasks of Antonym Classification and POS Tagging our method outperforms the state of the art.We create test sets for Morphological Analogies and for the new task of Polarity Spectrum Creation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2083"}, {"primary_key": "4048776", "vector": [], "sparse_vector": [], "title": "Grammatical Error Correction: Machine Translation and Classifiers.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We focus on two leading state-of-the-art approaches to grammatical error correction -machine learning classification and machine translation.Based on the comparative study of the two learning frameworks and through error analysis of the output of the state-of-the-art systems, we identify key strengths and weaknesses of each of these approaches and demonstrate their complementarity.In particular, the machine translation method learns from parallel data without requiring further linguistic input and is better at correcting complex mistakes.The classification approach possesses other desirable characteristics, such as the ability to easily generalize beyond what was seen in training, the ability to train without human-annotated data, and the flexibility to adjust knowledge sources for individual error types.Based on this analysis, we develop an algorithmic approach that combines the strengths of both methods.We present several systems based on resources used in previous work with a relative improvement of over 20% (and 7.4 F score points) over the previous state-of-the-art. SystemMethod Performance P R F0.5", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1208"}, {"primary_key": "4048777", "vector": [], "sparse_vector": [], "title": "Finding Optimists and Pessimists on Twitter.", "authors": ["Xianzhi Ruan", "<PERSON>", "<PERSON><PERSON>"], "summary": "Optimism is linked to various personality factors as well as both psychological and physical health, but how does it relate to the way a person tweets?We analyze the online activity of a set of Twitter users in order to determine how well machine learning algorithms can detect a person's outlook on life by reading their tweets.A sample of tweets from each user is manually annotated in order to establish ground truth labels, and classifiers are trained to distinguish between optimistic and pessimistic users.Our results suggest that the words in people's tweets provide ample evidence to identify them as optimists, pessimists, or somewhere in between.Additionally, several applications of these trained models are explored.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2052"}, {"primary_key": "4048778", "vector": [], "sparse_vector": [], "title": "An Unsupervised Method for Automatic Translation Memory Cleaning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We address the problem of automatically cleaning a large-scale Translation Memory (TM) in a fully unsupervised fashion, i.e. without human-labelled data. We approach the task by: i) designing a set of features that capture the similarity between two text segments in different languages, ii) use them to induce reliable training labels for a subset of the translation units (TUs) contained in the TM, and iii) use the automatically labelled data to train an ensemble of binary classifiers. We apply our method to clean a test set composed of 1,000 TUs randomly extracted from the English-Italian version of MyMemory, the world’s largest public TM. Our results show competitive performance not only against a strong baseline that exploits machine translation, but also against a state-of-the-art method that relies on human-labelled data.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2047"}, {"primary_key": "4048779", "vector": [], "sparse_vector": [], "title": "TMop: a Tool for Unsupervised Translation Memory Cleaning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present TMop, the first open-source tool for automatic Translation Memory (TM) cleaning. The tool implements a fully unsupervised approach to the task, which allows spotting unreliable translation units (sentence pairs in different languages, which are supposed to be translations of each other) without requiring labeled training data. TMop includes a highly configurable and extensible set of filters capturing different aspects of translation quality. It has been evaluated on a test set composed of 1,000 translation units (TUs) randomly extracted from the English-Italian version of MyMemory, a large-scale public TM. Results indicate its effectiveness in automatic removing “bad” TUs, with comparable performance to a state-of-the-art supervised method (76.3 vs. 77.7 balanced accuracy).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4009"}, {"primary_key": "4048780", "vector": [], "sparse_vector": [], "title": "Science Question Answering using Instructional Materials.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide a solution for elementary science tests using instructional materials.We posit that there is a hidden structure that explains the correctness of an answer given the question and instructional materials and present a unified max-margin framework that learns to find these hidden structures (given a corpus of questionanswer pairs and instructional materials), and uses what it learns to answer novel elementary science questions.Our evaluation shows that our framework outperforms several strong baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2076"}, {"primary_key": "4048781", "vector": [], "sparse_vector": [], "title": "Easy Questions First? A Case Study on Curriculum Learning for Question Answering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cognitive science researchers have emphasized the importance of ordering a complex task into a sequence of easy to hard problems.Such an ordering provides an easier path to learning and increases the speed of acquisition of the task compared to conventional learning.Recent works in machine learning have explored a curriculum learning approach called selfpaced learning which orders data samples on the easiness scale so that easy samples can be introduced to the learning algorithm first and harder samples can be introduced successively.We introduce a number of heuristics that improve upon selfpaced learning.Then, we argue that incorporating easy, yet, a diverse set of samples can further improve learning.We compare these curriculum learning proposals in the context of four non-convex models for QA and show that they lead to real improvements in each of them.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1043"}, {"primary_key": "4048782", "vector": [], "sparse_vector": [], "title": "Machine Comprehension using Rich Semantic Representations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Machine comprehension tests the system's ability to understand a piece of text through a reading comprehension task.For this task, we propose an approach using the Abstract Meaning Representation (AMR) formalism.We construct meaning representation graphs for the given text and for each question-answer pair by merging the AMRs of comprising sentences using cross-sentential phenomena such as coreference and rhetorical structures.Then, we reduce machine comprehension to a graph containment problem.We posit that there is a latent mapping of the question-answer meaning representation graph onto the text meaning representation graph that explains the answer.We present a unified max-margin framework that learns to find this mapping (given a corpus of texts and question-answer pairs), and uses what it learns to answer questions on novel texts.We show that this approach leads to state of the art results on the task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2079"}, {"primary_key": "4048783", "vector": [], "sparse_vector": [], "title": "Recurrent neural network models for disease name recognition using domain invariant features.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hand-crafted features based on linguistic and domain-knowledge play crucial role in determining the performance of disease name recognition systems.Such methods are further limited by the scope of these features or in other words, their ability to cover the contexts or word dependencies within a sentence.In this work, we focus on reducing such dependencies and propose a domain-invariant framework for the disease name recognition task.In particular, we propose various end-to-end recurrent neural network (RNN) models for the tasks of disease name recognition and their classification into four pre-defined categories.We also utilize convolution neural network (CNN) in cascade of RNN to get character-based embedded features and employ it with word-embedded features in our model.We compare our models with the state-of-the-art results for the two tasks on NCBI disease dataset.Our results for the disease mention recognition task indicate that state-of-the-art performance can be obtained without relying on feature engineering.Further the proposed models obtained improved performance on the classification task of disease names.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1209"}, {"primary_key": "4048784", "vector": [], "sparse_vector": [], "title": "An Entity-Focused Approach to Generating Company Descriptions.", "authors": ["<PERSON>", "Or Biran", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Finding quality descriptions on the web, such as those found in Wikipedia articles, of newer companies can be difficult: search engines show many pages with varying relevance, while multi-document summarization algorithms find it difficult to distinguish between core facts and other information such as news stories.In this paper, we propose an entity-focused, hybrid generation approach to automatically produce descriptions of previously unseen companies, and show that it outperforms a strong summarization baseline.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2040"}, {"primary_key": "4048785", "vector": [], "sparse_vector": [], "title": "Matrix Factorization using Window Sampling and Negative Sampling for Improved Word Representations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose LexVec, a new method for generating distributed word representations that uses low-rank, weighted factorization of the Positive Point-wise Mutual Information matrix via stochastic gradient descent, employing a weighting scheme that assigns heavier penalties for errors on frequent cooccurrences while still accounting for negative co-occurrence. Evaluation on word similarity and analogy tasks shows that LexVec matches and often outperforms state-of-the-art methods on many of these tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2068"}, {"primary_key": "4048786", "vector": [], "sparse_vector": [], "title": "Idiom Token Classification using Sentential Distributed Semantics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Idiom token classification is the task of deciding for a set of potentially idiomatic phrases whether each occurrence of a phrase is a literal or idiomatic usage of the phrase.In this work we explore the use of Skip-Thought Vectors to create distributed representations that encode features that are predictive with respect to idiom token classification.We show that classifiers using these representations have competitive performance compared with the state of the art in idiom token classification.Importantly, however, our models use only the sentence containing the target phrase as input and are thus less dependent on a potentially inaccurate or incomplete model of discourse context.We further demonstrate the feasibility of using these representations to train a competitive general idiom token classifier.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1019"}, {"primary_key": "4048787", "vector": [], "sparse_vector": [], "title": "Prediction of Prospective User Engagement with Intelligent Assistants.", "authors": ["Shumpei Sano", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intelligent assistants on mobile devices, such as <PERSON><PERSON>, have recently gained considerable attention as novel applications of dialogue technologies.A tremendous amount of real users of intelligent assistants provide us with an opportunity to explore a novel task of predicting whether users will continually use their intelligent assistants in the future.We developed prediction models of prospective user engagement by using large-scale user logs obtained from a commercial intelligent assistant.Experiments demonstrated that our models can predict prospective user engagement reasonably well, and outperforms a strong baseline that makes prediction based past utterance frequency.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1114"}, {"primary_key": "4048788", "vector": [], "sparse_vector": [], "title": "Domain Adaptation for Authorship Attribution: Improved Structural Correspondence Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "We present the ﬁrst domain adaptation model for authorship attribution to leverage unlabeled data. The model includes extensions to structural correspondence learning needed to make it appropriate for the task. For example, we propose a median-based classiﬁcation instead of the standard binary classiﬁcation used in previous work. Our results show that punctuation-based character n -grams form excellent pivot features. We also show how singular value decomposition plays a critical role in achieving domain adaptation, and that replacing (instead of concatenating) non-pivot features with correspondence features yields better performance.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1210"}, {"primary_key": "4048789", "vector": [], "sparse_vector": [], "title": "A Corpus-Based Analysis of Canonical Word Order of Japanese Double Object Constructions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The canonical word order of Japanese double object constructions has attracted considerable attention among linguists and has been a topic of many studies. However, most of these studies require either manual analyses or measurements of human characteristics such as brain activities or reading times for each example. Thus, while these analyses are reliable for the examples they focus on, they cannot be generalized to other examples. On the other hand, the trend of actual usage can be collected automatically from a large corpus. Thus, in this paper, we assume that there is a relationship between the canonical word order and the proportion of each word order in a large corpus and present a corpusbased analysis of canonical word order of Japanese double object constructions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1211"}, {"primary_key": "4048791", "vector": [], "sparse_vector": [], "title": "Roleo: Visualising Thematic Fit Spaces on the Web.", "authors": ["<PERSON><PERSON>", "Xudong Hong", "<PERSON>"], "summary": "In this paper, we present Roleo, a web tool for visualizing the vector spaces generated by the evaluation of distributional memory (DM) models over thematic fit judgements.A thematic fit judgement is a rating of the selectional preference of a verb for an argument that fills a given thematic role.The DM approach to thematic fit judgements involves the construction of a sub-space in which a prototypical role-filler can be built for comparison to the noun being judged.We describe a publicly-accessible web tool that allows for querying and exploring these spaces as well as a technique for visualizing thematic fit sub-spaces efficiently for web use.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4024"}, {"primary_key": "4048792", "vector": [], "sparse_vector": [], "title": "Model Architectures for Quotation Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Quotation detection is the task of locating spans of quoted speech in text.The state of the art treats this problem as a sequence labeling task and employs linear-chain conditional random fields.We question the efficacy of this choice: The Markov assumption in the model prohibits it from making joint decisions about the begin, end, and internal context of a quotation.We perform an extensive analysis with two new model architectures.We find that (a), simple boundary classification combined with a greedy prediction strategy is competitive with the state of the art; (b), a semi-Markov model significantly outperforms all others, by relaxing the Markov assumption.2 http://www.ims.uni-stuttgart.de/data/qsample 3PARC, wsj 2418 C1.Surface form, lemma, and PoS tag for all tokens within a window of ±5.C2.Bigrams of surface form, lemma, and PoS tag C3.Shape of ti C4.Is any token in a window of ±5 a named entity?C5.Does a quotation mark open or close at ti (determined by counting)?Is ti within quotation marks?C6.Is ti in the list of reporting verbs, noun cue verbs, titles, WordNet persons or organizations, and its VerbNet class C7.Do a sentence, paragraph, or the document begin or end at ti, ti-1, or ti+1?C8.Distance to sentence begin and end; sentence length C9.Does the sentence contain ti a pronoun/named entity/quotation mark?C10.Does a syntactic constituent starts or ends at ti? C11.Level of ti in the constituent tree C12.Label and level of the highest constituent in the tree starting at ti; label of ti's the parent node C13.Dependency relation with parent or any child of ti (with and without parent surface form) C14.Any conjunction of C5, C9, C10", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1164"}, {"primary_key": "4048793", "vector": [], "sparse_vector": [], "title": "Resolving References to Objects in Photographs using the Words-As-Classifiers Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A common use of language is to refer to visually present objects.Modelling it in computers requires modelling the link between language and perception.The \"words as classifiers\" model of grounded semantics views words as classifiers of perceptual contexts, and composes the meaning of a phrase through composition of the denotations of its component words.It was recently shown to perform well in a game-playing scenario with a small number of object types.We apply it to two large sets of real-world photographs that contain a much larger variety of object types and for which referring expressions are available.Using a pre-trained convolutional neural network to extract image region features, and augmenting these with positional information, we show that the model achieves performance competitive with the state of the art in a reference resolution task (given expression, find bounding box of its referent), while, as we argue, being conceptually simpler and more flexible.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1115"}, {"primary_key": "4048794", "vector": [], "sparse_vector": [], "title": "Word Alignment without NULL Words.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>&apo<PERSON>;an"], "summary": "In word alignment certain source words are only needed for fluency reasons and do not have a translation on the target side. Most word alignment models assume a target NULL word from which they generate these untranslatable source words. Hypothesising a target NULL word is not without problems, however. For example, because this NULL word has a position, it interferes with the distribution over alignment jumps. We present a word alignment model that accounts for untranslatable source words by generating them from preceding source words. It thereby removes the need for a target NULL word and only models alignments between word pairs that are actually observed in the data. Translation experiments on English paired with Czech, German, French and Japanese show that the model outperforms its traditional IBM counterparts in terms of BLEU score.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2028"}, {"primary_key": "4048795", "vector": [], "sparse_vector": [], "title": "Improving Neural Machine Translation Models with Monolingual Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neural Machine Translation (NMT) has obtained state-of-the art performance for several language pairs, while only using parallel data for training.Targetside monolingual data plays an important role in boosting fluency for phrasebased statistical machine translation, and we investigate the use of monolingual data for NMT.In contrast to previous work, which combines NMT models with separately trained language models, we note that encoder-decoder NMT architectures already have the capacity to learn the same information as a language model, and we explore strategies to train with monolingual data without changing the neural network architecture.By pairing monolingual training data with an automatic backtranslation, we can treat it as additional parallel training data, and we obtain substantial improvements on the WMT 15 task English↔German (****-3.7 BLEU), and for the low-resourced IWSLT 14 task Turkish→English (****-3.4BLEU), obtaining new state-of-the-art results.We also show that fine-tuning on in-domain monolingual and parallel data gives substantial improvements for the IWSLT 15 task English→German.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1009"}, {"primary_key": "4048796", "vector": [], "sparse_vector": [], "title": "Neural Machine Translation of Rare Words with Subword Units.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neural machine translation (NMT) models typically operate with a fixed vocabulary, but translation is an open-vocabulary problem.Previous work addresses the translation of out-of-vocabulary words by backing off to a dictionary.In this paper, we introduce a simpler and more effective approach, making the NMT model capable of open-vocabulary translation by encoding rare and unknown words as sequences of subword units.This is based on the intuition that various word classes are translatable via smaller units than words, for instance names (via character copying or transliteration), compounds (via compositional translation), and cognates and loanwords (via phonological and morphological transformations).We discuss the suitability of different word segmentation techniques, including simple character ngram models and a segmentation based on the byte pair encoding compression algorithm, and empirically show that subword models improve over a back-off dictionary baseline for the WMT 15 translation tasks English→German and English→Russian by up to 1.1 and 1.3 BLEU, respectively.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1162"}, {"primary_key": "4048797", "vector": [], "sparse_vector": [], "title": "Generating Factoid Questions With Recurrent Neural Networks: The 30M Factoid Question-Answer Corpus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>ülçeh<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1056"}, {"primary_key": "4048798", "vector": [], "sparse_vector": [], "title": "A Trainable Spaced Repetition Model for Language Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present half-life regression (HLR), a novel model for spaced repetition practice with applications to second language acquisition.HLR combines psycholinguistic theory with modern machine learning techniques, indirectly estimating the \"halflife\" of a word or concept in a student's long-term memory.We use data from Duolingo -a popular online language learning application -to fit HLR models, reducing error by 45%+ compared to several baselines at predicting student recall rates.HLR model weights also shed light on which linguistic concepts are systematically challenging for second language learners.Finally, HLR was able to improve Duolingo daily student engagement by 12% in an operational user study.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1174"}, {"primary_key": "4048799", "vector": [], "sparse_vector": [], "title": "RBPB: Regularization-Based Pattern Balancing Method for Event Extraction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Event extraction is a particularly challenging information extraction task, which intends to identify and classify event triggers and arguments from raw text.In recent works, when determining event types (trigger classification), most of the works are either pattern-only or feature-only.However, although patterns cannot cover all representations of an event, it is still a very important feature.In addition, when identifying and classifying arguments, previous works consider each candidate argument separately while ignoring the relationship between arguments.This paper proposes a Regularization-Based Pattern Balancing Method (RBPB).Inspired by the progress in representation learning, we use trigger embedding, sentence-level embedding and pattern features together as our features for trigger classification so that the effect of patterns and other useful features can be balanced.In addition, RBPB uses a regularization method to take advantage of the relationship between arguments.Experiments show that we achieve results better than current state-of-art equivalents.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1116"}, {"primary_key": "4048800", "vector": [], "sparse_vector": [], "title": "Incremental Acquisition of Verb Hypothesis Space towards Physical World Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As a new generation of cognitive robots start to enter our lives, it is important to enable robots to follow human commands and to learn new actions from human language instructions. To address this issue, this paper presents an approach that explicitly represents verb semantics through hypothesis spaces of ﬂuents and automatically acquires these hypothesis spaces by interacting with humans. The learned hypothesis spaces can be used to automatically plan for lower-level primitive actions towards physical world interaction. Our empirical results have shown that the representation of a hypothesis space of ﬂu-ents, combined with the learned hypothesis selection algorithm, outperforms a previous baseline. In addition, our approach applies incremental learning, which can contribute to life-long learning from humans in the future.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1011"}, {"primary_key": "4048801", "vector": [], "sparse_vector": [], "title": "Minimum Risk Training for Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Maosong Sun", "<PERSON>"], "summary": "We propose minimum risk training for end-to-end neural machine translation.Unlike conventional maximum likelihood estimation, minimum risk training is capable of optimizing model parameters directly with respect to arbitrary evaluation metrics, which are not necessarily differentiable.Experiments show that our approach achieves significant improvements over maximum likelihood estimation on a state-of-the-art neural machine translation system across various languages pairs.Transparent to architectures, our approach can be applied to more neural networks and potentially benefit more NLP tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1159"}, {"primary_key": "4048802", "vector": [], "sparse_vector": [], "title": "Detecting Common Discussion Topics Across Culture From News Reader Comments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Li<PERSON> Bing", "Yinqing Xu"], "summary": "News reader comments found in many on-line news websites are typically massive in amount.We investigate the task of Cultural-common Topic Detection (CTD), which is aimed at discovering common discussion topics from news reader comments written in different languages.We propose a new probabilistic graphical model called MCTA which can cope with the language gap and capture the common semantics in different languages.We also develop a partially collapsed Gibbs sampler which effectively incorporates the term translation relationship into the detection of cultural-common topics for model parameter learning.Experimental results show improvements over the state-of-the-art model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1064"}, {"primary_key": "4048803", "vector": [], "sparse_vector": [], "title": "Knowledge-Based Semantic Embedding for Machine Translation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, with the help of knowledge base, we build and formulate a semantic space to connect the source and target languages, and apply it to the sequence-to-sequence framework to propose a Knowledge-Based Semantic Embedding (KBSE) method.In our KB-SE method, the source sentence is firstly mapped into a knowledge based semantic space, and the target sentence is generated using a recurrent neural network with the internal meaning preserved.Experiments are conducted on two translation tasks, the electric business data and movie data, and the results show that our proposed method can achieve outstanding performance, compared with both the traditional SMT methods and the existing encoder-decoder models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1212"}, {"primary_key": "4048804", "vector": [], "sparse_vector": [], "title": "Neural Network-Based Model for Japanese Predicate Argument Structure Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a novel model for Japanese predicate argument structure (PAS) analysis based on a neural network framework. Japanese PAS analysis is challenging due to the tangled characteristics of the Japanese language, such as case disappearance and argument omission. To unravel this problem, we learn selectional preferences from a large raw corpus, and incorporate them into a SOTA PAS analysis model, which considers the consistency of all PASs in a given sentence. We demonstrate that the proposed PAS analysis model signiﬁcantly outperforms the base SOTA system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1117"}, {"primary_key": "4048805", "vector": [], "sparse_vector": [], "title": "Addressing Limited Data for Textual Entailment Across Domains.", "authors": ["Chaitanya P<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We seek to address the lack of labeled data (and high cost of annotation) for textual entailment in some domains.To that end, we first create (for experimental purposes) an entailment dataset for the clinical domain, and a highly competitive supervised entailment system, ENT, that is effective (out of the box) on two domains.We then explore self-training and active learning strategies to address the lack of labeled data.With self-training, we successfully exploit unlabeled data to improve over ENT by 15% F-score on the newswire domain, and 13% F-score on clinical data.On the other hand, our active learning experiments demonstrate that we can match (and even beat) ENT using only 6.6% of the training data in the clinical domain, and only 5.8% of the training data in the newswire domain.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1118"}, {"primary_key": "4048806", "vector": [], "sparse_vector": [], "title": "Improving Hypernymy Detection with an Integrated Path-based and Distributional Method.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Detecting hypernymy relations is a key task in NLP, which is addressed in the literature using two complementary approaches.Distributional methods, whose supervised variants are the current best performers, and path-based methods, which received less research attention.We suggest an improved path-based algorithm, in which the dependency paths are encoded using a recurrent neural network, that achieves results comparable to distributional methods.We then extend the approach to integrate both pathbased and distributional signals, significantly improving upon the state-of-the-art on this task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1226"}, {"primary_key": "4048807", "vector": [], "sparse_vector": [], "title": "One for All: Towards Language Independent Named Entity Linking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Entity linking (EL) is the task of disambiguating mentions in text by associating them with entries in a predefined database of mentions (persons, organizations, etc). Most previous EL research has focused mainly on one language, English, with less attention being paid to other languages, such as Spanish or Chinese. In this paper, we introduce LIEL, a Language Independent Entity Linking system, which provides an EL framework which, once trained on one language, works remarkably well on a number of different languages without change. LIEL makes a joint global prediction over the entire document, employing a discriminative reranking framework with many domain and language-independent feature functions. Experiments on numerous benchmark datasets, show that the proposed system, once trained on one language, English, outperforms several state-of-the-art systems in English (by 4 points) and the trained model also works very well on Spanish (14 points better than a competitor system), demonstrating the viability of the approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1213"}, {"primary_key": "4048809", "vector": [], "sparse_vector": [], "title": "Deep multi-task learning with low level tasks supervised at lower layers.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In all previous work on deep multi-task learning we are aware of, all task supervisions are on the same (outermost) layer. We present a multi-task learning architecture with deep bi-directional RNNs, where different tasks supervision can happen at different layers. We present experiments in syntactic chunking and CCG supertagging, coupled with the additional task of POS-tagging. We show that it is consistently better to have POS supervision at the innermost rather than the outermost layer. We argue that this is because “lowlevel” tasks are better kept at the lower layers, enabling the higher-level tasks to make use of the shared representation of the lower-level tasks. Finally, we also show how this architecture can be used for domain adaptation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2038"}, {"primary_key": "4048810", "vector": [], "sparse_vector": [], "title": "Learning Structured Predictors from Bandit Feedback for Interactive NLP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Structured prediction from bandit feed-back describes a learning scenario where instead of having access to a gold standard structure, a learner only receives partial feedback in form of the loss value of a predicted structure. We present new learning objectives and algorithms for this interactive scenario, focusing on convergence speed and ease of elicitability of feed-back. We present supervised-to-bandit simulation experiments for several NLP tasks (machine translation, sequence labeling, text classiﬁcation), showing that bandit learning from relative preferences eases feedback strength and yields improved empirical convergence.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1152"}, {"primary_key": "4048814", "vector": [], "sparse_vector": [], "title": "Syntactically Guided Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the use of hierarchical phrase-based SMT lattices in end-to-end neural machine translation (NMT).Weight pushing transforms the Hiero scores for complete translation hypotheses, with the full translation grammar score and full ngram language model score, into posteriors compatible with NMT predictive probabilities.With a slightly modified NMT beam-search decoder we find gains over both Hiero and NMT decoding alone, with practical advantages in extending NMT to very large input and output vocabularies.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2049"}, {"primary_key": "4048815", "vector": [], "sparse_vector": [], "title": "Annotating and Predicting Non-Restrictive Noun Phrase Modifications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The distinction between restrictive and non-restrictive modification in noun phrases is a well studied subject in linguistics.Automatically identifying non-restrictive modifiers can provide NLP applications with shorter, more salient arguments, which were found beneficial by several recent works.While previous work showed that restrictiveness can be annotated with high agreement, no large scale corpus was created, hindering the development of suitable classification algorithms.In this work we devise a novel crowdsourcing annotation methodology, and an accompanying large scale corpus.Then, we present a robust automated system which identifies non-restrictive modifiers, notably improving over prior methods.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1119"}, {"primary_key": "4048816", "vector": [], "sparse_vector": [], "title": "Specifying and Annotating Reduced Argument Span Via QA-SRL.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Prominent semantic annotations take an inclusive approach to argument span annotation, marking arguments as full constituency subtrees. Some works, however, showed that identifying a reduced argument span can be beneficial for various semantic tasks. While certain practical methods do extract reduced argument spans, such as in Open-IE , these solutions are often ad-hoc and system-dependent, with no commonly accepted standards. In this paper we propose a generic argument reduction criterion, along with an annotation procedure, and show that it can be consistently and intuitively annotated using the recent QA-SRL paradigm.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2077"}, {"primary_key": "4048817", "vector": [], "sparse_vector": [], "title": "MediaGist: A Cross-lingual Analyser of Aggregated News and Commentaries.", "authors": ["<PERSON>"], "summary": "We introduce MediaGist, an online system for crosslingual analysis of aggregated news and commentaries based on summarization and sentiment analysis technologies.It is designed to assist journalists to detect and explore news topics, which are controversially reported or discussed in different countries.News articles from current week are clustered separately in currently 5 languages and the clusters are then linked across languages.Sentiment analysis provides a basis to compute controversy scores and summaries help to explore the differences.Recognized entities play an important role in most of the system's modules and provide another way to explore the data.We demonstrate the capabilities of MediaGist by listing highlights from the last week and present a rough evaluation of the system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4025"}, {"primary_key": "4048818", "vector": [], "sparse_vector": [], "title": "On-line Active Reward Learning for Policy Optimisation in Spoken Dialogue Systems.", "authors": ["<PERSON><PERSON>-<PERSON><PERSON>", "Milica <PERSON>ic", "<PERSON>", "<PERSON><PERSON>-Bar<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Tsung<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ability to compute an accurate reward function is essential for optimising a dialogue policy via reinforcement learning. In real-world applications, using explicit user feedback as the reward signal is often unreliable and costly to collect. This problem can be mitigated if the user's intent is known in advance or data is available to pre-train a task success predictor off-line. In practice neither of these apply for most real world applications. Here we propose an on-line learning framework whereby the dialogue policy is jointly trained alongside the reward model via active learning with a Gaussian process model. This Gaussian process operates on a continuous space dialogue representation generated in an unsupervised fashion using a recurrent neural network encoder-decoder. The experimental results demonstrate that the proposed framework is able to significantly reduce data annotation costs and mitigate noisy user feedback in dialogue policy learning.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1230"}, {"primary_key": "4048819", "vector": [], "sparse_vector": [], "title": "On Approximately Searching for Similar Word Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We discuss an approximate similarity search for word embeddings, which is an operation to approximately find embeddings close to a given vector.We compared several metric-based search algorithms with hash-, tree-, and graphbased indexing from different aspects.Our experimental results showed that a graph-based indexing exhibits robust performance and additionally provided useful information, e.g., vector normalization achieves an efficient search with cosine similarity.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1214"}, {"primary_key": "4048820", "vector": [], "sparse_vector": [], "title": "Composing Distributed Representations of Relational Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning distributed representations for relation instances is a central technique in downstream NLP applications. In order to address semantic modeling of relational patterns, this paper constructs a new dataset that provides multiple similarity ratings for every pair of relational patterns on the existing dataset. In addition, we conduct a comparative study of different encoders including additive composition, RNN, LSTM, and GRU for composing distributed representations of relational patterns. We also present Gated Additive Composition, which is an enhancement of additive composition with the gating mechanism. Experiments show that the new dataset does not only enable detailed analyses of the different encoders, but also provides a gauge to predict successes of distributed representations of relational patterns in the relation classification task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1215"}, {"primary_key": "4048821", "vector": [], "sparse_vector": [], "title": "Target-Side Context for Discriminative Models in Statistical Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Discriminative translation models utilizing source context have been shown to help statistical machine translation performance.We propose a novel extension of this work using target context information.Surprisingly, we show that this model can be efficiently integrated directly in the decoding process.Our approach scales to large training data sizes and results in consistent improvements in translation quality on four language pairs.We also provide an analysis comparing the strengths of the baseline source-context model with our extended source-context and targetcontext model and we show that our extension allows us to better capture morphological coherence.Our work is freely available as part of <PERSON>.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1161"}, {"primary_key": "4048822", "vector": [], "sparse_vector": [], "title": "Bilingual Segmented Topic Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This study proposes the bilingual segmented topic model (BiSTM), which hierarchically models documents by treating each document as a set of segments, e.g., sections.While previous bilingual topic models, such as bilingual latent Dirichlet allocation (BiLDA) (<PERSON><PERSON><PERSON> et al., 2009;<PERSON> et al., 2009), consider only cross-lingual alignments between entire documents, the proposed model considers cross-lingual alignments between segments in addition to document-level alignments and assigns the same topic distribution to aligned segments.This study also presents a method for simultaneously inferring latent topics and segmentation boundaries, incorporating unsupervised topic segmentation (<PERSON> et al., 2013) into BiSTM.Experimental results show that the proposed model significantly outperforms BiLDA in terms of perplexity and demonstrates improved performance in translation pair extraction (up to +0.083 extraction accuracy).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1120"}, {"primary_key": "4048823", "vector": [], "sparse_vector": [], "title": "Improved Representation Learning for Question Answer Matching.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Passage-level question answer matching is a challenging task since it requires effective representations that capture the complex semantic relations between questions and answers.In this work, we propose a series of deep learning models to address passage answer selection.To match passage answers to questions accommodating their complex semantic relations, unlike most previous work that utilizes a single deep learning structure, we develop hybrid models that process the text using both convolutional and recurrent neural networks, combining the merits on extracting linguistic information from both structures.Additionally, we also develop a simple but effective attention mechanism for the purpose of constructing better answer representations according to the input question, which is imperative for better modeling long answer sequences.The results on two public benchmark datasets, InsuranceQA and TREC-QA, show that our proposed models outperform a variety of strong baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1044"}, {"primary_key": "4048824", "vector": [], "sparse_vector": [], "title": "Learning Semantically and Additively Compositional Distributional Representations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper connects a vector-based composition model to a formal semantics, the Dependency-based Compositional Semantics (DCS).We show theoretical evidence that the vector compositions in our model conform to the logic of DCS.Experimentally, we show that vector-based composition brings a strong ability to calculate similar phrases as similar vectors, achieving near state-of-the-art on a wide range of phrase similarity tasks and relation classification; meanwhile, DCS can guide building vectors for structured queries that can be directly executed.We evaluate this utility on sentence completion task and report a new state-of-the-art.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1121"}, {"primary_key": "4048825", "vector": [], "sparse_vector": [], "title": "GoWvis: A Web Application for Graph-of-Words-based Text Visualization and Summarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce GoWvis 1 , an interactive web application that represents any piece of text inputted by the user as a graph-ofwords and leverages graph degeneracy and community detection to generate an extractive summary (keyphrases and sentences) of the inputted text in an unsupervised fashion.The entire analysis can be fully customized via the tuning of many text preprocessing, graph building, and graph mining parameters.Our system is thus well suited to educational purposes, exploration and early research experiments.The new summarization strategy we propose also shows promise.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4026"}, {"primary_key": "4048826", "vector": [], "sparse_vector": [], "title": "Arabizi Identification in Twitter Data.", "authors": ["<PERSON><PERSON>"], "summary": "In this work we explore some challenges related to analysing one form of the Arabic language called Arabizi . Arabizi, a portmanteau of Araby-Englizi , meaning Arabic-English, is a digital trend in texting Non-Standard Arabic using Latin script. Arabizi users express their nat-ural dialectal Arabic in text without following a uniﬁed orthography. We address the challenge of identifying Arabizi from multi-lingual data in Twitter, a preliminary step for analysing sentiment from Arabizi data. We annotated a corpus of Twitter data streamed across two Arab countries, extracted linguistic features and trained a classiﬁer achieving an average Arabizi identiﬁcation accuracy of 94.5%. We also present the percentage of Arabizi usage on Twitter across both countries providing important insights for researchers in NLP and sociolinguistics.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3008"}, {"primary_key": "4048828", "vector": [], "sparse_vector": [], "title": "Domain Specific Named Entity Recognition Referring to the Real World by Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a method for referring to the real world to improve named entity recognition (NER) specialized for a domain.Our method adds a stacked autoencoder to a text-based deep neural network for NER.We first train the stacked auto-encoder only from the real world information, then the entire deep neural network from sentences annotated with NEs and accompanied by real world information.In our experiments, we took Japanese chess as the example.The dataset consists of pairs of a game state and commentary sentences about it annotated with gamespecific NE tags.We conducted NER experiments and showed that referring to the real world improves the NER accuracy.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2039"}, {"primary_key": "4048829", "vector": [], "sparse_vector": [], "title": "Compositional Learning of Embeddings for Relation Paths in Knowledge Base and Text.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hoifung Poon", "<PERSON>"], "summary": "Modeling relation paths has offered significant gains in embedding models for knowledge base (KB) completion.However, enumerating paths between two entities is very expensive, and existing approaches typically resort to approximation with a sampled subset.This problem is particularly acute when text is jointly modeled with KB relations and used to provide direct evidence for facts mentioned in it.In this paper, we propose the first exact dynamic programming algorithm which enables efficient incorporation of all relation paths of bounded length, while modeling both relation types and intermediate nodes in the compositional path representations.We conduct a theoretical analysis of the efficiency gain from the approach.Experiments on two datasets show that it addresses representational limitations in prior approaches and improves accuracy in KB completion.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1136"}, {"primary_key": "4048830", "vector": [], "sparse_vector": [], "title": "A Parallel-Hierarchical Model for Machine Comprehension on Sparse Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding unstructured text is a major goal within natural language processing.Comprehension tests pose questions based on short text passages to evaluate such understanding.In this work, we investigate machine comprehension on the challenging MCTest benchmark.Partly because of its limited size, prior work on MCTest has focused mainly on engineering better features.We tackle the dataset with a neural approach, harnessing simple neural networks arranged in a parallel hierarchy.The parallel hierarchy enables our model to compare the passage, question, and answer from a variety of trainable perspectives, as opposed to using a manually designed, rigid feature set.Perspectives range from the word level to sentence fragments to sequences of sentences; the networks operate only on word-embedding representations of text.When trained with a methodology designed to help cope with limited training data, our Parallel-Hierarchical model sets a new state of the art for MCTest, outperforming previous feature-engineered approaches slightly and previous neural approaches by a significant margin (over 15 percentage points).", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1041"}, {"primary_key": "4048831", "vector": [], "sparse_vector": [], "title": "Learning the Curriculum with Bayesian Optimization for Task-Specific Word Representation Learning.", "authors": ["<PERSON><PERSON>", "Manaal Faruqui", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We use Bayesian optimization to learn curricula for word representation learning, optimizing performance on downstream tasks that depend on the learned representations as features.The curricula are modeled by a linear ranking function which is the scalar product of a learned weight vector and an engineered feature vector that characterizes the different aspects of the complexity of each instance in the training corpus.We show that learning the curriculum improves performance on a variety of downstream tasks over random orders and in comparison to the natural corpus order.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1013"}, {"primary_key": "4048832", "vector": [], "sparse_vector": [], "title": "Modeling Coverage for Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "Zhengdong Lu", "<PERSON>", "<PERSON><PERSON>", "Hang Li"], "summary": "Attention mechanism has enhanced stateof-the-art Neural Machine Translation (NMT) by jointly learning to align and translate.It tends to ignore past alignment information, however, which often leads to over-translation and under-translation.To address this problem, we propose coverage-based NMT in this paper.We maintain a coverage vector to keep track of the attention history.The coverage vector is fed to the attention model to help adjust future attention, which lets NMT system to consider more about untranslated source words.Experiments show that the proposed approach significantly improves both translation quality and alignment quality over standard attention-based NMT. 1", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1008"}, {"primary_key": "4048833", "vector": [], "sparse_vector": [], "title": "Cross-lingual Models of Word Embeddings: An Empirical Comparison.", "authors": ["<PERSON><PERSON><PERSON>", "Manaal Faruqui", "<PERSON>", "<PERSON>"], "summary": "Despite interest in using cross-lingual knowledge to learn word embeddings for various tasks, a systematic comparison of the possible approaches is lacking in the literature.We perform an extensive evaluation of four popular approaches of inducing cross-lingual embeddings, each requiring a different form of supervision, on four typologically different language pairs.Our evaluation setup spans four different tasks, including intrinsic evaluation on mono-lingual and cross-lingual similarity, and extrinsic evaluation on downstream semantic and syntactic applications.We show that models which require expensive cross-lingual knowledge almost always perform better, but cheaply supervised models often prove competitive on certain tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1157"}, {"primary_key": "4048834", "vector": [], "sparse_vector": [], "title": "LiMoSINe Pipeline: Multilingual UIMA-based NLP Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Francisco <PERSON>-Albacete", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a robust and efficient parallelizable multilingual UIMA-based platform for automatically annotating textual inputs with different layers of linguistic description, ranging from surface level phenomena all the way down to deep discourse-level information.In particular, given an input text, the pipeline extracts: sentences and tokens; entity mentions; syntactic information; opinionated expressions; relations between entity mentions; co-reference chains and wikified entities.The system is available in two versions: a standalone distribution enables design and optimization of userspecific sub-modules, whereas a server-client distribution allows for straightforward highperformance NLP processing, reducing the engineering cost for higher-level tasks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4027"}, {"primary_key": "4048835", "vector": [], "sparse_vector": [], "title": "Significance of an Accurate Sandhi-Splitter in Shallow Parsing of Dravidian Languages.", "authors": ["Devadath V. V", "<PERSON><PERSON><PERSON>"], "summary": "This paper evaluates the challenges involved in shallow parsing of Dravidian languages which are highly agglutinative and morphologically rich.Text processing tasks in these languages are not trivial because multiple words concatenate to form a single string with morpho-phonemic changes at the point of concatenation.This phenomenon known as Sandhi, in turn complicates the individual word identification.Shallow parsing is the task of identification of correlated group of words given a raw sentence.The current work is an attempt to study the effect of <PERSON><PERSON> in building shallow parsers for Dravidian languages by evaluating its effect on Malayalam, one of the main languages from Dravidian family.We provide an in-depth analysis of effect of <PERSON><PERSON> in developing a robust shallow parser pipeline with experimental results emphasizing on how sensitive the individual components of shallow parser are, towards the accuracy of a sandhi splitter.Our work can serve as a guiding light for building robust text processing systems in Dravidian languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3006"}, {"primary_key": "4048836", "vector": [], "sparse_vector": [], "title": "The More Antecedents, the Merrier: Resolving Multi-Antecedent Anaphors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Anaphor resolution is an important task in NLP with many applications. Despite much research effort, it remains an open problem. The difficulty of the problem varies substantially across different sub-problems. One sub-problem, in particular, has been largely untouched by prior work despite occurring frequently throughout corpora: the anaphor that has multiple antecedents, which here we call multi-antecedent anaphors or manaphors. Current coreference resolvers restrict anaphors to at most a single antecedent. As we show in this paper, relaxing this constraint poses serious problems in coreference chain-building, where each chain is intended to refer to a single entity. This work provides a formalization of the new task with preliminary insights into multi-antecedent noun-phrase anaphors, and offers a method for resolving such cases that outperforms a number of baseline methods by a significant margin. Our system uses local agglomerative clustering on candidate antecedents and an existing coreference system to score clusters to determine which cluster of mentions is antecedent for a given anaphor. When we augment an existing coreference system with our proposed method, we observe a substantial increase in performance (0.6 absolute CoNLL F1) on an annotated", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1216"}, {"primary_key": "4048837", "vector": [], "sparse_vector": [], "title": "Unsupervised morph segmentation and statistical language models for vocabulary expansion.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This work explores the use of unsupervised morph segmentation along with statistical language models for the task of vocabulary expansion.Unsupervised vocabulary expansion has large potential for improving vocabulary coverage and performance in different natural language processing tasks, especially in lessresourced settings on morphologically rich languages.We propose a combination of unsupervised morph segmentation and statistical language models and evaluate on languages from the Babel corpus.The method is shown to perform well for all the evaluated languages when compared to the previous work on the task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2029"}, {"primary_key": "4048838", "vector": [], "sparse_vector": [], "title": "Beyond Plain Spatial Knowledge: Determining Where Entities Are and Are Not Located, and For How Long.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper complements semantic role representations with spatial knowledge beyond indicating plain locations.Namely, we extract where entities are (and are not) located, and for how long (seconds, hours, days, etc.).Crowdsourced annotations show that this additional knowledge is intuitive to humans and can be annotated by non-experts.Experimental results show that the task can be automated.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1142"}, {"primary_key": "4048839", "vector": [], "sparse_vector": [], "title": "One model, two languages: training bilingual parsers with harmonized treebanks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce an approach to train lexicalized parsers using bilingual corpora obtained by merging harmonized treebanks of different languages, producing parsers that can analyze sentences in either of the learned languages, or even sentences that mix both. We test the approach on the Universal Dependency Treebanks, training with <PERSON>t<PERSON><PERSON><PERSON> and MaltOptimizer. The results show that these bilingual parsers are more than competitive, as most combinations not only preserve accuracy, but some even achieve significant improvements over the corresponding monolingual parsers. Preliminary experiments also show the approach to be promising on texts with code-switching and when more languages are added.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2069"}, {"primary_key": "4048840", "vector": [], "sparse_vector": [], "title": "Detecting Mild Cognitive Impairment by Exploiting Linguistic Information from Transcripts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Magdolna <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2030"}, {"primary_key": "4048841", "vector": [], "sparse_vector": [], "title": "Don&apos;t Count, Predict! An Automatic Approach to Learning Sentiment Lexicons for Short Text.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe an efficient neural network method to automatically learn sentiment lexicons without relying on any manual resources.The method takes inspiration from the NRC method, which gives the best results in SemEval13 by leveraging emoticons in large tweets, using the PMI between words and tweet sentiments to define the sentiment attributes of words.We show that better lexicons can be learned by using them to predict the tweet sentiment labels.By using a very simple neural network, our method is fast and can take advantage of the same data volume as the NRC method.Experiments show that our lexicons give significantly better accuracies on multiple languages compared to the current best methods.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2036"}, {"primary_key": "4048842", "vector": [], "sparse_vector": [], "title": "Inferring Perceived Demographics from User Emotional Tone and User-Environment Emotional Contrast.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We examine communications in a social network to study user emotional contrast -the propensity of users to express different emotions than those expressed by their neighbors.Our analysis is based on a large Twitter dataset, consisting of the tweets of 123,513 users from the USA and Canada.Focusing on <PERSON><PERSON>'s basic emotions, we analyze differences between the emotional tone expressed by these users and their neighbors of different types, and correlate these differences with perceived user demographics.We demonstrate that many perceived demographic traits correlate with the emotional contrast between users and their neighbors.Unlike other approaches on inferring user attributes that rely solely on user communications, we explore the network structure and show that it is possible to accurately predict a range of perceived demographic traits based solely on the emotions emanating from users and their neighbors.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1148"}, {"primary_key": "4048843", "vector": [], "sparse_vector": [], "title": "On the Role of Seed Lexicons in Learning Bilingual Word Embeddings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A shared bilingual word embedding space (SBWES) is an indispensable resource in a variety of cross-language NLP and IR tasks.A common approach to the SB-WES induction is to learn a mapping function between monolingual semantic spaces, where the mapping critically relies on a seed word lexicon used in the learning process.In this work, we analyze the importance and properties of seed lexicons for the SBWES induction across different dimensions (i.e., lexicon source, lexicon size, translation method, translation pair reliability).On the basis of our analysis, we propose a simple but effective hybrid bilingual word embedding (BWE) model.This model (HYBWE) learns the mapping between two monolingual embedding spaces using only highly reliable symmetric translation pairs from a seed document-level embedding space.We perform bilingual lexicon learning (BLL) with 3 language pairs and show that by carefully selecting reliable translation pairs our new HYBWE model outperforms benchmarking BWE learning models, all of which use more expensive bilingual signals.Effectively, we demonstrate that a SBWES may be induced by leveraging only a very weak bilingual signal (document alignments) along with monolingual data.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1024"}, {"primary_key": "4048844", "vector": [], "sparse_vector": [], "title": "Is &quot;Universal Syntax&quot; Universally Useful for Learning Distributed Word Representations?", "authors": ["<PERSON>", "<PERSON>"], "summary": "Recent comparative studies have demonstrated the usefulness of dependencybased contexts (DEPS) for learning distributed word representations for similarity tasks.In English, DEPS tend to perform better than the more common, less informed bag-of-words contexts (BOW).In this paper, we present the first crosslinguistic comparison of different context types for three different languages.DEPS are extracted from \"universal parses\" without any language-specific optimization.Our results suggest that the universal DEPS (UDEPS) are useful for detecting functional similarity (e.g., verb similarity, solving syntactic analogies) among languages, but their advantage over BOW is not as prominent as previously reported on English.We also show that simple \"post-parsing\" filtering of useful UDEPS contexts leads to consistent improvements across languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2084"}, {"primary_key": "4048845", "vector": [], "sparse_vector": [], "title": "Multi-Modal Representations for Improved Bilingual Lexicon Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent work has revealed the potential of using visual representations for bilingual lexicon learning (BLL).Such image-based BLL methods, however, still fall short of linguistic approaches.In this paper, we propose a simple yet effective multimodal approach that learns bilingual semantic representations that fuse linguistic and visual input.These new bilingual multi-modal embeddings display significant performance gains in the BLL task for three language pairs on two benchmarking test sets, outperforming linguistic-only BLL models using three different types of state-of-the-art bilingual word embeddings, as well as visual-only BLL models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2031"}, {"primary_key": "4048846", "vector": [], "sparse_vector": [], "title": "Take and Took, <PERSON><PERSON><PERSON> and <PERSON>, Book and Read: Evaluating the Utility of Vector Differences for Lexical Relation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work has shown that simple vector subtraction over word embeddings is surprisingly effective at capturing different lexical relations, despite lacking explicit supervision.Prior work has evaluated this intriguing result using a word analogy prediction formulation and hand-selected relations, but the generality of the finding over a broader range of lexical relation types and different learning settings has not been evaluated.In this paper, we carry out such an evaluation in two learning settings:(1) spectral clustering to induce word relations, and (2) supervised learning to classify vector differences into relation types.We find that word embeddings capture a surprising amount of information, and that, under suitable supervised training, vector subtraction generalises well to a broad range of relations, including over unseen lexical items.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1158"}, {"primary_key": "4048847", "vector": [], "sparse_vector": [], "title": "Automatic Labeling of Topic Models Using Text Summaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Labeling topics learned by topic models is a challenging problem.Previous studies have used words, phrases and images to label topics.In this paper, we propose to use text summaries for topic labeling.Several sentences are extracted from the most related documents to form the summary for each topic.In order to obtain summaries with both high relevance, coverage and discrimination for all the topics, we propose an algorithm based on submodular optimization.Both automatic and manual analysis have been conducted on two real document collections, and we find 1) the summaries extracted by our proposed algorithm are superior over the summaries extracted by existing popular summarization methods; 2) the use of summaries as labels has obvious advantages over the use of words and phrases.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1217"}, {"primary_key": "4048848", "vector": [], "sparse_vector": [], "title": "Larger-Context Language Modelling with Recurrent Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we propose a novel method to incorporate corpus-level discourse information into language modelling.We call this larger-context language model.We introduce a late fusion approach to a recurrent language model based on long shortterm memory units (LSTM), which helps the LSTM unit keep intra-sentence dependencies and inter-sentence dependencies separate from each other.Through the evaluation on four corpora (IMDB, BBC, Penn TreeBank, and Fil9), we demonstrate that the proposed model improves perplexity significantly.In the experiments, we evaluate the proposed approach while varying the number of context sentences and observe that the proposed late fusion is superior to the usual way of incorporating additional inputs to the LSTM.By analyzing the trained larger-context language model, we discover that content words, including nouns, adjectives and verbs, benefit most from an increasing number of context sentences.This analysis suggests that larger-context language model improves the unconditional language model by capturing the theme of a document better and more easily.* Recently, (<PERSON> et al., 2015) independently proposed a similar approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1125"}, {"primary_key": "4048849", "vector": [], "sparse_vector": [], "title": "Graph-based Dependency Parsing with Bidirectional LSTM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a neural network model for graph-based dependency parsing which utilizes Bidirectional LSTM (BLSTM) to capture richer contextual information instead of using high-order factorization, and enable our model to use much fewer features than previous work.In addition, we propose an effective way to learn sentence segment embedding on sentence-level based on an extra forward LSTM network.Although our model uses only first-order factorization, experiments on English Peen Treebank and Chinese Penn Treebank show that our model could be competitive with previous higher-order graph-based dependency parsing models and state-of-the-art models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1218"}, {"primary_key": "4048850", "vector": [], "sparse_vector": [], "title": "Relation Classification via Multi-Level Attention CNNs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Relation classification is a crucial ingredient in numerous information extraction systems seeking to mine structured facts from text.We propose a novel convolutional neural network architecture for this task, relying on two levels of attention in order to better discern patterns in heterogeneous contexts.This architecture enables endto-end learning from task-specific labeled data, forgoing the need for external knowledge such as explicit dependency structures.Experiments show that our model outperforms previous state-of-the-art methods, including those relying on much richer forms of prior knowledge.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1123"}, {"primary_key": "4048851", "vector": [], "sparse_vector": [], "title": "CSE: Conceptual Sentence Embeddings based on Attention Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chong Feng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most sentence embedding models typically represent each sentence only using word surface, which makes these models indiscriminative for ubiquitous homonymy and polysemy.In order to enhance representation capability of sentence, we employ conceptualization model to assign associated concepts for each sentence in the text corpus, and then learn conceptual sentence embedding (CSE).Hence, this semantic representation is more expressive than some widely-used text representation models such as latent topic model, especially for short-text.Moreover, we further extend CSE models by utilizing a local attention-based model that select relevant words within the context to make more efficient prediction.In the experiments, we evaluate the CSE models on two tasks, text classification and information retrieval.The experimental results show that the proposed models outperform typical sentence embed-ding models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1048"}, {"primary_key": "4048852", "vector": [], "sparse_vector": [], "title": "Inner Attention based Recurrent Neural Networks for Answer Selection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "sentences (<PERSON> et al., 2015;<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2015;<PERSON> et al., 2015). Based on recurrent neural networks (RNN), external attention information was added to hidden representations to get an attentive sentence representation.Despite the improvement over nonattentive models, the attention mechanism under RNN is not well studied.In this work, we analyze the deficiency of traditional attention based RNN models quantitatively and qualitatively.Then we present three new RNN models that add attention information before RNN hidden representation, which shows advantage in representing sentence and achieves new stateof-art results in answer selection task.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1122"}, {"primary_key": "4048853", "vector": [], "sparse_vector": [], "title": "Knowledge Base Completion via Coupled Path Ranking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Knowledge bases (KBs) are often greatly incomplete, necessitating a demand for KB completion. The path ranking algorithm (PRA) is one of the most promising approaches to this task. Previous work on PRA usually follows a single-task learning paradigm, building a prediction model for each relation independently with its own training data. It ignores meaningful associations among certain relations, and might not get enough training data for less frequent relations. This paper proposes a novel multi-task learning framework for PRA, referred to as coupled PRA (CPRA). It first devises an agglomerative clustering strategy to automatically discover relations that are highly correlated to each other, and then employs a multi-task learning strategy to effectively couple the prediction of such relations. As such, CPRA takes into account relation association and enables implicit data sharing among them. We empirically evaluate CPRA on benchmark data created from Freebase. Experimental results show that CPRA can effectively identify coherent clusters in which relations are highly correlated. By further coupling such relations, CPRA significantly outperforms PRA, in terms of both predictive accuracy and model interpretability.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1124"}, {"primary_key": "4048854", "vector": [], "sparse_vector": [], "title": "Learning Language Games through Interaction.", "authors": ["Sid<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new language learning setting relevant to building adaptive natural language interfaces.It is inspired by <PERSON><PERSON><PERSON><PERSON>'s language games: a human wishes to accomplish some task (e.g., achieving a certain configuration of blocks), but can only communicate with a computer, who performs the actual actions (e.g., removing all red blocks).The computer initially knows nothing about language and therefore must learn it from scratch through interaction, while the human adapts to the computer's capabilities.We created a game called SHRDLURN in a blocks world and collected interactions from 100 people playing it.First, we analyze the humans' strategies, showing that using compositionality and avoiding synonyms correlates positively with task performance.Second, we compare computer strategies, showing that modeling pragmatics on a semantic parsing model accelerates learning for more strategic players.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1224"}, {"primary_key": "4048855", "vector": [], "sparse_vector": [], "title": "Dimensional Sentiment Analysis Using a Regional CNN-LSTM Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dimensional sentiment analysis aims to recognize continuous numerical values in multiple dimensions such as the valencearousal (VA) space. Compared to the categorical approach that focuses on sentiment classification such as binary classification (i.e., positive and negative), the dimensional approach can provide more fine-grained sentiment analysis. This study proposes a regional CNN-LSTM model consisting of two parts: regional CNN and LSTM to predict the VA ratings of texts. Unlike a conventional CNN which considers a whole text as input, the proposed regional CNN uses an individual sentence as a region, dividing an input text into several regions such that the useful affective information in each region can be extracted and weighted according to their contribution to the VA prediction. Such regional information is sequentially integrated across regions using LSTM for VA prediction. By combining the regional CNN and LSTM, both local (regional) information within sentences and long-distance dependency across sentences can be considered in the prediction process. Experimental results show that the proposed method outperforms lexicon-based, regression-based, and NN-based methods proposed in previous studies.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2037"}, {"primary_key": "4048856", "vector": [], "sparse_vector": [], "title": "Using mention accessibility to improve coreference resolution.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Modern coreference resolution systems require linguistic and general knowledge typically sourced from costly, manually curated resources.Despite their intuitive appeal, results have been mixed.In this work, we instead implement fine-grained surface-level features motivated by cognitive theory.Our novel fine-grained feature specialisation approach significantly improves the performance of a strong baseline, achieving state-of-the-art results of 65.29 and 61.13% on CoNLL-2012 using gold and automatic preprocessing, with system extracted mentions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2070"}, {"primary_key": "4048857", "vector": [], "sparse_vector": [], "title": "Is This Post Persuasive? Ranking Argumentative Comments in Online Forum.", "authors": ["Zhongyu Wei", "<PERSON>", "<PERSON>"], "summary": "In this paper we study how to identify persuasive posts in the online forum discussions, using data from Change My View sub-Reddit.Our analysis confirms that the users' voting score for a comment is highly correlated with its metadata information such as published time and author reputation.In this work, we propose and evaluate other features to rank comments for their persuasive scores, including textual information in the comments and social interaction related features.Our experiments show that the surface textual features do not perform well compared to the argumentation based features, and the social interaction based features are effective especially when more users participate in the discussion.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2032"}, {"primary_key": "4048859", "vector": [], "sparse_vector": [], "title": "The Creation and Analysis of a Website Privacy Policy Corpus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1126"}, {"primary_key": "4048861", "vector": [], "sparse_vector": [], "title": "Exploiting Linguistic Features for Sentence Completion.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper presents a novel approach to automated sentence completion based on pointwise mutual information (PMI).Feature sets are created by fusing the various types of input provided to other classes of language models, ultimately allowing multiple sources of both local and distant information to be considered.Furthermore, it is shown that additional precision gains may be achieved by incorporating feature sets of higher-order n-grams.Experimental results demonstrate that the PMI model outperforms all prior models and establishes a new state-of-the-art result on the Microsoft Research Sentence Completion Challenge.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2071"}, {"primary_key": "4048862", "vector": [], "sparse_vector": [], "title": "Sentiment Domain Adaptation with Multiple Sources.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Domain adaptation is an important research topic in sentiment analysis area.Existing domain adaptation methods usually transfer sentiment knowledge from only one source domain to target domain.In this paper, we propose a new domain adaptation approach which can exploit sentiment knowledge from multiple source domains.We first extract both global and domain-specific sentiment knowledge from the data of multiple source domains using multi-task learning.Then we transfer them to target domain with the help of words' sentiment polarity relations extracted from the unlabeled target domain data.The similarities between target domain and different source domains are also incorporated into the adaptation process.Experimental results on benchmark dataset show the effectiveness of our approach in improving cross-domain sentiment classification performance.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1029"}, {"primary_key": "4048863", "vector": [], "sparse_vector": [], "title": "Models and Inference for Prefix-Constrained Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We apply phrase-based and neural models to a core task in interactive machine translation: suggesting how to complete a partial translation.For the phrase-based system, we demonstrate improvements in suggestion quality using novel objective functions, learning techniques, and inference algorithms tailored to this task.Our contributions include new tunable metrics, an improved beam search strategy, an n-best extraction method that increases suggestion diversity, and a tuning procedure for a hierarchical joint model of alignment and translation.The combination of these techniques improves next-word suggestion accuracy dramatically from 28.5% to 41.2% in a large-scale English-German experiment.Our recurrent neural translation system increases accuracy yet further to 53.0%, but inference is two orders of magnitude slower.Manual error analysis shows the strengths and weaknesses of both approaches.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1007"}, {"primary_key": "4048864", "vector": [], "sparse_vector": [], "title": "An Efficient Cross-lingual Model for Sentence Classification Using Convolutional Neural Network.", "authors": ["<PERSON><PERSON>", "Zhongyu Wei", "<PERSON>"], "summary": "In this paper, we propose a cross-lingual convolutional neural network (CNN) model that is based on word and phrase embeddings learned from unlabeled data in two languages and dependency grammar.Compared to traditional machine translation (MT) based methods for cross lingual sentence modeling, our model is much simpler and does not need parallel corpora or language specific features.We only use a bilingual dictionary and dependency parser.This makes our model particularly appealing for resource poor languages.We evaluate our model using English and Chinese data on several sentence classification tasks.We show that our model achieves a comparable and even better performance than the traditional MT-based method.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-3019"}, {"primary_key": "4048865", "vector": [], "sparse_vector": [], "title": "Sequence-based Structured Prediction for Semantic Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose an approach for semantic parsing that uses a recurrent neural network to map a natural language question into a logical form representation of a KB query. Building on recent work by (<PERSON> et al., 2015), the interpretable logical forms, which are structured objects obeying certain constraints, are enumerated by an underlying grammar and are paired with their canonical realizations. In order to use sequence prediction, we need to sequentialize these logical forms. We compare three sequentializations: a direct linearization of the logical form, a linearization of the associated canonical realization, and a sequence consisting of derivation steps relative to the underlying grammar. We also show how grammatical constraints on the derivation sequence can easily be integrated inside the RNN-based sequential predictor. Our experiments show important improvements over previous results for the same dataset, and also demonstrate the advantage of incorporating the grammatical constraints.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1127"}, {"primary_key": "4048866", "vector": [], "sparse_vector": [], "title": "Entropy Converges Between Dialogue Participants: Explanations from an Information-Theoretic Perspective.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The applicability of entropy rate constancy to dialogue is examined on two spoken dialogue corpora.The principle is found to hold; however, new entropy change patterns within the topic episodes of dialogue are described, which are different from written text.Speaker's dynamic roles as topic initiators and topic responders are associated with decreasing and increasing entropy, respectively, which results in local convergence between these speakers in each topic episode.This implies that the sentence entropy in dialogue is conditioned on different contexts determined by the speaker's roles.Explanations from the perspectives of grounding theory and interactive alignment are discussed, resulting in a novel, unified informationtheoretic approach of dialogue.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1051"}, {"primary_key": "4048867", "vector": [], "sparse_vector": [], "title": "Convergence of Syntactic Complexity in Conversation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Using corpus data of spoken dialogue, we examine the convergence of syntactic complexity levels between interlocutors in natural conversations, as it occurs within spans of topic episodes. The findings of general convergence in the Switchboard and BNC corpora are compatible with an information-theoretic model of dialogue and with Interactive Alignment Theory.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2072"}, {"primary_key": "4048868", "vector": [], "sparse_vector": [], "title": "Question Answering on Freebase via Relation Extraction and Textual Evidence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yansong Feng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing knowledge-based question answering systems often rely on small annotated training data.While shallow methods like relation extraction are robust to data scarcity, they are less expressive than the deep meaning representation methods like semantic parsing, thereby failing at answering questions involving multiple constraints.Here we alleviate this problem by empowering a relation extraction method with additional evidence from Wikipedia.We first present a neural network based relation extractor to retrieve the candidate answers from Freebase, and then infer over Wikipedia to validate these answers.Experiments on the WebQuestions question answering dataset show that our method achieves an F 1 of 53.3%, a substantial improvement over the state-of-the-art.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1220"}, {"primary_key": "4048869", "vector": [], "sparse_vector": [], "title": "Dependency-based Gated Recursive Neural Network for Chinese Word Segmentation.", "authors": ["<PERSON><PERSON><PERSON> Xu", "<PERSON>"], "summary": "Recently, many neural network models have been applied to Chinese word segmentation. However, such models focus more on collecting local information while long distance dependencies are not well learned. To integrate local features with long distance dependencies, we propose a dependency-based gated recursive neural network. Local features are first collected by bi-directional long short term memory network, then combined and refined to long distance dependencies via gated recursive neural network. Experimental results show that our model is a competitive model for Chinese word segmentation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2092"}, {"primary_key": "4048870", "vector": [], "sparse_vector": [], "title": "Intrinsic Subspace Evaluation of Word Embedding Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new methodology for intrinsic evaluation of word representations.Specifically, we identify four fundamental criteria based on the characteristics of natural language that pose difficulties to NLP systems; and develop tests that directly show whether or not representations contain the subspaces necessary to satisfy these criteria.Current intrinsic evaluations are mostly based on the overall similarity or full-space similarity of words and thus view vector representations as points.We show the limits of these point-based intrinsic evaluations.We apply our evaluation methodology to the comparison of a count vector model and several neural network models and demonstrate important properties of these models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1023"}, {"primary_key": "4048871", "vector": [], "sparse_vector": [], "title": "DocChat: An Information Retrieval Approach for Chatbot Engines Using Unstructured Documents.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most current chatbot engines are designed to reply to user utterances based on existing utterance-response (or Q-R) 1 pairs.In this paper, we present DocChat, a novel information retrieval approach for chatbot engines that can leverage unstructured documents, instead of Q-R pairs, to respond to utterances.A learning to rank model with features designed at different levels of granularity is proposed to measure the relevance between utterances and responses directly.We evaluate our proposed approach in both English and Chinese: (i) For English, we evaluate Doc-Chat on WikiQA and QASent, two answer sentence selection tasks, and compare it with state-of-the-art methods.Reasonable improvements and good adaptability are observed.(ii) For Chinese, we compare DocChat with XiaoIce 2 , a famous chitchat engine in China, and side-by-side evaluation shows that DocChat is a perfect complement for chatbot engines using Q-R pairs as main source of responses.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1049"}, {"primary_key": "4048872", "vector": [], "sparse_vector": [], "title": "Chinese Couplet Generation with Neural Network Structures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaohua Hu", "<PERSON>"], "summary": "Part of the unique cultural heritage of China is the Chinese couplet. Given a sentence (namely an antecedent clause), people reply with another sentence (namely a subsequent clause) equal in length. Moreover, a special phenomenon is that corresponding characters from the same position in the two clauses match each other by following certain constraints on semantic and/or syntactic relatedness. Automatic couplet generation by computer is viewed as a difficult problem and has not been fully explored. In this paper, we formulate the task as a natural language generation problem using neural network structures. Given the issued antecedent clause, the system generates the subsequent clause via sequential language modeling. To satisfy special characteristics of couplets, we incorporate the attention mechanism and polishing schema into the encoding-decoding process. The couplet is generated incrementally and iteratively. A comprehensive evaluation, using perplexity and BLEU measurements as well as human judgments, has demonstrated the effectiveness of our proposed approach.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1222"}, {"primary_key": "4048873", "vector": [], "sparse_vector": [], "title": "Vector-space topic models for detecting Alzheimer&apos;s disease.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Semantic deficit is a symptom of language impairment in Alzheimer's disease (AD).We present a generalizable method for automatic generation of information content units (ICUs) for a picture used in a standard clinical task, achieving high recall, 96.8%, of human-supplied ICUs.We use the automatically generated topic model to extract semantic features, and train a random forest classifier to achieve an F-score of 0.74 in binary classification of controls versus people with AD using a set of only 12 features.This is comparable to results (0.72 F-score) with a set of 85 manual features.Adding semantic information to a set of standard lexicosyntactic and acoustic features improves F-score to 0.80.While control and dementia subjects discuss the same topics in the same contexts, controls are more informative per second of speech.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1221"}, {"primary_key": "4048874", "vector": [], "sparse_vector": [], "title": "A Discriminative Topic Model using Document Network Structure.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Document collections often have links between documents—citations, hyperlinks, or revisions—and which links are added is often based on topical similarity. To model these intuitions, we introduce a new topic model for documents situated within a network structure, integrating latent blocks of documents with a max-margin learning criterion for link prediction using topicand word-level features. Experiments on a scientific paper dataset and collection of webpages show that, by more robustly exploiting the rich link structure within a document network, our model improves link prediction, topic quality, and block distributions.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1065"}, {"primary_key": "4048875", "vector": [], "sparse_vector": [], "title": "The Value of Semantic Parse Labeling for Knowledge Base Question Answering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate the value of collecting semantic parse labels for knowledge base question answering. In particular, (1) unlike previous studies on small-scale datasets, we show that learning from labeled semantic parses significantly improves overall performance, resulting in absolute 5 point gain compared to learning from answers, (2) we show that with an appropriate user interface, one can obtain semantic parses with high accuracy and at a cost comparable or lower than obtaining just answers, and (3) we have created and shared the largest semantic-parse labeled dataset to date in order to advance research in question answering.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2033"}, {"primary_key": "4048876", "vector": [], "sparse_vector": [], "title": "new/s/leak - Information Extraction and Visualization for Investigative Data Journalists.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of ACL-2016 System Demonstrations. 2016.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-4028"}, {"primary_key": "4048877", "vector": [], "sparse_vector": [], "title": "Learning Word Meta-Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Word embeddings -distributed representations of words -in deep learning are beneficial for many tasks in NLP.However, different embedding sets vary greatly in quality and characteristics of the captured information.Instead of relying on a more advanced algorithm for embedding learning, this paper proposes an ensemble approach of combining different public embedding sets with the aim of learning metaembeddings.Experiments on word similarity and analogy tasks and on part-of-speech tagging show better performance of metaembeddings compared to individual embedding sets.One advantage of metaembeddings is the increased vocabulary coverage.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1128"}, {"primary_key": "4048879", "vector": [], "sparse_vector": [], "title": "Unsupervised Person Slot Filling based on Graph Mining.", "authors": ["<PERSON><PERSON>", "<PERSON>ng <PERSON>"], "summary": "Slot filling aims to extract the values (slot fillers) of specific attributes (slots types) for a given entity (query) from a largescale corpus.Slot filling remains very challenging over the past seven years.We propose a simple yet effective unsupervised approach to extract slot fillers based on the following two observations: (1) a trigger is usually a salient node relative to the query and filler nodes in the dependency graph of a context sentence; (2) a relation is likely to exist if the query and candidate filler nodes are strongly connected by a relation-specific trigger.Thus we design a graph-based algorithm to automatically identify triggers based on personalized PageRank and Affinity Propagation for a given (query, filler) pair and then label the slot type based on the identified triggers.Our approach achieves 11.6%-25% higher F-score over state-ofthe-art English slot filling methods.Our experiments also demonstrate that as long as a few trigger seeds, name tagging and dependency parsing capabilities exist, this approach can be quickly adapted to any language and new slot types.Our promising results on Chinese slot filling can serve as a new benchmark.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1005"}, {"primary_key": "4048880", "vector": [], "sparse_vector": [], "title": "User Embedding for Scholarly Microblog Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, many scholarly messages are posted on Chinese microblogs and more and more researchers tend to find scholarly information on microblogs.In order to exploit microblogging to benefit scientific research, we propose a scholarly microblog recommendation system in this study.It automatically collects and mines scholarly information from Chinese microblogs, and makes personalized recommendations to researchers.We propose two different neural network models which learn the vector representations for both users and microblog texts.Then the recommendation is accomplished based on the similarity between a user's vector and a microblog text's vector.We also build a dataset for this task.The two embedding models are evaluated on the dataset and show good results compared to several baselines.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2073"}, {"primary_key": "4048881", "vector": [], "sparse_vector": [], "title": "Modelling the Interpretation of Discourse Connectives by Bayesian Pragmatics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a framework to model human comprehension of discourse connectives.Following the Bayesian pragmatic paradigm, we advocate that discourse connectives are interpreted based on a simulation of the production process by the speaker, who, in turn, considers the ease of interpretation for the listener when choosing connectives.Evaluation against the sense annotation of the Penn Discourse Treebank confirms the superiority of the model over literal comprehension.A further experiment demonstrates that the proposed model also improves automatic discourse parsing.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2086"}, {"primary_key": "4048882", "vector": [], "sparse_vector": [], "title": "Easy Things First: Installments Improve Referring Expression Generation for Objects in Photographs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Research on generating referring expressions has so far mostly focussed on \"oneshot reference\", where the aim is to generate a single, discriminating expression.In interactive settings, however, it is not uncommon for reference to be established in \"installments\", where referring information is offered piecewise until success has been confirmed.We show that this strategy can also be advantageous in technical systems that only have uncertain access to object attributes and categories.We train a recently introduced model of grounded word meaning on a data set of REs for objects in images and learn to predict semantically appropriate expressions.In a human evaluation, we observe that users are sensitive to inadequate object names -which unfortunately are not unlikely to be generated from low-level visual input.We propose a solution inspired from human task-oriented interaction and implement strategies for avoiding and repairing semantically inaccurate words.We enhance a word-based REG with contextaware, referential installments and find that they substantially improve the referential success of the system.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1058"}, {"primary_key": "4048883", "vector": [], "sparse_vector": [], "title": "Learning Concept Taxonomies from Multi-modal Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>g Yan", "<PERSON>"], "summary": "We study the problem of automatically building hypernym taxonomies from textual and visual data.Previous works in taxonomy induction generally ignore the increasingly prominent visual data, which encode important perceptual semantics.Instead, we propose a probabilistic model for taxonomy induction by jointly leveraging text and images.To avoid hand-crafted feature engineering, we design end-to-end features based on distributed representations of images and words.The model is discriminatively trained given a small set of existing ontologies and is capable of building full taxonomies from scratch for a collection of unseen conceptual label items with associated images.We evaluate our model and features on the WordNet hierarchies, where our system outperforms previous approaches by a large gap.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1169"}, {"primary_key": "4048884", "vector": [], "sparse_vector": [], "title": "A Continuous Space Rule Selection Model for Syntax-based Statistical Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One of the major challenges for statistical machine translation (SMT) is to choose the appropriate translation rules based on the sentence context.This paper proposes a continuous space rule selection (CSRS) model for syntax-based SMT to perform this context-dependent rule selection.In contrast to existing maximum entropy based rule selection (MERS) models, which use discrete representations of words as features, the CSRS model is learned by a feed-forward neural network and uses real-valued vector representations of words, allowing for better generalization.In addition, we propose a method to train the rule selection models only on minimal rules, which are more frequent and have richer training data compared to non-minimal rules.We tested our model on different translation tasks and the CSRS model outperformed a baseline without rule selection and the previous MERS model by up to 2.2 and 1.1 points of BLEU score respectively.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1130"}, {"primary_key": "4048885", "vector": [], "sparse_vector": [], "title": "Stack-propagation: Improved Representation Learning for Syntax.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traditional syntax models typically leverage part-of-speech (POS) information by constructing features from hand-tuned templates.We demonstrate that a better approach is to utilize POS tags as a regularizer of learned representations.We propose a simple method for learning a stacked pipeline of models which we call \"stack-propagation\".We apply this to dependency parsing and tagging, where we use the hidden layer of the tagger network as a representation of the input tokens for the parser.At test time, our parser does not require predicted POS tags.On 19 languages from the Universal Dependencies, our method is 1.3% (absolute) more accurate than a state-of-the-art graph-based approach and 2.7% more accurate than the most comparable greedy model.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1147"}, {"primary_key": "4048886", "vector": [], "sparse_vector": [], "title": "Towards Constructing Sports News from Live Text Commentary.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we investigate the possibility to automatically generate sports news from live text commentary scripts. As a preliminary study, we treat this task as a special kind of document summarization based on sentence extraction. We formulate the task in a supervised learning to rank framework, utilizing both traditional sentence features for generic document summarization and novelly designed task-speciﬁc features. To tackle the problem of local redundancy, we also propose a probabilistic sentence selection algorithm. Experiments on our collected data from football live commentary scripts and corresponding sports news demonstrate the feasibility of this task. Evaluation results show that our methods are indeed appropriate for this task, outperforming several baseline methods in different aspects.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1129"}, {"primary_key": "4048887", "vector": [], "sparse_vector": [], "title": "Transition-Based Neural Word Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Character-based and word-based methods are two different solutions for Chinese word segmentation, the former exploiting sequence labeling models over characters and the latter using word-level features. Neural models have been exploited for character-based Chinese word segmentation, giving high accuracies by making use of external character embeddings, yet requiring less feature engineering. In this paper, we study a neural model for word-based Chinese word segmentation, by replacing the manually-designed discrete features with neural features in a transition-based word segmentation framework. Experimental results demonstrate that word features lead to comparable performance to the best systems in the literature, and a further combination of discrete and neural features obtains top accuracies on several benchmarks.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1040"}, {"primary_key": "4048888", "vector": [], "sparse_vector": [], "title": "Probabilistic Graph-based Dependency Parsing with Convolutional Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents neural probabilistic parsing models which explore up to thirdorder graph-based parsing with maximum likelihood training criteria.Two neural network extensions are exploited for performance improvement.Firstly, a convolutional layer that absorbs the influences of all words in a sentence is used so that sentence-level information can be effectively captured.Secondly, a linear layer is added to integrate different order neural models and trained with perceptron method.The proposed parsers are evaluated on English and Chinese Penn Treebanks and obtain competitive accuracies.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1131"}, {"primary_key": "4048889", "vector": [], "sparse_vector": [], "title": "Jointly Event Extraction and Visualization on Twitter via Probabilistic Modelling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Event extraction from texts aims to detect structured information such as what has happened, to whom, where and when.Event extraction and visualization are typically considered as two different tasks.In this paper, we propose a novel approach based on probabilistic modelling to jointly extract and visualize events from tweets where both tasks benefit from each other.We model each event as a joint distribution over named entities, a date, a location and event-related keywords.Moreover, both tweets and event instances are associated with coordinates in the visualization space.The manifold assumption that the intrinsic geometry of tweets is a low-rank, non-linear manifold within the high-dimensional space is incorporated into the learning framework using a regularization.Experimental results show that the proposed approach can effectively deal with both event extraction and visualization and performs remarkably better than both the state-of-the-art event extraction method and a pipeline approach for event extraction and visualization.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1026"}, {"primary_key": "4048890", "vector": [], "sparse_vector": [], "title": "Attention-Based Bidirectional Long Short-Term Memory Networks for Relation Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Zhenyu Qi", "<PERSON><PERSON>", "Hong<PERSON>", "<PERSON>"], "summary": "Relation classification is an important semantic processing task in the field of natural language processing (NLP). State-ofthe-art systems still rely on lexical resources such as WordNet or NLP systems like dependency parser and named entity recognizers (NER) to get high-level features. Another challenge is that important information can appear at any position in the sentence. To tackle these problems, we propose Attention-Based Bidirectional Long Short-Term Memory Networks(AttBLSTM) to capture the most important semantic information in a sentence. The experimental results on the SemEval-2010 relation classification task show that our method outperforms most of the existing methods, with only word vectors.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-2034"}, {"primary_key": "4048891", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Sentiment Classification with Bilingual Document Representation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-lingual sentiment classiﬁcation aims to adapt the sentiment resource in a resource-rich language to a resource-poor language. In this study, we propose a representation learning approach which simultaneously learns vector representations for the texts in both the source and the target languages. Different from previous research which only gets bilingual word embedding, our Bilingual Document Representation Learning model BiDRL directly learns document representations. Both semantic and sentiment correlations are utilized to map the bilingual texts into the same embedding space. The experiments are based on the multilingual multi-domain Amazon review dataset. We use English as the source language and use Japanese, German and French as the target languages. The experimental results show that BiDRL outperforms the state-of-the-art methods for all the target languages.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1133"}, {"primary_key": "4048892", "vector": [], "sparse_vector": [], "title": "Bi-Transferring Deep Neural Networks for Domain Adaptation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tingting He"], "summary": "Sentiment classification aims to automatically predict sentiment polarity (e.g., positive or negative) of user generated sentiment data (e.g., reviews, blogs).Due to the mismatch among different domains, a sentiment classifier trained in one domain may not work well when directly applied to other domains.Thus, domain adaptation for sentiment classification algorithms are highly desirable to reduce the domain discrepancy and manual labeling costs.To address the above challenge, we propose a novel domain adaptation method, called Bi-Transferring Deep Neural Networks (BTDNNs).The proposed BTDNNs attempts to transfer the source domain examples to the target domain, and also transfer the target domain examples to the source domain.The linear transformation of BTDNNs ensures the feasibility of transferring between domains, and the distribution consistency between the transferred domain and the desirable domain is constrained with a linear data reconstruction manner.As a result, the transferred source domain is supervised and follows similar distribution as the target domain.Therefore, any supervised method can be used on the transferred source domain to train a classifier for sentiment classification in a target domain.We conduct experiments on a benchmark composed of reviews of 4 types of Amazon products.Experimental results show that our proposed approach significantly outperforms the several baseline methods, and achieves an accuracy which is competitive with the state-of-the-art method for domain adaptation.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1031"}, {"primary_key": "4048893", "vector": [], "sparse_vector": [], "title": "A Search-Based Dynamic Reranking Model for Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xinyu Dai", "<PERSON><PERSON><PERSON>"], "summary": "We propose a novel reranking method to extend a deterministic neural dependency parser.Different to conventional k-best reranking, the proposed model integrates search and learning by utilizing a dynamic action revising process, using the reranking model to guide modification for the base outputs and to rerank the candidates.The dynamic reranking model achieves an absolute 1.78% accuracy improvement over the deterministic baseline parser on PTB, which is the highest improvement by neural rerankers in the literature.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1132"}, {"primary_key": "4048894", "vector": [], "sparse_vector": [], "title": "Segment-Level Sequence Modeling using Gated Recursive Semi-Markov Conditional Random Fields.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Zaiqing Nie"], "summary": "Most of the sequence tagging tasks in natural language processing require to recognize segments with certain syntactic role or semantic meaning in a sentence.They are usually tackled with Conditional Random Fields (CRFs), which do indirect word-level modeling over word-level features and thus cannot make full use of segment-level information.Semi-Markov Conditional Random Fields (Semi-CRFs) model segments directly but extracting segment-level features for Semi-CRFs is still a very challenging problem.This paper presents Gated Recursive Semi-CRFs (grSemi-CRFs), which model segments directly and automatically learn segmentlevel features through a gated recursive convolutional neural network.Our experiments on text chunking and named entity recognition (NER) demonstrate that grSemi-CRFs generally outperform other neural models.", "published": "2016-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P16-1134"}]