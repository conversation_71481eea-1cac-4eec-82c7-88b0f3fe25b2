import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // 如果访问根路径，重定向到 /survey
  if (request.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/survey', request.url))
  }

  // 其他路径正常返回
  return NextResponse.next()
}

export const config = {
  // matcher 定义了哪些路由路径会触发中间件
  // 只有匹配这些路径的请求才会经过中间件 middleware函数 处理
  // 其他路径的请求会直接通过而不会被中间件拦截
  matcher: [
    '/',           // 匹配根路径
    // '/chat',           // 匹配 chat
    // '/:id',        // 匹配动态路由，如 /123
    // '/ax',      // 匹配 /arxiv 路径
    // '/ax/',      // 匹配 /arxiv 路径
    // '/arxiv',      // 匹配 /arxiv 路径
    // '/arxiv/',     // 匹配 /arxiv/ 路径（带尾斜杠）
    // '/arxiv/survey', // 匹配 /arxiv/survey 路径
  ],
  
  // 被注释掉的配置示例
  // matcher: [
  //   '/',         // 匹配根路径
  //   '/:id',      // 匹配动态路由
  //   '/api/:path*',    // 匹配所有以 /api 开头的路径
  //   '/local_api/:path*'  // 匹配所有以 /local_api 开头的路径
  // ],
};