[{"primary_key": "4458466", "vector": [], "sparse_vector": [], "title": "A scalable processing-in-memory accelerator for parallel graph processing.", "authors": ["<PERSON><PERSON><PERSON>", "Sungpack Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The explosion of digital data and the ever-growing need for fast data analysis have made in-memory big-data processing in computer systems increasingly important. In particular, large-scale graph processing is gaining attention due to its broad applicability from social science to machine learning. However, scalable hardware design that can efficiently process large graphs in main memory is still an open problem. Ideally, cost-effective and scalable graph processing systems can be realized by building a system whose performance increases proportionally with the sizes of graphs that can be stored in the system, which is extremely challenging in conventional systems due to severe memory bandwidth limitations.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750386"}, {"primary_key": "4458467", "vector": [], "sparse_vector": [], "title": "PIM-enabled instructions: a low-overhead, locality-aware processing-in-memory architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Processing-in-memory (PIM) is rapidly rising as a viable solution for the memory wall crisis, rebounding from its unsuccessful attempts in 1990s due to practicality concerns, which are alleviated with recent advances in 3D stacking technologies. However, it is still challenging to integrate the PIM architectures with existing systems in a seamless manner due to two common characteristics: unconventional programming models for in-memory computation units and lack of ability to utilize large on-chip caches.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750385"}, {"primary_key": "4458468", "vector": [], "sparse_vector": [], "title": "Data reorganization in memory using 3D-stacked DRAM.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we focus on common data reorganization operations such as shuffle, pack/unpack, swap, transpose, and layout transformations. Although these operations simply relocate the data in the memory, they are costly on conventional systems mainly due to inefficient access patterns, limited data reuse and roundtrip data traversal throughout the memory hierarchy. This paper presents a two pronged approach for efficient data reorganization, which combines (i) a proposed DRAM-aware reshape accelerator integrated within 3D-stacked DRAM, and (ii) a mathematical framework that is used to represent and optimize the reorganization operations.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750397"}, {"primary_key": "4458469", "vector": [], "sparse_vector": [], "title": "Coherence protocol for transparent management of scratchpad memories in shared memory manycore architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing number of cores in manycore architectures causes important power and scalability problems in the memory subsystem. One solution is to introduce scratchpad memories alongside the cache hierarchy, forming a hybrid memory system. Scratchpad memories are more power-efficient than caches and they do not generate coherence traffic, but they suffer from poor programmability. A good way to hide the programmability difficulties to the programmer is to give the compiler the responsibility of generating code to manage the scratchpad memories. Unfortunately, compilers do not succeed in generating this code in the presence of random memory accesses with unknown aliasing hazards.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750411"}, {"primary_key": "4458470", "vector": [], "sparse_vector": [], "title": "LaZy superscalar.", "authors": ["Görkem Asilioglu", "Zhaoxiang Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "LaZy Superscalar is a processor architecture which delays the execution of fetched instructions until their results are needed by other instructions. This approach eliminates dead instructions and provides the necessary means to fuse dependent instructions across multiple control dependencies by explicitly tracking control and data dependencies through a matrix based scheduler. We present this novel redesign of scheduling, recovery and commit mechanisms and evaluate the performance of the proposed architecture. Our simulations using Spec 2006 benchmark suite indicate that LaZy Superscalar can achieve significant speed-ups while providing respectable power savings compared to a conventional superscalar processor.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750409"}, {"primary_key": "4458471", "vector": [], "sparse_vector": [], "title": "Flexible auto-refresh: enabling scalable and energy-efficient DRAM refresh reductions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "DRAM cells require periodic refreshing to preserve data. In JEDEC DDRx devices, a refresh operation is performed via an auto-refresh command, which refreshes multiple rows in multiple banks simultaneously. The internal implementation of auto-refresh is completely opaque outside the DRAM --- all the memory controller can do is to instruct the DRAM to refresh itself --- the DRAM handles all else, in particular determining which rows in which banks are to be refreshed. This is in conflict with a large body of research on reducing the refresh overhead, in which the memory controller needs fine-grained control over which regions of the memory are refreshed. For example, prior works exploit the fact that a subset of DRAM rows can be refreshed at a slower rate than other rows due to access rate or retention period variations. However, such row-granularity approaches cannot use the standard auto-refresh command, which refreshes an entire batch of rows at once and does not permit skipping of rows. Consequently, prior schemes are forced to use explicit sequences of activate (ACT) and precharge (PRE) operations to mimic row-level refreshing. The drawback is that, compared to using JEDEC's auto-refresh mechanism, using explicit ACT and PRE commands is inefficient, both in terms of performance and power.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750408"}, {"primary_key": "4458472", "vector": [], "sparse_vector": [], "title": "FASE: finding amplitude-modulated side-channel emanations.", "authors": ["<PERSON>", "Alenka <PERSON>", "<PERSON><PERSON>"], "summary": "While all computation generates electromagnetic (EM) side-channel signals, some of the strongest and farthest-propagating signals are created when an existing strong periodic signal (e.g. a clock signal) becomes stronger or weaker (amplitude-modulated) depending on processor or memory activity. However, modern systems create emanations at thousands of different frequencies, so it is a difficult, error-prone, and time-consuming task to find those few emanations that are AM-modulated by processor/memory activity.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750394"}, {"primary_key": "4458473", "vector": [], "sparse_vector": [], "title": "The load slice core microarchitecture.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Driven by the motivation to expose instruction-level parallelism (ILP), microprocessor cores have evolved from simple, in-order pipelines into complex, superscalar out-of-order designs. By extracting ILP, these processors also enable parallel cache and memory operations as a useful side-effect. Today, however, the growing off-chip memory wall and complex cache hierarchies of many-core processors make cache and memory accesses ever more costly. This increases the importance of extracting memory hierarchy parallelism (MHP), while reducing the net impact of more general, yet complex and power-hungry ILP-extraction techniques. In addition, for multi-core processors operating in power- and energy-constrained environments, energy-efficiency has largely replaced single-thread performance as the primary concern.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750407"}, {"primary_key": "4458474", "vector": [], "sparse_vector": [], "title": "Accelerating asynchronous programs through event sneak peek.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Asynchronous or event-driven programming is now being used to develop a wide range of systems, including mobile and Web 2.0 applications, Internet-of-Things, and even distributed servers. We observe that these programs perform poorly on conventional processor architectures that are heavily optimized for the characteristics of synchronous programs. Execution characteristics of asynchronous programs significantly differ from synchronous programs as they interleave short events from varied tasks in a fine-grained manner.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750373"}, {"primary_key": "4458475", "vector": [], "sparse_vector": [], "title": "Multiple clone row DRAM: a low latency and area optimized DRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinwoong Suh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Several previous works have changed DRAM bank structure to reduce memory access latency and have shown performance improvement. However, changes in the area-optimized DRAM bank can incur large area-overhead. To solve this problem, we propose Multiple Clone Row DRAM (MCR-DRAM), which uses existing DRAM bank structure without any modification.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750402"}, {"primary_key": "4458476", "vector": [], "sparse_vector": [], "title": "BEAR: techniques for mitigating bandwidth bloat in gigascale DRAM caches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Die stacking memory technology can enable gigascale DRAM caches that can operate at 4x-8x higher bandwidth than commodity DRAM. Such caches can improve system performance by servicing data at a faster rate when the requested data is found in the cache, potentially increasing the memory bandwidth of the system by 4x-8x. Unfortunately, a DRAM cache uses the available memory bandwidth not only for data transfer on cache hits, but also for other secondary operations such as cache miss detection, fill on cache miss, and writeback lookup and content update on dirty evictions from the last-level on-chip cache. Ideally, we want the bandwidth consumed for such secondary operations to be negligible, and have almost all the bandwidth be available for transfer of useful data from the DRAM cache to the processor.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750387"}, {"primary_key": "4458477", "vector": [], "sparse_vector": [], "title": "Manycore network interfaces for in-memory rack-scale computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Datacenter operators rely on low-cost, high-density technologies to maximize throughput for data-intensive services with tight tail latencies. In-memory rack-scale computing is emerging as a promising paradigm in scale-out datacenters capitalizing on commodity SoCs, low-latency and high-bandwidth communication fabrics and a remote memory access model to enable aggregation of a rack's memory for critical data-intensive applications such as graph processing or key-value stores. Low latency and high bandwidth not only dictate eliminating communication bottlenecks in the software protocols and off-chip fabrics but also a careful on-chip integration of network interfaces. The latter is a key challenge especially in architectures with RDMA-inspired one-sided operations that aim to achieve low latency and high bandwidth through on-chip Network Interface (NI) support. This paper proposes and evaluates network interface architectures for tiled manycore SoCs for in-memory rack-scale computing. Our results indicate that a careful splitting of NI functionality per chip tile and at the chip's edge along a NOC dimension enables a rack-scale architecture to optimize for both latency and bandwidth. Our best manycore NI architecture achieves latencies within 3% of an idealized hardware NUMA and efficiently uses the full bisection bandwidth of the NOC, without changing the on-chip coherence protocol or the core's microarchitecture.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750415"}, {"primary_key": "4458478", "vector": [], "sparse_vector": [], "title": "SLIP: reducing wire energy in the memory hierarchy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wire energy has become the major contributor to energy in large lower level caches. While wire energy is related to wire latency its costs are exposed differently in the memory hierarchy. We propose Sub-Level Insertion Policy (SLIP), a cache management policy which improves cache energy consumption by increasing the number of accesses from energy efficient locations while simultaneously decreasing intra-level data movement. In SLIP, each cache level is partitioned into several cache sublevels of differing sizes. Then, the recent reuse distance distribution of a line is used to choose an energy-optimized insertion and movement policy for the line. The policy choice is made by a hardware unit that predicts the number of accesses and inter-level movements.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750398"}, {"primary_key": "4458480", "vector": [], "sparse_vector": [], "title": "ShiDianNao: shifting vision processing closer to the sensor.", "authors": ["Zidong Du", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, neural network accelerators have been shown to achieve both high energy efficiency and high performance for a broad application scope within the important category of recognition and mining applications.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750389"}, {"primary_key": "4458483", "vector": [], "sparse_vector": [], "title": "DjiNN and Tonic: DNN as a service and its implications for future warehouse scale computers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As applications such as Apple Siri, Google Now, Microsoft Cortana, and Amazon Echo continue to gain traction, web-service companies are adopting large deep neural networks (DNN) for machine learning challenges such as image processing, speech recognition, natural language processing, among others. A number of open questions arise as to the design of a server platform specialized for DNN and how modern warehouse scale computers (WSCs) should be outfitted to provide DNN as a service for these applications.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749472"}, {"primary_key": "4458484", "vector": [], "sparse_vector": [], "title": "Efficient execution of memory access phases using dataflow specialization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper identifies a new opportunity for improving the efficiency of a processor core: memory access phases of programs. These are dynamic regions of programs where most of the instructions are devoted to memory access or address computation. These occur naturally in programs because of workload properties, or when employing an in-core accelerator, we get induced phases where the code execution on the core is access code. We observe such code requires an OOO core's dataflow and dynamism to run fast and does not execute well on an in-order processor. However, an OOO core consumes much power, effectively increasing energy consumption and reducing the energy efficiency of in-core accelerators.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750390"}, {"primary_key": "4458485", "vector": [], "sparse_vector": [], "title": "Unified address translation for memory-mapped SSDs with FlashMap.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Applications can map data on SSDs into virtual memory to transparently scale beyond DRAM capacity, permitting them to leverage high SSD capacities with few code changes. Obtaining good performance for memory-mapped SSD content, however, is hard because the virtual memory layer, the file system and the flash translation layer (FTL) perform address translations, sanity and permission checks independently from each other. We introduce FlashMap, an SSD interface that is optimized for memory-mapped SSD-files. FlashMap combines all the address translations into page tables that are used to index files and also to store the FTL-level mappings without altering the guarantees of the file system or the FTL. It uses the state in the OS memory manager and the page tables to perform sanity and permission checks respectively. By combining these layers, FlashMap reduces critical-path latency and improves DRAM caching efficiency. We find that this increases performance for applications by up to 3.32x compared to state-of-the-art SSD file-mapping mechanisms. Additionally, latency of SSD accesses reduces by up to 53.2%.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750420"}, {"primary_key": "4458487", "vector": [], "sparse_vector": [], "title": "BlueDBM: an appliance for big data analytics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Complex data queries, because of their need for random accesses, have proven to be slow unless all the data can be accommodated in DRAM. There are many domains, such as genomics, geological data and daily twitter feeds where the datasets of interest are 5TB to 20 TB. For such a dataset, one would need a cluster with 100 servers, each with 128GB to 256GBs of DRAM, to accommodate all the data in DRAM. On the other hand, such datasets could be stored easily in the flash memory of a rack-sized cluster. Flash storage has much better random access performance than hard disks, which makes it desirable for analytics workloads. In this paper we present BlueDBM, a new system architecture which has flash-based storage with in-store processing capability and a low-latency high-throughput inter-controller network. We show that BlueDBM outperforms a flash-based system without these features by a factor of 10 for some important applications. While the performance of a ram-cloud system falls sharply even if only 5%~10% of the references are to the secondary storage, this sharp performance degradation is not an issue in BlueDBM. BlueDBM presents an attractive point in the cost-performance trade-off for Big Data analytics.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750412"}, {"primary_key": "4458488", "vector": [], "sparse_vector": [], "title": "Profiling a warehouse-scale computer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the increasing prevalence of warehouse-scale (WSC) and cloud computing, understanding the interactions of server applications with the underlying microarchitecture becomes ever more important in order to extract maximum performance out of server hardware. To aid such understanding, this paper presents a detailed microarchitectural analysis of live datacenter jobs, measured on more than 20,000 Google machines over a three year period, and comprising thousands of different applications.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750392"}, {"primary_key": "4458489", "vector": [], "sparse_vector": [], "title": "Redundant memory mappings for fast access to large memories.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Page-based virtual memory improves programmer productivity, security, and memory utilization, but incurs performance overheads due to costly page table walks after TLB misses. This overhead can reach 50% for modern workloads that access increasingly vast memory with stagnating TLB sizes.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749471"}, {"primary_key": "4458490", "vector": [], "sparse_vector": [], "title": "Rumba: an online quality management system for approximate computing.", "authors": ["<PERSON><PERSON>", "Babak Zamirai", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Approximate computing can be employed for an emerging class of applications from various domains such as multimedia, machine learning and computer vision. The approximated output of such applications, even though not 100% numerically correct, is often either useful or the difference is unnoticeable to the end user. This opens up a new design dimension to trade off application performance and energy consumption with output correctness. However, a largely unaddressed challenge is quality control: how to ensure the user experience meets a prescribed level of quality. Current approaches either do not monitor output quality or use sampling approaches to check a small subset of the output assuming that it is representative. While these approaches have been shown to produce average errors that are acceptable, they often miss large errors without any means to take corrective actions. To overcome this challenge, we propose <PERSON><PERSON> for online detection and correction of large approximation errors in an approximate accelerator-based computing environment. <PERSON>umba employs continuous lightweight checks in the accelerator to detect large approximation errors and then fixes these errors by exact re-computation on the host processor. Rumba employs computationally inexpensive output error prediction models for efficient detection. Computing patterns amenable for approximation (e.g., map and stencil) are usually data parallel in nature and <PERSON><PERSON> exploits this property for selective correction. Overall, <PERSON><PERSON> is able to achieve 2.1x reduction in output error for an unchecked approximation accelerator while maintaining the accelerator performance gains at the cost of reducing the energy savings from 3.2x to 2.2x for a set of applications from different approximate computing domains.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750371"}, {"primary_key": "4458491", "vector": [], "sparse_vector": [], "title": "Stash: have your scratchpad and cache it too.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sarita V. Adve", "<PERSON><PERSON><PERSON>"], "summary": "Heterogeneous systems employ specialization for energy efficiency. Since data movement is expected to be a dominant consumer of energy, these systems employ specialized memories (e.g., scratchpads and FIFOs) for better efficiency for targeted data. These memory structures, however, tend to exist in local address spaces, incurring significant performance and energy penalties due to inefficient data movement between the global and private spaces. We propose an efficient heterogeneous memory system where specialized memory components are tightly coupled in a unified and coherent address space. This paper applies these ideas to a system with CPUs and GPUs with scratchpads and caches.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750374"}, {"primary_key": "4458492", "vector": [], "sparse_vector": [], "title": "Fusion: design tradeoffs in coherent cache hierarchies for accelerators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Chip designers have shown increasing interest in integrating specialized fixed-function coprocessors into multicore designs to improve energy efficiency. Recent work in academia [11, 37] and industry [16] has sought to enable more fine-grain offloading at the granularity of functions and loops. The sequential program now needs to migrate across the chip utilizing the appropriate accelerator for each program region. As the execution migrates, it has become increasingly challenging to retain the temporal and spatial locality of the original program as well as manage the data sharing.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750421"}, {"primary_key": "4458493", "vector": [], "sparse_vector": [], "title": "CAWA: coordinated warp scheduling and cache prioritization for critical warp acceleration of GPGPU workloads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ubiquity of graphics processing unit (GPU) architectures has made them efficient alternatives to chip-multiprocessors for parallel workloads. GPUs achieve superior performance by making use of massive multi-threading and fast context-switching to hide pipeline stalls and memory access latency. However, recent characterization results have shown that general purpose GPU (GPGPU) applications commonly encounter long stall latencies that cannot be easily hidden with the large number of concurrent threads/warps. This results in varying execution time disparity between different parallel warps, hurting the overall performance of GPUs -- the warp criticality problem.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750418"}, {"primary_key": "4458495", "vector": [], "sparse_vector": [], "title": "A fully associative, tagless DRAM cache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinkyu Jeong", "<PERSON><PERSON>"], "summary": "This paper introduces a tagless cache architecture for large in-package DRAM caches. The conventional die-stacked DRAM cache has both a TLB and a cache tag array, which are responsible for virtual-to-physical and physical-to-cache address translation, respectively. We propose to align the granularity of caching with OS page size and take a unified approach to address translation and cache tag management. To this end, we introduce cache-map TLB (cTLB), which stores virtual-to-cache, instead of virtual-to-physical, address mappings. At a TLB miss, the TLB miss handler allocates the requested block into the cache if it is not cached yet, and updates both the page table and cTLB with the virtual-to-cache address mapping. Assuming the availability of large in-package DRAM caches, this ensures that an access to the memory region within the TLB reach always hits in the cache with low hit latency since a TLB access immediately returns the exact location of the requested block in the cache, hence saving a tag-checking operation. The remaining cache space is used as victim cache for memory pages that are recently evicted from cTLB. By completely eliminating data structures for cache tag management, from either on-die SRAM or in-package DRAM, the proposed DRAM cache achieves best scalability and hit latency, while maintaining high hit rate of a fully associative cache. Our evaluation with 3D Through-Silicon Via (TSV)-based in-package DRAM demonstrates that the proposed cache improves the IPC and energy efficiency by 30.9% and 39.5%, respectively, compared to the baseline with no DRAM cache. These numbers translate to 4.3% and 23.8% improvements over an impractical SRAM-tag cache requiring megabytes of on-die SRAM storage, due to low hit latency and zero energy waste for cache tags.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750383"}, {"primary_key": "4458496", "vector": [], "sparse_vector": [], "title": "Warped-compression: enabling power efficient GPUs through register compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Won Woo Ro", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents Warped-Compression, a warp-level register compression scheme for reducing GPU power consumption. This work is motivated by the observation that the register values of threads within the same warp are similar, namely the arithmetic differences between two successive thread registers is small. Removing data redundancy of register values through register compression reduces the effective register width, thereby enabling power reduction opportunities. GPU register files are huge as they are necessary to keep concurrent execution contexts and to enable fast context switching. As a result register file consumes a large fraction of the total GPU chip power. GPU design trends show that the register file size will continue to increase to enable even more thread level parallelism. To reduce register file data redundancy warped-compression uses low-cost and implementation-efficient base-delta-immediate (BDI) compression scheme, that takes advantage of banked register file organization used in GPUs. Since threads within a warp write values with strong similarity, BDI can quickly compress and decompress by selecting either a single register, or one of the register banks, as the primary base and then computing delta values of all the other registers, or banks. Warped-compression can be used to reduce both dynamic and leakage power. By compressing register values, each warp-level register access activates fewer register banks, which leads to reduction in dynamic power. When fewer banks are used to store the register content, leakage power can be reduced by power gating the unused banks. Evaluation results show that register compression saves 25% of the total register file power consumption.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750417"}, {"primary_key": "4458497", "vector": [], "sparse_vector": [], "title": "Towards sustainable in-situ server systems in the big data era.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Juncheng Gu", "Mingcong Song", "<PERSON><PERSON><PERSON>", "Jingling Yuan", "<PERSON>"], "summary": "Recent years have seen an explosion of data volumes from a myriad of distributed sources such as ubiquitous cameras and various sensors. The challenges of analyzing these geographically dispersed datasets are increasing due to the significant data movement overhead, time-consuming data aggregation, and escalating energy needs. Rather than constantly move a tremendous amount of raw data to remote warehouse-scale computing systems for processing, it would be beneficial to leverage in-situ server systems (InS) to pre-process data, i.e., bringing computation to where the data is located.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750381"}, {"primary_key": "4458498", "vector": [], "sparse_vector": [], "title": "Architecting to achieve a billion requests per second throughput on a single key-value store server platform.", "authors": ["<PERSON><PERSON>", "Hyeontaek Lim", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Seongil O", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed in-memory key-value stores (KVSs), such as memcached, have become a critical data serving layer in modern Internet-oriented datacenter infrastructure. Their performance and efficiency directly affect the QoS of web services and the efficiency of datacenters. Traditionally, these systems have had significant overheads from inefficient network processing, OS kernel involvement, and concurrency control. Two recent research thrusts have focused upon improving key-value performance. Hardware-centric research has started to explore specialized platforms including FPGAs for KVSs; results demonstrated an order of magnitude increase in throughput and energy efficiency over stock memcached. Software-centric research revisited the KVS application to address fundamental software bottlenecks and to exploit the full potential of modern commodity hardware; these efforts too showed orders of magnitude improvement over stock memcached.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750416"}, {"primary_key": "4458499", "vector": [], "sparse_vector": [], "title": "Reducing world switches in virtualized environment with flexible cross-world calls.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>", "Haibing Guan"], "summary": "Modern computers are built with increasingly complex software stack crossing multiple layers (i.e., worlds), where cross-world call has been a necessity for various important purposes like security, reliability, and reduced complexity. Unfortunately, there is currently limited cross-world call support (e.g., syscall, vmcall), and thus other calls need to be emulated by detouring multiple times to the privileged software layer (i.e., OS kernel and hypervisor). This causes not only significant performance degradation, but also unnecessary implementation complexity.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750406"}, {"primary_key": "4458500", "vector": [], "sparse_vector": [], "title": "MiSAR: minimalistic synchronization accelerator with resource overflow management.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While numerous hardware synchronization mechanisms have been proposed, they either no longer function or suffer great performance loss when their hardware resources are exceeded, or they add significant complexity and cost to handle such resource overflows. Additionally, prior hardware synchronization proposals focus on one type (barrier or lock) of synchronization, so several mechanisms are likely to be needed to support real applications, many of which use locks, barriers, and/or condition variables.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750396"}, {"primary_key": "4458501", "vector": [], "sparse_vector": [], "title": "DynaSpAM: dynamic spatial architecture mapping using out of order instruction schedules.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> August"], "summary": "Spatial architectures are more efficient than traditional Out-of-Order (OOO) processors for computationally intensive programs. However, spatial architectures require mapping a program, either statically or dynamically, onto the spatial fabric. Static methods can generate efficient mappings, but they cannot adapt to changing workloads and are not compatible across hardware generations. Current dynamic methods are adaptive and compatible, but do not optimize as well due to their limited use of speculation and small mapping scopes. To overcome the limitations of existing dynamic mapping methods for spatial architectures, while minimizing the inefficiencies inherent in OOO superscalar processors, this paper presents DynaSpAM (Dynamic Spatial Architecture Mapping), a framework that tightly couples a spatial fabric with an OOO pipeline. DynaSpAM coaxes the OOO processor into producing an optimized mapping with a simple modification to the processor's scheduler. The insight behind DynaSpAM is that today's powerful OOO processors do for themselves most of the work necessary to produce a highly optimized mapping for a spatial architecture, including aggressively speculating control and memory dependences, and scheduling instructions using a large window. Evaluation of DynaSpAM shows a geomean speedup of 1.42x for 11 benchmarks from the Rodinia benchmark suite with a geomean 23.9% reduction in energy consumption compared to an 8-issue OOO pipeline.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750414"}, {"primary_key": "4458502", "vector": [], "sparse_vector": [], "title": "HEB: deploying and managing hybrid energy buffers for improving datacenter efficiency and economy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Hongbin Sun", "<PERSON>", "Juncheng Gu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>ning <PERSON>"], "summary": "Today, an increasing number of applications and services are being hosted by large-scale data centers. The massive and irregular load surges challenge data center power infrastructures. As a result, power mismatching between supply and demand has emerged as a crucial issue in modern data centers which are either under-provisioned or powered by intermittent power sources. Recent proposals have employed energy storage devices such as the uninterruptible power supply (UPS) systems to address this issue. However, current approaches lack the capacity of efficiently handling the irregular and unpredictable power mismatches.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750384"}, {"primary_key": "4458504", "vector": [], "sparse_vector": [], "title": "Heracles: improving resource efficiency at scale.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "User-facing, latency-sensitive services, such as websearch, underutilize their computing resources during daily periods of low traffic. Reusing those resources for other tasks is rarely done in production services since the contention for shared resources can cause latency spikes that violate the service-level objectives of latency-sensitive tasks. The resulting under-utilization hurts both the affordability and energy-efficiency of large-scale datacenters. With technology scaling slowing down, it becomes important to address this opportunity.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749475"}, {"primary_key": "4458505", "vector": [], "sparse_vector": [], "title": "SHRINK: reducing the ISA complexity via instruction recycling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microprocessor manufacturers typically keep old instruction sets in modern processors to ensure backward compatibility with legacy software. The introduction of newer extensions to the ISA increases the design complexity of microprocessor front-ends, exacerbates the consumption of precious on-chip resources (e.g., silicon area and energy), and demands more efforts for hardware verification and debugging. We analyzed several x86 applications and operating systems deployed between 1995 and 2012 and observed that many instructions stop being used over time, and more than 500 instructions were never used in these applications. We also investigate the impact of including these unused instructions in the design of the x86 decoders and propose SHRINK, a mechanism to remove old instructions without breaking backward compatibility with legacy code. SHRINK allows us to remove 40% of the instructions from the x86 ISA and improve the critical path, area, and power consumption of the instruction decoder, respectively, by 23%, 48%, and 49%, on average.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750391"}, {"primary_key": "4458507", "vector": [], "sparse_vector": [], "title": "ArMOR: defending against memory consistency model mismatches in heterogeneous architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Architectural heterogeneity is increasing: numerous products and studies have proven the benefits of combining cores and accelerators with varying ISAs into a single system. However, an underappreciated barrier to unlocking the full potential of heterogeneity is the need to specify and to reconcile differences in memory consistency models across layers of the hardware-software stack and among on-chip components.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750378"}, {"primary_key": "4458508", "vector": [], "sparse_vector": [], "title": "Branch vanguard: decomposing branch functionality into prediction and resolution instructions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While control speculation is highly effective for generating good schedules in out-of-order processors, it is less effective for in-order processors because compilers have trouble scheduling in the presence of unbiased branches, even when those branches are highly predictable. In this paper, we demonstrate a novel architectural branch decomposition that separates the prediction and deconvergence point of a branch from its resolution, which enables the compiler to profitably schedule across predictable, but unbiased branches. We show that the hardware support for this branch architecture is a trivial extension of existing systems and describe a simple code transformation for exploiting this architectural support. As architectural changes are required, this technique is most compelling for a dynamic binary translation-based system like Project Denver.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750400"}, {"primary_key": "4458509", "vector": [], "sparse_vector": [], "title": "VIP: virtualizing IP chains on handheld platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Energy-efficient user-interactive and display-oriented applications on handhelds rely heavily on multiple accelerators (termed IP cores) to meet their periodic frame processing needs. Further, these platforms are starting to host multiple applications concurrently on the multiple CPU cores. Unfortunately, today's hardware exposes an interface that forces the host software (Android drivers) to treat each IP core as an isolated device. Consequently, the host CPU has to get involved in the (i) processing of each frame, (ii) scheduling them to ensure timely progress through the IP cores to meet their QoS needs, and (iii) explicitly having to move data from one IP core to the next, with main memory serving as the common staging area.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750382"}, {"primary_key": "4458510", "vector": [], "sparse_vector": [], "title": "Quantitative comparison of hardware transactional memory for Blue Gene/Q, zEnterprise EC12, Intel Core, and POWER8.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Maged <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transactional Memory (TM) is a new programming paradigm for both simple concurrent programming and high concurrent performance. Hardware Transactional Memory (HTM) is hardware support for TM-based programming. It has lower overhead than software transactional memory (STM), which is a software-based implementation of TM. There are now four commercial systems, IBM Blue Gene/Q, IBM zEnterprise EC12, Intel Core, and IBM POWER8, offering HTM. Our work is the first to compare the performance of these four HTM systems. We measured the STAMP benchmarks, the most widely used TM benchmarks. We also evaluated the specific features of each HTM system. Our experimental results show that: (1) there is no single HTM system that is more scalable than the others in all of the benchmarks, (2) there are measurable performance differences among the HTM systems in some benchmarks, and (3) each HTM system has its own implementation characteristics that limit its scalability.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750403"}, {"primary_key": "4458511", "vector": [], "sparse_vector": [], "title": "FaultHound: value-locality-based soft-fault tolerance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Soft error susceptibility is a growing concern with continued CMOS scaling. Previous work explores full- and partial-redundancy schemes in hardware and software for soft-fault tolerance. However, full-redundancy schemes incur high performance and energy overheads whereas partial-redundancy schemes achieve low coverage. An initial study, called Perturbation Based Fault Screening (PBFS), explores exploiting value locality to provide hints of soft faults whenever a value falls outside its neighborhood. PBFS employs bit-mask filters to capture value neighborhoods. However, PBFS achieves low coverage; straightforwardly improving the coverage results in high false-positive rates, and performance and energy overheads. We propose FaultHound, a value-locality-based soft-fault tolerance scheme, which employs five mechanisms to address PBFS's limitations: (1) a scheme to cluster the filters via an inverted organization of the filter tables to reinforce learning and reduce the false-positive rates; (2) a learning scheme for ignoring the delinquent bit positions that raise repeated false alarms, to reduce further the false-positive rate; (3) a light-weight predecessor replay scheme instead of a full rollback to reduce the performance and energy penalty of the remaining false positives; (4) a simple scheme to distinguish rename faults, which require rollback instead of replay for recovery, from false positives to avoid unnecessary rollback penalty; and (5) a detection scheme, which avoids rollback, for the load-store queue which is not covered by our replay. Using simulations, we show that while PBFS achieves either low coverage (30%), or high false-positive rates (8%) with high performance overheads (97%), FaultHound achieves higher coverage (75%) and lower false-positive rates (3%) with lower performance and energy overheads (10% and 25%).", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750372"}, {"primary_key": "4458513", "vector": [], "sparse_vector": [], "title": "Exploring the potential of heterogeneous von neumann/dataflow execution models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "General purpose processors (GPPs), from small inorder designs to many-issue out-of-order, incur large power overheads which must be addressed for future technology generations. Major sources of overhead include structures which dynamically extract the data-dependence graph or maintain precise state. Considering irregular workloads, current specialization approaches either heavily curtail performance, or provide simply too little benefit. Interestingly, well known explicit-dataflow architectures eliminate these overheads by directly executing the data-dependence graph and eschewing instruction-precise recoverability. However, even after decades of research, dataflow architectures have yet to come into prominence as a solution. We attribute this to a lack of effective control speculation and the latency overhead of explicit communication, which is crippling for certain codes.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750380"}, {"primary_key": "4458515", "vector": [], "sparse_vector": [], "title": "COP: to compress and protect main memory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Protecting main memories from soft errors typically requires special dual-inline memory modules (DIMMs) which incorporate at least one extra chip per rank to store error-correcting codes (ECC). This increases the cost of the DIMM as well as its power consumption. To avoid these costs, some proposals have suggested protecting non-ECC DIMMs by allocating a portion of memory space to store ECC metadata. However, such proposals can significantly shrink the available memory space while degrading performance due to extra memory accesses. In this work, we propose a technique called COP which uses block-level compression to make room for ECC check bits in DRAM. Because a compressed block with check bits is the same size as an uncompressed block, no extra memory accesses are required and the memory space is not reduced. Unlike other approaches that require explicit compression-tracking metadata, COP employs a novel mechanism that relies on ECC to detect compressed data. Our results show that COP can reduce the DRAM soft error rate by 93% with no storage overhead and negligible impact on performance. We also propose a technique using COP to protect both compressible and incompressible data with minimal storage and performance overheads.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750377"}, {"primary_key": "4458516", "vector": [], "sparse_vector": [], "title": "MBus: an ultra-low power interconnect bus for next generation nanopower systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>yoong Foo", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As we show in this paper, I/O has become the limiting factor in scaling down size and power toward the goal of invisible computing. Achieving this goal will require composing optimized and specialized-yet reusable-components with an interconnect that permits tiny, ultra-low power systems. In contrast to today's interconnects which are limited by power-hungry pull-ups or high-overhead chip-select lines, our approach provides a superset of common bus features but at lower power, with fixed area and pin count, using fully synthesizable logic, and with surprisingly low protocol overhead. We present MBus, a new 4-pin, 22.6 pJ/bit/chip chip-to-chip interconnect made of two \"shoot-through\" rings. MBus facilitates ultra-low power system operation by implementing automatic power-gating of each chip in the system, easing the integration of active, inactive, and activating circuits on a single die. In addition, we introduce a new bus primitive: power oblivious communication, which guarantees message reception regardless of the recipient's power state when a message is sent. This disentangles power management from communication, greatly simplifying the creation of viable, modular, and heterogeneous systems that operate on the order of nanowatts. To evaluate the viability, power, performance, overhead, and scalability of our design, we build both hardware and software implementations of MBus and show its seamless operation across two FPGAs and twelve custom chips from three different semiconductor processes. A three-chip, 2.2 mm3 MBus system draws 8 nW of total system standby power and uses only 22.6 pJ/bit/chip for communication. This is the lowest power for any system bus with MBus's feature set.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750376"}, {"primary_key": "4458517", "vector": [], "sparse_vector": [], "title": "Harmonia: balancing compute and memory power in high-performance GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we address the problem of efficiently managing the relative power demands of a high-performance GPU and its memory subsystem. We develop a management approach that dynamically tunes the hardware operating configurations to maintain balance between the power dissipated in compute versus memory access across GPGPU application phases. Our goal is to reduce power with minimal performance degradation.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750404"}, {"primary_key": "4458518", "vector": [], "sparse_vector": [], "title": "Semantic locality and context-based prefetching using reinforcement learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most modern memory prefetchers rely on spatio-temporal locality to predict the memory addresses likely to be accessed by a program in the near future. Emerging workloads, however, make increasing use of irregular data structures, and thus exhibit a lower degree of spatial locality. This makes them less amenable to spatio-temporal prefetchers.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749473"}, {"primary_key": "4458519", "vector": [], "sparse_vector": [], "title": "Cost-effective speculative scheduling in high performance processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To maximize performance, out-of-order execution processors sometimes issue instructions without having the guarantee that operands will be available in time; e.g. loads are typically assumed to hit in the L1 cache and dependent instructions are issued accordingly. This form of speculation -- that we refer to as speculative scheduling -- has been used for two decades in real processors, but has received little attention from the research community.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749470"}, {"primary_key": "4458520", "vector": [], "sparse_vector": [], "title": "Probable cause: the deanonymizing effects of approximate DRAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Approximate computing research seeks to trade-off the accuracy of computation for increases in performance or reductions in power consumption. The observation driving approximate computing is that many applications tolerate small amounts of error which allows for an opportunistic relaxation of guard bands (e.g., clock rate and voltage). Besides affecting performance and power, reducing guard bands exposes analog properties of traditionally digital components. For DRAM, one analog property exposed by approximation is the variability of memory cell decay times.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750419"}, {"primary_key": "4458521", "vector": [], "sparse_vector": [], "title": "A variable warp size architecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>;<PERSON>", "<PERSON>"], "summary": "This paper studies the effect of warp sizing and scheduling on performance and efficiency in GPUs. We propose Variable Warp Sizing (VWS) which improves the performance of divergent applications by using a small base warp size in the presence of control flow and memory divergence. When appropriate, our proposed technique groups sets of these smaller warps together by ganging their execution in the warp scheduler, improving performance and energy efficiency for regular applications. Warp ganging is necessary to prevent performance degradation on regular workloads due to memory convergence slip, which results from the inability of smaller warps to exploit the same intra-warp memory locality as larger warps. This paper explores the effect of warp sizing on control flow divergence, memory divergence, and locality. For an estimated 5% area cost, our ganged scheduling microarchitecture results in a simulated 35% performance improvement on divergent workloads by allowing smaller groups of threads to proceed independently, and eliminates the performance degradation due to memory convergence slip that is observed when convergent applications are executed with smaller warp sizes.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750410"}, {"primary_key": "4458522", "vector": [], "sparse_vector": [], "title": "Callback: efficient synchronization without invalidation with a directory just for spin-waiting.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Cache coherence protocols based on self-invalidation allow a simpler design compared to traditional invalidation-based protocols, by relying on data-race-free (DRF) semantics and applying self-invalidation on racy synchronization points exposed to the hardware. Their simplicity lies in the absence of invalidation traffic, which eliminates the need to track readers in a directory, and reduces the number of transient protocol states. With the addition of self-downgrade these protocols can become effectively directory-free. While this works well for race-free data, unfortunately, lack of explicit invalidations compromises the effectiveness of any synchronization that relies on races. This includes any form of spin waiting, which is employed for signaling, locking, and barrier primitives.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750405"}, {"primary_key": "4458523", "vector": [], "sparse_vector": [], "title": "Clean: a race detector with cleaner semantics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data races make parallel programs hard to understand. Precise race detection that stops an execution on first occurrence of a race addresses this problem, but it comes with significant overhead. In this work, we exploit the insight that precisely detecting only write-after-write (WAW) and read-after-write (RAW) races suffices to provide cleaner semantics for racy programs. We demonstrate that stopping an execution only when these races occur ensures that synchronization-free-regions appear to be executed in isolation and that their writes appear atomic. Additionally, the undetected racy executions can be given certain deterministic guarantees with efficient mechanisms.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750395"}, {"primary_key": "4458524", "vector": [], "sparse_vector": [], "title": "Page overlays: an enhanced virtual memory framework to enable fine-grained memory management.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Trishul <PERSON>"], "summary": "Many recent works propose mechanisms demonstrating the potential advantages of managing memory at a fine (e.g., cache line) granularity---e.g., fine-grained deduplication and fine-grained memory protection. Unfortunately, existing virtual memory systems track memory at a larger granularity (e.g., 4 KB pages), inhibiting efficient implementation of such techniques. Simply reducing the page size results in an unacceptable increase in page table overhead and TLB pressure.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750379"}, {"primary_key": "4458525", "vector": [], "sparse_vector": [], "title": "Thermal time shifting: leveraging phase change materials to reduce cooling costs in warehouse-scale computers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Datacenters, or warehouse scale computers, are rapidly increasing in size and power consumption. However, this growth comes at the cost of an increasing thermal load that must be removed to prevent overheating and server failure. In this paper, we propose to use phase changing materials (PCM) to shape the thermal load of a datacenter, absorbing and releasing heat when it is advantageous to do so. We present and validate a methodology to study the impact of PCM on a datacenter, and evaluate two important opportunities for cost savings. We find that in a datacenter with full cooling system subscription, PCM can reduce the necessary cooling system size by up to 12% without impacting peak throughput, or increase the number of servers by up to 14.6% without increasing the cooling load. In a thermally constrained setting, PCM can increase peak throughput up to 69% while delaying the onset of thermal limits by over 3 hours.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2749474"}, {"primary_key": "4458526", "vector": [], "sparse_vector": [], "title": "Flexible software profiling of GPU architectures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>;<PERSON>", "<PERSON>"], "summary": "To aid application characterization and architecture design space exploration, researchers and engineers have developed a wide range of tools for CPUs, including simulators, profilers, and binary instrumentation tools. With the advent of GPU computing, GPU manufacturers have developed similar tools leveraging hardware profiling and debugging hooks. To date, these tools are largely limited by the fixed menu of options provided by the tool developer and do not offer the user the flexibility to observe or act on events not in the menu. This paper presents SASSI (NVIDIA assembly code \"SASS\" Instrumentor), a low-level assembly-language instrumentation tool for GPUs. Like CPU binary instrumentation tools, SASSI allows a user to specify instructions at which to inject user-provided instrumentation code. These facilities allow strategic placement of counters and code into GPU assembly code to collect user-directed, fine-grained statistics at hardware speeds. SASSI instrumentation is inherently parallel, leveraging the concurrency of the underlying hardware. In addition to the details of SASSI, this paper provides four case studies that show how SASSI can be used to characterize applications and explore the architecture design space along the dimensions of instruction control flow, memory systems, value similarity, and resilience.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750375"}, {"primary_key": "4458527", "vector": [], "sparse_vector": [], "title": "A case for core-assisted bottleneck acceleration in GPUs: enabling flexible data compression with assist warps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern Graphics Processing Units (GPUs) are well provisioned to support the concurrent execution of thousands of threads. Unfortunately, different bottlenecks during execution and heterogeneous application requirements create imbalances in utilization of resources in the cores. For example, when a GPU is bottlenecked by the available off-chip memory bandwidth, its computational resources are often overwhelmingly idle, waiting for data from memory to arrive.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750399"}, {"primary_key": "4458528", "vector": [], "sparse_vector": [], "title": "Dynamic thread block launch: a lightweight execution mechanism to support irregular applications on GPUs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GPUs have been proven effective for structured applications that map well to the rigid 1D-3D grid of threads in modern bulk synchronous parallel (BSP) programming languages. However, less success has been encountered in mapping data intensive irregular applications such as graph analytics, relational databases, and machine learning. Recently introduced nested device-side kernel launching functionality in the GPU is a step in the right direction, but still falls short of being able to effectively harness the GPUs performance potential.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750393"}, {"primary_key": "4458529", "vector": [], "sparse_vector": [], "title": "Computer performance microscopy with <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developers and architects spend a lot of time trying to understand and eliminate performance problems. Unfortunately, the root causes of many problems occur at a fine granularity that existing continuous profiling and direct measurement approaches cannot observe. This paper presents the design and implementation of <PERSON><PERSON>, a continuous profiler that samples at resolutions as fine as 15 cycles; three to five orders of magnitude finer than current continuous profilers. <PERSON><PERSON>'s fine-grain measurements reveal new behaviors, such as variations in instructions per cycle (IPC) within the execution of a single function. A Shim observer thread executes and samples autonomously on unutilized hardware. To sample, it reads hardware performance counters and memory locations that store software state. Shim improves its accuracy by automatically detecting and discarding samples affected by measurement skew. We measure <PERSON><PERSON>'s observer effects and show how to analyze them. When on a separate core, <PERSON><PERSON> can continuously observe one software signal with a 2% overhead at a ~1200 cycle resolution. At an overhead of 61%, <PERSON><PERSON> samples one software signal on the same core with SMT at a ~15 cycle resolution. Modest hardware changes could significantly reduce overheads and add greater analytical capability to Shi<PERSON>. We vary prefetching and DVFS policies in case studies that show the diagnostic power of fine-grain IPC and memory bandwidth results. By repurposing existing hardware, we deliver a practical tool for fine-grain performance microscopy for developers and architects.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750401"}, {"primary_key": "4458530", "vector": [], "sparse_vector": [], "title": "PrORAM: dynamic prefetcher for oblivious RAM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM) is an established technique to hide the access pattern to an untrusted storage system. With ORAM, a curious adversary cannot tell what address the user is accessing when observing the bits moving between the user and the storage system. All existing ORAM schemes achieve obliviousness by adding redundancy to the storage system, i.e., each access is turned into multiple random accesses. Such redundancy incurs a large performance overhead.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750413"}, {"primary_key": "4458531", "vector": [], "sparse_vector": [], "title": "CloudMonatt: an architecture for security health monitoring and attestation of virtual machines in cloud computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cloud customers need guarantees regarding the security of their virtual machines (VMs), operating within an Infrastructure as a Service (IaaS) cloud system. This is complicated by the customer not knowing where his VM is executing, and on the semantic gap between what the customer wants to know versus what can be measured in the cloud. We present an architecture for monitoring a VM's security health, with the ability to attest this to the customer in an unforgeable manner. We show a concrete implementation of property-based attestation and a full prototype based on the OpenStack open source cloud software.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750422"}, {"primary_key": "4458532", "vector": [], "sparse_vector": [], "title": "Hi-fi playback: tolerating position errors in shift operations of racetrack memory.", "authors": ["<PERSON>", "Guangyu Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhao", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Racetrack memory is an emerging non-volatile memory based on spintronic domain wall technology. It can achieve ultra-high storage density. Also, its read/write speed is comparable to that of SRAM. Due to the tape-like structure of its storage cell, a \"shift\" operation is introduced to access racetrack memory. Thus, prior research mainly focused on minimizing shift latency/energy of racetrack memory while leveraging its ultra-high storage density. Yet the reliability issue of a shift operation, however, is not well addressed. In fact, racetrack memory suffers from unsuccessful shift due to domain misalignment. Such a problem is called \"position error\" in this work. It can significantly reduce mean-time-to-failure (MTTF) of racetrack memory to an intolerable level. Even worse, conventional error correction codes (ECCs), which are designed for \"bit errors\", cannot protect racetrack memory from the position errors.", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469.2750388"}, {"primary_key": "4520219", "vector": [], "sparse_vector": [], "title": "Proceedings of the 42nd Annual International Symposium on Computer Architecture, Portland, OR, USA, June 13-17, 2015", "authors": ["<PERSON>", "<PERSON>"], "summary": "Welcome to the 42nd International Symposium on Computer Architecture (ISCA), in Portland, Oregon, June 13-17, 2015, at the Oregon Convention Center. ISCA has a long history of leadership as the top conference in the field of computer architecture. ISCA's success is because of all of you, our participants, organizers, and supporters. Thank you for coming, participating, and supporting this conference. As in previous ISCA conferences, expect to participate in technical presentations, workshops, tutorials, and networking opportunities of the highest caliber with colleagues from all over the world!", "published": "2015-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/2749469"}]