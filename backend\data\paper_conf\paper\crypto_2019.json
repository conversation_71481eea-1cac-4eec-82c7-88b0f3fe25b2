[{"primary_key": "2986264", "vector": [], "sparse_vector": [], "title": "Explicit Rate-1 Non-malleable Codes for Local Tampering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper constructs high-rate non-malleable codes in the information-theoretic plain model against tampering functions with bounded locality. We consider\\(\\delta \\)-local tampering functions; namely, each output bit of the tampering function is a function of (at most)\\(\\delta \\)input bits. This work presents the first explicit and efficient rate-1 non-malleable code for\\(\\delta \\)-local tampering functions, where\\(\\delta =\\xi \\lg n\\)and\\(\\xi <1\\)is any positive constant. As a corollary, we construct the first explicit rate-1 non-malleable code against NC\\(^0\\)tampering functions. Before our work, no explicit construction for a constant-rate non-malleable code was known even for the simplest 1-local tampering functions. <PERSON> et al. (EUROCRYPT–2016), and <PERSON><PERSON>pad<PERSON><PERSON> and <PERSON> (STOC–2017) provided the first explicit non-malleable codes against\\(\\delta \\)-local tampering functions. However, these constructions are rate-0 even when the tampering functions have 1-locality. In the CRS model, <PERSON> et al. (EUROCRYPT–2014) constructed efficient rate-1 non-malleable codes for\\(\\delta = O(\\log n)\\)local tampering functions. Our main result is a general compiler that bootstraps a rate-0 non-malleable code against leaky input and output local tampering functions to construct a rate-1 non-malleable code against\\(\\xi \\lg n\\)-local tampering functions, for any positive constant\\(\\xi < 1\\). Our explicit construction instantiates this compiler using an appropriate encoding by <PERSON> et al. (EUROCRYPT–2016).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_16"}, {"primary_key": "2986265", "vector": [], "sparse_vector": [], "title": "Unifying Computational Entropies via Kullback-Leibler Divergence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introducehardness in relative entropy, a new notion of hardness for search problems which on the one hand is satisfied by all one-way functions and on the other hand implies bothnext-block pseudoentropyandinaccessible entropy, two forms of computational entropy used in recent constructions of pseudorandom generators and statistically hiding commitment schemes, respectively. Thus, hardness in relative entropy unifies the latter two notions of computational entropy and sheds light on the apparent “duality” between them. Additionally, it yields a more modular and illuminating proof that one-way functions imply next-block inaccessible entropy, similar in structure to the proof that one-way functions imply next-block pseudoentropy (<PERSON><PERSON><PERSON> and <PERSON>, STOC ‘12).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_28"}, {"primary_key": "2986266", "vector": [], "sparse_vector": [], "title": "Stronger Leakage-Resilient and Non-Malleable Secret Sharing Schemes for General Access Structures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we present a collection of compilers that take secret sharing schemes for an arbitrary access structure as input and produce either leakage-resilient or non-malleable secret sharing schemes for the same access structure. A leakage-resilient secret sharing scheme hides the secret from an adversary, who has access to an unqualified set of shares, even if the adversary additionally obtains some size-bounded leakage fromallother secret shares. A non-malleable secret sharing scheme guarantees that a secret that is reconstructed from a set of tampered shares is either equal to the original secret or completely unrelated. To the best of our knowledge we present the first generic compiler for leakage-resilient secret sharing for general access structures. In the case of non-malleable secret sharing, we strengthen previous definitions, provide separations between them, and construct a non-malleable secret sharing scheme for general access structures that fulfills the strongest definition with respect to independent share tampering functions. More precisely, our scheme is secure againstconcurrent tampering: The adversary is allowed to (non-adaptively) tamper the shares multiple times, and in each tampering attempt can freely choose the qualified set of shares to be used by the reconstruction algorithm to reconstruct the tampered secret. This is a strong analogue of the multiple-tampering setting for split-state non-malleable codes and extractors. We show how to use leakage-resilient and non-malleable secret sharing schemes to construct leakage-resilient and non-malleable threshold signatures. Classical threshold signatures allow to distribute the secret key of a signature scheme among a set of parties, such that certain qualified subsets can sign messages. We construct threshold signature schemes that remain secure even if an adversary leaks from or tampers with all secret shares.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_18"}, {"primary_key": "2986267", "vector": [], "sparse_vector": [], "title": "Attribute Based Encryption (and more) for Nondeterministic Finite Automata from LWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Constructing Attribute Based Encryption (ABE) [56] for uniform models of computation from standard assumptions, is an important problem, about which very little is known. Theonlyknown ABE schemes in this setting that (i) avoid reliance on multilinear maps or indistinguishability obfuscation, (ii) supportunbounded length inputsand (iii) permitunbounded key requeststo the adversary in the security game, are by <PERSON> fromCrypto, 2012[57] and its variants. <PERSON> provided the first ABE for Deterministic Finite Automata (DFA) satisfying the above properties, from a parametrized or “q-type” assumption over bilinear maps. Generalizing this construction to Nondeterministic Finite Automata (NFA) was left as an explicit open problem in the same work, and has seen no progress to date. Constructions from other assumptions such as more standard pairing based assumptions, or lattice based assumptions has also proved elusive. In this work, we construct the first symmetric key attribute based encryption scheme for nondeterministic finite automata (NFA) from the learning with errors (LWE) assumption. Our scheme supports unbounded length inputs as well as unbounded length machines. In more detail, secret keys in our construction are associated with an NFAMofunboundedlength, ciphertexts are associated with a tuple\\((\\mathbf {x}, m)\\)where\\(\\mathbf {x}\\)is a public attribute ofunboundedlength andmis a secret message bit, and decryption recoversmif and only if\\(M(\\mathbf {x})=1\\). Further, we leverage our ABE to achieve (restricted notions of) attribute hiding analogous to the circuit setting, obtaining the firstpredicate encryptionand bounded keyfunctional encryptionschemes for NFA from LWE. We achieve machine hiding in the single/bounded key setting to obtain the firstreusable garbled NFAfrom standard assumptions. In terms of lower bounds, we show that secret keyfunctional encryptioneven for DFAs, with security against unbounded key requests implies indistinguishability obfuscation (\\(\\mathsf {iO}\\)) for circuits; this suggests a barrier in achieving full fledged functional encryption for NFA.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_26"}, {"primary_key": "2986268", "vector": [], "sparse_vector": [], "title": "Symmetric Primitives with Structured Secrets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Securely managing encrypted data on an untrusted party is a challenging problem that has motivated the study of a wide variety of cryptographic primitives. A special class of such primitives allows an untrusted party to transform a ciphertext encrypted under one key to a ciphertext under another key, using some auxiliary information that does not leak the underlying data. Prominent examples of such primitives in the symmetric setting are key-homomorphic (weak) PRFs, updatable encryption, and proxy re-encryption. Although these primitives differ significantly in terms of their constructions and security requirements, they share two important properties: (a) they havesecrets with structure or extra functionality, and (b) all known constructions of these primitives satisfying reasonably strong definitions of security are based onconcretepublic-key assumptions, e.g., DDH and LWE. This raises the question of whether these objects inherently belong to the world of public-key primitives, or they can potentially be built from simple symmetric-key objects such as pseudorandom functions. In this work, we show that the latter possibility is unlikely. More specifically, we show that: Any (bounded) key-homomorphicweakPRF with an abelian output group implies a (bounded)input-homomorphicweak PRF, which has recently been shown to imply not only public-key encryption  but also a variety of primitives such as PIR, lossy TDFs, and even IBE. Any ciphertext-independent updatable encryption scheme that is forward and post-compromise secure implies PKE. Moreover, any symmetric-key proxy re-encryption scheme with reasonably strong security guarantees implies a forward and post-compromise secure ciphertext-independent updatable encryption, and hence PKE. In addition, we show that unbounded (or exact) key-homomorphic weak PRFs over abelian groups areimpossiblein the quantum world. In other words, over abelian groups, bounded key-homomorphism is the best that we can hope for in terms of post-quantum security. Our attack also works over other structured primitives with abelian groups and exact homomorphisms, including homomorphic one-way functions and input-homomorphic weak PRFs.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_23"}, {"primary_key": "2986269", "vector": [], "sparse_vector": [], "title": "Quantum Security Proofs Using Semi-classical Oracles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an improved version of the one-way to hiding (O2H) Theorem by Unruh, J ACM 2015. Our new O2H Theorem gives higher flexibility (arbitrary joint distributions of oracles and inputs, multiple reprogrammed points) as well as tighter bounds (removing square-root factors, taking parallelism into account). The improved O2H Theorem makes use of a new variant of quantum oracles, semi-classical oracles, where queries are partially measured. The new O2H Theorem allows us to get better security bounds in several public-key encryption schemes.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_10"}, {"primary_key": "2986270", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation Without Multilinear Maps: New Paradigms via Low Degree Weak Pseudorandomness and Security Amplification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The existence of secure indistinguishability obfuscators (\\(i\\mathcal {O}\\)) has far-reaching implications, significantly expanding the scope of problems amenable to cryptographic study. All known approaches to constructing\\(i\\mathcal {O}\\)rely ond-linear maps. While securebilinear mapsare well established in cryptographic literature, the security of candidates for\\(d>2\\)is poorly understood. We propose a new approach to constructing\\(i\\mathcal {O}\\)for general circuits. Unlike all previously known realizations of\\(i\\mathcal {O}\\), we avoid the use ofd-linear maps of degree\\(d \\ge 3\\). At the heart of our approach is the assumption that a newweakpseudorandom object exists. We consider two related variants of these objects, which we callperturbation resilient generator(\\(\\varDelta \\)RG) andpseudo flawed-smudging generator(\\(\\mathrm {PFG}\\)), respectively. At a high level, both objects are polynomially expanding functions whose outputs partially hide (or smudge) small noise vectors when added to them. We further require that they are computable by a family of degree-3 polynomials over\\(\\mathbb {Z}\\). We show how they can be used to construct functional encryption schemes with weak security guarantees. Finally, we use novel amplification techniques to obtain full security. As a result, we obtain\\(i\\mathcal {O}\\)for general circuits assuming: Subexponentially secure LWE Bilinear Maps \\(\\mathrm {poly}(\\lambda )\\)-secure 3-block-local PRGs \\(\\varDelta \\)RGs or\\(\\mathrm {PFG}\\)s", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_10"}, {"primary_key": "2986271", "vector": [], "sparse_vector": [], "title": "Match Me if You Can: Matchmaking Encryption and Its Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new form of encryption that we namematchmaking encryption(ME). Using ME, sender <PERSON> and receiver <PERSON> (each with its own attributes) can both specify policies the other party must satisfy in order for the message to be revealed. The main security guarantee is that of privacy-preserving policy matching: During decryption nothing is leaked beyond the fact that a match occurred/did not occur. ME opens up new ways of secretly communicating, and enables several new applications where both participants can specify fine-grained access policies to encrypted data. For instance, in social matchmaking, <PERSON> can encrypt a file containing his/her personal details and specify a policy so that the file can be decrypted only by his/her ideal partner. On the other end, a receiver <PERSON> will be able to decrypt the file only if <PERSON> corresponds to his/her ideal partner defined through a policy. On the theoretical side, we define security for ME, as well as provide generic frameworks for constructing ME from functional encryption. These constructions need to face the technical challenge of simultaneously checking the policies chosen by S and R, to avoid any leakage. On the practical side, we construct an efficient identity-based scheme for equality policies, with provable security in the random oracle model under the standard BDH assumption. We implement and evaluate our scheme and provide experimental evidence that our construction is practical. We also apply identity-based ME to a concrete use case, in particular for creating an anonymous bulletin board over a Tor network.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_24"}, {"primary_key": "2986272", "vector": [], "sparse_vector": [], "title": "Non-malleable Codes for Decision Trees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct efficient, unconditional non-malleable codes that are secure against tampering functions computed by decision trees of depth\\(d= n^{1/4-o(1)}\\). In particular, each bit of the tampered codeword is set arbitrarily after adaptively reading up todarbitrary locations within the original codeword. Prior to this work, no efficient unconditional non-malleable codes were known for decision trees beyond depth\\(O(\\log ^2 n)\\). Our result also yields efficient, unconditional non-malleable codes that are\\(\\exp (-n^{\\varOmega (1)})\\)-secure against constant-depth circuits of\\(\\exp (n^{\\varOmega (1)})\\)-size. Prior work of Chattopadhyay and Li (STOC 2017) and <PERSON> et al. (FOCS 2018) only provide protection against\\(\\exp (O(\\log ^2n))\\)-size circuits with\\(\\exp (-O(\\log ^2n))\\)-security. We achieve our result through simple non-malleable reductions of decision tree tampering to split-state tampering. As an intermediary, we give a simple and generic reduction of leakage-resilient split-state tampering to split-state tampering with improved parameters. Prior work of <PERSON><PERSON><PERSON><PERSON> et al. (TCC 2015) only provides a reduction to split-state non-malleable codes with decoders that exhibit particular properties.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_15"}, {"primary_key": "2986273", "vector": [], "sparse_vector": [], "title": "The Privacy Blanket of the Shuffle Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work studies differential privacy in the context of the recently proposedshuffle model. Unlike in the local model, where the server collecting privatized data from users can track back an input to a specific user, in the shuffle model users submit their privatized inputs to a server anonymously. This setup yields a trust model which sits in between the classical curator and local models for differential privacy. The shuffle model is the core idea in the Encode, Shuffle, Analyze (ESA) model introduced by <PERSON><PERSON><PERSON> et al. (SOPS 2017). Recent work by <PERSON><PERSON> et al. (EUROCRYPT 2019) analyzes the differential privacy properties of the shuffle model and shows that in some cases shuffled protocols provide strictly better accuracy than local protocols. Additionally, <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2019) provide a privacy amplification bound quantifying the level of curator differential privacy achieved by the shuffle model in terms of the local differential privacy of the randomizer used by each user. In this context, we make three contributions. First, we provide an optimal single message protocol for summation of real numbers in the shuffle model. Our protocol is very simple and has better accuracy and communication than the protocols for this same problem proposed by <PERSON><PERSON> et al. Optimality of this protocol follows from our second contribution, a new lower bound for the accuracy of private protocols for summation of real numbers in the shuffle model. The third contribution is a new amplification bound for analyzing the privacy of protocols in the shuffle model in terms of the privacy provided by the corresponding local randomizer. Our amplification bound generalizes the results by <PERSON><PERSON><PERSON><PERSON> et al. to a wider range of parameters, and provides a whole family of methods to analyze privacy amplification in the shuffle model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_22"}, {"primary_key": "2986274", "vector": [], "sparse_vector": [], "title": "The Distinction Between Fixed and Random Generators in Group-Based Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "There is surprisingly little consensus on the precise role of the generatorgin group-based assumptions such as DDH. Some works considergto be a fixed part of the group description, while others take it to be random. We study this subtle distinction from a number of angles. In the generic group model, we demonstrate the plausibility of groups in which random-generator DDH (resp. CDH) is hard but fixed-generator DDH (resp. CDH) is easy. We observe that such groups have interesting cryptographic applications. We find that seemingly tight generic lower bounds for the Discrete-Log and CDH problems with preprocessing (<PERSON>rrigan<PERSON> and <PERSON>, Eurocrypt 2018) are not tight in the sub-constant success probability regime if the generator is random. We resolve this by proving tight lower bounds for the random generator variants; our results formalize the intuition that using a random generator will reduce the effectiveness of preprocessing attacks. We observe that DDH-like assumptions in which exponents are drawn from low-entropy distributions are particularly sensitive to the fixed- vs. random-generator distinction. Most notably, we discover that the Strong Power DDH assumption of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, Eurocrypt 2018) used for non-malleable point obfuscation is in factfalseprecisely because it requires a fixed generator. In response, we formulate an alternative fixed-generator assumption that suffices for a new construction of non-malleable point obfuscation, and we prove the assumption holds in the generic group model. We also give a generic group proof for the security of fixed-generator, low-entropy DDH (<PERSON>, <PERSON>pto 1997).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_27"}, {"primary_key": "2986275", "vector": [], "sparse_vector": [], "title": "Nonces Are Noticed: AEAD Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We draw attention to a gap between theory and usage of nonce-based symmetric encryption, under which the way the former treats nonces can result in violation of privacy in the latter. We bridge the gap with a new treatment of nonce-based symmetric encryption that modifies the syntax (decryption no longer takes a nonce), upgrades the security goal (asking that not just messages, but also nonces, be hidden) and gives simple, efficient schemes conforming to the new definitions. We investigate both basic security (holding when nonces are not reused) and advanced security (misuse resistance, providing best-possible guarantees when nonces are reused).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_9"}, {"primary_key": "2986276", "vector": [], "sparse_vector": [], "title": "Scalable Zero Knowledge with No Trusted Setup.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the approaches to constructing zero knowledge (ZK) arguments relies on “PCP techniques” that date back to influential works from the early 1990’s [<PERSON><PERSON> et al., <PERSON><PERSON><PERSON> et al. 1991-2]. These techniques require only minimal cryptographic assumptions, namely, the existence of a family of collision-resistant hash functions [<PERSON><PERSON>, STOC 1992], and achieve two remarkable properties: (i) all messages generated by the verifier are public random coins, and (ii) total verification time is merely poly-logarithmic in the time needed to naïvely execute the computation being verified [<PERSON><PERSON> et al., STOC 1991]. Those early constructions were never realized in code, mostly because proving time was too large. To address this, the model of interactive oracle proofs (IOPs), which generalizes the PCP model, was recently suggested. Proving time for ZK-IOPs was reduced toquasi-linear, even for problems that require nondeterministic exponential time to decide [<PERSON><PERSON> et al., TCC 2016, ICALP 2017]. Despite these recent advances it was still not clear whether ZK-IOP systems can lead to concretely efficient succinct argument systems. Our main claim is that this is indeed the case. We present a new construction of an IOP of knowledge (which we call a zk-STIK) that improves, asymptotically, on the state of art: for log-space computations of lengthTit is the first to\\(O(T \\log T)\\)arithmetic prover complexity and\\(O(\\log T)\\)verifier arithmetic complexity. Prior IOPs had additional\\(\\mathsf{poly} \\log T\\)factors in both prover and verifier. Additionally, we report a C++ realization of this system (which we calllibSTARK). Compared to prevailing ZK realizations, it has the fastest proving and (total) verification time for sufficiently largesequentialcomputations.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_23"}, {"primary_key": "2986277", "vector": [], "sparse_vector": [], "title": "On Round Optimal Statistical Zero Knowledge Arguments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct the first three message statistical zero knowledge arguments for all of NP, matching the known lower bound. We do so based on keyless multi-collision resistant hash functions and the Learning with Errors assumption—the same assumptions used to obtain round optimal computational zero knowledge. The main component in our construction is a statistically witness indistinguishable argument of knowledge based on a new notion of statistically hiding commitments with subset opening.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_5"}, {"primary_key": "2986278", "vector": [], "sparse_vector": [], "title": "Data-Independent Memory Hard Functions: New Attacks and Stronger Constructions.", "authors": ["<PERSON>", "<PERSON>", "Siteng Kang", "<PERSON><PERSON><PERSON><PERSON>", "Lu <PERSON>ng", "<PERSON>"], "summary": "Memory-hard functions (MHFs) are a key cryptographic primitive underlying the design of moderately expensive password hashing algorithms and egalitarian proofs of work. Over the past few years several increasingly stringent goals for an MHF have been proposed including the requirement that the MHF have high sequential space-time (ST) complexity, parallel space-time complexity, amortized area-time (aAT) complexity and sustained space complexity. Data-Independent Memory Hard Functions (iMHFs) are of special interest in the context of password hashing as they naturally resist side-channel attacks. iMHFs can be specified using a directed acyclic graph (DAG)Gwith\\(N=2^n\\)nodes and low indegree and the complexity of the iMHF can be analyzed using a pebbling game. Recently, <PERSON><PERSON> et al. [ABH17] constructed a DAG called DRSample that has aAT complexity at least\\(\\varOmega \\!\\left( N^2/{\\text {log}} N\\right) \\). Asymptotically DRSample outperformed all prior iMHF constructions including Argon<PERSON>i, winner of the password hashing competition (aAT cost\\({\\mathcal {O}} \\!\\left( N^{1.767}\\right) \\)), though the constants in these bounds are poorly understood. We show that the greedy pebbling strategy of <PERSON><PERSON> et al. [BCS16] is particularly effective against DRSample e.g., the aAT cost is\\({\\mathcal {O}} (N^2/{\\text {log}} N)\\). In fact, our empirical analysisreversesthe prior conclusion of <PERSON>wen et al. that DRSample provides stronger resistance to known pebbling attacks for practical values of\\(N \\le 2^{24}\\). We construct a new iMHF candidate (DRSample+BRG) by using the bit-reversal graph to extend DRSample. We then prove that the construction is asymptotically optimal under every MHF criteria, and we empirically demonstrate that our iMHF provides the best resistance toknownpebbling attacks. For example, we show that any parallel pebbling attack either has aAT cost\\(\\omega (N^2)\\)or requires at least\\(\\varOmega (N)\\)steps with\\(\\varOmega (N/{\\text {log}} N)\\)pebbles on the DAG. This makes our construction the first practical iMHF with a strong sustained space-complexity guarantee and immediately implies that any parallel pebbling has aAT complexity\\(\\varOmega (N^2/{\\text {log}} N)\\). We also prove that any sequential pebbling (including the greedy pebbling attack) has aAT cost\\(\\varOmega \\!\\left( N^2\\right) \\)and, if a plausible conjecture holds, any parallel pebbling has aAT cost\\(\\varOmega (N^2 \\log \\log N/{\\text {log}} N)\\)—the best possible bound for an iMHF. We implement our new iMHF and demonstrate that it is just as fast as Argon2. Along the way we propose a simple modification to the Argon2 round function that increases an attacker’s aAT cost by nearly an order of magnitude without increasing running time on a CPU. Finally, we give a pebbling reduction that proves that in the parallel random oracle model (PROM) the cost of evaluating an iMHF like Argon2i or DRSample+BRG is given by the pebbling cost of the underlying DAG. Prior pebbling reductions assumed that the iMHF round function concatenates input labels before hashing and did not apply to practical iMHFs such as Argon2i, DRSample or DRSample+BRG where input labels are instead XORed together.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_20"}, {"primary_key": "2986279", "vector": [], "sparse_vector": [], "title": "Unconditionally Secure Computation Against Low-Complexity Leakage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the problem of constructing leakage-resilient circuit compilers that are secure against global leakage functions with bounded output length. By global, we mean that the leakage can depend on all circuit wires and output a low-complexity function (represented as a multi-output Boolean circuit) applied on these wires. In this work, we design compilers both in the stateless (a.k.a. single-shot leakage) setting and the stateful (a.k.a. continuous leakage) setting that areunconditionallysecure against\\(\\mathsf {AC}^0\\)leakage and similar low-complexity classes. In the stateless case, we show that the original private circuits construction of Ishai, Sahai, and Wagner (Crypto 2003) is actually secure against\\(\\mathsf {AC}^0\\)leakage. In the stateful case, we modify the construction of <PERSON><PERSON><PERSON> (Crypto 2012), obtaining a simple construction with unconditional security. Prior works that designed leakage-resilient circuit compilers against\\(\\mathsf {AC}^0\\)leakage had to rely either on secure hardware components (<PERSON> et al., Eurocrypt 2010, Miles-Viola, STOC 2013) or on (unproven) complexity-theoretic assumptions (<PERSON><PERSON><PERSON>, Crypto 2012).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_14"}, {"primary_key": "2986280", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge Proofs on Secret-Shared Data via Fully Linear PCPs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "We introduce and study the notion offully linearprobabilistically checkable proof systems. In such a proof system, the verifier can make a small number of linear queries that applyjointlyto the input and a proof vector. Our new type of proof system is motivated by applications in which the input statement is not fully available to any single verifier, but can still be efficiently accessed via linear queries. This situation arises in scenarios where the input is partitioned or secret-shared between two or more parties, or alternatively is encoded using an additively homomorphic encryption or commitment scheme. This setting appears in the context of secure messaging platforms, verifiable outsourced computation, PIR writing, private computation of aggregate statistics, and secure multiparty computation (MPC). In all these applications, there is a need for fully linear proof systems withshort proofs. While several efficient constructions of fully linear proof systems are implicit in the interactive proofs literature, many questions about their complexity are open. We present several new constructions of fully linearzero-knowledgeproof systems withsublinearproof size for “simple” or “structured” languages. For example, in thenon-interactivesetting of fully linear PCPs, we show how to prove that an input vector\\(x\\in {\\mathbb {F}}^n\\), for a finite field\\({\\mathbb {F}}\\), satisfies a single degree-2 equation with a proof of size\\(O(\\sqrt{n})\\)and\\(O(\\sqrt{n})\\)linear queries, which we show to be optimal. More generally, for languages that can be recognized by systems of constant-degree equations, we can reduce the proof size to\\(O(\\log n)\\)at the cost of\\(O(\\log n)\\)rounds of interaction. We use our new proof systems to construct new short zero-knowledge proofs on distributed and secret-shared data. These proofs can be used to improve the performance of the example systems mentioned above. Finally, we observe that zero-knowledge proofs on distributed data provide a general-purpose tool for protecting MPC protocols against malicious parties. Applying our short fully linear PCPs to “natural” MPC protocols in the honest-majority setting, we can achieve unconditional protection against malicious parties with sublinear additive communication cost. We use this to improve the communication complexity of recent honest-majority MPC protocols. For instance, using any pseudorandom generator, we obtain a 3-party protocol for Boolean circuits in which the amortized communication cost is onlyone bitper AND gate per party (compared to 10 bits in the best previous protocol), matching the best known protocols for semi-honest parties.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_3"}, {"primary_key": "2986281", "vector": [], "sparse_vector": [], "title": "Batching Techniques for Accumulators with Applications to IOPs and Stateless Blockchains.", "authors": ["<PERSON>", "Benedikt <PERSON>", "<PERSON>"], "summary": "We present batching techniques for cryptographic accumulators and vector commitments in groups of unknown order. Our techniques are tailored for distributed settings where no trusted accumulator manager exists and updates to the accumulator are processed in batches. We develop techniques for non-interactively aggregating membership proofs that can be verified with a constant number of group operations. We also provide a constant sized batch non-membership proof for a large number of elements. These proofs can be used to build the first positional vector commitment (VC) with constant sized openings and constant sized public parameters. As a core building block for our batching techniques we develop several succinct proof systems in groups of unknown order. These extend a recent construction of a succinct proof of correct exponentiation, and include a succinct proof of knowledge of an integer discrete logarithm between two group elements. We circumvent an impossibility result for Sigma-protocols in these groups by using a short trapdoor-free CRS. We use these new accumulator and vector commitment constructions to design a stateless blockchain, where nodes only need a constant amount of storage in order to participate in consensus. Further, we show how to use these techniques to reduce the size of IOP instantiations, such as STARKs. The full version of the paper is available online [BBF18b].", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_20"}, {"primary_key": "2986282", "vector": [], "sparse_vector": [], "title": "Algebraic Techniques for Short(er) Exact Lattice-Based Zero-Knowledge Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A key component of many lattice-based protocols is a zero-knowledge proof of knowledge of a vector\\(\\vec {s}\\)with small coefficients satisfying\\(A\\vec {s}=\\vec {u}\\bmod \\,q\\). While there exist fairly efficient proofs for a relaxed version of this equation which prove the knowledge of\\(\\vec {s}'\\)andcsatisfying\\(A\\vec {s}'=\\vec {u}c\\)where\\(\\Vert \\vec {s}'\\Vert \\gg \\Vert \\vec {s}\\Vert \\)andcis some small element in the ring over which the proof is performed, the proofs for the exact version of the equation are considerably less practical. The best such proof technique is an adaptation of <PERSON>’s protocol (Crypto ’93), for proving knowledge of nearby codewords, to larger moduli. The scheme is a\\(\\varSigma \\)-protocol, each of whose iterations has soundness error\\(2{/}3\\), and thus requires over 200 repetitions to obtain soundness error of\\(2^{-128}\\), which is the main culprit behind the large size of the proofs produced. In this paper, we propose the first lattice-based proof system that significantly outperforms Stern-type proofs for proving knowledge of a short\\(\\vec {s}\\)satisfying\\(A\\vec {s}=\\vec {u}\\bmod \\,q\\). Unlike <PERSON>’s proof, which is combinatorial in nature, our proof is more algebraic and uses various relaxed zero-knowledge proofs as sub-routines. The main savings in our proof system comes from the fact that each round has soundness error of\\(1{/}n\\), wherenis the number of columns ofA. For typical applications,nis a few thousand, and therefore our proof needs to be repeated around 10 times to achieve a soundness error of\\(2^{-128}\\). For concrete parameters, it produces proofs that are around an order of magnitude smaller than those produced using Stern’s approach.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_7"}, {"primary_key": "2986283", "vector": [], "sparse_vector": [], "title": "Efficient Pseudorandom Correlation Generators: Silent OT Extension and More.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure multiparty computation (MPC) often relies on correlated randomness for better efficiency and simplicity. This is particularly useful for MPC with no honest majority, where input-independent correlated randomness enables a lightweight “non-cryptographic” online phase once the inputs are known. However, since the amount of randomness typically scales with the circuit size of the function being computed, securely generating correlated randomness forms an efficiency bottleneck, involving a large amount of communication and storage. A natural tool for addressing the above limitations is apseudorandom correlation generator(PCG). A PCG allows two or more parties to securely generate long sources of useful correlated randomness via a local expansion of correlated short seeds and no interaction. PCGs enable MPC withsilent preprocessing, where a small amount of interaction used for securely sampling the seeds is followed by silent local generation of correlated pseudorandomness. A concretely efficient PCG for Vector-OLE correlations was recently obtained by <PERSON> et al. (CCS 2018) based on variants of the learning parity with noise (LPN) assumption over large fields. In this work, we initiate a systematic study of PCGs and present concretely efficient constructions for several types of useful MPC correlations. We obtain the following main contributions: PCG foundations.We give a general security definition for PCGs. Our definition suffices for any MPC protocol satisfying a stronger security requirement that is met by existing protocols. We prove that a stronger security requirement is indeed necessary, and justify our PCG definition by ruling out a stronger and more natural definition. Silent OT extension.We present the first concretely efficient PCG for oblivious transfer correlations. Its security is based on a variant of the binary LPN assumption and any correlation-robust hash function. We expect it to provide a faster alternative to the IKNP OT extension protocol (Crypto 2003) when communication is the bottleneck. We present several applications, including protocols for non-interactive zero-knowledge with bounded-reusable preprocessing from binary LPN, and concretely efficient related-key oblivious pseudorandom functions. PCGs for simple 2-party correlations.We obtain PCGs for several other types of useful 2-party correlations, including (authenticated) one-time truth-tables and Beaver triples. While the latter PCGs are slower than our PCG for OT, they are still practically feasible. These PCGs are based on a host of assumptions and techniques, including specialized homomorphic secret sharing schemes and pseudorandom generators tailored to their structure. Multiparty correlations.We obtain PCGs for multiparty correlations that can be used to make the (input-dependent) online communication of MPC protocols scalelinearlywith the number of parties, instead of quadratically.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_16"}, {"primary_key": "2986284", "vector": [], "sparse_vector": [], "title": "Strong Asymmetric PAKE Based on Trapdoor CKEM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Password-Authenticated Key Exchange (PAKE) protocols allow two parties that share a password to establish a shared key in a way that is immune to offline attacks. Asymmetric PAKE (aPAKE) [20] adapts this notion to the common client-server setting, where the server stores a one-way hash of the password instead of the password itself, and server compromise allows the adversary to recover the password only via the (inevitable) offline dictionary attack. Most aPAKE protocols, however, allow an attacker topre-compute a dictionary of hashed passwords, thus instantly learning the password on server compromise. Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> formalized a Universally ComposablestrongaPAKE (saPAKE) [23], which requires the password hash to be salted so that the dictionary attack can only start after the server compromise leaks the salt and the salted hash. The UC saPAKE protocol shown in [23], called OPAQUE, uses 3 protocol flows, 3–4 exponentiations per party, and relies on the One-More Di<PERSON><PERSON>-<PERSON> assumption in ROM. We propose an alternative UC saPAKE construction based on a novel use of the encryption+SPHF paradigm for UC PAKE design [19,26]. Compared to OPAQUE, our protocol uses only 2 flows, has comparable costs, avoids hashing onto a group, and relies on different assumptions, namely <PERSON><PERSON> (DDH), <PERSON> (SDH), and an assumption that the Boneh-<PERSON>en function\\(f_ s (x)=g^{1/( s +x)}\\)[9] is aSalted Tight One-Way Function(STOWF). We formalize a UC model for STOWF and analyze the Boneh-Boyen function as UC STOWF in the generic group model and ROM. Our saPAKE protocol employs a new form of Conditional Key Encapsulation Mechanism (CKEM), a generalization of SPHF, which we call animplicit-statementCKEM. This strengthening of SPHF allows for a UC (sa)PAKE design where only the client commits to its password, and only the server performs an SPHF, compared to the standard UC PAKE design paradigm where the encrypt+SPHF subroutine is used symmetrically by both parties.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_26"}, {"primary_key": "2986285", "vector": [], "sparse_vector": [], "title": "Leakage Certification Revisited: Bounding Model Errors in Side-Channel Security Evaluations.", "authors": ["Olivier <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leakage certification aims at guaranteeing that the statistical models used in side-channel security evaluations are close to the true statistical distribution of the leakages, hence can be used to approximate a worst-case security level. Previous works in this direction were only qualitative: for a given amount of measurements available to an evaluation laboratory, they rated a model as “good enough” if the model assumption errors (i.e., the errors due to an incorrect choice of model family) were small with respect to the model estimation errors. We revisit this problem by providing the first quantitative tools for leakage certification. For this purpose, we provide bounds for the (unknown)Mutual Informationmetric that corresponds to the true statistical distribution of the leakages based on two easy-to-compute information theoretic quantities: thePerceived Information, which is the amount of information that can be extracted from a leaking device thanks to an estimated statistical model, possibly biased due to estimation and assumption errors, and theHypothetical Information, which is the amount of information that would be extracted from an hypothetical device exactly following the model distribution. This positive outcome derives from the observation that while the estimation of the Mutual Information is in general a hard problem (i.e., estimators are biased and their convergence is distribution-dependent), it is significantly simplified in the case of statistical inference attacks where a target random variable (e.g., a key in a cryptographic setting) has a constant (e.g., uniform) probability. Our results therefore provide a general and principled path to bound the worst-case security level of an implementation. They also significantly speed up the evaluation of any profiled side-channel attack, since they imply that the estimation of the Perceived Information, which embeds an expensive cross-validation step, can be bounded by the computation of a cheaper Hypothetical Information, for any estimated statistical model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_25"}, {"primary_key": "2986286", "vector": [], "sparse_vector": [], "title": "Two-Party ECDSA from Hash Proof Systems and Efficient Instantiations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "ECDSA is a widely adopted digital signature standard. Unfortunately, efficient distributed variants of this primitive are notoriously hard to achieve and known solutions often require expensive zero knowledge proofs to deal with malicious adversaries. For the two party case, <PERSON><PERSON> [<PERSON><PERSON>] recently managed to get an efficient solution which, to achieve simulation-based security, relies on an interactive, non standard, assumption on <PERSON><PERSON><PERSON>’s cryptosystem. In this paper we generalize <PERSON><PERSON>’s solution using hash proof systems. The main advantage of our generic method is that it results in a simulation-based security proof without resorting to non-standard interactive assumptions. Moving to concrete constructions, we show how to instantiate our framework using class groups of imaginary quadratic fields. Our implementations show that the practical impact of dropping such interactive assumptions is minimal. Indeed, while for 128-bit security our scheme is marginally slower than <PERSON><PERSON>’s, for 256-bit security it turns out to be better both in key generation and signing time. Moreover, in terms of communication cost, our implementation significantly reduces both the number of rounds and the transmitted bits without exception.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_7"}, {"primary_key": "2986287", "vector": [], "sparse_vector": [], "title": "Universally Composable Secure Computation with Corrupted Tokens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce thecorrupted token model. This model generalizes thetamper-proof token modelproposed by <PERSON> (EUROCRYPT ’07) relaxing the trust assumption on the honest behavior of tokens. Our model is motivated by the real-world practice of outsourcing hardware production to possibly corrupted manufacturers. We capture the malicious behavior of token manufacturers by allowing the adversary to corrupt the tokens of honest players at the time of their creation. We show that under minimal complexity assumptions, i.e., the existence of one-way functions, it is possible to UC-securely realize (a variant of) the tamper-proof token functionality of <PERSON> in the corrupted token model withnstateless tokens assuming that the adversary corrupts at most\\(n-1\\)of them (for any\\(n>0\\)). We apply this result to existing multi-party protocols in <PERSON>’s model to achieve UC-secure MPC in the corrupted token model assuming only the existence of one-way functions. Finally, we show how to obtain the above results using tokens of small size that take only short inputs. The technique in this result can also be used to improve the assumption of UC-secure hardware obfuscation recently proposed by Nayaket al.(NDSS ’17). While their construction requires the existence of collision-resistant hash functions, we can obtain the same result from only one-way functions. Moreover using our main result we can improve the trust assumption on the tokens as well.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_14"}, {"primary_key": "2986288", "vector": [], "sparse_vector": [], "title": "Reusable Non-Interactive Secure Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of Non-Interactive Two-Party Secure Computation (NISC), where <PERSON> wishes to publish an encryption of her inputx, in such a way that any other party, who holds an inputy, can send her a single message which conveys to her the valuef(x,y), and nothing more. We demand security against malicious parties. While such protocols are easy to construct using garbled circuits and general non-interactive zero-knowledge proofs, this approach inherently makes a non-black-box use of the underlying cryptographic primitives and is infeasible in practice. <PERSON><PERSON> et al. (Eurocrypt 2011) showed how to construct NISC protocols that only use parallel calls to an ideal oblivious transfer (OT) oracle, and additionally make only a black-box use of any pseudorandom generator. Combined with the efficient 2-message OT protocol of <PERSON><PERSON><PERSON><PERSON> et al. (Crypto 2008), this leads to a practical approach to NISC that has been implemented in subsequent works. However, a major limitation of all known OT-based NISC protocols is that they are subject toselective failure attacksthat allows a malicious sender to entirely compromise the security of the protocol when the receiver’s first message is reused. Motivated by the failure of the OT-based approach, we consider the problem of basingreusableNISC on parallel invocations of a standard arithmetic generalization of OT known as oblivious linear-function evaluation (OLE). We obtain the following results: We construct an information-theoretically secure reusable NISC protocol for arithmetic branching programs and general zero-knowledge functionalities in the OLE-hybrid model. Our zero-knowledge protocol only makes an absolute constant number of OLE calls per gate in an arithmetic circuit whose satisfiability is being proved. We also get reusable NISC in the OLE-hybrid model for general Boolean circuits using any one-way function. We complement this by a negative result, showing that reusable NISC is impossible to achieve in the OT-hybrid model. This provides a formal justification for the need to replace OT by OLE. We build a universally composable 2-messagereusableOLE protocol in the CRS model that can be based on the security of Paillier encryption and requires only a constant number of modular exponentiations. This provides the first arithmetic analogue of the 2-message OT protocols of Peikert et al. (Crypto 2008). By combining our NISC protocol in the OLE-hybrid model and the 2-message OLE protocol, we get protocols with new attractive asymptotic and concrete efficiency features. In particular, we get the first (designated-verifier) NIZK protocols for NP where following a statement-independent preprocessing, both proving and verifying are entirely “non-cryptographic” and involve only a constant computational overhead. Furthermore, we get the firststatisticaldesignated-verifier NIZK argument for NP under an assumption related to factoring.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_15"}, {"primary_key": "2986289", "vector": [], "sparse_vector": [], "title": "Continuous Space-Bounded Non-malleable Codes from Stronger Proofs-of-Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-malleable codes are encoding schemes that provide protections against various classes of tampering attacks. Recently <PERSON> et al. (CRYPTO 2017) initiated the study ofspace-boundednon-malleable codes that provide such protections against tampering within small-space devices. They put forward a construction based on anynon-interactive proof-of-space(NIPoS). However, the scheme only protects against an a priori bounded number of tampering attacks. We construct non-malleable codes that are resilient to an unbounded polynomial number of space-bounded tamperings. Towards that we introduce a stronger variant of\\(\\text {NIPoS}\\)calledproof-extractable\\(\\text {NIPoS}\\)(\\(\\text {PExt-NIPoS}\\)), and propose two approaches of constructing such a primitive. Using a new proof strategy we show that the generic encoding scheme of <PERSON> et al. achieves unbounded tamper-resilience when instantiated with a\\(\\text {PExt-NIPoS}\\). We show two methods to construct\\(\\text {PExt-NIPoS}\\): The first method uses a special family of “memory-hard” graphs, calledchallenge-hard graphs(CHG), a notion we introduce here. We instantiate such family of graphs based on anextension ofstack of localized expanders (first used by <PERSON> and <PERSON> in the context of proof-of-space). In addition, we show that the graph construction used as a building block for the proof-of-space by <PERSON><PERSON><PERSON><PERSON> et al. (CRYPTO 2015) satisfies challenge-hardness as well. These two CHG-instantiations lead to continuous space-bounded NMC with different features in the random oracle model. Our second instantiation relies on a new measurable property, calleduniquenessof\\(\\text {NIPoS}\\). We show that standard extractability can be upgraded to proof-extractability if the\\(\\text {NIPoS}\\)also has uniqueness. We propose a simple heuristic construction of\\(\\text {NIPoS}\\), that achieves (partial) uniqueness, based on a candidate memory-hard function in the standard model and a publicly verifiable computation with small-space verification. Instantiating the encoding scheme of Faust et al. with this\\(\\text {NIPoS}\\), we obtain a continuous space-bounded NMC that supports the “most practical” parameters, complementing the provably secure but “relatively impractical” CHG-based constructions. Additionally, we revisit the construction of Faust et al. and observe that due to the lack of uniqueness of their\\(\\text {NIPoS}\\), the resulting encoding schemes yield “highly impractical” parameters in the continuous setting. We conclude the paper with a comparative study of all our non-malleable code constructions with an estimation of concrete parameters.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_17"}, {"primary_key": "2986290", "vector": [], "sparse_vector": [], "title": "How to Build Pseudorandom Functions from Public Random Permutations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Pseudorandom functions are traditionally built upon block ciphers, but with the trend of permutation based cryptography, it is a natural question to investigate the design of pseudorandom functions from random permutations. We present a generic study of how to build beyond birthday bound secure pseudorandom functions from public random permutations. We first show that a pseudorandom function based on a single permutation call cannot be secure beyond the\\(2^{n/2}\\)birthday bound, wherenis the state size of the function. We next consider the Sum of Even-Mansour (SoEM) construction, that instantiates the sum of permutations with the Even-Mans<PERSON> construction. We prove that SoEM achieves tight\\(2n{/}3\\)-bit security if it is constructed from two independent permutations and two randomly drawn keys. We also demonstrate a birthday bound attack if either the permutations or the keys are identical. Finally, we present the Sum of Key Alternating Ciphers (SoKAC) construction, a translation of Encrypted Davies-Meyer Dual to a public permutation based setting, and show that SoKAC achieves tight\\(2n{/}3\\)-bit security even when a single key is used.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_10"}, {"primary_key": "2986291", "vector": [], "sparse_vector": [], "title": "Memory-Hard Functions from Cryptographic Primitives.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Memory-hard functions (MHFs) are moderately-hard functions which enforce evaluation costs both in terms of time and memory (often, in form of a trade-off). They are used e.g. for password protection, password-based key-derivation, and within cryptocurrencies, and have received a considerable amount of theoretical scrutiny over the last few years. However, analyses see MHFs as modes of operation of some underlying hash function\\(\\mathcal {H}\\), modeled as a monolithic random oracle. This is however a very strong assumption, as such hash functions are built from much simpler primitives, following somewhat ad-hoc design paradigms. This paper initiates the study of how to securely instantiate\\(\\mathcal {H}\\)within MHF designs using common cryptographic primitives like block ciphers, compression functions, and permutations. Security here will be in a model in which the adversary has parallel access to an idealized version of the underlying primitive. We will provide provably memory-hard constructions from all the aforementioned primitives. Our results are generic, in that we will rely on hard-to-pebble graphs designed in prior works to obtain our constructions. One particular challenge we encounter is that\\(\\mathcal {H}\\)is usually required to have large outputs (to increase memory hardness without changing the description size of MHFs), whereas the underlying primitives generally have small output sizes.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_19"}, {"primary_key": "2986292", "vector": [], "sparse_vector": [], "title": "Statistical Zeroizing Attack: Cryptanalysis of Candidates of BP Obfuscation over GGH15 Multilinear Map.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a new cryptanalytic algorithm on obfuscations based on GGH15 multilinear map. Our algorithm,statistical zeroizing attack, directly distinguishes two distributions from obfuscation while it follows the zeroizing attack paradigm, that is, it uses evaluations of zeros of obfuscated programs. Our attack breaks the recent indistinguishability obfuscation candidate suggested by <PERSON><PERSON>.(CRYPTO’18) for the optimal parameter settings. More precisely, we show that there are two functionally equivalent branching programs whose CVW obfuscations can be efficiently distinguished by computing the sample variance of evaluations. This statistical attack gives a new perspective on the security of the indistinguishability obfuscations: we should consider the shape of the distributions of evaluation of obfuscation to ensure security. In other words, while most of the previous (weak) security proofs have been studied with respect to algebraic attack model or ideal model, our attack shows that this algebraic security is not enough to achieve indistinguishability obfuscation. In particular, we show that the obfuscation scheme suggested by Bart<PERSON>ket al.(TCC’18) does not achieve the desired security in a certain parameter regime, in which their algebraic security proof still holds. The correctness of statistical zeroizing attacks holds under a mild assumption on the preimage sampling algorithm with a lattice trapdoor. We experimentally verify this assumption for implemented obfuscation by <PERSON><PERSON><PERSON> al.(ACM CCS’17).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_9"}, {"primary_key": "2986293", "vector": [], "sparse_vector": [], "title": "Adaptively Secure MPC with Sublinear Communication Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A central challenge in the study of MPC is to balance between security guarantees, hardness assumptions, and resources required for the protocol. In this work, we study the cost of tolerating adaptive corruptions in MPC protocols under various corruption thresholds. In the strongest setting, we consider adaptive corruptions of an arbitrary number of parties (potentially all) and achieve the following results: A two-round secure function evaluation (SFE) protocol in the CRS model, assuming LWE and indistinguishability obfuscation (iO). The communication, the CRS size, and the online-computation are sublinear in thesizeof the function. The iO assumption can be replaced by secure erasures. Previous results required either the communication or the CRS size to be polynomial in the function size. Under the same assumptions, we construct a “Bob-optimized” 2PC (where <PERSON> talks first, <PERSON> second, and <PERSON> learns the output). That is, the communication complexity and total computation of <PERSON> are sublinear in the function size and in <PERSON>’s input size. We prove impossibility of “Alice-optimized” protocols. Assuming LWE, we bootstrap adaptively secure NIZK arguments to achieve proof size sublinear in the circuit size of the NP-relation. On a technical level, our results are based onlaconic function evaluation (LFE)(<PERSON>ua<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, FOCS’18) and shed light on an interesting duality between LFE and FHE. Next, we analyze adaptive corruptions of all-but-one of the parties and show a two-round SFE protocol in the threshold PKI model (where keys of a threshold FHE scheme are pre-shared among the parties) with communication complexity sublinear in the circuit size, assuming LWE and NIZK. Finally, we consider the honest-majority setting, and show a two-round SFE protocol with guaranteed output delivery under the same constraints.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_2"}, {"primary_key": "2986294", "vector": [], "sparse_vector": [], "title": "Highly Efficient Key Exchange Protocols with Optimal Tightness.", "authors": ["<PERSON><PERSON>-<PERSON>", "Cas Cremers", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we give nearly-tight reductions for modern implicitly authenticated <PERSON><PERSON><PERSON><PERSON> protocols in the style of the Signal and Noise protocols, which are extremely simple and efficient. Unlike previous approaches, the combination of nearly-tight proofs and efficient protocols enables the first real-world instantiations for which the parameters can be chosen in a theoretically sound manner. Our reductions have only a linear loss in the number of users, implying that our protocols are more efficient than the state of the art when instantiated with theoretically sound parameters. We also prove that our security proofs are optimal: a linear loss in the number of users is unavoidable for our protocols for a large and natural class of reductions.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_25"}, {"primary_key": "2986295", "vector": [], "sparse_vector": [], "title": "Seedless Fruit Is the Sweetest: Random Number Generation, Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The need for high-quality randomness in cryptography makes random-number generation one of its most fundamental tasks. A recent important line of work (initiated by <PERSON><PERSON> et al., CCS ’13) focuses on the notion ofrobustnessforpseudorandom number generators (PRNGs) with inputs. These are primitives that use various sources to accumulate sufficient entropy into a state, from which pseudorandom bits are extracted. Robustness ensures that PRNGs remain secure even under state compromise and adversarial control of entropy sources. However, the achievability of robustness inherently depends on aseed, or, alternatively, on an ideal primitive (e.g., a random oracle), independent of the source of entropy. Both assumptions are problematic: seed generation requires randomness to start with, and it is arguable whether the seed or the ideal primitive can be kept independent of the source. This paper resolves this dilemma by putting forward new notions of robustness which enable both (1)seedlessPRNGs and (2)primitive-dependentadversarial sources of entropy. To bypass obvious impossibility results, we make a realistic compromise by requiring that the source produce sufficient entropyevengiven its evaluations of the underlying primitive. We also provide natural, practical, and provably secure constructions based on hash-function designs from compression functions, block ciphers, and permutations. Our constructions can be instantiated with minimal changes to industry-standard hash functions SHA-2 and SHA-3, or key derivation function HKDF, and can be downgraded to(online) seedless randomness extractors, which are of independent interest. On the way we consider both acomputationalvariant of robustness, where attackers only make a bounded number of queries to the ideal primitive, as well as a newinformation-theoreticvariant, which dispenses with this assumption to a certain extent, at the price of requiring a high rate of injected weak randomness (as it is, e.g., plausible on Intel’s on-chip RNG). The latter notion enables applications such as everlasting security. Finally, we show that the CBC extractor, used by Intel’s on-chip RNG, is provably insecure in our model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_8"}, {"primary_key": "2986296", "vector": [], "sparse_vector": [], "title": "Quantum Indistinguishability of Random Sponges.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we show that the sponge construction can be used to construct quantum-secure pseudorandom functions. As our main result we prove that random sponges are quantum indistinguishable from random functions. In this setting the adversary is given superposition access to the input-output behavior of the construction but not to the internal function. Our proofs hold under the assumption that the internal function is a random function or permutation. We then use this result to obtain a quantum-security version of a result by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (FSE’15) which shows that a sponge that uses a secure PRP or PRF as internal function is a secure PRF. This result also proves that the recent attacks against CBC-MAC in the quantum-access model by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (Crypto’16) and <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (QIC’16) can be prevented by introducing a state with a non-trivial inner part. The proof of our main result is derived by analyzing the joint distribution of anyqinput-output pairs. Our method analyzes the statistical behavior of the considered construction in great detail. The used techniques might prove useful in future analysis of different cryptographic primitives considering quantum adversaries. Using <PERSON><PERSON><PERSON>’s PRF/PRP switching lemma we then obtain that quantum indistinguishability also holds if the internal block function is a random permutation.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_11"}, {"primary_key": "2986297", "vector": [], "sparse_vector": [], "title": "Proofs of Replicated Storage Without Timing Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide a formal treatment ofproof of replicated storage, a novel cryptographic primitive recently proposed in the context of a novel cryptocurrency, namely Filecoin. In a nutshell, proofs of replicated storage is a solution to the following problem: A user stores a filemonndifferent servers to ensure that the file will be available even if some of the servers fail. Using proof of retrievability, the user could check that every server is indeed storing the file. However, what if the servers collude and, in order to save on resources, decide to only store one copy of the file? A proof of replicated storage guarantees that, unless the (potentially colluding) servers are indeed reserving the space necessary to storencopies of the file, the user will not accept the proofs. While some candidate proofs of replicated storage have already been proposed, their soundness relies on timing assumptions i.e., the user must reject the proof if the prover does not reply within a certain time-bound. In this paper we provide the first construction of a proof of replication which does not rely on any timing assumptions.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_13"}, {"primary_key": "2986298", "vector": [], "sparse_vector": [], "title": "Communication Lower Bounds for Statistically Secure MPC, With or Without Preprocessing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove a lower bound on the communication complexity of unconditionally secure multiparty computation, both in the standard model with\\(n=2t+1\\)parties of whichtare corrupted, and in the preprocessing model with\\(n=t+1\\). In both cases, we show that for any\\(g \\in \\mathbb {N}\\)there exists a Boolean circuitCwithggates, where any secure protocol implementingCmust communicate\\(\\varOmega (n g)\\)bits, even if only passive and statistical security is required. The results easily extends to constructing similar circuits over any fixed finite field. This shows that for all sizes of circuits, theO(n) overhead of all known protocols whentis maximal is inherent. It also shows that security comes at a price: the circuit we consider could namely be computed amongnparties with communication onlyO(g) bits if no security was required. Our results extend to the case where the thresholdtis suboptimal. For the honest majority case, this shows that the known optimizations via packed secret-sharing can only be obtained if one accepts that the threshold is\\(t= (1/2 - c)n\\)for a constantc. For the honest majority case, we also show an upper bound that matches the lower bound up to a constant factor (existing upper bounds are a factor\\(\\lg n\\)off for Boolean circuits).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_3"}, {"primary_key": "2986299", "vector": [], "sparse_vector": [], "title": "Security of the Fiat-Shamir Transformation in the Quantum Random-Oracle Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The famous Fiat-Shamir transformation turns any public-coin three-round interactive proof, i.e., any so-called\\(\\Sigma {\\text {-protocol}}\\), into a non-interactive proof in the random-oracle model. We study this transformation in the setting of aquantum adversarythat in particular may query the random oracle in quantum superposition. Our main result is a generic reduction that transforms any quantum dishonest prover attacking the Fiat-Shamir transformation in the quantum random-oracle model into a similarly successful quantum dishonest prover attacking the underlying\\(\\Sigma {\\text {-protocol}}\\)(in the standard model). Applied to the standard soundness and proof-of-knowledge definitions, our reduction implies that both these security properties, in both the computational and the statistical variant, are preserved under the Fiat-Shamir transformation even when allowing quantum attacks. Our result improves and completes the partial results that have been known so far, but it also proves wrong certain claims made in the literature. In the context of post-quantum secure signature schemes, our results imply that for any\\(\\Sigma {\\text {-protocol}}\\)that is a proof-of-knowledge against quantum dishonest provers (and that satisfies some additional natural properties), the corresponding Fiat-Shamir signature scheme is secure in the quantum random-oracle model. For example, we can conclude that the non-optimized version ofFish, which is the bare Fiat-Shamir variant of the NIST candidatePicnic, is secure in the quantum random-oracle model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_13"}, {"primary_key": "2986300", "vector": [], "sparse_vector": [], "title": "Trapdoor Hash Functions and Their Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new primitive, calledtrapdoor hash functions(TDH), which are hash functions\\(\\mathsf {H}: \\{0,1\\}^n \\rightarrow \\{0,1\\}^\\lambda \\)with additional trapdoor function-like properties. Specifically, given an index\\(i\\in [n]\\), TDHs allow for sampling an encoding key\\(\\mathsf {ek}\\)(that hidesi) along with a corresponding trapdoor. Furthermore, given\\(\\mathsf {H}(x)\\), a hint value\\(\\mathsf {E}(\\mathsf {ek},x)\\), and the trapdoor corresponding to\\(\\mathsf {ek}\\), the\\(i^{th}\\)bit ofxcan be efficiently recovered. In this setting, one of our main questions is: How small can the hint value\\(\\mathsf {E}(\\mathsf {ek},x)\\)be? We obtain constructions where the hint is onlyone bitlong based on DDH, QR, DCR, or LWE. This primitive opens a floodgate of applications for low-communication secure computation. We mainly focus on two-message protocols between a receiver and a sender, with private inputsxandy, resp., where the receiver should learnf(x,y). We wish to optimize the(download) rateof such protocols, namely the asymptotic ratio between the size of the output and the sender’s message. Using TDHs, we obtain: The first protocols for (two-message)rate-1 string OTbased on DDH, QR, or LWE. This has several useful consequences, such as: The first constructions of PIR with communication cost poly-logarithmic in the database size based on DDH or QR. These protocols are in fact rate-1 when considering block PIR. The first constructions of asemi-compacthomomorphic encryption scheme for branching programs, where the encrypted output grows only with the programlength, based on DDH or QR. The first constructions of lossy trapdoor functions with input to output ratio approaching 1 based on DDH, QR or LWE. The firstconstant-rateLWE-based construction of a 2-message “statistically sender-private” OT protocol in the plain model. The firstrate-1protocols (under any assumption) fornparallel OTs and matrix-vector products from DDH, QR or LWE. We further consider the setting wherefevaluates a RAM programywith running time\\(T\\ll |x|\\)onx. We obtain the first protocols with communication sublinear in the size ofx, namely\\(T\\cdot \\sqrt{|x|}\\)or\\(T\\cdot \\root 3 \\of {|x|}\\), based on DDH or, resp., pairings (and correlated-input secure hash functions).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_1"}, {"primary_key": "2986301", "vector": [], "sparse_vector": [], "title": "On the Shortness of Vectors to Be Found by the Ideal-SVP Quantum Algorithm.", "authors": ["Léo Du<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The hardness of finding short vectors in ideals of cyclotomic number fields (hereafter, Ideal-SVP) can serve as a worst-case assumption for numerous efficient cryptosystems, via the average-case problems Ring-SIS and Ring-LWE. For a while, it could be assumed the Ideal-SVP problem was as hard as the analog problem for general lattices (SVP), even when considering quantum algorithms. But in the last few years, a series of works has lead to a quantum algorithm for Ideal-SVP that outperforms what can be done for general SVP in certain regimes. More precisely, it was demonstrated (under certain hypotheses) that one can find in quantum polynomial time a vector longer by a factor at most\\(\\alpha = \\exp ({\\widetilde{O}(n^{1/2})})\\)than the shortest non-zero vector in a cyclotomic ideal lattice, wherenis the dimension. In this work, we explore the constants hidden behind this asymptotic claim. While these algorithms have quantum steps, the steps that impact the approximation factor\\(\\alpha \\)are entirely classical, which allows us to estimate it experimentally using only classical computing. Moreover, we design heuristic improvements for those steps that significantly decrease the hidden factors in practice. Finally, we derive new provable effective lower bounds based on volumetric arguments. This study allows to predict the crossover point with classical lattice reduction algorithms, and thereby determine the relevance of this quantum algorithm in any cryptanalytic context. For example we predict that this quantum algorithm provides shorter vectors than BKZ-300 (roughly the weakest security level of NIST lattice-based candidates) for cyclotomic rings of rank larger than about 24000.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_12"}, {"primary_key": "2986302", "vector": [], "sparse_vector": [], "title": "Lattice-Based Zero-Knowledge Proofs: New Techniques for Shorter and Faster Constructions and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Dong<PERSON> Liu"], "summary": "We devise new techniques for design and analysis of efficient lattice-based zero-knowledge proofs (ZKP). First, we introduceone-shotproof techniques for non-linear polynomial relations of degree\\(k\\ge 2\\), where the protocol achieves a negligible soundness error in a single execution, and thus performs significantly better in both computation and communication compared to prior protocols requiring multiple repetitions. Such proofs with degree\\(k\\ge 2\\)have been crucial ingredients for important privacy-preserving protocols in the discrete logarithm setting, such as Bulletproofs (IEEE S&P ’18) and arithmetic circuit arguments (EUROCRYPT ’16). In contrast, one-shot proofs in lattice-based cryptography have previously only been shown for the linear case (\\(k=1\\)) and a very specific quadratic case (\\(k=2\\)), which are obtained as a special case of our technique. Moreover, we introduce two speedup techniques for lattice-based ZKPs: a CRT-packing technique supporting “inter-slot” operations, and “NTT-friendly” tools that permit the use of fully-splitting rings. The former technique comes at almost no cost to the proof length, and the latter one barely increases it, which can be compensated for by tweaking the rejection sampling parameters while still having faster computation overall. To illustrate the utility of our techniques, we show how to use them to build efficient relaxed proofs for important relations, namely proof of commitment to bits, one-out-of-many proof, range proof and set membership proof. Despite their relaxed nature, we further show how our proof systems can be used as building blocks for advanced cryptographic tools such as ring signatures. Our ring signature achieves a dramatic improvement in length over all the existing proposals from lattices at the same security level. The computational evaluation also shows that our construction is highly likely to outperform all the relevant works in running times. Being efficient in both aspects, our ring signature is particularly suitable for both small-scale and large-scale applications such as cryptocurrencies and e-voting systems. No trusted setup is required for any of our proposals.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_5"}, {"primary_key": "2986303", "vector": [], "sparse_vector": [], "title": "Non-malleable Secret Sharing in the Computational Setting: Adaptive Tampering, Noisy-Leakage Resilience, and Improved Rate.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the concept ofnon-malleablesecret sharing (<PERSON><PERSON> and <PERSON>, STOC 2018) in the computational setting. In particular, under the assumption of one-to-one one-way functions, we exhibit acomputationallyprivate,thresholdsecret sharing scheme satisfying all of the following properties. Continuous non-malleability:No computationally-bounded adversary tampering independently with all the shares can produce mauled shares that reconstruct to a value related to the original secret. This holds even in case the adversary can tampercontinuously, for anunboundedpolynomial number of times, with the same target secret sharing, where the next sequence of tampering functions, as well as the subset of shares used for reconstruction, can be chosenadaptivelybased on the outcome of previous reconstructions. Resilience to noisy leakage:Non-malleability holds even if the adversary can additionally leak information independently from all the shares. There is no bound on the length of leaked information, as long as the overall leakage does not decrease the min-entropy of each share by too much. Improved rate:The information rate of our final scheme, defined as the ratio between the size of the message and the maximal size of a share, asymptotically approaches 1 when the message length goes to infinity. Previous constructions achieved information-theoretic security, sometimes even for arbitrary access structures, at the price ofat least oneof the following limitations: (i) Non-malleability only holds against one-time tampering attacks; (ii) Non-malleability holds against a bounded number of tampering attacks, but both the choice of the tampering functions and of the sets used for reconstruction is non-adaptive; (iii) Information rate asymptotically approaching zero; (iv) No security guarantee in the presence of leakage.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_16"}, {"primary_key": "2986304", "vector": [], "sparse_vector": [], "title": "Non-Uniformly Sound Certificates with Applications to Concurrent Zero-Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of non-uniformly sound certificates: succinct single-message (unidirectional) argument systems that satisfy a “best-possible security” against non-uniform polynomial-time attackers. In particular, no polynomial-time attacker withsbits of non-uniform advice can find significantly more thansaccepting proofs for false statements. Our first result is a construction of non-uniformly sound certificates for all\\(\\mathbf{NP }\\)in the random oracle model, where the attacker’s advice can depend arbitrarily on the random oracle. We next show that the existence of non-uniformly sound certificates for\\(\\mathbf{P }\\)(and collision resistant hash functions) yields apublic-coin constant-roundfully concurrent zero-knowledge argument for\\(\\mathbf{NP } \\).", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_4"}, {"primary_key": "2986305", "vector": [], "sparse_vector": [], "title": "The Communication Complexity of Threshold Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Threshold private set intersection enables <PERSON> and <PERSON> who hold sets\\(S_{\\mathsf {A}}\\)and\\(S_{\\mathsf {B}}\\)of sizento compute the intersection\\(S_{\\mathsf {A}} \\cap S_{\\mathsf {B}} \\)if the sets do not differ by more than some threshold parameter\\(t\\). In this work, we investigate the communication complexity of this problem and we establish the first upper and lower bounds. We show that any protocol has to have a communication complexity of\\(\\varOmega (t)\\). We show that an almost matching upper bound of\\(\\tilde{\\mathcal {O}}(t)\\)can be obtained via fully homomorphic encryption. We present a computationally more efficient protocol based on weaker assumptions, namely additively homomorphic encryption, with a communication complexity of\\(\\tilde{\\mathcal {O}}(t ^2)\\). For applications like biometric authentication, where a given fingerprint has to have a large intersection with a fingerprint from a database, our protocols may result in significant communication savings. Prior to this work, all previous protocols had a communication complexity of\\(\\varOmega (n)\\). Our protocols are the first ones with communication complexities that mainly depend on the threshold parameter\\(t\\)and only logarithmically on the set sizen.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_1"}, {"primary_key": "2986306", "vector": [], "sparse_vector": [], "title": "Improving Attacks on Round-Reduced Speck32/64 Using Deep Learning.", "authors": ["<PERSON><PERSON>"], "summary": "This paper has four main contributions. First, we calculate the predicted difference distribution of Speck32/64 with one specific input difference under the Markov assumption completely for up to eight rounds and verify that this yields a globally fairly good model of the difference distribution of Speck32/64. Secondly, we show that contrary to conventional wisdom, machine learning can produce very powerful cryptographic distinguishers: for instance, in a simple low-data, chosen plaintext attack on nine rounds of Speck, we present distinguishers based on deep residual neural networks that achieve a mean key rank roughly five times lower than an analogous classical distinguisher using the full difference distribution table. Thirdly, we develop a highly selective key search policy based on a variant of Bayesian optimization which, together with our neural distinguishers, can be used to reduce the remaining security of 11-round Speck32/64 to roughly 38 bits. This is a significant improvement over previous literature. Lastly, we show that our neural distinguishers successfully use features of the ciphertext pair distribution that are invisible to all purely differential distinguishers even given unlimited data. While our attack is based on a known input difference taken from the literature, we also show that neural networks can be used to rapidly (within a matter of minutes on our machine) find good input differences without using prior human cryptanalysis. Supplementary code and data for this paper is available athttps://github.com/agohr/deep_speck.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_6"}, {"primary_key": "2986307", "vector": [], "sparse_vector": [], "title": "ABE for DFA from k-Lin.", "authors": ["Junqing Gong", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present the first attribute-based encryption (ABE) scheme for deterministic finite automaton (DFA) based on static assumptions in bilinear groups; this resolves an open problem posed by <PERSON> (CRYPTO 2012). Our main construction achieves selective security against unbounded collusions under the standardk-linear assumption in prime-order bilinear groups, whereas previous constructions all rely onq-type assumptions.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_25"}, {"primary_key": "2986308", "vector": [], "sparse_vector": [], "title": "Simultaneous Amplification: The Case of Non-interactive Zero-Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we explore the question of simultaneous privacy and soundness amplification for non-interactive zero-knowledgeargumentsystems (NIZK). We show that any\\(\\delta _s-\\)sound and\\(\\delta _z-\\)zero-knowledge NIZK candidate satisfying\\(\\delta _s+\\delta _z=1-\\epsilon \\), for any constant\\(\\epsilon >0\\), can be turned into a computationally sound and zero-knowledge candidate with the only extra assumption of a subexponentially secure public-key encryption. We develop novel techniques to leverage the use of leakage simulation lemma (Jetchev-Peitzrak TCC 2014) to argue amplification. A crucial component of our result is a new notion for secret sharing\\(\\mathsf {NP}\\)instances. We believe that this may be of independent interest. To achieve this result we analyze following two transformations: Parallel Repetition:We show that using parallel repetition any\\(\\delta _s-\\)sound and\\(\\delta _z-\\)zero-knowledge\\(\\mathsf {NIZK}\\)candidate can be turned into (roughly)\\(\\delta ^n_s-\\)sound and\\(1-(1-\\delta _{z})^n-\\)zero-knowledge candidate. Herenis the repetition parameter. MPC based Repetition:We propose a new transformation that amplifies zero-knowledge in the same way that parallel repetition amplifies soundness. We show that using this any\\(\\delta _s-\\)sound and\\(\\delta _z-\\)zero-knowledge\\(\\mathsf {NIZK}\\)candidate can be turned into (roughly)\\(1-(1-\\delta _s)^n-\\)sound and\\(2\\cdot \\delta ^n_{z}-\\)zero-knowledge candidate. Then we show that using these transformations in a zig-zag fashion we can obtain our result. Finally, we also present a simple transformation which directly turns any\\(\\mathsf {NIZK}\\)candidate satisfying\\(\\delta _s,\\delta _z<1/3 -1/\\mathsf {poly}(\\lambda )\\)to a secure one.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_21"}, {"primary_key": "2986309", "vector": [], "sparse_vector": [], "title": "Watermarking Public-Key Cryptographic Primitives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A software watermarking scheme enables users to embed a message or mark within a program while preserving its functionality. Moreover, it is difficult for an adversary to remove a watermark from a marked program without corrupting its behavior. Existing constructions of software watermarking from standard assumptions have focused exclusively on watermarking pseudorandom functions (PRFs). In this work, we study watermarking public-key primitives such as the signing key of a digital signature scheme or the decryption key of a public-key (predicate) encryption scheme. While watermarking public-key primitives might intuitively seem more challenging than watermarking PRFs, our constructions only rely on simple assumptions. Our watermarkable signature scheme can be built from the minimal assumption of one-way functions while our watermarkable public-key encryption scheme can be built from most standard algebraic assumptions that imply public-key encryption (e.g., factoring, discrete log, or lattice assumptions). Our schemes also satisfy a number of appealing properties: public marking, public mark-extraction, and collusion resistance. Our schemes are the first to simultaneously achieve all of these properties. The key enabler of our new constructions is a relaxed notion of functionality-preserving. While traditionally, we require that a marked program (approximately) preserve theinput/outputbehavior of the original program, in the public-key setting, preserving the “functionality” does not necessarily require preserving theexactinput/output behavior. For instance, if we want to mark a signing algorithm, it suffices that the marked algorithm still output valid signatures (even if those signatures might be different from the ones output by the unmarked algorithm). Similarly, if we want to mark a decryption algorithm, it suffices that the marked algorithm correctly decrypt all valid ciphertexts (but may behave differently from the unmarked algorithm on invalid or malformed ciphertexts). Our relaxed notion of functionality-preserving captures the essence of watermarking and still supports the traditional applications, but provides additional flexibility to enable new and simple realizations of this powerful cryptographic notion.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_12"}, {"primary_key": "2986310", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Unconditional MPC with Guaranteed Output Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of unconditionally secure MPC with guaranteed output delivery over point-to-point channels for corruption threshold\\(t < n/3\\). We ask the question: “is it possible to construct MPC in this setting s.t. the communication complexity per multiplication gate is linear in the number of parties?” While a number of works have focused on reducing the communication complexity in this setting, the answer to the above question has remained elusive for over a decade. We resolve the above question in the affirmative by providing an MPC with communication complexity\\(O(Cn\\kappa + n^3\\kappa )\\)where\\(\\kappa \\)is the size of an element in the field,Cis the size of the (arithmetic) circuit, and,nis the number of parties. This represents a strict improvement over the previously best known communication complexity of\\(O(Cn\\kappa +D_Mn^2\\kappa +n^3\\kappa )\\)where\\(D_M\\)is the multiplicative depth of the circuit. To obtain this result, we introduce a novel technique called4-consistent tuples of sharingswhich we believe to be of independent interest.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_4"}, {"primary_key": "2986311", "vector": [], "sparse_vector": [], "title": "Broadcast and Trace with Nε Ciphertext Size from Standard Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct abroadcast and tracescheme (also known astrace and revokeorbroadcast, trace and revoke) withNusers, where the ciphertext size can be made as low as\\(O(N^\\varepsilon )\\), for any arbitrarily small constant\\(\\varepsilon >0\\). This improves on the prior best construction of broadcast and trace under standard assumptions by <PERSON><PERSON> and <PERSON> (CCS ‘06), which had ciphertext size\\(O(N^{1/2})\\). While that construction relied on bilinear maps, ours uses a combination of the learning with errors (LWE) assumption and bilinear maps. Recall that, in bothbroadcast encryptionandtraitor-tracingschemes, there is a collection ofNusers, each of which gets a different secret key\\({\\mathsf {sk}}_i\\). In broadcast encryption, it is possible to create ciphertexts targeted to a subset\\(S \\subseteq [N]\\)of the users such that only those users can decrypt it correctly. In a traitor tracing scheme, if a subset of users gets together and creates a decoder boxDthat is capable of decrypting ciphertexts, then it is possible to trace at least one of the users responsible for creatingD. A broadcast and trace scheme intertwines the two properties, in a way that results in more than just their union. In particular, it ensures that if a decoder<PERSON>is able to decrypt ciphertexts targeted toward a setSof users, then it should be possible to trace one of the users in the setSresponsible for creatingD, even if other users outside ofSalso participated. As of recently, we have essentially optimal broadcast encryption (Boneh, Gentry, Waters CRYPTO ’05) under bilinear maps and traitor tracing (Goyal, Koppula, Waters STOC ’18) under LWE, where the ciphertext size is at most poly-logarithmic inN. The main contribution of our paper is to carefully combine LWE and bilinear-map based components, and get them to interact with each other, to achieve broadcast and trace.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_27"}, {"primary_key": "2986312", "vector": [], "sparse_vector": [], "title": "Synchronous, with a Chance of Partition Tolerance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> decide to write a paper together over the Internet and submit it to the prestigious CRYPTO’19 conference that has the most amazing PC. They encounter a few problems. First, not everyone is online every day: some are lazy and go skiing on Mondays; others cannot use git correctly and they are completely unaware that they are losing messages. Second, a small subset of the co-authors may be secretly plotting to disrupt the project (e.g., because they are writing a competing paper in stealth). Suppose that each day, sufficiently many honest co-authors are online (and use git correctly); moreover, suppose that messages checked into git on Monday can be correctly received by honest and online co-authors on Tuesday or any future day. Can the honest co-authors successfully finish the paper in a small number of days such that they make the CRYPTO deadline; and perhaps importantly, can all the honest co-authors, including even those who are lazy and those who sometimes use git incorrectly, agree on the final theorem?", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_18"}, {"primary_key": "2986313", "vector": [], "sparse_vector": [], "title": "On the Plausibility of Fully Homomorphic Encryption for RAMs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We initiate the study of fully homomorphic encryption for RAMs (RAM-FHE). This is a public-key encryption scheme where, given an encryption of a large databaseD, anybody can efficiently compute an encryption ofP(D) for an arbitrary RAM programP. The running time over the encrypted data should be as close as possible to the worst case running time ofP, which may be sub-linear in the data size. A central difficulty in constructing a RAM-FHE scheme is hiding the sequence of memory addresses accessed byP. This is particularly problematic because an adversary may homomorphically evaluate many programs over the same ciphertext, therefore effectively “rewinding” any mechanism for making memory accesses oblivious. We identify a necessary prerequisite towards constructing RAM-FHE that we callrewindable oblivious RAM(rewindable ORAM), which provides security even in this strong adversarial setting. We show how to construct rewindable ORAM usingsymmetric-key doubly efficient PIR (SK-DEPIR)(Canetti-<PERSON>-<PERSON>, Boyle-Ishai-Pass-Wootters: TCC ’17). We then show how to use rewindable ORAM, along with virtual black-box (VBB) obfuscation for specific circuits, to construct RAM-FHE. The latter primitive can be heuristically instantiated using existing indistinguishability obfuscation candidates. Overall, we obtain a RAM-FHE scheme where the multiplicative overhead in running time is polylogarithmic in the database sizeN. Our basic scheme is single-hop, but we also extend it to obtain multi-hop RAM-FHE with overhead\\(N^\\epsilon \\)for arbitrarily small\\(\\epsilon >0\\). We view our work as the first evidence that RAM-FHE is likely to exist.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_21"}, {"primary_key": "2986314", "vector": [], "sparse_vector": [], "title": "Tight Leakage-Resilient CCA-Security from Quasi-Adaptive Hash Proof System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose the concept of quasi-adaptive hash proof system (QAHPS), where the projection key is allowed to depend on the specific language for which hash values are computed. We formalize leakage-resilient(LR)-ardency for QAHPS by defining two statistical properties, including LR-\\(\\langle \\mathscr {L}_0, \\mathscr {L}_1 \\rangle \\)-universal and LR-\\(\\langle \\mathscr {L}_0, \\mathscr {L}_1 \\rangle \\)-key-switching. We provide a generic approach to tightly leakage-resilient CCA (LR-CCA) secure public-key encryption (PKE) from LR-ardent QAHPS. Our approach is reminiscent of the seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt’02), and employ three QAHPS schemes, one for generating a uniform string to hide the plaintext, and the other two for proving the well-formedness of the ciphertext. The LR-ardency of QAHPS makes possible the tight LR-CCA security. We give instantiations based on the standardk-Linear (k-LIN) assumptions over asymmetric and symmetric pairing groups, respectively, and obtain fully compact PKE with tight LR-CCA security. The security loss is\\({{O}}(\\log {Q_{{e}}})\\)where\\({Q_{{e}}}\\)denotes the number of encryption queries. Specifically, our tightly LR-CCA secure PKE instantiation from SXDH has only 4 group elements in the public key and 7 group elements in the ciphertext, thus is the most efficient one.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_15"}, {"primary_key": "2986315", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of OCB2: Attacks on Authenticity and Confidentiality.", "authors": ["<PERSON><PERSON><PERSON>", "Tetsu Iwata", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present practical attacks on OCB2. This mode of operation of a blockcipher was designed with the aim to provide particularly efficient and provably-secure authenticated encryption services, and since its proposal about 15 years ago it belongs to the top performers in this realm. OCB2 was included in an ISO standard in 2009. An internal building block of OCB2 is the tweakable blockcipher obtained by operating a regular blockcipher in\\( \\text {XEX} ^*\\)mode. The latter provides security only when evaluated in accordance with certain technical restrictions that, as we note, are not always respected by OCB2. This leads to devastating attacks against OCB2’s security promises: We develop a range of very practical attacks that, amongst others, demonstrate universal forgeries and full plaintext recovery. We complete our report with proposals for (provably) repairing OCB2. To our understanding, as a direct consequence of our findings, OCB2 is currently in a process of removal from ISO standards. Our attacks do not apply to OCB1 and OCB3, and our privacy attacks on OCB2 require an active adversary.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_1"}, {"primary_key": "2986316", "vector": [], "sparse_vector": [], "title": "Cryptographic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Is it possible to measure a physical object in a way that makes the measurement signals unintelligible to an external observer? Alternatively, can one learn a natural concept by using a contrived training set that makes the labeled examples useless without the line of thought that has led to their choice? We initiate a study of “cryptographic sensing” problems of this type, presenting definitions, positive and negative results, and directions for further research.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_19"}, {"primary_key": "2986317", "vector": [], "sparse_vector": [], "title": "Quantum Cryptanalysis in the RAM Model: Claw-Finding Attacks on SIKE.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce models of computation that enable direct comparisons between classical and quantum algorithms. Incorporating previous work on quantum computation and error correction, we justify the use of the gate-count and depth-times-width cost metrics for quantum circuits. We demonstrate the relevance of these models to cryptanalysis by revisiting, and increasing, the security estimates for the Supersingular Isogeny Diffie–<PERSON> (SIDH) and Supersingular Isogeny Key Encapsulation (SIKE) schemes. Our models, analyses, and physical justifications have applications to a number of memory intensive quantum algorithms.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_2"}, {"primary_key": "2986318", "vector": [], "sparse_vector": [], "title": "Non-interactive Non-malleability from Quantum Supremacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct non-interactive non-malleable commitments without setup in the plain model, under well-studied assumptions. First, we construct non-interactive non-malleable commitments w.r.t. commitment for\\(\\epsilon \\log \\log n\\)tags for a small constant\\(\\epsilon > 0\\), under the following assumptions: Sub-exponential hardness of factoring or discrete log. Quantum sub-exponential hardness of learning with errors (LWE). Second, as our key technical contribution, we introduce a new tag amplification technique. We show how to convert any non-interactive non-malleable commitment w.r.t. commitment for\\(\\epsilon \\log \\log n\\)tags (for any constant\\(\\epsilon >0\\)) into a non-interactive non-malleable commitment w.r.t. replacement for\\(2^n\\)tags. This part only assumes the existence of sub-exponentially secure non-interactive witness indistinguishable (NIWI) proofs, which can be based on sub-exponential security of the decisional linear assumption. Interestingly, for the tag amplification technique, we crucially rely on the leakage lemma due to <PERSON><PERSON> and <PERSON>ich<PERSON> (STOC 2011). For the construction of non-malleable commitments for\\(\\epsilon \\log \\log n\\)tags, we rely on quantum supremacy. This use of quantum supremacy in classical cryptography is novel, and we believe it will have future applications. We provide one such application to two-message witness indistinguishable (WI) arguments from (quantum) polynomial hardness assumptions.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_18"}, {"primary_key": "2986319", "vector": [], "sparse_vector": [], "title": "Exploring Constructions of Compact NIZKs from Various Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A non-interactive zero-knowledge (NIZK) protocol allows a prover to non-interactively convince a verifier of the truth of the statement without leaking any other information. In this study, we explore shorter NIZK proofs for all\\(\\mathbf{NP }\\)languages. Our primary interest is NIZK proofs from falsifiable pairing/pairing-free group-based assumptions. Thus far, NIZKs in the common reference string model (CRS-NIZKs) for\\(\\mathbf{NP }\\)based on falsifiable pairing-based assumptions all require a proof size at least as large as\\(O(|C| \\kappa )\\), whereCis a circuit computing the\\(\\mathbf{NP }\\)relation and\\(\\kappa \\)is the security parameter. This holds true even for the weaker designated-verifier NIZKs (DV-NIZKs). Notably, constructing a (CRS, DV)-NIZK with proof size achieving anadditive-overhead\\(O(|C|) + \\mathsf {poly}(\\kappa )\\), rather than a multiplicative-overhead\\(|C| \\cdot \\mathsf {poly}(\\kappa )\\), based on any falsifiable pairing-based assumptions is an open problem. In this work, we present various techniques for constructing NIZKs withcompactproofs, i.e., proofs smaller than\\(O(|C|) + \\mathsf {poly}(\\kappa )\\), and make progress regarding the above situation. Our result is summarized below. We construct CRS-NIZK for all\\(\\mathbf{NP }\\)with proof size\\(|C| +\\mathsf {poly}(\\kappa )\\)from a (non-static) falsifiable Diffie-Hellman (DH) type assumption over pairing groups. This is the first CRS-NIZK to achieve a compact proof without relying on either lattice-based assumptions or non-falsifiable assumptions. Moreover, a variant of our CRS-NIZK satisfies universal composability (UC) in the erasure-free adaptive setting. Although it is limited to\\(\\mathbf{NP }\\)relations in\\(\\mathbf{NC }^1\\), the proof size is\\(|w| \\cdot \\mathsf {poly}(\\kappa )\\)wherewis the witness, and in particular, it matches the state-of-the-art UC-NIZK proposed by Cohen, shelat, and Wichs (CRYPTO’19) based on lattices. We construct (multi-theorem) DV-NIZKs for\\(\\mathbf{NP }\\)with proof size\\(|C|+\\mathsf {poly}(\\kappa )\\)from the computational DH assumption overpairing-freegroups. This is the first DV-NIZK that achieves a compact proof from a standard DH type assumption. Moreover, if we further assume the\\(\\mathbf{NP }\\)relation to be computable in\\(\\mathbf{NC }^1\\)and assume hardness of a (non-static) falsifiable DH type assumption overpairing-freegroups, the proof size can be made as small as\\(|w| + \\mathsf {poly}(\\kappa )\\). Another related but independent issue is that all (CRS, DV)-NIZKs require the running time of the prover to be at least\\(|C|\\cdot \\mathsf {poly}(\\kappa )\\). Considering that there exists NIZKs with efficient verifiers whose running time is strictly smaller than |C|, it is an interesting problem whether we can constructprover-efficientNIZKs. To this end, we construct prover-efficient CRS-NIZKs for\\(\\mathbf{NP }\\)with compact proof through a generic construction using laconic functional evaluation schemes (Quach, Wee, and Wichs (FOCS’18)). This is the first NIZK in any model where the running time of the prover is strictly smaller than the time it takes to compute the circuitCcomputing the\\(\\mathbf{NP }\\)relation. Finally, perhaps of an independent interest, we formalize the notion ofhomomorphic equivocal commitments, which we use as building blocks to obtain the first result, and show how to construct them from pairing-based assumptions.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_21"}, {"primary_key": "2986320", "vector": [], "sparse_vector": [], "title": "Watermarking PRFs from Lattices: Stronger Security via Extractable PRFs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A software watermarking scheme enables one to embed a “mark” (i.e., a message) within a program while preserving the program’s functionality. Moreover, there is an extraction algorithm that recovers an embedded message from a program. The main security goal is that it should be difficult to remove the watermark without destroying the functionality of the program. Existing constructions of watermarking focus on watermarking cryptographic functions like pseudorandom functions (PRFs); even in this setting, realizing watermarking from standard assumptions remains difficult. The first lattice-based construction of secret-key watermarking due to <PERSON> and <PERSON> (CRYPTO 2017) only ensures mark-unremovability against an adversary who does not have access to the mark-extraction oracle. The construction of <PERSON><PERSON><PERSON> et al. (TCC 2018) achieves the stronger notion of mark-unremovability even if the adversary can make extraction queries, but has the drawback that the watermarking authority (who holds the watermarking secret key) can break pseudorandomness of all PRF keys in the family (includingunmarkedkeys). In this work, we construct new lattice-based secret-key watermarking schemes for PRFs that both provide unremovability against adversaries that have access to the mark-extraction oracle and offer a strong and meaningful notion of pseudorandomness even against the watermarking authority (i.e., the outputs of unmarked keys are pseudorandom almost everywhere). Moreover, security of several of our schemes can be based on the hardness of computingnearly polynomialapproximations to worst-case lattice problems. This is a qualitatively weaker assumption than that needed for existing lattice-based constructions of watermarking (that support message-embedding), all of which require quasi-polynomial approximation factors. Our constructions rely on a new cryptographic primitive called anextractable PRF, which may be of independent interest.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_11"}, {"primary_key": "2986321", "vector": [], "sparse_vector": [], "title": "CCA Security and Trapdoor Functions via Key-Dependent-Message Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the relationship among public-key encryption (PKE) satisfying indistinguishability against chosen plaintext attacks (IND-CPA security), that against chosen ciphertext attacks (IND-CCA security), and trapdoor functions (TDF). Specifically, we aim at finding a unified approach and some additional requirement to realize IND-CCA secure PKE and TDF based on IND-CPA secure PKE, and show the following two main results. As the first main result, we show how to achieve IND-CCA security via a weak form of key-dependent-message (KDM) security. More specifically, we construct an IND-CCA secure PKE scheme based on an IND-CPA secure PKE scheme and a secret-key encryption (SKE) scheme satisfying one-time KDM security with respect to projection functions (projection-KDM security). Projection functions are very simple functions with respect to which KDM security has been widely studied. Since the existence of projection-KDM secure PKE implies that of the above two building blocks, as a corollary of this result, we see that the existence of IND-CCA secure PKE is implied by that of projection-KDM secure PKE. As the second main result, we extend the above construction of IND-CCA secure PKE into that of TDF by additionally requiring a mild requirement for each building block. Our TDF satisfies adaptive one-wayness. We can instantiate our TDF based on a wide variety of computational assumptions. Especially, we obtain the first TDF (with adaptive one-wayness) based on the sub-exponential hardness of the constant-noise learning-parity-with-noise (LPN) problem.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_2"}, {"primary_key": "2986322", "vector": [], "sparse_vector": [], "title": "Adaptively Secure and Succinct Functional Encryption: Improving Security and Efficiency, Simultaneously.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Functional encryption (FE) is advanced encryption that enables us to issue functional decryption keys where functions are hardwired. When we decrypt a ciphertext of a messagemby a functional decryption key where a functionfis hardwired, we can obtainf(m) and nothing else. We say FE is selectively or adaptively secure when target messages are chosen at the beginning or after function queries are sent, respectively. In the weakly-selective setting, function queries are also chosen at the beginning. We say FE is single-key/collusion-resistant when it is secure against adversaries that are given only-one/polynomially-many functional decryption keys, respectively. We say FE is sublinearly-succinct/succinct when the running time of an encryption algorithm is sublinear/poly-logarithmic in the function description size, respectively. In this study, we propose a generic transformation from weakly-selectively secure, single-key, and sublinearly-succinct (we call “building block”) PKFE for circuits into adaptively secure, collusion-resistant, and succinct (we call “fully-equipped”) one for circuits. Our transformation relies onneitherconcrete assumptions such as learning with errorsnorindistinguishability obfuscation (IO). This is the first generic construction of fully-equipped PKFE that does not rely on IO. As side-benefits of our results, we obtain the following primitives from the building block PKFE for circuits: (1) laconic oblivious transfer (2) succinct garbling scheme for Turing machines (3) selectively secure, collusion-resistant, and succinct PKFE for Turing machines (4) low-overhead adaptively secure traitor tracing (5) key-dependent message secure and leakage-resilient public-key encryption. We also obtain a generic transformation from simulation-based adaptively secure garbling schemes that satisfy a natural decomposability property into adaptively indistinguishable garbling schemes whose online complexity does not depend on the output length.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_17"}, {"primary_key": "2986323", "vector": [], "sparse_vector": [], "title": "Realizing Chosen Ciphertext Security Generically in Attribute-Based Encryption and Predicate Encryption.", "authors": ["Venkata Koppula", "<PERSON>"], "summary": "We provide generic and black box transformations from any chosen plaintext secure Attribute-Based Encryption (ABE) or One-sided Predicate Encryption system into a chosen ciphertext secure system. Our transformation requires only the IND-CPA security of the original ABE scheme coupled with a pseudorandom generator (PRG) with a special security property. In particular, we consider a PRG with annbit input\\(s \\in \\{0,1\\}^n\\)and\\(n \\cdot \\ell \\)bit output\\(y_1, \\ldots , y_n\\)where each\\(y_i\\)is an\\(\\ell \\)bit string. Then for a randomly chosensthe following two distributions should be computationally indistinguishable. In the first distribution\\(r_{s_i, i} = y_i\\)and\\(r_{\\bar{s}_i, i}\\)is chosen randomly for\\(i \\in [n]\\). In the second distribution all\\(r_{b, i}\\)are chosen randomly for\\(i \\in [n], b \\in \\{0,1\\}\\). We show that such PRGs can be built from either the computational <PERSON><PERSON><PERSON><PERSON> assumption (in non-bilinear groups) or the Learning with Errors (LWE) assumption (and potentially other assumptions). Thus, one can transform any IND-CPA secure system into a chosen ciphertext secure one by adding either assumption. (Or by simply assuming an existing PRG is hinting secure.) In addition, our work provides a new approach and perspective for obtaining chosen ciphertext security in the basic case of public key encryption.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_23"}, {"primary_key": "2986324", "vector": [], "sparse_vector": [], "title": "Public-Key Cryptography in the Fine-Grained Setting.", "authors": ["Rio LaVigne", "<PERSON>", "Virginia Vassilevska Williams"], "summary": "Cryptography is largely based on unproven assumptions, which, while believable, might fail. Notably if\\(P = NP\\), or if we live in Pessiland, then all current cryptographic assumptions will be broken. A compelling question is if any interesting cryptography might exist in Pessiland. A natural approach to tackle this question is to base cryptography on an assumption from fine-grained complexity. <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> [BRSV’17] attempted this, starting from popular hardness assumptions, such as the Orthogonal Vectors (OV) Conjecture. They obtained problems that are hard on average, assuming that OV and other problems are hard in the worst case. They obtained proofs of work, and hoped to use their average-case hard problems to build a fine-grained one-way function. Unfortunately, they proved that constructing one using their approach would violate a popular hardness hypothesis. This motivates the search for other fine-grained average-case hard problems. The main goal of this paper is to identify sufficient properties for a fine-grained average-case assumption that imply cryptographic primitives such as fine-grained public key cryptography (PKC). Our main contribution is a novel construction of a cryptographic key exchange, together with the definition of a small number of relatively weak structural properties, such that if a computational problem satisfies them, our key exchange has provable fine-grained security guarantees, based on the hardness of this problem. We then show that a natural and plausible average-case assumption for the key problem Zero-k-Clique from fine-grained complexity satisfies our properties. We also develop fine-grained one-way functions and hardcore bits even under these weaker assumptions. Where previous works had to assume random oracles or the existence of strong one-way functions to get a key-exchange computable inO(n) time secure against\\(O(n^2)\\)adversaries (see [Merkle’78] and [BGI’08]), our assumptions seem much weaker. Our key exchange has a similar gap between the computation of the honest party and the adversary as prior work, while being non-interactive, implying fine-grained PKC.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_20"}, {"primary_key": "2986325", "vector": [], "sparse_vector": [], "title": "Subvector Commitments with Application to Succinct Arguments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We put forward the notion of subvector commitments (SVC): An SVC allows one to open a committed vector at a set of positions, where the opening size is independent of length of the committed vector and the number of positions to be opened. We propose two constructions under variants of the root assumption and the CDH assumption, respectively. We further generalize SVC to a notion called linear map commitments (LMC), which allows one to open a committed vector to its images under linear maps with a single short message, and propose a construction over pairing groups. Equipped with these newly developed tools, we revisit the “CS proofs” paradigm [Micali, FOCS 1994] which turns any arguments with public-coin verifiers into non-interactive arguments using the Fiat-Shamir transform in the random oracle model. We propose a compiler that turns any (linear, resp.) PCP into a non-interactive argument, using exclusively SVCs (LMCs, resp.). For an approximate 80 bits of soundness, we highlight the following new implications: There exists a succinct non-interactive argument of knowledge (SNARK) with public-coin setup with proofs of size 5360 bits, under the adaptive root assumption over class groups of imaginary quadratic orders against adversaries with runtime\\(2^{128}\\). At the time of writing, this is the shortest SNARK with public-coin setup. There exists a non-interactive argument with private-coin setup, where proofs consist of 2 group elements and 3 field elements, in the generic bilinear group model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_19"}, {"primary_key": "2986326", "vector": [], "sparse_vector": [], "title": "Low-Memory Attacks Against Two-Round Even-Mansour Using the 3-XOR Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The iterated Even-Mansour construction is an elegant construction that idealizes block cipher designs such as the AES. In this work we focus on the simplest variant, the 2-round Even-Mansour construction with a single key. This is the most minimal construction that offers security beyond the birthday bound: there is a security proof up to\\(2^{2n/3}\\)evaluations of the underlying permutations and encryption, and the best known attacks have a complexity of roughly\\(2^n/n\\)operations. We show that attacking this scheme with block sizenis related to the 3-XOR problem with element size\\(\\ell = 2n\\), an important algorithmic problem that has been studied since the nineties. In particular the 3-XOR problem is known to require at least\\(2^{\\ell /3}\\)queries, and the best known algorithms require around\\(2^{\\ell /2}/\\ell \\)operations: this roughly matches the known bounds for the 2-round Even-Mansour scheme. Using this link we describe new attacks against the 2-round Even-Mansour scheme. In particular, we obtain the first algorithms where both the data and the memory complexity are significantly lower than\\(2^{n}\\). From a practical standpoint, previous works with a data and/or memory complexity close to\\(2^n\\)are unlikely to be more efficient than a simple brute-force search over the key. Our best algorithm requires just\\(\\lambda n\\)known plaintext/ciphertext pairs, for some constant\\(0< \\lambda < 1\\),\\(2^n/\\lambda n\\)time, and\\(2^{\\lambda n}\\)memory. For instance, with\\(n=64\\)and\\(\\lambda = 1/2\\), the memory requirement is practical, and we gain a factor 32 over brute-force search. We also describe an algorithm with asymptotic complexity\\(\\mathcal {O}(2^{n} \\ln ^2 n/n^2)\\), improving the previous asymptotic complexity of\\(\\mathcal {O}(2^n/n)\\), using a variant of the 3-SUM algorithm of Baran, Demaine, and Pǎtraşcu.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_8"}, {"primary_key": "2986327", "vector": [], "sparse_vector": [], "title": "Efficient Collision Attack Frameworks for RIPEMD-160.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RIPEMD-160 is an ISO/IEC standard and has been applied to generate the Bitcoin address with SHA-256. Due to the complex dual-stream structure, the first collision attack on reduced RIPEMD-160 presented by <PERSON>, <PERSON> and <PERSON> at Asiacrypt 2017 only reaches 30 steps, having a time complexity of\\(2^{70}\\). Apart from that, several semi-free-start collision attacks have been published for reduced RIPEMD-160 with the start-from-the-middle method. Inspired from such start-from-the middle structures, we propose two novel efficient collision attack frameworks for reduced RIPEMD-160 by making full use of the weakness of its message expansion. Those two frameworks are called dense-left-and-sparse-right (DLSR) framework and sparse-left-and-dense-right (SLDR) framework. As it turns out, the DLSR framework is more efficient than SLDR framework since one more step can be fully controlled, though with extra\\(2^{32}\\)memory complexity. To construct the best differential characteristics for the DLSR framework, we carefully build the linearized part of the characteristics and then solve the corresponding nonlinear part using a guess-and-determine approach. Based on the newly discovered differential characteristics, we provide colliding messages pairs for the first practical collision attacks on 30 and 31 (out of 80) steps of RIPEMD-160 with time complexity\\(2^{35.9}\\)and\\(2^{41.5}\\)respectively. In addition, benefiting from the partial calculation, we can attack 33 and 34 (out of 80) steps of RIPEMD-160 with time complexity\\(2^{67.1}\\)and\\(2^{74.3}\\)respectively. When applying the SLDR framework to the differential characteristic used in the Asiacrypt 2017 paper, we significantly improve the time complexity by a factor of\\(2^{13}\\). However, it still cannot compete with the results obtained from the DLSR framework. To the best of our knowledge, these are the best collision attacks on reduced RIPEMD-160 with respect to the number of steps, including the first colliding message pairs for 30 and 31 steps of RIPEMD-160.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_5"}, {"primary_key": "2986328", "vector": [], "sparse_vector": [], "title": "Revisiting Post-quantum Fiat-Shamir.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Fiat-Shamir transformation is a useful approach to building non-interactive arguments (of knowledge) in the random oracle model. Unfortunately, existing proof techniques are incapable of proving the security of Fiat-Shamir in thequantumsetting. The problem stems from (1) the difficulty of quantum rewinding, and (2) the inability of current techniques to adaptively program random oracles in the quantum setting. In this work, we show how to overcome the limitations above in many settings. In particular, we give mild conditions under which Fiat-Shamir is secure in the quantum setting. As an application, we show that existing lattice signatures based on Fiat-Shamir are secure without any modifications.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_12"}, {"primary_key": "2986329", "vector": [], "sparse_vector": [], "title": "New Constructions of Reusable Designated-Verifier NIZKs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Non-interactive zero-knowledge arguments (NIZKs) for\\(\\mathsf {NP}\\)are an important cryptographic primitive, but we currently only have instantiations under a few specific assumptions. Notably, we are missing constructions from the learning with errors (LWE) assumption, the Di<PERSON><PERSON><PERSON><PERSON><PERSON> (CDH/DDH) assumption, and the learning parity with noise (LPN) assumption. In this paper, we study a relaxation of NIZKs to thedesignated-verifiersetting (DV-NIZK), where a trusted setup generates a common reference string together with a secret key for the verifier. We wantreusableschemes, which allow the verifier to reuse the secret key to verify many different proofs, and soundness should hold even if the malicious prover learns whether various proofs are accepted or rejected. Such reusable DV-NIZKs were recently constructed under the CDH assumption, but it was open whether they can also be constructed under LWE or LPN. We also consider an extension of reusable DV-NIZKs to themalicious designated-verifiersetting (MDV-NIZK). In this setting, the only trusted setup consists of a common random string. However, there is also an additional untrusted setup in which the verifier chooses a public/secret key needed to generate/verify proofs, respectively. We require that zero-knowledge holds even if the public key is chosen maliciously by the verifier. Such reusable MDV-NIZKs were recently constructed under the “one-more CDH” assumption, but constructions under CDH/LWE/LPN remained open. In this work, we give new constructions of (reusable) DV-NIZKs and MDV-NIZKs using generic primitives that can be instantiated under CDH, LWE, or LPN.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_22"}, {"primary_key": "2986330", "vector": [], "sparse_vector": [], "title": "Homomorphic Time-Lock Puzzles and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "Time-lock puzzles allow one to encrypt messages for the future, by efficiently generating a puzzle with a solutionsthat remains hidden until time\\(\\mathcal {T}\\)has elapsed. The solution is required to be concealed from the eyes of any algorithm running in (parallel) time less than\\(\\mathcal {T}\\). We put forth the concept ofhomomorphic time-lock puzzles, where one can evaluate functions over puzzles without solving them, i.e., one can manipulate a set of puzzles with solutions\\((s_1, \\dots , s_n)\\)to obtain a puzzle that solves to\\(f(s_1, \\ldots , s_n)\\), for any functionf. We propose candidate constructions under concrete cryptographic assumptions for different classes of functions. Then we show how homomorphic time-lock puzzles overcome the limitations of classical time-lock puzzles by proposing new protocols for applications of interest, such as e-voting, multi-party coin flipping, and fair contract signing.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_22"}, {"primary_key": "2986331", "vector": [], "sparse_vector": [], "title": "Simple Proofs of Space-Time and Rational Proofs of Storage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new cryptographic primitive: Proofs of Space-Time (PoSTs) and construct an extremely simple, practical protocol for implementing these proofs. A PoST allows a prover to convince a verifier that she spent a “space-time” resource (storing data—space—over a period of time). Formally, we define the PoST resource as a trade-off between CPU work and space-time (under reasonable cost assumptions, a rational user will prefer to use the lower-cost space-time resource over CPU work). Compared to a proof-of-work, a PoST requires less energy use, as the “difficulty” can be increased by extending the time period over which data is stored without increasing computation costs. Our definition is very similar to “Proofs of Space” [ePrint 2013/796, 2013/805] but, unlike the previous definitions, takes into account amortization attacks and storage duration. Moreover, our protocol uses a very different (and much simpler) technique, making use of the fact that we explicitly allow a space-time tradeoff, and doesn’t require any non-standard assumptions (beyond random oracles). Unlike previous constructions, our protocol allows incremental difficulty adjustment, which can gracefully handle increases in the price of storage compared to CPU work. In addition, we show how, in a crypto-currency context, the parameters of the scheme can be adjusted using a market-based mechanism, similar in spirit to the difficulty adjustment for PoW protocols.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_14"}, {"primary_key": "2986332", "vector": [], "sparse_vector": [], "title": "It Wasn&apos;t Me! - Repudiability and Claimability of Ring Signatures.", "authors": ["Sunoo Park", "<PERSON>"], "summary": "Ring signatures, introduced by [RST01], are a variant of digital signatures which certify thatone among a particular setof parties has endorsed a message while hidingwhichparty in the set was the signer. Ring signatures are designed to allow<PERSON><PERSON><PERSON> attach anyone else’s name to a signature, as long as the signer’s own name is also attached. But what guarantee do ring signatures provide if a purported signatory wishes to denounce a signed message—or alternatively, if a signatory wishes to later come forward and claim ownership of a signature? Prior security definitions for ring signatures do not give a conclusive answer to this question: under most existing definitions, the guarantees could go either way. That is, it is consistent with some standard definitions that a non-signer might be able torepudiatea signature that he did not produce, or that this might be impossible. Similarly, a signer might be able to later convincingly claim that a signature he produced is indeed his own, or not. Any of these guarantees might be desirable. For instance, a whistleblower might have reason to want to later claim an anonymously released signature, or a person falsely implicated in a crime associated with a ring signature might wish to denounce the signature that is framing them and damaging their reputation. In other circumstances, it might be desirable that even under duress, a member of a ring cannot produce proof that he did or did not sign a particular signature. In any case, aguaranteeone way or the other seems highly desirable. In this work, we formalize definitions and give constructions of the new notions ofrepudiable,unrepudiable,claimable, andunclaimablering signatures. Our repudiable construction is based on VRFs, which are implied by several number-theoretic assumptions (including strong RSA or bilinear maps); our claimable construction is a black-box transformation from any standard ring signature scheme to a claimable one; and our unclaimable construction is derived from the lattice-based ring signatures of [BK10], which rely on hardness of SIS. Our repudiable construction also provides a new construction of standard ring signatures.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_6"}, {"primary_key": "2986333", "vector": [], "sparse_vector": [], "title": "Security in the Presence of Key Reuse: Context-Separable Interfaces and Their Applications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Key separation is often difficult to enforce in practice. While key reuse can be catastrophic for security, we know of a number of cryptographic schemes for which it is provably safe. But existing formal models, such as the notions of joint security (Haber-Pinkas, CCS ’01) and agility (<PERSON><PERSON> et al., EUROCRYPT ’10), do not address the full range of key-reuse attacks—in particular, those that break the abstraction of the scheme, or exploit protocol interactions at a higher level of abstraction. This work attends to these vectors by focusing on two key elements: thegamethat codifies the scheme under attack, as well as its intended adversarial model; and the underlyinginterfacethat exposes secret key operations for use by the game. Our main security experiment considers the implications of using an interface (in practice, the API of a software library or a hardware platform such as TPM) to realize the scheme specified by the game when the interface is shared with other unspecified, insecure, or even malicious applications. After building up a definitional framework, we apply it to the analysis of two real-world schemes: the EdDSA signature algorithm and the Noise protocol framework. Both provide some degree ofcontext separability, a design pattern for interfaces and their applications that aids in the deployment of secure protocols.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_26"}, {"primary_key": "2986334", "vector": [], "sparse_vector": [], "title": "Noninteractive Zero Knowledge for NP from (Plain) Learning with Errors.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We finally close the long-standing problem of constructing a noninteractive zero-knowledge (NIZK) proof system for any NP language with security based on theplain Learning With Errors(LWE) problem, and thereby on worst-case lattice problems. Our proof system instantiates the framework recently developed by <PERSON><PERSON>et al.[EUROCRYPT’18], <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS’18], and <PERSON><PERSON>et al.[STOC’19] for soundly applying the Fiat–Shamir transform using a hash function family that iscorrelation intractablefor a suitable class of relations. Previously, such hash families were based either on “exotic” assumptions (e.g., indistinguishability obfuscation or optimal hardness of certain LWE variants) or, more recently, on the existence of circularly secure fully homomorphic encryption (FHE). However, none of these assumptions are known to be implied by plain LWE or worst-case hardness. Our main technical contribution is a hash family that is correlation intractable for arbitrary size-Scircuits, for any polynomially boundedS, based on plain LWE (with small polynomial approximation factors). The construction combines two novel ingredients: a correlation-intractable hash family forlog-depthcircuits based on LWE (or even the potentially harder Short Integer Solution problem), and a “bootstrapping” transform that uses (leveled) FHE to promote correlation intractability for the FHE decryption circuit toarbitrary(bounded) circuits. Our construction can be instantiated in two possible “modes,” yielding a NIZK that is eithercomputationallysound andstatisticallyzero knowledge in the commonrandomstring model, or vice-versa in the commonreferencestring model.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_4"}, {"primary_key": "2986335", "vector": [], "sparse_vector": [], "title": "SpOT-Light: Lightweight Private Set Intersection from Sparse OT Extension.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe a novel approach for two-party private set intersection (PSI) with semi-honest security. Compared to existing PSI protocols, ours has a more favorable balance between communication and computation. Specifically, our protocol has thelowest monetary costof any known PSI protocol, when run over the Internet using cloud-based computing services (taking into account current rates for CPU + data). On slow networks (e.g., 10 Mbps) our protocol is actually thefastest. Our novel underlying technique is a variant of oblivious transfer (OT) extension that we callsparse OT extension.Conceptually it can be thought of as a communication-efficient multipoint oblivious PRF evaluation. Our sparse OT technique relies heavily on manipulating high-degree polynomials over large finite fields (i.e. elements whose representation requires hundreds of bits). We introduce extensive algorithmic and engineering improvements for interpolation and multi-point evaluation of such polynomials, which we believe will be of independent interest. Finally, we present an extensive empirical comparison of state-of-the-art PSI protocols in several application scenarios and along several dimensions of measurement: running time, communication, peak memory consumption, and—arguably the most relevant metric for practice—monetary cost.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_13"}, {"primary_key": "2986336", "vector": [], "sparse_vector": [], "title": "Unifying Leakage Models on a Rényi Day.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the last decade, several works have focused on finding the best way to model the leakage in order to obtain provably secure implementations. One of the most realistic models is the noisy leakage model, introduced in [PR13,DDF14] together with secure constructions. These works suffer from various limitations, in particular the use of ideal leak-free gates in [PR13] and an important loss (in the size of the field) in the reduction in [DDF14]. In this work, we provide new strategies to prove the security of masked implementations and start by unifying the different noisiness metrics used in prior works by relating all of them to a standard notion in information theory: the pointwise mutual information. Based on this new interpretation, we define two new natural metrics and analyze the security of known compilers with respect to these metrics. In particular, we prove (1) a tighter bound for reducing the noisy leakage models to the probing model using our first new metric, (2) better bounds for amplification-based security proofs using the second metric. To support that the improvements we obtain are not only a consequence of the use of alternative metrics, we show that for concrete representation of leakage (e.g.,“Hamming weight + Gaussian noise”), our approach significantly improves the parameters compared to prior works. Finally, using the Rényi divergence, we quantify concretely the advantage of an adversary in attacking a block cipher depending on the number of leakage acquisitions available to it.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_24"}, {"primary_key": "2986337", "vector": [], "sparse_vector": [], "title": "Correlation of Quadratic Boolean Functions: Cryptanalysis of All Versions of Full \\mathsf MORUS.", "authors": ["<PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that the correlation of any quadratic Boolean function can be read out from its so-calleddisjoint quadratic form. We further propose a polynomial-time algorithm that can transform an arbitrary quadratic Boolean function into its disjoint quadratic form. With this algorithm, the exact correlation of quadratic Boolean functions can be computed efficiently. We apply this method to analyze the linear trails of\\(\\mathsf {MORUS}\\)(one of the seven finalists of the CAESAR competition), which are found with the help of a generic model for linear trails of\\(\\mathsf {MORUS}\\)-like key-stream generators. In our model, any tool for finding linear trails of block ciphers can be used to search for trails of\\(\\mathsf {MORUS}\\)-like key-stream generators. As a result, a set of trails with correlation\\(2^{-38}\\)is identified for all versions of full\\(\\mathsf {MORUS}\\), while the correlations of previously published best trails for\\(\\mathsf {MORUS}\\)-640 and\\(\\mathsf {MORUS}\\)-1280 are\\(2^{-73}\\)and\\(2^{-76}\\)respectively (ASIACRYPT 2018). This significantly improves the complexity of the attack on\\(\\mathsf {MORUS}\\)-1280-256 from\\(2^{152}\\)to\\(2^{76}\\). These new trails also lead to the first distinguishing and message-recovery attacks on\\(\\mathsf {MORUS}\\)-640-128 and\\(\\mathsf {MORUS}\\)-1280-128 with surprisingly low complexities around\\(2^{76}\\). Moreover, we observe that the condition for exploiting these trails in an attack can be more relaxed than previously thought, which shows that the new trails are superior to previously published ones in terms of both correlation and the number of ciphertext blocks involved.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_7"}, {"primary_key": "2986338", "vector": [], "sparse_vector": [], "title": "Leakage Resilient Secret Sharing and Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A secret sharing scheme allows a dealer to share a secret among a set ofnparties such that any authorized subset of the parties can recover the secret, while any unauthorized subset learns no information about the secret. Aleakage-resilientsecret sharing scheme (introduced in independent works by <PERSON><PERSON> and <PERSON>, STOC ’18 and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, CRYPTO ’18) additionally requires the secrecy to hold against every unauthorized set of parties even if they obtain some bounded leakage from every other share. The leakage is said to belocalif it is computed independently for each share. So far, the only known constructions of local leakage resilient secret sharing schemes are for threshold access structures for very low (O(1)) or very high (\\(n -o(\\log n)\\)) thresholds. In this work, we give a compiler that takes a secret sharing scheme for any monotone access structure and produces a local leakage resilient secret sharing scheme for the same access structure, with only a constant-factor asymptotic blow-up in the sizes of the shares. Furthermore, the resultant secret sharing scheme has optimal leakage-resilience rate, i.e., the ratio between the leakage tolerated and the size of each share can be made arbitrarily close to 1. Using this secret sharing scheme as the main building block, we obtain the following results: Rate Preserving Non-Malleable Secret Sharing.We give a compiler that takes any secret sharing scheme for a 4-monotone access structure (A 4-monotone access structure has the property that any authorized set has size at least 4.) with rateRand converts it into a non-malleable secret sharing scheme for the same access structure with rate\\(\\varOmega (R)\\). The previous such non-zero rate construction (Badrinarayanan and Srinivasan, EUROCRYPT ’19) achieved a rate of\\(\\varTheta (R/{t_{\\max }\\log ^2 n})\\), where\\(t_{\\max }\\)is the maximum size of any minimal set in the access structure. As a special case, for any threshold\\(t \\ge 4\\)and an arbitrary\\(n \\ge t\\), we get the first constant-rate construction oft-out-of-nnon-malleable secret sharing. Leakage-Tolerant Multiparty Computation for General Interaction Patterns.For any functionf, we give a reduction from constructing a leakage-tolerant secure multi-party computation protocol for computingfthat obeys any given interaction pattern to constructing a secure (but not necessarily leakage-tolerant) protocol for a related function that obeys the star interaction pattern. Together with the known results for the star interaction pattern, this gives leakage tolerant MPC for any interaction pattern with statistical/computational security. This improves upon the result of (Halevi et al., ITCS 2016), who presented such a reduction in a leak-free environment.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_17"}, {"primary_key": "2986339", "vector": [], "sparse_vector": [], "title": "Fully Secure Attribute-Based Encryption for t-CNF from LWE.", "authors": ["Rotem Tsabary"], "summary": "Attribute-based Encryption (ABE), first introduced by [SW05,GPSW06], is a public key encryption system that can support multiple users with varying decryption permissions. One of the main properties of such schemes is the supported function class of policies. While there are fully secure constructions from bilinear maps for a fairly large class of policies, the situation with lattice-based constructions is less satisfactory and many efforts were made to close this gap. Prior to this work the only known fully secure lattice construction was for the class of point functions (also known as IBE). In this work we construct for the first time a lattice-based (ciphertext-policy) ABE scheme for the function classt-CNF, which consists of CNF formulas where each clause depends on at mosttbits of the input, for any constantt. This class includes NP-verification policies, bit-fixing policies andt-threshold policies. Towards this goal we also construct a fully secure single-key constrained PRF from OWF for the same function class, which might be of independent interest.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_3"}, {"primary_key": "2986340", "vector": [], "sparse_vector": [], "title": "Asymmetric Message Franking: Content Moderation for Metadata-Private End-to-End Encryption.", "authors": ["Nirvan Tyagi", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Content moderation is crucial for stopping abusive and harassing messages in online platforms. Existing moderation mechanisms, such as message franking, require platform providers to be able to associate user identifiers to encrypted messages. These mechanisms fail inmetadata-privatemessaging systems, such as Signal, where users can hide their identities from platform providers. The key technical challenge preventing moderation is achieving cryptographic accountability while preserving deniability. In this work, we resolve this tension with a new cryptographic primitive:asymmetric message franking(AMF) schemes. We define strong security notions for AMF schemes, including the first formal treatment of deniability in moderation settings. We then construct, analyze, and implement an AMF scheme that is fast enough to use for content moderation of metadata-private messaging.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_8"}, {"primary_key": "2986341", "vector": [], "sparse_vector": [], "title": "Libra: Succinct Zero-Knowledge Proofs with Optimal Prover Computation.", "authors": ["Tiancheng Xie", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We presentLibra, the first zero-knowledge proof system that has both optimal prover time and succinct proof size/verification time. In particular, ifCis the size of the circuit being proved (i) the prover time isO(C) irrespective of the circuit type; (ii) the proof size and verification time are both\\(O(d\\log C)\\)ford-depth log-space uniform circuits (such as RAM programs). In additionLibrafeatures an one-time trusted setup that depends only on the size of the input to the circuit and not on the circuit logic. UnderlyingLibrais a new linear-time algorithm for the prover of the interactive proof protocol by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (also known as GKR protocol), as well as an efficient approach to turn the GKR protocol to zero-knowledge using small masking polynomials. Not only doesLibrahave excellent asymptotics, but it is also efficient in practice. For example, our implementation shows that it takes 200 s to generate a proof for constructing a SHA2-based Merkle tree root on 256 leaves, outperforming all existing zero-knowledge proof systems. Proof size and verification time ofLibraare also competitive.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26954-8_24"}, {"primary_key": "2986342", "vector": [], "sparse_vector": [], "title": "New Results on Modular Inversion Hidden Number Problem and Inversive Congruential Generator.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Modular Inversion Hidden Number Problem (MIHNP), introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> in Asiacrypt 2001, is briefly described as follows: Let\\({\\mathrm {MSB}}_{\\delta }(z)\\)refer to the\\(\\delta \\)most significant bits ofz. Given many samples\\(\\left( t_{i}, {\\mathrm {MSB}}_{\\delta }((\\alpha + t_{i})^{-1} \\bmod {p})\\right) \\)for random\\(t_i \\in \\mathbb {Z}_p\\), the goal is to recover the hidden number\\(\\alpha \\in \\mathbb {Z}_p\\). MIHNP is an important class of Hidden Number Problem. In this paper, we revisit the Coppersmith technique for solving a class of modular polynomial equations, which is respectively derived from the recovering problem of the hidden number\\(\\alpha \\)in MIHNP. For any positive integer constantd, let integer\\(n=d^{3+o(1)}\\). Given a sufficiently large modulusp,\\(n+1\\)samples of MIHNP, we present a heuristic algorithm to recover the hidden number\\(\\alpha \\)with a probability close to 1 when\\(\\delta /\\log _2 p>\\frac{1}{d\\,+\\,1}+o(\\frac{1}{d})\\). The overall time complexity of attack is polynomial in\\(\\log _2 p\\), where the complexity of the LLL algorithm grows as\\(d^{\\mathcal {O}(d)}\\)and the complexity of the Gröbner basis computation grows as\\((2d)^{\\mathcal {O}(n^2)}\\). When\\(d> 2\\), this asymptotic bound outperforms\\(\\delta /\\log _2 p>\\frac{1}{3}\\)which is the asymptotic bound proposed by Boneh, Halevi and Howgrave-Graham in Asiacrypt 2001. It is the first time that a better bound for solving MIHNP is given, which implies that the conjecture that MIHNP is hard whenever\\(\\delta /\\log _2 p<\\frac{1}{3}\\)is broken. Moreover, we also get the best result for attacking the Inversive Congruential Generator (ICG) up to now.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_11"}, {"primary_key": "2986343", "vector": [], "sparse_vector": [], "title": "Efficient Lattice-Based Zero-Knowledge Arguments with Standard Soundness: Construction and Applications.", "authors": ["Rupeng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide new zero-knowledge argument of knowledge systems that work directly for a wide class of language, namely, ones involving the satisfiability of matrix-vector relations and integer relations commonly found in constructions of lattice-based cryptography. Prior to this work, practical arguments for lattice-based relations either have a constant soundness error\\((2/3)\\), or consider a weaker form of soundness, namely, extraction only guarantees that the prover is in possession of a witness that “approximates” the actual witness. Our systems do not suffer from these limitations. The core of our new argument systems is an efficient zero-knowledge argument of knowledge of a solution to a system of linear equations, where variables of this solution satisfy a set of quadratic constraints. This argument enjoys standard soundness, a small soundness error\\((1/poly)\\), and a complexity linear in the size of the solution. Using our core argument system, we construct highly efficient argument systems for a variety of statements relevant to lattices, including linear equations with short solutions and matrix-vector relations with hidden matrices. Based on our argument systems, we present several new constructions of common privacy-preserving primitives in thestandard latticesetting, including a group signature, a ring signature, an electronic cash system, and a range proof protocol. Our new constructions are one to three orders of magnitude more efficient than the state of the art (in standard lattice). This illustrates the efficiency and expressiveness of our argument system.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26948-7_6"}, {"primary_key": "2986344", "vector": [], "sparse_vector": [], "title": "How to Record Quantum Queries, and Applications to Quantum Indifferentiability.", "authors": ["<PERSON>"], "summary": "The quantum random oracle model (QROM) has become the standard model in which to prove the post-quantum security of random-oracle-based constructions. Unfortunately, none of the known proof techniques allow the reduction to record information about the adversary’s queries, a crucial feature of many classical ROM proofs, including all proofs of indifferentiability for hash function domain extension. In this work, we give a new QROM proof technique that overcomes this “recording barrier”. We do so by giving a new “compressed oracle” which allows for efficient on-the-fly simulation of random oracles, roughly analogous to the usual classical simulation. We then use this new technique to give the first proof of quantum indifferentiability for the Merkle-Damg<PERSON>rd domain extender for hash functions. We also give a proof of security for the Fuji<PERSON>-<PERSON><PERSON> transformation; previous proofs required modifying the scheme to include an additional hash term. Given the threat posed by quantum computers and the push toward quantum-resistant cryptosystems, our work represents an important tool for efficient post-quantum cryptosystems.", "published": "2019-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-26951-7_9"}]