[{"primary_key": "1776799", "vector": [], "sparse_vector": [], "title": "Online Allocation and Display Ads Optimization with Surplus Supply.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Balasubramanian <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we study a scenario where a publisher seeks to maximize its total revenue across two sales channels: guaranteed contracts that promise to deliver a certain number of impressions to the advertisers, and spot demands through an Ad Exchange. On the one hand, if a guaranteed contract is not fully delivered, it incurs a penalty for the publisher. On the other hand, the publisher might be able to sell an impression at a high price in the Ad Exchange. How does a publisher maximize its total revenue as a sum of the revenue from the Ad Exchange and the loss from the under-delivery penalty? We study this problem parameterized bysupply factorf: a notion we introduce that, intuitively, captures the number of times a publisher can satisfy all its guaranteed contracts given its inventory supply. In this work we present a fast simple deterministic algorithm with the optimal competitive ratio. The algorithm and the optimal competitive ratio are a function of the supply factor, penalty, and the distribution of the bids in the Ad Exchange. Beyond the yield optimization problem, classic online allocation problems such as online bipartite matching of Karp-Vazirani-Vazirani [25] and its vertex-weighted variant of <PERSON><PERSON><PERSON><PERSON> et al. [2] can be studied in the presence of the additional supply guaranteed by the supply factor. We show that a supply factor offimproves the approximation factors from\\(1-1/e\\)to\\(f-fe^{-1/f}\\). Our approximation factor is tight and approaches 1 as\\(f \\rightarrow \\infty \\).", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_3"}, {"primary_key": "1776800", "vector": [], "sparse_vector": [], "title": "Online Ad Allocation in Bounded-Degree Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the AdWords problem defined by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [10]. A search engine company has a set of advertisers who wish to link ads to user queries and issue respective bids. The goal is to assign advertisers to queries so as to maximize the total revenue accrued. The problem can be formulated as a matching problem in a bipartite graphG. We assume thatGis a (k,d)-graph, introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [11]. Such graphs model natural properties on the degrees of advertisers and queries. As a main result we present a deterministic online algorithm that achieves an optimal competitive ratio. The competitiveness tends to 1, for arbitrary\\(k\\ge d\\), using the standard small-bids assumption where the advertisers’ bids are small compared to their budgets. Hence, remarkably, nearly optimal ad allocations can be computed deterministically based on structural properties of the input. So far competitive ratios close to 1, for the AdWords problem, were only known in probabilistic input models.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_4"}, {"primary_key": "1776802", "vector": [], "sparse_vector": [], "title": "On Best-of-Both-Worlds Fair-Share Allocations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of fair allocation of indivisible items amongnagents with additive valuations, when agents have equal entitlements to the goods, and there are no transfers. Best-of-Both-Worlds (BoBW) fairness mechanisms aim to give all agents both an ex-ante guarantee (such as getting the proportional share in expectation) and an ex-post guarantee. Prior BoBW results have focused on ex-post guarantees that are based on the “up to one item\" paradigm, such as envy-free up to one item (EF1). In this work we attempt to give every agent a highvalueex-post, and specifically, a constant fraction of her maximin share (MMS). There are simple examples in which previous BoBW mechanisms give some agent only a\\(\\frac{1}{n}\\)fraction of her MMS. Our main result is a deterministic polynomial-time algorithm that computes a distribution over allocations that is ex-ante proportional, and ex-post, every allocation gives every agent at least half of her MMS. Moreover, the ex-post guarantee holds even with respect to a more demanding notion of a share, introduced in this paper, that we refer to as thetruncated proportional share(TPS). Our guarantees are nearly best possible, in the sense that one cannot guarantee agents more than their proportional share ex-ante, and one cannot guarantee all agents value larger than a\\(\\frac{n}{2n-1}\\)-fraction of their TPS ex-post.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_14"}, {"primary_key": "1776803", "vector": [], "sparse_vector": [], "title": "Nash Welfare Guarantees for Fair and Efficient Coverage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study coverage problems in which, for a set of agents and a given thresholdT, the goal is to selectTsubsets (of the agents) that, while satisfying combinatorial constraints, achieve fair and efficient coverage among the agents. In this setting, the valuation of each agent is equated to the number of selected subsets that contain it, plus one. The current work utilizes the Nash social welfare function to quantify the extent of fairness and collective efficiency. We develop a polynomial-time\\(\\left( 18 + o(1) \\right) \\)-approximation algorithm for maximizing Nash social welfare in coverage instances. Our algorithm applies to all instances wherein, for the underlying combinatorial constraints, there exists an FPTAS for weight maximization. We complement the algorithmic result by proving that Nash social welfare maximization is APX-hard in coverage instances.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_15"}, {"primary_key": "1776804", "vector": [], "sparse_vector": [], "title": "Fair and Efficient Multi-resource Allocation for Cloud Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of allocating multiple types of resources to agents with <PERSON><PERSON><PERSON> preferences. The classic Dominant Resource Fairness (DRF) mechanism satisfies several desired fairness and incentive properties, but is known to have poor performance in terms of social welfare approximation ratio. In this work, we propose a new approximation ratio measure, calledfair-ratio, which is defined as the worst-case ratio between the optimal social welfare (resp. utilization) among allfairallocations and that by the mechanism, allowing us to break the lower bound barrier under the classic approximation ratio. We then generalize DRF and present several new mechanisms with two and multiple types of resources that satisfy the same set of properties as DRF but with better social welfare and utilization guarantees under the new benchmark. We also demonstrate the effectiveness of these mechanisms through experiments on both synthetic and real-world datasets.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_10"}, {"primary_key": "1776805", "vector": [], "sparse_vector": [], "title": "Optimal Impartial Correspondences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study mechanisms that select a subset of the vertex set of a directed graph in order to maximize the minimum indegree of any selected vertex, subject to an impartiality constraint that the selection of a particular vertex is independent of the outgoing edges of that vertex. For graphs with maximum outdegreed, we give a mechanism that selects at most\\(d+1\\)vertices and only selects vertices whose indegree is at least the maximum indegree in the graph minus one. We then show that this is best possible in the sense that no impartial mechanism can only select vertices with maximum degree, even without any restriction on the number of selected vertices. We finally obtain the following trade-off between the maximum number of vertices selected and the minimum indegree of any selected vertex: when selecting at mostkvertices out ofn, it is possible to only select vertices whose indegree is at least the maximum indegree minus\\(\\lfloor (n-2)/(k-1)\\rfloor +1\\).", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_11"}, {"primary_key": "1776809", "vector": [], "sparse_vector": [], "title": "Tight Bounds on 3-Team Manipulations in Randomized Death Match.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consider a round-robin tournament onnteams, where a winner must be (possibly randomly) selected as a function of the results from the\\(\\left( {\\begin{array}{c}n\\\\ 2\\end{array}}\\right) \\)pairwise matches. A tournament rule is said to bek-SNM-\\(\\alpha \\)if no set ofkteams can ever manipulate the\\(\\left( {\\begin{array}{c}k\\\\ 2\\end{array}}\\right) \\)pairwise matches between them to improve the joint probability that one of thesekteams wins by more than\\(\\alpha \\). Prior work identifies multiple simple tournament rules that are 2-SNM-1/3 (Randomized Single Elimination Bracket [17], Randomized King of the Hill [18], Randomized Death Match [6]), which is optimal for\\(k=2\\)among all Condorcet-consistent rules (that is, rules that select an undefeated team with probability 1). Our main result establishes that Randomized Death Match is 3-SNM-(31/60), which is tight (for Randomized Death Match). This is the first tight analysis of any Condorcet-consistent tournament rule and at least three manipulating teams. Our proof approach is novel in this domain: we explicitly find the most-manipulable tournament, and directly show that no other tournament can be more manipulable. In addition to our main result, we establish that Randomized Death Match disincentivizes Sybil attacks (where a team enters multiple copies of themselves into the tournament, and arbitrarily manipulates the outcomes of matches between their copies). Specifically, for any tournament, and any teamuthat is not a Condorcet winner, the probability thatuor one of its Sybils wins in Randomized Death Match approaches 0 as the number of Sybils approaches\\(\\infty \\).", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_16"}, {"primary_key": "1776810", "vector": [], "sparse_vector": [], "title": "Online Team Formation Under Different Synergies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Team formation is ubiquitous in many sectors: education, labor markets, sports, etc. A team’s success depends on its members’ latent types, which are not directly observable but can be (partially) inferred from past performances. From the viewpoint of a principal trying to select teams, this leads to a natural exploration-exploitation trade-off: retain successful teams that are discovered early, or reassign agents to learn more about their types? We study a natural model for online team formation, where a principal repeatedly partitions a group of agents into teams. Agents have binary latent types, each team comprises two members, and a team’s performance is a symmetric function of its members’ types. Over multiple rounds, the principal selects matchings over agents and incurs regret equal to the deficit in the number of successful teams versus the optimal matching for the given function. Our work provides a complete characterization of the regret landscape for all symmetric functions of two binary inputs. In particular, we develop team-selection policies that, despite being agnostic of model parameters, achieve optimal or near-optimal regret against an adaptive adversary.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_5"}, {"primary_key": "1776812", "vector": [], "sparse_vector": [], "title": "Improved Approximation to First-Best Gains-from-Trade.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study the two-agent single-item bilateral trade. Ideally, the trade should happen whenever the buyer’s value for the item exceeds the seller’s cost. However, the classical result of <PERSON><PERSON> and <PERSON> showed that no mechanism can achieve this without violating one of the Bayesian incentive compatibility, individual rationality and weakly balanced budget conditions. This motivates the study of approximating the trade-whenever-socially-beneficial mechanism, in terms of the expected gains-from-trade. Recently, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> showed that the random-offerer mechanism achieves at least a 1/8.23 approximation. We improve this lower bound to 1/3.15 in this paper. We also determine the exact worst-case approximation ratio of the seller-pricing mechanism assuming the distribution of the buyer’s value satisfies the monotone hazard rate property.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_12"}, {"primary_key": "1776814", "vector": [], "sparse_vector": [], "title": "Stability of Decentralized Queueing Networks Beyond Complete Bipartite Cases.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Ji<PERSON>&<PERSON><PERSON>s;nan <PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [3,4] recently studied a model of queueing networks where queues compete for servers and re-send returned packets in future rounds. They quantify the amount of additional processing power that guarantees a decentralized system’s stability, both when the queues adapt their strategies from round to round using no-regret learning algorithms, and when they are patient and evaluate the utility of a strategy over long periods of time. In this paper, we generalize <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s model and consider scenarios where not all servers can serve all queues (i.e., the underlying graph is an incomplete bipartite graphs) and, further, when packets need to go through more than one server before their completions (i.e., when the underlying graph is a DAG). For the bipartite case, we obtain bounds comparable to those by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, with the factor slightly worse in the patient queueing model. For the more general multi-layer systems, we show that straightforward generalizations of the queues’ utilities and servers’ priority rules in [3] may lead to unbounded gaps between centralized and decentralized systems when the queues use no regret strategies. We give new utilities and service priority rules that are aware of the queue lengths, and show that these suffice to restore the bounded gap between centralized and decentralized systems.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_6"}, {"primary_key": "1776817", "vector": [], "sparse_vector": [], "title": "Optimal Prophet Inequality with Less than One Sample.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a growing interest in studying sample-based prophet inequality with the motivation stemming from the connection between the prophet inequalities and the sequential posted pricing mechanisms. <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (ITCS 2021) established the optimal single-choice prophet inequality with only a single sample per each distribution. Our work considers the sample-based prophet inequality withless than one sampleper distribution, i.e., scenarios with no prior information about some of the random variables. Specifically, we propose ap-sample model, where a sample from each distribution is revealed with probability\\(p \\in [0,1]\\)independently across all distributions. This model generalizes the single-sample setting of <PERSON><PERSON>, <PERSON>, and <PERSON> (ITCS 2021), and the i.i.d. prophet inequality with a linear number of samples of <PERSON><PERSON><PERSON> et al. (EC 2019). Our main result is the optimal\\(\\frac{p}{1+p}\\)prophet inequality for all\\(p\\in [0,1]\\).", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_7"}, {"primary_key": "1776818", "vector": [], "sparse_vector": [], "title": "Inefficiency of Pure Nash Equilibria in Series-Parallel Network Congestion Games.", "authors": ["Bai<PERSON>", "<PERSON>"], "summary": "We study the inefficiency of pure Nash equilibria in symmetric unweighted network congestion games defined over series-parallel networks. We introduce a quantity\\(y(\\mathcal {D})\\)to upper bound the Price of Anarchy (PoA) for delay functions in class\\(\\mathcal {D}\\). When\\(\\mathcal {D}\\)is the class of polynomial functions with highest degree\\(p\\), our upper bound is\\(2^{p+1} - 1\\), which is significantly smaller than the worst-case PoA for general networks. Thus, restricting to symmetric games over series-parallel networks can limit the inefficiency of pure Nash equilibria. We also construct a family of instances with polynomial delay functions that have a PoA in\\(\\varOmega ({2^p}/{p})\\)when the number of players goes to infinity. Compared with the subclass of extension-parallel networks, whose worst-case PoA is in\\(\\varTheta \\left( {p}/{\\ln p}\\right) \\), our results show that the worst-case PoA quickly degrades from sub-linear to exponential when relaxing the network topology. We also consider an alternative measure of the social cost of a strategy profile as the maximum players’ cost. We introduce a parameter\\(z(\\mathcal D)\\)and we show that the PoA is at most\\(y(\\mathcal D)z(\\mathcal D)\\), which for polynomial delays of maximum degree\\(p\\)is at most\\(2^{2p+ 1} - 2^p\\). Compared to the worst-case PoA in general symmetric congestion games, which is in\\(p^{\\varTheta (p)}\\), our results shows a significant improvement in efficiency. We finally prove that our previous lower bound in\\(\\varOmega (2^{p}/p)\\)is still valid for this measure of social cost. This is in stark contrast with the PoA in the subclass of extension-parallel networks, where each pure Nash equilibrium is a social optimum.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_1"}, {"primary_key": "1776820", "vector": [], "sparse_vector": [], "title": "Exploiting Extensive-Form Structure in Empirical Game-Theoretic Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Empirical game-theoretic analysis (EGTA) is a general framework for reasoning about complex games using agent-based simulation. Data from simulating select strategy profiles is employed to estimate a cogent and tractable game model approximating the underlying game. To date, EGTA methodology has focused on game models in normal form; though the simulations play out in sequential observations and decisions over time, the game model abstracts away this temporal structure. Richer models ofextensive-form games(EFGs) provide a means to capture temporal patterns in action and information, using tree representations. We proposetree-exploiting EGTA(TE-EGTA), an approach to incorporate EFG models into EGTA. TE-EGTA constructs game models that express observations and temporal organization of activity, albeit at a coarser grain than the underlying agent-based simulation model. The idea is to exploit key structure while maintaining tractability. We establish theoretically and experimentally that exploiting even a little temporal structure can vastly reduce estimation error in strategy-profile payoffs compared to the normal-form model. Further, we explore the implications of EFG models for iterative approaches to EGTA, where strategy spaces are extended incrementally. Our experiments on several game instances demonstrate that TE-EGTA can also improve performance in the iterative setting, as measured by the quality of equilibrium approximation as the strategy spaces are expanded.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_8"}, {"primary_key": "1776821", "vector": [], "sparse_vector": [], "title": "Better Approximation for Interdependent SOS Valuations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Submodular over signal (SOS) defines a family of interesting functions for which there exist truthful mechanisms with constant approximation to the social welfare for agents with interdependent valuations. The best-known truthful auction is of 4-approximation and a lower bound of 2 was proved. We propose a new and simple truthful mechanism to achieve an approximation ratio of 3.315. In particular, we first generalize the random sampling auction in [9]. Then, we proposed a brand new auction that is simple, efficient to implement and easy to verify the truthfulness. We call our mechanism the contribution-based mechanism. Our proposed mechanism with better approximation runs a convex combination of the above two mechanisms. Since the random sampling mechanism performs well when the second largest value is comparable to the largest one while contribution-based mechanism performs well when the largest one is much larger than all other values, their combination achieves a good balance for all instances. The approximation of our final mechanism is 3.315. This improves the previous 4-approximation mechanism for the first time. Besides the new auction, we also investigate the relation with SOS and strong-SOS, a stronger notion of SOS which was also introduced in [9]. We build a reduction and prove that strong-SOS is as difficult as SOS in terms of approximation ratio for single item setting. This means that it is fine to design mechanisms for strong-SOS valuation only if it is easier since the mechanism can be transformed to a mechanism for general SOS valuations with almost same approximation ratio. The full version of our paper can be found here:https://arxiv.org/abs/2210.06507.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_13"}, {"primary_key": "1776822", "vector": [], "sparse_vector": [], "title": "Constructing <PERSON><PERSON> Curves from a Single Observation of Bundle Sales.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Firms typically require multiple sales observations under varying prices to understand how the demand for their items respond to price. In this paper, our partner online retailer is faced with the problem of reconstructing demand curves when only asinglepoint on each curve has been historically observed. We show how a second point on each curve can be extracted from the sales of their discounted bundles, after which we help them estimate linear demand curves for their items. We perform this extraction by fitting a multi-item valuation model from the bundle pricing literature, introducing a new iterative procedure for solving this fitting problem. Our extraction process reveals a new insight on the relationship between an item’s relative frequency of bundle sales vs. the steepness of its demand curve around its current price. We validate this insight on the data provided by our partner firm.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_9"}, {"primary_key": "1776823", "vector": [], "sparse_vector": [], "title": "Auditing for Core Stability in Participatory Budgeting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider theparticipatory budgetingproblem where each ofnvoters specifies additive utilities overmcandidate projects with given sizes, and the goal is to choose a subset of projects (i.e., acommittee) with total size at mostk. Participatory budgeting mathematically generalizes multiwinner elections, and both have received great attention in computational social choice recently. A well-studied notion of group fairness in this setting iscore stability: Each voter is assigned an “entitlement” of\\(\\frac{k}{n}\\), so that a subsetSof voters can pay for a committee of size at most\\(|S| \\cdot \\frac{k}{n}\\). A given committee is in the core if no subset of voters can pay for another committee that provides each of them strictly larger utility. This provides proportional representation to all voters in a strong sense. In this paper, we study the following auditing question: Given a committee computed by some preference aggregation method, how close is it to the core? Concretely, how much does the entitlement of each voter need to be scaled down by, so that the core property subsequently holds? As our main contribution, we present computational hardness results for this problem, as well as a logarithmic approximation algorithm via linear program rounding. We show that our analysis is tight against the linear programming bound. Additionally, we consider two related notions of group fairness that have similar audit properties. The first isLindahl priceability, which audits the closeness of a committee to a market clearing solution. We show that this is related to the linear programming relaxation of auditing the core, leading to efficient exact and approximation algorithms for auditing. The second is a novel weakening of the core that we term thesub-core, and we present computational results for auditing this notion as well.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_17"}, {"primary_key": "1776825", "vector": [], "sparse_vector": [], "title": "Core-Stable Committees Under Restricted Domains.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the setting of committee elections, where a group of individuals needs to collectively select a given-size subset of available objects. This model is relevant for a number of real-life scenarios including political elections, participatory budgeting, and facility-location. We focus on the core—the classic notion of proportionality, stability and fairness. We show that for a number of restricted domains including voter-interval, candidate-interval, single-peaked, and single-crossing preferences the core is non-empty and can be found in polynomial time. We show that the core might be empty for strict top-monotonic preferences, yet we introduce a relaxation of this class, which guarantees non-emptiness of the core. Our algorithms work both in the randomized and discrete models. We also show that the classic known proportional rules do not return committees from the core even for the most restrictive domains among those we consider (in particular for 1D-Euclidean preferences). We additionally prove a number of structural results that give better insights into the nature of some of the restricted domains, and which in particular give a better intuitive understanding of the class of top-monotonic preferences.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_18"}, {"primary_key": "1776829", "vector": [], "sparse_vector": [], "title": "Beyond the Worst Case: Semi-random Complexity Analysis of Winner Determination.", "authors": ["Li<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The computational complexity of winner determination is a classical and important problem in computational social choice. Previous work based on worst-case analysis has established NP-hardness of winner determination for some classic voting rules, such as <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. In this paper, we revisit the classical problem of winner determination through the lens ofsemi-random analysis, which is a worst average-case analysis where the preferences are generated from a distribution chosen by the adversary. Under a natural class of semi-random models that are inspired by recommender systems, we prove that winner determination remains hard for <PERSON><PERSON>, <PERSON>, and some multi-winner rules such as the <PERSON>lin-<PERSON> rule and the <PERSON> rule. Under another natural class of semi-random models that are extensions of the Impartial Culture, we show that winner determination is hard for <PERSON><PERSON><PERSON>, but is easy for <PERSON><PERSON>. This illustrates an interesting separation between <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_19"}, {"primary_key": "1776832", "vector": [], "sparse_vector": [], "title": "Insightful Mining Equilibria.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chaozhe Kong", "<PERSON><PERSON>"], "summary": "The selfish mining attack, arguably the most famous game-theoretic attack in blockchain, indicates that the Bitcoin protocol is not incentive-compatible. Most subsequent works mainly focus on strengthening the selfish mining strategy, thus enabling a single strategic agent more likely to deviate. In sharp contrast, little attention has been paid to the resistant behavior against the selfish mining attack, let alone further equilibrium analysis for miners and mining pools in the blockchain as a multi-agent system. In this paper, first, we propose a novel strategy called insightful mining to counteract the selfish mining attack. By infiltrating an undercover miner into the selfish pool, the insightful pool could acquire the number of its hidden blocks. We prove that, with this extra insight, the utility of the insightful pool is strictly greater than the selfish pool’s when they have the same mining power. Then we investigate the mining game where all pools can choose to be honest or take the insightful mining strategy. We characterize the Nash equilibrium of such a game and derive three corollaries: (a) each mining game has apureNash equilibrium; (b) there are at most two insightful pools under some equilibrium no matter how the mining power is distributed; (c) honest mining is a Nash equilibrium if the largest mining pool has a fraction of mining power no more than 1/3. Our work explores, for the first time, the idea of spying in the selfish mining attack, which might shed new light on researchers in the field.", "published": "2022-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-22832-2_2"}]