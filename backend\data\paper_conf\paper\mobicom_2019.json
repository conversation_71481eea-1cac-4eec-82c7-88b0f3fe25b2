[{"primary_key": "3092836", "vector": [], "sparse_vector": [], "title": "Poster: Address Shuffling based Moving Target Defense for In-Vehicle Software-Defined Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hyuk Lim"], "summary": "As connected and autonomous vehicle technology evolves, the design of in-vehicle network architecture, which connects multiple electronic control units (ECUs) and internal sensors and supports connectivity to the outside of the vehicle, has increased significantly to meet security needs. However, the heterogeneous structure of the in-vehicle network and lack of security consideration has introduced a lack of scalability and security concerns. In this work, we propose a shuffling-based moving target defense (MTD) technique aiming to disturb network reconnaissance attacks and deployed it in the proposed software-defined networking (SDN)-based in-vehicle network architecture. To validate the proposed MTD, we compare the service availability of our proposed MTD and non-MTD counterpart in the presence of the reconnaissance-based false message injection attacks.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343392"}, {"primary_key": "3092837", "vector": [], "sparse_vector": [], "title": "Poster: Energy Efficient Mobile Video Transmission over Wireless Networks in IoT Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video surveillance is an important application of Internet of Thing (IoT) that provides convenience remote monitoring service to end users. How to reduce the power consumption of mobile devices with limited energy has become a research hotspot. We propose an energy-efficient architecture that includes smartphones' various power saving solutions for video transmission over wireless networks. Results demonstrate that each of our proposed solutions is significantly outperforms than the standard scheme.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343383"}, {"primary_key": "3092839", "vector": [], "sparse_vector": [], "title": "Poster: Can Mobile Hardware Keep Up with Today&apos;s Gigabit Wireless Technologies?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the advent of bandwidth-hungry applications and the new advancements in wireless LAN standards (802.11ad, 802.11ay) and cellular technologies (5G), modern smartphones need to be able support multi-Gbps data rates. In this work, we explore if today's smartphones are capable of handling such high-speed network traffic. Using two high-end smartphones, we show that, contrary to previous beliefs, they can indeed support Gbps data rates without significant strain on their hardware resources. Using projections, we further show that up to 12.8 Gbps could be supported with just 50% CPU utilization. Finally, we explore the factors that make this possible and the contribution of each of them.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343398"}, {"primary_key": "3092843", "vector": [], "sparse_vector": [], "title": "Poster: Protecting Control Planes in In-Band Software-Defined Wireless Networks.", "authors": ["Namwon An", "Hyuk Lim"], "summary": "Software-defined networking (SDN) is a mechanism that decouples a control plane from a network to manage the data plane of the network flexibly in a centralized view. In in-band-based software-defined wireless networks (IB-SDWNs), the control plane shares wireless channels with the data plane. As the amount of data-plane traffic increases, the interference from the data plane to the control plane becomes stronger, and the switches connected to the SDN controller via the control plane may become out-of-control. In this paper, we propose an interference management scheme that reduces the interference among control and data planes by adjusting the transmission rates of data-plane traffic in switches to ensure reliable connections between the SDN controller and switches. We implemented a prototype of an IB-SDWN and showed that the proposed interference management scheme ensures the reliability of control planes.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343396"}, {"primary_key": "3092844", "vector": [], "sparse_vector": [], "title": "Demo: Activating Wireless Voice for E-Toll Collection Systems with Zero Start-up Cost.", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lin"], "summary": "This work enhances the machine-to-human communication between electronic toll collection (ETC) systems and drivers by providing an AM broadcast service to deployed ETC systems. This demo is the first to show that ultra-high radio frequency identification signals can be received by an AM radio receiver due to the presence of the nonlinearity effect in the AM receiver. Such a phenomenon allows the development of a previously infeasible cross-technology communication, called Tagcaster, which converts an ETC reader to an AM station for broadcasting short messages (e.g, charged-fees and traffic forecast) to drivers at tollbooths. The prototype of Tagcaster is designed, implemented and evaluated over four general and five vehicle-mounted AM receivers (e.g, Toyota, Audi, and Jetta). Experiments reveal that Tagcaster can provide good-quality (PESQ>2) and stable AM broadcasting service with a 30m coverage range.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343363"}, {"primary_key": "3092846", "vector": [], "sparse_vector": [], "title": "HotEdgeVideo&apos;19: Workshop on Hot Topics in Video Analytics and Intelligent Edges.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuanchao Shu"], "summary": "No abstract available.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355630"}, {"primary_key": "3092848", "vector": [], "sparse_vector": [], "title": "vrAIn: A Deep Learning Approach Tailoring Computing and Radio Resources in Virtualized RANs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The virtualization of radio access networks (vRAN) is the last milestone in the NFV revolution. However, the complex dependencies between computing and radio resources make vRAN resource control particularly daunting. We present vrAIn, a dynamic resource controller for vRANs based on deep reinforcement learning. First, we use an autoencoder to project high-dimensional context data (traffic and signal quality patterns) into a latent representation. Then, we use a deep deterministic policy gradient (DDPG) algorithm based on an actor-critic neural network structure and a classifier to map (encoded) contexts into resource control decisions. We have implemented vrAIn using an open-source LTE stack over different platforms. Our results show that vrAIn successfully derives appropriate compute and radio control actions irrespective of the platform and context: (i) it provides savings in computational capacity of up to 30% over CPU-unaware methods; (ii) it improves the probability of meeting QoS targets by 25% over static allocation policies using similar CPU resources in average; (iii) upon CPU capacity shortage, it improves throughput performance by 25% over state-of-the-art schemes; and (iv) it performs close to optimal policies resulting from an offline oracle. To the best of our knowledge, this is the first work that thoroughly studies the computational behavior of vRANs, and the first approach to a model-free solution that does not need to assume any particular vRAN platform or system conditions.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345431"}, {"primary_key": "3092849", "vector": [], "sparse_vector": [], "title": "Demo: vrAIn Proof-of-Concept - A Deep Learning Approach for Virtualized RAN Resource Control.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While the application of the NFV paradigm into the network is proceeding full steam ahead, there is still one last mile- stone to be achieved in this context: the virtualization of the radio access network (vRAN). Due to the very complex de- pendency between the radio conditions and the computing resources needed to provide the baseband processing func- tionality, attaining an efficient resource control is particularly challenging. In this demonstration, we will showcase vrAIn, a vRAN dynamic resource controller that employs deep re- inforcement learning to perform resource assignment deci- sions. vrAIn, which is implemented using an open-source LTE stack over a Linux platform, can achieve substantial sav- ings in the used CPU resources while maintaining the target QoS for the attached terminals and maximize throughput when there is a deficit of computational capacity.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343370"}, {"primary_key": "3092850", "vector": [], "sparse_vector": [], "title": "Poster: Inaudible High-throughput Communication Through Acoustic Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Li Lu", "<PERSON><PERSON>"], "summary": "In recent decades, countless efforts have been put into the research and development of short-range wireless communication, which offers a convenient way for numerous applications (e.g., mobile payments, mobile advertisement). Regarding the design of acoustic communication, throughput and inaudibility are the most vital aspects, which greatly affect available applications that can be supported and their user experience. Existing studies on acoustic communication either use audible frequency band (e.g., <20kHz) to achieve a relatively high throughput or realize inaudibility using near-ultrasonic frequency band (e.g., 18-20kHz) which however can only achieve limited throughput. Leveraging the non-linearity of microphones, voice commands can be demodulated from the ultrasound signals, and further recognized by the speech recognition systems. In this poster, we design an acoustic communication system, which achieves high-throughput and inaudibility at the same time, and the highest throughput we achieve is over 17x higher than the state-of-the-art acoustic communication systems.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343405"}, {"primary_key": "3092851", "vector": [], "sparse_vector": [], "title": "Jigsaw: Robust Live 4K Video Streaming.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The popularity of 4K videos has grown significantly in the past few years. Yet coding and streaming live 4K videos incurs prohibitive cost to the network and end system. Motivated by this observation, we explore the feasibility of supporting live 4K video streaming over wireless networks using commodity devices. Given the high data rate requirement of 4K videos, 60 GHz is appealing, but its large and unpredictable throughput fluctuation makes it hard to provide desirable user experience. In particular, to support live 4K video streaming, we should (i) adapt to highly variable and unpredictable wireless throughput, (ii) support efficient 4K video coding on commodity devices. To this end, we propose a novel system, Jigsaw. It consists of (i) easy-to-compute layered video coding to seamlessly adapt to unpredictable wireless link fluctuations, (ii) efficient GPU implementation of video coding on commodity devices, and (iii) effectively leveraging both WiFi and WiGig through delayed video adaptation and smart scheduling. Using real experiments and emulation, we demonstrate the feasibility and effectiveness of our system. Our results show that it improves PSNR by 6-15dB and improves SSIM by 0.011-0.217 over state-of-the-art approaches. Moreover, even when throughput fluctuates widely between 0.2Gbps-2Gbps, it can achieve an average PSNR of 33dB.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300127"}, {"primary_key": "3092852", "vector": [], "sparse_vector": [], "title": "Fast and Efficient Cross Band Channel Prediction Using Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Channel information plays an important role in modern wireless communication systems. Systems that use different frequency bands for uplink and downlink communication often need feedback between devices to exchange band specific channel information. The current state-of-the-art approach proposes a way to predict the channel in the downlink based on that of the observed uplink by identifying variables underlying the uplink channel. In this paper we present a solution that greatly reduces the complexity of this task, and is even applicable for single antenna devices. Our approach uses a neural network trained on a standard channel model to generate coarse estimates for the variables underlying the channel. We then use a simple and efficient single antenna optimization framework to get more accurate variable estimates, which can be used for downlink channel prediction. We implement our approach on software defined radios and compare it to the state-of-the-art through experiments and simulations. Results show that our approach reduces the time complexity by at least an order of magnitude (10x), while maintaining similar prediction quality.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345438"}, {"primary_key": "3092853", "vector": [], "sparse_vector": [], "title": "Detecting if LTE is the Bottleneck with BurstTracker.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present BurstTracker, the first tool that developers can use to detect if the LTE downlink is the bottleneck for their applications. BurstTracker is driven by our discovery that the proprietary LTE downlink schedulers running on LTE base stations allocate resources to users in a way that reveals if a user's downlink queue runs empty during a download. We demonstrate that BurstTracker works across Tier-1 cellular providers and across a variety of network conditions. We also present a case study that shows how application developers can use this tool in practice. Surprisingly, with BurstTracker, we find that the LTE downlink may not be the bottleneck for video streaming on several Tier-1 providers, even during peak hours at busy locations. Rather, transparent TCP middleboxes deployed by these providers lead to downlink underutilization, because they force Slow-Start Restart. With a simple workaround, we improve video streaming bitrate on busy LTE links by 35%.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300140"}, {"primary_key": "3092854", "vector": [], "sparse_vector": [], "title": "CHANTS&apos;19: 14th Workshop on Challenged Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Although communication networks have been constantly evolving and increasing their capacity with state-of-the-art solutions, it is still hard to provide reliable and high capacity communications in some cases referred to as challenged networks. In these networks, ensuring performance guarantees is hard either due to the lack of infrastructure or its limitations, such as in public safety networks, or due to the challenging communication medium, such as in mmWave networks. Challenged networks face now new constraints with the proliferation of services and applications, increasing number of connected devices, the high computing and control demands, unpredicted human behavior, and only partially-available information. CHANTS'19 aims at bringing researchers together to have a platform for discussing the challenges emerging with new use cases and real-life applications, such as edge computing or autonomous driving, and corresponding innovative approaches in tackling the limitations of the challenged networks. Moreover, this workshop aims at providing another perspective to the broader audience by listing a subset of problems that communication networks, despite the advances on many fronts, still have to tackle. The expected outcomes of CHANTS'19 have the potential to influence industrial thinking about the technologies of next-generation challenged networks.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355634"}, {"primary_key": "3092859", "vector": [], "sparse_vector": [], "title": "ClientMarshal: Regaining Control from Wireless Clients for Better Experience.", "authors": ["<PERSON><PERSON><PERSON> Bhartia", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Client devices must cooperate to share limited resources in wireless local area networks (WLANs). Standards exist for access points (APs), to use a more holistic view of the RF environment, to share information with clients (802.11v/k), allowing clients to make better performance-impacting decisions, e.g. choosing to roam between APs. Standards intended to lower the cost of these expensive control-plane operations (802.11r) also exist. In this paper, we use large scale measurements to show the limited adoption of existing client-based techniques. We propose ClientMarshal for enterprise WLANs, which provides network infrastructure more control over these decisions without modification to client device software. ClientMarshal divides the enterprise network into logical groups called cells, each consisting of a small number of neighboring APs operating on different channels. All APs belonging to the same cell broadcast the same wireless identity (BSSID) to client devices, and appear as a single virtual AP. The key enabler behind ClientMarshal is FreeSteer (FST), a novel protocol we develop that allows an AP to dynamically steer an associated client to another AP in the same cell based on various network measurements in order to improve overall network performance. This design also improves the roaming experience by bypassing traditional control-plane operations, avoiding significant overhead as well as client implementation defects. Using systematic evaluation and implementation on commodity APs, we show the efficacy and control of FST protocol in client steering. Compared to state-of-the-art approaches, FST can steer clients almost 2.5X faster, with 52% higher success rate, and reduce packet loss by more than 68%.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300135"}, {"primary_key": "3092860", "vector": [], "sparse_vector": [], "title": "Experience: Design, Development and Evaluation of a Wearable Device for mHealth Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wrist-worn devices hold great potential as a platform for mobile health (mHealth) applications because they comprise a familiar, convenient form factor and can embed sensors in proximity to the human body. Despite this potential, however, they are severely limited in battery life, storage, bandwidth, computing power, and screen size. In this paper, we describe the experience of the research and development team designing, implementing and evaluating Amulet? an open-hardware, open-software wrist-worn computing device? and its experience using Amulet to deploy mHealth apps in the field. In the past five years the team conducted 11 studies in the lab and in the field, involving 204 participants and collecting over 77,780 hours of sensor data. We describe the technical issues the team encountered and the lessons they learned, and conclude with a set of recommendations. We anticipate the experience described herein will be useful for the development of other research-oriented computing platforms. It should also be useful for researchers interested in developing and deploying mHealth applications, whether with the Amulet system or with other wearable platforms.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345432"}, {"primary_key": "3092861", "vector": [], "sparse_vector": [], "title": "Poster: DyMand - An Open-Source Mobile and Wearable System for Assessing Couples&apos; Dyadic Management of Chronic Diseases.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Married adults share illness management with spouses and it involves social support and common dyadic coping (CDC). Social support and CDC have an impact on health behavior and well-being or emotions in couples' dyadic management of diabetes in daily life. Hence, understanding dyadic interactions in-situ in chronic disease management could inform behavioral interventions to help the dyadic management of chronic diseases. It is however not clear how well social support and CDC can be assessed in daily life among couples who are managing chronic diseases. In this ongoing work, we describe the development of DyMand, a novel open-source mobile and wearable system for ambulatory assessment of couples' dyadic management of chronic diseases. Our first prototype is used in the context of diabetes mellitus Type II. Additionally, we briefly describe our experience deploying the prototype in two pre-pilot tests with five subjects and our plans for future deployments.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343399"}, {"primary_key": "3092863", "vector": [], "sparse_vector": [], "title": "eBP: A Wearable System For Frequent and Comfortable Blood Pressure Monitoring From User&apos;s Ear.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Thang <PERSON>", "<PERSON>"], "summary": "Frequent blood pressure (BP) assessment is key to the diagnosis and treatment of many severe diseases, such as heart failure, kidney failure, hypertension, and hemodialysis. Current \"gold-standard'' BP measurement techniques require the complete blockage of blood flow, which causes discomfort and disruption to normal activity when the assessment is done repetitively and frequently. Unfortunately, patients with hypertension or hemodialysis often have to get their BP measured every 15 minutes for a duration of 4-5 hours or more. The discomfort of wearing a cumbersome and limited mobility device affects their normal activities. In this work, we propose a device called eBP to measure BP from inside the user's ear aiming to minimize the measurement's impact on users' normal activities while maximizing its comfort level. eBP has 3 key components: (1) a light-based pulse sensor attached on an inflatable pipe that goes inside the ear, (2) a digital air pump with a fine controller, and (3) a BP estimation algorithm. In contrast to existing devices, eBP introduces a novel technique that eliminates the need to block the blood flow inside the ear, which alleviates the user's discomfort. We prototyped eBP custom hardware and software and evaluated the system through a comparative study on 35 subjects. The study shows that eBP obtains the average error of 1.8 mmHg and -3.1 mmHg and a standard deviation error of 7.2 mmHg and 7.9 mmHg for systolic (high-pressure value) and diastolic (low-pressure value), respectively. These errors are around the acceptable margins regulated by the FDA's AAMI protocol, which allows mean errors of up to 5 mmHg and a standard deviation of up to 8 mmHg.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345454"}, {"primary_key": "3092866", "vector": [], "sparse_vector": [], "title": "Internet-QoE 2019: 4th Internet-QoE Workshop on QoE-based Analysis and Management of Data Communication Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "After three highly successful editions of the Internet-QoE workshop organized at ACM SIGCOMM 2016, ACM SIGCOMM 2017, and IEEE ICDCS 2018, the goal of the fourth edition of the Internet-QoE workshop is to scale the concepts of Quality of Experience (user satisfaction, user engagement, and behavioral analysis) out of the lab studies context and bring it to the analysis and operation of distributed systems and communication networks, giving a user-centric perspective to the research performed by the MOBICOM community. By fostering an explicit and deep integration of the end-user directly into the design, analysis and management of large-scale operational networks, we expect to reduce the gap between QoE research and its application to future network management paradigms, as well as to provide a more targeted end-user perspective to the research on distributed communication systems. The 4th edition of Internet-QoE also focuses on novel end-user services enabled by next generation technologies such as immersive media (3D, Virtual Reality and Augmented Reality), self-driving cars, intelligent manufacturing systems, Industry 4.0 and tactile Internet, 5G ultra-low-latency mobile networks, and real-time applications.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355631"}, {"primary_key": "3092867", "vector": [], "sparse_vector": [], "title": "Taprint: Secure Text Input for Commodity Smart Wristbands.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ruk<PERSON>ana Ruby", "<PERSON><PERSON><PERSON>"], "summary": "Smart wristband has become a dominant device in the wearable ecosystem, providing versatile functions such as fitness tracking, mobile payment, and transport ticketing. However, the small form-factor, low-profile hardware interfaces and computational resources limit their capabilities in security checking. Many wristband devices have recently witnessed alarming vulnerabilities, e.g., personal data leakage and payment fraud, due to the lack of authentication and access control. To fill this gap, we propose a secure text pin input system, namely Taprint, which extends a virtual number pad on the back of a user's hand. Taprint builds on the key observation that the hand \"landmarks'', especially finger knuckles, bear unique vibration characteristics when being tapped by the user herself. It thus uses the tapping vibrometry as biometrics to authenticate the user, while distinguishing the tapping locations. Taprint reuses the inertial measurement unit in the wristband, \"overclocks'' its sampling rate to extrapolate fine-grained features, and further refines the features to enhance the uniqueness and reliability. Extensive experiments on 128 users demonstrate that Taprint achieves a high accuracy (96%) of keystrokes recognition. It can authenticate users, even through a single-tap, at extremely low error rate (2.4%), and under various practical usage disturbances.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300124"}, {"primary_key": "3092868", "vector": [], "sparse_vector": [], "title": "Poster: Enabling Wideband Full-Duplex Wireless via Frequency-Domain Equalization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Full-duplex (FD) wireless can significantly enhance spectrum efficiency but requires tremendous amount of self-interference (SI) cancellation. Recent advances in the RFIC community enabled wideband RF SI cancellation (SIC) in integrated circuits (ICs) via frequency-domain equalization (FDE), where reconfigurable RF filters are used to channelize the SI signal path. In [2], we designed and implemented an FDE-based RF canceller on a printed circuit board (PCB). We also presented an optimized canceller configuration scheme based on the derived canceller model, and extensively evaluated the performance of the FDE-based FD radios in a software-defined radio (SDR) testbed in different network settings.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343406"}, {"primary_key": "3092869", "vector": [], "sparse_vector": [], "title": "Wideband Full-Duplex Wireless via Frequency-Domain Equalization: Design and Experimentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Full-duplex (FD) wireless can significantly enhance spectrum efficiency but requires tremendous amount of self-interference (SI) cancellation. Recent advances in the RFIC community enabled wideband RF SI cancellation (SIC) in integrated circuits (ICs) via frequency-domain equalization (FDE), where RF filters channelize the SI signal path. Unlike other FD implementations, that mostly rely on delay lines, FDE-based cancellers can be realized in small-form-factor devices. However, the fundamental limits and higher layer challenges associated with these cancellers were not explored yet. Therefore, and in order to support the integration with a software-defined radio (SDR) and to facilitate experimentation in a testbed with several nodes, we design and implement an FDE-based RF canceller on a printed circuit board (PCB). We derive and experimentally validate the PCB canceller model and present a canceller configuration scheme based on an optimization problem. We then extensively evaluate the performance of the FDE-based FD radio in the SDR testbed. Experiments show that it achieves 95dB overall SIC (52dB from RF SIC) across 20MHz bandwidth, and an average link-level FD gain of 1.87x. We also conduct experiments in: (i) uplink-downlink networks with inter-user interference, and (ii) heterogeneous networks with half-duplex and FD users. The experimental FD gains in the two types of networks confirm previous analytical results. They depend on the users' SNR values and the number of FD users, and are 1.14x-1.25x and 1.25x-1.73x, respectively. Finally, we numerically evaluate and compare the RFIC and PCB implementations and study various design tradeoffs.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300138"}, {"primary_key": "3092871", "vector": [], "sparse_vector": [], "title": "Verification: Constructive and Destructive Full Duplex Relays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With recent advances in in-band full duplex techniques, full duplex capable relays have been shown feasible in many recent works. Instead of generating data itself to transmit, a full duplex relay can forward the received signal while receiving it. It also has the opportunity to properly modify the signal before forwarding it, which has been explored to enable constructive and destructive full duplex relaying recently. With these designs, the signal through the full duplex relay could constructively or destructively add up with the signal from the direct link at the receiver side. As a result, the received signal power may be boosted or greatly reduced, compared with direct forwarding. In this paper, we do detailed analysis on whether such constructive and destructive full duplex relays are possible. We find that for OFDM signals, such relays can not be achieved if the additional latency of the relayed path is more than one sample time than the direct link. Our analysis and results challenge the assumptions on the relay latency requirements of previous works to realize such systems.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345445"}, {"primary_key": "3092872", "vector": [], "sparse_vector": [], "title": "Keep Others from Peeking at Your Mobile Device Screen!", "authors": ["<PERSON><PERSON><PERSON> (Daniel) Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "People use their mobile devices anywhere and anytime to run various apps, and the information shown on their device screens can be seen by nearby (unauthorized) parties, called shoulder surfers. To mitigate this privacy threat, we have developed HideScreen by utilizing the human vision and optical system properties to hide the users' on-screen information from the shoulder surfers.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300119"}, {"primary_key": "3092873", "vector": [], "sparse_vector": [], "title": "Keep Others from Peeking at Your Mobile Device Screen!", "authors": ["<PERSON><PERSON><PERSON> (Daniel) Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The information displayed on mobile device screens can be seen by nearby (unauthorized) parties, called shoulder surfers. To protect sensitive on-screen information, we have developed HideScreen by utilizing the human vision and optical system properties to hide the users' on-screen information from the shoulder surfers.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343384"}, {"primary_key": "3092874", "vector": [], "sparse_vector": [], "title": "Demo: All-You-Can-Bike - A Street View and Virtual Reality Based Cyber-Physical System for Bikers through IoT Technologies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a smartphone-based cyber-physical system, called All-You-Can-Bike, for bikers to ride a bicycle around the world through street view and virtual reality based on Internet of Things (IoT) technologies. On the bicycle side, a microcontroller, a rotation sensor, and a Bluetooth module are used to calculate the cycling speed of the biker, sense the rotation period of wheels, and communicate with the biker's smartphone, respectively. On the biker side, a smartphone is used to detect the riding direction (through the built-in gyroscope), receive the cycling speed data (through Bluetooth communications), and determine the feasible frame updating rate of street view/virtual reality (through the proposed fuzzy control mechanism). Based on the current and historical cycling speeds, All-You-Can-Bike can derive the feasible frame updating rate based on the defined fuzzy sets of wheel rotation periods and cycling acceleration. An Android-based prototype with the Arduino developing board is implemented to verify the feasibility and superiority of our All-You-Can-Bike system.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343367"}, {"primary_key": "3092875", "vector": [], "sparse_vector": [], "title": "Optimizing Energy Efficiency of Browsers in Energy-Aware Scheduling-enabled Mobile Devices.", "authors": ["<PERSON><PERSON>", "Seonghoon Park", "<PERSON><PERSON><PERSON>"], "summary": "Web browsing, previously optimized for the desktop environment, is being fine-tuned for energy-efficient use on mobile devices. Although active attempts have been made to reduce energy consumption, the advent of energy-aware scheduling (EAS) integrated in the recent devices suggests the possibility of a new approach for optimizing energy use by browsers. Our preliminary analysis showed that the existing EAS-enabled system is overly optimized for performance, leading to energy inefficiencies while a web browser is running. In this paper, we analyze the characteristics of web browsers, and investigate the cause of energy inefficiency in EAS-enabled mobile devices. We then propose a system, called WebTune, to improve the energy efficiency of mobile browsers. Exploiting the reinforcement learning technique, WebTune learns the optimal execution speed of the web browser's processes, and adjusts the speed at runtime, thus saving energy and ensuring the quality of service (QoS). WebTune is implemented on the latest Android-based smartphones, and evaluated with Alexa's top 200 websites. The experimental results show that WebTune reduced the device-level energy consumption of the Google Pixel 2 XL and Samsung Galaxy S9 Plus smartphones by 18.7-22.0% and 13.7-16.1%, respectively, without degrading the QoS.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345449"}, {"primary_key": "3092877", "vector": [], "sparse_vector": [], "title": "HealthSense: Software-defined Mobile-based Clinical Trials.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rise of ever-more sophisticated wearables and sensing technologies, mobile health continues to be an active area of research. However, from a clinical researcher point of view, testing novel use of the mobile health innovations remains a major hurdle, as composing a clinical trial using a combination of technologies still remains in the realm of computer scientists. We take a software-inspired viewpoint of clinical trial designs to design, develop and validate HealthSense to enable expressibility of complex ideas, composability with diverse devices and services while maximally maintaining simplicity for a clinical research user. A key innovation in HealthSense is the concept of a study state manager (SSM) that modifies parameters of the study over time as data accumulates and can trigger external events that affect the participant; this design allows us to implement nearly arbitrary clinical trial designs. The SSM can funnel data streams to custom or third-party cloud processing pipelines and the result can be used to give interventions and modify parameters of the study. HealthSense supports both Android and iOS platforms and is secure, scalable and fully operational. We outline three trials (two with clinical populations) to highlight simplicity, composability, and expressibility of HealthSense.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345433"}, {"primary_key": "3092879", "vector": [], "sparse_vector": [], "title": "S3&apos;19 - Wireless of the Students, by the Students, and for the Students Workshop.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Wireless of the Students, by the Students, and for the Students (S3) Workshop provides a unique venue for graduate students around the world to present, discuss, and exchange ideas on cross-cutting research on mobile wireless networks. As its name suggests, the workshop is organized by students, and the technical sessions are given by student presenters. The workshop aims at fostering early-career development among students and exposing them to the workings of academic life. It provides a venue for students to learn about each other's' work and discover opportunities for collaboration. The workshop invites students to submit papers, posters, and demos. All submissions are peer-reviewed by the student program committee.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355632"}, {"primary_key": "3092881", "vector": [], "sparse_vector": [], "title": "Towards Low Cost Soil Sensing Using Wi-Fi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A farm's soil moisture and soil electrical conductivity (EC)readings are extremely valuable for a farmer. They can help her reduce water use and improve productivity. However, the high cost of commercial soil moisture sensors and the inaccuracy of sub-1000 dollar EC sensors have limited their adoption. In this paper, we present the design and implementation of a system, called Strobe, that senses soil moisture and soil EC using RF propagation in existing Wi-Fi bands. Strobe overcomes the key challenge of limited bandwidth availability in the 2.4 GHz unlicensed spectrum using a novel multi-antenna technique. It maps the propagation time and amplitude of Wi-Fi signals received by different antennas to the soil permittivity and EC, which in turn depend on soil moisture and salinity. Our experiments with USRP, WARP, and commodity Wi-Fi cards show that Strobe can accurately estimate soil moisture and EC using Wi-Fi, thereby showing the potential of a future in which a farmer can sense soil in their farm without investing 1000s of dollars in soil sensing equipments.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345440"}, {"primary_key": "3092882", "vector": [], "sparse_vector": [], "title": "Strobe - Towards Low Cost Soil Sensing Using Wi-Fi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A farm's soil moisture and soil electrical conductivity (EC)readings are extremely valuable for a farmer. They can help her reduce water use and improve productivity. However, the high cost of commercial soil moisture sensors and the inaccuracy of sub-1000 dollar EC sensors have limited their adoption. In this paper, we present the design and implementation of a system, called Strobe, that senses soil moisture and soil EC using RF propagation in existing Wi-Fi bands. Strobe overcomes the key challenge of limited bandwidth availability in the 2.4 GHz unlicensed spectrum using a novel multi-antenna technique. It maps the propagation time and amplitude of Wi-Fi signals received by different antennas to the soil permittivity and EC, which in turn depend on soil moisture and salinity. Our experiments with USRP, WARP, and commodity Wi-Fi cards show that Strobe can accurately estimate soil moisture and EC using Wi-Fi, thereby showing the potential of a future where a farmer can sense soil in their farm without investing 1000s of dollars in sensing tools.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343389"}, {"primary_key": "3092883", "vector": [], "sparse_vector": [], "title": "DEMO: EApp: Improving Rural Emergency Preparedness and Response.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large-scale emergencies, both natural and man-made, are increasingly incurring devastating losses in terms of infrastructure and human lives. Rural areas are particularly vulnerable to such losses. Emergency preparedness and response services, in rural areas, severely lag behind that of their urban counterparts. One of the key limiting factors is the lack of adequate broadband connectivity, which limits agencies' capabilities to (i) disseminate emergency preparedness and response information to residents and (ii) efficiently coordinate in the face of a disaster. In this demo, we present the EApp; a smartphone application that strives to improve the information access regarding emergencies for rural residents and first responders.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343380"}, {"primary_key": "3092887", "vector": [], "sparse_vector": [], "title": "Demo: The RFID Can Hear Your Music Play.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we devise RF-DJ, a contactless music recognition system with the help of COTS RFID device. Since the music is caused by vibration and the vibration can influence the RF signal, our system could accurately recover the frequency of every tone, especially string instruments. Specifically, RF-DJ is immune to noises from the player/instrument motions and the ambient environment. Further more, it can recover the high frequency signal from the relatively low sampling rate data. In our demonstration, we put one tag on the surface of ukulele (not the string) and achieve the overall recognition accuracy of $93%, 90%, 87%, 81%$ when using 1,2,3,4 strings, respectively. Compared to typical machine learning based RF sensing systems, our system is model driven instead of data driven, which requires little training effort and could be applicable across different locations. Last but not the least, our system can also be used for other instruments such as zither, violin and kalimba and shows similarly good performances.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343379"}, {"primary_key": "3092888", "vector": [], "sparse_vector": [], "title": "Poster: Wireless Network Functions in the Era of Low-Power IoT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network Function Virtualization has demonstrated how general purpose infrastructure in the cloud can match the performance of specialized hardware while providing greater flexibility and scalability. Yet, specialized hardware remains the preferred implementation choice for the wireless physical layer. This is primarily due to the tight latency requirements and high bandwidth demanded by operations at the wireless PHY, as opposed to the network-layer. This poster argues that in the era of low-power Internet of Things, virtualization of physical layer functions deserves a revisit. We specifically study low-power Internet of Things technologies that present a ripe opportunity for cloudification of wireless network functions, with their lower bandwidths and lax latency constraints. We present our vision of how physical layer function virtualization should be structured to maximize benefits and performance. We present a feasibility study on the latency and scalability limits of such virtualization for various physical layer functions that could leverage such an architecture.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343387"}, {"primary_key": "3092889", "vector": [], "sparse_vector": [], "title": "Blind Distributed MU-MIMO for IoT Networking over VHF Narrowband Spectrum.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Longer range, in rural/urban IoT networks, allow a large geographical coverage with only a few base-stations, making their deployment and operation economical. In this paper we explore the 150-174 MHz spectrum for long range IoT networks comprising unlicensed MURS and licensed VHF narrowbands. Range in these bands is boosted by the lower RF frequencies as well as higher transmit powers allowed by the FCC. Through a 400 sq km wide area deployment study, we show that, these spectrum bands can provide > 20× the geographical coverage than that in the 900 MHz ISM band LoRa. Increased range translates to greater uplink IoT device traffic. The key contribution of this paper is a novel technique - Blind Distributed MU-MIMO, that allows capacity to scale with the number of antennas (base- stations) while not requiring any coordinated channel measurements between the devices and IoT base- stations. This requirement is crucial since in IoT networks power constrained IoT devices typically sleep and wake up to transmit short messages in response to unpredictable events without any coordination with the base-stations. We demonstrate the efficacy of Blind Distributed MU-MIMO through a real wide area deployment.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345427"}, {"primary_key": "3092890", "vector": [], "sparse_vector": [], "title": "Poster: Enhancing Capacity in Multi-hop Wireless Networks by Joint Node Units.", "authors": ["<PERSON><PERSON>", "<PERSON>ns<PERSON><PERSON> Tan", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Achievable capacity in multi-hop wireless networks is seriously lower than single-hop communication. Two full-duplex nodes potentially have 2× capacity in wireless communications, compared to two half-duplex nodes. Organize the two nodes as one unit and reorganize nodes to be the units in multi-hop paths, the capacity can achieve 1× to 2× under space division simultaneous transmission mode, which is verified by analysis and simulation results.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343390"}, {"primary_key": "3092892", "vector": [], "sparse_vector": [], "title": "Challenge: Unlicensed LPWANs Are Not Yet the Path to Ubiquitous Connectivity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low-power wide-area networks (LPWANs) are a compelling answer to the networking challenges faced by many Internet of Things devices. Their combination of low power, long range, and deployment ease has motivated a flurry of research, including exciting results on backscatter and interference cancellation that further lower power budgets and increase capacity. But despite the interest, we argue that unlicensed LPWAN technologies can only serve a narrow class of Internet of Things applications due to two principal challenges: capacity and coexistence. We propose a metric, bit flux, to describe networks and applications in terms of throughput over a coverage area. Using bit flux, we find that the combination of low bit rate and long range restricts the use case of LPWANs to sparse sensing applications. Furthermore, this lack of capacity leads networks to use as much available bandwidth as possible, and a lack of coexistence mechanisms causes poor performance in the presence of multiple, independently-administered networks. We discuss a variety of techniques and approaches that could be used to address these two challenges and enable LPWANs to achieve the promise of ubiquitous connectivity.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345444"}, {"primary_key": "3092897", "vector": [], "sparse_vector": [], "title": "4 Systems Perspectives into Human-Centered Machine Learning.", "authors": ["<PERSON>"], "summary": "Machine learning (ML) has had a tremendous impact in across the world over the last decade. As we think about ML solving complex tasks, sometimes at super-human levels, it is easy to forget that there is no machine learning without humans in the loop. Humans define tasks and metrics, develop and program algorithms, collect and label data, debug and optimize systems, and are (usually) ultimately the users of the ML-based applications we are developing. In this talk, we will cover 4 human-centered perspectives in the ML development process, along with methods and systems, to empower humans to maximize the ultimate impact of their ML-based applications. In particular, we will cover: 1. Developer tools for ML that allow a wider range of people to create intelligent applications?, focusing on mobile devices. 2. Learning to optimize the performance and power of ML models on a wide range of hardware backends and mobile devices. 3. Closing the gap between the loss function we optimize in ML and the product metrics we really want to optimize. 4. Helping humans understand why ML models make each prediction, when these models will break, and how to improve them.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3356017"}, {"primary_key": "3092898", "vector": [], "sparse_vector": [], "title": "Canceling Inaudible Voice Commands Against Voice Control Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies show that the voice control system (VCS) is subject to the inaudible voice command attack, which can not be heard by human ears but can be recorded by the microphone. An adversary could leverage the attack to disable the VCS user's home security system, leak the victim's privacy or download malware stealthily. Efforts have been dedicated to developing forensics based defense mechanisms, which target at detecting traces of the attack signal; however, we find that existing approaches of the kind still leave loopholes. Moreover, a complete defense mechanism should be able to not only detect the attack but also cancel out the attack signal, and meanwhile ensure the legitimate voice commands unaffected, which however is still unavailable to the best of our knowledge. This paper is an attempt to fill the gap. We first systematically analyze existing forensics based defense mechanisms and reveal the root cause of their loopholes. Then we present an active inaudible-voice-command cancellation (AIC) design, which can reliably detect and capture the attack signal facilitated by our custom-designed \"guard'' signal transmitter. AIC can create a special spectrum in the passband of the VCS microphone, based on which we are able to neutralize the attack signal in software means. We implement a prototype of our defense system and conduct comprehensive experiments to validate our design.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345429"}, {"primary_key": "3092899", "vector": [], "sparse_vector": [], "title": "Diagnosing Vehicles with Automotive Batteries.", "authors": ["<PERSON>", "Linghe Kong", "<PERSON><PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON>"], "summary": "The automotive industry is increasingly employing software- based solutions to provide value-added features on vehicles, especially with the coming era of electric vehicles and autonomous driving. The ever-increasing cyber components of vehicles (i.e., computation, communication, and control), however, incur new risks of anomalies, as demonstrated by the millions of vehicles recalled by different manufactures. To mitigate these risks, we design B-Diag, a battery-based diagnostics system that guards vehicles against anomalies with a cyber-physical approach, and implement B-Diag as an add-on module of commodity vehicles attached to automotive batteries, thus providing vehicles an additional layer of protection. B-Diag is inspired by the fact that the automotive battery operates in strong dependency with many physical components of the vehicle, which is observable as correlations between battery voltage and the vehicle's corresponding operational parameters, e.g., a faster revolutions-per-minute (RPM) of the engine, in general, leads to a higher battery voltage. B-Diag exploits such physically-induced correlations to diagnose vehicles by cross-validating the vehicle information with battery voltage, based on a set of data-driven norm models constructed online. Such a design of B-Diag is steered by a dataset collected with a prototype system when driving a 2018 Subaru Crosstrek in real-life over 3 months, covering a total mileage of about 1, 400 miles. Besides the Crosstrek, we have also evaluated B-Diag with driving traces of a 2008 Honda Fit, a 2018 Volvo XC60, and a 2017 Volkswagen Passat, showing B-Diag detects vehicle anomalies with >86% (up to 99%) averaged detection rate.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300126"}, {"primary_key": "3092900", "vector": [], "sparse_vector": [], "title": "Demo: TinySDR, A Software-Defined Radio Platform for Internet of Things.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wireless protocol design for IoT networks is an active area of research. We demonstrate tinySDR which is a low-power software-defined radio platform tailored to the needs of IoT endpoints. TinySDR is a standalone, fully programmable software-defined radio platform which has the requirements of IoT protocols. We present the physical layer implementations of BLE beacon and LoRa protocols to demonstrate the capabilities of tinySDR.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343373"}, {"primary_key": "3092902", "vector": [], "sparse_vector": [], "title": "SignSpeaker: A Real-time, High-Precision SmartWatch-based Sign Language Translator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sign language is a natural and fully-formed communication method for deaf or hearing-impaired people. Unfortunately, most of the state-of-the-art sign recognition technologies are limited by either high energy consumption or expensive device costs and have a difficult time providing a real-time service in a daily-life environment. Inspired by previous works on motion detection with wearable devices, we propose Sign Speaker - a real-time, robust, and user-friendly American sign language recognition (ASLR) system with affordable and portable commodity mobile devices. SignSpeaker is deployed on a smartwatch along with a smartphone; the smartwatch collects the sign signals and the smartphone outputs translation through an inbuilt loudspeaker. We implement a prototype system and run a series of experiments that demonstrate the promising performance of our system. For example, the average translation time is approximately $1.1$ seconds for a sentence with eleven words. The average detection ratio and reliability of sign recognition are 99.2% and 99.5%, respectively. The average word error rate of continuous sentence recognition is 1.04% on average.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300117"}, {"primary_key": "3092905", "vector": [], "sparse_vector": [], "title": "Living IoT: A Flying Wireless Platform on Live Insects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sensor networks with devices capable of moving could enable applications ranging from precision irrigation to environmental sensing. Using mechanical drones to move sensors, however, severely limits operation time since flight time is limited by the energy density of current battery technology. We explore an alternative, biology-based solution: integrate sensing, computing and communication functionalities onto live flying insects to create a mobile IoT platform. Such an approach takes advantage of these tiny, highly efficient biological insects which are ubiquitous in many outdoor ecosystems, to essentially provide mobility for free. Doing so however requires addressing key technical challenges of power, size, weight and self-localization in order for the insects to perform location-dependent sensing operations as they carry our IoT payload through the environment. We develop and deploy our platform on bumblebees which includes backscatter communication, low-power self-localization hardware, sensors, and a power source. We show that our platform is capable of sensing, backscattering data at 1 kbps when the insects are back at the hive, and localizing itself up to distances of 80 m from the access points, all within a total weight budget of 102 mg.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300136"}, {"primary_key": "3092906", "vector": [], "sparse_vector": [], "title": "Poster: Hawkeye - Predictive Positioning of a Ceiling-Mounted Mobile AP in mmWave WLANs for Maximizing Line-of-sight.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Line-of-sight (LOS) is a critical requirement for mmWave communication. In this work, we make the case for a ceilingmounted mobile (CMM) AP by comparing its performance with other types of AP mobility and single static AP. We then present <PERSON><PERSON> to solve the optimal location discovery problem for a CMM AP using a machine learning (ML) algorithm. <PERSON><PERSON> relies purely on the connectivity matrix between STAs and the AP to decide if and where the AP should move to for maximizing LOS connectivity. Using a prototype implementation, we show that the throughput of <PERSON><PERSON> is 219% and 129% compared with single static AP and other approaches for AP mobility, respectively.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343395"}, {"primary_key": "3092908", "vector": [], "sparse_vector": [], "title": "Software-Defined Cooking using a Microwave Oven.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite widespread popularity, today's microwave ovens are limited in their cooking capabilities, given that they heat food blindly, resulting in a non-uniform and unpredictable heating distribution. We present SDC (software-defined cooking), a low-cost closed-loop microwave oven system that aims to heat the food in a software-defined thermal trajectory. SDC achieves this through a novel high-resolution heat sensing and actuation system that uses microwave-safe components to augment existing microwaves. SDC first senses thermal gradient by using arrays of neon lamps that are charged by the Electromagnetic (EM) field a microwave produces. SDC then modifies the EM-field strength to desired levels by accurately moving food on a programmable turntable towards sensed hot and cold spots. To create a more skewed arbitrary thermal pattern, SDC further introduces two types of programmable accessories: microwave shield and susceptor. We design and implement one experimental test-bed by modifying a commercial off-the-shelf microwave oven. Our evaluation shows that SDC can programmatically create temperature deltas at a resolution of 21 degrees with a spatial resolution of 3 cm without accessories and 183 degrees with the help of accessories. We further demonstrate how a SDC-enabled microwave can be enlisted to perform unexpected cooking tasks: cooking meat and fat in bacon discriminatively and heating milk uniformly.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345441"}, {"primary_key": "3092909", "vector": [], "sparse_vector": [], "title": "Software-Defined Cooking (SDC) using a Microwave Oven.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a demonstration of SDC, a low-cost closed-loop microwave oven system that aims to heat the food in a software-defined thermal trajectory. SDC achieves this through a novel high-resolution heat sensing and actuation system that uses microwave-safe components to augment existing microwaves. In this demo, we demonstrate our experimental test-bed, a modified commercial off-the-shelf microwave oven, and show a SDC-enabled microwave can be enlisted to perform unexpected cooking tasks: cooking meat and fat in bacon discriminatively and heating rice uniformly.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343368"}, {"primary_key": "3092910", "vector": [], "sparse_vector": [], "title": "AMP up your Mobile Web Experience: Characterizing the Impact of Google&apos;s Accelerated Mobile Project.", "authors": ["Byungjin Jun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The rapid growth in the number of mobile devices, subscriptions and their associated traffic, has served as motivation for several projects focused on improving mobile users' quality of experience (QoE). Few have been as contentious as the Google-initiated Accelerated Mobile Project (AMP), both praised for its seemingly instant mobile web experience and criticized based on concerns about the enforcement of its formats. This paper presents the first characterization of AMP's impact on users' QoE. We do this using a corpus of over 2,100 AMP webpages, and their corresponding non-AMP counterparts, based on trendy-keyword-based searches. We characterized AMP's impact looking at common web QoE metrics, including Page Load Time, Time to First Byte and SpeedIndex (SI). Our results show that AMP significantly improves SI, yielding on average a 60% lower SI than non-AMP pages without accounting for prefetching. Prefetching of AMP pages pushes this advantage even further, with prefetched pages loading over 2,000ms faster than non-prefetched AMP pages. This clear boost may come, however, at a non-negligible cost for users with limited data plans as it incurs an average of over 1.4~MB of additional data downloaded, unbeknownst to users.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300137"}, {"primary_key": "3092911", "vector": [], "sparse_vector": [], "title": "Fire in Your Hands: Understanding Thermal Behavior of Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sooyoung Park", "Chunjong Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Overheating smartphones could hamper user experiences. While there have been numerous reports on smartphone overheating, a systematic measurement and user experience study on the thermal aspect of smartphones is missing. Using thermal imaging cameras, we measure and analyze the temperatures of various smartphones running diverse application workloads such as voice calling, video recording, video chatting, and 3D online gaming. Our experiments show that running popular applications such as video chat, could raise the smartphone's surface temperature to over 50$^\\circ$C in only 10 minutes, which could easily cause thermal pain to users. Recent ubiquitous scenarios such as augmented reality and mobile deep learning also have considerable thermal issues. We then perform a user study to examine when the users perceive heat discomfort from the smartphones and how they react to overheating. Most of our user study participants reported considerable thermal discomfort while playing a mobile game, and that overheating disrupted interaction flows. With this in mind, we devise a smartphone surface temperature prediction model, by using only system statistics and internal sensor values. Our evaluation showed high prediction accuracy with root-mean-square errors of less than 2$^\\circ$C. We discuss several insights from our findings and recommendations for user experience, OS design, and developer support for better user-thermal interactions.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300128"}, {"primary_key": "3092912", "vector": [], "sparse_vector": [], "title": "Demo: Wireless LAN Emulator Using Wireless Network Tap Device for Testing a Vehicular Network System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In order to improve the reliability of dynamic wireless LAN (WLAN) network systems such as a vehicular network system for traffic accident prevention, testing the system software is important. Network emulation enables to test the practical software of the system without building a physical network. Many existing network emulators are built by using a virtual Ethernet device named TUN/TAP device for enabling a network simulator to capture Ethernet frames. However, the network emulation with the TUN/TAP device cannot emulate the operation of the WLAN protocols such as ETSI DCC, which dynamically changes transmission power and interval based on receive signal strength indication (RSSI), because the TUN/TAP device cannot capture IEEE 802.11 frames and control parameters such as transmission power, RSSI. In previous work, we developed a wireless network tap device (wtap80211) and proposed a vehicular network emulator using wtap80211. In the demonstration, we will demonstrate the capability of our emulator using its prototype.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343376"}, {"primary_key": "3092917", "vector": [], "sparse_vector": [], "title": "Poster: SeamFarm - Distributed Data Analytic for Precision Agriculture based on Seamless Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This work proposes a framework for distributed data analytic for precision agriculture based on seamless computing paradigm named SeamFarm. Generally, heterogeneous nodes deployed for precision agriculture where these nodes generated an extensive amount of data. Then machine learning can be used to analyze this data for precision agriculture. However, most of the IoT devices are resource-constrained devices, which results in poor performance while conducting a machine learning task. Thus, in SeamFarm, we consider distributing the data as well as the task to all available nodes. The results show that SeamFarm can meet all of the functional and non-functional requirements of distributed data analytic for precision agriculture. Moreover, it can obtain faster data analytic results.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343400"}, {"primary_key": "3092918", "vector": [], "sparse_vector": [], "title": "XModal-ID: Using WiFi for Through-Wall Person Identification from Candidate Video Footage.", "authors": ["<PERSON><PERSON>", "Chitra R. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose XModal-ID, a novel WiFi-video cross-modal gait-based person identification system. Given the WiFi signal measured when an unknown person walks in an unknown area and a video footage of a walking person in another area, XModal-ID can determine whether it is the same person in both cases or not. XModal-ID only uses the Channel State Information (CSI) magnitude measurements of a pair of off-the-shelf WiFi transceivers. It does not need any prior wireless or video measurement of the person to be identified. Similarly, it does not need any knowledge of the operation area or person's track. Finally, it can identify people through walls. XModal-ID utilizes the video footage to simulate the WiFi signal that would be generated if the person in the video walked near a pair of WiFi transceivers. It then uses a new processing approach to robustly extract key gait features from both the real WiFi signal and the video-based simulated one, and compares them to determine if the person in the WiFi area is the same person in the video. We extensively evaluate XModal-ID by building a large test set with $8$ subjects, $2$ video areas, and $5$ WiFi areas, including 3 through-wall areas as well as complex walking paths, all of which are not seen during the training phase. Overall, we have a total of 2,256 WiFi-video test pairs. XModal-ID then achieves an $85%$ accuracy in predicting whether a pair of WiFi and video samples belong to the same person or not. Furthermore, in a ranking scenario where XModal-ID compares a WiFi sample to $8$ candidate video samples, it obtains top-1, top-2, and top-3 accuracies of $75%$, $90%$, and $97%$. These results show that XModal-ID can robustly identify new people walking in new environments, in various practical scenarios.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345437"}, {"primary_key": "3092920", "vector": [], "sparse_vector": [], "title": "Poster: While You Were Sleeping - Time-Shifted Prefetching of YouTube Videos to Reduce Peak-time Cellular Data Usage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The load on wireless cellular networks is not uniformly distributed through the day, and is significantly higher during peak-times. In this context, we present a time-shifted prefetching solution that prefetches content during off-peak periods of network connectivity. We specifically focus on YouTube as it represents a significant portion of overall cellular data-usage. We make the following contributions: first, we establish that a significant portion of a user's YouTube watch behavior is indeed predictable by analyzing a real-life dataset of YouTube watch history spanning a 1-year period, from 206 users comprised of over 1.8 million videos; second, we develop an accurate prediction algorithm using a K-nearest neighbor classifier approach; and finally, we evaluate the prefetching algorithm on two different datasets and show that MANTIS is able to reduce the traffic during peak periods by 34% for a typical user.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343393"}, {"primary_key": "3092922", "vector": [], "sparse_vector": [], "title": "Occlumency: Privacy-preserving Remote Deep-learning Inference Using SGX.", "authors": ["Taegyeong Lee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Caihua Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep-learning (DL) is receiving huge attention as enabling techniques for emerging mobile and IoT applications. It is a common practice to conduct DNN model-based inference using cloud services due to their high computation and memory cost. However, such a cloud-offloaded inference raises serious privacy concerns. Malicious external attackers or untrustworthy internal administrators of clouds may leak highly sensitive and private data such as image, voice and textual data. In this paper, we propose Occlumency, a novel cloud-driven solution designed to protect user privacy without compromising the benefit of using powerful cloud resources. Occlumency leverages secure SGX enclave to preserve the confidentiality and the integrity of user data throughout the entire DL inference process. DL inference in SGX enclave, however, impose a severe performance degradation due to limited physical memory space and inefficient page swapping. We designed a suite of novel techniques to accelerate DL inference inside the enclave with a limited memory size and implemented Occlumency based on Caffe. Our experiment with various DNN models shows that Occlumency improves inference speed by 3.6x compared to the baseline DL inference in SGX and achieves a secure DL inference within 72% of latency overhead compared to inference in the native environment.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345447"}, {"primary_key": "3092923", "vector": [], "sparse_vector": [], "title": "MobiSR: Efficient On-Device Super-Resolution through Heterogeneous Mobile Processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Lu<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, convolutional networks have demonstrated unprecedented performance in the image restoration task of super-resolution (SR). SR entails the upscaling of a single low-resolution image in order to meet application-specific image quality demands and plays a key role in mobile devices. To comply with privacy regulations and reduce the overhead of cloud computing, executing SR models locally on-device constitutes a key alternative approach. Nevertheless, the excessive compute and memory requirements of SR workloads pose a challenge in mapping SR networks on resource-constrained mobile platforms. This work presents MobiSR, a novel framework for performing efficient super-resolution on-device. Given a target mobile platform, the proposed framework considers popular model compression techniques and traverses the design space to reach the highest performing trade-off between image quality and processing speed. At run time, a novel scheduler dispatches incoming image patches to the appropriate model-engine pair based on the patch's estimated upscaling difficulty in order to meet the required image quality with minimum processing latency. Quantitative evaluation shows that the proposed framework yields on-device SR designs that achieve an average speedup of 2.13x over highly-optimized parallel difficulty-unaware mappings and 4.79x over highly-optimized single compute engine implementations.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345455"}, {"primary_key": "3092924", "vector": [], "sparse_vector": [], "title": "Poster: MobiSR - Efficient On-Device Super-Resolution through Heterogeneous Mobile Processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Lu<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, convolutional networks have demonstrated unprecedented performance in the image restoration task of super-resolution (SR). SR entails the upscaling of a single low-resolution image in order to meet application-specific image quality demands and plays a key role in mobile devices. To comply with privacy regulations and reduce the overhead of cloud computing, executing SR models locally on-device constitutes a key alternative approach. Nevertheless, the excessive compute and memory requirements of SR workloads pose a challenge in mapping SR networks on resource-constrained mobile platforms. This work presents MobiSR, a novel framework for performing efficient super-resolution on-device. Given a target mobile platform, the proposed framework considers popular model compression techniques and traverses the design space to reach the highest performing trade-off between image quality and processing speed. At run time, a novel scheduler dispatches incoming image patches to the appropriate model-engine pair based on the patch's estimated upscaling difficulty in order to meet the required image quality with minimum processing latency. Quantitative evaluation shows that the proposed framework yields on-device SR designs that achieve an average speedup of 2.13x over highly-optimized parallel difficulty-unaware mappings and 4.79x over highly-optimized single compute engine implementations.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343410"}, {"primary_key": "3092926", "vector": [], "sparse_vector": [], "title": "Touch Well Before Use: Intuitive and Secure Authentication for IoT Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet of Things (IoT) are densely deployed in smart environments, such as homes, factories and laboratories, where many people have physical access to IoT devices. How to authenticate users operating on these devices is thus an important problem. IoT devices usually lack conventional user interfaces, such as keyboards and mice, which makes traditional authentication methods inapplicable. We present a virtual sensing technique that allows IoT devices to virtually sense user 'petting' (in the form of some very simple touches for about 2 seconds) on the devices. Based on this technique, we build a secure and intuitive authentication method that authenticates device users by comparing the petting operations sensed by devices and those captured by the user wristband. The authentication method is highly secure as physical operations are required, rather than based on proximity. It is also intuitive, adopting very simple authentication operations, e.g., clicking buttons, twisting rotary knobs, and swiping touchscreens. Unlike the state-of-the-art methods, our method does not require any hardware modifications of devices, and thus can be applied to commercial off-the-shelf (COTS) devices. We build prototypes and evaluate them comprehensively, demonstrating their high effectiveness, security, usability, and efficiency.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345434"}, {"primary_key": "3092927", "vector": [], "sparse_vector": [], "title": "Rebooting Ultrasonic Positioning Systems for Ultrasound-incapable Smart Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lin", "Z<PERSON><PERSON> An", "<PERSON><PERSON>"], "summary": "An ultrasonic Positioning System (UPS) has outperformed RF-based systems in terms of its accuracy for years. However, few of the developed solutions have been deployed in practice to satisfy the localization demand of today's smart devices, which lack ultrasonic sensors and were considered as being \"deaf'' to ultrasound. A recent finding demonstrates that ultrasound may be audible to the smart devices under certain conditions due to their microphone's nonlinearity. Inspired by this insight, this work revisits the ultrasonic positioning technique and builds a practical UPS, called UPS+, for ultrasound-incapable smart devices. The core concept is to deploy two types of indoor beacon devices, which will advertise ultrasonic beacons at two different ultrasonic frequencies respectively. Their superimposed beacons are shifted to a low-frequency by virtue of the nonlinearity effect at the receiver's microphone. This underlying property functions as an implicit ultrasonic downconverter without throwing harm to the hearing system of humans. We demonstrate UPS+, a fully functional UPS prototype, with centimeter-level localization accuracy using custom-made beacon hardware and well-designed algorithms.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300139"}, {"primary_key": "3092928", "vector": [], "sparse_vector": [], "title": "Poster: Edge-cloud Enhancement - Latency-aware Virtual Cluster Placement for Supporting Cloud Applications in Mobile Edge Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile edge networks benefit cloud applications in particularly providing a shorter response latency for mobile terminals. However, the cumulative increase of mobile terminals and emerging cloud applications, poses new challenges for edge networks. To combat this issue, the promising idea of mobile micro-clouds (MMCs) is proposed to enhance edges and the cloud. In this paper, we investigate the problem of virtual cluster (VC) placement in MMCs, to minimize the average response latency with various requests among multiple cloud applications. Then a hybrid swarm intelligence approach is proposed to optimize VC placement scheme, for a trade-off between the average response latency and the overall VC placement cost. The preliminary evaluation results show the effectiveness and efficiency of our approach.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343388"}, {"primary_key": "3092930", "vector": [], "sparse_vector": [], "title": "Edge Assisted Real-time Object Detection for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON>", "Hongyu Li", "<PERSON>"], "summary": "Most existing Augmented Reality (AR) and Mixed Reality (MR) systems are able to understand the 3D geometry of the surroundings but lack the ability to detect and classify complex objects in the real world. Such capabilities can be enabled with deep Convolutional Neural Networks (CNN), but it remains difficult to execute large networks on mobile devices. Offloading object detection to the edge or cloud is also very challenging due to the stringent requirements on high detection accuracy and low end-to-end latency. The long latency of existing offloading techniques can significantly reduce the detection accuracy due to changes in the user's view. To address the problem, we design a system that enables high accuracy object detection for commodity AR/MR system running at 60fps. The system employs low latency offloading techniques, decouples the rendering pipeline from the offloading pipeline, and uses a fast object tracking method to maintain detection accuracy. The result shows that the system can improve the detection accuracy by 20.2%-34.8% for the object detection and human keypoint detection tasks, and only requires 2.24ms latency for object tracking on the AR device. Thus, the system leaves more time and computational resources to render virtual elements for the next frame and enables higher quality AR/MR experiences.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300116"}, {"primary_key": "3092931", "vector": [], "sparse_vector": [], "title": "Poster: Video Chat Scam Detection Leveraging Screen Light Reflection.", "authors": ["Hongbo Liu", "<PERSON><PERSON><PERSON>", "Yucheng Xie", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid advancement of social media and communication technology enables video chat to become an important and convenient way of daily communication. However, such convenience also makes personal video clips easily obtained and exploited by malicious users who launch scam attacks. Existing studies only deal with the attacks that use fabricated facial masks, while the liveness detection that targets the playback attacks using a virtual camera is still elusive. In this work, we develop a novel video chat liveness detection system, which can track the weak light changes reflected off the skin of a human face leveraging chromatic eigenspace differences. We design an inconspicuous challenge frame with minimal intervention to the video chat and develop a robust anomaly frame detector to verify the liveness of remote user in a video chat session. Furthermore, we propose a resilient defense strategy to defeat both naive and intelligent playback attacks leveraging spatial and temporal verification. The evaluation results show that our system can achieve accurate and robust liveness detection with the accuracy and false detection rate as high as 97.7% (94.8%) and 1% (1.6%) on smartphones (laptops), respectively.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343403"}, {"primary_key": "3092933", "vector": [], "sparse_vector": [], "title": "On-Off Noise Power Communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We design and build a protocol called on-off noise power communication (ONPC), which modifies the software in commodity packet radios to allow communication, independent of their standard protocol, at a very slow rate at long range. To achieve this long range, we use the transmitter as an RF power source that can be on or off if it does or does not send a packet, respectively, and a receiver that repeatedly measures the noise and interference power level. We use spread spectrum techniques on top of the basic on/off mechanism to overcome the interference caused by other devices' channel access to provide long ranges at a much lower data rate. We implement the protocol on top of commodity WiFi hardware. We discuss our design and how we overcome key challenges such as non-stationary interference, carrier sensing and hardware timing delays. We test ONPC in several situations to show that it achieves significantly longer range than standard WiFi.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345436"}, {"primary_key": "3092934", "vector": [], "sparse_vector": [], "title": "SolarGest: Ubiquitous and Battery-free Gesture Recognition using Solar Cells.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We design a system, SolarGest, which can recognize hand gestures near a solar-powered device by analyzing the patterns of the photocurrent. SolarGest is based on the observation that each gesture interferes with incident light rays on the solar panel in a unique way, leaving its distinguishable signature in harvested photocurrent. Using solar energy harvesting laws, we develop a model to optimize design and usage of SolarGest. To further improve the robustness of SolarGest under non-deterministic operating conditions, we combine dynamic time warping with Z-score transformation in a signal processing pipeline to pre-process each gesture waveform before it is analyzed for classification. We evaluate SolarGest with both conventional opaque solar cells as well as emerging see-through transparent cells. Our experiments with 6,960 gesture samples for 6 different gestures reveal that even with transparent cells, SolarGest can detect 96% of the gestures while consuming 44% less power compared to light sensor based systems.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300129"}, {"primary_key": "3092936", "vector": [], "sparse_vector": [], "title": "RNN-Based Room Scale Hand Motion Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart speakers allow users to interact with home appliances using voice commands and are becoming increasingly popular. While voice-based interface is intuitive, it is insufficient in many scenarios, such as in noisy or quiet environments, for users with language barriers, or in applications that require continuous motion tracking. Motion-based control is attractive and complementary to existing voice-based control. However, accurate and reliable room-scale motion tracking poses a significant challenge due to low SNR, interference, and varying mobility. To this end, we develop a novel recurrent neural network (RNN) based system that uses speakers and microphones to realize accurate room-scale tracking. Our system jointly estimates the propagation distance and angle-of-arrival (AoA) of signals reflected by the hand, based on AoA-distance profiles generated by 2D MUSIC. We design a series of techniques to significantly enhance the profile quality under low SNR. We feed the profiles in a recent history to our RNN to estimate the distance and AoA. In this way, we can exploit the temporal structure among consecutive profiles to remove the impact of noise, interference and mobility. Using extensive evaluation, we show our system achieves 1.2--3.7~cm error within 4.5~m range, supports tracking multiple users, and is robust against ambient sound. To our knowledge, this is the first acoustic device-free room-scale tracking system.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345439"}, {"primary_key": "3092937", "vector": [], "sparse_vector": [], "title": "Human-Machine and Human-Robot Interaction for Long-Term User Engagement and Behavior Change.", "authors": ["<PERSON><PERSON>"], "summary": "The nexus of in-home intelligent assistants, activity tracking, and machine learning creates opportunities for personalized virtual and physical agents / robots that can positively impacts user health and quality of life. Well beyond providing information, such agents can serve as physical and mental health and education coaches and companions that support positive behavior change. However, sustaining user engagement and motivation over long-term interactions presents complex challenges. Our work over the past 15 years has addressed those challenges by developing human-machine (human-robot) interaction methods for socially assistive robotics that utilize multi-modal interaction data and expressive agent behavior to monitor, coach, and motivate users to engage in heath- and wellness-promoting activities. This talk will present methods and results of modeling, learning, and personalizing user motivation, engagement, and coaching of healthy children and adults, as well as stroke patients, Alzheimer's patients, and children with autism spectrum disorders, in short and long-term (month+) deployments in schools, therapy centers, and homes, and discuss research and commercial implications for technologies aimed at human daily use.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300141"}, {"primary_key": "3092941", "vector": [], "sparse_vector": [], "title": "MP-H2: A Client-only Multipath Solution for HTTP/2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MP-H2 is a client-only, HTTP-based multipath solution. It enables an HTTP client to fetch content (an HTTP object) over multiple network paths such as WiFi and cellular on smartphones. Compared to MPTCP, MP-H2 offers several key advantages including server transparency, middlebox compatibility, and friendliness to CDN, anycast, and load balancing. MP-H2 strategically splits the file into byte range requests sent over multipath, and dynamically balances the workload across all paths. Furthermore, MP-H2 leverages new features in HTTP/2 including stream multiplexing, flow control, and application-layer PING to boost the performance. MP-H2 also supports multi-homing where each path contacts a different CDN server for enhanced performance. Evaluations show that MP-H2 offers only slightly degraded performance (6% on average) while being much easier to deploy compared to MPTCP. Compared to other state-of-the-art HTTP multipath solutions, MP-H2 reduces the file download time by up to 47%, and increases the DASH video streaming bitrate by up to 44%.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300131"}, {"primary_key": "3092942", "vector": [], "sparse_vector": [], "title": "FLUID: Flexible User Interface Distribution for Ubiquitous Multi-device Interaction.", "authors": ["<PERSON><PERSON><PERSON> Oh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "Insik Shin"], "summary": "The growing trend of multi-device ownerships creates a need and an opportunity to use applications across multiple devices. However, in general, the current app development and usage still remain within the single-device paradigm, falling far short of user expectations. For example, it is currently not possible for a user to dynamically partition an existing live streaming app with chatting capabilities across different devices, such that she watches her favorite broadcast on her smart TV while real-time chatting on her smartphone. In this paper, we present FLUID, a new Android-based multi-device platform that enables innovative ways of using multiple devices. FLUID aims to i) allow users to migrate or replicate individual user interfaces (UIs) of a single app on multiple devices (high flexibility), ii) require no additional development effort to support unmodified, legacy applications (ease of development), and iii) support a wide range of apps that follow the trend of using custom-made UIs (wide applicability). Previous approaches, such as screen mirroring, app migration, and customized apps utilizing multiple devices, do not satisfy those goals altogether. FLUID, on the other hand, meets the goals by carefully analyzing which UI states are necessary to correctly render UI objects, deploying only those states on different devices, supporting cross-device function calls transparently, and synchronizing the UI states of replicated UI objects across multiple devices. Our evaluation with 20 unmodified, real-world Android apps shows that FLUID can transparently support a wide range of apps and is fast enough for interactive use.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345443"}, {"primary_key": "3092943", "vector": [], "sparse_vector": [], "title": "FLUID: Multi-device Mobile Platform for Flexible User Interface Distribution.", "authors": ["<PERSON><PERSON><PERSON> Oh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "Insik Shin"], "summary": "The growing trend of multi-device ownerships creates a need and an opportunity to use applications across multiple devices. However, in general, the current app development and usage still remain within the single-device paradigm, falling far short of user expectations. We present FLUID, a new multi-device platform that allows users to migrate or replicate individual user interfaces (UIs) of a single app on multiple devices. In addition, FLUID aims to require no extra development effort to support a wide range of legacy apps that follow the trend of using custom-made UIs. To this end, FLUID analyzes which UI states are necessary to correctly render UI objects, deploys only those states on different devices, and supports cross-device function calls transparently. In this demo, we demonstrate several interesting use cases supported by our Android-based FLUID prototype.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343366"}, {"primary_key": "3092945", "vector": [], "sparse_vector": [], "title": "mQRCode: Secure QR Code Using Nonlinearity of Spatial Frequency in Light.", "authors": ["<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiaoyu Ji"], "summary": "Quick response (QR) codes are becoming pervasive due to their rapid readability and the popularity of smartphones with built-in cameras. QR codes are also gaining importance in the retail sector as a convenient mobile payment method. However, researchers have concerns regarding the security of QR codes, which leave users susceptible to financial loss or private information leakage. In this study, we addressed this issue by developing a novel QR code (called mQRCode), which exploits patterns presenting a specific spatial frequency as a form of camouflage. When the targeted receiver holds a camera in a designated position (e.g., directly in front at a distance of 30 cm from the camouflaged QR code), the original QR code is revealed in form of a Moire pattern. From any other position, only the camouflaged QR code can be seen. In experiments, the decryption rate of mQRCode was > 98.6% within 10.2 frames via a multi-frame decryption method. The decryption rate for cameras positioned 20° off axis or > 10cm away from the designated location dropped to 0%, indicating that mQRCode is robust against attacks.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345428"}, {"primary_key": "3092946", "vector": [], "sparse_vector": [], "title": "Poster: Secure Visible Light Communication based on Nonlinearity of Spatial Frequency in Light.", "authors": ["<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiaoyu Ji", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Quick response (QR) codes are becoming pervasive due to their rapid readability and the popularity of smartphones with built-in cameras. QR codes are also gaining importance in the retail sector as a convenient mobile payment method. However, researchers have concerns regarding the security of QR codes, which leave users susceptible to financial loss or private information leakage. In this study, we address this issue by developing a novel QR code (called mQR code), which exploits patterns presenting a specific spatial frequency as a form of camouflage. When the targeted receiver holds a camera in a designated position (e.g., directly in front at a distance of 30 cm from the camouflaged QR code), the original QR code is revealed in form of a Moiré pattern. From any other position, only the camouflaged QR code can be seen. In experiments, the decryption rate of mQR codes is $> 98%$. The decryption rate for cameras positioned $20\\degree$ off axis or $> 10cm$ from the designated location drops to $0%$, indicating that any attackers will be unable to steal a usable image.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343391"}, {"primary_key": "3092947", "vector": [], "sparse_vector": [], "title": "WiNTECH&apos;19: Workshop on Wireless Network Testbeds, Experimental evaluation &amp; CHaracterization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, it clearly emerged an increasing importance of experimental validation of innovative wireless solutions and applications, due to the increasing complexity of the wireless ecosystem. The WiNTECH workshop explicitly deals with methodological and technical issues that have to be faced for defining, running, controlling and benchmarking experiments on wireless solutions. The workshop, that this year reach the 13th edition, has a consolidated tradition for bringing together an important number of researchers and industry players working in different aspects of experimental wireless communications. The workshop will serve as a forum for sharing experiences and results with real testbeds, experimental evaluation, prototyping and empirical characterization of wireless technologies.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355636"}, {"primary_key": "3092948", "vector": [], "sparse_vector": [], "title": "Proximity Detection with Single-Antenna IoT Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Providing secure communications between wireless devices that encounter each other on an ad-hoc basis is a challenge that has not yet been fully addressed. In these cases, close physical proximity among devices that have never shared a secret key is sometimes used as a basis of trust; devices in close proximity are deemed trustworthy while more distant devices are viewed as potential adversaries. Because radio waves are invisible, however, a user may believe a wireless device is communicating with a nearby device when in fact the user's device is communicating with a distant adversary. Researchers have previously proposed methods for multi-antenna devices to ascertain physical proximity with other devices, but devices with a single antenna, such as those commonly used in the Internet of Things, cannot take advantage of these techniques.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300120"}, {"primary_key": "3092950", "vector": [], "sparse_vector": [], "title": "SMAS&apos;19: 1st ACM Workshop on Emerging Smart Technologies and Infrastructures for Smart Mobility and Sustainability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One of the main global challenges to cope with for a sustainable development is transport and mobility. They are undisputed key issues of modern life affecting people's well-being and quality of life, which are significantly impacting the environment. In this scenario, innovative self-driving car technologies and electric solutions, emerging sensing technologies, and interconnected infrastructure can lead to more efficient transport systems and innovative mobility services, keeping into account the urgent need to foster a sustainable development. This workshop aims to gather practitioners with different background and different perspective from both Academia and Industry to further the knowledge in the area of emerging smart technologies and infrastructures for Smart Mobility and Sustainability.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355633"}, {"primary_key": "3092951", "vector": [], "sparse_vector": [], "title": "A Systematic Way to LTE Testing.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "LTE test cases are standardized by 3GPP. They must be executed on every LTE-capable device model before commercial release. In this work, we examine the LTE testing practices in terms of completeness and efficiency. We discover that the standardized tests are incomplete in that a number of test cases related to multiple protocol interactions are missing. Our analysis also shows that, the isolated treatment of test cases, but not from the system perspective, incurs repetitive executions of test operations, thus resulting in testing inefficiencies. We thus make a case for a paradigm shift from ad hoc testing to a methodical approach to LTE testing. We follow a few guidelines from the LTE standards and propose an algorithmic approach to systematic testing. In the process, we address various challenges, provide complete list of test cases, and present the related algorithms. Our evaluation shows that, by eliminating repetitive operations, our new scheme reduces up to 70% of LTE testing steps. We also find 87 new, yet valid test cases that are not defined by the LTE standards.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300134"}, {"primary_key": "3092953", "vector": [], "sparse_vector": [], "title": "Extracting 3D Maps from Crowdsourced GNSS Skyview Data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "3D maps of urban environments are useful in various fields ranging from cellular network planning to urban planning and climatology. These models are typically constructed using expensive techniques such as manual annotation with 3D modeling tools, extrapolated from satellite or aerial photography, or using specialized hardware with depth sensing devices. In this work, we show that 3D urban maps can be extracted from standard GNSS data, by analyzing the received satellite signals that are attenuated by obstacles, such as buildings. Furthermore, we show that these models can be extracted from low-accuracy GNSS data, crowdsourced opportunistically from standard smartphones during their user's uncontrolled daily commute trips, unleashing the potential of applying the principle to wide areas. Our proposal incorporates position inaccuracies in the calculations, and accommodates different sources of variability of the satellite signals' SNR. The diversity of collection conditions of crowdsourced GNSS positions is used to mitigate bias and noise from the data. A binary classification model is trained and evaluated on multiple urban scenarios using data crowdsourced from over 900 users. Our results show that the generalization accuracy for a Random Forest classifier in typical urban environments lies between 79% and 91% on 4 m wide voxels, demonstrating the potential of the proposed method for building 3D maps for wide urban areas.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345456"}, {"primary_key": "3092954", "vector": [], "sparse_vector": [], "title": "Demo: Extracting 3D Maps from Crowdsourced GNSS Skyview Data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "3D maps of urban environments are useful in various fields, from cellular network planning to urban planning and climatology. We show that 3D urban maps can be extracted from received satellite signals that are attenuated by obstacles, such as buildings, from low-accuracy GNSS data, crowdsourced opportunistically from standard smartphones during their user's uncontrolled daily commute trips. Our proposal incorporates position inaccuracies in the calculations, and the diversity of collection conditions of crowdsourced GNSS positions is used to mitigate bias and noise from the data. A binary classification model is trained and evaluated on multiple urban scenarios. Our results show that the generalization accuracy for a Random Forest classifier lies between 79% and 91%, demonstrating the potential of the proposed method for building 3D maps for wide urban areas. In the demo, we show multiple 3D visualizations of the various processing stages, which can be viewed interactively using Google Earth, allowing hands-on exploration of the work.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343381"}, {"primary_key": "3092955", "vector": [], "sparse_vector": [], "title": "MuSher: An Agile Multipath-TCP Scheduler for Dual-Band 802.11ad/ac Wireless LANs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Future WLAN devices will combine both IEEE 802.11ad and 802.11ac interfaces. The former provides multi-Gbps rates but is susceptible to blockage, whereas the latter is slower but offers reliable connectivity. A fundamental challenge is thus how to combine those complementary technologies, to make the most of the advantages they offer. In this work, we explore leveraging Multipath TCP (MPTCP) to use both interfaces simultaneously in order to achieve a higher overall throughput as well as seamlessly switching to a single interface when the other one fails. We find that standard MPTCP often performs sub-optimally and may even yield a throughput much lower than that of single path TCP over the faster of the two interfaces. We analyze the cause of these performance issues in detail and then design MuSher, an agile MPTCP scheduler that allows MPTCP to fully utilize the channel resources available to both interfaces. Our evaluation in realistic scenarios shows that MuSher provides a throughput improvement of up to 1.5x/2.3x and speeds up the recovery of a traffic stream, after disruption, by a factor of up to 8x/75x, under WLAN/Internet settings respectively, compared to the default MPTCP scheduler.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345435"}, {"primary_key": "3092956", "vector": [], "sparse_vector": [], "title": "Poster: FlexDP-Flexible Data Plane for ENFV.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We design a flexible data plane to integrate SDN and ENFV to manage complex network services for future mobile services. We build our prototype and show its feasibility in terms of latency and throughput by using mobile edge computing as an example use case in a realistic mobile networking testbed.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343382"}, {"primary_key": "3092960", "vector": [], "sparse_vector": [], "title": "Experiences: Design, Implementation, and Deployment of CoLTE, a Community LTE Solution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we introduce CoLTE, a solution for LTE-based community networks. CoLTE is a lightweight, Internet-only LTE core network (EPC) designed to facilitate the deployment and operation of small-scale, community owned and operated LTE networks in rural areas with limited and unreliable backhaul. The key differentiator of CoLTE, when compared to existing LTE solutions, is that in CoLTE the EPC is designed to be located in the field and deployed alongside a small number of cellular radios (eNodeBs), as opposed to the centralized model seen in large-scale telecom networks. We also provide performance results and lessons learned from a real-world CoLTE network deployed in rural Indonesia. This network has been sustainably operating for over six months, currently serves over 40 active users, and provides measured backhaul reductions of up to 45% when compared to cloud-core solutions.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345446"}, {"primary_key": "3092961", "vector": [], "sparse_vector": [], "title": "Demo: An All-in-One Community LTE Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We will introduce and demonstrate CoLTE, an all-in-one solution for LTE-based community networks. CoLTE is a lightweight, Internet-only LTE core network (EPC) based on OpenAirInterface. CoLTE is designed to facilitate the deployment and operation of small-scale, community owned and operated LTE networks, with a particular eye towards expanding Internet access into rural areas with limited and unreliable backhaul. CoLTE comes paired with a basic, IP-based network manager called <PERSON><PERSON>ge, as well as basic locally-hosted webservices. The key differentiator of CoLTE, when compared to existing LTE solutions, is that in CoLTE the EPC is designed to be located in the field and deployed alongside a small number of cellular radios (eNodeBs), as opposed to the centralized model seen in large-scale telecom networks.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343371"}, {"primary_key": "3092962", "vector": [], "sparse_vector": [], "title": "Device Administrator Use and Abuse in Android: Detection and Characterization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Device Administrator (DA) capabilities for mobile devices, e.g., remote locking/wiping, or enforcing password strength, were originally introduced to help organizations manage phone fleets or enable parental control. However, DA capabilities have been subverted and abused: malicious apps have used DA to create ransomware or lock users out, while benign apps have used DA to prevent or hinder uninstallation; in certain cases the only remedy is to factory-reset the phone. We call these apps \"Deathless Device Administrator\" (DDA), i.e., apps that cannot be uninstalled. We provide the first systematic study of Android DA capabilities, DDA apps, DDA-attack resistance across Android versions, and DDA-induced families in malicious apps. To enable scalable studies of questionable DA behavior, we developed DAAX, a static analyzer which exposes potential DA abuse effectively and efficiently. In a corpus of 39,459 apps (20,467 malicious and 18,992 benign) DAAX has found 4,135 DA apps and 691 potential DDA apps. The static analysis results on the 4,135 apps were cross-checked via dynamic analysis on at least 3 phones, confirming 578 true DDAs, including apps currently on Google Play. The study has shown that DAAX is effective (84.8% F-measure) and efficient (analysis typically takes 205 seconds per app).", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345452"}, {"primary_key": "3092963", "vector": [], "sparse_vector": [], "title": "Demo: A Practical Application of Visible Light Communication: Opportunistic Sharing of Encryption Keys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a demonstration of Jive (Joint Integration of VLC and Encryption), a novel encryption key sharing framework utilizing the emerging wireless technology Visible Light Communication (VLC). Based on the idea of transmitting data by modulating light, we are able to (1) share a secret key within a constrained physical space and (2) leverage this shared key to communicate encrypted information among co-located mobile devices. In this demonstration, we showcase our complete implementation of Jive: a VLC transmitter and a VLC receiver. Both endpoints are built using off-the-shelf components. The VLC link is used to distribute a randomly generated secret key that can only be \"observed\" by VLC receivers that are physically in the same space as the transmitter. Each receiver is connected over a serial connection to an Android device; we developed applications for Android that take the key from the VLC receiver and subsequently use the key to encrypt or decrypt application data. Our demo invites participants to create their own encrypted messages in the Android application and interact with the VLC prototype as it transmits encryption keys, thus illustrating our system's ability to bootstrap security among physically co-located devices.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343377"}, {"primary_key": "3092965", "vector": [], "sparse_vector": [], "title": "Poster: A Machine Learning based Hybrid Trust Management Heuristic for Vehicular Ad hoc Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over the past few decades, Vehicular Ad hoc Networks have attracted the attention of numerous researchers from both academia and industry. Today, this promising wireless communication technology plays an indispensable role as vehicles exchange low-latent safety critical messages with one another in a bid to make the road traffic more safer, efficient, and convenient. However, dissemination of malicious messages within the network not only significantly reduces the network performance but also becomes a source of threat for the passengers and vulnerable pedestrians. Accordingly, a number of trust models have been recently proposed in the literature to ensure the identification and elimination of malicious vehicles from the network. These trust models primarily rely on the aggregation of both direct and indirect observations and evict the malicious vehicles based on a particular threshold set on this composite trust value. Nevertheless, setting-up of this threshold poses a significant challenge especially owing to diverse influential factors in such a dynamic and distributed networking environment. To this end, in this manuscript, machine learning has been employed to compute the aggregate trust score for flagging and evicting of the malicious vehicles from a vehicular network. It is evident from the simulated results that the devised method is both accurate and scalable.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343404"}, {"primary_key": "3092966", "vector": [], "sparse_vector": [], "title": "mmNets&apos;19: The 3rd ACM Workshop on Millimeter-Wave Networks and Sensing Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The 3rd ACM Workshop on Millimeter-Wave Wireless Networks and Sensing Systems (mmNets'19) is focused on the design and implementation of new millimeter-wave (mmWave) protocols and systems that can enable multi-Gbps wireless connectivity for 5G-and-beyond cellular systems and wireless LANs, as well as new wireless sensing and imaging systems. The goal of the mmNets'19 workshop is to bring together researchers from mmWave hardware, communication and signal processing, wireless networking, and mobile applications to set the future research agenda of mmWave systems, and present innovative ideas that will help realize the vision of extremely high data rate wireless networks and novel advanced sensing applications. The workshop will serve as a platform for both academia and industry to identify key challenges, present solutions and advance the field of mmWave technology.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3355635"}, {"primary_key": "3092968", "vector": [], "sparse_vector": [], "title": "Poster: In-situ Water-Quality Monitoring System through Ultraviolet Sensing Using Off-the-Shelf Cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Contaminants can leach into drinking water in transport from water-treatment facilities or treated water storage to consumer taps. In this work, we design a low-cost mobile system that can use off-the-shelf web-cameras as an ultraviolet (UV) spectrometer. We posit to analyze and categorize impurities present in water by using fundamental image processing and computer vision techniques. In this poster paper, we qualitatively and quantitatively analyze the ultraviolet absorption and scattering through images captured of an UV light source (at 385nm wavelength) transmitted through de-ionized water containing varying contaminants and at different concentrations. We particularly explore the tests for lead, arsenic, table salt, charcoal, and coconut oil, and run a pilot-study on ground-truth analysis of tap water from five counties around Atlanta, Georgia, USA.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343397"}, {"primary_key": "3092971", "vector": [], "sparse_vector": [], "title": "Characterizing Uncertainties of Wireless Channels in Connected Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The performance of autonomous cars can be greatly enhanced through wireless coordination. However, mobility has traditionally been a challenge for wireless networks due to rapid fluctuation of the signal quality. Current control systems handle this challenge by slowing down the vehicle to preserve safety. However, in this research, we demonstrate that we can robustly characterize the channel quality by mapping the multipath signals to the dynamics of the physical environment, thus controlling the trajectory of the mobile agent to a safe efficient motion path. This allows mobile systems to realize the performance benefits of wireless coordination while providing safety.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343409"}, {"primary_key": "3092977", "vector": [], "sparse_vector": [], "title": "Demo: Tagging IoT Data in a Drone View.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Both cameras and IoT devices have their particular capabilities in tracking moving objects. Their correlations are, however, unclear. In this work, we consider using a drone to track ground objects. We demonstrate how to retrieve IoT data from devices, which are attached on human objects, and correctly tag them on the human objects captured by a drone view. This is the first work correlating IoT data and computer vision from a drone camera. Potential applications of this work include aerial surveillance, people tracking, and intelligent human-drone interaction.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343378"}, {"primary_key": "3092979", "vector": [], "sparse_vector": [], "title": "TunnelScatter: Low Power Communication for Sensor Tags using Tunnel Diodes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to extremely low power consumption, backscatter has become the transmission mechanism of choice for battery-free devices that operate on harvested energy. However, a limitation of recent backscatter systems is that the communication range scales with the strength of the ambient carrier signal(ACS). This means that to achieve a long range, a backscatter tag needs to reflect a strong ACS, which in practice means that it needs to be close to an ACS emitter. We present TunnelScatter, a mechanism that overcomes this limitation. TunnelScatter uses a tunnel diode-based radio frequency oscillator to enable transmissions when there is no ACS, and the same oscillator as a reflection amplifier to support backscatter transmissions when the ACS is weak. Our results show that even without an ACS, TunnelScatter is able to transmit through several walls covering a distance of 18 meter while consuming a peak biasing power of 57 microwatts. Based on TunnelScatter, we design battery-free sensor tags, called TunnelTags, that can sense physical phenomena and transmit them using the TunnelScatter mechanism.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345451"}, {"primary_key": "3092982", "vector": [], "sparse_vector": [], "title": "Poster: A Linear Programming Approach for SFC Placement in Mobile Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile Edge Computing (MEC) is a promising architecture where network services are deployed to the network edge. Recent studies tend to deploy Network Function Virtualization (NFV) services to MEC. Network services in NFV are deployed as Service Function Chains (SFCs). In this paper, we mainly focus on the SFC placement problem in a MEC-NFV environment, which is different from the data center network. Firstly, we formulate this problem as a weighted graph matching problem consisting of graph matching and SFC mapping. Then, we propose a linear programming-based approach to match the edge network and SFC. Finally, we design a Hungarian-based placement algorithm to map SFC in the edge network. A heuristic-based greedy algorithm is also designed to compare the performance. Evaluation results show that our proposed solutions outperform the greedy algorithm in terms of execution time.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343394"}, {"primary_key": "3092983", "vector": [], "sparse_vector": [], "title": "Experience: Understanding Long-Term Evolving Patterns of Shared Electric Vehicle Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the ever-growing concerns on the air pollution and energy security, many cities have started to update their taxi fleets with electric ones. Although environmentally friendly, the rapid promotion of electric taxis raises problems to both taxi drivers and governments, e.g., prolonged waiting/charging time, unbalanced utilization of charging infrastructures and reduced taxi supply due to the long charging time. In this paper, we make the first effort to understand the long-term evolving patterns through a five-year study on one of the largest electric taxi networks in the world, i.e., the Shenzhen electric taxi network in China. In particular, we perform a comprehensive measurement investigation called ePat to explore the evolving mobility and charging patterns of electric vehicles. Our ePat is based on 4.8 TB taxi GPS data, 240 GB taxi transaction data, and metadata from 117 charging stations, during an evolving process from 427 electric taxis in 2013 to 13,178 in 2018. Moreover, ePat also explores the impacts of various contexts and benefits during the evolving process. Our ePat as a comprehensive investigation of the electric taxi network mobility and charging evolving has the potential to advance the understanding of the evolving patterns of electric taxi networks and pave the way for analyzing future shared autonomous vehicles.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300132"}, {"primary_key": "3092984", "vector": [], "sparse_vector": [], "title": "Contactless Infant Monitoring using White Noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "White noise machines are among the most popular devices to facilitate infant sleep. We introduce the first contactless system that uses white noise to achieve motion and respiratory monitoring in infants. Our system is designed for smart speakers that can monitor an infant's sleep using white noise. The key enabler underlying our system is a set of novel algorithms that can extract the minute infant breathing motion as well as position information from white noise which is random in both the time and frequency domain. We describe the design and implementation of our system, and present experiments with a life-like infant simulator as well as a clinical study at the neonatal intensive care unit with five new-born infants. Our study demonstrates that the respiratory rate computed by our system is highly correlated with the ground truth with a correlation coefficient of 0.938.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345453"}, {"primary_key": "3092985", "vector": [], "sparse_vector": [], "title": "Poster: Contactless Infant Monitoring using White Noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this poster accompanying the MobiCom 2019 paper, we describes how to enable infant monitoring capability using white noise. Specifically, we design a set of novel algorithms that can extract the minute infant breathing motion as well as position information from white noise which is random in both the time and frequency domain. Our study demonstrates that the system achieves high correlation between the measured respiratory rate and the ground truth.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343401"}, {"primary_key": "3092986", "vector": [], "sparse_vector": [], "title": "Poster: Polarization-based QAM for Visible Light Backscatter Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Visible Light Backscatter Communication presents an emerging IoT connectivity technology that offers low power and flexible angular alignment benefits. State-of-art approaches with unmodified LCD(s) as optical modulator cause flickering with a sub-kbps low data rate, which would be overcome by modulating polarization, but naive implementation requires careful Tx-Rx placement to avoid signal cancellation. To overcome such challenges, we propose a new modulation scheme called polarization-based quadrature amplitude modulation (PQAM). PQAM constructs the orthogonal basis in the polarization domain and provides 2x throughput gain with arbitrary relative orientation.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343385"}, {"primary_key": "3092987", "vector": [], "sparse_vector": [], "title": "Poster: Understanding Long-Term Mobility and Charging Evolving of Shared EV Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the ever-growing concerns over air pollution and energy security, many cities have started to update their taxi fleets with electric ones. In this paper, we perform the first comprehensive measurement investigation called ePat to explore the evolving mobility and charging patterns of electric vehicles. Our ePat is based on 5-year 4.8 TB taxi GPS data, 240 GB taxi transaction data, and metadata from 117 charging stations, during an evolving process from 427 electric taxis in 2013 to 13,178 in 2018. Moreover, ePat also explores the impacts of various contexts and benefits during the evolving process. Our ePat as a comprehensive investigation of the electric taxi network mobility and charging evolving has the potential to advance the understanding of the evolving patterns of electric taxi networks and pave the way for analyzing future shared autonomous vehicles.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343402"}, {"primary_key": "3092988", "vector": [], "sparse_vector": [], "title": "An Active-Passive Measurement Study of TCP Performance over LTE on High-speed Rails.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Wangyang Li", "<PERSON><PERSON>", "<PERSON><PERSON> Cheng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yi Sun", "<PERSON><PERSON><PERSON>"], "summary": "High-speed rail (HSR) systems potentially provide a more efficient way of door-to-door transportation than airplane. However, they also pose unprecedented challenges in delivering seamless Internet service for on-board passengers. In this paper, we conduct a large-scale active-passive measurement study of TCP performance over LTE on HSR. Our measurement targets the HSR routes in China operating at above 300 km/h. We performed extensive data collection through both controlled setting and passive monitoring, obtaining 1732.9 GB data collected over 135719 km of trips. Leveraging such a unique dataset, we measure important performance metrics such as TCP goodput, latency, loss rate, as well as key characteristics of TCP flows, application breakdown, and users' behaviors. We further quantitatively study the impact of frequent cellular handover on HSR networking performance, and conduct in-depth examination of the performance of two widely deployed transport-layer protocols: TCP CUBIC and TCP BBR. Our findings reveal the performance of today's commercial HSR networks \"in the wild'', as well as identify several performance inefficiencies, which motivate us to design a simple yet effective congestion control algorithm based on BBR to further boost the throughput by up to 36.5%. They together highlight the need to develop dedicated protocol mechanisms that are friendly to extreme mobility.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300123"}, {"primary_key": "3092992", "vector": [], "sparse_vector": [], "title": "Demo: Improving Visible Light Backscatter Communication with Delayed Superimposition Modulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Visible light backscatter communication (VLBC) has shown great potential in IoT field. LCD, often used as optical modulator in a VLBC system, mainly limits the data rate to sub-Kbps due to its slow state transition. There is a great demand for speed improvement, to support both low latency and high throughput communication in IoT deployment. With the observation of nonlinear and time-varying characteristics of LCD, we present sys, a novel modulation scheme named Delayed Superimposition Modulation (DSM) to achieve 4kbps, which is 4x rate over the status-quo. This design is robust in the presence of LCD heterogeneity in practical setting.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343364"}, {"primary_key": "3092993", "vector": [], "sparse_vector": [], "title": "Poster: Causal Inference of Smartphone App Choice.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In nowaday, there are more and more popular smartphone apps. It is important for us to know why a certain app is chosen and whether an alternative app will be selected if the reasons using this app are changing. Once we know the reasons of current app choice, it will be helpful for us to design better app or provide better services so that more users are attracted to use this app. In this paper, we propose a causal inference model to analyze the reasons why a certain app is used. In this model, we define a regret loss function to evaluate the causal influence of a cause on its effect after an intervention on this cause. And, we use maximum likelihood method to estimate the parameter of a tranfer distribution.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343386"}, {"primary_key": "3092994", "vector": [], "sparse_vector": [], "title": "DF-Mose: Device-Free Motion Sensing with Wireless Backscattering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yubo Yan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel motion sensing/recognition system, called DF-Mose, which marries low-power wireless backscattering and device-free sensing in one clean sheet. DF-Mose is an accurate, interference tolerable motion-recognition system that counts repetitive motions without using scenario-dependent templates or profiles within 5% count error and enables multiuser to perform certain motions simultaneously based on the nature of backscattered signals and dedicated signal separation method. With little efforts in learning the patterns, our method could achieve 95.2% motion-recognition accuracy for a variety of 7 typical motions.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343374"}, {"primary_key": "3092995", "vector": [], "sparse_vector": [], "title": "mD-Track: Leveraging Multi-Dimensionality for Passive Indoor Wi-Fi Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wi-Fi localization and tracking face accuracy limitations dictated by antenna count (for angle-of-arrival methods) and frequency bandwidth (for time-of-arrival methods). This paper presents mD-Track, a device-free Wi-Fi tracking system capable of jointly fusing information from as many dimensions as possible to overcome the resolution limit of each individual dimension. Through a novel path separation algorithm, mD-Track can resolve multipath at a much finer-grained resolution, isolating signals reflected off targets of interest. mD-Track can localize human passively at a high accuracy with just a single Wi-Fi transceiver pair. mD-Track also introduces novel methods to greatly streamline its estimation algorithms, achieving real-time operation. We implement mD-Track on both WARP and cheap off-the-shelf commodity Wi-Fi hardware, and evaluate its performance in different indoor environments.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300133"}, {"primary_key": "3092996", "vector": [], "sparse_vector": [], "title": "Source Compression with Bounded DNN Perception Loss for IoT Edge Computer Vision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "IoT and deep learning based computer vision together create an immense market opportunity, but running deep neural networks (DNNs) on resource-constrained IoT devices remains challenging. Offloading DNN inference to an edge server is a promising solution, but limited wireless bandwidth bottlenecks its end-to-end performance and scalability. While IoT devices can adopt source compression to cope with the limited bandwidth, existing compression algorithms (or codecs) are not designed for DNN (but for human eyes), and thus, suffer from either low compression rates or high DNN inference errors. This paper presents GRACE, a DNN-aware compression algorithm that facilitates the edge inference by significantly saving the network bandwidth consumption without disturbing the inference performance. Given a target DNN, GRACE (i) analyzes DNN's perception model w.r.t both spatial frequencies and colors and (ii) generates an optimized compression strategy for the model -- one-time offline process. Next, GRACE deploys thus-generated compression strategy at IoT devices (or source) to perform online source compression within the existing codec framework, adding no extra overhead. We prototype GRACE on JPEG (the most popular image codec framework), and our evaluation results show that GRACE indeed achieves the superior compression performance over existing strategies for key DNN applications. For semantic segmentation tasks, GRACE reduces a source size by 23% compared to JPEG with similar interference accuracy (0.38% lower than GRACE). Further, GRACE even achieves 7.5% higher inference accuracy than JPEG with a commonly used quality level of 75 does. For classification tasks, GRACE reduces the bandwidth consumption by 90% over JPEG with the same inference accuracy.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345448"}, {"primary_key": "3092998", "vector": [], "sparse_vector": [], "title": "Towards Touch-to-Access Device Authentication Using Induced Body Electric Potentials.", "authors": ["Zhenyu Yan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents TouchAuth, a new touch-to-access device authentication approach using induced body electric potentials (iBEPs) caused by the indoor ambient electric field that is mainly emitted from the building's electrical cabling. The design of TouchAuth is based on the electrostatics of iBEP generation and a resulting property, i.e., the iBEPs at two close locations on the same human body are similar, whereas those from different human bodies are distinct. Extensive experiments verify the above property and show that TouchAuth achieves high-profile receiver operating characteristics in implementing the touch-to-access policy. Our experiments also show that a range of possible interfering sources including appliances' electromagnetic emanations and noise injections into the power network do not affect the performance of TouchAuth. A key advantage of TouchAuth is that the iBEP sensing requires a simple analog-to-digital converter only, which is widely available on microcontrollers. Compared with existing approaches including intra-body communication and physiological sensing, TouchAuth is a low-cost, lightweight, and convenient approach for authorized users to access the smart objects found in indoor environments.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300118"}, {"primary_key": "3092999", "vector": [], "sparse_vector": [], "title": "Mobile Gaming on Personal Computers with Direct Android Emulation.", "authors": ["<PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Playing Android games on Windows x86 PCs has gained enormous popularity in recent years, and the de facto solution is to use mobile emulators built with the AOVB (Android-x86 On VirtualBox) architecture. When playing heavy 3D Android games with AOVB, however, users often suffer unsatisfactory smoothness due to the considerable overhead of full virtualization. This paper presents DAOW, a game-oriented Android emulator implementing the idea of direct Android emulation, which eliminates the overhead of full virtualization by directly executing Android app binaries on top of x86-based Windows. Based on pragmatic, efficient instruction rewriting and syscall emulation, DAOW offers foreign Android binaries direct access to the domestic PC hardware through Windows kernel interfaces, achieving nearly native hardware performance. Moreover, it leverages graphics and security techniques to enhance user experiences and prevent cheating in gaming. As of late 2018, DAOW has been adopted by over 50 million PC users to run thousands of heavy 3D Android games. Compared with AOVB, DAOW improves the smoothness by 21% on average, decreases the game startup time by 48%, and reduces the memory usage by 22%.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300122"}, {"primary_key": "3093000", "vector": [], "sparse_vector": [], "title": "VeMo: Enabling Transparent Vehicular Mobility Modeling at Individual Levels with Full Penetration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding and predicting real-time vehicle mobility patterns on highways are essential to address traffic congestion and respond to the emergency. However, almost all existing works (e.g., based on cellphones, onboard devices, or traffic cameras) suffer from high costs, low penetration rates, or only aggregate results. To address these drawbacks, we utilize Electric Toll Collection systems (ETC) as a large-scale sensor network and design a system called VeMo to transparently model and predict vehicle mobility at the individual level with a full penetration rate. Our novelty is how we address uncertainty issues (i.e., unknown routes and speeds) due to sparse implicit ETC data based on a key data-driven insight, i.e., individual driving behaviors are strongly correlated with crowds of drivers under certain spatiotemporal contexts and can be predicted by combining both personal habits and context information. More importantly, we evaluate VeMo with (i) a large-scale ETC system with tracking devices at 773 highway entrances and exits capturing more than 2 million vehicles every day; (ii) a fleet consisting of 114 thousand vehicles with GPS data as ground truth. We compared VeMo with state-of-the-art benchmark mobility models, and the experimental results show that VeMo outperforms them by average 10% in terms of accuracy.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300130"}, {"primary_key": "3093001", "vector": [], "sparse_vector": [], "title": "Demo: Mobile Gaming on Personal Computers with Direct Android Emulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "Guoyang Du", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Playing Android games with Windows x86 PCs is now popular, and the common solution is to use mobile emulators built with the AOVB (Android-x86 On VirtualBox) architecture. Nevertheless, running heavy 3D Android games on AOVB incurs considerable overhead of full virtualization, thus often leading to unsatisfactory smoothness. To tackle this issue, we present DAOW, a commercial game-oriented Android emulator implementing the idea of direct Android emulation, which eliminates the overhead of full virtualization by providing foreign Android binaries with direct access to the domestic PC hardware through Windows kernel interfaces. In this demo, we will demonstrate that DAOW essentially outperforms traditional AOVB-based emulators in terms of running smoothness, game startup time, and memory usage.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343372"}, {"primary_key": "3093003", "vector": [], "sparse_vector": [], "title": "A Framework for Analyzing Spectrum Characteristics in Large Spatio-temporal Scales.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Understanding spectrum characteristics with little prior knowledge requires fine-grained spectrum data in the frequency, spatial, and temporal domains; gathering such a diverse set of measurements results in a large data volume. Analysis of the resulting dataset poses unique challenges; methods in the status quo are tailored for specific spectrum-related applications (apps), and are ill equipped to process data of this magnitude. In this paper, we design BigSpec, a general-purpose framework that allows for fast processing of apps. The key idea is to reduce computation costs by performing computation extensively on compressed data that preserves signal features. Adhering to this guideline, we build solutions for three apps, i.e., energy detection, spatio-temporal spectrum estimation, and anomaly detection. These apps were chosen to highlight BigSpec's efficiency, scalability, and extensibility. To evaluate BigSpec's performance, we collect more than 1 terabyte of spectrum data spanning a year, across 300MHz-4GHz, covering 400 km2. Compared with baselines and prior works, we achieve 17× run time efficiency, sublinear rather than linear run time scalability, and extend the definition of anomaly to different domains (frequency & spatio-temporal). We also obtain high-level insights from the data to provide valuable advice on future spectrum measurement and data analysis.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345450"}, {"primary_key": "3093004", "vector": [], "sparse_vector": [], "title": "PDVocal: Towards Privacy-preserving Parkinson&apos;s Disease Detection using Non-speech Body Sounds.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Parkinson's disease (PD) is a chronic neurodegenerative disorder resulting from the progressive loss of dopaminergic nerve cells. People with PD usually demonstrate deficits in performing basic daily activities, and the relevant annual social cost can reach about $25 billion in the United States. Early detection of PD plays an important role in symptom relief and improvement in the performance of activities in daily life (ADL), which eventually reduces societal and economic burden. However, conventional PD detection methods are inconvenient in daily life (e.g., requiring users to wear sensors). To overcome this challenge, we propose and identify the non-speech body sounds as the new PD biomarker, and utilize the data in smartphone usage to realize the passive PD detection in daily life without interrupting the user. Specifically, we present PDVocal, an end-to-end smartphone-based privacy-preserving system towards early PD detection. PDVocal can passively recognize the PD digital biomarkers in the voice data during daily phone conversation. At the user end, PDVocal filters the audio stream and only extracts the non-speech body sounds (e.g., breathing, clearing throat and swallowing) which contain no privacy-sensitive content. At the cloud end, PDVocal analyzes the body sounds of interest and assesses the health condition using a customized residual network. For the sake of reliability in real-world PD detection, we investigate the method of the performance optimizer including an opportunistic learning knob and a long-term tracking protocol. We evaluate our proposed PDVocal on a collected dataset from 890 participants and real-life conversations from publicly available data sources. Results indicate that non-speech body sounds are a promising digital biomarker for privacy-preserving PD detection in daily life.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300125"}, {"primary_key": "3093005", "vector": [], "sparse_vector": [], "title": "Demo: The Design and Implementation of Intelligent Software Defined Security Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>gpeng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software-defined security (SDS) overcomes the limitations of traditional security mechanisms, which brings significant merits for design, deployment and management. However, existing researches are usually limited to some independent algorithms, while not able to apply multiple algorithms to accommodate various types of attack in actual deployment. In this paper, we propose and implement a novel SDS framework, which aims to flexibly deploy a variety of security functions and artificial intelligence (AI) algorithms to automatically learn ongoing threats and proactively protect the network from attacks.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343365"}, {"primary_key": "3093006", "vector": [], "sparse_vector": [], "title": "On the Feasibility of Wi-Fi Based Material Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless sensing has demonstrated the potential of using Wi-Fi signals to track people and objects, even behind walls.Yet, prior work in this space aims to merely detect the presence of objects around corners, rather than their type. In this paper, we explore the feasibility of the following re-search question: ?Can commodity Wi-Fi radios detect both the location and type of moving objects around them?\". We present IntuWition, a complementary sensing system that can sense the location and type of material of objects in the environment, including those out of line-of-sight. It achieves this by sensing wireless signals reflected off surrounding objects using commodity Wi-Fi radios, whose signals penetrate walls and occlusions. At the core of IntuWition is the idea that different materials reflect and scatter polarized waves in different ways. We build upon ideas from RADAR Polarimetry to detect the material of objects across spatial locations, despite mobility of the sensing device and the hardware non-idealities of commodity Wi-Fi radios. A detailed feasibility study reveals an average accuracy of 95% in line-of-sight and 92% in non-line-of-sight in classifying five types of materials:copper, aluminum, plywood, birch, and human. Finally, we present a proof-of-concept application of our system on an autonomous UAV that uses its onboard Wi-Fi radios to sense whether an occlusion is a person versus another UAV.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345442"}, {"primary_key": "3093007", "vector": [], "sparse_vector": [], "title": "Demo: A ROS-based Robot with Distributed Sensors for Seamless People Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ting-<PERSON> Ke", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents a robot for people identification and tracking developed on robot operating system (ROS). It achieves modulized, light-weight, low-cost, and high-performance design goals even with the existence of distributed sensors. The key idea is to utilize wearable devices to enhance the people tracking capability of a robot through instant wireless communications and multi-sensory data fusion. Experimental results in a realistic environment demonstrate that our robot can keep tracking a specific person at a safe distance even without seeing the biological features of the person, who walks in a crowd with complex trajectories.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343369"}, {"primary_key": "3093009", "vector": [], "sparse_vector": [], "title": "Poster: Cross Labelling and Learning Unknown Activities Among Multimodal Sensing Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the major challenges for fully enjoying the power of machine learning is the need for the high-quality labelled data. To tap-in the gold-mine of data generated by IoT devices with unprecedented volume and value, we discover and leverage the hidden connections among the multimodal data collected by various sensing devices. Different modal data can complete and learn from each other, but it is challenging to fuse multimodal data without knowing their perception (and thus the correct labels). In this work, we propose MultiSense, a paradigm for automatically mining potential perception, cross-labelling each modal data, and then improving the learning models over the set of multimodal data. We design innovative solutions for segmenting, aligning, and fusing multimodal data from different sensors. We implement our framework and conduct comprehensive evaluations on a rich set of data. Our results demonstrate that MultiSense significantly improves the data usability and the power of the learning models.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343407"}, {"primary_key": "3093010", "vector": [], "sparse_vector": [], "title": "Demo: Toward Continuous User Authentication Using PPG in Commodity Wrist-worn Wearables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a photoplethysmography (PPG)-based continuous user authentication (CA) system leveraging the pervasively equipped PPG sensor in commodity wrist-worn wearables such as the smartwatch. Compared to existing approaches, our system does not require any users' interactions (e.g., performing specific gestures) and is applicable to practical scenarios where the user's daily activities cause motion artifacts (MA). Notably, we design a robust MA removal method to mitigate the impact of MA. Furthermore, we explore the uniqueness of the human cardiac system and extract the fiducial features in the PPG measurements to train the gradient boosting tree (GBT) classifier, which can effectively differentiate users continuously using low training effort. In particular, we build the prototype of our system using a commodity smartwatch and a WebSocket server running on a laptop for CA. In order to demonstrate the practical use of our system, we will demo our prototype under different scenarios (i.e., static and moving) to show it can effectively detect MA caused by daily activities and achieve a high authentication success rate.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343375"}, {"primary_key": "3093011", "vector": [], "sparse_vector": [], "title": "OFDMA-Enabled Wi-Fi Backscatter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we for the first time demonstrate how to enable OFDMA in Wi-Fi backscatter for capacity and concurrency enhancement. With our approach, the excitation signal is reflected, modulated and shifted to lie in the frequency band of the OFDM subcarrier by the tag; OFDMA is realized by coordinating tags to convey information to the receiver with orthogonal subcarriers concurrently through backscatter. The crux of the design is to achieve strict synchronization among communication components, which is more challenging than in regular OFDMA systems due to the more prominent hardware diversity and uncertainty for backscattering. We reveal how the subtle asychnronization scenarios particularly for backscattering can incur system offsets, and present a series of novel designs for the excitation signal transmitter, tag, and receiver to address the issue. We build a prototype in 802.11g OFDM framework to validate our design. Experimental results show that our system can achieve 5.2-16Mbps aggregate throughput by allowing 48 tags to transmit concurrently, which is 1.45-5x capacity and 48x concurrency compared with the existing design respectively. We also design an OFDMA tag IC, and the simulation and numerical analysis results show that the tag's power consumption is in tens of μW.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3300121"}, {"primary_key": "3093013", "vector": [], "sparse_vector": [], "title": "Learning to Coordinate Video Codec with Transport Protocol for Mobile Video Telephony.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Guangyuan Su", "<PERSON><PERSON><PERSON>", "<PERSON>uo<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma", "Xiao<PERSON> Chen"], "summary": "Despite the pervasive use of real-time video telephony services, the users' quality of experience (QoE) remains unsatisfactory, especially over the mobile Internet. Previous work studied the problem via controlled experiments, while a systematic and in-depth investigation in the wild is still missing. To bridge the gap, we conduct a large-scale measurement campaign on \\appname, an operational mobile video telephony service. Our measurement logs fine-grained performance metrics over 1 million video call sessions. Our analysis shows that the application-layer video codec and transport-layer protocols remain highly uncoordinated, which represents one major reason for the low QoE. We thus propose \\name, a machine learning based framework to resolve the issue. Instead of blindly following the transport layer's estimation of network capacity, \\name reviews historical logs of both layers, and extracts high-level features of codec/network dynamics, based on which it determines the highest bitrates for forthcoming video frames without incurring congestion. To attain the ability, we train \\name with the aforementioned massive data traces using a custom-designed imitation learning algorithm, which enables \\name to learn from past experience. We have implemented and incorporated \\name into \\appname. Our experiments show that \\name outperforms state-of-the-art solutions, improving video quality while reducing stalling time by multi-folds under various practical scenarios.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3345430"}, {"primary_key": "3093014", "vector": [], "sparse_vector": [], "title": "Poster: Optimizing Mobile Video Telephony Using Deep Imitation Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Guangyuan Su", "<PERSON><PERSON><PERSON>", "<PERSON>uo<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma", "Xiao<PERSON> Chen"], "summary": "Despite the pervasive use of real-time video telephony services, their quality of experience (QoE) remains unsatisfactory, especially over the mobile Internet. We conduct a large-scale measurement campaign on \\appname, an operational mobile video telephony service. Our analysis shows that the application-layer video codec and transport-layer protocols remain highly uncoordinated, which represents one major reason for the low QoE. We thus propose \\name, a machine learning based framework to resolve the issue. We train \\name with the massive data traces from the measurement campaign using a custom-designed imitation learning algorithm, which enables \\name to learn from past experience following an expert's iterative demonstration/supervision. We have implemented and incorporated \\name into the \\appname. Our experiments show that \\name outperforms state-of-the-art solutions, improving video quality while reducing stalling time by multi-folds under various practical scenarios.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061.3343408"}, {"primary_key": "3140125", "vector": [], "sparse_vector": [], "title": "The 25th Annual International Conference on Mobile Computing and Networking, MobiCom 2019, Los Cabos, Mexico, October 21-25, 2019.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is our great pleasure to welcome you to the proceedings of ACM MobiCom 2019, the International Conference on Mobile Computing and Networking, the twenty-fifth edition in a series of annual conferences sponsored by ACM SIGMOBILE. The conference is being held in Los Cabos, Mexico, from October 21 to October 25, 2019. This is the first time that this highly selective and premier conference, or any ACM SIGMOBILE conference or workshop, has been held in Mexico. This is also the first time that it has been held in an all-inclusive resort. Being the silver jubilee anniversary of the conference, we wanted to hold it in a special venue where attendees could celebrate and form deeper connections with each other and the work that they do. ACM MobiCom 2019 includes two keynote talks, a Test of Time Award talk, regular paper sessions with 55 paper talks, which is more than ever before, seven workshops, four tutorials on current and exciting topics by field experts, and a poster and demo session that includes the ACM Student Research Competition. The ACM MobiCom 2019 program keynotes are given by world experts working in very impactful fields that have not been core to MobiCom, but we predict will have great influence on our work in the future. The ACM MobiCom 2019 organizing committee thanks the Keynote speakers - Professors <PERSON><PERSON> (University of Southern California) and <PERSON> (University of Washington and Apple). The ACM MobiCom 2019 organizing committee is composed of a strong mix of 28 industry and academic researchers with diverse backgrounds. Through their efforts, a stellar technical program has been put together for this year's conference. We are thankful to the TPC co-chairs, Professors <PERSON><PERSON> (University of Washington) and <PERSON><PERSON><PERSON> (University of California, San Diego). Thanks to their efforts, the ACM MobiCom 2019 technical program includes 55 regular papers - the most ever - on diverse and exciting topics in mobile communications.", "published": "2019-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3300061"}]