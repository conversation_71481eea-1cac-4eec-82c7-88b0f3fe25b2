[{"primary_key": "1475986", "vector": [], "sparse_vector": [], "title": "MobilePhys: Personalized Mobile Camera-Based Contactless Physiological Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Camera-based contactless photoplethysmography refers to a set of popular techniques for contactless physiological measurement. The current state-of-the-art neural models are typically trained in a supervised manner using videos accompanied by gold standard physiological measurements. However, they often generalize poorly out-of-domain examples (i.e., videos that are unlike those in the training set). Personalizing models can help improve model generalizability, but many personalization techniques still require some gold standard data. To help alleviate this dependency, in this paper, we present a novel mobile sensing system called MobilePhys, the first mobile personalized remote physiological sensing system, that leverages both front and rear cameras on a smartphone to generate high-quality self-supervised labels for training personalized contactless camera-based PPG models. To evaluate the robustness of MobilePhys, we conducted a user study with 39 participants who completed a set of tasks under different mobile devices, lighting conditions/intensities, motion tasks, and skin types. Our results show that MobilePhys significantly outperforms the state-of-the-art on-device supervised training and few-shot adaptation methods. Through extensive user studies, we further examine how does MobilePhys perform in complex real-world settings. We envision that calibrated or personalized camera-based contactless PPG models generated from our proposed dual-camera mobile sensing system will open the door for numerous future applications such as smart mirrors, fitness and mobile health applications.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517225"}, {"primary_key": "1475998", "vector": [], "sparse_vector": [], "title": "StretchAR: Exploiting Touch and Stretch as a Method of Interaction for Smart Glasses Using Wearable Straps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Juan C. Mesa", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past decade, augmented reality (AR) developers have explored a variety of approaches to allow users to interact with the information displayed on smart glasses and head-mounted displays (HMDs). Current interaction modalities such as mid-air gestures, voice commands, or hand-held controllers provide a limited range of interactions with the virtual content. Additionally, these modalities can also be exhausting, uncomfortable, obtrusive, and socially awkward. There is a need to introduce comfortable interaction techniques for smart glasses and HMDS without the need for visual attention. This paper presents StretchAR, wearable straps that exploit touch and stretch as input modalities to interact with the virtual content displayed on smart glasses. StretchAR straps are thin, lightweight, and can be attached to existing garments to enhance users' interactions in AR. StretchAR straps can withstand strains up to 190% while remaining sensitive to touch inputs. The strap allows the effective combination of these inputs as a mode of interaction with the content displayed through AR widgets, maps, menus, social media, and Internet of Things (IoT) devices. Furthermore, we conducted a user study with 15 participants to determine the potential implications of the use of StretchAR as input modalities when placed on four different body locations (head, chest, forearm, and wrist). This study reveals that StretchAR can be used as an efficient and convenient input modality for smart glasses with a 96% accuracy. Additionally, we provide a collection of 28 interactions enabled by the simultaneous touch-stretch capabilities of StretchAR. Finally, we facilitate recommendation guidelines for the design, fabrication, placement, and possible applications of StretchAR as an interaction modality for AR content displayed on smart glasses.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550305"}, {"primary_key": "1475882", "vector": [], "sparse_vector": [], "title": "SmokeMon: Unobtrusive Extraction of Smoking Topography Using Wearable Energy-Efficient Thermal.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lingfeng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Nabil <PERSON>"], "summary": "Smoking is the leading cause of preventable death worldwide. Cigarette smoke includes thousands of chemicals that are harmful and cause tobacco-related diseases. To date, the causality between human exposure to specific compounds and the harmful effects is unknown. A first step in closing the gap in knowledge has been measuring smoking topography, or how the smoker smokes the cigarette (puffs, puff volume, and duration). However, current gold-standard approaches to smoking topography involve expensive, bulky, and obtrusive sensor devices, creating unnatural smoking behavior and preventing their potential for real-time interventions in the wild. Although motion-based wearable sensors and their corresponding machine-learned models have shown promise in unobtrusively tracking smoking gestures, they are notorious for confounding smoking with other similar hand-to-mouth gestures such as eating and drinking. In this paper, we present SmokeMon, a chest-worn thermal-sensing wearable system that can capture spatial, temporal, and thermal information around the wearer and cigarette all day to unobtrusively and passively detect smoking events. We also developed a deep learning-based framework to extract puffs and smoking topography. We evaluate SmokeMon in both controlled and free-living experiments with a total of 19 participants, more than 110 hours of data, and 115 smoking sessions achieving an F1-score of 0.9 for puff detection in the laboratory and 0.8 in the wild. By providing SmokeMon as an open platform, we provide measurement of smoking topography in free-living settings to enable testing of smoking topography in the real world, with potential to facilitate timely smoking cessation interventions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569460"}, {"primary_key": "1475884", "vector": [], "sparse_vector": [], "title": "PrISM-Tracker: A Framework for Multimodal Procedure Tracking Using Wearable Sensors and State Transition Information with User-Driven Handling of Errors and Uncertainty.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vimal Mollyn", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A user often needs training and guidance while performing several daily life procedures, e.g., cooking, setting up a new appliance, or doing a COVID test. Watch-based human activity recognition (HAR) can track users' actions during these procedures. However, out of the box, state-of-the-art HAR struggles from noisy data and less-expressive actions that are often part of daily life tasks. This paper proposes PrISM-Tracker, a procedure-tracking framework that augments existing HAR models with (1) graph-based procedure representation and (2) a user-interaction module to handle model uncertainty. Specifically, PrISM-Tracker extends a Viterbi algorithm to update state probabilities based on time-series HAR outputs by leveraging the graph representation that embeds time information as prior. Moreover, the model identifies moments or classes of uncertainty and asks the user for guidance to improve tracking accuracy. We tested PrISM-Tracker in two procedures: latte-making in an engineering lab study and wound care for skin cancer patients at a clinic. The results showed the effectiveness of the proposed algorithm utilizing transition graphs in tracking steps and the efficacy of using simulated human input to enhance performance. This work is the first step toward human-in-the-loop intelligent systems for guiding users while performing new and complicated procedural tasks.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569504"}, {"primary_key": "1475892", "vector": [], "sparse_vector": [], "title": "Revisiting Reflection in HCI: Four Design Resources for Technologies that Support Reflection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "Pia S. F<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reflection is a commonly addressed design goal in commercial systems and in Human-Computer Interaction (HCI) research. Yet, it is still unclear what tools are at the disposal of designers who want to build systems that support reflection. Understanding the design space of reflection support systems and the interaction techniques that can foster reflection is necessary to enable building technologies that contribute to the users' well-being. In order to gain additional insight into how interactive artefacts foster reflection, we investigated past research prototypes and reflection-supporting smartphone applications (apps). Through a structured literature review and an analysis of app reviews, we constructed four design resources for reflection: temporal perspective, conversation, comparison and discovery. We also identified design patterns in past digital artefacts that implement the resources. Our work constitutes intermediate-level knowledge that is intended to inspire future technologies that better support reflection.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517233"}, {"primary_key": "1475897", "vector": [], "sparse_vector": [], "title": "MobiVQA: Efficient On-Device Visual Question Answering.", "authors": ["Qingqing Cao", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visual Question Answering (VQA) is a relatively new task where a user can ask a natural question about an image and obtain an answer. VQA is useful for many applications and is widely popular for users with visual impairments. Our goal is to design a VQA application that works efficiently on mobile devices without requiring cloud support. Such a system will allow users to ask visual questions privately, without having to send their questions to the cloud, while also reduce cloud communication costs. However, existing VQA applications use deep learning models that significantly improve accuracy, but is computationally heavy. Unfortunately, existing techniques that optimize deep learning for mobile devices cannot be applied for VQA because the VQA task is multi-modal---it requires both processing vision and text data. Existing mobile optimizations that work for vision-only or text-only neural networks cannot be applied here because of the dependencies between the two modes. Instead, we design MobiVQA, a set of optimizations that leverage the multi-modal nature of VQA. We show using extensive evaluation on two VQA testbeds and two mobile platforms, that MobiVQA significantly improves latency and energy with minimal accuracy loss compared to state-of-the-art VQA models. For instance, MobiVQA can answer a visual question in 163 milliseconds on the phone, compared to over 20-second latency incurred by the most accurate state-of-the-art model, while incurring less than 1 point reduction in accuracy.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534619"}, {"primary_key": "1475904", "vector": [], "sparse_vector": [], "title": "EFRing: Enabling Thumb-to-Index-Finger Microgesture Interaction through Electric Field Sensing Using Single Smart Ring.", "authors": ["Taizhou Chen", "Tianpei Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present EFRing, an index-finger-worn ring-form device for detecting thumb-to-index-finger (T2I) microgestures through the approach of electric-field (EF) sensing. Based on the signal change induced by the T2I motions, we proposed two machine-learning-based data-processing pipelines: one for recognizing/classifying discrete T2I microgestures, and the other for tracking continuous 1D T2I movements. Our experiments on the EFRing microgesture classification showed an average within-user accuracy of 89.5% and an average cross-user accuracy of 85.2%, for 9 discrete T2I microgestures. For the continuous tracking of 1D T2I movements, our method can achieve the mean-square error of 3.5% for the generic model and 2.3% for the personalized model. Our 1D-Fitts'-Law target-selection study shows that the proposed tracking method with EFRing is intuitive and accurate for real-time usage. Lastly, we proposed and discussed the potential applications for EFRing.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569478"}, {"primary_key": "1475905", "vector": [], "sparse_vector": [], "title": "RFCam: Uncertainty-aware Fusion of Camera and Wi-Fi for Real-time Human Identification with Mobile Devices.", "authors": ["Hongkai Chen", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As cameras and Wi-Fi access points are widely deployed in public places, new mobile applications and services can be developed by connecting live video analytics to the mobile Wi-Fi-enabled devices of the relevant users. To achieve this, a critical challenge is to identify the person who carries a device in the video with the mobile device's network ID, e.g., MAC address. To address this issue, we propose RFCam, a system for human identification with a fusion of Wi-Fi and camera data. RFCam uses a multi-antenna Wi-Fi radio to collect CSI of Wi-Fi packets sent by mobile devices, and a camera to monitor users in the area. With low sampling rate CSI data, RFCam derives heterogeneous embedding features on location, motion, and user activity for each device over time, and fuses them with visual user features generated from video analytics to find the best matches. To mitigate the impacts of multi-user environments on wireless sensing, we develop video-assisted learning models for different features and quantify their uncertainties, and incorporate them with video analytics to rank moments and features for robust and efficient fusion. RFCam is implemented and tested in indoor environments for over 800 minutes with 25 volunteers, and extensive evaluation results demonstrate that RFCam achieves real-time identification average accuracy of 97.01% in all experiments with up to ten users, significantly outperforming existing solutions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534588"}, {"primary_key": "1475914", "vector": [], "sparse_vector": [], "title": "ARticulate: One-Shot Interactions with Intelligent Assistants in Unfamiliar Smart Spaces Using Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart space technologies have entered the mainstream home market. Most users currently interact with smart homes that they (or an acquaintance) have set up and know well. However, as these technologies spread to commercial or public environments, users will need to frequently interact with unfamiliar smart spaces where they are unaware of the available capabilities and the system maintainer will not be present to help. Users will need to quickly and independently 1) discover what is and is not possible, and 2) make use of available functionality. Widespread adoption of smart space systems will not be possible until this discoverability issue is solved. We design and evaluate ARticulate, an interface that allows users to have successful smart space interactions with an intelligent assistant while learning transferable information about the overall set of devices in an unfamiliar space. Our method of using Snapchat-like contextual photo messages enhanced by two technologies---augmented reality and autocomplete---allows users to determine available functionality and achieve their goals in one attempt with a smart space they have never seen before, something no existing interface supports. The ability to easily operate unfamiliar smart spaces improves the usability of existing systems and removes a significant obstacle to the vision of ubiquitous computing.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517235"}, {"primary_key": "1475916", "vector": [], "sparse_vector": [], "title": "Multi-Task Learning for Randomized Controlled Trials: A Case Study on Predicting Depression with Wearable Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Nan Lv", "<PERSON>", "Chenyang Lu"], "summary": "A randomized controlled trial (RCT) is used to study the safety and efficacy of new treatments, by comparing patient outcomes of an intervention group with a control group. Traditionally, RCTs rely on statistical analyses to assess the differences between the treatment and control groups. However, such statistical analyses are generally not designed to assess the impact of the intervention at an individual level. In this paper, we explore machine learning models in conjunction with an RCT for personalized predictions of a depression treatment intervention, where patients were longitudinally monitored with wearable devices. We formulate individual-level predictions in the intervention and control groups from an RCT as a multi-task learning (MTL) problem, and propose a novel MTL model specifically designed for RCTs. Instead of training separate models for the intervention and control groups, the proposed MTL model is trained on both groups, effectively enlarging the training dataset. We develop a hierarchical model architecture to aggregate data from different sources and different longitudinal stages of the trial, which allows the MTL model to exploit the commonalities and capture the differences between the two groups. We evaluated the MTL approach in an RCT involving 106 patients with depression, who were randomized to receive an integrated intervention treatment. Our proposed MTL model outperforms both single-task models and the traditional multi-task model in predictive performance, representing a promising step in utilizing data collected in RCTs to develop predictive models for precision medicine.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534591"}, {"primary_key": "1475923", "vector": [], "sparse_vector": [], "title": "UltraSpeech: Speech Enhancement by Interaction between Ultrasound and Speech.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Speech enhancement can benefit lots of practical voice-based interaction applications, where the goal is to generate clean speech from noisy ambient conditions. This paper presents a practical design, namely UltraSpeech, to enhance speech by exploring the correlation between the ultrasound (profiled articulatory gestures) and speech. UltraSpeech uses a commodity smartphone to emit the ultrasound and collect the composed acoustic signal for analysis. We design a complex masking framework to deal with complex-valued spectrograms, incorporating the magnitude and phase rectification of speech simultaneously. We further introduce an interaction module to share information between ultrasound and speech two branches and thus enhance their discrimination capabilities. Extensive experiments demonstrate that UltraSpeech increases the Scale Invariant SDR by 12dB, improves the speech intelligibility and quality effectively, and is capable to generalize to unknown speakers.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550303"}, {"primary_key": "1475926", "vector": [], "sparse_vector": [], "title": "Robust Federated Learning for Ubiquitous Computing through Mitigation of Edge-Case Backdoor Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Federated Learning (FL) allows several data owners to train a joint model without sharing their training data. Such a paradigm is useful for better privacy in many ubiquitous computing systems. However, FL is vulnerable to poisoning attacks, where malicious participants attempt to inject a backdoor task in the model at training time, along with the main task that the model was initially trained for. Recent works show that FL is particularly vulnerable to edge-case backdoors introduced by data points with unusual out-of-distribution features. Such attacks are among the most difficult to counter, and today's FL defense mechanisms usually fail to tackle them. In this paper, we present ARMOR, a defense mechanism that leverages adversarial learning to uncover edge-case backdoors. In contrast to most of existing FL defenses, ARMOR does not require real data samples and is compatible with secure aggregation, thus, providing better FL privacy protection. ARMOR relies on GANs (Generative Adversarial Networks) to extract data features from model updates, and uses the generated samples to test the activation of potential edge-case backdoors in the model. Our experimental evaluations with three widely used datasets and neural networks show that ARMOR can tackle edge-case backdoors with 95% resilience against attacks, and without hurting model quality.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569492"}, {"primary_key": "1475927", "vector": [], "sparse_vector": [], "title": "One-handed Input for Mobile Devices via Motion Matching and Orbits Controls.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a novel one-handed input technique for mobile devices that is not based on pointing, but on motion matching-where users select a target by mimicking its unique animation. Our work is motivated by the findings of a survey (N=201) on current mobile use, from which we identify lingering opportunities for one-handed input techniques. We follow by expanding on current motion matching implementations-previously developed in the context of gaze or mid-Air input-so these take advantage of the affordances of touch-input devices. We validate the technique by characterizing user performance via a standard selection task (N=24) where we report success rates (>95%), selection times (∼1.6 s), input footprint, grip stability, usability, and subjective workload-in both phone and tablet conditions. Finally, we present a design space that illustrates six ways in which motion matching can be embedded into mobile interfaces via a camera prototype application.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534624"}, {"primary_key": "1475940", "vector": [], "sparse_vector": [], "title": "Assessing the State of Self-Supervised Human Activity Recognition Using Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The emergence of self-supervised learning in the field of wearables-based human activity recognition (HAR) has opened up opportunities to tackle the most pressing challenges in the field, namely to exploit unlabeled data to derive reliable recognition systems for scenarios where only small amounts of labeled training samples can be collected. As such, self-supervision, i.e., the paradigm of 'pretrain-then-finetune' has the potential to become a strong alternative to the predominant end-to-end training approaches, let alone hand-crafted features for the classic activity recognition chain. Recently a number of contributions have been made that introduced self-supervised learning into the field of HAR, including, Multi-task self-supervision, Masked Reconstruction, CPC, and SimCLR, to name but a few. With the initial success of these methods, the time has come for a systematic inventory and analysis of the potential self-supervised learning has for the field. This paper provides exactly that. We assess the progress of self-supervised HAR research by introducing a framework that performs a multi-faceted exploration of model performance. We organize the framework into three dimensions, each containing three constituent criteria, such that each dimension captures specific aspects of performance, including the robustness to differing source and target conditions, the influence of dataset characteristics, and the feature space characteristics. We utilize this framework to assess seven state-of-the-art self-supervised methods for HAR, leading to the formulation of insights into the properties of these techniques and to establish their value towards learning representations for diverse scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550299"}, {"primary_key": "1475953", "vector": [], "sparse_vector": [], "title": "A Design Space for Human Sensor and Actuator Focused In-Vehicle Interaction Based on a Systematic Literature Review.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automotive user interfaces constantly change due to increasing automation, novel features, additional applications, and user demands. While in-vehicle interaction can utilize numerous promising modalities, no existing overview includes an extensive set of human sensors and actuators and interaction locations throughout the vehicle interior. We conducted a systematic literature review of 327 publications leading to a design space for in-vehicle interaction that outlines existing and lack of work regarding input and output modalities, locations, and multimodal interaction. To investigate user acceptance of possible modalities and locations inferred from existing work and gaps unveiled in our design space, we conducted an online study (N=48). The study revealed users' general acceptance of novel modalities (e.g., brain or thermal activity) and interaction with locations other than the front (e.g., seat or table). Our work helps practitioners evaluate key design decisions, exploit trends, and explore new areas in the domain of in-vehicle interaction.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534617"}, {"primary_key": "1475955", "vector": [], "sparse_vector": [], "title": "Synapse: Interactive Guidance by Demonstration with Trial-and-Error Support for Older Adults to Use Smartphone Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fan"], "summary": "As smartphones are widely adopted, mobile applications (apps) are emerging to provide critical services such as food delivery and telemedicine. While bring convenience to everyday life, this trend may create barriers for older adults who tend to be less tech-savvy than young people. In-person or screen sharing support is helpful but limited by the help-givers' availability. Video tutorials can be useful but require users to switch contexts between watching the tutorial and performing the corresponding actions in the app, which is cumbersome to do on a mobile phone. Although interactive tutorials have been shown to be promising, none was designed for older adults. Furthermore, the trial-and-error approach has been shown to be beneficial for older adults, but they often lack support to use the approach. Inspired by both interactive tutorials and trial-and-error approach, we designed an app-independent mobile service, \\textit{Synapse}, for help-givers to create a multimodal interactive tutorial on a smartphone and for help-receivers (e.g., older adults) to receive interactive guidance with trial-and-error support when they work on the same task. We conducted a user study with 18 older adults who were 60 and over. Our quantitative and qualitative results show that Synapse provided better support than the traditional video approach and enabled participants to feel more confident and motivated. Lastly, we present further design considerations to better support older adults with trial-and-error on smartphones.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550321"}, {"primary_key": "1475956", "vector": [], "sparse_vector": [], "title": "I Want to Know Your Hand: Authentication on Commodity Mobile Phones Based on Your Hand&apos;s Vibrations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present HoldPass, the first system that can authenticate a user while they simply hold their phone. It uses the heart activity as biometric trait sensed via the hand vibrations in response to the cardiac cycle - a process known as ballistocardiography (BCG). While heart activity has been used for biometric authentication, sensing it through hand-based ballistocardiography (Hand-BCG) using standard sensors found on commodity mobile phones is an uncharted territory. Using a combination of in-depth qualitative analysis and large-scale quantitative analysis involving over 100 volunteers, we paint a detailed picture of opportunities and challenges. Authentication based on Hand-BCG is shown to be feasible but the signal is weak, uniquely prone to motion artifacts and does not land itself to the common approach of alignment-based authentication. HoldPass addresses these challenges by introducing a novel alignment-free authentication scheme that builds on asynchronous signal slicing and a data-driven algorithm for identifying a reduced set of features for characterizing a user. We implement HoldPass and evaluate it using a multi-modal approach: a large-case study involving 112 volunteers and targeted studies with a smaller set of volunteers over a period of several months. The data shows that HoldPass provides an authentication accuracy and user experience on par with or better than state-of-the-art systems with stronger requirements on hardware and/or user participation.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534575"}, {"primary_key": "1475963", "vector": [], "sparse_vector": [], "title": "Understanding Privacy Risks and Perceived Benefits in Open Dataset Collection for Mobile Affective Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Collecting large-scale mobile and wearable sensor datasets from daily contexts is essential in developing machine learning models for enabling everyday affective computing applications. However, there is a lack of knowledge on data contributors' perceived benefits and risks in participating in open dataset collection projects. To bridge this gap, we conducted an in-situ study on building an open dataset with mobile and wearable devices for affective computing research (N = 100, 4 weeks). Our study results showed that a mixture of financial and altruistic benefits was important in eliciting data contribution. Sensor-specific risks were largely associated with the revelation of personal traits and social behaviors. However, most of the participants were less concerned with open dataset collection and their perceived sensitivity of each sensor data did not change over time. We further discuss alternative approaches to promote data contributors' motivations and suggest design guidelines to alleviate potential privacy concerns in mobile open dataset collection.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534623"}, {"primary_key": "1475969", "vector": [], "sparse_vector": [], "title": "Towards Ubiquitous Personalized Music Recommendation with Smart Bracelets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, recommender systems play an increasingly important role in the music scenario. Generally, music preferences are related to internal and external conditions. For example, mood state and ongoing activity will affect users' music preferences. However, conventional music recommenders cannot capture these conditions since they only utilize the online data but ignore the impact of physical-world information. In this paper, we leverage the contexts from low-cost smart bracelets for ubiquitous personalized recommendation to meet users' music preference. We first conduct a large-scale questionnaire survey, which illustrates moods, activities, and environments will affect music preferences. Then we perform a one-week field study among 30 participants, where they receive personalized music recommendation and record preferences and mood. Meanwhile, participants' context information is collected with bracelets. Analyses on the data demonstrate significant relationships between music preference, mood, and bracelet contexts. Furthermore, we propose a novel Multi-task Ubiquitous Music Recommendation model (MUMR) to predict personalized music preference with bracelet contexts as input and mood prediction as an auxiliary task. Experiments show significant improvement in music recommendation performances with MUMR. Our work demonstrates the possibility of ubiquitous personalized music recommendations with smart bracelets data, which is an encouraging step towards building recommender systems aware of physical-world contexts.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550333"}, {"primary_key": "1475984", "vector": [], "sparse_vector": [], "title": "AdaEnlight: Energy-aware Low-light Video Stream Enhancement on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ubiquity of camera-embedded devices and the advances in deep learning have stimulated various intelligent mobile video applications. These applications often demand on-device processing of video streams to deliver real-time, high-quality services for privacy and robustness concerns. However, the performance of these applications is constrained by the raw video streams, which tend to be taken with small-aperture cameras of ubiquitous mobile platforms in dim light. Despite extensive low-light video enhancement solutions, they are unfit for deployment to mobile devices due to their complex models and and ignorance of system dynamics like energy budgets. In this paper, we propose AdaEnlight, an energy-aware low-light video stream enhancement system on mobile devices. It achieves real-time video enhancement with competitive visual quality while allowing runtime behavior adaptation to the platform-imposed dynamic energy budgets. We report extensive experiments on diverse datasets, scenarios, and platforms and demonstrate the superiority of AdaEnlight compared with state-of-the-art low-light image and video enhancement solutions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569464"}, {"primary_key": "1475990", "vector": [], "sparse_vector": [], "title": "Generalization and Personalization of Mobile Sensing-Based Mood Inference Models: An Analysis of College Students in Eight Countries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Chaitanya Nutakki", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Altangerel Chagnaa", "Amarsanaa Ganbold", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Al<PERSON><PERSON> Hume", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Can Gunel", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Mood inference with mobile sensing data has been studied in ubicomp literature over the last decade. This inference enables context-aware and personalized user experiences in general mobile apps and valuable feedback and interventions in mobile health apps. However, even though model generalization issues have been highlighted in many studies, the focus has always been on improving the accuracies of models using different sensing modalities and machine learning techniques, with datasets collected in homogeneous populations. In contrast, less attention has been given to studying the performance of mood inference models to assess whether models generalize to new countries. In this study, we collected a mobile sensing dataset with 329K self-reports from 678 participants in eight countries (China, Denmark, India, Italy, Mexico, Mongolia, Paraguay, UK) to assess the effect of geographical diversity on mood inference models. We define and evaluate country-specific (trained and tested within a country), continent-specific (trained and tested within a continent), country-agnostic (tested on a country not seen on training data), and multi-country (trained and tested with multiple countries) approaches trained on sensor data for two mood inference tasks with population-level (non-personalized) and hybrid (partially personalized) models. We show that partially personalized country-specific models perform the best yielding area under the receiver operating characteristic curve (AUROC) scores of the range 0.78-0.98 for two-class (negative vs. positive valence) and 0.76-0.94 for three-class (negative vs. neutral vs. positive valence) inference. Further, with the country-agnostic approach, we show that models do not perform well compared to country-specific settings, even when models are partially personalized. We also show that continent-specific models outperform multi-country models in the case of Europe. Overall, we uncover generalization issues of mood inference models to new countries and how the geographical similarity of countries might impact mood inference.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569483"}, {"primary_key": "1475991", "vector": [], "sparse_vector": [], "title": "Toward Proactive Support for Older Adults: Predicting the Right Moment for Providing Mobile Safety Help.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Peer support is a powerful tool in improving the digital literacy of older adults. However, while existing literature investigated reactive support, this paper examines proactive support for mobile safety. To predict moments that users need support, we conducted a user study to measure the severity of mobile scenarios (n=300) and users' attitudes toward receiving support in a specific interaction around safety on a mobile device (n=150). We compared classification methods and showed that the random forest method produces better performance than other regression models. We show that user anxiety, openness to social support, self-efficacy, and security awareness are important factors to predict willingness to receive support. We also explore various age variations in the training sample on moments users need support prediction. We find that training on the youngest population produces inferior results for older adults, and training on the aging population produces poor outcomes for young adults. We illustrate that the composition of age can affect how the sample impacts model performance. We conclude the paper by discussing how our findings can be used to design feasible proactive support applications to provide support at the right moment.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517249"}, {"primary_key": "1475992", "vector": [], "sparse_vector": [], "title": "Towards a Dynamic Inter-Sensor Correlations Learning Framework for Multi-Sensor-Based Wearable Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-sensor-based wearable human activity recognition (WHAR) is a research hotspot in the field of ubiquitous computing. Extracting effective features from multi-sensor data is essential to improve the performance of activity recognition. Despite the excellent achievements of previous works, the challenge remains for modelling the dynamic correlations between sensors. In this paper, we propose a lightweight yet efficient GCN-based dynamic inter-sensor correlations learning framework called DynamicWHAR for automatically learning the dynamic correlations between sensors. DynamicWHAR is mainly composed of two modules: Initial Feature Extraction and Dynamic Information Interaction. Firstly, Initial Feature Extraction module performs data-to-feature transformation to extract the initial features of each sensor. Subsequently, Dynamic Information Interaction module explicitly models the specific interaction intensity between any two sensors, and performs dynamic information aggregation between sensors by the learned interaction intensity. Extensive experiments on four diverse WHAR datasets and two different resource-constrained devices validate that DynamicWHAR outperforms the SOTA models in both recognition performance and computational complexity.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550331"}, {"primary_key": "1475996", "vector": [], "sparse_vector": [], "title": "Examining and Promoting Explainable Recommendations for Personal Sensing Technology Acceptance.", "authors": ["<PERSON>", "<PERSON>", "Simon D&apos;Alfonso", "<PERSON><PERSON>"], "summary": "Personal sensing is a promising approach for enabling the delivery of timely and personalised recommendations to improve mental health and well-being. However, existing research has revealed numerous barriers to personal sensing acceptance. This paper explores the influence of explanations on the acceptability of recommendations based on personal sensing. We conducted a qualitative study using five plausible personal sensing scenarios to elicit prospective users' attitudes towards personal sensing, followed by a reflective interview. Our analysis formed six nuanced design considerations for personal sensing recommendation acceptance: user personalisation, appropriate phrasing, adaptive capability, users' confidence, peer endorsement, and sense of agency. Simultaneously, we found that the availability of an explanation at each personal sensing layer positively influenced the willingness of the participants to accept personal sensing technology. Together, this paper contributes a better understanding of how we can design personal sensing technology to be more acceptable.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550297"}, {"primary_key": "1475997", "vector": [], "sparse_vector": [], "title": "Privacy-Enhancing Technology and Everyday Augmented Reality: Understanding Bystanders&apos; Varying Needs for Awareness and Consent.", "authors": ["Joseph <PERSON>;<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fundamental to Augmented Reality (AR) headsets is their capacity to visually and aurally sense the world around them, necessary to drive the positional tracking that makes rendering 3D spatial content possible. This requisite sensing also opens the door for more advanced AR-driven activities, such as augmented perception, volumetric capture and biometric identification - activities with the potential to expose bystanders to significant privacy risks. Existing Privacy-Enhancing Technologies (PETs) often safeguard against these risks at a low level e.g., instituting camera access controls. However, we argue that such PETs are incompatible with the need for always-on sensing given AR headsets' intended everyday use. Through an online survey (N=102), we examine bystanders' awareness of, and concerns regarding, potentially privacy infringing AR activities; the extent to which bystanders' consent should be sought; and the level of granularity of information necessary to provide awareness of AR activities to bystanders. Our findings suggest that PETs should take into account the AR activity type, and relationship to bystanders, selectively facilitating awareness and consent. In this way, we can ensure bystanders feel their privacy is respected by everyday AR headsets, and avoid unnecessary rejection of these powerful devices by society.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569501"}, {"primary_key": "1476001", "vector": [], "sparse_vector": [], "title": "Template Matching Based Early Exit CNN for Energy-efficient Myocardial Infarction Detection on Low-power Wearable Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Myocardial Infarction (MI), also known as heart attack, is a life-threatening form of heart disease that is a leading cause of death worldwide. Its recurrent and silent nature emphasizes the need for continuous monitoring through wearable devices. The wearable device solutions should provide adequate performance while being resource-constrained in terms of power and memory. This paper proposes an MI detection methodology using a Convolutional Neural Network (CNN) that outperforms the state-of-the-art works on wearable devices for two datasets - PTB and PTB-XL, while being energy and memory-efficient. Moreover, we also propose a novel Template Matching based Early Exit (TMEX) CNN architecture that further increases the energy efficiency compared to baseline architecture while maintaining similar performance. Our baseline and TMEX architecture achieve 99.33% and 99.24% accuracy on PTB dataset, whereas on PTB-XL dataset they achieve 84.36% and 84.24% accuracy, respectively. Both architectures are suitable for wearable devices requiring only 20 KB of RAM. Evaluation of real hardware shows that our baseline architecture is 0.6x to 53x more energy-efficient than the state-of-the-art works on wearable devices. Moreover, our TMEX architecture further improves the energy efficiency by 8.12% (PTB) and 6.36% (PTB-XL) while maintaining similar performance as the baseline architecture.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534580"}, {"primary_key": "1476009", "vector": [], "sparse_vector": [], "title": "M3Sense: Affect-Agnostic Multitask Representation Learning Using Multimodal Wearable Sensors.", "authors": ["<PERSON><PERSON> Sam<PERSON>un", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern smartwatches or wrist wearables having multiple physiological sensing modalities have emerged as a subtle way to detect different mental health conditions, such as anxiety, emotions, and stress. However, affect detection models depending on wrist sensors data often provide poor performance due to inconsistent or inaccurate signals and scarcity of labeled data representing a condition. Although learning representations based on the physiological similarities of the affective tasks offer a possibility to solve this problem, existing approaches fail to effectively generate representations that will work across these multiple tasks. Moreover, the problem becomes more challenging due to the large domain gap among these affective applications and the discrepancies among the multiple sensing modalities. We present M3Sense, a multi-task, multimodal representation learning framework that effectively learns the affect-agnostic physiological representations from limited labeled data and uses a novel domain alignment technique to utilize the unlabeled data from the other affective tasks to accurately detect these mental health conditions using wrist sensors only. We apply M3Sense to 3 mental health applications, and quantify the achieved performance boost compared to the state-of-the-art using extensive evaluations and ablation studies on publicly available and collected datasets. Moreover, we extensively investigate what combination of tasks and modalities aids in developing a robust Multitask Learning model for affect recognition. Our analysis shows that incorporating emotion detection in the learning models degrades the performance of anxiety and stress detection, whereas stress detection helps to boost the emotion detection performance. Our results also show that M3Sense provides consistent performance across all affective tasks and available modalities and also improves the performance of representation learning models on unseen affective tasks by 5% - 60%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534600"}, {"primary_key": "1476011", "vector": [], "sparse_vector": [], "title": "Calico: Relocatable On-cloth Wearables with Fast, Reliable, and Precise Locomotion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We explore Calico, a miniature relocatable wearable system with fast and precise locomotion for on-body interaction, actuation and sensing. Calico consists of a two-wheel robot and an on-cloth track mechanism or \"railway,\" on which the robot travels. The robot is self-contained, small in size, and has additional sensor expansion options. The track system allows the robot to move along the user's body and reach any predetermined location. It also includes rotational switches to enable complex routing options when diverging tracks are presented. We report the design and implementation of Calico with a series of technical evaluations for system performance. We then present a few application scenarios, and user studies to understand the potential of <PERSON><PERSON> as a dance trainer and also explore the qualitative perception of our scenarios to inform future research in this space.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550323"}, {"primary_key": "1476018", "vector": [], "sparse_vector": [], "title": "Learning Disentangled <PERSON>ha<PERSON> Patterns for Wearable-based Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yu <PERSON>"], "summary": "In wearable-based human activity recognition (HAR) research, one of the major challenges is the large intra-class variability problem. The collected activity signal is often, if not always, coupled with noises or bias caused by personal, environmental, or other factors, making it difficult to learn effective features for HAR tasks, especially when with inadequate data. To address this issue, in this work, we proposed a Behaviour Pattern Disentanglement (BPD) framework, which can disentangle the behavior patterns from the irrelevant noises such as personal styles or environmental noises, etc. Based on a disentanglement network, we designed several loss functions and used an adversarial training strategy for optimization, which can disentangle activity signals from the irrelevant noises with the least dependency (between them) in the feature space. Our BPD framework is flexible, and it can be used on top of existing deep learning (DL) approaches for feature refinement. Extensive experiments were conducted on four public HAR datasets, and the promising results of our proposed BPD scheme suggest its flexibility and effectiveness. This is an open-source project, and the code can be found at http://github.com/Jie-su/BPD", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517252"}, {"primary_key": "1476019", "vector": [], "sparse_vector": [], "title": "MicroFluID: A Multi-Chip RFID Tag for Interaction Sensing Based on Microfluidic Switches.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jiecheng Wu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "RFID has been widely used for activity and gesture recognition in emerging interaction paradigms given its low cost, lightweight, and pervasiveness. However, current learning-based approaches on RFID sensing require significant efforts in data collection, feature extraction, and model training. To save data processing effort, we present MicroFluID, a novel RFID artifact based on a multiple-chip structure and microfluidic switches, which informs the input state by directly reading variable ID information instead of retrieving primitive signals. Fabricated on flexible substrates, four types of microfluidic switch circuits are designed to respond to external physical events, including pressure, bend, temperature, and gravity. By default, chips are disconnected into the circuit owing to the reserved gaps in transmission line. While external input or status change occurs, conductive liquid floating in the microfluidics channels will fill the gap(s), creating a connection to certain chip(s). In prototyping the device, we conducted a series of simulations and experiments to explore the feasibility of the multi-chip tag design, key fabrication parameters, interaction performance, and users' perceptions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550296"}, {"primary_key": "1476020", "vector": [], "sparse_vector": [], "title": "On the Feasibility of Securing Vehicle-Pavement Interaction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Road surface information (e.g., smooth road or bumpy road with potholes or bumps) is important for safe driving (i.e., it's necessary to be aware of the road surface conditions during driving). However, the high-cost sensor (e.g., LiDAR and camera) based road surface sensing approaches cannot work properly in inclement weather conditions (e.g., fogging and snowing) due to the line-of-sight requirement. The low-cost and ubiquitous smartphone-based road surface sensing approach is not reliable and safe to use, since it relies on the vibration of the vehicle body to sense the road surface (i.e., the vehicle's tires need to touch the bumps on the road surface). Can we automate the contact-free road surface sensing with low-cost sensors for safe driving without requiring the vehicle's tires to touch the bumps on the road surface? In this paper, we propose Tago, a system that can achieve contact-free road surface sensing with commodity passive RFID tags. Instead of deploying RFID tags or readers along the road or lamp post (i.e., infrastructure-based deployment), we deploy the reader inside of the vehicle and attach the tag and the reader's antenna at the front end of the vehicle like the vehicle's headlights (i.e., infrastructure-free deployment). However, there is a great challenge to obtain the clean reflection from the road surface, since the reflection may be drown in the backscattered signals due to multipath effect. Moreover, it is not reliable to use the composite signals received at the reader to sense the road surface conditions. Therefore, we first comprehensively analyse the variation of composite signals received at the reader. Then, we propose a signal cancellation approach to extract the clean reflections from the road surface, such that we can accurately sense the road surface conditions for safe driving. Our experiments with different vehicles (e.g., Honda Civic Frankenfish, Folsom, Flutter and CR-V Warner) driven on different roadways (e.g., urban and residential area) show that Tago can effectively sense the road surface information.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517230"}, {"primary_key": "1476022", "vector": [], "sparse_vector": [], "title": "Naturalistic E-Scooter Maneuver Recognition with Federated Contrastive Rider Interaction Learning.", "authors": ["<PERSON><PERSON>", "Suining He"], "summary": "Smart micromobility, particularly the electric (e)-scooters, has emerged as an important ubiquitous mobility option that has proliferated within and across many cities in North America and Europe. Due to the fast speed (say, ~15km/h) and ease of maneuvering, understanding how the micromobility rider interacts with the scooter becomes essential for the e-scooter manufacturers, e-scooter sharing operators, and rider communities in promoting riding safety and relevant policy or regulations. In this paper, we propose FCRIL, a novel Federated maneuver identification and Contrastive e-scooter Rider Interaction Learning system. FCRIL aims at: (i) understanding, learning, and identifying the e-scooter rider interaction behaviors during naturalistic riding (NR) experience (without constraints on the data collection settings); and (ii) providing a novel federated maneuver learning model training and contrastive identification design for our proposed rider interaction learning (RIL). Towards the prototype and case studies of FCRIL, we have harvested an NR behavior dataset based on the inertial measurement units (IMUs), e.g., accelerometer and gyroscope, from the ubiquitous smartphones/embedded IoT devices attached to the e-scooters. Based on the harvested IMU sensor data, we have conducted extensive data analytics to derive the relevant rider maneuver patterns, including time series, spectrogram, and other statistical features, for the RIL model designs. We have designed a contrastive RIL network which takes in these maneuver features with class-to-class differentiation for comprehensive RIL and enhanced identification accuracy. Furthermore, to enhance the dynamic model training efficiency and coping with the emerging micromobility rider data privacy concerns, we have designed a novel asynchronous federated maneuver learning module, which asynchronously takes in multiple sets of model gradients (e.g., based on the IMU data from the riders' smartphones) for dynamic RIL model training and communication overhead reduction. We have conducted extensive experimental studies with different smartphone models and stand-alone IMU sensors on the e-scooters. Our experimental results have demonstrated the accuracy and effectiveness of FCRIL in learning and recognizing the e-scooter rider maneuvers.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570345"}, {"primary_key": "1476024", "vector": [], "sparse_vector": [], "title": "CAFI-AR: Contact-aware Freehand Interaction with AR Objects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Chi-Wing Fu"], "summary": "Freehand interaction enhances user experience, allowing one to use bare hands to manipulate virtual objects in AR. Yet, it remains challenging to accurately and efficiently detect contacts between real hand and virtual object, due to the imprecise captured/estimated hand geometry. This paper presents CAFI-AR, a new approach for Contact-Aware Freehand Interaction with virtual AR objects, enabling us to automatically detect hand-object contacts in real-time with low latency. Specifically, we formulate a compact deep architecture to efficiently learn to predict hand action and contact moment from sequences of captured RGB images relative to the 3D virtual object. To train the architecture for detecting contacts on AR objects, we build a new dataset with 4,008 frame sequences, each with annotated hand-object interaction information. Further, we integrate CAFI-AR into our prototyping AR system and develop various interactive scenarios, demonstrating fine-grained contact-aware interactions on a rich variety of virtual AR objects, which cannot be achieved by existing AR interaction approaches. Lastly, we also evaluate CAFI-AR, quantitatively and qualitatively, through two user studies to demonstrate its effectiveness in terms of accurately detecting the hand-object contacts and promoting fluid freehand interactions", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569499"}, {"primary_key": "1476028", "vector": [], "sparse_vector": [], "title": "StudentSADD: Rapid Mobile Depression and Suicidal Ideation Screening of College Students during the Coronavirus Pandemic.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The growing prevalence of depression and suicidal ideation among college students further exacerbated by the Coronavirus pandemic is alarming, highlighting the need for universal mental illness screening technology. With traditional screening questionnaires too burdensome to achieve universal screening in this population, data collected through mobile applications has the potential to rapidly identify at-risk students. While prior research has mostly focused on collecting passive smartphone modalities from students, smartphone sensors are also capable of capturing active modalities. The general public has demonstrated more willingness to share active than passive modalities through an app, yet no such dataset of active mobile modalities for mental illness screening exists for students. Knowing which active modalities hold strong screening capabilities for student populations is critical for developing targeted mental illness screening technology. Thus, we deployed a mobile application to over 300 students during the COVID-19 pandemic to collect the Student Suicidal Ideation and Depression Detection (StudentSADD) dataset. We report on a rich variety of machine learning models including cutting-edge multimodal pretrained deep learning classifiers on active text and voice replies to screen for depression and suicidal ideation. This unique StudentSADD dataset is a valuable resource for the community for developing mobile mental illness screening tools.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534604"}, {"primary_key": "1476037", "vector": [], "sparse_vector": [], "title": "First-Gen Lens: Assessing Mental Health of First-Generation Students across Their First Year at College Using Mobile Sensing.", "authors": ["<PERSON><PERSON>", "Subigya Nepal", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The transition from high school to college is a taxing time for young adults. New students arriving on campus navigate a myriad of challenges centered around adapting to new living situations, financial needs, academic pressures and social demands. First-year students need to gain new skills and strategies to cope with these new demands in order to make good decisions, ease their transition to independent living and ultimately succeed. In general, first-generation students are less prepared when they enter college in comparison to non-first-generation students. This presents additional challenges for first-generation students to overcome and be successful during their college years. We study first-year students through the lens of mobile phone sensing across their first year at college, including all academic terms and breaks. We collect longitudinal mobile sensing data for N=180 first-year college students, where 27 of the students are first-generation, representing 15% of the study cohort and representative of the number of first-generation students admitted each year at the study institution, Dartmouth College. We discuss risk factors, behavioral patterns and mental health of first-generation and non-first-generation students. We propose a deep learning model that accurately predicts the mental health of first-generation students by taking into account important distinguishing behavioral factors of first-generation students. Our study, which uses the StudentLife app, offers data-informed insights that could be used to identify struggling students and provide new forms of phone-based interventions with the goal of keeping students on track.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3543194"}, {"primary_key": "1476043", "vector": [], "sparse_vector": [], "title": "Lumos: An Open-Source Device for Wearable Spectroscopy Research.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Spectroscopy, the study of the interaction between electromagnetic radiation and matter, is a vital technique in many disciplines. This technique is limited to lab settings, and, as such, sensing is isolated and infrequent. Thus, it can only provide a brief snapshot of the monitored parameter. Wearable technology brings sensing and tracking technologies out into everyday life, creating longitudinal datasets that provide more insight into the monitored parameter. In this paper, we describe Lumos, an open-source device for wearable spectroscopy research. Lumos can facilitate on-body spectroscopy research in health monitoring, athletics, rehabilitation, and more. We developed an algorithm to determine the spectral response of a medium with a mean absolute error of 13nm. From this, researchers can determine the optimal spectrum and create customized sensors for their target application. We show the utility of Lumos in a pilot study, sensing of prediabetes, where we determine the relevant spectrum for glucose and create and evaluate a targeted tracking device.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569502"}, {"primary_key": "1476047", "vector": [], "sparse_vector": [], "title": "DeepBrain: Enabling Fine-Grained Brain-Robot Interaction through Human-Centered Learning of Coarse EEG Signals from Low-Cost Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Ningyi Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the recent advancements of electroencephalograph (EEG) techniques, some brain-computer interface (BCI) solutions have been explored to assist individuals performing various tasks with their minds. One promising application is to combine BCI with robotic systems so that the mobility-impaired people can control robots to take care of themselves. Towards this ultimate goal to design BCIs for mobility-impaired, we firstly conducted an online survey with 54 mobility-impaired participants who barely had previous experience with BCI to identify the challenges they face in life for the purpose of designing a personalized BCI system in need. The results revealed these challenges including small daily tasks (such as feeding and cleaning), which weigh on the financial burdens of hiring a caregiver. Meanwhile, the off-the-shelf high-fidelity BCIs are often expensive, whereas the cheaper devices only collect coarse-grained signals, preventing practical application in care aids due to lack of temporal resolution and accuracy. Based on the survey findings, we then designed DeepBrain, a human-centered learning augmented BCI system, that requires only coarse-grained brain signals with low-cost BCI equipment, but supports fine-grained brain-robot interaction and scalable multi-robot collaboration for domestic multi-task operations. A follow-up system comparison with other approaches show that the proposed human-centered solution is a promising step towards the ultimate goal, as it achieves satisfactory accuracy with less low computation resources. Also the practical brain to multi-robot interaction system validates the feasibility of our framework and model used in DeepBrain.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550334"}, {"primary_key": "1476049", "vector": [], "sparse_vector": [], "title": "TransRisk: Mobility Privacy Risk Prediction based on Transferred Knowledge.", "authors": ["<PERSON><PERSON>", "Zhiqing Hong", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human mobility data may lead to privacy concerns because a resident can be re-identified from these data by malicious attacks even with anonymized user IDs. For an urban service collecting mobility data, an efficient privacy risk assessment is essential for the privacy protection of its users. The existing methods enable efficient privacy risk assessments for service operators to fast adjust the quality of sensing data to lower privacy risk by using prediction models. However, for these prediction models, most of them require massive training data, which has to be collected and stored first. Such a large-scale long-term training data collection contradicts the purpose of privacy risk prediction for new urban services, which is to ensure that the quality of high-risk human mobility data is adjusted to low privacy risk within a short time. To solve this problem, we present a privacy risk prediction model based on transfer learning, i.e., TransRisk, to predict the privacy risk for a new target urban service through (1) small-scale short-term data of its own, and (2) the knowledge learned from data from other existing urban services. We envision the application of TransRisk on the traffic camera surveillance system and evaluate it with real-world mobility datasets already collected in a Chinese city, Shenzhen, including four source datasets, i.e., (i) one call detail record dataset (CDR) with 1.2 million users; (ii) one cellphone connection data dataset (CONN) with 1.2 million users; (iii) a vehicular GPS dataset (Vehicles) with 10 thousand vehicles; (iv) an electronic toll collection transaction dataset (ETC) with 156 thousand users, and a target dataset, i.e., a camera dataset (Camera) with 248 cameras. The results show that our model outperforms the state-of-the-art methods in terms of RMSE and MAE. Our work also provides valuable insights and implications on mobility data privacy risk assessment for both current and future large-scale services.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534581"}, {"primary_key": "1476055", "vector": [], "sparse_vector": [], "title": "Shoes++: A Smart Detachable Sole for Social Foot-to-foot Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sun", "Xiang &apos;Anthony&apos; Chen", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Feet are the foundation of our bodies that not only perform locomotion but also participate in intent and emotion expression. Thus, foot gestures are an intuitive and natural form of expression for interpersonal interaction. Recent studies have mostly introduced smart shoes as personal gadgets, while foot gestures used in multi-person foot interactions in social scenarios remain largely unexplored. We present Shoes++, which includes an inertial measurement unit (IMU)-mounted sole and an input vocabulary of social foot-to-foot gestures to support foot-based interaction. The gesture vocabulary is derived and condensed by a set of gestures elicited from a participatory design session with 12 users. We implement a machine learning model in Shoes++ which can recognize two-person and three-person social foot-to-foot gestures with 94.3% and 96.6% accuracies (N=18). In addition, the sole is designed to easily attach to and detach from various daily shoes to support comfortable social foot interaction without taking off the shoes. Based on users' qualitative feedback, we also found that Shoes++ can support team collaboration and enhance emotion expression, thus making social interactions or interpersonal dynamics more engaging in an expanded design space.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534620"}, {"primary_key": "1476057", "vector": [], "sparse_vector": [], "title": "MiniKers: Interaction-Powered Smart Environment Automation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automating operations of objects has made life easier and more convenient for billions of people, especially those with limited motor capabilities. On the other hand, even able-bodied users might not always be able to perform manual operations (e.g., both hands are occupied), and manual operations might be undesirable for hygiene purposes (e.g., contactless devices). As a result, automation systems like motion-triggered doors, remote-control window shades, contactless toilet lids have become increasingly popular in private and public environments. Yet, these systems are hampered by complex building wiring or short battery lifetimes, negating their positive benefits for accessibility, energy saving, healthcare, and other domains. In this paper we explore how these types of objects can be powered in perpetuity by the energy generated from a unique energy source - user interactions, specifically, the manual manipulations of objects by users who can afford them when they can afford them. Our assumption is that users' capabilities for object operations are heterogeneous, there are desires for both manual and automatic operations in most environments, and that automatic operations are often not needed as frequently - for example, an automatic door in a public space is often manually opened many times before a need for automatic operation shows up. The energy harvested by those manual operations would be sufficient to power that one automatic operation. We instantiate this idea by upcycling common everyday objects with devices which have various mechanical designs powered by a general-purpose backbone embedded system. We call these devices, MiniKers. We built a custom driver circuit that can enable motor mechanisms to toggle between generating powers (i.e., manual operation) and actuating objects (i.e., automatic operation). We designed a wide variety of mechanical mechanisms to retrofit existing objects and evaluated our system with a 48-hour deployment study, which proves the efficacy of MiniKers as well as shedding light into this people-as-power approach as a feasible solution to address energy needed for smart environment automation.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550287"}, {"primary_key": "1476061", "vector": [], "sparse_vector": [], "title": "HIPPO: Pervasive Hand-Grip Estimation from Everyday Interactions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hand-grip strength is widely used to estimate muscle strength and it serves as a general indicator of the overall health of a person, particularly in aging adults. Hand-grip strength is typically estimated using dynamometers or specialized force resistant pressure sensors embedded onto objects. Both of these solutions require the user to interact with a dedicated measurement device which unnecessarily restricts the contexts where estimates are acquired. We contribute HIPPO, a novel non-intrusive and opportunistic method for estimating hand-grip strength from everyday interactions with objects. HIPPO re-purposes light sensors available in wearables (e.g., rings or gloves) to capture changes in light reflectivity when people interact with objects. This allows HIPPO to non-intrusively piggyback everyday interactions for health information without affecting the user's everyday routines. We present two prototypes integrating HIPPO, an early smart glove proof-of-concept, and a further optimized solution that uses sensors integrated onto a ring. We validate HIPPO through extensive experiments and compare HIPPO against three baselines, including a clinical dynamometer. Our results show that HIPPO operates robustly across a wide range of everyday objects, and participants. The force strength estimates correlate with estimates produced by pressure-based devices, and can also determine the correct hand grip strength category with up to 86% accuracy. Our findings also suggest that users prefer our approach to existing solutions as HIPPO blends the estimation with everyday interactions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570344"}, {"primary_key": "1476068", "vector": [], "sparse_vector": [], "title": "WearSign: Pushing the Limit of Sign Language Translation Using Inertial and EMG Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sign language translation (SLT) is considered as the core technology to break the communication barrier between the deaf and hearing people. However, most studies only focus on recognizing the sequence of sign gestures (sign language recognition (SLR)), ignoring the significant difference of linguistic structures between sign language and spoken language. In this paper, we approach SLT as a spatio-temporal machine translation task and propose a wearable-based system, WearSign, to enable direct translation from the sign-induced sensory signals into spoken texts. WearSign leverages a smartwatch and an armband of ElectroMyoGraphy (EMG) sensors to capture the sophisticated sign gestures. In the design of the translation network, considering the significant modality and linguistic gap between sensory signals and spoken language, we design a multi-task encoder-decoder framework which uses sign glosses (sign gesture labels) for intermediate supervision to guide the end-to-end training. In addition, due to the lack of sufficient training data, the performance of prior studies usually degrades drastically when it comes to sentences with complex structures or unseen in the training set. To tackle this, we borrow the idea of back-translation and leverage the much more available spoken language data to synthesize the paired sign language data. We include the synthetic pairs into the training process, which enables the network to learn better sequence-to-sequence mapping as well as generate more fluent spoken language sentences. We construct an American sign language (ASL) dataset consisting of 250 commonly used sentences gathered from 15 volunteers. WearSign achieves 4.7% and 8.6% word error rate (WER) in user-independent tests and unseen sentence tests respectively. We also implement a real-time version of WearSign which runs fully on the smartphone with a low latency and energy overhead.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517257"}, {"primary_key": "1476069", "vector": [], "sparse_vector": [], "title": "Predicting Post-Operative Complications with Wearables: A Case Study with Patients Undergoing Pancreatic Surgery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Heidy Cos", "<PERSON>", "<PERSON>", "<PERSON>et <PERSON><PERSON>", "Chenyang Lu"], "summary": "Post-operative complications and hospital readmission are of great concern to surgical patients and health care providers. Wearable devices such as Fitbit wristbands enable long-term and non-intrusive monitoring of patients outside clinical environments. To build accurate predictive models based on wearable data, however, requires effective feature engineering to extract high-level features from time series data collected by the wearable sensors. This paper presents a pipeline for developing clinical predictive models based on wearable sensors. The core of the pipeline is a multi-level feature engineering framework for extracting high-level features from fine-grained time series data. The framework integrates a set of techniques tailored for noisy and incomplete wearable data collected in real-world clinical studies: (1) singular spectrum analysis for extracting high-level features from daily features over the course of the study; (2) a set of daily features that are resilient to missing data in wearable time series data; (3) a K-Nearest Neighbors (KNN) method for imputing short missing heart rate segments; (4) the integration of patients' clinical characteristics and wearable features. We evaluated the feature engineering approach and machine learning models in a clinical study involving 61 patients undergoing pancreatic surgery. Linear support vector machine (SVM) with integrated feature engineering achieved an AUROC of 0.8802 for predicting post-operative readmission or severe complications, which significantly outperformed the existing rule-based model used in clinical practice and other state-of-the-art feature engineering approaches.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534578"}, {"primary_key": "1476072", "vector": [], "sparse_vector": [], "title": "InDepth: Real-time Depth Inpainting for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile Augmented Reality (AR) demands realistic rendering of virtual content that seamlessly blends into the physical environment. For this reason, AR headsets and recent smartphones are increasingly equipped with Time-of-Flight (ToF) cameras to acquire depth maps of a scene in real-time. ToF cameras are cheap and fast, however, they suffer from several issues that affect the quality of depth data, ultimately hampering their use for mobile AR. Among them, scale errors of virtual objects - appearing much bigger or smaller than what they should be - are particularly noticeable and unpleasant. This article specifically addresses these challenges by proposing InDepth, a real-time depth inpainting system based on edge computing. InDepth employs a novel deep neural network (DNN) architecture to improve the accuracy of depth maps obtained from ToF cameras. The DNN fills holes and corrects artifacts in the depth maps with high accuracy and eight times lower inference time than the state of the art. An extensive performance evaluation in real settings shows that InDepth reduces the mean absolute error by a factor of four with respect to ARCore DepthLab. Finally, a user study reveals that InDepth is effective in rendering correctly-scaled virtual objects, outperforming DepthLab.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517260"}, {"primary_key": "1476079", "vector": [], "sparse_vector": [], "title": "LITAR: Visually Coherent Lighting for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "Chongyang Ma", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An accurate understanding of omnidirectional environment lighting is crucial for high-quality virtual object rendering in mobile augmented reality (AR). In particular, to support reflective rendering, existing methods have leveraged deep learning models to estimate or have used physical light probes to capture physical lighting, typically represented in the form of an environment map. However, these methods often fail to provide visually coherent details or require additional setups. For example, the commercial framework ARKit uses a convolutional neural network that can generate realistic environment maps; however the corresponding reflective rendering might not match the physical environments. In this work, we present the design and implementation of a lighting reconstruction framework called LITAR that enables realistic and visually-coherent rendering. LITAR addresses several challenges of supporting lighting information for mobile AR. First, to address the spatial variance problem, LITAR uses two-field lighting reconstruction to divide the lighting reconstruction task into the spatial variance-aware near-field reconstruction and the directional-aware far-field reconstruction. The corresponding environment map allows reflective rendering with correct color tones. Second, LITAR uses two noise-tolerant data capturing policies to ensure data quality, namely guided bootstrapped movement and motion-based automatic capturing. Third, to handle the mismatch between the mobile computation capability and the high computation requirement of lighting reconstruction, LITAR employs two novel real-time environment map rendering techniques called multi-resolution projection and anchor extrapolation. These two techniques effectively remove the need of time-consuming mesh reconstruction while maintaining visual quality. Lastly, LITAR provides several knobs to facilitate mobile AR application developers making quality and performance trade-offs in lighting reconstruction. We evaluated the performance of LITAR using a small-scale testbed experiment and a controlled simulation. Our testbed-based evaluation shows that LITAR achieves more visually coherent rendering effects than ARKit. Our design of multi-resolution projection significantly reduces the time of point cloud projection from about 3 seconds to 14.6 milliseconds. Our simulation shows that LITAR, on average, achieves up to 44.1% higher PSNR value than a recent work Xihe on two complex objects with physically-based materials.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550291"}, {"primary_key": "1476080", "vector": [], "sparse_vector": [], "title": "Affective Touch as Immediate and Passive Wearable Intervention.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigated affective touch as a new pathway to passively mitigate in-the-moment anxiety. While existing mobile interventions offer great promises for health and well-being, they typically focus on achieving long-term effects such as shifting behaviors. As such, most mobile interventions are not applicable to provide immediate help in acute conditions -- when a user experiences a high anxiety level during ongoing events (e.g., completing high-stake tasks or mitigating interpersonal conflicts). A few works have developed passive interventions that are effective in-the-moment by leveraging breathing regulations and biofeedback. In this paper, we drew on neuroscientific findings on affective touch, the slow stroking on hairy skin that can elicit innate pleasantness and evaluated affective touch as a mobile health intervention. To induce affective touch, we first engineered a wearable device that renders a soft stroking sensation on the user's forearm. Then, we conducted a between-group experiment, in which participants underwent high-stress situations with/without receiving affective touch and post-experiment interviews, with 24 participants. Our results showed that participants who received affective touch experienced lower state anxiety and the same physiological stress response level compared to the control group participants. We also found that affective touch facilitated emotion regulation by rendering pleasantness, providing emotional support, and shifting attention. Finally, we discussed the immediate effect of affective touch on anxiety and physiological stress, the benefits of affective touch as a passive intervention, and the implementation considerations to use affective touch in just-in-time systems.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569484"}, {"primary_key": "1476083", "vector": [], "sparse_vector": [], "title": "Learning on the Rings: Self-Supervised 3D Finger Motion Tracking Using Wearable Sensors.", "authors": ["<PERSON><PERSON>", "Taiting Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents ssLOTR (self-supervised learning on the rings), a system that shows the feasibility of designing self-supervised learning based techniques for 3D finger motion tracking using a custom-designed wearable inertial measurement unit (IMU) sensor with a minimal overhead of labeled training data. Ubiquitous finger motion tracking enables a number of applications in augmented and virtual reality, sign language recognition, rehabilitation healthcare, sports analytics, etc. However, unlike vision, there are no large-scale training datasets for developing robust machine learning (ML) models on wearable devices. ssLOTR designs ML models based on data augmentation and self-supervised learning to first extract efficient representations from raw IMU data without the need for any training labels. The extracted representations are further trained with small-scale labeled training data. In comparison to fully supervised learning, we show that only 15% of labeled training data is sufficient with self-supervised learning to achieve similar accuracy. Our sensor device is designed using a two-layer printed circuit board (PCB) to minimize the footprint and uses a combination of Polylactic acid (PLA) and Thermoplastic polyurethane (TPU) as housing materials for sturdiness and flexibility. It incorporates a system-on-chip (SoC) microcontroller with integrated WiFi/Bluetooth Low Energy (BLE) modules for real-time wireless communication, portability, and ubiquity. In contrast to gloves, our device is worn like rings on fingers, and therefore, does not impede dexterous finger motion. Extensive evaluation with 12 users depicts a 3D joint angle tracking accuracy of 9.07° (joint position accuracy of 6.55mm) with robustness to natural variation in sensor positions, wrist motion, etc, with low overhead in latency and power consumption on embedded platforms.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534587"}, {"primary_key": "1476086", "vector": [], "sparse_vector": [], "title": "Are You Left Out?: An Efficient and Fair Federated Learning for Personalized Profiles on Wearable Devices of Inferior Networking Conditions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pan Hui"], "summary": "Wearable computers engage in percutaneous interactions with human users and revolutionize the way of learning human activities. Due to rising privacy concerns, federated learning has been recently proposed to train wearable data with privacy preservation collaboratively. However, under the state-of-the-art (SOTA) schemes, user profiles on wearable devices of inferior networking conditions are regarded as 'left out'. Such schemes suffer from three fundamental limitations: (1) the widely adopted network-capacity-based client selection leads to biased training; (2) the aggregation has low communication efficiency; (3) users lack convenient channels for providing feedback on wearable devices. Therefore, this paper proposes a Fair and Communication-efficient Federated Learning scheme, namely FCFL. FCFL is a full-stack learning system specifically designed for wearable computers, improving the SOTA performance in terms of communication efficiency, fairness, personalization, and user experience. To this end, we design a technique named ThrowRightAway (TRA) to loose the network capacity constraints. Clients with poor networks are allowed to be selected as participators to improve the representation and guarantee the model's fairness. Remarkably, we propose Movement Aware Federated Learning (MAFL) to aggregate only the model updates with top contributions to the global model for the sake of communication efficiency. Accordingly, we implemented an FCFL-supported prototype as a sports application on smartwatches. Our comprehensive evaluation demonstrated that FCFL is a communication efficient scheme significantly reducing uploaded data by up to 29.77%, with a prominent feature of guaranteeing enhanced fairness up to 65.07%. Also, FCFL achieves robust personalization performance (i.e., 20% improvements of global model accuracy) in the face of packet loss below a certain fraction (10%-30%). A follow-up user survey shows that our FCFL-supported prototypical system on wearable devices significantly reduces users' workload.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534585"}, {"primary_key": "1475878", "vector": [], "sparse_vector": [], "title": "The City as a Personal Assistant: Turning Urban Landmarks into Conversational Agents for Serving Hyper Local Information.", "authors": ["Utku Günay Acer", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conversational agents are increasingly becoming digital partners in our everyday computational experiences. Although rich and fresh in content, they are oblivious to users' locality beyond geospatial weather and traffic conditions. We introduce <PERSON><PERSON>, a hyper-local conversational agent embedded deeply into the urban infrastructure that provides rich, purposeful, detailed, and in some cases, playful information relevant to a neighbourhood. Drawing lessons from a mixed-method contextual study (online survey, n = 1992 and semi-structured interviews, n = 21), we identify requirements for such a hyper-local conversational agent and a sample set of questions serving urban neighbourhoods of Belgium. Our agent design is manifested into a two-part system. First, a multi-modal reasoning engine serves as a hyper-local information source using automated machine-learning models operating on camera, microphone, and environmental sensor data. Second, a smart conversational speaker and a smartphone application serve as hyper-local information access points. Finally, we introduce a covert communication mechanism over Wi-Fi management frames that bridges the two parts of our Lingo system and enables the privacy-preserving proxemic interactions. We describe the design, implementation, and technical assessment of Lingo together with usability (n = 20) and real-world deployment (n = 5) studies. We reflect on information quality, accessibility benefits, and interaction dynamics and demonstrate the efficacy of Lingo in offering hyper-local information at the finest granularity in urban neighbourhoods while reducing access time up to a factor of 25.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534573"}, {"primary_key": "1475879", "vector": [], "sparse_vector": [], "title": "MiShape: Accurate Human Silhouettes and Body Joints from Commodity Millimeter-Wave Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sanjib Sur", "<PERSON><PERSON>"], "summary": "We propose MiShape, a millimeter-wave (mmWave) wireless signal based imaging system that generates high-resolution human silhouettes and predicts 3D locations of body joints. The system can capture human motions in real-time under low light and low-visibility conditions. Unlike existing vision-based motion capture systems, MiShape is privacy non-invasive and can generalize to a wide range of motion tracking applications at-home. To overcome the challenges with low-resolution, specularity, and aliasing in images from Commercial-Off-The-Shelf (COTS) mmWave systems, MiShape designs deep learning models based on conditional Generative Adversarial Networks and incorporates the rules of human biomechanics. We have customized MiShape for gait monitoring, but the model is well adaptive to any tracking applications with limited fine-tuning samples. We experimentally evaluate MiShape with real data collected from a COTS mmWave system for 10 volunteers, with diverse ages, gender, height, and somatotype, performing different poses. Our experimental results demonstrate that MiShape delivers high-resolution silhouettes and accurate body poses on par with an existing vision-based system, and unlocks the potential of mmWave systems, such as 5G home wireless routers, for privacy-noninvasive healthcare applications.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550300"}, {"primary_key": "1475880", "vector": [], "sparse_vector": [], "title": "Towards Automating Retinoscopy for Refractive Error Diagnosis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Refractive error is the most common eye disorder and is the key cause behind correctable visual impairment, responsible for nearly 80% of the visual impairment in the US. Refractive error can be diagnosed using multiple methods, including subjective refraction, retinoscopy, and autorefractors. Although subjective refraction is the gold standard, it requires cooperation from the patient and hence is not suitable for infants, young children, and developmentally delayed adults. Retinoscopy is an objective refraction method that does not require any input from the patient. However, retinoscopy requires a lens kit and a trained examiner, which limits its use for mass screening. In this work, we automate retinoscopy by attaching a smartphone to a retinoscope and recording retinoscopic videos with the patient wearing a custom pair of paper frames. We develop a video processing pipeline that takes retinoscopic videos as input and estimates the net refractive error based on our proposed extension of the retinoscopy mathematical model. Our system alleviates the need for a lens kit and can be performed by an untrained examiner. In a clinical trial with 185 eyes, we achieved a sensitivity of 91.0% and specificity of 74.0% on refractive error diagnosis. Moreover, the mean absolute error of our approach was 0.75±0.67D on net refractive error estimation compared to subjective refraction measurements. Our results indicate that our approach has the potential to be used as a retinoscopy-based refractive error screening tool in real-world medical settings.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550283"}, {"primary_key": "1475881", "vector": [], "sparse_vector": [], "title": "AdaMICA: Adaptive Multicore Intermittent Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies on intermittent computing target single-core processors and underestimate the efficient parallel execution of highly-parallelizable machine learning tasks. Even though general-purpose multicore processors provide a high degree of parallelism and programming flexibility, intermittent computing has not exploited them yet. Filling this gap, we introduce AdaMICA (Adaptive Multicore Intermittent Computing) runtime that supports, for the first time, parallel intermittent computing and provides the highest degree of flexibility of programmable general-purpose multiple cores. AdaMICA is adaptive since it responds to the changes in the environmental power availability by dynamically reconfiguring the underlying multicore architecture to use the power most optimally. Our results demonstrate that AdaMICA significantly increases the throughput (52% on average) and decreases the latency (31% on average) by dynamically scaling the underlying architecture, considering the variations in the unpredictable harvested energy.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550304"}, {"primary_key": "1475883", "vector": [], "sparse_vector": [], "title": "Gesture Recognition Method Using Acoustic Sensing on Usual Garment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this study, we show a new gesture recognition method for clothing-based gesture input methods using active and passive acoustic sensing. Our system consists of a piezoelectric speaker and a microphone. The speaker transmits ultrasonic swept sine signals, and the microphone simultaneously records the ultrasonic signals that propagate through the garment and the rubbing sounds generated by the gestures on the garment. Our method recognizes a variety of gestures, such as pinch, twist, touch, and swipe, by incorporating active and passive acoustic sensing. An important feature of our method is that it does not require a dedicated garment or embroidery embedded since our system only requires a pair of piezoelectric elements to be attached to the usual garment with a magnet. We performed recognition experiments of 11 gestures on the forearm with four types of garments made from different materials and recognition experiments of five one-handed gestures on the button of a shirt and the pocket of pants. The results of a per-user classifier confirmed that the f-scores were 83.9% and 95.9% for 11 gestures with four different types of garments and 5 gestures that were selected assuming actual use, respectively. In addition, we confirmed that the system recognizes five gestures, which can be performed with one hand, with 89.2% and 92.6% accuracy in the button and pocket sites, respectively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534579"}, {"primary_key": "1475885", "vector": [], "sparse_vector": [], "title": "DeXAR: Deep Explainable Sensor-Based Activity Recognition in Smart-Home Environments.", "authors": ["<PERSON>", "Gabriele Civitarese", "<PERSON>"], "summary": "The sensor-based recognition of Activities of Daily Living (ADLs) in smart-home environments is an active research area, with relevant applications in healthcare and ambient assisted living. The application of Explainable Artificial Intelligence (XAI) to ADLs recognition has the potential of making this process trusted, transparent and understandable. The few works that investigated this problem considered only interpretable machine learning models. In this work, we propose DeXAR, a novel methodology to transform sensor data into semantic images to take advantage of XAI methods based on Convolutional Neural Networks (CNN). We apply different XAI approaches for deep learning and, from the resulting heat maps, we generate explanations in natural language. In order to identify the most effective XAI method, we performed extensive experiments on two different datasets, with both a common-knowledge and a user-based evaluation. The results of a user study show that the white-box XAI method based on prototypes is the most effective.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517224"}, {"primary_key": "1475886", "vector": [], "sparse_vector": [], "title": "Not Only for Contact Tracing: Use of Belgium&apos;s Contact Tracing App among Young Adults.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many countries developed and deployed contact tracing apps to reduce the spread of the COVID-19 coronavirus. Prior research explored people's intent to install these apps, which is necessary to ensure effectiveness. However, adopting contact tracing apps is not enough on its own, and much less is known about how people actually use these apps. Exploring app use can help us identify additional failures or risk points in the app life cycle. In this study, we conducted 13 semi-structured interviews with young adult users of Belgium's contact-tracing app, Coronalert. The interviews were conducted approximately a year after the onset of the COVID-19 pandemic. Our findings offer potential design directions for addressing issues identified in prior work - such as methods for maintaining long-term use and better integrating with the local health systems - and offer insight into existing design tensions such as the trade-off between maintaining users' privacy (by minimizing the personal data collected) and users' desire to have more information about an exposure incident. We distill from our results and the results of prior work a framework of people's decision points in contact-tracing app use that can serve to motivate careful design of future contact tracing technology.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570348"}, {"primary_key": "1475887", "vector": [], "sparse_vector": [], "title": "RetroSphere: Self-Contained Passive 3D Controller Tracking for Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Advanced AR/VR headsets often have a dedicated depth sensor or multiple cameras, high processing power, and a high-capacity battery to track hands or controllers. However, these approaches are not compatible with the small form factor and limited thermal capacity of lightweight AR devices. In this paper, we present RetroSphere, a self-contained 6 degree of freedom (6DoF) controller tracker that can be integrated with almost any device. RetroSphere tracks a passive controller with just 3 retroreflective spheres using a stereo pair of mass-produced infrared blob trackers, each with its own infrared LED emitters. As the sphere is completely passive, no electronics or recharging is required. Each object tracking camera provides a tiny Arduino-compatible ESP32 microcontroller with the 2D position of the spheres. A lightweight stereo depth estimation algorithm that runs on the ESP32 performs 6DoF tracking of the passive controller. Also, RetroSphere provides an auto-calibration procedure to calibrate the stereo IR tracker setup. Our work builds upon <PERSON>'s Wii remote hacks and aims to enable a community of researchers, designers, and makers to use 3D input in their projects with affordable off-the-shelf components. RetroSphere achieves a tracking accuracy of about 96.5% with errors as low as ~3.5 cm over a 100 cm tracking range, validated with ground truth 3D data obtained using a LIDAR camera while consuming around 400 mW. We provide implementation details, evaluate the accuracy of our system, and demonstrate example applications, such as mobile AR drawing, 3D measurement, etc. with our Retrosphere-enabled AR glass prototype.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569479"}, {"primary_key": "1475888", "vector": [], "sparse_vector": [], "title": "WiNE: Monitoring Microwave Oven Leakage to Estimate Food Nutrients and Calorie.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Food analytic and estimation of food nutrients have an increasing demand in recent years to monitor and control food intake and calorie consumption by individuals. Microwave ovens have recently replaced conventional cooking methods due to efficient and quick heating and cooking techniques. Users estimate the food nutrient composition by using some lookup information for each of the food's ingredients or by using applications that map the picture of the food to their pre-defined dataset. These techniques are often time-consuming and not in real-time and thus can result in low accuracy. In this paper, we present WiNE, a system that introduces a new technique to estimate food nutrient composition and calorie content in real-time using microwave radiation. Our system monitors microwave oven leakage in the time and frequency domains and estimates the percentage of nutrients (carbohydrate, fat, protein, and water) present in the food. To evaluate the real-world performance of WiNE, we build a prototype using software-defined radios and conducted experiments on various food items using household microwave ovens. WiNE can estimate the food nutrient composition with a mean absolute error of ≤ 5% and the calorie content of the food with a high correlation of ~ 0.97.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550313"}, {"primary_key": "1475889", "vector": [], "sparse_vector": [], "title": "One Ring to Rule Them All: An Empirical Understanding of Day-to-Day Smartring Usage Through In-Situ Diary Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartrings have potential to extend our ubiquitous control through their always available and finger-worn location, as well as their quick and subtle interactions. As such, smartrings have gained popularity in research and in commercial usage; however, they often concentrate on a singular or novel aspect of a smartring's potential. While with any emerging technology the focus on these individual components is important, there is a lack of broader empirical understanding regarding a user's intentions for smartring usage. Thus in this work, we investigate concrete and reported smartring usage scenarios throughout the daily lives of participants. During a two-week in-situ diary study (N = 14), utilizing a mock smartring, we provide an initial understanding of the potential tasks, daily activities, connected devices, and interactions for which augmentation with a smartring was desired. We further highlight patterns of imagined smartring use found by our participants. Finally, we provide and discuss guidelines, grounded through our found knowledge, to inform research and development towards the design of future smartrings.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550315"}, {"primary_key": "1475890", "vector": [], "sparse_vector": [], "title": "PEPPER: Empowering User-Centric Recommender Systems over Gossip Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recommender systems are proving to be an invaluable tool for extracting user-relevant content helping users in their daily activities (e.g., finding relevant places to visit, content to consume, items to purchase). However, to be effective, these systems need to collect and analyze large volumes of personal data (e.g., location check-ins, movie ratings, click rates .. etc.), which exposes users to numerous privacy threats. In this context, recommender systems based on Federated Learning (FL) appear to be a promising solution for enforcing privacy as they compute accurate recommendations while keeping personal data on the users' devices. However, FL, and therefore FL-based recommender systems, rely on a central server that can experience scalability issues besides being vulnerable to attacks. To remedy this, we propose PEPPER, a decentralized recommender system based on gossip learning principles. In PEPPER, users gossip model updates and aggregate them asynchronously. At the heart of PEPPER reside two key components: a personalized peer-sampling protocol that keeps in the neighborhood of each node, a proportion of nodes that have similar interests to the former and a simple yet effective model aggregation function that builds a model that is better suited to each user. Through experiments on three real datasets implementing two use cases: a location check-in recommendation and a movie recommendation, we demonstrate that our solution converges up to 42% faster than with other decentralized solutions providing up to 9% improvement on average performance metric such as hit ratio and up to 21% improvement on long tail performance compared to decentralized competitors.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550302"}, {"primary_key": "1475891", "vector": [], "sparse_vector": [], "title": "Designing Reflective Derived Metrics for Fitness Trackers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Personal tracking devices are equipped with more and more sensors and offer an ever-increasing level of accuracy. Yet, this comes at the cost of increased complexity. To deal with that problem, fitness trackers use derived metrics---scores calculated based on sensor data, e.g. a stress score. This means that part of the agency in interpreting health data is transferred from the user to the tracker. In this paper, we investigate the consequences of that transition and study how derived metrics can be designed to offer an optimal personal informatics experience. We conducted an online survey and a series of interviews which examined a health score (a hypothetical derived metric) at three levels of abstraction. We found that the medium abstraction level led to the highest level of reflection. Further, we determined that presenting the metric without contextual information led to decreased transparency and meaning. Our work contributes guidelines for designing effective derived metrics.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569475"}, {"primary_key": "1475893", "vector": [], "sparse_vector": [], "title": "Technical Design Space Analysis for Unobtrusive Driver Emotion Assessment Using Multi-Domain Context.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Driver emotions play a vital role in driving safety and performance. Consequently, regulating driver emotions through empathic interfaces have been investigated thoroughly. However, the prerequisite - driver emotion sensing - is a challenging endeavor: Body-worn physiological sensors are intrusive, while facial and speech recognition only capture overt emotions. In a user study (N=27), we investigate how emotions can be unobtrusively predicted by analyzing a rich set of contextual features captured by a smartphone, including road and traffic conditions, visual scene analysis, audio, weather information, and car speed. We derive a technical design space to inform practitioners and researchers about the most indicative sensing modalities, the corresponding impact on users' privacy, and the computational cost associated with processing this data. Our analysis shows that contextual emotion recognition is significantly more robust than facial recognition, leading to an overall improvement of 7% using a leave-one-participant-out cross-validation.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569466"}, {"primary_key": "1475894", "vector": [], "sparse_vector": [], "title": "Leveraging Sound and Wrist Motion to Detect Activities of Daily Living with Commodity Smartwatches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>az"], "summary": "Automatically recognizing a broad spectrum of human activities is key to realizing many compelling applications in health, personal assistance, human-computer interaction and smart environments. However, in real-world settings, approaches to human action perception have been largely constrained to detecting mobility states, e.g., walking, running, standing. In this work, we explore the use of inertial-acoustic sensing provided by off-the-shelf commodity smartwatches for detecting activities of daily living (ADLs). We conduct a semi-naturalistic study with a diverse set of 15 participants in their own homes and show that acoustic and inertial sensor data can be combined to recognize 23 activities such as writing, cooking, and cleaning with high accuracy. We further conduct a completely in-the-wild study with 5 participants to better evaluate the feasibility of our system in practical unconstrained scenarios. We comprehensively studied various baseline machine learning and deep learning models with three different fusion strategies, demonstrating the benefit of combining inertial and acoustic data for ADL recognition. Our analysis underscores the feasibility of high-performing recognition of daily activities using inertial-acoustic data from practical off-the-shelf wrist-worn devices while also uncovering challenges faced in unconstrained settings. We encourage researchers to use our public dataset to further push the boundary of ADL recognition in-the-wild.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534582"}, {"primary_key": "1475895", "vector": [], "sparse_vector": [], "title": "DeepPCD: Enabling AutoCompletion of Indoor Point Clouds with Deep Learning.", "authors": ["<PERSON><PERSON>", "Sanjib Sur"], "summary": "3D Point Cloud Data (PCD) is an efficient machine representation for surrounding environments and has been used in many applications. But the measured PCD is often incomplete and sparse due to the sensor occlusion and poor lighting conditions. To automatically reconstruct complete PCD from the incomplete ones, we propose DeepPCD, a deep-learning-based system that reconstructs both geometric and color information for large indoor environments. For geometric reconstruction, DeepPCD uses a novel patch based technique that splits the PCD into multiple parts, approximates, extends, and independently reconstructs the parts by 3D planes, and then merges and refines them. For color reconstruction, DeepPCD uses a conditional Generative Adversarial Network to infer the missing color of the geometrically reconstructed PCD by using the color feature extracted from incomplete color PCD. We experimentally evaluate DeepPCD with several real PCD collected from large, diverse indoor environments and explore the feasibility of PCD autocompletion in various ubiquitous sensing applications.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534611"}, {"primary_key": "1475896", "vector": [], "sparse_vector": [], "title": "MilliPCD: Beyond Traditional Vision Indoor Point Cloud Generation via Handheld Millimeter-Wave Devices.", "authors": ["<PERSON><PERSON>", "Sanjib Sur"], "summary": "3D Point Cloud Data (PCD) has been used in many research and commercial applications widely, such as autonomous driving, robotics, and VR/AR. But existing PCD generation systems based on RGB-D and LiDARs require robust lighting and an unobstructed field of view of the target scenes. So, they may not work properly under challenging environmental conditions. Recently, millimeter-wave (mmWave) based imaging systems have raised considerable interest due to their ability to work in dark environments. But the resolution and quality of the PCD from these mmWave imaging systems are very poor. To improve the quality of PCD, we design and implement MilliPCD, a \"beyond traditional vision\" PCD generation system for handheld mmWave devices, by integrating traditional signal processing with advanced deep learning based algorithms. We evaluate MilliPCD with real mmWave reflected signals collected from large, diverse indoor environments, and the results show improvements in the quality w.r.t. the existing algorithms, both quantitatively and qualitatively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569497"}, {"primary_key": "1475898", "vector": [], "sparse_vector": [], "title": "Guard Your Heart Silently: Continuous Electrocardiogram Waveform Monitoring with Wrist-Worn Motion Sensor.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, particular attention has been devoted to continuous electrocardiogram (ECG) waveform monitoring due to its numerous applications. However, existing solutions require users to be confined to particular locations, rely on dedicated and expensive hardware, or require active user participation. The constrained recording conditions prevent them from being deployed in many practical application scenarios. In view of this, we present VibCardiogram, a continuous and reliable design for estimating ECG waveform shape via ubiquitous wrist-worn wearables; it renders a personal ECG waveform shape estimating system with prolonged recording time accessible to a larger sector of the population. Instead of adding auxiliary sensors to wearables, VibCardiogram leverages the widely integrated motion sensor to characterize cardiac activities, and interpret them to generate an alternative signal that has the same waveform shape as the ECG signal. Specifically, VibCardiogram extracts the cardiogenic body vibrations from noisy sensor data. As the waveform variability and inconstant period hinder the segmentation of cardiac cycles, VibCardiogram extracts features and realizes accurate segmentation via machine learning. To parse cardiac activities from vibration signals, we build a deep-learning pipeline associating the encoder-decoder framework and Generative Adversarial Networks. With dedicated construction and training, it can estimate the ECG waveform accurately. Experiments with 20 participants show that VibCardiogram can achieve an average estimation error of 5.989% for waveform amplitude estimation, which is within the 10% margins regulated by the American National Standards Institute. Moreover, the promising results further confirm that VibCardiogram effectively extracts Heart Rate Variability features and supports downstream applications.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550307"}, {"primary_key": "1475899", "vector": [], "sparse_vector": [], "title": "Cross Vision-RF Gait Re-identification with Low-cost RGB-D Cameras and mmWave Radars.", "authors": ["Dongjiang Cao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Human identification is a key requirement for many applications in everyday life, such as personalized services, automatic surveillance, continuous authentication, and contact tracing during pandemics, etc. This work studies the problem of cross-modal human re-identification (ReID), in response to the regular human movements across camera-allowed regions (e.g., streets) and camera-restricted regions (e.g., offices) deployed with heterogeneous sensors. By leveraging the emerging low-cost RGB-D cameras and mmWave radars, we propose the first-of-its-kind vision-RF system for cross-modal multi-person ReID at the same time. Firstly, to address the fundamental inter-modality discrepancy, we propose a novel signature synthesis algorithm based on the observed specular reflection model of a human body. Secondly, an effective cross-modal deep metric learning model is introduced to deal with interference caused by unsynchronized data across radars and cameras. Through extensive experiments in both indoor and outdoor environments, we demonstrate that our proposed system is able to achieve ~ 92.5% top-1 accuracy and ~ 97.5% top-5 accuracy out of 56 volunteers. We also show that our proposed system is able to robustly reidentify subjects even when multiple subjects are present in the sensors' field of view.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550325"}, {"primary_key": "1475900", "vector": [], "sparse_vector": [], "title": "Testing a Drop of Liquid Using Smartphone LiDAR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the first system to determine fluid properties using the LiDAR sensors present on modern smartphones. Traditional methods of measuring properties like viscosity require expensive laboratory equipment or a relatively large amount of fluid. In contrast, our smartphone-based method is accessible, contactless and works with just a single drop of liquid. Our design works by targeting a coherent LiDAR beam from the phone onto the liquid. Using the phone's camera, we capture the characteristic laser speckle pattern that is formed by the interference of light reflecting from light-scattering particles. By correlating the fluctuations in speckle intensity over time, we can characterize the Brownian motion within the liquid which is correlated with its viscosity. The speckle pattern can be captured on a range of phone cameras and does not require external magnifiers. Our results show that we can distinguish between different fat contents as well as identify adulterated milk. Further, algorithms can classify between ten different liquids using the smartphone LiDAR speckle patterns. Finally, we conducted a clinical study with whole blood samples across 30 patients showing that our approach can distinguish between coagulated and uncoagulated blood using a single drop of blood.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517256"}, {"primary_key": "1475901", "vector": [], "sparse_vector": [], "title": "Sensor-free Soil Moisture Sensing Using LoRa Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Soil moisture sensing is one of the most important components in smart agriculture. It plays a critical role in increasing crop yields and reducing water waste. However, existing commercial soil moisture sensors are either expensive or inaccurate, limiting their real-world deployment. In this paper, we utilize wide-area LoRa signals to sense soil moisture without a need of dedicated soil moisture sensors. Different from traditional usage of LoRa in smart agriculture which is only for sensor data transmission, we leverage LoRa signal itself as a powerful sensing tool. The key insight is that the dielectric permittivity of soil which is closely related to soil moisture can be obtained from phase readings of LoRa signals. Therefore, antennas of a LoRa node can be placed in the soil to capture signal phase readings for soil moisture measurements. Though promising, it is non-trivial to extract accurate phase information due to unsynchronization of LoRa transmitter and receiver. In this work, we propose to include a low-cost switch to equip the LoRa node with two antennas to address the issue. We develop a delicate chirp ratio approach to cancel out the phase offset caused by transceiver unsynchronization to extract accurate phase information. The proposed system design has multiple unique advantages including high accuracy, robustness against motion interference and large sensing range for large-scale deployment in smart agriculture. Experiments with commodity LoRa nodes show that our system can accurately estimate soil moisture at an average error of 3.1%, achieving a performance comparable to high-end commodity soil moisture sensors. Field studies show that the proposed system can accurately sense soil moisture even when the LoRa gateway is 100 m away from the LoRa node, enabling wide-area soil moisture sensing for the first time.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534608"}, {"primary_key": "1475902", "vector": [], "sparse_vector": [], "title": "SSpoon: A Shape-changing Spoon That Optimizes Bite Size for Eating Rate Regulation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Sheng<PERSON> Zhao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One key strategy of combating obesity is to slow down eating; however, this is difficult to achieve due to people's habitual nature. In this paper, we explored the feasibility of incorporating shape-changing interface into an eating spoon to directly intervene in undesirable eating behaviour. First, we investigated the optimal dimension (i.e., Z-depth) and ideal range of spoon transformation for different food forms that could affect bite size while maintaining usability. Those findings allowed the development of SSpoon prototype through a series of design explorations that are optimised for user's adoption. Then, we applied two shape-changing strategies: instant transformations based on food forms and subtle transformations based on food intake) and examined in two comparative studies involving a full course meal using Wizard-of-Oz approach. The results indicated that SSpoon could achieve comparable effects to a small spoon (5ml) in reducing eating rate by 13.7-16.1% and food consumption by 4.4-4.6%, while retaining similar user satisfaction as a normal eating spoon (10ml). These results demonstrate the feasibility of a shape-changing eating utensil as a promising alternative to combat the growing concern of obesity.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550312"}, {"primary_key": "1475903", "vector": [], "sparse_vector": [], "title": "LiSee: A Headphone that Provides All-day Assistance for Blind and Low-vision Users to Reach Surrounding Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "Lihua Lin", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reaching surrounding target objects is difficult for blind and low-vision (BLV) users, affecting their daily life. Based on interviews and exchanges, we propose an unobtrusive wearable system called LiSee to provide BLV users with all-day assistance. Following a user-centered design method, we carefully designed the LiSee prototype, which integrates various electronic components and is disguised as a neckband headphone such that it is an extension of the existing headphone. The top-level software includes a series of seamless image processing algorithms to solve the challenges brought by the unconstrained wearable form so as to ensure excellent real-time performance. Moreover, users are provided with a personalized guidance scheme so that they can use LiSee quickly based on their personal expertise. Finally, a system evaluation and a user study were completed in the laboratory and participants' homes. The results show that LiSee works robustly, indicating that it can meet the daily needs of most participants to reach surrounding objects.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550282"}, {"primary_key": "1475906", "vector": [], "sparse_vector": [], "title": "SwipePass: Acoustic-based Second-factor User Authentication for Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu"], "summary": "Pattern lock-based authentication has been widely adopted in modern smartphones. However, this scheme relies essentially on passwords, making it vulnerable to various side-channel attacks such as the smudge attack and the shoulder-surfing attack. In this paper, we propose a second-factor authentication system named SwipePass, which authenticates a smartphone user by examining the distinct physiological and behavioral characteristics embedded in the user's pattern lock process. By emitting and receiving modulated audio using the built-in modules of the smartphone, SwipePass can sense the entire unlocking process and extract discriminative features to authenticate the user from the signal variations associated with hand dynamics. Moreover, to alleviate the burden of data collection in the user enrollment phase, we conduct an in-depth analysis of users' behaviors under different conditions and propose two augmentation techniques to significantly improve identification accuracy even when only a few training samples are available. Finally, we design a robust authentication model based on CNN-LSTM and One-Class SVM for user identification and spoofer detection. We implement SwipePass on three off-the-shelf smartphones and conduct extensive evaluations in different real-world scenarios. Experiments involving 36 participants show that SwipePass achieves an average identification accuracy of 96.8% while maintaining a false accept rate below 0.45% against various attacks.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550292"}, {"primary_key": "1475907", "vector": [], "sparse_vector": [], "title": "Phone-based Ambient Temperature Measurement with a New Confidence-based Truth Inference Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Monitoring the indoor temperature can help saving of energy and improve the comfort level. Smartphone, as a ubiquitous device, can be an additional data source to provide the ambient temperature estimation. However, the estimation results sometimes can be unreliable due to the different phone using states. How to integrate multiple estimation results in one area to get a more accurate prediction result is still a challenge. In this work, we proposed one phone-based ambient temperature measurement system which contains two models. The first temperature prediction model takes easily accessible phone state features as inputs and outputs ambient temperature prediction with a confidence value. The second truth inference model takes multiple prediction results with confidences as inputs and outputs a referred final prediction result. Our temperature prediction model reaches 0.253°C with MAE in our testing set. We also proved by transfer learning our model can be used in other new type of phones. We evaluate the truth inference model in our testing dataset and it reaches 0.128°C, which outperforms the state-of-the-art truth inference algorithms. We believe this work can contribute to energy conservation and provide new ideas for crowdsourcing.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570347"}, {"primary_key": "1475908", "vector": [], "sparse_vector": [], "title": "iSpray: Reducing Urban Air Pollution with Intelligent Water Spraying.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite regulations and policies to improve city-level air quality in the long run, there lack precise control measures to protect critical urban spots from heavy air pollution. In this work, we propose iSpray, the first-of-its-kind data analytics engine for fine-grained PM2.5 and PM10 control at key urban areas via cost-effective water spraying. iSpray combines domain knowledge with machine learning to profile and model how water spraying affects PM25 and PM10 concentrations in time and space. It also utilizes predictions of pollution propagation paths to schedule a minimal number of sprayers to keep the pollution concentrations at key spots under control. In-field evaluations show that compared with scheduling based on real-time pollution concentrations, iSpray reduces the total sprayer switch-on time by 32%, equivalent to 1, 782 m3 water and 18, 262 kWh electricity in our deployment, while decreasing the days of poor air quality at key spots by up to 16%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517227"}, {"primary_key": "1475909", "vector": [], "sparse_vector": [], "title": "FlowSense: Monitoring Airflow in Building Ventilation Systems Using Audio Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proper indoor ventilation through buildings' heating, ventilation, and air conditioning (HVAC) systems has become an increasing public health concern that significantly impacts individuals' health and safety at home, work, and school. While much work has progressed in providing energy-efficient and user comfort for HVAC systems through IoT devices and mobile-sensing approaches, ventilation is an aspect that has received lesser attention despite its importance. With a motivation to monitor airflow from building ventilation systems through commodity sensing devices, we present FlowSense, a machine learning-based algorithm to predict airflow rate from sensed audio data in indoor spaces. Our ML technique can predict the state of an air vent-whether it is on or off-as well as the rate of air flowing through active vents. By exploiting a low-pass filter to obtain low-frequency audio signals, we put together a privacy-preserving pipeline that leverages a silence detection algorithm to only sense for sounds of air from HVAC air vent when no human speech is detected. We also propose the Minimum Persistent Sensing (MPS) as a post-processing algorithm to reduce interference from ambient noise, including ongoing human conversation, office machines, and traffic noises. Together, these techniques ensure user privacy and improve the robustness of FlowSense. We validate our approach yielding over 90% accuracy in predicting vent status and 0.96 MSE in predicting airflow rate when the device is placed within 2.25 meters away from an air vent. Additionally, we demonstrate how our approach as a mobile audio-sensing platform is robust to smartphone models, distance, and orientation. Finally, we evaluate FlowSense privacy-preserving pipeline through a user study and a Google Speech Recognition service, confirming that the audio signals we used as input data are inaudible and inconstructible.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517258"}, {"primary_key": "1475910", "vector": [], "sparse_vector": [], "title": "FLAME: Federated Learning across Multi-device Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) enables distributed training of machine learning models while keeping personal data on user devices private. While we witness increasing applications of FL in the area of mobile sensing, such as human activity recognition (HAR), FL has not been studied in the context of a multi-device environment (MDE), wherein each user owns multiple data-producing devices. With the proliferation of mobile and wearable devices, MDEs are increasingly becoming popular in ubicomp settings, therefore necessitating the study of FL in them. FL in MDEs is characterized by being not independent and identically distributed (non-IID) across clients, complicated by the presence of both user and device heterogeneities. Further, ensuring efficient utilization of system resources on FL clients in a MDE remains an important challenge. In this paper, we propose FLAME, a user-centered FL training approach to counter statistical and system heterogeneity in MDEs, and bring consistency in inference performance across devices. FLAME features (i) user-centered FL training utilizing the time alignment across devices from the same user; (ii) accuracy- and efficiency-aware device selection; and (iii) model personalization to devices. We also present an FL evaluation testbed with realistic energy drain and network bandwidth profiles, and a novel class-based data partitioning scheme to extend existing HAR datasets to a federated setup. Our experiment results on three multi-device HAR datasets show that FLAME outperforms various baselines by 4.3-25.8% higher F1 score, 1.02-2.86x greater energy efficiency, and up to 2.06x speedup in convergence to target accuracy through fair distribution of the FL workload.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550289"}, {"primary_key": "1475911", "vector": [], "sparse_vector": [], "title": "Design and Evaluation of a Clippable and Personalizable Pneumatic-haptic Feedback Device for Breathing Guidance.", "authors": ["<PERSON><PERSON>", "Neska ElHaouij", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To assist people in practicing mindful breathing and regulate their perceived workload while not disturbing the ongoing foreground task during daily routines, we developed a mobile and personalizable pneumatic-haptic feedback device that provides programmable subtle tactile feedback. The device consists of three soft inflatable actuators embedded with DIY stretchable sensors. We introduce their simple and cost-effective fabrication method. We conducted a technical and user-based evaluation of the device. The user-based evaluation focused on the personalization of the tactile feedback based on users' experience assessed during three pilot studies. Different personalization parameters have been tested, such as two tactile patterns, different levels of intensity and frequency. We collected the participants' self-reports and physiological data. Our results show that the device has the potential of a breathing guide under certain conditions. We provide the main findings and design insights from each study and suggest recommendations for developing an on-body personalizable pneumatic-haptic feedback interface.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517234"}, {"primary_key": "1475912", "vector": [], "sparse_vector": [], "title": "PPGface: Like What You Are Watching? Earphones Can &quot;Feel&quot; Your Facial Expressions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Yincheng Jin", "<PERSON> <PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recognition of facial expressions has been widely explored to represent people's emotional states. Existing facial expression recognition systems primarily rely on external cameras which make it less accessible and efficient in many real-life scenarios to monitor an individual's facial expression in a convenient and unobtrusive manner. To this end, we propose PPGface, a ubiquitous, easy-to-use, user-friendly facial expression recognition platform that leverages earable devices with built-in PPG sensor. PPGface understands the facial expressions through the dynamic PPG patterns resulting from facial muscle movements. With the aid of the accelerometer sensor, PPGface can detect and recognize the user's seven universal facial expressions and relevant body posture unobtrusively. We conducted an user study (N=20) using multimodal ResNet to evaluate the performance of PPGface, and showed that PPGface can detect different facial expressions with 93.5 accuracy and 0.93 fl-score. In addition, to explore the robustness and usability of our proposed platform, we conducted several comprehensive experiments under real-world settings. Overall results of this work validate a great potential to be employed in future commodity earable devices.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534597"}, {"primary_key": "1475913", "vector": [], "sparse_vector": [], "title": "Sounds of Health: Using Personalized Sonification Models to Communicate Health Information.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper explores the feasibility of using sonification in delivering and communicating health and wellness status on personal devices. Ambient displays have proven to inform users of their health and wellness and help them to make healthier decisions, yet, little technology provides health assessments through sounds, which can be even more pervasive than visual displays. We developed a method to generate music from user preferences and evaluated it in a two-step user study. In the first step, we acquired general healthiness impressions from each user. In the second step, we generated customized melodies from music preferences in the first step to capture participants' perceived healthiness of those melodies. We deployed our surveys for 55 participants to complete on their own over 31 days. We analyzed the data to understand commonalities and differences in users' perceptions of music as an expression of health. Our findings show the existence of clear associations between perceived healthiness and different music features. We provide useful insights into how different musical features impact the perceived healthiness of music, how perceptions of healthiness vary between users, what trends exist between users' impressions, and what influences (or does not influence) a user's perception of healthiness in a melody. Overall, our results indicate validity in presenting health data through personalized music models. The findings can inform the design of behavior management applications on personal and ubiquitous devices.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570346"}, {"primary_key": "1475915", "vector": [], "sparse_vector": [], "title": "Effects of Scene Detection, Scene Prediction, and Maneuver Planning Visualizations on Trust, Situation Awareness, and Cognitive Load in Highly Automated Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The successful introduction of automated vehicles (AVs) depends on the user's acceptance. To gain acceptance, the intended user must trust the technology, which itself relies on an appropriate understanding. Visualizing internal processes could aid in this. For example, the functional hierarchy of autonomous vehicles distinguishes between perception, prediction, and maneuver planning. In each of these stages, visualizations including possible uncertainties (or errors) are possible. Therefore, we report the results of an online study (N=216) comparing visualizations and their combinations on these three levels using a pre-recorded real-world video with visualizations shown on a simulated augmented reality windshield. Effects on trust, cognitive load, situation awareness, and perceived safety were measured. Situation Prediction-related visualizations were perceived as worse than the remaining levels. Based on a negative evaluation of the visualization, the abilities of the AV were also judged worse. In general, the results indicate the presence of overtrust in AVs.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534609"}, {"primary_key": "1475917", "vector": [], "sparse_vector": [], "title": "BayesBeat: Reliable Atrial Fibrillation Detection from Noisy Photoplethysmography Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartwatches or fitness trackers have garnered a lot of popularity as potential health tracking devices due to their affordable and longitudinal monitoring capabilities. To further widen their health tracking capabilities, in recent years researchers have started to look into the possibility of Atrial Fibrillation (AF) detection in real-time leveraging photoplethysmography (PPG) data, an inexpensive sensor widely available in almost all smartwatches. A significant challenge in AF detection from PPG signals comes from the inherent noise in the smartwatch PPG signals. In this paper, we propose a novel deep learning based approach, BayesBeat that leverages the power of Bayesian deep learning to accurately infer AF risks from noisy PPG signals, and at the same time provides an uncertainty estimate of the prediction. Extensive experiments on two publicly available dataset reveal that our proposed method BayesBeat outperforms the existing state-of-the-art methods. Moreover, BayesBeat is substantially more efficient having 40--200X fewer parameters than state-of-the-art baseline approaches making it suitable for deployment in resource constrained wearable devices.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517247"}, {"primary_key": "1475918", "vector": [], "sparse_vector": [], "title": "COCOA: Cross Modality Contrastive Learning for Sensor Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Self-Supervised Learning (SSL) is a new paradigm for learning discriminative representations without labeled data, and has reached comparable or even state-of-the-art results in comparison to supervised counterparts. Contrastive Learning (CL) is one of the most well-known approaches in SSL that attempts to learn general, informative representations of data. CL methods have been mostly developed for applications in computer vision and natural language processing where only a single sensor modality is used. A majority of pervasive computing applications, however, exploit data from a range of different sensor modalities. While existing CL methods are limited to learning from one or two data sources, we propose COCOA (Cross mOdality COntrastive leArning), a self-supervised model that employs a novel objective function to learn quality representations from multisensor data by computing the cross-correlation between different data modalities and minimizing the similarity between irrelevant instances. We evaluate the effectiveness of COCOA against eight recently introduced state-of-the-art self-supervised models, and two supervised baselines across five public datasets. We show that COCOA achieves superior classification performance to all other approaches. Also, COCOA is far more label-efficient than the other baselines including the fully supervised model using only one-tenth of available labeled data.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550316"}, {"primary_key": "1475919", "vector": [], "sparse_vector": [], "title": "Geryon: Edge Assisted Real-time and Robust Object Detection on Drones via mmWave Radar and Camera Fusion.", "authors": ["Kaikai Deng", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Vision-based drone-view object detection suffers from severe performance degradation under adverse conditions (e.g., foggy weather, poor illumination). To remedy this, leveraging complementary mmWave radar has become a trend. However, existing fusion approaches seldom apply to drones due to i) the aggravated sparsity and noise of point clouds from low-cost commodity radars, and ii) explosive sensing data and intensive computations leading to high latency. To address these issues, we design Geryon, an edge assisted object detection system on drones, which utilizes a suit of approaches to fully exploit the complementary advantages of camera and mmWave radar on three levels: (i) a novel multi-frame compositing approach utilizes camera to assist radar to address the aggravated sparsity and noise of radar point clouds; (ii) a saliency area extraction and encoding approach utilizes radar to assist camera to reduce the bandwidth consumption and offloading latency; (iii) a parallel transmission and inference approach with a lightweight box enhancement scheme further reduces the offloading latency while ensuring the edge-side accuracy-latency trade-off by the parallelism and better camera-radar fusion. We implement and evaluate Geryon with four datasets we collect under foggy/rainy/snowy weather and poor illumination conditions, demonstrating its great advantages over other state-of-the-art approaches in terms of both accuracy and latency.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550298"}, {"primary_key": "1475920", "vector": [], "sparse_vector": [], "title": "Plug-and-play Physical Computing with Jacdac.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Gabriele D&apos;Amone", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Physical computing is becoming mainstream. More people than ever---from artists, makers and entrepreneurs to educators and students---are connecting microcontrollers with sensors and actuators to create new interactive devices. However, physical computing still presents many challenges and demands many skills, spanning electronics, low-level protocols, and software---road blocks that reduce participation. While USB has made connecting peripherals to a personal computing device (PC) trivial, USB components are expensive and require a PC to operate. This makes USB impractical for many physical computing scenarios where cost, size and low power operation are often important.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550317"}, {"primary_key": "1475921", "vector": [], "sparse_vector": [], "title": "Reading the Room: Automated, Momentary Assessment of Student Engagement in the Classroom: Are We There Yet?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong Li", "<PERSON>"], "summary": "When in front of a classroom, a skilled teacher can read the room, identifying when students are engaged, frustrated, distracted, etc. In recent years we have seen significant changes in the traditional classroom, with virtual classes becoming a normal learning environment. Reasons for this change are the increased popularity of Massive Open Online Courses (MOOCs) and the disruptions imposed by the ongoing COVID-19 pandemic. However, it is difficult for teachers to read the room in these virtual classrooms, and researchers have begun to look at using sensors to provide feedback to help inform teaching practices. The study presented here sought to ground classroom sensor data in the form of electrodermal activities (EDA) captured using a wrist-worn sensing platform (Empatica E4), with observations about students' emotional engagement in the class. We collected a dataset from eleven students over eight lectures in college-level computer science classes. We trained human annotators who provided ground truth information about student engagement based on in-class observations. Inspired by related work in the field, we implemented an automated data analysis framework, which we used to explore momentary assessments of student engagement in classrooms. Our findings surprised us because we found no significant correlation between the sensor data and our trained observers' data. In this paper, we present our study and framework for automated engagement assessment, and report on our findings that indicate some of the challenges in deploying current technology for real-world, automated momentary assessment of student engagement in the classroom. We offer reflections on our findings and discuss ways forward toward an automated reading the room approach.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550328"}, {"primary_key": "1475922", "vector": [], "sparse_vector": [], "title": "P2-Loc: A Person-2-Person Indoor Localization System in On-Demand Delivery.", "authors": ["<PERSON>", "Dongz<PERSON> Jiang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On-demand delivery is a fast developing business where gig couriers deliver online orders within a short time from merchants to customers. Couriers' accurate indoor locations play an essential role in the business. Most of the existing indoor localization methods cannot be applied in practice due to the high cost or data unavailable on off-the-shelf smartphones. This paper explores a new angle to solve the problem in a relative and infrastructure-free fashion. We design a person-to-person localization system that can (1) detect encounter events via Bluetooth on couriers' smartphones, and (2) infer couriers' relative locations to all the indoor merchants via deep learning on a graph neural network. The system is infrastructure-free, map-free, and compatible for off-the-shelf devices. We deploy the system on a real-world industry platform. The system runs on the smartphones of 4,075 couriers around 79 merchants for a month. The evaluation in a mall area shows that P2-Loc improves the mean average error compared with state-of-art infrastructure-based, report-based, and encounter-based methods. We also use an application analysis based on real-world orders and trajectory data to show that the P2-Loc can save around $40,000 for the platform every day with improved indoor localization results.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517238"}, {"primary_key": "1475924", "vector": [], "sparse_vector": [], "title": "SpARklingPaper: Enhancing Common Pen- And Paper-Based Handwriting Training for Children by Digitally Augmenting Papers Using a Tablet Screen.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Educational apps support learning, but handwriting training is still based on analog pen- and paper. However, training handwriting with apps can negatively affect graphomotor handwriting skills due to the different haptic feedback of the tablet, stylus, or finger compared to pen and paper. With SpARklingPaper, we are the first to combine the genuine haptic feedback of analog pen and paper with the digital support of apps. Our artifact contribution enables children to write with any pen on a standard paper placed on a tablet's screen, augmenting the paper from below, showing animated letters and individual feedback. We conducted two online surveys with overall 29 parents and teachers of elementary school pupils and a user study with 13 children and 13 parents for evaluation. Our results show the importance of the genuine analog haptic feedback combined with the augmentation of SpARklingPaper. It was rated superior compared to our stylus baseline condition regarding pen-handling, writing training-success, motivation, and overall impression. SpARklingPaper can be a blueprint for high-fidelity haptic feedback handwriting training systems.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550337"}, {"primary_key": "1475925", "vector": [], "sparse_vector": [], "title": "A Case Study Investigating a User-Centred and Expert Informed &apos;Companion Guide&apos; for a Complex Sensor-based Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Russell Knights", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON>", "Aisling Ann O&apos;Kane"], "summary": "We present a case study that informs the creation of a 'companion guide' providing transparency to potential non-expert users of a ubiquitous machine learning (ML) platform during the initial onboarding. Ubiquitous platforms (e.g., smart home systems, including smart meters and conversational agents) are increasingly commonplace and increasingly apply complex ML methods. Understanding how non-ML experts comprehend these platforms is important in supporting participants in making an informed choice about if and how they adopt these platforms. To aid this decision-making process, we created a companion guide for a home health platform through an iterative user-centred-design process, seeking additional input from platform experts at all stages of the process to ensure the accuracy of explanations. This user-centred and expert informed design process highlights the need to present the platform's entire ecosystem at an appropriate level for those with differing backgrounds to understand, in order to support informed consent and decision making.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534625"}, {"primary_key": "1475928", "vector": [], "sparse_vector": [], "title": "Recursive Sparse Representation for Identifying Multiple Concurrent Occupants Using Floor Vibration Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a multiple concurrent occupant identification approach through footstep-induced floor vibration sensing. Identification of human occupants is useful in a variety of indoor smart structure scenarios, with applications in building security, space allocation, and healthcare. Existing approaches leverage sensing modalities such as vision, acoustic, RF, and wearables, but are limited due to deployment constraints such as line-of-sight requirements, sensitivity to noise, dense sensor deployment, and requiring each walker to wear/carry a device. To overcome these restrictions, we use footstep-induced structural vibration sensing. Footstep-induced signals contain information about the occupants' unique gait characteristics, and propagate through the structural medium, which enables sparse and passive identification of indoor occupants. The primary research challenge is that multiple-person footstep-induced vibration responses are a mixture of structurally-codependent overlapping individual responses with unknown timing, spectral content, and mixing ratios. As such, it is difficult to determine which part of the signal corresponds to each occupant. We overcome this challenge through a recursive sparse representation approach based on cosine distance that identifies each occupant in a footstep event in the order that their signals are generated, reconstructs their portion of the signal, and removes it from the mixed response. By leveraging sparse representation, our approach can simultaneously identify and separate mixed/overlapping responses, and the use of the cosine distance error function reduces the influence of structural codependency on the multiple walkers' signals. In this way, we isolate and identify each of the multiple occupants' footstep responses. We evaluate our approach by conducting real-world walking experiments with three concurrent walkers and achieve an average F1 score for identifying all persons of 0.89 (1.3x baseline improvement), and with a 10-person \"hybrid\" dataset (simulated combination of single-walker real-world data), we identify 2, 3, and 4 concurrent walkers with a trace-level accuracy of 100%, 93%, and 73%, respectively, and observe as much as a 2.9x error reduction over a naive baseline approach.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517229"}, {"primary_key": "1475929", "vector": [], "sparse_vector": [], "title": "Handwriting Velcro: Endowing AR Glasses with Personalized and Posture-adaptive Text Input Using Flexible Touch Sensor.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Fu"], "summary": "Text input is a desired feature for AR glasses. While there already exist various input modalities (e.g., voice, mid-air gesture), the diverse demands required by different input scenarios can hardly be met by the small number of fixed input postures offered by existing solutions. In this paper, we present Handwriting Velcro, a novel text input solution for AR glasses based on flexible touch sensors. The distinct advantage of our system is that it can easily stick to different body parts, thus endowing AR glasses with posture-adaptive handwriting input. We explored the design space of on-body device positions and identified the best interaction positions for various user postures. To flatten users' learning curves, we adapt our device to the established writing habits of different users by training a 36-character (i.e., A-Z, 0-9) recognition neural network in a human-in-the-loop manner. Such a personalization attempt ultimately achieves a low error rate of 0.005 on average for users with different writing styles. Subjective feedback shows that our solution has a good performance in system practicability and social acceptance. Empirically, we conducted a heuristic study to explore and identify the best interaction Position-Posture Correlation. Experimental results show that our Handwriting Velcro excels similar work [6] and commercial product in both practicality (12.3 WPM) and user-friendliness in different contexts.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569461"}, {"primary_key": "1475930", "vector": [], "sparse_vector": [], "title": "Wi-Learner: Towards One-shot Learning for Cross-Domain Wi-Fi based Gesture Recognition.", "authors": ["<PERSON>", "<PERSON>", "Yicheng Jiang", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xiao<PERSON> Chen"], "summary": "Contactless RF-based sensing techniques are emerging as a viable means for building gesture recognition systems. While promising, existing RF-based gesture solutions have poor generalization ability when targeting new users, environments or device deployment. They also often require multiple pairs of transceivers and a large number of training samples for each target domain. These limitations either lead to poor cross-domain performance or incur a huge labor cost, hindering their practical adoption. This paper introduces Wi-Learner, a novel RF-based sensing solution that relies on just one pair of transceivers but can deliver accurate cross-domain gesture recognition using just one data sample per gesture for a target user, environment or device setup. Wi-Learner achieves this by first capturing the gesture-induced Doppler frequency shift (DFS) from noisy measurements using carefully designed signal processing schemes. It then employs a convolution neural network-based autoencoder to extract the low-dimensional features to be fed into a downstream model for gesture recognition. Wi-Learner introduces a novel meta-learner to \"teach\" the neural network to learn effectively from a small set of data points, allowing the base model to quickly adapt to a new domain using just one training sample. By so doing, we reduce the overhead of training data collection and allow a sensing system to adapt to the change of the deployed environment. We evaluate Wi-Learner by applying it to gesture recognition using the Widar 3.0 dataset. Extensive experiments demonstrate Wi-<PERSON>rner is highly efficient and has a good generalization ability, by delivering an accuracy of 93.2% and 74.2% - 94.9% for in-domain and cross-domain using just one sample per gesture, respectively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550318"}, {"primary_key": "1475931", "vector": [], "sparse_vector": [], "title": "Social Emotional Learning with Conversational Agents: Reviewing Current Designs and Probing Parents&apos; Ideas for Future Ones.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Social emotional skills are foundational competencies upon which children draw throughout their lives. This work investigates current, commercially available experiences for social emotional learning (SEL) through conversational agents (CAs). Specifically, we reviewed 3,767 Skills available in the \"Kids\" category of the Alexa Skills Marketplace and found 42 working Skills with connections to SEL. We found that the most common scenarios these Skills sought to support were: active listening, emotional wellbeing, conversation with other people, and politeness. The interaction patterns used by these Skills distilled into a taxonomy of styles we labeled: The Delegator, The Lecturer, The Bulldozer, and The One-Track Mind. We found that, collectively, these Skills provide shallow experiences and lack contingent feedback. To examine the gap between current offerings and families' needs, we also conducted 26 interviews with parents to probe parents' ideas about CAs supporting children's SEL. Parents see potential for CAs to support children in four concrete ways, including attuning to others, cultivating curiosity, reinforcing politeness, and developing emotional awareness. Despite their optimism about these opportunities, parents expressed skepticism about CAs' impoverished conversational abilities and worry about CAs advancing values and behavioral norms that are at odds with their own.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534622"}, {"primary_key": "1475932", "vector": [], "sparse_vector": [], "title": "Towards Robust Gesture Recognition by Characterizing the Sensing Quality of WiFi Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "WiFi-based gesture recognition emerges in recent years and attracts extensive attention from researchers. Recognizing gestures via WiFi signal is feasible because a human gesture introduces a time series of variations to the received raw signal. The major challenge for building a ubiquitous gesture recognition system is that the mapping between each gesture and the series of signal variations is not unique, exact the same gesture but performed at different locations or with different orientations towards the transceivers generates entirely different gesture signals (variations). To remove the location dependency, prior work proposes to use gesture-level location-independent features to characterize the gesture instead of directly matching the signal variation pattern. We observe that gesture-level features cannot fully remove the location dependency since the signal qualities inside each gesture are different and also depends on the location. Therefore, we divide the signal time series of each gesture into segments according to their qualities and propose customized signal processing techniques to handle them separately. To realize this goal, we characterize signal's sensing quality by building a mathematical model that links the gesture signal with the ambient noise, from which we further derive a unique metric i.e., error of dynamic phase index (EDP-index) to quantitatively describe the sensing quality of signal segments of each gesture. We then propose a quality-oriented signal processing framework that maximizes the contribution of the high-quality signal segments and minimizes the impact of low-quality signal segments to improve the performance of gesture recognition applications. We develop a prototype on COTS WiFi devices. The extensive experimental results demonstrate that our system can recognize gestures with an accuracy of more than 94% on average, and significant improvements compared with state-of-arts.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517241"}, {"primary_key": "1475933", "vector": [], "sparse_vector": [], "title": "Individual and Group-wise Classroom Seating Experience: Effects on Student Engagement in Different Courses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Seating location in the classroom can affect student engagement, attention and academic performance by providing better visibility, improved movement, and participation in discussions. Existing studies typically explore how traditional seating arrangements (e.g. grouped tables or traditional rows) influence students' perceived engagement, without considering group seating behaviours under more flexible seating arrangements. Furthermore, survey-based measures of student engagement are prone to subjectivity and various response bias. Therefore, in this research, we investigate how individual and group-wise classroom seating experiences affect student engagement using wearable physiological sensors. We conducted a field study at a high school and collected survey and wearable data from 23 students in 10 courses over four weeks. We aim to answer the following research questions: 1. How does the seating proximity between students relate to their perceived learning engagement? 2. How do students' group seating behaviours relate to their physiologically-based measures of engagement (i.e. physiological arousal and physiological synchrony)? Experiment results indicate that the individual and group-wise classroom seating experience is associated with perceived student engagement and physiologically-based engagement measured from electrodermal activity. We also find that students who sit close together are more likely to have similar learning engagement and tend to have high physiological synchrony. This research opens up opportunities to explore the implications of flexible seating arrangements and has great potential to maximize student engagement by suggesting intelligent seating choices in the future.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550335"}, {"primary_key": "1475934", "vector": [], "sparse_vector": [], "title": "ThumbAir: In-Air Typing for Head Mounted Displays.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Typing while wearing a standalone Head Mounted Display (HMD)---systems without external input devices or sensors to support text entry---is hard. To address this issue, prior work has used external trackers to monitor finger movements to support in-air typing on virtual keyboards. While performance has been promising, current systems are practically infeasible: finger movements may be visually occluded from inside-out HMD based tracking systems or, otherwise, awkward and uncomfortable to perform. To address these issues, this paper explores an alternative approach. Taking inspiration from the prevalence of thumb-typing on mobile phones, we describe four studies exploring, defining and validating the performance of ThumbAir, an in-air thumb-typing system implemented on a commercial HMD. The first study explores viable target locations, ultimately recommending eight targets sites. The second study collects performance data for taps on pairs of these targets to both inform the design of a target selection procedure and also support a computational design process to select a keyboard layout. The final two studies validate the selected keyboard layout in word repetition and phrase entry tasks, ultimately achieving final WPMs of 27.1 and 13.73. Qualitative data captured in the final study indicate that the discreet movements required to operate ThumbAir, in comparison to the larger scale finger and hand motions used in a baseline design from prior work, lead to reduced levels of perceived exertion and physical demand and are rated as acceptable for use in a wider range of social situations.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569474"}, {"primary_key": "1475935", "vector": [], "sparse_vector": [], "title": "BreathMentor: Acoustic-based Diaphragmatic Breathing Monitor System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Chronic Obstructive Pulmonary Disease (COPD) is currently the third major cause of death--more than three million people died from it in 2019. Given that COPD cannot be cured currently, immediate treatment is crucial. Pulmonary rehabilitation (PR) is widely used to prevent COPD deterioration. Patients are advised to undergo a PR at home to get sufficient treatment in time. Monitoring patients during home rehabilitation can help not only improve patient adherence but also collect data on patients' recovery progress from rehabilitation team's perspective. However, how to track if proper diaphragmatic breathing, an essential part of PR, is taken by a patient has remained challenging. The current monitoring solution still appears obtrusive as it requires the patient to wear two uncomfortable respiration belts. Alternatively, therapists need to monitor the patients remotely through several cameras, which consumes substantial medical resources and causes privacy issues. In this work, we present BreathMentor, a smart speaker based diaphragmatic breathing monitoring system targeting early COPD stages I and II. BreathMentor is both unobtrusive and preventive of privacy invasion, so that it can solve the existing pain points and suits home care. BreathMentor converts the smart speaker into an active sonar system that continuously perceives and analyses the changes in surroundings, thereby detecting the user's respiration rate, deriving the breathing phases, and classifying whether the patient is practising diaphragmatic breathing. BreathMentor formulates breathing monitoring as a Temporal Action Localization task that enables us to detect each breathing cycle and classify its type. Our key insight is that breathing periodicity and phase duration are natural properties to localize and segment the breaths. Our key design to classify the breathing type is a hybrid architecture encompassing signal processing and deep learning techniques. Further, we evaluate the system performance on fifteen healthy subjects who would not breathe abnormally during diaphragmatic breathing under the supervision of therapists. In conclusion, BreathMentor can achieve robust performance for monitoring diaphragmatic breathing in different environments, as demonstrated in the results. The median error rate of respiration detection is 0.2 BPM, and the I/E ratio derivation is accurate with a mean absolute percentage error of less than 5.9% for breathing phase detection, together with a recall of 98.2%, and a precision of 95.5% in detecting diaphragmatic breathing. Above results indicate that BreathMentor can be used to track the patients' adherence and help monitor their breathing capacity.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534595"}, {"primary_key": "1475936", "vector": [], "sparse_vector": [], "title": "Layer by Layer, Patterned Valves Enable Programmable Soft Surfaces.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Programmable surfaces, which can be instructed to alter their shape or texture, may one day serve as a platform for tangible interfaces and adaptive environments. But so far, these structures have been constrained in scale by a challenging fabrication process, as the numerous constituent actuators must be built and assembled individually. We look towards emerging trends in mechanical engineering and consider an alternate framework --- layer-driven design, which enables the production of dynamic, discretely-actuated surfaces at multiple scales. By centering the construction around patterning and stacking, forgoing individual assembly in favor of bulk processes such as photo-etching and laser cutting, we avoid the need for multiple manufacturing steps that are repeated for each of the many actuators that compose the surface. As an instance of this layer-driven model, we build an array of electrostatic valves, and use this composite material (which we refer to as Stoma-Board) to drive four types of pneumatic transducers. We also show how this technique may be readily industrialized, through integration with the highly mature and automated manufacturing processes of modern electronics.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517251"}, {"primary_key": "1475937", "vector": [], "sparse_vector": [], "title": "ILLOC: In-Hall Localization with Standard LoRaWAN Uplink Frames.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "LoRaWAN is a narrowband wireless technology for ubiquitous connectivity. For various applications, it is desirable to localize LoRaWAN devices based on their uplink frames that convey application data. This localization service operates in an unobtrusive manner, in that it requires no special software instrumentation to the LoRaWAN devices. This paper investigates the feasibility of unobtrusive localization for LoRaWAN devices in hall-size indoor spaces like warehouses, airport terminals, sports centers, museum halls, etc. We study the TDoA-based approach, which needs to address two challenges of poor timing performance of LoRaWAN narrowband signal and nanosecond-level clock synchronization among anchors. We propose the ILLOC system featuring two LoRaWAN-specific techniques: (1) the cross-correlation among the differential phase sequences received by two anchors to estimate TDoA and (2) the just-in-time synchronization enabled by a specially deployed LoRaWAN end device providing time reference upon detecting a target device's transmission. In a long tunnel corridor, a 70 x 32 m2 sports hall, and a 110 x 70 m2 indoor plaza with extensive non-line-of-sight propagation paths, ILLOC achieves median localization errors of 6 m (with 2 anchors), 8.36 m (with 6 anchors), and 15.16 m (with 6 anchors and frame fusion), respectively. The achieved accuracy makes ILLOC useful for applications including zone-level asset tracking, misplacement detection, airport trolley management, and cybersecurity enforcement like detecting impersonation attacks launched by remote radios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517245"}, {"primary_key": "1475938", "vector": [], "sparse_vector": [], "title": "WePos: Weak-supervised Indoor Positioning with Unlabeled WiFi for On-demand Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhiqing Hong", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On-demand delivery is an emerging business in recent years where accurate indoor locations of Gig couriers play an important role in the order dispatch and delivery process. To cater to this need, WiFi-based indoor positioning methods have become an alternative method for on-demand delivery thanks to extensive WiFi deployment in the indoor environment. Existing WiFi-based indoor localization and positioning methods are not suitable for large-scale on-demand delivery scenarios due to high costs (e.g., high labor cost to collect fingerprints) and limited coverage due to limited labeled data. In this work, we explore (i) massive crowdsourced WiFi data collecting from wearable or mobile devices of couriers with little extra effort and (ii) natural manual reports data in the delivery process as two opportunities to perform merchant-level indoor positioning in a weak-supervised manner. Specifically, we proposed WePos, an end-to-end weak-supervised-based merchant-level positioning framework, which consists of the following three parts: (i) a Bidirectional Encoder Representations from Transformers (BERT) based pre-training module to learn latent embeddings of WiFi access points, (ii) a contrastive label self-generate module to produce pseudos for WiFi scanning lists by matching similarity embedding clustering results and couriers' reporting behaviors. (iii) a deep neural network-based classifier to fine-tune the whole training process and conduct online merchant-level position inference. To evaluate the performance of our system, we conduct extensive experiments in both a large-scale public crowdsourcing dataset with over 50 GB of WiFi signal records and a real-world WiFi crowdsourced dataset collected from Eleme, (i.e., one of the largest on-demand delivery platforms in China) in four multi-floor malls in Shanghai. Experimental results show that WePos outperforms state-of-the-art baselines in the merchant-level positioning performance, offer up to 91.4% in positioning accuracy.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534574"}, {"primary_key": "1475939", "vector": [], "sparse_vector": [], "title": "Total VREcall: Using Biosignals to Recognize Emotional Autobiographical Memory in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sur<PERSON>", "<PERSON>"], "summary": "Our memories and past experiences contribute to guiding our perception and action of future affective experiences. Virtual Reality (VR) experiences are more vividly memorized and recalled than non-VR ones, but there is little research on how to detect this recall in VR. We investigate the feasibility of recognizing autobiographical memory (AM) recall in VR using physiological cues: skin conductance, heart-rate variability, eye gaze, and pupillary response. We devised a methodology replicating an existing AM Test in VR. We conducted a user study with 20 participants recalling AM using three valence categories cue words: positive, negative, and neutral. We found a significant effect of AM recalls on EDA peak, and eye blink rate, with a generalized recognition accuracy of 77.1% and person dependent accuracy of up to 95.1%. This shows a promising approach for detecting AM recall in VR and we discuss the implications for VR experience design.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534615"}, {"primary_key": "1475941", "vector": [], "sparse_vector": [], "title": "Frisson Waves: Exploring Automatic Detection, Triggering and Sharing of Aesthetic Chills in Music Performances.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Frisson is the feeling and experience of physical reactions such as shivers, tingling skin, and goosebumps. Using entrainment through facilitating interpersonal transmissions of embodied sensations, we present \"Frisson Waves\" with the aim to enhance live music performance experiences. \"Frisson Waves\" is an exploratory real-time system to detect, trigger and share frisson in a wave-like pattern over audience members during music performances. The system consists of a physiological sensing wristband for detecting frisson and a thermo-haptic neckband for inducing frisson. In a controlled environment, we evaluate detection (n=19) and triggering of frisson (n=15). Based on our findings, we conducted an in-the-wild music concert with 48 audience members using our system to share frisson. This paper summarizes a framework for accessing, triggering and sharing frisson. We report our research insights, lessons learned, and limitations of \"Frisson Waves\".", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550324"}, {"primary_key": "1475942", "vector": [], "sparse_vector": [], "title": "Estimating 3D Finger Angle via Fingerprint Image.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Jianjiang Feng", "<PERSON><PERSON>"], "summary": "Touchscreens are the primary input devices for smartphones and tablets. Although widely used, the output of touchscreen controllers is still limited to the two-dimensional position of the contacting finger. Finger angle (or orientation) estimation from touchscreen images has been studied for enriching touch input. However, only pitch and yaw are usually estimated and estimation error is large. One main reason is that touchscreens provide very limited information of finger. With the development of under-screen fingerprint sensing technology, fingerprint images, which contain more information of finger compared with touchscreen images, can be captured when a finger touches the screen. In this paper, we constructed a dataset with fingerprint images and the corresponding ground truth values of finger angle. We contribute with a network architecture and training strategy that harness the strong dependencies among finger angle, finger region, finger type, and fingerprint ridge orientation to produce a top-performing model for finger angle estimation. The experimental results demonstrate the superiority of our method over previous state-of-the-art methods. The mean absolute errors of the three angles are 6.6 degrees for yaw, 7.1 degrees for pitch, and 9.1 degrees for roll, markedly smaller than previously reported errors. Extensive experiments were conducted to examine important factors including image resolution, image size, and finger type. Evaluations on a set of under-screen fingerprints were also performed to explore feasibility in real-world applications. Code and a subset of the data are publicly available.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517243"}, {"primary_key": "1475943", "vector": [], "sparse_vector": [], "title": "C-Cube: Rethinking Distributed Beamforming for Concurrent Charging in Backscatter Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Wen<PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent innovations significantly advance the communication capabilities of backscatter networks, while energy harvesting remains the bottleneck of these backscatters. To tackle this problem, existing studies explore distributed beamforming techniques to beam enhanced energy for a battery-free tag. However, they cannot concurrently transfer high-density power to multiple tags since the system is unable to estimate accurate channel state information (CSI). In this paper, we argue that by deploying a lightweight beamforming helper, we can push the limit of CSI estimation with negligible overhead on the tag. The big gap between the wireless charging threshold and the CSI estimation sensitivity can provide stable estimation result of CSI for the charging system even the signal experiences double fading. On this basis, we propose C-Cube, a concurrent charging system for backscatter networks. C-Cube employs distributed beamforming with the help of CSI to indicate the phase alignment process. To bring it into practice, we design a cold start scheme to activate tags by transmitting a distributed orthogonal frequency (DOF) signal. Moreover, to ensure all tags' CSIs have been successfully received, a spatial-based CSI classifier is explored in C-Cube. We further align the beamforming phase to optimize a novel metric called relative power, instead of the received power metric. The new metric can eliminate the impact of the unknown tag-to-helper channel. We implement our system with USRP/GNURadio platform and customized tags in a real environment. C-Cube can achieve the same coverage as the state-of-the-art and reduce the charge time by 44.1% and 30.2%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570342"}, {"primary_key": "1475944", "vector": [], "sparse_vector": [], "title": "GC-Loc: A Graph Attention Based Framework for Collaborative Indoor Localization Using Infrastructure-free Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Indoor localization techniques play a fundamental role in empowering plenty of indoor location-based services (LBS) and exhibit great social and commercial values. The widespread fingerprint-based indoor localization methods usually suffer from the low feature discriminability with discrete signal fingerprint or high time overhead for continuous signal fingerprint collection. To address this, we introduce the collaboration mechanism and propose a graph attention based collaborative indoor localization framework, termed GC-Loc, which provides another perspective for efficient indoor localization. GC-Loc utilizes multiple discrete signal fingerprints collected by several users as input for collaborative localization. Specifically, we first construct an adaptive graph representation to efficiently model the relationships among the collaborative fingerprints. Then taking state-of-the-art GAT model as basic unit, we design a deep network with the residual structure and the hierarchical attention mechanism to extract and aggregate the features from the constructed graph for collaborative localization. Finally, we further employ ensemble learning mechanism in GC-Loc and devise a location refinement strategy based on model consensus for enhancing the robustness of GC-Loc. We have conducted extensive experiments in three different trial sites, and the experimental results demonstrate the superiority of GC-Loc, outperforming the comparison schemes by a wide margin (reducing the mean localization error by more than 42%).", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569495"}, {"primary_key": "1475945", "vector": [], "sparse_vector": [], "title": "IMar: Multi-user Continuous Action Recognition with WiFi Signals.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Currently, WiFi-based user continuous action counting and recognition is limited to a single person. Being able to continuously analyze and record the different actions of multiple users in a device-free scene is one of the most challenging job to date. In this paper, we present a new WiFi-based multi-user action recognition system, called IMar, which can achieve decomposition of multi-user action information and retain action features as much as possible, i.e., simultaneously recognize and count the continuous and different actions of multiple people. Our main technical route is to design a Dynamic Propagation Delay Threshold Sanitization (DPDTS) algorithm to retain the path information that only passes through the target user body, in order to reduce the multipath effect and make the data as pure as possible, and then model the amplitude relationship of the multi-person action scene. After acquiring the individual data according to the model and tensor decomposition, we propose a Multiplayer Action Amplitude Decomposition and Completion (MAADC) algorithm to obtain more informative data for individual continuous action. Moreover, the single-person data of subcarrier-level obtained by tensor decomposition is extended to the data of stream-level, which brings great convenience to the single-person action recognition. Experimental results show that IMar can work with up to 6 people. The average recognition accuracy and counting accuracy are 78% and 91% respectively in the experimental group of continuous and natural actions by multiple users, and the average recognition accuracy of the experiments in all cases is 83.7%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550311"}, {"primary_key": "1475946", "vector": [], "sparse_vector": [], "title": "Investigating Cross-Modal Approaches for Evaluating Error Acceptability of a Recognition-Based Input Technique.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging input techniques that rely on sensing and recognition can misinterpret a user's intention, resulting in errors and, potentially, a negative user experience. To enhance the development of such input techniques, it is valuable to understand implications of these errors, but they can very costly to simulate. Through two controlled experiments, this work explores various low-cost methods for evaluating error acceptability of freehand mid-air gestural input in virtual reality. Using a gesture-driven game and a drawing application, the first experiment elicited error characteristics through text descriptions, video demonstrations, and a touchscreen-based interactive simulation. The results revealed that video effectively conveyed the dynamics of errors, whereas the interactive modalities effectively reproduced the user experience of effort and frustration. The second experiment contrasts the interactive touchscreen simulation with the target modality - a full VR simulation - and highlights the relative costs and benefits for assessment in an alternative, but still interactive, modality. These findings introduce a spectrum of low-cost methods for evaluating recognition-based errors in VR and a series of characteristics that can be understood in each.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517262"}, {"primary_key": "1475947", "vector": [], "sparse_vector": [], "title": "Bootstrapping Human Activity Recognition Systems for Smart Homes from Scratch.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart Homes have come a long way: From research laboratories in the early days, through (almost) neglect, to their recent revival in real-world environments enabled through the existence of commodity devices and robust, standardized software frameworks. With such availability, human activity recognition (HAR) in smart homes has become attractive for many real-world applications, especially in the domain of Ambient Assisted Living. Yet, getting started with an activity recognition system in specific smart homes, which are highly specialized spaces inhabited by individuals with idiosyncratic behaviors and habits, is a non-trivial endeavor. We present an approach for bootstrapping HAR systems for individual smart homes from scratch. At the beginning of the life cycle of a smart home, our system passively observes activities and derives rich representations for sensor data-action units-which are then aggregated into activity models through motif learning with minimal supervision. The resulting HAR system is then capable of recognizing relevant, most frequent activities in a smart home. We demonstrate the effectiveness of our bootstrapping procedure through experimental evaluations on CASAS datasets that show the practical value of our approach.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550294"}, {"primary_key": "1475948", "vector": [], "sparse_vector": [], "title": "SkillFence: A Systems Approach to Practically Mitigating Voice-Based Confusion Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice assistants are deployed widely and provide useful functionality. However, recent work has shown that commercial systems like Amazon Alexa and Google Home are vulnerable to voice-based confusion attacks that exploit design issues. We propose a systems-oriented defense against this class of attacks and demonstrate its functionality for Amazon Alexa. We ensure that only the skills a user intends execute in response to voice commands. Our key insight is that we can interpret a user's intentions by analyzing their activity on counterpart systems of the web and smartphones. For example, the Lyft ride-sharing Alexa skill has an Android app and a website. Our work shows how information from counterpart apps can help reduce dis-ambiguities in the skill invocation process. We build SkilIFence, a browser extension that existing voice assistant users can install to ensure that only legitimate skills run in response to their commands. Using real user data from MTurk (N = 116) and experimental trials involving synthetic and organic speech, we show that SkillFence provides a balance between usability and security by securing 90.83% of skills that a user will need with a False acceptance rate of 19.83%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517232"}, {"primary_key": "1475949", "vector": [], "sparse_vector": [], "title": "StructureSense: Inferring Constructive Assembly Structures from User Behaviors.", "authors": ["<PERSON><PERSON><PERSON>", "Key<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent advancements in object-tracking technologies can turn mundane constructive assemblies into Tangible User Interfaces (TUI) media. Users rely on instructions or their own creativity to build both permanent and temporary structures out of such objects. However, most existing object-tracking technologies focus on tracking structures as monoliths, making it impossible to infer and track the user's assembly process and the resulting structures. Technologies that can track the assembly process often rely on specially fabricated assemblies, limiting the types of objects and structures they can track. Here, we present StructureSense, a tracking system based on passive UHF-RFID sensing that infers constructive assembly structures from object motion. We illustrated StructureSense in two use cases (as guided instructions and authoring tool) on two different constructive sets (wooden lamp and Jumbo Blocks), and evaluated system performance and usability. Our results showed the feasibility of using StructureSense to track mundane constructive assembly structures.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570343"}, {"primary_key": "1475950", "vector": [], "sparse_vector": [], "title": "WristAcoustic: Through-Wrist Acoustic Response Based Authentication for Smartwatches.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Shin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "PIN and pattern lock are difficult to accurately enter on small watch screens, and are vulnerable against guessing attacks. To address these problems, this paper proposes a novel implicit biometric scheme based on through-wrist acoustic responses. A cue signal is played on a surface transducer mounted on the dorsal wrist and the acoustic response recorded by a contact microphone on the volar wrist. We build classifiers using these recordings for each of three simple hand poses (relax, fist and open), and use an ensemble approach to make final authentication decisions. In an initial single session study (N=25), we achieve an Equal Error Rate (EER) of 0.01%, substantially outperforming prior on-wrist biometric solutions. A subsequent five recall-session study (N=20) shows reduced performance with 5.06% EER. We attribute this to increased variability in how participants perform hand poses over time. However, after retraining classifiers performance improved substantially, ultimately achieving 0.79% EER. We observed most variability with the relax pose. Consequently, we achieve the most reliable multi-session performance by combining the fist and open poses: 0.51% EER. Further studies elaborate on these basic results. A usability evaluation reveals users experience low workload as well as reporting high SUS scores and fluctuating levels of perceived exertion: moderate during initial enrollment dropping to slight during authentication. A final study examining performance in various poses and in the presence of noise demonstrates the system is robust to such disturbances and likely to work well in wide range of real-world contexts.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569473"}, {"primary_key": "1475951", "vector": [], "sparse_vector": [], "title": "Dwell Selection with ML-based Intent Prediction Using Only Gaze Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We developed a dwell selection system with ML-based prediction of a user's intent to select. Because a user perceives visual information through the eyes, precise prediction of a user's intent will be essential to the establishment of gaze-based interaction. Our system first detects a dwell to roughly screen the user's intent to select and then predicts the intent by using an ML-based prediction model. We created the intent prediction model from the results of an experiment with five different gaze-only tasks representing everyday situations. The intent prediction model resulted in an overall area under the curve (AUC) of the receiver operator characteristic curve of 0.903. Moreover, it could perform independently of the user (AUC=0.898) and the eye-tracker (AUC=0.880). In a performance evaluation experiment with real interactive situations, our dwell selection method had both higher qualitative and quantitative performance than previously proposed dwell selection methods.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550301"}, {"primary_key": "1475952", "vector": [], "sparse_vector": [], "title": "ColloSSL: Collaborative Self-Supervised Learning for Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A major bottleneck in training robust Human-Activity Recognition models (HAR) is the need for large-scale labeled sensor datasets. Because labeling large amounts of sensor data is an expensive task, unsupervised and semi-supervised learning techniques have emerged that can learn good features from the data without requiring any labels. In this paper, we extend this line of research and present a novel technique called Collaborative Self-Supervised Learning (ColloSSL) which leverages unlabeled data collected from multiple devices worn by a user to learn high-quality features of the data. A key insight that underpins the design of ColloSSL is that unlabeled sensor datasets simultaneously captured by multiple devices can be viewed as natural transformations of each other, and leveraged to generate a supervisory signal for representation learning. We present three technical innovations to extend conventional self-supervised learning algorithms to a multi-device setting: a Device Selection approach which selects positive and negative devices to enable contrastive learning, a Contrastive Sampling algorithm which samples positive and negative examples in a multi-device setting, and a loss function called Multi-view Contrastive Loss which extends standard contrastive loss to a multi-device setting. Our experimental results on three multi-device datasets show that ColloSSL outperforms both fully-supervised and semi-supervised learning techniques in majority of the experiment settings, resulting in an absolute increase of upto 7.9% in F1 score compared to the best performing baselines. We also show that ColloSSL outperforms the fully-supervised methods in a low-data regime, by just using one-tenth of the available labeled data in the best case.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517246"}, {"primary_key": "1475954", "vector": [], "sparse_vector": [], "title": "EarCommand: &quot;Hearing&quot; Your Silent Speech Commands In Ear.", "authors": ["Yincheng Jin", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Intelligent speech interfaces have been developing vastly to support the growing demands for convenient control and interaction with wearable/earable and portable devices. To avoid privacy leakage during speech interactions and strengthen the resistance to ambient noise, silent speech interfaces have been widely explored to enable people's interaction with mobile/wearable devices without audible sounds. However, most existing silent speech solutions require either restricted background illuminations or hand involvement to hold device or perform gestures. In this study, we propose a novel earphone-based, hand-free silent speech interaction approach, named EarCommand. Our technique discovers the relationship between the deformation of the ear canal and the movements of the articulator and takes advantage of this link to recognize different silent speech commands. Our system can achieve a WER (word error rate) of 10.02% for word-level recognition and 12.33% for sentence-level recognition, when tested in human subjects with 32 word-level commands and 25 sentence-level commands, which indicates the effectiveness of inferring silent speech commands. Moreover, EarCommand shows high reliability and robustness in a variety of configuration settings and environmental conditions. It is anticipated that EarCommand can serve as an efficient, intelligent speech interface for hand-free operation, which could significantly improve the quality and convenience of interactions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534613"}, {"primary_key": "1475957", "vector": [], "sparse_vector": [], "title": "Ask the Users: A Case Study of Leveraging User-Centered Design for Designing Just-in-Time Adaptive Interventions (JITAIs).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "June <PERSON><PERSON>", "<PERSON>"], "summary": "Just-in-Time Adaptive Interventions (JITAIs) are envisioned to harness rich data on users' contexts. However, many JITAIs fall short in leveraging the value of the data while sculpting the interventions. Investigating the literature reveals a lack of user-centered design (UCD) methods in designing JITAIs. Our case study of applying a UCD process revealed that even without deploying a JITAI, UCD could uncover user interactions that inform critical design decisions of a JITAI's components. We reflect on our experiences engaging in a user-centered JITAI design process and urge the broader human-computer interaction (HCI) community to devise concrete design guidelines for JITAIs. We take the first step toward that goal by proposing a checklist of key design considerations for future JITAIs. Together, this case study contributes insights on applying UCD to the design of all components of a JITAI, which can help capture users' needs and provide actionable interventions.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534612"}, {"primary_key": "1475958", "vector": [], "sparse_vector": [], "title": "EarlyScreen: Multi-scale Instance Fusion for Predicting Neural Activation and Psychopathology in Preschool Children.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Emotion dysregulation in early childhood is known to be associated with a higher risk of several psychopathological conditions, such as ADHD and mood and anxiety disorders. In developmental neuroscience research, emotion dysregulation is characterized by low neural activation in the prefrontal cortex during frustration. In this work, we report on an exploratory study with 94 participants aged 3.5 to 5 years, investigating whether behavioral measures automatically extracted from facial videos can predict frustration-related neural activation and differentiate between low- and high-risk individuals. We propose a novel multi-scale instance fusion framework to develop EarlyScreen - a set of classifiers trained on behavioral markers during emotion regulation. Our model successfully predicts activation levels in the prefrontal cortex with an area under the receiver operating characteristic (ROC) curve of 0.85, which is on par with widely-used clinical assessment tools. Further, we classify clinical and non-clinical subjects based on their psychopathological risk with an area under the ROC curve of 0.80. Our model's predictions are consistent with standardized psychometric assessment scales, supporting its applicability as a screening procedure for emotion regulation-related psychopathological disorders. To the best of our knowledge, EarlyScreen is the first work to use automatically extracted behavioral features to characterize both neural activity and the diagnostic status of emotion regulation-related disorders in young children. We present insights from mental health professionals supporting the utility of EarlyScreen and discuss considerations for its subsequent deployment.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534583"}, {"primary_key": "1475959", "vector": [], "sparse_vector": [], "title": "Augmented Adversarial Learning for Human Activity Recognition with Partial Sensor Sets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human activity recognition (HAR) plays an important role in a wide range of applications, such as health monitoring and gaming. Inertial sensors attached to body segments constitute a critical sensing system for HAR. Diverse inertial sensor datasets for HAR have been released with the intention of attracting collective efforts and saving the data collection burden. However, these datasets are heterogeneous in terms of subjects and sensor positions. The coupling of these two factors makes it hard to generalize the model to a new application scenario, where there are unseen subjects and new sensor position combinations. In this paper, we design a framework to combine heterogeneous data to learn a general representation for HAR, so that it can work for new applications. We propose an Augmented Adversarial Learning framework for HAR (AALH) to learn generalizable representations to deal with diverse combinations of sensor positions and subject discrepancies. We train an adversarial neural network to map various sensor sets' data into a common latent representation space which is domain-invariant and class-discriminative. We enrich the latent representation space by a hybrid missing strategy and complement each subject domain with a multi-domain mixup method, and they significantly improve model generalization. Experiment results on two HAR datasets demonstrate that the proposed method significantly outperforms previous methods on unseen subjects and new sensor position combinations.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550285"}, {"primary_key": "1475960", "vector": [], "sparse_vector": [], "title": "Contactless Monitoring of PPG Using Radar.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we propose VitaNet, a radio frequency based contactless approach that accurately estimates the PPG signal using radar for stationary participants. The main insight behind VitaNet is that the changes in the blood volume that manifest in the PPG waveform are correlated to the physical movements of the heart, which the radar can capture. To estimate the PPG waveform, VitaNet uses a self-attention architecture to identify the most informative reflections in an unsupervised manner, and then uses an encoder decoder network to transform the radar phase profile to the PPG sequence. We have trained and extensively evaluated VitaNet on a large dataset obtained from 25 participants over 179 full nights. Our evaluations show that VitaNet accurately estimates the PPG waveform and its derivatives with high accuracy, significantly improves the heart rate and heart rate variability estimates from the prior works, and also accurately estimates several useful PPG features. We have released the codes of VitaNet as well as the trained models and the dataset used in this paper.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550330"}, {"primary_key": "1475961", "vector": [], "sparse_vector": [], "title": "Battery-free MakeCode: Accessible Programming for Intermittent Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hands-on computing has emerged as an exciting and accessible way to learn about computing and engineering in the physical world for students and makers of all ages. Current end-to-end approaches like Microsoft MakeCode require tethered or battery-powered devices like a micro:bit, limiting usefulness and applicability, as well as abdicating responsibility for teaching sustainable practices. Unfortunately, energy harvesting computing devices are usually only programmable by experts and require significant supporting toolchains and knowledge across multiple engineering and computing disciplines to work effectively. This paper bridges the gap between sustainable computing efforts, the maker movement, and novice-focused programming environments with MakeCode-Iceberg, a set of compiler extensions to Microsoft's open-source MakeCode project. The extensions automatically and invisibly transform user code in any language supported (Blocks, JavaScript, Python)into a version that can safely and correctly execute across intermittent power failures caused by unreliable energy harvesting. Determining where, when, and what to save in a checkpoint on limited energy, time, and hardware budget is challenging. We leverage the unique intermediate representation of the MakeCode source-to-source compiler to design and deploy various checkpointing techniques. Our approach allows us to provide, for the first time, a fully web-based and toolchain-free environment to program intermittent computing devices, making battery-free operation accessible to all. We demonstrate new use cases with multiple energy harvesters, peripherals, and application domains: including a Smart Terrarium, Step Counter, and Combination Lock. MakeCode-<PERSON><PERSON> provides sustainable hands-on computing opportunities to a broad audience of makers and learners, democratizing access to energy harvesting and battery-free embedded systems.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517236"}, {"primary_key": "1475962", "vector": [], "sparse_vector": [], "title": "On Privacy Risks of Watching YouTube over Cellular Networks with Carrier Aggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "One's core values, personality, and social status may be reflected in the watch history of online video streaming services such as YouTube. Unfortunately, several prior research work demonstrates that man-in-the-middle or malware-assisted attackers can accurately infer the titles of encrypted streaming videos by exploiting the inherent correlation between the encoded video contents and the traffic rate changes. In this paper, we present a novel video-inference attack called Moba that further exacerbates the problem by only requiring the adversary to simply eavesdrop the broadcast messages of a primary cell of a targeted user's cellular phone. Our attack utilizes a side channel in modern cellular networks that leaks the number of actively transmitting cells for each user. We show that this seemingly harmless system information leakage can be used to achieve practical video-inference attacks. To design effective video-inference attacks, we augment the coarse-grained side-channel measurements with precise timing information and estimate the traffic bursts of encrypted video contents. The Moba attack considers an adversary-chosen set of suspect YouTube videos, from which a targeted user may watch some videos during the attack. We confirm the feasibility of <PERSON><PERSON> in identifying the exact YouTube video title (if it is from the suspect set) via our over-the-air experiments conducted in LTE-Advanced networks in two countries. Moba can be effective in verifying whether a targeted user watches any of the suspect videos or not; e.g., precision of 0.98 is achieved after observing six-minutes of a single video play. When further allowed to observe multiple video plays, <PERSON><PERSON> adversary is able to identify whether the targeted user frequently watches the suspect videos with a probability close to one and a near-zero false positive rate. Finally, we present a simple padding-based countermeasure that significantly reduces the attack effectiveness without sacrificing any cellular radio resources.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517261"}, {"primary_key": "1475964", "vector": [], "sparse_vector": [], "title": "Don&apos;t &quot;Weight&quot; to Board: Augmenting Vision-based Passenger Weight Prediction via Viscoelastic Mat.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Airlines overestimate the weight of their passengers by simply assigning a constant weight for everyone, causing each plane to burn more fuel than needed to carry the extra weight. Accurately estimating the passenger weights is a difficult problem for airlines as naively weighing all passengers with scales is impractical in already busy airports. Hence, we propose CamScale, a novel vision-based weight inference system that is augmented by an off-the-shelf viscoelastic mat (e.g., memory foam mat). CamScale takes the video feed of the mat placed on the floor as the passengers walk over it. It utilizes the inherent strain, or deformation of the mat due to the passengers' footsteps to infer their weights. CamScale is advantageous because it does not incur additional weighing time, while being cost-effective and accurate. We evaluate CamScale through real-world experiments by deploying RGB and infrared cameras and inviting 36 participants to walk a total of more than 17,000 steps over viscoelastic mats, equivalent to walking approximately 13.1 km. We demonstrate that CamScale is able to accurately estimate an individual's weight with an average error of 1.12 kg.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569465"}, {"primary_key": "1475965", "vector": [], "sparse_vector": [], "title": "AEROKEY: Using Ambient Electromagnetic Radiation for Secure and Usable Wireless Device Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless connectivity is becoming common in increasingly diverse personal devices, enabling various interoperation- and Internet-based applications and services. More and more interconnected devices are simultaneously operated by a single user with short-lived connections, making usable device authentication methods imperative to ensure both high security and seamless user experience. Unfortunately, current authentication methods that heavily require human involvement, in addition to form factor and mobility constraints, make this balance hard to achieve, often forcing users to choose between security and convenience. In this work, we present a novel over-the-air device authentication scheme named AEROKEY that achieves both high security and high usability. With virtually no hardware overhead, AEROKEY leverages ubiquitously observable ambient electromagnetic radiation to autonomously generate spatiotemporally unique secret that can be derived only by devices that are closely located to each other. Devices can make use of this unique secret to form the basis of a symmetric key, making the authentication procedure more practical, secure and usable with no active human involvement. We propose and implement essential techniques to overcome challenges in realizing AEROKEY on low-cost microcontroller units, such as poor time synchronization, lack of precision analog front-end, and inconsistent sampling rates. Our real-world experiments demonstrate reliable authentication as well as its robustness against various realistic adversaries with low equal-error rates of 3.4% or less and usable authentication time of as low as 24 s.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517254"}, {"primary_key": "1475966", "vector": [], "sparse_vector": [], "title": "Reliable Digital Forensics in the Air: Exploring an RF-based Drone Identification System.", "authors": ["<PERSON><PERSON><PERSON>", "Baicheng Chen", "Xingyu Chen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the drone becomes widespread in numerous crucial applications with many powerful functionalities (e.g., reconnaissance and mechanical trigger), there are increasing cases related to misused drones for unethical even criminal activities. Therefore, it is of paramount importance to identify these malicious drones and track their origins using digital forensics. Traditional drone identification techniques for forensics (e.g., RF communication, ID landmarks using a camera, etc.) require high compliance of drones. However, malicious drones will not cooperate or even spoof these identification techniques. Therefore, we present an exploration for a reliable and passive identification approach based on unique hardware traits in drones directly (e.g., analogous to the fingerprint and iris in humans) for forensics purposes. Specifically, we investigate and model the behavior of the parasitic electronic elements under RF interrogation, a particular passive parasitic response modulated by an electronic system on drones, which is distinctive and unlikely to counterfeit. Based on this theory, we design and implement DroneTrace, an end-to-end reliable and passive identification system toward digital drone forensics. DroneTrace comprises a cost-effective millimeter-wave (mmWave) probe, a software framework to extract and process parasitic responses, and a customized deep neural network (DNN)-based algorithm to analyze and identify drones. We evaluate the performance of DroneTrace with 36 commodity drones. Results show that DroneTrace can identify drones with the accuracy of over 99% and an equal error rate (EER) of 0.009, under a 0.1-second sensing time budget. Moreover, we test the reliability, robustness, and performance variation under a set of real-world circumstances, where DroneTrace maintains accuracy of over 98%. DroneTrace is resilient to various attacks and maintains functionality. At its best, DroneTrace has the capacity to identify individual drones at the scale of 104 with less than 5% error.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534598"}, {"primary_key": "1475967", "vector": [], "sparse_vector": [], "title": "NeuralGait: Assessing Brain Health Using Your Smartphone.", "authors": ["Huining Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Brain health attracts more recent attention as the population ages. Smartphone-based gait sensing and analysis can help identify the risks of brain diseases in daily life for prevention. Existing gait analysis approaches mainly hand-craft temporal gait features or developing CNN-based feature extractors, but they are either prone to lose some inconspicuous pathological information or are only dedicated to a single brain disease screening. We discover that the relationship between gait segments can be used as a principle and generic indicator to quantify multiple pathological patterns. In this paper, we propose NeuralGait, a pervasive smartphone-cloud system that passively captures and analyzes principle gait segments relationship for brain health assessment. On the smartphone end, inertial gait data are collected while putting the smartphone in the pants pocket. We then craft local temporal-frequent gait domain features and develop a self-attention-based gait segment relationship encoder. Afterward, the domain features and relation features are fed to a scalable RiskNet in the cloud for brain health assessment. We also design a pathological hot update protocol to efficiently add new brain diseases in the RiskNet. NeuralGait is practical as it provides brain health assessment with no burden in daily life. In the experiment, we recruit 988 healthy people and 417 patients with a single or combination of PD, TBI, and stroke, and evaluate the brain health assessment using a set of specifically designed metrics including global accuracy, exact accuracy, sensitivity, and false alarm rate. We also demonstrate the generalization (e.g., analysis of feature effectiveness and model efficiency) and inclusiveness of NeuralGait.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569476"}, {"primary_key": "1475968", "vector": [], "sparse_vector": [], "title": "Evaluating Three Touch Gestures for Moving Objects across Folded Screens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qingzhou Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Foldable screens are gaining popularity on mobile platforms as it features novel design schemes for view management and screen extension. However, challenges for performing touch input arises when it is folded in various angles and held in various ways. This paper is among the first to systematically evaluate how touch gestures perform under the influence of various fold angles and holding postures. Three gestures for moving objects on touch screens, namely Direct Drag, Hold &amp; Tap, and Throw &amp; Catch, were adopted from previous works and compared in a controlled experiment, where five fold angles and portrait vs landscape modes were varied. Results provide an in-depth understanding of how orientation, fold angle, target distance and direction affects the performance of each technique. Overall Direct Drag was the most accurate but highly inefficient and tiring. Hold &amp; Tap was the most efficient technique with comparable accuracy, except being much more error-prone in Landscape than Portrait. Throw &amp; Catch had a more balanced trade-off between efficiency and accuracy.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550309"}, {"primary_key": "1475970", "vector": [], "sparse_vector": [], "title": "Enhancing Revisitation in Touchscreen Reading for Visually Impaired People with Semantic Navigation Design.", "authors": ["<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Revisitation, the process of non-linearly returning to previously visited regions, is an important task in academic reading. However, listening to content on mobile phones via a screen reader fails to support eyes-free revisiting due to its linear audio stream, ineffective text organization, and inaccessible interaction. To enhance the efficiency and experience of eyes-free revisiting, we identified visually impaired people's behaviors and difficulties during text revisiting through a survey (N=37) and an observation study (N=12). We proposed a series of design guidelines targeting high precision, high flexibility, and low workload in interaction, and iteratively designed and developed a reading prototype application. Our implementation supports dynamic text structure and is supplemented by both linear and non-linear layered text navigation. The evaluation results (N=8) show that compared to existing methods, our prototype improves the clarity of text understanding and fluency of revisiting with reduced workload.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550290"}, {"primary_key": "1475971", "vector": [], "sparse_vector": [], "title": "Modeling the Noticeability of User-Avatar Movement Inconsistency for Sense of Body Ownership Intervention.", "authors": ["<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yukang Yan", "<PERSON><PERSON><PERSON>"], "summary": "An avatar mirroring the user's movement is commonly adopted in Virtual Reality(VR). Maintaining the user-avatar movement consistency provides the user a sense of body ownership and thus an immersive experience. However, breaking this consistency can enable new interaction functionalities, such as pseudo haptic feedback [45] or input augmentation [37, 59], at the expense of immersion. We propose to quantify the probability of users noticing the movement inconsistency while the inconsistency amplitude is being enlarged, which aims to guide the intervention of the users' sense of body ownership in VR. We applied angular offsets to the avatar's shoulder and elbow joints and recorded whether the user identified the inconsistency through a series of three user studies and built a statistical model based on the results. Results show that the noticeability of movement inconsistency increases roughly quadratically with the enlargement of offsets and the offsets at two joints negatively affect the probability distributions of each other. Leveraging the model, we implemented a technique that amplifies the user's arm movements with unnoticeable offsets and then evaluated implementations with different parameters(offset strength, offset distribution). Results show that the technique with medium-level and balanced-distributed offsets achieves the best overall performance. Finally, we demonstrated our model's extendability in interventions in the sense of body ownership with three VR applications including stroke rehabilitation, action game and widget arrangement.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534590"}, {"primary_key": "1475972", "vector": [], "sparse_vector": [], "title": "LASense: Pushing the Limits of Fine-grained Activity Sensing Using Acoustic Signals.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic signals have been widely adopted in sensing fine-grained human activities, including respiration monitoring, finger tracking, eye blink detection, etc. One major challenge for acoustic sensing is the extremely limited sensing range, which becomes even more severe when sensing fine-grained activities. Different from the prior efforts that adopt multiple microphones and/or advanced deep learning techniques for long sensing range, we propose a system called LASense, which can significantly increase the sensing range for fine-grained human activities using a single pair of speaker and microphone. To achieve this, LASense introduces a virtual transceiver idea that purely leverages delicate signal processing techniques in software. To demonstrate the effectiveness of LASense, we apply the proposed approach to three fine-grained human activities, i.e., respiration, finger tapping and eye blink. For respiration monitoring, we significantly increase the sensing range from the state-of-the-art 2 m to 6 m. For finer-grained finger tapping and eye blink detection, we increase the state-of-the-art sensing range by 150% and 80%, respectively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517253"}, {"primary_key": "1475973", "vector": [], "sparse_vector": [], "title": "DiverSense: Maximizing Wi-Fi Sensing Range Leveraging Signal Diversity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON> Zhang"], "summary": "The ubiquity of Wi-Fi infrastructure has facilitated the development of a range of Wi-Fi based sensing applications. Wi-Fi sensing relies on weak signal reflections from the human target and thus only supports a limited sensing range, which significantly hinders the real-world deployment of the proposed sensing systems. To extend the sensing range, traditional algorithms focus on suppressing the noise introduced by the imperfect Wi-Fi hardware. This paper picks a different direction and proposes to enhance the quality of the sensing signal by fully exploiting the signal diversity provided by the Wi-Fi hardware. We propose DiverSense, a system that combines sensing signal received from all subcarriers and all antennas in the array, to fully utilize the spatial and frequency diversity. To guarantee the diversity gain after signal combining, we also propose a time-diversity based signal alignment algorithm to align the phase of the multiple received sensing signals. We implement the proposed methods in a respiration monitoring system using commodity Wi-Fi devices and evaluate the performance in diverse environments. Extensive experimental results demonstrate that DiverSense is able to accurately monitor the human respiration even when the sensing signal is under noise floor, and therefore boosts sensing range to 40 meters, which is a 3x improvement over the current state-of-the-art. DiverSense also works robustly under NLoS scenarios, e.g., DiverSense is able to accurately monitor respiration even when the human and the Wi-Fi transceivers are separated by two concrete walls with wooden doors.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3536393"}, {"primary_key": "1475974", "vector": [], "sparse_vector": [], "title": "EarIO: A Low-power Acoustic Sensing Earable for Continuously Tracking Detailed Facial Movements.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents EarIO, an AI-powered acoustic sensing technology that allows an earable (e.g., earphone) to continuously track facial expressions using two pairs of microphone and speaker (one on each side), which are widely available in commodity earphones. It emits acoustic signals from a speaker on an earable towards the face. Depending on facial expressions, the muscles, tissues, and skin around the ear would deform differently, resulting in unique echo profiles in the reflected signals captured by an on-device microphone. These received acoustic signals are processed and learned by a customized deep learning pipeline to continuously infer the full facial expressions represented by 52 parameters captured using a TruthDepth camera. Compared to similar technologies, it has significantly lower power consumption, as it can sample at 86 Hz with a power signature of 154 mW. A user study with 16 participants under three different scenarios, showed that EarIO can reliably estimate the detailed facial movements when the participants were sitting, walking or after remounting the device. Based on the encouraging results, we further discuss the potential opportunities and challenges on applying EarIO on future ear-mounted wearables.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534621"}, {"primary_key": "1475975", "vector": [], "sparse_vector": [], "title": "DRG-Keyboard: Enabling Subtle Gesture Typing on the Fingertip with Dual IMU Rings.", "authors": ["<PERSON>", "Chi Hsia", "<PERSON>", "Yukang Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present DRG-Keyboard, a gesture keyboard enabled by dual IMU rings, allowing the user to swipe the thumb on the index fingertip to perform word gesture typing as if typing on a miniature QWERTY keyboard. With dual IMUs attached to the user's thumb and index finger, DRG-Keyboard can 1) measure the relative attitude while mapping it to the 2D fingertip coordinates and 2) detect the thumb's touch-down and touch-up events combining the relative attitude data and the synchronous frequency domain data, based on which a fingertip gesture keyboard can be implemented. To understand users typing behavior on the index fingertip with DRG-Keyboard, we collected and analyzed user data in two typing manners. Based on the statistics of the gesture data, we enhanced the elastic matching algorithm with rigid pruning and distance measurement transform. The user study showed DRG-Keyboard achieved an input speed of 12.9 WPM (68.3% of their gesture typing speed on the smartphone) for all participants. The appending study also demonstrated the superiority of DRG-Keyboard for better form factors and wider usage scenarios. To sum up, DRG-Keyboard not only achieves good text entry speed merely on a tiny fingertip input surface, but is also well accepted by the participants for the input subtleness, accuracy, good haptic feedback, and availability.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569463"}, {"primary_key": "1475976", "vector": [], "sparse_vector": [], "title": "AccMyrinx: Speech Synthesis with Non-Acoustic Sensor.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiaokai Yan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The built-in loudspeakers of mobile devices (e.g., smartphones, smartwatches, and tablets) play significant roles in human-machine interaction, such as playing music, making phone calls, and enabling voice-based interaction. Prior studies have pointed out that it is feasible to eavesdrop on the speaker via motion sensors, but whether it is possible to synthesize speech from non-acoustic signals with sub-Nyquist sampling frequency has not been studied. In this paper, we present an end-to-end model to reconstruct the acoustic waveforms that are playing on the loudspeaker through the vibration captured by the built-in accelerometer. Specifically, we present an end-to-end speech synthesis framework dubbed AccMyrinx to eavesdrop on the speaker using the built-in low-resolution accelerometer of mobile devices. AccMyrinx takes advantage of the coexistence of an accelerometer with the loudspeaker on the same motherboard and compromises the loudspeaker by the solid-borne vibrations captured by the accelerometer. Low-resolution vibration signals are fed to a wavelet-based MelGAN to generate intelligible acoustic waveforms. We conducted extensive experiments on a large-scale dataset created based on audio clips downloaded from Voice of America (VOA). The experimental results show that AccMyrinx is capable of reconstructing intelligible acoustic signals that are playing on the loudspeaker with a smoothed word error rate (SWER) of 42.67%. The quality of synthesized speeches could be severely affected by several factors including gender, speech rate, and volume.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550338"}, {"primary_key": "1475977", "vector": [], "sparse_vector": [], "title": "Wheels Know Why You Travel: Predicting Trip Purpose via a Dual-Attention Graph Embedding Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Su<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Trip purpose - i.e., why people travel - is an important yet challenging research topic in travel behavior analysis. Generally, the key to this problem is understanding the activity semantics from trip contexts. However, most existing methods rely on passengers' sensitive information - e.g., daily travel log or home address from surveys - to achieve accurate results, and could thus be hardly applied in real-life scenarios. In this paper, we aim to predict the passenger's trip purpose in the scenarios of door-to-door ride services (e.g., taxi trips) by only using the vehicle's GPS trajectory on roads, for which \"wheels\" is used as a metaphor. Specifically, we propose a novel dual-attention graph embedding model based on the vehicle's trajectory and public POI check-in data. Firstly, both data are aggregated to augment the activity semantics of trip contexts, including the spatiotemporal context and POI contexts at the origin and destination, which are important clues. Based on that, graph attention networks and soft-attention are employed to model the dependency of different contexts on the trip purpose, so as to obtain the trip's comprehensive activity semantics for the final prediction. Extensive experiments are conducted based on the large-scale labeled datasets in Beijing. The prediction results show a considerable improvement compared to state-of-the-arts. A case study demonstrates the feasibility of our study.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517239"}, {"primary_key": "1475978", "vector": [], "sparse_vector": [], "title": "Differentiable Neural Network Pruning to Enable Smart Applications on Microcontrollers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Wearable, embedded, and IoT devices are a centrepiece of many ubiquitous computing applications, such as fitness tracking, health monitoring, home security and voice assistants. By gathering user data through a variety of sensors and leveraging machine learning (ML), applications can adapt their behaviour: in other words, devices become \"smart\". Such devices are typically powered by microcontroller units (MCUs). As MCUs continue to improve, smart devices become capable of performing a non-trivial amount of sensing and data processing, including machine learning inference, which results in a greater degree of user data privacy and autonomy, compared to offloading the execution of ML models to another device. Advanced predictive capabilities across many tasks make neural networks an attractive ML model for ubiquitous computing applications; however, on-device inference on MCUs remains extremely challenging. Orders of magnitude less storage, memory and computational ability, compared to what is typically required to execute neural networks, impose strict structural constraints on the network architecture and call for specialist model compression methodology. In this work, we present a differentiable structured pruning method for convolutional neural networks, which integrates a model's MCU-specific resource usage and parameter importance feedback to obtain highly compressed yet accurate models. Compared to related network pruning work, compressed models are more accurate due to better use of MCU resource budget, and compared to MCU specialist work, compressed models are produced faster. The user only needs to specify the amount of available computational resources and the pruning algorithm will automatically compress the network during training to satisfy them. We evaluate our methodology using benchmark image and audio classification tasks and find that it (a) improves key resource usage of neural networks up to 80x; (b) has little to no overhead or even improves model training time; (c) produces compressed models with matching or improved resource usage up to 1.4x in less time compared to prior MCU-specific model compression methods.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569468"}, {"primary_key": "1475979", "vector": [], "sparse_vector": [], "title": "BodyTrak: Inferring Full-body Poses from Body Silhouettes Using a Miniature Camera on a Wristband.", "authors": ["Hyunchul Lim", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present BodyTrak, an intelligent sensing technology that can estimate full body poses on a wristband. It only requires one miniature RGB camera to capture the body silhouettes, which are learned by a customized deep learning model to estimate the 3D positions of 14 joints on arms, legs, torso, and head. We conducted a user study with 9 participants in which each participant performed 12 daily activities such as walking, sitting, or exercising, in varying scenarios (wearing different clothes, outdoors/indoors) with a different number of camera settings on the wrist. The results show that our system can infer the full body pose (3D positions of 14 joints) with an average error of 6.9 cm using only one miniature RGB camera (11.5mm x 9.5mm) on the wrist pointing towards the body. Based on the results, we disscuss the possible application, challenges, and limitations to deploy our system in real-world scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3552312"}, {"primary_key": "1475980", "vector": [], "sparse_vector": [], "title": "mTransSee: Enabling Environment-Independent mmWave Sensing Based Gesture Recognition via Transfer Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kaiyuan Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Gesture recognition using millimeter-wave radios facilitates natural human-computer interactions, but existing works require a consistent environment, i.e., the neural networks for recognition are trained and tested for the same users and at some fixed positions. In this case, their performance will decrease rapidly when they enter into a new environment. To make the model applicable in different environments, a straightforward approach is to collect and re-train the model for the gesture samples on every possible position upon each new user. However, it may ask the users to spend unacceptable time to accomplish such adaptation, which makes it difficult to be widely used in practice. In this paper, we first collect an abundant mmWave gesture dataset containing 59,280 samples as a benchmark to investigate the impact of the environment changes quantitatively. Then we propose a novel transfer-learning approach called mTransSee, which can serve the gestures in practice using pre-learned experience by least adaptation, i.e., retraining using only 8 samples per gesture for the same accuracy. mTransSee reduces dozens of workloads for the environment adaptation. We implement mTransSee on a commodity mmWave sensor and make a user study to compare the advance of mTransSee over the state-of-the-art solution in terms of user experience during adaptation.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517231"}, {"primary_key": "1475981", "vector": [], "sparse_vector": [], "title": "PrinType: Text Entry via Fingerprint Recognition.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jianjiang Feng", "<PERSON><PERSON>"], "summary": "We present PrinType, a fingerprint recognition based typing technique for virtual reality. Different regions of fingers covered by friction ridges are assigned to different keys (i.e. letters, numbers, punctuation, or functions). Once the thumb-worn fingerprint sensor touches a finger, the contact region (and its key) is recognized by matching the current image with registered templates. Using only a small sensor, PrinType turns each segment of all fingers into a touchable key. We designed keyboard layouts corresponding to three interaction sub-spaces: whole finger keyboard, fingertip keyboard, and single-handed keyboard. A 12-person user study was conducted to evaluate the performance of different strategies. Our user evaluation showed that participants achieved an average of 29.56, 32.38, and 34.22 WPM with 0.79%, 0.20%, and 0.21% not corrected error rate in the three strategies. In addition, we provided a detailed analysis of various micro metrics to further understand user performance and technical characteristics. Overall, PrinType is favored by users for its usability, efficiency, and novelty.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569491"}, {"primary_key": "1475982", "vector": [], "sparse_vector": [], "title": "CamRadar: Hidden Camera Detection Leveraging Amplitude-modulated Sensor Images Embedded in Electromagnetic Emanations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhongjie Ba", "Li Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hidden cameras in sensitive locations have become an increasing threat to personal privacy all over the world. Because the camera is small and camouflaged, it is difficult to detect the presence of the camera with naked eyes. Existing works on this subject have either only covered using wireless transmission to detect cameras, or using other methods which are cumbersome in practical use. In this paper, we introduce a new direction that leverages the unintentional electromagnetic (EM) emanations of the camera to detect it. We first find that the digital output of the camera's image sensor will be amplitude-modulated to the EM emanations of the camera's clock. Thus, changes in the scope of the camera will directly cause changes in the camera's EM emanations, which constitutes a unique characteristic for a hidden camera. Based on this, we propose a novel camera detection system named CamRadar, which can filter out potential camera EM emanations from numerous EM signals quickly and achieve accurate hidden camera detection. Benefitting from the camera's EM emanations, CamRadar will not be limited by the camera transmission types or the detection angle. Our extensive real-world experiments using CamRadar and 19 hidden cameras show that CamRadar achieves a fast detection (in 16.75s) with a detection rate of 93.23% as well as a low false positive rate of 3.95%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569505"}, {"primary_key": "1475983", "vector": [], "sparse_vector": [], "title": "Enabling Contact-free Acoustic Sensing under Device Motion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have witnessed increasing attention from both academia and industry on contact-free acoustic sensing. Due to the pervasiveness of audio devices and fine granularity of acoustic sensing, it has been applied in numerous fields, including human-computer interaction and contact-free health sensing. Though promising, the limited working range hinders the wide adoption of acoustic sensing in real life. To break the sensing range limit, we propose to deploy the acoustic device on a moving platform (i.e., a robot) to support applications that require larger coverage and continuous sensing. In this paper, we propose SonicBot, a system that enables contact-free acoustic sensing under device motion. We propose a sequence of signal processing schemes to eliminate the impact of device motion and then obtain clean target movement information that is previously overwhelmed by device movement. We implement SonicBot using commercial audio devices and conduct extensive experiments to evaluate the performance of the proposed system. Experiment results show that our system can achieve a median error of 1.11 cm and 1.31 mm for coarse-grained and fine-grained tracking, respectively. To showcase the applicability of our proposed system in real-world settings, we perform two field studies, including coarse-grained gesture sensing and fine-grained respiration monitoring when the acoustic device moves along with a robot.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550329"}, {"primary_key": "1475985", "vector": [], "sparse_vector": [], "title": "UQRCom: Underwater Wireless Communication Based on QR Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While communication in the air has been a norm with the pervasiveness of WiFi and LTE infrastructure, underwater communication still faces a lot of challenges. Even nowadays, the main communication method for divers in underwater environment is hand gesture. There are multiple issues associated with gesture-based communication including limited amount of information and ambiguity. On the other hand, traditional RF-based wireless communication technologies which have achieved great success in the air can hardly work in underwater environment due to the extremely severe attenuation. In this paper, we propose UQRCom, an underwater wireless communication system designed for divers. We design a UQR code which stems from QR code and address the unique challenges in underwater environment such as color cast, contrast reduction and light interfere. With both real-world experiments and simulation, we show that the proposed system can achieve robust real-time communication in underwater environment. For UQR codes with a size of 19.8 cm x 19.8 cm, the communication distance can be 11.2 m and the achieved data rate (6.9 kbps ~ 13.6 kbps) is high enough for voice communication between divers.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3571588"}, {"primary_key": "1475987", "vector": [], "sparse_vector": [], "title": "Understanding People&apos;s Perceptions of Approaches to Semi-Automated Dietary Monitoring.", "authors": ["<PERSON>", "<PERSON>az", "<PERSON>"], "summary": "The respective benefits and drawbacks of manual food journaling and automated dietary monitoring (ADM) suggest the value of semi-automated journaling systems combining the approaches. However, the current understanding of how people anticipate strategies for implementing semi-automated food journaling systems is limited. We therefore conduct a speculative survey study with 600 responses, examining how people anticipate approaches to automatic capture and prompting for details. Participants feel the location and detection capability of ADM sensors influences anticipated physical, social, and privacy burdens. People more positively anticipate prompts which contain information relevant to their journaling goals, help them recall what they ate, and are quick to respond to. Our work suggests a tradeoff between ADM systems' detection performance and anticipated acceptability, with sensors on facial areas having higher performance but lower acceptability than sensors in other areas and more usable prompting methods like those containing specific foods being more challenging to produce than manual reminders. We suggest opportunities to improve higher-acceptability, lower-accuracy ADM sensors, select approaches based on individual and practitioner journaling needs, and better describe capabilities to potential users.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550288"}, {"primary_key": "1475988", "vector": [], "sparse_vector": [], "title": "Semantic-Discriminative Mixup for Generalizable Sensor-based Cross-domain Activity Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chunyu Hu", "<PERSON><PERSON>"], "summary": "It is expensive and time-consuming to collect sufficient labeled data to build human activity recognition (HAR) models. Training on existing data often makes the model biased towards the distribution of the training data, thus the model might perform terribly on test data with different distributions. Although existing efforts on transfer learning and domain adaptation try to solve the above problem, they still need access to unlabeled data on the target domain, which may not be possible in real scenarios. Few works pay attention to training a model that can generalize well to unseen target domains for HAR. In this paper, we propose a novel method called Semantic-Discriminative Mixup (SDMix) for generalizable cross-domain HAR. Firstly, we introduce semantic-aware Mixup that considers the activity semantic ranges to overcome the semantic inconsistency brought by domain differences. Secondly, we introduce the large margin loss to enhance the discrimination of Mixup to prevent misclassification brought by noisy virtual labels. Comprehensive generalization experiments on five public datasets demonstrate that our SDMix substantially outperforms the state-of-the-art approaches with 6% average accuracy improvement on cross-person, cross-dataset, and cross-position HAR.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534589"}, {"primary_key": "1475989", "vector": [], "sparse_vector": [], "title": "Non-intrusive Anomaly Detection of Industrial Robot Operations by Exploiting Nonlinear Effect.", "authors": ["Zhiqing Luo", "<PERSON><PERSON>uan Yan", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the development of Internet of Robotic Things concept, low-cost radio technologies open up many opportunities to facilitate the monitoring system of industrial robots, while the openness of wireless medium exposes robots to replay and man-in-the-middle attackers, who send pre-recorded movement data to mislead the system. Recent advances advocate the use of high-resolution sensors to monitor robot operations, which however require invasive retrofit to the robots. To overcome this predicament, we present RobotScatter, a non-intrusive system that exploits the nonlinear effect of RF circuits to fuse the propagation of backscatter tags attached to the robot to defend against active attacks. Specifically, the backscatter propagation interacted by the tags significantly depends on various movement operations, which can be captured with the nonlinearity at the receiver to uniquely determine its identity and the spatial movement trajectory. RobotScatter then profiles the robot movements to verify whether the received movement information matches the backscatter signatures, and thus detects the threat. We implement RobotScatter on two common robotic platforms, Universal Robot and iRobot Create, with over 1,500 operation cycles. The experiment results show that RobotScatter detects up to 94% of anomalies against small movement deviations of 10mm/s in velocity, and 2.6cm in distance.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569477"}, {"primary_key": "1475993", "vector": [], "sparse_vector": [], "title": "HaptiDrag: A Device with the Ability to Generate Varying Levels of Drag (Friction) Effects on Real Surfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We presently rely on mechanical approaches to leverage drag (friction) effects for digital interaction as haptic feedback over real surfaces. Unfortunately, due to their mechanical nature, such methods are inconvenient, difficult to scale, and include object deployment issues. Accordingly, we present HaptiDrag, a thin (1 mm) and lightweight (2 gram) device that can reliably produce various intensities of on-surface drag effects through electroadhesion phenomenon. We first performed design evaluation to determine minimal size (5 cm x 5 cm) of HaptiDrag to enable drag effect. Further, with reference to eight distinct surfaces, we present technical performance of 2 sizes of HaptiDrag in real environment conditions. Later, we conducted two user studies; the first to discover absolute detection threshold friction spots of varying intensities common to all surfaces under test and the second to validate the absolute detection threshold points for noticeability with all sizes of HaptiDrag. Finally, we demonstrate device's utility in different scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550310"}, {"primary_key": "1475994", "vector": [], "sparse_vector": [], "title": "SAMoSA: Sensing Activities with Motion and Subsampled Audio.", "authors": ["Vimal Mollyn", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite advances in audio- and motion-based human activity recognition (HAR) systems, a practical, power-efficient, and privacy-sensitive activity recognition system has remained elusive. State-of-the-art activity recognition systems often require power-hungry and privacy-invasive audio data. This is especially challenging for resource-constrained wearables, such as smartwatches. To counter the need for an always-on audio-based activity classification system, we first make use of power and compute-optimized IMUs sampled at 50 Hz to act as a trigger for detecting activity events. Once detected, we use a multimodal deep learning model that augments the motion data with audio data captured on a smartwatch. We subsample this audio to rates ≤ 1 kHz, rendering spoken content unintelligible, while also reducing power consumption on mobile devices. Our multimodal deep learning model achieves a recognition accuracy of 92.2% across 26 daily activities in four indoor environments. Our findings show that subsampling audio from 16 kHz down to 1 kHz, in concert with motion data, does not result in a significant drop in inference accuracy. We also analyze the speech content intelligibility and power requirements of audio sampled at less than 1 kHz and demonstrate that our proposed approach can improve the practicality of human activity recognition systems.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550284"}, {"primary_key": "1475995", "vector": [], "sparse_vector": [], "title": "A Review of Personal Informatics Research for People with Motor Disabilities.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Personal informatics (PI) has become an area of significant research over the past decade, maturing into a sub-field that seeks to support people from many backgrounds and life contexts in collecting and finding value in their personal data. PI research includes a focus on people with chronic conditions as a monolithic group, but currently fails to distinguish the needs of people with motor disabilities (MD). To understand how current PI literature addresses those needs, we conducted a mapping review on PI publications engaged with people with MD. We report results from 50 publications identified in the ACM DL, Pubmed, JMIR, SCOPUS, and IEEE Xplore. Our analysis shows significant incompatibilities between the needs of individuals with MD and the ways that PI literature supports them. We also found inconsistencies in the ways that disability levels are reported, that PI literature for MD excludes non-health-related data domains, and an insufficient focus on PI tools' accessibility and usability for some MD users. In contrast with <PERSON><PERSON><PERSON> et al.'s [36] recent PI review, behavior change and habit awareness were the most common motivation in these publications. Finally, many of the reviewed articles reported involvement by caregivers, trainers, healthcare providers, and researchers across the PI stages. In addition to these insights, we provide recommendations for designing PI technology through a user-centric lens that will broaden the scope of PI and include people regardless of their motor abilities.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534614"}, {"primary_key": "1475999", "vector": [], "sparse_vector": [], "title": "Sensurfaces: A Novel Approach for Embedded Touch Sensing on Everyday Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ubiquitous touch sensing surfaces are largely influenced by touchscreens' look and feel and fail to express the physical richness of existing surrounding materials. We introduce Sensurfaces, a plug-and-play electronic module that allows to rapidly experiment with touch-sensitive surfaces while preserving the original appearance of materials. Sensurfaces is composed of plug-and-play modules that can be connected together to expand the size and number of materials composing a sensitive surface. The combination of Sensurfaces modules allows the creation of small or large multi-material sensitive surfaces that can detect multi-touch but also body proximity, pose, pass, or even human steps. In this paper, we present the design and implementation of Sensurfaces. We propose a design space describing the factors of Sensurfaces interfaces. Then, through a series of technical evaluations, we demonstrate the capabilities of our system. Finally, we report on two workshops validating the usability of our system.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534616"}, {"primary_key": "1476000", "vector": [], "sparse_vector": [], "title": "Contextual Biases in Microinteraction Ecological Momentary Assessment (μEMA) Non-response.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ecological momentary assessment (EMA) is used to gather in-situ self-report on behaviors using mobile devices. Microinteraction EMA (μEMA), is a type of EMA where each survey is only one single question that can be answered with a glanceable microinteraction on a smartwatch. Prior work shows that even when μEMA interrupts far more frequently than smartphone-EMA, μEMA yields higher response rates with lower burden. We examined the contextual biases associated with non-response of μEMA prompts on a smartwatch. Based on prior work on EMA non-response and smartwatch use, we identified 10 potential contextual biases from three categories: temporal (time of the day, parts of waking day, day of the week, and days in study), device use (screen state, charging status, battery mode, and phone usage), and activity (wrist motion and location). We used data from a longitudinal study where 131 participants (Mean age 22.9 years, SD = 3.0) responded to μEMA surveys on a smartwatch for at least six months. Using mixed-effects logistic regression, we found that all temporal, activity/mobility, and device use variables had a statistically significant (p<0.001) association with momentary μEMA non-response. We discuss the implication of these results for future use of context-aware μEMA methodology.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517259"}, {"primary_key": "1476002", "vector": [], "sparse_vector": [], "title": "ODSearch: Fast and Resource Efficient On-device Natural Language Search for Fitness Trackers&apos; Data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile and wearable technologies have promised significant changes to the healthcare industry. Although cutting-edge communication and cloud-based technologies have allowed for these upgrades, their implementation and popularization in low-income countries have been challenging. We propose ODSearch, an On-device Search framework equipped with a natural language interface for mobile and wearable devices. To implement search, ODSearch employs compression and Bloom filter, it provides near real-time search query responses without network dependency. In particular, the Bloom filter reduces the temporal scope of the search and compression reduces the size of the data to be searched. Our experiments were conducted on a mobile phone and smartwatch. We compared ODSearch with current state-of-the-art search mechanisms, and it outperformed them on average by 53 times in execution time, 26 times in energy usage, and 2.3% in memory utilization.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569488"}, {"primary_key": "1476003", "vector": [], "sparse_vector": [], "title": "GoPose: 3D Human Pose Estimation Using WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents GoPose, a 3D skeleton-based human pose estimation system that uses WiFi devices at home. Our system leverages the WiFi signals reflected off the human body for 3D pose estimation. In contrast to prior systems that need specialized hardware or dedicated sensors, our system does not require a user to wear or carry any sensors and can reuse the WiFi devices that already exist in a home environment for mass adoption. To realize such a system, we leverage the 2D AoA spectrum of the signals reflected from the human body and the deep learning techniques. In particular, the 2D AoA spectrum is proposed to locate different parts of the human body as well as to enable environment-independent pose estimation. Deep learning is incorporated to model the complex relationship between the 2D AoA spectrums and the 3D skeletons of the human body for pose tracking. Our evaluation results show GoPose achieves around 4.7cm of accuracy under various scenarios including tracking unseen activities and under NLoS scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534605"}, {"primary_key": "1476004", "vector": [], "sparse_vector": [], "title": "Investigating In-Situ Personal Health Data Queries on Smartwatches.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartwatches enable not only the continuous collection of but also ubiquitous access to personal health data. However, exploring this data in-situ on a smartwatch is often reserved for singular and generic metrics, without the capacity for further insight. To address our limited knowledge surrounding smartwatch data exploration needs, we collect and characterize desired personal health data queries from smartwatch users. We conducted a week-long study (N = 18), providing participants with an application for recording responses that contain their query and current activity related information, throughout their daily lives. From the responses, we curated a dataset of 205 natural language queries. Upon analysis, we highlight a new preemptive and proactive data insight category, an activity-based lens for data exploration, and see the desired use of a smartwatch for data exploration throughout daily life. To aid in future research and the development of smartwatch health applications, we contribute the dataset and discuss implications of our findings.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569481"}, {"primary_key": "1476005", "vector": [], "sparse_vector": [], "title": "Sensing with Earables: A Systematic Literature Review and Taxonomy of Phenomena.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Earables have emerged as a unique platform for ubiquitous computing by augmenting ear-worn devices with state-of-the-art sensing. This new platform has spurred a wealth of new research exploring what can be detected on a wearable, small form factor. As a sensing platform, the ears are less susceptible to motion artifacts and are located in close proximity to a number of important anatomical structures including the brain, blood vessels, and facial muscles which reveal a wealth of information. They can be easily reached by the hands and the ear canal itself is affected by mouth, face, and head movements. We have conducted a systematic literature review of 271 earable publications from the ACM and IEEE libraries. These were synthesized into an open-ended taxonomy of 47 different phenomena that can be sensed in, on, or around the ear. Through analysis, we identify 13 fundamental phenomena from which all other phenomena can be derived, and discuss the different sensors and sensing principles used to detect them. We comprehensively review the phenomena in four main areas of (i) physiological monitoring and health, (ii) movement and activity, (iii) interaction, and (iv) authentication and identification. This breadth highlights the potential that earables have to offer as a ubiquitous, general-purpose platform.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550314"}, {"primary_key": "1476006", "vector": [], "sparse_vector": [], "title": "TinyOdom: Hardware-Aware Efficient Neural Inertial Navigation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep inertial sequence learning has shown promising odometric resolution over model-based approaches for trajectory estimation in GPS-denied environments. However, existing neural inertial dead-reckoning frameworks are not suitable for real-time deployment on ultra-resource-constrained (URC) devices due to substantial memory, power, and compute bounds. Current deep inertial odometry techniques also suffer from gravity pollution, high-frequency inertial disturbances, varying sensor orientation, heading rate singularity, and failure in altitude estimation. In this paper, we introduce TinyOdom, a framework for training and deploying neural inertial models on URC hardware. TinyOdom exploits hardware and quantization-aware Bayesian neural architecture search (NAS) and a temporal convolutional network (TCN) backbone to train lightweight models targetted towards URC devices. In addition, we propose a magnetometer, physics, and velocity-centric sequence learning formulation robust to preceding inertial perturbations. We also expand 2D sequence learning to 3D using a model-free barometric g-h filter robust to inertial and environmental variations. We evaluate TinyOdom for a wide spectrum of inertial odometry applications and target hardware against competing methods. Specifically, we consider four applications: pedestrian, animal, aerial, and underwater vehicle dead-reckoning. Across different applications, TinyOdom reduces the size of neural inertial models by 31× to 134× with 2.5m to 12m error in 60 seconds, enabling the direct deployment of models on URC devices while still maintaining or exceeding the localization resolution over the state-of-the-art. The proposed barometric filter tracks altitude within ±0.1m and is robust to inertial disturbances and ambient dynamics. Finally, our ablation study shows that the introduced magnetometer, physics, and velocity-centric sequence learning formulation significantly improve localization performance even with notably lightweight models.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534594"}, {"primary_key": "1476007", "vector": [], "sparse_vector": [], "title": "Auritus: An Open-Source Optimization Toolkit for Training and Development of Human Movement Models and Filters Using Earables.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Pei", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart ear-worn devices (called earables) are being equipped with various onboard sensors and algorithms, transforming earphones from simple audio transducers to multi-modal interfaces making rich inferences about human motion and vital signals. However, developing sensory applications using earables is currently quite cumbersome with several barriers in the way. First, time-series data from earable sensors incorporate information about physical phenomena in complex settings, requiring machine-learning (ML) models learned from large-scale labeled data. This is challenging in the context of earables because large-scale open-source datasets are missing. Secondly, the small size and compute constraints of earable devices make on-device integration of many existing algorithms for tasks such as human activity and head-pose estimation difficult. To address these challenges, we introduce Auritus, an extendable and open-source optimization toolkit designed to enhance and replicate earable applications. Auritus serves two primary functions. Firstly, Auritus handles data collection, pre-processing, and labeling tasks for creating customized earable datasets using graphical tools. The system includes an open-source dataset with 2.43 million inertial samples related to head and full-body movements, consisting of 34 head poses and 9 activities from 45 volunteers. Secondly, Auritus provides a tightly-integrated hardware-in-the-loop (HIL) optimizer and TinyML interface to develop lightweight and real-time machine-learning (ML) models for activity detection and filters for head-pose tracking. To validate the utlity of <PERSON>rit<PERSON>, we showcase three sample applications, namely fall detection, spatial audio rendering, and augmented reality (AR) interfacing. Auritus recognizes activities with 91% leave 1-out test accuracy (98% test accuracy) using real-time models as small as 6-13 kB. Our models are 98-740x smaller and 3-6% more accurate over the state-of-the-art. We also estimate head pose with absolute errors as low as 5 degrees using 20kB filters, achieving up to 1.6x precision improvement over existing techniques. We make the entire system open-source so that researchers and developers can contribute to any layer of the system or rapidly prototype their applications using our dataset and algorithms.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534586"}, {"primary_key": "1476008", "vector": [], "sparse_vector": [], "title": "CSI: DeSpy: Enabling Effortless Spy Camera Detection via Passive Sensing of User Activities and Bitrate Variations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, the spy cameras spotted in private rental places have raised immense privacy concerns. The existing solutions for detecting them require additional support from synchronous external sensing or stimulus hardware such as on/off LED circuits, which require extra obligations from the user. For example, a user needs to carry a smartphone and laboriously perform preset motions (e.g., jumping, waving, and preplanned walking pattern) for synchronous sensing of acceleration signals. These requirements cause considerable discomfort to the user and limit the practicability of prevalent solutions. To cope with this, we propose CSI:DeSpy, an efficient and painless method by leveraging video bitrate fluctuations of the WiFi camera and the passively obtained Channel States Information (CSI) from user motion. CSI:DeSpy includes a self-adaptive feature that makes it robust to detect motion efficiently in multipath-rich environments. We implemented CSI:DeSpy on the Android platform and assessed its performance in diverse real-life scenarios, namely; (1) its reliability with the intensities of physical activities in diverse multipath-rich environments, (2) its practicability with activities of daily living, (3) its unobtrusiveness with passive sensing, and (4) its robustness to different network loads. CSI:DeSpy attained average detection rates of 96.6%, 96.2%, 98.5%, and 93.6% respectively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534593"}, {"primary_key": "1476010", "vector": [], "sparse_vector": [], "title": "PACT: Scalable, Long-Range Communication for Monitoring and Tracking Systems Using Battery-less Tags.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The food and drug industry is facing the need to monitor the quality and safety of their products. This has made them turn to low-cost solutions that can enable smart sensing and tracking without adding much overhead. One such popular low-power solution is backscatter-based sensing and communication system. While it offers the promise of battery-less tags, it does so at the cost of a reduced communication range. In this work, we propose PACT - a scalable communication system that leverages the knowledge asymmetry in the network to improve the communication range of the tags. Borrowing from the backscatter principles, we design custom PACT Tags that are battery-less but use an active radio to extend the communication range beyond standard passive tags. They operate using the energy harvested from the PACT Source. A wide-band Reader is used to receive multiple Tag responses concurrently and upload them to a cloud server, enabling real-time monitoring and tracking at a longer range. We identify and address the challenges in the practical design of battery-less PACT Tags using an active radio and prototype them using off-the-shelf components. We show experimentally that our Tag consumes only 23μJ energy, which is harvested from an excitation Source that is up to 24 meters away from the Tag. We show that in outdoor deployments, the responses from an estimated 520 Tags can be received by a Reader concurrently while being 400 meters away from the Tags.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569471"}, {"primary_key": "1476012", "vector": [], "sparse_vector": [], "title": "PackquID: In-packet Liquid Identification Using RF Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yubo Yan", "Xiangyang Li"], "summary": "There are many scenarios where the liquid is occluded by other items (e.g. books in a packet), in which existing RF-based liquid identification methods are generally not suitable. Moreover, status methods are not applicable when the height of the liquid to be tested changes. This paper proposes PackquID, an RF-based in-packet liquid identification system, which can identify liquid without prior knowledge. In dealing with the obstruction of other items and the unknown container, we utilize a dual-antenna model and craft a relative frequency response factor, exploring the diversity of the permittivity in the frequency domain. In tackling the variable liquid height, we extend our model to 3D scope by analyzing the electric field distribution and solving the height effect via spatial-differential model. With 500 pages of printer paper obscured, PackquID can identify 9 common liquids, including Coca-Cola and Pepsi, with an accuracy of over 86% for 4 different packets (canvas bag, paper bag, backpack, and box) and 4 different containers. Nevertheless, PackquID can still identify liquids with an accuracy rate of over 87%, even when the liquid height changes from 4 cm to 12 cm.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569469"}, {"primary_key": "1476013", "vector": [], "sparse_vector": [], "title": "Psychophysiological Arousal in Young Children Who Stutter: An Interpretable AI Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The presented first-of-its-kind study effectively identifies and visualizes the second-by-second pattern differences in the physiological arousal of preschool-age children who do stutter (CWS) and who do not stutter (CWNS) while speaking perceptually fluently in two challenging conditions: speaking in stressful situations and narration. The first condition may affect children's speech due to high arousal; the latter introduces linguistic, cognitive, and communicative demands on speakers. We collected physiological parameters data from 70 children in the two target conditions. First, we adopt a novel modality-wise multiple-instance-learning (MI-MIL) approach to classify CWS vs. CWNS in different conditions effectively. The evaluation of this classifier addresses four critical research questions that align with state-of-the-art speech science studies' interests. Later, we leverage SHAP classifier interpretations to visualize the salient, fine-grain, and temporal physiological parameters unique to CWS at the population/group-level and personalized-level. While group-level identification of distinct patterns would enhance our understanding of stuttering etiology and development, the personalized-level identification would enable remote, continuous, and real-time assessment of stuttering children's physiological arousal, which may lead to personalized, just-in-time interventions, resulting in an improvement in speech fluency. The presented MI-MIL approach is novel, generalizable to different domains, and real-time executable. Finally, comprehensive evaluations are done on multiple datasets, presented framework, and several baselines that identified notable insights on CWSs' physiological arousal during speech production.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550326"}, {"primary_key": "1476014", "vector": [], "sparse_vector": [], "title": "Electromagnetic Fingerprinting of Memory Heartbeats: System and Applications.", "authors": ["<PERSON>", "<PERSON>", "Guangyu Sun", "Jing<PERSON> Chen"], "summary": "This paper presents MemScope, a system that fingerprints devices via electromagnetic sensing of their memory heartbeats, i.e., the clock signal that synchronizes memory and memory controller. MemScope leverages the enhanced resolution and security of memory heartbeat fingerprint, which has enriched spectral features thanks to the spread spectrum generation of memory clock, and cannot be concealed as long as the device accesses its memory during computing. MemScope employs signal processing algorithms that allow it to hear the memory heartbeats of devices from a distance, in the presence of noise, and in crowded environments where multiple devices coexist. It then fingerprints memory heartbeats using machine learning tools. Measurements on a set of 65 devices over a month validate the robustness of fingerprint against time variation, and show a high precision and recall. We then use the neural network to build a detector to defend against possible replay attacks. Finally, we further demonstrate the effectiveness of MemScope in two application scenarios, (i) detecting wireless identity spoofing and (ii) identifying and localizing unauthorized hidden cameras.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550295"}, {"primary_key": "1476015", "vector": [], "sparse_vector": [], "title": "ClenchClick: Hands-Free Target Selection Method Leveraging Teeth-Clench for Augmented Reality.", "authors": ["<PERSON><PERSON> Shen", "Yukang Yan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose to explore teeth-clenching-based target selection in Augmented Reality (AR), as the subtlety in the interaction can be beneficial to applications occupying the user's hand or that are sensitive to social norms. To support the investigation, we implemented an EMG-based teeth-clenching detection system (ClenchClick), where we adopted customized thresholds for different users. We first explored and compared the potential interaction design leveraging head movements and teeth clenching in combination. We finalized the interaction to take the form of a Point-and-Click manner with clenches as the confirmation mechanism. We evaluated the taskload and performance of ClenchClick by comparing it with two baseline methods in target selection tasks. Results showed that ClenchClick outperformed hand gestures in workload, physical load, accuracy and speed, and outperformed dwell in work load and temporal load. Lastly, through user studies, we demonstrated the advantage of ClenchClick in real-world tasks, including efficient and accurate hands-free target selection, natural and unobtrusive interaction in public, and robust head gesture input.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550327"}, {"primary_key": "1476016", "vector": [], "sparse_vector": [], "title": "Integrating Handcrafted Features with Deep Representations for Smartphone Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent research demonstrates the potential of touch dynamics as a usable and privacy-preserving scheme for smartphone authentication. Most existing approaches rely on handcrafted features since deep models may be vulnerable to behavioral uncertainty due to the lack of consistent semantic information. Toward this end, we propose an approach to integrating handcrafted features into two phases of the deep learning process. On one hand, we present three fine-grained behavior representations by encoding semantic handcrafted features into the raw touch actions. On the other hand, we devise a deep Feature Regularization Net (FRN) architecture to combine the complementary information in both handcrafted and deep features. FRN involves handcrafted features as regularization to guide the learning process of deep features and selectively fuses these two feature types through a feature re-weighting mechanism. Experimental findings demonstrate that FRN outperforms the existing handcrafted or deep features even with smaller training and template sets. The framework also works for SOTA deep models and further boosts the accuracy. Results show that our approach is more reliable to alleviate behavioral variability and is competitively robust to statistical attacks compared with the most effective handcrafted features, suggesting a promising mechanism to improve the effectiveness and usability of behavioral authentication for multi-touch enabled mobile devices.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517332"}, {"primary_key": "1476017", "vector": [], "sparse_vector": [], "title": "MuteIt: Jaw Motion Based Unvoiced Command Recognition Using Earable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present MuteIt, an ear-worn system for recognizing unvoiced human commands. MuteIt presents an intuitive alternative to voice-based interactions that can be unreliable in noisy environments, disruptive to those around us, and compromise our privacy. We propose a twin-IMU set up to track the user's jaw motion and cancel motion artifacts caused by head and body movements. MuteIt processes jaw motion during word articulation to break each word signal into its constituent syllables, and further each syllable into phonemes (vowels, visemes, and plosives). Recognizing unvoiced commands by only tracking jaw motion is challenging. As a secondary articulator, jaw motion is not distinctive enough for unvoiced speech recognition. MuteIt combines IMU data with the anatomy of jaw movement as well as principles from linguistics, to model the task of word recognition as an estimation problem. Rather than employing machine learning to train a word classifier, we reconstruct each word as a sequence of phonemes using a bi-directional particle filter, enabling the system to be easily scaled to a large set of words. We validate MuteIt for 20 subjects with diverse speech accents to recognize 100 common command words. MuteIt achieves a mean word recognition accuracy of 94.8% in noise-free conditions. When compared with common voice assistants, MuteIt outperforms them in noisy acoustic environments, achieving higher than 90% recognition accuracy. Even in the presence of motion artifacts, such as head movement, walking, and riding in a moving vehicle, MuteIt achieves mean word recognition accuracy of 91% over all scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550281"}, {"primary_key": "1476021", "vector": [], "sparse_vector": [], "title": "Earmonitor: In-ear Motion-resilient Acoustic Sensing Using Commodity Earphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xudong Wei", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Earphones are emerging as the most popular wearable devices and there has been a growing trend in bringing intelligence to earphones. Previous efforts include adding extra sensors (e.g., accelerometer and gyroscope) or peripheral hardware to make earphones smart. These methods are usually complex in design and also incur additional cost. In this paper, we present Earmonitor, a low-cost system that uses the in-ear earphones to achieve sensing purposes. The basic idea behind Earmonitor is that each person's ear canal varies in size and shape. We therefore can extract the unique features from the ear canal-reflected signals to depict the personalized differences in ear canal geometry. Furthermore, we discover that the signal variations are also affected by the fine-grained physiological activities. We can therefore further detect the subtle heartbeat from the ear canal reflections. Experiments show that Earmonitor can achieve up to 96.4% Balanced Accuracy (BAC) and low False Acceptance Rate (FAR) for user identification on a large-scale data of 120 subjects. For heartbeat monitoring, without any training, we propose signal processing schemes to achieve high sensing accuracy even in the most challenging scenarios when the target is walking or running.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569472"}, {"primary_key": "1476023", "vector": [], "sparse_vector": [], "title": "Driver Maneuver Identification with Multi-Representation Learning and Meta Model Update Designs.", "authors": ["<PERSON><PERSON>", "Suining He", "<PERSON>"], "summary": "Driver maneuver identification (DMI), i.e., the task of predicting the driver maneuver classes given sensor measurements (such as IMU sensors from mobile devices), can serve as a key enabler for many ubiquitous driver behavior analysis applications. Despite the prior studies, two major technical challenges remain before an effective DMI system can be deployed: (i) latent complex maneuver behavior feature relations for accuracy enhancement, and (ii) inconsistency and uncertainty in model adaptation given dynamic data collection settings (e.g., given different urban environments, drivers, and mobile devices). To address the aforementioned challenges, we propose MetaDMI, a novel and adaptive driver maneuver identification framework based on multi-representation learning and meta model update, with the case study on the inertial measurement unit (IMU) sensor measurements (i.e., accelerometer and gyroscope). Specifically, we first extract multiple feature representations for each driver maneuver record in the forms of graph, spectral, and time sequence. Next, MetaDMI processes them with a novel multi-representation learning network, extracting complex patterns and feature relations from the driver maneuvers. Finally, to further enhance the adaptivity of our DMI model to external impacts with dynamic data collection, we have designed a regularized meta learning-based training method to regularize the knowledge transfers across the source and target datasets (e.g., across cities/devices, and few-shot initialization) for consistent and robust identification performance. We have conducted extensive experimental studies upon our MetaDMI prototype based on two datasets (one is collected on our own) and shown that our approach outperforms other baseline approaches for DMI in terms of accuracy and adaptivity.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534603"}, {"primary_key": "1476025", "vector": [], "sparse_vector": [], "title": "Predicting Performance Improvement of Human Activity Recognition Model by Additional Data Collection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The development of a machine-learning-based human activity recognition (HAR) system using body-worn sensors is mainly composed of three phases: data collection, model training, and evaluation. During data collection, the HAR developer collects labeled data from participants wearing inertial sensors. In the model training phase, the developer trains the HAR model on the collected training data. In the evaluation phase, the developer evaluates the trained HAR model on the collected test data. When the HAR model cannot achieve the target recognition accuracy, the developer iterates the above procedures by taking certain measures, including collecting additional training data, until the re-trained model achieves the target accuracy. However, collecting labeled data for HAR requires additional time and incurs high monetary costs. In addition, it is difficult to determine the amount and type of data to collect for achieving the target accuracy while reducing costs. To address this issue, this paper proposes a new method that predicts the performance improvement of the current HAR model, i.e., it determines the level of performance improvement achievable by re-training the HAR model with additional data, before collecting the additional data. Thus, the method enables the HAR developer to establish a strategy for additional data collection by providing advice such as \"If labeled data for the Walking and Running activities from two additional participants is collected, the HAR accuracy of the current HAR model for Walking will improve by 20%.\" To achieve this, a neural network called AIP-Net is proposed to estimate the improvement in performance by analyzing the feature space of the current HAR model using the proposed entropy-based attention mechanism. The performance of AIP-Net was evaluated on eight HAR datasets using leave-one-dataset-out cross-validation.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550319"}, {"primary_key": "1476026", "vector": [], "sparse_vector": [], "title": "SunBox: Screen-to-Camera Communication with Ambient Light.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marco <PERSON>llo<PERSON>"], "summary": "A recent development in wireless communication is the use of optical shutters and smartphone cameras to create optical links solely from ambient light. At the transmitter, a liquid crystal display (LCD) modulates ambient light by changing its level of transparency. At the receiver, a smartphone camera decodes the optical pattern. This LCD-to-camera link requires low-power levels at the transmitter, and it is easy to deploy because it does not require modifying the existing lighting infrastructure. The system, however, provides a low data rate, of just a few tens of bps. This occurs because the LCDs used in the state-of-the-art are slow single-pixel transmitters. To overcome this limitation, we introduce a novel multi-pixel display. Our display is similar to a simple screen, but instead of using embedded LEDs to radiate information, it uses only the surrounding ambient light. We build a prototype, called SunBox, and evaluate it indoors and outdoors with both, artificial and natural ambient light. Our results show that SunBox can achieve a throughput between 2 kbps and 10 kbps using a low-end smartphone camera with just 30 FPS. To the best of our knowledge, this is the first screen-to-camera system that works solely with ambient light.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534602"}, {"primary_key": "1476027", "vector": [], "sparse_vector": [], "title": "DepreST-CAT: Retrospective Smartphone Call and Text Logs Collected during the COVID-19 Pandemic to Screen for Mental Illnesses.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rates of mental illness, especially anxiety and depression, have increased greatly since the start of the COVID-19 pandemic. Traditional mental illness screening instruments are too cumbersome and biased to screen an entire population. In contrast, smartphone call and text logs passively capture communication patterns and thus represent a promising screening alternative. To facilitate the advancement of such research, we collect and curate the DepreST Call and Text log (DepreST-CAT) dataset from over 365 crowdsourced participants during the COVID-19 pandemic. The logs are labeled with traditional anxiety and depression screening scores essential for training machine learning models. We construct time series ranging from 2 to 16 weeks in length from the retrospective smartphone logs. To demonstrate the screening capabilities of these time series, we then train a variety of unimodal and multimodal machine and deep learning models. These models provide insights into the relative screening value of the different types of logs, lengths of log time series, and classification methods. The DepreST-CAT dataset is a valuable resource for the research community to model communication patterns during the COVID-19 pandemic and further the development of machine learning algorithms for passive mental illness screening.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534596"}, {"primary_key": "1476029", "vector": [], "sparse_vector": [], "title": "mRisk: Continuous Risk Estimation for Smoking Lapse from Noisy Sensor Data with Incomplete and Positive-Only Labels.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Passive detection of risk factors (that may influence unhealthy or adverse behaviors) via wearable and mobile sensors has created new opportunities to improve the effectiveness of behavioral interventions. A key goal is to find opportune moments for intervention by passively detecting rising risk of an imminent adverse behavior. But, it has been difficult due to substantial noise in the data collected by sensors in the natural environment and a lack of reliable label assignment of low- and high-risk states to the continuous stream of sensor data. In this paper, we propose an event-based encoding of sensor data to reduce the effect of noises and then present an approach to efficiently model the historical influence of recent and past sensor-derived contexts on the likelihood of an adverse behavior. Next, to circumvent the lack of any confirmed negative labels (i.e., time periods with no high-risk moment), and only a few positive labels (i.e., detected adverse behavior), we propose a new loss function. We use 1,012 days of sensor and self-report data collected from 92 participants in a smoking cessation field study to train deep learning models to produce a continuous risk estimate for the likelihood of an impending smoking lapse. The risk dynamics produced by the model show that risk peaks an average of 44 minutes before a lapse. Simulations on field study data show that using our model can create intervention opportunities for 85% of lapses with 5.5 interventions per day.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550308"}, {"primary_key": "1476030", "vector": [], "sparse_vector": [], "title": "VECTOR: Velocity Based Temperature-field Monitoring with Distributed Acoustic Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shu<PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ambient temperature distribution monitoring is desired in a variety of real-life applications including indoors temperature control and building energy management. Traditional temperature sensors have their limitations in the aspects of single point/item based measurements, slow response time and huge cost for distribution estimation. In this paper, we introduce VECTOR, a temperature-field monitoring system that achieves high temperature sensing accuracy and fast response time using commercial sound playing/recording devices. First, our system uses a distributed ranging algorithm to measure the time-of-flight of multiple sound paths with microsecond resolution. We then propose a dRadon transform algorithm that reconstructs the temperature distribution from the measured speed of sound along different paths. Our experimental results show that we can measure the temperature with an error of 0.25°C from single sound path and reconstruct the temperature distribution at a decimeter-level spatial resolution.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550336"}, {"primary_key": "1476031", "vector": [], "sparse_vector": [], "title": "ECG-grained Cardiac Monitoring Using UWB Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the development of wireless sensing, researchers have proposed many contactless vital sign monitoring systems, which can be used to monitor respiration rates, heart rates, cardiac cycles and etc. However, these vital signs are ones of coarse granularity, so they are less helpful in the diagnosis of cardiovascular diseases (CVDs). Considering that electrocardiogram (ECG) is an important evidence base for the diagnoses of CVDs, we propose to generate ECGs from ultra-wideband (UWB) signals in a contactless manner as a fine-grained cardiac monitoring solution. Specifically, we analyze the properties of UWB signals containing heartbeats and respiration, and design two complementary heartbeat signal restoration methods to perfectly recover heartbeat signal variation. To establish the mapping between the mechanical activity of the heart sensed by UWB devices and the electrical activity of the heart recorded in ECGs, we construct a conditional generative adversarial network to encode the mapping between mechanical activity and electrical activity and propose a contrastive learning strategy to reduce the interference from noise in UWB signals. We build the corresponding cardiac monitoring system named RF-ECG and conduct extensive experiments using about 120,000 heartbeats from more than 40 participants. The experimental results show that the ECGs generated by RF-ECG have good performance in both ECG intervals and morphology compared with the ground truth. Moreover, diseases such as tachycardia/bradycardia, sinus arrhythmia, and premature contractions can be diagnosed from the ECGs generated by our RF-ECG.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569503"}, {"primary_key": "1476032", "vector": [], "sparse_vector": [], "title": "Wavesdropper: Through-wall Word Detection of Human Speech via Commercial mmWave Devices.", "authors": ["<PERSON>", "<PERSON>", "Zhongjie Ba", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most existing eavesdropping attacks leverage propagating sound waves for speech retrieval. However, soundproof materials are widely deployed in speech-sensitive scenes (e.g., a meeting room). In this paper, we reveal that human speech protected by an isolated room can be compromised by portable and commercial off-the-shelf mmWave devices. To achieve this goal, we develop Wavesdropper, a word detection system that utilizes a mmWave probe to sense the targeted speaker's throat vibration and recover speech contents in the obstructed condition. We proposed a CEEMD-based method to suppress dynamic clutters (e.g., human movements) in the room and a wavelet-based processing method to extract the delicate vocal vibration information from the hybrid signals. To recover speech contents from mmWave signals related to the vocal vibration, we designed a neural network to infer the speech contents. Moreover, we explored word detection on a conversation with multiple (two) probes and reveal that the adversary can detect words on multiple people simultaneously with only one mmWave device. We performed extensive experiments to evaluate the system performance with over 60,000 pronunciations. The experimental results indicate that Wavesdropper can achieve 91.3% accuracy for 57-word recognition on 23 volunteers.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534592"}, {"primary_key": "1476033", "vector": [], "sparse_vector": [], "title": "MotorBeat: Acoustic Communication for Home Appliances via Variable Pulse Width Modulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ming Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "More and more home appliances are now connected to the Internet, thus enabling various smart home applications. However, a critical problem that may impede the further development of smart home is overlooked: Small appliances account for the majority of home appliances, but they receive little attention and most of them are cut off from the Internet. To fill this gap, we propose MotorBeat, an acoustic communication approach that connects small appliances to a smart speaker. Our key idea is to exploit direct current (DC) motors, which are common components of small appliances, to transmit acoustic messages. We design a novel scheme named Variable Pulse Width Modulation (V-PWM) to drive DC motors. MotorBeat achieves the following 3C goals: (1) Comfortable to hear, (2) Compatible with multiple motor modes, and (3) Concurrent transmission. We implement MotorBeat with commercial devices and evaluate its performance on three small appliances and ten DC motors. The results show that the communication range can be up to 10 m.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517255"}, {"primary_key": "1476034", "vector": [], "sparse_vector": [], "title": "Toward Understanding Playful Beverage-based Gustosonic Experiences.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Florian &apos;Floyd&apos; Mueller"], "summary": "Recent advances in interactive technology are being used to enrich our interactions around food and drinks. Making use of sound to enrich dining - providing \"gustosonic\" experiences - has recently garnered interest as an exciting area of ubiquitous computing. However, these experiences have tended to focus on eating. In response to the lack of drinking-focused experiences, we explore the combining playful design and drinking activities so as to allow users to experience playful personalized sounds via drinking through \"Sonic Straws\". We present the findings of an in-the-wild study that highlights how our system supported self-expression via playful drinking actions, facilitated pleasurable social drinking moments, and promoted reflection on drinking practices. We discuss the implications of this work for designers of future gustosonic experiences, including how to amplify entertainment experiences around drinking/eating, how to highlight the joy coming from multisensory experiences, and how to facilitate mindful engagement with what one drinks. Ultimately, we aim to contribute to the enrichment of dining experiences through playful design.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517228"}, {"primary_key": "1476035", "vector": [], "sparse_vector": [], "title": "LoEar: Push the Range Limit of Acoustic Sensing for Vital Sign Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Acoustic sensing has been explored in numerous applications leveraging the wide deployment of acoustic-enabled devices. However, most of the existing acoustic sensing systems work in a very short range only due to fast attenuation of ultrasonic signals, hindering their real-world deployment. In this paper, we present a novel acoustic sensing system using only a single microphone and speaker, named LoEar, to detect vital signs (respiration and heartbeat) with a significantly increased sensing range. We first develop a model, namely Carrierforming, to enhance the signal-to-noise ratio (SNR) via coherent superposition across multiple subcarriers on the target path. We then propose a novel technique called Continuous-MUSIC (Continuous-MUltiple SIgnal Classification) to detect a dynamic reflections, containing subtle motion, and further identify the target user based on the frequency distribution to enable Carrierforming. Finally, we adopt an adaptive Infinite Impulse Response (IIR) comb notch filter to recover the heartbeat pattern from the Channel Frequency Response (CFR) measurements which are dominated by respiration and further develop a peak-based scheme to estimate respiration rate and heart rate. We conduct extensive experiments to evaluate our system, and results show that our system outperforms the state-of-the-art using commercial devices, i.e., the range of respiration sensing is increased from 2 m to 7 m, and the range of heartbeat sensing is increased from 1.2 m to 6.5 m.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550293"}, {"primary_key": "1476036", "vector": [], "sparse_vector": [], "title": "PhysiQ: Off-site Quality Assessment of Exercise in Physical Therapy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physical therapy (PT) is crucial for patients to restore and maintain mobility, function, and well-being. Many on-site activities and body exercises are performed under the supervision of therapists or clinicians. However, the postures of some exercises at home cannot be performed accurately due to the lack of supervision, quality assessment, and self-correction. Therefore, in this paper, we design a new framework, PhysiQ, that continuously tracks and quantitatively measures people's off-site exercise activity through passive sensory detection. In the framework, we create a novel multi-task spatio-temporal Siamese Neural Network that measures the absolute quality through classification and relative quality based on an individual's PT progress through similarity comparison. PhysiQ digitizes and evaluates exercises in three different metrics: range of motions, stability, and repetition.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3570349"}, {"primary_key": "1476038", "vector": [], "sparse_vector": [], "title": "Placement Matters: Understanding the Effects of Device Placement for WiFi Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tairong Lou", "<PERSON><PERSON> Zhang"], "summary": "WiFi-based contactless sensing has found numerous applications in the fields of smart home and health care owning to its low-cost, non-intrusive and privacy-preserving characteristics. While promising in many aspects, the limited sensing range and interference issues still exist, hindering the adoption of WiFi sensing in real world. In this paper, inspired by the SNR (signal-to-noise ratio) metric in communication theory, we propose a new metric named SSNR (sensing-signal-to-noise-ratio) to quantify the sensing capability of WiFi systems. We theoretically model the effect of transmitter-receiver distance on sensing coverage. We show that in LoS scenario, the sensing coverage area increases first from a small oval to a maximal one and then decreases. When the transmitter-receiver distance further increases, the coverage area is separated into two ovals located around the two transceivers respectively. We demonstrate that, instead of applying complex signal processing scheme or advanced hardware, by just properly placing the transmitter and receiver, the two well-known issues in WiFi sensing (i.e., small range and severe interference) can be greatly mitigated. Specifically, by properly placing the transmitter and receiver, the coverage of human walking sensing can be expanded by around 200%. By increasing the transmitter-receiver distance, a target's fine-grained respiration can still be accurately sensed with one interferer sitting just 0.5 m away.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517237"}, {"primary_key": "1476039", "vector": [], "sparse_vector": [], "title": "ToothSonic: Earable Authentication via Acoustic Toothprint.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Earables (ear wearables) are rapidly emerging as a new platform encompassing a diverse range of personal applications. The traditional authentication methods hence become less applicable and inconvenient for earables due to their limited input interface. Nevertheless, earables often feature rich around-the-head sensing capability that can be leveraged to capture new types of biometrics. In this work, we propose ToothSonic that leverages the toothprint-induced sonic effect produced by a user performing teeth gestures for earable authentication. In particular, we design representative teeth gestures that can produce effective sonic waves carrying the information of the toothprint. To reliably capture the acoustic toothprint, it leverages the occlusion effect of the ear canal and the inward-facing microphone of the earables. It then extracts multi-level acoustic features to reflect the intrinsic toothprint information for authentication. The key advantages of ToothSonic are that it is suitable for earables and is resistant to various spoofing attacks as the acoustic toothprint is captured via the user's private teeth-ear channel that modulates and encrypts the sonic waves. Our experiment studies with 25 participants show that ToothSonic achieves up to 95% accuracy with only one of the users' tooth gestures.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534606"}, {"primary_key": "1476040", "vector": [], "sparse_vector": [], "title": "Ultra Low-Latency Backscatter for Fast-Moving Location Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper explores building an ultra-low latency and high-accuracy location tracking solution using battery-free tags. While there is rich prior work on location tracking with battery-free RFID tags and backscatter devices, these systems typically face tradeoffs with accuracy, power consumption, and latency. Such limitations make these existing solutions unsuitable for emerging applications like industrial augmented reality which requires tracking fast-moving machinery; monitoring indoor sports activities that require real-time tracking of fast-moving objects with high precision and under stringent latency constraints. We propose and demonstrate FastLoc, a precision tracking system that locates tiny, battery-free analog backscatter tags at sub-millisecond latency and sub-centimeter accuracy. FastLoc is a hybrid system that simultaneously uses RF and optical signals to track tiny tags that can be attached to everyday objects. FastLoc leverages the RF channel responses from tags for estimating the coarse region where the tags may be located. It simultaneously uses the sensed optical information modulated on the backscatter signals to enable fine-grained location estimation within the coarse region. To achieve this, we design and fabricate a custom analog tag that consumes less than 150 uW and instantaneously converts incident optical signals to one-shot wideband harmonic RF responses at nanosecond latency. We then develop a static high-density distributed-frequency structured light pattern that can localize tags in the area of interest at a sub-centimeter accuracy and microsecond-scale latency. A detailed experimental evaluation of FastLoc shows a median accuracy of 0.7 cm in tag localization with a 0.51 ms effective localization latency.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517242"}, {"primary_key": "1476041", "vector": [], "sparse_vector": [], "title": "RF-Chain: Decentralized, Credible, and Counterfeit-proof Supply Chain Management with Commodity RFIDs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blockchain-based supply chains provide a new solution to decentralized multi-party product management. However, existing methods, including ID-based and cryptographic-based solutions, cannot achieve both counterfeit resistance and decentralization in supply chain management. We argue that this dilemma comes from the disconnection and inconsistency of the data records and physical product entities. This paper proposes RF-Chain, a novel decentralized supply chain management solution that uniquely combines data record authentication and physical-layer RFID tag authentication to effectively achieve credibility and counterfeit resistance. The main contribution of this work is to integrate physical-layer authentication of cheap commodity RFID tags into a blockchain-based information management system, and RF-Chain is the first to do so. The proposed cross-layer authentication can effectively defend against counterfeit attacks without relying on a central key management service. Real-world experiments utilizing the Ethereum (ETH) platform and more than 100 RFID tags demonstrate that RF-Chain is secure, effective, time-efficient, and cost-efficient.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569493"}, {"primary_key": "1476042", "vector": [], "sparse_vector": [], "title": "HearFire: Indoor Fire Detection via Inaudible Acoustic Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Indoor conflagration causes a large number of casualties and property losses worldwide every year. Yet existing indoor fire detection systems either suffer from short sensing range (e.g., ≤ 0.5m using a thermometer), susceptible to interferences (e.g., smoke detector) or high computational and deployment overhead (e.g., cameras, Wi-Fi). This paper proposes HearFire, a cost-effective, easy-to-use and timely room-scale fire detection system via acoustic sensing. HearFire consists of a collocated commodity speaker and microphone pair, which remotely senses fire by emitting inaudible sound waves. Unlike existing works that use signal reflection effect to fulfill acoustic sensing tasks, HearFire leverages sound absorption and sound speed variations to sense the fire due to unique physical properties of flame. Through a deep analysis of sound transmission, HearFire effectively achieves room-scale sensing by correlating the relationship between the transmission signal length and sensing distance. The transmission frame is carefully selected to expand sensing range and balance a series of practical factors that impact the system's performance. We further design a simple yet effective approach to remove the environmental interference caused by signal reflection by conducting a deep investigation into channel differences between sound reflection and sound absorption. Specifically, sound reflection results in a much more stable pattern in terms of signal energy than sound absorption, which can be exploited to differentiate the channel measurements caused by fire from other interferences. Extensive experiments demonstrate that HireFire enables a maximum 7m sensing range and achieves timely fire detection in indoor environments with up to 99.2% accuracy under different experiment configurations.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569500"}, {"primary_key": "1476044", "vector": [], "sparse_vector": [], "title": "IndexPen: Two-Finger Text Input with Millimeter-Wave Radar.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce IndexPen, a novel interaction technique for text input through two-finger in-air micro-gestures, enabling touch-free, effortless, tracking-based interaction, designed to mirror real-world writing. Our system is based on millimeter-wave radar sensing, and does not require instrumentation on the user. IndexPen can successfully identify 30 distinct gestures, representing the letters A-Z, as well as Space, Backspace, Enter, and a special Activation gesture to prevent unintentional input. Additionally, we include a noise class to differentiate gesture and non-gesture noise. We present our system design, including the radio frequency (RF) processing pipeline, classification model, and real-time detection algorithms. We further demonstrate our proof-of-concept system with data collected over ten days with five participants yielding 95.89% cross-validation accuracy on 31 classes (including noise). Moreover, we explore the learnability and adaptability of our system for real-world text input with 16 participants who are first-time users to IndexPen over five sessions. After each session, the pre-trained model from the previous five-user study is calibrated on the data collected so far for a new user through transfer learning. The F-1 score showed an average increase of 9.14% per session with the calibration, reaching an average of 88.3% on the last session across the 16 users. Meanwhile, we show that the users can type sentences with IndexPen at 86.2% accuracy, measured by string similarity. This work builds a foundation and vision for future interaction interfaces that could be enabled with this paradigm.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534601"}, {"primary_key": "1476045", "vector": [], "sparse_vector": [], "title": "SafeGait: Safeguarding Gait-based Key Generation against Vision-based Side Channel Attack Using Generative Adversarial Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent works have shown that wearable or implanted devices attached at different locations of the body can generate an identical security key from their independent measurements of the same gait. This has created an opportunity to realize highly secured data exchange to and from critical implanted devices. In this paper, we first demonstrate that vision can be used to easily attack such gait-based key generations; an attacker with a commodity camera can measure the gait from a distance and generate a security key with any target wearable or implanted device faster than other legitimate devices worn at different locations of the subject's body. To counter the attack, we propose a firewall to stop video-based gait measurements to proceed with key generation, but letting measurements from inertial measurement units (IMUs) that are widely used in wearable devices to measure the gait accelerations from the body to proceed. We implement the firewall concept with an IMU-vs-Video binary classifier that combines InceptionTime, an ensemble of deep Convolutional Neural Network (CNN) models for effective feature extraction from gait measurements, to a Generative Adversarial Network (GAN) that can generalize the classifier across subjects. Comprehensive evaluation with a real-world dataset shows that our proposed classifier can perform with an accuracy of 97.82%. Given that an attacker has to fool the classifier for multiple consecutive gait cycles to generate the complete key, the high single-cycle classification accuracy results in an extremely low probability for a video attacker to successfully pair with a target wearable device. More precisely, a video attacker would have one in a billion chance to successfully generate a 128-bit key, which would require the attacker to observe the subject for thousands of years.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534607"}, {"primary_key": "1476046", "vector": [], "sparse_vector": [], "title": "Use It-No Need to Shake It!: Accurate Implicit Authentication for Everyday Objects with Smart Sensing.", "authors": ["<PERSON><PERSON><PERSON> Wu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaojiang Du", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Implicit authentication for traditional objects, such as doors and dumbbells, has rich applications but is rarely studied. An ongoing trend is that traditional objects are retrofitted to smart environments; for instance, a contact sensor is attached to a door to detect door opening (but cannot tell \"who is opening the door\"). We present the first accurate implicit-authentication system for retrofitted everyday objects, named MoMatch. It makes an authentication decision based on a single natural object use, unlike prior work that requires shaking objects. MoMatch is built on the observation that an object has a motion typically because a human hand moves it; thus, the object's motion and the legitimate user's hand movement should correlate. The main challenge is, given the small amount of data collected during one object use, how to measure the correlation accurately. We convert the correlation measurement problem into an image comparison problem and resolve it using neural networks successfully. MoMatch does not need to profile the user's biometric information and is resilient to mimicry attacks.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550322"}, {"primary_key": "1476048", "vector": [], "sparse_vector": [], "title": "VoLearn: A Cross-Modal Operable Motion-Learning System Combined with Virtual Avatar and Auditory Feedback.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Conventional motion tutorials rely mainly on a predefined motion and vision-based feedback that normally limits the application scenario and requires professional devices. In this paper, we propose VoLearn, a cross-modal system that provides operability for user-defined motion learning. The system supports the ability to import a desired motion from RGB video and animates the motion in a 3D virtual environment. We built an interface to operate on the input motion, such as controlling the speed, and the amplitude of limbs for the respective directions. With exporting of virtual rotation data, a user can employ a daily device (i.e., smartphone) as a wearable device to train and practice the desired motion according to comprehensive auditory feedback, which is able to provide both temporal and amplitude assessment. The user study demonstrated that the system helps reduce the amplitude and time errors of motion learning. The developed motion-learning system maintains the characteristics of high user accessibility, flexibility, and ubiquity in its application.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534576"}, {"primary_key": "1476050", "vector": [], "sparse_vector": [], "title": "EarSpiro: Earphone-based Spirometry for Lung Function Assessment.", "authors": ["<PERSON><PERSON><PERSON>", "Qingyong Hu", "<PERSON>", "<PERSON><PERSON>"], "summary": "Spirometry is the gold standard for evaluating lung functions. Recent research has proposed that mobile devices can measure lung function indices cost-efficiently. However, these designs fall short in two aspects. First, they cannot provide the flow-volume (F-V) curve, which is more informative than lung function indices. Secondly, these solutions lack inspiratory measurement, which is sensitive to lung diseases such as variable extrathoracic obstruction. In this paper, we present EarSpiro, an earphone-based solution that interprets the recorded airflow sound during a spirometry test into an F-V curve, including both the expiratory and inspiratory measurements. EarSpiro leverages a convolutional neural network (CNN) and a recurrent neural network (RNN) to capture the complex correlation between airflow sound and airflow speed. Meanwhile, EarSpiro adopts a clustering-based segmentation algorithm to track the weak inspiratory signals from the raw audio recording to enable inspiratory measurement. We also enable EarSpiro with daily mouthpiece-like objects such as a funnel using transfer learning and a decoder network with the help of only a few true lung function indices from the user. Extensive experiments with 60 subjects show that EarSpiro achieves mean errors of 0.20L/s and 0.42L/s for expiratory and inspiratory flow rate estimation, and 0.61L/s and 0.83L/s for expiratory and inspiratory F-V curve estimation. The mean correlation coefficient between the estimated F-V curve and the true one is 0.94. The mean estimation error for four common lung function indices is 7.3%.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569480"}, {"primary_key": "1476051", "vector": [], "sparse_vector": [], "title": "TransFloor: Transparent Floor Localization for Crowdsourcing Instant Delivery.", "authors": ["Zhiqing Xie", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart on-demand delivery services require accurate indoor localization to enhance the system-human synergy experience of couriers in complex multi-story malls and platform construction. Floor localization is an essential part of indoor positioning, which can provide floor/altitude data support for upper-level 3D indoor navigation services (e.g., delivery route planning) to improve delivery efficiency and optimize order dispatching strategies. We argue that due to label dependence and device dependence, the existing floor localization methods cannot be flexibly deployed on a large scale in numerous multi-story malls across the country, nor can they apply to all couriers/users on the platform. This paper proposes a novel self-evolving and user-transparent floor localization system named TransFloor, based on crowdsourcing delivery data (e.g., order status and sensors data) without additional label investment and specialized equipment constraints. TransFloor consists of an unsupervised barometer-based module--IOD-TKPD and an NLP-inspired Wi-Fi-based module--Wifi2Vec, and Self-Labeling is a perfect bridge between both to completely achieve label-free and device-independent floor positioning. In addition, TransFloor is designed as a lightweight plugin embedded into the platform without refactoring the existing architecture, and it has been deployed nationwide to adaptively launch real-time accurate 3D/floor positioning services for numerous crowdsourcing couriers. We evaluate TransFloor on real-world records from an instant delivery platform (involving 672,282 orders, 7,390 couriers, and 6,206 merchants in 388 malls during two months). It can achieve an average accuracy of 94.61% and demonstrate good robustness to device heterogeneity and adaptive durability, outperforming existing state-of-the-art methods. Crucially, it can effectively improve user satisfaction and reduce overdue delivery by providing accurate floor navigation information in complex multi-story malls. As a case study, the platform reduces erroneous order scheduling by 60% and overdue delivery by 2.7%, and increases delivery efficiency by reducing courier arrival time by 12.27 seconds accounting for 7.29%. We believe that the key ideas of TransFloor can be extended to other crowdsourcing scenarios for the public further.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569470"}, {"primary_key": "1476052", "vector": [], "sparse_vector": [], "title": "Combating False Data Injection Attacks on Human-Centric Sensing Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "The recent prevalence of machine learning-based techniques and smart device embedded sensors has enabled widespread human-centric sensing applications. However, these applications are vulnerable to false data injection attacks (FDIA) that alter a portion of the victim's sensory signal with forged data comprising a targeted trait. Such a mixture of forged and valid signals successfully deceives the continuous authentication system (CAS) to accept it as an authentic signal. Simultaneously, introducing a targeted trait in the signal misleads human-centric applications to generate specific targeted inference; that may cause adverse outcomes. This paper evaluates the FDIA's deception efficacy on sensor-based authentication and human-centric sensing applications simultaneously using two modalities - accelerometer, blood volume pulse signals. We identify variations of the FDIA such as different forged signal ratios, smoothed and non-smoothed attack samples. Notably, we present a novel attack detection framework named Siamese-MIL that leverages the Siamese neural networks' generalizable discriminative capability and multiple instance learning paradigms through a unique sensor data representation. Our exhaustive evaluation demonstrates Siamese-MIL's real-time execution capability and high efficacy in different attack variations, sensors, and applications.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534577"}, {"primary_key": "1476053", "vector": [], "sparse_vector": [], "title": "GLOBEM: Cross-Dataset Generalization of Longitudinal Human Behavior Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Subigya Nepal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Shwetak N. Patel", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There is a growing body of research revealing that longitudinal passive sensing data from smartphones and wearable devices can capture daily behavior signals for human behavior modeling, such as depression detection. Most prior studies build and evaluate machine learning models using data collected from a single population. However, to ensure that a behavior model can work for a larger group of users, its generalizability needs to be verified on multiple datasets from different populations. We present the first work evaluating cross-dataset generalizability of longitudinal behavior models, using depression detection as an application. We collect multiple longitudinal passive mobile sensing datasets with over 500 users from two institutes over a two-year span, leading to four institute-year datasets. Using the datasets, we closely re-implement and evaluated nine prior depression detection algorithms. Our experiment reveals the lack of model generalizability of these methods. We also implement eight recently popular domain generalization algorithms from the machine learning community. Our results indicate that these methods also do not generalize well on our datasets, with barely any advantage over the naive baseline of guessing the majority. We then present two new algorithms with better generalizability. Our new algorithm, Reorder, significantly and consistently outperforms existing methods on most cross-dataset generalization setups. However, the overall advantage is incremental and still has great room for improvement. Our analysis reveals that the individual differences (both within and between populations) may play the most important role in the cross-dataset generalization challenge. Finally, we provide an open-source benchmark platform GLOBEM- short for Generalization of Longitudinal BEhavior Modeling - to consolidate all 19 algorithms. GLOBEM can support researchers in using, developing, and evaluating different longitudinal behavior modeling methods. We call for researchers' attention to model generalizability evaluation for future longitudinal human behavior modeling studies.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569485"}, {"primary_key": "1476054", "vector": [], "sparse_vector": [], "title": "Wet-Ra: Monitoring Diapers Wetness with Wireless Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chunkai Fan"], "summary": "Diaper wetness monitoring is essential in various situations (e.g., babies and patients) to guarantee hygiene and avoid embarrassment. Existing diaper wetness monitoring methods include indicator lines, special sensors, and RFID, which require modifications on every diaper piece and cannot be easily checked under visual occlusions (e.g., trousers). In this paper, we introduce Wet-Ra, a contactless, ubiquitous, and user-friendly diaper wetness monitoring system based on RF signals. To extract informative features for wetness detection from RF signals, we construct Continuous-Radio-Snapshot and build corresponding signal representations that capture the distinct patterns of diapers of different wetness levels. We refine the signal representation by eliminating multi-path interference from the environment and mitigating the smearing effect with wavelet multisynchrosqueezing transform. To expand the usability of Wet-Ra, we build a transferable model that yields robust detection results in diversified environments and for new users. We conduct extensive experiments to evaluate Wet-Ra with 47 volunteers in 7 different rooms with three off-the-shelf diaper brands. Experiment results confirm that Wet-Ra can accurately identify diaper wetness in the real environment.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534599"}, {"primary_key": "1476056", "vector": [], "sparse_vector": [], "title": "VULoc: Accurate UWB Localization for Countless Targets without Synchronization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ultra-WideBand (UWB) localization has shown promising prospects in both academia and industry. However, accurate UWB localization for a large number of tags (i.e., targets) is still an open problem. Existing works usually require tedious time synchronization and labor-intensive calibrations. We present VULoc, an accurate UWB localization system with high scalability for an unlimited number of targets, which significantly reduces synchronization and calibration overhead. The key idea of VULoc is an accurate localization method based on passive reception without time synchronization. Specifically, we propose a novel virtual-Two Way Ranging (V-TWR) method to enable accurate localization for an unlimited number of tags. We theoretically analyze the performance of our method and show its superiority. We leverage redundant ranging packets among anchors with known positions to infer a range mapping for auto-calibration, which eliminates the ranging bias arising from the hardware and multipath issues. We finally design an anchor scheduling algorithm, which estimates reception quality for adaptive anchor selection to minimize the influence of NLOS. We implement VULoc with DW1000 chips and extensively evaluate its performance in various environments. The results show that VULoc can achieve accurate localization with a median error of 10.5 cm and 90% error of 15.7 cm, reducing the error of ATLAS (an open-source TDOA-based UWB localization system) by 57.6% while supporting countless targets with no synchronization and low calibration overhead.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550286"}, {"primary_key": "1476058", "vector": [], "sparse_vector": [], "title": "Side-lobe Can Know More: Towards Simultaneous Communication and Sensing for mmWave.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Tao", "<PERSON><PERSON>"], "summary": "Thanks to the wide bandwidth, large antenna array, and short wavelength, millimeter wave (mmWave) has superior performance in both communication and sensing. Thus, the integration of sensing and communication is a developing trend for the mmWave band. However, the directional transmission characteristics of the mmWave limits the sensing scope to a narrow sector. Existing works coordinate sensing and communication in a time-division manner, which takes advantage of the sector level sweep during the beam training interval for sensing and the data transmission interval for communication. Beam training is a low frequency (e.g., 10Hz) and low duty-cycle event, which makes it hard to track fast movement or perform continuous sensing. Such time-division designs imply that we need to strike a balance between sensing and communication, and it is hard to get the best of both worlds. In this paper, we try to solve this dilemma by exploiting side lobes for sensing. We design Sidense, where the main lobe of the transmitter is directed towards the receiver, while in the meantime, the side lobes can sense the ongoing activities in the surrounding. In this way, sensing and downlink communication work simultaneously and will not compete for hardware and radio resources. In order to compensate for the low antenna gain of side lobes, <PERSON><PERSON> performs integration to boost the quality of sensing signals. Due to the uneven side-lobe energy, <PERSON><PERSON> also designs a target separation scheme to tackle the mutual interference in multi-target scenarios. We implement <PERSON><PERSON> with Sivers mmWave module. Results show that <PERSON><PERSON> can achieve millimeter motion tracking accuracy at 6m. We also demonstrate a multi-person respiration monitoring application. As <PERSON><PERSON> does not modify the communication procedure or the beamforming strategy, the downlink communication performance will not be sacrificed due to concurrent sensing. We believe that more fascinating applications can be implemented on this concurrent sensing and communication platform.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569498"}, {"primary_key": "1476059", "vector": [], "sparse_vector": [], "title": "Multi-Vib: Precise Multi-point Vibration Monitoring Using mmWave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vibration measurement is vital for fault diagnosis of structures (e.g., machines and civil structures). Different structure components undergo distinct vibration patterns, which jointly determine the structure's health condition, thus demanding simultaneous multi-point vibration monitoring. Existing solutions deploy multiple accelerometers along with their power supplies or laser vibrometers on the monitored object to measure multi-point vibration, which is inconvenient and costly. Cameras provide a less expensive solution while heavily relying on good lighting conditions. To overcome these limitations, we propose a cost-effective and passive system, called Multi-Vib, for precise multi-point vibration monitoring. Multi-Vib is implemented using a single mmWave radar to remotely and separately sense the vibration displacement of multiple points via signal reflection. However, simultaneously detecting and monitoring multiple points on a single object is a daunting task. This is because most radar signals are scattered away from vibration points due to their tilted locations and shapes by nature, causing an extremely weak reflected signal to the radar. To solve this issue, we dedicatedly design a physical marker placed on the target point, which can force the direction of the reflected signal towards the radar and significantly increase the reflected signal strength. Another practical issue is that the reflected signal from each point endures interferences and noises from the surroundings. Thus, we develop a series of effective signal processing methods to denoise the signal for accurate vibration frequency and displacement estimation. Extensive experimental results show that the average errors in multi-point vibration frequency and displacement estimation are around 0.16Hz and 14μm, respectively.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569496"}, {"primary_key": "1476060", "vector": [], "sparse_vector": [], "title": "Cellular-Assisted, Deep Learning Based COVID-19 Contact Tracing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Coronavirus disease (COVID-19) pandemic has caused social and economic crisis to the globe. Contact tracing is a proven effective way of containing the spread of COVID-19. In this paper, we propose CAPER, a Cellular-Assisted deeP lEaRning based COVID-19 contact tracing system based on cellular network channel state information (CSI) measurements. CAPER leverages a deep neural network based feature extractor to map cellular CSI to a neural network feature space, within which the Euclidean distance between points strongly correlates with the proximity of devices. By doing so, we maintain user privacy by ensuring that CAPER never propagates one client's CSI data to its server or to other clients. We implement a CAPER prototype using a software defined radio platform, and evaluate its performance in a variety of real-world situations including indoor and outdoor scenarios, crowded and sparse environments, and with differing data traffic patterns and cellular configurations in common use. Microbenchmarks show that our neural network model runs in 12.1 microseconds on the OnePlus 8 smartphone. End-to-end results demonstrate that CAPER achieves an overall accuracy of 93.39%, outperforming the accuracy of BLE based approach by 14.96%, in determining whether two devices are within six feet or not, and only misses 1.21% of close contacts. CAPER is also robust to environment dynamics, maintaining an accuracy of 92.35% after running for ten days.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550332"}, {"primary_key": "1476062", "vector": [], "sparse_vector": [], "title": "Acceleration-based Activity Recognition of Repetitive Works with Lightweight Ordered-work Segmentation Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study presents a new neural network model for recognizing manual works using body-worn accelerometers in industrial settings, named Lightweight Ordered-work Segmentation Network (LOS-Net). In industrial domains, a human worker typically repetitively performs a set of predefined processes, with each process consisting of a sequence of activities in a predefined order. State-of-the-art activity recognition models, such as encoder-decoder models, have numerous trainable parameters, making their training difficult in industrial domains because of the consequent substantial cost for preparing a large amount of labeled data. In contrast, the LOS-Net is designed to be trained on a limited amount of training data. Specifically, the decoder in the LOS-Net has few trainable parameters and is designed to capture only the necessary information for precise recognition of ordered works. These are (i) the boundary information between consecutive activities, because a transition in the performed activities is generally associated with the trend change of the sensor data collected during the manual works and (ii) long-term context regarding the ordered works, e.g., information about the previous and next activity, which is useful for recognizing the current activity. This information is obtained by introducing a module that can collect it at distant time steps using few trainable parameters. Moreover, the LOS-Net can refine the activity estimation by the decoder by incorporating prior knowledge regarding the order of activities. We demonstrate the effectiveness of the LOS-Net using sensor data collected from workers in actual factories and a logistics center, and show that it can achieve state-of-the-art performance.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534572"}, {"primary_key": "1476063", "vector": [], "sparse_vector": [], "title": "CornerRadar: RF-Based Indoor Localization Around Corners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unmanned robots are increasingly used around humans in factories, malls, and hotels. As they navigate our space, it is important to ensure that such robots do not collide with people who suddenly appear as they turn a corner. Today, however, there is no practical solution for localizing people around corners. Optical solutions try to track hidden people through their visible shadows on the floor or a sidewall, but they can easily fail depending on the ambient light and the environment. More recent work has considered the use of radio frequency (RF) signals to track people and vehicles around street corners. However, past RF-based proposals rely on a simplistic ray-tracing model that fails in practical indoor scenarios. This paper introduces CornerRadar, an RF-based method that provides accurate around-corner indoor localization. CornerRadar addresses the limitations of the ray-tracing model used in past work. It does so through a novel encoding of how RF signals bounce off walls and occlusions. The encoding, which we call the hint map, is then fed to a neural network along with the radio signals to localize people around corners. Empirical evaluation with people moving around corners in 56 indoor environments shows that CornerRadar achieves a median error that is 3x to 12x smaller than past RF-based solutions for localizing people around corners.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517226"}, {"primary_key": "1476064", "vector": [], "sparse_vector": [], "title": "SleepMore: Inferring Sleep Duration at Scale via Multi-Device WiFi Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The availability of commercial wearable trackers equipped with features to monitor sleep duration and quality has enabled more useful sleep health monitoring applications and analyses. However, much research has reported the challenge of long-term user retention in sleep monitoring through these modalities. Since modern Internet users own multiple mobile devices, our work explores the possibility of employing ubiquitous mobile devices and passive WiFi sensing techniques to predict sleep duration as the fundamental measure for complementing long-term sleep monitoring initiatives. In this paper, we propose SleepMore, an accurate and easy-to-deploy sleep-tracking approach based on machine learning over the user's WiFi network activity. It first employs a semi-personalized random forest model with an infinitesimal jackknife variance estimation method to classify a user's network activity behavior into sleep and awake states per minute granularity. Through a moving average technique, the system uses these state sequences to estimate the user's nocturnal sleep period and its uncertainty rate. Uncertainty quantification enables SleepMore to overcome the impact of noisy WiFi data that can yield large prediction errors. We validate SleepMore using data from a month-long user study involving 46 college students and draw comparisons with the Oura Ring wearable. Beyond the college campus, we evaluate SleepMore on non-student users of different housing profiles. Our results demonstrate that SleepMore produces statistically indistinguishable sleep statistics from the Oura ring baseline for predictions made within a 5% uncertainty rate. These errors range between 15-28 minutes for determining sleep time and 7-29 minutes for determining wake time, proving statistically significant improvements over prior work. Our in-depth analysis explains the sources of errors.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569489"}, {"primary_key": "1476065", "vector": [], "sparse_vector": [], "title": "Toward Reliable Non-Line-of-Sight Localization Using Multipath Reflections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The past decade's research in RF indoor localization has led to technologies with decimeter-level accuracy under controlled experimental settings. However, existing solutions are not reliable in challenging environments with rich multipath and various occlusions. The errors can be 3-5 times compared to settings with clear LoS paths. In addition, when the direct path is completely blocked, such approaches would generate wrong location estimates. In this paper, we present NLoc, a reliable non-line-of-sight localization system that overcomes the above limitations. The key innovation of NLoc is to convert multipath reflections to virtual direct paths to enhance the localization performance. To this end, NLoc first extracts reliable multi-dimensional parameters by characterizing phase variations. Then, it models the relation between the target location and the geometric features of multipath reflections to obtain virtual direct paths. Finally, it incorporates novel algorithms to remove random ToF offsets due to lack of synchronization and compensate target orientation that determines the geometric features, for accurate location estimates. We implement NLoc on commercial off-the-shelf WiFi devices. Our experiments in multipath challenged environments with dozens of obstacles and occlusions demonstrate that NLoc outperforms state-of-the-art approaches by 44% at the median and 200% at 90% percentile.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517244"}, {"primary_key": "1476066", "vector": [], "sparse_vector": [], "title": "MetaGanFi: Cross-Domain Unseen Individual Identification Using WiFi Signals.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Salil S<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Human has an unique gait and prior works show increasing potentials in using WiFi signals to capture the unique signature of individuals' gait. However, existing WiFi-based human identification (HI) systems have not been ready for real-world deployment due to various strong assumptions including identification of known users and sufficient training data captured in predefined domains such as fixed walking trajectory/orientation, WiFi layout (receivers locations) and multipath environment (deployment time and site). In this paper, we propose a WiFi-based HI system, MetaGanFi, which is able to accurately identify unseen individuals in uncontrolled domain with only one or few samples. To achieve this, the MetaGanFi proposes a domain unification model, CCG-GAN that utilizes a conditional cycle generative adversarial networks to filter out irrelevant perturbations incurred by interfering domains. Moreover, the MetaGanFi proposes a domain-agnostic meta learning model, DA-Meta that could quickly adapt from one/few data samples to accurately recognize unseen individuals. The comprehensive evaluation applied on a real-world dataset show that the MetaGanFi can identify unseen individuals with average accuracies of 87.25% and 93.50% for 1 and 5 available data samples (shot) cases, captured in varying trajectory and multipath environment, 86.84% and 91.25% for 1 and 5-shot cases in varying WiFi layout scenarios, while the overall inference process of domain unification and identification takes about 0.1 second per sample.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550306"}, {"primary_key": "1476067", "vector": [], "sparse_vector": [], "title": "Embracing Consumer-level UWB-equipped Devices for Fine-grained Wireless Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "RF sensing has been actively exploited in the past few years to enable novel IoT applications. Among different wireless technologies, WiFi-based sensing is most popular owing to the pervasiveness of WiFi infrastructure. However, one critical issue associated with WiFi sensing is that the information required for sensing can not be obtained from consumer-level devices such as smartphones or smart watches. The commonly-seen WiFi devices in our everyday lives actually can not be utilized for sensing. Instead, dedicated hardware with a specific WiFi card (e.g., Intel 5300) needs to be used for WiFi sensing. This paper involves Ultra-Wideband (UWB) into the ecosystem of RF sensing and makes RF sensing work on consumer-level hardware such as smartphones and smart watches for the first time. We propose a series of methods to realize UWB sensing on consumer-level electronics without any hardware modification. By leveraging fine-grained human respiration monitoring as the application example, we demonstrate that the achieved performance on consumer-level electronics is comparable to that achieved using dedicated UWB hardware. We show that UWB sensing hosted on consumer-level electronics is able to achieve fine granularity, robustness against interference and also multi-target sensing, pushing RF sensing one step towards real-life adoption.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569487"}, {"primary_key": "1476070", "vector": [], "sparse_vector": [], "title": "I Spy You: Eavesdropping Continuous Speech on Smartphones via Motion Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents iSpyU, a system that shows the feasibility of recognition of natural speech content played on a phone during conference calls (Skype, Zoom, etc) using a fusion of motion sensors such as accelerometer and gyroscope. While microphones require permissions from the user to be accessible by an app developer, the motion sensors are zero-permission sensors, thus accessible by a developer without alerting the user. This allows a malicious app to potentially eavesdrop on sensitive speech content played by the user's phone. In designing the attack, iSpyU tackles a number of technical challenges including: (i) Low sampling rate of motion sensors (500 Hz in comparison to 44 kHz for a microphone). (ii) Lack of availability of large-scale training datasets to train models for Automatic Speech Recognition (ASR) with motion sensors. iSpyU systematically addresses these challenges by a combination of techniques in synthetic training data generation, ASR modeling, and domain adaptation. Extensive measurement studies on modern smartphones show a word level accuracy of 53.3 - 59.9% over a dictionary of 2000-10000 words, and a character level accuracy of 70.0 - 74.8%. We believe such levels of accuracy poses a significant threat when viewed from a privacy perspective.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569486"}, {"primary_key": "1476071", "vector": [], "sparse_vector": [], "title": "BLEselect: Gestural IoT Device Selection via Bluetooth Angle of Arrival Estimation from Smart Glasses.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Zitong Lan", "<PERSON><PERSON>", "Yanrong <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spontaneous selection of IoT devices from the head-mounted device is key for user-centered pervasive interaction. BLEselect enables users to select an unmodified Bluetooth 5.1 compatible IoT device by nodding at, pointing at, or drawing a circle in the air around it. We designed a compact antenna array that fits on a pair of smart glasses to estimate the Angle of Arrival (AoA) of IoT and wrist-worn devices' advertising signals. We then developed a sensing pipeline that supports all three selection gestures with lightweight machine learning models, which are trained in real-time for both hand gestures. Extensive characterizations and evaluations show that our system is accurate, natural, low-power, and privacy-preserving. Despite the small effective size of the antenna array, our system achieves a higher than 90% selection accuracy within a 3 meters distance in front of the user. In a user study that mimics real-life usage cases, the overall selection accuracy is 96.7% for a diverse set of 22 participants in terms of age, technology savviness, and body structures.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569482"}, {"primary_key": "1476073", "vector": [], "sparse_vector": [], "title": "IF-ConvTransformer: A Framework for Human Activity Recognition Using IMU Fusion and ConvTransformer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in sensor based human activity recognition (HAR) have exploited deep hybrid networks to improve the performance. These hybrid models combine Convolutional Neural Networks (CNNs) and Recurrent Neural Networks (RNNs) to leverage their complementary advantages, and achieve impressive results. However, the roles and associations of different sensors in HAR are not fully considered by these models, leading to insufficient multi-modal fusion. Besides, the commonly used RNNs in HAR suffer from the 'forgetting' defect, which raises difficulties in capturing long-term information. To tackle these problems, an HAR framework composed of an Inertial Measurement Unit (IMU) fusion block and an applied ConvTransformer subnet is proposed in this paper. Inspired by the complementary filter, our IMU fusion block performs multi-modal fusion of commonly used sensors according to their physical relationships. Consequently, the features of different modalities can be aggregated more effectively. Then, the extracted features are fed into the applied ConvTransformer subnet for classification. Thanks to its convolutional subnet and self-attention layers, ConvTransformer can better capture local features and construct long-term dependencies. Extensive experiments on eight benchmark datasets demonstrate the superior performance of our framework. The source code will be published soon.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534584"}, {"primary_key": "1476074", "vector": [], "sparse_vector": [], "title": "Understanding the Mechanism of Through-Wall Wireless Sensing: A Model-based Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "During the last few years, there is a growing interest on the usage of Wi-Fi signals for human activity detection. A large number of Wi-Fi based sensing systems have been developed, including respiration detection, gesture classification, identity recognition, etc. However, the usability and robustness of such systems are still limited, due to the complexity of practical environments. Various pioneering approaches have been proposed to solve this problem, among which the model-based approach is attracting more and more attention, due to the advantage that it does not require a huge dataset for model training. Existing models are usually developed for Line-of-Sight (LoS) scenarios, and can not be applied to facilitating the design of wireless sensing systems in Non-Line-of-Sight (NLoS) scenarios (e.g., through-wall sensing). To fill this gap, we propose a through-wall wireless sensing model, aiming to characterize the propagation laws and sensing mechanisms of Wi-Fi signals in through-wall scenarios. Specifically, based on the insight that Wi-Fi signals will be refracted while there is a wall between the transceivers, we develop a refraction-aware Fresnel model, and prove theoretically that the original Fresnel model can be seen as a special case of the proposed model. We find that the presence of a wall will change the distribution of Fresnel zones, which we called the \"squeeze effect\" of Fresnel zones. Moreover, our theoretical analysis indicates that the \"squeeze effect\" can help improve the sensing capability (i.e., spatial resolution) of Wi-Fi signals. To validate the proposed model, we implement a through-wall respiration sensing system with a pair of transceivers. Extensive experiments in typical through-wall environments show that the respiration detection error is lower than 0.5 bpm, while the subject's vertical distance to the connection line of the transceivers is less than 200 cm. To the best of our knowledge, this is the first theoretical model that reveals the Wi-Fi based wireless sensing mechanism in through-wall scenarios.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569494"}, {"primary_key": "1476075", "vector": [], "sparse_vector": [], "title": "Helping Users Debug Trigger-Action Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> U<PERSON>", "<PERSON>"], "summary": "Trigger-action programming (TAP) empowers a wide array of users to automate Internet of Things (IoT) devices. However, it can be challenging for users to create completely correct trigger-action programs (TAPs) on the first try, necessitating debugging. While TAP has received substantial research attention, TAP debugging has not. In this paper, we present the first empirical study of users' end-to-end TAP debugging process, focusing on obstacles users face in debugging TAPs and how well users ultimately fix incorrect automations. To enable this study, we added TAP capabilities to an existing 3-D smart home simulator. Thirty remote participants spent a total of 84 hours debugging TAPs using this simulator. Without additional support, participants were often unable to fix buggy TAPs due to a series of obstacles we document. However, we also found that two novel tools we developed helped participants overcome many of these obstacles and more successfully debug TAPs. These tools collect either implicit or explicit feedback from users about automations that should or should not have happened in the past, using a SAT-solving-based algorithm we developed to automatically modify the TAPs to account for this feedback.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569506"}, {"primary_key": "1476076", "vector": [], "sparse_vector": [], "title": "AmbiEar: mmWave Based Voice Recognition in NLoS Scenarios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Millimeter wave (mmWave) based sensing is a significant technique that enables innovative smart applications, e.g., voice recognition. The existing works in this area require direct sensing of the human's near-throat region and consequently have limited applicability in non-line-of-sight (NLoS) scenarios. This paper proposes AmbiEar, the first mmWave based voice recognition approach applicable in NLoS scenarios. AmbiEar is based on the insight that the human's voice causes correlated vibrations of the surrounding objects, regardless of the human's position and posture. Therefore, AmbiEar regards the surrounding objects as ears that can perceive sound and realizes indirect sensing of the human's voice by sensing the vibration of the surrounding objects. By incorporating the designs like common component extraction, signal superimposition, and encoder-decoder network, AmbiEar tackles the challenges induced by low-SNR and distorted signals. We implement AmbiEar on a commercial mmWave radar and evaluate its performance under different settings. The experimental results show that AmbiEar has a word recognition accuracy of 87.21% in NLoS scenarios and reduces the recognition error by 35.1%, compared to the direct sensing approach.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3550320"}, {"primary_key": "1476077", "vector": [], "sparse_vector": [], "title": "Do Smart Glasses Dream of Sentimental Visions?: Deep Emotionship Analysis for Eyewear Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "Mingzhi Dong", "Qin Lv", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emotion recognition in smart eyewear devices is highly valuable but challenging. One key limitation of previous works is that the expression-related information like facial or eye images is considered as the only emotional evidence. However, emotional status is not isolated; it is tightly associated with people's visual perceptions, especially those sentimental ones. However, little work has examined such associations to better illustrate the cause of different emotions. In this paper, we study the emotionship analysis problem in eyewear systems, an ambitious task that requires not only classifying the user's emotions but also semantically understanding the potential cause of such emotions. To this end, we devise EMOShip, a deep-learning-based eyewear system that can automatically detect the wearer's emotional status and simultaneously analyze its associations with semantic-level visual perceptions. Experimental studies with 20 participants demonstrate that, thanks to the emotionship awareness, EMOShip not only achieves superior emotion recognition accuracy over existing methods (80.2% vs. 69.4%), but also provides a valuable understanding of the cause of emotions. Pilot studies with 20 participants further motivate the potential use of EMOShip to empower emotion-aware applications, such as emotionship self-reflection and emotionship life-logging.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517250"}, {"primary_key": "1476078", "vector": [], "sparse_vector": [], "title": "Unveiling Causal Attention in Dogs&apos; Eyes with Smart Eyewear.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Wen<PERSON>o Pan", "<PERSON><PERSON> Wang", "Mingzhi Dong", "<PERSON>", "Qin Lv", "<PERSON>", "Dongsheng Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Our goals are to better understand dog cognition, and to support others who share this interest. Existing investigation methods predominantly rely on human-manipulated experiments to examine dogs' behavioral responses to visual stimuli such as human gestures. As a result, existing experimental paradigms are usually constrained to in-lab environments and may not reveal the dog's responses to real-world visual scenes. Moreover, visual signals pertaining to dog behavioral responses are empirically derived from observational evidence, which can be prone to subjective bias and may lead to controversies. We aim to overcome or reduce the existing limitations of dog cognition studies by investigating a challenging issue: identifying the visual signal(s) from dog eye motion that can be utilized to infer causal explanations of its behaviors, namely estimating causal attention. To this end, we design a deep learning framework named Causal AtteNtIon NEtwork (CANINE) to unveil the dogs' causal attention mechanism, inspired by the recent advance in causality analysis with deep learning. Equipped with CANINE, we developed the first eyewear device to enable inference on the vision-related behavioral causality of canine wearers. We demonstrate the technical feasibility of the proposed CANINE glasses through their application in multiple representative experimental scenarios of dog cognitive study. Various in-field trials are also performed to demonstrate the generality of the CANINE eyewear in real-world scenarios. With the proposed CANINE glasses, we collect the first large-scale dataset, named DogsView, which consists of automatically generated annotations on the canine wearer's causal attention across a wide range of representative scenarios. The DogsView dataset is available online to facilitate research.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569490"}, {"primary_key": "1476081", "vector": [], "sparse_vector": [], "title": "Efficient Adaptive Beacon Deployment Optimization for Indoor Crowd Monitoring Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kota Tsubouchi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The indoor crowd density monitoring system using BLE beacons is one of the effective ways to prevent overcrowded indoor situations. The indoor crowd density monitoring system consists of a mobile application at the user's side and the beacon sensor network as the infrastructure. Since the performance of crowd density monitoring highly depends on how BLE beacons are placed, BLE beacon placement optimization is fundamental research work. This research proposes a beacon deployment method EABeD to incrementally place the beacons adaptively to the latest signal propagation status. Also, EABeD reduces most walking and measurement labor costs by applying Bayesian optimization and the walking distance optimization algorithm. We conducted the placement optimization experiment in the wild environment and compared the results with placements derived by the simulation-based method and people. The result shows that our proposed method can achieve 26.4% higher detection coverage than the simulation-based approach, 23.2% and 5.2% higher detection coverage than the inexperienced person's solution and the expert's solution. As for the labor cost reduction, our proposed method can reduce 90.2% of the walking distance and 74.4% of the optimization time compared with optimization by the dense data gathering method.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569462"}, {"primary_key": "1476082", "vector": [], "sparse_vector": [], "title": "WiAdv: Practical and Robust Adversarial Attack against WiFi-based Gesture Recognition System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "WiFi-based gesture recognition systems have attracted enormous interest owing to the non-intrusive of WiFi signals and the wide adoption of WiFi for communication. Despite boosted performance via integrating advanced deep neural network (DNN) classifiers, there lacks sufficient investigation on their security vulnerabilities, which are rooted in the open nature of the wireless medium and the inherent defects (e.g., adversarial attacks) of classifiers. To fill this gap, we aim to study adversarial attacks to DNN-powered WiFi-based gesture recognition to encourage proper countermeasures. We design WiAdv to construct physically realizable adversarial examples to fool these systems. WiAdv features a signal synthesis scheme to craft adversarial signals with desired motion features based on the fundamental principle of WiFi-based gesture recognition, and a black-box attack scheme to handle the inconsistency between the perturbation space and the input space of the classifier caused by the in-between non-differentiable processing modules. We realize and evaluate our attack strategies against a representative state-of-the-art system, Widar3.0 in realistic settings. The experimental results show that the adversarial wireless signals generated by WiAdv achieve over 70% attack success rate on average, and remain robust and effective across different physical settings. Our attack case study and analysis reveal the vulnerability of WiFi-based gesture recognition systems, and we hope WiAdv could help promote the improvement of the relevant systems.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534618"}, {"primary_key": "1476084", "vector": [], "sparse_vector": [], "title": "GPS-assisted Indoor Pedestrian Dead Reckoning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Indoor pedestrian dead reckoning (PDR) using embedded inertial sensors in smartphones has been actively studied in the ubicomp community. However, PDR relying only on inertial sensors suffers from the accumulation of errors from the sensors. Researchers have employed various indoor landmarks detectable by smartphone sensors such as magnetic fingerprints caused by elevators and Bluetooth signals from beacons with known coordinates to compensate for the errors. This study proposes a new type of indoor landmark that does not require additional device installation, e.g., beacons, and training data collection in a target environment, e.g., magnetic fingerprints, unlike existing landmarks. This study proposes the use of GPS signals received by a smartphone to correct the accumulated errors of the PDR. While it is impossible to locate the smartphone indoors using GPS satellites, the smartphone can receive signals at a window-side area through windows from satellites aligned with the orientation of the window normal. Based on this idea, we design a machine-learning-based module for detecting the proximity of a user to a window and the orientation of the window, which enables us to roughly determine the absolute coordinates of the smartphone and to correct the accumulated errors by referring to positions of window-side areas found in the floor plan of the environment. A key technical contribution of this study is designing the module, such that it can be trained based on data from environments other than the target environment yet work in any environment by extracting GPS-related information independent of wall orientation. We evaluated the effectiveness of the proposed method using sensor data collected in real environments.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3569467"}, {"primary_key": "1476085", "vector": [], "sparse_vector": [], "title": "Quali-Mat: Evaluating the Quality of Execution in Body-Weight Exercises with a Pressure Sensitive Sports Mat.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While sports activity recognition is a well studied subject in mobile, wearable and ubiquitous computing, work to date mostly focuses on recognition and counting of specific exercise types. Quality assessment is a much more difficult problem with significantly less published results. In this work, we present Quali-Mat: a method for evaluating the quality of execution (QoE) in exercises using a smart sports mat that can measure the dynamic pressure profiles during full-body, body-weight exercises. As an example, our system not only recognizes that the user is doing push-ups, but also distinguishes 5 subtly different types of push-ups, each of which (according to sports science literature and professional trainers) has a different effect on different muscle groups. We have investigated various machine learning algorithms targeting the specific type of spatio-temporal data produced by the pressure mat system. We demonstrate that computationally efficient, yet effective Conv3D model outperforms more complex state-of-the-art options such as transfer learning from the image domain. The approach is validated through an experiment designed to cover 47 quantifiable variants of 9 basic exercises with 12 participants. Overall, the model can categorize 9 exercises with 98.6% accuracy / 98.6% F1 score, and 47 QoE variants with 67.3% accuracy / 68.1% F1 score. Through extensive discussions with both the experiment results and practical sports considerations, our approach can be used for not only precisely recognizing the type of exercises, but also quantifying the workout quality of execution on a fine time granularity. We also make the Quali-Mat data set available to the community to encourage further research in the area.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3534610"}, {"primary_key": "1476087", "vector": [], "sparse_vector": [], "title": "EyeQoE: A Novel QoE Assessment Model for 360-degree Videos Using Ocular Behaviors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dongqing Ye", "<PERSON>"], "summary": "As virtual reality (VR) offers an unprecedented experience than any existing multimedia technologies, VR videos, or called 360-degree videos, have attracted considerable attention from academia and industry. How to quantify and model end users' perceived quality in watching 360-degree videos, or called QoE, resides the center for high-quality provisioning of these multimedia services. In this work, we present EyeQoE, a novel QoE assessment model for 360-degree videos using ocular behaviors. Unlike prior approaches, which mostly rely on objective factors, EyeQoE leverages the new ocular sensing modality to comprehensively capture both subjective and objective impact factors for QoE modeling. We propose a novel method that models eye-based cues into graphs and develop a GCN-based classifier to produce QoE assessment by extracting intrinsic features from graph-structured data. We further exploit the Siamese network to eliminate the impact from subjects and visual stimuli heterogeneity. A domain adaptation scheme named MADA is also devised to generalize our model to a vast range of unseen 360-degree videos. Extensive tests are carried out with our collected dataset. Results show that EyeQoE achieves the best prediction accuracy at 92.9%, which outperforms state-of-the-art approaches. As another contribution of this work, we have publicized our dataset on https://github.com/MobiSec-CSE-UTA/EyeQoE_Dataset.git.", "published": "2022-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3517240"}]