[{"primary_key": "1657050", "vector": [], "sparse_vector": [], "title": "Distributed (Correlation) Samplers: How to Remove a Trusted Dealer in One Round.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Structured random strings (SRSs) and correlated randomness are important for many cryptographic protocols. In settings where interaction is expensive, it is desirable to obtain such randomness in as few rounds of communication as possible; ideally, simply by exchanging one reusable round of messages which can be considered public keys. In this paper, we describe how to generate any SRS or correlated randomness in such a single round of communication, using, among other things, indistinguishability obfuscation. We introduce what we call adistributed sampler, which enables\\(n\\)parties to sample a single public value (SRS) from any distribution. We construct a semi-malicious distributed sampler in the plain model, and use it to build a semi-maliciouspublic-key PCF(Boyleet al., FOCS 2020) in the plain model. A public-key PCF can be thought of as a distributedcorrelationsampler; instead of producing a public SRS, it gives each party a private random value (where the values satisfy some correlation). We introduce a general technique called ananti-rusherwhich compiles any one-round protocol with semi-malicious security without inputs to a similar one-round protocol with active security by making use of a programmable random oracle. This gets us actively secure distributed samplers and public-key PCFs in the random oracle model. Finally, we explore some tradeoffs. Our first PCF construction is limited toreverse-sampleablecorrelations (where the random outputs of honest parties must be simulatable given the random outputs of corrupt parties); we additionally show a different construction without this limitation, but which does not allow parties to hold secret parameters of the correlation. We also describe how to avoid the use of a random oracle at the cost of relying on sub-exponentially secure indistinguishability obfuscation.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_27"}, {"primary_key": "1657051", "vector": [], "sparse_vector": [], "title": "Secure Non-interactive Reduction and Spectral Analysis of Correlations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Correlated pairs of random variables are a central concept in information-theoretically secure cryptography. Secure reductions between different correlations have been studied, and completeness results are known. Further, the complexity of such reductions is intimately connected with circuit complexity and efficiency of locally decodable codes. As such, making progress on these complexity questions faces strong barriers. Motivated by this, in this work, we study a restricted form of secure reductions—namely, SecureNon-InteractiveReductions (SNIR)—which is still closely related to the original problem, and establish several fundamental results and relevant techniques for it. We uncover striking connections between SNIR and linear algebraic properties of correlations. Specifically, we define the spectrum of a correlation, and show that a target correlation has a SNIR to a source correlation only if the spectrum of the latter contains the entire spectrum of the former. We also establish a “mirroring lemma” that shows an unexpected symmetry between the two parties in a SNIR, when viewed through the lens of spectral analysis. We also use cryptographic insights and elementary linear algebraic analysis to fully characterize the role of common randomness as well as local randomness in SNIRs. We employ these results to resolve several fundamental questions about SNIRs, and to define future directions.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_28"}, {"primary_key": "1657052", "vector": [], "sparse_vector": [], "title": "Post-Quantum Security of the Even-Mansour Cipher.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Even-Mansour cipher is a simple method for constructing a (keyed) pseudorandom permutationEfrom a public random permutation\\(P:\\{0,1\\}^n \\rightarrow \\{0,1\\}^n\\). It is secure against classical attacks, with optimal attacks requiring\\(q_E\\)queries toEand\\(q_P\\)queries toPsuch that\\(q_E \\cdot q_P \\approx 2^n\\). If the attacker is givenquantumaccess to bothEandP, however, the cipher is completely insecure, with attacks using\\(q_E, q_P = O(n)\\)queries known. In any plausible real-world setting, however, a quantum attacker would have onlyclassicalaccess to the keyed permutationEimplemented by honest parties, while retaining quantum access toP. Attacks in this setting with\\(q_E \\cdot q_P^2 \\approx 2^n\\)are known, showing that security degrades as compared to the purely classical case, but leaving open the question as to whether the Even-Mansour cipher can still be proven secure in that natural, “post-quantum” setting. We resolve this question, showing that any attack in that setting requires\\(q_E \\cdot q^2_P + q_P \\cdot q_E^2 \\approx 2^n\\). Our results apply to both the two-key and single-key variants of Even-Mansour. Along the way, we establish several generalizations of results from prior work on quantum-query lower bounds that may be of independent interest.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_17"}, {"primary_key": "1657053", "vector": [], "sparse_vector": [], "title": "Unclonable Polymers and Their Cryptographic Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Itsik Pe&apos;er", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a mechanism for generating and manipulating protein polymers to obtain a new type ofconsumable storagethat exhibits intriguing cryptographic “self-destruct” properties, assuming the hardness of certain polymer-sequencing problems. To demonstrate the cryptographic potential of this technology, we first develop a formalism that captures (in a minimalistic way) the functionality and security properties provided by the technology. Next, using this technology, we construct and prove security of two cryptographic applications that are currently obtainable only via trusted hardware that implements logical circuitry (either classical or quantum). The first application is a password-controlledsecure vaultwhere the stored data is irrecoverably erased once a threshold of unsuccessful access attempts is reached. The second is (a somewhat relaxed version of)one time programs, namely a device that allows evaluating a secret function only a limited number of times before self-destructing, where each evaluation is made on a fresh user-chosen input. Finally, while our constructions, modeling, and analysis are designed to capture the proposed polymer-based technology, they are sufficiently general to be of potential independent interest.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_26"}, {"primary_key": "1657054", "vector": [], "sparse_vector": [], "title": "CoCoA: Concurrent Continuous Group Key Agreement.", "authors": ["<PERSON><PERSON>", "Benedik<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Messaging platforms like Signal are widely deployed and provide strong security in an asynchronous setting. It is a challenging problem to construct a protocol with similar security guarantees that canefficientlyscale to large groups. A major bottleneck are the frequent key rotations users need to perform to achieve post compromise forward security. In current proposals – most notably in TreeKEM (which is part of the IETF’s Messaging Layer Security (MLS) protocol draft) – for users in a group of sizento rotate their keys, they must each craft a message of size\\(\\log (n)\\)to be broadcast to the group using an (untrusted) delivery server. In larger groups, having users sequentially rotate their keys requires too much bandwidth (or takes too long), so variants allowing any\\(T \\le n\\)users to simultaneously rotate their keys in just 2 communication rounds have been suggested (e.g. “Propose and Commit” by MLS). Unfortunately, 2-round concurrent updates are either damaging or expensive (or both); i.e. they either result in future operations being more costly (e.g. via “blanking” or “tainting”) or are costly themselves requiring\\(\\varOmega (T)\\)communication for each user [<PERSON><PERSON><PERSON> et al., TCC’20]. In this paper we propose CoCoA; a new scheme that allows forTconcurrent updates that are neither damaging nor costly. That is, they add no cost to future operations yet they only require\\(\\varOmega (\\log ^2(n))\\)communication per user. To circumvent the [Biens<PERSON> et al.] lower bound, CoCoA increases the number of rounds needed to complete all updates from 2 up to (at most)\\(\\log (n)\\); though typically fewer rounds are needed. The key insight of our protocol is the following: in the (non-concurrent version of) TreeKEM, a delivery server which getsTconcurrent update requests will approve one and reject the remaining\\(T-1\\). In contrast, our server attempts to apply all of them. If more than one user requests to rotate the same key during a round, the server arbitrarily picks a winner. Surprisingly, we prove that regardless of how the server chooses the winners, all previously compromised users will recover after at most\\(\\log (n)\\)such update rounds. To keep the communication complexity low, CoCoA is a server-aided CGKA. That is, the delivery server no longer blindly forwards packets, but instead actively computes individualized packets tailored to each user. As the server is untrusted, this change requires us to develop new mechanisms ensuring robustness of the protocol.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_28"}, {"primary_key": "1657055", "vector": [], "sparse_vector": [], "title": "Refined Cryptanalysis of the GPRS Ciphers GEA-1 and GEA-2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At EUROCRYPT 2021, <PERSON><PERSON><PERSON> et al. presented the first public analysis of the GPRS ciphers GEA-1 and GEA-2. They showed that although GEA-1 uses a 64-bit session key, it can be recovered with the knowledge of only 65 bits of keystream in time\\(2^{40}\\)using 44 GiB of memory. The attack exploits a weakness in the initialization process of the cipher that was presumably hidden intentionally by the designers to reduce its security. While no such weakness was found for GEA-2, the authors presented an attack on this cipher with time complexity of about\\(2^{45}\\). The main practical obstacle is the required knowledge of 12800 bits of keystream used to encrypt a full GPRS frame. Variants of the attack are applicable (but more expensive) when given less consecutive keystream bits, or when the available keystream is fragmented (it contains no long consecutive block). In this paper, we improve and complement the previous analysis of GEA-1 and GEA-2. For GEA-1, we devise an attack in which the memory complexity is reduced by a factor of about\\(2^{13} = 8192\\)from 44 GiB to about 4 MiB, while the time complexity remains\\(2^{40}\\). Our implementation recovers the GEA-1 session key in average time of 2.5 h on a modern laptop. For GEA-2, we describe two attacks that complement the analysis of <PERSON><PERSON><PERSON> et al. The first attack obtains a linear tradeoff between the number of consecutive keystream bits available to the attacker (denoted by\\(\\ell \\)) and the time complexity. It improves upon the previous attack in the range of (roughly)\\(\\ell \\le 7000\\). Specifically, for\\(\\ell = 1100\\)the complexity of our attack is about\\(2^{54}\\), while the previous one is not faster than the\\(2^{64}\\)brute force complexity. In case the available keystream is fragmented, our second attack reduces the memory complexity of the previous attack by a factor of 512 from 32 GiB to 64 MiB with no time complexity penalty. Our attacks are based on new combinations of stream cipher cryptanalytic techniques and algorithmic techniques used in other contexts (such as solving thek-XOR problem).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_3"}, {"primary_key": "1657056", "vector": [], "sparse_vector": [], "title": "A PCP Theorem for Interactive Proofs and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "E<PERSON>"], "summary": "The celebrated PCP Theorem states that any language in\\(\\mathrm {NP}\\)can be decided via a verifier that readsO(1) bits from a polynomially long proof. Interactive oracle proofs (IOP), a generalization of PCPs, allow the verifier to interact with the prover for multiple rounds while reading a small number of bits from each prover message. While PCPs are relatively well understood, the power captured by IOPs (beyond\\(\\mathrm {NP}\\)) has yet to be fully explored. We present a generalization of the PCP theorem for interactive languages. We show that any language decidable by ak(n)-round IP has ak(n)-round public-coin IOP, where the verifier makes its decision by reading onlyO(1) bits from each (polynomially long) prover message andO(1) bits from each of its own (random) messages to the prover. Our result and the underlying techniques have several applications. We get a new hardness of approximation result for a stochastic satisfiability problem, we show IOP-to-IOP transformations that previously were known to hold only for IPs, and we formulate a new notion of PCPs (index-decodable PCPs) that enables us to obtain a commit-and-prove SNARK in the random oracle model for nondeterministic computations.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_3"}, {"primary_key": "1657057", "vector": [], "sparse_vector": [], "title": "Field Instruction Multiple Data.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Fully homomorphic encryption (FHE) has flourished since it was first constructed by <PERSON><PERSON> (STOC 2009). Single instruction multiple data (SIMD) gave rise to efficient homomorphic operations on vectors in\\((\\mathbb {F}_{t^d})^\\ell \\), for prime\\(t\\). RLWE instantiated with cyclotomic polynomials of the form\\(X^{2^N}+1\\)dominate implementations of FHE due to highly efficient fast Fourier transformations. However, this choice yields very short SIMD plaintext vectors and high degree extension fields, e.g.\\(\\ell < 100, d > 100\\)for small primes (\\(t = 3, 5, \\dots \\)). In this work, we describe a method to encode more data on top of SIMD,Field Instruction Multiple Data, applying reverse multiplication friendly embedding (RMFE) to FHE. With RMFE, length-\\(k\\)\\(\\mathbb {F}_{t}\\)vectors can be encoded into\\(\\mathbb {F}_{t^d}\\)and multiplied once. The results have to be recoded (decoded and then re-encoded) before further multiplications can be done. We introduce an FHE-specific technique to additionally evaluate arbitrary linear transformations on encoded vectors for free during the FHE recode operation. On top of that, we present two optimizations to unlock high degree extension fields with small\\(t\\)for homomorphic computation:\\(r\\)-fold RMFE, which allows products of up to\\(2^r\\)encoded vectors before recoding, and a three-stage recode process for RMFEs obtained by composing two smaller RMFEs. Experiments were performed to evaluate the effectiveness of FIMD from various RMFEs compared to standard SIMD operations. Overall, we found that FIMD generally had\\(>2{\\times }\\)better (amortized) multiplication times compared to FHE for the same amount of data, while using almost\\(k/2{\\times }\\)fewer ciphertexts required.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_21"}, {"primary_key": "1657058", "vector": [], "sparse_vector": [], "title": "Adaptively Secure Computation for RAM Programs.", "authors": ["Laasya Bangalore", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we study the communication complexity of secure multiparty computation under minimal assumptions in the presence of an adversary that can adaptively corrupt all parties eventually. Specifically, we are interested in understanding the complexity when modeling the underlying function as a RAM program. Under minimal assumptions, the work of <PERSON><PERSON> et al. (STOC 2017) and <PERSON><PERSON><PERSON><PERSON> et al. (TCC 2018) give protocols whose communication complexity grows quadratically in the circuit size when the computation is expressed as a Boolean circuit. In this work, we obtain the first two-round two-party computation protocol, which is secure against adaptive adversaries who can adaptively corrupt all parties where the communication complexity is proportional to the square of the RAM complexity of the function up to polylogarithmic factors assuming the existence of non-committing encryption.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_7"}, {"primary_key": "1657059", "vector": [], "sparse_vector": [], "title": "Efficient Schemes for Committing Authenticated Encryption.", "authors": ["<PERSON><PERSON>", "Viet Tung Hoang"], "summary": "This paper provides efficient authenticated-encryption (AE) schemes in which a ciphertext is a commitment to the key. These are extended, at minimal additional cost, to schemes where the ciphertext is a commitment to all encryption inputs, meaning key, nonce, associated data and message. Our primary schemes are modifications of GCM (for basic, unique-nonce AE security) and AES-GCM-SIV (for misuse-resistant AE security) and add both forms of commitment without any increase in ciphertext size. We also give more generic, but somewhat more costly, solutions.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_29"}, {"primary_key": "1657060", "vector": [], "sparse_vector": [], "title": "Group Signatures and More from Isogenies and Lattices: Generic, Simple, and Efficient.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct an efficient dynamic group signature (or more generally an accountable ring signature) from isogeny and lattice assumptions. Our group signature is based on a simple generic construction that can be instantiated by cryptographically hard group actions such as the CSIDH group action or an MLWE-based group action. The signature is of size\\(O(\\log N)\\), whereNis the number of users in the group. Our idea builds on the recent efficient OR-proof by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Asiacrypt’20), where we efficiently add a proof of valid ciphertext to their OR-proof and further show that the resulting non-interactive zero-knowledge proof system isonline extractable. Our group signatures satisfy more ideal security properties compared to previously known constructions, while simultaneously having an attractive signature size. The signature size of our isogeny-based construction is an order of magnitude smaller than all previously known post-quantum group signatures (e.g., 6.6 KB for 64 members). In comparison, our lattice-based construction has a larger signature size (e.g., either 126 KB or 89 KB for 64 members depending on the satisfied security property). However, since the\\(O(\\cdot )\\)-notation hides a very small constant factor, it remains small even for very large group sizes, say\\(2^{20}\\).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_4"}, {"primary_key": "1657061", "vector": [], "sparse_vector": [], "title": "Non-malleable Commitments Against Quantum Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct, under standard hardness assumptions, the first non-malleable commitments secure against quantum attacks. Our commitments are statistically binding and satisfy the standard notion ofnon-malleability with respect to commitment. We obtain a\\(\\log ^\\star (\\lambda )\\)-round classical protocol, assuming the existence of post-quantum one-way functions. Previously, non-malleable commitments with quantum security were only known against a restricted class of adversaries known assynchronizing adversaries.At the heart of our results is a new general technique that allows to modularly obtain non-malleable commitments from any extractable commitment protocol, obliviously of the underlying extraction strategy (black-box or non-black-box) or round complexity. The transformation may also be of interest in the classical setting.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_19"}, {"primary_key": "1657062", "vector": [], "sparse_vector": [], "title": "On the Multi-user Security of Short Schnorr Signatures with Preprocessing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Schnorr signature scheme is an efficient digital signature scheme with short signature lengths, i.e., 4k-bit signatures forkbits of security. A Schnorr signature\\(\\sigma \\)over a group of size\\(p\\approx 2^{2k}\\)consists of a tuple (s,e), where\\(e \\in \\{0,1\\}^{2k}\\)is a hash output and\\(s\\in \\mathbb {Z}_p\\)must be computed using the secret key. While the hash outputerequires 2kbits to encode, <PERSON><PERSON><PERSON><PERSON> proposed that it might be possible to truncate the hash value without adversely impacting security. In this paper, we prove thatshortSchnorr signatures of length 3kbits providekbits of multi-user security in the (Shoup’s) generic group model and the programmable random oracle model. We further analyze the multi-user security of key-prefixed short Schnorr signatures against preprocessing attacks, showing that it is possible to obtain secure signatures of length\\(3k + \\log S + \\log N\\)bits. Here,Ndenotes the number of users andSdenotes the size of the hint generated by our preprocessing attacker, e.g., if\\(S=2^{k/2}\\), then we would obtain secure 3.75k-bit signatures for groups of up to\\(N \\le 2^{k/4}\\)users. Our techniques easily generalize to several other Fiat-Shamir-based signature schemes, allowing us to establish analogous results for Cha<PERSON><PERSON><PERSON> signatures and <PERSON>-<PERSON> signatures. As a building block, we also analyze the 1-out-of-Ndiscrete-log problem in the generic group model, with and without preprocessing.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_21"}, {"primary_key": "1657063", "vector": [], "sparse_vector": [], "title": "Beyond Quadratic Speedups in Quantum Attacks on Symmetric Schemes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we report the first quantum key-recovery attack on a symmetric block cipher design, using classical queries only, with a more than quadratic time speedup compared to the best classical attack. We study the 2XOR-Cascade construction of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT 2012). It is a key length extension technique which provides ann-bit block cipher with\\(\\frac{5n}{2}\\)bits of security out of ann-bit block cipher with 2nbits of key, with a security proof in the ideal model. We show that the offline-Simon algorithm of <PERSON><PERSON><PERSON> et al. (ASIACRYPT 2019) can be extended to, in particular, attack this construction in quantum time\\(\\widetilde{\\mathcal {O}}\\left( 2^n \\right) \\), providing a 2.5 quantum speedup over the best classical attack. Regarding post-quantum security of symmetric ciphers, it is commonly assumed that doubling the key sizes is a sufficient precaution. This is because <PERSON><PERSON>’s quantum search algorithm, and its derivatives, can only reach a quadratic speedup at most. Our attack shows that the structure of some symmetric constructions can be exploited to overcome this limit. In particular, the 2XOR-Cascade cannot be used to generically strengthen block ciphers against quantum adversaries, as it would offer only the same security as the block cipher itself.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_12"}, {"primary_key": "1657064", "vector": [], "sparse_vector": [], "title": "Gemini: Elastic SNARKs for Diverse Environments.", "authors": ["<PERSON>", "<PERSON>", "Yuncong Hu", "<PERSON>"], "summary": "We introduce a new class of succinct arguments, that we call elastic. Elastic SNARKs allow the prover to allocate different resources (such as memory and time) depending on the execution environment and the statement to prove. The resulting output is independent of the prover’s configuration. To study elastic SNARKs, we extend the streaming paradigm of [<PERSON> et al., TCC’20]. We provide a definitional framework for elastic polynomial interactive oracle proofs for R1CS instances and design a compiler which transforms an elastic PIOP into a preprocessing argument system that supports streaming or random access to its inputs. Depending on the configuration, the prover will choose different trade-offs for time (either linear, or quasilinear) and memory (either linear, or logarithmic). We prove the existence of elastic SNARKS by presenting Gemini, a novel FFT-free preprocessing argument. We prove its security and develop a proof-of-concept implementation in Rust based on the arkworks framework. We provide benchmarks for large R1CS instances of tens of billions of gates on a single machine.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_15"}, {"primary_key": "1657065", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge IOPs with Linear-Time Prover and Polylogarithmic-Time Verifier.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Interactive oracle proofs (IOPs) are a multi-round generalization of probabilistically checkable proofs that play a fundamental role in the construction of efficient cryptographic proofs. We present an IOP that simultaneously achieves the properties of zero knowledge, linear-time proving, and polylogarithmic-time verification. We construct a zero-knowledge IOP where, for the satisfiability of anN-gate arithmetic circuit over any field of size\\(\\varOmega (N)\\), the prover usesO(N) field operations and the verifier uses\\({\\mathsf {polylog}}(N)\\)field operations (with proof lengthO(N) and query complexity\\({\\mathsf {polylog}}(N)\\)). Polylogarithmic verification is achieved in the holographic setting for every circuit (the verifier has oracle access to a linear-time-computable encoding of the circuit whose satisfiability is being proved). Our result implies progress on a basic goal in the area of efficient zero knowledge. Via a known transformation, we obtain a zero knowledge argument system where the prover runs in linear time and the verifier runs in polylogarithmic time; the construction is plausibly post-quantum and only makes a black-box use of lightweight cryptography (collision-resistant hash functions).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_10"}, {"primary_key": "1657066", "vector": [], "sparse_vector": [], "title": "Secure Multiparty Computation with Sublinear Preprocessing.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A common technique for enhancing the efficiency of \nsecure multiparty computation (MPC) with dishonest majority is viapreprocessing: In an offline phase, parties engage in an input-independent protocol to securely generate correlated randomness. Once inputs are known, the correlated randomness is consumed by a “non-cryptographic” and highly efficient online protocol. The correlated randomness in such protocols traditionally comes in two flavors: multiplication triples (<PERSON>, Crypto ’91), which suffice for security against semi-honest parties, andauthenticatedmultiplication triples (<PERSON><PERSON> et al., Eurocrypt ’11, <PERSON><PERSON><PERSON><PERSON> et al., Crypto ’12) that yield efficient protocols against malicious parties. Recent constructions of pseudorandom correlation generators (<PERSON> et al., Crypto ’19, ’20) enable concretely efficient secure generation of multiplication triples withsublinear communication complexity. However, these techniques do not efficiently apply to authenticated triples, except in the case of secure two-party computation of arithmetic circuits over large fields. In this work, we propose the firstconcretely efficientapproach for (malicious) MPC with preprocessing in which the offline communication issublinearin the circuit size. More specifically, the offline communication scales with thesquare rootof the circuit size. From a feasibility point of view, our protocols can make use of any secure protocol for generating (unauthenticated) multiplication triples together with anyadditivehomomorphic encryption. We propose concretely efficient instantiations (based on strong but plausible “linear-only” assumptions) from existing homomorphic encryption schemes and pseudorandom correlation generators. Our technique is based on a variant of a recent protocol of <PERSON> et al. (Crypto ’21) for MPC with preprocessing. As a result, our protocols inherit the succinct correlated randomness feature of the latter protocol.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_15"}, {"primary_key": "1657067", "vector": [], "sparse_vector": [], "title": "Batch-OT with Optimal Rate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Pu"], "summary": "We show that it is possible to performnindependent copies of 1-out-of-2 oblivious transfer in two messages, where the communication complexity of the receiver and sender (each) is\\(n(1+o(1))\\)for sufficiently largen. Note that this matches the information-theoretic lower bound. Prior to this work, this was only achievable by using the heavy machinery of rate-1 fully homomorphic encryption (Rate-1 FHE, <PERSON><PERSON><PERSON><PERSON> et al., TCC 2019). To achieve rate-1 both on the receiver’s and sender’s end, we use the LPN assumption, with slightly sub-constant noise rate\\(1/m^{\\epsilon }\\)for any\\(\\epsilon >0\\)together with either the DDH, QR or LWE assumptions. In terms of efficiency, our protocols only rely on linear homomorphism, as opposed to the FHE-based solution which inherently requires an expensive “bootstrapping” operation. We believe that in terms of efficiency we compare favorably to existing batch-OT protocols, while achieving superior communication complexity. We show similar results for Oblivious Linear Evaluation (OLE). For our DDH-based solution we develop a new technique that may be of independent interest. We show that it is possible to “emulate” the binary group\\(\\mathbb {Z}_2\\)(or any other small-order group) inside a prime-order group\\(\\mathbb {Z}_p\\)in a function-private manner. That is,\\(\\mathbb {Z}_2\\)operations are mapped to\\(\\mathbb {Z}_p\\)operations such that the outcome of the latter do not reveal additional information beyond the\\(\\mathbb {Z}_2\\)outcome. Our encoding technique uses the discrete Gaussian distribution, which to our knowledge was not done before in the context of DDH.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_6"}, {"primary_key": "1657068", "vector": [], "sparse_vector": [], "title": "On Building Fine-Grained One-Way Functions from Strong Average-Case Hardness.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Constructing one-way functions from average-case hardness is a long-standing open problem. A positive result would exclude <PERSON><PERSON><PERSON><PERSON> (<PERSON>mpagliazzo ’95) and establish a highly desirable win-win situation: either (symmetric) cryptography exists unconditionally, or all\\(\\mathsf {NP}\\)problems can be solved efficiently on the average. Motivated by the lack of progress on this seemingly very hard question, we initiate the investigation of weaker yet meaningful candidate win-win results of the following type: either there arefine-grainedone-way functions (FGOWF), or nontrivial speedups can be obtained for all\\(\\mathsf {NP} \\)problems on the average. FGOWFs only require a fixed polynomial gap (as opposed to superpolynomial) between the running time of the function and the running time of an inverter. We obtain three main results: Construction.We show that if there is an\\(\\mathsf {NP} \\)language having a very strong form of average-case hardness, which we callblock finding hardness, then FGOWF exist. We provide heuristic support for this very strong average-case hardness notion by showing that it holds for a random language. Then, we study whether weaker (and more natural) forms of average-case hardness could already suffice to obtain FGOWF, and obtain two negative results: Separation I.We provide a strong oracle separation for the implication (\\(\\exists \\)exponentially average-case hard language\\(\\implies \\)\\(\\exists \\)FGOWF). Separation II.We provide a second strong negative result for an even weaker candidate win-win result. Namely, we rule out a black-box proof for the implication (\\(\\exists \\)exponentially average-case hard languagewhose hardness amplifies optimally through parallel repetitions\\(\\implies \\)\\(\\exists \\)FGOWF). This separation forms the core technical contribution of our work.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_20"}, {"primary_key": "1657069", "vector": [], "sparse_vector": [], "title": "COA-Secure Obfuscation and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We put forth a new paradigm for program obfuscation, where obfuscated programs are endowed with proofs of “well formedness.” In addition to asserting existence of an underlying plaintext program with an attested structure, these proofs also prevent mauling attacks, whereby an adversary surreptitiously creates an obfuscated program based on secrets which are embedded in other obfuscated programs. We call this new guaranteeChosen Obfuscation Attacks (COA) security. We show how to enhance a large class of obfuscation mechanisms to be COA-secure, assuming subexponentially secure IO for circuits and subexponentially secure one-way functions. To demonstrate the power of the new notion, we also use it to realize: A new form of software watermarking, which provides significantly broader protection than current schemes against counterfeits that pass a keyless, public verification process. Completely CCAencryption, which is a strengthening of completely non-malleable encryption.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_25"}, {"primary_key": "1657070", "vector": [], "sparse_vector": [], "title": "Asymptotically Quasi-Optimal Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The question of minimizing thecomputational overheadof cryptography was put forward by the work of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 2008). The main conclusion was that, under plausible assumptions, most cryptographic primitives can be realized withconstantcomputational overhead. However, this ignores an additive term that may depend polynomially on the (concrete) computational security parameter\\(\\lambda \\). In this work, we study the question of obtaining optimal efficiency, up to polylogarithmic factors, forallchoices ofnand\\(\\lambda \\), wherenis the size of the given task. In particular, when\\(n=\\lambda \\), we would like the computational cost to be only\\(\\tilde{O}(\\lambda )\\). We refer to this goal asasymptotically quasi-optimal(AQO) cryptography. We start by realizing the first AQO semi-honest batch oblivious linear evaluation (BOLE) protocol. Our protocol applies to OLE over small fields and relies on the near-exponential security of the ring learning with errors (RLWE) assumption. Building on the above and on known constructions of AQO PCPs, we design the first AQO zero-knowledge (ZK) argument system for Boolean circuit satisfiability. Our construction combines a new AQO ZK-PCP construction that respects the AQO property of the underlying PCP along with a technique for converting statistical secrecy into soundness via OLE reversal. Finally, combining the above results, we get AQO secure computation protocols for Boolean circuits with security against malicious parties under RLWE.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_11"}, {"primary_key": "1657071", "vector": [], "sparse_vector": [], "title": "Lightweight, Maliciously Secure Verifiable Function Secret Sharing.", "authors": ["<PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "In this work, we present a lightweight construction of verifiable two-party function secret sharing (FSS) for point functions and multi-point functions. Our verifiability method is lightweight in two ways. Firstly, it is concretely efficient, making use of only symmetric key operations and no public key or MPC techniques are involved. Our performance is comparable with the state-of-the-artnon-verifiableDPF constructions, and we outperform all prior DPF verification techniques in both computation and communication complexity, which we demonstrate with an implementation of our scheme. Secondly, our verification procedure is essentially unconstrained. It will verify that distributed point function (DPF) shares correspond to some point function irrespective of the output group size, the structure of the DPF output, or the set of points on which the DPF must be evaluated. This is in stark contrast with prior works, which depend on at least one and often all three of these constraints. In addition, our construction is the first DPF verification protocol that can verify general DPFs while remaining secure even if one server is malicious. Prior work on maliciously secure DPF verification could only verify DPFs where the non-zero output is binary and the output space is a large field. As an additional feature, our verification procedure can be batched so that verifying a polynomial number of DPF shares requires theexactsame amount of communication as verifying one pair of DPF shares. We combine this packed DPF verification with a novel method for packing DPFs into shares of a multi-point function where the evaluation time, verification time, and verification communication areindependentof the number of non-zero points in the function. An immediate corollary of our results are two-server protocols for PIR and PSI that remain secure when any one of the three parties is malicious (either the client or one of the servers).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_6"}, {"primary_key": "1657072", "vector": [], "sparse_vector": [], "title": "Universally Composable Subversion-Resilient Cryptography.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Subversion attacks undermine security of cryptographic protocols by replacing a legitimate honest party’s implementation with one that leaks information in an undetectable manner. An important limitation of all currently known techniques for designing cryptographic protocols with security against subversion attacks is that they do not automatically guarantee security in the realistic setting where a protocol session may run concurrently with other protocols. We remedy this situation by providing a foundation ofreverse firewalls(<PERSON><PERSON><PERSON> and <PERSON>, EUROCRYPT’15) in theuniversal composability(UC) framework (Canetti, FOCS’01 and J. ACM’20). More in details, our contributions are threefold: We generalize the UC framework to the setting where each party consists of a core (which has secret inputs and is in charge of generating protocol messages) and a firewall (which has no secrets and sanitizes the outgoing/incoming communication from/to the core). Both the core and the firewall can be subject to different flavors of corruption, modeling different kinds of subversion attacks. For instance, we capture the setting where a subverted core looks like the honest core to any efficient test, yet it may leak secret information via covert channels (which we callspecious subversion). We show how to sanitize UC commitments and UC coin tossing against specious subversion, under the DDH assumption. We show how to sanitize the classical GMW compiler (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, STOC 1987) for turning MPC with security in the presence of semi-honest adversaries into MPC with security in the presence of malicious adversaries. This yields a completeness theorem for maliciously secure MPC in the presence of specious subversion. Additionally, all our sanitized protocols aretransparent, in the sense that communicating with a sanitized core looks indistinguishable from communicating with an honest core. Thanks to the composition theorem, our methodology allows, for the first time, to design subversion-resilient protocols by sanitizing different sub-components in a modular way.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_10"}, {"primary_key": "1657073", "vector": [], "sparse_vector": [], "title": "On Succinct Non-interactive Arguments in Relativized Worlds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Succinct non-interactive arguments of knowledge (SNARKs) are cryptographic proofs with strong efficiency properties. Applications of SNARKs often involve proving computations that include the SNARK verifier, a technique called recursive composition. Unfortunately, SNARKs with desirable features such as a transparent (public-coin) setup are known only in the random oracle model (ROM). In applications this oracle must be heuristically instantiated and used in a non-black-box way. In this paper we identify a natural oracle model, the low-degree random oracle model, in which there exist transparent SNARKs for all NP computationsrelative to this oracle. Informally, letting\\(\\mathcal {O}\\)be a low-degree encoding of a random oracle, and assuming the existence of (standard-model) collision-resistant hash functions, there exist SNARKs relative to\\(\\mathcal {O}\\)for all languages in\\(\\mathsf {NP}^{\\mathcal {O}}\\). Such a SNARK can directly prove a computation about its own verifier. To analyze this model, we introduce a more general framework, thelinear code random oracle model(LCROM). We show how to obtain SNARKs in the LCROM for computations that query the oracle, given anaccumulation schemefor oracle queries. Then we construct such an accumulation scheme for the special case of a low degree random oracle.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_12"}, {"primary_key": "1657074", "vector": [], "sparse_vector": [], "title": "Quantum Algorithms for Variants of Average-Case Lattice Problems via Filtering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show polynomial-time quantum algorithms for the following problems: Short integer solution (SIS) problem under theinfinitynorm, where the public matrix is very wide, the modulus is a polynomially large prime, and the bound of infinity norm is set to be half of the modulus minus a constant. Learning with errors (LWE) problem given LWE-like quantum states with polynomially large moduli and certain error distributions, including bounded uniform distributions and Laplace distributions. Extrapolated dihedral coset problem (EDCP) with certain parameters. The SIS, LWE, and EDCP problems in their standard forms are as hard as solving lattice problems in the worst case. However, the variants that we can solve are not in the parameter regimes known to be as hard as solving worst-case lattice problems. Still, no classical or quantum polynomial-time algorithms were known for the variants of SIS and LWE we consider. For EDCP, our quantum algorithm slightly extends the result of <PERSON><PERSON><PERSON> et al. (2018). Our algorithms for variants of SIS and EDCP use the existing quantum reductions from those problems to LWE, or more precisely, to the problem of solving LWE given LWE-like quantum states. Our main contribution is solving LWE given LWE-like quantum states with interesting parameters using a filtering technique.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_14"}, {"primary_key": "1657075", "vector": [], "sparse_vector": [], "title": "Limits of Polynomial Packings for $\\mathbb {Z}_{pk}$ and $\\mathbb {F}_{pk}$.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We formally define polynomial packing methods and initiate a unified study of related concepts in various contexts of cryptography. This includes homomorphic encryption (HE) packing and reverse multiplication-friendly embedding (RMFE) in information-theoretically secure multi-party computation (MPC). We prove several upper bounds and impossibility results on packing methods for\\(\\mathbb {Z}_{p^k}\\)or\\(\\mathbb {F}_{p^k}\\)-messages into\\(\\mathbb {Z}_{p^t}[x]/f(x)\\)in terms of (i) packing density, (ii) level-consistency, and (iii) surjectivity. These results have implications on recent development of HE-based MPC over\\(\\mathbb {Z}_{2^k}\\)secure against actively corrupted majority and provide new proofs for upper bounds on RMFE.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_18"}, {"primary_key": "1657076", "vector": [], "sparse_vector": [], "title": "Constant-Round Blind Classical Verification of Quantum Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a recent breakthrough, <PERSON><PERSON><PERSON> constructed a classical verification of quantum computation (CVQC) protocol for a classical client to delegate decision problems in\\(\\mathsf {BQP}\\)to an untrusted quantum prover under computational assumptions. In this work, we explore further the feasibility of CVQC with the more generalsamplingproblems in BQP and with the desirableblindnessproperty. We contribute affirmative solutions to both as follows. Motivated by the sampling nature of many quantum applications (e.g., quantum algorithms for machine learning and quantum supremacy tasks), we initiate the study of CVQC forquantum sampling problems(denoted by\\(\\mathsf {SampBQP}\\)). More precisely, in a CVQC protocol for a\\(\\mathsf {SampBQP}\\)problem, the prover and the verifier are given an input\\(x\\in \\{0,1\\}^n\\)and a quantum circuitC, and the goal of the classical client is to learn a sample from the output\\(z \\leftarrow C(x)\\)up to a small error, from its interaction with an untrusted prover. We demonstrate its feasibility by constructing a four-message CVQC protocol for\\(\\mathsf {SampBQP}\\)based on the quantumLearning With Errorsassumption. Theblindnessof CVQC protocols refers to a property of the protocol where the prover learns nothing, and hence is blind, about the client’s input. It is a highly desirable property that has been intensively studied for the delegation of quantum computation. We provide a simple yet powerfulgenericcompiler that transforms any CVQC protocol to a blind one while preserving its completeness and soundness errors as well as the number of rounds. Applying our compiler to (a parallel repetition of) Mahadev’s CVQC protocol for\\(\\mathsf {BQP}\\)and our CVQC protocol for\\(\\mathsf {SampBQP}\\)yields the firstconstant-roundblind CVQC protocol for\\(\\mathsf {BQP}\\)and\\(\\mathsf {SampBQP}\\)respectively, with negligible and inverse polynomial soundness errors respectively, and negligible completeness errors.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_25"}, {"primary_key": "1657077", "vector": [], "sparse_vector": [], "title": "Round-Optimal and Communication-Efficient Multiparty Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Typical approaches for minimizing the round complexity of multiparty computation (MPC) come at the cost of increased communication complexity (CC) or the reliance on setup assumptions. A notable exception is the recent work of <PERSON><PERSON><PERSON><PERSON> <PERSON>[TCC 2019], which used Functional Encryption (FE) combiners to obtain a round optimal (two-round) semi-honest MPC in the plain model with a CC proportional to the depth and input-output length of the circuit being computed—we refer to such protocols ascircuit scalable. This leaves open the question of obtaining communication efficient protocols that are secure againstmaliciousadversaries in the plain model, which we present in this work. Concretely, our two main contributions are: 1) We provide a round-preserving black-box compiler that compiles a wide class of MPC protocols intocircuit-scalablemaliciously secure MPC protocols in the plain model, assuming (succinct) FE combiners. 2) We provide a round-preserving black-box compiler that compiles a wide class of MPC protocols intocircuit-independent—i.e., with a CC that depends only on the input-output length of the circuit—maliciously secure MPC protocols in the plain model, assuming Multi-Key Fully-Homomorphic Encryption (MFHE). Our constructions are based on a new compiler that turns a wide class of MPC protocols intok-delayed-input function MPC protocols (a notion we introduce), where the function that is being computed is specified only in thek-th round of the protocol. As immediate corollaries of our two compilers, we derive (1) the first round-optimal and circuit-scalable maliciously secure MPC, and (2) the first round-optimal and circuit-independent maliciously secure MPC in the plain model. The latter MPC achieves the best to-date CC for a round-optimal malicious MPC protocol. In fact, it is even communication-optimal when the output size of the function being evaluated is smaller than its input size (e.g., for boolean functions). All of our results are based on standard polynomial time assumptions.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_3"}, {"primary_key": "1657078", "vector": [], "sparse_vector": [], "title": "Round-Optimal Multi-party Computation with Identifiable Abort.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure multi-party computation (MPC) protocols that are resilient to a dishonest majority allow the adversary to get the output of the computation while, at the same time, forcing the honest parties to abort. <PERSON><PERSON> and <PERSON><PERSON> introduced the enhanced notion ofsecurity with identifiable abort, which still allows the adversary to trigger an abort but, at the same time, it enables the honest parties to agree on the identity of the party that led to the abort. More recently, in Eurocrypt 2016, <PERSON><PERSON><PERSON> et al. showed that, assuming access to a simultaneous message exchange channel for all the parties, at least four rounds of communication are required to securely realize non-trivial functionalities in the plain model. Following <PERSON><PERSON><PERSON> et al., a sequence of works has matched this lower bound, but none of them achieved security with identifiable abort. In this work, we close this gap and show that four rounds of communication are also sufficient to securely realize any functionality with identifiable abort using standard and generic polynomial-time assumptions. To achieve this result we introduce the new notion ofbounded-rewind secure MPCthat guarantees security even against an adversary that performs a mild form of reset attacks. We show how to instantiate this primitive starting fromanyMPC protocol and by assuming trapdoor-permutations. The notion of bounded-rewind secure MPC allows for easier parallel composition of MPC protocols with other (interactive) cryptographic primitives. Therefore, we believe that this primitive can be useful in other contexts in which it is crucial to combine multiple primitives with MPC protocols while keeping the round complexity of the final protocol low.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_12"}, {"primary_key": "1657079", "vector": [], "sparse_vector": [], "title": "Guaranteed Output in $O(\\sqrt{n})$ Rounds for Round-Robin Sampling Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a notion ofround-robinsecure sampling that captures several protocols in the literature, such as the “powers-of-tau” setup protocol for pairing-based polynomial commitments and zk-SNARKs, and certain verifiable mixnets. Due to their round-robin structure, protocols of this class inherently requirensequential broadcast rounds, wherenis the number of participants. We describe how to compile them generically into protocols that require only\\(O(\\sqrt{n})\\)broadcast rounds. Our compiled protocols guarantee output delivery againstanydishonest majority. This stands in contrast to prior techniques, which require\\(\\varOmega (n)\\)sequential broadcasts in most cases (and sometimes many more). Our compiled protocols permit a certain amount of adversarial bias in the output, as all sampling protocols with guaranteed output must, due to <PERSON><PERSON><PERSON>’s impossibility result (STOC’86). We show that in the context of the aforementioned applications, this bias is harmless.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_9"}, {"primary_key": "1657080", "vector": [], "sparse_vector": [], "title": "Single-Server Private Information Retrieval with Sublinear Amortized Time.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct new private-information-retrieval protocols in the single-server setting. Our schemes allow a client to privately fetch a sequence of database records from a server, while the server answers each query in average time sublinear in the database size. Specifically, we introduce the first single-server private-information-retrieval schemes that have sublinear amortized server time, require sublinear additional storage, and allow the client to make her queries adaptively. Our protocols rely only on standard cryptographic assumptions (decision Di<PERSON><PERSON>-<PERSON>, quadratic residuosity, learning with errors, etc.). They work by having the client first fetch a small “hint” about the database contents from the server. Generating this hint requires server time linear in the database size. Thereafter, the client can use the hint to make a bounded number of adaptive queries to the server, which the server answers in sublinear time—yielding sublinear amortized cost. Finally, we give lower bounds proving that our most efficient scheme is optimal with respect to the trade-off it achieves between server online time and client storage.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_1"}, {"primary_key": "1657081", "vector": [], "sparse_vector": [], "title": "On the Concrete Security of TLS 1.3 PSK Mode.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The pre-shared key (PSK) handshake modes of TLS 1.3 allow for the performant, low-latency resumption of previous connections and are widely used on the Web and by resource-constrained devices, e.g., in the Internet of Things. Taking advantage of these performance benefits with optimal and theoretically-sound parameters requires tight security proofs. We give the first tight security proofs for the TLS 1.3 PSK handshake modes. Our main technical contribution is to address a gap in prior tight security proofs of TLS 1.3 which modeled either the entire key schedule or components thereof as independent random oracles to enable tight proof techniques. These approaches ignore existing interdependencies in TLS 1.3’s key schedule, arising from the fact that the same cryptographic hash function is used in several components of the key schedule and the handshake more generally. We overcome this gap by proposing a new abstraction for the key schedule and carefully arguing its soundness via the indifferentiability framework. Interestingly, we observe that for one specific configuration, PSK-only mode with hash function SHA-384, it seems difficult to argue indifferentiability due to a lack of domain separation between the various hash function usages. We view this as an interesting insight for the design of protocols, such as future TLS versions. For all other configurations however, our proofs significantly tighten the security of the TLS 1.3 PSK modes, confirming standardized parameters (for which prior bounds provided subpar or even void guarantees) and enabling a theoretically-sound deployment.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_30"}, {"primary_key": "1657082", "vector": [], "sparse_vector": [], "title": "Revamped Differential-Linear Cryptanalysis on Reduced Round ChaCha.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we provide several improvements over the existing differential-linear attacks on ChaCha. ChaCha is a stream cipher which has 20 rounds. At CRYPTO 2020, <PERSON><PERSON><PERSON> et al. observed a differential in the 3.5-th round if the right pairs are chosen. They produced an improved attack using this, but showed that to achieve a right pair, we need\\(2^5\\)iterations on average. In this direction, we provide a technique to find the right pairs with the help of listing. Also, we provide a strategical improvement in PNB construction, modification of complexity calculation and an alternative attack method using two input-output pairs. Using these, we improve the time complexity, reducing it to\\(2^{221.95}\\)from\\(2^{230.86}\\)reported by <PERSON><PERSON><PERSON> et al. for 256 bit version of ChaCha. Also, after a decade, we improve existing complexity (<PERSON> et al. ICISC 2012) for a 6-round of 128 bit version of ChaCha by more than 11 million times and produce the first-ever attack on 6.5-round ChaCha128 with time complexity\\(2^{123.04}.\\)", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_4"}, {"primary_key": "1657083", "vector": [], "sparse_vector": [], "title": "Authentication in the Bounded Storage Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the \nstreaming variant of the Bounded Storage Model (BSM), where the honest parties can stream large amounts of data to each other, while only maintaining a small memory of sizen. The adversary also operates as a streaming algorithm, but has a much larger memory size\\(m \\gg n\\). The goal is to construct unconditionally secure cryptographic schemes in the BSM, and prior works did so for symmetric-key encryption, key agreement, oblivious transfer and multiparty computation. In this work, we constructmessage authentication and signaturesin the BSM. First, we consider the symmetric-key setting, where <PERSON> and <PERSON> share a small secret key. <PERSON> can authenticate arbitrarily many messages to <PERSON> by streaming long authentication tags of size\\(k \\gg m\\), while ensuring that the tags can be generated and verified using onlynbits of memory. We show a solution using local extractors (<PERSON><PERSON><PERSON>; JoC ’04), which allows for up to exponentially large adversarial memory\\(m = 2^{O(n)}\\), and has tags of size\\(k= O(m)\\). Second, we consider the same setting as above, but now additionally require each individual tag to be small, of size\\(k \\le n\\). We show a solution is still possible when the adversary’s memory is\\(m = O(n^2)\\), which is optimal. Our solution relies on a space lower bound for leaning parities (<PERSON><PERSON>; FOCS ’16). Third, we consider the public-key signature setting. A signer <PERSON> initially streams a long verification key over an authentic channel, while only keeping a short signing key in her memory. A verifier Bob receives the streamed verification key and generates a short verification digest that he keeps in his memory. Later, <PERSON> can sign arbitrarily many messages using her signing key by streaming large signatures to <PERSON>, who can verify them using his verification digest. We show a solution for\\(m= O(n^2)\\), which we show to be optimal. Our solution relies on a novel entropy lemma, of independent interest. We show that, if a sequence of blocks has sufficiently high min-entropy, then a large fraction of individual blocks must have high min-entropy. Naive versions of this lemma are false, but we show how to patch it to make it hold.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_26"}, {"primary_key": "1657084", "vector": [], "sparse_vector": [], "title": "Online-Extractability in the Quantum Random-Oracle Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show the following generic result: When a quantum query algorithm in the quantum random-oracle model outputs a classical valuetthat is promised to be in some tight relation withH(x) for somex, thenxcan be efficiently extracted with almost certainty. The extraction is by means of a suitable simulation of the random oracle and worksonline, meaning that it isstraightline, i.e., without rewinding, andon-the-fly, i.e., during the protocol execution and (almost) without disturbing it. The technical core of our result is a new commutator bound that bounds the operator norm of the commutator of the unitary operator that describes the evolution of the compressed oracle (which is used to simulate the random oracle above) and of the measurement that extractsx. We show two applications of our generic online extractability result. We showtightonline extractability of commit-and-open\\(\\Sigma \\)-protocols in the quantum setting, and we offer the first complete post-quantum security proof of thetextbookFujisaki-Okamo<PERSON> transformation, i.e., without adjustments to facilitate the proof, including concrete security bounds.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_24"}, {"primary_key": "1657085", "vector": [], "sparse_vector": [], "title": "Key Guessing Strategies for Linear Key-Schedule Algorithms in Rectangle Attacks.", "authors": ["<PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON><PERSON>"], "summary": "When generating quartets for the rectangle attacks on ciphers with linear key-schedule, we find the right quartets which may suggest key candidates have to satisfy some nonlinear relations. However, some quartets generated always violate these relations, so that they will never suggest any key candidates. Inspired by previous rectangle frameworks, we find that guessing certain key cells before generating quartets may reduce the number of invalid quartets. However, guessing a lot of key cells at once may lose the benefit from the early abort technique, which may lead to a higher overall complexity. To get better tradeoff, we build a new rectangle framework on ciphers with linear key-schedule with the purpose of reducing overall complexity or attacking more rounds. In the tradeoff model, there are many parameters affecting the overall complexity, especially for the choices of the number and positions of key guessing cells before generating quartets. To identify optimal parameters, we build a uniform automatic tool onSKINNYas an example, which includes the optimal rectangle distinguishers for key-recovery phase, the number and positions of key guessing cells before generating quartets, the size of key counters to build that affecting the exhaustive search step, etc. Based on the automatic tool, we identify a 32-round key-recovery attack onSKINNY-128-384 in the related-key setting, which extends the best previous attack by 2 rounds. For other versions withn-2norn-3n, we also achieve one more round than before. In addition, using the previous rectangle distinguishers, we achieve better attacks on round-reducedForkSkinny,Deoxys-BC-384andGIFT-64. At last, we discuss the conversion of our rectangle framework from related-key setting into single-key setting and give new single-key rectangle attack on 10-roundSerpent.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_1"}, {"primary_key": "1657086", "vector": [], "sparse_vector": [], "title": "On the Lattice Isomorphism Problem, Quadratic Forms, Remarkable Lattices, and Cryptography.", "authors": ["Léo Du<PERSON>", "<PERSON><PERSON> P<PERSON>"], "summary": "A natural and recurring idea in the knapsack/lattice cryptography literature is to start from a lattice with remarkable decoding capability as your private key, and hide it somehow to make a public key. This is also how the code-based encryption scheme of <PERSON><PERSON><PERSON><PERSON><PERSON> (1978) proceeds. This idea has never worked out very well for lattices: ad-hoc approaches have been proposed, but they have been subject to ad-hoc attacks, using tricks beyond lattice reduction algorithms. On the other hand the framework offered by the Short Integer Solution (SIS) and Learning With Errors (LWE) problems, while convenient and well founded, remains frustrating from a coding perspective: the underlying decoding algorithms are rather trivial, with poor decoding performance. In this work, we provide generic realizations of this natural idea (independently of the chosen remarkable lattice) by basing cryptography on the lattice isomorphism problem (LIP). More specifically, we provide: a worst-case to average-case reduction for search-LIP and distinguish-LIP within an isomorphism class, by extending techniques of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (SODA 2014). a zero-knowledge proof of knowledge (ZKPoK) of an isomorphism. This implies an identification scheme based on search-LIP. a key encapsulation mechanism (KEM) scheme and a hash-then-sign signature scheme, both based on distinguish-LIP. The purpose of this approach is for remarkable lattices to improve the security and performance of lattice-based cryptography. For example, decoding within poly-logarithmic factor from <PERSON><PERSON>’s bound in a remarkable lattice would lead to a KEM resisting lattice attacks down to poly-logarithmic approximation factor, provided that the dual lattice is also close to Minkowski’s bound. Recent works have indeed reached such decoders for certain lattices (Chor-Rivest, Barnes-Sloan), but these do not perfectly fit our need as their duals have poor minimal distance.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_23"}, {"primary_key": "1657087", "vector": [], "sparse_vector": [], "title": "Mitaka: <PERSON> <PERSON>r, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> of Falcon.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work describes theMitakasignature scheme: a new hash-and-sign signature scheme over NTRU lattices which can be seen as a variant of NIST finalistFalcon. It achieves comparable efficiency but is considerably simpler, online/offline, and easier to parallelize and protect against side-channels, thus offering significant advantages from an implementation standpoint. It is also much more versatile in terms of parameter selection. We obtain this signature scheme by replacing the FFO lattice Gaussian sampler inFalconby the “hybrid” sampler of Ducas and Prest, for which we carry out a detailed and corrected security analysis. In principle, such a change can result in a substantial security loss, but we show that this loss can be largely mitigated using new techniques in key generation that allow us to construct much higher quality lattice trapdoors for the hybrid sampler relatively cheaply. This new approach can also be instantiated on a wide variety of base fields, in contrast withFalcon’s restriction to power-of-two cyclotomics. We also introduce a new lattice Gaussian sampler with the same quality and efficiency, but which is moreover compatible with the integral matrix Gram root technique of <PERSON> et al., allowing us to avoid floating point arithmetic. This makes it possible to realize thesamesignature scheme asMitakaefficiently on platforms with poor support for floating point numbers. Finally, we describe a provably secure masking ofMitaka. More precisely, we introduce novel gadgets that allow provable masking at any order at much lower cost than previous masking techniques for Gaussian sampling-based signature schemes, for cheap and dependable side-channel protection.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_9"}, {"primary_key": "1657088", "vector": [], "sparse_vector": [], "title": "McEliece Needs a Break - Solving McEliece-1284 and Quasi-Cyclic-2918 with Modern ISD.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the recent shift to post-quantum algorithms it becomes increasingly important to provide precise bit-security estimates for code-based cryptography such as McEliece and quasi-cyclic schemes like BIKE and HQC. While there has been significant progress on information set decoding (ISD) algorithms within the last decade, it is still unclear to which extent this affects current cryptographic security estimates. We provide the first concrete implementations for representation-based ISD, such as May-Meurer-Thomae (MMT) or Becker-Joux-May-Meurer (BJMM), that are parameter-optimized for the McEliece and quasi-cyclic setting. Although MMT and BJMM consume more memory than naive ISD algorithms like Prange, we demonstrate that these algorithms lead to significant speedups for practical cryptanalysis on medium-sized instances (around 60 bit). More concretely, we provide data for the record computations of McEliece-1223 and McEliece-1284 (old record: 1161), and for the quasi-cyclic setting up to code length 2918 (before: 1938). Based on our record computations we extrapolate to the bit-security level of the proposed BIKE, HQC and McEliece parameters in NIST’s standardization process. For BIKE/HQC, we also show how to transfer the Decoding-One-Out-of-Many (DOOM) technique to MMT/BJMM. Although we achieve significant DOOM speedups, our estimates confirm the bit-security levels of BIKE and HQC. For the proposed McEliece round-3 192 bit and two out of three 256 bit parameter sets, however, our extrapolation indicates a security level overestimate by roughly 20 and 10 bits, respectively, i.e., the high-security McEliece instantiations may be a bit less secure than desired.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_16"}, {"primary_key": "1657089", "vector": [], "sparse_vector": [], "title": "Property-Preserving Hash Functions for Hamming Distance from Standard Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Property-preserving hash functions allow for compressing long inputs\\(x_0\\)and\\(x_1\\)into short hashes\\(h(x_0)\\)and\\(h(x_1)\\)in a manner that allows for computing a predicate\\(P(x_0, x_1)\\)given only the two hash values without having access to the original data. Such hash functions are said to be adversarially robust if an adversary that gets to pick\\(x_0\\)and\\(x_1\\)after the hash function has been sampled, cannot find inputs for which the predicate evaluated on the hash values outputs the incorrect result. In this work we construct robust property-preserving hash functions for the hamming-distance predicate which distinguishes inputs with a hamming distance at least some thresholdtfrom those with distance less thant. The security of the construction is based on standard lattice hardness assumptions. Our construction has several advantages over the best known previous construction by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt 2021). Our construction relies on a single well-studied hardness assumption from lattice cryptography whereas the previous work relied on a newly introduced family of computational hardness assumptions. In terms of computational effort, our construction only requires a small number of modular additions per input bit, whereas the work of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> required several exponentiations per bit as well as the interpolation and evaluation of high-degree polynomials over large fields. An additional benefit of our construction is that the description of the hash function can be compressed to\\(\\lambda \\)bits assuming a random oracle. Previous work has descriptions of length\\(\\mathcal {O}({\\ell \\lambda })\\)bits for input bit-length\\(\\ell \\). We prove a lower bound on the output size of any property-preserving hash function for the hamming distance predicate. The bound shows that the size of our hash value is not far from optimal.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_26"}, {"primary_key": "1657090", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir Bulletproofs are Non-Malleable (in the Algebraic Group Model).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bulletproofs (<PERSON><PERSON><PERSON> et al. IEEE S&P 2018) are a celebrated ZK proof system that allows for short and efficient proofs, and have been implemented and deployed in several real-world systems. In practice, they are most often implemented in theirnon-interactiveversion obtained using the Fiat-Shamir transform, despite the lack of a formal proof of security for this setting. Prior to this work, there was no evidence thatmalleability attackswere not possible against Fiat-Shamir Bulletproofs. Malleability attacks can lead to very severe vulnerabilities, as they allow an adversary to forge proofs re-using or modifying parts of the proofs provided by the honest parties. In this paper, we show for the first time that Bulletproofs (or any other similar multi-round proof system satisfying some form ofweak unique responseproperty) achievesimulation-extractabilityin thealgebraic group model. This implies that Fiat-Shamir Bulletproofs arenon-malleable.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_14"}, {"primary_key": "1657091", "vector": [], "sparse_vector": [], "title": "A Novel Completeness Test for Leakage Models and Its Application to Side Channel Attacks and Responsibly Engineered Simulators.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Today’s side channel attack targets are often complex devices in which instructions are processed in parallel and work on 32-bit data words. Consequently, thestatethat is involved in producing leakage in these modern devices is not only large, but also hard to predict due to various micro-architectural factors that users might not be aware of. On the other hand, security evaluations—basing on worst case attacks or simulators—explicitly rely on the underlyingstate: a potentiallyincomplete statecan easily lead to wrong conclusions. We put forward a novel notion for the “completeness” of an assumed state, together with an efficient statistical test that is based on “collapsed models”. Our novel test can be used to recover astatethat contains multiple 32-bit variables in a grey box setting. We illustrate how our novel test can help to guide side channel attacks and we reveal new attack vectors for existing implementations. We then demonstrate the application of this test in the context of leakage modelling for leakage simulators and confirm that even the most recent leakage simulators do not capture all available leakage of their respective target devices. Our new test enables finding nominal models that capture all available leakage but do not give a helping hand to adversaries. Thereby we make a first step towards leakage simulators that are responsibly engineered.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_10"}, {"primary_key": "1657092", "vector": [], "sparse_vector": [], "title": "Towards Micro-architectural Leakage Simulators: Reverse Engineering Micro-architectural Leakage Features Is Practical.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Leakage simulators offer the tantalising promise of easy and quick testing of software with respect to the presence of side channel leakage. The quality of their build in leakage models is therefore crucial, this includes the faithful inclusion of micro-architectural leakage. Micro-architectural leakage is a reality even on low- to mid-range commercial processors, such as the ARM Cortex M series. Dealing with it seems initially infeasible in a “grey box” setting: how should we describe it if micro-architectural elements are not publicly known? We demonstrate, for the first time, that it is feasible, using a recent leakage modelling technique, to reverse engineer significant elements of the micro-architectural leakage of a commercial processor. Our approach first recovers the micro-architectural leakage of each stage in the pipeline, and the leakage of elements that are known to produce glitches. Using the reverse engineered leakage features we build an enhanced version of the popular leakage simulator ELMO.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_11"}, {"primary_key": "1657093", "vector": [], "sparse_vector": [], "title": "Dynamic Collusion Bounded Functional Encryption from Identity-Based Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Functional Encryption is a powerful notion of encryption in which each decryption key is associated with a functionfsuch that decryption recovers the function evaluationf(m). Informally, security states that a user with access to function keys\\(\\mathsf {sk}_{f_1}, \\mathsf {sk}_{f_2}, \\ldots \\)(and so on) can only learn\\(f_1(m), f_2(m), \\ldots \\)(and so on) but nothing more about the message. The system is said to beq-bounded collusion resistant if the security holds as long as an adversary gets access to at most\\(q = q(\\lambda )\\)function keys. A major drawback of suchstaticallybounded collusion systems is that the collusion boundqmust be declared at setup time and is fixed for the entire lifetime of the system. We initiate the study ofdynamicallybounded collusion resistant functional encryption systems which provide more flexibility in terms of selecting the collusion bound, while reaping the benefits of statically bounded collusion FE systems (such as quantum resistance, simulation security, and general assumptions). Briefly, the virtues of a dynamically bounded scheme can be summarized as: Fine-grained individualized selection.It lets each encryptor select the collusion bound by weighing the trade-off between performance overhead and the amount of collusion resilience. Evolving encryption strategies.Since the system is no longer tied to a single collusion bound, thus it allows to dynamically adjust the desired collusion resilience based on any number of evolving factors such as the age of the system, or a number of active users, etc. Ease and simplicity of updatability.None of the system parameters have to be updated when adjusting the collusion bound. That is, the same key\\(\\mathsf {sk}_f\\)can be used to decrypt ciphertexts for collusion bound\\(q = 2\\)as well as\\(q = 2^\\lambda \\). We construct such a dynamically bounded functional encryption scheme for the class of all polynomial-size circuits under the general assumption of Identity-Based Encryption.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_25"}, {"primary_key": "1657094", "vector": [], "sparse_vector": [], "title": "Practical Non-interactive Publicly Verifiable Secret Sharing with Thousands of Parties.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-interactive publicly verifiable secret sharing (PVSS) schemes enables (re-)sharing of secrets in a decentralized setting in the presence of \n malicious parties. A recently proposed application of PVSS schemes is to enable permissionless proof-of-stake blockchains to “keep a secret” via a sequence of committees that share that secret. These committees can use the secret to produce signatures on the blockchain’s behalf, or to disclose hidden data conditioned on consensus that some event has occurred. That application needs very large committees with thousands of parties, so the PVSS scheme in use must be efficient enough to support such large committees, in terms of both computation and communication. Yet, previous PVSS schemes have large proofs and/or require many exponentiations over large groups. We present a non-interactive PVSS scheme in which the underlying encryption scheme is based on the learning with errors (LWE) problem. While lattice-based encryption schemes are very fast, they often have long ciphertexts and public keys. We use the following two techniques to conserve bandwidth: First, we adapt the Peikert-Vaikuntanathan-Waters (PVW) encryption scheme to the multi-receiver setting, so that the bulk of the parties’ keys is a common random string. The resulting scheme yields\\(\\varOmega (1)\\)amortized plaintext/ciphertext rate, where concretely the rate is\\(\\approx 1/60\\)for 100 parties,\\(\\approx 1/8\\)for 1000 parties, and approaching 1/2 as the number of parties grows. Second, we use bulletproofs over a DL-group of order about 256 bits to get compact proofs of correct encryption/decryption of shares. Alternating between the lattice and DL settings is relatively painless, as we equate the LWE modulus with the order of the group. We also show how to reduce the the number of exponentiations in the bulletproofs by applying Johnson-Lindenstrauss-like compression to reduce the dimension of the vectors whose properties must be verified. An implementation of our PVSS with 1000 parties showed that it is feasible even at that size, and should remain so even with one or two order of magnitude increase in the committee size.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_16"}, {"primary_key": "1657095", "vector": [], "sparse_vector": [], "title": "Round-Optimal Byzantine Agreement.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Byzantine agreement is a fundamental primitive in cryptography and distributed computing, and minimizing its round complexity is of paramount importance. It is long known that any randomizedr-round protocol must fail with probability at least\\((c\\cdot r)^{-r}\\), for some constantc, when the number of corruptions is linear in the number of parties,\\(t = \\theta (n)\\). On the other hand, current protocols fail with probability at least\\(2^{-r}\\). Whether we can match the lower bound agreement probability remains unknown. In this work, we resolve this long-standing open question. We present a protocol that matches the lower bound up to constant factors. Our results hold under a (strongly rushing) adaptive adversary that can corrupt up to\\(t = (1-\\epsilon )n/2\\)parties, and our protocols use a public-key infrastructure and a trusted setup for unique threshold signatures. This is the first protocol that decreases the failure probability (overall) by asuper-constantfactor per round.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_4"}, {"primary_key": "1657096", "vector": [], "sparse_vector": [], "title": "Hiding in Plain Sight: Memory-Tight Proofs via Randomness Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper continues the study ofmemory-tight reductions(<PERSON><PERSON><PERSON> et al., CRYPTO ’17). These are reductions that only incur minimal memory costs over those of the original adversary, allowing precise security statements for memory-bounded adversaries (under appropriate assumptions expressed in terms of adversary time and memory usage). Despite its importance, only a few techniques to achieve memory-tightness are known and impossibility results in prior works show that even basic, textbook reductions cannot be made memory-tight. This paper introduces a new class of memory-tight reductions which leverage random strings in the interaction with the adversary to hide state information, thus shifting the memory costs to the adversary. We exhibit this technique with several examples. We give memory-tight proofs for digital signatures allowing many forgery attempts when considering randomized message distributions or probabilistic RSA-FDH signatures specifically. We prove security of the authenticated encryption scheme Encrypt-then-PRF with a memory-tight reduction to the underlying encryption scheme. By considering specific schemes or restricted definitions we avoid generic impossibility results of <PERSON><PERSON><PERSON> et al. (CRYPTO ’17) and <PERSON><PERSON><PERSON> et al. (CRYPTO ’20). As a further case study, we consider the textbook equivalence of CCA-security for public-key encryption for one or multiple encryption queries. We show two qualitatively different memory-tight versions of this result, depending on the considered notion of CCA security.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_24"}, {"primary_key": "1657097", "vector": [], "sparse_vector": [], "title": "Stacking Sigmas: A Framework to Compose $\\varSigma $-Protocols for Disjunctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero-Knowledge (ZK) Proofs for disjunctive statements have been a focus of a long line of research. Classical results such as <PERSON><PERSON><PERSON><PERSON> al.[CRYPTO’94] and <PERSON><PERSON> al.[AC’02] design generic compilers that transform certain classes of ZK proofs into ZK proofs for disjunctive statements. However, communication complexity of the resulting protocols in these results ends up being proportional to the complexity of proving all clauses in the disjunction. More recently, Heathet al.[EC’20] exploited special properties of garbled circuits to construct efficient ZK proofs for disjunctions, where the proof size is only proportional to the length of the largest clause in the disjunction. However, these techniques do not appear to generalize beyond garbled circuits. In this work, we focus on achieving the best of both worlds. We design ageneral frameworkthat compiles a large class of unmodified\\(\\varSigma \\)-protocols, each for an individual statement, into a new\\(\\varSigma \\)-protocol that proves a disjunction of these statements. Our framework can be used both when each clause is proved with the same\\(\\varSigma \\)-protocol and when different\\(\\varSigma \\)-protocols are used for different clauses. The resulting\\(\\varSigma \\)-protocol is concretely efficient and has communication complexity proportional to the communication required by the largest clause, with additive terms that are only logarithmic in the number of clauses. We show that our compiler can be applied to many well-known\\(\\varSigma \\)-protocols, including classical protocols (e.g.<PERSON><PERSON> [JC’91] and Guillou-Quisquater [CRYPTO’88]) and modern MPC-in-the-head protocols such as the recent work of Katz, Kolesnikov and Wang [CCS’18] and the Ligero protocol of Ameset al.[CCS’17]. Finally, since all of the protocols in our class can be made non-interactive in the random oracle model using the Fiat-Shamir transform, our result yields the first generic non-interactive zero-knowledge protocol for disjunctions where the communication only depends on the size of the largest clause.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_16"}, {"primary_key": "1657098", "vector": [], "sparse_vector": [], "title": "Secure Multiparty Computation with Free Branching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study secure multi-party computation (MPC) protocols for branching circuits that contain multiple sub-circuits (i.e., branches) and the output of the circuit is that of  single “active” branch. Crucially, the identity of the active branch must remain hidden from the protocol participants. While such circuits can be securely computed by evaluating each branch and then multiplexing the output, such an approach incurs a communication cost linear in the size of the entire circuit. To alleviate this, a series of recent works have investigated the problem of reducing the communication cost of branching executions inside MPC (without relying on fully homomorphic encryption). Most notably, the stacked garbling paradigm [<PERSON> and Ko<PERSON>nikov, CRYPTO’20] yields garbled circuits for branching circuits whose size only depends on the size of the largest branch. Presently, however, it is not known how to obtain similar communication improvements for secure computation involvingmore than two parties. In this work, we provide a generic framework for branching multi-party computation that supportsany number of parties. The communication complexity of our scheme is proportional to the size of the largest branch and the computation is linear in the size of the entire circuit. We provide an implementation and benchmarks to demonstrate practicality of our approach.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_14"}, {"primary_key": "1657099", "vector": [], "sparse_vector": [], "title": "Private Circuits with Quasilinear Randomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At-privatecircuit for a functionfis a randomized Boolean circuitCthat maps a randomized encoding of an inputxto an encoding of the outputf(x), such that probingtwires anywhere inCreveals nothing aboutx. Private circuits can be used to protect embedded devices against side-channel attacks. Motivated by the high cost of generating fresh randomness in such devices, several works have studied the question of minimizing the randomness complexity of private circuits. The best known upper bound, due to <PERSON><PERSON> et al. (Eurocrypt 2020), is\\(O(t^2\\cdot \\log ts)\\)random bits, wheresis the circuit size off. We improve this to\\(O(t\\cdot \\log ts)\\), including the randomness used by the input encoder, and extend this bound to the stateful variant of private circuits. Our constructions are semi-explicit in the sense that there is an efficient randomized algorithm that generates the private circuitCfrom a circuit forfwith negligible failure probability.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_8"}, {"primary_key": "1657100", "vector": [], "sparse_vector": [], "title": "On the Security of ECDSA with Additive Key Derivation and Presignatures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Two common variations of ECDSA signatures areadditive key derivationandpresignatures. Additive key derivation is a simple mechanism for deriving many subkeys from a single master key, and is already widely used in cryptocurrency applications with the Hierarchical Deterministic Wallet mechanism standardized in Bitcoin Improvement Proposal 32 (BIP32). Because of its linear nature, additive key derivation is also amenable to efficient implementation in the threshold setting. With presignatures, the secret and public nonces used in the ECDSA signing algorithm are precomputed. In the threshold setting, using presignatures along with other precomputed data allows for an extremely efficient “online phase” of the protocol. Recent works have advocated for both of these variations, sometimes combined together. However, somewhat surprisingly, we are aware of no prior security proof for additive key derivation, let alone for additive key derivation in combination with presignatures. In this paper, we provide a thorough analysis of these variations, both in isolation and in combination. Our analysis is in the generic group model (GGM). Importantly, we do not modify ECDSA or weaken the standard notion of security in any way. Of independent interest, we also present a version of the GGM that is specific to elliptic curves. This EC-GGM better models some of the idiosyncrasies (such as the conversion function and malleability) of ECDSA. In addition to this analysis, we report security weaknesses in these variations that apparently have not been previously reported. For example, we show that when both variations are combined, there is a cube-root attack on ECDSA, which is much faster than the best known, square-root attack on plain ECDSA. We also present two mitigations against these weaknesses: re-randomized presignatures and homogeneous key derivation. Each of these mitigations is very lightweight, and when used in combination, the security is essentially the same as that of plain ECDSA (in the EC-GGM).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_13"}, {"primary_key": "1657101", "vector": [], "sparse_vector": [], "title": "Anonymous, Robust Post-quantum Public Key Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A core goal of the NIST PQC competition is to produce PKE schemes which, even if attacked with a large-scale quantum computer, maintain the security guarantees needed by applications. The main security focus in the NIST PQC context has been IND-CCA security, but other applications demand that PKE schemes provideanonymity(<PERSON><PERSON> al., ASIACRYPT 2001), androbustness(Abdallaet al., TCC 2010). Examples of such applications include anonymous cryptocurrencies, searchable encryption, and auction protocols. However, almost nothing is known about how to build post-quantum PKE schemes offering these security properties. In particular, the status of the NIST PQC candidates with respect to anonymity and robustness is unknown. This paper initiates a systematic study of anonymity and robustness for post-quantum PKE schemes. Firstly, we identify implicit rejection as a crucial design choice shared by most post-quantum KEMs, show that implicit rejection renders prior results on anonymity and robustness for KEM-DEM PKEs inapplicable, and transfer prior results to the implicit-rejection setting where possible. Secondly, since they are widely used to build post-quantum PKEs, we examine how the Fujisaki-Okamoto (FO) transforms (Fujisaki and Okamoto, Journal of Cryptology 2013) confer robustness and enhance weak anonymity of a base PKE. We then leverage our theoretical results to study the anonymity and robustness of three NIST KEM finalists—<PERSON>ber, Kyber, and Classic McEliece—and one alternate, FrodoKEM. Overall, our findings for robustness are definitive: we provide positive robustness results for Saber, Kyber, and FrodoKEM, and a negative result for Classic McEliece. Our negative result stems from a striking property of KEM-DEM PKE schemes built with the Classic McEliece KEM: for any messagem, we can construct a single hybrid ciphertextcwhich decrypts to the chosenmunderanyClassic McEliece private key. Our findings for anonymity are more mixed: we identify barriers to proving anonymity for Saber, Kyber, and Classic McEliece. We also found that in the case of Saber and Kyber, these barriers lead to issues with their IND-CCA security claims. We have worked with the Saber and Kyber teams to fix these issues, but they remain unresolved. On the positive side, we were able to prove anonymity for FrodoKEM and a variant of Saber introduced by D’Anverset al.(AFRICACRYPT 2018). Our analyses of these two schemes also identified technical gaps in their IND-CCA security claims, but we were able to fix them.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_15"}, {"primary_key": "1657102", "vector": [], "sparse_vector": [], "title": "Incompressible Cryptography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Incompressible encryptionallows us to make the ciphertext size flexibly large and ensures that an adversary learns nothing about the encrypted data, even if the decryption key later leaks, unless she stores essentially the entire ciphertext.Incompressible signaturescan be made arbitrarily large and ensure that an adversary cannot produce a signature on<PERSON><PERSON><PERSON><PERSON>, even one she has seen signed before, unless she stores one of the signatures essentially in its entirety. In this work, we give simple constructions of both incompressible public-key encryption and signatures under minimal assumptions. Furthermore, large incompressible ciphertexts (resp. signatures) can be decrypted (resp. verified) in a streaming manner with low storage. In particular, these notions strengthen the related concepts ofdisappearing encryption and signatures, recently introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC 2021), whose previous constructions relied on sophisticated techniques and strong, non-standard assumptions. We extend our constructions to achieve an optimal “rate”, meaning the large ciphertexts (resp. signatures) can contain almost equally large messages, at the cost of stronger assumptions.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_24"}, {"primary_key": "1657103", "vector": [], "sparse_vector": [], "title": "Optimal Tightness for Chain-Based Unique Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Unique signatures are digital signatures with exactly one unique and valid signature for each message. The security reduction for most unique signatures has a natural reduction loss (in the existentially unforgeable against chosen-message attacks, namely EUF-CMA, security model under a non-interactive hardness assumption). In Crypto 2017, <PERSON><PERSON> <PERSON>.proposed a particular chain-based unique signature scheme where each unique signature is composed ofnBLS signatures computed sequentially like a blockchain. Under the computational Di<PERSON><PERSON>-<PERSON> assumption, their reduction loss is\\(n\\cdot q_H^{1/n}\\)for\\(q_H\\)hash queries and it is logarithmically tight when\\(n=\\log {q_H}\\). However, it is currently unknown whether a better reduction than logarithmical tightness for the chain-based unique signatures exists. We show that the proposed chain-based unique signature scheme by <PERSON><PERSON> al.must have the reduction loss\\(q^{1/n}\\)forqsignature queries when each unique signature consists ofnBLS signatures. We use a meta reduction to prove this lower bound in the EUF-CMA security model under any non-interactive hardness assumption, and the meta-reduction is also applicable in the random oracle model. We also give a security reduction with reduction loss\\(4\\cdot q^{1/n}\\)for the chain-based unique signature scheme (in the EUF-CMA security model under the CDH assumption). This improves significantly on previous reduction loss\\(n\\cdot q_H^{1/n}\\)that is logarithmically tight at most. The core of our reduction idea is anon-uniformsimulation that is specially invented for the chain-based unique signature construction.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_19"}, {"primary_key": "1657104", "vector": [], "sparse_vector": [], "title": "Rubato: Noisy Ciphers for Approximate Homomorphic Encryption.", "authors": ["Jincheol Ha", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mincheol Son"], "summary": "A transciphering framework converts a symmetric ciphertext into a homomorphic ciphertext on the server-side, reducing computational and communication overload on the client-side. In Asiacrypt 2021, <PERSON> et al. proposed the\\(\\mathsf {RtF}\\)framework that supports approximate computation. In this paper, we propose a family ofnoisyciphers, dubbed\\(\\mathsf {R<PERSON><PERSON>}\\), with a novel design strategy of introducing noise to a symmetric cipher of a low algebraic degree. With this strategy, the multiplicative complexity of the cipher is significantly reduced, compared to existing HE-friendly ciphers, without degrading the overall security. More precisely, given a moderate block size (16 to 64),\\(\\mathsf {Rubato}\\)enjoys a low multiplicative depth (2 to 5) and a small number of multiplications per encrypted word (2.1 to 6.25) at the cost of slightly larger ciphertext expansion (1.26 to 1.31). The security of\\(\\mathsf {Rubato}\\)is supported by comprehensive analysis including symmetric and LWE cryptanalysis. Compared to\\(\\mathsf {HERA}\\)within the\\(\\mathsf {RtF}\\)framework, client-side and server-side throughput is improved by 22.9% and 32.2%, respectively, at the cost of only 1.6% larger ciphertext expansion.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_20"}, {"primary_key": "1657105", "vector": [], "sparse_vector": [], "title": "Highly Efficient OT-Based Multiplication Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ad <PERSON>"], "summary": "We present a new\\({\\text {OT}}\\)-based two-party multiplication protocol that is almost as efficient as Gilboa’s semi-honest protocol (Crypto ’99), but has a high-level of security against malicious adversaries without further compilation. The achieved security suffices for many applications, and, assuming DDH, can be cheaply compiled into full security.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_7"}, {"primary_key": "1657106", "vector": [], "sparse_vector": [], "title": "Garbled Circuits with Sublinear Evaluator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A recent line of work, Stacked Garbled Circuit (SGC), showed that Garbled Circuit (GC) can be improved for functions that include conditional behavior. SGC relieves the communication bottleneck of 2PC by only sending enough garbled material for a single branch out of thebtotal branches. Hence, communication is sublinear in the circuit size. However, both the evaluator and the generator pay in computation and perform at least factor\\(\\log b\\)extrawork as compared to standard GC. We extend the sublinearity of SGC toalsoinclude the work performed by the GC evaluator\\(E\\); thus we achieve a fully sublinear\\(E\\), which is essential when optimizing for the online phase. We formalize our approach as a garbling scheme called\\(\\mathsf {GCWise}\\):GC WIth Sublinear Evaluator. We show one attractive and immediate application, Garbled PIR, a primitive that marries GC with Private Information Retrieval. Garbled PIR allows the GC to non-interactively and sublinearly access a privately indexed element from a publicly known database, and then use this element in continued GC evaluation.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_2"}, {"primary_key": "1657107", "vector": [], "sparse_vector": [], "title": "EpiGRAM: Practical Garbled RAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garbled RAM (GRAM) is a powerful technique introduced by <PERSON> and <PERSON><PERSON><PERSON> that equips Garbled Circuit (GC) with a sublinear cost RAM without adding rounds of interaction. While multiple GRAM constructions are known, none are suitable for practice, due to costs that have high constants and poor scaling. We present the first GRAM suitable for practice. For computational security parameter\\(\\kappa \\)and for a size-nRAM that stores blocks of size\\(w = \\varOmega (\\log ^2n)\\)bits, our GRAM incurs amortized\\(O(w \\cdot \\log ^2 n \\cdot \\kappa )\\)communication and computation per access. We evaluate the concrete cost of our GRAM; our approach outperforms trivial linear-scan-based RAM for as few as 512 128-bit elements.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_1"}, {"primary_key": "1657108", "vector": [], "sparse_vector": [], "title": "Families of SNARK-Friendly 2-Chains of Elliptic Curves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "At CANS’20, <PERSON> and <PERSON><PERSON><PERSON><PERSON> introduced a new 2-chain of pairing-friendly elliptic curves for recursive zero-knowledge Succinct Non-interactive ARguments of Knowledge (zk-SNARKs) made of the former BLS12-377 curve (a <PERSON><PERSON>–<PERSON> curve over a 377-bit prime field) and the new BW6-761 curve (a <PERSON><PERSON><PERSON><PERSON> curve of embedding degree 6 over a 761-bit prime field). First we generalise the curve construction, the pairing formulas (\\(e :\\mathbb G_1 \\times \\mathbb G_2 \\rightarrow \\mathbb G_T\\)) and the group operations to any BW6 curve defined on top of any BLS12 curve, forming a family of 2-chain pairing-friendly curves. Second, we investigate other possible 2-chain families made on top of the BLS12 and BLS24 curves. We compare BW6 to Cocks–<PERSON> curves of higher embedding degrees 8 and 12 (CP8, CP12) at the 128-bit security level. We derive formulas for efficient optimal ate and optimal Tate pairings on our new CP curves. We show that for both BLS12 and BLS24, the BW6 construction always gives the fastest pairing and curve arithmetic compared to Cocks-Pinch curves. Finally, we suggest a short list of curves suitable for Groth16 and KZG-based universal SNARKs and present an optimized implementation of these curves. Based on Groth16 and PlonK (a KZG-based SNARK) implementations in thegnarkecosystem, we obtain that the BLS12-377/BW6-761 pair is optimized for the former while the BLS24-315/BW6-672 pair is optimized for the latter.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_13"}, {"primary_key": "1657109", "vector": [], "sparse_vector": [], "title": "On IND-qCCA Security in the ROM and Its Applications - CPA Security Is Sufficient for TLS 1.3.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bounded IND-CCA security (IND-qCCA) is a notion similar to the traditional IND-CCA security, except the adversary is restricted to a constant numberqof decryption/decapsulation queries. We show in this work that IND-qCCA is easily obtained from any passively secure PKE in the (Q)ROM. That is, simply adding a confirmation hash or computing the key as the hash of the plaintext and ciphertext holds an IND-qCCA KEM. In particular, there is no need for derandomization or re-encryption as in the Fujisaki-Okamoto (FO) transform [15]. This makes the decapsulation process of such IND-qCCA KEM much more efficient than its FO-derived counterpart. In addition, IND-qCCA KEMs could be used in the recently proposed KEMTLS protocol [29] that requires IND-1CCA ephemeral key-exchange mechanisms, or in TLS 1.3. Then, using similar proof techniques, we show that CPA-secure KEMs are sufficient for the TLS 1.3 handshake to be secure, solving an open problem in the ROM. In turn, this implies that the PRF-ODH assumption used to prove the security of TLS 1.3 is not necessary and can be replaced by the CDH assumption in the ROM. We also highlight and briefly discuss several use cases of IND-1CCA KEMs in protocols and ratcheting primitives.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_22"}, {"primary_key": "1657110", "vector": [], "sparse_vector": [], "title": "SNARGs for P from Sub-exponential DDH and QR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We obtain publicly verifiable Succinct Non-Interactive Arguments (SNARGs) for arbitrary deterministic computations and bounded space non-deterministic computation from standard group-based assumptions, without relying on pairings. In particular, assuming the sub-exponential hardness of both the Decisional Diffie-Hellman (DDH) and Quadratic Residuosity (QR) assumptions, we obtain the following results, wherendenotes the length of the instance: A SNARG for any language that can be decided in non-deterministic timeTand spaceSwith communication complexity and verifier runtime\\((n+S) \\cdot T^{o(1)}\\). A SNARG for any language that can be decided in deterministic timeTwith communication complexity and verifier runtime\\(n \\cdot T^{o(1)}\\).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_18"}, {"primary_key": "1657111", "vector": [], "sparse_vector": [], "title": "Round-Optimal Black-Box Protocol Compilers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give black-box, round-optimal protocol compilers from semi-honest security to malicious security in the Random Oracle Model (ROM) and in the 1-out-of-2 OT correlations model. We use our compilers to obtain the following results: A two-round, two-party protocol secure against malicious adversaries in the random oracle model making black-box use of a two-round semi-honest secure protocol. Prior to our work, such a result was not known even considering special functionalities such as a two-round oblivious transfer. This result also implies the first constructions of two-round malicious (batch) OT/OLE in the random oracle model based on the black-box use of two-round semi-honest (batch) OT/OLE. A three-round multiparty secure computation protocol in the random oracle model secure against malicious adversaries that is based on the black-box use of two-round semi-honest OT. This protocol matches a known round complexity lower bound due to <PERSON> et al. (ITCS’20) and is based on a minimal cryptographic hardness assumption. A two-round, multiparty secure computation protocol in the 1-out-of-2 OT correlations model that is secure against malicious adversaries and makes black-box use of cryptography. This gives new round-optimal protocols for computing arithmetic branching programs that are statistically secure and makes black-box use of the underlying field. As a contribution of independent interest, we provide a new variant of the IPS compiler (<PERSON><PERSON>, <PERSON> and <PERSON>, Crypto 2008) in the two-round setting, where we relax requirements on the IPS “inner protocol” by strengthening the “outer protocol”.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_8"}, {"primary_key": "1657112", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from LPN over $\\mathbb {F}_p$, DLIN, and PRGs in NC0.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we study what minimal sets of assumptions suffice for constructing indistinguishability obfuscation (\\(i\\mathcal {O}\\)). We prove: Theorem(Informal):Assume sub-exponential security of the following assumptions: –the Learning Parity with Noise (\\(\\mathsf {LPN}\\)) assumption over general prime fields\\(\\mathbb {F}_p\\)with polynomially many\\(\\mathsf {LPN}\\)samples and error rate\\(1/k^\\delta \\), wherekis the dimension of the\\(\\mathsf {LPN}\\)secret, and\\(\\delta >0\\)is any constant; –the existence of a Boolean Pseudo-Random Generator (\\(\\mathsf {PRG}\\)) in\\(\\mathsf {NC}^0\\)with stretch\\(n^{1+\\tau }\\), wherenis the length of the\\(\\mathsf {PRG}\\)seed, and\\(\\tau >0\\)is any constant; –the Decision Linear (\\(\\mathsf {DLIN}\\)) assumption on symmetric bilinear groups of prime order. Then, (subexponentially secure) indistinguishability obfuscation for all polynomial-size circuits exists. Further, assuming only polynomial security of the aforementioned assumptions, there exists collusion resistant public-key functional encryption for all polynomial-size circuits. This removes the reliance on the Learning With Errors (LWE) assumption from the recent work of [<PERSON>, <PERSON>, <PERSON>’21]. As a consequence, we obtain the first fully homomorphic encryption scheme that does not rely on any lattice-based hardness assumption. Our techniques feature a new notion of randomized encoding called Preprocessing Randomized Encoding (PRE), that essentially can be computed in the exponent of pairing groups. When combined with other new techniques, PRE gives a much more streamlined construction of\\(i\\mathcal {O}\\)while still maintaining reliance only on well-studied assumptions.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_23"}, {"primary_key": "1657113", "vector": [], "sparse_vector": [], "title": "Sine Series Approximation of the Mod Function for Bootstrapping of Approximate HE.", "authors": ["Cha<PERSON><PERSON>", "<PERSON>"], "summary": "While it is well known that the sawtooth function has a point-wise convergent Fourier series, the rate of convergence is not the best possible for the application of  approximating the mod function in small intervals around multiples of the modulus. We show a different sine series, such that the sine series of ordernhas error\\(O(\\epsilon ^{2n+1})\\)for approximating the mod function in\\(\\epsilon \\)-sized intervals around multiples of the modulus. Moreover, the resulting polynomial, after Taylor series approximation of the sine function, has small coefficients, and the whole polynomial can be computed at a precision that is only slightly larger than\\(-(2n+1)\\log \\epsilon \\), the precision of approximation being sought. This polynomial can then be used to approximate the mod function to almost arbitrary precision, and hence allows practical CKKS-HE bootstrapping with arbitrary precision. We validate our approach by an implementation and obtain 100 bit precision bootstrapping as well as improvements over prior work even at lower precision.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_17"}, {"primary_key": "1657114", "vector": [], "sparse_vector": [], "title": "Secure Non-interactive Simulation: Feasibility and Rate.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A natural solution to increase the efficiency of secure computation will be to non-interactively and securely transform diverse inexpensive-to-generate correlated randomness, like, joint samples from noise sources, into correlations useful for secure computation protocols. Motivated by this general application for secure computation, our work introduces the notion ofsecure non-interactive simulation(SNIS). Parties receive samples of correlated randomness, and they, without any interaction, securely convert them into samples from another correlated randomness. Our work presents a simulation-based security definition for SNIS and initiates the study of the feasibility and efficiency of SNIS. We also study SNIS among fundamental correlated randomnesses like random samples from the binary symmetric and binary erasure channels, represented by\\(\\mathsf {BSS}\\)and\\(\\mathsf {BES}\\), respectively. We show the impossibility of interconversion between\\(\\mathsf {BSS}\\)and\\(\\mathsf {BES}\\)samples. Next, we prove that a SNIS of a\\(\\mathsf {BES} (\\varepsilon ')\\)sample (a\\(\\mathsf {BES}\\)with noise characteristic\\(\\varepsilon '\\)) from\\(\\mathsf {BES} (\\varepsilon )\\)is feasible if and only if\\((1-\\varepsilon ') = (1-\\varepsilon )^k\\), for some\\(k\\in \\mathbb {N}\\). In this context, we prove that all SNIS constructions must be linear. Furthermore, if\\((1-\\varepsilon ') = (1-\\varepsilon )^k\\), then the rate of simulating multiple independent\\(\\mathsf {BES} (\\varepsilon ')\\)samples is at most 1/k, which is also achievable using (block) linear constructions. Finally, we show that a SNIS of a\\(\\mathsf {BSS} (\\varepsilon ')\\)sample from\\(\\mathsf {BSS} (\\varepsilon )\\)samples is feasible if and only if\\((1-2\\varepsilon ')=(1-2\\varepsilon )^k\\), for some\\(k\\in \\mathbb {N}\\). Interestingly, there are linear as well as non-linear SNIS constructions. When\\((1-2\\varepsilon ')=(1-2\\varepsilon )^k\\), we prove that the rate of aperfectly secureSNIS is at most 1/k, which is achievable using linear and non-linear constructions. Our technical approach algebraizes the definition of SNIS and proceeds via Fourier analysis. Our work develops general analysis methodologies for Boolean functions, explicitly incorporating cryptographic security constraints. Our work also proves strong forms ofstatistical-to-perfect securitytransformations: one can error-correct a statistically secure SNIS to make it perfectly secure. We show a connection of our research withhomogeneous Boolean functionsanddistance-invariant codes, which may be of independent interest.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_27"}, {"primary_key": "1657115", "vector": [], "sparse_vector": [], "title": "Watermarking PRFs Against Quantum Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of software watermarking against quantum adversaries. A quantum adversary generates aquantum stateas a pirate software that potentially removes an embedded message from aclassicalmarked software. Extracting an embedded message from quantum pirate software is difficult since measurement could irreversibly alter the quantum state. In software watermarking against classical adversaries, a message extraction algorithm crucially uses the (input-output) behavior of a classical pirate software to extract an embedded message. Even if we instantiate existing watermarking PRFs with quantum-safe building blocks, it is not clear whether they are secure against quantum adversaries due to the quantum-specific property above. Thus, we need entirely new techniques to achieve software watermarking against quantum adversaries. In this work, we define secure watermarking PRFs for quantum adversaries (unremovability against quantum adversaries). We also present two watermarking PRFs as follows. We construct a privately extractable watermarking PRF against quantum adversaries from the quantum hardness of the learning with errors (LWE) problem. The marking and extraction algorithms use a public parameter and a private extraction key, respectively. The watermarking PRF is unremovable even if adversaries have (the public parameter and) access to the extraction oracle, which returns a result of extraction for a queried quantum circuit. We construct a publicly extractable watermarking PRF against quantum adversaries from indistinguishability obfuscation (IO) and the quantum hardness of the LWE problem. The marking and extraction algorithms use a public parameter and a public extraction key, respectively. The watermarking PRF is unremovable even if adversaries have the extraction key (and the public parameter). We develop a quantum extraction technique to extract information (a classical string) from a quantum state without destroying the state too much. We also introduce the notion of extraction-less watermarking PRFs as a crucial building block to achieve the results above by combining the tool with our quantum extraction technique.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_18"}, {"primary_key": "1657116", "vector": [], "sparse_vector": [], "title": "High-Precision Bootstrapping for Approximate Homomorphic Encryption by <PERSON><PERSON><PERSON> V<PERSON>ce Minimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Cheon-Kim-Kim-Song (CKKS) scheme (Asiacrypt’17) is one of the most promising homomorphic encryption (HE) schemes as it enables privacy-preserving computing over real (or complex) numbers. It is known that bootstrapping is the most challenging part of the CKKS scheme. Further, homomorphic evaluation of modular reduction is the core of the CKKS bootstrapping. As modular reduction is not represented by the addition and multiplication of complex numbers, approximate polynomials for modular reduction should be used. The best-known techniques (Eurocrypt’21) use a polynomial approximation for trigonometric functions and their composition. However, all the previous methods are based on an indirect approximation, and thus it requires lots of multiplicative depth to achieve high accuracy. This paper proposes a direct polynomial approximation of modular reduction for CKKS bootstrapping, which is optimal in error variance and depth. Further, we propose an efficient algorithm, namely the lazy baby-step giant-step (BSGS) algorithm, to homomorphically evaluate the approximate polynomial, utilizing the lazy relinearization/rescaling technique. The lazy-BSGS reduces the computational complexity by half compared to the ordinary BSGS algorithm. The performance improvement for the CKKS scheme by the proposed algorithm is verified by implementation using HE libraries. The implementation results show that the proposed method has a multiplicative depth of 10 for modular reduction to achieve the state-of-the-art accuracy, while the previous methods have depths of 11 to 12. Moreover, we achieve higher accuracy within a small multiplicative depth, for example, 93-bit within multiplicative depth 11.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_19"}, {"primary_key": "1657117", "vector": [], "sparse_vector": [], "title": "One-Shot Fiat-Shamir-Based NIZK Arguments of Composite Residuosity and Logarithmic-Size Ring Signatures in the Standard Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The standard model security of the Fiat-<PERSON>hamir transform has been an active research area for many years. In breakthrough results, <PERSON><PERSON><PERSON> al.(STOC’19) and <PERSON><PERSON><PERSON><PERSON><PERSON> (Crypto’19) showed that, under the Learning-With-Errors (\\(\\mathsf {LWE}_{}\\)) assumption, it provides soundness by applying correlation-intractable (CI) hash functions to so-calledtrapdoor\\(\\varSigma \\)-protocols. In order to be compatible with CI hash functions based on standard\\(\\mathsf {LWE}_{}\\)assumptions with polynomial approximation factors, all known such protocols have been obtained via parallel repetitions of a basic protocol with binary challenges. In this paper, we consider languages related to <PERSON><PERSON><PERSON>’s composite residuosity assumption (\\(\\mathsf {DCR}\\)) for which we give the first trapdoor\\(\\varSigma \\)-protocols providing soundness in one shot, via exponentially large challenge spaces. This improvement is analogous to the one enabled by <PERSON><PERSON><PERSON><PERSON> over the original Fiat-Shamir protocol in the random oracle model. Using the correlation-intractable hash function paradigm, we then obtain simulation-sound NIZK arguments showing that an element of\\(\\mathbb {Z}_{N^2}^*\\)is a composite residue, which opens the door to space-efficient applications in the standard model. As a concrete example, we build logarithmic-size ring signatures (assuming a common reference string) with the shortest signature length among schemes based on standard assumptions in the standard model. We prove security under the\\(\\mathsf {DCR}\\)and\\(\\mathsf {LWE}_{}\\)assumptions, while keeping the signature size comparable with that of random-oracle-based schemes.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_17"}, {"primary_key": "1657118", "vector": [], "sparse_vector": [], "title": "Multi-Designated Receiver Signed Public Key Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper introduces a new type of public-key encryption scheme, called Multi-Designated Receiver Signed Public Key Encryption (\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)), which allows a sender to select a set of designated receivers and both encrypt and sign a message that only these receivers will be able to read and authenticate (confidentialityandauthenticity). An\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)scheme provides several additional security properties which allow for a fundamentally new type of communication not considered before. Namely, it satisfiesconsistency—a dishonest sender cannot make different receivers receive different messages—off-the-record—a dishonest receiver cannot convince a third party of what message was sent (e.g., by selling their secret key), because dishonest receivers have the ability to forge signatures—andanonymity—parties that are not in the set of designated receivers cannot identify who the sender and designated receivers are. We give a construction of an\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)scheme from standard assumptions. At the core of our construction lies yet another new type of public-key encryption scheme, which is of independent interest: Public Key Encryption for Broadcast (\\(\\textrm{PKEBC}\\)) which provides all the security guarantees of\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)schemes, except authenticity. We note that\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)schemes give strictly more guarantees than Multi-Designated Verifier Signature (\\({\\textrm{MDVS}}\\)) schemes with privacy of identities. This in particular means that our\\(\\textrm{M}{\\mathrm{DRS-PKE}}\\)construction yields the first\\({\\textrm{MDVS}}\\)scheme with privacy of identities from standard assumptions. The only prior construction of such schemes was based on Verifiable Functional Encryption for general circuits (Damgård et al., TCC ’20).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_22"}, {"primary_key": "1657119", "vector": [], "sparse_vector": [], "title": "Approximate Divisor Multiples - Factoring with Only a Third of the Secret CRT-Exponents.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We address Partial Key Exposure attacks on CRT-RSA on secret exponents\\(d_p, d_q\\)with small public exponente. For constanteit is known that the knowledge of half of the bits of one of\\(d_p, d_q\\)suffices to factor the RSA modulus<PERSON>by Coppersmith’s famousfactoring with a hintresult. We extend this setting to non-constante. Somewhat surprisingly, our attack shows that RSA witheof size\\(N^{\\frac{1}{12}}\\)is most vulnerable to Partial Key Exposure, since in this case only a third of the bits of both\\(d_p, d_q\\)suffices to factorNin polynomial time, knowing either most significant bits (MSB) or least significant bits (LSB). Let\\(ed_p = 1 + k(p-1)\\)and\\(ed_q = 1 + \\ell (q-1)\\). On the technical side, we find the factorization ofNin a novel two-step approach. In a first step we recoverkand\\(\\ell \\)in polynomial time, in the MSB case completely elementary and in the LSB case using <PERSON><PERSON>’s lattice-based method. We then obtain the prime factorization of<PERSON>by computing the root of a univariate polynomial modulokpfor our knownk. This can be seen as an extension of <PERSON><PERSON><PERSON><PERSON>’sapproximate divisoralgorithm to the case ofapproximate divisor multiplesfor some known multiplekof an unknown divisorpofN. The point ofapproximate divisor multiplesis that the unknown that is recoverable in polynomial time grows linearly with the size of the multiplek. Our resulting Partial Key Exposure attack with known MSBs is completely rigorous, whereas in the LSB case we rely on a standard Coppersmith-type heuristic. We experimentally verify our heuristic, thereby showing that in practice we reach our asymptotic bounds already using small lattice dimensions. Thus, our attack is highly efficient.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_6"}, {"primary_key": "1657120", "vector": [], "sparse_vector": [], "title": "Information-Combining Differential Fault Attacks on DEFAULT.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Differential fault analysis (DFA) is a very powerful attack vector for implementations of symmetric cryptography. Most countermeasures are applied at the implementation level. At ASIACRYPT 2021, <PERSON><PERSON><PERSON> et al. proposed a design strategy that aims to provide inherent cipher level resistance against DFA by using S-boxes with linear structures. They argue that in their instantiation, the block cipherDEFAULT, a DFA adversary can learn at most 64 of the 128 key bits, so the remaining brute-force complexity of\\(2^{64}\\)is impractical. In this paper, we show that a DFA adversary can combine information across rounds to recover the full key, invalidating their security claim. In particular, we observe that such ciphers exhibit large classes of equivalent keys that can be represented efficiently in normalized form using linear equations. We exploit this in combination with the specifics ofDEFAULT’s strong key schedule to recover the key using less than 100 faulty computation and negligible time complexity. Moreover, we show that even an idealized version ofDEFAULTwith independent round keys is vulnerable to our information-combining attacks based on normalized keys.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_7"}, {"primary_key": "1657121", "vector": [], "sparse_vector": [], "title": "Anamorphic Encryption: Private Communication Against a Dictator.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptosystems have been developed over the years under the typical prevalent setting which assumes that the receiver’s key is kept secure from the adversary, and that the choice of the message to be sent is freely performed by the sender and is kept secure from the adversary as well. Under these fundamental and basic operational assumptions, modern Cryptography has flourished over the last half a century or so, with amazing achievements: New systems (including public-key Cryptography), beautiful and useful models (including security definitions such as semantic security), and new primitives (such as zero-knowledge proofs) have been developed. Furthermore, these fundamental achievements have been translated into actual working systems, and span many of the daily human activities over the Internet. However, in recent years, there is an overgrowing pressure from many governments to allow the government itself access to keys and messages of encryption systems (under various names: escrow encryption, emergency access, communication decency acts, etc.). Numerous non-direct arguments against such policies have been raised, such as “the bad guys can utilize other encryption system” so all other cryptosystems have to be declared illegal, or that “allowing the government access is an ill-advised policy since it creates a natural weak systems security point, which may attract others (to masquerade as the government).” It has remained a fundamental open issue, though, to show directly that the above mentioned efforts by a government (called here “a dictator” for brevity) which mandate breaking of the basic operational assumption (and disallowing other cryptosystems), is, in fact, a futile exercise. This is a direct technical point which needs to be made and has not been made to date. In this work, as a technical demonstration of the futility of the dictator’s demands, we invent the notion of “Anamorphic Encryption” which shows that even if the dictator gets the keys and the messages used in the system (before anything is sent) and no other system is allowed, there is a covert way within the context ofwell established public-key cryptosystemsfor an entity to immediately (with no latency) send piggybacked secure messages which are, in spite of the stringent dictator conditions, hidden from the dictator itself! We feel that this may be an important direct technical argument against the nature of governments’ attempts to police the use of strong cryptographic systems, and we hope to stimulate further works in this direction.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_2"}, {"primary_key": "1657122", "vector": [], "sparse_vector": [], "title": "Embedding the UC Model into the IITM Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Universal Composability is a widely used concept for the design and analysis of protocols. Since <PERSON><PERSON>’s original UC model and the model by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> several different models for universal composability have been proposed, including, for example, the IITM model, GNUC, CC, but also extensions and restrictions of the UC model, such as JUC, GUC, and SUC. These were motivated by the lack of expressivity of existing models, ease of use, or flaws in previous models. Cryptographers choose between these models based on their needs at hand (e.g., support for joint state and global state) or simply their familiarity with a specific model. While all models follow the same basic idea, there are huge conceptually differences, which raises fundamental and practical questions: (How) do the concepts and results proven in one model relate to those in another model? Do the different models and the security notions formulated therein capture the same classes of attacks? Most importantly, can cryptographers re-use results proven in one model in another model, and if so, how? In this paper, we initiate a line of research with the aim to address this lack of understanding, consolidate the space of models, and enable cryptographers to re-use results proven in other models. As a start, here we focus on <PERSON><PERSON>’s prominent UC model and the IITM model proposed by <PERSON><PERSON> et al. The latter is an interesting candidate for comparison with the UC model since it has been used to analyze a wide variety of protocols, supports a very general protocol class and provides, among others, seamless treatment of protocols with shared state, including joint and global state. Our main technical contribution is an embedding of the UC model into the IITM model showing that all UC protocols, security and composition results carry over to the IITM model. Hence, protocol designers can profit from the features of the IITM model while being able to use all their results proven in the UC model. We also show that, in general, one cannot embed the full IITM model into the UC model.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_9"}, {"primary_key": "1657123", "vector": [], "sparse_vector": [], "title": "Asymmetric PAKE with Low Computation and communication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In Crypto’21 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [25] showed anasymmetricpassword authenticated key exchange protocol (aPAKE) whose computational cost matches (symmetric) password authenticated key exchange (PAKE) and plain (i.e. unauthenticated) key exchange (KE). However, this minimal-cost aPAKE did not match prior aPAKE’s in round complexity, using 4 rounds assuming the client initiates compared to 2 rounds in an aPAKE of <PERSON> et al. [13]. In this paper we show two aPAKE protocols (but notstrongaPAKEs like [13,30]), which achieve optimal computational costandoptimal round complexity. Our protocols can be seen as variants of theEncrypted Key Exchange(EKE) compiler of Bellovin and <PERSON><PERSON>tt [7], which creates password-authenticated key exchange by password-encrypting messages in a key exchange protocol. Whereas <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> used this method to construct a PAKE by applying password-encryption to KE messages, we construct an aPAKE by password-encrypting messages of aunilaterally authenticatedKey Exchange (ua-KE). We present two versions of this compiler. The first usessaltedpassword hash and takes 2 rounds if the server initiates. The second uses unsalted password hash and takes a single simultaneous flow, thussimultaneously matching the minimal computational cost and the minimal round complexity of PAKE and KE. We analyze our aPAKE protocols assuming an Ideal Cipher (IC) on a group, and we analyze them as modular constructions from ua-KE realized via a universally composable Authenticated Key Exchange where the server usesone-time keys(otk-AKE). We also show that one-pass variants of 3DH and HMQV securely realize otk-AKE in the ROM. Interestingly, the two resulting concrete aPAKE’s use the exact same protocol messages as variants of EKE, and the only difference between the symmetric PAKE (EKE) and asymmetric PAKE (our protocols) is in the key derivation equation.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_5"}, {"primary_key": "1657124", "vector": [], "sparse_vector": [], "title": "A Correlation Attack on Full SNOW-V and SNOW-Vi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, a method for searching correlations between the binary stream of Linear Feedback Shift Register (LFSR) and the keystream of SNOW-V and SNOW-Vi is presented based on the technique of approximation to composite functions. With the aid of the linear relationship between the four taps of LFSR input into Finite State Machine (FSM) at three consecutive clocks, we present an automatic search model based on the SAT/SMT technique and search out a series of linear approximation trails with high correlation. By exhausting the intermediate masks, we find a binary linear approximation with a correlation\\(-2^{-47.76}\\). Using such approximation, we propose a correlation attack on SNOW-V with an expected time complexity\\(2^{246.53}\\), a memory complexity\\(2^{238.77}\\)and\\(2^{237.5}\\)keystream words generated by the same key and Initial Vector (IV). For SNOW-Vi, we provide a binary linear approximation with the same correlation and mount a correlation attack with the same complexity as that of SNOW-V. To the best of our knowledge, this is the first known attack on full SNOW-V and SNOW-Vi, which is better than the exhaustive key search with respect to time complexity. The results indicate that neither SNOW-V nor SNOW-Vi can guarantee the 256-bit security level if we ignore the design constraint that the maximum length of keystream for a single pair of key and IV is less than\\(2^{64}\\).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_2"}, {"primary_key": "1657125", "vector": [], "sparse_vector": [], "title": "A Greater GIFT: Strengthening GIFT Against Statistical Cryptanalysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GIFT-64is a 64-bit block cipher with a 128-bit key that is more lightweight thanPRESENT. This paper provides a detailed analysis ofGIFT-64against differential and linear attacks. Our work complements automatic search methods for the best differential and linear characteristics with a careful manual analysis. This hybrid approach leads to new insights. In the differential setting, we theoretically explain the existence of differential characteristics with two active S-boxes per round and derive some novel properties of these characteristics. Furthermore, we prove that all optimal differential characteristics ofGIFT-64covering more than seven rounds must activate two S-boxes per round. We can construct all optimal characteristics by hand. In parallel to the work in the differential setting, we conduct a similar analysis in the linear setting. However, unlike the clear view in differential setting, the optimal linear characteristics ofGIFT-64must have at least one round activating only one S-box. Moreover, with the assistance of automatic searching methods, we identify 24GIFT-64variants achieving better resistance against differential attack while maintaining a similar security level against a linear attack. Since the new variants strengthenGIFT-64against statistical cryptanalysis, we claim that the number of rounds could be reduced from 28 to 26 for the variants. This observation enables us to create a cipher with lower energy consumption thanGIFT-64. Similarly to the case inGIFT-64, we do not claim any related-key security for the round-reduced variant as this is not relevant for most applications.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_5"}, {"primary_key": "1657126", "vector": [], "sparse_vector": [], "title": "Practical Post-Quantum Signature Schemes from Isomorphism Problems of Trilinear Forms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose a practical signature scheme based on the alternating trilinear form equivalence problem. Our scheme is inspired by the <PERSON><PERSON>ich-Mi<PERSON><PERSON><PERSON><PERSON><PERSON>’s zero-knowledge protocol for graph isomorphism, and can be served as an alternative candidate for the NIST’s post-quantum digital signatures. First, we present theoretical evidences to support its security, especially in the post-quantum cryptography context. The evidences are drawn from several research lines, including hidden subgroup problems, multivariate cryptography, cryptography based on group actions, the quantum random oracle model, and recent advances on isomorphism problems for algebraic structures in algorithms and complexity. Second, we demonstrate its potential for practical uses. Based on algorithm studies, we propose concrete parameter choices, and then implement a prototype. One concrete scheme achieves 128 bit security with public key size\\(\\approx 4100\\)bytes, signature size\\(\\approx 6800\\)bytes, and running times (key generation, sign, verify)\\(\\approx 0.8\\)ms on a common laptop computer.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_21"}, {"primary_key": "1657127", "vector": [], "sparse_vector": [], "title": "Short Pairing-Free Blind Signatures with Exponential Security.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes the first practical pairing-free three-move blind signature schemes that (1) are concurrently secure, (2) produce short signatures (i.e.,threeorfourgroup elements/scalars), and (3) are provably secure either in the generic group model (GGM) or the algebraic group model (AGM) under the (plain or one-more) discrete logarithm assumption (beyond additionally assuming random oracles). We also propose a partially blind version of one of our schemes. Our schemes do not rely on the hardness of the ROS problem (which can be broken in polynomial time) or of the mROS problem (which admits sub-exponential attacks). The only prior work with these properties is <PERSON>’s signature scheme (EUROCRYPT ’02), which was recently proved to be secure in the AGM by <PERSON><PERSON><PERSON> et al. (PKC ’22), but which also produces signatures twice as long as those from our scheme. The core of our proofs of security is a new problem, calledweightedfractionalROS (WFROS), for which we prove (unconditional) exponential lower bounds.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_27"}, {"primary_key": "1657128", "vector": [], "sparse_vector": [], "title": "A Fast and Simple Partially Oblivious PRF, with Applications.", "authors": ["Nirvan Tyagi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We build the first construction of a partially oblivious pseudorandom function (POPRF) that does not rely on bilinear pairings. Our construction can be viewed as combining elements of the 2HashDH OPRF of Jarecki, Kiayias, and Krawczyk with the Dodis-Yampolskiy PRF. We analyze our POPRF’s security in the random oracle model via reduction to a new one-more gap strong <PERSON><PERSON><PERSON><PERSON> inversion assumption. The most significant technical challenge is establishing confidence in the new assumption, which requires new proof techniques that enable us to show that its hardness is implied by theq-DL assumption in the algebraic group model. Our new construction is as fast as the current, standards-track OPRF 2HashDH protocol, yet provides a new degree of flexibility useful in a variety of applications. We show how POPRFs can be used to prevent token hoarding attacks against Privacy Pass, reduce key management complexity in the OPAQUE password authenticated key exchange protocol, and ensure stronger security for password breach alerting services.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_23"}, {"primary_key": "1657129", "vector": [], "sparse_vector": [], "title": "Non-Interactive Zero-Knowledge Proofs with Fine-Grained Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct thefirstnon-interactive zero-knowledge (NIZK) proof systems in the fine-grained setting where adversaries’ resources are bounded and honest users have no more resources than an adversary. More concretely, our setting is the\\(\\mathsf {NC^1}\\)-fine-grained setting, namely, all parties (including adversaries and honest participants) are in\\(\\mathsf {NC^1}\\). Our NIZK systems are for circuit satisfiability (SAT) under the worst-case assumption,\\(\\mathsf {NC^1}\\subsetneq \\mathsf{\\oplus L/poly}\\). As technical contributions, we propose two approaches to construct NIZKs in the\\(\\mathsf {NC^1}\\)-fine-grained setting. In stark contrast to the classical Fiat-Shamir transformation, both our approaches start with a simple\\(\\varSigma \\)-protocol and transform it into NIZKs for circuit SAT without random oracles. Additionally, our second approach firstly proposes afully homomorphic encryption(FHE) scheme in the fine-grained setting, which was not known before, as a building block. Compared with the first approach, the resulting NIZK only supports circuits with constant multiplicative depth, while its proof size is independent of the statement circuit size. Extending our approaches, we obtain two NIZK systems in the uniform reference string model and two non-interactive zaps (namely, non-interactive witness-indistinguishability proof systems in the plain model). While the previous constructions from Ball, Dachman-Soled, and <PERSON><PERSON><PERSON><PERSON> (CRYPTO 2020) require provers to run in polynomial-time, our constructions are the first one with provers in\\(\\mathsf {NC^1}\\).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_11"}, {"primary_key": "1657130", "vector": [], "sparse_vector": [], "title": "Optimal Broadcast Encryption and CP-ABE from Evasive Lattice Assumptions.", "authors": ["<PERSON><PERSON><PERSON>e"], "summary": "We present a new, simple candidate broadcast encryption scheme forNusers with parameter size\\(\\textsf {poly}(\\log N)\\). We prove security of our scheme under a non-standard variant of the LWE assumption where the distinguisher additionally receives short Gaussian pre-images while avoiding zeroizing attacks. This yields the first candidate optimal broadcast encryption that is plausibly post-quantum secure, and enjoys a security reduction to a simple assumption. As a secondary contribution, we present a candidate ciphertext-policy attribute-based encryption (CP-ABE) scheme for circuits of a-priori bounded polynomial depth where the parameter size is independent of the circuit size, and prove security under an additional non-standard assumption.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07085-3_8"}, {"primary_key": "1657131", "vector": [], "sparse_vector": [], "title": "Orientations and the Supersingular Endomorphism Ring Problem.", "authors": ["<PERSON>"], "summary": "We study two important families of problems in isogeny-based cryptography and how they relate to each other: computing the endomorphism ring of supersingular elliptic curves, and inverting the action of class groups on oriented supersingular curves. We prove that these two families of problems are closely related through polynomial-time reductions, assuming the generalised Riemann hypothesis. We identify two classes of essentially equivalent problems. The first class corresponds to the problem of computing the endomorphism ring oforiented curves. The security of a large family of cryptosystems (such as CSIDH) reduces to (and sometimes from) this class, for which there are heuristic quantum algorithms running in subexponential time. The second class corresponds to computing the endomorphism ring oforientable curves. The security of essentially all isogeny-based cryptosystems reduces to (and sometimes from) this second class, for which the best known algorithms are still exponential. Some of our reductions not only generalise, but also strengthen previously known results. For instance, it was known that in the particular case of curves defined over\\(\\mathbf {F}_p\\), the security of CSIDH reduces to the endomorphism ring problem in subexponential time. Our reductions imply that the security of CSIDH is actually equivalent to the endomorphism ring problem, under polynomial time reductions (circumventing arguments that proved such reductions unlikely).", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_13"}, {"primary_key": "1657132", "vector": [], "sparse_vector": [], "title": "A Complete Characterization of Game-Theoretically Fair, Multi-Party Coin Toss.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>’s celebrated lower bound (STOC’86) showed that ade factostrong fairness notion is impossible in 2-party coin toss, i.e., the corrupt party always has a strategy of biasing the honest party’s outcome by a noticeable amount. Nonetheless, <PERSON><PERSON>’s famous coin-tossing protocol (CRYPTO’81) achieves a strictly weaker “game-theoretic” notion of fairness—specifically, it is a 2-party coin toss protocol in which neither party can bias the outcometowards its own preference; and thus the honest protocol forms a Nash equilibrium in which neither party would want to deviate. Surprisingly, ann-party analog of <PERSON><PERSON>’s famous coin toss protocol was not studied till recently. The work by <PERSON> et al. (TCC’18) was the first to explore the feasibility of game-theoretically fairn-party coin toss in the presence of corrupt majority. We may assume that each party has a publicly stated preference for either the bit 0 or 1, and if the outcome agrees with the party’s preference, it obtains utility 1; else it obtains nothing. A natural game-theoretic formulation is to require that the honest protocol form a coalition-resistant Nash equilibrium, i.e., no coalition should have incentive to deviate from the honest behavior. <PERSON> et al. phrased this game-theoretic notion as “cooperative-strategy-proofness” or “CSP-fairness” for short. Unfortunately, <PERSON> et al. showed that under\\((n-1)\\)-sized coalitions, it is impossible to design such a CSP-fair coin toss protocol, unless all parties except one prefer the same bit. In this paper, we show that the impossibility of <PERSON> et al. is in fact not as broad as it may seem. When coalitions are majority but not\\(n-1\\)in size, we can indeed get feasibility results in some meaningful parameter regimes. We give a complete characterization of the regime in which CSP-fair coin toss is possible, by providing a matching upper- and lower-bound. Our complete characterization theorem also shows that the mathematical structure of game-theoretic fairness is starkly different from thede factostrong fairness notion in the multi-party computation literature.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_5"}, {"primary_key": "1657133", "vector": [], "sparse_vector": [], "title": "Anonymity of NIST PQC Round 3 KEMs.", "authors": ["<PERSON><PERSON>"], "summary": "This paper investigatesanonymityof all NIST PQC Round 3 KEMs: Classic McEliece, Kyber, NTRU, Saber, BIKE, FrodoKEM, HQC, NTRU Prime (Streamlined NTRU Prime and NTRU LPRime), and SIKE. We show the following results: NTRU is anonymous in the quantum random oracle model (QROM) if the underlying deterministic PKE is strongly disjoint-simulatable. NTRU is collision-free in the QROM. A hybrid PKE scheme constructed from NTRU as KEM and appropriate DEM is anonymous and robust. (Similar results for BIKE, FrodoKEM, HQC, NTRU LPRime, and SIKE hold except one of three parameter sets of HQC.) Classic McEliece is anonymous in the QROM if the underlying PKE is strongly disjoint-simulatable and a hybrid PKE scheme constructed from it as KEM and appropriate DEM is anonymous. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> pointed out that <PERSON><PERSON><PERSON> and <PERSON><PERSON> have a gap in the current IND-CCA security proof in the QROM (EUROCRYPT 2022). We found that Streamlined NTRU Prime has another technical obstacle for the IND-CCA security proof in the QROM. Those answer the open problem to investigate the anonymity and robustness of NIST PQC Round 3 KEMs posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (EUROCRYPT 2022). We use strong disjoint-simulatability of the underlying PKE of KEM and strong pseudorandomness and smoothness/sparseness of KEM as the main tools, which will be of independent interest.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-07082-2_20"}, {"primary_key": "1657134", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Candidate Obfuscators for Affine Determinant Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "At ITCS 2020, <PERSON><PERSON><PERSON> et al. proposed a candidate indistinguishability obfuscator (\\(i\\mathcal {O}\\)) for affine determinant programs (ADPs). The candidate is special since it directly applies specific randomization techniques to the underlyingADP, without relying on the hardness of traditional cryptographic assumptions like discrete-log or learning with errors. It is relatively efficient compared to the rest of the\\(i\\mathcal {O}\\)candidates. However, the obfuscation scheme requires further cryptanalysis since it was not known to be based on any well-formed mathematical assumptions. In this paper, we show cryptanalytic attacks on the\\(i\\mathcal {O}\\)candidate provided by <PERSON><PERSON><PERSON> et al. Our attack exploits the weakness of one of the randomization steps in the candidate. The attack applies to a fairly general class of programs. At the end of the paper we discuss plausible countermeasures to defend against our attacks.", "published": "2022-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-06944-4_22"}]