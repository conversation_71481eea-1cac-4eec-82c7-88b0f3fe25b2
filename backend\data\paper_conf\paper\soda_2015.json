[{"primary_key": "4501182", "vector": [], "sparse_vector": [], "title": "Welfare Maximization with Production Costs: A Primal Dual Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Welfare Maximization with Production Costs: A Primal Dual Approach<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.59 - 72Chapter DOI:https://doi.org/10.1137/1.*************.6PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study online combinatorial auctions with production costs proposed by <PERSON><PERSON> et al. [4] using the online primal dual framework. In this model, buyers arrive online, and the seller can produce multiple copies of each item subject to a non-decreasing marginal cost per copy. The goal is to allocate items to maximize social welfare less total production cost. For arbitrary (strictly convex and differentiable) production cost functions, we characterize the optimal competitive ratio achievable by online mechanisms/algorithms. We show that online posted pricing mechanisms, which are incentive compatible, can achieve competitive ratios arbitrarily close to the optimal, and construct lower bound instances on which no online algorithms, not necessarily incentive compatible, can do better. Our positive results improve or match the results in several previous work, e.g., <PERSON><PERSON> et al. [a], <PERSON><PERSON> et al. [4], and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [6]. Our lower bounds apply to randomized algorithms and resolve an open problem by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [6]. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "4501183", "vector": [], "sparse_vector": [], "title": "Subcubic Equivalences Between Graph Centrality Problems, APSP and Diameter.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "Measuring the importance of a node in a network is a major goal in the analysis of social networks, biological systems, transportation networks etc. Different centrality measures have been proposed to capture the notion of node importance. For example, the center of a graph is a node that minimizes the maximum distance to any other node (the latter distance is the radius of the graph). The median of a graph is a node that minimizes the sum of the distances to all other nodes. Informally, the betweenness centrality of a node w measures the fraction of shortest paths that have w as an intermediate node. Finally, the reach centrality of a node w is the smallest distance r such that any s-t shortest path passing through w has either s or t in the ball of radius r around w.The fastest known algorithms to compute the center and the median of a graph, and to compute the betweenness or reach centrality even of a single node take roughly cubic time in the number n of nodes in the input graph. It is open whether these problems admit truly subcubic algorithms, i.e. algorithms with running time Õ(n3–δ) for some constant δ > 01.We relate the complexity of the mentioned centrality problems to two classical problems for which no truly subcubic algorithm is known, namely All Pairs Shortest Paths (APSP) and Diameter. We show that Radius, Median and Betweenness Centrality are equivalent under subcubic reductions to APSP, i.e. that a truly subcubic algorithm for any of these problems implies a truly subcubic algorithm for all of them. We then show that Reach Centrality is equivalent to Diameter under subcubic reductions. The same holds for the problem of approximating Betweenness Centrality within any constant factor. Thus the latter two centrality problems could potentially be solved in truly subcubic time, even if APSP requires essentially cubic time.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "4501184", "vector": [], "sparse_vector": [], "title": "More Applications of the Polynomial Method to Algorithm Design.", "authors": ["<PERSON>", "<PERSON>", "Huacheng Yu"], "summary": "In low-depth circuit complexity, the polynomial method is a way to prove lower bounds by translating weak circuits into low-degree polynomials, then analyzing properties of these polynomials. Recently, this method found an application to algorithm design: <PERSON> (ST<PERSON> 2014) used it to compute all-pairs shortest paths in time on dense n-node graphs. In this paper, we extend this methodology to solve a number of problems in combinatorial pattern matching and Boolean algebra, considerably faster than previously known methods.First, we give an algorithm for Boolean Orthogonal Detection, which is to detect among two sets A,B ⊆ {0,1}dof size n if there is an x ∊ A and y ∊ B such that 〈x,y〉 = 0. For vectors of dimension d = c(n) log n, we solve Boolean Orthogonal Detection in n2–1/O(log c(n)) time by a <PERSON> randomized algorithm. We apply this as a subroutine in several other new algorithms:•In Batch Partial Match, we are given n query strings from from {0, 1, ⋆}c(n) log n (⋆ is a \"don't care\"), n strings from {0, 1}c(n)log n, and wish to determine for each query whether or not there is a string matching the query. We solve this problem in n2–1/O(logc(n)) time by a <PERSON> randomized algorithm.•Let t ≤ ν be integers. Given a DNF F on c log t variables with t terms, and v arbitrary assignments on the variables, F can be evaluated on all ν assignments in ν · t1–1/O(log c) time, with high probability.•There is a randomized algorithm that solves the Longest Common Substring with don't cares problem on two strings of length n in time.•Given two strings S, T of length n, there is a randomized algorithm that computes the length of the longest substring of S that has Edit-Distance less than k to a substring of T in time.•Symmetric Boolean Constraint Satisfaction Problems (CSPs) with n variables and m constraints are solvable in poly(m). 2n(1–1/O(log mn)) time.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "4501185", "vector": [], "sparse_vector": [], "title": "Testing Poisson Binomial Distributions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A Poisson Binomial distribution over n variables is the distribution of the sum of n independent <PERSON><PERSON><PERSON>. We provide a sample near-optimal algorithm for testing whether a distribution P supported on {0, …, n} to which we have sample access is a Poisson Binomial distribution, or far from all Poisson Binomial distributions. The sample complexity of our algorithm is O(n1/4) to which we provide a matching lower bound. We note that our sample complexity improves quadratically upon that of the naive \"learn followed by tolerant-test\" approach, while instance optimal identity testing [VV14] is not applicable since we are looking to simultaneously test against a whole family of distributions.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "4501186", "vector": [], "sparse_vector": [], "title": "The Complexity of Estimating Rényi Entropy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It was recently shown that estimating the Shannon entropy H(p) of a discrete k-symbol distribution p requires Θ(k/ log k) samples, a number that grows nearlinearly in the support size. In many applications H(p) can be replaced by the more general <PERSON><PERSON>yi entropy of order α, Hα(p). We determine the number of samples needed to estimate Hα(p) for all α, showing that α 1 requires near-linear, roughly k samples, but integer α > 1 requires only Θ(k1−1/α) samples. In particular, estimating H2(p), which arises in security, DNA reconstruction, closeness testing, and other applications, requires only samples. The estimators achieving these bounds are simple and run in time linear in the number of samples.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "4501187", "vector": [], "sparse_vector": [], "title": "A quasi-PTAS for the Two-Dimensional Geometric Knapsack Problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A quasi-PTAS for the Two-Dimensional Geometric Knapsack <PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.1491 - 1505Chapter DOI:https://doi.org/10.1137/1.*************.98PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the two-dimensional geometric knapsack problem defined as follows. Given a collection of rectangular axis-parallel items with weights, we want to find a maximum weight subset of the items that can be packed into a rectangular knapsack, i.e., which can be assigned positions within the knapsack such that the items are pair-wise non-overlapping. The goal is to compute the optimal collection of items together with a feasible packing. The problem arises naturally in several applications, and various special cases of the problem have been studied. For the general case the best known result is a (2 + ε)-approximation algorithm, while the only hardness result is NP-hardness. Our main result is a (1 + ε)-approximation algorithm that runs in quasi-polynomial time, provided that the input data consists of (quasi-)polynomially bounded integers. We achieve this result in the setting with and without allowing rotation of the items. Our key technical contribution is to show the existence of a partition for the knapsack into a small number of rectangular boxes. Intuitively, this partition describes the segmentation of the knapsack in some near-optimal solution into large items, areas containing items that are high and thin, and areas containing items that are small and wide. Handling the interaction between these three types of items is a core bottleneck in the design of approximation algorithms for the problem and our partition allows to control this at only marginal cost. In particular, it is so powerful that we do not even need to round the sizes of the items, which is a canonical step in algorithms for geometric knapsack, geometric bin-packing, etc. Finally, we present new algorithms for twodimensional knapsack and two-dimensional bin-packing under (1 + ∊)-resource augmentation with a substantially better running time dependence on ∊ (although at the expense of requiring quasi-polynomial time). The objective value of our computed solutions is as good as the optimum without additional resources. Here we exploit a recent result showing the existence of certain low-complexity low weight separators for sets of non-overlapping rectangles. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "4501188", "vector": [], "sparse_vector": [], "title": "Fast Algorithms for Online Stochastic Convex Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the online stochastic Convex Programming (CP) problem, a very general version of stochastic online problems which allows arbitrary concave objectives and convex feasibility constraints. Many well-studied problems like online stochastic packing and covering, online stochastic matching with concave returns, etc. form a special case of online stochastic CP. We present fast algorithms for these problems, which achieve near-optimal regret guarantees for both the i.i.d. and the random permutation models of stochastic inputs. When applied to the special case online packing, our ideas yield a simpler and faster primal-dual algorithm for this well studied problem, which achieves the optimal competitive ratio. Our techniques make explicit the connection of primal-dual paradigm and online learning to online stochastic CP.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}, {"primary_key": "4501189", "vector": [], "sparse_vector": [], "title": "Compatible Connectivity-Augmentation of Planar Disconnected Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by applications to graph morphing, we consider the following compatible connectivity-augmentation problem: We are given a labelled n-vertex planar graph, G, that has r ≥ 2 connected components, and k ≥ 2 isomorphic planar straight-line drawings, G1, …, G2, of G. We wish to augment G by adding vertices and edges to make it connected in such a way that these vertices and edges can be added to G1, …, G2 as points and straight-line segments, respectively, to obtain k planar straight-line drawings isomorphic to the augmentation of G. We show that adding Θ(nr1–1/k) edges and vertices to G is always sufficient and sometimes necessary to achieve this goal. The upper bound holds for all r ∊ {2, …, n} and k ≥ 2 and is achievable by an algorithm whose running time is O(nr1–1/k) for k = O(1) and whose running time is O(kn2) for general values of k. The lower bound holds for all r ∊ {2, …, n/4} and k ≥ 2.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "4501190", "vector": [], "sparse_vector": [], "title": "Dynamic Facility Location via Exponential Clocks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Dynamic Facility Location via Exponential Clocks<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.708 - 721Chapter DOI:https://doi.org/10.1137/1.*************.48PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The dynamic facility location problem is a generalization of the classic facility location problem proposed by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> to model the dynamics of evolving social/infrastructure networks. The generalization lies in that the distance metric between clients and facilities changes over time. This leads to a trade-off between optimizing the classic objective function and the “stability” of the solution: there is a switching cost charged every time a client changes the facility to which it is connected. While the standard linear program (LP) relaxation for the classic problem naturally extends to this problem, traditional LP-rounding techniques do not, as they are often sensitive to small changes in the metric resulting in frequent switches. We present a new LP-rounding algorithm for facility location problems, which yields the first constant approximation algorithm for the dynamic facility location problem. Our algorithm installs competing exponential clocks on the clients and facilities, and connect every client by the path that repeatedly follows the smallest clock in the neighborhood. The use of exponential clocks gives rise to several properties that distinguish our approach from previous LP-roundings for facility location problems. In particular, we use no clustering and we allow clients to connect through paths of arbitrary lengths. In fact, the clustering-free nature of our algorithm is crucial for applying our LP-rounding approach to the dynamic problem. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048Key words:facility location problems, exponential clocks, approximation algorithms", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "4501191", "vector": [], "sparse_vector": [], "title": "A dynamic model of barter exchange.", "authors": ["<PERSON>", "Itai Ashlagi", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of efficient operation of a barter exchange platform for indivisible goods. We introduce a dynamic model of barter exchange where in each period one agent arrives with a single item she wants to exchange for a different item. We study a homogeneous and stochastic environment: an agent is interested in the item possessed by another agent with probability p, independently for all pairs of agents. We consider two settings with respect to the types of allowed exchanges: a) Only two-way cycles, in which two agents swap their items, b) Two or three-way cycles. The goal of the platform is to minimize the average waiting time of an agent.Somewhat surprisingly, we find that in each of these settings, a policy that conducts exchanges in a greedy fashion is near optimal, among a large class of policies that includes batching policies. Further, we find that for small p, allowing three-cycles can greatly improve the waiting time over the two-cycles only setting. Specifically, we find that a greedy policy achieves an average waiting time of Θ(1/p2) in setting a), and Θ(1/p3/2) in setting b). Thus, a platform can achieve the smallest waiting times by using a greedy policy, and by facilitating three cycles, if possible.Our findings are consistent with empirical and computational observations which compare batching policies in the context of kidney exchange programs.MSC codesbarterrandom graphsdynamicskidney exchange", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.129"}, {"primary_key": "4501192", "vector": [], "sparse_vector": [], "title": "Combinatorial Algorithm for Restricted Max-Min Fair Allocation.", "authors": ["Chidam<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the basic allocation problem of assigning resources to players so as to maximize fairness. This is one of the few natural problems that enjoys the intriguing status of having a better estimation algorithm than approximation algorithm. Indeed, a certain configuration-LP can be used to estimate the value of the optimal allocation to within a factor of 4 + ε.In contrast, however, the best known approximation algorithm for the problem has an unspecified large constant guarantee. In this paper we significantly narrow this gap by giving a 13-approximation algorithm for the problem. Our approach develops a local search technique introduced by <PERSON><PERSON><PERSON> [11] for hypergraph matchings, and later used in this context by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [1]. For our local search procedure to terminate in polynomial time, we introduce several new ideas such as lazy updates and greedy players. Besides the improved approximation guarantee, the highlight of our approach is that it is purely combinatorial and uses the configuration-LP only in the analysis.MSC codesapproximation algorithmsfair allocationefficient local search", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "4501193", "vector": [], "sparse_vector": [], "title": "A Fully Polynomial-Time Approximation Scheme for Speed Scaling with Sleep State.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study classical deadline-based preemptive scheduling of jobs in a computing environment equipped with both dynamic speed scaling and sleep state capabilities: Each job is specified by a release time, a deadline and a processing volume, and has to be scheduled on a single, speed-scalable processor that is supplied with a sleep state. In the sleep state, the processor consumes no energy, but a constant wake-up cost is required to transition back to the active state. In contrast to speed scaling alone, the addition of a sleep state makes it sometimes beneficial to accelerate the processing of jobs in order to transition the processor to the sleep state for longer amounts of time and incur further energy savings. The goal is to output a feasible schedule that minimizes the energy consumption. Since the introduction of the problem by <PERSON><PERSON> et al. [17], its exact computational complexity has been repeatedly posed as an open question (see e.g. [2,9,16]). The currently best known upper and lower bounds are a 4/3-approximation algorithm and NP-hardness due to [2] and [2,18], respectively.We close the aforementioned gap between the upper and lower bound on the computational complexity of speed scaling with sleep state by presenting a fully polynomial-time approximation scheme for the problem. The scheme is based on a transformation to a non-preemptive variant of the problem, and a discretization that exploits a carefully defined lexicographical ordering among schedules.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "4501194", "vector": [], "sparse_vector": [], "title": "Wavelet Trees Meet Suffix Trees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present an improved wavelet tree construction algorithm and discuss its applications to a number of rank/select problems for integer keys and strings. Given a string of length n over an alphabet of size $\\sigma\\leq n$, our method builds the wavelet tree in $O(n \\log \\sigma/ \\sqrt{\\log{n}})$ time, improving upon the state-of-the-art algorithm by a factor of $\\sqrt{\\log n}$. As a consequence, given an array of n integers we can construct in $O(n \\sqrt{\\log n})$ time a data structure consisting of $O(n)$ machine words and capable of answering rank/select queries for the subranges of the array in $O(\\log n / \\log \\log n)$ time. This is a $\\log \\log n$-factor improvement in query time compared to <PERSON> and <PERSON>\\u{a}tra\\c{s}cu and a $\\sqrt{\\log n}$-factor improvement in construction time compared to <PERSON><PERSON><PERSON> et al. Next, we switch to stringological context and propose a novel notion of wavelet suffix trees. For a string w of length n, this data structure occupies $O(n)$ words, takes $O(n \\sqrt{\\log n})$ time to construct, and simultaneously captures the combinatorial structure of substrings of w while enabling efficient top-down traversal and binary search. In particular, with a wavelet suffix tree we are able to answer in $O(\\log |x|)$ time the following two natural analogues of rank/select queries for suffixes of substrings: for substrings x and y of w count the number of suffixes of x that are lexicographically smaller than y, and for a substring x of w and an integer k, find the k-th lexicographically smallest suffix of x. We further show that wavelet suffix trees allow to compute a run-length-encoded Burrows-Wheeler transform of a substring x of w in $O(s \\log |x|)$ time, where s denotes the length of the resulting run-length encoding. This answers a question by Cormode and Muthukrishnan, who considered an analogous problem for Lempel-Ziv compression.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "4501195", "vector": [], "sparse_vector": [], "title": "Minimum Forcing Sets for <PERSON><PERSON> Folding Patterns.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the study of forcing sets in mathematical origami. The origami material folds flat along straight line segments called creases, each of which is assigned a folding direction of mountain or valley. A subset F of creases is forcing if the global folding mountain/valley assignment can be deduced from its restriction to F. In this paper we focus on one particular class of foldable patterns called Miura-ori, which divide the plane into congruent parallelograms using horizontal lines and zigzag vertical lines. We develop efficient algorithms for constructing a minimum forcing set of a Miura-ori map, and for deciding whether a given set of creases is forcing or not. We also provide tight bounds on the size of a forcing set, establishing that the standard mountain-valley assignment for the Miura-ori is the one that requires the most creases in its forcing sets. Additionally, given a partial mountain/valley assignment to a subset of creases of a Miura-ori map, we determine whether the assignment domain can be extended to a locally flat-foldable pattern on all the creases. At the heart of our results is a novel correspondence between flat-foldable Miura-ori maps and 3-colorings of grid graphs.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "4501196", "vector": [], "sparse_vector": [], "title": "The optimal absolute ratio for online bin packing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ö<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an online bin packing algorithm with absolute competitive ratio 5/3, which is optimal.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "4501197", "vector": [], "sparse_vector": [], "title": "Approximation Schemes for Partitioning: Convex Decomposition and Surface Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [1, 2] presented a quasi-polynomial time approximation scheme (QPTAS) for the problem of computing a maximum weight independent set for certain families of planar objects. This major advance on the problem was based on their proof that a certain type of separator exists for any independent set. Subsequently, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [22] simplified and generalized their result. <PERSON> et al. [36] also described a simplification, and somewhat surprisingly, showed that QPTAS's can be obtained for certain, albeit special, type of covering problems.Building on these developments, we revisit two NP-hard geometric partitioning problems – convex decomposition and surface approximation. Partitioning problems combine the features of packing and covering. In particular, since the optimal solution does form a packing, the separator theorems are potentially applicable. Nevertheless, the two partitioning problems we study bring up additional difficulties that are worth examining in the context of the wider applicability of the separator methodology. We show how these issues can be handled in presenting quasi-polynomial time algorithms for these two problems with improved approximation guarantees.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "4501198", "vector": [], "sparse_vector": [], "title": "A new characterization of maximal repetitions by <PERSON> trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> I", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A new characterization of maximal repetitions by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.562 - 571Chapter DOI:https://doi.org/10.1137/1.*************.38PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give a new characterization of maximal repetitions (or runs) in strings, using a tree defined on recursive standard factorizations of Lyndon words, called the Lyndon tree. The characterization leads to a remarkably simple novel proof of the linearity of the maximum number of runs ρ(n) in a string of length n. Furthermore, we show an upper bound of ρ(n) < 1.5n, which improves on the best upper bound 1.6n (Crochemore & Ilie 2008) that does not rely on computational verification. The proof also gives rise to a new, conceptually simple linear-time algorithm for computing all the runs in a string. A notable characteristic of our algorithm is that, unlike all existing linear-time algorithms, it does not utilize the Lempel-Ziv factorization of the string. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "4501199", "vector": [], "sparse_vector": [], "title": "Approximating independent sets in sparse graphs.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the maximum independent set problem on sparse graphs with maximum degree d. The best known result for the problem is an SDP based O(d log log d/log d) approximation due to <PERSON><PERSON><PERSON>. It is also known that no o(d/ log2 d) approximation exists assuming the Unique Games Conjecture. We show the following two results:", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "4501200", "vector": [], "sparse_vector": [], "title": "Optimal detection of intersections between convex polyhedra.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For a polyhedron P in ℝd, denote by |P| its combinatorial complexity, i.e., the number of faces of all dimensions of the polyhedra. In this paper, we revisit the classic problem of preprocessing polyhedra independently so that given two preprocessed polyhedra P and Q in ℝd, each translated and rotated, their intersection can be tested rapidly.For d = 3 we show how to perform such a test in O(log |P| + log |Q|) time after linear preprocessing time and space. This running time is the best possible and improves upon the last best known query time of O(log |P| log |Q|) by <PERSON><PERSON><PERSON> and <PERSON> (1990).We then generalize our method to any constant dimension d, achieving the same optimal O(log |P| + log |Q|) query time using a representation of size O(|P| ⌊d/2⌋+ε) for any ε > 0 arbitrarily small. This answers an even older question posed by <PERSON><PERSON><PERSON> and <PERSON> 30 years ago.In addition, we provide an alternative O(log |P| + log |Q|) algorithm to test the intersection of two convex polygons P and Q in the plane.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "4501201", "vector": [], "sparse_vector": [], "title": "Characterization of cutoff for reversible Markov chains.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A sequence of Markov chains is said to exhibit (total variation) cutoff if the convergence to stationarity in total variation distance is abrupt. We consider reversible lazy chains. We prove a necessary and sufficient condition for the occurrence of the cutoff phenomena in terms of concentration of hitting time of “worst” (in some sense) sets of stationary measure at least α, for some α ∊ (0,1).We also give general bounds on the total variation distance of a reversible chain at time t in terms of the probability that some “worst” set of stationary measure at least α was not hit by time t. As an application of our techniques we show that a sequence of lazy Markov chains on finite trees exhibits a cutoff iff the product of their spectral gaps and their (lazy) mixing-times tends to ∞.MSC codesCutoffmixing-timefinite reversible Markov chainshitting timestreesmaximal inequality", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "4501202", "vector": [], "sparse_vector": [], "title": "New Approximation Schemes for Unsplittable Flow on a Path.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the unsplittable flow on a path problem which has received a lot of attention in the research community recently. Given is a path with capacities on its edges and a set of tasks where each task is characterized by a source and a sink vertex, a demand, and a profit. The goal is to find a subset of the tasks of maximum total profit such that all task demands from this subset can be routed simultaneously without violating the capacity constraints. The best known approximation results are a quasi-polynomial time-approximation scheme if the task demands are in a quasi-polynomial range [<PERSON><PERSON> et al., STOC 2006] and a polynomial time (2 + ∊)-approximation algorithm [<PERSON><PERSON><PERSON> et al., SODA 2014]. Finding a PTAS for it has remained an important open question.In this paper we make progress towards this goal. When the task densities—defined as the ratio of a task's profit and demand—lie in a constant range, we obtain a PTAS. We also improve the QPTAS of <PERSON> et al. by removing the assumption that the demands need to lie in a quasi-polynomial range. Our third result is a PTAS for the case where we are allowed to shorten the paths of the tasks by at most an ∊-fraction. This is particularly motivated by bandwidth allocation and scheduling applications of our problem if we are allowed to slightly increase the speed of the underlying transmission link/machine. Each of these results critically uses a sparsification lemma which we believe could be of independent interest. The lemma shows that in any (optimal) solution there exists an O(∊)-fraction (measured by weight) of its tasks whose removal creates, on each edge, a slack which is at least as large as the (1/∊)th largest demand using that edge. This slack can then be used to allow slight errors when estimating or rounding quantities arising in the computation.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "4501203", "vector": [], "sparse_vector": [], "title": "Plurality Consensus in the Gossip Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study Plurality Consensus in the Model over a network of n anonymous agents. Each agent supports an initial opinion or color. We assume that at the onset, the number of agents supporting the plurality color exceeds that of the agents supporting any other color by a sufficiently-large bias, though the initial plurality itself might be very far from absolute majority. The goal is to provide a protocol that, with high probability, brings the system into the configuration in which all agents support the (initial) plurality color.We consider the Undecided-State Dynamics, a well-known protocol which uses just one more state (the undecided one) than those necessary to store colors.We show that the speed of convergence of this protocol depends on the initial color configuration as a whole, not just on the gap between the plurality and the second largest color community. This dependence is best captured by a novel notion we introduce, namely, the monochromatic distance md() which measures the distance of the initial color configuration from the closest monochromatic one. In the complete graph, we prove that, for a wide range of the input parameters, this dynamics converges within O(md() log n) rounds. We prove that this upper bound is almost tight in the strong sense: Starting from any color configuration , the convergence time is Ω(md()).Finally, we adapt the Undecided-State Dynamics to obtain a fast, random walk-based protocol for plurality consensus on regular expanders. This protocol converges in O(md() polylog(n)) rounds using only polylog(n) local memory. A key-ingredient to achieve the above bounds is a new analysis of the maximum node congestion that results from performing n parallel random walks on regular expanders.All our bounds hold with high probability.MSC codesGossip AlgorithmsPlurality ConsensusMarkov ChainsRandom Walks", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "4501204", "vector": [], "sparse_vector": [], "title": "Learning Privately with Labeled and Unlabeled Examples.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Learning Privately with Labeled and Unlabeled ExamplesA<PERSON>l, <PERSON><PERSON><PERSON>, and <PERSON>ri Stemmer<PERSON> Be<PERSON>l, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>em<PERSON>pp.461 - 477Chapter DOI:https://doi.org/10.1137/1.*************.32PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A private learner is an algorithm that given a sample of labeled individual examples outputs a generalizing hypothesis while preserving the privacy of each individual. In 2008, <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (FOCS 2008) gave a generic construction of private learners, in which the sample complexity is (generally) higher than what is needed for non-private learners. This gap in the sample complexity was then further studied in several followup papers, showing that (at least in some cases) this gap is unavoidable. Moreover, those papers considered ways to overcome the gap, by relaxing either the privacy or the learning guarantees of the learner. We suggest an alternative approach, inspired by the (non-private) models of semi-supervised learning and active-learning, where the focus is on the sample complexity of labeled examples whereas unlabeled examples are of a significantly lower cost. We consider private semi-supervised learners that operate on a random sample, where only a (hopefully small) portion of this sample is labeled. The learners have no control over which of the sample elements are labeled. Our main result is that the labeled sample complexity of private learners is characterized by the VC dimension. We present two generic constructions of private semi-supervised learners. The first construction is of learners where the labeled sample complexity is proportional to the VC dimension of the concept class, however, the unlabeled sample complexity of the algorithm is as big as the representation length of domain elements. Our second construction presents a new technique for decreasing the labeled sample complexity of a given private learner, while roughly maintaining its unlabeled sample complexity. In addition, we show that in some settings the labeled sample complexity does not depend on the privacy parameters of the learner. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "4501205", "vector": [], "sparse_vector": [], "title": "Deterministic Fully Dynamic Data Structures for Vertex Cover and Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Giuseppe F<PERSON>"], "summary": "We present the first deterministic data structures for maintaining approximate minimum vertex cover and maximum matching in a fully dynamic graph in time per update. In particular, for minimum vertex cover we provide deterministic data structures for maintaining a (2 + ε) approximation in O(log n/ε2) amortized time per update. For maximum matching, we show how to maintain a (3 + e) approximation in O(m1/3/ε2) amortized time per update, and a (4 + ε) approximation in O(m1/3/ε2) worst-case time per update. Our data structure for fully dynamic minimum vertex cover is essentially near-optimal and settles an open problem by <PERSON><PERSON> and <PERSON> [13].", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "4501206", "vector": [], "sparse_vector": [], "title": "Algorithmic regularity for polynomials and applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithmic regularity for polynomials and applicationsArnab <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.1870 - 1889Chapter DOI:https://doi.org/10.1137/1.*************.125PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In analogy with the regularity lemma of <PERSON>ze<PERSON>é<PERSON> [Sze75], regularity lemmas for polynomials proved by <PERSON> and <PERSON> [GT09] and by <PERSON> and <PERSON> [KL08] show that one can modify a given collection of polynomials ℱ = {P1, …, Pm} into a new collection ℱ′ so that the polynomials in ℱ′ are “pseudorandom”. These lemmas have various applications, such as (special cases of) Reed-Muller testing and worst-case to average-case reductions for polynomials. However, the transformation from ℱ to ℱ′ in these works is not algorithmic. We define new notions of regularity for polynomials which, while being qualitatively equivalent to the above, also allow for efficient algorithms. Using the algorithmic regularity lemmas, we obtain an algorithmic version of the inverse theorem for Gowers norm (for bounded degree polynomials) over fields of high characteristic, by <PERSON> and <PERSON> [GT09]. As an application, we show that if a polynomial P of degree d is within (normalized) Hamming distance of some unknown polynomial of degree k over a prime field (for k < d < ||), then there is an efficient algorithm for finding a degree-k polynomial Q, which is within distance of P, for some η depending on ε, This can be thought of as decoding the Reed-Muller code of order k beyond the list decoding radius, in the sense of finding one close codeword, when the received word P itself is a polynomial (of degree larger than k but smaller than ||). We also show an algorithmic inverse theorem for polynomials over fields of small characteristic and somewhat simplify the original proof by Tao and Ziegler [TZ12]. We also obtain an algorithmic version of the worstcase to average-case reductions by Kaufman and Lovett [KL08]. They show that if a polynomial of degree d can be weakly approximated by a polynomial of lower degree, then it can be computed exactly using a collection of polynomials of degree at most d − 1. We give an effcient algorithm to find this collection. Finally, our algorithmic regularity lemma over low characteristics can be used to effciently decompose low-degree polynomials over n (for any prime order field ) into polynomials P1, …, Pm: n → that satisfy prescribed degree bounds and for which P(x) = Λ(P1(x), …. Pm(x)) for a given m and Λ. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "4501207", "vector": [], "sparse_vector": [], "title": "Tighter Low-rank Approximation via Sampling the Leveraged Element.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Tighter Low-rank Approximation via Sampling the Leveraged Element<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.902 - 920Chapter DOI:https://doi.org/10.1137/1.*************.62PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this work, we propose a new randomized algorithm for computing a low-rank approximation to a given matrix. Taking an approach different from existing literature, our method first involves a specific biased sampling, with an element being chosen based on the leverage scores of its row and column, and then involves weighted alternating minimization over the factored form of the intended low-rank matrix, to minimize error only on these samples. Our method can leverage input sparsity, yet produce approximations in spectral (as opposed to the weaker Frobenius) norm; this combines the best aspects of otherwise disparate current results, but with a dependence on the condition number k = σ1/σr. In particular we require computations to generate a rank-r approximation to M in spectral norm. In contrast, the best existing method requires time to compute an approximation in Frobenius norm. Besides the tightness in spectral norm, we have a better dependence on the error ε. Our method is naturally and highly parallelizable. Our new approach enables two extensions that are interesting on their own. The first is a new method to directly compute a low-rank approximation (in efficient factored form) to the product of two given matrices; it computes a small random set of entries of the product, and then executes weighted alternating minimization (as before) on these. The sampling strategy is different because now we cannot access leverage scores of the product matrix (but instead have to work with input matrices). The second extension is an improved algorithm with smaller communication complexity for the distributed PCA setting (where each server has small set of rows of the matrix, and want to compute low rank approximation with small amount of communication with other servers). Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "4501208", "vector": [], "sparse_vector": [], "title": "Online Principal Components Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Edo Liberty"], "summary": "We consider the online version of the well known Principal Component Analysis (PCA) problem. In standard PCA, the input to the problem is a set of d-dimensional vectors X = [x1, …, xn] and a target dimension k < d; the output is a set of k-dimensional vectors Y = [y1, …, yn] that minimize the reconstruction error: . Here, Φ ∊ ℝd × k is restricted to being isometric. The global minimum of this quantity, OPTk, is obtainable by offline PCA.In online PCA (OPCA) the setting is identical except for two differences: i) the vectors xt are presented to the algorithm one by one and for every presented xt the algorithm must output a vector yt before receiving xt+1; ii) the output vectors yt are ℓ dimensional with ℓ ≥ k to compensate for the handicap of operating online. To the best of our knowledge, this paper is the first to consider this setting of OPCA. Our algorithm produces yt ∊ ℝℓ with ℓ = O(k · poly(1/ε)) such that .", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "4501209", "vector": [], "sparse_vector": [], "title": "The matching polytope does not admit fully-polynomial size relaxation schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The groundbreaking work of <PERSON><PERSON><PERSON> [2014] established that every linear program expressing the matching polytope has an exponential number of inequalities (formally, the matching polytope has exponential extension complexity). We generalize this result by deriving strong bounds on the polyhedral inapproximability of the matching polytope: for fixed 0 < ε < 1, every polyhedral (1 + ε/n)-approximation requires an exponential number of inequalities, where n is the number of vertices. This is sharp given the well-known ρ-approximation of size provided by the odd-sets of size up to ρ/(ρ — 1). Thus matching is the first problem in P, whose natural linear encoding does not admit a fully polynomial-size relaxation scheme (the polyhedral equivalent of an FPTAS), which provides a sharp separation from the polynomial-size relaxation scheme obtained e.g., via constant-sized odd-sets mentioned above.Our approach reuses ideas from <PERSON><PERSON><PERSON> [2014], however the main lower bounding technique is different. While the original proof is based on the hyperplane separation bound (also called the rectangle corruption bound), we employ the information-theoretic notion of common information as introduced in <PERSON> and <PERSON> [2013], which allows to analyze perturbations of slack matrices. It turns out that the high extension complexity for the matching polytope stems from the same source of hardness as for the correlation polytope: a direct sum structure.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "4501210", "vector": [], "sparse_vector": [], "title": "Approximating the best Nash Equilibrium in no(log n)-time breaks the Exponential Time Hypothesis.", "authors": ["<PERSON>", "Young Kun-Ko", "<PERSON><PERSON><PERSON>"], "summary": "The celebrated PPAD hardness result for finding an exact Nash equilibrium in a two-player game initiated a quest for finding approximate Nash equilibria efficiently, and is one of the major open questions in algorithmic game theory.We study the computational complexity of finding an ε-approximate Nash equilibrium with good social welfare. <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and subsequent improvements showed that finding an ε-approximate Nash equilibrium with good social welfare in a two player game and many variants of this problem is at least as hard as finding a planted clique of size O(log n) in the random graph (n, 1/2).We show that any polynomial time algorithm that finds an ε-approximate Nash equilibrium with good social welfare refutes (the worst-case) Exponential Time Hypothesis by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, confirming the recent conjecture by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Specifically, it would imply a 2Õ(n1/2) algorithm for SAT.Our lower bound matches the quasi-polynomial time algorithm by <PERSON><PERSON>, <PERSON> and <PERSON> for solving the problem.Our key tool is a reduction from the PCP machinery to finding Nash equilibrium via free games, the framework introduced in the recent work by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Techniques developed in the process may be useful for replacing planted clique hardness with ETH-hardness in other applications.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "4501211", "vector": [], "sparse_vector": [], "title": "Comparing Apples and Oranges: Query Tradeoff in Submodular Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Comparing Apples and Oranges: Query Tradeoff in Submodular Maximization<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1149 - 1168Chapter DOI:https://doi.org/10.1137/1.*************.77PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Fast algorithms for submodular maximization problems have a vast potential use in applicative settings, such as machine learning, social networks, and economics. Though fast algorithms were known for some special cases, only recently <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [4] were the first to explicitly look for such algorithms in the general case of maximizing a monotone submodular function subject to a matroid independence constraint. The algorithm of Badanidiyu<PERSON> and <PERSON>drák matches the best possible approximation guarantee, while trying to reduce the number of value oracle queries the algorithm performs. Our main result is a new algorithm for this general case which establishes a surprising tradeoff between two seemingly unrelated quantities: the number of value oracle queries and the number of matroid independence queries performed by the algorithm. Specifically, one can decrease the former by increasing the latter and vice versa, while maintaining the best possible approximation guarantee. Such a tradeoff is very useful since various applications might incur significantly different costs in querying the value and matroid independence oracles. Furthermore, in case the rank of the matroid is O(nc), where n is the size of the ground set and c is an absolute constant smaller than 1, the total number of oracle queries our algorithm uses can be made to have a smaller magnitude compared to that needed by [4]. We also provide even faster algorithms for the well studied special cases of a cardinality constraint and a partition matroid independence constraint, both of which capture many real-world applications and have been widely studied both theorically and in practice. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "4501212", "vector": [], "sparse_vector": [], "title": "Online Submodular Maximization with Preemption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Submodular function maximization has been studied extensively in recent years under various constraints and models. The problem plays a major role in various disciplines. We study a natural online variant of this problem in which elements arrive one-by-one and the algorithm has to maintain a solution obeying certain constraints at all times. Upon arrival of an element, the algorithm has to decide whether to accept the element into its solution and may preempt previously chosen elements. The goal is to maximize a submodular function over the set of elements in the solution.We study two special cases of this general problem and derive upper and lower bounds on the competitive ratio. Specifically, we design a 1/e-competitive algorithm for the unconstrained case in which the algorithm may hold any subset of the elements, and constant competitive ratio algorithms for the case where the algorithm may hold at most k elements in its solution.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "4501213", "vector": [], "sparse_vector": [], "title": "Efficient and Robust Persistent Homology for Measures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A new paradigm for point cloud data analysis has emerged recently, where point clouds are no longer treated as mere compact sets but rather as empirical measures. A notion of distance to such measures has been defined and shown to be stable with respect to perturbations of the measure. This distance can easily be computed pointwise in the case of a point cloud, but its sublevel-sets, which carry the geometric information about the measure, remain hard to compute or approximate. This makes it challenging to adapt many powerful techniques based on the Euclidean distance to a point cloud to the more general setting of the distance to a measure on a metric space.We propose an efficient and reliable scheme to approximate the topological structure of the family of sublevel-sets of the distance to a measure. We obtain an algorithm for approximating the persistent homology of the distance to an empirical measure that works in arbitrary metric spaces. Precise quality and complexity guarantees are given with a discussion on the behavior of our approach in practice.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "4501214", "vector": [], "sparse_vector": [], "title": "Bi-Factor Approximation Algorithms for Hard Capacitated k-Median Problems.", "authors": ["Jaroslaw Byrka", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the classical k-median problem the goal is to select a subset of at most k facilities in order to minimize the total cost of opened facilities and established connections between clients and opened facilities. We consider the capacitated version of the problem, where a single facility may only serve a limited number of clients. We construct approximation algorithms slightly violating the capacities based on rounding a fractional solution to the standard LP.It is well known that the standard LP (even in the case of uniform capacities) has unbounded integrality gap if we only allow violating capacities by a factor smaller than 2, or if we only allow violating the number of facilities by a factor smaller than 2. It is also known that violating capacities by a factor of 2 + ε is sufficient to obtain constant factor approximation of the connection cost in the case of uniform capacities. In this paper we substantially extend this result in the following two directions. On one hand, we obtain a 2+ε capacity violating algorithm to the more general k-facility location problem with uniform capacities, where opening facilities incurs a location specific opening cost. On the other hand, we show that violating capacities by a slightly bigger factor of 3 + ε is sufficient to obtain constant factor approximation of the connection cost also in the case of the non-uniform hard capacitated k-median problem.Our algorithms first use the clustering of <PERSON><PERSON><PERSON> et al. to partition the facilities into sets of total fractional opening at least 1 — 1/ℓ for some fixed ℓ. Then we exploit the technique of <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> developed for the capacitated facility location problem, which is to locally group the demand from clients to obtain a system of single node demand instances. Next, depending on the setting, we either work with stars of facilities (for non-uniform capacities), or we use a dedicated routing tree on the demand nodes (for non-uniform opening cost), to redistribute the demand that cannot be satisfied locally within the clusters.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "4501215", "vector": [], "sparse_vector": [], "title": "An Improved Approximation for k-median, and Positive Correlation in Budgeted Optimization.", "authors": ["Jaroslaw Byrka", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dependent rounding is a useful technique for optimization problems with hard budget constraints. This framework naturally leads to negative correlation properties. However, what if an application naturally calls for dependent rounding on the one hand, and desires positive correlation on the other? More generally, we develop algorithms that guarantee the known properties of dependent rounding, but also have nearly best-possible behavior – near-independence, which generalizes positive correlation – on “small” subsets of the variables. The recent breakthrough of <PERSON> & Svensson for the classical k-median problem has to handle positive correlation in certain dependent-rounding settings, and does so implicitly. We improve upon <PERSON><PERSON><PERSON>'s approximation ratio for k-median from 2.732 + ε to 2.611 + ε by developing an algorithm that improves upon various aspects of their work. Our dependent-rounding approach helps us improve the dependence of the runtime on the parameter ε from <PERSON><PERSON><PERSON><PERSON>'s NO(1/ε2) to NO((1/ε)log (1/ε)).(An erratum has been attached to the previously published proceedings.).", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "4501216", "vector": [], "sparse_vector": [], "title": "Phase Transitions in Random Dyadic Tilings and Rectangular Dissections.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study rectangular dissections of an n × n lattice region into rectangles of area n, where n = 2k for an even integer k. We show that there is a natural edge-flipping Markov chain that connects the state space. A similar edge-flipping chain is also known to connect the state space when restricted to dyadic tilings, where each rectangle is required to have the form R = [s2u, (s + 1)2u] × [t2v, (t+1)2v], where s, t, u and v are nonnegative integers. The mixing time of these chains is open.We consider a weighted version of these Markov chains where, given a parameter λ > 0, we would like to generate each rectangular dissection (or dyadic tiling) σ with probability proportional to λ|σ|, where |σ| is the total edge length. We show there is a phase transition in the dyadic setting: when λ 1, the mixing time is exp(Ω(n2)). Simulations suggest that the chain converges quickly when λ = 1, but this case remains open. The behavior for general rectangular dissections is more subtle, and even establishing ergodicity of the chain requires a careful inductive argument. As in the dyadic case, we show that the edge-flipping Markov chain for rectangular dissections requires exponential time when λ > 1. Surprisingly, the chain also requires exponential time when λ < 1, which we show using a different argument. Simulations suggest that the chain converges quickly at the isolated point λ = 1.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "4501217", "vector": [], "sparse_vector": [], "title": "Tight Bounds on Vertex Connectivity Under Vertex Sampling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental result by <PERSON><PERSON> [10] states that for any λ-edge-connected graph with n nodes, independently sampling each edge with probability p = Ω(log n/λ) results in a graph that has edge connectivity Ω(λp), with high probability. This paper proves the analogous result for vertex connectivity, when sampling vertices. We show that for any k-vertex-connected graph G with n nodes, if each node is independently sampled with probability , then the subgraph induced by the sampled nodes has vertex connectivity Ω(kp2), with high probability. This bound improves upon the recent results of <PERSON><PERSON><PERSON><PERSON> et al. [6], and is existentially optimal.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "4501218", "vector": [], "sparse_vector": [], "title": "Property Testing on Product Distributions: Optimal Testers for Bounded Derivative Properties.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The primary problem in property testing is to decide whether a given function satisfies a certain property, or is far from any function satisfying it. This crucially requires a notion of distance between functions. The most prevalent notion is the Hamming distance over the uniform distribution on the domain. This restriction to uniformity is rather limiting, and it is important to investigate distances induced by more general distributions.In this paper, we give simple and optimal testers for bounded derivative properties over arbitrary product distributions. Bounded derivative properties include fundamental properties such as monotonicity and Lipschitz continuity. Our results subsume almost all known results (upper and lower bounds) on monotonicity and Lipschitz testing.We prove an intimate connection between bounded derivative property testing and binary search trees (BSTs). We exhibit a tester whose query complexity is the sum of expected depths of optimal BSTs for each marginal. Furthermore, we show this sum-of-depths is also a lower bound. A technical contribution of our work is an optimal dimension reduction theorem for all bounded derivative properties, which relates the distance of a function from the property to the distance of restrictions of the function to random lines. Such a theorem has been elusive even for monotonicity, and our theorem is an exponential improvement to the previous best known result.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "4501219", "vector": [], "sparse_vector": [], "title": "On (1, ∊)-Restricted Assignment Makespan Minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "PREVIOUS ARTICLEA Dynamic Programming Framework for Non-Preemptive Scheduling Problems on Multiple Machines [Extended Abstract]", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "4501220", "vector": [], "sparse_vector": [], "title": "On Survivable Set Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bundit Laekhanukit"], "summary": "In the Set Connectivity problem, we are given an n-node edge-weighted undirected graph and a collection of h set pairs (Si, Ti), where <PERSON> and Ti are subsets of nodes. The goal is to compute a min-cost subgraph H so that, for each set pair (Si, Ti), there exists at least one path in H between some node in Si and some node in Ti.In this paper, we initiate the study of the Survivable Set Connectivity problem (SSC), i.e., the generalization of Set Connectivity where we are additionally given an integer requirement ki ≥ 1 for each set pair (Si, Ti), and we want to find a min-cost subgraph H so that there are at least ki edge-disjoint paths in H between Si and Ti. We achieve the following main results:•We show that there is no poly-logarithmic approximation for SSC unless NP has a quasi-polynomial time algorithm. This result is based on a reduction from the Minimum Label Cover problem, and the result holds even for the special case where Si = {r} for all i, i.e., for the high-connectivity variant of the classical Group Steiner Tree problem. More precisely, we prove an approximability lower bound of 2log1−ε n for SSC, for any constant ∊ > 0, which is almost polynomial on n. A technical novelty of our proof is the first use of a padding scheme technique for an edge-connectivity problem on undirected graphs. (Prior to our results, the applications of this technique only pertain to either node-connectivity problems or problems on directed graphs).•We present a bicriteria approximation algorithm for SSC that computes a solution H of cost at most poly-logarithmically larger than the optimal cost and provides a connectivity at least Ω(ki/log n) for each set pair (Si,Ti). The main algorithmic idea is to solve a standard LP relaxation to the problem, and then embed the resulting fractional capacities into a tree via Räcke's cut-based tree embeddings. Based on that, we generate a random collection of Group Steiner Tree-like fractional solutions, which can then be handled by the rounding scheme of [Garg, Konjevod and Ravi – SODA'98]. The prior work on Set Connectivity and Group Steiner Tree used Bartal's distance-based tree embeddings which do not seem to generalize to the k-connectivity versions of these problems.Finally, we remark an interesting contrast demonstrated by our results: While our hardness result almost rules out “polynomial” approximation ratios, relaxing connectivity constraints allows us to obtain “poly-logarithmic” bounds. This naturally suggests that relaxing connectivity requirements might be a proper way in getting big improvements, even beyond (non-bicriteria) lower bounds, for other connectivity problems, especially those whose approximability lower bounds are derived from Minimum Label Cover.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "4501221", "vector": [], "sparse_vector": [], "title": "Speeding up the Four Russians Algorithm by About One More Logarithmic Factor.", "authors": ["<PERSON>"], "summary": "We present a new combinatorial algorithm for Boolean matrix multiplication that runs in O(n3(log log n)3/log3 n) time. This improves the previous combinatorial algorithm by <PERSON><PERSON> and <PERSON> [FOCS'O9] that runs in O(n3(log log n)2/log9/4n) time. Whereas <PERSON><PERSON> and <PERSON>' algorithm uses regularity lemmas for graphs, the new algorithm is simple and uses entirely elementary techniques: table lookup, word operations, plus a deceptively straightforward divide-and-conquer.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}, {"primary_key": "4501222", "vector": [], "sparse_vector": [], "title": "Revealing Optimal Thresholds for Generalized Secretary Problem via Continuous LP: Impacts on Online K-Item Auction and Bipartite K-Matching with Random Arrival Order.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Revealing Optimal Thresholds for Generalized Secretary Problem via Continuous LP: Impacts on Online K-Item Auction and Bipartite K-Matching with Random Arrival OrderT-H. <PERSON>, <PERSON><PERSON>, and Shaofeng H.-C. <PERSON>-<PERSON>. <PERSON>, <PERSON><PERSON>, and Shaofeng H.-C. Jiangpp.1169 - 1188Chapter DOI:https://doi.org/10.1137/1.*************.78PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the general (J, K)-secretary problem, where n totally ordered items arrive in a random order. An algorithm observes the relative merits of arriving items and is allowed to make J selections. The objective is to maximize the expected number of items selected among the K best items. <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> proposed a finite linear program (LP) that completely characterizes the problem, but it is difficult to analyze the asymptotic behavior of its optimal solution as n tends to infinity. Instead, we prove a formal connection between the finite model and an infinite model, where there are a countably infinite number of items, each of which has arrival time drawn independently and uniformly from [0, 1]. The finite LP extends to a continuous LP, whose complementary slackness conditions reveal an optimal algorithm which involves JK thresholds that play a similar role as the -threshold in the optimal classical secretary algorithm. In particular, for the case K = 1, the J optimal thresholds have a nice “rational description”. Our continuous LP analysis gives a very clear perspective on the problem, and the new insights inspire us to solve two related problems. 1. We settle the open problem whether algorithms based only on relative merits can achieve optimal ratio for matroid secretary problems. We show that, for online 2-item auction with random arriving bids (the K-uniform matroid problem with K = 2), an algorithm making decisions based only on relative merits cannot achieve the optimal ratio. This is in contrast with the folklore that, for online 1-item auction, no algorithm can have performance ratio strictly larger than 1/e, which is achievable by an algorithm that considers only relative merits. 2. We give a general transformation technique that takes any monotone algorithm (such as threshold algorithms) for the (K, K)-secretary problem, and constructs an algorithm for online bipartite K-matching with random arrival order that has at least the same performance guarantee. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "4501223", "vector": [], "sparse_vector": [], "title": "Detecting Weakly Simple Polygons.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A closed curve in the plane is weakly simple if it is the limit (in the <PERSON><PERSON><PERSON> metric) of a sequence of simple closed curves. We describe an algorithm to determine whether a closed walk of length n in a simple plane graph is weakly simple in O(n log n) time, improving an earlier O(n3)-time algorithm of <PERSON><PERSON><PERSON> et al. [Discrete Math. 2009]. As an immediate corollary, we obtain the first efficient algorithm to determine whether an arbitrary n-vertex polygon is weakly simple; our algorithm runs in O(n2 log n) time. We also describe algorithms that detect weak simplicity in O(n log n) time for two interesting classes of polygons. Finally, we discuss subtle errors in several previously published definitions of weak simplicity.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "4501224", "vector": [], "sparse_vector": [], "title": "The Value 1 Problem Under Finite-memory Strategies for Concurrent Mean-payoff Games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The Value 1 Problem Under Finite-memory Strategies for Concurrent Mean-payoff Games<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>-<PERSON><PERSON> and <PERSON><PERSON>-<PERSON>pp.1018 - 1029Chapter DOI:https://doi.org/10.1137/1.*************.69PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider concurrent mean-payoff games, a very well-studied class of two-player (player 1 vs player 2) zero-sum games on finite-state graphs where every transition is assigned a reward between 0 and 1, and the payoff function is the long-run average of the rewards. The value is the maximal expected payoff that player 1 can guarantee against all strategies of player 2. We consider the computation of the set of states with value 1 under finite-memory strategies for player 1, and our main results for the problem are as follows: (1) we present a polynomial-time algorithm; (2) we show that whenever there is a finite-memory strategy, there is a stationary strategy that does not need memory at all; and (3) we present an optimal bound (which is double exponential) on the patience of stationary strategies (where patience of a distribution is the inverse of the smallest positive probability and represents a complexity measure of a stationary strategy). Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "4501225", "vector": [], "sparse_vector": [], "title": "Degree-3 Treewidth Sparsifiers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study treewidth sparsifiers. Informally, given a graph G of treewidth k, a treewidth sparsifier H is a minor of G, whose treewidth is close to k, |V(H)| is small, and the maximum vertex degree in H is bounded. Treewidth sparsifiers of degree 3 are of particular interest, as routing on node-disjoint paths, and computing minors seems easier in sub-cubic graphs than in general graphs. In this paper we describe an algorithm that, given a graph G of treewidth k, computes a topological minor H of G such that (i) the treewidth of H is Ω(k/polylog(k)); (ii) |V(H)| = O(k4); and (iii) the maximum vertex degree in H is 3. The running time of the algorithm is polynomial in |V(G)| and k. Our result is in contrast to the known fact that unless NP ⊆ coNP/poly, treewidth does not admit polynomial-size kernels. One of our key technical tools, which is of independent interest, is a construction of a small minor that preserves node-disjoint routability between two pairs of vertex subsets. This is closely related to the open question of computing small good-quality vertex-cut sparsifiers that are also minors of the original graph.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "4501226", "vector": [], "sparse_vector": [], "title": "Triangulation Refinement and Approximate Shortest Paths in Weighted Regions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Let be a planar subdivision with n vertices. Each face of has a weight from [1, ρ] ∪ {∞}. A path inside a face has cost equal to the product of its length and the face weight. In general, the cost of a path is the sum of the subpath costs in the faces intersected by the path. For any ε ∊ (0, 1), we present a fully polynomial-time approximation scheme that finds a (1 + ε)-approximate shortest path between two given points in in time, where k is the smallest integer such that the sum of the k smallest angles in is at least π. Therefore, our running time can be as small as if there are O(1) small angles and it is in the worst case. Our algorithm relies on a new triangulation refinement method, which produces a triangulation of size O(n + k2) such that no triangle has two angles less than min{π/(2k), π/12}.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "4501227", "vector": [], "sparse_vector": [], "title": "Parameterized Streaming: Maximal Matching and Vertex Cover.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As graphs continue to grow in size, we seek ways to effectively process such data at scale. The model of streaming graph processing, in which a compact summary is maintained as each edge insertion/deletion is observed, is an attractive one. However, few results are known for optimization problems over such dynamic graph streams.In this paper, we introduce a new approach to handling graph streams, by instead seeking solutions for the parameterized versions of these problems. Here, we are given a parameter k and the objective is to decide whether there is a solution bounded by k. By combining kernelization techniques with randomized sketch structures, we obtain the first streaming algorithms for the parameterized versions of Maximal Matching and Vertex Cover. We consider various models for a graph stream on n nodes: the insertion-only model where the edges can only be added, and the dynamic model where edges can be both inserted and deleted. More formally, we show the following results:•In the insertion only model, there is a one-pass deterministic algorithm for the parameterized Vertex Cover problem which computes a sketch using Õ(k2) space1 such that at each timestamp in time Õ(2k) it can either extract a solution of size at most k for the current instance, or report that no such solution exists. We also show a tight lower bound of Ω(k2) for the space complexity of any (randomized) streaming algorithms for the parameterized Vertex Cover, even in the insertion-only model.•In the dynamic model, and under the promise that at each timestamp there is a maximal matching of size at most k, there is a one-pass Õ(k2)-space (sketch-based) dynamic algorithm that maintains a maximal matching with worst-case update time Õ(k2). This algorithm partially solves Open Problem 64 from [1]. An application of this dynamic matching algorithm is a one-pass Õ(k2)-space streaming algorithm for the parameterized Vertex Cover problem that in time Õ(2k) extracts a solution for the final instance with probability 1 – δ/no(1), where δ < 1. To the best of our knowledge, this is the first graph streaming algorithm that combines linear sketching with sequential operations that depend on the graph at the current time.•In the dynamic model without any promise, there is a one-pass randomized algorithm for the parameterized Vertex Cover problem which computes a sketch using Õ(nk) space such that in time Õ(nk + 2k) it can either extract a solution of size at most k for the final instance, or report that no such solution exists.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "4501228", "vector": [], "sparse_vector": [], "title": "The Polyhedron-Hitting Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider polyhedral versions of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>'s Orbit Problem [14, 13]—determining whether a target polyhedron V may be reached from a starting point x under repeated applications of a linear transformation A in an ambient vector space ℚm. In the context of program verification, very similar reachability questions were also considered and left open by <PERSON> and <PERSON> in [15], and by <PERSON><PERSON> in [4]. We present what amounts to a complete characterisation of the decidability landscape for the Polyhedron-Hitting Problem, expressed as a function of the dimension m of the ambient space, together with the dimension of the polyhedral target V: more precisely, for each pair of dimensions, we either establish decidability, or show hardness for longstanding number-theoretic open problems.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "4501229", "vector": [], "sparse_vector": [], "title": "Rejecting jobs to Minimize Load and Maximum Flow-time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online algorithms are usually analyzed using the notion of competitive ratio which compares the solution obtained by the algorithm to that obtained by an online adversary for the worst possible input sequence. Often this measure turns out to be too pessimistic, and one popular approach especially for scheduling problems has been that of \"resource augmentation\" which was first proposed by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Although resource augmentation has been very successful in dealing with a variety of objective functions, there are problems for which even a (arbitrary) constant speedup cannot lead to a constant competitive algorithm. In this paper we propose a \"rejection model\" which requires no resource augmentation but which permits the online algorithm to not serve an epsilon-fraction of the requests.The problems considered in this paper are in the restricted assignment setting where each job can be assigned only to a subset of machines. For the load balancing problem where the objective is to minimize the maximum load on any machine, we give O(log2 l/ε)-competitive algorithm which rejects at most an ε-fraction of the jobs. For the problem of minimizing the maximum weighted flow-time, we give an O(1/ε4)-competitive algorithm which can reject at most an ε-fraction of the jobs by weight. We also extend this result to a more general setting where the weights of a job for measuring its weighted flow-time and its contribution towards total allowed rejection weight are different. This is useful, for instance, when we consider the objective of minimizing the maximum stretch. We obtain an O(1/ε6)-competitive algorithm in this case.Our algorithms are immediate dispatch, though they may not be immediate reject. All these problems have strong lower bounds in speed augmentation model.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "4501230", "vector": [], "sparse_vector": [], "title": "Improved Bounds for the Flat Wall Theorem.", "authors": ["<PERSON>"], "summary": "The Flat Wall Theorem of <PERSON> and <PERSON> states that there is some function f, such that for all integers w,t > 1, every graph G containing a wall of size f(w, t), must contain either (i) a Kt-minor; or (ii) a small subset A ⊂ V(G) of vertices, and a flat wall of size w in G \\ A. <PERSON>, <PERSON> and <PERSON> recently showed a self-contained proof of this theorem with the following two sets of parameters: (1) f(w,t) = Θ(t2 +w)) with |A| = O(t24), and (2) f(w,t) = w2Θ(t24)with |A| ≤ t – 5. The latter result gives the best possible bound on |A|. In this paper we improve their bounds to f(w,t) = Θ(t(t + w)) with |A| ≤ t – 5. For the special case where the maximum vertex degree in G is bounded by D, we show that, if G contains a wall of size Ω(Dt(t + w)), then either G contains a Kt-minor, or there is a flat wall of size w in G. This setting naturally arises in algorithms for the Edge-Disjoint Paths problem, with D ≤ 4. Like the proof of <PERSON> et al., our proof is self-contained, except for using a well-known theorem on routing pairs of disjoint paths. We also provide efficient algorithms that return either a model of the Kt-minor, or a vertex set A and a flat wall of size w in G \\ A.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "4501231", "vector": [], "sparse_vector": [], "title": "Sketching for M-Estimators: A Unified Approach to Robust Regression.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Sketching for M-Estimators: A Unified Approach to Robust RegressionKenne<PERSON> <PERSON><PERSON> and <PERSON> and <PERSON>pp.921 - 939Chapter DOI:https://doi.org/10.1137/1.*************.63PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give algorithms for the M-estimators minx ‖Ax — b‖G, where A ∊ ℝn×d and b ∊ ℝn, and ‖y‖G for y ∊ ℝn is specified by a cost function G: ℝ → ℝ≥0, with ‖y‖G ≡ ∑i G(yi). The M-estimators generalize ℓp regression, for which G(x) = We first show that the Huber measure can be computed up to relative error ε in O(nnz(A)log n + poly(d(log n)/ε)) time, where nnz(A) denotes the number of non-zero entries of the matrix A. <PERSON><PERSON> is arguably the most widely used M-estimator, enjoying the robustness properties of ℓ1 as well as the smoothness properties of ℓ2. We next develop algorithms for general M-estimators. We analyze the M-sketch, which is a variation of a sketch introduced by <PERSON><PERSON><PERSON> and <PERSON> in the context of estimating the earthmover distance. We show that the M-sketch can be used much more generally for sketching any M- estimator provided G has growth that is at least linear and at most quadratic. Using the M-sketch we solve the M-estimation problem in O(nnz(A) + poly(d log n)) time for any such G that is convex, making a single pass over the matrix and finding a solution whose residual error is within a constant factor of optimal, with high probability. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "4501232", "vector": [], "sparse_vector": [], "title": "Cell-probe bounds for online edit distance and other pattern matching problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give cell-probe bounds for the computation of edit distance, Hamming distance, convolution and longest common subsequence in a stream. In this model, a fixed string of n symbols is given and one δ-bit symbol arrives at a time in a stream. After each symbol arrives, the distance between the fixed string and a suffix of most recent symbols of the stream is reported. The cell-probe model is perhaps the strongest model of computation for showing data structure lower bounds, subsuming in particular the popular word-RAM model.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "4501233", "vector": [], "sparse_vector": [], "title": "Pricing Online Decisions: Beyond Auctions.", "authors": ["<PERSON><PERSON>", "Alon Eden", "<PERSON>", "Lukasz Jez"], "summary": "We consider dynamic pricing schemes in online settings where selfish agents generate online events. Previous work on online mechanisms has dealt almost entirely with the goal of maximizing social welfare or revenue in an auction settings. This paper deals with quite general settings and minimizing social costs. We show that appropriately computed posted prices allow one to achieve essentially the same performance as the best online algorithm. This holds in a wide variety of settings. Unlike online algorithms that learn about the event, and then make enforcable decisions, prices are posted without knowing the future events or even the current event, and are thus inherently dominant strategy incentive compatible.In particular we show that one can give efficient posted price mechanisms for metrical task systems, some instances of the k-server problem, and metrical matching problems. We give both deterministic and randomized algorithms. Such posted price mechanisms decrease the social cost dramatically over selfish behavior where no decision incurs a charge. One alluring application of this is reducing the social cost of free parking exponentially.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "4501234", "vector": [], "sparse_vector": [], "title": "Contagious Sets in Expanders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the following activation process in undirected graphs: a vertex is active either if it belongs to a set of initially activated vertices or if at some point it has at least r active neighbors, where r > 1 is the activation threshold. A contagious set is a set whose activation results with the entire graph being active. Given a graph G, let m(G, r) be the minimal size of a contagious set. It is known that for every d-regular or nearly d-regular graph on n vertices, . We consider such graphs that additionally have expansion properties, parameterized by the spectral gap and/or the girth of the graphs.The general flavor of our results is that sufficiently strong expansion properties imply that (and more generally, . In addition, we demonstrate that rather weak assumptions on the girth and/or the spectral gap suffice in order to imply that . For example, we show this for graphs of girth at least 7, and for graphs with λ(G) < (1 − ε)d, provided the graph has no 4-cycles.Our results are algorithmic, entailing simple and effcient algorithms for selecting contagious sets.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "4501235", "vector": [], "sparse_vector": [], "title": "Approximate resilience, monotonicity, and the complexity of agnostic learning.", "authors": ["<PERSON>-<PERSON>ed", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "NEXT ARTICLELinear Programming-based Approximation Algorithms for Multi-Vehicle Minimum Latency Problems (Extended Abstract)", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "4501236", "vector": [], "sparse_vector": [], "title": "Short Paths on the Voronoi Graph and Closest Vector Problem with Preprocessing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Short Paths on the Voronoi Graph and Closest Vector Problem with Preprocessing<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.295 - 314Chapter DOI:https://doi.org/10.1137/1.*************.22PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Improving on the Voronoi cell based techniques of [28, 24], we give a Las Vegas Õ (2n) expected time and space algorithm for CVPP (the preprocessing version of the Closest Vector Problem, CVP). This improves on the Õ(4n) deterministic runtime of the Mi<PERSON><PERSON>cio <PERSON> algorithm [24] (henceforth MV) for CVPP1 at the cost of a polynomial amount of randomness (which only affects runtime, not correctness). As in MV, our algorithm proceeds by computing a short path on the Voronoi graph of the lattice, where lattice points are adjacent if their Voronoi cells share a common facet, from the origin to a closest lattice vector. Our main technical contribution is a randomized procedure that, given the Voronoi relevant vectors of a lattice – the lattice vectors inducing facets of the Voronoi cell – as preprocessing, and any \"close enough\" lattice point to the target, computes a path to a closest lattice vector of expected polynomial size. This improves on the Õ(2n) path length given by the MV algorithm. Furthermore, as in MV, each edge of the path can be computed using a single iteration over the Voronoi relevant vectors. As a byproduct of our work, we also give an optimal relationship between geometric and path distance on the Voronoi graph, which we believe to be of independent interest. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048Key words:Closest Vector Problem, Lattice Problems, Convex Geometry", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "4501237", "vector": [], "sparse_vector": [], "title": "Towards a Characterization of Constant-Factor Approximable Min CSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the approximability of Minimum Constraint Satisfaction Problems (Min CSPs) with a fixed finite constraint language Γ on an arbitrary finite domain. The goal in such a problem is to minimize the number of unsatisfied constraints in a given instance of CSP(Γ). A recent result of <PERSON><PERSON> et al. says that, under the mild technical condition that Γ contains the equality relation, the basic LP relaxation is optimal for constant-factor approximation for Min CSP(Γ) unless the Unique Games Conjecture fails. Using the algebraic approach to the CSP, we introduce a new natural algebraic condition, stable probability distributions on symmetric polymorphisms of a constraint language, and show that the presence of such distributions on polymorphisms of each arity is necessary and sufficient for the finiteness of the integrality gap for the basic LP relaxation of Min CSP(Γ). We also show how stable distributions on symmetric polymorphisms can in principle be used to round solutions of the basic LP relaxation, and how, for several examples that cover all previously known cases, this leads to efficient constant-factor approximation algorithms for Min CSP(Γ). Finally, we show that the absence of another condition, which is implied by stable distributions, leads to NP-hardness of constant-factor approximation.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "4501238", "vector": [], "sparse_vector": [], "title": "Bayesian Truthful Mechanisms for Job Scheduling from Bi-criterion Approximation Algorithms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide polynomial-time approximately optimal Bayesian mechanisms for makespan minimization on unrelated machines as well as for max-min fair allocations of indivisible goods, with approximation factors of 2 and respectively, matching the approximation ratios of best known polynomial-time algorithms (for max-min fairness, the latter claim is true for certain ratios of the number of goods m to people k). Our mechanisms are obtained by establishing a polynomial-time approximation-sensitive reduction from the problem of designing approximately optimal mechanisms for some arbitrary objective to that of designing bi-criterion approximation algorithms for the same objective plus a linear allocation cost term. Our reduction is itself enabled by extending the celebrated “equivalence of separation and optimization” [27, 32] to also accommodate bi-criterion approximations. Moreover, to apply the reduction to the specific problems of makespan and max-min fairness we develop polynomial-time bi-criterion approximation algorithms for makespan minimization with costs and max-min fairness with costs, adapting the algorithms of [45], [10] and [4] to the type of bi-criterion approximation that is required by the reduction.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "4501239", "vector": [], "sparse_vector": [], "title": "Learning from satisfying assignments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Learning from satisfying assignments<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Servediopp.478 - 497Chapter DOI:https://doi.org/10.1137/1.*************.33PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract This paper studies the problem of learning \"low-complexity\" probability distributions over the Boolean hypercube {—1,1}n. As in the standard PAC learning model, a learning problem in our framework is defined by a class C of Boolean functions over {—1,1}n, but in our model the learning algorithm is given uniform random satisfying assignments of an unknown f ∊ C and its goal is to output a high-accuracy approximation of the uniform distribution over f−1 (1). This distribution learning problem may be viewed as a demanding variant of standard Boolean function learning, where the learning algorithm only receives positive examples and — more importantly — must output a hypothesis function which has small multiplicative error (i.e. small error relative to the size of f−1(1)). As our main results, we show that the two most widely studied classes of Boolean functions in computational learning theory — linear threshold functions and DNF formulas — have efficient distribution learning algorithms in our model. Our algorithm for linear threshold functions runs in time poly(n, 1/ε) and our algorithm for polynomial-size DNF runs in time quasipoly(n, 1/ε). We obtain both these results via a general approach that combines a broad range of technical ingredients, including the complexity-theoretic study of approximate counting and uniform generation; the Statistical Query model from learning theory; and hypothesis testing techniques from statistics. A key conceptual and technical ingredient of this approach is a new kind of algorithm which we devise called a \"densifier\" and which we believe may be useful in other contexts. We also establish limitations on efficient learnability in our model by showing that the existence of certain types of cryptographic signature schemes imply that certain learning problems in our framework are computationally hard. Via this connection we show that assuming the existence of sufficiently strong unique signature schemes, there are no sub-exponential time learning algorithms in our framework for intersections of two halfspaces, for degree-2 polynomial threshold functions, or for monotone 2-CNF formulas. Thus our positive results for distribution learning come close to the limits of what can be achieved by efficient algorithms. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "4501240", "vector": [], "sparse_vector": [], "title": "Four terminal planar Delta-Wye reducibility via rooted K2, 4 minors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "NEXT ARTICLEAn algorithmic framework for obtaining lower bounds for random Ramsey problems extended abstract", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "4501241", "vector": [], "sparse_vector": [], "title": "Perfect Bayesian Equilibria in Repeated Sales.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Balasubramanian <PERSON>"], "summary": "A special case of <PERSON><PERSON>'s classic result describes the revenue-optimal equilibrium when a seller offers a single item to a buyer. We study a natural repeated sales extension of this model: a seller offers to sell a single fresh copy of an item to the same buyer every day via a posted price. The buyer's value for the item is unknown to the seller but is drawn initially from a publicly known distribution F and remains the same throughout. One key aspect of this game is revelation of the buyer's type through his actions: while the seller might try to learn this value to extract more revenue, the buyer is motivated to hide it to induce lower prices. If the seller is able to commit to future prices, then it is known that the best he can do is extract the Myerson optimal revenue each day. In a more realistic scenario, the seller is unable to commit and must play a perfect Bayesian equilibrium. It is known that not committing to future prices does not help the seller. Thus extracting Myerson optimal revenue each day is a natural upper bound and revenue benchmark in a setting without commitment.We study this setting without commitment and find several suprises. First, if the horizon is fixed, previous work showed that an equilibrium always exists, and all equilibria yield a very low revenue, often times only a constant amount of revenue. This is unintuitive and a far cry from the linearly growing benchmark of obtaining Myerson optimal revenue each day. Our first result shows that this is because the buyer strategies in these equilibria are necessarily unnatural. We restrict to a natural class of buyer strategies, which we call threshold strategies, and show that pure strategy threshold equilibria rarely exist. This offers an explanation for the non-prevalence of bizarre outcomes predicted by previous results. Second, if the seller can commit not to raise prices upon purchase, while still retaining the possibility of lowering prices in future, we recover the natural threshold equilibria by showing that they exist for a large class of distributions including the power law family of distributions. As an example, if the distribution F is uniform in [0,1], the seller can extract revenue of order in n rounds as opposed to the constant revenue obtainable when he is unable to make any commitments. Finally, we consider the infinite horizon game with partial commitment, where both the seller and the buyer discount the future utility by a factor of 1 – δ ∊ [0,1). When the value distribution is uniform in [0, 1], there exists a threshold equilibrium with expected revenue at least of the Myerson optimal revenue benchmark. Under some mild assumptions, this equilibrium is also unique.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "4501242", "vector": [], "sparse_vector": [], "title": "Testing Identity of Structured Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the question of identity testing for structured distributions. More precisely, given samples from a structured distribution q over [n] and an explicit distribution p over [n], we wish to distinguish whether q = p versus q is at least ε-far from p, in L1 distance. In this work, we present a unified approach that yields new, simple testers, with sample complexity that is information-theoretically optimal, for broad classes of structured distributions, including t-flat distributions, t-modal distributions, log-concave distributions, monotone hazard rate (MHR) distributions, and mixtures thereof.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "4501243", "vector": [], "sparse_vector": [], "title": "A Unified Framework for Clustering Constrained Data without Locality Property.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider a class of constrained clustering problems of points in ℝd space, where d could be rather high. A common feature of these problems is that their optimal clusterings no longer have the locality property (due to the additional constraints), which is a key property required by many algorithms for their unconstrained counterparts. To overcome the difficulty caused by the loss of locality, we present in this paper a unified framework, called Peeling-and-Enclosing, to iteratively solve two variants of the constrained clustering problems, constrained k-means clustering (k-CMeans) and constrained k-median clustering (k-CMedian). Our framework generalizes <PERSON> et al.'s elegant k-means clustering approach [35] from unconstrained data to constrained data, and is based on two standalone geometric techniques, called Simplex Lemma and Weaker Simplex Lemma, for k-CMeans and k-CMedian, respectively. Simplex lemma (or weaker simplex lemma) enables us to efficiently approximate the mean (or median) point of an unknown set of points by searching a small-size grid, independent of the dimensionality of the space, in a simplex (or the surrounding region of a simplex), and thus can be used to handle high dimensional data. With these techniques, our framework generates, in nearly linear time (i.e., O(n(log n)k+1d)), O((log n)k) k-tuple candidates for the k mean or median points, and one of them induces a (1 + ε)-approximation for k-CMeans or k-CMedian, where n is the number of points. Combining this unified framework with a problem-specific selection algorithm (which determines the best k-tuple candidate), we obtain a (1 + ε)-approximation for each of the constrained clustering problems. Our framework improves considerably the best known results for these problems. We expect that our technique will be applicable to other constrained clustering problems without locality.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "4501244", "vector": [], "sparse_vector": [], "title": "The Simplex Algorithm is NP-mighty.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose to classify the power of algorithms by the complexity of the problems that they can be used to solve. Instead of restricting to the problem a particular algorithm was designed to solve explicitly, however, we include problems that, with polynomial overhead, can be solved ‘implicitly’ during the algorithm's execution. For example, we allow to solve a decision problem by suitably transforming the input, executing the algorithm, and observing whether a specific bit in its internal configuration ever switches during the execution.We show that the Simplex Method, the Network Simplex Method (both with <PERSON><PERSON><PERSON>'s original pivot rule), and the Successive Shortest Path Algorithm are NP-mighty, that is, each of these algorithms can be used to solve any problem in NP. This result casts a more favorable light on these algorithms' exponential worst-case running times. Furthermore, as a consequence of our approach, we obtain several novel hardness results. For example, for a given input to the Simplex Algorithm, deciding whether a given variable ever enters the basis during the algorithm's execution and determining the number of iterations needed are both NP-hard problems. Finally, we close a long-standing open problem in the area of network flows over time by showing that earliest arrival flows are NP-hard to obtain.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "4501245", "vector": [], "sparse_vector": [], "title": "On the Complexity of Computing an Equilibrium in Combinatorial Auctions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study combinatorial auctions where each item is sold separately but simultaneously via a second price auction. We ask whether it is possible to efficiently compute in this game a pure Nash equilibrium with social welfare close to the optimal one. We show that when the valuations of the bidders are submodular, in many interesting settings (e.g., constant number of bidders, budget additive bidders) computing an equilibrium with good welfare is essentially as easy as computing, completely ignoring incentives issues, an allocation with good welfare. On the other hand, for subadditive valuations, we show that computing an equilibrium requires exponential communication. Finally, for XOS (a.k.a. fractionally subadditive) valuations, we show that if there exists an efficient algorithm that finds an equilibrium, it must use techniques that are very different from the ones currently known.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "4501246", "vector": [], "sparse_vector": [], "title": "A Linear-Size Logarithmic Stretch Path-Reporting Distance Oracle for General Graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Linear-Size Logarithmic Stretch Path-Reporting Distance Oracle for General Graphs<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.805 - 821Chapter DOI:https://doi.org/10.1137/1.*************.55PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In a seminal paper [27] for any n-vertex undirected graph G = (V,E) and a parameter k = 1, 2, …, <PERSON><PERSON> and <PERSON><PERSON> constructed a distance oracle of size O(kn1+1/k) which upon a query (u, v) constructs a path Π between u and ν of length δ(u, v) such that dG(u,ν) ≤ δ(u,v) ≤ (2k–1)dG(u,v). The query time of the oracle from [27] is O(k) (in addition to the length of the returned path), and it was subsequently improved to O(1) [29, 11]. A major drawback of the oracle of [27] is that its space is Ω(n · log n). <PERSON><PERSON> and <PERSON><PERSON> [18] devised an oracle with space O(n1+1/k) and stretch O(k), but their oracle can only report distance estimates and not actual paths. In this paper we devise a path-reporting distance oracle with size O(n1+1/k), stretch O(k) and query time O(nε), for an arbitrarily small ε > 0. In particular, for k = log n our oracle provides logarithmic stretch using linear size. Another variant of our oracle has linear size, polylogarithmic stretch, and query time O (log log n). For unweighted graphs we devise a distance oracle with multiplicative stretch O(1), additive stretch O(β(k)), for a function β(), space O(n1+1/k · β), and query time O(nε), for an arbitrarily small constant ε > 0. The tradeoff between multiplicative stretch and size in these oracles is far below Erdös's girth conjecture threshold (which is stretch 2k — 1 and size O(n)1+1/k)). Breaking the girth conjecture tradeoff is achieved by exhibiting a tradeoff of different nature between additive stretch β(k) and size O(n1+1/k). A similar type of tradeoff was exhibited by a construction of (1 + ε, β)-spanners due to Elkin and Peleg [16]. However, so far (1 + ε, β)-spanners had no counterpart in the distance oracles' world. An important novel tool that we develop on the way to these results is a distance-preserving path-reporting oracle. We believe that this oracle is of independent interest. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "4501247", "vector": [], "sparse_vector": [], "title": "(2Δ - l)-Edge-Coloring is Much Easier than Maximal Matching in the Distributed Setting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Graph coloring is a central problem in distributed computing. Both vertex- and edge-coloring problems have been extensively studied in this context. In this paper we show that a (2Δ — l)-edge-coloring can be computed in time smaller than logε n for any ε > 0, specifically, in rounds. This establishes a separation between the (2Δ — 1)-edge-coloring and Maximal Matching problems, as the latter is known to require time [15]. No such separation is currently known between the (Δ + l)-vertex-coloring and Maximal Independent Set problems.We devise a (1 + ε)Δ-edge-coloring algorithm for an arbitrarily small constant ε > 0. This result applies whenever Δ ≥ Δε, for some constant Δε which depends on e. The running time of this algorithm is . A much earlier logarithmic-time algorithm by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>conesi [11] assumed Δ ≥ (log n)1+Ω(1). For Δ = (log n)1+Ω(1) the running time of our algorithm is only O (log* n). This constitutes a drastic improvement of the previous logarithmic bound [11, 9].Our results for (2Δ — 1)-edge-coloring also follows from our more general results concerning (1 — ε)-locally sparse graphs. Specifically, we devise a (Δ + l)-vertex coloring algorithm for (1 — ε)-locally sparse graphs that runs in O(log* Δ + log(l/ε)) rounds for any ε > 0, provided that ε Δ = (log n)1+Ω(1). We conclude that the (Δ + l)-vertex coloring problem for (1 — ε)-locally sparse graphs can be solved in time. This imply our result about (2Δ — 1)-edge-coloring, because (2Δ — 1)-edge-coloring reduces to (Δ + l)-vertex-coloring of the line graph of the original graph, and because line graphs are (1/2 + o(1))-locally sparse.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "4501248", "vector": [], "sparse_vector": [], "title": "Geometric k Shortest Paths.", "authors": ["<PERSON>B<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of computing k shortest paths in a two-dimensional environment with polygonal obstacles, where the jth path, for 1 ≤ j ≤ k, is the shortest path in the free space that is also homotopically distinct from each of the first j – 1 paths. In fact, we consider a more general problem: given a source point s, construct a partition of the free space, called the kth shortest path map (k-SPM), in which the homotopy of the kth shortest path in a region has the same structure. Our main combinatorial result establishes a tight bound of Θ(k2h + kn) on the worst-case complexity of this map. We also describe an O((k3h + k2n) log (kn)) time algorithm for constructing the map. In fact, the algorithm constructs the jth map for every j ≤ k. Finally, we present a simple visibility-based algorithm for computing the k shortest paths between two fixed points. This algorithm runs in O(m log n + k) time and uses O(m + k) space, where m is the size of the visibility graph. This latter algorithm can be extended to compute k shortest simple (non-self-intersecting) paths, taking O(k2 m(m + kn) log (kn)) time.We invite the reader to play with our applet demonstrating k-SPMs [10].", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "4501249", "vector": [], "sparse_vector": [], "title": "Streaming Algorithms for Estimating the Matching Size in Planar Graphs and Beyond.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Streaming Algorithms for Estimating the Matching Size in Planar Graphs and BeyondHossein <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>1217 - 1233Chapter DOI:https://doi.org/10.1137/1.*************.81PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of estimating the size of a maximum matching when the edges are revealed in a streaming fashion. When the input graph is planar, we present a simple and elegant streaming algorithm that with high probability estimates the size of a maximum matching within a constant factor using space, where n is the number of vertices. The approach generalizes to the family of graphs that have bounded arboricity, which include graphs with an excluded constant-size minor. To the best of our knowledge, this is the first result for estimating the size of a maximum matching in the adversarial-order streaming model (as opposed to the random-order streaming model) in o(n) space. We circumvent the barriers inherent in the adversarial-order model by exploiting several structural properties of planar graphs, and more generally, graphs with bounded arboricity. We further reduce the required memory size to for three restricted settings: (i) when the input graph is a forest; (ii) when we have 2-passes and the input graph has bounded arboricity; and (iii) when the edges arrive in random order and the input graph has bounded arboricity. Finally, we design a reduction from the Boolean Hidden Matching Problem to show that there is no randomized streaming algorithm that estimates the size of the maximum matching to within a factor better than 3/2 and uses only o(n1/2) bits of space. Using the same reduction, we show that there is no deterministic algorithm that computes this kind of estimate in o(n) bits of space. The lower bounds hold even for graphs that are collections of paths of constant length. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "4501250", "vector": [], "sparse_vector": [], "title": "Universal Computation with Arbitrary Polyomino Tiles in Non-Cooperative Self-Assembly.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Trent <PERSON>", "<PERSON>"], "summary": "In this paper we explore the power of geometry to overcome the limitations of non-cooperative self-assembly. We define a generalization of the abstract Tile Assembly Model (aTAM), such that a tile system consists of a collection of polyomino tiles, the Polyomino Tile Assembly Model (polyTAM), and investigate the computational powers of polyTAM systems at temperature 1, where attachment among tiles occurs without glue cooperation (i.e., without the enforcement that more than one tile already existing in an assembly must contribute to the binding of a new tile). Systems composed of the unit-square tiles of the aTAM at temperature 1 are believed to be incapable of Turing universal computation (while cooperative systems, with temperature > 1, are able). As our main result, we prove that for any polyomino P of size 3 or greater, there exists a temperature-1 polyTAM system containing only shape-P tiles that is computationally universal. Our proof leverages the geometric properties of these larger (relative to the aTAM) tiles and their abilities to effectively utilize geometric blocking of particular growth paths of assemblies, while allowing others to complete. In order to prove the computational powers of polyTAM systems, we also prove a number of geometric properties held by all polyominoes of size ≥ 3.To round out our main result, we provide strong evidence that size-1 (i.e. aTAM tiles) and size-2 polyomino systems are unlikely to be computationally universal by showing that such systems are incapable of geometric bitreading, which is a technique common to all currently known temperature-1 computationally universal systems. We further show that larger polyominoes with a limited number of binding positions are unlikely to be computationally universal, as they are only as powerful as temperature-1 aTAM systems. Finally, we connect our work with other work on domino self-assembly to show that temperature-1 assembly with at least 2 distinct shapes, regardless of the shapes or their sizes, allows for universal computation.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "4501251", "vector": [], "sparse_vector": [], "title": "Combinatorial Auctions via Posted Prices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study anonymous posted price mechanisms for combinatorial auctions in a Bayesian framework. In a posted price mechanism, item prices are posted, then the consumers approach the seller sequentially in an arbitrary order, each purchasing her favorite bundle from among the unsold items at the posted prices. These mechanisms are simple, transparent and trivially dominant strategy incentive compatible (DSIC).We show that when agent preferences are fractionally subadditive (which includes all submodular functions), there always exist prices that, in expectation, obtain at least half of the optimal welfare. Our result is constructive: given black-box access to a combinatorial auction algorithm A, sample access to the prior distribution, and appropriate query access to the sampled valuations, one can compute, in polytime, prices that guarantee at least half of the expected welfare of A. As a corollary, we obtain the first polytime (in n and m) constant-factor DSIC mechanism for Bayesian submodular combinatorial auctions, given access to demand query oracles. Our results also extend to valuations with complements, where the approximation factor degrades linearly with the level of complementarity.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "4501252", "vector": [], "sparse_vector": [], "title": "A Simple O(log log(rank))-Competitive Algorithm for the Matroid Secretary Problem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Only recently progress has been made in obtaining o(log(rank))-competitive algorithms for the matroid secretary problem. More precisely, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2012) presented a -competitive procedure, and <PERSON><PERSON><PERSON> (2014) recently presented a O(log log(rank))-competitive algorithm. Both algorithms are involved with complex analyses.Using different tools, we present a considerably simpler O(log log(rank))-competitive algorithm. Our algorithm can be interpreted as a distribution over a simple type of matroid secretary algorithms which are easy to analyze. We are also able to vastly improve on the hidden constant in the competitive ratio.MSC codesmatroidsonline algorithmssecretary problem", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "4501253", "vector": [], "sparse_vector": [], "title": "Robust hamiltonicity of random directed graphs extended abstract.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust hamiltonicity of random directed graphs extended abstract<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1752 - 1758Chapter DOI:https://doi.org/10.1137/1.*************.117PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In his seminal paper from 1952 <PERSON><PERSON> showed that the complete graph on n ≥ 3 vertices remains Hamiltonian even if we allow an adversary to remove ⌊n/2⌋ edges touching each vertex. In 1960 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obtained an analogue statement for digraphs by showing that every directed graph on n ≥ 3 vertices with minimum in- and out-degree at least n/2 contains a directed Hamilton cycle. Both statements quantify the robustness of complete graphs (digraphs) with respect to the property of containing a Hamilton cycle. A natural way to generalize such results to arbitrary graphs (digraphs) is using the notion of local resilience. The local resilience of a graph (digraph) G with respect to a property is the maximum number r such that G has the property even if we allow an adversary to remove an r-fraction of (in- and out-going) edges touching each vertex. The theorems of <PERSON><PERSON> and <PERSON><PERSON><PERSON>-<PERSON>i state that the local resilience of the complete graph and digraph with respect to Hamiltonicity is 1/2. Recently, this statements have been generalized to random settings. Lee and Sudakov (2012) proved that the local resilience of a random graph with edge probability p = ω (log n/n) with respect to Hamiltonicity is 1/2 ± o(1). For random directed graphs, Hefetz, Steger and Sudakov (2014+) proved an analogue statement, but only for edge probability . In this paper we significantly improve their result to p = ω (log8 n/n), which is optimal up to the polylogarithmic factor. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "4501254", "vector": [], "sparse_vector": [], "title": "Solving d-SAT via Backdoors to Small Treewidth.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A backdoor set of a CNF formula is a set of variables such that fixing the truth values of the variables from this set moves the formula into a polynomial-time de-cidable class. In this work we obtain several algorithmic results for solving d-SAT, by exploiting backdoors to d-CNF formulas whose incidence graphs have small treewidth.For a CNF formula ϕ and integer t, a strong backdoor set to treewidth t is a set of variables such that each possible partial assignment τ to this set reduces ϕ to a formula whose incidence graph is of treewidth at most t. A weak backdoor set to treewidth t is a set of variables such that there is a partial assignment to this set that reduces ϕ to a satisfiable formula of treewidth at most t. Our main contribution is an algorithm that, given a d-CNF formula ϕ and an integer k, in time ,•either finds a satisfying assignment of ϕ, or•reports correctly that ϕ is not satisfiable, or•concludes correctly that ϕ has no weak or strong backdoor set to treewidth t of size at most k.As a consequence of the above, we show that d-SAT parameterized by the size of a smallest weak/strong backdoor set to formulas of treewidth t, is fixed-parameter tractable. Prior to our work, such results were know only for the very special case of t = 1 (<PERSON><PERSON> and <PERSON>, ICALP 2012). Our result not only extends the previous work, it also improves the running time substantially. The running time of our algorithm is linear in the input size for every fixed k. Moreover, the exponential dependence on the parameter k is asymptotically optimal under Exponential Time Hypothesis (ETH).One of our main technical contributions is a linear time “protrusion replacer” improving over a (n log2 n)-time procedure of Fomin et al. (FOCS 2012). The new deterministic linear time protrusion replacer has several applications in kernelization and parameterized algorithms.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "4501255", "vector": [], "sparse_vector": [], "title": "Density and regularity theorems for semi-algebraic hypergraphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A k-uniform semi-algebraic hypergraph H is a pair (P, E), where P is a subset of ℝd and E is a collection of k-tuples {p1, …, pk} ⊂ P such that (p1, …, pk) ∊ E if and only if the kd coordinates of the pi-s satisfy a boolean combination of a finite number of polynomial inequalities. The complexity of H can be measured by the number and the degrees of these inequalities and the number of variables (coordinates) kd. Several classical results in extremal hypergraph theory can be substantially improved when restricted to semi-algebraic hypergraphs.Substantially improving a theorem of <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, we establish the following \"polynomial regularity lemma\": For any 0 0 is a constant that depends on the complexity of H. We also establish an improved lower bound, single exponentially decreasing in k, on the best constant δ > 0 such that the vertex classes P1, …, Pk of every k-partite k-uniform semi-algebraic hypergraph H = (P1 ∪ … ∪ Pk, E) with |E| ≥ εΠkj=1|Pi| have, for 1 ≤ i ≤ k, δ|Pi|-element subsets P′i ⊆ Pi satisfying P′1 × … × P′k ⊆ E. The best previously known lower bound on δ due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> decreased double exponentially fast in k. We give three geometric applications of our results. In particular, we establish the following strengthening of the so-called same-type lemma of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>: Any disjoint finite sets P1, …, Pk ⊂ ℝd (k > d) have for 1 ≤ i ≤ k subsets P′i of size at least 2−O(d3k log k)|Pi| with the property that every k-tuple formed by taking one point from each P′i has the same order type.The above techniques carry over to property testing. We show that for any typical hereditary hypergraph property , there is a randomized algorithm with query complexity ) to determine (with probability at least .99) whether a k-uniform semi-algebraic hypergraph H = (P,E) with constant description complexity is ε-near to having property , that is, whether one can change at most ε|P|k hyperedges of H in order to obtain a hypergraph that has the property. The testability of such properties for general k-uniform hypergraphs was first shown by Alon and Shapira (for graphs) and by Rödl and Schacht (for k > 2). The query complexity time of their algorithms is enormous, growing considerably faster than a tower function.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "4501256", "vector": [], "sparse_vector": [], "title": "Forbidden structure characterization of circular-arc graphs and a certifying recognition algorithm.", "authors": ["<PERSON><PERSON>", "Pavol Hell", "<PERSON><PERSON>"], "summary": "A circular-arc graph is the intersection graph of arcs of a circle. It is a well-studied graph model with numerous natural applications. A certifying algorithm is an algorithm that outputs a certificate, along with its answer (be it positive or negative), where the certificate can be used to easily justify the given answer. While the recognition of circular-arc graphs has been known to be polynomial since the 1980s, no polynomial-time certifying recognition algorithm is known to date, despite such algorithms being found for many subclasses of circular-arc graphs. This is largely due to the fact that a forbidden structure characterization of circular-arc graphs is not known, even though the problem has been intensely studied since the seminal work of <PERSON><PERSON> in the 1960s.In this contribution, we settle this problem. We present the first forbidden structure characterization of circular-arc graphs. Our obstruction has the form of mutually avoiding walks in the graph. It naturally extends a similar obstruction that characterizes interval graphs. As a consequence, we give the first polynomial-time certifying algorithm for the recognition of circular-arc graphs.MSC codescircular-arc graphforbidden subgraph characterizationcertifying algorithmasteroidal tripleinvertible pairinterval orientationtransitive orientationknotting graph", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "4501257", "vector": [], "sparse_vector": [], "title": "<PERSON> covers for prize-collecting network activation problem.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the network activation problem, each edge in a graph is associated with an activation function that decides whether the edge is activated from weights assigned to its end nodes. The feasible solutions of the problem are node weights such that the activated edges form graphs of required connectivity, and the objective is to find a feasible solution minimizing its total weight. In this paper, we consider a prize-collecting version of the network activation problem and present the first nontrivial approximation algorithms. Our algorithms are based on a new linear programming relaxation of the problem. They round optimal solutions for the relaxation by repeatedly computing node weights activating subgraphs, called spiders. For the problem with element- and node-connectivity requirements, we also present a new potential function on uncrossable biset families and use it to analyze our algorithms.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "4501258", "vector": [], "sparse_vector": [], "title": "Set membership with a few bit probes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the bit-probe complexity of the set membership problem, where a set S of size at most n from a universe of size m is to be represented as a short bit vector in order to answer membership queries of the form Is x in S? by adaptively probing the bit vector at t places. Let s(m,n,t) be the minimum number of bits of storage needed for such a scheme. Several recent works investigate s(m,n,t) for various ranges of the parameter; we obtain improvements over some of the bounds shown by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2002) and <PERSON><PERSON> and <PERSON> (2009).", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "4501259", "vector": [], "sparse_vector": [], "title": "Capacity of Interactive Communication over Erasure Channels and Channels with Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider interactive communication performed over two simple types of noisy channels: binary error channels with noiseless feedback and binary erasure channels. In both cases, the noise model is adversarial Assuming at most ε-fraction of the bits can be corrupted, we show coding schemes that simulate any alternating interactive protocol with rate 1 — Θ(H(ε)). All our simulations are simple, randomized, and computationally efficient.The rates of our coding schemes stand in contrast to the interactive communication rates supported by random or adversarial error channels without feedback, for which the best known coding schemes achieve rates of and , respectively. As these rates are conjectured to be optimal, our result implies a large asymptotic gap between interactive communication rate over noisy channels with and without feedback. Such a gap has no equivalent in the standard one-way communication setting.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "4501260", "vector": [], "sparse_vector": [], "title": "2-Edge Connectivity in Directed Graphs.", "authors": ["<PERSON><PERSON>", "Giuseppe F<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)2-Edge Connectivity in Directed Graphs<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.1988 - 2005Chapter DOI:https://doi.org/10.1137/1.*************.132PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Edge and vertex connectivity are fundamental concepts in graph theory. While they have been thoroughly studied in the case of undirected graphs, surprisingly not much has been investigated for directed graphs. In this paper we study 2-edge connectivity problems in directed graphs and, in particular, we consider the computation of the following natural relation: We say that two vertices v and w are 2-edge-connected if there are two edge-disjoint paths from v to w and two edge-disjoint paths from w to v. This relation partitions the vertices into blocks such that all vertices in the same block are 2-edge-connected. Differently from the undirected case, those blocks do not correspond to the 2-edge-connected components of the graph. The main result of this paper is an algorithm for computing the 2-edge-connected blocks of a directed graph in linear time. Besides being asymptotically optimal, our algorithm improves significantly over previous bounds. Once the 2-edge-connected blocks are available, we can test in constant time if two vertices are 2-edge-connected. Additionally, we also show how to compute in linear time a sparse certificate for this relation, i.e., a subgraph of the input graph that has O(n) edges and maintains the same 2-edge-connected blocks as the input graph, where n is the number of vertices. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "4501261", "vector": [], "sparse_vector": [], "title": "LP/SDP Hierarchy Lower Bounds for Decoding Random LDPC Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Random (dv, dc)-regular LDPC codes (where each variable is involved in dv parity checks and each parity check involves dc variables) are well-known to achieve the Shannon capacity of the binary symmetric channel (for sufficiently large dv and dc) under exponential time decoding. However, polynomial time algorithms are only known to correct a much smaller fraction of errors. One of the most powerful polynomial-time algorithms with a formal analysis is the LP decoding algorithm of <PERSON><PERSON> et al. which is known to correct an Ω(1/dc) fraction of errors. In this work, we show that fairly powerful extensions of LP decoding, based on the Sherali-Adams and Lasserre hierarchies, fail to correct much more errors than the basic LP-decoder. In particular, we show that:•For any values of dv and dc, a linear number of rounds of the Sherali-Adams LP hierarchy cannot correct more than an O(1/dc) fraction of errors on a random (dv, dc)-regular LDPC code.•For any value of dv and infinitely many values of dc a linear number of rounds of the Lasserre SDP hierarchy cannot correct more than an O(1/dc) fraction of errors on a random (dv, dc)-regular LDPC code.Our proofs use a new streching and collapsing technique that allows us to leverage recent progress in the study of the limitations of LP/SDP hierarchies for Maximum Constraint Satisfaction Problems (Max-CSPs). The problem then reduces to the construction of special balanced pairwise independent distributions for Sherali-Adams and special cosets of balanced pairwise independent subgroups for Lasserre. Our (algebraic) construction for the Lasserre hierarchy is based on designing sets of points in (for q any power of 2 and d = 2, 3) with special hyperplane-incidence properties – constructions that may be of independent interest. An intriguing consequence of our work is that expansion seems to be both the strength and the weakness of random regular LDPC codes.Some of our techniques are more generally applicable to a large class of Boolean CSPs called Min-Ones. In particular, for k-Hypergraph Vertex Cover, we obtain an improved integrality gap of k – 1 – ε that holds after a linear number of rounds of the Lasserre hierarchy, for any k = q + 1 with q an arbitrary prime power. The best previous gap for a linear number of rounds was equal to 2 – ε and due to Schoenebeck.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "4501262", "vector": [], "sparse_vector": [], "title": "Connectivity in Random Forests and Credit Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong<PERSON> Zhang"], "summary": "Previous chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Connectivity in Random Forests and Credit NetworksAs<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2037 - 2048Chapter DOI:https://doi.org/10.1137/1.*************.135PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Recent work has highlighted credit networks as an effective mechanism for modeling trust in a network: agents issue their own currency and trust each other for a certain amount of each other's currency, allowing two nodes to transact if there is a chain of sufficient residual trust between them. Under a natural model of repeated transactions, the probability that two agents can successfully transact in a credit network (i.e. the liquidity between these two agents) is the same as the probability that they are connected to each other in a uniformly random forest of the network. Motivated by this connection, we define the RF-connectivity between a pair of nodes in a graph G as the probability that the two nodes belong to the same connected component in a uniformly random forest of G. Our first result is that for an arbitrary subset S of nodes in G, the average RF-connectivity between pairs of nodes in S is at least 1–2/h(GS), where h(GS) is the edge expansion of the subgraph GS induced by S. Informally, this implies that a well-connected “community” of nodes S in a credit network will have high liquidity among themselves, regardless of the structure of the remaining network. We extend this result to show that in fact every node in S has good average RF-connectivity to other nodes in S whenever S has good edge expansion. We also show that our results are nearly tight by proving an upper bound on the liquidity of regular graphs. For our motivating application, it is important that we relate the average RF-connectivity in S to the expansion inside S and not merely to expansion of G since we would like to assert that a well-connected community has high liquidity even if the graph as a whole is not well-connected. This naturally leads to a monotonicity conjecture: the RF-connectivity of two nodes can not decrease when a new edge is added to G. We show that the monotonicity conjecture is equivalent to showing negative correlation between inclusion of any two edges in a random forest, a long-standing open problem. Our result about the average RF-connectivity of nodes in S may be viewed as establishing a weak version of the monotonicity conjecture. Previous chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048Key words:Uniformly random forests, Liquidity in credit networks, Edge expansion, Markov chains", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "4501263", "vector": [], "sparse_vector": [], "title": "A Stable Marriage Requires Communication.", "authors": ["Yannai <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Stable Marriage Requires CommunicationY<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.1003 - 1017Chapter DOI:https://doi.org/10.1137/1.*************.68PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Gale-Shapley algorithm for the Stable Marriage Problem is known to take Θ(n2) steps to find a stable marriage in the worst case, but only Θ(n log n) steps in the average case (with n women and n men). In 1976, <PERSON><PERSON><PERSON> asked whether the worst-case running time can be improved in a model of computation that does not require sequential access to the whole input. A partial negative answer was given by <PERSON> and <PERSON>, who showed that Θ(n2) queries are required in a model that allows certain natural random-access queries to the participants' preferences. A significantly more general — albeit slightly weaker — lower bound follows from <PERSON><PERSON>'s elaborate analysis of communication complexity, namely that Ω(n2) Boolean queries are required in order to find a stable marriage, regardless of the set of allowed Boolean queries. Using a reduction to the communication complexity of the disjointness problem, we give a far simpler, yet significantly more powerful argument showing that Ω(n2) Boolean queries of any type are indeed required. Notably, unlike <PERSON>l's lower bound, our lower bound generalizes also to (A) randomized algorithms, (B) finding approximately-stable marriages (C) verifying the stability (or the approximate stability) of a proposed marriage, (D) allowing arbitrary separate preprocessing of the women's preferences profile and of the men's preferences profile, and (E) several variants of the basic problem, such as whether a given pair is married in every/some stable marriage. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "4501264", "vector": [], "sparse_vector": [], "title": "Approximate Range Emptiness in Constant Time and Optimal Space.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies the ε-approximate range emptiness problem, where the task is to represent a set S of n points from {0, …, U – 1} and answer emptiness queries of the form \"[a; b] ∩ S ≠  ?\" with a probability of false positives allowed. This generalizes the functionality of Bloom filters from single point queries to any interval length L. Setting the false positive rate to ε/L and performing L queries, Bloom filters yield a solution to this problem with space O(n lg(L/ε)) bits, false positive probability bounded by ε for intervals of length up to L, using query time O(L lg(L/ε)). Our first contribution is to show that the space/error trade-off cannot be improved asymptotically: Any data structure for answering approximate range emptiness queries on intervals of length up to L with false positive probability ε, must use space Ω(n lg(L/ε)) — O(n) bits. On the positive side we show that the query time can be improved greatly, to constant time, while matching our space lower bound up to a lower order additive term. This result is achieved through a succinct data structure for (non-approximate 1d) range emptiness/reporting queries, which may be of independent interest.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "4501265", "vector": [], "sparse_vector": [], "title": "The switch Markov chain for sampling irregular graphs (Extended Abstract).", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The switch Markov chain for sampling irregular graphs (Extended Abstract)<PERSON>hillpp.1564 - 1572Chapter DOI:https://doi.org/10.1137/1.*************.103PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The problem of efficiently sampling from a set of (undirected) graphs with a given degree sequence has many applications. One approach to this problem uses a simple Markov chain, which we call the switch chain, to perform the sampling. The switch chain is known to be rapidly mixing for regular degree sequences. We prove that the switch chain is rapidly mixing for any degree sequence with minimum degree at least 1 and with maximum degree dmax which satisfies , where M is the sum of the degrees. The mixing time bound obtained is only an order of n larger than that established in the regular case, where n is the number of vertices. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "4501266", "vector": [], "sparse_vector": [], "title": "Gossip vs. <PERSON>ov Chains, and Randomness-Efficient <PERSON><PERSON><PERSON> Spreading.", "authors": ["<PERSON><PERSON><PERSON>", "He Sun"], "summary": "We study gossip algorithms for the rumor spreading problem which asks one node to deliver a rumor to all nodes in an unknown network, and every node is only allowed to call one neighbor in each round. In this work we introduce two fundamentally new techniques in studying the rumor spreading problem:First, we establish a new connection between the rumor spreading process in an arbitrary graph and certain Markov chains. While most previous work analyzed the rumor spreading time in general graphs by studying the rate of the number of (un-)informed nodes after every round, we show that the mixing time of a certain Markov chain suffices to bound the rumor spreading time in an arbitrary graph.Second, we construct a reduction from rumor spreading processes to branching programs. This reduction gives us a general framework to derandomize the rumor spreading and other gossip processes. In particular, we show that, for any n-vertex expander graph, there is a protocol which informs every node in O(log n) rounds with high probability, and uses O (log n · log log n) random bits in total. The runtime of our protocol is tight, and the randomness requirement of O (log n· log log n) random bits almost matches the lower bound of Ω(log n) random bits. We further show that, for many graph families (defined with respect to the expansion and the degree), O (poly log n) random bits in total suffice for fast rumor spreading. These results give us an almost complete understanding of the role of randomness in the rumor spreading process, which was extensively studied over the past years.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "4501267", "vector": [], "sparse_vector": [], "title": "Improved Region-Growing and Combinatorial Algorithms for k-Route Cut Problems (Extended Abstract).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the k-route generalizations of various cut problems, the most general of which is k-route multicut (k-MC) problem, wherein we have r source-sink pairs and the goal is to delete a minimum-cost set of edges to reduce the edge-connectivity of every source-sink pair to below k. The k-route extensions of multiway cut (k-MWC), and the minimum s-t cut problem (k-(s, t)-Cut), are similarly defined. We present various approximation and hardness results for k-MC, k-MWC, and k-(s,t)-Cut that improve the state-of-the-art for these problems in several cases. Our contributions are threefold.•For k-route multiway cut, we devise simple, but surprisingly effective, combinatorial algorithms that yield bicriteria approximation guarantees that markedly improve upon the previous-best guarantees.•For k-route multicut, we design algorithms that improve upon the previous-best approximation factors by roughly an -factor, when k = 2, and for general k and unit costs and any fixed violation of the connectivity threshold k. The main technical innovation is the definition of a new, powerful region growing lemma that allows us to perform region-growing in a recursive fashion even though the LP solution yields a different metric for each source-sink pair, and without incurring an O(log2 r) blow-up in the cost that is inherent in some previous applications of region growing to k-route cuts. We obtain the same benefits as [15] do in their divide-and-conquer algorithms, and thereby obtain an O(ln r ln ln r)-approximation to the cost. We also obtain some extensions to k-route node-multicut problems.•We complement these results by showing that the k-route s-t cut problem is at least as hard to approximate as the densest-k-subgraph (DkS) problem on uniform hypergraphs. In particular, this implies that one cannot avoid a poly(k)-factor if one seeks a unicriterion approximation, without improving the state-of-the-art for DkS on graphs, and proving the existence of a family of one-way functions. Previously, only NP-hardness of k-(s; t)-Cut was known.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "4501268", "vector": [], "sparse_vector": [], "title": "Strong Inapproximability Results on Balanced Rainbow-Colorable Hypergraphs.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Consider a K-uniform hypergraph H = (V, E). A coloring c:V → {1, 2, …, k} with k colors is rainbow if every hyperedge e contains at least one vertex from each color, and is called perfectly balanced when each color appears the same number of times. A simple polynomial-time algorithm finds a 2-coloring if H admits a perfectly balanced rainbow k-coloring. For a hypergraph that admits an almost balanced rainbow coloring, we prove that it is NP-hard to find an independent set of size ε, for any ε > 0. Consequently, we cannot weakly color (avoiding monochromatic hyperedges) it with O(1) colors. With k = 2, it implies strong hardness for discrepancy minimization of systems of bounded set-size.Our techniques extend recent developments in inapproximability based on reverse hypercontractivity and invariance principles for correlated spaces. We give a recipe for converting a promising test distribution and a suitable choice of a outer PCP to hardness of finding an independent set in the presence of highly-structured colorings. We use this recipe to prove additional results almost in a black-box manner, including: (1) the first analytic proof of (K – 1 – ε)-hardness of K-Hypergraph Vertex Cover with more structure in completeness, and (2) hardness of (2Q + 1)-SAT when the input clause is promised to have an assignment where every clause has at least Q true literals.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "4501269", "vector": [], "sparse_vector": [], "title": "Limitations on Testable Affine-Invariant Codes in the High-Rate Regime.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "Madhu <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Locally testable codes (LTCs) of constant minimum (absolute) distance that allow the tester to make a nearly linear number of queries have become the focus of attention recently due to their connections to central questions in approximability theory. In particular, the binary Reed-Muller code of block length N and absolute distance d is known to be testable with O(N/d) queries, and has a dimension of  N – (log N)log d. The polylogarithmically small co-dimension is the basis of constructions of small set expanders with many “bad” eigenvalues, and size-efficient PCPs based on a shorter version of the long code. The smallest possible co-dimension for a distance d code (without any testability requirement) is , achieved by BCH codes. This raises the natural question of understanding where in the spectrum between the two classical families, Reed-Muller and BCH, the optimal co-dimension of a distance d LTC lies — in other words the “price” one has to pay for local testability.One promising approach for constructing LTCs is to focus on affine-invariant codes, whose structure makes testing guarantees easier to deduce than for general codes. Along these lines, the authors of [HRZS13] and [GKS13] recently constructed an affine-invariant family of high-rate LTCs with slightly smaller co-dimension than Reed-Muller codes. In this work, we show that their construction is essentially optimal among linear affine-invariant LTCs that contain the Reed-Muller code of the appropriate degree.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "4501270", "vector": [], "sparse_vector": [], "title": "Lopsidependency in the Moser-Tardos framework: Beyond the Lopsided Lovász Local Lemma.", "authors": ["<PERSON>"], "summary": "The Lopsided Lovász Local Lemma (LLLL) is a powerful probabilistic principle which has been used in a variety of combinatorial constructions. While this principle began as a general statement about probability spaces, it has recently been transformed into a variety of polynomial-time algorithms. The resampling algorithm of <PERSON><PERSON> & Tardos is the most well-known example of this. A variety of criteria have been shown for the LLLL; the strongest possible criterion was shown by <PERSON><PERSON>, and other criteria which are easier to use computationally have been shown by <PERSON><PERSON><PERSON> et al, <PERSON><PERSON>den, and Kolipaka & Szegedy.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "4501271", "vector": [], "sparse_vector": [], "title": "A Dynamic Programming Framework for Non-Preemptive Scheduling Problems on Multiple Machines [Extended Abstract].", "authors": ["Sungjin Im", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we consider a variety of scheduling problems where n jobs with release times are to be scheduled non-preemptively on a set of m identical machines. The problems considered are machine minimization, (weighted) throughput maximization and min-sum objectives such as (weighted) flow time and (weighted) tardiness.We develop a novel quasi-polynomial time dynamic programming framework that gives O(l)-speed O(l)-approximation algorithms for the offline versions of machine minimization and min-sum problems. For the weighted throughput problem, the framework gives a (1 + ε)-speed (1 – ε)-approximation algorithm. The generic DP is based on improving a naïve exponential time DP by developing a sketching scheme that compactly and accurately approximates parameters used in the DP states. We show that the loss of information due to the sketching scheme can be offset with limited resource augmentation. This framework is powerful and flexible, allowing us to apply it to this wide range of scheduling objectives and settings. We also provide new insight into the relative power of speed augmentation versus machine augmentation for non-preemptive scheduling problems; specifically, we give new evidence for the power and importance of extra speed for some non-preemptive scheduling problems.This novel DP framework leads to many new algorithms with improved results that solve many open problems, albeit with quasi-polynomial running times. We highlight our results as follows. For the problems with min-sum objectives, we give the first O(l)-speed O(l)-approximation algorithms for the multiple-machine setting. Even for the single machine case, we reduce both the resource augmentation required and the approximation ratios. In particular, our approximation ratios are either 1 or 1 + ε. Most of our algorithms use speed 1 + e or 2 + ε. We also resolve an open question (albeit with a quasi-polynomial time algorithm) of whether less than 2-speed could be used to achieve an O(1)-approximation for flow time. New techniques are needed to address this open question since it was proven that previous techniques are insufficient. We answer this open question by giving an algorithm that achieves a (1 + ε)-speed 1-approximation for flow time and (1 + ε)-speed (1 + ε)-approximation for weighted flow time.For the machine minimization problem, we give the first result using constant resource augmentation by showing a (1 + ε)-speed 2-approximation, and the first result only using speed augmentation and no additional machines by showing a (2 + ε)-speed 1-approximation. We complement our positive results for machine minimization by considering the discrete variant of the problem and show that no algorithm can use speed augmentation less than 2log1–εand achieve approximation less than O(log log n) for any constant ε > 0 unless NP admits quasi-polynomial time optimal algorithms. Thus, our results show a stark contrast between the two settings. In one, constant speed augmentation is sufficient whereas in the other, speed augmentation is essentially not effective.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "4501272", "vector": [], "sparse_vector": [], "title": "New Approximations for Broadcast Scheduling via Variants of α-point Rounding.", "authors": ["Sungjin Im", "<PERSON>"], "summary": "NEXT ARTICLEA Dynamic Programming Framework for Non-Preemptive Scheduling Problems on Multiple Machines [Extended Abstract]", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "4501273", "vector": [], "sparse_vector": [], "title": "Characterizing the easy-to-find subgraphs from the viewpoint of polynomial-time algorithms, kernels, and Turing kernels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study two fundamental problems related to finding subgraphs: (1) given graphs G and H, Subgraph Test asks if H is isomorphic to a subgraph of G, (2) given graphs G, H, and an integer t, PACKING asks if G contains t vertex-disjoint subgraphs isomorphic to H. For every graph class ℱ, let ℱ-Subgraph Test and ℱ-Packing be the special cases of the two problems where H is restricted to be in F. Our goal is to study which classes ℱ make the two problems tractable in one of the following senses:•(randomized) polynomial-time solvable,•admits a polynomial (many-one) kernel (that is, has a polynomial-time preprocessing procedure that creates an equivalent instance whose size is polynomially bounded by the size of the solution), or•admits a polynomial Turing kernel (that is, has an adaptive polynomial-time procedure that reduces the problem to a polynomial number of instances, each of which has size bounded polynomially by the size of the solution).To obtain a more robust setting, we restrict our attention to hereditary classes F.It is known that if every component of every graph in ℱ has at most two vertices, then ℱ-Packing is polynomial-time solvable, and NP-hard otherwise. We identify a simple combinatorial property (every component of every graph in ℱ either has bounded size or is a bipartite graph with one of the sides having bounded size) such that if a hereditary class ℱ has this property, then ℱ-Packing admits a polynomial kernel, and has no polynomial (many-one) kernel otherwise, unless the polynomial hierarchy collapses. Furthermore, if ℱ does not have this property, then ℱ-Packing is either WK[1]-hard, W[1]-hard, or Long Path-hard, giving evidence that it does not admit polynomial Turing kernels either.For ℱ-Subgraph Test, we show that if every graph of a hereditary class ℱ satisfies the property that it is possible to delete a bounded number of vertices such that every remaining component has size at most two, then F-Subgraph Test is solvable in randomized polynomial time and it is NP-hard otherwise. We introduce a combinatorial property called (a, b, c, d)-splittability and show that if every graph in a hereditary class ℱ has this property, then F-Subgraph Test admits a polynomial Turing kernel and it is WK[1]-hard, W[1]-hard, or Long Path-hard otherwise. We do not give a complete characterization of the cases when F-Subgraph Test admits polynomial many-one kernels, but show examples that this question is much more fragile than the characterization for Turing kernels.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "4501274", "vector": [], "sparse_vector": [], "title": "Approximately Stable, School Optimal, and Student-Truthful Many-to-One Matchings (via Differential Privacy).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a mechanism for computing asymptotically stable school optimal matchings, while guaranteeing that it is an asymptotic dominant strategy for every student to report their true preferences to the mechanism. Our main tool in this endeavor is differential privacy: we give an algorithm that coordinates a stable matching using differentially private signals, which lead to our truthfulness guarantee. This is the first setting in which it is known how to achieve nontrivial truthfulness guarantees for students when computing school optimal matchings, assuming worst-case preferences (for schools and students) in large markets.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "4501275", "vector": [], "sparse_vector": [], "title": "The size of the core in assignment markets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Assignment markets involve matching with transfers, as in labor markets and housing markets. We consider a two-sided assignment market with agent types and stochastic structure similar to models used in empirical studies, and characterize the size of the core in such markets. Each agent has a randomly drawn productivity with respect to each type of agent on the other side. The value generated from a match between a pair of agents is the sum of the two productivity terms, each of which depends only on the type but not the identity of one of the agents, and a third deterministic term driven by the pair of types. We allow the number of agents to grow, keeping the number of agent types fixed. Let n be the number of agents and K be the number of types on the side of the market with more types. We find, under reasonable assumptions, that the relative variation in utility per agent over core outcomes is bounded as O*(1/n1/K), where polylogarithmic factors have been suppressed. Further, we show that this bound is tight in worst case. We also provide a tighter bound under more restrictive assumptions.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "4501276", "vector": [], "sparse_vector": [], "title": "The amortized cost of finding the minimum.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We obtain an essentially optimal tradeoff between the amortized cost of the three basic priority queue operations insert, delete and find-min in the comparison model. More specifically, we show thatfor any fixed ε > 0, where n is the number of items in the priority queue and A(insert), A(delete) and A(find-min) are the amortized costs of the insert, delete and find-min operations, respectively. In particular, if A(insert) + A(delete) = O(1), then A(find-min) = Ω(n), and A(find-min) = O(nα), for some α < 1, only if A(insert) + A(delete) = Ω(log n). (We can, of course, have A(insert) = O(1), A(delete) = O(log n), or vice versa, and A(find-min) = O(1).) Our lower bound holds even if randomization is allowed. Surprisingly, such fundamental bounds on the amortized cost of the operations were not known before. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, obtained similar bounds for the worst-case complexity of find-min.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "4501277", "vector": [], "sparse_vector": [], "title": "Streaming Lower Bounds for Approximating MAX-CUT.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "We consider the problem of estimating the value of max cut in a graph in the streaming model of computation. At one extreme, there is a trivial 2-approximation for this problem that uses only O(log n) space, namely, count the number of edges and output half of this value as the estimate for max cut value. On the other extreme, if one allows Õ(n) space, then a near-optimal solution to the max cut value can be obtained by storing an Õ(n)-size sparsifier that essentially preserves the max cut. An intriguing question is if poly-logarithmic space suffices to obtain a non-trivial approximation to the max-cut value (that is, beating the factor 2). It was recently shown that the problem of estimating the size of a maximum matching in a graph admits a non-trivial approximation in poly-logarithmic space.Our main result is that any streaming algorithm that breaks the 2-approximation barrier requires space even if the edges of the input graph are presented in random order Our result is obtained by exhibiting a distribution over graphs which are either bipartite or -far from being bipartite, and establishing that space is necessary to differentiate between these two cases. Thus as a direct corollary we obtain that space is also necessary to test if a graph is bipartite or -far from being bipartite. We also show that for any ε > 0, any streaming algorithm that obtains a (1 + ε)-approximation to the max cut value when edges arrive in adversarial order requires n1-O(ε) space, implying that Ω(n) space is necessary to obtain an arbitrarily good approximation to the max cut value.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "4501278", "vector": [], "sparse_vector": [], "title": "Distributed Computation of Large-scale Graph Problems.", "authors": ["<PERSON><PERSON><PERSON>", "Danupon <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the increasing need for fast distributed processing of large-scale graphs such as the Web graph and various social networks, we study a number of fundamental graph problems in the message-passing model, where we have k machines that jointly perform computation on an arbitrary n-node (typically, n ≫ k) input graph. The graph is assumed to be randomly partitioned among the k ≥ 2 machines (a common implementation in many real world systems). The communication is point-to-point, and the goal is to minimize the time complexity, i.e., the number of communication rounds, of solving various fundamental graph problems.We present lower bounds that quantify the fundamental time limitations of distributively solving graph problems. We first show a lower bound of Ω(n/k) rounds for computing a spanning tree (ST) of the input graph. This result also implies the same bound for other fundamental problems such as computing a minimum spanning tree (MST), breadth-first tree (BFS), and shortest paths tree (SPT). We also show an Ω(n/k2) lower bound for connectivity, ST verification and other related problems. Our lower bounds develop and use new bounds in random-partition communication complexity.To complement our lower bounds, we also give algorithms for various fundamental graph problems, e.g., PageRank, MST, connectivity, ST verification, shortest paths, cuts, spanners, covering problems, densest subgraph, subgraph isomorphism, finding triangles, etc. We show that problems such as PageRank, MST, connectivity, and graph covering can be solved in Õ(n/k) time (the notation Õ hides polylog(n) factors and an additive polylog(n) term); this shows that one can achieve almost linear (in k) speedup, whereas for shortest paths, we present algorithms that run in time (for (1 + ε)-factor approximation) and in time (for O(log n)-factor approximation) respectively.Our results step towards understanding the complexity of distributively solving large-scale graph problems.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "4501279", "vector": [], "sparse_vector": [], "title": "Internal Pattern Matching Queries in a Text and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider several types of internal queries, that is, questions about fragments of a given text $T$ specified in constant space by their locations in $T$. Our main result is an optimal data structure for Internal Pattern Matching (IPM) queries which, given two fragments $x$ and $y$, ask for a representation of all fragments contained in $y$ and matching $x$ exactly; this problem can be viewed as an internal version of the Exact Pattern Matching problem. Our data structure answers IPM queries in time proportional to the quotient $|y|/|x|$ of fragments' lengths, which is required due to the information content of the output. If $T$ is a text of length $n$ over an integer alphabet of size $\\sigma$, then our data structure occupies $O(n/ \\log_\\sigma n)$ machine words (that is, $O(n\\log \\sigma)$ bits) and admits an $O(n/ \\log_\\sigma n)$-time construction algorithm. We show the applicability of IPM queries for answering internal queries corresponding to other classic string processing problems. Among others, we derive optimal data structures reporting the periods of a fragment and testing the cyclic equivalence of two fragments. IPM queries have already found numerous further applications, following the path paved by the classic Longest Common Extension (LCE) queries of <PERSON><PERSON> and <PERSON> (<PERSON><PERSON><PERSON>, 1988). In particular, IPM queries have been implemented in grammar-compressed and dynamic settings and, along with LCE queries, constitute elementary operations of the PILLAR model, developed by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Wellnitz (FOCS 2020). On the way to our main result, we provide a novel construction of string synchronizing sets of Kempa and Kociumaka (STOC 2019). Our method, based on a new restricted version of the recompression technique of Je\\.z (J. ACM, 2016), yields a hierarchy of $O(\\log n)$ string synchronizing sets covering the whole spectrum of fragments' lengths.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "4501280", "vector": [], "sparse_vector": [], "title": "Robust Price of Anarchy Bounds via LP and Fenchel Duality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Bounding the price of anarchy (PoA), which quantifies the degradation in the quality of outcomes in a (pure) Nash equilibrium of a game, is one of the fundamental questions in computational game theory. However, for a large class of games, a pure NE may not always exist and hence a natural question to pursue is to quantify the inefficiency for weaker notions of equilibrium such as mixed Nash equilibrium, correlated equilibrium or coarse correlated equilibrium, all of which are known to exist for finite games. Several techniques have been developed for bounding the price of anarchy, yet, only a handful of them are applicable for proving the PoA bounds for general equilibrium concepts. Most notable among such techniques is <PERSON><PERSON><PERSON>'s elegant smoothness framework, which led to the concept of robust price of anarchy. The term refers to the inefficiency bounds applicable to general equilibrium notions such as coarse correlated equilibrium.In this paper, we develop a new framework based on <PERSON> and <PERSON>chel duality for bounding the robust price of anarchy for a large class of games. We use our framework to give the first PoA bounds for temporal routing games on graphs and energy minimization games in machine scheduling. Most notably, we present the first coordination mechanisms with bounded PoA for temporal routing over general graphs, show a related lowerbound result, and an improved bound on the price of stability for this game. Previously, coordination mechanisms with bounded PoA were only known for restricted classes of graphs such as trees or parallel edges. Furthermore, we demonstrate the wide applicability of our framework by giving new proofs of the PoA bounds for three classical games – weighted affine congestion games, competitive facility location games and simultaneous second price auctions. Our price anarchy bounds for these games match the ones known in the literature or obtained using the smoothness framework.All our proofs use the following technique: we first show that for a wide class of games, one can formulate the underlying optimization problem as a linear (or convex) program such that the (Fenchel) dual of the relaxation encodes the equilibrium condition. Further, the dual program has a specific structure with variables for players and resources, which can be naturally interpreted as the cost incurred by the players and the congestion of the resource in an equilibrium outcome. This lets us argue that our definition of dual variables satisfy the dual constraints and using the weak duality theorem we establish the PoA bounds.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "4501281", "vector": [], "sparse_vector": [], "title": "On Uniform Capacitated k-Median Beyond the Natural LP Relaxation.", "authors": ["<PERSON>"], "summary": "In this paper, we study the uniform capacitated k-median problem. In the problem, we are given a set ℱ of potential facility locations, a set of clients, a metric d over ℱ ∪ , an upper bound k on the number of facilities we can open and an upper bound u on the number of clients each facility can serve. We need to open a subset ⊆ ℱ of k facilities and connect clients in to facilities in so that each facility is connected by at most u clients. The goal is to minimize the total connection cost over all clients. Obtaining a constant approximation algorithm for this problem is a notorious open problem; most previous works gave constant approximations by either violating the capacity constraints or the cardinality constraint. Notably, all these algorithms are based on the natural LP-relaxation for the problem. The LP-relaxation has unbounded integrality gap, even when we are allowed to violate the capacity constraints or the cardinality constraint by a factor of 2 — ε.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "4501282", "vector": [], "sparse_vector": [], "title": "The Parameterized Complexity of k-Biclique.", "authors": ["Bing<PERSON> Lin"], "summary": "Given a graph G and a parameter k, the k-Blclique problem asks whether G contains a complete bipartite subgraph Kk,k. This is one of the most easily stated problems on graphs whose parameterized complexity has been long unknown. We prove that k-Blclique is W[1]-hard by giving an fpt-reduction from k-Clique to k-Blclique, thus solving this longstanding open problem.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "4501283", "vector": [], "sparse_vector": [], "title": "On the Quickest Flow Problem in Dynamic Networks - A Parametric Min-Cost Flow Approach.", "authors": ["Mao<PERSON> Lin", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Quickest Flow Problem in Dynamic Networks – A Parametric Min-Cost Flow Approach<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>pp.1343 - 1356Chapter DOI:https://doi.org/10.1137/1.*************.89PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the quickest flow problem in dynamic networks with a single source s and a single sink t: given an amount of flow F, find the minimum time needed to send it from s to t, and the corresponding optimal flow over time. We introduce new mathematical formulations and derive optimality conditions for the quickest flow problem. Based on the optimality conditions, we develop a new cost-scaling algorithm that leverages the parametric nature of the problem. The algorithm solves the quickest flow problem with integer arc costs in O(nm log(n2/m) log(nC)) time, where n, m, and C are the number of nodes, arcs, and the maximum arc cost, respectively. Our algorithm runs in the same time bound as the cost-scaling algorithm by <PERSON> and <PERSON><PERSON><PERSON> [10, 11] for solving the min-cost flow problem. This result shows for the first time that the quickest flow problem can be solved within the same time bound as one of the fastest algorithms for the min-cost flow problem. As a consequence, our algorithm will remain one of the fastest unless the quickest flow problem can be shown to be simpler than the min-cost flow problem. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "4501284", "vector": [], "sparse_vector": [], "title": "FPTAS for Counting Monotone CNF.", "authors": ["Jingcheng Liu", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)FPTAS for Counting Monotone CNF<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.1531 - 1548Chapter DOI:https://doi.org/10.1137/1.*************.101PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A monotone CNF formula is a Boolean formula in conjunctive normal form where each variable appears positively. We design a deterministic fully polynomial-time approximation scheme (FPTAS) for counting the number of satisfying assignments for a given monotone CNF formula when each variable appears in at most 5 clauses. Equivalently, this is also an FPTAS for counting set covers where each set contains at most 5 elements. If we allow variables to appear in a maximum of 6 clauses (or sets to contain 6 elements), it is NP-hard to approximate it. Thus, this gives a complete understanding of the approximability of counting for monotone CNF formulas. It is also an important step towards a complete characterization of the approximability for all bounded degree Boolean #CSP problems. In addition, we study the hypergraph matching problem, which arises naturally towards a complete classification of bounded degree Boolean #CSP problems, and show an FPTAS for counting 3D matchings of hypergraphs with maximum degree 4. Our main technique is correlation decay, a powerful tool to design deterministic FPTAS for counting problems defined by local constraints among a number of variables. All previous uses of this design technique fall into two categories: each constraint involves at most two variables, such as independent set, coloring, and spin systems in general; or each variable appears in at most two constraints, such as matching, edge cover, and holant problem in general. The CNF problems studied here have more complicated structures than these problems and require new design and proof techniques. As it turns out, the technique we developed for the CNF problem also works for the hypergraph matching problem. We believe that it may also find applications in other CSP or more general counting problems. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "4501285", "vector": [], "sparse_vector": [], "title": "Fast Generation of Random Spanning Trees and the Effective Resistance Metric.", "authors": ["Aleksander Madry", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new algorithm for generating a uniformly random spanning tree in an undirected graph. Our algorithm samples such a tree in expected time. This improves over the best previously known bound of , O(nω)) − that follows from the work of <PERSON><PERSON><PERSON> and <PERSON> [FOCS'09] and of <PERSON><PERSON><PERSON> et al. [<PERSON><PERSON>'96] – whenever the input graph is sufficiently sparse.At a high level, our result stems from carefully exploiting the interplay of random spanning trees, random walks, and the notion of effective resistance, as well as from devising a way to algorithmically relate these concepts to the combinatorial structure of the graph. This involves, in particular, establishing a new connection between the effective resistance metric and the cut structure of the underlying graph.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "4501286", "vector": [], "sparse_vector": [], "title": "Approximate Nearest Line Search in High Dimensions.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximate Nearest Line Search in High DimensionsSepideh MahabadiSepideh Mahabadipp.337 - 354Chapter DOI:https://doi.org/10.1137/1.*************.25PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the Approximate Nearest Line Search (NLS) problem. Given a set L of N lines in the high dimensional Euclidean space ℝd, the goal is to build a data structure that, given a query point q ∊ ℝd, reports a line ℓ ∊ L such that its distance to the query is within (1 + ε) factor of the distance of the closest line to the query point q. The problem is a natural generalization of the well-studied Approximate Nearest Neighbor problem for point sets (ANN), and is a natural first step towards understanding how to build efficient nearest-neighbor data structures for objects that are more complex than points. Our main result is a data structure that, for any fixed ε > 0, reports the approximate nearest line in time (d + log N + 1/ε)O(1) using O(N + d)O(1/ε2) space. This is the first high-dimensional data structure for this problem with poly-logarithmic query time and polynomial space. In contrast, the best previous data structure for this problem, due to <PERSON><PERSON> [16], required quasi-polynomial space. Up to polynomials, the bounds achieved by our data structure match the performance of the best algorithm for the approximate nearest neighbor problem for point sets. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "4501287", "vector": [], "sparse_vector": [], "title": "Robust Probabilistic Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust Probabilistic InferenceY<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.449 - 460Chapter DOI:https://doi.org/10.1137/1.*************.31PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Robust probabilistic inference is an extension of probabilistic inference, where some of the observations are adversarially corrupted. We model it as a zero-sum game between the adversary, who can select a modification rule, and the predictor, who wants to accurately predict the state of nature. Given a black-box access to a Bayesian inference in the classic (adversary-free) setting, our near optimal policy runs in polynomial time in the number of observations and the number of possible modification rules. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "4501288", "vector": [], "sparse_vector": [], "title": "Zigzag Persistence via Reflections and Transpositions.", "authors": ["<PERSON><PERSON><PERSON> Maria", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Zigzag Persistence via Reflections and TranspositionsC<PERSON><PERSON> Maria and <PERSON> and <PERSON>. <PERSON>pp.181 - 199Chapter DOI:https://doi.org/10.1137/1.*************.14PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We introduce a new algorithm for computing zigzag persistence, designed in the same spirit as the standard persistence algorithm. Our algorithm reduces a single matrix, maintains an explicit set of chains encoding the persistent homology of the current zigzag, and updates it under simplex insertions and removals. The total worst-case running time matches the usual cubic bound. A noticeable difference with the standard persistence algorithm is that we do not insert or remove new simplices \"at the end\" of the zigzag, but rather \"in the middle\". To do so, we use arrow reflections and transpositions, in the same spirit as reflection functors in quiver theory. Our analysis introduces new kinds of reflections in quiver representation theory: the \"injective and surjective diamonds\". It also introduces the \"transposition diamond\" which models arrow transpositions. For each type of diamond we are able to predict the changes in the interval decomposition and associated compatible bases. Arrow transpositions have been studied previously in the context of standard persistent homology, and we extend the study to the context of zigzag persistence. For both types of transformations, we provide simple procedures to update the interval decomposition and associated compatible homology basis. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "4501289", "vector": [], "sparse_vector": [], "title": "An exact characterization of tractable demand patterns for maximum disjoint path problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the following general disjoint paths problem: given a supply graph G, a set T ⊆ V(G) of terminals, a demand graph H on the vertices T, and an integer k, the task is to find a set of k pairwise vertex-disjoint valid paths, where we say that a path of the supply graph G is valid if its endpoints are in T and adjacent in the demand graph H. For a class H of graphs, we denote by Maximum Disjoint ℋ-Paths the restriction of this problem when the demand graph H is assumed to be a member of ℋ. We study the fixed-parameter tractability of this family of problems, parameterized by k. Our main result is a complete characterization of the fixed-parameter tractable cases of Maximum Disjoint ℋ-Paths for every hereditary class ℋ of graphs: it turns out that complexity depends on the existence of large induced matchings and large induced skew bicliques in the demand graph H (a skew biclique is a bipartite graph on vertices a1, …, an, b1, …, bn with ai and bj being adjacent if and only if i ≤ j). Specifically, we prove the following classification for every hereditary class ℋ.•If ℋ does not contain every matching and does not contain every skew biclique, then MAXIMUM Disjoint ℋ-Paths is FPT.•If ℋ does not contain every matching, but contains every skew biclique, then MAXIMUM DISJOINT ℋ-Paths is W[1]-hard, admits an FPT approximation, and the valid paths satisfy an analog of the Erdös-Pósa property.•If ℋ contains every matching, then MAXIMUM DISJOINT ℋ-Paths is W[1]-hard and the valid paths do not satisfy the analog of the Erdös-Pósa property.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "4501290", "vector": [], "sparse_vector": [], "title": "Robust randomized matchings.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The following zero-sum game is played on a weighted graph G: <PERSON> selects a matching M in G and <PERSON> selects a number k. Then, <PERSON> receives a payoff equal to the ratio of the weight of the top k edges of M to optk, which is the maximum weight of a matching of size at most k in G. If <PERSON> guarantees a payoff of at least α then it is called α-robust. In 2002, <PERSON><PERSON> and <PERSON><PERSON> gave an algorithm that returns a -robust matching, which is best possible for this setting.In this paper, we show that <PERSON> can improve on the guarantee of when allowing her to play a randomized strategy. For this setting, we devise a simple algorithm that returns a 1/ln(4)-robust randomized matching. The algorithm is based on the following non-trivial observation: If all edge weights are integer powers of 2, then any lexicographically optimum matching is 1-robust. We prove this property not only for matchings but for any independence system in which optk is a concave function of k. This class of systems includes matroid intersection, b-matchings, and strong 2-exchange systems. We also show that our robustness results for randomized matchings translate to an asymptotic robustness guarantee for deterministic matchings: When restricting <PERSON>'s choice to cardinalities larger than a given constant, then <PERSON> can find a single deterministic matching with approximately the same guaranteed payoff as in the randomized setting. In addition to the above results, we also give a new simple LP-based proof of <PERSON><PERSON> and <PERSON><PERSON>'s original result.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "4501291", "vector": [], "sparse_vector": [], "title": "Online Stochastic Matching with Unequal Probabilities.", "authors": ["Aranyak <PERSON>hta", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The online stochastic matching problem is a variant of online bipartite matching in which edges are labeled with probabilities. A match will \"succeed\" with the probability along that edge; this models, for instance, the click of a user in search advertisement. The goal is to maximize the expected number of successful matches. This problem was introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (FOCS 2012), who focused on the case where all probabilities in the graph are equal. They gave a 0.567-competitive algorithm for vanishing probabilities, relative to a natural benchmark, leaving the general case as an open question.This paper examines the general case where the probabilities may be unequal. We take a new algorithmic approach rather than generalizing that of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>: Our algorithm maintains, at each time, the probability that each offline vertex has succeeded thus far, and chooses assignments so as to maximize marginal contributions to these probabilities. When the algorithm does not observe the realizations of the edges, this approach gives a 0.5-competitive algorithm, which achieves the known upper bound for such \"non-adaptive\" algorithms. We then modify this approach to be \"semi-adaptive:\" if the chosen target has already succeeded, choose the arrival's \"second choice\" instead (while still updating the probabilities non-adaptively). With one additional tweak to control the analysis, we show that this algorithm achieves a competitive ratio of 0.534 for the unequal, vanishing probabilities setting. A \"fully-adaptive\" version of this algorithm turns out to be identical to an algorithm proposed, but not analyzed, in <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2012); we do not manage to analyze it either since it introduces too many dependencies between the stochastic processes. Our semi-adaptive algorithm thus can be seen as allowing analysis of competitive ratio while still capturing the power of adaptivity.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "4501292", "vector": [], "sparse_vector": [], "title": "Fast Lattice Point Enumeration with Minimal Overhead.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Fast Lattice Point Enumeration with Minimal Overhead<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.276 - 294Chapter DOI:https://doi.org/10.1137/1.*************.21PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Enumeration algorithms are the best currently known methods to solve lattice problems, both in theory (within the class of polynomial space algorithms), and in practice (where they are routinely used to evaluate the concrete security of lattice cryptography). However, there is an uncomfortable gap between our theoretical understanding and practical performance of lattice point enumeration algorithms. The algorithms typically used in practice have worst-case asymptotic running time 2O·(n2), but perform extremely well in practice, at least for all values of the lattice dimension for which experimentation is feasible. At the same time, theoretical algorithms (<PERSON><PERSON><PERSON>, Mathematics of Operation Research 12(3):415–440, 1987) are asymptotically superior (achieving 2O(n log n) running time), but they are never used in practice because they incur a substantial overhead that makes them uncompetitive for all reasonable values of the lattice dimension n. This gap is especially troublesome when algorithms are run in practice to evaluate the concrete security of a cryptosystem, and then experimental results are extrapolated to much larger dimension where solving lattice problems is computationally infeasible. We introduce a new class of (polynomial space) lattice enumeration algorithms that simultaneously achieve asymptotic efficiency (meeting the theoretical nO(n) = 2O(n log n) time bound) and practicality, matching or surpassing the performance of practical algorithms already in moderately low dimension. Key technical contributions that allow us to achieve this result are a new analysis technique that allows us to greatly reduce the number of recursive calls performed during preprocessing (from super exponential in n to single exponential, or even polynomial in n), a new enumeration technique that can be directly applied to projected lattice (basis) vectors, without the need to remove linear dependencies, and a modified block basis reduction method with fast (logarithmic) convergence properties. The last technique is used to obtain a new SVP enumeration procedure with Õ(nn/2e) running time, matching (even in the constant in the exponent) the optimal worst-case analysis (Hanrot and Stehlé, CRYPTO 2007) of Kannan's theoretical algorithm, but with far superior performance in practice. We complement our theoretical analysis with a preliminary set of experiments that not only support our practicality claims, but also allow to estimate the crossover point between different versions of enumeration algorithms, as well as asymptotically faster (but not quite practical) algorithms running in single exponential 2O(n) time and space. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "4501293", "vector": [], "sparse_vector": [], "title": "Sperner&apos;s Colorings, Hypergraph Labeling Problems and Fair Division.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We prove three results about colorings of the simplex reminiscent of <PERSON><PERSON><PERSON>'s Lemma, with applications in hardness of approximation and fair division.First, we prove a coloring lemma conjectured by [5]: Let Vk,q = {v ∊ ℤk+: ∑ki=1 vi = q} and Ek,q = {{a + e1,a + e2, …, a + ek}: a ∊ ℤk+, ∑ki = 1 ai = q — 1}· Then for every Sperner-admissible labeling {ℓ: Vk,q → [k] such that uℓ(v) > 0 for each v ∊ Vk,q), there are at least non-monochromatic hyperedges in Ek,q. This implies an optimal Unique-Games hardness of (k – 1 – ∊)-approximation for the Hypergraph Labeling with Color Lists problem [2]: Given a k-uniform hypergraph H = (V, E) with color lists L(v) ⊆ [k] ∀v ∊ V, find a labeling ℓ(v) ∊ L(v) that minimizes the number of non-monochromatic hyperedges. We also show that a (k — l)-approximation can be achieved. Second, we show that in contrast to <PERSON><PERSON><PERSON>'s Lemma, there is a Sperner-admissible labeling of Vk,q such that every hyperedge in Ek,q contains at most 4 colors. We present an interpretation of this statement in the context of fair division: There is a preference function on Δk,q = {x ∊ ℝk+: ∑ki=1 xi = q} such that for any division of q units of a resource, (x1, x2, …, xk) ∊ δk,q such that ∑ki=1 ⌊xi⌋ = q – 1, at most 4 players out of k are satisfied.Third, we prove that there are subdivisions of the simplex with a fractional labeling (analogous to a fractional solution for Min-CSP problems) such that every hyperedge in the subdivision uses only labelings with 1 or 2 colors. This means that a natural LP cannot distinguish instances of Hypergraph Labeling with Color Lists that can be labeled so that every hyperedge uses at most 2 colors, and instances that must have a rainbow hyperedge. We prove that this problem is indeed NP-hard for k = 3.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "4501294", "vector": [], "sparse_vector": [], "title": "Decomposing a Graph Into Expanding Subgraphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A paradigm that was successfully applied in the study of both pure and algorithmic problems in graph theory can be colloquially summarized as stating that any graph is close to being the disjoint union of expanders. Our goal in this paper is to show that in several of the instantiations of the above approach, the quantitative bounds that were obtained are essentially best possible. Two examples of our results are the following:•Motivated by the Unique Games Conjecture, <PERSON><PERSON>visan [FOCS O5] and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [FOCS 10] showed that given a graph G, one can remove only 1% of <PERSON>'s edges and thus obtain a graph in which each connected component has good expansion properties. We show that in both of these decomposition results, the expansion properties they guarantee are (essentially) best possible even when one is allowed to remove 99% of <PERSON>'s edges. In particular, our results imply that the eigenspace enumeration approach of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eurer cannot give (even quasi-) polynomial time algorithms for unique games.•A classical result of <PERSON>, <PERSON> and <PERSON> from 1979 states that if ℱ is a hereditary family of graphs and every graph in ℱ has a vertex separator of size n/(log n)1+o(1), then every graph in ℱ has O(n) edges. We construct a hereditary family of graphs with vertex separators of size n/(log n)1–o(1) such that not all graphs in the family have O(n) edges.The above results are obtained as corollaries of a new family of graphs, which we construct by picking random subgraphs of the hypercube, and analyze using (simple) arguments from the theory of metric embedding.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "4501295", "vector": [], "sparse_vector": [], "title": "A polylogarithmic space deterministic streaming algorithm for approximating distance to monotonicity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The distance to monotonicity of a sequence of n numbers is the minimum number of entries whose deletion leaves an increasing sequence. We give the first deterministic streaming algorithm that approximates the distance to monotonicity within a 1 + ε factor for any fixed ε > 0 and runs in space polylogarithmic in the length of the sequence and the range of the numbers. The best previous deterministic algorithm achieving the same approximation factor required space [9]. Previous polylogarithmic space algorithms were either randomized [10], or had approximation factor no better than 2 [8].We also present space lower bounds for this problem: Any deterministic streaming algorithm that gets a 1 + ε approximation requires space and any randomized algorithm requires space .", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "4501296", "vector": [], "sparse_vector": [], "title": "An algorithmic framework for obtaining lower bounds for random Ramsey problems extended abstract.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we introduce a general framework for proving lower bounds for various Ramsey type problems within random settings. The main idea is to view the problem from an algorithmic perspective: we aim at providing an algorithm that finds the desired colouring with high probability. Our framework allows to reduce the probabilistic problem of whether the Ramsey property at hand holds for random (hy-per)graphs with edge probability p to a deterministic question of whether there exists a finite graph that forms an obstruction.In the second part of the paper we apply this framework to address and solve various open problems. In particular, we provide a matching lower bound for the result of <PERSON><PERSON>, <PERSON> and <PERSON> (2010) and, independently, <PERSON><PERSON> and <PERSON> (2014+) for the classical Ramsey problem for hypergraphs in the case of cliques. A problem that was open for more than 15 years. We also improve a result of <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> (2010) for bounded anti-Ramsey problems in random graphs and extend it to hypergraphs. Finally, we provide matching lower bounds for a proper-colouring version of anti-Ramsey problems introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (2014).", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "4501297", "vector": [], "sparse_vector": [], "title": "Approximating Hereditary Discrepancy via Small Width Ellipsoids.", "authors": ["Aleksan<PERSON>", "<PERSON><PERSON>"], "summary": "The Discrepancy of a hypergraph is the minimum attainable value, over two-colorings of its vertices, of the maximum absolute imbalance of any hyperedge. The Hereditary Discrepancy of a hypergraph, defined as the maximum discrepancy of a restriction of the hypergraph to a subset of its vertices, is a measure of its complexity. <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (1986) related the natural extension of this quantity to matrices to rounding algorithms for linear programs, and gave a determinant based lower bound on the hereditary discrepancy. <PERSON><PERSON><PERSON><PERSON> (2011) showed that this bound is tight up to a polylogarithmic factor, leaving open the question of actually computing this bound. Recent work by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2013) showed a polynomial time Õ(log3n)-approximation to hereditary discrepancy, as a by-product of their work in differential privacy. In this paper, we give a direct simple O(log3/2 n)-approximation algorithm for this problem. We show that up to this approximation factor, the hereditary discrepancy of a matrix A is characterized by the optimal value of simple geometric convex program that seeks to minimize the largest ℓ∞ norm of any point in a ellipsoid containing the columns of A. This characterization promises to be a useful tool in discrepancy theory.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "4501298", "vector": [], "sparse_vector": [], "title": "Surprise probabilities in Markov chains.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Surprise probabilities in Markov chains<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.1759 - 1773Chapter DOI:https://doi.org/10.1137/1.*************.118PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In a Markov chain started at a state x, the hitting time τ(y) is the first time that the chain reaches another state y. We study the probability Px(τ(y) = t) that the first visit to y occurs precisely at a given time t. Informally speaking, the event that a new state is visited at a large time t may be considered a “surprise”. We prove the following three bounds: In any Markov chain with n states, In a reversible chain with n states, For random walk on a simple graph with n ≥ 2 vertices, We construct examples showing that these bounds are close to optimal. The main feature of our bounds is that they require very little knowledge of the structure of the Markov chain. To prove the bound for random walk on graphs, we establish the following estimate conjectured by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (private communication): For random walk on an n-vertex graph, for every initial vertex x, Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "4501299", "vector": [], "sparse_vector": [], "title": "On Termination of Integer Linear Loops.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental problem in program verification concerns the termination of simple linear loops of the form:where x is a vector of variables, u, a, and c are integer vectors, and A and B are integer matrices. Assuming the matrix A is diagonalisable, we give a decision procedure for the problem of whether, for all initial integer vectors u, such a loop terminates. The correctness of our algorithm relies on sophisticated tools from algebraic and analytic number theory, Diophantine geometry, and real algebraic geometry.To the best of our knowledge, this is the first substantial advance on a 10-year-old open problem of <PERSON><PERSON><PERSON> [38] and <PERSON><PERSON> [8].", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "4501300", "vector": [], "sparse_vector": [], "title": "On the Richter-<PERSON>sen Conjecture about Pairwise Intersecting Closed Curves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A long standing conjecture of <PERSON> and <PERSON><PERSON> states that the total number of intersection points between any n simple closed Jordan curves in the plane, so that any two of them intersect and no three curves pass through the same point, is at least (1 – o(1))n2.We confirm the above conjecture in several important cases, including the case (1) when all curves are convex, and (2) when the family of curves can be partitioned into two equal classes such that each curve from the first class is touching every curve from the second class. (Two curves are said to be touching if they have precisely one point in common, at which they do not properly cross.)An important ingredient of our proofs is the following statement: Let S be a family of the graphs of n continuous real functions defined on ℝ, no three of which pass through the same point. If there are nt pairs of touching curves in S, then the number of crossing points is .", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "4501301", "vector": [], "sparse_vector": [], "title": "Sharp Bounds on Formation-free Sequences.", "authors": ["<PERSON>"], "summary": "An (r, s)-formation is the concatenation of s permutations over an r-letter alphabet. Formation-free sequences are a generalization of standard Davenport-Schinzel sequences (where r = 2) and can be used to obtain good bounds on the extremal function of any forbidden subsequence. More recently formation-free sequences have been applied to bounding the size of sets of permutations with fixed VC-dimension.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "4501302", "vector": [], "sparse_vector": [], "title": "Linear Programming-based Approximation Algorithms for Multi-Vehicle Minimum Latency Problems (Extended Abstract).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider various multi-vehicle versions of the minimum latency problem. There is a fleet of k vehicles located at one or more depot nodes, and we seek a collection of routes for these vehicles that visit all nodes so as to minimize the total latency incurred, which is the sum of the client waiting times. We obtain an 8.497-approximation for the version where vehicles may be located at multiple depots and a 7.183-approximation for the version where all vehicles are located at the same depot, both of which are the first improvements on this problem in a decade. Perhaps more significantly, our algorithms exploit various LP relaxations for minimum-latency problems. We show how to effectively leverage two classes of LPs—configuration LPs and bidirected LP relaxations—that are often believed to be quite powerful but have only sporadically been effectively leveraged for network-design and vehicle-routing problems. This gives the first concrete evidence of the effectiveness of LP relaxations for this class of problems.The 8.497-approximation the multiple-depot version is obtained by rounding a near-optimal solution to an underlying configuration LP for the problem. The 7.183-approximation can be obtained both via rounding a bidirected LP for the single-depot problem or via more combinatorial means. The latter approach uses a bidirected LP to obtain the following key result that is of independent interest: for any k, we can efficiently compute a rooted tree that is at least as good, with respect to the prize-collecting objective (i.e., edge cost + number of uncovered nodes) as the best collection of k rooted paths. This substantially generalizes a result of <PERSON><PERSON><PERSON><PERSON> et al. [11] for k = 1, yet our proof is significantly simpler. Our algorithms are versatile and extend easily to handle various extensions involving: (i) weighted sum of latencies, (ii) constraints specifying which depots may serve which nodes, (iii) node service times.Finally, we propose a configuration LP that sheds further light on the power of LP relaxations for minimum-latency problems. We prove that the integrality gap of this LP is at most 3.592, even for the multi-depot problem, both via an efficient rounding procedure, and by showing that it is at least as powerful as a stroll-based lower bound that is oft-used for minimum-latency problems; the latter result implies an integrality gap of at most 3.03 when k = 1. Although, we do not know how to solve this LP in general, it can be solved (near-optimally) when k = 1, and this yields an LP-relative 3.592-approximation for the single-vehicle problem, matching (essentially) the current-best approximation ratio for this problem.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "4501303", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Orthogonal Point Enclosure Query and Point Location in Orthogonal Subdivisions in ℝ3.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, new results for two fundamental problems in the field of computational geometry are presented: orthogonal point enclosure query (OPEQ) in ℝ3 and point location in orthogonal subdivisions in ℝ3. All the results are in the pointer machine model of computation.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "4501304", "vector": [], "sparse_vector": [], "title": "Beating Exhaustive Search for Quantified Boolean Formulas and Connections to Circuit Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study algorithms for the satisfiability problem for quantified Boolean formulas (QBFs), and consequences of faster algorithms for circuit complexity.•We show that satisfiability of quantified 3-CNFs with m clauses, n variables, and two quantifier blocks (one existential block and one universal) can be solved deterministically in time . poly(m). For the case of multiple quantifier blocks (alternations), we show that satisfiability of quantified CNFs of size poly(n) on n variables with q quantifier blocks can be solved in 2n−n1/(q + 1)· poly(n) time by a zero-error randomized algorithm. These are the first provable improvements over brute force search in the general case, even for quantified polynomial-sized CNFs with two quantifier blocks.A second zero-error randomized algorithm solves QBF on circuits of size s in 2n–Ω(q) · poly(s) time when the number of quantifier blocks is q.•We complement these algorithms by showing that improvements on them would imply new circuit complexity lower bounds. For example, if satisfiability of quantified CNF formulas with n variables, poly(n) size and at most q quantifier blocks can be solved in time 2n–nwq (1/q) then the complexity class NEXP does not have O(log n) depth circuits of polynomial size. Furthermore, solving satisfiability of quantified CNF formulas with n variables, poly(n) size and O(log n) quantifier blocks in time 2n–w(log (n)) time would imply the same circuit complexity lower bound. The proofs of these results proceed by establishing strong relationships between the time complexity of QBF satisfiability over CNF formulas and the time complexity of QBF satisfiability over arbitrary Boolean formulas.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "4501305", "vector": [], "sparse_vector": [], "title": "Sequential Random Permutation, List Contraction and Tree Contraction are Highly Parallel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show that simple sequential randomized iterative algorithms for random permutation, list contraction, and tree contraction are highly parallel. In particular, if iterations of the algorithms are run as soon as all of their dependencies have been resolved, the resulting computations have logarithmic depth (parallel time) with high probability. Our proofs make an interesting connection between the dependence structure of two of the problems and random binary trees. Building upon this analysis, we describe linear-work, polylogarithmic-depth algorithms for the three problems. Although asymptotically no better than the many prior parallel algorithms for the given problems, their advantages include very simple and fast implementations, and returning the same result as the sequential algorithm. Experiments on a 40-core machine show reasonably good performance relative to the sequential algorithms.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "4501306", "vector": [], "sparse_vector": [], "title": "Spatial mixing and the connective constant: Optimal bounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of deterministic approximate counting of matchings and independent sets in graphs of bounded connective constant. More generally, we consider the problem of evaluating the partition functions of the monomer-dimer model (which is defined as a weighted sum over all matchings where each matching is given a weight γ|V| –2|M| in terms of a fixed parameter γ called the monomer activity) and the hard core model (which is defined as a weighted sum over all independent sets where an independent set I is given a weight γ|I| in terms of a fixed parameter γ called the vertex activity). The connective constant is a natural measure of the average degree of a graph which has been studied extensively in combinatorics and mathematical physics, and can be bounded by a constant even for certain unbounded degree graphs such as those sampled from the sparse Erdös-Rényi model (n, d/n).Our main technical contribution is to prove the best possible rates of decay of correlations in the natural probability distributions induced by both the hard core model and the monomer-dimer model in graphs with a given bound on the connective constant. These results on decay of correlations are obtained using a new framework based on the so-called message approach that has been extensively used recently to prove such results for bounded degree graphs. We then use these optimal decay of correlations results to obtain FPTASs for the two problems on graphs of bounded connective constant.In particular, for the monomer-dimer model, we give a deterministic FPTAS for the partition function on all graphs of bounded connective constant for any given value of the monomer activity. The best previously known deterministic algorithm was due to <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [ST<PERSON> 2007], and gave the same runtime guarantees as our results but only for the case of bounded degree graphs. For the hard core model, we give an FPTAS for graphs of connective constant Δ whenever the vertex activity λ λc(Δ) would imply that NP=RP [Sly, FOCS 2010]. The previous best known result in this direction was a recent paper by a subset of the current authors [FOCS 2013], where the result was established under the suboptimal condition λ < λc(Δ + 1).Our techniques also allow us to improve upon known bounds for decay of correlations for the hard core model on various regular lattices, including those obtained by Restrepo, Shin, Vigoda and Tetali [FOCS 11] for the special case of ℤ2 using sophisticated numerically intensive methods tailored to that special case.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "4501307", "vector": [], "sparse_vector": [], "title": "A note on the ring loading problem.", "authors": ["<PERSON>"], "summary": "The Ring Loading Problem is an optimal routing problem arising in the planning of optical communication networks which use bidirectional SONET rings. In mathematical terms, it is an unsplittable multicommodity flow problem on undirected ring networks. We prove that any split routing solution to the Ring Loading Problem can be turned into an unsplittable solution while increasing the load on any edge of the ring by no more than +7/5 D, where D is the maximum demand value. This improves upon a classical result of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (1998) who obtained a slightly larger bound of +3/2 D. We also present an improved lower bound 11/10 D (previously 101/100 D) on the best possible bound and disprove a famous and long-standing conjecture of <PERSON><PERSON><PERSON><PERSON><PERSON> et al. in this context.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "4501308", "vector": [], "sparse_vector": [], "title": "Tight lower bound for the channel assignment problem.", "authors": ["Arkadius<PERSON>"], "summary": "We study the complexity of the Channel Assignment problem. An open problem asks whether Channel Assignment admits an O(cn)-time algorithm, for a constant c independent of the weights on the edges. We answer this question in the negative i.e. we show that there is no 2°(n log n)-time algorithm solving Channel Assignment unless the Exponential Time Hypothesis fails. Note that the currently best known algorithm works in time O*(n!) = 2O(n log n) so our, lower bound is tight.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "4501309", "vector": [], "sparse_vector": [], "title": "On largest volume simplices and sub-determinants.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the problem of finding the simplex of largest volume in the convex hull of n points in ℚd can be approximated with a factor of O(log d) d/2 in polynomial time. This improves upon the previously best known approximation guarantee of d(d–1)/2 by <PERSON><PERSON><PERSON><PERSON>.On the other hand, we show that there exists a constant c > 1 such that this problem cannot be approximated with a factor of cd, unless P = NP. Our hardness result holds even if n = O(d), in which case there exists a d-approximation algorithm that relies on recent sampling techniques, where is again a constant.We show that similar results hold for the problem of finding the largest absolute value of a subdeterminant of a d × n matrix.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "4501310", "vector": [], "sparse_vector": [], "title": "Optimal approximation for submodular and supermodular optimization with bounded curvature.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We design new approximation algorithms for the problems of optimizing submodular and supermodular functions subject to a single matroid constraint. Specifically, we consider the case in which we wish to maximize a nondecreasing submodular function or minimize a nonincreasing supermodular function in the setting of bounded total curvature c. In the case of submodular maximization with curvature c, we obtain a (1 — c/e)-approximation — the first improvement over the greedy (1 — e−c)/c-approximation of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> from 1984, which holds for a cardinality constraint, as well as recent approaches that hold for an arbitrary matroid constraint.Our approach is based on modifications of the continuous greedy algorithm and non-oblivious local search, and allows us to approximately maximize the sum of a nonnegative, nondecreasing submodular function and a (possibly negative) linear function. We show how to reduce both submodular maximization and supermodular minimization to this general problem when the objective function has bounded total curvature.We prove that the approximation results we obtain are the best possible in the value oracle model, even in the case of a cardinality constraint. Finally, we give two concrete applications of our results in the settings of maximum entropy sampling, and the column-subset selection problem.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "4501311", "vector": [], "sparse_vector": [], "title": "Online Network Design Algorithms via Hierarchical Decompositions.", "authors": ["<PERSON><PERSON>"], "summary": "We develop a new approach for online network design and obtain improved competitive ratios for several problems. Our approach gives natural deterministic algorithms and simple analyses. At the heart of our work is a novel application of embeddings into hierarchically well-separated trees (HSTs) to the analysis of online network design algorithms — we charge the cost of the algorithm to the cost of the optimal solution on any HST embedding of the terminals. This analysis technique is widely applicable to many problems and gives a unified framework for online network design.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "4501312", "vector": [], "sparse_vector": [], "title": "The Speed of Evolution.", "authors": ["Nisheeth K. <PERSON>"], "summary": "In this paper we study the mixing times of a Wright-Fisher model for an asexual population. Typically, such models consist of a population of several genotypes where each genotype reproduces at a different rate, genotypes can mutate to each other, and the population is subject to the evolutionary pressure of selection. While such models have been used extensively to study different types of populations, recently, such stochastic finite population models have been used to model viral populations with the goal of understanding the effect of mutagenic drugs. Here, the time it takes for the population to reach a steady state is important both for carrying out simulations and to determine treatment strength and duration. Despite their importance, and having been widely studied, there has been a lack of such bounds for many relevant ranges of model parameters even for the case of two genotypes (single locus); primarily due to their difficulty, see [Eth11].", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "4501313", "vector": [], "sparse_vector": [], "title": "Minors and Dimension.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Minors and DimensionBartosz WalczakBartosz Walczakpp.1698 - 1707Chapter DOI:https://doi.org/10.1137/1.*************.113PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Streib and Trotter proved in 2012 that posets with bounded height and with planar cover graphs have bounded dimension. Recently, <PERSON><PERSON> et al. proved that the dimension is bounded for posets with bounded height whose cover graphs have bounded tree-width. In this paper, it is proved that posets of bounded height whose cover graphs exclude a fixed (topological) minor have bounded dimension. This generalizes both the aforementioned results and verifies a conjecture of <PERSON><PERSON> et al. The proof relies on the Robertson<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> structural decomposition theorems. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "4501314", "vector": [], "sparse_vector": [], "title": "Finding Four-Node Subgraphs in Triangle Time.", "authors": ["Virginia Vassilevska Williams", "<PERSON>", "<PERSON>", "Huacheng Yu"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Finding Four-Node Subgraphs in Triangle TimeVirginia <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> YuVirgin<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1671 - 1680<PERSON><PERSON>pter DOI:https://doi.org/10.1137/1.*************.111PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present new algorithms for finding induced four-node subgraphs in a given graph, which run in time roughly that of detecting a clique on three nodes (i.e., a triangle). The best known algorithms for triangle finding in an n-node graph take O(nω) time, where ω < 2.373 is the matrix multiplication exponent. We give a general randomized technique for finding any induced four-node subgraph, except for the clique or independent set on 4 nodes, in Õ (nω) time with high probability. The algorithm can be derandomized in some cases: we show how to detect a diamond (or its complement) in deterministic Õ(nω) time. Our approach substantially improves on prior work. For instance, the previous best algorithm for C4 detection ran in O(n3.3) time, and for diamond detection in O(n3) time. For sparse graphs with m edges, the best known triangle finding algorithm runs in O(m2ω/(ω+1)) ≤ O(m1.41) time. We give a randomized Õ(m2ω/(ω+1)) time algorithm (analogous to the best known for triangle finding) for finding any induced four-node subgraph other than C4, K4 and their complements. In the case of diamond detection, we also design a deterministic Õ(m2ω/(ω+1)) time algorithm. For C4 or its complement, we give randomized Õ(m(4ω–1)/(2ω+1)) ≤ O(m1.48) time finding algorithms. These algorithms substantially improve on prior work. For instance, the best algorithm for diamond detection ran in O(m1.5) time. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}, {"primary_key": "4501315", "vector": [], "sparse_vector": [], "title": "Front Matter.", "authors": [], "summary": "\"If you build it, they will come.\" In the past, this seemed to be the dominant paradigm for technology development and implementation in healthcare. This initial paradigm often led to poor user satisfaction and failed implementations of healthcare information technology. In the last few decades, informatics has embraced user-centered design principles to improve both the design and adoption of information and communication technologies. However, frequently the end-user is perceived to only be the clinician. Although the data in healthcare are about and are received from patients, patients are not usually perceived of as end-users of health information or health information technology. In the popular press, we are seeing a grass-roots effort from patients to change their role in their own health management. A change to a more dynamic partnership with clinicians means we need tools that are able to support patients as well as clinicians in this partnership. New online tools and mobile applications are sprouting up to fill the demand, but rigorous evaluation of these tools can be lacking; leading to questionable quality and concerns for patient safety. The informatics field has the expertise to provide critical leadership in this area. The call for this year's conference asked for authors to consider the role and voice of the patient. Patients themselves were invited to contribute papers describing their experiences in healthcare and their use of their own data. The papers here reflect not only the informatics innovations in the field, but also explore how to include the patients when considering design, implementation and long-term adoption of health information systems. We hope that the knowledge shared between ITCH 2015 participants will generate further discussions and collaborations and lead to breakthroughs in delivering effective and inclusive healthcare worldwide. <PERSON> of Health Information Science University of Victoria Victoria, British Columbia Canada.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.FM"}, {"primary_key": "4501316", "vector": [], "sparse_vector": [], "title": "An n-to-1 Bidder Reduction for Multi-item Auctions and its Applications.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An n-to-1 Bidder Reduction for Multi-item Auctions and its ApplicationsAndrew <PERSON><PERSON><PERSON><PERSON>rew <PERSON><PERSON><PERSON><PERSON>.92 - 109Chapter DOI:https://doi.org/10.1137/1.*************.8PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we introduce a novel approach for reducing the k-item n-bidder auction with additive valuation to k-item 1-bidder auctions. This approach, called the Best-Guess reduction, can be applied to address several central questions in optimal revenue auction theory such as the relative strength of simple versus complex mechanisms, the power of randomization, and Bayesian versus dominant-strategy implementations. First, when the items have independent valuation distributions, we present a deterministic mechanism called Deterministic Best-Guess that yields at least a constant fraction of the optimal revenue by any randomized mechanism. This also gives the first simple mechanism that achieves constant fraction optimal revenue for such multi-buyer multi-item auctions. Second, if all the nk valuation random variables are independent, the optimal revenue achievable in dominant strategy incentive compatibility (DSIC) is shown to be at least a constant fraction of that achievable in Bayesian incentive compatibility (BIC). Third, when all the nk values are identically distributed according to a common one-dimensional distribution F, the optimal revenue is shown to be expressible in the closed form Θ(k(r+ ∫mr0 (1 – F(x)n)dx)) where r = supx≥0 x(1 – F(x)n) and m = [k/n]; this revenue is achievable by a simple mechanism called 2nd-Price Bundling. All our results apply to arbitrary distributions, regular or irregular. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "4501317", "vector": [], "sparse_vector": [], "title": "Using Optimization to Break the Epsilon Barrier: A Faster and Simpler Width-Independent Algorithm for Solving Positive Linear Programs in Parallel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2015 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Using Optimization to Break the Epsilon Barrier: A Faster and Simpler Width-Independent Algorithm for Solving Positive Linear Programs in ParallelZey<PERSON> and <PERSON> and <PERSON>.1439 - 1456Chapter DOI:https://doi.org/10.1137/1.*************.95PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the design of nearly-linear-time algorithms for approximately solving positive linear programs. Both the parallel and the sequential deterministic versions of these algorithms require Õ(ε−4) iterations, a dependence that has not been improved since the introduction of these methods in 1993 by <PERSON><PERSON> and <PERSON><PERSON>. Moreover, previous algorithms and their analyses rely on update steps and convergence arguments that are combinatorial in nature, and do not seem to arise naturally from an optimization viewpoint. In this paper, we leverage insights from optimization theory to construct a novel algorithm that breaks the longstanding Õ(ε−4) barrier. Our algorithm has a simple analysis and a clear motivation. Our work introduces a number of novel techniques, such as the combined application of gradient descent and mirror descent, and a truncated, smoothed version of the standard multiplicative weight update, which may be of independent interest. Previous chapter Next chapter RelatedDetails Published:2015ISBN:978-1-61197-374-7eISBN:978-1-61197-373-0 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA15Book Pages:viii + 2048", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "4521111", "vector": [], "sparse_vector": [], "title": "Proceedings of the Twenty-Sixth Annual ACM-SIAM Symposium on Discrete Algorithms, SODA 2015, San Diego, CA, USA, January 4-6, 2015", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This symposium focuses on research topics related to efficient algorithms and data structures for discrete problems. In addition to the design of such methods and structures, the scope also includes their use, performance analysis, and the mathematical problems related to their development or limitations. Performance analyses may be analytical or experimental and may address worst-case or expected-case performance. Studies can be theoretical or based on data sets that have arisen in practice and may address methodological issues involved in performance analysis.", "published": "2015-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************"}]