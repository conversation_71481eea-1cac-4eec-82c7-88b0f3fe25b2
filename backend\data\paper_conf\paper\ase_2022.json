[{"primary_key": "1727536", "vector": [], "sparse_vector": [], "title": "Outcome-Preserving Input Reduction for Scientific Data Analysis Workflows.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Analysis of data is the foundation of multiple scientific disciplines, manifesting in complex and diverse scientific data analysis workflows often involving exploratory analyses. Such analyses represent a particular case for traditional data engineering workflows, as results may be hard to interpret and judge whether they are correct or not, and where experimentation is a central theme. Oftentimes, there are certain aspects of a result which are suspicious and which should be further investigated to increase the trustworthiness of the workflow's outcome. To this end, we advocate a semi-automated approach to reducing a workflow's input data while preserving a specified outcome of interest, facilitating irregularity localization by narrowing down the search space for spotting corrupted input data or wrong assumptions made about it. We outline our vision on building engineering support for outcome-preserving input reduction within data analysis workflows, and report on preliminary results obtained from applying an early research prototype on a computational notebook taken from an online community of data scientists and machine learning practitioners.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559558"}, {"primary_key": "1727537", "vector": [], "sparse_vector": [], "title": "ThirdEye: Attention Maps for Safe Autonomous Driving Systems.", "authors": ["<PERSON>", "Paulo J. N<PERSON>s", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>", "<PERSON>"], "summary": "Automated online recognition of unexpected conditions is an indispensable component of autonomous vehicles to ensure safety even in unknown and uncertain situations. In this paper we propose a runtime monitoring technique rooted in the attention maps computed by explainable artificial intelligence techniques. Our approach, implemented in a tool called ThirdEye, turns attention maps into confidence scores that are used to discriminate safe from unsafe driving behaviours. The intuition is that uncommon attention maps are associated with unexpected runtime conditions.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556968"}, {"primary_key": "1727538", "vector": [], "sparse_vector": [], "title": "A Novel Coverage-guided Greybox Fuzzing based on Power Schedule Optimization with Time Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saihua Cai", "<PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coverage-guided Greybox fuzzing is regarded as a practical approach to detect software vulnerabilities, which targets to expand code coverage as much as possible. A common implementation is to assign more energy to such seeds which find new edges with less execution time. However, solely considering new edges may be less effective because some hard-to-find branches often exist in the complex code of program. Code complexity is one of the key indicators to measure the code security. Compared to the code with simple structure, the program with higher code complexity is more likely to find more branches and cause security problems. In this paper, we propose a novel fuzzing method which further uses code complexity to optimize power schedule process in AFL (American Fuzzy Lop) and AFLFAST (American Fuzzy Lop Fast). The goal of our method is to generate inputs which are more biased toward the code with higher complexity of the program under test. In addition, we conduct a preliminary empirical study under three widely used real-world programs, and the experimental results show that the proposed approach can trigger more crashes as well as improve the coverage discovery.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559550"}, {"primary_key": "1727539", "vector": [], "sparse_vector": [], "title": "Next Syntactic-Unit Code Completion and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code completion is an important feature in an IDE to improve developers' productivity. Existing code completion approaches focus on completing the current code token, next token or statement, or code pattern. We propose AstCC, a code completion approach to suggest the next syntactic unit via an AST-based statistical language model. AstCC learns from a large code corpus to derive the next AST subtree representing a syntactic unit, and then fills in the template with the concrete variables from the current program scope. Our empirical evaluation shows that AstCC can correctly suggest the next syntactic unit in 33% of the cases, and in 62% of the cases, it correctly suggests within five candidates. We will also explain the potential applications of AstCC in automated program repair, automated test case generation, and syntactic pattern mining.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559544"}, {"primary_key": "1727540", "vector": [], "sparse_vector": [], "title": "MalWhiteout: Reducing Label Errors in Android Malware Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning based Android malware detection has attracted a great deal of research work in recent years. A reliable malware dataset is critical to evaluate the effectiveness of malware detection approaches. Unfortunately, existing malware datasets used in our community are mainly labelled by leveraging existing anti-virus services (i.e., VirusTotal), which are prone to mislabelling. This, however, would lead to the inaccurate evaluation of the malware detection techniques. Removing label noises from Android malware datasets can be quite challenging, especially at a large data scale. To address this problem, we propose an effective approach called MalWhiteout to reduce label errors in Android malware datasets. Specifically, we creatively introduce Confident Learning (CL), an advanced noise estimation approach, to the domain of Android malware detection. To combat false positives introduced by CL, we incorporate the idea of ensemble learning and inter-app relation to achieve a more robust capability in noise detection. We evaluate MalWhiteout on a curated large-scale and reliable benchmark dataset. Experimental results show that MalWhiteout is capable of detecting label noises with over 94% accuracy even at a high noise ratio (i.e., 30%) of the dataset. MalWhiteout outperforms the state-of-the-art approach in terms of both effectiveness (8% to 218% improvement) and efficiency (70 to 249 times faster) across different settings. By reducing label noises, we show that the performance of existing malware detection approaches can be improved.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560418"}, {"primary_key": "1727541", "vector": [], "sparse_vector": [], "title": "A Light Bug Triage Framework for Applying Large Pre-trained Language Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Assigning appropriate developers to the bugs is one of the main challenges in bug triage. Demands for automatic bug triage are increasing in the industry, as manual bug triage is labor-intensive and time-consuming in large projects. The key to the bug triage task is extracting semantic information from a bug report. In recent years, large Pre-trained Language Models (PLMs) including BERT [4] have achieved dramatic progress in the natural language processing (NLP) domain. However, applying large PLMs to the bug triage task for extracting semantic information has several challenges. In this paper, we address the challenges and propose a novel framework for bug triage named LBT-P, standing for Light Bug Triage framework with a Pre-trained language model. It compresses a large PLM into small and fast models using knowledge distillation techniques and also prevents catastrophic forgetting of PLM by introducing knowledge preservation fine-tuning. We also develop a new loss function exploiting representations of earlier layers as well as deeper layers in order to handle the overthinking problem. We demonstrate our proposed framework on the real-world private dataset and three public real-world datasets [11]: Google Chromium, Mozilla Core, and Mozilla Firefox. The result of the experiments shows the superiority of LBT-P.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556898"}, {"primary_key": "1727542", "vector": [], "sparse_vector": [], "title": "Automated Feedback Generation for Competition-Level Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Hanyuan Shi", "Ruzica Piskac"], "summary": "Competitive programming has become a popular way for programmers to test their skills. Competition-level programming problems are challenging in nature, and participants often fail to solve the problem on their first attempt. Some online platforms for competitive programming allow programmers to practice on competition-level problems, and the standard feedback for an incorrect practice submission is the first test case that the submission fails. Often, the failed test case does not provide programmers with enough information to resolve the errors in their code, and they abandon the problem after making several more unsuccessful attempts.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560425"}, {"primary_key": "1727543", "vector": [], "sparse_vector": [], "title": "Identifying Solidity Smart Contract API Documentation Errors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart contracts are gaining popularity as a means to support transparent, traceable, and self-executing decentralized applications, which enable the exchange of value in a trustless environment. Developers of smart contracts rely on various libraries, such as OpenZeppelin for Solidity contracts, to improve application quality and reduce development costs. The API documentations of these libraries are important sources of information for developers who are unfamiliar with the APIs. Yet, maintaining high-quality documentations is non-trivial, and errors in documentations may place barriers for developers to learn the correct usages of APIs. In this paper, we propose a technique, DocCon, to detect inconsistencies between documentations and the corresponding code for Solidity smart contract libraries. Our fact-based approach allows inconsistencies of different severity levels to be queried, from a database containing precomputed facts about the API code and documentations. DocCon successfully detected high-priority API documentation errors in popular smart contract libraries, including mismatching parameters, missing requirements, outdated descriptions, etc. Our experiment result shows that DocCon achieves good precision and is applicable to different libraries: 29 and 22 out of our reported 40 errors have been confirmed and fixed by library developers so far.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556963"}, {"primary_key": "1727544", "vector": [], "sparse_vector": [], "title": "CBMC-SSM: Bounded Model Checking of C Programs with Symbolic Shadow Memory.", "authors": ["<PERSON><PERSON>", "Salvatore <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic program analysis tools such as Eraser, TaintCheck, or ThreadSanitizer abstract the contents of individual memory locations and store the abstraction results in a separate data structure called shadow memory. They then use this meta-information to efficiently implement the actual analyses. In this paper, we describe the implementation of an efficient symbolic shadow memory extension for the CBMC bounded model checker that can be accessed through an API, and sketch its use in the design of a new data race analyzer that is implemented by a code-to-code translation.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559523"}, {"primary_key": "1727545", "vector": [], "sparse_vector": [], "title": "Low-Resources Project-Specific Code Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code summarization generates brief natural language descriptions of source code pieces, which can assist developers in understanding code and reduce documentation workload. Recent neural models on code summarization are trained and evaluated on large-scale multi-project datasets consisting of independent code-summary pairs. Despite the technical advances, their effectiveness on a specific project is rarely explored. In practical scenarios, however, developers are more concerned with generating high-quality summaries for their working projects. And these projects may not maintain sufficient documentation, hence having few historical code-summary pairs. To this end, we investigate low-resource project-specific code summarization, a novel task more consistent with the developers' requirements. To better characterize project-specific knowledge with limited training samples, we propose a meta transfer learning method by incorporating a lightweight fine-tuning mechanism into a meta-learning framework. Experimental results on nine real-world projects verify the superiority of our method over alternative ones and reveal how the project-specific knowledge is learned.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556909"}, {"primary_key": "1727546", "vector": [], "sparse_vector": [], "title": "Coverage-based Greybox Fuzzing with Pointer Monitoring for C Programs.", "authors": ["<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "C has been regarded as a dominant programming language for system software implementation. Meanwhile, it often suffers from various memory vulnerabilities due to its low-level memory control. Quite massive approaches are proposed to enhance memory security, among which Coverage-based Greybox Fuzzing (CGF) is very popular because of its practicality and satisfactory effectiveness. However, CGF identifies vulnerabilities based on the catched crashes, thus cannot detect vulnerabilities with non-crash. In this paper, we consider to trace pointer metadata (status, bounds and referents) to detect more various vulnerabilities. Additionally, since pointers in C are often directly related to memory operations, we design two standards to further use pointer metadata as the guidance of CGF, making fuzzing process target to the vulnerable part of programs.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559566"}, {"primary_key": "1727547", "vector": [], "sparse_vector": [], "title": "Reentrancy Vulnerability Detection and Localization: A Deep Learning Based Two-phase Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart contracts have been widely and rapidly used to automate financial and business transactions together with blockchains, helping people make agreements while minimizing trusts. With millions of smart contracts deployed on blockchain, various bugs and vulnerabilities in smart contracts have emerged. Following the rapid development of deep learning, many recent studies have used deep learning for vulnerability detection to conduct security checks before deploying smart contracts. These approaches show effective results on detecting whether a smart contract is vulnerable or not whereas their results on locating suspicious statements responsible for the detected vulnerability are still unsatisfactory.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560428"}, {"primary_key": "1727548", "vector": [], "sparse_vector": [], "title": "LawBreaker: An Approach for Specifying Traffic Laws and Fuzzing Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous driving systems (ADSs) must be tested thoroughly before they can be deployed in autonomous vehicles. High-fidelity simulators allow them to be tested against diverse scenarios, including those that are difficult to recreate in real-world testing grounds. While previous approaches have shown that test cases can be generated automatically, they tend to focus on weak oracles (e.g. reaching the destination without collisions) without assessing whether the journey itself was undertaken safely and satisfied the law. In this work, we propose , an automated framework for testing ADSs against real-world traffic laws, which is designed to be compatible with different scenario description languages. provides a rich driver-oriented specification language for describing traffic laws, and a fuzzing engine that searches for different ways of violating them by maximising specification coverage. To evaluate our approach, we implemented it for Apollo+LGSVL and specified the traffic laws of China. was able to find 14 violations of these laws, including 173 test cases that caused accidents.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556897"}, {"primary_key": "1727549", "vector": [], "sparse_vector": [], "title": "Trimmer: Context-Specific Code Reduction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Trimmer, a state-of-the-art tool for reducing code size. <PERSON><PERSON> reduces code sizes by specializing programs with respect to constant inputs provided by developers. The static data can be provided as command-line options or through configuration files. The constants define the features that must be retained, which in turn determine the features that are unused in a specific deployment (and can therefore be removed). <PERSON>mmer includes sophisticated compiler transformations for input specialization, supports precise yet efficient context-sensitive inter-procedural constant propagation, and introduces a custom loop unroller. Trimmer is easy-to-use and extensively parameterized. We discuss how Trimmer can be configured by developers to explicitly trade analysis precision and specialization time. We also provide a high-level description of <PERSON><PERSON>'s static analysis passes. The source code is publicly available at: https://github.com/ashish-gehani/Trimmer. A video demonstration can be found here: https://youtu.be/6pAuJ68INnI.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559529"}, {"primary_key": "1727550", "vector": [], "sparse_vector": [], "title": "Few-shot training LLMs for project-specific code-summarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Very large language models (LLMs), such as GPT-3 and Codex have achieved state-of-the-art performance on several natural-language tasks, and show great promise also for code. A particularly exciting aspect of LLMs is their knack for few-shot and zero-shot learning: they can learn to perform a task with very few examples. Few-shotting has particular synergies in software engineering, where there are a lot of phenomena (identifier names, APIs, terminology, coding patterns) that are known to be highly project-specific. However, project-specific data can be quite limited, especially early in the history of a project; thus the few-shot learning capacity of LLMs might be very relevant. In this paper, we investigate the use few-shot training with the very large GPT (Generative Pre-trained Transformer) Codex model, and find evidence suggesting that one can significantly surpass state-of-the-art models for code-summarization, leveraging project-specific training.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559555"}, {"primary_key": "1727551", "vector": [], "sparse_vector": [], "title": "MCDA Framework for Edge-Aware Multi-Cloud Hybrid Architecture Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deploying applications on hybrid clouds with computational artifacts distributed over public backends and private edges involve several constraints. Designing such deployment requires application architects to solve several challenges, spanning over hard regulatory policy constraints as well as business policy constraints such as enablement of privacy by on-prem processing of data to the extent the business wants, backend support of privacy enabling technologies (PET), sustainability in terms of green energy utilization, latency sensitivity of the application. In this paper, we propose to optimize hybrid cloud application architectures, while taking all those factors into consideration, and empirically demonstrate the effectiveness of our approach. To the best of our knowledge, this work is the first of its kind.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559501"}, {"primary_key": "1727552", "vector": [], "sparse_vector": [], "title": "AntiCopyPaster: Extracting Code Duplicates As Soon As They Are Introduced in the IDE.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We developed a plugin for IntelliJ IDEA called AntiCopyPaster, which tracks the pasting of code fragments inside the IDE and suggests the appropriate Extract Method refactoring to combat the propagation of duplicates. Unlike the existing approaches, our tool is integrated with the developer's workflow, and pro-actively recommends refactorings. Since not all code fragments need to be extracted, we develop a classification model to make this decision. When a developer copies and pastes a code fragment, the plugin searches for duplicates in the currently opened file, waits for a short period of time to allow the developer to edit the code, and finally inferences the refactoring decision based on a number of features.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559537"}, {"primary_key": "1727553", "vector": [], "sparse_vector": [], "title": "Augur: Dynamic Taint Analysis for Asynchronous JavaScript.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dynamic taint analysis (DTA) is a popular approach to help protect JavaScript applications against injection vulnerabilities. In 2016, the ECMAScript 7 JavaScript language standard introduced many language features that most existing DTA tools for JavaScript do not support, e.g., the async/await keywords for asynchronous programming. We present Augur, a high-performance dynamic taint analysis for ES7 JavaScript that leverages VM-supported instrumentation. Integrating directly with a public, stable instrumentation API gives <PERSON><PERSON> the ability to run with high performance inside the VM and remain resilient to language revisions. We extend the abstract-machine approach to DTA to handle asynchronous function calls. In addition to providing the classic DTA use case of injection vulnerability detection, Augur is highly configurable to support any type of taint analysis, making it useful outside of the security domain. We evaluated Augur on a set of 20 benchmarks, and observed a median runtime overhead of only 1.77 ×, a median performance improvement of 298% compared to the previous state-of-the-art.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559522"}, {"primary_key": "1727554", "vector": [], "sparse_vector": [], "title": "Building recommenders for modelling languages with Droid.", "authors": ["Lissette Almonte", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recommender systems (RSs) are increasingly being used to help in all sorts of software engineering tasks, including modelling. However, building a RS for a modelling notation is costly. This is especially detrimental for development paradigms that rely on domain-specific languages (DSLs), like model-driven engineering and lowcode approaches.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559521"}, {"primary_key": "1727555", "vector": [], "sparse_vector": [], "title": "Reflecting on Recurring Failures in IoT Development.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As IoT systems are given more responsibility and autonomy, they offer greater benefits, but also carry greater risks. We believe this trend invigorates an old challenge of software engineering: how to develop high-risk software-intensive systems safely and securely under market pressures? As a first step, we conducted a systematic analysis of recent IoT failures to identify engineering challenges. We collected and analyzed 22 news reports and studied the sources, impacts, and repair strategies of failures in IoT systems. We observed failure trends both within and across application domains. We also observed that failure themes have persisted over time. To alleviate these trends, we outline a research agenda toward a Failure-Aware Software Development Life Cycle for IoT development. We propose an encyclopedia of failures and an empirical basis for system postmortems, complemented by appropriate automated tools.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559545"}, {"primary_key": "1727556", "vector": [], "sparse_vector": [], "title": "A model for automatic generating reusable code from multiple GUIs.", "authors": ["Cícero A. G. Araújo"], "summary": "In the context of user interface-oriented software development, the task of translating a GUI into code requires sufficient knowledge to identify visual elements and how to code it for one or more platforms. In addition, other issues are important, such as reuse, componentization and understanding of the behavior of trivial visual elements. This is a repetitive and tedious task that could be automated. To perform automation this task many challenges depend on the starting point (hand-draw or hi-fidelity images), the detection and recognition of visual elements from images, their data representation and the code generation itself. This work aims to build a model that makes it possible to automate the process of code generation from images so that it is possible to infer which visual elements are reusable across a range of GUIs and in such a way that you can navigate between GUIs in the same application. This study is being conducted through a DSR (Design Science Research) and so far some classes of problems and artifacts have been found that help to understand how to extract and represent data from GUI and generate web, android and ios application code. The open questions about this problem concern how to identify individual elements and in a group, how to represent these visual elements in a way that can be used in code generators and what is the most efficient code generator for this type of problem. The answers to these questions are part of the next steps of this work.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559563"}, {"primary_key": "1727557", "vector": [], "sparse_vector": [], "title": "Intelligent Code Review Assignment for Large Scale Open Source Software Stacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the process of developing software, code review is crucial. By identifying problems before they arise in production, it enhances the quality of the code. Finding the best reviewer for a code change, however, is extremely challenging especially in large scale, especially open source software stacks with cross functioning designs and collaborations among multiple developers and teams. Additionally, a review by someone who lacks knowledge and understanding of the code can result in high resource consumption and technical errors. The reviewers who have the specialty in both functioning (domain knowledge) and non-functioning areas of a commit are considered as the most qualified reviewer to look over any changes to the code. Quality attributes serve as the connection among the user requirements, delivered function description, software architecture and implementation through put the entire software stack cycle. In this study, we target on auto reviewer assignment in large scale software stacks and aim to build a self-learning, and self-correct platform for intelligently matching between a commit based on its quality attributes and the skills sets of reviewers. To achieve this, quality attributes are classified and abstracted from the commit messages and based on which, the commits are assigned to the reviewers with the capability in reviewing the target commits. We first designed machine learning schemes for abstracting quality attributes based on historical data from the OpenStack repository. Two models are built and trained for automating the classification of the commits based on their quality attributes using the manual labeling of commits and multi-class classifiers. We then positioned the reviewers based on their historical data and the quality attributes characteristics. Finally we selected the recommended reviewer based on the distance between a commit and candidate reviewers. In this paper, we demonstrate how the models can choose the best quality attributes and assign the code review to the most qualified reviewers. With a comparatively small training dataset, the models are able to achieve F-1 scores of 77% and 85.31%, respectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561147"}, {"primary_key": "1727558", "vector": [], "sparse_vector": [], "title": "Dancing, not Wrestling: Moving from Compliance to Concordance for Secure Software Development.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure software development has become an increasingly important focus for research in recent years, not least because of advances in technology such as AI, machine learning (AI/ML), robotics, and autonomous systems (RAS). AI/ML and RAS facilitate automated decision-making and have the capability to have a significant impact on society. As such this technology needs to be trustworthy, and secure software development is a key attribute for trustworthiness. Software developers frequently have responsibility and accountability for delivering secure code but limited authority over how this is achieved. Authority tends to lie with cyber security professionals who mandate security processes, tools and training, often with limited success. Our research objective was to better understand how to bridge this gap between software developers and cyber security practitioners so that authority, responsibility and accountability are shared equally. We took inspiration from healthcare research that looks at the relationship between compliance, adherence and concordance. We use this research as a lens through which to analyse qualitative data from 35 interviews with professional software developers. Our research suggests that if software developers and cyber security professionals move to a point of concordance in their interactions it could lead to the negotiation of more realistic cyber security solutions, as well as removing friction from the practice of software developers and ultimately lead to more secure and trustworthy systems.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561145"}, {"primary_key": "1727559", "vector": [], "sparse_vector": [], "title": "Efficient Greybox Fuzzing to Detect Memory Errors.", "authors": ["Jinsheng Ba", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Greybox fuzzing is a proven and effective testing method for the detection of security vulnerabilities and other bugs in modern software systems. Greybox fuzzing can also be used in combination with a sanitizer, such as AddressSanitizer (ASAN), to further enhance the detection of certain classes of bugs such as buffer overflow and use-after-free errors. However, sanitizers also introduce additional performance overheads, and this can degrade the performance of greybox mode fuzzing—measured in the order of 2.36 × for fuzzing with ASAN—partially negating the benefit of using a sanitizer in the first place. Recent research attributes the extra overhead to program startup/teardown costs that can dominate fork-mode fuzzing.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561161"}, {"primary_key": "1727560", "vector": [], "sparse_vector": [], "title": "A fault injection platform for learning AIOps models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In today's IT environment with a growing number of costly outages, increasing complexity of the systems, and availability of massive operational data, there is a strengthening demand to effectively leverage Artificial Intelligence and Machine Learning (AI/ML) towards enhanced resiliency. In this paper, we present an automatic fault injection platform to enable and optimize the generation of data needed for building AI/ML models to support modern IT operations. The merits of our platform include the ease of use, the possibility to orchestrate complex fault scenarios and to optimize the data generation for the modeling task at hand. Specifically, we designed a fault injection service that (i) combines fault injection with data collection in a unified framework, (ii) supports hybrid and multi-cloud environments, and (iii) does not require programming skills for its use. Our current implementation covers the most common fault types both at the application and infrastructure levels. The platform also includes some AI capabilities. In particular, we demonstrate the interventional causal learning capability currently available in our platform. We show how our system is able to learn a model of error propagation in a micro-service application in a cloud environment (when the communication graph among micro-services is unknown and only logs are available) for use in subsequent applications such as fault localization.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559503"}, {"primary_key": "1727561", "vector": [], "sparse_vector": [], "title": "B-AIS: An Automated Process for Black-box Evaluation of Visual Perception in AI-enabled Software against Domain Semantics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "AI-enabled software systems (AIS) are prevalent in a wide range of applications, such as visual tasks of autonomous systems, extensively deployed in automotive, aerial, and naval domains. Hence, it is crucial for humans to evaluate the model's intelligence before AIS is deployed to safety-critical environments, such as public roads.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561162"}, {"primary_key": "1727562", "vector": [], "sparse_vector": [], "title": "A Drift Handling Approach for Self-Adaptive ML Software in Scalable Industrial Processes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most industrial processes in real-world manufacturing applications are characterized by the scalability property, which requires an automated strategy to self-adapt machine learning (ML) software systems to the new conditions. In this paper, we investigate an Electroslag Remelting (ESR) use case process from the Uddeholms AB steel company. The use case involves predicting the minimum pressure value for a vacuum pumping event. Taking into account the long time required to collect new records and efficiently integrate the new machines with the built ML software system. Additionally, to accommodate the changes and satisfy the non-functional requirement of the software system, namely adaptability, we propose an automated and adaptive approach based on a drift handling technique called importance weighting. The aim is to address the problem of adding a new furnace to production and enable the adaptability attribute of the ML software. The overall results demonstrate the improvements in ML software performance achieved by implementing the proposed approach over the classical non-adaptive approach.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559495"}, {"primary_key": "1727563", "vector": [], "sparse_vector": [], "title": "Call Me Maybe: Using NLP to Automatically Generate Unit Test Cases Respecting Temporal Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A class may need to obey temporal constraints in order to function correctly. For example, the correct usage protocol for an iterator is to always check whether there is a next element before asking for it; iterating over a collection when there are no items left leads to a NoSuchElementException. Automatic test case generation tools such as Randoop and EvoSuite do not have any notion of these temporal constraints. Generating test cases by randomly invoking methods on a new instance of the class under test may raise run time exceptions that do not necessarily expose software faults, but are rather a consequence of violations of temporal properties.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556961"}, {"primary_key": "1727564", "vector": [], "sparse_vector": [], "title": "Detecting Inconsistencies in If-Condition-Raise Statements.", "authors": ["Islem Bouzenia"], "summary": "Developers use exceptions guarded by conditions to abort the execution when a program reaches an unexpected state. However, sometimes the condition and the raised exception do not imply the same stopping reason, in which case, we call them inconsistent if-condition-raise statements. The inconsistency can originate from a mistake in the condition or the exception message. This paper presents IICR-Finder, a deep learning-based approach to detect inconsistent if-condition-raise statements. The approach reasons both about the condition's logic and the natural language of the exception message and raises a warning in case of inconsistency. We present six techniques to automatically generate large numbers of inconsistent statements to train two neural models based on binary classification and triplet loss. We apply the approach to 210K if-condition-raise statements extracted from 42 million lines of Python code. It achieves a precision of 72% at a recall of 60% on a dataset of past bug fixes. Running IICR-Finder on open-source projects reveals 30 previously unknown bugs, ten of which we reported, with eight confirmed by the developers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559514"}, {"primary_key": "1727565", "vector": [], "sparse_vector": [], "title": "SmOOD: Smoothness-based Out-of-Distribution Detection Approach for Surrogate Neural Networks in Aircraft Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>iro Guid<PERSON>"], "summary": "Aircraft industry is constantly striving for more efficient design optimization methods in terms of human efforts, computation time, and resources consumption. Hybrid surrogate optimization maintains high results quality while providing rapid design assessments when both the surrogate model and the switch mechanism for eventually transitioning to the HF model are calibrated properly. Feedforward neural networks (FNNs) can capture highly nonlinear input-output mappings, yielding efficient surrogates for aircraft performance factors. However, FNNs often fail to generalize over the out-of-distribution (OOD) samples, which hinders their adoption in critical aircraft design optimization. Through SmOOD, our smoothness-based out-of-distribution detection approach, we propose to codesign a model-dependent OOD indicator with the optimized FNN surrogate, to produce a trustworthy surrogate model with selective but credible predictions. Unlike conventional uncertainty-grounded methods, SmOOD exploits inherent smoothness properties of the HF simulations to effectively expose OODs through revealing their suspicious sensitivities, thereby avoiding over-confident uncertainty estimates on OOD samples. By using SmOOD, only high-risk OOD inputs are forwarded to the HF model for re-evaluation, leading to more accurate results at a low overhead cost. Three aircraft performance models are investigated. Results show that FNN-based surrogates outperform their Gaussian Process counterparts in terms of predictive performance. Moreover, SmOOD does cover averagely of actual OODs on all the study cases. When SmOOD plus FNN surrogates are deployed in hybrid surrogate optimization settings, they result in a decrease error rate of and a computational speed up rate of 58.36 ×, respectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556936"}, {"primary_key": "1727566", "vector": [], "sparse_vector": [], "title": "ICEBAR: Feedback-Driven Iterative Repair of Alloy Specifications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marcelo F. Frias"], "summary": "Automated program repair (APR) techniques have shown great success in automatically finding fixes for programs in programming languages such as C or Java. In this work, we focus on repairing formal specifications, in particular for the Alloy specification language. As opposed to most APR tools, our approach to repair Alloy specifications, named ICEBAR, does not use test-based oracles for patch assessment. Instead, ICEBAR relies on the use of property-based oracles, commonly found in Alloy specifications as predicates and assertions. These property-based oracles define stronger conditions for patch assessment, thus reducing the notorious overfitting issue caused by using test-based oracles, typically observed in APR contexts. Moreover, as assertions and predicates are inherent to Alloy, whereas test cases are not, our tool is potentially more appealing to Alloy users than test-based Alloy repair tools.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556944"}, {"primary_key": "1727567", "vector": [], "sparse_vector": [], "title": "Finding and Understanding Incompleteness Bugs in SMT Solvers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose Jan<PERSON>, an approach for finding incompleteness bugs in SMT solvers. The key insight is to mutate SMT formulas with local weakening and strengthening rules that preserve the satisfiability of the seed formula. The generated mutants are used to test SMT solvers for incompleteness bugs, i.e., inputs on which SMT solvers unexpectedly return unknown. We realized <PERSON><PERSON> on top of the SMT solver fuzzing framework YinYang. From June to August 2021, we stress-tested the two state-of-the-art SMT solvers Z3 and CVC5 with <PERSON><PERSON> and totally reported 31 incompleteness bugs. Out of these, 26 have been confirmed as unique bugs and 19 are already fixed by the developers. Our diverse bug findings uncovered functional, regression, and performance bugs—several triggered discussions among the developers sharing their in-depth analysis.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560435"}, {"primary_key": "1727568", "vector": [], "sparse_vector": [], "title": "Towards Robust Models of Code via Energy-Based Learning on Auxiliary Datasets.", "authors": ["Nghi D<PERSON>", "<PERSON><PERSON>"], "summary": "Existing approaches to improving the robustness of source code models concentrate on recognizing adversarial samples rather than valid samples that fall outside of a given distribution, which we refer to as out-of-distribution (OOD) samples. To this end, we propose to use an auxiliary dataset (out-of-distribution) such that, when trained together with the main dataset, they will enhance the model's robustness. We adapt energy-bounded learning objective function to assign a higher score to in-distribution samples and a lower score to out-of-distribution samples in order to incorporate such out-of-distribution samples into the training process of source code models. In terms of OOD detection and adversarial samples detection, our evaluation results demonstrate a greater robustness for existing source code models to become more accurate at recognizing OOD data while being more resistant to adversarial attacks at the same time.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561171"}, {"primary_key": "1727569", "vector": [], "sparse_vector": [], "title": "XSA: eXplainable Self-Adaptation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Self-adaptive systems increasingly rely on machine learning techniques as black-box models to make decisions even when the target world of interest includes uncertainty and unknowns. Because of the lack of transparency, adaptation decisions, as well as their effect on the world, are hard to explain. This often hinders the ability to trace unsuccessful adaptations back to understandable root causes. In this paper, we introduce our vision of explainable self-adaptation. We demonstrate our vision by instantiating our ideas on a running example in the robotics domain and by showing an automated proof-of-concept process providing human-understandable explanations for successful and unsuccessful adaptations in critical scenarios.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559552"}, {"primary_key": "1727570", "vector": [], "sparse_vector": [], "title": "Explaining the Behaviour of Game Agents Using Differential Comparison.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The difficulty in exploring the game balance has been increasing, especially in Game-as-a-Service (GaaS) with updates in every few weeks, and due to the complexity in game design and business models. In the limited time available for testing, using automated game agents enables much more test plays than using human test players does, and it has been accelerated by the recent progress of deep reinforcement learning. However, understanding specific behaviours of each agent is hard due to their \"black-box\" nature. In this paper, we propose a method for explaining the behaviour of game agents using differential comparison between agents. This comparison approach is motivated by our experience with existing explanation techniques that often extracted uninteresting, common aspects of the behaviour. In addition, there are large potentials for the application of the comparison: between agents with different learning algorithms, between human agents and automated agents, and between test agents and users. We applied our technique to a prototype of a commercial GaaS and confirmed our technique can extract specific differences between agents.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560503"}, {"primary_key": "1727571", "vector": [], "sparse_vector": [], "title": "Automatic Software Timing Attack Evaluation &amp; Mitigation on Clear Hardware Assumption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Embedded systems are widely used for implementing diverse Internet-of-Things (IoT) applications. These applications often deal with secret/sensitive data and encryption keys which can potentially be leaked through timing side-channel analysis. Runtime-based timing side-channel attacks are performed by measuring the time a code takes to execute and using that information to extract sensitive data. Effectively detecting such vulnerabilities with high precision and low false positives is a challenging task due to the runtime dependence of software code on the underlying hardware. Effectively fixing such vulnerabilities with low overhead is also non-trivial due to the diverse nature of embedded systems. In this article, we propose an automatic runtime side channel vulnerability detection and mitigation framework that not only considers the software code but also use the underlying hardware architecture information to tune the framework for more accurate vulnerability detection and system-specific tailored mitigation.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559516"}, {"primary_key": "1727572", "vector": [], "sparse_vector": [], "title": "Call Graph Evolution Analytics over a Version Series of an Evolving Software System.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Call Graph evolution analytics can aid a software engineer when maintaining or evolving a software system. This paper proposes Call Graph Evolution Analytics to extract information from an evolving call graph ECG = CG_1, CG_2,... CG_N for their version series VS = V_1, V_2, ... V_N of an evolving software system. This is done using Call Graph Evolution Rules (CGERs) and Call Graph Evolution Subgraphs (CGESs). Similar to association rule mining, the CGERs are used to capture co-occurrences of dependencies in the system. Like subgraph patterns in a call graph, the CGESs are used to capture evolution of dependency patterns in evolving call graphs. Call graph analytics on the evolution in these patterns can identify potentially affected dependencies (or procedure calls) that need attention. The experiments are done on the evolving call graphs of 10 large evolving systems to support dependency evolution management. We also consider results from a detailed study for evolving call graphs of Maven-Core's version series.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559573"}, {"primary_key": "1727573", "vector": [], "sparse_vector": [], "title": "How Useful is Code Change Information for Fault Localization in Continuous Integration?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>) Chen", "<PERSON><PERSON><PERSON>"], "summary": "Continuous integration (CI) is the process in which code changes are automatically integrated, built, and tested in a shared repository. In CI, developers frequently merge and test code under development, which helps isolate faults with finer-grained change information. To identify faulty code, prior research has widely studied and evaluated the performance of spectrum-based fault localization (SBFL) techniques. While the continuous nature of CI requires the code changes to be atomic and presents fine-grained information on what part of the system is being changed, traditional SBFL techniques do not benefit from it. To overcome the limitation, we propose to integrate the code and coverage change information in fault localization under CI settings. First, code changes show how faults are introduced into the system, and provide developers with better understanding on the root cause. Second, coverage changes show how the code coverage is impacted when faults are introduced. This change information can help limit the search space of code coverage, which offers more opportunities for improving fault localization techniques. Based on the above observations, we propose three new change-based fault localization techniques, and compare them with Ochiai, a commonly used SBFL technique. We evaluate these techniques on 192 real faults from seven software systems. Our results show that all three change-based techniques outperform Ochiai on the Defects4J dataset. In particular, the improvement varies from 7% to 23% and 17% to 24% for average MAP and MRR, respectively. Moreover, we find that our change-based fault localization techniques can be integrated with Ochiai, and boost its performance by up to 53% and 52% for average MAP and MRR, respectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556931"}, {"primary_key": "1727574", "vector": [], "sparse_vector": [], "title": "DeepPerform: An Efficient Approach for Performance Testing of Resource-Constrained Neural Networks.", "authors": ["<PERSON><PERSON>", "Mirazul <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Today, an increasing number of Adaptive Deep Neural Networks (AdNNs) are being used on resource-constrained embedded devices. We observe that, similar to traditional software, redundant computation exists in AdNNs, resulting in considerable performance degradation. The performance degradation is dependent on the input and is referred to as input-dependent performance bottlenecks (IDPBs). To ensure an AdNN satisfies the performance requirements of resource-constrained applications, it is essential to conduct performance testing to detect IDPBs in the AdNN. Existing neural network testing methods are primarily concerned with correctness testing, which does not involve performance testing. To fill this gap, we propose DeepPerform, a scalable approach to generate test samples to detect the IDPBs in AdNNs. We first demonstrate how the problem of generating performance test samples detecting IDPBs can be formulated as an optimization problem. Following that, we demonstrate how DeepPerform efficiently handles the optimization problem by learning and estimating the distribution of AdNNs' computational consumption. We evaluate DeepPerform on three widely used datasets against five popular AdNN models. The results show that DeepPerform generates test samples that cause more severe performance degradation (FLOPs: increase up to 552%). Furthermore, DeepPerform is substantially more efficient than the baseline methods in generating test inputs (runtime overhead: only 6–10 milliseconds).", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561158"}, {"primary_key": "1727575", "vector": [], "sparse_vector": [], "title": "Consistent Scene Graph Generation by Constraint Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Scene graph generation takes an image and derives a graph representation of key objects in the image and their relations. This core computer vision task is often used in autonomous driving, where traditional software and machine learning (ML) components are used in tandem. However, in such a safety-critical context, valid scene graphs can be further restricted by consistency constraints captured by domain or safety experts. Existing ML approaches for scene graph generation focus exclusively on relation-level accuracy but provide little to no guarantee that consistency constraints are satisfied in the generated scene graphs. In this paper, we aim to complement existing ML-based approaches by a post-processing step using constraint optimization over probabilistic scene graphs that can (1) guarantee that no consistency constraints are violated and (2) improve the overall accuracy of scene graph generation by fixing constraint violations. We evaluate the effectiveness of our approach using well-known, and novel metrics in the context of two popular ML datasets augmented with consistency constraints and two ML-based scene graph generation approaches as baselines.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560433"}, {"primary_key": "1727576", "vector": [], "sparse_vector": [], "title": "Jasmine: A Static Analysis Framework for Spring Core Technologies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Spring framework is widely used in developing enterprise web applications. Spring core technologies, such as Dependency Injection and Aspect-Oriented Programming, make development faster and easier. However, the implementation of Spring core technologies uses a lot of dynamic features. Those features impose significant challenges when using static analysis to reason about the behavior of Spring-based applications. In this paper, we propose <PERSON>, a static analysis framework for Spring core technologies extends from <PERSON><PERSON> to enhance the call graph's completeness while not greatly affecting its performance. We evaluate <PERSON>'s completeness, precision, and performance using Spring micro-benchmarks and a suite of 18 real-world Spring programs. Our experiments show that <PERSON> effectively enhances the state-of-the-art tools based on <PERSON><PERSON> and <PERSON><PERSON> to better support Spring core technologies. We also add <PERSON> support to FlowDroid and discovered twelve sensitive information leakage paths in our benchmarks. <PERSON> is expected to provide significant benefits for many program analyses scenes of Spring applications where more complete call graphs are required.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556910"}, {"primary_key": "1727577", "vector": [], "sparse_vector": [], "title": "AUSERA: Automated Security Vulnerability Detection for Android Apps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jiaming Li", "<PERSON>"], "summary": "To reduce the attack surface from app source code, massive tools focus on detecting security vulnerabilities in Android apps. However, some obvious weaknesses have been highlighted in the previous studies. For example, (1) most of the available tools such as AndroBugs, MobSF, Qark, and Super use pattern-based methods to detect security vulnerabilities. Although they are effective in detecting some types of vulnerabilities, a large number of false positives would be introduced, which inevitably increases the patching overhead for app developers. (2) Similarly, static taint analysis tools such as FlowDroid and IccTA present hundreds of vulnerability candidates of data leakage instead of confirmed vulnerabilities. (3) Last but not least, a relatively complete vulnerability taxonomy is missing, which would introduce a lot of false negatives. In this paper, based on our prior knowledge in this research domain, we empirically propose a vulnerability taxonomy as the baseline and then extend AUSERA by augmenting the detection capability to 50 security vulnerability types. Meanwhile, a new benchmark dataset including all these 50 vulnerability types is constructed to demonstrate the effectiveness of AUSERA. The tool and datasets are available at https://github.com/tjusenchen/AUSERA and the demonstration video can be found at https://youtu.be/UCiGwVaFPpY.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559524"}, {"primary_key": "1727578", "vector": [], "sparse_vector": [], "title": "On the Naturalness of Bytecode Instructions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bytecode is used in software analysis and other approaches due to its advantages such as high availability and simple specification. Therefore, to leverage these advantages in training language models with bytecode, it is important to clearly recognize the characteristics of the naturalness of bytecode. However, the naturalness of bytecode has not been actively explored.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559559"}, {"primary_key": "1727579", "vector": [], "sparse_vector": [], "title": "LISSA: Lazy Initialization with Specialized Solver Aid.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marcelo F. Frias"], "summary": "Programs that deal with heap-allocated inputs are difficult to analyze with symbolic execution (SE). Lazy Initialization (LI) is an approach to SE that deals with heap-allocated inputs by starting SE over a fully symbolic heap, and initializing the inputs' fields on demand, as the program under analysis accesses them. However, when the program's assumed precondition has structural constraints over the inputs, operationally captured via repOK routines, LI may produce spurious symbolic structures, making SE traverse infeasible paths and undermining SE's performance. repOK can only decide the feasibility of fully concrete structures, and thus previous work relied on manually crafted specifications designed to decide the (in)validity of partially symbolic inputs, to avoid producing spurious symbolic structures. However, these additional specifications require significant further effort from the developers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556965"}, {"primary_key": "1727580", "vector": [], "sparse_vector": [], "title": "SymFusion: Hybrid Instrumentation for Concolic Execution.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Concolic execution is a dynamic twist of symbolic execution designed with scalability in mind. Recent concolic executors heavily rely on program instrumentation to achieve such scalability. The instrumentation code can be added at compilation time (e.g., using an LLVM pass), or directly at execution time with the help of a dynamic binary translator. The former approach results in more efficient code but requires recompilation. Unfortunately, recompiling the entire code of a program is not always feasible or practical (e.g., in presence of third-party components). On the contrary, the latter approach does not require recompilation but incurs significantly higher execution time overhead.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556928"}, {"primary_key": "1727581", "vector": [], "sparse_vector": [], "title": "Differentially Testing Database Transactions for Fun and Profit.", "authors": ["<PERSON><PERSON><PERSON>", "Wensheng Dou", "Qianwang Dai", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Database Management Systems (DBMSs) utilize transactions to ensure the consistency and integrity of data. Incorrect transaction implementations in DBMSs can lead to severe consequences, e.g., incorrect database states and query results. Therefore, it is critical to ensure the reliability of transaction implementations.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556924"}, {"primary_key": "1727582", "vector": [], "sparse_vector": [], "title": "Towards Using Data-Influence Methods to Detect Noisy Samples in Source Code Corpora.", "authors": ["Anh T. V. <PERSON>", "Nghi D<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the recent trend of developing and applying neural source code models to software engineering tasks, the quality of such models is insufficient for real-world use. This is because there could be noise in the source code corpora used to train such models. We adapt data-influence methods to detect such noises in this paper. Data-influence methods are used in machine learning to evaluate the similarity of a target sample to the correct samples in order to determine whether or not the target sample is noisy. Our evaluation results show that data-influence methods can identify noisy samples from neural code models in classification-based tasks. This approach will contribute to the larger vision of developing better neural source code models from a data-centric perspective, which is a key driver for developing useful source code models in practice.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561168"}, {"primary_key": "1727583", "vector": [], "sparse_vector": [], "title": "Extraction and Management of Rationale.", "authors": ["<PERSON><PERSON>"], "summary": "Software developers often have to make many design decisions. The underlying logic behind these decisions, also called design rationale, represents beneficial and valuable information. In the past, researchers have tried to automatically extract and exploit this information, however, prior techniques are only applicable to specific contexts and there is insufficient progress on an automated end-to-end rationale extraction and management system. In this research project, we propose to use Natural Language Processing (NLP) and Machine Learning (ML) techniques to create a system for the automated extraction, structuring and management of design rationale. This system would support and ensure the consistency and the coherence of the development process.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559568"}, {"primary_key": "1727584", "vector": [], "sparse_vector": [], "title": "End-to-End Rationale Reconstruction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The logic behind design decisions, called design rationale, is very valuable. In the past, researchers have tried to automatically extract and exploit this information, but prior techniques are only applicable to specific contexts and there is insufficient progress on an end-to-end rationale information extraction pipeline. Here we outline a path towards such a pipeline that leverages several Machine Learning (ML) and Natural Language Processing (NLP) techniques. Our proposed context-independent approach, called Kantara, produces a knowledge graph representation of decisions and of their rationales, which considers their historical evolution and traceability. We also propose validation mechanisms to ensure the correctness of the extracted information and the coherence of the development process. We conducted a preliminary evaluation of our proposed approach on a small example sourced from the Linux Kernel, which shows promising results.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559547"}, {"primary_key": "1727585", "vector": [], "sparse_vector": [], "title": "HyperAST: Enabling Efficient Analysis of Software Histories at Scale.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Syntax Trees (ASTs) are widely used beyond compilers in many tools that measure and improve code quality, such as code analysis, bug detection, mining code metrics, refactoring. With the advent of fast software evolution and multistage releases, the temporal analysis of an AST history is becoming useful to understand and maintain code.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560423"}, {"primary_key": "1727586", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Automation in Software Security Patch Management.", "authors": ["Nesara Dissanayake", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Several studies have shown that automated support for different activities of the security patch management process has great potential for reducing delays in installing security patches. However, it is also important to understand how automation is used in practice, its limitations in meeting real-world needs and what practitioners really need, an area that has not been empirically investigated in the existing software engineering literature. This paper reports an empirical study aimed at investigating different aspects of automation for security patch management using semi-structured interviews with 17 practitioners from three different organisations in the healthcare domain. The findings are focused on the role of automation in security patch management for providing insights into the as-is state of automation in practice, the limitations of current automation, how automation support can be enhanced to effectively meet practitioners' needs, and the role of the human in an automated process. Based on the findings, we have derived a set of recommendations for directing future efforts aimed at developing automated support for security patch management.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556969"}, {"primary_key": "1727587", "vector": [], "sparse_vector": [], "title": "Privacy Analysis of Period Tracking Mobile Apps in the Post-Roe v. Wade Era.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To help people manage their health, period tracking apps have become very popular in recent years. However, the U.S. Supreme Court overturned <PERSON> v<PERSON> on June 24, 2022. Abortion will be banned in more and more states. Since the health data stored in the period tracking apps can be used to infer whether the user has had or is considering an abortion, mobile users are worrying that these apps may disclose their sensitive information, which can be used to prosecute users. Although period tracking apps have received attention from the research community, no existing work has performed a systematic privacy analysis of these apps, especially in the Post-Roe v. Wade era. To fill the void, this paper presents a comprehensive privacy analysis of popular period tracking apps. We first collect 35 popular period tracking apps from Google Play. Then, we analyze the sensitive user data collected by the period tracking apps using traffic analysis and static analysis. Further we inspect their privacy policies and check the consistency of the privacy policy with the app's behavior. In addition, we analyze the app reviews to understand the users' concerns about the period tracking apps. Our study reveals that some period tracking apps have indeed collected sensitive information and have the potential to share the data with third-party authorities. It is urgent for these apps to take action to protect user privacy, and mobile users should pay special attention to this kind of apps they used.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561343"}, {"primary_key": "1727588", "vector": [], "sparse_vector": [], "title": "RoboSimVer: A Tool for RoboSim Modeling and Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present RoboSimVer, a tool for modeling and analyzing RoboSim models. It uses a graphical notation called RoboSim to describe platform-independent simulation models of robotic systems. For model analysis, we have implemented a model-transformation approach to translate RoboSim models into NTA (Network of Timed Automata) and their stochastic version based on patterns and mapping rules. RoboSimVer takes a RoboSim simulation model as input and provides different rigorous verification techniques to check whether the simulation models satisfy property constraints. For experimental demonstrations, we adopt the alpha algorithm for swarm robotics as a case study. We use an abstract robotic-platform model to describe a swarm in an uncertain environment and illustrate how our tool supports the verification of stochastic and hybrid systems. The demonstration video is at youtu.be/mNe4q64GkmQ.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559533"}, {"primary_key": "1727589", "vector": [], "sparse_vector": [], "title": "Unsupervised Summarization of Privacy Concerns in Mobile Application Reviews.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of mobile applications (app) over the past decade has imposed unprecedented challenges on end-users privacy. Apps constantly demand access to sensitive user information in exchange for more personalized services. These—mostly unjustifiable—data collection tactics have raised major privacy concerns among mobile app users. Such concerns are commonly expressed in mobile app reviews, however, they are typically overshadowed by more generic categories of user feedback, such as app reliability and usability. This makes extracting user privacy concerns manually, or even using automated tools, a challenging and time-consuming task. To address these challenges, in this paper, we propose an effective unsupervised approach for summarizing user privacy concerns in mobile app reviews. Our analysis is conducted using a dataset of 2.6 million app reviews sampled from three different application domains. The results show that users in different application domains express their privacy concerns using domain-specific vocabulary. This domain knowledge can be leveraged to help unsupervised automated text summarization algorithms to generate concise and comprehensive summaries of privacy concerns in app review collections. Our analysis is intended to help app developers quickly and accurately identify the most critical privacy concerns in their domain of operation, and ultimately, alter their data collection practices to address these concerns.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561155"}, {"primary_key": "1727590", "vector": [], "sparse_vector": [], "title": "CrystalBLEU: Precisely and Efficiently Measuring the Similarity of Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have brought a surge of work on predicting pieces of source code, e.g., for code completion, code migration, program repair, or translating natural language into code. All this work faces the challenge of evaluating the quality of a prediction w.r.t. some oracle, typically in the form of a reference solution. A common evaluation metric is the BLEU score, an n-gram-based metric originally proposed for evaluating natural language translation, but adopted in software engineering because it can be easily computed on any programming language and enables automated evaluation at scale. However, a key difference between natural and programming languages is that in the latter, completely unrelated pieces of code may have many common n-grams simply because of the syntactic verbosity and coding conventions of programming languages. We observe that these trivially shared n-grams hamper the ability of the metric to distinguish between truly similar code examples and code examples that are merely written in the same language. This paper presents CrystalBLEU, an evaluation metric based on BLEU, that allows for precisely and efficiently measuring the similarity of code. Our metric preserves the desirable properties of BLEU, such as being language-agnostic, able to handle incomplete or partially incorrect code, and efficient, while reducing the noise caused by trivially shared n-grams. We evaluate CrystalBLEU on two datasets from prior work and on a new, labeled dataset of semantically equivalent programs. Our results show that CrystalBLEU can distinguish similar from dissimilar code examples 1.9–4.5 times more effectively, when compared to the original BLEU score and a previously proposed variant of BLEU for code.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556903"}, {"primary_key": "1727591", "vector": [], "sparse_vector": [], "title": "Quacky: Quantitative Access Control Permissiveness Analyzer✱.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Emily <PERSON>&a<PERSON>;<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "quacky is a tool for quantifying permissiveness of access control policies in the cloud. Given a policy, quacky translates it into a SMT formula and uses a model counting constraint solver to quantify permissiveness. When given multiple policies, quacky not only determines which policy is more permissive, but also quantifies the relative permissiveness between the policies. With quacky, policy authors can automatically analyze complex policies, helping them ensure that there is no unintended access to private data. quacky supports access control policies written in the Amazon Web Services (AWS) Identity and Access Management (IAM), Microsoft Azure, and Google Cloud Platform (GCP) policy languages. It has command-line and web interfaces. It is open-source and available at https://github.com/vlab-cs-ucsb/quacky.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559530"}, {"primary_key": "1727592", "vector": [], "sparse_vector": [], "title": "MV-HAN: A Hybrid Attentive Networks based Multi-View Learning Model for Large-scale Contents Recommendation.", "authors": ["Ge Fan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Industrial recommender systems usually employ multi-source data to improve the recommendation quality, while effectively sharing information between different data sources remain a challenge. In this paper, we introduce a novel Multi-View Approach with Hybrid Attentive Networks (MV-HAN) for contents retrieval at the matching stage of recommender systems. The proposed model enables high-order feature interaction from various input features while effectively transferring knowledge between different types. By employing a well-placed parameters sharing strategy, the MV-HAN substantially improves the retrieval performance in sparse types. The designed MV-HAN inherits the efficiency advantages in the online service from the two-tower model, by mapping users and contents of different types into the same features space. This enables fast retrieval of similar contents with an approximate nearest neighbor algorithm. We conduct offline experiments on several industrial datasets, demonstrating that the proposed MV-HAN significantly outperforms baselines on the content retrieval tasks. Importantly, the MV-HAN is deployed in a real-world matching system. Online A/B test results show that the proposed method can significantly improve the quality of recommendations.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559496"}, {"primary_key": "1727593", "vector": [], "sparse_vector": [], "title": "Neuroevolution-Based Generation of Tests and Oracles for Games.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Game-like programs have become increasingly popular in many software engineering domains such as mobile apps, web applications, or programming education. However, creating tests for programs that have the purpose of challenging human players is a daunting task for automatic test generators. Even if test generation succeeds in finding a relevant sequence of events to exercise a program, the randomized nature of games means that it may neither be possible to reproduce the exact program behavior underlying this sequence, nor to create test assertions checking if observed randomized game behavior is correct. To overcome these problems, we propose Neatest, a novel test generator based on the NeuroEvolution of Augmenting Topologies (NEAT) algorithm. <PERSON><PERSON>est systematically explores a program's statements, and creates neural networks that operate the program in order to reliably reach each statement -- that is, Neatest learns to play the game in a way to reliably cover different parts of the code. As the networks learn the actual game behavior, they can also serve as test oracles by evaluating how surprising the observed behavior of a program under test is compared to a supposedly correct version of the program. We evaluate this approach in the context of Scratch, an educational programming environment. Our empirical study on 25 non-trivial Scratch games demonstrates that our approach can successfully train neural networks that are not only far more resilient to random influences than traditional test suites consisting of static input sequences, but are also highly effective with an average mutation score of more than 65%.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556939"}, {"primary_key": "1727594", "vector": [], "sparse_vector": [], "title": "Towards Agent-Based Testing of 3D Games using Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Computer game is a billion-dollar industry and is booming. Testing games has been recognized as a difficult task, which mainly relies on manual playing and scripting based testing. With the advances in technologies, computer games have become increasingly more interactive and complex, thus play-testing using human participants alone has become unfeasible. In recent days, play-testing of games via autonomous agents has shown great promise by accelerating and simplifying this process. Reinforcement Learning solutions have the potential of complementing current scripted and automated solutions by learning directly from playing the game without the need of human intervention. This paper presented an approach based on reinforcement learning for automated testing of 3D games. We make use of the notion of curiosity as a motivating factor to encourage an RL agent to explore its environment. The results from our exploratory study are promising and we have preliminary evidence that reinforcement learning can be adopted for automated testing of 3D games.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560507"}, {"primary_key": "1727595", "vector": [], "sparse_vector": [], "title": "Towards a Live Environment for Code Refactoring.", "authors": ["<PERSON>"], "summary": "Refactoring code manually can be complex. Several refactoring tools were developed to mitigate the effort needed to create more readable, adaptable, and maintainable code. However, most of them continue to provide late feedback, assistance, and support on how developers should improve their software. That's where the concept of Live Refactoring comes in. We believe the immediate and continuous suggestion of refactoring candidates to the code will help reduce this problem. Therefore, we prototyped a Live Refactoring Environment that identifies, recommends, and applies Extract Method refactorings. We carried out an empirical experiment that showed us that our approach helped developers reach better code, with more quality, improving their refactoring experience.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559569"}, {"primary_key": "1727596", "vector": [], "sparse_vector": [], "title": "LiveRef: a Tool for Live Refactoring Java Code.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "André <PERSON>"], "summary": "Refactoring software can be hard and time-consuming. Several refactoring tools assist developers in reaching more readable and maintainable code. However, most of them are characterized by long feedback loops that impoverish their refactoring experience. We believe that we can reduce this problem by focusing on the concept of Live Refactoring and its main principles: the live recommendation and continuous visualization of refactoring candidates, and the immediate visualization of results from applying a refactoring to the code. Therefore, we implemented a Live Refactoring Environment that identifies, suggests, and applies Extract Method refactorings. To evaluate our approach, we carried out an empirical experiment. Early results showed us that our refactoring environment improves several code quality aspects, being well received, understood, and used by the experiment participants. The source code of our tool is available on: https://github.com/saracouto1318/LiveRef. Its demonstration video can be found at: https://youtu.be/_jxx21ZiQ0o.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559532"}, {"primary_key": "1727597", "vector": [], "sparse_vector": [], "title": "Scalable Sampling of Highly-Configurable Systems: Generating Random Instances of the Linux Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software systems are becoming increasingly configurable. A paradigmatic example is the Linux kernel, which can be adjusted for a tremendous variety of hardware devices, from mobile phones to supercomputers, thanks to the thousands of configurable features it supports. In principle, many relevant problems on configurable systems, such as completing a partial configuration to get the system instance that consumes the least energy or optimizes any other quality attribute, could be solved through exhaustive analysis of all configurations. However, configuration spaces are typically colossal and cannot be entirely computed in practice. Alternatively, configuration samples can be analyzed to approximate the answers. Generating those samples is not trivial since features usually have inter-dependencies that constrain the configuration space. Therefore, getting a single valid configuration by chance is extremely unlikely. As a result, advanced samplers are being proposed to generate random samples at a reasonable computational cost. However, to date, no sampler can deal with highly configurable complex systems, such as the Linux kernel. This paper proposes a new sampler that does scale for those systems, based on an original theoretical approach called extensible logic groups. The sampler is compared against five other approaches. Results show our tool to be the fastest and most scalable one.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556899"}, {"primary_key": "1727598", "vector": [], "sparse_vector": [], "title": "Griffin : Grammar-Free DBMS Fuzzing.", "authors": ["Jingzhou Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu <PERSON>"], "summary": "Fuzzing is a promising approach to DBMS testing. One crucial component in DBMS fuzzing is grammar: since DBMSs enforce strict validation on inputs, a grammar improves fuzzing efficiency by generating syntactically- and semantically-correct SQL statements. However, due to the vast differences in the complex grammar of various DBMSs, it is painstaking to adapt these fuzzers to them. Considering that lots of DBMSs are not yet well tested, there is an urgent need for an effective DBMS fuzzing approach that is free from grammar dependencies.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560431"}, {"primary_key": "1727599", "vector": [], "sparse_vector": [], "title": "Using Consensual Biterms from Text Structures of Requirements and Code to Improve IR-Based Traceability Recovery.", "authors": ["<PERSON>", "Hong<PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON> Ma", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Traceability approves trace links among software artifacts based on whether two artifacts are related by system functionalities. The traces are valuable for software development, but are difficult to obtain manually. To cope with the costly and fallible manual recovery, automated approaches are proposed to recover traces through textual similarities among software artifacts, such as those based on Information Retrieval (IR). However, the low quality & quantity of artifact texts negatively impact the calculated IR values, thus greatly hindering the performance of IR-based approaches. In this study, we propose to extract co-occurred word pairs from the text structures of both requirements and code (i.e., consensual biterms) to improve IR-based traceability recovery. We first collect a set of biterms based on the part-of-speech of requirement texts, and then filter them through the code texts. We then use these consensual biterms to both enrich the input corpus for IR techniques and enhance the calculations of IR values. A nine-system-based evaluation shows that in general, when solely used to enhance IR techniques, our approach can outperform pure IR-based approaches and another baseline by 21.9% & 21.8% in AP, and 9.3% & 7.2% in MAP, respectively. Moreover, when used to collaborate with another enhancing strategy from different perspectives, it can outperform this baseline by 5.9% in AP and 4.8% in MAP.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556948"}, {"primary_key": "1727600", "vector": [], "sparse_vector": [], "title": "A Framework for Testing Chemical Reaction Networks.", "authors": ["<PERSON>"], "summary": "The use of non-traditional computing devices is growing rapidly. One paradigm of interest is chemical reaction networks (CRNs) which can model and use chemical interactions for computation. These CRNs are used to develop programs at the nanoscale for applications such as intelligent drug delivery. In practice, these programs are developed in simulation environments, and then compiled into physical systems. A challenge when designing CRNs for computation is the lack of techniques to verify and validate correctness. In this work, we adapt software testing and repair techniques for use in this domain. In initial work, we designed a testing framework to handle the challenges presented by CRN programs; this includes distributed computation and stochastic behavior. We extended this framework to implement automated program repair of CRN models and automated test generation via program invariants. For future work, we will develop a notion of fault localization for these programs, develop a theory of mutation generation, and address issues regarding flakiness present in this computing paradigm.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559562"}, {"primary_key": "1727601", "vector": [], "sparse_vector": [], "title": "Towards Effective Static Analysis Approaches for Security Vulnerabilities in Smart Contracts.", "authors": ["<PERSON><PERSON>"], "summary": "The growth in the popularity of smart contracts has been accompanied by a rise in security attacks targeting smart contracts, which have led to financial losses of millions of dollars and erosion of trust. To enable developers discover vulnerabilities in smart contracts, several static analysis tools have been proposed. However, despite the numerous bug-finding tools, security vulnerabilities abound in smart contracts, and developers rely on finding vulnerabilities manually. Our goal in this dissertation study is to expand the space of security vulnerabilities detection by proposing effective static analysis approaches for smart contracts. We study the effectiveness of the existing static analysis tools and propose solutions for security vulnerabilities detection relying on analyzing the dependency of the contract code on user inputs that lead to security vulnerabilities. Our results of evaluating static analysis tools show that existing static tools for smart contracts have significant false-negatives and false-positives. Further, the results show that our first vulnerability detection approach achieves a significant improvement in the effectiveness of detecting vulnerabilities compared to the prior work.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559567"}, {"primary_key": "1727602", "vector": [], "sparse_vector": [], "title": "Shibboleth: Hybrid Patch Correctness Assessment in Automated Program Repair.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Test-based generate-and-validate automated program repair (APR) systems generate many patches that pass the test suite without fixing the bug. The generated patches must be manually inspected by the developers, a task that tends to be time-consuming, thereby diminishing the role of APR in reducing debugging costs.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559519"}, {"primary_key": "1727603", "vector": [], "sparse_vector": [], "title": "ecoCode: a SonarQube Plugin to Remove Energy Smells from Android Projects.", "authors": ["<PERSON>", "<PERSON>"], "summary": "To face the climate change, Android developers urge to become green software developers. But how to ensure carbon-efficient mobile apps at large? In this paper, we introduce ecoCode, a SonarQube plugin able to highlight code structures that are smelly from an energy perspective. It is based on a curated list of energy code smells likely to impact negatively the battery lifespan of Android-powered devices. The ecoCode plugin enables analysis of any native Android project written in Java in order to enforce green code. — Demo video on https://youtu.be/4XIYGyPEhXQ", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559518"}, {"primary_key": "1727604", "vector": [], "sparse_vector": [], "title": "Sorry, I don&apos;t Understand: Improving Voice User Interface Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Voice-based virtual assistants are becoming increasingly popular. Such systems provide frameworks to developers on which they can build their own apps. End-users can interact with such apps through a Voice User Interface (VUI), which allows to use natural language commands to perform actions. Testing such apps is far from trivial: The same command can be expressed in different ways. To support developers in testing VUIs, Deep Learning (DL)-based tools have been integrated in the development environments (e.g., the Alexa Developer Console, or ADC) to generate paraphrases for the commands (seed utterances) specified by the developers. Such tools, however, generate few paraphrases that do not always cover corner cases. In this paper, we introduce VUI-UPSET, a novel approach that aims at adapting chatbot-testing approaches to VUI-testing. Both systems, indeed, provide a similar natural-language-based interface to users. We conducted an empirical study to understand how VUI-UPSET compares to existing approaches in terms of (i) correctness of the generated paraphrases, and (ii) capability of revealing bugs. Multiple authors analyzed 5,872 generated paraphrases, with a total of 13,310 manual evaluations required for such a process. Our results show that, while the DL-based tool integrated in the ADC generates a higher percentage of meaningful paraphrases compared to VUI-UPSET, VUI-UPSET generates more bug-revealing paraphrases. This allows developers to test more thoroughly their apps at the cost of discarding a higher number of irrelevant paraphrases.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556934"}, {"primary_key": "1727605", "vector": [], "sparse_vector": [], "title": "ElecDaug: Electromagnetic Data Augmentation for Model Repair based on Metamorphic Relation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weisong Sun", "<PERSON><PERSON><PERSON>", "Chunrong Fang", "<PERSON>"], "summary": "With the application of deep learning (DL) in signal detection, improving the robustness of classification models has received much attention, especially in automatic modulation classification (AMC) of electromagnetic signals. A large amount of electromagnetic signal data is required to obtain robust models in the training and testing process. However, the high cost of manual collection and the issue of low quality of automatically generated data contribute to the AMC model's defects. Therefore, it is essential to generate electromagnetic data by data augmentation. In this paper, we propose a novel electromagnetic data augmentation tool, namely ElecDaug, which directs the metamorphic process by electromagnetic signal characteristics to achieve automatic data augmentation. Based on electromagnetic data pre-processing, transmission or time-frequency domains characteristic metamorphic, ElecDaug can augment the data samples to build robust AMC models. Preliminary experiments show that ElecDaug can effectively augment available data samples for model repair. The video is at https://youtu.be/x5g6IVX_Q3s. Documentation and source code can be found here: https://github.com/ehhhhjw/tool_ElecDaug.git.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559536"}, {"primary_key": "1727606", "vector": [], "sparse_vector": [], "title": "Graph based Incident Extraction and Diagnosis in Large-Scale Online Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hongyang Chen", "Guangba Yu", "<PERSON><PERSON>"], "summary": "With the ever increasing scale and complexity of online systems, incidents are gradually becoming commonplace. Without appropriate handling, they can seriously harm the system availability. However, in large-scale online systems, these incidents are usually drowning in a slew of issues (i.e., something abnormal, while not necessarily an incident), rendering them difficult to handle. Typically, these issues will result in a cascading effect across the system, and a proper management of the incidents depends heavily on a thorough analysis of this effect. Therefore, in this paper, we propose a method to automatically analyze the cascading effect of availability issues in online systems and extract the corresponding graph based issue representations incorporating both of the issue symptoms and affected service attributes. With the extracted representations, we train and utilize a graph neural networks based model to perform incident detection. Then, for the detected incident, we leverage the PageRank algorithm with a flexible transition matrix design to locate its root cause. We evaluate our approach using real-world data collected from the WeChat online service system, the largest instant message system in China. The results confirm the effectiveness of our approach. Moreover, our approach is successfully deployed in the company and eases the burden of operators in the face of a flood of issues and related alert signals.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556904"}, {"primary_key": "1727607", "vector": [], "sparse_vector": [], "title": "Towards Generating Labeled Property Graphs for Comprehending C#-based Software Projects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "C# is the most widely used programming language among XR developers. However, only a limited number of graph-based data acquisition tools exist for C# software. XR development commonly relies on reusing existing software components to accelerate development. Graph-based visualization tools can facilitate this comprehension process, e.g., by providing an overview of relationships between components. This work describes a new tool called Src2Neo that generates labeled property graphs of C#-based software projects. The stored graph follows a simple C# naming scheme and — contrary to other solutions — maps each software entity to exactly one node. The resulting graph facilitates the comprehension process by providing an easy to read representation of software components. Additionally, the generated graphs can act as a data basis for more advanced software visualizations without the need for complex data requests.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560513"}, {"primary_key": "1727608", "vector": [], "sparse_vector": [], "title": "Towards Improving the Adoption and Usage of National Digital Identity Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "User perceptions of national digital identity systems (NDIDs) significantly impact their use and acceptance. Previous study on the use of NDIDs has provided limited frameworks for future research, with a strong emphasis on government services as well as how the system may be improved. This study evaluates how human-centric cybersecurity factors influence the use of NDIDs and acceptance among users. For instance, MyHealth record, which is used in Australia to record medical services provided to users, was overwhelmingly rejected by users due to concerns about digital identification information being used without authorisation and other privacy concerns. We hypothesise that human-centric cybersecurity factors influence the use of NDID and acceptance among users. The study also has a practical implication since it provides a framework to determine human-centric cybersecurity factors that influence adoption and improve NDIDs usage.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561144"}, {"primary_key": "1727609", "vector": [], "sparse_vector": [], "title": "TreeCen: Building Tree Graph for Scalable Semantic Code Clone Detection.", "authors": ["<PERSON><PERSON><PERSON>", "Deqing Zou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code clone detection is an important research problem that has attracted wide attention in software engineering. Many methods have been proposed for detecting code clone, among which text-based and token-based approaches are scalable but lack consideration of code semantics, thus resulting in the inability to detect semantic code clones. Methods based on intermediate representations of codes can solve the problem of semantic code clone detection. However, graph-based methods are not practicable due to code compilation, and existing tree-based approaches are limited by the scale of trees for scalable code clone detection.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556927"}, {"primary_key": "1727610", "vector": [], "sparse_vector": [], "title": "A Transferable Time Series Forecasting Service Using Deep Transformer Model for Online Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many real-world online systems expect to forecast the future trend of software quality to better automate operational processes, optimize software resource cost and ensure software reliability. To achieve that, all kinds of time series metrics collected from online software systems are adopted to characterize and monitor the quality of software services. To meet relevant software engineers' requirements, we focus on time series forecasting and aim to provide an event-driven and self-adaptive forecasting service. In this paper, we present TTSF-transformer, a transferable time series forecasting service using deep transformer model. TTSF-transformer normalizes multiple metric frequencies to ensure the model sharing across multi-source systems, employs a deep transformer model with Bayesian estimation to generate the predictive marginal distribution, and introduces transfer learning and incremental learning into the training process to ensure the performance of long-term prediction. We conduct experiments on real-world time series metrics from two different types of game business in Tencent®. The results show that TTSF-transformer significantly outperforms other state-of-the-art methods and is suitable for wide deployment in large online systems.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560414"}, {"primary_key": "1727611", "vector": [], "sparse_vector": [], "title": "Prompt-tuned Code Language Model as a Neural Knowledge Base for Type Inference in Statically-Typed Partial Code.", "authors": ["<PERSON>", "Z<PERSON>qiang Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qinghua Lu"], "summary": "Partial code usually involves non-fully-qualified type names (non-FQNs) and undeclared receiving objects. Resolving the FQNs of these non-FQN types and undeclared receiving objects (referred to as type inference) is the prerequisite to effective search and reuse of partial code. Existing dictionary-lookup based methods build a symbolic knowledge base of API names and code contexts, which involve significant compilation overhead and are sensitive to unseen API names and code context variations. In this paper, we formulate type inference as a cloze-style fill-in-blank language task. Built on source code naturalness, our approach fine-tunes a code masked language model (MLM) as a neural knowledge base of code elements with a novel \"pre-train, prompt and predict\" paradigm from raw source code. Our approach is lightweight and has minimum requirements on code compilation. Unlike existing symbolic name and context matching for type inference, our prompt-tuned code MLM packs FQN syntax and usage in its parameters and supports fuzzy neural type inference. We systematically evaluate our approach on a large amount of source code from GitHub and Stack Overflow. Our results confirm the effectiveness of our approach design and the practicality for partial code type inference. As the first of its kind, our neural type inference method opens the door to many innovative ways of using partial code.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556912"}, {"primary_key": "1727612", "vector": [], "sparse_vector": [], "title": "Data Augmentation for Improving Emotion Recognition in Software Engineering Communication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emotions (e.g., <PERSON>, <PERSON>) are prevalent in daily software engineering (SE) activities, and are known to be significant indicators of work productivity (e.g., bug fixing efficiency). Recent studies have shown that directly applying general purpose emotion classification tools to SE corpora is not effective. Even within the SE domain, tool performance degrades significantly when trained on one communication channel and evaluated on another (e.g, StackOverflow vs. GitHub comments). Retraining a tool with channel-specific data takes significant effort since manually annotating a large dataset of ground truth data is expensive.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556925"}, {"primary_key": "1727613", "vector": [], "sparse_vector": [], "title": "V-Achilles: An Interactive Visualization of Transitive Security Vulnerabilities.", "authors": ["Vipawan Jarukitpipat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Morakot Choetkiertikul", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A key threat to the usage of third-party dependencies has been the threat of security vulnerabilities, which risks unwanted access to a user application. As part of an ecosystem of dependencies, users of a library are prone to both the direct and transitive dependencies adopted into their applications. Recent work involves tool supports for vulnerable dependency updates, rarely showing the complexity of the transitive updates. In this paper, we introduce our solution to support vulnerability updating in npm. V-Achilles is a prototype that shows a visualization (i.e., using dependency graphs) affected by vulnerability attacks. In addition to the tool overview, we highlight three use cases to demonstrate the usefulness and application of our prototype with real-world npm packages. The prototype is available at https://github.com/MUICT-SERU/V-Achilles, with an accompanying video demonstration at https://www.youtube.com/watch?v=tspiZfhMNcs.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559526"}, {"primary_key": "1727614", "vector": [], "sparse_vector": [], "title": "Towards the Integration of Human Factors in Collaborative Decision Making for Secure Architecture Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designing a large and complex software system depends not only on the nature of the system itself, but also on human-centric characteristics of the team of architects, developers, and managers involved in the design activity. Each of these team members often comes with varying levels of knowledge, experience, attitudes, and behaviors (i.e., human factors) towards securing systems that impact the decision-making process of the individual team members and of the team as a whole. Thus, these human factors can influence architectural design decisions impacting many different system qualities including security. In this paper, we propose a framework for considering human factors in collaborative decision-making for secure architecture design. At the core of the proposed framework, are conceptual models for security human factors and architectural design decisions. We describe the steps and our preliminary results towards creating the proposed framework using a combination of model-driven engineering techniques and human science approaches. We also provide a simple design scenario to illustrate the envisioned design workflow of the proposed framework. With the proposed framework, we aim to improve our understanding of how decisions are made by a team of diverse members, and to provide better traceability of decisions impacting system security.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561149"}, {"primary_key": "1727615", "vector": [], "sparse_vector": [], "title": "Towards Gradual Multiparty Session Typing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "To make concurrent programming easier, languages (e.g., Go, Rust, Clojure) have started to offer core support for message passing through channels in shared memory. However, channels also have their issues. Multiparty session types (MPST) constitute a method to make channel usage simpler. In this paper, to consolidate the best qualities of \"static MPST\" (early feedback, fast execution) and \"dynamic MPST\" (high expressiveness), we present a project that reinterprets the MPST method through the lens of gradual typing.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561167"}, {"primary_key": "1727616", "vector": [], "sparse_vector": [], "title": "FuzzerAid: Grouping Fuzzed Crashes Based On Fault Signatures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Fuzzing has been an important approach for finding bugs and vulnerabilities in programs. Many fuzzers deployed in industry run daily and can generate an overwhelming number of crashes. Diagnosing such crashes can be very challenging and time-consuming. Existing fuzzers typically employ heuristics such as code coverage or call stack hashes to weed out duplicate reporting of bugs. While these heuristics are cheap, they are often imprecise and end up still reporting many \"unique\" crashes corresponding to the same bug. In this paper, we present FuzzerAid that uses fault signatures to group crashes reported by the fuzzers. Fault signature is a small executable program and consists of a selection of necessary statements from the original program that can reproduce a bug. In our approach, we first generate a fault signature using a given crash. We then execute the fault signature with other crash inducing inputs. If the failure is reproduced, we classify the crashes into the group labeled with the fault signature; if not, we generate a new fault signature. After all the crash inducing inputs are classified, we further merge the fault signatures of the same root cause into a group. We implemented our approach in a tool called FuzzerAid and evaluated it on 3020 crashes generated from 15 real-world bugs and 4 large open source projects. Our evaluation shows that we are able to correctly group 99.1% of the crashes and reported only 17 (+2) \"unique\" bugs, outperforming the state-of-the-art fuzzers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556959"}, {"primary_key": "1727617", "vector": [], "sparse_vector": [], "title": "A Role Based Model Template for Specifying Virtual Reality Software.", "authors": ["<PERSON>", "<PERSON><PERSON>k Pareek", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Research in hardware and software support for Virtual Reality (VR) has significantly increased over the last decade. Given the software platform fragmentation and hardware volatility, there is an apparent disconnect among practitioners while building applications in the VR domain. This paper proposes a role-based model template as a meta-model to specify the bare minimum VR software system. We conducted a grounded-theory-based qualitative study on prevailing and phased-out VR SDKs and standards to propose this meta-model. This model template can help VR practitioners build open-source tools to develop, design, and test VR software systems.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560514"}, {"primary_key": "1727618", "vector": [], "sparse_vector": [], "title": "Automatic Code Documentation Generation Using GPT-3.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Source code documentation is an important artifact for efficient software development. Code documentation could greatly benefit from automation since manual documentation is often labouring, resource and time-intensive. In this paper, we employed Codex for automatic code documentation creation. Codex is a GPT-3 based model pre-trained on both natural and programming languages. We find that Codex outperforms existing techniques even with basic settings like one-shot learning (i.e., providing only one example for training). Codex achieves an overall BLEU score of 20.6 for six different programming languages (11.2% improvement over earlier state-of-the-art techniques). Thus, Codex shows promise and warrants in-depth future studies for automatic code documentation generation to support diverse development tasks.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559548"}, {"primary_key": "1727619", "vector": [], "sparse_vector": [], "title": "Global Decision Making Over Deep Variability in Feedback-Driven Software Development.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>ue<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To succeed with the development of modern software, organizations must have the agility to adapt faster to constantly evolving environments to deliver more reliable and optimized solutions that can be adapted to the needs and environments of their stakeholders including users, customers, business, development, and IT. However, stakeholders do not have sufficient automated support for global decision making, considering the increasing variability of the solution space, the frequent lack of explicit representation of its associated variability and decision points, and the uncertainty of the impact of decisions on stakeholders and the solution space. This leads to an ad-hoc decision making process that is slow, error-prone, and often favors local knowledge over global, organization-wide objectives. The Multi-Plane Models and Data (MP-MODA) framework explicitly represents and manages variability, impacts, and decision points. It enables automation and tool support in aid of a multi-criteria decision making process involving different stakeholders within a feedback-driven software development process where feedback cycles aim to reduce uncertainty. We present the conceptual structure of the framework, discuss its potential benefits, and enumerate key challenges related to tool supported automation and analysis within MP-MODA.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559551"}, {"primary_key": "1727620", "vector": [], "sparse_vector": [], "title": "Multi-objective Optimization-based Bug-fixing Template Mining for Automated Program Repair.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Template-based automatic program repair (T-APR) techniques depend on the quality of bug-fixing templates. For such templates to be of sufficient quality for T-APR techniques to succeed, they must satisfy three criteria: applicability, fixability, and efficiency. Existing template mining approaches select templates based only on the first criteria, and are thus suboptimal in their performance. This study proposes a multi-objective optimization-based bug-fixing template mining method for T-APR in which we estimate template quality based on nine code abstraction tasks and three objective functions. Our method determines the optimal code abstraction strategy (i.e., the optimal combination of abstraction tasks) which maximizes the values of three objective functions and generates a final set of bug-fixing templates by clustering template candidates to which the optimal abstraction strategy is applied. Our preliminary experiment demonstrated that our optimized strategy can improve templates' applicability and efficiency by 7% and 146% over the existing mining technique, respectively. We therefore conclude that the multi-objective optimization-based template mining technique effectively finds high-quality bug-fixing templates.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559554"}, {"primary_key": "1727621", "vector": [], "sparse_vector": [], "title": "Auto Off-Target: Enabling Thorough and Scalable Testing for Complex Software Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software systems powering OS kernels, basebands, bootloaders, firmware, IoT or automotive build the foundation of infrastructure that billions of people rely on every day. Testing these systems is crucial, especially as their complexity grows and they are often written in unsafe languages such as C/C++.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556915"}, {"primary_key": "1727622", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> or not <PERSON><PERSON><PERSON>? The Impact of CNF Transformations on Feature-Model Analyses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Feature modeling is widely used to systematically model features of variant-rich software systems and their dependencies. By translating feature models into propositional formulas and analyzing them with solvers, a wide range of automated analyses across all phases of the software development process become possible. Most solvers only accept formulas in conjunctive normal form (CNF), so an additional transformation of feature models is often necessary. However, it is unclear whether this transformation has a noticeable impact on analyses. In this paper, we compare three transformations (i.e., distribut<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>) for bringing feature-model formulas into CNF. We analyze which transformation can be used to correctly perform feature-model analyses and evaluate three CNF transformation tools (i.e., FeatureIDE, KConfigReader, and Z3) on a corpus of 22 real-world feature models. Our empirical evaluation illustrates that some CNF transformations do not scale to complex feature models or even lead to wrong results for model-counting analyses. Further, the choice of the CNF transformation can substantially influence the performance of subsequent analyses.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556938"}, {"primary_key": "1727623", "vector": [], "sparse_vector": [], "title": "Not All Dependencies are Equal: An Empirical Study on Production Dependencies in NPM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern software systems are often built by leveraging code written by others in the form of libraries and packages to accelerate their development. While there are many benefits to using third-party packages, software projects often become dependent on a large number of software packages. Consequently, developers are faced with the difficult challenge of maintaining their project dependencies by keeping them up-to-date and free of security vulnerabilities. However, how often are project dependencies used in production where they could pose a threat to their project's security?", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556896"}, {"primary_key": "1727624", "vector": [], "sparse_vector": [], "title": "Fuzzle: Making a Puzzle for Fuzzers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With rapidly growing fuzzing technology, there has been surging demand for automatically synthesizing buggy programs. Previous approaches have been focused on injecting bugs into existing programs, making them suffer from providing the ground truth as the generated programs may contain unexpected bugs. In this paper, we address this challenge by casting the bug synthesis problem as a maze generation problem. Specifically, we synthesize a whole buggy program by encoding a sequence of moves in a maze as a chain of function calls. By design, our approach provides the exact ground truth of the synthesized benchmark. Furthermore, it allows generation of benchmarks with realistic path constraints extracted from existing vulnerabilities. We implement our idea in a tool, named Fuzzle, and evaluate it with five state-of-the-art fuzzers to empirically prove its value.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556908"}, {"primary_key": "1727625", "vector": [], "sparse_vector": [], "title": "VITAS : Guided Model-based VUI Testing of VPA Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guangdong Bai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Virtual personal assistant (VPA) services, e.g. Amazon Alexa and Google Assistant, are becoming increasingly popular recently. Users interact with them through voice-based apps, e.g. Amazon Alexa skills and Google Assistant actions. Unlike the desktop and mobile apps which have visible and intuitive graphical user interface (GUI) to facilitate interaction, VPA apps convey information purely verbally through the voice user interface (VUI), which is known to be limited in its invisibility, single mode and high demand of user attention. This may lead to various problems on the usability and correctness of VPA apps.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556957"}, {"primary_key": "1727626", "vector": [], "sparse_vector": [], "title": "SML4ADS: An Open DSML for Autonomous Driving Scenario Representation and Generation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous Driving Systems(ADS) require extensive evaluation of safety before they can come onto the market. However, since relying solely on field testing is practically infeasible due to the impossibility to cover sufficient distances to ensure adequate safety, the focus shifted to scenario-based testing. The challenge is to generate scenarios flexibly. We proposed Scenario Modeling Language for ADS (SML4ADS) as a Domain-Specific Modeling Language (DSML) for scenario representation and generation. Compared to other existing works, our approach simplifies the description of scenarios in a non-programming, user-friendly manner, allows modeling stochastic behavior of vehicles and generating executable scenario in CARLA. We apply SML4ADS in numerous typical scenarios to preliminarily demonstrate the effectiveness and feasibility of our approach in modeling and generating executable scenarios.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561169"}, {"primary_key": "1727627", "vector": [], "sparse_vector": [], "title": "TransRepair: Context-aware Program Repair for Compilation Errors.", "authors": ["<PERSON><PERSON><PERSON>", "Shang<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatically fixing compilation errors can greatly raise the productivity of software development, by guiding the novice or AI programmers to write and debug code. Recently, learning-based program repair has gained extensive attention and became the state-of-the-art in practice. But it still leaves plenty of space for improvement. In this paper, we propose an end-to-end solution TransRepair to locate the error lines and create the correct substitute for a C program simultaneously. Superior to the counterpart, our approach takes into account the context of erroneous code and diagnostic compilation feedback. Then we devise a Transformer-based neural network to learn the ways of repair from the erroneous code as well as its context and the diagnostic feedback. To increase the effectiveness of TransRepair, we summarize 5 types and 74 fine-grained sub-types of compilations errors from two real-world program datasets and the Internet. Then a program corruption technique is developed to synthesize a large dataset with 1,821,275 erroneous C programs. Through the extensive experiments, we demonstrate that TransRepair outperforms the state-of-the-art in both single repair accuracy and full repair accuracy. Further analysis sheds light on the strengths and weaknesses in the contemporary solutions for future improvement.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560422"}, {"primary_key": "1727628", "vector": [], "sparse_vector": [], "title": "Robust Learning of Deep Predictive Models from Noisy and Imbalanced Software Engineering Datasets.", "authors": ["<PERSON><PERSON>", "Minxue Pan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid development of Deep Learning, deep predictive models have been widely applied to improve Software Engineering tasks, such as defect prediction and issue classification, and have achieved remarkable success. They are mostly trained in a supervised manner, which heavily relies on high-quality datasets. Unfortunately, due to the nature and source of software engineering data, the real-world datasets often suffer from the issues of sample mislabelling and class imbalance, thus undermining the effectiveness of deep predictive models in practice. This problem has become a major obstacle for deep learning-based Software Engineering.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556941"}, {"primary_key": "1727629", "vector": [], "sparse_vector": [], "title": "Are they Toeing the Line? Diagnosing Privacy Compliance Violations among Browser Extensions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guangdong Bai", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Browser extensions have emerged as integrated characteristics in modern browsers, with the aim to boost the online browsing experience. Their advantageous position between a user and the Internet endows them with easy access to the user's sensitive data, which has raised mounting privacy concerns from both legislators and extension users. In this work, we propose an end-to-end approach to automatically diagnosing the privacy compliance violations among extensions. It analyzes the compliance of privacy policy versus regulation requirements and their actual privacy-related practices during runtime. This approach can serve the extension users, developers and store operators as an efficient and practical detection mechanism for privacy compliance violations.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560436"}, {"primary_key": "1727630", "vector": [], "sparse_vector": [], "title": "Inline Tests.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unit tests are widely used to check source code quality, but they can be too coarse-grained or ill-suited for testing individual program statements. We introduce inline tests to make it easier to check for faults in statements. We motivate inline tests through several language features and a common testing scenario in which inline tests could be beneficial. For example, inline tests can allow a developer to test a regular expression in place. We also define language-agnostic requirements for inline testing frameworks. Lastly, we implement I-Test, the first inline testing framework. I-Test works for Python and Java, and it satisfies most of the requirements. We evaluate I-Test on open-source projects by using it to test 144 statements in 31 Python programs and 37 Java programs. We also perform a user study. All nine user study participants say that inline tests are easy to write and that inline testing is beneficial. The cost of running inline tests is negligible, at 0.007x–0.014x, and our inline tests helped find two faults that have been fixed by the developers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556952"}, {"primary_key": "1727631", "vector": [], "sparse_vector": [], "title": "A Unified Specification Mining Framework for Smart Contracts.", "authors": ["<PERSON>"], "summary": "Smart contracts are self-governed computer programs that run on blockchain to facilitate asset transfer between users within a trustless environment. The absence of contract specifications hinders routine tasks, such as program understanding, debugging, testing, and verification of smart contracts. In this work, we propose a unified specification mining framework to infer specification models from past transaction histories. These include access control models describing high-level authorization rules, program invariants capturing low-level program semantics, and behavior models characterizing interaction patterns allowed by contract implementations. The extracted specification models can be used to perform conformance checking on smart contracts, with the goal of eliminating unforeseen contract quality issues.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559512"}, {"primary_key": "1727632", "vector": [], "sparse_vector": [], "title": "RESTCluster: Automated Crash Clustering for RESTful API.", "authors": ["<PERSON>"], "summary": "RESTful API has been adopted by many other notable companies to provide cloud services. Quality assurance of RESTful API is essential. Several automated RESTful API testing techniques have been proposed to overcome this problem. However, automated tools often generate a large number of failed test cases. Since validating each test case is a lot of work for developers, automatic failure clustering is a promising solution to help debug cloud services.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559511"}, {"primary_key": "1727633", "vector": [], "sparse_vector": [], "title": "Automatic Generation of Visualizations for Machine Learning Pipelines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visualization is very important for machine learning (ML) pipelines because it can show explorations of the data to inspire data scientists and show explanations of the pipeline to improve understandability. In this paper, we present a novel approach that automatically generates visualizations for ML pipelines by learning visualizations from highly-upvoted Kaggle pipelines. The solution extracts both code and dataset features from these high-quality human-written pipelines and corresponding training datasets, learns the mapping rules from code and dataset features to visualizations using association rule mining (ARM), and finally uses the learned rules to predict visualizations for unseen ML pipelines. The evaluation results show that the proposed solution is feasible and effective to generate visualizations for ML pipelines.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559504"}, {"primary_key": "1727634", "vector": [], "sparse_vector": [], "title": "Learning Contract Invariants Using Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Due to the popularity of smart contracts in the modern financial ecosystem, there has been growing interest in formally verifying their correctness and security properties. Most existing techniques in this space focus on common vulnerabilities like arithmetic overflows and perform verification by leveraging contract invariants (i.e., logical formulas hold at transaction boundaries). In this paper, we propose a new technique, based on deep reinforcement learning, for automatically learning contract invariants that are useful for proving arithmetic safety. Our method incorporates an off-line training phase in which the verifier uses its own verification attempts to learn a policy for contract invariant generation. This learned (neural) policy is then used at verification time to predict likely invariants that are also useful for proving arithmetic safety. We implemented this idea in a tool called Cider and incorporated it into an existing verifier (based on refinement type checking) for proving arithmetic safety. Our evaluation shows that Cider improves both the quality of the inferred invariants as well as inference time, leading to faster verification and hardened contracts with fewer run-time assertions.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556962"}, {"primary_key": "1727635", "vector": [], "sparse_vector": [], "title": "QATest: A Uniform Fuzzing Framework for Question Answering Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jingyu Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The tremendous advancements in deep learning techniques have empowered question answering(QA) systems with the capability of dealing with various tasks. Many commercial QA systems, such as Siri, Google Home, and Alexa, have been deployed to assist people in different daily activities. However, modern QA systems are often designed to deal with different topics and task formats, which makes both the test collection and labeling tasks difficult and thus threats their quality.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556929"}, {"primary_key": "1727636", "vector": [], "sparse_vector": [], "title": "InvCon: A Dynamic Invariant Detector for Ethereum Smart Contracts.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Smart contracts are self-executing computer programs deployed on blockchain to enable trustworthy exchange of value without the need of a central authority. With the absence of documentation and specifications, routine tasks such as program understanding, maintenance, verification, and validation, remain challenging for smart contracts. In this paper, we propose a dynamic invariant detection tool, InvCon, for Ethereum smart contracts to mitigate this issue. The detected invariants can be used to not only support the reverse engineering of contract specifications, but also enable standard-compliance checking for contract implementations. InvCon provides a Web-based interface and a demonstration video of it is available at: https://youtu.be/Y1QBHjDSMYk.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559539"}, {"primary_key": "1727637", "vector": [], "sparse_vector": [], "title": "Morest: Industry Practice of Automatic RESTful API Testing.", "authors": ["<PERSON>", "Yuekang Li", "<PERSON>", "<PERSON><PERSON><PERSON> Wan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many big companies are providing cloud services through RESTful APIs nowadays. With the growing popularity of RESTful API, testing RESTful API becomes crucial. To address this issue, researchers have proposed several automatic RESTful API testing techniques. At Huawei, we design and implement an automatic RESTful API testing framework named Morest. Morest has been used to test ten RESTful API services and helped to detected 83 previously unknown bugs which were all confirmed and fixed by the developers. On one hand, we find that <PERSON>st shows great capability of detecting bugs in RESTful API s. On the other hand, we also notice that human effort is inevitable and important when applying automatic RESTful API techniques in practice.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559498"}, {"primary_key": "1727638", "vector": [], "sparse_vector": [], "title": "A First Look at CI/CD Adoptions in Open-Source Android Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Continuous Integration (CI) and Continuous Delivery (CD) have been demonstrated to be effective in facilitating software building, testing, and deployment. Many research studies have investigated and subsequently improved their working processes. Unfortunately, such research efforts have largely not touched on the usage of CI/CD in the development of Android apps. We fill this gap by conducting an exploratory study of CI/CD adoption in open-source Android apps. We start by collecting a set of 84,475 open-source Android apps from the most popular three online code hosting sites, namely Github, GitLab, and Bitbucket. We then look into those apps and find that (1) only around 10% of apps have leveraged CI/CD services, i.e., the majority of open-source Android apps are developed without accessing CI/CD services, (2) a small number of apps (291) has even adopted multiple CI/CD services, (3) nearly half of the apps adopted CI/CD services have not really used them, and (4) CI/CD services are useful to improve the popularity of projects.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561341"}, {"primary_key": "1727639", "vector": [], "sparse_vector": [], "title": "AST-Probe: Recovering abstract syntax trees from hidden representations of pre-trained language models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The objective of pre-trained language models is to learn contextual representations of textual data. Pre-trained language models have become mainstream in natural language processing and code modeling. Using probes, a technique to study the linguistic properties of hidden vector spaces, previous works have shown that these pre-trained language models encode simple linguistic properties in their hidden representations. However, none of the previous work assessed whether these models encode the whole grammatical structure of a programming language. In this paper, we prove the existence of a syntactic subspace, lying in the hidden representations of pre-trained language models, which contain the syntactic information of the programming language. We show that this subspace can be extracted from the models' representations and define a novel probing method, the AST-Probe, that enables recovering the whole abstract syntax tree (AST) of an input code snippet. In our experimentations, we show that this syntactic subspace exists in five state-of-the-art pre-trained language models. In addition, we highlight that the middle layers of the models are the ones that encode most of the AST information. Finally, we estimate the optimal size of this syntactic subspace and show that its dimension is substantially lower than those of the models' representation spaces. This suggests that pre-trained language models use a small part of their representation spaces to encode syntactic information of the programming languages.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556900"}, {"primary_key": "1727640", "vector": [], "sparse_vector": [], "title": "Checking LTL Satisfiability via End-to-end Learning.", "authors": ["<PERSON><PERSON>", "Hai Wan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Linear temporal logic (LTL) satisfiability checking is a fundamental and hard (PSPACE-complete) problem. In this paper, we explore checking LTL satisfiability via end-to-end learning, so that we can take only polynomial time to check LTL satisfiability. Existing approaches have shown that it is possible to leverage end-to-end neural networks to predict the Boolean satisfiability problem with performance considerably higher than random guessing. Inspired by these approaches, we study two interesting questions: can end-to-end neural networks check LTL satisfiability, and can neural networks capture the semantics of LTL? To this end, we train different neural networks for keeping three logical properties of LTL, i.e., recursive property, permutation invariance, and sequentiality. We demonstrate that neural networks can indeed capture some effective biases for checking LTL satisfiability. Besides, designing a special neural network keeping the logical properties of LTL can provide a better inductive bias. We also show the competitive results of neural networks compared with state-of-the-art approaches, i.e., nuXmv and Aalta, on large scale datasets.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561163"}, {"primary_key": "1727641", "vector": [], "sparse_vector": [], "title": "PRCBERT: Prompt Learning for Requirement Classification using BERT-based Pretrained Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ji<PERSON>u Sun"], "summary": "Software requirement classification is a longstanding and important problem in requirement engineering. Previous studies have applied various machine learning techniques for this problem, including Support Vector Machine (SVM) and decision trees. With the recent popularity of NLP technique, the state-of-the-art approach NoRBERT utilizes the pre-trained language model BERT and achieves a satisfactory performance. However, the dataset PROMISE used by the existing approaches for this problem consists of only hundreds of requirements that are outdated according to today's technology and market trends. Besides, the NLP technique applied in these approaches might be obsolete. In this paper, we propose an approach of prompt learning for requirement classification using BERT-based pretrained language models (PRCBERT), which applies flexible prompt templates to achieve accurate requirements classification. Experiments conducted on two existing small-size requirement datasets (PROMISE and NFR-Review) and our collected large-scale requirement dataset NFR-SO prove that PRCBERT exhibits moderately better classification performance than NoRBERT and MLM-BERT (BERT with the standard prompt template). On the de-labeled NFR-Review and NFR-SO datasets, Trans_PRCBERT (the version of PRCBERT which is fine-tuned on PROMISE) is able to have a satisfactory zero-shot performance with 53.27% and 72.96% F1-score when enabling a self-learning strategy.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560417"}, {"primary_key": "1727642", "vector": [], "sparse_vector": [], "title": "Fastbot2: Reusable Automated Model-based GUI Testing for Android Enhanced by Reinforcement Learning.", "authors": ["Zhengwei Lv", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a reusable automated model-based GUI testing technique for Android apps to accelerate the testing cycle. Our key insight is that the knowledge of event-activity transitions from the previous testing runs, i.e., executing which events can reach which activities, is valuable for guiding the follow-up testing runs to quickly cover major app functionalities. To this end, we propose (1) a probabilistic model to memorize and leverage this knowledge during testing, and (2) design a model-based guided testing strategy (enhanced by a reinforcement learning algorithm). We implemented our technique as an automated testing tool named Fastbot2. The evaluation on two popular industrial apps (with billions of user installations), <PERSON><PERSON><PERSON> and Toutiao, shows that Fastbot2 outperforms the state-of-the-art testing tools (Monkey, Ape and Stoat) in both activity coverage and fault detection in the context of continuous testing. To date, Fastbot2 has been deployed in the CI pipeline at ByteDance for nearly two years, and 50.8% of the developer-fixed crash bugs were reported by Fastbot2, which significantly improves app quality. Fastbot2 has been made publicly available to benefit the community at: https://github.com/bytedance/Fastbot_Android.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559505"}, {"primary_key": "1727643", "vector": [], "sparse_vector": [], "title": "Do Regional Variations Affect the CAPTCHA User Experience? A Comparison of CAPTCHAs in China and the United States.", "authors": ["Xinyao Ma", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Systems worldwide deploy CAPTCHAs as a security mechanism to protect from unauthorized automated access. Typically, the effectiveness of CAPTCHAs is evaluated based on their resilience against bots. User perceptions of the interactive experience and effectiveness of CAPTCHAs have received less attention, especially for comparing the variations of CAPTCHAs presented in different regions across the world. As the first step toward filling this gap, we conducted semi-structured interviews with ten participants fluent in Chinese and English to investigate whether user perceptions are affected by variations in CAPTCHAs presented in China and the United States, respectively. We found notable differences in the perceived user experience and effectiveness across the different CAPTCHA types, but not across regional variations of the same type. Our findings point to a number of avenues for making the CAPTCHA user experience more universal and inclusive.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561146"}, {"primary_key": "1727644", "vector": [], "sparse_vector": [], "title": "Automatically Detecting Visual Bugs in HTML5 Canvas Games.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Cor-<PERSON>"], "summary": "The HTML5 is used to display high quality graphics in web applications such as web games (i.e., games). However, automatically testing games is not possible with existing web testing techniques and tools, and manual testing is laborious. Many widely used web testing tools rely on the Document Object Model (DOM) to drive web test automation, but the contents of the are not represented in the DOM. The main alternative approach, snapshot testing, involves comparing oracle snapshot images with test-time snapshot images using an image similarity metric to catch visual bugs, i.e., bugs in the graphics of the web application. However, creating and maintaining oracle snapshot images for games is onerous, defeating the purpose of test automation. In this paper, we present a novel approach to automatically detect visual bugs in games. By leveraging an internal representation of objects on the , we decompose snapshot images into a set of object images, each of which is compared with a respective oracle asset (e.g., a sprite) using four similarity metrics: percentage overlap, mean squared error, structural similarity, and embedding similarity. We evaluate our approach by injecting 24 visual bugs into a custom game, and find that our approach achieves an accuracy of 100%, compared to an accuracy of 44.6% with traditional snapshot testing.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556913"}, {"primary_key": "1727645", "vector": [], "sparse_vector": [], "title": "How Readable is Model-generated Code? Examining Readability and Visual Inspection of GitHub Copilot.", "authors": ["<PERSON><PERSON>"], "summary": "Background: Recent advancements in large language models have motivated the practical use of such models in code generation and program synthesis. However, little is known about the effects of such tools on code readability and visual attention in practice. Objective: In this paper, we focus on GitHub Copilot to address the issues of readability and visual inspection of model generated code. Readability and low complexity are vital aspects of good source code, and visual inspection of generated code is important in light of automation bias. Method: Through a human experiment (n=21) we compare model generated code to code written completely by human programmers. We use a combination of static code analysis and human annotators to assess code readability, and we use eye tracking to assess the visual inspection of code. Results: Our results suggest that model generated code is comparable in complexity and readability to code written by human pair programmers. At the same time, eye tracking data suggests, to a statistically significant level, that programmers direct less visual attention to model generated code. Conclusion: Our findings highlight that reading code is more important than ever, and programmers should beware of complacency and automation bias with model generated code.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560438"}, {"primary_key": "1727646", "vector": [], "sparse_vector": [], "title": "Namesake: A Checker of Lexical Similarity in Identifier Names.", "authors": ["<PERSON><PERSON>"], "summary": "Identifier naming is one of the main sources of information in program comprehension, where a significant portion of software development time is spent. Previous research shows that similarity in identifier names could potentially hinder code comprehension, and subsequently code maintenance and evolution. In this paper, we present an open-source tool for assessing confusing naming combinations in Python programs. The tool which we call Namesake, flags confusing identifier naming combinations that are similar in orthography (word form), phonology (pronunciation), or semantics (meaning). Our tool extracts identifier names from the abstract syntax tree of a program, splits compound names, and evaluates the similarity of each pair in orthography, phonology, and semantics. Problematic identifier combinations are flagged to programmers along with their line numbers. In combination with existing coding style checkers, Namesake can provide programmers with an additional resource to enhance identifier naming quality. The tool can be integrated easily in DevOps pipelines for automated checking and identifier naming appraisal.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560441"}, {"primary_key": "1727647", "vector": [], "sparse_vector": [], "title": "Right to Know, Right to Refuse: Towards UI Perception-Based Automated Fine-Grained Permission Controls for Android Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is the basic right of a user to know how the permissions are used within the Android app's scope and to refuse the app if granted permissions are used for the activities other than specified use which can amount to malicious behavior.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559556"}, {"primary_key": "1727648", "vector": [], "sparse_vector": [], "title": "A transformer-based IDE plugin for vulnerability detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Automatic vulnerability detection is of paramount importance to promote the security of an application and should be exercised at the earliest stages within the software development life cycle (SDLC) to reduce the risk of exposure. Despite the advancements with state-of-the-art deep learning techniques in software vulnerability detection, the development environments are not yet leveraging their performance. In this work, we integrate the Transformers architecture, one of the main highlights of advances in deep learning for Natural Language Processing, within a developer-friendly tool for code security. We introduce VDet for Java, a transformer-based VS Code extension that enables one to discover vulnerabilities in Java files. Our preliminary model evaluation presents an accuracy of 98.9% for multi-label classification and can detect up to 21 vulnerability types. The demonstration of our tool can be found at https://youtu.be/OjiUBQ6TdqE, and source code and datasets are available at https://github.com/TQRG/VDET-for-Java.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559534"}, {"primary_key": "1727649", "vector": [], "sparse_vector": [], "title": "Too Much Accessibility is Harmful! Automated Detection and Analysis of Overly Accessible Elements in Mobile Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile apps, an essential technology in today's world, should provide equal access to all, including 15% of the world population with disabilities. Assistive Technologies (AT), with the help of Accessibility APIs, provide alternative ways of interaction with apps for disabled users who cannot see or touch the screen. Prior studies have shown that mobile apps are prone to the under-access problem, i.e., a condition in which functionalities in an app are not accessible to disabled users, even with the use of ATs. We study the dual of this problem, called the over-access problem, and defined as a condition in which an AT can be used to gain access to functionalities in an app that are inaccessible otherwise. Over-access has severe security and privacy implications, allowing one to bypass protected functionalities using ATs, e.g., using VoiceOver to read notes on a locked phone. Over-access also degrades the accessibility of apps by presenting to disabled users information that is actually not intended to be available on a screen, thereby confusing and hindering their ability to effectively navigate. In this work, we first empirically study overly accessible elements in Android apps and define a set of conditions that can result in over-access problem. We then present OverSight, an automated framework that leverages these conditions to detect overly accessible elements and verifies their accessibility dynamically using an AT. Our empirical evaluation of OverSight on real-world apps demonstrates OverSight's effectiveness in detecting previously unknown security threats, workflow violations, and accessibility issues.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560424"}, {"primary_key": "1727650", "vector": [], "sparse_vector": [], "title": "Keeping Secrets: Multi-objective Genetic Improvement for Detecting and Reducing Information Leakage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Information leaks in software can unintentionally reveal private data, yet they are hard to detect and fix. Although several methods have been proposed to detect leakage, such as static verification-based approaches, they require specialist knowledge, and are time-consuming. Recently, we introduced HyperGI, a dynamic, hypertest-based approach that can detect and produce potential fixes for hyperproperty violations. In particular, we focused on violations of the noninterference property, as it results in information flow leakage. Our instantiation of HyperGI was able to detect and reduce leakage in three small programs. Its fitness function tried to balance information leakage and program correctness but, as we pointed out, there may be tradeoffs between keeping program semantics and reducing information leakage that require developer decisions.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556947"}, {"primary_key": "1727651", "vector": [], "sparse_vector": [], "title": "Rank Learning-Based Code Readability Assessment with Siamese Neural Networks.", "authors": ["Qing Mi"], "summary": "Automatically assessing code readability is a relatively new challenge that has attracted growing attention from the software engineering community. In this paper, we outline the idea to regard code readability assessment as a learning-to-rank task. Specifically, we design a pairwise ranking model with siamese neural networks, which takes as input a code pair and outputs their readability ranking order. We have evaluated our approach on three publicly available datasets. The result is promising, with an accuracy of 83.5%, a precision of 86.1%, a recall of 81.6%, an F-measure of 83.6% and an AUC of 83.4%.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560440"}, {"primary_key": "1727652", "vector": [], "sparse_vector": [], "title": "How students choose names: A replication study.", "authors": ["Qing Mi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Names of classes/methods/variables play an important role in code readability. To investigate how developers choose names, <PERSON><PERSON><PERSON> et al. conducted an empirical survey and suggested a method to improve naming quality. We replicated their study, but limited the survey subjects to university students. Specifically, we conducted two experiments including 341 students from freshmen to seniors. The aim of the first experiment was to investigate the characteristics of the names given by students. The experimental results showed that the name length as well as the number of words contained in names increased with the grade and students have ambiguity in understanding variable names. The second experiment was to verify whether <PERSON><PERSON><PERSON> et al.'s naming method can help improve the quality of the names given by students. The experimental results showed an improvement in naming quality for more than 67% of cases, which confirms the validity of the method for university students.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561174"}, {"primary_key": "1727653", "vector": [], "sparse_vector": [], "title": "Automatically Fixing Breaking Changes of Data Science Libraries.", "authors": ["<PERSON><PERSON>"], "summary": "Data science libraries are updated frequently, and new version releases commonly include breaking changes. These are updates that cause existing code to not compile or run. Developers often use older versions of libraries because it is challenging to update the source code of large projects. We propose CombyInferPy, a new tool to automatically analyze and fix breaking changes in library APIs. CombyInferPy infers rules from the history of library source code in the form of Comby templates, a structural code search and replace tool that can automatically transform code. Preliminary results indicate CombyInferPy can update the pandas library Python code. Using the Comby rules inferred by CombyInferPy, we can automatically fix several failing tests and warnings. This shows this approach is promising to help developers update libraries.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559507"}, {"primary_key": "1727654", "vector": [], "sparse_vector": [], "title": "Maktub: Lightweight Robot System Test Creation and Automation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapid expansion of robotics relies on properly configuring and testing hardware and software. Due to the expense and hazard of real-world testing on hardware, robot system testing increasingly utilizes extensive simulation. Creating robot simulation tests requires specialized skills in robot programming and simulation tools. While there are many platforms and tool-kits to create these simulations, they can be cumbersome when combined with automated testing. We present Maktub: a tool for creating tests using Unity and ROS. Maktub leverages the extensive 3D manipulation capabilities of Unity to lower the barrier in creating system tests for robots. A key idea of Maktub is to make tests without needing robotic software development skills. A video demonstration of Makt<PERSON> can be found here: https://youtu.be/c0Bacy3DlEE, and the source code can be found at https://github.com/RobotCodeLab/Maktub.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559531"}, {"primary_key": "1727655", "vector": [], "sparse_vector": [], "title": "Automatic Comment Generation via Multi-Pass Deliberation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deliberation is a common and natural behavior in human daily life. For example, when writing papers or articles, we usually first write drafts, and then iteratively polish them until satisfied. In light of such a human cognitive process, we propose DECOM, which is a multi-pass deliberation framework for automatic comment generation. DECOM consists of multiple Deliberation Models and one Evaluation Model. Given a code snippet, we first extract keywords from the code and retrieve a similar code fragment from a pre-defined corpus. Then, we treat the comment of the retrieved code as the initial draft and input it with the code and keywords into DECOM to start the iterative deliberation process. At each deliberation, the deliberation model polishes the draft and generates a new comment. The evaluation model measures the quality of the newly generated comment to determine whether to end the iterative process or not. When the iterative process is terminated, the best-generated comment will be selected as the target comment. Our approach is evaluated on two real-world datasets in Java (87K) and Python (108K), and experiment results show that our approach outperforms the state-of-the-art baselines. A human evaluation study also confirms the comments generated by DECOM tend to be more readable, informative, and useful.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556917"}, {"primary_key": "1727656", "vector": [], "sparse_vector": [], "title": "Boosting Spectrum-Based Fault Localization for Spreadsheets with Product Metrics in a Learning Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Faults in spreadsheets are not uncommon and they can have significant negative consequences in practice. Various approaches for fault localization were proposed in recent years, among them techniques that transferred ideas from spectrum-based fault localization (SFL) to the spreadsheet domain. Applying SFL to spreadsheets proved to be effective, but has certain limitations. Specifically, the constrained computational structures of spreadsheets may lead to large sets of cells that have the same assumed fault probability according to SFL and thus have to be inspected manually. In this work, we propose to combine SFL with a fault prediction method based on spreadsheet metrics in a machine learning (ML) approach. In particular, we train supervised ML models using two orthogonal types of features: (i) variables that are used to compute similarity coefficients in SFL and (ii) spreadsheet metrics that have shown to be good predictors for faulty formulas in previous work. Experiments with a widely-used corpus of faulty spreadsheets indicate that the combined model helps to significantly improve fault localization performance in terms of wasted effort and accuracy.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559546"}, {"primary_key": "1727657", "vector": [], "sparse_vector": [], "title": "Research on Test Flakiness: from Unit to System Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Test flakiness has been a common problem in the software automation testing, affecting the effectiveness and productivity of test automation. Many studies have been published to tackle test flakiness in the software research community, and existing test automation tools provide capabilities to address issues related to test flakiness. This paper describes our review of recent approaches in the research community and popular industrial tools with capabilities to tackle test flakiness. Our review indicates that while many studies focus on test flakiness in unit testing, few address the problem in the end-to-end system testing. On the contrary, industrial test automation tools tend to be more concerned with test flakiness at the system level.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3563242"}, {"primary_key": "1727658", "vector": [], "sparse_vector": [], "title": "A Hybrid Approach for Inference between Behavioral Exception API Documentation and Implementations, and Its Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automatically producing behavioral exception (BE) API documentation helps developers correctly use the libraries. The state-of-the-art approaches are either rule-based, which is too restrictive in its applicability, or deep learning (DL)-based, which requires large training dataset. To address that, we propose StatGen, a novel hybrid approach between statistical machine translation (SMT) and tree-structured translation to generate the BE documentation for any code and vice versa. We consider the documentation and source code of an API method as the two abstraction levels of the same intent. StatGen is specifically designed for this two-way inference, and takes advantage of their structures for higher accuracy.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560434"}, {"primary_key": "1727659", "vector": [], "sparse_vector": [], "title": "CARGO: AI-Guided Dependency Analysis for Migrating Monolithic Applications to Microservices Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ray", "<PERSON><PERSON>"], "summary": "Microservices Architecture (MSA) has become a de-facto standard for designing cloud-native enterprise applications due to its efficient infrastructure setup, service availability, elastic scalability, dependability, and better security. Existing (monolithic) systems must be decomposed into microservices to harness these characteristics. Since manual decomposition of large scale applications can be laborious and error-prone, AI-based systems to decompose applications are gaining popularity. However, the usefulness of these approaches is limited by the expressiveness of the program representation and their inability to model the application's dependency on critical external resources such as databases. Consequently, partitioning recommendations offered by current tools result in architectures that result in (a) distributed monoliths, and/or (b) force the use of (often criticized) distributed transactions. This work attempts to overcome these challenges by introducing CARGO (short for Context-sensitive lAbel pRopaGatiOn)—a novel un-/semi-supervised partition refinement technique that uses a context- and flow-sensitive system dependency graph of the monolithic application to refine and thereby enrich the partitioning quality of the current state-of-the-art algorithms. CARGO was used to augment four state-of-the-art microservice partitioning techniques (comprised of 1 industrial tool and 3 open-source projects). These were applied on five Java EE applications (comprised of 1 proprietary and 4 open source projects). Experiments show that CARGO is capable of improving the partition quality of all four partitioning techniques. Further, CARGO substantially reduces distributed transactions, and a real-world performance evaluation of a benchmark application (deployed under varying loads) shows that CARGO also lowers the overall the latency of the deployed microservice application by 11% and increases throughput by 120% on average.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556960"}, {"primary_key": "1727660", "vector": [], "sparse_vector": [], "title": "Enhancing the security of gaming transactions using blockchain technology.", "authors": ["<PERSON><PERSON><PERSON>", "Rares <PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose GameBlockchain, an open-source blockchain framework designed to support secure transactions of NFTs in modern computer games. Its purpose is to enable game industry stakeholders such as game developers, content creators, and regular gamers to create and exchange game assets in a more secure and trusted environment. The security of traditional databases and potential data tampering or dangerous user behavior is improved, as outlined in the paper, by blockchain technology, which is used to record critical operations in a ledger, preserving the identity of the user at all times. From a technical perspective, the main goal is to provide an architecture that is easy to use, flexible, understandable, and has an extensible SDK. Using the framework, game developers and regular users should be able to create and trade assets without third-party providers, and use all related services directly in the game interface itself, without having to switch between applications or pay additional transfer fees to providers. We also encourage the development of games with shared marketplaces and wallets on both the developer and user sides, making it easier to monetize assets and services.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560504"}, {"primary_key": "1727661", "vector": [], "sparse_vector": [], "title": "Transfer learning of cars behaviors from reality to simulation applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Creating synthetic behaviors of vehicles in simulation applications has always been challenging from a development standpoint. First, it is a real challenge to create a credible and realistic simulation while achieving the required runtime efficiency. Second, the effort required to implement it can add significant cost to the development processes. In this paper, we propose an automated way to design vehicle simulation systems by transfer learning from reality to simulators. Our methods rely on advanced deep learning technologies and datasets commonly used in the field of self-driving cars. To assess how well this approach would work in a simulation environment, experiments using the CARLA simulator are presented in the evaluation. The results show that the proposed transfer learning approach provides good results, both quantitatively and qualitatively, and is suitable for runtime evaluation even in resource-constrained simulation applications such as video games.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560506"}, {"primary_key": "1727662", "vector": [], "sparse_vector": [], "title": "Assessment of Automated (Intelligent) Toolchains.", "authors": ["<PERSON>ti"], "summary": "[Background:] Automated Intelligent Toolchains, which are a composition of different tools that use AI or static analysis, are widely used in software engineering to deploy automated program repair techniques, or in software security to identify vulnerabilities. [Overall Research Problem:] Most studies with automated intelligent toolchains report uncertainty and evaluations only of the individual components of the chain. How do we calculate the uncertainty and error propagation on the overall automated toolchain? [Approach:] I plan to replicate research case studies to collect data and design a methodology to reconstruct the overall correctness metrics of the toolchains, or identifying missing variables. Further confirmatory experiments with humans will be performed. Finally, I will implement an artifact to automate the overall assessment of automated toolchains. [Current Status:] A preliminary validation of published studies showed promising results.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559572"}, {"primary_key": "1727663", "vector": [], "sparse_vector": [], "title": "ESAVE: Estimating Server and Virtual Machine Energy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sustainable software engineering has received a lot of attention in recent times, as we witness an ever-growing slice of energy use, for example, at data centers, as software systems utilize the underlying infrastructure. Characterizing servers for their energy use accurately without being intrusive, is therefore important to make sustainable software deployment choices. In this paper, we introduce ESAVE which is a machine learning-based approach that leverages a small set of hardware attributes to characterize a server or virtual machine's energy usage across different levels of utilization. This is based upon an extensive exploration of multiple ML approaches, with a focus on a minimal set of required attributes, while showcasing good accuracy. Early validations show that ESAVE has only around 12% average prediction error, despite being non-intrusive.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561170"}, {"primary_key": "1727664", "vector": [], "sparse_vector": [], "title": "Scaling Arbitrary Android App Analyses.", "authors": ["<PERSON>"], "summary": "More apps are published every day and the functionality of each app increases steadily as well. Consequently app analyses are often overwhelmed when confronted with up-to-date, real-world apps. One of the biggest issues originates from the scalability of analyses with respect to libraries. Analyses, more precisely the tools implementing them, cannot distinguish the app's code from the code of a library. Always analyzing the whole code base is the result. However, this is usually not necessary, for example, when a security property is checked, trusted libraries must not be analyzed.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561339"}, {"primary_key": "1727665", "vector": [], "sparse_vector": [], "title": "ASTOR: An Approach to Identify Security Code Reviews.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "During code reviews, software developers often raise security concerns if they find any. Ignoring such concerns can bring a severe impact on the performance of a software product. This risk can be reduced if we can automatically identify such code reviews that trigger security concerns so that we can perform additional scrutiny from the security experts. Therefore, the objective of this study is to develop an automated tool to identify code reviews that trigger security concerns.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559509"}, {"primary_key": "1727666", "vector": [], "sparse_vector": [], "title": "A real-world case study for automated ticket team assignment using natural language processing and explainable models.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the context of software development, managing and organizing agile boards of multi-disciplinary teams distributed around the world is a great challenge, especially regarding the process of assigning tickets to the correct team roles. Incorrectly assigned tickets can result in significant resource waste in any project and directly influence delivery outcomes and project costs. This work proposes a method for ticket analysis and automatic team assignment using Natural Language Processing and explainable Machine Learning models. Results show that the models perform well on a real-world team assignment task and provide insights into their decision.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561164"}, {"primary_key": "1727667", "vector": [], "sparse_vector": [], "title": "Design-Space Exploration for Decision-Support Software.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Presence monitoring or intrusion detection in a location/area are examples of decision-support applications. Decision-support applications are applications where monitoring is used to collect (heterogeneous) data and create situational awareness, which further requires decisions and/or actions. As such, decision-support software consists of different interconnected components with very diverse roles, whose communication and synchronization are essential for the application functionality and performance. Despite this complexity, software design for decision-support is often driven by short-term functional requirements and only supported by designers' previous experience. In the current non-systematic approach, mistakes can be easily made, and can be very difficult to repair.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559502"}, {"primary_key": "1727668", "vector": [], "sparse_vector": [], "title": "A Review of AI-augmented End-to-End Test Automation Tools.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software testing is a process of evaluating and verifying whether a software product still works as expected, and it is repetitive, laborious, and time-consuming. To address this problem, automation tools have been developed to automate testing activities and enhance quality and delivery time. However, automation tools become less effective with continuous integration and continuous delivery (CI/CD) pipelines when the system under test is constantly changing. Recent advances in artificial intelligence and machine learning (AI/ML) present the potential for addressing important challenges in test automation. AI/ML can be applied to automate various testing activities such as detecting bugs and errors, maintaining existing test cases, or generating new test cases much faster than humans.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3563240"}, {"primary_key": "1727669", "vector": [], "sparse_vector": [], "title": "Application of Natural Language Processing Towards Autonomous Software Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The process of creating test cases from requirements written in natural language (NL) requires intensive human efforts and can be tedious, repetitive, and error-prone. Thus, many studies have attempted to automate that process by utilizing Natural Language Processing (NLP) approaches. Furthermore, with the advent of massive language models and transfer learning techniques, people have introduced various advancements in NLP-assisted software testing with promising results. More notably, in recent years, not only have researchers been engrossed in solving the above task, but many companies have also embedded the feature to translate from human language to test cases their products. This paper presents an overview of NLP-assisted solutions being used in both the literature and the software testing industry.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3563241"}, {"primary_key": "1727670", "vector": [], "sparse_vector": [], "title": "So Many Fuzzers, So Little Time✱: Experience from Evaluating Fuzzers on the Contiki-NG Network (Hay)Stack.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fuzz testing (\"fuzzing\") is a widely-used and effective dynamic technique to discover crashes and security vulnerabilities in software, supported by numerous tools, which keep improving in terms of their detection capabilities and speed of execution. In this paper, we report our findings from using state-of-the-art mutation-based and hybrid fuzzers (AFL, Angora, Honggfuzz, Intriguer, MOpt-AFL, QSym, and SymCC) on a non-trivial code base, that of Contiki-NG, to expose and fix serious vulnerabilities in various layers of its network stack, during a period of more than three years. As a by-product, we provide a Git-based platform which allowed us to create and apply a new, quite challenging, open-source bug suite for evaluating fuzzers on real-world software vulnerabilities. Using this bug suite, we present an impartial and extensive evaluation of the effectiveness of these fuzzers, and measure the impact that sanitizers have on it. Finally, we offer our experiences and opinions on how fuzzing tools should be used and evaluated in the future.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556946"}, {"primary_key": "1727671", "vector": [], "sparse_vector": [], "title": "Principled Composition of Function Variants for Dynamic Software Diversity and Program Protection.", "authors": ["<PERSON>", "Daniele Cono D&apos;Elia", "<PERSON>"], "summary": "Artificial diversification of a software program can be a versatile tool in a wide range of software engineering and security scenarios. For example, randomizing implementation aspects can increase the costs for attackers as it prevents them from benefiting of precise knowledge of their target. A promising angle for diversification can be having two runs of a program on the same input yield inherently diverse instruction traces. Inspired by on-stack replacement designs for managed runtimes, in this paper we study how to transform a C program to realize continuous transfers of control and program state among function variants as they run. We discuss the technical challenges toward such goal and propose effective compiler techniques for it that enable the re-use of existing techniques for static diversification with no modifications. We implement our approach in LLVM and evaluate it on both synthetic and real-world subjects.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559553"}, {"primary_key": "1727672", "vector": [], "sparse_vector": [], "title": "Patching Weak Convolutional Neural Network Models through Modularization and Composition.", "authors": ["Binhang Qi", "Hailong Sun", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite great success in many applications, deep neural networks are not always robust in practice. For instance, a convolutional neuron network (CNN) model for classification tasks often performs unsatisfactorily in classifying some particular classes of objects. In this work, we are concerned with patching the weak part of a CNN model instead of improving it through the costly retraining of the entire model. Inspired by the fundamental concepts of modularization and composition in software engineering, we propose a compressed modularization approach, CNNSplitter, which decomposes a strong CNN model for N-class classification into N smaller CNN modules. Each module is a sub-model containing a part of the convolution kernels of the strong model. To patch a weak CNN model that performs unsatisfactorily on a target class (TC), we compose the weak CNN model with the corresponding module obtained from a strong CNN model. The ability of the weak CNN model to recognize the TC can thus be improved through patching. Moreover, the ability to recognize non-TCs is also improved, as the samples misclassified as TC could be classified as non-TCs correctly. Experimental results with two representative CNNs on three widely-used datasets show that the averaged improvement on the TC in terms of precision and recall are 12.54% and 2.14%, respectively. Moreover, patching improves the accuracy of non-TCs by 1.18%. The results demonstrate that CNNSplitter can patch a weak CNN model through modularization and composition, thus providing a new solution for developing robust CNN models.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561153"}, {"primary_key": "1727673", "vector": [], "sparse_vector": [], "title": "Accelerating OCR-Based Widget Localization for Test Automation of GUI Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Optical character recognition (OCR) algorithms often run slow. They may take several seconds to recognize the texts on a GUI screen, which makes OCR-based widget localization in test automation unfriendly for use, especially on GPU-free computers. This paper first concludes a common type of widget text to be located in GUI testing: label text, which are short texts in widgets like buttons, menu items, and window titles. We then investigate the characteristics of texts on a GUI screen and introduce a fast GPU-independent Label Text Screening (LTS) technique to accelerate the OCR process for label text localization. The technique opens the black box of OCR engines and uses a combination of simple methods to avoid excessive text analysis on a screen as much as possible. Experiments show that, on the subject datasets, LTS reduces the average OCR-based label text localization time to a large extent. On 4k resolution GUI screens, it keeps the localization time below 0.5 seconds for over about 60% of cases without GPU support on a normal laptop computer. In contrast, the existing CPU-based approaches built on popular OCR engines Tesseract, PaddleOCR, and EasyOCR usually need over 2 seconds to achieve the same goal on the same platform. Even with GPU acceleration, they can hardly keep the analysis time in 1 second. We believe the proposed approach would be helpful for implementing OCR-based test automation tools.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556966"}, {"primary_key": "1727674", "vector": [], "sparse_vector": [], "title": "DyTRec: A Dynamic Testing Recommendation tool for Unity-based Virtual Reality Software.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Virtual Reality (VR) technology has been utilized in other fields besides gaming, such as education, training, arts, shopping, and e-commerce. However, the technical support of VR software is not growing as fast as its market size, especially for testing. Because of the immersive feature that requires VR apps to act and react to all the interactions dynamically, the traditional static testing techniques such as unit test generation cannot fully guarantee the correctness of the tested functions. In this paper, we proposed a Dynamic Testing Recommendation tool (DyTRec) to suggest the potential types of dynamic testing for the target VR projects. Specifically, we categorize the dynamic testing types by analyzing the official APIs from the VR engine documentation and then apply the extracting and searching on all VR script files. We evaluated DyTRec on 20 VR projects and successfully reported 39 suggested results.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560510"}, {"primary_key": "1727675", "vector": [], "sparse_vector": [], "title": "Answering Software Deployment Questions via Neural Machine Reading at Scale.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xiaodong Gu", "<PERSON><PERSON><PERSON>"], "summary": "As software systems continue to grow in complexity and scale, deploying and delivering them becomes increasingly difficult. In this work, we develop DeployQA, a novel QA bot that automatically answers software deployment questions over user manuals and Stack Overflow posts. DeployQA is built upon RoBERTa. To bridge the gap between natural language and the domain of software deployment, we propose three adaptations in terms of vocabulary, pre-training, and fine-tuning, respectively. We evaluate our approach on our constructed DeQuAD dataset. The results show that DeployQA remarkably outperforms baseline methods by leveraging the three domain adaptation strategies.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559525"}, {"primary_key": "1727676", "vector": [], "sparse_vector": [], "title": "Towards Understanding the Faults of JavaScript-Based Deep Learning Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Quality assurance is of great importance for deep learning (DL) systems, especially when they are applied in safety-critical applications. While quality issues of native DL applications have been extensively analyzed, the issues of JavaScript-based DL applications have never been systematically studied. Compared with native DL applications, JavaScript-based DL applications can run on major browsers, making the platform- and device-independent. Specifically, the quality of JavaScript-based DL applications depends on the 3 parts: the application, the third-party DL library used and the underlying DL framework (e.g., TensorFlow.js), called JavaScript-based DL system. In this paper, we conduct the first empirical study on the quality issues of JavaScript-based DL systems. Specifically, we collect and analyze 700 real-world faults from relevant GitHub repositories, including the official TensorFlow.js repository, 13 third-party DL libraries, and 58 JavaScript-based DL applications. To better understand the characteristics of these faults, we manually analyze and construct taxonomies for the fault symptoms, root causes, and fix patterns, respectively. Moreover, we also study the fault distributions of symptoms and root causes, in terms of the different stages of the development lifecycle, the 3-level architecture in the DL system, and the 4 major components of TensorFlow.js framework. Based on the results, we suggest actionable implications and research avenues that can potentially facilitate the development, testing, and debugging of JavaScript-based DL systems.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560427"}, {"primary_key": "1727677", "vector": [], "sparse_vector": [], "title": "PredART: Towards Automatic Oracle Prediction of Object Placements in Augmented Reality Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While the emerging Augmented Reality (AR) technique allows a lot of new application opportunities, from education and communication to gaming, current augmented apps often have complaints about their usability and/or user experience due to placement errors of virtual objects. Therefore, identifying noticeable placement errors is an important goal in the testing of AR apps. However, placement errors can only be perceived by human beings and may need to be confirmed by multiple users, making automatic testing very challenging. In this paper, we propose PredART, a novel approach to predict human ratings of virtual object placements that can be used as test oracles in automated AR testing. PredART is based on automatic screenshot sampling, crowd sourcing, and a hybrid neural network for image regression. The evaluation on a test set of 480 screenshots shows that our approach can achieve an accuracy of 85.0% and a mean absolute error, mean squared error, and root mean squared error of 0.047, 0.008, and 0.091, respectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561160"}, {"primary_key": "1727678", "vector": [], "sparse_vector": [], "title": "Exploiting Epochs and Symmetries in Analysing MPI Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Communication nondeterminism is one of the main reasons for the intractability of verification of message passing concurrency. In many practical message passing programs, the non-deterministic communication structure is symmetric and decomposed into epochs to obtain efficiency. Thus, symmetries and epoch structure can be exploited to reduce verification complexity. In this paper, we present a dynamic-symbolic runtime verification technique for single-path MPI programs, which (i) exploits communication symmetries by way of specifying symmetry breaking predicates (SBP) and (ii) performs compositional verification based on epochs. On the one hand, SBPs prevent the symbolic decision procedure from exploring isomorphic parts of the search space, and on the other hand, epochs restrict the size of a program needed to be analyzed at a point in time. We show that our analysis is sound and complete for single-path MPI programs on a given input. Using our prototype tool SIMIAN, we further demonstrate that our approach leads to (i) a significant reduction in verification times and (ii) scaling up to larger benchmark sizes compared to prior trace verifiers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556954"}, {"primary_key": "1727679", "vector": [], "sparse_vector": [], "title": "Leveraging Practitioners&apos; Feedback to Improve a Security Linter.", "authors": ["Sofia Reis", "<PERSON><PERSON>", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>", "<PERSON>"], "summary": "Infrastructure-as-Code (IaC) is a technology that enables the management and distribution of infrastructure through code instead of manual processes. In 2020, Palo Alto Network’s Unit 42 announced the discovery of over 199K vulnerable IaC templates through their “Cloud Threat” Report. This report highlights the importance of tools to prevent vulnerabilities from reaching production. Unfortunately, we observed through a comprehensive study that a security linter for IaC scripts is not reliable yet—high false positive rates. Our approach to tackling this problem was to leverage community expertise to improve the precision of this tool. More precisely, we interviewed professional developers to collect their feedback on the root causes of imprecision of the state-of-the-art security linter for <PERSON>uppet. From that feedback, we developed a linter adjusting 7 rules of an existing linter ruleset and adding 3 new rules. We conducted a new study with 131 practitioners, which helped us improve the tool’s precision significantly and achieve a final precision of . An important takeaway from this paper is that obtaining professional feedback is fundamental to improving the rules’ precision and extending the rulesets, which is critical for the usefulness and adoption of lightweight tools, such as IaC security linters.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560419"}, {"primary_key": "1727680", "vector": [], "sparse_vector": [], "title": "Prototyping Deep Learning Applications with Non-Experts: An Assistant Proposition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) systems based on deep neural networks are more present than ever in software solutions for numerous industries. Their inner workings relying on models learning with data are as helpful as they are mysterious for non-expert people. There is an increasing need to make the design and development of those solutions accessible to a more general public while at the same time making them easier to explore. In this paper, to address this need, we discuss a proposition of a new assisted approach, centered on the downstream task to be performed, for helping practitioners to start using and applying Deep Learning (DL) techniques. This proposal, supported by an initial testbed UI prototype, uses an externalized form of knowledge, where JSON files compile different pipeline metadata information with their respective related artifacts (e.g., model code, the dataset to be loaded, good hyperparameter choices) that are presented as the user interacts with a conversational agent to suggest candidate solutions for a given task.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561166"}, {"primary_key": "1727681", "vector": [], "sparse_vector": [], "title": "Are Neural Bug Detectors Comparable to Software Developers on Variable Misuse Bugs?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Debugging, that is, identifying and fixing bugs in software, is a central part of software development. Developers are therefore often confronted with the task of deciding whether a given code snippet contains a bug, and if yes, where. Recently, data-driven methods have been employed to learn this task of bug detection, resulting (amongst others) in so called neural bug detectors. Neural bug detectors are trained on millions of buggy and correct code snippets.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561156"}, {"primary_key": "1727682", "vector": [], "sparse_vector": [], "title": "SAFA: A Tool for Supporting Safety Analysis in Evolving Software Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Many organizations seek to increase their agility in order to deliver more timely and competitive products. However, in safety-critical systems such as medical devices, autonomous vehicles, or factory floor robots, the release of new features has the potential to introduce hazards that potentially lead to run-time failures that impact software safety. As a result, many projects suffer from a phenomenon referred to as the big freeze. SAFA is designed to address this challenge. Through the use of cutting-edge deep-learning solutions, it generates trees of requirements, designs, code, tests, and other artifacts that visually depict how hazards are mitigated in the system, and it automatically warns the user when key artifacts are missing. It also uses a combination of colors, annotations, and recommendations to dynamically visualize change across software versions and augments safety cases with visual annotations to aid users in detecting and analyzing potentially adverse impacts of change upon system safety. A link to our tool demo can be found at https://www.youtube.com/watch?v=r-CwxerbSVA.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559535"}, {"primary_key": "1727683", "vector": [], "sparse_vector": [], "title": "E-MANAFA: Energy Monitoring and ANAlysis tool For Android.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This article introduces the E-MANAFA energy profiler, a plug-and-play, device-independent, model-based profiler capable of obtaining fine-grained energy measurements on Android devices. Besides having the capability to calculate performance metrics such as the energy consumed and runtime during a time interval, E-MANAFA also allows to estimate the energy consumed by each device component (e.g. CPU, WI-FI, screen). In this article, we present the main elements that compose this framework, as well as its workflow. In order to present the power of this tool, we demonstrate how the tool can measure the overhead of the instrumentation technique used in the PyAnaDroid application benchmarking pipeline, which already supports E-MANAFA to monitor power consumption in its Android application automatic execution process. Video demo: shorturl.at/hmyz5", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561342"}, {"primary_key": "1727684", "vector": [], "sparse_vector": [], "title": "Code Understanding Linter to Detect Variable Misuse.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We share our experience in developing Code Understanding Linter, an automated code review tool based on language models of code. We introduce several ideas to make the tool be more practical, including combining two different language models, filtering meaningless outputs from the model, and generating developer-friendly diagnosis messages by interpreting the outputs from the model. On top of those ideas, we describe the design and implementation of an automated code review tool to detect variable-misuse defects in Python codes and suggest how to fix them. We evaluated the tool with a set of code repositories in Samsung Electronics, which contains real-world Python codes. Our experiment proves that our tool can discover hidden defects in the real-world codes, but the false positive rate is far higher than we expected. After manually investigating every false positives, we discuss the limitations of the language models and possible solutions.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559497"}, {"primary_key": "1727685", "vector": [], "sparse_vector": [], "title": "GLITCH: Automated Polyglot Security Smell Detection in Infrastructure as Code.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Infrastructure as Code (IaC) is the process of managing IT infrastructure via programmable configuration files (also called IaC scripts). Like other software artifacts, IaC scripts may contain security smells, which are coding patterns that can result in security weaknesses. Automated analysis tools to detect security smells in IaC scripts exist, but they focus on specific technologies such as Puppet, Ansible, or Chef. This means that when the detection of a new smell is implemented in one of the tools, it is not immediately available for the technologies supported by the other tools — the only option is to duplicate the effort.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556945"}, {"primary_key": "1727686", "vector": [], "sparse_vector": [], "title": "Groundhog: An Automated Accessibility Crawler for Mobile Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Accessibility is a critical software quality affecting more than 15% of the world's population with some form of disabilities. Modern mobile platforms, i.e., iOS and Android, provide guidelines and testing tools for developers to assess the accessibility of their apps. The main focus of the testing tools is on examining a particular screen's compliance with some predefined rules derived from accessibility guidelines. Unfortunately, these tools cannot detect accessibility issues that manifest themselves in interactions with apps using assistive services, e.g., screen readers. A few recent studies have proposed assistive-service driven testing; however, they require manually constructed inputs from developers to evaluate a specific screen or presume availability of UI test cases. In this work, we propose an automated accessibility crawler for mobile apps, Groundhog, that explores an app with the purpose of finding accessibility issues without any manual effort from developers. Groundhog assesses the functionality of UI elements in an app with and without assistive services and pinpoints accessibility issues with an intuitive video of how to replicate them. Our experiments show Groundhog is highly effective in detecting accessibility barriers that existing techniques cannot discover. Powered by Groundhog, we conducted an empirical study on a large set of real-world apps and found new classes of critical accessibility issues that should be the focus of future work in this area.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556905"}, {"primary_key": "1727687", "vector": [], "sparse_vector": [], "title": "Identification and Mitigation of Toxic Communications Among Open Source Software Developers.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Toxic and unhealthy conversations during the developer's communication may reduce the professional harmony and productivity of Free and Open Source Software (FOSS) projects. For example, toxic code review comments may raise pushback from an author to complete suggested changes. A toxic communication with another person may hamper future communication and collaboration. Research also suggests that toxicity disproportionately impacts newcomers, women, and other participants from marginalized groups. Therefore, toxicity is a barrier to promote diversity, equity, and inclusion. Since the occurrence of toxic communications is not uncommon among FOSS communities and such communications may have serious repercussions, the primary objective of my proposed dissertation is to automatically identify and mitigate toxicity during developers' textual interactions. On this goal, I aim to: i) build an automated toxicity detector for Software Engineering (SE) domain, ii) identify the notion of toxicity across demographics, and iii) analyze the impacts of toxicity on the outcomes of Open Source Software (OSS) projects.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559570"}, {"primary_key": "1727688", "vector": [], "sparse_vector": [], "title": "&apos;Who built this crap?&apos; Developing a Software Engineering Domain Specific Toxicity Detector.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Since toxicity during developers' interactions in open source software (OSS) projects show negative impacts on developers' relation, a toxicity detector for the Software Engineering (SE) domain is needed. However, prior studies found that contemporary toxicity detection tools performed poorly with the SE texts. To address this challenge, I have developed ToxiCR, a SE-specific toxicity detector that is evaluated with manually labeled 19,571 code review comments. I evaluate ToxiCR with different combinations of ten supervised learning models, five text vectorizers, and eight preprocessing techniques (two of them are SE domain-specific). After applying all possible combinations, I have found that ToxiCR significantly outperformed existing toxicity classifiers with accuracy of 95.8% and an F1 score of 88.9%.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559508"}, {"primary_key": "1727689", "vector": [], "sparse_vector": [], "title": "Natural Test Generation for Precise Testing of Question Answering Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Question answering (QA) software uses information retrieval and natural language processing techniques to automatically answer questions posed by humans in a natural language. Like other AI-based software, QA software may contain bugs. To automatically test QA software without human labeling, previous work extracts facts from question answer pairs and generates new questions to detect QA software bugs. Nevertheless, the generated questions could be ambiguous, confusing, or with chaotic syntax, which are unanswerable for QA software. As a result, a relatively large proportion of the reported bugs are false positives. In this work, we proposed QAQA, a sentence-level mutation based metamorphic testing technique for QA software. To eliminate false positives and achieve precise automatic testing, QAQA leverages five Metamorphic Relations (MRs) as well as semantics-guided search and enhanced test oracles. Our evaluation on three QA datasets demonstrates that QAQA outperforms the state-of-the-art in both quantity (8,133 vs. 6,601 bugs) and quality (97.67% vs. 49% true positive rate) of the reported bugs. Moreover, the test inputs generated by QAQA successfully reduce MR violation rate from 44.29% to 20.51% when being adopted in fine-tuning the QA software under test.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556953"}, {"primary_key": "1727690", "vector": [], "sparse_vector": [], "title": "Precise (Un)Affected Version Analysis for Web Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiangyu Mao", "<PERSON>"], "summary": "Web applications are attractive attack targets given their popularity and large number of vulnerabilities. To mitigate the threat of web vulnerabilities, an important piece of information is their affected versions. However, it is non-trivial to build accurate affected version information because confirming a version as affected or unaffected requires security expertise and huge efforts, while there are usually hundreds of versions to examine. As a result, such information is maintained in a low-quality manner in almost every public vulnerability database. Therefore, it is extremely useful to have a tool that can automatically and precisely examine a large part (even if not all) of the software versions as affected or unaffected.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556933"}, {"primary_key": "1727691", "vector": [], "sparse_vector": [], "title": "Compressing Pre-trained Models of Code into 3 MB.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although large pre-trained models of code have delivered significant advancements in various code processing tasks, there is an impediment to the wide and fluent adoption of these powerful models in software developers' daily workflow: these large models consume hundreds of megabytes of memory and run slowly on personal devices, which causes problems in model deployment and greatly degrades the user experience.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556964"}, {"primary_key": "1727692", "vector": [], "sparse_vector": [], "title": "Cornucopia : A Framework for Feedback Guided Generation of Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Binary analysis is an important capability required for many security and software engineering applications. Consequently, there are many binary analysis techniques and tools with varied capabilities. However, testing these tools requires a large, varied binary dataset with corresponding source-level information. In this paper, we present Cornucopia, an architecture agnostic automated framework that can generate a plethora of binaries from corresponding program source by exploiting compiler optimizations and feedback-guided learning. Our evaluation shows that Cornucopia was able to generate 309K binaries across four architectures (x86, x64, ARM, MIPS) with an average of 403 binaries for each program and outperforms Bintuner, a similar technique. Our experiments revealed issues with the LLVM optimization scheduler resulting in compiler crashes ($\\sim$300). Our evaluation of four popular binary analysis tools Angr, Ghidra, Idapro, and Radare, using Cornucopia generated binaries, revealed various issues with these tools. Specifically, we found 263 crashes in Angr and one memory corruption issue in Idapro. Our differential testing on the analysis results revealed various semantic bugs in these tools. We also tested machine learning tools, Asmvec, Safe, and Debin, that claim to capture binary semantics and show that they perform poorly (For instance, Debin F1 score dropped to 12.9% from reported 63.1%) on Cornucopia generated binaries. In summary, our exhaustive evaluation shows that Cornucopia is an effective mechanism to generate binaries for testing binary analysis techniques effectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561152"}, {"primary_key": "1727693", "vector": [], "sparse_vector": [], "title": "A Scenario Distribution Model for Effective and Efficient Testing of Autonomous Driving Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While autonomous driving systems are expected to change future means of mobility and reduce road accidents, understanding intensive and complex traffic situations is essential to enable testing of such systems under realistic traffic conditions. Particularly, we need to cover more relevant driving scenarios in the test. However, we do not want to spend time and resources testing useless scenarios that never happen in the real road traffic. In this work, we propose a new model that defines the distribution of scenarios using TTC (Time-to-Collision) for the vehicle–pedestrian interactions at unsignalized crossings based on the traffic density. The scenario distribution can be used as an input for test scenario generation and selection. We validate the model using real traffic data collected in Sweden and the result indicates that the model is effective and consistently upholds the real distribution, especially for critical scenarios with TTC less than 3 seconds. We also demonstrate the use of the model by connecting it to the testing of an auto-braking function from the industry. As a first step, our contribution is a model that predicts the worst-case distribution of scenarios using TTC and provides a mandatory input for testing autonomous driving systems.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3563239"}, {"primary_key": "1727694", "vector": [], "sparse_vector": [], "title": "Evolving Ranking-Based Failure Proximities for Better Clustering in Fault Isolation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Failures that are not related to a specific fault can reduce the effectiveness of fault localization in multi-fault scenarios. To tackle this challenge, researchers and practitioners typically cluster failures (e.g., failed test cases) into several disjoint groups, with those caused by the same fault grouped together. In such a fault isolation process that requires input in a mathematical form, ranking-based failure proximity (R-proximity) is widely used to model failed test cases. In R-proximity, each failed test case is represented as a suspiciousness ranking list of program statements through a fingerprinting function (i.e., a risk evaluation formula, REF). Although many off-the-shelf REFs have been integrated into R-proximity, they were designed for single-fault localization originally. To the best of our knowledge, no REF has been developed to serve as a fingerprinting function of R-proximity in multi-fault scenarios. For better clustering failures in fault isolation, in this paper, we present a genetic programming-based framework along with a sophisticated fitness function, for evolving REFs with the goal of more properly representing failures in multi-fault scenarios. By using a small set of programs for training, we get a collection of REFs that can obtain good results applicable in a larger and more general scale of scenarios. The best one of them outperforms the state-of-the-art by 50.72% and 47.41% in faults number estimation and clustering effectiveness, respectively. Our framework is highly configurable for further use, and the evolved formulas can be directly applied in future failure representation tasks without any retraining.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556922"}, {"primary_key": "1727695", "vector": [], "sparse_vector": [], "title": "Generalizability of Code Clone Detection on CodeBERT.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Clemens-<PERSON>", "<PERSON>"], "summary": "Transformer networks such as CodeBERT already achieve outstanding results for code clone detection in benchmark datasets, so one could assume that this task has already been solved. However, code clone detection is not a trivial task. Semantic code clones, in particular, are challenging to detect.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561165"}, {"primary_key": "1727696", "vector": [], "sparse_vector": [], "title": "Finding Property Violations through Network Falsification: Challenges, Adaptations and Lessons Learned from OpenPilot.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Openpilot is an open source system to assist drivers by providing features like automated lane centering and adaptive cruise control. Like most systems for autonomous vehicles, Openpilot relies on a sophisticated deep neural network (DNN) to provide its functionality, one that is susceptible to safety property violations that can lead to crashes. To uncover such potential violations before deployment, we investigate the use of falsification, a form of directed testing that analyzes a DNN to generate an input that will cause a safety property violation. Specifically, we explore the application of a state-of-the-art falsifier to the DNN used in OpenPilot, which reflects recent trends in network design. Our investigation reveals the challenges in applying such falsifiers to real-world DNNs, conveys our engineering efforts to overcome such challenges, and showcases the potential of falsifiers to detect property violations and provide meaningful counterexamples. Finally, we summarize the lessons learned as well as the pending challenges for falsifiers to realize their potential on systems like OpenPilot.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559500"}, {"primary_key": "1727697", "vector": [], "sparse_vector": [], "title": "Automated Identification of Security-Relevant Configuration Settings Using NLP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To secure computer infrastructure, we need to configure all security-relevant settings. We need security experts to identify security-relevant settings, but this process is time-consuming and expensive. Our proposed solution uses state-of-the-art natural language processing to classify settings as security-relevant based on their description. Our evaluation shows that our trained classifiers do not perform well enough to replace the human security experts but can help them classify the settings. By publishing our labeled data sets and the code of our trained model, we want to help security experts analyze configuration settings and enable further research in this area.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559499"}, {"primary_key": "1727698", "vector": [], "sparse_vector": [], "title": "The Metamorphosis: Automatic Detection of Scaling Issues for Mobile Apps.", "authors": ["<PERSON><PERSON>", "Chunyang Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As the bridge between users and software, Graphical User Interface (GUI) is critical to the app accessibility. Scaling up the font or display size of GUI can help improve the visual impact, readability, and usability of an app, and is frequently used by the elderly and people with vision impairment. Yet this can easily lead to scaling issues such as text truncation, component overlap, which negatively influence the acquirement of the right information and the fluent usage of the app. Previous techniques for UI display issue detection and cross-platform inconsistency detection cannot work well for these scaling issues. In this paper, we propose an automated method, dVermin, for scaling issue detection, through detecting the inconsistency of a view under the default and a larger display scale. The evaluation result shows that dVermin achieves 97% precision and 97% recall in issue page detection, and 84% precision and 91% recall for issue view detection, outperforming two state-of-the-art baselines by a large margin. We also evaluate dV<PERSON>min with popular Android apps on F-droid, and successfully uncover 21 previously-undetected scaling issues with 20 of them being confirmed/fixed.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556935"}, {"primary_key": "1727699", "vector": [], "sparse_vector": [], "title": "Effectively Generating Vulnerable Transaction Sequences in Smart Contracts with Reinforcement Learning-guided Fuzzing.", "authors": ["Ji<PERSON>zhong Su", "Hong-Ning Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As computer programs run on top of blockchain, smart contracts have proliferated a myriad of decentralized applications while bringing security vulnerabilities, which may cause huge financial losses. Thus, it is crucial and urgent to detect the vulnerabilities of smart contracts. However, existing fuzzers for smart contracts are still inefficient to detect sophisticated vulnerabilities that require specific vulnerable transaction sequences to trigger. To address this challenge, we propose a novel vulnerability-guided fuzzer based on reinforcement learning, namely RLF, for generating vulnerable transaction sequences to detect such sophisticated vulnerabilities in smart contracts. In particular, we firstly model the process of fuzzing smart contracts as a Markov decision process to construct our reinforcement learning framework. We then creatively design an appropriate reward with consideration of both vulnerability and code coverage so that it can effectively guide our fuzzer to generate specific transaction sequences to reveal vulnerabilities, especially for the vulnerabilities related to multiple functions. We conduct extensive experiments to evaluate RLF's performance. The experimental results demonstrate that our RLF outperforms state-of-the-art vulnerability-detection tools (e.g., detecting 8%-69% more vulnerabilities within 30 minutes).", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560429"}, {"primary_key": "1727700", "vector": [], "sparse_vector": [], "title": "Constructing a System Knowledge Graph of User Tasks and Failures from Bug Reports to Support Soap Opera Testing.", "authors": ["Yanqi Su", "<PERSON><PERSON>ing <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qinghua Lu"], "summary": "Exploratory testing is an effective testing approach which leverages the tester's knowledge and creativity to design test cases to provoke and recognize failures at the system level from the end user's perspective. Although some principles and guidelines have been proposed to guide exploratory testing, there are no effective tools for automatic generation of exploratory test scenarios (a.k.a soap opera tests). Existing test generation techniques rely on specifications, program differences and fuzzing, which are not suitable for exploratory test generation. In this paper, we propose to leverage the scenario and oracle knowledge in bug reports to generate soap opera test scenarios. We develop open information extraction methods to construct a system knowledge graph (KG) of user tasks and failures from the steps to reproduce, expected results and observed results in bug reports. We construct a proof-of-concept KG from 25,939 bugs of the Firefox browser. Our evaluation shows the constructed KG is of high quality. Based on the KG, we create soap opera test scenarios by combining the scenarios of relevant bugs, and develop a web tool to present the created test scenarios and support exploratory testing. In our user study, 5 users find 18 bugs from 5 seed bugs in 2 hours using our tool, while the control group finds only 5 bugs based on the recommended similar bugs.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556967"}, {"primary_key": "1727701", "vector": [], "sparse_vector": [], "title": "Prioritized Constraint-Aided Dynamic Partial-Order Reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thread alternation aggravates the difficulty of concurrent program verification since the number of traces to be explored grows rapidly as the scale of a concurrent program increases. Partial-Order Reduction (POR) techniques alleviate the trace-space explosion problem by partitioning the traces into different equivalent classes. However, due to the coarse dependency approximation of transitions, there are still a large number of redundant traces explored throughout the verification. In this paper, a symbolic approach, namely Prioritized Constraint-Aided Dynamic Partial-Order Reduction (PC-DPOR), is proposed to reduce the redundant traces. Specifically, a constrained dependency graph is presented to refine dependencies between transitions, and the exploration of isolated transitions in the graph is prioritized to reduce redundant equivalent traces. Further, we utilize the generated constraints to dynamically detect whether the enabled transitions at the given reachable states are dependent, and thereby to overcome the inherent imprecision of the traditional dependence over-approximation. We have implemented the proposed approach as an extension of CPAchecker by utilizing BDDs as the representation of state sets. Experimental results show that our approach can effectively reduce the time and memory consumption for verifying concurrent programs. In particular, the number of explored states is reduced to 8.62% on average.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561159"}, {"primary_key": "1727702", "vector": [], "sparse_vector": [], "title": "Identification and Mitigation of Gender Biases to Promote Diversity and Inclusion among Open Source Communities.", "authors": ["<PERSON><PERSON>"], "summary": "Contemporary software development organizations are dominated by straight males and lack diversity. As a result, people from other demographic such as women and LGBTQ+ often encounter bias, sexism, and misogyny. Due to negative experiences, many women switch careers. Therefore, biases pose barriers to promote diversity and inclusion. To get benefits from diverse pools of talents and reduce the attrition rate of minorities, we need to identify the degree and effect of various biases and develop mitigation strategies. Therefore, my dissertation study aims at promoting diversity and inclusion among software development organizations by identifying the manifestation, magnitude, and frequency of various gender biases. For this purpose, I plan to investigate i) the effect of gender of the contributors in the code review process of Free/Libre Open Source Software (FLOSS) projects, ii) the frequency of different dimensions of gender bias and their effect, and iii) develop a tool to identify sexist and misogynistic and derogatory (SMD) texts.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559571"}, {"primary_key": "1727703", "vector": [], "sparse_vector": [], "title": "Identifying Sexism and Misogyny in Pull Request Comments.", "authors": ["<PERSON><PERSON>"], "summary": "Being extremely dominated by men, software development organizations lack diversity. People from other groups often encounter sexist, misogynistic, and discriminatory (SMD) speech during communication. To identify SMD contents, I aim to build an automatic misogyny identification (AMI) tool for the domain of software developers. On this goal, I built a dataset of 10,138 pull request comments mined from Github based on a keyword-based selection, followed by manual validation. Using ten-fold cross-validation, I evaluated ten machine learning algorithms for automatic identification. The best performing model achieved 80% precision, 67.07% recall, 72.5% f-score, and 95.96% accuracy.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559515"}, {"primary_key": "1727704", "vector": [], "sparse_vector": [], "title": "Mining Android API Usage to Generate Unit Test Cases for Pinpointing Compatibility Issues.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite being one of the largest and most popular projects, the official Android framework has only provided test cases for less than 30% of its APIs. Such a poor test case coverage rate has led to many compatibility issues that can cause apps to crash at runtime on specific Android devices, resulting in poor user experiences for both apps and the Android ecosystem. To mitigate this impact, various approaches have been proposed to automatically detect such compatibility issues. Unfortunately, these approaches have only focused on detecting signature-induced compatibility issues (i.e., a certain API does not exist in certain Android versions), leaving other equally important types of compatibility issues unresolved. In this work, we propose a novel prototype tool, JUnitTestGen, to fill this gap by mining existing Android API usage to generate unit test cases. After locating Android API usage in given real-world Android apps, JUnitTestGen performs inter-procedural backward data-flow analysis to generate a minimal executable code snippet (i.e., test case). Experimental results on thousands of real-world Android apps show that JUnitTestGen is effective in generating valid unit test cases for Android APIs. We show that these generated test cases are indeed helpful for pinpointing compatibility issues, including ones involving semantic code changes.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561151"}, {"primary_key": "1727705", "vector": [], "sparse_vector": [], "title": "Static Type Recommendation for Python.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, Python has adopted optional type annotation to support type checking and program documentation. However, to enjoy the benefits, developers have to manually write type annotations, which is recognized to be a time-consuming task. To alleviate human efforts on manual type annotation, machine-learning-based approaches have been proposed to recommend types based on code features. However, they suffer from the correctness problem, i.e., the recommended types cannot pass type checking. To address the correctness problem of the machine-learning-based approaches, in this paper, we present a static type recommendation approach, named Stray. Stray can recommend types correctly. We evaluate Stray by comparing it against four state-of-art type recommendation approaches, and find that <PERSON>ray outperforms these baselines by over 30% absolute improvement in both precision and recall.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561150"}, {"primary_key": "1727706", "vector": [], "sparse_vector": [], "title": "Snapshot Metrics Are Not Enough: Analyzing Software Repositories with Longitudinal Metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shilpika", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software metrics capture information about software development processes and products. These metrics support decision-making, e.g., in team management or dependency selection. However, existing metrics tools measure only a snapshot of a software project. Little attention has been given to enabling engineers to reason about metric trends over time—longitudinal metrics that give insight about process, not just product. In this work, we present PRIME (PRocess MEtrics), a tool to compute and visualize process metrics. The currently-supported metrics include productivity, issue density, issue spoilage, and bus factor. We illustrate the value of longitudinal data and conclude with a research agenda. The tool's demo video can be watched at https://bit.ly/ase2022-prime. Source code can be found at https://github.com/SoftwareSystemsLaboratory/prime.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559517"}, {"primary_key": "1727707", "vector": [], "sparse_vector": [], "title": "Towards Understanding Third-party Library Dependency in C/C++ Ecosystem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Third-party libraries (TPLs) are frequently reused in software to reduce development cost and the time to market. However, external library dependencies may introduce vulnerabilities into host applications. The issue of library dependency has received considerable critical attention. Many package managers, such as Maven, Pip, and NPM, are proposed to manage TPLs. Moreover, a significant amount of effort has been put into studying dependencies in language ecosystems like Java, Python, and JavaScript except C/C++. Due to the lack of a unified package manager for C/C++, existing research has only few understanding of TPL dependencies in the C/C++ ecosystem, especially at large scale. Towards understanding TPL dependencies in the C/C++ecosystem, we collect existing TPL databases, package management tools, and dependency detection tools, summarize the dependency patterns of C/C++ projects, and construct a comprehensive and precise C/C++ dependency detector. Using our detector, we extract dependencies from a large-scale database containing 24K C/C++ repositories from GitHub. Based on the extracted dependencies, we provide the results and findings of an empirical study, which aims at understanding the characteristics of the TPL dependencies. We further discuss the implications to manage dependency for C/C++ and the future research directions for software engineering researchers and developers in fields of library development, software composition analysis, and C/C++package manager.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560432"}, {"primary_key": "1727708", "vector": [], "sparse_vector": [], "title": "SA4U: Practical Static Analysis for Unit Type Error Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Feng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unit type errors, where values with physical unit types (e.g., meters, hours) are used incorrectly in a computation, are common in today's unmanned aerial system (UAS) firmware. Recent studies show that unit type errors represent over 10% of bugs in UAS firmware. Moreover, the consequences of unit type errors are severe. Over 30% of unit type errors cause UAS crashes. This paper proposes SA4U: a practical system for detecting unit type errors in real-world UAS firmware. SA4U requires no modifications to firmware or developer annotations. It deduces the unit types of program variables by analyzing simulation traces and protocol definitions. SA4U uses the deduced unit types to identify when unit type errors occur. SA4U is effective: it identified 14 previously undetected bugs in two popular open-source firmware (ArduPilot & PX4.)", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556937"}, {"primary_key": "1727709", "vector": [], "sparse_vector": [], "title": "Learning to Construct Better Mutation Faults.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mutation faults are the core of mutation testing and have been widely used in many other software testing and debugging tasks. Hence, constructing high-quality mutation faults is critical. There are many traditional mutation techniques that construct syntactic mutation faults based on a limited set of manually-defined mutation operators. To improve them, the state-of-the-art deep-learning (DL) based technique (i.e., DeepMutation) has been proposed to construct mutation faults by learning from real faults via classic sequence-to-sequence neural machine translation (NMT). However, its performance is not satisfactory since it cannot ensure syntactic correctness of constructed mutation faults and suffers from the effectiveness issue due to the huge search space and limited features by simply treating each targeted method as a token stream.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556949"}, {"primary_key": "1727710", "vector": [], "sparse_vector": [], "title": "Is this Change the Answer to that Problem?: Correlating Descriptions of Bug and Code Changes for Evaluating Patch Correctness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tegawendé F. Bissyandé"], "summary": "In this work, we propose a novel perspective to the problem of patch correctness assessment: a correct patch implements changes that \"answer\" to a problem posed by buggy behaviour. Concretely, we turn the patch correctness assessment into a Question Answering problem. To tackle this problem, our intuition is that natural language processing can provide the necessary representations and models for assessing the semantic correlation between a bug (question) and a patch (answer). Specifically, we consider as inputs the bug reports as well as the natural language description of the generated patches. Our approach, <PERSON>ua<PERSON>in, first considers state of the art commit message generation models to produce the relevant inputs associated to each generated patch. Then we leverage a neural network architecture to learn the semantic correlation between bug reports and commit messages. Experiments on a large dataset of 9135 patches generated for three bug datasets (Defects4j, Bugs.jar and Bears) show that Quatrain can achieve an AUC of 0.886 on predicting patch correctness, and recalling 93% correct patches while filtering out 62% incorrect patches. Our experimental results further demonstrate the influence of inputs quality on prediction performance. We further perform experiments to highlight that the model indeed learns the relationship between bug reports and code change descriptions for the prediction. Finally, we compare against prior work and discuss the benefits of our approach.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556914"}, {"primary_key": "1727711", "vector": [], "sparse_vector": [], "title": "Generating Critical Test Scenarios for Autonomous Driving Systems via Influential Behavior Patterns.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yan <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Autonomous Driving Systems (ADSs) are safety-critical, and must be fully tested before being deployed on real-world roads. To comprehensively evaluate the performance of ADSs, it is essential to generate various safety-critical scenarios. Most of existing studies assess ADSs either by searching high-dimensional input space, or using simple and pre-defined test scenarios, which are not efficient or not adequate. To better test ADSs, this paper proposes to automatically generate safety-critical test scenarios for ADSs by influential behavior patterns, which are mined from real traffic trajectories. Based on influential behavior patterns, a novel scenario generation technique, CRISCO, is presented to generate safety-critical scenarios for ADSs testing. CRISCO assigns participants to perform influential behaviors to challenge the ADS. It generates different test scenarios by solving trajectory constraints, and improves the challenge of those non-critical scenarios by adding participants' behavior from influential behavior patterns incrementally. We demonstrate CRISCO on an industrial-grade ADS platform, Baidu Apollo. The experiment results show that our approach can effectively and efficiently generate critical scenarios to crash ADS, and it exposes 13 distinct types of safety violations in 12 hours. It also outperforms two state-of-art ADS testing techniques by exposing more 5 distinct types of safety violations on the same roads.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560430"}, {"primary_key": "1727712", "vector": [], "sparse_vector": [], "title": "Property-Based Automated Repair of DeFi Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Programming errors enable security attacks on smart contracts, which are used to manage large sums of financial assets. Automated program repair (APR) techniques aim to reduce developers' burden of manually fixing bugs by automatically generating patches for a given issue. Existing APR tools for smart contracts focus on mitigating typical smart contract vulnerabilities rather than violations of functional specification. However, in decentralized financial (DeFi) smart contracts, the inconsistency between intended behavior and implementation translates into the deviation from the underlying financial model, resulting in monetary losses for the application and its users. In this work, we propose DeFinery—a technique for automated repair of a smart contract that does not satisfy a user-defined correctness property. To explore a larger set of diverse patches while providing formal correctness guarantees w.r.t. the intended behavior, we combine search-based patch generation with semantic analysis of an original program for inferring its specification. Our experiments in repairing 9 real-world and benchmark smart contracts prove that DeFinery efficiently generates high-quality patches that cannot be found by other existing tools.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559560"}, {"primary_key": "1727713", "vector": [], "sparse_vector": [], "title": "Simulating cyber security management: A gamified approach to executive decision making.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Executive managers are not all equipped with the cyber security expertise necessary to enable them to make business decisions that accurately represent the status and needs of the cyber security side of the business. Unfortunately, the lack of understanding between the business and cyber security domains contribute to structurally endorsed vulnerabilities within a business context, where either the business needs were considered without understanding the impact on cyber security, or alternatively, the cyber security needs were considered without fully understanding the impact this would have on the business strategy and financial stability. To combat this dilemma, a gamified approach to cyber security training for executives is proposed as a solution to not only minimise the realisation of cyber vulnerabilities within a business context, but also to improve business outcomes that are supported by cyber security measures. We developed a serious game software platform, Aurelius, to simulate an executive decision maker's role in managing the everyday cyber security investment decisions, and linking that to business metrics to incorporate the business and cyber security understanding. Our game includes simulated cyber security attacks that would require the executive decision maker (the player) to respond appropriately. The algorithms underpinning our simulated cyber security game are a product of a complex systems approach, as this most accurately models an executive's experience. In our design, we set up Aurelius to fulfil eight of the nine criteria specified for a state of the art serious game in the cyber security domain.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561148"}, {"primary_key": "1727714", "vector": [], "sparse_vector": [], "title": "Detecting Build Conflicts in Software Merge for Java Programs via Static Analysis.", "authors": ["Sheikh <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In software merge, the edits from different branches can textually overlap (i.e., textual conflicts) or cause build and test errors (i.e., build and test conflicts), jeopardizing programmer productivity and software quality. Existing tools primarily focus on textual conflicts; few tools detect higher-order conflicts (i.e., build and test conflicts). However, existing detectors of build conflicts are limited. Due to their heavy usage of automatic build, current detectors (e.g., Crystal) only report build errors instead of identifying the root causes; developers have to manually locate conflicting edits. These detectors only help when the branches-to-merge have no textual conflict.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556950"}, {"primary_key": "1727715", "vector": [], "sparse_vector": [], "title": "Taming Multi-Output Recommenders for Software Engineering.", "authors": ["<PERSON>"], "summary": "Recommender systems are a valuable tool for software engineers. For example, they can provide developers with a ranked list of files likely to contain a bug, or multiple auto-complete suggestions for a given method stub. However, the way these recommender systems interact with developers is often rudimentary—a long list of recommendations only ranked by the model's confidence. In this vision paper, we lay out our research agenda for re-imagining how recommender systems for software engineering communicate their insights to developers. When issuing recommendations, our aim is to recommend diverse rather than redundant solutions and present them in ways that highlight their differences. We also want to allow for seamless and interactive navigation of suggestions while striving for holistic end-to-end evaluations. By doing so, we believe that recommender systems can play an even more important role in helping developers write better software.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559557"}, {"primary_key": "1727716", "vector": [], "sparse_vector": [], "title": "reformulator: Automated Refactoring of the N+1 Problem in Database-Backed Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An Object-Relational Mapping (ORM) provides an object-oriented interface to a database and facilitates the development of database-backed applications. In an ORM, programmers do not need to write queries in a separate query language such as SQL, they instead write ordinary method calls that are mapped by the ORM to database queries. This added layer of abstraction hides the significant performance cost of database operations, and misuse of ORMs can lead to far more queries being generated than necessary. Of particular concern is the infamous \"N+1 problem\", where an initial query yields N results that are used to issue N subsequent queries. This anti-pattern is prevalent in applications that use ORMs, as it is natural to iterate over collections in object-oriented languages. However, iterating over data that originates from a database and calling an ORM method in each iteration may result in suboptimal performance. In such cases, it is often possible to reduce the number of round-trips to the database by issuing a single, larger query that fetches all desired results at once.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556911"}, {"primary_key": "1727717", "vector": [], "sparse_vector": [], "title": "Towards Improving Code Review Effectiveness Through Task Automation.", "authors": ["<PERSON><PERSON>"], "summary": "Modern code review (MCR) is a widely adopted software quality assurance practice in the contemporary software industry. As software developers spend significant amounts of time on MCR activities, even a small improvement in MCR effectiveness will incur significant savings. As most of the MCR activities are heavily dependent on manual work, there are significant opportunities to improve effectiveness through tool support. To address the challenges, the primary objective of my proposed dissertation is to improve the effectiveness of modern code reviews with the automation of reviewer selection and bug identification. On this goal, I propose three studies. The first study aims to investigate the notion of useful MCRs and factors influencing MCR usefulness. The second study aims to develop a reviewer recommendation system that leverages a reviewer's prior history of providing useful feedback under similar contexts. Finally, the third study aims to improve the effectiveness of static analysis tools by leveraging bugs identified during prior reviews.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559565"}, {"primary_key": "1727718", "vector": [], "sparse_vector": [], "title": "Detecting Blocking Errors in Go Programs using Localized Abstract Interpretation.", "authors": ["<PERSON><PERSON>", "Georgian-<PERSON>", "<PERSON>"], "summary": "Channel-based concurrency is a widely used alternative to shared-memory concurrency but is difficult to use correctly. Common programming errors may result in blocked threads that wait indefinitely. Recent work exposes this as a considerable problem in Go programs and shows that many such errors can be detected automatically using SMT encoding and dynamic analysis techniques.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561154"}, {"primary_key": "1727719", "vector": [], "sparse_vector": [], "title": "WebMonitor: Verification of Web User Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Application development for the modern Web involves sophisticated engineering workflows which include user interface aspects. Those involve Web elements typically created with HTML/CSS markup and JavaScript-like languages, yielding Web documents. WebMonitor leverages requirements formally specified in a logic able to capture both the layout of visual components as well as how they change over time, as a user interacts with them. Then, requirements are verified upon arbitrary web pages, allowing for automated support for a wide set of use cases in interaction testing and simulation. We position WebMonitor within a developer workflow, where in case of a negative result, a visual counterexample is returned. The monitoring framework we present follows a black-box approach, and as such is independent of the underlying technologies a Web application may be developed with, as well as the browser and operating system used.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559538"}, {"primary_key": "1727720", "vector": [], "sparse_vector": [], "title": "FlexType: A Plug-and-Play Framework for Type Inference Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Types in TypeScript play an important role in the correct usage of variables and APIs. Type errors such as variable or function misuse can be avoided with explicit type annotations. In this work, we introduce FlexType, an IDE extension that can be used on both JavaScript and TypeScript to infer types in an interactive or automatic fashion. We perform experiments with FlexType in JavaScript to determine how many types FlexType could resolve if it were to be used to migrate top JavaScript projects to TypeScript. FlexType is able to annotate 56.69% of all types with high precision and confidence including native and imported types from modules. In addition to the automatic inference, we believe the interactive Visual Studio Code extension is inherently useful in both TypeScript and JavaScript especially when resolving types is taxing for the developer.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559527"}, {"primary_key": "1727721", "vector": [], "sparse_vector": [], "title": "Test-Driven Multi-Task Learning with Functionally Equivalent Code Transformation for Neural Code Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automated code generation is a longstanding challenge in both communities of software engineering and artificial intelligence. Currently, some works have started to investigate the functional correctness of code generation, where a code snippet is considered correct if it passes a set of test cases. However, most existing works still model code generation as text generation without considering program-specific information, such as functionally equivalent code snippets and test execution feedback. To address the above limitations, this paper proposes a method combining program analysis with deep learning for neural code generation, where functionally equivalent code snippets and test execution feedback will be considered at the training stage. Concretely, we firstly design several code transformation heuristics to produce different variants of the code snippet satisfying the same functionality. In addition, we employ the test execution feedback and design a test-driven discriminative task to train a novel discriminator, aiming to let the model distinguish whether the generated code is correct or not. The preliminary results on a newly published dataset demonstrate the effectiveness of our proposed framework for code generation. Particularly, in terms of the [email protected] metric, we achieve 8.81 and 11.53 gains compared with CodeGPT and CodeT5, respectively.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559549"}, {"primary_key": "1727722", "vector": [], "sparse_vector": [], "title": "ADEPT: A Testing Platform for Simulated Autonomous Driving.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Ma"], "summary": "Effective quality assurance methods for autonomous driving systems ADS have attracted growing interests recently. In this paper, we report a new testing platform ADEPT, aiming to provide practically realistic and comprehensive testing facilities for DNN-based ADS. ADEPT is based on the virtual simulator CARLA and provides numerous testing facilities such as scene construction, ADS importation, test execution and recording, etc. In particular, ADEPT features two distinguished test scenario generation strategies designed for autonomous driving. First, we make use of real-life accident reports from which we leverage natural language processing to fabricate abundant driving scenarios. Second, we synthesize physically-robust adversarial attacks by taking the feedback of ADS into consideration and thus are able to generate closed-loop test scenarios. The experiments confirm the efficacy of the platform.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559528"}, {"primary_key": "1727723", "vector": [], "sparse_vector": [], "title": "Learning to Synthesize Relational Invariants.", "authors": ["<PERSON><PERSON> Wang", "<PERSON>"], "summary": "We propose a method for synthesizing invariants that can help verify relational properties over two programs or two different executions of a program. Applications of such invariants include verifying functional equivalence, non-interference security, and continuity properties. Our method generates invariant candidates using syntax guided synthesis (SyGuS) and then filters them using an SMT-solver based verifier, to ensure they are both inductive invariants and sufficient for verifying the property at hand. To improve performance, we propose two learning based techniques: a logical reasoning (LR) technique to determine which part of the search space can be pruned away, and a reinforcement learning (RL) technique to determine which part of the search space to prioritize. Our experiments on a diverse set of relational verification benchmarks show that our learning based techniques can drastically reduce the search space and, as a result, they allow our method to generate invariants of a higher quality in much shorter time than state-of-the-art invariant synthesis techniques.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556942"}, {"primary_key": "1727724", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Numerical Bugs in Deep Learning Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ming Yan"], "summary": "The task of a deep learning (DL) program is to train a model with high precision and apply it to different scenarios. A DL program often involves massive numerical calculations. Therefore, the robustness and stability of the numerical calculations are dominant in the quality of DL programs. Indeed, numerical bugs are common in DL programs, producing NaN (Not-a-Number) and INF (Infinite). A numerical bug may render the DL models inaccurate, causing the DL applications unusable. In this work, we conduct the first empirical study on numerical bugs in DL programs by analyzing the programs implemented on the top of two popular DL libraries (i.e., TensorFlow and PyTorch). Specifically, We collect a dataset of 400 numerical bugs in DL programs. Then, we classify these numerical bugs into nine categories based on their root causes and summarize two findings. Finally, we provide the implications of our study on detecting numerical bugs in DL programs.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559561"}, {"primary_key": "1727725", "vector": [], "sparse_vector": [], "title": "smartPip: A Smart Approach to Resolving Python Dependency Conflict Issues.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guoqing Li"], "summary": "As one of the representative software ecosystems, PyPI, together with the Python package management tool pip, greatly facilitates Python developers to automatically manage the reuse of third-party libraries, thus saving development time and cost. Despite its great success in practice, a recent empirical study revealed the risks of dependency conflict (DC) issues and then summarized the characteristics of DC issues. However, the dependency resolving strategy, which is the foundation of the prior study, has evolved to a new one, namely the backtracking strategy. To understand how the evolution of this dependency resolving strategy affects the prior findings, we conducted an empirical study to revisit the characteristics of DC issues under the new strategy. Our study revealed that, of the two previously discovered DC issue manifestation patterns, one has significantly changed (Pattern A), while the other remained the same (Pattern B). We also observed, the resolving strategy for the DC issues of Pattern A suffers from the efficiency issue, while the one for the DC issues of Pattern B would lead to a waste of time and space. Based on our findings, we propose a tool smartPip to overcome the limitations of the resolving strategies. To resolve the DC issues of Pattern A, instead of iteratively verifying each candidate dependency library, we leverage a pre-built knowledge base of library dependencies to collect version constraints for concerned libraries, and then convert the version constraints into the SMT expressions for solving. To resolve the DC issues of Pattern B, we improve the existing virtual environment solution to reuse the local libraries as far as possible. Finally, we evaluated smartPip in three benchmark datasets of open source projects. The results showed that, smartPip can outperform the existing Python package management tools including pip with the new strategy and Conda in resolving DC issues of Pattern A, and achieve 1.19X - 1.60X speedups over the best baseline approach. Compared with the built-in Python virtual environment (venv), smartPip reduced 34.55% - 80.26% of storage space and achieved up to 2.26X - 6.53X speedups in resolving the DC issues of Pattern B.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560437"}, {"primary_key": "1727726", "vector": [], "sparse_vector": [], "title": "Verifying Game Logic in Unreal Engine 5 Blueprint Visual Scripting System Using Model Checking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper examines modeling methods for applying model checking to game programs created with Unreal Engine 5 Blueprint scripting system (hereinafter UE5 Blueprint). UE5 Blueprint can visually describe game logic by combining various processing nodes, but as the size of the game grows, it becomes more difficult to find and fix bugs that prevent the game from progressing. In this paper, a formal verification technique, model checking, is used to automatically detect game logic bugs. We convert a game program created in UE5 Blueprint into an input model for the model-checker NuSMV to achieve verification by NuSMV. The proposed framework enables the automatic generation of models by formally defining the semantics of nodes. We also propose methods for data flow optimization and abstraction of variable domain for the purpose of reducing the number of states in the model. We applied the proposed method to a blueprint containing a typical flag management bug and confirmed that the bug was correctly detected by NuSMV. Furthermore, we show that the number of states can be significantly reduced by the optimization and abstraction.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560505"}, {"primary_key": "1727727", "vector": [], "sparse_vector": [], "title": "Automatically Tagging the &quot;AAA&quot; Pattern in Unit Test Cases Using Machine Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Tingting Yu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The AAA pattern, i.e. the Arrangement, Action, and Assertion, is a common and nature layout to create a test case. Following this pattern in test cases may benefit comprehension, debugging, and maintenance. The AAA structure of real-life test cases may not be explicit due to its high complexity. Manually labeling AAA statements in test cases is tedious. Thus, an automated approach for labeling AAA statements in existing test cases could benefit new developers and projects that practice collective code ownership and test driven development.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559510"}, {"primary_key": "1727728", "vector": [], "sparse_vector": [], "title": "Execution Path Detection through Dynamic Analysis in Black-Box Testing Environments.", "authors": ["<PERSON>"], "summary": "Path coverage is the process of measuring the fraction of execution paths that are taken during run-time in a software by a given set of inputs. It is commonly used to assess the stability, security, and functionality of an application; therefore, this is closely associated with software testing. Path coverage requires knowledge of the software's source code (white-box testing), specifically the software's potential execution paths; however, the problem becomes more challenging when the source code is not available and path coverage must be done using only the software's binary code. This can occur if the software is a product, the software is a legacy system, or the source code is not available (e.g. contracted software or permission-less).", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559506"}, {"primary_key": "1727729", "vector": [], "sparse_vector": [], "title": "Software Evolution Management with Differential Facts.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Many techniques have been proposed to mine knowledge from software artefacts and solve software evolution management tasks. To promote effective reusing of those knowledge, we propose a unified format, differential facts, to represent software changes across versions as well as various relations within each version, such as call graphs. Based on queryable formats, differential facts can be manipulated to implement complex evolution management tasks. Since facts once extracted can be shared among different tasks, the reusability brings improvements to overall performance. We validate the technique and show its benefits of being efficient, flexible, and easy to implement, with several applications, including semantic history slicing, regression test selection, documentation error detection and client-specific usage patterns discovery.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559513"}, {"primary_key": "1727730", "vector": [], "sparse_vector": [], "title": "Accelerating Build Dependency Error Detection via Virtual Build.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gang Fan", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Build scripts play an important role in transforming the source code into executable artifacts. However, the development of build scripts is typically error-prone. As one kind of the most prevalent errors in build scripts, the dependency-related errors, including missing dependencies and redundant dependencies, draw the attention of many researchers. A variety of build dependency analysis techniques have been proposed to tackle them. Unfortunately, most of these techniques, even the state-of-the-art ones, suffer from efficiency issues due to the expensive cost of monitoring the complete build process to build dynamic dependencies. Especially for large-scale projects, such the cost would not be affordable.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556930"}, {"primary_key": "1727731", "vector": [], "sparse_vector": [], "title": "Detecting Semantic Code Clones by Building AST-based Markov Chains Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Deqing Zou", "<PERSON>"], "summary": "Code clone detection aims to find functionally similar code fragments, which is becoming more and more important in the field of software engineering. Many code clone detection methods have been proposed, among which tree-based methods are able to handle semantic code clones. However, these methods are difficult to scale to big code due to the complexity of tree structures. In this paper, we design Amain, a scalable tree-based semantic code clone detector by building Markov chains models. Specifically, we propose a novel method to transform the original complex tree into simple Markov chains and measure the distance of all states in these chains. After obtaining all distance values, we feed them into a machine learning classifier to train a code clone detector. To examine the effectiveness of Amain, we evaluate it on two widely used datasets namely Google Code Jam and BigCloneBench. Experimental results show that Amain is superior to nine state-of-the-art code clone detection tools (i.e., SourcererCC, RtvNN, Deckard, ASTNN, TBCNN, CDLH, FCCA, DeepSim, and SCDetector).", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560426"}, {"primary_key": "1727732", "vector": [], "sparse_vector": [], "title": "Understanding and Predicting Docker Build Duration: An Empirical Study of Containerized Workflow of OSS Projects.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Docker building is a critical component of containerized workflow, which automates the process by which sources are packaged and transformed into container images. If not run properly, Docker builds can bring long durations (i.e., slow builds), which increases the cost in human and computing resources, and thus inevitably affect the software development. However, the current status and remedy for the duration cost in Docker builds remain unclear and need an in-depth study. To fill this gap, this paper provides the first empirical investigation on 171,439 Docker builds from 5,833 open source software (OSS) projects. Starting with an exploratory study, the Docker build durations can be characterized in real-world projects, and the developers' perceptions of slow builds are obtained via a comprehensive survey. Driven by the results of our exploratory study, we propose a prediction modeling of Docker build duration, leveraging 27 handcrafted features from build-related context and configuration and 8 regression algorithms for the prediction task. Our results demonstrate that Random Forest model provides the superior performance with a <PERSON><PERSON><PERSON>'s correlation of 0.781, outperforming the baseline random model by 82.9% in RMSE, 90.6% in MAE, and 94.4% in MAPE, respectively. The implications of this study will facilitate research and assist practitioners in improving the Docker build process.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556940"}, {"primary_key": "1727733", "vector": [], "sparse_vector": [], "title": "Repairing Failure-inducing Inputs with Input Reflection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Changsheng Sun", "<PERSON>", "<PERSON>"], "summary": "Trained with a sufficiently large training and testing dataset, Deep Neural Networks (DNNs) are expected to generalize. However, inputs may deviate from the training dataset distribution in real deployments. This is a fundamental issue with using a finite dataset, which may lead deployed DNNs to mis-predict in production.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556932"}, {"primary_key": "1727734", "vector": [], "sparse_vector": [], "title": "Boosting the Revealing of Detected Violations in Deep Learning Testing: A Diversity-Guided Method.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yin", "<PERSON><PERSON><PERSON>"], "summary": "Due to the ability to bypass the oracle problem, Metamorphic Testing (MT) has been a popular technique to test deep learning (DL) software. However, no work has taken notice of the prioritization for Metamorphic test case Pairs (MPs), which is quite essential and beneficial to the effectiveness of MT in DL testing. When the fault-sensitive MPs apt to trigger violations and expose defects are not prioritized, the revealing of some detected violations can be greatly delayed or even missed to conceal critical defects. In this paper, we propose the first method to prioritize the MPs for DL software, so as to boost the revealing of detected violations in DL testing. Specifically, we devise a new type of metric to measure the execution diversity of DL software on MPs based on the distribution discrepancy of the neuron outputs. The fault-sensitive MPs are next prioritized based on the devised diversity metric. Comprehensive evaluation results show that the proposed prioritization method and diversity metric can effectively prioritize the fault-sensitive MPs, boost the revealing of detected violations, and even facilitate the selection and design of the effective Metamorphic Relations for the image classification DL software.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556919"}, {"primary_key": "1727735", "vector": [], "sparse_vector": [], "title": "Scrutinizing Privacy Policy Compliance of Virtual Personal Assistant Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guangdong Bai"], "summary": "A large number of functionality-rich and easily accessible applications have become popular among various virtual personal assistant (VPA) services such as Amazon Alexa. VPA applications (or VPA apps for short) are accompanied by a privacy policy document that informs users of their data handling practices. These documents are usually lengthy and complex for users to comprehend, and developers may intentionally or unintentionally fail to comply with them. In this work, we conduct the first systematic study on the privacy policy compliance issue of VPA apps. We develop Skipper, which targets Amazon Alexa skills. It automatically depicts the skill into the declared privacy profile by analyzing their privacy policy documents with Natural Language Processing (NLP) and machine learning techniques, and derives the behavioral privacy profile of the skill through a black-box testing. We conduct a large-scale analysis on all skills listed on Alexa store, and find that a large number of skills suffer from the privacy policy noncompliance issues.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560416"}, {"primary_key": "1727736", "vector": [], "sparse_vector": [], "title": "Studying and Understanding the Tradeoffs Between Generality and Reduction in Software Debloating.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing approaches for program debloating often use a usage profile, typically provided as a set of inputs, for identifying the features of a program to be preserved. Specifically, given a program and a set of inputs, these techniques produce a reduced program that behaves correctly for these inputs. Focusing only on reduction, however, would typically result in programs that are overfitted to the inputs used for debloating. For this reason, another important factor to consider in the context of debloating is generality, which measures the extent to which a debloated program behaves correctly also for inputs that were not in the initial usage profile. Unfortunately, most evaluations of existing debloating approaches only consider reduction, thus providing partial information on the effectiveness of these approaches. To address this limitation, we perform an empirical evaluation of the reduction and generality of 4 debloating techniques, 3 state-of-the-art ones, and a baseline, on a set of 25 programs and different sets of inputs for these programs. Our results show that these approaches can indeed produce programs that are overfitted to the inputs used and have low generality. Based on these results, we also propose two new augmentation approaches and evaluate their effectiveness. The results of this additional evaluation show that these two approaches can help improve program generality without significantly affecting size reduction. Finally, because different approaches have different strengths and weaknesses, we also provide guidelines to help users choose the most suitable approach based on their specific needs and context.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556970"}, {"primary_key": "1727737", "vector": [], "sparse_vector": [], "title": "Insight: Exploring Cross-Ecosystem Vulnerability Impacts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vulnerabilities, referred to as CLV issues, are induced by cross-language invocations of vulnerable libraries. Such issues greatly increase the attack surface of Python/Java projects due to their pervasive use of C libraries. Existing Python/Java build tools in PyPI and Maven ecosystems fail to report the dependency on vulnerable libraries written in other languages such as C. CLV issues are easily missed by developers. In this paper, we conduct the first empirical study on the status quo of CLV issues in PyPI and Maven ecosystems. It is found that 82,951 projects in these ecosystems are directly or indirectly dependent on libraries compiled from the C project versions that are identified to be vulnerable in CVE reports. Our study arouses the awareness of CLV issues in popular ecosystems and presents related analysis results.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556921"}, {"primary_key": "1727738", "vector": [], "sparse_vector": [], "title": "A Comprehensive Evaluation of Android ICC Resolution Techniques.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Inter-component communication (ICC) is a widely used mechanism in mobile apps, which enables message-based control flow transferring and data passing between Android components. Effective ICC resolution requires precisely identifying entry points, analyzing data values of ICC fields, modeling related framework APIs, etc. Due to various control-flow- and data-flow-related characteristics involved and the lack of oracles for real-world apps, the comprehensive evaluation of ICC resolution techniques is challenging. To fill this gap, we collect multiple-type benchmark suites with 4,104 apps, covering hand-made apps, open-source, and commercial ones. Considering their differences, various evaluation metrics, e.g., number count, graph structure, and reliable oracle based metrics, are adopted on-demand. As the oracle for real-world apps is unavailable, we design a dynamic analysis approach to extract the real ICC links triggered during GUI exploration. By auditing the code implementations, we carefully check the extracted ICCs and confirm 1,680 ones to form a reliable oracle set, in which each ICC is labeled with 25 code characteristic tags. The evaluation performed on six state-of-the-art ICC resolution tools shows that 1) the completeness of static ICC resolution results on real-world apps is not satisfactory, as up to 38%-85% ICCs are missed by tools; 2) many wrongly reported ICCs are sent from or received by only a few components and the graph structure information can help the identification; 3) the efficiency of fundamental tools, like ICC resolution ones, should be optimized in both engineering and research aspects. By investigating both the missed and wrongly reported ICCs, we discuss the strengths of different tools for users and summarize eight common FN/FP patterns in ICC resolution for tool developers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560420"}, {"primary_key": "1727739", "vector": [], "sparse_vector": [], "title": "Data Leakage in Notebooks: Static Detection and Better Processes.", "authors": ["<PERSON><PERSON>", "<PERSON>-Sinning", "<PERSON>", "<PERSON>"], "summary": "Data science pipelines to train and evaluate models with machine learning may contain bugs just like any other code. Leakage between training and test data can lead to overestimating the model's accuracy during offline evaluations, possibly leading to deployment of low-quality models in production. Such leakage can happen easily by mistake or by following poor practices, but may be tedious and challenging to detect manually. We develop a static analysis approach to detect common forms of data leakage in data science code. Our evaluation shows that our analysis accurately detects data leakage and that such leakage is pervasive among over 100,000 analyzed public notebooks. We discuss how our static analysis approach can help both practitioners and educators, and how leakage prevention can be designed into the development process.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556918"}, {"primary_key": "1727740", "vector": [], "sparse_vector": [], "title": "TransplantFix: Graph Differencing-based Code Transplantation for Automated Program Repair.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated program repair (APR) holds the promise of aiding manual debugging activities. Over a decade of evolution, a broad range of APR techniques have been proposed and evaluated on a set of real-world bug datasets. However, while more and more bugs have been correctly fixed, we observe that the growth of newly fixed bugs by APR techniques has hit a bottleneck in recent years. In this work, we explore the possibility of addressing complicated bugs by proposing TransplantFix, a novel APR technique that leverages graph differencing-based transplantation from the donor method. The key novelty of TransplantFix lies in three aspects: 1) we propose to use a graph-based differencing algorithm to distill semantic fix actions from the donor method; 2) we devise an inheritance-hierarchy-aware code search approach to identify donor methods with similar functionality; 3) we present a namespace transfer approach to effectively adapt donor code.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556893"}, {"primary_key": "1727741", "vector": [], "sparse_vector": [], "title": "Answer Summarization for Technical Queries: Benchmark and New Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>hung", "Yucen Shi", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Han", "<PERSON>"], "summary": "Prior studies have demonstrated that approaches to generate an answer summary for a given technical query in Software Question and Answer (SQA) sites are desired. We find that existing approaches are assessed solely through user studies. Hence, a new user study needs to be performed every time a new approach is introduced; this is time-consuming, slows down the development of the new approach, and results from different user studies may not be comparable to each other. There is a need for a benchmark with ground truth summaries as a complement assessment through user studies. Unfortunately, such a benchmark is non-existent for answer summarization for technical queries from SQA sites.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560421"}, {"primary_key": "1727742", "vector": [], "sparse_vector": [], "title": "A Study of User Privacy in Android Mobile AR Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the development of augmented reality (AR) technology, the use of mobile AR applications (MAR apps) is rising rapidly in various aspects of people's everyday lives, such as games, shopping, and education. When compared to traditional apps, AR apps typically need access to the smartphone's camera all the time and collect and analyze significantly more data, such as sensor data, geolocation, and biometric information. Due to the sensitivity and volume of data collected by MAR apps, new privacy concerns are raised. In this paper, we describe a preliminary empirical study of Android MAR apps in terms of the sensitive data collected by MAR apps, whether the collected data is well protected, and whether the data practice is publicly available so that users can learn about the data safety and make informed decisions when deciding which apps to install. In this study, we analyzed 390 real-world MAR apps and reported the dangerous permissions they requested, the data leaks detected in them, and the availability of their data safety.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560512"}, {"primary_key": "1727743", "vector": [], "sparse_vector": [], "title": "SelfAPR: Self-supervised Program Repair with Test Execution Diagnostics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Learning-based program repair has achieved good results in a recent series of papers. Yet, we observe that the related work fails to repair some bugs because of a lack of knowledge about 1) the application domain of the program being repaired, and 2) the fault type being repaired. In this paper, we solve both problems by changing the learning paradigm from supervised training to self-supervised training in an approach called SelfAPR. First, SelfAPR generates training samples on disk by perturbing a previous version of the program being repaired, enforcing the neural model to capture project-specific knowledge. This is different from the previous work based on mined past commits. Second, SelfAPR executes all training samples and extracts and encodes test execution diagnostics into the input representation, steering the neural model to fix the kind of fault. This is different from the existing studies that only consider static source code as input. We implement SelfAPR and evaluate it in a systematic manner. We generate 1 039 873 training samples obtained by perturbing 17 open-source projects. We evaluate SelfAPR on 818 bugs from Defects4J, SelfAPR correctly repairs 110 of them, outperforming all the supervised learning repair approaches.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556926"}, {"primary_key": "1727744", "vector": [], "sparse_vector": [], "title": "Empirical Study of System Resources Abused by IoT Attackers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "IoT devices have been under frequent attacks in recent years, causing severe impacts. Previous research has shown the evolution and features of some specific IoT malware families or stages of IoT attacks through offline sample analysis. However, we still lack a systematic observation of various system resources abused by active attackers and the malicious intentions behind these behaviors. This makes it difficult to design appropriate protection strategies to defend against existing attacks and possible future variants.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556901"}, {"primary_key": "1727745", "vector": [], "sparse_vector": [], "title": "HTFuzz: Heap Operation Sequence Sensitive Fuzzing.", "authors": ["<PERSON><PERSON>", "Xiangkun <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Purui Su"], "summary": "Heap-based temporal vulnerabilities (i.e., use-after-free, double-free and null pointer dereference) are highly sensitive to heap operation (e.g., memory allocation, deallocation and access) sequences. To efficiently find such vulnerabilities, traditional code coverage-guided fuzzing solutions could be promoted by integrating heap operation sequence feedback. But current sequence sensitive solutions have limitations in practice.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3560415"}, {"primary_key": "1727746", "vector": [], "sparse_vector": [], "title": "Unveiling Hidden DNN Defects with Decision-Based Metamorphic Testing.", "authors": ["Yuanyuan Yuan", "<PERSON>", "<PERSON><PERSON>"], "summary": "Contemporary DNN testing works are frequently conducted using metamorphic testing (MT). In general, de facto MT frameworks mutate DNN input images using semantics-preserving mutations and determine if DNNs can yield consistent predictions. Nevertheless, we find that DNNs may rely on erroneous decisions (certain components on the DNN inputs) to make predictions, which may still retain the outputs by chance. Such DNN defects would be neglected by existing MT frameworks. Erroneous decisions, however, would likely result in successive mis-predictions over diverse images that may exist in real-life scenarios.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3561157"}, {"primary_key": "1727747", "vector": [], "sparse_vector": [], "title": "Compiler Testing using Template Java Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "August Shi"], "summary": "We present JAttack, a framework that enables template-based testing for compilers. Using JAttack, a developer writes a template program that describes a set of programs to be generated and given as test inputs to a compiler. Such a framework enables developers to incorporate their domain knowledge on testing compilers, giving a basic program structure that allows for exploring complex programs that can trigger sophisticated compiler optimizations. A developer writes a template program in the host language (Java) that contains holes to be filled by JAttack. Each hole, written using a domain-specific language, constructs a node within an extended abstract syntax tree (eAST). An eAST node defines the search space for the hole, i.e., a set of expressions and values. JAttack generates programs by executing templates and filling each hole by randomly choosing expressions and values (available within the search space defined by the hole). Additionally, we introduce several optimizations to reduce JAttack's generation cost. While JAttack could be used to test various compiler features, we demonstrate its capabilities in helping test just-in-time (JIT) Java compilers, whose optimizations occur at runtime after a sufficient number of executions. Using JAttack, we have found six critical bugs that were confirmed by Oracle developers. Four of them were previously unknown, including two unknown CVEs (Common Vulnerabilities and Exposures). JAttack shows the power of combining developers' domain knowledge (via templates) with random testing to detect bugs in JIT compilers.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556958"}, {"primary_key": "1727748", "vector": [], "sparse_vector": [], "title": "BuildSonic: Detecting and Repairing Performance-Related Configuration Smells for Continuous Integration Builds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the benefits, continuous integration (CI) can incur high costs. One of the well-recognized costs is long build time, which greatly affects the speed of software development and increases the cost in computational resources. While there exist configuration options in the CI infrastructure to accelerate builds, the CI infrastructure is often not optimally configured, leading to CI configuration smells. Attempts have been made to detect or repair CI configuration smells. However, none of them is specifically designed to improve build performance in CI.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556923"}, {"primary_key": "1727749", "vector": [], "sparse_vector": [], "title": "Leveraging Artificial Intelligence on Binary Code Comprehension.", "authors": ["<PERSON><PERSON>"], "summary": "Understanding binary code is an essential but complex software engineering task for reverse engineering, malware analysis, and compiler optimization. Unlike source code, binary code has limited semantic information, which makes it challenging for human comprehension. At the same time, compiling source to binary code, or transpiling among different programming languages (PLs) can provide a way to introduce external knowledge into binary comprehension. We propose to develop Artificial Intelligence (AI) models that aid human comprehension of binary code. Specifically, we propose to incorporate domain knowledge from large corpora of source code (e.g., variable names, comments) to build AI models that capture a generalizable representation of binary code. Lastly, we will investigate metrics to assess the performance of models that apply to binary code by using human studies of comprehension.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559564"}, {"primary_key": "1727750", "vector": [], "sparse_vector": [], "title": "Xscope: Hunting for Cross-Chain Bridge Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-Chain bridges have become the most popular solution to support asset interoperability between heterogeneous blockchains. However, while providing efficient and flexible cross-chain asset transfer, the complex workflow involving both on-chain smart contracts and off-chain programs causes emerging security issues. In the past year, there have been more than ten severe attacks against cross-chain bridges, causing billions of loss. With few studies focusing on the security of cross-chain bridges, the community still lacks the knowledge and tools to mitigate this significant threat. To bridge the gap, we conduct the first study on the security of cross-chain bridges. We document three new classes of security bugs and propose a set of security properties and patterns to characterize them. Based on those patterns, we design Xscope, an automatic tool to find security violations in cross-chain bridges and detect real-world attacks. We evaluate Xscope on four popular cross-chain bridges. It successfully detects all known attacks and finds suspicious attacks unreported before. A video of <PERSON>scope is available at https://youtu.be/vMRO_qOqtXY.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559520"}, {"primary_key": "1727751", "vector": [], "sparse_vector": [], "title": "Has My Release Disobeyed Semantic Versioning? Static Detection Based on Semantic Differencing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To enhance the compatibility in the version control of Java Third-party Libraries (TPLs), <PERSON><PERSON> adopts Semantic Versioning (SemVer) to standardize the underlying meaning of versions, but users could still confront abnormal execution and crash after upgrades even if compilation and linkage succeed. It is caused by semantic breaking (SemB) issues, such that APIs directly used by users have identical signatures but inconsistent semantics across upgrades. To strengthen compliance with SemVer rules, developers and users should be alerted of such issues. Unfortunately, it is challenging to detect them statically, because semantic changes in the internal methods of APIs are difficult to capture. Dynamic testing can confirmingly uncover some, but it is limited by inadequate coverage.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556956"}, {"primary_key": "1727752", "vector": [], "sparse_vector": [], "title": "CoditT5: Pretraining for Source Code and Natural Language Editing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pretrained language models have been shown to be effective in many software-related generation tasks; however, they are not well-suited for editing tasks as they are not designed to reason about edits. To address this, we propose a novel pretraining objective which explicitly models edits and use it to build CoditT5, a large language model for software-related editing tasks that is pretrained on large amounts of source code and natural language comments. We fine-tune it on various downstream editing tasks, including comment updating, bug fixing, and automated code review. By outperforming standard generation-based models, we demonstrate the generalizability of our approach and its suitability for editing tasks. We also show how a standard generation model and our edit-based model can complement one another through simple reranking strategies, with which we achieve state-of-the-art performance for the three downstream editing tasks.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556955"}, {"primary_key": "1727753", "vector": [], "sparse_vector": [], "title": "Toward Improving the Robustness of Deep Learning Models via Model Transformation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning (DL) techniques have attracted much attention in recent years, and have been applied to many application scenarios, including those that are safety-critical. Improving the universal robustness of DL models is vital and many approaches have been proposed in the last decades aiming at such a purpose. Among existing approaches, adversarial training is the most representative. It advocates a post model tuning process via incorporating adversarial samples. Although successful, they still suffer from the challenge of generalizability issues in the face of various attacks with unsatisfactory effectiveness. Targeting this problem, in this paper we propose a novel model training framework, which aims at improving the universal robustness of DL models via model transformation incorporated with a data augmentation strategy in a delta debugging fashion. We have implemented our approach in a tool, called Dare, and conducted an extensive evaluation on 9 DL models. The results show that our approach significantly outperforms existing adversarial training techniques. Specifically, <PERSON> has achieved the highest Empirical Robustness in 29 of 45 testing scenarios under various attacks, while the number drops to 5 of 45 for the best baseline approach.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556920"}, {"primary_key": "1727754", "vector": [], "sparse_vector": [], "title": "Provably Tightest Linear Approximation for Robustness Verification of Sigmoid-like Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The robustness of deep neural networks is crucial to modern AI-enabled systems and should be formally verified. Sigmoid-like neural networks have been adopted in a wide range of applications. Due to their non-linearity, Sigmoid-like activation functions are usually over-approximated for efficient verification, which inevitably introduces imprecision. Considerable efforts have been devoted to finding the so-called tighter approximations to obtain more precise verification results. However, existing tightness definitions are heuristic and lack theoretical foundations. We conduct a thorough empirical analysis of existing neuron-wise characterizations of tightness and reveal that they are superior only on specific neural networks. We then introduce the notion of network-wise tightness as a unified tightness definition and show that computing network-wise tightness is a complex non-convex optimization problem. We bypass the complexity from different perspectives via two efficient, provably tightest approximations. The results demonstrate the promising performance achievement of our approaches over state of the art: (i) achieving up to 251.28% improvement to certified lower robustness bounds; and (ii) exhibiting notably more precise verification results on convolutional networks.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556907"}, {"primary_key": "1727755", "vector": [], "sparse_vector": [], "title": "QVIP: An ILP-based Formal Verification Approach for Quantized Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fu Song", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning has become a promising programming paradigm in software development, owing to its surprising performance in solving many challenging tasks. Deep neural networks (DNNs) are increasingly being deployed in practice, but are limited on resource-constrained devices owing to their demand for computational power. Quantization has emerged as a promising technique to reduce the size of DNNs with comparable accuracy as their floating-point numbered counterparts. The resulting quantized neural networks (QNNs) can be implemented energy-efficiently. Similar to their floating-point numbered counterparts, quality assurance techniques for QNNs, such as testing and formal verification, are essential but are currently less explored. In this work, we propose a novel and efficient formal verification approach for QNNs. In particular, we are the first to propose an encoding that reduces the verification problem of QNNs into the solving of integer linear constraints, which can be solved using off-the-shelf solvers. Our encoding is both sound and complete. We demonstrate the application of our approach on local robustness verification and maximum robustness radius computation. We implement our approach in a prototype tool QVIP and conduct a thorough evaluation. Experimental results on QNNs with different quantization bits confirm the effectiveness and efficiency of our approach, e.g., two orders of magnitude faster and able to solve more verification tasks in the same time limit than the state-of-the-art methods.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556916"}, {"primary_key": "1727756", "vector": [], "sparse_vector": [], "title": "Towards Understanding the Runtime Performance of Rust.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Rust is a young systems programming language, but it has gained tremendous popularity thanks to its assurance of memory safety. However, the performance of Rust has been less systematically understood, although many people are claiming that Rust is comparable to C/C++ regarding efficiency.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3559494"}, {"primary_key": "1727757", "vector": [], "sparse_vector": [], "title": "Efficient Synthesis of Method Call Sequences for Test Generation and Bounded Verification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern programs are usually heap-based, where the programs manipulate heap-based data structures to perform computations. In software engineering tasks such as test generation and bounded verification, we need to determine the existence of a reachable heap state that satisfies a given specification, or construct the heap state by a sequence of calls to the public methods. Given the huge space combined from the methods and their arguments, the existing approaches typically adopt static analysis or heuristic search to explore only a small part of search space in the hope of finding the target state and target call sequence early on. However, these approaches do not have satisfactory performance on many real-world complex methods and specifications. In this paper, we propose an efficient synthesis algorithm for method call sequences, including an offline procedure for exploring all reachable heap states within a scope, and an online procedure for generating a method call sequence from the explored heap states to satisfy the given specification. To improve the efficiency of state exploration, we introduce a notion of abstract heap state for compactly representing heap states of the same structure and propose a strategy of merging structurally-isomorphic states. The experimental results demonstrate that our approach substantially outperforms the baselines in both test generation and bounded verification.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556951"}, {"primary_key": "1727758", "vector": [], "sparse_vector": [], "title": "Enriching Compiler Testing with Real Program from Bug Report.", "authors": ["<PERSON><PERSON>"], "summary": "Researchers have proposed various approaches to generate test programs. The state-of-the-art approaches can be roughly divided into random-based and mutation-based approaches: random-based approaches generate random programs and mutation-based approaches mutate programs to generate more test programs. Both lines of approaches mainly generate random code, but it is more beneficial to use real programs, since it is easier to learn the impacts of compiler bugs and it becomes reasonable to use both valid and invalid code. However, most real programs from code repositories are ineffective to trigger compiler bugs, partially because they are compiled before they are submitted.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556894"}, {"primary_key": "1727759", "vector": [], "sparse_vector": [], "title": "Which Exception Shall We Throw?", "authors": ["<PERSON><PERSON>"], "summary": "Although the exception handling mechanism is critical for resolving runtime errors, bugs inside this process can have far-reaching impacts. Therefore, researchers have proposed various approaches to assist catching and handling such thrown exceptions and to detect corresponding bugs. If the thrown exceptions themselves are incorrect, their errors will never be correctly caught and handled. Like bugs in catching and handling exceptions, wrong thrown exceptions have caused real critical bugs. However, to the best of our knowledge, no approach has been proposed to recommend which exceptions shall be thrown. Exceptions are widely adopted in programs, often poorly documented, and sometimes ambiguous, making the rules of throwing correct exceptions rather complicated. A project team can leverage exceptions in a way totally different from other teams. As a result, even experienced programmers can have difficulties in determining which exception shall be thrown, although they have the skills to implement its surrounding code. In this paper, we propose the first approach, ThEx, to predict which exception(s) shall be thrown under a given programming context. The basic idea is to learn a classification model from existing thrown exceptions in source files. Here, the learning features are extracted from various code information surrounding the thrown exceptions, such as the thrown locations and related variable names. Then, given a new context, ThEx can predict its best exception(s).", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556895"}, {"primary_key": "1727760", "vector": [], "sparse_vector": [], "title": "StandUp4NPR: Standardizing SetUp for Empirically Comparing Neural Program Repair Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Hongliang Ge", "<PERSON><PERSON><PERSON> Ai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON>"], "summary": "Recently, the emerging trend in automatic program repair is to apply deep neural networks to generate fixed code from buggy ones, called NPR (Neural Program Repair). However, the existing NPR systems are trained and evaluated under very different settings (e.g., different training data, inconsistent evaluation data, wide-ranged candidate numbers), which makes it hard to draw fair-enough conclusions when comparing them. Motivated by this, we first build a standard benchmark dataset and an extensive framework tool to mitigate threats for the comparison. The dataset consists of a training set, a validation set and an evaluation set with 144,641, 13,739 and 13,706 bug-fix pairs of Java respectively. The tool supports selecting specific training, validation, and evaluation datasets and automatically conducting the pipeline of training and evaluating NPR models, as well as easily integrating new NPR models by implementing well-defined interfaces. Then, based on the benchmark and tool, we conduct a comprehensive empirical comparison of six SOTA NPR systems w.r.t the repairability, inclination and generalizability. The experimental results reveal deeper characteristics of compared NPR systems and subvert some existing comparative conclusions, which further verify the necessity of unifying the experimental setups in exploring the progresses of NPR systems. Meanwhile, we reveal some common features of NPR systems (e.g., they are good at dealing with code-delete bugs). Finally, we identify some promising research directions derived from our findings.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556943"}, {"primary_key": "1727761", "vector": [], "sparse_vector": [], "title": "Selectively Combining Multiple Coverage Goals in Search-Based Unit Test Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chunrong Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unit testing is a critical part of software development process, ensuring the correctness of basic programming units in a program (e.g., a method). Search-based software testing (SBST) is an automated approach to generating test cases. SBST generates test cases with genetic algorithms by specifying the coverage criterion (e.g., branch coverage). However, a good test suite must have different properties, which cannot be captured by using an individual coverage criterion. Therefore, the state-of-the-art approach combines multiple criteria to generate test cases. As combining multiple coverage criteria brings multiple objectives for optimization, it hurts the test suites' coverage for certain criteria compared with using the single criterion. To cope with this problem, we propose a novel approach named smart selection. Based on the coverage correlations among criteria and the coverage goals' subsumption relationships, smart selection selects a subset of coverage goals to reduce the number of optimization objectives and avoid missing any properties of all criteria. We conduct experiments to evaluate smart selection on 400 Java classes with three state-of-the-art genetic algorithms. On average, smart selection outperforms combining all goals on of the classes having significant differences between the two approaches.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556902"}, {"primary_key": "1727762", "vector": [], "sparse_vector": [], "title": "Safety and Performance, Why not Both? Bi-Objective Optimized Model Compression toward AI Software Deployment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The size of deep learning models in artificial intelligence (AI) software is increasing rapidly, which hinders the large-scale deployment on resource-restricted devices (e.g., smartphones). To mitigate this issue, AI software compression plays a crucial role, which aims to compress model size while keeping high performance. However, the intrinsic defects in the big model may be inherited by the compressed one. Such defects may be easily leveraged by attackers, since the compressed models are usually deployed in a large number of devices without adequate protection. In this paper, we try to address the safe model compression problem from a safety-performance co-optimization perspective. Specifically, inspired by the test-driven development (TDD) paradigm in software engineering, we propose a test-driven sparse training framework called SafeCompress. By simulating the attack mechanism as the safety test, SafeCompress can automatically compress a big model to a small one following the dynamic sparse training paradigm. Further, considering a representative attack, i.e., membership inference attack (MIA), we develop a concrete safe model compression mechanism, called MIA-SafeCompress. Extensive experiments are conducted to evaluate MIA-SafeCompress on five datasets for both computer vision and natural language processing tasks. The results verify the effectiveness and generalization of our method. We also discuss how to adapt SafeCompress to other attacks besides MIA, demonstrating the flexibility of SafeCompress.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3556906"}, {"primary_key": "1727763", "vector": [], "sparse_vector": [], "title": "Horntinuum: Autonomous Testing using Constrained Horn Clauses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2022-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1145/3551349.3563235"}]