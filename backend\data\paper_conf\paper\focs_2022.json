[{"primary_key": "1660357", "vector": [], "sparse_vector": [], "title": "Local Computation of Maximal Independent Set.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a randomized Local Computation Algorithm (LCA) with query complexity poly $(\\Delta) \\cdot \\log n$ for the Maximal Independent Set (MIS) problem. That is, the algorithm determines whether each node is in the computed MIS or not using poly $(\\Delta)\\cdot\\log n$ queries to the adjacency lists of the graph, with high probability, and this can be done for different nodes simultaneously and independently. Here $\\Delta$ and n denote the maximum degree and the number of nodes. This algorithm resolves a key open problem in the study of local computations and sublinear algorithms (attributed to <PERSON><PERSON>).", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00049"}, {"primary_key": "1660358", "vector": [], "sparse_vector": [], "title": "Õ(n+poly(k))-time Algorithm for Bounded Tree Edit Distance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Computing the edit distance of two strings is one of the most basic problems in computer science and combinatorial optimization. Tree edit distance is a natural generalization of edit distance in which the task is to compute a measure of dissimilarity between two (unweighted) rooted trees with node labels. Perhaps the most notable recent application of tree edit distance is in NoSQL big databases, such as MongoDB, where each row of the database is a JSON document represented as a labeled rooted tree and finding dissimilarity between two rows is a basic operation. Until recently, the fastest algorithm for tree edit distance ran in cubic time (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>; TALG'10); however, <PERSON> (FOCS'21) broke the cubic barrier for the tree edit distance problem using fast matrix multiplication.Given a parameter k as an upper bound on the distance, an $\\mathcal{O}(n+k^{2})$-time algorithm for edit distance has been known since the 1980s due to works of <PERSON> (Algorithmica'86) and <PERSON><PERSON> and <PERSON> (JCSS'88). The existence of an $\\tilde{\\mathcal{O}}(n+poly(k))$-time algorithm for tree edit distance has been posed as open question, e.g., by <PERSON><PERSON><PERSON> and <PERSON> (ICALP'21), who give a stateof-the-art $O(nk^{2})$-time algorithm. In this paper, we answer this question positively.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00071"}, {"primary_key": "1660359", "vector": [], "sparse_vector": [], "title": "Balanced Allocations: The Heavily Loaded Case with Deletions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In the 2-choice allocation problem, m balls are placed into n bins, and each ball must choose between two random bins $i,j\\in[n]$ that it has been assigned to. It has been known for more than two decades, that if each ball follows the GREEDY strategy (i.e., always pick the less-full bin), then the maximum load will be $m/n+O(\\log\\log n)$ with high probability in n (and $m/n+O(\\log m)$ with high probability in m). It has remained an open question whether the same bounds hold in the dynamic version of the same game, where balls are inserted/deleted with no more than m balls present at a time.We show that, somewhat surprisingly, these bounds do not hold in the dynamic setting: already on 4 bins, there exists a sequence of insertions/deletions that cause the GREEDY strategy to incur a maximum load of $m/4+\\Omega(\\sqrt{m})$ with probability $\\Omega(1)$—this is the same bound that one gets in the single-choice allocation model where each ball is assigned to a random bin!This raises the question of whether any 2-choice allocation strategy can offer a strong bound in the dynamic setting. Our second result answers this question in the affirmative: we present a new strategy, called MODULATEDGREEDY, that guarantees a maximum load of $m/n+O(\\log m)$, at any given moment, with high probability in m. We also show how to generalize ModulatedGreedy to obtain dynamic guarantees for the $(1+\\beta)$-choice setting, and for the setting of balls-and-bins on a graph.Finally, we consider an extension of the dynamic setting in which balls can be reinserted after they are deleted, and where the pair i, j that a given ball uses is consistent across insertions. This seemingly small modification renders tight load balancing impossible: on 4 bins, any balls-and-bins strategy that is oblivious to the specific identities of balls being inserted/deleted must allow for a maximum load of $m/4+\\mathrm{poly}(m)$ at some point in the first poly (m) insertions/deletions, with high probability in m. This is a remarkable departure from the m=n case where the maximum load of O(loglogn) holds independently of whether reinsertions are allowed or not.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00081"}, {"primary_key": "1660360", "vector": [], "sparse_vector": [], "title": "Unstructured Hardness to Average-Case Randomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The leading technical approach in uniform hardness-to-randomness in the last two decades faced several well-known barriers that caused results to rely on overly strong hardness assumptions, and yet still yield suboptimal conclusions. In this work we show uniform hardness-to-randomness results that simultaneously break through all of the known barriers. Specifically, consider any one of the following three assumptions:1)For some $\\epsilon>0$ there exists a function f computable by uniform circuits of size $2^{O(n)}$ and depth $2^{o(n)}$ such that f is hard for probabilistic time $2^{\\epsilon n}$.2)For every $c\\in \\mathbb{N}$ there exists a function f computable by logspace-uniform circuits of polynomial size and depth n 2 such that every probabilistic algorithm running in time n c fails to compute f on $\\mathrm{a}(1/n)$-fraction of the inputs.3)For every $c\\in \\mathbb{N}$ there exists a logspace-uniform family of arithmetic formulas of degree n 2 over a field of size poly $(n)$ such that no algorithm running in probabilistic time n c can evaluate the family on a worst-case input. Assuming any of these hypotheses, where the hardness is for every sufficiently large input length $n\\in \\mathbb{N}$, we deduce that $\\mathcal{R}\\mathcal{P}$ can be derandomized in polynomial time and on all input lengths, on average. Furthermore, under the first assumption we also show that $\\mathcal{B}\\mathcal{P}\\mathcal{P}$ can be derandomized in polynomial time, on average and on all input lengths, with logarithmically many advice bits. On the way to these results we also resolve two related open problems. First, we obtain an optimal worst-case to average-case reduction for computing problems in linear space by uniform probabilistic algorithms; this result builds on a new instance checker based on the doubly efficient proof system of Goldwasser, Kalai, and Rothblum (J. ACM, 2015). Secondly, we resolve the main open problem in the work of Carmosino, Impagliazzo and Sabin (ICALP 2018), by deducing derandomization from weak and general fine-grained hardness hypotheses. The full version of this paper is available online [5].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00048"}, {"primary_key": "1660361", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation via Mathematical Proofs of Equivalence.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin"], "summary": "Over the last decade, indistinguishability obfuscation (iO) has emerged as a seemingly omnipotent primitive with numerous applications to cryptography and beyond. Moreover, recent breakthrough work has demonstrated that iO can be realized from well-founded assumptions. A thorn to all this remarkable progress is a limitation of all known constructions of general-purpose iO: the security reduction incurs a loss that is exponential in the input length of the function. This \"input-length barrier\" to iO stems from the non-falsifiability of the iO definition and is discussed in folklore as being possibly inherent. It has many negative consequences; notably, constructing iO for programs with inputs of unbounded length remains elusive due to this barrier. We present a new framework aimed towards overcoming the input-length barrier. Our approach relies on short mathematical proofs of functional equivalence of circuits (and Turing machines) to avoid the brute-force \"input-by-input\" check employed in prior works.– We show how to obfuscate circuits that have efficient proofs of equivalence in Propositional Logic with a security loss independent of input length.– Next, we show how to obfuscate Turing machines with unbounded length inputs, whose functional equivalence can be proven in <PERSON>'s Theory PV.– Finally, we demonstrate applications of our results to succinct non-interactive arguments and witness encryption, and provide guidance on using our techniques for building new applications.To realize our approach, we depart from prior work and develop a new gate-by-gate obfuscation template that preserves the topology of the input circuit.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00100"}, {"primary_key": "1660362", "vector": [], "sparse_vector": [], "title": "Breaking the Cubic Barrier for All-Pairs Max-Flow: <PERSON><PERSON><PERSON><PERSON><PERSON> in Nearly Quadratic Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON>"], "summary": "In 1961, <PERSON><PERSON><PERSON> and <PERSON> showed that the All-Pairs Max-Flow problem of computing the max-flow between all $\\begin{pmatrix}n\\\\2\\end{pmatrix}$ pairs of vertices in an undirected graph can be solved using only $n-1$ calls to any (single-pair) max-flow algorithm. Even assuming a linear-time max-flow algorithm, this yields a running time of $O(mn)$, which is $O(n^{3})$ when $m=\\Theta(n^{2})$. While subsequent work has improved this bound for various special graph classes, no subcubic-time algorithm has been obtained in the last 60 years for general graphs. We break this longstanding barrier by giving an $\\tilde{O}(n^{2})$-time algorithm on general, integer-weighted graphs. Combined with a popular complexity assumption, we establish a counter-intuitive separation: all-pairs max-flows are strictly easier to compute than all-pairs shortest-paths.Our algorithm produces a cut-equivalent tree, known as the Gomory-Hu tree, from which the max-flow value for any pair can be retrieved in near-constant time. For unweighted graphs, we refine our techniques further to produce a Gomory-Hu tree in the time of a poly-logarithmic number of calls to any maxflow algorithm. This shows an equivalence between the all-pairs and single-pair max-flow problems, and is optimal up to polylogarithmic factors. Using the recently announced $m^{1+o(1)}$-time max-flow algorithm (<PERSON> et al., March 2022), our Gomory-Hu tree algorithm for unweighted graphs also runs in $m^{1+o(1)}$-time.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00088"}, {"primary_key": "1660363", "vector": [], "sparse_vector": [], "title": "Sampling from the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> measure via algorithmic stochastic localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the <PERSON><PERSON><PERSON><PERSON><PERSON> model of spin glasses at high-temperature and no external field, and study the problem of sampling from the Gibbs distribution $\\mu$ in polynomial time. We prove that, for any inverse temperature $\\beta\\lt 1/2$, there exists an algorithm with complexity $O(n^{2})$ that samples from a distribution $\\mu^{\\text{als}}$ which is close in normalized <PERSON><PERSON><PERSON> distance to $\\mu$. Namely, there exists a coupling of $\\mu$ and $\\mu^{\\text{alg}}$ such that if $(x,x^{\\text{als}})\\in\\{-1,+1\\}^{n}\\times\\{-1,+1\\}^{n}$ is a pair drawn from this coupling, then $n^{-1}\\mathbb{E}\\{\\|x-x^{\\text{ald}}\\|_{2}^{2}\\}=o_{n}(1)$. The best previous results, by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [BB19] and by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> [EKZ21], implied efficient algorithms to approximately sample (under a stronger metric) for $\\beta\\lt 1/4$. We complement this result with a negative one, by introducing a suitable \"stability\" property for sampling algorithms, which is verified by many standard techniques. We prove that no stable algorithm can approximately sample for $\\beta$>1, even under the normalized <PERSON><PERSON>stein metric. Our sampling method is based on an algorithmic implementation of stochastic localization, which progressively tilts the measure $\\mu$ towards a single configuration, together with an approximate message passing algorithm that is used to approximate the mean of the tilted measure.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00038"}, {"primary_key": "1660364", "vector": [], "sparse_vector": [], "title": "Interior point methods are not worse than Simplex.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "László A. <PERSON>"], "summary": "Whereas interior point methods provide polynomial-time linear programming algorithms, the running time bounds depend on bit-complexity or condition measures that can be unbounded in the problem dimension. This is in contrast with the simplex method that always admits an exponential bound. We introduce a new polynomial-time path-following interior point method where the number of iterations also admits a combinatorial upper bound $O(2^{n}n^{15}\\log n)$ for an n-variable linear program in standard form. This complements previous work by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (SIAGA 2018) that exhibited a family of instances where any path-following method must take exponentially many iterations. The number of iterations of our algorithm is at most $O(n^{15}\\log n)$ times the number of segments of any piecewise linear curve in the wide neighborhood of the central path. In particular, it matches the number of iterations of any path following interior point method up to this polynomial factor. The overall exponential upper bound derives from studying the max central path', a piecewise-linear curve with the number of pieces bounded by the total length of 2n shadow vertex simplex paths. From the existence of a line segment in the wide neighborhood we derive strong implications on the structure of the corresponding segment of the central path. Our algorithm is able to detect this structure from the local geometry at the current iterate, and constructs a step direction that descends along this segment. The bound $O(n^{15}\\log n)$ that applies for arbitrarily long line segments is derived from a combinatorial progress measure. Our algorithm falls into the family of layered least squares interior point methods introduced by Vavasis and Ye (Math. Prog. 1996). In contrast to previous layered least squares methods that partition the kernel of the constraint matrix into coordinate subspaces, our method creates layers based on a general subspace providing more flexibility. Our result also implies the same bound on the number of iterations of the trust region interior point method by Lan, Monteiro, and Tsuchiya (SIOPT 2009).", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00032"}, {"primary_key": "1660365", "vector": [], "sparse_vector": [], "title": "Optimal Sublinear Sampling of Spanning Trees and Determinantal Point Processes via Average-Case Entropic Independence.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "We design fast algorithms for repeatedly sampling from strongly Rayleigh distributions, which include as special cases random spanning tree distributions and determinantal point processes. For a graph $G=(V,\\ E)$, we show how to approximately sample uniformly random spanning trees from G in $O(|V|)$ 1 time per sample after an initial $O(|E|)$ time preprocessing. This is the first nearly-linear runtime in the output size, which is clearly optimal. For a determinantal point process on k-sized subsets of a ground set of n elements, defined via an $n\\times n$ kernel matrix, we show how to approximately sample in ${\\widetilde{O}}(k^{\\omega})$ time after an initial ${\\widetilde{O}}(nk^{\\omega-1})$ time preprocessing, where $\\omega\\lt 2.372864$ is the matrix multiplication exponent. The time to compute just the weight of the output set is simply $\\simeq k^{\\omega}$, a natural barrier that suggests our runtime might be optimal for determinantal point processes as well. As a corollary, we even improve the state of the art for obtaining a single sample from a determinantal point process, from the prior runtime of ${\\widetilde{O}}(\\min\\{nk^{2},\\ n^{\\omega}\\})$ to ${\\widetilde{O}}(nk^{\\omega-1})$.In our main technical result, we achieve the optimal limit on domain sparsification for strongly Rayleigh distributions. In domain sparsification, sampling from a distribution $\\mu$ on $\\binom{[n]}{k}$ is reduced to sampling from related distributions on $\\binom{[t]}{k}$ for $t\\ll n$. We show that for strongly Rayleigh distributions, the domain size can be reduced to nearly linear in the output size $t={\\widetilde{O}}(k)$, improving the state of the art from $t={\\widetilde{O}}(k^{2})$ for general strongly Rayleigh distributions and the more specialized $t={\\widetilde{O}}(k^{15})$ for sBanning tree distributions. Our reduction involves sampling from ${\\widetilde{O}}(1)$ domain-sparsified distributions, all of which can be produced efficiently assuming approximate overestimates for marginals of $\\mu$ are known and stored in a convenient data structure. Having access to marginals is the discrete analog of having access to the mean and covariance of a continuous distribution, or equivalently knowing \"isotropy\" for the distribution, the key behind optimal samplers in the continuous setting based on the famous Kannan-Lovász-Simonovits (KLS) conjecture. We view our result as analogous in spirit to the KLS conjecture and its consequences for sampling, but rather for discrete strongly Rayleigh measures. 1 Throughout, ${\\widetilde{O}}(\\cdot)$ hides polylogarithmic factors in n.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00019"}, {"primary_key": "1660366", "vector": [], "sparse_vector": [], "title": "Solving the Hamilton cycle problem fast on average.", "authors": ["<PERSON>"], "summary": "We present CertifyHAM, a deterministic algorithm that takes a graph G as input and either finds a Hamilton cycle of G or outputs that such a cycle does not exist. If $G\\sim G(n,p)$ and $p\\displaystyle \\geq\\frac{100\\log n}{n}$ then the expected running time of CertifyHAM is $O\\left(\\displaystyle \\frac{n}{p}\\right)$ which is best possible. This improves upon previous results due to <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, who proved analogous results for p being constant, $p\\geq 12n^{-1/3}$ and $p\\geq 70n^{-1/2}$ respectively.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00091"}, {"primary_key": "1660367", "vector": [], "sparse_vector": [], "title": "Estimating the Longest Increasing Subsequence in Nearly Optimal Time.", "authors": ["<PERSON><PERSON><PERSON>", "Negev <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Longest Increasing Subsequence (LIS) is a fundamental statistic of a sequence, and has been studied for decades. While the LIS of a sequence of length n can be computed exactly in time $O(n\\log n)$, the complexity of estimating the (length of the) LIS in sublinear time, especially when LIS $\\ll n$, is still open. We show that for any $n\\in\\mathbb{N}$ and $\\lambda=o(1)$, there exists a (randomized) non-adaptive algorithm that, given a sequence of length n with LIS $\\geq\\lambda n$, approximates the LIS up to a factor of $1/\\lambda^{o(1)}$ in $ n^{o(1)}/\\lambda$ time. Our algorithm improves upon prior work substantially in terms of both approximation and run-time: (i) we provide the first sub-polynomial approximation for LIS in sub-linear time; and (ii) our run-time complexity essentially matches the trivial sample complexity lower bound of $\\Omega(1/\\lambda)$, which is required to obtain any non-trivial approximation of the LIS. As part of our solution, we develop two novel ideas which may be of independent interest. First, we define a new Genuine-LIS problem, in which each sequence element may be either genuine or corrupted. In this model, the user receives unrestricted access to the actual sequence, but does not know a priori which elements are genuine. The goal is to estimate the LIS using genuine elements only, with the minimal number of tests for genuineness. The second idea, Precision Tree, enables accurate estimations for composition of general functions from “coarse” (sub-)estimates. Precision Tree essentially generalizes classical precision sampling, which works only for summations. As a central tool, the Precision Tree is pre-processed on a set of samples, which thereafter is repeatedly used by multiple components of the algorithm, improving their amortized complexity.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00073"}, {"primary_key": "1660368", "vector": [], "sparse_vector": [], "title": "On Matrix Multiplication and Polynomial Identity Testing.", "authors": ["<PERSON>"], "summary": "We show that lower bounds on the border rank of matrix multiplication can be used to non-trivially derandomize polynomial identity testing for small algebraic circuits. Letting $\\underline{\\text{R}}(n)$ denote the border rank of $n\\times n\\times n$ matrix multiplication, we construct a hitting set generator with seed length $O(\\sqrt{n}.\\underline{\\text{R}}^{-1}(s))$ that hits n-variate circuits of multiplicative complexity s. If the matrix multiplication exponent w is not 2, our generator has seed length $O(n^{1-\\varepsilon})$ and hits circuits of size $O(n^{1+\\delta})$ for sufficiently small $\\varepsilon, \\delta\\gt 0$. Surprisingly, the fact that $\\underline{\\text{R}}(n)\\geq n^{2}$ already yields new, non-trivial hitting set generators for circuits of sublinear multiplicative complexity.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00041"}, {"primary_key": "1660369", "vector": [], "sparse_vector": [], "title": "Cut Query Algorithms with Star Contraction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We study the complexity of determining the edge connectivity of a simple graph with cut queries. We show that (i) there is a bounded-error randomized algorithm that computes edge connectivity with O(n) cut queries, and (ii) there is a bounded-error quantum algorithm that computes edge connectivity with O(vn) cut queries. To prove these results we introduce a new technique, called star contraction, to randomly contract edges of a graph while preserving non-trivial minimum cuts. In star contraction vertices randomly contract an edge incident on a small set of randomly chosen 'center' vertices. In contrast to the related 2-out contraction technique of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [SODA'20], star contraction only contracts vertex-disjoint star subgraphs, which allows it to be efficiently implemented via cut queries. The O(n) bound from item (i) was not known even for the simpler problem of connectivity, and it improves the O(n log3n) upper bound by <PERSON><PERSON>, <PERSON>, and <PERSON> [ITCS'18]. The bound is tight under the reasonable conjecture that the randomized communication complexity of connectivity is O(n\\log n), an open question since the seminal work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS'86]. The bound also excludes using edge connectivity on simple graphs to prove a superlinear randomized query lower bound for minimizing a symmetric submodular function. The quantum algorithm from item (ii) gives a nearlyquadratic separation with the randomized complexity, and addresses an open question of <PERSON>, <PERSON><PERSON>, and <PERSON> [SODA'21]. The algorithm can alternatively be viewed as computing the edge connectivity of a simple graph with O(vn) matrix-vector multiplication queries to its adjacency matrix. Finally, we demonstrate the use of star contraction outside of the cut query setting by designing a one-pass semi-streaming algorithm for computing edge connectivity in the complete vertex arrival setting. This contrasts with the edge arrival setting where two passes are required.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00055"}, {"primary_key": "1660370", "vector": [], "sparse_vector": [], "title": "Rounds vs Communication Tradeoffs for Maximal Independent Sets.", "authors": ["<PERSON><PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of finding a maximal independent set (MIS) in the shared blackboard communication model with vertex-partitioned inputs. There are n players corresponding to vertices of an undirected graph, and each player sees the edges incident on its vertex – this way, each edge is known by both its endpoints and is thus shared by two players. The players communicate in simultaneous rounds by posting their messages on a shared blackboard visible to all players, with the goal of computing an MIS of the graph. While the MIS problem is well studied in other distributed models, and while shared blackboard is, perhaps, the simplest broadcast model, lower bounds for our problem were only known against one-round protocols. We present a lower bound on the round-communication tradeoff for computing an MIS in this model. Specifically, we show that when r rounds of interaction are allowed, at least one player needs to communicate $\\Omega(n^{1/20^{r+1}})$ bits. In particular, with logarithmic bandwidth, finding an MIS requires $\\Omega(\\log\\log n)$ rounds. This lower bound can be compared with the algorithm of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> [PODC 2018] that solves MIS in $O(\\log\\log n)$ rounds but with a logarithmic bandwidth for an average player. Additionally, our lower bound further extends to the closely related problem of maximal bipartite matching. The presence of edge-sharing gives the algorithms in our model a surprising power and numerous algorithmic results exploiting this power are known. For a similar reason, proving lower bounds in this model is much more challenging, as this sharing in the players' inputs prohibits the use of standard number-in-hand communication complexity arguments. Thus, to prove our results, we devise a new round elimination framework, which we call partial-input embedding, that may also be useful in future work for proving round-sensitive lower bounds in the presence of shared inputs. Finally, we discuss several implications of our results to multi-round (adaptive) distributed sketching algorithms, broadcast congested clique, and to the welfare maximization problem in two-sided matching markets.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00115"}, {"primary_key": "1660371", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Power-Sum Decomposition of Polynomials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give efficient algorithms for finding power-sum decomposition of an input polynomial $P(x)=\\displaystyle \\sum_{i\\leq m}p_{i}(x)^{d}$ with component $p_{i}s$. The case of linear $p_{i}s$ is equivalent to the well-studied tensor decomposition problem while the quadratic case occurs naturally in studying identifiability of non-spherical Gaussian mixtures from low-order moments. Unlike tensor decomposition, both the unique identifiability and algorithms for this problem are not well-understood. For the simplest setting of quadratic $p_{i}s$ and $d=3$, prior work of [11] yields an algorithm only when $m\\leq\\overline{O}(\\sqrt{n})$. On the other hand, the more general recent result of [13] builds an algebraic approach to handle any $m=n^{O(1)}$ components but only when d is large enough (while yielding no bounds for d=3 or even d=100) and only handles an inverse exponential noise. Our results obtain a substantial quantitative improvement on both the prior works above even in the base case of d=3 and quadratic $p_{i}s$. Specifically, our algorithm succeeds in decomposing a sum of $m\\sim\\overline{O}(n)$ generic quadratic $p_{i}s$ for $d=3$ and more generally the dth power-sum of $m\\sim n^{2d/15}$ generic degree-K polynomials for any K$\\geq$2. Our algorithm relies only on basic numerical linear algebraic primitives, is exact (i.e., obtain arbitrarily tiny error up to numerical precision), and handles an inverse polynomial noise when the $p_{i}s$ have random Gaussian coefficients. Our main tool is a new method for extracting the linear span of $p_{i}s$ by studying the linear subspace of low-order partial derivatives of the input P. For establishing polynomial stability of our algorithm in average-case, we prove inverse polynomial bounds on the smallest singular value of certain correlated random matrices with low-degree polynomial entries that arise in our analyses. Since previous techniques only yield significantly weaker bounds, we analyze the smallest singular value of matrices by studying the largest singular value of certain deviation matrices via graph matrix decomposition and the trace moment method.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00094"}, {"primary_key": "1660372", "vector": [], "sparse_vector": [], "title": "Performance and limitations of the QAOA at constant levels on large sparse hypergraphs and spin glass models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Quantum Approximate Optimization Algorithm (QAOA) is a general purpose quantum algorithm designed for combinatorial optimization. We analyze its expected performance and prove concentration properties at any constant level (number of layers) on ensembles of random combinatorial optimization problems in the infinite size limit. These ensembles include mixed spin models and Max-q-XORSAT on sparse random hypergraphs. Our analysis can be understood via a saddlepoint approximation of a sum-over-paths integral. This is made rigorous by proving a generalization of the multinomial theorem, which is a technical result of independent interest. We then show that the performance of the QAOA at constant levels for the pure q-spin model matches asymptotically the ones for Max-q XORSAT on random sparse Erdôs-Rényi hypergraphs and every large-girth regular hypergraph. Through this correspondence, we establish that the average-case value produced by the QAOA at constant levels is bounded away from optimality for pure q-spin models when $q\\geq 4$ and is even. This limitation gives a hardness of approximation result for quantum algorithms in a new regime where the whole graph is seen.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00039"}, {"primary_key": "1660373", "vector": [], "sparse_vector": [], "title": "Geometry of Secure Two-party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "What is the round and communication complexity of secure computation? The seminal results of <PERSON><PERSON><PERSON><PERSON><PERSON> (STOC-1989, FOCS-1989, DIMACS-1989) answer this question for computations with deterministic output. However, this question has remained unanswered for computations with randomized output. Our work answers this question for two-party secure function evaluation functionalities. We introduce a geometric encoding of all candidate secure protocols for a given computation as points in a high-dimensional space. The following results follow by analyzing the properties of these sets of points.1)It is decidable to determine if a given computation has a secure protocol within round or communication constraints.2)We construct one such protocol if it exists.3)Otherwise, we present an obstruction to achieving security.Our technical contributions imply new information complexity bounds for secure computation.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00101"}, {"primary_key": "1660374", "vector": [], "sparse_vector": [], "title": "Almost 3-Approximate Correlation Clustering in Constant Rounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study parallel algorithms for correlation clustering. Each pair among n objects is labeled as either \"similar\" or \"dissimilar\". The goal is to partition the objects into arbitrarily many clusters while minimizing the number of disagreements with the labels.Our main result is an algorithm that for any $\\varepsilon>0$ obtains a (3 + $\\varepsilon$)-approximation in $O(1/\\varepsilon$) rounds (of models such as massively parallel computation, local, and semi-streaming). This is a culminating point for the rich literature on parallel correlation clustering. On the one hand, the approximation (almost) matches a natural barrier of 3 for combinatorial algorithms. On the other hand, the algorithm's round-complexity is essentially constant.To achieve this result, we introduce a simple $O(1/\\varepsilon$)-round parallel algorithm. Our main result is to provide an analysis of this algorithm, showing that it achieves a (3 + $\\varepsilon$)-approximation. Our analysis draws on new connections to sublinear-time algorithms. Specifically, it builds on the work of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> [1] on bounding the \"query complexity\" of greedy maximal independent set. To our knowledge, this is the first application of this method in analyzing the approximation ratio of any algorithm.Full version. Due to the page limit, this version of the paper does not include all the proofs. The full version of the paper is available at [2].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00074"}, {"primary_key": "1660375", "vector": [], "sparse_vector": [], "title": "Randomised Composition and Small-Bias Minimax.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove 1 two results about randomised query complexity $\\mathbf{R}(f)$. First, we introduce a linearised complexity measure LR and show that it satisfies an inner-optimal composition theorem: $\\mathbf{R}(f^{\\circ} g)\\geq\\Omega(\\mathbf{R}(f)\\mathbf{L R}(g))$ for all partial f and g, and moreover, LR is the largest possible measure with this property. In particular, LR can be polynomially larger than previous measures that satisfy an inner composition theorem, such as the max-conflict complexity of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (ICALP 2019). Our second result addresses a question of <PERSON> (FOCS 1977). He asked if $\\epsilon$-error expected query complexity $\\overline{\\mathbf{R}}_{\\epsilon}(f)$ admits a distributional characterisation relative to some hard input distribution. <PERSON><PERSON><PERSON><PERSON><PERSON> (TCS 1998) answered this question affirmatively in the bounded-error case. We show that an analogous theorem fails in the small-bias case $\\epsilon=1/2-o(1)$. 1 This is an extended abstract. For the full version of this article, please refer to [BDBGM22].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00065"}, {"primary_key": "1660376", "vector": [], "sparse_vector": [], "title": "Online List Labeling: Breaking the log2n Barrier.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The online list-labeling problem is an algorithmic primitive with a large literature of upper bounds, lower bounds, and applications. The goal is to store a dynamically-changing set of n items in an array of m slots, while maintaining the invariant that the items appear in sorted order, and while minimizing the relabeling cost, defined to be the number of items that are moved per insertion/deletion. For the linear regime, where $m = (1+\\Theta(1))n$, an upper bound of $O(\\log^{2}n)$ on the relabeling cost has been known since 1981. A lower bound of $\\Omega(\\log^{2}n)$ is known for deterministic algorithms and for so-called smooth algorithms, but the best general lower bound remains $\\Omega(\\log n)$. The central open question in the field is whether $O(\\log^{2}n)$ is optimal for all algorithms. In this paper, we give a randomized data structure that achieves an expected relabeling cost of $O(\\log^{3/2}n)$ per operation. More generally, if $m=(1+\\varepsilon)n$ for $\\varepsilon=O(1)$, the expected relabeling cost becomes $O(\\varepsilon^{-1}\\log^{3/2}n)$. Our solution is history independent, meaning that the state of the data structure is independent of the order in which items are inserted/deleted. For history-independent data structures, we also prove a matching lower bound: for all $\\varepsilon$ between $1/n^{1/3}$ and some sufficiently small positive constant, the optimal expected cost for history-independent list-labeling solutions is $\\Theta(\\varepsilon^{-1}\\log^{3/2}n)$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00096"}, {"primary_key": "1660377", "vector": [], "sparse_vector": [], "title": "Negative-Weight Single-Source Shortest Paths in Near-linear Time.", "authors": ["<PERSON>", "Danupon <PERSON>", "<PERSON><PERSON>"], "summary": "We present a randomized algorithm that computes single-source shortest paths (SSSP) in O(mlog(8)(n) logW) time when edge weights are integral and can be negative. This essentially resolves the classic negative-weight SSSP problem. The previous bounds are (O) over tilde ((m + n(1.5)) logW) [BLNPSSSW FOCS'20] and m(4/3+o(1)) logW [AMV FOCS'20]. Near-linear time algorithms were known previously only for the special case of planar directed graphs [<PERSON><PERSON><PERSON><PERSON><PERSON>ph<PERSON> and Rao FOCS'01]. In contrast to all recent developments that rely on sophisticated continuous optimization methods and dynamic algorithms, our algorithm is simple: it requires only a simple graph decomposition and elementary combinatorial tools. In fact, ours is the first combinatorial algorithm for negative-weight SSSP to break through the classic (O) over tilde (m root n logW) bound from over three decades ago [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'89].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00063"}, {"primary_key": "1660378", "vector": [], "sparse_vector": [], "title": "Fast Multivariate Multipoint Evaluation Over All Finite Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multivariate multipoint evaluation is the problem of evaluating a multivariate polynomial, given as a coefficient vector, simultaneously at multiple evaluation points. In this work, we show that there exists a deterministic algorithm for multivariate multipoint evaluation over any finite field F that outputs the evaluations of an m-variate polynomial of degree less than d in each variable at N points in time $(d^{m}+N)^{1+o(1)}$ poly $(m,\\ d,\\ \\log|\\mathbb{F}|)$ for all $m\\in \\mathbb{N}$ and all sufficiently large $d\\in \\mathbb{N}$. A previous work of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS 2008, SICOMP 2011) achieved the same time complexity when the number of variables m is at most $d^{o(1)}$ and had left the problem of removing this condition as an open problem. A recent work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> (STOC 2022) answered this question when the underlying field is not too large and has characteristic less than $d^{o(1)}$. In this work, we remove this constraint on the number of variables over all finite fields, thereby answering the question of <PERSON><PERSON><PERSON> and <PERSON><PERSON> over all finite fields. Our algorithm relies on a non-trivial combination of ideas from three seemingly different previously known algorithms for multivariate multipoint evaluation, namely the algorithms of <PERSON><PERSON><PERSON> and <PERSON><PERSON>, that of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (IPEC 2017, Algorithmica 2019), and that of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, together with a result of <PERSON>ieri and <PERSON>ogradov from analytic number theory about the distribution of primes in an arithmetic progression. We also present a second algorithm for multivariate multipoint evaluation that is completely elementary and in particular, avoids the use of the Bombieri-Vinogradov Theorem. However, it requires a mild assumption that the field size is bounded by an exponential-tower in d of bounded height.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00028"}, {"primary_key": "1660379", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Communication and Query Complexity of Bipartite Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We settle the complexities of the maximum-cardinality bipartite matching problem (BMM) up to polylogarithmic factors in five models of computation: the two-party communication, AND query, OR query, XOR query, and quantum edge query models. Our results answer open problems that have been raised repeatedly since at least three decades ago [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>ran STOC'88; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> FSTTCS'12; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>'14; <PERSON><PERSON>'21] and tighten the lower bounds shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [STOC'21] and <PERSON> [ICALP'04]. We also settle the communication complexity of the generalizations of BMM, such as maximum-cost bipartite b-matching and transshipment; and the query complexity of unique bipartite perfect matching (answering an open question by <PERSON><PERSON><PERSON> [2022]). Our algorithms and lower bounds follow from simple applications of known techniques such as cutting planes methods and set disjointness.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00113"}, {"primary_key": "1660380", "vector": [], "sparse_vector": [], "title": "New Additive Spanner Lower Bounds by an Unlayered Obstacle Product.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For an input graph G, an additive spanner is a sparse subgraph H whose shortest paths match those of G up to small additive error. We prove two new lower bounds in the area of additive spanners:•We construct n-node graphs G for which any spanner on $O(n)$ edges must increase a pairwise distance by $+\\Omega(n^{1/7})$. This improves on a recent lower bound of $+\\Omega(n^{1/10.5})$ by <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> [SODA 22].•A classic result by <PERSON>smith and Elkin [SODA 05] proves that for any n-node graph G and set of $p=O(n^{1/2})$ demand pairs, one can exactly preserve all pairwise distances among demand pairs using a spanner on $O(n)$ edges. They also provided a lower bound construction, establishing that that this range $p=O(n^{1/2})$ cannot be improved. We strengthen this lower bound by proving that, for any constant k, this range of p is still unimprovable even if the spanner is allowed $+k$ additive error among the demand pairs. This negatively resolves an open question asked by <PERSON>smith and <PERSON><PERSON> [SODA 05] and again by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [STACS 13] and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SODA 16].At a technical level, our lower bounds are obtained by an improvement to the entire obstacle product framework used to compose \"inner\" and outer\" graphs into lower bound instances. In particular, we develop a new strategy for analysis that allows certain non-layered graphs to be used in the product, and we use this freedom to design better inner and outer graphs that lead to our new lower bounds.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00079"}, {"primary_key": "1660381", "vector": [], "sparse_vector": [], "title": "Fast Deterministic Fully Dynamic Distance Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we develop deterministic fully dynamic algorithms for computing approximate distances in a graph with worst-case update time guarantees. In particular, we obtain improved dynamic algorithms that, given an unweighted and undirected graph G = (V, E) undergoing edge insertions and deletions, and a parameter $0 \\lt \\epsilon \\leq 1$, maintain (1 + ϵ)-approximations of the st-distance between a given pair of nodes s and t, the distances from a single source to all nodes (“SSSP”), the distances from multiple sources to all nodes (“MSSP”), or the distances between all nodes (“APSP”). Our main result is a deterministic algorithm for maintaining (1 + ϵ)-approximate st-distance with worst-case update time O(n 1.407 ) (for the current best known bound on the matrix multiplication exponent (ω). This even improves upon the fastest known randomized algorithm for this problem. Similar to several other well-studied dynamic problems whose state-of-the-art worst-case update time is O(n 1.407 ), this matches a conditional lower bound [BNS, FOCS 2019]. We further give a deterministic algorithm for maintaining (1 + ϵ)-approximate single-source distances with worst-case update time O(n 1.529 ), which also matches a conditional lower bound. At the core, our approach is to combine algebraic distance maintenance data structures with near-additive emulator constructions. This also leads to novel dynamic algorithms for maintaining (1 + ϵ, β)-emulators that improve upon the state of the art, which might be of independent interest. Our techniques also lead to improved randomized algorithms for several problems such as exact st-distances and diameter approximation.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00099"}, {"primary_key": "1660382", "vector": [], "sparse_vector": [], "title": "The Power of Uniform Sampling for Coresets.", "authors": ["<PERSON>", "<PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by practical generalizations of the classic k-median and k-means objectives, such as clustering with size constraints, fair clustering, and Wasserstein barycenter, we introduce a meta-theorem for designing coresets for constrained-clustering problems. The meta-theorem reduces the task of coreset construction to one on a bounded number of ring instances with a much-relaxed additive error. This reduction enables us to construct coresets using uniform sampling, in contrast to the widely-used importance sampling, and consequently we can easily handle constrained objectives. Notably and perhaps surprisingly, this simpler sampling scheme can yield coresets whose size is independent of n, the number of input points. Our technique yields smaller coresets, and sometimes the first coresets, for a large number of constrained clustering problems, including capacitated clustering, fair clustering, Euclidean Wasserstein barycenter, clustering in minor-excluded graph, and polygon clustering under <PERSON><PERSON><PERSON> and <PERSON><PERSON> distance. Finally, our technique yields also smaller coresets for 1-median in low-dimensional Euclidean spaces, specifically of size O(?-1.5) in R2 and O(?-1.6) in R3.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00051"}, {"primary_key": "1660383", "vector": [], "sparse_vector": [], "title": "Determinant Maximization via Matroid Intersection Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Determinant maximization problem gives a general framework that models problems arising in as diverse fields as statistics [1], convex geometry [2], fair allocations [3], combinatorics [4], spectral graph theory [5], network design, and random processes [6]. In an instance of a determinant maximization problem, we are given a collection of vectors $U=\\{v_{1},\\cdots,\\ v_{n}\\}\\subset \\mathbb{R}^{d}$, and a goal is to pick a subset $S\\subseteq U$ of given vectors to maximize the determinant of the matrix $\\displaystyle \\sum_{i\\in S}v_{i}v_{i}^{\\text{T}}$. Often, the set S of picked vectors must satisfy additional combinatorial constraints such as cardinality constraint $(|S|\\leq k)$ or matroid constraint $(S$ is a basis of a matroid defined on the vectors). In this paper, we give a polynomial-time deterministic algorithm that returns a $r^{O(r)}$-approximation for any matroid of rank $r \\leq d$. This improves previous results that give $e^{O(r^{2})}$-approximation algorithms relying on $e^{O(r)}$-approximate estimation algorithms [4], [7] –[9] for any r$\\leq$d. All previous results use convex relaxations and their relationship to stable polynomials and strongly $\\log$-concave polynomials or non-convex relaxations for the problem [10]. In contrast, our algorithm builds on combinatorial algorithms for matroid intersection, which iteratively improve any solution by finding an alternating negative cycle in the exchange graph defined by the matroids. While the $\\det(.)$ function is not linear, we show that taking appropriate linear approximations at each iteration suffice to give the improved approximation algorithm.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00031"}, {"primary_key": "1660384", "vector": [], "sparse_vector": [], "title": "A Characterization of Multiclass Learnability.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A seminal result in learning theory characterizes the PAC learnability of binary classes through the Vapnik-Chervonenkis dimension. Extending this characterization to the general multiclass setting has been open since the pioneering works on multiclass PAC learning in the late 1980s. This work resolves this problem: we characterize multiclass PAC learnability through the DS dimension, a combinatorial dimension defined by <PERSON><PERSON> and <PERSON><PERSON>, (2014). The classical characterization of the binary case boils down to empirical risk minimization. In contrast, our characterization of the multiclass case involves a variety of algorithmic ideas; these include a natural setting we call list PAC learning. In the list learning setting, instead of predicting a single outcome for a given unseen input, the goal is to provide a short menu of predictions. Our second main result concerns the Natarajan dimension, which has been a central candidate for characterizing multiclass learnability. This dimension was introduced by <PERSON><PERSON><PERSON> (1988) as a barrier for PAC learning. He furthered showed that it is the only barrier, provided that the number of labels is bounded. Whether the Natarajan dimension characterizes PAC learnability in general has been posed as an open question in several papers since. This work provides a negative answer: we construct a non-learnable class with Natarajan dimension 1. For the construction, we identify a fundamental connection between concept classes and topology (i.e., colorful simplicial complexes). We crucially rely on a deep and involved construction of hyperbolic pseudo-manifolds by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. It is interesting that hyperbolicity is directly related to learning problems that are difficult to solve although no obvious barriers exist. This is another demonstration of the fruitful links machine learning has with different areas in mathematics.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00093"}, {"primary_key": "1660385", "vector": [], "sparse_vector": [], "title": "Shortest Paths without a Map, but with an Entropic Regularizer.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In a 1989 paper titled \"shortest paths without a map\", <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> introduced an online model of searching in a weighted layered graph for a target node, while attempting to minimize the total length of the path traversed by the searcher. This problem, later called layered graph traversal, is parametrized by the maximum cardinality k of a layer of the input graph. It is an online setting for dynamic programming, and it is known to be a rather general and fundamental model of online computing, which includes as special cases other acclaimed models. The deterministic competitive ratio for this problem was soon discovered to be exponential in k, and it is now nearly resolved: it lies between $\\Omega(2^{k})$ and $O(k2^{k})$. Regarding the randomized competitive ratio, in 1993 <PERSON><PERSON> proved, surprisingly, that this ratio has to be at least $\\Omega(k^{2}/log^{1+\\varepsilon}k)$ (for any constant $\\varepsilon\\gt0)$. In the same paper, <PERSON><PERSON> also gave an $O(k^{13})$-competitive randomized online algorithm. Since 1993, no progress has been reported on the randomized competitive ratio of layered graph traversal. In this work we show how to apply the mirror descent framework on a carefully selected evolving metric space, and obtain an $O(k^{2})$ competitive randomized online algorithm, nearly matching the known lower bound on the randomized competitive ratio.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00036"}, {"primary_key": "1660386", "vector": [], "sparse_vector": [], "title": "Algorithms for the ferromagnetic Potts model on expanders.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give algorithms for approximating the partition function of the ferromagnetic Potts model on d-regular expanding graphs. We require much weaker expansion than in previous works; for example, the expansion exhibited by the hypercube suffices. The main improvements come from a significantly sharper analysis of standard polymer models, using extremal graph theory and applications of <PERSON><PERSON>'s algorithm to counting cuts that may be of independent interest. It is #BIS-hard to approximate the partition function at low temperatures on bounded-degree graphs, so our algorithm can be seen as evidence that hard instances of #BIS are rare. We believe that these methods can shed more light on other important problems such as sub-exponential algorithms for approximate counting problems.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00040"}, {"primary_key": "1660387", "vector": [], "sparse_vector": [], "title": "Improved Lower Bounds for Submodular Function Minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide a generic technique for constructing families of submodular functions to obtain lower bounds for submodular function minimization (SFM). Applying this technique, we prove that any deterministic SFM algorithm on a ground set of n elements requires at least $\\Omega(n\\log n)$ queries to an evaluation oracle. This is the first super-linear query complexity lower bound for SFM and improves upon the previous best lower bound of 2n given by [<PERSON><PERSON><PERSON> et al., ITCS 2020]. Using our construction, we also prove that any (possibly randomized) parallel SFM algorithm, which can make up to poly $(n)$ queries per round, requires at least $\\Omega(n/\\log n)$ rounds to minimize a submodular function. This improves upon the previous best lower bound of $\\tilde{\\Omega}(n^{1/3})$ rounds due to [<PERSON><PERSON><PERSON><PERSON> et al., FOCS 2021], and settles the parallel complexity of query-efficient SFM up to logarithmic factors due to a recent advance in [Jiang, SODA 2021].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00030"}, {"primary_key": "1660388", "vector": [], "sparse_vector": [], "title": "Faster Pattern Matching under Edit Distance : A Reduction to Dynamic Puzzle Matching and the Seaweed Monoid of Permutation Matrices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the approximate pattern matching problem under the edit distance. Given a text T of length n, a pattern P of length m, and a threshold k, the task is to find the starting positions of all substrings of T that can be transformed to P with at most k edits. More than 20 years ago, <PERSON> and <PERSON> [SODA'98, <PERSON><PERSON>'02] gave an $\\mathcal{O}(n+k^{4}\\cdot n/m)$ time algorithm for this classic problem, and this runtime has not been improved since.Here, we present an algorithm that runs in time $\\mathcal{O}\\left(n+ k^{3.5}\\sqrt{\\log m\\log k}\\cdot n/m\\right)$, thus breaking through this longstanding barrier. In the case where $n^{1/4+\\varepsilon}\\leq k\\leq n^{2/5-\\varepsilon}$ for some arbitrarily small positive constant $\\varepsilon$, our algorithm improves over the state-of-the-art by polynomial factors: it is polynomially faster than both the algorithm of <PERSON> and <PERSON><PERSON> and the classic $\\mathcal{O}(kn)$-time algorithm of Land<PERSON> and Vishkin [STOC'86, <PERSON><PERSON>'89].We observe that the bottleneck case of the alternative $\\mathcal{O}(n+k^4 \\cdot n / m$-time algorithm of Charalamp<PERSON>los, Kociumaka, and Wellnitz [FOCS'20] is when the text and the pattern are (almost) periodic. Our new algorithm reduces this case to a new Dynamic Puzzle Matching problem, which we solve by building on tools developed by Tiskin [SODA'10, Algorithmica'15] for the so-called seaweed monoid of permutation matrices. Our algorithm relies only on a small set of primitive operations on strings and thus also applies to the fully-compressed setting (where text and pattern are given as straight-line programs) and to the dynamic setting (where we maintain a collection of strings under creation, splitting, and concatenation), improving over the state of the art.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00072"}, {"primary_key": "1660389", "vector": [], "sparse_vector": [], "title": "Constant Approximation of Min-Distances in Near-Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a weighed directed graph $G=(V, E, \\omega)$ with m edges and n vertices, we are interested in its basic graph parameters such as diameter, radius and eccentricities, under the nonstandard measure of min-distance which is defined for every pair of vertices $u, v \\in V$ as the minimum of the shortest path distances from u to v and from v to u. Similar to standard shortest paths distances, computing graph parameters exactly in terms of min-distances essentially requires $\\tilde{\\Omega}(m n)$ time under plausible hardness conjectures 1 . Hence, for faster running time complexities we have to tolerate approximations. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [SODA 2016] were the first to study min-distance problems, and they obtained constant factor approximation algorithms in acyclic graphs, with running time $\\tilde{O}(m)$ and $\\tilde{O}(m \\sqrt{n})$ for diameter and radius, respectively. The time complexity of radius in acyclic graphs was recently improved to $\\tilde{O}(m)$ by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [ICALP 2021], but at the cost of an $O(\\log n)$ approximation ratio. For general graphs, the authors of [DWV+, ICALP 2019] gave the first constant factor approximation algorithm for diameter, radius and eccentricities which runs in time $\\tilde{O}(m \\sqrt{n})$; besides, for the diameter problem, the running time can be improved to $\\tilde{O}(m)$ while blowing up the approximation ratio to $O(\\log n)$. A natural question is whether constant approximation and near-linear time can be achieved simultaneously for diameter, radius and eccentricities; so far this is only possible for diameter in the restricted setting of acyclic graphs. In this paper, we answer this question in the affirmative by presenting near-linear time algorithms for all three parameters in general graphs. 1 As usual, the $\\tilde{O}(\\cdot)$ notation hides poly-logarithmic factors in n", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00089"}, {"primary_key": "1660390", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Quantum State Certification with Incoherent Measurements.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of quantum state certification, where we are given the description of a mixed state $\\sigma\\in\\mathbb{C}^{d\\times d},n$ copies of a mixed state $\\rho\\in\\mathbb{C}^{d\\times d}$, and $\\varepsilon\\gt 0$, and we are asked to determine whether $\\rho=\\sigma$ or whether $\\|\\rho-\\sigma\\|_{1}\\gt \\varepsilon$. When $\\sigma$ is the maximally mixed state $\\frac{1}{d}I_{d}$, this is known as mixedness testing. We focus on algorithms which use incoherent measurements, i.e. which only measure one copy of $\\rho$ at a time. Unlike those that use entangled, multi-copy measurements, these can be implemented without persistent quantum memory and thus represent a large class of protocols that can be run on current or near-term devices. For mixedness testing, there is a folklore algorithm which uses incoherent measurements and only needs $O(d^{3/2}/\\varepsilon^{2})$ copies. The algorithm is non-adaptive, that is, its measurements are fixed ahead of time, and is known to be optimal for non-adaptive algorithms. However, when the algorithm can make arbitrary incoherent measurements, the best known lower bound is only $\\Omega(d^{4/3}/\\varepsilon^{2})$ [5], and it has been an outstanding open problem to close this polynomial gap. In this work: •We settle the copy complexity of mixedness testing with incoherent measurements and show that $\\Omega(d^{3/2}/\\varepsilon^{2})$ copies are necessary. This fully resolves open questions of [15] and [5].•We show that the instance-optimal bounds for state certification to general $\\sigma$ first derived in [7] for non-adaptive measurements also hold for arbitrary incoherent measurements. Qualitatively, our results say that adaptivity does not help at all for these problems. Our results are based on new techniques that allow us to reduce the problem to understanding the concentration of certain matrix martingales, which we believe may be of independent interest.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00118"}, {"primary_key": "1660391", "vector": [], "sparse_vector": [], "title": "Optimal mixing for two-state anti-ferromagnetic spin systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove an optimal $\\Omega(n^{-1})$ lower bound for modified $\\log$-<PERSON><PERSON><PERSON> (<PERSON>) constant of the <PERSON><PERSON><PERSON> dynamics for anti-ferromagnetic two-spin systems with n vertices in the tree uniqueness regime. Specifically, this optimal MLS bound holds for the following classes of two-spin systems in the tree uniqueness regime: (1) all strictly anti-ferromagnetic two-spin systems (where both edge parameters $\\beta, \\gamma\\leq$ 1), which cover the hardcore models and the anti-ferromagnetic Ising models; (2) general antiferromagnetic two-spin systems on regular graphs. Consequently, an optimal $O(n\\log n)$ mixing time holds for these anti-ferromagnetic two-spin systems when the uniqueness condition is satisfied. These MLS and mixing time bounds hold for any bounded or unbounded maximum degree, and the constant factors in the bounds depend only on the gap to the uniqueness threshold. We prove this by showing a boosting theorem for MLS constant for distributions satisfying certain spectral independence and marginal stability properties.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00062"}, {"primary_key": "1660392", "vector": [], "sparse_vector": [], "title": "Localization Schemes: A Framework for Proving Mixing Bounds for Markov Chains (extended abstract).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Two recent and seemingly-unrelated techniques for proving mixing bounds for Markov chains are: (i) the framework of Spectral Independence, introduced by <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, and its numerous extensions, which have given rise to several breakthroughs in the analysis of mixing times of discrete Markov chains and (ii) the Stochastic Localization technique which has proven useful in establishing mixing and expansion bounds for both log-concave measures and for measures on the discrete hypercube. In this paper, we introduce a framework which connects ideas from both techniques. Our framework unifies, simplifies and extends those two techniques. In its center is the concept of a \"localization scheme\" which, to every probability measure on some space $\\Omega$, assigns a martingale of probability measures which \"localize\" in space as time evolves. As it turns out, to every such scheme corresponds a Markov chain, and many chains of interest appear naturally in this framework. This viewpoint provides tools for deriving mixing bounds for the dynamics through the analysis of the corresponding localization process. Generalizations of concepts of Spectral Independence and Entropic Independence naturally arise from our definitions, and in particular we recover the main theorems in the spectral and entropic independence frameworks via simple martingale arguments (completely bypassing the need to use the theory of high-dimensional expanders). We demonstrate the strength of our proposed machinery by giving short and (arguably) simpler proofs to many mixing bounds in the recent literature. In particular, we: (i) Give the first O(n log n) bound for mixing time of the hardcore-model (of arbitrary degree) in the tree-uniqueness regime, under Glauber dynamics, (ii) Give the first optimal mixing bounds for Ising models in the uniqueness regime under any external fields, (iii) Prove a KL-divergence decay bound for log-concave sampling via the Restricted Gaussian Oracle, which achieves optimal mixing under any exp (n)-ivarm start, (iv) Prove a logarithmic-Sobolev inequality for near-critical Ferromagnetic Ising models, recovering in a simple way a variant of a recent result by Bauerschmidt and Dagallier.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00018"}, {"primary_key": "1660393", "vector": [], "sparse_vector": [], "title": "On Weighted Graph Sparsification by Linear Sketching.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A seminal work of [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PODS'12] showed that one can compute a cut sparsifier of an unweighted undirected graph by taking a near-linear number of linear measurements on the graph. Subsequent works also studied computing other graph sparsifiers using linear sketching, and obtained near-linear upper bounds for spectral sparsifiers [<PERSON><PERSON><PERSON><PERSON>, FOCS'14] and first non-trivial upper bounds for spanners [<PERSON><PERSON><PERSON><PERSON>, SODA'21]. All these linear sketching algorithms, however, only work on unweighted graphs, and are extended to weighted graphs by weight grouping, a non-linear operation not implementable in, for instance, general turnstile streams.In this paper, we initiate the study of weighted graph sparsification by linear sketching by investigating a natural class of linear sketches that we call incidence sketches, in which each measurement is a linear combination of the weights of edges incident on a single vertex. This class captures all aforementioned linear sketches for unweighted sparsification. It also covers linear sketches implementable in the simultaneous communication model, where edges are distributed across n machines. Our results are:1)Weighted cut sparsification: We give an algorithm that computes a $(1+\\epsilon)$-cut sparsifier using $\\tilde{O}(n\\epsilon^{-3})$ linear measurements, which is nearly optimal. This also implies a turnstile streaming algorithm with $\\tilde{O}(n\\epsilon^{-3})$ space. Our algorithm is achieved by building a so-called \"weighted edge sampler\" for each vertex.2)Weighted spectral sparsification: We give an algorithm that computes a $(1+\\epsilon)$-spectral sparsifier using $\\tilde{O}(n^{6/5}\\epsilon^{-4})$ linear measurements. This also implies a turnstile streaming algorithm with $\\tilde{O}(n^{6/5}\\epsilon^{-4})$ space. Key to our algorithm is a novel analysis of how the effective resistances change under vertex sampling. Complementing our algorithm, we then prove a superlinear lower bound of $\\Omega(n^{21/20-o(1)})$ measurements for computing some O(1)-spectral sparsifier using incidence sketches.3)Weighted spanner computation: We first show that any $o(n^{2})$ linear measurements can only recover a spanner of stretch that in general depends linearly on $\\frac{w_{\\max}}{w_{\\min}}$. We thus focus on graphs with $\\frac{w_{\\max}}{w_{\\min}}=O(1)$ and study the stretch's dependence on n. On such graphs, the algorithm in [FiltserKapralov-Nouri, SODA'21] can obtain a spanner of stretch $\\tilde{O}\\left(n^{\\frac{2}{3}\\left(1-\\alpha\\right)}\\right)$ using $\\tilde{O}(n^{1+\\alpha})$ measurements for any $\\alpha\\in [0,1]$. We prove that, for incidence sketches, this tradeoff is optimal up to an $n^{o(1)}$ factor for all $\\alpha\\lt 1/10$.We prove both our lower bounds by analyzing the \"effective resistances\" in certain matrix-weighted graphs, where we develop a number of new tools for reasoning about such graphs – most notably (i) a matrix-weighted analog of the widely used expander decomposition of ordinary graphs, and (ii) a proof that a random vertex-induced subgraph of a matrix-weighted expander is also an expander. We believe these tools are of independent interest.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00052"}, {"primary_key": "1660394", "vector": [], "sparse_vector": [], "title": "Maximum Flow and Minimum-Cost Flow in Almost-Linear Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an algorithm that computes exact maximum flows and minimum-cost flows on directed graphs with m edges and polynomially bounded integral demands, costs, and capacities in $m^{1+o(1)}$ time. Our algorithm builds the flow through a sequence of $m^{1+o(1)}$ approximate undirected minimum-ratio cycles, each of which is computed and processed in amortized $m^{o(1)}$ time using a new dynamic graph data structure. Our framework extends to algorithms running in $m^{1+o(1)}$ time for computing flows that minimize general edge-separable convex functions to high accuracy. This gives almost-linear time algorithms for several problems including entropy-regularized optimal transport, matrix scaling, p-norm flows, and p-norm isotonic regression on arbitrary directed acyclic graphs.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00064"}, {"primary_key": "1660395", "vector": [], "sparse_vector": [], "title": "Survivable Network Design Revisited: Group-Connectivity.", "authors": ["<PERSON><PERSON> Chen", "Bundit Laekhanukit", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the classical survivable network design problem (SNDP), we are given an undirected graph $G-(V,E)$ with costs on edges and a connectivity requirement $k(5,t)$ for each pair of vertices. The goal is to find a minimum-cost subgraph $H\\sqsubseteq G$ such that every pair $(s,t)$ are connected by $k(s,t)$ edge or (openly) vertex disjoint paths, abbreviated as EC-SNDP and VC-SNDP, respectively. The seminal result of Jain [FOCS'98, Combinatorica'01] gives a 2-approximation algorithm for EC-SNDP, and a decade later, an $O(k^{3}\\log n)-$ approximation algorithm for VC-SNDP, where k is the largest connectivity requirement, was discovered by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS'09, Theory Comput'12]. While there is a rich literature on point-to-point settings of SNDP, the viable case of connectivity between subsets is still relatively poorly understood. This paper concerns the generalization of SNDP into the subset-to-subset setting, namely Group EC-SNDR We develop the framework, which yields the first non-trivial (true) approximation algorithm for Group. EC-SNDE Previously only a bicriteria approximation algorithm is known for Group EC-SNDP [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, SODA'15l, and a true approximation algorithm is known only for the single-source variant with connectivity requirement $k(S,T)\\in\\{0,1,2\\}$ [<PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>OD<PERSON>'10; Khan<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and N<PERSON>v, FST<PERSON>S'09 and <PERSON>r <PERSON><PERSON><PERSON>. <PERSON><PERSON>'12].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00033"}, {"primary_key": "1660396", "vector": [], "sparse_vector": [], "title": "Memory Bounds for Continual Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Continual learning, or lifelong learning, is a formidable current challenge to machine learning. It requires the learner to solve a sequence of k different learning tasks, one after the other, while retaining its aptitude for earlier tasks; the continual learner should scale better than the obvious solution of developing and maintaining a separate learner for each of the k tasks. We embark on a complexity-theoretic study of continual learning in the PAC framework. We make novel uses of communication complexity to establish that any continual learner, even an improper one, needs memory that grows linearly with k, strongly suggesting that the problem is intractable. When logarithmically many passes over the learning tasks are allowed, we provide an algorithm based on multiplicative weights update whose memory requirement scales well; we also establish that improper learning is necessary for such performance. We conjecture that these results may lead to new promising approaches to continual learning.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00056"}, {"primary_key": "1660397", "vector": [], "sparse_vector": [], "title": "Factorial Lower Bounds for (Almost) Random Order Streams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we introduce and study the STREAMINGCYCLES problem, a random order streaming version of the Boolean Hidden Hypermatching problem that has been instrumental in streaming lower bounds over the past decade. In this problem the edges of a graph G, comprising n/$\\ell$ disjoint length-$\\ell$ cycles on n vertices, are partitioned randomly among n players. Every edge is annotated with an independent uniformly random bit, and the players’ task is to output, for some cycle in G, the sum (modulo 2) of the bits on its edges, after one round of sequential communication.Our main result is an $\\ell^{\\Omega(\\ell)}$ lower bound on the communication complexity of STREAMINGCYCLES, which is tight up to constant factors in the exponent. Applications of our lower bound for STREAMINGCYCLES include an essentially tight lower bound for component collection in (almost) random order graph streams, making progress towards a conjecture of <PERSON><PERSON> and <PERSON> [SODA’18] and the first exponential space lower bounds for random walk generation.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00053"}, {"primary_key": "1660398", "vector": [], "sparse_vector": [], "title": "Fitting Metrics and Ultrametrics with Minimum Disagreements.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>"], "summary": "Given $x\\in(\\mathbb{R}_{\\geqslant 0})(_{2}^{[n]})$ recording pairwise distances, the Metric Violation Distance problem asks to compute the $\\ell_{0}$ distance between x and the metric cone; i.e., modify the minimum number of entries of x to make it a metric. Due to its large number of applications in various data analysis and optimization tasks, this problem has been actively studied recently. We present an $O(\\log n)$-approximation algorithm for METRIC VIOLATION Distance, exponentially improving the previous best approximation ratio of $O(OPT^{1/3})$ of <PERSON>, <PERSON>, and <PERSON> [SODA, 2018]. Furthermore, a major strength of our algorithm is its simplicity and running time. We also study the related problem of Ultrametric Violation Distance, where the goal is to compute the $\\ell_{0}$ distance to the cone of ultrametrics, and achieve a constant factor approximation algorithm. The ULTRAMETRIC VIOLATION DISTANCE problem can be regarded as an extension of the problem of fitting ultrametrics studied by <PERSON><PERSON> and <PERSON> [SIAM J. Computing, 2011] and by <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS, 2021] from $\\ell_{1}$ norm to $\\ell_{0}$ norm. We show that this problem can be favorably interpreted as an instance of CORRELATION CLUSTERING with an additional hierarchical structure, which we solve using a new $O(1)$-approximation algorithm for correlation clustering that has the structural property that it outputs a refinement of the optimum clusters. An algorithm satisfying such a property can be considered of independent interest. We also provide an $O(\\log n\\log\\log n)$ approximation algorithm for weighted instances. Finally, we investigate the complementary version of these problems where one aims at choosing a maximum number of entries of x forming an (ultra-)metric. In stark contrast with the minimization versions, we prove that these maximization versions are hard to approximate within any constant factor assuming the Unique Games Conjecture.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00035"}, {"primary_key": "1660399", "vector": [], "sparse_vector": [], "title": "Correlation Clustering with Sherali-Adams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>"], "summary": "Given a complete graph G=(V, E) where each edge is labeled + or −, the CORRELATION CLUSTERING problem asks to partition V into clusters to minimize the number of +edges between different clusters plus the number of −edges within the same cluster. CORRELATION CLUSTERING has been used to model a large number of clustering problems in practice, making it one of the most widely studied clustering formulations. The approximability of CORRELATION CLUSTERING has been actively investigated [BBC04], [CGW05], [ACN08], culminating in a 2.06-approximation algorithm [CMSY15], based on rounding the standard LP relaxation. Since the integrality gap for this formulation is 2, it has remained a major open question to determine if the approximation factor of 2 can be reached, or even breached. In this paper, we answer this question affirmatively by showing that there exists a($1.994+\\varepsilon$)-approximation algorithm based on $O(1/\\varepsilon^{2})$ rounds of the Sherali-Adams hierarchy. In order to round a solution to the Sherali-Adams relaxation, we adapt the correlated rounding originally developed for CSPs [BRSII], [GSII], [RT12]. With this tool, we reach an approximation ratio of $2+\\varepsilon$ for CORRELATION CLUSTERING. To breach this ratio, we go beyond the traditional triangle-based analysis by employing a global charging scheme that amortizes the total cost of the rounding across different triangles.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00068"}, {"primary_key": "1660400", "vector": [], "sparse_vector": [], "title": "Relaxed Locally Decodable and Correctable Codes: Beyond Tensoring.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In their highly influential paper, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ich, Harsha, Sudan, and <PERSON><PERSON><PERSON> (STOC 2004) introduced the notion of a relaxed locally decodable code (RLDC). Similarly to a locally decodable code (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; STOC 2000), the former admits access to any desired message symbol with only a few queries to a possibly corrupted codeword. An RLDC, however, is allowed to abort when identifying corruption. The natural analog to locally correctable codes, dubbed relaxed locally correctable codes (RLCC), was introduced by <PERSON><PERSON>, <PERSON> and <PERSON> (ITCS 2018) who constructed asymptotically-good length-nRLCC and RLDC with $(\\log n)^{O(\\log\\log n)}$ queries.In this work we construct asymptotically-good RLDC and RLCC with an improved query complexity of $(\\log n)^{O(\\log\\log\\log n)}$. To achieve this, we devise a mechanism-an alternative to the tensor product-that squares the length of a given code. Compared to the tensor product that was used by <PERSON><PERSON> et al. and by many other constructions, our mechanism is significantly more efficient in terms of rate deterioration, allowing us to obtain our improved construction.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00010"}, {"primary_key": "1660401", "vector": [], "sparse_vector": [], "title": "Streaming Facility Location in High Dimension via Geometric Hashing.", "authors": ["<PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In Euclidean Uniform Facility Location, the input is a set of clients in $\\mathrm{R}^{d}$ and the goal is to place facilities to serve them, so as to minimize the total cost of opening facilities plus connecting the clients. We study the classical setting of dynamic geometric streams, where the clients are presented as a sequence of insertions and deletions of points in the grid $\\{1,ldots\\,\\Delta \\}^{d}$, and we focus on the high-dimensional regime, where the algorithm's space complexity must be polynomial (and certainly not exponential) in $d \\cdot \\log \\Delta$.We present a new algorithmic framework, based on importance sampling from the stream, for $O(1)$-approximation of the optimal cost using only poly $(d\\cdot\\log\\Delta)$ space. This framework is easy to implement in two passes, one for sampling points and the other for estimating their contribution. Over random-order streams, we can extend this to a one-pass algorithm by using the two halves of the stream separately. Our main result, for arbitrary-order streams, computes $O(d^{1.5})$-approximation in one pass by using the new framework but combining the two passes differently. This improves upon previous algorithms that either need space exponential in d or only guarantee $O(d\\cdot\\log^{2}\\Delta)$-approximation, and therefore our algorithms for high-dimensional streams are the first to avoid the $O(\\log\\Delta)$ factor in approximation that is inherent to the widely-used quadtree decomposition. Our improvement is achieved by employing a geometric hashing scheme that maps points in $\\mathbb{R}^{d}$ into buckets of bounded diameter, with the key property that every point set of small-enough diameter is hashed into at most poly $(d)$ distinct buckets.Finally, we complement our results with a proof that every streaming 1.085-approximation algorithm requires space exponential in poly $(d \\cdot log \\Delta)$, even for insertion-only streams.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00050"}, {"primary_key": "1660402", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms and Hardness for n-Pairs Shortest Paths and All-Nodes Shortest Cycles.", "authors": ["<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "We study the approximability of two related problems on graphs with n nodes and m edges: n-Pairs Shortest Paths (n-PSP), where the goal is to find a shortest path between O(n) prespecified pairs, and All Node Shortest Cycles (ANSC), where the goal is to find the shortest cycle passing through each node. Approximate n-PSP has been previously studied, mostly in the context of distance oracles. We ask the question of whether approximate n-PSP can be solved faster than by using distance oracles or All Pair Shortest Paths (APSP). ANSC has also been studied previously, but only in terms of exact algorithms, rather than approximation.We provide a thorough study of the approximability of n PSP and ANSC, providing a wide array of algorithms and conditional lower bounds that trade off between running time and approximation ratio.A highlight of our conditional lower bounds results is that for any integer k$\\geq$1, under the combinatorial 4k-clique hypothesis, there is no combinatorial algorithm for unweighted undirected n-PSP with approximation ratio better than $1+1/k$ that runs in $O(m^{2-2/(k+1)}n^{1/(k+1)-\\varepsilon})$ time. This nearly matches an upper bound implied by the result of <PERSON><PERSON><PERSON> (2014).Our algorithms use a surprisingly wide range of techniques, including techniques from the girth problem, distance oracles, approximate APSP, spanners, fault-tolerant spanners, and link-cut trees.A highlight of our algorithmic results is that one can solve both n-PSP and ANSC in $O(m+n^{3/2+\\in})$ time 1 with approximation factor $2+\\varepsilon$ (and additive error that is function of $\\varepsilon$), for any constant $\\varepsilon\\lt 0$. For n-PSP, our conditional lower bounds imply that this approximation ratio is nearly optimal for any subquadratic-time combinatorial algorithm. We further extend these algorithms for n-PSP and ANSC to obtain a time/accuracy trade-off that includes near-linear time algorithms. 1 $\\tilde{O}$ hides sub-polynomial factors.Additionally, for ANSC, for all integers $k\\geq 1$, we extend the very recent almost k-approximation algorithm for the girth problem that works in $\\tilde{O}(n^{1+1/k})$ time [Kadria et al. SODA'22], and obtain an almost k-approximation algorithm for ANSC in $\\tilde{O}(mn^{1/k})$ time.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00034"}, {"primary_key": "1660403", "vector": [], "sparse_vector": [], "title": "Induced Cycles and Paths Are Harder Than You Think.", "authors": ["<PERSON>", "Virginia Vassilevska Williams"], "summary": "The goal of the paper is to give fine-grained hardness results for the Subgraph Isomorphism (SI) problem for fixed size induced patterns H, based on the k-Clique hypothesis that the current best algorithms for Clique are optimal. Our first main result is that for any pattern graph H that is a core, the SI problem for H is at least as hard as t-Clique, where t is the size of the largest clique minor of H. This improves (for cores) the previous known results [Dalirrooyfard-Vassilevska W. STOC'20] that the SI for H is at least as hard as k-clique where k is the size of the largest clique subgraph in H, or the chromatic number of H (under the <PERSON><PERSON><PERSON> conjecture). For detecting any graph pattern H, we further remove the dependency of the result of [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>assile<PERSON>ska W. STOC'20] on the <PERSON><PERSON><PERSON> conjecture at the cost of a sub-polynomial decrease in the lower bound. The result for cores allows us to prove that the SI problem for induced k-Path and k-Cycle is harder than previously known. Previously [<PERSON><PERSON><PERSON><PERSON> et al. Theor. CS 2015] had shown that k-Path and k-Cycle are at least as hard to detect as a $\\lfloor$k/2$\\rfloor -$Clique. We show that they are in fact at least as hard as 3k/4-O(1)-Clique, improving the conditional lower bound exponent by a factor of 3/2. This shoivs for instance that the knoivn $O(n^{5})$ combinatorial algorithm for 7-cycle detection is conditionally tight. Finally, we provide a new conditional lower bound for detecting induced 4-cycles: $n^{2-o(1)}$ time is necessary even in graphs with n nodes and $O(n^{15})$ edges. The 4-cycle is the smallest induced pattern whose running time is not well-understood. It can be solved in matrix multiplication, $O(n^{\\omega})$ time, but no conditional lower bounds were known until ours. We provide evidence that certain types of reductions from triangle detection to 4-Cycle would not be possible. We do this by studying a new problem called Paired Pattern Detection.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00057"}, {"primary_key": "1660404", "vector": [], "sparse_vector": [], "title": "Pure-Circuit: Strong Inapproximability for PPAD.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The current state-of-the-art methods for showing inapproximability in PPAD arise from the $\\varepsilon$-Generalized-Circuit ($\\varepsilon$-GCIRCUIT) problem. <PERSON><PERSON> (2018) showed that there exists a small unknown constant $\\varepsilon$ for which $\\varepsilon$-GCIRCUIT is PPAD-hard, and subsequent work has shown hardness results for other problems in PPAD by using $\\varepsilon$-GCIRCUIT as an intermediate problem.We introduce PURE-CIRCUIT, a new intermediate problem for PPAD, which can be thought of as $\\varepsilon$-GCIRCUIT pushed to the limit as $\\varepsilon\\rightarrow 1$, and we show that the problem is PPAD-complete. We then prove that $\\varepsilon$-GCIRCUIT is PPAD-hard for all $\\varepsilon \\lt 0.1$ by a reduction from PURE-CIRCUIT, and thus strengthen all prior work that has used GCIRCUIT as an intermediate problem from the existential-constant regime to the large-constant regime. We show that stronger inapproximability results can be derived by a direct reduction from PURE-CIRCUIT. In particular, we prove that finding an $\\varepsilon$-well-supported Nash equilibrium in a polymatrix game is PPAD-hard for all $\\varepsilon \\lt 1/3$, and that this result is tight for two-action games.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00022"}, {"primary_key": "1660405", "vector": [], "sparse_vector": [], "title": "Fooling polynomials using invariant theory*.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the problem of constructing explicit pseudorandom generators that fool with error ϵ degree-d polynomials in n variables over the field F q , in the case of large q. Previous constructions either have seed length $\\geq 2^{d}\\log q$, and thus are only non-trivial when $d\\lt \\log n$, or else rely on a seminal reduction by <PERSON><PERSON><PERSON><PERSON> (STOC 2005). This reduction yields seed length not less than $d^{4}\\log n+\\log q$ and requires fields of size $q\\geq d^{6}/\\epsilon^{2}$; and explicit generators meeting such bounds are known.Departing from <PERSON><PERSON><PERSON><PERSON>'s reduction, we develop an algebraic analogue of the Bogdanov-Viola paradigm (FOCS 2007, SICOMP 2010) of summing generators for degree-one polynomials. Whereas previous analyses of the paradigm are restricted to degree $d\\lt \\log n$, we give a new analysis which handles large degrees. A main new idea is to show that the construction preserves indecomposability of polynomials. Apparently for the first time in the area, the proof uses invariant theory.Our approach in particular yields several new pseudorandom generators. In particular, for large enough fields we obtain seed length $O(d\\log n+\\log q)$ which is optimal up to constant factors. We also construct generators for fields of size as small as $O(d^{4})$. Further reducing the field size requires a significant change in techniques: Most or all generators for large-degree polynomials rely on Weil bounds; but such bounds are only applicable when $q\\gt d^{4}$", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00045"}, {"primary_key": "1660406", "vector": [], "sparse_vector": [], "title": "Rate-1 Non-Interactive Arguments for Batch-NP and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a rate-1 construction of a publicly verifiable non-interactive argument system for batch-NP (also called a BARG), under the LWE assumption. Namely, a proof corresponding to a batch of k NP statements each with an m-bit witness, has size $m+poly(\\lambda, log k)$.In contrast, prior work either relied on non-standard knowledge assumptions, or produced proofs of size m. poly $(\\lambda, \\log k)$ (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, STOC 2021, following <PERSON><PERSON>, <PERSON>, and Yang 2019).We show how to use our rate-l BARG scheme to obtain the following results, all under the LWE assumption:•A multi-hop BARG scheme for NP.•A multi-hop aggregate signature scheme (in the standard model).•An incrementally verifiable computation (IVC) scheme for arbitrary T-time deterministic computations with proof size poly $(\\lambda, log T)$.Prior to this work, multi-hop BARGs were only known under non-standard knowledge assumptions or in the random oracle model; aggregate signatures were only known under indistinguishability obfuscation (and RSA) or in the random oracle model; IVC schemes with proofs of size poly $(\\lambda, T^{\\epsilon})$ were known under a bilinear map assumption, and with proofs of size poly $(\\lambda, log T)$ under non-standard knowledge assumptions or in the random oracle model.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00103"}, {"primary_key": "1660407", "vector": [], "sparse_vector": [], "title": "Linear Hashing with ℓ∞ guarantees and two-sided Kakeya bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that a randomly chosen linear map over a finite field gives a good hash function in the $\\ell_{\\infty}$ sense. More concretely, consider a set $S\\subset\\mathbb{F}_{q}^{n}$ and a randomly chosen linear ${map}L:\\mathbb{F}_{q}^{n}\\rightarrow\\mathbb{F}_{q}^{t}$ with q t taken to be sufficiently smaller than $|S|$. Let U S denote a random variable distributed uniformly on S. Our main theorem shows that, with high probability over the choice of L, the random variable $L(U_{S})$ is close to uniform in the $\\ell_{\\infty}$ norm. In other words, every element in the range $\\mathbb{F}_{q}^{t}$ has about the same number of elements in S mapped to it. This complements the widely-used Leftover Hash Lemma (LHL) which proves the analog statement under the statistical, or $\\ell_{1}$, distance (for a richer class of functions) as well as prior work on the expected largest ’bucket size’ in linear hash functions [1]. By known bounds from the load balancing literature [2], our results are tight and show that linear functions hash as well as truly random function up to a constant factor in the entropy loss. Our proof leverages a connection between linear hashing and the finite field Kakeya problem and extends some of the tools developed in this area, in particular the polynomial method.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00047"}, {"primary_key": "1660408", "vector": [], "sparse_vector": [], "title": "Differential Privacy from Locally Adjustable Graph Algorithms: k-Core Decomposition, Low Out-Degree Ordering, and Densest Subgraphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differentially private algorithms allow large-scale data analytics while preserving user privacy. Designing such algorithms for graph data is gaining importance with the growth of large networks that model various (sensitive) relationships between individuals. While there exists a rich history of important literature in this space, to the best of our knowledge, no results formalize a relationship between certain parallel and distributed graph algorithms and differentially private graph analysis. In this paper, we define locally adjustable graph algorithms and show that algorithms of this type can be transformed into differentially private algorithms. Our formalization is motivated by a set of results that we present in the central and local models of differential privacy for a number of problems, including k-core decomposition, low out-degree ordering, and densest subgraphs. First, we design an $\\varepsilon$-edge differentially private (DP) algorithm that returns a subset of nodes that induce a subgraph of density at least $ \\frac{D^{*}}{1+\\eta}-O(\\operatorname{poly}(\\log n)/\\varepsilon)$, where $D^{*}$ is the density of the densest subgraph in the input graph (for any constant $\\eta\\gt 0$). This algorithm achieves a two-fold improvement on the multiplicative approximation factor of the previously best-known private densest subgraph algorithms while maintaining a near-linear runtime. Then, we present an $\\varepsilon$-locally edge differentially private (LEDP) algorithm for k-core decompositions. Our LEDP algorithm provides approximates the core numbers (for any constant $\\eta\\gt 0$) with $(2+\\eta)$ multiplicative and $O(\\operatorname{poly}(\\log n)/\\varepsilon)$ additive error. This is the first differentially private algorithm that outputs private k-core decomposition statistics. We also modify our algorithm to return a differentially private low out-degree ordering of the nodes, where orienting the edges from nodes earlier in the ordering to nodes later in the ordering results in out-degree at most $O(d+$ poly $(\\log n)/\\varepsilon$) (where d is the degeneracy of the graph). A small modification to the algorithm also yields a $\\varepsilon$-LEDP algorithm for $(4+\\eta,O(\\operatorname{poly}(\\log n)/\\varepsilon))$ approximate densest subgraph (which returns both the set of nodes in the subgraph and its density). Our algorithm uses $O(\\log^{2}n)$ rounds of communication between the curator and individual nodes.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00077"}, {"primary_key": "1660409", "vector": [], "sparse_vector": [], "title": "Separated borders: Exponential-gap fanin-hierarchy theorem for approximative depth-3 circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2001) proposed an ambitious program, the Geometric Complexity Theory (GCT), to prove $P\\neq NP$ and related conjectures using algebraic geometry and representation theory. Gradually, GCT has introduced new structures and questions in complexity. GCT tries to capture the algebraic/geometric notion of 'approximation' by defining border classes. Surprisingly, (<PERSON>'20) proved the universal power of the border of top-fanin- 2 depth-3 circuits $(\\overline{\\Sigma^{[2]}\\Pi\\Sigma})$; which is in complete contrast to its classical model. Recently, (<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>, FOCS'21) put an upper bound, by showing that bounded-top-fanin border depth-3 circuits $(\\overline{\\Sigma^{[k]}\\Pi\\Sigma}$ for constant $k)$ can be computed by a polynomial-size algebraic branching program (ABP). It was left open to show an exponential separation between the class of ABPs and $\\overline{\\Sigma^{[k]}\\Pi\\Sigma}$. In this article, we show a strongly-exponential separation between any two consecutive border classes, $\\overline{\\Sigma^{[k]}\\Pi\\Sigma}$ and $\\Sigma^{[k+1]}\\Pi\\Sigma$, establishing an optimal hierarchy of constant topfanin border depth- 3 circuits. Put in GCT language: we prove an exponential-hierarchy for padded- k-th-secant-varieties of the Chow variety of $\\mathbb{F}^{n+1} $. This positively answers [Open question 2 of Dutta,Dwivedi,Saxena FOCS'21] and [Problem 8.10 with constant r, of Landsberg, Annal.Ferrara'15]. Full version: https://www.cse.iitk.ac.in/users/nitin/papers/exphierarchy.pdf", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00026"}, {"primary_key": "1660410", "vector": [], "sparse_vector": [], "title": "Binary Codes with Resilience Beyond 1/4 via Interaction.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the reliable transmission problem, a sender, <PERSON>, wishes to transmit a bit-string x to a remote receiver, <PERSON>, over a binary channel with adversarial noise. The solution to this problem is to encode x using an error correcting code. As it is long known that the distance of binary codes is at most 1/2, reliable transmission is possible only if the channel corrupts (flips) at most a 1/4-fraction of the communicated bits.We revisit the reliable transmission problem in the two-way setting, where both <PERSON> and <PERSON> can send bits to each other. Our main result is the construction of two-way error correcting codes that are resilient to a constant fraction of corruptions strictly larger than 1/4. Moreover, our code has constant rate and requires <PERSON> to only send one short message. We mention that our result resolves an open problem by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [APPROX-RANDOM, 2015] and by <PERSON>, <PERSON>, and <PERSON> [STOC, 2022].Curiously, our new two-way code requires a fresh perspective on classical error correcting codes: While classical codes have only one distance guarantee for all pairs of codewords (i.e., the minimum distance), we construct codes where the distance between a pair of codewords depends on the “compatibility” of the messages they encode. We also prove that such codes are necessary for our result.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00008"}, {"primary_key": "1660411", "vector": [], "sparse_vector": [], "title": "Low Treewidth Embeddings of Planar and Minor-Free Metrics.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [FOCS’20] constructed a stochastic embedding of minor-free graphs of diameter D into graphs of treewidth $O_{\\epsilon}(\\log n)$ with expected additive distortion $+\\epsilon D$. <PERSON> et al. then used the embedding to design the first quasi-polynomial time approximation scheme (QPTAS) for the capacitated vehicle routing problem. <PERSON><PERSON><PERSON> and <PERSON> [STOC’21] used the embedding (in a different way) to design a QPTAS for the metric <PERSON>’s problems in minor-free graphs. In this work, we devise a new embedding technique to improve the treewidth bound of <PERSON><PERSON><PERSON> et al. exponentially to $O_{\\epsilon}(\\log \\log n)^{2}$. As a corollary, we obtain the first efficient PTAS for the capacitated vehicle routing problem in minor-free graphs. We also significantly improve the running time of the QPTAS for the metric <PERSON>’s problems in minor-free graphs from $n^{O_{\\epsilon}(\\log (n))}$ to $n^{O_{\\epsilon}(\\log \\log (n))^{3}}$. Applying our embedding technique to planar graphs, we obtain a deterministic embedding of planar graphs of diameter D into graphs of treewidth $\\left.O\\left((\\log \\log n)^{2}\\right) / \\epsilon\\right)$ and additive distortion $+\\epsilon D$ that can be constructed in nearly linear time. Important corollaries of our result include a bicriteria PTAS for metric Baker’s problems and a PTAS for the vehicle routing problem with bounded capacity in planar graphs, both run in almost-linear time. The running time of our algorithms is significantly better than previous algorithms that require quadratic time. A key idea in our embedding is the construction of an (exact) emulator for tree metrics with treewidth $O(\\log \\log n)$ and hop-diameter $O(\\log \\log n)$. This result may be of independent interest.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00105"}, {"primary_key": "1660412", "vector": [], "sparse_vector": [], "title": "Algorithms and Barriers in the Symmetric Binary Perceptron Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The binary (or Ising) perceptron is a toy model of a single-layer neural network and can be viewed as a random constraint satisfaction problem with a high degree of connectivity. The model and its symmetric variant, the symmetric binary perceptron (SBP), have been studied widely in statistical physics, mathematics, and machine learning.The SBP exhibits a dramatic statistical-to-computational gap: the densities at which known efficient algorithms find solutions are far below the threshold for the existence of solutions. Furthermore, the SBP exhibits a striking structural property: at all positive constraint densities almost all of its solutions are ‘totally frozen’ singletons separated by large Hamming distance [1], [2]. This suggests that finding a solution to the SBP may be computationally intractable. At the same time, however, the SBP does admit polynomial-time search algorithms at low enough densities. A conjectural explanation for this conundrum was put forth in [3]: efficient algorithms succeed in the face of freezing by finding exponentially rare clusters of large size. However, it was discovered recently that such rare large clusters exist at all subcritical densities, even at those well above the limits of known efficient algorithms [4]. Thus the driver of the statistical-to-computational gap exhibited by this model remains a mystery. In this paper, we conduct a different landscape analysis to explain the statistical-to-computational gap exhibited by this problem. We show that at high enough densities the SBP exhibits the multi Overlap Gap Property (m-OGP), an intricate geometrical property known to be a rigorous barrier for large classes of algorithms. Our analysis shows that the m-OGP threshold (a) is well below the satisfiability threshold; and (b) matches the best known algorithmic threshold up to logarithmic factors as $m\\rightarrow\\infty$. We then prove that the m-OGP rules out the class of stable algorithms for the SBP above this threshold. We conjecture that the $m\\rightarrow\\infty$ limit of the m-OGP threshold marks the algorithmic threshold for the problem. Furthermore, we investigate the stability of known efficient algorithms for perceptron models and show that the Kim-Roche algorithm [5], devised for the asymmetric binary perceptron, is stable in the sense we consider.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00061"}, {"primary_key": "1660413", "vector": [], "sparse_vector": [], "title": "Gap Edit Distance via Non-Adaptive Queries: Simple and Optimal.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of approximating edit distance in sublinear time. This is formalized as the $(k,\\ k^{\\mathrm{c}})$-GAP EDIT DISTANCE problem, where the input is a pair of strings $X, \\mathrm{Y}$ and parameters $k, c\\gt 1$, and the goal is to return YES if ED(X, Y) $\\leq k$, NO if ED(X, Y) $\\gt k^{\\mathrm{c}}$, and an arbitrary answer when $k\\lt $ ED(X, Y) $\\leq k^{\\mathrm{c}}$. Recent years have witnessed significant interest in designing sublinear-time algorithms for GAP EDIT DISTANCE.In this work, we resolve the non-adaptive query complexity of GAP EDIT DISTANCE for the entire range of parameters, improving over a sequence of previous results. Specifically, we design a non-adaptive algorithm with query complexity $\\tilde{O}(n/k^{\\mathrm{c}-\\mathrm{O}.5})$, and we further prove that this bound is optimal up to polylogarithmic factors.Our algorithm also achieves optimal time complexity $\\tilde{O}(n/k^{\\mathrm{c}-\\mathrm{O}.5})$ whenever $ c\\geq$ 1.5. For $1 \\lt c\\lt $ 1.5, the running time of our algorithm is $\\tilde{O}(n/k^{2\\mathrm{c}-2})$. In the restricted case of $k^{\\mathrm{c}}=\\Omega(n)$, this matches a known result [Batu, Ergün, Kilian, Magen, Raskhodnikova, Rubinfeld, and Sami; STOC 2003], and in all other (nontrivial) cases, our running time is strictly better than all previous algorithms, including the adaptive ones. However, independent work of Bringmann, Cassis, Fischer, and Nakos [STOC 2022] provides an adaptive algorithm that bypasses the non-adaptive lower bound, but only for small enough k and c.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00070"}, {"primary_key": "1660414", "vector": [], "sparse_vector": [], "title": "Planting Undetectable Backdoors in Machine Learning Models : [Extended Abstract].", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given the computational cost and technical expertise required to train machine learning models, users may delegate the task of learning to a service provider. Delegation of learning has clear benefits, and at the same time raises serious concerns of trust. This work studies possible abuses of power by untrusted learners.We show how a malicious learner can plant an undetectable backdoor into a classifier. On the surface, such a backdoored classifier behaves normally, but in reality, the learner maintains a mechanism for changing the classification of any input, with only a slight perturbation. Importantly, without the appropriate \"backdoor key,\" the mechanism is hidden and cannot be detected by any computationally-bounded observer. We demonstrate two frameworks for planting undetectable backdoors, with incomparable guarantees.•First, we show how to plant a backdoor in any model, using digital signature schemes. The construction guarantees that given query access to the original model and the backdoored version, it is computationally infeasible to find even a single input where they differ. This property implies that the backdoored model has generalization error comparable with the original model. Moreover, even if the distinguisher can request backdoored inputs of its choice, they cannot backdoor a new input—a property we call non-replicability.•Second, we demonstrate how to insert undetectable backdoors in models trained using the Random Fourier Features (RFF) learning paradigm (<PERSON><PERSON><PERSON>, <PERSON>; NeurIPS 2007). In this construction, undetectability holds against powerful white-box distinguishers: given a complete description of the network and the training data, no efficient distinguisher can guess whether the model is \"clean\" or contains a backdoor. The backdooring algorithm executes the RFF algorithm faithfully on the given training data, tampering only with its random coins. We prove this strong guarantee under the hardness of the Continuous Learning With Errors problem (Bruna, Regev, Song, Tang; STOC 2021). We show a similar white-box undetectable backdoor for random ReLU networks based on the hardness of Sparse PCA (Berthet, Rigollet; COLT 2013).Our construction of undetectable backdoors also sheds light on the related issue of robustness to adversarial examples. In particular, by constructing undetectable backdoor for an \"adversarially-robust\" learning algorithm, we can produce a classifier that is indistinguishable from a robust classifier, but where every input has an adversarial example! In this way, the existence of undetectable backdoors represent a significant theoretical roadblock to certifying adversarial robustness.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00092"}, {"primary_key": "1660415", "vector": [], "sparse_vector": [], "title": "Separations in Proof Complexity and TFNP.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is well-known that Resolution proofs can be efficiently simulated by <PERSON><PERSON><PERSON><PERSON><PERSON> (SA) proofs. We show 1 , however, that any such simulation needs to exploit huge coefficients: Resolution cannot be efficiently simulated by SA when the coefficients are written in unary. We also show that Reversible Resolution (a variant of MaxSAT Resolution) cannot be efficiently simulated by Nullstellensatz (NS). These results have consequences for total NP search problems. First, we characterise the classes PPADS, PPAD, SOPL by unary-SA, unary-NS, and Reversible Resolution, respectively. Second, we show that, relative to an oracle, PLS $\\nsubseteq$ PPP, SOPL $\\nsubseteq$ PPA, and EOPL $\\nsubseteq$ UEOPL. In particular, together with prior work, this gives a complete picture of the black-box relationships between all classical TFNP classes introduced in the 1990s. 1 This is an extended abstract. For the full version of this article, please refer to [GHJ+22b].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00111"}, {"primary_key": "1660416", "vector": [], "sparse_vector": [], "title": "Simple Hard Instances for Low-Depth Algebraic Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove super-polynomial lower bounds on the size of propositional proof systems operating with constant-depth algebraic circuits over fields of zero characteristic. Specifically, we show that the subset-sum variant $\\displaystyle \\sum_{i,j,k,\\ell\\in[n]}Z_{i_{J}^{\\prime}k}\\ell x_{i}x_{j}x_{k}x_{\\ell}-\\beta=0$, for Boolean variables, does not have polynomial-size IPS refutations where the refutations are multilinear and written as constant-depth circuits. <PERSON> and <PERSON> (STOC'22) established recently a constant-depth IPS lower bound, but their hard instance does not have itself small constant-depth circuits, while our instance is computable already with small depth-2 circuits. Our argument relies on extending the recent breakthrough lower bounds against constant-depth algebraic circuits by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS'21) to the functional lower bound framework of Forbes, Shpilka, Tzam<PERSON> and <PERSON>ig<PERSON> (ToC'21), and may be of independent interest. Specifically, we construct a polynomial f computable with small-size constant-depth circuits, such that the multilinear polynomial computing $1/f$ over Boolean values and its appropriate set-multilinear projection are hard for constant-depth circuits.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00025"}, {"primary_key": "1660417", "vector": [], "sparse_vector": [], "title": "Continuous LWE is as Hard as LWE &amp; Applications to Learning Gaussian Mixtures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show direct and conceptually simple reductions between the classical learning with errors (LWE) problem and its continuous analog, CLWE (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>, STOC 2021). This allows us to bring to bear the powerful machinery of LWE-based cryptography to the applications of CLWE. For example, we obtain the hardness of CLWE under the classical worst-case hardness of the gap shortest vector problem. Previously, this was known only under quantum worst-case hardness of lattice problems. More broadly, with our reductions between the two problems, any future developments to LWE will also apply to CLWE and its downstream applications. As a concrete application, we show an improved hardness result for density estimation for mixtures of Gaussians. In this computational problem, given sample access to a mixture of Gaussians, the goal is to output a function that estimates the density function of the mixture. Under the (plausible and widely believed) exponential hardness of the classical LWE problem, we show that Gaussian mixture density estimation in $\\mathbb{R}^{n}$ with roughly $\\log n$ Gaussian components given poly $(n)$ samples requires time quasi-polynomial in n. Under the (conservative) polynomial hardness of LWE, we show hardness of density estimation for $n^{\\epsilon}$ Gaussians for any constant $\\epsilon>0$, which improves on <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON> (STOC 2021), who show hardness for at least $\\sqrt{n}$ Gaussians under polynomial (quantum) hardness assumptions. Our key technical tool is a reduction from classical LWE to LWE with k-sparse secrets where the multiplicative increase in the noise is only $O(\\sqrt{k})$, independent of the ambient dimension n.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00112"}, {"primary_key": "1660418", "vector": [], "sparse_vector": [], "title": "Punctured Low-Bias Codes Behave Like Random Linear Codes.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "Random linear codes are a workhorse in coding theory, and are used to show the existence of codes with the best known or even near-optimal trade-offs in many noise models. However, they have little structure besides linearity, and are not amenable to tractable error-correction algorithms. In this work, we prove a general derandomization result applicable to random linear codes. Namely, in settings where the coding-theoretic property of interest is \"local\" (in the sense of forbidding certain bad configurations involving few vectors–code distance and list-decodability being notable examples), one can replace random linear codes (RLCs) with a significantly derandomized variant with essentially no loss in parameters. Specifically, instead of randomly sampling coordinates of the (long) Hadamard code (which is an equivalent way to describe RLCs), one can randomly sample coordinates of any code with low bias. Over large alphabets, the low bias requirement can be weakened to just large distance. Furthermore, large distance suffices even with a small alphabet in order to match the current best known bounds for RLC list-decodability. In particular, by virtue of our result, all current (and future) achievability bounds for list-decodability of random linear codes extend automatically to random puncturings of any low-bias (or large alphabet) \"mother\" code. We also show that our punctured codes emulate the behavior of RLCs on stochastic channels, thus giving a derandomization of RLCs in the context of achieving Shannon capacity as well. Thus, we have a randomness-efficient way to sample codes achieving capacity in both worst-case and stochastic settings that can further inherit algebraic or other algorithmically useful structural properties of the mother code. This is an extended abstract. The full version is available at https://arxiv.org/abs/2109.11725.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00011"}, {"primary_key": "1660419", "vector": [], "sparse_vector": [], "title": "Optimal learning of quantum Hamiltonians from high-temperature Gibbs states.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of learning a Hamiltonian H to precision $\\varepsilon$, supposing we are given copies of its Gibbs state $\\rho =\\exp(-\\beta H)/\\mathrm{Tr}(\\exp(-\\beta H))$ at a known inverse temperature $\\beta$. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [AAKS21] recently studied the sample complexity (number of copies of $\\rho$ needed) of this problem for geometrically local N-qubit Hamiltonians. In the high-temperature (low $\\beta$) regime, their algorithm has sample complexity poly (N, 1/$\\beta$, 1/$\\varepsilon$) and can be implemented with polynomial, but suboptimal, time complexity. In this paper, we study the same question for a more general class of Hamiltonians. We show how to learn the coefficients of a Hamiltonian to error $\\varepsilon$ with sample complexity $S=O(\\log N/(\\beta\\varepsilon)^{2}$) and time complexity linear in the sample size, O(SN). Furthermore, we prove a matching lower bound showing that our algorithm's sample complexity is optimal, and hence our time complexity is also optimal. In the appendix, we show that virtually the same algorithm can be used to learn H from a real-time evolution unitary $e^{-i t H}$ in a small t regime with similar sample and time complexity.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00020"}, {"primary_key": "1660420", "vector": [], "sparse_vector": [], "title": "On Bounded <PERSON><PERSON><PERSON> Proofs for Tseitin Formulas on the Grid; Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study Frege proofs using depth-d Boolean formulas for the Tseitin contradiction on $n\\times n$ grids. We prove that if each line in the proof is of size M then the number of lines is exponential in $n/(\\log M)^{O(d)}$. This strengthens a recent result of <PERSON><PERSON><PERSON> et al. [12]. The key technical step is a multi-switching lemma extending the switching lemma of <PERSON><PERSON> [8] for a space of restrictions related to the Tseitin contradiction. The strengthened lemma also allows us to improve the lower bound for standard proof size of bounded depth Frege refutations from exponential in $\\tilde{\\Omega}(n^{1/59d})$ to exponential in $\\tilde{\\Omega}(n^{1/(2d-1)})$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00110"}, {"primary_key": "1660421", "vector": [], "sparse_vector": [], "title": "The Implicit Graph Conjecture is False.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An efficient implicit representation of an n-vertex graph G in a family $\\mathcal{F}$ of graphs assigns to each vertex of G a binary code of length O(log n) so that the adjacency between every pair of vertices can be determined only as a function of their codes. This function can depend on the family but not on the individual graph. Every family of graphs admitting such a representation contains at most $2^{O(n\\log(n))}$ graphs on n vertices, and thus has at most factorial speed of growth. The Implicit Graph Conjecture states that, conversely, every hereditary graph family with at most factorial speed of growth admits an efficient implicit representation. We refute this conjecture by establishing the existence of hereditary graph families with factorial speed of growth that require codes of length $n^{\\Omega(1)}$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00109"}, {"primary_key": "1660422", "vector": [], "sparse_vector": [], "title": "Sampling Lov<PERSON>z local lemma for general constraint satisfaction solutions in near-linear time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "We give a fast algorithm for sampling uniform solutions of general constraint satisfaction problems (CSPs) in a local lemma regime. Ihe expected running time of our algorithm is near-linear in n and a fixed polynomial in $\\Delta$, where n is the number of variables and $\\Delta$ is the max degree of constraints. Previously, up to similar conditions, sampling algorithms with running time polynomial in both n and $\\Delta$, only existed for the almost atomic case, where each constraint is violated by a small number of forbidden local configurations.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00021"}, {"primary_key": "1660423", "vector": [], "sparse_vector": [], "title": "NP-Hardness of Learning Programs and Partial MCSP.", "authors": ["<PERSON><PERSON>"], "summary": "A long-standing open question in computational learning theory is to prove NP-hardness of learning efficient programs, the setting of which is in between proper learning and improper learning. <PERSON> (CO<PERSON>'90, SICOMP'91) explicitly raised this open question and demonstrated its difficulty by proving that there exists no relativizing proof of NP-hardness of learning programs. In this paper, we overcome <PERSON>'s relativization barrier and prove NP-hardness of learning programs under randomized polynomial-time many-one reductions. Our result is provably non-relativizing, and comes somewhat close to the parameter range of improper learning: We observe that mildly improving our inapproximability factor is sufficient to exclude Heuristica, i.e., show the equivalence between average-case and worst-case complexities of N P. We also make progress on another long-standing open question of showing NP-hardness of the Minimum Circuit Size Problem (MCSP). We prove NP-hardness of the partial function variant of MCSP as well as other meta-computational problems, such as the problems MKTP * and MINKT * of computing the time-bounded Kolmogorov complexity of a given partial string, under randomized polynomial-time reductions. Our proofs are algorithmic information (a.k. a. Kolmogorov complexity) theoretic. We utilize black-box pseudorandom generator constructions, such as the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> generator, as a one-time encryption scheme secure against a program which \"does not know\" a random function. Our key technical contribution is to quantify the \"knowledge\" of a program by using conditional Kolmogorov complexity and show that no small program can know many random functions.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00095"}, {"primary_key": "1660424", "vector": [], "sparse_vector": [], "title": "Hardness Self-Amplification from Feasible Hard-Core Sets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the question of hardness self-amplification: Given a Boolean function f that is hard to compute on an o (1)-fraction of inputs drawn from some distribution, can we prove that f is hard to compute on a $(\\displaystyle \\frac{1}{2}-o(1))$-fraction of inputs drawn from the same distribution? We prove hardness self-amplification results for natural distributional problems studied in fine-grained average-case complexity, such as the problem of counting the number of the triangles modulo 2 in a random tripartite graph and the online vector-matrix-vector multiplication problem over $\\mathbb{F}_{2}$. More generally, we show that any problem that can be decomposed into \"computationally disjoint\" subsets of inputs admits hardness self-amplification. This is proved by generalizing the security proof of the Ni<PERSON><PERSON><PERSON><PERSON> pseudorandom generator, in which case nearly disjoint subsets of inputs are considered. At the core of our proof techniques is a new notion of feasible hard-core set, which generalizes Impagliazzo’s hard-core set [<PERSON><PERSON><PERSON><PERSON><PERSON>, FOCS’95]. We show that any weak average-case hard function f has a feasible hard-core set H: any small H-oracle circuit (that is allowed to make queries q to H if $f(q)$ can be computed without the oracle) fails to compute f on a $(\\displaystyle \\frac{1}{2}-o(1))$-fraction of inputs in H.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00058"}, {"primary_key": "1660425", "vector": [], "sparse_vector": [], "title": "Explicit Lower Bounds Against Ω(n)-Rounds of Sum-of-Squares.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We construct an explicit family of 3-XOR instances hard for $\\Omega(n)$-levels of the Sum-of-Squares (SoS) semi-definite programming hierarchy. Not only is this the first explicit construction to beat brute force search (beyond low-order improvements (<PERSON>lsiani 2021, Pratt 2021)), combined with standard gap amplification techniques it also matches the (optimal) hardness of random instances up to imperfect completeness (<PERSON><PERSON><PERSON><PERSON> 2001, <PERSON><PERSON><PERSON>beck FOCS 2008).Our result is based on a new form of small-set high dimensional expansion (SS-HDX) inspired by recent breakthroughs in locally testable and quantum LDPC codes. Adapting the recent framework of Dinur, Filmus, Harsha, and Tulsiani (ITCS 2021) for SoS lower bounds from the Ramanujan complex to this setting, we show any (bounded-degree) SS-HDX can be transformed into a highly unsatisfiable 3-XOR instance that cannot be refuted by $\\Omega(n)$-levels of SoS. We then show <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s (Arxiv 2022) recent qLDPC construction gives the desired explicit family of bounded-degree SS-HDX. Incidentally, this gives the strongest known form of bi-directional high dimensional expansion to date.A full version of this paper is accessible at: https://arxiv.org/abs/2204.11469.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00069"}, {"primary_key": "1660426", "vector": [], "sparse_vector": [], "title": "Solving SDP Faster: A Robust IPM Framework and Efficient Implementation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Runzhou Tao", "<PERSON><PERSON>"], "summary": "This paper introduces a new robust interior point method analysis for semidefinite programming (SDP). This new robust analysis can be combined with either logarithmic barrier or hybrid barrier.Under this new framework, we can improve the running time of semidefinite programming (SDP) with variable size $n\\times n$ and m constraints up to $\\epsilon$ accuracy.We show that for the case $m=\\Omega(n^{2})$, we can solve SDPs in $m^{\\omega}$ time. This suggests solving SDP is nearly as fast as solving the linear system with equal number of variables and constraints. This is the first result that tall dense SDP can be solved in the nearly-optimal running time, and it also improves the stateof-the-art SDP solver [<PERSON>, Kat<PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, FOCS 2020].In addition to our new IPM analysis, we also propose a number of techniques that might be of further interest, such as, maintaining the inverse of a Kronecker product using lazy updates, a general amortization scheme for positive semi-definite matrices.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00029"}, {"primary_key": "1660427", "vector": [], "sparse_vector": [], "title": "Tight Lipschitz Hardness for optimizing Mean Field Spin Glasses.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of algorithmically optimizing the Hamiltonian of a spherical or Ising mean field spin glass. The maximum asymptotic value OPT of this random function is characterized by a variational principle known as the <PERSON><PERSON> formula, proved first by <PERSON><PERSON><PERSON> and in more generality by <PERSON><PERSON>. Recently developed approximate message passing algorithms efficiently optimize these functions up to a value ALG given by an extended Paris<PERSON> formula, which minimizes over a larger space of functional order parameters. These two objectives are equal for spin glasses exhibiting a no overlap gap property. However, ALG can be strictly smaller than OPT, and no efficient algorithm producing a value exceeding ALG is known. We prove that when all interactions have even degree, no algorithm satisfying an overlap concentration property can produce an objective larger than ALG with non-negligible probability. This property holds for all algorithms with suitably Lipschitz dependence on the random disorder coefficients of the objective. It encompasses natural formulations of gradient descent, approximate message passing, and Langevin dynamics run for bounded time and in particular includes the algorithms achieving ALG mentioned above. To prove this result, we substantially generalize the overlap gap property framework introduced by Gamarnik and Sudan to arbitrary ultrametric forbidden structures of solutions.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00037"}, {"primary_key": "1660428", "vector": [], "sparse_vector": [], "title": "What is in #P and what is not?", "authors": ["<PERSON>", "<PERSON>"], "summary": "For several classical nonnegative integer functions we investigate if they are members of the counting complexity class # P or not. We prove # P membership in surprising cases, and in other cases we prove non-membership, relying on standard complexity assumptions or on oracle separations. We initiate the study of the polynomial closure properties of # P on affine varieties, i.e., if all problem instances satisfy algebraic constraints. This is directly linked to classical combinatorial proofs of algebraic identities and inequalities. We investigate # TFNP and obtain oracle separations that prove the strict inclusion of # P in all standard syntactic subclasses of # TFNP minus 1.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00087"}, {"primary_key": "1660429", "vector": [], "sparse_vector": [], "title": "Almost <PERSON><PERSON><PERSON> Expanders from Arbitrary Expanders via Operator Amplification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give an efficient algorithm that transforms any bounded degree expander graph into another that achieves almost optimal (namely, near-quadratic, $d\\leq 1/\\lambda^{2+o(1)}$) trade-off between (any desired) spectral expansion $\\lambda$ and degree d. Furthermore, the algorithm is local: every vertex can compute its new neighbors as a subset of its original neighborhood of radius $O(\\log(1/\\lambda))$. The optimal quadratic trade-off is known as the Ramanujan bound, so our construction gives almost Ramanujan expanders from arbitrary expanders. The locality of the transformation preserves structural properties of the original graph, and thus has many consequences. Applied to Cayley graphs, our transformation shows that any expanding finite group has almost Ramanujan expanding generators. Similarly, one can obtain almost optimal explicit constructions of quantum expanders, dimension expanders, monotone expanders, etc., from existing (suboptimal) constructions of such objects. Another consequence is a “derandomized” random walk on the original (suboptimal) expander with almost optimal convergence rate. Our transformation also applies when the degree is not bounded or the expansion is not constant. We obtain our results by a generalization of <PERSON><PERSON><PERSON><PERSON><PERSON>’s technique in his breakthrough paper [STOC 2017], used to obtain explicit almost optimal binary codes. Specifically, our spectral amplification extends <PERSON><PERSON><PERSON><PERSON><PERSON>’s analysis of bias amplification from scalars to matrices of arbitrary dimension in a very natural way. Curiously, while <PERSON><PERSON><PERSON><PERSON><PERSON>’s explicit bias amplification derandomizes a well-known probabilistic argument (underlying the Gilbert-<PERSON><PERSON><PERSON><PERSON> bound), there seems to be no known probabilistic (or other existential) way of achieving our explicit (high-dimensional”) spectral amplification.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00043"}, {"primary_key": "1660430", "vector": [], "sparse_vector": [], "title": "First Price Auction is 1 - 1 /e2 Efficient.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>"], "summary": "We prove that the PoA of First Price Auctions is 1-1/$ e^{2}\\approx$0.8647, closing the gap between the best known bounds [0.7430, 0.8689].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00024"}, {"primary_key": "1660431", "vector": [], "sparse_vector": [], "title": "The Quantum and Classical Streaming Complexity of Quantum and Classical Max-Cut.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the space complexity of two graph streaming problems: MAX-CUT and its quantum analogue, QUANTUM MAX-CUT. Previous work by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC 19] resolved the classical complexity of the classical problem, showing that any (2 – ε)-approximation requires Ω(n) space (a 2-approximation is trivial with O(log n) space). We generalize both of these qualifiers, demonstrating Ω(n) space lower bounds for (2 – ε)-approximating MAX-CUT and QUANTUM MAX-CUT, even if the algorithm is allowed to maintain a quantum state. As the trivial approximation algorithm for QUANTUM MAX-CUT only gives a 4-approximation, we show tightness with an algorithm that returns a (2 + ε)-approximation to the QUANTUM MAX-CUT value of a graph in O(log n) space. Our work resolves the quantum and classical approximability of quantum and classical Max-Cut using o(n) space.We prove our lower bounds through the techniques of Boolean Fourier analysis. We give the first application of these methods to sequential one-way quantum communication, in which each player receives a quantum message from the previous player, and can then perform arbitrary quantum operations on it before sending it to the next. To this end, we show how Fourier-analytic techniques may be used to understand the application of a quantum channel.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00054"}, {"primary_key": "1660432", "vector": [], "sparse_vector": [], "title": "Motif Cut Sparsifiers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A motif is a frequently occurring subgraph of a given directed or undirected graph G (<PERSON> et al.). Motifs capture higher order organizational structure of G beyond edge relationships, and, therefore, have found wide applications such as in graph clustering, community detection, and analysis of biological and physical networks to name a few (<PERSON> at al., <PERSON><PERSON><PERSON><PERSON><PERSON> at al.). In these applications, the cut structure of motifs plays a crucial role as vertices are partitioned into clusters by cuts whose conductance is based on the number of instances of a particular motif, as opposed to just the number of edges, crossing the cuts.In this paper, we introduce the concept of a motif cut sparsifier. We show that one can compute in polynomial time a sparse weighted subgraph $G^{\\prime}$ with only $\\widetilde{O}\\left(n / \\epsilon^{2}\\right)$ edges such that for every cut, the weighted number of copies of <PERSON> crossing the cut in $G^{\\prime}$ is within a $1+\\epsilon$ factor of the number of copies of <PERSON> crossing the cut in G, for every constant size motif M.Our work carefully combines the viewpoints of both graph sparsification and hypergraph sparsification. We sample edges which requires us to extend and strengthen the concept of cut sparsifiers introduced in the seminal works of <PERSON><PERSON> and <PERSON> et al. to the motif setting. The task of adapting the importance sampling framework common to efficient graph sparsification algorithms to the motif setting turns out to be nontrivial due to the fact that cut sizes in a random subgraph of G depend non-linearly on the sampled edges. To overcome this, we adopt the viewpoint of hypergraph sparsification to define edge sampling probabilities which are derived from the strong connectivity values of a hypergraph whose hyperedges represent motif instances. Finally, an iterative sparsification primitive inspired by both viewpoints is used to reduce the number of edges in G to nearly linear.In addition, we present a strong lower bound ruling out a similar result for sparsification with respect to induced occurrences of motifs 1 . 1 The full version of the paper is found at https://arxiv.org/abs/2204.09951", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00044"}, {"primary_key": "1660433", "vector": [], "sparse_vector": [], "title": "A (Slightly) Improved Bound on the Integrality Gap of the Subtour LP for TSP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this extended abstract, we show that for some $\\epsilon>10^{-36}$ and any metric TSP instance, the max entropy algorithm studied by [1] returns a solution of expected cost at most $\\frac{3}{2}-\\epsilon$ times the cost of the optimal solution to the subtour elimination LP. This implies that the integrality gap of the subtour LP is at most $\\frac{3}{2}-\\epsilon$. This analysis also shows that there is a randomized $\\frac{3}{2}-\\epsilon$ approximation for the 2-edge-connected multi-subgraph problem, improving upon <PERSON><PERSON><PERSON>' algorithm.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00084"}, {"primary_key": "1660434", "vector": [], "sparse_vector": [], "title": "Improved Optimal Testing Results from Global Hypercontractivity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The problem of testing low-degree polynomials has received significant attention over the years due to its importance in theoretical computer science, and in particular in complexity theory. The problem is specified by three parameters: field size q, degree d and proximity parameter δ, and the goal is to design a tester making as few as possible queries to a given function, which is able to distinguish between the case the given function has degree at most d, and the case the given function is δ-far from any degree d function. With respect to these parameters, we say that a tester is optimal if it makes $O(q^{t}+1/\\delta)$ queries, where $t=t(d,q)$ is the testing dimension of d, q (defined as the minimum integer so that for all $g:\\mathbb{F}_{q}^{n}\\rightarrow\\mathbb{F}_{q}$ of degree more than d, there is a subspace of dimension t on which their restriction has degree exceeding d). For the field of size q, such tester was first given by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. for q = 2, and later by <PERSON><PERSON><PERSON> et al. [7] for all prime powers q. In fact, they showed that the natural t-flat tester is an optimal tester for the Reed-Muller code, for an appropriate t. Here, the t-flat tester is the tester that picks a uniformly random affine subspace A of dimension t, and checks that $\\operatorname{deg}(f|_{A})\\leqslant d$. Their analysis proves that the dependency of the t-flat tester on δ and d is optimal, however the dependency on the field size, i.e. the hidden constant in the O, is a tower-type function in q. We improve the result of Haramaty et al., showing that the dependency on the field size is polynomial. Our technique also applies in the more general setting of lifted affine invariant codes, and gives the same polynomial dependency on the field size. This answers a problem raised in [6]. Our approach significantly deviates from the strategy taken in earlier works [2], [7], [6], and is based on studying the structure of the collection of erroneous subspaces, i.e. subspaces A such that f|A has degree greater than d. Towards this end, we observe that these sets are poorly expanding in the affine version of the Grassmann graph and use that to establish structural results on them via global hypercontractivity. We then use this structure to perform local correction on f.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00017"}, {"primary_key": "1660435", "vector": [], "sparse_vector": [], "title": "Having Hope in Hops: New Spanners, Preservers and Lower Bounds for Hopsets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hopsets and spanners are fundamental graph structures, playing a key role in shortest path computation, distributed communication, and more. A (near-exact) hopset for a given graph G is a (small) subset of weighted edges H that when added to the graph G reduces the number of hops (edges) of near-exact shortest paths. Spanners and distance preservers, on the other hand, ask for removing many edges from the graph while approximately preserving shortest path distances.We provide a general reduction scheme from graph hopsets to the known metric compression schemes of spanners, emulators and distance preservers. Consequently, we get new and improved upper bound constructions for the latter, as well as, new lower bound results for hopsets. Our main results include:•For n-vertex directed weighted graphs, one can provide $(1+\\epsilon)$-approximate distance preservers 1 for p pairs in $V\\times V$ with $O_{\\epsilon}(n\\cdot p^{2/5}+(np)^{2/3})$ edges. For $p\\geq n^{5/4}$, this matches the state-of-the art bounds for reachability preservers by [<PERSON><PERSON><PERSON> and Bod<PERSON>, SODA 2018] and the lower bound for exact-distance preservers by [<PERSON><PERSON><PERSON>, SODA 2016].•For n-vertex undirected weighted graphs, one can provide $(1+\\epsilon)$ distance preserves with $\\overline{O}_{\\epsilon}(n^{1+o(1)}+p\\cdot n^{o(1)})$ edges. So far, such bounds could be obtained only for unweighted graphs. Consequently, we also get improved sourcewise spanners [Roditty, Thorup and Zwick, ICALP 2005] and spanners with slack [Chan, Dinitz and Gupta, ESA 2006].•Exact hopsets of linear size admit a worst-case hopbound of $\\beta=\\Omega(n^{1/3})$. This holds even for undirected weighted graphs, improving upon the $\\Omega(n^{1/6})$ lower bound by [Huang and Pettie, SIAM J. Discret. Math 2021]. Interestingly this matches the recent diameter bound achieved for linear directed shortcuts. 1 I.e., subgraphs that preserve the pairwise distances up to a multiplicative stretch of (1+$\\epsilon$).More conceptually, our work makes a significant progress on the tantalizing open problem concerning the formal connection between hopsets and spanners, e.g., as posed by Elkin and Neiman [Bull. EATCS 2020].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00078"}, {"primary_key": "1660436", "vector": [], "sparse_vector": [], "title": "An Approximate Generalization of the Okamura-Seymour Theorem.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the problem of multi-commodity flows in planar graphs. <PERSON><PERSON><PERSON> and <PERSON> showed that if all the demands are incident on one face, then the cut-condition is sufficient for routing demands. We consider the following generalization of this setting and prove an approximate max flow-min cut theorem: for every demand edge, there exists a face containing both its end points. We show that the cut-condition is sufficient for routing $\\Omega(1)$-fraction of all the demands. To prove this, we give a $L_{1} $-embedding of the planar metric which approximately preserves distance between all pair of points on the same face.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00106"}, {"primary_key": "1660437", "vector": [], "sparse_vector": [], "title": "A tight (non-combinatorial) conditional lower bound for <PERSON><PERSON>&apos;s Measure Problem in 3D.", "authors": ["<PERSON>"], "summary": "We revisit the classic geometric problem of computing the volume of the union of n 3-dimensional axis-parallel boxes (<PERSON><PERSON>’s measure problem in $3 D$). It is well known that the problem can be solved in time $O\\left(n^{3 / 2}\\right)$ (<PERSON><PERSON><PERSON>, <PERSON><PERSON>91; <PERSON>13). Can we justify this 30-year old barrier of $n^{3 / 2 \\pm o(1)}$ under plausible fine-grained complexity assumptions? The only previous conditional lower bound (Chan Comp. Geom.’10) shows that this barrier holds for purely combinatorial algorithms, i.e., algorithms avoiding algebraic techniques for fast matrix multiplication. This leaves open an algorithmic improvement exploiting algebraic techniques, and does not give any superlinear bound if the matrix multiplication exponent $\\omega$ turns out to be equal to 2. We resolve this issue by giving a tight conditional lower bound for general algorithms, based on the 3-uniform hyperclique hypothesis. Specifically, we prove that an $O\\left(n^{3 / 2-\\epsilon}\\right)$ algorithm for <PERSON><PERSON>’s measure problem in 3D would give a $O\\left(n^{k-\\epsilon^{\\prime}}\\right)$-time algorithm for counting k-cliques in 3-uniform hypergraphs - this in turn would give a novel $O\\left(\\left(2-\\epsilon^{\\prime \\prime}\\right)^{n}\\right)$-algorithm for Max-3SAT. Our lower bound can be generalized to $n^{\\frac{d}{3-3 / d}}-o(1)$, which matches the upper bound up to a factor of $n^{\\frac{d-3}{6-6 / d}+o(1)}$ and separates the general problem from popular special cases: For all $d \\geq 3$, known $\\tilde{O}\\left(n^{\\frac{d+1}{3}}\\right)$ algorithms (Bringmann Comp. Geom.’12; Chan FOCS’13) compute the problem for arbitrary hypercubes polynomially faster than our lower bound for the general problem.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00059"}, {"primary_key": "1660438", "vector": [], "sparse_vector": [], "title": "A Hash Table Without Hash Functions, and How to Get the Most Out of Your Random Bits.", "authors": ["<PERSON>"], "summary": "This paper considers the basic question of how strong of a probabilistic guarantee can a hash table, storing $n(1+\\Theta(1))\\log n$-bit key/value pairs, offer? Past work on this question has been bottlenecked by limitations of the known families of hash functions: The only hash tables to achieve failure probabilities less than $1/2^{\\text{polylog}n}$ require access to fully-random hash functions-if the same hash tables are implemented using the known explicit families of hash functions, their failure probabilities become $1/{poly}(n)$. To get around these obstacles, we show how to construct a randomized data structure that has the same guarantees as a hash table, but that avoids the direct use of hash functions. Building on this, we able to construct a hash table using $O(n)$ random bits that achieves failure probability $1/n^{n^{1-\\varepsilon}}$ for an arbitrary positive constant $\\varepsilon$. In fact, we show that this guarantee can even be achieved by a succinct dictionary, that is, by a dictionary that uses space within a $1+o(1)$ factor of the information-theoretic optimum. Finally we also construct a succinct hash table whose probabilistic guarantees fall on a different extreme, offering a failure probability of $1/{poly}(n)$ while using only $\\tilde{O}(\\log n)$ random bits. This latter result replicates a guarantee previously achieved by <PERSON><PERSON><PERSON><PERSON><PERSON> et al., but with increased space efficiency and with several surprising technical components.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00097"}, {"primary_key": "1660439", "vector": [], "sparse_vector": [], "title": "Cheeger Inequalities for Vertex Expansion and Reweighted Eigenvalues.", "authors": ["Tsz Chiu Kwok", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The classical <PERSON><PERSON><PERSON><PERSON>s inequality relates the edge conductance of a graph and the second smallest eigenvalue of the Laplacian matrix. Recently, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> discovered a Cheeger-type inequality connecting the vertex expansion of a graph and the maximum reweighted second smallest eigenvalue of the Laplacian matrix.In this work, we first improve their result to a logarithmic dependence on the maximum degree in the graph, which is optimal up to a constant factor. Also, the improved result holds for weighted vertex expansion, answering an open question by <PERSON><PERSON><PERSON><PERSON> and <PERSON>. Building on this connection, we then develop a new spectral theory for vertex expansion. We discover that several interesting generalizations of Cheeger inequalities relating edge conductances and eigenvalues have a close analog in relating vertex expansions and reweighted eigenvalues. These include an analog of <PERSON><PERSON><PERSON><PERSON>’s result on bipartiteness, an analog of higher order <PERSON><PERSON><PERSON>’s inequality, and an analog of improved <PERSON><PERSON><PERSON><PERSON>s inequality. Finally, inspired by this connection, we present negative evidence to the 0/1-polytope edge expansion conjecture by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. We construct 0/1-polytopes whose graphs have very poor vertex expansion. This implies that the fastest mixing time to the uniform distribution on the vertices of these 0/1-polytopes is almost linear in the graph size.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00042"}, {"primary_key": "1660440", "vector": [], "sparse_vector": [], "title": "Derandomizing Directed Random Walks in Almost-Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this article, we present the first deterministic directed Laplacian L systems solver that runs in time almost-linear in the number of non-zero entries of L. Previous reductions imply the first deterministic almost-linear time algorithms for computing various fundamental quantities on directed graphs including stationary distributions, personalized PageRank, hitting times and escape probabilities.We obtain these results by introducing partial symmetrization, a new technique that makes the Laplacian of an Eulerian directed graph \"less directed\" in a useful sense, which may be of independent interest. The usefulness of this technique comes from two key observations: Firstly, the partially symmetrized Laplacian preconditions the original Eulerian Laplacian well in Richardson iteration, enabling us to construct a solver for the original matrix from a solver for the partially symmetrized one. Secondly, the undirected structure in the partially symmetrized Laplacian makes it possible to sparsify the matrix very crudely, i.e. with large spectral error, and still show that <PERSON> iterations convergence when using the sparsified matrix as a preconditioner. This allows us to develop deterministic sparsification tools for the partially symmetrized Laplacian.Together with previous reductions from directed Laplacians to Eulerian Laplacians, our technique results in the first deterministic almost-linear time algorithm for solving linear equations in directed Laplacians. To emphasize the generality of our new technique, we show that two prominent existing (randomized) frameworks for solving linear equations in Eulerian Laplacians can be derandomized in this way: the squaring-based framework of [1] and the sparsified Cholesky-based framework of [2].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00046"}, {"primary_key": "1660441", "vector": [], "sparse_vector": [], "title": "Properly learning monotone functions via local correction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a $2^{\\tilde{O}(\\sqrt{n}/\\varepsilon)}$-time algorithm for properly learning monotone Boolean functions under the uniform distribution over $\\{0,1\\}^{n}$. Our algorithm is robust to adversarial label noise and has a running time nearly matching that of the state-of-the-art improper learning algorithm of <PERSON><PERSON><PERSON><PERSON> and Tamon (JACM 96) and an information-theoretic lower bound of <PERSON><PERSON><PERSON> et al (RANDOM ’15). Prior to this work, no proper learning algorithm with running time smaller than $2^{\\Omega(n)}$ was known to exist. The core of our proper learner is a local computation algorithm for sorting binary labels on a poset. Our algorithm is built on a body of work on distributed greedy graph algorithms; specifically we rely on a recent work of <PERSON><PERSON><PERSON><PERSON> (FOCS’22), which gives an efficient algorithm for computing maximal matchings in a graph in the LCA model of <PERSON><PERSON> et al and <PERSON> et al (ICS’II, SODA’12). The applications of our local sorting algorithm extend beyond learning on the Boolean cube: we also give a tolerant tester for Boolean functions over general posets that distinguishes functions that are $\\varepsilon$/3-close to monotone from those that are $\\varepsilon-$far. Previous tolerant testers for the Boolean cube only distinguished between $\\varepsilon/\\Omega(\\sqrt{n}$)-close and $\\varepsilon-$far.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00015"}, {"primary_key": "1660442", "vector": [], "sparse_vector": [], "title": "Quantum Tanner codes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Tanner codes are long error correcting codes obtained from short codes and a graph, with bits on the edges and parity-check constraints from the short codes enforced at the vertices of the graph. Combining good short codes together with a spectral expander graph yields the celebrated expander codes of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, which are asymptotically good classical LDPC codes. In this work we apply this prescription to the left-right <PERSON><PERSON>ley complex that lies at the heart of the recent construction of a c 3 locally testable code by <PERSON><PERSON> et at. Specifically, we view this complex as two graphs that share the same set of edges. By defining a Tanner code on each of those graphs we obtain two classical codes that together define a quantum code. This construction can be seen as a simplified variant of the <PERSON>telee<PERSON> and <PERSON><PERSON><PERSON> asymptotically good quantum LDPC code, with improved estimates for its minimum distance. This quantum code is closely related to the <PERSON><PERSON> et at. code in more than one sense: indeed, we prove a theorem that simultaneously gives a linearly growing minimum distance for the quantum code and recovers the local testability of the <PERSON>ur et at. code.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00117"}, {"primary_key": "1660443", "vector": [], "sparse_vector": [], "title": "Minimax Rates for Robust Community Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we study the problem of community detection in the stochastic block model with adversarial node corruptions. Our main result is an efficient algorithm that can tolerate an $\\epsilon$-fraction of corruptions and achieves error $O(\\epsilon)+e^{-\\frac{C}{2}(1\\pm o(1))}$ where $C=(\\sqrt{a}-\\sqrt{b})^{2}$ is the signal-to-noise ratio and $a/n$ and $b/n$ are the inter-community and intra-community connection probabilities respectively. These bounds essentially match the minimax rates for the SBM without corruptions. We also give robust algorithms for $\\mathbb{Z}_{2}$-synchronization. At the heart of our algorithm is a new semidefinite program that uses global information to robustly boost the accuracy of a rough clustering. Moreover, we show that our algorithms are doubly-robust in the sense that they work in an even more challenging noise model that mixes adversarial corruptions with unbounded monotone changes, from the semi-random model.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00083"}, {"primary_key": "1660444", "vector": [], "sparse_vector": [], "title": "Post-Quantum Zero Knowledge, Revisited or: How to Do Quantum Rewinding Undetectably.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When do classical zero-knowledge protocols remain secure against quantum attacks? In this work, we develop the techniques, tools, and abstractions necessary to answer this question for foundational protocols:1)We prove that the Goldreich-Micali-Wigderson protocol for graph non-isomorphism and the Feige-Shamir protocol for NP remain zero-knowledge against quantum adversaries. At the heart of our proof is a new quantum rewinding technique that enables extracting information from multiple invocations of a quantum adversary without disturbing its state.2)We prove that the Goldreich-Kahan protocol for NP is post-quantum zero knowledge using a simulator that can be seen as a natural quantum extension of the classical simulator.Our results achieve negligible simulation error, appearing to contradict a recent impossibility result due to <PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS 2021). This brings us to our final contribution:3.We introduce coherent-runtime expected quantum polynomial time, a simulation notion that (a) precisely captures all of our zero-knowledge simulators, (b) cannot break any polynomial hardness assumptions, (c) implies strict polynomial-time ε-simulation and (d) is not subject to the CCLY impossibility. In light of our positive results and the CCLY negative results, we propose coherent-runtime simulation to be the appropriate quantum analogue of classical expected polynomial-time simulation.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00086"}, {"primary_key": "1660445", "vector": [], "sparse_vector": [], "title": "Near-Optimal Deterministic Vertex-Failure Connectivity Oracles.", "authors": ["<PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We revisit the vertex-failure connectivity oracle problem. This is one of the most basic graph data structure problems under vertex updates, yet its complexity is still not well-understood. We essentially settle the complexity of this problem by showing a new data structure whose space, preprocessing time, update time, and query time are simultaneously optimal up to sub-polynomial factors assuming popular conjectures. Moreover, the data structure is deterministic.More precisely, for any integer $d_{\\star}$, the data structure preprocesses a graph G with n vertices and m edges in $\\hat{O}\\left(m d_{\\star}\\right)$ time and uses $\\tilde{O}\\left(\\min \\left\\{m, n d_{\\star}\\right\\}\\right)$ space. Then, given the vertex set D to be deleted where $|D|=d \\leq d_{\\star}$, it takes $\\hat{O}\\left(d^{2}\\right)$ updates time. Finally, given any vertex pair $(u, v)$, it checks if u and v are connected in $G \\backslash D$ in $O(d)$ time. This improves the previously best deterministic algorithm by <PERSON><PERSON> and <PERSON><PERSON> [SICOMP 2020] in both space and update time by a factor of d. It also significantly speeds up the $\\Omega\\left(\\min \\left\\{m n, n^{\\omega}\\right\\}\\right)$ preprocessing time of all known (even randomized) algorithms with update time at most $\\tilde{O}\\left(d^{5}\\right)$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00098"}, {"primary_key": "1660446", "vector": [], "sparse_vector": [], "title": "Computing in Anonymous Dynamic Networks Is Linear.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give the first linear-time counting algorithm for processes in anonymous 1-interval-connected dynamic networks with a leader. As a byproduct, we are able to compute in 3n rounds every function that is deterministically computable in such networks. If explicit termination is not required, the running time improves to 2n rounds, which we show to be optimal up to a small additive constant (this is also the first non-trivial lower bound for counting). As our main tool of investigation, we introduce a combinatorial structure called history tree, which is of independent interest. This makes our paper completely self-contained, our proofs elegant and transparent, and our algorithms straightforward to implement.In recent years, considerable effort has been devoted to the design and analysis of counting algorithms for anonymous 1-interval-connected networks with a leader. A series of increasingly sophisticated works, mostly based on classical mass-distribution techniques, have recently led to a celebrated counting algorithm in $O(n^{4+\\epsilon}\\log^{3}(n))$ rounds (for ϵ > 0), which was the state of the art prior to this paper. Our contribution not only opens a promising line of research on applications of history trees, but also demonstrates that computation in anonymous dynamic networks is practically feasible, and far less demanding than previously conjectured.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00108"}, {"primary_key": "1660447", "vector": [], "sparse_vector": [], "title": "Binary Iterative Hard Thresholding Converges with Optimal Number of Measurements for 1-Bit Compressed Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Compressed sensing has been a very successful high-dimensional signal acquisition and recovery technique that relies on linear operations. However, the actual measurements of signals have to be quantized before storing or processing them. 1(One)-bit compressed sensing is a heavily quantized version of compressed sensing, where each linear measurement of a signal is reduced to just one bit: the sign of the measurement. Once enough of such measurements are collected, the recovery problem in 1-bit compressed sensing aims to find the original signal with as much accuracy as possible. The recovery problem is related to the traditional \"halfspace-learning\" problem in learning theory. For recovery of sparse vectors, a popular reconstruction method from one-bit measurements is the binary iterative hard thresholding (BIHT) algorithm. The algorithm is a simple projected subgradient descent method, and is known to converge well empirically, despite the nonconvexity of the problem. The convergence property of BIHT was not theoretically justified, except with an exorbitantly large number of measurements (i.e., a number of measurement greater than $\\max\\{k^{10},24^{48}, k^{3.5}/\\epsilon\\}$, where k is the sparsity and $\\epsilon$ denotes the approximation error, and even this expression hides other factors). In this paper we show that the BIHT estimates converge to the original signal with only ${\\tilde{O}}\\left(\\frac{k}{\\epsilon}\\right)$ measurements. Note that, this dependence on k and $\\epsilon$ is optimal for any recovery method in 1-bit compressed sensing. With this result, to the best of our knowledge, BIHT is the only practical and efficient (polynomial time) algorithm that requires the optimal number of measurements in all parameters (both k and $\\epsilon$). This is also an example of a gradient descent algorithm converging to the correct solution for a nonconvex problem, under suitable structural conditions.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00082"}, {"primary_key": "1660448", "vector": [], "sparse_vector": [], "title": "Inapproximability of Positive Semidefinite Permanents and Quantum State Tomography.", "authors": ["<PERSON>"], "summary": "Matrix permanents are hard to compute or even estimate in general. It had been previously suggested that the permanents of Positive Semidefinite (PSD) matrices may have efficient approximations. By relating PSD permanents to a task in quantum state tomography, we show that PSD permanents are NP-hard to approximate within a constant factor, and so admit no polynomial-time approximation scheme (unless P=NP). We also establish that several natural tasks in quantum state tomography, even approximately, are NP-hard in the dimension of the Hilbert space. These state tomography tasks therefore remain hard even with only logarithmically few qubits.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00013"}, {"primary_key": "1660449", "vector": [], "sparse_vector": [], "title": "Generalised entropy accumulation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The min-entropy of a quantum system A conditioned on another quantum system E describes how much randomness can be extracted from A with respect to an adversary in possession of E. This quantity plays a crucial role in quantum cryptography: the security proofs of many quantum cryptographic protocols reduce to showing a lower bound on such a min-entropy. Here, we develop a new tool, called generalised entropy accumulation, for computing such bounds. Concretely, we consider a sequential process in which each step outputs a system A i and updates a side information register E. We prove that if this process satisfies a natural \"non-signalling\" condition between past outputs and future side information, the min-entropy of the outputs $A_{1},\\ldots,\\ A_{n}$ conditioned on the side information E at the end of the process can be bounded from below by a sum of von <PERSON> entropies associated with the individual steps. This is a generalisation of the entropy accumulation theorem (EAT) [1], which deals with a more restrictive model of side information: there, past side information cannot be updated in subsequent rounds, and newly generated side information has to satisfy a Markov condition.Due to its more general model of side-information, our generalised EAT can be applied more easily and to a broader range of cryptographic protocols. In particular, it is the first general tool that is applicable to mistrustful device-independent cryptography. To demonstrate this, we give the first security proof for blind randomness expansion [2] against general adversaries. Furthermore, our generalised EAT can be used to give improved security proofs for quantum key distribution [3], and also has applications beyond quantum cryptography.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00085"}, {"primary_key": "1660450", "vector": [], "sparse_vector": [], "title": "Active Linear Regression for ℓp Norms and Beyond.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study active sampling algorithms for linear regression, which aim to query only a small number of entries of a target vector and output a near minimizer to the objective function. For $\\ell_{p}$ norm regression for any $ 0\\lt p \\lt \\infty$, we give an algorithm based on Lewis weight sampling which outputs $\\mathrm{a}(1+\\epsilon)$-approximate solution using just $\\tilde{O}(d/\\epsilon^{2})$ queries to b for $p\\in(0,1)$, $\\tilde{O}$ $(d/\\epsilon)$ queries for $p\\in(1,2)$, and $\\tilde{O}$ $(d^{p/2}/\\epsilon^{p})$ queries for $p\\in(2,\\ \\infty)$. For $p\\in(0,2)$, our bounds are optimal up to logarithmic factors, thus settling the query complexity for this range of p. For $p\\in(2,\\ \\infty)$, our dependence on d is optimal, while our dependence on $\\epsilon$ is off by at most a single $\\epsilon$ factor, up to logarithmic factors. Our result resolves an open question of <PERSON> and <PERSON><PERSON><PERSON><PERSON>, who gave near optimal bounds for the $\\ell_{1}$ norm, but required at least $d^{2}/\\epsilon^{2}$ samples for $\\ell_{p}$ regression with $p\\in(1,2)$, and gave no bounds for $p\\in(2,\\ \\infty)$ or $p\\in(0,1)$. We also provide the first total sensitivity upper bound for loss functions with at most degree p polynomial growth. This improves a recent result of Tukan, Maalouf, and Feldman. By combining this with our techniques for $\\ell_{p}$ regression, we obtain the first active regression algorithms for such loss functions, including the important cases of the Tukey and Huber losses. This answers another question of Chen and Dereziński. Our sensitivity bounds also give improvements to a variety of previous results using sensitivity sampling, including Orlicz norm subspace embeddings, robust subspace approximation, and dimension reduction for smoothed p-norms. Finally, our active sampling results give the first sublinear time algorithms for Kronecker product regression under every $\\ell_{p}$ norm. Previous results required reading the entire b vector in the kernel feature space. 1 1 Extended abstract; full version available at https://arxiv.org/abs/2111.04888.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00076"}, {"primary_key": "1660451", "vector": [], "sparse_vector": [], "title": "Testing Positive Semidefiniteness Using Linear Measurements.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of testing whether a symmetric $d\\times d$ input matrix A is symmetric positive semidefinite (PSD), or is $\\epsilon$-far from the PSD cone, meaning that $\\lambda_{min}(A)\\leq-\\epsilon\\Vert A\\Vert_{p}$, where $\\Vert A\\Vert_{p}$ is the Schatten-p norm of A. In applications one often needs to quickly tell if an input matrix is PSD, and a small distance from the PSD cone may be tolerable. We consider two well-studied query models for measuring efficiency, namely, the matrix-vector and vector-matrix-vector query models. We first consider one-sided testers, which are testers that correctly classify any PSD input, but may fail on a non-PSD input with a tiny failure probability. Up to logarithmic factors, in the matrix-vector query model we show a tight $\\tilde{\\Theta}(1/\\epsilon^{p/(2p+1)})$ bound, while in the vector-matrix-vector query model we show a tight $\\tilde{\\Theta}(d^{1-1/p}/\\epsilon)$ bound, for every $p\\geq 1$. We also show a strong separation between one-sided and two-sided testers in the vector-matrix-vector model, where a two-sided tester can fail on both PSD and non-PSD inputs with a tiny failure probability. In particular, for the important case of the Frobenius norm, we show that any one-sided tester requires $\\tilde{\\Omega}(\\sqrt{d}/\\epsilon)$ queries. However we introduce a bilinear sketch for two-sided testing from which we construct a Frobenius norm tester achieving the optimal $\\tilde{O}(1/\\epsilon^{2})$ queries. We also give a number of additional separations between adaptive and non-adaptive testers. Our techniques have implications beyond testing, providing new methods to approximate the spectrum of a matrix with Frobenius norm error using dimensionality reduction in a way that preserves the signs of eigenvalues.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00016"}, {"primary_key": "1660452", "vector": [], "sparse_vector": [], "title": "Radical Sylvester-<PERSON><PERSON><PERSON> for Cubics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that any cubic radical Sylvester-Gallai configuration is constant dimensional. This solves a conjecture of <PERSON> in degree 3 and generalizes the result from <PERSON><PERSON><PERSON><PERSON>, who proved that quadratic radical Sylvester-Gallai configurations are constant dimensional. To prove our Sylvester-Gallai theorem, we develop several new tools combining techniques from algebraic geometry and elimination theory. Among our technical contributions, we prove a structure theorem characterizing non-radical ideals generated by two cubic forms, generalizing previous structure theorems for intersections of two quadrics. Moreover, building upon the groundbreaking work <PERSON><PERSON><PERSON> and <PERSON><PERSON>, we introduce the notion of wide Ananyan-Hochster algebras and show that these algebras allow us to transfer the local conditions of Sylvester-Gallai configurations into global conditions.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00027"}, {"primary_key": "1660453", "vector": [], "sparse_vector": [], "title": "Incrementally Verifiable Computation via Rate-1 Batch Arguments.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Non-interactive delegation schemes enable producing succinct proofs (that can be efficiently verified) that a machine M transitions from c 1 to c 2 in a certain number of deterministic steps. We here consider the problem of efficiently merging such proofs: given a proof Π 1 that M transitions from c 1 to c 2 , and a proof Π 2 that <PERSON> transitions from c 2 to c 3 , can these proofs be efficiently merged into a single short proof (of roughly the same size as the original proofs) that M transitions from c 1 to c 3 ? To date, the only known constructions of such a mergeable delegation scheme rely on strong non-falsifiable \"knowledge extraction\" assumptions. In this work, we present a provably secure construction based on the standard LWE assumption. As an application of mergeable delegation, we obtain a construction of incrementally verifiable computation (IVC) (with polylogarithmic length proofs) for any (unbounded) polynomial number of steps based on LWE; as far as we know, this is the first such construction based on any falsifiable (as opposed to knowledge-extraction) assumption. The central building block that we rely on, and construct based on LWE, is a rate-l batch argument (BARG): this is a non-interactive argument for NP that enables proving k NP statements $x_{1},\\ldots, x_{k}$ with communication/verifier complexity m + o(m), where m is the length of one witness. rate-1 BARGs are particularly useful as they can be recursively composed a super-constant number of times.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00102"}, {"primary_key": "1660454", "vector": [], "sparse_vector": [], "title": "A Proof of the Kahn-Kalai Conjecture.", "authors": ["Jinyoung Park", "<PERSON><PERSON>"], "summary": "Proving the \"expectation-threshold\" conjecture of <PERSON> and <PERSON><PERSON>, we show that for any increasing property $\\mathcal{F}$ on a finite set X,\\begin{equation*}p_{c}(\\mathcal{F})=O(q(\\mathcal{F})\\log\\ell(\\mathcal{F})),\\end{equation*}where $p_{c}(\\mathcal{F})$ and $q(\\mathcal{F})$ are the threshold and \"expectation threshold\" of $\\mathcal{F}$, and $\\ell(\\mathcal{F})$ is the maximum size of a minimal member of $\\mathcal{F}$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00066"}, {"primary_key": "1660455", "vector": [], "sparse_vector": [], "title": "Order Selection Prophet Inequality: From Threshold Optimization to Arrival Time Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the classical prophet inequality, a gambler faces a sequence of items, whose values are drawn independently from known distributions. Upon the arrival of each item, its value is realized and the gambler either accepts it and the game ends, or irrevocably rejects it and continues to the next item. The goal is to maximize the value of the selected item and compete against the expected maximum value of all items. A tight competitive ratio of $\\frac{1}{2}$ is established in the classical setting and various relaxations have been proposed to surpass the barrier, including the i.i.d. model, the order selection model, and the random order model. In this paper, we advance the study of the order selection prophet inequality, in which the gambler is given the extra power for selecting the arrival order of the items. Our main result is a 0.725-competitive algorithm, that substantially improves the state-of-the-art 0.669 ratio by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (Math. Program. 2021), achieved in the harder random order model. Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (EC 2020) proved that the task of selecting the optimal order is NP-hard. Despite this fact, we introduce a novel algorithm design framework that translates the discrete order selection problem into a continuous arrival time design problem. From this perspective, we can focus on the arrival time design without worrying about the threshold optimization afterwards. As a side result, we achieve the optimal 0.745 competitive ratio by applying our algorithm to the i.i.d. model.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00023"}, {"primary_key": "1660456", "vector": [], "sparse_vector": [], "title": "On the Range Avoidance Problem for Circuits.", "authors": ["<PERSON><PERSON> Ren", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the range avoidance problem (called Avoid): given the description of a circuit with more output gates than input gates, find a string that is not in the range of the circuit. This problem is complete for the class APEPP that corresponds to explicit constructions of objects whose existence follows from the probabilistic method (<PERSON><PERSON>, FOCS 2021). Motivated by applications in explicit constructions and complexity theory, we initiate the study of the range avoidance problem for weak circuit classes, and obtain the following results: 1)Generalising <PERSON>'s connections between circuitanalysis algorithms and circuit lower bounds (<PERSON><PERSON> 2014), we present a framework for solving $\\mathscr{C}$-Avoid in FP NP using circuit-analysis data structures for $\\mathscr{C}$, for \"typical\" multi-output circuit classes $\\mathscr{C}$. As an application, we present a non-trivial FP NP range avoidance algorithm for De Morgan formulas./inlp>An important technical ingredient is a construction of rectangular PCPs of proximity, building on the rectangular PCPs by Bhangale, Harsha, Paradise, and Tal (FOCS 2020).2)Using the above framework, we show that circuit lower bounds for E NP are equivalent to circuit-analysis algorithms with E NP preprocessing. This is the first equivalence result regarding circuit lower bounds for E NP . Our equivalences have the additional advantages that they work in both infinitely-often and almost-everywhere settings, and that they also hold for larger (e.g., subexponential) size bounds.3)Complementing the above results, we show that in some settings, solving $\\mathscr{C}$-Avoid would imply breakthrough lower bounds, even for very weak circuit classes $\\mathscr{C}$. In particular, an algorithm for AC 0 -Avoid with polynomial stretch implies lower bounds against NC 1 , and an algorithm for $NC_{4}^{0}$-Avoid with very small stretch implies lower bounds against NC 1 and branching programs.4)We show that Avoid is in FNP if and only if there is a propositional proof system that breaks every non-uniform proof complexity generator. This result connects the study of range avoidance with fundamental questions in proof complexity.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00067"}, {"primary_key": "1660457", "vector": [], "sparse_vector": [], "title": "Deterministic Low-Diameter Decompositions for Weighted Graphs and Distributed and Parallel Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents new deterministic and distributed low-diameter decomposition algorithms for weighted graphs. In particular, we show that if one can efficiently compute approximate distances in a parallel or a distributed setting, one can also efficiently compute low-diameter decompositions. This consequently implies solutions to many fundamental distance based problems using a polylogarithmic number of approximate distance computations.Our low-diameter decomposition generalizes and extends the line of work starting from [RG20] to weighted graphs in a very model-independent manner. Moreover, our clustering results have additional useful properties, including strong-diameter guarantees, separation properties, restricting cluster centers to specified terminals, and more. Applications include:–The first near-linear work and polylogarithmic depth randomized and deterministic parallel algorithm for low-stretch spanning trees (LSST) with polylogarithmic stretch. Previously, the best parallel LSST algorithm required $m.n^{o(1)}$ work and $n^{o(1)}$ depth and was inherently randomized. No deterministic LSST algorithm with truly sub-quadratic work and sub-linear depth was known.–The first near-linear work and polylogarithmic depth deterministic algorithm for computing an $\\ell_{1}-$embedding into polylogarithmic dimensional space with polylogarithmic distortion. The best prior deterministic algorithms for $\\ell_{1}$-embeddings either require large polynomial work or are inherently sequential.Even when we apply our techniques to the classical problem of computing a ball-carving with strong-diameter $O(\\log^{2}n)$ in an unweighted graph, our new clustering algorithm still leads to an improvement in round complexity from $O(\\log^{10}n)$ rounds [CG21] to $O(\\log^{4}n)$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00107"}, {"primary_key": "1660458", "vector": [], "sparse_vector": [], "title": "Deterministic Small Vertex Connectivity in Almost Linear Time.", "authors": ["Thatchaphol <PERSON>", "Sorrachai <PERSON>i"], "summary": "In the vertex connectivity problem, given an undirected n-vertex m-edge graph G, we need to compute the minimum number of vertices that can disconnect <PERSON> after removing them. This problem is one of the most well-studied graph problems. From 2019, a new line of work [<PERSON><PERSON><PERSON> et al. STOC’19;SODA’20;STOC’21] has used randomized techniques to break the quadratic-time barrier and, very recently, culminated in an almost-linear time algorithm via the recently announced maxflow algorithm by <PERSON> et al. In contrast, all known deterministic algorithms are much slower. The fastest algorithm [Gabow FOCS’00] takes $O(m(n+min\\{c^{5/2}, cn^{3/4}\\}))$ time where c is the vertex connectivity. It remains open whether there exists a subquadratic-time deterministic algorithm for any constant c > 3. In this paper, we give the first deterministic almost-linear time vertex connectivity algorithm for all constants c. Our running time is $m^{1+o(1)}2^{O(c^{2})}$ time, which is almost-linear for all $c=o(\\sqrt{\\log n})$. This is the first deterministic algorithm that breaks the $O(n^{2})$-time bound on sparse graphs where $m=O(n)$, which is known for more than 50 years ago [<PERSON><PERSON><PERSON><PERSON>’69]. Towards our result, we give a new reduction framework to vertex expanders which in turn exploits our new almost-linear time construction of mimicking network for vertex connectivity. The previous construction by <PERSON>rat<PERSON> and <PERSON>ahlström [FOCS’12] requires large polynomial time and is randomized.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00080"}, {"primary_key": "1660459", "vector": [], "sparse_vector": [], "title": "Error Correcting Codes that Achieve BSC Capacity Against Channels that are Poly-Size Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> and <PERSON> (<PERSON><PERSON> 2016) considered codes for channels that are poly-size circuits which modify at most a p-fraction of the bits of the codeword. This class of channels is significantly stronger than <PERSON>'s binary symmetric channel (BSC), but weaker than <PERSON><PERSON>'s channels which are computationally unbounded. <PERSON><PERSON><PERSON> and <PERSON> gave an explicit Monte-Carlo construction of codes with optimal rate of R(p) = 1 − H(p) that achieve list-decoding in this scenario. Here, \"explicit Monte-Carlo\" means that both encoding and decoding algorithms run in polynomial time. However, the encoding and decoding algorithms also receive a uniformly chosen string of polynomial length (which is chosen and published, once and for all, in a pre-processing stage) and their correctness is guaranteed w.h.p. over this random choice. <PERSON><PERSON><PERSON> and <PERSON> asked whether it is possible to obtain uniquely decodable codes for poly-size channels with rate that beats the Gilbert-Varshamov bound $R^{GV}(p)=1-H(2p)$. We give an affirmative answer, Specifically:•For every $0\\leq p\\lt\\frac{1}{4}$, we give an explicit Monte-Carlo construction of uniquely-decodable codes with optimal rate R(p) = 1 − H(p). This matches the rate achieved by <PERSON><PERSON><PERSON> and <PERSON> for the easier task of list-decoding, and also matches the capacity of binary symmetric channels. Moreover, this rate is strictly larger than that of codes for the standard coding scenario (namely, uniquely-decodable codes for Hamming channels).•Even ignoring explicitness, our result implies a characterization of the capacity of poly-size channels, which was not previously understood.Our technique builds on the earlier list-decodable codes of Guruswami and Smith, achieving unique-decoding by extending and modifying the construction so that we can identify the correct message in the list. For this purpose we use ideas from coding theory and pseudorandomness, specifically:•We construct codes for binary symmetric channels that beat the Gilbert-Varshamov bound, and are \"evasive\" in the sense that a poly-size circuit that receives a random (or actually pseudorandom) string, cannot find a codeword within relative distance 2p. This notion of evasiveness is inspired by the recent work of Shaltiel and Silbak (STOC 2021) on codes for space bounded channels.•We develop a methodology (that is inspired by proofs of t-wise independent tail inequalities, and may be of independent interest) to analyze random codes, in scenarios where the success of the channel is measured in an additional random experiment (as in the evasiveness experiment above).•We introduce a new notion of \"small-set non-malleable codes\" that is tailored for our application, and may be of independent interest.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00009"}, {"primary_key": "1660460", "vector": [], "sparse_vector": [], "title": "Sparse random hypergraphs: Non-backtracking spectra and community detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the community detection problem in a sparse q-uniform hypergraph G, assuming that G is generated according to the Hypergraph Stochastic Block Model (HSBM). We prove that a spectral method based on the non-backtracking operator for hypergraphs works with high probability down to the generalized Kesten-Stig<PERSON> detection threshold conjectured by <PERSON><PERSON> et al. (2015). We characterize the spectrum of the non-backtracking operator for the sparse HSBM and provide an efficient dimension reduction procedure using the Ihara-Bass formula for hypergraphs. As a result, community detection for the sparse HSBM on n vertices can be reduced to an eigenvector problem of a 2n×2n non-normal matrix constructed from the adjacency matrix and the degree matrix of the hypergraph. To the best of our knowledge, this is the first provable and efficient spectral algorithm that achieves the conjectured threshold for HSBMs with r blocks generated according to a general symmetric probability tensor.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00060"}, {"primary_key": "1660461", "vector": [], "sparse_vector": [], "title": "Killing a vortex.", "authors": ["Dimitrios M. Thilikos", "<PERSON>"], "summary": "We provide a \"vortex-free\" refinement of the seminal structure theorem for $K_{t} -$minor free graphs by <PERSON> and <PERSON> as follows: we identify a (parameterized) graph H t and we prove that if we replace K t by H t , then the resulting decomposition becomes \"vortex-free\". Up to now, the most general classes of graphs admitting such a result were either bounded Euler genus graphs or the single-crossing minor-free graphs. This result is tight in the sense that, whenever we minor-exclude a graph that is not a minor of some H t , the appearance of vortices is unavoidable. Using the above decomposition theorem, we design an algorithm that, given an $H_{t} -$minor-free graph G, computes the generating function of all perfect matchings of G in polynomial time. This algorithm yields, on $H_{t} -$minor-free graphs, polynomial algorithms for computational problems such as the dimer problem, the exact matching problem, and the computation of the permanent. Our results, combined with known complexity results, imply a complete characterization of minor-closed graph classes where the number of perfect matchings is polynomially computable: They are precisely those graph classes that do not contain every H t as a minor. This provides a sharp complexity dichotomy for the problem of counting perfect matchings in minor-closed classes.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00104"}, {"primary_key": "1660462", "vector": [], "sparse_vector": [], "title": "Algorithms and Lower Bounds for Replacement Paths under Multiple Edge Failure.", "authors": ["Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "This paper considers a natural fault-tolerant shortest paths problem: for some constant integer f, given a directed weighted graph with no negative cycles and two fixed vertices s and t, compute (either explicitly or implicitly) for every tuple of f edges, the distance from s to t if these edges fail. We call this problem f-Fault Replacement Paths (f FRP).We first present an $\\tilde{O}(n^{3}$) time algorithm for 2FRP in n-vertex directed graphs with arbitrary edge weights and no negative cycles. As 2FRP is a generalization of the well-studied Replacement Paths problem (RP) that asks for the distances between s and t for any single edge failure, 2FRP is at least as hard as RP. Since RP in graphs with arbitrary weights is equivalent in a fine-grained sense to All-Pairs Shortest Paths (APSP) [<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> FOCS’10, J. AC<PERSON>’18], 2FRP is at least as hard as APSP, and thus a substantially subcubic time algorithm in the number of vertices for 2FRP would be a breakthrough. Therefore, our algorithm in $\\tilde{O}(n^{3})$ time is conditionally nearly optimal. Our algorithm immediately implies an $\\tilde{O}(n^{f+1})$ time algorithm for the more general f FRP problem, giving the first improvement over the straightforward $O(n^{f+2})$ time algorithm.Then we focus on the restriction of 2FRP to graphs with small integer weights bounded by M in absolute values. We show that similar to $\\mathrm{R}\\mathrm{P}, 2\\mathrm{F}\\mathrm{R}\\mathrm{P}$ has a substantially subcubic time algorithm for small enough M. Using the current best algorithms for rectangular matrix multiplication, we obtain a randomized algorithm that runs in $\\tilde{O}(M^{2/3}n^{2.9153})$ time. This immediately implies an improvement over our $\\tilde{O}(n^{f+1})$ time arbitrary weight algorithm for all $f\\gt1$. We also present a data structure variant of the algorithm that can trade off pre-processing and query time. In addition to the algebraic algorithms, we also give an $n^{8/3-o(1)}$ conditional lower bound for combinatorial 2FRP algorithms in directed unweighted graphs, and more generally, combinatorial lower bounds for the data structure version of $fF\\mathrm{R}\\mathrm{P}$.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00090"}, {"primary_key": "1660463", "vector": [], "sparse_vector": [], "title": "High-Dimensional Geometric Streaming in Polynomial Space.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Many existing algorithms for streaming geometric data analysis have been plagued by exponential dependencies in the space complexity, which are undesirable for processing high-dimensional data sets, i.e., large d. In particular, once $d\\geq\\log n$, there are no known non-trivial streaming algorithms for problems such as maintaining convex hulls and Löwner-John ellipsoids of n points, despite a long line of work in high-dimensional streaming computational geometry since [2]. We simultaneously improve all of these results to poly $(d,\\ \\log n)$ bits of space by trading off with a poly $(d,\\ \\log n)$ factor distortion. We achieve these results in a unified manner, by designing the first streaming algorithm for maintaining a coreset for $\\ell_{\\infty}$ subspace embeddings with poly $(d,\\ \\log n)$ space and poly $(d,\\ \\log n)$ distortion. Our algorithm also gives similar guarantees in the online coreset model. Along the way, we sharpen known results for online numerical linear algebra by replacing a $\\log$ condition number dependence with a $\\log n$ dependence, answering an open question of [13]. Our techniques provide a novel connection between leverage scores, a fundamental object in numerical linear algebra, and computational geometry. For $\\ell_{p}$ subspace embeddings, our improvements in online numerical linear algebra yield nearly optimal tradeoffs between space and distortion for one-pass streaming algorithms. For instance, we obtain a deterministic coreset using $o(d^{2}\\log n)$ space and $o((d\\log n)^{\\frac{1}{2}-\\frac{1}{p}})$ distortion for $p\\gt 2$, whereas previous deterministic algorithms incurred a poly (n) factor in the space or the distortion [26]. Our techniques have implications also in the offline setting, where we give optimal trade-offs between the space complexity and distortion of a subspace sketch data structure, which preprocesses an $n\\times d$ matrix A and outputs $\\Vert \\mathrm{A}\\mathrm{x}\\Vert_{p}$ up to a poly (d) factor distortion for any x. To do this we give an elementary proof of a \"change of density\" theorem of [42] and make it algorithmic. 1 1 Extended abstract; full version available at https://arxiv.org/abs/ 2204.03790.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00075"}, {"primary_key": "1660464", "vector": [], "sparse_vector": [], "title": "Verifiable Quantum Advantage without Structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show the following hold, unconditionally unless otherwise stated, relative to a random oracle with probability 1: •There are NP search problems solvable by BQP machines but not BPP machines.•There exist functions that are one-way, and even collision resistant, against classical adversaries but are easily inverted quantumly. Similar separations hold for digital signatures and CPA-secure public key encryption (the latter requiring the assumption of a classically CPA-secure encryption scheme). Interestingly, the separation does not necessarily extend to the case of other cryptographic objects such as PRGs.•There are unconditional publicly verifiable proofs of quantumness with the minimal rounds of interaction: for uniform adversaries, the proofs are non-interactive, whereas for non-uniform adversaries the proofs are two message public coin.•Our results do not appear to contradict the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> conjecture. Assuming this conjecture, there exist publicly verifiable certifiable randomness, again with the minimal rounds of interaction.By replacing the random oracle with a concrete cryptographic hash function such as SHA2, we obtain plausible Minicrypt instantiations of the above results. Previous analogous results all required substantial structure, either in terms of highly structured oracles and/or algebraic assumptions in Cryptomania and beyond.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00014"}, {"primary_key": "1660465", "vector": [], "sparse_vector": [], "title": "Strong XOR Lemma for Communication with Bounded Rounds : (extended abstract).", "authors": ["Huacheng Yu"], "summary": "In this paper, we prove a strong XOR lemma for bounded-round two-player randomized communication. For a function $f:\\mathcal{X}\\times \\mathcal{Y}\\rightarrow\\{0,1\\}$, the n-fold XOR function $f^{\\oplus n}:\\mathcal{X}^{n}\\times \\mathcal{Y}^{n}\\rightarrow\\{0,1\\}$ maps n input pairs $(X_{1},\\ldots,\\ X_{n},\\ Y_{1},\\ldots\\,\\ Y_{n})$ to the XOR of the n output bits $f(X_{1},\\ Y_{1})\\oplus\\cdots\\oplus f(X_{n},\\ Y_{n})$. We prove that if every r-round communication protocols that computes f with probability 2/3 uses at least C bits of communication, then any r-round protocol that computes $f^{\\oplus n}$ with probability $1/2+\\exp(-O(n))$ must use $n\\cdot(r^{-O(r)}\\cdot C-1)$ bits. When r is a constant and C is sufficiently large, this is $\\Omega(n\\cdot C)$ bits. It matches the communication cost and the success probability of the trivial protocol that computes the n bits $f(X_{i},\\ Y_{i})$ independently and outputs their XOR, up to a constant factor in n. A similar XOR lemma has been proved for f whose communication lower bound can be obtained via bounding the discrepancy [17]. By the equivalence between the discrepancy and the correlation with 2-bit communication protocols [19], our new XOR lemma implies the previous result.", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00114"}, {"primary_key": "1660466", "vector": [], "sparse_vector": [], "title": "Classical Verification of Quantum Computations in Linear Time.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the quantum computation verification problem, a quantum server wants to convince a client that the output of evaluating a quantum circuit C is some result that it claims. This problem is considered very important both theoretically and practically in quantum computation [1], [2], [3]. The client is considered to be limited in computational power, and one desirable property is that the client can be completely classical, which leads to the classical verification of quantum computation (CVQC) problem. In terms of the time complexity of server-side quantum computations (which typically dominate the total time complexity of both the client and the server), the fastest single-server CVQC protocol so far has complexity $O(\\mathrm{p}\\mathrm{o}\\mathrm{l}\\mathrm{y}(\\kappa)|C|^{3})$ where $|C|$ is the size of the circuit to be verified and $\\kappa$ is the security parameter, given by <PERSON><PERSON><PERSON> [4]. This leads to a similar cubic time blowup in many existing protocols including multiparty quantum computation, zero knowledge and obfuscation [5], [6], [7], [8], [9], [10]. Considering the preciousness of quantum computation resources, this cubic complexity barrier could be a big obstacle for theoretical and practical development of protocols for these problems.In this work, by developing new techniques, we give a new CVQC protocol with complexity $O(\\mathrm{p}\\mathrm{o}\\mathrm{l}\\mathrm{y}(\\kappa)|C|)$ (in terms of the total time complexity of both the client and the server), which is significantly faster than existing protocols. Our protocol is secure in the quantum random oracle model [11] assuming the existence of noisy trapdoor claw-free functions [12], which are both extensively used assumptions in quantum cryptography. Along the way, we also give a new classical channel remote state preparation protocol for states in $\\displaystyle \\left\\{|+\\theta\\rangle=\\frac{1}{\\sqrt{2}}(|0\\rangle+e^{\\mathrm{i}\\theta\\pi/4}|1\\rangle)\\ :\\ \\theta\\in\\left\\{0,1\\cdots 7\\right\\}\\right\\}$, another basic primitive in quantum cryptography. Our protocol allows for parallel verifiable preparation of L independently random states in this form (up to a constant overall error and a possibly unbounded server-side simulator), and runs in only O(poly($\\kappa$)L) time and constant rounds; for comparison, existing works (even for possibly simpler state families) all require very large or unestimated time and round complexities [13], [14], [15], [16].", "published": "2022-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS54457.2022.00012"}]