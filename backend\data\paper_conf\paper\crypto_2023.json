[{"primary_key": "1114709", "vector": [], "sparse_vector": [], "title": "Bingo: Adaptivity and Asynchrony in Verifiable Secret Sharing and Distributed Key Generation.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present\\(\\textsf{<PERSON><PERSON>}\\), an adaptively secure and optimally resilient packed asynchronous verifiable secret sharing (PAVSS) protocol that allows a dealer to share\\(f+1\\)secrets with a total communication complexity of\\(O(\\lambda n^2)\\)words, where\\(\\lambda \\)is the security parameter andnis the number of parties. Using\\(\\textsf{Bingo}\\), we obtain an adaptively secure validated asynchronous Byzantine agreement (VABA) protocol that uses\\(O(\\lambda n^3)\\)expected words and constant expected time, which we in turn use to construct an adaptively secure high-threshold asynchronous distributed key generation (ADKG) protocol that uses\\(O(\\lambda n^3)\\)expected words and constant expected time. To the best of our knowledge, our ADKG is the first to allow for an adaptive adversary while matching the asymptotic complexity of the best known static ADKGs.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_2"}, {"primary_key": "1114710", "vector": [], "sparse_vector": [], "title": "Security-Preserving Distributed Samplers: How to Generate Any CRS in One Round Without Random Oracles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A distributed sampler is a way for several mutually distrusting parties to non-interactively generate a common reference string (CRS) that all parties trust. Previous work constructs distributed samplers in the random oracle model, or in the standard model with very limited security guarantees. This is no accident, as standard model distributed samplers with full security were shown impossible. In this work, we provide new definitions for distributed samplers which we show achieve meaningful security guarantees in the standard model. In particular, our notion implies that the hardness of a wide range of security games is preserved when the CRS is replaced with a distributed sampler. We also show how to realize our notion of distributed samplers. A core technical tool enabling our construction is a new notion of single-message zero knowledge.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_16"}, {"primary_key": "1114711", "vector": [], "sparse_vector": [], "title": "Best of Both Worlds - Revisiting the Spymasters Double Agent Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This work introduces the notion of secure multiparty computation: MPC with fall-back security. Fall-back security for ann-party protocol is defined with respect to an adversary structure\\(\\mathcal{Z}\\)wherein security is guaranteed in the presence of both a computationally unbounded adversary with adversary structure\\(\\mathcal{Z}\\), and a computationally bounded adversary corrupting an arbitrarily large subset of the parties. This notion was considered in the work of <PERSON><PERSON> (Crypto 89) via the Spy<PERSON>’s double agent problem where he showed a semi-honest secure protocol for the honest majority adversary structure. Our first main result is a compiler that can transform anyn-party protocol that is semi-honestly secure with statistical security tolerating an adversary structure\\(\\mathcal{Z}\\)to one that (additionally) provides semi-honest fall-back security w.r.t\\(\\mathcal{Z}\\). The resulting protocol has optimal round complexity, up to a constant factor, and is optimal in assumptions and the adversary structure. Our second result fully characterizes when malicious fall-back security is feasible. More precisely, we show that malicious fallback secure protocol w.r.t\\(\\mathcal{Z}\\)exists if and only if\\(\\mathcal{Z}\\)admits unconditional MPC against a semi-honest adversary (namely, iff\\(\\mathcal{Z}\\in \\mathcal{Q}^2\\)).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_11"}, {"primary_key": "1114712", "vector": [], "sparse_vector": [], "title": "Extractors: Low Entropy Requirements Colliding with Non-malleability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two-source extractors are deterministic functions that, given two independent weak sources of randomness, output a (close to) uniformly random string of bits. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC 2015) introduced two-source non-malleable extractors that combine the properties of randomness extraction with tamper resilience. Two-source non-malleable extractors have since then attracted a lot of attention, and have very quickly become fundamental objects in cryptosystems involving communication channels that cannot be fully trusted. Various applications of two-source non-malleable extractors include in particular non-malleable codes, non-malleable commitments, non-malleable secret sharing, network extraction, and privacy amplification with tamperable memory. The best known constructions of two-source non-malleable extractors are due to <PERSON><PERSON>pad<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (STOC 2016), <PERSON> (STOC 2017), and <PERSON> (CCC 2019). All of these constructions require both sources to have min-entropy at least 0.99n, wherenis the bit-length of each source. In this work, we introduce collision-resistant randomness extractors. This allows us to design a compiler that, given a two-source non-malleable extractor, and a collision-resistant extractor, outputs a two-source non-malleable extractor that inherits the non-malleability property from the non-malleable extractor, and the entropy requirement from the collision-resistant extractor. Nested application of this compiler leads to a dramatic improvement of the state-of-the-art mentioned above. We obtain a construction of a two-source non-malleable extractor where one source is required to have min-entropy greater than 0.8n, and the other source is required to have only\\(\\text {polylog} (n)\\)min-entropy. Moreover, the other parameters of our construction, i.e., the output length, and the error remain comparable to prior constructions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_19"}, {"primary_key": "1114713", "vector": [], "sparse_vector": [], "title": "Constant Input Attribute Based (and Predicate) Encryption from Evasive and Tensor LWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Constructing advanced cryptographic primitives such as obfuscation or broadcast encryption from standard hardness assumptions in the post quantum regime is an important area of research, which has met with limited success despite significant effort. It is therefore extremely important to find new, simple to state assumptions in this regime which can be used to fill this gap. An important step was taken recently by <PERSON><PERSON> (Eurocrypt ’22) who identified two new assumptions from lattices, namely evasive\\(\\textsf{LWE}\\)and tensor\\(\\textsf{LWE}\\), and used these to construct broadcast encryption and ciphertext policy attribute based encryption for\\(\\textsf{P}\\)with optimal parameters. Independently, <PERSON><PERSON><PERSON><PERSON> formulated a similar assumption and used it to construct witness encryption (Crypto ’22). Following <PERSON><PERSON>’s work, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> independently provided a construction of witness encryption (Asiacrypt ’22). In this work, we advance this line of research by providing the first construction of multi-input attribute based encryption (\\(\\textsf{miABE}\\)) for the function class\\(\\mathsf{NC_1}\\)foranyconstant arity from evasive\\(\\textsf{LWE}\\). Our construction can be extended to support the function class\\(\\textsf{P}\\)by using evasive and a suitable strengthening of tensor\\(\\textsf{LWE}\\). In more detail, our construction supportskencryptors, for any constantk, where each encryptor uses the master secret key\\(\\textsf{msk}\\)to encode its input\\((\\textbf{x}_i, m_i)\\), the key generator computes a key\\(\\textsf{sk}_f\\)for a function\\(f \\in {\\textsf{NC}}_1\\)and the decryptor can recover\\((m_1,\\ldots ,m_k)\\)if and only if\\(f(\\textbf{x}_1,\\ldots ,\\textbf{x}_k)=1\\). The only known construction for\\(\\textsf{miABE}\\)for\\({\\textsf{NC}}_1\\)by Agrawal, Yadav and Yamada (Crypto ’22) supports arity 2 and relies on pairings in the generic group model (or with a non-standard knowledge assumption) in addition to\\(\\textsf{LWE}\\). Furthermore, it is completely unclear how to go beyond arity 2 using this approach due to the reliance on pairings. Using a compiler from Agrawal, Yadav and Yamada (Crypto ’22), our\\(\\textsf{miABE}\\)can be upgraded to multi-inputpredicateencryption for the same arity and function class. Thus, we obtain the first constructions for constant-arity predicate and attribute based encryption for a generalized class such as\\({\\textsf{NC}}_1\\)or\\(\\textsf{P}\\)from simple assumptions that may be conjectured post-quantum secure. Along the way, we show that the tensor\\(\\textsf{LWE}\\)assumption can be reduced to standard\\(\\textsf{LWE}\\)in an important special case which was not known before. This adds confidence to the plausibility of the assumption and may be of wider interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_17"}, {"primary_key": "1114714", "vector": [], "sparse_vector": [], "title": "Attribute-Based Multi-input FE (and More) for Attribute-Weighted Sums.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (Crypto 2020) provided the first functional encryption scheme for attribute-weighted sums (AWS), where encryption takes as inputN(unbounded) attribute-value pairs\\(\\{\\textbf{x}_i, \\textbf{z}_i\\}_{i \\in [N]}\\)where\\(\\textbf{x}_i\\)is public and\\(\\textbf{z}_i\\)is private, the secret key is associated with an arithmetic branching programsf, and decryption returns the weighted sum\\({\\sum }_{{i \\in [N]}} f(\\textbf{x}_i)^\\top \\textbf{z}_i\\), leaking no additional information about the\\(\\textbf{z}_i\\)’s. We extend FE for AWS to the significantly more challenging multi-party setting and provide the first construction forattribute-basedmulti-input FE (MIFE) supporting AWS. For\\(i \\in [n]\\), encryptorican choose an attribute\\(\\textbf{y}_i\\)together with AWS input\\(\\{\\textbf{x}_{i,j}, \\textbf{z}_{i,j}\\}\\)where\\(j \\in [N_i]\\)and\\(N_i\\)is unbounded, the key generator can choose an access control policy\\(g_i\\)along with its AWS function\\(h_i\\)for each\\(i \\in [n]\\), and the decryptor can compute Previously, the only known attribute based MIFE was for the inner product functionality (Abdallaet al.Asiacrypt 2020), where additionally,\\(\\textbf{y}_i\\)had to be fixed during setup and must remain the same for all ciphertexts in a given slot. Our attribute based MIFE implies the notion of multi-inputattribute based encryption(MIABE) recently studied by Agrawal, Yadav and Yamada (Crypto 2022) and Francati, Friolo, Malavolta and Venturi (Eurocrypt 2023), for a conjunction of predicates represented as arithmetic branching programs (ABP). Along the way, we also provide the first constructions of multi-client FE (MCFE)\\(^3\\)and dynamic decentralized FE (DDFE) for the AWS functionality. Previously, the best known MCFE and DDFE schemes were for inner products (Chotardet al.ePrint 2018, Abdalla, Benhamouda and Gay, Asiacrypt 2019, and Chotardet al.Crypto 2020). Our constructions are based on pairings and proven selectively secure under the matrix DDH assumption.(\\(^3\\)The literature considers two notions termed as MCFE, one strictly stronger than the other. The stronger notion implies MIFE while the weaker does not. Here, we refer to the stronger notion, making MCFE a strict generalization of MIFE.)", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_15"}, {"primary_key": "1114715", "vector": [], "sparse_vector": [], "title": "Fork-Resilient Continuous Group Key Agreement.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Continuous Group Key Agreement (CGKA) lets an evolving group of clients agree on a sequence of group keys. An important application of CGKA is scalable end-to-end (E2E) encrypted group messaging. A major problem preventing the use of CGKA over unreliable infrastructure are so-called forks. Aforkoccurs when group members have diverging views of the group’s history (and thus its current state); e.g. due to network or server failures. Once communication channels are restored, membersresolvea fork by agreeing on the state of the group again. Today’s CGKA protocols make fork resolution challenging, as natural resolution strategies seem to conflict with the way the protocols enforce group state agreement and forward secrecy. Meanwhile, secure group messaging protocols which do support fork resolution do not scale nearly as well as CGKA does. In this work, we pave the way to practical scalable E2E messaging over unreliable infrastructure. To that end, we generalize CGKA toFork Resilient-CGKA which allows clients to process significantly more types of out-of-order network traffic. This is important for many natural fork resolution procedures as they are based, in part, on replaying missed traffic. Next, we give two FR-CGKA constructions: a practical one based on the CGKA underlying the MLS messaging standard and an optimally secure one (albeit with only theoretical efficiency). To further assist with fork resolution, we introduce a simple new abstraction to describe a client’s local protocol state. The abstraction describes all and only the information relevant to natural fork resolution, making it easier for higher-level fork resolution procedures to work with and reason about. We define a black-box extension of an FR-CGKA which maintains such a description of a client’s internal state. Finally, as a proof of concept, we give a basic fork resolution protocol.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_13"}, {"primary_key": "1114716", "vector": [], "sparse_vector": [], "title": "Cloning Games: A General Framework for Unclonable Primitives.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The powerful no-cloning principle of quantum mechanics can be leveraged to achieve interesting primitives, referred to as unclonable primitives, that are impossible to achieve classically. In the past few years, we have witnessed a surge of new unclonable primitives. While prior works have mainly focused on establishing feasibility results, another equally important direction, that of understanding the relationship between different unclonable primitives is still in its nascent stages. Moving forward, we need a more systematic study of unclonable primitives. To this end, we introduce a new framework calledcloning games. This framework captures many fundamental unclonable primitives such as quantum money, copy-protection, unclonable encryption, single-decryptor encryption, and many more. By reasoning about different types of cloning games, we obtain many interesting implications to unclonable cryptography, including the following: We obtain the first construction of information-theoretically secure single-decryptor encryption in the one-time setting. We construct unclonable encryption in the quantum random oracle model based on BB84 states, improving upon the previous work, which used coset states. Our work also provides a simpler security proof for the previous work. We construct copy-protection for single-bit point functions in the quantum random oracle model based on BB84 states, improving upon the previous work, which used coset states, and additionally, providing a simpler proof. We establish a relationship between different challenge distributions of copy-protection schemes and single-decryptor encryption schemes. Finally, we present a new construction of one-time encryption with certified deletion.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_3"}, {"primary_key": "1114717", "vector": [], "sparse_vector": [], "title": "How to Recover a Secret with O(n) Additions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Threshold cryptography is typically based on the idea of secret-sharing a private-key\\(s\\in F\\)“in the exponent” of some cryptographic groupG, or more generally, encodingsin some linearly homomorphic domain. In each invocation of the threshold system (e.g., for signing or decrypting) an “encoding” of the secret is being recovered and so the complexity, measured as the number of group multiplications overG, is equal to the number ofF-additions that are needed to reconstruct the secret. Motivated by this scenario, we initiate the study ofn-party secret-sharing schemes whose reconstruction algorithm makes a minimal number ofadditions. The complexity of existing schemes either scales linearly with\\(n\\log |F|\\)(e.g., <PERSON><PERSON><PERSON>, CACM’79) or, at least, quadratically withnindependently of the size of the domainF(e.g., C<PERSON><PERSON>-<PERSON>, EUROCRYPT ’20). This leaves open the existence of a secret sharing whose recovery algorithm can be computed by performing onlyO(n) additions. We resolve the question in the affirmative and present such a near-threshold secret sharing scheme that provides privacy against unauthorized sets of density at most\\(\\tau _p\\), and correctness for authorized sets of density at least\\(\\tau _c\\), for any given arbitrarily close constants\\(\\tau _p<\\tau _c\\). Reconstruction can be computed by making at mostO(n) additions and, in addition, (1) the share size is constant, (2) the sharing procedure also makes onlyO(n) additions, and (3) the scheme is a blackbox secret-sharing scheme, i.e., the sharing and reconstruction algorithms work universally for all finite abelian groupsF. Prior to our work, no such scheme was known even without features (1)–(3) and even for the ramp setting where\\(\\tau _p\\)and\\(\\tau _c\\)are far apart. As a by-product, we derive the first blackbox near-threshold secret-sharing scheme with linear-time sharing. We also present several concrete instantiations of our approach that seem practically efficient (e.g., for threshold discrete-log-based signatures). Our constructions are combinatorial in nature. We combine graph-based erasure codes that support “peeling-based” decoding with a new randomness extraction method that is based on inner-product with a small-integer vector. We also introduce a general concatenation-like transform for secret-sharing schemes that allows us to arbitrarily shrink the privacy-correctness gap with a minor overhead. Our techniques enrich the secret-sharing toolbox and, in the context of blackbox secret sharing, provide a new alternative to existing number-theoretic approaches.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_8"}, {"primary_key": "1114718", "vector": [], "sparse_vector": [], "title": "Analysis of the Security of the PSSI Problem and Cryptanalysis of the Durandal Signature Scheme.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new attack against the PSSI problem, one of the three problems at the root of security of <PERSON>randal, an efficient rank metric code-based signature scheme with a public key size of 15 kB and a signature size of 4 kB, presented at EUROCRYPT’19. Our attack recovers the private key using a leakage of information coming from several signatures produced with the same key. Our approach is to combine pairs of signatures and perform Cramer-like formulas in order to build subspaces containing a secret element. We break all existing parameters of <PERSON>rand<PERSON>: the two published sets of parameters claiming a security of 128 bits are broken in respectively\\(2^{66}\\)and\\(2^{73}\\)elementary bit operations, and the number of signatures required to finalize the attack is 1,792 and 4,096 respectively. We implemented our attack and ran experiments that demonstrated its success with smaller parameters.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_5"}, {"primary_key": "1114719", "vector": [], "sparse_vector": [], "title": "Network-Agnostic Security Comes (Almost) for Free in DKG and MPC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Distributed key generation (DKG) protocols are an essential building block for threshold cryptosystems. Many DKG protocols tolerate up to\\(t_s<n/2\\)corruptions assuming a well-behaved synchronous network, but become insecure as soon as the network delay becomes unstable. On the other hand, solutions in the asynchronous model operate under arbitrary network conditions, but only tolerate\\(t_a<n/3\\)corruptions, even when the network is well-behaved. In this work, we ask whether one can design a protocol that achieves security guarantees in either scenario. We show a complete characterization ofnetwork-agnosticDKG protocols, showing that the tight bound is\\(t_a+2t_s <n\\). As a second contribution, we provide an optimized version of the network-agnostic multi-party computation (MPC) protocol by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [CRYPTO’20] which improves over the communication complexity of their protocol by a linear factor. Moreover, using our DKG protocol, we can instantiate our MPC protocol in theplain PKI model, i.e., without the need to assume an expensive trusted setup. Our protocols incur comparable communication complexity as state-of-the-art DKG and MPC protocols with optimal resilience in their respective purely synchronous and asynchronous settings, thereby showing that network-agnostic security comes(almost) for free.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_3"}, {"primary_key": "1114720", "vector": [], "sparse_vector": [], "title": "When Messages Are Keys: Is HMAC a Dual-PRF?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In Internet security protocols including TLS 1.3, KEMTLS, MLS and Noise,\\(\\textsf{HMAC}\\)is being assumed to be a dual-PRF, meaning a PRF not only when keyed conventionally (through its first input), but also when “swapped” and keyed (unconventionally) through its second (message) input. We give the first in-depth analysis of the dual-PRF assumption on\\(\\textsf{HMAC}\\). For the swap case, we note that security does not hold in general, but completely characterize when it does; we show that\\(\\textsf{HMAC}\\)is swap-PRF secure if and only if keys are restricted to sets satisfying a condition called feasibility, that we give, and that holds in applications. The sufficiency is shown by proof and the necessity by attacks. For the conventional PRF case, we fill a gap in the literature by proving PRF security of\\(\\textsf{HMAC}\\)for keys of arbitrary length. Our proofs are in the standard model, make assumptions only on the compression function underlying the hash function, and give good bounds in the multi-user setting. The positive results are strengthened through achieving a new notion of variable key-length PRF security that guarantees security even if different users use keys of different lengths, as happens in practice.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_22"}, {"primary_key": "1114721", "vector": [], "sparse_vector": [], "title": "HERMES: Efficient Ring Packing Using MLWE Ciphertexts and Application to Transciphering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most of the current fully homomorphic encryption (FHE) schemes are based on either the learning-with-errors (LWE) problem or on its ring variant (RLWE) for storing plaintexts. During the homomorphic computation of FHE schemes, RLWE formats provide high throughput when considering several messages, and LWE formats provide a low latency when there are only a few messages. Efficient conversion can bridge the advantages of each format. However, converting LWE formats into RLWE format, which is calledring packing, has been a challenging problem. We propose an efficient solution for ring packing for FHE. The main improvement of this work is twofold. First, we accelerate the existing ring packing methods by using bootstrapping and ring switching techniques, achieving practical runtimes. Second, we propose a new method for efficient ring packing,HERMES, by using ciphertexts in Module-LWE (MLWE) formats, to also reduce the memory. To this end, we generalize the tools of LWE and RLWE formats for MLWE formats. On a single-thread implementation,HERMESconsumes 10.2s for the ring packing of\\(2^{15}\\)LWE-format ciphertexts into an RLWE-format ciphertext. This gives 41x higher throughput compared to the state-of-the-art ring packing for FHE,PEGASUS[S &P’21], which takes 51.7s for packing\\(2^{12}\\)LWE ciphertexts with similar homomorphic capacity. We also illustrate the efficiency ofHERMESby using it for transciphering from LWE symmetric encryption to CKKS fully homomorphic encryption, significantly outperforming the recent proposalsHERA[Asiacrypt’21] andRubato[Eurocrypt’22].", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_2"}, {"primary_key": "1114722", "vector": [], "sparse_vector": [], "title": "Fixing and Mechanizing the Security Proof of Fiat-Shamir with Aborts and Dilithium.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We extend and consolidate the security justification for the Dilithium signature scheme. In particular, we identify a subtle but crucial gap that appears in several ROM and QROM security proofs for signature schemes that are based on the Fiat-Shamir with aborts paradigm, including Dilithium. The gap lies in theCMA-to-NMAreduction and was uncovered when trying to formalize a variant of the QROM security proof by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2018). The gap was confirmed by the authors, and there seems to be no simple patch for it. We provide new, fixed proofs for the affectedCMA-to-NMAreduction, both for the ROM and the QROM, and we perform a concrete security analysis for the case of Dilithium to show that the claimed security level is still valid after addressing the gap. Furthermore, we offer a fully mechanized ROM proof for theCMA-security of Dilithium in the EasyCrypt proof assistant. Our formalization includes several new tools and techniques of independent interest for future formal verification results.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_12"}, {"primary_key": "1114723", "vector": [], "sparse_vector": [], "title": "Machine-Checked Security for rmXMSS as in RFC 8391 and $\\mathrm {SPHINCS{+}} $.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work presents a novel machine-checked tight security proof for\\(\\textrm{XMSS} \\)—a stateful hash-based signature scheme that is (1) standardized in RFC 8391 and NIST SP 800-208, and (2) employed as a primary building block of\\(\\mathrm {SPHINCS^{+}} \\), one of the signature schemes recently selected for standardization as a result of NIST’s post-quantum competition. In 2020, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> pointed out a flaw affecting the tight security proofs of\\(\\mathrm {SPHINCS^{+}} \\)and\\(\\textrm{XMSS} \\). For the case of\\(\\mathrm {SPHINCS^{+}} \\), this flaw was fixed in a subsequent tight security proof by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Unfortunately, employing the fix from this proof to construct an analogous tight security proof for\\(\\textrm{XMSS} \\)would merely demonstrate security with respect to an insufficient notion. At the cost of modeling the message-hashing function as a random oracle, we complete the tight security proof for\\(\\textrm{XMSS} \\)and formally verify it using the EasyCrypt proof assistant. (Note that this merely extends the use of the random oracle model, as this model is already required in other parts of the security analysis to justify the currently standardized parameter values). As part of this endeavor, we formally verify the crucial step common to the security proofs of\\(\\mathrm {SPHINCS^{+}} \\)and\\(\\textrm{XMSS} \\)that was found to be flawed before, thereby confirming that the core of the aforementioned security proof by Hülsing and Kudinov is correct. As this is the first work to formally verify proofs for hash-based signature schemes in EasyCrypt, we develop several novel libraries for the fundamental cryptographic concepts underlying such schemes—e.g., hash functions and digital signature schemes—establishing a common starting point for future formal verification efforts. These libraries will be particularly helpful in formally verifying proofs of other hash-based signature schemes such as\\(\\textrm{LMS} \\)or\\(\\mathrm {SPHINCS^{+}} \\).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_14"}, {"primary_key": "1114724", "vector": [], "sparse_vector": [], "title": "On Active Attack Detection in Messaging with Immediate Decryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The widely used Signal protocol provides protection against state exposure attacks through forward security (protecting past messages) and post-compromise security (for restoring security). It supportsimmediate decryption, allowing messages to be re-ordered or dropped at the protocol level without affecting correctness. In this work, we consider strongactive attack detection for secure messaging with immediate decryption, where parties are able to immediately detect active attacks under certain conditions. We first consider in-band active attack detection, where participants who have been actively compromised but are still able to send a single message to their partner can detect the compromise. We propose two complementary notions to capture security, and present a compiler that provides security with respect to both notions. Our notions generalise existing work (RECOVER security) which only supported in-order messaging. We also study the related out-of-band attack detection problem by considering communication over out-of-band, authenticated channels and propose analogous security notions. We prove that one of our two notions in each setting imposes a linear communication overhead in the number of sent messages and security parameter using an information-theoretic argument. This implies that each message must information-theoretically contain all previous messages and that our construction, that essentially attaches the entire message history to every new message, is asymptotically optimal. We then explore ways to bypass this lower bound and highlight the feasibility of practical active attack detection compatible with immediate decryption.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_12"}, {"primary_key": "1114725", "vector": [], "sparse_vector": [], "title": "Cryptography with Certified Deletion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a unifying framework that yields an array of cryptographic primitives with certified deletion. These primitives enable a party in possession of a quantum ciphertext to generate a classical certificate that the encrypted plaintext has been information-theoretically deleted, and cannot be recovered even given unbounded computational resources. For\\(X \\in \\{\\textsf{public}\\text {-}\\textsf{key},\\mathsf {attribute\\text {-}based},\\mathsf {fully\\text {-}homomorphic},\\textsf{witness},\\textsf{timed}\\text {-}\\textsf{release}\\}\\), our compiler converts any (post-quantum)Xencryption toXencryption with certified deletion. In addition, we compile statistically-binding commitments to statistically-binding commitments with certified everlasting hiding. As a corollary, we also obtain statistically-sound zero-knowledge proofs for QMA with certified everlasting zero-knowledge assuming statistically-binding commitments. We also obtain a strong form of everlasting security for two-party and multi-party computation in the dishonest majority setting. While simultaneously achieving everlasting security againstallparties in this setting is known to be impossible, we introduceeverlasting security transfer (EST). This enablesany oneparty (or a subset of parties) to dynamically and certifiably information-theoretically delete other participants’ data after protocol execution. We construct general-purpose secure computation with EST assuming statistically-binding commitments, which can be based on one-way functions or pseudorandom quantum states. We obtain our results by developing a novel proof technique to argue that a bitbhas beeninformation-theoretically deletedfrom an adversary’s view once they output a valid deletion certificate, despite having been previouslyinformation-theoretically determinedby the ciphertext they held in their view. This technique may be of independent interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_7"}, {"primary_key": "1114726", "vector": [], "sparse_vector": [], "title": "Publicly-Verifiable Deletion via Target-Collapsing Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We build quantum cryptosystems that support publicly-verifiable deletion from standard cryptographic assumptions. We introduce target-collapsing as a weakening of collapsing for hash functions, analogous to how second preimage resistance weakens collision resistance; that is, target-collapsing requires indistinguishability between superpositions and mixtures of preimages of an honestly sampled image. We show that target-collapsing hashes enable publicly-verifiable deletion (\\(\\textsf{PVD}\\)), proving conjectures from [Poremba, ITCS’23] and demonstrating that the Dual-Regev encryption (and corresponding fully homomorphic encryption) schemes support\\(\\textsf{PVD}\\)under the LWE assumption. We further build on this framework to obtain a variety of primitives supporting publicly-verifiable deletion from weak cryptographic assumptions, including: Commitments with\\(\\textsf{PVD}\\)assuming the existence of injective one-way functions, or more generally,almost-regularone-way functions. Along the way, we demonstrate that (variants of) target-collapsing hashes can be built from almost-regular one-way functions. Public-key encryption with\\(\\textsf{PVD}\\)assuming trapdoored variants of injective (or almost-regular) one-way functions. We also demonstrate that the encryption scheme of [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, Eurocrypt’23] based on pseudorandom group actions has\\(\\textsf{PVD}\\). Xwith\\(\\textsf{PVD}\\)for\\(X \\in \\{\\)attribute-based encryption, quantum fully-homomorphic encryption, witness encryption, time-revocable encryption\\(\\}\\), assumingXand trapdoored variants of injective (or almost-regular) one-way functions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_4"}, {"primary_key": "1114727", "vector": [], "sparse_vector": [], "title": "Secure Computation with Shared EPR Pairs (Or: How to Teleport in Zero-Knowledge).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Can a sender non-interactively transmit one of two strings to a receiver without knowing which string was received? Does there exist minimally-interactive secure multiparty computation that only makes (black-box) use of symmetric-key primitives? We provide affirmative answers to these questions in a model where parties have access to shared EPR pairs, thus demonstrating the cryptographic power of this resource. First, we construct a one-shot (i.e., single message) string oblivious transfer (OT) protocol with random receiver bit in the shared EPR pairs model, assuming the (sub-exponential) hardness of LWE. Building on this, we show thatsecure teleportation through quantum channelsis possible. Specifically, given the description of any quantum operationQ, a sender with (quantum) input\\(\\rho \\)can send a single classical message that securely transmits\\(Q(\\rho )\\)to a receiver. That is, we realize an ideal quantum channel that takes input\\(\\rho \\)from the sender and provably delivers\\(Q(\\rho )\\)to the receiver without revealing any other information. This immediately gives a number of applications in the shared EPR pairs model: (1) non-interactive secure computation of unidirectionalclassicalrandomized functionalities, (2) NIZK for QMA from standard (sub-exponential) hardness assumptions, and (3) a non-interactivezero-knowledgestate synthesis protocol. Next, we construct a two-round (round-optimal) secure multiparty computation protocol for classical functionalities in the shared EPR pairs model that isunconditionally-securein the (quantum-accessible) random oracle model. Classically, both of these results cannot be obtained without some form of correlated randomness shared between the parties, and the only known approach is to have a trusted dealer set up random (string) OT correlations. In the quantum world, we show that shared EPR pairs (which are simple and can be deterministically generated) are sufficient. At the heart of our work are novel techniques for making use of entangling operations to generate string OT correlations, and for instantiating the Fiat-Shamir transform using correlation-intractability in the quantum setting.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_8"}, {"primary_key": "1114728", "vector": [], "sparse_vector": [], "title": "Publicly Verifiable Zero-Knowledge and Post-Quantum Signatures from VOLE-in-the-Head.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Guilhem", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new method for transforming zero-knowledge protocols in the designated verifier setting into public-coin protocols, which can be made non-interactive and publicly verifiable. Our transformation applies to a large class of ZK protocols based on oblivious transfer. In particular, we show that it can be applied to recent, fast protocols based onvector oblivious linear evaluation(VOLE), with a technique we callVOLE-in-the-head, upgrading these protocols to support public verifiability. Our resulting ZK protocols have linear proof size, and are simpler, smaller and faster than related approaches based on MPC-in-the-head. To build VOLE-in-the-head while supporting both binary circuits and large finite fields, we develop several new technical tools. One of these is a new proof of security for the SoftSpokenOT protocol (Crypto 2022), which generalizes it to produce certain types of VOLE correlations over large fields. Secondly, we present a new ZK protocol that is tailored to take advantage of this form of VOLE, which leads to a publicly verifiable VOLE-in-the-head protocol with only 2x more communication than the best, designated-verifier VOLE-based protocols. We analyze the soundness of our approach when made non-interactive using the Fiat-Shamir transform, using round-by-round soundness. As an application of the resulting NIZK, we present\\(\\textsf{FAEST}\\), a post-quantum signature scheme based on AES. FAEST is the first AES-based signature scheme to be smaller than SPHINCS+, with signature sizes between 5.6 and 6.6kB at the 128-bit security level. Compared with the smallest version of SPHINCS+ (7.9kB), FAEST verification is slower, but the signing times are between 8x and 40x faster.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_19"}, {"primary_key": "1114729", "vector": [], "sparse_vector": [], "title": "On Perfect Linear Approximations and Differentials over Two-Round SPNs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent constructions of (tweakable) block ciphers with an embedded cryptographic backdoor relied on the existence of probability-one differentials or perfect (non-)linear approximations over a reduced-round version of the primitive. In this work, we study how the existence of probability-one differentials or perfect linear approximations over two rounds of a substitution-permutation network can be avoided by design. More precisely, we develop criteria on the s-box and the linear layer that guarantee the absence of probability-one differentials for all keys. We further present an algorithm that allows to efficiently exclude the existence of keys for which there exists a perfect linear approximation.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_8"}, {"primary_key": "1114730", "vector": [], "sparse_vector": [], "title": "Unifying Freedom and Separation for Tight Probing-Secure Composition.", "authors": ["<PERSON>", "G<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The masking countermeasure is often analyzed in the probing model. Proving the probing security of large circuits at high masking orders is achieved by composing gadgets that satisfy security definitions such as non-interference (NI), strong non-interference (SNI) or free SNI. The region probing model is a variant of the probing model, where the probing capabilities of the adversary scale with the number of regions in a masked circuit. This model is of interest as it allows better reductions to the more realistic noisy leakage model. The efficiency of composable region probing secure masking has been recently improved with the introduction of the input-output separation (IOS) definition. In this paper, we first establish equivalences between the non-interference framework and the IOS formalism. We also generalize the security definitions to multiple-input gadgets and systematically show implications and separations between these notions. Then, we study which gadgets from the literature satisfy these. We give new security proofs for some well-known arbitrary-order gadgets, and also some automated proofs for fixed-order, special-case gadgets. To this end, we introduce a new automated formal verification algorithm that solves the open problem of verifying free SNI, which is not a purely simulation-based definition. Using the relationships between the security notions, we adapt this algorithm to further verify IOS. Finally, we look at composition theorems. In the probing model, we use the link between free SNI and the IOS formalism to generalize and improve the efficiency of the tight private circuit (Asiacrypt 2018) construction, also fixing a flaw in the original proof. In the region probing model, we relax the assumptions for IOS composition (TCHES 2021), which allows to save many refresh gadgets, hence improving the efficiency.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_15"}, {"primary_key": "1114731", "vector": [], "sparse_vector": [], "title": "Revisiting Cycles of Pairing-Friendly Elliptic Curves.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A recent area of interest in cryptography is recursive composition of proof systems. One of the approaches to make recursive composition efficient involves cycles of pairing-friendly elliptic curves of prime order. However, known constructions have very low embedding degrees. This entails large parameter sizes, which makes the overall system inefficient. In this paper, we explore 2-cycles composed of curves from families parameterized by polynomials, and show that such cycles do not exist unless a strong condition holds. As a consequence, we prove that no 2-cycles can arise from the known families, except for those cycles already known. Additionally, we show some general properties about cycles, and provide a detailed computation on the density of pairing-friendly cycles among all cycles.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_1"}, {"primary_key": "1114732", "vector": [], "sparse_vector": [], "title": "Combined Fault and Leakage Resilience: Composability, Constructions and Compiler.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Real-world cryptographic implementations nowadays are not only attacked via classical cryptanalysis but also via implementation attacks, including passive attacks (observing side-channel information about the inner computation) and active attacks (inserting faults into the computation). While countermeasures exist for each type of attack, countermeasures against combined attacks have only been considered recently. Masking is a standard technique for protecting against passive side-channel attacks, but protecting against active attacks with additive masking is challenging. Previous approaches include running multiple copies of a masked computation, requiring a large amount of randomness or being vulnerable to horizontal attacks. An alternative approach is polynomial masking, which is inherently fault-resistant. This work presents a compiler based on polynomial masking that achieves linear computational complexity for affine functions and cubic complexity for non-linear functions. The resulting compiler is secure against attackers using region probes and adaptive faults. In addition, the notion of fault-invariance is introduced to improve security against combined attacks without the need to consider all possible fault combinations. Our approach has the best-known asymptotic efficiency among all known approaches.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_13"}, {"primary_key": "1114733", "vector": [], "sparse_vector": [], "title": "Graph-Theoretic Algorithms for the Alternating Trilinear Form Equivalence Problem.", "authors": ["<PERSON>"], "summary": "At Eurocrypt’22 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> proposed a digital signature algorithm based on the hardness of the isomorphism problem of alternating trilinear forms. They propose three concrete parameters in dimensions 9, 10, and 11 respectively. We give new heuristic algorithms that solve this problem more efficiently. With our new algorithms, the first parameter set can be broken in less than a day on a laptop. For the second parameter set, we show there is a\\(2^{-17}\\)fraction of the public keys that can also be broken in less than a day. We do not break the third parameter set in practice, but we claim it falls short of the target security level of 128 bits.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_4"}, {"primary_key": "1114734", "vector": [], "sparse_vector": [], "title": "LaBRADOR: Compact Proofs for R1CS from Module-SIS.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The most compact quantum-safe proof systems for large circuits are PCP-type systems such as Ligero, Aurora, and Shockwave, that only use weak cryptographic assumptions, namely hash functions modeled as random oracles. One would expect that by allowing for stronger assumptions, such as the hardness of Module-SIS, it should be possible to design more compact proof systems. But alas, despite considerable progress in lattice-based proofs, no such proof system was known so far. We rectify this situation by introducing a Lattice-Based Recursively Amortized Demonstration Of R1CS (LaBRADOR), with more compact proof sizes than known hash-based proof systems. At the 128 bits security level, LaBRADOR proves knowledge of a solution for an R1CS mod\\(2^{64}+1\\)with\\(2^{20}\\)constraints, with a proof size of only 58 KB, an order of magnitude more compact than previous quantum-safe proofs.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_17"}, {"primary_key": "1114735", "vector": [], "sparse_vector": [], "title": "On Linear Communication Complexity for (Maximally) Fluid MPC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "Secure multiparty computation protocols with dynamic parties, which assume that honest parties do not need to be online throughout the whole execution of the protocol, have recently gained a lot of traction for computations oflarge scaledistributed protocols, such as blockchains. More specifically, inFluidMPC, introduced in (<PERSON><PERSON><PERSON><PERSON> al.CRYPTO 2021), parties can dynamically join and leave the computation from round to round. The best known Fluid MPC protocol in the honest majority setting communicates\\(O(n^2)\\)elements per gate wherenis the number of parties online at a time. While <PERSON> (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, CRYPTO 2022) extends Fluid MPC to the dishonest majority setting with preprocessing, it still communicates\\(O(n^2)\\)elements per gate. In this work we present alternative Fluid MPC solutions that requireO(n) communication per gate for both the information-theoretic honest majority setting and the information-theoretic dishonest majority setting with preprocessing. Our solutions also achievemaximal fluiditywhere parties only need to be online for a single communication round. Additionally, we show that a protocol in the information-theoretic dishonest majority setting with sub-quadratic\\(o(n^2)\\)overhead per gate requires for each of theNparties who may ever participate in the (later) execution phase,\\(\\varOmega (N)\\)preprocessed data per gate.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_9"}, {"primary_key": "1114736", "vector": [], "sparse_vector": [], "title": "Non-interactive Universal Arguments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In 2002, <PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced the notion of auniversal argumentand constructed an interactive universal argument for non-deterministic computations based on polynomially hard collision-resistant hash functions. Since then, and especially in recent years, there have been tremendous developments in the construction ofnon-interactivesuccinct arguments for deterministic computations under standard hardness assumptions. However, the constructed succinct arguments can be proven universal only undersub-exponentialassumptions. Assumingpolynomially hardfully homomorphic encryption and a widely believed worst-case complexity assumption, we prove a general lifting theorem showing that all existing non-interactive succinct arguments can be made universal. The required complexity assumption is that non-uniformity does not allow arbitrary polynomial speedup. In the setting of uniform adversaries, this extra assumption is not needed.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_5"}, {"primary_key": "1114737", "vector": [], "sparse_vector": [], "title": "Correlated Pseudorandomness from the Hardness of Quasi-Abelian Decoding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A recent paradigm put forth by <PERSON><PERSON>.(CCS 2018, Crypto 2019) showed howpseudorandom correlation generators(PCG) can be used to generate large amounts of useful forms of correlated (pseudo)randomness, using minimal interactions followed solely by local computations, yieldingsilentsecure two-party computation protocols. This can be extended to\\(N \\)-party usingprogrammablePCG’s. Previous works constructed very efficient (non-programmable)PCG’s for correlations such as random oblivious transfers. However, the situation is less satisfying forrandom oblivious linear evaluations(\\({\\textsf{OLE}}\\)’s), their generalisation over large fields. The state-of-the-art work of <PERSON><PERSON> al.(Crypto 2020) constructed programmablePCG’s for\\({\\textsf{OLE}}\\), but their work suffers from two important downsides: (1) it only generates\\({\\textsf{OLE}}\\)’s overlarge fields, and (2) it relies on a relatively new “splittable” ring-\\(\\textsf{LPN}\\)assumption, which lacks strong security foundations. In this work, we introduce thequasi-abelian syndrome decoding problem(\\(\\textsf{QA}\\text {-}\\textsf{SD}\\)), a family of assumptions which generalises the well-established quasi-cyclic syndrome decoding assumption and allows to construct new programmablePCG’s for\\({\\textsf{OLE}}\\)over any field\\(\\mathbb {F}_{q}\\)with\\(q>2\\). Our analysis also sheds light on the security of the ring-\\(\\textsf{LPN}\\)assumption used in Boyleet al., Crypto 2020). Using our newPCG’s, we obtain the first efficient\\(N \\)-party silent secure computation protocols for computing general arithmetic circuit over\\(\\mathbb {F}_q\\)for any\\(q > 2\\).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_18"}, {"primary_key": "1114738", "vector": [], "sparse_vector": [], "title": "Arithmetic Sketching.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "This paper introducesarithmetic sketching, an abstraction of a primitive that several previous works use to achieve lightweight, low-communication zero-knowledge verification of secret-shared vectors. An arithmetic sketching scheme for a language\\(\\mathcal {L}\\subseteq \\mathbb {F}^n\\)consists of (1) a randomized linear function compressing a long inputxto a short “sketch,” and (2) a small arithmetic circuit that accepts the sketch if and only if\\(x \\in \\mathcal {L}\\), up to some small error. If the language\\(\\mathcal {L}\\)has an arithmetic sketching scheme with short sketches, then it is possible to test membership in\\(\\mathcal {L}\\)using an arithmetic circuit with few multiplication gates. Since multiplications are the dominant cost in protocols for computation on secret-shared, encrypted, and committed data, arithmetic sketching schemes give rise to lightweight protocols in each of these settings. Beyond the formalization of arithmetic sketching, our contributions are: A general framework for constructing arithmetic sketching schemes from algebraic varieties. This framework unifies schemes from prior work and gives rise to schemes for useful new languages and with improved soundness error. The first arithmetic sketching schemes for languages ofsparsevectors: vectors with bounded Hamming weight, bounded\\(L_1\\)norm, and vectors whose few non-zero values satisfy a given predicate. A method for “compiling” any arithmetic sketching scheme for a language\\(\\mathcal {L}\\)into a low-communication malicious-secure multi-server protocol for securely testing that a client-provided secret-shared vector is in\\(\\mathcal {L}\\). We also prove the first nontrivial lower bounds showing limits on the sketch size for certain languages (e.g., vectors of Hamming-weight one) and proving the non-existence of arithmetic sketching schemes for others (e.g., the language of all vectors that contain a specific value).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_6"}, {"primary_key": "1114739", "vector": [], "sparse_vector": [], "title": "Lattice-Based Succinct Arguments for NP with Polylogarithmic-Time Verification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Succinct arguments that rely on the Merkle-tree paradigm introduced by <PERSON><PERSON> (STOC 92) suffer from larger proof sizes in practice due to the use of generic cryptographic primitives. In contrast, succinct arguments with the smallest proof sizes in practice exploit homomorphic commitments. However these latter are quantum insecure, unlike succinct arguments based on the Merkle-tree paradigm. A recent line of works seeks to address this limitation, by constructing quantum-safe succinct arguments that exploit lattice-based commitments. The eventual goal is smaller proof sizes than those achieved via the Merkle-tree paradigm. Alas, known constructions lack succinct verification. In this paper, we construct the first interactive argument system for NP with succinct verification that, departing from the Merkle-tree paradigm, exploits the homomorphic properties of lattice-based commitments. For an arithmetic circuit withNgates, our construction achieves verification time\\(\\textsf{polylog}(N)\\)based on the hardness of the Ring Short-Integer-Solution (RSIS) problem. The core technique in our construction is a delegation protocol built from commitment schemes based on leveled bilinear modules, a new notion that we deem of independent interest. We show that leveled bilinear modules can be realized from pre-quantum and from post-quantum cryptographic assumptions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_8"}, {"primary_key": "1114740", "vector": [], "sparse_vector": [], "title": "A Framework for Practical Anonymous Credentials from Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a framework for building practical anonymous credential schemes based on the hardness of lattice problems. The running time of the prover and verifier is independent of the number of users and linear in the number of attributes. The scheme is also compact in practice, with the proofs being as small as a few dozen kilobytes for arbitrarily large (say up to\\(2^{128}\\)) numbers of users with each user having several attributes. The security of our scheme is based on a new family of lattice assumptions which roughly states that given short pre-images of random elements in some setS, it is hard to create a pre-image for a fresh element in such a set. We show that if the set admits efficient zero-knowledge proofs of knowledge of a commitment to a set element and its pre-image, then this yields practically-efficient privacy-preserving primitives such as blind signatures, anonymous credentials, and group signatures. We propose a candidate instantiation of a function from this family which allows for such proofs and thus yields practical lattice-based primitives.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_13"}, {"primary_key": "1114741", "vector": [], "sparse_vector": [], "title": "Differential Meet-In-The-Middle Cryptanalysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>Plase<PERSON>"], "summary": "In this paper we introduce the differential meet-in-the-middle framework, a new cryptanalysis technique for symmetric primitives. Our new cryptanalysis method combines techniques from both meet-in-the-middle and differential cryptanalysis. As such, the introduced technique can be seen as a way of extending meet-in-the-middle attacks and their variants but also as a new way to perform the key recovery part in differential attacks. We apply our approach toSKINNY-128-384in the single-key model and toAES-256in the related-key model. Our attack onSKINNY-128-384permits to break 25 out of the 56 rounds of this variant and improves by two rounds the previous best known attacks. ForAES-256we attack 12 rounds by considering two related keys, thus outperforming the previous best related-key attack onAES-256with only two related keys by 2 rounds.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_9"}, {"primary_key": "1114742", "vector": [], "sparse_vector": [], "title": "New Design Techniques for Efficient Arithmetization-Oriented Hash Functions: ttAnemoi Permutations and ttJive Compression Mode.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Advanced cryptographic protocols such as Zero-knowledge (ZK) proofs of knowledge, widely used in cryptocurrency applications such as Zcash, Monero, Filecoin, Tezos, Topos, demand new cryptographic hash functions that are efficient not only over the binary field\\(\\mathbb {F}_2\\), but also over large fields of prime characteristic\\(\\mathbb {F}_p\\). This need has been acknowledged by the wider community and new so-calledArithmetization-Oriented(AO) hash functions have been proposed, e.g. MiMC-Hash, Rescue–Prime,Poseidon,Reinforced ConcreteandGriffinto name a few. In this paper we proposeAnemoi: a new family of ZK-friendly permutations, that can be used to construct efficient hash functions and compression functions. The main features of these algorithms are that 1) they are designed to be efficient within multiple proof systems (e.g. Groth16,Plonk, etc.), 2) they contain dedicated functions optimised for specific applications (namely Merkle tree hashing and general purpose hashing), 3) they have highly competitive performance e.g. about a factor of 2 improvement overPoseidonand Rescue–Prime in terms ofR1CSconstraints, a 21%–35%Plonkconstraint reduction over a highly optimizedPoseidonimplementation, as well as competitive native performance, running between two and three times faster than Rescue–Prime, depending on the field size. On the theoretical side,<PERSON><PERSON><PERSON><PERSON><PERSON> further the frontier in understanding the design principles that are truly entailed by arithmetization-orientation. In particular, we identify and exploit a previously unknown relationship between CCZ-equivalence and arithmetization-orientation. In addition, we propose two new standalone components that can be easily reused in new designs. One is a new S-box calledFlystel, based on the well-studied butterfly structure, and the second is\\(\\textsf{Jive}_{}\\)– a new mode of operation, inspired by the “Latin dance” symmetric algorithms (Salsa, ChaCha and derivatives). Our design is a conservative one: it uses a very classical Substitution-Permutation Network structure, and our detailed analysis of algebraic attacks highlights can be of independent interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_17"}, {"primary_key": "1114743", "vector": [], "sparse_vector": [], "title": "Black-Hole Radiation Decoding Is Quantum Cryptography.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We propose to study equivalence relations between phenomena in high-energy physics and the existence of standard cryptographic primitives, and show the first example where such an equivalence holds. A small number of prior works showed that high-energy phenomenacan be explainedby cryptographic hardness. Examples include using the existence of one-way functions to explain the hardness of decoding black-hole Hawking radiation (<PERSON> and <PERSON> 2013, <PERSON> 2016), and using pseudorandom quantum states to explain the hardness of computing AdS/CFT dictionary (<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, 2020). In this work we show, for the former example of black-hole radiation decoding, that it alsoimpliesthe existence of secure quantum cryptography. In fact, we show an existential equivalence between the hardness of black-hole radiation decoding and a variety of cryptographic primitives, including bit-commitment schemes and oblivious transfer protocols (using quantum communication). This can be viewed (with proper disclaimers, as we discuss) as providing a physical justification for the existence of secure cryptography. We conjecture that such connections may be found in other high-energy physics phenomena.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_2"}, {"primary_key": "1114744", "vector": [], "sparse_vector": [], "title": "SNARGs for Monotone Policy Batch NP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We construct a succinct non-interactive argument (\\(\\textsf{SNARG} \\)) for the class of monotone policy batch\\(\\textsf{NP} \\)languages, under the Learning with Errors (\\(\\textsf{LWE} \\)) assumption. This class is a subclass of\\(\\textsf{NP} \\)that is associated with a monotone function\\(f:\\{0,1\\}^k\\rightarrow \\{0,1\\}\\)and an\\(\\textsf{NP} \\)language\\(\\mathcal {L} \\), and contains instances\\((x_1,\\ldots ,x_k)\\)such that\\(f(b_1,\\ldots ,b_k)=1\\)where\\(b_j=1\\)if and only if\\(x_j\\in \\mathcal {L} \\). Our\\(\\textsf{SNARG} \\)s are arguments of knowledge in the non-adaptive setting, and satisfy a new notion of somewhere extractability against adaptive adversaries. This is the first\\(\\textsf{SNARG} \\)under standard hardness assumptions for a sub-class of\\(\\textsf{NP} \\)that is not known to have a (computational) non-signaling\\(\\textsf{PCP} \\)with parameters compatible with the standard framework for constructing\\(\\textsf{SNARG} \\)s dating back to [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, STOC ’13]. Indeed, our approach necessarily departs from this framework. Our construction combines existing quasi-arguments for\\(\\textsf{NP} \\)(based on batch arguments for\\(\\textsf{NP}\\)) with a new type of cryptographic encoding of the instance and a new analysis going from local to global soundness. The main novel ingredient used in our encoding is apredicate-extractable hash(\\(\\textsf{PEHash}\\)) family, which is a primitive that generalizes the notion of a somewhere extractable hash. Whereas a somewhere extractable hash allows to extract a single input coordinate, our\\(\\textsf{PEHash}\\)extracts aglobalproperty of the input. We view this primitive to be of independent interest, and believe that it will find other applications.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_9"}, {"primary_key": "1114745", "vector": [], "sparse_vector": [], "title": "Simple Tests of Quantumness Also Certify Qubits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Eitan <PERSON>", "<PERSON>"], "summary": "A test of quantumness is a protocol that allows a classical verifier to certify (only) that a prover is not classical. We show that tests of quantumness that follow a certain template, which captures recent proposals such as [KCVY21,KLVY22], can in fact do much more. Namely, the same protocols can be used forcertifying a qubit, a building-block that stands at the heart of applications such as certifiable randomness and classical delegation of quantum computation. Certifying qubits was previously only known to be possible based on families of post-quantum trapdoor claw-free functions (TCF) with an advanced “adaptive hardcore bit” property, which have only been constructed based on the hardness of the Learning with Errors problem [BCM+21] and recently isogeny-based group actions [AMR23]. Our framework allows certification of qubits based only on the existence of post-quantum TCF, without the adaptive hardcore bit property, or on quantum fully homomorphic encryption. These can be instantiated, for example, from Ring Learning with Errors. This has the potential to improve the efficiency of qubit certification and derived functionalities. On the technical side, we show that thequantum soundnessof any such protocol can be reduced to proving a bound on a simple algorithmic task: informally, answering “two challenges simultaneously” in the protocol. Our reduction formalizes the intuition that these protocols demonstrate quantumness by leveraging the impossibility of rewinding a general quantum prover. This allows us to prove tight bounds on the quantum soundness of [KCVY21] and [KLVY22], showing that no quantum polynomial-time prover can succeed with probability larger than\\(\\cos ^2 \\frac{\\pi }{8}\\approx 0.853\\). Previously, only an upper bound on the success probability of classical provers, and a lower bound on the success probability of quantum provers, were known. We then extend this proof of quantum soundness to show that provers that approach the quantum soundness bound must perform almost anti-commuting measurements. This certifies that the prover holds a qubit.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_6"}, {"primary_key": "1114746", "vector": [], "sparse_vector": [], "title": "A Framework for Statistically Sender Private OT with Optimal Rate.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Statistical sender privacy (SSP) is the strongest achievable security notion for two-message oblivious transfer (OT) in the standard model, providing statistical security against malicious receivers and computational security against semi-honest senders. In this work we provide a novel construction of SSP OT from the Decisional Diffie-Hellman (DDH) and the Learning Parity with Noise (LPN) assumptions achieving (asymptotically) optimal amortized communication complexity, i.e. it achieves rate 1. Concretely, the total communication complexity forkOT instances is\\(2k(1+o(1))\\), which (asymptotically) approaches the information-theoretic lower bound. Previously, it was only known how to realize this primitive using heavy rate-1 FHE techniques [<PERSON><PERSON><PERSON><PERSON> et al., Gentry and Halevi TCC’19]. At the heart of our construction is a primitive called statistical co-PIR, essentially a a public key encryption scheme which statistically erases bits of the message in a few hidden locations. Our scheme achieves nearly optimal ciphertext size and provides statistical security against malicious receivers. Computational security against semi-honest senders holds under the DDH assumption.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_18"}, {"primary_key": "1114747", "vector": [], "sparse_vector": [], "title": "Secure Multiparty Computation from Threshold Encryption Based on Class Groups.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct the first actively-secure threshold version of the cryptosystem based on class groups from the so-called CL framework (Castagnos and Laguillaumie, 2015). We show how to use our threshold scheme to achieve general universally composable (UC) secure multiparty computation (MPC) with only transparent set-up, i.e., with no secret trapdoors involved. On the way to our goal, we design new zero-knowledge (ZK) protocols with constant communication complexity for proving multiplicative relations between encrypted values. This allows us to use the ZK proofs to achieve MPC with active security with only a constant factor overhead. Finally, we adapt our protocol for the so called “You-Only-Speak-Once” (YOSO) setting, which is a very promising recent approach for performing MPC over a blockchain. This is possible because our key generation protocol is simpler and requires significantly less interaction compared to previous approaches: in particular, our new key generation protocol allows the adversary to bias the public key, but we show that this has no impact on the security of the resulting cryptosystem.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_20"}, {"primary_key": "1114748", "vector": [], "sparse_vector": [], "title": "Weak Instances of Class Group Action Based Cryptography via Self-pairings.", "authors": ["W<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we study non-trivial self-pairings with cyclic domains that are compatible with isogenies between elliptic curves oriented by an imaginary quadratic order\\(\\mathcal {O}\\). We prove that the ordermof such a self-pairing necessarily satisfies\\(m \\mid \\varDelta _\\mathcal {O}\\)(and even\\(2m \\mid \\varDelta _\\mathcal {O}\\)if\\(4 \\mid \\varDelta _\\mathcal {O}\\)and\\(4m \\mid \\varDelta _\\mathcal {O}\\)if\\(8 \\mid \\varDelta _\\mathcal {O}\\)) and is not a multiple of the field characteristic. Conversely, for eachmsatisfying these necessary conditions, we construct a family of non-trivial cyclic self-pairings of ordermthat are compatible with oriented isogenies, based on generalized Weil and Tate pairings. As an application, we identify weak instances of class group actions on elliptic curves assuming the degree of the secret isogeny is known. More in detail, we show that if\\(m^2 \\mid \\varDelta _\\mathcal {O}\\)for some prime powermthen given two primitively\\(\\mathcal {O}\\)-oriented elliptic curves\\((E, \\iota )\\)and\\((E',\\iota ') = [\\mathfrak {a}](E,\\iota )\\)connected by an unknown invertible ideal\\(\\mathfrak {a}\\subseteq \\mathcal {O}\\), we can recover\\(\\mathfrak {a}\\)essentially at the cost of a discrete logarithm computation in a group of order\\(m^2\\), assuming the norm of\\(\\mathfrak {a}\\)is given and is smaller than\\(m^2\\). We give concrete instances, involving ordinary elliptic curves over finite fields, where this turns into a polynomial time attack. Finally, we show that these self-pairings simplify known results on the decisional Diffie–Hellman problem for class group actions on oriented elliptic curves.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_25"}, {"primary_key": "1114749", "vector": [], "sparse_vector": [], "title": "Non-interactive Zero-Knowledge from Non-interactive Batch Arguments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Zero-knowledge and succinctness are two important properties that arise in the study of non-interactive arguments. Previously, <PERSON><PERSON><PERSON> et al. (TCC 2020) showed how to obtain a non-interactive zero-knowledge (NIZK) argument for\\(\\textsf{NP}\\)from a succinct non-interactive argument (SNARG) for\\(\\textsf{NP}\\). In particular, their work demonstrates how to leverage the succinctness property from an argument system and transform it into a zero-knowledge property. In this work, we study a similar question of leveraging succinctness for zero-knowledge. Our starting point is a batch argument for\\(\\textsf{NP}\\), a primitive that allows a prover to convince a verifier ofT\\(\\textsf{NP}\\)statements\\(x_1, \\ldots , x_T\\)with a proof whose size scales sublinearly withT. Unlike SNARGs for\\(\\textsf{NP}\\), batch arguments for\\(\\textsf{NP}\\)can be built from group-based assumptions in both pairing and pairing-free groups and from lattice-based assumptions. The challenge with batch arguments is that the proof size is only amortized over the number of instances, but can still encode full information about the witness to a small number of instances. We show how to combine a batch argument for\\(\\textsf{NP}\\)with a local pseudorandom generator (i.e., a pseudorandom generator where each output bit only depends on a small number of input bits) and a dual-mode commitment scheme to obtain a NIZK for\\(\\textsf{NP}\\). Our work provides a newgenericapproach of realizing zero-knowledge from succinctness and highlights a new connection between succinctness and zero-knowledge.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_2"}, {"primary_key": "1114750", "vector": [], "sparse_vector": [], "title": "Anonymous Tokens with Stronger Metadata Bit Hiding from Algebraic MACs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "On the one hand, the web needs to be secured from malicious activities such as bots or DoS attacks; on the other hand, such needs ideally should not justify services tracking people’s activities on the web. Anonymous tokens provide a nice tradeoff between allowing an issuer to ensure that a user has been vetted and protecting the users’ privacy. However, in some cases, whether or not a token is issued reveals a lot of information to an adversary about the strategies used to distinguish honest users from bots or attackers. In this work, we focus on designing an anonymous token protocol between a client and an issuer (also a verifier) that enables the issuer to support its fraud detection mechanisms while preserving users’ privacy. This is done by allowing the issuer to embed a hidden (from the client) metadata bit into the tokens. We first study an existing protocol from CRYPTO 2020 which is an extension of Privacy Pass from PoPETs 2018; that protocol aimed to provide support for a hidden metadata bit, but provided a somewhat restricted security notion. We demonstrate a new attack, showing that this is a weakness of the protocol, not just the definition. In particular, the metadata bit hiding is weak in the setting where the attacker can redeem some tokens and get feedback on whether the bit extraction succeeded. We then revisit the formalism of anonymous tokens with private metadata bit, consider the more natural notion, and design a scheme which achieves it. In order to design this new secure protocol, we base our construction on algebraic MACs instead of PRFs. Our security definitions capture a realistic threat model where adversaries could, through direct feedback or side channels, learn the embedded bit when the token is redeemed. Finally, we compare our protocol with one of the CRYPTO 2020 protocols. We obtain 20% more efficient performance.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_14"}, {"primary_key": "1114751", "vector": [], "sparse_vector": [], "title": "sfDualMS: Efficient Lattice-Based Two-Round Multi-signature with Trapdoor-Free Simulation.", "authors": ["<PERSON><PERSON> Chen"], "summary": "A multi-signature scheme allows multiple signers to jointly sign a common message. In recent years, two lattice-based two-round multi-signature schemes based on Dilithium-G were proposed:DOTTby Damgård, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (PKC’21) andMuSig-L<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Crypto’22). In this work, we propose a new lattice-based two-round multi-signature scheme called\\( \\textsf{DualMS}\\). Compared toDOTT,\\( \\textsf{DualMS}\\)is likely to significantly reduce signature size, since it replaces an opening to a homomorphic trapdoor commitment with a Dilithium-G response in the signature. Compared toMuSig-L, concrete parameters show that\\( \\textsf{DualMS}\\)has smaller public keys, signatures, and lower communication, while the first round cannot be preprocessed offline as inMuSig-L. The main reason behind such improvements is a trapdoor-free “dual signing simulation” of our scheme. Signature simulation of\\( \\textsf{DualMS}\\)is virtually identical the normal signing procedure and does not use lattice trapdoors likeDOTTandMuSig-L.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_23"}, {"primary_key": "1114752", "vector": [], "sparse_vector": [], "title": "Improved Multi-user Security Using the Squared-Ratio Method.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Proving security bounds in contexts with a large number of users is one of the central problems in symmetric-key cryptography today. This paper introduces a new method for information-theoretic multi-user security proofs, called “the Squared-Ratio method”. At its core, the method requires the expectation of the square of the ratio of observing the so-called good transcripts (from <PERSON><PERSON><PERSON>’s H-coefficient technique) in the real and the ideal world. Central to the method is the observation that for information-theoretic adversaries, the KL-divergence for the multi-user security bound can be written as a summation of the KL-divergence of every single user. We showcase the Squared-Ratio method on three examples: the Xor of two Permutations by <PERSON><PERSON> et al. (EUROCRYPT ’98) and <PERSON> et al. (CRYPTO ’98), the Encrypted Davies-Mayer by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO ’16), and the two permutation variant of the nEHtM MAC algorithm by <PERSON><PERSON> et al. (EUROCRYPT ’19). With this new tool, we provide improved bounds for themulti-usersecurity of these constructions. Our approach is modular in the sense that the multi-user security can be obtained directly from single-user results.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_23"}, {"primary_key": "1114753", "vector": [], "sparse_vector": [], "title": "Correlation Intractability and SNARGs from Sub-exponential DDH.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin", "<PERSON><PERSON><PERSON>"], "summary": "We provide the first constructions of SNARGs for Batch-\\(\\textsf{NP}\\)and\\(\\textsf{P}\\)based solely on the sub-exponential Decisional Di<PERSON><PERSON> (DDH) assumption. Our schemes achieve poly-logarithmic proof sizes. We obtain our results by following the correlation-intractability framework for secure instantiation of the Fiat-Shamir paradigm. The centerpiece of our results and of independent interest is a new construction of correlation-intractable hash functions for “small input” product relations verifiable in\\(\\textsf{TC}^0\\), based on sub-exponential DDH.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_20"}, {"primary_key": "1114754", "vector": [], "sparse_vector": [], "title": "Practical Schnorr Threshold Signatures Without the Algebraic Group Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Threshold signatures are digital signature schemes in which a set ofnsigners specify a thresholdtsuch that any subset of sizetis authorized to produce signatures on behalf of the group. There has recently been a renewed interest in this primitive, largely driven by the need to secure highly valuable signing keys, e.g., DNSSEC keys or keys protecting digital wallets in the cryptocurrency ecosystem. Of special interest is FROST, a practical Schnorr threshold signature scheme, which is currently undergoing standardization in the IETF and whose security was recently analyzed at CRYPTO’22. We continue this line of research by focusing on FROST’s unforgeability combined with a practical distributed key generation (DKG) algorithm. Existing proofs of this setup either use non-standard heuristics, idealized group models like the AGM, or idealized key generation. Moreover, existing proofs do not consider all practical relevant optimizations that have been proposed. We close this gap between theory and practice by presenting the Schnorr threshold signature scheme\\(\\textsf{Olaf} \\), which combines the most efficient known FROST variant\\(\\textsf{FROST3} \\)with a variant of <PERSON><PERSON><PERSON>’s DKG protocol (as commonly used for FROST), and prove its unforgeability. Our proof relies on the AOMDL assumption (a weaker and falsifiable variant of the OMDL assumption) and, like proofs of regular Schnorr signatures, on the random oracle model.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_24"}, {"primary_key": "1114755", "vector": [], "sparse_vector": [], "title": "List Oblivious Transfer and Applications to Round-Optimal Black-Box Multiparty Coin Tossing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work we study the problem of minimizing the round complexity for securely evaluating multiparty functionalities while making black-box use of polynomial time assumptions. In Eurocrypt 2016, <PERSON><PERSON><PERSON> et al. showed that assuming all parties have access to a broadcast channel, then at least four rounds of communication are required to securely realize non-trivial functionalities in the plain model. A sequence of works follow-up the result of <PERSON><PERSON><PERSON> et al. matching this lower bound under a variety of assumptions. Unfortunately, none of these works make black-box use of the underlying cryptographic primitives. In Crypto 2021, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> came closer to matching the four-round lower bound, obtaining a five-round protocol that makes black-box use of oblivious transfer and PKE with pseudorandom public keys. In this work, we show how to realize any input-less functionality (e.g., coin-tossing, generation of key-pairs, and so on) infour roundswhile making black-box use of two-round oblivious transfer. As an additional result, we construct the first four-round MPC protocol for generic functionalities that makes black-box use of the underlying primitives, achieving security against non-aborting adversaries. Our protocols are based on a new primitive calledlist two-party computation. This primitive offers relaxed security compared to the standard notion of secure two-party computation. Despite this relaxation, we argue that this tool suffices for our applications. List two-party computation is of independent interest, as we argue it can also be used for the generation of setups, like oblivious transfer correlated randomness, in three rounds. Prior to our work, generating such a setup required at least four rounds of interactions or a trusted third party.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_15"}, {"primary_key": "1114756", "vector": [], "sparse_vector": [], "title": "Lattice-Based Succinct Arguments from Vanishing Polynomials - (Extended Abstract).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Succinct arguments allow a prover to convince a verifier of the validity of any statement in a language, with minimal communication and verifier’s work. Among other approaches, lattice-based protocols offer solid theoretical foundations, post-quantum security, and a rich algebraic structure. In this work, we present some new approaches to constructing efficient lattice-based succinct arguments. Our main technical ingredient is a new commitment scheme based onvanishing polynomials, a notion borrowed from algebraic geometry. We analyse the security of such a commitment scheme, and show how to take advantage of the additional algebraic structure to build new lattice-based succinct arguments. A few highlights amongst our results are: The first recursive folding (i.e. Bulletproofs-like) protocol for linear relations withpolylogarithmicverifier runtime. Traditionally, the verifier runtime has been the efficiency bottleneck for such protocols (regardless of the underlying assumptions). The first verifiable delay function (VDF) based on lattices, building on a recently introduced sequential relation. The first lattice-basedlinear-time proversuccinct argument for NP, in the preprocessing model. The soundness of the scheme is based on (knowledge)-k-R-ISIS assumption [<PERSON><PERSON> et al., CRYPTO’22].", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_3"}, {"primary_key": "1114757", "vector": [], "sparse_vector": [], "title": "Completeness Theorems for Adaptively Secure Broadcast.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advent of blockchain protocols has reignited the interest in adaptively secure broadcast; it is by now well understood that broadcasting over a diffusion network allows an adaptive adversary to corrupt the sender depending on the message it attempts to send and change it. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [Eurocrypt ’10] proved that this is an inherent limitation of broadcast in the simulation-based setting—i.e., this task is impossible against an adaptive adversary corrupting a majority of the parties (a task that is achievable against a static adversary). The contributions of this paper are two-fold. First, we show that, contrary to previous perception, the above limitation of adaptively secure broadcast isnotan artifact of simulation-based security, but rather an inherent issue of adaptive security. In particular, we show that: (1) it also applies to the property-based broadcast definition adapted for adaptive adversaries, and (2) unlike other impossibilities in adaptive security, this impossibility cannot be circumvented by adding a programmable random oracle, in neither setting, property-based or simulation-based. Second, we turn to the resource-restricted cryptography (RRC) paradigm [Garayet al., Eurocrypt ’20], which has proven useful in circumventing impossibility results, and ask whether it also affects the above negative result. We answer this question in the affirmative, by showing that time-lock puzzles (TLPs)—which can be viewed as an instance of RRC—indeed allow for achieving the property-based definition and circumvent the impossibility of adaptively secure broadcast. The natural question is then, do TLPs also allow for simulation-based adaptively secure broadcast against corrupted majorities? We answer this question in the negative. However, we show that a positive result can be achieved via anon-committinganalogue of TLPs in the programmable random-oracle model. Importantly, and as a contribution of independent interest, we also present the first (limited) composition theorem in the resource-restricted setting, which is needed for the complexity-based, non-idealized treatment of TLPs in the context of other protocols.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_1"}, {"primary_key": "1114758", "vector": [], "sparse_vector": [], "title": "A Note on Non-interactive Zero-Knowledge from CDH.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin", "<PERSON>"], "summary": "We build non-interactive zero-knowledge (NIZK) and ZAP arguments for all\\(\\textsf{NP} \\)where soundness holds for infinitely-many security parameters, and against uniform adversaries, assuming the subexponential hardness of the Computational Diffie-Hellman (CDH) assumption. We additionally prove the existence of NIZK arguments with these same properties assuming the polynomial hardness of both CDH and the Learning Parity with Noise (LPN) assumption. In both cases, the CDH assumption does not require a group equipped with a pairing. Infinitely-often uniform security is a standard byproduct of commonly used non-black-box techniques that build on disjunction arguments on the (in)security of some primitive. In the course of proving our results, we develop a new variant of this non-black-box technique that yields improved guarantees: we obtain explicit constructions (previous works generally only obtained existential results) where security holds for a relatively dense set of security parameters (as opposed to an arbitrary infinite set of security parameters). We demonstrate that our technique can have applications beyond our main results.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_23"}, {"primary_key": "1114759", "vector": [], "sparse_vector": [], "title": "Fully Adaptive Schnorr Threshold Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove adaptive security of a simple three-round threshold Schnorr signature scheme, which we call\\(\\textsf{Sparkle}\\). The standard notion of security for threshold signatures considers astaticadversary – one who must declare which parties are corrupt at the beginning of the protocol. The strongeradaptiveadversary can at any time corrupt parties and learn their state. This notion is natural and practical, yet not proven to be met by most schemes in the literature. In this paper, we demonstrate that\\(\\textsf{Sparkle}\\)achieves several levels of security based on different corruption models and assumptions. To begin with,\\(\\textsf{Sparkle}\\)is statically secure under minimal assumptions: the discrete logarithm assumption (DL) and the random oracle model (ROM). If an adaptive adversary corrupts fewer than\\(t/2\\)out of a threshold of\\(t+1\\)signers, then\\(\\textsf{Sparkle}\\)is adaptively secure under a weaker variant of the one-more discrete logarithm assumption (AOMDL) in the ROM. Finally, we prove that\\(\\textsf{Sparkle}\\)achievesfulladaptive security, with a corruption threshold of\\(t\\), under AOMDL in the algebraic group model (AGM) with random oracles. Importantly, we show adaptive security without requiring secure erasures. Ours is the first proof achieving full adaptive security without exponential tightness loss foranythreshold Schnorr signature scheme; moreover, the reduction is tight.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_22"}, {"primary_key": "1114760", "vector": [], "sparse_vector": [], "title": "Snowblind: A Threshold Blind Signature in Pairing-Free Groups.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Both threshold and blind signatures have, individually, received a considerable amount of attention. However little is known about their combination, i.e., a threshold signature which is also blind, in that no coalition of signers learns anything about the message being signed or the signature being produced. Several applications of blind signatures (e.g., anonymous tokens) would benefit from distributed signing as a means to increase trust in the service and hence reduce the risks of key compromise. This paper builds the first blind threshold signatures in pairing-free groups. Our main contribution is a construction that transforms an underlying blind non-threshold signature scheme with a suitable structure into a threshold scheme, preserving its blindness. The resulting signing protocol proceeds in three rounds, and produces signatures consisting of one group element and two scalars. The underlying non-threshold blind signature schemes are of independent interest, and improve upon the current state of the art (<PERSON><PERSON> and <PERSON>, EUROCRYPT ’22) with shorter signatures (three elements, instead of four) and simpler proofs of security. All of our schemes are proved secure in the Random Oracle and Algebraic Group Models, assuming the hardness of the discrete logarithm problem.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_23"}, {"primary_key": "1114761", "vector": [], "sparse_vector": [], "title": "Revisiting Security Estimation for LWE with Hints from a Geometric Perspective.", "authors": ["<PERSON>-<PERSON>ed", "Huijing Gong", "<PERSON>", "<PERSON>"], "summary": "The Distorted Bounded Distance Decoding Problem (\\(\\textsf{DBDD}\\)) was introduced by <PERSON><PERSON><PERSON><PERSON> et al. [Crypto ’20] as an intermediate problem between\\(\\textsf{LWE}\\)and unique-SVP (\\({\\textsf{uSVP}}\\)). They presented an approach that reduces an\\(\\textsf{LWE}\\)instance to a\\(\\textsf{DBDD}\\)instance, integrates side information (or “hints”) into the\\(\\textsf{DBDD}\\)instance, and finally reduces it to a\\({\\textsf{uSVP}}\\)instance, which can be solved via lattice reduction. They showed that this principled approach can lead to algorithms for side-channel attacks that perform better than ad-hoc algorithms that do not rely on lattice reduction. The current work focuses on new methods for integrating hints into a\\(\\textsf{DBDD}\\)instance. We view hints from a geometric perspective, as opposed to the distributional perspective from the prior work. Our approach provides the rigorous promise that, as hints are integrated into the\\(\\textsf{DBDD}\\)instance, the correct solution remains a lattice point contained in the specified ellipsoid. We instantiate our approach with two new types of hints: (1) Inequality hints, corresponding to the region of intersection of an ellipsoid and a halfspace; (2) Combined hints, corresponding to the region of intersection of two ellipsoids. Since the regions in (1) and (2) are not necessarily ellipsoids, we replace them with ellipsoidal approximations that circumscribe the region of intersection. Perfect hints are reconsidered as the region of intersection of an ellipsoid and a hyperplane, which is itself an ellipsoid. The compatibility of “approximate,” “modular,” and “short vector” hints from the prior work is examined. We apply our techniques to the decryption failure and side-channel attack settings. We show that “inequality hints” can be used to model decryption failures, and that our new approach yields a geometric analogue of the “failure boosting” technique of D’anvers et al. [ePrint,’18]. We also show that “combined hints” can be used to fuse information from a decryption failure and a side-channel attack, and provide rigorous guarantees despite the data being non-Gaussian. We provide experimental data for both applications. The code that we have developed to implement the integration of hints and hardness estimates extends the Toolkit from prior work and has been released publicly.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_24"}, {"primary_key": "1114762", "vector": [], "sparse_vector": [], "title": "Multi-party Homomorphic Secret Sharing and Sublinear MPC from Sparse LPN.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past few years, homomorphic secret sharing (HSS) emerged as a compelling alternative to fully homomorphic encryption (FHE), due to its feasibility from an array of standard assumptions and its potential efficiency benefits. However, all known HSS schemes, with the exception of schemes built from FHE or indistinguishability obfuscation (iO), can only support two parties. In this work, we give the first construction of amulti-partyHSS scheme for a non-trivial function class, from an assumption not known to imply FHE. In particular, we construct an HSS scheme for anarbitrarynumber of parties with anarbitrarycorruption threshold, supporting evaluations of multivariate polynomials of degree\\(\\log / \\log \\log \\)over arbitrary finite fields. As a consequence, we obtain a secure multiparty computation (MPC) protocol for any number of parties, with (slightly)sub-linearper-party communication of roughly\\(O(S/\\log \\log S)\\)bits when evaluating a layered Boolean circuit of sizeS. Our HSS scheme relies on thesparseLearning Parity with Noise (LPN) assumption, a standard variant of LPN with a sparse public matrix that has been studied and used in prior works. Thanks to this assumption, our construction enjoys several unique benefits. In particular, it can be built on top ofanylinear secret sharing scheme, producing noisy output shares that can be error-corrected by the decoder. This yields HSS for low-degree polynomials with optimal download rate. Unlike prior works, our scheme also has a low computation overhead in that the per-party computation of a constant degree polynomial takesO(M) work, whereMis the number of monomials.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_11"}, {"primary_key": "1114763", "vector": [], "sparse_vector": [], "title": "Perfect MPC over Layered Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The classical “BGW protocol” (<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, STOC 1988) shows that secure multiparty computation (MPC) amongnparties can be realized withperfect full securityif\\(t < n/3\\)parties are corrupted. This holds against malicious adversaries in the “standard” model for MPC, where a fixed set ofnparties is involved in the full execution of the protocol. However, the picture is less clear in the mobile adversary setting of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (PODC 1991), where the adversary may periodically “move” by uncorrupting parties and corrupting a new set oftparties. In this setting, it is unclear if full security can be achieved against an adversary that is maximally mobile,i.e.,moves after every round. The question is further motivated by the “You Only Speak Once” (YOSO) setting of Gentryet al.(Crypto 2021), where not only the adversary is mobile but also each round is executed by a disjoint set of parties. Previous positive results in this model do not achieve perfect security, and either assume probabilistic corruption and a nonstandard communication model, or only realize the weaker goal of security-with-abort. The question of matching the BGW result in these settings remained open. In this work, we tackle the above two challenges simultaneously. We consider alayered MPCmodel, a simplified variant of the fluid MPC model of Choud<PERSON><PERSON> al.(Crypto 2021). Layered MPC is an instance of standard MPC where the interaction pattern is defined by a layered graph of widthn, allowing each party to send secret messages and broadcast messages only to parties in the next layer. We require perfect security against a malicious adversary who may corrupt at mosttparties in each layer. Our main result is a perfect, fully secure layered MPC protocol with an optimal corruption threshold of\\(t < n/3\\), thus extending the BGW feasibility result to the layered setting. This implies perfectly secure MPC protocols against a maximally mobile adversary.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_12"}, {"primary_key": "1114764", "vector": [], "sparse_vector": [], "title": "Security Analysis of the WhatsApp End-to-End Encrypted Backup Protocol.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "WhatsApp is an end-to-end encrypted (E2EE) messaging service used by billions of people. In late 2021, WhatsApp rolled out a new protocol for backing up chat histories. The E2EE WhatsApp backup protocol (WBP) allows users to recover their chat history from passwords, leaving <PERSON>s<PERSON><PERSON> oblivious of the actual encryption keys. The WBP builds upon the OPAQUE framework for password-based key exchange, which is currently undergoing standardization. While considerable efforts have gone into the design and auditing of the WBP, the complexity of the protocol’s design and shortcomings in the existing security analyses of its building blocks make it hard to understand the actual security guarantees that the WBP provides. In this work, we provide the first formal security analysis of the WBP. Our analysis in the universal composability (UC) framework confirms that the WBP provides strong protection of users’ chat history and passwords. It also shows that a corrupted server can under certain conditions make more password guesses than what previous analysis suggests.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_11"}, {"primary_key": "1114765", "vector": [], "sparse_vector": [], "title": "A Detailed Analysis of Fiat-Shamir with Aborts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>’s signatures are based on the Fiat-Shamir with Aborts paradigm. It transforms an interactive identification protocol that has a non-negligible probability of aborting into a signature by repeating executions until a loop iteration does not trigger an abort. Interaction is removed by replacing the challenge of the verifier by the evaluation of a hash function, modeled as a random oracle in the analysis. The access to the random oracle is classical (ROM), resp. quantum (QROM), if one is interested in security against classical, resp. quantum, adversaries. Most analyses in the literature consider a setting with a bounded number of aborts (i.e., signing fails if no signature is output within a prescribed number of loop iterations), while practical instantiations (e.g., Dilithium) run until a signature is output (i.e., loop iterations are unbounded). In this work, we emphasize that combining random oracles with loop iterations induces numerous technicalities for analyzing correctness, run-time, and security of the resulting schemes, both in the bounded and unbounded case. As a first contribution, we put light on errors in all existing analyses. We then provide two detailed analyses in the QROM for the bounded case, adapted from <PERSON><PERSON><PERSON> al[EUROCRYPT’18] and <PERSON><PERSON><PERSON><PERSON> al[ASIACRYPT’21]. In the process, we prove the underlying\\(\\varSigma \\)-protocol to achieve a stronger zero-knowledge property than usually considered for\\(\\varSigma \\)-protocols with aborts, which enables a corrected analysis. A further contribution is a detailed analysis in the case of unbounded aborts, the latter inducing several additional subtleties.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_11"}, {"primary_key": "1114766", "vector": [], "sparse_vector": [], "title": "Random Oracle Combiners: Breaking the Concatenation Barrier for Collision-Resistance.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Suppose we have two hash functions\\(h_1\\)and\\(h_2\\), but we trust the security of only one of them. To mitigate this worry, we wish to build ahash combiner\\(C^{h_1, h_2}\\)which is secure so long as one of the underlying hash functions is. This question has been well-studied in the regime ofcollision resistance. In this case, concatenating the two hash function outputs clearly works. Unfortunately, a long series of works (<PERSON><PERSON> and <PERSON><PERSON>, CRYPTO’06; <PERSON><PERSON><PERSON>, Eurocrypt’07; <PERSON><PERSON><PERSON>, CRYPTO’08) showed no (noticeably) shorter combiner for collision resistance is possible. In this work, we revisit this pessimistic state of affairs, motivated by the observation that collision-resistance is insufficient for many interesting applications of cryptographic hash functions anyway. We argue the right formulation of the “hash combiner” is to build what we callrandom oracle (RO) combiners, utilizing stronger assumptions for stronger constructions. Indeed, we circumvent the previous lower bounds for collision resistance by constructing a simple length-preserving RO combiner where\\(\\mathcal {Z}_1, \\mathcal {Z}_2\\)are random salts of appropriate length. We show that this extra randomness is necessary for RO combiners, and indeed our construction is somewhat tight with this lower bound. On the negative side, we show that one cannot generically apply the composition theorem to further replace “monolithic” hash functions\\(h_1\\)and\\(h_2\\)by some simpler indifferentiable construction (such as theMerkle-Damgård transformation) from smaller components, such as fixed-length compression functions. Finally, despite this issue, we directly prove collision resistance of the Merkle-Damgård variant of our combiner, where\\(h_1\\)and\\(h_2\\)are replaced by iterative Merkle-Damgård hashes applied to a fixed-length compression function. Thus, we can still subvert the concatenation barrier for collision-resistance combiners while utilizing practically small fixed-length components underneath.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_17"}, {"primary_key": "1114767", "vector": [], "sparse_vector": [], "title": "Finding Short Integer Solutions When the Modulus Is Small.", "authors": ["Léo Du<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present cryptanalysis of the inhomogenous short integer solution (\\(\\textsf{ISIS}_{}\\)) problem for anomalously small moduli\\(q\\)by exploiting the geometry of BKZ reduced bases ofq-ary lattices. We apply this cryptanalysis to examples from the literature where taking such small moduli has been suggested. A recent work [Espitau–<PERSON>–<PERSON>–Yu, CRYPTO 2022] suggests small\\(q\\)versions of the lattice signature schemeFalconand its variantMitaka. For one small\\(q\\)parametrisation ofFalconwe reduce the estimated security against signature forgery by approximately 26 bits. For one small\\(q\\)parametrisation ofMitakawe successfully forge a signature in 15 s.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_6"}, {"primary_key": "1114768", "vector": [], "sparse_vector": [], "title": "Does the Dual-Sieve Attack on Learning with Errors Even Work?", "authors": ["Léo Du<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON> and <PERSON><PERSON> (ASIACRYPT 2021), and MATZOV (tech. report 2022) have independently claimed improved attacks against various NIST lattice candidates by adding a Fast Fourier Transform (FFT) trick on top of the so-called Dual-Sieve attack. Recently, there was more follow up work in this line adding new practical improvements. However, from a theoretical perspective, all of these works are painfully specific to Learning with Errors, while the principle of the Dual-Sieve attack is more general (Laarhoven & Walter, CT-RSA 2021). More critically, all of these works are based on heuristics that have received very little theoretical and experimental attention. This work attempts to rectify the above deficiencies of the literature. We first propose a generalization of the FFT trick by <PERSON> and <PERSON><PERSON> to arbitrary Bounded Distance Decoding instances. This generalization offers a new improvement to the attack. We then theoretically explore the underlying heuristics and show that these are in contradiction with formal, unconditional theorems in some regimes, and with well-tested heuristics in other regimes. The specific instantiations of the recent literature fall into this second regime. We confirm these contradictions with experiments, documenting several phenomena that are not predicted by the analysis, including a “waterfall-floor” phenomenon, reminiscent of Low-Density Parity-Check decoding failures. We conclude that the success probability of the recent Dual-Sieve-FFT attacks are presumably significantly overestimated. We further discuss the adequate way forward towards fixing the attack and its analysis.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_2"}, {"primary_key": "1114769", "vector": [], "sparse_vector": [], "title": "Practical-Time Related-Key Attack on GOST with Secret S-Boxes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The block cipher GOST 28147-89 was the Russian Federation encryption standard for over 20 years, and is still one of its two standard block ciphers. GOST is a 32-round Feistel construction, whose security benefits from the fact that the S-boxes used in the design are kept secret. In the last 10 years, several attacks on the full 32-round GOST were presented. However, they all assume that the S-boxes are known. When the S-boxes are secret, all published attacks either target a small number of rounds, or apply for small sets of weak keys. In this paper we present the first practical-time attack on GOST with secret S-boxes. The attack works in the related-key model and is faster than all previous attacks in this model which assume that the S-boxes are known. The complexity of the attack is less than\\(2^{27}\\)encryptions. It was fully verified, and runs in a few seconds on a PC. The attack is based on a novel type of related-key differentials of GOST, inspired by local collisions. Our new technique may be applicable to certain GOST-based hash functions as well. To demonstrate this, we show how to find a collision on a Davies-Meyer construction based on GOST with an arbitrary initial value, in less than\\(2^{10}\\)hash function evaluations.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_7"}, {"primary_key": "1114770", "vector": [], "sparse_vector": [], "title": "Individual Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We initiate a formal study ofindividual cryptography. Informally speaking, an algorithm\\(\\textsf{Alg}\\)isindividualif, in every implementation of\\(\\textsf{Alg}\\), there always exists an individual user with full knowledge of the cryptographic dataSused by\\(\\textsf{Alg}\\). In particular, it should be infeasible to design implementations of this algorithm that would hideSby distributing it between a group of parties using an MPC protocol or outsourcing it to a trusted execution environment. We define and construct two primitives in this model. The first one, calledproofs of individual knowledge, is a tool for proving that a given message is fully known to a single (“individual”) machine on the Internet, i.e., it cannot be shared between a group of parties. The second one, dubbedindividual secret sharing, is a scheme for sharing a secretSbetween a group of parties so that the parties have no knowledge ofSas long as they do not reconstruct it. The reconstruction ensures that if the shareholders attempt to collude, one of them will learn the secret entirely. Individual secret sharing has applications for preventing collusion in secret sharing. A central technique for constructing individual cryptographic primitives is the concept of MPC hardness. MPC hardness precludes an adversary from completing a cryptographic task in a distributed fashion within a specific time frame.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_18"}, {"primary_key": "1114771", "vector": [], "sparse_vector": [], "title": "Efficient Hybrid Exact/Relaxed Lattice Proofs and Applications to Rounding and VRFs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Dong<PERSON> Liu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we studyhybrid exact/relaxed zero-knowledge proofsfrom lattices, where the proved relation is exact in one part and relaxed in the other. Such proofs arise in important real-life applications such as those requiring verifiable PRF evaluation and have so far not received significant attention as a standalone problem. We first introduce a general framework,\\(\\mathsf {LANES^+}\\), for realizing such hybrid proofs efficiently by combining standardrelaxedproofs of knowledge\\(\\textsf{RPoK}\\)and the\\(\\textsf{LANES}\\)framework (due to a series of works in Crypto’20, Asiacrypt’20, ACM CCS’20). The latter framework is a powerful lattice-based proof system that can prove exact linear and multiplicative relations. The advantage of\\(\\mathsf {LANES^+}\\)is its ability to realize hybrid proofs more efficiently by exploiting\\(\\textsf{RPoK}\\)for the high-dimensional part of the secret witness while leaving a low-dimensional secret witness part for the exact proof that is proven at a significantly lower cost via\\(\\textsf{LANES}\\). Thanks to the flexibility of\\(\\mathsf {LANES^+}\\), other exact proof systems can also be supported. We apply our\\(\\mathsf {LANES^+}\\)framework to construct substantially shorter proofs of rounding, which is a central tool forverifiabledeterministic lattice-based cryptography. Based on our rounding proof, we then design an efficient long-term verifiable random function (VRF), named\\(\\textsf{LaV}\\).\\(\\textsf{LaV}\\)leads to the shortest VRF outputs among the proposals of standard (i.e., long-term and stateless) VRFs based on quantum-safe assumptions. Of independent interest, we also present generalized results for challenge difference invertibility, a fundamental soundness security requirement for many proof systems.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_16"}, {"primary_key": "1114772", "vector": [], "sparse_vector": [], "title": "Orbweaver: Succinct Linear Functional Commitments from Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We presentOrbwe<PERSON>, the first plausibly post-quantum functional commitment to achieve quasilinear prover time together with\\(O(\\log n)\\)proof size and\\(O(\\log n \\log \\log n)\\)verifier time.Orbweaverenables evaluation of linear maps on committed vectors over cyclotomic rings or the integers. It is extractable, preprocessing, non-interactive, structure-preserving, amenable to recursive composition, and supports logarithmic public proof aggregation. The security of our scheme is based on thek-R-ISISassumption (and its knowledge counterpart), whereby we require a trusted setup to generate a universal structured reference string. We additionally useOrbweaverto construct a succinct polynomial commitment for integer polynomials.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_4"}, {"primary_key": "1114773", "vector": [], "sparse_vector": [], "title": "How to Use (Plain) Witness Encryption: Registered ABE, Flexible Broadcast, and More.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Witness encryption is a generalization of public-key encryption where the public key can be any\\(\\textsf{NP}\\)statementxand the associated decryption key is any witnesswforx. While early constructions of witness encryption relied on multilinear maps and indistinguishability obfuscation (\\(i\\mathcal {O}\\)), recent works have provided direct constructions of witness encryption that are more efficient than\\(i\\mathcal {O}\\)(and also seem unlikely to yield\\(i\\mathcal {O}\\)). Motivated by this progress, we revisit the possibility of using witness encryption to realize advanced cryptographic primitives previously known only in “obfustopia.” In this work, we give new constructions oftrustlessencryption systems fromplainwitness encryption (in conjunction with the learning-with-errors assumption): (1) flexible broadcast encryption (a broadcast encryption scheme where users choose theirownsecret keys and users can encrypt to anarbitraryset of public keys); and (2) registered attribute-based encryption (a system where users choose their own keys and then register their public key together with a set of attributes with a deterministic and transparent key curator). Both primitives were previously only known from\\(i\\mathcal {O}\\). We also show how to use our techniques to obtain anoptimalbroadcast encryption scheme in the random oracle model. Underlying our constructions is a novel technique for using witness encryption based on a new primitive which we callfunction-binding hash functions. Whereas a somewhere statistically binding hash function statistically binds a digest to a few bits of the input, a function-binding hash function statistically binds a digest to theoutputof a function of the inputs. As we demonstrate in this work, function-binding hash functions provide us new ways to leverage the power of plain witness encryption and use it as the foundation of advanced cryptographic primitives. Finally, we show how to build function-binding hash functions for the class of disjunctions of block functions from leveled homomorphic encryption; this in combination with witness encryption yields our main results.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_16"}, {"primary_key": "1114774", "vector": [], "sparse_vector": [], "title": "On the Security of Keyed Hashing Based on Public Permutations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Doubly-extendable cryptographic keyed functions (deck) generalize the concept of message authentication codes (MAC) and stream ciphers in that they support variable-length strings as input and return variable-length strings as output. A prominent example of building deck functions is Farfalle, which consists of a set of public permutations and rolling functions that are used in its compression and expansion layers. By generalizing the compression layer of Farfalle, we prove its universality in terms of the probability of differentials over the public permutation used in it. As the compression layer of Farfalle is inherently parallel, we compare it to a generalization of a serial compression function inspired by Pelican-MAC. The same public permutation may result in different universalities depending on whether the compression is done in parallel or serial. The parallel construction consistently performs better than the serial one, sometimes by a big factor. We demonstrate this effect usingXoodoo\\([3]\\), which is a round-reduced variant of the public permutation used in the deck function Xoofff.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_20"}, {"primary_key": "1114775", "vector": [], "sparse_vector": [], "title": "Cryptography with Weights: MPC, Encryption and Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The security of many powerful cryptographic systems such as secure multiparty computation, threshold encryption, and threshold signatures rests on trust assumptions about the parties. The de-facto model treats all parties equally and requires that a certain fraction of the parties are honest. While this paradigm of one-person-one-vote has been very successful over the years, current and emerging practical use cases suggest that it is outdated. In this work, we considerweightedcryptosystems where every party is assigned a certain weight and the trust assumption is that a certain fraction of the total weight is honest. This setting can be translated to the standard setting (where each party has a unit weight) via virtualization. However, this method is quite expensive, incurring a multiplicative overhead in the weight. We present new weighted cryptosystems with significantly better efficiency: our proposed schemes incur only anadditiveoverhead in weights. We first present a weighted ramp secret-sharing scheme (WRSS) where the size of a secret share isO(w) (wherewcorresponds to the weight). In comparison, <PERSON><PERSON><PERSON>’s secret sharing with virtualization requires secret shares of size\\(w\\cdot \\lambda \\), where\\(\\lambda =\\log |{\\mathbb {F}}|\\)is the security parameter. Next, we use our WRSS to construct weighted versions of (semi-honest) secure multiparty computation (MPC), threshold encryption, and threshold signatures. All these schemes inherit the efficiency of our WRSS and incur only an additive overhead in weights. Our WRSS is based on the Chinese remainder theorem-based secret-sharing scheme. Interestingly, this secret-sharing scheme isnon-linearand only achieves statistical privacy. These distinct features introduce several technical hurdles in applications to MPC and threshold cryptosystems. We resolve these challenges by developing several new ideas.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_10"}, {"primary_key": "1114776", "vector": [], "sparse_vector": [], "title": "Malicious Secure, Structure-Aware Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Structure-Aware private set intersection (sa-PSI) is a variant of PSI where <PERSON>’s input setAhas some publicly known structure, <PERSON>’s inputBis an unstructured set of points, and <PERSON> learns the intersection\\(A \\cap B\\).sa-PSIwas recently introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> al.(Crypto 2022), who described a semi-honest protocol with communication that scales with the description size of <PERSON>’s set, instead of its cardinality. In this paper, we present the firstsa-PSIprotocol secure against malicious adversaries. sa-PSIprotocols are built from function secret sharing (FSS) schemes, and the main challenge in our work is ensuring that multiple FSS sharings encode thesamestructured set. We do so using a cut-and-choose approach. In order to make FSS compatible with cut-and-choose, we introduce a new variant of function secret sharing, calledderandomizableFSS (dFSS). We show how to constructdFSSfor union of geometric balls, leading to a malicious-securesa-PSIprotocol where <PERSON>’s input is a union of balls. We also improve prior FSS constructions, giving asymptotic improvements to semi-honestsa-PSI.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_19"}, {"primary_key": "1114777", "vector": [], "sparse_vector": [], "title": "Practical Settlement Bounds for Longest-Chain Consensus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>’s longest-chain consensus paradigm now powers the bulk of the world’s cryptocurrencies and distributed finance infrastructure. An emblematic property of longest-chain consensus is that it provides probabilistic settlement guarantees that strengthen over time. This makes the exact relationship between settlement error and settlement latency a critical aspect of the protocol that both users and system designers must understand to make informed decisions. A recent line of work has finally provided a satisfactory rigorous accounting of this relationship for proof-of-work longest-chain protocols, but those techniques do not appear to carry over to the proof-of-stake setting. This article develops a new analytic approach for establishing such settlement guarantees that yields explicit, rigorous settlement bounds for proof-of-stake longest-chain protocols, placing them on equal footing with their proof-of-work counterparts. Our techniques apply with some adaptations to the proof-of-work setting where they provide improvements to the state-of-the-art settlement bounds for proof-of-work protocols.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_4"}, {"primary_key": "1114778", "vector": [], "sparse_vector": [], "title": "Tighter <PERSON>-Secure Key Encapsulation Mechanism with Explicit Rejection in the Quantum Random Oracle Model.", "authors": ["Jiangxia Ge", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> et al. (TCC 2017) proposed several key encapsulation mechanism (KEM) variants of Fujisaki-Okamoto (FO) transformation, includingand\\(\\textsf {QFO}_m^\\bot \\), and they are widely used in the post-quantum cryptography standardization launched by NIST. These transformations are divided into two types, the implicit and explicit rejection type, includingand\\(\\{\\textsf {FO}^{\\bot }\\),\\(\\textsf {FO}_m^\\bot \\),\\(\\textsf {QFO}_m^\\bot \\}\\), respectively. The decapsulation algorithm of the implicit (resp. explicit) rejection type returns a pseudorandom value (resp. an abort symbol\\(\\bot \\)) for an invalid ciphertext. For the implicit rejection type, theIND-CCAsecurity reduction ofin the quantum random oracle model (QROM) can avoid the quadratic security loss, as shown by <PERSON><PERSON><PERSON> et al. (EUROCRYPT 2020). However, for the explicit rejection type, the best knownIND-CCAsecurity reduction in the QROM presented by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (ASIACRYPT 2022) for\\(\\textsf {FO}_m^\\bot \\)still suffers from a quadratic security loss. Moreover, it is not clear until now whether the implicit rejection type is more secure than the explicit rejection type. In this paper, a QROM security reduction of\\(\\textsf {FO}_m^\\bot \\)without incurring a quadratic security loss is provided. Furthermore, our reduction achievesIND-qCCAsecurity, which is stronger than theIND-CCAsecurity. To achieve our result, two steps are taken: The first step is to prove that theIND-qCCAsecurity of\\(\\textsf {FO}_m^\\bot \\)can be tightly reduced to theIND-CPAsecurity of\\(\\textsf {FO}_m^\\bot \\)by using the online extraction technique proposed by Don et al. (EUROCRYPT 2022). The second step is to prove that theIND-CPAsecurity of\\(\\textsf {FO}_m^\\bot \\)can be reduced to theIND-CPAsecurity of the underlying public key encryption (PKE) scheme without incurring quadratic security loss by using the Measure-Rewind-Measure One-Way to Hiding Lemma (EUROCRYPT 2020). In addition, we prove that (at least from a theoretic point of view), security is independent of whether the rejection type is explicit (\\(\\textsf {FO}_m^\\bot \\)) or implicit () if the underlying PKE scheme is weakly\\(\\gamma \\)-spread.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_10"}, {"primary_key": "1114779", "vector": [], "sparse_vector": [], "title": "On Optimal Tightness for Key Exchange with Full Forward Secrecy via Key Confirmation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A standard paradigm for building key exchange protocols withfullforward secrecy (andexplicitauthentication) is to add key confirmation messages to an underlying protocol having onlyweakforward secrecy (andimplicitauthentication). Somewhat surprisingly, we show through an impossibility result that this simple trick must nevertheless incur a linear tightness loss in the number of parties for many natural protocols. This includes <PERSON><PERSON><PERSON><PERSON>’s HMQV protocol (CRYPTO 2005) and the protocol of <PERSON><PERSON><PERSON> et al. (CRYPTO 2019). <PERSON><PERSON><PERSON> et al. gave a very efficient underlying protocol withweakforward secrecy having a linear security loss, and showed that this is optimal for certain reductions. However, they also claimed thatfullforward secrecy could be achieved by adding key confirmation messages, andwithout any additional loss. Our impossibility result disproves this claim, showing that their approach, in fact, has an overallquadraticloss. Motivated by this predicament we seek to restore the original linear loss claim of <PERSON><PERSON><PERSON> et al. by using a different proof strategy. Specifically, we start by lowering the goal for the underlying protocol with weak forward secrecy, to aselectivesecurity notion where the adversary must commit to a long-term key it cannot reveal. This allows atightreduction rather than a linear loss reduction. Next, we show that the protocol can be upgraded to full forward secrecy using key confirmation messages with a linear tightness loss, even when starting from the weaker selective security notion. Thus, our approach yields anoveralltightness loss for the fully forward-secret protocol that is only linear, as originally claimed. Finally, we confirm that the underlying protocol of <PERSON><PERSON><PERSON> et al. can indeed be proven selectively secure, tightly.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_10"}, {"primary_key": "1114780", "vector": [], "sparse_vector": [], "title": "The Query-Complexity of Preprocessing Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A large number of works prove lower bounds on space-time trade-offs in preprocessing attacks, i.e., trade-offs between the size of the advice and the time needed to break a scheme given such advice. We contend that the question of how muchtimeis needed to produce this advice is equally important, and often highly non-trivial. However, this question has received significantly less attention. In this paper, we present lower bounds on the complexity of preprocessing attacks that depend on both offline and online time. As in the case of space-time trade-offs, we focus in particular on settings with ideal primitives, where both the offline and online time-complexities are approximated by the number of queries to the given primitive. We give generic results that highlight the benefits of salting to generically increase the offline costs of preprocessing attacks. The majority of our paper presents several results focusing onsaltedhash functions. In particular, we provide a fairly involved analysis of the pre-image- and collision-resistance security of the (two-block) Merkle-<PERSON> construction in our model.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_16"}, {"primary_key": "1114781", "vector": [], "sparse_vector": [], "title": "On the Impossibility of Algebraic NIZK in Pairing-Free Groups.", "authors": ["<PERSON><PERSON>"], "summary": "Non-Interactive Zero-Knowledge proofs (NIZK) allow a prover to convince a verifier that a statement is true by sending only one message and without conveying any other information. In the CRS model, many instantiations have been proposed from group-theoretic assumptions. On the one hand, some of these constructions use the group structure in a black-box way but rely on pairings, an example being the celebrated Groth-Sahai proof system. On the other hand, a recent line of research realized NIZKs from sub-exponential DDH in pairing-free groups using Correlation Intractable Hash functions, but at the price of making non black-box usage of the group. As of today no construction is known tosimultaneouslyreduce its security to pairing-free group problems and to use the underlying group in a black-box way. This is indeed not a coincidence: in this paper, we prove that for a large class of NIZK either a pairing-free group is used non black-box by relying on element representation, or security reduces to external hardness assumptions. More specifically our impossibility applies to two incomparable cases. The first one covers Arguments of Knowledge (AoK) which proves that a preimage under a given one way function is known. The second one covers NIZK (not necessarily AoK) for hard subset problems, which captures relations such as DDH, Decision-Linear and Matrix-DDH.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_22"}, {"primary_key": "1114782", "vector": [], "sparse_vector": [], "title": "Revisiting Time-Space Tradeoffs for Function Inversion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We study the black-box function inversion problem, which is the problem of finding\\(x \\in [N]\\)such that\\(f(x) = y\\), given as input some challenge pointyin the image of a function\\(f : [N] \\rightarrow [N]\\), usingToracle queries tofandpreprocessed advice\\(\\sigma \\in \\{0,1\\}^S\\)depending onf. We prove a number of new results about this problem, as follows. We show an algorithm that works for anyTandSsatisfying In the important setting when\\(S < T\\), this improves on the celebrated algorithm of Fiat and Naor [STOC, 1991], which requires\\(T S^3 \\gtrsim N^3\\). E.g., Fiat and Naor’s algorithm is only non-trivial for\\(S \\gg N^{2/3}\\), while our algorithm gives a non-trivial tradeoff for any\\(S \\gg N^{1/2}\\). (Our algorithm and analysis are quite simple. As a consequence of this, we also give a self-contained and simple proof of <PERSON> and <PERSON><PERSON>’s original result, with certain optimizations left out for simplicity.) We observe that there is a very simplenon-adaptivealgorithm (i.e., an algorithm whoseith query\\(x_i\\)is chosen based entirely on\\(\\sigma \\)andy, and not on the\\(f(x_1),\\ldots , f(x_{i-1})\\)) that improves slightly on the trivial algorithm. It works for anyTandSsatisfying\\( S = \\varTheta (N \\log (N/T))\\), for example,\\(T = N /\\mathrm {poly\\,log}(N)\\),\\(S = \\varTheta (N/\\log \\log N)\\). This answers a question due to Corrigan-Gibbs and Kogan [TCC, 2019], who asked whether non-trivial non-adaptive algorithms exist; namely, algorithms that work with parametersTandSsatisfying\\(T + S/\\log N < o(N)\\). We also observe that our non-adaptive algorithm is what we call aguess-and-checkalgorithm, that is, it is non-adaptiveandits final output is always one of the oracle queries\\(x_1,\\ldots , x_T\\). For guess-and-check algorithms, we prove a matching lower bound, therefore completely characterizing the achievable parameters (S,T) for this natural class of algorithms. (Corrigan-Gibbs and Kogan showed that any such lower bound forarbitrarynon-adaptive algorithms would imply new circuit lower bounds.) We show equivalence between function inversion and a natural decision version of the problem in both the worst case and the average case, and similarly for functions\\(f : [N] \\rightarrow [M]\\)with different ranges. Some of these equivalence results are deferred to the full version [ECCC, 2022]. All of the above results are most naturally described in a model withshared randomness(i.e., random coins shared between the preprocessing algorithm and the online algorithm). However, as an additional contribution, we show (using a technique from communication complexity due to Newman [IPL, 1991]) how to generically convert any algorithm that uses shared randomness into one that does not.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_15"}, {"primary_key": "1114783", "vector": [], "sparse_vector": [], "title": "Brakedown: Linear-Time and Field-Agnostic SNARKs for R1CS.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Riad S. Wahby"], "summary": "This paper introduces a SNARK calledBrakedown. Brakedown targets R1CS, a popular NP-complete problem that generalizes circuit-satisfiability. It is the first built system that provides alinear-timeprover, meaning the prover incursO(N) finite field operations to prove the satisfiability of anN-sized R1CS instance. Brakedown ’s prover is faster, both concretely and asymptotically, than prior SNARK implementations. It does not require a trusted setup and may be post-quantum secure. Furthermore, it is compatible witharbitraryfinite fields of sufficient size; this property is new among built proof systems with sublinear proof sizes. To design Brakedown, we observe that recent work of Bootle, Chiesa, and Groth (BCG, TCC 2020) provides a polynomial commitment scheme that, when combined with the linear-time interactive proof system of Spartan (CRYPTO 2020), yields linear-time IOPs and SNARKs for R1CS (a similar theoretical result was previously established by BCG, but our approach is conceptually simpler, and crucial for achieving high-speed SNARKs). A core ingredient in the polynomial commitment scheme that we distill from BCG is a linear-time encodable code. Existing constructions of such codes are believed to be impractical. Nonetheless, we design and engineer a new one that is practical in our context. We also implement a variant of Brakedown that uses Reed-Solomon codes instead of our linear-time encodable codes; we refer to this variant asShockwave. Shockwave isnota linear-time SNARK, but it provides shorter proofs and lower verification times than Brakedown, and also provides a faster prover than prior plausibly post-quantum SNARKs.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_7"}, {"primary_key": "1114784", "vector": [], "sparse_vector": [], "title": "On Concurrent Multi-party Quantum Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, significant progress has been made toward quantumly secure multi-party computation (MPC) in thestand-alonesetting. In sharp contrast, the picture ofconcurrentlysecure MPC (or even 2PC), for both classical and quantum functionalities, still remains unclear. Quantum information behaves in a fundamentally different way, making the job of adversaries harder and easier at the same time. Thus, it is unclear if the positive or negative results from the classical setting still apply. This work initiates a systematic study ofconcurrentsecure computation in the quantum setting. We obtain a mix of positive and negative results. We first show that assuming the existence of post-quantum one-way functions (PQ-OWFs), concurrently secure 2PC (and thus MPC) for quantum functionalities is impossible. Next, we focus on thebounded-concurrentsetting, where we obtainsimulation-soundzero-knowledge arguments for both\\(\\textbf{NP} \\)and\\(\\textbf{QMA}\\), assuming PQ-OWFs. This is obtained by a new design of simulation-sound gadget, relying on the recent post-quantum non-malleable commitments by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [arXiv:2207.05861], and the quantum rewinding strategy recently developed by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> Placa [CRYPTO’21] for bounded-concurrent post-quantum ZK. Moreover, we show that our technique is general enough—It also leads to quantum-secure bounded-concurrent coin-flipping protocols, and eventuallygeneral-purpose2PC and MPC, for both classical and quantum functionalities. All these constructions can be based on the quantum hardness of Learning with Errors.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_5"}, {"primary_key": "1114785", "vector": [], "sparse_vector": [], "title": "Reusable Secure Computation in the Plain Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consider the standard setting of two-party computation where the sender has a secret functionfand the receiver has a secret inputxand the outputf(x) is delivered to the receiver at the end of the protocol. Let us consider the unidirectional message model where only one party speaks in each round. In this setting, <PERSON> and <PERSON><PERSON> (<PERSON><PERSON><PERSON> 2004) showed that at least four rounds of interaction between the parties are needed in the plain model (i.e., no trusted setup) if the simulator uses the adversary in a black-box way (a.k.a. black-box simulation). Suppose the sender and the receiver would like to run multiple sequential iterations of the secure computation protocol on possibly different inputs. For each of these iterations, do the parties need to start the protocol from scratch and exchange four messages? In this work, we explore the possibility ofamortizingthe round complexity or in other words,reusinga certain number of rounds of the secure computation protocol in the plain model. We obtain the following results. Under standard cryptographic assumptions, we construct a four-round two-party computation protocol where (i) the first three rounds of the protocol could be reused an unbounded number of times if the receiver input remains the same and only the sender input changes, and (ii) the first two rounds of the protocol could be reused an unbounded number of times if the receiver input needs to change as well. In other words, the sender sends a single additional message if only its input changes, and in the other case, we need one message each from the receiver and the sender. The number of additional messages needed in each of the above two modes is optimal and, additionally, our protocol allows arbitrary interleaving of these two modes. We also extend these results to the multiparty setting (in the simultaneous message exchange model) and give round-optimal protocols such that (i) the first two rounds could be reused an unbounded number of times if the inputs of the parties need to change and (ii) the first three rounds could be reused an unbounded number of times if the inputs remain the same but the functionality to be computed changes. As in the two-party setting, we allow arbitrary interleaving of the above two modes of operation.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_14"}, {"primary_key": "1114786", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Symmetric Primitives over Rings and a Key Recovery Attack on Rubato.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON> Hovd", "Morten Øygarden", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Symmetric primitives are a cornerstone of cryptography, and have traditionally been defined over fields, where cryptanalysis is now well understood. However, a few symmetric primitives defined overrings\\(\\mathbb Z _q\\)for a composite number\\(q\\)have recently been proposed, a setting where security is much less studied. In this paper we focus on studying established algebraic attacks typically defined over fields and the extent of their applicability to symmetric primitives defined over the ring of integers modulo a composite\\(q\\). Based on our analysis, we present an attack on fullRubato, a family of symmetric ciphers proposed by <PERSON> et al. at Eurocrypt 2022 designed to be used in a transciphering framework for approximate fully homomorphic encryption. We show that at least 25\\(\\%\\)of the possible choices for\\(q\\)satisfy certain conditions that lead to a successful key recovery attack with complexity significantly lower than the claimed security level for five of the six ciphers in theRubatofamily.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_11"}, {"primary_key": "1114787", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> Meets Fluid-SPN: Griffin for Zero-Knowledge Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Walch", "<PERSON><PERSON>"], "summary": "Zero-knowledge (ZK) applications form a large group of use cases in modern cryptography, and recently gained in popularity due to novel proof systems. For many of these applications, cryptographic hash functions are used as the main building blocks, and they often dominate the overall performance and cost of these approaches. Therefore, in the last years several new hash functions were built in order to reduce the cost in these scenarios, includingPoseidonandRescueamong others. These hash functions often look very different from more classical designs such as AES or SHA-2. For example, they work natively over prime fields rather than binary ones. At the same time, for examplePoseidonandRescueshare some common features, such as being SPN schemes and instantiating the nonlinear layer with invertible power maps. While this allows the designers to provide simple and strong arguments for establishing their security, it also introduces crucial limitations in the design, which may affect the performance in the target applications. In this paper, we propose theHorstconstruction, in which the addition in a Feistel scheme\\((x,y)\\mapsto (y+F(x), x)\\)is extended via a multiplication, i.e.,\\((x,y)\\mapsto (y\\times G(x) + F(x), x)\\). By carefully analyzing the performance metrics in SNARK and STARK protocols, we show how to combine an expandingHorstscheme with aRescue-like SPN scheme in order to provide security and better efficiency in the target applications. We provide an extensive security analysis for our new designGriffinand a comparison with all current competitors.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_19"}, {"primary_key": "1114788", "vector": [], "sparse_vector": [], "title": "Streaming Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study ofstreaming functional encryption(sFE) which is designed for scenarios in which data arrives in a streaming manner and is computed on in an iterative manner as the stream arrives. Unlike in a standard functional encryption (FE) scheme, in an sFE scheme, we (1) do not require the entire data set to be known at encryption time and (2) allow for partial decryption given only a prefix of the input. More specifically, in an sFE scheme, we can sequentially encrypt each data point\\(x_i\\)in a stream of data\\(x = x_1\\ldots x_n\\)as it arrives, without needing to wait for allnvalues. We can then generate function keys for streaming functions which are stateful functions that take as input a message\\(x_i\\)and a state\\(\\textsf{st}_i\\)and output a value\\(y_i\\)and the next state\\(\\textsf{st}_{i+1}\\). For any\\(k \\le n\\), a user with a function key for a streaming functionfcan learn the firstkoutput values\\(y_1\\ldots y_k\\)where\\((y_i, \\textsf{st}_{i+1}) = f(x_i, \\textsf{st}_i)\\)and\\(\\textsf{st}_1 = \\bot \\)given only ciphertexts for the firstkelements\\(x_1\\ldots x_k\\). In this work, we introduce the notion of sFE and show how to construct it from FE. In particular, we show how to achieve a secure sFE scheme for\\(\\mathsf {P/Poly}\\)from a compact, secure FE scheme for\\(\\mathsf {P/Poly}\\), where our security notion for sFE is similar to standard FE security except that we require all function queries to be made before the challenge ciphertext query. Furthermore, by combining our result with the FE construction of Jain, Lin, and Sahai (STOC, 2022), we show how to achieve a secure sFE scheme for\\(\\mathsf {P/Poly}\\)from the polynomial hardness of well-studied assumptions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_14"}, {"primary_key": "1114789", "vector": [], "sparse_vector": [], "title": "Revisiting the Indifferentiability of the Sum of Permutations.", "authors": ["Aldo <PERSON>ing", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The sum of twon-bit pseudorandom permutations is known to behave like a pseudorandom function withnbits of security. A recent line of research has investigated the security of two publicn-bit permutations and its degree of indifferentiability. <PERSON><PERSON> et al. (INDOCRYPT 2010) proved 2n/3-bit security, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (ACNS 2015) pointed out a non-trivial flaw in their analysis and re-proved\\((2n/3-\\log _2(n))\\)-bit security. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT 2018) eventually improved the result ton-bit security. Recently, Guns<PERSON> at CRYPTO 2022 already observed that a proof technique used in this line of research only holds for sequential indifferentiability. We revisit the line of research in detail, and observe that the strongest bound ofn-bit security has two other serious issues in the reasoning, the first one is actually the same non-trivial flaw that was present in the work of <PERSON><PERSON> et al., while the second one discards biases in the randomness influenced by the distinguisher. More concretely, we introduce two attacks that show limited potential of different approaches. We (i) show that the latter issue that discards biases only holds up to\\(2^{3n/4}\\)queries, and (ii) perform a differentiability attack against their simulator in\\(2^{5n/6}\\)queries. On the upside, we revive the result of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and show\\((2n/3-\\log _2(n))\\)-bit regular indifferentiability security of the sum of public permutations.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_21"}, {"primary_key": "1114790", "vector": [], "sparse_vector": [], "title": "Additive Randomized Encodings and Their Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Addition ofninputs is often the easiest nontrivial function to compute securely. Motivated by several open questions, we ask what can be computed securely given only an oracle that computes the sum. Namely, what functions can be computed in a model where parties can only encode their input locally, then sum up the encodings over some Abelian group\\({\\mathbb G}\\), and decode the result to get the function output. Anadditive randomized encoding(ARE) of a function\\(f(x_1,\\ldots ,x_n)\\)maps every input\\(x_i\\)independently into a randomized encoding\\(\\hat{x}_i\\), such that\\(\\sum _{i=1}^n\\)\\(\\hat{x}_i\\)reveals\\(f(x_1,\\ldots ,x_n)\\)and nothing else about the inputs. In arobustARE, the sum ofany subsetof the\\(\\hat{x}_i\\)only reveals theresidual functionobtained by restricting the corresponding inputs. We obtain positive and negative results on ARE. In particular: Information-theoretic ARE.We fully characterize the 2-party functions\\(f:X_1\\times X_2\\rightarrow \\{0,1\\}\\)admitting a perfectly secure ARE. For\\(n\\ge 3\\)parties, we show a useful “capped sum” function that separates statistical security from perfect security. Computational ARE.We present a general feasibility result, showing thatall functionscan be computed in this model, under a standard hardness assumption in bilinear groups. We also describe a heuristic lattice-based construction. Robust ARE.We present a similar feasibility result forrobustcomputational ARE based on ideal obfuscation along with standard cryptographic assumptions. We then describe several applications of ARE and the above results. Under a standard cryptographic assumption, our computational ARE schemes imply the feasibility of general non-interactive secure computation in theshuffle model, where messages from different parties are shuffled. This implies a general utility-preserving compiler from differential privacy in the central model to computational differential privacy in the (non-robust) shuffle model. The existence of information-theoreticrobustARE implies “best-possible” information-theoretic MPC protocols (Halevi et al., TCC 2018) and degree-2 multiparty randomized encodings (Applebaum et al., TCC 2018). This yields new positive results for specific functions in the former model, as well as a simple unifying barrier for obtaining negative results in both models.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_7"}, {"primary_key": "1114791", "vector": [], "sparse_vector": [], "title": "Almost Tight Multi-user Security Under Adaptive Corruptions from LWE in the Standard Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we construct thefirstdigital signature (SIG) and public-key encryption (PKE) schemes with almost tight multi-user security under adaptive corruptions based on the learning-with-errors (LWE) assumption in the standard model. Our PKE scheme achieves almost tight IND-CCA security and our SIG scheme achieves almost tight strong EUF-CMA security, both in the multi-user setting with adaptive corruptions. The security loss is quadratic in the security parameter\\(\\lambda \\), and independent of the number of users, signatures or ciphertexts. Previously, such schemes were only known to exist under number-theoretic assumptions or in classical random oracle model, thus vulnerable to quantum adversaries. To obtain our schemes from LWE, we propose new frameworks for constructing SIG and PKE with a core technical tool namedprobabilisticquasi-adaptive hash proof system (pr-QA-HPS). As a new variant of HPS, our pr-QA-HPS providesprobabilisticpublic and private evaluation modes that may toss coins. This is in stark contrast to the traditional HPS [C<PERSON><PERSON> and Shoup, Eurocrypt 2002] and existing variants like approximate HPS [<PERSON> and <PERSON>, Asiacrypt 2009], whose public and private evaluations are deterministic in their inputs. Moreover, we formalize a new property called evaluation indistinguishability by requiring statistical indistinguishability of the two probabilistic evaluation modes, even in the presence of the secret key. The evaluation indistinguishability, as well as other nice properties resulting from the probabilistic features of pr-QA-HPS, are crucial for the multi-user security proof of our frameworks under adaptive corruptions. As for instantiations, we construct pr-QA-HPS from the LWE assumption and prove its properties with almost tight reductions, which admit almost tightly secure LWE-based SIG and PKE schemes under our frameworks. Along the way, we also provide new almost-tight reductions from LWE to multi-secret LWE, which may be of independent interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_22"}, {"primary_key": "1114792", "vector": [], "sparse_vector": [], "title": "Tri-State Circuits - A Circuit Model that Captures RAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introducetri-state circuits(TSCs). TSCs form a natural model of computation that, to our knowledge, has not been considered by theorists. The model captures a surprising combination of simplicity and power. TSCs are simple in that they allow only three wire values (0, 1, and undefined –\\(\\mathcal {Z}\\)) and three types of fan-in two gates; they are powerful in that their statically placed gates fire (execute) eagerly as their inputs become defined, implying orders of execution that depend on input. This behavior is sufficient to efficiently evaluate RAM programs. We construct a TSC that emulatesTsteps of any RAM program and that has only\\(O(T \\cdot \\log ^3 T \\cdot \\log \\log T)\\)gates. Contrast this with the reduction from RAM to Boolean circuits, where the best approach scans all of memory on each access, incurring quadratic cost. We connect TSCs with Garbled Circuits (GC). TSCs capture the power of garbling far better than Boolean Circuits, offering a more expressive model of computation that leaves per-gate cost essentially unchanged. As an important application, we constructauthenticated GarbledRAM (GRAM), enabling constant-round maliciously-secure 2PC of RAM programs. Let\\(\\lambda \\)denote the security parameter. We extend authenticated garbling to TSCs; by simply plugging in our TSC-based RAM, we obtain authenticated GRAM running at cost\\(O(T \\cdot \\log ^3 T \\cdot \\log \\log T \\cdot \\lambda )\\), outperforming all prior work, including prior semi-honest GRAM. We also give semi-honest garbling of TSCs from a one-way function (OWF). This yields OWF-based GRAM at cost\\(O(T \\cdot \\log ^3 T \\cdot \\log \\log T \\cdot \\lambda )\\), outperforming the best prior OWF-based GRAM by more than factor\\(\\lambda \\).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_5"}, {"primary_key": "1114793", "vector": [], "sparse_vector": [], "title": "Twin Column Parity Mixers and Gaston - A New Mixing Layer and Permutation.", "authors": ["<PERSON>ane <PERSON> Hirch", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce a new type of mixing layer for the round function of cryptographic permutations, called circulant twin column parity mixer (CPM), that is a generalization of the mixing layers inKeccak-\\(f\\)andXoodoo. While these mixing layers have a bitwise differential branch number of 4 and a computational cost of 2 (bitwise) additions per bit, the circulant twin CPMs we build have a bitwise differential branch number of 12 at the expense of an increase in computational cost: depending on the dimension this ranges between 3 and 3.34 XORs per bit. Our circulant twin CPMs operate on a state in the form of a rectangular array and can serve as mixing layer in a round function that has as non-linear step a layer of S-boxes operating in parallel on the columns. When sandwiched between two ShiftRow-like mappings, we can obtain a columnwise branch number of 12 and hence it guarantees 12 active S-boxes per two rounds in differential trails. Remarkably, the linear branch numbers (bitwise and columnwise alike) of these mappings is only 4. However, we define thetransposeof a circulant twin CPM that has linear branch number of 12 and a differential branch number of 4. We give a concrete instantiation of a permutation using such a mixing layer, namedGaston. It operates on a state of\\(5 \\times 64\\)bits and uses\\(\\chi \\)operating on columns for its non-linear layer. Most notably, theGastonround function is lightweight in that it takes as few bitwise operations as the one of NIST lightweight standardAscon. We show that the best 3-round differential and linear trails ofGastonhave much higher weights than those ofAscon. Permutations likeGastoncan be very competitive in applications that rely for their security exclusively on good differential properties, such as keyed hashing as in the compression phase of Farfalle.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_16"}, {"primary_key": "1114794", "vector": [], "sparse_vector": [], "title": "Learning with Physical Rounding for Linear and Quadratic Leakage Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fresh re-keying is a countermeasure against side-channel analysis where an ephemeral key is derived from a long-term key using a public random value. Popular instances of such schemes rely on key-homomorphic primitives, so that the re-keying process is easy to mask and the rest of the (e.g., block cipher) computations can run with cheaper countermeasures. The main requirement for these schemes to be secure is that the leakages of the ephemeral keys do not allow recovering the long-term key. The Learning with Physical Rounding (LWPR) problem formalizes this security in a practically-relevant model where the adversary can observe noise-free leakages. It can be viewed as a physical version of the Learning With Rounding (LWR) problem, where the rounding is performed by a leakage function and therefore does not have to be computed explicitly. In this paper, we first consolidate the intuition that LWPR cannot be secure in a serial implementation context without additional countermeasures (like shuffling), due to attacks exploiting worst-case leakages that can be mounted with practical data complexity. We then extend the understanding of LWPR in a parallel implementation setting. On the one hand, we generalize its robustness against cryptanalysis taking advantage of any (i.e., not only worst-case) leakage. A previous work claimed security in the specific context of a Hamming weight leakage function. We clarify necessary conditions to maintain this guarantee, based on the degree of the leakage function and the accuracy of its coefficients. On the other hand, we show that parallelism inherently provides good security against attacks exploiting worst-case leakages. We finally confirm the practical relevance of these findings by validating our assumptions experimentally for an exemplary implementation.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_14"}, {"primary_key": "1114795", "vector": [], "sparse_vector": [], "title": "The Power of Undirected Rewindings for Adaptive Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing proofs of adaptive security (e.g., in settings in which decryption keys are adaptively revealed) often rely on guessing arguments. Such guessing arguments can be simple (and, e.g., just involve guessing which keys are revealed), or more complex “partitioning” arguments. Since guessing directly and negatively impacts the loss of the corresponding security reduction, this leads to black-box lower bounds for a number of cryptographic scenarios that involve adaptive security. In this work, we provide an alternative to such guessing arguments: instead of guessing in a security reduction which adaptive choices an adversary\\(\\mathcal {A} \\)makes, werewind\\(\\mathcal {A} \\)many times until we can successfully embed a given computational challenge. The main benefit of using rewindings is that these rewindings can be arranged sequentially, and the corresponding reduction loss only accumulates additively (instead of multiplicatively, as with guessing). The main technical challenge is to show that\\(\\mathcal {A} \\)’s success is not negatively affected after (potentially many) rewindings. To this end, we develop a machinery for “undirected ” rewindings that preserve\\(\\mathcal {A} \\)’s success across (potentially many) rewindings. We use this strategy to show security of the “Logical Key Hierarchy” protocol underlying the popular TreeKEM key management protocol, and security of the Goldreich-Goldwasser-Micali (GGM) pseudorandom function (PRF) as a prefix-constrained PRF. In both cases, we provide the first polynomial reductions to standard assumptions (i.e., to IND-CPA and PRG security, respectively), and in case of the GGM PRF, we also circumvent an existing lower bound.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_24"}, {"primary_key": "1114796", "vector": [], "sparse_vector": [], "title": "Computational Wiretap Coding from Indistinguishability Obfuscation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A wiretap coding scheme for a pair of noisy channels\\((\\textsf{ChB},\\textsf{ChE})\\)enables <PERSON> to reliably communicate a message to <PERSON> by sending its encoding over\\(\\textsf{ChB}\\), while hiding the message from an adversary <PERSON> who obtains the same encoding over\\(\\textsf{ChE}\\). A necessary condition for the feasibility of wiretap coding is that\\(\\textsf{ChB}\\)is not adegradationof\\(\\textsf{ChE}\\), namely <PERSON> cannot simulate <PERSON>’s view. While insufficient in the information-theoretic setting, a recent work of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (Crypto 2022) showed that the non-degradation conditionissufficient in the computational setting, assuming idealized flavors of obfuscation. The question of basing a similar feasibility result on standard cryptographic assumptions was left open, even in simple special cases. In this work, we settle the question for all discrete memoryless channels where the (common) input alphabet of\\(\\textsf{ChB}\\)and\\(\\textsf{ChE}\\)isbinary, and with arbitrary finite output alphabet, under standard (sub-exponential) hardness assumptions: namely those assumptions that imply indistinguishability obfuscation (<PERSON><PERSON><PERSON><PERSON><PERSON> 2021, 2022), and injective PRGs. In particular, this establishes the feasibility of computational wiretap coding when\\(\\textsf{ChB}\\)is a binary symmetric channel with crossover probabilitypand\\(\\textsf{ChE}\\)is a binary erasure channel with erasure probabilitye, where\\(e>2p\\). On the information-theoretic side, our result builds on a new polytope characterization of channel degradation for pairs of binary-input channels, which may be of independent interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_9"}, {"primary_key": "1114797", "vector": [], "sparse_vector": [], "title": "One-Message Secure Reductions: On the Cost of Converting Correlations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Correlated secret randomness is a useful resource for secure computation protocols, often enabling dramatic speedups compared to protocols in the plain model. This has motivated a line of work on identifying and securely generating useful correlations. Different kinds of correlations can vary greatly in terms of usefulness and ease of generation. While there has been major progress on efficiently generatingoblivious transfer(OT) correlations, other useful kinds of correlations are much more costly to generate. Thus, it is highly desirable to develop efficient techniques for securelyconvertingcopies of a given source correlation into copies of a given target correlation, especially when the former are cheaper to generate than the latter. In this work, we initiate a systematic study of such conversions that only involve a single uni-directional message. We refer to such a conversion as aone-message secure reduction(OMSR). Recent works (<PERSON><PERSON><PERSON> et al., Eurocrypt 2022; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., Eurocrypt 2022) studied a similar problem when no communication is allowed; this setting is quite restrictive, however, with few non-trivial conversions being feasible. The OMSR setting substantially expands the scope of feasible results, allowing for direct applications to existing MPC protocols. We obtain the following positive and negative results. OMSR constructions.We present a general rejection-sampling based technique for OMSR with OT source correlations. We apply it to substantially improve in the communication complexity of optimized protocols for distributed symmetric cryptography (<PERSON><PERSON> et al., Crypto 2021). OMSR lower bounds.We develop general techniques for proving lower bounds on the communication complexity of OMSR, matching our positive results up to small constant factors.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_17"}, {"primary_key": "1114798", "vector": [], "sparse_vector": [], "title": "Round-Optimal Black-Box MPC in the Plain Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give the first construction of a (fully) black-box round-optimal secure multiparty computation protocol in the plain model. Our protocol makes black-box use of a sub-exponentially secure two-message statistical sender private oblivious transfer (SSP-OT), which in turn can be based on (sub-exponential variants of) most of the standard cryptographic assumptions known to imply public-key cryptography.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_13"}, {"primary_key": "1114799", "vector": [], "sparse_vector": [], "title": "Succinct Arguments for RAM Programs via Projection Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the goal of proving statements that involve small subsets of a big database, we introduce and study the notion ofprojection codes. A standard error-correcting code allows one to encode a message\\(\\textbf{x}\\)into a codeword\\(\\textbf{X}\\), such that even if a constant fraction of\\(\\textbf{X}\\)is corrupted, the message\\(\\textbf{x}\\)can still be recovered. A projection code extends this guarantee to anysubsetof the bits of\\(\\textbf{x}\\). Concretely, for every projection of\\(\\textbf{x}\\)to a subset\\(\\textbf{s}\\)of its coordinates, there is a subset\\(\\textbf{S}\\)of comparable size such that the projected encoding\\(\\textbf{X}|_\\textbf{S}\\)forms a robust encoding of the projected message\\(\\textbf{x}|_\\textbf{s}\\). Our first main result is a construction of projection codes with a near-optimal increase in the length of\\(\\textbf{x}\\)and size of\\(\\textbf{s}\\). We then apply this to obtain our second main result: succinct arguments for the computation of a RAM program on a (big) committed database, where the communication and the run-time of both the prover and the verifier are close to optimal even when the RAM program run-time is much smaller than the database size. Our solution makes only a black-box use of a collision-resistant hash function, providing the first black-box alternative to previous non-black-box constructions with similar asymptotic efficiency.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_6"}, {"primary_key": "1114800", "vector": [], "sparse_vector": [], "title": "The Pseudorandom Oracle Model and Ideal Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new idealized model of hash functions, which we refer to as thepseudorandom oracle(Pr\\(\\mathcal {O}\\)) model. Intuitively, it allows us to model cryptosystems that use the code of an ideal hash function in a non-black-box way. Formally, we model hash functions via a combination of a pseudorandom function (PRF) family and an ideal oracle. A user can initialize the hash function by choosing a PRF keykand mapping it to a public handlehusing the oracle. Given the handlehand some inputx, the oracle can also be called to evaluate the PRF atxwith the corresponding keyk. A user who chooses the PRF keyktherefore has a complete description of the hash function and can use its code in non-black-box constructions, while an adversary, who just gets the handleh, only has black-box access to the hash function via the oracle. As our main result, we show how to construct ideal obfuscation in the Pr\\(\\mathcal {O}\\)model, starting from functional encryption (FE), which in turn can be based on well-studied polynomial hardness assumptions. In contrast, we know that ideal obfuscation cannot be instantiated in the basic random oracle model under any assumptions. We believe our result provides heuristic justification for the following: (1) most natural security goals implied by ideal obfuscation can be achieved in the real world; (2) obfuscation can be constructed from FE at polynomial security loss. We also discuss how to interpret our result in the Pr\\(\\mathcal {O}\\)model as a construction of ideal obfuscation using simple hardware tokens or as a way to bootstrap ideal obfuscation for PRFs to that for all functions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_8"}, {"primary_key": "1114801", "vector": [], "sparse_vector": [], "title": "La<PERSON>ce Signature with Efficient Protocols, Application to Anonymous Credentials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Digital signature is an essential primitive in cryptography, which can be used as the digital analogue of handwritten signatures but also as a building block for more complex systems. In the latter case, signatures with specific features are needed, so as to smoothly interact with the other components of the systems, such as zero-knowledge proofs. This has given rise to so-calledsignatures with efficient protocols, a versatile tool that has been used in countless applications. Designing such signatures is however quite difficult, in particular if one wishes to withstand quantum computing. We are indeed aware of only one post-quantum construction, proposed by <PERSON><PERSON> et al. at Asiacrypt’16, yielding very large signatures and proofs. In this paper, we propose a new construction that can be instantiated in both standard lattices and structured ones, resulting in each case in dramatic performance improvements. In particular, the size of a proof of message-signature possession, which is one of the main metrics for such schemes, can be brought down to less than 650 KB. As our construction retains all the features expected from signatures with efficient protocols, it can be used as a drop-in replacement in all systems using them, which mechanically improves their own performance, and has thus a direct impact on many applications. It can also be used to easily design new privacy-preserving mechanisms. As an example, we provide the first lattice-based anonymous credentials system.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_12"}, {"primary_key": "1114802", "vector": [], "sparse_vector": [], "title": "CSI -Otter: Isogeny-Based (Partially) Blind Signatures from the Class Group Action with a Twist.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we construct the first provably-secure isogeny-based (partially) blind signature scheme. While at a high level the scheme resembles the Schnorr blind signature, our work does not directly follow from that construction, since isogenies do not offer as rich an algebraic structure. Specifically, our protocol does not fit into thelinear identification protocolabstraction introduced by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (EUROCYRPT’19), which was used to generically construct Schnorr-like blind signatures based on modules such as classical groups and lattices. Consequently, our scheme does not seem susceptible to the recent efficient ROS attack exploiting the linear nature of the underlying mathematical tool. In more detail, our blind signature exploits thequadratic twistof an elliptic curve in an essential way to endow isogenies with a strictly richer structure than abstract group actions (but still more restrictive than modules). The basic scheme has public key size 128 B and signature size 8 KB under the CSIDH-512 parameter sets—these are the smallest among all provably secure post-quantum secure blind signatures. Relying on a newringvariant of the group action inverse problem (\\(\\textsf{rGAIP}\\)), we can halve the signature size to 4 KB while increasing the public key size to 512 B. We provide preliminary cryptanalysis of\\({\\textsf{rGAIP}} \\)and show that for certain parameter settings, it is essentially as secure as the standard\\(\\textsf{GAIP}\\). Finally, we show a novel way to turn our blind signature into a partially blind signature, where we deviate from prior methods since they require hashing into the set of public keys while hiding the corresponding secret key—constructing such a hash function in the isogeny setting remains an open problem.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_24"}, {"primary_key": "1114803", "vector": [], "sparse_vector": [], "title": "Toward Practical Lattice-Based Proof of Knowledge from Hint-MLWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the last decade, zero-knowledge proof of knowledge protocols have been extensively studied to achieve active security of various cryptographic protocols. However, the existing solutions simply seek zero-knowledge for both message and randomness, which is an overkill in many applications since protocols may remain secure even if some information about randomness is leaked to the adversary. We develop this idea to improve the state-of-the-art proof of knowledge protocols for RLWE-based public-key encryption and BDLOP commitment schemes. In a nutshell, we present new proof of knowledge protocols without using noise flooding or rejection sampling which are provably secure under a computational hardness assumption, called Hint-MLWE. We also show an efficient reduction from Hint-MLWE to the standard MLWE assumption. Our approach enjoys the best of two worlds because it has no computational overhead from repetition (abort) and achieves a polynomial overhead between the honest and proven languages. We prove this claim by demonstrating concrete parameters and compare with previous results. Finally, we explain how our idea can be further applied to other proof of knowledge providing advanced functionality.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_18"}, {"primary_key": "1114804", "vector": [], "sparse_vector": [], "title": "Accelerating HE Operations from Key Decomposition Technique.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Lattice-based homomorphic encryption (HE) schemes are based on the noisy encryption technique, where plaintexts are masked with some random noise for security. Recent advanced HE schemes rely on a decomposition technique to manage the growth of noise, which involves a conversion of a ciphertext entry into a short vector followed by multiplication with an evaluation key. Prior to this work, the decomposition procedure turns out to be the most time-consuming part, as it requires discrete Fourier transforms (DFTs) over the base ring for efficient polynomial arithmetic. In this paper, an expensive decomposition operation over a large modulus is replaced with relatively cheap operations over a ring of integers with a small bound. Notably, the cost of DFTs is reduced from quadratic to linear with the level of a ciphertext without any extra noise growth. We demonstrate the implication of our approach by applying it to the key-switching procedure. Our experiments show that the new key-switching method achieves a speedup of 1.2–2.3 or 2.1–3.3 times over the previous method, when the dimension of a base ring is\\(2^{15}\\)or\\(2^{16}\\), respectively.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_3"}, {"primary_key": "1114805", "vector": [], "sparse_vector": [], "title": "New Bounds on the Local Leakage Resilience of Shamir&apos;s Secret Sharing Scheme.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the local leakage resilience of <PERSON><PERSON><PERSON>’s secret sharing scheme. In <PERSON><PERSON><PERSON>’s scheme, a random polynomialfof degreetis sampled over a field of size\\(p>n\\), conditioned on\\(f(0)=s\\)for a secrets. Anytshares (i,f(i)) can be used to fully recoverfand therebyf(0). But, any\\(t-1\\)evaluations offat non-zero coordinates are completely independent off(0). Recent works ask whether the secret remains hidden even if say only 1 bit of information is leaked from each share, independently. This question is well motivated due to the wide range of applications of <PERSON><PERSON><PERSON>’s scheme. For instance, it is known that if <PERSON><PERSON><PERSON>’s scheme is leakage resilient in some range of parameters, then known secure computation protocols are secure in a local leakage model. Over characteristic-2 fields, the answer is known to be negative (e.g., <PERSON><PERSON><PERSON> and W<PERSON><PERSON>, STOC ’16). <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO ’18) were the first to give a positive answer assuming computation is done over prime-order fields. They showed that if\\(t \\ge 0.907n\\), then <PERSON><PERSON><PERSON>’s scheme is leakage resilient. Since then, there has been extensive efforts to improve the above threshold and after a series of works, the current record shows leakage resilience for\\(t\\ge 0.78n\\)(<PERSON><PERSON> et al., ISIT ’22). All existing analyses of <PERSON>ham<PERSON>’s leakage resilience for general leakage functions follow a single framework for which there is a known barrier for any\\(t \\le 0.5 n\\). In this work, we a develop a new analytical framework that allows us to significantly improve upon the previous record and obtain additional new results. Specifically, we show: Shamir’s scheme is leakage resilient for any\\(t \\ge 0.69n\\). If the leakage functions are guaranteed to be “balanced” (i.e., splitting the domain of possible shares into 2 roughly equal-size parts), then Shamir’s scheme is leakage resilient for any\\(t \\ge 0.58n\\). If the leakage functions are guaranteed to be “unbalanced” (i.e., splitting the domain of possible shares into 2 parts of very different sizes), then Shamir’s scheme is leakage resilient as long as\\(t \\ge 0.01 n\\). Such a result isprovablyimpossible to obtain using the previously known technique. All of the above apply more generally to any MDS codes-based secret sharing scheme. Confirming leakage resilience is most important in the range\\(t \\le n/2\\), as in many applications, Shamir’s scheme is used with thresholds\\(t\\le n/2\\). As opposed to the previous approach, ours does not seem to have a barrier at\\(t=n/2\\), as demonstrated by our third contribution.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_5"}, {"primary_key": "1114806", "vector": [], "sparse_vector": [], "title": "Two-Round Stateless Deterministic Two-Party Schnorr Signatures from Pseudorandom Correlation Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Schnorr signatures are a popular choice due to their simplicity, provable security, and linear structure that enables relatively easy threshold signing protocols. The deterministic variant of Schnorr (where the nonce is derived in a stateless manner using a PRF from the message and a long term secret) is widely used in practice since it mitigates the threats of a faulty or poor randomness generator (which in <PERSON>hnorr leads to catastrophic breaches of security). Unfortunately, threshold protocols for the deterministic variant of Schnorr have so far been quite inefficient, as they make non black-box use of the PRF involved in the nonce generation. In this paper, we present the first two-party threshold protocol for Schnorr signatures, where signing is stateless and deterministic, and only makes black-box use of the underlying cryptographic algorithms. We present a protocol from general assumptions which achieves covert security, and a protocol that achieves full active security under standard factoring-like assumptions. Our protocols make crucial use of recent advances within the field ofpseudorandom correlation functions (PCFs). As an additional benefit, only two-rounds are needed to perform distributed signing in our protocol, connecting our work to a recent line of research on the trade-offs between round complexity and cryptographic assumptions for threshold Schnorr signatures.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38557-5_21"}, {"primary_key": "1114807", "vector": [], "sparse_vector": [], "title": "Algebraic Reductions of Knowledge.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introducereductions of knowledge, a generalization of arguments of knowledge, which reduce checking knowledge of a witness in one relation to checking knowledge of a witness in another (simpler) relation. Reductions of knowledge unify a growing class of modern techniques as well as provide a compositional framework to modularly reason about individual steps in complex arguments of knowledge. As a demonstration, we simplify and unify recursive arguments over linear algebraic statements by decomposing them as a sequence of reductions of knowledge. To do so, we develop thetensor reduction of knowledge, which generalizes the central reductive step common to many recursive arguments. Underlying the tensor reduction of knowledge is a new information-theoretic reduction, which, for any modulesU,\\(U_1\\), and\\(U_2\\)such that\\(U \\cong U_1 \\otimes U_2\\), reduces the task of evaluating a homomorphism inUto evaluating a homomorphism in\\(U_1\\)and evaluating a homomorphism in\\(U_2\\).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_21"}, {"primary_key": "1114808", "vector": [], "sparse_vector": [], "title": "Anamorphic Signatures: Secrecy from a Dictator Who Only Permits Authentication!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The goal of this research is to raise technical doubts regarding the usefulness of the repeated attempts by governments to curb Cryptography (aka the “Crypto Wars”), and argue that they, in fact, cause more damage than adding effective control. The notion ofAnamorphic Encryptionwas presented in Eurocrypt’22 for a similar aim. There, despite the presence of a Dictator who possesses all keys and knows all messages, parties can arrange a hidden “anamorphic” message in an otherwise indistinguishable from regular ciphertexts (wrt the Dictator). In this work, we postulate a stronger cryptographic control setting where encryption does not exist (or is neutralized) since all communication is passed through the Dictator in, essentially, cleartext mode (or otherwise, when secure channels to and from the Dictator are the only confidentiality mechanism). Messages are only authenticated to assure recipients of the identity of the sender. We ask whether security against the Dictator still exists, even under such a strict regime which allows only authentication (i.e., authenticated/ signed messages) to pass end-to-end, and where received messages are determined by/ known to the Dictator, and the Dictator also eventually gets all keys to verify compliance of past signing. To frustrate the Dictator, this authenticated message setting gives rise to the possible notion of anamorphic channels inside signature and authentication schemes, where parties attempt to send undetectable secure messages (or other values) using signature tags which are indistinguishable from regular tags. We define and present implementation of schemes for anamorphic signature and authentication; these are applicable to existing and standardized signature and authentication schemes which were designed independently of the notion of anamorphic messages. Further, some cornerstone constructions of the foundations of signatures, in fact, introduce anamorphism. The extended version of this paper, including extra results and proofs, is available as [17].", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_25"}, {"primary_key": "1114809", "vector": [], "sparse_vector": [], "title": "Lattice-Based Timed Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Timed cryptography studies primitives that retain their security only for a predetermined amount of time, such as proofs of sequential work and time-lock puzzles. This feature has proven to be useful in a large number of practical applications, e.g. randomness generation, sealed-bid auctions, and fair multi-party computation. However, the current state of affairs in timed cryptography is unsatisfactory: Virtually all efficient constructions rely on a single sequentiality assumption, namely that repeated squaring in unknown order groups cannot be parallelised. This is a single point of failure in the classical setting and is even false against quantum adversaries. In this work we put forward a new sequentiality assumption, which essentially says that a repeated application of the standard lattice-based hash function cannot be parallelised. We provide concrete evidence of the validity of this assumption and, to substantiate its usefulness, we show how it enables a new proof of sequential work, with a stronger sequentiality guarantee than prior hash-based schemes.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_25"}, {"primary_key": "1114810", "vector": [], "sparse_vector": [], "title": "TreePIR: Sublinear-Time and Polylog-Bandwidth Private Information Retrieval from DDH.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In Private Information Retrieval (PIR), a client wishes to retrieve the value of an indexifrom a public database ofNvalues without leaking any information abouti. In their recent seminal work, <PERSON><PERSON><PERSON><PERSON> and <PERSON> (EUROCRYPT 2020) introduced the first two-server PIR protocol with sublinear amortized server time and sublinear\\(O(\\sqrt{N} \\log N)\\)bandwidth. In a followup work, <PERSON> et al. (CRYPTO 2021) reduced the bandwidth to polylogarithmic by proposing a construction based on privately puncturable pseudorandom functions, a primitive whose only construction known to date is based on heavy cryptographic primitives such as LWE. Partly because of this, their PIR protocol does not achieve concrete efficiency. In this paper we propose TreePIR, a two-server PIR protocol with sublinear amortized server time and polylogarithmic bandwidth whose security can be based on just the DDH assumption. TreePIR can be partitioned in two phases that are both sublinear: The first phase is remarkably simple and only requires pseudorandom generators. The second phase is a single-server PIR protocol ononly\\(\\sqrt{N}\\)indices, for which we can use the protocol by <PERSON><PERSON><PERSON><PERSON> et al. (CRYPTO 2019) based on DDH, or, for practical purposes, the most concretely efficient single-server PIR protocol. Not only does TreePIR achieve better asymptotics than previous approaches while resting on weaker cryptographic assumptions, it also outperforms existing two-server PIR protocols in practice. The crux of our protocol is a new cryptographic primitive that we call weak privately puncturable pseudorandom functions, which we believe can have further applications.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_10"}, {"primary_key": "1114811", "vector": [], "sparse_vector": [], "title": "Coefficient Grouping for Complex Affine Layers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designing symmetric-key primitives for applications in Fully Homomorphic Encryption (FHE) has become important to address the issue of the ciphertext expansion. In such a context, cryptographic primitives with a low-AND-depth decryption circuit are desired. Consequently, quadratic nonlinear functions are commonly used in these primitives, including the well-known\\(\\chi \\)function over\\(\\mathbb {F}_2^n\\)and the power map over a large finite field\\(\\mathbb {F}_{p^n}\\). In this work, we study the growth of the algebraic degree for an SPN cipher over\\(\\mathbb {F}_{2^n}^{m}\\), whose S-box is defined as the combination of a power map\\(x\\mapsto x^{2^d+1}\\)and an\\(\\mathbb {F}_2\\)-linearized affine polynomial\\(x\\mapsto c_0+\\sum _{i=1}^{w}c_ix^{2^{h_i}}\\)where\\(c_1,\\ldots ,c_w\\ne 0\\). Specifically, motivated by the fact that the original coefficient grouping technique published at EUROCRYPT 2023 becomes less efficient for\\(w>1\\), we develop a variant technique that can efficiently work for arbitraryw. With this new technique to study the upper bound of the algebraic degree, we answer the following questions from a theoretic perspective: can the algebraic degree increase exponentially when\\(w=1\\)? what is the influence ofw,dand\\((h_1,\\ldots ,h_w)\\)on the growth of the algebraic degree? Based on this, we show (i) how to efficiently find\\((h_1,\\ldots ,h_w)\\)to achieve the exponential growth of the algebraic degree and (ii) how to efficiently compute the upper bound of the algebraic degree for arbitrary\\((h_1,\\ldots ,h_w)\\). Therefore, we expect that these results can further advance the understanding of the design and analysis of such primitives.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_18"}, {"primary_key": "1114812", "vector": [], "sparse_vector": [], "title": "One-Way Functions and the Hardness of (Probabilistic) Time-Bounded <PERSON><PERSON><PERSON><PERSON> Complexity w.r.t. Samplable Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Consider the recently introduced notion ofprobabilistic time-bounded Kolmogorov Complexity,\\(pK^t\\)(<PERSON> et al., CCC’22), and let\\(\\textsf{MpK}^t\\textsf{P}\\)denote the language of pairs (x,k) such that\\(pK^t(x) \\le k\\). We show the equivalence of the following: \\(\\textsf{MpK}^\\textsf{poly}\\textsf{P}\\)is (mildly) hard-on-average w.r.t.anysamplable distribution\\(\\mathcal {D}\\); \\(\\textsf{MpK}^\\textsf{poly}\\textsf{P}\\)is (mildly) hard-on-average w.r.t. theuniformdistribution; existence of one-way functions. As far as we know, this yields the first natural class of problems where hardness with respect to any samplable distribution is equivalent to hardness with respect to the uniform distribution. Under standard derandomization assumptions, we can show the same result also w.r.t. the standard notion of time-bounded Kolmogorov complexity,\\(K^t\\).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_21"}, {"primary_key": "1114813", "vector": [], "sparse_vector": [], "title": "Layout Graphs, Random Walks and the t-Wise Independence of SPN Block Ciphers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We continue the study oft-wise independence of substitution-permutation networks (SPNs) initiated by the recent work of <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (CRYPTO 2021). Our key technical result shows that when the S-boxes arerandomly and independently chosenand kept secret, anr-round SPN with input length\\(n = b \\cdot k\\)is\\(2^{-\\varTheta (n)}\\)-close tot-wise independent within\\(r = O(\\min \\{k, \\log t\\})\\)rounds for anytalmost as large as\\(2^{b/2}\\). Here,bis the input length of the S-box and we assume that the underlying mixing achieves maximum branch number. We also analyze the special case of AES parameters (with random S-boxes), and show it is\\(2^{-128}\\)-close to pairwise independent in 7 rounds. Central to our result is the analysis of a random walk on what we call thelayout graph, a combinatorial abstraction that captures equality and inequality constraints among multiple SPN evaluations. We use our technical result to show concrete security bounds for SPNs with actual block cipher parameters andsmall-inputS-boxes. (This is in contrast to the large body of results on ideal-model analyses of SPNs.) For example, for the censored-AES block cipher, namely AES with most of the mixing layers removed, we show that 192 rounds suffice to attain\\(2^{-128}\\)-closeness to pairwise independence. The prior such result for AES (<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>YPTO 2021) required more than 9000 rounds.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_23"}, {"primary_key": "1114814", "vector": [], "sparse_vector": [], "title": "Prouff and Rivain&apos;s Formal Security Proof of Masking, Revisited - Tight Bounds in the Noisy Leakage Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Masking is a counter-measure that can be incorporated to software and hardware implementations of block ciphers to provably secure them against side-channel attacks. The security of masking can be proven in different types of threat models. In this paper, we are interested in directly proving the security in the most realistic threat model, the so-callednoisy leakageadversary, that captures well how real-world side-channel adversaries operate. Direct proofs in this leakage model have been established by Prouff & Rivain atEurocrypt 2013, Dziembowskiet al.atEurocrypt 2015, and Prestet al.atCrypto 2019. These proofs are complementary to each other, in the sense that the weaknesses of one proof are fixed in at least one of the others, and conversely. These weaknesses concerned in particular the strong requirements on the noise level and the security parameter to get meaningful security bounds, and some requirements on the type of adversary covered by the proof—i.e., chosen or random plaintexts. This suggested that the drawbacks of each security bound could actually be proof artifacts. In this paper, we solve both issues, by revisiting Prouff & Rivain’s approach.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_12"}, {"primary_key": "1114815", "vector": [], "sparse_vector": [], "title": "MacORAMa: Optimal Oblivious RAM with Integrity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM), introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON>96), is a primitive that allows a client to perform RAM computations on an external database without revealing any information through the access pattern. For a database of sizeN, well-known lower bounds show that a multiplicative overhead of\\(\\varOmega (\\log N)\\)in the number of RAM queries is necessary assumingO(1) client storage. A long sequence of works culminated in the asymptotically optimal construction of Asharo<PERSON>, Komargodski, Lin, and Shi (CRYPTO ‘21) with\\(O(\\log N)\\)worst-case overhead andO(1) client storage. However, this optimal ORAM is known to be secure only in thehonest-but-curioussetting, where an adversary is allowed to observe the access patterns but not modify the contents of the database. In themalicioussetting, where an adversary is additionally allowed to tamper with the database, this construction and many others in fact become insecure. In this work, we construct the first maliciously secure ORAM with worst-case\\(O(\\log N)\\)overhead andO(1) client storage assuming one-way functions, which are also necessary. By the\\(\\varOmega (\\log N)\\)lower bound, our construction is asymptotically optimal. To attain this overhead, we develop techniques to intricately interleave online and offline memory checking for malicious security. Furthermore, we complement our positive result by showing the impossibility of agenericoverhead-preserving compiler from honest-but-curious to malicious security, barring a breakthrough in memory checking.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_4"}, {"primary_key": "1114816", "vector": [], "sparse_vector": [], "title": "Error Correction and Ciphertext Quantization in Lattice Cryptography.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Recent work in the design of rate\\(1 - o(1)\\)lattice-based cryptosystems have used two distinct design paradigms, namely replacing the noise-tolerant encoding\\(m \\mapsto (q/2)m\\)present in many lattice-based cryptosystems with a more efficient encoding, and post-processing traditional lattice-based ciphertexts with a lossy compression algorithm, using a technique very similar to the technique of “vector quantization” within coding theory. We introduce a framework for the design of lattice-based encryption that captures both of these paradigms, and prove information-theoretic rate bounds within this framework. These bounds separate the settings of trivial and non-trivial quantization, and show the impossibility of rate\\(1 - o(1)\\)encryption using both trivial quantization and polynomial modulus. They furthermore put strong limits on the rate of constructions that utilize lattices built by tensoring a lattice of small dimension with\\(\\mathbb {Z}^k\\), which is ubiquitous in the literature. We additionally introduce a new cryptosystem, that matches the rate of the highest-rate currently known scheme, while encoding messages with a “gadget”, which may be useful for constructions of Fully Homomorphic Encryption.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_21"}, {"primary_key": "1114817", "vector": [], "sparse_vector": [], "title": "Reductions from Module Lattices to Free Module Lattices, and Application to Dequantizing Module-LLL.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "Nam <PERSON>"], "summary": "In this article, we give evidence that free modules (i.e., modules which admit a basis) are no weaker than arbitrary modules, when it comes to solving cryptographic algorithmic problems (and when the rank of the module is at least 2). More precisely, we show that for three algorithmic problems used in cryptography, namely the shortest vector problem, the Hermite shortest vector problem and a variant of the closest vector problem, there is a reduction from solving the problem in any module of rank\\(n \\ge 2\\)to solving the problem in anyfreemodule of the same rankn. As an application, we show that this can be used to dequantize the LLL algorithm for module lattices presented by <PERSON> et al. (Asiacrypt 2019).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_27"}, {"primary_key": "1114818", "vector": [], "sparse_vector": [], "title": "A Lower Bound for Proving Hardness of Learning with Rounding with Polynomial Modulus.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>’s Learning with Errors (LWE) problem (STOC 2005) is a fundamental hardness assumption for modern cryptography. The Learning with Rounding (LWR) Problem was put forth by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> (Eurocrypt 2012) as an alternative to LWE, for use in cryptographic situations which require determinism. The only method we currently have for proving hardness of LWR is the so-called “rounding reduction” which is a specific reduction from an analogous LWE problem. This reduction works whenever the LWE error is small relative to the noise introduced by rounding, but it fails otherwise. For this reason, all prior work on establishing hardness of LWR forces the LWE error to be small, either by setting other parameters extremely large (which hurts performance), or by limiting the number of LWR samples seen by the adversary (which rules out certain applications). Hardness of LWR is poorly understood when the LWE modulus (q) is polynomial and when the number of LWE samples (m) seen by the adversary is an unbounded polynomial. This range of parameters is the most relevant for practical implementations, so the lack of a hardness proof in this situation is not ideal. In this work, we identify an obstacle for proving the hardness of LWR from LWE in the above framework whenqis polynomial andmis an unbounded polynomial. Specifically, we show that any “pointwise” reduction from LWE to LWR (i.e., any reduction which maps LWE samples independently to LWR samples) admits an efficient algorithm which directly solves LWE (without the use of an LWR solver). Consequently, LWE cannot be reduced to LWR in our pointwise reduction model with our setting ofqandm, unless LWE is easy. Our model of a pointwise reduction from LWE to LWR captures all prior reductions from LWE to LWR except the rejection sampling reduction of Bogdanovet al.(TCC 2016); while their reduction still operates in a pointwise manner, it can reject an LWE sample instead of mapping it to an LWR sample. However we conjecture that our result still holds in this setting. Our argument proceeds roughly as follows. First, we show that any pointwise reduction from LWE to LWR must have good agreement with some affine map. Then, we use the affine agreement of a pointwise reduction together with a type of Goldreich-Levin “prediction-implies-inversion” argument to extract the LWE secret from LWE input samples. Both components may be of independent interest.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_26"}, {"primary_key": "1114819", "vector": [], "sparse_vector": [], "title": "Lattice-Based Authenticated Key Exchange with Tight Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zeng"], "summary": "We construct the first tightly secure authenticated key exchange (AKE) protocol from lattices. Known tight constructions are all based on Diffie-Hellman-like assumptions. Thus, our protocol is thefirstconstruction with tight security from a post-quantum assumption. Our AKE protocol is constructed tightly from a new security notion for key encapsulation mechanisms (KEMs), called one-way security against checkable chosen-ciphertext attacks (OW-ChCCA). We show how an OW-ChCCA secure KEM can be tightly constructed based on the Learning With Errors assumption, leading to the desired AKE protocol. To show the usefulness of OW-ChCCA security beyond AKE, we use it to construct thefirsttightly bilateral selective-opening (BiSO) secure PKE. BiSO security is a stronger selective-opening notion proposed by <PERSON> et al. (ASIACRYPT 2021).", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_20"}, {"primary_key": "1114820", "vector": [], "sparse_vector": [], "title": "Limits of Breach-Resistant and Snapshot-Oblivious RAMs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Oblivious RAMs (ORAMs) are an important cryptographic primitive that enable outsourcing data to a potentially untrusted server while hiding patterns of access to the data. ORAMs provide strong guarantees even in the face of apersistent adversarythat views the transcripts of all operations and resulting memory contents. Unfortunately, the strong guarantees against persistent adversaries comes at the cost of efficiency as ORAMs are known to require\\(\\varOmega (\\log n)\\)overhead. In an attempt to obtain faster constructions, prior works considered security againstsnapshot adversariesthat only have limited access to operational transcripts and memory. We consider\\((s,\\ell )\\)-snapshot adversaries that performsdata breaches and views the transcripts of\\(\\ell \\)total queries. Promisingly, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [Crypto’22] presented an ORAM construction with\\(O(\\log \\ell )\\)overhead protecting against\\((1,\\ell )\\)-snapshot adversaries with the transcript of\\(\\ell \\)consecutive operations from a single breach. For small values of\\(\\ell \\), this outperforms standard ORAMs. In this work, we tackle whether it is possible to further push this construction beyond a single breach. Unfortunately, we show that protecting against even slightly stronger snapshot adversaries becomes difficult. As our main result, we present a\\(\\varOmega (\\log n)\\)lower bound for any ORAM protecting against a (3, 1)-snapshot adversary that performs three breaches and sees the transcript of only one query. In other words, our lower bound holds even if an adversary observes only memory contents during two breaches while managing to view the transcript of only one query in the other breach. Therefore, we surprisingly show that protecting against a snapshot adversary with three data breaches is as difficult as protecting against a persistent adversary.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_6"}, {"primary_key": "1114821", "vector": [], "sparse_vector": [], "title": "Expand-Convolute Codes for Pseudorandom Correlation Generators from LPN.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The recent development of pseudorandom correlation generators (PCG) holds tremendous promise for highly efficient MPC protocols. Among other correlations, PCGs allow for the efficient generation of oblivious transfer (OT) and vector oblivious linear evaluations (VOLE) with sublinear communication and concretely good computational overhead. This type of PCG makes use of a so-called LPN-friendly error-correcting code. That is, for large dimensions the code should have very efficient encoding and have high minimum distance. We investigate existing LPN-friendly codes and find that several candidates are less secure than was believed. Beginning with the recentexpand-accumulatecodes, we find that for theiraggressiveparameters, aimed at good concrete efficiency, they achieve a smaller [pseudo] minimum distance than conjectured. This decreases the resulting security parameter of the PCG but it remains unclear by how much. We additionally show that the recently proposed and extremely efficientsilvercodes achieve only very small minimum distance and result in concretely efficient attacks on the resulting PCG protocol. As such, silver codes should not be used. We introduce a new LPN-friendly code which we callexpand-convolute. These codes have provably high minimum distance and faster encoding time than suitable alternatives, e.g. expand-accumulate. The main contribution of these codes is the introduction of a convolution step that dramatically increases the minimum distance. This in turn allows for a more efficient parameter selection which results in improved concrete performance. In particular, we observe a 3 times improvement in running time for a comparable security level.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_19"}, {"primary_key": "1114822", "vector": [], "sparse_vector": [], "title": "Fast Practical Lattice Reduction Through Iterated Compression.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new lattice basis reduction algorithm with approximation guarantees analogous to the LLL algorithm and practical performance that far exceeds the current state of the art. We achieve these results by iteratively applying precision management techniques within a recursive algorithm structure and show the stability of this approach. We analyze the asymptotic behavior of our algorithm, and show that the heuristic running time is\\(O(n^{\\omega }(C+n)^{1+\\varepsilon })\\)for lattices of dimensionn,\\(\\omega \\in (2,3]\\)bounding the cost of size reduction, matrix multiplication, and QR factorization, andCbounding the log of the condition number of the input basisB. This yields a running time of\\(O\\left( n^\\omega (p + n)^{1 + \\varepsilon }\\right) \\)for precision\\(p = O(\\log \\Vert B\\Vert _{max})\\)in common applications. Our algorithm is fully practical, and we have published our implementation. We experimentally validate our heuristic, give extensive benchmarks against numerous classes of cryptographic lattices, and show that our algorithm significantly outperforms existing implementations.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_1"}, {"primary_key": "1114823", "vector": [], "sparse_vector": [], "title": "Quantum Linear Key-Recovery Attacks Using the QFT.", "authors": ["<PERSON>"], "summary": "The Quantum Fourier Transform is a fundamental tool in quantum cryptanalysis. In symmetric cryptanalysis, hidden shift algorithms such as <PERSON>, which rely on the QFT, have been used to obtain structural attacks on some very specific block ciphers. The Fourier Transform is also used in classical cryptanalysis, for example in FFT-based linear key-recovery attacks introduced by <PERSON><PERSON> et al. (ICISC 2007). Whether such techniques can be adapted to the quantum setting has remained so far an open question. In this paper, we introduce a new framework for quantum linear key-recovery attacks using the QFT. These attacks loosely follow the classical method of <PERSON><PERSON> et al., in that they rely on the fast computation of acorrelation statein which experimental correlations, rather than being directly accessible, are encoded in the amplitudes of a quantum state. The experimental correlation is a statistic that is expected to be higher for the good key, and on some conditions, the increased amplitude creates a speedup with respect to an exhaustive search of the key. The same method also yields a new family of structural attacks, and new examples of quantum speedups beyond quadratic using classical known-plaintext queries.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_9"}, {"primary_key": "1114824", "vector": [], "sparse_vector": [], "title": "Moving a Step of <PERSON><PERSON><PERSON> in Syncopated Rhythm.", "authors": ["<PERSON><PERSON>", "Mei<PERSON> Liu", "<PERSON><PERSON>", "Dongdai Lin"], "summary": "The stream cipher ChaCha is one of the most widely used ciphers in the real world, such as in TLS, SSH and so on. In this paper, we study the security of ChaCha via differential cryptanalysis based on probabilistic neutrality bits (PNBs). We introduce thesyncopationtechnique for the PNB-based approximation in the backward direction, which significantly amplifies its correlation by utilizing the property of ARX structure. In virtue of this technique, we present a new and efficient method for finding a good set of PNBs. A refined framework of key-recovery attack is then formalized for round-reduced ChaCha. The new techniques allow us to break 7.5 rounds of ChaCha without the last XOR and rotation, as well as to bring faster attacks on 6 rounds and 7 rounds of ChaCha.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_10"}, {"primary_key": "1114825", "vector": [], "sparse_vector": [], "title": "Exploring Decryption Failures of BIKE: New Class of Weak Keys and Key Recovery Attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code-based cryptography has received a lot of attention recently because it is considered secure under quantum computing. Among them, the QC-MDPC based scheme is one of the most promising due to its excellent performance. QC-MDPC based schemes are usually subject to a small rate of decryption failure, which can leak information about the secret key. This raises two crucial problems: how to accurately estimate the decryption failure rate and how to use the failure information to recover the secret key. However, the two problems are challenging due to the difficulty of geometrically characterizing the bit-flipping decoder employed in QC-MDPC, such as using decoding radius. In this work, we introduce the gathering property and show it is strongly connected with the decryption failure rate of QC-MDPC. Based on this property, we present two results for QC-MDPC based schemes. The first is a new construction of weak keys obtained by extending the keys that have gathering property via ring isomorphism. For the set of weak keys, we present a rigorous analysis of the probability, as well as experimental simulation of the decryption failure rates. Considering BIKE’s parameter set targeting 128-bit security, our result eventually indicates that the average decryption failure rate is lower bounded by\\(\\text {DFR}_{\\text {avg}} \\ge 2^{-116.61}\\). The second entails two key recovery attacks against CCA secure QC-MDPC schemes using decryption failures in a multi-target setting. The two attacks consider whether or not it is allowed to reuse ciphertexts respectively. In both cases, we show the decryption failures can be used to identify whether a target’s secret key satisfies the gathering property. Then using the gathering property as an extra information, we present a modified information set decoding algorithm that efficiently retrieves the target’s secret key. For BIKE’s parameter set targeting 128-bit security, we show a key recovery attack with complexity\\(2^{116.61}\\)can be mounted if ciphertexts reusing is not permitted, and the complexity can be reduced to\\(2^{98.77}\\)when ciphertexts reusing is permitted.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38548-3_3"}, {"primary_key": "1114826", "vector": [], "sparse_vector": [], "title": "Universal Amplification of KDM Security: From 1-Key Circular to Multi-Key KDM.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An encryption scheme isKey Dependent Message(KDM) secure if it is safe to encrypt messages that can arbitrarily depend on the secret keys themselves. In this work, we show how to upgrade essentially the weakest form of KDM security into the strongest one. In particular, we assume the existence of asymmetric-key bit-encryptionthat iscircular-securein the 1-keysetting, meaning that it maintains security even if one can encrypt individual bits of a single secret key under itself. We also rely on a standard CPA-secure public-key encryption. We construct apublic-keyencryption scheme that isKDM secure for general functions(of a-priori bounded circuit size) in themulti-keysetting, meaning that it maintains security even if one can encrypt arbitrary functions of arbitrarily many secret keys under each of the public keys. As a special case, the latter guarantees security in the presence of arbitrary length key cycles. Prior work already showed how to amplifyn-key circular ton-key KDM security for general functions. Therefore, the main novelty of our work is to upgrade from 1-key ton-key security for arbitraryn. As an independently interesting feature of our result, our construction does not need to know the actualspecificationof the underlying 1-key circular secure scheme, and we only rely on theexistenceof some such scheme in the proof of security. In particular, we present auniversalconstruction of a multi-key KDM-secure encryption that is secure as long as some 1-key circular-secure scheme exists. While this feature is similar in spirit to <PERSON>’s universal construction of one-way functions, the way we achieve it is quite different technically, and does not come with the same “galactic inefficiency”.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_22"}, {"primary_key": "1114827", "vector": [], "sparse_vector": [], "title": "Fast Blind Rotation for Bootstrapping FHEs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blind rotation is one of the key techniques to construct fully homomorphic encryptions with the best known bootstrapping algorithms running in less than one second. Currently, the two main approaches, namely, AP and GINX, for realizing blind rotation are first introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 2014) and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT 2016), respectively. In this paper, we propose a new blind rotation algorithm based on a GSW-like encryption from the NTRU assumption. Our algorithm has performance asymptotically independent from the key distributions, and outperforms AP and GINX in both the evaluation key size and the computational efficiency (especially for large key distributions). By using our blind rotation algorithm as a building block, we present new bootstrapping algorithms for both LWE and RLWE ciphertexts. We implement our bootstrapping algorithm for LWE ciphertexts, and compare the actual performance with two bootstrapping algorithms, namely, FHEW/AP by <PERSON><PERSON> and <PERSON> (EUROCRYPT 2015) and TFHE/GINX by <PERSON><PERSON>tti, Gama, <PERSON><PERSON> and <PERSON><PERSON> (Journal of Cryptology 2020), that were implemented in the OpenFHE library. For parameters with ternary key distribution at 128-bit security, our bootstrapping only needs to store evaluation key of size 18.65 MB for blind rotation, which is about 89.8 times smaller than FHEW/AP and 2.9 times smaller than TFHE/GINX. Moreover, our bootstrapping can be done in 112 ms on a laptop, which is about 3.2 times faster than FHEW/AP and 2.1 times faster than TFHE/GINX. More improvements are available for large key distributions such as Gaussian distributions.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_1"}, {"primary_key": "1114828", "vector": [], "sparse_vector": [], "title": "PAC Privacy: Automatic Privacy Measurement and Control of Data Processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose and study a new privacy definition, termed Probably Approximately Correct (PAC) Privacy. PAC Privacy characterizes the information-theoretic hardness to recover sensitive data given arbitrary information disclosure/leakage during/after any processing. Unlike the classic cryptographic definition and Differential Privacy (DP), which consider theadversarial (input-independent) worst case, PAC Privacy is a simulatable metric that quantifiesthe instance-basedimpossibility of inference. A fully automatic analysis and proof generation framework is proposed: security parameters can be produced with arbitrarily high confidence via Monte-Carlo simulation for any black-box data processing oracle. This appealing automation property enables analysis of complicated data processing, where the worst-case proof in the classic privacy regime could be loose or even intractable. Moreover, we show that the produced PAC Privacy guarantees enjoy simple composition bounds and the automatic analysis framework can be implemented in an online fashion to analyze the composite PAC Privacy loss even under correlated randomness. On the utility side, the magnitude of (necessary) perturbation required in PAC Privacy isnotlower bounded by\\({\\varTheta }(\\sqrt{d})\\)for ad-dimensional release but could beO(1) for many practical data processing tasks, which is in contrast to the input-independent worst-case information-theoretic lower bound. Example applications of PAC Privacy are included with comparisons to existing works.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38545-2_20"}, {"primary_key": "1114829", "vector": [], "sparse_vector": [], "title": "Cuckoo Hashing in Cryptography: Optimal Parameters, Robustness and Applications.", "authors": ["<PERSON>"], "summary": "Cuckoo hashing is a powerful primitive that enables storing items using small space with efficient querying. At a high level, cuckoo hashing mapsnitems intobentries storing at most\\(\\ell \\)items such that each item is placed into one ofkrandomly chosen entries. Additionally, there is an overflow stash that can store at mostsitems. Many cryptographic primitives rely upon cuckoo hashing to privately embed and query data where it is integral to ensure small failure probability when constructing cuckoo hashing tables as it directly relates to the privacy guarantees. As our main result, we present a more query-efficient cuckoo hashing construction using more hash functions. For construction failure probability\\(\\epsilon \\), the query overhead of our scheme is\\(O(1 + \\sqrt{\\log (1/\\epsilon )/\\log n})\\). Our scheme has quadratically smaller query overhead than prior works for any target failure probability\\(\\epsilon \\). We also prove lower bounds matching our construction. Our improvements come from a new understanding of the locality of cuckoo hashing failures for small sets of items. We also initiate the study of robust cuckoo hashing where the input set may be chosen with knowledge of the hash functions. We present a cuckoo hashing scheme using more hash functions with query overhead\\(\\tilde{O}(\\log \\lambda )\\)that is robust against\\(\\textsf{poly}(\\lambda )\\)adversaries. Furthermore, we present lower bounds showing that this construction is tight and that extending previous approaches of large stashes or entries cannot obtain robustness except with\\(\\varOmega (n)\\)query overhead. As applications of our results, we obtain improved constructions for batch codes and PIR. In particular, we present the most efficient explicit batch code and blackbox reduction from single-query PIR to batch PIR.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38551-3_7"}, {"primary_key": "1114830", "vector": [], "sparse_vector": [], "title": "Compact Lattice Gadget and Its Applications to Hash-and-Sign Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Lattice gadgets and the associated algorithms are the essential building blocks of lattice-based cryptography. In the past decade, they have been applied to build versatile and powerful cryptosystems. However, the practical optimizations and designs of gadget-based schemes generally lag their theoretical constructions. For example, the gadget-based signatures have elegant design and capability of extending to more advanced primitives, but they are far less efficient than other lattice-based signatures. This work aims to improve the practicality of gadget-based cryptosystems, with a focus on hash-and-sign signatures. To this end, we develop a compact gadget framework in which the used gadget is asquarematrix instead of the short and fat one used in previous constructions. To work with this compact gadget, we devise a specialized gadget sampler, calledsemi-random sampler, to compute the approximate preimage. It firstdeterministicallycomputes the error and then randomly samples the preimage. We show that for uniformly random targets, the preimage and error distributions are simulatable without knowing the trapdoor. This ensures the security of the signature applications. Compared to the Gaussian-distributed errors in previous algorithms, the deterministic errors have a smaller size, which lead to a substantial gain in security and enables a practically working instantiation. As the applications, we present two practically efficient gadget-based signature schemes based on NTRU and Ring-LWE respectively. The NTRU-based scheme offers comparable efficiency to Falcon and Mitaka and a simple implementation without the need of generating the NTRU trapdoor. The LWE-based scheme also achieves a desirable overall performance. It not only greatly outperforms the state-of-the-art LWE-based hash-and-sign signatures, but also has an even smaller size than the LWE-based Fiat-Shamir signature scheme Dilithium. These results fill the long-term gap in practical gadget-based signatures.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_13"}, {"primary_key": "1114831", "vector": [], "sparse_vector": [], "title": "Tracing Quantum State Distinguishers via Backtracking.", "authors": ["<PERSON>"], "summary": "We show the following results: The post-quantum equivalence of indistinguishability obfuscation and differing inputs obfuscation in the restricted setting where the outputs differ on at most a polynomial number of points. Our result handles the case where the auxiliary input may contain aquantum state; previous results could only handle classical auxiliary input. Bounded collusion traitor tracing from general public key encryption, where the decoder is allowed to contain aquantum state. The parameters of the scheme grow polynomially in the collusion bound. Collusion-resistant traitor tracing with constant-size ciphertexts from general public key encryption, again forquantum state decoders. The public key and secret keys grow polynomially in the number of users. Traitor tracing with embedded identities in the keys, again forquantum state decoders, under a variety of different assumptions with different parameter size trade-offs. Traitor tracing and differing inputs obfuscation with quantum decoders/auxiliary input arises naturally when considering the post-quantum security of these primitives. We obtain our results by abstracting out a core algorithmic model, which we call the Back One Step (BOS) model. We prove a general theorem, reducing many quantum results including ours to designingclassicalalgorithms in the BOS model. We then provide simple algorithms for the particular instances studied in this work.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_1"}, {"primary_key": "1114832", "vector": [], "sparse_vector": [], "title": "Revisiting the Constant-Sum Winternitz One-Time Signature with Applications to SPHINCS+ and XMSS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hash-based signatures offer a conservative alternative to post-quantum signatures with arguably better-understood security than other post-quantum candidates. As a core building block of hash-based signatures, the efficiency of one-time signature (OTS) largely dominates that of hash-based signatures. The WOTS\\(^{+}\\)signature scheme (Africacrypt 2013) is the current state-of-the-art OTS adopted by the signature schemes standardized by NIST—XMSS, LMS, and SPHINCS\\(^+\\). A natural question is whether there is (and how much) room left for improving one-time signatures (and thus standard hash-based signatures). In this paper, we show that WOTS\\(^{+}\\)one-time signature, when adopting the constant-sum encoding scheme (<PERSON><PERSON> <PERSON>, Crypto 1992), is size-optimal not only under <PERSON><PERSON>’s OTS framework, but also among all tree-based OTS designs. Moreover, we point out a flaw in the DAG-based OTS design previously shown to be size-optimal at Asiacrypt 1996, which makes the constant-sum WOTS\\(^{+}\\)the most size-efficient OTS to the best of our knowledge. Finally, we evaluate the performance of constant-sum WOTS\\(^{+}\\)integrated into the SPHINCS\\(^+\\)(CCS 2019) and XMSS (PQC 2011) signature schemes which exhibit certain degrees of improvement in both signing time and signature size.", "published": "2023-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-38554-4_15"}]