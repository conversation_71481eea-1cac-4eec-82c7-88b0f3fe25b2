[{"primary_key": "4077497", "vector": [], "sparse_vector": [], "title": "Extended Tower Number Field Sieve: A New Complexity for the Medium Prime Case.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new variant of the number field sieve algorithm for discrete logarithms in\\(\\mathbb {F}_{p^n}\\)called exTNFS. The most important modification is done in the polynomial selection step, which determines the cost of the whole algorithm: if one knows how to select good polynomials to tackle discrete logarithms in\\(\\mathbb {F}_{p^\\kappa }\\), exTNFS allows to use this method when tackling\\(\\mathbb {F}_{p^{\\eta \\kappa }}\\)whenever\\(\\gcd (\\eta ,\\kappa )=1\\). This simple fact has consequences on the asymptotic complexity of NFS in the medium prime case, where the complexity is reduced from\\(L_Q(1/3,\\root 3 \\of {96/9})\\)to\\(L_Q(1/3,\\root 3 \\of {48/9})\\),\\(Q=p^n\\), respectively from\\(L_Q(1/3,2.15)\\)to\\(L_Q(1/3,1.71)\\)if multiple number fields are used. On the practical side, exTNFS can be used when\\(n=6\\)and\\(n=12\\)and this requires to updating the keysizes used for the associated pairing-based cryptosystems.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_20"}, {"primary_key": "4077498", "vector": [], "sparse_vector": [], "title": "Design in Type-I, Run in Type-III: Fast and Scalable Bilinear-Type Conversion Using Integer Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bilinear type conversion is to convert cryptographic schemes designed over symmetric groups instantiated with imperilled curves into ones that run over more secure and efficient asymmetric groups. In this paper we introduce a novel type conversion method calledIPConvusing 0–1 Integer Programming. Instantiated with a widely available IP solver, it instantly converts existing intricate schemes, and can process large-scale schemes that involves more than a thousand variables and hundreds of pairings. Such a quick and scalable method allows a new approach in designing cryptographic schemes over asymmetric bilinear groups. Namely, designers work without taking much care about asymmetry of computation but the converted scheme runs well in the asymmetric setting. We demonstrate the usefulness of conversion-aided design by presenting somewhat counter-intuitive examples where converted DLIN-based Groth-Sahai proofs are more compact than manually built SXDH-based proofs.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_14"}, {"primary_key": "4077499", "vector": [], "sparse_vector": [], "title": "Fully Secure Functional Encryption for Inner Products, from Standard Assumptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Functional encryption is a modern public-key paradigm where a master secret key can be used to derive sub-keys\\(SK_F\\)associated with certain functionsFin such a way that the decryption operation revealsF(M), ifMis the encrypted message, and nothing else. Recently, <PERSON><PERSON><PERSON><PERSON> <PERSON>.gave simple and efficient realizations of the primitive for the computation of linear functions on encrypted data: given an encryption of a vector\\(\\varvec{y}\\)over some specified base ring, a secret key\\(SK_{\\varvec{x}}\\)for the vector\\(\\varvec{x}\\)allows computing\\(\\langle \\varvec{x},\\varvec{y} \\rangle \\). Their technique surprisingly allows for instantiations under standard assumptions, like the hardness of the Decision Diffie-Hellman (\\(\\mathsf {DDH}\\)) and Learning-with-Errors (\\(\\mathsf {LWE}\\)) problems. Their constructions, however, are only proved secure againstselectiveadversaries, which have to declare the challenge messages\\(M_0\\)and\\(M_1\\)at the outset of the game. In this paper, we provide constructions that provably achieve security against more realisticadaptiveattacks (where the messages\\(M_0\\)and\\(M_1\\)may be chosen in the challenge phase, based on the previously collected information) for the same inner product functionality. Our constructions are obtained from hash proof systems endowed with homomorphic properties over the key space. They are (almost) as efficient as those of <PERSON>allaet al.and rely on the same hardness assumptions. In addition, we obtain a solution based on Paillier’s composite residuosity assumption, which was an open problem even in the case of selective adversaries. We also propose\\(\\mathsf {LWE}\\)-based schemes that allow evaluation of inner products modulo a primep, as opposed to the schemes of Abdallaet al.that are restricted to evaluations of integer inner products of short integer vectors. We finally propose a solution based on Paillier’s composite residuosity assumption that enables evaluation of inner products modulo an RSA integer\\(N = p \\cdot q\\). We demonstrate that the functionality of inner products over a prime field is powerful and can be used to construct bounded collusion FE for all circuits.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_12"}, {"primary_key": "4077500", "vector": [], "sparse_vector": [], "title": "Three&apos;s Compromised Too: Circular Insecurity for Any Cycle Length from (Ring-)LWE.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A public-key encryption scheme isk-circular secureif a cycle ofkencrypted secret keys\\((\\mathsf {Enc} _{pk_{1}}(sk_{2}), \\mathsf {Enc} _{pk_{2}}(sk_{3}), \\ldots , \\mathsf {Enc} _{pk_{k}}(sk_{1}))\\)is indistinguishable from encryptions of zeros. Circular security has applications in a wide variety of settings, ranging from security of symbolic protocols to fully homomorphic encryption. A fundamental question is whether standard security notions like IND-CPA/CCA implyk-circular security. For the case\\(k=2\\), several works over the past years have constructed counterexamples—i.e., schemes that are CPA or even CCA secure but not 2-circular secure—under a variety of well-studied assumptions (SXDH, decision linear, and LWE). However, for\\(k > 2\\)the only known counterexamples are based on strong general-purpose obfuscation assumptions. In this work we constructk-circular security counterexamples for any\\(k \\ge 2\\)based on (ring-)LWE. Specifically: for any constant\\(k=O(1)\\), we construct a counterexample based onn-dimensional (plain) LWE for\\(\\mathrm{poly}(n)\\)approximation factors; for any\\(k=\\mathrm{poly}(\\lambda )\\), we construct one based on degree-nring-LWE for at most subexponential\\(\\exp (n^{\\varepsilon })\\)factors. Moreover, both schemes are\\(k'\\)-circular insecure for\\(2 \\le k' \\le k\\). Notably, our ring-LWE construction does not immediately translate to an LWE-based one, because matrix multiplication is not commutative. To overcome this, we introduce a new “tensored” variant of LWE which provides the desired commutativity, and which we prove is actually equivalent to plain LWE.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_23"}, {"primary_key": "4077501", "vector": [], "sparse_vector": [], "title": "A Subfield Lattice Attack on Overstretched NTRU Assumptions - Cryptanalysis of Some FHE and Graded Encoding Schemes.", "authors": ["<PERSON>", "<PERSON>", "Léo Du<PERSON>"], "summary": "The subfield attack exploits the presence of a subfield to solve overstretched versions of the NTRU assumption: norming the public keyhdown to a subfield may lead to an easier lattice problem and any sufficiently good solution may be lifted to a short vector in the full NTRU-lattice. This approach was originally sketched in a paper of <PERSON><PERSON> and Szydlo at Eurocrypt’02 and there also attributed to <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>. However, because it does not apply for small moduli and hence NTRUEncrypt, it seems to have been forgotten. In this work, we resurrect this approach, fill some gaps, analyze and generalize it to any subfields and apply it to more recent schemes. We show that for significantly larger moduli — a case we call overstretched — the subfield attack is applicable and asymptotically outperforms other known attacks. This directly affects the asymptotic security of the bootstrappable homomorphic encryption schemes LTV and YASHE which rely on a mildly overstretched NTRU assumption: the subfield lattice attack runs in sub-exponential time\\(2^{O(\\lambda /\\log ^{1/3}\\lambda )}\\)invalidating the security claim of\\(2^{\\varTheta (\\lambda )}\\). The effect is more dramatic on GGH-like Multilinear Maps: this attack can run in polynomial time withoutencodings of zeronor thezero-testing parameter, yet requiring an additional quantum step to recover the secret parameters exactly. We also report on practical experiments. Running LLL in dimension 512 we obtain vectors that would have otherwise required running BKZ with block-size 130 in dimension 8192. Finally, we discuss concrete aspects of this attack, the condition on the modulusqto guarantee full immunity, discuss countermeasures and propose open questions.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_6"}, {"primary_key": "4077502", "vector": [], "sparse_vector": [], "title": "Efficiently Computing Data-Independent Memory-Hard Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A memory-hard function (MHF)fis equipped with aspace cost\\({\\sigma } \\)andtime cost\\({\\tau } \\)parameter such that repeatedly computing\\(f_{{\\sigma },{\\tau }}\\)on an application specific integrated circuit (ASIC) is not economically advantageous relative to a general purpose computer. Technically we would like that any (generalized) circuit for evaluating an iMHF\\(f_{{\\sigma },{\\tau }}\\)has area\\(\\times \\)time (AT) complexity at\\(\\varTheta ({\\sigma } ^2 * {\\tau })\\). A data-independent MHF (iMHF) has the added property that it can be computed with almost optimal memory and time complexity by an algorithm which accesses memory in a pattern independent of the input value. Such functions can be specified by fixing a directed acyclic graph (DAG)Gon\\(n=\\varTheta ({\\sigma } * {\\tau })\\)nodes representing its computation graph. In this work we develop new tools for analyzing iMHFs. First we define and motivate a new complexity measure capturing the amount ofenergy(i.e. electricity) required to compute a function. We argue that, in practice, this measure is at least as important as the more traditional AT-complexity. Next we describe an algorithm\\({{\\mathcal {A}}} \\)for repeatedly evaluating an iMHF based on an arbitrary DAGG. We upperbound both its energy and AT complexities per instance evaluated in terms of a certain combinatorial property ofG. Next we instantiate our attack for several general classes of DAGs which include those underlying many of the most important iMHF candidates in the literature. In particular, we obtain the following results which hold for all choices of parameters\\({\\sigma } \\)and\\({\\tau } \\)(and thread-count) such that\\(n={\\sigma } *{\\tau } \\). The Catena-Dragonfly function of [FLW13] has AT and energy complexities\\(O(n^{1.67})\\). The Catena-Butterfly function of [FLW13] has complexities is\\(O(n^{1.67})\\). The Double-Buffer and the Linear functions of [CGBS16] both have complexities in\\(O(n^{1.67})\\). The Argon2i function of [BDK15] (winner of the Password Hashing Competition [PHC]) has complexities\\(O(n^{7/4}\\log (n))\\). The Single-Buffer function of [CGBS16] has complexities\\(O(n^{7/4}\\log (n))\\). AnyiMHF can be computed by an algorithm with complexities\\(O(n^2/\\log ^{1-{\\epsilon }}(n))\\)for all\\({\\epsilon } > 0\\). In particular when\\({\\tau } =1\\)this shows that the goal of constructing an iMHF with AT-complexity\\(\\varTheta ({\\sigma } ^2 * {\\tau })\\)is unachievable. Along the way we prove a lemma upper-bounding the depth-robustness of any DAG which may prove to be of independent interest.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_9"}, {"primary_key": "4077503", "vector": [], "sparse_vector": [], "title": "Universal Constructions and Robust Combiners for Indistinguishability Obfuscation and Witness Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "Over the last few years a new breed of cryptographic primitives has arisen: on one hand they have previously unimagined utility and on the other hand they are not based on simple to state and tried out assumptions. With the on-going study of these primitives, we are left with several different candidate constructions each based on a different, not easy to express, mathematical assumptions, where some even turn out to be insecure. Acombinerfor a cryptographic primitive takes several candidate constructions of the primitive and outputs one construction that is as good as any of the input constructions. Furthermore, this combiner must be efficient: the resulting construction should remain polynomial-time even when combining polynomially many candidate. Combiners are especially important for a primitive where there are several competing constructions whose security is hard to evaluate, as is the case for indistinguishability obfuscation (IO) and witness encryption (WE). One place where the need for combiners appears is in design of auniversal construction, where one wishes to find “one construction to rule them all”: an explicit construction that is secure ifanyconstruction of the primitive exists. In a recent paper, <PERSON><PERSON><PERSON> and <PERSON><PERSON> posed as a challenge finding universal constructions for indistinguishability obfuscation and witness encryption. In this work we resolve this issue: we construct universal schemes for IO, and for witness encryption, and also resolve the existence of combiners for these primitives along the way. For IO, our universal construction and combiners can be built based oneitherassuming DDH, or assuming LWE, with security against subexponential adversaries. For witness encryption, we need only one-way functions secure against polynomial time adversaries.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_17"}, {"primary_key": "4077504", "vector": [], "sparse_vector": [], "title": "On the Relationship Between Statistical Zero-Knowledge and Statistical Randomized Encodings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Statistical Zero-knowledge proofs(<PERSON><PERSON><PERSON> et al. [GMR89]) allow a computationally unbounded server to convince a computationally limited client that an inputxis in a language\\(\\varPi \\)without revealing any additional information aboutxthat the client cannot compute by herself.Randomized encoding(RE) of functions (<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [IK00]) allows a computationally limited client to publish a single (randomized) message,\\(\\mathsf {Enc} (x)\\), from which the server learns whetherxis in\\(\\varPi \\)and nothing else. It is known that\\(\\mathcal {SRE} \\), the class of problems that admit statistically private randomized encoding with polynomial-time client and computationally unbounded server, is contained in the class\\(\\mathcal {SZK} \\)of problems that have statistical zero-knowledge proof. However, the exact relation between these two classes, and, in particular, the possibility of equivalence was left as an open problem. In this paper, we explore the relationship between\\(\\mathcal {SRE} \\)and\\(\\mathcal {SZK} \\), and derive the following results: In a non-uniform setting, statistical randomized encoding with one-side privacy (\\(\\textit{1}\\mathcal {RE} \\)) is equivalent to non-interactive statistical zero-knowledge (\\(\\mathcal {NISZK} \\)). These variants were studied in the past as natural relaxation/strengthening of the original notions. Our theorem shows that proving\\(\\mathcal {SRE} =\\mathcal {SZK} \\)is equivalent to showing that\\(\\textit{1}\\mathcal {RE} =\\mathcal {SRE} \\)and\\(\\mathcal {SZK} =\\mathcal {NISZK} \\). The latter is a well-known open problem (Goldreich et al. [GSV99]). If\\(\\mathcal {SRE} \\)is non-trivial (not in\\(\\mathcal {BPP} \\)), then infinitely-often one-way functions exist. The analog hypothesis for\\(\\mathcal {SZK} \\)yields onlyauxiliary-inputone-way functions (Ostrovsky [Ost91]), which is believed to be a significantly weaker implication. If there exists an average-case hard language withperfect randomized encoding, then collision-resistance hash functions (CRH) exist. Again, a similar assumption for\\(\\mathcal {SZK} \\)implies only constant-round statistically-hiding commitments, a primitive which seems weaker than CRH. We believe that our results sharpen the relationship between\\(\\mathcal {SRE} \\)and\\(\\mathcal {SZK} \\)and illuminates the core differences between these two classes.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_16"}, {"primary_key": "4077505", "vector": [], "sparse_vector": [], "title": "A 270 Attack on the Full MISTY1.", "authors": ["<PERSON><PERSON>ya Bar-On", "<PERSON>"], "summary": "MISTY1 is a block cipher designed by <PERSON><PERSON> in 1997. It is widely deployed in Japan, and is recognized internationally as a European NESSIE-recommended cipher and an ISO standard. After almost 20 years of unsuccessful cryptanalytic attempts, a first attack on the full MISTY1 was presented at CRYPTO 2015 by <PERSON><PERSON>. The attack, using a new technique calleddivision property, requires almost the full codebook and has time complexity of\\(2^{107.3}\\)encryptions. In this paper we present a new attack on the full MISTY1. It is based on <PERSON><PERSON>’s division property, along with a variety of refined key-recovery techniques. Our attack requires almost the full codebook (like <PERSON><PERSON>’s attack), but allows to retrieve 49 bits of the secret key in time complexity of only\\(2^{64}\\)encryptions, and the full key in time complexity of\\(2^{69.5}\\)encryptions. While our attack is clearly impractical due to its large data complexity, it shows that MISTY1 provides security of only\\(2^{70}\\)— significantly less than what was considered before.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_16"}, {"primary_key": "4077506", "vector": [], "sparse_vector": [], "title": "How to Prove Knowledge of Small Secrets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new zero-knowledge protocol applicable to additively homomorphic functions that map integer vectors to an Abelian group. The protocol demonstrates knowledge of a short preimage and achieves amortised efficiency comparable to the approach of <PERSON><PERSON><PERSON> and <PERSON><PERSON> from Crypto 2010, but gives a much tighter bound on what we can extract from a dishonest prover. Towards achieving this result, we develop an analysis for bins-and-balls games that might be of independent interest. We also provide a general analysis of rewinding of a cut-and-choose protocol as well as a method to use <PERSON><PERSON><PERSON><PERSON><PERSON>’s rejection sampling technique efficiently in an interactive protocol when many proofs are given simultaneously. Our new protocol yields improved proofs of plaintext knowledge for (Ring-)LWE-based cryptosystems, where such general techniques were not known before. Moreover, they can be extended to prove preimages of homomorphic hash functions as well.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_17"}, {"primary_key": "4077507", "vector": [], "sparse_vector": [], "title": "The SKINNY Family of Block Ciphers and Its Low-Latency Variant MANTIS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a new tweakable block cipher familySKINNY, whose goal is to compete with NSA recent designSIMONin terms of hardware/software performances, while proving in addition much stronger security guarantees with regards to differential/linear attacks. In particular, unlikeSIMON, we are able to provide strong bounds for all versions, and not only in the single-key model, but also in the related-key or related-tweak model.SKINNYhas flexible block/key/tweak sizes and can also benefit from very efficient threshold implementations for side-channel protection. Regarding performances, it outperforms all known ciphers for ASIC round-based implementations, while still reaching an extremely small area for serial implementations and a very good efficiency for software and micro-controllers implementations (SKINNYhas the smallest total number of AND/OR/XOR gates used for encryption process). Secondly, we presentMANTIS, a dedicated variant ofSKINNYfor low-latency implementations, that constitutes a very efficient solution to the problem of designing a tweakable block cipher for memory encryption.MANTISbasically reuses well understood, previously studied, known components. Yet, by putting those components together in a new fashion, we obtain a competitive cipher toPRINCEin latency and area, while being enhanced with a tweak input.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_5"}, {"primary_key": "4077508", "vector": [], "sparse_vector": [], "title": "Lightweight Multiplication in GF(2n) with Applications to MDS Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we consider the fundamental question of optimizing finite field multiplications with one fixed element. Surprisingly, this question did not receive much attention previously. We investigate which field representation, that is which choice of basis, allows for an optimal implementation. Here, the efficiency of the multiplication is measured in terms of the number of XOR operations needed to implement the multiplication. While our results are potentially of larger interest, we focus on a particular application in the second part of our paper. Here we construct new MDS matrices which outperform or are on par with all previous results when focusing on a round-based hardware implementation.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_23"}, {"primary_key": "4077509", "vector": [], "sparse_vector": [], "title": "Big-Key Symmetric Encryption: Resisting Key Exfiltration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper aims to move research in the bounded retrieval model (BRM) from theory to practice by considering symmetric (rather than public-key) encryption, giving efficient schemes, and providing security analyses with sharp, concrete bounds. The threat addressed is malware that aims to exfiltrate a user’s key. Our schemes aim to thwart this by using an enormously long key, yet paying for this almost exclusively in storage cost, not speed. Our main result is a general-purpose lemma, thesubkey prediction lemma, that gives a very good bound on an adversary’s ability to guess a (modest length) subkey of a big-key, the subkey consisting of the bits of the big-key found at random, specified locations, after the adversary has exfiltrated partial information about the big-key (e.g., half as many bits as the big-key is long). We then use this to design a new kind of key encapsulation mechanism, and, finally, a symmetric encryption scheme. Both are in the random-oracle model. We also give a less efficient standard-model scheme that is based on universal computational extractors (UCE). Finally, we define and achieve hedged BRM symmetric encryption, which provides authenticity in the absence of leakage.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_14"}, {"primary_key": "4077510", "vector": [], "sparse_vector": [], "title": "The Multi-user Security of Authenticated Encryption: AES-GCM in TLS 1.3.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We initiate the study of multi-user (mu) security of authenticated encryption (AE) schemes as a way to rigorously formulate, and answer, questions about the “randomized nonce” mechanism proposed for the use of the AE scheme GCM in TLS 1.3. We (1) Give definitions of mu ind (indistinguishability) and mu kr (key recovery) security for AE (2) Characterize the intent of nonce randomization as being improved mu security as a defense against mass surveillance (3) Cast the method as a (new) AE scheme RGCM (4) Analyze and compare the mu security of both GCM and RGCM in the model where the underlying block cipher is ideal, showing that the mu security of the latter is indeed superior in many practical contexts to that of the former, and (5) Propose an alternative AE scheme XGCM having the same efficiency as RGCM but better mu security and a more simple and modular design.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_10"}, {"primary_key": "4077511", "vector": [], "sparse_vector": [], "title": "A Practical Cryptanalysis of the Algebraic Eraser.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel cryptanalysis of the Algebraic Eraser primitive. This key agreement scheme, based on techniques from permutation groups, matrix groups and braid groups, is proposed as an underlying technology for ISO/IEC 29167-20, which is intended for authentication of RFID tags. SecureRF, the company owning the trademark Algebraic Eraser, markets it as suitable in general for lightweight environments such as RFID tags and other IoT applications. Our attack is practical on standard hardware: for parameter sizes corresponding to claimed 128-bit security, our implementation recovers the shared key using less than 8 CPU hours, and less than 64 MB of memory.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_7"}, {"primary_key": "4077512", "vector": [], "sparse_vector": [], "title": "Bounded Indistinguishability and the Complexity of Recovering Secrets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by cryptographic applications, we study the notion ofbounded indistinguishability, a natural relaxation of the well studied notion of bounded independence. We say that two distributions\\(\\mu \\)and\\(\\nu \\)over\\(\\varSigma ^n\\)arek-wise indistinguishableif their projections to anyksymbols are identical. We say that a function\\(f{:}\\varSigma ^n \\rightarrow \\mathrm {\\{0,1\\}}\\)is\\(\\mathrm {\\epsilon }\\)-fooled byk-wise indistinguishabilityiffcannot distinguish with advantage\\(\\mathrm {\\epsilon }\\)between any twok-wise indistinguishable distributions\\(\\mu \\)and\\(\\nu \\)over\\(\\varSigma ^n\\). We are interested in characterizing the class of functions that are fooled byk-wise indistinguishability. While the case ofk-wise independence (corresponding to one of the distributions being uniform) is fairly well understood, the more general case remained unexplored. When\\(\\varSigma = \\mathrm {\\{0,1\\}}\\), we observe that whetherfis fooled is closely related to its approximate degree. For larger alphabets\\(\\varSigma \\), we obtain several positive and negative results. Our results imply the first efficient secret sharing schemes with a high secrecy threshold in which the secret can be reconstructed in AC\\(^0\\). More concretely, we show that for every\\(0< \\sigma < \\rho \\le 1\\)it is possible to share a secret amongnparties so that any set of fewer than\\(\\sigma n\\)parties can learn nothing about the secret, any set of at least\\(\\rho n\\)parties can reconstruct the secret, and where both the sharing and the reconstruction are done by constant-depth circuits of size\\(\\mathrm {{poly}}(n)\\). We present additional cryptographic applications of our results to low-complexity secret sharing, visual secret sharing, leakage-resilient cryptography, and eliminating “selective failure” attacks.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_21"}, {"primary_key": "4077513", "vector": [], "sparse_vector": [], "title": "Another View of the Division Property.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A new distinguishing property against block ciphers, called the division property, was introduced by <PERSON><PERSON> at Eurocrypt 2015. Our work gives a new approach to it by the introduction of the notion of parity sets. First of all, this new notion permits us to formulate and characterize in a simple way the division property of any order. At a second step, we are interested in the way of building distinguishers on a block cipher by considering some further properties of parity sets, generalising the division property. We detail in particular this approach for substitution-permutation networks. To illustrate our method, we provide low-data distinguishers against reduced-roundPresent. These distinguishers reach a much higher number of rounds than generic distinguishers based on the division property and demonstrate, amongst others, how the distinguishers can be improved when the properties of the linear and the Sbox layers are taken into account. At last, this work provides an analysis of the resistance of Sboxes against this type of attacks, demonstrates links with the algebraic normal form of an Sbox as well as its inverse Sbox and exhibit design criteria for Sboxes to resist such attacks.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_24"}, {"primary_key": "4077514", "vector": [], "sparse_vector": [], "title": "FHE Circuit Privacy Almost for Free.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "Circuit privacy is an important property for many applications of fully homomorphic encryption. Prior approaches for achieving circuit privacy rely on superpolynomial noise flooding or on bootstrapping. In this work, we present a conceptually different approach to circuit privacy based on a novel characterization of the noise growth amidst homomorphic evaluation. In particular, we show that a variant of the GSW FHE for branching programs already achieves circuit privacy; this immediately yields a circuit-private FHE for NC\\(^1\\)circuits under the standard LWE assumption with polynomial modulus-to-noise ratio. Our analysis relies on a variant of the discrete Gaussian leftover hash lemma which states that\\(\\mathbf {e}^\\intercal \\mathbf {G}^{-1}(\\mathbf {v})+small\\)noisedoes not depend on\\(\\mathbf {v}\\). We believe that this result is of independent interest.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_3"}, {"primary_key": "4077515", "vector": [], "sparse_vector": [], "title": "Breaking the Circuit Size Barrier for Secure Computation Under DDH.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "Under the Decisional <PERSON><PERSON><PERSON><PERSON> (DDH) assumption, we present a 2-out-of-2 secret sharing scheme that supports a compact evaluation of branching programs on the shares. More concretely, there is an evaluation algorithm\\(\\mathsf{Eval}\\)with a single bit of output, such that if an input\\(w\\in \\{0,1\\}^n\\)is shared into\\((w^0,w^1)\\), then for any deterministic branching programPof sizeSwe have that\\(\\mathsf{Eval}(P,w^0)\\oplus \\mathsf{Eval}(P,w^1)=P(w)\\)except with at most\\(\\delta \\)failure probability. The running time of the sharing algorithm is polynomial innand the security parameter\\(\\lambda \\), and that of\\(\\mathsf{Eval}\\)is polynomial in\\(S,\\lambda \\), and\\(1/\\delta \\). This applies as a special case to boolean formulas of sizeSor boolean circuits of depth\\(\\log S\\). We also present a public-key variant that enables homomorphic computation on inputs contributed by multiple clients. The above result implies the following DDH-based applications: A secure 2-party computation protocol for evaluating any branching program or formula of sizeS, where the communication complexity is linear in the input size and only the running time grows withS. A secure 2-party computation protocol for evaluating layered boolean circuits of sizeSwith communication complexity\\(O(S/\\log S)\\). A 2-partyfunction secret sharingscheme, as defined by <PERSON> et al. (Eurocrypt 2015), for general branching programs (with inverse polynomial error probability). A 1-round 2-serverprivate information retrievalscheme supporting general searches expressed by branching programs. Prior to our work, similar results could only be achieved using fully homomorphic encryption. We hope that our approach will lead to more practical alternatives to known fully homomorphic encryption schemes in the context of low-communication secure computation.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_19"}, {"primary_key": "4077516", "vector": [], "sparse_vector": [], "title": "On Statistically Secure Obfuscation with Approximate Correctness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC ’07) prove that statistical indistinguishability obfuscation (iO) cannot exist if the obfuscator must maintain perfect correctness (under a widely believed complexity theoretic assumption:\\(\\mathcal {NP}\\not \\subseteq \\mathcal {SZK}\\subseteq \\mathcal {AM}\\cap \\mathbf {co}\\mathcal {AM}\\)). However, for many applications of iO, such as constructing public-key encryption from one-way functions (one of the main open problems in theoretical cryptography),approximatecorrectness is sufficient. It had been unknown thus far whether statistical approximate iO (saiO) can exist. We show that saiO does not exist, even for a minimal correctness requirement, if\\(\\mathcal {NP}\\not \\subseteq \\mathcal {AM}\\cap \\mathbf {co}\\mathcal {AM}\\), and if one-way functions exist. A simple complementary observation shows that if one-way functions do not exist, then average-case saiO exists. Technically, previous approaches utilized the behavior of the obfuscator onevasivefunctions, for which saiO always exists. We overcome this barrier by using a PRF as a “baseline” for the obfuscated program. We broaden our study and consider relaxed notions ofsecurityfor iO. We introduce the notion ofcorrelation obfuscation, where the obfuscations of equivalent circuits only need to be mildly correlated (rather than statistically indistinguishable). Perhaps surprisingly, we show that correlation obfuscators exist via a trivial construction for some parameter regimes, whereas our impossibility result extends to other regimes. Interestingly, within the gap between the parameters regimes that we show possible and impossible, there is a small fraction of parameters that still allow to build public-key encryption from one-way functions and thus deserve further investigation.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_19"}, {"primary_key": "4077517", "vector": [], "sparse_vector": [], "title": "Lattice-Based Fully Dynamic Multi-key FHE with Short Ciphertexts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a multi-key fully homomorphic encryption scheme that supports anunboundednumber of homomorphic operations for anunboundednumber of parties. Namely, it allows to perform arbitrarily many computational steps on inputs encrypted by an a-priori unbounded (polynomial) number of parties. Inputs from new parties can be introduced into the computation dynamically, so the final set of parties needs not be known ahead of time. Furthermore, the length of the ciphertexts, as well as the space complexity of an atomic homomorphic operation, grow onlylinearlywith the current number of parties. Prior works either supported only an a-priori bounded number of parties (<PERSON>-<PERSON>, <PERSON>romer and <PERSON><PERSON><PERSON>, STOC ’12), or only supported single-hop evaluation where all inputs need to be known before the computation starts (<PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, Crypto ’15, <PERSON><PERSON><PERSON> and <PERSON>ichs, Eurocrypt ’16). In all aforementioned works, the ciphertext length grew at least quadratically with the number of parties. Technically, our starting point is the LWE-based approach of previous works. Our result is achieved via a careful use of <PERSON><PERSON>’s bootstrapping technique, tailored to the specific scheme. Our hardness assumption is that the scheme of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> is circular secure (and thus bootstrappable). A leveled scheme can be achieved under standard LWE.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_8"}, {"primary_key": "4077518", "vector": [], "sparse_vector": [], "title": "Circuit-ABE from LWE: Unbounded Attributes and Semi-adaptive Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct an LWE-based key-policy attribute-based encryption (ABE) scheme that supports attributes ofunbounded polynomial length. Namely, the size of the public parameters is a fixed polynomial in the security parameter and a depth bound, and with these fixed length parameters, one can encrypt attributes of arbitrary length. Similarly, any polynomial size circuit that adheres to the depth bound can be used as the policy circuit regardless of its input length (recall that a depthdcircuit can have as many as\\(2^d\\)inputs). This is in contrast to previous LWE-based schemes where the length of the public parameters has to grow linearly with the maximal attribute length. We prove that our scheme issemi-adaptively secure, namely, the adversary can choose the challenge attribute after seeing the public parameters (but before any decryption keys). Previous LWE-based constructions were only able to achieve selective security. (We stress that the “complexity leveraging” technique is not applicable for unbounded attributes). We believe that our techniques are of interest at least as much as our end result. Fundamentally, selective security and bounded attributes are both shortcomings that arise out of the current LWE proof techniques thatprogram the challenge attributes into the public parameters. The LWE toolbox we develop in this work allows us todelay this programming. In a nutshell, the new tools include a way to generate an a-prioriunboundedsequence of LWE matrices, and have fine-grained control over which trapdoor is embedded in each and every one of them, all with succinct representation.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_13"}, {"primary_key": "4077519", "vector": [], "sparse_vector": [], "title": "UC Commitments for Modular Protocol Design and Applications to Revocation and Attribute Tokens.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Complex cryptographic protocols are often designed from simple cryptographic primitives, such as signature schemes, encryption schemes, verifiable random functions, and zero-knowledge proofs, by bridging between them with commitments to some of their inputs and outputs. Unfortunately, the known universally composable (UC) functionalities for commitments and the cryptographic primitives mentioned above do not allow such constructions of higher-level protocols as hybrid protocols. Therefore, protocol designers typically resort to primitives with property-based definitions, often resulting in complex monolithic security proofs that are prone to mistakes and hard to verify. We address this gap by presenting a UC functionality for non-interactive commitments that enables modular constructions of complex protocols within the UC framework. We also show how the new functionality can be used to construct hybrid protocols that combine different UC functionalities and use commitments to ensure that the same inputs are provided to different functionalities. We further provide UC functionalities for attribute tokens and revocation that can be used as building blocks together with our UC commitments. As an example of building a complex system from these new UC building blocks, we provide a construction (a hybrid protocol) of anonymous attribute tokens with revocation. Unlike existing accumulator-based schemes, our scheme allows one to accumulate several revocation lists into a single commitment value and to hide the revocation status of a user from other users and verifiers.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_8"}, {"primary_key": "4077520", "vector": [], "sparse_vector": [], "title": "Linicrypt: A Model for Practical Cryptography.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A wide variety of objectively practical cryptographic schemes can be constructed using only symmetric-key operations and linear operations. To formally study this restricted class of cryptographic algorithms, we present a new model calledLinicrypt. A Linicrypt program has access to a random oracle whose inputs and outputs are field elements, and otherwise manipulates data only via fixed linear combinations. Our main technical result is that it is possible to decidein polynomial timewhether two given Linicrypt programs induce computationally indistinguishable distributions (against arbitrary PPT adversaries, in the random oracle model). We show also that indistinguishability of Linicrypt programs can be expressed as an existential formula, making the model amenable toautomated program synthesis.In other words, it is possible to use a SAT/SMT solver to automatically generate Linicrypt programs satisfying a given security constraint. Interestingly, the properties of Linicrypt imply that this synthesis approach is both sound and complete. We demonstrate this approach by synthesizing Linicrypt constructions of garbled circuits.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_15"}, {"primary_key": "4077521", "vector": [], "sparse_vector": [], "title": "Rate-1, Linear Time and Additively Homomorphic UC Commitments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We construct the first UC commitment scheme for binary strings with the optimal properties of rate approaching 1 and linear time complexity (in the amortised sense, using a small number of seed OTs). On top of this, the scheme is additively homomorphic, which allows for applications to maliciously secure 2-party computation. As tools for obtaining this, we make three contributions of independent interest: we construct the first (binary) linear time encodable codes with non-trivial distance and rate approaching 1, we construct the first almost universal hash function with small seed that can be computed in linear time, and we introduce a new primitive called interactive proximity testing that can be used to verify whether a string is close to a given linear code.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_7"}, {"primary_key": "4077522", "vector": [], "sparse_vector": [], "title": "Efficient Zero-Knowledge Proof of Algebraic and Non-Algebraic Statements with Applications to Privacy Preserving Credentials.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Practical anonymous credential systems are generally built around sigma-protocol ZK proofs. This requires that credentials be based on specially formed signatures. Here we ask whether we can instead use a standard (say, RSA, or (EC)DSA) signature that includes formatting and hashing messages, as a credential, and still provide privacy. Existing techniques do not provide efficient solutions for proving knowledge of such a signature: On the one hand, ZK proofs based on garbled circuits (<PERSON><PERSON><PERSON><PERSON> et al. 2013) give efficient proofs for checking formatting of messages and evaluating hash functions. On the other hand they are expensive for checking algebraic relations such as RSA or discrete-log, which can be done efficiently with sigma protocols. We design new constructions obtaining the best of both worlds: combining the efficiency of the garbled circuit approach for non-algebraic statements and that of sigma protocols for algebraic ones. We then discuss how to use these as building-blocks to construct privacy-preserving credential systems based on standard RSA and (EC)DSA signatures. Other applications of our techniques include anonymous credentials with more complex policies, the ability to efficiently switch between commitments (and signatures) in different groups, and secure two-party computation on committed/signed inputs.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_18"}, {"primary_key": "4077523", "vector": [], "sparse_vector": [], "title": "Concurrent Non-Malleable Commitments (and More) in 3 Rounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The round complexity of commitment schemes secure against man-in-the-middle attacks has been the focus of extensive research for about 25 years. The recent breakthrough of <PERSON><PERSON> et al. [22] showed that 3 rounds are sufficient for (one-left, one-right) non-malleable commitments. This result matches a lower bound of [41]. The state of affairs leaves still open the intriguing problem of constructing 3-round concurrent non-malleable commitment schemes. In this paper we solve the above open problem by showing how to transform any 3-round (one-left one-right) non-malleable commitment scheme (with some extractability property) in a 3-round concurrent non-malleable commitment scheme. Our transform makes use of complexity leveraging and when instantiated with the construction of [22] gives a 3-round concurrent non-malleable commitment scheme from one-way permutations secure w.r.t. subexponential-time adversaries. We also show a 3-round arguments of knowledge and a 3-round identification scheme secure against concurrent man-in-the-middle attacks.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_10"}, {"primary_key": "4077524", "vector": [], "sparse_vector": [], "title": "EWCDM: An Efficient, Beyond-Birthday Secure, Nonce-Misuse Resistant MAC.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a nonce-based MAC construction called EWCDM (Encrypted <PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON>), based on an almost xor-universal hash function and a block cipher, with the following properties: (i) it is simple and efficient, requiring only two calls to the block cipher, one of which can be carried out in parallel to the hash function computation; (ii) it is provably secure beyond the birthday bound when nonces are not reused; (iii) it provably retains security up to the birthday bound in case of nonce misuse. Our construction is a simple modification of the Encrypted <PERSON><PERSON><PERSON> construction, which is known to achieve only (i) and (iii) when based on a block cipher. Underlying our new construction is a new PRP-to-PRF conversion method coinedEncrypted Davies<PERSON>Meyer, which turns a pair of secret random permutations into a function which is provably indistinguishable from a perfectly random function up to at least\\(2^{2n/3}\\)queries, wherenis the bit-length of the domain of the permutations.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_5"}, {"primary_key": "4077525", "vector": [], "sparse_vector": [], "title": "Probabilistic Termination and Composability of Cryptographic Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When analyzing the round complexity of multi-party computation (MPC), one often overlooks the fact that underlying resources, such as a broadcast channel, can by themselves be expensive to implement. For example, it is impossible to implement a broadcast channel by a (deterministic) protocol in a sub-linear (in the number of corrupted parties) number of rounds. The seminal works of <PERSON><PERSON> and <PERSON><PERSON><PERSON> from the early 80’s demonstrated that limitations as the above can be overcome by allowing parties to terminate in different rounds, igniting the study of protocols with probabilistic termination. However, absent a rigorous simulation-based definition, the suggested protocols are proven secure in a property-based manner, guaranteeing limited composability. In this work, we define MPC with probabilistic termination in the UC framework. We further prove a special universal composition theorem for probabilistic-termination protocols, which allows to compile a protocol using deterministic-termination hybrids into a protocol that uses expected-constant-round protocols for emulating these hybrids, preserving the expected round complexity of the calling protocol. We showcase our definitions and compiler by providing the first composable protocols (with simulation-based security proofs) for the following primitives, relying on point-to-point channels: (1) expected-constant-round perfect Byzantine agreement, (2) expected-constant-round perfect parallel broadcast, and (3) perfectly secure MPC with round complexity independent of the number of parties.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_9"}, {"primary_key": "4077526", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of GGH15 Multilinear Maps.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe a cryptanalysis of the GGH15 multilinear maps. Our attack breaks the multipartite key-agreement protocol in polynomial time by generating an equivalent user private key; it also applies to GGH15 with safeguards. We also describe attacks against variants of the GGH13 multilinear maps proposed by <PERSON><PERSON> (ePrint 2015/866) aiming at supporting graph-induced constraints, as in GGH15.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_21"}, {"primary_key": "4077527", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Supersingular Isogeny Diffie-<PERSON>man.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new suite of algorithms that significantly improve the performance of supersingular isogeny <PERSON> (SIDH) key exchange. Subsequently, we present a full-fledged implementation of SIDH that is geared towards the 128-bit quantum and 192-bit classical security levels. Our library is the first constant-time SIDH implementation and is up to 2.9 times faster than the previous best (non-constant-time) SIDH software. The high speeds in this paper are driven by compact, inversion-free point and isogeny arithmetic and fast SIDH-tailored field arithmetic: on an Intel Haswell processor, generating ephemeral public keys takes 46 million cycles for <PERSON> and 52 million cycles for <PERSON>, while computing the shared secret takes 44 million and 50 million cycles, respectively. The size of public keys is only 564 bytes, which is significantly smaller than most of the popular post-quantum key exchange alternatives. Ultimately, the size and speed of our software illustrates the strong potential of SIDH as a post-quantum key exchange candidate and we hope that these results encourage a wider cryptanalytic effort.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_21"}, {"primary_key": "4077528", "vector": [], "sparse_vector": [], "title": "Encryption Switching Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We formally define the primitive ofencryption switching protocol(ESP), allowing to switch between two encryption schemes. Intuitively, this two-party protocol converts given ciphertexts from one scheme into ciphertexts of the same messages under the other scheme, for any polynomial number ofswitches, in any direction. AlthoughESPis a special kind of two-party computation protocol, it turns out thatESPimplies general two-party computation (2-PC) under natural conditions. In particular, our new paradigm is tailored to the evaluation of functions over rings. Indeed, assuming the compatibility of two additively and multiplicatively homomorphic encryption schemes, switching ciphertexts makes it possible to efficiently reconcile the two internal laws. Since no such pair of public-key encryption schemes appeared in the literature, except for the non-interactive case of fully homomorphic encryption which still remains prohibitive in practice, we build the first multiplicatively homomorphic ElGamal-like encryption scheme over\\((\\mathbb {Z}_n,\\times )\\)as a complement to the Paillier encryption scheme over\\((\\mathbb {Z}_n,+)\\), wherenis a strong RSA modulus. Eventually, we also instantiate secureESPs between the two schemes, in front of malicious adversaries. This enhancement relies on a new technique calledrefreshable twin ciphertext pool, which we show being of independent interest. We additionally prove this is enough to argue the security of our general2-PCprotocol against malicious adversaries.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_12"}, {"primary_key": "4077529", "vector": [], "sparse_vector": [], "title": "Indifferentiability of 8-Round Feistel Networks.", "authors": ["Yuanxi Dai", "<PERSON>"], "summary": "We prove that a balanced 8-round Feistel network is indifferentiable from a random permutation, improving on previous 10-round results by <PERSON><PERSON><PERSON><PERSON> et al. and <PERSON> et al. Our simulator achieves security\\(O(q^8/2^n)\\), similarly to the security of <PERSON> et al. For further comparison, <PERSON><PERSON><PERSON><PERSON><PERSON> et al. achieve security\\(O(q^{12}/2^n)\\), while the original 14-round simulator of <PERSON><PERSON> et al. achieves security\\(O(q^{10}/2^n)\\).", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_4"}, {"primary_key": "4077530", "vector": [], "sparse_vector": [], "title": "On the Communication Required for Unconditionally Secure Multiplication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON>"], "summary": "Many information-theoretic secure protocols are known for general secure multi-party computation, in the honest majority setting, and in the dishonest majority setting with preprocessing. All known protocols that are efficient in the circuit size of the evaluated function follow the same “gate-by-gate” design pattern: we work through an arithmetic (boolean) circuit on secret-shared inputs, such that after we process a gate, the output of the gate is represented as a random secret sharing among the players. This approach usually allows non-interactive processing of addition gates but requires communication for every multiplication gate. Thus, while information-theoretic secure protocols are very efficient in terms of computational work, they (seem to) require more communication and more rounds than computationally secure protocols. Whether this is inherent is an open and probably very hard problem. However, in this work we show that it is indeed inherent for protocols that follow the “gate-by-gate” design pattern. We present the following results: In the honest majority setting, as well as for dishonest majority with preprocessing, any gate-by-gate protocol must communicate\\(\\varOmega (n)\\)bits for every multiplication gate, wherenis the number of players. In the honest majority setting, we show that one cannot obtain a bound that also grows with the field size. Moreover, for a constant number of players, amortizing over several multiplication gates does not allow us to save on the computational work, and – in a restricted setting – we show that this also holds for communication. All our lower bounds are met up to a constant factor by known protocols that follow the typical gate-by-gate paradigm. Our results imply that a fundamentally new approach must be found in order to improve the communication complexity of known protocols, such as BGW, GMW, SPDZ etc.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_16"}, {"primary_key": "4077531", "vector": [], "sparse_vector": [], "title": "Backdoors in Pseudorandom Number Generators: Possibility and Impossibility Results.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Inspired by the Dual EC DBRG incident, <PERSON><PERSON> et al. (Eurocrypt 2015) initiated the formal study of backdoored PRGs, showing that backdoored PRGs are equivalent to public key encryption schemes, giving constructions for backdoored PRGs (BPRGs), and showing how BPRGs can be “immunised” by careful post-processing of their outputs. In this paper, we continue the foundational line of work initiated by <PERSON><PERSON> et al., providing both positive and negative results. We first revisit the backdoored PRG setting of <PERSON><PERSON> et al., showing that PRGs can bemore stronglybackdoored than was previously envisaged. Specifically, we give efficient constructions of BPRGs for which, given a single generator output, <PERSON> Brother can recover the initial state and, therefore,alloutputs of the BPRG. Moreover, our constructions areforward-securein the traditional sense for a PRG, resolving an open question of <PERSON><PERSON> et al. in the negative. We then turn to the question of the effectiveness of backdoors in robust PRNGs with input (c.f<PERSON> et al., ACM-CCS 2013): generators in which the state can be regularly refreshed using an entropy source, and in which, provided sufficient entropy has been made available since the last refresh, the outputs will appear pseudorandom. The presence of a refresh procedure might suggest that <PERSON> could be defeated, since he would not be able to predict the values of the PRNG state backwards or forwards through the high-entropy refreshes. Unfortunately, we show that this intuition is not correct: we are also able to construct robust PRNGs with input that are backdoored in a backwards sense. Namely, given a single output, <PERSON> <PERSON> is able to rewind through a number of refresh operations to earlier “phases”, and recover all the generator’s outputs in those earlier phases. Finally, and ending on a positive note, we give an impossibility result: we provide a bound on the number of previous phases that <PERSON> Brother can compromise as a function of the state-size of the generator: smaller states provide more limited backdooring opportunities for Big Brother.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_15"}, {"primary_key": "4077532", "vector": [], "sparse_vector": [], "title": "Fine-Grained Cryptography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fine-grained cryptographic primitivesare ones that are secure against adversaries with an a-priori bounded polynomial amount of resources (time, space or parallel-time), where the honest algorithms use less resources than the adversaries they are designed to fool. Such primitives were previously studied in the context of time-bounded adversaries (<PERSON><PERSON><PERSON>, <PERSON>CM 1978), space-bounded adversaries (<PERSON><PERSON><PERSON> and <PERSON>, CRYPTO 1997) and parallel-time-bounded adversaries (<PERSON><PERSON>, IPL 1987). Our goal is come up with fine-grained primitives (in the setting of parallel-time-bounded adversaries) and to show unconditional security of these constructions when possible, or base security on widely believed separation of worst-case complexity classes. We show: \\({\\textsf {NC}^{1}}\\)-cryptography: Under the assumption that, we construct one-way functions, pseudo-random generators (with sub-linear stretch), collision-resistant hash functions and most importantly,public-key encryption schemes, all computable in\\({\\textsf {NC}^{1}}\\)and secure against all\\({\\textsf {NC}^{1}}\\)circuits. Our results rely heavily on the notion of randomized encodings pioneered by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, and crucially, makenon-black-boxuse of randomized encodings for logspace classes. \\({\\textsf {AC}^{0}}\\)-cryptography: We construct (unconditionally secure) pseudo-random generators with arbitrary polynomial stretch, weak pseudo-random functions, secret-key encryption and perhaps most interestingly,collision-resistant hash functions, computable in\\({\\textsf {AC}^{0}}\\)and secure against all\\({\\textsf {AC}^{0}}\\)circuits. Previously, one-way permutations and pseudo-random generators (with linear stretch) computable in\\({\\textsf {AC}^{0}}\\)and secure against\\({\\textsf {AC}^{0}}\\)circuits were known from the works of Håstad and Braverman.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_19"}, {"primary_key": "4077533", "vector": [], "sparse_vector": [], "title": "Automatic Search of Meet-in-the-Middle and Impossible Differential Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tracking bits through block ciphers and optimizing attacks at hand is one of the tedious task symmetric cryptanalysts have to deal with. It would be nice if a program will automatically handle them at least for well-known attack techniques, so that cryptanalysts will only focus on finding new attacks. However, current automatic tools cannot be used as is, either because they are tailored for specific ciphers or because they only recover a specific part of the attacks and cryptographers are still needed to finalize the analysis. In this paper we describe a generic algorithm exhausting the best meet-in-the-middle and impossible differential attacks on a very large class of block ciphers from byte to bit-oriented, SPN, Feistel and Lai-Massey block ciphers. Contrary to previous tools that target to find the best differential / linear paths in the cipher and leave the cryptanalysts to find the attack using these paths, we automatically find the best attacks by considering the cipher and the key schedule algorithms. The building blocks of our algorithm led to two algorithms designed to find the best simple meet-in-the-middle attacks and the best impossible truncated differential attacks respectively. We recover and improve many attacks on AES, mCRYPTON, SIMON, IDEA, KTANTAN, PRINCE and ZORRO. We show that this tool can be used by designers to improve their analysis.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_6"}, {"primary_key": "4077534", "vector": [], "sparse_vector": [], "title": "Memory-Efficient Algorithms for Finding Needles in Haystacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One of the most common tasks in cryptography and cryptanalysis is to find some interesting event (a needle) in an exponentially large collection (haystack) of\\(N=2^n\\)possible events, or to demonstrate that no such event is likely to exist. In particular, we are interested in finding needles which are defined as events that happen with an unusually high probability of\\(p \\gg 1/N\\)in a haystack which is an almost uniform distribution onNpossible events. When the search algorithm can only sample values from this distribution, the best known time/memory tradeoff for finding such an event requires\\(O(1/Mp^2)\\)time givenO(M) memory. In this paper we develop much faster needle searching algorithms in the common cryptographic setting in which the distribution is defined by applying some deterministic functionfto random inputs. Such a distribution can be modelled by a random directed graph withNvertices in which almost all the vertices haveO(1) predecessors while the vertex we are looking for has an unusually large number ofO(pN) predecessors. When we are given only a constant amount of memory, we propose a new search methodology which we callNestedRho. Aspincreases, such random graphs undergo several subtle phase transitions, and thus the log-log dependence of the time complexityTonpbecomes a piecewise linear curve which bends four times. Our new algorithm is faster than the\\(O(1/p^2)\\)time complexity of the best previous algorithm in the full range of\\(1/N<p<1\\), and in particular it improves the previous time complexity by a significant factor of\\(\\sqrt{N}\\)for anypin the range\\(N^{-0.75}<p< N^{-0.5}\\). When we are given more memory, we show how to combine theNestedRhotechnique with the parallel collision search technique in order to further reduce its time complexity. Finally, we show how to apply our new search technique to more complicated distributions with multiple peaks when we want to find all the peaks whose probabilities are higher thanp.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_7"}, {"primary_key": "4077535", "vector": [], "sparse_vector": [], "title": "Spooky Encryption and Its Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Consider encryptingninputs undernindependent public keys. Given the ciphertexts\\(\\{c_i = \\mathsf {Enc}_{\\mathsf {pk}_i}(x_i)\\}_i\\), <PERSON> outputs ciphertexts\\(c'_1,\\ldots ,c'_n\\)that decrypt to\\(y_1,\\ldots ,y_n\\)respectively. What relationships between the\\(x_i\\)’s and\\(y_i\\)’s can <PERSON> induce? Motivated by applications to delegating computations, <PERSON><PERSON> et al. [11] showed that a semantically secure scheme disallowssignalingin this setting, meaning that\\(y_i\\)cannot depend on\\(x_j\\)for\\(j \\ne i\\). On the other hand if the scheme is homomorphic then anylocal(component-wise) relationship is achievable, meaning that each\\(y_i\\)can be an arbitrary function of\\(x_i\\). However, there are also relationships which are neither signaling nor local. <PERSON><PERSON> et al. asked if it is possible to have encryption schemes that support such “spooky” relationships. Answering this question is the focus of our work. Our first result shows that, under the\\(\\textsf {LWE}\\)assumption, there exist encryption schemes supporting a large class of “spooky” relationships, which we calladditive function sharing(AFS) spooky. In particular, for any polynomial-time functionf, <PERSON> can ensure that\\(y_1,\\ldots ,y_n\\)are random subject to\\(\\sum _{i=1}^n y_i = f(x_1,\\ldots ,x_n)\\). For this result, the public keys all depend on common public randomness. Our second result shows that, assuming sub-exponentially hard indistinguishability obfuscation (iO) (and additional more standard assumptions), we can remove the common randomness and choose the public keys completely independently. Furthermore, in the case of\\(n=2\\)inputs, we get a scheme that supports an even larger class of spooky relationships. We discuss several implications of AFS-spooky encryption. Firstly, it gives a strong counter-example to a method proposed by Aiello et al. [1] for building arguments for\\(\\textsf {NP}\\)from homomorphic encryption. Secondly, it gives a simple 2-round multi-party computation protocol where, at the end of the first round, the parties can locally compute an additive secret sharing of the output. Lastly, it immediately yields a function secret sharing (FSS) scheme for all functions. We also define a notion ofspooky-freeencryption, which ensures that no spooky relationship is achievable. We show that any non-malleable encryption scheme is spooky-free. Furthermore, we can construct spooky-freehomomorphicencryption schemes from SNARKs, and it remains an open problem whether it is possible to do so from falsifiable assumptions.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_4"}, {"primary_key": "4077536", "vector": [], "sparse_vector": [], "title": "Message Transmission with Reverse Firewalls - Secure Communication on Corrupted Machines.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Suppose <PERSON> wishes to send a message to <PERSON> privately over an untrusted channel. Cryptographers have developed a whole suite of tools to accomplish this task, with a wide variety of notions of security, setup assumptions, and running times. However, almost all prior work on this topic made a seemingly innocent assumption: that <PERSON> has access to a trusted computer with a proper implementation of the protocol. The Snowden revelations show us that, in fact, powerful adversaries can and will corrupt users’ machines in order to compromise their security. And, (presumably) accidental vulnerabilities are regularly found in popular cryptographic software, showing that users cannot even trust implementations that were created honestly. This leads to the following (seemingly absurd) question: “Can <PERSON> securely send a message to <PERSON> even if she cannot trust her own computer?!” <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> recently studied this question. They show a strong impossibility result that in particular rules out even semantically secure public-key encryption in their model. However, <PERSON><PERSON><PERSON> and <PERSON><PERSON> recently introduced a new framework for solving such problems: reverse firewalls. A secure reverse firewall is a third party that “sits between <PERSON> and the outside world” and modifies her sent and received messages so thateven if the her machine has been corrupted, <PERSON>’s security is still guaranteed. We show how to use reverse firewalls to sidestep the impossibility result of <PERSON><PERSON> et al., and we achieve strong security guarantees in this extreme setting. Indeed, we find a rich structure of solutions that vary in efficiency, security, and setup assumptions, in close analogy with message transmission in the classical setting. Our strongest and most important result shows a protocol that achieves interactive, concurrent CCA-secure message transmission with a reverse firewall—i.e., CCA-secure message transmission on a possibly compromised machine! Surprisingly, this protocol is quite efficient and simple, requiring only four rounds and a small constant number of public-key operations for each party. It could easily be used in practice. Behind this result is a technical composition theorem that shows how key agreement with a sufficiently secure reverse firewall can be used to construct a message-transmission protocol with its own secure reverse firewall.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_13"}, {"primary_key": "4077537", "vector": [], "sparse_vector": [], "title": "Two-Message, Oblivious Evaluation of Cryptographic Functionalities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of two round oblivious evaluation of cryptographic functionalities. In this setting, one party\\(P_1\\)holds a private key\\(\\textit{sk}\\)for a provably secure instance of a cryptographic functionality\\(\\mathcal {F} \\)and the second party\\(P_2\\)wishes to evaluate\\(\\mathcal {F} _\\textit{sk}\\)on a valuex. Although it has been known for 22 years thatgeneralfunctionalities cannot be computed securely in the presence of malicious adversaries with only two rounds of communication, we show the existence of a round optimal protocol that obliviously evaluatescryptographicfunctionalities. Our protocol is provably secure against malicious receivers under standard assumptions and does not rely on heuristic (setup) assumptions. Our main technical contribution is a novelnonblack-box technique, which makesnonblack-box use of the security reduction of\\(\\mathcal {F} _\\textit{sk}\\). Specifically, our proof of malicious receiver security usesthe codeof the reduction, which reduces the security of\\(\\mathcal {F} _\\textit{sk}\\)to some hard problem, in order to break that problem directly. Instantiating our framework, we obtain the first two-round oblivious pseudorandom function that is secure in the standard model. This question was left open since the invention of OPRFs in 1997.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_22"}, {"primary_key": "4077538", "vector": [], "sparse_vector": [], "title": "Quantum Homomorphic Encryption for Polynomial-Sized Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new scheme for quantum homomorphic encryption which is compact and allows for efficient evaluation of arbitrary polynomial-sized quantum circuits. Building on the framework of Broad<PERSON> and Jeff<PERSON> [BJ15] and recent results in the area of instantaneous non-local quantum computation [Spe15], we show how to construct quantum gadgets that allow perfect correction of the errors which occur during the homomorphic evaluation ofTgates on encrypted quantum data. Our scheme can be based on any classical (leveled) fully homomorphic encryption (FHE) scheme and requires no computational assumptions besides those already used by the classical scheme. The size of our quantum gadget depends on the space complexity of the classical decryption function – which aligns well with the current efforts to minimize the complexity of the decryption function. Our scheme (or slight variants of it) offers a number of additional advantages such as ideal compactness, the ability to supply gadgets “on demand”, and circuit privacy for the evaluator against passive adversaries.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_1"}, {"primary_key": "4077539", "vector": [], "sparse_vector": [], "title": "Adaptive Versus Non-Adaptive Strategies in the Quantum Setting with Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove a general relation betweenadaptiveandnon-adaptivestrategies in the quantum setting, i.e., between strategies where the adversary can or cannot adaptively base its action on some auxiliary quantum side information. Our relation holds in a very general setting, and is applicable as long as we can control the bit-size of the side information, or, more generally, its “information content”. Since adaptivity is notoriously difficult to handle in the analysis of (quantum) cryptographic protocols, this gives us a very powerful tool: as long as we have enough control over the side information, it is sufficient to restrict ourselves to non-adaptive attacks. We demonstrate the usefulness of this methodology with two examples. The first is a quantum bit commitment scheme based on1-bit cut-and-choose. Since bit commitment implies oblivious transfer (in the quantum setting), and oblivious transfer is universal for two-party computation, this implies the universality of 1-bit cut-and-choose, and thus solves the main open problem of [9]. The second example is a quantum bit commitment scheme proposed in 1993 by Brassardet al. It was originally suggested as an unconditionally secure scheme, back when this was thought to be possible. We partly restore the scheme by proving it secure in (a variant of) the bounded quantum storage model. In both examples, the fact that the adversary holds quantum side information obstructs a direct analysis of the scheme, and we circumvent it by analyzing a non-adaptive version, which can be done by means of known techniques, and applying our main result.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_2"}, {"primary_key": "4077540", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the FLIP Family of Stream Ciphers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At Eurocrypt 2016, <PERSON><PERSON><PERSON> et al. proposedFLIP, a new family of stream ciphers intended for use in Fully Homomorphic Encryption systems. Unlike its competitors which either have a low initial noise that grows at each successive encryption, or a high constant noise, theFLIPfamily of ciphers achieves a low constant noise thanks to a new construction calledfilter permutator. In this paper, we present an attack on the early version ofFLIPthat exploits the structure of the filter function and the constant internal state of the cipher. Applying this attack to the two instantiations proposed by <PERSON><PERSON><PERSON> et al. allows for a key recovery in\\(2^{54}\\)basic operations (resp.\\(2^{68}\\)), compared to the claimed security of\\(2^{80}\\)(resp.\\(2^{128}\\)).", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_17"}, {"primary_key": "4077541", "vector": [], "sparse_vector": [], "title": "Spooky Interaction and Its Discontents: Compilers for Succinct Two-Message Argument Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We are interested in constructing short two-message arguments for various languages, where the complexity of the verifier is small (e.g. linear in the input size, or even sublinear if the input is coded appropriately). In 2000 <PERSON><PERSON> et al. suggested the tantalizing possibility of obtaining such arguments for all ofNP. These have proved elusive, despite extensive efforts. Our work builds on the compiler of <PERSON><PERSON> and <PERSON><PERSON>, which takes as input an interactive proof system consisting of several rounds and produces a two-message argument system. The proof of soundness of their compiler relies on superpolynomial hardness assumptions. In this work we obtain a succinct two-message argument system for any language in NC, where the verifier’s work is linear (or even polylogarithmic). Soundness relies on any standard (polynomially hard) private information retrieval scheme or fully homomorphic encryption scheme. This is the first non trivial two-message succinct argument system that is based on a standard polynomial-time hardness assumption. We obtain this result by proving that the compiler is sound (under standard polynomial hardness assumptions) if the verifier in the original protocol runs in logarithmic space and public coins. We obtain our two-message argument by applying the compiler to an interactive proof protocol of <PERSON>was<PERSON>, <PERSON><PERSON> and <PERSON>. On the other hand, we prove that under standard assumptions there is a sound interactive proof protocol that, when run through the compiler, results in a protocol that is not sound.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_5"}, {"primary_key": "4077542", "vector": [], "sparse_vector": [], "title": "Towards Sound Fresh Re-keying with <PERSON> (Physical) Learning Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most leakage-resilient cryptographic constructions aim at limiting the information adversaries can obtain about secret keys. In the case of asymmetric algorithms, this is usually obtained by secret sharing (aka masking) the key, which is made easy by their algebraic properties. In the case of symmetric algorithms, it is rather key evolution that is exploited. While more efficient, the scope of this second solution is limited to stateful primitives that easily allow for key evolution such as stream ciphers. Unfortunately, it seems generally hard to avoid the need of (at least one) execution of a stateless primitive, both for encryption and authentication protocols. As a result, fresh re-keying has emerged as an alternative solution, in which a block cipher that is hard to protect against side-channel attacks is re-keyed with a stateless function that is easy to mask. While previous proposals in this direction were all based on heuristic arguments, we propose two new constructions that, for the first time, allow a more formal treatment of fresh re-keying. More precisely, we reduce the security of our re-keying schemes to two building blocks that can be of independent interest. The first one is an assumption of Learning Parity with Leakage, which leverages the noise that is available in side-channel measurements. The second one is based on the Learning With Rounding assumption, which can be seen as an alternative solution for low-noise implementations. Both constructions are efficient and easy to mask, since they are key homomorphic or almost key homomorphic.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_10"}, {"primary_key": "4077543", "vector": [], "sparse_vector": [], "title": "Obfuscation Combiners.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Obfuscation is challenging; we currently have practical candidates with rather vague security guarantees on the one side, and theoretical constructions which have recently experienced jeopardizing attacks against the underlying cryptographic assumptions on the other side. This motivates us to study and presentrobust combiners for obfuscators,which integrate several candidate obfuscators into a single obfuscator which is secure as long as a quorum of the candidates is indeed secure. We give several results about building obfuscation combiners, with matching upper and lower bounds for the precise quorum of secure candidates. Namely, we show that one can build 3-out-of-4 obfuscation combiners where at least three of the four combiners are secure, whereas 2-out-of-3 structural combiners (which combine the obfuscator candidates in a black-box sense) with only two secure candidates, are impossible. Our results generalize to\\((2\\gamma +1)\\)-out-of-\\((3\\gamma +1)\\)combiners for the positive result, and to\\(2\\gamma \\)-out-of-\\(3\\gamma \\)results for the negative result, for any integer\\(\\gamma \\). To reduce overhead, we definedetecting combiners, where the combined obfuscator may sometimes produce an error-indication instead of the desired output, indicating that some of the component obfuscators is faulty. We present a\\((\\gamma +1)\\)-out-of-\\((2\\gamma +1)\\)detecting combiner for any integer\\(\\gamma \\), bypassing the previous lower bound. We further show that\\(\\gamma \\)-out-of-\\(2\\gamma \\)structural detecting combiners are again impossible. Since our approach can be used for practical obfuscators, as well as for obfuscators proven secure (based on assumptions), we also briefly report on implementation results for some applied obfuscator programs.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_18"}, {"primary_key": "4077544", "vector": [], "sparse_vector": [], "title": "Semantic Security and Indistinguishability in the Quantum World.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "At CRYPTO 2013, <PERSON><PERSON> and <PERSON><PERSON><PERSON> initiated the study of quantum-secure encryption. They proposed first indistinguishability definitions for the quantum world where the actual indistinguishability only holds for classical messages, and they provide arguments why it might be hard to achieve a stronger notion. In this work, we show that stronger notions are achievable, where the indistinguishability holds for quantum superpositions of messages. We investigate exhaustively the possibilities and subtle differences in defining such a quantum indistinguishability notion for symmetric-key encryption schemes. We justify our stronger definition by showing its equivalence to novel quantum semantic-security notions that we introduce. Furthermore, we show that our new security definitions cannot be achieved by a large class of ciphers – those which are quasi-preserving the message length. On the other hand, we provide a secure construction based on quantum-resistant pseudorandom permutations; this construction can be used as a generic transformation for turning a large class of encryption schemes into quantum indistinguishable and hence quantum semantically secure ones. Moreover, our construction is the first completely classical encryption scheme shown to be secure against an even stronger notion of indistinguishability, which was previously known to be achievable only by using quantum messages and arbitrary quantum encryption circuits.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_3"}, {"primary_key": "4077545", "vector": [], "sparse_vector": [], "title": "TWORAM: Efficient Oblivious RAM in Two Rounds with Applications to Searchable Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present\\(\\mathsf {TWORAM}\\), an asymptotically efficient oblivious RAM (ORAM) protocol providing oblivious access (read and write) of a memory indexyin exactlytworounds: The client prepares an encrypted query encapsulatingyand sends it to the server. The server accesses memory\\(\\mathsf {M}\\)obliviously and returns encrypted information containing the desired value\\(\\mathsf {M}[y]\\). The cost of\\(\\mathsf {TWORAM}\\)is only a multiplicative factor of security parameter higher than the tree-based ORAM schemes such as the path ORAM scheme of <PERSON><PERSON> et al. [34]. \\(\\mathsf {TWORAM}\\)gives rise to interesting applications, and in particular to a 4-round symmetric searchable encryption scheme where search is sublinear in the worst case and the search pattern is not leaked—the access pattern can also be concealed assuming the documents are stored in the obliviously accessed memory\\(\\mathsf {M}\\).", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_20"}, {"primary_key": "4077546", "vector": [], "sparse_vector": [], "title": "Revisiting the Cryptographic Hardness of Finding a Nash Equilibrium.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The exact hardness of computing a Nash equilibrium is a fundamental open question in algorithmic game theory. This problem is complete for the complexity classPPAD. It is well known that problems inPPADcannot be\\(\\mathrm {NP}\\)-complete unless\\(\\mathrm {NP}=\\mathrm {coNP}\\). Therefore, a natural direction is to reduce the hardness ofPPADto the hardness of problems used in cryptography. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS 2015] prove the hardness ofPPADassuming the existence of quasi-polynomially hard indistinguishability obfuscation and sub-exponentially hard one-way functions. This leaves open the possibility of basingPPADhardness on simpler, polynomially hard, computational assumptions. We make further progress in this direction and reducePPADhardness directly to polynomially hard assumptions. Our first result proves hardness ofPPADassuming the existence ofpolynomially hardindistinguishability obfuscation (\\(i\\mathcal {O}\\)) and one-way permutations. While this improves upon <PERSON><PERSON><PERSON> et al.’s work, it does not give us a reduction to simpler, polynomially hard computational assumption because constructions of\\(i\\mathcal {O}\\)inherently seems to require assumptions with sub-exponential hardness. In contrast,public key functional encryptionis a much simpler primitive and does not suffer from this drawback. Our second result shows that\\(\\mathsf{PPAD}\\)hardness can be based onpolynomially hardcompact public key functional encryption and one-way permutations. Our results further demonstrate the power of polynomially hard compact public key functional encryption which is believed to be weaker than indistinguishability obfuscation. Our techniques are general and we expect them to have various applications.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_20"}, {"primary_key": "4077547", "vector": [], "sparse_vector": [], "title": "On the Power of Secure Two-Party Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 2007, SIAM JoC 2009) introduced the powerful “MPC-in-the-head” technique that provided a general transformation of information-theoretic MPC protocols secure against passive adversaries to a ZK proof in a “black-box” way. In this work, we extend this technique and provide a generic transformation of any semi-honest secure two-party computation (2PC) protocol (with mild adaptive security guarantees) in the so calledoblivious-transferhybrid model to anadaptiveZK proof for any NP-language, in a “black-box” way assuming only one-way functions. Our basic construction based on Goldreich-Mi<PERSON><PERSON>-<PERSON><PERSON>’s 2PC protocol yields an adaptive ZK proof with communication complexity proportional to quadratic in the size of the circuit implementing the NP relation. Previously such proofs relied on an expensive Karp reduction of the NP language to Graph Hamiltonicity (<PERSON><PERSON> and <PERSON> (TCC 2009, Journal of Cryptology 2011)). We also improve our basic construction to obtain the first linear-rate adaptive ZK proofs by relying on efficient maliciously secure 2PC protocols. Core to this construction is a new way of transforming 2PC protocols to efficient (adaptively secure) instance-dependent commitment schemes. As our second contribution, we provide a general transformation to construct a randomized encoding of a functionffrom any 2PC protocol that securely computes a related functionality (in a black-box way). We show that if the 2PC protocol has mild adaptive security guarantees then the resulting randomized encoding (RE) can be decomposed to an offline/online encoding. As an application of our techniques, we show how to improve the construction of Lapidot and Shamir (Crypto 1990) to obtain a four-round ZK proof with an “input-delayed” property. Namely, the honest prover’s algorithm does not require the actual statement to be proved until the last round. We further generalize this to obtain a four-round “commit and prove” zero-knowledge with the same property where the prover commits to a witnesswin the second message and proves a statementxregarding the witnesswthat is determined only in the fourth round.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_14"}, {"primary_key": "4077548", "vector": [], "sparse_vector": [], "title": "Adaptively Secure Garbled Circuits from One-Way Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A garbling scheme is used to garble a circuit C and an input x in a way that reveals the output C(x) but hides everything else. In many settings, the circuit can be garbled off-line without strict efficiency constraints, but the input must be garbled very efficiently on-line, with much lower complexity than evaluating the circuit. <PERSON>'s garbling scheme [31] has essentially optimal on-line complexity, but only achieves selective security, where the adversary must choose the input x prior to seeing the garbled circuit. It has remained an open problem to achieve adaptive security, where the adversary can choose x after seeing the garbled circuit, while preserving on-line efficiency. In this work, we modify <PERSON>'s scheme in a way that allows us to prove adaptive security under one-way functions. In our main instantiation we achieve on-line complexity only proportional to the width w of the circuit. Alternatively we can also get an instantiation with on-line complexity only proportional to the depth d (and the output size) of the circuit, albeit incurring in a $$2^{O(d)}$$ security loss in our reduction. More broadly, we relate the on-line complexity of adaptively secure garbling schemes in our framework to a certain type of pebble complexity of the circuit. As our main tool, of independent interest, we develop a new notion of somewhere equivocal encryption, which allows us to efficiently equivocate on a small subset of the message bits.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_6"}, {"primary_key": "4077549", "vector": [], "sparse_vector": [], "title": "Network-Hiding Communication and Applications to Multi-party Protocols.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As distributed networks are heavily used in modern applications, new security challenges emerge. In a multi-party computation (in short, MPC) protocol over an incomplete network, such a challenge is to hide, to the extent possible, the topology of the underlying communication network. Such a topology-hiding (aka network hiding) property is in fact very relevant in applications where anonymity is needed. To our knowledge, with the exception of two recent works by <PERSON><PERSON> al.[ITCS 2015] and by <PERSON><PERSON> al.[TCC 2015], existing MPC protocols do not hide the topology of the underlying communication network. Moreover, the above two solutions are either not applicable to arbitrary networks (as is [ITCS 2015]) or, as in [TCC 2015], they make non-black-box and recursive use of cryptographic primitives resulting in an unrealistic communication and computation complexity even for simple, i.e., low degree and diameter, networks. Our work suggests the first topology-hiding communication protocol for incomplete networks which makes black-box use of the underlying cryptographic assumption—in particular, a public-key encryption scheme—and tolerates any adversary who passively corrupts arbitrarily many network nodes. Our solutions are based on a new, enhanced variant of threshold homomorphic encryption, in short, TH-PKE, that requires no a-priori setup and allows to circulate an encrypted message over any (unknown) incomplete network and then decrypt it without revealing any network information to intermediate nodes. We show how to realize this enhanced TH-PKE from the DDH assumption. The black-box nature of our scheme, along with some optimization tricks that we employ, makes our communication protocol more efficient than existing solutions. We then use our communication protocol to make any semi-honest secure MPC protocol topology-hiding with a reasonable—i.e., for simple networks, polynomial with small constants—communication and computation overhead. We further show how to construct anonymous broadcast without using expensive MPCs to setup the original pseudonyms.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_12"}, {"primary_key": "4077550", "vector": [], "sparse_vector": [], "title": "Key-Alternating Ciphers and Key-Length Extension: Exact Bounds and Multi-user Security.", "authors": ["Viet Tung Hoang", "<PERSON>"], "summary": "The best existing bounds on the concrete security of key-alternating ciphers (<PERSON> and <PERSON>, EUROCRYPT ’14) are onlyasymptoticallytight, and the quantitative gap with the best existing attacks remains numerically substantial for concrete parameters. Here, we proveexactbounds on the security of key-alternating ciphers and extend them to XOR cascades, the most efficient construction for key-length extension. Our bounds essentially match, for any possible query regime, the advantage achieved by the best existing attack. Our treatment also extends to the multi-user regime. We show that the multi-user security of key-alternating ciphers and XOR cascades is very close to the single-user case, i.e., given enough rounds, it does not substantially decrease as the number of users increases. On the way, we also provide the first explicit treatment of multi-user security for key-length extension, which is particularly relevant given the significant security loss of block ciphers (even if ideal) in the multi-user setting. The common denominator behind our results are new techniques for information-theoretic indistinguishability proofs that both extend and refine existing proof techniques like the H-coefficient method.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_1"}, {"primary_key": "4077551", "vector": [], "sparse_vector": [], "title": "Secure Protocol Transformations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the rich literature of secure multi-party computation (MPC), several important results rely on “protocol transformations,” whereby protocols from one model of MPC are transformed to protocols from another model. Motivated by the goal of simplifying and unifying results in the area of MPC, we formalize a general notion of black-box protocol transformations that captures previous transformations from the literature as special cases, and present several new transformations. We motivate our study of protocol transformations by presenting the following applications. Simplifying feasibility results: Easily rederive a result in <PERSON><PERSON><PERSON>’s book (2004), on MPC with full security in the presence of an honest majority, from an earlier result in the book, on MPC that offers “security with abort.” Rederive the classical result of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (1989) by applying a transformation to the simpler protocols of <PERSON><PERSON><PERSON> et al. or <PERSON> et al. (1988). Efficiency improvements: The first “constant-rate” MPC protocol for a constant number of parties that offers full information-theoretic security with an optimal threshold, improving over the protocol of Rabin and Ben-Or; A fully secure MPC protocol with optimal threshold that improves over a previous protocol of <PERSON><PERSON><PERSON> et al. (2012) in the case of “deep and narrow” computations; A fully secure MPC protocol with near-optimal threshold that improves over a previous protocol of <PERSON><PERSON><PERSON><PERSON> et al. (2010) by improving the dependence on the security parameter from linear to polylogarithmic; An efficient new transformation from passive-secure two-party computation in the OT-hybrid and OLE-hybrid model to zero-knowledge proofs, improving over a recent similar transformation of Hazay and Venkitasubramaniam (2016) for the case of static zero-knowledge, which is restricted to the OT-hybrid model and requires a large number of commitments. Finally, we prove theimpossibilityof two simple types of black-box protocol transformations, including an unconditional variant of a previous negative result of Rosulek (2012) that relied on the existence of one-way functions.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_15"}, {"primary_key": "4077552", "vector": [], "sparse_vector": [], "title": "Breaking Symmetric Cryptosystems Using Quantum Period Finding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>Plase<PERSON>"], "summary": "Due to <PERSON><PERSON><PERSON>s algorithm, quantum computers are a severe threat for public key cryptography. This motivated the cryptographic community to search for quantum-safe solutions. On the other hand, the impact of quantum computing on secret key cryptography is much less understood. In this paper, we consider attacks where an adversary can query an oracle implementing a cryptographic primitive in a quantum superposition of different states. This model gives a lot of power to the adversary, but recent results show that it is nonetheless possible to build secure cryptosystems in it. We study applications of a quantum procedure called<PERSON><PERSON><PERSON><PERSON>s algorithm(the simplest quantum period finding algorithm) in order to attack symmetric cryptosystems in this model. Following previous works in this direction, we show that several classical attacks based on finding collisions can be dramatically sped up using <PERSON>’s algorithm: finding a collision requires\\(\\varOmega (2^{n/2})\\)queries in the classical setting, but when collisions happen with some hidden periodicity, they can be found with onlyO(n) queries in the quantum model. We obtain attacks with very strong implications. First, we show that the most widely used modes of operation for authentication and authenticated encryption (e.g.CBC-MAC, PMAC, GMAC, GCM, and OCB) are completely broken in this security model. Our attacks are also applicable to many CAESAR candidates: CLOC, AEZ, COPA, OTR, POET, OMD, and Minalpher. This is quite surprising compared to the situation with encryption modes: Anandet al.show that standard modes are secure with a quantum-secure PRF. Second, we show that <PERSON>s algorithm can also be applied to slide attacks, leading to an exponential speed-up of a classical symmetric cryptanalysis technique in the quantum model.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_8"}, {"primary_key": "4077553", "vector": [], "sparse_vector": [], "title": "Optimal Security Proofs for Signatures from Identification Schemes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We perform a concrete security treatment of digital signature schemes obtained from canonical identification schemes via the Fiat-<PERSON>ham<PERSON> transform. If the identification scheme is random self-reducible and satisfies the weakest possible security notion (hardness of key-recoverability), then the signature scheme obtained via Fiat-Shamir is unforgeable against chosen-message attacks in the multi-user setting. Our security reduction is in the random oracle model and loses a factor of roughly\\(Q_h\\), the number of hash queries. Previous reductions incorporated an additional multiplicative loss ofN, the number of users in the system. Our analysis is done in small steps via intermediate security notions, and all our implications have relatively simple proofs. Furthermore, for each step, we show the optimality of the given reduction in terms of model assumptions and tightness. As an important application of our framework, we obtain a concrete security treatment for Schnorr signatures in the multi-user setting.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_2"}, {"primary_key": "4077554", "vector": [], "sparse_vector": [], "title": "Circular Security Separations for Arbitrary Length Cycles from LWE.", "authors": ["Venkata Koppula", "<PERSON>"], "summary": "We describe a public key encryption that is IND-CPA secure under the Learning with Errors (LWE) assumption, but that is not circular secure for arbitrary length cycles. Previous separation results for cycle length greater than 2 require the use of indistinguishability obfuscation, which is not currently realizable under standard assumptions.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_24"}, {"primary_key": "4077555", "vector": [], "sparse_vector": [], "title": "Network Oblivious Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the goal of improving the concrete efficiency of secure multiparty computation (MPC), we study the possibility of implementing an infrastructure for MPC. We propose an infrastructure based on oblivious transfer (OT), which would consist of OT channels between some pairs of parties in the network. We devise information-theoretically secure protocols that allow additional pairs of parties to establish secure OT correlations using the help of other parties in the network in the presence of a dishonest majority. Our main technical contribution is an upper bound that matches a lower bound of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Crypto 2007), who studied the number of OT channels necessary and sufficient for MPC. In particular, we characterize whichn-party OT graphsGallowt-secure computation of OT correlations between all pairs of parties, showing that this is possible if and only if the complement ofGdoes not contain the complete bipartite graph\\(K_{n-t,n-t}\\)as a subgraph.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_13"}, {"primary_key": "4077556", "vector": [], "sparse_vector": [], "title": "XPX: Generalized Tweakable Even-Mansour with Improved Security Guarantees.", "authors": ["<PERSON>"], "summary": "We present\\(\\mathrm {XPX}\\), a tweakable blockcipher based on a single permutation\\(P\\). On input of a tweak\\((t_{11},t_{12},t_{21},t_{22})\\in \\mathcal {T}\\)and a messagem, it outputs ciphertext\\(c=P(m\\oplus \\varDelta _1)\\oplus \\varDelta _2\\), where\\(\\varDelta _1=t_{11}k\\oplus t_{12}P(k)\\)and\\(\\varDelta _2=t_{21}k\\oplus t_{22}P(k)\\). Here, the tweak space\\(\\mathcal {T}\\)is required to satisfy a certain set of trivial conditions (such as\\((0,0,0,0)\\not \\in \\mathcal {T}\\)). We prove that\\(\\mathrm {XPX}\\)with any such tweak space is a strong tweakable pseudorandom permutation. Next, we consider the security of\\(\\mathrm {XPX}\\)under related-key attacks, where the adversary can freely select a key-deriving function upon every evaluation. We prove that\\(\\mathrm {XPX}\\)achieves various levels of related-key security, depending on the set of key-deriving functions and the properties of\\(\\mathcal {T}\\). For instance, if\\(t_{12}, t_{22}\\ne 0\\)and\\((t_{21}, t_{22})\\ne (0,1)\\)for all tweaks,\\(\\mathrm {XPX}\\)is XOR-related-key secure.\\(\\mathrm {XPX}\\)generalizes Even-Mansour (\\(\\mathrm {EM}\\)), but also Rogaway’s\\(\\mathrm {XEX}\\)based on\\(\\mathrm {EM}\\), and various other tweakable blockciphers. As such,\\(\\mathrm {XPX}\\)finds a wide range of applications. We show how our results on\\(\\mathrm {XPX}\\)directly imply related-key security of the authenticated encryption schemes Prøst-\\(\\mathrm {COPA}\\)and\\(\\mathrm {Minalpher}\\), and how a straightforward adjustment to the MAC function\\(\\mathrm {Chaskey}\\)and to keyed Sponges makes them provably related-key secure.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_3"}, {"primary_key": "4077557", "vector": [], "sparse_vector": [], "title": "Annihilation Attacks for Multilinear Maps: Cryptanalysis of Indistinguishability Obfuscation over GGH13.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we present a new class of polynomial-time attacks on the original multilinear maps of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (2013). Previous polynomial-time attacks on GGH13 were “zeroizing” attacks that generally required the availability of low-level encodings of zero. Most significantly, such zeroizing attacks were not applicable to candidate indistinguishability obfuscation (iO) schemes. iO has been the subject of intense study. To address this gap, we introduceannihilation attacks, which attack multilinear maps using non-linear polynomials. Annihilation attacks can work in situations where there are no low-level encodings of zero. Using annihilation attacks, we give the first polynomial-time cryptanalysis of candidate iO schemes over GGH13. More specifically, we exhibit two simple programs that are functionally equivalent, and show how to efficiently distinguish between the obfuscations of these two programs. Given the enormous applicability of iO, it is important to devise iO schemes that can avoid attack. We discuss some initial directions for safeguarding against annihilating attacks.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_22"}, {"primary_key": "4077558", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of a Theorem: Decomposing the Only Known Solution to the Big APN Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ek<PERSON>", "<PERSON>"], "summary": "The existence of Almost Perfect Non-linear (APN) permutations operating on an even number of bits has been a long standing open question until <PERSON> et al., who work for the NSA, provided an example on 6 bits in 2009. In this paper, we apply methods intended to reverse-engineer S-Boxes with unknown structure to this permutation and find a simple decomposition relying on the cube function over\\(GF(2^3)\\). More precisely, we show that it is a particular case of a permutation structure we introduce, thebutterfly. Such butterflies are 2n-bit mappings with two CCZ-equivalent representations: one is a quadratic non-bijective function and one is a degree\\(n+1\\)permutation. We show that these structures always have differential uniformity at most 4 whennis odd. A particular case of this structure is actually a 3-round Feistel Network with similar differential and linear properties. These functions also share an excellent non-linearity for\\(n=3,5,7\\). Furthermore, we deduce a bitsliced implementation and significantly reduce the hardware cost of a 6-bit APN permutation using this decomposition, thus simplifying the use of such a permutation as building block for a cryptographic primitive.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_4"}, {"primary_key": "4077559", "vector": [], "sparse_vector": [], "title": "Counter-in-Tweak: Authenticated Encryption Modes for Tweakable Block Ciphers.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We propose the Synthetic Counter-in-Tweak (\\(\\mathsf {SCT}\\)) mode, which turns a tweakable block cipher into a nonce-based authenticated encryption scheme (with associated data). The\\(\\mathsf {SCT}\\)mode combines in a SIV-like manner a Wegman-Carter MAC inspired from\\(\\mathsf {PMAC}\\)for the authentication part and a new counter-like mode for the encryption part, with the unusual property that the counter is applied on the tweak input of the underlying tweakable block cipher rather than on the plaintext input. Unlike many previous authenticated encryption modes,\\(\\mathsf {SCT}\\)enjoys provable security beyond the birthday bound (and even up to roughly\\(2^n\\)tweakable block cipher calls, wherenis the block length, when the tweak length is sufficiently large) in the nonce-respecting scenario where nonces are never repeated. In addition,\\(\\mathsf {SCT}\\)ensures security up to the birthday bound even when nonces are reused, in the strong nonce-misuse resistance sense (MRAE) of Rogaway and Shrimpton (EUROCRYPT 2006). To the best of our knowledge, this is the first authenticated encryption mode that provides at the same time close-to-optimal security in the nonce-respecting scenario and birthday-bound security for the nonce-misuse scenario. While two passes are necessary to achieve MRAE-security, our mode enjoys a number of desirable features: it is simple, parallelizable, it requires the encryption direction only, it is particularly efficient for small messages compared to other nonce-misuse resistant schemes (no precomputation is required) and it allows incremental update of associated data.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_2"}, {"primary_key": "4077560", "vector": [], "sparse_vector": [], "title": "ParTI - Towards Combined Hardware Countermeasures Against Side-Channel and Fault-Injection Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Side-channel analysis and fault-injection attacks are known as major threats to any cryptographic implementation. Hardening cryptographic implementations with appropriate countermeasures is thus essential before they are deployed in the wild. However, countermeasures for both threats are of completely different nature: Side-channel analysis is mitigated by techniques that hide or mask key-dependent information while resistance against fault-injection attacks can be achieved by redundancy in the computation for immediate error detection. Since already the integration of any single countermeasure in cryptographic hardware comes with significant costs in terms of performance and area, a combination of multiple countermeasures is expensive and often associated with undesired side effects. In this work, we introduce a countermeasure for cryptographic hardware implementations that combines the concept of a provably-secure masking scheme (i.e., threshold implementation) with an error detecting approach against fault injection. As a case study, we apply our generic construction to the lightweight LED cipher. Our LED instance achieves first-order resistance against side-channel attacks combined with a fault detection capability that is superior to that of simple duplication for most error distributions at an increased area demand of 12 %.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_11"}, {"primary_key": "4077561", "vector": [], "sparse_vector": [], "title": "A Modular Treatment of Cryptographic APIs: The Symmetric-Key Case.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Application Programming Interfaces (APIs) to cryptographic tokens like smartcards and Hardware Security Modules (HSMs) provide users with commands to manage and use cryptographic keys stored on trusted hardware. Their design is mainly guided by industrial standards with only informal security promises. In this paper we propose cryptographic models for the security of such APIs. The key feature of our approach is that it enables modular analysis. Specifically, we show that a secure cryptographic API can be obtained by combining a secure API for key-management together with secure implementations of, for instance, encryption or message authentication. Our models are the first to provide such compositional guarantees while considering realistic adversaries that can adaptively corrupt keys stored on tokens. We also provide a proof of concept instantiation (from a deterministic authenticated-encryption scheme) of the key-management portion of cryptographic API.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_11"}, {"primary_key": "4077562", "vector": [], "sparse_vector": [], "title": "New Insights on AES-Like SPN Ciphers.", "authors": ["<PERSON>", "Mei<PERSON> Liu", "<PERSON><PERSON>", "Longjiang Qu", "<PERSON>"], "summary": "It has been proved in Eurocrypt 2016 by <PERSON>et al. that if the details of the S-boxes are not exploited, an impossible differential and a zero-correlation linear hull can extend over at most 4 rounds of the AES. This paper concentrates on distinguishing properties of AES-like SPN ciphers by investigating the details of both the underlying S-boxes and the MDS matrices, and illustrates some new insights on the security of these schemes. Firstly, we construct several types of 5-round zero-correlation linear hulls for AES-like ciphers that adoptidentical S-boxesto construct the round function and that havetwo identical elements in a column of the inverse of their MDS matrices. We then use these linear hulls to construct 5-round integrals provided that the difference of two sub-key bytes is known. Furthermore, we prove that we can always distinguish 5 rounds of such ciphers from random permutations even when the difference of the sub-keys is unknown. Secondly, the constraints for the S-boxes and special property of the MDS matrices can be removed if the cipher is used as a building block of the Miyaguchi-Preneel hash function. As an example, we construct two types of 5-round distinguishers for the hash function Whirlpool. Finally, we show that, in the chosen-ciphertext mode, there exist some nontrivial distinguishers for 5-round AES. To the best of our knowledge, this is the longest distinguisher for the round-reduced AES in the secret-key setting. Since the 5-round distinguisher for the AES can only be constructed in the chosen-ciphertext mode,the security margin for the round-reduced AES under the chosen-plaintext attack may be different from that under the chosen-ciphertext attack.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_22"}, {"primary_key": "4077563", "vector": [], "sparse_vector": [], "title": "Adversary-Dependent Lossy Trapdoor Function from Hardness of Factoring Semi-smooth RSA Subgroup Moduli.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Lossy trapdoor functions (LTDFs), proposed by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC’08), are known to have a number of applications in cryptography. They have been constructed based on various assumptions, which include the quadratic residuosity (QR) and decisional composite residuosity (DCR) assumptions, which are factoring-baseddecisionassumptions. However, there is no known construction of an LTDF based on the factoring assumption or other factoring-related search assumptions. In this paper, we first define a notion ofadversary-dependent lossy trapdoor functions(ad-LTDFs) that is a weaker variant of LTDFs. Then we construct an ad-LTDF based on the hardness of factorizing RSA moduli of a special form called semi-smooth RSA subgroup (SS) moduli proposed by <PERSON><PERSON> (TCC’05). Moreover, we show that ad-LTDFs can replace LTDFs in many applications. Especially, we obtain the first factoring-based deterministic encryption scheme that satisfies the security notion defined by <PERSON><PERSON><PERSON> et al. (CRYPTO’08) without relying on a decision assumption. Besides direct applications of ad-LTDFs, by a similar technique, we construct a chosen ciphertext secure public key encryption scheme whose ciphertext overhead is the shortest among existing schemes based on the factoring assumption w.r.t. SS moduli.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53008-5_1"}, {"primary_key": "4077564", "vector": [], "sparse_vector": [], "title": "Cryptography with Auxiliary Input and Trapdoor from Constant-Noise LPN.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (STOC 2009) initiated the study of the Learning Parity with Noise (LPN) problem with (static) exponentially hard-to-invert auxiliary input. In particular, they showed that under a new assumption (called Learning Subspace with Noise) the above is quasi-polynomially hard in the high (polynomially close to uniform) noise regime. Inspired by the “sampling from subspace” technique by <PERSON> (eprint 2009/467) and <PERSON> et al. (ITCS 2010), we show that standard LPN can work in a mode (reducible to itself) where the constant-noise LPN (by sampling its matrix from a random subspace) is robust against sub-exponentially hard-to-invert auxiliary input with comparable security to the underlying LPN. Plugging this into the framework of [DKL09], we obtain the same applications as considered in [DKL09] (i.e., CPA/CCA secure symmetric encryption schemes, average-case obfuscators, reusable and robust extractors) with resilience to a more general class of leakages, improved efficiency and better security under standard assumptions. As a main contribution, under constant-noise LPN with certain sub-exponential hardness (i.e.,\\(2^{\\omega (n^{1/2})}\\)for secret sizen) we obtain a variant of the LPN with security on poly-logarithmic entropy sources, which in turn implies CPA/CCA secure public-key encryption (PKE) schemes and oblivious transfer (OT) protocols. Prior to this, basing PKE and OT on constant-noise LPN had been an open problem since <PERSON><PERSON><PERSON><PERSON>’s work (FOCS 2003).", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_9"}, {"primary_key": "4077565", "vector": [], "sparse_vector": [], "title": "The Magic of ELFs.", "authors": ["<PERSON>"], "summary": "We introduce the notion of anExtremely Lossy Function(ELF). An ELF is a family of functions with an image size that is tunable anywhere from injective to having a polynomial-sized image. Moreover, for any efficient adversary, for a sufficiently large polynomialr(necessarily chosen to be larger than the running time of the adversary), the adversary cannot distinguish the injective case from the case of image sizer. We develop a handful of techniques for using ELFs, and show that such extreme lossiness is useful for instantiating random oracles in several settings. In particular, we show how to use ELFs to build secure point function obfuscation with auxiliary input, as well as polynomially-many hardcore bits for any one-way function. Such applications were previously known from strong knowledge assumptions — for example polynomially-many hardcore bits were only know from differing inputs obfuscation, a notion whose plausibility has been seriously challenged. We also use ELFs to build a simple hash function withoutput intractability, a new notion we define that may be useful for generating common reference strings. Next, we give a construction of ELFs relying on theexponentialhardness of the decisional <PERSON><PERSON><PERSON>-<PERSON> problem, which is plausible in pairing-based groups. Combining with the applications above, our work gives several practical constructions relying on qualitatively different — and arguably better — assumptions than prior works.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53018-4_18"}, {"primary_key": "4077566", "vector": [], "sparse_vector": [], "title": "Programmable Hash Functions from Lattices: Short Signatures and IBEs with Small Key Sizes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Driven by the open problem raised by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [34], we study the formalization of lattice-based programmable hash function (PHF), and give two types of constructions by using several techniques such as a novel combination of cover-free sets and lattice trapdoors. Under the Inhomogeneous Small Integer Solution (ISIS) assumption, we show that any (non-trivial) lattice-based PHF is collision-resistant, which gives a direct application of this new primitive. We further demonstrate the power of lattice-based PHF by giving generic constructions of signature and identity-based encryption (IBE) in the standard model, which not only provide a way to unify several previous lattice-based schemes using the partitioning proof techniques, but also allow us to obtain a new short signature scheme and a new fully secure IBE scheme with keys consisting of a logarithmic number of matrices/vectors in the security parameter\\(\\kappa \\). Besides, we also give a refined way of combining two concrete PHFs to construct an improved short signature scheme with short verification keys from weaker assumptions. In particular, our methods depart from the confined guessing technique of <PERSON><PERSON><PERSON> et al. [8] that was used to construct previous standard model short signature schemes with short verification keys by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [24] and by <PERSON><PERSON><PERSON><PERSON><PERSON> [6], and allow us to achieve existential unforgeability against chosen message attacks (EUF-CMA) without resorting to chameleon hash functions.", "published": "2016-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-53015-3_11"}]