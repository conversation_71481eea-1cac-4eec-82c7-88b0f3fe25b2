[{"primary_key": "4516130", "vector": [], "sparse_vector": [], "title": "Learning Activation Functions to Improve Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Artificial neural networks typically have a fixed, non-linear activation function at each neuron. We have designed a novel form of piecewise linear activation function that is learned independently for each neuron using gradient descent. With this adaptive activation function, we are able to improve upon deep neural network architectures composed of static rectified linear units, achieving state-of-the-art performance on CIFAR-10 (7.51%), CIFAR-100 (30.83%), and a benchmark from high-energy physics involving Higgs boson decay modes.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516132", "vector": [], "sparse_vector": [], "title": "Learning Non-deterministic Representations with Energy-based Ensembles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The goal of a generative model is to capture the distribution underlying the data, typically through latent variables. After training, these variables are often used as a new representation, more effective than the original features in a variety of learning tasks. However, the representations constructed by contemporary generative models are usually point-wise deterministic mappings from the original feature space. Thus, even with representations robust to class-specific transformations, statistically driven models trained on them would not be able to generalize when the labeled data is scarce. Inspired by the stochasticity of the synaptic connections in the brain, we introduce Energy-based Stochastic Ensembles. These ensembles can learn non-deterministic representations, i.e., mappings from the feature space to a family of distributions in the latent space. These mappings are encoded in a distribution over a (possibly infinite) collection of models. By conditionally sampling models from the ensemble, we obtain multiple representations for every input example and effectively augment the data. We propose an algorithm similar to contrastive divergence for training restricted Boltzmann stochastic ensembles. Finally, we demonstrate the concept of the stochastic representations on a synthetic dataset as well as test them in the one-shot learning scenario on MNIST.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516144", "vector": [], "sparse_vector": [], "title": "Diverse Embedding Neural Network Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose Diverse Embedding Neural Network (DENN), a novel architecture for language models (LMs). A DENNLM projects the input word history vector onto multiple diverse low-dimensional sub-spaces instead of a single higher-dimensional sub-space as in conventional feed-forward neural network LMs. We encourage these sub-spaces to be diverse during network training through an augmented loss function. Our language modeling experiments on the Penn Treebank data set show the performance benefit of using a DENNLM.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516147", "vector": [], "sparse_vector": [], "title": "Multiple Object Recognition with Visual Attention.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an attention-based model for recognizing multiple objects in images. The proposed model is a deep recurrent neural network trained with reinforcement learning to attend to the most relevant regions of the input image. We show that the model learns to both localize and recognize multiple objects despite being given only class labels during training. We evaluate the model on the challenging task of transcribing house number sequences from Google Street View images and show that it is both more accurate than the state-of-the-art convolutional networks and uses fewer parameters and less computation.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516149", "vector": [], "sparse_vector": [], "title": "Hot Swapping for Online Adaptation of Optimization Hyperparameters.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe a general framework for online adaptation of optimization hyperparameters by `hot swapping' their values during learning. We investigate this approach in the context of adaptive learning rate selection using an explore-exploit strategy from the multi-armed bandit literature. Experiments on a benchmark neural network show that the hot swapping approach leads to consistently better solutions compared to well-known alternatives such as AdaDelta and stochastic gradient with exhaustive hyperparameter search.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516151", "vector": [], "sparse_vector": [], "title": "Neural Machine Translation by Jointly Learning to Align and Translate.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural machine translation is a recently proposed approach to machine translation. Unlike the traditional statistical machine translation, the neural machine translation aims at building a single neural network that can be jointly tuned to maximize the translation performance. The models proposed recently for neural machine translation often belong to a family of encoder-decoders and consists of an encoder that encodes a source sentence into a fixed-length vector from which a decoder generates a translation. In this paper, we conjecture that the use of a fixed-length vector is a bottleneck in improving the performance of this basic encoder-decoder architecture, and propose to extend this by allowing a model to automatically (soft-)search for parts of a source sentence that are relevant to predicting a target word, without having to form these parts as a hard segment explicitly. With this new approach, we achieve a translation performance comparable to the existing state-of-the-art phrase-based system on the task of English-to-French translation. Furthermore, qualitative analysis reveals that the (soft-)alignments found by the model agree well with our intuition.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516167", "vector": [], "sparse_vector": [], "title": "Reweighted <PERSON>-<PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Training deep directed graphical models with many hidden variables and performing inference remains a major challenge. Helmholtz machines and deep belief networks are such models, and the wake-sleep algorithm has been proposed to train them. The wake-sleep algorithm relies on training not just the directed generative model but also a conditional generative model (the inference network) that runs backward from visible to latent, estimating the posterior distribution of latent given visible. We propose a novel interpretation of the wake-sleep algorithm which suggests that better estimators of the gradient can be obtained by sampling latent variables multiple times from the inference network. This view is based on importance sampling as an estimator of the likelihood, with the approximate inference network as a proposal distribution. This interpretation is confirmed experimentally, showing that better likelihood can be achieved with this reweighted wake-sleep procedure. Based on this interpretation, we propose that a sigmoidal belief network is not sufficiently powerful for the layers of the inference network in order to recover a good estimator of the posterior distribution of latent variables. Our experiments show that using a more powerful layer model, such as NADE, yields substantially better generative models.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516186", "vector": [], "sparse_vector": [], "title": "Semantic Image Segmentation with Deep Convolutional Nets and Fully Connected CRFs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Convolutional Neural Networks (DCNNs) have recently shown state of the art performance in high level vision tasks, such as image classification and object detection. This work brings together methods from DCNNs and probabilistic graphical models for addressing the task of pixel-level classification (also called \"semantic image segmentation\"). We show that responses at the final layer of DCNNs are not sufficiently localized for accurate object segmentation. This is due to the very invariance properties that make DCNNs good for high level tasks. We overcome this poor localization property of deep networks by combining the responses at the final DCNN layer with a fully connected Conditional Random Field (CRF). Qualitatively, our \"DeepLab\" system is able to localize segment boundaries at a level of accuracy which is beyond previous methods. Quantitatively, our method sets the new state-of-art at the PASCAL VOC-2012 semantic image segmentation task, reaching 71.6% IOU accuracy in the test set. We show how these results can be obtained efficiently: Careful network re-purposing and a novel application of the 'hole' algorithm from the wavelet community allow dense computation of neural net responses at 8 frames per second on a modern GPU.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516187", "vector": [], "sparse_vector": [], "title": "Restricted Boltzmann Machine for Classification with Hierarchical Correlated Prior.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Restricted Boltzmann machines (RBM) and its variants have become hot research topics recently, and widely applied to many classification problems, such as character recognition and document categorization. Often, classification RBM ignores the interclass relationship or prior knowledge of sharing information among classes. In this paper, we are interested in RBM with the hierarchical prior over classes. We assume parameters for nearby nodes are correlated in the hierarchical tree, and further the parameters at each node of the tree be orthogonal to those at its ancestors. We propose a hierarchical correlated RBM for classification problem, which generalizes the classification RBM with sharing information among different classes. In order to reduce the redundancy between node parameters in the hierarchy, we also introduce orthogonal restrictions to our objective function. We test our method on challenge datasets, and show promising results compared to competitive baselines.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516188", "vector": [], "sparse_vector": [], "title": "Learning Deep Structured Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many problems in real-world applications involve predicting several random variables which are statistically related. Markov random fields (MRFs) are a great mathematical tool to encode such relationships. The goal of this paper is to combine MRFs with deep learning algorithms to estimate complex representations while taking into account the dependencies between the output random variables. Towards this goal, we propose a training algorithm that is able to learn structured models jointly with deep features that form the MRF potentials. Our approach is efficient as it blends learning and inference and makes use of GPU acceleration. We demonstrate the effectiveness of our algorithm in the tasks of predicting words from noisy images, as well as multi-class classification of Flickr photographs. We show that joint learning of the deep features and the MRF parameters results in significant performance gains.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516190", "vector": [], "sparse_vector": [], "title": "Discovering Hidden Factors of Variation in Deep Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning has enjoyed a great deal of success because of its ability to learn useful features for tasks such as classification. But there has been less exploration in learning the factors of variation apart from the classification signal. By augmenting autoencoders with simple regularization terms during training, we demonstrate that standard deep architectures can discover and explicitly represent factors of variation beyond those relevant for categorization. We introduce a cross-covariance penalty (XCov) as a method to disentangle factors like handwriting style for digits and subject identity in faces. We demonstrate this on the MNIST handwritten digit database, the Toronto Faces Database (TFD) and the Multi-PIE dataset by generating manipulated instances of the data. Furthermore, we demonstrate these deep networks can extrapolate `hidden' variation in the supervised signal.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516195", "vector": [], "sparse_vector": [], "title": "Transformation Properties of Learned Visual Representations.", "authors": ["Taco S. Cohen", "<PERSON>"], "summary": "When a three-dimensional object moves relative to an observer, a change occurs on the observer's image plane and in the visual representation computed by a learned model. Starting with the idea that a good visual representation is one that transforms linearly under scene motions, we show, using the theory of group representations, that any such representation is equivalent to a combination of the elementary irreducible representations. We derive a striking relationship between irreducibility and the statistical dependency structure of the representation, by showing that under restricted conditions, irreducible representations are decorrelated. Under partial observability, as induced by the perspective projection of a scene onto the image plane, the motion group does not have a linear action on the space of images, so that it becomes necessary to perform inference over a latent representation that does transform linearly. This idea is demonstrated in a model of rotating NORB objects that employs a latent representation of the non-commutative 3D rotation group SO(3).", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516196", "vector": [], "sparse_vector": [], "title": "Representation Learning for cold-start recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A standard approach to Collaborative Filtering (CF), i.e. prediction of user ratings on items, relies on Matrix Factorization techniques. Representations for both users and items are computed from the observed ratings and used for prediction. Unfortunatly, these transductive approaches cannot handle the case of new users arriving in the system, with no known rating, a problem known as user cold-start. A common approach in this context is to ask these incoming users for a few initialization ratings. This paper presents a model to tackle this twofold problem of (i) finding good questions to ask, (ii) building efficient representations from this small amount of information. The model can also be used in a more standard (warm) context. Our approach is evaluated on the classical CF problem and on the cold-start problem on four different datasets showing its ability to improve baseline performance in both cases.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516197", "vector": [], "sparse_vector": [], "title": "Low precision arithmetic for deep learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multipliers are the most space and power-hungry arithmetic operators of the digital implementation of deep neural networks. We train a set of state-of-the-art neural networks (Maxout networks) on three benchmark datasets: MNIST, CIFAR-10 and SVHN. They are trained with three distinct formats: floating point, fixed point and dynamic fixed point. For each of those datasets and for each of those formats, we assess the impact of the precision of the multiplications on the final error after training. We find that very low precision is sufficient not just for running trained networks but also for training them. For example, it is possible to train Maxout networks with 10 bits multiplications.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516203", "vector": [], "sparse_vector": [], "title": "Generative Modeling of Convolutional Neural Networks.", "authors": ["Jifeng Dai", "<PERSON>"], "summary": "The convolutional neural networks (CNNs) have proven to be a powerful tool for discriminative learning. Recently researchers have also started to show interest in the generative aspects of CNNs in order to gain a deeper understanding of what they have learned and how to further improve them. This paper investigates generative modeling of CNNs. The main contributions include: (1) We construct a generative model for the CNN in the form of exponential tilting of a reference distribution. (2) We propose a generative gradient for pre-training CNNs by a non-parametric importance sampling scheme, which is fundamentally different from the commonly used discriminative gradient, and yet has the same computational architecture and cost as the latter. (3) We propose a generative visualization method for the CNNs by sampling from an explicit parametric image distribution. The proposed visualization method can directly draw synthetic samples for any given node in a trained CNN by the Hamiltonian Monte Carlo (HMC) algorithm, without resorting to any extra hold-out images. Experiments on the challenging ImageNet benchmark show that the proposed generative gradient pre-training consistently helps improve the performances of CNNs, and the proposed generative visualization method generates meaningful and varied samples of synthetic images from a large-scale deep CNN.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516209", "vector": [], "sparse_vector": [], "title": "Theano-based Large-Scale Visual Recognition with Multiple GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this report, we describe a Theano-based AlexNet (<PERSON><PERSON><PERSON><PERSON> et al., 2012) implementation and its naive data parallelism on multiple GPUs. Our performance on 2 GPUs is comparable with the state-of-art Caffe library (<PERSON><PERSON> et al., 2014) run on 1 GPU. To the best of our knowledge, this is the first open-source Python-based AlexNet implementation to-date.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516210", "vector": [], "sparse_vector": [], "title": "NICE: Non-linear Independent Components Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a deep learning framework for modeling complex high-dimensional densities called Non-linear Independent Component Estimation (NICE). It is based on the idea that a good representation is one in which the data has a distribution that is easy to model. For this purpose, a non-linear deterministic transformation of the data is learned that maps it to a latent space so as to make the transformed data conform to a factorized distribution, i.e., resulting in independent latent variables. We parametrize this transformation so that computing the Jacobian determinant and inverse transform is trivial, yet we maintain the ability to learn complex non-linear transformations, via a composition of simple building blocks, each based on a deep neural network. The training criterion is simply the exact log-likelihood, which is tractable. Unbiased ancestral sampling is also easy. We show that this approach yields good generative models on four image datasets and can be used for inpainting.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516211", "vector": [], "sparse_vector": [], "title": "Improving zero-shot learning by mitigating the hubness problem.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The zero-shot paradigm exploits vector-based word representations extracted from text corpora with unsupervised methods to learn general mapping functions from other feature spaces onto word space, where the words associated to the nearest neighbours of the mapped vectors are used as their linguistic labels. We show that the neighbourhoods of the mapped elements are strongly polluted by hubs, vectors that tend to be near a high proportion of items, pushing their correct labels down the neighbour list. After illustrating the problem empirically, we propose a simple method to correct it by taking the proximity distribution of potential neighbours across many mapped vectors into account. We show that this correction leads to consistent improvements in realistic zero-shot experiments in the cross-lingual, image labeling and image retrieval domains.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516220", "vector": [], "sparse_vector": [], "title": "Variational Recurrent Auto-Encoders.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we propose a model that combines the strengths of RNNs and SGVB: the Variational Recurrent Auto-Encoder (VRAE). Such a model can be used for efficient, large scale unsupervised learning on time series data, mapping the time series data to a latent vector representation. The model is generative, such that data can be generated from samples of the latent space. An important contribution of this work is that the model can make use of unlabeled data in order to facilitate supervised training of RNNs by initialising the weights and network state.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516224", "vector": [], "sparse_vector": [], "title": "Learning Compact Convolutional Neural Networks with Nested Dropout.", "authors": ["Chelsea Finn", "<PERSON>", "<PERSON>"], "summary": "Recently, nested dropout was proposed as a method for ordering representation units in autoencoders by their information content, without diminishing reconstruction cost. However, it has only been applied to training fully-connected autoencoders in an unsupervised setting. We explore the impact of nested dropout on the convolutional layers in a CNN trained by backpropagation, investigating whether nested dropout can provide a simple and systematic way to determine the optimal representation size with respect to the desired accuracy and desired task and data complexity.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516230", "vector": [], "sparse_vector": [], "title": "Incorporating Both Distributional and Relational Semantics in Word Representations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We investigate the hypothesis that word representations ought to incorporate both distributional and relational semantics. To this end, we employ the Alternating Direction Method of Multipliers (ADMM), which flexibly optimizes a distributional objective on raw text and a relational objective on WordNet. Preliminary results on knowledge base completion, analogy tests, and parsing show that word representations trained on both objectives can give improvements in some cases.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516236", "vector": [], "sparse_vector": [], "title": "Scheduled denoising autoencoders.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a representation learning method that learns features at multiple different levels of scale. Working within the unsupervised framework of denoising autoencoders, we observe that when the input is heavily corrupted during training, the network tends to learn coarse-grained features, whereas when the input is only slightly corrupted, the network tends to learn fine-grained features. This motivates the scheduled denoising autoencoder, which starts with a high level of noise that lowers as training progresses. We find that the resulting representation yields a significant boost on a later supervised task compared to the original input, or to a standard denoising autoencoder trained at a single noise level. After supervised fine-tuning our best model achieves the lowest ever reported error on the CIFAR-10 data set among permutation-invariant methods.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516241", "vector": [], "sparse_vector": [], "title": "On the Stability of Deep Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we study the properties of deep neural networks (DNN) with random weights. We formally prove that these networks perform a distance-preserving embedding of the data. Based on this we then draw conclusions on the size of the training data and the networks' structure. A longer version of this paper with more results and details can be found in (<PERSON><PERSON><PERSON> et al., 2015). In particular, we formally prove in the longer version that DNN with random Gaussian weights perform a distance-preserving embedding of the data, with a special treatment for in-class and out-of-class data.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516243", "vector": [], "sparse_vector": [], "title": "Compact Part-Based Image Representations: Extremal Competition and Overgeneralization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Learning compact and interpretable representations is a very natural task, which has not been solved satisfactorily even for simple binary datasets. In this paper, we review various ways of composing experts for binary data and argue that competitive forms of interaction are best suited to learn low-dimensional representations. We propose a new composition rule that discourages experts from focusing on similar structures and that penalizes opposing votes strongly so that abstaining from voting becomes more attractive. We also introduce a novel sequential initialization procedure, which is based on a process of oversimplification and correction. Experiments show that with our approach very intuitive models can be learned.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516245", "vector": [], "sparse_vector": [], "title": "On distinguishability criteria for estimating generative models", "authors": ["<PERSON>"], "summary": "Two recently introduced criteria for estimation of generative models are both based on a reduction to binary classification. Noise-contrastive estimation (NCE) is an estimation procedure in which a generative model is trained to be able to distinguish data samples from noise samples. Generative adversarial networks (GANs) are pairs of generator and discriminator networks, with the generator network learning to generate samples by attempting to fool the discriminator network into believing its samples are real data. Both estimation procedures use the same function to drive learning, which naturally raises questions about how they are related to each other, as well as whether this function is related to maximum likelihood estimation (MLE). NCE corresponds to training an internal data model belonging to the {\\em discriminator} network but using a fixed generator network. We show that a variant of NCE, with a dynamic generator network, is equivalent to maximum likelihood estimation. Since pairing a learned discriminator with an appropriate dynamically selected generator recovers MLE, one might expect the reverse to hold for pairing a learned generator with a certain discriminator. However, we show that recovering MLE for a learned generator requires departing from the distinguishability game. Specifically:(i) The expected gradient of the NCE discriminator can be made to match the expected gradient ofMLE, if one is allowed to use a non-stationary noise distribution for NCE,(ii) No choice of discriminator network can make the expected gradient for the GAN generator match that of MLE, and(iii) The existing theory does not guarantee that GANs will converge in the non-convex case.This suggests that the key next step in GAN research is to determine whether GANs converge, and if not, to modify their training algorithm to force convergence.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516246", "vector": [], "sparse_vector": [], "title": "Explaining and Harnessing Adversarial Examples.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Several machine learning models, including neural networks, consistently misclassify adversarial examples---inputs formed by applying small but intentionally worst-case perturbations to examples from the dataset, such that the perturbed input results in the model outputting an incorrect answer with high confidence. Early attempts at explaining this phenomenon focused on nonlinearity and overfitting. We argue instead that the primary cause of neural networks' vulnerability to adversarial perturbation is their linear nature. This explanation is supported by new quantitative results while giving the first explanation of the most intriguing fact about them: their generalization across architectures and training sets. Moreover, this view yields a simple and fast method of generating adversarial examples. Using this approach to provide examples for adversarial training, we reduce the test set error of a maxout network on the MNIST dataset.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516247", "vector": [], "sparse_vector": [], "title": "Qualitatively characterizing neural network optimization problems.", "authors": ["<PERSON>", "Oriol Vinyals"], "summary": "Training neural networks involves solving large-scale non-convex optimization problems. This task has long been believed to be extremely difficult, with fear of local minima and other obstacles motivating a variety of schemes to improve optimization, such as unsupervised pretraining. However, modern neural networks are able to achieve negligible training error on complex tasks, using only direct training with stochastic gradient descent. We introduce a simple analysis technique to look for evidence that such networks are overcoming local optima. We find that, in fact, on a straight path from initialization to solution, a variety of state of the art neural networks never encounter any significant obstacles.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516248", "vector": [], "sparse_vector": [], "title": "Unsupervised Feature Learning from Temporal Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Current state-of-the-art classification and detection algorithms rely on supervised training. In this work we study unsupervised feature learning in the context of temporally coherent video data. We focus on feature learning from unlabeled video data, using the assumption that adjacent video frames contain semantically similar information. This assumption is exploited to train a convolutional pooling auto-encoder regularized by slowness and sparsity. We establish a connection between slow feature learning to metric learning and show that the trained encoder can be used to define a more temporally and semantically coherent metric.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516250", "vector": [], "sparse_vector": [], "title": "Towards Deep Neural Network Architectures Robust to Adversarial Examples.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent work has shown deep neural networks (DNNs) to be highly susceptible to well-designed, small perturbations at the input layer, or so-called adversarial examples. Taking images as an example, such distortions are often imperceptible, but can result in 100% mis-classification for a state of the art DNN. We study the structure of adversarial examples and explore network topology, pre-processing and training strategies to improve the robustness of DNNs. We perform various experiments to assess the removability of adversarial examples by corrupting with additional noise and pre-processing with denoising autoencoders (DAEs). We find that DAEs can remove substantial amounts of the adversarial noise. How- ever, when stacking the DAE with the original DNN, the resulting network can again be attacked by new adversarial examples with even smaller distortion. As a solution, we propose Deep Contractive Network, a model with a new end-to-end training procedure that includes a smoothness penalty inspired by the contractive autoencoder (CAE). This increases the network robustness to adversarial examples, without a significant performance penalty.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516262", "vector": [], "sparse_vector": [], "title": "Classifier with Hierarchical Topographical Maps as Internal Representation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this study we want to connect our previously proposed context-relevant topographical maps with the deep learning community. Our architecture is a classifier with hidden layers that are hierarchical two-dimensional topographical maps. These maps differ from the conventional self-organizing maps in that their organizations are influenced by the context of the data labels in a top-down manner. In this way bottom-up and top-down learning are combined in a biologically relevant representational learning setting. Compared to our previous work, we are here specifically elaborating the model in a more challenging setting compared to our previous experiments and to advance more hidden representation layers to bring our discussions into the context of deep representational learning.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516266", "vector": [], "sparse_vector": [], "title": "The local low-dimensionality of natural images.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a new statistical model for photographic images, in which the local responses of a bank of linear filters are described as jointly Gaussian, with zero mean and a covariance that varies slowly over spatial position. We optimize sets of filters so as to minimize the nuclear norms of matrices of their local activations (i.e., the sum of the singular values), thus encouraging a flexible form of sparsity that is not tied to any particular dictionary or coordinate system. Filters optimized according to this objective are oriented and bandpass, and their responses exhibit substantial local correlation. We show that images can be reconstructed nearly perfectly from estimates of the local filter response covariances alone, and with minimal degradation (either visual or MSE) from low-rank approximations of these covariances. As such, this representation holds much promise for use in applications such as denoising, compression, and texture representation, and may form a useful substrate for hierarchical decompositions.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516269", "vector": [], "sparse_vector": [], "title": "Embedding Word Similarity with Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural language models learn word representations, or embeddings, that capture rich linguistic and conceptual information. Here we investigate the embeddings learned by neural machine translation models, a recently-developed class of neural language model. We show that embeddings from translation models outperform those learned by monolingual models at tasks that require knowledge of both conceptual similarity and lexical-syntactic role. We further show that these effects hold when translating from both English to French and English to German, and argue that the desirable properties of translation embeddings should emerge largely independently of the source and target languages. Finally, we apply a new method for training neural translation models with very large vocabularies, and show that this vocabulary expansion algorithm results in minimal degradation of embedding quality. Our embedding spaces can be queried in an online demo and downloaded from our web page. Overall, our analyses indicate that translation-based embeddings should be used in applications that require concepts to be organised according to similarity and/or lexical function, while monolingual embeddings are better suited to modelling (nonspecific) inter-word relatedness.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516272", "vector": [], "sparse_vector": [], "title": "Deep metric learning using Triplet network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning has proven itself as a successful set of models for learning useful semantic representations of data. These, however, are mostly implicitly learned as part of a classification task. In this paper we propose the triplet network model, which aims to learn useful representations by distance comparisons. A similar model was defined by <PERSON> et al. (2014), tailor made for learning a ranking for image information retrieval. Here we demonstrate using various datasets that our model learns a better representation than that of its immediate competitor, the Siamese network. We also discuss future possible usage as a framework for unsupervised learning.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516280", "vector": [], "sparse_vector": [], "title": "Pixel-wise Deep Learning for Contour Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We address the problem of contour detection via per-pixel classifications of edge point. To facilitate the process, the proposed approach leverages with DenseNet, an efficient implementation of multiscale convolutional neural networks (CNNs), to extract an informative feature vector for each pixel and uses an SVM classifier to accomplish contour detection. In the experiment of contour detection, we look into the effectiveness of combining per-pixel features from different CNN layers and verify their performance on BSDS500.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516281", "vector": [], "sparse_vector": [], "title": "Understanding Minimum Probability Flow for RBMs Under Various Kinds of Dynamics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Energy-based models are popular in machine learning due to the elegance of their formulation and their relationship to statistical physics. Among these, the Restricted Boltzmann Machine (RBM), and its staple training algorithm contrastive divergence (CD), have been the prototype for some recent advancements in the unsupervised training of deep neural networks. However, CD has limited theoretical motivation, and can in some cases produce undesirable behavior. Here, we investigate the performance of Minimum Probability Flow (MPF) learning for training RBMs. Unlike CD, with its focus on approximating an intractable partition function via Gibbs sampling, MPF proposes a tractable, consistent, objective function defined in terms of a Taylor expansion of the KL divergence with respect to sampling dynamics. Here we propose a more general form for the sampling dynamics in MPF, and explore the consequences of different choices for these dynamics for training RBMs. Experimental results show MPF outperforming CD for various RBM configurations.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516282", "vector": [], "sparse_vector": [], "title": "Modeling Compositionality with Multiplicative Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present the multiplicative recurrent neural network as a general model for compositional meaning in language, and evaluate it on the task of fine-grained sentiment analysis. We establish a connection to the previously investigated matrix-space models for compositionality, and show they are special cases of the multiplicative recurrent net. Our experiments show that these models perform comparably or better than Elman-type additive recurrent neural networks and outperform matrix-space models on a standard fine-grained sentiment analysis corpus. Furthermore, they yield comparable results to structural deep models on the recently published Stanford Sentiment Treebank without the need for generating parse trees.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516284", "vector": [], "sparse_vector": [], "title": "Deep Structured Output Learning for Unconstrained Text Recognition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a representation suitable for the unconstrained recognition of words in natural images: the general case of no fixed lexicon and unknown length.To this end we propose a convolutional neural network (CNN) based architecture which incorporates a Conditional Random Field (CRF) graphical model, taking the whole word image as a single input. The unaries of the CRF are provided by a CNN that predicts characters at each position of the output, while higher order terms are provided by another CNN that detects the presence of N-grams. We show that this entire model (CRF, character predictor, N-gram predictor) can be jointly optimised by back-propagating the structured output loss, essentially requiring the system to perform multi-task learning, and training uses purely synthetically generated data. The resulting model is a more accurate system on standard real-world text recognition benchmarks than character prediction alone, setting a benchmark for systems that have not been trained on a particular lexicon. In addition, our model achieves state-of-the-art accuracy in lexicon-constrained scenarios, without being specifically modelled for constrained recognition. To test the generalisation of our model, we also perform experiments with random alpha-numeric strings to evaluate the method when no visual language model is applicable.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516287", "vector": [], "sparse_vector": [], "title": "Score Function Features for Discriminative Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Feature learning forms the cornerstone for tackling challenging learning problems in domains such as speech, computer vision and natural language processing. In this paper, we consider a novel class of matrix and tensor-valued features, which can be pre-trained using unlabeled samples. We present efficient algorithms for extracting discriminative information, given these pre-trained features and labeled samples for any related task. Our class of features are based on higher-order score functions, which capture local variations in the probability density function of the input. We establish a theoretical framework to characterize the nature of discriminative information that can be extracted from score-function features, when used in conjunction with labeled samples. We employ efficient spectral decomposition algorithms (on matrices and tensors) for extracting discriminative components. The advantage of employing tensor-valued features is that we can extract richer discriminative information in the form of an overcomplete representations. Thus, we present a novel framework for employing generative models of the input for discriminative learning.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516288", "vector": [], "sparse_vector": [], "title": "Entity-Augmented Distributional Semantics for Discourse Relations.", "authors": ["Yangfeng Ji", "<PERSON>"], "summary": "Discourse relations bind smaller linguistic elements into coherent texts. However, automatically identifying discourse relations is difficult, because it requires understanding the semantics of the linked sentences. A more subtle challenge is that it is not enough to represent the meaning of each sentence of a discourse relation, because the relation may depend on links between lower-level elements, such as entity mentions. Our solution computes distributional meaning representations by composition up the syntactic parse tree. A key difference from previous work on compositional distributional semantics is that we also compute representations for entity mentions, using a novel downward compositional pass. Discourse relations are predicted not only from the distributional representations of the sentences, but also of their coreferent entity mentions. The resulting system obtains substantial improvements over the previous state-of-the-art in predicting implicit discourse relations in the Penn Discourse Treebank.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516289", "vector": [], "sparse_vector": [], "title": "Flattened Convolutional Neural Networks for Feedforward Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "Aysegul Dundar", "<PERSON><PERSON><PERSON>"], "summary": "We present flattened convolutional neural networks that are designed for fast feedforward execution. The redundancy of the parameters, especially weights of the convolutional filters in convolutional neural networks has been extensively studied and different heuristics have been proposed to construct a low rank basis of the filters after training. In this work, we train flattened networks that consist of consecutive sequence of one-dimensional filters across all directions in 3D space to obtain comparable performance as conventional convolutional networks. We tested flattened model on different datasets and found that the flattened layer can effectively substitute for the 3D filters without loss of accuracy. The flattened convolution pipelines provide around two times speed-up during feedforward pass compared to the baseline model due to the significant reduction of learning parameters. Furthermore, the proposed method does not require efforts in manual tuning or post processing once the model is trained.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516294", "vector": [], "sparse_vector": [], "title": "Gradual Training Method for Denoising Auto Encoders.", "authors": ["<PERSON>", "Gal Chechik"], "summary": "Stacked denoising auto encoders (DAEs) are well known to learn useful deep representations, which can be used to improve supervised training by initializing a deep network. We investigate a training scheme of a deep DAE, where DAE layers are gradually added and keep adapting as additional layers are added. We show that in the regime of mid-sized datasets, this gradual training provides a small but consistent improvement over stacked training in both reconstruction quality and classification error over stacked training on MNIST and CIFAR datasets.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516297", "vector": [], "sparse_vector": [], "title": "Permutohedral Lattice CNNs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a convolutional layer that is able to process sparse input features. As an example, for image recognition problems this allows an efficient filtering of signals that do not lie on a dense grid (like pixel position), but of more general features (such as color values). The presented algorithm makes use of the permutohedral lattice data structure. The permutohedral lattice was introduced to efficiently implement a bilateral filter, a commonly used image processing operation. Its use allows for a generalization of the convolution type found in current (spatial) convolutional network architectures.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516298", "vector": [], "sparse_vector": [], "title": "Adam: A Method for Stochastic Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce <PERSON>, an algorithm for first-order gradient-based optimization of stochastic objective functions, based on adaptive estimates of lower-order moments. The method is straightforward to implement, is computationally efficient, has little memory requirements, is invariant to diagonal rescaling of the gradients, and is well suited for problems that are large in terms of data and/or parameters. The method is also appropriate for non-stationary objectives and problems with very noisy and/or sparse gradients. The hyper-parameters have intuitive interpretations and typically require little tuning. Some connections to related algorithms, on which <PERSON> was inspired, are discussed. We also analyze the theoretical convergence properties of the algorithm and provide a regret bound on the convergence rate that is comparable to the best known results under the online convex optimization framework. Empirical results demonstrate that <PERSON> works well in practice and compares favorably to other stochastic optimization methods. Finally, we discuss AdaMax, a variant of <PERSON> based on the infinity norm.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516307", "vector": [], "sparse_vector": [], "title": "Deep Gaze I: Boosting Saliency Prediction with Feature Maps Trained on ImageNet.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent results suggest that state-of-the-art saliency models perform far from optimal in predicting fixations. This lack in performance has been attributed to an inability to model the influence of high-level image features such as objects. Recent seminal advances in applying deep neural networks to tasks like object recognition suggests that they are able to capture this kind of structure. However, the enormous amount of training data necessary to train these networks makes them difficult to apply directly to saliency prediction. We present a novel way of reusing existing neural networks that have been pretrained on the task of object recognition in models of fixation prediction. Using the well-known network of <PERSON><PERSON> et al. (2012), we come up with a new saliency model that significantly outperforms all state-of-the-art models on the MIT Saliency Benchmark. We show that the structure of this network allows new insights in the psychophysics of fixation selection and potentially their neural implementation. To train our network, we build on recent work on the modeling of saliency as point processes.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516313", "vector": [], "sparse_vector": [], "title": "Speeding-up Convolutional Neural Networks Using Fine-tuned CP-Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a simple two-step approach for speeding up convolution layers within large convolutional neural networks based on tensor decomposition and discriminative fine-tuning. Given a layer, we use non-linear least squares to compute a low-rank CP-decomposition of the 4D convolution kernel tensor into a sum of a small number of rank-one tensors. At the second step, this decomposition is used to replace the original convolutional layer with a sequence of four convolutional layers with small kernels. After such replacement, the entire network is fine-tuned on the training data using standard backpropagation process.We evaluate this approach on two CNNs and show that it is competitive with previous approaches, leading to higher obtained CPU speedups at the cost of lower accuracy drops for the smaller of the two networks. Thus, for the 36-class character classification CNN, our approach obtains a 8.5x CPU speedup of the whole network with only minor accuracy drop (1% from 91% to 90%). For the standard ImageNet architecture (AlexNet), the approach speeds up the second convolution layer by a factor of 4x at the cost of $1\\%$ increase of the overall top-5 classification error.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516314", "vector": [], "sparse_vector": [], "title": "N-gram-Based Low-Dimensional Representation for Document Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The bag-of-words (BOW) model is the common approach for classifying documents, where words are used as feature for training a classifier. This generally involves a huge number of features. Some techniques, such as Latent Semantic Analysis (LSA) or Latent Dirichlet Allocation (LDA), have been designed to summarize documents in a lower dimension with the least semantic information loss. Some semantic information is nevertheless always lost, since only words are considered. Instead, we aim at using information coming from n-grams to overcome this limitation, while remaining in a low-dimension space. Many approaches, such as the Skip-gram model, provide good word vector representations very quickly. We propose to average these representations to obtain representations of n-grams. All n-grams are thus embedded in a same semantic space. A K-means clustering can then group them into semantic concepts. The number of features is therefore dramatically reduced and documents can be represented as bag of semantic concepts. We show that this model outperforms LSA and LDA on a sentiment classification task, and yields similar results than a traditional BOW-model with far less features.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516315", "vector": [], "sparse_vector": [], "title": "Simple Image Description Generator via a Linear Phrase-Based Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Generating a novel textual description of an image is an interesting problem that connects computer vision and natural language processing. In this paper, we present a simple model that is able to generate descriptive sentences given a sample image. This model has a strong focus on the syntax of the descriptions. We train a purely bilinear model that learns a metric between an image representation (generated from a previously trained Convolutional Neural Network) and phrases that are used to described them. The system is then able to infer phrases from a given image sample. Based on caption syntax statistics, we propose a simple language model that can produce relevant descriptions for a given test image using the phrases inferred. Our approach, which is considerably simpler than state-of-the-art models, achieves comparable results on the recently release Microsoft COCO dataset.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516317", "vector": [], "sparse_vector": [], "title": "Target Propagation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Back-propagation has been the workhorse of recent successes of deep learning but it relies on infinitesimal effects (partial derivatives) in order to perform credit assignment. This could become a serious issue as one considers deeper and more non-linear functions, e.g., consider the extreme case of nonlinearity where the relation between parameters and cost is actually discrete. Inspired by the biological implausibility of back-propagation, a few approaches have been proposed in the past that could play a similar credit assignment role. In this spirit, we explore a novel approach to credit assignment in deep networks that we call target propagation. The main idea is to compute targets rather than gradients, at each layer. Like gradients, they are propagated backwards. In a way that is related but different from previously proposed proxies for back-propagation which rely on a backwards network with symmetric weights, target propagation relies on auto-encoders at each layer. Unlike back-propagation, it can be applied even when units exchange stochastic bits rather than real numbers. We show that a linear correction for the imperfectness of the auto-encoders, called difference target propagation, is very effective to make target propagation actually work, leading to results comparable to back-propagation for deep networks with discrete and continuous units and denoising auto-encoders and achieving state of the art for stochastic networks.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516318", "vector": [], "sparse_vector": [], "title": "Joint RNN-Based Greedy Parsing and Word Composition.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces a greedy parser based on neural networks, which leverages a new compositional sub-tree representation. The greedy parser and the compositional procedure are jointly trained, and tightly depends on each-other. The composition procedure outputs a vector representation which summarizes syntactically (parsing tags) and semantically (words) sub-trees. Composition and tagging is achieved over continuous (word or tag) representations, and recurrent neural networks. We reach F1 performance on par with well-known existing parsers, while having the advantage of speed, thanks to the greedy nature of the parser. We provide a fully functional implementation of the method described in this paper.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516323", "vector": [], "sparse_vector": [], "title": "Purine: A bi-graph based deep learning framework.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shuicheng Yan"], "summary": "In this paper, we introduce a novel deep learning framework, termed Purine. In Purine, a deep network is expressed as a bipartite graph (bi-graph), which is composed of interconnected operators and data tensors. With the bi-graph abstraction, networks are easily solvable with event-driven task dispatcher. We then demonstrate that different parallelism schemes over GPUs and/or CPUs on single or multiple PCs can be universally implemented by graph composition. This eases researchers from coding for various parallelization schemes, and the same dispatcher can be used for solving variant graphs. Scheduled by the task dispatcher, memory transfers are fully overlapped with other computations, which greatly reduce the communication overhead and help us achieve approximate linear acceleration.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516331", "vector": [], "sparse_vector": [], "title": "Move Evaluation in Go Using Deep Convolutional Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The game of Go is more challenging than other board games, due to the difficulty of constructing a position or move evaluation function. In this paper we investigate whether deep convolutional networks can be used to directly represent and learn this knowledge. We train a large 12-layer convolutional neural network by supervised learning from a database of human professional games. The network correctly predicts the expert move in 55% of positions, equalling the accuracy of a 6 dan human player. When the trained convolutional network was used directly to play games of Go, without any search, it beat the traditional search program GnuGo in 97% of games, and matched the performance of a state-of-the-art Monte-Carlo tree search that simulates a million positions per move.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516333", "vector": [], "sparse_vector": [], "title": "Tailoring Word Embeddings for Bilexical Predictions: An Experimental Comparison.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the problem of inducing word embeddings that are tailored for a particular bilexical relation. Our learning algorithm takes an existing lexical vector space and compresses it such that the resulting word embeddings are good predictors for a target bilexical relation. In experiments we show that task-specific embeddings can benefit both the quality and efficiency in lexical prediction tasks.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516337", "vector": [], "sparse_vector": [], "title": "Deep Captioning with Multimodal Recurrent Neural Networks (m-RNN).", "authors": ["Junhua Mao", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present a multimodal Recurrent Neural Network (m-RNN) model for generating novel image captions. It directly models the probability distribution of generating a word given previous words and an image. Image captions are generated by sampling from this distribution. The model consists of two sub-networks: a deep recurrent neural network for sentences and a deep convolutional network for images. These two sub-networks interact with each other in a multimodal layer to form the whole m-RNN model. The effectiveness of our model is validated on four benchmark datasets (IAPR TC-12, Flickr 8K, Flickr 30K and MS COCO). Our model outperforms the state-of-the-art methods. In addition, we apply the m-RNN model to retrieval tasks for retrieving images or sentences, and achieves significant performance improvement over the state-of-the-art methods which directly optimize the ranking objective function for retrieval. The project page of this work is:this http URL.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516343", "vector": [], "sparse_vector": [], "title": "Zero-bias autoencoders and the benefits of co-adapting features.", "authors": ["<PERSON>", "<PERSON><PERSON> Reddy Konda", "<PERSON>"], "summary": "Regularized training of an autoencoder typically results in hidden unit biases that take on large negative values. We show that negative biases are a natural result of using a hidden layer whose responsibility is to both represent the input data and act as a selection mechanism that ensures sparsity of the representation. We then show that negative biases impede the learning of data distributions whose intrinsic dimensionality is high. We also propose a new activation function that decouples the two roles of the hidden layer and that allows us to learn representations on data with very high intrinsic dimensionality, where standard autoencoders typically fail. Since the decoupled activation function acts like an implicit regularizer, the model can be trained by minimizing the reconstruction error of training data, without requiring any additional regularization.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516344", "vector": [], "sparse_vector": [], "title": "Ensemble of Generative and Discriminative Techniques for Sentiment Analysis of Movie Reviews.", "authors": ["Grégoire <PERSON>nil", "<PERSON><PERSON>", "Marc&a<PERSON>s;<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sentiment analysis is a common task in natural language processing that aims to detect polarity of a text document (typically a consumer review). In the simplest settings, we discriminate only between positive and negative sentiment, turning the task into a standard binary classification problem. We compare several ma- chine learning approaches to this problem, and combine them to achieve the best possible results. We show how to use for this task the standard generative lan- guage models, which are slightly complementary to the state of the art techniques. We achieve strong results on a well-known dataset of IMDB movie reviews. Our results are easily reproducible, as we publish also the code needed to repeat the experiments. This should simplify further advance of the state of the art, as other researchers can combine their techniques with ours with little effort.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516346", "vector": [], "sparse_vector": [], "title": "Learning Longer Memory in Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marc&a<PERSON>s;<PERSON><PERSON><PERSON>"], "summary": "Recurrent neural network is a powerful model that learns temporal patterns in sequential data. For a long time, it was believed that recurrent networks are difficult to train using simple optimizers, such as stochastic gradient descent, due to the so-called vanishing gradient problem. In this paper, we show that learning longer term patterns in real data, such as in natural language, is perfectly possible using gradient descent. This is achieved by using a slight structural modification of the simple recurrent neural network architecture. We encourage some of the hidden units to change their state slowly by making part of the recurrent weight matrix close to identity, thus forming kind of a longer term memory. We evaluate our model in language modeling experiments, where we obtain similar performance to the much more complex Long Short Term Memory (LSTM) networks (Hochreiter & Schmidhuber, 1997).", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516347", "vector": [], "sparse_vector": [], "title": "Fast Label Embeddings for Extremely Large Output Spaces.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Many modern multiclass and multilabel problems are characterized by increasingly large output spaces. For these problems, label embeddings have been shown to be a useful primitive that can improve computational and statistical efficiency. In this work we utilize a correspondence between rank constrained estimation and low dimensional label embeddings that uncovers a fast label embedding algorithm which works in both the multiclass and multilabel settings. The result is a randomized algorithm for partial least squares, whose running time is exponentially faster than naive algorithms. We demonstrate our techniques on two large-scale public datasets, from the Large Scale Hierarchical Text Challenge and the Open Directory Project, where we obtain state of the art results.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516350", "vector": [], "sparse_vector": [], "title": "Deep Narrow Boltzmann Machines are Universal Approximators.", "authors": ["<PERSON>"], "summary": "We show that deep narrow Boltzmann machines are universal approximators of probability distributions on the activities of their visible units, provided they have sufficiently many hidden layers, each containing the same number of units as the visible layer. We show that, within certain parameter domains, deep Boltzmann machines can be studied as feedforward networks. We provide upper and lower bounds on the sufficient depth and width of universal approximators. These results settle various intuitions regarding undirected networks and, in particular, they show that deep narrow Boltzmann machines are at least as compact universal approximators as narrow sigmoid belief networks and restricted Boltzmann machines, with respect to the currently available bounds for those models.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516355", "vector": [], "sparse_vector": [], "title": "On the Importance of a Hierarchy for Learning Continuous Vector Representations of a Label Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An important problem in multi-label classification is to capture label patterns or underlying structures that have an impact on such patterns. This paper addresses one such problem, namely how to exploit hierarchical structures over labels. We present a novel method to learn vector representations of a label space given a hierarchy of labels and label co-occurrence patterns. Our experimental results demonstrate qualitatively that the proposed method is able to learn regularities among labels by exploiting a label hierarchy as well as label co-occurrences. It highlights the importance of the hierarchical information in order to obtain regularities which facilitate analogical reasoning over a label space. We also experimentally illustrate the dependency of the learned representations on the label hierarchy.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516357", "vector": [], "sparse_vector": [], "title": "In Search of the Real Inductive Bias: On the Role of Implicit Regularization in Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present experiments demonstrating that some other form of capacity control, different from network size, plays a central role in learning multilayer feed-forward networks. We argue, partially through analogy to matrix factorization, that this is an inductive bias that can help shed light on deep learning.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516359", "vector": [], "sparse_vector": [], "title": "Algorithmic Robustness for Semi-Supervised (ε, γ, τ)-Good Metric Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Massih<PERSON><PERSON><PERSON>"], "summary": "The notion of metric plays a key role in machine learning problems such as classification, clustering or ranking. However, it is worth noting that there is a severe lack of theoretical guarantees that can be expected on the generalization capacity of the classifier associated to a given metric. The theoretical framework of $(\\epsilon, \\gamma, \\tau)$-good similarity functions (<PERSON><PERSON><PERSON> et al., 2008) has been one of the first attempts to draw a link between the properties of a similarity function and those of a linear classifier making use of it. In this paper, we extend and complete this theory by providing a new generalization bound for the associated classifier based on the algorithmic robustness framework.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516363", "vector": [], "sparse_vector": [], "title": "An Analysis of Unsupervised Pre-training in Light of Recent Advances.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Convolutional neural networks perform well on object recognition because of a number of recent advances: rectified linear units (ReLUs), data augmentation, dropout, and large labelled datasets. Unsupervised data has been proposed as another way to improve performance. Unfortunately, unsupervised pre-training is not used by state-of-the-art methods leading to the following question: Is unsupervised pre-training still useful given recent advances? If so, when? We answer this in three parts: we 1) develop an unsupervised method that incorporates ReLUs and recent unsupervised regularization techniques, 2) analyze the benefits of unsupervised pre-training compared to data augmentation and dropout on CIFAR-10 while varying the ratio of unsupervised to supervised samples, 3) verify our findings on STL-10. We discover unsupervised pre-training, as expected, helps when the ratio of unsupervised to supervised samples is high, and surprisingly, hurts when the ratio is low. We also use unsupervised pre-training with additional color augmentation to achieve near state-of-the-art performance on STL-10.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516364", "vector": [], "sparse_vector": [], "title": "Learning linearly separable features for speech recognition using convolutional neural networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automatic speech recognition systems usually rely on spectral-based features, such as MFCC of PLP. These features are extracted based on prior knowledge such as, speech perception or/and speech production. Recently, convolutional neural networks have been shown to be able to estimate phoneme conditional probabilities in a completely data-driven manner, i.e. using directly temporal raw speech signal as input. This system was shown to yield similar or better performance than HMM/ANN based system on phoneme recognition task and on large scale continuous speech recognition task, using less parameters. Motivated by these studies, we investigate the use of simple linear classifier in the CNN-based framework. Thus, the network learns linearly separable features from raw speech. We show that such system yields similar or better performance than MLP based system using cepstral-based features as input.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516366", "vector": [], "sparse_vector": [], "title": "Automatic Discovery and Optimization of Parts for Image Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Part-based representations have been shown to be very useful for image classification. Learning part-based models is often viewed as a two-stage problem. First, a collection of informative parts is discovered, using heuristics that promote part distinctiveness and diversity, and then classifiers are trained on the vector of part responses. In this paper we unify the two stages and learn the image classifiers and a set of shared parts jointly. We generate an initial pool of parts by randomly sampling part candidates and selecting a good subset using L1/L2 regularization. All steps are driven \"directly\" by the same objective namely the classification loss on a training set. This lets us do away with engineered heuristics. We also introduce the notion of \"negative parts\", intended as parts that are negatively correlated with one or more classes. Negative parts are complementary to the parts discovered by other methods, which look only for positive correlations.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516367", "vector": [], "sparse_vector": [], "title": "Fully Convolutional Multi-Class Multiple Instance Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multiple instance learning (MIL) can reduce the need for costly annotation in tasks such as semantic segmentation by weakening the required degree of supervision. We propose a novel MIL formulation of multi-class semantic segmentation learning by a fully convolutional network. In this setting, we seek to learn a semantic segmentation model from just weak image-level labels. The model is trained end-to-end to jointly optimize the representation while disambiguating the pixel-image label assignment. Fully convolutional training accepts inputs of any size, does not need object proposal pre-processing, and offers a pixelwise loss map for selecting latent instances. Our multi-class MIL loss exploits the further supervision given by images with multiple labels. We evaluate this approach through preliminary experiments on the PASCAL VOC segmentation challenge.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516368", "vector": [], "sparse_vector": [], "title": "A Group Theoretic Perspective on Unsupervised Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Why does Deep Learning work? What representations does it capture? How do higher-order representations emerge? We study these questions from the perspective of group theory, thereby opening a new approach towards a theory of Deep learning.One factor behind the recent resurgence of the subject is a key algorithmic step called {\\em pretraining}: first search for a good generative model for the input samples, and repeat the process one layer at a time. We show deeper implications of this simple principle, by establishing a connection with the interplay of orbits and stabilizers of group actions. Although the neural networks themselves may not form groups, we show the existence of {\\em shadow} groups whose elements serve as close approximations.Over the shadow groups, the pre-training step, originally introduced as a mechanism to better initialize a network, becomes equivalent to a search for features with minimal orbits. Intuitively, these features are in a way the {\\em simplest}. Which explains why a deep learning network learns simple features first. Next, we show how the same principle, when repeated in the deeper layers, can capture higher order representations, and why representation complexity increases as the layers get deeper.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516371", "vector": [], "sparse_vector": [], "title": "What Do Deep CNNs Learn About Objects?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Baochen Sun", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep convolutional neural networks learn extremely powerful image representations, yet most of that power is hidden in the millions of deep-layer parameters. What exactly do these parameters represent? Recent work has started to analyse CNN representations, finding that, e.g., they are invariant to some 2D transformations <PERSON> et al. (2014), but are confused by particular types of image noise <PERSON><PERSON><PERSON> et al. (2014). In this work, we delve deeper and ask: how invariant are CNNs to object-class variations caused by 3D shape, pose, and photorealism?", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516377", "vector": [], "sparse_vector": [], "title": "Parallel training of Deep Neural Networks with Natural Gradient and Parameter Averaging.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>v <PERSON>hudan<PERSON>"], "summary": "We describe the neural-network training framework used in the Kaldi speech recognition toolkit, which is geared towards training DNNs with large amounts of training data using multiple GPU-equipped or multi-core machines. In order to be as hardware-agnostic as possible, we needed a way to use multiple machines without generating excessive network traffic. Our method is to average the neural network parameters periodically (typically every minute or two), and redistribute the averaged parameters to the machines for further training. Each machine sees different data. By itself, this method does not work very well. However, we have another method, an approximate and efficient implementation of Natural Gradient for Stochastic Gradient Descent (NG-SGD), which seems to allow our periodic-averaging method to work well, as well as substantially improving the convergence of SGD on a single machine.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516378", "vector": [], "sparse_vector": [], "title": "A Generative Model for Deep Convolutional Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A generative model is developed for deep (multi-layered) convolutional dictionary learning. A novel probabilistic pooling operation is integrated into the deep model, yielding efficient bottom-up (pretraining) and top-down (refinement) probabilistic learning. Experimental results demonstrate powerful capabilities of the model to learn multi-layer features from images, and excellent classification results are obtained on the MNIST and Caltech 101 datasets.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516379", "vector": [], "sparse_vector": [], "title": "Random Forests Can Hash.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hash codes are a very efficient data representation needed to be able to cope with the ever growing amounts of data. We introduce a random forest semantic hashing scheme with information-theoretic code aggregation, showing for the first time how random forest, a technique that together with deep learning have shown spectacular results in classification, can also be extended to large-scale retrieval. Traditional random forest fails to enforce the consistency of hashes generated from each tree for the same class data, i.e., to preserve the underlying similarity, and it also lacks a principled way for code aggregation across trees. We start with a simple hashing scheme, where independently trained random trees in a forest are acting as hashing functions. We the propose a subspace model as the splitting function, and show that it enforces the hash consistency in a tree for data from the same class. We also introduce an information-theoretic approach for aggregating codes of individual trees into a single hash code, producing a near-optimal unique hash for each class. Experiments on large-scale public datasets are presented, showing that the proposed approach significantly outperforms state-of-the-art hashing methods for retrieval tasks.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516380", "vector": [], "sparse_vector": [], "title": "Representation using the Weyl Transform.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Weyl transform is introduced as a rich framework for data representation. Transform coefficients are connected to the <PERSON><PERSON> transform of multiscale autocorrelations, and different forms of dyadic periodicity in a signal are shown to appear as different features in its Weyl coefficients. The Weyl transform has a high degree of symmetry with respect to a large group of multiscale transformations, which allows compact yet discriminative representations to be obtained by pooling coefficients. The effectiveness of the <PERSON><PERSON> transform is demonstrated through the example of textured image classification.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516383", "vector": [], "sparse_vector": [], "title": "Techniques for Learning Binary Stochastic Feedforward Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stochastic binary hidden units in a multi-layer perceptron (MLP) network give at least three potential benefits when compared to deterministic MLP networks. (1) They allow to learn one-to-many type of mappings. (2) They can be used in structured prediction problems, where modeling the internal structure of the output is important. (3) Stochasticity has been shown to be an excellent regularizer, which makes generalization performance potentially better in general. However, training stochastic networks is considerably more difficult. We study training using M samples of hidden activations per input. We show that the case M=1 leads to a fundamentally different behavior where the network tries to avoid stochasticity. We propose two new estimators for the training gradient and propose benchmark tests for comparing training algorithms. Our experiments confirm that training stochastic networks is difficult and show that the proposed two estimators perform favorably among all the five known estimators.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516384", "vector": [], "sparse_vector": [], "title": "Denoising autoencoder with modulated lateral connections learns invariant representations of natural images.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Suitable lateral connections between encoder and decoder are shown to allow higher layers of a denoising autoencoder (dAE) to focus on invariant representations. In regular autoencoders, detailed information needs to be carried through the highest layers but lateral connections from encoder to decoder relieve this pressure. It is shown that abstract invariant features can be translated to detailed reconstructions when invariant features are allowed to modulate the strength of the lateral connection. Three dAE structures with modulated and additive lateral connections, and without lateral connections were compared in experiments using real-world images. The experiments verify that adding modulated lateral connections to the model 1) improves the accuracy of the probability model for inputs, as measured by denoising performance; 2) results in representations whose degree of invariance grows faster towards the higher layers; and 3) supports the formation of diverse invariant poolings.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516385", "vector": [], "sparse_vector": [], "title": "Visual Instance Retrieval with Deep Convolutional Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper provides an extensive study on the availability of image representations based on convolutional networks (ConvNets) for the task of visual instance retrieval. Besides the choice of convolutional layers, we present an efficient pipeline exploiting multi-scale schemes to extract local features, in particular, by taking geometric invariance into explicit account, i.e. positions, scales and spatial consistency. In our experiments using five standard image retrieval datasets, we demonstrate that generic ConvNet image representations can outperform other state-of-the-art methods if they are extracted appropriately.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516386", "vector": [], "sparse_vector": [], "title": "Training Deep Neural Networks on Noisy Labels with Bootstrapping.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Current state-of-the-art deep learning systems for visual object recognition and detection use purely supervised training with regularization such as dropout to avoid overfitting. The performance depends critically on the amount of labeled examples, and in current practice the labels are assumed to be unambiguous and accurate. However, this assumption often does not hold; e.g. in recognition, class labels may be missing; in detection, objects in the image may not be localized; and in general, the labeling may be subjective. In this work we propose a generic way to handle noisy and incomplete labeling by augmenting the prediction objective with a notion of consistency. We consider a prediction consistent if the same prediction is made given similar percepts, where the notion of similarity is between deep network features computed from the input data. In experiments we demonstrate that our approach yields substantial robustness to label noise on several datasets. On MNIST handwritten digits, we show that our model is robust to label corruption. On the Toronto Face Database, we show that our model handles well the case of subjective labels in emotion recognition, achieving state-of-the- art results, and can also benefit from unlabeled face images with no modification to our method. On the ILSVRC2014 detection challenge data, we show that our approach extends to very deep networks, high resolution images and structured outputs, and results in improved scalable detection.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516390", "vector": [], "sparse_vector": [], "title": "FitNets: Hints for Thin Deep Nets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While depth tends to improve network performances, it also makes gradient-based training more difficult since deeper networks tend to be more non-linear. The recently proposed knowledge distillation approach is aimed at obtaining small and fast-to-execute models, and it has shown that a student network could imitate the soft output of a larger teacher network or ensemble of networks. In this paper, we extend this idea to allow the training of a student that is deeper and thinner than the teacher, using not only the outputs but also the intermediate representations learned by the teacher as hints to improve the training process and final performance of the student. Because the student intermediate hidden layer will generally be smaller than the teacher's intermediate hidden layer, additional parameters are introduced to map the student hidden layer to the prediction of the teacher hidden layer. This allows one to train deeper students that can generalize better or run faster, a trade-off that is controlled by the chosen student capacity. For example, on CIFAR-10, a deep student network with almost 10.4 times less parameters outperforms a larger, state-of-the-art teacher network.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516393", "vector": [], "sparse_vector": [], "title": "Generative Class-conditional Autoencoders.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Recent work by <PERSON><PERSON> et al. (2013) proposes a sampling procedure for denoising autoencoders which involves learning the transition operator of a Markov chain. The transition operator is typically unimodal, which limits its capacity to model complex data. In order to perform efficient sampling from conditional distributions, we extend this work, both theoretically and algorithmically, to gated autoencoders (<PERSON><PERSON><PERSON>, 2013), The proposed model is able to generate convincing class-conditional samples when trained on both the MNIST and TFD datasets.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516395", "vector": [], "sparse_vector": [], "title": "Explorations on high dimensional landscapes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Finding minima of a real valued non-convex function over a high dimensional space is a major challenge in science. We provide evidence that some such functions that are defined on high dimensional domains have a narrow band of values whose pre-image contains the bulk of its critical points. This is in contrast with the low dimensional picture in which this band is wide. Our simulations agree with the previous theoretical work on spin glasses that proves the existence of such a band when the dimension of the domain tends to infinity. Furthermore our experiments on teacher-student networks with the MNIST dataset establish a similar phenomenon in deep networks. We finally observe that both the gradient descent and the stochastic gradient descent methods can reach this level within the same number of steps.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516400", "vector": [], "sparse_vector": [], "title": "Provable Methods for Training Neural Networks with Sparse Connectivity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide novel guaranteed approaches for training feedforward neural networks with sparse connectivity. We leverage on the techniques developed previously for learning linear networks and show that they can also be effectively adopted to learn non-linear networks. We operate on the moments involving label and the score function of the input, and show that their factorization provably yields the weight matrix of the first layer of a deep network under mild conditions. In practice, the output of our method can be employed as effective initializers for gradient descent.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516401", "vector": [], "sparse_vector": [], "title": "Attention for Fine-Grained Categorization.", "authors": ["<PERSON>", "<PERSON>", "Esteban Real"], "summary": "This paper presents experiments extending the work of <PERSON> et al. (2014) on recurrent neural models for attention into less constrained visual environments, specifically fine-grained categorization on the Stanford Dogs data set. In this work we use an RNN of the same structure but substitute a more powerful visual network and perform large-scale pre-training of the visual network outside of the attention RNN. Most work in attention models to date focuses on tasks with toy or more constrained visual environments, whereas we present results for fine-grained categorization better than the state-of-the-art GoogLeNet classification model. We show that our model learns to direct high resolution attention to the most discriminative regions without any spatial supervision such as bounding boxes, and it is able to discriminate fine-grained dog breeds moderately well even when given only an initial low-resolution context image and narrow, inexpensive glimpses at faces and fur patterns. This and similar attention models have the major advantage of being trained end-to-end, as opposed to other current detection and recognition pipelines with hand-engineered components where information is lost. While our model is state-of-the-art, further work is needed to fully leverage the sequential input.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516404", "vector": [], "sparse_vector": [], "title": "Very Deep Convolutional Networks for Large-Scale Image Recognition.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this work we investigate the effect of the convolutional network depth on its accuracy in the large-scale image recognition setting. Our main contribution is a thorough evaluation of networks of increasing depth using an architecture with very small (3x3) convolution filters, which shows that a significant improvement on the prior-art configurations can be achieved by pushing the depth to 16-19 weight layers. These findings were the basis of our ImageNet Challenge 2014 submission, where our team secured the first and the second places in the localisation and classification tracks respectively. We also show that our representations generalise well to other datasets, where they achieve state-of-the-art results. We have made our two best-performing ConvNet models publicly available to facilitate further research on the use of deep visual representations in computer vision.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516406", "vector": [], "sparse_vector": [], "title": "Visual Scene Representations: Sufficiency, Minimality, Invariance and Deep Approximations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An ideal visual representation would be a function of visual data that is a minimal sufficient statistics for the scene, and a maximal invariant to nuisance variability. We derive analytical expressions for such representations and show that, under certain assumptions underlying the Lambert-Ambient model, they are related to convolutional architectures. This link highlights the conditions under which they can be expected to perform well, and also suggests ways to improve and generalize them. This new interpretation draws connections to the classical theories of sampling, hypothesis testing and group invariance. We show that one layer of a convolutional architecture can approximate an optimal representation of one im- age, given sufficiently many receptive fields. We also show that stacking multiple layers, each of which is invariant to a small group transformation such as affine, achieves invariance to larger groups, all the way to planar diffeomorphisms, given sufficiently many layers and receptive fields.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516407", "vector": [], "sparse_vector": [], "title": "Visual Scene Representations: Scaling and Occlusion in Convolutional Architectures.", "authors": ["<PERSON>", "Jing<PERSON> Dong", "<PERSON><PERSON>"], "summary": "We study the structure of representations, defined as approximations of minimal sufficient statistics that are maximal invariants to nuisance factors, for visual data subject to scaling and occlusion of line-of-sight. We derive analytical expressions for such representations and show that, under certain restrictive assumptions, they are related to features commonly in use in the computer vision community. This link highlights the condition tacitly assumed by these descriptors, and also suggests ways to improve and generalize them. This new interpretation draws connections to the classical theories of sampling, hypothesis testing and group invariance.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516409", "vector": [], "sparse_vector": [], "title": "Leveraging Monolingual Data for Crosslingual Compositional Word Representations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we present a novel neural network based architecture for inducing compositional crosslingual word representations. Unlike previously proposed methods, our method fulfills the following three criteria; it constrains the word-level representations to be compositional, it is capable of leveraging both bilingual and monolingual data, and it is scalable to large vocabularies and large quantities of data. The key component of our approach is what we refer to as a monolingual inclusion criterion, that exploits the observation that phrases are more closely semantically related to their sub-phrases than to other randomly sampled phrases. We evaluate our method on a well-established crosslingual document classification task and achieve results that are either comparable, or greatly improve upon previous state-of-the-art methods. Concretely, our method reaches a level of 92.7% and 84.4% accuracy for the English to German and German to English sub-tasks respectively. The former advances the state of the art by 0.9% points of accuracy, the latter is an absolute improvement upon the previous state of the art by 7.7% points of accuracy and an improvement of 33.0% in error reduction.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516410", "vector": [], "sparse_vector": [], "title": "Audio Source Separation with Discriminative Scattering Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this report we describe an ongoing line of research for solving single-channel source separation problems. Many monaural signal decomposition techniques proposed in the literature operate on a feature space consisting of a time-frequency representation of the input data. A challenge faced by these approaches is to effectively exploit the temporal dependencies of the signals at scales larger than the duration of a time-frame. In this work we propose to tackle this problem by modeling the signals using a time-frequency representation with multiple temporal resolutions. The proposed representation consists of a pyramid of wavelet scattering operators, which generalizes Constant Q Transforms (CQT) with extra layers of convolution and complex modulus. We first show that learning standard models with this multi-resolution setting improves source separation results over fixed-resolution methods. As study case, we use Non-Negative Matrix Factorizations (NMF) that has been widely considered in many audio application. Then, we investigate the inclusion of the proposed multi-resolution setting into a discriminative training regime. We discuss several alternatives using different deep neural network architectures.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516411", "vector": [], "sparse_vector": [], "title": "Striving for Simplicity: The All Convolutional Net.", "authors": ["Jost <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most modern convolutional neural networks (CNNs) used for object recognition are built using the same principles: Alternating convolution and max-pooling layers followed by a small number of fully connected layers. We re-evaluate the state of the art for object recognition from small images with convolutional networks, questioning the necessity of different components in the pipeline. We find that max-pooling can simply be replaced by a convolutional layer with increased stride without loss in accuracy on several image recognition benchmarks. Following this finding -- and building on other recent work for finding simple network structures -- we propose a new architecture that consists solely of convolutional layers and yields competitive or state of the art performance on several object recognition datasets (CIFAR-10, CIFAR-100, ImageNet). To analyze the network we introduce a new variant of the \"deconvolution approach\" for visualizing features learned by CNNs, which can be applied to a broader range of network structures than existing approaches.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516412", "vector": [], "sparse_vector": [], "title": "Understanding Locally Competitive Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently proposed neural network activation functions such as rectified linear, maxout, and local winner-take-all have allowed for faster and more effective training of deep neural architectures on large and complex datasets. The common trait among these functions is that they implement local competition between small groups of computational units within a layer, so that only part of the network is activated for any given input pattern. In this paper, we attempt to visualize and understand this self-modularization, and suggest a unified explanation for the beneficial properties of such networks. We also show how our insights can be directly useful for efficiently performing retrieval over large datasets using neural networks.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516413", "vector": [], "sparse_vector": [], "title": "Learning from Noisy Labels with Deep Neural Networks.", "authors": ["Sainbayar Sukhbaatar", "<PERSON>"], "summary": "The availability of large labeled datasets has allowed Convolutional Network models to achieve impressive recognition results. However, in many settings manual annotation of the data is impractical; instead our data has noisy labels, i.e. there is some freely available label for each image which may or may not be accurate. In this paper, we explore the performance of discriminatively-trained Convnets when trained on such noisy data. We introduce an extra noise layer into the network which adapts the network outputs to match the noisy label distribution. The parameters of this noise layer can be estimated as part of the training process and involve simple modifications to current training infrastructures for deep networks. We demonstrate the approaches on several datasets, including large scale experiments on the ImageNet classification benchmark.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516415", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Multi-Embeddings Learning of Acoustic Models.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We trained a Siamese network with multi-task same/different information on a speech dataset, and found that it was possible to share a network for both tasks without a loss in performance. The first task was to discriminate between two same or different words, and the second was to discriminate between two same or different talkers.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516417", "vector": [], "sparse_vector": [], "title": "Inducing Semantic Representation from Text by Jointly Predicting and Factorizing Relations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we propose a new method to integrate two recent lines of work: unsupervised induction of shallow semantics (e.g., semantic roles) and factorization of relations in text and knowledge bases. Our model consists of two components: (1) an encoding component: a semantic role labeling model which predicts roles given a rich set of syntactic and lexical features; (2) a reconstruction component: a tensor factorization model which relies on roles to predict argument fillers. When the components are estimated jointly to minimize errors in argument reconstruction, the induced roles largely correspond to roles defined in annotated resources. Our method performs on par with most accurate role induction methods on English, even though, unlike these previous approaches, we do not incorporate any prior linguistic knowledge about the language.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516421", "vector": [], "sparse_vector": [], "title": "Example Selection For Dictionary Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In unsupervised learning, an unbiased uniform sampling strategy is typically used, in order that the learned features faithfully encode the statistical structure of the training data. In this work, we explore whether active example selection strategies - algorithms that select which examples to use, based on the current estimate of the features - can accelerate learning. Specifically, we investigate effects of heuristic and saliency-inspired selection algorithms on the dictionary learning task with sparse activations. We show that some selection algorithms do improve the speed of learning, and we speculate on why they might work.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516423", "vector": [], "sparse_vector": [], "title": "Fast Convolutional Nets With fbfft: A GPU Performance Evaluation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We examine the performance profile of Convolutional Neural Network training on the current generation of NVIDIA Graphics Processing Units. We introduce two new Fast Fourier Transform convolution implementations: one based on NVIDIA's cuFFT library, and another based on a Facebook authored FFT implementation, fbfft, that provides significant speedups over cuFFT (over 1.5x) for whole CNNs. Both of these convolution implementations are available in open source, and are faster than NVIDIA's cuDNN implementation for many common convolutional layers (up to 23.5x for some synthetic kernel configurations). We discuss different performance regimes of convolutions, comparing areas where straightforward time domain convolutions outperform Fourier frequency domain convolutions. Details on algorithmic applications of NVIDIA GPU hardware specifics in the implementation of fbfft are also provided.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516424", "vector": [], "sparse_vector": [], "title": "Deep Networks With Large Output Spaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks have been extremely successful at various image, speech, video recognition tasks because of their ability to model deep structures within the data. However, they are still prohibitively expensive to train and apply for problems containing millions of classes in the output layer. Based on the observation that the key computation common to most neural network layers is a vector/matrix product, we propose a fast locality-sensitive hashing technique to approximate the actual dot product enabling us to scale up the training and inference to millions of output classes. We evaluate our technique on three diverse large-scale recognition tasks and show that our approach can train large-scale models at a faster rate (in terms of steps/total time) compared to baseline methods.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516425", "vector": [], "sparse_vector": [], "title": "Word Representations via Gaussian Embedding.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Current work in lexical distributed representations maps each word to a point vector in low-dimensional space. Mapping instead to a density provides many interesting advantages, including better capturing uncertainty about a representation and its relationships, expressing asymmetries more naturally than dot product or cosine similarity, and enabling more expressive parameterization of decision boundaries. This paper advocates for density-based distributed embeddings and presents a method for learning representations in the space of Gaussian distributions. We compare performance on various word embedding benchmarks, investigate the ability of these embeddings to model entailment and other asymmetric relationships, and explore novel properties of the representation.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516426", "vector": [], "sparse_vector": [], "title": "Efficient Exact Gradient Update for training Deep Networks with Very Large Sparse Targets.", "authors": ["<PERSON>"], "summary": "An important class of problems involves training deep neural networks with sparse prediction targets of very high dimension D. These occur naturally in e.g. neural language models or the learning of word-embeddings, often posed as predicting the probability of next words among a vocabulary of size D (e.g. 200 000). Computing the equally large, but typically non-sparse D-dimensional output vector from a last hidden layer of reasonable dimension d (e.g. 500) incurs a prohibitive O(Dd) computational cost for each example, as does updating the D x d output weight matrix and computing the gradient needed for backpropagation to previous layers. While efficient handling of large sparse network inputs is trivial, the case of large sparse targets is not, and has thus so far been sidestepped with approximate alternatives such as hierarchical softmax or sampling-based approximations during training. In this work we develop an original algorithmic approach which, for a family of loss functions that includes squared error and spherical softmax, can compute the exact loss, gradient update for the output weights, and gradient for backpropagation, all in O(d^2) per example instead of O(Dd), remarkably without ever computing the D-dimensional output. The proposed algorithm yields a speedup of D/4d , i.e. two orders of magnitude for typical sizes, for that critical part of the computations that often dominates the training time in this kind of network architecture.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516429", "vector": [], "sparse_vector": [], "title": "Real-World Font Recognition Using Deep Network and Domain Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Jin", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We address a challenging fine-grain classification problem: recognizing a font style from an image of text. In this task, it is very easy to generate lots of rendered font examples but very hard to obtain real-world labeled images. This real-to-synthetic domain gap caused poor generalization to new real data in previous methods (<PERSON> et al. (2014)). In this paper, we refer to Convolutional Neural Networks, and use an adaptation technique based on a Stacked Convolutional Auto-Encoder that exploits unlabeled real-world images combined with synthetic data. The proposed method achieves an accuracy of higher than 80% (top-5) on a real-world dataset.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516430", "vector": [], "sparse_vector": [], "title": "Self-informed neural network structure learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of large scale, multi-label visual recognition with a large number of possible classes. We propose a method for augmenting a trained neural network classifier with auxiliary capacity in a manner designed to significantly improve upon an already well-performing model, while minimally impacting its computational footprint. Using the predictions of the network itself as a descriptor for assessing visual similarity, we define a partitioning of the label space into groups of visually similar entities. We then augment the network with auxilliary hidden layer pathways with connectivity only to these groups of label units. We report a significant improvement in mean average precision on a large-scale object recognition task with the augmented model, while increasing the number of multiply-adds by less than 3%.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516431", "vector": [], "sparse_vector": [], "title": "Memory Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a new class of learning models called memory networks. Memory networks reason with inference components combined with a long-term memory component; they learn how to use these jointly. The long-term memory can be read and written to, with the goal of using it for prediction. We investigate these models in the context of question answering (QA) where the long-term memory effectively acts as a (dynamic) knowledge base, and the output is a textual response. We evaluate them on a large-scale QA task, and a smaller, but more complex, toy task generated from a simulated world. In the latter, we show the reasoning power of such models by chaining multiple supporting sentences to answer questions that require understanding the intension of verbs.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516435", "vector": [], "sparse_vector": [], "title": "Unsupervised Domain Adaptation with Feature Embeddings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Representation learning is the dominant technique for unsupervised domain adaptation, but existing approaches often require the specification of \"pivot features\" that generalize across domains, which are selected by task-specific heuristics. We show that a novel but simple feature embedding approach provides better performance, by exploiting the feature template structure common in NLP problems.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516436", "vector": [], "sparse_vector": [], "title": "A Unified Perspective on Multi-Domain and Multi-Task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we provide a new neural-network based perspective on multi-task learning (MTL) and multi-domain learning (MDL). By introducing the concept of a semantic descriptor, this framework unifies MDL and MTL as well as encompassing various classic and recent MTL/MDL algorithms by interpreting them as different ways of constructing semantic descriptors. Our interpretation provides an alternative pipeline for zero-shot learning (ZSL), where a model for a novel class can be constructed without training data. Moreover, it leads to a new and practically relevant problem setting of zero-shot domain adaptation (ZSDA), which is the analogous to ZSL but for novel domains: A model for an unseen domain can be generated by its semantic descriptor. Experiments across this range of problems demonstrate that our framework outperforms a variety of alternatives.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516437", "vector": [], "sparse_vector": [], "title": "Embedding Entities and Relations for Learning and Inference in Knowledge Bases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider learning representations of entities and relations in KBs using the neural-embedding approach. We show that most existing models, including NTN (<PERSON> et al., 2013) and TransE (<PERSON> et al., 2013b), can be generalized under a unified learning framework, where entities are low-dimensional vectors learned from a neural network and relations are bilinear and/or linear mapping functions. Under this framework, we compare a variety of embedding models on the link prediction task. We show that a simple bilinear formulation achieves new state-of-the-art results for the task (achieving a top-10 accuracy of 73.2% vs. 54.7% by TransE on Freebase). Furthermore, we introduce a novel approach that utilizes the learned relation embeddings to mine logical rules such as \"BornInCity(a,b) and CityInCountry(b,c) => Nationality(a,c)\". We find that embeddings learned from the bilinear objective are particularly good at capturing relational semantics and that the composition of relations is characterized by matrix multiplication. More interestingly, we demonstrate that our embedding-based rule extraction approach successfully outperforms a state-of-the-art confidence-based rule mining approach in mining Horn rules that involve compositional reasoning.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516439", "vector": [], "sparse_vector": [], "title": "Deep learning with Elastic Averaging SGD.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of stochastic optimization for deep learning in the parallel computing environment under communication constraints. A new algorithm is proposed in this setting where the communication and coordination of work among concurrent processes (local workers), is based on an elastic force which links the parameters they compute with a center variable stored by the parameter server (master). The algorithm enables the local workers to perform more exploration, i.e. the algorithm allows the local variables to fluctuate further from the center variable by reducing the amount of communication between local workers and the master. We empirically demonstrate that in the deep learning setting, due to the existence of many local optima, allowing more exploration can lead to the improved performance. We propose synchronous and asynchronous variants of the new algorithm. We provide the stability analysis of the asynchronous variant in the round-robin scheme and compare it with the more common parallelized method ADMM. We show that the stability of EASGD is guaranteed when a simple stability condition is satisfied, which is not the case for ADMM. We additionally propose the momentum-based version of our algorithm that can be applied in both synchronous and asynchronous settings. Asynchronous variant of the algorithm is applied to train convolutional neural networks for image classification on the CIFAR and ImageNet datasets. Experiments demonstrate that the new algorithm accelerates the training of deep architectures compared to DOWNPOUR and other common baseline approaches and furthermore is very communication efficient.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516440", "vector": [], "sparse_vector": [], "title": "Predictive Encoding of Contextual Relationships for Perceptual Inference, Interpolation and Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yizhou Wang", "<PERSON>"], "summary": "We propose a new neurally-inspired model that can learn to encode the global relationship context of visual events across time and space and to use the contextual information to modulate the analysis by synthesis process in a predictive coding framework. The model learns latent contextual representations by maximizing the predictability of visual events based on local and global contextual information through both top-down and bottom-up processes. In contrast to standard predictive coding models, the prediction error in this model is used to update the contextual representation but does not alter the feedforward input for the next layer, and is thus more consistent with neurophysiological observations. We establish the computational feasibility of this model by demonstrating its ability in several aspects. We show that our model can outperform state-of-art performances of gated Boltzmann machines (GBM) in estimation of contextual information. Our model can also interpolate missing events or predict future events in image sequences while simultaneously estimating contextual information. We show it achieves state-of-art performances in terms of prediction accuracy in a variety of tasks and possesses the ability to interpolate missing frames, a function that is lacking in GBM.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "4516441", "vector": [], "sparse_vector": [], "title": "Object Detectors Emerge in Deep Scene CNNs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "À<PERSON> Lapedriz<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the success of new computational architectures for visual processing, such as convolutional neural networks (CNN) and access to image databases with millions of labeled examples (e.g., ImageNet, Places), the state of the art in computer vision is advancing rapidly. One important factor for continued progress is to understand the representations that are learned by the inner layers of these deep architectures. Here we show that object detectors emerge from training CNNs to perform scene classification. As scenes are composed of objects, the CNN for scene classification automatically discovers meaningful objects detectors, representative of the learned scene categories. With object detectors emerging as a result of learning to recognize scenes, our work demonstrates that the same network can perform both scene recognition and object localization in a single forward-pass, without ever having been explicitly taught the notion of objects.", "published": "2015-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}]