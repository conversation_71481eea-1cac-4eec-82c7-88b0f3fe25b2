[{"primary_key": "2691427", "vector": [], "sparse_vector": [], "title": "A Visual Analytics Based Decision Making Environment for COVID-19 Modeling and Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Public health officials dealing with pandemics like COVID-19 have to evaluate and prepare response plans. This planning phase requires not only looking into the spatiotemporal dynamics and impact of the pandemic using simulation models, but they also need to plan and ensure the availability of resources under different spread scenarios. To this end, we have developed a visual analytics environment that enables public health officials to model, simulate, and explore the spread of COVID-19 by supplying county-level information such as population, demographics, and hospital beds. This environment facilitates users to explore spatiotemporal model simulation data relevant to COVID-19 through a geospatial map with linked statistical views, apply different decision measures at different points in time, and understand their potential impact. Users can drill-down to county-level details such as the number of sicknesses, deaths, needs for hospitalization, and variations in these statistics over time. We demonstrate the usefulness of this environment through a use case study and also provide feedback from domain experts. We also provide details about future extensions and potential applications of this work.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00024"}, {"primary_key": "2691434", "vector": [], "sparse_vector": [], "title": "GlassViz: Visualizing Automatically-Extracted Entry Points for Exploring Scientific Corpora in Problem-Driven Visualization Research.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we report the development of a model and a proof-of-concept visual text analytics (VTA) tool to enhance document discovery in a problem-driven visualization research (PDVR) context. The proposed model captures the cognitive model followed by domain and visualization experts by analyzing the interdisciplinary communication channel as represented by keywords found in two disjoint collections of research papers. High distributional intercollection similarities are employed to build informative keyword associations that serve as entry points to drive the exploration of a large document corpus. Our approach is demonstrated in the context of research on visualization for the digital humanities.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00052"}, {"primary_key": "2691442", "vector": [], "sparse_vector": [], "title": "Co-Visualization of Air Temperature and Urban Data for Visual Exploration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Urban climate data remain complex to analyze regarding their spatial distribution. The co-visualization of simulated air temperature into urban models could help experts to analyze horizontal and vertical spatial distributions. We design a co-visualization framework enabling simulated air temperature data exploration, based on the graphic representation of three types of geometric proxies, and their co-visualization with a 3D urban model with various possible rendering styles. Through this framework, we aim at allowing meteorological researchers to visually analyze and interpret the relationships between simulated air temperature data and urban morphology.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00021"}, {"primary_key": "2691456", "vector": [], "sparse_vector": [], "title": "Improving Engagement of Animated Visualization with Visual Foreshadowing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Haidong Zhang", "<PERSON><PERSON><PERSON>"], "summary": "Animated visualization is becoming increasingly popular as a compelling way to illustrate changes in time series data. However, maintaining the viewer's focus throughout the entire animation is difficult because of its time-consuming nature. Viewers are likely to become bored and distracted during the ever-changing animated visualization. Informed by the role of foreshadowing that builds the expectation in film and literature, we introduce visual foreshadowing to improve the engagement of animated visualizations. In specific, we propose designs of visual foreshadowing that engage the audience while watching the animation. To demonstrate our approach, we built a proof-of-concept animated visualization authoring tool that incorporates visual foreshadowing techniques with various styles. Our user study indicates the effectiveness of our foreshadowing techniques on improving engagement for animated visualization.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00035"}, {"primary_key": "2691428", "vector": [], "sparse_vector": [], "title": "Data Visualization for Transgender Voice Training.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Social and political factors structure how data are collected, analyzed, and visually presented. Visualization researchers are increasingly discussing these issues, and calling for feminist approaches that allow not only for the interrogation of unspoken assumptions and power imbalances in how visualizations are designed and deployed, but also for the creation of new systems by and for marginalized groups. This paper describes the motivation, ideation, and prototyping of a novel visualization tool to support voice training for transgender people. We centered our design around user needs and lived experiences, in order to support self-determination of gender expression. In so doing, we make novel inroads into how to visualize speech data, and open possibilities for future work in the design of visualizations that prioritize user agency within social and political contexts.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00051"}, {"primary_key": "2691429", "vector": [], "sparse_vector": [], "title": "Exploring How Personality Models Information Visualization Preferences.", "authors": ["<PERSON><PERSON>", "Bárbara Ramalho", "<PERSON>", "<PERSON>", "<PERSON><PERSON>Cal<PERSON>"], "summary": "Recent research on information visualization has shown how individual differences act as a mediator on how users interact with visualization systems. We focus our exploratory study on whether personality has an effect on user preferences regarding idioms used for hierarchy, evolution over time, and comparison contexts. Specifically, we leverage all personality variables from the Five-Factor Model and the three dimensions from Locus of Control (LoC) with correlation and clustering approaches. The correlation-based method suggested that Neuroticism, Openness to Experience, Agreeableness, several facets from each trait, and the External dimensions from LoC mediate how much individuals prefer certain idioms. In addition, our results from the cluster-based analysis showed that Neuroticism, Extraversion, Conscientiousness, and all dimensions from LoC have an effect on preferences for idioms in hierarchy and evolution contexts. Our results support the incorporation of in-depth personality synergies with InfoVis into the design pipeline of visualization systems.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00047"}, {"primary_key": "2691431", "vector": [], "sparse_vector": [], "title": "Let&apos;s Gamble: How a Poor Visualization Can Elicit Risky Behavior.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data visualizations are standard tools for assessing and communicating risks. However, it is not always clear which designs are optimal or how encoding choices might influence risk perception and decision-making. In this paper, we report the findings of a large-scale gambling game that immersed participants in an environment where their actions impacted their bonuses. Participants chose to either enter a lottery or receive guaranteed monetary gains based on five visualization designs. By measuring risk perception and observing decision-making, we present suggestive evidence that people were more likely to gamble when presented area proportioned triangle and circle designs. Using our results, we model risk perception and discuss how our findings can improve visualization selection.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00046"}, {"primary_key": "2691436", "vector": [], "sparse_vector": [], "title": "Visually Analyzing Contextualized Embeddings.", "authors": ["<PERSON>"], "summary": "In this paper we introduce a method for visually analyzing contextualized embeddings produced by deep neural network-based language models. Our approach is inspired by linguistic probes for natural language processing, where tasks are designed to probe language models for linguistic structure, such as parts-of-speech and named entities. These approaches are largely confirmatory, however, only enabling a user to test for information known a priori. In this work, we eschew supervised probing tasks, and advocate for unsupervised probes, coupled with visual exploration techniques, to assess what is learned by language models. Specifically, we cluster contextualized embeddings produced from a large text corpus, and introduce a visualization design based on this clustering and textual structure - cluster co-occurrences, cluster spans, and cluster-word membership- to help elicit the functionality of, and relationship between, individual clusters. User feedback highlights the benefits of our design in discovering different types of linguistic structures.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00062"}, {"primary_key": "2691437", "vector": [], "sparse_vector": [], "title": "Why Shouldn&apos;t All Charts Be Scatter Plots? Beyond Precision-Driven Visualizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A central tenet of information visualization research and practice is the notion of visual variable effectiveness, or the perceptual precision at which values are decoded given visual channels of encoding. Formative work from Cleveland & McGill has shown that position along a common axis is the most effective visual variable for comparing individual values. One natural conclusion is that any chart that is not a dot plot or scatterplot is deficient and should be avoided. In this paper we refute a caricature of this \"scatterplots only\" argument as a way to call for new perspectives on how information visualization is researched, taught, and evaluated.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00048"}, {"primary_key": "2691439", "vector": [], "sparse_vector": [], "title": "Trrack: A Library for Provenance-Tracking in Web-Based Visualizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Provenance-tracking is widely acknowledged as an important feature of visualization systems. By tracking provenance data, visualization designers can provide a wide variety of functionality, ranging from action recovery (undo/redo), reproducibility, collaboration and sharing, to logging in support of quantitative and longitudinal evaluation. However, no widely used library that can provide that functionality is current available. As a consequence, visualization designers either develop ad hoc solutions that are rarely comprehensive, or do not track provenance at all. In this paper, we introduce a web-based software library - Trrack - that is designed for easy integration in existing or future visualization systems. Trrack supports a wide range of use cases, from simple action recovery, to capturing intent and reasoning, and can be used to share states with collaborators and store provenance on a server. Trrack also includes an optional provenance visualization component that supports annotation of states and aggregation of events.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00030"}, {"primary_key": "2691446", "vector": [], "sparse_vector": [], "title": "Visualizing Information on Watch Faces: A Survey with Smartwatch Users.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "People increasingly wear smartwatches that can track a wide variety of data. However, it is currently unknown which data people consume and how it is visualized. To better ground research on smartwatch visualization, it is important to understand the current use of these representation types on smartwatches, and to identify missed visualization opportunities. We present the findings of a survey with 237 smartwatch wearers, and assess the types of data and representations commonly displayed on watch faces. We found a predominant display of health & fitness data, with icons accompanied by text being the most frequent representation type. Combining these results with a further analysis of online searches of watch faces and the data tracked on smartwatches that are not commonly visualized, we discuss opportunities for visualization research. Supplementary material is available at https://osf.io/nwy2r/.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00038"}, {"primary_key": "2691447", "vector": [], "sparse_vector": [], "title": "How Does Visualization Help People Learn Deep Learning? Evaluating GAN Lab with Observational Study and Log Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Polo) Chau"], "summary": "While a rapidly growing number of people want to learn artificial intelligence (AI) and deep learning, the increasing complexity of such models poses significant learning barriers. Recently, interactive visualizations, such as TensorFlow Playground and GAN Lab, have demonstrated success in lowering these barriers. However, there has been little work in evaluating these tools with human subjects. This paper presents two studies on evaluating GAN Lab, an interactive tool designed to help people learn how Generated Adversarial Networks (GANs) work. First, through an observational study, we investigate how the tool is used and what users learn from their usage. Second, we conduct a log analysis of the deployed tool to investigate how its visitors engage with GAN Lab. Based on the studies and our experience in developing and successfully deploying the tool, we provide design considerations and discuss further evaluation challenges for interactive educational tools for deep learning.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00060"}, {"primary_key": "2691448", "vector": [], "sparse_vector": [], "title": "A Didactic Methodology for Crafting Information Visualizations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Finding or creating the right information visualization solution that meets design goals can be a very challenging task, not only for students but also for visualization experts. In this paper, we introduce a didactic methodology for designing interactive visualizations through hands-on activities. Our approach can assist students and anyone interested in crafting, ranking, and improving new visualization solutions. The suggested approach follows divergent and convergent thinking that motivates designing several low-fidelity prototypes, discussing pros and cons of each in groups, and improving the final solution by incorporating visual and perceptual principles. The methodology is described with teaching course examples for undergraduate and graduate students. We also list observations made when applying the methodology online and offline along with gathered student feedback.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00044"}, {"primary_key": "2691450", "vector": [], "sparse_vector": [], "title": "Gaze-Driven Links for Magazine Style Narrative Visualizations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Magazine Style Narrative Visualizations (MSNV) can be challenging due to the need to integrate textual and visual information. This problem has prompted researchers to design dynamic guidance meant to ease the mapping of the information provided in the two modalities. We contribute to this line of work by evaluating gaze-driven adaptive guidance that dynamically links relevant sentences in the text to the corresponding datapoints in the visualizations (see Fig. 1). We conducted a user study that involved participants reading a series of MSNVs extracted from real-world sources. Results show that the adaptive links significantly improve the comprehension of the MSNVs as compared to receiving no guidance. This improvement comes at no expense of user reading time, and is consistent regardless of the MSNV complexity.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00040"}, {"primary_key": "2691452", "vector": [], "sparse_vector": [], "title": "What are Data Insights to Professional Visualization Users?", "authors": ["Po<PERSON>Ming Law", "<PERSON>", "<PERSON>"], "summary": "While many visualization researchers have attempted to define data insights, little is known about how visualization users perceive them. We interviewed 23 professional users of end-user visualization platforms (e.g., Tableau and Power BI) about their experiences with data insights. We report on seven characteristics of data insights based on interviewees' descriptions. Grounded in these characteristics, we propose practical implications for creating tools that aim to automatically communicate data insights to users.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00043"}, {"primary_key": "2691453", "vector": [], "sparse_vector": [], "title": "COVIs: Supporting Temporal Visual Analysis of Covid-19 Events Usable in Data-Driven Journalism.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Caused by a newly discovered coronavirus, COVID-19 is an infectious disease easily transmitted between people through close contacts that had exponential global growth in 2020 and became, in a very short time, a major health, and economic global issue. Real-world data concerning the spread of the disease was quickly made available by different global institutions and resulted in many works involving data visualizations and prediction models. In this paper, (1) we discuss the problem, data aspects, and challenges of COVID-19 data analysis; (2) We propose a Visual Analytics approach (called COVis) combining different temporal aspects of COVID-19 data with the output of a predictive model. This combination supports the estimation of the spread of the disease in different scenarios and allows correlating and monitoring the virus development in relation to different government response events; (3) We evaluate the approach with two domain experts to support the understanding of how our system can facilitate journalistic investigation tasks and (4) we discuss future works and a possible generalization of our solution.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00018"}, {"primary_key": "2691455", "vector": [], "sparse_vector": [], "title": "CrowdTrace: Visualizing Provenance in Distributed Sensemaking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Capturing analytic provenance is important for refining sensemaking analysis. However, understanding this provenance can be difficult. First, making sense of the reasoning in intermediate steps is time-consuming. Especially in distributed sensemaking, the provenance is less cohesive because each analyst only sees a small portion of the data without an understanding of the overall collaboration workflow. Second, analysis errors from one step can propagate to later steps. Furthermore, in exploratory sensemaking, it is difficult to define what an error is since there are no correct answers to reference. In this paper, we explore provenance analysis for distributed sense-making in the context of crowdsourcing, where distributed analysis contributions are captured in microtasks. We propose crowd auditing as a way to help individual analysts visualize and trace provenance to debug distributed sensemaking. To evaluate this concept, we implemented a crowd auditing tool, CrowdTrace. Our user study-based evaluation demonstrates that CrowdTrace offers an effective mechanism to audit and refine multi-step crowd sensemaking.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00045"}, {"primary_key": "2691457", "vector": [], "sparse_vector": [], "title": "A Visual Analytics Approach to Scheduling Customized Shuttle Buses via Perceiving Passengers&apos; Travel Demands.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Shuttle buses have been a popular means to move commuters sharing similar origins and destinations during periods of high travel demand. However, planning and deploying reasonable, customized service bus systems becomes challenging when the commute demand is rather dynamic. It is difficult, if not impossible to form a reliable, unbiased estimation of user needs in such a case using traditional modeling methods. We propose a visual analytics approach to facilitating assessment of actual, varying travel demands and planning of night customized shuttle systems. A preliminary case study verifies the efficacy of our approach.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00022"}, {"primary_key": "2691458", "vector": [], "sparse_vector": [], "title": "Loch Prospector: Metadata Visualization for Lakes of Open Data.", "authors": ["Neha Makhija", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sara Di Bartolomeo", "<PERSON>"], "summary": "Data lakes are an emerging storage paradigm that promotes data availability over integration. A prime example are repositories of Open Data which show great promise for transparent data science. Due to the lack of proper integration, Data Lakes may not have a common consistent schema and traditional data management techniques fall short with these repositories. Much recent research has tried to address the new challenges associated with these data lakes. Researchers in this area are mainly interested in the structural proper-ties of the data for developing new algorithms, yet typical Open Data portals offer limited functionality in that respect and instead focus on data semantics. We propose Loch Prospector, a visualization to assist data management researchers in exploring and understanding the most crucial structural aspects of Open Data - in particular, metadata attributes - and the associated task abstraction for their work. Our visualization enables researchers to navigate the contents of data lakes effectively and easily accomplish what were previously laborious tasks. A copy of this paper with all supplemental material is available at osf.io/zkxv9.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00032"}, {"primary_key": "2691460", "vector": [], "sparse_vector": [], "title": "Just TYPEical: Visualizing Common Function Type Signatures in R.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sara Di Bartolomeo", "<PERSON>"], "summary": "Data-driven approaches to programming language design are uncommon. Despite the availability of large code repositories, distilling semantically-rich information from programs remains difficult. Important dimensions, like run-time type data, are inscrutable without the appropriate tools. We contribute a task abstraction and interactive visualization, TYPEICAL, for programming language designers who are exploring and analyzing type information from execution traces. Our approach aids user understanding of function type signatures across many executions. Insights derived from our visualization are aimed at informing language design decisions - specifically of a new gradual type system being developed for the R programming language. A copy of this paper, along with all the supplemental material, is available at osf.io/mc6zt.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00031"}, {"primary_key": "2691461", "vector": [], "sparse_vector": [], "title": "Representing Real-Time Multi-User Collaboration in Visualizations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Establishing common ground and maintaining shared awareness amongst participants is a key challenge in collaborative visualization. For real-time collaboration, existing work has primarily focused on synchronizing constituent visualizations - an approach that makes it difficult for users to work independently, or selectively attend to their collaborators' activity. To address this gap, we introduce a design space for representing synchronous multi-user collaboration in visualizations defined by two orthogonal axes: situatedness, or whether collaborators' interactions are overlaid on or shown outside of a user's view, and specificity, or whether collaborators are depicted through abstract, generic representations or through specific means customized for the given visualization. We populate this de-sign space with a variety of examples including generic and custom synchronized cursors, and user legends that collect these cursors together or reproduce collaborators' views as thumbnails. To build common ground, users can interact with these representations by peeking to take a quick look at a collaborator's view, tracking to follow along with a collaborator in real-time, and forking to independently explore the visualization based on a collaborator's work. We present a reference implementation of a wrapper library that converts interactive Vega-Lite charts into collaborative visualizations. We find that our approach affords synchronous collaboration across an expressive range of visual designs and interaction techniques.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00036"}, {"primary_key": "2691462", "vector": [], "sparse_vector": [], "title": "Designing for Ambiguity: Visual Analytics in Avalanche Forecasting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ambiguity, an information state where multiple interpretations are plausible, is a common challenge in visual analytics (VA) systems. We discuss lessons learned from a case study designing VA tools for Canadian avalanche forecasters. Avalanche forecasting is a complex and collaborative risk-based decision-making and analysis domain, demanding experience and knowledge-based interpretation of human reported and uncertain data. Differences in reporting practices, organizational contexts, and the particularities of individual reports result in a variety of potential interpretations that have to be negotiated as part of the forecaster's sensemaking processes. We describe our preliminary research using glyphs to support sensemaking under ambiguity. Ambiguity is not unique to public avalanche forecasting. There are many other domains where the way data are measured and reported vary in ways not accounted explicitly in the data and require analysts to negotiate multiple potential meanings. We argue that ambiguity is under-served by visualization research and would benefit from more explicit VA support.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00023"}, {"primary_key": "2691463", "vector": [], "sparse_vector": [], "title": "Design Judgment in Data Visualization Practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data visualization is becoming an increasingly popular field of design practice. Although many studies have highlighted the knowledge required for effective data visualization design, their focus has largely been on formal knowledge and logical decision-making processes that can be abstracted and codified. Less attention has been paid to the more situated and personal ways of knowing that are prevalent in all design activity. In this study, we conducted semistructured interviews with data visualization practitioners during which they were asked to describe the practical and situated aspects of their design processes. Using a philosophical framework of design judgment from <PERSON> and <PERSON> [23], we analyzed the transcripts to describe the volume and complex layering of design judgments that are used by data visualization practitioners as they describe and interrogate their work. We identify aspects of data visualization practice that require further investigation beyond notions of rational, model- or principle-directed decision-making processes.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00042"}, {"primary_key": "2691464", "vector": [], "sparse_vector": [], "title": "Data Visualization Practitioners&apos; Perspectives on Chartjunk.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Chartjunk is a popular yet contentious topic. Previous studies have shown that extreme minimalism is not always best, and that visual embellishments can be useful depending on the context. While more knowledge is being developed regarding the effects of embellishments on users, less attention has been given to the perspectives of practitioners regarding how they design with embellishments. We conducted semi-structured interviews with 20 data visualization practitioners, investigating how they understand chartjunk and the factors that influence how and when they make use of embellishments. Our investigation uncovers a broad and pluralistic understanding of chartjunk among practitioners, and foregrounds a variety of personal and situated factors that influence the use of chartjunk beyond context. We highlight the personal nature of design practice, and discuss the need for more practice-led research to better understand the ways in which concepts like chartjunk are interpreted and used by practitioners.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00049"}, {"primary_key": "2691465", "vector": [], "sparse_vector": [], "title": "Evaluating Animated Transitions between Contiguous Visualizations for Streaming Big Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "An approach to analyzing Streaming Big Data as it comes in while maintaining the proper context of past events is to employ contiguous visualizations with an increasingly aggressive aggregation degree. This allows for the most recent data to be displayed in detail, while older data is shown in an aggregated form according to how long ago it was received. However, the transitions applied between visualizations with different aggregations must not compromise the understandability of the data flow. Particularly, new data should be perceived considering the context established by older data, and the visualizations should not be perceived as independent or un-connected. In this paper, we present the first study on transitions between two contiguous visualizations, focusing on time series data. We developed several animated transitions between a scatter plot, where all data points are individually represented as they arrive, and other visualizations where data is displayed in an aggregated form. We then conducted a user evaluation to assess the most appealing and effective transitions that allow for the best comprehension of the displayed data for each visualization pair.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00039"}, {"primary_key": "2691466", "vector": [], "sparse_vector": [], "title": "InstanceFlow: Visualizing the Evolution of Classifier Confusion at the Instance Level.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Classification is one of the most important supervised machine learning tasks. During the training of a classification model, the training instances are fed to the model multiple times (during multiple epochs) in order to iteratively increase the classification performance. The increasing complexity of models has led to a growing demand for model interpretability through visualizations. Existing approaches mostly focus on the visual analysis of the final model performance after training and are often limited to aggregate performance measures. In this paper we introduce InstanceFlow, a novel dual-view visualization tool that allows users to analyze the learning behavior of classifiers over time on the instance-level. A Sankey diagram visualizes the flow of instances throughout epochs, with on-demand detailed glyphs and traces for individual instances. A tabular view allows users to locate interesting instances by ranking and filtering. In this way, InstanceFlow bridges the gap between class-level and instance-level performance evaluation while enabling users to perform a full temporal analysis of the training process.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00065"}, {"primary_key": "2691468", "vector": [], "sparse_vector": [], "title": "ClaimViz: Visual Analytics for Identifying and Verifying Factual Claims.", "authors": ["<PERSON><PERSON> <PERSON>", "Enamul <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Verifying a factual claim made by public figures, aka fact-checking, is a common task of the journalists in the newsrooms. One critical challenge that fact-checkers face is-they have to swift through a large amount of text to find claims that are check-worthy. While there exist some computational methods for automating the fact-checking process, little research has been done on how a system should combine such techniques with visualizations to assist fact-checkers. ClaimViz is a visual analytic system that integrates natural language processing and machine learning methods with interactive visualizations to facilitate the fact-checking process. The design of ClaimViz is based on analyzing the requirements of real fact-checkers and our case studies demonstrate how the system can help users to effectively spot and verify claims.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00056"}, {"primary_key": "2691470", "vector": [], "sparse_vector": [], "title": "Visually Analyzing and Steering Zero Shot Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a visual analytics system to help a user analyze and steer zero-shot learning models. Zero-shot learning has emerged as a viable scenario for categorizing data that consists of no labeled examples, and thus a promising approach to minimize data annotation from humans. However, it is challenging to understand where zero-shot learning fails, the cause of such failures, and how a user can modify the model to prevent such failures. Our visualization system is designed to help users diagnose and understand mispredictions in such models, so that they may gain insight on the behavior of a model when applied to data associated with categories not seen during training. Through usage scenarios, we highlight how our system can help a user improve performance in zero-shot learning.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00057"}, {"primary_key": "2691472", "vector": [], "sparse_vector": [], "title": "Sentifiers: Interpreting Vague Intent Modifiers in Visual Analysis using Word Co-occurrence and Sentiment Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Natural language interaction with data visualization tools often involves the use of vague subjective modifiers in utterances such as \"show me the sectors that are performing\" and \"where is a good neighborhood to buy a house?.\" Interpreting these modifiers is often difficult for these tools because their meanings lack clear semantics and are in part defined by context and personal user preferences. This paper presents a system called Sentifiers that makes a first step in better understanding these vague predicates. The algorithm employs word co-occurrence and sentiment analysis to determine which data attributes and filters ranges to associate with the vague predicates. The provenance results from the algorithm are exposed to the user as interactive text that can be repaired and refined. We conduct a qualitative evaluation of the Sentifiers that indicates the usefulness of the interface as well as opportunities for better supporting subjective utterances in visual analysis tasks through natural language.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00050"}, {"primary_key": "2691473", "vector": [], "sparse_vector": [], "title": "DebateVis: Visualizing Political Debates for Non-Expert Users.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Political debates provide an important opportunity for voters to observe candidate behavior, learn about issues, and make voting decisions. However, debates are generally broadcast late at night and last more than ninety minutes, so watching debates live can be inconvenient, if not impossible, for many potential viewers. Even voters who do watch debates may find themselves overwhelmed by a deluge of information in a substantive, issue-driven debate. Media outlets produce short summaries of debates, but these are not always effective as a method of deeply comprehending the policies candidates propose or the debate techniques they employ. In this paper we contribute reflections and results of an 18-month design study through an interdisciplinary collaboration with journalism and political science researchers. We characterize task and data abstractions for visualizing political debate transcripts for the casual user, and present a novel tool (DebateVis) to help non-expert users explore and analyze debate transcripts.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00055"}, {"primary_key": "2691476", "vector": [], "sparse_vector": [], "title": "TradAO: A Visual Analytics System for Trading Algorithm Optimization.", "authors": ["<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the wide applications of algorithmic trading, it has become critical for traders to build a winning trading algorithm to beat the market. However, due to the lack of efficient tools, traders mainly rely on their memory to manually compare the algorithm instances of a trading algorithm and further select the best trading algorithm instance for the real trading deployment. We work closely with industry practitioners to discover and consolidate user requirements and develop an interactive visual analytics system for trading algorithm optimization. Structured expert interviews are conducted to evaluate TradAO and a representative case study is documented for illustrating the system effectiveness. To the best of our knowledge, previous financial data visual analyses have mainly aimed to assist investment managers in investment portfolio analysis but have neglected the need of traders in developing trading algorithms for portfolio execution. TradAO is the first visual analytics system that assists users in comprehensively exploring the performances of a trading algorithm with different parameter settings.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00019"}, {"primary_key": "2691477", "vector": [], "sparse_vector": [], "title": "Knowing what to look for: A Fact-Evidence Reasoning Framework for Decoding Communicative Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the widespread use of charts as a medium for communicating data in science domains, we lack a systematic understanding of the goals and principles of effective visual communication. Existing research mostly focuses on the means, i.e. the encoding principles, and not the end, i.e. the key takeaway of a chart. To address this gap, we start from the first principles and aim to answer the fundamental question: how can we describe the message of a scientific chart? We contribute a fact-evidence reasoning framework (FaEvR) by augmenting the conventional visualization pipeline with the stages of gathering and associating evidence for decoding the facts presented in a chart. We apply the resulting classification scheme of fact and evidence on a collection of 500 charts collected from publications in multiple science domains. We demonstrate the practical applications of FaEvR in calibrating task complexity and detecting barriers towards chart interpretability.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00053"}, {"primary_key": "2691481", "vector": [], "sparse_vector": [], "title": "Encodable: Configurable Grammar for Visualization Components.", "authors": ["<PERSON><PERSON>"], "summary": "There are so many Libraries of visualization components nowadays with their APIs often different from one another. Could these components be more similar, both in terms of the APIs and common functionalities? For someone who is developing a new visualization component, how should the API look like? This work drew inspiration from visualization grammar, decoupled the grammar from its rendering engine and adapted it into a configurable grammar for individual components called Encodable. Encodable helps component authors define grammar for their components, and parse encoding specifications from users into utility functions for the implementation. This paper explains the grammar design and demonstrates how to build components with it.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00033"}, {"primary_key": "2691483", "vector": [], "sparse_vector": [], "title": "A Review of Geospatial Content in IEEE Visualization Publications.", "authors": ["<PERSON>", "<PERSON>", "Elyssa L. <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Geospatial analysis is crucial for addressing many of the world's most pressing challenges. Given this, there is immense value in improving and expanding the visualization techniques used to communicate geospatial data. In this work, we explore this important intersection - between geospatial analytics and visualization - by examining a set of recent IEEE VIS Conference papers (a selection from 2017-2019) to assess the inclusion of geospatial data and geospatial analyses within these papers. After removing the papers with no geospatial data, we organize the remaining literature into geospatial data domain categories and provide insight into how these categories relate to VIS Conference paper types. We also contextualize our results by investigating the use of geospatial terms in IEEE Visualization publications over the last 30 years. Our work provides an understanding of the quantity and role of geospatial subject matter in recent IEEE VIS publications and supplies a foundation for future meta-analytical work around geospatial analytics and geovisualization that may shed light on opportunities for innovation.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00017"}, {"primary_key": "2691485", "vector": [], "sparse_vector": [], "title": "ProtoViewer: Visual Interpretation and Diagnostics of Deep Neural Networks with Factorized Prototypes.", "authors": ["<PERSON><PERSON>", "<PERSON>g <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years deep neural networks (DNNs) are increasingly used in a variety of application domains for their state-of-the-art performance in many challenging machine learning tasks. However their lack of interpretability could cause trustability and fairness issues and also makes model diagnostics a difficult task. In this paper we present a novel visual analytics framework to interpret and diagnose DNNs. Our approach utilizes ProtoFac to factorize the latent representations in DNNs into weighted combinations of prototypes, which are exemplar cases (e.g., representative image patches) from the original data. The visual interface uses the factorized prototypes to summarize and explain the model behaviour as well as support comparisons across subsets of data such that the users can form a hypothesis about the model's failure on certain subsets. The method is model-agnostic and provides global explanation of the model behaviour. Furthermore, the system selects prototypes and weights that faithfully represents the model under analysis by mimicking its latent representation and predictions. Example usage scenarios on two DNN architectures and two datasets illustrates the effectiveness and general applicability of the proposed approach.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00064"}, {"primary_key": "2691430", "vector": [], "sparse_vector": [], "title": "MeetCues: Supporting Online Meetings Experience.", "authors": ["<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The remote work ecosystem is transforming patterns of communication between teams and individuals located at distance. Particularly, the absence of certain subtle cues in current communication tools may hinder an online's meeting outcome by negatively impacting attendees' overall experience and, often, make them feeling disconnected. The problem here might be due to the fact that current tools fall short in capturing it. To partly address this, we developed an online platform-MeetCues-with the aim of supporting online communication during meetings. MeetCues is a companion platform for a commercial communication tool with interactive and visual UI features that support back-channels of communications. It allows attendees to be more engaged during a meeting, and reflect in real-time or post-meeting. We evaluated our platform in a diverse set of five, real-world corporate meetings, and we found that, not only people were more engaged and aware during their meetings, but they also felt more connected. These findings suggest promise in the design of new communications tools, and reinforce the role of InfoVis in augmenting and enriching online meetings.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00054"}, {"primary_key": "2691432", "vector": [], "sparse_vector": [], "title": "Topological Analysis of Magnetic Reconnection in Kinetic Plasma Simulations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Magnetic reconnection is a ubiquitous plasma process in which oppositely directed magnetic field lines break and rejoin, resulting in a change of the magnetic field topology. Reconnection generates magnetic islands: regions enclosed by magnetic field lines and separated by reconnection points. Proper identification of these features is important to understand particle acceleration and overall behavior of plasma. We present a contour-tree based visualization for robust and objective identification of islands and reconnection points in two-dimensional (2D) magnetic reconnection simulations. The application of this visualization to a simple simulation has revealed a physical phenomenon previously not reported, resulting in a more comprehensive understanding of magnetic reconnection.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00008"}, {"primary_key": "2691433", "vector": [], "sparse_vector": [], "title": "PRAGMA: Interactively Constructing Functional Brain Parcellations.", "authors": ["Roza G. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A prominent goal of neuroimaging studies is mapping the human brain, in order to identify and delineate functionally-meaningful regions and elucidate their roles in cognitive behaviors. These brain regions are typically represented by atlases that capture general trends over large populations. Despite being indispensable to neuroimaging experts, population-level atlases do not capture individual differences in functional organization. In this work, we present an interactive visualization method, PRAGMA, that allows domain experts to derive scan-specific parcellations from established atlases. PRAGMA features a user-driven, hierarchical clustering scheme for defining temporally correlated parcels in varying granularity. The visualization design supports the user in making decisions on how to perform clustering, namely when to expand, collapse, or merge parcels. This is accomplished through a set of linked and coordinated views for understanding the user's current hierarchy, assessing intra-cluster variation, and relating parcellations to an established atlas. We assess the effectiveness of PRAGMA through a user study with four neuroimaging domain experts, where our results show that PRAGMA shows the potential to enable exploration of individualized and state-specific brain parcellations and to offer interesting insights into functional brain networks.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00016"}, {"primary_key": "2691435", "vector": [], "sparse_vector": [], "title": "Vortex Boundary Identification using Convolutional Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Feature extraction is an integral component of scientific visualization, and specifically in situations in which features are difficult to formalize, deep learning has great potential to aid in data analysis. In this paper, we develop a deep neural network that is capable of finding vortex boundaries. For training data generation, we employ a parametric flow model that generates thousands of vector field patches with known ground truth. Compared to previous methods, our approach does not require the manual setting of a threshold in order to generate the training data or to extract the vortices. After supervised learning, we apply the method to numerical fluid flow simulations, demonstrating its applicability in practice. Our results show that the vortices extracted using the proposed method can capture more accurate behavior of the vortices in the flow.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00059"}, {"primary_key": "2691438", "vector": [], "sparse_vector": [], "title": "DRIL: Descriptive Rules by Interactive Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Analyzing data is increasingly a part of jobs across industry, science and government, but data stakeholders are not necessarily experts in analytics. The human-in-the-loop (HIL) approach includes semantic interaction tools, which leverage machine learning behind the scenes to assist users with their tasks without engaging them directly with algorithms. One widely applicable model for how humans under-stand data is descriptive rules, which can characterize important attributes and simultaneously their crucial values or ranges. In this paper, we introduce an approach to help with data understanding via interactively and automatically generated rules. Our approach makes discerning the behavior of groups of interesting data efficient and simple by bridging the gap between machine learning methods for rule learning and the user experience of sensemaking through visual exploration. We have evaluated our approach with machine learning experiments to confirm an existing rule learning algorithm performs well in this interactive context even with a small amount of user input, and created a prototype system, DRIL (Descriptive Rules by Interactive Learning), to demonstrate its capability through a case study.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00058"}, {"primary_key": "2691440", "vector": [], "sparse_vector": [], "title": "DRUIDJS - A JavaScript Library for Dimensionality Reduction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dimensionality reduction (DR) is a widely used technique for visualization. Nowadays, many of these visualizations are developed for the web, most commonly using JavaScript as the underlying programming language. So far, only few DR methods have a JavaScript implementation though, necessitating developers to write wrappers around implementations in other languages. In addition, those DR methods that exist in JavaScript libraries, such as PCA, t-SNE, and UMAP, do not offer consistent programming interfaces, hampering the quick integration of different methods. Toward a coherent and comprehensive DR programming framework, we developed an open source JavaScript library named DRUID JS . Our library contains implementations of ten different DR algorithms, as well as the required linear algebra techniques, tools, and utilities.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00029"}, {"primary_key": "2691441", "vector": [], "sparse_vector": [], "title": "Bluff: Interactively Deciphering Adversarial Attacks on Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Haekyu Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Polo) Chau"], "summary": "Deep neural networks (DNNs) are now commonly used in many domains. However, they are vulnerable to adversarial attacks: carefully-crafted perturbations on data inputs that can fool a model into making incorrect predictions. Despite significant research on developing DNN attack and defense techniques, people still lack an understanding of how such attacks penetrate a model's internals. We present Bluff, an interactive system for visualizing, characterizing, and deciphering adversarial attacks on vision-based neural networks. Bluff allows people to flexibly visualize and compare the activation pathways for benign and attacked images, revealing mechanisms that adversarial attacks employ to inflict harm on a model. Bluff is open-sourced and runs in modern web browsers.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00061"}, {"primary_key": "2691443", "vector": [], "sparse_vector": [], "title": "A Virtual Frame Buffer Abstraction for Parallel Rendering of Large Tiled Display Walls.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present dw2, a flexible and easy-to-use software infrastructure for interactive rendering of large tiled display walls. Our library represents the tiled display wall as a single virtual screen through a display \"service\", which renderers connect to and send image tiles to be displayed, either from an on-site or remote cluster. The display service can be easily configured to support a range of typical network and display hardware configurations; the client library provides a straightforward interface for easy integration into existing renderers. We evaluate the performance of our display wall service in different configurations using a CPU and GPU ray tracer, in both on-site and remote rendering scenarios using multiple display walls.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00009"}, {"primary_key": "2691444", "vector": [], "sparse_vector": [], "title": "Relationship-aware Multivariate Sampling Strategy for Scientific Simulation Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the increasing computational power of current supercomputers, the size of data produced by scientific simulations is rapidly growing. To reduce the storage footprint and facilitate scalable post-hoc analyses of such scientific data sets, various data reduction/summarization methods have been proposed over the years. Different flavors of sampling algorithms exist to sample the high-resolution scientific data, while preserving important data properties required for subsequent analyses. However, most of these sampling algorithms are designed for univariate data and cater to post-hoc analyses of single variables. In this work, we propose a multivariate sampling strategy which preserves the original variable relationships and enables different multivariate analyses directly on the sampled data. Our proposed strategy utilizes principal component analysis to capture the variance of multivariate data and can be built on top of any existing state-of-the-art sampling algorithms for single variables. In addition, we also propose variants of different data partitioning schemes (regular and irregular) to efficiently model the local multivariate relationships. Using two real-world multivariate data sets, we demonstrate the efficacy of our proposed multivariate sampling strategy with respect to its data reduction capabilities as well as the ease of performing efficient post-hoc multivariate analyses.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00015"}, {"primary_key": "2691445", "vector": [], "sparse_vector": [], "title": "A Study of Opacity Ranges for Transparent Overlays in 3D Landscapes.", "authors": ["<PERSON>", "Li Ji", "<PERSON>", "<PERSON>"], "summary": "When visualizing data in a realistically rendered 3D virtual environment, it is often important to represent not only the 3D scene but also overlaid information about additional, abstract data. These overlays must be usefully visible, i.e. be readable enough to convey the information they represent, but remain unobtrusive to avoid cluttering the view. We take a step toward establishing guidelines for designing such overlays by studying the relationship between three different patterns (filled, striped and dotted patterns), two pattern densities, the presence or not of a solid outline, two types of background (blank and with trees), and the opacity of the overlay. For each combination of factors, participants set the faintest and the strongest acceptable opacity values. Results from this first study suggest that i) ranges of acceptable opacities are around 20-70%, that ii) ranges can be extended by 5% by using an outline, and that iii) ranges shift based on features like pattern and density.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00020"}, {"primary_key": "2691449", "vector": [], "sparse_vector": [], "title": "Fast and Flexible Overlap Detection for Chart Labeling with Occupancy Bitmap.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Legible labels should not overlap with other marks in a chart. The state-of-the-art labeling algorithm detects overlaps using a set of points to approximate each mark's shape. This approach is inefficient for large marks or many marks as it requires too many points to detect overlaps. In response, we present a Bitmap-Based label placement algorithm, which leverages occupancy bitmap to accelerate overlap detection. To create an occupancy bitmap, we rasterize marks onto a bitmap based on the area they occupy in the chart. With the bitmap, we can efficiently place labels without overlapping existing marks, regardless of the number and geometric complexity of the marks. This Bitmap-Based algorithm offers significant performance improvements over the state-of-the-art approach while placing a similar number of labels.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00027"}, {"primary_key": "2691451", "vector": [], "sparse_vector": [], "title": "Characterizing Automated Data Insights.", "authors": ["Po<PERSON>Ming Law", "<PERSON>", "<PERSON>"], "summary": "Many researchers have explored tools that aim to recommend data insights to users. These tools automatically communicate a rich diversity of data insights and offer such insights for many different purposes. However, there is a lack of structured understanding concerning what researchers of these tools mean by \"insight\" and what tasks in the analysis workflow these tools aim to support. We conducted a systematic review of existing systems that seek to recommend data insights. Grounded in the review, we propose 12 types of automated insights and four purposes of automating insights. We further discuss the design opportunities emerged from our analysis.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00041"}, {"primary_key": "2691454", "vector": [], "sparse_vector": [], "title": "Mapping the Global South: Equal-Area Projections for Choropleth Maps.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Choropleth maps are among the most common visualization techniques used to present geographical data. These maps require an equal-area projection but there are no clear criteria for selecting one. We collaborated with 20 social scientists researching on the Global South, interested in using choropleth maps, to investigate their design choices according to their research tasks. We asked them to design world choropleth maps through a survey, and analyzed their answers both qualitatively and quantitatively. The results suggest that the design choices of map projection, center, scale, and color scheme, were influenced by their personal research goals and the tasks. The projection was considered the most important choice and the Equal Earth projection was the most common projection used. Our study takes the first substantial step in investigating projection choices for world choropleth maps in applied visualization research.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00025"}, {"primary_key": "2691459", "vector": [], "sparse_vector": [], "title": "High-quality real-time raycasting and raytracing of streamtubes with sparse voxel octrees.", "authors": ["<PERSON>"], "summary": "Voxel-based rendering and signed distance function (SDF) raycasting have been active areas of graphics research recently because they simplify high-quality graphical effects like ambient occlusion and shadowing as compared to rasterization. Much work has centered around converting triangle meshes to sparse voxel octrees (SVOs) and developing memory efficient storage schemes but little exploration of the implications to scientific visualization has taken place. In this work we explore techniques for high-performance rendering of tubes, such as streamtubes used for visualizing vector fields and fiber tracts from diffusion tensor MRI. We first present our method for generating a voxelization of the tubes, and then describe several methods for rendering: raytracing a SVO storing straight tube segments in the leaves, and hybrid raytracing/raycasting a SVO storing curved tube segments. We discuss the tradeoffs inherent in these different representations and compare the rendering techniques and results. Compared to standard graphics pipeline approaches, like using the geometry shader, we achieve joining, capping, and smooth circular cross-sections with minimal additional effort.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00011"}, {"primary_key": "2691467", "vector": [], "sparse_vector": [], "title": "Uncertain Transport in Unsteady Flows.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study uncertainty in the dynamics of time-dependent flows by identifying barriers and enhancers to stochastic transport. This topological segmentation is closely related to the theory of Lagrangian coherent structures and is based on a recently introduced quantity, the diffusion barrier strength (DBS). The DBS is defined similar to the finite-time Lyapunov exponent (FTLE), but incorporates diffusion during flow integration. Height ridges of the DBS indicate stochastic transport barriers and enhancers, i.e. material surfaces that are minimally or maximally diffusive. To apply these concepts to real-world data, we represent uncertainty in a flow by a stochastic differential equation that consists of a deterministic and a stochastic component modeled by a Gaussian. With this formulation we identify barriers and enhancers to stochastic transport, without performing expensive Monte Carlo simulation and with a computational complexity comparable to FTLE. In addition, we propose a complementary visualization to convey the absolute scale of uncertainties in the Lagrangian frame of reference. This enables us to study uncertainty in real-world datasets, for example due to small deviations, data reduction, or estimated from multiple ensemble runs.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00010"}, {"primary_key": "2691469", "vector": [], "sparse_vector": [], "title": "GPU-based Raycasting of Hermite Spline Tubes.", "authors": ["<PERSON>", "Mir<PERSON>", "<PERSON>"], "summary": "Visualizing curve and trajectory data is a common task in many scientific fields including medicine and physics. Tubes are an effective visualization primitive for this sort of data, but they require highly specialized renderers to achieve high image quality at frame rates sufficient for interactive visualization. We present a rendering algorithm for Hermite spline tubes, i.e. tubes that result from Hermite splines interpolating the data, with support for varying-radii circular tube cross sections. Our approach employs raycasting and works directly on this continuous representation without the need for surface tessellation, made possible by an efficient ray-tube intersection routine suitable for execution on modern GPUs.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00012"}, {"primary_key": "2691471", "vector": [], "sparse_vector": [], "title": "The Anatomical Edutainer.", "authors": ["<PERSON><PERSON>", "<PERSON>sian<PERSON><PERSON><PERSON>", "Renata G<PERSON>"], "summary": "Physical visualizations (i.e., data representations by means of physical objects) have been used for many centuries in medical and anatomical education. Recently, 3D printing techniques started also to emerge. Still, other medical physicalizations that rely on affordable and easy-to-find materials are limited, while smart strategies that take advantage of the optical properties of our physical world have not been thoroughly investigated. We propose the Anatomical Edutainer, a workflow to guide the easy, accessible, and affordable generation of physicalizations for tangible, interactive anatomical edutainment. The Anatomical Edutainer supports 2D printable and 3D foldable physicalizations that change their visual properties (i.e., hues of the visible spectrum) under colored lenses or colored lights, to reveal distinct anatomical structures through user interaction.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00007"}, {"primary_key": "2691474", "vector": [], "sparse_vector": [], "title": "GPU Parallel Computation of Morse-Smale Complexes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Morse-Smale complex is a well studied topological structure that represents the gradient flow behavior of a scalar function. It supports multi-scale topological analysis and visualization of large scientific data. Its computation poses significant algorithmic challenges when considering large scale data and increased feature complexity. Several parallel algorithms have been proposed towards the fast computation of the 3D Morse-Smale complex. The non-trivial structure of the saddle-saddle connections are not amenable to parallel computation. This paper describes a fine grained parallel method for computing the Morse-Smale complex that is implemented on a GPU. The saddle-saddle reachability is first determined via a transformation into a sequence of vector operations followed by the path traversal, which is achieved via a sequence of matrix operations. Computational experiments show that the method achieves up to 7 × speedup over current shared memory implementations.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00014"}, {"primary_key": "2691475", "vector": [], "sparse_vector": [], "title": "Narrative Transitions in Data Videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xi<PERSON>uan Shu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transitions are widely used in data videos to seamlessly connect data-driven charts or connect visualizations and non-data-driven motion graphics. To inform the transition designs in data videos, we conduct a content analysis based on more than 3500 clips extracted from 284 data videos. We annotate visualization types and transition designs on these segments, and examine how these transitions help make connections between contexts. We propose a taxonomy of transitions in data videos, where two transition categories are defined in building fluent narratives by using visual variables.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00037"}, {"primary_key": "2691478", "vector": [], "sparse_vector": [], "title": "FAVR - Accelerating Direct Volume Rendering for Virtual RealitySystems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, virtual reality (VR) has become readily available using robust and affordable head-mounted display (HMD) systems. Several VR-based scientific visualization solutions were proposed recently, but direct volume rendering (DVR) was considered only in a handful of VR-applications. We attribute this to the high computational demand of DVR and the limited rendering budget available for VR systems. For a heavily fragment-bound method such as DVR, it is challenging to achieve the very high update rates essential for VR. We propose an acceleration technique designed to take advantage of the specific characteristics of HMD systems. We utilize an adaptive rendering approach based on the lens distortion of HMDs and the visual perception of the human eye. Our implementation reduces the rendering cost of DVR while providing an experience indistinguishable to standard rendering techniques.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00028"}, {"primary_key": "2691479", "vector": [], "sparse_vector": [], "title": "Explainable Spatial Clustering: Leveraging Spatial Data in Radiation Oncology.", "authors": ["<PERSON>", "Guadalupe Canahuate", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Advances in data collection in radiation therapy have led to an abundance of opportunities for applying data mining and machine learning techniques to promote new data-driven insights. In light of these advances, supporting collaboration between machine learning experts and clinicians is important for facilitating better development and adoption of these models. Although many medical use-cases rely on spatial data, where understanding and visualizing the underlying structure of the data is important, little is known about the interpretability of spatial clustering results by clinical audiences. In this work, we reflect on the design of visualizations for explaining novel approaches to clustering complex anatomical data from head and neck cancer patients. These visualizations were developed, through participatory design, for clinical audiences during a multi-year collaboration with radiation oncologists and statisticians. We distill this collaboration into a set of lessons learned for creating visual and explainable spatial clustering for clinical users.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00063"}, {"primary_key": "2691480", "vector": [], "sparse_vector": [], "title": "Implicit <PERSON>ing of the Parallel Vectors Operator.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Feature extraction is an essential aspect of scientific data analysis, as it allows for a data reduction onto relevant structures. The extraction of such features from scalar and vector fields, however, can be computationally expensive and numerically challenging. In this paper, we concentrate on 3D line features in vector fields that are defined by the parallel vectors operator. Common examples are vortex corelines and hyperbolic trajectories, i.e., lines around which particles are rotating, or from which particles are repelled and attracted locally the strongest. In our work, we use a GPU volume rendering framework to calculate the lines on-the-fly via a parallel vectors implementation in the volume rendering kernels. We achieve real-time performance for the feature curve extraction, which enables interactive filtering and parameter adjustment.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00013"}, {"primary_key": "2691482", "vector": [], "sparse_vector": [], "title": "Facilitating Exploration with Interaction Snapshots under High Latency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Latency is, unfortunately, a reality when working with large data sets. Guaranteeing imperceptible latency for interactivity is often prohibitively expensive: the application developer may be forced to migrate data processing engines or deal with complex error bounds on samples, and to limit the application to users with high network bandwidth. Instead of relying on the backend, we propose a simple UX design-interaction snapshots. Responses of requests from the interactions are asynchronously loaded in \"snapshots\". With interaction snapshots, users can interact concurrently while the snapshots load. Our user study participants found it useful not to have to wait for each result and easily navigate to prior snapshots. For latency up to 5 seconds, participants were able to complete extrema, threshold, and trend identification tasks with little negative impact.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00034"}, {"primary_key": "2691484", "vector": [], "sparse_vector": [], "title": "Accelerating Force-Directed Graph Drawing with RT Cores.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph drawing with spring embedders employs a V ×V computation phase over the graph's vertex set to compute repulsive forces. Here, the efficacy of forces diminishes with distance: a vertex can effectively only influence other vertices in a certain radius around its position. Therefore, the algorithm lends itself to an implementation using search data structures to reduce the runtime complexity. NVIDIA RT cores implement hierarchical tree traversal in hard-ware. We show how to map the problem of finding graph layouts with force-directed methods to a ray tracing problem that can subsequently be implemented with dedicated ray tracing hardware. With that, we observe speedups of 4× to 13× over a CUDA software implementation.", "published": "2020-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS47514.2020.00026"}]