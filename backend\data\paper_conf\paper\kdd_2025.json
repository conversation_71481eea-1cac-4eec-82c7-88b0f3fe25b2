[{"primary_key": "186371", "vector": [], "sparse_vector": [], "title": "Towards Controllable Hybrid Fairness in Graph Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Graph Neural Networks (GNNs) have shown remarkable capabilities in mining graph-structured data. However, conventional GNNs often encounter various fairness issues, such as predictions with prejudices when dealing with nodes with different sensitive attributes like genders or races, or significantly different prediction performance when facing nodes with different degrees. Existing studies mainly focus on addressing one specific fairness issue, neglecting the fact that a GNN model may face multiple unfairness simultaneously in reality, and addressing only one specific fairness may still leave the GNNs in an unfair status. In this paper, we focus on achieving multiple fairness on GNNs simultaneously, which we call hybrid fairness. To achieve this objective, we propose a novel GNN framework called LibraGNN. Specifically, we adopt a multi-teacher knowledge distillation training framework that successfully unifies the learning paradigms for multiple fairness. To ensure LibraGNN strikes a better trade-off among different fairness, we transform the multi-teacher knowledge distillation into a multi-objective optimization problem and further employ Pareto efficiency for optimization guidance. Finally, a controllable preference vector is introduced to assist LibraGNN in modulating its capability towards various forms of fairness, thereby achieving controllable hybrid fairness. Extensive experiments on three real-world datasets demonstrate the effectiveness of LibraGNN on both hybrid fairness and utility.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709224"}, {"primary_key": "186373", "vector": [], "sparse_vector": [], "title": "Enhancing Unsupervised Graph Few-shot Learning via Set Functions and Optimal Transport.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph few-shot learning has garnered significant attention for its ability to rapidly adapt to downstream tasks with limited labeled data, sparking considerable interest among researchers. Recent advancements in graph few-shot learning models have exhibited superior performance across diverse applications. Despite their successes, several limitations still exist. First, existing models in the meta-training phase predominantly focus on instance-level features within tasks, neglecting crucial set-level features essential for distinguishing between different categories. Second, these models often utilize query sets directly on classifiers trained with support sets containing only a few labeled examples, overlooking potential distribution shifts between these sets and leading to suboptimal performance. Finally, previous models typically require necessitate abundant labeled data from base classes to extract transferable knowledge, which is typically infeasible in real-world scenarios. To address these issues, we propose a novel model named STAR, which leverages Set funcTions and optimAl tRansport for enhancing unsupervised graph few-shot learning. Specifically, STAR utilizes expressive set functions to obtain set-level features in an unsupervised manner and employs optimal transport principles to align the distributions of support and query sets, thereby mitigating distribution shift effects. Theoretical analysis demonstrates that STAR can capture more task-relevant information and enhance generalization capabilities. Empirically, extensive experiments across multiple datasets validate the effectiveness of STAR. Our code can be found here.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709208"}, {"primary_key": "186378", "vector": [], "sparse_vector": [], "title": "Embedding Prior Task-specific Knowledge into Language Models for Context-aware Document Ranking.", "authors": ["Shuting <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Exploiting users' contextual behaviors in the current session has been proven favorable to the document ranking task. Recently, the context-aware document ranking task has benefited from pre-trained language models (PLMs) due to their superior ability in language modeling. Most PLM-based context-aware document ranking models implicitly learn task-specific knowledge by fine-tuning PLMs on historical search logs. However, since search log data is noisy and contains various user intents and search patterns, such a black-box way may prevent models from fully mastering effective context-aware search knowledge. To solve this problem, we propose LOCK, a PLM-based context-aware document ranking model that explicitly embeds task-specific prior knowledge into PLMs to guide the model optimization. From local to global, we identify three types of task-specific knowledge, including intra-turn signals, inter-turn signals, and global session signals. LOCK formulates such prior knowledge into prior attention biases for impacting the fine-tuning of PLMs. This operation can guide the ranking model by task-specific prior knowledge, thereby improving model convergence and ranking ability. Additionally, we introduce a task-specific pre-training stage that involves masked language modeling and the soft reconstruction of the prior attention matrix, which helps the PLMs adapt to our task. Extensive experiments validate the effectiveness and convergence of our method.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709282"}, {"primary_key": "186379", "vector": [], "sparse_vector": [], "title": "Handling Feature Heterogeneity with Learnable Graph Patches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, the rapid development of foundation models and graph pre-training technologies has spurred increasing interest in constructing a universal pre-trained graph model or Graph Foundation Model (GFM). However, a significant challenge is that existing models are unable to address feature heterogeneity in graph data without textual information, which hinders the transferability of graph models across different datasets. To bridge this gap, we propose the concept of learnable graph patches, which we regard as the smallest semantic units of any graph data. We decompose the graph into learnable graph patches by unfolding the node features and constructing corresponding patch structures separately. We then design PatchNet, a framework that mines transferable information from graph data across domains. Specifically, after extracting graph patches, we propose a patch encoder to extract knowledge from each unit and a patch aggregator to learn how the units are combined into a whole. Due to its domain-agnostic nature, the model can be applied to downstream data across different domains. Furthermore, we analyze the connection between PatchNet and existing graph models, as well as the transferability of the node embeddings it generates. Empirically, our method not only achieves the capability to use multi-domain graphs for pre-training, but also shows enhanced performance across various downstream datasets and tasks. Moreover, we observe consistent improvement in downstream performance as the volume of pre-training data increases.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709242"}, {"primary_key": "186381", "vector": [], "sparse_vector": [], "title": "CoopRide: Cooperate All Grids in City-Scale Ride-Hailing Dispatching with Multi-Agent Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "Qianyue Hao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ride-hailing services offer convenient travel options in urban transportation. To improve passengers' experience and platforms' revenue, plentiful studies use multi-agent reinforcement learning (MARL) for efficient order dispatching, controlling each grid with one agent to balance the supply-demand (drivers-orders) distribution. However, despite the critical role of cooperation among grids for efficient dispatching strategies, existing works neglect it or limit it within neighboring grids. There exist three key challenges in scaling the cooperation to the whole city: (1) cooperative strategies cause complex interactions among grids, making the grids' states coupled and complicating the information extraction from the states for decision-making; (2) cooperation among grids requires both within- and cross-grid dispatching, where the priorities of these two types of actions are difficult to balance; (3) the value of cooperation is not only heterogeneous over different pairs of grids, but also varies temporally, adding difficulty to dynamically determine the intensities of cooperation for each pair of grids and obtain the global cooperation rewards. In this paper, we propose the CoopRide framework to solve the above challenges. We model the interactions among agents with graphs and utilize graph neural network (GNN) for efficient information extraction. We uniformly encode both within- and cross-grid dispatching, enabling flexible choice of both types of actions in the embedding space. We also design to automatically learn the cooperation intensities among grids, thereby obtaining the cooperative rewards to drive the learning of global cooperation actions. We conduct experiments in three real-world datasets with millions of orders, and extensive results demonstrate the superior performance of CoopRide, outperforming the state-of-the-art baselines by up to 12.4%. Our source codes are available at https://github.com/tsinghua-fib-lab/CoopRide.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709205"}, {"primary_key": "186382", "vector": [], "sparse_vector": [], "title": "A Deep Subgrouping Framework for Precision Drug Repurposing via Emulating Clinical Trials on Real-world Patient Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Drug repurposing identifies new therapeutic uses for existing drugs, reducing the time and costs compared to traditional de novo drug discovery. Most existing drug repurposing studies using real-world patient data often treat the entire population as homogeneous, ignoring the heterogeneity of treatment responses across patient subgroups. This approach may overlook promising drugs that benefit specific subgroups but lack notable treatment effects across the entire population, potentially limiting the number of repurposable candidates identified. To address this, we introduce STEDR, a novel drug repurposing framework that integrates subgroup analysis with treatment effect estimation. Our approach first identifies repurposing candidates by emulating multiple clinical trials on real-world patient data and then characterizes patient subgroups by learning subgroup-specific treatment effects. We deploy \\model to Alzheimer's Disease (AD), a condition with few approved drugs and known heterogeneity in treatment responses. We emulate trials for over one thousand medications on a large-scale real-world database covering over 8 million patients, identifying 14 drug candidates with beneficial effects to AD in characterized subgroups. Experiments demonstrate STEDR's superior capability in identifying repurposing candidates compared to existing approaches. Additionally, our method can characterize clinically relevant patient subgroups associated with important AD-related risk factors, paving the way for precision drug repurposing.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709418"}, {"primary_key": "186385", "vector": [], "sparse_vector": [], "title": "Fast and Accurate Temporal Hypergraph Representation for Hyperedge Prediction.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Temporal hypergraph representation learning is a concept that integrates high-order structure learning with temporal dynamics, enabling more accurate analysis of temporal and high-order interactions. To enhance model expressiveness, the latest work samples multi-hop hyperedge-centric neighbors directly from temporal hypergraphs and encodes them for high-order structure learning, achieving promising performance. Such modeling, however, incurs prohibitive computational complexity, which increases exponentially with model depth and quadratically with average hyperedge cardinality, thereby limiting model scalability. In this paper, we propose FastHeP, a fast and accurate approach for temporal hyperedge prediction, which can handle large temporal hypergraphs. The key idea is to minimize computational complexity while maintaining model expressiveness. Concretely, we design an online hyperedge-centric neighbor store, which can store time-aware and redundancy-aware neighbors for nodes with rational theoretical guarantees. Upon the neighbor store, we propose a novel hybrid message passing to model temporal high-order structures, theoretically preserving strong expressive power. This explicitly learns local high-order structures for nodes of each hyperedge via graph attention, generating the node-wise structure features. These structure features are then fused into global correlations modeling among hyperedges, with a theoretical guarantee of permutation invariance. Last, FastHeP leverages local and global high-order semantics to generate temporal hyperedge embeddings, which is efficient in a linear complexity w.r.t. model depth and average hyperedge cardinality. Extensive experiments show that FastHeP achieves up to two orders of magnitude speed-up against baselines, with an average accuracy improvement of 5.1%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709327"}, {"primary_key": "186387", "vector": [], "sparse_vector": [], "title": "Probabilistic Hypergraph Recurrent Neural Networks for Time-series Forecasting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dir<PERSON>"], "summary": "Leveraging graph structures for time-series forecasting has garnered significant attention due to their effective relationship modeling between nodes and their associated time-series. However, in scenarios entities communicate in a broadcasting manner, graph models fall short of pairwise modeling. Hypergraph models address this by capturing beyond-pairwise interactions among node time-series. Nevertheless, most hypergraph models overlook the dynamics between nodes and their incident hyperedges, assuming constant node-hyperedge connections. In this paper, we introduce a novel model, Probabilistic Hypergraph Recurrent Neural Networks (PHRNN), which leverages node-hyperedge dynamics for accurate time-series forecasting. PHRNN associates each time-series with a node and models node interactions on a hypergraph, capturing beyond-pairwise interactions. Moreover, PHRNN learns a probabilistic hypergraph in which node-hyperedge relations are modeled as probabilistic distributions instead of fixed values, capturing dynamic node-hyperedge relations. PHRNN further integrates a prior knowledge KNN hypergraph as regularization when learning the probabilistic hypergraph structure. To the best of our knowledge, PHRNN is the first time-series forecasting model that incorporates hypergraph modeling and probabilistic relationship modeling. Forecasting results from extensive experiments show that PHRNN outperforms state-of-the-art graph and hypergraph baselines on real-world datasets.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709202"}, {"primary_key": "186388", "vector": [], "sparse_vector": [], "title": "Harnessing Scale and Physics: A Multi-Graph Neural Operator Framework for PDEs on Arbitrary Geometries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Partial Differential Equations (PDEs) underpin many scientific phenomena, yet traditional computational approaches often struggle with complex, nonlinear systems and irregular geometries.This paper introduces the AMG method, a Multi-Graph neural operator approach designed for efficiently solving PDEs on Arbitrary geometries.AMG leverages advanced graph-based techniques and dynamic attention mechanisms within a novel GraphFormer architecture, enabling precise management of diverse spatial domains and complex data interdependencies.By constructing multi-scale graphs to handle variable feature frequencies and a physics graph to encapsulate inherent physical properties, AMG significantly outperforms previous methods, which are typically limited to uniform grids.We present a comprehensive evaluation of AMG across six benchmarks, demonstrating its consistent superiority over existing state-of-the-art models.Our findings highlight the transformative potential of tailored graph neural operators in surmounting the challenges faced by conventional PDE solvers.Our code and datasets are available on https://github.com/lizhihao2022/AMG.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709173"}, {"primary_key": "186389", "vector": [], "sparse_vector": [], "title": "Conditional Generative Modeling for High-dimensional Marked Temporal Point Processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Point processes offer a versatile framework for sequential event modeling. However, the computational challenges and constrained representational power of the existing point process models have impeded their potential for wider applications. This limitation becomes especially pronounced when dealing with event data that is associated with multi-dimensional or high-dimensional marks such as texts or images. To address this challenge, this study proposes a novel event-generation framework for modeling point processes with high-dimensional marks. We aim to capture the distribution of events without explicitly specifying the conditional intensity or probability density function. Instead, we use a conditional generator that takes the history of events as input and generates the high-quality subsequent event that is likely to occur given the prior observations. The proposed framework offers a host of benefits, including considerable representational power to capture intricate dynamics in multi- or even high-dimensional event space, as well as exceptional efficiency in learning the model and generating samples. Our numerical results demonstrate superior performance compared to other state-of-the-art baselines.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709258"}, {"primary_key": "186391", "vector": [], "sparse_vector": [], "title": "Towards Context-Aware Traffic Classification via Time-Wavelet Fusion Network.", "authors": ["Zim<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tingting Li"], "summary": "Encrypted traffic classification occupies a significant role in cybersecurity and network management. The existing encrypted traffic classification technology mostly relies on intra-flow semantics for extracting features. However, considering that some attack behaviors inherently have similar patterns to legitimate behaviors, and powerful adversaries could simulate benign users to conceal their attack intentions, intra-flow features may be similar between different categories. In this paper, we propose TrafficScope, a time-wavelet fusion network based on Transformer to enhance the performance of encrypted traffic classification. Specifically, in addition to using intra-flow semantics, TrafficScope also extracts contextual information to construct more comprehensive representations. Moreover, to cope with the non-stationary and dynamic contextual traffic, we employ wavelet transform to extract invariant features. For feature fusion, the cross-attention mechanism is adopted to inline combine temporal and wavelet-domain features. We extensively evaluate TrafficScope compared with 7 state-of-the-art baselines based on four groups of real-world traffic datasets, the results show that TrafficScope outperforms existing methods. We conduct a series of experiments in terms of similar intra-flow feature evaluation, data pollution, flow manipulations, and dynamic context to demonstrate the robustness and stability of the proposed method. Furthermore, we produce additional experiments to present the potential of TrafficScope in cross-dataset scenarios.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709315"}, {"primary_key": "186392", "vector": [], "sparse_vector": [], "title": "Incremental Label Distribution Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hong <PERSON>", "<PERSON><PERSON>"], "summary": "Label distribution learning (LDL) has large practical application potentials due to its superiority in dealing with ambiguous label information. Most existing LDL methods are designed in a closed environment, wherein all the elements, e.g., feature and label space, are fixed. Nevertheless, in reality, data are dynamically acquired in the open environment, wherein the feature space can accumulate over time and the label space can be further enriched and refined accordingly with the accumulated feature space. Conducting LDL for such simultaneous augmentation of feature and label is crucial but rarely studied, particularly when the labeled samples with full observations are limited. In this paper, we propose a novel Incremental Label Distribution Learning (ILDL) method to tackle this brand new LDL problem by continuously transiting discriminative information from the previous model to the current one. Concretely, a prior compensation regularization is designed for such discriminative information transitivity. In this manner, the current model has the capacity to reuse the previous model to guide its own training. Furthermore, we present the theoretical analyses about the generalization bound, which provides guarantees for model inheritance. Comprehensive experimental studies validate the effectiveness of our proposal.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709318"}, {"primary_key": "186393", "vector": [], "sparse_vector": [], "title": "IN-Flow: Instance Normalization Flow for Non-stationary Time Series Forecasting.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the non-stationarity of time series, the distribution shift problem largely hinders the performance of time series forecasting. Existing solutions either rely on using certain statistics to specify the shift, or developing specific mechanisms for certain network architectures. However, the former would fail for the unknown shift beyond simple statistics, while the latter has limited compatibility on different forecasting models. To overcome these problems, we first propose a decoupled formulation for time series forecasting, with no reliance on fixed statistics and no restriction on forecasting architectures. This formulation regards the removing-shift procedure as a special transformation between a raw distribution and a desired target distribution and separates it from the forecasting. Such a formulation is further formalized into a bi-level optimization problem, to enable the joint learning of the transformation (outer loop) and forecasting (inner loop). Moreover, the special requirements of expressiveness and bi-direction for the transformation motivate us to propose instance normalization flow (IN-Flow), a novel invertible network for time series transformation. Different from the classic ''normalizing flow'' models, IN-Flow does not aim for normalizing input to the prior distribution (e.g., Gaussian distribution) for generation, but creatively transforms time series distribution by stacking normalization layers and flow-based invertible networks, which is thus named ''normalization'' flow. Finally, we have conducted extensive experiments on both synthetic data and real-world data, which demonstrate the superiority of our method.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709260"}, {"primary_key": "186395", "vector": [], "sparse_vector": [], "title": "DimCL: Dimension-Aware Augmentation in Contrastive Learning for Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiangyu Zhao", "<PERSON><PERSON>"], "summary": "Contrastive learning (CL) has achieved remarkable success in addressing data sparsity issues in collaborative filtering (CF) for recommender systems (RSs). The key principle is to generate different augmented views given a user-item interaction graph. However, prior endeavors mainly focus on performing augmentation via stochastic functions, e.g., by injecting perturbations into different hidden dimensions uniformly. Without fine control, the hidden representations of augmentations may contain noisy dimensions that are harmful to CL and irrelevant to RSs. Removing dimension-specific noise is a challenging task due to the following two major bottlenecks. It is difficult to (i) distinguish different dimensions' efficacy for CL and (ii) bridge the semantic gap between CL and RSs. Overlooking these limitations may cause redundant, false-positive, and irrelevant noise in hidden dimensions of the augmented views. In this paper, we solve the above challenges from the perspective of robust learning and curriculum learning, and propose a novel Dimension-aware augmentation in Ceontrastive Leearning for recommendation (DimCL). In DimCL, we first theoretically analyze the easiness and hardness of different dimensions for CL and RSs. With thorough analysis, we propose two propositions, which reveal that the gradients of different dimensions of augmented views are potentially related to the learning difficulty of optimizing CL and RSs. The comparison of gradients can provide detectable signals to reflect the efficacy of different dimensions for CL and the semantic gap between CL and RSs. Based on the analysis results, we devise three denoising factors, which can help DimCL to identify hard-to-learn dimensions as redundant or false-positive noise and pinpoint dimensions in different augmented views with inconsistent difficulties of RSs as irrelevant noise without requiring additional supervised labels. After denoising, DimCL can remove dimension-level noise to reduce unnecessary difficulty, making CL easier and maintaining more consistent difficulty in RSs. Extensive experiments on four public datasets demonstrate DimCL's superiority and flexible applications over various traditional and CL-based CF methods. The source code is publicly available online at https://github.com/zc-97/DimCL.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709200"}, {"primary_key": "186396", "vector": [], "sparse_vector": [], "title": "ResMoE: Space-efficient Compression of Mixture of Experts LLMs via Residual Restoration.", "authors": ["Mengting Ai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tang", "Hanghang Tong", "<PERSON><PERSON><PERSON>"], "summary": "Mixture-of-Experts (MoE) Transformer, the backbone architecture of multiple phenomenal language models, leverages sparsity by activating only a fraction of model parameters for each input token. The sparse structure, while allowing constant time costs, results in space inefficiency: we still need to load all the model parameters during inference. We introduce ResMoE, an innovative MoE approximation framework that utilizes <PERSON><PERSON>stein barycenter to extract a common expert (barycenter expert) and approximate the residuals between this barycenter expert and the original ones. ResMoE enhances the space efficiency for inference of large-scale MoE Transformers in a one-shot and data-agnostic manner without retraining while maintaining minimal accuracy loss, thereby paving the way for broader accessibility to large language models. We demonstrate the effectiveness of ResMoE through extensive experiments on Switch Transformer, Mixtral, and DeepSeekMoE models. The results show that ResMoE can reduce the number of parameters in an expert by up to 75% while maintaining comparable performance. The code is available at https://github.com/iDEA-iSAIL-Lab-UIUC/ResMoE, and the supplementary appendix is available at https://famous-blue-raincoat.github.io/mengtingai/files/ResMoE_Appendix.pdf.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709196"}, {"primary_key": "186397", "vector": [], "sparse_vector": [], "title": "Hypergraph Motif Representation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Valerio <PERSON> Pasquale", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hypergraphs have emerged as a powerful tool for representing high-order connections in real-world complex systems. Similar to graphs, local structural patterns in hypergraphs, known as high-order motifs (h-motifs), play a crucial role in network dynamics and serve as fundamental building blocks across various domains. For this reason, predicting h-motifs can be highly beneficial in different fields. In this paper, we aim to advance our understanding of such complex high-order dynamics by introducing and formalizing the problem of h-motifs prediction. To address this task, we propose a novel solution that leverages both high-order and pairwise information by combining hypergraph and graph convolutions to capture hyperedges correlation within h-motifs, along with an innovative negative sampling approach designed to generate close-to-positive negative samples. To evaluate the effectiveness of our approach, we defined several baselines inspired by existing literature on hyperedge prediction methods. Our extensive experimental assessments demonstrate that our approach consistently outperforms all the considered baselines, showcasing its superior performance and robustness in predicting h-motifs.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709274"}, {"primary_key": "186398", "vector": [], "sparse_vector": [], "title": "Chainlet Orbits: Topological Address Embedding for Blockchain.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Cuneyt Gurcan Akcora"], "summary": "The rise of cryptocurrencies like Bitcoin has not only increased trade volumes but also broadened the use of graph machine learning techniques, such as address embeddings, to analyze transactions and decipher user patterns. Traditional analysis methods rely on simple heuristics and extensive data gathering, while more advanced Graph Neural Networks encounter challenges such as scalability, poor interpretability, and label scarcity in massive blockchain transaction networks. To overcome existing techniques' computational and interpretability limitations, we introduce a topological approach, Chainlet Orbits, which embeds blockchain addresses by leveraging their topological characteristics in temporal transactions. We employ our innovative address embeddings to investigate financial behavior and e-crime in the Bitcoin and Ethereum networks, focusing on distinctive substructures that arise from user behavior. Our model demonstrates exceptional performance in node classification experiments compared to GNN-based approaches. Furthermore, our approach embeds all daily nodes of the largest blockchain transaction network, Bitcoin, and creates explainable machine learning models in less than 17 minutes which takes days for GNN-based approaches.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709322"}, {"primary_key": "186400", "vector": [], "sparse_vector": [], "title": "Fast and Effective GNN Training through Sequences of Random Path Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present GERN, a novel scalable framework for training GNNs in node classification tasks, based on effective resistance, a standard tool in spectral graph theory. Our method progressively refines the GNN weights on a sequence of random spanning trees suitably transformed into path graphs which, despite their simplicity, are shown to retain essential topological and node information of the original input graph. The sparse nature of these path graphs substantially lightens the computational burden of GNN training. This not only enhances scalability but also improves accuracy in subsequent test phases, especially under small training set regimes, which are of great practical importance, as in many real-world scenarios labels may be hard to obtain. In these settings, our framework yields very good results as it effectively counters the training deterioration caused by overfitting when the training set is small. Our method also addresses common issues like over-squashing and over-smoothing while avoiding under-reaching phenomena. Although our framework is flexible and can be deployed in several types of GNNs, in this paper we focus on graph convolutional networks and carry out an extensive experimental investigation on a number of real-world graph benchmarks, where we achieve simultaneous improvement of training speed and test accuracy over a wide pool of representative baselines.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709301"}, {"primary_key": "186401", "vector": [], "sparse_vector": [], "title": "HyperZero: A Customized End-to-End Auto-Tuning System for Recommendation with Hourly Feedback.", "authors": ["<PERSON>feng Cai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wenbo Ren", "Renkai Xiang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern recommendation systems can be broadly divided into two key stages: the ranking stage, where the system predicts various user engagements (e.g., click-through rate, like rate, follow rate, watch time), and the value model stage, which aggregates these predictive scores through a function (e.g., a linear combination defined by a weight vector) to measure the value of each content by a single numerical score. Both stages play roughly equally important roles in real industrial systems; however, how to optimize the model weights for the second stage still lacks systematic study. This paper focuses on optimizing the second stage through auto-tuning technology. Although general auto-tuning systems and solutions - both from established production practices and open-source solutions - can address this problem, they typically require weeks or even months to identify a feasible solution. Such prolonged tuning processes are unacceptable in production environments for recommendation systems, as suboptimal value models can severely degrade user experience. An effective auto-tuning solution is required to identify a viable model within 2-3 days, rather than the extended timelines typically associated with existing approaches. In this paper, we introduce a practical auto-tuning system named HyperZero that addresses these time constraints while effectively solving the unique challenges inherent in modern recommendation systems. Moreover, this framework has the potential to be expanded to broader tuning tasks within recommendation systems.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709409"}, {"primary_key": "186402", "vector": [], "sparse_vector": [], "title": "How to use Graph Data in the Wild to Help Graph Anomaly Detection?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, graph anomaly detection has gained considerable attention and has found extensive applications in various domains such as social, financial, and communication networks. However, anomalies in graph-structured data present unique challenges, including label scarcity, ill-defined anomalies, and varying anomaly types, making supervised or semi-supervised methods unreliable. Researchers often adopt unsupervised approaches to address these challenges, assuming that anomalies deviate significantly from the normal data distribution. Yet, when the available data is insufficient, capturing the normal distribution accurately and comprehensively becomes difficult. To overcome this limitation, we propose to utilize external graph data (i.e., graph data in the wild) to help anomaly detection tasks. This naturally raises the question: How can we use external data to help graph anomaly detection task? To answer this question, we propose a novel framework Wild-GAD. Our framework is built upon a unified database, UniWildGraph, which comprises a large and diverse collection of graph data with broad domain coverage, ample data volume, and a unified feature space. We further develop selection criteria based on representativity and diversity to identify the most suitable external data for each anomaly detection task. Extensive experiments on six real-world test datasets demonstrate the effectiveness of Wild-GAD. Compared to the baseline methods, our framework has an average 18% AUCROC and 32% AUCPR improvement over the best-competing methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709320"}, {"primary_key": "186403", "vector": [], "sparse_vector": [], "title": "Safe Online Bid Optimization with Return on Investment and Budget Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In online marketing, the advertisers aim to balance achieving high volumes and high profitability. The companies' business units address this tradeoff by maximizing the volumes while guaranteeing a minimum Return On Investment (ROI) level. Such a task can be naturally modeled as a combinatorial optimization problem subject to ROI and budget constraints that can be solved online. In this picture, the learner's uncertainty over the constraints' parameters plays a crucial role since the algorithms' exploration choices might lead to their violation during the entire learning process. Such violations represent a major obstacle to adopting online techniques in real-world applications. Thus, controlling the algorithms' exploration during learning is paramount to making humans trust online learning tools. This paper studies the nature of both optimization and learning problems. In particular, we show that the learning problem is inapproximable within any factor (unless P = NP) and provide a pseudo-polynomial-time algorithm to solve its discretized version. Subsequently, we prove that no online learning algorithm can violate the (ROI or budget) constraints a sublinear number of times during the learning process while guaranteeing a sublinear regret. We provide the GCB algorithm that guarantees sublinear regret at the cost of a linear number of constraint violations and GCBsafe that guarantees w.h.p.a constant upper bound on the number of constraint violations at the cost of a linear regret. Moreover, we designed GCBsafe(ψ, φ), which guarantees both sublinear regret and safety w.h.p. at the cost of accepting tolerances ψ and φ in the satisfaction of the ROI and budget constraints, respectively. Finally, we provide experimental results to compare the regret and constraint violations of GCB, GCBsafe, and GCBsafe(ψ, φ).", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709288"}, {"primary_key": "186404", "vector": [], "sparse_vector": [], "title": "Locally Balancing Signed Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Signed graphs capture both positive and negative relationships between entities, with balance being a fundamental concept. In these graphs, a vertex is considered balanced if all cycles it belongs to contain an even number of negative edges. On the other hand, unbalanced vertices often experience cognitive dissonance and emotional disturbance, motivating efforts to modify the graph to achieve balance for these vertices. Yet, most existing research emphasizes global balance, focusing on lengthy cycles that represent distant interactions. In contrast, this paper shifts the focus to local balance, where a vertex is deemed balanced when the triangles (length-three cycles) it participates in are positive, reflecting more immediate relationships. Building on this, we introduce the Locally Balancing Signed Graph (LBS) problem, which aims to maximize the number of locally balanced vertices through graph modification. Despite the NP-hard nature of the LBS problem and the absence of properties such as monotonicity and submodularity, our novel greedy method effectively addresses these challenges. We further enhance our method with dynamic computation and pruning techniques. Extensive experiments show the efficacy of our greedy method in solving the LBS problem and underscore the substantial runtime reductions achieved through our optimization techniques.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709342"}, {"primary_key": "186406", "vector": [], "sparse_vector": [], "title": "Advancing Confidence Calibration and Quantification in Medication Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Medication recommendation (MR) has undergone rapid advancement in recent years, driven by its significant practical implications in healthcare. However, such high-risk scenarios still experience two critical yet overlooked challenges: the prevalent overconfidence in raw confidence for individual medications and the lack of a robust solution for confidence quantification in medication combinations. This paper represents the first in-depth study addressing this gap. We introduce two innovative methodologies tailored to the unique challenges of MR scenarios: 1) A discernible binning-based calibration method with theoretical guarantees for the confidence of individual medication. It guarantees distinct accuracy levels between adjacent bins and maintains consistent statistical reliability across calibration and test data, enabling calibrated confidence to reflect the correctness of medication recommendations distinctively. 2) A sample-based quantification method for the set confidence of medication combination, which is applicable for various existing performance metrics in MR. Utilizing representative deep MR models as backbones and conducting extensive experiments on the widely recognized MIMIC datasets, we empirically prove the effectiveness and robustness of our proposed methods. Our approaches not only improve the reliability of MR but also pave the way for more informed decision-making in clinical settings.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709232"}, {"primary_key": "186407", "vector": [], "sparse_vector": [], "title": "NodeImport: Imbalanced Node Classification with Node Importance Assessment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In real-world applications, node classification on graphs often faces the challenge of class imbalance, where majority classes dominate training, resulting in biased model performance. Traditional Graph Neural Networks (GNNs) often struggle in such scenarios, as they tend to overfit to majority classes while underrepresenting minority classes. Existing solutions, which either prioritize nodes based on class size or synthesize new nodes for minority classes, often fall short of effectively addressing this imbalance issue. This paper introduces a novel approach to class-imbalanced node classification by utilizing a balanced meta-set for importance measurement, where a training node is considered significant if it enhances model performance under an unbiased setting. Our method identifies important nodes that can counteract class imbalance and utilizes them for model training, allowing for fine-grained and dynamic node selection throughout the training process. We theoretically derive a formula to directly assess node importance, reducing computational overhead and providing an intuitive threshold for node selection. Guided by this metric, we develop a novel framework that filters valuable labeled, unlabeled, and synthetic nodes that enhance model performance in an unbiased context. A key advantage of this framework is its separation of the synthetic node generation process from the filtering process, ensuring compatibility with various node generation techniques. Furthermore, we introduce a strategy to construct a high-quality meta-set that closely approximates the overall feature distribution, ensuring robust representation of each class. We evaluate our framework, NodeImport, across multiple benchmark datasets using popular GNN architectures, demonstrating its superiority over state-of-the-art baselines. Our results highlight the flexibility and effectiveness of the framework in mitigating class imbalance, leading to improved node classification outcomes. The source code is available at https://github.com/NanChanNN/NodeImport.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709215"}, {"primary_key": "186408", "vector": [], "sparse_vector": [], "title": "Scalable Link Recommendation for Influence Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The rise of link recommendation systems in online social networks has sparked significant research interest in strategically adding links to enhance social influence. This paper delves into the influence maximization with augmentation (IMA) problem that aims to add k edges connecting seed nodes and ordinary nodes to boost the influence propagation of the given seed set. IMA is a monotone submodular maximization problem so that the greedy algorithm provides a (1-1/e-ε)-approximate solution, where ε is an error term caused by the intractable nature of influence spread computation. Previous work often utilizes an unbiased estimator that relies on the chosen edges for influence estimation, resulting in non-submodular estimate with respect to edge selection. To ensure the overall error being bounded by ε, such an estimator requires Θ(ε/k) multiplicative error for each estimation, incurring prohibitive overhead. Meanwhile, some other work approximates IMA via conventional influence maximization (IM) on an augmented graph by adding a new node for every edge candidate, leading to heavy extra sampling due to a significant increase in graph size. To address these challenges, we design a novel unbiased estimator on the original graph that is independent of the chosen edges by leveraging the tractability of one-hop influence computation. We show that the estimate via our estimator is submodular so that it enables the estimate of all k edges in a whole with a bounded estimation error of Θ(ε), saving O(k2) time compared to the chosen-edge-dependent estimator while retaining the same graph size. Moreover, we propose several techniques based on the properties of our estimator to further speed up the greedy selection. Putting it together, we develop a scalable algorithm for the IMA problem, namely ScaLIM. Finally, extensive experiments are conducted to validate the effectiveness and efficiency of our proposed approach, e.g., ScaLIM is faster than baselines by nearly two orders of magnitude.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709190"}, {"primary_key": "186409", "vector": [], "sparse_vector": [], "title": "Seeing the Unseen in Micro-Video Popularity Prediction: Self-Correlation Retrieval for Missing Modality Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Micro-video popularity prediction (MVPP) plays a crucial role in numerous real-world applications, including product marketing and recommendation systems. While existing methodologies predominantly assume complete modalities during multimodal learning, this assumption often fails to hold in practical scenarios due to various constraints, such as privacy concerns or data integrity issues. To address this limitation, we propose SCRAG, a novel Self-Correlation Retrieval-Augmented Generative framework designed to enhance missing-modality robustness in MVPP. SCRAG operates in a retrieval-guided generation manner that explores relevant knowledge to enhance the reconstruction of missing content, which consists of two primary components: (1) a self-correlation retriever and (2) a multimodal mixture-of-experts generator. It first acquires instances pertinent to the missing content through multimodal prompt alignment. Subsequently, the generator extracts contextual modal information from the retrieved context-rich instances. By learning the joint distribution of modalities, SCRAG effectively recovers missing content and addresses the modal heterogeneity challenge inherent in cross-modal generation approaches. Extensive experiments conducted on three real-world datasets demonstrate that SCRAG consistently outperforms state-of-the-art baselines, underscoring its effectiveness in handling incomplete modalities and improving the accuracy of micro-video popularity prediction.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709308"}, {"primary_key": "186411", "vector": [], "sparse_vector": [], "title": "Mixing Time Matters: Accelerating Effective Resistance Estimation via Bidirectional Method.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of efficiently approximating the effective resistance (ER) on undirected graphs, where ER is a widely used node proximity measure with applications in graph spectral sparsification, multi-class graph clustering, network robustness analysis, graph machine learning, and more. Specifically, given any nodes s and t in an undirected graph G, we aim to efficiently estimate the ER value R(s,t) between nodes s and t, ensuring a small absolute error ε. The previous best algorithm for this problem has a worst-case computational complexity of Õ(Lmax3/ε2d2), where the value of Lmax depends on the mixing time of random walks on G, d = mind(s), d(t), and d(s), d(t) denote the degrees of nodes s and t, respectively. We improve this complexity to Õ ( min Lmax7/3/ε2/3, Lmax3/ε2d2,mLmax ), achieving a theoretical improvement of Õ (maxLmax2/3/ε4/3d2,1, Lmax2/ε2d2m2) over previous results. Here, m denotes the number of edges. Given that Lmax is often very large in real-world networks (e.g., Lmax > 104), our improvement on Lmax is significant, especially for real-world networks. We also conduct extensive experiments on real-world and synthetic graph datasets to empirically demonstrate the superiority of our method. The experimental results show that our method achieves a 10× to 1000× speedup in running time while maintaining the same absolute error compared to baseline methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709298"}, {"primary_key": "186412", "vector": [], "sparse_vector": [], "title": "Fair Set Cover.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Abolfazl Asudeh"], "summary": "The potential harms of algorithmic decisions have ignited algorithmic fairness as a central topic in computer science.One of the fundamental problems in computer science is Set Cover, which has numerous applications with societal impacts, such as assembling a small team of individuals that collectively satisfy a range of expertise requirements.However, despite its broad application spectrum and significant potential impact, set cover has yet to be studied through the lens of fairness.Therefore, in this paper, we introduce Fair Set Cover, which aims not only to cover with a minimum-size set but also to satisfy demographic parity in its selection of sets.To this end, we develop multiple versions of fair set cover, study their hardness, and devise efficient approximation algorithms for each variant.Notably, under certain assumptions, our algorithms always guarantee zerounfairness, with only a small increase in the approximation ratio compared to regular set cover.Furthermore, our experiments on various data sets and across different settings confirm the negligible price of fairness, as (a) the output size increases only slightly (if any) and (b) the time to compute the output does not significantly increase. CCS Concepts• Theory of computation → Packing and covering problems.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709184"}, {"primary_key": "186415", "vector": [], "sparse_vector": [], "title": "Stabilizing Modality Gap &amp; Lowering Gradient Norms Improve Zero-Shot Adversarial Robustness of VLMs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xinghua Qu", "Yew-<PERSON>g"], "summary": "Contemporary Vision-Language Models (VLMs) such as CLIP offer an attractive zero-shot classification functionality facilitated by large-scale vision-language pre-training. However, they remain vulnerable to adversarial attacks, a critical security threat in realistic deployment. Adversarially robust fine-tuning provides generalizable robustness on new datasets while preserving natural performance by fine-tuning the pre-trained models. Fine-tuning robust CLIP typically relies on adversaries generated solely from the vision branch. However, this singular focus on the vision modality, coupled with static text prompts used as fixed category prototypes, limits the robustness achieved through dual-modality fine-tuning. We observe for CLIP fine-tuning that zero-shot adversarial robustness improves when we (i) stabilize the modality gap (a phenomenon where image and text features occupy different feature space regions) and (ii) lower/stabilize gradient norms. Both these steps enjoy further improvement of robustness if one fine-tunes with both visual and text adversaries. For both modalities, we leverage (i) the maximization of an effective rank of features and (ii) noise modulation of features. We show that maximizing the effective rank helps lower and stabilize the modality gap over adversaries with varying perturbation radii. The noise modulation of features, achieved by the so-called count sketching, lowers/stabilizes gradient norms. We outperform the state of the art on 15 datasets. We provide the first insights into the effects of modality gap & gradient norms in VLM fine-tuning.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709296"}, {"primary_key": "186416", "vector": [], "sparse_vector": [], "title": "Bi-Dynamic Graph ODE for Opinion Evolution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jinghua Piao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modeling opinion dynamics in social networks has been the focus of multiple disciplines in recent decades. Previous studies have often modeled the opinion dynamics as a discrete and homogeneous process, neglecting its continuous and complex nature. To fill this gap, we propose a Bi-Dynamics Graph Ordinary Differential Equation (BDG-ODE) framework, which models complex opinion dynamics as the result of two dynamical processes: the evolution of positive and negative opinions. The proposed model incorporates a dual opinion encoder that processes positive and negative opinions independently. Furthermore, the temporal opinion evolution is modeled through bidirectional graph ordinary differential equations, which allows the model to capture the changes in opinion in continuous time. We introduce an opinion synthesis decoder that effectively maps the evolved representations from the latent space back to the opinion space. Extensive experiments conducted on six datasets with varying characteristics highlight the superiority of BDG-ODE in forecasting opinion evolution within social networks. It achieved an average accuracy improvement of 23.16%, an average enhancement of 29.46% in the F1 score, and an average mean square error of difference improvement of 90. 30%, and an average correlation coefficient improvement of 45.93%, significantly outperforming eight state-of-the-art models. The code for reproduction is available: https://github.com/tsinghua-fib-lab/Bi-Dynamic-Graph-ODE-for-Opinion-Evolution.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709297"}, {"primary_key": "186417", "vector": [], "sparse_vector": [], "title": "The k-Trine Cohesive Subgraph and Its Efficient Algorithms.", "authors": ["<PERSON><PERSON>", "Haicheng Guo", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce and study a novel cohesive subgraph model, named k-trine, to address the defects in the classical k-core and k-truss models. Our analysis shows that the k-trine is a more feasible model for capturing cohesive subgraphs by containing the strongly connected vertices. We analyze the theoretical properties of k-trine and propose efficient algorithms to compute the k-trine. Particularly, we design batch processing algorithms to update the decomposition of k-trine against highly dynamic graphs. Extensive experiments on real-world networks validate the effectiveness of the k-trine model and the efficiency of our algorithms.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709174"}, {"primary_key": "186419", "vector": [], "sparse_vector": [], "title": "ForTune: Running Offline Scenarios to Estimate Impact on Business Metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Making ideal decisions as a product leader in a web-facing company is incredibly challenging. Beyond navigating the ambiguity of customer satisfaction and achieving business goals, leaders must also ensure their products and services remain relevant, desirable, and profitable. Data and experimentation are crucial for testing product hypotheses and informing decisions. Online controlled experiments, such as A/B testing, can provide highly reliable data to support decisions. However, these experiments can be time-consuming and costly, particularly when assessing impacts on key business metrics like retention or long-term value. Offline experimentation allows for rapid iteration and testing but often lacks the same level of confidence and clarity regarding business metrics impact. To address this, we introduce a novel, lightweight, and flexible approach called scenario analysis. This method aims to support product leaders' decisions by using user data and estimates of business metrics. While it cannot fully replace online experiments, it offers valuable insights into trade-offs involved in growth or consumption shifts, estimates trends in long-term outcomes like retention, and can generate hypotheses about relationships between metrics at scale. We implemented scenario analysis in a tool named ForTune. We conducted experiments with this tool using a publicly available dataset and reported the results of experiments carried out by Spotify, a large audio streaming service, using ForTune in production. In both cases, the tool reasonably predicted the outcomes of controlled experiments, provided that features were carefully chosen. We demonstrate how this method was used to make strategic decisions regarding the impact of prioritizing one type of content over another at Spotify.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709431"}, {"primary_key": "186420", "vector": [], "sparse_vector": [], "title": "Experimenting, Fast and Slow: Bayesian Optimization of Long-term Outcomes with Online Experiments.", "authors": ["Qing Feng", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online experiments in internet systems, also known as A/B tests, are used for a wide range of system tuning problems, such as optimizing recommender system ranking policies and learning adaptive streaming controllers. Decision-makers generally wish to optimize for long-term treatment effects of the system changes, which often requires running experiments for a long time as short-term measurements can be misleading due to non-stationarity in treatment effects over time. The sequential experimentation strategies---which typically involve several iterations---can be prohibitively long in such cases. We describe a novel approach that combines fast experiments (e.g., biased experiments run only for a few hours or days) and/or offline proxies (e.g., off-policy evaluation) with long-running, slow experiments to perform sequential, Bayesian optimization over large action spaces in a short amount of time.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709419"}, {"primary_key": "186422", "vector": [], "sparse_vector": [], "title": "Wedjat: Detecting Sophisticated Evasion Attacks via Real-time Causal Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Traffic encryption has been widely adopted to protect the confidentiality and integrity of Internet traffic. However, attackers can also abuse such mechanism to deliver malicious traffic. Particularly, existing methods detecting encrypted malicious traffic are not robust against evasion attacks that manipulate traffic to obfuscate traffic features. Robust detection against evasion attacks remains an open problem. To the end, we develop Wedjat, which utilizes a causal network to model benign packet interactions among relevant flows, such that it recognizes abnormal causality that represents malicious traffic and disrupted causality incurred by evasion attacks. We extensively evaluate Wedjat with millions of flows collected from a real-world enterprise. The experimental results demonstrate that Wedjat achieves an accuracy of 0.957 F1-score when detecting various advanced attacks. Notably, five sophisticated evasion attacks, which have successfully evaded all existing methods, are accurately detected by <PERSON><PERSON><PERSON><PERSON> with over 0.915 F1. It demonstrates that Wedjat achieves exceptional robustness against evasions. Meanwhile, Wed- jat maintains an outstanding detection latency, i.e., it can predict each packet in less than 0.125 seconds.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709218"}, {"primary_key": "186423", "vector": [], "sparse_vector": [], "title": "Denoising Programming Knowledge Tracing with a Code Graph-based Tuning Adaptor.", "authors": ["Wei<PERSON> Gao", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fangzhou Yao", "<PERSON>"], "summary": "Programming Knowledge Tracking (PKT) aims to dynamically diagnose learners' mastery levels of programming knowledge based on their coding activities, facilitating more effective and personalized programming education. However, current PKT studies primarily focus on the implicit relationship between code content and knowledge assessment, often overlooking two types of noise signals in long-term programming activities: unwanted signals from unrelated submissions and weak signals from minor modifications. This practical challenge significantly limits model performance and application. To address this issue, we propose Coda, a Code graph-based tuning adaptor designed to enhance existing PKT models by identifying and mitigating the impact of noise. Specifically, Coda first transforms the loose code sequences submitted by each learner into a compact code graph. By leveraging this code graph, unwanted signals can be identified from a semantic similarity perspective. We then apply a cluster-aware GCN to the code graph, which improves the discrimination of weak signals and enables their clustering for identification. Finally, a lightweight yet effective adaptor is incorporated into the PKT task through optimization with two noise feature-based constraints and a navigational regularization term, to correct knowledge states affected by noise. It is worth mentioning that the Coda framework is model-agnostic and can be adapted to most existing PKT solutions. Extensive experimental results on four real-world datasets demonstrate that Coda effectively performs the PKT task in the presence of noisy programming records, outperforming typical baselines.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709172"}, {"primary_key": "186424", "vector": [], "sparse_vector": [], "title": "FABind+: Enhancing Molecular Docking through Improved Pocket Prediction and Pose Generation.", "authors": ["Kaiyuan Gao", "Qizhi Pei", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Molecular docking is a pivotal process in drug discovery. While traditional techniques rely on extensive sampling and simulation governed by physical principles, deep learning has emerged as a promising alternative, offering improvements in both accuracy and efficiency. Building upon the foundational work of FABind, a model focused on speed and accuracy, we introduce FABind+, an enhanced iteration that significantly elevates the performance of its predecessor. We identify pocket prediction as a critical bottleneck in molecular docking and introduce an enhanced approach. In addition to the pocket prediction module, the docking module has also been upgraded with permutation loss and a more refined model design. These designs enable the regression-based FABind+ to surpass most of the generative models. In contrast, while sampling-based models often struggle with inefficiency, they excel in capturing a wide range of potential docking poses, leading to better overall performance. To bridge the gap between sampling and regression docking models, we incorporate a simple yet effective sampling technique coupled with a lightweight confidence model, transforming the regression-based FABind+ into a sampling version without requiring additional training. This involves the introduction of pocket clustering to capture multiple binding sites and dropout sampling for various conformations. The combination of a classification loss and a ranking loss enables the lightweight confidence model to select the most accurate prediction. Experimental results and analysis demonstrate that FABind+ (both the regression and sampling versions) not only significantly outperforms the original FABind, but also achieves competitive state-of-the-art performance. Our code is available at https://github.com/QizhiPei/FABind.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709253"}, {"primary_key": "186426", "vector": [], "sparse_vector": [], "title": "TEMPER: Capturing Consistent and Fluctuating TEMPoral User Behaviour for EtheReum Phishing Scam Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Phishing scams on the Ethereum network have become a serious threat, especially with the influx of new users into the cryptocurrency market. Current detection methods are mainly focused on long-term consistent transaction patterns with smooth temporal dynamics. However, these methods often struggle to differentiate between phishing and non-phishing users, whose behaviours may appear deceptively similar. Additionally, they face challenges such as network sparsity and data leakage, leading to significant performance limitations. To address these issues, we introduce TEMPER, a novel sequential learning framework designed to jointly capture the subtle distinctions between long- and short-term user behaviours and their correlations to provide more comprehensive insights. TEMPER effectively generates distinguishable user embeddings, enabling the accurate identification of phishing users. Unlike previous approaches, TEMPER mitigates data leakage through a novel sequential transaction sampling algorithm and addresses network sparsity with short-term temporal learning. Through extensive experimentation on three real-world Ethereum datasets, TEMPER demonstrates its efficacy by achieving a 3-4% improvement in the F1-Score compared to existing baseline models, representing a significant advancement in Ethereum phishing user detection.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709399"}, {"primary_key": "186427", "vector": [], "sparse_vector": [], "title": "Detecting Interpretable Subgroup Drifts.", "authors": ["Flavio Giobergia", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ability to detect and adapt to changes in data distributions is crucial to maintain the accuracy and reliability of machine learning models. Detection is generally approached by observing the drift of model performance from a global point of view. However, drifts occurring in (fine-grained) data subgroups may go unnoticed when monitoring global drift.We take a different perspective, and introduce methods for observing drift at the finer granularity of subgroups. Relevant data subgroups are identified during training and monitored efficiently throughout the model's life. Performance drifts in any subgroup are detected, quantified and characterized so as to provide an interpretable summary of the model behavior over time. Experimental results confirm that our subgroup-level drift analysis identifies drifts that do not show at the (coarser) global dataset level. The proposed approach provides a valuable tool for monitoring model performance in dynamic real-world applications, offering insights into the evolving nature of data and ultimately contributing to more robust and adaptive models.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709259"}, {"primary_key": "186428", "vector": [], "sparse_vector": [], "title": "Benchmarking Fraud Detectors on Private Graph Data.", "authors": ["<PERSON>", "G<PERSON><PERSON>", "<PERSON><PERSON> B<PERSON>", "<PERSON>"], "summary": "We introduce the novel problem of benchmarking fraud detectors on private graph-structured data. Currently, many types of fraud are managed in part by automated detection algorithms that operate over graphs. We consider the scenario where a data holder wishes to outsource development of fraud detectors to third parties (e.g., vendors or researchers). The third parties submit their fraud detectors to the data holder, who evaluates these algorithms on a private dataset and then publicly communicates the results. We propose a realistic privacy attack on this system that allows an adversary to de-anonymize individuals' data based only on the evaluation results. In simulations of a privacy-sensitive benchmark for facial recognition algorithms by the National Institute of Standards and Technology (NIST), our attack achieves near perfect accuracy in identifying whether individuals' data is present in a private dataset, with a True Positive Rate of 0.98 at a False Positive Rate of 0.00. We then study how to benchmark algorithms while satisfying a formal differential privacy (DP) guarantee. We empirically evaluate two classes of solutions: subsample-and-aggregate and DP synthetic graph data. We demonstrate through extensive experiments that current approaches fail to provide utility when guaranteeing DP. Our results indicate that the error arising from DP trades off between bias from distorting graph structure and variance from adding random noise. Current methods lie on different points along this bias-variance trade-off, but more complex methods tend to require high-variance noise addition, undermining utility.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709170"}, {"primary_key": "186430", "vector": [], "sparse_vector": [], "title": "Adaptive Domain Inference Attack with Concept Hierarchy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With increasingly deployed deep neural networks in sensitive application domains, such as healthcare and security, it's essential to understand what kind of sensitive information can be inferred from these models. Most known model-targeted attacks assume attackers have learned the application domain or training data distribution to ensure successful attacks. Can removing the domain information from model APIs protect models from these attacks? This paper studies this critical problem. Unfortunately, even with minimal knowledge, i.e., accessing the model as an unnamed function without leaking the meaning of input and output, the proposed adaptive domain inference attack (ADI) can still successfully estimate relevant subsets of training data. We show that the extracted relevant data can significantly improve, for instance, the performance of model-inversion attacks. Specifically, the ADI method utilizes the concept hierarchy extracted from the public and private datasets that the attacker can access and applies a novel algorithm to adaptively tune the likelihood of leaf concepts in the hierarchy showing up in the unseen training data. For comparison, we also designed a straightforward hypothesis-testing-based attack -- LDI. The ADI attack not only extracts partial training data at the concept level but also converges fastest and requires the fewest target-model accesses among all candidate methods. Our code is available at https://anonymous.4open.science/r/KDD-362D.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709332"}, {"primary_key": "186431", "vector": [], "sparse_vector": [], "title": "Revisiting Cognition in Neural Cognitive Diagnosis.", "authors": ["Hengnian Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dongdai Zhou"], "summary": "Cognitive diagnosis is a fundamental task in intelligent education, aiming to measure students' proficiency on knowledge concepts based on practice data. Traditional methods utilize a broadly-defined latent trait θ to represent knowledge proficiency with some cognitive factors like skill or ability. However, existing methods simplify this to a narrowly-defined latent trait θ, which focuses only on knowledge or treats these cognitive factors as implicit features inferred from data. They fail to explicitly model these cognitive factors, resulting in limited performance and interpretability. To this end, we revisit essence of cognition in Educational Psychology Theory and propose a novel Cognition-aware Cognitive Diagnosis (CCD) model, where we first introduce the Cognition factor as a bridge into the long-standing three-basic-factors (Student, Exercise, Knowledge concept) paradigm. CCD has two main parts: cognition representations and a two-stage diagnostic process. In the first part, we explicitly model cognitive process (CP) dimensions from <PERSON>'s Taxonomy of Educational Objectives, leading to two innovative concepts proposed: the student's Subjective Cognitive Ability (SCA) and the exercise's Objective Cognitive Attribute (OCA), derived by regulating the CP through S-K and E-K interactions, respectively. Then, the SCA and OCA are formed into a new cognition-aware latent trait θ. In the second part, we employ a basic interaction function and a slip and guess influence function, inputting our new θ, a continuous Q-matrix (generated by a siamese PLMs), and other features to obtain the ideal result, followed by feeding it into the slip and guess influence function to obtain the actual result. Extensive experiments on real-world datasets demonstrates the superior effectiveness and good interpretability.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709319"}, {"primary_key": "186432", "vector": [], "sparse_vector": [], "title": "Efficient Multi-Expert Tabular Language Model for Banking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Pre-training large Tabular Language Models (TaLMs) on tabular data has shown effectiveness for table understanding tasks. However, training proprietary large TaLMs on a company's private databases requires substantial computational resources. This paper presents an efficient multi-expert TaLM architecture and training method tailored for multi-domain databases and modest infrastructure. This architecture leverages a divide-and-conquer pretraining approach and a sparsely activated fine-tuning paradigm to reduce computation. Using this architecture, we pre-train and fine-tune a TaLM with 10 billion parameters on a banking database under simple computational infrastructures. We apply our TaLM to support various important banking applications, including risk assessment, information prediction, and profit assessment. Compared with previous baselines, our model achieves +29.3% in precision@0.6% on risk assessment and +16.5% in accuracy on information prediction, showing great effectiveness and profitability of our model. This model is successfully deployed in WeBank and now supports many real business scenarios.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709400"}, {"primary_key": "186435", "vector": [], "sparse_vector": [], "title": "UniGraph: Learning a Unified Cross-Domain Foundation Model for Text-Attributed Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Foundation models like ChatGPT and GPT-4 have revolutionized artificial intelligence, exhibiting remarkable abilities to generalize across a wide array of tasks and applications beyond their initial training objectives. However, graph learning has predominantly focused on single-graph models, tailored to specific tasks or datasets, lacking the ability to transfer learned knowledge to different domains. This limitation stems from the inherent complexity and diversity of graph structures, along with the different feature and label spaces specific to graph data. In this paper, we recognize text as an effective unifying medium and employ Text-Attributed Graphs (TAGs) to leverage this potential. We present our UniGraph framework, designed to learn a foundation model for TAGs, which is capable of generalizing to unseen graphs and tasks across diverse domains. Unlike single-graph models that use pre-computed node features of varying dimensions as input, our approach leverages textual features for unifying node representations, even for graphs such as molecular graphs that do not naturally have textual features. We propose a novel cascaded architecture of Language Models (LMs) and Graph Neural Networks (GNNs) as backbone networks. Additionally, we propose the first pre-training algorithm specifically designed for large-scale self-supervised learning on TAGs, based on Masked Graph Modeling. We introduce graph instruction tuning using Large Language Models (LLMs) to enable zero-shot prediction ability. Our comprehensive experiments across various graph learning tasks and domains demonstrate the model's effectiveness in self-supervised representation learning on unseen graphs, few-shot in-context transfer, and zero-shot transfer, even surpassing or matching the performance of GNNs that have undergone supervised training on target datasets.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709277"}, {"primary_key": "186436", "vector": [], "sparse_vector": [], "title": "D-Tracker: Modeling Interest Diffusion in Social Activity Tensor Data Streams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large quantities of social activity data, such as weekly web search volumes and the number of new infections with infectious diseases, reflect peoples' interests and activities. It is important to discover temporal patterns from such data and to forecast future activities accurately. However, modeling and forecasting social activity data streams is difficult because they are high-dimensional and composed of multiple time-varying dynamics such as trends, seasonality, and interest diffusion. In this paper, we propose D-Tracker, a method for continuously capturing time-varying temporal patterns within social activity tensor data streams and forecasting future activities. Our proposed method has the following properties: (a) Interpretable: it incorporates the partial differential equation into a tensor decomposition framework and captures time-varying temporal patterns such as trends, seasonality, and interest diffusion between locations in an interpretable manner; (b) Automatic: it has no hyperparameters and continuously models tensor data streams fully automatically; (c) Scalable: the computation time of D-Tracker is independent of the time series length. Experiments using web search volume data obtained from GoogleTrends, and COVID-19 infection data obtained from COVID-19 Open Data Repository show that our method can achieve higher forecasting accuracy in less computation time than existing methods while extracting the interest diffusion between locations. Our source code and datasets are available at https://github.com/Higashiguchi-Shingo/D-Tracker.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709192"}, {"primary_key": "186441", "vector": [], "sparse_vector": [], "title": "Learning Adaptive Reserve Price in Display Advertising.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-Time Bidding (RTB) is a trading mechanism that allocates advertising (ad) requests through online auctions. Participants in these auctions typically include an ad exchange (AdX) and several demand-side platforms (DSPs). When an RTB auction begins, the AdX first establishes the reserve price set by publishers as the starting bid, after which the DSPs bid to compete for potential ad impressions. The reserve price strategy is crucial to the ad revenue of publishers; however, due to the strategic and dynamic bidding behavior of DSPs, optimizing the reserve price presents a significant challenge. In this work, we report a novel adaptive reserve price strategy based on reinforcement learning (RL). In our scheme, value bucket identification is leveraged to estimate the intrinsic values of ad inventories. Following this estimation, specialized reward functions are utilized to generate informative reward signals for RL models. Furthermore, we study the issue of risk management on the publisher side and develop a risk-aware instantiation to model risk tendency, considering both empirical expert knowledge and real-time trading conditions. Extensive experiments using real-world datasets collected from operational environments have demonstrated the effectiveness of the proposed method.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709439"}, {"primary_key": "186442", "vector": [], "sparse_vector": [], "title": "DIPS: Optimal Dynamic Index for Poisson πps Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper addresses the Poisson πps sampling problem, a topic of significant academic interest in various domains and with practical data mining applications, such as influence maximization. The problem includes a set S of n elements, where each element v is assigned a weight w(v) reflecting its importance. The goal is to generate a random subset X of S, where each element v ∈ S is included in X independently with probability c ⋅ w(v)over ∑v ∈ S w(v), where 0 < c≤ 1 is a constant. The subsets must be independent across different queries. While the Poisson πps sampling problem can be reduced to the well-studied subset sampling problem, updates in Poisson πps sampling, such as adding a new element or removing an element, would cause the probabilities of all n elements to change in the corresponding subset sampling problem, making this approach impractical for dynamic scenarios. To address this, we propose a dynamic index specifically tailored for the Poisson πps sampling problem, supporting optimal expected O (1) query time and O (1) index update time, with an optimal O (n) space cost. Our solution involves recursively partitioning the set by weights and ultimately using table lookup. The core of our solution lies in addressing the challenges posed by weight explosion and correlations between elements. Empirical evaluations demonstrate that our approach achieves significant speedups in update time while maintaining consistently competitive query time compared to the subset-sampling-based methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709162"}, {"primary_key": "186443", "vector": [], "sparse_vector": [], "title": "On the Hyperparameter Loss Landscapes of Machine Learning Models: An Exploratory Study.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous efforts on hyperparameter optimization (HPO) of machine learning (ML) models predominately focus on algorithmic advances, yet little is known about the topography of the underlying hyperparameter (HP) loss landscape, which plays a fundamental role in governing the search process of HPO. While several works have conducted fitness landscape analysis (FLA) on various ML systems, they are limited to properties of isolated landscape without interrogating the potential structural similarities among landscapes induced on different scenarios. The exploration of such similarities can provide a novel perspective for understanding the mechanism behind modern HPO methods, but has been missing. In this paper, we mapped 1,500 HP loss landscapes of 6 representative ML models on 63 datasets across different fidelity levels, with 11M+ configurations. By conducting exploratory analysis on these landscapes with fine-grained visualizations and dedicated FLA metrics, we observed a similar landscape topography across a wide range of models, datasets, and fidelities, and shed light on the mechanism behind the success of several popular methods in HPO. The artifacts associated with this paper is available at https://github.com/COLA-Laboratory/GraphFLA.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709229"}, {"primary_key": "186444", "vector": [], "sparse_vector": [], "title": "Progressive Dependency Representation Learning for Stock Ranking in Uncertain Risk Contrasting.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The practice of ranking a list of stocks to facilitate investment decisions has garnered a lot of attention in the fintech field, aiming at minimizing investment risk while maximizing profitable returns. With recent developments in deep representation learning such as temporal/relational dependency, prior efforts either strive to explore the temporal dynamics behind distinct stocks or expect to expose the collaborative signals from predefined relations, resulting in promising achievements in stock ranking. However, owing to the profound or intricate fluctuations of stock markets, existing insights rarely consider the uncertain risks underlying the learning of dependency representation, which could bring a narrow perspective on how to perceive market laws and ultimately yield an unprofitable decision-making procedure. In this study, we introduce a novel Progressive Dependency representation learning solution with Uncertain risk contrasting (PDU), primarily seeking to progressively uncover multiple dependency dynamics from historical trading signals for stock ranking in addition to addressing the uncertain risks. Specifically, we devise a Progressive Dependency learning block (or PD) in PDU that can progressively capture the temporal and relational dependencies besides multi-term dependencies in the latent space, allowing a coupled exposure of diffusion impacts over historical trading. Furthermore, we introduce an uncertain risk contrasting mechanism in PDU by placing the PD block in a contrastive environment (i.e., certainty vs. uncertainty), aiming to stably enhance dependency learning in the latent space. The experimental results conducted on four real-world stock market datasets demonstrate the superiority of PDU over several baselines.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709189"}, {"primary_key": "186445", "vector": [], "sparse_vector": [], "title": "Path Complex Neural Networks for Sequential Process Activities Classification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Process mining aims to uncover, track, and enhance real-world workflows by deriving insights from event logs commonly found in modern information systems. With the growing focus on improving productivity within complex business operations, recent research has looked into developing process models to improve business performance metrics. As such, this study aims to enhance process mining from event logs by proposing a novel path-complex construction based on process mining sequential data and a path-complex-based message-passing mechanism for higher-order structural information. We adopt path-complex representations for event logs and their temporal connections developed from instance graphs. Representations are identified and optimised for 0-paths (events), 1-paths (two events in chronological order) and 2-paths (three consecutive events) to characterise intrinsic higher-order information among events. The proposed framework, Path Complex Neural Networks (PCNN), leverages the advantages of topological deep learning and obtains representations for higher-order complexes inductively. Additionally, we evaluated the results with four real-world benchmark datasets and found that PCNN outperforms existing models in analysing sequential and complex process data.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709193"}, {"primary_key": "186446", "vector": [], "sparse_vector": [], "title": "Partial Pre-Post Code Tree: A Memory-Efficient Tree Structure for Conjunctive Rule Mining.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "State-of-the-art rule mining algorithms rely on summarizing the training set into efficient data structures which allow to quickly answer arbitrary conjunctive queries about the data. The key limitation of such techniques is their memory consumption. Pre-post code trees (PPC-trees) which are the basis of several efficient association and classification rule mining algorithms, are only constructed as an intermediate representation and subsequently converted into a much more efficient N-lists structure. In this paper, we introduce partial pre-post code trees (P3C-trees), which are based on the idea that partial trees are iteratively constructed, and immediately converted into N-lists. This tight integration of these phases allows to avoid the memory bottleneck of a full PPC-tree construction, and thus enables these algorithms to tackle the memory scalability problem posed by large-scale datasets. Our experiments with big datasets confirm that the memory used by P3C-tree is orders of magnitude smaller than the memory consumed by PPC-tree, and the generated N-lists are also more effective than alternative structures such as Tidset or Diffset. Moreover, the N-list construction can also be considerably sped up with the P3C-tree structure.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709303"}, {"primary_key": "186447", "vector": [], "sparse_vector": [], "title": "Seeing the Unseen: Learning Basis Confounder Representations for Robust Traffic Prediction.", "authors": ["Jiahao Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traffic prediction is essential for intelligent transportation systems and urban computing. It aims to establish a relationship between historical traffic data X and future traffic states Y by employing various statistical or deep learning methods. However, the relations of X → Y are often influenced by external confounders that simultaneously affect both X and Y, such as weather, accidents, and holidays. Existing deep-learning traffic prediction models adopt the classic front-door and back-door adjustments to address the confounder issue. However, these methods have limitations in addressing continuous or undefined confounders, as they depend on predefined discrete values that are often impractical in complex, real-world scenarios. To overcome this challenge, we propose the Spatial-Temporal sElf-superVised confoundEr learning (STEVE) model. This model introduces a basis vector approach, creating a base confounder bank to represent any confounder as a linear combination of a group of basis vectors. It also incorporates self-supervised auxiliary tasks to enhance the expressive power of the base confounder bank. Afterward, a confounder-irrelevant relation decoupling module is adopted to separate the confounder effects from direct X → Y relations. Extensive experiments across four large-scale datasets validate our model's superior performance in handling spatial and temporal distribution shifts and underscore its adaptability to unseen confounders. Our model implementation is available at https://github.com/bigscity/STEVE_CODE.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709201"}, {"primary_key": "186448", "vector": [], "sparse_vector": [], "title": "Synthetic Survey Data Generation and Evaluation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Survey data are common and invaluable in social science research for understanding population processes and supporting policymaking and planning. Depending on the nature and scale, survey data sharing comes with privacy risks, and data collectors and agencies are constrained by disclosure permissions, limiting usage across research groups and institutes. Previous methods for synthetic data generation and deidentification may not entirely prevent information disclosures, or they may sacrifice data quality and granularity. Using a large-scale national voter file at both national and state levels, this paper introduces an end-to-end pipeline to streamline synthetic data generation and evaluation for survey researchers. This study selects four generative approaches based on different statistical assumptions: the regression-based Synthpop, the generative deep learning-based CTGAN and TVAE, and the large language model-based REaLDTabFormer, and compares them to the baseline synthetic minority oversampling technique (SMOTE). We consider three key dimensions of evaluation (utility, fidelity, and privacy) to highlight the strengths and weaknesses of each approach, and systematically evaluate across various datasets and training sizes. The results reveal that Synthpop is optimized for general utility (i.e., fidelity), while TVAE excels in downstream applications (i.e., target-specific utility) but compromises on general utility and potentially risks data overfitting. REaLDTabFormer demonstrates a balanced performance in both general and target-specific utility, whereas CTGAN offers the best privacy protection. We recommend that future researchers select a generative method by considering the trade-offs between performance across various evaluation dimensions, training size, data type, and computational infrastructure.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709421"}, {"primary_key": "186449", "vector": [], "sparse_vector": [], "title": "PipeRAG: Fast Retrieval-Augmented Generation via Adaptive Pipeline Parallelism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Retrieval-augmented generation (RAG) can enhance the generation quality of large language models (LLMs) by incorporating external token databases. However, retrievals from large databases can constitute a substantial portion of the overall generation time, particularly when retrievals are periodically performed to align the retrieved content with the latest states of generation. In this paper, we introduce PipeRAG, a novel algorithm-system co-design approach to reduce generation latency and enhance generation quality. PipeRAG integrates (1) pipeline parallelism to enable concurrent retrieval and generation processes, (2) flexible retrieval intervals to maximize the efficiency of pipeline parallelism, and (3) a performance model to automatically balance retrieval quality and latency based on the generation states and underlying hardware. Our evaluation shows that, by combining the three aforementioned methods, PipeRAG achieves up to 2.6× speedup in end-to-end generation latency while improving generation quality. These promising results showcase the effectiveness of co-designing algorithms with underlying systems, paving the way for the adoption of PipeRAG in future RAG systems.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709194"}, {"primary_key": "186450", "vector": [], "sparse_vector": [], "title": "Why Not Together? A Multiple-Round Recommender System for Queries and Items.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental technique of recommender systems involves modeling user preferences, where queries and items are widely used as symbolic representations of user interests. Queries delineate user needs at an abstract level, providing a high-level description, whereas items operate on a more specific and concrete level, representing the granular facets of user preference. While practical, both query and item recommendations encounter the challenge of sparse user feedback. To this end, we propose a novel approach named Multiple-round Auto Guess-and-Update System (MAGUS) that capitalizes on the synergies between both types, allowing us to leverage both query and item information to form user interests. This integrated system introduces a recursive framework that could be applied to any recommendation method to exploit queries and items in historical interactions and to provide recommendations for both queries and items in each interaction round. Concretely, MAGUS first represents queries and items through combinations of categorical words, and then constructs a relational graph to capture the interconnections and dependencies among these individual words and word combinations. In response to each user request, MAGUS employs an offline tuned recommendation model to assign estimated scores to words representing items; and these scores are subsequently disseminated throughout the graph, impacting each individual word or combination of words. Through multiple-round interactions, MAGUS initially guesses user interests by formulating meaningful word combinations and presenting them as potential queries or items. Subsequently, MAGUS is updated based on user feedback, enhancing its recommendations iteratively. Empirical results from testing 12 different recommendation methods demonstrate that integrating queries into item recommendations via MAGUS significantly enhances the efficiency, with which users can identify their preferred items during multiple-round interactions.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709261"}, {"primary_key": "186451", "vector": [], "sparse_vector": [], "title": "Large Vison-Language Foundation Model in Baidu AIGC Image Advertising.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in generative artificial intelligence have revolutionized information retrieval and content generation, opening up new opportunities for the e-commerce industry. Alignment learning between small models and parallel corpora cannot meet current needs. The success of ChatGPT demonstrates that large models need to first establish a fundamental understanding, and then utilize high-quality corpora for generation. Having a large model foundation is indispensable. In this paper, we establish a fundamental 10B multimodal model foundation for multimodal generation tasks and propose a scene-based alignment learning approach called conditional sample supervised fine-tuning for downstream generation tasks. Meanwhile, diffusion models are known to be vulnerable to outliers in training data. To address this, we utilize an alternative diffusion loss function that preserves the high quality of generated data like the original squared L2 loss while being robust to outliers.In practical test sets, the multimodal foundation fully demonstrates its alignment and comprehension abilities for graphic and textual content. Additionally, conditional fine-tuning and the design of the loss function significantly enhance the quality of generated content. The quality rate of images has increased by 34.3 percentage points, and prompt control has improved by 19.8 percentage points. The application of our framework in Baidu Search Ads has led to significant revenue growth. For instance, ads with generated image creatives have achieved a 29% higher click-through rate (CTR), resulting in a daily consumption of 3 million yuan.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709401"}, {"primary_key": "186453", "vector": [], "sparse_vector": [], "title": "Simplicial SMOTE: Oversampling Solution to the Imbalanced Learning Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "SMOTE (Synthetic Minority Oversampling Technique) is the established geometric approach to random oversampling to balance classes in the imbalanced learning problem, followed by many extensions. Its idea is to introduce synthetic data points of the minor class, with each new point being the convex combination of an existing data point and one of its k-nearest neighbors. In this paper, by viewing SMOTE as sampling from the edges of a geometric neighborhood graph and borrowing tools from the topological data analysis, we propose a novel technique, Simplicial SMOTE, that samples from the simplices of a geometric neighborhood simplicial complex. A new synthetic point is defined by the barycentric coordinates w.r.t. a simplex spanned by an arbitrary number of data points being sufficiently close rather than a pair. Such a replacement of the geometric data model results in better coverage of the underlying data distribution compared to existing geometric sampling methods and allows the generation of synthetic points of the minority class closer to the majority class on the decision boundary. We experimentally demonstrate that our Simplicial SMOTE outperforms several popular geometric sampling methods, including the original SMOTE. Moreover, we show that simplicial sampling can be easily integrated into existing SMOTE extensions. We generalize and evaluate simplicial extensions of the classic Borderline SMOTE, Safe-level SMOTE, and ADASYN algorithms, all of which outperform their graph-based counterparts.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709268"}, {"primary_key": "186454", "vector": [], "sparse_vector": [], "title": "YaART: Yet Another ART Rendering Technology.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the rapidly progressing field of generative models, the development of efficient and high-fidelity text-to-image diffusion systems represents a significant frontier. This study introduces YaART, a novel production-grade text-to-image cascaded diffusion model aligned to human preferences using Reinforcement Learning from Human Feedback (RLHF). During the development of YaART, we especially focus on the choices of the model and training dataset sizes, the aspects that were not systematically investigated for text-to-image cascaded diffusion models before. In particular, we comprehensively analyze how these choices affect both the efficiency of the training process and the quality of the generated images, which are highly important in practice. Furthermore, we demonstrate that models trained on smaller datasets of higher-quality images can successfully compete with those trained on larger datasets, establishing a more efficient scenario of diffusion models training. From the quality perspective, YaART is consistently preferred by users over many existing state-of-the-art models. The proposed system is integrated in five commercial products. YaART is also distributed for B2C clients via mobile and web applications and for B2B clients via API.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709404"}, {"primary_key": "186456", "vector": [], "sparse_vector": [], "title": "LLMLight: Large Language Models as Traffic Signal Control Agents.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traffic Signal Control (TSC) is a crucial component in urban traffic management, aiming to optimize road network efficiency and reduce congestion. Traditional TSC methods, primarily based on transportation engineering and reinforcement learning (RL), often struggle with generalization abilities across varied traffic scenarios and lack interpretability. This paper presents LLMLight, a novel framework employing Large Language Models (LLMs) as decision-making agents for TSC. Specifically, the framework begins by instructing the LLM with a knowledgeable prompt detailing real-time traffic conditions. Leveraging the advanced generalization capabilities of LLMs, LLMLight engages a reasoning and decision-making process akin to human intuition for effective traffic control. Moreover, we build LightGPT, a specialized backbone LLM tailored for TSC tasks. By learning nuanced traffic patterns and control strategies, LightGPT enhances the LLMLight framework cost-effectively. Extensive experiments conducted on ten real-world and synthetic datasets, along with evaluations by fifteen human experts, demonstrate the exceptional effectiveness, generalization ability, and interpretability of LLMLight with LightGPT, outperforming nine baseline methods and ten advanced LLMs. Our project is available at https://github.com/usail-hkust/LLMTSCS.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709379"}, {"primary_key": "186462", "vector": [], "sparse_vector": [], "title": "APEX2: Adaptive and Extreme Summarization for Personalized Knowledge Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "Dongqi Fu", "Mengting Ai", "<PERSON><PERSON><PERSON>"], "summary": "Knowledge graphs (KGs), which store an extensive number of relational facts, serve various applications.Recently, personalized knowledge graphs (PKGs) have emerged as a solution to optimize storage costs by customizing their content to align with users' specific interests within particular domains.In the real world, on one hand, user queries and their underlying interests are inherently evolving, requiring PKGs to adapt continuously; on the other hand, the summarization is constantly expected to be as small as possible in terms of storage cost.However, the existing PKG summarization methods implicitly assume that the user's interests are constant and do not shift.Furthermore, when the size constraint of PKG is extremely small, the existing methods cannot distinguish which facts are more of immediate interest and guarantee the utility of the summarized PKG.To address these limitations, we propose APEX 2 , a highly scalable PKG summarization framework designed with robust theoretical guarantees to excel in adaptive summarization tasks with extremely small size constraints.To be specific, after constructing an initial PKG, APEX 2 continuously tracks the interest shift and adjusts the previous summary.We evaluate APEX 2 under an evolving query setting on benchmark KGs containing up to 12 million triples, summarizing with compression ratios ≤ 0.1%.The experiments show that APEX outperforms state-of-the-art baselines in terms of both query-answering accuracy and efficiency.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709213"}, {"primary_key": "186463", "vector": [], "sparse_vector": [], "title": "MGS3: A Multi-Granularity Self-Supervised Code Search Framework.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON><PERSON>"], "summary": "In the pursuit of enhancing software reusability and developer productivity, code search has emerged as a key area, aimed at retrieving code snippets relevant to functionalities based on natural language queries. Despite significant progress in self-supervised code pre-training utilizing the vast amount of code data in repositories, existing methods have primarily focused on leveraging contrastive learning to align natural language with function-level code snippets. These studies have overlooked the abundance of fine-grained (such as block-level and statement-level) code snippets prevalent within the function-level code snippets, which results in suboptimal performance across all levels of granularity. To address this problem, we first construct a multi-granularity code search dataset called MGCodeSearchNet, which contains 536K+ pairs of natural language and code snippets. Subsequently, we introduce a novel Multi-Granularity Self-Supervised contrastive learning code Search framework (MGS3). First, MGS3 features a Hierarchical Multi-Granularity Representation module (HMGR), which leverages syntactic structural relationships for hierarchical representation and aggregates fine-grained information into coarser-grained representations. Then, during the contrastive learning phase, we endeavor to construct positive samples of the same granularity for fine-grained code, and introduce in-function negative samples for fine-grained code. Finally, we conduct extensive experiments on code search benchmarks across various granularities, demonstrating that the framework exhibits outstanding performance in code search tasks of multiple granularities. These experiments also showcase its model-agnostic nature and compatibility with existing pre-trained code representation models.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709263"}, {"primary_key": "186464", "vector": [], "sparse_vector": [], "title": "Contrastive Learning for Inventory Add Prediction at Fliggy.", "authors": ["<PERSON><PERSON>", "Detao Lv", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online Travel Platforms (OTPs) serve as crucial bridges between hotels and users, hotel staff can synchronize room inventory information with OTPs through manual and auto modes. In the manual mode, the hotel staff must manually maintain the inventory information on the OTPs. This mode often leads to the \"inventory synchronization delay'' phenomenon where OTPs show no availability while hotels still have available rooms, seriously affecting the competitiveness of OTPs and hotel sales. To address this issue, <PERSON><PERSON><PERSON> uses inventory add prediction (IAP) to determine whether to add an inventory for the sold-out room type. However, in practice, accurate modeling of IAP faces significant challenges due to the data sparsity. In this paper, we propose a Contrastive Learning framework for Inventory Add Prediction at Fliggy (CL4IAP), which consists of the Joint Pay-Accept Prediction Module, the Data Augmentation Module, and the Contrastive Learning Module. Specifically, the Joint Pay-Accept Prediction Module aims to predict the likelihood of generating an order and the hotel acceptance after adding an inventory. It also includes a specially designed correlation enhancement component that facilitates the expert prediction network's learning through knowledge transfer based on inter-task correlation. In the Data Augmentation Module, we design three novel data augmentation strategies for the first time based on the correlation and importance of features. In the Contrastive Learning Module, we design instance-level and cluster-level contrastive losses, which aim to minimize the distance between positive sample pairs and mitigate the negative impact of false negative sample pairs, respectively. Both offline and online experiments demonstrate the effectiveness of CL4IAP, and CL4IAP has been successfully deployed on Fliggy.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709384"}, {"primary_key": "186467", "vector": [], "sparse_vector": [], "title": "RankElectra: Semi-supervised Pre-training of Learning-to-Rank Electra for Web-scale Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON>"], "summary": "While representation learning has been used to boost the performance of Learning-to-Rank (LTR) models through distilling key features for webpage ranking, the weak supervision signals extracted from users' sparse click-through data lead to inadequate representation of query-webpage pairs for ranking score prediction. Recent studies in generative LTR pre-training demonstrate the feasibility of incorporating reconstruction loss for enhanced ranking score prediction. However, LTR is afterall a regression task and it might be reasonable to find an alternate route that pre-trains LTR models with discriminative losses. Following the success of Electra in representation learning for natural language processing (NLP), this work proposes RankElectra that pre-trains the LTR model as a discriminator module inside a generative learning framework. Specifically, RankElectra first structures sparsely-annotated query-webpage pairs into a bipartite graph, with query and webpage feature vectors as node types and ranking scores as the connecting edges, and then leverages positive and negative extension strategies to densify the graph by link predictions. Later, this work proposes a novel Electra module that pre-trains the LTR model as a discriminator module for node reconstruction tasks, where node features of selected edges would be randomly masked and reconstructed by a generator, and the discriminator learns to classify whether the reconstructed features are the original or replaced as well as perform correct ranking. Finally, the pre-trained discriminator module, rather than the generator, would be fine-tuned on the labeled graph. We carried out extensive offline and online evaluations using the real-world web traffic of Baidu search engine. The results show that RankElectra could significantly boost the ranking performance of Baidu Search compared with numbers of competitor systems.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709395"}, {"primary_key": "186468", "vector": [], "sparse_vector": [], "title": "TSPRank: Bridging Pairwise and Listwise Methods with a Bilinear Travelling Salesman Model.", "authors": ["Weixian <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional Learning-To-Rank (LETOR) approaches, including pairwise methods like RankNet and LambdaMART, often fall short by solely focusing on pairwise comparisons, leading to sub-optimal global rankings. Conversely, deep learning based listwise methods, while aiming to optimise entire lists, require complex tuning and yield only marginal improvements over robust pairwise models. To overcome these limitations, we introduce Travelling Salesman Problem Rank (TSPRank), a hybrid pairwise-listwise ranking method. TSPRank reframes the ranking problem as a Travelling Salesman Problem (TSP), a well-known combinatorial optimisation challenge that has been extensively studied for its numerous solution algorithms and applications. This approach enables the modelling of pairwise relationships and leverages combinatorial optimisation to determine the listwise ranking. TSPRank can be directly integrated as an additional component into embeddings generated by existing backbone models to enhance ranking performance. Our extensive experiments across three backbone models on diverse tasks, including stock ranking, information retrieval, and historical events ordering, demonstrate that TSPRank significantly outperforms both pure pairwise and listwise methods. Our qualitative analysis reveals that TSPRank's main advantage over existing methods is its ability to harness global information better while ranking. TSPRank's robustness and superior performance across different domains highlight its potential as a versatile and effective LETOR solution.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709234"}, {"primary_key": "186470", "vector": [], "sparse_vector": [], "title": "Learnable Prompt as Pseudo-Imputation: Rethinking the Necessity of Traditional EHR Data Imputation in Downstream Clinical Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Liantao Ma"], "summary": "Analyzing the health status of patients based on Electronic Health Records (EHR) is a fundamental research problem in medical informatics. The presence of extensive missing values in EHR makes it challenging for deep neural networks (DNNs) to directly model the patient's health status. Existing DNNs training protocols, including Impute-then-Regress Procedure and Jointly Optimizing of Impute-n-Regress Procedure, require the additional imputation models to reconstruction missing values. However, Impute-then-Regress Procedure introduces the risk of injecting imputed, non-real data into downstream clinical prediction tasks, resulting in power loss, biased estimation, and poorly performing models, while Jointly Optimizing of Impute-n-Regress Procedure is also difficult to generalize due to the complex optimization space and demanding data requirements. Inspired by the recent advanced literature of learnable prompt in the fields of NLP and CV, in this work, we rethought the necessity of the imputation model in downstream clinical tasks, and proposed Learnable Prompt as Pseudo-Imputation (PAI) as a new training protocol to assist EHR analysis. PAI no longer introduces any imputed data but constructs a learnable prompt to model the implicit preferences of the downstream model for missing values, resulting in a significant performance improvement for all state-of-the-arts EHR analysis models on four real-world datasets across two clinical prediction tasks. Further experimental analysis indicates that PAI exhibits higher robustness in situations of data insufficiency and high missing rates. More importantly, as a plug-and-play protocol, PAI can be easily integrated into any existing or even imperceptible future EHR analysis models. The code of this work is deployed publicly available at https://github.com/MrBlankness/PAI to help the research community reproduce the results and assist the EHR analysis tasks.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709166"}, {"primary_key": "186473", "vector": [], "sparse_vector": [], "title": "MobileSteward: Integrating Multiple App-Oriented Agents with Self-Evolution to Automate Cross-App Instructions.", "authors": ["<PERSON><PERSON><PERSON>", "Hongda Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile phone agents can assist people in automating daily tasks on their phones, which have emerged as a pivotal research spotlight. However, existing procedure-oriented agents struggle with cross-app instructions, due to the following challenges: (1) complex task relationships, (2) diverse app environment, and (3) error propagation and information loss in multi-step execution. Drawing inspiration from object-oriented programming principles, we recognize that object-oriented solutions is more suitable for cross-app instruction. To address these challenges, we propose a self-evolving multi-agent framework named MobileSteward which integrates multiple app-oriented StaffAgents coordinated by a centralized StewardAgent. We design three specialized modules in MobileSteward: (1) Dynamic Recruitment generates a scheduling graph guided by information flow to explicitly associate tasks among apps. (2) Assigned Execution assigns the task to app-oriented StaffAgents, each equipped with app-specialized expertise to address the diversity between apps. (3) Adjusted Evaluation conducts evaluation to provide reflection tips or deliver key information, which alleviates error propagation and information loss during multi-step execution. To continuously improve the performance of MobileSteward, we develop a Memory-based Self-evolution mechanism, which summarizes the experience from successful execution, to improve the performance of MobileSteward. We establish the first English Cross-APP Benchmark (CAPBench) in the real-world environment to evaluate the agents' capabilities of solving complex cross-app instructions. Experimental results demonstrate that MobileSteward achieves the best performance compared to both single-agent and multi-agent frameworks, highlighting the superiority of MobileSteward in better handling user instructions with diverse complexity.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709171"}, {"primary_key": "186474", "vector": [], "sparse_vector": [], "title": "SEPTQ: A Simple and Effective Post-Training Quantization Paradigm for Large Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hong Yu"], "summary": "Large language models (LLMs) have shown remarkable performance in various domains, but they are constrained by massive computational and storage costs. Quantization, an effective technique for compressing models to fit resource-limited devices while preserving generative quality, encompasses two primary methods: quantization aware training (QAT) and post-training quantization (PTQ). QAT involves additional retraining or fine-tuning, thus inevitably resulting in high training cost and making it unsuitable for LLMs. Consequently, PTQ has become the research hotspot in recent quantization methods. However, existing PTQ methods usually rely on various complex computation procedures and suffer from considerable performance degradation under low-bit quantization settings. To alleviate the above issues, we propose a simple and effective post-training quantization paradigm for LLMs, named SEPTQ. Specifically, SEPTQ first calculates the importance score for each element in the weight matrix and determines the quantization locations in a static global manner. Then it utilizes the mask matrix which represents the important locations to quantize and update the associated weights column-by-column until the appropriate quantized weight matrix is obtained. Compared with previous methods, SEPTQ simplifies the post-training quantization procedure into only two steps, and considers the effectiveness and efficiency simultaneously. Experimental results on various datasets across a suite of models ranging from millions to billions in different quantization bit-levels demonstrate that SEPTQ significantly outperforms other strong baselines, especially in low-bit quantization scenarios.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709287"}, {"primary_key": "186475", "vector": [], "sparse_vector": [], "title": "3DGraphX: Explaining 3D Molecular Graph Models via Incorporating Chemical Priors.", "authors": ["<PERSON><PERSON>", "Dongsheng Luo", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the explanation of 3D graph neural networks (GNNs) in the field of molecular learning. Recent studies have modeled molecules as 3D graphs, but there exist formidable challenges for 3D graph explanation. In this work, we propose a novel and principled paradigm, known as 3DGraphX, for 3D molecular graph explanation. Unlike existing 2D GNN explanation methods, 3DGraphX focuses on 3D motifs, which are subgraphs showing great occurrence and function significance in molecular activities. Once generated, 3D motifs are fixed in the explanation model; hence, 3DGraphX produces more accurate and chemically plausible explanations in an efficient manner. 3DGraphX contains two branches with several novel methods for instance-level and geometry-level explanations, respectively. Two novel components, known as the mask pooling component and mask unpooling component, are developed to discover important motifs for each 3D molecule as the instance-level explanation. Local spherical coordinate systems are built to investigate the relative positions among motifs for geometry-level explanation. Altogether, 3DGraphX sheds light on the characteristics of molecules as well as the behaviors of 3D GNNs in molecular learning. Experimental results show that 3DGraphX significantly outperforms baselines in instance-level explanation with various explanation budgets. Additional experiments show that 3DGraphX reveals the important geometries taken by 3D GNNs for accurate molecular learning. The code is publicly available at https://github.com/xufliu/3DGraphX.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709302"}, {"primary_key": "186477", "vector": [], "sparse_vector": [], "title": "Fine-tuning Multimodal Large Language Models for Product Bundling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advances in product bundling have leveraged multimodal information through sophisticated encoders, but remain constrained by limited semantic understanding and a narrow scope of knowledge. Therefore, some attempts employ In-context Learning (ICL) to explore the potential of large language models (LLMs) for their extensive knowledge and complex reasoning abilities. However, these efforts are inadequate in understanding mulitmodal data and exploiting LLMs' knowledge for product bundling. To bridge the gap, we introduce Bundle-MLLM, a novel framework that fine-tunes LLMs through a hybrid item tokenization approach within a well-designed optimization strategy. Specifically, we integrate textual, media, and relational data into a unified tokenization, introducing a soft separation token to distinguish between textual and non-textual tokens. Additionally, a streamlined yet powerful multimodal fusion module is employed to embed all non-textual features into a single, informative token, significantly boosting efficiency. To tailor product bundling tasks for LLMs, we reformulate the task as a multiple-choice question with candidate items as options. We further propose a progressive optimization strategy that fine-tunes LLMs for disentangled objectives: learning bundle patterns and enhancing multimodal semantic understanding specific to product bundling. Extensive experiments demonstrate that our approach outperforms a range of state-of-the-art (SOTA) methods. Codes are available at https://github.com/<PERSON><PERSON>-<PERSON>/Bundle-MLLM", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709255"}, {"primary_key": "186479", "vector": [], "sparse_vector": [], "title": "Scenario Shared Instance Modeling for Click-through Rate Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-scenario recommendation (MSR) is a popular training paradigm in industrial platforms for uniformly integrating information from multiple scenarios and serving them simultaneously. A key challenge in MSR research is accurately identifying the commonalities and distinctive information between scenarios. Currently, most existing MSR methods focus on implicitly extracting this information from the architectural level. However, this continues to increase the complexity and training overhead of MSR. Furthermore, the custom components responsible for extracting implicit information in each MSR method are too dependent on the specific MSR architecture and are not easily reused in other methods. Given these challenges, we first show in a motivating experiment that it may be beneficial to explicitly select a reasonable set of shared instances that can affect parameter optimization in all scenarios during the training of MSR, i.e., to explicitly obtain the critical information required for MSR from the data level. Then, this paper proposes SSIM with an adaptive selection network. Specifically, SSIM can be integrated with existing MSR methods in a lightweight way to adaptively select an informative and shareable subset of instances from each scenario to improve recommendations. In particular, the selected multi-scenario shared subset has extraordinary reusability and can be easily saved to benefit model training of various future MSR models. Finally, we evaluate SSIM and demonstrate its effectiveness through experiments on two public multi-scenario benchmarks and an online A/B test.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709390"}, {"primary_key": "186480", "vector": [], "sparse_vector": [], "title": "Learning Attribute as Explicit Relation for Sequential Recommendation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The data on user behaviors is sparse given the vast array of user-item combinations. Attributes related to users (e.g., age), items (e.g., brand), and behaviors (e.g., co-purchase) serve as crucial input sources for item-item transitions of user's behavior prediction. While recent Transformer-based sequential recommender systems learn the attention matrix for each attribute to update item representations, the attention of a specific attribute is optimized by gradients from all input sources, leading to potential information mixture. Besides, Transformers mainly focus on intra-sequence attention for item attributes, neglecting cross-sequence relations and user attributes. Addressing these challenges, we propose the Attribute Transformer (AttrFormer) to learn attributes as explicit relations. This model transforms each type of attribute into an explicit relation defined in the feature space, and it ensures no information mixing among different input sources. Explicit relations introduce cross-sequence and intra-sequence relations. AttrFormer has novel relation-augmented heads to handle them at both the item and behavioral levels, seamlessly integrating the augmented heads into the multi-head attention mechanism. Furthermore, we employ position-to-position aggregation to refine behavior representation for users with similar patterns at the sequence level. To capture the subjective nature of user preferences, AttrFormer is trained using posterior targets where upcoming user behaviors follow a multinomial distribution with a Dirichlet prior. Our evaluations on four popular datasets, including Amazon (Toys & Games and Beauty) and MovieLens (1M and 25M versions), reveal that AttrFormer outperforms leading Transformer baselines, achieving around 20% improvement in NDCG@20 scores. Extensive ablation studies also demonstrate the efficiency of AttrFormer in managing long behavior sequences and inter-sequence relations.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709267"}, {"primary_key": "186481", "vector": [], "sparse_vector": [], "title": "SCode: A Spherical Code Metric Learning Approach to Continuously Monitoring Predictive Events in Networked Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Dynamic graphs are common in many applications to conveniently model heterogeneous data integrated from multiple sources. We study the monitoring of predictive events in dynamic graphs. Treating the problem as a continuous multi-label classification, we use deep metric learning to manage the embedding space and to create spherical codes where each codeword is an embedding vector representing a cluster of data state embeddings with the same results of the predictive events. By continuously training data embeddings from a dynamic graph neural network (DGNN) model and a code generator together, our method, called SCode, achieves significantly better accuracy than DGNN baselines. Moreover, SCode is also about twice as fast as the DGNN baselines, owing to its efficient matching between data state embedding and codewords for multiple events together. Finally, our training sample complexity analysis also sheds light on the generalizability of the online learning.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709246"}, {"primary_key": "186484", "vector": [], "sparse_vector": [], "title": "Future Matters for Present: Towards Effective Physical Simulation over Meshes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Huiyu Jiang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Yizhou Sun"], "summary": "This paper investigates the problem of learning mesh-based physical simulations, which is a crucial task with applications in fluid mechanics and aerodynamics. Recent works typically utilize graph neural networks (GNNs) to produce next-time states on irregular meshes by modeling interacting dynamics, and then adopt iterative rollouts for the whole trajectories. However, these methods cannot achieve satisfactory performance in long-term predictions due to the failure of capturing long-term dependency and potential error accumulations. To tackle this, we introduce a new future-to-present learning perspective, and further develop a simple yet effective approach named Foresight And Interpolation (FAIR) for long-term mesh-based simulations. The main idea of our FAIR is to first learn a graph ODE model for coarse long-term predictions and then refine short-term predictions via interpolation. Specifically, FAIR employs a continuous graph ODE model that incorporates past states into the evolution of interacting node representations, which is capable of learning coarse long-term trajectories under a multi-task learning framework. Then, we leverage a channel aggregation strategy to summarize the trajectories for refined short-term predictions, which can be illustrated using an interpolation process. Through pyramid-like alternative propagation between the foresight step and refinement step, our proposed framework FAIR can generate accurate long-term trajectories, achieving a significant error reduction compared with the best baseline on four benchmark datasets. Extensive ablation studies and visualization further validate the superiority of our proposed FAIR.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709340"}, {"primary_key": "186485", "vector": [], "sparse_vector": [], "title": "Dynamic Deep Clustering of High-Dimensional Directional Data via Hyperspherical Embeddings with Bayesian Nonparametric Mixtures.", "authors": ["<PERSON><PERSON><PERSON>", "Wen<PERSON><PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Clustering high-dimensional directional data (i.e., L2 normalized vectors) presents significant challenges due to the intricate spherical representations of latent embeddings and the limitations of classical (non-deep) clustering techniques. Moreover, dynamically inferring the number of clusters remains a fundamental issue in existing deep clustering methods, especially those involving complex model-selection criteria. This paper addresses these challenges by introducing a novel deep nonparametric clustering framework that employs hyperspherical latent embeddings within a Variational Autoencoder architecture, enhanced by an infinite Von Mises-Fisher Mixture Model as a dynamic prior. This approach enables automatic adaptation of cluster numbers during training, eliminating the need for predefined clusters and traditional model selection processes. Our scalable architecture effectively integrates In-vMFMM with hyperspherical embeddings to tackle the complexities of directional data. Utilizing a joint training strategy, our method alternates between updating neural network parameters and adjusting mixture model priors via nonparametric variational Bayes. Empirical evaluations on benchmark datasets, including complex ImageNet-50, demonstrate that our approach significantly outperforms state-of-the-art deep nonparametric clustering methods. It also robustly estimates the number of clusters, showcasing its effectiveness and versatility in handling high-dimensional directional data.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709230"}, {"primary_key": "186486", "vector": [], "sparse_vector": [], "title": "Online Item Cold-Start Recommendation with Popularity-Aware Meta-Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the rise of e-commerce and short videos, online recommender systems that can capture users' interests and update new items in real-time play an increasingly important role. In both online and offline recommendation systems, the cold-start problem caused by interaction sparsity has been impacting the effectiveness of recommendations for cold-start items. Many cold-start scheme based on fine-tuning or knowledge transferring shows excellent performance on offline recommendation. Yet, these schemes are infeasible for online recommendation on streaming data pipelines due to different training method, computational overhead and time constraints. Inspired by the above questions, we propose a model-agnostic recommendation algorithm called Popularity-Aware Meta-learning (PAM), to address the item cold-start problem under streaming data settings. PAM divides the incoming data into different meta-learning tasks by predefined item popularity thresholds. The model can distinguish and reweight behavior-related and content-related features in each task based on their different roles in different popularity levels, thus adapting to recommendations for cold-start samples. These task-fixing design significantly reduces additional computation and storage costs compared to offline methods. Furthermore, PAM also introduced data augmentation and an additional self-supervised loss specifically designed for low-popularity tasks, leveraging insights from high-popularity samples. This approach effectively mitigates the issue of inadequate supervision due to the scarcity of cold-start samples. Experimental results across multiple public datasets demonstrate the superiority of our approach over other baseline methods in addressing cold-start challenges in online streaming data scenarios.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709336"}, {"primary_key": "186487", "vector": [], "sparse_vector": [], "title": "Fairness without Demographics through Learning Graph of Gradients.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning systems are notoriously prone to biased predictions about certain demographic groups, leading to algorithmic fairness issues.Due to privacy concerns and data quality problems, some demographic information may not be available in the training data and the complex interaction of different demographics can lead to a lot of unknown minority subpopulations, which all limit the applicability of group fairness.Many existing works on fairness without demographics assume the correlation between groups and features.However, we argue that the model gradients are also valuable for fairness without demographics.In this paper, we show that the correlation between gradients and groups can help identify and improve group fairness.With an adversarial weighting architecture, we construct a graph where samples with similar gradients are connected and learn the weights of different samples from it.Unlike the surrogate grouping methods that cluster groups from features and labels as proxy sensitive attribute, our method leverages the graph structure as a soft grouping mechanism, which is much more robust to noises.The results show that our method is robust to noise and can improve fairness significantly without decreasing the overall accuracy too much.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709160"}, {"primary_key": "186488", "vector": [], "sparse_vector": [], "title": "Collaboration of Large Language Models and Small Recommendation Models for Device-Cloud Recommendation.", "authors": ["Zheqi Lv", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) for Recommendation (LLM4Rec) is a promising research direction that has demonstrated exceptional performance in this field.However, its inability to capture real-time user preferences greatly limits the practical application of LLM4Rec because (i) LLMs are costly to train and infer frequently, and (ii) LLMs struggle to access real-time data (its large number of parameters poses an obstacle to deployment on devices).Fortunately, small recommendation models (SRMs) can effectively supplement these shortcomings of LLM4Rec diagrams by consuming minimal resources for frequent training and inference, and by conveniently accessing real-time data on devices.In light of this, we designed the Device-Cloud LLM-SRM Collaborative Recommendation Framework (LSC4Rec) under a device-cloud collaboration setting.LSC4Rec aims to integrate the advantages of both LLMs and SRMs, as well as the benefits of cloud and edge computing, achieving a complementary synergy.We enhance the practicability of LSC4Rec by designing three strategies: collaborative training, collaborative inference, and intelligent request.During training, LLM generates candidate lists to enhance the ranking ability of SRM in collaborative scenarios and enables SRM to update adaptively to capture real-time user interests.During inference, LLM and SRM are deployed on the cloud and on the device, respectively.LLM generates candidate lists and initial ranking results based on user behavior, and SRM get reranking results based on the candidate list, with final results integrating", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709335"}, {"primary_key": "186491", "vector": [], "sparse_vector": [], "title": "Achieving Nearly-Optimal Regret and Sample Complexity in Dueling Bandits with Applications in Online Recommendations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We focus on the dueling bandits problem, which has recently drawn significant attention due to its wide-ranging applications in online recommendation systems and the alignment of large language models (LLMs), considers an online preference learning scenario where the learner iteratively selects arms based on pairwise comparison feedback to infer user preferences. Two primary objectives are typically considered in dueling bandits: Regret Minimization (RM), which aims to improve the overall quality of selected arms over time, and Best Arm Identification (BAI), which seeks to efficiently identify the best item with minimal user feedback. For instance, RM is exemplified by the objective of consistently providing high-quality items, while BAI reduces the required human feedback by minimizing the number of necessary comparisons. Conventional research treats RM and BAI as two conflicting objectives, optimizing one at the expense of the other. In this paper, we propose a novel framework that demonstrates the near-consistency of RM and BAI in dueling bandits by reducing the BAI in dueling bandits into a sequential noisy identification problem. Based on our formulation, we propose a black-box reduction technique that transforms any RM algorithm into a BAI algorithm, and prove that such reduction with optimal RM algorithm achieves optimal sample complexity and nearly-optimal cumulative weak regret simultaneously. Our proposed algorithm acheives a nearly-optimal BAI sample complexity and attains a cumulative weak regret that is order-wise equivalent to the best-known result simultaneously. Experiments on both synthetic benchmarks and real-world online recommendation tasks validate the effectiveness of the proposed method, providing empirical evidences for our theoretical findings.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709279"}, {"primary_key": "186493", "vector": [], "sparse_vector": [], "title": "On the Support Vector Effect in DNNs: Rethinking Data Selection and Attribution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In Deep Neural Networks (DNNs), manipulating gradients is central to various algorithms, including data subset selection and instance attribution. For better tractability, practitioners often resort to using only the gradients of the last layer as a heuristic, instead of the full gradient across all model parameters, which we show is detrimental due to the Support Vector Effect (SVE). We introduce SVE, a max-margin-like behavior in the last layer(s) of DNNs and employ it to thoroughly scrutinize prevalent data selection and attribution methods relying on last layer gradients. Our investigation exposes limitations in these techniques and not only provides explanations for previously observed pitfalls, like lack of diversity and temporal performance degradation, but also offers fresh insights, including the vulnerability of existing methods to basic poisoning attacks and the potential for competitive performance using much simpler alternatives. Based on insights from SVE, we craft new methods RandE and PAE for data subset selection and instance attribution, respectively, which often outperform the purported state-of-the-art at a fraction of the cost, emphasizing the practical advantages of more efficient and less complex approaches.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709295"}, {"primary_key": "186494", "vector": [], "sparse_vector": [], "title": "Enhancing Black-Box Adversarial Attacks on Discrete Sequential Data via Bilevel Bayesian Optimization in Hybrid Spaces.", "authors": ["Tianxing Man", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Black-box attacks have emerged as a significant threat to deep neural networks. This challenge is particularly difficult in discrete sequential data compared to continuous data. Recently, the Blockwise Bayesian Attack (BBA) leveraging discrete Bayesian optimization with an adapted RBF kernel has gained prominence as a cutting-edge solution. However, it relies solely on alignment information (i.e., positional differences) within the RBF kernel, which may not fully capture the information (such as statistical, structural, and semantic information) inherent in discrete sequential data and potentially lacks the desired inductive bias necessary to approximate the target function accurately. To overcome this limitation, this paper proposes a novel bilevel Bayesian optimization approach to adaptively learn a hybrid space that better captures the similarity between discrete sequences. Specifically, we introduce a multi-kernel mechanism that incorporates multiple types of information, creating a more comprehensive similarity measure. Moreover, we develop a bilevel Bayesian optimization algorithm, where the outer-level objective determines the optimal weights of the multiple kernels, while the inner-level objective identifies the optimal adversarial sequence. Extensive experiments conducted on discrete sequential data demonstrate that our approach ensures secure multi-kernel selection and achieves a higher attack success rate with only a few additional queries, compared to BBA and other traditional optimization strategies.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709265"}, {"primary_key": "186497", "vector": [], "sparse_vector": [], "title": "Roadside Multi-LiDAR Data Fusion for Enhanced Traffic Safety.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Roadside LiDAR (Light Detection and Ranging) sensors promise safer and faster traffic management and vehicular operations. However, occlusion and small view angles are significant challenges to widespread use of roadside LiDARs. We consider fusing data from multiple LiDARs at a traffic intersection to better estimate traffic parameters than one can estimate from a single LiDAR. The key challenge is to calibrate multiple LiDARs both in time and space. The problem is more complex when heterogeneous sensors differ in resolution and are positioned arbitrarily on a traffic intersection. We propose a calibration technique to fuse multiple LiDARs. We show that our technique works on various data granularity and enables real-time analytics for roadside traffic monitoring. We evaluate on a large number of simulated traffic scenarios and show that fusion improves accuracy of vehicle counting and near-collision detection. We apply our algorithm on real traffic data and demonstrate utility in classifying vehicles and detecting occluded traffic participants.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709410"}, {"primary_key": "186498", "vector": [], "sparse_vector": [], "title": "TGDataset: Collecting and Exploring the Largest Telegram Channels Dataset.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Telegram is a widely adopted instant messaging platform. It has become worldwide popular because of its emphasis on privacy and its social network features such as channels-virtual rooms in which only the admins can post and broadcast messages to all the subscribers. Channels are used to deliver live updates (e.g., weather alerts) and content to a large audience (e.g., COVID-19 announcements) but unfortunately also to disseminate radical ideologies and coordinate attacks such as the Capitol Hill riot. This paper introduces the TGDataset, the most extensive publicly available collection of Telegram channels, comprising 120,979 channels and over 400 million messages. We outline the data collection process and provide a comprehensive overview of the data set. Using language detection, we identify the predominant languages within the dataset. We then focus on English channels, employing topic modeling to analyze the subjects they cover. Finally, we discuss some use cases in which our dataset can be instrumental in understanding the Telegram ecosystem and studying the diffusion of questionable news. Alongside the raw dataset, we release the scripts used in our analysis, as well as a list of channels associated with a novel conspiracy theory known as Sabmyk.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709397"}, {"primary_key": "186499", "vector": [], "sparse_vector": [], "title": "Using Instruction-Tuned LMs for Scalable Use Case-Based Shopping - Where Customers Meet Their Needs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Products on e-commerce platforms are usually organized based on seller-provided product attributes. Customers looking for a product typically have certain needs or use cases in mind, such as headphones for gym classes, or a printer for school projects. However, they often struggle to map these use cases to product attributes, thereby failing to find the product they need. To help customers shop online confidently, we propose a Use case-Based Shopping (UBS) system that facilitates customer experiences based on use cases (Fig. 1). UBS consists of three steps: 1) use case phrase extraction from product reviews, 2) clustering the extracted use case phrases to identify the dominant ones, and 3) mapping products in a given category to one or more of these dominant use cases. In this work, we utilize instruction-tuned LMs to primarily focus on the first two steps. However, the way we design them also helps us to seamlessly solve the third step to complete the design of our proposed system. First, we define the novel task of joint Use Uase, Uentiment Uxtraction (UCSE) from product reviews which can be used for both steps 1 and 3. We harness the task adaptation capability of instruction-tuned FLAN-T5 models and gradually improve their zero-shot UCSE performance through instruction tuning, multi-task training, and few-shot iterative re-training for new categories, achieving around ~90% reduction in annotation bandwidth. We then employ Anthropic's Claude 2 LLM to propose an unsupervised approach for hierarchical use case phrase clustering that demonstrates better clustering and cluster naming capabilities when compared to K-Means and LDA. In an online experiment targeting the top 7 product categories, UBS recommendations on search, browse, and product pages resulted in a revenue lift of 0.77%, 0.94%, and 0.44% respectively, and an average click rate lift of 0.15%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709411"}, {"primary_key": "186500", "vector": [], "sparse_vector": [], "title": "Data Glitches Discovery using Influence-based Model Explanations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the problem of detecting data glitches in ML training sets, specifically mislabeled and anomalous samples. Detection of data glitches provides insights into the quality of the data sampling. Their repair may improve the reliability and the performance of the model. The proposed methodology is based on exploiting influence functions that estimate how much the loss of the model (or a given sample) is affected when a sample is removed from the training set. We introduce three novel signals for detecting, characterizing, and repairing data glitches in a training set based on sample influences. Influence-based signals form an explainable-by-design data glitch detection framework, producing intuitively explainable signals of the actual predictive model built. In contrast, specialized algorithms that are agnostic to the target ML model (e.g., anomaly detectors) replicate the work of fitting the data distribution and may detect glitches that are inconsistent with the decision boundary of the predictive model. Computational experiments on tabular and image data modalities demonstrate that the proposed signals outperform, in some cases up to a factor of 6, all existing influence-based signals, and generalize across different datasets and ML models. In addition, they often outperform specialized glitch detectors (e.g., mislabeled and anomaly detectors) and provide accurate label repairs for mislabeled samples.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709285"}, {"primary_key": "186501", "vector": [], "sparse_vector": [], "title": "Electron-Informed Coarse-Graining Molecular Representation Learning for Real-World Molecular Physics.", "authors": ["Gyoung S. Na", "Chanyoung Park"], "summary": "Various representation learning methods for molecular structures have been devised to accelerate data-driven chemistry. However, the representation capabilities of existing methods are essentially limited to atom-level information, which is not sufficient to describe real-world molecular physics. Although electron-level information can provide fundamental knowledge about chemical compounds beyond the atom-level information, obtaining the electron-level information in real-world molecules is computationally impractical and sometimes infeasible. We propose a method for learning electron-informed molecular representations without additional computation costs by transferring readily accessible electron-level information about small molecules to large molecules of our interest. The proposed method achieved state-of-the-art prediction accuracy on extensive benchmark datasets containing experimentally observed molecular physics. The source code for HEDMoL is available at https://github.com/ngs00/HEDMoL.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709270"}, {"primary_key": "186504", "vector": [], "sparse_vector": [], "title": "Weight-Constrained Simple Path Enumeration in Weighted Graph.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Path enumeration is a fundamental problem and has been extensively studied in the literature. Given two query vertices and a weight threshold, the problem aims to identify all simple paths with weight not exceeding the threshold. Existing studies on path enumeration include DFS-based solutions and join-based solutions, where the join-based solutions only work for unweighted graphs. In this paper, we are the first to propose a join-based framework for weighted graphs. By observing the characteristics of DFS, we design a series of novel data structures and operations based on the join-based framework. In this way, our final solution combines the advantages of both join and DFS. We conduct experiments on several real large graphs. For weighted graphs, our method is much more efficient than existing algorithms. For unweighted graphs, our method is still competitive compared with the state-of-the-art solution which only works for unweighted graphs.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709310"}, {"primary_key": "186505", "vector": [], "sparse_vector": [], "title": "Distributional Prototype Learning for Out-of-distribution Detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Out-of-distribution (OOD) detection has emerged as a pivotal approach for enhancing the reliability of machine learning models, considering the potential for test data to be sampled from classes disparate from in-distribution (ID) data employed during model training. Detecting those OOD data is typically realized as a distance measurement problem, where those deviating far away from the training distribution in the learned feature space are considered OOD samples. Advanced works have shown great success in learning with prototypes for feature-based OOD detection methods, where each ID class is represented with single or multiple prototypes. However, modeling with a finite number of prototypes would fail to maximally capture intra-class variations. In view of this, this paper extends the existing prototype-based learning paradigm to an infinite setting. This motivates us to design two feasible formulations for the Distributional Prototype Learning (DPL) objective, where, to avoid intractable computation and exploding parameters caused by the infinity nature, our key idea is to model an infinite number of discrete prototypes of each ID class with a class-wise continuous distribution. We theoretically analyze both alternatives, identifying the more stable-converging version of the learning objective. We show that, by sampling prototypes from a mixture of class-conditioned Gaussian distributions, the objective can be efficiently computed in a closed form without resorting to the computationally expensive Monte-Carlo approximation of the involved expectation terms. Extensive evaluations across mainstream OOD detection benchmarks empirically manifest that our proposed DPL has established a new state-of-the-art in various OOD settings.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709294"}, {"primary_key": "186506", "vector": [], "sparse_vector": [], "title": "On the Necessity of World Knowledge for Mitigating Missing Labels in Extreme Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Extreme Classification (XC) aims to map a query to the most relevant documents from a very large document set. XC algorithms used in real-world applications typically learn this mapping from datasets curated from implicit feedback, such as user clicks. However, these datasets often suffer from missing labels. In this work, we observe that systematic missing labels lead to missing knowledge, which is critical for modelling relevance between queries and documents. We formally show that this absence of knowledge is hard to recover using existing methods such as propensity weighting and data imputation strategies that solely rely on the training dataset. While Large Language Models (LLMs) provide an attractive solution to augment the missing knowledge, leveraging them in applications with low latency requirements and large document sets is challenging. To mitigate missing knowledge at scale, we propose SKIM (Scalable Knowledge Infusion for Missing Labels), an algorithm that leverages a combination of Small Language Models or SLMs, e.g., Llama2-7b, and abundant unstructured meta-data to effectively address the missing label problem. We show the efficacy of our method on large-scale public datasets through a combination of unbiased evaluation strategies, such as exhaustive human annotations and simulation-based evaluation benchmarks. SKIM outperforms existing methods on Recall@100 by more than 10 absolute points. Additionally, SKIM scales to proprietary query-ad retrieval datasets containing 10 million documents, outperforming baseline methods by 12% in offline evaluations and increasing ad click-yield by 1.23% in an online A/B test conducted on Bing Search. We release the code and trained models at: github.com/bicycleman15/skim", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709290"}, {"primary_key": "186507", "vector": [], "sparse_vector": [], "title": "Understanding the Effect of Loss Functions on the Generalization of Recommendations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The two-tower model has become prevalent in recommender systems for its computational efficiency and robust predictive capabilities. The model usually employs two independent neural networks to encode user and item data separately, and predicts the similarity score with inner product or cosine functions, depending on which the Top-k ranked item list is generated. The optimization process typically involves a multi-label classification objective, often guided by surrogate loss functions like Softmax and One-vs-All (OvA), to enhance the recommendation performance. Despite both Softmax and OvA losses being Bayes-consistent, empirical observations reveal a significant performance gap in evaluation metrics, suggesting limitations in Bayes-consistency for analyzing loss effectiveness. To address this, we introduce ℋ-consistency into the discussion, which provides non-asymptotic and hypothesis-specific guarantees for Top-k classification within the two-tower model's hypothesis space. Through theoretical analysis, we demonstrate that Softmax and Cosine Contrastive Loss exhibit ℋ-consistency, while the OvA loss does not, explaining the observed performance discrepancies. Our findings bridge the gap between theoretical properties and practical outcomes, offering deeper insights into the optimization of two-tower models and contributing to the development of more effective recommendation systems.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709169"}, {"primary_key": "186508", "vector": [], "sparse_vector": [], "title": "Input Snapshots Fusion for Scalable Discrete-Time Dynamic Graph Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Hongyang Chen", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, there has been a surge in research on dynamic graph representation learning, primarily focusing on modeling the evolution of temporal-spatial patterns in real-world applications. However, within the domain of discrete-time dynamic graphs, the exploration of temporal edges remains underexplored. Existing approaches often rely on additional sequential models to capture dynamics, leading to high computational and memory costs, particularly for large-scale graphs. To address this limitation, we propose the Input Snapshots Fusion based Dynamic Graph Neural Network (SFDyG), which combines Hawkes processes with graph neural networks to capture temporal and structural patterns in dynamic graphs effectively. By fusing multiple snapshots into a single temporal graph, SFDyG decouples computational complexity from the number of snapshots, enabling efficient full-batch and mini-batch training. Experimental evaluations on eight diverse dynamic graph datasets for future link prediction tasks demonstrate that SFDyG consistently outperforms existing methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709316"}, {"primary_key": "186509", "vector": [], "sparse_vector": [], "title": "Tackling the Length Barrier: Dynamic Context Browsing for Knowledge-Intensive Task.", "authors": ["Hongjin Qian", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Knowledge-intensive tasks often require complex reasoning and contextual understanding over long contexts. However, the learning and deployment of long-LLMs remains a challenging problem despite recent progresses. In this work, we propose that the short LLMs have great potentiality for solving knowledge-intensive tasks that have long context, i.e. they can be solved by purely working with oracle short-contexts within the input long-context. On top of this argument, we propose a framework called DCISO DynamiC knowledge-Intensive task S>Olver), which enables a short-LLM to address the knowledge-intensive tasks with long context via dynamic context browsing. In our framework, the short-LLM prompts itself to reason for two critical decisions: 1) how to access to the appropriate part of context within the input, 2) how to make effective use of the accessed context. By adaptively accessing and utilizing the context based on the presented tasks, DCISO can serve as a general framework to handle diversified knowledge-intensive long-context problems. We comprehensively evaluate different types of tasks from popular long-context benchmarks, where DCISO is able to achieve a substantially improved performance. Our codes will be released at this repository.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709240"}, {"primary_key": "186510", "vector": [], "sparse_vector": [], "title": "Adapting to Generalized Online Label Shift by Invariant Representation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yuan Jiang", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The problem of online label shift, where label distribution evolves over time while the label-conditional density remains unchanged, has attracted increasing attentions. Although existing approaches have achieved sound theoretical guarantees and encouraging performance, the assumption of an unchanged conditional distribution may limit its application in broader tasks. In this paper, we investigate an extended variant named generalized online label shift (GOLS) problem, in which we relax the label shift assumption on the raw feature space and instead assume the existence of an unknown invariant representation such that conditional distribution of this representation given the label remains constant. To handle GOLS, our main idea involves capturing the inherently stable information from non-stationary streams, in the form of learning an invariant representation. Specifically, we design a novel objective to learn the invariant representation, which exploits the unique structure in GOLS. To optimize this objective, we propose an algorithm employing online ensemble paradigm to perform multi-resolution updates using various historical data windows, thereby enhancing the stability of the representation. This approach is theoretically guaranteed to achieve an optimal convergence rate. To improve the efficiency of the ensemble framework, we further propose a mask-based implementation for ensembling with DNNs. Experiments on benchmarks and real-world tasks validate the effectiveness of our approach.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709182"}, {"primary_key": "186511", "vector": [], "sparse_vector": [], "title": "Quantum Time-index Models with Reservoir for Time Series Forecasting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The time-index models are a class of time series forecasting models that map time-index features to forecasts in continuous space. Compared to the historical-value models, the time-index models can avoid the effect of data sampling frequency and are usually more expressive. However, the vanilla deep time-index model is weak in modeling the high-frequency components of time series and often requires the introduction of many parameters to enhance the modeling capability. Moreover, the time-index model learns only a mapping relationship and ignores the sequence relationship between temporal features, leading to a weak extrapolation capability in the forecast horizon. In this paper, inspired by the ability of quantum implicit neural representations to model the high-frequency components of signals with fewer parameters, we propose Quantum Time-Index Models with Reservoir (QuantumTime). Specifically, we introduce variational quantum circuits to address the challenge of representing high-frequency components in time series. Then, we introduce a reservoir that empowers QuantumTime with powerful extrapolation capabilities by exploiting the rich dynamical properties of reservoir computing. Ultimately, experiments conducted on chaotic datasets and various real-world datasets demonstrate that QuantumTime achieves highly competitive results compared to the state-of-the-art deep time-index model while reducing training parameters by at least 95%. Our approach provides a paradigm for utilizing potential quantum advantage in practical tasks.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709228"}, {"primary_key": "186513", "vector": [], "sparse_vector": [], "title": "Explainable LiDAR 3D Point Cloud Segmentation and Clustering for Detecting Airplane-Generated Wind Turbulence.", "authors": ["<PERSON><PERSON>", "Shuzhou Yuan", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Wake vortices-strong, coherent air turbulences created by aircrafts-pose a significant risk to aviation safety and therefore require accurate and reliable detection methods. In this paper, we present an advanced, explainable machine learning method that utilizes Light Detection and Ranging (LiDAR) data for effective wake vortex detection. Our method leverages a dynamic graph CNN (DGCNN) with semantic segmentation to partition a 3D LiDAR point cloud into meaningful segments. Further refinement is achieved through clustering techniques. A novel feature of our research is the use of a perturbation-based explanation technique, which clarifies the model's decision-making processes for air traffic regulators and controllers, increasing transparency and building trust. Our experimental results, based on measured and simulated LiDAR scans compared against four baseline methods, underscore the effectiveness and reliability of our approach. This combination of semantic segmentation and clustering for real-time wake vortex tracking significantly advances aviation safety measures, ensuring that these are both effective and comprehensible.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709436"}, {"primary_key": "186514", "vector": [], "sparse_vector": [], "title": "Fast Causal Discovery by Approximate Kernel-based Generalized Score Functions with Linear Computational Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Score-based causal discovery methods can effectively identify causal relationships by evaluating candidate graphs and selecting the one with the highest score. One popular class of scores is kernel-based generalized score functions, which can adapt to a wide range of scenarios and work well in practice because they circumvent assumptions about causal mechanisms and data distributions. Despite these advantages, kernel-based generalized score functions pose serious computational challenges in time and space, with a time complexity of O (n3) and a memory complexity of O (n2), where n is the sample size. In this paper, we propose an approximate kernel-based generalized score function with O (n) time and space complexities by using low-rank technique and designing a set of rules to handle the complex composite matrix operations required to calculate the score, as well as developing sampling algorithms for different data types to benefit the handling of diverse data types efficiently. Our extensive causal discovery experiments on both synthetic and real-world data demonstrate that compared to the state-of-the-art method, our method can not only significantly reduce computational costs, but also achieve comparable accuracy, especially for large datasets.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709338"}, {"primary_key": "186515", "vector": [], "sparse_vector": [], "title": "ST-MTM: Masked Time Series Modeling with Seasonal-Trend Decomposition for Time Series Forecasting.", "authors": ["Hyunwoo Seo", "Chiehyeon Lim"], "summary": "Forecasting complex time series is an important yet challenging problem that involves various industrial applications. Recently, masked time-series modeling has been proposed to effectively model temporal dependencies for forecasting by reconstructing masked segments from unmasked ones. However, since the semantic information in time series is involved in intricate temporal variations generated by multiple time series components, simply masking a raw time series ignores the inherent semantic structure, which may cause MTM to learn spurious temporal patterns present in the raw data. To capture distinct temporal semantics, we show that masked modeling techniques should address entangled patterns through a decomposition approach. Specifically, we propose ST-MTM, a masked time-series modeling framework with seasonal-trend decomposition, which includes a novel masking method for the seasonal-trend components that incorporates different temporal variations from each component. ST-MTM uses a period masking strategy for seasonal components to produce multiple masked seasonal series based on inherent multi-periodicity and a sub-series masking strategy for trend components to mask temporal regions that share similar variations. The proposed masking method presents an effective pre-training task for learning intricate temporal variations and dependencies. Additionally, ST-MTM introduces a contrastive learning task to support masked modeling by enhancing contextual consistency among multiple masked seasonal representations. Experimental results show that our proposed ST-MTM achieves consistently superior forecasting performance compared to existing masked modeling, contrastive learning, and supervised forecasting methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709254"}, {"primary_key": "186516", "vector": [], "sparse_vector": [], "title": "Towards Web-scale Recommendations with LLMs: From Quality-aware Ranking to Candidate Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongzhi Li"], "summary": "Explore Further @ Bing is a webpage-to-webpage recommendation product, enhancing the search experience on <PERSON> by surfacing engaging webpage recommendations tied to the search result URLs. In this paper, we present our approach for leveraging Large Language Models (LLMs) for enhancing our web-scale recommendation system. We describe the development and validation of our LLM-powered recommendation quality metric RecoDCG. We discuss our core techniques for utilizing LLMs to make our ranking stage quality-aware. Furthermore, we detail Q' recall, a recall path that enhances our system's candidate generation stage by leveraging LLMs to produce complementary and engaging recommendation candidates. We also address how we optimize our system for multiple objectives, balancing recommendation quality with click metrics. We deploy our work to production, achieving a significant improvement in recommendation quality. We share results from offline and online experiments as well as insights and steps we took to ensure our approaches scale effectively for our web-scale needs.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709413"}, {"primary_key": "186517", "vector": [], "sparse_vector": [], "title": "Abductive Learning for Neuro-Symbolic Grounded Imitation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent learning-to-imitation methods have shown promise in planning by imitating within the observation-action space, yet they remain constrained in open environments, especially for long-horizon tasks. In contrast, while traditional symbolic planning excels in such tasks through logical reasoning over human-defined symbolic spaces, it struggles with high-dimensional visual inputs encountered in real-world scenarios. In this work, we draw inspiration from abductive learning and introduce a novel framework ABductive Imitation Learning (ABIL) that integrates the benefits of data-driven learning and symbolic-based reasoning, enabling long-horizon planning. Specifically, we employ abductive reasoning to understand the demonstrations in symbolic space and design the principles of sequential consistency to resolve the conflicts between perception and reasoning. ABIL generates predicate candidates to facilitate the perception from raw observations to symbolic space without laborious predicate annotations, providing a groundwork for symbolic planning. With the symbolic understanding, we develop a policy ensemble with base policies designed around different logical objectives, managed through symbolic reasoning. Experiments demonstrate that our method successfully comprehends observations with task-relevant symbolics to aid imitation learning. Importantly, ABIL demonstrates improved data efficiency and generalization across various long-horizon tasks, highlighting it as a promising solution for long-horizon planning. Project website: https://www.lamda.nju.edu.cn/shaojj/KDD25_ABIL/.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709344"}, {"primary_key": "186518", "vector": [], "sparse_vector": [], "title": "Exploring Heterogeneity and Uncertainty for Graph-based Cognitive Diagnosis Models in Intelligent Education.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph-based Cognitive Diagnosis (CD) has attracted much research interest due to its strong ability on inferring students' proficiency levels on knowledge concepts. While graph-based CD models have demonstrated remarkable performance, we contend that they still cannot achieve optimal performance due to the neglect of edge heterogeneity and uncertainty. Edges involve both correct and incorrect response logs, indicating heterogeneity. Meanwhile, a response log can have uncertain semantic meanings, e.g., a correct log can indicate true mastery or fortunate guessing, and a wrong log can indicate a lack of understanding or a careless mistake. In this paper, we propose an Informative Semantic-aware Graph-based Cognitive Diagnosis model (ISG-CD), which focuses on how to utilize the heterogeneous graph in CD and minimize effects of uncertain edges. Specifically, to explore heterogeneity, we propose a semantic-aware graph neural networks based CD model. To minimize effects of edge uncertainty, we propose an Informative Edge Differentiation layer from an information bottleneck perspective, which suggests keeping a minimal yet sufficient reliable graph for CD in an unsupervised way. We formulate this process as maximizing mutual information between the reliable graph and response logs, while minimizing mutual information between the reliable graph and the original graph. After that, we prove that mutual information maximization can be theoretically converted to the classic binary cross entropy loss function, while minimizing mutual information can be realized by the Hilbert-Schmidt Independence Criterion.Finally, we adopt an alternating training strategy for optimizing learnable parameters of both the semantic-aware graph neural networks based CD model and the edge differentiation layer. Extensive experiments on three real-world datasets have demonstrated the effectiveness of ISG-CD.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709264"}, {"primary_key": "186520", "vector": [], "sparse_vector": [], "title": "HeavyLocker: Lock Heavy Hitters in Distributed Data Streams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, sketching has emerged as a pivotal technique for identifying heavy hitters (items with high frequency) in large-scale data streams. Despite this progress, the majority of existing sketch algorithms are tailored primarily for detecting local heavy hitters within a single data stream, with only a few capable of extending their application to global heavy hitters across distributed data streams. A common challenge encountered by these algorithms is balancing performance with accuracy. To address this challenge, we introduce HeavyLocker, a novel sketch algorithm that takes advantage of a distinct feature of real data streams: the separability of heavy hitters. By leveraging this attribute, HeavyLocker precisely locks and protects potential heavy hitters during the data stream processing, ensuring accuracy in local heavy hitter detection without compromising on speed. This unique capability also facilitates its application to global detection tasks. Through theoretical analysis, we validate the efficacy of HeavyLocker's locking mechanism. Our extensive experiments show that HeavyLocker outperforms five benchmarked algorithms in accuracy and maintains fast speed for both local and global heavy hitter detection, significantly reducing errors by up to an order of magnitude compared to the renowned Double-Anonymous Sketch.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709167"}, {"primary_key": "186521", "vector": [], "sparse_vector": [], "title": "AntAkso: Claims Management System for Health Insurance in Alipay.", "authors": ["Qitao Shi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xiao<PERSON> Qin"], "summary": "The rapid growth of health insurance and the rising incidence of fraudulent claims underscore the necessity for an efficient and professional claims management system. However, there is a noticeable lack of shared relevant experience from previous research in this field. In response to this challenge, we introduce AntAkso, a robust claims management system specifically designed for health insurance operations within Alipay. AntAkso incorporates a digital and professional management system, achieving a notable decrease in the volume of false claims, reduction in administrative costs, and heightened satisfaction among its policyholders. We begin by highlighting the core components of this system, including the case stratification, hospital recommendation, and case dispatch modules, along with the pivotal algorithms employed, i.e., the fraud detection, recommendation, and robust satisficing algorithms. We also detail the system's implementation and deployment. We substantiate the proposed system's effectiveness and efficiency with empirical evidence from experiments on a large set of real-world health insurance claims data.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709398"}, {"primary_key": "186522", "vector": [], "sparse_vector": [], "title": "Off-Policy Evaluation and Learning for the Future under Non-Stationarity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the novel problem of future off-policy evaluation (F-OPE) and learning (F-OPL) for estimating and optimizing the future value of policies in non-stationary environments, where distributions vary over time. In e-commerce recommendations, for instance, our goal is often to estimate and optimize the policy value for the upcoming month using data collected by an old policy in the previous month. A critical challenge is that data related to the future environment is not observed in the historical data. Existing methods assume stationarity or depend on restrictive reward-modeling assumptions, leading to significant bias. To address these limitations, we propose a novel estimator named Off-Policy Estimator for the Future Value (OPFV), designed for accurately estimating policy values at any future time point. The key feature of OPFV is its ability to leverage the useful structure within time-series data. While future data might not be present in the historical log, we can leverage, for example, seasonal, weekly, or holiday effects that are consistent in both the historical and future data. Our estimator is the first to exploit these time-related structures via a new type of importance weighting, enabling effective F-OPE. Theoretical analysis identifies the conditions under which OPFV becomes low-bias. In addition, we extend our estimator to develop a new policy-gradient method to proactively learn a good future policy using only historical data. Empirical results show that our methods substantially outperform existing methods in estimating and optimizing the future policy value under non-stationarity for various experimental setups.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709237"}, {"primary_key": "186523", "vector": [], "sparse_vector": [], "title": "CATER: A Cluster-Based Alternative-Term Recommendation Framework for Large-Scale Web Search at NAVER.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, searching for information by using search engines such as Google, Bing, and NAVER has become ubiquitous. While they attempt to provide information based on the search queries that users enter, it is not trivial to accurately capture the search intent of users. Motivated by this situation, NAVER Corp., the largest portal company in Korea, has developed a framework named as CATER (Cluster-based Alternative TErm Recommendation) framework that suggests alternative terms (\"al-terms,'' in short) for better search outcomes relevant to a user's search intent. We introduce four design considerations (DCs) that were considered when designing and implementing CATER. Then, we describe how our CATER addresses the four DCs by using a clustering stage that dynamically maintains a pool of topic-oriented clusters containing terms, and a recommendation stage that identifies the top-k clusters (i.e., topics) and the top-k al-terms for each cluster. Furthermore, we present the scalable architecture adopted by CATER. Through various offline and online A/B tests using real-world datasets from NAVER, we validate that CATER successfully incorporates all DCs and that all design choices help improve the recommendation accuracy.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709426"}, {"primary_key": "186524", "vector": [], "sparse_vector": [], "title": "Covering Cracks in Content Moderation: Delexicalized Distant Supervision for Illicit Drug Jargon Detection.", "authors": ["<PERSON><PERSON><PERSON> Song", "<PERSON>", "<PERSON><PERSON><PERSON>", "Seungwon Shin"], "summary": "In light of rising drug-related concerns and the increasing role of social media, sales and discussions of illicit drugs have become commonplace online. Social media platforms hosting user-generated content must therefore perform content moderation, which is a difficult task due to the vast amount of jargon used in drug discussions. Previous works on drug jargon detection were limited to extracting a list of terms, but these approaches have fundamental problems in practical application. First, they are trivially evaded using word substitutions. Second, they cannot distinguish whether euphemistic terms (pot, crack) are being used as drugs or as their benign meanings. We argue that drug content moderation should be done using contexts, rather than relying on a banlist. However, manually annotated datasets for training such a task are not only expensive but also prone to becoming obsolete. We present JEDIS, a framework for detecting illicit drug jargon terms by analyzing their contexts. JEDIS utilizes a novel approach that combines distant supervision and delexicalization, which allows JEDIS to be trained without human-labeled data while being robust to new terms and euphemisms. Experiments on two manually annotated datasets show JEDIS significantly outperforms state-of-the-art word-based baselines in terms of F1-score and detection coverage in drug jargon detection. We also conduct qualitative analysis that demonstrates JEDIS is robust against pitfalls faced by existing approaches.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709183"}, {"primary_key": "186525", "vector": [], "sparse_vector": [], "title": "Counterfactual Explanations with Probabilistic Guarantees on their Robustness to Model Change.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Counterfactual explanations (CFEs) guide users on how to adjust inputs to machine learning models to achieve desired outputs. While existing research primarily addresses static scenarios, real-world applications often involve data or model changes, potentially invalidating previously generated CFEs and rendering user-induced input changes ineffective. Current methods addressing this issue often support only specific models or change types, require extensive hyperparameter tuning, or fail to provide probabilistic guarantees on CFE robustness to model changes. This paper proposes a novel approach for generating CFEs that provides probabilistic guarantees for any model and change type, while offering interpretable and easy-to-select hyperparameters. We establish a theoretical framework for probabilistically defining robustness to model change and demonstrate how our BetaRCE method directly stems from it. BetaRCE is a post-hoc method applied alongside a chosen base CFE generation method to enhance the quality of the explanation beyond robustness. It facilitates a transition from the base explanation to a more robust one with user-adjusted probability bounds. Through experimental comparisons with baselines, we show that BetaRCE yields robust, most plausible, and closest to baseline counterfactual explanations.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709300"}, {"primary_key": "186526", "vector": [], "sparse_vector": [], "title": "CLEAR: Addressing Representation Contamination in Multimodal Healthcare Analytics.", "authors": ["Ge Su", "<PERSON><PERSON>", "Tiancheng Zhao", "<PERSON><PERSON><PERSON>"], "summary": "Electronic health records (EHRs) are the de facto standard for analyzing comprehensive patient conditions. Existing methods mainly employ specialized neural networks to extract modality-specific information, followed by modality correlation modeling to support clinical decision-making. However, these methods generally overlook the issue of ''contaminated'' representations inherent in routine EHR data, which can undermine the model's discriminative ability, as less relevant representations associated with false positive correlations may impede the recognition of truly effective representations. To address the issue of representation contamination, we propose CLEAR, a counterfactual disparity learning model for explicit multimodal EHR analytics. The core idea is to first model the contamination in representations, and subsequently perform calibration and enhancement to construct highly discriminative representations. Specifically, CLEAR first proposes the Counterfactual Prompt Learning Module to capture the representation discrepancy to model representation contamination. Subsequently, an Adaptive Dynamic Imputation Module is devised to decouple the elementwise representations for representation calibration, while a gating mechanism is further proposed to incorporate discriminative discrepancy information for representation enhancement. Finally, the Multimodal Representation Fusion Module establishes intra- and inter-modality correlations, thereby creating a seamless integration towards downstream analytic tasks. To our knowledge, CLEAR is the first to model and resolve representation contamination in multimodal EHR analytics. Experimental results on two real-world datasets demonstrate that CLEAR consistently outperforms state-of-the-art baselines in facilitating multimodal healthcare analytics.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709164"}, {"primary_key": "186527", "vector": [], "sparse_vector": [], "title": "A Framework for Leveraging Partially-Labeled Data for Product Attribute-Value Identification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mausam", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the e-commerce domain, the accurate extraction of attribute-value pairs (e.g., Brand: Apple) from product titles and user search queries is crucial for enhancing search and recommendation systems. A major challenge with neural models for this task is the lack of high-quality training data, as the annotations for attribute-value pairs in the available datasets are often incomplete. To address this, we introduce GenToC, a model designed for training directly with partially-labeled data, eliminating the necessity for a fully annotated dataset. GenToC employs a marker-augmented generative model to identify potential attributes, followed by a token classification model that determines the associated values for each attribute. GenToC outperforms existing state-of-the-art models, exhibiting upto 56.3% increase in the number of accurate extractions. Furthermore, we utilize GenToC to regenerate the training dataset to expand attribute-value annotations. This bootstrapping substantially improves the data quality for training other standard NER models, which are typically faster but less capable in handling partially-labeled data, enabling them to achieve comparable performance to GenToC. Our results demonstrate GenToC's unique ability to learn from a limited set of partially-labeled data and improve the training of more efficient models, advancing the automated extraction of attribute-value pairs. Finally, our model has been successfully integrated into IndiaMART, India's largest B2B e-commerce platform, achieving a significant increase of 20.2% in the number of correctly identified attribute-value pairs over the existing deployed system while achieving a high precision of 89.5%. We have released the code for GenToC model at https://github.com/KnowDisAI/GenToC.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709427"}, {"primary_key": "186530", "vector": [], "sparse_vector": [], "title": "Unifying Adversarial Multi-Deconfounded Learning Paradigm for Fake News Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the task of fake news detection, ensuring authenticity and accuracy is of paramount importance. This task, however, is susceptible to the influence of confounders, necessitating effective confounder debiasing strategies. Conventional methods are typically designed to address specific confounders, resulting in frameworks that relatively lack generalization and overlook potential correlations among confounders. The presence of multiple confounders further escalates the complexity and challenges of debiasing learning. To tackle this issue, we introduce the Adversarial Multi-Deconfounded (AMD) Learning Paradigm, a generic training framework designed to eliminate biases from multiple confounders. Our approach leverages adversarial networks to extract confounder-invariant feature representations, guiding the model to ignore potential biases introduced by confounders and extract stable representations independent of these confounders, thereby enhancing generalization. Comprehensive experiments demonstrate that our approach outperforms state-of-the-art methods on the Weibo and GossipCop datasets, and significantly exceeds other methods in generalization evaluation on CHEF. Additionally, we validate that our AMD framework exhibits improved robustness against confounders.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709406"}, {"primary_key": "186531", "vector": [], "sparse_vector": [], "title": "Struct-X: Enhancing the Reasoning Capabilities of Large Language Models in Structured Data Scenarios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yuan Qi"], "summary": "Conducting reasoning tasks with large language models (LLMs) on structured and redundant data poses significant challenges, primarily due to the complexity introduced by the structured markdown tokens and the presence of extraneous contextual information. These elements can overburden and disrupt the generation process of LLMs, complicating the extraction of relevant insights and the production of coherent outputs. To address this, we propose Struct-X, a novel framework that operates through five key phases: ''read-model-fill-reflect-reason'' efficiently enabling LLMs to utilize structured data. It begins by encoding structured data into a topological space using graph embeddings, followed by filling in missing entity information with knowledge retrieval modules, and filtering out irrelevant tokens via a self-supervised module. The final phase involves constructing a topological network with selected tokens to further reduce the total token length for more effective LLM inference. Additionally, Struct-X includes an Auxiliary Module trained to generate prompts, aiding LLMs in analyzing structured data. Extensive experiments on open-source benchmarks, including the knowledge graph question-answer task and the long document reading comprehension task, show that Struct-X notably improves LLM reasoning in complex structured input context. Finally, we deployed Struct-X in a real-world financial report analysis task, where it exhibits enhanced reasoning capabilities when applied to authentic scenario. The code has been undergoing open-source development to facilitate easy replication.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709381"}, {"primary_key": "186532", "vector": [], "sparse_vector": [], "title": "Spatially Compact Dense Block Mining in Spatial Tensors.", "authors": ["<PERSON><PERSON>", "<PERSON>g<PERSON>", "<PERSON><PERSON> <PERSON>", "Kezhong Lu"], "summary": "Spatial tensors have been extensively used in a wide range of applications, including remote sensing, geospatial information systems, conservation planning, and urban planning. We study the problem of Spatially Compact Dense (SCD) block mining in a spatial tensor, which targets for discovering dense blocks that cover small spatial regions. However, most of existing dense block mining (DBM) algorithms cannot solve the SCD-block mining problem since they only focus on maximizing the density of candidate blocks, so that the discovered blocks are spatially loose, i.e., covering large spatial regions. Therefore, we first formulate the problem of mining top-k Spatially Compact Dense blocks (SCD-blocks) in spatial tensors, which ranks SCD-blocks based on a new scoring function that takes both the density value and the spatial coverage into account. Then, we adopt a filter-refinement framework that first generates candidate SCD-blocks with good scores in the filtering phase and then uses the traditional DBM algorithm to further maximize the density values of the candidates in the refinement phase. Due to the NP-hardness of the problem, we develop two types of solutions in the filtering phase, namely the top-down solution and the bottom-up solution, which can find good candidate SCD-blocks by approximately solving the new scoring function. The evaluations on four real datasets verify that compared with the dense blocks returned by existing DBM algorithms, the proposed solutions are able to find SCD-blocks with comparable density values and significantly smaller spatial coverage.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709221"}, {"primary_key": "186534", "vector": [], "sparse_vector": [], "title": "R2MR: Review and Rewrite Modality for Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the explosive growth of online multimodal content, multimodal recommender systems(MRSs) have brought significant benefits to multimedia platforms. As MRSs evolve, many studies incorporate advanced technologies like graph neural networks(GNNs) and self-supervised learning(SSL), achieving remarkable results. However, these efforts still suffer from the quality disparity problem. It refers to the mixture of high and low quality across items' multiple modalities, owing to disparities in construction costs or design levels. These low-quality modalities often lack crucial details or introduce noise to the depiction of item, leading to insufficient or polluted item representation. Therefore, we propose a novel framework R2MR: Review and Rewrite Modality for Recommendation to tackle this issue. Specifically, R2MR is composed of two key components: Modality Reviewer and Modality Rewriter. The Modality Reviewer introduces a Consensus Review Mechanism. It performs perspective decomposition based on user representations and learns the consensus quality scores for modalities from diverse perspectives of multiple users. The Modality Rewriter proposes a Latent Mapping Model, which improves the quality of inferior modalities by learning various mapping patterns from high-quality modalities. Comprehensive experiments across three benchmark datasets reveal that R2MR substantially outperforms state-of-the-art methods, achieving an average improvement of 9.20%. The implementations are available at https://github.com/gutang-97/R2MR.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709250"}, {"primary_key": "186537", "vector": [], "sparse_vector": [], "title": "From Missteps to Mastery: Enhancing Low-Resource Dense Retrieval through Adaptive Query Generation.", "authors": ["Zhenyu Tong", "<PERSON><PERSON>", "Chuyu Fang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Document retrieval, designed to recall query-relevant documents from expansive collections, is essential for information-seeking tasks, such as web search and open-domain question-answering. Advances in representation learning and pretrained language models (PLMs) have driven a paradigm shift from traditional sparse retrieval methods to more effective dense retrieval approaches, forging enhanced semantic connections between queries and documents and establishing new performance benchmarks. However, reliance on extensive annotated document-query pairs limits their competitiveness in low-resource scenarios. Recent research efforts employing the few-shot capabilities of large language models (LLMs) and prompt engineering for synthetic data generation have emerged as a promising solution. Nonetheless, these approaches are hindered by the generation of lower-quality data within the conventional dense retrieval training process. To this end, in this paper, we introduce iGFT, a framework aimed at enhancing low-resource dense retrieval by integrating a three-phase process --- Generation, Filtering, and Tuning --- coupled with an iterative optimization strategy. Specifically, we first employ supervised fine-tuning on limited ground truth data, enabling an LLM to function as the generator capable of producing potential queries from given documents. Subsequently, we present a multi-stage filtering module to minimize noise in the generated data while retaining samples poised to significantly improve the dense retrieval model's performance in the follow-up fine-tuning process. Furthermore, we design a novel iterative optimization strategy that dynamically optimizes the query generator for producing more informative queries, thereby enhancing the efficacy of the entire framework. Finally, extensive experiments conducted on a series of publicly available retrieval benchmark datasets have demonstrated the effectiveness of the proposed iGFT.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709225"}, {"primary_key": "186538", "vector": [], "sparse_vector": [], "title": "GROOT: Effective Design of Biological Sequences with Limited Experimental Data.", "authors": ["Thanh V. T. Tran", "<PERSON><PERSON>", "<PERSON><PERSON>", "Truong Son Hy"], "summary": "Latent space optimization (LSO) is a powerful method for designing discrete, high-dimensional biological sequences that maximize expensive black-box functions, such as wet lab experiments. This is accomplished by learning a latent space from available data and using a surrogate model fΦ to guide optimization algorithms toward optimal outputs. However, existing methods struggle when labeled data is limited, as training fΦ with few labeled data points can lead to subpar outputs, offering no advantage over the training data itself. We address this challenge by introducing GROOT, a GRaph-based Latent SmOOThing for Biological Sequence Optimization. In particular, GROOT generates pseudo-labels for neighbors sampled around the training latent embeddings. These pseudo-labels are then refined and smoothed by Label Propagation. Additionally, we theoretically and empirically justify our approach, demonstrate GROOT's ability to extrapolate to regions beyond the training set while maintaining reliability within an upper bound of their expected distances from the training regions. We evaluate GROOT on various biological sequence design tasks, including protein optimization (GFP and AAV) and three tasks with exact oracles from Design-Bench. The results demonstrate that GROOT equalizes and surpasses existing methods without requiring access to black-box oracles or vast amounts of labeled data, highlighting its practicality and effectiveness. We release our code at https://github.com/Fsoft-AIC/GROOT.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709291"}, {"primary_key": "186539", "vector": [], "sparse_vector": [], "title": "Cross-Species Insights: Transforming Drug Efficacy from Rats to Humans Using Tissue-Specific Generative Models.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Within the realm of drug development, the transition from successful animal trials to human clinical efficacy remains a daunting challenge. While initial outcomes may appear promising in animal studies, ensuring similar effectiveness in humans, especially across specific target tissues, presents a significant obstacle. To address this pressing concern, we introduce a novel generative model tailored to optimize molecules that have demonstrated efficacy in rats for enhanced performance in specific human tissues. Central to our solution is the transformer architecture, enhanced with intricate mechanisms such as molecule self-attention within the encoder and a novel dedicated tissue-specific generator. Intuitively, by learning to generate molecules simultaneously from multiple tissues, the generative model enhances its ability to perform the necessary adaptations from rats to humans. Through rigorous empirical evaluation across various tissues, our model consistently exhibits remarkable efficacy compared to existing methods. We anticipate that this model has the potential to minimize the requirement for lengthy and inconclusive trials, thereby streamlining the drug development process.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709389"}, {"primary_key": "186540", "vector": [], "sparse_vector": [], "title": "How Well Calibrated are Extreme Multi-label Classifiers? An Empirical Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Extreme multilabel classification (XMLC) problems occur in settings such as related product recommendation, large-scale document tagging, or ad prediction, and are characterized by a label space that can span millions of possible labels. There are two implicit tasks that the classifier performs: Evaluating each potential label for its expected worth, and then selecting the best candidates. For the latter task, only the relative order of scores matters, and this is what is captured by the standard evaluation procedure in the XMLC literature. However, in many practical applications, it is important to have a good estimate of the actual probability of a label being relevant, e.g., to decide whether to pay the fee to be allowed to display the corresponding ad. To judge whether an extreme classifier is indeed suited to this task, one can look, for example, to whether it returns calibrated probabilities, which has hitherto not been done in this field. Therefore, this paper aims to establish the current status quo of calibration in XMLC by providing a systematic evaluation, comprising nine models from four different model families across seven benchmark datasets. As naive application of Expected Calibration Error (ECE) leads to meaningless results in long-tailed XMC datasets, we instead introduce the notion of calibration@k (e.g., ECE@k), which focusses on the top-k probability mass, offering a more appropriate measure for evaluating probability calibration in XMLC scenarios. While we find that different models can exhibit widely varying reliability plots, we also show that post-training calibration via a computationally efficient isotonic regression method enhances model calibration without sacrificing prediction accuracy. Thus, the practitioner can choose the model family based on accuracy considerations, and leave calibration to isotonic regression.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709333"}, {"primary_key": "186541", "vector": [], "sparse_vector": [], "title": "Scaling the Vocabulary of Non-autoregressive Models for Fast Generative Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Generative Retrieval introduces a new approach to Information Retrieval by reframing it as a constrained generation task, leveraging recent advancements in Autoregressive (AR) language models. However, AR-based Generative Retrieval methods suffer from high inference latency and cost compared to traditional dense retrieval techniques, limiting their practical applicability. This paper investigates fully Non-autoregressive (NAR) language models as a more efficient alternative for generative retrieval. While standard NAR models alleviate latency and cost concerns, they exhibit a significant drop in retrieval performance (compared to AR models) due to their inability to capture dependencies between target tokens. To address this, we question the conventional choice of limiting the target token space to solely words or sub-words. We propose PIXNAR, a novel approach that expands the target vocabulary of NAR models to include multi-word entities and common phrases (up to 5 million tokens), thereby reducing token dependencies. PIXNAR employs inference optimization strategies to maintain low inference latency despite the significantly larger vocabulary. Our results demonstrate that PIXNAR achieves a relative improvement of 31.0% in MRR@10 on MS MARCO and 23.2% in Hits@5 on Natural Questions compared to standard NAR models with similar latency and cost. Furthermore, online A/B experiments on a large commercial search engine show significant increase in clicks and revenue.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709330"}, {"primary_key": "186545", "vector": [], "sparse_vector": [], "title": "Noise-Resilient Point-wise Anomaly Detection in Time Series Using Weak Segment Labels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Qingsong Wen", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Detecting anomalies in temporal data has gained significant attention across various real-world applications, aiming to identify unusual events and mitigate potential hazards. In practice, situations often involve a mix of segment-level labels (detected abnormal events with segments of time points) and unlabeled data (undetected events), while the ideal algorithmic outcome should be point-level predictions. Therefore, the huge label information gap between training data and targets makes the task challenging. In this study, we formulate the above imperfect information as noisy labels and propose NRdetector, a noise-resilient framework that incorporates confidence-based sample selection, robust segment-level learning, and data-centric point-level detection for multivariate time series anomaly detection. Particularly, to bridge the information gap between noisy segment-level labels and missing point-level labels, we develop a novel loss function that can effectively mitigate the label noise and consider the temporal features. It encourages the smoothness of consecutive points and the separability of points from segments with different labels. Extensive experiments on real-world multivariate time series datasets with 11 different evaluation metrics demonstrate that NRdetector consistently achieves robust results across multiple real-world datasets, outperforming various baselines adapted to operate in our setting.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709257"}, {"primary_key": "186546", "vector": [], "sparse_vector": [], "title": "Producer-Side Experiments Based on Counterfactual Interleaving Designs for Online Recommender Systems.", "authors": ["<PERSON>", "Shan Ba"], "summary": "Recommender systems play a crucial role in online platforms, providing personalized recommendations for purchases, content consumption, and interpersonal connections. These systems involve two sides: producers (sellers, content creators, service providers, etc.) and consumers (buyers, viewers, customers, etc.). To optimize online recommender systems, A/B tests serve as the golden standard for comparing different ranking models and evaluating their impacts on both sides. While consumer-side experiments are relatively straightforward to design and commonly employed to assess ranking changes' effects on the behavior of consumers (buyers, viewers, etc.), designing producer-side experiments for an online recommender/ranking system is notably more complex. This complexity arises from the necessity of ranking producer items in the treatment and control groups by different models and then merging them into a unified ranking for presentation to each consumer. Existing design solutions in the literature lack rigorous guiding principles, leading to ad hoc approaches. In this paper, we address the limitations of current methods and propose the principles of consistency and monotonicity for designing producer-side experiments in online recommender systems. Building upon these principles, we also present a systematic solution based on counterfactual interleaving designs to accurately measure the impacts of ranking changes on the producers (sellers, content creators, etc.).", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709428"}, {"primary_key": "186547", "vector": [], "sparse_vector": [], "title": "Beyond Item Dissimilarities: Diversifying by Intent in Recommender Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It has become increasingly clear that recommender systems that overly focus on short-term engagement prevents users from exploring diverse interests, ultimately hurting long-term user experience.To tackle this challenge, numerous diversification algorithms have been proposed as the final stage of recommender systems.These algorithms typically rely on measures of item similarity, aiming to maximize the dissimilarity across items in the final set of recommendations.However, in this work, we demonstrate the benefits of going beyond item-level similarities by utilizing higher-level user understanding-specifically, user intents that persist across multiple interactions or recommendation sessions-in diversification.Our approach is motivated by the observation that user behaviors on online platforms are largely driven by their underlying intents.Therefore, recommendations should ensure that diverse user intents are accurately represented.While intent has primarily been studied in the context of search, it is less clear how to incorporate real-time dynamic intent predictions into recommender systems.To address this gap, we develop a probabilistic intent-based whole-page diversification framework for the final stage of a recommender system.Starting with a prior belief of user intents, the proposed framework sequentially selects items for each position based on these beliefs and subsequently updates posterior beliefs about the intents.This approach ensures that different user intents are represented on a page, towards optimizing long-term user experience.We experiment with the intent diversification framework on YouTube, the world's largest video recommendation platform, serving billions of users daily.Live experiments on a diverse set of", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709429"}, {"primary_key": "186548", "vector": [], "sparse_vector": [], "title": "HoME: Hierarchy of Multi-Gate Experts for Multi-Task Learning at Kuaishou.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present the practical problems and the lessons learned at short-video services from Kuaishou. In industry, a widely-used multi-task framework is the Mixture-of-Experts (MoE) paradigm, which always introduces some shared and specific experts for each task and then uses gate networks to measure related experts' contributions. Although the MoE achieves remarkable improvements, we still observe three anomalies that seriously affect model performances in our iteration: (1) Expert Collapse: We found that experts' output distributions are significantly different, and some experts have over 90% zero activations with ReLU, making it hard for gate networks to assign fair weights to balance experts. (2) Expert Degradation: Ideally, the shared-expert aims to provide predictive information for all tasks simultaneously. Nevertheless, we find that some shared-experts are occupied by only one task, which indicates that shared-experts lost their ability but degenerated into some specific-experts. (3) Expert Underfitting: In our services, we have dozens of behavior tasks that need to be predicted, but we find that some data-sparse prediction tasks tend to ignore their specific-experts and assign large weights to shared-experts. The reason might be that the shared-experts can perceive more gradient updates and knowledge from dense tasks, while specific-experts easily fall into underfitting due to their sparse behaviors. Motivated by those observations, we propose HoME to achieve a simple, efficient and balanced MoE system for multi-task learning. Specifically, we conduct three insightful modifications: (1) Expert normalization&Swish mechanism to align expert output distributions and avoid expert collapse. (2) Hierarchy mask mechanism to enhance sharing efficiency between tasks to reduce occupancy issues and away from expert degradation. (3) Feature-gate&Self-gate mechanisms to ensure each expert could obtain appropriate gradient to maximize its effectiveness. To our knowledge, this paper is the first work to focus on improving multi-task MoE system stability, and we conduct extensive offline&online (average improves 0.52% GAUC offline & 0.954% play-time per user online) experiments and ablation analyses to demonstrate our HoME effectiveness. From March 2024, our HoME has been widely deployed on various services at Kuaishou, supporting 400 Million active users daily.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709416"}, {"primary_key": "186550", "vector": [], "sparse_vector": [], "title": "Mitigating Redundancy in Deep Recommender Systems: A Field Importance Distribution Perspective.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Li<PERSON><PERSON> Wu", "<PERSON><PERSON>", "Haitao Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the realm of recommender systems, accurately predicting Click-Through Rate (CTR) is a critical task that involves learning user-item interaction features. Many researchers propose novel models to mine interaction signals, but they neglect that redundancy itself causes high computational cost and leads to suboptimal performance. Some tried to remove redundancy by dropping useless features, or shrinking the size of embedding table. However, current feature selection methods are vulnerable to training stochasticity and data dynamics, while embedding size assignment techniques neglect the importance relationships between feature fields. The simple combination of the two optimization ways will also yield poor performance due to the inherent gap in their optimization targets. Hence, there is no effective paradigm that can optimize feature fields from the two aspects in a simultaneous and coordinated way. In this paper, we identify the core issue as the lack of a practical score to measure the contribution of feature fields, and propose a distribution-based field optimization framework that adopts importance distribution to provide a comprehensive view for both methods. We innovatively design a learner for each field to acquire the stable and comprehensive importance situation. Then, based on this, we eliminate noise features, and assign adaptive embedding sizes for different feature fields according to the similarity of importance. With this field optimization, our proposed framework has extremely low pre-training overhead, greatly reduces training and inference time, and even achieves more accurate prediction results with fewer feature fields.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709275"}, {"primary_key": "186551", "vector": [], "sparse_vector": [], "title": "SoAy: A Solution-based LLM API-using Methodology for Academic Information Seeking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shangqing Tu", "Yiyang Fu", "<PERSON><PERSON>", "Jin<PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yuan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Applying large language models (LLMs) to academic API usage shows promise in reducing researchers' efforts to seek academic information. However, current LLM methods for using APIs struggle with the complex API coupling commonly encountered in academic queries. To address this, we introduce SoAy, a solution-based LLM methodology for academic information seeking. SoAy enables LLMs to generate code for invoking APIs, guided by a pre-constructed API calling sequence referred to as a solution. This solution simplifies the model's understanding of complex API relationships, while the generated code enhances reasoning efficiency. LLMs are aligned with this solution-oriented, code-based reasoning method by automatically enumerating valid API coupling sequences and transforming them into queries and executable code. To evaluate SoAy, we introduce SoAyBench, an evaluation benchmark accompanied by SoAyEval, built upon a cloned environment of APIs from AMiner. Experimental results demonstrate a 34.58-75.99% performance improvement compared to state-of-the-art LLM API-based baselines. All datasets, codes, tuned models, and deployed online services are publicly accessible at https://github.com/RUCKBReasoning/SoAy.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709412"}, {"primary_key": "186552", "vector": [], "sparse_vector": [], "title": "Runtime-Aware Pipeline for Vertical Federated Learning with Bounded Model Staleness.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yuqing Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vertical federated learning (VFL) enables a privacy-preserving collaboration among various parties to train a global model by melding their geo-distributed data features. Communication has been recognized as the primary bottleneck that impairs training efficiency due to frequent cross-party statistics exchange over wide area network. Existing synchronous VFL works often suffer from excessive communication overhead, while asynchronous schemes may introduce significant model staleness, potentially eroding the learning accuracy. In this paper, we propose BS-VFL, an asynchronous VFL with bounded staleness, to pipeline local computation and statistics transmission, substantially reducing the communication overhead while ensuring favorable model performance. Specifically, all data parties will give precedence to local model updates before generating embeddings to curtail model staleness. By analyzing convergence error, we show that BS-VFL can achieve a comparable result to synchronous VFL. Then, we develop a general framework to derive the closed-form wall-clock time of BS-VFL, offering a measure of its runtime efficiency and highlighting a marked communication reduction. Utilizing this convergence and time analysis, we refine learning parameters to minimize the convergence error for optimizing BS-VFL performance without compromising training efficiency. Extensive experiments on real-world datasets validate the superiority of BS-VFL over leading-edge methods, evidencing a reduction in training duration by 48%-90% while preserving model accuracy.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709243"}, {"primary_key": "186553", "vector": [], "sparse_vector": [], "title": "Graph Triple Attention Networks: A Decoupled Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Haizhou Shi", "<PERSON><PERSON><PERSON>", "Chun<PERSON><PERSON> Hong"], "summary": "Graph Transformers (GTs) have recently achieved significant success in the graph domain by effectively capturing both long-range dependencies and graph inductive biases. However, these methods face two primary challenges: (1) multi-view chaos, which results from coupling multi-view information (positional, structural, attribute), thereby impeding flexible usage and the interpretability of the propagation process. (2) local-global chaos, which arises from coupling local message passing with global attention, leading to issues of overfitting and over-globalizing. To address these challenges, we propose a high-level decoupled perspective of GTs, breaking them down into three components and two interaction levels: positional attention, structural attention, and attribute attention, alongside local and global interaction. Based on this decoupled perspective, we design a decoupled graph triple attention network named DeGTA, which separately computes multi-view attentions and adaptively integrates multi-view local and global information. This approach offers three key advantages: enhanced interpretability, flexible design, and adaptive integration of local and global information. Through extensive experiments, DeGTA achieves state-of-the-art performance across various datasets and tasks, including node classification and graph classification. Comprehensive ablation studies demonstrate that decoupling is essential for improving performance and enhancing interpretability. Our code is available at: https://github.com/wangxiaotang0906/DeGTA", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709223"}, {"primary_key": "186554", "vector": [], "sparse_vector": [], "title": "Breaker: Removing Shortcut Cues with User Clustering for Single-slot Recommendation System.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "An You", "<PERSON>"], "summary": "In a single-slot recommendation system, users are only exposed to one item at a time, and the system cannot collect user feedback on multiple items simultaneously. Therefore, only pointwise modeling solutions can be adopted, focusing solely on modeling the likelihood of clicks or conversions for items by users to learn user-item preferences, without the ability to capture the ranking information among different items directly. However, since user-side information is often much more abundant than item-side information, the model can quickly learn the differences in user intrinsic tendencies, which are independent of the items they are exposed to. This can cause these intrinsic tendencies to become a shortcut bias for the model, leading to insufficient mining of the most concerned user-item preferences. To solve this challenge, we introduce the Breaker model. Break<PERSON> integrates an auxiliary task of user representation clustering with a multi-tower structure for cluster-specific preference modeling. By clustering user representations, we ensure that users within each cluster exhibit similar characteristics, which increases the complexity of the pointwise recommendation task on the user side. This forces the multi-tower structure with cluster-driven parameter learning to better model user-item preferences, ultimately eliminating shortcut biases related to user intrinsic tendencies. In terms of training, we propose a delayed parameter update mechanism to enhance training stability and convergence, enabling end-to-end joint training of the auxiliary clustering and classification tasks. Both offline and online experiments demonstrate that our method surpasses the baselines. It has already been deployed and is actively serving tens of millions of users daily on Meituan, one of the most popular e-commerce platforms for services.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709387"}, {"primary_key": "186555", "vector": [], "sparse_vector": [], "title": "Connecting Domains and Contrasting Samples: A Ladder for Domain Generalization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distribution shifts between training and testing samples frequently occur in practice and impede model generalization performance. This crucial challenge thereby motivates studies on domain generalization (DG), which aim to predict the label on unseen target domain data by solely using data from source domains. It is intuitive to conceive the class-separated representations learned in contrastive learning (CL) are able to improve DG, while the reality is quite the opposite: users observe directly applying CL deteriorates the performance. We analyze the phenomenon with the insights from CL theory and discover lack of intra-class connectivity in the DG setting causes the deficiency. We thus propose a new paradigm, domain-connecting contrastive learning (DCCL), to enhance the conceptual connectivity across domains and obtain generalizable representations for DG. On the data side, more aggressive data augmentation and cross-domain positive samples are introduced to improve intra-class connectivity. On the model side, to better embed the unseen test domains, we propose model anchoring to exploit the intra-class connectivity in pre-trained representations and complement the anchoring with generative transformation loss. Extensive experiments on five standard DG benchmarks are performed. The results verify that DCCL outperforms state-of-the-art baselines even without domain supervision. The detailed model implementation and the code are provided through https://github.com/weitianxin/DCCL.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709280"}, {"primary_key": "186559", "vector": [], "sparse_vector": [], "title": "Breaking the Memory Wall for Heterogeneous Federated Learning via Progressive Training.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) enables multiple devices to collaboratively train a shared model while preserving data privacy. Most existing research assumes that all participating devices have sufficient resources to support the training process. However, the high memory requirements of model training present a significant challenge to deploying FL on resource-constrained devices in practical scenarios. To this end, this paper presents ProFL, a new framework that effectively addresses the memory constraints in FL. Rather than updating the full model during local training, ProFL partitions the model into blocks based on its original architecture and trains each block in a progressive fashion. It first trains the front blocks and safely freezes them after convergence. Training of the next block is then triggered. This process progressively grows the model to be trained until the training of the full model is completed. In this way, the peak memory footprint is effectively reduced for feasible deployment on heterogeneous devices. In order to preserve the feature representation of each block, the training process is divided into two stages: model shrinking and model growing. During the model shrinking stage, we meticulously design corresponding output modules to assist each block in learning the expected feature representation and obtain the initialization model parameters. Subsequently, the obtained output modules and initialization model parameters are utilized in the corresponding model growing stage, which progressively trains the full model. Additionally, a novel metric from the scalar perspective is proposed to assess the learning status of each block, enabling us to securely freeze it after convergence and initiate the training of the next one. Finally, we theoretically prove the convergence of ProFL and conduct extensive experiments on representative models and datasets to evaluate its effectiveness. The results demonstrate that ProFL effectively reduces the peak memory footprint by up to 57.4% and improves model accuracy by up to 82.4%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709284"}, {"primary_key": "186560", "vector": [], "sparse_vector": [], "title": "ProgDiffusion: Progressively Self-encoding Diffusion Models.", "authors": ["<PERSON><PERSON> Wu", "<PERSON><PERSON>", "<PERSON><PERSON> Cao"], "summary": "Learning low-dimensional semantic representations in diffusion models (DMs) is an open task, since in standard DMs, the dimensions of its intermediate latents are the same as that of the observations and thus are unable to represent low-dimensional semantics. Existing methods address this task either by encoding observations into semantics which makes it difficult to generate samples without observations, or by synthesizing the U-Net's layers of pre-trained DMs into low-dimensional semantics, which is mainly used for downstream tasks rather than using semantics to facilitate the training process. Further, those generated static representations might not be aligned with dynamic timestep-wise intermediate latents. This work introduces a Progressive self-encoded Diffusion model (ProgDiffusion), which simultaneously learns semantic representations and reconstructs observations, does efficient unconditional generation, and produces progressively structured semantic representations. These benefits are gained by a novel self-encoder mechanism which takes the U-Net's upsampling features, intermediate latent and the denoising timestep as conditions to generate time-specific semantic representations, differing from existing work of conditioning on observations only. As a result, the learned intermediate latents are dynamic and mapped to a series of semantic representations that capture their gradual changes. Notably, our proposed encoder operates independently of the observations, making it feasible for unconditional generation as observations are not required. To evaluate ProgDiffusion, we design tasks to visualise the learned progressive semantic representations, in addition to other common tasks, which validate the effectiveness of ProgDiffusion against the state-of-the-art. The code is available at https://github.com/amasawa/ProgDiffusion.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709222"}, {"primary_key": "186561", "vector": [], "sparse_vector": [], "title": "Classifying Treatment Responders: Bounds and Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Treatment responders are individuals whose outcomes would change from negative to positive if treated, and learning a classifier to predict responders would help causal decision-making in real applications. Although many treatment effect estimation methods have been proposed to identify treatment responders, there are fundamental differences between treatment effect estimation and treatment responder classification, including: (1) accurate causal effect estimation is not necessary for optimal intervention decisions; (2) methods for accurate causal effect estimation do not directly optimize classification loss; (3) treatment responder classification requires identifying joint potential outcomes, while treatment effect estimation focuses on marginal distributions. To fill this gap, we tackle the treatment responder classification problem without assuming monotonicity. We derive sharp bounds of the probability that an individual is a responder and determine a sharp upper bound on the weighted classification risk to measure the worst classification performance. Based on these findings, we further propose a Classifying Treatment Responder Learning (CTRL) algorithm to accurately identify the treatment responders, and theoretically demonstrate the superiority of jointly learning over two-stage learning. Extensive experiments on semi-synthetic and real-world datasets show that our method better predicts treatment responders and adaptively trades off false-positives and false-negatives with varying weight coefficients.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709191"}, {"primary_key": "186563", "vector": [], "sparse_vector": [], "title": "ProST: Prompt Future Snapshot on Dynamic Graphs for Spatio-Temporal Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Spatio-temporal prediction focuses on jointly modeling spatial correlations and temporal evolution and has a wide range of applications. Due to the heterogeneity of spatio-temporal data, accurate prediction relies on effectively integrating topological structures and sequential patterns. Although recurrent graph learning methods excel at capturing dynamic graph patterns, explicitly inferring future snapshots from historical dynamic graphs remains a significant challenge. Recently, prompt-based graph learning has shown the potential to improve future snapshot inference by leveraging node or task-specific prompts. However, these methods fail to fully capture edge information resulting in incomplete and less accurate representations of future snapshot structures. To bridge this gap, we propose ProST, a framework that Prompts future snapshots on dynamic graphs for Spatio-Temporal prediction, which leverages dynamic graph pre-training to generate a premise graph containing historical graph information and then employs prompts on the premise graph to infer explicit future snapshots. Specifically, this framework comprises three steps: Firstly, dynamic graph pre-training is performed using multi-granularity evolution graph convolution to obtain the premise graph with both local and global features of dynamic graphs. Secondly, prompt subgraphs are used to prompt node pairs and edge features within the premise graph. The subgraph prompt aggregation mechanism propagates this information to generate future snapshots. Finally, we freeze the parameters of the pre-trained model and update the subgraph prompt parameters using meta-learning to adapt to downstream spatio-temporal prediction tasks. Extensive experiments on real-world datasets validate that ProST achieves state-of-the-art performance.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709273"}, {"primary_key": "186564", "vector": [], "sparse_vector": [], "title": "LDMapNet-U: An End-to-End System for City-Scale Lane-Level Map Updating.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chenting Gong", "<PERSON>", "Jizhou Huang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An up-to-date city-scale lane-level map is an indispensable infrastructure and a key enabling technology for ensuring the safety and user experience of autonomous driving systems.In industrial scenarios, reliance on manual annotation for map updates creates a critical bottleneck.Lane-level updates require precise change information and must ensure consistency with adjacent data while adhering to strict standards.Traditional methods utilize a three-stage approach-construction, change detection, and updating-which often necessitates manual verification due to accuracy limitations.This results in labor-intensive processes and hampers timely updates.To address these challenges, we propose LDMapNet-U, which implements a new end-to-end paradigm for city-scale lane-level map updating.By reconceptualizing the update task as an end-toend map generation process grounded in historical map data, we introduce a paradigm shift in map updating that simultaneously generates vectorized maps and change information.To achieve this, a Prior-Map Encoding (PME) module is introduced to effectively encode historical maps, serving as a critical reference for detecting changes.Additionally, we incorporate a novel Instance Change Prediction (ICP) module that learns to predict associations with historical maps.Consequently, LDMapNet-U simultaneously achieves vectorized map element generation and change detection.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709383"}, {"primary_key": "186565", "vector": [], "sparse_vector": [], "title": "Effective AOI-level Parcel Volume Prediction: When Lookahead Parcels Matter.", "authors": ["Yinfeng Xiang", "<PERSON><PERSON>", "<PERSON>", "Haitao Yuan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Last-mile Delivery Parcel Volume (LDPV) quantifies the number of parcels destined for a specific region, particularly a manually divided Area-Of-Interest (AOI). Accurate prediction of AOI-level LDPV is crucial for the efficient management of logistics resources. However, the straightforward adaptation of existing prediction models often falls short, primarily due to (I) a lack of consideration for the intuition behind AOI divisions, and (II) a reliance solely on fully observed historical data, which may not inform future trends. To overcome the above pitfalls, leveraging rich AOI data and advanced parcel travel time estimation services in JD Logistics, this paper introduces a novel framework called Dual-view Prediction Networks (DualPNs). It combines a Vector-Quantified AutoEncoder (VQ-AE) and a Template-Augmented Zero-Inflated Poisson (TA-ZIP), enabling both point and probabilistic distribution predictions of AOI-level LDPV. Specifically, VQ-AE utilizes a vector quantization technique to distill a large number of AOIs into representative templates, thereby addressing the first pitfall. Subsequently, TA-ZIP dynamically integrates fully observed and lookahead features, aligning them with template-specific decoders to parameterize the probabilistic distributions, thus resolving the second pitfall. We conduct extensive experiments in two cities, comprising over 47,000 and 126,000 AOIs respectively, to demonstrate the superiority of our DualPNs over other baselines. Moreover, a real-world case study highlights the effectiveness of DualPNs for enhancing downstream courier allocation by yielding an average improvement of 1.51% in the on-time delivery rate.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709441"}, {"primary_key": "186566", "vector": [], "sparse_vector": [], "title": "Scalable Area Difficulty Assessment with Knowledge-enhanced AI for Nationwide Logistics Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logistics services have become a core business in online-to-offline e-commerce like Amazon, Alibaba, and JD. In logistics services, a city is partitioned into distinct geographical areas, and each area is assigned a worker, responsible for all delivery tasks within it. Due to varying geographic conditions (e.g., high-rise buildings, buildings without elevators), the difficulty of completing tasks can differ significantly between areas, which results in unbalanced workloads and salaries for workers. The necessity for scalable data-driven methods to assess area difficulty in logistics is well-recognized. However, the significant expenses associated with ground truth data collection limit the capabilities of current machine learning methods. In this paper, we consider a frequently overlooked resource, i.e., the workers' firsthand knowledge of areas, to address this problem in a human-AI collaboration fashion. In particular, we design RAICA (Ranking-Aggregated Isotonic Calibration Assessment) framework, which includes two key modules: (i) a Judgment Rank Aggregation module, which aggregates individual workers' judgment rankings collected from surveys into an overall ranking to mitigate personal biases and inconsistency between different workers; (ii) an Isotonic Calibration module, which calibrates the assessment from existing machine learning models with the aggregated ranking through Isotonic regression to enhance the accuracy of area difficulty assessment with theoretical guarantees. Extensive evaluation based on real-world data including over 2 million orders collected from 97 areas during 6 months by one of the largest logistics companies in the world shows that RAICA outperforms existing methods, increasing F1 score by 0.25. More importantly, RAICA has been deployed by this logistics company, which significantly improved crowdsourcing couriers' salary fairness with a 0.2 decrease in the Gini coefficient across over 1,200 delivery stations nationwide and increased the on-time delivery rates for full-time couriers by 1.67%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709407"}, {"primary_key": "186567", "vector": [], "sparse_vector": [], "title": "Brain Effective Connectivity Estimation via Fourier Spatiotemporal Attention.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Junzhong Ji", "<PERSON><PERSON>"], "summary": "Estimating brain effective connectivity (EC) from functional magnetic resonance imaging (fMRI) data can aid in comprehending the neural mechanisms underlying human behavior and cognition, providing a foundation for disease diagnosis. However, current spatiotemporal attention modules handle temporal and spatial attention separately, extracting temporal and spatial features either sequentially or in parallel. These approach overlooks the inherent spatiotemporal correlations present in real world fMRI data. Additionally, the presence of noise in fMRI data further limits the performance of existing methods. In this paper, we propose a novel brain effective connectivity estimation method based on Fourier spatiotemporal attention (FSTA-EC), which combines Fourier attention and spatiotemporal attention to simultaneously capture inter-series (spatial) dynamics and intra-series (temporal) dependencies from high-noise fMRI data. Specifically, Fourier attention is designed to convert the high-noise fMRI data to frequency domain, and map the denoised fMRI data back to physical domain, and spatiotemporal attention is crafted to simultaneously learn spatiotemporal dynamics. Furthermore, through a series of proofs, we demonstrate that incorporating learnable filters into fast Fourier transform and inverse fast Fourier transform processes is mathematically equivalent to performing cyclic convolution. The experimental results on simulated and real-resting-state fMRI datasets demonstrate that the proposed method exhibits superior performance when compared to state-of-the-art methods. The code is available at https://github.com/XiongWenXww/FSTA.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709226"}, {"primary_key": "186568", "vector": [], "sparse_vector": [], "title": "Neural Network Pruning for Invariance Learning.", "authors": ["<PERSON>", "Yuanzhou Chen", "Yizhou Sun", "<PERSON>"], "summary": "Model scaling laws have driven the development of neural networks with more-and-more parameters. As a result, there is a growing need for neural network pruning to enable the efficient deployment during inference. We take a deeper look at neural network pruning from the lens of invariance preservation. We argue that successfully pruned neural networks should be invariant to transformations which do not alter the data's underlying semantics (ex. translations). To this goal, we first show existing post-pruning algorithms do not perserve desired invariances. We then propose a framework to discover novel architectures that do capture desired invariances from data via pruning. Specifically, we show contrastive learning with small initialization can effectively transfer invariance preservation encoded in the model weights to the pruning mask. Our approach consistently outperform traditional pruning algorithms on fully-connected, convolutional, and transformer networks across 3 vision, 40 tabular, and 1 natural language datasets.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709262"}, {"primary_key": "186569", "vector": [], "sparse_vector": [], "title": "Succinct Interaction-Aware Explanations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shapley values (Shap) are a popular approach to explaining decisions of black-box models by revealing the importance of individual features. Shap explanations are easy to interpret, but as they do not incorporate feature interactions, they are also incomplete and potentially misleading. Interaction-aware methods such as nShap report the additive importance of all subsets up to n features. These explanations are complete, but in practice excessively large and difficult to interpret. In this paper, we combine the best of both worlds. We partition the features into significantly interacting groups, and use these to compose a succinct, interpretable explanation. To determine which partitioning out of super-exponentially many explains a model best, we derive a criterion that weighs the complexity of an explanation against its representativeness for the model's behavior. To be able to find the best partitioning, we show how to prune sub-optimal solutions using a statistical test. This not only improves runtime but also helps to avoid explaining spurious interactions. Experiments show that iShap represents underlying modeling more accurately than <PERSON>ha<PERSON> and nShap, and a user study suggests that iShap is perceived as more interpretable and trustworthy.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709175"}, {"primary_key": "186570", "vector": [], "sparse_vector": [], "title": "Disclosing Actual Controller based on Equity Knowledge Graph Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Baokun Yi"], "summary": "Disclosing Actual Controllers (ACs) of a company has been the basis for financial risk governance. A shareholder in a winning stable coalition, where members make consistent decisions and win in votes, is considered an AC. However, existing methods fail to discover stable coalitions due to the ignorance of various relations other than the shareholding relation among shareholders, such as kinship, subsidiary and so on. Moreover, the above relations form a large-scale equity network, which brings challenges for efficiently identifying winning stable coalitions. We construct an Equity Knowledge Graph (EKG) to represent the semantic and structural information of the equity network. In this paper, we propose an AC disclosure method based on Equity Knowledge Graph Learning (EKGL). Specifically, to discover stable coalitions, EKGL designs a multi-relational aggregation module to aggregate the information of different relations horizontally. Based on the aggregated information, EKGL leverages a metapath-based aggregation module to encode the shareholding structure by capturing different shareholding paths on EKG vertically. To identify winning stable coalitions, we propose a control neural network to simulate the voting process of shareholders. Experiments and a case study on the EKG constructed from real datasets demonstrate that EKGL outperforms baselines by achieving 0.33 improvement in F1 score and reducing time cost.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709432"}, {"primary_key": "186571", "vector": [], "sparse_vector": [], "title": "Mutual Information-aware Knowledge Distillation for Short Video Recommendation.", "authors": ["<PERSON>", "Taoxing Pan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Short-video sharing platforms engaging billions of users have attracted intense interest recently. A key insight is that user feedback on these platforms is heavily influenced by preceding exposed videos in the same request, called context cumulative effects. For example, multiple repeated videos in a request often cause user fatigue and influence user feedback. However, related factors, such as the other exposed items in the same request, are available during model training but not accessible during online serving. Vanilla distillation methods mitigate the training-inference inconsistency, struggling to capture the dynamic dependence between context cumulative effects and user feedback. To address this problem, we propose the Mutual Information-aware Knowledge Distillation (MIKD) framework, which fuses such effects and user-item matching degrees by evaluating their impacts on user feedback based on mutual information estimation. Rigorous analysis and extensive experiments demonstrate that MIKD precisely extracts personal interests and consistently improves performance. We conduct online A/B testing on a leading short-video sharing mobile app, and the results demonstrate the effectiveness of the proposed method. MIKD has been successfully deployed online to serve the main traffic and optimize user experiences.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709403"}, {"primary_key": "186573", "vector": [], "sparse_vector": [], "title": "ScalaGBM: Memory Efficient GBDT Training for High-Dimensional Data on GPU.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gradient Boosted Decision Trees (GBDTs) are classical machine learning algorithms widely employed in recommendation systems, database queries, etc. Due to the extensive memory access involved in histogram-based GBDT training methods, high-bandwidth GPUs have been widely adopted to accelerate the training. However, when handling millions of feature data, it requires significant memory to store the training data and histograms, posing challenges for training on limited GPU memories. In this paper, we develop a GPU-based GBDT framework named ScalaGBM, aiming to accelerate high-dimensional data training with less memory usage. We first employ a CSR-like data format and CSR-based histogram construction to reduce the memory occupation of the training data. Then, we reorganize the training workflow with a double buffer structure to reduce the overall memory consumption for the histogram. Finally, we develop multi-dimensional parallel histogram construction and global optimal split point reduction to speed up the training process. Experimental results demonstrate that ScalaGBM handles real-world datasets with over 100 million instances of 50 million features with a single commercial GPU while existing GBDT frameworks all run into out-of-memory errors. Meanwhile, ScalaGBM achieves a maximum speedup of 39× over state-of-the-art GBDT counterparts without sacrificing the training quality. The code is available at https://github.com/Xtra-Computing/thundergbm.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709271"}, {"primary_key": "186575", "vector": [], "sparse_vector": [], "title": "CausalMob: Causal Human Mobility Prediction with LLMs-derived Human Intentions toward Public Events.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zipei Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large-scale human mobility exhibits spatial and temporal patterns that can assist policymakers in decision making. Although traditional prediction models attempt to capture these patterns, they are often affected by nonperiodic public events, such as disasters and occasional celebrations. Since regular human mobility patterns are affected by these events, estimating their causal effects is critical to accurate mobility predictions. News articles provide unique perspectives on these events, though processing them is a challenge. In this study, we propose a causality based prediction model, CausalMob, to analyze the causal effects of public events. We first utilize large language models (LLMs) to extract human intentions from news and transform them into features that act as causal treatments. Next, the model learns representations of spatio-temporal regional covariates from multiple data sources to serve as confounders for causal inference. Finally, we present a causal effect estimation framework to ensure that event features remain independent of confounders during prediction. Based on large-scale real-world data, the experimental results show that the proposed model excels in human mobility prediction, outperforming state-of-the-art models.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709231"}, {"primary_key": "186578", "vector": [], "sparse_vector": [], "title": "Causal Discovery from Shifted Multiple Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "A fundamental problem in many science domains is learning the causal structure of a system from observed data. The observed data canonically come from multiple environments (i.e. different times, locations, and measurements), and causal models may have unobserved shifts. Although the causal graphs can be identified by modeling the distribution changes among different environments, existing solutions can only learn causal structures when given environmental information. In contrast, we propose a causal discovery approach (CausalSME) which automatically identifies pseudo environments and unobserved distribution shifts. Specifically, CausalSME learns a causal model containing unobserved variables, which can correct the distribution shifts with mixed environments. The heart of CausalSME is a variational autoencoder that infers shifted causal effects of unobserved variables and guides the identification of environment information. It further divides the shifted samples by the identified environments to jointly learn an invariant causal model. We prove the structure identifiability of CausalSME with the causal additive model. In our extensive experiments we show that CausalSME achieves state-of-the-art performance.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709247"}, {"primary_key": "186581", "vector": [], "sparse_vector": [], "title": "Benchmarking and Defending against Indirect Prompt Injection Attacks on Large Language Models.", "authors": ["Jingwei Yi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guangzhong Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The integration of large language models (LLMs) with external content has enabled applications such as Microsoft Copilot but also introduced vulnerabilities to indirect prompt injection attacks. In these attacks, malicious instructions embedded within external content can manipulate LLM outputs, causing deviations from user expectations. To address this critical yet under-explored issue, we introduce the first benchmark for bindirect prompt injection attacks, named BIPIA, to assess the risk of such vulnerabilities. Using BIPIA, we evaluate existing LLMs and find them universally vulnerable. Our analysis identifies two key factors contributing to their success: LLMs' inability to distinguish between informational context and actionable instructions, and their lack of awareness in avoiding the execution of instructions within external content. Based on these findings, we propose two novel defense mechanisms -- boundary awareness and explicit reminder -- to address these vulnerabilities in both black-box and white-box settings. Extensive experiments demonstrate that our black-box defense provides substantial mitigation, while our white-box defense reduces the attack success rate to near-zero levels, all while preserving the output quality of LLMs. We hope this work inspires further research into securing LLM applications and fostering their safe and reliable use. Our code is available at https://github.com/microsoft/BIPIA.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709179"}, {"primary_key": "186582", "vector": [], "sparse_vector": [], "title": "Inductive Link Prediction on N-ary Relational Facts via Semantic Hypergraph Reasoning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "N-ary relational facts represent semantic correlations among more than two entities. While recent studies have developed link prediction (LP) methods to infer missing relations for knowledge graphs (KGs) containing n-ary relational facts, they are generally limited to transductive settings. Fully inductive settings, where predictions are made on previously unseen entities, remain a significant challenge. As existing methods are mainly entity embedding-based, they struggle to capture entity-independent logical rules. To fill in this gap, we propose an n-ary subgraph reasoning framework for fully inductive link prediction (ILP) on n-ary relational facts. This framework reasons over local subgraphs and has a strong inductive inference ability to capture n-ary patterns. Specifically, we introduce a novel graph structure, the n-ary semantic hypergraph, to facilitate subgraph extraction. Moreover, we develop a subgraph aggregating network, NS-HART, to effectively mine complex semantic correlations within subgraphs. Theoretically, we provide a thorough analysis from the score function optimization perspective to shed light on NS-HART's effectiveness for n-ary ILP tasks. Empirically, we conduct extensive experiments on a series of inductive benchmarks, including transfer reasoning (with and without entity features) and pairwise subgraph reasoning. The results highlight the superiority of the n-ary subgraph reasoning framework and the exceptional inductive ability of NS-HART. The source code of this paper has been made publicly available at https://github.com/yin-gz/Nary-Inductive-SubGraph.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709195"}, {"primary_key": "186583", "vector": [], "sparse_vector": [], "title": "SepsisCalc: Integrating Clinical Calculators into Early Sepsis Prediction via Dynamic Temporal Graph Construction.", "authors": ["<PERSON><PERSON> Yin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yao", "Thai-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sepsis is an organ dysfunction caused by a deregulated immune response to an infection. Early sepsis prediction and identification allow for timely intervention, leading to improved clinical outcomes. Clinical calculators (e.g., the six-organ dysfunction assessment of SOFA in Figure 1) play a vital role in sepsis identification within clinicians' workflow, providing evidence-based risk assessments essential for sepsis diagnosis. However, artificial intelligence (AI) sepsis prediction models typically generate a single sepsis risk score without incorporating clinical calculators for assessing organ dysfunctions, making the models less convincing and transparent to clinicians. To bridge the gap, we propose to mimic clinicians' workflow with a novel framework SepsisCalc to integrate clinical calculators into the predictive model, yielding a clinically transparent and precise model for utilization in clinical settings. Practically, clinical calculators usually combine information from multiple component variables in Electronic Health Records (EHR), and might not be applicable when the variables are (partially) missing. We mitigate this issue by representing EHRs as temporal graphs and integrating a learning module to dynamically add the accurately estimated calculator to the graphs. Experimental results on real-world datasets show that the proposed model outperforms state-of-the-art methods on sepsis prediction tasks. Moreover, we developed a system to identify organ dysfunctions and potential sepsis risks, providing a human-AI interaction tool for deployment, which can help clinicians understand the prediction outputs and prepare timely interventions for the corresponding dysfunctions, paving the way for actionable clinical decision-making support for early intervention.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709402"}, {"primary_key": "186584", "vector": [], "sparse_vector": [], "title": "Generalizable Recommender System During Temporal Popularity Distribution Shifts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hanghang Tong"], "summary": "Many modern recommender systems represent user and item attributes as embedding vectors, relying on them for accurate recommendations. However, entangled embeddings often capture not only intrinsic property factors (e.g., user interest in item property) but also popularity factors (e.g., user conformity to item popularity) indistinguishably. These embeddings, influenced by popularity distribution, may face challenges when the popularity distribution at test time differs from historical distribution. Existing remedies in the literature involve disentangled embedding learning, which aims to separately capture intrinsic and popularity factors, demonstrating plausible generalization during popularity distribution shifts. However, we highlight that these methods often overlook a crucial aspect of popularity shifts-their temporal nature-in both training and inference phases. To address this, we propose Temporal Popularity distribution shift generalizABle recommender system (TPAB), a novel disentanglement framework incorporating temporal popularity. TPAB introduce a new (1) temporal-aware embedding design for users and items. Within this design, (2) popularity coarsening and (3) popularity bootstrapping are proposed to enhance generalization further. We also provide theoretical analysis showing that the bootstrapping loss eliminates the effect of popularity on the learned model. During inference, we infer test-time popularity and corresponding embeddings, using them alongside property embeddings for prediction. Extensive experiments on real-world datasets validate TPAB, showcasing its outstanding generalization ability during temporal popularity distribution shifts.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709299"}, {"primary_key": "186585", "vector": [], "sparse_vector": [], "title": "Instruction Semantics Enhanced Dual-Flow Graph Model for GPU Error Resilience Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jingjing Gu", "<PERSON><PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As GPUs are widely deployed in High Performance Computing systems, it is critical to ensure that these systems can perform reliably. To improve system reliability, researchers estimate the error resilience of GPU programs by understanding resilience characteristics or modeling error propagation. However, features indicative of resilience rely on manual extraction from simulations of numerous faults, and error propagation analysis cannot target fine-grained bit-level faults. To address those problems, this paper introduces a novel paradigm, namely InstrDGM, for efficiently predicting GPU error resilience. Specifically, InstrDGM first fine-tunes a large language model using extensive sequences of GPU assembly instructions for extracting the semantic representation of instructions automatically. Meanwhile, we consider the propagation of bit-level faults during instruction execution and data transfer processes, and leverage graph neural networks to capture their distinct error propagation patterns. Then, the fault embeddings extracted from these error propagation patterns are integrated for error resilience prediction. Additionally, this paper releases a new dataset for GPU error resilience assessment, containing 1.2 million fault samples. Finally, extensive experiments show that InstrDGM significantly outperforms existing methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709424"}, {"primary_key": "186586", "vector": [], "sparse_vector": [], "title": "BackdoorMBTI: A Backdoor Learning Multimodal Benchmark Tool Kit for Backdoor Defense Evaluation.", "authors": ["Haiyang Yu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pengzhou Cheng", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over the past few years, the emergence of backdoor attacks has presented significant challenges to deep learning systems, allowing attackers to insert backdoors into neural networks. When data with a trigger is processed by a backdoor model, it can lead to mispredictions targeted by attackers, whereas normal data yields regular results. The scope of backdoor attacks is expanding beyond computer vision and encroaching into areas such as natural language processing and speech recognition. Nevertheless, existing backdoor defense methods are typically tailored to specific data modalities, restricting their application in multimodal contexts. While multimodal learning proves highly applicable in facial recognition, sentiment analysis, action recognition, visual question answering, the security of these models remains a crucial concern. Specifically, there are no existing backdoor benchmarks targeting multimodal applications or related tasks. In order to facilitate the research in multimodal backdoor, we introduce BackdoorMBTI, the first backdoor learning toolkit and benchmark designed for multimodal evaluation across three representative modalities from eleven commonly used datasets. BackdoorMBTI provides a systematic backdoor learning pipeline, encompassing data processing, data poisoning, backdoor training, and evaluation. The generated poison datasets and backdoor models enable detailed evaluation of backdoor defenses. Given the diversity of modalities, BackdoorMBTI facilitates systematic evaluation across different data types. Furthermore, BackdoorMBTI offers a standardized approach to handling practical factors in backdoor learning, such as issues related to data quality and erroneous labels. We anticipate that BackdoorMBTI will expedite future research in backdoor defense methods within a multimodal context. Code is available at https://github.com/SJTUHaiyangYu/BackdoorMBTI.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709385"}, {"primary_key": "186588", "vector": [], "sparse_vector": [], "title": "Boosting Explainability through Selective Rationalization in Pre-trained Language Models.", "authors": ["Libing Yuan", "Shuaibo Hu", "<PERSON><PERSON>", "<PERSON>"], "summary": "The widespread application of pre-trained language models (PLMs) in natural language processing (NLP) has led to increasing concerns about their explainability.Selective rationalization is a selfexplanatory framework that selects human-intelligible input subsets as rationales for predictions.Recent studies have shown that applying existing rationalization frameworks to PLMs will result in severe degeneration and failure problems, producing sub-optimal or meaningless rationales.Such failures severely damage trust in rationalization methods and constrain the application of rationalization techniques on PLMs.In this paper, we find that the homogeneity of tokens in the sentences produced by PLMs is the primary contributor to these problems.To address these challenges, we propose a method named Pre-trained Language Model's Rationalization (PLMR), which splits PLMs into a generator and a predictor to deal with NLP tasks while providing interpretable rationales.The generator in PLMR also alleviates homogeneity by pruning irrelevant tokens, while the predictor uses full-text information to standardize predictions.Experiments conducted on two widely used datasets across multiple PLMs demonstrate the effectiveness of the proposed method PLMR in addressing the challenge of applying selective rationalization to PLMs.Codes: https://github.com/ylb777/PLMR.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709212"}, {"primary_key": "186589", "vector": [], "sparse_vector": [], "title": "Annotation-guided Protein Design with Multi-Level Domain Alignment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>u Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ji<PERSON><PERSON> Yao", "<PERSON>"], "summary": "The core challenge of de novo protein design lies in creating proteins with specific functions or properties, guided by certain conditions. Current models explore to generate protein using structural and evolutionary guidance, which only provide indirect conditions concerning functions and properties. However, textual annotations of proteins, especially the annotations for protein domains, which directly describe the protein's high-level functionalities, properties, and their correlation with target amino acid sequences, remain unexplored in the context of protein design tasks. In this paper, we propose Protein-Annotation Alignment Generation PAAG, a multi-modality protein design framework that integrates the textual annotations extracted from protein database for controllable generation in sequence space. Specifically, within a multi-level alignment module, PAAG can explicitly generate proteins containing specific domains conditioned on the corresponding domain annotations, and can even design novel proteins with flexible combinations of different kinds of annotations. Our experimental results underscore the superiority of the aligned protein representations from PAAG over 7 prediction tasks. Furthermore, PAAG demonstrates a significant increase in generation success rate (24.7% vs 4.7% in zinc finger, and 54.3% vs 22.0% in the immunoglobulin domain) in comparison to the existing model. We anticipate that PAAG will broaden the horizons of protein design by leveraging the knowledge from between textual annotation and proteins.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709199"}, {"primary_key": "186590", "vector": [], "sparse_vector": [], "title": "A Structure-aware Invariant Learning Framework for Node-level Graph OOD Generalization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) have been proven effective in modeling graph data, mostly depending on the in-distribution assumption. While in the out-of-distribution (OOD) scenarios, especially for the more challenging node-level task, the feature and structure distribution shifts between training and test nodes lead to performance degradation. To improve node-level OOD generalization, typical approaches introduce graph augmentation to enrich the training environments and conduct invariant learning to learn stable representations across various augmented environments. However, their graph augmentations emphasize diversity but neglect the preservation of invariant patterns which are fundamental to invariant learning. Moreover, most of them simply conduct the classic invariant learning objective but lack the consideration of the graph-specific structure information. Therefore, to mitigate their weakness, we propose a Structure-aware Invariant learning framework for Node-level Graph OOD generalization (SING). Specifically, we develop the invariance constraint regularization terms during the optimization of augmentations. Additionally, we define the structure embedding to elucidate the structural property and design the structure embedding alignment loss to optimize the augmentations and the invariant representations. By introducing the structure information, we further integrate the unique structural property into invariant learning, thereby boosting the invariant message-passing GNNs. The extensive experiments on the transductive GOOD benchmark and the inductive datasets empirically validate our superior OOD generalization performance to baselines.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709227"}, {"primary_key": "186593", "vector": [], "sparse_vector": [], "title": "GLINT-RU: Gated Lightweight Intelligent Recurrent Units for Sequential Recommender Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Gao", "Xiangyu Zhao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Transformer-based models have gained significant traction in sequential recommender systems (SRSs) for their ability to capture user-item interactions effectively. However, these models often suffer from high computational costs and slow inference. Meanwhile, existing efficient SRS approaches struggle to embed high-quality semantic and positional information into latent representations. To tackle these challenges, this paper introduces GLINT-RU, a lightweight and efficient SRS leveraging a single-layer dense selective Gated Recurrent Units (GRU) module to accelerate inference. By incorporating a dense selective gate, GLINT-RU adaptively captures temporal dependencies and fine-grained positional information, generating high-quality latent representations. Additionally, a parallel mixing block infuses fine-grained positional features into user-item interactions, enhancing both recommendation quality and efficiency. Extensive experiments on three datasets demonstrate that GLINT-RU achieves superior prediction accuracy and inference speed, outperforming baselines based on RNNs, Transformers, MLPs, and SSMs. These results establish GLINT-RU as a powerful and efficient solution for SRSs. The implementation code is publicly available for reproducibility. https://github.com/szhang-cityu/GLINT-RU.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709304"}, {"primary_key": "186594", "vector": [], "sparse_vector": [], "title": "Can Large Language Models Improve the Adversarial Robustness of Graph Neural Networks?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph neural networks (GNNs) are vulnerable to adversarial attacks, especially for topology perturbations, and many methods that improve the robustness of GNNs have received considerable attention.Recently, we have witnessed the significant success of large language models (LLMs), leading many to explore the great potential of LLMs on GNNs.However, they mainly focus on improving the performance of GNNs by utilizing LLMs to enhance the node features.Therefore, we ask: Will the robustness of GNNs also be enhanced with the powerful understanding and inference capabilities of LLMs?By presenting the empirical results, we find that despite that LLMs can improve the robustness of GNNs, there is still an average decrease of 23.1% in accuracy, implying that the GNNs remain extremely vulnerable against topology attacks.Therefore, another question is how to extend the capabilities of LLMs on graph adversarial robustness.In this paper, we propose an LLM-based robust graph structure inference framework, LLM4RGNN, which distills the inference capabilities of GPT-4 into a local LLM for identifying malicious edges and an LM-based edge predictor for finding missing important edges, so as to recover a robust graph structure.Extensive experiments demonstrate that LLM4RGNN consistently improves the robustness across various GNNs.Even in some cases where the perturbation ratio increases to 40%, the accuracy of GNNs is still better than that on the clean graph.The source code can be found in https://github.com/zhongji<PERSON>-zhang/LLM4RGNN.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709256"}, {"primary_key": "186595", "vector": [], "sparse_vector": [], "title": "LLM-Eraser: Optimizing Large Language Model Unlearning through Selective Pruning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We focus on unlearning unwanted knowledge in autoregressive large language models (LLMs) through pruning. Our goal is to selectively remove undesirable information (e.g., harmful responses, privacy-sensitive data) while ensuring the preservation of desirable knowledge (e.g., positive responses and objective facts). Previous approaches use gradient ascent (GA) over undesired knowledge to inversely optimize LLMs, which compromises the model's performance on desired knowledge. To address this limitation, we introduce a novel two-stage approach, named LLM-Eraser, for selectively identifying and editing parameters specifically associated with undesirable knowledge. LLM-Eraser operates in two stages: localization and unlearning. During the localization stage, we utilize neuron scores and trainable soft masks to identify parameters crucial to the undesired knowledge. In the unlearning stage, we prune these identified parameters and apply a selective post-training process to enhance the model's selectiveness. Our experiments, conducted across five task datasets, demonstrate that LLM-Eraser effectively unlearns undesirable knowledge-evidenced by the model's near-random performance on multiple-choice questions related to the erased knowledge-while maintaining high proficiency in desirable knowledge, with an average performance deficit of only 2.5%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709312"}, {"primary_key": "186596", "vector": [], "sparse_vector": [], "title": "Way to Specialist: Closing Loop Between Specialized LLM and Evolving Domain Knowledge Graph.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large language models (LLMs) have demonstrated exceptional performance across a wide variety of domains. Nonetheless, generalist LLMs continue to fall short in reasoning tasks necessitating specialized knowledge, e.g., emotional sociology and medicine. Prior investigations into specialized LLMs focused on domain-specific training, which entails substantial efforts in domain data acquisition and model parameter fine-tuning. To address these challenges, this paper proposes the Way-to-Specialist (WTS) framework, which synergizes retrieval-augmented generation with knowledge graphs (KGs) to enhance the specialized capability of LLMs in the absence of specialized training. In distinction to existing paradigms that merely utilize external knowledge from general KGs or static domain KGs to prompt LLM for enhanced domain-specific reasoning, WTS proposes an innovative ''LLM↻KG'' paradigm, which achieves bidirectional enhancement between specialized LLM and domain knowledge graph (DKG). The proposed paradigm encompasses two closely coupled components: the DKG-Augmented LLM and the LLM-Assisted DKG Evolution. The former retrieves question-relevant domain knowledge from DKG and uses it to prompt LLM to enhance the reasoning capability for domain-specific tasks; the latter leverages LLM to generate new domain knowledge from processed tasks and use it to evolve DKG. WTS closes the loop between DKG-Augmented LLM and LLM-Assisted DKG Evolution, enabling continuous improvement in the domain specialization as it progressively answers and learns from domain-specific questions. We validate the performance of WTS on 7 datasets (e.g., TweetQA, ChatDoctor5k) spanning 6 domains, e.g., emotional sociology, medical, ect. The experimental results show that <PERSON><PERSON> surpasses the previous SOTA in 5 specialized domains, and achieves a maximum performance improvement of 11.3%.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709187"}, {"primary_key": "186599", "vector": [], "sparse_vector": [], "title": "Multi-period Learning for Financial Time Series Forecasting.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yun<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Time series forecasting is important in finance domain. Financial time series (TS) patterns are influenced by both short-term public opinions and medium-/long-term policy and market trends. Hence, processing multi-period inputs becomes crucial for accurate financial time series forecasting (TSF). However, current TSF models either use only single-period input, or lack customized designs for addressing multi-period characteristics. In this paper, we propose a Multi-period Learning Framework (MLF) to enhance financial TSF performance. MLF considers both TSF's accuracy and efficiency requirements. Specifically, we design three new modules to better integrate the multi-period inputs for improving accuracy: (i) Inter-period Redundancy Filtering (IRF), that removes the information redundancy between periods for accurate self-attention modeling, (ii) Learnable Weighted-average Integration (LWI), that effectively integrates multi-period forecasts, (iii) Multi-period self-Adaptive Patching (MAP), that mitigates the bias towards certain periods by setting the same number of patches across all periods. Furthermore, we propose a Patch Squeeze module to reduce the number of patches in self-attention modeling for maximized efficiency. MLF incorporates multiple inputs with varying lengths (periods) to achieve better accuracy and reduces the costs of selecting input lengths during training. The codes and datasets are available at https://github.com/Meteor-Stars/MLF.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709422"}, {"primary_key": "186600", "vector": [], "sparse_vector": [], "title": "Large-scale Human Mobility Data Regeneration for Open Urban Research.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Liangz<PERSON> Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Weifeng Lv"], "summary": "Large-scale human mobility data contains rich spatial and temporal information for urban sensing, crowd flow modeling, and urban planning. However, it is usually difficult to access wide-coverage, long-term, and consistent-time human mobility data. Most of the publicly available datasets are actually only records of discontinuous trajectories of a very small portion of urban citizens in asynchronous time due to the limited usage of apps for location data collection or the limited number of volunteers. To address this problem and empower open urban research, this paper constructs a high-quality human mobility dataset by generating large-scale citizen trajectories based on massive cellular signaling data. Particularly, we first propose a heatmap diffusion module to generate a probability heatmap that produces plausible trajectories at both the individual and city scales. Then, we propose a masked trajectory AutoEncoder, which can generate individual trajectory embeddings from partially given or empty trajectories. Third, a flexible framework is provided to incorporate the heatmap diffusion module with the masked trajectory embeddings, demonstrating significant flexibility in handling both fully masked trajectories for city-wide analysis and partially masked trajectories for specific locations. We have conducted extensive experiments to validate the utility of the regenerated trajectories at both individual and region levels for various applications. Numerous case studies further illustrate that our model learns not only the distribution of the trajectories but also the semantics of different urban areas. In summary, this paper provides a Heatmap Diffusion framework based on a Masked Trajectory AutoEncoder to regenerate flexible trajectories for open urban research. Correspondingly, we will try to open a large-scale human mobility data service for open urban research. Further information can be found at https://github.com/Rising0321/FinalOpenUR.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709380"}, {"primary_key": "186601", "vector": [], "sparse_vector": [], "title": "Generalizing Personalized Federated Graph Augmentation via Min-max Adversarial Learning.", "authors": ["<PERSON>", "<PERSON> Long", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingjiang Shi"], "summary": "Federated learning (FL) enables the training of a global machine learning model among multiple local clients in a collaborative fashion without directly sharing the details of their data. Due to this advantage, it has been utilized in a wide range of applications where privacy is a critical concern and has attracted great attention for graph representation learning (GRL). Despite the offered advances, there still exist two major challenges in the FL for GRL across distributed graph data, including heterogeneity and complementarity. In order to tackle these challenges, a novel personalized federated graph augmentation (PFGA) framework is proposed in this work. Unlike existing techniques, it utilizes generative models as bridges to enable information sharing among clients, thereby facilitating the collaborative training of GRL models. Instead of directly using the generative model trained on each client individually, we aggregate them into the globally generative model to gain a global view of the entire graph, which effectively alleviates the heterogeneity and complementarity issues simultaneously. We formulate the training of the generative and GRL models as a min-max adversarial learning problem and theoretically prove the convergence. Furthermore, the effectiveness of the method is demonstrated using experimental results on six real-world datasets.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709311"}, {"primary_key": "186602", "vector": [], "sparse_vector": [], "title": "MentorPDM: Learning Data-Driven Curriculum for Multi-Modal Predictive Maintenance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>yyagura", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Predictive Maintenance (PDM) systems are essential for preemptive monitoring of sensor signals to detect potential machine component failures in industrial assets such as bearings in rotating machinery. Existing PDM systems face two primary challenges: 1) Irregular Signal Acquisition, where data collection from the sensors is intermittent, and 2) Signal Heterogeneity, where the full spectrum of sensor modalities is not effectively integrated. To address these challenges, we propose a Curriculum Learning Framework for Multi-Modal Predictive Maintenance - MentorPDM. MentorPDM consists of 1) a graph-augmented pretraining module that captures intrinsic and structured temporal correlations across time segments via a temporal contrastive learning objective and 2) a bi-level curriculum learning module that captures task complexities for weighing the importance of signal modalities and samples via modality and sample curricula. Empirical results from MentorPDM show promising performance with better generalizability in PDM tasks compared to existing benchmarks. The efficacy of the MentorPDM model will be further demonstrated in real industry testbeds and platforms.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709388"}, {"primary_key": "186604", "vector": [], "sparse_vector": [], "title": "Semi-supervised Multi-view Clustering with Active Constraints.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-view clustering has attracted increasing attention in recent years. However, most existing multi-view clustering approaches are performed in a purely unsupervised manner, while ignoring the valuable weak supervision information that can be obtained (e.g., active query) in many real applications. This paper considers the weak pairwise constraints among samples to enhance the clustering performance, and proposes a Semi-supervised Multi-view Clustering method with Active Constraints, SMCAC for short. SMCAC consists of two stages, clustering (C-stage) and active query (A-stage). In the C-stage, we design a tensor based multi-view graph learning model equipped with sample pairwise constraints regularization to facilitate the discriminative graph learning and fusion. An effective optimization algorithm based on alternating direction minimization is devised to solve the clustering model. In the A-stage, the most uncertain or difficult sample pairs are actively selected to query the constraints, based on the divergence of multi-view similarities learned in the C-stage. The two processes alternate iteratively until the maximum number of queries is reached. Extensive experiments on several popular datasets well validate the effectiveness of the proposed method.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709204"}, {"primary_key": "186605", "vector": [], "sparse_vector": [], "title": "Understanding and Mitigating Hyperbolic Dimensional Collapse in Graph Contrastive Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning generalizable self-supervised graph representations for downstream tasks is challenging. To this end, Contrastive Learning (CL) has emerged as a leading approach. The embeddings of CL are arranged on a hypersphere where similarity is measured by the cosine distance. However, many real-world graphs, especially of hierarchical nature, cannot be embedded well in the Euclidean space. Although the hyperbolic embedding is suitable for hierarchical representation learning, naively applying CL to the hyperbolic space may result in the so-called dimension collapse, i.e., features will concentrate mostly within few density regions, leading to poor utilization of the whole feature space. Thus, we propose a novel contrastive learning framework to learn high-quality graph embeddings in hyperbolic space. Specifically, we design the alignment metric that effectively captures the hierarchical data-invariant information, as well as we propose a substitute of the uniformity metric to prevent the so-called dimensional collapse. We show that in the hyperbolic space one has to address the leaf- and height-level uniformity related to properties of trees. In the ambient space of the hyperbolic manifold these notions translate into imposing an isotropic ring density towards boundaries of Poincar<PERSON> ball. Our experiments support the efficacy of our method.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709249"}, {"primary_key": "186608", "vector": [], "sparse_vector": [], "title": "Variational Graph Autoencoder for Heterogeneous Information Networks with Missing and Inaccurate Attributes.", "authors": ["<PERSON><PERSON>", "Jianxiang Yu", "<PERSON>", "Chengcheng Yu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Heterogeneous Information Networks (HINs), which consist of various types of nodes and edges, have recently witnessed excellent performance in graph mining. However, most existing heterogeneous graph neural networks (HGNNs) fail to simultaneously handle the problems of missing attributes, inaccurate attributes and scarce node labels, which limits their expressiveness. In this paper, we propose a generative self-supervised model GraMI to address these issues simultaneously. Specifically, GraMI first initializes all the nodes in the graph with a low-dimensional representation matrix. After that, based on the variational graph autoencoder framework, GraMI learns both node-level and attribute-level embeddings in the encoder, which can provide fine-grained semantic information to construct node attributes. In the decoder, GraMI reconstructs both links and attributes. Instead of directly reconstructing raw features for attributed nodes, GraMI generates the initial low-dimensional representation matrix for all the nodes, based on which raw features of attributed nodes are further reconstructed. In this way, GraMI can not only complete informative features for non-attributed nodes, but rectify inaccurate ones for attributed nodes. Finally, we conduct extensive experiments to show the superiority of GraMI in tackling HINs with missing and inaccurate attributes. Our code and data can be found here: https://github.com/See-r/GraMI.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709251"}, {"primary_key": "186609", "vector": [], "sparse_vector": [], "title": "Proactive Model Adaptation Against Concept Drift for Online Time Series Forecasting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Time series forecasting always faces the challenge of concept drift, where data distributions evolve over time, leading to a decline in forecast model performance. Existing solutions are based on online learning, which continually organize recent time series observations as new training samples and update model parameters according to the forecasting feedback on recent data. However, they overlook a critical issue: obtaining ground-truth future values of each sample should be delayed until after the forecast horizon. This delay creates a temporal gap between the training samples and the test sample. Our empirical analysis reveals that the gap can introduce concept drift, causing forecast models to adapt to outdated concepts. In this paper, we present Proceed, a novel proactive model adaptation framework for online time series forecasting. Proceed first estimates the concept drift between the recently used training samples and the current test sample. It then employs an adaptation generator to efficiently translate the estimated drift into parameter adjustments, proactively adapting the model to the test sample. To enhance the generalization capability of the framework, Proceed is trained on synthetic diverse concept drifts. Extensive experiments on five real-world datasets across various forecast models demonstrate that <PERSON><PERSON><PERSON> brings more performance improvements than the state-of-the-art online learning methods, significantly facilitating forecast models' resilience against concept drifts. Code is available at https://github.com/SJTU-DMTai/OnlineTSF.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709210"}, {"primary_key": "186610", "vector": [], "sparse_vector": [], "title": "Graph Contrastive Learning with Progressive Augmentations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To be still yet still moving. - <PERSON> Choe Graph contrastive learning (GCL) has recently gained prominence in unsupervised graph representation learning. Traditional GCL approaches generally focus on creating a single contrastive view alongside the main graph view, targeting invariant representation learning in a static framework. Our study introduces a novel manner: despite using static graphs, we aim to learn invariant representations by generating a series of evolving contrastive views with temporal coherence and multi-viewpoint insights at various granularities. In this context, we propose the Progressive Augmentation framework for Graph Contrastive Learning (PaGCL). This framework advances beyond traditional methods by producing a sequence of augmented views, each evolving from the previous one, and assigning timestamps based on piecewise smoothness. This approach enables our model to more effectively extract invariant features from these dynamic views, capturing multi-grained structural and temporal information. Our experiments on diverse benchmark datasets demonstrate that PaGCL significantly outperforms current state-of-the-art methods.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709307"}, {"primary_key": "186611", "vector": [], "sparse_vector": [], "title": "Stable Representation Learning on Graphs from Multiple Environments with Structure Distribution Shift.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, Graph Neural Networks (GNNs) become very effective methods to utilize graphs and have been applied to many real-world applications, including recommendation, advertisement, and financial fraud detection. In fact, GNNs are mostly trained and test in the environments with the same distribution. However, in the real cases, selection bias are inevitably existed in both the node features and the graph structures, which will lead to serious impact on the GNN performance. Several works of literature have investigated the out-of-distribution (OOD) problem on the feature distribution, but little research specifically studies the effect caused by the bias of graph structure. However, graph structure is very fundamental for GNNs since it greatly affects the message propagation mechanism. In order to solve the above problem, we propose an unsupervised Stable Graph Representation learning (SGR) framework to obtain stable graphs from multiple environments with graph structure bias, and to improve the stability ability of GNN model across environments. Comprehensive experiments have been carried out on 4 public benchmark dataset and a real-world financial dataset. The experimental results show that the proposed stable learning method significantly improves the stability of GNN model in varying test environments.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709269"}, {"primary_key": "186612", "vector": [], "sparse_vector": [], "title": "Graph Learning with Distributional Edge Layouts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) learn from graph-structured data by passing messages between neighboring nodes along edges on certain topological layouts. While layouts can be essential to GNNs' performance, extant methods generally consider obtaining layouts from limited perspectives. In this paper, we introduce Distributional Edge Layouts (DELs), a first-of-its-kind method to sample a collection of topological layouts from a Boltzmann distribution under physical energies. By integrating DELs into GNNs, a wide landscape of feasible graph layouts can be captured from a holistic perspective, overcoming the intrinsic drawbacks in existing GNN designs.In practice, DELs can complement various GNN architectures with high versatility. Our theoretical analysis proves that GNNs equipped with DELs maintain at least the same expressive as their original counterparts, with empirical potential offering extra expressivity. Extensive experiments demonstrate that DELs consistently and substantially improve the performance of a wide range of GNN baselines across multiple datasets, achieving state-of-the-art results. This improvement suggests that DELs capture important distributional information previously overlooked by traditional GNN approaches. DEL is open-sourced at https://github.com/LOGO-CUHKSZ/DEL.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709206"}, {"primary_key": "186614", "vector": [], "sparse_vector": [], "title": "BTFL: A Bayesian-based Test-Time Generalization Method for Internal and External Data Distributions in Federated learning.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Federated Learning (FL) enables multiple clients to collaboratively develop a global model while maintaining data privacy. However, online FL deployment faces challenges due to distribution shifts and evolving test samples. Personalized Federated Learning (PFL) tailors the global model to individual client distributions, but struggles with Out-Of-Distribution (OOD) samples during testing, leading to performance degradation. In real-world scenarios, balancing personalization and generalization during online testing is crucial and existing methods primarily focus on training-phase generalization. To address the test-time trade-off, we introduce a new scenario: Test-time Generalization for Internal and External Distributions in Federated Learning (TGFL), which evaluates adaptability under Internal Distribution (IND) and External Distribution (EXD). We propose BTFL, a Bayesian-based test-time generalization method for TGFL, which balances generalization and personalization at the sample level during testing. BTFL employs a two-head architecture to store local and global knowledge, interpolating predictions via a dual-Bayesian framework that considers both historical test data and current sample characteristics with theoretical guarantee and faster speed. Our experiments demonstrate that BTFL achieves improved performance across various datasets and models with less time cost. The source codes are made publicly available at https://github.com/ZhouYuCS/BTFL.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709309"}, {"primary_key": "186615", "vector": [], "sparse_vector": [], "title": "A Two-Stage Pretraining-Finetuning Framework for Treatment Effect Estimation with Unmeasured Confounding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Mingming Gong"], "summary": "Estimating the conditional average treatment effect (CATE) from observational data plays a crucial role in areas such as e-commerce, healthcare, and economics. Existing studies mainly rely on the strong ignorability assumption that there are no unmeasured confounders, whose presence cannot be tested from observational data and can invalidate any causal conclusion. In contrast, data collected from randomized controlled trials (RCT) do not suffer from confounding, but are usually limited by a small sample size. In this paper, we propose a two-stage pretraining-finetuning (TSPF) framework using both large-scale observational data and small-scale RCT data to estimate the CATE in the presence of unmeasured confounding. In the first stage, a foundational representation of covariates is trained to estimate counterfactual outcomes through large-scale observational data. In the second stage, we propose to train an augmented representation of the covariates, which is concatenated to the foundational representation obtained in the first stage to adjust for the unmeasured confounding. To avoid overfitting caused by the small-scale RCT data in the second stage, we further propose a partial parameter initialization approach, rather than training a separate network. The superiority of our approach is validated on two public datasets with extensive experiments. The code is available at https://github.com/zhouchuanCN/KDD25-TSPF.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709161"}, {"primary_key": "186617", "vector": [], "sparse_vector": [], "title": "HRSTORY: Historical News Review Based Online Story Discovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Story discovery on news streams can help people quickly find story from vast amounts of news, improving the efficiency of information acquisition. Recent online story discovery methods encode text topics and then cluster articles into stories based on similarity. However, the results obtained by these methods are one-time, and clustered news cannot adaptively update in a continuous news stream. Additionally, the inadequate quality of article encoding and the presence of noise data deteriorate the performance of story discovery. To this end, we propose HRSTORY for online story discovery on news streams, which employs a historical news review method to enable news to continuously adapt to the latest environment in the stream data and make corrections and updates. Furthermore, HRSTORY captures better article embeddings through modeling multi-layer relational dependencies within the text. By using sentence-level noise masking, HRSTORY improves the relevance of news article representation to core topics and reduces the interference of noise data. Experiments on real news datasets show that HRSTORY outperforms the state-of-the-art algorithms in unsupervised online story discovery performance.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709198"}, {"primary_key": "186619", "vector": [], "sparse_vector": [], "title": "Prices Do Matter: Modeling Price Competitiveness for Online Hotel Industry.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Broad adoption of Online Travel Platforms (OTPs) has led to increasing interest in accurately predicting users' hotel purchase behavior, with price being a key influencer in user decision-making and receiving significant focus. In examining the hotel purchasing process, we identify a pervasive trend that users make extensive price comparisons before making decisions. Existing research primarily focuses on a hotel's own price, neglecting the complex dynamics of market-driven price competition. In this paper, we propose the concept of Marketplace-oriented Hotel Price Competitiveness (MHPC) to model a hotel's pricing competitiveness within the marketplace. Being independent of specific user preferences, MHPC can be applied to and improve various downstream operations in the online hotel industry, such as hotel ranking and pricing, ultimately benefiting hoteliers, users, and OTPs. Furthermore, a novel Hotel Price Competitiveness-aware Purchase Prediction Model (HP3M) is constructed by incorporating MHPC and demand dynamics into a multi-task learning framework, featuring three distinct submodules to encompass the tri-dimensional facets of MHPC. Extensive offline and online experiments demonstrate HP3M's effectiveness in predicting hotel purchase probability and enhancing the performance of hotel ranking and pricing compared to the state-of-the-art methods. HP3M has been fully deployed on Fliggy, a leading OTP in China, serving thousands of hoteliers and tens of millions of users.", "published": "2025-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3690624.3709420"}]