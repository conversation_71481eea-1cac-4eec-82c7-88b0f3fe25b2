# ArXiv Insight

<p align="center">
  <strong>基于AI的学术论文智能检索与综述平台</strong><br>
  集成语义搜索、智能对话、论文管理的现代化学术研究工具
</p>

<p align="center">
  <a href="#核心功能"><strong>核心功能</strong></a> ·
  <a href="#检索原理"><strong>检索原理</strong></a> ·
  <a href="#快速部署"><strong>快速部署</strong></a> ·
  <a href="#使用指南"><strong>使用指南</strong></a>
</p>

---

## 核心功能

**🔍 智能检索**：中英文自然语言查询 + 多LLM协同评分 + 多源数据支持（ArXiv、ACL等）+ 多维度筛选

**💬 AI对话**：基于收藏论文的智能问答 + PDF自动解析 + 多模型支持 + 流式响应

**📚 知识管理**：一键收藏 + 智能分类 + 批量导出 + 本地存储

**🎨 现代界面**：响应式设计 + 暗色主题 + 直观交互 + 实时反馈

## 技术架构

**前端**：Next.js 15 + React 18 + TypeScript + Tailwind CSS + shadcn/ui

**后端**：FastAPI + Zilliz(Milvus) + BGE-M3嵌入 + 多LLM模型(GLM-4/Qwen2.5) + SQLite

**数据源**：ArXiv + ACL Anthology + 可扩展学术数据库

## 检索原理

### 核心流程
```
用户查询 → 中英双语增强 → BGE-M3向量化 → Zilliz检索 → 多LLM协同评分 → 结果优化
```

### 关键技术

**1. 语义检索**
- BGE-M3生成1024维向量，基于HNSW算法高效相似度搜索
- 多字段匹配（标题/摘要/子摘要），支持多维度筛选

**2. 深度重排序**
- 多模型协同：GLM-4-9B + Qwen2.5-14B等并行评分(1-10分)
- 动态阈值：根据结果质量自适应调整筛选标准(5.1-6分)
- 评分维度：主题相关性 + 方法相似性 + 应用场景 + 创新程度

**3. 数据处理**
- 自动采集：ArXiv API定期获取 + PDF解析 + 内容清洗
- 智能压缩：LLMLingua技术压缩摘要
- 增量更新：定时任务保持数据实时性

## 快速部署

### 环境要求
Node.js >= 18.0.0 + Python >= 3.8 + pnpm

### 一键启动
```bash
# 1. 克隆并安装前端
git clone <repository-url> && cd arxiv-insight
pnpm install && cp .env.example .env.local
# 配置 NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# 2. 安装并启动后端
cd backend && python -m venv venv && source venv/bin/activate
pip install -r requirements.txt && cp config.example.py config.py
# 配置API密钥和数据库连接
python controller.py &

# 3. 启动前端
cd .. && pnpm dev
```

### 数据初始化
```bash
cd backend
python -c "from data.paper_deal import deal_arxiv_paper; deal_arxiv_paper(year=2024)"
```

访问 [http://localhost:3000](http://localhost:3000) 开始使用

## 使用指南

**搜索**：输入研究主题 → 选择筛选条件 → 开启深度搜索获得精准结果

**管理**：收藏论文 → 创建主题文件夹 → 批量导出

**对话**：@引用收藏论文 → AI自动解析PDF内容 → 深度学术问答

## 配置说明

### 核心配置 (`backend/config.py`)
```python
# LLM API
API_KEY_SC = "your_siliconflow_key"
API_KEY_GLM = "your_zhipu_key"

# 向量数据库
zilliz_uri_gpt = "your_zilliz_endpoint"
zilliz_token = "your_zilliz_token"

# 翻译API
YOUDAO_APP_KEY = "your_youdao_key"
YOUDAO_APP_SECRET = "your_youdao_secret"
```

### 项目结构
```
arxiv-insight/
├── app/                    # Next.js应用(api/survey/chat)
├── components/             # React组件(ui/survey/collect/minichat)
├── backend/                # FastAPI后端
│   ├── controller.py       # 主控制器
│   ├── service/           # 业务服务(search/translate/pdf2md)
│   ├── data/              # 数据处理(paper_get/paper_deal)
│   └── utils/             # 工具模块(llm/config)
└── lib/hooks/public/       # 前端工具库
```
