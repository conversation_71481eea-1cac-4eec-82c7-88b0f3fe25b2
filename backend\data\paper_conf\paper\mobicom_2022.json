[{"primary_key": "1738973", "vector": [], "sparse_vector": [], "title": "Non-cooperative wi-fi localization &amp; its privacy implications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present Wi-Peep - a new location-revealing privacy attack on non-cooperative Wi-Fi devices. Wi-Peep exploits loopholes in the 802.11 protocol to elicit responses from Wi-Fi devices on a network that we do not have access to. It then uses a novel time-of-flight measurement scheme to locate these devices. Wi-Peep works without any hardware or software modifications on target devices and without requiring access to the physical space that they are deployed in. Therefore, a pedestrian or a drone that carries a Wi-Peep device can estimate the location of every Wi-Fi device in a building. Our Wi-Peep design costs $20 and weighs less than 10 g. We deploy it on a lightweight drone and show that a drone flying over a house can estimate the location of Wi-Fi devices across multiple floors to meter-level accuracy. Finally, we investigate different mitigation techniques to secure future Wi-Fi devices against such attacks.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560530"}, {"primary_key": "1738974", "vector": [], "sparse_vector": [], "title": "Audio-domain position-independent backdoor attack via unnoticeable triggers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning models have become key enablers of voice user interfaces. With the growing trend of adopting outsourced training of these models, backdoor attacks, stealthy yet effective training-phase attacks, have gained increasing attention. They inject hidden trigger patterns through training set poisoning and overwrite the model's predictions in the inference phase. Research in backdoor attacks has been focusing on image classification tasks, while there have been few studies in the audio domain. In this work, we explore the severity of audio-domain backdoor attacks and demonstrate their feasibility under practical scenarios of voice user interfaces, where an adversary injects (plays) an unnoticeable audio trigger into live speech to launch the attack. To realize such attacks, we consider jointly optimizing the audio trigger and the target model in the training phase, deriving a position-independent, unnoticeable, and robust audio trigger. We design new data poisoning techniques and penalty-based algorithms that inject the trigger into randomly generated temporal positions in the audio input during training, rendering the trigger resilient to any temporal position variations. We further design an environmental sound mimicking technique to make the trigger resemble unnoticeable situational sounds and simulate played over-the-air distortions to improve the trigger's robustness during the joint optimization process. Extensive experiments on two important applications (i.e., speech command recognition and speaker recognition) demonstrate that our attack can achieve an average success rate of over 99% under both digital and physical attack settings.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560531"}, {"primary_key": "1738975", "vector": [], "sparse_vector": [], "title": "Experience: practical problems for acoustic sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic sensing shows great potential to transform billions of consumer-grade electronic devices that people interact with on a daily basis into ubiquitous sensing platforms. In this paper, we share our experience and findings during the process of developing and deploying acoustic sensing systems for real-world usage. We identify multiple practical problems that were not paid attention to in the research community, and propose the corresponding solutions. The challenges include: (i) there exists annoying audible sound leakage caused by acoustic sensing; (ii) acoustic sensing actually affects music play and voice call; (iii) acoustic sensing consumes a significant amount of power, degrading the battery life; (iv) real-world device mobility can fail acoustic sensing. We hope the shared experience can benefit not only the future development of sensing algorithms but also the hardware design, pushing acoustic sensing one step further towards real-life adoption.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560527"}, {"primary_key": "1738977", "vector": [], "sparse_vector": [], "title": "Deep reinforcement learning-based control framework for radio access networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Network performance optimization represents one of the major challenges for mobile network operators, especially with the increasingly use cases that have diverse performance expectations. In this work we propose a novel control framework that maximizes radio resources utilization and minimizes performance degradation in the most challenging part of cellular architecture that is the radio access network (RAN). Based on deep reinforcement learning, we devise two control schemes: centralized and distributed, respectively. Using extensive discrete event simulations, we confirm that our proposed control framework succeeds in optimizing radio resources utilization while minimizing service level agreement (SLA) violations in a multi-slice RAN.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558276"}, {"primary_key": "1738983", "vector": [], "sparse_vector": [], "title": "A non-intrusive and adaptive speaker de-identification scheme using adversarial examples.", "authors": ["<PERSON><PERSON>", "Li Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhongjie Ba", "<PERSON>", "<PERSON><PERSON>"], "summary": "Faced with the threat of identity leakage during voice data publishing, users are engaged in a privacy-utility dilemma while enjoying convenient voice services. Existing studies employ direct modification or text-based re-synthesis to de-identify users' voices, but resulting in inconsistent audibility for human participants and not adaptive to informed attacks. In this poster, we propose a non-intrusive and adaptive speaker de-identification scheme to balance the privacy and utility of voice services. We generate adversarial examples to conceal user identity from exposure by Automatic Speaker Identification (ASI). By learning a compact distribution with a conditional variational auto-encoder, our system enables on-demand target sampling and diverse identity transformation. We also introduce the acoustic masking effect to construct inaudible perturbations, thus preserving the speech content and perceptual quality. Experiments on 50 speakers show our system could achieve 98.2% successful de-identification on 4 mainstream ASIs with an objective perceptual quality of 4.38 and a subjective mean opinion score of 4.56.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558260"}, {"primary_key": "1738985", "vector": [], "sparse_vector": [], "title": "Anchor-few: an adaptive precise indoor positioning system for low anchor densities based on IoT localization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper designs and implements an adaptive precise indoor positioning system, called Anchor-Few, for low anchor densities through Internet of Things (IoT) localization. <PERSON><PERSON> <PERSON> exploits the IoT localization device, iBeacon, to provide accurate indoor positioning using only one or two anchors as three anchors are unavailable for triangulation. To the best of our knowledge, Anchor-Few is the first system that can provide the following features. First, the localization environments can be automatically classified based on the number of different iBeacon signals received by mobile devices, current moving direction, and historical location information. Second, it can use the distances from iBeacon nodes and previous user locations to detect whether the user is moving or not and determine the current moving direction. Third, in the environment covered by only one iBeacon node, a more accurate position than that using existing methods can be derived through the iBeacon position, distance from the iBeacon, and current moving direction. Finally, for environments covered by two iBeacon nodes, multiple iBeacon information and the user's historical locations can be cooperatively used for accurate position estimation. An Android-based prototype with deployed iBeacon nodes is implemented to verify the feasibility and correctness of our Anchor-Few system, which can achieve high localization accuracy while keeping anchor densities low.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558254"}, {"primary_key": "1738989", "vector": [], "sparse_vector": [], "title": "MMCamera: an imaging modality for future RF-based physiological sensing.", "authors": ["<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Sun", "<PERSON>"], "summary": "By leveraging the mechanical motions on the body surface conducted by physiological activities, many works have achieved radio-frequency(RF)-based physiological sensing. However, previous works generally simplify the model on both the mechanism of physiological motion and the signal propagation around the human body, which leads to the loss of valuable information. In this paper, we introduce the concept of micro-motion(MM) camera to provide a more cognitive imaging modality to observe torso surface motion comprehensively so as to dynamically image the motions of the breath and cardiac activities. We develop a sub-6G MMCamera prototype system. The camera functionality is implemented to prove the concept novelty from the view of respiratory-cardiovascular system monitoring. Our result shows that the proposed system could provide fine-grid torso surface motion imaging with breath and cardiac activities distributed over the entire thorax and abdomen.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558275"}, {"primary_key": "1738990", "vector": [], "sparse_vector": [], "title": "FLEW: fully emulated wifi.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "WiFi is the de facto standard for providing wireless access to the Internet using the 2.4GHz ISM band. Tens of billions of WiFi devices were shipped worldwide and WiFi Access Points (APs) are ubiquitous in public, enterprise and personal environments. We have also witnessed the fast growth of IoT (Internet of Things) devices. With more stringent board-space and power requirements, many IoT devices use more power-efficient, lower-cost and smaller wireless chips, such as Bluetooth or proprietary wireless chips. Due to the mismatch of different wireless technologies, these devices access the Internet indirectly via far less ubiquitous IoT gateways. Bluetooth and most proprietary wireless chips are based on FSK (Frequency-Shift Keying) modulation since FSK can be implemented with extremely simple and low-power FM (Frequency Modulation) circuits.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517030"}, {"primary_key": "1738992", "vector": [], "sparse_vector": [], "title": "Introspecting network behavior with mixed reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The operation of wireless devices is often affected by their physical locations and spatial relationships to other objects and phenomena in the environment. As devices become increasing small and mobile, these relationships can change rapidly. We need tools that can provide an at-a-glance operating picture of each moment and highlight spatial patterns. Such insights are poorly supported by existing tools that abstract away the physical reality of the underlying network. Fortunately, recent advances in localization technologies and mixed reality platforms provide an opportunity for a new approach: mixed reality network introspection. Mixed reality allows users to see network traffic situated directly in the real world, taking advantage of human spatial intuition and visual processing to quickly identify behavior of interest. We propose an architecture for mixed reality network introspection, and implement a prototype called XRShark, a network visualizer with augmented reality and virtual reality modes. Initial evaluations of XRShark show promise in enabling users to identify behaviors of local networks that would be difficult to detect using traditional tools.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558251"}, {"primary_key": "1738993", "vector": [], "sparse_vector": [], "title": "Inducing wireless chargers to voice out.", "authors": ["<PERSON><PERSON> Dai", "Z<PERSON><PERSON> An", "<PERSON><PERSON>"], "summary": "Recent advances have demonstrated that voice assistants or speech recognition systems can be manipulated by malicious and inaudible voice commands. However, the previously proposed attacks require an acoustical generator (e.g., a speaker or a capacitor) to trigger mechanical vibrations at a microphone diaphragm. In this work, we investigate a new type of inaudible command attack using wireless chargers. Specifically, the magnetic interference generated by a wireless charger can induce an inaudible sound at a nearby microphone, without triggering any mechanical vibrations, even if the microphone is equipped with a Faraday cage and an internal electromagnetic interference filter already. By taking advantage of this new insight, we will present a novel inaudible command attack demo that can inject inaudible voice commands into smart devices that are being charged or near to a charger. We conduct extensive experiments with 17 victim devices (iPhone, Huawei, Samsung, etc.) and six types of voice assistants (Siri, Google STT, Bixby, etc.). Evaluation results demonstrate the feasibility of the proposed attack with commercial charging settings.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558763"}, {"primary_key": "1738994", "vector": [], "sparse_vector": [], "title": "ioTree: a battery-free wearable system with biocompatible sensors for continuous tree health monitoring.", "authors": ["<PERSON><PERSON>", "Trung Tran", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a low-maintenance, wind-powered, battery-free, biocompatible, tree wearable, and intelligent sensing system, namely IoTree, to monitor water and nutrient levels inside a living tree. IoTree system includes tiny-size, biocompatible, and implantable sensors that continuously measure the impedance variations inside the living tree's xylem, where water and nutrients are transported from the root to the upper parts. The collected data are then compressed and transmitted to a base station located at up to 1.8 kilometers (approximately 1.1 miles) away. The entire IoTree system is powered by wind energy and controlled by an adaptive computing technique called block-based intermittent computing, ensuring the forward progress and data consistency under intermittent power and allowing the firmware to execute with the most optimal memory and energy usage. We prototype IoTree that opportunistically performs sensing, data compression, and long-range communication tasks without batteries. During in-lab experiments, IoTree also obtains the accuracy of 91.08% and 90.51% in measuring 10 levels of nutrients, NH3 and K2O, respectively. While tested with Burkwood Viburnum and White Bird trees in the indoor environment, IoTree data strongly correlated with multiple watering and fertilizing events. We also deployed IoTree on a grapevine farm for 30 days, and the system is able to provide sufficient measurements every day.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558749"}, {"primary_key": "1738995", "vector": [], "sparse_vector": [], "title": "IoTree: a battery-free wearable system with biocompatible sensors for continuous tree health monitoring.", "authors": ["<PERSON><PERSON>", "Trung Tran", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a low-maintenance, wind-powered, battery-free, biocompatible, tree wearable, and intelligent sensing system, namely IoTree, to monitor water and nutrient levels inside a living tree. IoTree system includes tiny-size, biocompatible, and implantable sensors that continuously measure the impedance variations inside the living tree's xylem, where water and nutrients are transported from the root to the upper parts. The collected data are then compressed and transmitted to a base station located at up to 1.8 kilometers (approximately 1.1 miles) away. The entire IoTree system is powered by wind energy and controlled by an adaptive computing technique called block-based intermittent computing, ensuring the forward progress and data consistency under intermittent power and allowing the firmware to execute with the most optimal memory and energy usage. We prototype IoTree that opportunistically performs sensing, data compression, and long-range communication tasks without batteries. During in-lab experiments, IoTree also obtains the accuracy of 91.08% and 90.51% in measuring 10 levels of nutrients, NH3 and K2O, respectively. While tested with Burkwood Viburnum and White Bird trees in the indoor environment, IoTree data strongly correlated with multiple watering and fertilizing events. We also deployed IoTree on a grapevine farm for 30 days, and the system is able to provide sufficient measurements every day.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3567652"}, {"primary_key": "1738996", "vector": [], "sparse_vector": [], "title": "Federated learning-based air quality prediction for smart cities using BGRU model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, Internet of Things (IoT) has become very popular due to its applications in various fields such as industry, commerce, and education. Cities become smart cities by utilizing lots of applications and services of IoT. However, these intelligent applications and services significantly threaten the environment regarding air pollution. Therefore, high accuracy in air pollution monitoring and future air quality predictions have become our primary concern to save human beings from health issues coming from air pollution. In general, deep learning (DL) and federated learning (FL) techniques are suitable for solving various forecasting problems and dealing with the high volatile air components in heterogeneous big data scenarios. This ambiance of DL and FL motivates us to exploit the DL-based Bidirectional Gated Recurrent Unit (BGRU) method for future air quality prediction using big data and federated learning (FL) to train our model in a distributed, decentral, and secure ways. This paper proposes a novel distributed and decentralized FL-based BGRU model to accurately predict air quality using the smart city's big data. The effectiveness of the FL-based BGRU Model is estimated with other machine learning (ML) models by using various evaluation metrics.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558267"}, {"primary_key": "1738999", "vector": [], "sparse_vector": [], "title": "AdaptOver: adaptive overshadowing attacks in cellular networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In cellular networks, attacks on the communication link between a mobile device and the core network significantly impact privacy and availability. Up until now, fake base stations have been required to execute such attacks. Since they require a continuously high output power to attract victims, they are limited in range and can be easily detected both by operators and dedicated apps on users' smartphones.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560525"}, {"primary_key": "1739000", "vector": [], "sparse_vector": [], "title": "BatchSketch: a &quot;network-server&quot; aligned solution for efficient mobile edge network sketching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Heavy flow identification is essential for discovering potential adversarial activities in mobile edge networks. However, state-of-the-art falls short in accuracy with approximation algorithms and unbounded memory usages with precise measurement. To this end, we introduce BatchSketch, a \"network - server\" aligned solution to achieve both high accuracy and low memory usage. The intelligence behind is that BatchSketch first conducts coarse-grained filtering from the switch with bounded memory and computation resources, and it then sends the filtered flows to the RDMA-link attached server with plenty of memory for accurate measurement. Our primary experimental results indicate that the filter on the switch can filter 99% of non-heavy flows, remarkably reducing the memory usage for the measurement.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558246"}, {"primary_key": "1739001", "vector": [], "sparse_vector": [], "title": "MUFFLE: prototype of light-weight haptic augmented pressure interface for on-fly neurorehabilitation.", "authors": ["<PERSON><PERSON>", "Hongyi Ren", "<PERSON><PERSON><PERSON>", "Zhigeng Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this demonstration, we suggest lightweight haptic communications from the context of remote neurorehabilitation. In particular, we collect tactile data from pressure sensors attached to the human hand and design a classifier to determine the objects that an individual holds or grasps. Finally, we have implemented the proposed system on Raspberry Pi and demonstrated a personalized classification while rendering the haptic feedback in a virtual perception.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558755"}, {"primary_key": "1739003", "vector": [], "sparse_vector": [], "title": "Mobile IoT-RoadBot: an AI-powered mobile IoT solution for real-time roadside asset management.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Timely detection of roadside assets that require maintenance is essential for improving citizen satisfaction. Currently, the process of identifying such maintenance issues is typically performed manually, which is time consuming, expensive, and slow to respond. In this paper, we present Mobile IoT-RoadBot, a mobile 5G-based Internet of Things (IoT) solution, powered by Artificial Intelligence (AI) techniques to enable opportunistic real-time identification and detection of maintenance issues with roadside assets. The Mobile IoT-RoadBot solution has been deployed on 11 bin service (waste collection) trucks in the western suburbs of Melbourne, Australia, performing real-time assessments of road-side assets as they service areas within the local government. We present the architecture of Mobile IoT-RoadBot and demonstrate its capability via an online 'points of maintenance' (PoMs) map.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558271"}, {"primary_key": "1739004", "vector": [], "sparse_vector": [], "title": "A GPU-enabled mobile telemedicine training system for graphic rendering.", "authors": ["<PERSON><PERSON>eng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Aiming for overcoming the constraints in graphic rendering of mobile devices used in telemedicine training for 3D images, this paper presents a GPU-enabled mobile telemedicine training system for graphic rendering. Two improvements are made to seamlessly display interactive 3D images of human organs, bones and blood vessels with realtime rendering. Firstly, instead of multiple stages of instruction translation between OpenGL ES and OpenGL, a bespoke GPU driver is added in Virtual Android to invoke GPU resources directly. Secondly, a Video Process Unit (VPU) is added to the hardware layer replacing CPU to code rendered results in H.264 format, reducing CPU load significantly. Test results suggest that the system can deliver consistent performance even in mobile devices of weak capability and a single server can support up to 24 concurrent virtual Android operating systems, each of which connects to 5 clients. The framework proposed by this paper is not only suitable for telemedicine training, but also for other application areas such as Virtual Reality and Augmented Reality in mobile environment.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558269"}, {"primary_key": "1739005", "vector": [], "sparse_vector": [], "title": "Leveraging public buses to relay UAVs for on-demand applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unmanned Aerial Vehicles (UAVs) are widely employed in smart city. However, the limited battery capacity is one of the UAV's most critical obstacles to monitoring mission. Recently, leveraging public buses to relay UAVs has been shown to be a promising solution to this critical issue. Existing works on this solution focused on pre-determined scenarios, namely the mission time and location of the UAVs are determined in advance. While in on-demand mission such as emergency response, mission time and location of the UAV is on-demand and stochastic. How the UAV riding on a bus perform in such stochastic missions remains open. In this paper, driven by the bus mobility data, a sampling-based algorithm is designed to navigate UAVs to land on buses in on-demand applications. A simple greedy algorithm is proposed to determine the appropriate buses to relay UAVs, so that the scheduling performance of the UAVs is optimized. Comprehensive evaluation using a large-scale bus trajectory data is conducted.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558279"}, {"primary_key": "1739006", "vector": [], "sparse_vector": [], "title": "Edge-assisted deep video denoising and super-resolution for real-time surveillance at night.", "authors": ["Liming Ge", "<PERSON>", "Dong <PERSON>", "<PERSON>"], "summary": "Video surveillance cameras have been extensively deployed over the last few years. In case of incidents such as natural disaster rescue, it provides vital guidance in real-time. However, due to the limited camera hardware and network bandwidth, noise are observed especially at night and the resolution is low. To tackle these two issues, we design and implement EADV, an Edge-Assisted Deep Video denoising and super-resolution system for real-time surveillance at night. We demonstrate the video quality enhancement using a camera, a displayer, and an edge server. The low-quality video captured by the camera is enhanced by the server and shown on the displayer. The enhanced real-time video is smooth and the performance uplift is observable.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558754"}, {"primary_key": "1739007", "vector": [], "sparse_vector": [], "title": "NextG-UP: a longitudinal and cross-sectional study of uplink performance of 5G networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "5G networks are being deployed rapidly across the world and have opened door to many uplink-oriented, bandwidth-intensive applications such as Augmented Reality and Connected Autonomous Vehicles. However, the roll-out is still in the early phase and the nature of deployment also varies across different geographic regions of the world. In this demo, we present NextG-UP, an Android-based tool designed to help understand the performance and evolution of 5G networks around the world. The crowd-sourcing mobile app collects various cellular network metrics and runs a short uplink throughput/latency test.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558746"}, {"primary_key": "1739009", "vector": [], "sparse_vector": [], "title": "Constructing smart buildings with in-concrete backscatter networks.", "authors": ["<PERSON>", "Z<PERSON><PERSON> An", "Jingyu Tong", "<PERSON><PERSON> Dai", "<PERSON><PERSON>"], "summary": "Given the increasing number of building collapse tragedies nowadays (e.g., Florida condo collapse), people gradually recognize that long-term and persistent structural health monitoring (SHM) becomes indispensable for civilian buildings. However, current SHM techniques suffer from high cost and deployment difficulty caused by the wired connection. In this work, we collaborate with experts from civil engineering to create a type of promising self-sensing concrete by introducing a novel functional filler, called EcoCapsule-a battery-free and miniature piezoelectric backscatter node. We overcome the fundamental challenges in in-concrete energy harvesting and wireless communication to achieve SHM via EcoCapsules. We prototype EcoCapsules and mix them with other raw materials (such as cement, sand, water, etc) to cast the self-sensing concrete, into which EcoCapsules are implanted permanently. We tested EcoCapsules regarding real-world buildings comprehensively.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558756"}, {"primary_key": "1739012", "vector": [], "sparse_vector": [], "title": "RF-transformer: a unified backscatter radio hardware abstraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents RF-Transformer, a unified backscatter radio hardware abstraction that allows a low-power IoT device to directly communicate with heterogeneous wireless receivers at the minimum power consumption. Unlike existing backscatter systems that are tailored to a specific wireless communication protocol, RF-Transformer provides a programmable interface to the micro-controller, allowing IoT devices to synthesize different types of protocol-compliant backscatter signals sharing radically different PHY-layer designs. To show the efficacy of our design, we implement a PCB prototype of RF-Transformer on 2.4 GHz ISM band and showcase its capability on generating standard ZigBee, Bluetooth, LoRa, and Wi-Fi 802.11b/g/n/ac packets. Our extensive field studies show that RF-Transformer achieves 23.8 Mbps, 247.1 Kbps, 986.5 Kbps, and 27.3 Kbps throughput when generating standard Wi-Fi, ZigBee, Bluetooth, and LoRa signals while consuming 7.6--74.2X less power than their active counterparts. Our ASIC simulation based on the 65-nm CMOS process shows that the power gain of RF-Transformer can further grow to 92--678X. We further integrate RF-Transformer with pressure sensors and present a case study on detecting foot traffic density in hallways. Our 7-day case studies demonstrate RF-Transformer can reliably transmit sensor data to a commodity gateway by synthesizing LoRa packets on top of Wi-Fi signals. Our experimental results also verify the compatibility of RF-Transformer with commodity receivers. Code and hardware schematics can be found at: https://github.com/LeFsCC/RF-Transformer.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560549"}, {"primary_key": "1739014", "vector": [], "sparse_vector": [], "title": "NeuLens: spatial-based dynamic acceleration of convolutional neural networks on edge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tao Han"], "summary": "Convolutional neural networks (CNNs) play an important role in today's mobile and edge computing systems for vision-based tasks like object classification and detection. However, state-of-the-art methods on CNN acceleration are trapped in either limited practical latency speed-up on general computing platforms or latency speed-up with severe accuracy loss. In this paper, we propose a spatial-based dynamic CNN acceleration framework, NeuLens, for mobile and edge platforms. Specially, we design a novel dynamic inference mechanism, assemble region-aware convolution (ARAC) supernet, that peels off redundant operations inside CNN models as many as possible based on spatial redundancy and channel slicing. In ARAC supernet, the CNN inference flow is split into multiple independent micro-flows, and the computational cost of each can be autonomously adjusted based on its tiled-input content and application requirements. These micro-flows can be loaded into hardware like GPUs as single models. Consequently, its operation reduction can be well translated into latency speed-up and is compatible with hardware-level accelerations. Moreover, the inference accuracy can be well preserved by identifying critical regions on images and processing them in the original resolution with large micro-flow. Based on our evaluation, NeuLens outperforms baseline methods by up to 58% latency reduction with the same accuracy and by up to 67.9% accuracy improvement under the same latency/memory constraints.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560528"}, {"primary_key": "1739015", "vector": [], "sparse_vector": [], "title": "Experience: practical indoor localization for malls.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We report our experiences of developing, deploying, and evaluating MLoc, a smartphone-based indoor localization system for malls. MLoc uses Bluetooth Low Energy RSSI and geomagnetic field strength as fingerprints. We develop efficient approaches for large-scale, outsourced training data collection. We also design robust online algorithms for localizing and tracking users' positions in complex malls. Since 2018, MLoc has been deployed in 7 cities in China, and used by more than 1 million customers. We conduct extensive evaluations at 35 malls in 7 cities, covering 152K m2 mall areas with a total walking distance of 215 km (1,100 km training data). MLoc yields a median location tracking error of 2.4m. We further characterize the behaviors of MLoc's customers (472K users visiting 12 malls), and demonstrate that MLoc is a promising marketing platform through a promotion event. The e-coupons delivered through MLoc yield an overall conversion rate of 22%. To facilitate future research on mobile sensing and indoor localization, we have released a large dataset (43 GB at the time when this paper was published) that contains IMU, BLE, GMF readings, and the localization ground truth collected by trained testers from 37 shopping malls.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517021"}, {"primary_key": "1739016", "vector": [], "sparse_vector": [], "title": "Uncovering insecure designs of cellular emergency services (911).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cellular networks that offer ubiquitous connectivity have been the major medium for delivering emergency services. In the U.S., mobile users can dial an emergency call with 911 for emergency uses in cellular networks, and the call can be forwarded to public safety answer points (PSAPs), which deal with emergency service requests. According to regulatory authority requirements for the cellular emergency services, anonymous user equipment (UE), which does not have a SIM (Subscriber Identity Module) card or a valid mobile subscription, is allowed to access them. Such support of emergency services for anonymous UEs requires different operations from conventional cellular services, and can therefore increase the attack surface of the cellular infrastructure. In this work, we are thus motivated to study the insecurity of the cellular emergency services and then discover four security vulnerabilities from them. Threateningly, they can be exploited to launch not only free data service attacks against cellular carriers, but also data DoS/overcharge and denial of cellular emergency service (DoCES) attacks against mobile users. All vulnerabilities and attacks have been validated experimentally as practical security issues in the networks of three major U.S. carriers. We finally propose and prototype standard-compliant remedies to mitigate the vulnerabilities.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560534"}, {"primary_key": "1739017", "vector": [], "sparse_vector": [], "title": "Passive light spectral indoor localization.", "authors": ["Ji<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel Visible Light Positioning (VLP) method, called Iris, that uses light spectral information (LSI) to localize humans completely passively in the sense that it neither requires the user to carry any device, nor does it require any modifications to existing lighting infrastructure. <PERSON> localizes a user based on the interference they produce on the LSI recorded at an array of spectral sensors embedded in the environment. We design a deep neural network that can effectively learn location fingerprints directly from the sensor LSI data and predict locations accurately under varying lighting conditions. We prototype Iris using a commercial-off-the-shelf light spectral sensor, AS7265x, which can measure light intensity over 18 different wavelength channels. We benchmark Iris against the state-of-the-art passive VLPs that rely on conventional photo-sensors capable of measuring only a single light intensity value aggregated over the entire visible spectrum. Our evaluations over two typical indoor environments, a 25 m2 one-bedroom apartment and a 13m × 8m office space, demonstrate that Iris can significantly reduce both the localization errors and the number of required sensors, while increasing robustness against changes in environmental lighting.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558253"}, {"primary_key": "1739018", "vector": [], "sparse_vector": [], "title": "Real-time neural network inference on extremely weak devices: agile offloading with explainable AI.", "authors": ["<PERSON>", "<PERSON>"], "summary": "With the wide adoption of AI applications, there is a pressing need of enabling real-time neural network (NN) inference on small embedded devices, but deploying NNs and achieving high performance of NN inference on these small devices is challenging due to their extremely weak capabilities. Although NN partitioning and offloading can contribute to such deployment, they are incapable of minimizing the local costs at embedded devices. Instead, we suggest to address this challenge via agile NN offloading, which migrates the required computations in NN offloading from online inference to offline learning. In this paper, we present AgileNN, a new NN offloading technique that achieves real-time NN inference on weak embedded devices by leveraging eXplainable AI techniques, so as to explicitly enforce feature sparsity during the training phase and minimize the online computation and communication costs. Experiment results show that AgileNN's inference latency is >6X lower than the existing schemes, ensuring that sensory data on embedded devices can be timely consumed. It also reduces the local device's resource consumption by >8X, without impairing the inference accuracy.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560551"}, {"primary_key": "1739019", "vector": [], "sparse_vector": [], "title": "Sifter: protecting security-critical kernel modules in Android through attack surface reduction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Linux kernel is an important part of the Trusted Computing Base (TCB) of a mobile device using the Android OS, making it attractive to attackers. While all vulnerabilities in the kernel are important, those that are directly reachable by untrusted programs pose a grave threat. This paper introduces Sifter, a solution for protecting security-critical kernel modules, i.e., those modules that are directly exposed to untrusted programs. <PERSON><PERSON>'s key approach is the use of fine-grained, highly-selective filters to reduce the attack surface of these kernel modules and make their vulnerabilities unreachable for untrusted programs. The key observation in <PERSON><PERSON> is that there are rich patterns in how legitimate programs issue syscalls to these kernel modules; thus, one can generate filters that only allow such syscall patterns, and as a result mitigate vulnerabilities (including zero-day ones) that could only be exploited by the use of unorthodox syscall patterns.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560548"}, {"primary_key": "1739020", "vector": [], "sparse_vector": [], "title": "Location-aware IT system security using IoT in multizone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In general, GPS receivers cannot be used for indoor settings and multizone infrastructure needs to be configured to create secure zones. Since a user may have different working roles in different zones, therefore, we are proposing here a solution for fixed IT infrastructure using IoT devices, sensors, and IoT cloud services to create a technology-agnostic secure zone in home/business/industry where sensing of natural parameters like location, temperature, pressure, light, images, sound, etc. are used to create a unique hash of a zone. AWS IoT greengrass core device coordinates with all IoT devices/sensors and creates the hash of a zone. MAC address is hashed using zone hash and a unique token for the PC is generated. All PC tokens are mapped with zone ID into the IT system deployed in the cloud and AWS IoT greengrass core device deployed in home/business/industry. This token and biometric a user needs to share during login to decide allowed roles for a user in that zone.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558258"}, {"primary_key": "1739025", "vector": [], "sparse_vector": [], "title": "StreamingTag: a scalable piracy tracking solution for mobile streaming services.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Qi<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Streaming services have billions of mobile subscribers, yet video piracy has cost service providers billions. Digital Rights Management (DRM), however, is still far from satisfactory. Unlike DRM, which attempts to prohibit the creation of pirated copies, fingerprinting may be used to track out the source of piracy. Nevertheless, the idea of piracy tracing is not widely used at the moment, since existing fingerprinting-based streaming systems fail to serve numerous users. In this paper, we present the design and evaluation of StreamingTag, a scalable piracy tracing system for mobile streaming services. StreamingTag adopts a segment-level fingerprint embedding scheme to remove the need of re-embedding the fingerprint into the video for each new viewer. The key innovations of StreamingTag include a scalable and CDN-friendly delivery framework, a polarized and randomized SVD watermarking scheme suitable for short segments, and a collusion-resistant fingerprinting scheme optimized for large-scale streaming services. Experiment results show the good QoS of StreamingTag in terms of preparation latency, bandwidth consumption, and video fidelity. Compared with existing SVD watermarking schemes, the proposed watermarking scheme improves the watermark extraction accuracy by 2.25x at most and 1.5x on average. Compared with existing collusion-resistant fingerprinting schemes, the proposed scheme catches more colluders and improves the recall rate by 26%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560521"}, {"primary_key": "1739027", "vector": [], "sparse_vector": [], "title": "Estimating soil moisture using RF signals.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose CoMEt, a radio frequency based approach that measures soil moisture at multiple depths underneath the ground surface without installing any objects in the soil and without making any contact with the ground surface. The main insight behind CoMEt is that the phase of an RF signal depends on its wavelength in the medium through which it is propagating, which in turn depends on the amount of soil moisture. To measure soil moisture, CoMEt leverages the phase changes across successive antennas in a receive antenna array along with the time of flight of the received signal to jointly estimate the depth of each layer of soil and the wavelength of the signal in each layer. It then uses these estimates to obtain the amount of moisture in each soil layer. We have implemented CoMEt using a software defined radio and a Raspberry Pi to measure soil moisture in real-time. We have extensively evaluated CoMEt in both indoor and outdoor environments. Our results show that CoMEt estimated soil moisture for up to three layers of soil with a median error of just 1.1%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517025"}, {"primary_key": "1739028", "vector": [], "sparse_vector": [], "title": "A wearable ultrasonic bladder monitoring device.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Progress on the development of a wearable ultrasonic bladder monitoring device is reported. The device is intended to help in the diagnosis of urinary incontinence of both young and elderly patients. We present all the components of the device - the textile band, electronics, as well as the early results of reconstruction algorithms.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558272"}, {"primary_key": "1739029", "vector": [], "sparse_vector": [], "title": "Fall detection based on interpretation of important features with wrist-wearable sensors.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartwatches are attracting great industrial attention, and the demand for daily monitoring is increasing. Thus, a method for high fall detection with the smallest data is required to minimize the load on telecommunication. It is difficult to run machine learning on a smartwatch for fall detection, and the data must be transmitted to a mobile phone. Therefore, we propose a method for detecting a fall with few features by detecting important features in the inertial signal acquired from the wristband sensor. We used the FallAllD dataset for evaluation and detected 60 descriptive statistical features from each of the three-axis acceleration and angular velocity. Additionally, important features were detected through SHapley additive exPlanations. The sampling frequency of the inertial signal obtained the highest accuracy of 94% at 120 Hz, with at least 14 important features.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558250"}, {"primary_key": "1739030", "vector": [], "sparse_vector": [], "title": "uGPS: design and field-tested seamless GNSS infrastructure in metro city.", "authors": ["<PERSON><PERSON><PERSON>", "Junghun Park", "Seonghoon Park", "Ji<PERSON>on Ryoo"], "summary": "This paper presents a uGPS (Underground GPS), which is the first SDR (Software Defined Radio)-based GNSS service to commodity GNSS receivers, including the latest iPhone, Android, and Car navigation. The uGPS is a system consisting of SDRs for GNSS signal generation, a GNSS D.O. for nano-second level timing synchronization's economic selection, fiber optic, and leaky feeder for a minimal environmental effect in tunnel environments. The proposed uGPS provides consistent 8 GPS satellites and 4 GLONASS satellites signal through a 1.5-kilometer-long tunnel. The uGPS equipped tunnel is highly compatible with commodity GNSS/GPS receivers. Therefore, it does not require any APP or special hardware. Our benchmark tested package was on top of the FPGA, in which modifications and upgrades were demonstrated via evaluation on three popular GNSS/GPS devices including Android phones, iPhones, and Car navigation systems. Further extensive evaluations demonstrated that the uGPS consistently achieved average location accuracy of 10 meters in 30~70km/h speed driving tests (without a map-matching algorithm), and a seamless handover between live GNSS and the uGPS system.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560520"}, {"primary_key": "1739031", "vector": [], "sparse_vector": [], "title": "Warm-started quantum sphere decoding via reverse annealing for massive IoT connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the continuous growth of the Internet of Things (IoT), the trend of increasing numbers of IoT devices will continue. To increase the network's capability to support a large number of active devices accessing a network concurrently, this work presents IoT-ResQ, a warm-started quantum annealing-based multi-device detector via quantum reverse annealing (RA). Unlike in typical quantum forward annealing (FA) protocol, IoT-ResQ's RA starts its search operation on a controllable candidate classical state, instead of a quantum superposition, and thus allows refined local quantum search around the initial state. This procedure can provide an opportunity of utilizing both conventional classical- and quantum-based detectors together in a hybrid synergy, to boost quantum optimization performance, mitigating the effect of quantum decoherence and noise on quantum hardware. In our evaluation, IoT-ResQ achieves nearly two to three orders of magnitude better BER and over 2X packet success rate with packet size of 32-byte compared to other quantum and conventional detectors at SNR 9 dB to support 48 active IoT devices with QPSK modulation (implying 48,000 deployed devices with 0.1% wake-up radio rate at a time), requiring ≈ 140 μs pure compute time for detection.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560516"}, {"primary_key": "1739032", "vector": [], "sparse_vector": [], "title": "The use of heterogeneous deep neural network system in radio tomography to detect people indoors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensor networks, made so that objects and people can be found without devices, are an important part of our high-tech world. This poster aims to show how heterogeneous convolutional neural networks can be used to improve a radio tomographic imaging system that can find people indoors precisely. In addition to original algorithmic solutions, the system's advantages include using properly designed and integrated devices---radio probes---whose task is to emit Wi-Fi waves and measure the received signal strength. Thanks to the use of the two-stage approach, the sensitivity, resolution, and accuracy of imaging have increased. Furthermore, our solution works well for radio tomography and other types of tomography because it is easy to understand and can be used in many ways.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558262"}, {"primary_key": "1739033", "vector": [], "sparse_vector": [], "title": "Quasi-optical 3D localization using asymmetric signatures above 100 GHz.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The spectrum above 100 GHz has the potential to enable accurate 3D wireless localization due to the large swath of available spectrum. Yet, existing wide-band localization systems utilize the time of arrival measurements requiring strict time synchronization. In this paper, we present 123-LOC, a novel non-coherent system for one-shot dual-polarized 3D localization above 100 GHz. Our key idea is to create unique asymmetric THz fingerprints in 3D so that a wireless node can jointly infer its angular position and distance by taking hints from the measured power-spectrum profile. We introduce a dual-polarized dual-slit waveguide structure that emits out signals into free-space with a key feature that the beam pattern depends on the frequency of the signal and the geometry of the slit. To distinguish the emissions from the two slits, we use polarization diversity and manipulate the aperture geometry of the two slits so that they transmit slightly different angular-spectral signatures. Our over-the-air experiments demonstrate that 123-LOC achieves an average angle estimation error of 1° together with millimeter-scale ranging resolution, solely through non-coherent power measurements.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517022"}, {"primary_key": "1739037", "vector": [], "sparse_vector": [], "title": "In-situ data curation: a key to actionable AI at the edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning (ML) algorithms have shown great potential in edge-computing environments, however, the literature mainly focuses on model inference only. We investigate how ML can be operationalized and how in-situ curation can improve the quality of edge applications, in the context of ML-assisted environmental surveys. We show that camera-enabled ML systems deployed on edge devices can enable scientists to perform real-time monitoring of species of interest or characterization of natural habitats. However, the benefit of this new technology is only as good as the quality and accuracy of the edge ML model inferences. In this demonstration, we show that with small additional time investment, domain scientists can manually curate ML model outputs and thus obtain highly reliable scientific insights, leading to more effective and scalable environmental surveys.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558758"}, {"primary_key": "1739039", "vector": [], "sparse_vector": [], "title": "A-mash: providing single-app illusion for multi-app use through user-centric UI mashup.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sang<PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Oh", "Insik Shin"], "summary": "Mobile apps offer a variety of features that greatly enhance user experience. However, users still often find it difficult to use mobile apps in the way they want. For example, it is not easy to use multiple apps simultaneously on a small screen of a smartphone. In this paper, we present A-Mash, a mobile platform that aims to simplify the way of interacting with multiple apps concurrently to the level of using a single app only. A key feature of A-Mash is that users can mash up the UIs of different existing mobile apps on a single screen according to their preferences. To this end, A-Mash 1) extracts UIs from unmodified existing apps (dynamic UI extraction) and 2) embeds extracted UIs from different apps into a single wrapper app (cross-process UI embedding), while 3) making all these processes hidden from the users (transparent execution environment). To the best of our knowledge, A-Mash is the first work to enable UIs of different unmodified legacy apps to seamlessly integrate and synchronize on a single screen, providing an illusion as if they were developed as a single app. A-Mash offers great potential for a number of useful usage scenarios. For instance, a user can mashup UIs of different IoT administration apps to create an all-in-one IoT device controller or one can mashup today's headlines from different news and magazine apps to craft one's own news headline collection. In addition, A-Mash can be extended to an AR space, in which users can map UI elements of different mobile apps to physical objects inside their AR scenes. Our evaluation of the A-Mash prototype implemented in Android OS demonstrates that A-Mash successfully supports the mashup of various existing mobile apps with little or no performance bottleneck. We also conducted in-depth user studies to assess the effectiveness of the A-Mash in real-world use cases.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560522"}, {"primary_key": "1739041", "vector": [], "sparse_vector": [], "title": "Protego: securing wireless communication via programmable metasurface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Phased array beamforming has been extensively explored as a physical layer primitive to improve the secrecy capacity of wireless communication links. However, existing solutions are incompatible with low-profile IoT devices due to cost, power and form factor constraints. More importantly, they are vulnerable to eavesdroppers with a high-sensitivity receiver. This paper presents Protego, which offloads the security protection to a metasurface comprised of a large number of 1-bit programmable unit-cells (i.e., phase shifters). Protego builds on a novel observation that, due to phase quantization effect, not all the unit-cells contribute equally to beamforming. By judiciously flipping the phase shift of certain unit-cells, Protego can generate artificial phase noise to obfuscate the signals towards potential eavesdroppers, while preserving the signal integrity and beamforming gain towards the legitimate receiver. A hardware prototype along with extensive experiments has validated the feasibility and effectiveness of Protego.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560547"}, {"primary_key": "1739043", "vector": [], "sparse_vector": [], "title": "A real-time edge-AI system for reef surveys.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Crown-of-Thorn Starfish (COTS) outbreaks are a major cause of coral loss on the Great Barrier Reef (GBR) and substantial surveillance and control programs are ongoing to manage COTS populations to ecologically sustainable levels. In this paper, we present a comprehensive real-time machine learning-based underwater data collection and curation system on edge devices for COTS monitoring. In particular, we leverage the power of deep learning-based object detection techniques, and propose a resource-efficient COTS detector that performs detection inferences on the edge device to assist marine experts with COTS identification during the data collection phase. The preliminary results show that several strategies for improving computational efficiency (e.g., batch-wise processing, frame skipping, model input size) can be combined to run the proposed detection model on edge hardware with low resource consumption and low information loss.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558278"}, {"primary_key": "1739044", "vector": [], "sparse_vector": [], "title": "SmartLens: sensing eye activities using zero-power contact lens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qing We", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "As the most important organs of sense, human eyes perceive 80% information from our surroundings. Eyeball movement is closely related to our brain health condition. Eyeball movement and eye blink are also widely used as an efficient human-computer interaction scheme for paralyzed individuals to communicate with others. Traditional methods mainly use intrusive EOG sensors or cameras to capture eye activity information. In this work, we propose a system named SmartLens to achieve eye activity sensing using zero-power contact lens. To make it happen, we develop dedicated antenna design which can be fitted in an extremely small space and still work efficiently to reach a working distance more than 1 m. To accurately track eye movements in the presence of strong self-interference, we employ another tag to track the user's head movement and cancel it out to support sensing a walking or moving user. Comprehensive experiments demonstrate the effectiveness of the proposed system. At a distance of 1.4 m, the proposed system can achieve an average accuracy of detecting the basic eye movement and blink at 89.63% and 82%, respectively.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560532"}, {"primary_key": "1739046", "vector": [], "sparse_vector": [], "title": "PyramidFL: a fine-grained client selection framework for efficient federated learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) is an emerging distributed machine learning (ML) paradigm with enhanced privacy, aiming to achieve a \"good\" ML model for as many as participants while consuming as little as wall clock time. By executing across thousands or even millions of clients, FL demonstrates heterogeneous statistical characteristics and system divergence widely across participants, making its training suffer when adopting the traditional ML paradigm. The root cause of the training efficiency degradation is the random client selection criteria. Although existing FL paradigms propose several optimization schemes for client selection, they are still coarse-grained due to their under-exploitation on the clients' data and system heterogeneity, yielding sub-optimal performance for a variety of FL applications. In this paper, we propose PyramidFL1 to speed up the FL training while achieving a higher final model performance (i.e., time-to-accuracy). The core of PyramidFL is a fine-grained client selection, in which PyramidFL does not only focus on the divergence of those selected participants and non-selected ones for client selection but also fully exploits the data and system heterogeneity within selected clients to profile their utility more efficiently. Specifically, PyramidFL first determines the utility-based client selection from the global (i.e., server) view and then optimizes its utility profiling locally (i.e., client) for further client selection. In this way, we can prioritize the use of those clients with higher statistical and system utility consistently. In comparison with the state-of-the-art (i.e., Oort), our evaluation on the open-source FL benchmark shows that PyramidFL improves the final model accuracy by 3.68% -- 7.33%, with a speedup of 2.71 x -- 13.66X on the wall clock time consumption.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517017"}, {"primary_key": "1739047", "vector": [], "sparse_vector": [], "title": "Romou: rapidly generate high-performance tensor kernels for mobile GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Jicheng Wen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile GPU, as a ubiquitous and powerful accelerator, plays an important role in accelerating on-device DNN (Deep Neural Network) inference. The frequent-upgrade and diversity of mobile GPUs require automatic kernel generation to empower fast DNN deployment. However, current generated kernels have poor performance.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517020"}, {"primary_key": "1739048", "vector": [], "sparse_vector": [], "title": "Development of C-plane DoS attacker for O-RAN FHI.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fransiscus <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Open radio access network (O-RAN) introduces a new architecture with new components and interfaces, which suffers from numerous security challenges and risks. The C-Plane denial-of-service (DoS) attack is considered one of the security threats for O-RAN fronthaul interface (FHI) [6]. In this paper, we develop a software tool named 'C-Plane DoS Attacker' to conduct the C-Plane DoS attack for O-RAN FHI. The effectiveness of the proposed C-Plane DoS Attacker is verified on top of a DU-RU testbed developed based on the source code provided by the O-RAN Software Community (OSC).", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558259"}, {"primary_key": "1739050", "vector": [], "sparse_vector": [], "title": "Vues: practical mobile volumetric video streaming through multiview transcoding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The emerging volumetric videos offer a fully immersive, six degrees of freedom (6DoF) viewing experience, at the cost of extremely high bandwidth demand. In this paper, we design, implement, and evaluate Vues, an edge-assisted transcoding system that delivers high-quality volumetric videos with low bandwidth requirement, low decoding overhead, and high quality of experience (QoE) on mobile devices. Through an IRB-approved user study, we build a first-of-its-kind QoE model to quantify the impact of various factors introduced by transcoding volumetric content into 2D videos. Motivated by the key observations from this user study, Vues employs a novel multiview approach with the overarching goal of boosting QoE. The Vues edge server adaptively transcodes a volumetric video frame into multiple 2D views with the help of a few lightweight machine learning models and strategically balances the extra bandwidth consumption of additional views and the improved QoE, indicated by our QoE model. The client selects the view that optimizes the QoE among the delivered candidates for display. Comprehensive evaluations using a prototype implementation indicate that <PERSON>ues dramatically outperforms existing approaches. On average, it improves the QoE by 35% (up to 85%), compared to single-view transcoding schemes, and reduces the bandwidth consumption by 95%, compared to the state-of-the-art that directly streams volumetric videos.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517027"}, {"primary_key": "1739051", "vector": [], "sparse_vector": [], "title": "CORE-lens: simultaneous communication and object recognition with disentangled-GAN cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yimao Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Optical camera communication (OCC) enabled by LED and embedded cameras has attracted extensive attention, thanks to its rich spectrum availability and ready deployability. However, the close interactions between OCC and the indoor spaces have created two major challenges. On one hand, the stripe pattern incurred by OCC may greatly damage the accuracy of image-based object recognition. On the other hand, the patterns inherent to indoor spaces can significantly degrade the decoding performance of reflected OCC. To this end, we propose CORE-Lens as a pipeline to make the mutual interference transparent to existing OR and OCC algorithms. Essentially, CORE-Lens treats the two challenges as two sides of a signal mixture issue: the signals transmitted by OCC get mixed with background images so well that their features become entangled. Consequently, CORE-Lens exploits the idea of disentangled representation learning to separate the mixed signals in the feature space: while the GAN-reconstructed clean background images are used to perform object recognition, OCC decoding is conducted on the residual of the original image after subtracting the reconstructed background. Our extensive experiments on evaluating the real-life performance of CORE-Lens evidently demonstrate its superiority over conventional approaches.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560526"}, {"primary_key": "1739052", "vector": [], "sparse_vector": [], "title": "A facial authentication system using post-quantum-secure data generated on mobile devices.", "authors": ["<PERSON>", "Rosario Arjona", "<PERSON>", "Iluminada Baturone"], "summary": "This paper describes a demonstrator of a post-quantum-secure facial authentication system distributed between a mobile device acting as a client and a remote computer acting as an authentication server. Homomorphic encryption based on Classic McE<PERSON>, one of the fourth-round candidates of the NIST post-quantum standardization process, is carried out by the client for protecting the biometric data extracted from the users' faces at enrollment and verification. The remote computer only stores and compares the received protected data, thus preserving user privacy. An Android App and a Graphical User Interface (GUI) were implemented at the client and the server, respectively, to show the system performance in terms of computation and security.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558761"}, {"primary_key": "1739053", "vector": [], "sparse_vector": [], "title": "Mobi2Sense: enabling wireless sensing under device motions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Besides the communication function, various RF signals such as WiFi and RFID have been actively exploited for sensing purposes recently. However, a missing component of existing RF sensing is sensing under device motions. This paper takes the first step to involve device mobility into the ecosystem of RF sensing. Owning to the miniaturization and low cost of ultra-wideband (UWB) chips in recent years, we propose to integrate the accuracy of UWB sensing with device mobility to support truly ubiquitous RF sensing. This is a challenging task because the motion artifacts from RF devices can easily overwhelm the target motion, such as subtle chest displacement for respiration sensing. In this demo, we propose Mobi2Sense to support sensing under device motions. We propose novel signal processing schemes to remove the effect of device motions on sensing and prototype Mobi2Sense using a commodity UWB module. Comprehensive evaluation demonstrates that Mobi2Sense is able to \"hear\" music and \"see\" human respiration at high accuracy in the presence of device motions.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558748"}, {"primary_key": "1739054", "vector": [], "sparse_vector": [], "title": "Involving ultra-wideband in consumer-level devices into the ecosystem of wireless sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Among various wireless sensing modalities, Ultra-Wideband (UWB) exhibits unique advantages such as fine granularity owing to its super large bandwidth (500 MHz - 2 GHz). Though promising, UWB sensing was only demonstrated on dedicated hardware including DW1000 and XETHRU X4 which are not available in existing consumer-level devices. In the last few years, we observed an interesting trend of UWB module being embedded into consumer-level devices such as smartphones and smart watches. However, leveraging UWB module inside consumer-level devices for sensing poses new challenges. One key challenge is that while dedicated UWB hardware can present us with raw physical-layer signal amplitude and phase, only upper-layer distance and angle information can be extracted from consumer-level devices. In this demo, we address the challenges and present the first UWB sensing system hosted on iPhone and Apple Watch without any dedicated hardware components. We show that with just the upper-layer UWB data reported from smartphones, exciting sensing applications such as fine-grained 3D handwriting and multi-target tracking can be realized, pushing RF sensing one step forward towards real-life adoption.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558745"}, {"primary_key": "1739055", "vector": [], "sparse_vector": [], "title": "Edge assisted frame interpolation and super resolution for efficient 360-degree video delivery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "360° videos are getting popular providing an immersive streaming experience for the user, nevertheless, demand high bandwidth in mobile networks due to their larger spherical frames. In this preliminary work, we propose to combine frame interpolation and super resolution methods to optimize tile based 360° video delivery by streaming them at low qualities in network and increasing the quality leveraging Multi Access Edge Computing. We propose a mechanism to adaptively decide this quality conversion at the client side which improves average video quality by 30% and bandwidth saving by 43.3% compared to existing tile based streaming.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558261"}, {"primary_key": "1739056", "vector": [], "sparse_vector": [], "title": "Deep learning model optimization for faster inference using multi-task learning for embedded systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The research aims to develop and optimize a deep learning model for faster inference using multi-task learning for embedded systems. Experiments using face photos and sound in the form of a spectrogram were prepared to verify the model's performance in recognizing a person and their emotional state. Research has shown that in IoT devices, the inference is faster when a multi-tasking model is used compared to a system based on several models, each responsible for inferring one thing.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558274"}, {"primary_key": "1739057", "vector": [], "sparse_vector": [], "title": "Network-side digital contact tracing on a large university campus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe a study conducted at a large public university campus in the United States which shows the efficacy of network log information for digital contact tracing and prediction of COVID-19 cases. Over the period of January 18, 2021 to May 7, 2021, more than 216 million client-access-point associations were logged across over 11,000 wireless access points (APs). The association information was used to find potential contacts for approximately 30,000 individuals. Contacts are determined using an AP colocation algorithm, which supposes contact when two individuals connect to the same WiFi AP at approximately the same time. The approach was validated with a truth set of 350 positive COVID-19 cases inferred from the log data by observing associations with APs in isolation residence halls reserved for individuals with a confirmed (clinical) positive COVID-19 test result. The network log data and AP-colocation have a predictive value of greater than 10%; more precisely, the contacts of an individual with a confirmed positive COVID-19 test have greater than a 10% chance of testing positive in the following 7 days (compared with a 0.79% chance if chosen at random, a relative risk ratio of 12.6). A cumulative exposure score is computed to account for exposure to multiple individuals that test positive. Over the duration of the study, the exposure score predicts positive cases with a true positive rate of 16.5% and missed detection rate of 79% at a specified operating point.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517029"}, {"primary_key": "1739058", "vector": [], "sparse_vector": [], "title": "DIY-IPS: towards an off-the-shelf accurate indoor positioning system.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present DIY-IPS - Do It Yourself - Indoor Positioning System, an open-source real-time indoor positioning mobile application. DIY-IPS detects users' indoor position by employing dual-band RSSI fingerprinting of available WiFi access points. The app can be used, without additional infrastructural costs, to detect users' indoor positions in real time. We published our app as an open source to save other researchers time recreating it. The app enables researchers/users to (1) collect indoor positioning datasets with a ground truth label, (2) customize the app for higher accuracy or other research purposes (3) test the accuracy of modified methods by live testing with ground truth. We ran preliminary experiments to demonstrate the effectiveness of the app.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558752"}, {"primary_key": "1739060", "vector": [], "sparse_vector": [], "title": "IABEST: an integrated access and backhaul 5G testbed for large-scale experimentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Ilario Fi<PERSON>i", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter wave (mmWave) communications have the potential to dramatically increase the throughput of 5G-and-beyond wireless networks. However, the challenging propagation conditions typical of higher frequencies require expensive base station densification to guarantee reliable Radio Access Networks (RANs). Integrated Access and Backhaul (IAB), a solution where wireless access and backhaul use the same waveform, spectrum, and protocol stack, has been proposed and standardized as a highly effective means of decreasing these costs. While IAB is considered a key enabler for high-frequency RANs, experimental research in this context is hampered by the lack of accessible testing platforms. In this demonstration, we showcase IABEST, a large-scale end-to-end IAB testbed based on open-source software and compatible with off-the-shelf hardware. We show how to deploy IABEST capabilities at scale on Colosseum, a publicly available massive channel emulator. Finally, we show how IABEST can support researchers in data collection and algorithm testing from the highest levels of network abstraction down to scheduling decisions.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558750"}, {"primary_key": "1739061", "vector": [], "sparse_vector": [], "title": "Opportunistic mobile crowd computing: task-dependency based work-stealing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pubudu <PERSON>"], "summary": "Mobile devices are ubiquitous, heterogeneous and resource constrained. Execution of complex tasks in mobile devices are resource demanding and time-consuming, forcing developers to offload portions of the complex task to cloud or edge computing resources. Task offloading becomes increasingly challenging due to intermittent Internet connectivity, remote resource unavailability, high costs, latency, and limited energy of the mobile device. A mobile device user is typically surrounded by other mobile devices, which can be leveraged to collaboratively compute a resource-intensive task. With the help of a work sharing framework, it is feasible for devices to communicate and collaborate. However, some mobile devices are incapable of computing complex portions of the task, and some can compute in accelerated mode. In this demonstration, we introduce Honeybee-T a collaborative mobile crowd computing framework that uses a work-stealing algorithm. The algorithm allows work sharing with collaborating devices based on devices' computational ability and task-dependencies. The experiments show that by employing Honeybee-T framework, when compared to monolithic execution of a large compute-intensive task, there is a considerable performance gain, as well as energy savings.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558751"}, {"primary_key": "1739063", "vector": [], "sparse_vector": [], "title": "Experience: pushing indoor localization from laboratory to the wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangyu Bian", "<PERSON><PERSON>", "<PERSON>"], "summary": "While GPS-based outdoor localization has become a norm, very few indoor localization systems have been deployed and used. In this paper, we share our 5-year experience on the design, development and evaluation of a large-scale WiFi indoor localization system. We address practical challenges encountered to bridge the gap between indoor localization research in the laboratory and system deployment in the wild. The system is currently used in 1469 shopping malls, 393 office buildings and 35 hospitals across 35 cities to provide location service to millions of users on a daily basis. We hope the shared experience can benefit the design of real-world indoor localization systems and the practical problems identified can change the focus of indoor localization research. We released our dataset that contains fingerprints collected from 1469 shopping malls and one office building.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560546"}, {"primary_key": "1739064", "vector": [], "sparse_vector": [], "title": "MoiréPose: ultra high precision camera-to-screen pose estimation based on Moir<PERSON> pattern.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Camera tracking has become a key technology for various application scenarios, especially for AR-based camera-to-screen interaction. Demand for subtle motion detection in camera tracking makes it essential to explore the six degrees of freedom (6-DoF) pose detection with ultra-high precision. In this paper, we propose a novel sensing method MoiréPose to achieve ultra-high precision on the camera's 6-DoF pose estimation. The purpose of MoiréPose is to derive the camera's 3-DoF position and 3-DoF posture relative to the screen according to the captured moiré pattern, which is produced by the superposition of the camera's Color Filter Array (CFA) and the screen raster projected onto the CFA layer. Based on moiré pattern's high sensitivity to 6-DoF pose movement and robustness to the environmental interference in the frequency domain, we propose a spectrogram-based method to realize the camera's 6-DoF detection with ultra-high precision. Moreover, we propose a thumbnail-based method to effectively extend the working range of Moiré<PERSON>ose, so as to realize pervasive camera-to-screen interaction. We have implemented a prototype system and evaluate the performance in real-world environments. Extensive experiment results show that MoiréPose achieves an average position error of 7.5mm and an overall posture error of 1.66°.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560537"}, {"primary_key": "1739065", "vector": [], "sparse_vector": [], "title": "Cosmo: contrastive fusion learning with small data for multimodal human activity recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human activity recognition (HAR) is a key enabling technology for a wide range of emerging applications. Although multimodal sensing systems are essential for capturing complex and dynamic human activities in real-world settings, they bring several new challenges including limited labeled multimodal data. In this paper, we propose Cosmo, a new system for contrastive fusion learning with small data in multimodal HAR applications. Cosmo features a novel two-stage training strategy that leverages both unlabeled data on the cloud and limited labeled data on the edge. By integrating novel fusion-based contrastive learning and quality-guided attention mechanisms, <PERSON>smo can effectively extract both consistent and complementary information across different modalities for efficient fusion. Our evaluation on a cloud-edge testbed using two public datasets and a new multimodal HAR dataset shows that <PERSON><PERSON> delivers significant improvement over state-of-the-art baselines in both recognition accuracy and convergence delay.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560519"}, {"primary_key": "1739066", "vector": [], "sparse_vector": [], "title": "Wiffract: a new foundation for RF imaging via edge tracing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we are interested in high-quality imaging of still objects with only received power measurements of off-the-shelf WiFi transceivers. We show that the scattered WiFi signals off of objects carry much richer information about the edges of the objects than the surface points. Based on this observation, we then propose a completely different way of thinking about this imaging problem. More specifically, we propose Wiffract, a new foundation for imaging objects via edge tracing. Our approach uses the Geometrical Theory of Diffraction (GTD) and the corresponding Keller cones to image edges of the object. We extensively validate our approach with 37 experiments in three different areas, including through-wall scenarios. We take developing a WiFi Reader as one example application to showcase the capabilities of our proposed pipeline. More specifically, we show how our approach can successfully image several alphabet-shaped objects. We further show that our approach enables WiFi to read, i.e., correctly classify the letters, with an accuracy of 86.7%. Finally, we show how our approach enables WiFi to image and read through walls, by imaging the details and further reading the letters of the word \"BELIEVE\" through walls. Overall, our proposed approach can open up new directions for RF imaging.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3514261"}, {"primary_key": "1739067", "vector": [], "sparse_vector": [], "title": "RF-DNA: large-scale physical-layer identifications of RFIDs via dual natural attributes.", "authors": ["Qingrui Pan", "Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physical-layer identification aims to identify wireless devices during RF communication by exploiting the imperfections of their radio circuitry, i.e., hardware fingerprint. Previous work proposed several hardware fingerprints for RFIDs (e.g., TIE, ABD, PSD, etc). However, these proposed fingerprints suffer from either unscalability or acquisition inefficiency. This work presents RF-DNA, a new hardware fingerprint composed of millions of Dual Natural Attributes (DNA) organized in a helical structure, where a pair of DNA represents a tag's intrinsic response at some frequency. We take advantage of the frequency agnostic phenomenon that a commercial RFID tag can respond within a wider band than the regulated, to acquire 10X more features than previous fingerprints. At the heart of this work are the context-free acquisition approach to extracting DNA from backscatter signals; and the accurate DNA matching algorithm for verifying a tag's identity. A total of 160,000 RF-DNA instances were collected from 16,000 tags using a customized automatic acquisition system. We subsequently carried out large-scale experiments to test the identification accuracy of RF-DNA and previously proposed fingerprints. Our comprehensive evaluation reveals that RF-DNA can achieve a mean accuracy of 95.98%. In contrast, those of previous fingerprints fall to 60% below when in face of thousands of tags.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517028"}, {"primary_key": "1739068", "vector": [], "sparse_vector": [], "title": "DoCam: depth sensing with an optical image stabilization supported RGB camera.", "authors": ["<PERSON><PERSON> Pan", "Feitong Tan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qingyang Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiaoyu Ji"], "summary": "Optical image stabilizers (OIS) are widely used in digital cameras to counteract motion blur caused by camera shakes in capturing videos and photos. In this paper, we sought to expand the applicability of the lens-shift OIS technology for metric depth estimation, i.e., let a RGB camera to achieve the similar function of a time-of-flight (ToF) camera. Instead of having to move the entire camera for depth estimation, we propose DoCam, which controls the lens motion in the OIS module to achieve 3D reconstruction. After controlling the lens motion by altering the MEMS gyroscopes readings through acoustic injection, we improve the traditional bundle adjustment algorithm by establishing additional constraints from the linearity of the lens control model for high-precision camera pose estimation. Then, we elaborate a dense depth reconstruction algorithm to compute depth maps at real-world scale from multiple captures with micro lens motion (i.e., ≤ 3 mm). Extensive experiments demonstrate that our proposed DoCam can enable a 2D color camera to estimate high-accuracy depth information of the captured scene by means of controlling lens motion in the OIS. DoCam is suitable for a variety of applications that require depth information of the scenes, especially when only a single color camera is available and located at a fixed position.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560523"}, {"primary_key": "1739069", "vector": [], "sparse_vector": [], "title": "Which uber is mine?: identifying target in crowd of objects with RF analysis and AR visual tags.", "authors": ["Junghun Park", "<PERSON><PERSON>", "Ji<PERSON>on Ryoo"], "summary": "This paper presents ABC, the AR(augmented reality), and BLE combined target detector in a crowd of objects. The AR design of BLE analysis is a ready-to-use option for the ever-growing app-based ride market. The carefully designed AR tags and BLE beacons effectively collaborate to identify the target car. Such that a passenger can pinpoint the ride from a far distance. ABC consistently identify the target in a realistic environment through evaluations.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558268"}, {"primary_key": "1739071", "vector": [], "sparse_vector": [], "title": "PROS: an efficient pattern-driven compressive sensing framework for low-power biopotential-based wearables with on-chip intelligence.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While the global healthcare market of wearable devices has been growing significantly in recent years and is predicted to reach $60 billion by 2028, many important healthcare applications such as seizure monitoring, drowsiness detection, etc. have not been deployed due to the limited battery lifetime, slow response rate, and inadequate biosignal quality.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560533"}, {"primary_key": "1739072", "vector": [], "sparse_vector": [], "title": "Experimenting with localization management functions in 5G core networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Localization has achieved great attention in 5G networks, pushed by standardization. However, experimentation in 5G networks lacks the integration of network function modules designed for localization. We present our implementation of the 5G Localization Management Function. It complies with the 3GPP standard and OpenAirInterface, the most advanced framework that implements a full 5G-New Radio stack. We show that we are able to extend the functionality of OpenAirInterface, enabling location services. Finally, we demonstrate that the tool's performance satisfies the 5G Key Performance Indicators required by 3GPP for localization.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558762"}, {"primary_key": "1739074", "vector": [], "sparse_vector": [], "title": "MilliMirror: 3D printed reflecting surface for millimeter-wave coverage expansion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Next generation wireless networks embrace mmWave technology for its high capacity. Yet, mmWave radios bear a fundamental coverage limitation due to the high directionality and propagation artifacts. In this paper, we explore an economical paradigm based on 3D printing technology for mmWave coverage expansion. We propose MilliMirror, a fully passive metasurface, which can reshape and resteer mmWave beams to anomalous directions to illuminate the coverage blind spots. We develop a closed-form model to efficiently synthesize the MilliMirror design with thousands of unit elements and across a wide frequency band. We further develop an economical process based on 3D printing and metal deposition to fabricate MilliMirror. Our field test results show that MilliMirror can effectively fill the coverage holes and operate transparently to the standard mmWave beam management protocols.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517024"}, {"primary_key": "1739077", "vector": [], "sparse_vector": [], "title": "Person re-identification using wifi signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Person re-identification (Re-ID) has become increasingly important as it supports a wide range of security applications. In this work, we propose a WiFi-based person Re-ID system in 3D space, which leverages the advances of WiFi and deep learning to extract the static body shape and dynamic walking patterns to recognize people. In particular, we leverage multiple antennas on WiFi devices to capture signal reflections of the human body and produce a WiFi image of a person. We then leverage deep learning to extract both the static body shape and dynamic walking patterns for person Re-ID. Our evaluation results show that our system achieves an overall rank-1 accuracy of 87.1%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558252"}, {"primary_key": "1739078", "vector": [], "sparse_vector": [], "title": "Demonstrating OmniCells: a resilient indoor localization system to devices&apos; diversity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we demonstrate OmniCells: a cellular-based indoor localization system designed to combat the device heterogeneity problem. OmniCells is a deep learning-based system that leverages cellular measurements from one or more training devices to provide consistent performance across unseen tracking phones. In this demo, we show the effect of device heterogeneity on the received cellular signals and how this leads to performance deterioration of traditional localization systems. In particular, we show how OmniCells and its novel feature extraction methods enable learning a rich and device-invariant representation without making any assumptions about the source or target devices. The system also includes other modules to increase the deep model's generalization and resilience to unseen scenarios.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558753"}, {"primary_key": "1739079", "vector": [], "sparse_vector": [], "title": "Demonstrating hitonavi-μ: a novel wearable LiDAR for human activity recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we demonstrate a brand new design of a wearable device that enables privacy-preserving human activity recognition based on light-weight compact-size LiDAR. The device scans and represents the surrounding environment in 3D point clouds form. The system further processes this representation to define discriminative features that facilitate recognizing human activity on edge. These features are extracted using Spatio-temporal probabilistic clustering and fisher vector representations and then used to train a classification model for activity recognition purposes. Implementation and evaluation of the proposed system confirm its efficient ability to identify human activities.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558744"}, {"primary_key": "1739080", "vector": [], "sparse_vector": [], "title": "Designing, building, and characterizing RF switch-based reconfigurable intelligent surfaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this poster, we present the Reconfigurable Intelligent Surface (RIS) that we designed, built, and tested. At first, the RIS technology is briefly discussed, subsequently, our prototype details are explained, and finally, we conclude by showing the obtained test results. Our RIS design comprises arrays of patch antennas, delay lines, and programmable radio-frequency (RF) switches that enable almost-passive 3D beamforming, i.e., without active RF components.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558256"}, {"primary_key": "1739082", "vector": [], "sparse_vector": [], "title": "Enabling high accuracy pervasive tracking with ultra low power UWB tags.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the recent popularity of active ultra-wideband (UWB) tracking beacons, the next era of low-power IoT aims to offer tracking as a core feature in numerous enterprise and industrial applications. However, generating high-bandwidth tracking signals on cost-effective, low-power tags in a large scale deployment, poses fundamental challenges in energy-cost-performance trade-offs, not afforded by existing solutions that fall short in one or more critical dimensions.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560542"}, {"primary_key": "1739084", "vector": [], "sparse_vector": [], "title": "MobiCache: a mobility-aware caching technique in vehicular edge computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vehicular edge computing (VEC) brings computational resources at the edge of vehicular networks (VANETs). In VEC, the roadside unit (RSU) across the road segment acts as an edge server. The vehicle having less computational capability offloads high computation tasks to its nearby RSU for processing. There is a significant energy consumption occurs at the RSU in computing each high computation task. To minimize the energy consumption, a caching technique is used at RSUs. The greatest challenge of caching in VEC is the mobility of vehicles. In this poster, we propose a Mobility-Aware Caching technique (MobiCache) in VEC. MobiCache uses an actor-critic deep reinforcement learning framework to find the best routes for migrating the popular cache contents of RSUs according to the mobility pattern of vehicles. Simulation results show that our proposed caching strategy reduces the energy consumption by an average of 39.54% as compared to other existing caching techniques.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558266"}, {"primary_key": "1739088", "vector": [], "sparse_vector": [], "title": "LiqRay: non-invasive and fine-grained liquid recognition system.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yubo Yan", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The existing RF-based liquid identification methods commonly require a training network of liquid or the container information, such as material and width. Moreover, status quo methods are inapplicable when the solution height is lower than that of the antenna, which is generally unknown either. This paper proposes LiqRay, an RF-based solution, retaining non-invasive and fine-grained liquid recognition abilities, thus can recognize unknown solutions without prior knowledge. In dealing with the unknown container material and width, we utilize a dual-antenna model and craft a relative frequency response factor, exploring diversity of the permittivity in frequency domain. In tackling the unknown heights of solution and antenna, we devise the electric field distribution model at the receiving antenna, solving the unknown heights via spatio-differential model. Among eight different solvents, LiqRay can identify alcohol solutions with a concentration difference of 1% with 94.92% accuracy. Nevertheless, LiqRay can obtain the relative frequency response factor with a relative error of 6.7% without being affected by the height of the solution. Even if it is merely 4 cm, this is fairly lower than that of most antennas' heights, since the operating frequency is around 2 GHz.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560540"}, {"primary_key": "1739090", "vector": [], "sparse_vector": [], "title": "Authentication for drone delivery through a novel way of using face biometrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Wu", "<PERSON><PERSON>"], "summary": "Drone delivery, which makes use of unmanned aerial vehicles (UAVs) to deliver or pick up packages, is an emerging service. To ensure that a package is picked up by a legitimate drone and delivered to the correct user, mutual authentication between drones and users is critical. As delivery drones are expensive and may carry important packages, drones should keep a distance from users until the authentication succeeds. Thus, authentication approaches that require human-drone physical contact cannot be applied. Face recognition does not need human-drone contact. However, it has major limitations: (1) it needs users to enroll their face information, (2) it is vulnerable to attacks, such as 3D-printed masks and adversarial examples, and (3) it only supports a drone to authenticate a user (rather than mutual authentication). We propose a novel way of using face biometrics, without these limitations, and apply it to building an authentication system for drone delivery, named Smile2Auth. The evaluation shows that Smile2Auth is highly accurate, secure and usable.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560550"}, {"primary_key": "1739091", "vector": [], "sparse_vector": [], "title": "VIPS: real-time perception fusion for infrastructure-assisted autonomous driving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhenyu Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Infrastructure-assisted autonomous driving is an emerging paradigm that expects to significantly improve the driving safety of autonomous vehicles. The key enabling technology for this vision is to fuse LiDAR results from the roadside infrastructure and the vehicle to improve the vehicle's perception in real time. In this work, we propose VIPS, a novel lightweight system that can achieve decimeter-level and real-time (up to 100 ms) perception fusion between driving vehicles and roadside infrastructure. The key idea of VIPS is to exploit highly efficient matching of graph structures that encode objects' lean representations as well as their relationships, such as locations, semantics, sizes, and spatial distribution. Moreover, by leveraging the tracked motion trajectories, VIPS can maintain the spatial and temporal consistency of the scene, which effectively mitigates the impact of asynchronous data frames and unpredictable communication/compute delays. We implement VIPS end-to-end based on a campus smart lamppost testbed. To evaluate the performance of VIPS under diverse situations, we also collect two new multi-view point cloud datasets using the smart lamppost testbed and an autonomous driving simulator, respectively. Experiment results show that VIPS can extend the vehicle's perception range by 140% within 58 ms on average, and delivers a 4X improvement in perception fusion accuracy and 47X data transmission saving over existing approaches. A video demo of VIPS based on the lamppost dataset is available at https://youtu.be/zW4oi_EWOu0.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560539"}, {"primary_key": "1739092", "vector": [], "sparse_vector": [], "title": "Towards automatic troubleshooting for user-level performance degradation in cellular services.", "authors": ["Xiaofeng Shi", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Troubleshooting cellular service issues at the per-UE (User Equipment) level is an essential task for cellular providers. However, diagnosing service issues at per-UE level is costly because it requires advanced expertise and in-depth inspection of massive network log data. This paper presents NeTExp, a generic and comprehensive data-driven approach to automatically troubleshoot cellular service issues reported by customers. NeTExp determines whether the root cause of a user-reported service issue is from the network side or the device side through deep neural networks, which extract complex spatial-temporal feature profiles from massive network log data. The system is trained and validated using an extensive period of network and customer care data from a major cellular service provider in United States. We also present a case study on an external event that caused cellular service issues in 2020 to demonstrate the effectiveness of NeTExp on detecting network issues and identifying network-issue-related root causes at per-UE level.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560535"}, {"primary_key": "1739094", "vector": [], "sparse_vector": [], "title": "RetroIoT: retrofitting internet of things deployments by hiding data in battery readings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Campbell"], "summary": "Commercial Internet of Things (IoT) deployments are mostly closed-source systems that offer little to no flexibility to modify the hardware and software of the end devices. Once deployed, retrofitting such systems to an upgraded functionality requires replacing all the devices, which can be extremely time and cost prohibitive. End users cannot generally leverage deployed infrastructure to add their own sensors or custom data. However, we observe that IoT systems sometimes report battery voltage information to the cloud, and batteries are often user-serviceable. This indicates that perturbing the battery voltage to encode customized information could be a minimally invasive method to retrofit existing IoT devices.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560536"}, {"primary_key": "1739095", "vector": [], "sparse_vector": [], "title": "Towards behavior-independent in-hand user authentication on smartphone using vibration: poster.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As the human hand makes direct physical contact with smartphones, significant efforts have recently been made to study the behavioral information of hand gripping of smartphones for user authentication purposes. Most existing methods leverage hand gripping behavior (e.g., gripping gesture, gripping position, gripping strength) of smartphones as biometrics to identify users. However, behavioral-based biometric authentication approaches may suffer from two problems: authentication performance (accuracy) degradation due to high-intra class variations arising from changes in user behavior over time, and vulnerability under spoofing attacks. To address these issues, we propose HoldPass, which is a behavior-independent in-hand user authentication method using vibration. HoldPass is able to adapt to the changes of hand gripping behavior of smartphones by extracting unique and stable physical features of human hands and eliminating the behavior-related prior information. Specifically, in HoldPass, we propose an adversarial neural network to achieve authentication based on unique physical features. Experiments with 10 users show that HoldPass can authenticate users with 97.39% accuracy while keeping False Accepted Rates (FAR) at a minimum of 2.1%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558257"}, {"primary_key": "1739096", "vector": [], "sparse_vector": [], "title": "RF-URL: unsupervised representation learning for RF sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chunyang Xie", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The major obstacle for learning-based RF sensing is to obtain a high-quality large-scale annotated dataset. However, unlike visual datasets that can be easily annotated by human workers, RF signal is non-intuitive and non-interpretable, which causes the annotation of RF signals time-consuming and laborious. To resolve the rapacious appetite of annotated data, we propose a novel unsupervised representation learning (URL) framework for RF sensing, RF-URL, to learn a pre-training model on large-scale unannotated RF datasets that can be easily collected. RF-URL utilizes a contrastive framework to mind the gap between signal-processing-based RF sensing and learning-based RF sensing. By constructing positive and negative pairs through different signal processing representations, RF-URL seamlessly integrates the existing RF signal processing algorithms into the learning-based networks. Moreover, the RF-URL is carefully designed to take into account the asymmetric characteristics of different RF signal processing representations. We show that RF-URL is universal to a variety of RF sensing tasks by evaluating RF-URL in three typical RF sensing tasks (human gesture recognition, 3D pose estimation and silhouette generation) based on two general RF devices (WiFi and radar). All experimental results strongly demonstrate that RF-URL takes an important step towards learning-based solutions for large-scale RF sensing applications.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560529"}, {"primary_key": "1739098", "vector": [], "sparse_vector": [], "title": "BSMA: scalable LoRa networks using full duplex gateways.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With its ability to communicate long distances, LoRa promises city-scale IoT deployments for smart city applications. This long-range, however, also increases contention as many thousands of devices are connected. Recently, CSMA has been proposed as a viable MAC for resolving contention in LoRa networks. In this paper, supported by measurements, we demonstrate that CSMA is ineffective in urban deployments. While gateways stationed at rooftops enjoy a long communication range, 70% of the devices placed at street level fail to sense each others' transmissions and remain hidden, owing to obstructions by tall structures. We present Busy Signal Multiple Access (BSMA), where the LoRa gateway transmits a downlink busy signal while receiving an uplink transmission. The IoT devices defer uplink transmissions while a busy signal is present. Practically viable BSMA requires a full-duplex LoRa gateway - i.e., a gateway that can simultaneously receive and transmit in the same channel. We develop the first full Duplex LoRa gateway in the 915 MHz ISM band, overcoming challenges that arise from a 9× greater delay spread and the need for 1000× greater self-interference cancellation. Our implementation works with COTS LoRa devices and improves network capacity by 100% compared to CSMA in bursty loads while being fair to all IoT devices near and far.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560544"}, {"primary_key": "1739099", "vector": [], "sparse_vector": [], "title": "TMM-TinyML: tensor memory mapping (TMM) method for tiny machine learning (TinyML).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "TinyML: Tiny in size, big in impact! In this paper, we present a Tensor Memory Mapping (TMM) method, which can accurately calculate the on-device execution memory consumed by a range of ML and TinyML models during execution on small central processing units (CPUs), microcontroller units (MCUs), and single board computers (SBCs).", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558265"}, {"primary_key": "1739100", "vector": [], "sparse_vector": [], "title": "TinyML-CAM: 80 FPS image recognition in 1 kB RAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "TinyML: Tiny in size, big in impact! This paper presents TinyML-CAM pipeline for real-time and memory-efficient image recognition on IoT boards. TinyML-CAM can be used by developers to implement their customized tasks in ≈ 30 minutes, with minimal code configuration. We evaluated TinyML-CAM by using it to create a RandomForestClassifier (RF) based real-time image recognition system, which ran at 80 FPS and consumed only 1 kB of RAM on ESP32.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558264"}, {"primary_key": "1739102", "vector": [], "sparse_vector": [], "title": "De-spreading over the air: long-range CTC for diverse receivers with LoRa.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unlicensed LPWANs on ISM bands share the spectrum with various wireless techniques, such as Wi-Fi, Bluetooth, and ZigBee. The explosion of IoT deployments calls for an increasing need for long-range cross-technology communication (CTC) between LPWANs and other techniques. Yet, existing technologies cannot achieve real long-range CTC for commodity wireless. We propose L2X, which provides long-range CTC to diverse receivers with LoRa transmitters. At the heart of L2X, we design an energy-concentrating demodulation mechanism that de-spreads LoRa chirps over the air. Therefore, L2X enables non-LoRa receivers to detect and demodulate LoRa signals even under extremely low SNR. We address practical challenges in L2X design. We propose a packet detection method to detect low-SNR LoRa transmissions at non-LoRa receivers. To decode LoRa transmissions, we accurately synchronize the demodulation window with incoming packets and propose a cross-domain demodulation approach to enhance the demodulation SNR. We implement L2X, all using commodity devices, and extensively evaluate its performance. The results show that L2X achieves 1.2 km CTC with the signal -9 dB below the noise floor, improving the distance by 30X compared with state-of-the-arts.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560524"}, {"primary_key": "1739106", "vector": [], "sparse_vector": [], "title": "mmEve: eavesdropping on smartphone&apos;s earpiece via COTS mmWave device.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Earpiece mode of smartphones is often used for confidential communication. In this paper, we proposed a remote(>2m) and motion-resilient attack on smartphone earpiece. We developed an end-to-end eavesdropping system mmEve based on a commercial mmWave sensor to recover speech emitted from smartphone earpiece. The rationale of the attack is based on our observation that, soundwaves emitted from the smartphone's earpiece have a strong correlation with reflected mmWaves from the smartphone's rear. However, we find the recovered speech suffers from the sensor's self-noise and smartphone user's motion which limit attack distance to less than 2m, causing limited threats in real world. We modeled the motion interference under mmWave sensing and proposed a motion-resilient solution by optimizing the fitting function on I/Q plane. To achieve a practical attack with reasonable attack distance, we developed a GAN-based denoising scheme to eliminate the noise pattern of the sensor, which boosted the attack range to 6--8m. We evaluated mmEve with extensive experiments and find 23 different models of smartphones manufactured by Samsung, Huawei, etc. can be compromised by the proposed attack.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560543"}, {"primary_key": "1739107", "vector": [], "sparse_vector": [], "title": "Enabling secure touch-to-access device pairing based on human body&apos;s electrical response.", "authors": ["<PERSON>", "<PERSON> Gu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent efforts in reducing user involvement during device pairing have successfully introduced touch-to-access. To detect whether two devices are being held by the same person, existing touch-to-access solutions extract features from a shared information source to generate pairing keys. They focus on validating the device's authenticity by only requiring the user's simple touching of the device, however, ignore the device holder's legitimacy and pairing intent. Moreover, the pairing keys may be vulnerable to eavesdropping attacks since they are exchanged over an open wireless link (e.g., WiFi or Bluetooth). In this paper, we develop a secure device pairing mechanism that essentially uses the human body to generate and transmit user-specific pairing keys, ensuring the user's legitimacy and pairing intent, as well as improving key transmission reliability. Our work is based on the observation that the human body produces a unique response to the electrical signal flowing through it, and different bodies induce distinct responses to the signal. The built-in microphone on devices captures ambient sound as an entropy source and converts it into an electrical signal, which is subsequently processed and transmitted by the human body for device pairing. We build a prototype using off-the-shelf microphones and conduct extensive experiments with 31 participants to evaluate its security performance and usability. The results show that our system achieves a pairing success rate of 97.74% and an equal error rate of 2.28%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3564146"}, {"primary_key": "1739108", "vector": [], "sparse_vector": [], "title": "BiTouch: enabling secure touch-to-access device pairing based on human body&apos;s electrical response.", "authors": ["<PERSON>", "<PERSON> Gu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a secure device pairing approach, called BiTouch, using the human body as a conductor to generate and transmit user-specific pairing keys for advancing touch-to-access policy. BiTouch is designed based on the observation that the human body responds uniquely to electrical signals flowing through it. Built-in microphones on devices are essentially used to capture ambient sound as entropy and convert it into an electrical signal, which is subsequently transmitted by the body for device pairing. We implement BiTouch using off-the-shelf microphones and evaluate it with 31 participants. The results demonstrate that BiTouch ensures the user's legitimacy and key transmission reliability, and achieves a pairing success rate of 97.74% and an equal error rate of 2.28%.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558273"}, {"primary_key": "1739109", "vector": [], "sparse_vector": [], "title": "Indoor localization using light spectral information.", "authors": ["<PERSON><PERSON><PERSON>", "Ji<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we investigate the impacts of location on the spectral distribution of received light, i.e., the intensity of light for different wavelengths, in indoor environments. Our findings show that, even when using the same light source, different locations exhibit slightly different spectral distribution due to reflections from their localised environment containing different materials or colours. Based on this observation, we present Spectral-Loc, a novel indoor localization method that employs light spectrum information to detect the device's position. Because spectrum sensors are increasingly being used in new products and applications, such as white balance in smartphone photography, Spectral-Loc can be quickly implemented without the need for extra hardware or infrastructure. We used a commercially available light spectrum sensor, the AS7265x, to prototype Spectral-Loc, which can measure light intensity over 18 different wavelength sub-bands. We benchmark the localization accuracy of Spectral-Loc against the conventional light intensity sensors that provide only a single intensity value. Our evaluations in two indoor areas, a meeting room and a large office, show that using light spectral information considerably decreases the localization error for different percentiles.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558249"}, {"primary_key": "1739110", "vector": [], "sparse_vector": [], "title": "Multi-modal sensing for behaviour recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a multi-modal sensing system for reliable recognition of animal behaviour that can operate in varying environmental conditions. We present the system architecture including the utilised sensors, visualise some intermediate processes, and provide a quantitative performance evaluation in one of the target use cases.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558277"}, {"primary_key": "1739111", "vector": [], "sparse_vector": [], "title": "Automatic calibration of magnetic tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Magnetic sensing is emerging as an enabling technology for various engaging applications. Representative use cases include high-accuracy posture tracking, human-machine interaction, and haptic sensing. This technology uses multiple MEMS magnetometers to capture the changing magnetic field at a close distance. However, magnetometers are susceptible to real-world disturbances, such as hard- and soft-iron effects. As a result, users need to perform a cumbersome and lengthy calibration process frequently, severely limiting the usability of magnetic tracking.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558760"}, {"primary_key": "1739112", "vector": [], "sparse_vector": [], "title": "Automatic calibration of magnetic tracking: demo.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Magnetic sensing is emerging as an enabler technology for various engaging applications. Representative use-cases include high-accuracy posture tracking, human-machine interaction, haptic sensing, etc. This technology uses multiple MEMS magnetometers to capture the changing magnetic field at a close distance. However, magnetometers are susceptible to real-world disturbances, such as hard- and soft-iron effects. As a result, users need to perform a cumbersome and lengthy calibration process frequently, severely limiting the usability of magnetic tracking.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3567653"}, {"primary_key": "1739113", "vector": [], "sparse_vector": [], "title": "A wifi vision-based 3D human mesh reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we present, Wi-Mesh, a WiFi vision-based 3D human mesh construction system. Our system leverages the advances of WiFi to visualize the shape and deformations of the human body for 3D mesh construction. In particular, it estimates the two-dimensional angle of arrival (2D AoA) of the WiFi signal reflections to enable WiFi devices to \"see\" the physical environment as we humans do. It then extracts only the images of the human body from the physical environment, and leverages deep learning models to digitize the extracted human body into 3D mesh representation. Experimental evaluation under various indoor environments shows that Wi-Mesh achieves an average vertices location error of 2.58cm and joint position error of 2.24cm.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558247"}, {"primary_key": "1739114", "vector": [], "sparse_vector": [], "title": "SalientVR: saliency-driven mobile 360-degree video streaming with gaze information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile 360° video streaming has grown significantly in popularity but the quality of experience (QoE) suffers from insufficient wireless network bandwidth. The state-of-the-art solutions are limited by the temporal correlation assumption. Recent studies are aware of the potential of saliency to further QoE improvement, but several fundamental challenges about saliency judgment, saliency acquirement, and quality adaptation are still not fully addressed. To solve these challenges, we present SalientVR, a saliency-driven mobile 360° video streaming system integrated with gaze information. We design (i) a precise gaze-driven saliency judging criterion for mobile VR viewers, (ii) two pragmatic gaze-driven, tile-level saliency acquiring methods based on cross-user similarity and a specific content-aware deep neural network respectively, and (iii) a lightweight saliency-aware quality adaptation algorithm with a motion-assisted online correction, which is robust to wireless bandwidth vagaries and saliency bias. Moreover, we contribute a gaze-annotated dataset and a gaze-driven quality assessment metric for 360° videos. By extensive prototype evaluations (based on dataset tests and user studies), compared to alternatives, SalientVR significantly enhances the video quality and reduces the rebuffering ratio over 4G/LTE network emulations and in the wild, which achieves a 43.68% QoE improvement.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517018"}, {"primary_key": "1739116", "vector": [], "sparse_vector": [], "title": "Assessing certificate validation user interfaces of WPA supplicants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Guangdong Bai", "Mingchuang Qin", "<PERSON><PERSON>", "<PERSON>"], "summary": "WPA (Wi-Fi Protected Access) Enterprise is the de facto standard for safeguarding enterprise-level wireless networks. It relies on Transport Layer Security (TLS) to establish a secure tunnel during its authentication process, and thus the notoriously error-prone certificate validation may haunt it. Incorrect validation may lead to the SSL/TLS man-in-the-middle attack, or the evil twin attack in the context of wireless networking, where the supplicant connects and unwittingly sends authentication credentials to a fake access point.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517026"}, {"primary_key": "1739119", "vector": [], "sparse_vector": [], "title": "Transforming eyeglass rim into touch panel using piezoelectric sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The traditional interaction method for smart eyewear is by touching a control panel located at the temple front of the eyeglass. This method can be unnatural since the control panel and the display are not within the same plane. In this paper, we propose a new and natural interaction technology for smart eyewear that allows users to interact with the rim of the eyeglass without adding additional hardware to the rim. This design is based on an observation that a finger touch would slightly alter the channel frequency response (CFR) of the eyeglass. We use one pair of piezoelectric (PZT) transducers to measure the CFR, and we recognize the tiny CFR changes by analyzing the complex representation of the CFR. The system detects five touch locations using a deep learning classifier. We recruit ten subjects to evaluate the system and the result shows that the system can recognize the five touch locations with an F1 score of 0.91.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558255"}, {"primary_key": "1739121", "vector": [], "sparse_vector": [], "title": "HiToF: a ToF camera system for capturing high-resolution textures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Li Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a demonstration of an enhanced Time-of-Flight (ToF) depth system named HiToF, which can expose high-resolution textures from captured depth maps. By design, a ToF camera can easily capture the depth maps of a scene while largely omitting the corresponding texture information, which is often critical for the performance of many depth applications. HiToF is developed to address this issue by generating enhanced depth maps with high-resolution textures. The key idea is to manipulate the phase components used in the measurement of time-of-flight for the received IR light. In this demo, we showcase our implementation using off-the-shelf ToF cameras and engage audience with an interactive experience in various scenarios, which illustrates the system's effectiveness in improving the performance of ToF cameras in depth applications.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558747"}, {"primary_key": "1739124", "vector": [], "sparse_vector": [], "title": "Mask does not matter: anti-spoofing face authentication using mmWave without on-site registration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan<PERSON> Zheng", "Jinsong Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Face authentication (FA) schemes are universally adopted. However, current FA systems are mainly camera-based and hence susceptible to face occlusion (e.g., facial masks) and vulnerable to spoofing attacks (e.g., 3D-printed masks). This paper exploits the penetrability, material sensitivity, and fine-grained sensing capability of millimeter wave (mmWave) to build an anti-spoofing FA system, named mmFace. It scans the human face by moving a commodity off-the-shelf (COTS) mmWave radar along a specific trajectory. The mmWave signals bounced off the human face carry the facial biometric features and structure features, which allows mmFace to achieve reliable liveness detection and FA. Due to the penetrability of mmWave, mmFace can still work well even if users wear masks. We explore a distance-resistant facial structure feature to suppress the impact of unstable face-to-device distance. To avoid inconvenient on-site registration, we also propose a novel virtual registration approach based on the core idea of cross-modal transformation from photos to mmWave signals. We implement mmFace with various antenna configurations and prototype two typical modes of mmFace. Extensive experiments show that mmFace can realize accurate FA as well as reliable liveness detection.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560515"}, {"primary_key": "1739125", "vector": [], "sparse_vector": [], "title": "Mandheling: mixed-precision on-device DNN training with DSP offloading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Xi<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes Mandheling, the first system that enables highly resource-efficient on-device training by orchestrating mixed-precision training with on-chip Digital Signal Processor (DSP) offloading. Mandheling fully explores the advantages of DSP in integer-based numerical calculations using four novel techniques: (1) a CPU-DSP co-scheduling scheme to situationally mitigate the overhead from DSP-unfriendly operators; (2) a self-adaptive rescaling algorithm to reduce the overhead of dynamic rescaling in backward propagation; (3) a batch-splitting algorithm to improve DSP cache efficiency; (4) a DSP compute subgraph-reusing mechanism to eliminate the preparation overhead on DSP. We have fully implemented Mandheling and demonstrated its effectiveness through extensive experiments. The results show that, compared to the state-of-the-art DNN engines from TFLite and MNN, Mandheling reduces per-batch training time by 5.5X and energy consumption by 8.9X on average. In end-to-end training tasks, Mandheling reduces convergence time by up to 10.7X and energy consumption by 13.1X, with only 1.9%--2.7% accuracy loss compared to the FP32 precision setting.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560545"}, {"primary_key": "1739126", "vector": [], "sparse_vector": [], "title": "Tutti: coupling 5G RAN and mobile edge computing for latency-critical video analytics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiangyu Li", "Jialiang Pei", "Huadong Ma"], "summary": "Mobile edge computing (MEC), as a key ingredient of the 5G ecosystem, is envisioned to support demanding applications with stringent latency requirements. The basic idea is to deploy servers close to end-users, e.g., on the network edge-side instead of the remote cloud. While conceptually reasonable, we find that the operational 5G is not coordinated with MEC and thus suffers from intolerable long response latency. In this work, we propose <PERSON><PERSON>, which couples 5G RAN and MEC at the user space to assure the performance of latency-critical video analytics. To enable such capacity, <PERSON><PERSON> precisely customizes the application service demand by fusing instantaneous wireless dynamics from the 5G RAN and application-layer content changes from edge servers. <PERSON><PERSON> then enforces a deadline-sensitive resource provision for meeting the application service demand by real-time interaction between 5G RAN and edge servers in a lightweight and standard-compatible way. We prototype and evaluate <PERSON><PERSON> on a software-defined platform, which shows that <PERSON><PERSON> reduces the response latency by an average of 61.69% compared with the existing 5G MEC system, as well as negligible interaction costs.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560538"}, {"primary_key": "1739127", "vector": [], "sparse_vector": [], "title": "Enabling L3: low cost, low complexity and low power radio frequency sensing using tunnel diodes.", "authors": ["Wenqing Yan", "<PERSON><PERSON><PERSON>"], "summary": "The past decade has seen a great interest in developing radio frequency sensing technology and its applications. At a basic level, these systems operate by tracking changes in the wireless signal reflected from a physical object. These reflections contain a wealth of information about the object, such as its motion and material. However, existing radio frequency sensing solutions are constrained by the complexity of the deployment and their high power consumption. This is because these systems extract weak reflections in presence of a strong incident signal. This paper introduces a new radio frequency sensing modality that allows tracking of the incident signal and not reflections from the object. This allows us to simplify the receiver and algorithm design significantly. We design the system using tunnel diode oscillators to generate a high-frequency carrier signal at only tens of microwatts of power consumption. The critical contribution that we make is to show that the frequency of the tunnel diode oscillator is sensitive to the physical environment. As an example, we demonstrate that performing simple hand gestures near the tunnel diode oscillator causes notable changes to its frequency. Thus, the receiver tracks the frequency of the carrier signal generated by the tunnel diode oscillator, and not reflections from physical objects. It enables inferring of sensing information using a commodity receiver costing only a few USD. Our system enables radio frequency sensing at low cost, complexity and power.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558281"}, {"primary_key": "1739128", "vector": [], "sparse_vector": [], "title": "A vision-based indoor positioning systems utilizing computer aided design drawing.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>o", "Gaoyang Shan", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent years, with the increase in users' demand for location services, the research of Indoor Positioning Systems (IPS) has attracted much attention. Many researchers proposed schemes to estimate the user's location based on the Received Signal Strength Indicator (RSSI) values of wireless technologies. However, the RSSI value is affected by signal interference seriously. This causes the accuracy of the positioning to be greatly reduced. To solve this problem, we use Computer Vision (CV) to replace traditional solutions in this paper. CV is known for its high performance and low complexity. The proposed scheme is capable of inferring the current location of users in the possible candidates from the interior structural features of buildings captured by cameras and Computer-Aided Design (CAD) drawing.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558270"}, {"primary_key": "1739129", "vector": [], "sparse_vector": [], "title": "Magnetoelectric backscatter communication for millimeter-sized wireless biomedical implants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the design, implementation, and experimental evaluation of a wireless biomedical implant platform exploiting the magnetoelectric effect for wireless power and bi-directional communication. As an emerging wireless power transfer method, magnetoelectric is promising for mm-scaled bio-implants because of its superior misalignment sensitivity, high efficiency, and low tissue absorption compared to other modalities [46, 59, 60]. Utilizing the same physical mechanism for power and communication is critical for implant miniaturization, but low-power magnetoelectric uplink communication has not been achieved yet. For the first time, we design and demonstrate near-zero power magnetoelectric backscatter from the mm-sized implants by exploiting the converse magnetostriction effects.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560541"}, {"primary_key": "1739130", "vector": [], "sparse_vector": [], "title": "ST-ICM: spatial-temporal inference calibration model for low cost fine-grained mobile sensing.", "authors": ["<PERSON>z<PERSON> Yu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In order to reduce the measurement error of low cost sensor in the real-time mobile sensing network, rendezvous calibration mechanism is widely used. To tackle the sparsity of reference data and the lack of calibration opportunities, we propose ST-ICM: a Spatial-Temporal Inference Calibration Model based on Gaussian Process Regression, assisting the calibration task by creating more calibration grids in both spatial and temporal dimensions. By using the GPR, the inferred grids generated by ST-ICM are associated with various confidence levels. Based on this property, we propose to make use of a hyperparameter, i.e., variance threshold, to balance the tradeoff between the quantity and quality of the inferred grids. Specifically, only the grids with variances below the threshold will be employed. We conducted experiments using a real-world dataset collected in Nanjing, China, to evaluate the performance of the proposed ST-ICM. The experimenal results show that our model achieves 24% improvement on error calibration compared to the baseline.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558280"}, {"primary_key": "1739131", "vector": [], "sparse_vector": [], "title": "InFi: end-to-end learnable input filter for resource-efficient mobile-centric inference.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiangyang Li"], "summary": "Mobile-centric AI applications put forward high requirements for resource-efficiency of model inference. Input filtering is a promising approach to eliminate the redundancy in the input so as to reduce the cost of inference. Previous efforts have tailored effective solutions for many applications, but left two essential questions unanswered: (1) theoretical filterability of an inference workload to guide the application of input filtering techniques, thereby avoiding the trial-and-error cost for resource-constrained mobile applications; (2) robust discriminability of feature embedding to allow input filtering to be widely effective for diverse inference tasks and input content. To answer these questions, we first provide a generic formalization of the input filtering problem and theoretically compare the hypothesis complexity of inference models and their input filters to understand the optimization potential of applying input filtering. Then we propose the first end-to-end learnable input filtering framework that covers most state-of-the-art methods and surpasses them in feature embedding with robust discriminability. Based on our framework, we design and implement an input filtering system InFi supporting six input modalities. InFi is the first to support text and sensor signal inputs and model partitioning deployments widely adopted by under-resourced mobile systems. Comprehensive evaluations confirm our theoretical results and show that InFi outperforms strong baselines in applicability, accuracy, and efficiency, owing to its generality and end-to-end learnability. InFi can achieve 8.5X throughput and save 95% bandwidth, while keeping over 90% accuracy, for a video analytics app on mobile platforms.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517016"}, {"primary_key": "1739134", "vector": [], "sparse_vector": [], "title": "Mobi2Sense: empowering wireless sensing with mobility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Besides the conventional communication function, wireless signals are actively exploited for sensing purposes recently. However, a missing component of existing wireless sensing is sensing under device motions. This is challenging because device motions can easily overwhelm target motions such as chest displacement used for respiration sensing. This paper takes a first step in the direction of involving device mobility into the ecosystem of wireless sensing. Owning to the miniaturization and low cost of ultra-wideband (UWB) chip in recent years, we propose to integrate the accuracy of UWB sensing with mobility to support truly ubiquitous wireless sensing. We propose Mobi2Sense, a system design to support sensing under device motions. We propose novel signal processing schemes to remove the effect of device motions on sensing and prototype Mobi2Sense using commodity UWB hardware. Real-world applications demonstrate that even in the presence of device motions, fine-grained Mobi2Sense is able to capture subtle target motions to \"hear\" music, \"see\" human respiration, and \"recognize\" multi-target gestures at a high accuracy.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560518"}, {"primary_key": "1739135", "vector": [], "sparse_vector": [], "title": "U-star: an underwater navigation system based on passive 3D optical identification tags.", "authors": ["<PERSON>", "Han<PERSON> Guo", "<PERSON>", "<PERSON>"], "summary": "Underwater optical wireless communication techniques are promising due to a broad bandwidth with a long communication range compared with existing expensive acoustic and RF-based underwater communication techniques. For underwater navigation assistance during dive and rescue, it is more practical to adopt passive optical tags for objects/human identification and location-based services. However, existing optical tags (bar/QR codes) employ one/two dimensional designs, which lack significant element/symbol distance for robust decoding and full-directional localization capabilities for underwater navigation tasks. This paper investigates opportunities to increase the element distance in passive low-order optical tags by exploiting 3D spatial diversity. Specifically, we design U-Star, a system that consists of Underwater Optical Identification (UOID) tags and commercial camera-based tag readers for underwater navigation. Our UOID tags embed rich location and guidance information. Additionally, because our UOID tags employ a three-dimensional design, they can also determine the relative location of a user in real-time based on the perspective principles. We design AI based mobile algorithms for underwater denoising, relative positioning, and robust data parsing for tag readers. Finally, we evaluate U-Star on real UOID tag prototypes under different underwater scenarios. Results show that our 3-order UOID tag can embed 21 bits with a BER of 0.003 at 1m and less than 0.05 at up to 3m, which is sufficient for underwater navigation guidance with backup database.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517019"}, {"primary_key": "1739138", "vector": [], "sparse_vector": [], "title": "MobiDepth: real-time depth estimation using on-device dual cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time depth estimation is critical for the increasingly popular augmented reality and virtual reality applications on mobile devices. Yet existing solutions are insufficient as they require expensive depth sensors or motion of the device, or have a high latency. We propose MobiDepth, a real-time depth estimation system using the widely-available on-device dual cameras. While binocular depth estimation is a mature technique, it is challenging to realize the technique on commodity mobile devices due to the different focal lengths and unsynchronized frame flows of the on-device dual cameras and the heavy stereo-matching algorithm.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3560517"}, {"primary_key": "1739139", "vector": [], "sparse_vector": [], "title": "FedHD: federated learning with hyperdimensional computing.", "authors": ["Quanling <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated Learning (FL) is a widely adopted distributed learning paradigm for to its privacy-preserving and collaborative nature. In FL, each client trains and sends a local model to the central cloud for aggregation. However, FL systems using neural network (NN) models are expensive to deploy on constrained edge devices regarding computation and communication. In this demo, we present FedHD, a FL system using Hyperdimensional Computing (HDC). In contrast to NN, HDC is a brain-inspired and lightweight computing paradigm using high-dimensional vectors and associative memory. Our measurements indicate that FedHD is 3.2×, 3.2×, 5× better on performance, energy and communication efficiency respectively compared to NN-based FL systems whilst maintaining similar accuracy to the state of the art. Our code is available on GitHub1.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558757"}, {"primary_key": "1739140", "vector": [], "sparse_vector": [], "title": "IMAP: individual huMAn mobility patterns visualizing platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding human mobility is essential for the development of smart cities and social behavior research. Human mobility models may be used in numerous applications, including pandemic control, urban planning, and traffic management. The existing models' accuracy in predicting users' mobility patterns is less than 25%. The low accuracy may be justified by the flexible nature of human movement. Indeed, humans are not rigid in their daily movement. In addition, the rigid mobility models may result in missing the hidden regularities in users' records. Thus, we propose a novel perspective to study and analyze human mobility patterns and capture their flexibility. Typically, the mobility patterns are represented by a sequence of locations. We propose to define the mobility patterns by abstracting these locations into a set of places. Labeling these locations will allow us to detect close-to-reality hidden patterns. We present IMAP, an Individual huMAn mobility Patterns visualizing platform. Our platform enables users to visualize a graph of the places they visited based on their history records. In addition, our platform displays the most frequent mobility patterns computed using a modified PrefixSpan approach.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558759"}, {"primary_key": "1739142", "vector": [], "sparse_vector": [], "title": "Experience: adopting indoor outdoor detection in on-demand food delivery business.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents our experience in adopting recent research results of mobile phone based indoor/outdoor detection (IODetector) to support the real world business of on-demand food delivery. The real world deployment of the adopted IODetector involves three phases spanning 20 months, during which the deployment scales from a feasibility study across a few areas of interest to a city-wide trial in Shanghai, and eventually to nationwide deployment over 367 cities in China. Iterative development has been performed throughout different deployment phases to excel the IODetector. Large scale evaluation and comparative A/B testing suggest key value of adopting indoor/outdoor detection in the real world business. We also present the lessons learned from the deployment experience including real world know-hows, practical limits and constraints, as well as discussions on design alternatives. We believe this paper provides insights to guide future efforts in translating research results to industry adoptions.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3517023"}, {"primary_key": "1739143", "vector": [], "sparse_vector": [], "title": "NestFL: efficient federated learning through progressive model pruning in heterogeneous edge computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present NestFL, a learning-efficient FL framework for edge computing, which can jointly improve the training efficiency and achieve personalization. Specifically, NestFL takes the runtime resources of the edge devices into consideration and assigns each device a sparse-structured subnetwork by progressively performing the structured pruning. During training, only the updates of these subnetworks are transmitted to the central server. Additionally, these generated subnetworks adopt a structure- and parameter-sharing mechanism, making themselves nested inside a multi-capacity global model. In doing so, the overall communication and computation costs can be significantly reduced, and each device can learn a personalized model without introducing extra parameters. Furthermore, a weighted aggregation mechanism is designed to improve the training performance and maximally preserve personalization.", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243.3558248"}, {"primary_key": "1782796", "vector": [], "sparse_vector": [], "title": "ACM MobiCom &apos;22: The 28th Annual International Conference on Mobile Computing and Networking, Sydney, NSW, Australia, October 17 - 21, 2022", "authors": [], "summary": "With the continuous growth of the Internet of Things (IoT), the trend of increasing numbers of IoT devices will continue. To increase the network's capability to support a large number of active devices accessing a network concurrently, this work ...", "published": "2022-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3495243"}]