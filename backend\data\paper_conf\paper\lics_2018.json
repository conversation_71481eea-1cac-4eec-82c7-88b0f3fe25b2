[{"primary_key": "3477483", "vector": [], "sparse_vector": [], "title": "Model-Theoretic Characterization of Boolean and Arithmetic Circuit Classes of Small Depth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we give a characterization of both Boolean and arithmetic circuit classes of logarithmic depth in the vein of descriptive complexity theory, i.e., the Boolean classes NC1, SAC1 and AC1 as well as their arithmetic counterparts #NC1, #SAC1 and #AC1. We build on <PERSON><PERSON><PERSON>'s characterization of constant-depth polynomial-size circuits by formulae of first-order logic, i.e., AC0 = FO, and augment the logical language with an operator for defining relations in an inductive way. Considering slight variations of the new operator, we obtain uniform characterizations of the three just mentioned Boolean classes. The arithmetic classes can then be characterized by functions counting winning strategies in semantic games for formulae characterizing languages in the corresponding Boolean class.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209179"}, {"primary_key": "3477484", "vector": [], "sparse_vector": [], "title": "Wreath Products of Distributive Forest Algebras.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is an open problem whether definability in Propositional Dynamic Logic (PDL) on forests is decidable. Based on an algebraic characterization by <PERSON><PERSON>, et. al.,(2012) in terms of forest algebras, <PERSON><PERSON><PERSON><PERSON> (2013) described an approach to PDL based on a k-fold iterated distributive law. A proof that all languages satisfying such a k-fold iterated distributive law are in PDL would settle decidability of PDL. We solve this problem in the case k=2: All languages recognized by forest algebras satisfying a 2-fold iterated distributive law are in PDL. Furthermore, we show that this class is decidable. This provides a novel nontrivial decidable subclass of PDL, and demonstrates the viability of the proposed approach to deciding PDL in general.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209158"}, {"primary_key": "3477485", "vector": [], "sparse_vector": [], "title": "Distribution-based objectives for Markov Decision Processes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider distribution-based objectives for Markov Decision Processes (MDP). This class of objectives gives rise to an interesting trade-off between full and partial information. As in full observation, the strategy in the MDP can depend on the state of the system, but similar to partial information, the strategy needs to account for all the states at the same time.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209185"}, {"primary_key": "3477486", "vector": [], "sparse_vector": [], "title": "A Simple and Optimal Complementation Algorithm for Büchi Automata.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Complementation of Büchi automata is complex as Büchi automata in general are nondeterministic. A worst-case state-space growth of O((0.76n)n) cannot be avoided. Experiments suggest that complementation algorithms perform better on average when they are structurally simple. We present a simple algorithm for complementing Büchi automata, operating directly on subsets of states, structured into state-set tuples (similar to slices), and producing a deterministic automaton. Then a complementation procedure is applied that resembles the straightforward complementation algorithm for deterministic Büchi automata, the latter algorithm actually being a special case of our construction. Finally, we prove our construction to be optimal, i.e. having an upper bound in O((0.76n)n), and furthermore calculate the 0.76 factor in a novel exact way.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209138"}, {"primary_key": "3477487", "vector": [], "sparse_vector": [], "title": "Syntax and Semantics of Quantitative Type Theory.", "authors": ["<PERSON>"], "summary": "We present Quantitative Type Theory, a Type Theory that records usage information for each variable in a judgement, based on a previous system by <PERSON>. The usage information is used to give a realizability semantics using a variant of Linear Combinatory Algebras, refining the usual realizability semantics of Type Theory by accurately tracking resource behaviour. We define the semantics in terms of Quantitative Categories with Families, a novel extension of Categories with Families for modelling resource sensitive type theories.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209189"}, {"primary_key": "3477488", "vector": [], "sparse_vector": [], "title": "Definable Ellipsoid Method, Sums-of-Squares Proofs, and the Isomorphism Problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The ellipsoid method is an algorithm that solves the (weak) feasibility and linear optimization problems for convex sets by making oracle calls to their (weak) separation problem. We observe that the previously known method for showing that this reduction can be done in fixed-point logic with counting (FPC) for linear and semidefinite programs applies to any family of explicitly bounded convex sets. We use this observation to show that the exact feasibility problem for semidefinite programs is expressible in the infinitary version of FPC. As a corollary we get that, for the graph isomorphism problem, the Lasserre/Sums-of-Squares semidefinite programming hierarchy of relaxations collapses to the Sherali-Adams linear programming hierarchy, up to a small loss in the degree.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209186"}, {"primary_key": "3477489", "vector": [], "sparse_vector": [], "title": "Impredicative Encodings of (Higher) Inductive Types.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Postulating an impredicative universe in dependent type theory allows System F style encodings of finitary inductive types, but these fail to satisfy the relevant η-equalities and consequently do not admit dependent eliminators. To recover η and dependent elimination, we present a method to construct refinements of these impredicative encodings, using ideas from homotopy type theory. We then extend our method to construct impredicative encodings of some higher inductive types, such as 1-truncation and the unit circle S1.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209130"}, {"primary_key": "3477490", "vector": [], "sparse_vector": [], "title": "Boolean-Valued Semantics for the Stochastic λ-Calculus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ordinary untyped -calculus has a -theoretic model proposed in two related forms by <PERSON> and <PERSON><PERSON> in the 1970s. Recently <PERSON> showed how to introduce probability by extending these models with random variables. However, to reason about correctness and to add further features, it is useful to reinterpret the construction in a higher-order Boolean-valued model involving a measure algebra. We develop the semantics of an extended stochastic -calculus suitable for modeling a simple higher-order probabilistic programming language. We exhibit a number of key equations satisfied by the terms of our language. The terms are interpreted using a continuation-style semantics with an additional argument, an infinite sequence of coin tosses, which serves as a source of randomness. We also introduce a fixpoint operator as a new syntactic construct, as Β-reduction turns out not to be sound for unrestricted terms. Finally, we develop a new notion of equality between terms interpreted in a measure algebra, allowing one to reason about terms that may not be equal almost everywhere. This provides a new framework and reasoning principles for probabilistic programs and their higher-order properties.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209175"}, {"primary_key": "3477491", "vector": [], "sparse_vector": [], "title": "An Algebraic Theory of Markov Processes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Markov processes are a fundamental model of probabilistic transition systems and are the underlying semantics of probabilistic programs. We give an algebraic axiomatisation of Markov processes using the framework of quantitative equational logic introduced in (<PERSON><PERSON><PERSON> et al LICS'16). We present the theory in a structured way using work of (<PERSON><PERSON> et al. TCS'06) on combining monads. We take the interpolative barycentric algebras of (<PERSON><PERSON><PERSON> et al LICS'16) which captures the Kantorovich metric and combine it with a theory of contractive operators to give the required axiomatisation of Markov processes both for discrete and continuous state spaces. This work apart from its intrinsic interest shows how one can extend the general notion of combining effects to the quantitative setting.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209177"}, {"primary_key": "3477492", "vector": [], "sparse_vector": [], "title": "Stochastic Shortest Paths and Weight-Bounded Properties in Markov Decision Processes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ocan Sankur"], "summary": "The paper deals with finite-state Markov decision processes (MDPs) with integer weights assigned to each state-action pair. New algorithms are presented to classify end components according to their limiting behavior with respect to the accumulated weights. These algorithms are used to provide solutions for two types of fundamental problems for integer-weighted MDPs. First, a polynomial-time algorithm for the classical stochastic shortest path problem is presented, generalizing known results for special classes of weighted MDPs. Second, qualitative probability constraints for weight-bounded (repeated) reachability conditions are addressed. Among others, it is shown that the problem to decide whether a disjunction of weight-bounded reachability conditions holds almost surely under some scheduler belongs to NP ∩ coNP, is solvable in pseudo-polynomial time and is at least as hard as solving two-player mean-payoff games, while the corresponding problem for universal quantification over schedulers is solvable in polynomial time.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209184"}, {"primary_key": "3477493", "vector": [], "sparse_vector": [], "title": "Computability Beyond Church-Turing via Choice Sequences.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Church-Turing computability was extended by <PERSON><PERSON><PERSON> who considered non-lawlike computability in the form of free choice sequences. Those are essentially unbounded sequences whose elements are chosen freely, i.e. not subject to any law. In this work we develop a new type theory BITT, which is an extension of the type theory of the Nuprl proof assistant, that embeds the notion of choice sequences. Supporting the evolving, non-deterministic nature of these objects required major modifications to the underlying type theory. Even though the construction of a choice sequence is non-deterministic, once certain choices were made, they must remain consistent. To ensure this, BITT uses the underlying library as state and store choices as they are created. Another salient feature of BITT is that it uses a Beth-like semantics to account for the dynamic nature of choice sequences. We formally define BITT and use it to interpret and validate essential axioms governing choice sequences. These results provide a foundation for a fully intuitionistic version of Nuprl.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209200"}, {"primary_key": "3477494", "vector": [], "sparse_vector": [], "title": "Black Ninjas in the Dark: Formal Analysis of Population Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this interactive paper, which you should preferably read connected to the Internet, the Black Ninjas introduce you to population protocols, a fundamental model of distributed computation, and to recent work by the authors and their colleagues on their automatic verification.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209110"}, {"primary_key": "3477495", "vector": [], "sparse_vector": [], "title": "Extensional and Intensional Semantic Universes: A Denotational Model of Dependent Types.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a dependent type theory, and a denotational model for it, that incorporates both intensional and extensional semantic universes. In the former, terms and types are interpreted as strategies on certain graph games, which are concrete data structures of a generalized form, and in the latter as stable functions on event domains.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209206"}, {"primary_key": "3477496", "vector": [], "sparse_vector": [], "title": "A universal-algebraic proof of the complexity dichotomy for Monotone Monadic SNP.", "authors": ["<PERSON>", "Florent <PERSON><PERSON>", "<PERSON>"], "summary": "The logic MMSNP is a restricted fragment of existential second-order logic which allows to express many interesting queries in graph theory and finite model theory. The logic was introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> who showed that every MMSNP sentence is computationally equivalent to a finite-domain constraint satisfaction problem (CSP); the involved probabilistic reductions were derandomized by <PERSON><PERSON> using explicit constructions of expander structures. We present a new proof of the reduction to finite-domain CSPs that does not rely on the results of <PERSON><PERSON>. This new proof allows us to obtain a stronger statement and to verify the Bo<PERSON>sky-<PERSON> dichotomy conjecture for CSPs in MMSNP. Our approach uses the fact that every MMSNP sentence describes a finite union of CSPs for countably infinite ω-categorical structures; moreover, by a recent result of <PERSON><PERSON><PERSON><PERSON> and Nešetřil, these structures can be expanded to homogeneous structures with finite relational signature and the Ramsey property. This allows us to use the universal-algebraic approach to study the computational complexity of MMSNP.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209156"}, {"primary_key": "3477497", "vector": [], "sparse_vector": [], "title": "A Hybrid, Dynamic Logic for Hybrid-Dynamic Information Flow.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Information-flow security is important to the safety and privacy of cyber-physical systems (CPSs) across many domains: information leakage can both violate user privacy and reveal vulnerabilities to physical attacks. CPSs face the challenge that information can flow both in discrete cyber channels and in continuous real-valued physical channels ranging from time to motion to electrical currents. We call these hybrid-dynamic information flows (HDIFs) and introduce dHL, the first logic for verifying HDIFs in hybrid-dynamical models of CPSs. Our logic extends differential dynamic logic (dL) for hybrid-dynamical systems with hybrid-logical features for explicit program state representation, supporting relational reasoning used for information flow arguments. By verifying HDIFs, we ensure security even under a strong attacker model wherein an attacker can observe time and physical values continuously. We present a Hilbert-style proof calculus for dHL, prove it sound, and compare the expressive power of dHL with dL. We develop a hybrid system model based on the smart electrical grid FREEDM, with which we showcase dHL. We prove that the naive model has a previously unknown information flow vulnerability, which we verify is resolved in a revised model. This is the first information flow proof both for HDIFs and for a hybrid-dynamical model in general.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209151"}, {"primary_key": "3477498", "vector": [], "sparse_vector": [], "title": "Regular and First-Order List Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We define two classes of functions, called regular (respectively, first-order) list functions, which manipulate objects such as lists, lists of lists, pairs of lists, lists of pairs of lists, etc. The definition is in the style of regular expressions: the functions are constructed by starting with some basic functions (e.g. projections from pairs, or head and tail operations on lists) and putting them together using four combinators (most importantly, composition of functions). Our main results are that first-order list functions are exactly the same as first-order transductions, under a suitable encoding of the inputs; and the regular list functions are exactly the same as MSO-transductions.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209163"}, {"primary_key": "3477499", "vector": [], "sparse_vector": [], "title": "Definable decompositions for graphs of bounded linear cliquewidth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that for every positive integer k, there exists an MSO1-transduction that given a graph of linear cliquewidth at most k outputs, nondeterministically, some clique decomposition of the graph of width bounded by a function of k. A direct corollary of this result is the equivalence of the notions of CMSO1-definability and recognizability on graphs of bounded linear cliquewidth.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209135"}, {"primary_key": "3477500", "vector": [], "sparse_vector": [], "title": "On computability and tractability for infinite sets.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a definition for computable functions on hereditarily definable sets. Such sets are possibly infinite data structures that can be defined using a fixed underlying logical structure, such as (N, =). We show that, under suitable assumptions on the underlying structure, a programming language called definable while programs captures exactly the computable functions. Next, we introduce a complexity class called fixed-dimension polynomial time, which intuitively speaking describes polynomial computation on hereditarily definable sets. We show that this complexity class contains all functions computed by definable while programs with suitably defined resource bounds. Proving the converse inclusion would prove that Choiceless Polynomial Time with Counting captures polynomial time on finite graphs.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209190"}, {"primary_key": "3477501", "vector": [], "sparse_vector": [], "title": "Automaton-Based Criteria for Membership in CTL.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computation Tree Logic (CTL) is widely used in formal verification, however, unlike linear temporal logic (LTL), its connection to automata over words and trees is not yet fully understood. Moreover, the long sought connection between LTL and CTL is still missing; It is not known whether their common fragment is decidable, and there are very limited necessary conditions and sufficient conditions for checking whether an LTL formula is definable in CTL.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209143"}, {"primary_key": "3477502", "vector": [], "sparse_vector": [], "title": "Sound up-to techniques and Complete abstract domains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Abstract interpretation is a method to automatically find invariants of programs or pieces of code whose semantics is given via least fixed-points. Up-to techniques have been introduced as enhancements of coinduction, an abstract principle to prove properties expressed via greatest fixed-points.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209169"}, {"primary_key": "3477503", "vector": [], "sparse_vector": [], "title": "Rewriting with <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Symmetric monoidal categories have become ubiquitous as a formal environment for the analysis of compound systems in a compositional, resource-sensitive manner using the graphical syntax of string diagrams. Recently, reasoning with string diagrams has been implemented concretely via double-pushout (DPO) hypergraph rewriting. The hypergraph representation has the twin advantages of being convenient for mechanisation and of completely absorbing the structural laws of symmetric monoidal categories, leaving just the domain-specific equations explicit in the rewriting system.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209137"}, {"primary_key": "3477504", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Asymptotic Bounds on Termination Time in VASS.", "authors": ["Tomás Brázdil", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vector Addition Systems with States (VASS) provide a well-known and fundamental model for the analysis of concurrent processes, parameterized systems, and are also used as abstract models of programs in resource bound analysis. In this paper we study the problem of obtaining asymptotic bounds on the termination time of a given VASS. In particular, we focus on the practically important case of obtaining polynomial bounds on termination time. Our main contributions are as follows: First, we present a polynomial-time algorithm for deciding whether a given VASS has a linear asymptotic complexity. We also show that if the complexity of a VASS is not linear, it is at least quadratic. Second, we classify VASS according to quantitative properties of their cycles. We show that certain singularities in these properties are the key reason for non-polynomial asymptotic complexity of VASS. In absence of singularities, we show that the asymptotic complexity is always polynomial and of the form Θ(nk), for some integer k ≤ d, where d is the dimension of the VASS. We present a polynomial-time algorithm computing the optimal k. For general VASS, the same algorithm, which is based on a complete technique for the construction of ranking functions in VASS, produces a valid lower bound, i.e., a k such that the termination complexity is Ω(nk). Our results are based on new insights into the geometry of VASS dynamics, which hold the potential for further applicability to VASS analysis.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209191"}, {"primary_key": "3477505", "vector": [], "sparse_vector": [], "title": "Concurrency and Probability: Removing Confusion, Compositionally.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Assigning a satisfactory truly concurrent semantics to Petri nets with confusion and distributed decisions is a long standing problem, especially if one wants to resolve decisions by drawing from some probability distribution. Here we propose a general solution based on a recursive, static decomposition of (occurrence) nets in loci of decision, called structural branching cells (s-cells). Each s-cell exposes a set of alternatives, called transactions. Our solution transforms a given Petri net into another net whose transitions are the transactions of the s-cells and whose places are those of the original net, with some auxiliary structure for bookkeeping. The resulting net is confusion-free, and thus conflicting alternatives can be equipped with probabilistic choices, while nonintersecting alternatives are purely concurrent and their probability distributions are independent. The validity of the construction is witnessed by a tight correspondence with the recursively stopped configurations of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Some advantages of our approach are that: i) s-cells are defined statically and locally in a compositional way; ii) our resulting nets faithfully account for concurrency.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209202"}, {"primary_key": "3477506", "vector": [], "sparse_vector": [], "title": "Cellular Cohomology in Homotopy Type Theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Favonia)"], "summary": "We present a development of cellular cohomology in homotopy type theory. Cohomology associates to each space a sequence of abelian groups capturing part of its structure, and has the advantage over homotopy groups in that these abelian groups of many common spaces are easier to compute. Cellular cohomology is a special kind of cohomology designed for cell complexes: these are built in stages by attaching spheres of progressively higher dimension, and cellular cohomology defines the groups out of the combinatorial description of how spheres are attached. Our main result is that for finite cell complexes, a wide class of cohomology theories (including the ones defined through Eilenberg-Mac<PERSON>ane spaces) can be calculated via cellular cohomology. This result was formalized in the Agda proof assistant.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209188"}, {"primary_key": "3477507", "vector": [], "sparse_vector": [], "title": "Higher Groups in Homotopy Type Theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a development of the theory of higher groups, including infinity groups and connective spectra, in homotopy type theory. An infinity group is simply the loops in a pointed, connected type, where the group structure comes from the structure inherent in the identity types of Martin-Löf type theory. We investigate ordinary groups from this viewpoint, as well as higher dimensional groups and groups that can be delooped more than once. A major result is the stabilization theorem, which states that if an n-type can be delooped n + 2 times, then it is an infinite loop type. Most of the results have been formalized in the <PERSON><PERSON> proof assistant.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209150"}, {"primary_key": "3477508", "vector": [], "sparse_vector": [], "title": "The concurrent game semantics of Probabilistic PCF.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We define a new games model of Probabilistic PCF (PPCF) by enriching thin concurrent games with symmetry, recently introduced by <PERSON> et al, with probability. This model supports two interpretations of PPCF, one sequential and one parallel. We make the case for this model by exploiting the causal structure of probabilistic concurrent strategies. First, we show that the strategies obtained from PPCF programs have a deadlock-free interaction, and therefore deduce that there is an interpretation-preserving functor from our games to the probabilistic relational model recently proved fully abstract by <PERSON><PERSON><PERSON> et al. It follows that our model is intensionally fully abstract. Finally, we propose a definition of probabilistic innocence and prove a finite definability result, leading to a second (independent) proof of full abstraction.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209187"}, {"primary_key": "3477509", "vector": [], "sparse_vector": [], "title": "Tree-depth, quantifier elimination, and quantifier rank.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For a class K of finite graphs we consider the following three statements. (i) K has bounded tree-depth. (ii) First-order logic FO has an effective generalized quantifier elimination on K. (iii) The parameterized model checking for FO on K is in para-AC0. We prove that (i) ⟹ (ii) and (ii) ⟺ (iii). All three statements are equivalent if K is closed under taking subgraphs, but not in general.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209160"}, {"primary_key": "3477510", "vector": [], "sparse_vector": [], "title": "A parameterized halting problem, the linear time hierarchy, and the MRDP theorem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The complexity of the parameterized halting problem for nondeterministic Turing machines p-Halt is known to be related to the question of whether there are logics capturing various complexity classes [10]. Among others, if p-Halt is in para-AC0, the parameterized version of the circuit complexity class AC0, then AC0, or equivalently, (+, x)-invariant FO, has a logic. Although it is widely believed that p-Halt ∉. para-AC0, we show that the problem is hard to settle by establishing a connection to the question in classical complexity of whether NE ⊈ LINH. Here, LINH denotes the linear time hierarchy.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209155"}, {"primary_key": "3477511", "vector": [], "sparse_vector": [], "title": "Inner Models of Univalence.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a simple inner model construction for dependent type theory, which preserves univalence.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209112"}, {"primary_key": "3477512", "vector": [], "sparse_vector": [], "title": "On Higher Inductive Types in Cubical Type Theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cubical type theory provides a constructive justification to certain aspects of homotopy type theory such as <PERSON><PERSON><PERSON><PERSON><PERSON>'s univalence axiom. This makes many extensionality principles, like function and propositional extensionality, directly provable in the theory. This paper describes a constructive semantics, expressed in a presheaf topos with suitable structure inspired by cubical sets, of some higher inductive types. It also extends cubical type theory by a syntax for the higher inductive types of spheres, torus, suspensions, truncations, and pushouts. All of these types are justified by the semantics and have judgmental computation rules for all constructors, including the higher dimensional ones, and the universes are closed under these type formers.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209197"}, {"primary_key": "3477513", "vector": [], "sparse_vector": [], "title": "Strong Sums in Focused Logic.", "authors": ["<PERSON>"], "summary": "A useful connective that has not previously been made to work in focused logic is the strong sum, a form of dependent sum that is eliminated by projection rather than pattern matching. This makes strong sums powerful, but it also creates a problem adapting them to focusing: The type of the right projection from a strong sum refers to the term being projected from, but due to the structure of focused logic, that term is not available.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209145"}, {"primary_key": "3477514", "vector": [], "sparse_vector": [], "title": "Probabilistic Stable Functions on Discrete Cones are Power Series.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the category Cstabm of measurable cones and measurable stable functions---a denotational model of an higher-order language with continuous probabilities and full recursion [7]. We look at Cstabm as a model for discrete probabilities, by showing the existence of a cartesian closed, full and faithful functor which embeds probabilistic coherence spaces---a fully abstract denotational model of an higher language with full recursion and discrete probabilities [8]---into Cstabm. The proof is based on a generalization of <PERSON>'s theorem from real analysis allowing to see stable functions between discrete cones as generalized power series.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209198"}, {"primary_key": "3477515", "vector": [], "sparse_vector": [], "title": "Unary negation fragment with equivalence relations has the finite model property.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider an extension of the unary negation fragment of first-order logic in which arbitrarily many binary symbols may be required to be interpreted as equivalence relations. We show that this extension has the finite model property. More specifically, we show that every satisfiable formula has a model of at most doubly exponential size. We argue that the satisfiability (= finite satisfiability) problem for this logic is 2-ExpTime-complete. We also transfer our results to a restricted variant of the guarded negation fragment with equivalence relations.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209205"}, {"primary_key": "3477516", "vector": [], "sparse_vector": [], "title": "Logics for Word Transductions with Synthesis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a logic, called ℒT, to express properties of transductions, i.e. binary relations from input to output (finite) words. In ℒT, the input/output dependencies are modelled via an origin function which associates to any position of the output word, the input position from which it originates. ℒT is well-suited to express relations (which are not necessarily functional), and can express all regular functional transductions, i.e. transductions definable for instance by deterministic two-way transducers.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209181"}, {"primary_key": "3477517", "vector": [], "sparse_vector": [], "title": "Work Analysis with Resource-Aware Session Types.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While there exist several successful techniques for supporting programmers in deriving static resource bounds for sequential code, analyzing the resource usage of message-passing concurrent processes poses additional challenges. To meet these challenges, this article presents an analysis for statically deriving worst-case bounds on the total work performed by message-passing processes. To decompose interacting processes into components that can be analyzed in isolation, the analysis is based on novel resource-aware session types, which describe protocols and resource contracts for inter-process communication. A key innovation is that both messages and processes carry potential to share and amortize cost while communicating. To symbolically express resource usage in a setting without static data structures and intrinsic sizes, resource contracts describe bounds that are functions of interactions between processes. Resource-aware session types combine standard binary session types and type-based amortized resource analysis in a linear type system. This type system is formulated for a core session-type calculus of the language SILL and proved sound with respect to a multiset-based operational cost semantics that tracks the total number of messages that are exchanged in a system. The effectiveness of the analysis is demonstrated by analyzing standard examples from amortized analysis and the literature on session types and by a comparative performance analysis of different concurrent programs implementing the same interface.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209146"}, {"primary_key": "3477518", "vector": [], "sparse_vector": [], "title": "Regular Transducer Expressions for Regular Transformations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Functional MSO transductions, deterministic two-way transducers, as well as streaming string transducers are all equivalent models for regular functions. In this paper, we show that every regular function, either on finite words or on infinite words, captured by a deterministic two-way transducer, can be described with a regular transducer expression (RTE). For infinite words, the transducer uses Muller acceptance and ω-regular look-ahead. RTEs are constructed from constant functions using the combinators if-then-else (deterministic choice), Hadamard product, and unambiguous versions of the Cauchy product, the 2-chained Kleene-iteration and the 2-chained omega-iteration. Our proof works for transformations of both finite and infinite words, extending the result on finite words of <PERSON><PERSON> et al. in LICS'14. In order to construct an RTE associated with a deterministic two-way Muller transducer with look-ahead, we introduce the notion of transition monoid for such two-way transducers where the look-ahead is captured by some backward deterministic Büchi automaton. Then, we use an unambiguous version of <PERSON><PERSON><PERSON>'s famous forest factorization theorem in order to derive a \"good\" (ω-)regular expression for the domain of the two-way transducer. \"Good\" expressions are unambiguous and Kleene-plus as well as ω-iterations are only used on subexpressions corresponding to idempotent elements of the transition monoid. The combinator expressions are finally constructed by structural induction on the \"good\" (ω-)regular expression describing the domain of the transducer.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209182"}, {"primary_key": "3477519", "vector": [], "sparse_vector": [], "title": "A pseudo-quasi-polynomial algorithm for mean-payoff parity games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a mean-payoff parity game, one of the two players aims both to achieve a qualitative parity objective and to minimize a quantitative long-term average of payoffs (aka. mean payoff). The game is zero-sum and hence the aim of the other player is to either foil the parity objective or to maximize the mean payoff.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209162"}, {"primary_key": "3477520", "vector": [], "sparse_vector": [], "title": "Causal Computational Complexity of Distributed Processes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the complexity of π-calculus processes with respect to the quantity of transitions caused by an incoming message. First we propose a typing system for integrating <PERSON><PERSON><PERSON> and <PERSON>'s characterisation of polynomially-bound recursive functions into <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s typing system for termination. We then define computational complexity of distributed messages based on <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s causal semantics, which identifies the dependency between interleaved transitions. Next we apply a syntactic flow analysis to typable processes to ensure the computational bound of distributed messages. We prove that our analysis is decidable for a given process; sound in the sense that it guarantees that the total number of messages causally dependent of an input request received from the outside is bounded by a polynomial of the content of this request; and complete which means that each polynomial recursive function can be computed by a typable process.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209122"}, {"primary_key": "3477521", "vector": [], "sparse_vector": [], "title": "Eager Functions as Processes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study <PERSON><PERSON>'s encoding of the call-by-value λ-calculus into the π-calculus. We show that, by tuning the encoding to two subcalculi of the π-calculus (Internal π and Asynchronous Local π), the equivalence on λ-terms induced by the encoding coincides with <PERSON><PERSON>'s eager normal-form bisimilarity, extended to handle η-equality. As behavioural equivalence in the π-calculus we consider contextual equivalence and barbed congruence. We also extend the results to preorders.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209152"}, {"primary_key": "3477522", "vector": [], "sparse_vector": [], "title": "What&apos;s in a game?: A theory of game models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Game semantics is a rich and successful class of denotational models for programming languages. Most game models feature a rather intuitive setup, yet surprisingly difficult proofs of such basic results as associativity of composition of strategies. We seek to unify these models into a basic abstract framework for game semantics, game settings. Our main contribution is the generic construction, for any game setting, of a category of games and strategies. Furthermore, we extend the framework to deal with innocence, and prove that innocent strategies form a subcategory. We finally show that our constructions cover many concrete cases, mainly among the early models [5, 23] and the recent, sheaf-based ones [40].", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209114"}, {"primary_key": "3477523", "vector": [], "sparse_vector": [], "title": "One Theorem to Rule Them All: A Unified Translation of LTL into ω-Automata.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a unified translation of LTL formulas into deterministic Rabin automata, limit-deterministic Büchi automata, and nondeterministic Büchi automata. The translations yield automata of asymptotically optimal size (double or single exponential, respectively). All three translations are derived from one single Master Theorem of purely logical nature. The Master Theorem decomposes the language of a formula into a positive boolean combination of languages that can be translated into ω-automata by elementary means. In particular, <PERSON><PERSON>'s, ranking, and breakpoint constructions used in other translations are not needed.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209161"}, {"primary_key": "3477524", "vector": [], "sparse_vector": [], "title": "A Theory of Register Monitors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The task of a monitor is to watch, at run-time, the execution of a reactive system, and signal the occurrence of a safety violation in the observed sequence of events. While finite-state monitors have been studied extensively, in practice, monitoring software also makes use of unbounded memory. We define a model of automata equipped with integer-valued registers which can execute only a bounded number of instructions between consecutive events, and thus can form the theoretical basis for the study of infinite-state monitors. We classify these register monitors according to the number k of available registers, and the type of register instructions. In stark contrast to the theory of computability for register machines, we prove that for every k ≥ 1, monitors with k + 1 counters (with instruction set 〈+1, =〉) are strictly more expressive than monitors with k counters. We also show that adder monitors (with instruction set 〈1, +, =〉) are strictly more expressive than counter monitors, but are complete for monitoring all computable safety ω-languages for k = 6. Real-time monitors are further required to signal the occurrence of a safety violation as soon as it occurs. The expressiveness hierarchy for counter monitors carries over to real-time monitors. We then show that 2 adders cannot simulate 3 counters in real-time. Finally, we show that real-time adder monitors with inequalities are as expressive as real-time Turing machines.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209194"}, {"primary_key": "3477525", "vector": [], "sparse_vector": [], "title": "Playing with Repetitions in Data Words Using Energy Games.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce two-player games which build words over infinite alphabets, and we study the problem of checking the existence of winning strategies. These games are played by two players, who take turns in choosing valuations for variables ranging over an infinite data domain, thus generating multi-attributed data words. The winner of the game is specified by formulas in the Logic of Repeating Values, which can reason about repetitions of data values in infinite data words. We prove that it is undecidable to check if one of the players has a winning strategy, even in very restrictive settings. However, we prove that if one of the players is restricted to choose valuations ranging over the Boolean domain, the games are effectively equivalent to single-sided games on vector addition systems with states (in which one of the players can change control states but cannot change counter values), known to be decidable and effectively equivalent to energy games.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209154"}, {"primary_key": "3477526", "vector": [], "sparse_vector": [], "title": "The State Complexity of Alternating Automata.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper studies the complexity of languages of finite words using automata theory. To go beyond the class of regular languages, we consider infinite automata and the notion of state complexity defined by <PERSON><PERSON>. We look at alternating automata as introduced by <PERSON>, <PERSON> and <PERSON>: such machines run independent computations on the word and gather their answers through boolean combinations.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209167"}, {"primary_key": "3477527", "vector": [], "sparse_vector": [], "title": "Rational Synthesis Under Imperfect Information.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the rational synthesis problem for turn-based multiplayer non zero-sum games played on finite graphs for omega-regular objectives. Rationality is formalized by the concept of Nash equilibrium (NE). Contrary to previous works, we consider here the more general and more practically relevant case where players are imperfectly informed. In sharp contrast with the perfect information case, NE are not guaranteed to exist in this more general setting. This motivates the study of the NE existence problem. We show that this problem is ExpTime-C for parity objectives in the two-player case (even if both players are imperfectly informed) and undecidable for more than 2 players. We then study the rational synthesis problem and show that the problem is also ExpTime-C for two imperfectly informed players and undecidable for more than 3 players. As the rational synthesis problem considers a system (Player 0) playing against a rational environment (composed of k players), we also consider the natural case where only Player 0 is imperfectly informed about the state of the environment (and the environment is considered as perfectly informed). In this case, we show that the ExpTime-C result holds when k is arbitrary but fixed. We also analyse the complexity when k is part of the input.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209164"}, {"primary_key": "3477528", "vector": [], "sparse_vector": [], "title": "Sequential Relational Decomposition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The concept of decomposition in computer science and engineering is considered a fundamental component of computational thinking and is prevalent in design of algorithms, software construction, hardware design, and more. We propose a simple and natural formalization of sequential decomposition, in which a task is decomposed into two sequential sub-tasks, with the first sub-task to be executed out before the second sub-task is executed. These tasks are specified by means of input/output relations. We define and study decomposition problems, which is to decide whether a given specification can be sequentially decomposed. Our main result is that decomposition itself is a difficult computational problem. More specifically, we study decomposition problems in three settings: where the input task is specified explicitly, by means of Boolean circuits, and by means of automatic relations. We show that in the first setting decomposition is NP-complete, in the second setting it is NEXPTIME-complete, and in the third setting there is evidence to suggest that it is undecidable. Our results indicate that the intuitive idea of decomposition as a system-design approach requires further investigation. In particular, we show that adding human to the loop by asking for a decomposition hint lowers the complexity of decomposition problems considerably.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209203"}, {"primary_key": "3477529", "vector": [], "sparse_vector": [], "title": "ReLoC: A Mechanised Relational Logic for Fine-Grained Concurrency.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present ReLoC: a logic for proving refinements of programs in a language with higher-order state, fine-grained concurrency, polymorphism and recursive types. The core of our logic is a judgement e ≲ e': , which expresses that a program e refines a program e' at type . In contrast to earlier work on refinements for languages with higher-order state and concurrency, ReLoC provides type- and structure-directed rules for manipulating this judgement, whereas previously, such proofs were carried out by unfolding the judgement into its definition in the model. These more abstract proof rules make it simpler to carry out refinement proofs. Moreover, we introduce logically atomic relational specifications: a novel approach for relational specifications for compound expressions that take effect at a single instant in time. We demonstrate how to formalise and prove such relational specifications in ReLoC, allowing for more modular proofs. ReLoC is built on top of the expressive concurrent separation logic Iris, allowing us to leverage features of Iris such as invariants and ghost state. We provide a mechanisation of our logic in Coq, which does not just contain a proof of soundness, but also tactics for interactively carrying out refinements proofs. We have used these tactics to mechanise several examples, which demonstrates the practicality and modularity of our logic.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209174"}, {"primary_key": "3477530", "vector": [], "sparse_vector": [], "title": "Quantitative Behavioural Reasoning for Higher-order Effectful Programs: Applicative Distances.", "authors": ["<PERSON>"], "summary": "This paper studies quantitative refinements of <PERSON><PERSON>'s applicative similarity and bisimilarity in the context of a generalisation of Fuzz, a call-by-value λ-calculus with a linear type system that can express program sensitivity, enriched with algebraic operations à <PERSON> P<PERSON> and Power. To do so a general, abstract framework for studying behavioural relations taking values over quantales is introduced according to <PERSON><PERSON>'s analysis of generalised metric spaces. <PERSON>'s notion of relator (or lax extension) is then extended to quantale-valued relations, adapting and extending results from the field of monoidal topology. Abstract notions of quantale-valued effectful applicative similarity and bisimilarity are then defined and proved to be a compatible generalised metric (in the sense of <PERSON><PERSON>) and pseudometric, respectively, under mild conditions.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209149"}, {"primary_key": "3477531", "vector": [], "sparse_vector": [], "title": "Classical realizability as a classifier for nondeterminism.", "authors": ["<PERSON>"], "summary": "We show how the language of <PERSON><PERSON><PERSON><PERSON>'s classical realizability may be used to specify various forms of nondeterminism and relate them with properties of realizability models. More specifically, we introduce an abstract notion of multi-evaluation relation which allows us to finely describe various nondeterministic behaviours. This defines a hierarchy of computational models, ordered by their degree of nondeterminism, similar to <PERSON><PERSON><PERSON>'s degrees of parallelism. What we show is a duality between the structure of the characteristic Boolean algebra of a realizability model and the degree of nondeterminism in its underlying computational model.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209140"}, {"primary_key": "3477532", "vector": [], "sparse_vector": [], "title": "Compositional Game Theory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce open games as a compositional foundation of economic game theory. A compositional approach potentially allows methods of game theory and theoretical computer science to be applied to large-scale economic models for which standard economic tools are not practical. An open game represents a game played relative to an arbitrary environment and to this end we introduce the concept of coutility, which is the utility generated by an open game and returned to its environment. Open games are the morphisms of a symmetric monoidal category and can therefore be composed by categorical composition into sequential move games and by monoidal products into simultaneous move games. Open games can be represented by string diagrams which provide an intuitive but formal visualisation of the information flows. We show that a variety of games can be faithfully represented as open games in the sense of having the same Nash equilibria and off-equilibrium best responses.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209165"}, {"primary_key": "3477533", "vector": [], "sparse_vector": [], "title": "Can One Escape Red Chains?: Regular Path Queries Determinacy is Undecidable.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"], "summary": "For a given set of queries (which are expressions in some query language) Q = {Q1, Q2, ... Qk} and for another query Q0 we say that Q determines Q0 if -- informally speaking -- for every database D, the information contained in the views Q(D) is sufficient to compute Q0(D).", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209120"}, {"primary_key": "3477534", "vector": [], "sparse_vector": [], "title": "A Generalized Modality for Recursion.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s later modality allows types to express that the output of a function does not immediately depend on its input, and thus that computing its fixpoint is safe. This idea, guarded recursion, has proved useful in various contexts, from functional programming with infinite data structures to formulations of step-indexing internal to type theory. Categorical models have revealed that the later modality corresponds in essence to a simple reindexing of the discrete time scale. Unfortunately, existing guarded type theories suffer from significant limitations for programming purposes. These limitations stem from the fact that the later modality is not expressive enough to capture precise input-output dependencies of functions. As a consequence, guarded type theories reject many productive definitions. Combining insights from guarded type theories and synchronous programming languages, we propose a new modality for guarded recursion. This modality can apply any well-behaved reindexing of the time scale to a type. We call such reindexings time warps. Several modalities from the literature, including later, correspond to fixed time warps, and thus arise as special cases of ours.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209148"}, {"primary_key": "3477535", "vector": [], "sparse_vector": [], "title": "Two complete axiomatisations of pure-state qubit quantum computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Categorical quantum mechanics places finite-dimensional quantum theory in the context of compact closed categories, with an emphasis on diagrammatic reasoning. In this framework, two equational diagrammatic calculi have been proposed for pure-state qubit quantum computing: the ZW calculus, developed by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and the first author for the purpose of qubit entanglement classification, and the ZX calculus, introduced by <PERSON><PERSON><PERSON> and <PERSON> to give an abstract description of complementary observables. Neither calculus, however, provided a complete axiomatisation of their model.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209128"}, {"primary_key": "3477536", "vector": [], "sparse_vector": [], "title": "Quasi-Open Bisimilarity with Mismatch is Intuitionistic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quasi-open bisimilarity is the coarsest notion of bisimilarity for the π-calculus that is also a congruence. This work extends quasi-open bisimilarity to handle mismatch (guards with inequalities). This minimal extension of quasi-open bisimilarity allows fresh names to be manufactured to provide constructive evidence that an inequality holds. The extension of quasi-open bisimilarity is canonical and robust --- coinciding with open barbed bisimilarity (an objective notion of bisimilarity congruence) and characterised by an intuitionistic variant of an established modal logic. The more famous open bisimilarity is also considered, for which the coarsest extension for handling mismatch is identified. Applications to checking privacy properties are highlighted. Examples and soundness results are mechanised using the proof assistant <PERSON><PERSON>.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209125"}, {"primary_key": "3477537", "vector": [], "sparse_vector": [], "title": "Polynomial Invariants for Affine Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We exhibit an algorithm to compute the strongest polynomial (or algebraic) invariants that hold at each location of a given affine program (i.e., a program having only non-deterministic (as opposed to conditional) branching and all of whose assignments are given by affine expressions). Our main tool is an algebraic result of independent interest: given a finite set of rational square matrices of the same dimension, we show how to compute the <PERSON><PERSON><PERSON> closure of the semigroup that they generate.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209142"}, {"primary_key": "3477538", "vector": [], "sparse_vector": [], "title": "Unification nets: canonical proof net quantifiers.", "authors": ["<PERSON>"], "summary": "Proof nets for MLL (unit-free Multiplicative Linear Logic) are concise graphical representations of proofs which are canonical in the sense that they abstract away syntactic redundancy such as the order of non-interacting rules. We argue that <PERSON><PERSON><PERSON>'s extension to MLL1 (first-order MLL) fails to be canonical because of redundant existential witnesses, and present canonical MLL1 proof nets called unification nets without them. For example, while there are infinitely many cut-free Girard nets ∀x Px ⊢ ∃x Px, one per arbitrary witness for ∃x, there is a unique cut-free unification net, with no specified witness.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209159"}, {"primary_key": "3477539", "vector": [], "sparse_vector": [], "title": "Satisfiability in multi-valued circuits.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Satisfiability of Boolean circuits is among the most known and important problems in theoretical computer science. This problem is NP-complete in general but becomes polynomial time when restricted either to monotone gates or linear gates. We go outside Boolean realm and consider circuits built of any fixed set of gates on an arbitrary large finite domain. From the complexity point of view this is strictly connected with the problems of solving equations (or systems of equations) over finite algebras.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209173"}, {"primary_key": "3477540", "vector": [], "sparse_vector": [], "title": "A Complete Axiomatisation of the ZX-Calculus for Clifford+T Quantum Mechanics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the first complete and approximately universal diagrammatic language for quantum mechanics. We make the ZX-Calculus, a diagrammatic language introduced by <PERSON><PERSON><PERSON> and <PERSON>, complete for the so-called Clifford+T quantum mechanics by adding two new axioms to the language. The completeness of the ZX-Calculus for Clifford+T quantum mechanics -- also called the π/4-fragment of the ZX-Calculus -- was one of the main open questions in categorical quantum mechanics. We prove the completeness of this fragment using the recently studied ZW-Calculus, a calculus dealing with integer matrices. We also prove that the π/4-fragment of the ZX-Calculus represents exactly all the matrices over some finite dimensional extension of the ring of dyadic rationals.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209131"}, {"primary_key": "3477541", "vector": [], "sparse_vector": [], "title": "Diagrammatic Reasoning beyond Clifford+T Quantum Mechanics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The ZX-Calculus is a graphical language for diagrammatic reasoning in quantum mechanics and quantum information theory. An axiomatisation has recently been proven to be complete for an approximatively universal fragment of quantum mechanics, the so-called Clifford+T fragment. We focus here on the expressive power of this axiomatisation beyond Clifford+T Quantum mechanics. We consider the full pure qubit quantum mechanics, and mainly prove two results: (i) First, the axiomatisation for Clifford+T quantum mechanics is also complete for all equations involving some kind of linear diagrams. The linearity of the diagrams reflects the phase group structure, an essential feature of the ZX-calculus. In particular all the axioms of the ZX-calculus are involving linear diagrams. (ii) We also show that the axiomatisation for Clifford+T is not complete in general but can be completed by adding a single (non linear) axiom, providing a simpler axiomatisation of the ZX-calculus for pure quantum mechanics than the one recently introduced by <PERSON><PERSON>.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209139"}, {"primary_key": "3477542", "vector": [], "sparse_vector": [], "title": "Type-two polynomial-time and restricted lookahead.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper provides an alternate characterization of second-order polynomial-time computability, with the goal of making second-order complexity theory more approachable. We rely on the usual oracle machines to model programs with subroutine calls. In contrast to previous results, the use of higher-order objects as running times is avoided, either explicitly or implicitly. Instead, regular polynomials are used. This is achieved by refining the notion of oracle-poly-time computability introduced by <PERSON>. We impose a further restriction on oracle interactions to force feasibility. Both the restriction and its purpose are very simple: it is well-known that <PERSON>'s model allows polynomial depth iteration of functional inputs with no restrictions on size, and thus does not preserve poly-time computability. To mend this we restrict the number of lookahead revisions, that is the number of times a query whose size exceeds that of any previous query may be asked. We prove that this leads to a class of feasible functionals and that all feasible problems can be solved within this class if one is allowed to separate a task into efficiently solvable subtasks. Formally, the closure of our class under lambda-abstraction and application are the basic feasible functionals. We also revisit the very similar class of strongly poly-time computable operators previously introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON>. We prove it to be strictly included in our class and, somewhat surprisingly, to have the same closure property. This is due to the nature of the limited recursion operator: it is not strongly poly-time but decomposes into two such operations and lies in our class.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209124"}, {"primary_key": "3477543", "vector": [], "sparse_vector": [], "title": "A Logical Account for Linear Partial Differential Equations.", "authors": ["<PERSON>"], "summary": "Differential Linear Logic (DiLL), introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON>, extends linear logic with a notion of linear approximation of proofs. While DiLL is classical logic, i.e. has an involutive negation, classical denotational models of it in which this notion of differentiation corresponds to the usual one, defined on any smooth function, were missing. We solve this issue by constructing a model of it based on nuclear topological vector spaces and distributions with compact support.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209192"}, {"primary_key": "3477544", "vector": [], "sparse_vector": [], "title": "Free Higher Groups in Homotopy Type Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a type A in homotopy type theory (HoTT), we can define the free ∞-group on A as the loop space of the suspension of A + 1. Equivalently, this free higher group can be defined as a higher inductive type F(A) with constructors unit: F(A), cons: A~F(A)~F(A), and conditions saying that every cons(a) is an auto-equivalence on F(A). Assuming that A is a set (i.e. satisfies the principle of unique identity proofs), we are interested in the question whether F(A) is a set as well, which is very much related to an open problem in the HoTT book [22, Ex. 8.2]. We show an approximation to the question, namely that the fundamental groups of F(A) are trivial, i.e. that ||F(A)||1 is a set.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209183"}, {"primary_key": "3477545", "vector": [], "sparse_vector": [], "title": "Conditional Value-at-Risk for Reachability and Mean Payoff in Markov Decision Processes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present the conditional value-at-risk (CVaR) in the context of Markov chains and Markov decision processes with reachability and mean-payoff objectives. CVaR quantifies risk by means of the expectation of the worst p-quantile. As such it can be used to design risk-averse systems. We consider not only CVaR constraints, but also introduce their conjunction with expectation constraints and quantile constraints (value-at-risk, VaR). We derive lower and upper bounds on the computational complexity of the respective decision problems and characterize the structure of the strategies in terms of memory and randomization.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209176"}, {"primary_key": "3477546", "vector": [], "sparse_vector": [], "title": "Weighted model counting beyond two-variable logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It was recently shown by <PERSON> at al. that the symmetric weighted first-order model counting problem (WFOMC) for sentences of two-variable logic FO2 is in polynomial time, while it is #P1-complete for some FO3-sentences. We extend the result for FO2 in two independent directions: to sentences of the form φ∧∀x∃=1 y ψ (x, y) with φ and ψ formulated in FO2 and to sentences of the uniform one-dimensional fragment U1 of FO, a recently introduced extension of two-variable logic with the capacity to deal with relation symbols of all arities. We note that the former generalizes the extension of FO2 with a functional relation symbol. We also identify a complete classification of first-order prefix classes according to whether WFOMC is in polynomial time or #P1-complete.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209168"}, {"primary_key": "3477547", "vector": [], "sparse_vector": [], "title": "Around Classical and Intuitionistic Linear Logics.", "authors": ["<PERSON>"], "summary": "We revisit many aspects of the syntactic relations between (variants of) classical linear logic (LL) and (variants of) intuitionistic linear logic (ILL) in the propositional setting.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209132"}, {"primary_key": "3477548", "vector": [], "sparse_vector": [], "title": "A modal μ perspective on solving parity games in quasi-polynomial time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a new quasi-polynomial algorithm for solving parity games. It is based on a new bisimulation invariant measure of complexity for parity games, called the register-index, which captures the complexity of the priority assignment. For fixed parameter k, the class of games with register-index bounded by k is solvable in polynomial time.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209115"}, {"primary_key": "3477549", "vector": [], "sparse_vector": [], "title": "Probabilistic Böhm Trees and Probabilistic Separation.", "authors": ["<PERSON>"], "summary": "We study the notion of observational equivalence in the call-by-name probabilistic λ-calculus, where two terms are said observationally equivalent if under any context, their head reductions converge with the same probability. Our goal is to generalise the separation theorem to this probabilistic setting. To do so we define probabilistic Böhm trees and probabilistic Nakajima trees, and we mix the well-known Böhm-out technique with some new techniques to manipulate and separate probability distributions.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209126"}, {"primary_key": "3477550", "vector": [], "sparse_vector": [], "title": "Enriching a Linear/Non-linear Lambda Calculus: A Programming Language for String Diagrams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Linear/non-linear (LNL) models, as described by <PERSON>, soundly model a LNL term calculus and LNL logic closely related to intuitionistic linear logic. Every such model induces a canonical enrichment that we show soundly models a LNL lambda calculus for string diagrams, introduced by <PERSON><PERSON> and <PERSON> (with primary application in quantum computing). Our abstract treatment of this language leads to simpler concrete models compared to those presented so far. We also extend the language with general recursion and prove soundness. Finally, we present an adequacy result for the diagram-free fragment of the language which corresponds to a modified version of <PERSON> and <PERSON><PERSON><PERSON>'s adjoint calculus with recursion.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209196"}, {"primary_key": "3477551", "vector": [], "sparse_vector": [], "title": "Ribbon Tensorial Logic.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We introduce a topologically-aware version of tensorial logic, called ribbon tensorial logic. To every proof of the logic, we associate a ribbon tangle which tracks the flow of tensorial negations inside the proof. The translation is functorial: it is performed by exhibiting a correspondence between the notion of dialogue category in proof theory and the notion of ribbon category in knot theory. Our main result is that the translation is also faithful: two proofs are equal modulo the equational theory of ribbon tensorial logic if and only if the associated ribbon tangles are equal up to topological deformation. This \"proof-as-tangle\" theorem may be understood as a coherence theorem for balanced dialogue categories, and as a mathematical foundation for topological game semantics.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209129"}, {"primary_key": "3477552", "vector": [], "sparse_vector": [], "title": "An Asynchronous Soundness Theorem for Concurrent Separation Logic.", "authors": ["<PERSON><PERSON><PERSON>", "Léo <PERSON>"], "summary": "Concurrent separation logic (CSL) is a specification logic for concurrent imperative programs with shared memory and locks. In this paper, we develop a concurrent and interactive account of the logic inspired by asynchronous game semantics. To every program C, we associate a pair of asynchronous transition systems [C]S and [C]L which describe the operational behavior of the Code when confronted to its Environment or Frame --- both at the level of machine states (S) and of machine instructions and locks (L). We then establish that every derivation tree π of a judgment Γ ⊢ {P}C{Q} defines a winning and asynchronous strategy [π]Sep with respect to both asynchronous semantics [C]S and [C]L. From this, we deduce an asynchronous soundness theorem for CSL, which states that the canonical map ℒ: [C]S~[C]L, from the stateful semantics [C]S to the stateless semantics [C]L satisfies a basic fibrational property. We advocate that this provides a clean and conceptual explanation for the usual soundness theorem of CSL, including the absence of data races.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209116"}, {"primary_key": "3477553", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Logic with Threshold Operators.", "authors": ["<PERSON>"], "summary": "We present a sound and complete axiomatisation of the Riesz modal logic extended with one inductively defined operator which allows the definition of threshold operators. This logic is capable of interpreting the bounded fragment of the logic probabilistic CTL over discrete and continuous Markov chains.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209118"}, {"primary_key": "3477554", "vector": [], "sparse_vector": [], "title": "A sequent calculus with dependent types for classical arithmetic.", "authors": ["<PERSON>"], "summary": "In a recent paper [11], <PERSON><PERSON> developed dPAω, a calculus in which constructive proofs for the axioms of countable and dependent choices could be derived via the encoding of a proof of countable universal quantification as a stream of it components. However, the property of normalization (and therefore the one of soundness) was only conjectured. The difficulty for the proof of normalization is due to the simultaneous presence of dependent types (for the constructive part of the choice), of control operators (for classical logic), of coinductive objects (to encode functions of type N→A into streams (a0, a1, ...)) and of lazy evaluation with sharing (for these coinductive objects).", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209199"}, {"primary_key": "3477555", "vector": [], "sparse_vector": [], "title": "An answer to the Gamma question.", "authors": ["<PERSON><PERSON>"], "summary": "We answer in this paper an open question (known as the \"Gamma question\"), related to the recent notion of coarse computability, which stems from complexity theory. The question was formulated by <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> in \"Asymptotic density, computable traceability and 1-randomness\" [1]. The Gamma value of an oracle set measures to what extent each set computable with the oracle is approximable in the sense of density by a computable set. The closer to 1 this value is, the closer the oracle is to being computable. The Gamma question asks whether this value can be strictly in between 0 and 1/2.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209117"}, {"primary_key": "3477556", "vector": [], "sparse_vector": [], "title": "Dialectica models of type theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present two Dialectica-like constructions for models of intensional Martin-Löf type theory based on <PERSON><PERSON><PERSON>'s original Dialectica interpretation and the <PERSON><PERSON><PERSON><PERSON><PERSON> variant, bringing dependent types to categorical proof theory. We set both constructions within a logical predicates style theory for display map categories where we show that 'quasifibred' versions of dependent products and universes suffice to construct their standard counterparts. To support the logic required for dependent products in the first construction, we propose a new semantic notion of finite sum for dependent types, generalizing finitely-complete extensive categories. The second avoids extensivity assumptions using biproducts in a Kleisli category for a fibred additive monad.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209207"}, {"primary_key": "3477557", "vector": [], "sparse_vector": [], "title": "The Geometry of Computation-Graph Abstraction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The popular library TENSORFLOW (TF) has familiarised the mainstream of machine-learning community with programming language concepts such as data-flow computing and automatic differentiation. Additionally, it has introduced some genuinely new syntactic and semantic programming concepts. In this paper we study one such new concept, the ability to extract and manipulate the state of a computation graph. This feature allows the convenient specification of parameterised models by freeing the programmer of the bureaucracy of parameter management, while still permitting the use of generic, model-independent, search and optimisation algorithms. We study this new language feature, which we call 'graph abstraction' in the context of the call-by-value lambda calculus, using the recently developed Dynamic Geometry of Interaction formalism. We give a simple type system guaranteeing the safety of graph abstraction, and we also show the safety of critical language properties such as garbage collection and the beta law. The semantic model suggests that the feature could be implemented in a general-purpose functional language reasonably efficiently.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209127"}, {"primary_key": "3477558", "vector": [], "sparse_vector": [], "title": "A Fixpoint Logic and Dependent Effects for Temporal Property Verification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing approaches to temporal verification of higher-order functional programs have either sacrificed compositionality in favor of achieving automation or vice-versa. In this paper we present a dependent-refinement type & effect system to ensure that well-typed programs satisfy given temporal properties, and also give an algorithmic approach---based on deductive reasoning over a fixpoint logic---to typing in this system. The first contribution is a novel type-and-effect system capable of expressing dependent temporal effects, which are fixpoint logic predicates on event sequences and program values, extending beyond the (non-dependent) temporal effects used in recent proposals. Temporal effects facilitate compositional reasoning whereby the temporal behavior of program parts are summarized as effects and combined to form those of the larger parts. As a second contribution, we show that type checking and typability for the type system can be reduced to solving first-order fixpoint logic constraints. Finally, we present a novel deductive system for solving such constraints. The deductive system consists of rules for reasoning via invariants and well-founded relations, and is able to reduce formulas containing both least and greatest fixpoints to predicate-based reasoning.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209204"}, {"primary_key": "3477559", "vector": [], "sparse_vector": [], "title": "MSO Queries on Trees: Enumerating Answers under Updates Using Forest Algebras.", "authors": ["<PERSON>"], "summary": "We investigate efficient enumeration of answers to MSO-definable queries over trees which are subject to local updates. We exhibit an algorithm that uses an O(n) preprocessing phase and enumerates answers with O(log(n)) delay between them. When the tree is updated, the algorithm can avoid repeating expensive preprocessing and restart the enumeration phase within O(log(n)) time. This improves over previous results that require O(log2(n)) time after updates and have O(log2(n)) delay. Our algorithms and complexity results in the paper are presented in terms of node-selecting tree automata representing the MSO queries. To present our algorithm, we introduce a balancing scheme for parse trees of forest algebra formulas that is of its own interest to lift results from strings to trees.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209144"}, {"primary_key": "3477560", "vector": [], "sparse_vector": [], "title": "Degrees of Relatedness: A Unified Framework for Parametricity, Irrelevance, Ad Hoc Polymorphism, Intersections, Unions and Algebra in Dependent Type Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Dependent type theory allows us to write programs and to prove properties about those programs in the same language. However, some properties do not require much proof, as they are evident from a program's implementation, e.g. if a polymorphic program is not ad hoc but relationally parametric, then we get parametricity theorems for free. If we want to safely shortcut proofs by relying on the evident good behaviour of a program, then we need a type-level guarantee that the program is indeed well-behaved. This can be achieved by annotating function types with a modality describing the behaviour of functions.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209119"}, {"primary_key": "3477561", "vector": [], "sparse_vector": [], "title": "Continuous Reasoning: Scaling the impact of formal methods.", "authors": ["Peter <PERSON>;Hearn"], "summary": "This paper describes work in continuous reasoning, where formal reasoning about a (changing) codebase is done in a fashion which mirrors the iterative, continuous model of software development that is increasingly practiced in industry. We suggest that advances in continuous reasoning will allow formal reasoning to scale to more programs, and more programmers. The paper describes the rationale for continuous reasoning, outlines some success cases from within industry, and proposes directions for work by the scientific community.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209109"}, {"primary_key": "3477562", "vector": [], "sparse_vector": [], "title": "Parameterized circuit complexity of model-checking on sparse structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that for every class ℒ of graphs with effectively bounded expansion, given a first-order sentence φ and an n-element structure A whose <PERSON><PERSON><PERSON><PERSON> graph belongs to ℒ, the question whether φ holds in A can be decided by a family of AC-circuits of size f(φ) · nc and depth f(φ) + c log n, where f is a computable function and c is a universal constant. This places the model-checking problem for classes of bounded expansion in the parameterized circuit complexity class para-AC1. On the route to our result we prove that the basic decomposition toolbox for classes of bounded expansion, including orderings with bounded weak coloring numbers and low treedepth decompositions, can be computed in para-AC1.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209136"}, {"primary_key": "3477563", "vector": [], "sparse_vector": [], "title": "On the number of types in sparse graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that for every class of graphs ℒ which is nowhere dense, as defined by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [28, 29], and for every first order formula φ(x, y), whenever one draws a graph G ∈ ℒ and a subset of its nodes A, the number of subsets of A|y| which are of the form {u ∈ A|y|: G |= φ(ū, v)} for some valuation ū of x in G is bounded by O(|A||x|ε), for every ε > 0. This provides optimal bounds on the VC-density of first-order definable set systems in nowhere dense graph classes. We also give two new proofs of upper bounds on quantities in nowhere dense classes which are relevant for their logical treatment. Firstly, we provide a new proof of the fact that nowhere dense classes are uniformly quasi-wide, implying explicit, polynomial upper bounds on the functions relating the two notions. Secondly, we give a new combinatorial proof of the result of <PERSON> and <PERSON> [1] stating that every nowhere dense class of graphs is stable. In contrast to the previous proofs of the above results, our proofs are completely finitistic and constructive, and yield explicit and computable upper bounds on quantities related to uniform quasi-wideness (margins) and stability (ladder indices).", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209178"}, {"primary_key": "3477564", "vector": [], "sparse_vector": [], "title": "Syntax and Semantics for Operations with Scopes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by the problem of separating syntax from semantics in programming with algebraic effects and handlers, we propose a categorical model of abstract syntax with so-called scoped operations. As a building block of a term, a scoped operation is not merely a node in a tree, as it can also encompass a whole part of the term (a scope). Some examples from the area of programming are given by the operation catch for handling exceptions, in which the part in the scope is the code that may raise an exception, or the operation once, which selects a single solution from a nondeterministic computation. A distinctive feature of such operations is their behaviour under program composition, that is, syntactic substitution.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209166"}, {"primary_key": "3477565", "vector": [], "sparse_vector": [], "title": "Differential Equation Axiomatization: The Impressive Power of Differential Ghosts.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove the completeness of an axiomatization for differential equation invariants. First, we show that the differential equation axioms in differential dynamic logic are complete for all algebraic invariants. Our proof exploits differential ghosts, which introduce additional variables that can be chosen to evolve freely along new differential equations. Cleverly chosen differential ghosts are the proof-theoretical counterpart of dark matter. They create new hypothetical state, whose relationship to the original state variables satisfies invariants that did not exist before. The reflection of these new invariants in the original system then enables its analysis. We then show that extending the axiomatization with existence and uniqueness axioms makes it complete for all local progress properties, and further extension with a real induction axiom makes it complete for all real arithmetic invariants. This yields a parsimonious axiomatization, which serves as the logical foundation for reasoning about invariants of differential equations. Moreover, our results are purely axiomatic, and so the axiomatization is suitable for sound implementation in foundational theorem provers.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209147"}, {"primary_key": "3477566", "vector": [], "sparse_vector": [], "title": "Allegories: decidability and graph homomorphisms.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Allegories were introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON>; they form a fragment of <PERSON><PERSON><PERSON>'s calculus of relations. We show that their equational theory is decidable by characterising it in terms of a specific class of graph homomorphisms.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209172"}, {"primary_key": "3477567", "vector": [], "sparse_vector": [], "title": "A functional interpretation with state.", "authors": ["<PERSON>"], "summary": "We present a new variant of <PERSON><PERSON><PERSON>'s functional interpretation in which extracted programs, rather than being pure terms of system T, interact with a global state. The purpose of the state is to store relevant information about the underlying mathematical environment. Because the validity of extracted programs can depend on the validity of the state, this offers us an alternative way of dealing with the contraction problem. Furthermore, this new formulation of the functional interpretation gives us a clear semantic insight into the computational content of proofs, and provides us with a way of improving the efficiency of extracted programs.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209134"}, {"primary_key": "3477568", "vector": [], "sparse_vector": [], "title": "LMSO: A Curry-Howard Approach to Church&apos;s Synthesis via Linear Logic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose LMSO, a proof system inspired from Linear Logic, as a proof-theoretical framework to extract finite-state stream transducers from linear-constructive proofs of omega-regular specifications. We advocate LMSO as a stepping stone toward semi-automatic approaches to Church's synthesis combining computer assisted proofs with automatic decisions procedures. LMSO is correct in the sense that it comes with an automata-based realizability model in which proofs are interpreted as finite-state stream transducers. It is moreover complete, in the sense that every solvable instance of <PERSON>'s synthesis problem leads to a linear-constructive proof of the formula specifying the synthesis problem.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209195"}, {"primary_key": "3477569", "vector": [], "sparse_vector": [], "title": "Computable decision making on the reals and other spaces: via partiality and nondeterminism.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Though many safety-critical software systems use floating point to represent real-world input and output, the mathematical specifications of these systems' behaviors use real numbers. Significant deviations from those specifications can cause errors and jeopardize safety. To ensure system safety, some programming systems offer exact real arithmetic, which often enables a program's computation to match its mathematical specification exactly. However, exact real arithmetic complicates decision-making: in these systems, it is impossible to compute (total and deterministic) discrete decisions based on connected spaces such as R. We present programming-language semantics based on constructive topology with variants allowing nondeterminism and/or partiality. Either nondeterminism or partiality suffices to allow computable decision making on connected spaces such as R. We then introduce pattern matching on spaces, a language construct for creating programs on spaces, generalizing pattern matching in functional programming, where patterns need not represent decidable predicates and also may overlap or be inexhaustive, giving rise to nondeterminism or partiality, respectively. Nondeterminism and/or partiality also yield formal logics for constructing approximate decision procedures. We extended the Marshall language for exact real arithmetic with these constructs and implemented some programs with it.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209193"}, {"primary_key": "3477570", "vector": [], "sparse_vector": [], "title": "Logical paradoxes in quantum computation.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The precise features of quantum theory enabling quantum computational power are unclear. Contextuality---the denial of a notion of classical physical reality---has emerged as a promising hypothesis: e.g. <PERSON> et al. showed that the magic states needed to practically achieve quantum computation are contextual.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209123"}, {"primary_key": "3477571", "vector": [], "sparse_vector": [], "title": "A General Framework for Relational Parametricity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>' original theory of relational parametricity was intended to capture the observation that polymorphically typed System F programs preserve all relations between inputs. But as <PERSON> himself later showed, his theory can only be formulated in a metatheory with an impredicative universe, such as the Calculus of Inductive Constructions. A number of more abstract treatments of relational parametricity have since appeared; however, as we show, none of these seem to express <PERSON>' original theory in a satisfactory way.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209141"}, {"primary_key": "3477572", "vector": [], "sparse_vector": [], "title": "Guarded Computational Type Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s later modality can be used to specify and define recursive functions which are causal or synchronous; in concert with a notion of clock variable, it is possible to also capture the broader class of productive (co)programs. Until now, it has been difficult to combine these constructs with dependent types in a way that preserves the operational meaning of type theory and admits a hierarchy of universes Ui.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209153"}, {"primary_key": "3477573", "vector": [], "sparse_vector": [], "title": "Species, Profunctors and Taylor Expansion Weighted by SMCC: A Unified Framework for Modelling Nondeterministic, Probabilistic and Quantum Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "C.<PERSON><PERSON><PERSON>"], "summary": "Motivated by a tight connection between <PERSON><PERSON>'s combinatorial species and quantitative models of linear logic, this paper introduces weighted generalised species (or weighted profunctors), where weights are morphisms of a given symmetric monoidal closed category (SMCC). For each SMCC W, we show that the category of W-weighted profunctors is a Lafont category, a categorical model of linear logic with exponential. As a model of programming languages, the construction of this paper gives a unified framework that induces adequate models of nondeterministic, probabilistic, algebraic and quantum programming languages by an appropriate choice of the weight SMCC.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209157"}, {"primary_key": "3477574", "vector": [], "sparse_vector": [], "title": "Every λ-Term is Meaningful for the Infinitary Relational Model.", "authors": ["<PERSON>"], "summary": "Infinite types and formulas are known to have really curious and unsound behaviors. For instance, they allow to type Ω, the auto-autoapplication and they thus do not ensure any form of normalization/productivity. Moreover, in most infinitary frameworks, it is not difficult to define a type R that can be assigned to every λ-term. However, these observations do not say much about what coinductive (i.e. infinitary) type grammars are able to provide: it is for instance very difficult to know what types (besides R) can be assigned to a given term in this setting. We begin with a discussion on the expressivity of different forms of infinite types. Then, using the resource-awareness of sequential intersection types (system S) and tracking, we prove that infinite types are able to characterize the arity of every λ-terms and that, in the infinitary extension of the relational model, every term has a \"meaning\" i.e. a non-empty denotation. From the technical point of view, we must deal with the total lack of guarantee of productivity for typable terms: we do so by importing methods inspired by first order model theory.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209133"}, {"primary_key": "3477575", "vector": [], "sparse_vector": [], "title": "A <PERSON>rem for Fuzzy Modal Logic.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a fuzzy (or quantitative) version of the <PERSON> theorem, which characterizes propositional modal logic as the bisimulation-invariant fragment of first-order logic. Specifically, we consider a first-order fuzzy predicate logic along with its modal fragment, and show that the fuzzy first-order formulas that are non-expansive w.r.t. the natural notion of bisimulation distance are exactly those that can be approximated by fuzzy modal formulas.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209180"}, {"primary_key": "3477576", "vector": [], "sparse_vector": [], "title": "A theory of linear typings as flows on 3-valent graphs.", "authors": ["<PERSON><PERSON>"], "summary": "Building on recently established enumerative connections between lambda calculus and the theory of embedded graphs (or \"maps\"), this paper develops an analogy between typing (of lambda terms) and coloring (of maps). Our starting point is the classical notion of an abelian group-valued \"flow\" on an abstract graph (<PERSON><PERSON>, 1954). Typing a linear lambda term may be naturally seen as constructing a flow (on an embedded 3-valent graph with boundary) valued in a more general algebraic structure consisting of a preordered set equipped with an \"implication\" operation and unit satisfying composition, identity, and unit laws. Interesting questions and results from the theory of flows (such as the existence of nowhere-zero flows) may then be re-examined from the standpoint of lambda calculus and logic. For example, we give a characterization of when the local flow relations (across vertices) may be categorically lifted to a global flow relation (across the boundary), proving that this holds just in case the underlying map has the orientation of a lambda term. We also develop a basic theory of rewriting of flows that suggests topological meanings for classical completeness results in combinatory logic, and introduce a polarized notion of flow, which draws connections to the theory of proof-nets in linear logic and to bidirectional typing.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209121"}, {"primary_key": "3477577", "vector": [], "sparse_vector": [], "title": "Separability by piecewise testable languages and downward closures beyond subwords.", "authors": ["<PERSON>"], "summary": "We introduce a flexible class of well-quasi-orderings (WQOs) on words that generalizes the ordering of (not necessarily contiguous) subwords. Each such WQO induces a class of piecewise testable languages (PTLs) as Boolean combinations of upward closed sets. In this way, a range of regular language classes arises as PTLs. Moreover, each of the WQOs guarantees regularity of all downward closed sets. We consider two problems. First, we study which (perhaps non-regular) language classes allow to decide whether two given languages are separable by a PTL with respect to a given WQO. Second, we want to effectively compute downward closures with respect to these WQOs. Our first main result is that for each of the WQOs, under mild assumptions, both problems reduce to the simultaneous unboundedness problem (SUP) and are thus solvable for many powerful system models. In the second main result, we apply the framework to show decidability of separability of regular languages by B∑1[<, mod], a fragment of first-order logic with modular predicates.", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108.3209201"}, {"primary_key": "3530605", "vector": [], "sparse_vector": [], "title": "Proceedings of the 33rd Annual ACM/IEEE Symposium on Logic in Computer Science, LICS 2018, Oxford, UK, July 09-12, 2018", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It is our pleasure to present the proceedings volume of the 33rd Annual ACM/IEEE Symposium on Logic in Computer Science (LICS 2018), held from9th to 12th July 2018 at Oxford (UK) as part of the Federated Logic Conference (FLoC 2018).", "published": "2018-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3209108"}]