#!/bin/bash

# 获取项目根目录的绝对路径
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 用于记录上次执行的日期
LAST_RUN_DATE=""

while true; do
    # 获取当前时间
    CURRENT_HOUR=$(date +%H)
    CURRENT_MIN=$(date +%M)
    CURRENT_DATE=$(date +%Y-%m-%d)
    
    # 检查是否为凌晨5点且今天还未执行
    if [ "$CURRENT_HOUR" = "05" ] && [ "$CURRENT_MIN" = "00" ] && [ "$CURRENT_DATE" != "$LAST_RUN_DATE" ]; then
        # 激活 Python 虚拟环境
        source $PROJECT_ROOT/arxiv/bin/activate
        
        # 执行 Python 脚本
        cd $PROJECT_ROOT
        python backend/arxiv_paper.py --pre_day 1
        
        # 记录日志
        echo "$(date): Executed arxiv_paper.py" >> $PROJECT_ROOT/cron.log
        
        # 更新上次执行日期
        LAST_RUN_DATE=$CURRENT_DATE
    fi
    
    # 休眠60秒
    sleep 60
done