[{"primary_key": "3751738", "vector": [], "sparse_vector": [], "title": "Dealing with Uncertainties in Analog/Mixed-Signal Systems: Invited.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Analog circuits lack perfect accuracy; noise, PVT-Variations, non-linearities, crosstalk and many other effects cause unforeseen deviations that we also call \"uncertainties\". In the paper we classify various causes of uncertainties and describe a simple, generic, mathematical model of uncertain signals and systems that is applicable from circuit level up to system level. We show in particular how to use affine arithmetic forms to document uncertainties of different kind in an uncertainty table, from its causes in circuits up to properties at system-level.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072949"}, {"primary_key": "3751739", "vector": [], "sparse_vector": [], "title": "Fixed-Parameter Tractable Algorithms for Optimal Layout Decomposition and Beyond.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "This paper studies the application of fixed-parameter tractable (FPT) algorithms to solve computer-aided design (CAD) problems. Specifically, we focus on layout decomposition problems for three lithography technologies: double patterning lithography (DPL), DPL with E-beam lithography (DPL+EBL), and DPL+DSA+EBL. Layout decomposition for the first two technologies are long-standing open problems without efficient optimal solutions, and the third technology is very promising in the future. The proposed approaches use ideas drastically different from all the previous works and can get optimal solutions in a short time. We show the great potential of applying FPT algorithms to solve more NP-hard problems efficiently in CAD.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062250"}, {"primary_key": "3751740", "vector": [], "sparse_vector": [], "title": "Task Mapping on SMART NoC: Contention Matters, Not the Distance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "On-chip communication is the bottleneck of system performance for NoC-based MPSoCs. SMART, a recently proposed NoC architecture, enables single-cycle multi-hop communications. In SMART NoCs, unconflicted messages can go through an express bypass and the communication efficiency is significantly improved, while conflicted messages have to be buffered for guaranteed delivery with extra delays. Therefore, that performance of SMART NoC may be seriously degraded when communication contention increases. In this paper, we present task mapping techniques to address this problem for SMART NoCs, with the consideration of communication contention, rather than inter-processor distance, by minimizing conflicts and thus maximizing bypass utilization. We first model the entire problem by ILP formulations to find the theoretically optimal solution, and further propose polynomial-time algorithms for contention-aware task mapping and message priority assignment. Communicating tasks can be mapped to distant processors in SMART NoCs as long as conflict-free communication paths can be established and bypass can be enabled. Evaluation results on real benchmarks show an average of 44.1% and 32.8% improvement in communication efficiency and application performance compared to state-of-the-art techniques. The proposed heuristic algorithms only introduce 1.9% performance difference compared to the ILP model and are more scalable to large-size NoCs.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062323"}, {"primary_key": "3751741", "vector": [], "sparse_vector": [], "title": "Accelerator Design for Deep Learning Training: Extended Abstract: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jung<PERSON><PERSON> Choi", "<PERSON><PERSON>", "Jinwook Oh", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNNs) have emerged as a powerful and versatile set of techniques showing successes on challenging artificial intelligence (AI) problems. Applications in domains such as image/video processing, autonomous cars, natural language processing, speech synthesis and recognition, genomics and many others have embraced deep learning as the foundation. DNNs achieve superior accuracy for these applications with high computational complexity using very large models which require 100s of MBs of data storage, exaops of computation and high bandwidth for data movement. In spite of these impressive advances, it still takes days to weeks to train state of the art Deep Networks on large datasets - which directly limits the pace of innovation and adoption. In this paper, we present a multi-pronged approach to address the challenges in meeting both the throughput and the energy efficiency goals for DNN training.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072944"}, {"primary_key": "3751742", "vector": [], "sparse_vector": [], "title": "Efficient Hierarchical Performance Modeling for Integrated Circuits via Bayesian Co-Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the continuous drive towards integrated circuits scaling, efficient performance modeling is becoming more crucial yet, more challenging. In this paper, we propose a novel method of hierarchical performance modeling based on Bayesian co-learning. We exploit the hierarchical structure of a circuit to establish a Bayesian framework where unlabeled data samples are generated to improve modeling accuracy without running additional simulation. Consequently, our proposed method only requires a small number of labeled samples, along with a large number of unlabeled samples obtained at almost no-cost, to accurately learn a performance model. Our numerical experiments demonstrate that the proposed approach achieves up to 3.66x runtime speed-up over the state-of-the-art modeling technique without surrendering any accuracy.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062235"}, {"primary_key": "3751743", "vector": [], "sparse_vector": [], "title": "DIMP: A Low-Cost Diversity Metric Based on Circuit Path Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Diversity has been regarded as a desirable property of redundant instances, since it allows circuits to behave differently in front of a given fault. However, while qualitatively diversity is a well-understood concept, usable efficient metrics do not exist to quantify diversity in the context of safety-related systems. In this paper we cover this gap by proposing DIMP, a low-cost diversity metric based on analyzing the paths of the redundant circuits. We relate it to the particular case of automotive microcontrollers implementing lockstep cores and show that it can be successfully used providing relevant information for addressing common cause faults.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062231"}, {"primary_key": "3751744", "vector": [], "sparse_vector": [], "title": "An Architecture for Learning Stream Distributions with Application to RNG Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Learning cumulative distribution functions (CDFs) is a widely studied problem in data stream summarization. While current techniques have efficient software implementations, their efficiency depends on updates to data structures that are not easily adapted to FPGA or ASIC implementation. In this work, we develop an algorithm and a compact hardware architecture for learning the CDF of a data stream and apply our technique to the problem of on-chip run-time testing for bias in the output of random number generators (RNGs). Unlike previous approaches, our method is successful regardless of the expected output distribution of the RNG under test.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062199"}, {"primary_key": "3751745", "vector": [], "sparse_vector": [], "title": "Towards Aging-Induced Approximations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent technology nodes, wide guardbands are needed to overcome reliability degradations due to aging. Such guardbands manifest as reduced efficiency and performance. Existing approaches to reduce guardbands trade off aging impact for increased circuit overhead. By contrast, the goal of this work is to completely remove guardbands through exploring, for the first time, application of approximate computing principles in the context of aging. As a result of naively narrowing or removing guardbands, timing errors start to appear as transistors age. We demonstrate that even in circuits that may tolerate errors, aging can be catastrophic due to unacceptable quality loss. Furthermore, quantifying such aging-induced quality loss necessitates expensive (often infeasible) gate-level simulations of the complete design. We show how nondeterministic aging-induced timing errors can be converted into deterministic and controlled approximations instead. We first translate the required guardband over time into an equivalent reduction in precision for individual RTL components. We then demonstrate how, based on pre-characterization of RTL components, we can quantify aging-induced approximation at the whole microarchitecture level without the need for further gate-level simulations. Results show that a 3 bit reduction in precision is sufficient to sustain 10 years of operation under worst-case aging in the context of an image processing circuit. This corresponds to an acceptable PSNR reduction of merely 8 dB, while at the same time increasing area and energy efficiency by 13%.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062331"}, {"primary_key": "3751746", "vector": [], "sparse_vector": [], "title": "Crossroads: Time-Sensitive Autonomous Intersection Management Technique.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For autonomous vehicles, intelligent autonomous intersection management will be required for safe and efficient operation. In order to achieve safe operation despite uncertainties in vehicle trajectory, intersection management techniques must consider a safety buffer around the vehicles. For truly safe operation, an extra buffer space should be added to account for the network and computational delay caused by communication with the Intersection Manager (IM). However, modeling the worst-case computation and network delay as additional buffer around the vehicle degrades the throughput of the intersection. To avoid this problem, AIM[1], a popular state-of-the-art IM, adopts a query-based approach in which the vehicle requests to enter at a certain arrival time dictated by its current velocity and distance to the intersection, and the IM replies yes/no. Although this solution does not degrade the position uncertainty, it ultimately results in poor intersection throughput. We present Crossroads, a time-sensitive programming method to program the interface of a vehicle and the IM. Without requiring additional buffer to account for the effect of network and computational delay, Crossroads enables efficient intersection management. Test results on a 1/10 scale model of intersection using TRAXXAS RC cars demonstrates that our Crossroads approach obviates the need for large buffers to accommodate for the network and computation delay, and can reduce the average wait time for the vehicles at a single-lane intersection by 24%. To compare Crossroads with previous approaches, we perform extensive Matlab simulations, and find that Crossroads achieves on average 1.62X higher throughput than a simple VT-IM with extra safety buffer, and 1.36X better than AIM.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062221"}, {"primary_key": "3751747", "vector": [], "sparse_vector": [], "title": "RESPARC: A Reconfigurable and Energy-Efficient Architecture with Memristive Crossbars for Deep Spiking Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neuromorphic computing using post-CMOS technologies is gaining immense popularity due to its promising abilities to address the memory and power bottlenecks in von-Neumann computing systems. In this paper, we propose RESPARC - a reconfigurable and energy efficient architecture built-on Memristive Crossbar Arrays (MCA) for deep Spiking Neural Networks (SNNs). Prior works were primarily focused on device and circuit implementations of SNNs on crossbars. RESPARC advances this by proposing a complete system for SNN acceleration and its subsequent analysis. RESPARC utilizes the energy-efficiency of MCAs for inner-product computation and realizes a hierarchical reconfigurable design to incorporate the data-flow patterns in an SNN in a scalable fashion. We evaluate the proposed architecture on different SNNs ranging in complexity from 2k-230k neurons and 1.2M-5.5M synapses. Simulation results on these networks show that compared to the baseline digital CMOS architecture, RESPARC achieves 500x (15x) efficiency in energy benefits at 300x (60x) higher throughput for multi-layer perceptrons (deep convolutional networks). Furthermore, RESPARC is a technology-aware architecture that maps a given SNN topology to the most optimized MCA size for the given crossbar technology.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062311"}, {"primary_key": "3751748", "vector": [], "sparse_vector": [], "title": "Statistical Error Analysis for Low Power Approximate Adders.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Low-power approximate adders provide basic building blocks for approximate computing hardware that have shown remarkable energy efficiency for error-resilient applications (like image/video processing, computer vision, etc.), especially for battery-driven portable systems. In this paper, we present a novel scalable, fast yet accurate analytical method to evaluate the output error probability of multi-bit low power adders for a predetermined probability of input bits. Our method recursively computes the error probability by considering the accurate cases only, which are considerably smaller than the erroneous ones. Our method can handle the error analysis of a wider-range of adders with negligible computational overhead. To ensure its rapid adoption in industry and academia, we have open-sourced our LabVIEW and MATLAB libraries.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062319"}, {"primary_key": "3751749", "vector": [], "sparse_vector": [], "title": "ESL Design in SystemC AMS: Introducing a top-down design methodology for mixed-signal systems: Invited.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "For the design and verification of a heterogeneous system architecture containing analog, digital, and software functionality, the use of modern languages and advanced Electronic System-Level (ESL) top-down design methodologies becomes fundamental. This paper will present the industrial application of the SystemC analog/mixed-signal (AMS) extensions in combination with SystemC and other C++ libraries, to enable mixed-signal system-level design of a Magneto Resistive Sensor product. It will demonstrate an efficient top-down design and early verification approach by creating an accurate mixed-signal virtual prototype for concept development and architecture exploration, bringing high modelling fidelity and high simulation speed.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072951"}, {"primary_key": "3751750", "vector": [], "sparse_vector": [], "title": "Arbitrary Precision and Complexity Tradeoffs for Gate-Level Information Flow Tracking.", "authors": ["<PERSON>", "<PERSON>", "Yu Tai", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hardware has become an increasingly attractive target for attackers, yet we still largely lack tools that enable us to analyze large designs for security flaws. Information flow tracking (IFT) models provide an approach to verifying a hardware design's adherence to security properties related to isolation and reachability.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062203"}, {"primary_key": "3751751", "vector": [], "sparse_vector": [], "title": "LSTA: Learning-Based Static Timing Analysis for High-Dimensional Correlated On-Chip Variations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the transistor process technology continues to scale, the aging effect posits new challenges to the already complex static timing analysis (STA) process. In this paper, we first observe that aging can be thought of a type of correlated dynamic on-chip variations (OCV), and identify the problem introduced by such type of OCV. In particular, we take the negative bias temperature instability (NBTI) as an example dynamic OCV mechanism. We then propose a learning-based STA (LSTA) library to \"predict\" the timing of gates by capturing the correlation between our designed predictors. In the experiment, we used a linear regressor, support vector regression, and a non-linear method, random forest, to create the prediction model. An ISCAS'89 benchmark circuit is used as a training sample to for the algorithms to learn the aging model of gates, and the accuracies of the model is then tested on two processor-scale designs using the library are evaluated, achieving a maximum absolute error of 3.42%.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062280"}, {"primary_key": "3751752", "vector": [], "sparse_vector": [], "title": "Coupled circuit/EM simulation for radio frequency circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In radio frequency (RF) circuits the lumped model assumptions are often not valid anymore. To circumvent this problem, mixed-level circuit/electro-magnetic/device simulation allows the use of Maxwell's and semiconductor equations for the critical devices and lumped models for the ambient circuitry. Besides AC, DC, periodic steady state and transient analysis, the multirate partial differential equation technique is employed for RF circuitry, based on a spline/wavelet expansion of the waveforms. The novel technique is demonstrated for RF circuits with on-chip inductors, transmission lines and baluns, modeled as 3D devices.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062219"}, {"primary_key": "3751753", "vector": [], "sparse_vector": [], "title": "Power-aware Performance Tuning of GPU Applications Through Microbenchmarking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Tuning GPU applications is a very challenging task as any source-code optimization can sensibly impact performance, power, and energy consumption of the GPU device. Such an impact also depends on the GPU on which the application is run. This paper presents a suite of microbenchmarks that provides the actual characteristics of specific GPU device components (e.g., arithmetic instruction units, memories, etc.) in terms of throughput, power, and energy consumption. It shows how the suite can be combined to standard profiler information to efficiently drive the application tuning by considering the three design constraints (power, performance, energy consumption) and the characteristics of the target GPU device.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062304"}, {"primary_key": "3751754", "vector": [], "sparse_vector": [], "title": "EDiFy: An Execution time Distribution Finder.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded real-time systems are subjected to stringent timing constraints. Analysing their timing behaviour is therefore of great significance. So far, research on the timing behaviour of real-time systems has been primarily focused on finding out what happens in the worst-case (i.e., finding the worst case execution time, or WCET).", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062233"}, {"primary_key": "3751755", "vector": [], "sparse_vector": [], "title": "iClaire: A Fast and General Layout Pattern Classification Algorithm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Layout pattern classification, which groups similar layout clips into clusters, underlies a variety of design for manufacturability (DFM) applications such as hotspot library generation, hierarchical data storage, and yield optimization speedup. The key challenges of layout pattern classification are clip representation and clip clustering, while the mutually conflicting concerns are efficiency and solution quality (in terms of cluster count). In this paper, we present a fast and general layout pattern classification algorithm. Our simple but general clip representation captures both topology and density; we can handle not only rigid area match or edge displacement constraints but also variant edge tolerances and don't care regions. On the other hand, for achieving a small cluster count, our clip clustering is guided by the natural grouping structure of layout clips. Our experiments are conducted on 2016 CAD contest at ICCAD benchmark suite; our results show that our algorithm outperforms the reference solution and all contest winning teams, delivering the smallest cluster count, fastest runtime, and 100% validity. In addition to the good solution quality, the interplay between adopted simple and easily manipulated data structures and our algorithm makes it fast and viable to be incorporated into practical DFM flows.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062263"}, {"primary_key": "3751756", "vector": [], "sparse_vector": [], "title": "Modeling the Effects of AUTOSAR Overheads on Application Timing and Schedulability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "AUTOSAR (AUTomotive Open System ARchitecture) provides an open and standardized E/E architecture for automobiles. AUTOSAR systems exhibit real-time requirements, i.e., an AUTOSAR application must always be schedulable. In this paper, we propose an overhead-aware method to find schedulable design configurations for an AUTOSAR application. We show how to construct a timing model for the application, discuss how to quantify the overheads of an AUTOSAR stack implementation, and assess their impact on timing and schedulability. We demonstrate the proposed method on an automotive case study and evaluate the effects of different types of overheads using synthetic applications.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062287"}, {"primary_key": "3751757", "vector": [], "sparse_vector": [], "title": "Minimizing Thermal Gradient and Pumping Power in 3D IC Liquid Cooling Network Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>", "<PERSON><PERSON>"], "summary": "Liquid cooling shows great potential in resolving the huge thermal obstacle in 3D ICs. However, it brings new challenges including large thermal gradient and high pumping requirement. In this paper, liquid cooling networks with flexible topology are investigated to achieve more desirable trade-offs between energy efficiency and thermal profile. Specifically, a fast thermal model for the cooling network is proposed and analyzed, followed by our optimization methodologies to construct cooling networks targeting at pumping power saving and thermal gradient reduction, respectively. Experimental results show that, under the same constraints, the cooling network can save as much as 84.03% pumping power or reduce 37.65% thermal gradient compared to straight microchannels.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062285"}, {"primary_key": "3751758", "vector": [], "sparse_vector": [], "title": "Optimizing Memory Efficiency for Convolution Kernels on Kepler GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON>"], "summary": "Convolution is a fundamental operation in many applications, such as computer vision, natural language processing, image processing, etc. Recent successes of convolutional neural networks in various deep learning applications put even higher demand on fast convolution. The high computation throughput and memory bandwidth of graphics processing units (GPUs) make GPUs a natural choice for accelerating convolution operations. However, maximally exploiting the available memory bandwidth of GPUs for convolution is a challenging task. This paper introduces a general model to address the mismatch between the memory bank width of GPUs and computation data width of threads. Based on this model, we develop two convolution kernels, one for the general case and the other for a special case with one input channel. By carefully optimizing memory access patterns and computation patterns, we design a communication-optimized kernel for the special case and a communication-reduced kernel for the general case. Experimental data based on implementations on Kepler GPUs show that our kernels achieve 5.16x and 35.5% average performance improvement over the latest cuDNN library, for the special case and the general case, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062297"}, {"primary_key": "3751759", "vector": [], "sparse_vector": [], "title": "Enabling Write-Reduction Strategy for Journaling File Systems over Byte-addressable NVRAM.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-volatile random-access memory (NVRAM) becomes a mainstream storage device in embedded systems due to its favorable features, such as small size, low power consumption, and short read/write latency. On NVRAM, a write operation consumes more energy and time than a read operation. However, current mobile/embedded file systems (e.g., EXT2/3 and EXT4) are very unfriendly for NVRAM devices. The reason is that a journaling mechanism writes the same data twice during data commitment and checkpoint. Such observations motivate this paper to design a two-phase write reduction journaling file system called wrJFS. In the first phase, wrJFS classified data into two categories: Metadata and user data. Metadata will be handled by partial byte-enabled journaling strategy, and user data will be processed in the second phase. In the second phase, user data will be compressed by hardware encoder so as to reduce the write size, and managed compressed-enabled journaling strategy to avoid the write amplification. The experimental results show that the proposed wrJFS can reduce the size of the write request by 89.7% on average, compared with the original EXT3.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062236"}, {"primary_key": "3751760", "vector": [], "sparse_vector": [], "title": "VirtualGC: Enabling Erase-free Garbage Collection to Upgrade the Performance of Rewritable SLC NAND Flash Memory.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since 3D NAND flash memory could provide more reliable storage than a 2D planar flash memory by relaxing the design rule of a memory cell, a kind of brand new programming technique, namely erase-free scheme, has been proposed to further enhance the endurance of a 3D SLC NAND flash memory. The erase-free scheme brings tons of benefits to flash memory performance and endurance. For example, the erase-free scheme could reclaim invalid (page) space without physically erasing a flash block. However, current flash management designs could not fully exploit the benefits of the erase-free scheme. With the considerations of the features of the erase-free scheme, this paper is the first work to propose a novel flash management design, namely VirtualGC strategy, to deal with the erase-free garbage collection process. By taking the advantages of the erase-free scheme, the proposed strategy reduces the overhead of copying live pages so as to increase flash memory performance. The results show that the proposed strategy significantly improves the performance of rewritable 3D flash memory drives.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062339"}, {"primary_key": "3751761", "vector": [], "sparse_vector": [], "title": "Boosting the Performance of 3D Charge Trap NAND Flash with Asymmetric Feature Process Size Characteristic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The growing demands of large capacity fash-based storages have facilitated the down-scaling process of NAND fash memory. Among NAND fash technologies, 3D charge trap fash is regarded as one of the most promising candidates. Owing to the cylindrical geometry of vertical channels, the access performance of each page in one block is distinctive, and this situation is exaggerated in the 3D charge trap fash with the fast-growing number of layers. In this study, a progressive performance boosting strategy is proposed to boost the performance of 3D charge trap fash by utilizing its asymmetric page access speed feature. A series of experiments was conducted to demonstrate the capability of the proposed strategy on improving access performance of 3D charge trap flash.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062209"}, {"primary_key": "3751762", "vector": [], "sparse_vector": [], "title": "Minimizing Cluster Number with <PERSON><PERSON> in Hotspot Pattern Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the rapid advance of semiconductor process technologies, layout features in integrated circuits (ICs) become highly prone to process variations. Lithography hotspots are a set of problematic layout patterns with poor printability even if they pass design rule checking (DRC). These hotspots need to be detected and fixed as early as possible in the design flow to improve manufacturability and yield. While most of existing studies focus on hotspot detection, hotspot pattern classification is rarely addressed but plays an important role in determining the efficiency of hotspot detection. In hotspot pattern classification, similar hotspots are classified into a cluster with tolerance constraints, and a representative hotspot is chosen for each cluster for future application. To minimize the problem size of subsequent hotspot detection, the number of clusters, and thus the number of representative hotspots, should be minimized. In this paper, a clip shifting method is adopted to further reduce the cluster number during hotspot classification. We propose a two-stage pattern matching algorithm flow and derive optimal solutions by solving a set cover problem. Experiment results show that our flow can reduce the cluster number by about 30% compared to reference results in 2016 CAD Contest at ICCAD.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062283"}, {"primary_key": "3751763", "vector": [], "sparse_vector": [], "title": "Optimal Circuits for Parallel Bit Reversal.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we develop novel parallel circuit designs for calculating the bit reversal. To perform bit reversal on 2n data words, the designs take 2k (k <n) words as input each cycle. The circuits consist of concatenated single-port buffers and 2-to-1 multiplexers and use minimum number of registers for control. The designs consume minimum number of single-port memory banks that are necessary for calculating continuous-flow bit reversal, as well as near optimal 2™ memory words. The proposed parallel circuits can be built for any given fixed k and n, and achieve superior performance over state-of-the-art for calculating the bit reversal in parallel multi-path FFT architectures.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062295"}, {"primary_key": "3751764", "vector": [], "sparse_vector": [], "title": "Toward Optimal Legalization for Mixed-Cell-Height Circuit Designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern circuits often contain standard cells of different row heights to meet various design requirements. Higher cells give larger drive strengths at the costs of larger areas and power. Multi-row-height standard cells incur challenging issues to layout designs, especially the mixed-cell-height legalization problem due to the heterogeneous cell structures. Honoring the good cell positions from global placement, we present in this paper a fast and near-optimal algorithm to solve the legalization problem. Fixing the cell ordering from global placement and relaxing the right boundary constraints, we first convert the problem into a linear complementarity problem (LCP). With the converted LCP, we split its matrices to meet the convergence requirement of a modulus-based matrix splitting iteration method (MMSIM), and then apply the MMSIM to solve the LCP. This MMSIM method guarantees the optimality if no cells are placed beyond the right boundary of a chip. Finally, a Tetris-like allocation approach is used to align cells to placement sites on rows and fix the placement of out-of-right-boundary cells, if any. Experimental results show that our proposed algorithm can achieve the best cell displacement and wirelength among all published methods in reasonable runtimes. The MMSIM optimality is theoretically proven and empirically validated. In particular, our formulation provides new generic solutions and research directions for various optimization problems that require solving large-scale quadratic programs efficiently.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062330"}, {"primary_key": "3751765", "vector": [], "sparse_vector": [], "title": "TIME: A Training-in-memory Architecture for Memristor-based Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ai", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The training of neural network (NN) is usually time-consuming and resource intensive. Memristor has shown its potential in computation of NN. Especially for the metal-oxide resistive random access memory (RRAM), its crossbar structure and multi-bit characteristic can perform the matrix-vector product in high precision, which is the most common operation of NN. However, there exist two challenges on realizing the training of NN. Firstly, the current architecture can only support the inference phase of training and cannot perform the backpropagation (BP), the weights update of NN. Secondly, the training of NN requires enormous iterations and constantly updates the weights to reach the convergence, which leads to large energy consumption because of lots of write and read operations. In this work, we propose a novel architecture, TIME, and peripheral circuit designs to enable the training of NN in RRAM. TIME supports the BP and the weights update while maximizing the reuse of peripheral circuits for the inference operation on RRAM. Meanwhile, a variability-free tuning scheme and gradually-write circuits are designed to reduce the cost of tuning RRAM. We explore the performance of both SL (supervised learning) and DRL (deep reinforcement learning) in TIME, and a specific mapping method of DRL is also introduced to further improve the energy efficiency. Experimental results show that, in SL, TIME can achieve 5.3x higher energy efficiency on average compared with the most powerful application-specific integrated circuits (ASIC) in the literature. In DRL, TIME can perform averagely 126x higher than GPU in energy efficiency. If the cost of tuning RRAM can be further reduced, TIME have the potential of boosting the energy efficiency by 2 orders of magnitude compared with ASIC.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062326"}, {"primary_key": "3751766", "vector": [], "sparse_vector": [], "title": "Power and Area Efficient Hold Time Fixing by Free Metal Segment Allocation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>en<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Hold time fixing ensures correct data synchronization, which is essential and serves as the final step of timing closure for IC design. Conventionally, buffer insertion is adopted to fix hold time violations; buffers, however, induce routing difficulty, increase area utilization, and contribute leakage power. Therefore, in this paper, we propose to fix hold time violations by free metal segment allocation for achieving leakage power efficiency and maintaining utilization for mobile and portable devices. At the final step of timing closure, free metal segments and hold violating nets are both fragmented and scattered over the design. We thus partition a design and perform minimum cost network flow to assign proper free metal segments to hold violating nets. Our experiments are conducted on six industrial smartphone designs with TSMC 16nm process, and our results show that compared with the conventional buffer insertion method, our approach can reduce 37% hold time buffer area, promising for saving leakage power and maintaining area utilization---suited to the final step of timing closure.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062303"}, {"primary_key": "3751767", "vector": [], "sparse_vector": [], "title": "Instruction-Level Data Isolation for the Kernel on ARM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As more sophisticated services are increasingly offered by the OS kernel on mobile devices, the security and sensitivity of kernel data that they depend on are becoming a critical issue. Data isolation has emerged as a key technique that can address the issue by providing strong protection for sensitive kernel data. However, existing data isolation mechanisms for mobile devices all incur non-negligible performance overhead. We deem that such computational burden would be a serious problem for mobile devices which already suffer from resource poverty. To alleviate this problem, we have developed a new mechanism that enforces data isolation very efficiently on ARM-based machines backed by unique hardware instructions. For evaluation, this instruction-level data isolation mechanism has been implemented in the Android/Linux kernel running on ARM. According to the experiment, it provides a lightweight data isolation capability for security services installed in the kernel.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062267"}, {"primary_key": "3751768", "vector": [], "sparse_vector": [], "title": "Bandwidth Optimization Through On-Chip Memory Restructuring for HLS.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "High-level synthesis (HLS) is getting increasing attention from both academia and industry for high-quality and high-productivity designs. However, when inferring primitive-type arrays in HLS designs into on-chip memory buffers, commercial HLS tools fail to effectively organize FPGAs' on-chip BRAM building blocks to realize high-bandwidth data communication; this often leads to sub-optimal quality of results. This paper addresses this issue via automated on-chip buffer restructuring. Specifically, we present three buffer restructuring approaches and develop an analytical model for each approach to capture its impact on performance and resource consumption. With the proposed model, we formulate the process of identifying the optimal design choice into an integer non-linear programming (INLP) problem and demonstrate that it can be solved efficiently with the help of a one-time C-to-HDL (hardware description language) synthesis. The experimental results show that our automated source-to-source code transformation tool improves the performance of a broad class of HLS designs by averagely 4.8x.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062208"}, {"primary_key": "3751769", "vector": [], "sparse_vector": [], "title": "A-TEAM: Automatic template-based assertion miner.", "authors": ["<PERSON>", "<PERSON><PERSON>ò <PERSON> R<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Different mining approaches have been proposed in literature for the automatic generation of temporal assertions from execution traces of digital systems. However, in most cases, existing tools can only mine assertions compliant with a limited set of pre-defined templates. Furthermore, they tend to generate a huge amount of assertions, while they still lack an effective way to measure their coverage in terms of design behaviours. To fill in the gap, this paper presents A-TEAM, a tool for the automatic extraction of temporal assertions starting from a set of user-defined assertion templates. Our method involves a combination of data mining and coverage analysis for mining a compact and expressive set of LTL formulas.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062206"}, {"primary_key": "3751770", "vector": [], "sparse_vector": [], "title": "In Quest of the Next Information Processing Substrate: Extended Abstract: Invited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "H.<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional CMOS scaling and the <PERSON>'s law have been the cornerstone of progress in computing hardware technology. However, with dimensional scaling expected to end soon, there is a pressing need to find the next information processing hardware that can continue to support the technology revolution. Will this hardware solution be an enhanced or an augmented version of MOSFET or a switch based on a radically new switching mechanism. Ultimately, do we require a complete deviation from the Boolean paradigm itself? In this invited paper, we will review some of the actively pursued future logic, merged logic-memory and related concepts.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072953"}, {"primary_key": "3751771", "vector": [], "sparse_vector": [], "title": "Low-Power On-Chip Network Providing Guaranteed Services for Snoopy Coherent and Artificial Neural Network Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "During the transition to packet-switched on-chip networks we lose the relative timing and ordering of requests, which are essential for shared memory coherency and the communication of spikes in hardware-based artificial neural networks. We present a bufferless network architecture that enforces a time-based sharing of multi-hop single-cycle paths, providing guaranteed services at low cost. We guarantee ordered delivery of requests, fixed network latency, and jitter-free neural spikes. In a 64-node network, we achieve a 84% lower latency and 7.5x higher throughput than SCORPIO. Full-system 36-core simulations show a 9% lower runtime than SCORPIO, with 39% lower power and 36% lower area.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062278"}, {"primary_key": "3751772", "vector": [], "sparse_vector": [], "title": "Vertical M1 Routing-Aware Detailed Placement for Congestion and Wirelength Reduction in Sub-10nm Nodes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aggressive pitch scaling in sub-10nm nodes has introduced complex design rules which make routing extremely challenging. Cell architectures have also been changed to meet the design rules. For example, metal layers below M1 are used to gain additional routing resources. New cell architectures wherein inter-row M1 routing is allowed force consideration of vertical alignment of cells. In this work, we propose a mixed-integer linear programming (MILP)-based, detailed placement optimization to maximize direct vertical M1 routing utilization for congestion and wirelength reduction.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062338"}, {"primary_key": "3751773", "vector": [], "sparse_vector": [], "title": "LO-FAT: Low-Overhead Control Flow ATtestation in Hardware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>. <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Attacks targeting software on embedded systems are becoming increasingly prevalent. Remote attestation is a mechanism that allows establishing trust in embedded devices. However, existing attestation schemes are either static and cannot detect control-flow attacks, or require instrumentation of software incurring high performance overheads. To overcome these limitations, we present LO-FAT, the first practical hardware-based approach to control-flow attestation. By leveraging existing processor hardware features and commonly-used IP blocks, our approach enables efficient control-flow attestation without requiring software instrumentation. We show that our proof-of-concept implementation based on a RISC-V SoC incurs no processor stalls and requires reasonable area overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062276"}, {"primary_key": "3751774", "vector": [], "sparse_vector": [], "title": "InCheck: An In-application Recovery Scheme for Soft Errors.", "authors": ["Mosle<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An ideal solution for soft error tolerance should hide the effect of soft errors from user and provide correct results at expected time. Software solutions are attractive because they can provide flexible reliability without imposing any hardware modifications. Our investigation of state-of-the-art error recovery techniques reveals that they suffer from poor coverage (ability to detect and correctly recover from soft errors). This paper presents InCheck (In-application Checkpointing and Recovery) as an effective, safe and timely software technique for complete error coverage. The key features of InCheck are: verified register preservation, single memory location checkpoints, and safe & timely recovery. To evaluate the effectiveness of InCheck, we performed more than 210,000 fault injection experiments on different hardware components of an ARM cortex53-like processor running MiBench applications. The original and SWIFTR (state-of-the-art) protected programs suffered from 8000 and 1800 instances of wrong outputs respectively, but when protected by InCheck, there was no failure.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062265"}, {"primary_key": "3751775", "vector": [], "sparse_vector": [], "title": "Reducing LDPC Soft Sensing Latency by Lightweight Data Refresh for Flash Read Performance Improvement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Deqing Zou", "<PERSON>", "<PERSON>"], "summary": "In order to relieve reliability problem caused by technology scaling, LDPC codes have been widely applied in flash memories to provide high error correction capability. However, LDPC read performance slowdown along with data retention largely weakens the access speed advantage of flash memories. This paper considers to apply the concept of refresh, that were used for flash lifetime improvement, to optimize flash read performance. Exploiting data read characteristics, this paper proposes LDR, a lightweight data refresh method, that aggressively corrects errors in read-hot pages with long read latency and reprograms error-free data into new pages. Experimental results show that LDR can achieve 29% read performance improvement with only 0.2% extra P/E cycles on average, which causes negligible overhead on flash lifetime.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062309"}, {"primary_key": "3751776", "vector": [], "sparse_vector": [], "title": "Advances in Formal Methods for the Design of Analog/Mixed-Signal Systems: Invited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Analog/mixed-signal (AMS) systems are rapidly expanding in all domains of information and communication technology. They are a critical part of the support for large-scale high-performance digital systems, provide important functionalities in medium-scale embedded and mobile systems, and act as a core organ of autonomous electronics such as sensor nodes. Analog and digital parts are closely intermixed, hence demanding AMS design methods and tools to be more holistic. In particular, the emergence of \"little digital\" electronics inside or near analog circuitry calls for the increasing use of asynchronous logic. To cope with the growing complexity of AMS designs, formal methods are required to complement traditional simulation approaches. This paper presents an overview of the state-of-the-art in AMS formal verification and asynchronous design that enables the development of analog/asynchronous co-design methods. One such co-design methodology is exemplified by the LEMA-Workcraft workflow currently under development by the authors.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072945"}, {"primary_key": "3751777", "vector": [], "sparse_vector": [], "title": "Accelerating Graph Community Detection with Approximate Updates via an Energy-Efficient NoC.", "authors": ["<PERSON><PERSON><PERSON>", "Hao Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Community detection is an advanced graph operation that is used to reveal tightly-knit groups of vertices (aka. communities) in real-world networks. Given the intractability of the problem, efficient heuristics are used in practice. Yet, even the best of these state-of-the-art heuristics can become computationally demanding over large inputs and can generate workloads that exhibit inherent irregularity in data movement on manycore platforms. In this paper, we posit that effective acceleration of the graph community detection operation can be achieved by reducing the cost of data movement through a combined innovation at both software and hardware levels. More specifically, we first propose an efficient software-level parallelization of community detection that uses approximate updates to cleverly exploit a diminishing returns property of the algorithm. Secondly, as a way to augment this innovation at the software layer, we design an efficient Wireless Network on Chip (WiNoC) architecture that is suited to handle the irregular on-chip data movements exhibited by the community detection algorithm under both unicast- and broadcast-heavy cache coherence protocols. Experimental results show that our resulting WiNoC-enabled manycore platform achieves on average 52% savings in execution time, without compromising on the quality of the outputs, when compared to a traditional manycore platform designed with a wireline mesh NoC and running community detection without employing approximate updates.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062194"}, {"primary_key": "3751778", "vector": [], "sparse_vector": [], "title": "Estimation of Safe Sensor Measurements of Autonomous System Under Attack.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The introduction of automation in cyber-physical systems (CPS) has raised major safety and security concerns. One attack vector is the sensing unit whose measurements can be manipulated by an adversary through attacks such as denial of service and delay injection. To secure an autonomous CPS from such attacks, we use a challenge response authentication (CRA) technique for detection of attack in active sensors data and estimate safe measurements using the recursive least square algorithm. For demonstrating effectiveness of our proposed approach, a car-follower model is considered where the follower vehicle's radar sensor measurements are manipulated in an attempt to cause a collision.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062241"}, {"primary_key": "3751779", "vector": [], "sparse_vector": [], "title": "Adaptation of Enhanced TSV Capacitance as Membrane Property in 3D Brain-inspired Computing System.", "authors": ["<PERSON><PERSON>", "Hongyu An", "<PERSON><PERSON>", "<PERSON>"], "summary": "Neurophysiological architecture using 3D integration technology offers a high device interconnection density as well as fast and energy efficient links among the neuron and synapses layers. In this paper, we propose to reconfigure the Through-Silicon-Vias (TSVs) to serve as the neuronal membrane capacitors that map the membrane electrical activities in a hybrid 3D neuromorphic system. We also investigate new methodology that could significantly enhance the TSV capacitance to achieve a high efficiency of signal processing through membrane. An optimal CAD framework is designed to optimally utilize such TSV devices, and resolve the signal-integrity issues arising at fast data rates during massively parallel data transmissions. The electrical performance of the 3D neuromorphic chip is compared against the ones of the 2D counterpart design to demonstrate the advantages of our design and methodology.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062196"}, {"primary_key": "3751780", "vector": [], "sparse_vector": [], "title": "A Clock Tree Optimization Framework with Predictable Timing Quality.", "authors": ["<PERSON><PERSON>"], "summary": "Eliminating timing violations using clock tree optimization (CTO) persist to be a tedious problem in ultra scaled technologies. State-of-the-art CTO techniques are based on predicting the final timing quality by specifying a set of delay adjustments in the form of delay adjustment points (DAPs). Next, the DAPs are realized to eliminate the timing violations. Unfortunately, it is difficult to realize delay adjustments of exact magnitudes. In this paper, the correlation between the predicted and achieved timing quality is improved by specifying delay adjustments in the form of delay adjustment ranges (DARs). The DARs are formed such that the predicted timing quality is achieved if each delay adjustment is realized within the respective DAR. The framework first predicts the final timing quality. Next, the DARs are specified and optimized while treating the predicted timing quality as a constraint. The optimization is a trade-off between the ease of delay adjustment realization, the total amount of delay adjustment, and the number of delay adjustments. Moreover, the framework accounts for delay adjustment induced on-chip variations (OCV) and transition time constraints. On a set of synthesized circuits, it is demonstrated that the framework improves the correlation between the predicted and achieved timing quality compared with in earlier studies. The average total negative slack (TNS) and worst negative slack (WNS) are improved by 91% and 85%, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062184"}, {"primary_key": "3751781", "vector": [], "sparse_vector": [], "title": "Secure Information Flow Verification with Mutable Dependent Types.", "authors": ["<PERSON>", "<PERSON>z<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a novel secure hardware description language (HDL) that uses an information flow type system to ensure that hardware is secure at design time. The novelty of this HDL lies in its ability to securely share hardware modules and storage elements across multiple security levels. Unlike previous secure HDLs, the new HDL enables secure sharing at a fine granularity and without implicitly adding hardware for security enforcement; this is important because the implicitly added hardware can break functionality and harm efficiency. The new HDL enables practical hardware designs that are secure, correct, and efficient. We demonstrate the practicality of the new HDL by using it to design and type-check a synthesizable pipelined processor implementation that support protection rings and instructions that change modes.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062316"}, {"primary_key": "3751782", "vector": [], "sparse_vector": [], "title": "Template Aware Coverage: Taking Coverage Analysis to the Next Level.", "authors": ["<PERSON><PERSON>", "Einat Kermany", "Bilal <PERSON>h", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding the relationship between coverage and test-templates (a generic term we use to describe the inputs for the random stimuli generator) is an important layer in understanding the state and progress of the verification process. Today, this is extremely hard to achieve and is based on expert knowledge. Template Aware Coverage (TAC) is a novel approach to meeting this challenge. Based on collecting statistics of the relations between coverage and test-templates, TAC maintains these statistics in efficient data structures. It also introduces analytics means to provide useful information based on this data. Template Aware Coverage is currently being used in the verification of a high-end processor systems, where it significantly helps hitting hard-to-hit coverage events as well as never hit events.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062324"}, {"primary_key": "3751783", "vector": [], "sparse_vector": [], "title": "On Characterizing Near-Threshold SRAM Failures in FinFET Technology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Adoption of near-threshold voltage (NTV) operation in SRAM-based memories has been limited by reduced robustness resulting from marginal transistor operation that results in bit failures. Using silicon measurements from a large sample of 14nm FinFET test chips, we show that our cells operate at frequencies of up to 1GHz with a minimum 15% voltage guardband, below which the cells begin to fail. We find that when operated at 32.5% below nominal voltage, >95% of the lines experience fewer than 2 failures, which can be corrected with SECDED ECC. Our results indicate that for frequencies of up to 1GHz, NTV can help maximize power savings potential while requiring minimal protection.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062292"}, {"primary_key": "3751784", "vector": [], "sparse_vector": [], "title": "Error Propagation Aware Timing Relaxation For Approximate Near Threshold Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Near threshold computing (NTC) through aggressive supply voltage scaling has the potential to significantly improve energy-efficiency. However, the increase in variation-induced timing errors is a major challenge in NTC. This can be addressed in the scope of approximate computing by selectively embracing non-important variation-induced timing errors. In this paper, we propose a framework to leverage the error tolerance potential of approximate computing for energy-efficient NTC designs. In our framework, statistical timing error analysis as well as structural and functional error propagation analysis is performed to identify the approximable portion of a design. Then, a mixed-timing logic synthesis is employed to improve energy-efficiency by embracing errors in the approximable portion of the design. Experimental results show that the proposed approach can improve the energy-efficiency of NTC designs by more than 30%.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062240"}, {"primary_key": "3751785", "vector": [], "sparse_vector": [], "title": "A New Paradigm for Synthesis of Linear Decompressors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For more than two decades, the key objective for synthesis of linear decompressors has been maximizing encoding efficiency. For combinational decompressors, encoding satisfiability is dynamically checked for each specified care bit. By contrast, for sequential linear decompressors (e.g. PRPGs), encoding is performed for each test cube; the resultant static encoding considers that a test cube is encodable only if all of its care bits are encodable. The paper introduces a new class of sequential linear decompressors that provides a trade-off between the computational complexity and the encoding efficiency of linear encoding. As a result, it becomes feasible to dynamically encode care bits before a test cube has been completed, and derive decompressor-implied scan cell values during test generation. The resultant dynamic encoding enables an identification of encoding conflicts during branch-and-bound search and a reduction of search space for dynamic compaction. Experimental results demonstrate that dynamic encoding consistently outperforms static encoding in a wide range of compression ratios.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062190"}, {"primary_key": "3751786", "vector": [], "sparse_vector": [], "title": "Cooperative DVFS for energy-efficient HEVC decoding on embedded CPU-GPU architecture.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The next generation video coding standard High Efficiency Video Coding (HEVC) provides better compression rate for high resolution videos, at the cost of substantially higher computational complexity. While some latest off-the-shelf consumer electronics support HEVC via ASIC solutions, software implementation of real-time HEVC remains an open challenge for resource-constraint embedded systems. In this work, we present an HEVC decoder design on a low-power embedded heterogeneous multiprocessor System-on-Chip (HMPSoC) with CPU and GPU. Our analysis shows that the massive parallel architecture of GPU leads to a relatively smooth fluctuation on the processing time between video frames. Moreover, the dynamic workload of each frame has a monotonic correlation with a particular coding parameter that can be obtained at decoding time. Based on these observations, we propose an application-specific userspace CPU-GPU DVFS scheme which effectively saves the energy consumption for HEVC decoding. Furthermore, given our accurate workload prediction, only a small frame buffer is required to ensure real-time video decoding.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062216"}, {"primary_key": "3751787", "vector": [], "sparse_vector": [], "title": "A Discrete Model for Networked Labs-on-Chips: Linking the Physical World to Design Automation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Labs-on-Chip integrate and minimize the functionality of complete conventional laboratories on a single chip. An upcoming and especially biocompatible realization are Networked Labs-on-Chips (NLoCs). In NLoCs, small volumes of reagents, so-called droplets, flow in an immiscible fluid in closed channels. An external pump applies a force to this immiscible fluid driving the droplets through the channels of the NLoC. However, the exact flow behavior of droplets in NLoCs physically depends on many factors and interdependencies. This makes it cumbersome to manually determine the taken path of a droplet and the time it needs to pass the NLoC. For the same reason, also almost no automated design solutions exist for NLoCs yet. In this work, we present a discrete model enabling designers and design automation tools to efficiently determine the droplets' path and positions. The precision of the proposed model is evaluated by a systematic examination for basic building blocks of NLoCs as well as for a complete architecture. The resulting model can be used for manual inspections of the droplets' behavior in an NLoC and, additionally, provides the basis for automated design solutions.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062186"}, {"primary_key": "3751788", "vector": [], "sparse_vector": [], "title": "Stress-Aware Loops Mapping on CGRAs with Considering NBTI Aging Effect.", "authors": ["Jiangyuan Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the process scaling into nano-scale VLSI technology, the increasingly serious aging issues (e.g. NBTI aging effect) bring a significant threat to system reliability. Coarsegrained reconfigurable architectures (CGRAs) exhibit the feature to reconfigure different mapping schemes (Maps) dynamically during loops execution, which can mitigate the aging issues on CGRAs effectively. In this paper, we propose a stress-aware loops mapping algorithm by jointing intra-kernel and inter-kernel stress optimizations strategies in the early phase of CGRA-mapped designs. With the pipelining technique, a stress-aware force-directed method is introduced in the intra-kernel optimization, avoiding many operations to be mapped on some certain PEs and reducing the stresses accumulated on them. By leveraging the dynamic reconfiguration, a multi-map scheduling method is proposed in the inter-kernel stress optimization to find a set of ordered maps to reconfigure dynamically, which diversifies PE usages and compensates for the accumulated stresses on different PEs among them. Experimental results show our proposed approach enlarges the maximum stress reduction up to 78.9% and improves the MTTF by 340.3% on average while keeping the optimized performance.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062322"}, {"primary_key": "3751789", "vector": [], "sparse_vector": [], "title": "FFD: A Framework for Fake Flash Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Domenic Forte"], "summary": "Counterfeit electronics have become a big concern in the globalized semiconductor industry where chips might be recycled, remarked, cloned or overproduced. In this work, we advance the state-of-the-art counterfeit detection of flash memory, which is widely used in electronic systems. Fake memories may be used in critical systems, such as missiles, military aircrafts and helicopters, thus diminishing their reliability. In addition, there are countless stories of fake flash drives in the general consumer market. We propose a comprehensive framework called FFD to detect fake flash memories (i.e., recycled, remarked and cloned parts). FFD is validated with 200,000 commercial flash memory pages. Experimental results show that our framework performs well in: 1) nearly 100% detection accuracy of flash with as little as 5% usage, 2) estimating the flash memory usage with high resolution (≤ 5% of its maximal endurance). Another contribution of this work is a chip ID generation technique that can generate unique flash fingerprints with greater than 99.3% reliability.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062249"}, {"primary_key": "3751790", "vector": [], "sparse_vector": [], "title": "ObfusCADe: Obfuscating Additive Manufacturing CAD Models Against Counterfeiting: Invited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Nektar<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As additive manufacturing (AM) becomes more pervasive, its supply chains shift towards distributed business models that heavily rely on cloud resources. Despite its countless benefits, this paradigm raises significant concerns about the trustworthiness of the globalized process, as there exist several classes of cybersecurity attacks that can undermine its security guarantees. In this work, we focus on the protection of the intellectual property (IP) of 3D designs, and introduce ObfusCADe, which is a novel protection method against counterfeiting, by embedding special features in CAD models. The introduced features interfere with the integrity of the design, effectively restricting high quality manufacturing to only a unique set of processing settings and conditions; under all other conditions, the printed artifact suffers from poor quality, premature failures and/or malfunctions.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3079847"}, {"primary_key": "3751791", "vector": [], "sparse_vector": [], "title": "Compiler Techniques to Reduce the Synchronization Overhead of GPU Redundant Multithreading.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Redundant Multi-Threading (RMT) provides a potentially low cost mechanism to increase GPU reliability by replicating computation at the thread level. Prior work has shown that RMT's high performance overhead stems not only from executing redundant threads, but also from the synchronization overhead between the original and redundant threads. The overhead of inter-thread synchronization can be especially significant if the synchronization is implemented using global memory. This work presents novel compiler techniques using fingerprinting and cross-lane operations to reduce synchronization overhead for RMT on GPUs. Fingerprinting combines multiple synchronization events into one event by hashing, and cross-lane operations enable thread-level synchronization via register-level communication. This work shows that fingerprinting yields a 73.5% reduction in GPU RMT overhead while cross-lane operations reduce the overhead by 43% when compared to the state-of-the-art GPU RMT solutions on real hardware.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062212"}, {"primary_key": "3751792", "vector": [], "sparse_vector": [], "title": "A Heterogeneous SDR MPSoC in 28 nm CMOS for Low-Latency Wireless Applications.", "authors": ["<PERSON>", "<PERSON>", "Benedikt <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Current and future applications impose high demands on software-defined radio (SDR) platforms in terms of latency, reliability, and flexibility. This paper presents a heterogeneous SDR MPSoC with a hexagonal network-on-chip to address these issues. It features four data processing modules and a baseband processing engine for iterative multiple-input multiple-output (MIMO) receiving. Integrated memory controllers enable dynamic data flow mapping and application isolation. In a 4 x 4 MIMO application scenario, the MPSoC achieves a throughput of 232 Mbit/s with a latency of 20 μs while consuming 414 mW. It outperforms state-of-the-art platforms in terms of throughput by a factor of 4.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062188"}, {"primary_key": "3751793", "vector": [], "sparse_vector": [], "title": "QuAd: Design and Analysis of Quality-Area Optimal Low-Latency Approximate Adders.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Approximate circuits exploit error resilience property of applications to tradeoff computation quality (accuracy) for gaining advantage in terms of performance, power, and/or area. While state-of-the-art low-latency approximate adders provide an accuracy-area-latency configurable design space, the selection of a particular configuration from the design space is still manually done. In this paper, we analytically analyze different structural properties of low-latency approximate adders to formulate a new adder model, Quality-area optimal Low-Latency approximate Adder (QuAd). It provides an increased design space as compared to state-of-the-art, providing design points that require less logic area for the same accuracy, as compared to state-of-the-art approximate adders. Furthermore, based upon our mathematical analysis, we show that, provided a latency constraint, an adder configuration with the highest quality and lowest area requirement can effortlessly be selected from the whole design space of QuAd adder model, without requiring any optimization strategy or numerical simulation. Our experimental results validate the developed model and also the quality-area optimality of our optimal QuAd adder configuration. For functional verification and prototyping, we have used a Xilinx Virtex-6 FPGA. RTL/behavioral models and MATLAB equivalent scripts, of our proposed adder model are made open source, to facilitate further research and development.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062306"}, {"primary_key": "3751794", "vector": [], "sparse_vector": [], "title": "No-Jump-into-Basic-Block: Enforce Basic Block CFI on the Fly for Real-world Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Code-reuse attack is a growing threat to computing systems as it can circumvent existing security defenses. Fortunately, control flow integrity (CFI) is promising in defending such attack. However, former implementations generally suffer from two major drawbacks: 1) complex pre-processing to obtain control flow graph; 2) high overhead. In this paper, we propose a cross-layer approach that employs basic block information inside the binary code and read-only data to enforce fine-grained control-flow integrity. Our approach demonstrates high applicability and thorough attack detection coverage without static analysis or recompilation. Meanwhile, it can effectively protect even striped programs, while incurring negligible 0.13% performance overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062291"}, {"primary_key": "3751795", "vector": [], "sparse_vector": [], "title": "Real-Time Multi-Scale Pedestrian Detection for Driver Assistance Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pedestrian detection is one of the most challenging and vital tasks of driver assistance systems (DAS). Among several algorithms developed for human detection, histogram of oriented gradients (HOG) followed by support vector machine (SVM) has shown the most promising results. This paper presents a hardware accelerator for real-time pedestrian detection at different scales to fulfill the real-time requirements of DAS. It proposes an algorithmic modification to the conventional multi-scale object detection by means of HOG+SVM to increase the throughput and maintain the accuracy reasonably high. Our hardware accelerator detects pedestrians at the rate of 60 fps for HDTV (1080x1920) frame.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062308"}, {"primary_key": "3751796", "vector": [], "sparse_vector": [], "title": "3 Channel Dependency-Based Power Model for Mobile AMOLED Displays.", "authors": ["Se<PERSON>wo<PERSON> Hong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Active matrix organic light-emitting diode (AMOLED) displays are being adopted in increasing number of smartphones. Most applications are now based on consistent user interfaces, such as games and instant messaging, and it is therefore crucial to efficiently manage the power consumption of the AMOLED display. To this end, an accurate power model for this type of display is needed. The prior work available in the literature did not consider the dependencies between the red, green, and blue (RGB) channels, or only accounted for limited dependencies between the channels. In this paper, we develop a novel accurate power model for AMOLED displays that considers all possible dependencies between the three channels. We applied this model to four panels from the Galaxy S1, Galaxy S3, and Galaxy S5 from SAMSUNG, and a CHIMEI panel with different customizations. Using four well-known image datasets, the proposed model shows that the smallest error rate is on average 1.13% for the Galaxy S5 panel, while both the simple model and the existing dependency models represent average error rates of 9.69% and 5.53%, respectively. As a result, the proposed model is shown to be correct and generally usable. In these tests, this model was implemented using the vector floating point support of the Cortex-A9 on an Android smartphone at the operating system level. The computation time required for each test image that had a resolution of 720 x 1280 was 3 seconds.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062181"}, {"primary_key": "3751797", "vector": [], "sparse_vector": [], "title": "Hierarchical Dataflow Modeling of Iterative Applications.", "authors": ["<PERSON><PERSON><PERSON>", "Hyunok Oh", "<PERSON><PERSON><PERSON>"], "summary": "Even though dataflow models are good at exploiting task-level parallelism of an application, it is difficult to exploit the parallelism of loop structures since they are not explicitly specified in existent dataflow models. To overcome this drawback, we propose a novel extension to the SDF model, called SDF/L graph, specifying the loop structures explicitly in a hierarchical fashion. With a given SDF/L graph specification and the mapping and scheduling information, an application can be automatically parallelized on a multicore system. The enhanced expression capability by the proposed extension is verified with two applications, k-means clustering and deep neural network application.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062260"}, {"primary_key": "3751798", "vector": [], "sparse_vector": [], "title": "Leveraging Compiler Optimizations to Reduce Runtime Fault Recovery Overhead.", "authors": ["Fate<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guang R. <PERSON>"], "summary": "Smaller feature size, lower supply voltage, and faster clock rates have made modern computer systems more susceptible to faults. Although previous fault tolerance techniques usually target a relatively low fault rate and consider error recovery less critical, with the advent of higher fault rates, recovery overhead is no longer negligible. In this paper, we propose a scheme that leverages and revises a set of compiler optimizations to design, for each application hotspot, a smart recovery plan that identifies the minimal set of instructions to be re-executed in different fault scenarios. Such fault scenario and recovery plan information is efficiently delivered to the processor for runtime fault recovery. The proposed optimizations are implemented in LLVM and GEM5. The results show that the proposed scheme can significantly reduce runtime recovery overhead by 72%.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062273"}, {"primary_key": "3751799", "vector": [], "sparse_vector": [], "title": "Learning to Produce Direct Tests for Security Verification Using Constrained Process Discovery.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Security verification relies on using direct tests manually prepared. Test preparation often requires intensive efforts from experts with in-depth domain knowledge. This work presents an approach to learn from direct tests written by an expert. After the learning, the learned model acts as a surrogate for the expert to produce new tests. The learning software comprises a database for accumulating and sharing security verification knowledge. The learning approach uses process discovery to build an upper-bound model and continuously adds constraints to refine it. We demonstrate the feasibility and effectiveness of the learning approach in a commercial SoC verification environment.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062271"}, {"primary_key": "3751800", "vector": [], "sparse_vector": [], "title": "Fogging Effect Aware Placement in Electron Beam Lithography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern electron beam lithography (EBL) suffers from the long-range fogging effect which incurs undesired excessive exposure and thus layout pattern distortions. In this paper, we propose the first placement algorithm to tackle the fogging effect. The underlying idea is to place standard cells, guided by our efficient, yet reasonably accurate fogging effect model to minimize the fogging variation during placement, and thus the effect can be corrected by reducing dosage uniformly over the chip. We use fast Gauss transform with Hermite expansion for convolution approximation to estimate the effect. This approximation achieves a 26.5X speedup over traditional convolution computation, with only about 0.33% absolute average errors. We derive a fogging source model and further develop an efficient, accurate evaluation scheme to estimate the fogging effect by fast <PERSON><PERSON><PERSON> transform. The scheme achieves a 30.2X speedup over traditional convolution computation, with only about 2.35% absolute average errors, which enables the iterative evaluation and variation minimization of the effect during analytical global placement. We also develop fogging-aware legalization and detailed placement to further optimize the placement quality, while maintaining fogging variation. Experimental results show that our algorithm can effectively reduce the fogging variation by 15.5%, while maintaining high wirelength quality, at reasonable runtime.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062252"}, {"primary_key": "3751801", "vector": [], "sparse_vector": [], "title": "Graph-Based Logic Bit Slicing for Datapath-Aware Placement.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Extracting similar datapath bit slices which handle highly parallel bit operations can help a modern placer to obtain better solutions for datapath-oriented designs. A current state-of-the-art datapath bit slicing method achieves the best extraction results using a network-flow-based algorithm. However, this work has two major drawbacks: (1) it extracts only a limited number of bit slices for datapaths with different I/O widths, which are commonly seen in real designs, and (2) it does not consider bit-slice similarity, which is an important feature for placement considering datapaths. To remedy these drawbacks, we present (1) a balanced bipartite edge-cover algorithm to fully slice a datapath with different I/O widths, and (2) a simulated annealing scheme to further improve bit-slice similarity, while maintaining fully-sliced structures. Compared with the state-of-the-art work, experimental results show that our slicing algorithm extracts more bit slices with similar structures, and helps a leading academic placer achieve averagely 5% smaller routed wirelength. The results also validate the high correlation between datapaths and structure regularity/similarity.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062254"}, {"primary_key": "3751802", "vector": [], "sparse_vector": [], "title": "Test Methodology for Dual-rail Asynchronous Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>"], "summary": "With low power and variation-tolerant features, asynchronous have been widely used in advanced VLSI designs. Testing asynchronous circuits has become a very important practical issue. This research presents new test methodology, including design for testability (DFT) and automatic test pattern generation (ATPG), for asynchronous dual-rail circuits. The proposed DAC-scan cell is a hazard-free scan design, which can be applied to various implementations of dual-rail asynchronous circuits. Two-pattern stuck-at test and three-pattern delay test techniques are presented to detect local feedback faults in the circuits without inserting extra DFT into feedback loops. With our test methodology, we can use traditional full-scan ATPG to generate high fault coverage test patterns. Moreover, designers can tradeoff between fault coverage and area overhead by using different versions of DAC-scan cells.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062325"}, {"primary_key": "3751803", "vector": [], "sparse_vector": [], "title": "Ultra-Efficient Processing In-Memory for Data Intensive Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have witnessed a rapid growth in the domain of Internet of Things (IoT). This network of billions of devices generates and exchanges huge amount of data. The limited cache capacity and memory bandwidth make transferring and processing such data on traditional CPUs and GPUs highly inefficient, both in terms of energy consumption and delay. However, many IoT applications are statistical at heart and can accept a part of inaccuracy in their computation. This enables the designers to reduce complexity of processing by approximating the results for a desired accuracy. In this paper, we propose an ultra-efficient approximate processing in-memory architecture, called APIM, which exploits the analog characteristics of non-volatile memories to support addition and multiplication inside the crossbar memory, while storing the data. The proposed design eliminates the overhead involved in transferring data to processor by virtually bringing the processor inside memory. APIM dynamically configures the precision of computation for each application in order to tune the level of accuracy during runtime. Our experimental evaluation running six general OpenCL applications shows that the proposed design achieves up to 20x performance improvement and provides 480x improvement in energy-delay product, ensuring acceptable quality of service. In exact mode, it achieves 28x energy savings and 4.8x speed up compared to the state-of-the-art GPU cores.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062337"}, {"primary_key": "3751804", "vector": [], "sparse_vector": [], "title": "CFPU: Configurable Floating Point Multiplier for Energy-Efficient Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many applications, such as machine learning and data sensing are statistical in nature and can tolerate some level of inaccuracy in their computation. Approximate computation is a viable method to save energy and increase performance by trading energy for accuracy. There are a number of proposed approximate solutions, however, they are limited to a small range of applications because they cannot control the error rate of their output. In this paper, we propose a novel approximate floating point multiplier, called CFPU, which significantly reduces energy and improves performance of multiplication at the expense of accuracy. Our design approximately models multiplication by replacing the most costly step of the operation with a lower energy alternative. In order to tune the level of approximation, CFPU dynamically identifies the inputs which will produce the largest approximation error and processes them in precise CFPU mode. We showed that our CFPU can outperforms a standard FPU when at least 4% of multiplications are performed in approximate mode. In our tested applications this percentage of multiplications is substantially higher, leading to significant energy savings. Our experimental evaluation on AMD Southern Island GPU shows that replacing the proposed CFPU with traditional FPUs results in 77% energy savings and 3.5x energy-delay product improvement over eight general OpenCL applications while providing acceptable quality of service. In addition, for the same level of accuracy, the CFPU provides 2.4x energy-delay product improvement compared to state-of-the-art approximate multipliers.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062210"}, {"primary_key": "3751805", "vector": [], "sparse_vector": [], "title": "Greybox Design Methodology: A Program Driven Hardware Co-optimization with Ultra-Dynamic Clock Management.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, a novel Greybox design methodology is proposed to establish a design and co-optimization flow across the boundary of conventional software and hardware design. The dynamic timing of each software instruction is simulated and associated with processor hardware design, which provides the basis of ultra-dynamic clock management. The proposed scheme effectively implements the instruction-based clock management and achieves 21.71% frequency speedup. Besides, a novel program-driven hardware optimization flow is proposed, in which software operations are mapped with hardware gate netlist and sorted by the usage frequency. The experiments on an ARM based pipeline design in commercial 65nm CMOS process show an extra 10% frequency speedup is obtained with high optimization efficiency. Overall, the proposed Greybox design method achieves frequency speedup by 31.56%, comparing with conventional design method.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062255"}, {"primary_key": "3751806", "vector": [], "sparse_vector": [], "title": "HyCUBE: A CGRA with Reconfigurable Single-cycle Multi-hop Interconnect.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "CGRAs are promising as accelerators due to their improved energy-efficiency compared to FPGAs. Existing CGRAs support reconfigurability for operations, but not communications because of the static neighbor-to-neighbor interconnect, leading to both performance loss and increased complexity of the compiler. In this paper, we introduce HyCUBE, a novel CGRA architecture with a reconfigurable interconnect providing single-cycle communications between distant FUs, resulting in a new formulation of the application mapping problem that leads to the design of an efficient compiler. HyCUBE achieves 1.5X and 3X better performance-per-watt compared to a CGRA with standard NoC and a CGRA with neighbor-to-neighbor connectivity, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062262"}, {"primary_key": "3751807", "vector": [], "sparse_vector": [], "title": "RIC: Relaxed Inclusion Caches for Mitigating LLC Side-Channel Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, side-channel attacks on Last Level Caches (LLCs) were demonstrated. The attacks require the ability to evict critical data from the cache hierarchy, making future accesses visible. We propose Relaxed Inclusion Caches (RIC), a low-complexity cache design protecting against LLC side channel attacks. RIC relaxes inclusion when it is not needed, preventing the attacker from replacing the victim's data from the local core caches thus protecting critical data from leakage. RIC improves performance (by about 10%) and retains snoop filtering capabilities of inclusive cache hierarchies, while requiring only minimal changes to the cache.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062313"}, {"primary_key": "3751808", "vector": [], "sparse_vector": [], "title": "XFC: A Framework for eXploitable Fault Characterization in Block Ciphers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Aritra Hazra"], "summary": "Fault attacks recover secret keys by exploiting faults injected during the execution of a block cipher. However, not all faults are exploitable and every exploitable fault is associated with an offline complexity to determine the key. The ideal fault attack would recover maximum key bits with minimum offline effort. Finding the ideal fault attack for a block cipher is a laborious manual task, which can take several months to years before such an attack is discovered.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062340"}, {"primary_key": "3751809", "vector": [], "sparse_vector": [], "title": "Energy and Performance Trade-off in Nanophotonic Interconnects using Coding Techniques.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>;<PERSON>"], "summary": "Nanophotonic is an emerging technology considered as one of the key solutions for future generation on-chip interconnects. Indeed, this technology provides high bandwidth for data transfers and can be a very interesting alternative to bypass the bottleneck induced by classical NoC. However, their implementation in fully integrated 3D circuits remains uncertain due to the high power consumption of on-chip lasers. However, if a specific bit error rate is targeted, digital processing can be added in the electrical domain to reduce the laser power and keep the same communication reliability. This paper addresses this problem and proposesto transmit encoded data on the optical interconnect, which allows for a reduction of the laser power consumption, thus increasing nanophotonics interconnects energy efficiency. The results presented in this paper show that using simple Hamming coder and decoder permits to reduce the laser power by nearly 50% without loss in communication data rate and with a negligible hardware overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062237"}, {"primary_key": "3751810", "vector": [], "sparse_vector": [], "title": "Fast Predictive Useful Skew Methodology for Timing-Driven Placement Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "SangGi Do", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Incremental timing-driven placement (TDP) is one of the most crucial steps for timing closure in a physical design. The need for high-performance incremental TDP continues to grow, but prior studies have focused on optimizing only setup timing slacks, which can be easily stuck in local optima. In this paper, we present a useful skew methodology based on a maximum mean weight cycle (MMWC) approach in the incremental TDP. The proposed useful skew methodology finds an optimal clock latency for each flip-flop, and the clock latency is implemented by moving the flip-flops and/ or reassigning them to local clock buffers. With the proposed TDP method, we effectively reduce the early slack of ICCAD 2015 contest benchmarks, and achieve 124(%) and 78(%) of total quality score improvement compared to the 2015 contest winner, and early slack histogram compression (EHC) method, respectively. Moreover, with fewer iterations in the optimization, the runtime of our predictive useful skew method is an average of 7.4 times faster than an EHC method.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062247"}, {"primary_key": "3751811", "vector": [], "sparse_vector": [], "title": "Improving Performance and Lifetime of Large-Page NAND Storages Using Erase-Free Subpage Programming.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jisung Park", "<PERSON><PERSON>"], "summary": "Recent NAND flash devices have large page sizes. Although large pages are useful in increasing the flash capacity, they can degrade both the performance and lifetime of flash storage systems when small writes are dominant. We propose a new NAND programming scheme, called erase-free sub-page programming (ESP), which allows the same page to be programmed multiple times for small writes. By avoiding internal fragmentation, the ESP scheme reduces the overhead of garbage collection for large-page NAND storages. Experimental results show that an ESP-aware FTL can improve the IOPS and lifetime by up to 74% and 177%, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062264"}, {"primary_key": "3751812", "vector": [], "sparse_vector": [], "title": "A Kernel Decomposition Architecture for Binary-weight Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The binary-weight CNN is one of the most efficient solutions for mobile CNNs. However, a large number of operations are required to process each image. To reduce such a huge operation count, we propose an energy-efficient kernel decomposition architecture, based on the observation that a large number of operations are redundant. In this scheme, all kernels are decomposed into sub-kernels to expose the common parts. By skipping the redundant computations, the operation count for each image was consequently reduced by 47.7%. Furthermore, a low cost bit-width quantization technique was implemented by exploiting the relative scales of the feature data. Experimental results showed that the proposed architecture achieves a 22% energy reduction.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062189"}, {"primary_key": "3751813", "vector": [], "sparse_vector": [], "title": "ArchEx: An Extensible Framework for the Exploration of Cyber-Physical System Architectures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "We present ArchEx, a framework for cyber-physical system architecture exploration. We formulate the exploration problem as a mapping problem, where \"virtual\" components are mapped into \"real\" components from pre-defined libraries to minimize an objective function while guaranteeing that system requirements are satisfied. ArchEx leverages an extensible set of patterns to enable formal, yet flexible, requirement specification, a graph-based internal representation of the system architecture, and algorithms based on mixed integer linear programming to solve the mapping problem. Its effectiveness is demonstrated on two industrial case studies: an aircraft power distribution network and a reconfigurable automated production line.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062204"}, {"primary_key": "3751814", "vector": [], "sparse_vector": [], "title": "On Mitigation of Side-Channel Attacks in 3D ICs: Decorrelating Thermal Patterns from Power and Activity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Various side-channel attacks (SCAs) on ICs have been successfully demonstrated and also mitigated to some degree. In the context of 3D ICs, however, prior art has mainly focused on efficient implementations of classical SCA countermeasures. That is, SCAs tailored for up-and-coming 3D ICs have been overlooked so far. In this paper, we conduct such a novel study and focus on one of the most accessible and critical side channels: thermal leakage of activity and power patterns. We address the thermal leakage in 3D ICs early on during floorplanning, along with tailored extensions for power and thermal management. Our key idea is to carefully exploit the specifics of material and structural properties in 3D ICs, thereby decorrelating the thermal behaviour from underlying power and activity patterns. Most importantly, we discuss powerful SCAs and demonstrate how our open-source tool helps to mitigate them.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062293"}, {"primary_key": "3751815", "vector": [], "sparse_vector": [], "title": "Design of an Energy-Efficient Accelerator for Training of Convolutional Neural Networks using Frequency-Domain Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Taesik Na", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) require high computation and memory demand for training. This paper presents the design of a frequency-domain accelerator for energy-efficient CNN training. With Fourier representations of parameters, we replace convolutions with simpler pointwise multiplications. To eliminate the Fourier transforms at every layer, we train the network entirely in the frequency domain using approximate frequency-domain nonlinear operations. We further reduce computation and memory requirements using sinc interpolation and Hermitian symmetry. The accelerator is designed and synthesized in 28nm CMOS, as well as prototyped in an FPGA. The simulation results show that the proposed accelerator significantly reduces training time and energy for a target recognition accuracy.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062228"}, {"primary_key": "3751816", "vector": [], "sparse_vector": [], "title": "Toggle MUX: How X-Optimism Can Lead to Malicious Hardware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To highlight a potential threat to hardware security, we propose a methodology to derive a trigger signal from the behavior of Verilog simulation models of field-programmable gate array (FPGA) primitives that behave X-optimistic. We demonstrate our methodology with an example trigger that is implemented using Xilinx 7 Series FPGAs. Experimental results show that it is easily possible to create a trigger signal that is '0' in simulation (pre- and post-synthesis), and '1' in hardware. We show that this kind of trigger is neither detectable by formal equivalence checks, nor by recent Trojan detection techniques. As a countermeasure, we propose to carefully reconsider the utilization of X-optimism in FPGA simulation models.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062328"}, {"primary_key": "3751817", "vector": [], "sparse_vector": [], "title": "A Clock Skewing Strategy to Reduce Power and Area of ASIC Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "A new method for reducing power and area of standard cell ASICs is described. The method is based on deliberately introducing clock skew without the use of extra buffers in the clock network. This is done by having some flipflops, called sources, generate clock signals for other flipflops, called targets. The method involves two key features: (1) the design of new differential flipflop, referred to as KVFF, that is functionally identical to a master-slave edge-triggered D flipflop, but in addition, produces an completion signal that is a skewed version of its input clock, which is used to clock other flipflops; and (2) an efficient algorithm that identifies the sources and targets involved in the new clocking scheme, with the objective of reducing area and power. These are reduced because deliberate skew introduces extra slack on the logic cones that feed the target flipflops, which is exploited by synthesis tools to reduce area and power. In addition, the overhead of conventional methods of introducing skew, e.g. buffers, is eliminated. Using commercial tools, significant improvements in power and area are shown on placed and routed netlists of several circuits.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062183"}, {"primary_key": "3751818", "vector": [], "sparse_vector": [], "title": "LibAbs: An Efficient and Accurate Timing Macro-Modeling Algorithm for Large Hierarchical Designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ever-increasing design complexity is driving the need of fast and accurate macro-modeling algorithms to accelerate the hierarchical timing. We introduce LibAbs, an effective macro-modeling algorithm that efficiently supports high accuracy, high compression rate, and multi-threading. LibAbs applies tree-based graph reduction techniques to reduce the model size with comparable accuracy values to the flat model under multi-threaded environment. LibAbs outperforms existing tools including top winners from TAU 2016 macro-modeling contest in terms of model size, accuracy, and runtime on industry benchmarks. The in-context usage of our abstracted model has also demonstrated promising performance for timing-driven optimizations in large hierarchical designs.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062274"}, {"primary_key": "3751819", "vector": [], "sparse_vector": [], "title": "Path-Specific Functional Timing Verification under Floating and Transition Modes of Operation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Functional timing analysis (FTA) overcomes the limitation of static timing analysis (STA) to allow distinction of false and true paths. Modern FTA methods exploit timed characteristic functions (TCFs) to implicitly calculate the longest true delay of a circuit. However, they are inadequate for the verification of timing exceptions, which is crucial for timing signoff, due to their implicit enumeration of all paths rather than specific paths of concern. We present the first TCF-based FTA method for path-specific timing verification under both floating and transition modes of operation. Experiments demonstrate the unique benefit and scalability of our method.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062299"}, {"primary_key": "3751820", "vector": [], "sparse_vector": [], "title": "Leave the Cache Hierarchy Operation as It Is: A New Persistent Memory Accelerating Approach.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Persistent memory places NVRAM on the memory bus, offering fast access to persistent data. Yet maintaining NVRAM data persistence raises a host of challenges. Most proposed schemes either incur much performance overhead or require substantial modifications to existing architectures.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062272"}, {"primary_key": "3751821", "vector": [], "sparse_vector": [], "title": "Low-overhead Aging-aware Resource Management on Embedded GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "GPUs have been employed in the embedded systems to handle increased amount of computation and satisfy the timing requirement. Therefore, the lifetime of embedded GPUs is considered one of the most important aspects to ensure functional correctness over a long period of time. Moreover, existing state-of-the-art compiler-based GPU aging management techniques suffer from a considerable amount of performance overhead. In this paper, we propose a low-overhead aging-aware resource management technique. The proposed technique extends the behavior of the existing warp scheduler and the instruction dispatcher to cluster the computational cores and distribute instructions based on the aging information. Compared to when using the original applications, our technique improves the aging of the embedded GPU by 30% on average. In addition, compared to the state-of-the-art GPU aging management technique, our technique reduces the performance overhead by 16.4% on average while improving the aging by 3% on average.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062277"}, {"primary_key": "3751822", "vector": [], "sparse_vector": [], "title": "Sneak-Path Based Test and Diagnosis for 1R RRAM Crossbar Using Voltage Bias Technique.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Xiangyu Bi", "Naifeng Jing", "<PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "Metal-oxide resistive random access memories with a single memristor device at the crosspoint (1R RRAM) is a promising alternative to next generation storage technology due to their high density, scalability, non-volatility and low power consumption. However, the imperfect fabrication process introduces high defect rates of the nanoscale memristor devices and leads to yield degradation. In addition, sneak-paths occur in 1R RRAM crossbar that can jeaperdize the normal read/write operation. Previous work proposes voltage bias technique to eliminate the sneak-paths. Instead, in the paper, we leverage voltage bias to manipulate various distribution of sneak-paths that can screen one or multiple faults out of a 4 x 4 region of memristors at once, and consequently diagnose the exact location of each faulty memristor within three write-read operations. The SPICE simulation results highlight the effectiveness and efficiency of the proposed test method.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062318"}, {"primary_key": "3751823", "vector": [], "sparse_vector": [], "title": "Energy-Efficient Execution for Repetitive App Usages on big.LITTLE Architectures.", "authors": ["Xi<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smartphones are now equipped with high-performance processors to meet the increasing complexity of apps. However, these processors drain the battery quickly, which has become a major concern for Smartphone users. The latest big.LITTLE multicore architecture provides new energy-saving facilities in addition to traditional DVFS technique. But this mechanism has not been well exploited by current Smartphones. In this paper, we propose a framework that records the performance demands for repetitive app usage scenarios, and adjusts the big.LITTLE processor to an energy-efficient configuration for subsequent executions of these scenarios. We implemented our framework on Android Smartphones, and the experiments achieve 30% energy saving on average without compromising user experience.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062239"}, {"primary_key": "3751824", "vector": [], "sparse_vector": [], "title": "Cross-level Monte Carlo Framework for System Vulnerability Evaluation against Fault Attack.", "authors": ["<PERSON><PERSON>", "<PERSON>zhen Lai", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fault attack becomes a serious threat to system security and requires to be evaluated in the design stage. Existing methods usually ignore the intrinsic uncertainty in attack process and suffer from low scalability. In this paper, we develop a general framework to evaluate system vulnerability against fault attack. A holistic model for fault injection is incorporated to capture the probabilistic nature of attack process. Based on the probabilistic model, a security metric named as System Security Factor (SSF) is defined to measure the system vulnerability. In the framework, a Monte Carlo method is leveraged to enable a feasible evaluation of SSF for different systems, security policies, and attack techniques. We enhance the framework with a novel system pre-characterization procedure, based on which an importance sampling strategy is proposed. Experimental results on a commercial processor demonstrate that compared to random sampling, a 2500X speedup is achieved with the proposed sampling strategy. Meanwhile, 3% registers are identified to contribute to more than 95% SSF. By hardening these registers, a 6.5X security improvement can be achieved with less than 2% area overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062220"}, {"primary_key": "3751825", "vector": [], "sparse_vector": [], "title": "Component-Oriented High-level Synthesis for Continuous-Flow Microfluidics Considering Hybrid-Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Technological innovations in continuous-flow microfluidics require updated automated synthesis methods. As new microfluidic components and biochemical applications are constantly introduced, the current functionality-based application mapping methods and the fixed-time-slot scheduling methods are insufficient to solve the new design challenges. In this work, we propose a component-oriented general device concept that enables precise description of operations and devices, and adapts well to technological updates. Applying this concept, we propose a layering algorithm together with a mathematical modeling method to synthesize binding and hybrid-scheduling solutions that support both fixed schedule and real-time decisions. We also consider potential chip layout and optimize the number of flow channels among devices to save routing efforts. Experimental results demonstrate that our solution fully utilizes the chip resources and can handle operations with different requirements.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062213"}, {"primary_key": "3751826", "vector": [], "sparse_vector": [], "title": "Adaptive Thermal Management for 3D ICs with Stacked DRAM Caches.", "authors": ["<PERSON><PERSON>", "Kai<PERSON> Zhang", "<PERSON><PERSON><PERSON>", "Seda Ogrenci <PERSON>mi<PERSON>"], "summary": "We describe an adaptive thermal management system for 3D-ICs with stacked DRAM cache memories. We present a detailed analysis of the impact of 3D-IC hotspot aggregation on the refresh behavior of the stacked DRAM-based L3 cache. We also present the consequence of the refresh variation on the overall system performance and cache energy consumption. Our analysis demonstrates that memory intensive applications are influenced more strongly by the DRAM refresh variation. We show that there is an optimal operating point where, with a reduced clock frequency, processor cores would actually recover any performance loss induced by DRAM refresh and at the same time the cache energy consumption could be optimized. We propose a low overhead run-time method that can identify the best CPU frequency modulation factor to cool the system to minimize accelerated refresh rates in the DRAM caches. Our system can provide a customizable trade-off between performance of the processor and energy savings of the memory.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062197"}, {"primary_key": "3751827", "vector": [], "sparse_vector": [], "title": "Maximizing Forward Progress with Cache-aware Backup for Self-powered Non-volatile Processors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy harvesting is replacing battery to power embedded systems such as Internet of Things and wearable devices. Unstable energy supply brings challenges to energy harvesting powered system, resulting in frequent interruptions. Non-volatile processor is proposed to back up volatile logics before energy depletion and recover the system status after energy resumes. The backup efficiency of memory content significantly affects program performance. There are existing researches focusing on backup optimizations, but they did not fully consider cache behaviors. In this paper, we introduce cache persistence analysis into memory backup for self-powered non-volatile processors. The evaluation shows that the proposed cache-aware backup delivers on average 45.6% improvement in forward progress, achieving 40.2% and 12.7% higher system performance compared with instant and cache-unaware backup.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062282"}, {"primary_key": "3751828", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: Accelerating Inverse Kinematics for High-DOF Robots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Sun"], "summary": "Kinematics is the basis of robotic control, which manages the robots' movement, walking and balancing. As a critical part of Kinematics, the Inverse Kinematics (IK) will consume more time and energy to figure out the solution with the degrees of freedom increase. It goes beyond the ability of general-purpose processor based methods to provide real-time IK solver for manipulators with high degree of freedom. In this paper, we present a novel parallel algorithm, Quick-IK, based on the Jacobian transpose method. Via speculative searching in parallel, Quick-IK can reduce the number of iterations by 97% for the baseline Jacobian transpose method. In addition, we propose a novel specialized architecture, IKAcc, to boost the energy efficiency of Quick-IK through hardware acceleration. The evaluation shows that IKAcc can solve IK problem in 12 milliseconds for a 100 degrees of freedom manipulator. In addition, IKAcc can achieve 1700x performance speed-up over the CPU implementation of the original Jacobian transpose method and 30x speedup over the GPU implementation of Quick-IK. At same time, IKAcc achieves about 776x higher energy efficiency than the GPU implementation of Quick-IK.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062223"}, {"primary_key": "3751829", "vector": [], "sparse_vector": [], "title": "Detailed Placement for Two-Dimensional Directed Self-Assembly Technology.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two-dimensional directed self-assembly (2D-DSA), a promising nanotechnology, manipulates the orientation of double posts to guide block copolymers to fabricate 2D patterns in nanoscale. In this paper, we present the first detailed placement algorithm for 2D-DSA. We first propose an orientation-number model for nets to estimate post orientations. Then, a cost model based on the orientation numbers is defined to estimate the 2D-DSA routability efficiently during placement, and a novel detailed placement framework is proposed to consider the orientationsto optimize wirelength. In this framework, we develop a dynamic-programming-based algorithm for single-row detailed placement with a specialized pruning technique based on the defined orientation cost. Experimental results show that our algorithm can effectively generate a 2D-DSA friendly placement solution.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062229"}, {"primary_key": "3751830", "vector": [], "sparse_vector": [], "title": "INVITED Challenges and Potential for Incorporating Model-Based Design in Medical Device Development: Extended Abstract.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072947"}, {"primary_key": "3751831", "vector": [], "sparse_vector": [], "title": "Hardware ODE Solvers using Stochastic Circuits.", "authors": ["Siting <PERSON>", "<PERSON><PERSON>"], "summary": "A novel ordinary differential equation (ODE) solver is proposed by using a stochastic integrator to implement the accumulative function of the Euler method. We show that a stochastic integrator is an unbiased estimator for a Euler numerical solution. Unlike in conventional stochastic circuits, in which long stochastic bit streams are required to produce a result with a high accuracy, the proposed stochastic ODE solver provides an estimate of the solution for every bit in the stochastic bit stream, thus significantly reducing the latency and energy consumption of the circuit. Complex ODE solvers are constructed for solving nonhomogeneous ODEs, systems of ODEs and higher-order ODEs. Experimental results show that the stochastic ODE solvers provide very accurate solutions compared to their binary counterparts, with on average an energy saving of 46% (up to 74%), 8x throughput per area (up to nearly 12x) and a runtime reduction of 72% (up to 82%).", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062258"}, {"primary_key": "3751832", "vector": [], "sparse_vector": [], "title": "Rescuing Memristor-based Neuromorphic Design with High Defects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (<PERSON>) <PERSON>"], "summary": "Memristor-based synaptic network has been widely investigated and applied to neuromorphic computing systems for the fast computation and low design cost. As memristors continue to mature and achieve higher density, bit failures within crossbar arrays can become a critical issue. These can degrade the computation accuracy significantly. In this work, we propose a defect rescuing design to restore the computation accuracy. In our proposed design, significant weights in a specified network are first identified and retraining and remapping algorithms are described. For a two layer neural network with 92.64% classification accuracy on MNIST digit recognition, our evaluation based on real device testing shows that our design can recover almost its full performance when 20% random defects are present.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062310"}, {"primary_key": "3751833", "vector": [], "sparse_vector": [], "title": "Streak: Synergistic Topology Generation and Route Synthesis for On-Chip Performance-Critical Signal Groups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As VLSI technology scales to deep sub-micron, design for interconnections becomes increasingly challenging. The traditional bus routing follows a sequential bit-by-bit order, and few works explicitly target inter-bit regularity for signal groups via multilayer topology selection. To overcome these limitations, we present Streak, an efficient framework that combines topology generation and wire synthesis with a global view of optimization and constrained metal layer track resource allocation. In the framework, an identification stage decomposes binding groups into a set of representative objects; with the generated backbones, equivalent topologies are accompanied by the bits in every object; then a formulation guides the routing considering wire congestion and design regularity. Experimental results using industrial benchmarks demonstrate the effectiveness of the proposed technique.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062321"}, {"primary_key": "3751834", "vector": [], "sparse_vector": [], "title": "Transport or Store?: Synthesizing Flow-based Microfluidic Biochips using Distributed Channel Storage.", "authors": ["<PERSON><PERSON> Liu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Flow-based microfluidic biochips have attracted much attention in the EDA community due to their miniaturized size and execution efficiency. Previous research, however, still follows the traditional computing model with a dedicated storage unit, which actually becomes a bottleneck of the performance of biochips. In this paper, we propose the first architectural synthesis framework considering distributed storage constructed temporarily from transportation channels to cache fluid samples. Since distributed storage can be accessed more efficiently than a dedicated storage unit and channels can switch between the roles of transportation and storage easily, biochips with this distributed computing architecture can achieve a higher execution efficiency even with fewer resources. Experimental results confirm that the execution efficiency of a bioassay can be improved by up to 28% while the number of valves in the biochip can be reduced effectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062334"}, {"primary_key": "3751835", "vector": [], "sparse_vector": [], "title": "Network Synthesis for Database Processing Units.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore on-chip network topologies for the Q100, an analytic query accelerator for relational databases. In such data-centric accelerators, interconnects play a critical role by moving large volumes of data. In this paper we show that various interconnect topologies can trade a factor of 2.5x in performance for 3.3x area. Moreover, standard topologies (e.g., ring or mesh) are not optimal.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062289"}, {"primary_key": "3751836", "vector": [], "sparse_vector": [], "title": "Minimizing Pipeline Stalls in Distributed-Controlled Coarse-Grained Reconfigurable Arrays with Triggered Instruction Issue and Execution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yangdong Deng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The pipeline stall in distributed-controlled coarse-grained reconfigurable arrays is a major source stumbling performance. This work presents a Triggered-Issue and Triggered-Execution (TITE) paradigm motivated from the Triggered Instruction Architecture (TIA) which converts control and data dependencies into predicate dependencies as triggers for spatial parallelism. TITE separately triggers the issuing and execution of instructions to further relax the predicate dependencies in TIA. Triggered dual instructions and tag forwarding are proposed to minimize pipeline stalls of both intra and inter-processing elements. Experiments show that TITE improves performance, energy efficiency, and area efficiency by 21%, 17%, and 12%, respectively, compared with TIA.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062284"}, {"primary_key": "3751837", "vector": [], "sparse_vector": [], "title": "Latency-Aware Packet Processing on CPU-GPU Heterogeneous Systems.", "authors": ["<PERSON><PERSON>", "Unmesh D<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In response to the tremendous growth of the Internet, towards what we call the Internet of Things (IoT), there is a need to move from costly, high-time-to-market specific-purpose hardware to flexible, low-time-to-market general-purpose devices for packet processing. Among several such devices, GPUs have attracted attention in the past, mainly because the high computing demand of packet processing applications can, potentially, be satisfied by these throughput-oriented machines. However, another important aspect of such applications is the packet latency which, if not handled carefully, will overshadow the throughput benefits. Unfortunately, until now, this aspect has been mostly ignored. To address this issue, we propose a method that considers the variable bit rate of the traffic and, depending on the current rate, minimizes the latency, while meeting the rate demand. We propose a persistent kernel based software architecture to overcome the challenges inherent in GPU implementation like kernel invocation overhead, CPU-GPU communication and memory access overhead. We have chosen packet classification as the packet processing application to demonstrate our technique. Using the proposed approach, we are able to reduce the packet latency on average by a factor of 3.5, compared to the state-of-the-art solutions, without any packet drop.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062269"}, {"primary_key": "3751838", "vector": [], "sparse_vector": [], "title": "TrojanGuard: Simple and Effective Hardware Trojan Mitigation Techniques for Pipelined MPSoCs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hardware Trojans are a major concern due to the damage caused by their stealth. One popular utilization of Multiprocessor System on Chips (MPSoCs) is the Pipelined MPSoC (PMPSoC) architectures. They are used in applications from video surveillance to consumer electronics. We present a method that detects the presence of Trojans in third party IP cores of PMPSoCs, by continuous monitoring and testing, and recovers by switching the infected core with another core. We implemented the system on a commercial cycle accurate multiprocessor simulation environment. Our system incurs about 2x area and leakage power, and 1.5x dynamic power overheads, without any adverse impact on throughput compared to the state of the art that uses Triple Modular Redundancy (TMR) and therefore incurs at least 3x overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062336"}, {"primary_key": "3751839", "vector": [], "sparse_vector": [], "title": "An Ultra-Low Power Address-Event Sensor Interface for Energy-Proportional Time-to-Information Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Internet-of-Things devices need sensors with low power footprint and capable of producing semantically rich data. Promising candidates are spiking sensors that use asynchronous Address-Event Representation (AER) carrying information within inter-spike times. To minimize the overhead of coupling AER sensors with off-the-shelf microcontrollers, we propose an FPGA-based methodology that i) tags the AER spikes with timestamps to make them carriable by standard interfaces (e.g. I2S, SPI); ii) uses a recursively divided clock generated on-chip by a pausable ring-oscillator, to reduce power while keeping accuracy above 97% on timestamps. We prototyped our methodology on a IGLOOnano AGLN250 FPGA, consuming less than 4.5mW under a 550kevt/s spike rate (i.e. a noisy environment), and down to 50uW in absence of spikes.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062201"}, {"primary_key": "3751840", "vector": [], "sparse_vector": [], "title": "Multi-variable Dynamic Power Management for the GPU Subsystem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we present a control-theoretic algorithm to improve the energy efficiency of the GPU targeting deadline-driven graphics applications. Our algorithm dynamically controls multiple power knobs within the GPU (DVFS and number of active slices) that have different control time granularities. We developed a multi-rate predictive control to overcome the time granularity constraints in the control variables and reduce runtime overhead. To enable predictive control, we developed runtime analytical predictive models for performance and power of the GPU, that take input from hardware counters and temperature sensor readings. We evaluated our approach on the latest generation of Intel Core i5 platform. Our experimental results demonstrate significant average GPU energy savings of 25% compared to the state-of-the-art algorithm at negligible performance overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062288"}, {"primary_key": "3751841", "vector": [], "sparse_vector": [], "title": "Incorporating the Role of Stress on Electromigration in Power Grids with Via Arrays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sachin <PERSON>"], "summary": "Modern power grids use via arrays to connect wires across metal layers. These arrays are susceptible to electromigration (EM), which creates voids under the vias, potentially causing circuit malfunction. We combine the effect of via redundancy with models that characterize the effect of via array geometry on thermomechanical stress, and determine how the choice of via arrays can affect EM-induced failure in a power grid based on IR-drop threshold based failure criteria.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062266"}, {"primary_key": "3751842", "vector": [], "sparse_vector": [], "title": "Optimized Design of a Human Intranet Network.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "We address the design space exploration of wireless body area networks for wearable and implantable technologies, a task that is increasingly challenging as the number and variety of devices per person grow. Our method efficiently decomposes the problem into smaller subproblems by coordinating specialized analysis and optimization techniques. We leverage mixed integer linear programming to generate candidate network configurations based on coarse energy estimations. Accurate discrete-event simulation is used to check the feasibility of the proposed configurations under reliability constraints and guide the search to achieve fast convergence. Numerical results show that our application-specific approach substantially reduces the exploration time with respect to generic optimization techniques and helps provide clear identification of promising solutions.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062296"}, {"primary_key": "3751843", "vector": [], "sparse_vector": [], "title": "Fast and Energy-Efficient Digital Filters for Signal Conditioning in Low-Power Microcontrollers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Embedded systems often use digital filtering after analog-to-digital conversion when signal conditioning is required. However, digital filters are computationally demanding, making them unsuitable for low-power microcontrollers.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062245"}, {"primary_key": "3751844", "vector": [], "sparse_vector": [], "title": "Formal Techniques for Effective Co-verification of Hardware/Software Co-designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Verification is indispensable for building reliable of hardware/software co-designs. However, the scope of formal methods in this domain is limited. This is attributed to the lack of unified property specification languages, the semantic gap between hardware and software components, and the lack of verifiers that support both C and Verilog/VHDL. To address these limitations, we present an approach that uses a bounded co-verification tool, HW-CBMC, for formally validating hardware/software co-designs written in Verilog and C. Properties are expressed in C enriched with special-purpose primitives that capture temporal correlation between hardware and software events. We present an industrial case-study, proving bounded safety properties as well as discovering critical co-design bugs on a large and complex text analytics FPGA accelerator from IBM®.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062253"}, {"primary_key": "3751845", "vector": [], "sparse_vector": [], "title": "Dynamic Platforms for Uncertainty Management in Future Automotive E/E Architectures: Invited.", "authors": ["<PERSON>", "Ghizlane Tibba", "<PERSON><PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Current automotive E/E architectures are comprised of hardware and software and are mostly designed in a monolithic approach, static over the lifetime of the vehicle. Design, implementation and updates are mostly performed on a per-component-basis, exchanging complete Electronic Control Units (ECUs) or their software image as a whole. With an increasing amount of functionality being realized in software, the benefits of software can be used increasingly. This includes modularization of components, which forms the basis for updates and addition of functions. Additionally, this modularization allows the consolidation of ECUs and supports a higher level of integration. Such modularization and dynamic behavior over the lifetime of a vehicle feet, as well as a single vehicle does, however, hold a lot of challenges for safety-critical systems. Safety-critical systems, such as cars, require their behavior to be deterministic. The design of such modular systems needs to consider and cope with uncertainties in modular architectures. This paper highlights some of the dimensions of uncertainty, which will exist in future E/E architectures and presents initial approaches on how to manage these.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072950"}, {"primary_key": "3751846", "vector": [], "sparse_vector": [], "title": "HALWPE: Hardware-Assisted Light Weight Performance Estimation for GPUs.", "authors": ["Kenneth <PERSON>;<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a predictive modeling framework for GPU performance. The key innovation underlying this approach is that performance statistics collected from representative workloads running on current generation GPUs can effectively predict the performance of next-generation GPUs. This is useful when simulators are available for the next-generation device, but simulation times are exorbitant, rendering early design space exploration of microarchitectural parameters and other features infeasible. When predicting performance across three Intel GPU generations (Haswell, Broadwell, Skylake), our models achieved low out-of-sample-errors ranging from 7.45% to 8.91%, while running 30,000-45,000 times faster than cycle-accurate simulation.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062257"}, {"primary_key": "3751847", "vector": [], "sparse_vector": [], "title": "A Fast and Power Efficient Architecture to Parallelize LSTM based RNN for Cognitive Intelligence Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Long Short-Term Memory (LSTM) based Recurrent Neural Networks (RNNs) are promising for cognitive intelligence applications like speech recognition, image caption and nature language processing, etc. However, the cascade dependent structure in RNN with huge amount of power inefficient operations like multiplication, memory accessing and nonlinear transformation, could not guarantee high computing speed and low power consumption. In this work, by exploiting semantic correlation, we propose a semantic correlation based data pre-fetch method to break the dependency and achieve parallel processing. Based on this method, a full parallel and pipeline architecture that tackles huge amount operations is designed. Experiments on benchmarks of image caption, speech recognition and language processing show that, this work improves computing speed by 5.1 times, 44.9 times and 1.53 times, respectively, and power efficiency by 1885.7 times, 4061.5 times and 127.5 times, respectively, when compared with state-of-the-art works.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062187"}, {"primary_key": "3751848", "vector": [], "sparse_vector": [], "title": "Statistical Pattern Based Modeling of GPU Memory Access Streams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent research studies have shown that modern GPU performance is often limited by the memory system performance. Optimizing memory hierarchy performance requires GPU designers to draw design insights based on the cache & memory behavior of end-user applications. Unfortunately, it is often difficult to get access to end-user workloads due to the confidential or proprietary nature of the software/data. Furthermore, the efficiency of early design space exploration of cache & memory systems is often limited due to either the slow speed of detailed simulation techniques or limited scope of state-of-the-art cache analytical models.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062320"}, {"primary_key": "3751849", "vector": [], "sparse_vector": [], "title": "Towards Design and Automation of Hardware-Friendly NOMA Receiver with Iterative Multi-User Detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a two-user non-orthogonal-multiple-access (NOMA) communication channel with an iterative multi-user receiver. It is known that NOMA provides performance gains over conventional orthogonal-multiple-access, but it comes at the cost of increased decoder complexity. Moreover, the decoder complexity varies with fraction of duration in which the users' transmissions overlap using NOMA. For this scheme, we present a hardware-friendly implementation of an LDPC-code-based multi-user-detector (MUD). We also present a MATLAB-based high-level design-automation tool that generates hardware descriptions of different NOMA-receivers. Results indicate that the simplified MUD design results in significant area and power savings with negligible impact on performance.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062332"}, {"primary_key": "3751850", "vector": [], "sparse_vector": [], "title": "Analyzing Hardware Based Malware Detectors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Detection of malicious software at the hardware level is emerging as an effective solution to increasing security threats. Hardware based detectors rely on Machine Learning(ML) classifiers to detect malware-like execution pattern based on Hardware Performance Counters(HPC) information at runtime. The effectiveness of these learning methods mainly relies on the information provided by expensive-to-implement limited number of HPC. This paper is the first attempt to thoroughly analyze various robust machine learning methods to classify benign and malware applications. Given the limited availability of HPC the analysis results help guiding architectural decision on what hardware performance counters are needed most to effectively improve ML classification accuracy. For software implementation we fully implemented these classifier at OS Kernel to understand various software overheads. The software implementation of these classifiers are found to be relatively slow with the execution time in the range of milliseconds, order of magnitude higher than the latency needed to capture malware at runtime. This is calling for hardware accelerated implementation of these algorithms. For hardware implementation, we have synthesized the studied classifier models on FPGA to compare various design parameters including logic area, power, and latency. The results show that while complex ML classifier such as MultiLayerPerceptron and logistics are achieving close to 90% accuracy, after taking into consideration their implementation overheads, they perform worst in terms of PDP, accuracy/area and latency compared to simpler but slightly less accurate rule based and tree based classifiers. Our results further show OneR to be the most cost-effective classifier with more than 80% accuracy and fast execution time of less than 10ns, achieving highest accuracy per logic area, while mainly relying on only a single branch-instruction HPC information.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062202"}, {"primary_key": "3751851", "vector": [], "sparse_vector": [], "title": "A Systems Approach to Computing in Beyond CMOS Fabrics: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "H.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "No abstract available.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072943"}, {"primary_key": "3751852", "vector": [], "sparse_vector": [], "title": "LiveSynth: Towards an Interactive Synthesis Flow.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Currently, one of the major bottlenecks in digital design is synthesis. Each iteration of a design takes several hours to synthesize, putting pressure on designers to carefully consider when to submit jobs and wait for the delayed feedback. This delay is especially important in FPGA emulation, when synthesis is performed frequently while fixing the system functionality. This work proposes LiveSynth, a different approach for digital design with relatively quick feedback after small, incremental changes. Our approach delivers results with close-to-optimal quality--within a few seconds of processing time in most cases. LiveSynth was able to improve synthesis time by about 10x with minimal impact on QoR.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062275"}, {"primary_key": "3751853", "vector": [], "sparse_vector": [], "title": "Towards Full-System Energy-Accuracy Tradeoffs: A Case Study of An Approximate Smart Camera System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The intrinsic error resilience exhibited by emerging application domains enables a new dimension for energy optimization of computing systems, namely the introduction of a controlled amount of approximations during system operation in exchange for substantial energy savings. Prior work in the area of approximate computing has focused on individual subsystems of a computing system, e.g., the computational subsystem or the memory subsystem. Since they focus only on individual subsystems, these techniques are unable to exploit the large energy-saving opportunities that stem from adopting a full-system perspective and approximating multiple subsystems of a computing platform simultaneously in a coordinated manner. This paper proposes a systematic methodology to perform joint approximations across different subsystems, leading to significant energy benefits compared to approximating individual subsystems in isolation. We use the example of a smart camera system that executes various computer vision and image processing applications to illustrate how the sensing, memory, and processing subsystems can all be approximated synergistically. We have implemented such an approximate smart camera system using an Altera Stratix IV GX FPGA development board, a Terasic TRDB-D5M 5 Megapixel camera module, and a 1GB DDR3 SODIMM module. Experimental results obtained using six application benchmarks demonstrate significant energy savings (around 7.5X on average) for minimal (< 1%) loss in application quality. Compared to approximating a single subsystem, the proposed full-system approximation methodology achieves additional energy benefits of 3.5X - 5.5X on average for minimal (< 1%) quality loss.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062333"}, {"primary_key": "3751854", "vector": [], "sparse_vector": [], "title": "ASSURE: Authentication Scheme for SecURE Energy Efficient Non-Volatile Memories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data tampering threatens data integrity in emerging non-volatile memories (NVMs). Whereas Merkle Tree (MT) memory authentication is effective in thwarting data tampering attacks, it drastically increases cell writes and memory accesses, adversely impacting NVM energy, lifetime, and system performance (instructions per cycle (IPC)). We propose ASSURE, a low overhead, high performance Authentication Scheme for SecURE energy efficient (ASSURE) NVMs. ASSURE synergistically integrates (i) smart message authentication codes (SMACs), which eliminate redundant cell writes by enabling MAC computation of only modified words on memory writes, with (ii) multi-root MTs (MMTs), which reduce MT reads/writes by constructing either high performance static MMTs (SMMTs) or low overhead dynamic MMTs (DMMTs) over frequently accessed memory regions. Our full-system simulations of the SPEC CPU2006 benchmarks on a triple-level cell (TLC) resistive RAM (RRAM) architecture show that on average, SMMT ASSURE (DMMT ASSURE) reduces NVM energy by 59% (55%), increases memory lifetime by 2.36x (2.11x), and improves IPC by 11% (10%), over state-of-the-art MT memory authentication.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062205"}, {"primary_key": "3751855", "vector": [], "sparse_vector": [], "title": "Specification, Verification and Design of Evolving Automotive Software: Invited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>"], "summary": "Modern automotive systems consist of hundreds of functionalities implemented in software. Moreover, these functionalities are constantly evolving with increasing demand for automation, industry competition and changing sensor and actuator capabilities. Correspondingly, it is important to adapt the engineering and software development processes for such systems to consider fast management of this evolution at minimum cost. Towards this, in this paper, we outline three different problems in the context of evolving automotive software and discuss potential solutions for each of them. First, we outline a framework that can accommodate variability in specifications while developing software for automotive product lines. Secondly, a technique is illustrated to addresses after-sales addition of new features in existing systems by studying corresponding acceptable performance degradation of existing functionalities. Finally, we discuss how an inconsistency management framework and regression verification can ensure consistent evolution of engineering processes for automotive mechatronic systems.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072946"}, {"primary_key": "3751856", "vector": [], "sparse_vector": [], "title": "Extensibility in Automotive Security: Current Practice and Challenges: Invited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A modern automotive design contains over a hundred microprocessors, several cyber-physical modules, connectivity to a variety of networks, and several hundred megabytes of software. The future is anticipated to see an even sharper rise in complexity of this electronics, with the imminence of driverless vehicles, the potential of connected automobiles within a few years, and work towards seamless integration of automobiles with smart cities and infrastructure systems. Security is a fundamental challenge in the design of automotive systems. Unfortunately, security considerations in automotive systems are complicated by two factors: (1) need for real-time mitigation against in-field threats; and (2) in-field configurability and extensibility of security features. This paper examines the trade-offs between security countermeasures, real-time requirements, and in-field configurability needs for modern automotive systems. We discuss the current state of the practice in automotive security architecture, as well as gaps and challenges that need to be addressed for a viable security solution in future.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072952"}, {"primary_key": "3751857", "vector": [], "sparse_vector": [], "title": "Linear Periodically Time-Varying (LPTV) Circuits Enable New Radio Architectures for Emerging Wireless Communication Paradigms: Extended Abstract: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The next generation of cellular wireless communication networks (the much hyped \"5G\") is targeting a 1000x increase in data capacity. This has sparked an investigation of new and transformative wireless communication paradigms, including massive MIMO, full duplex and millimeter-wave wireless. These new wireless paradigms place requirements on the radio circuitry that are orders of magnitude more challenging than traditional systems, forcing us to rethink conventional radio design. Conventional analog and radio frequency circuit design has relied on linear, time-invariant (LTI) components and circuits. However, LTI components and circuits are restricted in the signal processing functionalities that can be implemented. Recently, there has been significant interest in linear, periodically time varying (LPTV) circuits that can enable new functionalities and components, such as highly-tunable, high quality integrated filters, front-ends with spatio-spectral filtering capability and integrated non-magnetic non-reciprocal components such as circulators and isolators. This paper reviews recent research breakthroughs in LPTV circuits and systems that enable full-duplex and massive MIMO wireless.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072954"}, {"primary_key": "3751858", "vector": [], "sparse_vector": [], "title": "PriSearch: Efficient Search on Private Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose PriSearch, a provably secure methodology for two-party string search. The scenario involves two parties, <PERSON> (holding a query string) and <PERSON> (holding a text), who wish to perform a string search while keeping both the query and the text private without relying on any third party. Such privacy-preserving string search avoids any data leakage when handling sensitive information, e.g., genomic data. PriSearch provides an efficient solution where two parties only need to interact for a constant number of rounds independent of the query and text size. Our approach is based on the provably secure Yao's Garbled Circuit (GC) protocol that requires the string search algorithm to be described as a Boolean circuit. We leverage logic synthesis tools to generate an optimized Boolean circuit for PriSearch such that it incurs the minimum communication/computation cost. We achieve approximately 2x and 140x performance improvements compared to the best prior non-GC and GC-based solutions, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062305"}, {"primary_key": "3751859", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> for Quantum Computer Architectures.", "authors": ["<PERSON>", "Xiang Fu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Pauli frame mechanism allows Pauli gates to be tracked in classical electronics and can relax the timing constraints for error syndrome measurement and error decoding. When building a quantum computer, such a mechanism may be beneficial, and the goal of this paper is not only to study the working principles of a Pauli frame but also to quantify its potential effect on the logical error rate. To this purpose, we implemented and simulated the Pauli frame module which, in principle, can be directly mapped into a hardware implementation. Simulation of a surface code 17 logical qubit has shown that a Pauli frame can reduce the error rate of a logical qubit up to 70% compared to the same logical qubit without Pauli frame when the decoding time equals the error correction time, and maximum parallelism can be obtained.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062300"}, {"primary_key": "3751860", "vector": [], "sparse_vector": [], "title": "Deep3: Leveraging Three Levels of Parallelism for Efficient Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes Deep3 an automated platform-aware Deep Learning (DL) framework that brings orders of magnitude performance improvement to DL training and execution. Deep3 is the first to simultaneously leverage three levels of parallelism for performing DL: data, network, and hardware. It uses platform profiling to abstract physical characterizations of the target platform. The core of Deep3 is a new extensible methodology that enables incorporation of platform characteristics into the higher-level data and neural network transformation. We provide accompanying libraries to ensure automated customization and adaptation to different datasets and platforms. Proof-of-concept evaluations demonstrate 10-100 fold physical performance improvement compared to the state-of-the-art DL frameworks, e.g., TensorFlow.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062225"}, {"primary_key": "3751861", "vector": [], "sparse_vector": [], "title": "Energy-Aware Standby-Sparing on Heterogeneous Multicore Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Da<PERSON> Zhu"], "summary": "Standby-sparing systems where one processor is used as primary while another one is deployed as spare have been used to provide high reliability to real-time embedded systems. To reduce the energy consumption, the primary uses DVFS while the spare employs DPM to postpone the backup tasks. In this paper, we re-visit the problem for heterogeneous multicore systems that include both high-performance and low-power cores. We identify and address the two main dimensions of the problem, namely, what type of core to use as the primary or backup, and how to make frequency assignments on the primary to maximize energy savings.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062238"}, {"primary_key": "3751862", "vector": [], "sparse_vector": [], "title": "Cryptography for Next Generation TLS: Implementing the RFC 7748 Elliptic Curve448 Cryptosystem in Hardware.", "authors": ["<PERSON>", "<PERSON>"], "summary": "With RFC 7748 the two elliptic curves Curve25519 and Curve448 were proposed for the next generation of TLS. Both curves were designed and optimized purely for software implementation; their implementation in hardware or physical protection against side-channel attacks were not considered in the design phase. Recently, it has been shown that for Curve25519 an efficient implementations in hardware along with side-channel protection is feasible -- yet results for the high-security Curve448 are missing. In this work we demonstrate that Curve448 can indeed be efficiently and securely implemented in hardware. We present a novel architecture for Curve448 that can compute more than 1000 point multiplications per second with 1580 logic slices and 33 DSP units of a Xilinx XC7Z020 FPGA.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062222"}, {"primary_key": "3751863", "vector": [], "sparse_vector": [], "title": "Exploiting Thread and Data Level Parallelism for Ultimate Parallel SystemC Simulation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most parallel SystemC approaches have two limitations: (a) the user must manually separate all parallel threads to avoid data corruption due to race conditions, and (b) available hardware vector units are not utilized. In this paper, we present an advanced compiler infrastructure for automatic parallelization of SystemC models at the thread-level. In addition, our infrastructure exploits opportunities for data-level parallelization. Our experimental results show a nearly linear speedup of NxM, where N and M denote the thread and data-level factors, respectively. In turn, a 4-core multi-processor achieves a speedup of up to 8.8x, and a 60-core Xeon Phi processor reaches up to 212x.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062243"}, {"primary_key": "3751864", "vector": [], "sparse_vector": [], "title": "Cryo-CMOS Electronic Control for Scalable Quantum Computing: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rosario M. Incandela", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum computers1 could revolutionize computing in a profound way due to the massive speedup they promise. A quantum computer comprises a cryogenic quantum processor and a classical electronic controller. When scaling up the cryogenic quantum processor to at least a few thousands, and possibly millions, of qubits required for any practical quantum algorithm, cryogenic CMOS (cryo-CMOS) electronics is required to allow feasible and compact interconnections between the controller and the quantum processor. Cryo-CMOS leverages the CMOS fabrication infrastructure while exploiting the continuous improvement of performance and miniaturization guaranteed by <PERSON>'s law, in order to enable the fabrication of a cost-effective practical quantum computer. However, designing cryo-CMOS integrated circuits requires a new set of CMOS device models, their embedding in design and verification tools, and the possibility to co-simulate the cryo-CMOS/quantum-processor architecture for full-system optimization. In this paper, we address these challenges by focusing on their impact on the design of complex cryo-CMOS systems.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072948"}, {"primary_key": "3751865", "vector": [], "sparse_vector": [], "title": "Timing Driven Incremental Multi-Bit Register Composition Using a Placement-Aware ILP formulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To reduce clock power, we present a novel timing-driven incremental multi-bit register (MBR) composition methodology for designs that may be rich in MBRs after logic synthesis. It identifies nearby compatible registers that can be merged without degrading timing, and without reducing the \"useful clock skew\" potential. These registers are merged providing the MBR placement can be legalized according to the proposed simplified physical constraints. A new integer linear programming (ILP) formulation minimizes the total number of registers in the design. It significantly reduces register count and clock capacitance, without adding any timing/routing/placement violations and without increasing the total wire-length of the designs, as shown by experimental results on industrial benchmarks.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062327"}, {"primary_key": "3751866", "vector": [], "sparse_vector": [], "title": "SABER: Selection of Approximate Bits for the Design of Error Tolerant Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sachin <PERSON>"], "summary": "A wide variety of error tolerant applications supports the use of approximate circuits that achieve power savings by introducing small errors. This paper proposes a fast and novel algorithm for the design of such circuits with the goal of maximizing power savings, constrained by a fixed error budget, through an analytical expression to optimally select the number of bits to be approximated. This algorithm outperforms uniform approximation schemes by over 30% in power savings, with negligible computational overhead.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062314"}, {"primary_key": "3751867", "vector": [], "sparse_vector": [], "title": "Pin Accessibility-Driven Cell Layout Redesign and Placement Optimization.", "authors": ["<PERSON>aewo<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The layout of standard cells is very dense these days, so some pins are hard to get access to. This is in particular true in complex cells with many pins (e.g. AOI) and in the layout where many of those cells are densely packed without much whitespace. We redesign those complex cells, so a library now contains both original cell and its new version with easier pin access; a systematic method is proposed to pick candidate cells for redesign and to dictate how redesign should be performed. We also introduce a measure of inaccessibility of pins in a cell, named IOC. Placement optimization is performed, which uses IOC to determine which cells should be replaced by its redesigned version and how whitespace should be redistributed. Experiments with 12 test circuits indicate that the number of routing errors (after the initial placement) is reduced by 82% on average, and the subsequent detailed routing takes 72% less runtime.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062302"}, {"primary_key": "3751868", "vector": [], "sparse_vector": [], "title": "TraPL: Track Planning of Local Congestion for Global Routing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a framework to quickly analyze track congestion inside each g-cell at the global routing stage. A distinguishing feature of our framework compared to prior work is estimating the locations of vias and partial track utilization by a global segment inside each g-cell for a given global routing solution. We integrate this model with a proposed track assignment algorithm which we show can more effectively reduce track overlaps compared to prior work. A strength of this work is to evaluate the accuracy with respect to an accurate congestion map generated by a commercial detailed router as reference. This work is a step towards bridging the gap between global and detailed routing which is an important obstacle facing modern IC design.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062335"}, {"primary_key": "3751869", "vector": [], "sparse_vector": [], "title": "Closing the Accuracy Gap of Static Performance Analysis of Asynchronous Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Asynchronous methodologies are gaining their presence in modern integrated circuit design. Cycle-time analysis of asynchronous design is nontrivial and crucial to circuit optimization. Among prior methods, linear programming-based analysis (LPA) and static performance analysis (SPA) are two representatives with high accuracy (but inefficient) and high efficiency (but inaccurate), respectively. However, the exactness of LPA remains unknown and the accuracy of SPA remains room for improvement. In this work, we demonstrate the inexactness of LPA and enhance the accuracy of SPA. Experimental results suggest our enhanced SPA almost always returns exact cycle-times while achieving up to 4000x speedup over LPA.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062211"}, {"primary_key": "3751870", "vector": [], "sparse_vector": [], "title": "A Testbed to Verify the Timing Behavior of Cyber-Physical Systems: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Time is a foundational aspect of Cyber-Physical Systems (CPS). Correct time and timing of system events are critical to optimized responsiveness to the environment, in terms of timeliness, accuracy, and precision in the knowledge, measurement, prediction, and control of CPS behavior. However, both the specification and verification of timing requirements of the CPS are typically done in an ad-hoc manner. While feasible, the system can become costly and difficult to analyze and maintain, and the process of implementing and verifying correct timing behavior can be error-prone. Towards the development of a verification testbed for testing timing behavior in tools and platforms with explicit time support, this paper first describes a way to express the various kinds of timing constraints in distributed CPS. Then, we outline the design and initial implementation of a distributed testbed to verify the timing of a distributed CPS analytically through a systematic framework. Finally, we illustrate the use of the verified timing testbed on two distributed CPS case studies.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072955"}, {"primary_key": "3751871", "vector": [], "sparse_vector": [], "title": "A New Stochastic Computing Multiplier with Application to Deep Convolutional Neural Networks.", "authors": ["Hyeon Uk Sim", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic computing (SC) allows for extremely low cost and low power implementations of common arithmetic operations. However inherent random fluctuation error and long latency of SC lead to the degradation of accuracy and energy efficiency when applied to convolutional neural networks (CNNs). In this paper we address the two critical problems of SC-based CNNs, by proposing a novel SC multiply algorithm and its vector extension, SC-MVM (Matrix-Vector Multiplier), under which one SC multiply takes just a few cycles, generates much more accurate results, and can be realized with significantly less cost, as compared to the conventional SC method. Our experimental results using CNNs designed for MNIST and CIFAR-10 datasets demonstrate that not only is our SC-based CNN more accurate and 40X~490X more energy-efficient in computation than the conventional SC-based ones, but ours can also achieve lower area-delay product and lower energy compared with bitwidth-optimized fixed-point implementations of the same accuracy.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062290"}, {"primary_key": "3751872", "vector": [], "sparse_vector": [], "title": "LSC: A Large-Scale Consensus-Based Clustering Algorithm for High-Performance FPGAs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Saurabh N. Adya"], "summary": "With recent advances in Field Programmable Gate Array (FPGA) architecture and design, the robustness and scalability of design implementation tools is becoming increasingly important. In an FPGA implementation flow, the basic logic elements (BLEs) like flip-flops (FFs) and lookup tables (LUTs) are clustered into adaptive logic modules (ALMs) and Logic Array Blocks (LABs). Clustering is a key stage in the flow that determines whether a design can fit onto the target FPGA device, and whether the Quality of Results (QoR) goals are met. Traditionally, FPGA implementation tools have used greedy clustering techniques. This paper presents an innovative clustering algorithm based on a new concept of consensus building at a large scale (LSC). The LSC algorithm is designed to work with designs with millions of elements, and to the best of our knowledge, this is the first parallel clustering algorithm in the industry. In our industrial designs benchmark set using modern FPGA devices on two deep submicron technology nodes, the new clustering engine results in average improvements of 0.5% and 2.5% in maximum clock frequency (Fmax) for the two target devices. Additionally, wiring usage is improved on the average by 2.8% and 6.5% respectively. The fitting success rate of highly utilized designs is also improved significantly with the new clustering engine.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062279"}, {"primary_key": "3751873", "vector": [], "sparse_vector": [], "title": "Optimizing Message Routing and Scheduling in Automotive Mixed-Criticality Time-Triggered Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Upcoming high-bandwidth protocols like Ethernet TSN feature mechanisms for redundant and deterministic (scheduled) message delivery to integrate safety- and real-time--critical applications and, thus, realize mixed-criticality systems. In existing design approaches, the message routing and system scheduling are generated in two entirely separated design steps, ignoring and/or not exploiting the distinct interrelations between routing and scheduling decisions. In this paper, we first introduce an exact approach to generate an implementation with a valid routing and a valid schedule in a single step by solving a 0-1 ILP. Second, we show that the 0-1 ILP formulation can be utilized in a design space exploration to optimize the routing and schedule with respect to, e.g., interference imposed on non-scheduled traffic or the number of configured port slots. We demonstrate the optimization potential of the proposed approach using a mixed-criticality system from the automotive domain.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062298"}, {"primary_key": "3751874", "vector": [], "sparse_vector": [], "title": "Hierarchical Reversible Logic Synthesis Using LUTs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's rapid advances in the physical implementation of quantum computers demand for scalable synthesis methods in order to map practical logic designs to quantum architectures. We present a synthesis algorithm for quantum computing based on k-LUT networks, which can be derived from Verilog netlists using state-of-the-art and off-the-shelf mapping algorithms. We demonstrate the effectiveness of our method in automatically synthesizing several floating point networks up to double precision. As many quantum algorithms target scientific simulation applications, they can make rich use of floating point arithmetic components. But due to the lack of quantum circuit descriptions for those components, it is not possible to find a realistic cost estimation for the algorithms. Our synthesized benchmarks provide cost estimates that allow quantum algorithm designers to provide the first complete cost estimates for a host of quantum algorithms. This is an essential step towards the goal of understanding which quantum algorithms will be practical in the first generations of quantum computers.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062261"}, {"primary_key": "3751875", "vector": [], "sparse_vector": [], "title": "Making DRAM Stronger Against Row Hammering.", "authors": ["Mung<PERSON> Son", "Hyunsun Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern DRAM suffers from a new problem called row hammering. The problem is expected to become more severe in future DRAMs mostly due to increased inter-row coupling at advanced technology. In order to address this problem, we present a probabilistically managed table (called PRoHIT) implemented on the DRAM chip. The table keeps track of victim row candidates in a probabilistic way and, in case of auto-refresh, the topmost entry is additionally refreshed thereby mitigating the row hammering problem. Our experiments with PARSEC benchmark and synthetic traces show that PRoHIT outperforms the state-of-the-art method, PARA, by 35.7% (PARSEC) in terms of the reduction ratio of row-hammer cases. Our proposed method also shows constantly superior performance to PARA for synthetic traces.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062281"}, {"primary_key": "3751876", "vector": [], "sparse_vector": [], "title": "Fast Embedding of Constrained Satisfaction Problem to Quantum Annealer with Minimizing Chain Length.", "authors": ["Juexiao Su", "<PERSON><PERSON>"], "summary": "Recent research has demonstrated promising results in solving constrained satisfaction problem (CSP) using D-Wave quantum annealer. However, the embedding of the CSP suffers drawbacks such as long embedding time in addition to poor quality due to long chains that reduce the ground state probability. To address those issues, we propose an effective embedding technique that reduces the embedding time and minimizes the chain length. We compared to the most recent method published in DAC 2016. Experiments using existing D-Wave 2X quantum annealer show that the proposed embedding technique increases the ground state probability by 29% on average. Furthermore, to demonstrate the efficiency, we embedded large problems onto a predicted C100 D-Wave Chimera architecture. Experimental results show that our approach reduces the run-time by 3.4x on average with reduced longest chain length.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062246"}, {"primary_key": "3751877", "vector": [], "sparse_vector": [], "title": "Hardware-Software Codesign of Accurate, Multiplier-free Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While Deep Neural Networks (DNNs) push the state-of-the-art in many machine learning applications, they often require millions of expensive floating-point operations for each input classification. This computation overhead limits the applicability of DNNs to low-power, embedded platforms and incurs high cost in data centers. This motivates recent interests in designing low-power, low-latency DNNs based on fixed-point, ternary, or even binary data precision. While recent works in this area offer promising results, they often lead to large accuracy drops when compared to the floating-point networks. We propose a novel approach to map floating-point based DNNs to 8-bit dynamic fixed-point networks with integer power-of-two weights with no change in network architecture. Our dynamic fixed-point DNNs allow different radix points between layers. During inference, power-of-two weights allow multiplications to be replaced with arithmetic shifts, while the 8-bit fixed-point representation simplifies both the buffer and adder design. In addition, we propose a hardware accelerator design to achieve low-power, low-latency inference with insignificant degradation in accuracy. Using our custom accelerator design with the CIFAR-10 and ImageNet datasets, we show that our method achieves significant power and energy savings while increasing the classification accuracy.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062259"}, {"primary_key": "3751878", "vector": [], "sparse_vector": [], "title": "Correlated Rare Failure Analysis via Asymptotic Probability Evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yangfeng Su", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, a novel Asymptotic Probability Estimation (APE) method is proposed to estimate the probability of correlated rare failure events for complex integrated systems containing a large number of replicated cells. The key idea is to approximate the failure rate of the entire system by solving a set of nonlinear equations derived from a general analytical model. An error refinement method based on Look-up Table (LUT) is further developed to improve numerical stability and, hence, reduce estimation error. Our numerical experiments demonstrate that compared to the state-of-the-art method, APE can reduce the estimation error by up to 45x without increasing the computational cost.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062217"}, {"primary_key": "3751879", "vector": [], "sparse_vector": [], "title": "Co-training of Feature Extraction and Classification using Partitioned Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "There are an increasing number of neuromorphic hardware platforms designed to efficiently support neural network inference tasks. However, many applications contain structured processing in addition to classification. Being able to map both neural network classification and structured computation onto the same platform is appealing from a system design perspective. In this paper, we perform a case study on mapping the feature extraction stage of pedestrian detection using Histogram of Oriented Gradients (HoG) onto a neuromophic platform. We consider three implementations: one that approximates HoG using neuromorphic intrinsics, one that emulates HoG outputs using a trained network, and one that allows feature extraction to be absorbed into classification. The proposed feature extraction methods are implemented and evaluated on neuromorphic hardware (IBM Neurosynaptic System). Our study shows that both a designed approximation and a \"parroted\" emulation can achieve similar accuracy, and that the latter appears to better capitalize on limited training and resource budgets, compared to the absorbed approach, while also being more power efficient than the programmed approach by a factor of 6.5x-208x.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062218"}, {"primary_key": "3751880", "vector": [], "sparse_vector": [], "title": "Developing Dynamic Profiling and Debugging Support in OpenCL for FPGAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With FPGAs emerging as a promising accelerator for general-purpose computing, there is a strong demand to make them accessible to software developers. Recent advances in OpenCL compilers for FPGAs pave the way for synthesizing FPGA hardware from OpenCL kernel code. To enable broader adoption of this paradigm, significant challenges remain. This paper presents our efforts in developing dynamic profiling and debugging support in OpenCL for FPGAs. We first propose primitive code patterns, including a timestamp and an event-ordering function, and then develop a framework, which can be plugged easily into OpenCL kernels, to dynamically collect and process run-time information.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062230"}, {"primary_key": "3751881", "vector": [], "sparse_vector": [], "title": "A Comprehensive Framework for Synthesizing Stencil Algorithms on FPGAs using OpenCL Model.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Iterative stencil algorithms find applications in a wide range of domains. FPGAs have long been adopted for computation acceleration due to its advantages of dedicated hardware design. Hence, FPGAs are a compelling alternative for executing iterative stencil algorithms. However, efficient implementation of iterative stencil algorithms on FPGAs is very challenging due to the data dependencies between iterations and elements in the stencil algorithms, programming hurdle of FPGAs, and large design space.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062185"}, {"primary_key": "3751882", "vector": [], "sparse_vector": [], "title": "Real-Time Meets Approximate Computing: An Elastic CNN Inference Accelerator with Adaptive Trade-off between QoS and QoR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the recent progress in deep learning and neural acceleration architectures, specialized deep neural network or convolutional neural network (CNNs) accelerators are expected to provide an energy-efficient solution for real-time vision/speech processing. recognition and a wide spectrum of approximate computing applications. In addition to their wide applicability scope, we also found that the fascinating feature of deterministic performance and high energy-efficiency, makes such deep learning (DL) accelerators ideal candidates as application-processor IPs in embedded SoCs concerned with real-time processing. However, unlike traditional accelerator designs, DL accelerators introduce a new aspect of design trade-off between real-time processing (QoS) and computation approximation (QoR) into embedded systems. This work proposes an elastic CNN acceleration architecture that automatically adapts to the hard QoS constraint by exploiting the error-resilience in typical approximate computing workloads For the first time, the proposed design, including network tuning-and-mapping software and reconfigurable accelerator hardware, aims to reconcile the design constraint of QoS and Quality of Result (QoR). which are respectively the key concerns in real-time and approximate computing. It is shown in experiments that the proposed architecture enables the embedded system to work flexibly in an expanded operating space, significantly enhances its real-time ability. and maximizes the energy-efficiency of system within the user-specified QoS-QoR constraint through self-reconfiguration.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062307"}, {"primary_key": "3751883", "vector": [], "sparse_vector": [], "title": "FlexCL: An Analytical Performance Model for OpenCL Workloads on Flexible FPGAs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The recent adoption of OpenCL programming model by FPGA vendors has realized the function portability of OpenCL workloads on FPGA. However, the poor performance portability prevents its wide adoption. To harness the power of FPGAs using OpenCL programming model, it is advantageous to design an analytical performance model to estimate the performance of OpenCL workloads on FPGAs and provide insights into the performance bottlenecks of OpenCL model on FPGA architecture. To this end, this paper presents FlexCL, an analytical performance model for OpenCL workloads on flexible FPGAs. FlexCL estimates the overall performance by tightly coupling the off-chip global memory and on-chip computation models based on the communication mode. Experiments demonstrate that with respect to RTL-based implementation, the average of absolute error of FlexCL is 9.5% and 8.7% for the Rodinia and PolyBench suite, respectively. Moreover, FlexCL enables rapid exploration of the design space within seconds instead of hours or days.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062251"}, {"primary_key": "3751884", "vector": [], "sparse_vector": [], "title": "A 700fps Optimized Coarse-to-Fine Shape Searching Based Hardware Accelerator for Face Alignment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huiyu Mo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, a fast shape searching face alignment (F-SSFA) algorithm based accelerator is proposed to achieve real-time processing. Firstly, a learning based low-dimensional SURF feature is introduced to reduce the computation cost in the cascaded regression. Then the Euclidean distance and shape affine transformation are utilized to accelerate the shape searching procedure. F-SSFA therefore greatly reduces the computational complexity while keeping the same accuracy. Also, a fixed-point F-SSFA based VLSI architecture is designed with approximately 80% decrease in the data transmission traffic. The throughput of this accelerator achieves 700 fps, which is especially suitable for high-speed facial-related applications.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062182"}, {"primary_key": "3751885", "vector": [], "sparse_vector": [], "title": "MOCA: an Inter/Intra-Chip Optical Network for Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The memory wall problem is due to the imbalanced developments and separation of processors and memories. It is becoming acute as more and more processor cores are integrated into a single chip and demand higher memory bandwidth through limited chip pins. Optical memory interconnection network (OMIN) promises high bandwidth, bandwidth density, and energy efficiency, and can potentially alleviate the memory wall problem. In this paper, we propose an optical inter/intra-chip processor-memory communication architecture, called MOCA. Experimental results and analysis show that MOCA can significantly improve system performance and energy efficiency. For example, comparing to Hybrid Memory Cube (HMC), MOCA can speedup application execution time by 2.6x, reduce communication latency by 75%, and improve energy efficiency by 3.4x for 256-core processors in 7 nm technology.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062286"}, {"primary_key": "3751886", "vector": [], "sparse_vector": [], "title": "A Pathway to Enable Exponential Scaling for the Beyond-CMOS Era: Invited.", "authors": ["<PERSON><PERSON><PERSON>", "Sachin <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many key technologies of our society, including so-called artificial intelligence (AI) and big data, have been enabled by the invention of transistor and its ever-decreasing size and ever-increasing integration at a large scale. However, conventional technologies are confronted with a clear scaling limit. Many recently proposed advanced transistor concepts are also facing an uphill battle in the lab because of necessary performance tradeoffs and limited scaling potential. We argue for a new pathway that could enable exponential scaling for multiple generations. This pathway involves layering multiple technologies that enable new functions beyond those available from conventional and newly proposed transistors. The key principles for this new pathway have been demonstrated through an interdisciplinary team effort at C-SPIN (a STARnet center), where systems designers, device builders, materials scientists and physicists have all worked under one umbrella to overcome key technology barriers. This paper reviews several successful outcomes from this effort on topics such as the spin memory, logic-in-memory, cognitive computing, stochastic and probabilistic computing and reconfigurable information processing.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072942"}, {"primary_key": "3751887", "vector": [], "sparse_vector": [], "title": "Group Scissor: Scaling Neuromorphic Computing Design to Large Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (<PERSON>) <PERSON>"], "summary": "Synapse crossbar is an elementary structure in neuromorphic computing systems (NCS). However, the limited size of crossbars and heavy routing congestion impede the NCS implementation of large neural networks. In this paper, we propose a two-step framework (namely, group scissor) to scale NCS designs to large neural networks. The first step rank clipping integrates low-rank approximation into the training to reduce total crossbar area. The second step is group connection deletion, which structurally prunes connections to reduce routing congestion between crossbars. Tested on convolutional neural networks of LeNet on MNIST database and ConvNet on CIFAR-10 database, our experiments show significant reduction of crossbar and routing area in NCS designs. Without accuracy loss, rank clipping reduces the total crossbar area to 13.62% or 51.81% in the NCS design of LeNet or ConvNet, respectively. The following group connection deletion further decreases the routing area of LeNet or ConvNet to 8.1% or 52.06%, respectively.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062256"}, {"primary_key": "3751888", "vector": [], "sparse_vector": [], "title": "Efficient Bayesian Yield Optimization Approach for Analog and SRAM Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Chang<PERSON> Yan", "<PERSON><PERSON>", "Xiangdong Hu"], "summary": "Conventional yield optimization approaches rely on accurate yield estimation for given design parameters, which would be computational intensive. In this paper, a novel Bayesian yield optimization approach is proposed for analog and SRAM circuits. An equivalent a problem is formulated via applying <PERSON><PERSON>' theorem on the augmented yield problem. The yield optimization problem is converted to identifying the design parameters with maximal probability density conditioning on the event that the corresponding circuit is \"pass\". Gaussian kernel density estimation is employed to approximate the conditional probability, and a multi-start-point based EM-like algorithm is proposed to solve the equivalent problem efficiently Compared with the state-of-the-art yield optimization approaches, the proposed method can significantly reduce the number of circuit simulations with comparable optimization accuracy by avoiding repetitive yield estimations.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062234"}, {"primary_key": "3751889", "vector": [], "sparse_vector": [], "title": "Retiming of Two-Phase Latch-Based Resilient Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Timing resilient design has shown significant promise in mitigating the excess margins associated with rare worst-case data and increased process, voltage, and temperature (PVT) variations. However, resilient circuits need error detecting sequential logic (EDL) to detect timing errors which represents area and power overhead. This article proposes a new network-simplex-based retiming method for two-phase latch-based resilient circuits to reduce the overhead of the combination of normal and error detecting latches. Our experimental results show that our method is computationally efficient and reduces the cost of the sequential elements by an average of up to 20% when compared to traditional methods.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062312"}, {"primary_key": "3751890", "vector": [], "sparse_vector": [], "title": "Convergence-Boosted Graph Partitioning using Maximum Spanning Trees for Iterative Solution of Large Linear Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ability to solve large linear systems efficiently has been a key part of building a successful circuit simulator such as ones for power grid analysis. Partition-based iterative methods have been widely adopted due to their divide-and-conquer nature and amenability to parallel implementation. However such methods rely heavily on the quality of circuit graph partitioning, and it is extremely challenging to develop a robust partitioning scheme that always generates near-optimal results. In this paper, we present a new line of thinking by integrating two rather distinct schools of iterative methods: partitioning based and support graph based. The former enjoys ease of parallelization, however, lacks a direct control of the numerical properties of the produced partitions. In contrast, the latter operates on the maximum spanning tree (MST) of the circuit graph, which is optimized for fast numerical convergence, but is bottlenecked by its difficulty of parallelization. By combining the two, we propose a partitioning-based preconditioner based on the theory of support graph. The circuit partitioning is guided by the MST of the underlying circuit graph, offering essential guidance for achieving fast convergence. The resulting block-Jacobi-like preconditioner maximizes the numerical benefit inherited from support graph theory while lending itself to straightforward parallelization as a partition-based method. The experimental results on IBM power grid suite and synthetic power grid benchmarks show that our proposed method speeds up the DC simulation by up to 11.5X over an state-of-the-art direct solver.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062215"}, {"primary_key": "3751891", "vector": [], "sparse_vector": [], "title": "Exploiting Parallelism for Convolutional Connections in Processing-In-Memory Architecture.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep convolutional neural networks (CNNs) are widely adopted in intelligent systems with unprecedented accuracy but at the cost of a substantial amount of data movement. Although recent development in processing-in-memory (PIM) architecture seeks to minimize data movement by computing the data at the dedicated nonvolatile device, how to jointly explore the computation capability of PIM and utilize the highly parallel property of neural network remains a critical issue.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062242"}, {"primary_key": "3751892", "vector": [], "sparse_vector": [], "title": "Deep Reinforcement Learning for Building HVAC Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Buildings account for nearly 40% of the total energy consumption in the United States, about half of which is used by the HVAC (heating, ventilation, and air conditioning) system. Intelligent scheduling of building HVAC systems has the potential to significantly reduce the energy cost. However, the traditional rule-based and model-based strategies are often inefficient in practice, due to the complexity in building thermal dynamics and heterogeneous environment disturbances. In this work, we develop a data-driven approach that leverages the deep reinforcement learning (DRL) technique, to intelligently learn the effective strategy for operating the building HVAC systems. We evaluate the performance of our DRL algorithm through simulations using the widely-adopted EnergyPlus tool. Experiments demonstrate that our DRL-based algorithm is more effective in energy cost reduction compared with the traditional rule-based approach, while maintaining the room temperature within desired range.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062224"}, {"primary_key": "3751893", "vector": [], "sparse_vector": [], "title": "Automated Systolic Array Architecture Synthesis for High Throughput CNN Inference on FPGAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Convolutional neural networks (CNNs) have been widely applied in many deep learning applications. In recent years, the FPGA implementation for CNNs has attracted much attention because of its high performance and energy efficiency. However, existing implementations have difficulty to fully leverage the computation power of the latest FPGAs. In this paper we implement CNN on an FPGA using a systolic array architecture, which can achieve high clock frequency under high resource utilization. We provide an analytical model for performance and resource utilization and develop an automatic design space exploration framework, as well as source-to-source code transformation from a C program to a CNN implementation using systolic array. The experimental results show that our framework is able to generate the accelerator for real-life CNN models, achieving up to 461 GFlops for floating point data type and 1.2 Tops for 8-16 bit fixed point.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062207"}, {"primary_key": "3751894", "vector": [], "sparse_vector": [], "title": "Safety Guard: Runtime Enforcement for Safety-Critical Cyber-Physical Systems: Invited.", "authors": ["<PERSON><PERSON>", "Haibo Zeng", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to their safety-critical nature, cyber-physical systems (CPS) must tolerate faults and security attacks to remain fail-operational. However, conventional techniques for improving safety, such as testing and validation, do not meet this requirement, as shown by many of the real-world system failures in recent years, often with major economic and public-safety implications. We aim to improve the safety of critical CPS through synthesis of runtime enforcers, named safety guards, which are reactive components attached to the original systems to protect them against catastrophic failures. That is, even if the system occasionally malfunctions due to unknown defects, transient errors, or malicious attacks, the guard always reacts instantaneously to ensure that the combined system satisfies a predefined set of safety properties, and the deviation from the original system is kept at minimum. We illustrate the main ideas of this approach with examples, discuss the advantages compared to existing approaches, and point out some research challenges.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072957"}, {"primary_key": "3751895", "vector": [], "sparse_vector": [], "title": "Fault-Tolerant Training with On-Line Fault Detection for RRAM-Based Neural Computing Systems.", "authors": ["<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An RRAM-based computing system (RCS) is an attractive hardware platform for implementing neural computing algorithms. Online training for RCS enables hardware-based learning for a given application and reduces the additional error caused by device parameter variations. However, a high occurrence rate of hard faults due to immature fabrication processes and limited write endurance restrict the applicability of on-line training for RCS. We propose a fault-tolerant on-line training method that alternates between a fault-detection phase and a fault-tolerant training phase. In the fault-detection phase, a quiescent-voltage comparison method is utilized. In the training phase, a threshold-training method and a re-mapping scheme is proposed. Our results show that, compared to neural computing without fault tolerance, the recognition accuracy for the Cifar-10 dataset improves from 37% to 83% when using low-endurance RRAM cells, and from 63% to 76% when using RRAM cells with high endurance but a high percentage of initial faults.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062248"}, {"primary_key": "3751896", "vector": [], "sparse_vector": [], "title": "Exploring Heterogeneous Algorithms for Accelerating Deep Convolutional Neural Networks on FPGAs.", "authors": ["Qingcheng Xiao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yu-Wing Tai"], "summary": "Convolutional neural network (CNN) finds applications in a variety of computer vision applications ranging from object recognition and detection to scene understanding owing to its exceptional accuracy. There exist different algorithms for CNNs computation. In this paper, we explore conventional convolution algorithm with a faster algorithm using Winograd's minimal filtering theory for efficient FPGA implementation. Distinct from the conventional convolution algorithm, Winograd algorithm uses less computing resources but puts more pressure on the memory bandwidth. We first propose a fusion architecture that can fuse multiple layers naturally in CNNs, reusing the intermediate data. Based on this fusion architecture, we explore heterogeneous algorithms to maximize the throughput of a CNN. We design an optimal algorithm to determine the fusion and algorithm strategy for each layer. We also develop an automated toolchain to ease the mapping from Caffe model to FPGA bitstream using Vivado HLS. Experiments using widely used VGG and AlexNet demonstrate that our design achieves up to 1.99X performance speedup compared to the prior fusion-based FPGA accelerator for CNNs.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062244"}, {"primary_key": "3751897", "vector": [], "sparse_vector": [], "title": "Delay Locking: Security Enhancement of Logic Locking against IC Counterfeiting and Overproduction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logic locking is a technique that has been proposed to thwart IC counterfeiting and overproduction by untrusted foundry. Recently, the security of logic locking is threatened by a new attack called SAT attack, which can effectively decipher the correct key of most logic locking techniques. In this paper, we propose a new technique called delay locking to enhance the security of existing logic locking techniques. For delay locking, the key into a locked circuit not only determines its functionality, but also its timing profile. A functionality-correct but timing-incorrect key will result in timing violations and thus making the circuit malfunction.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062226"}, {"primary_key": "3751898", "vector": [], "sparse_vector": [], "title": "Concurrent Pin Access Optimization for Unidirectional Routing.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In advanced technology nodes, standard cell pin access is becoming challenging due to a small number of routing tracks and complex design-for-manufacturing constraints. Pin access interference is further exacerbated by unidirectional routing, which is highly preferred to enable high-density metal patterns and comply with self-aligned multiple patterning solutions. Previous manufacturing-aware routing studies simply depend on the router or sequential planning schemes to resolve pin access interference, which introduces significant overhead on solution qualities. Therefore, we propose concurrent pin access optimization techniques to achieve fast and high-quality routing solutions. The concurrent pin access optimization is modeled as a weighted interval assignment problem, which is solved by an optimal integer linear programming formulation and a scalable Lagrangian relaxation algorithm. A concurrent pin access router is implemented while accommodating advanced manufacturing constraints, which outperforms state-of-the-art manufacturing-aware routers with better routability, fewer vias and faster runtime.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062214"}, {"primary_key": "3751899", "vector": [], "sparse_vector": [], "title": "A Scaling Compatible, Synthesis Friendly VCO-based Delta-sigma ADC Design and Synthesis Methodology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Conventional analog/mixed-signal (AMS) circuits design methodology relying heavily on the use of operational amplifiers (opamps) to process signals in voltage-domain (VD) encounters severe difficulties in advanced nanometer-scale CMOS process. We present a novel scaling compatible, synthesis friendly ring voltage-controlled oscillator (VCO) based time-domain (TD) delta-sigma analog-to-digital converter (ADC) whose performance improves as technology advances. Decomposed into digital gates (e.g. inverters) and a small set of simple customized cells (e.g. resistors), its layout is fully synthesizable by leveraging digital layout synthesis tools. Post-layout simulation results demonstrate the scaling compatibility of the proposed ADC and a drastic boost to design productivity.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062192"}, {"primary_key": "3751900", "vector": [], "sparse_vector": [], "title": "On Quality Trade-off Control for Approximate Computing Using Iterative Training.", "authors": ["<PERSON><PERSON>", "Xiangyu Wu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Naifeng Jing", "<PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "Quality control plays a key role in approximate computing to save the energy and guarantee that the quality of the computation outcome satisfies users' requirement. Previous works proposed a hybrid architecture, composed of a classifier for error prediction and an approximate accelerator for approximate computing using well trained neural-networks. Only inputs predicted to meet the quality are executed by the accelerator. However, the design of this hybrid architecture, relying on one-pass training process, has not been fully explored. In this paper, we propose a novel optimization framework. It advocates an iteratively training process to coordinate the training of the classifier and the accelerator with a judicious selection of training data. It integrates a dynamic threshold tuning algorithm to maximize the invocation of the accelerator (i.e., energy-efficiency) under the quality requirement. At last, we propose an efficient algorithm to explore the topologies of the accelerator and the classifier comprehensively. Experimental results shows significant improvement on the quality and the energy-efficiency compared to the conventional one-pass training method.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062294"}, {"primary_key": "3751901", "vector": [], "sparse_vector": [], "title": "An Efficient Memristor-based Distance Accelerator for Time Series Data Mining on Data Centers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The rapid development of Internet-of-Things (IoT) is yielding a huge volume of time series data, the real-time mining of which becomes a major load for data centers. The computation bottleneck in time series data mining is the distance function, which has been tackled by various software optimization and hardware acceleration techniques recently. However, each of these techniques is only designed or optimized for a specific distance function. To address this problem, in this paper we propose an efficient and reconfigurable memristor-based distance accelerator for real-time and energy-efficient data mining with time series on data centers. Common circuit structure is extracted to save chip areas, and the circuit can be configured to any specific distance functions. Experimental results show that compared with existing works, our work has achieved a speedup of 3.5x-376x on performance and an improvement of 1-3 orders of magnitude on energy efficiency.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062200"}, {"primary_key": "3751902", "vector": [], "sparse_vector": [], "title": "Age-aware Logic and Memory Co-Placement for RRAM-FPGAs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Jingtong Hu"], "summary": "Resistive RAM (RRAM) is a promising non-volatile memory (NVM) device which can replace traditional SRAM as on-chip storage for logic and data in FPGAs. While RRAM outperforms SRAM by offering high scalability, low leakage power, and near-zero power-on delay, RRAM-FPGAs have limited programming cycles, and different writes frequencies of memory and logic blocks make the challenge more severe. To overcome this endurance challenge, we propose an age-aware placement framework for RRAM-FPGAs with uniform reconfigurable logic/memory units. The framework, consisting of a dynamic reconfiguration region allocation algorithm and a logic/memory co-placement algorithm, balances write distributions across the entire FPGA according to logic and memory write frequency differences. The proposed algorithms have been integrated into the VTR synthesis flow. Experiments show that the framework achieves 94.9% write reduction, thus effectively extending RRAM-FPGA programming cycles.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062198"}, {"primary_key": "3751903", "vector": [], "sparse_vector": [], "title": "Phase-driven Learning-based Dynamic Reliability Management For Multi-core Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Tiantao Lu", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a phase-driven Q-learning based dynamic reliability management (DRM) technique for multi-core processors to solve DRM problems of maximizing the processor performance subject to a large class of reliability constraints by turning ON/OFF cores and dynamic voltage frequency scaling. Our technique utilizes the existing methods to detect program phases (i.e. [17]) and learns (rather than obtaining at the off-line stage) the optimal configuration of the multi-core processor for each phase. Our technique outperforms the existing learning-based DRM methods in managing programs with highly diverse phases. Our proposed technique is evaluated by solving a DRM problem in 3D CPUs of maximizing processor performance subject to the electromigration induced power delivery network reliability constraint. Compared to the latest Q-learning based DRM technique [11], our method can achieve more than 1.3x improvement in performance with 77% memory savings.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062301"}, {"primary_key": "3751904", "vector": [], "sparse_vector": [], "title": "Layout Hotspot Detection with Feature Tensor Generation and Deep Biased Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Detecting layout hotspots is one of the key problems in physical verification flow. Although machine learning solutions show benefits over lithography simulation and pattern matching based methods, it is still hard to select a proper model for large scale problems and it is inevitable that performance degradation will occur. To overcome these issues, in this paper we develop a deep learning framework for high performance and large scale hotspot detection. First, feature tensor generation is proposed to extract representative layout features that fit well with convolutional neural networks while keeping the spatial relationship of the original layout pattern with minimal information loss. Second, we propose a biased learning algorithm to train the convolutional neural network to further improve detection accuracy with small false alarm penalties. Experimental results show that our framework outperforms previous machine learning-based hotspot detectors in both the ICCAD 2012 Contest benchmarks and large scale industrial benchmarks.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062270"}, {"primary_key": "3751905", "vector": [], "sparse_vector": [], "title": "Disturbance Aware Memory Partitioning for Parallel Data Access in STT-RAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spin-transfer torque random access memory (STT-RAM) has been proposed to be an excellent candidate for substituting traditional memory due to its fascinating features such as high density and low power. Memory partitioning is an efficient strategy to overcome the obstacle of memory bandwidth limiting speed of parallel data access. However, the performance is unsatisfactory, while previous memory partitioning methods are applied to STT-RAM, since they have no regard for the problem of read disturbance. In this paper, a disturbance aware memory partitioning (DaMP) method for STT-RAM is proposed. The experimental results show DaMP outperforms state-of-the-art method in terms of bank number, storage overhead, performance and searching speed.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062232"}, {"primary_key": "3751906", "vector": [], "sparse_vector": [], "title": "A Novel ReRAM-based Main Memory Structure for Optimizing Access Latency and Reliability.", "authors": ["<PERSON>", "<PERSON>", "Jing<PERSON> Liu", "<PERSON>", "<PERSON>", "Caihua Fang"], "summary": "Emerging Resistive Memory (ReRAM) is a promising candidate as the replacement for DRAM because of its low power consumption, high density and high endurance. Due to the unique crossbar structure, ReRAM can be constructed with a very high density. However, ReRAM's crossbar structure causes an IR drop problem which results in non-uniform access latency in ReRAM banks and reduces its reliability. Besides, the access latency and reliability of ReRAM arrays are greatly influenced by the data patterns involved in a write operation. In this paper, we propose a performance and reliability efficient ReRAM-based main memory structure. At the circuit level, we propose a double-sided write driver design to reduce the IR drops along bitlines. At the architecture level, a region partition with address remapping method and two flip schemes are proposed to reduce the access latency and improve the reliability of ReRAM arrays. The experimental results show that the proposed design can improve the system performance by 30.3% on average and reduce the memory access latency by 25.9% on average over an aggressive baseline, meanwhile the design improves the reliability of ReRAM-based memory system.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062191"}, {"primary_key": "3751907", "vector": [], "sparse_vector": [], "title": "Toss-up Wear Leveling: Protecting Phase-Change Memories from Inconsistent Write Patterns.", "authors": ["<PERSON><PERSON>", "Guangyu Sun"], "summary": "Limited write endurance is one of major obstacles to adopt Phase Change Memories (PCMs) in practice as future main memory. Considering process variation (PV) and non-uniform write intensity, PCM cells with low endurance (i.e. weak cells) can wear out in seconds under intensive writes. To prolong PCMs' lifetime, many PV-aware wear leveling schemes have been proposed following a common idea: intensive writes are predicted and allocated to cells with high endurance (i.e. strong cells) based on the write intensity distribution, which should be consistent at predicted intervals. However, we discover that this idea leaves a serious vulnerability against a malicious program, which is designed to have an inconsistent write intensity distribution. Prior wear-leveling schemes can even be leveraged to speed up wearing out weak cells. To counteract this attack, we propose Toss-up Wear Leveling (TWL), a novel scheme that randomly allocates writes between two bond blocks discounting the consistency of write distribution. Experiment results demonstrate that, compared to prior works, TWL can improve lifetime substantially with negligible overhead in performance and hardware cost.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062329"}, {"primary_key": "3751908", "vector": [], "sparse_vector": [], "title": "A Spectral Graph Sparsification Approach to Scalable Vectorless Power Grid Integrity Verification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vectorless integrity verification is becoming increasingly critical to robust design of nanoscale power delivery networks (PDNs). To dramatically improve efficiency and capability of vectorless integrity verifications, this paper introduces a scalable multilevel integrity verification framework by leveraging a hierarchy of almost linear-sized spectral power grid sparsifiers that can well retain effective resistances between nodes, as well as a recent graph-theoretic algebraic multigrid (AMG) algorithmic framework. As a result, vectorless integrity verification solution obtained on coarse level problems can effectively help find the solution of the original problem. Extensive experimental results show that the proposed vectorless verification framework can always efficiently and accurately obtain worst-case scenarios in even very large power grid designs.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062193"}, {"primary_key": "3751909", "vector": [], "sparse_vector": [], "title": "Design Methodology for Thin-Film Transistor Based Pseudo-CMOS Logic Array with Multi-Layer Interconnect Architecture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Wenyu Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thin-film transistor (TFT) circuits are important for flexible electronics which are promising in the area of wearable devices. However, most TFT technologies only have unipolar devices and the process variation and defective rate are relatively high, which impose challenges to TFT circuit design. In this paper, we propose a novel logic array based on pseudo-CMOS logic to address the problem of unipolar TFT circuit design. A multi-layer interconnect architecture and wire routing methodology are presented to improve the routability and meanwhile the area efficiency. The experimental results show that the proposed logic array reduces more than 80% area compared with transistor level scheme.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062227"}, {"primary_key": "3751910", "vector": [], "sparse_vector": [], "title": "Secure and Reliable XOR Arbiter PUF Design: An Experimental Study based on 1 Trillion Challenge Response Pair Measurements.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper shows that performing an XOR operation between the outputs of parallel arbiter PUFs generates a more secure output at the expense of reduced stability. In this work, we evaluate the security and stability of XOR PUFs using 1,000,000 randomly chosen challenges, applied to 10 custom-designed PUF chips, tested for 100,000 cycles per challenge, under different voltage and temperature conditions. Based on extensive hardware data, we propose a practical method for selecting challenges that will produce stable responses. A linear regression approach based on soft responses collected during enrollment phase was used to build accurate models for each individual arbiter PUF. Hardware data from fabricated chips verify that the approach is highly effective.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062315"}, {"primary_key": "3751911", "vector": [], "sparse_vector": [], "title": "SmartSwap: High-Performance and User Experience Friendly Swapping in Mobile Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jinting Ren", "<PERSON>"], "summary": "With high-performance mobile processors and large main memory, smartphones are now integrated with more applications and richer functionality than ever. This poses larger memory and storage space demands, however, most mobile systems have limited memory space, which in turn affects user satisfaction. For example, application response time could become longer due to limited memory capacity. Swapping is an effective way to extend memory capacity, but often lead to poor performance in smartphones.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062317"}, {"primary_key": "3751912", "vector": [], "sparse_vector": [], "title": "Extensibility-Driven Automotive In-Vehicle Architecture Design: Invited.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasingly more software-based applications are being developed and deployed in modern vehicles. As a result, the extensibility of a system design has become an important issue in order to accommodate more future applications and update of existing ones on one hand and reduce the effort and cost of re-design, test and validation on the other. In this paper, we discuss the extensibility-driven design in the automotive E/E architecture. We explain the motivation for such a design objective and discuss the definition of extensibility metric and extensibility-driven design methods under two different setting, namely the system based on CAN bus and FlexRay bus. Based on these two examples, we illustrate the importance and advantages of extensibility-driven design in the automotive E/E architecture.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3072956"}, {"primary_key": "3751913", "vector": [], "sparse_vector": [], "title": "Ivory: Early-Stage Design Space Exploration Tool for Integrated Voltage Regulators.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Yazhou Zu", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite being employed in burgeoning efforts to improve power delivery efficiency, integrated voltage regulators (IVRs) have yet to be evaluated in a rigorous, systematic, or quantitative manner. To fulfill this need, we present Ivory, a high-level design space exploration tool capable of providing accurate conversion efficiency, static performance characteristics, and dynamic transient responses of an IVR-enabled power delivery subsystem (PDS), enabling rapid trade-off exploration at early design stage, approximately 1000x faster than SPICE simulation. We demonstrate and validate Ivory with a wide spectrum of IVR topologies. In addition, we present a case study using Ivory to reveal the optimal PDS configurations, with underlying power break-downs and area overheads for the GPU manycore architecture, which has yet to embrace IVRs.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062268"}, {"primary_key": "3751914", "vector": [], "sparse_vector": [], "title": "Accurate High-level Modeling and Automated Hardware/Software Co-design for Effective SoC Design Space Exploration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A desirable feature of a development tool for SoC design is that, given the important applications in the domain to be targeted by the SoC, a powerful hardware-software partitioning engine is available to determine which function(s) shall be mapped to hardware. However, to provide high-quality partitioning, this engine must be able to consider a rich design space of possible alternate hardware and software implementations for each program region candidate for hardware acceleration, in turn making the task of finding the optimal mapping very difficult given the number of design points to consider and the need for accurate modeling of latency, power and area.", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639.3062195"}, {"primary_key": "3883207", "vector": [], "sparse_vector": [], "title": "Proceedings of the 54th Annual Design Automation Conference, DAC 2017, Austin, TX, USA, June 18-22, 2017", "authors": [], "summary": "Resistive RAM (RRAM) is a promising non-volatile memory (NVM) device which can replace traditional SRAM as on-chip storage for logic and data in FPGAs. While RRAM outperforms SRAM by offering high scalability, low leakage power, and near-zero power-on ...", "published": "2017-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3061639"}]