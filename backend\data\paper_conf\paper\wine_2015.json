[{"primary_key": "4513709", "vector": [], "sparse_vector": [], "title": "Sequential Posted Price Mechanisms with Correlated Valuations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the revenue performance of sequential posted price mechanisms and some natural extensions, for a general setting where the valuations of the buyers are drawn from a correlated distribution. Sequential posted price mechanisms are conceptually simple mechanisms that work by proposing a \"take-it-or-leave-it\" offer to each buyer. We apply sequential posted price mechanisms to single-parameter multi-unit settings in which each buyer demands only one item and the mechanism can assign the service to at most k of the buyers. For standard sequential posted price mechanisms, we prove that with the valuation distribution having finite support, no sequential posted price mechanism can extract a constant fraction of the optimal expected revenue, even with unlimited supply. We extend this result to the case of a continuous valuation distribution when various standard assumptions hold simultaneously. In fact, it turns out that the best fraction of the optimal revenue that is extractable by a sequential posted price mechanism is proportional to the ratio of the highest and lowest possible valuation. We prove that for two simple generalizations of these mechanisms, a better revenue performance can be achieved: if the sequential posted price mechanism has for each buyer the option of either proposing an offer or asking the buyer for its valuation, then a $$\\varOmega (1/\\max \\{1,d\\})$$ fraction of the optimal revenue can be extracted, where d denotes the \"degree of dependence\" of the valuations, ranging from complete independence ( $$d=0$$ ) to arbitrary dependence ( $$d = n-1$$ ). When we generalize the sequential posted price mechanisms further, such that the mechanism has the ability to make a take-it-or-leave-it offer to the i-th buyer that depends on the valuations of all buyers except i, we prove that a constant fraction $$(2 - \\sqrt{e})/4 \\approx 0.088$$ of the optimal revenue can be always extracted.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_1"}, {"primary_key": "4513710", "vector": [], "sparse_vector": [], "title": "Price Competition in Networked Markets: How Do Monopolies Impact Social Welfare?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the efficiency of allocations in large markets with a network structure where every seller owns an edge in a graph and every buyer desires a path connecting some nodes. While it is known that stable allocations can be very inefficient, the exact properties of equilibria in markets with multiple sellers are not fully understood, even in single-source single-sink networks. In this work, we show that for a large class of buyer demand functions, equilibrium always exists and allocations can often be close to optimal. In the process, we characterize the structure and properties of equilibria using techniques from min-cost flows, and obtain tight bounds on efficiency in terms of the various parameters governing the market, especially the number of monopoliesM. Although monopolies can cause large inefficiencies in general, our main results for single-source single-sink networks indicate that for several natural demand functions the efficiency only drops linearly withM. For example, for concave demand we prove that the efficiency loss is at most a factor\\(1+\\frac{M}{2}\\)from the optimum, for demand with monotone hazard rate it is at most\\(1+M\\), and for polynomial demand the efficiency decreases logarithmically withM. In contrast to previous work that showed that monopolies may adversely affect welfare, our main contribution is showing that monopolies may not be as ‘evil’ as they are made out to be. Finally, we consider more general, multiple-source networks and show that in the absence of monopolies, mild assumptions on the network topology guarantee an equilibrium that maximizes social welfare.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_2"}, {"primary_key": "4513711", "vector": [], "sparse_vector": [], "title": "Computing Stable Coalitions: Approximation Algorithms for Reward Sharing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Consider a setting where selfish agents are to be assigned to coalitions or projects from a set $$\\mathcal {P}$$ . Each project $$k\\in \\mathcal {P}$$ is characterized by a valuation function; $$v_k(S)$$ is the value generated by a set S of agents working on project k. We study the following classic problem in this setting: \"how should the agents divide the value that they collectively create?\". One traditional approach in cooperative game theory is to study core stability with the implicit assumption that there are infinite copies of one project, and agents can partition themselves into any number of coalitions. In contrast, we consider a model with a finite number of non-identical projects; this makes computing both high-welfare solutions and core payments highly non-trivial. The main contribution of this paper is a black-box mechanism that reduces the problem of computing a near-optimal core stable solution to the well-studied algorithmic problem of welfare maximization; we apply this to compute an approximately core stable solution that extracts one-fourth of the optimal social welfare for the class of subadditive valuations. We also show much stronger results for several popular sub-classes: anonymous, fractionally subadditive, and submodular valuations, as well as provide new approximation algorithms for welfare maximization with anonymous functions. Finally, we establish a connection between our setting and simultaneous auctions with item bidding; we adapt our results to compute approximate pure Nash equilibria for these auctions.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_3"}, {"primary_key": "4513712", "vector": [], "sparse_vector": [], "title": "The (Non)-Existence of Stable Mechanisms in Incomplete Information Environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider two-sided matching markets, and study the incentives of agents to circumvent a centralized clearing house by signing binding contracts with one another. It is well-known that if the clearing house implements a stable match and preferences are known, then no group of agents can profitably deviate in this manner. We ask whether this property holds even when agents haveincomplete informationabout their own preferences or the preferences of others. We find that it does not. In particular, when agents are uncertain about the preferences of others,everymechanism is susceptible to deviations by groups of agents. When, in addition, agents are uncertain about theirownpreferences, every mechanism is susceptible to deviations in which a single pair of agents agrees in advance to match to each other.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_4"}, {"primary_key": "4513713", "vector": [], "sparse_vector": [], "title": "Fast Convergence in the Double Oral Auction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>kes<PERSON> V<PERSON>"], "summary": "A classical trading experiment consists of a set of unit demand buyers and unit supply sellers with identical items. Each agent's value or opportunity cost for the item is their private information and preferences are quasi-linear. Trade between agents employs a double oral auction (DOA) in which both buyers and sellers call out bids or offers which an auctioneer recognizes. Transactions resulting from accepted bids and offers are recorded. This continues until there are no more acceptable bids or offers. Remarkably, the experiment consistently terminates in a Walrasian price. The main result of this paper is a mechanism in the spirit of the DOA that converges to a Walrasian equilibrium in a polynomial number of steps, thus providing a theoretical basis for the above-described empirical phenomenon. It is well-known that computation of a Walrasian equilibrium for this market corresponds to solving a maximum weight bipartite matching problem. The uncoordinated but rational responses of agents thus solve in a distributed fashion a maximum weight bipartite matching problem that is encoded by their private valuations. We show, furthermore, that every Walrasian equilibrium is reachable by some sequence of responses. This is in contrast to the well known auction algorithms for this problem which only allow one side to make offers and thus essentially choose an equilibrium that maximizes the surplus for the side making offers. Our results extend to the setting where not every agent pair is allowed to trade with each other.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_5"}, {"primary_key": "4513714", "vector": [], "sparse_vector": [], "title": "Minority Becomes Majority in Social Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is often observed that agents tend to imitate the behavior of their neighbors in a social network. This imitating behavior might lead to the strategic decision of adopting a public behavior that differs from what the agent believes is the right one and this can subvert the behavior of the population as a whole. In this paper, we consider the case in which agents express preferences over two alternatives and model social pressure with themajoritydynamics: at each step an agent is selected and its preference is replaced by the majority of the preferences of her neighbors. In case of a tie, the agent does not change her current preference. A profile of the agents’ preferences isstableif the each agent’s preference coincides with the preference of at least half of the neighbors (thus, the system is in equilibrium). We ask whether there are network topologies that are robust to social pressure. That is, we ask whether there are graphs in which the majority of preferences in an initial profile\\({\\mathbf {s}}\\)always coincides with the majority of the preference in all stable profiles reachable from\\({\\mathbf {s}}\\). We completely characterize the graphs with this robustness property by showing that this is possible only if the graph has no edge or is a clique or very close to a clique. In other words, except for this handful of graphs, every graph admits at least one initial profile of preferences in which the majority dynamics can subvert the initial majority. We also show that deciding whether a graph admits a minority that becomes majority is NP-hard when the minority size is at most 1 / 4-th of the social network size.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_6"}, {"primary_key": "4513715", "vector": [], "sparse_vector": [], "title": "New Complexity Results and Algorithms for the Minimum Tollbooth Problem.", "authors": ["<PERSON><PERSON><PERSON>", "Thanasis <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The inefficiency of the Wardrop equilibrium of nonatomic routing games can be eliminated by placing tolls on the edges of a network so that the socially optimal flow is induced as an equilibrium flow. A solution where the minimum number of edges are tolled may be preferable over others due to its ease of implementation in real networks. In this paper we consider the minimum tollbooth ( $${MINTB}$$ ) problem, which seeks social optimum inducing tolls with minimum support. We prove for single commodity networks with linear latencies that the problem is NP-hard to approximate within a factor of 1.1377 through a reduction from the minimum vertex cover problem. Insights from network design motivate us to formulate a new variation of the problem where, in addition to placing tolls, it is allowed to remove unused edges by the social optimum. We prove that this new problem remains NP-hard even for single commodity networks with linear latencies, using a reduction from the partition problem. On the positive side, we give the first exact polynomial solution to the $${MINTB}$$ problem in an important class of graphs—series-parallel graphs. Our algorithm solves $${MINTB}$$ by first tabulating the candidate solutions for subgraphs of the series-parallel network and then combining them optimally.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_7"}, {"primary_key": "4513716", "vector": [], "sparse_vector": [], "title": "Ad Exchange: Envy-Free Auctions with Mediators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ad exchanges are an emerging platform for trading advertisement slots on the web with billions of dollars revenue per year. Every time a user visits a web page, the publisher of that web page can ask an ad exchange to auction off the ad slots on this page to determine which advertisements are shown at which price. Due to the high volume of traffic, ad networks typically act as mediators for individual advertisers at ad exchanges. If multiple advertisers in an ad network are interested in the ad slots of the same auction, the ad network might use a \"local\" auction to resell the obtained ad slots among its advertisers. In this work we want to deepen the theoretical understanding of these new markets by analyzing them from the viewpoint of combinatorial auctions. Prior work studied mostly single-item auctions, while we allow the advertisers to express richer preferences over multiple items. We develop a game-theoretic model for the entanglement of the central auction at the ad exchange with the local auctions at the ad networks. We consider the incentives of all three involved parties and suggest a three-party competitive equilibrium, an extension of the Walrasian equilibrium that ensures envy-freeness for all participants. We show the existence of a three-party competitive equilibrium and a polynomial-time algorithm to find one for gross-substitute bidder valuations.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_8"}, {"primary_key": "4513717", "vector": [], "sparse_vector": [], "title": "Computing Approximate Nash Equilibria in Network Congestion Games with Polynomially Decreasing Cost Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of computing approximate Nash equilibria in monotone congestion games with polynomially decreasing cost functions. This class of games generalizes the one of network congestion games, while polynomially decreasing cost functions also include the fundamental Shapley cost sharing value. We design an algorithm that, given a parameter $$\\gamma >1$$ and a subroutine able to compute $$\\rho $$ -approximate best-responses, outputs a $$\\gamma (1/p+\\rho )$$ -approximate Nash equilibrium, where p is the number of players. The computational complexity of the algorithm heavily depends on the choice of $$\\gamma $$ . In particular, when $$\\gamma \\in O(1)$$ , the complexity is quasi-polynomial, while when $$\\gamma \\in \\varOmega (p^\\epsilon )$$ , for a fixed constant $$\\epsilon >0$$ , it becomes polynomial. Our algorithm provides the first non-trivial approximability results for this class of games and achieves an almost tight performance for network games in directed graphs. On the negative side, we also show that the problem of computing a Nash equilibrium in Shapley network cost sharing games is PLS-complete even in undirected graphs, where previous hardness results where known only in the directed case.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_9"}, {"primary_key": "4513718", "vector": [], "sparse_vector": [], "title": "On Stackelberg Strategies in Affine Congestion Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the efficiency of some Stackelberg strategies in congestion games with affine latency functions. A Stackelberg strategy is an algorithm that chooses a subset of players and assigns them a prescribed strategy with the purpose of mitigating the detrimental effect that the selfish behavior of the remaining uncoordinated players may cause to the overall performance of the system. The efficiency of a Stackelberg strategy is measured in terms of the price of anarchy of the pure Nash equilibria they induce. Three Stackelberg strategies, namely Largest Latency First, Cover and Scale, were already considered in the literature and non-tight upper and lower bounds on their price of anarchy were given. We reconsider these strategies and provide the exact bound on the price of anarchy of both Largest Latency First and Cover and a better upper bound on the price of anarchy of Scale.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_10"}, {"primary_key": "4513719", "vector": [], "sparse_vector": [], "title": "Impartial Selection and the Power of up to Two Choices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study mechanisms that select members of a set of agents based on nominations by other members and that are impartial in the sense that agents cannot influence their own chance of selection. Prior work has shown that deterministic mechanisms for selecting any fixed number of agents are severely limited, whereas randomization allows for the selection of a single agent that in expectation receives at least 1 / 2 of the maximum number of nominations. The bound of 1 / 2 is in fact best possible subject to impartiality. We prove here that the same bound can also be achieved deterministically by sometimes but not always selecting a second agent. We then show a separation between randomized mechanisms that make exactly two or up to two choices, and give upper and lower bounds on the performance of mechanisms allowed more than two choices.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_11"}, {"primary_key": "4513720", "vector": [], "sparse_vector": [], "title": "Online Allocation and Pricing with Economies of Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Allocating multiple goods to customers in a way that maximizes some desired objective is a fundamental part of Algorithmic Mechanism Design. We consider here the problem of offline and online allocation of goods that have economies of scale, or decreasing marginal cost per item for the seller. In particular, we analyze the case where customers have unit-demand and arrive one at a time with valuations on items, sampled iid from some unknown underlying distribution over valuations. Our strategy operates by using an initial sample to learn enough about the distribution to determine how best to allocate to future customers, together with an analysis of structural properties of optimal solutions that allow for uniform convergence analysis. We show, for instance, if customers have $$\\{0,1\\}$$ valuations over items, and the goal of the allocator is to give each customer an item he or she values, we can efficiently produce such an allocation with cost at most a constant factor greater than the minimum over such allocations in hindsight, so long as the marginal costs do not decrease too rapidly. We also give a bicriteria approximation to social welfare for the case of more general valuation functions when the allocator is budget constrained.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_12"}, {"primary_key": "4513721", "vector": [], "sparse_vector": [], "title": "Multilateral Deferred-Acceptance Mechanisms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the design of multilateral markets, where agents with several different roles engage in trade. We first observe that the modular approach proposed by <PERSON><PERSON><PERSON> et al. [5] for bilateral markets can also be applied in multilateral markets. This gives a general method to design Deferred Acceptance mechanisms in such settings; these mechanisms, defined by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [10], are known to satisfy some highly desired properties. We then show applications of this framework in the context of supply chains. We show how existing mechanisms can be implemented as multilateral Deferred Acceptance mechanisms, and thus exhibit nice practical properties (as group strategy-proofness and equivalence to clock auctions). We use the general framework to design a novel mechanism that improves upon previous mechanisms in terms of social welfare. Our mechanism manages to avoid \"trade reduction\" in some scenarios, while maintaining the incentive and budget-balance properties.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_13"}, {"primary_key": "4513722", "vector": [], "sparse_vector": [], "title": "Testing Consumer Rationality Using Perfect Graphs and Oriented Discs.", "authors": ["Shan<PERSON>", "<PERSON>"], "summary": "Given a consumer data-set, the axioms of revealed preference proffer a binary test for rational behaviour. A natural (non-binary) measure of the degree of rationality exhibited by the consumer is the minimum number of data points whose removal induces a rationalisable data-set. We study the computational complexity of the resultant consumer rationality problem in this paper. This problem is, in the worst case, equivalent (in terms of approximation) to the directed feedback vertex set problem. Our main result is to obtain an exact threshold on the number of commodities that separates easy cases and hard cases. Specifically, for two-commodity markets the consumer rationality problem is polynomial time solvable; we prove this via a reduction to the vertex cover problem on perfect graphs. For three-commodity markets, however, the problem is NP-complete; we prove this using a reduction from planar 3-sat that is based upon oriented-disc drawings.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_14"}, {"primary_key": "4513723", "vector": [], "sparse_vector": [], "title": "Computation of Stackelberg Equilibria of Finite Sequential Games.", "authors": ["<PERSON><PERSON><PERSON>", "Simina Brânzei", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Stackelberg equilibrium is a solution concept that describes optimal strategies to commit to: Player~1 (the leader) first commits to a strategy that is publicly announced, then Player~2 (the follower) plays a best response to the leader's choice. We study Stackelberg equilibria in finite sequential (i.e., extensive-form) games and provide new exact algorithms, approximate algorithms, and hardness results for finding equilibria for several classes of such two-player games.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_15"}, {"primary_key": "4513724", "vector": [], "sparse_vector": [], "title": "Welfare and Rationality Guarantees for the Simultaneous Multiple-Round Ascending Auction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The simultaneous multiple-round auction (SMRA) and the combinatorial clock auction (CCA) are the two primary mechanisms used to sell bandwidth. Under truthful bidding, the SMRA is known to output a Walrasian equilibrium that maximizes social welfare provided the bidder valuation functions satisfy the gross substitutes property. Recently, it was shown that the combinatorial clock auction (CCA) provides good welfare guarantees for general classes of valuation functions. This motivates the question of whether similar welfare guarantees hold for the SMRA in the case of general valuation functions. We show the answer is no. But we prove that good welfare guarantees still arise if the degree of complementarities in the bidder valuations are bounded. In particular, if bidder valuations functions are $\\alpha$-near-submodular then, under truthful bidding, the SMRA has a welfare ratio (the worst case ratio between the social welfare of the optimal allocation and the auction allocation) of at most $(1+\\alpha)$. The special case of submodular valuations, namely $\\alpha=1$, and produces individually rational solutions. However, for $\\alpha>1$, this is a bicriteria guarantee, to obtain good welfare under truthful bidding requires relaxing individual rationality. Finally, we examine what strategies are required to ensure individual rationality in the SMRA with general valuation functions. First, we provide a weak characterization, namely \\emph{secure bidding}, for individual rationality. We then show that if the bidders use a profit-maximizing secure bidding strategy the welfare ratio is at most $1+\\alpha$. Consequently, by bidding securely, it is possible to obtain the same welfare guarantees as truthful bidding without the loss of individual rationality.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_16"}, {"primary_key": "4513725", "vector": [], "sparse_vector": [], "title": "Combinatorial Auctions with Conflict-Based Externalities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Combinatorial auctions (CA) are a well-studied area in algorithmic mechanism design. However, contrary to the standard model, empirical studies suggest that a bidder's valuation often does not depend solely on the goods assigned to him. For instance, in adwords auctions an advertiser might not want his ads to be displayed next to his competitors' ads. In this paper, we propose and analyze several natural graph-theoretic models that incorporate such negative externalities, in which bidders form a directed conflict graph with maximum out-degree $$\\varDelta $$ . We design algorithms and truthful mechanisms for social welfare maximization that attain approximation ratios depending on $$\\varDelta $$ . For CA, our results are twofold: (1) A lottery that eliminates conflicts by discarding bidders/items independent of the bids. It allows to apply any truthful $$\\alpha $$ -approximation mechanism for conflict-free valuations and yields an $${\\mathcal O}(\\alpha \\varDelta )$$ -approximation mechanism. (2) For fractionally sub-additive valuations, we design a rounding algorithm via a novel combination of a semi-definite program and a linear program, resulting in a cone program; the approximation ratio is $${\\mathcal O}((\\varDelta \\log \\log \\varDelta )/\\log \\varDelta )$$ . The ratios are almost optimal given existing hardness results. For adwords auctions, we present several algorithms for the most relevant scenario when the number of items is small. In particular, we design a truthful mechanism with approximation ratio $$o(\\varDelta )$$ when the number of items is only logarithmic in the number of bidders.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_17"}, {"primary_key": "4513726", "vector": [], "sparse_vector": [], "title": "Applications of α-Strongly Regular Distributions to Bayesian Auctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two classes of distributions that are widely used in the analysis of Bayesian auctions are the Monotone Hazard Rate (MHR) and Regular distributions. They can both be characterized in terms of the rate of change of the associated virtual value functions: for MHR distributions the condition is that for values $$v < v'$$ , $$\\phi (v') - \\phi (v) \\ge v' - v$$ , and for regular distributions, $$\\phi (v') - \\phi (v) \\ge 0$$ . <PERSON> and <PERSON> introduced the interpolating class of $$\\alpha $$ -Strongly Regular distributions ( $$\\alpha $$ -SR distributions for short), for which $$\\phi (v') - \\phi (v) \\ge \\alpha (v' - v)$$ , for $$0 \\le \\alpha \\le 1$$ . In this paper, we investigate five distinct auction settings for which good expected revenue bounds are known when the bidders' valuations are given by MHR distributions. In every case, we show that these bounds degrade gracefully when extended to $$\\alpha $$ -SR distributions. For four of these settings, the auction mechanism requires knowledge of these distribution(s) (in the other setting, the distributions are needed only to ensure good bounds on the expected revenue). In these cases we also investigate what happens when the distributions are known only approximately via samples, specifically how to modify the mechanisms so that they remain effective and how the expected revenue depends on the number of samples.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_18"}, {"primary_key": "4513727", "vector": [], "sparse_vector": [], "title": "The Curse of Sequentiality in Routing Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the \"The curse of simultaneity\", <PERSON><PERSON> et al. show that there are interesting classes of games for which sequential decision making and corresponding subgame perfect equilibria avoid worst case Nash equilibria, resulting in substantial improvements for the price of anarchy. This is called the sequential price of anarchy. A handful of papers have lately analysed it for various problems, yet one of the most interesting open problems was to pin down its value for linear atomic routing (also: network congestion) games, where the price of anarchy equals 5/2. The main contribution of this paper is the surprising result that the sequential price of anarchy is unbounded even for linear symmetric routing games, thereby showing that sequentiality can be arbitrarily worse than simultaneity for this class of games. Complementing this result we solve an open problem in the area by establishing that the (regular) price of anarchy for linear symmetric routing games equals 5/2. Additionally, we prove that in these games, even with two players, computing the outcome of a subgame perfect equilibrium is $$\\mathsf {NP}$$ -hard.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_19"}, {"primary_key": "4513728", "vector": [], "sparse_vector": [], "title": "Adaptive Rumor Spreading.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Motivated by the recent emergence of the so-called opportunistic communication networks, we consider the issue of adaptivity in the most basic continuous time (asynchronous) rumor spreading process. In our setting a rumor has to be spread to a population; the service provider can push it at any time to any node in the network and has unit cost for doing this. On the other hand, as usual in rumor spreading, nodes share the rumor upon meeting and this imposes no cost on the service provider. Rather than fixing a budget on the number of pushes, we consider the cost version of the problem with a fixed deadline and ask for a minimum cost strategy that spreads the rumor to every node. A non-adaptive strategy can only intervene at the beginning and at the end, while an adaptive strategy has full knowledge and intervention capabilities. Our main result is that in the homogeneous case (where every pair of nodes randomly meet at the same rate) the benefit of adaptivity is bounded by a constant. This requires a subtle analysis of the underlying random process that is of interest in its own right.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_20"}, {"primary_key": "4513729", "vector": [], "sparse_vector": [], "title": "Privacy and Truthful Equilibrium Selection for Aggregative Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a very general class of games — multi-dimensional aggregative games — which in particular generalize both anonymous games and weighted congestion games. For any such game that is also large, we solve the equilibrium selection problem in a strong sense. In particular, we give an efficient weak mediator: a mechanism which has only the power to listen to reported types and provide non-binding suggested actions, such that (a) it is an asymptotic Nash equilibrium for every player to truthfully report their type to the mediator, and then follow its suggested action; and (b) that when players do so, they end up coordinating on a particular asymptotic pure strategy Nash equilibrium of the induced complete information game. In fact, truthful reporting is an ex-post Nash equilibrium of the mediated game, so our solution applies even in settings of incomplete information, and even when player types are arbitrary or worst-case (i.e. not drawn from a common prior). We achieve this by giving an efficient differentially private algorithm for computing a Nash equilibrium in such games. The rates of convergence to equilibrium in all of our results are inverse polynomial in the number of players n. We also apply our main results to a multi-dimensional market game. Our results can be viewed as giving, for a rich class of games, a more robust version of the Revelation Principle, in that we work with weaker informational assumptions (no common prior), yet provide a stronger solution concept (ex-post Nash versus Bayes Nash equilibrium). In comparison to previous work, our main conceptual contribution is showing that weak mediators are a game theoretic object that exist in a wide variety of games – previously, they were only known to exist in traffic routing games. We also give the first weak mediator that can implement an equilibrium optimizing a linear objective function, rather than implementing a possibly worst-case Nash equilibrium.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_21"}, {"primary_key": "4513730", "vector": [], "sparse_vector": [], "title": "Welfare and Revenue Guarantees for Competitive Bundling Equilibrium.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Competitive equilibrium, the central equilibrium notion in markets with indivisible goods, is based on pricing each good such that the demand for goods equals their supply and the market clears. This equilibrium notion is not guaranteed to exist beyond the narrow case of substitute goods, might result in zero revenue even when consumers value the goods highly, and overlooks the widespread practice of pricing bundles rather than individual goods. Alternative equilibrium notions proposed to address these shortcomings have either made a strong assumption on the ability to withhold supply in equilibrium, or have allowed an exponential number of prices. In this paper we study the notion of competitive bundling equilibrium – a competitive equilibrium over the market induced by partitioning the goods into bundles. Such an equilibrium is guaranteed to exist, is succinct, and satisfies the fundamental economic condition of market clearance. We establish positive welfare and revenue guarantees for this solution concept: For welfare we show that in markets with homogeneous goods, there always exists a competitive bundling equilibrium that achieves a logarithmic fraction of the optimal welfare. We also extend this result to establish nontrivial welfare guarantees for markets with heterogeneous goods. For revenue we show that in a natural class of markets for which competitive equilibrium does not guarantee positive revenue, there always exists a competitive bundling equilibrium that extracts as revenue a logarithmic fraction of the optimal welfare. Both results are tight.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_22"}, {"primary_key": "4513731", "vector": [], "sparse_vector": [], "title": "Often Harder than in the Constructive Case: Destructive Bribery in CP-nets.", "authors": ["<PERSON><PERSON><PERSON>", "Dominikus Krüger", "<PERSON>"], "summary": "We study the complexity of the destructive bribery problem (an external agent tries to prevent a disliked candidate from winning by bribery actions) in voting over combinatorial domains, where the set of candidates is the Cartesian product of several issues. This problem is related to the concept of the margin of victory of an election which constitutes a measure of robustness of the election outcome and plays an important role in the context of electronic voting. In our setting, voters have conditional preferences over assignments to these issues, modelled by CP-nets. We settle the complexity of all combinations of this problem based on distinctions of four voting rules, five cost schemes, three bribery actions, weighted and unweighted voters, as well as the negative and the non-negative scenario. We show that almost all of these cases are $$\\mathcal {NP}$$ -complete or $$\\mathcal {NP}$$ -hard for weighted votes while approximately half of the cases can be solved in polynomial time for unweighted votes.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_23"}, {"primary_key": "4513732", "vector": [], "sparse_vector": [], "title": "Improving Selfish Routing for Risk-Averse Players.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Thanasis <PERSON>"], "summary": "We investigate how and to which extent one can exploit risk-aversion and modify the perceived cost of the players in selfish routing so that the Price of Anarchy ( $$\\mathrm {PoA}$$ ) is improved. We introduce small random perturbations to the edge latencies so that the expected latency does not change, but the perceived cost of the players increases due to risk-aversion. We adopt the model of $$\\gamma $$ -modifiable routing games, a variant of routing games with restricted tolls. We prove that computing the best $$\\gamma $$ -enforceable flow is $$\\mathrm {NP}$$ -hard for parallel-link networks with affine latencies and two classes of heterogeneous risk-averse players. On the positive side, we show that for parallel-link networks with heterogeneous players and for series-parallel networks with homogeneous players, there exists a nicely structured $$\\gamma $$ -enforceable flow whose $$\\mathrm {PoA}$$ improves fast as $$\\gamma $$ increases. We show that the complexity of computing such a $$\\gamma $$ -enforceable flow is determined by the complexity of computing a Nash flow of the original game. Moreover, we prove that the $$\\mathrm {PoA}$$ of this flow is best possible in the worst-case, in the sense that there are instances where (i) the best $$\\gamma $$ -enforceable flow has the same $$\\mathrm {PoA}$$ , and (ii) considering more flexible modifications does not lead to any further improvement.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_24"}, {"primary_key": "4513733", "vector": [], "sparse_vector": [], "title": "The VCG Mechanism for Bayesian Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of scheduling m tasks to n selfish, unrelated machines in order to minimize the makespan, where the execution times are independent random variables, identical across machines. We show that the VCG mechanism, which myopically allocates each task to its best machine, achieves an approximation ratio of $$O\\left( \\frac{\\ln n}{\\ln \\ln n}\\right) $$ . This improves significantly on the previously best known bound of $$O\\left( \\frac{m}{n}\\right) $$ for prior-independent mechanisms, given by <PERSON><PERSON><PERSON> et al. [STOC'13] under the additional assumption of Monotone Hazard Rate (MHR) distributions. Although we demonstrate that this is in general tight, if we do maintain the MHR assumption, then we get improved, (small) constant bounds for $$m\\ge n\\ln n$$ i.i.d. tasks, while we also identify a sufficient condition on the distribution that yields a constant approximation ratio regardless of the number of tasks.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_25"}, {"primary_key": "4513734", "vector": [], "sparse_vector": [], "title": "Query Complexity of Approximate Equilibria in Anonymous Games.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the computation of equilibria of two-strategy anonymous games, via algorithms that may proceed via a sequence of adaptive queries to the game's payoff function, assumed to be unknown initially. The general topic we consider is query complexity, that is, how many queries are necessary or sufficient to compute an exact or approximate Nash equilibrium. We show that exact equilibria cannot be found via query-efficient algorithms. We also give an example of a 2-strategy, 3-player anonymous game that does not have any exact Nash equilibrium in rational numbers. Our main result is a new randomized query-efficient algorithm that finds a $$O(n^{-1/4})$$ -approximate Nash equilibrium querying $$\\tilde{O}(n^{3/2})$$ payoffs and runs in time $$\\tilde{O}(n^{3/2})$$ . This improves on the running time of pre-existing algorithms for approximate equilibria of anonymous games, and is the first one to obtain an inverse polynomial approximation in poly-time. We also show how this can be used to get an efficient PTAS. Furthermore, we prove that $$\\varOmega (n \\log {n})$$ payoffs must be queried in order to find any $$\\epsilon $$ -well-supported Nash equilibrium, even by randomized algorithms.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_26"}, {"primary_key": "4513735", "vector": [], "sparse_vector": [], "title": "Incentivizing Exploration with Heterogeneous Value of Money.", "authors": ["Li Han", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON> et al. proposed a natural model for crowdsourced exploration of different a priori unknown options: a principal is interested in the long-term welfare of a population of agents who arrive one by one in a multi-armed bandit setting. However, each agent is myopic, so in order to incentivize him to explore options with better long-term prospects, the principal must offer the agent money. <PERSON><PERSON><PERSON> et al. showed that a simple class of policies called time-expanded are optimal in the worst case, and characterized their budget-reward tradeoff. The previous work assumed that all agents are equally and uniformly susceptible to financial incentives. In reality, agents may have different utility for money. We therefore extend the model of <PERSON><PERSON><PERSON> et al. to allow agents that have heterogeneous and non-linear utilities for money. The principal is informed of the agent's tradeoff via a signal that could be more or less informative. Our main result is to show that a convex program can be used to derive a signal-dependent time-expanded policy which achieves the best possible Lagrangian reward in the worst case. The worst-case guarantee is matched by so-called \"Diamonds in the Rough\" instances; the proof that the guarantees match is based on showing that two different convex programs have the same optimal solution for these specific instances.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_27"}, {"primary_key": "4513736", "vector": [], "sparse_vector": [], "title": "Bottleneck Routing with Elastic Demands.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bottleneck routing games are a well-studied model to investigate the impact of selfish behavior in communication networks. In this model, each user selects a path in a network for routing their fixed demand. The disutility of a used only depends on the most congested link visited. We extend this model by allowing users to continuously vary the demand rate at which data is sent along the chosen path. As our main result we establish tight conditions for the existence of pure strategy Nash equilibria.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_28"}, {"primary_key": "4513737", "vector": [], "sparse_vector": [], "title": "Mechanisms with Monitoring for Truthful RAM Allocation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Novel algorithmic ideas for big data have not been accompanied by advances in the way central memory is allocated to concurrently running programs. Commonly, RAM is poorly managed since the programs' trade offs between speed of execution and RAM consumption are ignored. This trade off is, however, well known to the programmers. We adopt mechanism design tools to truthfully elicit this (multidimensional) information with the aim of designing more clever RAM allocation algorithms. We introduce a novel paradigm wherein programs are bound to overbidding declarations of their running times. We show the limitations of this paradigm in the absence of transfers and prove how to leverage waiting times, as a currency, to obtain optimal money burning mechanisms for the makespan.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_29"}, {"primary_key": "4513738", "vector": [], "sparse_vector": [], "title": "Inverse Game Theory: Learning Utilities in Succinct Games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One of the central questions in game theory deals with predicting the behavior of an agent. Here, we study the inverse of this problem: given the agents’ equilibrium behavior, what are possible utilities that motivate this behavior? We consider this problem in arbitrary normal-form games in which the utilities can be represented by a small number of parameters, such as in graphical, congestion, and network design games. In all such settings, we show how to efficiently, i.e. in polynomial time, determine utilities consistent with a given correlated equilibrium. However, inferring both utilities and structural elements (e.g., the graph within a graphical game) is in general NP-hard. From a theoretical perspective our results show that rationalizing an equilibrium is computationally easier than computing it; from a practical perspective a practitioner can use our algorithms to validate behavioral models.", "published": "2015-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-662-48995-6_30"}]