[{"primary_key": "1730189", "vector": [], "sparse_vector": [], "title": "Partitions and Ewens Distributions in element-free Probability Theory.", "authors": ["<PERSON>"], "summary": "This article redevelops and deepens the probability theory of <PERSON><PERSON><PERSON> and others from the 1970s in population biology. At the heart of this theory are the so-called <PERSON><PERSON><PERSON> distributions describing biolological mutations. These distributions have a particularly rich (and beautiful) mathematical structure. The original work is formulated in terms of partitions, which are special multisets on natural numbers. The current redevelopment starts from multisets on arbitrary sets, with partitions as a special form that captures only the multiplicities of multiplicities, without naming the elements themselves. This 'element-free' approach will be developed in parallel to the usual element-based theory. <PERSON><PERSON><PERSON>' famous sampling formula describes a cone of (parametrised) distributions on partitions. Another cone for this chain is described in terms of new (element-free) multinomials. They are well-defined because of a novel 'partitions multinomial theorem' that extends the familiar multinomial theorem. This is based on a new concept of 'division', as element-free distribution, in terms of multisets of probabilities that add up to one.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532419"}, {"primary_key": "1730190", "vector": [], "sparse_vector": [], "title": "Stochastic Games with Synchronizing Objectives.", "authors": ["<PERSON>"], "summary": "We consider two-player stochastic games played on a finite graph for infinitely many rounds. Stochastic games generalize both Markov decision processes (MDP) by adding an adversary player, and two-player deterministic games by adding stochasticity. The outcome of the game is a sequence of distributions over the states of the game graph. We consider synchronizing objectives, which require the probability mass to accumulate in a set of target states, either always, once, infinitely often, or always after some point in the outcome sequence; and the winning modes of sure winning (if the accumulated probability is equal to 1) and almost-sure winning (if the accumulated probability is arbitrarily close to 1).", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532439"}, {"primary_key": "1730191", "vector": [], "sparse_vector": [], "title": "Geometric decision procedures and the VC dimension of linear arithmetic theories.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper resolves two open problems on linear integer arithmetic (LIA), also known as Presburger arithmetic. First, we give a triply exponential geometric decision procedure for LIA, i.e., a procedure based on manipulating semilinear sets. This matches the running time of the best quantifier elimination and automata-based procedures. Second, building upon our first result, we give a doubly exponential upper bound on the <PERSON><PERSON><PERSON> (VC) dimension of sets definable in LIA, proving a conjecture of <PERSON><PERSON> and <PERSON><PERSON> [Combinatorica 39, pp. 923–932, 2019].", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533372"}, {"primary_key": "1730192", "vector": [], "sparse_vector": [], "title": "Abstractions for the local-time semantics of timed automata: a foundation for partial-order methods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A timed network is a parallel composition of timed automata synchronizing on common actions. We develop a methodology that allows to use partial-order methods when solving the reachability problem for timed networks. It is based on a local-time semantics proposed by [<PERSON><PERSON><PERSON> et al. 1998]. A new simulation based abstraction of local-time zones is proposed. The main technical contribution is an efficient algorithm for testing subsumption between local-time zones with respect to this abstraction operator. The abstraction is not finite for all networks. It turns out that, under relatively mild conditions, there is no finite abstraction for local-time zones that works for arbitrary timed networks. To circumvent this problem, we introduce a notion of a bounded-spread network. The spread of a network is a parameter that says how far the local times of individual processes need to diverge. For bounded-spread networks, we show that it is possible to use subsumption and partial-order methods at the same time.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533343"}, {"primary_key": "1730193", "vector": [], "sparse_vector": [], "title": "Solvability of orbit-finite systems of linear equations.", "authors": ["Arka Ghosh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study orbit-finite systems of linear equations, in the setting of sets with atoms. Our principal contribution is a decision procedure for solvability of such systems. The procedure works for every field (and even commutative ring) under mild effectiveness assumptions, and reduces a given orbit-finite system to a number of finite ones: exponentially many in general, but polynomially many when the atom dimension of input systems is fixed. Towards obtaining the procedure we push further the theory of vector spaces generated by orbit-finite sets, and show that each such vector space admits an orbit-finite basis. This fundamental property is a key tool in our development, but should be also of wider interest.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533333"}, {"primary_key": "1730194", "vector": [], "sparse_vector": [], "title": "Exponentials as Substitutions and the Cost of Cut Elimination in Linear Logic.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper introduces the exponential substitution calculus (ESC), a new presentation of cut elimination for IMELL, based on proof terms and building on the idea that exponentials can be seen as explicit substitutions. The idea in itself is not new, but here it is pushed to a new level, inspired by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>'s linear substitution calculus (LSC).", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532445"}, {"primary_key": "1730195", "vector": [], "sparse_vector": [], "title": "Reasonable Space for the λ-Calculus, Logarithmically.", "authors": ["<PERSON><PERSON><PERSON>", "Ugo <PERSON>", "<PERSON><PERSON>"], "summary": "Can the λ-calculus be considered a reasonable computational model? Can we use it for measuring the time and space consumption of algorithms? While the literature contains positive answers about time, much less is known about space. This paper presents a new reasonable space cost model for the λ-calculus, based on a variant over the Krivine abstract machine. For the first time, this cost model is able to accommodate logarithmic space. Moreover, we study the time behavior of our machine and show how to transport our results to the call-by-value λ-calculus.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533362"}, {"primary_key": "1730196", "vector": [], "sparse_vector": [], "title": "Computable PAC Learning of Continuous Features.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce definitions of computable PAC learning for binary classification over computable metric spaces. We provide sufficient conditions on a hypothesis class to ensure than an empirical risk minimizer (ERM) is computable, and bound the strong Weihrauch degree of an ERM under more general conditions. We also give a presentation of a hypothesis class that does not admit any proper computable PAC learner with computable sample function, despite the underlying class being PAC learnable.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533330"}, {"primary_key": "1730197", "vector": [], "sparse_vector": [], "title": "Varieties of Quantitative Algebras and Their Monads.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Quantitative Σ-algebras, where Σ is a signature with countable arities, are Σ-algebras equipped with a metric making all operations nonexpanding. They have been studied by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> who also introduced c-basic quantitative equations for regular cardinals c. Categories of quantitative algebras that can be presented by such equations for c = ℵ1 are called ω1-varieties. We prove that they are precisely the monadic categories , where is a countably basic monad on the category of metric spaces", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532405"}, {"primary_key": "1730198", "vector": [], "sparse_vector": [], "title": "Semantics for two-dimensional type theory.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a general notion of model for two-dimensional type theory, in the form of comprehension bicategories. Examples of comprehension bicategories are plentiful; they include interpretations of directed type theory previously studied in the literature.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533334"}, {"primary_key": "1730199", "vector": [], "sparse_vector": [], "title": "On the Satisfiability of Context-free String Constraints with Subword-Ordering.", "authors": ["<PERSON><PERSON>", "Soumodev Mal", "<PERSON>"], "summary": "We consider a variant of string constraints given by membership constraints in context-free languages and subword relation between variables. The satisfiability problem for this variant turns out to be undecidable. We consider a fragment in which the subword-order constraints do not impose any cyclic dependency between variables. We show that this fragment is NexpTime-complete. As an application of our result, we settle the complexity of control state reachability in acyclic lossy channel pushdown systems, an important distributed system model. The problem was shown to be decidable in [8]. However, no elementary upper bound was known. We show that this problem is NexpTime-complete.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533329"}, {"primary_key": "1730200", "vector": [], "sparse_vector": [], "title": "<PERSON> and <PERSON>.", "authors": ["<PERSON>", "Ugo <PERSON>", "<PERSON>"], "summary": "We show that an intuitionistic version of counting propositional logic corresponds, in the sense of <PERSON> and <PERSON>, to an expressive type system for the probabilistic event λ-calculus, a vehicle calculus in which both call-by-name and call-by-value evaluation of discrete randomized functional programs can be simulated. In this context, proofs (respectively, types) do not guarantee that validity (respectively, termination) holds, but reveal the underlying probability. We finally show how to obtain a system precisely capturing the probabilistic behavior of λ-terms, by endowing the type system with an intersection operator.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533361"}, {"primary_key": "1730201", "vector": [], "sparse_vector": [], "title": "Quantum Expectation Transformers for Cost Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new kind of expectation transformer for a mixed classical-quantum programming language. Our semantic approach relies on a new notion of a cost structure, which we introduce and which can be seen as a specialisation of the Kegelspitzen of <PERSON><PERSON><PERSON> and <PERSON>kin. We show that our weakest precondition analysis is both sound and adequate with respect to the operational semantics of the language. Using the induced expectation transformer, we provide formal analysis methods for the expected cost analysis and expected value analysis of classical-quantum programs. We illustrate the usefulness of our techniques by computing the expected cost of several well-known quantum algorithms and protocols, such as coin tossing, repeat until success, entangled state preparation, and quantum walks.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533332"}, {"primary_key": "1730202", "vector": [], "sparse_vector": [], "title": "Bouncing Threads for Circular and Non-Wellfounded Proofs: Towards Compositionality with Circular Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given that (co)inductive types are naturally modelled as fixed points, it is unsurprising that fixed-point logics are of interest in the study of programming languages, via the Curry-Howard (or proofs-as-programs) correspondence. This motivates investigations of the structural proof-theory of fixed-point logics and of their cut-elimination procedures.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533375"}, {"primary_key": "1730203", "vector": [], "sparse_vector": [], "title": "Identity Testing for Radical Expressions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the Radical Identity Testing problem (RIT): Given an algebraic circuit representing a polynomial and nonnegative integers a1, …, ak and d1, …, dk, written in binary, test whether the polynomial vanishes at the real radicals , i.e., test whether . We place the problem in coNP assuming the Generalised Riemann Hypothesis (GRH), improving on the straightforward PSPACE upper bound obtained by reduction to the existential theory of reals. Next we consider a restricted version, called 2-RIT, where the radicals are square roots of prime numbers, written in binary. It was known since the work of <PERSON> and <PERSON><PERSON> [16] that 2-RIT is at least as hard as the polynomial identity testing problem, however no better upper bound than PSPACE was known prior to our work. We show that 2-RIT is in coRP assuming GRH and in coNP unconditionally. Our proof relies on theorems from algebraic and analytic number theory, such as the Chebotarev density theorem and quadratic reciprocity.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533331"}, {"primary_key": "1730204", "vector": [], "sparse_vector": [], "title": "Resource approximation for the λμ-calculus.", "authors": ["<PERSON><PERSON>"], "summary": "The λμ-calculus plays a central role in the theory of programming languages as it extends the Curry<PERSON><PERSON> correspondence to classical logic. A major drawback is that it does not satisfy <PERSON><PERSON><PERSON>'s Theorem and it lacks the corresponding notion of approximation. On the contrary, we show that <PERSON><PERSON><PERSON> and <PERSON><PERSON>'s Taylor expansion can be easily adapted, thus providing a resource conscious approximation theory. This produces a sensible λμ-theory with which we prove some advanced properties of the λμ-calculus, such as Stability and Perpendicular Lines Property, from which the impossibility of parallel computations follows.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532469"}, {"primary_key": "1730205", "vector": [], "sparse_vector": [], "title": "The Regular Languages of First-Order Logic with One Alternation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The regular languages with a neutral letter expressible in first-order logic with one alternation are characterized. Specifically, it is shown that if an arbitrary Σ2 formula defines a regular language with a neutral letter, then there is an equivalent Σ2 formula that only uses the order predicate. This shows that the so-called Central Conjecture of Straubing holds for Σ2 over languages with a neutral letter, the first progress on the Conjecture in more than 20 years. To show the characterization, lower bounds against polynomial-size depth-3 Boolean circuits with constant top fan-in are developed. The heart of the combinatorial argument resides in studying how positions within a language are determined from one another, a technique of independent interest.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533371"}, {"primary_key": "1730206", "vector": [], "sparse_vector": [], "title": "Ramsey Quantifiers over Automatic Structures: Complexity and Applications to Verification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic structures are infinite structures that are finitely represented by synchronized finite-state automata. This paper concerns specifically automatic structures over finite words and trees (ranked/unranked). We investigate the \"directed version\" of Ramsey quantifiers, which express the existence of an infinite directed clique. This subsumes the standard \"undirected version\" of Ramsey quantifiers. Interesting connections between Ramsey quantifiers and two problems in verification are firstly observed: (1) reachability with <PERSON><PERSON><PERSON> and generalized <PERSON><PERSON><PERSON> conditions in regular model checking can be seen as Ramsey quantification over transitive automatic graphs (i.e., whose edge relations are transitive), (2) checking monadic decomposability (a.k.a. recognizability) of automatic relations can be viewed as Ramsey quantification over co-transitive automatic graphs (i.e., the complements of whose edge relations are transitive). We provide a comprehensive complexity landscape of Ramsey quantifiers in these three cases (general, transitive, co-transitive), all between NL and EXP. In turn, this yields a wealth of new results with precise complexity, e.g., verification of subtree/flat prefix rewriting, as well as monadic decomposability over tree-automatic relations. We also obtain substantially simpler proofs, e.g., for NL complexity for monadic decomposability over word-automatic relations (given by DFAs).", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533346"}, {"primary_key": "1730207", "vector": [], "sparse_vector": [], "title": "Deciding Hyperproperties Combined with Functional Specifications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study satisfiability for HyperLTL with a ∀*∃* quantifier prefix, known to be highly undecidable in general. HyperLTL can express system properties that relate multiple traces (so-called hyperproperties), which are often combined with trace properties that specify functional behavior on single traces. Following this conceptual split, we first define several safety and liveness fragments of ∀*∃* HyperLTL, and characterize the complexity of their (often much easier) satisfiability problem. We then add LTL trace properties as functional specifications. Though (highly) undecidable in many cases, this way of combining \"simple\" HyperLTL and arbitrary LTL also leads to interesting new decidable fragments. This systematic study of ∀*∃* fragments is complemented by a new (incomplete) algorithm for ∀∃*-HyperLTL satisfiability.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533369"}, {"primary_key": "1730208", "vector": [], "sparse_vector": [], "title": "The complexity of soundness in workflow nets.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Workflow nets are a popular variant of Petri nets that allow for the algorithmic formal analysis of business processes. The central decision problems concerning workflow nets deal with soundness, where the initial and final configurations are specified. Intuitively, soundness states that from every reachable configuration one can reach the final configuration. We settle the widely open complexity of the three main variants of soundness: classical, structural and generalised soundness. The first two are EXPSPACE-complete, and, surprisingly, the latter is PSPACE-complete, thus computationally simpler.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533341"}, {"primary_key": "1730209", "vector": [], "sparse_vector": [], "title": "A direct computational interpretation of second-order arithmetic via update recursion.", "authors": ["<PERSON><PERSON>"], "summary": "Second-order arithmetic has two kinds of computational interpretations: via <PERSON><PERSON><PERSON>'s bar recursion of via <PERSON><PERSON><PERSON>'s polymorphic lambda-calculus. Bar recursion interprets the negative translation of the axiom of choice which, combined with an interpretation of the negative translation of the excluded middle, gives a computational interpretation of the negative translation of the axiom scheme of comprehension. It is then possible to instantiate universally quantified sets with arbitrary formulas (second-order elimination). On the other hand, polymorphic lambda-calculus interprets directly second-order elimination by means of polymorphic types. The present work aims at bridging the gap between these two interpretations by interpreting directly second-order elimination through update recursion, which is a variant of bar recursion.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532458"}, {"primary_key": "1730210", "vector": [], "sparse_vector": [], "title": "Transducers of polynomial growth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The polyregular functions are a class of string-to-string functions that have polynomial size outputs, and which can be defined using finite state models. There are many equivalent definitions of this class, with roots in automata theory, programming languages and logic. This paper surveys recent results on polyregular functions. It presents five of the equivalent definitions, and gives self-contained proofs for most of the equivalences. Decision problems as well as restricted subclasses of the polyregular functions are also discussed.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533326"}, {"primary_key": "1730211", "vector": [], "sparse_vector": [], "title": "On the strength of <PERSON><PERSON><PERSON><PERSON> and Nullstellensatz as propositional proof systems.", "authors": ["Ilario Bonacina", "<PERSON>"], "summary": "We characterize the strength of the algebraic proof systems <PERSON><PERSON><PERSON><PERSON>Adams () and Nullstellensatz () in terms of Frege-style proof systems. Unlike bounded-depth Frege, has polynomial-size proofs of the pigeonhole principle (). A natural question is whether adding to bounded-depth Frege is enough to simulate .", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533344"}, {"primary_key": "1730212", "vector": [], "sparse_vector": [], "title": "Model Checking on Interpretations of Classes of Bounded Local Cliquewidth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An interpretation is an operation that maps an input graph to an output graph by redefining its edge relation using a first-order formula. This rich framework includes operations such as taking the complement or a fixed power of a graph as (very) special cases.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533367"}, {"primary_key": "1730213", "vector": [], "sparse_vector": [], "title": "Cyclic Implicit Complexity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Circular (or cyclic) proofs have received increasing attention in recent years, and have been proposed as an alternative setting for studying (co)inductive reasoning. In particular, now several type systems based on circular reasoning have been proposed. However, little is known about the complexity theoretic aspects of circular proofs, which exhibit sophisticated loop structures atypical of more common 'recursion schemes'.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533340"}, {"primary_key": "1730214", "vector": [], "sparse_vector": [], "title": "The boundedness and zero isolation problems for weighted automata over nonnegative rationals.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider linear cost-register automata (equivalent to weighted automata) over the semiring of nonnegative rationals, which generalise probabilistic automata. The two problems of boundedness and zero isolation ask whether there is a sequence of words that converge to infinity and to zero, respectively. In the general model both problems are undecidable so we focus on the copyless linear restriction. There, we show that the boundedness problem is decidable.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533336"}, {"primary_key": "1730215", "vector": [], "sparse_vector": [], "title": "Lower Bounds for the Reachability Problem in Fixed Dimensional VASSes.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the complexity of the reachability problem for Vector Addition Systems with States (VASSes) in fixed dimensions. We provide four lower bounds improving the currently known state-of-the-art: 1) NP-hardness for unary flat 4-VASSes (VASSes in dimension 4), 2) PSpace-hardness for unary 5-VASSes, 3) ExpSpace-hardness for binary 6-VASSes and 4) Tower-hardness for unary 8-VASSes.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533357"}, {"primary_key": "1730216", "vector": [], "sparse_vector": [], "title": "Logical Foundations of Quantitative Equality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In quantitative reasoning one compares objects by distances, instead of equivalence relations, so that one can measure how much they are similar, rather than just saying whether they are equivalent or not. In this paper we aim at providing a logical ground to quantitative reasoning with distances in Linear Logic, using the categorical language of <PERSON><PERSON>'s doctrines. The key idea is to see distances as equality predicates in Linear Logic. We use graded modalities to write a resource sensitive substitution rule for equality, which allows us to give it a quantitative meaning by distances. We introduce a deductive calculus for (Graded) Linear Logic with quantitative equality and the notion of Lipschitz doctrine to give it a sound and complete categorical semantics. We also describe a universal construction of Lipschitz doctrines, which generates examples based for instance on metric spaces and quantitative realisability.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533337"}, {"primary_key": "1730217", "vector": [], "sparse_vector": [], "title": "Efficient Construction of Reversible Transducers from Regular Transducer Expressions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The class of regular transformations has several equivalent characterizations such as functional MSO transductions, deterministic two-way transducers, streaming string transducers, as well as regular transducer expressions (RTE).", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533364"}, {"primary_key": "1730218", "vector": [], "sparse_vector": [], "title": "Separating LREC from LFP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "is an extension of first-order logic with a logarithmic recursion operator. It was introduced by <PERSON><PERSON><PERSON> et al. and shown to capture the complexity class L over trees and interval graphs. It does not capture L in general as it is contained in —fixed-point logic with counting. We show that this containment is strict. In particular, we show that the path systems problem, a classic P-complete problem which is definable in —fixed-point logic—is not definable in . This shows that the logarithmic recursion mechanism is provably weaker than general least fixed points.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533368"}, {"primary_key": "1730219", "vector": [], "sparse_vector": [], "title": "On Almost-Uniform Generation of SAT Solutions: The power of 3-wise independent hashing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a Boolean formula φ and a distribution parameter ε, the problem of almost-uniform generation seeks to design a randomized generator such that every solution of φ is output with probability within (1 + ε)-factor of where sol(φ) is the set of all the solutions of φ. The prior state of the art scheme due to <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, makes calls to a SAT oracle and employs 2 − wise independent hash functions.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533338"}, {"primary_key": "1730220", "vector": [], "sparse_vector": [], "title": "Treelike Decompositions for Transductions of Sparse Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give new decomposition theorems for classes of graphs that can be transduced in first-order logic from classes of sparse graphs — more precisely, from classes of bounded expansion and nowhere dense classes. In both cases, the decomposition takes the form of a single colored rooted tree of bounded depth where, in addition, there can be links between nodes that are not related in the tree. The constraint is that the structure formed by the tree and the links has to be sparse. Using the decomposition theorem for transductions of nowhere dense classes, we show that they admit low-shrubdepth covers of size , where n is the vertex count and ε > 0 is any fixed real. This solves an open problem posed by <PERSON><PERSON><PERSON> et al. (ACM TOCL '20) and also by <PERSON><PERSON> et al. (SIDMA '21).", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533349"}, {"primary_key": "1730221", "vector": [], "sparse_vector": [], "title": "Reasoning on Data Words over Numeric Domains.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce parametric semilinear data logic (pSDL) for reasoning about data words with numeric data. The logic allows parameters, and <PERSON><PERSON><PERSON> guards on the data and on the <PERSON><PERSON><PERSON> image of equivalence classes (i.e. data counting), allowing us to capture data languages like: (1) each data value occurs at most once in the word and is an even number, (2) the subset of the positions containing data values divisible by 4 has the same number of a's and b's, (3) the data value with the highest frequency in the word is divisible by 3, and (4) each data value occurs at most once, and the set of data values forms an interval. We provide decidability and complexity results for the problem of membership and satisfiability checking over these models. In contrast to two-variable logic of data words and data automata (which also permit a form of data counting but no arithmetics over numeric domains and have incomparable inexpressivity), pSDL has elementary complexity of satisfiability checking. We show interesting potential applications of our models in databases and verification.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533354"}, {"primary_key": "1730222", "vector": [], "sparse_vector": [], "title": "A Type Theory for Strictly Unital ∞-Categories.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We use type-theoretic techniques to present an algebraic theory of $\\infty$-categories with strict units. Starting with a known type-theoretic presentation of fully weak $\\infty$-categories, in which terms denote valid operations, we extend the theory with a non-trivial definitional equality. This forces some operations to coincide strictly in any model, yielding the strict unit behaviour. We make a detailed investigation of the meta-theoretic properties of this theory. We give a reduction relation that generates definitional equality, and prove that it is confluent and terminating, thus yielding the first decision procedure for equality in a strictly-unital setting. Moreover, we show that our definitional equality relation identifies all terms in a disc context, providing a point comparison with a previously proposed definition of strictly unital $\\infty$-category. We also prove a conservativity result, showing that every operation of the strictly unital theory indeed arises from a valid operation in the fully weak theory. From this, we infer that strict unitality is a property of an $\\infty$-category rather than additional structure.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533363"}, {"primary_key": "1730223", "vector": [], "sparse_vector": [], "title": "Graded Monads and Behavioural Equivalence Games.", "authors": ["Chase Ford", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The framework of graded semantics uses graded monads to capture behavioural equivalences of varying granularity, for example as found in the linear-time / branching-time spectrum, over general system types. We describe a generic Spoiler-Duplicator game for graded semantics that is extracted from the given graded monad, and may be seen as playing out an equational proof; instances include standard pebble games for simulation and bisimulation as well as games for trace-like equivalences and coalgebraic behavioural equivalence. Considerations on an infinite variant of such games lead to a novel notion of infinite-depth graded semantics. Under reasonable restrictions, the infinite-depth graded semantics associated to a given graded equivalence can be characterized in terms of a determinization construction for coalgebras under the equivalence at hand.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533374"}, {"primary_key": "1730224", "vector": [], "sparse_vector": [], "title": "Stable graphs of bounded twin-width.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that every class of graphs that is monadically stable and has bounded twin-width can be transduced from some class with bounded sparse twin-width. This generalizes analogous results for classes of bounded linear cliquewidth [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. 2021b] and of bounded cliquewidth [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. 2021a]. It also implies that monadically stable classes of bounded twin-width are linearly χ-bounded.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533356"}, {"primary_key": "1730225", "vector": [], "sparse_vector": [], "title": "The Complexity of Bidirected Reachability in Valence Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reachability problems in infinite-state systems are often subject to extremely high complexity. This motivates the investigation of efficient overapproximations, where we add transitions to obtain a system in which reachability can be decided more efficiently. We consider bidirected infinite-state systems, where for every transition there is a transition with opposite effect.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533345"}, {"primary_key": "1730226", "vector": [], "sparse_vector": [], "title": "A first-order completeness result about characteristic Boolean algebras in classical realizability.", "authors": ["<PERSON>"], "summary": "We prove the following completeness result about classical realizability: given any Boolean algebra with at least two elements, there exists a Krivine-style classical realizability model whose characteristic Boolean algebra is elementarily equivalent to it. This is done by controlling precisely which combinations of so-called \"angelic\" (or \"may\") and \"demonic\" (or \"must\") nondeterminism exist in the underlying model of computation.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532484"}, {"primary_key": "1730227", "vector": [], "sparse_vector": [], "title": "Milner&apos;s Proof System for Regular Expressions Modulo Bisimilarity is Complete: Crystallization: Near-Collapsing Process Graph Interpretations of Regular Expressions.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> (1984) defined a process semantics for regular expressions. He formulated a sound proof system for bisimilarity of process interpretations of regular expressions, and asked whether this system is complete.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532430"}, {"primary_key": "1730228", "vector": [], "sparse_vector": [], "title": "Zero-One Laws and Almost Sure Valuations of First-Order Logic in Semiring Semantics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Semiring semantics evaluates logical statements by values in some commutative semiring (K, +, ·, 0, 1). Random semiring interpretations, induced by a probability distribution on K, generalise random structures, and we investigate here the question of how classical results on first-order logic on random structures, most importantly the 0-1 laws of <PERSON><PERSON><PERSON><PERSON><PERSON> et al. and <PERSON><PERSON><PERSON>, generalise to semiring semantics. For positive semirings, the classical 0-1 law implies that every first-order sentence is, asymptotically, either almost surely evaluated to 0 by random semiring interpretations, or almost surely takes only values different from 0. However, by means of a more sophisticated analysis, based on appropriate extension properties and on algebraic representations of first-order formulae, we can prove much stronger results.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533358"}, {"primary_key": "1730229", "vector": [], "sparse_vector": [], "title": "Normalization for Multimodal Type Theory.", "authors": ["<PERSON>"], "summary": "We prove normalization for MTT, a general multimodal dependent type theory capable of expressing modal type theories for guarded recursion, internalized parametricity, and various other prototypical modal situations. We prove that deciding type checking and conversion in MTT can be reduced to deciding the equality of modalities in the underlying modal situation, immediately yielding a type checking algorithm for all instantiations of MTT in the literature. This proof follows from a generalization of synthetic Tait computability—an abstract approach to gluing proofs—to account for modalities. This extension is based on MTT itself, so that this proof also constitutes a significant case study of MTT.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532398"}, {"primary_key": "1730230", "vector": [], "sparse_vector": [], "title": "Temporal Team Semantics Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study a novel approach to asynchronous hyperproperties by reconsidering the foundations of temporal team semantics. We consider three logics: , and , which are obtained by adding quantification over so-called time evaluation functions controlling the asynchronous progress of traces. We then relate synchronous to our new logics and show how it can be embedded into them. We show that the model checking problem for with Boolean disjunctions is highly undecidable by encoding recurrent computations of non-deterministic 2-counter machines. Finally, we present a translation from to Alternating Asynchronous Büchi Automata and obtain decidability results for the path checking problem as well as restricted variants of the model checking and satisfiability problems.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533360"}, {"primary_key": "1730231", "vector": [], "sparse_vector": [], "title": "Zigzag normalisation for associative n-categories.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The theory of associative n-categories has recently been proposed as a strictly associative and unital approach to higher category theory. As a foundation for a proof assistant, this is potentially attractive, since it has the potential to allow simple formal proofs of complex high-dimensional algebraic phenomena. However, the theory relies on an implicit term normalisation procedure to recognize correct composites, with no recursive method available for computing it.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533352"}, {"primary_key": "1730232", "vector": [], "sparse_vector": [], "title": "Complexity of Modular Circuits.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study how the complexity of modular circuits computing AND depends on the depth of the circuits and the prime factorization of the modulus they use. In particular our construction of subexponential circuits of depth 2 for AND helps us to classify (modulo Exponential Time Hypothesis) modular circuits with respect to the complexity of their satisfiability. We also study a precise correlation between this complexity and the sizes of modular circuits realizing AND. In particular we use the superlinear lower bound from [10] to check satisfiability of CC0 circuits in probabilistic 2O(n/ε(n)) time, where ε is some extremely slowly increasing function. Moreover we show that AND can be computed by a polynomial size modular circuit of depth 2 (with O(log n) random bits) providing a probabilistic computational model that can not be derandomized.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533350"}, {"primary_key": "1730233", "vector": [], "sparse_vector": [], "title": "Computing the Density of the Positivity Set for Linear Recurrence Sequences.", "authors": ["<PERSON><PERSON>"], "summary": "The set of indices that correspond to the positive entries of a sequence of numbers is called its positivity set. In this paper, we study the density of the positivity set of a given linear recurrence sequence, that is the question of how much more frequent are the positive entries compared to the non-positive ones. We show that one can compute this density to arbitrary precision, as well as decide whether it is equal to zero (or one). If the sequence is diagonalisable, we prove that its positivity set is finite if and only if its density is zero. Lastly, arithmetic properties of densities are treated, in particular we prove that it is decidable whether the density is a rational number, given that the recurrence sequence has at most one pair of dominant complex roots.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532399"}, {"primary_key": "1730234", "vector": [], "sparse_vector": [], "title": "Greatest HITs: Higher inductive types in coinductive definitions via induction under clocks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present Clocked Cubical Type Theory, the first type theory combining multi-clocked guarded recursion with the features of Cubical Type Theory. Guarded recursion is an abstract form of step-indexing, which can be used for construction of advanced programming language models. In its multi-clocked version, it can also be used for coinductive programming and reasoning, encoding productivity in types. Combining this with Higher Inductive Types (HITs) the encoding extends to coinductive types that are traditionally hard to represent in type theory, such as the type of finitely branching labelled transition systems.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533359"}, {"primary_key": "1730235", "vector": [], "sparse_vector": [], "title": "Size measures and alphabetic equivalence in the μ-calculus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Algorithms for solving computational problems related to the modal μ-calculus generally do not take the formulas themselves as input, but operate on some kind of representation of formulas. This representation is usually based on a graph structure that one may associate with a μ-calculus formula. Recent work by <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON> showed that the operation of renaming bound variables may incur an exponential blow-up of the size of such a graph representation. Their example revealed the undesirable situation that standard constructions, on which algorithms for model checking and satisfiability depend, are sensitive to the specific choice of bound variables used in a formula.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533339"}, {"primary_key": "1730236", "vector": [], "sparse_vector": [], "title": "Monoidal Streams for Dataflow Programming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce monoidal streams: a generalization of causal stream functions to monoidal categories. In the same way that streams provide semantics to dataflow programming with pure functions, monoidal streams provide semantics to dataflow programming with theories of processes represented by a symmetric monoidal category. At the same time, monoidal streams form a feedback monoidal category, which can be used to interpret signal flow graphs. As an example, we study a stochastic dataflow language.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533365"}, {"primary_key": "1730237", "vector": [], "sparse_vector": [], "title": "Probabilistic Verification Beyond Context-Freeness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Probabilistic pushdown automata (recursive state machines) are a widely known model of probabilistic computation associated with many decidable problems concerning termination (time) and linear-time model checking. Higher-order recursion schemes (HORS) are a prominent formalism for the analysis of higher-order computation.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533351"}, {"primary_key": "1730238", "vector": [], "sparse_vector": [], "title": "Choiceless Polynomial Time with Witnessed Symmetric Choice.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We extend Choiceless Polynomial Time (CPT), the currently only remaining promising candidate in the quest for a logic capturing Ptime, so that this extended logic has the following property: for every class of structures for which isomorphism is definable, the logic automatically captures Ptime.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533348"}, {"primary_key": "1730239", "vector": [], "sparse_vector": [], "title": "On the Skolem Problem and the Skolem Conjecture.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is a longstanding open problem whether there is an algorithm to decide the Skolem Problem for linear recurrence sequences (LRS) over the integers, namely whether a given such sequence has a zero term (i.e., whether un = 0 for some n). A major breakthrough in the early 1980s established decidability for LRS of order 4 or less, i.e., for LRS in which every new term depends linearly on the previous four (or fewer) terms. The Skolem Problem for LRS of order 5 or more, in particular, remains a major open challenge to this day.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533328"}, {"primary_key": "1730240", "vector": [], "sparse_vector": [], "title": "Quantum Weakest Preconditions for Reasoning about Expected Runtimes of Quantum Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study expected runtimes for quantum programs. Inspired by recent work on probabilistic programs, we first define expected runtime as a generalisation of quantum weakest precondition. Then, we show that the expected runtime of a quantum program can be represented as the expectation of an observable (in physics). A method for computing the expected runtimes of quantum programs in finite-dimensional state spaces is developed. Several examples are provided as applications of this method, including computing the expected runtime of quantum Bernoulli Factory – a quantum algorithm for generating random numbers. In particular, using our new method, an open problem of computing the expected runtime of quantum random walks introduced by <PERSON><PERSON><PERSON><PERSON> et al. (STOC 2001) is solved.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533327"}, {"primary_key": "1730241", "vector": [], "sparse_vector": [], "title": "When Locality Meets Preservation.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper investigates the expressiveness of a fragment of first-order sentences in G<PERSON><PERSON>man normal form, namely the positive Boolean combinations of basic local sentences. We show that they match exactly the first-order sentences preserved under local elementary embeddings, thus providing a new general preservation theorem and extending the Łós-Tarski Theorem.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532498"}, {"primary_key": "1730242", "vector": [], "sparse_vector": [], "title": "Concrete categories and higher-order recursion: With applications including probability, differentiability, and full abstraction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study concrete sheaf models for a call-by-value higher-order language with recursion. Our family of sheaf models is a generalization of many examples from the literature, such as models for probabilistic and differentiable programming, and fully abstract logical relations models. We treat recursion in the spirit of synthetic domain theory. We provide a general construction of a lifting monad starting from a class of admissible monomorphisms in the site of the sheaf category. In this way, we obtain a family of models parametrized by a concrete site and a class of monomorphisms, for which we prove a general computational adequacy theorem.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533370"}, {"primary_key": "1730243", "vector": [], "sparse_vector": [], "title": "A Functorial Excursion Between Algebraic Geometry and Linear Logic.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The language of Algebraic Geometry combines two complementary and dependent levels of discourse: on the geometric side, schemes define spaces of the same cohesive nature as manifolds ; on the vectorial side, every scheme X comes equipped with a symmetric monoidal category of quasicoherent modules, which may be seen as generalised vector bundles on the scheme X. In this paper, we use the functor of points approach to Algebraic Geometry developed by <PERSON><PERSON><PERSON><PERSON><PERSON> in the 1970s to establish that every covariant presheaf X on the category of commutative rings — and in particular every scheme X — comes equipped \"above it\" with a symmetric monoidal closed category PshModX of presheaves of modules. This category PshModX defines moreover a model of intuitionistic linear logic, whose exponential modality is obtained by glueing together in an appropriate way the <PERSON><PERSON><PERSON> dual construction on ring algebras.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532488"}, {"primary_key": "1730244", "vector": [], "sparse_vector": [], "title": "Beyond Nonexpansive Operations in Quantitative Algebraic Reasoning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The framework of quantitative equational logic has been successfully applied to reason about algebras whose carriers are metric spaces and operations are nonexpansive. We extend this framework in two orthogonal directions: algebras endowed with generalised metric space structures, and operations being nonexpansive up to a lifting. We apply our results to the algebraic axiomatisation of the <PERSON><PERSON><PERSON><PERSON> distance on probability distributions, which has recently found application in the field of representation learning on Markov processes.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533366"}, {"primary_key": "1730245", "vector": [], "sparse_vector": [], "title": "The Pebble-Relation Comonad in Finite Model Theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The pebbling comonad, introduced by <PERSON><PERSON>, <PERSON> and <PERSON>, provides a categorical interpretation for the k-pebble games from finite model theory. The coKleisli category of the pebbling comonad specifies equivalences under different fragments and extensions of infinitary k-variable logic. Moreover, the coalgebras over this pebbling comonad characterise treewidth and correspond to tree decompositions. In this paper we introduce the pebble-relation comonad, which characterises pathwidth and whose coalgebras correspond to path decompositions. We further show that the existence of a coKleisli morphism in this comonad is equivalent to truth preservation in the restricted conjunction fragment of k-variable infinitary logic. We do this using <PERSON><PERSON><PERSON>'s pebble-relation game and an equivalent all-in-one pebble game. We then provide a similar treatment to the corresponding coKleisli isomorphisms via a bijective version of the all-in-one pebble game with a hidden pebble placement. Finally, we show as a consequence a new Lovász-type theorem relating pathwidth to the restricted conjunction fragment of k-variable infinitary logic with counting quantifiers.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533335"}, {"primary_key": "1730246", "vector": [], "sparse_vector": [], "title": "Probability monads with submonads of deterministic states.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Probability theory can be studied synthetically as the computational effect embodied by a commutative monad. In the recently proposed Markov categories, one works with an abstraction of the <PERSON><PERSON><PERSON><PERSON> category and then defines deterministic morphisms equationally in terms of copying and discarding. The resulting difference between 'pure' and 'deterministic' leads us to investigate the 'sober' objects for a probability monad, for which the two concepts coincide. We propose natural conditions on a probability monad which allow us to identify the sober objects and define an idempotent sobrification functor. Our framework applies to many examples of interest, including the Giry monad on measurable spaces, and allows us to sharpen a previously given version of <PERSON>'s theorem for Markov categories. This is an extended version of the paper accepted for the Logic In Computer Science (LICS) conference 2022. In this document we include more mathematical details, including all the proofs, of the statements and constructions given in the published version.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533355"}, {"primary_key": "1730247", "vector": [], "sparse_vector": [], "title": "Smooth approximations and CSPs over finitely bounded homogeneous structures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce the novel machinery of smooth approximations, and apply it to confirm the CSP dichotomy conjecture for first-order reducts of the random tournament, and to give new short proofs of the conjecture for various homogeneous graphs including the random graph (STOC'11, ICALP'16), and for expansions of the order of the rationals (STOC'08). Apart from obtaining these dichotomy results, we show how our new proof technique allows to unify and significantly simplify the previous results from the literature. For all but the last structure, we moreover characterize for the first time those CSPs which are solvable by local consistency methods, again using the same machinery.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533353"}, {"primary_key": "1730248", "vector": [], "sparse_vector": [], "title": "Active learning for sound negotiations✱.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present two active learning algorithms for sound deterministic negotiations. Sound deterministic negotiations are models of distributed systems, a kind of Petri nets or Zielonka automata with additional structure. We show that this additional structure allows to minimize such negotiations. The two active learning algorithms differ in the type of membership queries they use. Both have similar complexity to <PERSON><PERSON><PERSON>'s L* algorithm, in particular, the number of queries is polynomial in the size of the negotiation, and not in the number of configurations.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533342"}, {"primary_key": "1730249", "vector": [], "sparse_vector": [], "title": "Characterizing Positionality in Games of Infinite Duration over Infinite Graphs.", "authors": ["<PERSON>"], "summary": "We study turn-based quantitative games of infinite duration opposing two antagonistic players and played over graphs. This model is widely accepted as providing the adequate framework for formalizing the synthesis question for reactive systems. This important application motivates the question of strategy complexity: which valuations (or payoff functions) admit optimal positional strategies (without memory)? Valuations for which both players have optimal positional strategies have been characterized by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [16] for finite graphs and by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [12] for infinite graphs.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532418"}, {"primary_key": "1730250", "vector": [], "sparse_vector": [], "title": "The amazing mixed polynomial closure and its applications to two-variable first-order logic.", "authors": ["<PERSON>"], "summary": "Polynomial closure is a standard operator which is applied to a class of regular languages. In this paper, we investigate three restrictions called left (LPol), right (RPol) and mixed polynomial closure (MPol). The first two were known while MPol is new. We look at two decision problems that are defined for every class . Membership takes a regular language as input and asks if it belongs to . Separation takes two regular languages as input and asks if there exists a third language in including the first one and disjoint from the second. We prove that LPol, RPol and MPol preserve the decidability of membership under mild hypotheses on the input class, and the decidability of separation under much stronger hypotheses. We apply these results to natural hierarchies.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3532410"}, {"primary_key": "1730251", "vector": [], "sparse_vector": [], "title": "Syllepsis in Homotopy Type Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "The Eckmann-<PERSON> argument shows that any two monoid structures on the same set satisfying the interchange law are in fact the same operation, which is moreover commutative. When the monoids correspond to the vertical and horizontal composition of a sufficiently higher-dimensional category, the <PERSON><PERSON><PERSON>-<PERSON> argument itself appears as a higher cell. This cell is often required to satisfy an additional piece of coherence, which is known as the syllepsis. We show that the syllepsis can be constructed from the elimination rule of intensional identity types in Martin-Löf type theory.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533347"}, {"primary_key": "1730252", "vector": [], "sparse_vector": [], "title": "Linear-Algebraic Models of Linear Logic as Categories of Modules over Σ-Semirings✱.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A number of models of linear logic are based on or closely related to linear algebra, in the sense that morphisms are \"matrices\" over appropriate coefficient sets. Examples include models based on coherence spaces, finiteness spaces and probabilistic coherence spaces, as well as the relational and weighted relational models. This paper introduces a unified framework based on module theory, making the linear algebraic aspect of the above models more explicit. Specifically we consider modules over Σ-semirings R, which are ring-like structures with partially-defined countable sums, and show that morphisms in the above models are actually R-linear maps in the standard algebraic sense for appropriate R. An advantage of our algebraic treatment is that the category of R-modules is locally presentable, from which it easily follows that this category becomes a model of intuitionistic linear logic with the cofree exponential. We then discuss constructions of classical models and show that the above-mentioned models are examples of our constructions.", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130.3533373"}, {"primary_key": "1782622", "vector": [], "sparse_vector": [], "title": "LICS &apos;22: 37th Annual ACM/IEEE Symposium on Logic in Computer Science, Haifa, Israel, August 2 - 5, 2022", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The polyregular functions are a class of string-to-string functions that have polynomial size outputs, and which can be defined using finite state models. There are many equivalent definitions of this class, with roots in automata theory, programming ...", "published": "2022-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3531130"}]