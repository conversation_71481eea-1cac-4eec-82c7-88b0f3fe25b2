[{"primary_key": "4196483", "vector": [], "sparse_vector": [], "title": "Fitter: A System for Easily Printing Objects that Fit Real Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When printing both self-making and existing 3D models, users often create models to fit to a real object within it. Fitting models to the size of a real object is a delicate problem. To address it, we present a concept to capture the size of a real object, create or modify a model that conforms to the captured image, and print the model on the spot. We create a 3D printer to realize this concept by installing a touch panel display in the build plate system. In this paper, we focus on creating containers that fit accessories. We create containers for a pair of scissors, a smart watch, a drone, a pair of glasses, and a pen holder.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985730"}, {"primary_key": "4196484", "vector": [], "sparse_vector": [], "title": "Expressive Keyboards: Enriching Gesture-Typing on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Gesture-typing is an efficient, easy-to-learn, and errortolerant technique for entering text on software keyboards. Our goal is to \"recycle\" users' otherwise-unused gesture variation to create rich output under the users' control, without sacrificing accuracy. Experiment 1 reveals a high level of existing gesture variation, even for accurate text, and shows that users can consciously vary their gestures under different conditions. We designed an Expressive Keyboard for a smart phone which maps input gesture features identified in Experiment 1 to a continuous output parameter space, i.e. RGB color. Experiment 2 shows that users can consciously modify their gestures, while retaining accuracy, to generate specific colors as they gesture-type. Users are more successful when they focus on output characteristics (such as red) rather than input characteristics (such as curviness). We designed an app with a dynamic font engine that continuously interpolates between several typefaces, as well as controlling weight and random variation. Experiment 3 shows that, in the context of a more ecologically-valid conversation task, users enjoy generating multiple forms of rich output. We conclude with suggestions for how the Expressive Keyboard approach can enhance a wide variety of gesture recognition applications.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984560"}, {"primary_key": "4196485", "vector": [], "sparse_vector": [], "title": "Initial Trials of ofxEpilog: From Real Time Operation to Dynamic Focus of Epilog Laser Cutter.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes ofxEpilog which enable people to control a laser cutter of Epilog in real time. ofxEpilog is an add on of open Frameworks, an open source C++ toolkit for creative coding. With the add on, people could directly send their image object to a laser cutter through Ethernet. By alternating the generation and transmission of the command of cutting, the add on could sequentially control a laser cutter in real time. This paper introduces our initial trials of ofxEpilog with a real time operation (A), dynamic focus (z-axis) control with a given 3D object (B), and a scanned 3D object (C). Technical limitations and our upcoming challenges are also discussed.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984756"}, {"primary_key": "4196486", "vector": [], "sparse_vector": [], "title": "On Suggesting Phrases vs. Predicting Words for Mobile Text Composition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A system capable of suggesting multi-word phrases while someone is writing could supply ideas about content and phrasing and allow those ideas to be inserted efficiently. Meanwhile, statistical language modeling has provided various approaches to predicting phrases that users type. We introduce a simple extension to the familiar mobile keyboard suggestion interface that presents phrase suggestions that can be accepted by a repeated-tap gesture. In an extended composition task, we found that phrases were interpreted as suggestions that affected the content of what participants wrote more than conventional single-word suggestions, which were interpreted as predictions. We highlight a design challenge: how can a phrase suggestion system make valuable suggestions rather than just accurate predictions'", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984584"}, {"primary_key": "4196487", "vector": [], "sparse_vector": [], "title": "Extended<PERSON><PERSON> on Wheelchair.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a novel welfare system which utilizes a spatial augmented reality technique. Hand is a crucial component in human-human communication. For example, we can intuitively indicate an object or place by reaching and pointing it to nearby partners. Unfortunately, for wheelchair users, such communication is often limited because their reaching ranges are narrow, and moving their bodies to the target is tiresome. To solve this issue, we propose a novel wheelchair system on which a battery-powered mobile projector is mounted. A user manipulates the projected virtual hand as an extension of the real one using a touch panel equipped on an armrest of the wheelchair. We implement our proposed system and demonstrate the effectiveness.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985738"}, {"primary_key": "4196488", "vector": [], "sparse_vector": [], "title": "NormalTouch and TextureTouch: High-fidelity 3D Haptic Shape Rendering on Handheld Virtual Reality Controllers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present an investigation of mechanically-actuated hand-held controllers that render the shape of virtual objects through physical shape displacement, enabling users to feel 3D surfaces, textures, and forces that match the visual rendering. We demonstrate two such controllers, NormalTouch and TextureTouch, which are tracked in 3D and produce spatially-registered haptic feedback to a user's finger. NormalTouch haptically renders object surfaces and provides force feedback using a tiltable and extrudable platform. TextureTouch renders the shape of virtual objects including detailed surface structure through a 4×4 matrix of actuated pins. By moving our controllers around while keeping their finger on the actuated platform, users obtain the impression of a much larger 3D shape by cognitively integrating output sensations over time. Our evaluation compares the effectiveness of our controllers with the two de-facto standards in Virtual Reality controllers: device vibration and visual feedback only. We find that haptic feedback significantly increases the accuracy of VR interaction, most effectively by rendering high-fidelity shape output as in the case of our controllers.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984526"}, {"primary_key": "4196489", "vector": [], "sparse_vector": [], "title": "Predicting Finger-Touch Accuracy Based on the Dual Gaussian Distribution Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurately predicting the accuracy of finger-touch target acquisition is crucial for designing touchscreen UI and for modeling complex and higher level touch interaction behaviors. Despite its importance, there has been little theoretical work on creating such models. Building on the Dual Gaussian Distribution Model[3], we derived an accuracy model that predicts the success rate of target acquisition based on the target size. We evaluated the model by comparing the predicted success rates with empirical measures for three types of targets including 1-dimensional vertical and horizontal, and 2-dimensional circular targets. The predictions matched the empirical data very well: the differences between predicted and observed success rates were under 5% for 4.8 mm and 7.2 mm targets, and under 10% for 2.4 mm targets. The evaluation results suggest that our simple model can reliably predict touch accuracy.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984546"}, {"primary_key": "4196490", "vector": [], "sparse_vector": [], "title": "Reading and Learning Smartfonts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As small displays on devices like smartwatches become increasingly common, many people have difficulty reading the text on these displays. Vision conditions like presbyopia that result in blurry near vision make reading small text particularly hard. We design multiple different scripts for displaying English text, legible at small sizes even when blurry, for small screens such as smartphones and smartwatches. These \"smartfonts\" redesign visual character presentations to improve the reading experience. Like cursive, Grade 1 Braille, and ordinary fonts, they preserve orthography and spelling. They have the potential to enable people to read more text comfortably on small screens, e.g., without reading glasses. To simulate presbyopia, we blur images and evaluate their legibility using paid crowdsourcing. We also evaluate the difficulty of learning to read smartfonts and observe a learnability/legibility trade-off. Our most learnable smartfont can be read at roughly half the speed of Latin after two thousand practice sentences. It is also legible smaller than half the size of traditional Latin (i.e. \"English\") when blurry.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984554"}, {"primary_key": "4196491", "vector": [], "sparse_vector": [], "title": "Prevention of Unintentional Input While Using Wrist Rotation for Device Configuration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinwook Seo"], "summary": "We describe the design of the safeguard interface that helps users avoid unintentional input while using wrist rotation. When configuring the parameters of various devices, our interface helps reduce the chance of making accidental changes by delaying the result of input and allowing the users to make deliberate attempt to change the parameters to their desired value. We evaluated our methods with a set of user experience and found that our methods were more preferred when the end-results of configurational changes of the devices become more critical and can cause irreversible damage.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984771"}, {"primary_key": "4196492", "vector": [], "sparse_vector": [], "title": "Supporting Mobile Sensemaking Through Intentionally Uncertain Highlighting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Patients researching medical diagnoses, scientist exploring new fields of literature, and students learning about new domains are all faced with the challenge of capturing information they find for later use. However, saving information is challenging on mobile devices, where the small screen and font sizes combined with the inaccuracy of finger based touch screens makes it time consuming and stressful for people to select and save text for future use. Furthermore, beyond the challenge of simply selecting a region of bounded text on a mobile device, in many learning and data exploration tasks the boundaries of what text may be relevant and useful later are themselves uncertain for the user. In contrast to previous approaches which focused on speeding up the selection process by making the identification of hard boundaries faster, we introduce the idea of intentionally supporting uncertain input in the context of saving information during complex reading and information exploration. We embody this idea in a system that uses force touch and fuzzy bounding boxes along with posthoc expandable context to support identifying and saving information in an intentionally uncertain way on mobile devices. In a two part user study we find that this approach reduced selection time and was preferred by participants over the default system text selection method.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984538"}, {"primary_key": "4196493", "vector": [], "sparse_vector": [], "title": "Making Fabrication Real.", "authors": ["Xiang &apos;Anthony&apos; Chen"], "summary": "Low-cost, easy-to-use 3D printers have promised to empower everyday users with the ability to fabricate physical objects of their own design. While these printers specialize in building objects from scratch, they are innately oblivious to the real world in which the printed objects will be situated and in use. In my thesis research, I develop fabrication techniques with tool integration to enable users to expressively specify how a design can be attached to, augment, adapt, support, or otherwise function with existing real world objects. In this paper, I describe projects to date as well as ongoing work that explores this space of research.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984785"}, {"primary_key": "4196494", "vector": [], "sparse_vector": [], "title": "SoEs: Attachable Augmented Haptic on Gaming Controller for Immersive Interaction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Kong<PERSON><PERSON>", "<PERSON>ao<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present SoEs (Sword of Elements), an attachable augmented haptic device for enhancing gaming controller in the immersive first-person game. Generally, Player can easily receive visual and auditory feedback through head-mounted displays (HMD) and headphones from first-person perspective in virtual world. However, the tactile feedback is less than those feedbacks in immersive environment. Although gaming controller, i.e. VIVE or Oculus controller, can provide tactile feedback by some vibration sensors, the haptic feedback is more complicated and various, it includes kinesthesia and cutaneous feedback. Our key idea is to provide a low-cost approach to simulate the haptic feedback of player manipulation in the immersive environment such as striking while the iron is hot which the player could feel the heat and reaction force. Eventually, the game makers could utilize the attachable device into their games for providing haptic feedback.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985707"}, {"primary_key": "4196495", "vector": [], "sparse_vector": [], "title": "Reprise: A Design Tool for Specifying, Generating, and Customizing 3D Printable Adaptations on Everyday Objects.", "authors": ["Xiang &apos;Anthony&apos; Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Everyday tools and objects often need to be customized for an unplanned use or adapted for specific user, such as adding a bigger pull to a zipper or a larger grip for a pen. The advent of low-cost 3D printing offers the possibility to rapidly construct a wide range of such adaptations. However, while 3D printers are now affordable enough for even home use, the tools needed to design custom adaptations normally require skills that are beyond users with limited 3D modeling experience.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984512"}, {"primary_key": "4196496", "vector": [], "sparse_vector": [], "title": "Bootstrapping User-Defined Body Tapping Recognition with Offline-Learned Probabilistic Representation.", "authors": ["Xiang &apos;Anthony&apos; Chen", "<PERSON>"], "summary": "To address the increasing functionality (or information) overload of smartphones, prior research has explored a variety of methods to extend the input vocabulary of mobile devices. In particular, body tapping has been previously proposed as a technique that allows the user to quickly access a target functionality by simply tapping at a specific location of the body with a smartphone. Though compelling, prior work often fell short in enabling users' unconstrained tapping locations or behaviors. To address this problem, we developed a novel recognition method that combines both offline-before the system sees any user-defined gestures and online learning to reliably recognize arbitrary, user-defined body tapping gestures, only using a smartphone's built-in sensors. Our experiment indicates that our method significantly outperforms baseline approaches in several usage conditions. In particular, provided only with a single sample per location, our accuracy is 30.8% over an SVM baseline and 24.8% over a template matching method. Based on these findings, we discuss how our approach can be generalized to other user-defined gesture problems.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984541"}, {"primary_key": "4196497", "vector": [], "sparse_vector": [], "title": "Authoring Illustrations of Human Movements by Iterative Physical Demonstration.", "authors": ["<PERSON><PERSON><PERSON><PERSON> (Peggy) <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Illustrations of human movements are used to communicate ideas and convey instructions in many domains, but creating them is time-consuming and requires skill. We introduce DemoDraw, a multi-modal approach to generate these illustrations as the user physically demonstrates the movements. In a Demonstration Interface, DemoDraw segments speech and 3D joint motion into a sequence of motion segments, each characterized by a key pose and salient joint trajectories. Based on this sequence, a series of illustrations is automatically generated using a stylistically rendered 3D avatar annotated with arrows to convey movements. During demonstration, the user can navigate using speech and amend or re-perform motions if needed. Once a suitable sequence of steps has been created, a Refinement Interface enables fine control of visualization parameters. In a three-part evaluation, we validate the effectiveness of the generated illustrations and the usability of DemoDraw. Our results show 4 to 7-step illustrations can be created in 5 or 10 minutes on average.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984559"}, {"primary_key": "4196498", "vector": [], "sparse_vector": [], "title": "A Novel Real Time Monitor System of 3D Printing Layers for Better Slicing Parameter Setting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>uh<PERSON>ung Ming"], "summary": "We proposed a novel real time monitor system of 3D printer with dual cameras, which capture and reconstruct the printed result layer by layer. With the reconstructed image, we can apply computer vision technique to evaluate the difference with the ideal path generate by G-code. The difference gives us clues to classify which might be the possible factor of the result. Hence we can produce advice to user for better slicing parameter settings. We believe that this system can give helps to beginner or users of 3D printer that struggle in parameter settings in the future.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984773"}, {"primary_key": "4196499", "vector": [], "sparse_vector": [], "title": "RealPen: Providing Realism in Handwriting Tasks on Touch Surfaces using Auditory-Tactile Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present RealPen, an augmented stylus for capacitive tablet screens that recreates the physical sensation of writing on paper with a pencil, ball-point pen or marker pen. The aim is to create a more engaging experience when writing on touch surfaces, such as screens of tablet computers. This is achieved by regenerating the friction-induced oscillation and sound of a real writing tool in contact with paper. To generate realistic tactile feedback, our algorithm analyzes the frequency spectrum of the friction oscillation generated when writing with traditional tools, extracts principal frequencies, and uses the actuator's frequency response profile for an adjustment weighting function. We enhance the realism by providing the sound feedback aligned with the writing pressure and speed. Furthermore, we investigated the effects of superposition and fluctuation of several frequencies on human tactile perception, evaluated the performance of RealPen, and characterized users' perception and preference of each feedback type.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984550"}, {"primary_key": "4196500", "vector": [], "sparse_vector": [], "title": "Wolverine: A Wearable Haptic Interface for Grasping in VR.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Wolverine is a mobile, wearable haptic device designed for simulating the grasping of rigid objects in virtual environment. In contrast to prior work on force feedback gloves, we focus on creating a low cost, lightweight, and wireless device that renders a force directly between the thumb and three fingers to simulate objects held in pad opposition type grasps. Leveraging low-power brake-based locking sliders, the system can withstand over 100N of force between each finger and the thumb, and only consumes 2.78 Wh(10 mJ) for each braking interaction. Integrated sensors are used both for feedback control and user input: time-of-flight sensors provide the position of each finger and an IMU provides overall orientation tracking. This design enables us to use the device for roughly 6 hours with 5500 full fingered grasping events. The total weight is 55g including a 350 mAh battery.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985725"}, {"primary_key": "4196501", "vector": [], "sparse_vector": [], "title": "SleepCoacher: A Personalized Automated Self-Experimentation System for Sleep Recommendations.", "authors": ["<PERSON><PERSON><PERSON>", "Danaë Metaxa-Kakavouli", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present SleepCoacher, an integrated system implementing a framework for effective self-experiments. SleepCoacher automates the cycle of single-case experiments by collecting raw mobile sensor data and generating personalized, data-driven sleep recommendations based on a collection of template recommendations created with input from clinicians. The system guides users through iterative short experiments to test the effect of recommendations on their sleep. We evaluate SleepCoacher in two studies, measuring the effect of recommendations on the frequency of awakenings, self-reported restfulness, and sleep onset latency, concluding that it is effective: participant sleep improves as adherence with SleepCoacher's recommendations and experiment schedule increases. This approach presents computationally-enhanced interventions leveraging the capacity of a closed feedback loop system, offering a method for scaling guided single-case experiments in real time.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984534"}, {"primary_key": "4196502", "vector": [], "sparse_vector": [], "title": "Data-driven Mobile App Design.", "authors": ["Biplab Deka"], "summary": "Design is becoming a key differentiating factor for successful apps in today's crowded app marketplaces. This thesis describes how data-driven approaches can enable useful tools for mobile app design. It presents interaction mining -- capturing both static (UI layouts, visual details) and dynamic (user flows, motion details) components of an app's design. It develops two approaches for interaction mining existing Android apps and presents three applications enabled by the resultant data: a search engine for user flows, lightweight usability testing at scale, and automated generation of mobile UIs.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984786"}, {"primary_key": "4196503", "vector": [], "sparse_vector": [], "title": "ERICA: Interaction Mining Mobile Apps.", "authors": ["Biplab Deka", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Design plays an important role in adoption of apps. App design, however, is a complex process with multiple design activities. To enable data-driven app design applications, we present interaction mining -- capturing both static (UI layouts, visual details) and dynamic (user flows, motion details) components of an app's design. We present ERICA, a system that takes a scalable, human-computer approach to interaction mining existing Android apps without the need to modify them in any way. As users interact with apps through ERICA, it detects UI changes, seamlessly records multiple data-streams in the background, and unifies them into a user interaction trace. Using ERICA we collected interaction traces from over a thousand popular Android apps. Leveraging this trace data, we built machine learning classifiers to detect elements and layouts indicative of 23 common user flows. User flows are an important component of UX design and consists of a sequence of UI states that represent semantically meaningful tasks such as searching or composing. With these classifiers, we identified and indexed more than 3000 flow examples, and released the largest online search engine of user flows in Android apps.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984581"}, {"primary_key": "4196504", "vector": [], "sparse_vector": [], "title": "Rovables: Miniature On-Body Robots as Mobile Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Rovables, a miniature robot that can move freely on unmodified clothing. The robots are held in place by magnetic wheels, and can climb vertically. The robots are untethered and have an onboard battery, microcontroller, and wireless communications. They also contain a low-power localization system that uses wheel encoders and IMU, allowing Rovables to perform limited autonomous navigation on the body. In the technical evaluations, we found that Rovables can operate continuously for 45 minutes and can carry up to 1.5N. We propose an interaction space for mobile on-body devices spanning sensing, actuation, and interfaces, and develop application scenarios in that space. Our applications include on-body sensing, modular displays, tactile feedback and interactive clothing and jewelry.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984531"}, {"primary_key": "4196505", "vector": [], "sparse_vector": [], "title": "The Toastboard: Ubiquitous Instrumentation and Automated Checking of Breadboarded Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The recent proliferation of easy to use electronic components and toolkits has introduced a large number of novices to designing and building electronic projects. Nevertheless, debugging circuits remains a difficult and time-consuming task. This paper presents a novel debugging tool for electronic design projects, the Toastboard, that aims to reduce debugging time by improving upon the standard paradigm of point-wise circuit measurements. Ubiquitous instrumentation allows for immediate visualization of an entire breadboard's state, meaning users can diagnose problems based on a wealth of data instead of having to form a single hypothesis and plan before taking a measurement. Basic connectivity information is displayed visually on the circuit itself and quantitative data is displayed on the accompanying web interface. Software-based testing functions further lower the expertise threshold for efficient debugging by diagnosing classes of circuit errors automatically. In an informal study, participants found the detailed, pervasive, and context-rich data from our tool helpful and potentially time-saving.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984566"}, {"primary_key": "4196506", "vector": [], "sparse_vector": [], "title": "Touchscreen Overlay Augmented with the Stick-Slip Phenomenon to Generate Kinetic Energy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Kinesthetic feedback requires linkage-based high-powered multi-dimensional manipulators, which are currently not possible to integrate with mobile devices. To overcome this challenge, we developed a novel system that can utilize a wide range of actuation components and apply various techniques to optimize stick-slip motion of a tangible object on a display surface. The current setup demonstrates how it may be possible to generate directional forces on an interactive display in order to move a linkage-free stylus over a touchscreen in a fully controlled and efficient manner. The technology described in this research opens up new possibilities for interacting with displays and tangible surfaces such as continuously supervised learning; active feed-forward systems as well as dynamic gaming environments that predict user behavior and are able modify and physically react to human input at real-time.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984758"}, {"primary_key": "4196507", "vector": [], "sparse_vector": [], "title": "Meta: Enabling Programming Languages to Learn from the Crowd.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Collectively authored programming resources such as Q&A sites and open-source libraries provide a limited window into how programs are constructed, debugged, and run. To address these limitations, we introduce Meta: a language extension for Python that allows programmers to share functions and track how they are used by a crowd of other programmers. Meta functions are shareable via URL and instrumented to record runtime data. Combining thousands of Meta functions with their collective runtime data, we demonstrate tools including an optimizer that replaces your function with a more efficient version written by someone else, an auto-patcher that saves your program from crashing by finding equivalent functions in the community, and a proactive linter that warns you when a function fails elsewhere in the community. We find that professional programmers are able to use Meta for complex tasks (creating new Meta functions that, for example, cross-validate a logistic regression), and that Meta is able to find 44 optimizations (for a 1.45 times average speedup) and 5 bug fixes across the crowd.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984532"}, {"primary_key": "4196508", "vector": [], "sparse_vector": [], "title": "Beyond Snapping: Persistent, Tweakable Alignment and Distribution with StickyLines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Aligning and distributing graphical objects is a common, but cumbersome task. In a preliminary study (six graphic designers, six non-designers), we identified three key problems with current tools: lack of persistence, unpredictability of results, and inability to 'tweak' the layout. We created StickyLines, a tool that treats guidelines as first-class objects: Users can create precise, predictable and persistent interactive alignment and distribution relationships, and 'tweaked' positions can be maintained for subsequent interactions. We ran a [2x2] within-participant experiment to compare StickyLines with standard commands, with two levels of layout difficulty. StickyLines performed 40% faster and required 49% fewer actions than traditional alignment and distribution commands for complex layouts. In study three, six professional designers quickly adopted StickyLines and identified novel uses, including creating complex compound guidelines and using them for both spatial and semantic grouping.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984577"}, {"primary_key": "4196509", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: A Tool for Resolution Independent Haptic Texture.", "authors": ["<PERSON>", "K<PERSON>ungwon Seo", "<PERSON><PERSON>"], "summary": "Haptic systems usually stimulate the kinesthetic aspects of the sense of touch, i.e. force feedback systems. But more and more devices aim to stimulate the cutaneous part of the sense of touch to reproduce more complex tactile sensations. To do so, they stimulate one's fingertip in different locations, usually in the fashion of a matrix pattern. In this paper we investigate the new possibilities that are offered by such a framework and present an ongoing project that investigates the benefits of Hilbert curves to display resolution independent mid-air haptic textures in comparison with other implementation approaches.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984774"}, {"primary_key": "4196510", "vector": [], "sparse_vector": [], "title": "ambient.", "authors": ["<PERSON><PERSON>"], "summary": "\"A single design is one molecule that contributes to the atmosphere of the whole environment.\" This is the basic idea of design in this age. That is to say, designing today seems to embody the subject by its surroundings, rather than by the subject itself.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984595"}, {"primary_key": "4196511", "vector": [], "sparse_vector": [], "title": "Activity-Aware Video Stabilization for BallCam.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a video stabilization algorithm for ball camera systems that undergo extreme egomotion during sports play. In particular, we focus on the BallCam system which is an American football embedded with an action camera at the tip of the ball. We propose an activity-aware video stabilization algorithm which is able to understand the current activity of the BallCam, which uses estimated activity labels to inform a robust video stabilization algorithm. Activity recognition is performed with a deep convolutional neural network, which uses optical flow.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984767"}, {"primary_key": "4196512", "vector": [], "sparse_vector": [], "title": "Estimating Contact Force of Fingertip and Providing Tactile Feedback Simultaneously.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study proposes a method for estimating the contact force of the fingertip by inputting vibrations actively. The use of active bone-conducted sound sensing has been limited to estimating the joint angle of the elbow and the finger. We applied it to the method for estimating the contact force of the fingertip. Unlike related works, it is not necessary to mount the device on a fingertip, and tactile feedback is enabled using tangible vibrations.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984766"}, {"primary_key": "4196513", "vector": [], "sparse_vector": [], "title": "Boomerang: Rebounding the Consequences of Reputation Feedback on Crowdsourcing Platforms.", "authors": ["<PERSON><PERSON><PERSON><PERSON> (Neil) <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Senad<PERSON>athige S<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Paid crowdsourcing platforms suffer from low-quality work and unfair rejections, but paradoxically, most workers and requesters have high reputation scores. These inflated scores, which make high-quality work and workers difficult to find, stem from social pressure to avoid giving negative feedback. We introduce Boomerang, a reputation system for crowdsourcing platforms that elicits more accurate feedback by rebounding the consequences of feedback directly back onto the person who gave it. With Boomerang, requesters find that their highly-rated workers gain earliest access to their future tasks, and workers find tasks from their highly-rated requesters at the top of their task feed. Field experiments verify that Boomerang causes both workers and requesters to provide feedback that is more closely aligned with their private opinions. Inspired by a game-theoretic notion of incentive-compatibility, Boomerang opens opportunities for interaction design to incentivize honest reporting over strategic dishonesty.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984542"}, {"primary_key": "4196514", "vector": [], "sparse_vector": [], "title": "Rig Animation with a Tangible and Modular Input Device.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a novel approach to digital character animation, combining the benefits of modular and tangible input devices and sophisticated rig animation algorithms. With a symbiotic software and hardware approach, we overcome limitations inherent to all previous tangible devices. It allows users to directly control complex rigs with 5-10 physical controls only. These compact input device configurations - optimized for a specific rig and a set of sample poses - are automatically generated by our algorithm. This avoids oversimplification of the pose space and excessively bulky devices.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985696"}, {"primary_key": "4196515", "vector": [], "sparse_vector": [], "title": "Zooids: Building Blocks for Swarm User Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces swarm user interfaces, a new class of human-computer interfaces comprised of many autonomous robots that handle both display and interaction. We describe the design of Zooids, an open-source open-hardware platform for developing tabletop swarm interfaces. The platform consists of a collection of custom-designed wheeled micro robots each 2.6 cm in diameter, a radio base-station, a high-speed DLP structured light projector for optical tracking, and a software framework for application development and control. We illustrate the potential of tabletop swarm user interfaces through a set of application scenarios developed with Zooids, and discuss general design considerations unique to swarm user interfaces.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984547"}, {"primary_key": "4196516", "vector": [], "sparse_vector": [], "title": "WhammyPhone: Exploring Tangible Audio Manipulation Using Bend Input on a Flexible Smartphone.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present WhammyPhone, a novel audio interface that supports physical manipulation of digital audio through bend gestures. WhammyPhone combines a high-resolution flexible display, bend sensors, and a set of intuitive interaction techniques that enable novice users to manipulate sound in a tangible fashion. With WhammyPhone, bend gestures can control both discrete (e.g. triggering a note) and continuous parameters (e.g. pitch bend). We showcase application scenarios that leverage the unique input modalities of WhammyPhone and discuss its potential for digital audio manipulation.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985742"}, {"primary_key": "4196517", "vector": [], "sparse_vector": [], "title": "WristWhirl: One-handed Continuous Smartwatch Input using Wrist Gestures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose and study a new input modality, Wrist<PERSON>hirl, that uses the wrist as an always-available joystick to perform one-handed continuous input on smartwatches. We explore the influence of the wrist's bio-mechanical properties for performing gestures to interact with a smartwatch, both while standing still and walking. Through a user study, we examine the impact of performing 8 distinct gestures (4 directional marks, and 4 free-form shapes) on the stability of the watch surface. Participants were able to perform directional marks using the wrist as a joystick at an average rate of half a second and free-form shapes at an average rate of approximately 1.5secs. The free-form shapes could be recognized by a $1 gesture recognizer with an accuracy of 93.8% and by three human inspectors with an accuracy of 85%. From these results, we designed and implemented a proof-of-concept device by augmenting the watchband using an array of proximity sensors, which can be used to draw gestures with high quality. Finally, we demonstrate a number of scenarios that benefit from one-handed continuous input on smartwatches using WristWhirl.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984563"}, {"primary_key": "4196518", "vector": [], "sparse_vector": [], "title": "HoloFlex: A Flexible Light-Field Smartphone with a Microlens Array and a P-OLED Touchscreen.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present HoloFlex, a 3D flexible smartphone featuring a light-field display consisting of a high-resolution P-OLED display and an array of 16,640 microlenses. HoloFlex allows mobile users to interact with 3D images featuring natural visual cues such as motion parallax and stereoscopy without glasses or head tracking. Its flexibility allows the use of bend input for interacting with 3D objects along the z axis. Images are rendered into 12-pixel wide circular blocks-pinhole views of the 3D scene-which enable ~80 unique viewports at an effective resolution of 160 × 104. The microlens array distributes each pixel from the display in a direction that preserves the angular information of light rays in the 3D scene. We present a preliminary study evaluating the effect of bend input vs. a vertical touch screen slider on 3D docking performance. Results indicate that bend input significantly improves movement time in this task. We also present 3D applications including a 3D editor, a 3D Angry Birds game and a 3D teleconferencing system that utilize bend input.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984524"}, {"primary_key": "4196519", "vector": [], "sparse_vector": [], "title": "The UIST Video Browser: Creating Shareable Playlists of Video Previews.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the UIST Video Browser which provides a rapid overview of the UIST 30-second video previews, based on the conference schedule. Attendees can see an overview of upcoming talks, search by topic, and create personalized, shareable video playlists that capture the most interesting or relevant papers.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985703"}, {"primary_key": "4196520", "vector": [], "sparse_vector": [], "title": "Exploring the Design Space for Energy-Harvesting Situated Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore the design space of energy-neutral situated displays, which give physical presence to digital information. We investigate three central dimensions: energy sources, display technologies, and wireless communications. Based on the power implications from our analysis, we present a thin, wireless, photovoltaic-powered display that is quick and easy to deploy and capable of indefinite operation in indoor lighting conditions. The display uses a low-resolution e-paper architecture, which is 35 times more energy-efficient than smaller-sized high-resolution displays. We present a detailed analysis on power consumption, photovoltaic energy harvesting performance, and a detailed comparison to other display-driving architectures. Depending on the ambient lighting, the display can trigger an update every 1 -- 25 minutes and communicate to a PC or smartphone via Bluetooth Low-Energy.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984513"}, {"primary_key": "4196521", "vector": [], "sparse_vector": [], "title": "Nomadic Virtual Reality: Exploring New Interaction Concepts for Mobile Virtual Reality Head-Mounted Displays.", "authors": ["<PERSON>"], "summary": "Technical progress and miniaturization enables virtual reality (VR) head-mounted displays (HMDs) now to be solely operated using a smartphone as a display, processing unit and sensor unit. These mobile VR HMDs (e.g. Samsung GearVR) allow for a whole new interaction scenario, where users can bring their HMD with them wherever they want and immerse themselves anytime at any place (nomadic VR). However, most of the early research on interaction with VR HMDs focused around stationary setups. My research revolves around enabling new forms of interaction for these nomadic VR scenarios. In my research I choose a user-centered design approach where I build research prototypes to solve potential problems of nomadic VR and evaluate those prototypes in user studies. I am going to present three prototypes revolving around current challenges of nomadic VR (input and feedback).", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984783"}, {"primary_key": "4196522", "vector": [], "sparse_vector": [], "title": "FaceTouch: Enabling Touch Interaction in Display Fixed UIs for Mobile Virtual Reality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present FaceTouch, a novel interaction concept for mobile Virtual Reality (VR) head-mounted displays (HMDs) that leverages the backside as a touch-sensitive surface. With FaceTouch, the user can point at and select virtual content inside their field-of-view by touching the corresponding location at the backside of the HMD utilizing their sense of proprioception. This allows for rich interaction (e.g. gestures) in mobile and nomadic scenarios without having to carry additional accessories (e.g. a gamepad). We built a prototype of FaceTouch and conducted two user studies. In the first study we measured the precision of FaceTouch in a display-fixed target selection task using three different selection techniques showing a low error rate of 2% indicate the viability for everyday usage. To asses the impact of different mounting positions on the user performance we conducted a second study. We compared three mounting positions of the touchpad (face, hand and side) showing that mounting the touchpad at the back of the HMD resulted in a significantly lower error rate, lower selection time and higher usability. Finally, we present interaction techniques and three example applications that explore the FaceTouch design space.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984576"}, {"primary_key": "4196523", "vector": [], "sparse_vector": [], "title": "GyroVR: Simulating Inertia in Virtual Reality using Head Worn Flywheels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present GyroVR, head worn flywheels designed to render inertia in Virtual Reality (VR. Motions such as flying, diving or floating in outer space generate kinesthetic forces onto our body which impede movement and are currently not represented in VR. We simulate those kinesthetic forces by attaching flywheels to the users head, leveraging the gyroscopic effect of resistance when changing the spinning axis of rotation. GyroVR is an ungrounded, wireless and self contained device allowing the user to freely move inside the virtual environment. The generic shape allows to attach it to different positions on the users body. We evaluated the impact of GyroVR onto different mounting positions on the head (back and front) in terms of immersion, enjoyment and simulator sickness. Our results show, that attaching GyroVR onto the users head (front of the Head Mounted Display (HMD)) resulted in the highest level of immersion and enjoyment and therefore can be built into future VR HMDs, enabling kinesthetic forces in VR.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984535"}, {"primary_key": "4196524", "vector": [], "sparse_vector": [], "title": "VizLens: A Robust and Interactive Screen Reader for Interfaces in the Real World.", "authors": ["<PERSON><PERSON>", "Xiang &apos;Anthony&apos; Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The world is full of physical interfaces that are inaccessible to blind people, from microwaves and information kiosks to thermostats and checkout terminals. Blind people cannot independently use such devices without at least first learning their layout, and usually only after labeling them with sighted assistance. We introduce VizLens - an accessible mobile application and supporting backend that can robustly and interactively help blind people use nearly any interface they encounter. VizLens users capture a photo of an inaccessible interface and send it to multiple crowd workers, who work in parallel to quickly label and describe elements of the interface to make subsequent computer vision easier. The VizLens application helps users recapture the interface in the field of the camera, and uses computer vision to interactively describe the part of the interface beneath their finger (updating 8 times per second). We show that VizLens provides accurate and usable real-time feedback in a study with 10 blind participants, and our crowdsourcing labeling workflow was fast (8 minutes), accurate (99.7%), and cheap ($1.15). We then explore extensions of VizLens that allow it to (i) adapt to state changes in dynamic interfaces, (ii) combine crowd labeling with OCR technology to handle dynamic displays, and (iii) benefit from head-mounted cameras. VizLens robustly solves a long-standing challenge in accessibility by deeply integrating crowdsourcing and computer vision, and foreshadows a future of increasingly powerful interactive applications that would be currently impossible with either alone.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984518"}, {"primary_key": "4196525", "vector": [], "sparse_vector": [], "title": "Porous Interfaces for Small Screen Multitasking using Finger Identification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The lack of dedicated multitasking interface features in smartphones has resulted in users attempting a sequential form of multitasking via frequent app switching. In addition to the obvious temporal cost, it requires physical and cognitive effort which increases multifold as the back and forth switching becomes more frequent. We propose porous interfaces, a paradigm that combines the concept of translucent windows with finger identification to support efficient multitasking on small screens. Porous interfaces enable partially transparent app windows overlaid on top of each other, each of them being accessible simultaneously using a different finger as input. We design porous interfaces to include a broad range of multitasking interactions with and between windows, while ensuring fidelity with the existing smartphone interactions. We develop an end-to-end smartphone interface that demonstrates porous interfaces. In a qualitative study, participants found porous interfaces intuitive, easy, and useful for frequent multitasking scenarios.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984557"}, {"primary_key": "4196526", "vector": [], "sparse_vector": [], "title": "Haptic Learning of Semaphoric Finger Gestures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Khai N. <PERSON>", "<PERSON><PERSON>"], "summary": "Haptic learning of gesture shortcuts has never been explored. In this paper, we investigate haptic learning of a freehand semaphoric finger tap gesture shortcut set using haptic rings. We conduct a two-day study of 30 participants where we couple haptic stimuli with visual and audio stimuli, and compare their learning performance with wholly visual learning. The results indicate that with <30 minutes of learning, haptic learning of finger tap semaphoric gestures is comparable to visual learning and maintains its recall on the second day.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984558"}, {"primary_key": "4196527", "vector": [], "sparse_vector": [], "title": "Semi-Automated SVG Programming via Direct Manipulation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Direct manipulation interfaces provide intuitive and interactive features to a broad range of users, but they often exhibit two limitations: the built-in features cannot possibly cover all use cases, and the internal representation of the content is not readily exposed. We believe that if direct manipulation interfaces were to (a) use general-purpose programs as the representation format, and (b) expose those programs to the user, then experts could customize these systems in powerful new ways and non-experts could enjoy some of the benefits of programmable systems. In recent work, we presented a prototype SVG editor called Sketch-n-Sketch that offered a step towards this vision. In that system, the user wrote a program in a general-purpose lambda-calculus to generate a graphic design and could then directly manipulate the output to indirectly change design parameters (i.e. constant literals) in the program in real-time during the manipulation. Unfortunately, the burden of programming the desired relationships rested entirely on the user. In this paper, we design and implement new features for Sketch-n-Sketch that assist in the programming process itself. Like typical direct manipulation systems, our extended Sketch-n-Sketch now provides GUI-based tools for drawing shapes, relating shapes to each other, and grouping shapes together. Unlike typical systems, however, each tool carries out the user's intention by transforming their general-purpose program. This novel, semi-automated programming workflow allows the user to rapidly create high-level, reusable abstractions in the program while at the same time retaining direct manipulation capabilities. In future work, our approach may be extended with more graphic design features or realized for other application domains.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984575"}, {"primary_key": "4196528", "vector": [], "sparse_vector": [], "title": "Multi-Device Storyboards for Cinematic Narratives in VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Virtual Reality (VR) narratives have the unprecedented potential to connect with an audience through presence, placing viewers within the narrative. The onset of consumer VR has resulted in an explosion of interest in immersive storytelling. Planning narratives for VR, however, is a grand challenge due to its unique affordances, its evolving cinematic vocabulary, and most importantly the lack of supporting tools to explore the creative process in VR.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984539"}, {"primary_key": "4196529", "vector": [], "sparse_vector": [], "title": "Telescope: Fine-Tuned Discovery of Interactive Web UI Feature Implementation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Professional websites contain rich interactive features that developers can learn from, yet understanding their implementation remains a challenge due to the nature of unfamiliar code. Existing tools provide affordances to analyze source code, but feature-rich websites reveal tens of thousands of lines of code and can easily overwhelm the user. We thus present Telescope, a platform for discovering how JavaScript and HTML support a website interaction. Telescope helps users understand unfamiliar website code through a composite view they control by adjusting JavaScript detail, scoping the runtime timeline, and triggering relational links between JS, HTML, and website components. To support these affordances on the open web, Telescope instruments the JavaScript in a website without request intercepts using a novel sleight-of-hand technique, then watches for traces emitted from the website. In a case study across seven popular websites, Telescope helped identify less than 150 lines of front-end code out of tens of thousands that accurately describe the desired interaction in six of the sites. In an exploratory user study, we observed users identifying difficult programming concepts by developing strategies to analyze relatively small amounts of unfamiliar website source code with Telescope.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984570"}, {"primary_key": "4196530", "vector": [], "sparse_vector": [], "title": "Phones on Wheels: Exploring Interaction for Smartphones with Kinetic Capabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces novel interaction and applications using smartphones with kinetic capabilities. We develop an accessory module with robot wheels for a smartphone. With this module, the smartphone can move in a linear direction or rotate with sufficient power. The module also includes rotary encoders, allowing us to use the wheels as an input modality. We demonstrate a series of novel mobile interaction for mobile devices with kinetic capabilities through three applications.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985727"}, {"primary_key": "4196531", "vector": [], "sparse_vector": [], "title": "Interactive Volume Segmentation with Threshold Field Painting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An interactive method for segmentation and isosurface extraction of medical volume data is proposed. In conventional methods, users decompose a volume into multiple regions iteratively, segment each region using a threshold, and then manually clean the segmentation result by removing clutter in each region. However, this is tedious and requires many mouse operations from different camera views. We propose an alternative approach whereby the user simply applies painting operations to the volume using tools commonly seen in painting systems, such as flood fill and brushes. This significantly reduces the number of mouse and camera control operations. Our technical contribution is in the introduction of the threshold field, which assigns spatially-varying threshold values to individual voxels. This generalizes discrete decomposition of a volume into regions and segmentation using a constant threshold in each region, thereby offering a much more flexible and efficient workflow. This paper describes the details of the user interaction and its implementation. Furthermore, the results of a user study are discussed. The results indicate that the proposed method can be a few times faster than a conventional method.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984537"}, {"primary_key": "4196532", "vector": [], "sparse_vector": [], "title": "An Input Switching Interface Using Carbon Copy Metaphor.", "authors": ["<PERSON><PERSON>", "Itiro <PERSON>"], "summary": "This paper proposes a novel input technique that aims to switch between relative and absolute coordinates input methods seamlessly based on the \"carbon copy\" metaphor. We display a small workspace (``carbon copy area'') on a computer screen that corresponds one-to-one with the handy trackpad. The user can input hand-written characters or images using absolute coordinates input on this virtual carbon copy paper and move it anywhere using relative coordinates. Our technique allows a user to call both absolute and relative coordinates input methods and use them appropriately with arbitrary timing. Several advantages can be obtained by combining these methods. We developed a desktop application software to utilize this technique in a real GUI environment based on a user's evaluation.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985715"}, {"primary_key": "4196533", "vector": [], "sparse_vector": [], "title": "2.5 Dimensional Panoramic Viewing Technique utilizing a Cylindrical Mirror Widget.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Itiro <PERSON>"], "summary": "We propose a panoramic viewing system, which applies the technique of Anamorphosis, mapping a 2D display onto a cylindrical mirror. In this system, a distorted scene image is shown on a flat panel display or tabletop surface. When a user places the cylindrical mirror on the display, the original image appears on the cylindrical mirror.By simply rotating the cylinder, a user can decide the direction to walk-through in VR world.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985737"}, {"primary_key": "4196534", "vector": [], "sparse_vector": [], "title": "Metamaterial Mechanisms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>siang<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, researchers started to engineer not only the outer shape of objects, but also their internal microstructure. Such objects, typically based on 3D cell grids, are also known as metamaterials. Metamaterials have been used, for example, to create materials with soft and hard regions.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984540"}, {"primary_key": "4196535", "vector": [], "sparse_vector": [], "title": "Optical Marionette: Graphical Manipulation of Human&apos;s Walking Direction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel manipulation method that subconsciously changes the walking direction of users via visual processing on a head mounted display (HMD). Unlike existing navigation systems that require users to recognize information and then follow directions as two separate, conscious processes, the proposed method guides users without them needing to pay attention to the information provided by the navigation system and also allows them to be graphically manipulated by controllers. In the proposed system, users perceive the real world by means of stereo images provided by a stereo camera and the HMD. Specifically, while walking, the navigation system provides users with real-time feedback by processing the images they have just perceived and giving them visual stimuli. This study examined two image-processing methods for manipulation of human's walking direction: moving stripe pattern and changing focal region. Experimental results indicate that the changing focal region method most effectively leads walkers as it changes their walking path by approximately 200 mm/m on average.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984545"}, {"primary_key": "4196536", "vector": [], "sparse_vector": [], "title": "Immersive Scuba Diving Simulator Using Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Amphibian, a simulator to experience scuba diving virtually in a terrestrial setting. While existing diving simulators mostly focus on visual and aural displays, Amphibian simulates a wider variety of sensations experienced underwater. Users rest their torso on a motion platform to feel buoyancy. Their outstretched arms and legs are placed in a suspended harness to simulate drag as they swim. An Oculus Rift head-mounted display (HMD) and a pair of headphones delineate the visual and auditory ocean scene. Additional senses simulated in Amphibian are breath motion, temperature changes, and tactile feedback through various sensors. Twelve experienced divers compared Amphibian to real-life scuba diving. We analyzed the system factors that influenced the users' sense of being there while using our simulator. We present future UI improvements for enhancing immersion in VR diving simulators.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984519"}, {"primary_key": "4196537", "vector": [], "sparse_vector": [], "title": "SkyAnchor: Optical Design for Anchoring Mid-air Images onto Physical Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For glass-free mixed reality (MR), mid-air imaging is a promising way of superimposing a virtual image onto a real object. We focus on attaching virtual images to non-static real life objects. In previous work, moving the real object causes latency in the superimposing system, and the virtual image seems to follow the object with a delay. This is caused by delays due to sensors, displays and computational devices for position sensing, and occasionally actuators for moving the image generation source. In order to avoid this problem, this paper proposes to separate the object-anchored imaging effect from the position sensing. Our proposal is a retro-reflective system called \"SkyAnchor,\" which consists of only optical devices: two mirrors and an aerial-imaging plate. The system reflects light from a light source anchored under the physical object itself, and forms an image anchored around the object. This optical solution does not cause any latency in principle and is effective for high-quality mixed reality applications. We consider two types of light sources to be attached to physical objects: reflecting content from a touch table on which the object rests, or attaching the source directly on the object. As for position sensing, we utilize a capacitive marker on the bottom of the object, tracked on a touch table. We have implemented a prototype, where mid-air images move with the object, and whose content may change based on its position.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984589"}, {"primary_key": "4196538", "vector": [], "sparse_vector": [], "title": "Smart Headlight: An Application of Projector-Camera Vision.", "authors": ["<PERSON><PERSON>"], "summary": "A projector manipulates outgoing light rays, while a camera records incoming ones. Combining these optically inverse devices, especially in a coaxial manner, creates the possibility of a new computer-vision technology. The \"Smart Headlight,\" currently under development at Carnegie Mellon's Robotics Institute, is one example: a device that can \"erase\" raindrops or snowflakes from a driver's sight, allowing for continuous use of the \"high beams\" mode while not causing glare against oncoming drivers, and enhance the appearance of important objects, such as pedestrians. In that sense, it constitutes a \"genuine\" augmented reality, manipulating the reality for how it appears to a viewer, rather than merely overlaying objects on the image of the reality. This talk will present the state of the Smart Headlight project and discuss further possible applications of projector-camera systems.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984594"}, {"primary_key": "4196539", "vector": [], "sparse_vector": [], "title": "Uniformity Based Haptic Alert Network.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We experience haptic feedback on a wide variety of devices in the modern day, including cellphones, tablets, and smartwatches. However haptic alerts can quickly become disruptive rather than helpful to a user when multiple devices are providing feedback simultaneously or consecutively. Thus in this paper, we propose an intercommunicating, turn-based local network between a user's devices. This will allow a guaranteed minimal time span between device alerts. Additionally, when multiple devices provide a notification-based haptic alert, devices often produce different feedback due to the varying materials they are placed on. To address this, our framework allows devices to self-regulate their levels of haptic responses based on the material density of the surface they are placed on. This allows the framework to enforce a uniform level of haptic feedback across all the surface-device combinations. Finally, we will also utilize this common network to eliminate redundant alerts across devices.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984769"}, {"primary_key": "4196540", "vector": [], "sparse_vector": [], "title": "3D Printed Physical Interfaces that can Extend Touch Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a method to create a physical interface that allows touch input to be transferred from an external surface attached to a touch panel. Our technique prints a grid having multiple conductive points using an FDM-based 3D printer. When the user touches the conductive points, touch input is generated. This allows the user to control the touch input at arbitrary locations on an XY plane. By arranging the structure of the conductive wiring inside a physical object, a variety of interfaces can be realized.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985700"}, {"primary_key": "4196541", "vector": [], "sparse_vector": [], "title": "Interaction Technique Using Acoustic Sensing for Different Squeak Sounds Caused by Number of Rubbing Fingers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Various studies have been conducted for developing interaction techniques in a smart house. Some of our previous studies [1, 2] focused on bathrooms and we converted an existing normal bathtub system into a user interface by using embedded sensors. A system called Bathcratch [2] detects squeak sounds by rubbing on a bathtub edge. To generate squeaks, it requires some conditions to cause the Stick-slip phenomenon. A bathtub has a smooth surface, and water can cause the phenomenon with human skins. Bathcratch uses it as an interaction technique to play DJ-scratching. Here, we extended the interaction technique using squeaks to recognize rubbing states, rubbing events including sequence, and the difference between squeaks caused by the number of fingers. This can be used in various wet environments including kitchen, washbowls in a restroom, swimming pool, and spa. This paper describes the method and its performance for identifying the number of rubbing fingers by using frequency analysis. In addition, we illustrate some smart home applications by using the proposed technique.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985740"}, {"primary_key": "4196542", "vector": [], "sparse_vector": [], "title": "Music Composition with Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Creating a piece of music requires deep knowledge of composition, and is time-consuming even for experts. Algorithmic composition systems can generate pieces in an existing style. However, these systems are not interactive. Therefore, it is difficult for them to express the user's intention. We propose a system that recommends a continuation melody in accordance with a melody expressed by the user. Recommendation uses the style of the piece of the composer, thus users give the system a piece of the style in which they want to compose. With this system, users can compose pieces tailored to their needs, and composers can get assistance with composition.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985733"}, {"primary_key": "4196543", "vector": [], "sparse_vector": [], "title": "SketchingWithHands: 3D Sketching Handheld Products with First-Person Hand Posture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present SketchingWithHands, a 3D sketching system that incorporates a hand-tracking sensor. The system enables product designers to easily capture desired hand postures from a first-person point of view at any time and to use the captured hand information to explore handheld product concepts by 3D sketching while keeping the proper scale and usage of the products. Based on the analysis of design practices and drawing skills in the art and design literature, we suggest novel ideas for efficiently acquiring hand postures (palm-pinning widget, front and center mirrors, responsive spangles), for quickly creating and easily adjusting sketch planes (modified tick-triggered, orientable and shiftable sketch planes), for appropriately starting 3D sketching products with hand information (hand skeleton, grip axis), and for practically increasing user throughput (intensifier, rough and precise erasers)---all of which are coherently and consistently integrated in our system. A user test by ten industrial design students and an in-depth discussion show that our system is both useful and usable in designing handheld products.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984567"}, {"primary_key": "4196544", "vector": [], "sparse_vector": [], "title": "MagTacS: Delivering Tactile Sensation over an Object.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soon-<PERSON><PERSON><PERSON>"], "summary": "A system that can deliver tactile sensation despite an object existing between an actuator and human was developed. This system composed of a control part, power part, output part, and coil. The control part controls the overall system using a microcontroller. The power part generates electric current to create a magnetic field. The output part delivers high energies to the coil. The coil generates a time-varying magnetic field to induce current flow within the body. Through the tactile sensation recognition test, delivery of tactile sensation was confirmed in the air even an object existed between actuator and human skin.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985698"}, {"primary_key": "4196545", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: Prototyping Tool for Linkage-Based Mechanism Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present M<PERSON>Sketch, a prototyping tool to support non-experts to design and build linkage-based mechanism prototype. It enables users to draw and simulate arbitrary mechanisms as well as to make physical prototype for testing actual movement. Mix of bottom-up and top-down sketching approaches, real-time movement visualization, and functions for digital fabrication can make the users to design the desired mechanism easily and effectively. M.Sketch can be used to design customized products with kinetic movement, such as interactive robot, toys, and sculptures.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985709"}, {"primary_key": "4196546", "vector": [], "sparse_vector": [], "title": "Histogram: Spatiotemporal Photo-Displaying Interface.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the smartphone has become more widely available, we easily take photos and upload them online to share with others. Photographs are abundant, but they are not used properly, even though they provide meaningful information about the social scenes of our daily lives. To address this issue, Histogram was created as a new interface for displaying and sharing location-related photographs chronologically to trace the changes in a location. The prototype of this system is mobile-optimized to encourage users to easily upload photos with their smartphones, so that the system can be run through social cooperative work.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984759"}, {"primary_key": "4196547", "vector": [], "sparse_vector": [], "title": "Synesthesia Suit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985739"}, {"primary_key": "4196548", "vector": [], "sparse_vector": [], "title": "Computational Design Driven by Aesthetic Preference.", "authors": ["<PERSON><PERSON>"], "summary": "Tweaking design parameters is one of the most fundamental tasks in many design domains. In this paper, we describe three computational design methods for parameter tweaking tasks in which aesthetic preference---how aesthetically preferable the design looks---is used as a criterion to be maximized. The first method estimates a preference distribution in the target parameter space using crowdsourced human computation. The estimated preference distribution is then used in a design interface to facilitate interactive design exploration. The second method also estimates a preference distribution and uses it in an interface, but the distribution is estimated using the editing history of the target user. In contrast to these two methods, the third method automatically finds the best parameter that maximizes aesthetic preference, without requiring the user of this method to manually tweak parameters. This is enabled by implementing optimization algorithms using crowdsourced human computation. We validated these methods mainly in the scenario of photo color enhancement where parameters, such as brightness and contrast, need to be tweaked.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984781"}, {"primary_key": "4196549", "vector": [], "sparse_vector": [], "title": "Watch Commander: A Gesture-based Invocation System for Rectangular Smartwatches using B2B-Swipe.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Watch Commander, a gesture-based invocation system for rectangular smartwatches. Watch Commander allows the user to invoke functions easily and quickly by using Bezel to Bezel-Swipe (B2B-Swipe). This is because B2B-Swipe does not conflict with other swipe gestures such as flick and bezel swipe and can be performed in an eyes-free manner. Moreover, by providing GUIs that display functions assigned with B2B-Swipe, Watch Commander helps the user memorize those functions.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985697"}, {"primary_key": "4196550", "vector": [], "sparse_vector": [], "title": "CloakingNote: A Novel Desktop Interface for Subtle Writing Using Decoy Texts.", "authors": ["Sehi L&apos;Yi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinwook Seo"], "summary": "We present CloakingNote, a novel desktop interface for subtle writing. The main idea of CloakingNote is to misdirect observers' attention away from a real text by using a prominent decoy text. To assess the subtlety of CloakingNote, we conducted a subtlety test while varying the contrast ratio between the real text and its background. Our results demonstrated that the real text as well as the interface itself were subtle even when participants were aware that a writer might be engaged in suspicious activities. We also evaluated the feasibility of CloakingNote through a performance test and categorized the users' layout strategies.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984571"}, {"primary_key": "4196551", "vector": [], "sparse_vector": [], "title": "Crowdsourced Fabrication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, extensive research in the HCI literature has explored interactive techniques for digital fabrication. However, little attention in this body of work has examined how to involve and guide human workers in fabricating larger-scale structures. We propose a novel model of crowdsourced fabrication, in which a large number of workers and volunteers are guided through the process of building a pre-designed structure. The process is facilitated by an intelligent construction space capable of guiding individual workers and coordinating the overall build process. More specifically, we explore the use of smartwatches, indoor location sensing, and instrumented construction materials to provide real-time guidance to workers, coordinated by a foreman engine that manages the overall build process. We report on a three day deployment of our system to construct a 12-tall bamboo pavilion with assistance from more than one hundred volunteer workers, and reflect on observations and feedback collected during the exhibit.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984553"}, {"primary_key": "4196552", "vector": [], "sparse_vector": [], "title": "ViBand: High-Fidelity Bio-Acoustic Sensing Using Commodity Smartwatch Accelerometers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartwatches and wearables are unique in that they reside on the body, presenting great potential for always-available input and interaction. Their position on the wrist makes them ideal for capturing bio-acoustic signals. We developed a custom smartwatch kernel that boosts the sampling rate of a smartwatch's existing accelerometer to 4 kHz. Using this new source of high-fidelity data, we uncovered a wide range of applications. For example, we can use bio-acoustic data to classify hand gestures such as flicks, claps, scratches, and taps, which combine with on-device motion tracking to create a wide range of expressive input modalities. Bio-acoustic sensing can also detect the vibrations of grasped mechanical or motor-powered objects, enabling passive object recognition that can augment everyday experiences with context-aware functionality. Finally, we can generate structured vibrations using a transducer, and show that data can be transmitted through the human body. Overall, our contributions unlock user interface techniques that previously relied on special-purpose and/or cumbersome instrumentation, making such interactions considerably more feasible for inclusion in future consumer devices.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984582"}, {"primary_key": "4196553", "vector": [], "sparse_vector": [], "title": "Peripersonal Space in Virtual Reality: Navigating 3D Space with Different Perspectives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce the concept of \"peripersonal space\" of an avatar in 3D virtual reality and discuss how it plays an important role on 3D navigation with different perspectives. By analyzing the eye-gaze data of avatar-based navigation with first-person perspective and third-person perspective, we examine the effects of an avatar's peripersonal space on the users' perceptual scopes within 3D virtual environments. We propose that manipulating peripersonal space of an avatar with various perspectives has the immediate effects on the users' scopes of perception as well as the patterns of attentional capture. This study provides a helpful guideline for designing more effective navigation system with an avatar in 3D virtual environment.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984772"}, {"primary_key": "4196554", "vector": [], "sparse_vector": [], "title": "LaserStroke: Mid-air Tactile Experiences on Contours Using Indirect Laser Radiation.", "authors": ["<PERSON><PERSON> Lee", "<PERSON><PERSON>", "Junsuk Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Soon-<PERSON><PERSON><PERSON>"], "summary": "This demonstration presents a novel form of mid-air tactile display, LaserStroke, that makes use of a laser irradiated on the elastic medium attached to the skin. LaserStroke extends a laser device with an orientation control platform and a magnetic tracker so that it can elicit tapping and stroking sensations to a user's palm from a distance. LaserStroke offers unique tactile experiences while a user freely moves his/her hand in midair.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985708"}, {"primary_key": "4196555", "vector": [], "sparse_vector": [], "title": "Designing a Non-contact Wearable Tactile Display Using Airflows.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional wearable tactile displays transfer tactile stimulations through a firm contact between the stimulator and the skin. We conjecture that a firm contact may not be always possible and acceptable. Therefore, we explored the concept of a non-contact wearable tactile display using an airflow, which can transfer information without a firm contact. To secure an empirical ground for the design of a wearable airflow display, we conducted a series of psychophysical experiments to estimate the intensity thresholds, duration thresholds, and distance thresholds of airflow perception on various body locations, and report the resulting empirical data in this paper. We then built a 4-point airflow display, compared its performance with that of a vibrotactile display, and could show that the two tactile displays are comparable in information transfer performance. User feedback was also positive and revealed many unique expressions describing airflow-based tactile experiences. Lastly, we demonstrate the feasibility of an airflow-based wearable tactile display with a prototype using micro-fans.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984583"}, {"primary_key": "4196556", "vector": [], "sparse_vector": [], "title": "Analysis of Sequential Tasks in Use Context of Mobile Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most of the work on context-aware systems has focused on the context of time, location, and activity. Previous studies on the context flow have been primarily conducted on a qualitative basis. This paper proposes a new approach from a quantitative perspective. We gathered the data from automated task service, \"If This Then That (IFTTT)\", and analyzed the sequential tasks in terms of event occurrence in smart devices through association rule mining. We found out three consecutive tasks in cross-applications. The results of analysis have potential to find hidden use patterns as telling what kinds of services and channels are associated with each other. The findings provide some insights on the development of design guidelines for context-aware services.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984777"}, {"primary_key": "4196557", "vector": [], "sparse_vector": [], "title": "proCover: Sensory Augmentation of Prosthetic Limbs Using Smart Textile Covers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's commercially available prosthetic limbs lack tactile sensation and feedback. Recent research in this domain focuses on sensor technologies designed to be directly embedded into future prostheses. We present a novel concept and prototype of a prosthetic-sensing wearable that offers a non-invasive, self-applicable and customizable approach for the sensory augmentation of present-day and future low to mid-range priced lower-limb prosthetics. From consultation with eight lower-limb amputees, we investigated the design space for prosthetic sensing wearables and developed novel interaction methods for dynamic, user-driven creation and mapping of sensing regions on the foot to wearable haptic feedback actuators. Based on a pilot-study with amputees, we assessed the utility of our design in scenarios brought up by the amputees and we summarize our findings to establish future directions for research into using smart textiles for the sensory enhancement of prosthetic limbs.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984572"}, {"primary_key": "4196558", "vector": [], "sparse_vector": [], "title": "Polyspector™: An Interactive Visualization Platform Optimized for Visual Analysis of Big Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advent of the 'big data' era, there are unprecedented opportunities and challenges to explore complex and large datasets. In the paper, we introduce Polyspector, a web-based interactive visualization platform optimized for interactive visual analysis with two distinguishing features. Firstly, a visualization-specific database engine based on pixel-aware aggregation is implemented to generate views of hundreds of millions of data items within a second even with an off-the-shelf PC. Secondly, a novel deep-linking mechanism, combined with the pixel-aware aggregation, is exploited to realize interactive visual analysis interfaces such as zooming, overview + detail, context + focus etc.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985720"}, {"primary_key": "4196559", "vector": [], "sparse_vector": [], "title": "OmniEyeball: Spherical Display Embedded With Omnidirectional Camera Using Dynamic Spherical Mapping.", "authors": ["Zhengqing Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, 360-degree panorama and spherical displays have received more and more attention due to their unique panoramic properties. Compared with existing works, we plan to utilize omnidirectional cameras in our spherical display system to enable omnidirectional panoramic image as input and output. In our work, we present a novel movable spherical display embedded with omnidirectional cameras. Our system can use embedded cameras to shoot 360-degree panoramic video and project the live stream from its cameras onto its spherical display in real time.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984765"}, {"primary_key": "4196560", "vector": [], "sparse_vector": [], "title": "EdgeVib: Effective Alphanumeric Character Output Using a Wrist-Worn Tactile Display.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents EdgeVib, a system of spatiotemporal vibration patterns for delivering alphanumeric characters on wrist-worn vibrotactile displays. We first investigated spatiotemporal pattern delivery through a watch-back tactile display by performing a series of user studies. The results reveal that employing a 2×2 vibrotactile array is more effective than employing a 3×3 one, because the lower-resolution array creates clearer tactile sensations in less time consumption. We then deployed EdgeWrite patterns on a 2×2 vibrotactile array to determine any difficulties of delivering alphanumerical characters, and then modified the unistroke patterns into multistroke EdgeVib ones on the basis of the findings. The results of a 24-participant user study reveal that the recognition rates of the modified multistroke patterns were significantly higher than the original unistroke ones in both alphabet (85.9% vs. 70.7%) and digits (88.6% vs. 78.5%) delivery, and a further study indicated that the techniques can be generalized to deliver two-character compound messages with recognition rates higher than 83.3%. The guidelines derived from our study can be used for designing watch-back tactile displays for alphanumeric character output.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984522"}, {"primary_key": "4196561", "vector": [], "sparse_vector": [], "title": "OctaRing: Examining Pressure-Sensitive Multi-Touch Input on a Finger Ring Device.", "authors": ["Hyunchul Lim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Oh", "SoHyun Park", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce OctaRing, an octagon-shaped finger ring device that facilitates pressure-sensitive multi- touch gestures. To explore the feasibility of its prototype, we conducted an experiment and investigated users' sensorimotor skills in exerting different levels of pressure on the ring with more than one finger. The results of the experiment indicate that users are comfortable with the two-finger touch configuration with two levels of pressure. Based on this result, future work will explore novel gestures involving a finger ring device.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984780"}, {"primary_key": "4196562", "vector": [], "sparse_vector": [], "title": "Habitsourcing: Sensing the Environment through Immersive, Habit-Building Experiences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Citizen science and communitysensing applications allow everyday citizens to collect data about the physical world to benefit science and society. Yet despite successes, current approaches are still limited by the number of domain-interested volunteers who are willing and able to contribute useful data. In this paper we introduce habitsourcing, an alternative approach that harnesses the habit-building practices of millions of people to collect environmental data. To support the design and development of habitsourcing apps, we present (1) interaction techniques and design principles for sensing through actuation, a method for acquiring sensing data from cued interactions; and (2) ExperienceKit, an iOS library that makes it easy for developers to build and test habitsourcing applications. In two experiments, we show that our two proof-of-concept apps, ZenWalk and Zombies Interactive, compare favorably to their non-data collecting counterparts, and that we can effectively extract environmental data using simple detection techniques.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984533"}, {"primary_key": "4196563", "vector": [], "sparse_vector": [], "title": "Changing the Appearance of Physical Interfaces Through Controlled Transparency.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present physical interfaces that change their appearance through controlled transparency. These transparency-controlled physical interfaces are well suited for applications where communication through optical appearance is sufficient, such as ambient display scenarios. They transition between perceived shapes within milliseconds, require no mechanically moving parts and consume little energy. We build 3D physical interfaces with individually controllable parts by laser cutting and folding a single sheet of transparency-controlled material. Electrical connections are engraved in the surface, eliminating the need for wiring individual parts. We consider our work as complementary to current shape-changing interfaces. While our proposed interfaces do not exhibit dynamic tangible qualities, they have unique benefits such as the ability to create apparent holes or nesting of objects. We explore the benefits of transparency-controlled physical interfaces by characterizing their design space and showcase four physical prototypes: two activity indicators, a playful avatar, and a lamp shade with dynamic appearance.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984556"}, {"primary_key": "4196564", "vector": [], "sparse_vector": [], "title": "Fluxa: Body Movements as a Social Display.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Fluxa, a compact wearable device that exploits body movements, as well as the visual effects of persistence of vision (POV), to generate mid-air displays on and around the body. When the user moves his/her limb, <PERSON><PERSON><PERSON> displays a pattern that, due to retinal afterimage, can be perceived by the surrounding people. We envision Fluxa as a wearable display to foster social interactions. It can be used to enhance existing social gestures such as hand-waving to get attention, as a communicative tool that displays the speed and distance covered by joggers, and as a self-expression device that generates images while dancing. We discuss the advantages of Fluxa: a display size that could be much larger than the device itself, a semi-transparent display that allows users and others to see though it and promotes social interaction.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985741"}, {"primary_key": "4196565", "vector": [], "sparse_vector": [], "title": "Aesthetic Electronics: Designing, Sketching, and Fabricating Circuits through Digital Exploration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Jasper O&apos;<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As interactive electronics become increasingly intimate and personal, the design of circuitry is correspondingly developing a more playful and creative aesthetic. Circuit sketching and design is a multidimensional activity which combines the arts, crafts, and engineering broadening participation of electronic creation to include makers of diverse backgrounds. In order to support this design ecology, we present Ellustrate, a digital design tool that enables the functional and aesthetic design of electronic circuits with multiple conductive and dielectric materials. Ellustrate guides users through the fabrication and debugging process, easing the task of practical circuit creation while supporting designers' aesthetic decisions throughout the circuit authoring workflow. In a formal user study, we demonstrate how Ellustrate enables a new electronic design conversation that combines electronics, materials, and visual aesthetic concerns.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984579"}, {"primary_key": "4196566", "vector": [], "sparse_vector": [], "title": "Muscle-plotter: An Interactive System based on Electrical Muscle Stimulation that Produces Spatial Output.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore how to create interactive systems based on electrical muscle stimulation that offer expressive output. We present muscle-plotter, a system that provides users with input and output access to a computer system while on the go. Using pen-on-paper interaction, muscle-plotter allows users to engage in cognitively demanding activities, such as writing math. Users write formulas using a pen and the system responds by making the users' hand draw charts and widgets. While Anoto technology in the pen tracks users' input, muscle-plotter uses electrical muscle stimulation (EMS) to steer the user's wrist so as to plot charts, fit lines through data points, find data points of interest, or fill in forms. We demonstrate the system at the example of six simple applications, including a wind tunnel simulator.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984530"}, {"primary_key": "4196567", "vector": [], "sparse_vector": [], "title": "LIME: LIquid MEtal Interfaces for Non-Rigid Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Room-temperature liquid metal GaIn25 (Eutectic Gallium- Indium alloy, 75% gallium and 25% indium) has distinctive properties of reversible deformation and controllable locomotion under an external electric field stimulus. Liquid metal's newly discovered properties imply great possibilities in developing new technique for interface design. In this paper, we present LIME, LIquid MEtal interfaces for non-rigid interaction. We first discuss the interaction potential of LIME interfaces. Then we introduce the development of LIME cells and the design of some LIME widgets.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984562"}, {"primary_key": "4196568", "vector": [], "sparse_vector": [], "title": "ScalableBody: A Telepresence Robot Supporting Socially Acceptable Interactions and Human Augmentation through Vertical Actuation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Most telepresence robots have a fixed-size body, and are unable to change the camera or display position. Therefore, although making eye contact is important in human expression, current fixed-size telepresence robots fail to achieve this.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985718"}, {"primary_key": "4196569", "vector": [], "sparse_vector": [], "title": "Towards Understanding Collaboration around Interactive Surfaces: Exploring Joint Visual Attention.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this abstract, we present a novel method for exploring the visual behavior of multiple users engaged in a collaborative task around an interactive surface. The proposed method synchronizes input from multiple eye trackers, describes the visual behavior of individual users over time, and identifies joint attention across multiple users. We applied this method to analyze the visual behavior of four users collaborating using a large-scale multi-touch tabletop.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984778"}, {"primary_key": "4196570", "vector": [], "sparse_vector": [], "title": "Ballumiere: Real-Time Tracking and Projection for High-Speed Moving Balls.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Projection onto moving objects has a serious slipping problem due to delay between tracking and projection. We propose a new method to overcome the delay problem, and we succeed in increasing the accuracy of projection. We present <PERSON><PERSON><PERSON> as a demo for projection to volleyballs and juggling balls.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985717"}, {"primary_key": "4196571", "vector": [], "sparse_vector": [], "title": "Wrap &amp; Sense: Grasp Capture by a Band Sensor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a bare hand grasp observation system named Wrap & Sense. We built a band type sensing equipment composed of infrared distance sensors placed in an array. The sensor band is attached to a target object with all sensors directed along the object surface and detects the hand side edge with respect to the object. Assuming type of grasp as 'power grasp', the whole hand posture can be determined according to the 3D shape of the object. Three types of application are shown as proof-of-concept.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985713"}, {"primary_key": "4196572", "vector": [], "sparse_vector": [], "title": "Digital Gastronomy: Methods &amp; Recipes for Hybrid Cooking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Rotem Gruber", "<PERSON>", "<PERSON><PERSON>"], "summary": "Several recent projects have introduced digital machines to the kitchen, yet their impact on culinary culture is limited. We envision a culture of Digital Gastronomy that enhances traditional cooking with new interactive capabilities, rather than replacing the chef with an autonomous machine. Thus, we deploy existing digital fabrication instruments in traditional kitchen and integrate them into cooking via hybrid recipes. This concept merges manual and digital procedures, and imports parametric design tools into cooking, allowing the chef to personalize the tastes, flavors, structures and aesthetics of dishes. In this paper we present our hybrid kitchen and the new cooking methodology, illustrated by detailed recipes with degrees of freedom that can be set digitally prior to cooking. Lastly, we discuss future work and conclude with thoughts on the future of hybrid gastronomy.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984528"}, {"primary_key": "4196573", "vector": [], "sparse_vector": [], "title": "Partial Bookmarking: A Structure-independent Mechanism of Transclusion for a Portion of any Web Page.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A novel mechanism of transclusion for collecting and producing information on the Web, named partial bookmarking, is proposed. Partial bookmarking allows a user to collect portions of any web page by making it able to use for a spatial hypertext, like a web document element, without the need to duplicate its contents. Whereas the previous studies involving transclusion required pre-designed linkable objects, such as XML elements or HTML objects, partial bookmarking does not rely on any document structure. To accomplish partial bookmarking, we enhanced a conventional web browser with multiple tabs by introducing the technology of mirroring to display only a portion of a web page appropriately while factoring in potential copyright issues.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984761"}, {"primary_key": "4196574", "vector": [], "sparse_vector": [], "title": "ChainFORM: A Linear Integrated Modular Hardware System for Shape Changing Interfaces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents ChainFORM: a linear, modular, actuated hardware system as a novel type of shape changing interface. Using rich sensing and actuation capability, this modular hardware system allows users to construct and customize a wide range of interactive applications. Inspired by modular and serpentine robotics, our prototype comprises identical modules that connect in a chain. Modules are equipped with rich input and output capability: touch detection on multiple surfaces, angular detection, visual output, and motor actuation. Each module includes a servo motor wrapped with a flexible circuit board with an embedded microcontroller.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984587"}, {"primary_key": "4196575", "vector": [], "sparse_vector": [], "title": "Luminescent Tentacles: A Scalable SMA Motion Display.", "authors": ["<PERSON>"], "summary": "The Luminescent Tentacles system is a scalable kinetic surface system for kinetic art, ambient display, and animatronics. The 256 shape-memory alloy actuators react to hand movement by fluid dynamics and Kinect. These actuators behave like waving tentacles of sea anemones under the sea, and the top of the actuator softly glows like a bioluminescent organism. To precisely control a large number of actuators simultaneously, the system utilizes one microcontroller per actuator for distributed processing. In addition, it provides a scalable platform, which can be easily built into various forms.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985695"}, {"primary_key": "4196576", "vector": [], "sparse_vector": [], "title": "Next-Point Prediction Metrics for Perceived Spatial Errors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Touch screens have a delay between user input and corresponding visual interface feedback, called input 'latency' (or 'lag'). Visual latency is more noticeable during continuous input actions like dragging, so methods to display feedback based on the most likely path for the next few input points have been described in research papers and patents. Designing these 'next-point prediction' methods is challenging, and there have been no standard metrics to compare different approaches. We introduce metrics to quantify the probability of 7 spatial error 'side-effects' caused by next-point prediction methods. Types of side-effects are derived using a thematic analysis of comments gathered in a 12 participants study covering drawing, dragging, and panning tasks using 5 state-of-the-art next-point predictors. Using experiment logs of actual and predicted input points, we develop quantitative metrics that correlate positively with the frequency of perceived side-effects. These metrics enable practitioners to compare next-point predictors using only input logs.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984590"}, {"primary_key": "4196577", "vector": [], "sparse_vector": [], "title": "Study on Control Method of Virtual Food Texture by Electrical Muscle Stimulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose Electric Food Texture System, which can present virtual food texture such as hardness and elasticity by electrical muscle stimulation (EMS) to the masseter muscle. In our previous study, we investigated the feasibility to detect user's bite with a photoreflector and that to construct database of food texture with electromyography sensors. In this paper, we investigated the feasibility to control virtual food texture by EMS. We conducted an experiment to reveal the relationship of the parameters of EMS and those of virtual food texture. The experimental results show that the higher strength of EMS is, the harder virtual food texture is, and the longer duration of EMS is, the more elastic virtual food texture is.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984768"}, {"primary_key": "4196578", "vector": [], "sparse_vector": [], "title": "Friend*Chip: A Bracelet with Digital Pet for Socially Inclusive Games for Children.", "authors": ["Eleuda Nuñez", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning in groups have different potential benefits for children. They have the opportunity to solve problems together, to share experiences and to develop social skills. However, from teachers point of view, creating a safe and inclusive positive environment for children is not an simple task since each child has differences that represent a challenge for implementing effectively group dynamics. The focus of this work is the design of a system that motivates children to approach to others and create opportunities of social interaction. The system creates a fun and enjoyable situation that is always supervised by the teacher, who can monitor and change the group dynamics at any moment during the activity.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984775"}, {"primary_key": "4196579", "vector": [], "sparse_vector": [], "title": "Holoportation: Virtual 3D Teleportation in Real-time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Mingsong Dou", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Julien P<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an end-to-end system for augmented and virtual reality telepresence, called Holoportation. Our system demonstrates high-quality, real-time 3D reconstructions of an entire space, including people, furniture and objects, using a set of new depth cameras. These 3D models can also be transmitted in real-time to remote users. This allows users wearing virtual or augmented reality displays to see, hear and interact with remote participants in 3D, almost as if they were present in the same physical space. From an audio-visual perspective, communicating and interacting with remote users edges closer to face-to-face communication. This paper describes the Holoportation technical system in full, its key interactive capabilities, the application scenarios it enables, and an initial qualitative study of using this new communication medium.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984517"}, {"primary_key": "4196580", "vector": [], "sparse_vector": [], "title": "Representing Gaze Direction in Video Communication Using Eye-Shaped Display.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hideaki <PERSON>", "<PERSON><PERSON>"], "summary": "A long-standing challenge of video-mediated communication systems is to correctly represent a remote participant's gaze direction in local environments. To address this problem, we developed a video communication system using an \"eye-shaped display.\" This display is made of an artificial ulexite (TV rock) that is cut into a hemispherical shape, enabling the light from the bottom surface to be projected onto the curved surface. By displaying a simulated iris onto the eye-shaped display, we theorize that our system can represent the gaze direction as accurately as a real human eye.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985705"}, {"primary_key": "4196581", "vector": [], "sparse_vector": [], "title": "aeroMorph - Heat-sealing Inflatable Shape-change Materials for Interaction Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a design, simulation, and fabrication pipeline for making transforming inflatables with various materials. We introduce a bending mechanism that creates multiple, programmable shape-changing behaviors with inextensible materials, including paper, plastics and fabrics. We developed a software tool that generates these bending mechanism for a given geometry, simulates its transformation, and exports the compound geometry as digital fabrication files. We show a range of fabrication methods, from manual sealing, to heat pressing with custom stencils and a custom heat-sealing head that can be mounted on usual 3-axis CNC machines to precisely fabricate the designed transforming material. Finally, we present three applications to show how this technology could be used for designing interactive wearables, toys, and furniture.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984520"}, {"primary_key": "4196582", "vector": [], "sparse_vector": [], "title": "Physiological Signal-Driven Virtual Reality in Social Spaces.", "authors": ["<PERSON>"], "summary": "Virtual and augmented reality are becoming the new medium that transcend the way we interact with virtual content, paving the way for many immersive and interactive forms of applications. The main purpose of my research is to create a seamless combination of physiological sensing with virtual reality to provide users with a new layer of input modality or as a form of implicit feedback. To achieve this, my research focuses in novel augmented reality (AR) and virtual reality (VR) based application for a multi-user, multi-view, multi-modal system augmented by physiological sensing methods towards an increased public and social acceptance.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984787"}, {"primary_key": "4196583", "vector": [], "sparse_vector": [], "title": "Transparent Reality: Using Eye Gaze Focus Depth as Interaction Modality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel, eye gaze based interaction technique, using focus depth as an input modality for virtual reality (VR) applications. We also show custom hardware prototype implementation. Comparing the focus depth based interaction to a scroll wheel interface, we find no statistically significant difference in performance (the focus depth works slightly better) and a subjective preference of the users in a user study with 10 participants playing a simple VR game. This indicates that it is a suitable interface modality that should be further explored. Finally, we give some application scenarios and guidelines for using focus depth interactions in VR applications.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984754"}, {"primary_key": "4196584", "vector": [], "sparse_vector": [], "title": "VidCrit: Video-based Asynchronous Video Review.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video production is a collaborative process in which stakeholders regularly review drafts of the edited video to indicate problems and offer suggestions for improvement. Although practitioners prefer in-person feedback, most reviews are conducted asynchronously via email due to scheduling and location constraints. The use of this impoverished medium is challenging for both providers and consumers of feedback. We introduce VidCrit, a system for providing asynchronous feedback on drafts of edited video that incorporates favorable qualities of an in-person review. This system consists of two separate interfaces: (1) A feedback recording interface captures reviewers' spoken comments, mouse interactions, hand gestures and other physical reactions. (2) A feedback viewing interface transcribes and segments the recorded review into topical comments so that the video author can browse the review by either text or timelines. Our system features novel methods to automatically segment a long review session into topical text comments, and to label such comments with additional contextual information. We interviewed practitioners to inform a set of design guidelines for giving and receiving feedback, and based our system's design on these guidelines. Video reviewers using our system preferred our feedback recording interface over email for providing feedback due to the reduction in time and effort. In a fixed amount of time, reviewers provided 10.9 (σ=5.09) more local comments than when using text. All video authors rated our feedback viewing interface preferable to receiving feedback via e-mail.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984552"}, {"primary_key": "4196585", "vector": [], "sparse_vector": [], "title": "Thermocons: Evaluating the Thermal Haptic Perception of the Forehead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Thermocons describes our work in progress for evaluating thermal haptic feedback on the forehead as a viable feedback modality for integration with head mounted devices. The purpose was to identify the thermal perception for simultaneous feedback at three locations of the forehead. We provided hot-only, cold-only and hot/cold-mixed thermal stimulations at these location to identify the sensitivity for accurate perception. Our evaluation with 9 participants indicated that perceiving cold-only stimulations were significantly better with an accuracy of 88%. The perception accuracy for hot-only and hot/cold-mixed stimulations were 66% and 65% respectively.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984762"}, {"primary_key": "4196586", "vector": [], "sparse_vector": [], "title": "A 3D Printer for Interactive Electromagnetic Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new form of low-cost 3D printer to print interactive electromechanical objects with wound in place coils. At the heart of this printer is a mechanism for depositing wire within a five degree of freedom (5DOF) fused deposition modeling (FDM) 3D printer. Copper wire can be used with this mechanism to form coils which induce magnetic fields as a current is passed through them. Soft iron wire can additionally be used to form components with high magnetic permeability which are thus able to shape and direct these magnetic fields to where they are needed. When fabricated with structural plastic elements, this allows simple but complete custom electromagnetic devices to be 3D printed. As examples, we demonstrate the fabrication of a solenoid actuator for the arm of a Lucky Cat figurine, a 6-pole motor stepper stator, a reluctance motor rotor and a Ferrofluid display. In addition, we show how printed coils which generate small currents in response to user actions can be used as input sensors in interactive devices.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984523"}, {"primary_key": "4196587", "vector": [], "sparse_vector": [], "title": "Gaze and Touch Interaction on Tablets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We explore how gaze can support touch interaction on tablets. When holding the device, the free thumb is normally limited in reach, but can provide an opportunity for indirect touch input. Here we propose gaze and touch input, where touches redirect to the gaze target. This provides whole-screen reachability while only using a single hand for both holding and input. We present a user study comparing this technique to direct-touch, showing that users are slightly slower but can utilise one-handed use with less physical effort. To enable interaction with small targets, we introduce CursorShift, a method that uses gaze to provide users temporal control over cursors during direct-touch interactions. Taken together, users can employ three techniques on tablets: direct-touch, gaze and touch, and cursor input. In three applications, we explore how these techniques can coexist in the same UI and demonstrate how tablet tasks can be performed with thumb-only input of the holding hand, and with it describe novel interaction techniques for gaze based tablet interaction.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984514"}, {"primary_key": "4196588", "vector": [], "sparse_vector": [], "title": "Developing fMRI-Compatible Interaction Systems through Air Pressure.", "authors": ["Handityo Au<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We leverage the use of air pressure to expand the interaction space within fMRI (functional magnetic resonance imaging). We present three example applications that are not previously possible in conventional fMRI interaction devices: 1) pedal interface that can record continuous pressure value pressed by users, 2) wrist tactile interface that can provide various tactile patterns or stimuli, 3) adjustable resistance joystick that can provide feedback through different resistance levels. Our work shows that the use of air pressure can enable new research opportunities for fMRI researchers.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984764"}, {"primary_key": "4196589", "vector": [], "sparse_vector": [], "title": "Virtual Sweet: Simulating Sweet Sensation Using Thermal Stimulation on the Tip of the Tongue.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Being a pleasurable sensation, sweetness is recognized as the most preferred sensation among the five primary taste sensations. In this paper, we present a novel method to virtually simulate the sensation of sweetness by applying thermal stimulation to the tip of the human tongue. To digitally simulate the sensation of sweetness, the system delivers rapid heating and cooling stimuli to the tongue via a 2x2 grid of Peltier elements. To achieve distinct, controlled, and synchronized temperature variations in the stimuli, a control module is used to regulate each of the Peltier elements. Results from our preliminary experiments suggest that the participants were able to perceive mild sweetness on the tip of their tongue while using the proposed system.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985729"}, {"primary_key": "4196590", "vector": [], "sparse_vector": [], "title": "AmbioTherm: Simulating Ambient Temperatures and Wind Conditions in VR Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As Virtual Reality (VR) experiences become increasingly popular, simulating sensory perceptions of environmental conditions is essential for providing an immersive user experience. In this paper, we present Ambiotherm, a wearable accessory for existing Head Mounted Displays (HMD), which simulates real-world environmental conditions such as ambient temperatures and wind conditions. The system consists of a wearable accessory for the HMD and a mobile application, which generates interactive VR environments and controls the thermal and wind stimuli. The thermal stimulation module is attached to the user's neck while two fans are focused on the user's face to simulate wind conditions. We demonstrate the Ambiotherm system with two VR environments, a desert and a snowy mountain, to showcase the different types of ambient temperatures and wind conditions that can be simulated. Results from initial user experiments show that the participants perceive VR environments to be more immersive when external thermal and wind stimuli are presented as part of the VR experience.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985712"}, {"primary_key": "4196591", "vector": [], "sparse_vector": [], "title": "CodeMend: Assisting Interactive Programming with Bimodal Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software APIs often contain too many methods and parameters for developers to memorize or navigate effectively. Instead, developers resort to finding answers through online search engines and systems such as Stack Overflow. However, the process of finding and integrating a working solution is often very time-consuming. Though code search engines have increased in quality, there remain significant language- and workflow-gaps in meeting end-user needs. <PERSON><PERSON> and intermediate programmers often lack the language to query, and the expertise in transferring found code to their task. To address this problem, we present CodeMend, a system to support finding and integration of code. CodeMend leverages a neural embedding model to jointly model natural language and code as mined from large Web and code datasets. We also demonstrate a novel, mixed-initiative, interface to support query and integration steps. Through CodeMend, end-users describe their goal in natural language. The system makes salient the relevant API functions, the lines in the end-user's program that should be changed, as well as proposing the actual change. We demonstrate the utility and accuracy of CodeMend through lab and simulation studies.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984544"}, {"primary_key": "4196592", "vector": [], "sparse_vector": [], "title": "NeverMind: Using Augmented Reality for Memorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "NeverMind is an interface and application designed to support human memory. We combine the memory palace memorization method with augmented reality technology to create a tool to help anyone memorize more effectively. Preliminary experiments show that content memorized with NeverMind remains longer in memory compared to general memorization techniques. With this project, we hope to make the memory palace method accessible to novices and demonstrate one way augmented reality can support learning.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984776"}, {"primary_key": "4196593", "vector": [], "sparse_vector": [], "title": "Mobile Fabrication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an exploration into the future of fabrication, in particular the vision of mobile fabrication, which we define as \"personal fabrication on the go\". We explore this vision with two surveys, two simple hardware prototypes, matching custom apps that provide users with access to a solution database, custom fabrication processes we designed specifically for these devices, and a user study conducted in situ on metro trains. Our findings suggest that mobile fabrication is a compelling next direction for personal fabrication. From our experience with the prototypes we derive hardware requirements to make mobile fabrication also technically feasible.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984586"}, {"primary_key": "4196594", "vector": [], "sparse_vector": [], "title": "Private Webmail 2.0: Simple and Easy-to-Use Secure Email.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Private Webmail 2.0 (Pwm 2.0) improves upon the current state of the art by increasing the usability and practical security of secure email for ordinary users. More users are able to send and receive encrypted emails without mistakenly revealing sensitive information. In this paper we describe four user interface traits that positively affect the usability and security of Pwm 2.0. In a user study involving 51 participants we validate that these interface modifications result in high usability, few mistakes, and a strong understanding of the protection provided to secure email messages. We also show that the use of manual encryption has no effect on usability or security.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984580"}, {"primary_key": "4196595", "vector": [], "sparse_vector": [], "title": "JOLED: A Mid-air Display based on Electrostatic Rotation of Levitated Janus Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present JOLED, a mid-air display for interactive physical visualization using Janus objects as physical voxels. The Janus objects have special surfaces that have two or more asymmetric physical properties at different areas. In JOLED, they are levitated in mid-air and controllably rotated to reveal their different physical properties. We made voxels by coating the hemispheres of expanded polystyrene beads with different materials, and applied a thin patch of titanium dioxide to induce electrostatic charge on them. Transparent indium tin oxide electrodes are used around the levitation volume to create a tailored electric field to control the orientation of the voxels. We propose a novel method to control the angular position of individual voxels in a grid using electrostatic rotation and their 3D position using acoustic levitation. We present a display in which voxels can be flipped independently, and two mid-air physical games with a voxel as the playable character that moves in 3D across other physical structures and rotates to reflect its status in the games. We demonstrate a voxel update speed of 37.8 ms/flip, which is video-rate.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984549"}, {"primary_key": "4196596", "vector": [], "sparse_vector": [], "title": "MlioLight: Multi-Layered Image Overlay using Multiple Flashlight Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a technique that overlays natural images on the real world using the information from multiple flashlight devices. We focus on finding areas of overlapping lights in a multiple light-source scenario and overlaying multi-layered information on a real world object in these areas.In order to mix multiple images, we developed a light identification and overlapping area detection technique using rapid synchronization between high-speed cameras and multiple light devices.In this paper, we describe the concept of our system and a prototype implementation.We also describe two different applications.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985719"}, {"primary_key": "4196597", "vector": [], "sparse_vector": [], "title": "Eviza: A Natural Language Interface for Visual Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Angel <PERSON>"], "summary": "Natural language interfaces for visualizations have emerged as a promising new way of interacting with data and performing analytics. Many of these systems have fundamental limitations. Most return minimally interactive visualizations in response to queries and often require experts to perform modeling for a set of predicted user queries before the systems are effective. Eviza provides a natural language interface for an interactive query dialog with an existing visualization rather than starting from a blank sheet and asking closed-ended questions that return a single text answer or static visualization. The system employs a probabilistic grammar based approach with predefined rules that are dynamically updated based on the data from the visualization, as opposed to computationally intensive deep learning or knowledge based approaches.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984588"}, {"primary_key": "4196598", "vector": [], "sparse_vector": [], "title": "Designing a Haptic Feedback System for Hearing-Impaired to Experience Tap Dance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this study, we have designed a system to enable hearing-impaired to enjoy the performance of tap dancers. This system transfers the haptic sensation of tap dancing from the stage to the audience and helps hearing-impaired people enjoy the vibration of the taps even if they cannot hear the sound. We organized an event to verify the effectiveness of the system. To do this, we collaborated with a tap dance unit and science museum. We found that our system succeeded in helping the tap dancers share the fun and enjoyment of dance with the audience comprising people with hearing disabilities.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985716"}, {"primary_key": "4196599", "vector": [], "sparse_vector": [], "title": "DriftBoard: A Panning-Based Text Entry Technique for Ultra-Small Touchscreens.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Beste F. Yu<PERSON>el", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging ultra-small wearables like smartwatches pose a design challenge for touch-based text entry. This is due to the \"fat-finger problem,\" wherein users struggle to select elements much smaller than their fingers. To address this challenge, we developed DriftBoard, a panning-based text entry technique where the user types by positioning a movable qwerty keyboard on an interactive area with respect to a fixed cursor point. In this paper, we describe the design and implementation of DriftBoard and report results of a user study on a watch-size touchscreen. The study compared DriftBoard to two ultra-small keyboards, ZoomBoard (tapping-based) and Swipeboard (swiping-based). DriftBoard performed comparably (no significant difference) to ZoomBoard in the major metrics of text entry speed and error rate, and outperformed Swipeboard, which suggests that panning-based typing is a promising input method for text entry on ultra-small touchscreens.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984591"}, {"primary_key": "4196600", "vector": [], "sparse_vector": [], "title": "Dynamic Authoring of Audio with Linked Scripts.", "authors": ["Hijung Valentina Shin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Speech recordings are central to modern media from podcasts to audio books to e-lectures and voice-overs. Authoring these recordings involves an iterative back and forth process between script writing/editing and audio recording/editing. Yet, most existing tools treat the script and the audio separately, making the back and forth workflow very tedious. We present Voice Script, an interface to support a dynamic workflow for script writing and audio recording/editing. Our system integrates the script with the audio such that, as the user writes the script or records speech, edits to the script are translated to the audio and vice versa. Through informal user studies, we demonstrate that our interface greatly facilitates the audio authoring process in various scenarios.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984561"}, {"primary_key": "4196601", "vector": [], "sparse_vector": [], "title": "Depth Based Shadow Pointing Interface for Public Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a robust pointing detection with virtual shadow representation for interacting with a public display. Using a depth camera, our shadow is generated by a model with an angled virtual sun light and detects the nearest point as a pointer. The position of the shadow becomes higher when user walks closer, which conveys the notion of correct distance to control the pointer and offers accessibility to the higher area of the display.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985710"}, {"primary_key": "4196602", "vector": [], "sparse_vector": [], "title": "IdeaHound: Improving Large-scale Collaborative Ideation with Crowd-Powered Real-time Semantic Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Prior work on creativity support tools demonstrates how a computational semantic model of a solution space can enable interventions that substantially improve the number, quality and diversity of ideas. However, automated semantic modeling often falls short when people contribute short text snippets or sketches. Innovation platforms can employ humans to provide semantic judgments to construct a semantic model, but this relies on external workers completing a large number of tedious micro tasks. This requirement threatens both accuracy (external workers may lack expertise and context to make accurate semantic judgments) and scalability (external workers are costly). In this paper, we introduce IdeaHound, an ideation system that seamlessly integrates the task of defining semantic relationships among ideas into the primary task of idea generation. The system combines implicit human actions with machine learning to create a computational semantic model of the emerging solution space. The integrated nature of these judgments allows IDEAHOUND to leverage the expertise and efforts of participants who are already motivated to contribute to idea generation, overcoming the issues of scalability inherent to existing approaches. Our results show that participants were equally willing to use (and just as productive using) IDEAHOUND compared to a conventional platform that did not require organizing ideas. Our integrated crowdsourcing approach also creates a more accurate semantic model than an existing crowdsourced approach (performed by external crowds). We demonstrate how this model enables helpful creative interventions: providing diverse inspirational examples, providing similar ideas for a given idea and providing a visual overview of the solution space.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984578"}, {"primary_key": "4196603", "vector": [], "sparse_vector": [], "title": "Mining Controller Inputs to Understand Gameplay.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Today's game analytics systems are powered by event logs, which reveal information about what players are doing but offer little insight about the types of gameplay that games foster. Moreover, the concept of gameplay itself is difficult to define and quantify. In this paper, we show that analyzing players' controller inputs using probabilistic topic models allows game developers to describe the types of gameplay -- or action -- in games in a quantitative way. More specifically, developers can discover the types of action that a game fosters and the extent that each game level fosters each type of action, all in an unsupervised manner. They can use this information to verify that their levels feature the appropriate style of gameplay and to recommend levels with gameplay that is similar to levels that players like. We begin with latent Dirichlet allocation (LDA), the simplest topic model, then develop the player-gameplay action (PGA) model to make the same types of discoveries about gameplay in a way that is independent of each player's play style. We train a player recognition system on the PGA model's output to verify that its discoveries about gameplay are in fact independent of each player's play style. The system recognizes players with over 90% accuracy in about 20 seconds of playtime.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984543"}, {"primary_key": "4196604", "vector": [], "sparse_vector": [], "title": "Sparkle: Towards Haptic Hover-Feedback with Electric Arcs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate a method for stimulating the fingertip with touchable electric arcs above a hover sensing input device. We built a hardware platform using a high-voltage resonant transformer for which we control the electric discharge to create in-air haptic feedback up to 4 mm in height, and combined this technology with infrared proximity sensing. Our method is a first step towards supporting novel in-air haptic experiences for hover input that does not require the user to wear haptic feedback stimulators.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985702"}, {"primary_key": "4196605", "vector": [], "sparse_vector": [], "title": "Asymmetric Design Approach and Collision Avoidance Techniques For Room-scale Multiplayer Virtual Reality.", "authors": ["<PERSON><PERSON>"], "summary": "Recent advances in consumer virtual reality (VR) technology have made it easy to accurately capture users' motions over room-sized areas allowing natural locomotion for navigation in VR. While this helps create a stronger match between proprioceptive information from human body movements for enhancing immersion and reducing motion sickness, it introduces a few challenges. Walking is only possible within virtual environments (VEs) that fit inside the boundaries of the tracked physical space which for most users is quite small. Within this space the potential for colliding with physical objects around the play area is high. Additionally, only limited haptic feedback is available. In this paper, I focus on the problem of variations in the size and shape of each user's tracked physical space for multiplayer interactions. As part of the constrained physical space problem, I also present an automated system for steering the user away from play area boundaries using Galvanic Vestibular Stimulation (GVS). In my thesis, I will build techniques to enable the system to intelligently apply redirection and GVS-based steering as users explore virtual environments of arbitrary sizes.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984788"}, {"primary_key": "4196606", "vector": [], "sparse_vector": [], "title": "Resolving Spatial Variation And Allowing Spectator Participation In Multiplayer VR.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multiplayer virtual reality (VR) games introduce the problem of variations in the physical size and shape of each user's space for mapping into a shared virtual space. We propose an asymmetric approach to solve the spatial variation problem, by allowing people to choose roles based on the size of their space. We demonstrate this concept through the implementation of a virtual snowball fight where players can choose from multiple roles, namely the shooter, the target, or an onlooker depending on whether the game is played remotely or together in one large space. In the co-located version, the target stands behind an actuated cardboard fort that responds to events in VR, providing non-VR spectators a way to participate in the experience. During preliminary deployment, users showed extremely positive reactions and the spectators were thrilled.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984779"}, {"primary_key": "4196607", "vector": [], "sparse_vector": [], "title": "Applications of Switchable Permanent Magnetic Actuators in Shape Change and Tactile Display.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Systems realizing shape change and tactile display remain hindered by the power, cost, and size limitations of current actuation technology. We describe and evaluate a novel use of switchable permanent magnets as a bistable actuator for haptic feedback which draws power only when switching states. Because of their efficiency, low cost, and small size, these actuators show promise in realizing tactile display within mobile, wearable, and embedded systems. We present several applications demonstrating potential uses in the mobile, automotive, and desktop computing domains, and perform a technical evaluation of the actuators used in these systems.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985728"}, {"primary_key": "4196608", "vector": [], "sparse_vector": [], "title": "AggreGaze: Collective Estimation of Audience Attention on Public Displays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Gaze is frequently explored in public display research given its importance for monitoring and analysing audience attention. However, current gaze-enabled public display interfaces require either special-purpose eye tracking equipment or explicit personal calibration for each individual user. We present AggreGaze, a novel method for estimating spatio-temporal audience attention on public displays. Our method requires only a single off-the-shelf camera attached to the display, does not require any personal calibration, and provides visual attention estimates across the full display. We achieve this by 1) compensating for errors of state-of-the-art appearance-based gaze estimation methods through on-site training data collection, and by 2) aggregating uncalibrated and thus inaccurate gaze estimates of multiple users into joint attention estimates. We propose different visual stimuli for this compensation: a standard 9-point calibration, moving targets, text and visual stimuli embedded into the display content, as well as normal video content. Based on a two-week deployment in a public space, we demonstrate the effectiveness of our method for estimating attention maps that closely resemble ground-truth audience gaze distributions.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984536"}, {"primary_key": "4196609", "vector": [], "sparse_vector": [], "title": "Facial Expression Mapping inside Head Mounted Display by Embedded Optical Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Head Mounted Display (HMD) provides an immersive ex-perience in virtual environments for various purposes such as for games and communication. However, it is difficult to capture facial expression in a HMD-based virtual environ-ment because the upper half of user's face is covered up by the HMD. In this paper, we propose a facial expression mapping technology between user and a virtual avatar using embedded optical sensors and machine learning. The distance between each sensor and surface of the face is measured by the optical sensors that are attached inside the HMD. Our system learns the sensor values of each facial expression by neural network and creates a classifier to estimate the current facial expression.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985714"}, {"primary_key": "4196610", "vector": [], "sparse_vector": [], "title": "Gushed Diffusers: Fast-moving, Floating, and Lightweight Midair Display.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel method for fast-moving aerial imaging using aerosol-based fog screens. Conventional systems of aerial imaging cannot move fast because they need large and heavy setup. In this study, we propose to add new tradeoffs between limited display time and payloads. This system employ aerosol distribution from off-the-shelf spray as a fog screen that can resist the wind, and have high portability. As application examples, we present wearable application and aerial imaging on objects with high speed movements such as a drone, a radio-controlled model car, and performers. We believe that our study contribute to the exploration of new application areas for fog displays and expand expressions of entertainments and interactivity.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985706"}, {"primary_key": "4196611", "vector": [], "sparse_vector": [], "title": "Thickness Control Technique for Printing Tactile Sheets with Fused Deposition Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a printing technique that controls the thickness of objects by increasing and decreasing the amount of material extruded during printing. Using this technique, printers can dynamically control thickness and output thicker objects without a staircase effect. This technique allows users to print aesthetic pattern sheets and objects that are tactile without requiring any new hardware. This extends the capabilities of fused deposition modeling (FDM) 3D printers in a simple way. We describe a method of generating and calculating a movement path for printing tactile sheets, and demonstrate the usage and processing of example objects.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985701"}, {"primary_key": "4196612", "vector": [], "sparse_vector": [], "title": "Toward a Compact Device to Interact with a Capacitive Touch Screen.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Capacitive touch screens are widely used in various products. Touch screens have an advantage that an input system and output system can be integrated into a single module. We consider this advantage could make it possible to realize a new universal interface for both human-to-machine (H2M) and machine-to-machine (M2M). For a M2M interface, some sort of method to simulate finger touching is needed. Therefore, we propose an alternative method to interact with a touch screen using two electrical approaches. Our proposal is effective in automating touch screen operations, modality conversion device for people with disabilities, and so on. We assembled a prototype to confirm the principle to control a touch screen with the electrical methods. We believe that our proposal will complement the weakness of touch screens and expand their possibility.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984755"}, {"primary_key": "4196613", "vector": [], "sparse_vector": [], "title": "UnlimitedHand: Input and Output Hand Gestures with Less Calibration Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Numerous devices that either track hand gestures or provide haptic feedback have been developed with the aim of manipulating objects within Virtual Reality(VR) and Augmented Reality(AR) environments. However, these devices implement lengthy calibration processes to ease out individual differences. In this research, a wearable device that simultaneously recognizes hand gestures and outputs haptic feedback: UnlimitedHand is suggested. Photo-reflectors are placed over specific muscle groups on the forearm to read in hand gestures. For output, electrodes are placed over the same muscles to control the user's hand movements. Both sensors and electrodes target main muscle groups responsible for moving the hand. Since the positions of these muscle groups are common between humans, UnlimitedHand is able to reduce the time spent on performing calibration.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985743"}, {"primary_key": "4196614", "vector": [], "sparse_vector": [], "title": "A Rapid Prototyping Approach to Synthetic Data Generation for Improved 2D Gesture Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> Jr."], "summary": "Training gesture recognizers with synthetic data generated from real gestures is a well known and powerful technique that can significantly improve recognition accuracy. In this paper we introduce a novel technique called gesture path stochastic resampling (GPSR) that is computationally efficient, has minimal coding overhead, and yet despite its simplicity is able to achieve higher accuracy than competitive, state-of-the-art approaches. GPSR generates synthetic samples by lengthening and shortening gesture subpaths within a given sample to produce realistic variations of the input via a process of nonuniform resampling. As such, GPSR is an appropriate rapid prototyping technique where ease of use, understandability, and efficiency are key. Further, through an extensive evaluation, we show that accuracy significantly improves when gesture recognizers are trained with GPSR synthetic samples. In some cases, mean recognition errors are reduced by more than 70%, and in most cases, GPSR outperforms two other evaluated state-of-the-art methods.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984525"}, {"primary_key": "4196615", "vector": [], "sparse_vector": [], "title": "Reconstruction of Scene from Multiple Sketches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper discusses the feasibility of extension of expressive style with multiple 3D sketches drawn by a sketching tool that enables its users to draw and paint on 3D structured surfaces. Users of our proposed system take a picture of target objects and sketch with reference to the taken picture. They can not only sketch on the pictures but can also change their viewpoint of the sketched environment, since the system captures 3D structure by using a depth sensor as well as RGB data. Trial usage of the system shows that our users can rapidly extract their target objects/space and extend their ideas by taking pictures and drawing/painting on them. This paper presents examples of system usage, and discusses the feasibility of extension of sketches.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985736"}, {"primary_key": "4196616", "vector": [], "sparse_vector": [], "title": "QuickCut: An Interactive Tool for Editing Narrated Video.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present QuickCut, an interactive video editing tool designed to help authors efficiently edit narrated video. QuickCut takes an audio recording of the narration voiceover and a collection of raw video footage as input. Users then review the raw footage and provide spoken annotations describing the relevant actions and objects in the scene. QuickCut time-aligns a transcript of the annotations with the raw footage and a transcript of the narration to the voiceover. These aligned transcripts enable authors to quickly match story events in the narration with semantically relevant video segments and form alignment constraints between them. Given a set of such constraints, QuickCut applies dynamic programming optimization to choose frame-level cut points between the video segments while maintaining alignments with the narration and adhering to low-level film editing guidelines. We demonstrate QuickCut's effectiveness by using it to generate a variety of short (less than 2 minutes) narrated videos. Each result required between 14 and 52 minutes of user time to edit (i.e. between 8 and 31 minutes for each minute of output video), which is far less than typical authoring times with existing video editing workflows.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984569"}, {"primary_key": "4196617", "vector": [], "sparse_vector": [], "title": "Design and Evaluation of EdgeWrite Alphabets for Round Face Smartwatches.", "authors": ["Ke<PERSON><PERSON>", "<PERSON>aro <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study presents a project aimed at designing and evaluating a unistroke gesture set of alphanumeric characters targeting round-face smartwatches. We conducted a user study with 10 participants to generate the basic gesture design for 40 characters. For each character, we measured the preference and agreement scores and uncovered any challenges faced in designing unistroke gestures for round-face smartwatches. We developed a gesture recognizer using machine learning, which used a backpropagation mechanism to evaluate the designed gestures. Using the gesture recognizer, we collected 80,000 gesture data, and evaluated them with 5-fold cross-validation. The obtained mean recognition rate was 92.14%.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984757"}, {"primary_key": "4196618", "vector": [], "sparse_vector": [], "title": "A Tangible Interface to Realize Touch Operations on the Face of a Physical Object.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we describe a tangible interface that can realize touch operations on a physical object. We printed physical objects that have conductive striped patterns using a multi-material 3D printer. The ExtensionSticker technique allows the user to operate capacitive touch-panel devices by tapping, scrolling, and swiping the physical object. By shaping the structure of conductive wiring inside a physical object, a variety of interfaces can be realized. We examined the conditions for using our proposed method on touch-panel devices.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985711"}, {"primary_key": "4196619", "vector": [], "sparse_vector": [], "title": "The Elements of Fashion Style.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The outfits people wear contain latent fashion concepts capturing styles, seasons, events, and environments. Fashion theorists have proposed that these concepts are shaped by design elements such as color, material, and silhouette. A dress may be \"bohemian\" because of its pattern, material, trim, or some combination of them: it is not always clear how low-level elements translate to high-level styles. In this paper, we use polylingual topic modeling to learn latent fashion concepts jointly in two languages capturing these elements and styles. Using this latent topic formation we can translate between these two languages through topic space, exposing the elements of fashion style. We train the polylingual topic model (PLTM) on a set of more than half a million outfits collected from Polyvore, a popular fashion-based social net- work. We present novel, data-driven fashion applications that allow users to express their needs in natural language just as they would to a real stylist and produce tailored item recommendations for these style needs.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984573"}, {"primary_key": "4196620", "vector": [], "sparse_vector": [], "title": "Flying User Interface.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper describes a special type of drone called \"Flying User Interface\", comprised of a robotic projector-camera system, an onboard digital computer connected with the Internet, sensors, and a hardware interface capable of sticking to any surface such as wall, ceilings, etc. Computer further consists of other subsystems, devices, and sensors such as accelerometer, compass, gyroscope, flashlight, etc. Drone flies from one place to another, detects a surface, and attaches itself to it. After a successful attachment, the device stops all its rotators; it then projects or augments images, information, and user interfaces on nearby surfaces and walls. User interface may contain applications, information about object being augmented and information from Internet. User can interact with user-interface using commands and gestures such as hand, body, feet, voice, etc.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984770"}, {"primary_key": "4196621", "vector": [], "sparse_vector": [], "title": "Mavo: Creating Interactive Data-Driven Web Applications by Authoring HTML.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many people can author static web pages with HTML and CSS but find it hard or impossible to program persistent, interactive web applications. We show that for a broad class of CRUD (Create, Read, Update, Delete) applications, this gap can be bridged. <PERSON><PERSON> extends the declarative syntax of HTML to describe Web applications that manage, store and transform data. Using Mavo, authors with basic HTML knowledge define complex data schemas implicitly as they design their HTML layout. They need only add a few attributes and expressions to their HTML elements to transform their static design into a persistent, data-driven web application whose data can be edited by direct manipulation of the content in the browser. We evaluated Mavo with 20 users who marked up static designs---some provided by us, some their own creation---to transform them into fully functional web applications. Even users with no programming experience were able to quickly craft Mavo applications.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984551"}, {"primary_key": "4196622", "vector": [], "sparse_vector": [], "title": "Foundry: Hierarchical Material Design for Multi-Material Fabrication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We demonstrate a new approach for designing functional material definitions for multi-material fabrication using our system called Foundry. Foundry provides an interactive and visual process for hierarchically designing spatially-varying material properties (e.g., appearance, mechanical, optical). The resulting meta-materials exhibit structure at the micro and macro level and can surpass the qualities of traditional composites. The material definitions are created by composing a set of operators into an operator graph. Each operator performs a volume decomposition operation, remaps space, or constructs and assigns a material composition. The operators are implemented using a domain-specific language for multi-material fabrication; users can easily extend the library by writing their own operators. Foundry can be used to build operator graphs that describe complex, parameterized, resolution-independent, and reusable material definitions. We also describe how to stage the evaluation of the final material definition which in conjunction with progressive refinement, allows for interactive material evaluation even for complex designs. We show sophisticated and functional parts designed with our system.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984516"}, {"primary_key": "4196623", "vector": [], "sparse_vector": [], "title": "Interacting with Soli: Exploring Fine-Grained Dynamic Gesture Recognition in the Radio-Frequency Spectrum.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel machine learning architecture, specifically designed for radio-frequency based gesture recognition. We focus on high-frequency (60]GHz), short-range radar based sensing, in particular Google's Soli sensor. The signal has unique properties such as resolving motion at a very fine level and allowing for segmentation in range and velocity spaces rather than image space. This enables recognition of new types of inputs but poses significant difficulties for the design of input recognition algorithms. The proposed algorithm is capable of detecting a rich set of dynamic gestures and can resolve small motions of fingers in fine detail. Our technique is based on an end-to-end trained combination of deep convolutional and recurrent neural networks. The algorithm achieves high recognition rates (avg 87%) on a challenging set of 11 dynamic gestures and generalizes well across 10 users. The proposed model runs on commodity hardware at 140 Hz (CPU only).", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984565"}, {"primary_key": "4196624", "vector": [], "sparse_vector": [], "title": "CircuitStack: Supporting Rapid Prototyping and Evolution of Electronic Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For makers and developers, circuit prototyping is an integral part of building electronic projects. Currently, it is common to build circuits based on breadboard schematics that are available on various maker and DIY websites. Some breadboard schematics are used as is without modification, and some are modified and extended to fit specific needs. In such cases, diagrams and schematics merely serve as blueprints and visual instructions, but users still must physically wire the breadboard connections, which can be time-consuming and error-prone. We present CircuitStack, a system that combines the flexibility of breadboarding with the correctness of printed circuits, for enabling rapid and extensible circuit construction. This hybrid system enables circuit reconfigurability, component reusability, and high efficiency at the early stage of prototyping development.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984527"}, {"primary_key": "4196625", "vector": [], "sparse_vector": [], "title": "Wearables as Context for Guiard-abiding Bimanual Touch.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore the contextual details afforded by wearable devices to support multi-user, direct-touch interaction on electronic whiteboards in a way that-unlike previous work-can be fully consistent with natural bimanual-asymmetric interaction as set forth by <PERSON><PERSON><PERSON>.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984564"}, {"primary_key": "4196626", "vector": [], "sparse_vector": [], "title": "RunPlay: Action Recognition Using Wearable Device Apply on Parkour Game.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present an action recognition system which consists of pressure insoles, with 16 pressure sensors, and an inertial measurement unit. By analysing the data measured from these sensors, we are able to recognised several human activities. In this circumstance, we focus on the detection of jumping, squatting, moving left and right. We also designed a parkour game on a mobile device to demonstrate the in-game control of an avatar by human action.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985731"}, {"primary_key": "4196627", "vector": [], "sparse_vector": [], "title": "Stretchis: Fabricating Highly Stretchable User Interfaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in materials science research allow production of highly stretchable sensors and displays. Such technologies, however, are still not accessible to non-expert makers. We present a novel and inexpensive fabrication method for creating Stretchis, highly stretchable user interfaces that combine sensing capabilities and visual output. We use Polydimethylsiloxan (PDMS) as the base material for a Stretchi and show how to embed stretchable touch and proximity sensors and stretchable electroluminescent displays. Stretchis can be ultra-thin (≈ 200μm), flexible, and fully customizable, enabling non-expert makers to add interaction to elastic physical objects, shape-changing surfaces, fabrics, and the human body. We demonstrate the usefulness of our approach with three application examples that range from ubiquitous computing to wearables and on-skin interaction.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984521"}, {"primary_key": "4196628", "vector": [], "sparse_vector": [], "title": "waveSense: Ultra Low Power Gesture Sensing Based on Selective Volumetric Illumination.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sur<PERSON>"], "summary": "We present waveSense, a low power hand gestures recogni- tion system suitable for mobile and wearable devices. A novel Selective Volumetric Illumination (SVI) approach using off-the-shelf infrared (IR) emitters and non-focused IR sensors were introduced to achieve the power efficiency. Our current implementation consumes 8.65mW while sensing hand gestures within 60cm radius from the sensors. In this demo, we introduce the concept and the theoretical background of waveSense, details of the prototype implementation, and application possibilities.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985734"}, {"primary_key": "4196629", "vector": [], "sparse_vector": [], "title": "Object-Oriented Interaction: Enabling Direct Physical Manipulation of Abstract Content via Objectification.", "authors": ["<PERSON><PERSON>"], "summary": "Touch input promises intuitive interactions with digital content as it employs our experience of manipulating physical objects: digital content can be rotated, scaled, and translated using direct manipulation gestures. However, the reliance on analog also confines the scope of direct physical manipulation: the physical world provides no mechanism to interact with digital abstract content. As such, applications on touchscreen devices either only include limited functionalities or fallback on the traditional form-filling paradigm, which is tedious, slow, and error prone for touch input. My research focuses on designing a new UI framework to enable complex functionalities on touch screen devices by expanding direct physical manipulation to abstract content via objectification. I present two research projects, objectification of attributes and selection, which demonstrate considerable promises.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984784"}, {"primary_key": "4196630", "vector": [], "sparse_vector": [], "title": "Energy-Brushes: Interactive Tools for Illustrating Stylized Elemental Dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic effects such as waves, splashes, fire, smoke, and explosions are an integral part of stylized animations. However, such dynamics are challenging to produce, as manually sketching key-frames requires significant effort and artistic expertise while physical simulation tools lack sufficient expressiveness and user control. We present an interactive interface for designing these elemental dynamics for animated illustrations. Users draw with coarse-scale energy brushes which serve as control gestures to drive detailed flow particles which represent local velocity fields. These fields can convey both realistic and artistic effects based on user specification. This painting metaphor for creating elemental dynamics simplifies the process, providing artistic control, and preserves the fluidity of sketching. Our system is fast, stable, and intuitive. An initial user evaluation shows that even novice users with no prior animation experience can create intriguing dynamics using our system.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984585"}, {"primary_key": "4196631", "vector": [], "sparse_vector": [], "title": "Expanding the Field-of-View of Head-Mounted Displays with Peripheral Blurred Images.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Head-mounted displays are rapidly becoming popular. Field-of-view is one of the key parameters of head-mounted displays, because a wider field-of-view gives higher presence and immersion in the virtual environment. However, wider field-of-view often increase device cost and weight because it needs complicated optics or expensive modules such as multi high-resolution displays or complex lenses. This paper proposes a method that expands the field-of-view by using two kinds of lenses with different levels of magnification. The principle of the proposed method is that Fresnel lenses with high magnification surround convex lenses to fill the peripheral vision with a blurred image. The proposed method doesn't need complicated optics, and is advantageous in terms of device cost and weight, because only two additional Fresnel lenses are necessary. We implement a prototype and confirm that the Fresnel lenses fill the peripheral with a blurred image, and effectively expand the field-of-view.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985735"}, {"primary_key": "4196632", "vector": [], "sparse_vector": [], "title": "AquaCAVE: Augmented Swimming Environment with Immersive Surround-Screen Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AquaCAVE is a system for enhancing the swimming experience. Although swimming is considered to be one of the best exercises to maintain our health, swimming in a pool is normally monotonous; thus, maintaining its motivation is sometimes difficult. AquaCAVE is a computer-augmented swimming pool with rear-projection acrylic walls that surround a swimmer, providing a CAVE-like immersive stereoscopic projection environment. The swimmer wears goggles with liquid-crystal display (LCD) shutter glasses, and cameras installed in the pool tracks swimmer's head position. Swimmers can be immersed into synthetic scenes such as coral reefs, outer space, or any other computer generated environments. The system can also provide swimming training with projections such as record lines and swimming forms as 3D virtual characters in the 3D space.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984760"}, {"primary_key": "4196633", "vector": [], "sparse_vector": [], "title": "RadarCat: Radar Categorization for Input &amp; Interaction.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In RadarCat we present a small, versatile radar-based system for material and object classification which enables new forms of everyday proximate interaction with digital devices. We demonstrate that we can train and classify different types of materials and objects which we can then recognize in real time. Based on established research designs, we report on the results of three studies, first with 26 materials (including complex composite objects), next with 16 transparent materials (with different thickness and varying dyes) and finally 10 body parts from 6 participants. Both leave one-out and 10-fold cross-validation demonstrate that our approach of classification of radar signals using random forest classifier is robust and accurate. We further demonstrate four working examples including a physical object dictionary, painting and photo editing application, body shortcuts and automatic refill based on RadarCat. We conclude with a discussion of our results, limitations and outline future directions.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984515"}, {"primary_key": "4196634", "vector": [], "sparse_vector": [], "title": "Sidetap &amp; Slingshot Gestures on Unmodified Smartwatches.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a technique for detecting gestures on the edge of an unmodified smartwatch. We demonstrate two exemplary gestures, i) Sidetap -- tapping on any side and ii) Slingshot -- pressing on the edge and then releasing quickly. Our technique is lightweight, as it relies on measuring the data from the internal Inertial measurement unit (IMU) only. With these two gestures, we expand the input expressiveness of a smartwatch, allowing users to use intuitive gestures with natural tactile feedback, e.g., for the rapid navigation of a long list of items with a tap, or act as shortcut commands to launch applications. It can also allow for eyes-free interaction or subtle interaction where visual attention is not available.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984763"}, {"primary_key": "4196635", "vector": [], "sparse_vector": [], "title": "Switch++: An Output Device of the Switches by the Finger Gestures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Regarding human-machine-interfaces, switches have not changed significantly despite the machines themselves evolving constantly. In this paper, we propose a new method of operability for devices by providing multiple switches dynamically, and users choose the switch that has the functionality that they want to use. Switch++ senses the mental model of the operating sensation of switches against the user's finger gestures and changes the shape of the switch and its affordances accordingly. We design the interface based on the raw data.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984753"}, {"primary_key": "4196636", "vector": [], "sparse_vector": [], "title": "Hand Gesture and On-body Touch Recognition by Active Acoustic Sensing throughout the Human Body.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a novel acoustic sensing technique that recognizes two convenient input actions: hand gestures and on-body touch. We achieved them by observing the frequency spectrum of the wave propagated in the body, around the periphery of the wrist. Our approach can recognize hand gestures and on-body touch concurrently in real-time and is expected to obtain rich input variations by combining them. We conducted a user study that showed classification accuracy of 97%, 96%, and 97% for hand gestures, touches on the forearm, and touches on the back of the hand.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985721"}, {"primary_key": "4196637", "vector": [], "sparse_vector": [], "title": "Promoting Natural Interactions Through Embedded Input Using Novel Sensing Techniques.", "authors": ["<PERSON>"], "summary": "From mobile devices to interactive objects, various input methods are provided using built-in motion and capacitive touch sensors. These inputs are offered in effective and efficient manner where users can operate interface quickly and easily. However, they do not fully explore the input space supported by human's natural motion behavior. As a solution, my work focuses on promoting natural interaction through hand-driven embedded input powered by multimodal and magnetic sensing techniques. In my previous works, embedded inputs were implemented in the form of smart textile, stylus, and ring supporting from mobile devices to everyday objects. Throughout the paper, I will briefly go over implemented systems along with evaluated results and potential applications. Future research direction is highlighted at the end of the paper.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984782"}, {"primary_key": "4196638", "vector": [], "sparse_vector": [], "title": "TRing: Instant and Customizable Interactions with Objects Using an Embedded Magnet and a Finger-Worn Device.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present TRing, a finger-worn input device which provides instant and customizable interactions. TRing offers a novel method for making plain objects interactive using an embedded magnet and a finger-worn device. With a particle filter integrated magnetic sensing technique, we compute the fingertip's position relative to the embedded magnet. We also offer a magnet placement algorithm that guides the magnet installation location based upon the user's interface customization. By simply inserting or attaching a small magnet, we bring interactivity to both fabricated and existing objects. In our evaluations, TRing shows an average tracking error of 8.6 mm in 3D space and a 2D targeting error of 4.96 mm, which are sufficient for implementing average-sized conventional controls such as buttons and sliders. A user study validates the input performance with TRing on a targeting task (92% accuracy within 45 mm distance) and a cursor control task (91% accuracy for a 10 mm target). Furthermore, we show examples that highlight the interaction capability of our approach.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984529"}, {"primary_key": "4196639", "vector": [], "sparse_vector": [], "title": "Phyxel: Realistic Display of Shape and Appearance using Physical Objects with High-speed Pixelated Lighting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A computer display that is sufficiently realistic such that the difference between a presented image and a real object cannot be discerned is in high demand in a wide range of fields, such as entertainment, digital signage, and design industry. To achieve such a level of reality, it is essential to reproduce the three-dimensional (3D) shape and material appearances simultaneously; however, to date, developing a display that can satisfy both conditions has been difficult. To address this problem, we propose a system that places physical elements at desired locations to create a visual image that is perceivable by the naked eye. This configuration can be realized by exploiting characteristics of human visual perception. Humans perceive light modulation as perfectly steady light if the modulation rate is sufficiently high. Therefore, if high-speed spatially varying illumination is projected to the actuated physical elements possessing various appearances at the desired timing, a realistic visual image that can be transformed dynamically by simply modifying the lighting pattern can be obtained. We call the proposed display technology Phyxel. This paper describes the proposed configuration and required performance for Phyxel. We also demonstrate three applications: dynamic stop motion, a layered 3D display, and shape mixture.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984548"}, {"primary_key": "4196640", "vector": [], "sparse_vector": [], "title": "Orchestrated Informal Care Coordination: Designing a Connected Network of Tools in Support of Collective Care Activities for Informal Caregivers.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Often, family caregivers experience difficulties in coordinating older adults' health care because it requires not only a lot of time but also a diverse set of responsibilities to coordinate care for their loved ones. While many can reduce their individual burden by sharing care tasks with other family members, there are still many challenges to overcome in maintaining the quality of care when they work together. As they increase their informal care network, it becomes more difficult for them to stay informed and coordinated. Coordination breakdowns caused by having multiple caregivers who are cooperating to care for the same care recipient result in reduced quality of care. I explored opportunities for \"Internet of Things (IoT)\" technologies to help informal caregivers better coordinate and communicate care with each other for their loved ones. Based on identified design opportunities, I propose the concept of CareBot, a smart home platform consisting of interactive tools in support of collective care activities of family caregivers. \\", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2984752"}, {"primary_key": "4196641", "vector": [], "sparse_vector": [], "title": "floatio: Floating Tangible User Interface Based on Animacy Perception.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this study, we propose floatio: a floating tangible user interface that makes it easy to create a perception of animacy (lifelike movement). It has been pointed out that there are three requirements that make animacy more likely to be perceived: interactivity, irregularities, and automatic movement resisting the force of gravity. Based on these requirements, floatio provides a tangible user interface where a polystyrene ball resembling a pixel is suspended in a stream of air where it can be positioned passively by the user, or autonomously by the system itself. To implement floatio, we developed three mechanisms: a floating field mechanism, a pointer input/output mechanism and a hand-over mechanism. We also measured the precision of the pointer input/output and hand-over mechanisms.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985699"}, {"primary_key": "4196642", "vector": [], "sparse_vector": [], "title": "WithYou: An Interactive Shadowing Coach with Speech Recognition.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Speech shadowing, in which the subject listens to native narration sound and tries to repeat it immediately while listening, is a proven way of practicing speaking skills when learning foreign languages. However, since the narration is independent of user's speech, the playback cannot make an adjustment when the learner fails to catch up, and this makes shadowing difficult. We propose WithYou, a system based on Automated Speech Recognition (ASR) that is able to adjust narration playback during a live shadowing speech. <PERSON><PERSON><PERSON> compares the student's live speech with the narration playback to detect shadowing mistakes. In addition, <PERSON><PERSON><PERSON> is able to handle pauses and recognize repetitive phrases in shadowing practice. A user study shows that practicing shadowing with <PERSON><PERSON><PERSON> is easier and more effective compared with conventional methods.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751.2985704"}, {"primary_key": "4196643", "vector": [], "sparse_vector": [], "title": "Advancing Hand Gesture Recognition with High Resolution Electrical Impedance Tomography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Electrical Impedance Tomography (EIT) was recently employed in the HCI domain to detect hand gestures using an instrumented smartwatch. This prior work demonstrated great promise for non-invasive, high accuracy recognition of gestures for interactive control. We introduce a new system that offers improved sampling speed and resolution. In turn, this enables superior interior reconstruction and gesture recognition. More importantly, we use our new system as a vehicle for experimentation ' we compare two EIT sensing methods and three different electrode resolutions. Results from in-depth empirical evaluations and a user study shed light on the future feasibility of EIT for sensing human input.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984574"}, {"primary_key": "4196644", "vector": [], "sparse_vector": [], "title": "AuraSense: Enabling Expressive Around-Smartwatch Interactions with Electric Field Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Existing smartwatches rely on touchscreens for display and input, which inevitably leads to finger occlusion and confines interactivity to a small area. In this work, we introduce AuraSense, which enables rich, around-device, smartwatch interactions using electric field sensing as an adapted device. To explore how this sensing approach could enhance smartwatch interactions, we considered different antenna configurations and how they could enable useful interaction modalities. We identified four configurations that can support six well-known modalities of particular interest and utility, including gestures above or in close proximity to watches, and touchscreen-like finger tracking on the skin. We quantify the feasibility of these input modalities, suggesting that AuraSense can be low latency and robust across users and environments.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511.2984568"}, {"primary_key": "4211423", "vector": [], "sparse_vector": [], "title": "Proceedings of the 29th Annual Symposium on User Interface Software and Technology, UIST 2016, Tokyo, Japan, October 16-19, 2016", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We are very excited to welcome you to the 29th Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 16-19th 2016, in Tokyo, Japan. UIST is the premier forum for the presentation of research innovations in the software and technology of human-computer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing and CSCW. UIST 2016 received 384 technical paper submissions. After a thorough review process, the 42- member program committee accepted 79 papers (20.6%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, a second member of the program committee was asked to review the paper and we then asked the authors to submit a short rebuttal addressing the reviewers' concerns. The program committee met in person in Seattle, Washington, USA on June 23rd and 24th, 2016, to select the papers to invite for the program. Submissions were accepted only after the authors provided a final revision addressing the committee's comments. In addition to papers, our program includes 29 posters, 52 demonstrations, and 8 student presentations in the twelfth annual Doctoral Symposium. Our program also features the eighth annual Student Innovation Contest -- teams from all over the world will compete in this year's contest, which focuses on the development of new haptic sensations using Electrical Muscle Stimulation. UIST 2016 will feature two keynote presentations. The opening keynote will be given by Takeo Kanade (Carnegie Mellon University) on new computer-vision technology combining a projector and camera. Naoto Fukasawa (Naoto Fukasawa Design) will deliver the closing keynote on product design. We welcome you to Tokyo, a city of rich tradition and vibrant modern culture. We hope that you will find the technical program interesting and thought-provoking. We also hope that UIST 2016 will provide you with enjoyable opportunities to engage with fellow researchers from both industry and academia, from institutions around the world.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984511"}, {"primary_key": "4211424", "vector": [], "sparse_vector": [], "title": "Proceedings of the 29th Annual Symposium on User Interface Software and Technology, UIST 2016 Adjunct Volume, Tokyo, Japan, October 16 - 19, 2016", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We are very excited to welcome you to the 29th Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 16-19th 2016, in Tokyo, Japan. UIST is the premier forum for the presentation of research innovations in the software and technology of human-computer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing and CSCW. UIST 2016 received 384 technical paper submissions. After a thorough review process, the 42- member program committee accepted 79 papers (20.6%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, a second member of the program committee was asked to review the paper and we then asked the authors to submit a short rebuttal addressing the reviewers' concerns. The program committee met in person in Seattle, Washington, USA on June 23rd and 24th, 2016, to select the papers to invite for the program. Submissions were accepted only after the authors provided a final revision addressing the committee's comments. In addition to papers, our program includes 29 posters, 52 demonstrations, and 8 student presentations in the twelfth annual Doctoral Symposium. Our program also features the eighth annual Student Innovation Contest -- teams from all over the world will compete in this year's contest, which focuses on the development of new haptic sensations using Electrical Muscle Stimulation. UIST 2016 will feature two keynote presentations. The opening keynote will be given by Takeo Kanade (Carnegie Mellon University) on new computer-vision technology combining a projector and camera. Naoto Fukasawa (Naoto Fukasawa Design) will deliver the closing keynote on product design. We welcome you to Tokyo, a city of rich tradition and vibrant modern culture. We hope that you will find the technical program interesting and thought-provoking. We also hope that UIST 2016 will provide you with enjoyable opportunities to engage with fellow researchers from both industry and academia, from institutions around the world.", "published": "2016-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/2984751"}]