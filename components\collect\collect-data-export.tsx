'use client';

import { Paper } from '@/components/survey/class-utils';

// 新增：导出获取所有收藏夹和论文的方法，便于 MiniChat 使用
export const getAllFoldersAndPapers = () => {
    if (typeof window === 'undefined') return { folders: [], papers: [] };
    try {
      const storedData = localStorage.getItem('arxivInsightCollection');
      if (storedData) {
        const { folders, papers } = JSON.parse(storedData);
        // 为每个 paper 增加在其文件夹下的 index 字段（从 1 开始）
        const papersWithIndex = (papers || []).map((paper: any) => {
          // 找到同一文件夹下的所有论文
          const folderPapers = (papers || []).filter((p: any) => p.folderId === paper.folderId);
          // index: 在该文件夹下的序号（从 1 开始）
          let idx = folderPapers.findIndex((p: any) => {
            if (p.id && paper.id) return p.id === paper.id;
            return p.title === paper.title;
          });
          return { ...paper, folderIndex: idx + 1 };
        });
        return { folders: folders || [], papers: papersWithIndex };
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load collection data:', error);
    }
    return { folders: [], papers: [] };
  };

// 根据 @标签 获取文献列表
export const genAtSuggestions = (query: string, papers: Paper[]) => {
  // 每次都主动获取最新收藏夹和论文（不 setState）
  const { folders, papers: allPapers } = getAllFoldersAndPapers();
  const suggestions: any[] = [];
  const seenPapers = new Set<string>();
  // 1. 主页面全部论文
  if ('全部'.includes(query) || 'all'.includes(query.toLowerCase()) || query === '') {
    suggestions.push({ type: 'all', label: '📚主页面全部论文', value: 'all' });
  }
  // 2. 主页面单篇论文
  papers.forEach((p, idx) => {
    const idxStr = (idx + 1).toString();
    const title = p.title || '';
    const titleZh = p.title_translation || '';
    const allNames = [title, titleZh].map(s => (s || '').toLowerCase());
    const uniqueKey = p.title || '';
    if (
      (idxStr.startsWith(query) ||
       allNames.some(n => n.includes(query.toLowerCase()))) &&
      !seenPapers.has(uniqueKey)
    ) {
      suggestions.push({
        type: 'main-paper',
        label: `[${idx + 1}] ${titleZh ? titleZh + ' / ' : ''}${title}`,
        value: uniqueKey,
        paper: p,
        idx: idx + 1
      });
      seenPapers.add(uniqueKey);
    }
  });
  // 3. 收藏夹
  folders.forEach((f: any) => {
    if (f.name && f.name.toLowerCase().includes(query.toLowerCase())) {
      suggestions.push({ type: 'folder', label: `📁收藏夹：${f.name}`, value: f.id, folder: f });
    }
  });
  // 4. 收藏夹内论文
  // 先筛选所有匹配的收藏夹论文，按文件夹分组
  const folderPaperMap: Record<string, any[]> = {};
  allPapers.forEach((p: any) => {
    const folderId = p.folderId || '__uncategorized__';
    const title = p.title || '';
    const titleZh = p.title_translation || '';
    const customName = p.customName || '';
    const allNames = [title, titleZh, customName].map(s => (s || '').toLowerCase());
    const uniqueKey = p.title || '';
    if (
      allNames.some(n => n.includes(query.toLowerCase())) &&
      !seenPapers.has(uniqueKey)
    ) {
      if (!folderPaperMap[folderId]) folderPaperMap[folderId] = [];
      folderPaperMap[folderId].push(p);
      seenPapers.add(uniqueKey);
    }
  });
  // 按文件夹顺序聚集，每组内按 folderIndex 排序
  Object.keys(folderPaperMap).forEach(folderId => {
    const folder = folders.find((f: any) => f.id === (folderId === '__uncategorized__' ? undefined : folderId));
    const papersInFolder = folderPaperMap[folderId].sort((a, b) => (a.folderIndex || 0) - (b.folderIndex || 0));
    papersInFolder.forEach((p: any) => {
      const title = p.title || '';
      const titleZh = p.title_translation || '';
      const customName = p.customName || '';
      suggestions.push({
        type: 'collected-paper',
        label: `${folder ? `【${folder.name}】` : '【未分类】'}${p.folderIndex ? ` [${p.folderIndex}] ` : ''}${customName || titleZh || title}`,
        value: p.title || '',
        paper: p,
        folder: folder,
      });
    });
  });
  return suggestions;
};