[{"primary_key": "4503411", "vector": [], "sparse_vector": [], "title": "Approximating the Nash Social Welfare with Indivisible Items.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of allocating a set of indivisible items among agents with additive valuations, with the goal of maximizing the geometric mean of the agents' valuations, i.e., the Nash social welfare. This problem is known to be NP-hard, and our main result is the first efficient constant-factor approximation algorithm for this objective. We first observe that the integrality gap of the natural fractional relaxation is exponential, so we propose a different fractional allocation which implies a tighter upper bound and, after appropriate rounding, yields a good integral allocation. An interesting contribution of this work is the fractional allocation that we use. The relaxation of our problem can be solved efficiently using the Eisenberg-Gale program, whose optimal solution can be interpreted as a market equilibrium with the dual variables playing the role of item prices. Using this market-based interpretation, we define an alternative equilibrium allocation where the amount of spending that can go into any given item is bounded, thus keeping the highly priced items under-allocated, and forcing the agents to spend on lower priced items. The resulting equilibrium prices reveal more information regarding how to assign items so as to obtain a good integral allocation.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746589"}, {"primary_key": "4503412", "vector": [], "sparse_vector": [], "title": "The List Decoding <PERSON><PERSON> of Reed-Muller Codes over Small Fields.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The list decoding problem for a code asks for the maximal radius up to which any ball of that radius contains only a constant number of codewords. The list decoding radius is not well understood even for well studied codes, like Reed-Solomon or Reed-Muller codes. Fix a finite field F. The Reed-Muller code RMF(n,d) is defined by n-variate degree-d polynomials over F. In this work, we study the list decoding radius of Reed-Muller codes over a constant prime field F=Fp, constant degree d and large n. We show that the list decoding radius is equal to the minimal distance of the code.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746543"}, {"primary_key": "4503413", "vector": [], "sparse_vector": [], "title": "Forrelation: A Problem that Optimally Separates Quantum from Classical Computing.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We achieve essentially the largest possible separation between quantum and classical query complexities. We do so using a property-testing problem called Forrelation, where one needs to decide whether one Boolean function is highly correlated with the Fourier transform of a second function. This problem can be solved using 1 quantum query, yet we show that any randomized algorithm needs Ω(√(N)log(N)) queries (improving an Ω(N1/4) lower bound of <PERSON><PERSON>). Conversely, we show that this 1 versus Ω(√(N)) separation is optimal: indeed, any t-query quantum algorithm whatsoever can be simulated by an O(N1-1/2t)-query randomized algorithm. Thus, resolving an open question of <PERSON><PERSON><PERSON> et al. from 2002, there is no partial Boolean function whose quantum query complexity is constant and whose randomized query complexity is linear. We conjecture that a natural generalization of Forrelation achieves the optimal t versus Ω(N1-1/2t) separation for all t. As a bonus, we show that this generalization is BQP-complete. This yields what's arguably the simplest BQP-complete problem yet known, and gives a second sense in which Forrel<PERSON> \"captures the maximum power of quantum computation.\"", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746547"}, {"primary_key": "4503414", "vector": [], "sparse_vector": [], "title": "Reed-Muller Codes for Random Erasures and Errors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies the parameters for which binary Reed-Muller (RM) codes can be decoded successfully on the BEC and BSC, and in particular when can they achieve capacity for these two classical channels. Necessarily, the paper also studies properties of evaluations of multi-variate GF(2) polynomials on random sets of inputs. For erasures, we prove that RM codes achieve capacity both for very high rate and very low rate regimes. For errors, we prove that RM codes achieve capacity for very low rate regimes, and for very high rates, we show that they can uniquely decode at about square root of the number of errors at capacity.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746575"}, {"primary_key": "4503415", "vector": [], "sparse_vector": [], "title": "Matching Triangles and Basing Hardness on an Extremely Popular Conjecture.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "Huacheng Yu"], "summary": "Due to the lack of unconditional polynomial lower bounds, it is now in fashion to prove conditional lower bounds in order to advance our understanding of the class P. The vast majority of these lower bounds are based on one of three famous hypotheses: the 3-SUM conjecture, the APSP conjecture, and the Strong Exponential Time Hypothesis. Only circumstantial evidence is known in support of these hypotheses, and no formal relationship between them is known. In hopes of obtaining \"less conditional\" and therefore more reliable lower bounds, we consider the conjecture that at least one of the above three hypotheses is true. We design novel reductions from 3-SUM, APSP, and CNF-SAT, and derive interesting consequences of this very plausible conjecture, including: Tight n3-o(1) lower bounds for purely-combinatorial problems about the triangles in unweighted graphs. New n1-o(1) lower bounds for the amortized update and query times of dynamic algorithms for single-source reachability, strongly connected components, and Max-Flow. New n1.5-o(1) lower bound for computing a set of n st-maximum-flow values in a directed graph with n nodes and ~O(n) edges. There is a hierarchy of natural graph problems on n nodes with complexity nc for c ∈ (2,3).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746594"}, {"primary_key": "4503416", "vector": [], "sparse_vector": [], "title": "A Directed Isoperimetric Inequality with application to Bregman Near Neighbor Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bregman divergences are important distance measures that are used in applications such as computer vision, text mining, and speech processing, and are a focus of interest in machine learning due to their information-theoretic properties. There has been extensive study of algorithms for clustering and near neighbor search with respect to these divergences. In all cases, the guarantees depend not just on the data size n and dimensionality d, but also on a structure constant μ ≥ 1 that depends solely on a generating convex function φ and can grow without bound independently. In general, this μ parametrizes the degree to which a given divergence is \"asymmetric\". In this paper, we provide the first evidence that this dependence on μ might be intrinsic. We focus on the problem of ac{ann} search for Bregman divergences. We show that under the cell probe model, any non-adaptive data structure (like locality-sensitive hashing) for c-approximate near-neighbor search that admits r probes must use space Ω(dn1 + μ/c r). In contrast for LSH under l1 the best bound is Ω(dn1+ 1/cr).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746595"}, {"primary_key": "4503417", "vector": [], "sparse_vector": [], "title": "Byzantine Agreement with Optimal Early Stopping, Optimal Resilience and Polynomial Complexity.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON>"], "summary": "We provide the first protocol that solves Byzantine agreement with optimal early stopping (min{f+2,t+1} rounds) and optimal resilience (n>3t) using polynomial message size and computation.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746581"}, {"primary_key": "4503418", "vector": [], "sparse_vector": [], "title": "Non-malleable Reductions and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-malleable codes, introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [DPW10], provide a useful message integrity guarantee in situations where traditional error-correction (and even error-detection) is impossible; for example, when the attacker can completely overwrite the encoded message. Informally, a code is non-malleable if the message contained in a modified codeword is either the original message, or a completely \"unrelated value\". Although such codes do not exist if the family of \"tampering functions\" cF allowed to modify the original codeword is completely unrestricted, they are known to exist for many broad tampering families cF. The family which received the most attention [DPW10,LL12,DKO13,ADL14,CG14a,CG14b] is the family of tampering functions in the so called (2-part) split-state model: here the message x is encoded into two shares L and R, and the attacker is allowed to arbitrarily tamper with each L and R individually. Despite this attention, the following problem remained open: Build efficient, information-theoretically secure non-malleable codes in the split-state model with constant encoding rate: |L|=|R|=O(|x|).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746544"}, {"primary_key": "4503419", "vector": [], "sparse_vector": [], "title": "Solving the Shortest Vector Problem in 2n Time Using Discrete Gaussian Sampling: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We give a randomized 2n+o(n)-time and space algorithm for solving the Shortest Vector Problem (SVP) on n-dimensional Euclidean lattices. This improves on the previous fastest algorithm: the deterministic ~O(4n)-time and ~O(2n)-space algorithm of <PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC 2010, SIAM J. Comp. 2013). In fact, we give a conceptually simple algorithm that solves the (in our opinion, even more interesting) problem of discrete Gaussian sampling (DGS). More specifically, we show how to sample 2n/2 vectors from the discrete Gaussian distribution at any parameter in 2n+o(n) time and space. (Prior work only solved DGS for very large parameters.) Our SVP result then follows from a natural reduction from SVP to DGS.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746606"}, {"primary_key": "4503420", "vector": [], "sparse_vector": [], "title": "Adjacency Labeling Schemes and Induced-Universal Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that there exists a graph $G$ with $\\Oh(n)$ nodes, where any forest of $n$ nodes is a node-induced subgraph of $G$. Furthermore, for constant arboricity $k$, the result implies the existence of a graph with $\\Oh(n^k)$ nodes that contains all $n$-node graphs as node-induced subgraphs, matching a $\\Omega(n^k)$ lower bound. The lower bound and previously best upper bounds were presented in Alstrup and Ra<PERSON><PERSON> (FOCS'02). Our upper bounds are obtained through a $\\log_2 n +\\Oh(1)$ labeling scheme for adjacency queries in forests. We hereby solve an open problem being raised repeatedly over decades, e.g. in <PERSON>nna<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (STOC 1988), <PERSON> (J. of Graph Theory 1990), <PERSON><PERSON><PERSON> and <PERSON> (SODA 2010).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746545"}, {"primary_key": "4503421", "vector": [], "sparse_vector": [], "title": "High Parallel Complexity Graphs and Memory-Hard Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We develop new theoretical tools for proving lower-bounds on the (amortized) complexity of certain functions in models of parallel computation. We apply the tools to construct a class of functions with high amortized memory complexity in the *parallel* Random Oracle Model (pROM); a variant of the standard ROM allowing for batches of *simultaneous* queries. In particular we obtain a new, more robust, type of Memory-Hard Functions (MHF); a security primitive which has recently been gaining acceptance in practice as an effective means of countering brute-force attacks on security relevant functions. Along the way we also demonstrate an important shortcoming of previous definitions of MHFs and give a new definition addressing the problem. The tools we develop represent an adaptation of the powerful pebbling paradigm (initially introduced by <PERSON> and <PERSON> [HP70] and <PERSON> [Coo73]) to a simple and intuitive parallel setting. We define a simple pebbling game Gp over graphs which aims to abstract parallel computation in an intuitive way. As a conceptual contribution we define a measure of pebbling complexity for graphs called *cumulative complexity* (CC) and show how it overcomes a crucial shortcoming (in the parallel setting) exhibited by more traditional complexity measures used in the past. As a main technical contribution we give an explicit construction of a constant in-degree family of graphs whose CC in Gp approaches maximality to within a polylogarithmic factor for any graph of equal size (analogous to the graphs of <PERSON><PERSON><PERSON> et. al. [PTC76, LT82] for sequential pebbling games). Finally, for a given graph G and related function fG, we derive a lower-bound on the amortized memory complexity of fG in the pROM in terms of the CC of G in the game Gp.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746622"}, {"primary_key": "4503422", "vector": [], "sparse_vector": [], "title": "Fast Matrix Multiplication: Limitations of the Coppersmith-Winograd Method.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Until a few years ago, the fastest known matrix multiplication algorithm, due to <PERSON><PERSON> and <PERSON> (1990), ran in time O(n2.3755). Recently, a surge of activity by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> has led to an improved algorithm running in time O(n2.3729). These algorithms are obtained by analyzing higher and higher tensor powers of a certain identity of <PERSON><PERSON> and <PERSON>. We show that this exact approach cannot result in an algorithm with running time O(n2.3725), and identify a wide class of variants of this approach which cannot result in an algorithm with running time $O(n^{2.3078}); in particular, this approach cannot prove the conjecture that for every ε > 0, two n x n matrices can be multiplied in time O(n2+ε).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746554"}, {"primary_key": "4503423", "vector": [], "sparse_vector": [], "title": "Sketching and Embedding are Equivalent for Norms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Ilya <PERSON>"], "summary": "An outstanding open question (http://sublinear.info, Question #5) asks to characterize metric spaces in which distances can be estimated using efficient sketches. Specifically, we say that a sketching algorithm is efficient if it achieves constant approximation using constant sketch size. A well-known result of <PERSON><PERSON> (<PERSON><PERSON>, 2006) implies that a metric that admits a constant-distortion embedding into lp for p∈(0,2] also admits an efficient sketching scheme. But is the converse true, i.e., is embedding into lp the only way to achieve efficient sketching? We address these questions for the important special case of normed spaces, by providing an almost complete characterization of sketching in terms of embeddings. In particular, we prove that a finite-dimensional normed space allows efficient sketches if and only if it embeds (linearly) into l1-ε with constant distortion. We further prove that for norms that are closed under sum-product, efficient sketching is equivalent to embedding into l1 with constant distortion. Examples of such norms include the Earth Mover's Distance (specifically its norm variant, called Kantorovich-Rubinstein norm), and the trace norm (a.k.a. <PERSON> 1-norm or the nuclear norm). Using known non-embeddability theorems for these norms by <PERSON><PERSON> and <PERSON> (SICOMP, 2007) and by <PERSON><PERSON><PERSON> (Compositio. Math., 1978), we then conclude that these spaces do not admit efficient sketches either, making progress towards answering another open question (http://sublinear.info, Question #7).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746552"}, {"primary_key": "4503424", "vector": [], "sparse_vector": [], "title": "Optimal Data-Dependent Hashing for Approximate Near Neighbors.", "authors": ["<PERSON><PERSON><PERSON>", "Ilya <PERSON>"], "summary": "We show an optimal data-dependent hashing scheme for the approximate near neighbor problem. For an n-point dataset in a d-dimensional space our data structure achieves query time O(d ⋅ nρ+o(1)) and space O(n1+ρ+o(1) + d ⋅ n), where ρ=1/(2c2-1) for the Euclidean space and approximation c>1. For the Hamming space, we obtain an exponent of ρ=1/(2c-1). Our result completes the direction set forth in (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> 2014) who gave a proof-of-concept that data-dependent hashing can outperform classic Locality Sensitive Hashing (LSH). In contrast to (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> 2014), the new bound is not only optimal, but in fact improves over the best (optimal) LSH data structures (<PERSON>, <PERSON> 1998) (<PERSON><PERSON>, <PERSON> 2006) for all approximation factors c>1.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746553"}, {"primary_key": "4503425", "vector": [], "sparse_vector": [], "title": "Edit Distance Cannot Be Computed in Strongly Subquadratic Time (unless SETH is false).", "authors": ["Arturs Backurs", "<PERSON><PERSON><PERSON>"], "summary": "The edit distance (a.k.a. the <PERSON><PERSON><PERSON><PERSON> distance) between two strings is defined as the minimum number of insertions, deletions or substitutions of symbols needed to transform one string into another. The problem of computing the edit distance between two strings is a classical computational task, with a well-known algorithm based on dynamic programming. Unfortunately, all known algorithms for this problem run in nearly quadratic time.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746612"}, {"primary_key": "4503426", "vector": [], "sparse_vector": [], "title": "Sparse Quantum Codes from Quantum Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sparse quantum codes are analogous to LDPC codes in that their check operators require examining only a constant number of qubits. In contrast to LDPC codes, good sparse quantum codes are not known, and even to encode a single qubit, the best known distance is O(√{n log(n)}), due to <PERSON>, <PERSON> and <PERSON><PERSON>.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746608"}, {"primary_key": "4503427", "vector": [], "sparse_vector": [], "title": "On the Lovász Theta function for Independent Sets in Sparse Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the maximum independent set problem on graphs with maximum degree d. We show that the integrality gap of the Lovasz Theta function-based SDP has an integrality gap of O~(d/log3/2 d). This improves on the previous best result of O~(d/log d), and narrows the gap of this basic SDP to the integrality gap of O~(d/log2 d) recently shown for stronger SDPs, namely those obtained using poly log(d) levels of the SA+ semidefinite hierarchy. The improvement comes from an improved Ramsey-theoretic bound on the independence number of Kr-free graphs for large values of r.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746607"}, {"primary_key": "4503428", "vector": [], "sparse_vector": [], "title": "Minimizing Flow-Time on Unrelated Machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider some classical flow-time minimization problems in the unrelated machines setting. In this setting, there is a set of m machines and a set of n jobs, and each job j has a machine dependent processing time of pij on machine i. The flow-time of a job is the amount of time the job spends in a system (its completion time minus its arrival time), and is one of the most natural measure of quality of service. We show the following two results: an $O(min(log2 n, log n log P)) approximation algorithm for minimizing the total flow-time, and an O(log n) approximation for minimizing the maximum flow-time. Here P is the ratio of maximum to minimum job size. These are the first known poly-logarithmic guarantees for both the problems.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746601"}, {"primary_key": "4503429", "vector": [], "sparse_vector": [], "title": "Sum of Squares Lower Bounds from Pairwise Independence.", "authors": ["<PERSON>az <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that for every ε>0 and predicate P:{0,1}k-> {0,1} that supports a pairwise independent distribution, there exists an instance I of the Max P constraint satisfaction problem on n variables such that no assignment can satisfy more than a ~(|P-1(1)|)/(2k)+ε fraction of I's constraints but the degree Ω(n) Sum of Squares semidefinite programming hierarchy cannot certify that I is unsatisfiable. Similar results were previously only known for weaker hierarchies.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746625"}, {"primary_key": "4503430", "vector": [], "sparse_vector": [], "title": "Dictionary Learning and Tensor Decomposition via the Sum-of-Squares Method.", "authors": ["<PERSON>az <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a new approach to the dictionary learning (also known as \"sparse coding\") problem of recovering an unknown n x m matrix A (for m ≥ n) from examples of the form [y = Ax + e,] where x is a random vector in Rm with at most τ m nonzero coordinates, and e is a random noise vector in Rn with bounded magnitude. For the case m=O(n), our algorithm recovers every column of A within arbitrarily good constant accuracy in time mO(log m/log(τ-1)), in particular achieving polynomial time if τ = m-δ for any δ>0, and time mO(log m) if τ is (a sufficiently small) constant. Prior algorithms with comparable assumptions on the distribution required the vector $x$ to be much sparser---at most √n nonzero coordinates---and there were intrinsic barriers preventing these algorithms from applying for denser x.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746605"}, {"primary_key": "4503431", "vector": [], "sparse_vector": [], "title": "Approximating Nash Equilibria and Dense Bipartite Subgraphs via an Approximate Version of Caratheodory&apos;s Theorem.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present algorithmic applications of an approximate version of <PERSON><PERSON><PERSON><PERSON>'s theorem. The theorem states that given a set of vectors X in Rd, for every vector in the convex hull of X there exists an ε-close (under the p-norm distance, for 2 ≤ p < ∞) vector that can be expressed as a convex combination of at most b vectors of X, where the bound b depends on ε and the norm p and is independent of the dimension d. This theorem can be derived by instantiating <PERSON><PERSON><PERSON>'s lemma, early references to which can be found in the work of <PERSON><PERSON><PERSON> (1981) and <PERSON> (1985). However, in this paper we present a self-contained proof of this result.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746566"}, {"primary_key": "4503432", "vector": [], "sparse_vector": [], "title": "Local, Private, Efficient Protocols for Succinct Histograms.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We give efficient protocols and matching accuracy lower bounds for frequency estimation in the local model for differential privacy. In this model, individual users randomize their data themselves, sending differentially private reports to an untrusted server that aggregates them. We study protocols that produce a succinct histogram representation of the data. A succinct histogram is a list of the most frequent items in the data (often called \"heavy hitters\") along with estimates of their frequencies; the frequency of all other items is implicitly estimated as 0. If there are $n$ users whose items come from a universe of size $d$, our protocols run in time polynomial in $n$ and $\\log(d)$. With high probability, they estimate the accuracy of every item up to error $O\\left(\\sqrt{\\log(d)/(\\epsilon^2n)}\\right)$ where $\\epsilon$ is the privacy parameter. Moreover, we show that this much error is necessary, regardless of computational efficiency, and even for the simple setting where only one item appears with significant frequency in the data set. Previous protocols (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2006; <PERSON><PERSON>, <PERSON> and <PERSON>, 2012) for this task either ran in time $\\Omega(d)$ or had much worse error (about $\\sqrt[6]{\\log(d)/(\\epsilon^2n)}$), and the only known lower bound on error was $\\Omega(1/\\sqrt{n})$. We also adapt a result of <PERSON> et al (2010) to the local setting. In a model with public coins, we show that each user need only send 1 bit to the server. For all known local protocols (including ours), the transformation preserves computational efficiency.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746632"}, {"primary_key": "4503433", "vector": [], "sparse_vector": [], "title": "Space- and Time-Efficient Algorithm for Maintaining Dense Subgraphs on One-Pass Dynamic Streams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>", "Charalampos E<PERSON>"], "summary": "While in many graph mining applications it is crucial to handle a stream of updates efficiently in terms of both time and space, not much was known about achieving such type of algorithm. In this paper we study this issue for a problem which lies at the core of many graph mining applications called densest subgraph problem. We develop an algorithm that achieves time- and space-efficiency for this problem simultaneously. It is one of the first of its kind for graph problems to the best of our knowledge.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746592"}, {"primary_key": "4503434", "vector": [], "sparse_vector": [], "title": "Succinct Randomized Encodings and their Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A randomized encoding allows to express a \"complex\" computation, given by a function f and input x, by a \"simple to compute\" randomized representation f(x) whose distribution encodes f(x), while revealing nothing else regarding f and x. Existing randomized encodings, geared mostly to allow encoding with low parallel-complexity, have proven instrumental in various strong applications such as multiparty computation and parallel cryptography. This work focuses on another natural complexity measure: the time required to encode. We construct succinct randomized encodings where the time to encode a computation, given by a program Π and input x, is essentially independent of <PERSON>'s time complexity, and only depends on its space complexity, as well as the size of its input, output, and description. The scheme guarantees computational privacy of (Π,x), and is based on indistinguishability obfuscation for a relatively simple circuit class, for which there exist instantiations based on polynomial hardness assumptions on multi-linear maps.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746574"}, {"primary_key": "4503435", "vector": [], "sparse_vector": [], "title": "Toward a Unified Theory of Sparse Dimensionality Reduction in Euclidean Space.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let Φ∈Rm x n be a sparse <PERSON><PERSON><PERSON> transform [52] with column sparsity s. For a subset T of the unit sphere and ε∈(0,1/2), we study settings for m,s to ensure EΦ supx∈ T |Φ x|22 - 1| < ε, i.e. so that Φ preserves the norm of every x ∈ T simultaneously and multiplicatively up to 1+ε. We introduce a new complexity parameter, which depends on the geometry of T, and show that it suffices to choose s and m such that this parameter is small. Our result is a sparse analog of <PERSON>'s theorem, which was concerned with a dense Φ having i.i.d. Gaussian entries. We qualitatively unify several results related to the <PERSON> lemma, subspace embeddings, and Fourier-based restricted isometries. Our work also implies new results in using the sparse <PERSON> transform in randomized linear algebra, compressed sensing, manifold learning, and constrained least squares problems such as the Lasso.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746541"}, {"primary_key": "4503436", "vector": [], "sparse_vector": [], "title": "Inapproximability of Combinatorial Problems via Small LPs and SDPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Motivated by [12], we provide a framework for studying the size of linear programming formulations as well as semidefinite programming formulations of combinatorial optimization problems without encoding them first as linear programs. This is done via a factorization theorem for the optimization problem itself (and not a specific encoding of such). As a result we define a consistent reduction mechanism that degrades approximation factors in a controlled fashion and which, at the same time, is compatible with approximate linear and semidefinite programming formulations. Moreover, our reduction mechanism is a minor restriction of classical reductions establishing inapproximability in the context of PCP theorems. As a consequence we establish strong linear programming inapproximability (for LPs with a polynomial number of constraints) for several problems that are not 0/1-CSPs: we obtain a 3/2-epsilon inapproximability for Vertex Cover (which is not of the CSP type) answering an open question in [12], we answer a weak version of our sparse graph conjecture posed in [6] showing an inapproximability factor of 1/2+ε for bounded degree IndependentSet, and we establish inapproximability of MaxMULTICUT (a non-binary CSP). In the case of SDPs, we obtain relative inapproximability results for these problems.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746550"}, {"primary_key": "4503437", "vector": [], "sparse_vector": [], "title": "Small Value Parallel Repetition for General Games.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a parallel repetition theorem for general games with value tending to 0. Previously <PERSON><PERSON> and <PERSON><PERSON><PERSON> proved such a theorem for the special case of projection games. We use information theoretic techniques in our proof. Our proofs also extend to the high value regime (value close to 1) and provide alternate proofs for the parallel repetition theorems of <PERSON><PERSON> and <PERSON> for general and projection games respectively. We also extend the example of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> to show that the small-value parallel repetition bound we obtain is tight. Our techniques are elementary in that we only need to employ basic information theory and discrete probability in the small-value parallel repetition proof.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746565"}, {"primary_key": "4503438", "vector": [], "sparse_vector": [], "title": "An Interactive Information Odometer and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a novel technique which enables two players to maintain an estimate of the internal information cost of their conversation in an online fashion without revealing much extra information. We use this construction to obtain new results about communication complexity and information-theoretic privacy.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746548"}, {"primary_key": "4503439", "vector": [], "sparse_vector": [], "title": "Efficiently Learning Ising Models on Arbitrary Graphs.", "authors": ["<PERSON>"], "summary": "graph underlying an Ising model from i.i.d. samples. Over the last fifteen years this problem has been of significant interest in the statistics, machine learning, and statistical physics communities, and much of the effort has been directed towards finding algorithms with low computational cost for various restricted classes of models. Nevertheless, for learning Ising models on general graphs with p nodes of degree at most d, it is not known whether or not it is possible to improve upon the pd computation needed to exhaustively search over all possible neighborhoods for each node.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746631"}, {"primary_key": "4503440", "vector": [], "sparse_vector": [], "title": "Succinct Garbling and Indistinguishability Obfuscation for RAM Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show how to construct succinct Indistinguishability Obfuscation (IO) schemes for RAM programs. That is, given a RAM program whose computation requires space S and time T, we generate a RAM program with size and space requirements of ~O(S) and runtime ~O(T). The construction uses non-succinct IO (i.e., IO for circuits) and injective one way functions, both with sub-exponential security. A main component in our scheme is a succinct garbling scheme for RAM programs. Our garbling scheme has the same size, space and runtime parameters as above, and requires only polynomial security of the underlying primitives. This scheme has other qualitatively new applications such as publicly verifiable succinct non-interactive delegation of computation and succinct functional encryption.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746621"}, {"primary_key": "4503441", "vector": [], "sparse_vector": [], "title": "Clustered Integer 3SUM via Additive Combinatorics.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present a collection of new results on problems related to 3SUM, including: The first truly subquadratic algorithm for computing the (min,+) convolution for monotone increasing sequences with integer values bounded by O(n), solving 3SUM for monotone sets in 2D with integer coordinates bounded by O(n), and preprocessing a binary string for histogram indexing (also called jumbled indexing).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746568"}, {"primary_key": "4503442", "vector": [], "sparse_vector": [], "title": "Near Optimal LP Rounding Algorithm for CorrelationClustering on Complete and Complete k-partite Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give new rounding schemes for the standard linear programming relaxation of the correlation clustering problem, achieving approximation factors almost matching the integrality gaps: For complete graphs our approximation is 2.06 - ε, which almost matches the previously known integrality gap of 2. For complete k-partite graphs our approximation is 3. We also show a matching integrality gap. For complete graphs with edge weights satisfying triangle inequalities and probability constraints, our approximation is 1.5, and we show an integrality gap of 1.2.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746604"}, {"primary_key": "4503443", "vector": [], "sparse_vector": [], "title": "Approximate Distance Oracles with Improved Bounds.", "authors": ["<PERSON><PERSON>"], "summary": "A distance oracle is a compact data structure capable of quickly estimating distances in a given graph. In this paper we provide a new construction for distance oracles in general undirected weighted graphs. Our data structure, for any integer k, requires O( n1+1/k) space, guarantees a stretch of 2k-1, and answers any query in only O(1) time.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746562"}, {"primary_key": "4503444", "vector": [], "sparse_vector": [], "title": "On the Complexity of Nash Equilibria in Anonymous Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the problem of finding an ε-approximate Nash equilibrium in an {anonymous} game with seven pure strategies is complete in PPAD, when the approximation parameter ε is exponentially small in the number of players.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746571"}, {"primary_key": "4503445", "vector": [], "sparse_vector": [], "title": "Boolean Function Monotonicity Testing Requires (Almost) n1/2 Non-adaptive Queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a lower bound of Ω(n1/2-c), for all c> 0, on the query complexity of (two-sided error) non-adaptive algorithms for testing whether an n-variable Boolean function is monotone versus constant-far from monotone. This improves a ~Ω(n1/5) lower bound for the same problem that was obtained in [6], and is very close to the recent upper bound of ~O(n1/2/ε2) by <PERSON><PERSON> et al. [13].", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746570"}, {"primary_key": "4503446", "vector": [], "sparse_vector": [], "title": "A Characterization of the Capacity of Online (causal) Binary Channels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the binary online (or \"causal\") channel coding model, a sender wishes to communicate a message to a receiver by transmitting a codeword x = (x1,...,xn) ∈ {0,1}n bit by bit via a channel limited to at most pn corruptions. The channel is \"online\" in the sense that at the ith step of communication the channel decides whether to corrupt the ith bit or not based on its view so far, i.e., its decision depends only on the transmitted bits (x1,...,xi). This is in contrast to the classical adversarial channel in which the error is chosen by a channel that has full knowledge of the transmitted codeword x.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746591"}, {"primary_key": "4503447", "vector": [], "sparse_vector": [], "title": "From Independence to Expansion and Back Again.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following fundamental problems: Constructing k-independent hash functions with a space-time tradeoff close to <PERSON><PERSON>'s lower bound. Constructing representations of unbalanced expander graphs having small size and allowing fast computation of the neighbor function.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746620"}, {"primary_key": "4503448", "vector": [], "sparse_vector": [], "title": "Excluded <PERSON><PERSON>: Improved and Simplified.", "authors": ["<PERSON>"], "summary": "We study the Excluded Grid Theorem of <PERSON> and <PERSON>. This is a fundamental result in graph theory, that states that there is some function f:Z+→ Z+, such that for any integer g> 0, any graph of treewidth at least f(g), contains the (g x g)-grid as a minor. Until recently, the best known upper bounds on f were super-exponential in g. A recent work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> provided the first polynomial bound, by showing that treewidth f(g)=O(g98 poly log g) is sufficient to ensure the existence of the (g x g)-grid minor in any graph. In this paper we provide a much simpler proof of the Excluded Grid Theorem, achieving a bound of $f(g)=O(g^{36} poly log g)$. Our proof is self-contained, except for using prior work to reduce the maximum vertex degree of the input graph to a constant.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746551"}, {"primary_key": "4503449", "vector": [], "sparse_vector": [], "title": "Dimensionality Reduction for k-Means Clustering and Low Rank Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how to approximate a data matrix A with a much smaller sketch ~A that can be used to solve a general class of constrained k-rank approximation problems to within (1+ε) error. Importantly, this class includes k-means clustering and unconstrained low rank approximation (i.e. principal component analysis). By reducing data points to just O(k) dimensions, we generically accelerate any exact, approximate, or heuristic algorithm for these ubiquitous problems. For k-means dimensionality reduction, we provide (1+ε) relative error results for many common sketching techniques, including random row projection, column selection, and approximate SVD. For approximate principal component analysis, we give a simple alternative to known algorithms that has applications in the streaming setting. Additionally, we extend recent work on column-based matrix reconstruction, giving column subsets that not only 'cover' a good subspace for A}, but can be used directly to compute this subspace.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746569"}, {"primary_key": "4503450", "vector": [], "sparse_vector": [], "title": "Lp Row Sampling by <PERSON> Weights.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a simple algorithm to efficiently sample the rows of a matrix while preserving the p-norms of its product with vectors. Given an n * d matrix A, we find with high probability and in input sparsity time an A' consisting of about d log d rescaled rows of A such that |Ax|1 is close to |A'x|1 for all vectors x. We also show similar results for all Lp that give nearly optimal sample bounds in input sparsity time. Our results are based on sampling by \"Lewis weights\", which can be viewed as statistical leverage scores of a reweighted matrix. We also give an elementary proof of the guarantees of this sampling process for L1.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746567"}, {"primary_key": "4503451", "vector": [], "sparse_vector": [], "title": "Bypassing KLS: Gaussian Cooling and an O*(n3) Volume Algorithm.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "We present an O*(n3) randomized algorithm for estimating the volume of a well-rounded convex body given by a membership oracle, improving on the previous best complexity of O*(n4). The new algorithmic ingredient is an accelerated cooling schedule where the rate of cooling increases with the temperature. Previously, the known approach for potentially achieving such complexity relied on a positive resolution of the KLS hyperplane conjecture, a central open problem in convex geometry.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746563"}, {"primary_key": "4503452", "vector": [], "sparse_vector": [], "title": "Random Permutations using Switching Networks.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the problem of designing a simple, oblivious scheme to generate (almost) random permutations. We use the concept of switching networks and show that almost every switching network of logarithmic depth can be used to almost randomly permute any set of (1-ε) n elements with any ε > 0 (that is, gives an almost (1-ε) n$-wise independent permutation). Furthermore, we show that the result still holds for every switching network of logarithmic depth that has some special expansion properties, leading to an explicit construction of such networks. Our result can be also extended to an explicit construction of a switching network of depth O(log2n) and with O(n log n) switches that almost randomly permutes any set of n elements. We also discuss basic applications of these results in cryptography. Our results are obtained using a non-trivial coupling approach to study mixing times of Markov chains which allows us to reduce the problem to some random walk-like problem on expanders.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746629"}, {"primary_key": "4503453", "vector": [], "sparse_vector": [], "title": "Testing Cluster Structure of Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of recognizing the cluster structure of a graph in the framework of property testing in the bounded degree model. Given a parameter ε, a d-bounded degree graph is defined to be (k, φ)-clusterable, if it can be partitioned into no more than k parts, such that the (inner) conductance of the induced subgraph on each part is at least φ and the (outer) conductance of each part is at most cd,kε4φ2, where cd,k depends only on d,k. Our main result is a sublinear algorithm with the running time ~O(√n ⋅ poly(φ,k,1/ε)) that takes as input a graph with maximum degree bounded by d, parameters k, φ, ε, and with probability at least 2/3, accepts the graph if it is (k,φ)-clusterable and rejects the graph if it is ε-far from (k, φ*)-clusterable for φ* = c'd,kφ2 ε4}/log n, where c'd,k depends only on d,k. By the lower bound of Ω(√n) on the number of queries needed for testing graph expansion, which corresponds to k=1 in our problem, our algorithm is asymptotically optimal up to polylogarithmic factors.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746618"}, {"primary_key": "4503454", "vector": [], "sparse_vector": [], "title": "Inapproximability of Truthful Mechanisms via Generalizations of the VC Dimension.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Algorithmic mechanism design (AMD) studies the delicate interplay between computational efficiency, truthfulness, and optimality. We focus on AMD's paradigmatic problem: combinatorial auctions. We present a new generalization of the VC dimension to multivalued collections of functions, which encompasses the classical VC dimension, Natarajan dimension, and Steele dimension. We present a corresponding generalization of the Sauer-<PERSON><PERSON> Lemma and harness this VC machinery to establish inapproximability results for deterministic truthful mechanisms. Our results essentially unify all inapproximability results for deterministic truthful mechanisms for combinatorial auctions to date and establish new separation gaps between truthful and non-truthful algorithms.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746597"}, {"primary_key": "4503455", "vector": [], "sparse_vector": [], "title": "Proof of the Satisfiability Conjecture for Large k.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Nike Sun"], "summary": "We establish the satisfiability threshold for random k-SAT for all k ≥ k0. That is, there exists a limiting density αs(k) such that a random k-SAT formula of clause density α is with high probability satisfiable for α αs. The satisfiability threshold αs is given explicitly by the one-step replica symmetry breaking (1SRB) prediction from statistical physics. We believe that our methods may apply to a range of random constraint satisfaction problems in the 1RSB class.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746619"}, {"primary_key": "4503456", "vector": [], "sparse_vector": [], "title": "Polynomially Low Error PCPs with polyloglog n Queries via Modular Composition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that every language in NP has a PCP verifier that tosses O(log n) random coins, has perfect completeness, and a soundness error of at most 1/poly(n), while making O(poly log log n) queries into a proof over an alphabet of size at most n1/poly log log n. Previous constructions that obtain 1/poly(n) soundness error used either poly log n queries or an exponential alphabet, i.e. of size 2nc for some c> 0. Our result is an exponential improvement in both parameters simultaneously. Our result can be phrased as polynomial-gap hardness for approximate CSPs with arity poly log log n and alphabet size n1/poly log n. The ultimate goal, in this direction, would be to prove polynomial hardness for CSPs with constant arity and polynomial alphabet size (aka the sliding scale conjecture for inverse polynomial soundness error).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746630"}, {"primary_key": "4503457", "vector": [], "sparse_vector": [], "title": "2-Server PIR with Sub-Polynomial Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A 2-server Private Information Retrieval (PIR) scheme allows a user to retrieve the ith bit of an n-bit database replicated among two non-communicating servers, while not revealing any information about i to either server. In this work we construct a 2-server PIR scheme with total communication cost nO√(log log n)/(log n). This improves over current 2-server protocols which all require Ω(n1/3) communication. Our construction circumvents the n1/3 barrier of Razborov and Yekhanin which holds for the restricted model of bilinear group-based schemes (covering all previous 2-server schemes). The improvement comes from reducing the number of servers in existing protocols, based on Matching Vector Codes, from 3 or 4 servers to 2. This is achieved by viewing these protocols in an algebraic way (using polynomial interpolation) and extending them using partial derivatives.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746546"}, {"primary_key": "4503458", "vector": [], "sparse_vector": [], "title": "Preserving Statistical Validity in Adaptive Data Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A great deal of effort has been devoted to reducing the risk of spurious scientific discoveries, from the use of sophisticated validation techniques, to deep statistical methods for controlling the false discovery rate in multiple hypothesis testing. However, there is a fundamental disconnect between the theoretical results and the practice of data analysis: the theory of statistical inference assumes a fixed collection of hypotheses to be tested, or learning algorithms to be applied, selected non-adaptively before the data are gathered, whereas in practice data is shared and reused with hypotheses and new analyses being generated on the basis of data exploration and the outcomes of previous analyses.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746580"}, {"primary_key": "4503459", "vector": [], "sparse_vector": [], "title": "Prioritized Metric Structures and Embedding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Metric data structures (distance oracles, distance labeling schemes, routing schemes) and low-distortion embeddings provide a powerful algorithmic methodology, which has been successfully applied for approximation algorithms [21], online algorithms [7], distributed algorithms [19] and for computing sparsifiers [28]. However, this methodology appears to have a limitation: the worst-case performance inherently depends on the cardinality of the metric, and one could not specify in advance which vertices/points should enjoy a better service (i.e., stretch/distortion, label size/dimension) than that given by the worst-case guarantee.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746623"}, {"primary_key": "4503460", "vector": [], "sparse_vector": [], "title": "The Complexity of the Simplex Method.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The simplex method is a well-studied and widely-used pivoting method for solving linear programs. When <PERSON><PERSON><PERSON> originally formulated the simplex method, he gave a natural pivot rule that pivots into the basis a variable with the most violated reduced cost. In their seminal work, <PERSON><PERSON> and <PERSON><PERSON> showed that this pivot rule takes exponential time in the worst case. We prove two main results on the simplex method. Firstly, we show that it is PSPACE-complete to find the solution that is computed by the simplex method using <PERSON><PERSON><PERSON>'s pivot rule. Secondly, we prove that deciding whether <PERSON><PERSON><PERSON>'s rule ever chooses a specific variable to enter the basis is PSPACE-complete. We use the known connection between Markov decision processes (MDPs) and linear programming, and an equivalence between <PERSON><PERSON><PERSON>'s pivot rule and a natural variant of policy iteration for average-reward MDPs. We construct MDPs and then show PSPACE-completeness results for single-switch policy iteration, which in turn imply our main results for the simplex method.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746558"}, {"primary_key": "4503461", "vector": [], "sparse_vector": [], "title": "On the Complexity of Random Satisfiability Problems with Planted Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Santosh S<PERSON>"], "summary": "The problem of identifying a planted assignment given a random k-SAT formula consistent with the assignment exhibits a large algorithmic gap: while the planted solution can always be identified given a formula with O(n log n) clauses, there are distributions over clauses for which the best known efficient algorithms require nk/2 clauses. We propose and study a unified model for planted k-SAT, which captures well-known special cases. An instance is described by a planted assignment σ and a distribution on clauses with k literals. We define its distribution complexity as the largest r for which the distribution is not r-wise independent (1 ≤ r ≤ k for any distribution with a planted assignment).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746577"}, {"primary_key": "4503462", "vector": [], "sparse_vector": [], "title": "A Polynomial-time Bicriteria Approximation Scheme for Planar Bisection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given an undirected graph with edge costs and node weights, the minimum bisection problem asks for a partition of the nodes into two parts of equal weight such that the sum of edge costs between the parts is minimized. We give a polynomial time bicriteria approximation scheme for bisection on planar graphs. Specifically, let W be the total weight of all nodes in a planar graph G. For any constant ε > 0, our algorithm outputs a bipartition of the nodes such that each part weighs at most W/2 + ε and the total cost of edges crossing the partition is at most (1+ε) times the total cost of the optimal bisection. The previously best known approximation for planar minimum bisection, even with unit node weights, was ~O(log n). Our algorithm actually solves a more general problem where the input may include a target weight for the smaller side of the bipartition.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746564"}, {"primary_key": "4503463", "vector": [], "sparse_vector": [], "title": "Exponential Separation of Information and Communication for Boolean Functions.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON>"], "summary": "We show an exponential gap between communication complexity and information complexity for boolean functions, by giving an explicit example of a partial function with information complexity ≤ O(k), and distributional communication complexity ≥ 2k. This shows that a communication protocol for a partial boolean function cannot always be compressed to its internal information. By a result of <PERSON><PERSON> [Bra12], our gap is the largest possible. By a result of <PERSON><PERSON> and <PERSON> [BR11], our example shows a gap between communication complexity and amortized communication complexity, implying that a tight direct sum result for distributional communication complexity of boolean functions cannot hold, answering a long standing open problem. Our techniques build on [GKR14], that proved a similar result for relations with very long outputs (double exponentially long in k). In addition to the stronger result, the current work gives a simpler proof, benefiting from the short output length of boolean functions.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746572"}, {"primary_key": "4503464", "vector": [], "sparse_vector": [], "title": "Garbled RAM From One-Way Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>'s garbled circuit construction is a very fundamental result in cryptography and recent efficiency optimizations have brought it much closer to practice. However these constructions work only for circuits and garbling a RAM program involves the inefficient process of first converting it into a circuit. Towards the goal of avoiding this inefficiency, <PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt 2013) introduced the notion of \"garbled RAM\" as a method to garble RAM programs directly. It can be seen as a RAM analogue of <PERSON>'s garbled circuits such that, the size of the garbled program and the time it takes to create and evaluate it, is proportional only to the running time on the RAM program rather than its circuit size. Known realizations of this primitive, either need to rely on strong computational assumptions or do not achieve the aforementioned efficiency (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, EUROCRYPT 2014). In this paper we provide the first construction with strictly poly-logarithmic overhead in both space and time based only on the minimal assumption that one-way functions exist. Our scheme allows for garbling multiple programs being executed on a persistent database, and has the additional feature that the program garbling is decoupled from the database garbling. This allows a client to provide multiple garbled programs to the server as part of a pre-processing phase and then later determine the order and the inputs on which these programs are to be executed, doing work independent of the running times of the programs itself.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746593"}, {"primary_key": "4503465", "vector": [], "sparse_vector": [], "title": "Learning Mixtures of Gaussians in High Dimensions.", "authors": ["<PERSON><PERSON> Ge", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Efficiently learning mixture of Gaussians is a fundamental problem in statistics and learning theory. Given samples coming from a random one out of k Gaussian distributions in Rn, the learning problem asks to estimate the means and the covariance matrices of these Gaussians. This learning problem arises in many areas ranging from the natural sciences to the social sciences, and has also found many ma- chine learning applications. Unfortunately, learning mixture of Gaussians is an information theoretically hard problem: in order to learn the parameters up to a reasonable accuracy, the number of samples required is exponential in the number of Gaussian components in the worst case. In this work, we show that provided we are in high enough dimensions, the class of Gaussian mixtures is learnable in its most general form under a smoothed analysis framework, where the parameters are randomly perturbed from an adversarial starting point. In particular, given samples from a mixture of Gaussians with randomly perturbed parameters, when n ≥ Ω(k2), we give an algorithm that learns the parameters with polynomial running time and using polynomial number of samples.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746616"}, {"primary_key": "4503466", "vector": [], "sparse_vector": [], "title": "Test-and-Set in Optimal Space.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The test-and-set object is a fundamental synchronization primitive for shared memory systems. This paper addresses the number of registers (supporting atomic reads and writes) required to implement a one-shot test-and-set object in the standard asynchronous shared memory model with n processes. The best lower bound is log n - 1 [12,21] for obstruction-free and deadlock-free implementations, and recently a deterministic obstruction-free implementation using O(√ n) registers was presented [11].", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746627"}, {"primary_key": "4503467", "vector": [], "sparse_vector": [], "title": "Rectangles Are Nonnegative Juntas.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a new method to prove communication lower bounds for composed functions of the form f o gn where f is any boolean function on n inputs and g is a sufficiently \"hard\" two-party gadget. Our main structure theorem states that each rectangle in the communication matrix of f o gn can be simulated by a nonnegative combination of juntas. This is the strongest yet formalization for the intuition that each low-communication randomized protocol can only \"query\" few inputs of f as encoded by the gadget g. Consequently, we characterize the communication complexity of f o gn in all known one-sided zero-communication models by a corresponding query complexity measure of f. These models in turn capture important lower bound techniques such as corruption, smooth rectangle bound, relaxed partition bound, and extended discrepancy. As applications, we resolve several open problems from prior work: We show that SBPcc (a class characterized by corruption) is not closed under intersection. An immediate corollary is that MAcc ≠ SBPcc. These results answer questions of <PERSON><PERSON><PERSON> (CCC 2003) and <PERSON><PERSON> et al. (JCSS 2006). We also show that approximate nonnegative rank of partial boolean matrices does not admit efficient error reduction. This answers a question of <PERSON><PERSON> et al. (ICALP) for partial matrices.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746596"}, {"primary_key": "4503468", "vector": [], "sparse_vector": [], "title": "Leveled Fully Homomorphic Signatures from Standard Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a homomorphic signature scheme, a user <PERSON> signs some large dataset x using her secret signing key and uploads the signed data to an untrusted remote server. The server can then run some computation y=f(x) over the signed data and homomorphically derive a short signature σf,y certifying that y is the correct output of the computation f. Anybody can verify the tuple (f, y, σf,y) using <PERSON>'s public verification key and become convinced of this fact without having to retrieve the entire underlying data. In this work, we construct the first leveled fully homomorphic signature} schemes that can evaluate arbitrary {circuits} over signed data. Only the maximal {depth} d of the circuits needs to be fixed a-priori at setup, and the size of the evaluated signature grows polynomially in d, but is otherwise independent of the circuit size or the data size. Our solution is based on the (sub-exponential) hardness of the small integer solution (SIS) problem in standard lattices and satisfies full (adaptive) security. In the standard model, we get a scheme with large public parameters whose size exceeds the total size of a dataset. In the random-oracle model, we get a scheme with short public parameters. In both cases, the schemes can be used to sign many different datasets. The complexity of verifying a signature for a computation f is at least as large as that of computing f, but can be amortized when verifying the same computation over many different datasets. Furthermore, the signatures can be made context-hiding so as not to reveal anything about the data beyond the outcome of the computation.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746576"}, {"primary_key": "4503469", "vector": [], "sparse_vector": [], "title": "The communication complexity of interleaved group products.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON> receives a tuple (a1,...,at) of t elements from the group G = SL(2,q). <PERSON> similarly receives a tuple of t elements (b1,...,bt). They are promised that the interleaved product prodi ≤ t ai bi equals to either g and h, for two fixed elements g,h ∈ G. Their task is to decide which is the case.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746560"}, {"primary_key": "4503470", "vector": [], "sparse_vector": [], "title": "Computing with <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Tangles of graphs have been introduced by <PERSON> and <PERSON> in the context of their graph minor theory. Tangles may be viewed as describing \"k-connected components\" of a graph (though in a twisted way). They play an important role in graph minor theory. An interesting aspect of tangles is that they cannot only be defined for graphs, but more generally for arbitrary connectivity functions (that is, integer-valued submodular and symmetric set functions).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746587"}, {"primary_key": "4503471", "vector": [], "sparse_vector": [], "title": "Greedy Algorithms for Steiner Forest.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the Steiner Forest problem, we are given terminal pairs si, ti, and need to find the cheapest subgraph which connects each of the terminal pairs together. In 1991, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> gave a primal-dual constant-factor approximation algorithm for this problem. Until this work, the only constant-factor approximations we know are via linear programming relaxations.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746590"}, {"primary_key": "4503472", "vector": [], "sparse_vector": [], "title": "How Well Can Graphs Represent Wireless Interference?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient use of a wireless network requires that transmissions be grouped into feasible sets, where feasibility means that each transmission can be successfully decoded in spite of the interference caused by simultaneous transmissions. Feasibility is most closely modeled by a signal-to-interference-plus-noise (SINR) formula, which unfortunately is conceptually complicated, being an asymmetric, cumulative, many-to-one relationship. We re-examine how well graphs can capture wireless receptions as encoded in SINR relationships, placing them in a framework in order to understand the limits of such modelling. We seek for each wireless instance a pair of graphs that provide upper and lower bounds on the feasibility relation, while aiming to minimize the gap between the two graphs. The cost of a graph formulation is the worst gap over all instances, and the price of (graph) abstraction is the smallest cost of a graph formulation. We propose a family of conflict graphs that is parameterized by a non-decreasing sub-linear function, and show that with a judicious choice of functions, the graphs can capture feasibility with a cost of O(log* Δ), where Δ is the ratio between the longest and the shortest link length. This holds on the plane and more generally in doubling metrics. We use this to give greatly improved O(log* Δ)-approximation for fundamental link scheduling problems with arbitrary power control. We also explore the limits of graph representations and find that our upper bound is tight: the price of graph abstraction is Ω(log* Δ). In addition, we give strong impossibility results for general metrics, and for approximations in terms of the number of links.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746585"}, {"primary_key": "4503473", "vector": [], "sparse_vector": [], "title": "An Improved Version of the Random-Facet Pivoting Rule for the Simplex Algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The Random-Facet pivoting rule of <PERSON><PERSON> and of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> is an elegant randomized pivoting rule for the simplex algorithm, the classical combinatorial algorithm for solving linear programs (LPs). The expected number of pivoting steps performed by the simplex algorithm when using this rule, on any linear program involving n inequalities in d variables, is 2O(√{(n-d),log({d}/{√{n-d}}},), where log n=max{1,log n}. A dual version of the algorithm performs an expected number of at most 2O(√{d,log({(n-d)}/√d},) dual pivoting steps. This dual version is currently the fastest known combinatorial algorithm for solving general linear programs. <PERSON><PERSON> also obtained a primal pivoting rule which performs an expected number of at most 2O(√d,log n) pivoting steps. We present an improved version of <PERSON><PERSON>'s pivoting rule for which the expected number of primal pivoting steps is at most min{2O(√(n-d),log(d/(n-d),)},2O(√{d,log((n-d)/d}},)}. This seemingly modest improvement is interesting for at least two reasons. First, the improved bound for the number of primal pivoting steps is better than the previous bounds for both the primal and dual pivoting steps. There is no longer any need to consider a dual version of the algorithm. Second, in the important case in which n=O(d), i.e., the number of linear inequalities is linear in the number of variables, the expected running time becomes 2O(√d) rather than 2O(√d log d). Our results, which extend previous results of Gartner, apply not only to LP problems, but also to LP-type problems, supplying in particular slightly improved algorithms for solving 2-player turn-based stochastic games and related problems.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746557"}, {"primary_key": "4503474", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Learning a Mixture of Two Gaussians.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of identifying the parameters of an unknown mixture of two arbitrary d-dimensional gaussians from a sequence of independent random samples. Our main results are upper and lower bounds giving a computationally efficient moment-based estimator with an optimal convergence rate, thus resolving a problem introduced by <PERSON> (1894). Denoting by σ2 the variance of the unknown mixture, we prove that Θ(σ12) samples are necessary and sufficient to estimate each parameter up to constant additive error when d=1. Our upper bound extends to arbitrary dimension d>1 up to a (provably necessary) logarithmic loss in d using a novel---yet simple---dimensionality reduction technique. We further identify several interesting special cases where the sample complexity is notably smaller than our optimal worst-case bound. For instance, if the means of the two components are separated by Ω(σ) the sample complexity reduces to O(σ2) and this is again optimal.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746579"}, {"primary_key": "4503475", "vector": [], "sparse_vector": [], "title": "Unifying and Strengthening Hardness for Dynamic Problems via the Online Matrix-Vector Multiplication Conjecture.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "Consider the following Online Boolean Matrix-Vector Multiplication problem: We are given an n x n matrix M and will receive n column-vectors of size n, denoted by v1, ..., vn, one by one. After seeing each vector vi, we have to output the product Mvi before we can see the next vector. A naive algorithm can solve this problem using O(n3) time in total, and its running time can be slightly improved to O(n3/log2 n) [Williams SODA'07]. We show that a conjecture that there is no truly subcubic (O(n3-ε)) time algorithm for this problem can be used to exhibit the underlying polynomial time hardness shared by many dynamic problems. For a number of problems, such as subgraph connectivity, <PERSON><PERSON>'s problem, d-failure connectivity, decremental single-source shortest paths, and decremental transitive closure, this conjecture implies tight hardness results. Thus, proving or disproving this conjecture will be very interesting as it will either imply several tight unconditional lower bounds or break through a common barrier that blocks progress with these problems. This conjecture might also be considered as strong evidence against any further improvement for these problems since refuting it will imply a major breakthrough for combinatorial Boolean matrix multiplication and other long-standing problems if the term \"combinatorial algorithms\" is interpreted as \"Strassen-like algorithms\" [<PERSON> et al. SPAA'11].", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746609"}, {"primary_key": "4503476", "vector": [], "sparse_vector": [], "title": "The Directed G<PERSON>.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The grid theorem, originally proved in 1986 by <PERSON> and <PERSON> in Graph Minors V, is one of the most central results in the study of graph minors. It has found numerous applications in algorithmic graph structure theory, for instance in bidimensionality theory, and it is the basis for several other structure theorems developed in the graph minors project.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746586"}, {"primary_key": "4503477", "vector": [], "sparse_vector": [], "title": "Beyond the Euler Characteristic: Approximating the Genus of General Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Computing the Euler genus of a graph is a fundamental problem in graph theory and topology. It has been shown to be NP-hard by <PERSON><PERSON> [27] and a linear-time fixed-parameter algorithm has been obtained by <PERSON><PERSON> [20]. Despite extensive study, the approximability of the Euler genus remains wide open. While the existence of a constant factor approximation is not ruled out, the currently best-known upper bound is a trivial O(n/g)-approximation that follows from bounds on the Euler characteristic.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746583"}, {"primary_key": "4503478", "vector": [], "sparse_vector": [], "title": "Deterministic Global Minimum Cut of a Simple Graph in Near-Linear Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a deterministic near-linear time algorithm that computes the edge-connectivity and finds a minimum cut for a simple undirected unweighted graph G with n vertices and m edges. This is the first o(mn) time deterministic algorithm for the problem. In near-linear time we can also construct the classic cactus representation of all minimum cuts.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746588"}, {"primary_key": "4503479", "vector": [], "sparse_vector": [], "title": "Secretary Problems with Non-Uniform Arrival Order.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For a number of problems in the theory of online algorithms, it is known that the assumption that elements arrive in uniformly random order enables the design of algorithms with much better performance guarantees than under worst-case assumptions. The quintessential example of this phenomenon is the secretary problem, in which an algorithm attempts to stop a sequence at the moment it observes the maximum value in the sequence. As is well known, if the sequence is presented in uniformly random order there is an algorithm that succeeds with probability 1/e, whereas no non-trivial performance guarantee is possible if the elements arrive in worst-case order.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746602"}, {"primary_key": "4503480", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation for Turing Machines with Unbounded Memory.", "authors": ["Venkata Koppula", "<PERSON>", "<PERSON>"], "summary": "We show how to build indistinguishability obfuscation (iO) for Turing Machines where the overhead is polynomial in the security parameter λ, machine description |M| and input size |x| (with only a negligible correctness error). In particular, we avoid growing polynomially with the maximum space of a computation. Our construction is based on iO for circuits, one way functions and injective pseudo random generators.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746614"}, {"primary_key": "4503481", "vector": [], "sparse_vector": [], "title": "Online Submodular Welfare Maximization: Greedy Beats 1/2 in Random Order.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Submodular Welfare Maximization (SWM) problem, the input consists of a set of n items, each of which must be allocated to one of m agents. Each agent l has a valuation function vl, where vl(S) denotes the welfare obtained by this agent if she receives the set of items S. The functions vl are all submodular; as is standard, we assume that they are monotone and vl(∅) = 0. The goal is to partition the items into m disjoint subsets S1, S2, ... Sm in order to maximize the social welfare, defined as ∑l = 1m vl(Sl). A simple greedy algorithm gives a 1/2-approximation to SWM in the offline setting, and this was the best known until <PERSON><PERSON><PERSON>'s recent (1-1/e)-approximation algorithm [34]. In this paper, we consider the online version of SWM. Here, items arrive one at a time in an online manner; when an item arrives, the algorithm must make an irrevocable decision about which agent to assign it to before seeing any subsequent items. This problem is motivated by applications to Internet advertising, where user ad impressions must be allocated to advertisers whose value is a submodular function of the set of users / impressions they receive. There are two natural models that differ in the order in which items arrive. In the fully adversarial setting, an adversary can construct an arbitrary / worst-case instance, as well as pick the order in which items arrive in order to minimize the algorithm's performance. In this setting, the 1/2-competitive greedy algorithm is the best possible. To improve on this, one must weaken the adversary slightly: In the random order model, the adversary can construct a worst-case set of items and valuations, but does not control the order in which the items arrive; instead, they are assumed to arrive in a random order. The random order model has been well studied for online SWM and various special cases, but the best known competitive ratio (even for several special cases) is 1/2 + 1/n [9,10], barely better than the ratio for the adversarial order. Obtaining a competitive ratio of 1/2 + Ω(1) for the random order model has been an important open problem for several years. We solve this open problem by demonstrating that the greedy algorithm has a competitive ratio of at least 0.505 for online SWM in the random order model. This is the first result showing a competitive ratio bounded above 1/2 in the random order model, even for special cases such as the weighted matching or budgeted allocation problems (without the so-called 'large capacity' assumptions). For special cases of submodular functions including weighted matching, weighted coverage functions and a broader class of \"second-order supermodular\" functions, we provide a different analysis that gives a competitive ratio of 0.51. We analyze the greedy algorithm using a factor-revealing linear program, bounding how the assignment of one item can decrease potential welfare from assigning future items. We also formulate a natural conjecture which, if true, would improve the competitive ratio of the greedy algorithm to at least 0.567.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746626"}, {"primary_key": "4503482", "vector": [], "sparse_vector": [], "title": "Almost Optimal Pseudorandom Generators for Spherical Caps: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Halfspaces or linear threshold functions are widely studied in complexity theory, learning theory and algorithm design. In this work we study the natural problem of constructing pseudorandom generators (PRGs) for halfspaces over the sphere, aka spherical caps, which besides being interesting and basic geometric objects, also arise frequently in the analysis of various randomized algorithms (e.g., randomized rounding). We give an explicit PRG which fools spherical caps within error ε and has an almost optimal seed-length of O(log n + log(1/ε) ⋅ log log(1/ε)). For an inverse-polynomially growing error ε, our generator has a seed-length optimal up to a factor of O( log log (n)). The most efficient PRG previously known (due to <PERSON> 2012) requires a seed-length of Ω(log3/2(n)) in this setting. We also obtain similar constructions to fool halfspaces with respect to the Gaussian distribution. Our construction and analysis are significantly different from previous works on PRGs for halfspaces and build on the iterative dimension reduction ideas of <PERSON> et. al. 2011 and <PERSON><PERSON> et. al. 2013, the classical moment problem from probability theory and explicit constructions of approximate orthogonal designs based on the seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2011 on expansion in Lie groups.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746611"}, {"primary_key": "4503483", "vector": [], "sparse_vector": [], "title": "The Power of Dynamic Distance Oracles: Efficient Dynamic Algorithms for the Steiner Tree.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we study the Steiner tree problem over a dynamic set of terminals. We consider the model where we are given an n-vertex graph G=(V,E,w) with positive real edge weights, and our goal is to maintain a tree which is a good approximation of the minimum Steiner tree spanning a terminal set S ⊆ V, which changes over time. The changes applied to the terminal set are either terminal additions (incremental scenario), terminal removals (decremental scenario), or both (fully dynamic scenario). Our task here is twofold. We want to support updates in sublinear o(n) time, and keep the approximation factor of the algorithm as small as possible.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746615"}, {"primary_key": "4503484", "vector": [], "sparse_vector": [], "title": "Time Lower Bounds for Nonadaptive Turnstile Streaming Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "We say a turnstile streaming algorithm is {\\em non-adaptive} if, during updates, the memory cells written and read depend only on the index being updated and random coins tossed at the beginning of the stream (and not on the memory contents of the algorithm). Memory cells read during queries may be decided upon adaptively. All known turnstile streaming algorithms in the literature, except a single recent example for a particular promise problem [7], are non-adaptive. In fact, even more specifically, they are all linear sketches. We prove the first non-trivial update time lower bounds for both randomized and deterministic turnstile streaming algorithms, which hold when the algorithms are non-adaptive. While there has been abundant success in proving space lower bounds, there have been no non-trivial turnstile update time lower bounds. Our lower bounds hold against classically studied problems such as heavy hitters, point query, entropy estimation, and moment estimation. In some cases of deterministic algorithms, our lower bounds nearly match known upper bounds.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746542"}, {"primary_key": "4503485", "vector": [], "sparse_vector": [], "title": "Hardness of Graph Pricing Through Generalized Max-Dicut.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "The Graph Pricing problem is among the fundamental problems whose approximability is not well-understood. While there is a simple combinatorial 1/4-approximation algorithm, the best hardness result remains at 1/2 assuming the Unique Games Conjecture (UGC). We show that it is NP-hard to approximate within a factor better than 1/4 under the UGC, so that the simple combinatorial algorithm might be the best possible. We also prove that for any ε > 0, there exists δ > 0 such that the integrality gap of nδ-rounds of the Sherali-<PERSON> hierarchy of linear programming for Graph Pricing is at most 1/4 + ε. This work is based on the effort to view the Graph Pricing problem as a Constraint Satisfaction Problem (CSP) simpler than the standard and complicated formulation. We propose the problem called Generalized Max-Dicut(T), which has a domain size T + 1 for every T ≥ 1. Generalized Max-Dicut(1) is well-known Max-Dicut. There is an approximation preserving reduction from Generalized Max-Dicut on directed acyclic graphs (DAGs) to Graph Pricing, and both our results are achieved through this reduction. Besides its connection to Graph Pricing, the hardness of Generalized Max-Dicut is interesting in its own right since in most arity two CSPs studied in the literature, SDP-based algorithms perform better than LP-based or combinatorial algorithms --- for this arity two CSP, a simple combinatorial algorithm does the best.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746549"}, {"primary_key": "4503486", "vector": [], "sparse_vector": [], "title": "Lower Bounds on the Size of Semidefinite Programming Relaxations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a method for proving lower bounds on the efficacy of semidefinite programming (SDP) relaxations for combinatorial problems. In particular, we show that the cut, TSP, and stable set polytopes on n-vertex graphs are not the linear image of the feasible region of any SDP (i.e., any spectrahedron) of dimension less than 2nδ, for some constant δ > 0. This result yields the first super-polynomial lower bounds on the semidefinite extension complexity of any explicit family of polytopes.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746599"}, {"primary_key": "4503487", "vector": [], "sparse_vector": [], "title": "Learning Arbitrary Statistical Mixtures of Discrete Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of learning from unlabeled samples very general statistical mixture models on large finite sets. Specifically, the model to be learned, mix, is a probability distribution over probability distributions p, where each such p is a probability distribution over [n] = {1,2,...,n}. When we sample from mix, we do not observe p directly, but only indirectly and in very noisy fashion, by sampling from [n] repeatedly, independently K times from the distribution p. The problem is to infer mix to high accuracy in transportation (earthmover) distance.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746584"}, {"primary_key": "4503488", "vector": [], "sparse_vector": [], "title": "FPTAS for #BIS with Degree Bounds on One Side.", "authors": ["Jingcheng Liu", "<PERSON><PERSON><PERSON>"], "summary": "Counting the number of independent sets for a bipartite graph (#BIS) plays a crucial role in the study of approximate counting. It has been conjectured that there is no fully polynomial-time (randomized) approximation scheme (FPTAS/FPRAS) for #BIS, and it was proved that the problem for instances with a maximum degree of 6 is already as hard as the general problem. In this paper, we obtain a surprising tractability result for a family of #BIS instances. We design a very simple deterministic fully polynomial-time approximation scheme (FPTAS) for #BIS when the maximum degree for one side is no larger than 5. There is no restriction for the degrees on the other side, which do not even have to be bounded by a constant. Previously, FPTAS was only known for instances with a maximum degree of 5 for both sides.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746598"}, {"primary_key": "4503489", "vector": [], "sparse_vector": [], "title": "Hypergraph Markov Operators, Eigenvalues and Approximation Algorithms.", "authors": ["<PERSON>"], "summary": "The celebrated <PERSON><PERSON><PERSON>'s Inequality [AM85,a86] establishes a bound on the expansion of a graph via its spectrum. This inequality is central to a rich spectral theory of graphs, based on studying the eigenvalues and eigenvectors of the adjacency matrix (and other related matrices) of graphs. It has remained open to define a suitable spectral model for hypergraphs whose spectra can be used to estimate various combinatorial properties of the hypergraph. In this paper we introduce a new hypergraph Laplacian operator generalizing the Laplacian matrix of graphs. Our operator can be viewed as the gradient operator applied to a certain natural quadratic form for hypergraphs. We show that various hypergraph parameters (for e.g. expansion, diameter, etc) can be bounded using this operator's eigenvalues. We study the heat diffusion process associated with this Laplacian operator, and bound its parameters in terms of its spectra. All our results are generalizations of the corresponding results for graphs.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746555"}, {"primary_key": "4503490", "vector": [], "sparse_vector": [], "title": "Improved Noisy Population Recovery, and Reverse <PERSON>ami-Beckner Inequality for Sparse Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The noisy population recovery problem is a basic statistical inference problem. Given an unknown distribution in {0,1}n with support of size k, and given access only to noisy samples from it, where each bit is flipped independently with probability (1-μ)/2, estimate the original probability up to an additive error of ε. We give an algorithm which solves this problem in time polynomial in (klog log k, n, 1/ε). This improves on the previous algorithm of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS 2012] which solves the problem in time polynomial in (klog k, n, 1/ε). Our main technical contribution, which facilitates the algorithm, is a new reverse <PERSON><PERSON><PERSON> inequality for the L1 norm of sparse functions.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746540"}, {"primary_key": "4503491", "vector": [], "sparse_vector": [], "title": "Sum-of-squares Lower Bounds for Planted Clique.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Finding cliques in random graphs and the closely related \"planted\" clique variant, where a clique of size k is planted in a random G(n,1/2) graph, have been the focus of substantial study in algorithm design. Despite much effort, the best known polynomial-time algorithms only solve the problem for k = Θ(√n). In this paper we study the complexity of the planted clique problem under algorithms from the Sum-Of-Squares hierarchy. We prove the first average case lower bound for this model: for almost all graphs in G(n,1/2), r rounds of the SOS hierarchy cannot find a planted k-clique unless k ≥ (√n/log n)1/rCr. Thus, for any constant number of rounds planted cliques of size no(1) cannot be found by this powerful class of algorithms. This is shown via an integrability gap for the natural formulation of maximum clique problem on random graphs for SOS and Lasserre hierarchies, which in turn follow from degree lower bounds for the Positivestellensatz proof system.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746600"}, {"primary_key": "4503492", "vector": [], "sparse_vector": [], "title": "Randomized Composable Core-sets for Distributed Submodular Maximization.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An effective technique for solving optimization problems over massive data sets is to partition the data into smaller pieces, solve the problem on each piece and compute a representative solution from it, and finally obtain a solution inside the union of the representative solutions for all pieces. This technique can be captured via the concept of composable core-sets, and has been recently applied to solve diversity maximization problems as well as several clustering problems [7,15,8]. However, for coverage and submodular maximization problems, impossibility bounds are known for this technique [15]. In this paper, we focus on efficient construction of a randomized variant of composable core-sets where the above idea is applied on a random clustering of the data. We employ this technique for the coverage, monotone and non-monotone submodular maximization problems. Our results significantly improve upon the hardness results for non-randomized core-sets, and imply improved results for submodular maximization in a distributed and streaming settings. The effectiveness of this technique has been confirmed empirically for several machine learning applications [22], and our proof provides a theoretical foundation to this idea.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746624"}, {"primary_key": "4503493", "vector": [], "sparse_vector": [], "title": "Super-resolution, Extremal Functions and the Condition Number of Vandermonde Matrices.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Super-resolution is a fundamental task in imaging, where the goal is to extract fine-grained structure from coarse-grained measurements. Here we are interested in a popular mathematical abstraction of this problem that has been widely studied in the statistics, signal processing and machine learning communities. We exactly resolve the threshold at which noisy super-resolution is possible. In particular, we establish a sharp phase transition for the relationship between the cutoff frequency (m) and the separation (Δ). If m > 1/Δ + 1, our estimator converges to the true values at an inverse polynomial rate in terms of the magnitude of the noise. And when m < (1-ε) /Δ no estimator can distinguish between a particular pair of Δ-separated signals even if the magnitude of the noise is exponentially small. Our results involve making novel connections between extremal functions and the spectral properties of Vandermonde matrices. We establish a sharp phase transition for their condition number which in turn allows us to give the first noise tolerance bounds for the matrix pencil method. Moreover we show that our methods can be interpreted as giving preconditioners for Vandermonde matrices, and we use this observation to design faster algorithms for super-resolution. We believe that these ideas may have other applications in designing faster algorithms for other basic tasks in signal processing.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746561"}, {"primary_key": "4503494", "vector": [], "sparse_vector": [], "title": "Consistency Thresholds for the Planted Bisection Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The planted bisection model is a random graph model in which the nodes are divided into two equal-sized communities and then edges are added randomly in a way that depends on the community membership. We establish necessary and sufficient conditions for the asymptotic recoverability of the planted bisection in this model. When the bisection is asymptotically recoverable, we give an efficient algorithm that successfully recovers it. We also show that the planted bisection is recoverable asymptotically if and only if with high probability every node belongs to the same community as the majority of its neighbors.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746603"}, {"primary_key": "4503495", "vector": [], "sparse_vector": [], "title": "Approximate k-flat Nearest Neighbor Search.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Let k ≥ 0 be an integer. In the approximate k-flat nearest neighbor (k-ANN) problem, we are given a set P ⊂ Rd of n points in d-dimensional space and a fixed approximation factor c > 1. Our goal is to preprocess P so that we can efficiently answer approximate k-flat nearest neighbor queries: given a k-flat F, find a point in P whose distance to F is within a factor c of the distance between F and the closest point in P. The case k = 0 corresponds to the well-studied approximate nearest neighbor problem, for which a plethora of results are known, both in low and high dimensions. The case k = 1 is called approximate line nearest neighbor. In this case, we are aware of only one provably efficient data structure, due to <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (AIKN) [2]. For k ≥ 2, we know of no previous results.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746559"}, {"primary_key": "4503496", "vector": [], "sparse_vector": [], "title": "Randomized Rounding for the Largest Simplex Problem.", "authors": ["Aleksan<PERSON>"], "summary": "The maximum volume j-simplex problem asks to compute the j-dimensional simplex of maximum volume inside the convex hull of a given set of n points in Qd. We give a deterministic approximation algorithm for this problem which achieves an approximation ratio of ej/2 + o(j). The problem is known to be NP-hard to approximate within a factor of cj for some constant c > 1. Our algorithm also gives a factor ej + o(j) approximation for the problem of finding the principal j x j submatrix of a rank d positive semidefinite matrix with the largest determinant. We achieve our approximation by rounding solutions to a generalization of the D-optimal design problem, or, equivalently, the dual of an appropriate smallest enclosing ellipsoid problem. Our arguments give a short and simple proof of a restricted invertibility principle for determinants.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746628"}, {"primary_key": "4503497", "vector": [], "sparse_vector": [], "title": "Quantum Spectrum Testing.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "In this work, we study the problem of testing properties of the spectrum of a mixed quantum state. Here one is given n copies of a mixed state ρ∈ Cd x d and the goal is to distinguish (with high probability) whether ρ's spectrum satisfies some property P or whether it is at least ε-far in l1-distance from satisfying P. This problem was promoted under the name of testing unitarily invariant properties of mixed states. It is the natural quantum analogue of the classical problem of testing symmetric properties of probability distributions.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746582"}, {"primary_key": "4503498", "vector": [], "sparse_vector": [], "title": "Inapproximability of Nash Equilibrium.", "authors": ["<PERSON>via<PERSON>"], "summary": "We prove that finding an ε-approximate Nash equilibrium is PPAD-complete for constant ε and a particularly simple class of games: polymatrix, degree 3 graphical games, in which each player has only two actions.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746578"}, {"primary_key": "4503499", "vector": [], "sparse_vector": [], "title": "Analysis of a Classical Matrix Preconditioning Algorithm.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study a classical iterative algorithm for the problem of balancing matrices in the L∞ norm via a scaling transformation. This algorithm, which goes back to <PERSON> and Parlett & Reinsch in the 1960s, is implemented as a standard preconditioner in many numerical linear algebra packages. Surprisingly, despite its widespread use over several decades, no bounds were known on its rate of convergence. In this paper we prove that, for a large class of irreducible n x n (real or complex) input matrices~$A$, a natural variant of the algorithm converges in O(n3 log(nρ/ε)) elementary balancing operations, where ρ measures the initial imbalance of A and ε is the target imbalance of the output matrix. (The imbalance of A is maxi |log(aiout/aiin)|, where aiout,aiin are the maximum entries in magnitude in the ith row and column respectively.) This bound is tight up to the log n factor. A balancing operation scales the ith row and column so that their maximum entries are equal, and requires O(m/n) arithmetic operations on average, where m is the number of non-zero elements in A. Thus the running time of the iterative algorithm is ~O(n2m). This is the first time bound of any kind on any variant of the <PERSON>-<PERSON><PERSON><PERSON> algorithm. The class of matrices for which the above analysis holds are those which satisfy a condition we call Unique Balance, meaning that the limit of the iterative balancing process does not depend on the order in which balancing operations are performed. We also prove a combinatorial characterization of the Unique Balance property, which had earlier been conjectured by <PERSON>.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746556"}, {"primary_key": "4503500", "vector": [], "sparse_vector": [], "title": "Faster Canonical Forms for Primitive Coherent Configurations: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Primitive coherent configurations (PCCs) are edge-colored digraphs that generalize strongly regular graphs (SRGs), a class perceived as difficult for Graph Isomorphism (GI). Moreover, PCCs arise naturally as obstacles to combinatorial divide-and-conquer approaches for general GI. In a natural sense, the isomorphism problem for PCCs is a stepping stone between SRGs and general GI. In his 1981 paper in the Annals of Math., <PERSON><PERSON> proposed a combinatorial approach to GI testing via an analysis of the standard individualization/refinement (I/R) technique and proved that I/R yields canonical forms of PCCs in time exp(~O(n1/2)). (The tilde hides polylogarithmic factors.) We improve this bound to exp(~O(n1/3)). This is faster than the current best bound, exp(~O(n1/2)), for general GI, and subsumes <PERSON><PERSON><PERSON>'s exp(~O(n1/3)) bound for SRGs (STOC'96, only recently improved to exp(~O(n1/5)) by the present authors and their coauthors (FOCS'13)).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746617"}, {"primary_key": "4503501", "vector": [], "sparse_vector": [], "title": "Quantum Information Complexity.", "authors": ["<PERSON>"], "summary": "We define a new notion of information cost for quantum protocols, and a corresponding notion of quantum information complexity for bipartite quantum tasks. These are the fully quantum generalizations of the analogous quantities for bipartite classical tasks that have found many applications recently, in particular for proving communication complexity lower bounds and direct sum theorems. Finding such a quantum generalization of information complexity was one of the open problems recently raised by <PERSON><PERSON> (STOC'12).", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746613"}, {"primary_key": "4503502", "vector": [], "sparse_vector": [], "title": "Spectral Sparsification and Regret Minimization Beyond Matrix Multiplicative Updates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we provide a novel construction of the linear-sized spectral sparsifiers of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [11]. While previous constructions required Ω(n4) running time [11, 45], our sparsification routine can be implemented in almost-quadratic running time O(n2+ε). The fundamental conceptual novelty of our work is the leveraging of a strong connection between sparsification and a regret minimization problem over density matrices. This connection was known to provide an interpretation of the randomized sparsifiers of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [39] via the application of matrix multiplicative weight updates (MWU) [17, 43]. In this paper, we explain how matrix MWU naturally arises as an instance of the Follow-the-Regularized-Leader framework and generalize this approach to yield a larger class of updates. This new class allows us to accelerate the construction of linear-sized spectral sparsifiers, and give novel insights on the motivation behind <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [11].", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746610"}, {"primary_key": "4503503", "vector": [], "sparse_vector": [], "title": "Nearly-Linear Time Positive LP Solver with Faster Convergence Rate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Positive linear programs (LP), also known as packing and covering linear programs, are an important class of problems that bridges computer science, operation research, and optimization. Efficient algorithms for solving such LPs have received significant attention in the past 20 years [2, 3, 4, 6, 7, 9, 11, 15, 16, 18, 19, 21, 24, 25, 26, 29, 30]. Unfortunately, all known nearly-linear time algorithms for producing (1+ε)-approximate solutions to positive LPs have a running time dependence that is at least proportional to ε-2. This is also known as an O(1/√T) convergence rate and is particularly poor in many applications. In this paper, we leverage insights from optimization theory to break this longstanding barrier. Our algorithms solve the packing LP in time ~O(N ε-1) and the covering LP in time ~O(N ε-1.5). At high level, they can be described as linear couplings of several first-order descent steps. This is the first application of our linear coupling technique (see [1]) to problems that are not amenable to blackbox applications known iterative algorithms in convex optimization. Our work also introduces a sequence of new techniques, including the stochastic and the non-symmetric execution of gradient truncation operations, which may be of independent interest.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2746539.2746573"}, {"primary_key": "4521179", "vector": [], "sparse_vector": [], "title": "Proceedings of the Forty-Seventh Annual ACM on Symposium on Theory of Computing, STOC 2015, Portland, OR, USA, June 14-17, 2015", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The papers in this volume were presented at the Forty-Seventh Annual ACM Symposium on Theory of Computing (STOC 2015), held as part of the Federated Computing Research Conference in Portland, Oregon, June 15-June 17, 2015. The Symposium was sponsored by the ACM Special Interest Group on Algorithms and Computation Theory (SIGACT). On June 14, the day before STOC, there was a program of workshops and tutorials organized by <PERSON> and <PERSON><PERSON><PERSON>. The workshop was on \"Algorithmic Frontiers of Modern Massively Parallel Computation\"; the tutorials were on \"Hardness and Equivalences for Problems in P\" and \"Sampling and Volume Computation in High Dimension\". In response to a Call for Papers, 347 submissions were received by the submission deadline of November 4, 2014, 3:59PM EST. The Program Committee began its deliberations electronically on December 22, 2014 and continued in that medium until its meeting at MIT in Cambridge, MA on January 30 - February 1, 2015, where final decisions were made. All 26 Program Committee members attended the Program Committee meeting. The Program Committee selected 93 papers for presentation. The submissions were not refereed, and many of these papers represent reports of continuing research. It is expected that most of them will appear in a more polished and complete form in scientific journals. The Program Committee would like to thank all authors who submitted papers for consideration. From among many excellent candidates, the papers \"Exponential Separation of Information and Communication for Boolean Function\", by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, \"2-Server PIR with sub-polynomial communication\" by <PERSON><PERSON><PERSON> and <PERSON>vakanth Gopi, and \"Lower bounds on the size of semidefinite programming relaxations\" by James Lee, Prasad Raghavendra and David Steurer, were selected for the STOC Best Paper Award. The paper \"Inapproximability of Nash Equilibrium\", by Aviad Rubinstein, was selected for the Danny Lewin Best Student Paper Award.", "published": "2015-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": ""}]