import os
import sys

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import re
import time
import asyncio
from utils.llm import BaseLLM, ModelSelector, BasePrompt, BaseEmbedding, BaseZilliz, BaseRerank
from utils.base import write_search_log
from utils.config import config

from service.translate import YoudaoTranslator

# 连接 Milvus
client_bge = BaseZilliz(**ModelSelector.zilliz_bge)
client_pub = BaseZilliz(**ModelSelector.zilliz_pub)

# 初始化 Embedding 模型
embeddings_bge = BaseEmbedding(**ModelSelector.sc_bge_m3)

# 初始化翻译器
translator = YoudaoTranslator()

class ScoreTools:
    @staticmethod
    def get_matching_prompt():
        query_matching_score_cn = '''
            你是一名学术文献评估专家，需要根据用户提供的查询主题 <Query> 和 待匹配论文 <Results>，严格评估两者的语义关联度。请按以下规则输出评分：

            **评分规则**
            1. 10分制（整数），6分为及格线，主要衡量论文是否符合用户检索目标，若不在评分维度内但与用户检索目标相关，可酌情打6分以上
            
            2. 评分维度：
            - (1) 主题相关性（符合检索主题的核心关键词）重要性：100%
            - (2) 语义相关性（与检索主题有语义关联）重要性：60%
            - (3) 技术关联性（和检索主题有技术关联性）重要性：30%

            3. 评分标准（6分以上请严格核查，不要随意打高分）
            - 9-10分：完美符合评分维度(1)(2)
            - 7-8分：符合评分维度(1)，且部分符合评分维度(2)
            - 6分：部分符合评分维度(1)
            - 3-5分：部分符合评分维度(2)(3)
            - 1-2分：在评分维度上基本无关
            - 0分：无任何关联

            **输出要求**
            1. 仅返回Python列表格式的分数：[x, x, x, ...]
            2. 严格保持与results相同的顺序
            3. 不包含任何解释性文字
        '''
        return query_matching_score_cn

    @staticmethod
    def get_strict_matching_prompt():
        system_prompt = '''
            你是一名学术文献评估专家，需要根据用户提供的查询主题 和 待匹配论文，严格评估两者的语义关联度。
        '''
        user_prompt = '''

        # 查询主题为：
        {Query}

        # 待匹配论文为：
        {Results}

        # 请按以下规则输出评分：
            **评分规则**
            1. 10分制（整数），6分为及格线，主要衡量论文是否符合用户检索目标，若不在评分维度内但与用户检索目标相关，可酌情打6分以上
            
            2. 评分维度：
            - (1) 主题相关性（符合检索主题的核心关键词）重要性：100%
            - (2) 语义相关性（与检索主题有语义关联）重要性：60%
            - (3) 技术关联性（和检索主题有技术关联性）重要性：30%

            3. 评分标准（6分以上请严格核查，不要随意打高分）
            - 9-10分：完美符合评分维度(1)(2)
            - 7-8分：符合评分维度(1)，且部分符合评分维度(2)
            - 6分：部分符合评分维度(1)
            - 3-5分：部分符合评分维度(2)(3)
            - 1-2分：在评分维度上基本无关
            - 0分：无任何关联

            **输出要求**
            1. 仅返回列表格式的分数：[x, x, x, ...]
            2. 严格保持与results相同的顺序，并且和 results 数量一致，即一共有 {Number} 个分数
            3. 不包含任何解释性文字，首个字符和最终字符为中括号
        '''
        return system_prompt, user_prompt

    @staticmethod
    async def get_scores(model: BaseLLM, query: str, results: list[str], default_score=6, match_limit=4):
        '''
            单个模型 对一个 query - results 对的评分，只需 ask 单次
        Args:
            model: 模型名称
            query: 查询字符串
            results: 该 query 的搜索结果列表
            default_score: 正则匹配出错时的默认分数
            limit: 单词匹配的 results 数量（防止过多导致上下文过长）
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # 1. 定义子函数，用于对每个切分后的 results 进行评分
        async def sub_scores(query: str, matchs: list[str]):
            try:
                model_curr = model.__copy__()
                model_curr.api_key = config.get_sc_key(have_balance=not model.is_free)

                # 1. 构建 prompt
                matchs_str = [f"{i+1}. {out}\n" for i, out in enumerate(matchs)]
                matchs_str = ''.join(matchs_str)
                prompt = f"Query: {query} \n Results: {matchs_str} \n\n Please output scores list:"

                # 2. 评分，添加超时控制
                try:
                    result = await asyncio.wait_for(
                        model_curr.ask_async(
                            prompt, 
                            system_prompt=ScoreTools.get_matching_prompt(), 
                            temperature=0,  # 降低温度以获得更稳定的结果
                            max_tokens=15,    # 限制输出长度
                            top_k=1,
                        ),
                        timeout=3.5
                    )
                except asyncio.TimeoutError:
                    print(f"timeout: {model_curr.name} 响应超时，打默认分数")
                    return [default_score] * len(matchs)

                # 3. 提取分数
                result = re.search(r'\[([\d,\s]+)\]', result)
                if not result:
                    # 提取错误 -> 打默认分数
                    print(f"re failed: {model_curr.name} 正则匹配 scores 失败，打默认分数")
                    return [default_score] * len(matchs)
                else:
                    # 确保分数顺序与原始结果顺序一致
                    numbers = [int(x.strip()) for x in result.group(1).split(',')]
                    if len(numbers) != len(matchs):
                        # 分数数量与结果数量不一致 -> 打默认分数
                        print(f"number failed: {model_curr.name} scores 数量与 results 数量不一致，打默认分数")
                        return [default_score] * len(matchs)
                    return numbers
                
            except Exception as e:
                # 其他错误 -> 打默认分数
                print(f"other failed: {model_curr.name} 打分出错，打默认分数")
                print(f"error: {e}")
                return [default_score] * len(matchs)

        # 2. 按照 limit 分页，并逐个分发进行评分
        tasks = [sub_scores(query, results[i:i+match_limit]) for i in range(0, len(results), match_limit)]
        scores_list = await asyncio.gather(*tasks)
        scores = [score for sub_scores in scores_list for score in sub_scores]

        return scores

    @staticmethod
    async def get_strict_scores(model: BaseLLM, query: str, results: list[str], default_score=6, match_limit=4) -> list[int]:
        '''
            单个模型 对一个 query - results 对的严格格式评分，防止出现格式错误的情况
        Args:
            model: 模型实例
            query: 查询字符串
            results: 该 query 的搜索结果列表
            default_score: 正则匹配出错时的默认分数
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # 1. 定义子函数，用于对每个切分后的 results 进行严格评分
        async def sub_scores(query: str, matchs: list[str]):
            try:
                model_curr = model.__copy__()
                model_curr.api_key = config.get_sc_key(have_balance=not model.is_free)

                # 1. 构建 prompt
                matchs_str = [f"{i+1}. {out}\n" for i, out in enumerate(matchs)]
                matchs_str = ''.join(matchs_str)
                
                # 2. 获取严格评分的 prompt
                system_prompt, user_prompt = ScoreTools.get_strict_matching_prompt()
                formatted_prompt = user_prompt.format(Query=query, Results=matchs_str, Number=len(matchs))

                # 3. 评分，添加超时控制
                try:
                    result = await asyncio.wait_for(
                        model_curr.ask_async(
                            formatted_prompt, 
                            system_prompt=system_prompt, 
                            temperature=0,  # 降低温度以获得更稳定的结果
                            max_tokens=25,    # 限制输出长度
                            top_k=1,
                        ),
                        timeout=3.5
                    )
                except asyncio.TimeoutError:
                    print(f"timeout: {model_curr.name} 严格评分响应超时，打默认分数")
                    return [default_score] * len(matchs)

                # 3. 提取分数
                old_result = result
                result = re.search(r'\[([\d,\s]+)\]', result)
                if not result:
                    # 提取错误 -> 打默认分数
                    print(f"re failed: {model_curr.name} 正则匹配 scores 失败 {old_result}，打默认分数")
                    return [default_score] * len(matchs)
                else:
                    # 确保分数顺序与原始结果顺序一致
                    numbers = [int(x.strip()) for x in result.group(1).split(',')]
                    if len(numbers) != len(matchs):
                        # 分数数量与结果数量不一致 -> 打默认分数
                        print(f"number failed: {model_curr.name} scores 数量与 results 数量不一致，打默认分数")
                        return [default_score] * len(matchs)
                    return numbers
                
            except Exception as e:
                # 其他错误 -> 打默认分数
                print(f"other failed: {model_curr.name} 严格评分出错，打默认分数")
                print(f"error: {e}")
                return [default_score] * len(matchs)

        # 2. 按照 limit 分页，并逐个分发进行评分
        tasks = [sub_scores(query, results[i:i+match_limit]) for i in range(0, len(results), match_limit)]
        scores_list = await asyncio.gather(*tasks)
        scores = [score for sub_scores in scores_list for score in sub_scores]

        return scores

    @staticmethod
    async def get_scores_by_models(models: list[BaseLLM], query: str, results: list[str], default_score=6, match_limit=4) -> list[int]:
        ''' 
            多个模型 对一个 query - results 对的评分，并发 ask 多次
        Args:
            models: 模型列表
            query: 搜索关键词
            results: 搜索结果
            default_score: 正则匹配出错时的默认分数
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # # 1. 多模型并行获取 scores
        # for model in models:
        #     model.api_key = config.get_sc_key()

        # 1. 多模型并行获取 scores
        # tasks = [ScoreTools.get_strict_scores(model, query, results, default_score, match_limit) for model in models]
        tasks = [ScoreTools.get_scores(model, query, results, default_score, match_limit) for model in models]
        scores_list = await asyncio.gather(*tasks)

        # 2. 计算平均分
        scores = [sum(score) / len(score) for score in zip(*scores_list)]

        return scores

async def semantic_rerank(query: str, doc_list: list, top_n: int=64):
    """根据 query 和 doc_list，返回 rerank 后 doc_list，测试效果不佳
    Args:
        query (str): 用户输入的搜索关键词或语句。
        doc_list (list): 文档列表。
        top_n (int, optional): 返回结果数量。默认为64。
    Returns:
        list: 排序后的文档列表，每个文档包含原始内容和相关性分数。
    """

    # 1. 初始化 rerank 模型
    rerank_model = BaseRerank(**ModelSelector.bge_rerank)

    # 2. 进行 rerank
    # tmp_doc_list = [f"{d['title']} || {d['sub_summary']}" for d in doc_list]
    tmp_doc_list = [f"{d['title']}" for d in doc_list]
    rerank_results = rerank_model.rerank(query, tmp_doc_list, top_n)
    
    # 3. 根据 rerank 结果重新组织文档列表
    reranked_docs = []
    for result in rerank_results:
        index = result['index']
        score = result['score']
        doc = doc_list[index].copy() if isinstance(doc_list[index], dict) else doc_list[index]
        if isinstance(doc, dict):
            doc['score'] = score
        reranked_docs.append(doc)

    # 4. 按照 score 排序
    reranked_docs = sorted(reranked_docs, key=lambda x: x['score'], reverse=True)
    
    return reranked_docs

async def semantic_search(query: str, page: int = 1, limit: int = 12, years: str = '',
                          source: list[str] = ['arxiv'],
                          output_fields: list[str] = ["title", "authors", "summary", "published", "source", "pdf_url"],
                          translate: bool = True):
    """根据搜索内容返回语义搜索结果。
    Args:
        query (str): 用户输入的搜索关键词或语句。
        page (int, optional): 分页页码，从1开始。默认为1。
        limit (int, optional): 每页返回的结果数量。默认为12。
        years (str, optional): 限定年份范围（如 "2020,2021,2023"）。默认为空字符串。
        source (list[str], optional): 数据源。默认为 ['arxiv']。
        output_fields (list[str], optional): 指定返回结果的字段列表。默认为常见字段。
    Returns:
        list[dict]: 搜索结果列表，每个结果包含字段和相似度分数。示例：
            [
                {
                    "title": "论文标题",
                    "authors": ["作者1", "作者2"],
                    "summary": "摘要文本...",
                    "published": "2023-01-01",
                    "pdf_url": "https://example.com/paper.pdf",
                    "distance": 0.123  # 相似度分数（越小越相关）
                },
                ...
            ]
    Raises:
        ValueError: 如果 `query` 为空或 `page/limit` 为负数。
    """


    # 1. 翻译
    start_time = time.time()
    query_en = ''
    if translate:
        query_en = translator.translate(query)
        if not query_en:
            # 如果翻译失败，使用原始查询
            print(f"翻译失败，使用原始查询: {query}")
            query_en = query
    trans_time = time.time() - start_time

    # 2. 构建年份 + 来源信息
    years = [y.strip() for y in years.split(',')]
    years_filter = ' or '.join(f'published like "{y}%"' for y in years) if years else ''
    source_filter = ' or '.join(f'source == "{s}"' for s in source)
    print(f"years_filter: {years_filter}, source_filter: {source_filter}")

    # 组合过滤条件
    if years_filter:
        filter = f'({years_filter}) and ({source_filter})'
    else:
        filter = source_filter
    print(f"filter: {filter}")
    
    # 3. 获取 embeding，并进行超时检测
    start_time = time.time()
    vector = await embeddings_bge.aembedding([query + ' - ' + query_en], vector_length=1024, timeout=10)
    embed_time = time.time() - start_time
    
    # 4. 进行语义匹配
    start_time = time.time()
    client = client_bge if (len(source)==1 and source[0] == 'arxiv') else client_pub
    results = client.search(data=vector[0],
                            output_fields=output_fields,
                            limit=limit,
                            offset=(page - 1) * limit,
                            filter=filter,
                            # search_params={
                            #     "params": {
                            #         # 精准度控制，越大越精准，但是时间消耗越多（1-10，默认 1）
                            #         "level": 6, 
                            #     }
                            # }
                            )
    search_time = time.time() - start_time

    # 5. 添加日志
    write_search_log(f"翻译：{query} |==>| {query_en}\nstep1 总耗时: {trans_time + embed_time + search_time:.2f}s || 翻译：{trans_time:.2f}s, 向量化：{embed_time:.2f}s, 搜索：{search_time:.2f}s")

    # 6. 结构化搜索结果（云端）
    out_contents = []
    for hit in results:
        # 直接访问 entity 字典中的字段
        entity = hit.get('entity', {})
        content = {}
        for field in output_fields:
            if field == 'authors':
                content[field] = list(entity.get(field, []))
            else:
                content[field] = str(entity.get(field, ''))
        # 添加相似度分数
        content['distance'] = float(hit.get('distance', 0.0))
        out_contents.append(content)

    # 按相似度分数从小到大排序（distance 越小表示越相似）
    out_contents = sorted(out_contents, key=lambda x: x['distance'])

    return out_contents

async def deep_semantic_search(query: str,
                               page: int = 1,
                               limit: int = 12,
                               years: str = '',
                               match_limit: int = 2,
                               source: list[str] = ['arxiv'],
                               use_sub_summary: bool = True,
                               output_fields: list[str] = ["title", "authors", "summary", "published", "source", "pdf_url"]):
    """根据搜索内容，返回语义搜索结果（使用 llm 打分以进行深度搜索）
    Args:
        query (str): 用户输入的搜索关键词或语句。
        page (int, optional): 分页页码，从1开始。默认为1。
        limit (int, optional): 每页返回的结果数量。默认为12。
        years (str, optional): 限定年份范围（如 "2020-2023"）。默认为空字符串。
        match_limit (int, optional): 每次 query 匹配的 result 数量。越小效果越好，但会增加请求次数，提高 token 用量。默认为4。
        use_sub_summary (bool, optional): 是否使用 sub_summary 进行搜索。默认为True。
    Returns:
        list[dict]: 搜索结果列表，每个结果包含字段和相似度分数。示例：
            [
                {
                    "title": "论文标题",
                    "authors": ["作者1", "作者2"],
                    "summary": "摘要文本...",
                    "published": "2023-01-01",
                    "pdf_url": "https://example.com/paper.pdf",
                    "distance": 0.123  # 相似度分数（越小越相关）
                },
                ...
            ]
    Raises:
        ValueError: 如果 `query` 为空或 `page/limit` 为负数。
    """
    print("source:", source)

    # 1. 通过语义搜索获取初步结果
    output_fields = ["title", "authors", "summary", "published", "source", "pdf_url"]
    if use_sub_summary:
        output_fields.append("sub_summary")

    start_time = time.time()
    results = await semantic_search(query, page, limit, years, source, output_fields)
    search_time = time.time() - start_time

    # 2. 进行 rerank
    # start_time = time.time()
    # results = await semantic_rerank(query, results, top_n=limit * 2)
    # rerank_time = time.time() - start_time

    # 3. 初始化打分模型
    start_time = time.time()
    # models = [ModelSelector.qwen25_32b,
    #     # ModelSelector.qwen25_coder_32b,
    #           ModelSelector.glm4_32b,]
    models = [
            #   ModelSelector.intern_7b,
            #   ModelSelector.qwen25_coder_7b,
              ModelSelector.glm4_9b,
              ModelSelector.glm4_9b_0414,
              ModelSelector.qwen25_14b
              ]
    models = [BaseLLM(**m, log=False) for m in models]

    # 3. 初始化打分对象
    if use_sub_summary:
        matchs = [f"{result['title']} || {result['sub_summary']}" for result in results]
    else:
        matchs = [f"{result['title']}" for result in results]

    # 4. 进行打分
    scores = await ScoreTools.get_scores_by_models(models, query, matchs, match_limit=match_limit)
    score_time = time.time() - start_time

    # 5. 合并结果并排序
    for result, score in zip(results, scores):
        result['score'] = score
    # write_search_log(f"step2 总耗时: {search_time + rerank_time + score_time:.2f}s || 搜索：{search_time:.2f}s, 重排：{rerank_time:.2f}s, 打分：{score_time:.2f}s")
    write_search_log(f"step2 总耗时: {search_time + score_time:.2f}s || 搜索：{search_time:.2f}s, 打分：{score_time:.2f}s")

    # 6. 设置 distance 字段为 score 的数值的倒数
    for result in results:
        if result['score'] != 0: # 避免 score 为 0 时，distance 为无穷大
            result['distance'] = 1 / result['score']
        else:
            result['distance'] = 1

    return results

async def abstract_refinement(querys: list[str]):
    """根据内容，直接返回摘要精炼结果（批量）
    
    Args:
        querys: 需要处理的查询列表
        model_num: 使用的模型数量，默认为2
    """
    if len(querys) == 0:
        return []

    # 1. 使用随机 key 初始化模型
    models = [BaseLLM(**ModelSelector.qwen25_14b, log=False) for _ in range(len(querys))]
    
    # 2. 初始化付费 key
    for model in models:
        model.api_key = config.get_sc_key(have_balance=not model.is_free)

    # 3. 进行打分
    tasks = [model.ask_async(query, system_prompt=BasePrompt.abstract_refinement_base) for query, model in zip(querys, models)]
    responses = await asyncio.gather(*tasks)

    return responses

if __name__ == "__main__":
    llm_sm = BaseLLM(**ModelSelector.glm4_flash)

    # 1. 深度搜索
    query = "使用 agent 进行文献综述合成"
    # results = asyncio.run(semantic_search(query, limit=128, source=['acl'], output_fields=["title", "authors", "summary", "published", "source", "pdf_url"]))
    results = asyncio.run(deep_semantic_search(query, limit=166, match_limit=4, source=['arxiv'], use_sub_summary=False))
    results = [result for result in results if result['score'] > 6]
    results = results[0:32]

    # 2. 翻译标题
    start_time = time.time()
    titles = [out['title'] for out in results]
    titles_cn = asyncio.run(llm_sm.ask_multi_async(titles, system_prompt=BasePrompt.simple_translate_2cn, temperature=0.2))
    for cn, out in zip(titles_cn, results):
        out['title_cn'] = cn
    print(f"翻译耗时: {time.time() - start_time:.2f}s")

    # 3. 打印结果
    for i, result in enumerate(results):
        print(f"{i+1}. {result['title_cn'][0:60]} - {result['source']}")