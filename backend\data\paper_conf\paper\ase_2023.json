[{"primary_key": "1219972", "vector": [], "sparse_vector": [], "title": "Government Mobile Apps: Analysing Citizen Feedback via App Reviews.", "authors": ["<PERSON><PERSON>", "<PERSON>", "M. <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Governments worldwide are increasingly embracing digital transformation initiatives to enhance service delivery, engage citizens, and achieve better outcomes. However, obtaining continuous feedback on these initiatives poses a substantial challenge. This paper investigates the feasibility of leveraging mobile app reviews as a valuable source of citizen feedback on government digital services. We analyse 100,146 app reviews from 129 government mobile apps in Australia and identify several functional and usability issues. These include issues such as app instability, complexity, integration problems, navigation difficulties, inaccuracies, and challenges with ID verification and authentication processes. Furthermore, we uncover several factors that influence user satisfaction, including accuracy and reliability, convenience, dependability, user-centric design, and overall user-friendliness. These findings demonstrate a strong correlation between user feedback and the government's digital transformation strategy, underscoring the viability of mobile app reviews as a cost-effective avenue for collecting citizen feedback.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00190"}, {"primary_key": "1219974", "vector": [], "sparse_vector": [], "title": "Better Patching Using LLM Prompting, via Self-Consistency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large Language models (LLMs) can be induced to solve non-trivial problems with \"few-shot\" prompts including illustrative problem-solution examples. Now if the few-shots also include \"chain of thought\" ( $\\mathcal{C}oT$ ) explanations, which are of the form problem-explanation-solution, LLMs will generate a \"explained\" solution, and perform even better. Recently an exciting, substantially better technique, self-consistency [1] ( $\\mathcal{S}-C$ ) has emerged, based on the intuition that there are many plausible explanations for the right solution; when the LLM is sampled repeatedly to generate a pool of explanation-solution pairs, for a given problem, the most frequently occurring solutions in the pool (ignoring the explanations) tend to be even more likely to be correct! Unfortunately, the use of this highly-performant $\\mathcal{S}-C$ (or even $\\mathcal{C}oT$ ) approach in software engineering settings is hampered by the lack of explanations; most software datasets lack explanations. In this paper, we describe an application of the $\\mathcal{S}-C$ approach to program repair, using the commit log on the fix as the explanation, only in the illustrative few-shots. We achieve state-of-the art results, beating previous approaches to prompting-based program repair, on the MODIT dataset; we also find evidence suggesting that the correct commit messages are helping the LLM learn to produce better patches.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00065"}, {"primary_key": "1219977", "vector": [], "sparse_vector": [], "title": "Systematically Detecting Packet Validation Vulnerabilities in Embedded Network Stacks.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Embedded Network Stacks (ENS) enable low-resource devices to communicate with the outside world, facilitating the development of Internet of Things and Cyber-Physical Systems. Some defects in ENS are thus high-severity cybersecurity vulnerabilities: they are remotely triggerable and can impact the physical world. While prior research has shed light on the characteristics of defects in many classes of software systems, no study has described the properties of ENS defects nor identified a systematic technique to expose them. The most common automated approach to detecting ENS defects is feedback-driven randomized dynamic analysis (\"fuzzing\"), a costly and unpredictable technique. This paper provides the first systematic characterization of cybersecurity vulnerabilities in ENS. We analyzed 61 vulnerabilities across 6 open-source ENS. Most of these ENS defects are concentrated in the transport and network layers of the network stack, require reaching different states in the network protocol, and can be triggered by only 1–2 modifications to a single packet. We therefore propose a novel systematic testing framework that focuses on the transport and network layers, uses seeds that cover a network protocol's states, and systematically modifies packet fields. We evaluate this framework on 4 ENS and replicated 12 of the 14 reported IP/TCP/UDP vulnerabilities. On recent versions of these ENSs, it discovered 7 novel defects (6 assigned CVES) during a bounded systematic test that covered all protocol states and made up to 3 modifications per packet. We found defects in 3 of the 4 ENS we tested that had not been found by prior fuzzing research. Our results suggest that fuzzing should be deferred until after systematic testing is employed.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00095"}, {"primary_key": "1219978", "vector": [], "sparse_vector": [], "title": "BUGSC++: A Highly Usable Real World Defect Benchmark for C/C++.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yung<PERSON> Choi", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As software systems grow larger and more complex, debugging takes up an increasingly significant portion of developers' time and efforts during software maintenance. To aid software engineers in debugging, many automated debugging and repair techniques have been proposed. Both the development and evaluation of these automated techniques depend on benchmarks of bugs. While many different defect benchmarks have been developed, only a few benchmarks are widely used due to the origin of the collected bugs as well as the usability of the benchmarks themselves, risking a biased research landscape. This paper presents BUGSC++, a new benchmark that contains 209 real-world bugs collected from 22 open-source C/C++ projects. BugsC++ aims to provide high usability by providing a similar user interface to the widely used Defects4J. Further, BugsC++ ensures the replicability of the bugs in its collection by encapsulating each buggy program in a Docker container. By providing a highly usable real-world defect benchmark for C/C++, we hope to promote debugging research for C/C++.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00208"}, {"primary_key": "1219979", "vector": [], "sparse_vector": [], "title": "SmartBugs 2.0: An Execution Framework for Weakness Detection in Ethereum Smart Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart contracts are blockchain programs that often handle valuable assets. Writing secure smart contracts is far from trivial, and any vulnerability may lead to significant financial losses. To support developers in identifying and eliminating vulnerabilities, methods and tools for the automated analysis of smart contracts have been proposed. However, the lack of commonly accepted benchmark suites and performance metrics makes it difficult to compare and evaluate such tools. Moreover, the tools are heterogeneous in their interfaces and reports as well as their runtime requirements, and installing several tools is time-consuming. In this paper, we present SmartBugs 2.0, a modular execution framework. It provides a uniform interface to 19 tools aimed at smart contract analysis and accepts both Solidity source code and EVM bytecode as input. After describing its architecture, we highlight the features of the framework. We evaluate the framework via its reception by the community and illustrate its scalability by describing its role in a study involving 3.25 million analyses.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00060"}, {"primary_key": "1219983", "vector": [], "sparse_vector": [], "title": "Minecraft: Automated Mining of Software Bug Fixes with Precise Code Context.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Shouvick Mondal"], "summary": "Repository mining of bug fixes from version control systems like GitHub is a challenging problem as far as the precision of the bug context is concerned, i.e., source codes immediately preceding and succeeding the fix location. Coupled with this, identification of the type of the bug fix goes a long way towards creating high quality datasets that can be used for several downstream tasks. However, existing bug fix datasets suffer from the following limitations that dilute the data quality. Firstly, they do not focus on multilingual projects in their entirety given that most open-source projects are now multilingual. Secondly, the granularity of the bug fixes are considered only at the function/method level without specifying line/statement level information. Thirdly, bug fixes lying within the scope of a source file but outside any of its constituent functions have not been examined. In this paper, we propose a solution to overcome the aforementioned limitations by introducing a novel and extensive dataset named Minecraft. With a size of 28.8GB (considering 416 GitHub projects encompassing programming languages such as C, C++, Java, and Python, 2.2M commits, 3.29M bug-fix pairs), Minecraft surpasses the existing datasets by 4-fold enlargement in terms of data availability. We believe Minecraft would serve as a valuable resource for various stakeholders in the software development and research communities, empowering them to improve software quality, develop innovative bug detection and auto-fix techniques, and advance the field of software engineering.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00116"}, {"primary_key": "1219984", "vector": [], "sparse_vector": [], "title": "Modeling Programmer Attention as <PERSON><PERSON><PERSON> Pre<PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "This paper launches a new effort at modeling programmer attention by predicting eye movement scanpaths. Programmer attention refers to what information people intake when performing programming tasks. Models of programmer attention refer to machine prediction of what information is important to people. Models of programmer attention are important because they help researchers build better interfaces, assistive technologies, and more human-like AI. For many years, researchers in SE have built these models based on features such as mouse clicks, key logging, and IDE interactions. Yet the holy grail in this area is scanpath prediction - the prediction of the sequence of eye fixations a person would take over a visual stimulus. A person's eye movements are considered the most concrete evidence that a person is taking in a piece of information. Scanpath prediction is a notoriously difficult problem, but we believe that the emergence of lower-cost, higheraccuracy eye tracking equipment and better large language models of source code brings a solution within grasp. We present an eye tracking experiment with 27 programmers and a prototype scanpath predictor to present preliminary results and obtain early community feedback.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00092"}, {"primary_key": "1219985", "vector": [], "sparse_vector": [], "title": "Provengo: A Tool Suite for Scenario Driven Model-Based Testing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Provengo, a comprehensive suite of tools designed to facilitate the implementation of Scenario-Driven Model-Based Testing (SDMBT), an innovative approach that utilizes scenarios to construct a model encompassing the user's perspective and the system's business value while also defining the desired outcomes. With the assistance of Provengo, testers gain the ability to effortlessly create natural user stories and seamlessly integrate them into a model capable of generating effective tests. The demonstration illustrates how SDMBT effectively addresses the bootstrapping challenge commonly encountered in model-based testing (MBT) by enabling incremental development, starting from simple models and gradually augmenting them with additional stories.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00146"}, {"primary_key": "1219986", "vector": [], "sparse_vector": [], "title": "Optimizing Continuous Development by Detecting and Preventing Unnecessary Content Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Basak Balci", "<PERSON><PERSON>", "August Shi", "Wing Lam"], "summary": "Continuous development (CD) helps developers quickly release and update their software. To enact CD, developers customize their CD builds to perform several tasks, including compiling, testing, static analysis checks, etc. However, as developers add more tasks to their builds, the builds take longer to run, therefore slowing down the entire CD process. Furthermore, developers may unknowingly include tasks into their builds whose results are not used (e.g., generating coverage files that are never read or uploaded anywhere), therefore wasting build runtime doing unnecessary tasks. We propose OptCD, a technique to dynamically detect unnecessary work within CD builds. Our intuition is that unnecessary work can be identified by the generation of files that are not used by any other task within the build. OptCD runs alongside a CD build, tracking the generated files during the build and which files are read/written. Files that are written to but are never read from are unnecessary content from a build. Based on the names of the unnecessary files, OptCD then maps the files to the specific build tasks responsible for generating or writing to those files. Finally, OptCD leverages ChatGPT to suggest changing the build configuration to disable generating these unnecessary files. Our evaluation of OptCD on 22 open-source projects finds that 95.6% of projects generate at least one unused directory, a directory whose contents are all unnecessarily generated. OptCD identifies the correct task that generates 92.0% of the unused directories. Further, OptCD can produce a patch for the CD configuration file to prevent generating 72.0% of the unused directories. Using the patches, we reduce the runtime by 7.0% on average for the projects we studied. We submitted 26 pull requests for the unused directories that we could disable. Developers have accepted 12 of them, with five rejected, and nine still pending.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00216"}, {"primary_key": "1219987", "vector": [], "sparse_vector": [], "title": "CPA-DF: A Tool for Configurable Interval Analysis to Boost Program Verification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Software verification is challenging, and auxiliary program invariants are used to improve the effectiveness of verification approaches. For instance, the k-induction implementation in CPACHECKER, an award-winning framework for program analysis, uses invariants produced by a configurable data-flow analysis to strengthen induction hypotheses. This invariant generator, CPA-DF, uses arithmetic expressions over intervals as its abstract domain and is able to prove some safe verification tasks alone. After extensively evaluating CPA-DF on SV-Benchmarks, the largest publicly available suite of C safety-verification tasks, we discover that its potential as a stand-alone analysis or a sub-analysis in a parallel portfolio for combined verification approaches has been significantly underestimated: (1) As a stand-alone analysis, CPA-DF finds almost as many proofs as the plain k-induction implementation without auxiliary invariants. (2) As a sub-analysis running in parallel to the plain k-induction implementation, CPA-DF boosts the portfolio verifier to solve a comparable amount of tasks as the heavily-optimized k-induction implementation with invariant injection. Our detailed analysis reveals that dynamic precision adjustment is crucial to the efficiency and effectiveness of CPA-DF. To generalize our results beyond CPACHECKER, we use CoVeriteam,a platform for cooperative verification, to compose three portfolio verifiers that execute CPA-DF and three other software verifiers in parallel, respectively. Surprisingly, running CPA-DF merely in parallel to these state-of-the-art tools further boosts the number of correct results up to more than 20 %. Demonstration video: https://youtu.be/l7UG-vhTL_4", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00213"}, {"primary_key": "1219988", "vector": [], "sparse_vector": [], "title": "cegar-pt: A Tool for Abstraction by Program Transformation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstraction is an important approach for proving the correctness of computer programs. There are many implementations of this approach available, but unfortunately, the various implementations are difficult to reuse and combine, and the successful techniques have to be re-implemented in new tools again and again. We address this problem by contributing the tool cegar-pt, which views abstraction as program transformation and integrates different verification components off-the-shelf. The idea is to use existing components without having to change their implementation, while still adjusting the precision of the abstraction using the successful CEGAR approach. The approach of cegar-pt is largely general: It only restricts the abstraction to transform, given a precision that defines the level of abstraction, one program into another program. The abstraction by program transformation can over-approximate the data flow (e.g., havoc some variables, use more abstract types) or the control flow (e.g., loop abstraction, slicing). To illustrate our tool, we provide a demonstration video, accessible at https://youtu.be/ASZ6hoq8asE.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00215"}, {"primary_key": "1219989", "vector": [], "sparse_vector": [], "title": "LIV: Loop-Invariant Validation Using Straight-Line Programs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Validation of program invariants (a.k.a. correctness witnesses) is an established procedure in software verification. There are steady advances in verification of more and more complex software systems, but coming up with good loop invariants remains the central task of many verifiers. While it often requires large amounts of computation to construct safe and inductive invariants, they are more easy to automatically validate. We propose LIV, a new tool for loop-invariant validation, which makes it more practical to check if the invariant produced by a verifier is sufficient to establish an inductive safety proof. The main idea is to apply divide-and-conquer on the program level: We split the program into smaller, loop-free programs (a.k.a. straight-line programs) that form simpler verification tasks. Because the verification conditions are not encoded in logic syntax (such as SMT), but as programs in the language of the original program, any off-the-shelf verifier can be used to verify the generated straight-line programs. In case the validation fails, useful information can be extracted about which part of the proof failed (which straight-line programs are wrong). We show that our approach works by evaluating it on a suitable benchmark. Supplementary website: https://www.sosy-lab.org/research/liv/", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00214"}, {"primary_key": "1219990", "vector": [], "sparse_vector": [], "title": "TEASER: Simulation-Based CAN Bus Regression Testing for Self-Driving Cars Software.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Safety-critical systems such as self-driving cars (SDCs) must be rigorously tested. Especially electronic control units (ECUs) of SDCs should be tested with realistic input data. In this context, a communication protocol called Controller Area Network (CAN) is typically used to transfer sensor data to the SDC control units. A challenge for SDC maintainers and testers is the need to manually define the CAN inputs that realistically represent the state of the SDC in the real world. To address this challenge, we developed TEASER; a tool that generates realistic CAN signals for SDCs obtained from sensors from state-of-the-art car simulators. We evaluated TEASER based on its integration capability into a DevOps pipeline of aicas GmbH, a company in the automotive sector. Concretely, we integrated TEASER in a Continous Integration (CI) pipeline configured with Jenkins. The pipeline executes the test cases in simulation environments and sends the sensor data over the CAN bus to a physical CAN device, the test subject. Our evaluation shows the ability of TEASER to generate and execute CI test cases that expose simulation-based faults (using regression strategies); the tool produces CAN inputs that realistically represent the state of the SDC in the real world. This result is critically important for increasing the automation and effectiveness of simulation-based CAN bus regression testing for SDCs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00154"}, {"primary_key": "1219991", "vector": [], "sparse_vector": [], "title": "Message from the Chairs: ASE 2023.", "authors": ["Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>", "Federica Sarro"], "summary": "On behalf of the entire conference organizing committee, it is our great pleasure to welcome you to the 38 th edition of the IEEE/ACM International Conference on Automated Software Engineering, ASE 2023, in the Grand Duchy of Luxembourg. The ASE conference series is the premier research forum for automated software engineering research and practice. Each year it brings together researchers and practitioners from academia and industry to discuss foundations, techniques, and tools for automated analysis, design, implementation, testing, and maintenance of large software systems.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00005"}, {"primary_key": "1219992", "vector": [], "sparse_vector": [], "title": "MUT4SLX: Fast Mutant Generation for Simulink.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Several experience reports illustrate that mutation testing is capable of supporting a \"shift-left\" testing strategy for software systems coded in textual programming languages like C++. For graphical modelling languages like Simulink, such experience reports are missing, primarily because of a lack of adequate tool support. In this paper, we present a proof-of-concept (named MUT4S LX) for automatic mutant generation and test execution of Simulink models. MUT14SLX features 15 mutation operators which are modelled after realistic faults (mined from an industrial bug database) and are fast to inject (because we only replace parameter values within blocks). An experimental evaluation on a sample project (a Helicopter Control System) demonstrates that MUT4SLX is capable of injecting 70 mutants in less than a second, resulting in a total analysis time of 8.14 hours.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00093"}, {"primary_key": "1219993", "vector": [], "sparse_vector": [], "title": "ESRO: Experience Assisted Service Reliability against Outages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern cloud services are prone to failures due to their complex architecture, making diagnosis a critical process. Site Reliability Engineers (SREs) spend hours leveraging multiple sources of data, including the alerts, error logs, and domain expertise through past experiences to locate the root cause(s). These experiences are documented as natural language text in outage reports for previous outages. However, utilizing the raw yet rich semi-structured information in the reports systematically is time-consuming. Structured information, on the other hand, such as alerts that are often used during fault diagnosis, is voluminous and requires expert knowledge to discern. Several strategies have been proposed to use each source of data separately for root cause analysis. In this work, we build a diagnostic service called ESRO that recommends root causes and remediation for failures by utilizing structured as well as semi-structured sources of data systematically. ESRO constructs a causal graph using alerts and a knowledge graph using outage reports, and merges them in a novel way to form a unified graph during training. A retrieval based mechanism is then used to search the unified graph and rank the likely root causes and remediation techniques based on the alerts fired during an outage at inference time. Not only the individual alerts, but their respective importance in predicting an outage group is taken into account during recommendation. We evaluated our model on several cloud service outages of a large SaaS enterprise over the course of ~2 years, and obtained an average improvement of 27% in rouge scores after comparing the likely root causes against the ground truth over state-of-the-art baselines. We further establish the effectiveness of ESRO through qualitative analysis on multiple real outage examples.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00131"}, {"primary_key": "1219994", "vector": [], "sparse_vector": [], "title": "Characterizing Flaky Tests in Node.js Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Regression testing is an important means of assessing the quality of Node.js applications. However, non-deterministic executions inside Node.js framework could make test cases intermittently pass or fail on the same version of code, which are called flaky tests. Flaky tests can cause unreliable test results, and make developers waste a significant amount of time debugging the bugs that do not belong to the target application. In this paper, we conduct an empirical study on 87 flaky tests from 7 popular Node.js applications, and analyze the non-determinism that causes these flaky tests. Through this study, there is a wide range of non-determinism to cause flaky tests, including non-deterministic event triggering order, non-deterministic function calls, non-deterministic process/thread scheduling order, non-deterministic execution of asynchronous tasks and non-deterministic event triggering data. The result reveals that, existing approaches on event race detection are not sufficient for flaky test detection. In future, researchers can design flaky test detection approaches targeted at different categories of non-determinism.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00025"}, {"primary_key": "1219995", "vector": [], "sparse_vector": [], "title": "RJoules: An Energy Measurement Tool for R.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the exponential growth of data, the demand for effective data analysis tools has increased significantly. R language, known for its statistical modeling and data analysis capabilities, has become one of the most popular programming languages among data scientists and researchers. As the importance of energy-aware software systems continues to rise, several studies investigate the impact of source code and different stages of machine learning model training on energy consumption. However, existing studies in this domain primarily focus on programming languages like Python and Java, resulting in a lack of energy measuring tools for other programming languages such as R. To address this gap, we propose \"RJoules\", a tool designed to measure the energy consumption of R code snippets. We evaluate the correctness and performance of RJoules by applying it to four machine learning algorithms on three different systems. Our aim is to support developers and practitioners in building energy-aware systems in R. The demonstration of the tool is available at https://youtu.be/yMKFuvAM-DE and related artifacts at https://rishalab.github.io/RJoules/.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00207"}, {"primary_key": "1219996", "vector": [], "sparse_vector": [], "title": "An Integrated Program Analysis Framework for Graduate Courses in Programming Languages and Software Engineering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Program analysis, verification and testing are important topics in programming languages and software engineering. They aim to produce engineers who are not only capable of empirically evaluating but, also formally reasoning on the correctness of software systems. We propose a specialized framework, Chiron, designed to teach graduate-level courses on these topics. Chiron has a small code base for easy understanding, uses a unified intermediate representation across all its analysis modules, maintains a modular architecture for plugging in new algorithms and uses a \"fun\" programming language to provide a gamified experience. Currently, it packages a dataflow analysis engine for driving compiler optimizations, an abstract interpretation engine for verification, a symbolic execution engine, a fuzzer and an evolutionary test generator for program testing, and a spectrum based statistical bug localization module. Within Chiron, program analysis tasks are posed in an unconventional setting (as adventures of a turtle) to provide a gamified experience; the accompanying animations (showing the movements of the turtle) allow the student to understand the underlying concepts better, and the detailed logs allow the teaching assistants in their grading activities. Chiron has been used in two offerings of a graduate level course on program analysis, verification and testing. In response to our survey questionnaire, all the students unanimously held the opinion that <PERSON><PERSON> was extremely helpful in aiding their learning, and recommended its use in similar courses.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00101"}, {"primary_key": "1219997", "vector": [], "sparse_vector": [], "title": "Fast and Reliable Program Synthesis via User Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The performance of programming-by-example systems varies significantly across different tasks and even across different examples in one task. The key issue is that the search space depends on the given examples in a complex way. In particular, scalable synthesizers typically rely on a combination of machine learning to prioritize search order and deduction to prune search space, making it hard to quantitatively reason about how much an example speeds up the search. We propose a novel approach for quantifying the effectiveness of an example at reducing synthesis time. Based on this technique, we devise an algorithm that actively queries the user to obtain additional examples that significantly reduce synthesis time. We evaluate our approach on 30 challenging benchmarks across two different data science domains. Even with ineffective initial user-provided examples for pruning, our approach on average achieves a 6.0× speed-up in synthesis time compared to state-of-the-art synthesizers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00129"}, {"primary_key": "1219998", "vector": [], "sparse_vector": [], "title": "Dynamic Graph Neural Networks-Based Alert Link Prediction for Online Service Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A fault in large online service systems often triggers numerous alerts due to the complex business and component dependencies among services, which is known as \"alert storm\". In a short time, an online service system may generate a huge amount of alert data. This poses a challenge for on-call engineers to identify alerts that are associated with a system failure for root cause analysis. In this paper, we propose DyAlert, a dynamic graph neural networks-based approach for linking alerts that might be triggered by a same fault to reduce the burden of on-call engineers in the fault analysis. Our insight is that alerts are often triggered by alert propagation when a system failure occurs, e.g., alert $a$ would lead to the occurrence of alert $b$ . Whether two alerts should be linked depends on if one alert is triggered by the propagation of the other. Leveraging this insight, we design a dynamic graph (namely Alert-Metric Dynamic Graph) that describes the propagation process of alerts. Based on the dynamic graph, we train a neural networks-based model to predict alert links. We evaluate DyAlert with real-world data collected from an online service system running 85 business units and about 30,000 different services in a large enterprise. The results show that DyAlert is effective in predicting alert links and it outperforms the state-of-the-art approaches with an average increase of 0.259 in F1-score.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00177"}, {"primary_key": "1220000", "vector": [], "sparse_vector": [], "title": "Detection of Java Basic Thread Misuses Based on Static Event Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The fundamental asynchronous thread (java.lang. Thread) in Java can be easily misused, due to the lack of deep understanding for garbage collection and thread interruption mechanism. For example, a careless implementation of asynchronous thread may cause no response to the interrupt mechanism in time, resulting in unexpected thread-related behaviors, especially resource leak/waste. Currently, few works aim at these misuses and related works adopt either the dynamic approach which lacks effective inputs or the static path-sensitive approach with high time consumption due to the path explosion, causing false negatives. We have found that the behavior of threads and the interaction between threads and its referencing objects can be abstracted. In this paper, we propose an event analysis approach to detect the defects in Java programs and Android apps, which focuses on the existence or the order of the events to reduce the false negatives. We extract the misuse-related events, containing the thread events and the destroy events of the object referenced by the thread. Then we analyze the events with loop identification, happens-before relationship construction and alias determination. Finally, we implement an automatic tool named <PERSON><PERSON> and evaluate it on real world Java programs and Android apps. Experiments show that it is efficient when comparing with the existing approach (misuse: 723 vs 47, time: 60s vs 30min), which also outperforms the existing work in precision. The manual check indicates that <PERSON><PERSON> is more efficient and effective than existing work. Besides, 66 issues reported by us have been confirmed and 21 of them have been fixed by developers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00145"}, {"primary_key": "1220001", "vector": [], "sparse_vector": [], "title": "COMEX: A Tool for Generating Customized Source Code Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning effective representations of source code is critical for any Machine Learning for Software Engineering (ML4SE) system. Inspired by natural language processing, large language models (LLMs) like Codex and CodeGen treat code as generic sequences of text and are trained on huge corpora of code data, achieving state of the art performance on several software engineering (SE) tasks. However, valid source code, unlike natural language, follows a strict structure and pattern governed by the underlying grammar of the programming language. Current LLMs do not exploit this property of the source code as they treat code like a sequence of tokens and overlook key structural and semantic properties of code that can be extracted from code-views like the Control Flow Graph (CFG), Data Flow Graph (DFG), Abstract Syntax Tree (AST), etc. Unfortunately, the process of generating and integrating code-views for every programming language is cumbersome and time consuming. To overcome this barrier, we propose our tool COMEX - a framework that allows researchers and developers to create and combine multiple code-views which can be used by machine learning (ML) models for various SE tasks. Some salient features of our tool are: (i) it works directly on source code (which need not be compilable), (ii) it currently supports Java and C#, (iii) it can analyze both method-level snippets and program-level snippets by using both intra-procedural and inter-procedural analysis, and (iv) it is easily extendable to other languages as it is built on tree-sitter - a widely used incremental parser that supports over 40 languages. We believe this easy-to-use code-view generation and customization tool will give impetus to research in source code representation learning methods and ML4SE. The source code and demonstration of our tool can be found at https://github.com/IBM/tree-sitter-codeviews and https://youtu.be/GER6U87FVbU, respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00010"}, {"primary_key": "1220003", "vector": [], "sparse_vector": [], "title": "Challenges of Accurate and Efficient AutoML.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Embedded Artificial Intelligence (AI) is becoming increasingly important in the field of healthcare where such AI enabled devices are utilized to assist physicians, clinicians, and surgeons in their diagnosis, rehabilitation and therapy planning. However, it is still a challenging task to come up with an accurate and efficient machine learning model for resource-limited devices that work $24\\times 7$ .. It requires both intuition and experience. This dependence on human expertise and reliance on trial-and-error-based design methods create impediments to the standard processes of effort estimation, design phase planning, and generating service-level agreements for projects that involve AI-enabled MedTech devices. In this paper, we present AutoML search from an algorithmic perspective, instead of a more prevalent optimization or black-box tool view. We briefly present and point to case studies that demonstrate the efficacy of the automation approach in terms of productivity improvements. We believe that our proposed method can make AutoML more amenable to the applications of software engineering principles and also accelerate biomedical device engineering, where there is a high dependence on skilled human resources.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00182"}, {"primary_key": "1220004", "vector": [], "sparse_vector": [], "title": "A Comparative Study of Transformer-Based Neural Text Representation Techniques on Bug Triaging.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Bug report management has been shown to be an important and time consuming software maintenance task. Often, the first step in managing bug reports is related to triaging a bug to the appropriate developer who is best suited to understand, localize, and fix the target bug. Additionally, assigning a given bug to a particular part of a software project can help to expedite the fixing process. However, despite the importance of these activities, they are quite challenging, where days can be spent on the manual triaging process. Past studies have attempted to leverage the limited textual data of bug reports to train text classification models that automate this process - to varying degrees of success. However, the textual representations and machine learning models used in prior work are limited by their expressiveness, often failing to capture nuanced textual patterns that might otherwise aid in the triaging process. Recently, large, transformer-based, pre-tained neural text representation techniques (i.e., large language models or LLMs) such as BERT and CodeBERT have achieved greater performance with simplified training procedures in several natural language processing tasks, including text classification. However, the potential for using these techniques to improve upon prior approaches for automated bug triaging is not well studied or understood. Therefore, in this paper we offer one of the first investigations that fine-tunes transformer-based language models for the task of bug triaging on four open source datasets, spanning a collective 53 years of development history with over 400 developers and over 150 software project components. Our study includes both a quantitative and qualitative analysis of effectiveness. Our findings illustrate that DeBERTa is the most effective technique across the triaging tasks of developer and component assignment, and the measured performance delta is statistically significant compared to other techniques. However, through our qualitative analysis, we also observe that each technique possesses unique abilities best suited to certain types of bug reports.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00217"}, {"primary_key": "1220005", "vector": [], "sparse_vector": [], "title": "Merge Conflict Resolution: Classification or Generation?", "authors": ["Jin<PERSON> Dong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Collaborative development is critical to improve the productivity. Multiple contributors work simultaneously on the same project and might make changes to the same code locations. This can cause conflicts and require manual intervention from developers to resolve them. To alleviate the human efforts of manual conflict resolution, researchers have proposed various automatic techniques. More recently, deep learning models have been adopted to solve this problem and achieved state-of-the-art performance. However, these techniques leverage classification to combine the existing elements of input. The classification- based models cannot generate new tokens or produce flexible combinations, and have a wrong hypothesis that fine-grained conflicts of one single coarse-grained conflict are independent. In this work, we propose to generate the resolutions of merge conflicts from a totally new perspective, that is, generation, and we present a conflict resolution technique, MergeGen. First, we design a structural and fine-grained conflict-aware representation for the merge conflicts. Then, we propose to leverage an encoder- decoder-based generative model to process the designed conflict representation and generate the resolutions auto-regressively. We further perform a comprehensive study to evaluate the effectiveness of MergeGen. The quantitative results show that MergeGen outperforms the state-of-the-art (SOTA) techniques from both precision and accuracy. Our evaluation on multiple programming languages verifies the good generalization ability of MergeGen. In addition, the ablation study shows that the major component of our technique makes a positive contribution to the performance of MergeGen, and the granularity analysis reveals the high tolerance of MergeGen to coarse-grained conflicts. Moreover, the analysis on generating new tokens further proves the advance of generative models.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00155"}, {"primary_key": "1220006", "vector": [], "sparse_vector": [], "title": "Persisting and Reusing Results of Static Program Analyses on a Large Scale.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Static Program Analysis (SPA) has long been established as an important technique for gaining insights into software systems. Over the last years, analysis designers increasingly produced analyses that are compositional, collaborative, or incremental in nature - thus relying on some form of existing results to increase performance or even precision. However, systematic result reuse is still rare in this field even though the analyzed software is mainly composed of reusable software components. For this work, we study 40 state-of-the-art SPA implementations and find that there is a tremendous potential for reusing analysis results. We attribute this to the fact that there is no systematic process in place for persisting and sharing analysis results and propose such a process here to fill this gap. In this paper, we present SPARRI, a prototype implementation providing an HTTP API to publish, search, and reuse SPA results. Our evaluation shows that reusing existing results with SPARRI can improve analysis performance by up to 92%. Furthermore, we see potential in applying it to other research areas like empirical software studies. benchmark creation. and artifact evaluation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00080"}, {"primary_key": "1220007", "vector": [], "sparse_vector": [], "title": "From Commit Message Generation to History-Aware Commit Message Completion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Commit messages are crucial to software development, allowing developers to track changes and collaborate effectively. Despite their utility, most commit messages lack important information since writing high-quality commit messages is tedious and time-consuming. The active research on commit message generation (CMG) has not yet led to wide adoption in practice. We argue that if we could shift the focus from commit message generation to commit message completion and use previous commit history as additional context, we could significantly improve the quality and the personal nature of the resulting commit messages. In this paper, we propose and evaluate both of these novel ideas. Since the existing datasets lack historical data, we collect and share a novel dataset called CommitChronicle, containing 10.7M commits across 20 programming languages. We use this dataset to evaluate the completion setting and the usefulness of the historical context for state-of-the-art CMG models and GPT-3.5-turbo. Our results show that in some contexts, commit message completion shows better results than generation, and that while in general GPT-3.5-turbo performs worse, it shows potential for long and detailed messages. As for the history, the results show that historical information improves the performance of CMG models in the generation task, and the performance of GPT-3.5-turbo in both generation and completion.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00078"}, {"primary_key": "1220008", "vector": [], "sparse_vector": [], "title": "RPCover: Recovering gRPC Dependency in Multilingual Projects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advent of microservice architecture has led to a significant shift in the development of service-oriented software. In particular, the use of Remote Procedure Call (RPC), a mode of Inter-Process Communication (IPC) prevalent in microservices, has noticeably increased. To figure out the relationships between services and obtain a high-level understanding of service-oriented software, a line of recent work focuses on the dynamic construction of service call graphs, which relies on the preliminary deployment of services and only captures the calling relationships within a specific time frame. Meanwhile, static methods avoid the need for pre-deployment and often provide a more stable and complete graph compared to dynamic techniques. However, research and practical applications of static call graph construction remain relatively unexplored. This paper introduces RPCover, a novel gRPC dependency recovery framework that facilitates the interconnection of services across various programming languages using their static gRPC calls. In addition, due to the lack of a multilingual microservice benchmark that uses gRPC, we build the first multilingual benchmark RPCoverBench that contains complex gRPC call relations. RPCover has been evaluated on a single language benchmark (DeathStarBench) and our multilingual benchmark (RPCoverBench). The results show that RPCover effectively recovers 99.33% of the use cases of gRPC calls with less than 200% of the overhead compared with a single-language semantic dependency analyzer.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00108"}, {"primary_key": "1220009", "vector": [], "sparse_vector": [], "title": "Towards Autonomous Testing Agents via Conversational Large Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software testing is an important part of the development cycle, yet it requires specialized expertise and substantial developer effort to adequately test software. Recent discoveries of the capabilities of large language models (LLMs) suggest that they can be used as automated testing assistants, and thus provide helpful information and even drive the testing process. To highlight the potential of this technology, we present a taxonomy of LLM-based testing agents based on their level of autonomy, and describe how a greater level of autonomy can benefit developers in practice. An example use of LLMs as a testing assistant is provided to demonstrate how a conversational framework for testing can help developers. This also highlights how the often criticized \"hallucination\" of LLMs can be beneficial for testing. We identify other tangible benefits that LLM-driven testing agents can bestow, and also discuss potential limitations.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00148"}, {"primary_key": "1220010", "vector": [], "sparse_vector": [], "title": "Towards a Formal Framework for Normative Requirements Elicitation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Beverley A. <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As software and cyber-physical systems interacting with humans become prevalent in domains such as healthcare, education and customer service, software engineers need to consider normative (i.e., social, legal, ethical, empathetic and cultural) requirements. However, their elicitation is challenging, as they must reflect the often conflicting or redundant views of stakeholders ranging from users and operators to lawyers, ethicists and regulators. To address this challenge, we introduce a tool-supported Formal framework for normaTive requirements elicitation (FormaTive). It allows specification of normative rules for a software system in an intuitive high-level language, and automates: (i) the mapping of the rules to an internal formal representation; (ii) their analysis to identify rule conflicts, redundancies, and concerns; and (iii) the synthesis of feedback enabling users to understand and resolve problems.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00152"}, {"primary_key": "1220012", "vector": [], "sparse_vector": [], "title": "What Makes Good In-Context Demonstrations for Code Intelligence Tasks with LLMs?", "authors": ["Shuzheng Gao", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Pre-trained models of source code have gained widespread popularity in many code intelligence tasks. Recently, with the scaling of the model and corpus size, large language models have shown the ability of in-context learning (ICL). ICL employs task instructions and a few examples as demonstrations, and then inputs the demonstrations to the language models for making predictions. This new learning paradigm is training-free and has shown impressive performance in various natural language processing and code intelligence tasks. However, the performance of ICL heavily relies on the quality of demonstrations, e.g., the selected examples. It is important to systematically investigate how to construct a good demonstration for code-related tasks. In this paper, we empirically explore the impact of three key factors on the performance of ICL in code intelligence tasks: the selection, order, and number of demonstration examples. We conduct extensive experiments on three code intelligence tasks including code summarization, bug fixing, and program synthesis. Our experimental results demonstrate that all the above three factors dramatically impact the performance of ICL in code intelligence tasks. Additionally, we summarize our findings and provide takeaway suggestions on how to construct effective demonstrations, taking into account these three perspectives. We also show that a carefully-designed demonstration based on our findings can lead to substantial improvements over widely-used demonstration construction methods, e.g., improving BLEU-4, EM, and EM by at least 9.90%, 175.96%, and 50.81% on code summarization, bug fixing, and program synthesis, respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00109"}, {"primary_key": "1220013", "vector": [], "sparse_vector": [], "title": "Mutation-based Fault Localization of Deep Neural Networks.", "authors": ["<PERSON>", "Deepak-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) are susceptible to bugs, just like other types of software systems. A significant uptick in using DNN, and its applications in wide-ranging areas, including safety-critical systems, warrant extensive research on software engineering tools for improving the reliability of DNN-based systems. One such tool that has gained significant attention in the recent years is DNN fault localization. This paper revisits mutation-based fault localization in the context of DNN models and proposes a novel technique, named deepmufl, applicable to a wide range of DNN models. We have implemented deepmufl and have evaluated its effectiveness using 109 bugs obtained from StackOverflow. Our results show that deepmufl detects 53/109 of the bugs by ranking the buggy layer in top-1 position, outperforming state-of-the-art static and dynamic DNN fault localization systems that are also designed to target the class of bugs supported by deepmufl. Moreover, we observed that we can halve the fault localization time for a pre-trained model using mutation selection, yet losing only 7.55% of the bugs localized in ton-1 position.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00171"}, {"primary_key": "1220016", "vector": [], "sparse_vector": [], "title": "Merge-Replay: Efficient IFDS-Based Taint Analysis by Consolidating Equivalent Value Flows.", "authors": ["Yujiang Gui", "<PERSON><PERSON><PERSON>", "Jingling Xue"], "summary": "The IFDS-based taint analysis employs two mutually iterative passes: a forward pass that identifies taints and a backward pass that detects aliases. This approach ensures both flow and context sensitivity, leading to remarkable precision. To preserve flow sensitivity, the IFDS-based taint analysis enhances data abstractions with activation statements that pinpoint the moment they acquire taint. Nonetheless, this mechanism can inadvertently introduce equivalent, yet redundant, value flows. This occurs when distinct activation statements are linked with the same data abstraction, resulting in unnecessary computational and memory-intensive demands on the analysis process. We introduce MergeDroid, a novel approach to improve the efficiency of IFDS-based taint analysis by consolidating equivalent value flows. This involves merging activation statements linked to the same data abstraction from various reachable data facts that are reachable at a given program point during the backward pass. This process generates a representative symbolic activation statement applicable to all equivalent data facts, reducing them to a single symbolic data fact. During the forward pass, when this symbolic data fact returns to its point of creation, the analysis reverts to the original data facts alongside their initial activation statements. This merge-and-replay strategy eliminates redundant value flow propagation, resulting in performance gains. Furthermore, we also improve analysis efficiency and precision by leveraging context-sensitive insights from activation statements. Our evaluation on 40 Android apps demonstrates that MergeDroid significantly enhances IFDS-based taint analysis performance. On average, MergeDroid accelerates analysis by 9.0× while effectively handling 6 more apps scalably. Additionally, it reduces false positives by significantly decreasing reported leak warnings, achieving an average reduction of 19.2%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00027"}, {"primary_key": "1220017", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Malicious Code In PyPI Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "PyPI provides a convenient and accessible package management platform to developers, enabling them to quickly implement specific functions and improve work efficiency. However, the rapid development of the PyPI ecosystem has led to a severe problem of malicious package propagation. Malicious developers disguise malicious packages as normal, posing a significant security risk to end-users. To this end, we conducted an empirical study to understand the characteristics and current state of the malicious code lifecycle in the PyPI ecosystem. We first built an automated data collection framework and collated a multi-source malicious code dataset containing 4,669 malicious package files. We preliminarily classified these malicious code into five categories based on malicious behaviour characteristics. Our research found that over 50 % of malicious code exhibits multiple malicious behaviours, with information stealing and command execution being particularly prevalent. In addition, we observed several novel attack vectors and anti-detection techniques. Our analysis revealed that 74.81 % of all malicious packages successfully entered end-user projects through source code installation, thereby increasing security risks. A real-world investigation showed that many reported malicious packages persist in PyPI mirror servers globally, with over 72 % remaining for an extended period after being discovered. Finally, we sketched a portrait of the malicious code lifecycle in the PyPI ecosystem, effectively reflecting the characteristics of malicious code at different stages. We also present some suggested mitigations to improve the security of the Python open-source ecosystem.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00135"}, {"primary_key": "1220018", "vector": [], "sparse_vector": [], "title": "Hot Patching Hot Fixes: Reflection and Perspectives.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "With our reliance on software continuously increasing, it is of utmost importance that it be reliable. However, complete prevention of bugs in live systems is unfortunately an impossible task due to time constraints, incomplete testing, and developers not having knowledge of the full stack. As a result, mitigating risks for systems in production through hot patching and hot fixing has become an integral part of software development. In this paper, we first give an overview of the terminology used in the literature for research on this topic. Subsequently, we build upon these findings and present our vision for an automated framework for predicting and mitigating critical software issues at runtime. Our framework combines hot patching and hot fixing research from multiple fields, in particular: software defect and vulnerability prediction, automated test generation and repair, as well as runtime patching. We hope that our vision inspires research collaboration between the different communities.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00021"}, {"primary_key": "1220019", "vector": [], "sparse_vector": [], "title": "SmartCoCo: Checking Comment-Code Inconsistency in Smart Contracts via Constraint Propagation and Binding.", "authors": ["Sicheng Hao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart contracts are programs running on the blockchain. Comments in source code provide meaningful information for developers to facilitate code writing and understanding. Given various kinds of token standards in smart contracts (e.g., ERC-20, ERC-721), developers often copy&paste code from other projects as templates, and then implement their own logic as add-ons to such templates. In many cases, the consistency between code and comment is not well-aligned, leading to comment-code inconsistencies (as we call CCIs). Such inconsistencies can mislead developers and users, and even introduce vulnerabilities to the contracts. In this paper, we present SmartCoCo, a novel framework to detect comment-code inconsistencies in smart contracts. In particular, our research focuses on comments related to roles, parameters, and events that may lead to security implications. To achieve this, SmartCoCo takes the original smart contract source code as input and automatically analyzes the comment and code to find potential inconsistencies. SmartCoCo associates comment constraints and code facts via a set of propagation and binding strategies, allowing it to effectively discover inconsistencies with more contextual information. We evaluated SmartCoCo on 101,780 unique smart contracts on Ethereum. The evaluation result shows that SmartCoCo achieves good effectiveness and efficiency. In particular, SmartCoCo reports 4,732 inconsistencies from 1,745 smart contracts, with a precision of over 79% on 439 manual-labeled comment-code inconsistencies. Meanwhile, it only takes 2.64 seconds to check a smart contract on average.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00142"}, {"primary_key": "1220020", "vector": [], "sparse_vector": [], "title": "Understanding and Enhancing Issue Prioritization in GitHub.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Minxue Pan", "<PERSON><PERSON>", "<PERSON>"], "summary": "GitHub has become a prominent platform for open source software development, facilitating collaboration and communication among a diverse group of contributors. Efficient issue tracking is a crucial aspect of managing projects on GitHub, and labels serve as one of the primary mechanisms for issue prioritization, while various other issue features are also utilized by issue handlers for the same purpose. However, in large projects, prioritizing issues remains a challenge, and the efficacy of using labels or other issue features for prioritization is not well understood. To address this knowledge gap, we conduct a comprehensive empirical study that investigates the role of labels in GitHub issue prioritization, examines the influence of various issue features on prioritization, and assesses the performance of different ranking algorithms based on these impactful features. Our study, conducted on a dataset comprising data from over 1.5 million issues across diverse GitHub projects, provides valuable insights for issue handling in open source platforms and offers guidance for future research in this domain. Specifically, the study reveals the limited effectiveness of labels in issue prioritization, highlights the significance of certain issue features in the prioritization process, and compares the performance of various ranking algorithms for issue prioritization to support issue handlers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00044"}, {"primary_key": "1220021", "vector": [], "sparse_vector": [], "title": "The MAP Metric in Information Retrieval Fault Localization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The MAP (Mean Average Precision) metric is one of the most popular performance metrics in the field of Information Retrieval Fault Localization (IRFL). However, there are problematic implementations of this MAP metric used in IRFL research. These implementations deviate from the text book definitions of MAP, rendering the metric sensitive to the truncation of retrieval results and inaccuracies and impurities of the used datasets. The application of such a deviating metric can lead to performance overestimation. This can pose a problem for comparability, transferability, and validity of IRFL performance results. In this paper, we discuss the definition and mathematical properties of MAP and common deviations and pitfalls in its implementation. We investigate and discuss the conditions enabling such overestimation: the truncation of retrieval results in combination with ground truths spanning multiple files and improper handling of undefined AP results. We demonstrate the overestimation effects using the Bench4BL benchmark and five well known IRFL techniques. Our results indicate that a flawed implementation of the MAP metric can lead to an overestimation of the IRFL performance, in extreme cases by up to 70 %. We argue for a strict adherence to the text book version of MAP with the extension of undefined AP values to be set to 0 for all IRFL experiments. We hope that this work will help to improve comparability and transferability in IRFL research.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00041"}, {"primary_key": "1220022", "vector": [], "sparse_vector": [], "title": "MUTEN: Mutant-Based Ensembles for Boosting Gradient-Based Adversarial Attack.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mutation testing (MT) for deep learning (DL) has gained huge attention in the past few years. However, how MT can really help DL is still unclear. In this paper, we introduce one promising direction for the usage of mutants. Specifically, since mutants can be seen as one kind of ensemble model and ensemble model can be used to boost the adversarial attack, we propose MUTEN, which applies the attack on mutants to improve the success rate of well-known attacks against gradient-masking models. Experimental results on MNIST, SVHN, and CIFAR-10 show that MUTEN can increase the success rate of four attacks by up to 45%. Furthermore, experiments on four defense approaches, bit-depth reduction, JPEG compression, Defensive distillation, and Label smoothing, demonstrate that MUTEN can break the defense models effectively by enhancing the attacks with the success rate of up to 96%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00042"}, {"primary_key": "1220023", "vector": [], "sparse_vector": [], "title": "Identify and Update Test Cases When Production Code Changes: A Transformer-Based Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software testing is one of the most essential parts of the software lifecycle and requires a substantial amount of time and effort. During the software evolution, test cases should co-evolve with the production code. However, the co-evolution of test cases often fails due to tight project schedules and other reasons. Obsolete test cases improve the cost of software maintenance and may fail to reveal faults and even lead to future bugs. Therefore, it is essential to detect and update these obsolete test cases in time. In this paper, we propose a novel approach <PERSON><PERSON>rot (Co-Evolution of Production-Test Code) to identify outdated test cases and update them automatically according to changes in the production code. <PERSON><PERSON><PERSON> consists of two stages, i.e., obsolete test identification and updating. Specifically, given a production code change and a corresponding test case, <PERSON><PERSON><PERSON> first identifies whether the test case should be updated. If the test is identified as obsolete, <PERSON><PERSON><PERSON> will update it to a new version of test case. To evaluate the effectiveness of the two stages, we construct two datasets. Our dataset focuses on method-level production code changes and updates on their obsolete test cases. The experimental results show that <PERSON><PERSON><PERSON> can effectively identify obsolete test cases with precision and recall of 98.3% and 90.0%, respectively. In addition, test cases generated by <PERSON><PERSON><PERSON> are identical to the ground truth for 12.3% of samples that are identified as obsolete by <PERSON><PERSON><PERSON>. We also conduct dynamic evaluation and human evaluation to measure the effectiveness of the updated test cases by <PERSON><PERSON><PERSON>. 48.0% of updated test cases can be compiled and the average coverage of updated cases is 34.2% which achieves 89% coverage improvement over the obsolete tests. We believe that this study can motivate the co-evolution of production and test code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00165"}, {"primary_key": "1220025", "vector": [], "sparse_vector": [], "title": "Predicting Compilation Resources for Adaptive Build in an Industrial Setting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Development teams in large companies often maintain a huge codebase whose build time can be painfully long in a single machine. To reduce the build time, tools such as Bazel and distcc are used to build the code base in a distributed way. However, in the process of distributed build, certain remote slave machines can crash due to two types of errors: Out Of Memory (OOM) and Deadline Exceeded (DE) errors. These crashes lead to time-consuming rebuilds, as suffered by the WeiXin Group (WXG) of Tencent Inc. (the vendor of WeChat, a highly popular mobile app in China). Aiming to prevent these two types of errors, in this paper, we propose a new approach named PCRLINEAR, which predicts the memory and time requirements of the given C++ file, allowing the underlying distributed build system to schedule compilation resources adaptively according to the prediction results. Our experiments show that PCRLINEAR reduces the number of OOM and DE errors from 5% to 0.2% and, at the same time, achieves substantial build-performance improvement of 30% on average.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00128"}, {"primary_key": "1220026", "vector": [], "sparse_vector": [], "title": "Fixing Privilege Escalations in Cloud Access Control with MaxSAT and Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Sarfraz Khurshid", "<PERSON>", "<PERSON><PERSON>"], "summary": "Identity and Access Management (IAM) is an access control service employed within cloud platforms. Customers must configure IAM to establish secure access control rules for their cloud organizations. However, IAM misconfigurations can be exploited to conduct Privilege Escalation (PE) attacks, resulting in significant financial losses. Consequently, addressing these PEs is crucial for improving security assurance for cloud customers. Nevertheless, the area of repairing IAM PEs due to IAM mis-configurations is relatively underexplored. To our knowledge, the only existing IAM repair tool called IAM-Deescalate focuses on a limited number of IAM PE patterns, indicating the potential for further enhancements. We propose a novel IAM Privilege Escalation Repair Engine called IAMPERE that efficiently generates an approximately minimal patch for repairing a broader range of IAM PEs. To achieve this, we first formulate the IAM repair problem into a MaxSAT problem. Despite the remarkable success of modern MaxSAT solvers, their scalability for solving complex repair problems remains a challenge due to the state explosion. To improve scalability, we employ deep learning to prune the search space. Specifically, we apply a carefully designed GNN model to generate an intermediate patch that is relatively small, but not necessarily minimal. We then apply a MaxSAT solver to search for a minimum repair within the space defined by the intermediate patch, as the final approximately minimum patch. Experimental results on both synthesized and real-world IAM misconfigurations show that, compared to IAM-Deescalate, IAMPERE repairs a significantly larger number of IAM misconfigurations with markedly smaller patch sizes.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00167"}, {"primary_key": "1220027", "vector": [], "sparse_vector": [], "title": "ATOM: Automated Black-Box Testing of Multi-Label Image Classification Systems.", "authors": ["Shengy<PERSON> Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xinta<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-label Image Classification Systems (MICSs) developed based on Deep Neural Networks (DNNs) are extensively used in people's daily life. Currently, although there are a variety of approaches to test DNN-based systems, they typically rely on the internals of DNNs to design test cases, and do not take the core specification of MICS (i.e., correctly recognizing multiple objects in a given image) into account. In this paper, we propose ATOM, an automated and systematic black-box testing framework for testing MICS. Specifically, ATOM exploits the label combination as the testing adequacy criteria, hoping to systematically examine the impact of correlations between a fixed number of labels on the classification ability of MICS. Then, ATOM leverages image search engine and natural language processing to find test images that are not only common to the real-world, but also relevant to target label combinations. Finally, ATOM combines metamorphic testing and label information to realize test oracle identification, based on which the ability of MICS in classifying different label combinations is evaluated. To evaluate the effectiveness of ATOM, we have performed experiments on two popular datasets of MICS, VOC and COCO (each with five state-of-the-art DNN models), and one real-world photo tagging application from our industrial partner. The experimental results reveal that the performance of current DNN-based MICSs remains less satisfactory even in recognizing correlations between only two labels, as ATOM triggers a total number of 6,049 such label combination related errors for all MICSs studied. In particular, ATOM reports 587 error-revealing images for the industrial MICS, in which 92% of them are confirmed by the developers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00156"}, {"primary_key": "1220028", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Fine-Tuning Large Language Models of Code for Automated Program Repair.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The advent of large language models (LLMs) has opened up new opportunities for automated program repair (APR). In particular, some recent studies have explored how to leverage large language models of code (LLMCs) for program repair tasks and show promising results. However, most of them adopt the zero/few-shot learning paradigm for APR, which directly use LLMCs to generate the possibly correct code given its surrounding context. Though effective, the repair capabilities of LLMCs based on the fine-tuning paradigm have yet to be extensively explored. Also, it remains unknown whether LLMCs have the potential to repair more complicated bugs (e.g., multi-hunk bugs). To fill the gap, in this work, we conduct a comprehensive study on the program repair capability of LLMCs in the fine-tuning paradigm. We select 5 popular LLMCs with representative pre-training architectures, including CodeBERT, GraphCode-BERT, PLBART, CodeT5, and UniX coder. We consider 3 typical program repair scenarios (i.e., bugs, vulnerabilities, and errors) involving 3 programming languages (i.e., Java, $\\mathrm{C}/\\mathrm{C}++$ , and JavaScript). Notably, we take both single-hunk and multi-hunk bugs/vulnerabilities into account. We then fine-tune them on widely-used datasets and compare them with existing state-of-the-art APR tools. We also investigate the impact of different design choices, which include code abstractions, code representations, and model evaluation metrics. Our experimental results show that LLMCs in the fine-tuning paradigm can significantly outperform previous state-of-the-art APR tools. Through in-depth analysis, we provide insights into choosing appropriate strategies to guide LLMCs for better performance. Lastly, we reveal several limitations of LLMCs for APR and make suggestions for future research on LLMC-based APR.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00181"}, {"primary_key": "1220029", "vector": [], "sparse_vector": [], "title": "Let&apos;s Chat to Find the APIs: Connecting Human, LLM and Knowledge Graph through AI Chain.", "authors": ["<PERSON>", "Zhenyu Wan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qinghua Lu"], "summary": "API recommendation methods have evolved from literal and semantic keyword matching to query expansion and query clarification. The latest query clarification method is knowledge graph (KG)-based, but limitations include out-of-vocabulary (OOV) failures and rigid question templates. To address these limitations, we propose a novel knowledge-guided query clarification approach for API recommendation that leverages a large language model (LLM) guided by KG. We utilize the LLM as a neural knowledge base to overcome OOV failures, generating fluent and appropriate clarification questions and options. We also leverage the structured API knowledge and entity relationships stored in the KG to filter out noise, and transfer the optimal clarification path from KG to the LLM, increasing the efficiency of the clarification process. Our approach is designed as an AI chain that consists of five steps, each handled by a separate LLM call, to improve accuracy, efficiency, and fluency for query clarification in API recommendation. We verify the usefulness of each unit in our AI chain, which all received high scores close to a perfect 5. When compared to the baselines, our approach shows a significant improvement in MRR, with a maximum increase of 63.9% higher when the query statement is covered in KG and 37.2% when it is not. Ablation experiments reveal that the guidance of knowledge in the KG and the knowledge-guided pathfinding strategy are crucial for our approach's performance, resulting in a 19.0% and 22.2% increase in MAP, respectively. Our approach demonstrates a way to bridge the gap between KG and LLM, effectively compensating for the strengths and weaknesses of both.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00075"}, {"primary_key": "1220030", "vector": [], "sparse_vector": [], "title": "Twin Graph-Based Anomaly Detection via Attentive Multi-Modal Learning for Microservice System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Microservice architecture has sprung up over recent years for managing enterprise applications, due to its ability to independently deploy and scale services. Despite its benefits, ensuring the reliability and safety of a microservice system remains highly challenging. Existing anomaly detection algorithms based on a single data modality (i.e., metrics, logs, or traces) fail to fully account for the complex correlations and interactions between different modalities, leading to false negatives and false alarms, whereas incorporating more data modalities can offer opportunities for further performance gain. As a fresh attempt, we propose in this paper a semi-supervised graph-based anomaly detection method, MSTGAD, which seamlessly integrates all available data modalities via attentive multi-modal learning. First, we extract and normalize features from the three modalities, and further integrate them using a graph, namely MST (microservice system twin) graph, where each node represents a service instance and the edge indicates the scheduling relationship between different service instances. The MST graph provides a virtual representation of the status and scheduling relationships among service instances of a real-world microservice system. Second, we construct a transformer-based neural network with both spatial and temporal attention mechanisms to model the inter-correlations between different modalities and temporal dependencies between the data points. This enables us to detect anomalies automatically and accurately in real-time. Extensive experiments on two real-world datasets verify the effectiveness of our proposed MSTGAD method, achieving competitive performance against state-of-the-art approaches, with a 0.961 F1-score and an average increase of 4.85%. The source code of MST-GAD is publicly available at https://github.com/ant-research/microservice_system_twin_graph_based_anomaly_detection.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00138"}, {"primary_key": "1220031", "vector": [], "sparse_vector": [], "title": "NaturalFuzz: Natural Input Generation for Big Data Analytics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fuzzing applies input mutations iteratively with the only goal of finding more bugs, resulting in synthetic tests that tend to lack realism. Big data analytics are expected to ingest real-world data as input. Therefore, when synthetic test data are not easily comprehensible, they are less likely to facilitate the downstream task of fixing errors. Our position is that fuzzing in this domain must achieve both high naturalness and high code coverage. We propose a new natural synthetic test generation tool for big data analytics, called NaturalFuzz. It generates both unstructured, semi-structured, and structured data with corresponding semantics such as 'zipcode' and 'age.' The key insights behind NaturalFuzz are two-fold. First, though existing test data may be small and lack coverage, we can grow this data to increase code coverage. Second, we can strategically mix constituent parts across different rows and columns to construct new realistic synthetic data by leveraging fine-grained data provenance. On commercial big data application benchmarks, NaturalFuzz achieves an additional 19.9% coverage and detects 1.9× more faults than a machine learning-based synthetic data generator (SDV) when generating comparably sized inputs. This is because an ML-based synthetic data generator does not consider which code branches are exercised by which input rows from which tables, while NaturalFuzz is able to select input rows that have a high potential to increase code coverage and mutate the selected data towards unseen, new program behavior. NaturalFuzz's test data is more realistic than the test data generated by two baseline fuzzers (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>), while increasing code coverage and fault detection potential. NaturalFuzz is the first fuzzing methodology with three benefits: (1) exclusively generate natural inputs, (2) fuzz multiple input sources simultaneously, and (3) find deeper semantics faults.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00034"}, {"primary_key": "1220032", "vector": [], "sparse_vector": [], "title": "AutoLog: A Log Sequence Synthesis Framework for Anomaly Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rapid progress of modern computing systems has led to a growing interest in informative run-time logs. Various log-based anomaly detection techniques have been proposed to ensure software reliability. However, their implementation in the industry has been limited due to the lack of high-quality public log resources as training datasets. While some log datasets are available for anomaly detection, they suffer from limitations in (1) comprehensiveness of log events; (2) scalability over diverse systems; and (3) flexibility of log utility. To address these limitations, we propose AUTOLOG, the first automated log generation methodology for anomaly detection. AUTOLOG uses program analysis to generate runtime log sequences without actually running the system. AUTOLOG starts with probing comprehensive logging statements associated with the call graphs of an application. Then, it constructs execution graphs for each method after pruning the call graphs to find log-related execution paths in a scalable manner. Finally, AUTOLOG propagates the anomaly label to each acquired execution path based on human knowledge. It generates flexible log sequences by walking along the log execution paths with controllable parameters. Experiments on 50 popular Java projects show that AUTOLOG acquires significantly more (9x-58x) log events than existing log datasets from the same system, and generates log messages much faster (15x) with a single machine than existing passive data collection approaches. AUTOLOG also provides hyper-parameters to adjust the data size, anomaly rate, and component indicator for simulating different real-world scenarios. We further demonstrate AUTOLOG's practicality by showing that AUTOLOG enables log-based anomaly detectors to achieve better performance (1.93%) compared to existing log datasets. We hope AUTOLOG can facilitate the benchmarking and adoption of automated log analysis techniques.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00133"}, {"primary_key": "1220034", "vector": [], "sparse_vector": [], "title": "RocketHA: A High Availability Design Paradigm for Distributed Log-Based Storage System.", "authors": ["Juntao Ji", "Rongtong Jin", "Yubao Fu", "Yin<PERSON>u Gu", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "As a team from Alibaba Cloud, we have developed and open-sourced RocketMQ, a cloud-native \"messaging, eventing, streaming\" real-time data processing platform that covers cloud-edge-device collaboration scenarios. During the development of RocketMQ, we also formulated RocketHA, a log-based storage high availability design theory that provides a robust solution for distributed log storage software used in industrial applications. RocketHA comprises six fundamental components that enable automatic cluster recovery from failures such as crashes and partitions. This design paradigm has been successfully implemented in the open-source RocketMQ. Our evaluation demonstrates that RocketHA ensures high availability, fast recovery, high throughput, and data loss prevention. We hope that RocketHA will inspire and guide the development of high-availability solutions for all log-based storage systems.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00123"}, {"primary_key": "1220035", "vector": [], "sparse_vector": [], "title": "Perfce: Performance Debugging on Databases with Chaos Engineering-Enhanced Causality Analysis.", "authors": ["<PERSON><PERSON><PERSON> Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Debugging performance anomalies in databases is challenging. Causal inference techniques enable qualitative and quantitative root cause analysis of performance downgrades. Nevertheless, causality analysis is challenging in practice, particularly due to limited observability. Recently, chaos engineering (CE) has been applied to test complex software systems. CE frameworks mutate chaos variables to inject catastrophic events (e.g., network slowdowns) to stress-test these software systems. The systems under chaos stress are then tested (e.g., via differential testing) to check if they retain normal functionality, such as returning correct SQL query outputs even under stress. To date, CE is mainly employed to aid software testing. This paper identifies the novel usage of CE in diagnosing performance anomalies in databases. Our framework, PERFCE, has two phases - offline and online. The offline phase learns statistical models of a database using both passive observations and proactive chaos experiments. The online phase diagnoses the root cause of performance anomalies from both qualitative and quantitative aspects on-the-fly. In evaluation, Perfce outperformed previous works on synthetic datasets and is highly accurate and moderately expensive when analyzing real-world (distributed) databases like MySQL and TiDB.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00106"}, {"primary_key": "1220036", "vector": [], "sparse_vector": [], "title": "Causality-Aided Trade-Off Analysis for Machine Learning Fairness.", "authors": ["<PERSON><PERSON><PERSON> Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There has been an increasing interest in enhancing the fairness of machine learning (ML). Despite the growing number of fairness-improving methods, we lack a systematic understanding of the trade-offs among factors considered in the ML pipeline when fairness-improving methods are applied. This understanding is essential for developers to make informed decisions regarding the provision of fair ML services. Nonetheless, it is extremely difficult to analyze the trade-offs when there are multiple fairness parameters and other crucial metrics involved, coupled, and even in conflict with one another. This paper uses causality analysis as a principled method for analyzing trade-offs between fairness parameters and other crucial metrics in ML pipelines. To practically and effectively conduct causality analysis, we propose a set of domain-specific optimizations to facilitate accurate causal discovery and a unified, novel interface for trade-off analysis based on well-established causal inference methods. We conduct a comprehensive empirical study using three real-world datasets on a collection of widely-used fairness-improving techniques. Our study obtains actionable suggestions for users and developers of fair ML. We further demonstrate the versatile usage of our approach in selecting the optimal fairness-improving method, paving the way for more ethical and socially responsible AI technologies.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00105"}, {"primary_key": "1220037", "vector": [], "sparse_vector": [], "title": "Vision-Based Widget Mapping for Test Migration Across Mobile Platforms: Are We There Yet?", "authors": ["Ruihua Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "Chunyang Chen", "Minxue Pan", "<PERSON><PERSON>"], "summary": "Automated GUI testing through the reuse of existing tests has recently gained prominence in research. Cross-platform migration of GUI tests between different platform versions of an application offers a promising opportunity for test reuse. Widget mapping, identifying similarities between source and target application widgets and connecting semantically analogous pairs, is central to these approaches. Vision-based widget mapping approaches are supposed to provide platform-agnostic solutions more suitable for cross-platform migration, considering that different platform versions frequently display strong resemblances in the appearance of their semantically similar widgets. However, the efficacy of vision-based widget mapping for cross-platform migration remains limited and the reasons remain unclear. In this paper, we present the first comprehensive investigation of vision-based widget mapping for cross-platform GUI test migration. We devote considerable effort to constructing a dataset consisting of 6,730 bi-directional mapped widget pairs across the iOS and Android platforms, and categorize the mapped widgets into eight classifications to thoroughly assess the capabilities of various approaches. We implement 89 configurations, derived from five distinct vision-based widget mapping methodologies, and evaluate their performance utilizing our dataset. Our findings reveal valuable insights that can be employed to advance vision-based widget mapping techniques: (1) The current approach exhibits potential for improvement, as certain configurations demonstrate superior performance in comparison to existing methods; (2) Some features can adversely impact the mapping, requiring more consideration; (3) A substantial proportion of mapped widgets display varying inconsistent contents in their appearance, which require more sophisticated vision algorithms.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00068"}, {"primary_key": "1220038", "vector": [], "sparse_vector": [], "title": "PSMT: Satisfiability Modulo Theories Meets Probability Distribution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SMT (Satisfiability Modulo Theories) has been widely used in program verification, analysis, and test generation. But sometimes, SMT solver outputs incomprehensible solutions, especially for practical instances. Besides, due to the design of the deterministic algorithms, for a given formula, the result of each run is the same. In this paper, we concentrate on combining SMT solving with probability, which will instruct the SMT solver to give some plausible solutions. We define a special problem: PSMT, which allows solving an SMT instance with variables conforming to a certain distribution. We define distribution under constraint for PSMT, which is based on MCSAT (Model Constructing Satisfiability), a mainstream SMT-solving algorithm. We propose the Prob-MCSAT algorithm, which combines the MCSAT algorithm and introduces the probability to variables. The visualized examples show that the resulting assignments will form a clear trend based on Prob-SMT.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00183"}, {"primary_key": "1220039", "vector": [], "sparse_vector": [], "title": "SCPatcher: Mining Crowd Security Discussions to Enrich Secure Coding Practices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure coding practices (SCPs) have been proposed to guide software developers to write code securely to prevent potential security vulnerabilities. Yet, they are typically one-sentence principles without detailed specifications, e.g., \"Properly free allocated memory upon the completion of functions and at all exit points.\", which makes them difficult to follow in practice, especially for software developers who are not yet experienced in secure programming. To address this problem, this paper proposes SCPatcher, an automated approach to enrich secure coding practices by mining crowd security discussions on online knowledge-sharing platforms, such as Stack Overflow. In particular, for each security post, SCPatcher first extracts the area of coding examples and coding explanations with a fix-prompt tuned Large Language Model (LLM) via Prompt Learning. Then, it hierarchically slices the lengthy code into coding examples and summarizes the coding explanations with the areas. Finally, SCPatcher matches the CWE and Public SCP, integrating them with extracted coding examples and explanations to form the SCP specifications, which are the wild SCPs with details, proposed by the developers. To evaluate the performance of SCPatcher, we conduct experiments on 3,907 security posts from Stack Overflow. The experimental results show that SCPatcher outperforms all baselines in extracting the coding examples with 2.73 % MLine on average, as well as coding explanations with 3.97 % F1 on average. Moreover, we apply SCPatcher on 447 new security posts to further evaluate its practicality, and the extracted SCP specifications enrich the public SCPs with 3,074 lines of code and 1,967 sentences.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00040"}, {"primary_key": "1220040", "vector": [], "sparse_vector": [], "title": "Effective Concurrency Testing for Go via Directional Primitive-Constrained Interleaving Exploration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Go language (Go/Golang) has been attracting increasing attention from the industry over recent years due to its strong concurrency support and ease of deployment. This programming language encourages developers to use channel-based concurrency, which simplifies the development of concurrent programs. Unfortunately, it also introduces new concurrency problems that differ from those caused by the mechanism of shared memory concurrency. However, there are only few works that aim to detect such Go-specific concurrency issues. Even state-of-the-art testing tools will miss critical concurrent bugs that require fine-grained and effective interleaving exploration. This paper presents GoPie, a novel testing approach for detecting Go concurrency bugs through primitive-constrained interleaving exploration. GoPie utilizes execution histories to identify new interleavings instead of relying on exhaustive exploration or random scheduling. To evaluate its performance, we applied GoPie to existing benchmarks and large-scale open-source projects. Results show that GoPie can effectively explore concurrent interleavings and detect significantly more bugs in the benchmark. Furthermore, it uncovered 11 unique previously unknown concurrent bugs, and 9 of which have been confirmed.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00086"}, {"primary_key": "1220041", "vector": [], "sparse_vector": [], "title": "Revealing Performance Issues in Server-Side WebAssembly Runtimes Via Differential Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "WebAssembly (Wasm) is a bytecode format originally serving as a compilation target for Web applications. It has recently been used increasingly on the server side, e.g., providing a safer, faster, and more portable alternative to Linux containers. With the popularity of server-side Wasm applications, it is essential to study performance issues (i.e., abnormal latency) in Wasm runtimes, as they may cause a significant impact on server-side applications. However, there is still a lack of attention to performance issues in server-side Wasm runtimes. In this paper, we design a novel differential testing approach WarpDiff to identify performance issues in server-side Wasm runtimes. The key insight is that in normal cases, the execution time of the same test case on different Wasm runtimes should follow an oracle ratio. We identify abnormal cases where the execution time ratio significantly deviates from the oracle ratio and subsequently locate the Wasm runtimes that cause the performance issues. We apply WarpDiff to test five popular server-side Wasm runtimes using 123 test cases from the LLVM test suite and demonstrate the top 10 abnormal cases we identified. We further conduct an in-depth analysis of these abnormal cases and summarize seven performance issues, all of which have been confirmed by the developers. We hope our work can inspire future investigation on improving Wasm runtime implementation and thus promoting the development of server-side Wasm applications.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00088"}, {"primary_key": "1220042", "vector": [], "sparse_vector": [], "title": "On the Evaluation of Neural Code Translation: Taxonomy and Benchmark.", "authors": ["Mingsheng <PERSON>", "Ting<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiaodong Gu", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, neural code translation has gained increasing attention. While most of the research focuses on improving model architectures and training processes, we notice that the evaluation process and benchmark for code translation models are severely limited: they primarily treat source code as natural languages and provide a holistic accuracy score while disregarding the full spectrum of model capabilities across different translation types and complexity. In this paper, we present a comprehensive investigation of four state-of-the-art models and analyze in-depth the advantages and limitations of three existing benchmarks. Based on the empirical results, we develop a taxonomy that categorizes code translation tasks into four primary types according to their complexity and knowledge dependence: token level (type 1), syntactic level (type 2), library level (type 3), and algorithm level (type 4). We then conduct a thorough analysis of how existing approaches perform across these four categories. Our findings indicate that while state-of-the-art code translation models excel in type-1 and type-2 translations, they struggle with knowledge-dependent ones such as type-3 and type-4. Existing benchmarks are biased towards trivial translations, such as keyword mapping. To overcome these limitations, we construct G-TransEval, a new benchmark by manually curating type-3 and type-4 translation pairs and unit test cases. Results on our new benchmark suggest that G-TransEval can exhibit more comprehensive and finer-grained capability of code translation models and thus provide a more rigorous evaluation. Our studies also provide more insightful findings and suggestions for future research, such as building type-3 and type-4 training data and ensembling multiple pretraining approaches.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00114"}, {"primary_key": "1220043", "vector": [], "sparse_vector": [], "title": "Pluggable Type Inference for Free.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A pluggable type system extends a host programming language with type qualifiers. It lets programmers write types like unsigned int, secret string, and nonnull object. Typechecking with pluggable types detects and prevents more errors than the host type system. However, programmers must write type qualifiers; this is the biggest obstacle to use of pluggable types in practice. Type inference can solve this problem. Traditional approaches to type inference are type-system-specific: for each new pluggable type system, the type inference algorithm must be extended to build and then solve a system of constraints corresponding to the rules of the underlying type system. We propose a novel type inference algorithm that can infer type qualifiers for any pluggable type system with little to no new type-system-specific code-that is, \"for free\". The key insight is that extant practical pluggable type systems are flow-sensitive and therefore already implement local type inference. Using this insight, we can derive a global inference algorithm by re-using existing implementations of local inference. Our algorithm runs iteratively in rounds. Each round uses the results of local type inference to produce summaries (specifications) for procedures and fields. These summaries enable improved inference throughout the program in subsequent rounds. The algorithm terminates when the inferred summaries reach a fixed point. In practice, many pluggable type systems are built on frameworks. By implementing our algorithm once, at the framework level, it can be reused by any typechecker built using that frame-work. Using that insight, we have implemented our algorithm for the open-source Checker Framework project, which is widely used in industry and on which dozens of specialized pluggable typecheckers have been built. In experiments with 11 distinct pluggable type systems and 12 projects, our algorithm reduced, by 45 % on average, the number of warnings that developers must resolve by writing annotations.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00186"}, {"primary_key": "1220044", "vector": [], "sparse_vector": [], "title": "Towards Safe Automated Refactoring of Imperative Deep Learning Programs to Graph Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Efficiency is essential to support responsiveness w.r.t. ever-growing datasets, especially for Deep Learning (DL) systems. DL frameworks have traditionally embraced deferred execution-style DL code-supporting symbolic, graph-based Deep Neural Network (DNN) computation. While scalable, such development is error-prone, non-intuitive, and difficult to debug. Consequently, more natural, imperative DL frameworks encouraging eager execution have emerged at the expense of run-time performance. Though hybrid approaches aim for the \"best of both worlds,\" using them effectively requires subtle considerations to make code amenable to safe, accurate, and efficient graph execution. We present our ongoing work on automated refactoring that assists developers in specifying whether and how their otherwise eagerly-executed imperative DL code could be reliably and efficiently executed as graphs while preserving semantics. The approach, based on a novel imperative tensor analysis, will automatically determine when it is safe and potentially advantageous to migrate imperative DL code to graph execution and modify decorator parameters or eagerly executing code already running as graphs. The approach is being implemented as a PyDev Eclipse IDE plug-in and uses the WALA Ariadne analysis framework. We discuss our ongoing work towards optimizing imperative DL code to its full potential.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00187"}, {"primary_key": "1220045", "vector": [], "sparse_vector": [], "title": "Adaptive REST API Testing with Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern web services increasingly rely on REST APIs. Effectively testing these APIs is challenging due to the vast search space to be explored, which involves selecting API operations for sequence creation, choosing parameters for each operation from a potentially large set of parameters, and sampling values from the virtually infinite parameter input space. Current testing tools lack efficient exploration mechanisms, treating all operations and parameters equally (i.e., not considering their importance or complexity) and lacking prioritization strategies. Furthermore, these tools struggle when response schemas are absent in the specification or exhibit variants. To address these limitations, we present an adaptive REST API testing technique that incorporates reinforcement learning to prioritize operations and parameters during exploration. Our approach dynamically analyzes request and response data to inform dependent parameters and adopts a sampling-based strategy for efficient processing of dynamic API feedback. We evaluated our technique on ten RESTful services, comparing it against state-of-the-art REST testing tools with respect to code coverage achieved, requests generated, operations covered, and service failures triggered. Additionally, we performed an ablation study on prioritization, dynamic feedback analysis, and sampling to assess their individual effects. Our findings demonstrate that our approach outperforms existing REST API testing tools in terms of effectiveness, efficiency, and fault-finding ability.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00218"}, {"primary_key": "1220046", "vector": [], "sparse_vector": [], "title": "Bus Factor Explorer.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bus factor (BF) is a metric that tracks knowledge distribution in a project. It is the minimal number of engineers that have to leave for a project to stall. Despite the fact that there are several algorithms for calculating the bus factor, only a few tools allow easy calculation of bus factor and convenient analysis of results for projects hosted on Git-based providers. We introduce Bus Factor Explorer, a web application that provides an interface and an API to compute, export, and explore the Bus Factor metric via treemap visualization, simulation mode, and chart editor. It supports repositories hosted on GitHub and enables functionality to search repositories in the interface and process many repositories at the same time. Our tool allows users to identify the files and subsystems at risk of stalling in the event of developer turnover by analyzing the VCS history. The application and its source code are publicly available on GitHub at https://github.com/JetBrains-Research/bus-factor-explorer. The demonstration video can be found on YouTube: https://youtu.be/uIoV79N14z8", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00015"}, {"primary_key": "1220048", "vector": [], "sparse_vector": [], "title": "Towards Self-Adaptive Machine Learning-Enabled Systems Through QoS-Aware Model Switching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine Learning (ML), particularly deep learning, has seen vast advancements, leading to the rise of Machine Learning-Enabled Systems (MLS). However, numerous software engineering challenges persist in propelling these MLS into production, largely due to various run-time uncertainties that impact the overall Quality of Service (QoS). These uncertainties emanate from ML models, software components, and environmental factors. Self-adaptation techniques present potential in managing run-time uncertainties, but their application in MLS remains largely unexplored. As a solution, we propose the concept of a Machine Learning Model Balancer, focusing on managing uncertainties related to ML models by using multiple models. Subsequently, we introduce AdaMLS, a novel self-adaptation approach that leverages this concept and extends the traditional MAPE-K loop for continuous MLS adaptation. AdaMLS employs lightweight unsupervised learning for dynamic model switching, thereby ensuring consistent QoS. Through a self-adaptive object detection system prototype, we demonstrate AdaMLS's effectiveness in balancing system and model performance. Preliminary results suggest AdaMLS surpasses naive and single state-of-the-art models in QoS guarantees, heralding the advancement towards self-adaptive MLS with optimal OoS in dynamic environments.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00172"}, {"primary_key": "1220049", "vector": [], "sparse_vector": [], "title": "Scalable Industrial Control System Analysis via XAI-Based Gray-Box Fuzzing.", "authors": ["<PERSON>", "Jing<PERSON> Chen", "<PERSON>"], "summary": "Conventional approaches to analyzing industrial control systems have relied on either white-box analysis or black-box fuzzing. However, white-box methods rely on sophisticated domain expertise, while black-box methods suffers from state explosion and thus scales poorly when analyzing real ICS involving a large number of sensors and actuators. To address these limitations, we propose XAI-based gray-box fuzzing, a novel approach that leverages explainable AI and machine learning modeling of ICS to accurately identify a small set of actuators critical to ICS safety, which result in significant reduction of state space without relying on domain expertise. Experiment results show that our method accurately explains the ICS model and significantly speeds-up fuzzing by 64x when compared to conventional black-box methods.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00161"}, {"primary_key": "1220050", "vector": [], "sparse_vector": [], "title": "Thunderkaller: Profiling and Improving the Performance of <PERSON><PERSON><PERSON><PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fuzzing is widely adopted to discover vulnerabilities in software, including the kernel. One of the most popular and state-of-the-art fuzzers for kernels is Syzkaller. However, Syzkaller has a much lower testing throughput compared to other user-space fuzzers, which affects the efficiency of both Syzkaller and other Syzkaller-based fuzzers. In this paper, we profiled the performance of <PERSON>yz<PERSON><PERSON>, recognized that the major cost comes from program isolation and kernel instrumentation, and then proposed kernel image duplication and three optimization techniques to mitigate such overheads and present the solution Thunderkaller. Our solution does not change or depend on the fuzzing algorithm in any way, orthogonal to other refinements to Syzkaller. Our evaluation shows that, in 24 hours, Thunderkaller speeds up 2.8× compared to vanilla Syzkaller, achieves 25.8% more basic block coverage, detects 21 more unique bugs, and triggers the common bugs 6.3× faster. In a long time of fuzzing, we have found 6 unique Linux kernel bugs and obtained a CVE.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00124"}, {"primary_key": "1220051", "vector": [], "sparse_vector": [], "title": "Log Parsing: How Far Can ChatGPT Go?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software logs play an essential role in ensuring the reliability and maintainability of large-scale software systems, as they are often the sole source of runtime information. Log parsing, which converts raw log messages into structured data, is an important initial step towards downstream log analytics. In recent studies, ChatGPT, the current cutting-edge large language model (LLM), has been widely applied to a wide range of software engineering tasks. However, its performance in automated log parsing remains unclear. In this paper, we evaluate ChatGPT's ability to undertake log parsing by addressing two research questions. (1) Can ChatGPT effectively parse logs? (2) How does ChatGPT perform with different prompting methods? Our results show that ChatGPT can achieve promising results for log parsing with appropriate prompts, especially with few-shot prompting. Based on our findings, we outline several challenges and opportunities for ChatGPT-based log parsing.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00206"}, {"primary_key": "1220052", "vector": [], "sparse_vector": [], "title": "Fuzzing for CPS Mutation Testing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mutation testing can help reduce the risks of releasing faulty software. For such reason, it is a desired practice for the development of embedded software running in safety-critical cyber-physical systems (CPS). Unfortunately, state-of-the-art test data generation techniques for mutation testing of C and C++ software, two typical languages for CPS software, rely on symbolic execution, whose limitations often prevent its application (e.g., it cannot test black-box components). We propose a mutation testing approach that leverages fuzz testing, which has proved effective with C and C++ software. Fuzz testing automatically generates diverse test inputs that exercise program branches in a varied number of ways and, therefore, exercise statements in different program states, thus maximizing the likelihood of killing mutants, our objective. We performed an empirical assessment of our approach with software components used in satellite systems currently in orbit. Our empirical evaluation shows that mutation testing based on fuzz testing kills a significantly higher proportion of live mutants than symbolic execution (i.e., up to an additional 47 percentage points). Further, when symbolic execution cannot be applied, fuzz testing provides significant benefits (i.e., up to 41% mutants killed). Our study is the first one comparing fuzz testing and symbolic execution for mutation testing; our results provide guidance towards the development of fuzz testing tools dedicated to mutation testing.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00079"}, {"primary_key": "1220053", "vector": [], "sparse_vector": [], "title": "Maat: Performance Metric Anomaly Anticipation for Cloud Services with Conditional Diffusion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ensuring the reliability and user satisfaction of cloud services necessitates prompt anomaly detection followed by diagnosis. Existing techniques for anomaly detection focus solely on real-time detection, meaning that anomaly alerts are issued as soon as anomalies occur. However, anomalies can propagate and escalate into failures, making faster-than-real-time anomaly detection highly desirable for expediting downstream analysis and intervention. This paper proposes Maat, the first work to address anomaly anticipation of performance metrics in cloud services. Maat adopts a novel two-stage paradigm for anomaly anticipation, consisting of metric forecasting and anomaly detection on forecasts. The metric forecasting stage employs a conditional denoising diffusion model to enable multi-step forecasting in an auto-regressive manner. The detection stage extracts anomaly-indicating features based on domain knowledge and applies isolation forest with incremental learning to detect upcoming anomalies. Thus, our method can uncover anomalies that better conform to human expertise. Evaluation on three publicly available datasets demonstrates that Maat can anticipate anomalies faster than real-time comparatively or more effectively compared with state-of-the-art real-time anomaly detectors. We also present cases highlighting Maat's success in forecasting abnormal metrics and discovering anomalies.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00082"}, {"primary_key": "1220054", "vector": [], "sparse_vector": [], "title": "On Automated Assistants for Software Development: The Role of LLMs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Software developers handle many complex tasks that include gathering and applying domain knowledge, coordinating subtasks, designing interfaces, turning ideas into elegant code, and more. They must switch contexts between these tasks, incurring more cognitive costs. Recent advances in large language models (LLMs) open up new possibilities for moving beyond the support provided by automated assistants (AAs) available today. In this paper, we explore if a human memory model can provide a framework for the systematic investigation of AAs for software development based on LLMs and other new technologies.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00035"}, {"primary_key": "1220055", "vector": [], "sparse_vector": [], "title": "Are They All Good? Studying Practitioners&apos; Expectations on the Readability of Log Messages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Developers write logging statements to generate logs that provide run-time information for various tasks. The readability of log messages in the logging statements (i.e., the descriptive text) is rather crucial to the value of the generated logs. Immature log messages may slow down or even obstruct the process of log analysis. Despite the importance of log messages, there is still a lack of standards on what constitutes good readability of log messages and how to write them. In this paper, we conduct a series of interviews with 17 industrial practitioners to investigate their expectations on the readability of log messages. Through the interviews, we derive three aspects related to the readability of log messages, including Structure, Information, and Wording, along with several specific practices to improve each aspect. We validate our findings through a series of online questionnaire surveys and receive positive feedback from the participants. We then manually investigate the readability of log messages in large-scale open source systems and find that a large portion (38.1%) of the log messages have inadequate readability. Motivated by such observation, we further explore the potential of automatically classifying the readability of log messages using deep learning and machine learning models. We find that both deep learning and machine learning models can effectively classify the readability of log messages with a balanced accuracy above 80.0% on average. Our study provides comprehensive guidelines for composing log messages to further improve practitioners' logging practices.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00136"}, {"primary_key": "1220056", "vector": [], "sparse_vector": [], "title": "HOBAT: Batch Verification for Homogeneous Structural Neural Networks.", "authors": ["Jingyang Li", "<PERSON><PERSON><PERSON>"], "summary": "The rapid development of deep learning has significantly transformed the ecology of the software engineering field. As new data continues to grow and evolve at an explosive rate, the challenge of iteratively updating software built on neural networks has become a critical issue. While the continuous learning paradigm enables networks to incorporate new data and update accordingly without losing previous memories, resulting in a batch of new networks as candidates for software updating, these approaches merely select from these networks by empirically testing their accuracy; they lack formal guarantees for such a batch of networks, especially in the presence of adversarial samples. Existing verification techniques, based on constraint solving, interval propagation, and linear approximation, provide formal guarantees but are designed to verify the properties of individual networks rather than a batch of networks. To address this issue, we analyze the batch verification problem corresponding to several non-traditional machine learning paradigms and further propose a framework named HOBAT (BATch verification for HOmogeneous structural neural networks) to enhance batch verification under reasonable assumptions about the representation of homogeneous structure neural networks, increasing scalability in practical applications. Our method involves abstracting the neurons at the same position in a batch of networks into a single neuron, followed by an iterative refinement process on the abstracted neuron to restore the precision until the desired properties for verification are met. Our method is orthogonal to boundary propagation verification on a single neural network. To assess our methodology, we integrate it with boundary propagation verification and observe significant improvements compared to the vanilla approach. Our experiments demonstrate the enormous potential for verifying large batches of networks in the era of big data.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00033"}, {"primary_key": "1220057", "vector": [], "sparse_vector": [], "title": "ZC3: Zero-Shot Cross-Language Code Clone Detection.", "authors": ["<PERSON><PERSON>", "Chongyang Tao", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ge Li"], "summary": "Developers introduce code clones to improve programming productivity. Many existing studies have achieved impressive performance in monolingual code clone detection. However, during software development, more and more developers write semantically equivalent programs with different languages to support different platforms and help developers translate projects from one language to another. Considering that collecting cross-language parallel data, especially for low-resource languages, is expensive and time-consuming, how designing an effective cross-language model that does not rely on any parallel data is a significant problem. In this paper, we propose a novel method named ZC 3 for Z_ero-shot Cross-language Code Clone detection. ZC 3 designs the contrastive snippet prediction to form an isomorphic representation space among different programming languages. Based on this, ZC 3 exploits domain-aware learning and cycle consistency learning to further constrain the model to generate representations that are aligned among different languages meanwhile are diacritical for different types of clones. To evaluate our approach, we conduct extensive experiments on four representative cross-language clone detection datasets. Experimental results show that ZC 3 outperforms the state-of-the-art baselines by 67.12%, 51.39%, 14.85%, and 53.01% on the MAP score, respectively. We further investigate the representational distribution of different languages and discuss the effectiveness of our method.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00210"}, {"primary_key": "1220058", "vector": [], "sparse_vector": [], "title": "MalWuKong: Towards Fast, Accurate, and Multilingual Detection of Malicious Code Poisoning in OSS Supply Chains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ming<PERSON> Feng", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "In the face of increased threats within software registries and management systems, we address the critical need for effective malicious code detection. In this paper, we propose an innovative approach that integrates source code slicing, inter-procedural analysis, and cross-file inter-procedural analysis, thereby enhancing the detection precision and reducing false positives. This approach has been encapsulated within a multi-analysis-based framework for automatic detection of malicious code in real-world software packages. In its application to major third-party software registries like PyPI and NPM, our framework has proven effective, identifying 130 malicious packages from a total of 169,640 monitored over a continuous period of five weeks. This work advances the current state-of-the-art solution to malicious code detection, demonstrating significant practical impact in strengthening the software supply chain defense.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00073"}, {"primary_key": "1220059", "vector": [], "sparse_vector": [], "title": "A Large-Scale Empirical Study on Semantic Versioning in Golang Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Cai Fu", "<PERSON>"], "summary": "Third-party libraries (TPLs) have become an essential component of software, accelerating development and reducing maintenance costs. However, breaking changes often occur during the upgrades of TPLs and prevent client programs from moving forward. Semantic versioning (SemVer) has been applied to standardize the versions of releases according to compatibility, but not all releases follow SemVer compliance. Lots of work focuses on SemVer compliance in ecosystems such as Java and JavaScript beyond Golang (Go for short). Due to the lack of tools to detect breaking changes and dataset for Go, developers of TPLs do not know if breaking changes occur and affect client programs, and developers of client programs may hesitate to upgrade dependencies in terms of breaking changes. To bridge this gap, we conduct the first large-scale empirical study in the Go ecosystem to study SemVer compliance in terms of breaking changes and their impact. In detail, we propose GoSVI (Go Semantic Versioning Insight) to detect breaking changes and analyze their impact by resolving identifiers in client programs and comparing their types with breaking changes. Moreover, we collect the first large-scale Go dataset with a dependency graph from GitHub, including 124K TPLs and 532K client programs. Based on the dataset, our results show that 86.3% of library upgrades follow SemVer compliance and 28.6% of no-major upgrades introduce breaking changes. Furthermore, the tendency to comply with SemVer has improved over time from 63.7% in 2018/09 to 92.2% in 2023/03. Finally, we find 33.3% of downstream client programs may be affected by breaking changes. These findings provide developers and users of TPLs with valuable insights to help make decisions related to SemVer.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00140"}, {"primary_key": "1220060", "vector": [], "sparse_vector": [], "title": "Contextuality of Code Representation Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Advanced machine learning models (ML) have been successfully leveraged in several software engineering (SE) applications. The existing SE techniques have used the embedding models ranging from static to contextualized ones to build the vectors for program units. The contextualized vectors address a phenomenon in natural language texts called polysemy, which is the coexistence of different meanings of a word/phrase. However, due to different nature, program units exhibit the nature of mixed polysemy. Some code tokens and statements exhibit polysemy while other tokens (e.g., keywords, separators, and operators) and statements maintain the same meaning in different contexts. A natural question is whether static or contextualized embeddings fit better with the nature of mixed polysemy in source code. The answer to this question is helpful for the SE researchers in selecting the right embedding model. We conducted experiments on 12 popular sequence-/tree-/graph-based embedding models and on the samples of a dataset of 10,222 Java projects with +14M methods. We present several contextuality evaluation metrics adapted from natural-language texts to code structures to evaluate the embeddings from those models. Among several findings, we found that the models with higher contextuality help a bug detection model perform better than the static ones. Neither static nor contextualized embedding models fit well with the mixed polysemy nature of source code. Thus, we develop Hycode, a hybrid embedding model that fits better with the nature of mixed polysemy in source code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00029"}, {"primary_key": "1220061", "vector": [], "sparse_vector": [], "title": "Generative Model-Based Testing on Decision-Making Policies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The reliability of decision-making policies is urgently important today as they have established the fundamentals of many critical applications, such as autonomous driving and robotics. To ensure reliability, there have been a number of research efforts on testing decision-making policies that solve Markov decision processes (MDPs). However, due to the deep neural network (DNN)-based inherit and infinite state space, developing scalable and effective testing frameworks for decision-making policies still remains open and challenging. In this paper, we present an effective testing framework for decision-making policies. The framework adopts a generative diffusion model-based test case generator that can easily adapt to different search spaces, ensuring the practicality and validity of test cases. Then, we propose a termination state novelty-based guidance to diversify agent behaviors and improve the test effectiveness. Finally, we evaluate the framework on five widely used benchmarks, including autonomous driving, aircraft collision avoidance, and gaming scenarios. The results demonstrate that our approach identifies more diverse and influential failure-triggering test cases compared to current state-of-the-art techniques. Moreover, we employ the detected failure cases to repair the evaluated models, achieving better robustness enhancement compared to the baseline method.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00153"}, {"primary_key": "1220062", "vector": [], "sparse_vector": [], "title": "LiSum: Open Source Software License Summarization with Multi-Task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xiangrui <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open source software (OSS) licenses regulate the conditions under which users can reuse, modify, and distribute the software legally. However, there exist various OSS licenses in the community, written in a formal language, which are typically long and complicated to understand. In this paper, we conducted a 661-participants online survey to investigate the perspectives and practices of developers towards OSS licenses. The user study revealed an indeed need for an automated tool to facilitate license understanding. Motivated by the user study and the fast growth of licenses in the community, we propose the first study towards automated license summarization. Specifically, we released the first high quality text summarization dataset and designed two tasks, i.e., license text summarization (LTS), aiming at generating a relatively short summary for an arbitrary license, and license term classification (LTC), focusing on the attitude inference towards a predefined set of key license terms (e.g., Distribute). Aiming at the two tasks, we present LiSum, a multi-task learning method to help developers overcome the obstacles of understanding OSS licenses. Comprehensive experiments demonstrated that the proposed jointly training objective boosted the performance on both tasks, surpassing state-of-the-art baselines with gains of at least 5 points w.r.t. F1 scores of four summarization metrics and achieving 95.13% micro average F1 score for classification simultaneously. We released all the datasets, the replication package, and the questionnaires for the community.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00150"}, {"primary_key": "1220063", "vector": [], "sparse_vector": [], "title": "Nuances are the Key: Unlocking ChatGPT to Find Failure-Inducing Tests with Differential Prompting.", "authors": ["Tsz On Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automated detection of software failures is an important but challenging software engineering task. It involves finding in a vast search space the failure-inducing test cases that contain an input triggering the software fault and an oracle asserting the incorrect execution. We are motivated to study how far this outstanding challenge can be solved by recent advances in large language models (LLMs) such as ChatGPT. However, our study reveals that ChatGPT has a relatively low success rate (28.8%) in finding correct failure-inducing test cases for buggy programs. A possible conjecture is that finding failure-inducing test cases requires analyzing the subtle differences (nuances) between the tokens of a program's correct version and those for its buggy version. When these two versions have similar sets of tokens and attentions, ChatGPT is weak in distinguishing their differences. We find that ChatGPT can successfully generate failure-inducing test cases when it is guided to focus on the nuances. Our solution is inspired by an interesting observation that ChatGPT could infer the intended functionality of buggy code if it is similar to the correct version. Driven by the inspiration, we develop a novel technique, called Differential Prompting, to effectively find failure-inducing test cases with the help of the compilable code synthesized by the inferred intention. Prompts are constructed based on the nuances between the given version and the synthesized code. We evaluate Differential Prompting on Quixbugs (a popular benchmark of buggy programs) and recent programs published at Codeforces (a popular programming contest portal, which is also an official benchmark of ChatGPT). We compare Differential Prompting with two baselines constructed using conventional ChatGPT prompting and Pynguin (the state-of-the-art unit test generation tool for Python programs). Our evaluation results show that for programs of Quixbugs, Differential Prompting can achieve a success rate of 75.0% in finding failure-inducing test cases, outperforming the best baseline by 2.6X. For programs of Codeforces, Differential Prompting's success rate is 66.7%, outperforming the best baseline by 4.0X.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00089"}, {"primary_key": "1220064", "vector": [], "sparse_vector": [], "title": "Robin: A Novel Method to Produce Robust Interpreters for Deep Learning-Based Code Classifiers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Deqing Zou", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep learning has been widely used in source code classification tasks, such as code classification according to their functionalities, code authorship attribution, and vulnerability detection. Unfortunately, the black-box nature of deep learning makes it hard to interpret and understand why a classifier (i.e., classification model) makes a particular prediction on a given example. This lack of interpretability (or explainability) might have hindered their adoption by practitioners because it is not clear when they should or should not trust a classifier's prediction. The lack of interpretability has motivated a number of studies in recent years. However, existing methods are neither robust nor able to cope with out-of-distribution examples. In this paper, we propose a novel method to produce Robust interpreters for a given deep learning-based code classifier; the method is dubbed Robin. The key idea behind <PERSON> is a novel hybrid structure combining an interpreter and two approximators, while leveraging the ideas of adversarial training and data augmentation. Experimental results show that on average the interpreter produced by <PERSON> achieves a 6.11% higher fidelity (evaluated on the classifier), 67.22% higher fidelity (evaluated on the approximator), and 15.87x higher robustness than that of the three existing interpreters we evaluated. Moreover, the interpreter is 47.31% less affected by out-of-distribution examples than that of LEMNA.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00164"}, {"primary_key": "1220065", "vector": [], "sparse_vector": [], "title": "A Needle is an Outlier in a Haystack: Hunting Malicious PyPI Packages with Code Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jingzheng Wu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As the most popular Python software repository, PyPI has become an indispensable part of the Python ecosystem. Regrettably, the open nature of PyPI exposes end-users to substantial security risks stemming from malicious packages. Consequently, the timely and effective identification of malware within the vast number of newly-uploaded PyPI packages has emerged as a pressing concern. Existing detection methods are dependent on difficult-to-obtain explicit knowledge, such as taint sources, sinks, and malicious code patterns, rendering them susceptible to overlooking emergent malicious packages. In this paper, we present a lightweight and effective method, namely MPHunter, to detect malicious packages without requiring any explicit prior knowledge. MPHunter is founded upon two fundamental and insightful observations. First, malicious packages are considerably rarer than benign ones, and second, the functionality of installation scripts for malicious packages diverges significantly from those of benign packages, with the latter frequently forming clusters. Consequently, MPHunter utilizes clustering techniques to group the installation scripts of PyPI packages and identifies outliers. Subsequently, MPHunter ranks the outliers according to their outlierness and the distance between them and known malicious instances, thereby effectively highlighting potential evil packages. With MPHunter, we successfully identified 60 previously unknown malicious packages from a pool of 31,329 newly-uploaded packages over a two-month period. All of them have been confirmed by the PyPI official. Moreover, a manual analysis shows that MPHunter recognizes all potentially malicious installation scripts with a recall of 100% across all analyzed packages. We assert that MPHunter offers a valuable and advantageous supplement to existing detection techniques, augmenting the arsenal of software supply chain security analysis.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00085"}, {"primary_key": "1220066", "vector": [], "sparse_vector": [], "title": "Automated Fixing of Web UI Tests via Iterative Element Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Web UI test cases are used for the automatic testing of web applications. When a web application is updated, these UI tests should also be updated for regression testing of the new version of web application. With the rapid evolution, updating UI tests is a tedious and time-consuming task. To solve these problems, automatically repairing web UI tests has gained increasing attention recently. To repair web UI tests, the most important step is to match the UI elements before and after the web page update. Existing work matches UI elements according to visual information, attributes value, or Document Object Model (DOM) structures. However, they either achieve low element matching accuracy or only work on simple UI tests. To solve these problems, we proposed UITestFix, an approach based on a novel iterative matching algorithm for improving the accuracy of matching UI elements. UITestFix is designed based on two main insights: (1) beyond attribute and DOM structures, the relations between different elements can also guide the matching process, and (2) the matching results of previous iterations could guide the matching of the current iteration. Our evaluation of publicly available datasets and two industrial apps shows that UITestFix outperforms four existing approaches by achieving more accurate element matching and producing more correct fixes.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00048"}, {"primary_key": "1220067", "vector": [], "sparse_vector": [], "title": "ReuNify: A Step Towards Whole Program Analysis for React Native Android Apps.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chunyang Chen", "<PERSON>"], "summary": "React Native is a widely-used open-source frame-work that facilitates the development of cross-platform mobile apps. The framework enables JavaScript code to interact with native-side code, such as Objective-C/Swift for iOS and Java/Kotlin for Android, via a communication mechanism provided by React Native. However, previous research and tools have overlooked this mechanism, resulting in incomplete analysis of React Native app code. To address this limitation, we have developed REUNIFY, a prototype tool that integrates the JavaScript and native-side code of React Native apps into an intermediate language that can be processed by the Soot static analysis framework. By doing so, REUNIFY enables the generation of a comprehensive model of the app's behavior. Our evaluation indicates that, by leveraging REUNIFY, the Soot-based framework can improve its coverage of static analysis for the 1,007 most popular React Native Android apps, augmenting the number of lines of Jimple code by 70%. Additionally, we observed an average increase of 84% in new nodes reached in the callgraph for these apps, after integrating REUNIFY. When REUNIFY is used for taint flow analysis, an average of two additional privacy leaks were identified. Overall, our results demonstrate that REUNIFY significantly enhances the Soot-based framework's capability to analyze React Native Android apps.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00113"}, {"primary_key": "1220068", "vector": [], "sparse_vector": [], "title": "VD-Guard: DMA Guided Fuzzing for Hypervisor Virtual Device.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Libo Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Purui Su"], "summary": "Virtualization has been widely used in various scenarios, such as cloud computing. As its core technology, virtualization hypervisor brings up the efficiency of sharing the physical machine's resources via virtual devices. However, virtualization hypervisor also introduces significant security risks due to defective design or implementation schemes on virtual devices. Although several methods have been proposed to detect vulnerabilities in virtual devices, they still cannot effectively discover them because of missing critical information related to the MMIO/PIO and DMA operations to guide their dynamic methods. In this paper, we propose a hybrid method, VD-GUARD, to detect vulnerabilities in virtual devices. Specifically, it first leverages static control flow analysis to track call traces from various data entry points of virtual devices (MMIO/PIO functions) to the critical dispatcher points (DMA functions), and generate seeds that can trigger this call trace via static analysis and limited fuzzing test. And then, it takes these seeds as input and leverages DMA guided fuzzing to discover bugs. To verify the effectiveness of Vd-guard, we build a dataset, including 10 bugs in QEMU, based on previous works, and Vd-guardoutperforms the state-of-the-art hypervisor fuzzer Morphuzz. Vd-guardalso has found 4 new vulnerabilities in QEMU and VirtualBox, all of which have been confirmed and fixed (have been assigned 3 CVE IDs).", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00051"}, {"primary_key": "1220069", "vector": [], "sparse_vector": [], "title": "AutoDebloater: Automated Android App Debloating.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>hung", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Android applications are getting bigger with an increasing number of features. However, not all the features are needed by a specific user. The unnecessary features can increase the attack surface and cost additional resources (e.g., storage and memory). Therefore, it is important to remove unnecessary features from Android applications. However, it is difficult for the end users to fully explore the apps to identify the unnecessary features, and there is no off-the-shelf tool available to assist users to debloat the apps by themselves. In this work, we propose AutoDebloater to debloat Android applications automatically for end users. AutoDebloater is a web application that can be accessed by end-users through a web browser. In particular, AutoDebloater can automatically explore an app and identify the transitions between activities. Then, AutoDebloater will present the Activity Transition Graph to users and ask them to select the activities they do not want to keep. Finally, AutoDebloater will remove the activities that are selected by users from the app. We conducted a user study on five Android apps downloaded from three categories (i.e., Finance, Tools, and Navigation) in Google Play and F-Droid. The results show that users are satisfied with AutoDebloater in terms of the stability of the debloated apps and the ability of AutoDebloater to identify features that are never noticed before. The tool is available at http://autodebloater.club. The code is available at https://github.com/jiakun-liu/autodebloater/ and the demonstration video can be found at https://youtu.be/Gmz0-p2n9D4.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00017"}, {"primary_key": "1220070", "vector": [], "sparse_vector": [], "title": "Prism: Revealing Hidden Functional Clusters from Massive Instances in Cloud Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ensuring the reliability of cloud systems is critical for both cloud vendors and customers. Cloud systems often rely on virtualization techniques to create instances of hardware resources, such as virtual machines. However, virtualization hinders the observability of cloud systems, making it challenging to diagnose platform-level issues. To improve system observability, we propose to infer functional clusters of instances, i.e., groups of instances having similar functionalities. We first conduct a pilot study on a large-scale cloud system, i.e., Huawei Cloud, demonstrating that instances having similar functionalities share similar communication and resource usage patterns. Motivated by these findings, we formulate the identification of functional clusters as a clustering problem and propose a non-intrusive solution called Prism. Prism adopts a coarse-to-fine clustering strategy. It first partitions instances into coarse-grained chunks based on communication patterns. Within each chunk, Prism further groups instances with similar resource usage patterns to produce fine-grained functional clusters. Such a design reduces noises in the data and allows Prism to process massive instances efficiently. We evaluate Prism on two datasets collected from the real-world production environment of Huawei Cloud. Our experiments show that Prism achieves a v-measure of ∼0.95, surpassing existing state-of-the-art solutions. Additionally, we illustrate the integration of Prism within monitoring systems for enhanced cloud reliability through two real-world use cases.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00077"}, {"primary_key": "1220071", "vector": [], "sparse_vector": [], "title": "ASTER: Automatic Speech Recognition System Accessibility Testing for Stutterers.", "authors": ["<PERSON>", "Yuekang Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The popularity of automatic speech recognition (ASR) systems nowadays leads to an increasing need for improving their accessibility. Handling stuttering speech is an important feature for accessible ASR systems. To improve the accessibility of ASR systems for stutterers, we need to expose and analyze the failures of ASR systems on stuttering speech. The speech datasets recorded from stutterers are not diverse enough to expose most of the failures. Furthermore, these datasets lack ground truth information about the non-stuttered text, rendering them unsuitable as comprehensive test suites. Therefore, a methodology for generating stuttering speech as test inputs to test and analyze the performance of ASR systems is needed. However, generating valid test inputs in this scenario is challenging. The reason is that although the generated test inputs should mimic how stutterers speak, they should also be diverse enough to trigger more failures. To address the challenge, we propose Aster, a technique for automatically testing the accessibility of ASR systems. <PERSON><PERSON> can generate valid test cases by injecting five different types of stuttering. The generated test cases can both simulate realistic stuttering speech and expose failures in ASR systems. Moreover, <PERSON><PERSON> can further enhance the quality of the test cases with a multi-objective optimization-based seed updating algorithm. We implemented Aster as a framework and evaluated it on four open-source ASR models and three commercial ASR systems. We conduct a comprehensive evaluation of <PERSON><PERSON> and find that it significantly increases the word error rate, match error rate, and word information loss in the evaluated ASR systems. Additionally, our user study demonstrates that the generated stuttering audio is indistinguishable from real-world stuttering audio clips.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00107"}, {"primary_key": "1220072", "vector": [], "sparse_vector": [], "title": "NRAgo: Solving SMT(NRA) Formulas with Gradient-Based Optimization.", "authors": ["<PERSON><PERSON>", "Kunhang Lv", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The satisfiability problem modulo the nonlinear real arithmetic (NRA) theory serves as the foundation for a wide range of important applications, such as model checking, program analysis, and software testing. However, due to the high computational complexity, developing efficient solving algorithms for this problem has consistently presented a substantial challenge. We present a hybrid SMT(NRA) solver, called NRAgo, which combines the efficiency of gradient-based optimization method with the completeness of algebraic solving algorithm. With our approach, the practical performance on many satisfiable instances is substantially improved. The experimental evaluation shows that NRAgo achieves remarkable acceleration effects on a set of challenging SMT(NRA) benchmarks that are hard to solve for state-of-the-art SMT solvers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00013"}, {"primary_key": "1220073", "vector": [], "sparse_vector": [], "title": "Automated Software Entity Matching Between Successive Versions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Version control systems are widely used to manage the evolution of software applications. However, such version control systems take source code as lines of plain text, and thus they cannot present the evolution of software entities embedded in the source code. To this end, a few approaches have been proposed to match software entities before and after a given commit, known as software entity matching algorithms. However, the accuracy of such algorithms requires further improvement. In this paper, we propose an automated iterative algorithm (called ReMapper) to match software entities between two successive versions. The key insight of ReMapper is that the qualified name, the implementation, and the references of a software entity together can distinguish it from others. It matches software entities iteratively because the mapping depends on the reference-based similarity whereas the reference-based similarity depends on the mapping of entities as well. We evaluated ReMapper on a benchmark consisting of 215 commits from 21 real-world projects. Our evaluation results suggest that ReMapper substantially outperformed the state of the art, reducing the number of mistakes (false positives plus false negatives) substantially by 85.8%. We also evaluated to what extent it may improve the automated refactoring discovery (mining) that relies heavily on automated entity matching. Our evaluation results suggest that it substantially improved the state of the art in refactoring discovery, improving recall by 6.9% and reducing the number of false positives by 72.6%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00132"}, {"primary_key": "1220074", "vector": [], "sparse_vector": [], "title": "VALAR: Streamlining Alarm Ranking in Static Analysis with Value-Flow Assisted Active Learning.", "authors": ["Peng<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Minxue Pan"], "summary": "Static analyzers play a critical role in program defects and security vulnerabilities detection. Despite their importance, the widespread adoption of static analysis techniques in industrial development faces numerous obstacles, among which the high rate of false alarms constitutes a significant one. To address this issue, we propose a novel approach called Valar, which performs alarm ranking for advanced value-flow analysis using the active learning technique. Active learning algorithms minimize the manual effort for alarm inspection by maximizing the effect of each user labeling in recognizing true/false alarms. Meanwhile, the value-flows provide Valar with a concise and comprehensive summary of the operational semantics about programs. Based on this, Valar is able to reason about the potential correlations between alarms and prioritize the most profitable unlabeled alarm. Additionally, the accuracy of Valar increases as more user labels are given and Valar's active learning model is further refined. We evaluate Valar on 20 real-world C/C++ programs using three value-flow based checkers. Our experimental results demonstrated that Valar significantly lowers the priorities of false alarms with most true alarms ranked high. Notably, Valar ranked all true alarms in the top 47% in 90% projects and ranked 90% true alarms in the top 22% in 75% projects. Furthermore, Valar has no requirement for pretraining and has a negligible computation time of less than 0.1s for each alarm prioritization.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00098"}, {"primary_key": "1220075", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Parameter-Efficient Fine-Tuning Methods for Pre-Trained Code Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pre-trained code models (e.g. CodeBERT and CodeT5) have demonstrated their code intelligence in various software engineering tasks, such as code summarization. And full fine-tuning has become the typical approach to adapting these models to downstream tasks. However, full fine-tuning these large models can be computationally expensive and memory-intensive, particularly when training for multiple tasks. To alleviate this issue, several parameter-efficient fine-tuning methods (e.g. Adapter and LoRA) have been proposed to only train a small number of additional parameters, while keeping the original pre-trained parameters frozen. Although these methods claim superiority over the prior techniques, they seldom make a comprehensive and fair comparison on multiple software engineering tasks. Moreover, besides their potential in reducing fine-tuning costs and maintaining approximate performance, the effectiveness of these methods in low-resource, cross-language, and cross-project scenarios is inadequately studied. To this end, we first conduct experiments by fine-tuning state-of-the-art code models with these methods on both code understanding tasks and code generation tasks. The results show that, by tuning only 0.5% additional parameters, these methods may achieve comparable or higher performance than full fine-tuning in code understanding tasks, but they may exhibit slightly weaker performance in code generation tasks. We also investigate the impact of these methods with varying numbers of training samples and find that, a considerable number of samples (e.g. 1000 for clone detection) may be required for them to approximate the performance of full fine-tuning. Our experimental results in cross-language and cross-project scenarios demonstrate that by freezing most pre-trained parameters and tuning only 0.5% additional parameters, these methods achieve consistent improvements in models' transfer learning ability in comparison to full fine-tuning. Our code and data are available at https://github.com/anonymous-ase23/ CodeModelParameterEfficientFinetuning.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00125"}, {"primary_key": "1220076", "vector": [], "sparse_vector": [], "title": "FLUX: Finding Bugs with LLVM IR Based Unit Test Crossovers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Optimizing compilers are as ubiquitous as they are crucial to software development. However, bugs in compilers are not uncommon. Among the most serious are bugs in compiler optimizations, which can cause unexpected behavior in compiled binaries. Existing approaches for detecting such bugs have focused on end-to-end compiler fuzzing, which limits their ability for targeted exploration of a compiler's optimizations. This paper proposes FLUX (Finding bugs with LLVM IR based Unit test cross(X)overs), a fuzzer that is designed to generate test cases that stress compiler optimizations. Previous compiler fuzzers are overly constrained by having to construct well-formed inputs. FLUX sidesteps this constraint by using human-written unit test suites as a starting point, and then selecting random combinations of them to generate new tests. We hypothesize that tests generated this way will be able to explore new execution paths through compiler optimizations and find new bugs. Our evaluation of FLUX on LLVM indicates that it is able to increase path coverage over the baseline LLVM unit test suite and explores more edge coverage than previous work. Further, we demonstrate FLUX's ability to generate miscompiled and crash-producing IR on LLVM's optimizations. After a month of fuzzing, FLUX found 28 unique bugs in LLVM's active development branch. We have reported 11 of these bugs which led to 6 of them being patched by LLVM developers. 22 of these are crashes that are triggered by well-formed input programs, and 6 of these are miscompilation bugs that silently produced incorrect code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00100"}, {"primary_key": "1220077", "vector": [], "sparse_vector": [], "title": "CodeGen4Libs: A Two-Stage Approach for Library-Oriented Code Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Automated code generation has been extensively studied in recent literature. In this work, we first survey 66 participants to motivate a more pragmatic code generation scenario, i.e., library-oriented code generation, where the generated code should implement the functionally of the natural language query with the given library. We then revisit existing learning-based code generation techniques and find they have limited effectiveness in such a library-oriented code generation scenario. To address this limitation, we propose a novel library-oriented code generation technique, CodeGen4Libs, which incorporates two stages: import generation and code generation. The import generation stage generates import statements for the natural language query with the given third-party libraries, while the code generation stage generates concrete code based on the generated imports and the query. To evaluate the effectiveness of our approach, we conduct extensive experiments on a dataset of 403,780 data items. Our results demonstrate that CodeGen4Libs outperforms baseline models in both import generation and code generation stages, achieving improvements of up to 97.4% on EM (Exact Match), 54.5% on BLEU, and 53.5% on Hit@All. Overall, our proposed CodeGen4Libs approach shows promising results in generating high-quality code with specific third-party libraries, which can improve the efficiency and effectiveness of software development.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00159"}, {"primary_key": "1220078", "vector": [], "sparse_vector": [], "title": "PTDETECTOR: An Automated JavaScript Front-end Library Detector.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Identifying what front-end library runs on a web page is challenging. Although many mature detectors exist on the market, they suffer from false positives and the inability to detect libraries bundled by packers such as Webpack. Most importantly, the detection features they use are collected from developers' knowledge leading to an inefficient manual workflow and a large number of libraries that the existing detectors cannot detect. This paper introduces PTDETECTOR, which provides the first automated method for generating features and detecting libraries on web pages. We propose a novel data structure, the pTree, which we use as a detection feature. The pTree is well-suited for automation and addresses the limitations of existing detectors. We implement PTDETECTOR as a browser extension and test it on 200 top-traffic websites. Our experiments show that PTDETECTOR can identify packer-bundled libraries, and its detection results outperform existing tools.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00049"}, {"primary_key": "1220079", "vector": [], "sparse_vector": [], "title": "Enhancing Malware Detection for Android Apps: Detecting Fine-Granularity Malicious Components.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Existing Android malware detection systems primarily concentrate on detecting malware apps, leaving a gap in the research concerning the detection of malicious components in apps. In this work, we propose a novel approach to detect fine-granularity malicious components for Android apps and build a prototype called AMCDroid. For a given app, AMCDroid first models app behavior to a homogenous graph based on the call graph and code statements of the app. Then, the graph is converted to a statement tree sequence for malware detection through the AST-based Neural Network with Feature Mapping (ASTNNF) model. Finally, if the app is detected as malware, AMCDroid applies fine-granularity malicious component detection (MCD) algorithm which is based on many-objective genetic algorithm to the homogenous graph for detecting malicious component in the app adaptively. We evaluate AMCDroid on 95,134 samples. Compared with the other two state-of-the-art methods in malware detection, AMCDroid gets the highest performance on the test set with 0.9699 F1-Score, and shows better robustness in facing obfuscation. Moreover, AMCDroid is capable of detecting fine-granularity malicious components of (obfuscated) malware apps. Especially, its average F1-Score exceeds another state-of-the-art method by 50%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00074"}, {"primary_key": "1220080", "vector": [], "sparse_vector": [], "title": "Fault Localization for Buggy Deep Learning Framework Conversions in Image Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When deploying Deep Neural Networks (DNNs), developers often convert models from one deep learning framework to another (e.g., TensorFlow to PyTorch). However, this process is error-prone and can impact target model accuracy. To identify the extent of such impact, we perform and briefly present a differential analysis against three DNNs widely used for image recognition (MobileNetV2, ResNet101, and InceptionV3)converted across four well-known deep learning frameworks (PyTorch, Keras, TensorFlow (TF), and TFLite), which revealed numerous model crashes and output label discrepancies of up to 72%. To mitigate such errors, we present a novel approach towards fault localization and repair of buggy deep learning framework conversions, focusing on pre-trained image recognition models. Our technique consists of four stages of analysis: 1) conversion tools, 2) model parameters, 3) model hyperparameters, and 4) graph representation. In addition, we propose various strategies towards fault repair of the faults detected. We implement our technique on top of the Apache TVM deep learning compiler, and we test it by conducting a preliminary fault localization analysis for the conversion of InceptionV3 from TF to TFLite. Our approach detected a fault in a common DNN converter tool, which introduced precision errors in weights, reducing model accuracy. After our fault localization, we repaired the issue, reducing our conversion error to zero.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00147"}, {"primary_key": "1220081", "vector": [], "sparse_vector": [], "title": "Automatic Generation and Reuse of Precise Library Summaries for Object-Sensitive Pointer Analysis.", "authors": ["Jingbo Lu", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yaoqing Gao", "Jingling Xue"], "summary": "The extensive use of libraries in modern software impedes the scalability of pointer analysis. To address this issue, library summarization can be beneficial, but only if the resulting summary-based pointer analysis is faster without sacrificing much precision in the application code. However, currently, no library summarization approaches exist that meet this design objective. This paper presents a novel approach that solves this problem by using k-object-sensitive pointer analysis, k-obj, for Java. The approach involves applying k-obj, along with a set of summary-based inference rules, to generate a k-object-sensitive library summary. By replacing the program's library with this summary and applying k-obj, the efficiency of the program can be significantly improved while maintaining nearly the same or better precision in the application code. We validate our approach with an implementation in Soot and an evaluation using representative Java programs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00039"}, {"primary_key": "1220082", "vector": [], "sparse_vector": [], "title": "Enhancing Code Safety in Quantum Intermediate Representation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum Intermediate Representation (QIR) is an LLVM-based intermediate representation developed by Microsoft for quantum program compilers. QIR is designed to offer a universal solution for quantum program compilers, decoupled from both front-end languages and back-end hardware, thereby eliminating the need for redundant development of intermediate representations and compilers. However, the lack of a formal definition and reliance on natural language descriptions in the current state of QIR result in interpretational ambiguity and a dearth of rigor in implementing quantum functions. In this paper, we present formal definitions for QIR's data types and instruction sets to establish correctness and safety assurances for operations and intermediate code conversions within QIR. To demonstrate the effectiveness of our approach, we provide examples of unsafe QIR codes where errors can be identified with our method.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00195"}, {"primary_key": "1220083", "vector": [], "sparse_vector": [], "title": "SAT-Verifiable LTL Satisfiability Checking via Graph Representation Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Rongzhen Ye", "Hai Wan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "With the superior learning ability of neural networks, it is promising to obtain highly confident results for linear temporal logic (LTL) satisfiability checking in polynomial time. However, existing neural approaches are limited in inductive ability and in supporting with an arbitrary number of atomic propositions. Besides, there is no mechanism to verify the results for satisfiability checking. In this paper, we propose an approach to checking the satisfiability of an LTL formula and meanwhile generating a satisfiable trace if the LTL formula is satisfiable, where the satisfiable trace verifies the satisfiability result. The core contribution is a new graph representation for LTL formulae - one-step unfolded graph (OSUG) to incorporate the syntax and semantic features of LTL. Preliminary results show that our approach is superior to the state-of-the-art neural approaches on synthetic datasets and confirms the effectiveness of OSUG.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00173"}, {"primary_key": "1220084", "vector": [], "sparse_vector": [], "title": "Detecting Memory Errors in Python Native Code by Tracking Object Lifecycle with Reference Count.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Third-party Python modules are usually implemented as binary extensions by using native code (C/C++) to provide additional features and runtime acceleration. In native code, the heap-allocated PyObjects are managed by the reference counting mechanism provided in Python/C APIs for automatic reclaiming. Hence, improper refcount manipulations can lead to memory leaks and use-after-free problems, and cannot be detected by simply pairing the occurrence of source and sink points. To detect such problems, state-of-the-art approaches have made groundbreaking contributions to identifying inappropriate final refcount values before returning from native code to Python. However, not all problems can be exposed at the end of a path. To detect those hidden in the middle of a path in native code, it is also crucial to track the lifecycle state of PyObjects through the refcount and lifecycle operations in API calls. To achieve this goal, we propose the PyObject State Transition Model (PSTM) recording the lifecycle states and refcount values of PyObjects to describe the effects of Python/C API calls and pointer operations. We track state transitions of PyObjects with symbolic execution based on the model, and report problems when a statement triggers a transition to buggy states. The program state is also expanded to handle pointer nullity checks and smart pointers of PyObjects. We conduct experiments on 12 open-source projects and detect 259 real problems out of 280 reports, which is twice as many bugs as state-of-the-art approaches. We submit 168 real bugs to those active projects, and 106 issues are either confirmed or resolved.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00198"}, {"primary_key": "1220085", "vector": [], "sparse_vector": [], "title": "CiD4HMOS: A Solution to HarmonyOS Compatibility Issues.", "authors": ["Tianzhi Ma", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "HarmonyOS is an operating system boasting a substantial global user base and provides multiple versions of its SDK. Various open-source applications continue to utilize older versions, leading to compatibility issues arising from system constraints. These prevalent issues can substantially affect the user experience. Although numerous solutions have been suggested for addressing compatibility issues in Android, the subject remains largely unexplored within the context of HarmonyOS. To bridge this gap, we investigate the evolution of APIs in HarmonyOS to pinpoint those potentially causing compatibility issues. Based on these insights, we implement CiD4HMOS, a tool designed to detect and categorize compatibility issues in HarmonyOS. We evaluate the feasibility of CiD4HMOS with open-source apps and subsequently apply it to commercially released apps, highlighting its effectiveness in accurately identifying HarmonyOS compatibility issues. The experimental results uncover that CiD4HMOS is effective in detecting compatibility issues in HarmonyOS apps, achieving an accuracy rate of 86.8 % in open-source apps. And, developers of commercially released apps have significantly endorsed our reports. Our research emphasizes the necessity of continuous exploration into compatibility issues within HarmonyOS, underlining the significant role tools like CiD4HMOS play in enhancing the overall user experience.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00134"}, {"primary_key": "1220086", "vector": [], "sparse_vector": [], "title": "Repeated Builds During Code Review: An Empirical Study of the OpenStack Community.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code review is a popular practice where developers critique each others' changes. Since automated builds can identify low-level issues (e.g., syntactic errors, regression bugs), it is not uncommon for software organizations to incorporate automated builds in the code review process. In such code review deployment scenarios, submitted change sets must be approved for integration by both peer code reviewers and automated build bots. Since automated builds may produce an unreliable signal of the status of a change set (e.g., due to \"flaky\" or non-deterministic execution behaviour), code review tools, such as Gerrit, allow developers to request a \"recheck\", which repeats the build process without updating the change set. We conjecture that an unconstrained recheck command will waste time and resources if it is not applied judiciously. To explore how the recheck command is applied in a practical setting, in this paper, we conduct an empirical study of 66,932 code reviews from the OpenStack community. We quantitatively analyze (i) how often build failures are rechecked; (ii) the extent to which invoking recheck changes build failure outcomes; and (iii) how much waste is generated by invoking recheck. We observe that (i) 55% of code reviews invoke the recheck command after a failing build is reported; (ii) invoking the recheck command only changes the outcome of a failing build in 42% of the cases; and (iii) invoking the recheck command increases review waiting time by an average of 2,200% and equates to 187.4 compute years of waste-enough compute resources to compete with the oldest land living animal on earth. Our observations indicate that the recheck command is frequently used after the builds fail, but does not achieve a high likelihood of build success. Based on a developer survey and our history-based quantitative findings, we encourage reviewer teams to think twice before rechecking and be considerate of waste. While recheck currently generates plenty of wasted computational resources and bloats waiting times, it also presents exciting future opportunities for researchers and tool builders to propose solutions that can reduce waste.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00030"}, {"primary_key": "1220087", "vector": [], "sparse_vector": [], "title": "Improving Code Extraction from Coding Screencasts Using a Code-Aware Encoder-Decoder Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Accurate automatic code extraction from tutorial videos is crucial for software developers seeking to reuse the code contained in these videos. Current methods using optical character recognition (OCR) often yield inaccurate results due to code complexity and variations in screencast formats. To address this issue, we introduce CodeT5-OCRfix, an approach that leverages the pre-trained code-aware large language model CodeT5 to enhance code extraction accuracy by post-processing OCRed code. We first collect a large and diverse dataset of source code screenshots captured from more than 10K Java projects from GitHub. We then apply the most widely used OCR engine for the task of code extraction from videos, Tesseract, on these screenshots and collect the OCRed code along with the ground truth code extracted from the Java files. We built a training dataset of more than 585K pairs of OCRed and ground truth code pairs, which we then used to fine-tune CodeT5, obtaining our model CodeT5-OCRfix. An empirical evaluation on both screenshots and screencast frames shows that CodeT5-OCRfix outperforms baseline code extraction models and is also more time-efficient. Our approach therefore improves the state-of-the-art in code extraction techniques from screencasts and images.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00184"}, {"primary_key": "1220088", "vector": [], "sparse_vector": [], "title": "An Industrial Practice for Securing Android Apps in the Banking Domain.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The emergence of mobile technology has significantly advanced the banking sector in terms of how consumers interact with their banks and manage their finances. The accessibility and ease of financial services have been improved by the switch from desktop banking to mobile banking. Mobile banking has a lot of advantages, but it also has security concerns. Illegal access to personal and financial information often occurs due to lapses in mobile security. In recent years, we have worked with banks from 10 countries and systematically analyzed 28 of their apps. We found several vulnerabilities in these apps by manual code reviews and by conducting 11 types of attacks. We then proposed and applied adequate security measures to protect these apps. Finally, we added these measures to our tool named AppProtect+ to effectively identify and thwart these threats. In this paper, we report our experience and practice of securing these Android apps.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00057"}, {"primary_key": "1220089", "vector": [], "sparse_vector": [], "title": "Fine-Grained In-Context Permission Classification for Android Apps Using Control-Flow Graph Embedding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android is the most popular operating system for mobile devices nowadays. Permissions are a very important part of Android security architecture. Apps frequently need the users' permission, but many of them only ask for it once—when the user uses the app for the first time—and then they keep and abuse the given permissions. Longing to enhance Android permission security and users' private data protection is the driving factor behind our approach to explore fine-grained context-sensitive permission usage analysis and thereby identify misuses in Android apps. In this work, we propose an approach for classifying the fine-grained permission uses for each functionality of Android apps that a user interacts with. Our approach, named DroidGem, relies on mainly three technical components to provide an in-context classification for permission (mis)uses by Android apps for each functionality triggered by users: (1) static inter-procedural control-flow graphs and call graphs representing each functionality in an app that may be triggered by users' or systems' events through UI-linked event handlers, (2) graph embedding techniques converting graph structures into numerical encoding, and (3) supervised machine learning models classifying (mis)uses of permissions based on the embedding. We have implemented a prototype of DroidGem and evaluated it on 89 diverse apps. The results show that DroidGem can accurately classify whether permission used by the functionality of an app triggered by a UI-linked event handler is a misuse in relation to manually verified decisions, with up to 95% precision and recall. We believe that such a permission classification mechanism can be helpful in providing fine-grained permission notices in a context related to app users' actions, and improving their awareness of (mis)uses of permissions and private data in Android apps.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00056"}, {"primary_key": "1220090", "vector": [], "sparse_vector": [], "title": "Towards Automatically Addressing Self-Admitted Technical Debt: How Far Are We?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Upon evolving their software, organizations and individual developers have to spend a substantial effort to pay back technical debt, i.e, the fact that software is released in a shape not as good as it should be, e.g, in terms of functionality, reliability, or maintainability. This paper empirically investigates the extent to which technical debt can be automatically paid back by neural-based generative models, and in particular models exploiting different strategies for pre-training and fine-tuning. We start by extracting a dateset of 5,039 Self-Admitted Technical Debt (SATD) removals from 595 open-source projects. SATD refers to technical debt instances documented (e.g, via code comments) by developers. We use this dataset to experiment with seven different generative deep learning (DL) model configurations. Specifically, we compare transformers pre-trained and fine-tuned with different combinations of training objectives, including the fixing of generic code changes, SATD removals, and SATD-comment prompt tuning. Also, we investigate the applicability in this context of a recently-available Large Language Model (LLM)-based chat bot. Results of our study indicate that the automated repayment of SATD is a challenging task, with the best model we experimented with able to automatically fix ∼2% to 8% of test instances, depending on the number of attempts it is allowed to make. Given the limited size of the fine-tuning dataset (∼5k instances), the model's pre-training plays a fundamental role in boosting performance. Also, the ability to remove SATD steadily drops if the comment documenting the SATD is not provided as input to the model. Finally, we found general-purpose LLMs to not be a competitive approach for addressing SATD.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00103"}, {"primary_key": "1220091", "vector": [], "sparse_vector": [], "title": "Assessing the Impact of Refactoring Energy-Inefficient Code Patterns on Software Sustainability: An Industry Case Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Advances in technologies like artificial intelligence and metaverse have led to a proliferation of software systems in business and everyday life. With this widespread penetration, the carbon emissions of software are rapidly growing as well, thereby negatively impacting the long-term sustainability of our environment. Hence, optimizing software from a sustainability standpoint becomes more crucial than ever. We believe that the adoption of automated tools that can identify energy-inefficient patterns in the code and guide appropriate refactoring can significantly assist in this optimization. In this extended abstract, we present an industry case study that evaluates the sustainability impact of refactoring energy -inefficient code patterns identified by automated software sustainability assessment tools for a large application. Preliminary results highlight a positive impact on the application's sustainability post-refactoring, leading to a 29% decrease in per-user per-month energy consumption.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00205"}, {"primary_key": "1220094", "vector": [], "sparse_vector": [], "title": "DeepScaler: Holistic Autoscaling for Microservices Based on Spatiotemporal GNN with Adaptive Graph Learning.", "authors": ["Chunyang Meng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>g Tong", "<PERSON><PERSON>", "<PERSON>"], "summary": "Autoscaling functions provide the foundation for achieving elasticity in the modern cloud computing paradigm. It enables dynamic provisioning or de-provisioning resources for cloud software services and applications without human intervention to adapt to workload fluctuations. However, autoscaling microservice is challenging due to various factors. In particular, complex, time-varying service dependencies are difficult to quantify accurately and can lead to cascading effects when allocating resources. This paper presents DeepScaler, a deep learning-based holistic autoscaling approach for microservices that focus on coping with service dependencies to optimize service-level agreements (SLA) assurance and cost efficiency. DeepScaler employs (i) an expectation-maximization-based learning method to adaptively generate affinity matrices revealing service dependencies and (ii) an attention-based graph convolutional network to extract spatio-temporal features of microservices by aggregating neighbors' information of graph-structural data. Thus DeepScaler can capture more potential service dependencies and accurately estimate the resource requirements of all services under dynamic workloads. It allows DeepScaler to reconfigure the resources of the interacting services simultaneously in one resource provisioning operation, avoiding the cascading effect caused by service dependencies. Experimental results demonstrate that our method implements a more effective autoscaling mechanism for microservice that not only allocates resources accurately but also adapts to dependencies changes, significantly reducing SLA violations by an average of 41% at lower costs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00038"}, {"primary_key": "1220095", "vector": [], "sparse_vector": [], "title": "Wemint:Tainting Sensitive Data Leaks in WeChat Mini-Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangdong Bai", "<PERSON><PERSON><PERSON>"], "summary": "Mini-programs (MiniApps), lightweight versions of full-featured mobile apps that run inside a host app such as WeChat, have become increasingly popular due to their simplified and convenient user experiences. However, MiniApps raise new security and privacy concerns as they can access partially or all of host apps' system resources, including sensitive personal data. While taint detection has been proven effective in addressing this kind of concerns, existing taint detection techniques for mobile apps cannot be directly applied to MiniApps. The main reason is that the key logics of MiniApps are usually written in J avaScript, and its intrinsic characteristics (function-level scope, dynamic types, synchronous programming, and code obfuscation) prevent existing taint detection techniques from precisely propagating the taints. To address this problem, we propose a novel taint detection technique, Wemint, that detects sensitive information leaks in MiniApps. Specifically, Wemint facilitates taint propagation via building a context-based model based on the operational prin-ciple of MiniApps and J avaScript, and addresses asynchronous function calls by modeling their callbacks explicitly in taint rules. In addition, due to the adoption of Abstract Syntax Trees (ASTs) for code representation during taint detection, Wemint exhibits better robustness against the commonly-applied code obfuscation. Our experimental results show that Wemint can effectively detect sensitive information leaks in WeChat MiniApps, as well as trace the path of sensitive data flows. By applying <PERSON>min<PERSON> to over 20K suspicious MiniApps, we found that over 7.5K (36.5 %) of them have sensitive data leaks, and <PERSON>mint outperforms the state-of-the-art DoubleX based techniques in detecting these leaks.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00151"}, {"primary_key": "1220096", "vector": [], "sparse_vector": [], "title": "Semantic Data Augmentation for Deep Learning Testing Using Generative AI.", "authors": ["<PERSON><PERSON><PERSON>", "Simos G<PERSON>", "<PERSON>"], "summary": "The performance of state-of-the-art Deep Learning models heavily depends on the availability of well-curated training and testing datasets that sufficiently capture the operational domain. Data augmentation is an effective technique in alleviating data scarcity, reducing the time-consuming and expensive data collection and labelling processes. Despite their potential, existing data augmentation techniques primarily focus on simple geometric and colour space transformations, like noise, flipping and resizing, producing datasets with limited diversity. When the augmented dataset is used for testing the Deep Learning models, the derived results are typically uninformative about the robustness of the models. We address this gap by introducing GENFUZZER, a novel coverage-guided data augmentation fuzzing technique for Deep Learning models underpinned by generative AI. We demonstrate our approach using widely-adopted datasets and models employed for image classification, illustrating its effectiveness in generating informative datasets leading up to a 26% increase in widely-used coverage criteria.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00194"}, {"primary_key": "1220097", "vector": [], "sparse_vector": [], "title": "A Comprehensive Study on Code Clones in Automated Driving Software.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li"], "summary": "With the continuous improvement of artificial intelligence technology, autonomous driving technology has been greatly developed. Hence automated driving software has drawn more and more attention from both researchers and practitioners. Code clone is a commonly used to speed up the development cycle in software development, but many studies have shown that code clones may affect software maintainability. Currently, there is little research investigating code clones in automated driving software. To bridge this gap, we conduct a comprehensive experience study on the code clones in automated driving software. Through the analysis of Apollo and Autoware, we have presented that code clones are prevalent in automated driving software. about 30% of code lines are involved in code clones and more than 50% of files contain code clones. Moreover, a notable portion of these code clones has caused bugs and co-modifications. Due to the high complexity of autonomous driving, the automated driving software is often designed to be modular, with each module responsible for a single task. When considering each module individually, we have found that Perception, Planning, Canbus, and Sensing modules are more likely to encounter code clones, and more likely to have bug-prone and co-modified clones. Finally, we have shown that there exist cross-module clones to propagate bugs and co-modifications in different modules, which undermine the software's modularity.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00053"}, {"primary_key": "1220098", "vector": [], "sparse_vector": [], "title": "SpecFuzzer: A Tool for Inferring Class Specifications via Grammar-Based Fuzzing.", "authors": ["Facundo <PERSON>", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In object-oriented design, class specifications are primarily used to express properties describing the intended behavior of the class methods and constraints on class' objects. Although the presence of these specifications is important for various software engineering tasks such as test generation, bug finding and automated debugging, developers rarely write them. In this tool demo we present the details of SPEcFuzZER, a tool that aims at alleviating the problem of writing class specifications by using a combination of grammar-based fuzzing, dynamic invariant detection and mutation analysis to auto-maticallyautomatically infer specifications for Java classes. Given a class under analysis, SPEcFuzZER uses (i) a generator of candidate assertions derived from a grammar automatically extracted from the class; (ii) a dynamic invariant detector -Daikon- in order to discard the assertions invalidated by a test suite; and (iii) a mutation-based mechanism to cluster and rank assertions, so that similar constraints are grouped together and the stronger assertions are prioritized. The tool is available on GitHub at https://github.com/facumolina/specfuzzer, and the demo video can be found on YouTube: https://youtu.be/IfakNCbzOUg.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00024"}, {"primary_key": "1220099", "vector": [], "sparse_vector": [], "title": "Cell2Doc: ML Pipeline for Generating Documentation in Computational Notebooks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computational notebooks have become the go-to way for solving data-science problems. While they are designed to combine code and documentation, prior work shows that documentation is largely ignored by the developers because of the manual effort. Automated documentation generation can help, but existing techniques fail to capture algorithmic details and developers often end up editing the generated text to provide more explanation and sub-steps. This paper proposes a novel machine-learning pipeline, Cell2Doc, for code cell documentation in Python data science notebooks. Our approach works by identifying different logical contexts within a code cell, generating documentation for them separately, and finally combining them to arrive at the documentation for the entire code cell. Cell2Doc takes advantage of the capabilities of existing pre-trained language models and improves their efficiency for code cell documentation. We also provide a new benchmark dataset for this task, along with a data-preprocessing pipeline that can be used to create new datasets. We also investigate an appropriate input representation for this task. Our automated evaluation suggests that our best input representation improves the pre-trained model's performance by 2.5x on average. Further, Cell2Doc achieves 1.33x improvement during human evaluation in terms of correctness, informativeness, and readability against the corresponding standalone pretrained model.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00200"}, {"primary_key": "1220100", "vector": [], "sparse_vector": [], "title": "Automating Bias Testing of LLMs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) are being quickly integrated in a myriad of software applications. This may introduce a number of biases, such as gender, age or ethnicity, in the behavior of such applications. To face this challenge, we explore the automatic generation of tests suites to assess the potential biases of an LLM. Each test is defined as a prompt used as input to the LLM and a test oracle that analyses the LLM output to detect the presence of biases.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00018"}, {"primary_key": "1220101", "vector": [], "sparse_vector": [], "title": "Where to Go Now? Finding Alternatives for Declining Packages in the npm Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software ecosystems (e.g., npm, PyPI) are the backbone of modern software developments. Developers add new packages to ecosystems every day to solve new problems or provide alternative solutions, causing obsolete packages to decline in their importance to the community. Packages in decline are reused less over time and may become less frequently maintained. Thus, developers usually migrate their dependencies to better alternatives. Replacing packages in decline with better alternatives requires time and effort by developers to identify packages that need to be replaced, find the alternatives, asset migration benefits, and finally, perform the migration. This paper proposes an approach that automatically identifies packages that need to be replaced and finds their alternatives supported with real-world examples of open source projects performing the suggested migrations. At its core, our approach relies on the dependency migration patterns performed in the ecosystem to suggest migrations to other developers. We evaluated our approach on the npm ecosystem and found that 96% of the suggested alternatives are accurate. Furthermore, by surveying expert JavaScript developers, 67% of them indicate that they will use our suggested alternative packages in their future projects.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00119"}, {"primary_key": "1220102", "vector": [], "sparse_vector": [], "title": "To Share, or Not to Share: Exploring Test-Case Reusability in Fork Ecosystems.", "authors": ["Mukelabai <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Code is often reused to facilitate collaborative development, to create software variants, to experiment with new ideas, or to develop new features in isolation. Social-coding platforms, such as GitHub, enable enhanced code reuse with forking, pull requests, and cross-project traceability. With these concepts, forking has become a common strategy to reuse code by creating clones (i.e., forks) of projects. Thereby, forking establishes fork ecosystems of co-existing projects that are similar, but developed in parallel, often with rather sporadic code propagation and synchronization. Consequently, forked projects vary in quality and often involve redundant development efforts. Unfortunately, as we will show, many projects do not benefit from test cases created in other forks, even though those test cases could actually be reused to enhance the quality of other projects. We believe that reusing test cases—in addition to the implementation code—can improve software quality, software maintainability, and coding efficiency in fork ecosystems. While researchers have worked on test-case-reuse techniques, their potential to improve the quality of real fork ecosystems is unknown. To shed light on test-case reusability, we study to what extent test cases can be reused across forked projects. We mined a dataset of test cases from 305 fork ecosystems on GitHub—totaling 1,089 projects—and assessed the potential for reusing these test cases among the forked projects. By performing a manual inspection of the test cases' applicability, by transplanting the test cases, and by analyzing the causes of non-applicability, we contribute an understanding of the benefits (e.g., uncovering bugs) and of the challenges (e.g., automated code transplantation, deciding about applicability) of reusing test cases in fork ecosystems.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00191"}, {"primary_key": "1220103", "vector": [], "sparse_vector": [], "title": "Software Entity Recognition with Noise-Robust Learning.", "authors": ["<PERSON>", "Yifeng Di", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recognizing software entities such as library names from free-form text is essential to enable many software engineering (SE) technologies, such as traceability link recovery, automated documentation, and API recommendation. While many approaches have been proposed to address this problem, they suffer from small entity vocabularies or noisy training data, hindering their ability to recognize software entities mentioned in sophisticated narratives. To address this challenge, we leverage the Wikipedia taxonomy to develop a comprehensive entity lexicon with 79K unique software entities in 12 fine-grained types, as well as a large labeled dataset of over 1.7M sentences. Then, we propose self-regularization, a noise-robust learning approach, to the training of our software entity recognition (SER) model by accounting for many dropouts. Results show that models trained with self-regularization outperform both their vanilla counterparts and state-of-the-art approaches on our Wikipedia benchmark and two Stack Overflow benchmarks. We release our models 1 1 https://huggingface.co/taidng/wikiser-bert-base; https.//huggingface.co/taidng/wikiser-bert-large., data, and code for future research. 2 2 https://github.com/taidnguyen/software_entity_recognition", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00203"}, {"primary_key": "1220104", "vector": [], "sparse_vector": [], "title": "Function-Level Vulnerability Detection Through Fusing Multi-Modal Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software vulnerabilities damage the functionality of software systems. Recently, many deep learning-based approaches have been proposed to detect vulnerabilities at the function level by using one or a few different modalities (e.g., text representation, graph-based representation) of the function and have achieved promising performance. However, some of these existing studies have not completely leveraged these diverse modalities, particularly the underutilized image modality, and the others using images to represent functions for vulnerability detection have not made adequate use of the significant graph structure underlying the images. In this paper, we propose MVulD, a multi-modal-based function-level vulnerability detection approach, which utilizes multi-modal features of the function (i.e., text representation, graph representation, and image representation) to detect vulnerabilities. Specifically, MVulD utilizes a pre-trained model (i.e., UniXcoder) to learn the semantic information of the textual source code, employs the graph neural network to distill graph-based representation, and makes use of computer vision techniques to obtain the image representation while retaining the graph structure of the function. We conducted a large-scale experiment on 25,816 functions. The experimental results show that MVulD improves four state-of-the-art baselines by 30.8%-81.3%, 12.8%-27.4%, 48.8%-115%, and 22.9%-141% in terms of F1-score, Accuracy, Precision, and PR-AUC respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00084"}, {"primary_key": "1220105", "vector": [], "sparse_vector": [], "title": "Unifying Defect Prediction, Categorization, and Repair by Multi-Task Deep Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Just-In- Time defect prediction models can identify defect-inducing commits at check-in time and many approaches are proposed with remarkable performance. However, these approaches still have a few limitations which affect their effectiveness and practical usage: (1) partially using semantic information or structure information of code, (2) coarsely providing results to a commit (buggy or clean), and (3) independently investigating the defect prediction model and defect repair model. In this study, to handle the aforementioned limitations, we propose a unified defect prediction and repair framework named COMPDEFECT,which can identify whether a changed function inside a commit is defect-prone, categorize the type of defect, and repair such a defect automatically if it falls into several scenarios, e.g., defects with single statement fixes, or those that match a small set of defect templates. Technically, the first two tasks in COMPDEFECT are treated as a multiclass classification task, while the last task is treated as a sequence generation task. To verify the effectiveness of COMPDEFECT, we first build a large-scale function-level dataset (i.e., 21,047) named Function-SStuBs4J and then compare COMPDEFECT with tens of state-of-the-art (SOTA) approaches by considering five performance measures. The experimental results indicate that COMPDEFECT outperforms all SOTAs with a substantial improvement in three tasks separately. Moreover, the pipeline experimental results also indicate the feasibility of COMPDEFECT to unify three tasks in a model.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00083"}, {"primary_key": "1220108", "vector": [], "sparse_vector": [], "title": "Precise Data-Driven Approximation for Program Analysis via Fuzzing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Program analysis techniques such as abstract interpretation and symbolic execution suffer from imprecision due to over- and underapproximation, which results in false alarms and missed violations. To alleviate this imprecision, we propose a novel data structure, program state probability (PSP), that leverages execution samples to probabilistically approximate reachable program states. The core intuition of this approximation is that the probability of reaching a given state varies greatly, and thus we can considerably increase analysis precision at the cost of a small probability of unsoundness or incompleteness, which is acceptable when analysis targets bug-finding. Specifically, PSP enhances existing analyses by disregarding low-probability states deemed feasible by overapproximation and recognising high-probability states deemed infeasible by underapproximation. We apply PSP in three domains. First, we show that PSP enhances the precision of the Clam abstract interpreter in terms of MCC from 0.09 to 0.27 and F1 score from 0.22 to 0.34. Second, we demonstrate that a symbolic execution search strategy based on PSP that prioritises program states with a higher probability increases the number of found bugs and reduces the number of solver calls compared to state-of-the-art techniques. Third, a program repair patch prioritisation strategy based on PSP reduces the average patch rank by 26%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00185"}, {"primary_key": "1220109", "vector": [], "sparse_vector": [], "title": "Towards a Knowledge Base of Common Sustainability Weaknesses in Green Software Development.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the climate crisis looming, engineering sustainable software systems become crucial to optimize resource utilization, minimize environmental impact, and foster a greener, more resilient digital ecosystem. For developers, getting access to automated tools that analyze code and suggest sustainability-related optimizations becomes extremely important from a learning and implementation perspective. However, there is currently a dearth of such tools due to the lack of standardized knowledge, which serves as the foundation of these tools. In this paper, we motivate the need for the development of a standard knowledge base of commonly occurring sustainability weaknesses in code, and propose an initial way of doing that. Furthermore, through preliminary experiments, we demonstrate why existing knowledge regarding software weaknesses cannot be re-tagged \"as is\" to sustainability without significant due diligence, thereby urging further explorations in this ecologically significant domain.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00204"}, {"primary_key": "1220110", "vector": [], "sparse_vector": [], "title": "PURLTL: Mining LTL Specification from Imperfect Traces in Testing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Tingchen Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai Wan", "Rongzhen Ye", "<PERSON><PERSON>"], "summary": "Formal specifications are widely used in software testing approaches, while writing such specifications is a time-consuming job. Recently, a number of methods have been proposed to mine specifications from execution traces, typically in the form of linear temporal logic (LTL). However, existing works have the following disadvantages: (1) ignoring the negative impact of imperfect traces, which come from partial profiling, missing context information, or buggy programs; (2) relying on templates, resulting in limited expressiveness; (3) requesting negative traces, which are usually unavailable in practice. In this paper, we propose PURLTL, which is able to mine arbitrary LTL specifications from imperfect traces. To alleviate the search space explosion and the wrong search bias, we propose a neural-based method to search LTL formulae, which, intuitively, simulates LTL path checking through differentiable parameter operations. To solve the problem of lacking negative traces, we transform the problem into learning from positive and unlabeled samples, by means of data augmentation and applying positive and unlabeled learning to the training process. Experiments show that our approach surpasses the previous start-of-the-art (SOTA) approach by a large margin. Besides, the results suggest that our approach is not only robust with imperfect traces, but also does not rely on formula templates.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00202"}, {"primary_key": "1220111", "vector": [], "sparse_vector": [], "title": "Generative Type Inference for Python.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Python is a popular dynamic programming language, evidenced by its ranking as the second most commonly used language on GitHub. However, its dynamic type system can lead to potential type errors, leading researchers to explore automatic type inference approaches for Python programs. Existing type inference approaches can be generally grouped into three categories, i.e., rule-based, supervised, and cloze- style approaches. The rule-based type inference approaches can ensure the accuracy of predicted variable types, but they suffer from low coverage problems caused by dynamic features and external calls. Supervised type inference approaches, while feature-agnostic and able to mitigate the low coverage problem, require large, high- quality annotated datasets and are limited to pre-defined types. As zero-shot approaches, the cloze-style approaches reformulate the type inference problem into a fill-in-the-blank problem by leveraging the general knowledge in powerful pre-trained code models. However, their performance is limited since they ignore the domain knowledge from static typing rules which reflect the inference logic. What is more, their predictions are not interpretable, hindering developers' understanding and verification of the results. This paper introduces Typegen, a few-shot generative type inference approach that incorporates static domain knowledge from static analysis. Typegen creates chain-of-thought (COT) prompts by translating the type inference steps of static analysis into prompts based on the type dependency graphs (TDGs), enabling language models to learn from how static analysis infers types. By combining COT prompts with code slices and type hints, TypegEnconstructs example prompts from human annotations. Typeg Enonly requires very few annotated examples to teach language models to generate similar COT prompts via in-context learning. Moreover, Typeg Enenhances the interpretability of results through the use of the input- explanation-output strategy, which generates both explanations and type predictions in COT prompts. Experiments show that Typegen outperforms the best baseline Type4Py by 10.0% for argument type prediction and 22.5 % in return value type prediction in terms of top-l Exact Match by using only five examples. Furthermore, Typeg Enachieves substantial improvements of 27 % to 84 % compared to the zero-shot performance of large language models with parameter sizes ranging from 1.3B to 175B in terms of top-I Exact Match.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00031"}, {"primary_key": "1220112", "vector": [], "sparse_vector": [], "title": "Smart Prompt Advisor: Multi-Objective Prompt Framework for Consistency and Best Practices.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent breakthroughs in Large Language Models (LLM), comprised of billions of parameters, have achieved the ability to unveil exceptional insight into a wide range of Natural Language Processing (NLP) tasks. The onus of the performance of these models lies in the sophistication and completeness of the input prompt. Minimizing the enhancement cycles of prompt with improvised keywords becomes critically important as it directly affects the time to market and cost of the developing solution. However, this process inevitably has a trade-off between the learning curve/proficiency of the user and completeness of the prompt, as generating such a solutions is an incremental process. In this paper, we have designed a novel solution and implemented it in the form of a plugin for Visual Studio Code IDE, which can optimize this trade-off, by learning the underlying prompt intent to enhance with keywords. This will tend to align with developers' collection of semantics while developing a secure code, ensuring parameter and local variable names, return expressions, simple pre and post-conditions. and basic control and data flow are met.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00019"}, {"primary_key": "1220114", "vector": [], "sparse_vector": [], "title": "Vicious Cycles in Distributed Software Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A major threat to distributed software systems' reliability is vicious cycles, which are observed when an event in the distributed software system's execution causes a system degradation, and the degradation, in turn, causes more of such events. Vicious cycles often result in large-scale cloud outages that are hard to recover from due to their self-reinforcing nature. This paper formally defines Vicious Cycle, and conducts the first in-depth study of 33 real-world vicious cycles in 13 widely-used open-source distributed software systems, shedding light on the root causes, triggering conditions, and fixing strategies of vicious cycles, with over a dozen concrete implications to combat them. Our findings show that the majority of the vicious cycles are caused by incorrect error handlers, where the handlers do not obtain enough information to distinguish between 1) an error induced by incoming requests and 2) an error induced by an unexpected interference from another error handler. This paper further performs a feasibility study by 1) building a monitoring tool that prevents one type of vicious cycle by collecting information to make a more informed decision in error handling, and 2) investigating the effectiveness of one commonly suggested practice-injecting exponential backoff-to prevent vicious cycles induced by unconstrained retry.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00032"}, {"primary_key": "1220115", "vector": [], "sparse_vector": [], "title": "MELT: Mining Effective Lightweight Transformations from Pull Requests.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vasco <PERSON>ho", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software developers often struggle to update APIs, leading to manual, time-consuming, and error-prone processes. We introduce Melt, a new approach that generates lightweight API migration rules directly from pull requests in popular library repositories. Our key insight is that pull requests merged into open-source libraries are a rich source of information sufficient to mine API migration rules. By leveraging code examples mined from the library source and automatically generated code examples based on the pull requests, we infer transformation rules in Comby, a language for structural code search and replace. Since inferred rules from single code examples may be too specific, we propose a generalization procedure to make the rules more applicable to client projects. Melt rules are syntax-driven, interpretable, and easily adaptable. Moreover, unlike previous work, our approach enables rule inference to seamlessly integrate into the library workflow, removing the need to wait for client code migrations. We evaluated <PERSON><PERSON> on pull requests from four popular libraries, successfully mining 461 migration rules from code examples in pull requests and 114 rules from auto-generated code examples. Our generalization procedure increases the number of matches for mined rules by 9×. We applied these rules to client projects and ran their tests, which led to an overall decrease in the number of warnings and fixing some test cases demonstrating MELT's effectiveness in real-world scenarios.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00117"}, {"primary_key": "1220116", "vector": [], "sparse_vector": [], "title": "CAT-LM Training Language Models on Aligned Code And Tests.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Testing is an integral but often neglected part of the software development process. Classical test generation tools such as EvoSuite generate behavioral test suites by optimizing for coverage, but tend to produce tests that are hard to understand. Language models trained on code can generate code that is highly similar to that written by humans, but current models are trained to generate each file separately, as is standard practice in natural language processing, and thus fail to consider the code-under-test context when producing a test file. In this work, we propose the Aligned Code And Tests Language Model (CAT-LM), a GPT-style language model with 2.7 Billion parameters, trained on a corpus of Python and Java projects. We utilize a novel pretraining signal that explicitly considers the mapping between code and test files when available. We also drastically increase the maximum sequence length of inputs to 8,192 tokens, 4x more than typical code generation models, to ensure that the code context is available to the model when generating test code. We analyze its usefulness for realistic applications, showing that sampling with filtering (e.g., by compilability, coverage) allows it to efficiently produce tests that achieve coverage similar to ones written by developers while resembling their writing style. By utilizing the code context, CAT-LM generates more valid tests than even much larger language models trained with more data (CodeGen 16B and StarCoder) and substantially outperforms a recent test-specific model (TeCo) at test completion. Overall, our work highlights the importance of incorporating software-specific insights when training language models for code and paves the way to more powerful automated test generation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00193"}, {"primary_key": "1220118", "vector": [], "sparse_vector": [], "title": "From Misuse to Mastery: Enhancing Code Generation with Knowledge-Driven AI Chaining.", "authors": ["<PERSON><PERSON><PERSON>", "Xi<PERSON><PERSON> Ye", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have shown promising results in automatic code generation by improving coding efficiency to a certain extent. However, generating high-quality and reliable code remains a formidable task because of LLMs' lack of good programming practice, especially in exception handling. In this paper, we first conduct an empirical study and summarize three crucial challenges of LLMs in exception handling, i.e., incomplete exception handling, incorrect exception handling and abuse of try-catch. We then try prompts with different granularities to address such challenges, finding fine-grained knowledge-driven prompts works best. Based on our empirical study, we propose a novel Knowledge-driven Prompt Chaining-based code generation approach, name KPC, which decomposes code generation into an AI chain with iterative check-rewrite steps and chains fine-grained knowledge-driven prompts to assist LLMs in considering exception-handling specifications. We evaluate our KPC-based approach with 3,079 code generation tasks extracted from the Java official API documentation. Extensive experimental results demonstrate that the KPC-based approach has considerable potential to ameliorate the quality of code generated by LLMs. It achieves this through proficiently managing exceptions and obtaining remarkable enhancements of 109.86% and 578.57% with static evaluation methods, as well as a reduction of 18 runtime bugs in the sampled dataset with dynamic validation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00143"}, {"primary_key": "1220120", "vector": [], "sparse_vector": [], "title": "How to Train Your Neural Bug Detector: Artificial vs Real Bugs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real bug fixes found in open source repositories seem to be the perfect source for learning to localize and repair real bugs. Yet, the scale of existing bug fix collections is typically too small for training data-intensive neural approaches. Neural bug detectors are hence almost exclusively trained on artificial bugs, produced by mutating existing source code and thus easily obtainable at large scales. However, neural bug detectors trained on artificial bugs usually underperform when faced with real bugs. To address this shortcoming, we set out to explore the impact of training on real bug fixes at scale. Our systematic study compares neural bug detectors trained on real bug fixes, artificial bugs and mixtures of real and artificial bugs at various dataset scales and with varying training techniques. Based on our insights gained from training on a novel dataset of 33k real bug fixes, we were able to identify a training setting capable of significantly improving the performance of existing neural bug detectors by up to 170% on simple bugs in Python. In addition, our evaluation shows that further gains can be expected by increasing the size of the real bug fix dataset or the code dataset used for generating artificial bugs. To facilitate future research on neural bug detection, we release our real bug fix dataset, trained models and code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00104"}, {"primary_key": "1220122", "vector": [], "sparse_vector": [], "title": "Polyglot Code Smell Detection for Infrastructure as Code with GLITCH.", "authors": ["<PERSON><PERSON>", "João <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents GLITCH, a new technology-agnostic framework that enables automated polyglot code smell detection for Infrastructure as Code scripts. GLITCH uses an intermediate representation on which different code smell detectors can be defined. It currently supports the detection of nine security smells and nine design & implementation smells in scripts written in An<PERSON>, <PERSON>, Docker, <PERSON>uppet, or Terraform. Studies conducted with GLITCH not only show that GLITCH can reduce the effort of writing code smell analyses for multiple IaC technologies, but also that it has higher precision and recall than current state-of-the-art tools. A video describing and demonstrating GLITCH is available at: https://youtu.be/E4RhCcZjWbk.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00162"}, {"primary_key": "1220123", "vector": [], "sparse_vector": [], "title": "Coding and Debugging by Separating Secret Code Toward Secure Remote Development.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "It is a higher priority for organizations to keep their source code secured. When a certain specific code includes a secret such as intellectual property, they need to pay special attention to prevent the secret code from leaking outside. On the other hand, sometimes code leaks comes from acts by inside programmers. This industrial paper proposes a MORDEn (Micro Organized Remote Development Environment) toward preventing code leaks. MORDEn enables programmers capable of coding and debugging by physically separating secret code from their client. We also introduce a showcase that demonstrates the feasibility of MORDEn from a case study project using it.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00160"}, {"primary_key": "1220125", "vector": [], "sparse_vector": [], "title": "Symbolic Fixpoint Algorithms for Logical LTL Games.", "authors": ["<PERSON><PERSON>", "Deepak D&apos;Souza", "<PERSON><PERSON><PERSON>"], "summary": "Two-player games are a fruitful way to represent and reason about several important synthesis tasks. These tasks include controller synthesis (where one asks for a controller for a given plant such that the controlled plant satisfies a given temporal specification), program repair (setting values of variables to avoid exceptions), and synchronization synthesis (adding lock/unlock statements in multi-threaded programs to satisfy safety assertions). In all these applications, a solution directly corresponds to a winning strategy for one of the players in the induced game. In turn, logically-specified games offer a powerful way to model these tasks for large or infinite-state systems. Much of the techniques proposed for solving such games typically rely on abstraction-refinement or template-based solutions. In this paper, we show how to apply classical fixpoint algorithms, that have hitherto been used in explicit, finite-state, settings, to a symbolic logical setting. We implement our techniques in a tool called GENSys-LTL and show that they are not only effective in synthesizing valid controllers for a variety of challenging benchmarks from the literature, but often compute maximal winning regions and maximally-permissive controllers. We achieve 46.38X speed-up over the state of the art and also scale well for non-trivial LTL specifications.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00212"}, {"primary_key": "1220127", "vector": [], "sparse_vector": [], "title": "Contrastive Learning for API Aspect Analysis.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel approach - CLAA - for API aspect detection in API reviews that utilizes transformer models trained with a supervised contrastive loss objective function. We evaluate CLAA using performance and impact analysis. For performance analysis, we utilized a benchmark dataset on developer discussions collected from Stack Overflow and compare the results to those obtained using state-of-the-art transformer models. Our experiments show that contrastive learning can significantly improve the performance of transformer models in detecting aspects such as Performance, Security, Usability, and Documentation. For impact analysis, we performed empirical and developer study. On a randomly selected and manually labeled 200 online reviews, CLAA achieved 92% accuracy while the SOTA baseline achieved 81.5%. According to our developer study involving 10 participants, the use of Stack Overflow + CLAA resulted in increased accuracy and confidence during API selection. Replication package: https://github.com/disa-lab/Contrastive-Learning-API-Aspect-ASE2023.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00064"}, {"primary_key": "1220128", "vector": [], "sparse_vector": [], "title": "Leakpair: Proactive Repairing of Memory Leaks in Single Page Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "Askar <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern web applications often resort to application development frameworks such as React, Vue.js, and Angular. While the frameworks facilitate the development of web applications with several useful components, they are inevitably vulnerable to unmanaged memory consumption since the frameworks often produce Single Page Applications (SPAs). Web applications can be alive for hours and days with behavior loops, in such cases, even a single memory leak in a SPA app can cause performance degradation on the client side. However, recent debugging techniques for web applications still focus on memory leak detection, which requires manual tasks and produces imprecise results. We propose Leakpair,a technique to repair memory leaks in single page applications. Given the insight that memory leaks are mostly non-functional bugs and fixing them might not change the behavior of an application, the technique is designed to proactively generate patches to fix memory leaks, without leak detection, which is often heavy and tedious. To generate effective patches, Leakpairfollows the idea of pattern-based program repair since the automated repair strategy shows successful results in many recent studies. We evaluate the technique on more than 20 open-source projects without using explicit leak detection. The patches generated by our technique are also submitted to the projects as pull requests. The results show that Leakpaircan generate effective patches to reduce memory consumption that are acceptable to developers. In addition, we execute the test suites given by the projects after applying the patches, and it turns out that the patches do not cause any functionality breakage; this might imply that Leakpaircan generate non-intrusive patches for memory leaks.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00097"}, {"primary_key": "1220129", "vector": [], "sparse_vector": [], "title": "Information Retrieval-Based Fault Localization for Concurrent Programs.", "authors": ["<PERSON><PERSON>", "Tingting Yu"], "summary": "Information retrieval-based fault localization (IRFL) techniques have been proposed as a solution to identify the files that are likely to contain faults that are root causes of failures reported by users. These techniques have been extensively studied to accurately rank source files, however, none of the existing approaches have focused on the specific case of concurrent programs. This is a critical issue since concurrency bugs are notoriously difficult to identify. To address this problem, this paper presents a novel approach called BLCoiR, which aims to reformulate bug report queries to more accurately localize source files related to concurrency bugs. The key idea of BLCoiR is based on a novel knowledge graph (KG), which represents the domain entities extracted from the concurrency bug reports and their semantic relations. The KG is then transformed into the IR query to perform fault localization. BLCoiR leverages natural language processing (NLP) and concept modeling techniques to construct the knowledge graph. Specifically, NLP techniques are used to extract relevant entities from the bug reports, such as the word entities related to concurrency constructs. These entities are then linked together based on their semantic relationships, forming the KG. We have conducted an empirical study on 692 concurrency bug reports from 44 real-world applications. The results show that BLCoiR outperforms existing IRFL techniques in terms of accuracy and efficiency in localizing concurrency bugs. BLCoiR demonstrates effectiveness of using a knowledge graph to model the domain entities and their relationships, providing a promising direction for future research in this area.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00122"}, {"primary_key": "1220130", "vector": [], "sparse_vector": [], "title": "AutoConf: Automated Configuration of Unsupervised Learning Systems Using Metamorphic Testing and Bayesian Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unsupervised learning systems using clustering have gained significant attention for numerous applications due to their unique ability to discover patterns and structures in large unlabeled datasets. However, their effectiveness highly depends on their configuration, which requires domain-specific expertise and often involves numerous manual trials. Specifically, selecting appropriate algorithms and hyperparameters adds to the complexity of the configuration process. In this paper, we propose, apply, and assess an automated approach (AutoConf) for configuring unsupervised learning systems using clustering, leveraging metamorphic testing and Bayesian optimization. Metamorphic testing is utilized to verify the configurations of unsupervised learning systems by applying a series of input transformations. We use Bayesian optimization guided by metamorphic-testing output to automatically identify the optimal configuration. The approach aims to streamline the configuration process and enhance the effectiveness of unsupervised learning systems. It has been evaluated through experiments on six datasets from three domains for anomaly detection. The evaluation results show that our approach can find configurations outperforming the baseline approaches as they achieved a recall of 0.89 and a precision of 0.84 (on average).", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00094"}, {"primary_key": "1220131", "vector": [], "sparse_vector": [], "title": "Two Birds with One Stone: Multi-Derivation for Fast Context-Free Language Reachability Analysis.", "authors": ["Chenghang Shi", "<PERSON>ofeng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingling Xue"], "summary": "Context-free language (CFL) reachability is a fundamental framework for formulating program analyses. CFL-reachability analysis works on top of an edge-labeled graph by deriving reachability relations and adding them as labeled edges to the graph. Existing CFL-reachability algorithms typically adopt a single-reachability relation derivation (SRD) strategy, i.e., one reachability relation is derived at a time. Unfortunately, this strategy can lead to redundancy, hindering the efficiency of the analysis. To address this problem, this paper proposes Pearl, a multi-derivation approach that reduces derivation redundancy for transitive relations that frequently arise when solving reachability relations, significantly improving the efficiency of CFL-reachability analysis. Our key insight is that multiple edges involving transitivity can be simultaneously derived via batch propagation of reachability relations on the transitivity-aware subgraphs that are induced from the original edge-labeled graph. We evaluate the performance of <PERSON> on two clients, i.e., context-sensitive value-flow analysis and field-sensitive alias analysis for C/C++. By eliminating a large amount of redundancy, <PERSON> achieves average speedups of 82.73x for value-flow analysis and 155.26x for alias analysis over the standard CFL-reachability algorithm. The comparison with Poc<PERSON>, a state-of-the-art CFL-reachability solver, shows that <PERSON> runs 10.1x (up to 29.2x) and 2.37x (up to 4.22x) faster on average respectively for value-flow analysis and alias analysis with less consumed memory.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00118"}, {"primary_key": "1220132", "vector": [], "sparse_vector": [], "title": "Green AI Quotient: Assessing Greenness of AI-based software and the way forward.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As the world takes cognizance of AI's growing role in greenhouse gas(GHG) and carbon emissions, the focus of AI research & development is shifting towards inclusion of energy efficiency as another core metric. Sustainability, a core agenda for most organizations, is also being viewed as a core non-functional requirement in software engineering. A similar effort is being undertaken to extend sustainability principles to AI-based systems with focus on energy efficient training and inference techniques. But an important question arises, does there even exist any metrics or methods which can quantify adoption of \"green\" practices in the life cycle of AI-based systems? There is a huge gap which exists between the growing research corpus related to sustainable practices in AI research and its adoption at an industry scale. The goal of this work is to introduce a methodology and novel metric for assessing \"greenness\" of any AI-based system and its development process, based on energy efficient AI research and practices. The novel metric, termed as Green AI Quotient, would be a key step towards AI practitioner's Green AI journey. Empirical validation of our approach suggest that Green AI Quotient is able to encourage adoption and raise awareness regarding sustainable practices in AI lifecycle.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00115"}, {"primary_key": "1220133", "vector": [], "sparse_vector": [], "title": "BugMiner: Automating Precise Bug Dataset Construction by Code Evolution History Mining.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jun<PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bugs and their fixes in the code evolution histories are important assets for many software engineering tasks such as deriving new state-of-the-art automatic bug fixing techniques. Existing bug datasets are either manually built which is difficult to grow efficiently to a scale large enough for massive data analysis, or lack of precise information of how bugs are introduced and fixed which is critical for in-depth analysis such as buggy/fixing code identification. Moreover, the types of the bugs are typically missing in the existing bug datasets, limiting the possibility of developing high-precision type-specific approaches for enterprise-level purposes. In this work, we propose BugMiner, an approach to automatically collecting bugs from code repositories by isolating the critical changes of the bugs. We also propose a learning-based approach for automating bug type classification with relatively small manual labels of bug types. We evaluate our approach regarding the precision of bug information and the efficiency of the bug-mining process with 2,082 bugs automatically mined from 100 open-source projects. We demonstrate the improved effectiveness and efficiency in bug-fixing location identification, compared to the SOTA BugBuilder, and high recall and precision in bug-inducing location identification. We also compare our learning-based bug classification approach to traditional baseline method, indicating about 17 % improvement in classification effectiveness under macro-F1.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00201"}, {"primary_key": "1220134", "vector": [], "sparse_vector": [], "title": "Bridging the Gap Between Academia and Industry in Machine Learning Software Defect Prediction: Thirteen Considerations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This experience paper describes thirteen considerations for implementing machine learning software defect prediction (ML SDP) in vivo. Specifically, we provide the following report on the ground of the most important observations and lessons learned gathered during a large-scale research effort and introduction of ML SDP to the system-level testing quality assurance process of one of the leading telecommunication vendors in the world — Nokia. We adhere to a holistic and logical progression based on the principles of the business analysis body of knowledge: from identifying the need and setting requirements, through designing and implementing the solution, to profitability analysis, stakeholder management, and handover. Conversely, for many years, industry adoption has not kept up the pace of academic achievements in the field, despite promising potential to improve quality and decrease the cost of software products for many companies worldwide. Therefore, discussed considerations hopefully help researchers and practitioners bridge the gaps between academia and industry.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00026"}, {"primary_key": "1220135", "vector": [], "sparse_vector": [], "title": "DeFiWarder: Protecting DeFi Apps from Token Leaking Vulnerabilities.", "authors": ["Ji<PERSON>zhong Su", "<PERSON><PERSON><PERSON>", "Zhiyuan Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>v", "<PERSON><PERSON><PERSON>"], "summary": "Decentralized Finance (DeFi) apps have rapidly proliferated with the development of blockchain and smart contracts, whose maximum total value locked (TVL) has exceeded 100 billion dollars in the past few years. These apps allow users to interact and perform complicated financial activities. However, the vulnerabilities hiding in the smart contracts of DeFi apps have resulted in numerous security incidents, with most of them leading to funds (tokens) leaking and resulting in severe financial loss. In this paper, we summarize Token Leaking vulnerability of DeFi apps, which enable someone to abnormally withdraw funds that far exceed their deposits. Due to the massive amount of funds in DeFi apps, it is crucial to protect DeFi apps from Token Leaking vulnerabilities. Unfortunately, existing tools have limitations in addressing this vulnerability. To address this issue, we propose DeFiWarder, a tool that traces on-chain transactions and protects DeFi apps from Token Leaking vulnerabilities. Specifically, DeFiWarder first records the execution logs (traces) of smart contracts. It then accurately recovers token transfers within transactions to catch the funds flow between users and DeFi apps, as well as the relations between users based on role mining. Finally, DeFiWarder utilizes anomaly detection to reveal Token Leaking vulnerabilities and related attack behaviors. We conducted experiments to demonstrate the effectiveness and efficiency of DeFiWarder. Specifically, DeFi-Warder successfully revealed 25 Token Leaking vulnerabilities from 30 Defi apps. Moreover, its efficiency supports real-time detection of token leaking within on-chain transactions. In addition, we summarize five major reasons for Token Leaking vulnerability to assist DeFi apps in protecting their funds.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00110"}, {"primary_key": "1220136", "vector": [], "sparse_vector": [], "title": "Live Programming for Finite Model Finders.", "authors": ["<PERSON>"], "summary": "Finite model finders give users the ability to specify properties of a system in mathematical logic and then automatically find concrete examples, called solutions, that satisfy the properties. These solutions are often viewed as a key benefit of model finders, as they create an exploratory environment for developers to engage with their model. In practice, users find less benefit from these solutions than expected. For years, researchers believed that the problem was that too many solutions are produced. However, a recent user study found that users actually prefer enumerating a broad set of solutions. Inspired by a recent user study on Alloy, a modeling language backed by a finite model finder, we believe that the issue is that solutions are too removed from the logical constraints that generate them to help users build an understanding of the constraints themselves. In this paper, we outline a proof-of-concept for live programming of Alloy models in which writing the model and exploring solutions are intertwined. We highlight how this development environment enables more productive feedback loops between the developer, the model and the solutions.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00016"}, {"primary_key": "1220137", "vector": [], "sparse_vector": [], "title": "Revisiting and Improving Retrieval-Augmented Deep Assertion Generation.", "authors": ["Weifeng Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Unit testing validates the correctness of the unit under test and has become an essential activity in software development process. A unit test consists of a test prefix that drives the unit under test into a particular state, and a test oracle (e.g., assertion), which specifies the behavior in that state. To reduce manual efforts in conducting unit testing, <PERSON> et al. proposed an integrated approach (integration for short), combining information retrieval with a deep learning-based approach, to generate assertions for a unit test. Despite being promising, there is still a knowledge gap as to why or where integration works or does not work. In this paper, we describe an in-depth analysis of the effectiveness of integration. Our analysis shows that: ① The overall performance of integration is mainly due to its success in retrieving assertions. ② integration struggles to understand the semantic differences between the retrieved focal-test (focal-test includes a test prefix and a unit under test) and the input focal-test, resulting in many tokens being incorrectly modified; ③ integration is limited to specific types of edit operations (i.e., replacement) and cannot handle token addition or deletion. To improve the effectiveness of assertion generation, this paper proposes a novel retrieve-and-edit approach named EDITAS. Specifically, <PERSON><PERSON> first retrieves a similar focal-test from a pre-defined corpus and treats its assertion as a prototype. Then, <PERSON><PERSON> reuses the information in the prototype and edits the prototype automatically. <PERSON>as is more generalizable than integration because it can ❶ comprehensively understand the semantic differences between input and similar focal-tests; ❷ apply appropriate assertion edit patterns with greater flexibility; and ❸ generate more diverse edit actions than just replacement operations. We conduct experiments on two large-scale datasets and the experimental results demonstrate that <PERSON><PERSON> outperforms the state-of-the-art approaches, with an average improvement of 10.00%-87.48% and 3.30%-42.65% in accuracy and BLEU score, respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00090"}, {"primary_key": "1220138", "vector": [], "sparse_vector": [], "title": "Who is the Real Hero? Measuring Developer Contribution via Multi-Dimensional Data Integration.", "authors": ["<PERSON><PERSON>ang Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Proper incentives are important for motivating developers in open-source communities, which is crucial for maintaining the development of open-source software healthy. To provide such incentives, an accurate and objective developer contribution measurement method is needed. However, existing methods rely heavily on manual peer review, lacking objectivity and transparency. The metrics of some automated works about effort estimation use only syntax-level or even text-level information, such as changed lines of code, which lack robustness. Furthermore, some works about identifying core developers provide only a qualitative understanding without a quantitative score or have some project-specific parameters, which makes them not practical in real-world projects. To this end, we propose CVALUE, a multidimensional information fusion-based approach to measure developer contributions. CVALUE extracts both syntax and semantic information from the source code changes in four dimensions: modification amount, understandability, inter-function and intra-function impact of modification. It fuses the information to produce the contribution score for each of the commits in the projects. Experimental results show that CVALUE outperforms other approaches by 19.59% on 10 real-world projects with manually labeled ground truth. We validated and proved that the performance of CVALUE, which takes 83.39 seconds per commit, is acceptable to be applied in real-world projects. Furthermore, we performed a large-scale experiment on 174 projects and detected 2,282 developers having inflated commits. Of these, 2,050 developers did not make any syntax contribution; and 103 were identified as bots.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00102"}, {"primary_key": "1220139", "vector": [], "sparse_vector": [], "title": "SMT Solver Validation Empowered by Large Pre-Trained Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SMT solvers are utilized to check the satisfiability of logic formulas and have been applied in various crucial domains, including software verification, test case generation, and program synthesis. However, bugs hidden in SMT solvers can lead to severe consequences, causing erroneous results in these domains. Therefore, ensuring the reliability and robustness of SMT solvers is of critical importance. Despite several testing approaches proposed for SMT solvers, generating effective test formulas to comprehensively test SMT solvers remains a challenge. To address this challenge, in this study, we propose to port large language models (LLMs) to generate SMT formulas for fuzzing solvers. Specifically, the study presents a novel retrain-finetune pipeline to unleash the potential of language models to generate effective SMT formulas and improve their generation performance through data augmentation. We implemented our approach as a practical fuzzing tool, named LasT,and then extensively tested the state-of-the-art SMT solvers, namely Z3, cvc5, and Bitwuzla. To date, <PERSON> has successfully uncovered 65 genuine bugs for the solvers, of which 45 have been fixed by the developers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00180"}, {"primary_key": "1220140", "vector": [], "sparse_vector": [], "title": "Software Engineering Using Autonomous Agents: Are We There Yet?", "authors": ["Samdyuti Suri", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous agents equipped with Large Language Models (LLMs) are rapidly gaining prominence as a revolutionary technology within the realm of Software Engineering. These intelligent and autonomous systems demonstrate the capacity to perform tasks and make independent decisions, leveraging their intrinsic reasoning and decision-making abilities. This paper delves into the current state of autonomous agents, their capabilities, challenges, and opportunities in Software Engineering practices. By employing different prompts (with or without context), we conclude the advantages of contextrich prompts for autonomous agents. Prompts with context enhance user requirement understanding, avoiding irrelevant details that could hinder task comprehension and degrade model performance, particularly when dealing with complex frameworks such as Spring Boot, Django, Flask, etc. This exploration is conducted using Auto-GPT (v0.3.0), an open-source application powered by GPT-3.5 and GPT-4 which intelligently connects the \"thoughts\" of Large Language Models (LLMs) to independently accomplish the assigned goals or tasks.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00174"}, {"primary_key": "1220141", "vector": [], "sparse_vector": [], "title": "Neural SZZ Algorithm.", "authors": ["<PERSON><PERSON><PERSON>", "Ling<PERSON> Bao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Huang"], "summary": "The SZZ algorithm has been widely used for identifying bug-inducing commits. However, it suffers from low precision, as not all deletion lines in the bug-fixing commit are related to the bug fix. Previous studies have attempted to address this issue by using static methods to filter out noise, e.g., comments and refactoring operations in the bug-fixing commit. However, these methods have two limitations. First, it is challenging to include all refactoring and non-essential change patterns in a tool, leading to the potential exclusion of relevant lines and the inclusion of irrelevant lines. Second, applying these tools might not always improve performance. In this paper, to address the aforementioned challenges, we propose NEURALSZZ, a deep learning approach for detecting the root cause deletion lines in a bug-fixing commit and using them as input for the SZZ algorithm. NEURALSZZ first constructs a heterogeneous graph attention network model that captures the semantic relationships between each deletion line and the other deletion and addition lines. To pinpoint the root cause of a bug, Neuralszz uses a learning-to-rank technique to rank all deletion lines in the commit. To evaluate the effectiveness of NEURALSZZ, we utilize three datasets containing high-quality bug-fixing and bug-inducing commits. The experiment results show that NEURALSZZ outperforms various baseline methods, e.g., traditional machine learning-based approaches and BI-LSTM in identifying the root cause of bugs. Moreover, by utilizing the top-ranked deletion lines and applying the SZZ algorithm, Neuralszz demonstrates better precision and F1-score compared to previous SZZ algorithms.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00037"}, {"primary_key": "1220142", "vector": [], "sparse_vector": [], "title": "Domain Adaptive Code Completion via Language Models and Decoupled Domain Databases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Ge", "Shang<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large Language Models (LLMs) have demonstrated remarkable performance in code completion. However, due to the lack of domain-specific knowledge, they may not be optimal in completing code that requires intensive domain knowledge for example completing the library names. Although there are several works that have confirmed the effectiveness of fine-tuning techniques to adapt language models for code completion in specific domains. They are limited by the need for constant fine-tuning of the model when the project is in constant iteration. To address this limitation, in this paper, we propose $k$ NM-LM, a retrieval-augmented language model (R-LM), that integrates domain knowledge into language models without fine-tuning. Different from previous techniques, our approach is able to automatically adapt to different language models and domains. Specifically, it utilizes the in-domain code to build the retrieval-based database decoupled from LM, and then combines it with LM through Bayesian inference to complete the code. The extensive experiments on the completion of intra-project and intra-scenario have confirmed that $k$ NM-LM brings about appreciable enhancements when compared to CodeGPT and UnixCoder. A deep analysis of our tool including the responding speed, storage usage, specific type code completion, and API invocation completion has confirmed that $k$ NM-LM provides satisfactory performance, which renders it highly appropriate for domain adaptive code completion. Furthermore, our approach operates without the requirement for direct access to the language model's parameters. As a result, it can seamlessly integrate with black-box code completion models, making it easy to integrate our approach as a plugin to further enhance the performance of these models.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00076"}, {"primary_key": "1220143", "vector": [], "sparse_vector": [], "title": "DCLINK: Bridging Data Constraint Changes and Implementations in FinTech Systems.", "authors": ["Wensheng Tang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xianjin Fu", "Gang Fan", "<PERSON>"], "summary": "A FinTech system is a cluster of FinTech applications that intensively interact with databases containing a large quantity of user data. To ensure data consistency, it is a common practice to specify data constraints to validate data at runtime. However, data constraints often evolve according to changes in business requirements. Meanwhile, the developers can hardly keep up with the latest requirements during the development cycle. Such an information barrier increases the communication burden and prevents FinTech applications from being updated in time, impeding the development cycle significantly. In this paper, we present a comprehensive empirical study on data constraints in FinTech systems, investigating how they evolve and affect the development process. Our results show that developers find it hard to update their code timely because no mapping from data constraint changes to code is provided. Inspired by the findings from code updates respecting data constraint changes, we propose DCLINK, a traceability link analysis for linking each data constraint change to target methods demanding the code update in the FinTech application. We extensively evaluate DCLINK upon real-world change cases in Ant Group. The results show that DCLINK can effectively and efficiently localize the target methods.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00170"}, {"primary_key": "1220144", "vector": [], "sparse_vector": [], "title": "Code Difference Guided Adversarial Example Generation for Deep Code Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Adversarial examples are important to test and enhance the robustness of deep code models. As source code is discrete and has to strictly stick to complex grammar and semantics constraints, the adversarial example generation techniques in other domains are hardly applicable. Moreover, the adversarial example generation techniques specific to deep code models still suffer from unsatisfactory effectiveness due to the enormous ingredient search space. In this work, we propose a novel adversarial example generation technique (i.e., CODA) for testing deep code models. Its key idea is to use code differences between the target input (i.e., a given code snippet as the model input) and reference inputs (i.e., the inputs that have small code differences but different prediction results with the target input) to guide the generation of adversarial examples. It considers both structure differences and identifier differences to preserve the original semantics. Hence, the ingredient search space can be largely reduced as the one constituted by the two kinds of code differences, and thus the testing process can be improved by designing and guiding corresponding equivalent structure transformations and identifier renaming transformations. Our experiments on 15 deep code models demonstrate the effective-ness and efficiency of CODA, the naturalness of its generated examples, and its capability of enhancing model robustness after adversarial fine-tuning. For example, CODA reveals 88.05 % and 72.51 % more faults in models than the state-of-the-art techniques (i.e., CARROT and ALERT) on average, respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00149"}, {"primary_key": "1220145", "vector": [], "sparse_vector": [], "title": "On-the-fly Improving Performance of Deep Code Models via Input Denoising.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning has been widely adopted to tackle various code-based tasks by building deep code models based on a large amount of code snippets. While these deep code models have achieved great success, even state-of-the-art models suffer from noise present in inputs leading to erroneous predictions. While it is possible to enhance models through retraining/fine-tuning, this is not a once-and-for-all approach and incurs significant overhead. In particular, these techniques cannot on-the-fly improve performance of (deployed) models. There are currently some techniques for input denoising in other domains (such as image processing), but since code input is discrete and must strictly abide by complex syntactic and semantic constraints, input denoising techniques in other fields are almost not applicable. In this work, we propose the first input denoising technique (i.e., CodeDenoise) for deep code models. Its key idea is to localize noisy identifiers in (likely) mispredicted inputs, and denoise such inputs by cleansing the located identifiers. It does not need to retrain or reconstruct the model, but only needs to cleanse inputs on-the-fly to improve performance. Our experiments on 18 deep code models (i.e., three pre-trained models with six code-based datasets) demonstrate the effectiveness and efficiency of CodeDenoise. For example, on average, CodeDenoise successfully denoises 21.91% of mispredicted inputs and improves the original models by 2.04% in terms of the model accuracy across all the subjects in an average of 0.48 second spent on each input, substantially outperforming the widely-used fine-tuning strategy.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00166"}, {"primary_key": "1220146", "vector": [], "sparse_vector": [], "title": "Open Source Software Tools for Data Management and Deep Model Training Automation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Designing and optimizing deep models require managing large datasets and conducting carefully designed controlled experiments that depend on large sets of hyper-parameters and problem dependent software/data configurations. These experiments are executed by training the model under observation with varying configurations. Since executing a typical training run can take days even on proven acceleration fabrics such as Graphics Processing Units (GPU), properly managing training data, avoiding human error in configuration preparations and securing the repeatability of the experiments are of utmost importance. In this paper, we present two open source software tools that aim to achieve these goals, namely, a Dataset Manager (DatumAid) tool and a Training Automation Manager (OrchesTrain) tool. DatumAid is a software tool that integrates with Computer Vision Annotation Tool (CVAT) to facilitate the management of annotated datasets. By adding additional functionality, DatumAid allows users to filter labeled data, manipulate datasets, and export datasets for training purposes. The tool adopts a simple code structure while providing flexibility to users through configuration files. OrchesTrain aims to automate model training process by facilitating rapid preparation and training of models in the desired style for the intended tasks. Users can seamlessly integrate their models prepared in the PyTorch library into the system and leverage the full capabilities of OrchesTrain. It enables the simultaneous or separate usage of Wandb, MLflow, and TensorBoard loggers. To ensure reproducibility of the conducted experiments, all configurations and codes are saved to the selected logger in an appropriate structure within a YAML file along with the serialized model files. Both software tools are publicly available on GitHub.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00014"}, {"primary_key": "1220147", "vector": [], "sparse_vector": [], "title": "An Energy-Aware Approach to Design Self-Adaptive AI-based Applications on the Edge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The advent of edge devices dedicated to machine learning tasks enabled the execution of AI-based applications that efficiently process and classify the data acquired by the resource-constrained devices populating the Internet of Things. The proliferation of such applications (e.g., critical monitoring in smart cities) demands new strategies to make these systems also sustainable from an energetic point of view. In this paper, we present an energy-aware approach for the design and deployment of self-adaptive AI-based applications that can balance application objectives (e.g., accuracy in object detection and frames processing rate) with energy consumption. We address the problem of determining the set of configurations that can be used to self-adapt the system with a meta-heuristic search procedure that only needs a small number of empirical samples. The final set of configurations are selected using weighted gray relational analysis, and mapped to the operation modes of the self-adaptive application. We validate our approach on an AI-based application for pedestrian detection. Results show that our self-adaptive application can outperform non-adaptive baseline configurations by saving up to 81% of energy while loosing only between 2% and 6 % in accuracy.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00046"}, {"primary_key": "1220148", "vector": [], "sparse_vector": [], "title": "Increasing the Responsiveness of Web Applications by Introducing Lazy Loading.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Front-end developers want their applications to contain no more code than is needed in order to minimize the amount of time that elapses between visiting a web page and the page becoming responsive. However, front-end code is typically written in JavaScript, the ubiquitous \"language of the web\", and tends to rely heavily on third-party packages. While the reuse of packages improves developer productivity, it is notorious for resulting in very large \"bloated\" applications, resulting in a degraded end-user experience. One way to combat such bloat is to lazily load external packages on an as-needed basis, for which support was added to JavaScript in 2020 when asynchronous, dynamic imports were added to the language standard. Unfortunately, migrating existing projects to take advantage of this feature is nontrivial, as the code changes required to introduce asynchrony may involve complex, non-local transformations. In this work, we propose an approach for automatically introducing lazy loading of third-party packages in JavaScript applications. Our approach relies on static analysis to identify external packages that can be loaded lazily and generates the code transformations required to lazily load those packages. Since the static analysis is unsound, these transformations are presented as suggestions that programmers should review and test carefully. We implement this approach in a tool called Lazifier, and evaluate Lazifier on 10 open-source front-end JavaScript applications, showing that each application was successfully refactored, reducing initial application size and load times in all cases. On average, for these applications, Lazifier reduces initial application size by 36.2 %, initial load time by 29.7 %, and unsoundness did not arise in any of these applications.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00192"}, {"primary_key": "1220150", "vector": [], "sparse_vector": [], "title": "Using Deep Learning to Automatically Improve Code Readability.", "authors": ["<PERSON>", "Valentina Piantadosi", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reading source code occupies most of developer's daily activities. Any maintenance and evolution task requires developers to read and understand the code they are going to modify. For this reason, previous research focused on the definition of techniques to automatically assess the readability of a given snippet. However, when many unreadable code sections are detected, developers might be required to manually modify them all to improve their readability. While existing approaches aim at solving specific readability-related issues, such as improving variable names or fixing styling issues, there is still no approach to automatically suggest which actions should be taken to improve code readability. In this paper, we define the first holistic readability-improving approach. As a first contribution, we introduce a methodology for automatically identifying readability-improving commits, and we use it to build a large dataset of 122k commits by mining the whole revision history of all the projects hosted on GitHub between 2015 and 2022. We show that such a methodology has ~86% accuracy. As a second contribution, we train and test the T5 model to emulate what developers did to improve readability. We show that our model achieves a perfect prediction accuracy between 21% and 28%. The results of a manual evaluation we performed on 500 predictions shows that when the model does not change the behavior of the input and it applies changes (34% of the cases), in the large majority of the cases (79.4%) it allows to improve code readability.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00112"}, {"primary_key": "1220151", "vector": [], "sparse_vector": [], "title": "QuCAT: A Combinatorial Testing Tool for Quantum Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the increased developments in quantum computing, the availability of systematic and automatic testing approaches for quantum programs is becoming increasingly essential. To this end, we present the quantum software testing tool QuCAT for combinatorial testing of quantum programs. QuCAT provides two functionalities of use. With the first functionality, the tool generates a test suite of a given strength (e.g., pairwise). With the second functionality, it generates test suites with increasing strength until a failure is triggered or a maximum strength is reached. QuCAT uses two test oracles to check the correctness of test outputs. We assess the cost and effectiveness of QuCAT with 3 faulty versions of 5 quantum programs. Results show that combinatorial test suites with a low strength can find faults with limited cost, while a higher strength performs better to trigger some difficult faults with relatively higher cost.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00062"}, {"primary_key": "1220152", "vector": [], "sparse_vector": [], "title": "Zero-Config Fuzzing for Microservices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The microservice paradigm is a popular software development pattern that breaks down a large application into smaller, independent services. While this approach offers several advantages, such as scalability, agility, and flexibility, it also introduces new security challenges. This paper presents a novel approach to securing microservice architectures using fuzz testing. Fuzz testing is known to find programming errors in software by feeding it with unexpected or random inputs. In this paper, we propose a zero-config fuzz test generation technique for microservices that can maximize coverage of internal states by mutating both the incoming requests and the backend responses from dependent services. We successfully deployed our technique to over 95 % of C++ services built on Google's internal microservice platform. It reported and got fixed thousands of errors in real-world microservice applications.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00036"}, {"primary_key": "1220153", "vector": [], "sparse_vector": [], "title": "Detecting Smart Home Automation Application Interferences with Domain Knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Trigger-action programming (TAP) is a widely used development paradigm that simplifies the Internet of Things (loT) automation. However, the exceptional interactions between automation applications may result in interferences, such as conflicts and infinite loops, which cause undesirable consequences and even security and safety risks. While several techniques have been proposed to address this problem, they are often restricted in handling explicit and simple conflicts without considering contextual influences. In addition, they suffer from performance issues when applying to large-scale applications. To address these challenges, we design an effective and practical tool KnowDetector with comprehensive domain knowledge to detect application interferences. To detect application interferences, KnowDetector constructs an automation graph with 1) events, conditions, and actions from automation applications, 2) vertices representing physical environment channels, and 3) edges derived from potential semantic relations between the vertices. In order to make the graph extensively capture the interactions between automation applications, we propose a knowledge model named KnowloT that accurately characterizes loT devices with command-level loT services and the intricate relations between these services and the contextual environment. We abstract the interference detection into a graph pattern-matching problem and summarize ten application interference patterns of four types. Finally, KnowDetector can efficiently detect application interferences by searching for sub-graphs matching the patterns within the automation graph. We evaluated KnowDetector on three real-world datasets. The results demonstrated that it outperformed the other state-of-the-art tools with the highest precision, recall, and F-measure. In addition, KnowDetector is scalable to detect application interferences within a large number of applications with a minimal time overhead.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00070"}, {"primary_key": "1220154", "vector": [], "sparse_vector": [], "title": "MLIRSmith: Random Program Generation for Fuzzing MLIR Compiler Infrastructure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "MLIR (Multi-Level Intermediate Representation) compiler infrastructure has gained popularity in recent years to support the construction of many compilers. Instead of designing a new IR with a single abstraction for each domain, MLIR compiler infrastructure provides systematic passes to support a wide range of functionalities for benefiting multiple domains together and introduces dialects to support different levels of abstraction in MLIR. Due to its fundamental role in compiler community, ensuring its quality is very critical. In this work, we propose MLIRSmith, the first fuzzing technique for MLIR compiler infrastructure. MLIRSmith employs a two-phase strategy to generate valid and diverse MLIR programs, which first constructs diverse program templates guided by extended MLIR syntax rules and then generates valid MLIR programs through template instantiation guided by our designed context-sensitive grammar. After applying <PERSON><PERSON><PERSON><PERSON><PERSON> to the latest revision of MLIR compiler infrastructure, we detected 53 previously unknown bugs, among which 49/38 have been confirmed/fixed by developers. We also transform the high-level programs generated by NNSmith (a high-level program generator for deep learning compilers) to MLIR programs for indirectly fuzzing MLIR compiler infrastructure. During the same testing time, <PERSON><PERSON>IR<PERSON>mith largely outperforms such an indirect technique by detecting 328.57% more bugs and covering 194.67%/225.87% more lines/branches in MLIR compiler infrastructure.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00120"}, {"primary_key": "1220155", "vector": [], "sparse_vector": [], "title": "An Image is Worth a Thousand Toxic Words: A Metamorphic Testing Framework for Content Moderation Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The exponential growth of social media platforms has brought about a revolution in communication and content dissemination in human society. Nevertheless, these platforms are being increasingly misused to spread toxic content, including hate speech, malicious advertising, and pornography, leading to severe negative consequences such as harm to teenagers' mental health. Despite tremendous efforts in developing and deploying textual and image content moderation methods, malicious users can evade moderation by embedding texts into images, such as screenshots of the text, usually with some interference. We find that modern content moderation software's performance against such malicious inputs remains underexplored. In this work, we propose OASIS, a metamorphic testing framework for content moderation software. OASIS employs 21 transform rules summarized from our pilot study on 5,000 real-world toxic contents collected from 4 popular social media applications, including Twitter, Instagram, Sina Weibo, and Baidu Tieba. Given toxic textual contents, OASIS can generate image test cases, which preserve the toxicity yet are likely to bypass moderation. In the evaluation, we employ OASIS to test five commercial textual content moderation software from famous companies (i.e., Google Cloud, Microsoft Azure, Baidu Cloud, Alibaba Cloud and Tencent Cloud), as well as a state-of-the-art moderation research model. The results show that OASIS achieves up to 100% error finding rates. Moreover, through retraining the models with the test cases generated by OASIS, the robustness of the moderation model can be improved without performance degradation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00189"}, {"primary_key": "1220156", "vector": [], "sparse_vector": [], "title": "ConfTainter: Static Taint Analysis For Configuration Options.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ji<PERSON>", "Yu <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The prevalence and severity of software configuration-induced issues have driven the design and development of a number of detection and diagnosis techniques. Many of these techniques need to perform static taint analysis on configuration-related variables to analyze the data flow, control flow, and execution paths given by configuration options. However, existing taint analysis or static slicer tools are not suitable for configuration analysis due to the complex effects of configuration on program behaviors. In this experience paper, we conducted an empirical study on the propagation policy of configuration options. We concluded four rules of how configurations affect program behaviors, among which implicit data-flow and control-flow propagation are often ignored by existing tools. We report our experience designing and implementing a taint analysis infrastructure for configurations, ConfTainter. It can support various kinds of configuration analysis, e.g., explicit or implicit analysis for data or control flow. Based on the infrastructure, researchers and developers can easily implement analysis techniques for different configuration-related targets, e.g., misconfiguration detection. We evaluated the effectiveness of ConfTainter on 5 popular open-source systems. The result shows that the accuracy rate of data- and control-flow analysis is 96.1% and 97.7%, and the recall rate is 94.2% and 95.5%, respectively. We also apply ConfTainter to two types of configuration-related tasks: misconfiguration detection and configuration-related bug detection. The result shows that ConfTainter is highly applicable for configuration-related tasks with a few lines of code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00067"}, {"primary_key": "1220157", "vector": [], "sparse_vector": [], "title": "Generating Variable Explanations via Zero-shot Prompt Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As basic elements in program, variables convey essential information that is critical for program comprehension and maintenance. However, understanding the meanings of variables in program is not always easy for developers, since poor-quality variable names are prevalent while such variable are less informative for program comprehension. Therefore, in this paper, we target at generating concise natural language explanations for variables to facilitate program comprehension. In particular, there are two challenges in variable explanation generation, including the lack of training data and the association with complex code contexts around the variable. To address these issues, we propose a novel approach ZeroVar,which leverages code pre-trained models and zero-shot prompt learning to generate explanations for the variable based on its code context. ZeroVarcontains two stages: (i) a pre-training stage that continually pre-trains a base model (i.e., CodeT5) to recover the randomly-masked parameter descriptions in method docstrings; and (ii) a zero-shot prompt learning stage that leverages the pre-trained model to generate explanations for a given variable via the prompt constructed with the variable and its belonging method context. We then extensively evaluate the quality and usefulness of the variable explanations generated by ZeroVar.We construct an evaluation dataset of 773 variables and their reference explanations. Our results show that ZeroVarcan generate higher-quality explanations than baselines, not only on automated metrics such as BLEU and ROUGE, but also on human metrics such as correctness, completeness, and conciseness. Moreover, we further assess the usefulness of ZeroVAR-generated explanations on two downstream tasks related to variable naming quality, i.e., abbreviation expansion and spelling correction. For abbreviation expansion, the generated variable explanations can help improve the present rate (+13.1%), precision (+3.6%), and recall (+10.0%) of the state-of-the-art abbreviation explanation approach. For spelling correction, by using the generated explanations we can achieve higher hit@1 (+162.9(%) and hit@3 (+49.6%) than the recent variable representation learning approach.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00130"}, {"primary_key": "1220158", "vector": [], "sparse_vector": [], "title": "REEF: A Framework for Collecting Real-World Vulnerabilities and Fixes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Shuzheng Gao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software plays a crucial role in our daily lives, and therefore the quality and security of software systems have become increasingly important. However, vulnerabilities in software still pose a significant threat, as they can have serious consequences. Recent advances in automated program repair have sought to automatically detect and fix bugs using data-driven techniques. Sophisticated deep learning methods have been applied to this area and have achieved promising results. However, existing benchmarks for training and evaluating these techniques remain limited, as they tend to focus on a single programming language and have relatively small datasets. Moreover, many benchmarks tend to be outdated and lack diversity, focusing on a specific codebase. Worse still, the quality of bug explanations in existing datasets is low, as they typically use imprecise and uninformative commit messages as explanations. To address these issues, we propose an automated collecting framework REEF to collect REal-world vulnErabilities and Fixes from open-source repositories. We focus on vulnerabilities since they are exploitable and have serious consequences. We develop a multi-language crawler to collect vulnerabilities and their fixes, and design metrics to filter for high-quality vulnerability-fix pairs. Furthermore, we propose a neural language model-based approach to generate high-quality vulnerability explanations, which is key to producing informative fix messages. Through extensive experiments, we demonstrate that our approach can collect high-quality vulnerability-fix pairs and generate strong explanations. The dataset we collect contains 4,466 CVEs with 30,987 patches (including 236 CWE) across 7 programming languages with detailed related information, which is superior to existing benchmarks in scale, coverage, and quality. Evaluations by human experts further confirm that our framework produces high-quality vulnerability explanations.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00199"}, {"primary_key": "1220159", "vector": [], "sparse_vector": [], "title": "VRGuide: Efficient Testing of Virtual Reality Scenes via Dynamic Cut Coverage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) is an emerging technique that has been applied to more and more areas such as gaming, remote conference, and education. Since VR user interface has very different characteristics compared with traditional graphic user interface (GUI), VR applications also require new testing techniques for quality assurance. Recently, some frameworks (e.g., VRTest) have been proposed to automate VR user interface testing by automatically controlling the player camera. However, their testing strategies are not able to address VR-specific testing challenges such as object occlusion and movement. In this paper, we propose a novel testing technique called VRGuide to explore VR scenes more efficiently. In particular, VRGuide adapts a computer geometry technique called Cut Extension to optimize the camera routes for covering all interact-able objects. We compared the testing strategy with VRTest on eight top VR software projects with scenes. The results show that VRGuide is able to achieve higher test coverage upon testing timeout in two of the projects, and achieve saturation coverage with averagely 31% less testing time than VRTest on the remaining six projects. Furthermore, VRGuide detected and reported four unknown bugs confirmed by developers, only one of which is also detected by VRTest.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00197"}, {"primary_key": "1220160", "vector": [], "sparse_vector": [], "title": "LogOnline: A Semi-Supervised Log-Based Anomaly Detector Aided with Online Learning Mechanism.", "authors": ["<PERSON><PERSON><PERSON>", "Ji<PERSON><PERSON> Song", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logs are prevalent in modern cloud systems and serve as a valuable source of information for system maintenance. Over the years, a lot of research and industrial efforts have been devoted to the field of log-based anomaly detection. Through analyzing the limitations of existing approaches, we find that most of them still suffer from practical issues and are thus hard to be applied in real-world scenarios. For example, supervised approaches are dependent on a large amount of labeled log data for training, which can require much manual labeling effort. Besides, log instability, which is a pervasive issue in real-world systems, poses great challenge to existing methods, especially under the presence of many dissimilar new log events. To overcome these problems, we propose LogOnline, which is a semi supervised anomaly detector aided with online learning mechanism. The semi-supervised nature of LogOnline makes it able to get rid of the erroneous and time-consuming manual labeling of log data. Based on our proposed online learning mechanism, LogOnline can learn the normal sequence patterns continuously as new log sequences emerge, thus staying robust to unstable log data. Unlike previous works, the proposed online learning mechanism requires no labeled log data nor human intervention in the process. We have evaluated LogOnline on two widely used public datasets, and the experimental results demonstrate the effectiveness of LogOnline. In particular, LogOnline achieves a comparable result with the studied supervised approaches, outperforming all semi-supervised counterparts. When the log instability issue is more common, LogOnline exhibits the best performance over all compared approaches, further confirming its practicability.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00043"}, {"primary_key": "1220161", "vector": [], "sparse_vector": [], "title": "Delving into Commit-Issue Correlation to Enhance Commit Message Generation Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shuhua Shi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Commit message generation (CMG) is a challenging task in automated software engineering that aims to generate natural language descriptions of code changes for commits. Previous methods all start from the modified code snippets, outputting commit messages through template-based, retrieval-based, or learning-based models. While these methods can summarize what is modified from the perspective of code, they struggle to provide reasons for the commit. The correlation between commits and issues that could be a critical factor for generating rational commit messages is still unexplored. In this work, we delve into the correlation between commits and issues from the perspective of dataset and methodology. We construct the first dataset anchored on combining correlated commits and issues. The dataset consists of an unlabeled commit-issue parallel part and a labeled part in which each example is provided with human-annotated rational information in the issue. Furthermore, we propose ExGroFi (Extraction, Grounding, Ene-tuning), a novel paradigm that can introduce the correlation between commits and issues into the training phase of models. To evaluate whether it is effective, we perform comprehensive experiments with various state-of-the-art CMG models. The results show that compared with the original models, the performance of ExGroFi-enhanced models is significantly improved.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00050"}, {"primary_key": "1220162", "vector": [], "sparse_vector": [], "title": "Fork Entropy: Assessing the Diversity of Open Source Software Projects&apos; Forks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On open source software (OSS) platforms such as GitHub, forking and accepting pull-requests is an important approach for OSS projects to receive contributions, especially from external contributors who cannot directly commit into the source repositories. Having a large number of forks is often considered as an indicator of a project being popular. While extensive studies have been conducted to understand the reasons of forking, communications between forks, features and impacts of forks, there are few quantitative measures that can provide a simple yet informative way to gain insights about an OSS project's forks besides their count. Inspired by studies on biodiversity and OSS team diversity, in this paper, we propose an approach to measure the diversity of an OSS project's forks (i.e., its fork population). We devise a novel fork entropy metric based on <PERSON>'s quadratic entropy to measure such diversity according to the forks' modifications to project files. With properties including symmetry, continuity, and monotonicity, the proposed fork entropy metric is effective in quantifying the diversity of a project's fork population. To further examine the usefulness of the proposed metric, we conduct empirical studies with data retrieved from fifty projects on GitHub. We observe significant correlations between a project's fork entropy and different outcome variables including the project's external productivity measured by the number of external contributors' commits, acceptance rate of external contributors' pull-requests, and the number of reported bugs. We also observe significant interactions between fork entropy and other factors such as the number of forks. The results suggest that fork entropy effectively enriches our understanding of OSS projects' forks beyond the simple number of forks, and can potentially support further research and applications.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00168"}, {"primary_key": "1220163", "vector": [], "sparse_vector": [], "title": "When Less is Enough: Positive and Unlabeled Learning Model for Vulnerability Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated code vulnerability detection has gained increasing attention in recent years. The deep learning (DL)-based methods, which implicitly learn vulnerable code patterns, have proven effective in vulnerability detection. The performance of DL-based methods usually relies on the quantity and quality of labeled data. However, the current labeled data are generally automatically collected, such as crawled from human-generated commits, making it hard to ensure the quality of the labels. Prior studies have demonstrated that the non-vulnerable code (i.e., negative labels) tends to be unreliable in commonly-used datasets, while vulnerable code (i.e., positive labels) is more determined. Considering the large numbers of unlabeled data in practice, it is necessary and worth exploring to leverage the positive data and large numbers of unlabeled data for more accurate vulnerability detection. In this paper, we focus on the Positive and Unlabeled (PU) learning problem for vulnerability detection and propose a novel model named PILOT, i.e., Positive and unlabeled Learning mOdel for vulnerability deTection. PILOT only learns from positive and unlabeled data for vulnerability detection. It mainly contains two modules: (1) A distance-aware label selection module, aiming at generating pseudo-labels for selected unlabeled data, which involves the inter-class distance prototype and progressive fine-tuning; (2) A mixed-supervision representation learning module to further alleviate the influence of noise and enhance the discrimination of representations. Extensive experiments in vulnerability detection are conducted to evaluate the effectiveness of PILOT based on real-world vulnerability datasets. The experimental results show that PILOT outperforms the popular weakly supervised methods by 2.78%-18.93% in the PU learning setting. Compared with the state-of-the-art methods, PILOT also improves the performance of 1.34%-12.46 % in F1 score metrics in the supervised setting. In addition, PILOT can identify 23 mislabeled from the FFMPeg+Qemu dataset in the PU learning setting based on manual checking.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00144"}, {"primary_key": "1220165", "vector": [], "sparse_vector": [], "title": "CoMSA: A Modeling-Driven Sampling Approach for Configuration Performance Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Highly configurable systems enable customers to flexibly configure the systems in diverse deployment environments. The flexibility of configurations also poses challenges for performance testing. On one hand, there exist a massive number of possible configurations; while on the other hand, the time and resources are limited for performance testing, which is already a costly process during software development. Modeling the performance of configurations is one of the solutions to reduce the cost of configuration performance testing. Although prior research proposes various modeling and sampling techniques to build configuration performance models, the sampling approaches used in the model typically do not consider the accuracy of the performance models, leading to potential sub-optimal performance modeling results in practice. In this paper, we present a modeling-driven sampling approach (CoMSA) to improve the performance modeling of highly configurable systems. The intuition of CoMSA is to select samples based on their uncertainties to the performance models. In other words, the configurations that have the more uncertain performance prediction results by the performance models are more likely to be selected as further training samples to improve the model. CoMSA is designed by considering both scenarios where 1) the software projects do not have historical performance testing results (cold start) and 2) there exist historical performance testing results (warm start). We evaluate the performance of our approach in four subjects, namely LRZIP, LLVM, x264, and SQLite. Through the evaluation result, we can conclude that our sampling approaches could highly enhance the accuracy of the prediction models and the efficiency of configuration performance testing compared to other baseline sampling approaches.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00091"}, {"primary_key": "1220166", "vector": [], "sparse_vector": [], "title": "The Plastic Surgery Hypothesis in the Era of Large Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automated Program Repair (APR) aspires to automatically generate patches for an input buggy program. Traditional APR tools typically focus on specific bug types and fixes through the use of templates, heuristics, and formal specifications. However, these techniques are limited in terms of the bug types and patch variety they can produce. As such, researchers have designed various learning-based APR tools with recent work focused on directly using Large Language Models (LLMs) for APR. While LLM-based APR tools are able to achieve state-of-the-art performance on many repair datasets, the LLMs used for direct repair are not fully aware of the project-specific information such as unique variable or method names. The plastic surgery hypothesis is a well-known insight for APR, which states that the code ingredients to fix the bug usually already exist within the same project. Traditional APR tools have largely leveraged the plastic surgery hypothesis by designing manual or heuristic-based approaches to exploit such existing code ingredients. However, as recent APR research starts focusing on LLM-based approaches, the plastic surgery hypothesis has been largely ignored. In this paper, we ask the following question: How useful is the plastic surgery hypothesis in the era of LLMs? Interestingly, LLM-based APR presents a unique opportunity to fully automate the plastic surgery hypothesis via fine-tuning (training on the buggy project) and prompting (directly providing valuable code ingredients as hints to the LLM). To this end, we propose FitRepair, which combines the direct usage of LLMs with two domain-specific fine-tuning strategies and one prompting strategy (via information retrieval and static analysis) for more powerful APR. While traditional APR techniques require intensive manual efforts in both generating patches based on the plastic surgery hypothesis and guaranteeing patch validity, our approach is fully automated and general. Moreover, while it is very challenging to manually design heuristics/patterns for effectively leveraging the hypothesis, due to the power of LLMs in code vectorization/understanding, even partial/imprecise project-specific information can still guide LLMs in generating correct patches! Our experiments on the widely studied Defects4j 1.2 and 2.0 datasets show that FitRepair fixes 89 and 44 bugs (substantially outperforming baseline techniques by 15 and 8), respectively, demonstrating a promising future of the plastic surgery hypothesis in the era of LLMs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00047"}, {"primary_key": "1220167", "vector": [], "sparse_vector": [], "title": "Personalized First Issue Recommender for Newcomers in Open Source Projects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many open source projects provide good first issues (GFIs) to attract and retain newcomers. Although several automated GFI recommenders have been proposed, existing recommenders are limited to recommending generic GFIs without considering differences between individual newcomers. However, we observe mismatches between generic GFIs and the diverse background of newcomers, resulting in failed attempts, discouraged onboarding, and delayed issue resolution. To address this problem, we assume that personalized first issues (PFIs) for newcomers could help reduce the mismatches. To justify the assumption, we empirically analyze 37 newcomers and their first issues resolved across multiple projects. We find that the first issues resolved by the same newcomer share similarities in task type, programming language, and project domain. These findings underscore the need for a PFI recommender to improve over state-of-the-art approaches. For that purpose, we identify features that influence newcomers' personalized selection of first issues by analyzing the relationship between possible features of the newcomers and the characteristics of the newcomers' chosen first issues. We find that the expertise preference, OSS experience, activeness, and sentiment of newcomers drive their personalized choice of the first issues. Based on these findings, we propose a Personalized First Issue Recommender (PFIRec), which employs LamdaMART to rank candidate issues for a given newcomer by leveraging the identified influential features. We evaluate PFIRec using a dataset of 68,858 issues from 100 GitHub projects. The evaluation results show that PFIRec outperforms existing first issue recommenders, potentially doubling the probability that the top recommended issue is suitable for a specific newcomer and reducing one-third of a newcomer's unsuccessful attempts to identify suitable first issues, in the median. We provide a replication package at https://zenodo.org/record/7915841.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00158"}, {"primary_key": "1220168", "vector": [], "sparse_vector": [], "title": "PhyFu: Fuzzing Modern Physics Simulation Engines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A physical simulation engine (PSE) is a software system that simulates physical environments and objects. Modern PSEs feature both forward and backward simulations, where the forward phase predicts the behavior of a simulated system, and the backward phase provides gradients (guidance) for learning-based control tasks, such as a robot arm learning to fetch items. This way, modern PSEs show promising support for learning-based control methods. To date, PSEs have been largely used in various high-profitable, commercial applications, such as games, movies, virtual reality (VR), and robotics. Despite the prosperous development and usage of PSEs by academia and industrial manufacturers such as Google and NVIDIA, PSEs may produce incorrect simulations, which may lead to negative results, from poor user experience in entertainment to accidents in robotics-involved manufacturing and surgical operations. This paper introduces PhyFu, a fuzzing framework designed specifically for PSEs to uncover errors in both forward and backward simulation phases. PHyFu mutates initial states and asserts if the PSE under test behaves consistently with respect to basic Physics Laws (PLs). We further use feedback-driven test input scheduling to guide and accelerate the search for errors. Our study of four PSEs covers mainstream industrial vendors (Google and NVIDIA) as well as academic products. We successfully uncover over 5K error-triggering inputs that generate incorrect simulation results spanning across the whole software stack of PSEs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00054"}, {"primary_key": "1220169", "vector": [], "sparse_vector": [], "title": "LEAP: Efficient and Automated Test Method for NLP Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Hai Dong", "Shunhui Ji", "Peng<PERSON> Zhang"], "summary": "The widespread adoption of DNNs in NLP software has highlighted the need for robustness. Researchers proposed various automatic testing techniques for adversarial test cases. However, existing methods suffer from two limitations: weak error-discovering capabilities, with success rates ranging from 0% to 24.6% for BERT-based NLP software, and time inefficiency, taking 177.8s to 205.28s per test case, making them challenging for time-constrained scenarios. To address these issues, this paper proposes LEAP, an automated test method that uses LEvy flight-based Adaptive Particle swarm optimization integrated with textual features to generate adversarial test cases. Specifically, we adopt Levy flight for population initialization to increase the diversity of generated test cases. We also design an inertial weight adaptive update operator to improve the efficiency of LEAP's global optimization of high-dimensional text examples and a mutation operator based on the greedy strategy to reduce the search time. We conducted a series of experiments to validate LEAP's ability to test NLP software and found that the average success rate of LEAP in generating adversarial test cases is 79.1%, which is 6.1% higher than the next best approach (PSO attack ). While ensuring high success rates, LEAP significantly reduces time overhead by up to 147.6s compared to other heuristic-based methods. Additionally, the experimental results demonstrate that LEAP can generate more transferable test cases and significantly enhance the robustness of DNN-based systems.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00052"}, {"primary_key": "1220170", "vector": [], "sparse_vector": [], "title": "ExpressAPR: Efficient Patch Validation for Java Automated Program Repair Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated program repair (APR) approaches suffer from long patch validation time, which limits their practical application and receives relatively low attention. The patch validation process repeatedly executes tests to filter patches, and has been recognized as the dual of mutation analysis. We systematically investigate existing mutation testing techniques and recognize five families of acceleration techniques that are suitable for patch validation, two of which are never adapted to a general-purpose patch validator. We implement and demonstrate ExpressAPR, the first framework that combines five families of acceleration techniques for patch validation as the complete set. In our evaluation on 30 random Defects4J bugs and four APR systems, ExpressAPR accelerates patch validation for two order-of-magnitudes over plain validation or one order-of-magnitude over the state-of-the-art approach, benefiting APR researchers and users with a much shorter patch validation time. Demo video available at https://youtu.be/7AB-4VvBuuM Tool repo (source code + Docker image + evaluation dataset) available at https://github.com/ExpressAPR/ExpressAPR", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00012"}, {"primary_key": "1220171", "vector": [], "sparse_vector": [], "title": "HexT5: Unified Pre-Training for Stripped Binary Code Information Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kejiang Chen", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Decompilation is a widely used process for reverse engineers to significantly enhance code readability by lifting assembly code to a higher-level C-like language, pseudo-code. Nevertheless, the process of compilation and stripping irreversibly discards high-level semantic information that is crucial to code comprehension, such as comments, identifier names, and types. Existing approaches typically recover only one type of information, making them suboptimal for semantic inference. In this paper, we treat pseudo-code as a special programming language, then present a unified pre-trained model, HexT5, that is trained on vast amounts of natural language comments, source identifiers, and pseudo-code using novel pseudo-code-based pre-training objectives. We fine-tune HexT5 on various downstream tasks, including code summarization, variable name recovery, function name recovery, and similarity detection. Comprehensive experiments show that HexT5 achieves state-of-the-art performance on four downstream tasks, and it demonstrates the robust effectiveness and generalizability of HexT5 for binary-related tasks.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00099"}, {"primary_key": "1220172", "vector": [], "sparse_vector": [], "title": "Understanding and Remediating Open-Source License Incompatibilities in the PyPI Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The reuse and distribution of open-source software must be in compliance with its accompanying open-source license. In modern packaging ecosystems, maintaining such compliance is challenging because a package may have a complex multi-layered dependency graph with many packages, any of which may have an incompatible license. Although prior research finds that license incompatibilities are prevalent, empirical evidence is still scarce in some modern packaging ecosystems (e.g., PyPI). It also remains unclear how developers remediate the license incompatibilities in the dependency graphs of their packages (including direct and transitive dependencies), let alone any automated approaches. To bridge this gap, we conduct a large-scale empirical study of license incompatibilities and their remediation practices in the PyPI ecosystem. We find that 7.27% of the PyPI package releases have license incompatibilities and 61.3 % of them are caused by transitive dependencies, causing challenges in their remediation; for remediation, developers can apply one of the five strategies: migration, removal, pinning versions, changing their own licenses, and negotiation. Inspired by our findings, we propose Silence, an SMT-solver-based approach to recommend license incompatibility remediations with minimal costs in package dependency graph. Our evaluation shows that the remediations proposed by Silencecan match 19 historical real-world cases (except for migrations not covered by an existing knowledge base) and have been accepted by five popular PyPI packages whose developers were previously unaware of their license incompatibilities.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00175"}, {"primary_key": "1220173", "vector": [], "sparse_vector": [], "title": "EXPRESS 2.0: An Intelligent Service Management Framework for AIoT Systems in the Edge.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Wuzhen Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AIoT (Artificial Intelligence of Things) which integrates AI and IoT has received rapidly growing interest from the software engineering community in recent years. It is crucial to design scalable, efficient, and reliable software solutions for large-scale AIoT systems in edge computing environments. However, the lack of effective service management including the support for service collaboration, AI application, and data security in the edge, has seriously limited the development of AIoT systems. To seal this gap, we propose EXPRESS 2.0 which is an intelligent service management framework for AI oT in the edge. Specifically, on top of the existing EXPRESS platform, EXPRESS 2.0 includes the intelligent service collaboration management module, AI application management module, and data security management module. To demonstrate the effectiveness of the framework, we design and implement a last-mile delivery system using both UAVs (Unmanned Aerial Vehicles) and UGVs (Unmanned Ground Vehicles). The EXPRESS 2.0 is open-sourced at https://github.com/ISEC-AHU/EXPRESS2.0. A video demonstration of EXPRESS 2.0 is at https://youtu.be/GHKD_VvJD88.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00020"}, {"primary_key": "1220174", "vector": [], "sparse_vector": [], "title": "Are We Ready to Embrace Generative AI for Software Q&amp;A?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stack Overflow, the world's largest software Q&A (SQA) website, is facing a significant traffic drop due to the emergence of generative AI techniques. ChatGPT is banned by Stack Overflow after only 6 days from its release. The main reason provided by the official Stack Overflow is that the answers generated by ChatGPT are of low quality. To verify this, we conduct a comparative evaluation of human-written and ChatGPT-generated answers. Our methodology employs both automatic comparison and a manual study. Our results suggest that human-written and ChatGPT-generated answers are semantically similar, however, human-written answers outperform ChatGPT-generated ones consistently across multiple aspects, specifically by 10% on the overall score. We release the data, analysis scripts, and detailed results at https://github.com/maxxbw54/GAI4SQA.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00023"}, {"primary_key": "1220175", "vector": [], "sparse_vector": [], "title": "Compsuite: A Dataset of Java Library Upgrade Incompatibility Issues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern software systems heavily rely on external libraries developed by third-parties to ensure efficient development. However, frequent library upgrades can lead to compatibility issues between the libraries and their client systems. In this paper, we introduce Compsuite, a dataset that includes 123 real-world Java client-library pairs where upgrading the library causes an incompatibility issue in the corresponding client. Each incompatibility issue in Compsuite is associated with a test case authored by the developers, which can be used to reproduce the issue. The dataset also provides a command-line interface that simplifies the execution and validation of each issue. With this infrastructure, users can perform an inspection of any incompatibility issue with the push of a button, or reproduce an issue step-by-step for a more detailed investigation. We make Compsuite publicly available to promote open science. We believe that various software analysis techniques, such as compatibility checking, debugging, and regression test selection, can benefit from Compsuite. The demonstration video of <PERSON>mpsuit<PERSON> is available at https://www.youtube.com/watch?v=7DQGsGs_65s.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00127"}, {"primary_key": "1220176", "vector": [], "sparse_vector": [], "title": "Potential Solutions to Challenges in C Program Repair: A Practical Perspective.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated program repair is to reduce the manual work for bug fixing by human developers. In recent 15 years, the research community of program repair has created many novel techniques. However, these techniques share several assumptions that cannot always be satisfied in daily software development. This badly hurts the application of program repair in practice. For example, many repair techniques assume that test cases are well written before patch generation; many techniques assume that specific language features can be ignored (or already-processed). In this paper, we propose a framework of C program repair, which mainly addresses two challenges: test-independent repair and preprocessor directive processing. Our solution to test-independent repair is to automatically construct patch conditions for C programs via parsing the syntax structures; our solution to preprocessor directive processing is to generate code symbols to replace preprocessor directives. We plan to implement these potential solutions with program analysis techniques. The goal of this paper is to present practical solutions for developers to automate C program repair.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00211"}, {"primary_key": "1220177", "vector": [], "sparse_vector": [], "title": "ACWRecommender: A Tool for Validating Actionable Warnings with Weak Supervision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Static analysis tools have gained popularity among developers for finding potential bugs, but their widespread adoption is hindered by the accomnpanying high false alarm rates (up to 90%). To address this challenge, previous studies proposed the concept of actionable warnings, and apply machine-learning methods to distinguish actionable warnings from false alarms. Despite these efforts, our preliminary study suggests that the current methods used to collect actionable warnings are rather shaky and unreliable, resulting in a large proportion of invalid actionable warnings. In this work, we mined 68,274 reversions from Top-500 Github C repositories to create a substantia actionable warning dataset and assigned weak labels to each warning's likelihood of being a real bug. To automatically identify actionable warnings and recommend those with a high probability of being real bugs (AWHB), we propose a two-stage framework called ACWRecommender. In the first stage, our tool use a pre-trained model, i.e., UniXcoder, to identify actionable warnings from a huge number of SA tool's reported warnings. In the second stage, we rerank valid actionable warnings to the top by using weakly supervised learning. Experimental results showed that our tool outperformed several baselines for actionable warning detection (in terms of F1-score) and performed better for AWHB recommendation (in terms of nDCG and MRR). Additionaly, we also performed an in-the-wild evaluation, we manually validated 24 warnings out of 2,197 reported warnings on 10 randomly selected projects, 22 of which were confirmed by developers as real bugs, demonstrating the practical usage of our tool.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00169"}, {"primary_key": "1220178", "vector": [], "sparse_vector": [], "title": "An Intentional Forgetting-Driven Self-Healing Method for Deep Reinforcement Learning Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep reinforcement learning (DRL) is increasingly applied in large-scale productions like Netflix and Facebook. As with most data-driven systems, DRL systems can exhibit undesirable behaviors due to environmental drifts, which often occur in constantly-changing production settings. Continual Learning (CL) is the inherent self-healing approach for adapting the DRL agent in response to the environment's conditions shifts. However, successive shifts of considerable magnitude may cause the production environment to drift from its original state. Recent studies have shown that these environmental drifts tend to drive CL into long, or even unsuccessful, healing cycles, which arise from inefficiencies such as catastrophic forgetting, warm-starting failure, and slow convergence. In this paper, we propose Dr. DRL, an effective self-healing approach for DRL systems that integrates a novel mechanism of intentional forgetting into vanilla CL (i.e., standard CL) to overcome its main issues. Dr. <PERSON><PERSON> deliberately erases the DRL system's minor behaviors to systematically prioritize the adaptation of the key problem-solving skills. Using well-established DRL algorithms, Dr. DRL is compared with vanilla CL on various drifted environments. Dr. DR<PERSON> is able to reduce, on average, the healing time and fine-tuning episodes by, respectively, 18.74% and 17.72%. Dr. DRL successfully helps agents to adapt to 19.63% of drifted environments left unsolved by vanilla CL while maintaining and even enhancing by up to 45% the obtained rewards for drifted environments that are resolved by both approaches.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00121"}, {"primary_key": "1220179", "vector": [], "sparse_vector": [], "title": "A Closer Look at Different Difficulty Levels Code Generation Abilities of ChatGPT.", "authors": ["<PERSON><PERSON>g Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code generation aims to generate source code implementing human requirements illustrated with natural language specifications. With the rapid development of intelligent software engineering, automated code generation has become a hot research topic in both artificial intelligence and software engineering, and researchers have made significant achievements on code generation. More recently, large language models (LLMs) have demonstrated outstanding performance on code generation tasks, such as ChatGPT released by OpenAI presents the fantastic potential on automated code generation. However, the existing studies are limited to exploring LLMs' ability for generating code snippets to solve simple programming problems, the task of competition-level code generation has never been investigated. The specifications of the programming competition are always complicated and require the specific input/output format as well as the high-level algorithmic reasoning ability. In this study, we conduct the first large empirical study to investigate the zero-shot learning ability of ChatGPT for solving competition programming problems. Specifically, we warm up the design of prompts by using the Human-Eval dataset. Then, we apply the well-designed prompt to the competition-level code generation dataset, namely APPS, to further explore the effectiveness of using ChatGPT for solving competition problems. We collect ChatGPT's outputs on 5,000 code competition problems, the evaluation results show that it can successfully pass 25.4% test cases. By further feeding extra information (e.g, test failed information) to ChatGPT, we observe that ChatGPT has the potential to fix partial pass into a fully pass program. Moreover, we investigate the solutions generated by LLMs and the existing solutions, we find that it prefers to directly copy the code instead of re-write when facing more difficult problems. Finally, we evaluate the code quality generated by ChatGPT in terms of \"code cleanness\", we observe that the generated codes are with small functions and file sizes, which are in line with the standard of clean code.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00096"}, {"primary_key": "1220180", "vector": [], "sparse_vector": [], "title": "Demystifying Template-Based Invariant Generation for Bit-Vector Programs.", "authors": ["<PERSON><PERSON><PERSON>", "Jingyu Ke", "<PERSON><PERSON><PERSON>", "Hongfei Fu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The template-based approach to invariant generation is a parametric and relatively complete methodology for inferring loop invariants. The relative completeness ensures the generated invariants' accuracy up to the template's form and the inductive condition. However, there has been limited in advancing the approach to bit-precise reasoning, which involves modeling integers using bit-vector arithmetic. This is unfortunate because bit-precise reasoning is crucial for faithfully and accurately modeling machine integer semantics and, thus, for ensuring sound and precise program verification. In this experience paper, we present an experimental study of bit-precise, template-based invariant generation on three fronts: the precision of different invariant templates, the performance of different constraint solvers for solving the constraints, and the effectiveness of the template-based approach compared to existing bit-precise verification techniques. Through an extensive experimental evaluation over a wide range of benchmarks, we find that (1) the choices of invariant templates and constraint solvers have varying degrees of impact on the precision and efficiency of invariant generation; (2) the template-based approach can handle benchmarks that other approaches for bit-vectors cannot handle. The results also reveal several guidelines for advancing future research on template-based invariant generation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00069"}, {"primary_key": "1220181", "vector": [], "sparse_vector": [], "title": "PreciseBugCollector: Extensible, Executable and Precise Bug-Fix Collection: Solution for Challenge 8: Automating Precise Data Collection for Code Snippets with Bugs, Fixes, Locations, and Types.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bug datasets are vital for enabling deep learning techniques to address software maintenance tasks related to bugs. However, existing bug datasets suffer from precise and scale limitations: they are either small-scale but precise with manual validation or large-scale but imprecise with simple commit message processing. In this paper, we introduce Precise-BugCollector, a precise, multi -language bug collection approach that overcomes these two limitations. PreciseBugCollector is based on two novel components: a) A bug tracker to map the codebase repositories with external bug repositories to trace bug type information, and b) A bug injector to generate project-specific bugs by injecting noise into the correct codebases and then executing them against their test suites to obtain test failure messages. We implement PreciseBugCollector against three sources: 1) A bug tracker that links to the national vulnerability data set (NVD) to collect general-wise vulnerabilities, 2) A bug tracker that links to OSS-Fuzz to collect general-wise bugs, and 3) A bug injector based on 16 injection rules to generate project-wise bugs. To date, PreciseBugCollector comprises 1057818 bugs extracted from 2968 open-source projects. Of these, 12602 bugs are sourced from bug repositories (NVD and OSS-Fuzz), while the remaining 1045216 project-specific bugs are generated by the bug injector. Considering the challenge objectives, we argue that a bug injection approach is highly valuable for the industrial setting, since project-specific bugs align with domain knowledge, share the same codebase, and adhere to the coding style employed in industrial projects.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00163"}, {"primary_key": "1220182", "vector": [], "sparse_vector": [], "title": "QuraTest: Integrating Quantum Specific Features in Quantum Program Testing.", "authors": ["Jiaming Ye", "Shangzhou Xia", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The recent fast development of quantum computers breaks several computation limitations that are difficult for conventional computers. Up to the present, although many approaches and tools have been proposed to test quantum programs, the fundamental features of quantum programs, i.e., magnitude, phase, and entanglement, have been largely overlooked, leading to limited fault detection capability and reduced testing effectiveness. To address this problem, we propose an automated testing framework named QURATEST, equipped with three test case generators (including two newly proposed techniques, UCNOT and IQFT in this paper, as well as one based on Random techniques) to test quantum programs. Overall, the proposed generators enable the generation of diverse test inputs by considering the quantum features of quantum programs. In the experiments, we perform an in-depth evaluation of QURATEST from three aspects: generated test case diversity, output coverage of the program under test, and fault detection capability. The results demonstrate the potential of our newly proposed techniques in that IQFT can generate the most diverse test cases regarding magnitude, phase, and entanglement, with 66% cell coverage. Comparatively, the Random approach only has 10% cell coverage. Regarding the evaluations of the output coverage, IQFT can achieve the highest output coverage in 70.2% (33 out of 47) of all quantum programs. In terms of fault detection, UCNOT outperforms the other two techniques. Specifically, the test cases generated by UCNOT have the best mutation score in 88.4% (23 out of 26) quantum programs.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00196"}, {"primary_key": "1220183", "vector": [], "sparse_vector": [], "title": "Evolve the Model Universe of a System Universe.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Uncertain, unpredictable, real-time, and lifelong evolution causes operational failures in intelligent software systems, leading to significant damages, safety and security hazards, and tragedies. To fully unleash such systems' potential and facilitate their wider adoption, ensuring the trustworthiness of their decision-making under uncertainty is the prime challenge. To overcome this challenge, an intelligent software system and its operating environment should be continuously monitored, tested, and refined during its lifetime operation. Existing technologies, such as digital twins, can enable continuous synchronisation with such systems to reflect their most up-to-date states. Such representations are often in the form of prior-knowledge-based and machine-learning models, together called 'model universe'. In this paper, we present our vision of combining techniques from software engineering, evolutionary computation, and machine learning to support the model universe evolution.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00022"}, {"primary_key": "1220184", "vector": [], "sparse_vector": [], "title": "ArduinoProg: Towards Automating Arduino Programming.", "authors": ["Imam <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Writing code for Arduino poses unique challenges. A developer 1) needs hardware-specific knowledge about the interface configuration between the Arduino controller and the I/Ohardware, 2) identifies a suitable driver library for the I/O hardware, and 3) follows certain usage patterns of the driver library in order to use them properly. In this work, based on a study of real-world user queries posted in the Arduino forum, we propose ArduinoProg to address such challenges. ArduinoProg consists of three components, i.e., Library Retriever, Configuration Classifier, and Pattern Generator. Given a query, Library Retriever retrieves library names relevant to the I/O hardware identified from the query using vector-based similarity matching. Configuration Classifier predicts the interface configuration between the I/O hardware and the Arduino controller based on the method definitions of each library. Pattern Generator generates the usage pattern of a library using a sequence-to-sequence deep learning model. We have evaluated ArduinoProg using real-world queries, and our results show that the components of ArduinoProg can generate accurate and useful suggestions to guide developers in writing Arduino code. Demo video: bit.ly/3Y3aeBe Tool: https://huggingface.co/spaces/imamnurby/ArduinoProg Code and data: https://github.com/imamnurby/ArduProg", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00055"}, {"primary_key": "1220185", "vector": [], "sparse_vector": [], "title": "iASTMapper: An Iterative Similarity-Based Abstract Syntax Tree Mapping Algorithm.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Abstract syntax tree (AST) mapping algorithms are widely used to locate the code changes in a file revision by mapping the AST nodes of the source code before and after the code changes. A recent differential testing of three state-of- the-art AST mapping algorithms, i.e., GumTree, MTDiff, and IJM, reveals that the algorithms generate inaccurate mappings for a considerable number of file revisions. We find that the inaccurate mappings could be caused by the mutual influence: the mappings of lower-level AST nodes (e.g., tokens) have impacts on the mappings of higher-level AST nodes (e.g., statements) and vice versa. This mutual influence issue is rarely considered by existing algorithms. In this paper, we propose an algorithm, called iASTMapper, that iteratively map two ASTs based on the similarities between AST nodes. Given a file revision, we extract three types of AST nodes in different levels of program structures (i.e., tokens, statements, and inner-statements) from the ASTs of the two source code files. We first build mappings of the unchanged statements and inner-statements. Then, we use an iterative method to map the rest of the nodes without mapping. For each of the three types of nodes, we iteratively map the nodes based on their similarities measured using heuristic rules. We further use an iterative mechanism to connect the three iterative mapping processes by considering the mutual influence between the mappings of different types of nodes. Finally, a series of code edit actions are generated from the node mappings to help users understand and locate the code changes during revisions. We conduct experiments to compare iASTMapper with three baselines, i.e., GumTree, MTDiff, and IJM, by automatically evaluating 210,997 file revisions from ten Java projects. Furthermore, we manually evaluate the correctness of the code edit actions generated for 200 file revisions with 12 evaluators. The results demonstrate that iASTMapper outperforms the baselines. iASTMapper can generate shorter code edit actions by at least 1.29% than the baselines, with a high accuracy of 96.23%.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00178"}, {"primary_key": "1220186", "vector": [], "sparse_vector": [], "title": "Scene-Driven Exploration and GUI Modeling for Android Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yucheng Su", "<PERSON><PERSON>"], "summary": "Due to the competitive environment, mobile apps are usually produced under pressure with lots of complicated functionality and UI pages. Therefore, it is challenging for various roles to design, understand, test, and maintain these apps. The extracted transition graphs for apps such as ATG, WTG, and STG have a low transition coverage and coarse-grained granularity, which limits the existing methods of graphical user interface (GUI) modeling by UI exploration. To solve these problems, in this paper, we propose SceneDroid, a scene-driven exploration approach to extracting the GUI scenes dynamically by integrating a series of novel techniques including smart exploration, state fuzzing, and indirect launching strategies. We present the GUI scenes as a scene transition graph (SceneTG) to model the GUI of apps with high transition coverage and fine-grained granularity. Compared with the existing GUI modeling tools, SceneDroid has improved by 168.74% in the coverage of transition pairs and 162.42% in scene extraction. Apart from the effectiveness evaluation of SceneDroid, we also illustrate the future potential of SceneDroid as a fundamental capability to support app development, reverse engineering, and GUI rearession testing.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00179"}, {"primary_key": "1220187", "vector": [], "sparse_vector": [], "title": "Gamma: Revisiting Template-Based Automated Program Repair Via Mask Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "Chunrong Fang", "<PERSON><PERSON>", "<PERSON>", "Weisong Sun", "<PERSON><PERSON><PERSON>"], "summary": "Automated program repair (APR) aims to fix software bugs without manual debugging efforts and plays a crucial role in software development and maintenance. Template-based APR has been widely investigated and shown promising results. However, it is challenging for template-based APR to select the appropriate donor code, which is an important repair ingredient for generating candidate patches. Inappropriate donor code may cause plausible but incorrect patch generation even with correct fix patterns, limiting the repair performance. In this paper, we aim to revisit template-based APR, and propose <PERSON>, to directly leverage large pre-trained language models for donor code generation. Our main insight is that instead of retrieving donor code in the local buggy file, we can directly predict the correct code tokens based on the context code snippets and repair patterns by a cloze task. Specifically, (1) <PERSON> revises a variety of fix templates from state-of-the-art template-based APR techniques (i.e., TBar) and transforms them into mask patterns. (2) <PERSON> adopts a pre-trained language model to predict the correct code for masked code as a fill-in-the-blank task. Although our idea is general and can be built on various existing pre-trained language models, we have implemented Gamma as a practical APR tool based on the recent UniXcoder model. The experimental results demonstrate that <PERSON> correctly repairs 82 bugs on Defects4J-v1.2, which achieves 20.59% (14 bugs) and 26.15% (17 bugs) improvement over the previous state-of-the-art template-based approach TBar and learning-based one Recoder. Furthermore, <PERSON> repairs 45 bugs and 22 bugs from the additional Defects4J-v2.0 and QuixBugs, indicating the generalizability of Gamma in addressing the dataset overfitting issue. We also prove that adopting other pre-trained language models can provide substantial advancement, e.g., CodeBERT-based and ChatGPT-based Gamma is able to fix 80 and 67 bugs on Defects4J-v1.2, indicating the scalability of Gamma. Overall, our study highlights the promising future of adopting pre-trained models to generate correct patches on top of fix patterns in practice.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00063"}, {"primary_key": "1220188", "vector": [], "sparse_vector": [], "title": "InfeRE: Step-by-Step Regex Generation via Chain of Inference.", "authors": ["<PERSON><PERSON>", "Xiaodong Gu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatically generating regular expressions (abbrev. regexes) from natural language description (NL2RE) has been an emerging research area. Prior studies treat regex as a linear sequence of tokens and generate the final expressions autoregressively in a single pass. They did not take into account the step-by-step internal text-matching processes behind the final results. This significantly hinders the efficacy and interpretability of regex generation by neural language models. In this paper, we propose a new paradigm called InfeRE, which decomposes the generation of regexes into chains of step-bystep inference. To enhance the robustness, we introduce a self-consistency decoding mechanism that ensembles multiple outputs sampled from different models. We evaluate InfeRE on two publicly available datasets, NL-RX-Turk and KB13, and compare the results with state-of-the-art approaches and the popular tree-based generation approach TRANX. Experimental results show that InfeRE substantially outperforms previous baselines, yielding 16.3% and 14.7% improvement in DFA@5 accuracy on two datasets, respectively.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00111"}, {"primary_key": "1220189", "vector": [], "sparse_vector": [], "title": "Mitigating Persistence of Open-Source Vulnerabilities in Maven Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Vulnerabilities from third-party libraries (TPLs) have been unveiled to threaten the Maven ecosystem in the long term. Despite patches being released promptly after vulnerabilities are disclosed, the libraries and applications in the community still use the vulnerable versions, which makes the vulnerabilities persistent in the Maven ecosystem (e.g., the notorious Log4Shell still greatly influences the Maven ecosystem nowadays from 2021). Both academic and industrial researchers have proposed user-oriented standards and solutions to address vulnerabilities, while such solutions fail to tackle the ecosystem-wide persistent vulnerabilities because it requires a collective effort from the community to timely adopt patches without introducing breaking issues. To seek an ecosystem-wide solution, we first carried out an empirical study to examine the prevalence of persistent vulnerabilities in the Maven ecosystem. Then, we identified affected libraries for alerts by implementing an algorithm monitoring downstream dependents of vulnerabilities based on an up-to-date dependency graph. Based on them, we further quantitatively revealed that patches blocked by upstream libraries caused the persistence of vulnerabilities. After reviewing the drawbacks of existing countermeasures, to address them, we proposed a solution for range restoration (<PERSON>) to automatically restore the compatible and secure version ranges of dependencies for downstream dependents. The automatic restoration requires no manual effort from the community, and the code-centric compatibility assurance ensures smooth upgrades to patched versions. Moreover, <PERSON> along with the ecosystem monitoring can timely alert developers of blocking libraries and suggest flexible version ranges to rapidly unblock patch versions. By evaluation, <PERSON> could restore 75.64% of ranges which automatically remediated 90.32% of vulnerable downstream projects.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00058"}, {"primary_key": "1220190", "vector": [], "sparse_vector": [], "title": "ICTDroid: Parameter-Aware Combinatorial Testing for Components of Android Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Components are the fundamental building blocks of Android applications. Different functional modules represented by components often rely on inter-component communication mechanisms to achieve cross-module data transfer and method invocation. It is necessary to conduct robustness testing on components to prevent component launching crashes and privacy leaks caused by unexpected input parameters. However, as the complexity of the input parameter structure and the diversity of possible inputs, developers may overlook specific inputs that result in exceptions. At the same time, the vast input space also brings challenges to efficient component testing. In this paper, we designed an automated test generation and execution tool for Android application components named ICTDroid, which combines static parameter extraction and adaptive-strength combinatorial testing generation to detect bugs with a compact test suite. Experiments have shown that the tool triggers 205 unique exceptions in 30 open-source applications with 1,919 test cases in 83 minutes, where the developers have confirmed six defects in three issues we reported.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00071"}, {"primary_key": "1220191", "vector": [], "sparse_vector": [], "title": "How Android Apps Break the Data Minimization Principle: An Empirical Study.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Data Minimization Principle is crucial for protecting individual privacy. However, existing Android runtime permissions do not guarantee this principle. Moreover, the lack of an automatic enforcement mechanism leads to uncertainty as to whether apps strictly comply with this principle. To bridge this gap, we conduct the first systematic empirical study on violations of the Data Minimization Principle and design a new enforcement tool called GUIMind to detect them. GUIMind first utilizes a reinforcement learning model to explore app activities and monitor access to sensitive APIs that require sensitive permissions, and then it leverages an existing tool to detect such violations. We evaluate the performance of GUIMind using 120 real-world Android apps. The results indicate that GUIMind can achieve a detection accuracy of 96.1%, effectively accelerating the empirical study. Our empirical research is mainly focused on the prevalence of violations, the responses of administrators to violations, and the potential factors and characteristics that lead to violations, such as typical violations, app categories, and personal data types. Our study reveals that 83.5% of apps contain at least one privacy violation, with health apps being the most severe. In addition, telephony information is the most commonly leaked personal data type, accounting for 71.1%. Finally, we randomly selected 60 non-compliant apps for reporting to the administrator, whose responses confirm the effectiveness of our approach.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00141"}, {"primary_key": "1220192", "vector": [], "sparse_vector": [], "title": "Learning to Locate and Describe Vulnerabilities.", "authors": ["<PERSON><PERSON>", "Shang<PERSON> Liu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatically discovering software vulnerabilities is a long-standing pursuit for software developers and security analysts. Since detection tools usually provide limited information for vulnerability inspection, recent work turns the attention to identify fine-grained vulnerabilities, i.e., vulnerable statements. However, existing work for vulnerability localization struggles to capture long-range and integral dependency information due to the bottleneck of Graph Neural Networks (GNNs). Moreover, little research has been done to help developers understand detected vulnerabilities, leaving vulnerability diagnosis a challenging task. In this paper, we propose VulTeller, a deep learning-based approach that can automatically locate vulnerable statements in a function and more importantly, can describe the vulnerability. Our approach focuses on extracting precise control and data dependencies in the code, achieved through modeling control flow paths and employing taint analysis. We design a novel neural model that encodes the control flows and taint flows which reside in the control flow paths, and decodes them via node classification and an attentional decoder for the two tasks respectively. We conduct extensive experiments with real-world vulnerabilities to evaluate the proposed approach. The evaluation results, including quantitative measurement and human evaluation, demonstrate that our approach is highly effective and outperforms state-of-the-art approaches. Our work for the first time formulates the problem of vulnerability description generation, and makes one step further towards automated vulnerability diagnosis.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00045"}, {"primary_key": "1220193", "vector": [], "sparse_vector": [], "title": "DroneReqValidator: Facilitating High Fidelity Simulation Testing for Uncrewed Aerial Systems Developers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rigorous testing of small Uncrewed Aerial Systems (sUAS) is crucial to ensure their safe and reliable deployment in the real world. sUAS developers aim to validate the reliability and safety of their applications through simulation testing. However, the dynamic nature of the real-world environment, including factors such as challenging weather conditions and wireless interference, causes unique software faults that may only be revealed through field testing. Considering the high cost and impracticality of conducting field testing in thousands of environmental contexts and conditions, there exists a pressing need to develop automated techniques that can generate high-fidelity, realistic environments enabling sUAS developers to deploy their applications and conduct thorough simulation testing in close- to- reality environmental conditions. To address this need, DroneReqValidator (DRV) offers a comprehensive small Unmanned Aerial Vehicle (sUAV) simulation ecosystem that automatically generates realistic environments based on developer-specified constraints, monitors sUAV activities against predefined safety parameters, and generates detailed acceptance test reports for effective debugging and analysis of sUAV applications. Providing these capabilities, DRV offers a valuable solution for enhancing the testing and development process of sUAS. The comprehensive demo of DRV is available at https://www.youtube.com/watch?v=Fd9ft55gbO8", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00011"}, {"primary_key": "1220194", "vector": [], "sparse_vector": [], "title": "OrdinalFix: Fixing Compilation Errors via Shortest-Path CFL Reachability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The development of correct and efficient software can be hindered by compilation errors, which must be fixed to ensure the code's syntactic correctness and program language constraints. Neural network-based approaches have been used to tackle this problem, but they lack guarantees of output correctness and can require an unlimited number of modifications. Fixing compilation errors within a given number of modifications is a challenging task. We demonstrate that finding the minimum number of modifications to fix a compilation error is NP-hard. To address compilation error fixing problem, we propose OrdinalFix, a complete algorithm based on shortest-path CFL (context-free language) reachability with attribute checking that is guaranteed to output a program with the minimum number of modifications required. Specifically, OrdinalFix searches possible fixes from the smallest to the largest number of modifications. By incorporating merged attribute checking to enhance efficiency, the time complexity of OrdinalFix is acceptable for application. We evaluate OrdinalFix on two datasets and demonstrate its ability to fix compilation errors within reasonable time limit. Comparing with existing approaches, OrdinalFix achieves a success rate of 83.5 %, surpassing all existing approaches (71.7%).", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00072"}, {"primary_key": "1220195", "vector": [], "sparse_vector": [], "title": "EALink: An Efficient and Accurate Pre-Trained Framework for Issue-Commit Link Recovery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Rongrong Ji"], "summary": "Issue-commit links, as a type of software traceability links, play a vital role in various software development and maintenance tasks. However, they are typically deficient, as developers often forget or fail to create tags when making commits. Existing studies have deployed deep learning techniques, including pretrained models, to improve automatic issue-commit link recovery. Despite their promising performance, we argue that previous approaches have four main problems, hindering them from recovering links in large software projects. To overcome these problems, we propose an efficient and accurate pre-trained framework called EALink for issue-commit link recovery. EALink requires much fewer model parameters than existing pre-trained methods, bringing efficient training and recovery. Moreover, we design various techniques to improve the recovery accuracy of EALink. We construct a large-scale dataset and conduct extensive experiments to demonstrate the power of EALink. Results show that EALink outperforms the state-of-the-art methods by a large margin (15.23%-408.65%) on various evaluation metrics. Meanwhile, its training and inference overhead is orders of magnitude lower than existing methods. We provide our implementation and data at https://github.com/KDEGroup/EALink.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00059"}, {"primary_key": "1220196", "vector": [], "sparse_vector": [], "title": "EndWatch: A Practical Method for Detecting Non-Termination in Real-World Software.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Detecting non-termination is crucial for ensuring program correctness and security, such as preventing denial-of-service attacks. While termination analysis has been studied for many years, existing methods have limited scalability and are only effective on small programs. To address this issue, we propose a practical termination checking technique, called EndWatch, for detecting non-termination caused by infinite loops through testing. Specifically, we introduce two methods to generate non-termination oracles based on checking state revisits, i.e., if the program returns to a previously visited state at the same program location, it does not terminate. The non-termination oracles can be incorporated into testing tools (e.g., AFL used in this paper) to detect non-termination in large programs. For linear loops, we perform symbolic execution on individual loops to infer State Revisit Conditions (SRCs) and instrument SRCs into target loops. For non-linear loops, we instrument target loops for checking concrete state revisits during execution. We evaluated EndWatch on standard benchmarks with small-sized programs and real-world projects with large-sized programs. The evaluation results show that EndWatch is more effective than the state-of-the-art tools on standard benchmarks (detecting 87% of non-terminating programs while the best baseline detects only 67%), and useful in detecting non-termination in real-world projects (detecting 90% of known non-termination CVEs and 4 unknown bugs).", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00061"}, {"primary_key": "1220197", "vector": [], "sparse_vector": [], "title": "Eiffel: Inferring Input Ranges of Significant Floating-point Errors via Polynomial Extrapolation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing search heuristics used to find input values that result in significant floating-point (FP) errors or small ranges that cover them are accompanied by severe constraints, complicating their implementation and restricting their general applicability. This paper introduces an error analysis tool called Eiffel to infer error-inducing input ranges instead of searching them. Given an FP expression with its domain $\\mathcal{D}$ , <PERSON><PERSON><PERSON> first constructs an error data set by sampling values across a smaller domain $\\mathcal{R}$ and assembles these data into clusters. If more than two clusters are formed, <PERSON><PERSON><PERSON> derives polynomial curves that best fit the bound coordinates of the error-inducing ranges in $\\mathcal{R}$ , extrapolating them to infer all target ranges of $\\mathcal{D}$ and reporting the maximal error. Otherwise, Eiffel simply returns the largest error across $\\mathcal{R}$ . Experimental results show that <PERSON><PERSON><PERSON> exhibits a broader applicability than Atomu and $\\mathbf{S}^{3}$ FP by successfully detecting the errors of all 70 considered benchmarks while the two baselines only report errors for part of them. By taking as input the inferred ranges of <PERSON><PERSON><PERSON>, <PERSON><PERSON> obtains an average accuracy improvement of 3.35 bits and up to 53.3 bits.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00139"}, {"primary_key": "1220198", "vector": [], "sparse_vector": [], "title": "Symbolic Verification of Fuzzy Logic Models.", "authors": ["<PERSON><PERSON>", "Zhongyang Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fuzzy logic is widely applied in various applications. However, verifying the correctness of fuzzy logic models can be difficult. This extended abstract presents our ongoing work on verifying fuzzy logic models. We treat a fuzzy logic model as a program and propose a verification method based on symbolic execution for fuzzy logic models. We have developed and implemented the environment models for the common functions and the inference rules in fuzzy logic models. Our preliminary evaluation shows the potential of our verification method.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00087"}, {"primary_key": "1220199", "vector": [], "sparse_vector": [], "title": "CertPri: Certifiable Prioritization for Deep Neural Networks via Movement Cost in Feature Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai<PERSON> Jin"], "summary": "Deep neural networks (DNNs) have demonstrated their outperformance in various software systems, but also exhibit misbehavior and even result in irreversible disasters. Therefore, it is crucial to identify the misbehavior of DNN-based software and improve DNNs' quality. Test input prioritization is one of the most appealing ways to guarantee DNNs' quality, which prioritizes test inputs so that more bug-revealing inputs can be identified earlier with limited time and manual labeling efforts. However, the existing prioritization methods are still limited from three aspects: certifiability, effectiveness, and generalizability. To overcome the challenges, we propose CertPri, a test input prioritization technique designed based on a movement cost perspective of test inputs in DNNs' feature space. CertPri differs from previous works in three key aspects: (1) certifiable - it provides a formal robustness guarantee for the movement cost; (2) effective - it leverages formally guaranteed movement costs to identify malicious bug-revealing inputs; and (3) generic - it can be applied to various tasks, data, models, and scenarios. Extensive evaluations across 2 tasks (i.e., classification and regression), 6 data forms, 4 model structures, and 2 scenarios (i.e., white-box and black-box) demonstrate CertPri's superior performance. For instance, it significantly improves 53.97 % prioritization effectiveness on average compared with baselines. Its robustness and generalizability are 1.41~2.00 times and 1.33~3.39 times that of baselines on average, respectively. The code of CertPri is open-sourced at https://github.com/haibinzheng/CertPri.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00126"}, {"primary_key": "1220200", "vector": [], "sparse_vector": [], "title": "An Automated and Flexible Multilingual Bug-Fix Dataset Construction System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>iwen Ge", "<PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON>"], "summary": "Developing effective data-driven automated bug-fixing approaches is heavily relying on large bug-fix datasets. However, the granularity of current repository-mined bug-fixing datasets is usually at the function level, without meta-information such as the fault type. In order to alleviate the open challenge of precisely mining code snippets with bugs, their fix, location, and types from open source repositories, in this paper, we propose a flexible, extensible, and automated multilingual bug-fix dataset construction system, that is, the Multilingual Bug-Fix Constructor (MBFC). Furthermore, we release a large-scale and fine-grained Multi-lingual Bug-Fix (M-BF) dataset automatically built using the proposed system, which includes a total of 921,825 Bug-Fix pairs that are from 442,164 different open-source software projects starting from January 2020 to September 2020 in the initial version. It is expected that our system and dataset can benefit the development of innovative and practical program repair methods, thereby improving the efficiency of program debugging and code review processes.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00176"}, {"primary_key": "1220201", "vector": [], "sparse_vector": [], "title": "Expediting Neural Network Verification via Network Reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A wide range of verification methods have been proposed to verify the safety properties of deep neural networks ensuring that the networks function correctly in critical applications. However, many well-known verification tools still struggle with complicated network architectures and large network sizes. In this work, we propose a network reduction technique as a pre-processing method prior to verification. The proposed method reduces neural networks via eliminating stable ReLU neurons, and transforming them into a sequential neural network consisting of ReLU and Affine layers which can be handled by most verification tools. We instantiate the reduction technique on the state-of-the-art complete and incomplete verification tools, including $\\alpha,\\beta$ -crown, VeriNet and PRIMA. Our experiments on a large set of benchmarks indicate that the proposed technique can significantly reduce neural networks and speed up existing verification tools. Furthermore, the experiment results also show that network reduction can improve the availability of existing verification tools on many networks by reducing them into sequential neural networks.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00081"}, {"primary_key": "1220202", "vector": [], "sparse_vector": [], "title": "WADIFF: A Differential Testing Framework for WebAssembly Runtimes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "WebAssembly (Wasm) runtime provides a virtual machine that can execute the WebAssembly modules and is widely used in different areas (e.g., browsers, edge computing, blockchain). Thus, the precision and reliability of the WebAssembly runtime are important and deserve our attention. To ensure the correctness and detect potential bugs in WebAssembly runtimes, we propose WADIFF, a differential testing framework, which consists of a sufficient test case generator and a deterministic differential testing engine. To evaluate the effectiveness of WADIFF, we apply it to seven popular WebAssembly runtimes and found 417 inconsistent instructions due to bugs and different implementations in the runtimes. Furthermore, we identify 21 bugs from 7 WebAssembly runtimes, and 8 of them are confirmed by their developers.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00188"}, {"primary_key": "1220203", "vector": [], "sparse_vector": [], "title": "The Devil is in the Tails: How Long-Tailed Code Distributions Impact Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Han", "<PERSON>"], "summary": "Learning-based techniques, especially advanced Large Language Models (LLMs) for code, have gained considerable popularity in various software engineering (SE) tasks. However, most existing works focus on designing better learning-based models and pay less attention to the properties of datasets. Learning-based models, including popular LLMs for code, heavily rely on data, and the data's properties (e.g., data distribution) could significantly affect their behavior. We conducted an exploratory study on the distribution of SE data and found that such data usually follows a skewed distribution (i.e., long-tailed distribution) where a small number of classes have an extensive collection of samples, while a large number of classes have very few samples. We investigate three distinct SE tasks and analyze the impacts of long-tailed distribution on the performance of LLMs for code. Our experimental results reveal that the long-tailed distribution has a substantial impact on the effectiveness of LLMs for code. Specifically, LLMs for code perform between 30.0% and 254.0% worse on data samples associated with infrequent labels compared to data samples of frequent labels. Our study provides a better understanding of the effects of long-tailed distributions on popular LLMs for code and insights for the future development of SE automation.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00157"}, {"primary_key": "1220204", "vector": [], "sparse_vector": [], "title": "A Majority Invariant Approach to Patch Robustness Certification for Deep Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Patch robustness certification ensures no patch within a given bound on a sample can manipulate a deep learning model to predict a different label. However, existing techniques cannot certify samples that cannot meet their strict bars at the classifier level or the patch region level. This paper proposes MajorCert. MajorCert firstly finds all possible label sets manipulatable by the same patch region on the same sample across the underlying classifiers, then enumerates their combinations element-wise, and finally checks whether the majority invariant of all these combinations is intact to certify samples.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00137"}, {"primary_key": "1220205", "vector": [], "sparse_vector": [], "title": "Compiler Auto-Tuning via Critical Flag Selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Widely used compilers like GCC usually have hundreds of optimizations controlled by optimization flags, which can be enabled or disabled during compilation to improve the runtime performance of a compiled program. Due to the large number of optimization flags and their combination, it is difficult for compiler users to tune compiler optimization flags manually. In the literature, many auto-tuning techniques have been proposed, which find a desired setting on all optimization flags (i.e., an optimization sequence) by designing different search strategies in the entire optimization space. Due to the huge search space, these techniques suffer from the widely-recognized efficiency problem. To reduce the search space, in this paper, we propose a critical-flag selection based approach CFSCA which first finds flags potentially relevant to the target program by analyzing program structure and compiler documentation, and then identifies critical flags through statistical analysis on the program's predicted runtime performance with various optimization sequences. With the reduced search space, CFSCA selects a desired optimization sequence. To evaluate the performance of the proposed approach CFSCA, we conduct an extensive experimental study on the latest version of the compiler GCC with a widely used benchmark cBench. The experimental results show that CFSCA significantly outperforms the four compared techniques, including the state-of-art technique BOCA.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00209"}, {"primary_key": "1220206", "vector": [], "sparse_vector": [], "title": "Improving Design Reviews at Google.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Design review is an important initial phase of the software development life-cycle where stakeholders gain and discuss early insights into the design's viability, discover potentially costly mistakes, and identify inconsistencies and inadequacies. For improved development velocity, it is important that design owners get their designs approved as Quickly as possible. In this paper, we discuss how engineering design reviews are typically conducted at Google, and propose a novel, structured, automated solution to improve design review velocity. Based on data collected on 141,652 approved documents authored by 41,030 users over four years, we show that our proposed solution decreases median time-to-approval by 25%, and provides further gains when used consistently. We also provide qualitative data to demonstrate our solution's success, discuss factors that impact design review latency, propose strategies to tackle them, and share lessons learned from the usage of our solution.", "published": "2023-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE56229.2023.00066"}]