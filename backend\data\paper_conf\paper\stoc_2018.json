[{"primary_key": "3512922", "vector": [], "sparse_vector": [], "title": "Holiest minimum-cost paths and flows in surface graphs.", "authors": ["<PERSON>", "<PERSON>", "Luv<PERSON>dondov <PERSON>khamsuren"], "summary": "Let G be an edge-weighted directed graph with n vertices embedded on an orientable surface of genus g. We describe a simple deterministic lexicographic perturbation scheme that guarantees uniqueness of minimum-cost flows and shortest paths in G. The perturbations take O(gn) time to compute. We use our perturbation scheme in a black box manner to derive a deterministic O(n loglogn) time algorithm for minimum cut in directed edge-weighted planar graphs and a deterministic O(g2 n logn) time proprocessing scheme for the multiple-source shortest paths problem of computing a shortest path oracle for all vertices lying on a common face of a surface embedded graph. The latter result yields faster deterministic near-linear time algorithms for a variety of problems in constant genus surface embedded graphs.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188904"}, {"primary_key": "3512923", "vector": [], "sparse_vector": [], "title": "Improved approximation for tree augmentation: saving by rewiring.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Tree Augmentation Problem (TAP) is a fundamental network design problem in which we are given a tree and a set of additional edges, also called links. The task is to find a set of links, of minimum size, whose addition to the tree leads to a 2-edge-connected graph. A long line of results on TAP culminated in the previously best known approximation guarantee of 1.5 achieved by a combinatorial approach due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [ACM Transactions on Algorithms 2016], and also by an SDP-based approach by <PERSON><PERSON><PERSON> and <PERSON> [Algorithmica 2017]. Moreover, an elegant LP-based (1.5+є)-approximation has also been found very recently by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [SODA 2018]. In this paper, we show that an approximation factor below 1.5 can be achieved, by presenting a 1.458-approximation that is based on several new techniques.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188898"}, {"primary_key": "3512924", "vector": [], "sparse_vector": [], "title": "Counting hypergraph colourings in the local lemma regime.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a fully polynomial-time approximation scheme (FPTAS) to count the number of q-colorings for k-uniform hypergraphs with maximum degree Δ if k≥ 28 and q > 315Δ14/k−14. We also obtain a polynomial-time almost uniform sampler if q>798Δ16/k−16/3. These are the first approximate counting and sampling algorithms in the regime q≪Δ (for large Δ and k) without any additional assumptions. Our method is based on the recent work of <PERSON><PERSON><PERSON> (STOC, 2017). One important contribution of ours is to remove the dependency of k and Δ in <PERSON><PERSON><PERSON>'s approach.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188934"}, {"primary_key": "3512925", "vector": [], "sparse_vector": [], "title": "A (5/3 + ε)-approximation for unsplittable flow on a path: placing small tasks into boxes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the unsplittable flow on a path problem (UFP) we are given a path with edge capacities and a collection of tasks. Each task is characterized by a subpath, a profit, and a demand. Our goal is to compute a maximum profit subset of tasks such that, for each edge e, the total demand of selected tasks that use e does not exceed the capacity of e. The current best polynomial-time approximation factor for this problem is 2+є for any constant є>0 [<PERSON><PERSON><PERSON><PERSON> et al.-SODA 2014]. This is the best known factor even in the case of uniform edge capacities [<PERSON><PERSON><PERSON><PERSON> et al.-IPCO 2002, TALG 2011]. These results, likewise most prior work, are based on a partition of tasks into large and small depending on their ratio of demand to capacity over their respective edges: these algorithms invoke (1+є)-approximations for large and small tasks separately.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188894"}, {"primary_key": "3512926", "vector": [], "sparse_vector": [], "title": "How to match when all vertices arrive online.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a fully online model of maximum cardinality matching in which all vertices arrive online. On the arrival of a vertex, its incident edges to previously-arrived vertices are revealed. Each vertex has a deadline that is after all its neighbors' arrivals. If a vertex remains unmatched until its deadline, the algorithm must then irrevocably either match it to an unmatched neighbor, or leave it unmatched. The model generalizes the existing one-sided online model and is motivated by applications including ride-sharing platforms, real-estate agency, etc. We show that the Ranking algorithm by <PERSON><PERSON> et al. (STOC 1990) is 0.5211-competitive in our fully online model for general graphs. Our analysis brings a novel charging mechanic into the randomized primal dual technique by <PERSON><PERSON><PERSON> et al. (SODA 2013), allowing a vertex other than the two endpoints of a matched edge to share the gain. To our knowledge, this is the first analysis of Ranking that beats 0.5 on general graphs in an online matching problem, a first step towards solving the open problem by <PERSON><PERSON> et al. (STOC 1990) about the optimality of Ranking on general graphs. If the graph is bipartite, we show that the competitive ratio of Ranking is between 0.5541 and 0.5671. Finally, we prove that the fully online model is strictly harder than the previous model as no online algorithm can be 0.6317 < 1−1/e-competitive in our model even for bipartite graphs.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188858"}, {"primary_key": "3512927", "vector": [], "sparse_vector": [], "title": "Smooth heaps and a dual view of self-adjusting data structures.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We present a new connection between self-adjusting binary search trees (BSTs) and heaps, two fundamental, extensively studied, and practically relevant families of data structures (<PERSON>,<PERSON>, 1978; <PERSON><PERSON><PERSON>, <PERSON>, 1983; <PERSON><PERSON>, Sedgewick, <PERSON>, Tarjan, 1986; <PERSON><PERSON><PERSON>, 1989; <PERSON><PERSON>, 1999; <PERSON><PERSON><PERSON>, <PERSON>, 2014). Roughly speaking, we map an arbitrary heap algorithm within a broad and natural model, to a corresponding BST algorithm with the same cost on a dual sequence of operations (i.e. the same sequence with the roles of time and key-space switched). This is the first general transformation between the two families of data structures.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188864"}, {"primary_key": "3512928", "vector": [], "sparse_vector": [], "title": "Shadow tomography of quantum states.", "authors": ["<PERSON>"], "summary": "We introduce the problem of *shadow tomography*: given an unknown D-dimensional quantum mixed state ρ, as well as known two-outcome measurements E1,…,EM, estimate the probability that <PERSON><PERSON> accepts ρ, to within additive error ε, for each of the M measurements. How many copies of ρ are needed to achieve this, with high probability? Surprisingly, we give a procedure that solves the problem by measuring only O( ε−5·log4 M·logD) copies. This means, for example, that we can learn the behavior of an arbitrary n-qubit state, on *all* accepting/rejecting circuits of some fixed polynomial size, by measuring only nO( 1) copies of the state. This resolves an open problem of the author, which arose from his work on private-key quantum money schemes, but which also has applications to quantum copy-protected software, quantum advice, and quantum one-way communication. Recently, building on this work, <PERSON> et al. have given a different approach to shadow tomography using semidefinite programming, which achieves a savings in computation time.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188802"}, {"primary_key": "3512929", "vector": [], "sparse_vector": [], "title": "More consequences of falsifying SETH and the orthogonal vectors conjecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Strong Exponential Time Hypothesis and the OV-conjecture are two popular hardness assumptions used to prove a plethora of lower bounds, especially in the realm of polynomial-time algorithms. The OV-conjecture in moderate dimension states there is no ε>0 for which an O(N2−ε) poly(D) time algorithm can decide whether there is a pair of orthogonal vectors in a given set of size N that contains D-dimensional binary vectors.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188938"}, {"primary_key": "3512930", "vector": [], "sparse_vector": [], "title": "Metric embedding via shortest path decompositions.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of embedding weighted graphs of pathwidth k into ℓp spaces. Our main result is an O(kmin{1p,12})-distortion embedding. For p=1, this is a super-exponential improvement over the best previous bound of <PERSON> and <PERSON>. Our distortion bound is asymptotically tight for any fixed p >1.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188808"}, {"primary_key": "3512931", "vector": [], "sparse_vector": [], "title": "Fast fencing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider very natural “fence enclosure” problems studied by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> in the early 90s. Given a set S of n points in the plane, we aim at finding a set of closed curves such that (1) each point is enclosed by a curve and (2) the total length of the curves is minimized. We consider two main variants. In the first variant, we pay a unit cost per curve in addition to the total length of the curves. An equivalent formulation of this version is that we have to enclose n unit disks, paying only the total length of the enclosing curves. In the other variant, we are allowed to use at most k closed curves and pay no cost per curve. For the variant with at most k closed curves, we present an algorithm that is polynomial in both n and k. For the variant with unit cost per curve, or unit disks, we present a near-linear time algorithm. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> solved the problem with at most k curves in nO(k) time. <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> used this to solve the unit cost per curve version in exponential time. At the time, they conjectured that the problem with k curves is NP-hard for general k. Our polynomial time algorithm refutes this unless P equals NP.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188878"}, {"primary_key": "3512932", "vector": [], "sparse_vector": [], "title": "The art gallery problem is ∃ ℝ-complete.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that the art gallery problem is equivalent under polynomial time reductions to deciding whether a system of polynomial equations over the real numbers has a solution. The art gallery problem is a classic problem in computational geometry, introduced in 1973 by <PERSON>. Given a simple polygon P and an integer k, the goal is to decide if there exists a set G of k guards within P such that every point p ∈ P is seen by at least one guard g∈ G. Each guard corresponds to a point in the polygon P, and we say that a guard g sees a point p if the line segment pg is contained in P.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188868"}, {"primary_key": "3512933", "vector": [], "sparse_vector": [], "title": "Fine-grained complexity for sparse graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the fine-grained complexity of sparse graph problems that currently have Õ(mn) time algorithms, where m is the number of edges and n is the number of vertices in the input graph. This class includes several important path problems on both directed and undirected graphs, including APSP, MWC (Minimum Weight Cycle), Radius, Eccentricities, BC (Betweenness Centrality), etc.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188888"}, {"primary_key": "3512934", "vector": [], "sparse_vector": [], "title": "(Gap/S)ETH hardness of SVP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We prove the following quantitative hardness results for the Shortest Vector Problem in the ℓp norm (SVP_p), where n is the rank of the input lattice.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188840"}, {"primary_key": "3512935", "vector": [], "sparse_vector": [], "title": "Bootstrapping variables in algebraic circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that for the blackbox polynomial identity testing (PIT) problem it suffices to study circuits that depend only on the first extremely few variables. One only need to consider size-s degree-s circuits that depend on the first log∘ c s variables (where c is a constant and we are composing c logarithms). Thus, hitting-set generator (hsg) manifests a bootstrapping behavior— a partial hsg against very few variables can be efficiently grown to a complete hsg. A boolean analog, or a pseudorandom generator property of this type, is unheard-of. Our idea is to use the partial hsg and its annihilator polynomial to efficiently bootstrap the hsg exponentially wrt variables. This is repeated c times in an efficient way.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188762"}, {"primary_key": "3512936", "vector": [], "sparse_vector": [], "title": "Operator scaling via geodesically convex optimization, invariant theory and polynomial identity testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>zhi Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new second-order method for geodesically convex optimization on the natural hyperbolic metric over positive definite matrices. We apply it to solve the operator scaling problem in time polynomial in the input size and logarithmic in the error. This is an exponential improvement over previous algorithms which were analyzed in the usual Euclidean, \"commutative\" metric (for which the above problem is not convex). Our method is general and applicable to other settings.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188942"}, {"primary_key": "3512937", "vector": [], "sparse_vector": [], "title": "Cell-probe lower bounds from online communication complexity.", "authors": ["<PERSON>", "<PERSON>", "Huacheng Yu"], "summary": "In this work, we introduce an online model for communication complexity. Analogous to how online algorithms receive their input piece-by-piece, our model presents one of the players, <PERSON>, his input piece-by-piece, and has the players <PERSON> and <PERSON> cooperate to compute a result each time before the next piece is revealed to <PERSON>. This model has a closer and more natural correspondence to dynamic data structures than classic communication models do, and hence presents a new perspective on data structures.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188862"}, {"primary_key": "3512938", "vector": [], "sparse_vector": [], "title": "Data-dependent hashing via nonlinear spectral gaps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aleksan<PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "We establish a generic reduction from _nonlinear spectral gaps_ of metric spaces to data-dependent Locality-Sensitive Hashing, yielding a new approach to the high-dimensional Approximate Near Neighbor Search problem (ANN) under various distance functions. Using this reduction, we obtain the following results:", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188846"}, {"primary_key": "3512939", "vector": [], "sparse_vector": [], "title": "Fully dynamic maximal independent set with sublinear update time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A maximal independent set (MIS) can be maintained in an evolving m-edge graph by simply recomputing it from scratch in O(m) time after each update. But can it be maintained in time sublinear in m in fully dynamic graphs?", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188922"}, {"primary_key": "3512940", "vector": [], "sparse_vector": [], "title": "Clique is hard on average for regular resolution.", "authors": ["<PERSON>", "Ilario Bonacina", "Susanna F. de Rezende", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that for k ≪ n1/4 regular resolution requires length nΩ(k) to establish that an <PERSON><PERSON><PERSON><PERSON> graph with appropriately chosen edge density does not contain a k-clique. This lower bound is optimal up to the multiplicative constant in the exponent, and also implies unconditional nΩ(k) lower bounds on running time for several state-of-the-art algorithms for finding maximum cliques in graphs.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188856"}, {"primary_key": "3512941", "vector": [], "sparse_vector": [], "title": "Towards tight approximation bounds for graph diameter and eccentricities.", "authors": ["Arturs Backurs", "<PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "Among the most important graph parameters is the Diameter, the largest distance between any two vertices. There are no known very efficient algorithms for computing the Diameter exactly. Thus, much research has been devoted to how fast this parameter can be approximated. <PERSON><PERSON><PERSON> et al. [SODA 2014] showed that the diameter can be approximated within a multiplicative factor of 3/2 in Õ(m3/2) time. Furthermore, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> W. [STOC 13] showed that unless the Strong Exponential Time Hypothesis (SETH) fails, no O(n2−ε) time algorithm can achieve an approximation factor better than 3/2 in sparse graphs. Thus the above algorithm is essentially optimal for sparse graphs for approximation factors less than 3/2. It was, however, completely plausible that a 3/2-approximation is possible in linear time. In this work we conditionally rule out such a possibility by showing that unless SETH fails no O(m3/2−ε) time algorithm can achieve an approximation factor better than 5/3.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188950"}, {"primary_key": "3512942", "vector": [], "sparse_vector": [], "title": "Succinct delegation for low-space non-deterministic computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We construct a delegation scheme for verifying non-deterministic computations, with complexity proportional only to the non-deterministic space of the computation. Specifically, letting n denote the input length, we construct a delegation scheme for any language verifiable in non-deterministic time and space (T(n), S(n)) with communication complexity poly(S(n)), verifier runtime n.polylog(T(n))+poly(S(n)), and prover runtime poly(T(n)).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188924"}, {"primary_key": "3512943", "vector": [], "sparse_vector": [], "title": "The adaptive complexity of maximizing a submodular function.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study the adaptive complexity of submodular optimization. Informally, the adaptive complexity of a problem is the minimal number of sequential rounds required to achieve a constant factor approximation when polynomially-many queries can be executed in parallel at each round. Adaptivity is a fundamental concept that is heavily studied in computer science, largely due to the need for parallelizing computation. Somewhat surprisingly, very little is known about adaptivity in submodular optimization. For the canonical problem of maximizing a monotone submodular function under a cardinality constraint, to the best of our knowledge, all that is known to date is that the adaptive complexity is between 1 and Ω(n).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188752"}, {"primary_key": "3512944", "vector": [], "sparse_vector": [], "title": "New classes of distributed time complexity.", "authors": ["Alki<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A number of recent papers – e.g. <PERSON> et al. (STOC 2016), <PERSON> et al. (FOCS 2016), <PERSON><PERSON><PERSON><PERSON> & Su (SODA 2017), <PERSON> et al. (PODC 2017), and <PERSON> (FOCS 2017) – have advanced our understanding of one of the most fundamental questions in theory of distributed computing: what are the possible time complexity classes of LCL problems in the LOCAL model? In essence, we have a graph problem Π in which a solution can be verified by checking all radius-O(1) neighbourhoods, and the question is what is the smallest T such that a solution can be computed so that each node chooses its own output based on its radius-T neighbourhood. Here T is the distributed time complexity of Π. The time complexity classes for deterministic algorithms in bounded-degree graphs that are known to exist by prior work are Θ(1), Θ(log* n), Θ(logn), Θ(n1/k), and Θ(n). It is also known that there are two gaps: one between ω(1) and o(loglog* n), and another between ω(log* n) and o(logn). It has been conjectured that many more gaps exist, and that the overall time hierarchy is relatively simple – indeed, this is known to be the case in restricted graph families such as cycles and grids. We show that the picture is much more diverse than previously expected. We present a general technique for engineering LCL problems with numerous different deterministic time complexities, including Θ(logα n) for any α ≥ 1, 2Θ(logα n) for any α ≤ 1, and Θ(nα) for any α < 1/2 in the high end of the complexity spectrum, and Θ(logα log* n) for any α ≥ 1, 2Θ(logα log* n) for any α ≤ 1, and Θ((log* n)α) for any α ≤ 1 in the low end of the complexity spectrum; here α is a positive rational number.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188860"}, {"primary_key": "3512945", "vector": [], "sparse_vector": [], "title": "The gram-schmidt walk: a cure for the Banaszczyk blues.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Garg", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An important result in discrepancy due to <PERSON><PERSON><PERSON><PERSON><PERSON> states that for any set of n vectors in ℝm of ℓ2 norm at most 1 and any convex body K in ℝm of Gaussian measure at least half, there exists a ± 1 combination of these vectors which lies in 5K. This result implies the best known bounds for several problems in discrepancy. <PERSON><PERSON><PERSON><PERSON><PERSON>'s proof of this result is non-constructive and an open problem has been to give an efficient algorithm to find such a ± 1 combination of the vectors.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188850"}, {"primary_key": "3512946", "vector": [], "sparse_vector": [], "title": "Fast algorithms for knapsack via convolution and prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The knapsack problem is a fundamental problem in combinatorial optimization. It has been studied extensively from theoretical as well as practical perspectives as it is one of the most well-known NP-hard problems. The goal is to pack a knapsack of size t with the maximum value from a collection of n items with given sizes and values.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188876"}, {"primary_key": "3512947", "vector": [], "sparse_vector": [], "title": "A framework for ETH-tight algorithms and lower bounds in geometric intersection graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give an algorithmic and lower-bound framework that facilitates the construction of subexponential algorithms and matching conditional complexity bounds. It can be applied to a wide range of geometric intersection graphs (intersections of similarly sized fat objects), yielding algorithms with running time 2O(n1−1/d) for any fixed dimension d≥ 2 for many well known graph problems, including Independent Set, r-Dominating Set for constant r, and Steiner Tree. For most problems, we get improved running times compared to prior work; in some cases, we give the first known subexponential algorithm in geometric intersection graphs. Additionally, most of the obtained algorithms work on the graph itself, i.e., do not require any geometric information. Our algorithmic framework is based on a weighted separator theorem and various treewidth techniques.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188854"}, {"primary_key": "3512948", "vector": [], "sparse_vector": [], "title": "Inapproximability of the independent set polynomial in the complex plane.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the complexity of approximating the value of the independent set polynomial ZG(λ) of a graph G with maximum degree Δ when the activity λ is a complex number.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188788"}, {"primary_key": "3512949", "vector": [], "sparse_vector": [], "title": "Multi-collision resistance: a paradigm for keyless hash functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new notion of multi-collision resistance for keyless hash functions. This is a natural relaxation of collision resistance where it is hard to find multiple inputs with the same hash in the following sense. The number of colliding inputs that a polynomial-time non-uniform adversary can find is not much larger than its advice. We discuss potential candidates for this notion and study its applications.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188870"}, {"primary_key": "3512950", "vector": [], "sparse_vector": [], "title": "Generalized matrix completion and algebraic natural proofs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algebraic natural proofs were recently introduced by <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Proc. of the 49th Annual ACM SIGACT Symposium on Theory of Computing (STOC), pages 653–664, 2017) and independently by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> (CoRR, abs/1701.01717, 2017) as an attempt to transfer <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s famous barrier result (J. Comput. Syst. Sci., 55(1): 24–35, 1997) for Boolean circuit complexity to algebraic complexity theory. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s barrier result relies on a widely believed assumption, namely, the existence of pseudo-random generators. Unfortunately, there is no known analogous theory of pseudo-randomness in the algebraic setting. Therefore, <PERSON> et al. use a concept called succinct hitting sets instead. This assumption is related to polynomial identity testing, but it is currently not clear how plausible this assumption is. <PERSON> et al. are only able to construct succinct hitting sets against rather weak models of arithmetic circuits.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188832"}, {"primary_key": "3512951", "vector": [], "sparse_vector": [], "title": "General strong polarization.", "authors": ["Jaroslaw Blasiok", "<PERSON>en<PERSON><PERSON>wami", "Preetum Nakkiran", "<PERSON><PERSON>", "Madhu <PERSON>"], "summary": "<PERSON><PERSON>'s exciting discovery of polar codes has provided an altogether new way to efficiently achieve Shannon capacity. Given a (constant-sized) invertible matrix M, a family of polar codes can be associated with this matrix and its ability to approach capacity follows from the polarization of an associated [0,1]-bounded martingale, namely its convergence in the limit to either 0 or 1 with probability 1. <PERSON><PERSON> showed appropriate polarization of the martingale associated with the matrix G2 = ( [complex formula not displayed] ) to get capacity achieving codes. His analysis was later extended to all matrices M which satisfy an obvious necessary condition for polarization.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188816"}, {"primary_key": "3512952", "vector": [], "sparse_vector": [], "title": "Extensor-coding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We devise an algorithm that approximately computes the number of paths of length k in a given directed graph with n vertices up to a multiplicative error of 1 ± ε. Our algorithm runs in time ε−2 4k(n+m) poly(k). The algorithm is based on associating with each vertex an element in the exterior (or, Grassmann) algebra, called an extensor, and then performing computations in this algebra. This connection to exterior algebra generalizes a number of previous approaches for the longest path problem and is of independent conceptual interest. Using this approach, we also obtain a deterministic 2k·poly(n) time algorithm to find a k-path in a given directed graph that is promised to have few of them. Our results and techniques generalize to the subgraph isomorphism problem when the subgraphs we are looking for have bounded pathwidth. Finally, we also obtain a randomized algorithm to detect k-multilinear terms in a multivariate polynomial given as a general algebraic circuit. To the best of our knowledge, this was previously only known for algebraic circuits not involving negative constants.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188902"}, {"primary_key": "3512953", "vector": [], "sparse_vector": [], "title": "Hitting sets with near-optimal error for read-once branching programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> (Combinatorica'92) constructed a pseudorandom generator for length n, width n read-once branching programs (ROBPs) with error ε and seed length O(log2n + logn · log(1/ε)). A major goal in complexity theory is to reduce the seed length, hopefully, to the optimal O(logn+log(1/ε)), or to construct improved hitting sets, as these would yield stronger derandomization of BPL and RL, respectively. In contrast to a successful line of work in restricted settings, no progress has been made for general, unrestricted, ROBPs. Indeed, <PERSON><PERSON>'s construction is the best pseudorandom generator and, prior to this work, also the best hitting set for unrestricted ROBPs.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188780"}, {"primary_key": "3512954", "vector": [], "sparse_vector": [], "title": "Interactive compression to external information.", "authors": ["<PERSON>", "Gillat Kol"], "summary": "We describe a new way of compressing two-party communication protocols to get protocols with potentially smaller communication. We show that every communication protocol that communicates C bits and reveals I bits of information about the participants' private inputs to an observer that watches the communication, can be simulated by a new protocol that communicates at most poly(I) · loglog(C) bits. Our result is tight up to polynomial factors, as it matches the recent work separating communication complexity from external information cost.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188956"}, {"primary_key": "3512955", "vector": [], "sparse_vector": [], "title": "An homotopy method for lp regression provably beyond self-concordance and in input-sparsity time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>zhi Li"], "summary": "We consider the problem of linear regression where the ℓ2n norm loss (i.e., the usual least squares loss) is replaced by the ℓpn norm. We show how to solve such problems up to machine precision in Õp(n|1/2 − 1/p|) (dense) matrix-vector products and Õp(1) matrix inversions, or alternatively in Õp(n|1/2 − 1/p|) calls to a (sparse) linear system solver. This improves the state of the art for any p∉{1,2,+∞}. Furthermore we also propose a randomized algorithm solving such problems in input sparsity time, i.e., Õp(N + poly(d)) where N is the size of the input and d is the number of variables. Such a result was only known for p=2. Finally we prove that these results lie outside the scope of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s theory of interior point methods by showing that any symmetric self-concordant barrier on the ℓpn unit ball has self-concordance parameter Ω(n).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188776"}, {"primary_key": "3512956", "vector": [], "sparse_vector": [], "title": "k-server via multiscale entropic regularization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Aleksander Madry"], "summary": "We present an O((logk)2)-competitive randomized algorithm for the k-server problem on hierarchically separated trees (HSTs). This is the first o(k)-competitive randomized algorithm for which the competitive ratio is independent of the size of the underlying HST. Our algorithm is designed in the framework of online mirror descent where the mirror map is a multiscale entropy. When combined with <PERSON><PERSON>'s static HST embedding reduction, this leads to an O((logk)2 logn)-competitive algorithm on any n-point metric space. We give a new dynamic HST embedding that yields an O((logk)3 logΔ)-competitive algorithm on any metric space where the ratio of the largest to smallest non-zero distance is at most Δ.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188798"}, {"primary_key": "3512957", "vector": [], "sparse_vector": [], "title": "Composable and versatile privacy via truncated CDP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose truncated concentrated differential privacy (tCDP), a refinement of differential privacy and of concentrated differential privacy. This new definition provides robust and efficient composition guarantees, supports powerful algorithmic techniques such as privacy amplification via sub-sampling, and enables more accurate statistical analyses. In particular, we show a central task for which the new definition enables exponential accuracy improvement.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188946"}, {"primary_key": "3512958", "vector": [], "sparse_vector": [], "title": "The polynomial method strikes back: tight quantum query bounds via dual polynomials.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The approximate degree of a Boolean function f is the least degree of a real polynomial that approximates f pointwise to error at most 1/3. Approximate degree is known to be a lower bound on quantum query complexity. We resolve or nearly resolve the approximate degree and quantum query complexities of the following basic functions: $\\bullet$ $k$-distinctness: For any constant $k$, the approximate degree and quantum query complexity of $k$-distinctness is $\\Omega(n^{3/4-1/(2k)})$. This is nearly tight for large $k$ (<PERSON><PERSON>s, FOCS 2012). $\\bullet$ Image size testing: The approximate degree and quantum query complexity of testing the size of the image of a function $[n] \\to [n]$ is $\\tilde{\\Omega}(n^{1/2})$. This proves a conjecture of <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2016), and it implies the following lower bounds: $-$ $k$-junta testing: A tight $\\tilde{\\Omega}(k^{1/2})$ lower bound, answering the main open question of <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2016). $-$ Statistical Distance from Uniform: A tight $\\tilde{\\Omega}(n^{1/2})$ lower bound, answering the main question left open by <PERSON><PERSON><PERSON><PERSON> et al. (STACS 2010 and IEEE Trans. Inf. Theory 2011). $-$ Shannon entropy: A tight $\\tilde{\\Omega}(n^{1/2})$ lower bound, answering a question of <PERSON> and <PERSON> (2017). $\\bullet$ Surjectivity: The approximate degree of the Surjectivity function is $\\tilde{\\Omega}(n^{3/4})$. The best prior lower bound was $\\Omega(n^{2/3})$. Our result matches an upper bound of $\\tilde{O}(n^{3/4})$ due to Sherstov, which we reprove using different techniques. The quantum query complexity of this function is known to be $\\Theta(n)$ (Beame and Machmouchi, QIC 2012 and Sherstov, FOCS 2015). Our upper bound for Surjectivity introduces new techniques for approximating Boolean functions by low-degree polynomials. Our lower bounds are proved by significantly refining techniques recently introduced by Bun and Thaler (FOCS 2017).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188784"}, {"primary_key": "3512959", "vector": [], "sparse_vector": [], "title": "Constant-factor approximation for ordered k-median.", "authors": ["Jaroslaw Byrka", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the Ordered k-Median problem, in which the solution is evaluated by first sorting the client connection costs and then multiplying them with a predefined non-increasing weight vector (higher connection costs are taken with larger weights). Since the 1990s, this problem has been studied extensively in the discrete optimization and operations research communities and has emerged as a framework unifying many fundamental clustering and location problems such as k-Median and k-Center. Obtaining non-trivial approximation algorithms was an open problem even for simple topologies such as trees. Recently, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2017) were able to obtain an O(log n) approximation algorithm for Ordered k-Median using a sophisticated local-search approach. The existence of a constant-factor approximation algorithm, however, remained open even for the rectangular weight vector.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188930"}, {"primary_key": "3512960", "vector": [], "sparse_vector": [], "title": "Testing conditional independence of discrete distributions.", "authors": ["Clément L. <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of testing *conditional independence* for discrete distributions. Specifically, given samples from a discrete random variable (X, Y, Z) on domain [ℓ1]×[ℓ2] × [n], we want to distinguish, with probability at least 2/3, between the case that X and Y are conditionally independent given Z from the case that (X, Y, Z) is є-far, in ℓ1-distance, from every distribution that has this property. Conditional independence is a concept of central importance in probability and statistics with important applications in various scientific domains. As such, the statistical task of testing conditional independence has been extensively studied in various forms within the statistics and econometrics community for nearly a century. Perhaps surprisingly, this problem has not been previously considered in the framework of distribution property testing and in particular no tester with *sublinear* sample complexity is known, even for the important special case that the domains of X and Y are binary.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188756"}, {"primary_key": "3512961", "vector": [], "sparse_vector": [], "title": "Tight cell probe bounds for succinct Boolean matrix-vector multiplication.", "authors": ["Diptarka Chakraborty", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The conjectured hardness of Boolean matrix-vector multiplication has been used with great success to prove conditional lower bounds for numerous important data structure problems, see <PERSON><PERSON><PERSON> et al. [STOC’15]. In recent work, <PERSON> and <PERSON> [SODA’17] attacked the problem from the upper bound side and gave a surprising cell probe data structure (that is, we only charge for memory accesses, while computation is free). Their cell probe data structure answers queries in Õ(n7/4) time and is succinct in the sense that it stores the input matrix in read-only memory, plus an additional Õ(n7/4) bits on the side. In this paper, we essentially settle the cell probe complexity of succinct Boolean matrix-vector multiplication. We present a new cell probe data structure with query time Õ(n3/2) storing just Õ(n3/2) bits on the side. We then complement our data structure with a lower bound showing that any data structure storing r bits on the side, with n < r < n2 must have query time t satisfying tr = Ω (n3). For r ≤ n, any data structure must have t = Ω (n2). Since lower bounds in the cell probe model also apply to classic word-RAM data structures, the lower bounds naturally carry over. We also prove similar lower bounds for matrix-vector multiplication over F2.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188830"}, {"primary_key": "3512962", "vector": [], "sparse_vector": [], "title": "An optimal distributed (Δ+1)-coloring algorithm?", "authors": ["<PERSON><PERSON><PERSON>", "Wenzheng Li", "<PERSON>"], "summary": "Vertex coloring is one of the classic symmetry breaking problems studied in distributed computing. In this paper we present a new algorithm for (Δ+1)-list coloring in the randomized LOCAL model running in O(log∗n + Detd(poly logn)) time, where Detd(n′) is the deterministic complexity of (deg+1)-list coloring (v's palette has size deg(v)+1) on n′-vertex graphs. This improves upon a previous randomized algorithm of <PERSON>, <PERSON>, and <PERSON> (STOC 2016). with complexity O(√logΔ + loglogn + Detd(poly logn)), and (when Δ is sufficiently large) is much faster than the best known deterministic algorithm of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (FOCS 2016), with complexity O(√Δlog2.5Δ + log* n).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188964"}, {"primary_key": "3512963", "vector": [], "sparse_vector": [], "title": "Improved pseudorandomness for unordered branching programs through local monotonicity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an explicit pseudorandom generator with seed length Õ((logn)w+1) for read-once, oblivious, width w branching programs that can read their input bits in any order. This improves upon the work of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (FOCS'12) where they required seed length n1/2+o(1).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188800"}, {"primary_key": "3512964", "vector": [], "sparse_vector": [], "title": "Simulation beats richness: new data-structure lower bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop a new technique for proving lower bounds in the setting of asymmetric communication, a model that was introduced in the famous works of <PERSON><PERSON><PERSON> (STOC'94) and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (STOC'95). At the core of our technique is the first simulation theorem in the asymmetric setting, where <PERSON> gets a p × n matrix x over F2 and <PERSON> gets a vector y ∈ F2n. <PERSON> and <PERSON> need to evaluate f(x· y) for a Boolean function f: {0,1}p → {0,1}. Our simulation theorems show that a deterministic/randomized communication protocol exists for this problem, with cost C· n for <PERSON> and C for <PERSON>, if and only if there exists a deterministic/randomized *parity decision tree* of cost Θ(C) for evaluating f.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188874"}, {"primary_key": "3512965", "vector": [], "sparse_vector": [], "title": "Capacity upper bounds for deletion-type channels.", "authors": ["<PERSON><PERSON>"], "summary": "We develop a systematic approach, based on convex programming and real analysis, for obtaining upper bounds on the capacity of the binary deletion channel and, more generally, channels with i.i.d. insertions and deletions. Other than the classical deletion channel, we give a special attention to the Poisson-repeat channel introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (IEEE Transactions on Information Theory, 2006). Our framework can be applied to obtain capacity upper bounds for any repetition distribution (the deletion and Poisson-repeat channels corresponding to the special cases of <PERSON><PERSON><PERSON> and Po<PERSON>on distributions). Our techniques essentially reduce the task of proving capacity upper bounds to maximizing a univariate, real-valued, and often concave function over a bounded interval. The corresponding univariate function is carefully designed according to the underlying distribution of repetitions and the choices vary depending on the desired strength of the upper bounds as well as the desired simplicity of the function (e.g., being only efficiently computable versus having an explicit closed-form expression in terms of elementary, or common special, functions).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188768"}, {"primary_key": "3512966", "vector": [], "sparse_vector": [], "title": "Universal points in the asymptotic spectrum of tensors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The asymptotic restriction problem for tensors s and t is to find the smallest ≥ 0 such that the nth tensor power of t can be obtained from the (n + o(n))th tensor power of s by applying linear maps to the tensor legs — this is called restriction — when n goes to infinity. Applications include computing the arithmetic complexity of matrix multiplication in algebraic complexity theory, deciding the feasibility of an asymptotic transformation between pure quantum States via stochastic local operations and classical communication in quantum information theory, bounding the query complexity of certain properties in algebraic property testing, and bounding the size of combinatorial structures like tri-colored sum-free sets in additive combinatorics. Naturally, the asymptotic restriction problem asks for obstructions (think of lower bounds in computational complexity) and constructions (think of fast matrix multiplication algorithms). <PERSON><PERSON><PERSON> showed that for obstructions it is sufficient to consider maps from k-tensors to nonnegative reals, that are monotone under restriction, normalised on diagonal tensors, additive under direct sum and multiplicative under tensor product, named spectral points (SFCS 1986 and <PERSON><PERSON>. Math. 1988). <PERSON><PERSON><PERSON> introduced the support functionals, which are spectral points for oblique tensors, a strict subfamily of all tensors (<PERSON><PERSON> <PERSON><PERSON> Math. 1991). On the construction side, an important work is the <PERSON><PERSON>–<PERSON> method for tight tensors and tight sets. We present the first nontrivial spectral points for the family of all complex tensors, named quantum functionals. Finding such universal spectral points has been an open problem for thirty years. We use techniques from quantum information theory, invariant theory and moment polytopes. We present comparisons among the support functionals and our quantum functionals, and compute generic values. We relate the functionals to instability from geometric invariant theory, in the spirit of Blasiak et al. (Discrete Anal. 2017). We prove that the quantum functionals are asymptotic upper bounds on slice-rank and multi-slice rank, extending a result of Tao and Sawin.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188766"}, {"primary_key": "3512967", "vector": [], "sparse_vector": [], "title": "Almost polynomial hardness of node-disjoint paths in grids.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the classical Node-Disjoint Paths (NDP) problem, we are given an n-vertex graph G=(V,E), and a collection M={(s1,t1),…,(sk,tk)} of pairs of its vertices, called source-destination, or demand pairs. The goal is to route as many of the demand pairs as possible, where to route a pair we need to select a path connecting it, so that all selected paths are disjoint in their vertices. The best current algorithm for NDP achieves an O(√n)-approximation, while, until recently, the best negative result was a factor Ω(log1/2−єn)-hardness of approximation, for any constant є, unless NP ⊆ ZPTIME(npoly logn). In a recent work, the authors have shown an improved 2Ω(√logn)-hardness of approximation for NDP, unless NP⊆ DTIME(nO(logn)), even if the underlying graph is a subgraph of a grid graph, and all source vertices lie on the boundary of the grid. Unfortunately, this result does not extend to grid graphs.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188772"}, {"primary_key": "3512968", "vector": [], "sparse_vector": [], "title": "Explicit binary tree codes with polylogarithmic size alphabet.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper makes progress on the problem of explicitly constructing a binary tree code with constant distance and constant alphabet size.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188928"}, {"primary_key": "3512969", "vector": [], "sparse_vector": [], "title": "Round compression for parallel matching algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aleksander Madry", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For over a decade now we have been witnessing the success of massive parallel computation (MPC) frameworks, such as MapReduce, Hadoop, Dryad, or Spark. One of the reasons for their success is the fact that these frameworks are able to accurately capture the nature of large-scale computation. In particular, compared to the classic distributed algorithms or PRAM models, these frameworks allow for much more local computation. The fundamental question that arises in this context is though: can we leverage this additional power to obtain even faster parallel algorithms?", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188764"}, {"primary_key": "3512970", "vector": [], "sparse_vector": [], "title": "A friendly smoothed analysis of the simplex method.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Explaining the excellent practical performance of the simplex method for linear programming has been a major topic of research for over 50 years. One of the most successful frameworks for understanding the simplex method was given by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (JACM '04), who the developed the notion of smoothed analysis. Starting from an arbitrary linear program with d variables and n constraints, <PERSON><PERSON><PERSON> and <PERSON><PERSON> analyzed the expected runtime over random perturbations of the LP (smoothed LP), where variance σ Gaussian noise is added to the LP data. In particular, they gave a two-stage shadow vertex simplex algorithm which uses an expected O(n86 d55 σ−30) number of simplex pivots to solve the smoothed LP. Their analysis and runtime was substantially improved by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS '05) and later <PERSON><PERSON><PERSON><PERSON> (SICOMP '09). The fastest current algorithm, due to <PERSON><PERSON><PERSON><PERSON>, solves the smoothed LP using an expected O(d3 log3 n σ−4 + d9log7 n) number of pivots, improving the dependence on n from polynomial to logarithmic.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188826"}, {"primary_key": "3512971", "vector": [], "sparse_vector": [], "title": "A converse to Banach&apos;s fixed point theorem and its CLS-completeness.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "<PERSON><PERSON>'s fixed point theorem for contraction maps has been widely used to analyze the convergence of iterative methods in non-convex problems. It is a common experience, however, that iterative maps fail to be globally contracting under the natural metric in their domain, making the applicability of <PERSON><PERSON>'s theorem limited. We explore how generally we can apply <PERSON><PERSON>'s fixed point theorem to establish the convergence of iterative methods when pairing it with carefully designed metrics. Our first result is a strong converse of <PERSON><PERSON>'s theorem, showing that it is a universal analysis tool for establishing global convergence of iterative methods to unique fixed points, and for bounding their convergence rate. In other words, we show that, whenever an iterative map globally converges to a unique fixed point, there exists a metric under which the iterative map is contracting and which can be used to bound the number of iterations until convergence. We illustrate our approach in the widely used power method, providing a new way of bounding its convergence rate through contraction arguments.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188968"}, {"primary_key": "3512972", "vector": [], "sparse_vector": [], "title": "Fine-grained reductions from approximate counting to decision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce a general framework for fine-grained reductions of approximate counting problems to their decision versions. (Thus we use an oracle that decides whether any witness exists to multiplicatively approximate the number of witnesses with minimal overhead.) This mirrors a foundational result of <PERSON><PERSON><PERSON> (STOC 1983) and <PERSON><PERSON> (SICOMP 1985) in the polynomial-time setting, and a similar result of <PERSON><PERSON><PERSON>uller (IWPEC 2006) in the FPT setting. Using our framework, we obtain such reductions for some of the most important problems in fine-grained complexity: the Orthogonal Vectors problem, 3SUM, and the Negative-Weight Triangle problem (which is closely related to All-Pairs Shortest Path). We also provide a fine-grained reduction from approximate #SAT to SAT. Suppose the Strong Exponential Time Hypothesis (SETH) is false, so that for some $1 0$ as part of the input).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188920"}, {"primary_key": "3512973", "vector": [], "sparse_vector": [], "title": "List-decodable robust mean estimation and learning mixtures of spherical gaussians.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of list-decodable (robust) Gaussian mean estimation and the related problem of learning mixtures of separated spherical Gaussians. In the former problem, we are given a set T of points in n with the promise that an α-fraction of points in T, where 0< α < 1/2, are drawn from an unknown mean identity covariance Gaussian G, and no assumptions are made about the remaining points. The goal is to output a small list of candidate vectors with the guarantee that at least one of the candidates is close to the mean of G. In the latter problem, we are given samples from a k-mixture of spherical Gaussians on n and the goal is to estimate the unknown model parameters up to small accuracy. We develop a set of techniques that yield new efficient algorithms with significantly improved guarantees for these problems. Specifically, our main contributions are as follows:", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188758"}, {"primary_key": "3512974", "vector": [], "sparse_vector": [], "title": "Learning geometric concepts with nasty noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the efficient learnability of geometric concept classes — specifically, low-degree polynomial threshold functions (PTFs) and intersections of halfspaces — when a fraction of the training data is adversarially corrupted. We give the first polynomial-time PAC learning algorithms for these concept classes with dimension-independent error guarantees in the presence of nasty noise under the Gaussian distribution. In the nasty noise model, an omniscient adversary can arbitrarily corrupt a small fraction of both the unlabeled data points and their labels. This model generalizes well-studied noise models, including the malicious noise model and the agnostic (adversarial label noise) model. Prior to our work, the only concept class for which efficient malicious learning algorithms were known was the class of origin-centered halfspaces.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188754"}, {"primary_key": "3512975", "vector": [], "sparse_vector": [], "title": "Towards a proof of the 2-to-1 games conjecture?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a polynomial time reduction from gap-3LIN to label cover with 2-to-1 constraints. In the \"yes\" case the fraction of satisfied constraints is at least 1 −ε, and in the \"no\" case we show that this fraction is at most ε, assuming a certain (new) combinatorial hypothesis on the Grassmann graph. In other words, we describe a combinatorial hypothesis that implies the 2-to-1 conjecture with imperfect completeness. The companion submitted paper [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, STOC 2018] makes some progress towards proving this hypothesis.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188804"}, {"primary_key": "3512976", "vector": [], "sparse_vector": [], "title": "On non-optimally expanding sets in Grassmann graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the structure of non-expanding sets in the Grassmann graph. We put forth a hypothesis stating that every small set whose expansion is smaller than 1−δ must be correlated with one of a specified list of sets which are isomorphic to smaller Grassmann graphs. We develop a framework of Fourier analysis for analyzing functions over the Grassmann graph, and prove that our hypothesis holds for all sets whose expansion is below 7/8.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188806"}, {"primary_key": "3512977", "vector": [], "sparse_vector": [], "title": "Universal protocols for information dissemination using emergent signals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider a population of n agents which communicate with each other in a decentralized manner, through random pairwise interactions. One or more agents in the population may act as authoritative sources of information, and the objective of the remaining agents is to obtain information from or about these source agents. We study two basic tasks: broadcasting, in which the agents are to learn the bit-state of an authoritative source which is present in the population, and source detection, in which the agents are required to decide if at least one source agent is present in the population or not.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188818"}, {"primary_key": "3512978", "vector": [], "sparse_vector": [], "title": "Discovering the roots: uniform closure results for algebraic classes under factoring.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Newton iteration (NI) is an almost 350 years old recursive formula that approximates a simple root of a polynomial quite rapidly. We generalize it to a matrix recurrence (allRootsNI) that approximates all the roots simultaneously. In this form, the process yields a better circuit complexity in the case when the number of roots r is small but the multiplicities are exponentially large. Our method sets up a linear system in r unknowns and iteratively builds the roots as formal power series. For an algebraic circuit f(x1,…,xn) of size s we prove that each factor has size at most a polynomial in: s and the degree of the squarefree part of f. Consequently, if f1 is a 2Ω(n)-hard polynomial then any nonzero multiple ∏i fiei is equally hard for arbitrary positive ei's, assuming that ∑ideg(fi) is at most 2O(n).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188760"}, {"primary_key": "3512979", "vector": [], "sparse_vector": [], "title": "On approximating the number of k-cliques in sublinear time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of approximating the number of k-cliques in a graph when given query access to the graph. We consider the standard query model for general graphs via (1) degree queries, (2) neighbor queries and (3) pair queries. Let n denote the number of vertices in the graph, m the number of edges, and Ck the number of k-cliques. We design an algorithm that outputs a (1+ε)-approximation (with high probability) for Ck, whose expected query complexity and running time are O(n/Ck1/k+mk/2/Ck )(logn, 1/ε,k).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188810"}, {"primary_key": "3512980", "vector": [], "sparse_vector": [], "title": "Interactive coding over the noisy broadcast channel.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A set of n players, each holding a private input bit, communicate over a noisy broadcast channel. Their mutual goal is for all players to learn all inputs. At each round one of the players broadcasts a bit to all the other players, and the bit received by each player is flipped with a fixed constant probability (independently for each recipient). How many rounds are needed?", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188884"}, {"primary_key": "3512981", "vector": [], "sparse_vector": [], "title": "Approximating generalized network design under (dis)economies of scale with applications to energy efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Yangguang Shi"], "summary": "In a generalized network design (GND) problem, a set of resources are assigned (non-exclusively) to multiple requests. Each request contributes its weight to the resources it uses and the total load on a resource is then translated to the cost it incurs via a resource specific cost function. Motivated by energy efficiency applications, recently, there is a growing interest in GND using cost functions that exhibit (dis)economies of scale ((D)oS), namely, cost functions that appear subadditive for small loads and superadditive for larger loads.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188812"}, {"primary_key": "3512982", "vector": [], "sparse_vector": [], "title": "Efficient decoding of random errors for quantum expander codes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show that quantum expander codes, a constant-rate family of quantum low-density parity check (LDPC) codes, with the quasi-linear time decoding algorithm of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> can correct a constant fraction of random errors with very high probability. This is the first construction of a constant-rate quantum LDPC code with an efficient decoding algorithm that can correct a linear number of random errors with a negligible failure probability. Finding codes with these properties is also motivated by <PERSON><PERSON><PERSON>’s construction of fault tolerant schemes with constant space overhead.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188886"}, {"primary_key": "3512983", "vector": [], "sparse_vector": [], "title": "Consensus halving is PPA-complete.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show that the computational problem Consensus Halving is PPA-Complete, the first PPA-Completeness result for a problem whose definition does not involve an explicit circuit. We also show that an approximate version of this problem is polynomial-time equivalent to Necklace Splitting, which establishes PPAD-hardness for Necklace Splitting and suggests that it is also PPA-Complete.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188880"}, {"primary_key": "3512984", "vector": [], "sparse_vector": [], "title": "Nearly work-efficient parallel algorithm for digraph reachability.", "authors": ["<PERSON>"], "summary": "One of the simplest problems on directed graphs is that of identifying the set of vertices reachable from a designated source vertex. This problem can be solved easily sequentially by performing a graph search, but efficient parallel algorithms have eluded researchers for decades. For sparse high-diameter graphs in particular, there is no known work-efficient parallel algorithm with nontrivial parallelism. This amounts to one of the most fundamental open questions in parallel graph algorithms: Is there a parallel algorithm for digraph reachability with nearly linear work? This paper shows that the answer is yes.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188926"}, {"primary_key": "3512985", "vector": [], "sparse_vector": [], "title": "A PSPACE construction of a hitting set for the closure of small algebraic circuits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we study the complexity of constructing a hitting set for VP, the class of polynomials that can be infinitesimally approximated by polynomials that are computed by polynomial sized algebraic circuits, over the real or complex numbers. Specifically, we show that there is a PSPACE algorithm that given n,s,r in unary outputs a set of rational n-tuples of size poly(n,s,r), with poly(n,s,r) bit complexity, that hits all n-variate polynomials of degree r that are the limit of size s algebraic circuits. Previously it was known that a random set of this size is a hitting set, but a construction that is certified to work was only known in EXPSPACE (or EXPH assuming the generalized Riemann hypothesis). As a corollary we get that a host of other algebraic problems such as Noether Normalization Lemma, can also be solved in PSPACE deterministically, where earlier only randomized algorithms and EXPSPACE algorithms (or EXPH assuming the generalized Riemann hypothesis) were known.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188792"}, {"primary_key": "3512986", "vector": [], "sparse_vector": [], "title": "Operator scaling with specified marginals.", "authors": ["<PERSON>"], "summary": "The completely positive maps, a generalization of the nonnegative matrices, are a well-studied class of maps from n× n matrices to m× m matrices. The existence of the operator analogues of doubly stochastic scalings of matrices, the study of which is known as operator scaling, is equivalent to a multitude of problems in computer science and mathematics such rational identity testing in non-commuting variables, noncommutative rank of symbolic matrices, and a basic problem in invariant theory (<PERSON><PERSON><PERSON> et. al., 2016).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188932"}, {"primary_key": "3512987", "vector": [], "sparse_vector": [], "title": "Monotone circuit lower bounds from resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For any unsatisfiable CNF formula F that is hard to refute in the Resolution proof system, we show that a gadget-composed version of F is hard to refute in any proof system whose lines are computed by efficient communication protocols—or, equivalently, that a monotone function associated with F has large monotone circuit complexity. Our result extends to monotone real circuits, which yields new lower bounds for the Cutting Planes proof system.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188838"}, {"primary_key": "3512988", "vector": [], "sparse_vector": [], "title": "A matrix expander <PERSON><PERSON><PERSON> bound.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove a Chernoff-type bound for sums of matrix-valued random variables sampled via a random walk on an expander, confirming a conjecture due to [<PERSON><PERSON><PERSON><PERSON> and <PERSON> 06]. Our proof is based on a new multi-matrix extension of the Golden<PERSON>Thompson inequality which improves upon the inequality in [<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> 17], as well as an adaptation of an argument for the scalar case due to [<PERSON><PERSON> 08]. Our new multi-matrix Golden-Thompson inequality could be of independent interest. Secondarily, we also provide a generic reduction showing that any concentration inequality for vector-valued martingales implies a concentration inequality for the corresponding expander walk, with a weakening of parameters proportional to the squared mixing time.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188890"}, {"primary_key": "3512989", "vector": [], "sparse_vector": [], "title": "Extractor-based time-space lower bounds for learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A matrix M: A × X → {−1,1} corresponds to the following learning problem: An unknown element x ∈ X is chosen uniformly at random. A learner tries to learn x from a stream of samples, (a1, b1), (a2, b2) …, where for every i, ai ∈ A is chosen uniformly at random and bi = M(ai,x).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188962"}, {"primary_key": "3512990", "vector": [], "sparse_vector": [], "title": "Deterministic distributed edge-coloring with fewer colors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a deterministic distributed algorithm, in the LOCAL model, that computes a (1+o(1))Δ-edge-coloring in polylogarithmic-time, so long as the maximum degree Δ=Ω(logn). For smaller Δ, we give a polylogarithmic-time 3Δ/2-edge-coloring. These are the first deterministic algorithms to go below the natural barrier of 2Δ−1 colors, and they improve significantly on the recent polylogarithmic-time (2Δ−1)(1+o(1))-edge-coloring of <PERSON><PERSON><PERSON><PERSON> and <PERSON> [SODA’17] and the (2Δ−1)-edge-coloring of <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS’17], positively answering the main open question of the latter. The key technical ingredient of our algorithm is a simple and novel gradual packing of judiciously chosen near-maximum matchings, each of which becomes one of the color classes.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188906"}, {"primary_key": "3512991", "vector": [], "sparse_vector": [], "title": "Improved distributed algorithms for exact shortest paths.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Computing shortest paths is one of the central problems in the theory of distributed computing. For the last few years, substantial progress has been made on the approximate single source shortest paths problem, culminating in an algorithm of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [STOC'16] which deterministically computes (1+o(1))-approximate shortest paths in Õ(D+√n) time, where D is the hop-diameter of the graph. Up to logarithmic factors, this time complexity is optimal, matching the lower bound of <PERSON><PERSON> [STOC'04].", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188948"}, {"primary_key": "3512992", "vector": [], "sparse_vector": [], "title": "A generalized Turán problem and its applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Our first theorem in this paper is a hierarchy theorem for the query complexity of testing graph properties with 1-sided error; more precisely, we show that for every sufficiently fast-growing function f, there is a graph property whose 1-sided-error query complexity is precisely f(Θ(1/ε)). No result of this type was previously known for any f which is super-polynomial. <PERSON><PERSON><PERSON> [ECCC 2005] asked to exhibit a graph property whose query complexity is 2Θ(1/ε). Our hierarchy theorem partially resolves this problem by exhibiting a property whose 1-sided-error query complexity is 2Θ(1/ε). We also use our hierarchy theorem in order to resolve a problem raised by the second author and <PERSON><PERSON> [STOC 2005] regarding testing relaxed versions of bipartiteness.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188778"}, {"primary_key": "3512993", "vector": [], "sparse_vector": [], "title": "Bounding the menu-size of approximately optimal auctions via optimal-transport duality.", "authors": ["Yannai <PERSON><PERSON>"], "summary": "The question of the minimum menu-size for approximate (i.e., up-to-ε) Bayesian revenue maximization when selling two goods to an additive risk-neutral quasilinear buyer was introduced by <PERSON> and <PERSON><PERSON> [2013], who give an upper bound of O(1/ε4) for this problem. Using the optimal-transport duality framework of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [2013, 2015], we derive the first lower bound for this problem — of Ω(1/∜ε), even when the values for the two goods are drawn i.i.d. from \"nice\" distributions, establishing how to reason about approximately optimal mechanisms via this duality framework. This bound implies, for any fixed number of goods, a tight bound of Θ(log1/ε) on the minimum deterministic communication complexity guaranteed to suffice for running some approximately revenue-maximizing mechanism, thereby completely resolving this problem. As a secondary result, we show that under standard economic assumptions on distributions, the above upper bound of <PERSON> and <PERSON><PERSON> [2013] can be strengthened to O(1/ε2).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188786"}, {"primary_key": "3512994", "vector": [], "sparse_vector": [], "title": "Non-malleable secret sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A number of works have focused on the setting where an adversary tampers with the shares of a secret sharing scheme. This includes literature on verifiable secret sharing, algebraic manipulation detection(AMD) codes, and, error correcting or detecting codes in general. In this work, we initiate a systematic study of what we call non-malleable secret sharing. Very roughly, the guarantee we seek is the following: the adversary may potentially tamper with all of the shares, and still, either the reconstruction procedure outputs the original secret, or, the original secret is \"destroyed\" and the reconstruction outputs a string which is completely \"unrelated\" to the original secret. Recent exciting work on non-malleable codes in the split-state model led to constructions which can be seen as 2-out-of-2 non-malleable secret sharing schemes. These constructions have already found a number of applications in cryptography. We investigate the natural question of constructing t-out-of-n non-malleable secret sharing schemes. Such a secret sharing scheme ensures that only a set consisting of t or more shares can reconstruct the secret, and, additionally guarantees non-malleability under an attack where potentially every share maybe tampered with. Techniques used for obtaining split-state non-malleable codes (or 2-out-of-2 non-malleable secret sharing) are (in some form) based on two-source extractors and seem not to generalize to our setting.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188872"}, {"primary_key": "3512995", "vector": [], "sparse_vector": [], "title": "Collusion resistant traitor tracing from learning with errors.", "authors": ["<PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "In this work we provide a traitor tracing construction with ciphertexts that grow polynomially in log(n) where n is the number of users and prove it secure under the Learning with Errors (LWE) assumption. This is the first traitor tracing scheme with such parameters provably secure from a standard assumption. In addition to achieving new traitor tracing results, we believe our techniques push forward the broader area of computing on encrypted data under standard assumptions. Notably, traitor tracing is substantially different problem from other cryptography primitives that have seen recent progress in LWE solutions.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188844"}, {"primary_key": "3512996", "vector": [], "sparse_vector": [], "title": "Synchronization strings: explicit constructions, local decoding, and applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper gives new results for synchronization strings, a powerful combinatorial object introduced by [<PERSON><PERSON><PERSON>, <PERSON><PERSON>; STOC'17] that allows to efficiently deal with insertions and deletions in various communication problems:", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188940"}, {"primary_key": "3512997", "vector": [], "sparse_vector": [], "title": "Mixture models, robustness, and sum of squares proofs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We use the Sum of Squares method to develop new efficient algorithms for learning well-separated mixtures of Gaussians and robust mean estimation, both in high dimensions, that substantially improve upon the statistical guarantees achieved by previous efficient algorithms. Our contributions are:", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188748"}, {"primary_key": "3512998", "vector": [], "sparse_vector": [], "title": "A tighter welfare guarantee for first-price auctions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proves that the welfare of the first price auction in Bayes-Nash equilibrium is at least a .743-fraction of the welfare of the optimal mechanism assuming agents' values are independently distributed. The previous best bound was 1−1/e≈.63, derived using smoothness, the standard technique for reasoning about welfare of games in equilibrium. In the worst known example, the first price auction achieves a ≈.869-fraction of the optimal welfare, far better than the theoretical guarantee. Despite this large gap, it was unclear whether the 1−1/e bound was tight. We prove that it is not. Our analysis eschews smoothness, and instead uses the independence assumption on agents' value distributions to give a more careful accounting of the welfare contribution of agents who win despite not having the highest value.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188944"}, {"primary_key": "3512999", "vector": [], "sparse_vector": [], "title": "On the complexity of hazard-free circuits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of constructing hazard-free Boolean circuits dates back to the 1940s and is an important problem in circuit design. Our main lower-bound result unconditionally shows the existence of functions whose circuit complexity is polynomially bounded while every hazard-free implementation is provably of exponential size. Previous lower bounds on the hazard-free complexity were only valid for depth 2 circuits. The same proof method yields that every subcubic implementation of Boolean matrix multiplication must have hazards. These results follow from a crucial structural insight: Hazard-free complexity is a natural generalization of monotone complexity to all (not necessarily monotone) Boolean functions. Thus, we can apply known monotone complexity lower bounds to find lower bounds on the hazard-free complexity. We also lift these methods from the monotone setting to prove exponential hazard-free complexity lower bounds for non-monotone functions.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188912"}, {"primary_key": "3513000", "vector": [], "sparse_vector": [], "title": "Online load balancing on related machines.", "authors": ["Sungjin Im", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider the problem of assigning jobs online to machines with non-uniform speeds (also called related machines) so to optimize a given norm of the machine loads. A long line of work, starting with the seminal work of <PERSON> in the 1960s, has led to tight competitive ratios for all ℓq norms for two scenarios: the special case of identical machines (uniform machine speeds) and the more general setting of unrelated machines (jobs have arbitrary processing times on machines). For non-uniform machine speeds, however, the only known result was a constant competitive competitive ratio for the makespan (ℓ∞) norm, via the so-called slowest-fit algorithm (Aspnes, Azar, Fiat, Plotkin, and Waarts, JACM '97). Our first result in this paper is to obtain the first constant-competitive algorithm for scheduling on related machines for any arbitrary ℓq norm.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188966"}, {"primary_key": "3513001", "vector": [], "sparse_vector": [], "title": "Near-optimal linear decision trees for k-SUM and related problems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct near optimal linear decision trees for a variety of decision problems in combinatorics and discrete geometry. For example, for any constant k, we construct linear decision trees that solve the k-SUM problem on n elements using O(n log2 n) linear queries. Moreover, the queries we use are comparison queries, which compare the sums of two k-subsets; when viewed as linear queries, comparison queries are 2k-sparse and have only {−1,0,1} coefficients. We give similar constructions for sorting sumsets A+B and for solving the SUBSET-SUM problem, both with optimal number of queries, up to poly-logarithmic terms.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188770"}, {"primary_key": "3513002", "vector": [], "sparse_vector": [], "title": "A simply exponential upper bound on the maximum number of stable matchings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Stable matching is a classical combinatorial problem that has been the subject of intense theoretical and empirical study since its introduction in 1962 in a seminal paper by <PERSON> and <PERSON>. In this paper, we provide a new upper bound on f(n), the maximum number of stable matchings that a stable matching instance with n men and n women can have. It has been a long-standing open problem to understand the asymptotic behavior of f(n) as n→∞, first posed by <PERSON> in the 1970s. Until now the best lower bound was approximately 2.28n, and the best upper bound was 2nlogn− O(n). In this paper, we show that for all n, f(n) ≤ cn for some universal constant c. This matches the lower bound up to the base of the exponent. Our proof is based on a reduction to counting the number of downsets of a family of posets that we call \"mixing\". The latter might be of independent interest.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188848"}, {"primary_key": "3513003", "vector": [], "sparse_vector": [], "title": "Construction of new local spectral high dimensional expanders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High dimensional expanders is a vibrant emerging field of study. Nevertheless, the only known construction of bounded degree high dimensional expanders is based on Ramanujan complexes, whereas one dimensional bounded degree expanders are abundant.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188782"}, {"primary_key": "3513004", "vector": [], "sparse_vector": [], "title": "At the roots of dictionary compression: string attractors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A well-known fact in the field of lossless text compression is that high-order entropy is a weak model when the input contains long repetitions. Motivated by this, decades of research have generated myriads of so-called dictionary compressors: algorithms able to reduce the text's size by exploiting its repetitiveness. Lempel-Ziv 77 is one of the most successful and well-known tools of this kind, followed by straight-line programs, run-length Burrows-Wheeler transform, macro schemes, collage systems, and the compact directed acyclic word graph. In this paper, we show that these techniques are different solutions to the same, elegant, combinatorial problem: to find a small set of positions capturing all text's substrings. We call such a set a string attractor. We first show reductions between dictionary compressors and string attractors. This gives the approximation ratios of dictionary compressors with respect to the smallest string attractor and uncovers new relations between the output sizes of different compressors. We show that the $k$-attractor problem: deciding whether a text has a size-$t$ set of positions capturing substrings of length at most $k$, is NP-complete for $k\\geq 3$. We provide several approximation techniques for the smallest $k$-attractor, show that the problem is APX-complete for constant $k$, and give strong inapproximability results. To conclude, we provide matching lower and upper bounds for the random access problem on string attractors. The upper bound is proved by showing a data structure supporting queries in optimal time. Our data structure is universal: by our reductions to string attractors, it supports random access on any dictionary-compression scheme. In particular, it matches the lower bound also on LZ77, straight-line programs, collage systems, and macro schemes, and therefore closes (at once) the random access problem for all these compressors.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188814"}, {"primary_key": "3513005", "vector": [], "sparse_vector": [], "title": "Sum-of-squares meets nash: lower bounds for finding any equilibrium.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Computing Nash equilibrium (NE) in two-player game is a central question in algorithmic game theory. The main motivation of this work is to understand the power of sum-of-squares method in computing equilibria, both exact and approximate. Previous works in this context have focused on hardness of approximating \"best\" equilibria with respect to some natural quality measure on equilibria such as social welfare. Such results, however, do not directly relate to the complexity of the problem of finding any equilibrium.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188892"}, {"primary_key": "3513006", "vector": [], "sparse_vector": [], "title": "Robust moment estimation and improved clustering via sum of squares.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop efficient algorithms for estimating low-degree moments of unknown distributions in the presence of adversarial outliers and design a new family of convex relaxations for k-means clustering based on sum-of-squares method. As an immediate corollary, for any γ > 0, we obtain an efficient algorithm for learning the means of a mixture of k arbitrary distributions in d in time dO(1/γ) so long as the means have separation Ω(kγ). This in particular yields an algorithm for learning Gaussian mixtures with separation Ω(kγ), thus partially resolving an open problem of <PERSON><PERSON> and Vijayaraghavan regev2017learning. The guarantees of our robust estimation algorithms improve in many cases significantly over the best previous ones, obtained in the recent works. We also show that the guarantees of our algorithms match information-theoretic lower-bounds for the class of distributions we consider. These improved guarantees allow us to give improved algorithms for independent component analysis and learning mixtures of Gaussians in the presence of outliers.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188970"}, {"primary_key": "3513007", "vector": [], "sparse_vector": [], "title": "Constant approximation for k-median and k-means with outliers via iterative rounding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present a new iterative rounding framework for many clustering problems. Using this, we obtain an (α1 + є ≤ 7.081 + є)-approximation algorithm for k-median with outliers, greatly improving upon the large implicit constant approximation ratio of <PERSON>. For k-means with outliers, we give an (α2+є ≤ 53.002 + є)-approximation, which is the first O(1)-approximation for this problem. The iterative algorithm framework is very versatile; we show how it can be used to give α1- and (α1 + є)-approximation algorithms for matroid and knapsack median problems respectively, improving upon the previous best approximations ratios of 8 due to <PERSON><PERSON> and 17.46 due to <PERSON><PERSON> et al. The natural LP relaxation for the k-median/k-means with outliers problem has an unbounded integrality gap. In spite of this negative result, our iterative rounding framework shows that we can round an LP solution to an almost-integral solution of small cost, in which we have at most two fractionally open facilities. Thus, the LP integrality gap arises due to the gap between almost-integral and fully-integral solutions. Then, using a pre-processing procedure, we show how to convert an almost-integral solution to a fully-integral solution losing only a constant-factor in the approximation ratio. By further using a sparsification technique, the additive factor loss incurred by the conversion can be reduced to any є > 0.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188882"}, {"primary_key": "3513008", "vector": [], "sparse_vector": [], "title": "The <PERSON><PERSON> problem, continuous operator scaling, and smoothed analysis.", "authors": ["Tsz Chiu Kwok", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The <PERSON>sen problem is a basic open problem in operator theory: Given vectors u1, …, un ∈ ℝd that are є-nearly satisfying the <PERSON><PERSON><PERSON>'s condition and the equal norm condition, is it close to a set of vectors v1, …, vn ∈ ℝd that exactly satisfy the <PERSON><PERSON><PERSON>'s condition and the equal norm condition? Given u1, …, un, the squared distance (to the set of exact solutions) is defined as infv ∑i=1n || ui − vi ||22 where the infimum is over the set of exact solutions. Previous results show that the squared distance of any є-nearly solution is at most O(poly(d,n,є)) and there are є-nearly solutions with squared distance at least Ω(d є). The fundamental open question is whether the squared distance can be independent of the number of vectors n.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188794"}, {"primary_key": "3513009", "vector": [], "sparse_vector": [], "title": "Incomplete nested dissection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present an asymptotically faster algorithm for solving linear systems in well-structured 3-dimensional truss stiffness matrices. These linear systems arise from linear elasticity problems, and can be viewed as extensions of graph Laplacians into higher dimensions. Faster solvers for the 2-D variants of such systems have been studied using generalizations of tools for solving graph Laplacians [<PERSON><PERSON><PERSON><PERSON><PERSON>lman CSC'07, Shklarski-Toledo SIMAX'08].", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188960"}, {"primary_key": "3513010", "vector": [], "sparse_vector": [], "title": "Crossing the logarithmic barrier for dynamic Boolean data structure lower bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu"], "summary": "This paper proves the first super-logarithmic lower bounds on the cell probe complexity of dynamic boolean (a.k.a. decision) data structure problems, a long-standing milestone in data structure lower bounds. We introduce a new approach and use it to prove a Ω (lg 1.5 n) lower bound on the operational time of a wide range of boolean data structure problems, most notably, on the query time of dynamic range counting over F 2 (<PERSON><PERSON><PERSON>, 2007). Proving an (lg n) lower bound for this problem was explicitly posed as one of five important open problems in the late <PERSON><PERSON>’s obituary (<PERSON><PERSON>, 2013). This result also implies the first (lg n) lower bound for the classical 2D range counting problem, one of the most fundamental data structure problems in computational geometry and spatial databases. We derive similar lower bounds for boolean versions of dynamic polynomial evaluation and 2D rectangle stabbing, and for the (non-boolean) problems of range selection and range median. Our technical centerpiece is a new way of “weakly\" simulating dynamic data structures using efficient one-way communication protocols with small advantage over random guessing. This simulation involves a surprising excursion to low-degree (Chebyshev) polynomials which May be of independent interest, and offers an entirely new algorithmic angle on the “cell sampling\" method of <PERSON><PERSON><PERSON><PERSON> et al. (2010).", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188790"}, {"primary_key": "3513011", "vector": [], "sparse_vector": [], "title": "Convergence rate of riemannian Hamiltonian <PERSON> and faster polytope volume computation.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "We give the first rigorous proof of the convergence of Riemannian Hamiltonian Monte <PERSON>, a general (and practical) method for sampling Gibbs distributions. Our analysis shows that the rate of convergence is bounded in terms of natural smoothness parameters of an associated Riemannian manifold. We then apply the method with the manifold defined by the log barrier function to the problems of (1) uniformly sampling a polytope and (2) computing its volume, the latter by extending Gaussian cooling to the manifold setting. In both cases, the total number of steps needed is O*(mn2/3), improving the state of the art. A key ingredient of our analysis is a proof of an analog of the KLS conjecture for Gibbs distributions over manifolds.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188774"}, {"primary_key": "3513012", "vector": [], "sparse_vector": [], "title": "Stochastic localization + <PERSON><PERSON><PERSON><PERSON><PERSON> barrier = tight bound for log-So<PERSON>ev.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "Logarithmic Sobolev inequalities are a powerful way to estimate the rate of convergence of Markov chains and to derive concentration inequalities on distributions. We prove that the log-Sobolev constant of any isotropic logconcave density in Rn with support of diameter D is Ω(1/D), resolving a question posed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 1997. This is asymptotically the best possible estimate and improves on the previous bound of Ω(1/D2) by Kannan-Lovász-Montenegro. It follows that for any isotropic logconcave density, the ball walk with step size δ=Θ(1/√n) mixes in O*(n2D) proper steps from any starting point. This improves on the previous best bound of O*(n2D2) and is also asymptotically tight. The new bound leads to the following refined large deviation inequality for an L-Lipschitz function g over an isotropic logconcave density p: for any t>0, [complex formula not displayed]", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188866"}, {"primary_key": "3513013", "vector": [], "sparse_vector": [], "title": "Capacity approaching coding for low noise interactive quantum communication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the problem of implementing two-party interactive quantum communication over noisy channels, a necessary endeavor if we wish to fully reap quantum advantages for communication. For an arbitrary protocol with n messages, designed for noiseless qudit channels (where d is arbitrary), our main result is a simulation method that fails with probability less than 2−Θ (nє) and uses a qudit channel n (1 + Θ (√є)) times, of which an є fraction can be corrupted adversarially. The simulation is thus capacity achieving to leading order, and we conjecture that it is optimal up to a constant factor in the √є term. Furthermore, the simulation is in a model that does not require pre-shared resources such as randomness or entanglement between the communicating parties. Perhaps surprisingly, this outperforms the best known overhead of 1 + O(√є loglog1/є) in the corresponding classical model, which is also conjectured to be optimal [<PERSON><PERSON><PERSON>, FOCS'14]. Our work also improves over the best previously known quantum result where the overhead is a non-explicit large constant [<PERSON> et al., FOCS'14] for low є.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188908"}, {"primary_key": "3513014", "vector": [], "sparse_vector": [], "title": "Distribution-free junta testing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of testing whether an unknown n-variable Boolean function is a k-junta in the distribution-free property testing model, where the distance between functions is measured with respect to an arbitrary and unknown probability distribution over {0,1}n. Our first main result is that distribution-free k-junta testing can be performed, with one-sided error, by an adaptive algorithm that uses Õ(k2)/є queries (independent of n). Complementing this, our second main result is a lower bound showing that any non-adaptive distribution-free k-junta testing algorithm must make Ω(2k/3) queries even to test to accuracy є=1/3. These bounds establish that while the optimal query complexity of non-adaptive k-junta testing is 2Θ(k), for adaptive testing it is poly(k), and thus show that adaptivity provides an exponential improvement in the distribution-free query complexity of testing juntas.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188842"}, {"primary_key": "3513015", "vector": [], "sparse_vector": [], "title": "Breaking the circuit-size barrier in secret sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study secret sharing schemes for general (non-threshold) access structures. A general secret sharing scheme for n parties is associated to a monotone function F:{0,1}n→{0,1}. In such a scheme, a dealer distributes shares of a secret s among n parties. Any subset of parties T ⊆ [n] should be able to put together their shares and reconstruct the secret s if F(T)=1, and should have no information about s if F(T)=0. One of the major long-standing questions in information-theoretic cryptography is to minimize the (total) size of the shares in a secret-sharing scheme for arbitrary monotone functions F.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188936"}, {"primary_key": "3513016", "vector": [], "sparse_vector": [], "title": "Dynamic matching in school choice: efficient seat reassignment after late cancellations (invited talk).", "authors": ["<PERSON>"], "summary": "In the school choice market, where scarce public school seats are assigned to students, a key issue is how to reassign seats that are vacated after an initial round of centralized assignment. Every year around 10% of students assigned a seat in the NYC public high school system eventually do not use it, and their vacated seats can be reassigned. Practical solutions to the reassignment problem must be simple to implement, truthful and efficient. I propose and axiomatically justify a class of reassignment mechanisms, the Per- muted Lottery Deferred Acceptance (PLDA) mechanisms, which generalize the commonly used Deferred Acceptance (DA) school choice mechanism to a two-round setting and retain its desirable in- centive and efficiency properties. I also provide guidance to school districts as to how to choose the appropriate mechanism in this class for their setting. Centralized admissions are typically conducted in a single round using Deferred Acceptance, with a lottery used to break ties in each school's prioritization of students. Our proposed PLDA mechanisms reassign vacated seats using a second round of DA with a lottery based on a suitable permutation of the first-round lottery numbers. I demonstrate that under a natural order condition on aggregate student demand for schools, the second-round tie-breaking lottery can be correlated arbitrarily with that of the first round without affecting allocative welfare. I also show how the identifying char- acteristic of PLDA mechanisms, their permutation, can be chosen to control reallocation. vacated after the initial round are reassigned using decentralized waitlists that create significant student movement after the start of the school year, which is costly for both students and schools. I show that reversing the lottery order between rounds minimizes reassignment among all PLDA mechanisms, allowing us to alleviate costly student movement between schools without affecting the ef- ficiency of the final allocation. In a setting without school priorities, I also characterize PLDA mechanisms as the class of mechanisms that provide students with a guarantee at their first-round assign- ment, respect school priorities, and are strategy-proof, constrained Pareto efficient, and satisfy some mild symmetry properties. Finally, I provide simulations of the performance of different PLDA mecha- nisms in the presence of school priorities. All simulated PLDAs have similar allocative efficiency, while the PLDA based on reversing the tie-breaking lottery between rounds minimizes the number of reassigned students. These results support our theoretical findings. This is based on joint work with Itai Feigenbaum, Yash Kanoria, and Jay Sethuraman.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3232193"}, {"primary_key": "3513017", "vector": [], "sparse_vector": [], "title": "The minimum euclidean-norm point in a convex polytope: Wolfe&apos;s combinatorial algorithm is exponential.", "authors": ["Jesús A. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The complexity of <PERSON>'s method for the minimum Euclidean-norm point problem over a convex polytope has remained unknown since he proposed the method in 1974. We present the first example that <PERSON>'s method takes exponential time. Additionally, we improve previous results to show that linear programming reduces in strongly-polynomial time to the minimum norm point problem over a simplex", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188820"}, {"primary_key": "3513018", "vector": [], "sparse_vector": [], "title": "Stochastic bandits robust to adversarial corruptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new model of stochastic bandits with adversarial corruptions which aims to capture settings where most of the input follows a stochastic pattern but some fraction of it can be adversarially changed to trick the algorithm, e.g., click fraud, fake reviews and email spam. The goal of this model is to encourage the design of bandit algorithms that (i) work well in mixed adversarial and stochastic models, and (ii) whose performance deteriorates gracefully as we move from fully stochastic to fully adversarial models.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188918"}, {"primary_key": "3513019", "vector": [], "sparse_vector": [], "title": "Generalization and equilibrium in generative adversarial nets (GANs) (invited talk).", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Generative Adversarial Networks (GANs) have become one of the dominant methods for fitting generative models to complicated real-life data, and even found unusual uses such as designing good cryptographic primitives. In this talk, we will first introduce the ba- sics of GANs and then discuss the fundamental statistical question about GANs — assuming the training can succeed with polynomial samples, can we have any statistical guarantees for the estimated distributions? In the work with <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, we suggested a dilemma: powerful discriminators cause overfitting, whereas weak discriminators cannot detect mode collapse. Such a conundrum may be solved or alleviated by designing discrimina- tor class with strong distinguishing power against the particular generator class (instead of against all possible generators.)", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3232194"}, {"primary_key": "3513020", "vector": [], "sparse_vector": [], "title": "Nonlinear dimension reduction via outer Bi-Lipschitz extensions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ilya <PERSON>"], "summary": "We introduce and study the notion of *an outer bi-Lipschitz extension* of a map between Euclidean spaces. The notion is a natural analogue of the notion of *a Lipschitz extension* of a Lipschitz map. We show that for every map f there exists an outer bi-Lipschitz extension f′ whose distortion is greater than that of f by at most a constant factor. This result can be seen as a counterpart of the classic <PERSON><PERSON><PERSON><PERSON><PERSON> theorem for outer bi-Lipschitz extensions. We also study outer bi-Lipschitz extensions of near-isometric maps and show upper and lower bounds for them. Then, we present applications of our results to prioritized and terminal dimension reduction problems, described next.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188828"}, {"primary_key": "3513021", "vector": [], "sparse_vector": [], "title": "Circuit lower bounds for nondeterministic quasi-polytime: an easy witness lemma for NP and NQP.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that if every problem in NP has nk-size circuits for a fixed constant k, then for every NP-verifier and every yes-instance x of length n for that verifier, the verifier's search space has an nO(k3)-size witness circuit: a witness for x that can be encoded with a circuit of only nO(k3) size. An analogous statement is proved for nondeterministic quasi-polynomial time, i.e., NQP = NTIME[nlogO(1) n]. This significantly extends the Easy Witness Lemma of Impagliazzo, Kabanets, and Wigderson [JCSS'02] which only held for larger nondeterministic classes such as NEXP.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188910"}, {"primary_key": "3513022", "vector": [], "sparse_vector": [], "title": "Sparse Kneser graphs are Hamiltonian.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For integers k≥1 and n≥2k+1, the Kneser graph K(n,k) is the graph whose vertices are the k-element subsets of {1,…,n} and whose edges connect pairs of subsets that are disjoint. The Kneser graphs of the form K(2k+1,k) are also known as the odd graphs. We settle an old problem due to <PERSON>, <PERSON>, and <PERSON> from the 1970s, proving that for every k≥3, the odd graph K(2k+1,k) has a Hamilton cycle. This and a known conditional result due to <PERSON> imply that all Kneser graphs of the form K(2k+2a,k) with k≥3 and a≥0 have a Hamilton cycle. We also prove that K(2k+1,k) has at least 22k−6 distinct Hamilton cycles for k≥6. Our proofs are based on a reduction of the Hamiltonicity problem in the odd graph to the problem of finding a spanning tree in a suitably defined hypergraph on <PERSON><PERSON><PERSON> words.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188834"}, {"primary_key": "3513023", "vector": [], "sparse_vector": [], "title": "An exponential lower bound for individualization-refinement algorithms for graph isomorphism.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The individualization-refinement paradigm provides a strong toolbox for testing isomorphism of two graphs and indeed, the currently fastest implementations of isomorphism solvers all follow this approach. While these solvers are fast in practice, from a theoretical point of view, no general lower bounds concerning the worst case complexity of these tools are known. In fact, it is an open question what the running time of individualization-refinement algorithms is. For all we know some of the algorithms could have polynomial running time.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188900"}, {"primary_key": "3513024", "vector": [], "sparse_vector": [], "title": "Shape of diffusion and size of monochromatic region of a two-dimensional spin system.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider an agent-based distributed algorithm with exponentially distributed waiting times in which agents with binary states interact locally over a geometric graph, and based on this interaction and on the value of a common intolerance threshold τ, decide whether to change their states. This model is equivalent to an Asynchronous Cellular Automaton (ACA) with extended Moore neighborhoods, a zero-temperature Ising model with Glauber dynamics, or a Schelling model of self-organized segregation in an open system, and has applications in the analysis of social and biological networks, and spin glasses systems.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188836"}, {"primary_key": "3513025", "vector": [], "sparse_vector": [], "title": "The query complexity of graph isomorphism: bypassing distribution testing lower bounds.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the query complexity of graph isomorphism in the property testing model for dense graphs. We give an algorithm that makes n1+o(1) queries, improving on the previous best bound of Õ(n5/4). Since the problem is known to require Ω(n) queries, our algorithm is optimal up to a subpolynomial factor.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188952"}, {"primary_key": "3513026", "vector": [], "sparse_vector": [], "title": "Lifting nullstellensatz to monotone span programs over any field.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We characterize the size of monotone span programs computing certain \"structured\" boolean functions by the Null<PERSON>llen<PERSON>z degree of a related unsatisfiable Boolean formula. This yields the first exponential lower bounds for monotone span programs over arbitrary fields, the first exponential separations between monotone span programs over fields of different characteristic, and the first exponential separation between monotone span programs over arbitrary fields and monotone circuits. We also show tight quasipolynomial lower bounds on monotone span programs computing directed st-connectivity over arbitrary fields, separating monotone span programs from non-deterministic logspace and also separating monotone and non-monotone span programs over GF(2). Our results yield the same lower bounds for linear secret sharing schemes due to the previously known relationship between monotone span programs and linear secret sharing. To prove our characterization we introduce a new and general tool for lifting polynomial degree to rank over arbitrary fields.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188914"}, {"primary_key": "3513027", "vector": [], "sparse_vector": [], "title": "Hardness of approximate nearest neighbor search.", "authors": ["<PERSON>via<PERSON>"], "summary": "We prove conditional near-quadratic running time lower bounds for approximate Bichromatic Closest Pair with Euclidean, Manhattan, Hamming, or edit distance. Specifically, unless the Strong Exponential Time Hypothesis (SETH) is false, for every δ>0 there exists a constant ε>0 such that computing a (1+ε)-approximation to the Bichromatic Closest Pair requires Ω(n2−δ) time. In particular, this implies a near-linear query time for Approximate Nearest Neighbor search with polynomial preprocessing time.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188916"}, {"primary_key": "3513028", "vector": [], "sparse_vector": [], "title": "On the parameterized complexity of approximating dominating set.", "authors": ["<PERSON><PERSON><PERSON> C. S.", "Bundit Laekhanukit", "<PERSON><PERSON>"], "summary": "We study the parameterized complexity of approximating the k-Dominating Set (domset) problem where an integer k and a graph G on n vertices are given as input, and the goal is to find a dominating set of size at most F(k) · k whenever the graph G has a dominating set of size k. When such an algorithm runs in time T(k)poly(n) (i.e., FPT-time) for some computable function T, it is said to be an F(k)-FPT-approximation algorithm for k-domset. Whether such an algorithm exists is listed in the seminal book of <PERSON><PERSON> and <PERSON> (2013) as one of the \"most infamous\" open problems in Parameterized Complexity. This work gives an almost complete answer to this question by showing the non-existence of such an algorithm under W[1]≠FPT and further providing tighter running time lower bounds under stronger hypotheses. Specifically, we prove the following for every computable functions T, F and every constant ε > 0:", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188896"}, {"primary_key": "3513029", "vector": [], "sparse_vector": [], "title": "An almost-linear time algorithm for uniform random spanning tree generation.", "authors": ["<PERSON>"], "summary": "We give an m1+o(1)βo(1)-time algorithm for generating uniformly random spanning trees in weighted graphs with max-to-min weight ratio β. In the process, we illustrate how fundamental tradeoffs in graph partitioning can be overcome by eliminating vertices from a graph using <PERSON>hur complements of the associated Laplacian matrix.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188852"}, {"primary_key": "3513030", "vector": [], "sparse_vector": [], "title": "Prediction with a short memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of predicting the next observation given a sequence of past observations, and consider the extent to which accurate prediction requires complex algorithms that explicitly leverage long-range dependencies. Perhaps surprisingly, our positive results show that for a broad class of sequences, there is an algorithm that predicts well on average, and bases its predictions only on the most recent few observation together with a set of simple summary statistics of the past observations. Specifically, we show that for any distribution over observations, if the mutual information between past observations and future observations is upper bounded by I, then a simple Markov model over the most recent I/є observations obtains expected KL error є—and hence ℓ1 error √є—with respect to the optimal predictor that has access to the entire past and knows the data generating distribution. For a Hidden Markov Model with n hidden states, I is bounded by logn, a quantity that does not depend on the mixing time, and we show that the trivial prediction algorithm based on the empirical frequencies of length O(logn/є) windows of observations achieves this error, provided the length of the sequence is dΩ(logn/є), where d is the size of the observation alphabet.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188954"}, {"primary_key": "3513031", "vector": [], "sparse_vector": [], "title": "Algorithmic polynomials.", "authors": ["<PERSON>"], "summary": "The approximate degree of a Boolean function f(x1,x2,…,xn) is the minimum degree of a real polynomial that approximates f pointwise within 1/3. Upper bounds on approximate degree have a variety of applications in learning theory, differential privacy, and algorithm design in general. Nearly all known upper bounds on approximate degree arise in an existential manner from bounds on quantum query complexity.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188958"}, {"primary_key": "3513032", "vector": [], "sparse_vector": [], "title": "Tight query complexity lower bounds for PCA via finite sample deformed wigner law.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove a query complexity lower bound for approximating the top r dimensional eigenspace of a matrix. We consider an oracle model where, given a symmetric matrix M ∈ ℝd × d, an algorithm Alg is allowed to make T exact queries of the form w(i) = M v(i) for i in {1,...,T}, where v(i) is drawn from a distribution which depends arbitrarily on the past queries and measurements {v(j),w(i)}1 ≤ j ≤ i−1. We show that for every gap ∈ (0,1/2], there exists a distribution over matrices M for which 1) gapr(M) = Ω(gap) (where gapr(M) is the normalized gap between the r and r+1-st largest-magnitude eigenvector of M), and 2) any Alg which takes fewer than const × r logd/√gap queries fails (with overwhelming probability) to identity a matrix V ∈ ℝd × r with orthonormal columns for which ⟨ V, M V⟩ ≥ (1 − const × gap)∑i=1r λi(M). Our bound requires only that d is a small polynomial in 1/gap and r, and matches the upper bounds of Musco and Musco '15. Moreover, it establishes a strict separation between convex optimization and \"strict-saddle\" non-convex optimization of which PCA is a canonical example: in the former, first-order methods can have dimension-free iteration complexity, whereas in PCA, the iteration complexity of gradient-based methods must necessarily grow with the dimension.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188796"}, {"primary_key": "3513033", "vector": [], "sparse_vector": [], "title": "A constant-factor approximation algorithm for the asymmetric traveling salesman problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "László A. <PERSON>"], "summary": "We give a constant-factor approximation algorithm for the asymmetric traveling salesman problem. Our approximation guarantee is analyzed with respect to the standard LP relaxation, and thus our result confirms the conjectured constant integrality gap of that relaxation.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188824"}, {"primary_key": "3513034", "vector": [], "sparse_vector": [], "title": "Quantified derandomization of linear threshold circuits.", "authors": ["<PERSON><PERSON>"], "summary": "One of the prominent current challenges in complexity theory is the attempt to prove lower bounds for TC0, the class of constant-depth, polynomial-size circuits with majority gates. Relying on the results of <PERSON> (2013), an appealing approach to prove such lower bounds is to construct a non-trivial derandomization algorithm for TC0. In this work we take a first step towards the latter goal, by proving the first positive results regarding the derandomization of TC0 circuits of depth d>2.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3188745.3188822"}, {"primary_key": "3531379", "vector": [], "sparse_vector": [], "title": "Proceedings of the 50th Annual ACM SIGACT Symposium on Theory of Computing, STOC 2018, Los Angeles, CA, USA, June 25-29, 2018", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": ""}]