[{"primary_key": "1771778", "vector": [], "sparse_vector": [], "title": "Beyond Visuals: Examining the Experiences of Geoscience Professionals With Vision Disabilities in Accessing Data Visualizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Tiffany Fourment", "<PERSON>", "Marika M. Holland", "<PERSON>"], "summary": "Data visualizations are ubiquitous in all disciplines and have become the primary means of analysing data and communicating insights. However, the predominant reliance on visual encoding of data con-tinues to create accessibility barriers for people who are blind/vision impaired resulting in their under representation in Science, Tech-nology, Engineering and Mathematics (STEM) disciplines. This research study seeks to understand the experiences of professionals who are blind/vision impaired in one such STEM discipline (geo-sciences) in accessing data visualizations. In-depth, semi-structured interviews with seven professionals were conducted to examine the accessibility barriers and areas for improvement to inform acces-sibility research pertaining to data visualizations through a socio-technical lens. A reflexive thematic analysis revealed the negative impact of visualizations in influencing their career path, lack of data exploration tools for research, barriers in accessing works of peers and mismatched pace of visualization and accessibility research. The article also includes recommendations from the participants to address some of these accessibility barriers.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00041"}, {"primary_key": "1771792", "vector": [], "sparse_vector": [], "title": "Visual Auditor: Interactive Visualization for Detection and Summarization of Model Biases.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As machine learning (ML) systems become increasingly widespread, it is necessary to audit these systems for biases prior to their de-ployment. Recent research has developed algorithms for effectively identifying intersectional bias in the form of interpretable, underper-forming subsets (or slices) of the data. However, these solutions and their insights are limited without a tool for visually understanding and interacting with the results of these algorithms. We propose Visual Auditor, an interactive visualization tool for auditing and summarizing model biases. Visual Auditor assists model validation by providing an interpretable overview of intersectional bias (bias that is present when examining populations defined by multiple features), details about relationships between problematic data slices, and a comparison between underperforming and overper-forming data slices in a model. Our open-source tool runs directly in both computational notebooks and web browsers, making model auditing accessible and easily integrated into current ML development workflows. An observational user study in collaboration with domain experts at Fiddler AI highlights that our tool can help ML practitioners identify and understand model biases.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00018"}, {"primary_key": "1771793", "vector": [], "sparse_vector": [], "title": "Who benefits from Visualization Adaptations? Towards a better Understanding of the Influence of Visualization Literacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ability to read, understand, and comprehend visual information representations is subsumed under the term visualization literacy (VL). One possibility to improve the use of information visualizations is to introduce adaptations. However, it is yet unclear whether people with different VL benefit from adaptations to the same degree. We conducted an online experiment (n = 42) to investigate whether the effect of an adaptation (here: De-Emphasis) of visualizations (bar charts, scatter plots) on performance (accuracy, time) and user experiences depends on users' VL level. Using linear mixed models for the analyses, we found a positive impact of the De-Emphasis adaptation across all conditions, as well as an interaction effect of adaptation and VL on the task completion time for bar charts. This work contributes to a better understanding of the intertwined relationship of VL and visual adaptations and motivates future research.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00027"}, {"primary_key": "1771770", "vector": [], "sparse_vector": [], "title": "Explaining Website Reliability by Visualizing Hyperlink Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haekyu Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As the information on the Internet continues growing exponentially, understanding and assessing the reliability of a website is becoming increasingly important. Misinformation has far-ranging repercussions, from sowing mistrust in media to undermining democratic elections. While some research investigates how to alert people to misinformation on the web, much less research has been conducted on explaining how websites engage in spreading false information. To fill the research gap, we present MisVis, a web-based interactive visualization tool that helps users assess a website's reliability by understanding how it engages in spreading false information on the World Wide Web. MisVis visualizes the hyperlink connectivity of the website and summarizes key characteristics of the Twitter accounts that mention the site. A large-scale user study with 139 participants demonstrates that MisVis facilitates users to assess and understand false information on the web and node-link diagrams can be used to communicate with non-experts. MisVis is available at the public demo link: https://poloclub.github.io/MisVis.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00014"}, {"primary_key": "1771772", "vector": [], "sparse_vector": [], "title": "Volume Puzzle: visual analysis of segmented volume data with multivariate attributes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A variety of application domains, including material science, neuroscience, and connectomics, commonly use segmented volume data for exploratory visual analysis. In many cases, segmented objects are characterized by multivariate attributes expressing specific geometric or physical features. Objects with similar characteristics, determined by selected attribute configurations, can create peculiar spatial patterns, whose detection and study is of fundamental importance. This task is notoriously difficult, especially when the number of attributes per segment is large. In this work, we propose an interactive framework that combines a state-of-the-art direct volume renderer for categorical volumes with techniques for the analysis of the attribute space and for the automatic creation of 2D transfer function. We show, in particular, how dimensionality reduction, kernel-density estimation, and topological techniques such as Morse analysis combined with scatter and density plots allow the efficient design of two-dimensional color maps that highlight spatial patterns. The capabilities of our framework are demonstrated on synthetic and real-world data from several domains.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00035"}, {"primary_key": "1771773", "vector": [], "sparse_vector": [], "title": "Visualizing Rule-based Classifiers for Clinical Risk Prognosis.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Deteriorating conditions in hospital patients are a major factor in clinical patient mortality. Currently, timely detection is based on clinical experience, expertise, and attention. However, healthcare trends towards larger patient cohorts, more data, and the desire for better and more personalized care are pushing the existing, simple scoring systems to their limits. Data-driven approaches can extract decision rules from available medical coding data, which offer good interpretability and thus are key for successful adoption in practice. Before deployment, models need to be scrutinized by domain experts to identify errors and check them against existing medical knowledge. We propose a visual analytics system to support health-care professionals in inspecting and enhancing rule-based classifier through identification of similarities and contradictions, as well as modification of rules. This work was developed iteratively in close collaboration with medical professionals. We discuss how our tool supports the inspection and assessment of rule-based classifiers in the clinical coding domain and propose possible extensions.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00020"}, {"primary_key": "1771774", "vector": [], "sparse_vector": [], "title": "Streamlining Visualization Authoring in D3 Through User-Driven Templates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Favour Nerrise", "<PERSON><PERSON><PERSON>", "Leilani Battle"], "summary": "D3 is arguably the most popular tool for implementing web-based visualizations. Yet D3 has a steep learning curve that may hinder its adoption and continued use. To simplify the process of programming D3 visualizations, we must first understand the space of implementation practices that D3 users engage in. We present a qualitative analysis of 2500 D3 visualizations and their corresponding imple-mentations. We find that 5 visualization types (Bar Charts, Geomaps, Line Charts, Scatterplots, and Force Directed Graphs) account for 80% of D3 visualizations found in our corpus. While implementation styles vary slightly across designs, the underlying code structure for all visualization types remains the same; presenting an opportunity for code reuse. Using our corpus of D3 examples, we synthesize reusable code templates for eight popular D3 visualization types and share them in our open source repository. Based on our results, we discuss design considerations for leveraging users' implementation patterns to reduce visualization design effort through design templates and auto-generated code recommendations.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00012"}, {"primary_key": "1771776", "vector": [], "sparse_vector": [], "title": "VisQuiz: Exploring Feedback Mechanisms to Improve Graphical Perception.", "authors": ["<PERSON>", "Mad<PERSON><PERSON> Caten", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we explore the design and evaluation of feedback for graphical perception tasks, called VisQuiz. Using a quiz-like metaphor, we design feedback for a typical visualization comparison experiment, showing participants their answer alongside the correct answer in an animated sequence in each trial, as well as summary feedback at the end of trial sections. To evaluate VisQuiz, we conduct a between-subjects experiment, including three stages of 40 trials each with a control condition that included only summary feedback. Results from $n=80$ participants show that once participants started receiving trial feedback (Stage 2) they performed significantly better with bubble charts than those in the control condition. This effect carried over when feedback was removed (Stage 3). Results also suggest an overall trend of improved performance due to feedback. We discuss these findings in the context of other visualization literacy efforts, and possible future work at the intersection of visualization, feedback, and learning. Experiment data and analysis scripts are available at the following repository https://osf.io/jys5d/", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00028"}, {"primary_key": "1771777", "vector": [], "sparse_vector": [], "title": "Let&apos;s Get Personal: Exploring the Design of Personalized Visualizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Media outlets often publish visualizations that can be personalized based on users' demographics, such as location, race, and age. However, the design of such personalized visualizations remains under-explored. In this work, we contribute a design space analysis of 47 public-facing articles with personalized visualizations to understand how designers structure content, encourage exploration, and present insights. We find that articles often lack explicit exploration suggestions or instructions, data notices, and personalized visual insights. We then outline three trajectories for future research: (1) explore how users choose to personalize visualizations, (2) examine how exploration suggestions and examples impact user interaction, and (3) investigate how personalization influences user insights.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00026"}, {"primary_key": "1771780", "vector": [], "sparse_vector": [], "title": "Plotly-Resampler: Effective Visual Analytics for Large Time Series.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Visual analytics is arguably the most important step in getting acquainted with your data. This is especially the case for time series, as this data type is hard to describe and cannot be fully understood when using for example summary statistics. To realize effective time series visualization, four requirements have to be met; a tool should be (1) interactive, (2) scalable to millions of data points, (3) integrable in conventional data science environments, and (4) highly configurable. We observe that open source Python visualization toolkits empower data scientists in most visual analytics tasks, but lack the combination of scalability and interactivity to realize effective time series visualization. As a means to facilitate these requirements, we created Plotly-Resampler, an open source Python library. Plotly-Resampler is an add-on for Plotly's Python bindings, enhancing line chart scalability on top of an interactive toolkit by aggregating the underlying data depending on the current graph view. Plotly-Resampler is built to be snappy, as the reactivity of a tool qualitatively affects how analysts visually explore and analyze data. A benchmark task highlights how our toolkit scales better than alternatives in terms of number of samples and time series. Additionally, Plotly-Resampler's flexible data aggregation functionality paves the path towards researching novel aggregation techniques. Plotly-Resampler's integrability, together with its configurability, convenience, and high scalability, allows to effectively analyze high-frequency data in your day-to-day Python environment.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00013"}, {"primary_key": "1771781", "vector": [], "sparse_vector": [], "title": "ASEVis: Visual Exploration of Active System Ensembles to Define Characteristic Measures.", "authors": ["<PERSON> Evers", "<PERSON>", "<PERSON>"], "summary": "Simulation ensembles are a common tool in physics for understanding how a model outcome depends on input parameters. We analyze an active particle system, where each particle can use energy from its surroundings to propel itself. A multi-dimensional feature vector containing all particles' motion information can describe the whole system at each time step. The system's behavior strongly depends on input parameters like the propulsion mechanism of the particles. To understand how the time-varying behavior depends on the input parameters, it is necessary to introduce new measures to quantify the difference of the dynamics of the ensemble members. We propose a tool that supports the interactive visual analysis of time-varying feature-vector ensembles. A core component of our tool allows for the interactive definition and refinement of new measures that can then be used to understand the system's behavior and compare the ensemble members. Different visualizations support the user in finding a characteristic measure for the system. By visualizing the user-defined measure, the user can then investigate the parameter dependencies and gain insights into the relationship between input parameters and simulation output.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00039"}, {"primary_key": "1771785", "vector": [], "sparse_vector": [], "title": "VegaFusion: Automatic Server-Side Scaling for Interactive Vega Visualizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Vega grammar has been broadly adopted by a growing ecosys-tem of browser-based visualization tools. However, the reference Vega renderer does not scale well to large datasets (e.g., millions of rows or hundreds of megabytes) because it requires the entire dataset to be loaded into browser memory. We introduce VegaFusion, which brings automatic server-side scaling to the Vega ecosystem. VegaFusion accepts generic Vega specifications and partitions the required computation between the client and an out-of-browser, natively-compiled server-side process. Large datasets can be pro-cessed server-side to avoid loading them into the browser and to take advantage of multi-threading, more powerful server hardware and caching. We demonstrate how VegaFusion can be integrated into the existing Vega ecosystem, and show that VegaFusion greatly outperforms the reference implementation. We demonstrate these benefits with VegaFusion running on the same machine as the client as well as on a remote machine.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00011"}, {"primary_key": "1771786", "vector": [], "sparse_vector": [], "title": "RMExplorer: A Visual Analytics Approach to Explore the Performance and the Fairness of Disease Risk Models on Population Subgroups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Disease risk models can identify high-risk patients and help clinicians provide more personalized care. However, risk models de-veloped on one dataset may not generalize across diverse subpop-ulations of patients in different datasets and may have unexpected performance. It is challenging for clinical researchers to inspect risk models across different subgroups without any tools. Therefore, we developed an interactive visualization system called RMExplorer (Risk Model Explorer) to enable interactive risk model assessment. Specifically, the system allows users to define subgroups of patients by selecting clinical, demographic, or other characteristics, to ex-plore the performance and fairness of risk models on the subgroups, and to understand the feature contributions to risk scores. To demonstrate the usefulness of the tool, we conduct a case study, where we use RMExplorer to explore three atrial fibrillation risk models by applying them to the UK Biobank dataset of 445,329 individuals. RMExplorer can help researchers to evaluate the performance and biases of risk models on subpopulations of interest in their data.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00019"}, {"primary_key": "1771789", "vector": [], "sparse_vector": [], "title": "LineCap: Line Charts for Data Visualization Captioning Models.", "authors": ["<PERSON>", "Zona Kostic", "<PERSON>"], "summary": "Data visualization captions help readers understand the purpose of a visualization and are crucial for individuals with visual impairments. The prevalence of poor figure captions and the successful application of deep learning approaches to image captioning motivate the use of similar techniques for automated figure captioning. However, research in this field has been stunted by the lack of suitable datasets. We introduce LineCap, a novel figure captioning dataset of 3,528 figures, and we provide insights from curating this dataset and using end-to-end deep learning models for automated figure captioning.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00016"}, {"primary_key": "1771790", "vector": [], "sparse_vector": [], "title": "Facilitating Conversational Interaction in Natural Language Interfaces for Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Natural language (NL) toolkits enable visualization developers, who may not have a background in natural language processing (NLP), to create natural language interfaces (NLIs) for end-users to flexibly specify and interact with visualizations. However, these toolkits currently only support one-off utterances, with minimal capability to facilitate a multi-turn dialog between the user and the system. Developing NLIs with such conversational interaction capabilities remains a challenging task, requiring implementations of low-level NLP techniques to process a new query as an intent to follow-up on an older query. We extend an existing Python-based toolkit, NL4DV, that processes an NL query about a tabular dataset and returns an analytic specification containing data attributes, analytic tasks, and relevant visualizations, modeled as a JSON object. Specifically, NL4DV now enables developers to facilitate multiple simultaneous conversations about a dataset and resolve associated ambiguities, augmenting new conversational information into the output JSON object. We demonstrate these capabilities through three examples: (1) an NLI to learn aspects of the Vega-Lite grammar, (2) a mind mapping application to create free-flowing conversations, and (3) a chatbot to answer questions and resolve ambiguities.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00010"}, {"primary_key": "1771791", "vector": [], "sparse_vector": [], "title": "Guided Data Discovery in Interactive Visualizations via Active Search.", "authors": ["<PERSON><PERSON>", "Sunwoo Ha", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in visual analytics have enabled us to learn from user interactions and uncover analytic goals. These innovations set the foundation for actively guiding users during data exploration. Providing such guidance will become more critical as datasets grow in size and complexity, precluding exhaustive investigation. Mean-while, the machine learning community also struggles with datasets growing in size and complexity, precluding exhaustive labeling. Active learning is a broad family of algorithms developed for actively guiding models during training. We will consider the intersection of these analogous research thrusts. First, we discuss the nuances of matching the choice of an active learning algorithm to the task at hand. This is critical for performance, a fact we demonstrate in a simulation study. We then present results of a user study for the particular task of data discovery guided by an active learning algorithm specifically designed for this task.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00023"}, {"primary_key": "1771796", "vector": [], "sparse_vector": [], "title": "FairFuse: Interactive Visual Support for Fair Consensus Ranking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Mallak Alkhathlan", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fair consensus building combines the preferences of multiple rankers into a single consensus ranking, while ensuring any group defined by a protected attribute (such as race or gender) is not disadvantaged compared to other groups. Manually generating a fair consensus ranking is time-consuming and impractical- even for a fairly small number of candidates. While algorithmic approaches for auditing and generating fair consensus rankings have been developed, these have not been operationalized in interactive systems. To bridge this gap, we introduce FairFuse, a visualization system for generating, analyzing, and auditing fair consensus rankings. We construct a data model which includes base rankings entered by rankers, augmented with measures of group fairness, and algorithms for generating consensus rankings with varying degrees of fairness. We design novel visualizations that encode these measures in a parallel-coordinates style rank visualization, with interactions for generating and exploring fair consensus rankings. We describe use cases in which FairFuse supports a decision-maker in ranking scenarios in which fairness is important, and discuss emerging challenges for future efforts supporting fairness-oriented rank analysis. Code and demo videos available at https://osf.io/hd639/.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00022"}, {"primary_key": "1771798", "vector": [], "sparse_vector": [], "title": "Toward Systematic Considerations of Missingness in Visual Analytics.", "authors": ["Maoyuan Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Data-driven decision making has been a common task in today's big data era, from simple choices such as finding a fast way to drive home, to complex decisions on medical treatment. It is often supported by visual analytics. For various reasons (e.g., system failure, interrupted network, intentional information hiding, or bias), visual analytics for sensemaking of data involves missingness (e.g., data loss and incomplete analysis), which impacts human decisions. For example, missing data can cost a business millions of dollars, and failing to recognize key evidence can put an innocent person in jail. Being aware of missingness is critical to avoid such catastrophes. To fulfill this, as an initial step, we consider missingness in visual analytics from two aspects: data-centric and human-centric. The former emphasizes missingness in three data-related categories: data composition, data relationship, and data usage. The latter focuses on the human-perceived missingness at three levels: observed-level, inferred-level, and ignored-level. Based on them, we discuss possible roles of visualizations for handling missingness, and conclude our discussion with future research opportunities.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00031"}, {"primary_key": "1771800", "vector": [], "sparse_vector": [], "title": "Visualizing Confidence Intervals for Critical Point Probabilities in 2D Scalar Field Ensembles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An important task in visualization is the extraction and highlighting of dominant features in data to support users in their analysis process. Topological methods are a well-known means of identifying such features in deterministic fields. However, many real-world phenomena studied today are the result of a chaotic system that cannot be fully described by a single simulation. Instead, the variability of such systems is usually captured with ensemble simulations that produce a variety of possible outcomes of the simulated process. The topological analysis of such ensemble data sets and uncertain data, in general, is less well studied. In this work, we present an approach for the computation and visual representation of confidence intervals for the occurrence probabilities of critical points in ensemble data sets. We demonstrate the added value of our approach over existing methods for critical point prediction in uncertain data on a synthetic data set and show its applicability to a data set from climate research.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00038"}, {"primary_key": "1771801", "vector": [], "sparse_vector": [], "title": "TimberTrek: Exploring and Curating Sparse Decision Trees with Interactive Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Given thousands of equally accurate machine learning (ML) models, how can users choose among them? A recent ML technique enables domain experts and data scientists to generate a complete Rashomon set for sparse decision trees--a huge set of almost-optimal interpretable ML models. To help ML practitioners identify models with desirable properties from this Rashomon set, we develop TimberTrek, the first interactive visualization system that summarizes thousands of sparse decision trees at scale. Two usage scenarios highlight how TimberTrek can empower users to easily explore, compare, and curate models that align with their domain knowledge and values. Our open-source tool runs directly in users' computational notebooks and web browsers, lowering the barrier to creating more responsible ML models. TimberTrek is available at the following public demo link: https://poloclub.github.io/timbertrek.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00021"}, {"primary_key": "1771802", "vector": [], "sparse_vector": [], "title": "ARShopping: In-Store Shopping Decision Support Through Augmented Reality and Immersive Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Online shopping gives customers boundless options to choose from, backed by extensive product details and customer reviews, all from the comfort of home; yet, no amount of detailed, online information can outweigh the instant gratification and hands-on understanding of a product that is provided by physical stores. However, making purchasing decisions in physical stores can be challenging due to a large number of similar alternatives and limited accessibility of the relevant product information (e.g., features, ratings, and reviews). In this work, we present ARShopping: a web-based prototype to visually communicate detailed product information from an online setting on portable smart devices (e.g., phones, tablets, glasses), within the physical space at the point of purchase. This prototype uses augmented reality (AR) to identify products and display detailed information to help consumers make purchasing decisions that fulfill their needs while decreasing the decision-making time. In particular, we use a data fusion algorithm to improve the precision of the product detection; we then integrate AR visualizations into the scene to facilitate comparisons across multiple products and features. We designed our prototype based on interviews with 14 participants to better understand the utility and ease of use of the prototype.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00033"}, {"primary_key": "1771771", "vector": [], "sparse_vector": [], "title": "Color Coding of Large Value Ranges Applied to Meteorological Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a novel color scheme designed to address the challenge of visualizing data series with large value ranges, where scale transformation provides limited support. We focus on meteorological data, where the presence of large value ranges is common. We apply our approach to meteorological scatterplots, as one of the most common plots used in this domain area. Our approach leverages the numerical representation of mantissa and exponent of the values to guide the design of novel \"nested\" color schemes, able to emphasize differences between magnitudes. Our user study evaluates the new designs, the state of the art color scales and representative color schemes used in the analysis of meteorological data: ColorCrafter, Viridis, and Rainbow. We assess accuracy, time and confidence in the context of discrimination (comparison) and interpretation (reading) tasks. Our proposed color scheme significantly outperforms the others in interpretation tasks, while showing comparable performances in discrimination tasks.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00034"}, {"primary_key": "1771775", "vector": [], "sparse_vector": [], "title": "Exploring D3 Implementation Challenges on Stack Overflow.", "authors": ["Leilani Battle", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Visualization languages help to standardize the process of designing effective visualizations, one of the most prominent being D3. However, few researchers have analyzed at scale how users incorporate these languages into existing visualization programming processes, i.e., implementation workflows. In this paper, we present an analysis of the experiences of D3 users as observed through Stack Overflow, summarizing common D3 implementation workflows and challenges discussed online. Our results show how the visualization community may be limiting its understanding of users' visualization implementation challenges by ignoring the larger context in which languages such as D3 are used. Based on our findings, we suggest new research directions to enhance the user experience with visualization languages. All our data and code are available at: https://osf.io/fup48/.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00009"}, {"primary_key": "1771779", "vector": [], "sparse_vector": [], "title": "Intentable: A Mixed-Initiative System for Intent-Based Chart Captioning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Intentable, a mixed-initiative caption authoring system that allows the author to steer an automatic caption generation pro-cess to reflect their intent, e.g., the finding that the author gained from visualization and thus wants to write a caption for. We first derive a grammar for specifying the intent, i.e., a caption recipe, and build a neural network that generates caption sentences given a recipe. Our quantitative evaluation revealed that our intent-based generation system not only allows the author to engage in the generation process but also produces more fluent captions than the previous end-to-end approaches without user intervention. Finally, we demonstrate the versatility of our system, such as context adaptation, unit conversion, and sentence reordering.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00017"}, {"primary_key": "1771782", "vector": [], "sparse_vector": [], "title": "Paths Through Spatial Networks.", "authors": ["<PERSON>"], "summary": "Spatial networks present unique challenges to understanding topo-logical structure. Each node occupies a location in physical space (e.g., longitude and latitude) and each link may either indicate a fixed and well-described path (e.g., along streets) or a logical connection only. Placing these elements in a map maintains these physical relationships while making it more difficult to identify topological features of interest such as clusters, cliques, and paths. While some systems provide coordinated representations of a spatial network, it is almost universally assumed that the network itself is the sole mechanism for movement across space. In this paper, I present a novel approach for exploring spatial networks by orienting them along a point or path in physical space that provides the guide for parameters in a force-directed layout. By specifying a path across topography, networks can be spatially filtered independently of the topology of the network. Initial case studies indicate promising results for exploring spatial networks in transportation and energy distribution.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00015"}, {"primary_key": "1771783", "vector": [], "sparse_vector": [], "title": "Accelerated Probabilistic Marching Cubes by Deep Learning for Time-Varying Scalar Ensembles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visualizing the uncertainty of ensemble simulations is challenging due to the large size and multivariate and temporal features of en-semble data sets. One popular approach to studying the uncertainty of ensembles is analyzing the positional uncertainty of the level sets. Probabilistic marching cubes is a technique that performs Monte Carlo sampling of multivariate Gaussian noise distributions for positional uncertainty visualization of level sets. However, the technique suffers from high computational time, making interactive visualization and analysis impossible to achieve. This paper introduces a deep-learning-based approach to learning the level-set uncertainty for two-dimensional ensemble data with a multivariate Gaussian noise assumption. We train the model using the first few time steps from time-varying ensemble data in our workflow. We demonstrate that our trained model accurately infers uncertainty in level sets for new time steps and is up to 170X faster than that of the original probabilistic model with serial computation and 10X faster than that of the original parallel computation.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00040"}, {"primary_key": "1771784", "vector": [], "sparse_vector": [], "title": "Uniform Manifold Approximation with Two-phase Optimization.", "authors": ["<PERSON><PERSON><PERSON> Jeon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinwook Seo"], "summary": "We introduce Uniform Manifold Approximation with Two-phase Optimization (UMATO), a dimensionality reduction (DR) technique that improves UMAP to capture the global structure of high-dimensional data more accurately. In UMATO, optimization is divided into two phases so that the resulting embeddings can depict the global structure reliably while preserving the local structure with sufficient accuracy. In the first phase, hub points are identified and projected to construct a skeletal layout for the global structure. In the second phase, the remaining points are added to the embedding preserving the regional characteristics of local areas. Through quan-titative experiments, we found that UMATO (1) outperformed widely used DR techniques in preserving the global structure while (2) pro-ducing competitive accuracy in representing the local structure. We also verified that UMATO is preferable in terms of robustness over diverse initialization methods, numbers of epochs, and subsampling techniques.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00025"}, {"primary_key": "1771787", "vector": [], "sparse_vector": [], "title": "Parametric Dimension Reduction by Preserving Local Structure.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We extend a well-known dimension reduction method, t-distributed stochastic neighbor embedding (t-SNE), from non-parametric to parametric by training neural networks. The main advantage of a parametric technique is the generalization of handling new data, which is beneficial for streaming data visualization. While previous parametric methods either require a network pre-training by the restricted <PERSON><PERSON>mann machine or intermediate results obtained from the traditional non-parametric t-SNE, we found that recent network training skills can enable a direct optimization for the t-SNE objective function. Accordingly, our method achieves high embedding quality while enjoying generalization. Due to mini-batch network training, our parametric dimension reduction method is highly efficient. For evaluation, we compared our method to several baselines on a variety of datasets. Experiment results demonstrate the feasibility of our method. The source code is available at https://github.com/a07458666/parametric_dr.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00024"}, {"primary_key": "1771788", "vector": [], "sparse_vector": [], "title": "Efficient Interpolation-based Pathline Tracing with B-spline Curves in Particle Dataset.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Particle tracing through numerical integration is a well-known approach to generating pathlines for visualization. However, for particle simulations, the computation of pathlines is expensive, since the interpolation method is complicated due to the lack of connectivity information. Previous studies utilize the k-d tree to reduce the time for neighborhood search. However, the efficiency is still limited by the number of tracing time steps. Therefore, we propose a novel interpolation-based particle tracing method that first represents particle data as B-spline curves and interpolates B-spline control points to reduce the number of interpolation time steps. We demonstrate our approach achieves good tracing accuracy with much less computation time.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00037"}, {"primary_key": "1771794", "vector": [], "sparse_vector": [], "title": "Oscar: A Semantic-based Data Binning Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Binning is applied to categorize data values or to see distributions of data. Existing binning algorithms often rely on statistical properties of data. However, there are semantic considerations for selecting appropriate binning schemes. Surveys, for instance, gather respon-dent data for demographic-related questions such as age, salary, number of employees, etc., that are bucketed into defined semantic categories. In this paper, we leverage common semantic categories from survey data and Tableau Public visualizations to identify a set of semantic binning categories. We employ these semantic binning categories in Oscar: a method for automatically selecting bins based on the inferred semantic type of the field. We conducted a crowdsourced study with 120 participants to better understand user preferences for bins generated by Oscar vs. binning provided in Tableau. We find that maps and histograms using binned values generated by <PERSON> are preferred by users as compared to binning schemes based purely on the statistical properties of the data.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00029"}, {"primary_key": "1771795", "vector": [], "sparse_vector": [], "title": "Toward Systematic Design Considerations of Organizing Multiple Views.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Maoyuan Sun"], "summary": "Multiple-view visualization (MV) has been used for visual analytics in various fields (e.g., bioinformatics, cybersecurity, and intelligence analysis). Because each view encodes data from a particular per-spective, analysts often use a set of views laid out in 2D space to link and synthesize information. The difficulty of this process is impacted by the spatial organization of these views. For instance, connecting information from views far from each other can be more challenging than neighboring ones. However, most visual analysis tools currently either fix the positions of the views or completely delegate this organization of views to users (who must manually drag and move views). This either limits user involvement in managing the layout of MV or is overly flexible without much guidance. Then, a key design challenge in MV layout is determining the factors in a spatial organization that impact understanding. To address this, we review a set of MV-based systems and identify considerations for MV layout rooted in two key concerns: perception, which considers how users perceive view relationships, and content, which considers the relationships in the data. We show how these allow us to study and analyze the design of MV layout systematically.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00030"}, {"primary_key": "1771797", "vector": [], "sparse_vector": [], "title": "Droplet-Local Line Integration for Multiphase Flow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Line integration of stream-, streak-, and pathlines is widely used and popular for visualizing single-phase flow. In multiphase flow, i.e., where the fluid consists, e.g., of a liquid and a gaseous phase, these techniques could also provide valuable insights into the internal flow of droplets and ligaments and thus into their dynamics. However, since such structures tend to act as entities, high translational and rotational velocities often obfuscate their detail. As a remedy, we present a method for deriving a droplet-local velocity field, using a decomposition of the original velocity field removing translational and rotational velocity parts, and adapt path- and streaklines. Ge-nerally, the resulting integral lines are thus shorter and less tangled, which simplifies their analysis. We demonstrate and discuss the uti-lity of our approach on droplets in two-phase flow data and visualize the removed velocity parts employing glyphs for context.", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00036"}, {"primary_key": "1771799", "vector": [], "sparse_vector": [], "title": "The role of extended reality for planning coronary artery bypass graft surgery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Immersive visual displays are becoming more common in the diagnostic imaging and pre-procedural planning of complex cardiology revascularization surgeries. One such procedure is coronary artery bypass grafting (CABG) surgery, which is a gold standard treat-ment for patients with advanced coronary heart disease. Treatment planning of the CABG surgery can be aided by extended reality (XR) displays as they are known for offering advantageous visual-ization of spatially heterogeneous and complex tasks. Despite the benefits of XR, it remains unknown whether clinicians will benefit from higher visual immersion offered by XR. In order to assess the impact of increased immersion as well as the latent factor of geometrical complexity, a quantitative user evaluation $(\\mathrm{n}=14)$ was performed with clinicians of advanced cardiology training simulating CABG placement on sixteen 3D arterial tree models derived from 6 patients two levels of anatomic complexity. These arterial models were rendered on 3D/XR and 2D display modes with the same tactile interaction input device. The findings of this study reveal that compared to a monoscopic 2D display, the greater visual immersion of 3D/XR does not significantly alter clinician accuracy in the task of bypass graft placement. Latent factors such as arterial complexity and clinical experience both influence the accuracy of graft placement. In addition, an anatomically less complex model", "published": "2022-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS54862.2022.00032"}]