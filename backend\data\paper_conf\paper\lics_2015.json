[{"primary_key": "4470728", "vector": [], "sparse_vector": [], "title": "On the Relative Usefulness of Fireballs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In CSL-LICS 2014, <PERSON><PERSON><PERSON><PERSON> and <PERSON> [1] showed that there is an implementation of the ordinary (i.e. strong, pure, call-by-name) λ-calculus into models like RAM machines which is polynomial in the number of β-steps, answering a long-standing question. The key ingredient was the use of a calculus with useful sharing, a new notion whose complexity was shown to be polynomial, but whose implementation was not explored. This paper, meant to be complementary, studies useful sharing in a call-by-value scenario and from a practical point of view. We introduce the Fireball Calculus, a natural extension of call-by-value to open terms, that is an intermediary step towards the strong case, and we present three results. First, we adapt useful sharing, refining the meta-theory. Then, we introduce the GLAMOUr a simple abstract machine implementing the Fireball Calculus extended with useful sharing. Its key feature is that usefulness of a step is tested-surprisingly-in constant time. Third, we provide a further optimisation that leads to an implementation having only a linear overhead with respect to the number of β-steps.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.23"}, {"primary_key": "4470729", "vector": [], "sparse_vector": [], "title": "Feedback Turing Computability, and Turing Computability as Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The notion of a feedback query is a natural generalization of choosing for an oracle the set of indices of halting computations. Notice that, in that setting, the computations being run are different from the computations in the oracle: the former can query an oracle, whereas the latter cannot. A feedback computation is one that can query an oracle, which itself contains the halting information about all feedback computations. Although this is self-referential, sense can be made of at least some such computations. This threatens, though, to obliterate the distinction between con- and divergence: before running a computation, a machine can ask the oracle whether that computation converges, and then run it if and only if the oracle says \"yes.\" This would quickly lead to a diagonalization paradox, except that a new distinction is introduced, this time between freezing and non-freezing computations. The freezing computations are even more extreme than the divergent ones, in that they prevent the dovetailing on all computations into a single run. In this paper, we study feedback around Turing computability. In one direction, we examine feedback Turing machines, and show that they provide exactly hyper arithmetic computability. In the other direction, Turing computability is itself feedback primitive recursion (at least, one version thereof). We also examine parallel feedback. Several different notions of parallelism in this context are identified. We show that parallel feedback Turing machines are strictly stronger than sequential feedback TMs, while in contrast parallel feedback p.r. Is the same as sequential feedback p.r.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.55"}, {"primary_key": "4470730", "vector": [], "sparse_vector": [], "title": "Varieties of Languages in a Category.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s variety theorem, a centerpiece of algebraic automata theory, establishes a bijective correspondence between varieties of languages and pseudovarieties of monoids. In the present paper this result is generalized to an abstract pair of algebraic categories: we introduce varieties of languages in a category C, and prove that they correspond to pseudovarieties of monoids in a closed monoidal category D, provided that C and D are dual on the level of finite objects. By suitable choices of these categories our result uniformly covers <PERSON><PERSON><PERSON>'s theorem and three variants due to <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, respectively, and yields new Eilenberg-type correspondences.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.46"}, {"primary_key": "4470731", "vector": [], "sparse_vector": [], "title": "Finite Open-World Query Answering with Number Restrictions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Open-world query answering is the problem of deciding, given a set of facts, conjunction of constraints, and query, whether the facts and constraints imply the query. This amounts to reasoning over all instances that include the facts and satisfy the constraints. We study finite open-world query answering (FQA), which assumes that the underlying world is finite and thus only considers the finite completions of the instance. The major known decidable cases of FQA derive from the following: the guarded fragment of first-order logic, which can express referential constraints (data in one place points to data in another) but cannot express number restrictions such as functional dependencies, and the guarded fragment with number restrictions but on a signature of arity only two. In this paper, we give the first decidability results for FQA that combine both referential constraints and number restrictions for arbitrary signatures: we show that, for unary inclusion dependencies and functional dependencies, the finiteness assumption of FQA can be lifted up to taking the finite implication closure of the dependencies[5]. Our result relies on new techniques to construct finite universal models of such constraints, for any bound on the maximal query size.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.37"}, {"primary_key": "4470732", "vector": [], "sparse_vector": [], "title": "Entailment among Probabilistic Implications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study a natural variant of the implicational fragment of propositional logic. Its formulas are pairs of conjunctions of positive literals, related together by an implicational-like connective, the semantics of this sort of implication is defined in terms of a threshold on a conditional probability of the consequent, given the antecedent: we are dealing with what the data analysis community calls confidence of partial implications or association rules. Existing studies of redundancy among these partial implications have characterized so far only entailment from one premise and entailment from two premises. By exploiting a previously noted alternative view of this entailment in terms of linear programming duality, we characterize exactly the cases of entailment from arbitrary numbers of premises. As a result, we obtain decision algorithms of better complexity, additionally, for each potential case of entailment, we identify a critical confidence threshold and show that it is, actually, intrinsic to each set of premises and antecedent of the conclusion.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.63"}, {"primary_key": "4470733", "vector": [], "sparse_vector": [], "title": "A Note on the Complexity of Classical and Intuitionistic Proofs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show an effective cut-free variant of <PERSON><PERSON><PERSON><PERSON>'s theorem extended to formulas with weak quantifiers: \"There is an elementary function f such that if φ is a cut-free LK proof of ⊢ A with symbol complexity ≤ c, then there exists a cut-free LJ proof of ⊢ ⊯ ⊯ A with symbol complexity ≤ f(c)\". This follows from the more general result: \"There is an elementary function f such that if φ is a cut-free LK proof of A ⊢ with symbol complexity ≤ c, then there exists a cut-free LJ proof of A ⊢ with symbol complexity ≤ f(c)\". The result is proved using a suitable variant of cut-elimination by resolution (CERES) and subsumption.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.66"}, {"primary_key": "4470734", "vector": [], "sparse_vector": [], "title": "A Canonical Form for Weighted Automata and Applications to Approximate Minimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Doina Precup"], "summary": "We study the problem of constructing approximations to a weighted automaton. Weighted finite automata (WFA) are closely related to the theory of rational series. A rational series is a function from strings to real numbers that can be computed by a WFA. Among others, this includes probability distributions generated by hidden Markov models and probabilistic automata. The relationship between rational series and WFA is analogous to the relationship between regular languages and ordinary automata. Associated with such rational series are infinite matrices called Hankel matrices which play a fundamental role in the theory of minimal WFA. Our contributions are: (1) an effective procedure for computing the singular value decomposition (SVD) of such infinite Hankel matrices based on their finite representation in terms of WFA, (2) a new canonical form for WFA based on this SVD decomposition, and, (3) an algorithm to construct approximate minimizations of a given WFA. The goal of our approximate minimization algorithm is to start from a minimal WFA and produce a smaller WFA that is close to the given one in a certain sense. The desired size of the approximating automaton is given as input. We give bounds describing how well the approximation emulates the behavior of the original WFA. The study of this problem is motivated by the analysis of machine learning algorithms that synthesize weighted automata from spectral decompositions of finite Hankel matrices. It is known that when the number of states of the target automaton is correctly guessed, these algorithms enjoy consistency and finite-sample guarantees in the probably approximately correct (PAC) learning model. It has also been suggested that asking the learning algorithm to produce a model smaller than the true one will still yield useful models with reduced complexity. Our results in this paper vindicate these ideas and confirm intuitions provided by empirical studies. Beyond learning problems, our techniques can also be used to reduce the complexity of any algorithm working with WFA, at the expense of incurring a small, controlled amount of error.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.70"}, {"primary_key": "4470735", "vector": [], "sparse_vector": [], "title": "Hyper Natural Deduction.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a Hyper Natural Deduction system as an extension of <PERSON><PERSON><PERSON>'s Natural Deduction system. A Hyper Natural Deduction consists of a finite set of derivations which may use, beside typical Natural Deduction rules, additional rules providing means for communication between derivations. We show that our Hyper Natural Deduction system is sound and complete for infinite-valued propositional Gödel Logic, by giving translations to and from <PERSON><PERSON><PERSON>'s Hyper sequent Calculus. We also provide conversions for normalisation and prove the existence of normal forms for our Hyper Natural Deduction system.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.57"}, {"primary_key": "4470736", "vector": [], "sparse_vector": [], "title": "Interpolation with Decidable Fixpoint Logics.", "authors": ["<PERSON>", "<PERSON><PERSON> ten <PERSON>", "<PERSON>"], "summary": "A logic satisfies Craig interpolation if whenever one formula ?1 in the logic entails another formula ?2 in the logic, there is an intermediate formula -- one entailed by ?1 and entailing ?2 -- using only relations in the common signature of ? and ?2. Uniform interpolation strengthens this by requiring the interpolant to depend only on ?1 and the common signature. A uniform interpolant can thus be thought of as a minimal upper approximation of a formula within a sub signature. For first-order logic, interpolation holds but uniform interpolation fails. Uniform interpolation is known to hold for several modal and description logics, but little is known about uniform interpolation for fragments of predicate logic over relations with arbitrary arity. Further, little is known about ordinary Craig interpolation for logics over relations of arbitrary arity that have a recursion mechanism, such as fix point logics. In this work we take a step towards filling these gaps, proving interpolation for a decidable fragment of least fix point logic called unary negation fix point logic. We prove this by showing that for any fixed k, uniform interpolation holds for the k-variable fragment of the logic. In order to show this we develop the technique of reducing questions about logics with tree-like models to questions about modal logics, following an approach by <PERSON><PERSON>, <PERSON>, and <PERSON>. While this technique has been applied to expressivity and satisfiability questions before, we show how to extend it to reduce interpolation questions about such logics to interpolation for the µ-calculus.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.43"}, {"primary_key": "4470737", "vector": [], "sparse_vector": [], "title": "The Complexity of Boundedness for Guarded Logics.", "authors": ["<PERSON>", "<PERSON><PERSON> ten <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given a formula phi(x, X) positive in X, the bounded ness problem asks whether the fix point induced by phi is reached within some uniform bound independent of the structure (i.e. Whether the fix point is spurious, and can in fact be captured by a finite unfolding of the formula). In this paper, we study the bounded ness problem when phi is in the guarded fragment or guarded negation fragment of first-order logic, or the fix point extensions of these logics. It is known that guarded logics have many desirable computational and model theoretic properties, including in some cases decidable bounded ness. We prove that bounded ness for the guarded negation fragment is decidable in elementary time, and, making use of an unpublished result of Colcombet, even 2EXPTIME-complete. Our proof extends the connection between guarded logics and automata, reducing bounded ness for guarded logics to a question about cost automata on trees, a type of automaton with counters that assigns a natural number to each input rather than just a boolean.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.36"}, {"primary_key": "4470738", "vector": [], "sparse_vector": [], "title": "Tree-like Queries in OWL 2 QL: Succinctness and Complexity Results.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vladimir V. Podolskii"], "summary": "This paper investigates the impact of query topology on the difficulty of answering conjunctive queries in the presence of OWL 2 QL ontologies. Our first contribution is to clarify the worst-case size of positive existential (PE), non-recursive Data log (NDL), and first-order (FO) rewritings for various classes of tree-like conjunctive queries, ranging from linear queries to bounded tree width queries. Perhaps our most surprising result is a super polynomial lower bound on the size of PE-rewritings that holds already for linear queries and ontologies of depth 2. More positively, we show that polynomial-size NDL-rewritings always exist for tree-shaped queries with a bounded number of leaves (and arbitrary ontologies), and for bounded tree width queries paired with bounded depth ontologies. For FO-rewritings, we equate the existence of polysize rewritings with well-known problems in Boolean circuit complexity. As our second contribution, we analyze the computational complexity of query answering and establish tractability results (either NL-or LOGCFL-completeness) for a range of query-ontology pairs. Combining our new results with those from the literature yields a complete picture of the succinctness and complexity landscapes for the considered classes of queries and ontologies.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.38"}, {"primary_key": "4470739", "vector": [], "sparse_vector": [], "title": "Reachability in Two-Dimensional Vector Addition Systems with States Is PSPACE-Complete.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Determining the complexity of the reachability problem for vector addition systems with states (VASS) is a long-standing open problem in computer science. Long known to be decidable, the problem to this day lacks any complexity upper bound whatsoever. In this paper, reachability for two-dimensional VASS is shown PSPACE-complete. This improves on a previously known doubly exponential time bound established by <PERSON>, <PERSON>, <PERSON> and <PERSON> in 1986. The coverability and boundedness problems are also noted to be PSPACE-complete. In addition, some complexity results are given for the reachability problem in two-dimensional VASS and in integer VASS when numbers are encoded in unary.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.14"}, {"primary_key": "4470740", "vector": [], "sparse_vector": [], "title": "Star Height via Games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes a new algorithm deciding the star height problem. As shown by <PERSON><PERSON>, the star height problem reduces to a problem concerning automata with counters, called limitedness. The new contribution is a different algorithm for the limitedness problem, which reduces it to solving a Gale-Stewart game with an ω-regular winning condition.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.29"}, {"primary_key": "4470741", "vector": [], "sparse_vector": [], "title": "The Target Discounted-Sum Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The target discounted-sum problem is the following: Given a rational discount factor 0 < λ < 1 and three rational values a, b, and t, does there exist a finite or an infinite sequence ω ∈(a, b)* or ω ∈(a, b)ω, such that Σ|ω| i=0 ω(i)λi equals t? The problem turns out to relate to many fields of mathematics and computer science, and its decidability question is surprisingly hard to solve. We solve the finite version of the problem, and show the hardness of the infinite version, linking it to various areas and open problems in mathematics and computer science: β-expansions, discounted-sum automata, piecewise affine maps, and generalizations of the Cantor set. We provide some partial results to the infinite version, among which are solutions to its restriction to eventually-periodic sequences and to the cases that λ λ 1/2 or λ = 1/n, for every n ∈ N. We use our results for solving some open problems on discounted-sum automata, among which are the exact-value problem for nondeterministic automata over finite words and the universality and inclusion problems for functional automata.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.74"}, {"primary_key": "4470742", "vector": [], "sparse_vector": [], "title": "On the Complexity of Temporal Equilibrium Logic.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Temporal Equilibrium Logic (TEL) [1] is a promising framework that extends the knowledge representation and reasoning capabilities of Answer Set Programming with temporal operators in the style of LTL. To our knowledge it is the first nonmonotonic logic that accommodates fully the syntax of a standard temporal logic (specifically LTL) without requiring further constructions. This paper provides a systematic complexity analysis for the (consistency) problem of checking the existence of a temporal equilibrium model of a TEL formula. It was previously shown that this problem in the general case lies somewhere between PSPACE and EXPSPACE. Here we establish a lower bound matching the EXPSPACE upper bound in [2]. Additionally we analyse the complexity for various natural subclasses of TEL formulas, identifying both tractable and intractable fragments. Finally the paper offers some new insights on the logic LTL by addressing satisfiability for minimal LTL models. The complexity results obtained highlight a substantial difference between interpreting LTL over finite or infinite words.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.65"}, {"primary_key": "4470743", "vector": [], "sparse_vector": [], "title": "Long-Run Average Behaviour of Probabilistic Vector Addition Systems.", "authors": ["Tomás Brázdil", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the pattern frequency vector for runs in probabilistic Vector Addition Systems with States (pVASS). Intuitively, each configuration of a given pVASS is assigned one of finitely many patterns, and every run can thus be seen as an infinite sequence of these patterns. The pattern frequency vector assigns to each run the limit of pattern frequencies computed for longer and longer prefixes of the run. If the limit does not exist, then the vector is undefined. We show that for one-counter pVASS, the pattern frequency vector is defined and takes one of finitely many values for almost all runs. Further, these values and their associated probabilities can be approximated up to an arbitrarily small relative error in polynomial time. For stable two-counter pVASS, we show the same result, but we do not provide any upper complexity bound. As a byproduct of our study, we discover counterexamples falsifying some classical results about stochastic Petri nets published in the 80s.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.15"}, {"primary_key": "4470744", "vector": [], "sparse_vector": [], "title": "Petri Automata for Kleene Allegories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Kleene algebra axioms are complete with respect to both language models and binary relation models. In particular, two regular expressions recognise the same language if and only if they are universally equivalent in the model of binary relations. We consider Kleene allegories, i.e., Kleene algebras with two additional operations which are natural in binary relation models: intersection and converse. While regular languages are closed under those operations, the above characterisation breaks. Instead, we give a characterisation in terms of languages of directed and labelled graphs. We then design a finite automata model allowing to recognise such graphs, by taking inspiration from Petri nets. This model allows us to obtain decidability of identity-free relational Kleene lattices, i.e., The equational theory generated by binary relations on the signature of regular expressions with intersection, but where one forbids unit. This restriction is used to ensure that the corresponding graphs are a cyclic. The decidability of graph-language equivalence in the full model remains open.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.17"}, {"primary_key": "4470745", "vector": [], "sparse_vector": [], "title": "Defining Winning Strategies in Fixed-Point Logic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study definability questions for positional winning strategies in infinite games on graphs. The quest for efficient algorithmic constructions of winning regions and winning strategies in infinite games, in particular parity games, is of importance in many branches of logic and computer science. A closely related, yet different, facet of this problem concerns the definability of winning regions and winning strategies in logical systems such as monadic second-order logic, least fixed-point logic LFP, the modal μ-calculus and some of its fragments. While a number of results concerning definability issues for winning regions have been established, so far almost nothing has been known concerning the definability of winning strategies. We make the notion of logical definability of positional winning strategies precise and study systematically the possibility of translations between definitions of winning regions and definitions of winning strategies. We present explicit LFP-definitions for winning strategies in games with relatively simple objectives, such as safety, reach ability, eventual safety (Co-Büchi) and recurrent reach ability (Büchi), and then prove, based on the Stage Comparison Theorem, that winning strategies for any class of parity games with a bounded number of priorities are LFP-definable. For parity games with an unbounded number of priorities, LFP-definitions of winning strategies are provably impossible on arbitrary (finite and infinite) game graphs. On finite game graphs however, this definability problem turns out to be equivalent to the fundamental open question about the algorithmic complexity of parity games. Indeed, based on a general argument about LFP-translations we prove that LFP definable winning strategies on the class of all finite parity games exist if, and only if, parity games can be solved in polynomial time, despite the fact that LFP is, in general, strictly weaker than polynomial time.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.42"}, {"primary_key": "4470746", "vector": [], "sparse_vector": [], "title": "How Good Is a Strategy in a Game with Nature?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider games with two antagonistic players -- <PERSON><PERSON><PERSON><PERSON> (modelling a program) and <PERSON><PERSON> (modelling a byzantine environment) -- and a third, unpredictable and uncontrollable player, that we call Nature. Motivated by the fact that the usual probabilistic semantics very quickly leads to undecidability when considering either infinite game graphs or imperfect information, we propose two alternative semantics that leads to decidability where the probabilistic one fails: one based on counting and one based on topology.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.62"}, {"primary_key": "4470747", "vector": [], "sparse_vector": [], "title": "PDL Is the Bisimulation-Invariant Fragment of Weak Chain Logic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce a new class of parity automata which, on trees, captures the expressive power of weak chain logic. This logic is a variant of monadic second-order logic which quantifies over finite chains. Using this new tool, we show that the bisimulation-invariant fragment of weak chain logic is equivalent to propositional dynamic logic.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.40"}, {"primary_key": "4470748", "vector": [], "sparse_vector": [], "title": "From Complexity to Algebra and Back: Digraph Classes, Collapsibility, and the PGP.", "authors": ["<PERSON><PERSON>", "Florent <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inspired by computational complexity results for the quantified constraint satisfaction problem, we study the clones of idem potent polymorphisms of certain digraph classes. Our first results are two algebraic dichotomy, even \"gap\", theorems. Building on and extending [Martin <PERSON>11], we prove that partially reflexive paths bequeath a set of idem potent polymorphisms whose associated clone algebra has: either the polynomially generated powers property (PGP), or the exponentially generated powers property (EGP). Similarly, we build on [DaMM ICALP'14] to prove that semi complete digraphs have the same property. These gap theorems are further motivated by new evidence that PGP could be the algebraic explanation that a QCSP is in NP even for unbounded alternation. Along the way we also effect a study of a concrete form of PGP known as collapsibility, tying together the algebraic and structural threads from [Chen Sicomp'08], and show that collapsibility is equivalent to its Pi2-restriction. We also give a decision procedure for k-collapsibility from a singleton source of a finite structure (a form of collapsibility which covers all known examples of PGP for finite structures). Finally, we present a new QCSP trichotomy result, for partially reflexive paths with constants. Without constants it is known these QCSPs are either in NL or Pspace-complete [<PERSON>11], but we prove that with constants they attain the three complexities NL, NP-complete and Pspace-complete.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.50"}, {"primary_key": "4470749", "vector": [], "sparse_vector": [], "title": "The Parallel Intensionally Fully Abstract Games Model of PCF.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe a framework for truly concurrent game semantics of programming languages, based on <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s concurrent games on event structures. The model supports a notion of innocent strategy that permits concurrent and non-deterministic behaviour, but which coincides with traditional <PERSON>yland-Ong innocent strategies if one restricts to the deterministic sequential case. In this framework we give an alternative interpretation of <PERSON><PERSON> kin's PCF, that takes advantage of the concurrent nature of strategies and formalizes the idea that although PCF is a sequential language, certain sub-computations are independent and can be computed in a parallel fashion. We show that just as <PERSON><PERSON> and <PERSON><PERSON>'s sequential interpretation of PCF, our parallel interpretation yields a model that is intensionally fully abstract for PCF.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.31"}, {"primary_key": "4470750", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for One-Pair and k-Pair Streett Objectives.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The computation of the winning set for one-pair Streett objectives and for k-pair Streett objectives in (standard) graphs as well as in game graphs are central problems in computer-aided verification, with application to the verification of closed systems with strong fairness conditions, the verification of open systems, checking interface compatibility, well-formed ness of specifications, and the synthesis of reactive systems. We give faster algorithms for the computation of the winning set for (1) one-pair Streett objectives (aka parity-3 problem) in game graphs and (2) for k-pair Streett objectives in graphs. For both problems this represents the first improvement in asymptotic running time in 15 years.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.34"}, {"primary_key": "4470751", "vector": [], "sparse_vector": [], "title": "Nested Weighted Automata.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently there has been a significant effort to handle quantitative properties in formal verification and synthesis. While weighted automata over finite and infinite words provide a natural and flexible framework to express quantitative properties, perhaps surprisingly, some basic system properties such as average response time cannot be expressed using weighted automata, nor in any other know decidable formalism. In this work, we introduce nested weighted automata as a natural extension of weighted automata which makes it possible to express important quantitative properties such as average response time. In nested weighted automata, a master automaton spins off and collects results from weighted slave automata, each of which computes a quantity along a finite portion of an infinite word. Nested weighted automata can be viewed as the quantitative analogue of monitor automata, which are used in run-time verification. We establish an almost complete decidability picture for the basic decision problems about nested weighted automata, and illustrate their applicability in several domains. In particular, nested weighted automata can be used to decide average response time properties.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.72"}, {"primary_key": "4470752", "vector": [], "sparse_vector": [], "title": "Unifying Two Views on Multiple Mean-Payoff Objectives in Markov Decision Processes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider Markov decision processes (MDPs) with multiple limit-average (or mean-payoff) objectives. There exist two different views: (i) ~the expectation semantics, where the goal is to optimize the expected mean-payoff objective, and (ii) ~the satisfaction semantics, where the goal is to maximize the probability of runs such that the mean-payoff value stays above a given vector. We consider optimization with respect to both objectives at once, thus unifying the existing semantics. Precisely, the goal is to optimize the expectation while ensuring the satisfaction constraint. Our problem captures the notion of optimization with respect to strategies that are risk-averse (i.e., Ensure certain probabilistic guarantee). Our main results are as follows: First, we present algorithms for the decision problems, which are always polynomial in the size of the MDP. We also show that an approximation of the <PERSON><PERSON><PERSON> curve can be computed in time polynomial in the size of the MDP, and the approximation factor, but exponential in the number of dimensions. Second, we present a complete characterization of the strategy complexity (in terms of memory bounds and randomization) required to solve our problem.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.32"}, {"primary_key": "4470753", "vector": [], "sparse_vector": [], "title": "Timed Pushdown Automata Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper contains two results on timed extensions of pushdown automata (PDA). As our first result we prove that the model of dense-timed PDA of <PERSON><PERSON> et al. Collapses: it is expressively equivalent to dense-timed PDA with timeless stack. Motivated by this result, we advocate the framework of first-order definable PDA, a specialization of PDA in sets with atoms, as the right setting to define and investigate timed extensions of PDA. The general model obtained in this way is Turing complete. As our second result we prove NEXPTIME upper complexity bound for the non-emptiness problem for an expressive subclass. As a byproduct, we obtain a tight EXPTIME complexity bound for a more restrictive subclass of PDA with timeless stack, thus subsuming the complexity bound known for dense-timed PDA.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.73"}, {"primary_key": "4470754", "vector": [], "sparse_vector": [], "title": "Multidimensional beyond Worst-Case and Almost-Sure Problems for Mean-Payoff Objectives.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The beyond worst-case threshold problem (BWC), recently introduced by <PERSON><PERSON><PERSON><PERSON> et al., asks given a quantitative game graph for the synthesis of a strategy that i) enforces some minimal level of performance against any adversary, and ii) achieves a good expectation against a stochastic model of the adversary. They solved the BWC problem for finite-memory strategies and unidimensional mean-payoff objectives and they showed membership of the problem in NP∩coNP. They also noted that infinite-memory strategies are more powerful than finite-memory ones, but the respective threshold problem was left open. We extend these results in several directions. First, we consider multidimensional mean-payoff objectives. Second, we study both finite-memory and infinite-memory strategies. We show that the multidimensional BWC problem is coNPc in both cases. Third, in the special case when the worst-case objective is unidimensional (but the expectation objective is still multidimensional) we show that the complexity decreases to NP∩coNP. This solves the infinite-memory threshold problem left open by <PERSON><PERSON><PERSON> et al., and this complexity cannot be improved without improving the currently known complexity of classical mean-payoff games. Finally, we introduce a natural relaxation of the BWC problem, the beyond almost-sure threshold problem (BAS), which asks for the synthesis of a strategy that ensures some minimal level of performance with probability one and a good expectation against the stochastic model of the adversary. We show that the multidimensional BAS threshold problem is solvable in P.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.33"}, {"primary_key": "4470755", "vector": [], "sparse_vector": [], "title": "Metric Reasoning about λ-Terms: The Affine Case.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Ugo <PERSON>"], "summary": "Terms of Church's λ-calculus can be considered equivalent along many different definitions, but context equiv-alence is certainly the most direct and universally accepted one. If the underlying calculus becomes probabilistic, however, equivalence is too discriminating: terms which have totally unrelated behaviours are treated the same as terms which behave very similarly. We study the problem of evaluating the distance between affine λ-terms. A natural generalisation of context equiv-alence, is shown to be characterised by a notion of trace distance, and to be bounded from above by a co inductively defined distance based on the Ka<PERSON>rovich metric on distributions. A different, again fully-abstract, tuple-based notion of trace distance is shown to be able to handle nontrivial examples.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.64"}, {"primary_key": "4470756", "vector": [], "sparse_vector": [], "title": "Branching Bisimilarity of Normed BPA Processes Is in NEXPTIME.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Branching bisimilarity of nor med Basic Process Algebra (BPA) processes was shown to be decidable by <PERSON><PERSON> (ICALP 2013) but his proof has not provided any upper complexity bound. We present a simpler approach based on relative prime decompositions that leads to a nondeterministic exponential-time algorithm, this is \"close\" to the known exponential-time lower bound. We also derive that semantic finiteness (the question if a given nor med BPA process is branching bisimilar with some finite-state process) belongs to NExpTime as well.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.25"}, {"primary_key": "4470757", "vector": [], "sparse_vector": [], "title": "Descriptive Complexity of List H-Coloring Problems in Logspace: A Refined Dichotomy.", "authors": ["<PERSON><PERSON><PERSON>", "László <PERSON>", "Pavol Hell", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Dichotomy Conjecture for constraint satisfaction problems (CSPs) states that every CSP is in P or is NP-complete (<PERSON><PERSON><PERSON>, 1993). It has been verified for conservative problems (also known as list homomorphism problems) by <PERSON><PERSON> (2003). <PERSON><PERSON><PERSON> et al. (SODA 2014) augmented this result by showing that for digraph templates H, every conservative CSP, denoted LHOM(H), is solvable in log space or is hard for NL. A conjecture of <PERSON><PERSON> and <PERSON> from 2007 forecasts that when LHOM(H) is in log space, then in fact, it falls in a small subclass of log space, the set of problems expressible in symmetric Data log. The present work verifies the conjecture for LHOM(H) (and, indeed, for the wider class of conservative CSPs with binary constraints), and by so doing sharpens the aforementioned dichotomy. A combinatorial characterization of symmetric Data log provides the language in which the algorithmic ideas of the paper, quite different from the ones in <PERSON><PERSON><PERSON> et al., are formalized.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.52"}, {"primary_key": "4470758", "vector": [], "sparse_vector": [], "title": "A Complete Axiomatization of MSO on Infinite Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that an adaptation of <PERSON><PERSON><PERSON>'s axioms for second-order arithmetic to the language of MSO completely axiomatizes the theory over infinite trees. This continues a line of work begun by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> with axiomatizations of MSO over various classes of linear orders. Our proof formalizes, in the axiomatic theory, a translation of MSO formulas to alternating parity tree automata. The main ingredient is the formalized proof of positional determinacy for the corresponding parity games which, as usual, allows us to complement automata in order to deal with negation of MSO formulas. The Comprehension scheme of monadic second-order logic is used to obtain uniform winning strategies, whereas most usual proofs of positional determinacy rely on forms of the Axiom of Choice or transfinite induction.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.44"}, {"primary_key": "4470759", "vector": [], "sparse_vector": [], "title": "Game Semantics for Type Soundness.", "authors": ["<PERSON>", "Cormac Flanagan"], "summary": "The key idea of game semantics is that a term can interact with its enclosing context via various events, such as function calls and returns. A trace is a sequence of such interaction events. The meaning of the term is then naturally represented by the set of all event traces that the term can generate. Game semantics allows us to define the meaning of both expressions and types in the same domain which enables an interesting alternative to subject reduction for proving type soundness. This paper uses game semantics to define the meaning of and verify type soundness for a sequence of programming languages, starting with a functional sequential language (the call-by-value simply-typed lambda calculus), and then extending that proof with sub typing, side effects, control effects, and concurrency. These proofs are reasonably short and fairly semantic in structure, focusing on the relationship between the meanings of each term and its corresponding type. In particular, we show that the typing and sub typing relations are both conservative approximations of alternating trace containment.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.20"}, {"primary_key": "4470760", "vector": [], "sparse_vector": [], "title": "Extensions of Domain Maps in Differential and Integral Calculus.", "authors": ["<PERSON>"], "summary": "We introduce in the context of differential and integral calculus several key extensions of higher order maps from a dense subset of a topological space into a continuous Scott domain. These higher order maps include the classical derivative operator and the Riemann integration operator. Using a sequence of test functions, we prove that the subspace of real-valued continuously differentiable functions on a finite dimensional Euclidean space is dense in the space of Li<PERSON><PERSON><PERSON> maps equipped with the L-topology. This provides a new result in basic mathematical analysis, which characterises the L-topology in terms of the limsup of the sequence of derivatives of a sequence of C1 maps that converges to a Lipschitz map. Using this result, it is also shown that the generalised (<PERSON>) gradient on Lipschitz maps is the extension of the derivative operator on C1 maps. We show that the generalised Riemann integral (R-integral) of a real-valued continuous function on a compact metric space with respect to a Borel measure can be extended to the integral of interval-valued functions on the metric space with respect to valuations on the probabilistic power domain of the space of non-empty and compact sets of the metric space. We also prove that the Lebesgue integral operator on integrable functions is the extension of the R-integral operator on continuous functions. We finally illustrate an application of these results by deriving a simple proof of <PERSON>'s theorem for interval-valued vector fields.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.47"}, {"primary_key": "4470761", "vector": [], "sparse_vector": [], "title": "Regularity Preserving but Not Reflecting Encodings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Encodings, that is, injective functions from words to words, have been studied extensively in several settings. In computability theory the notion of encoding is crucial for defining computability on arbitrary domains, as well as for comparing the power of models of computation. In language theory much attention has been devoted to regularity preserving functions. A natural question arising in these contexts is: Is there a bijective encoding such that its image function preserves regularity of languages, but its pre-image function does not? Our main result answers this question in the affirmative: For every countable class C of languages there exists a bijective encoding f such that for every language L ∈ L its image f[L] is regular. Our construction of such encodings has several noteworthy consequences. Firstly, anomalies arise when models of computation are compared with respect to a known concept of implementation that is based on encodings which are not required to be computable: Every countable decision model can be implemented, in this sense, by finite-state automata, even via bijective encodings. Hence deterministic finite-state automata would be equally powerful as Turing machine deciders. A second consequence concerns the recognizability of sets of natural numbers via number representations and finite automata. A set of numbers is said to be recognizable with respect to a representation if an automaton accepts the language of representations. Our result entails that there is one number representation with respect to which every recursive set is recognizable.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.56"}, {"primary_key": "4470762", "vector": [], "sparse_vector": [], "title": "Monadic Second-Order Logic and Bisimulation Invariance for Coalgebras.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Generalizing standard monadic second-order logic for <PERSON><PERSON><PERSON> models, we introduce monadic second-order logic MSO(T) interpreted over co algebras for an arbitrary set functor T. Similar to well-known results for monadic second-order logic over trees, we provide a translation of this logic into a class of automata, relative to the class of T-co algebras that admit a tree-like supporting <PERSON><PERSON><PERSON> frame. We then consider invariance under behavioral equivalence of MSO(T)-formulas, more in particular, we investigate whether the co algebraic mu-calculus is the bisimulation-invariant fragment of MSO(T). Building on recent results by the third author we show that in order to provide such a co algebraic generalization of the Janin-<PERSON><PERSON><PERSON><PERSON><PERSON> Theorem, it suffices to find what we call an adequate uniform construction for the functor T. As applications of this result we obtain a partly new proof of the Jan<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> Theorem, and bisimulation invariance results for the bag functor (graded modal logic) and all exponential polynomial functors. Finally, we consider in some detail the monotone neighborhood functor M, which provides co algebraic semantics for monotone modal logic. It turns out that there is no adequate uniform construction for M, whence the automata-theoretic approach towards bisimulation invariance does not apply directly. This problem can be overcome if we consider global bisimulations between neighborhood models: one of our main results provides a characterization of the monotone modal mu-calculus extended with the global modalities, as the fragment of monadic second order logic for the monotone neighborhood functor that is invariant for global bisimulations.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.41"}, {"primary_key": "4470763", "vector": [], "sparse_vector": [], "title": "Path Logics for Querying Graphs: Combining Expressiveness and Efficiency.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study logics expressing properties of paths in graphs that are tailored to querying graph databases: a data model for new applications such as social networks, the Semantic Web, biological data, crime detection, and others. The basic construct of such logics, a regular path query, checks for paths whose labels belong to a regular language. These logics fail to capture two commonly needed features: counting properties, and the ability to compare paths. It is known that regular path-comparison relations (e.g., Prefix or equality) can be added without significant complexity overhead, however, adding common relations often demanded by applications (e.g., Sub word, subsequence, suffix) results in either undecidability or astronomical complexity. We propose, as a way around this problem, to use automata with counting functionalities, namely Parikh automata. They express many counting properties directly, and they approximate many relations of interest. We prove that with Parikh automata defining both languages and relations used in queries, we retain the low complexity of the standard path logics for graphs. In particular, this gives us efficient approximations to queries with prohibitively high complexity. We extend the best known decidability results by showing that even more expressive classes of relations are possible in query languages (sometimes with restriction on the shape of formulae). We also show that Parikh automata admit two convenient representations by analogs of regular expressions, making them usable in real-life querying.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.39"}, {"primary_key": "4470764", "vector": [], "sparse_vector": [], "title": "One Context Unification Problems Solvable in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One context unification extends first-order unification by introducing a single context variable, possibly with multiple occurrences. One context unification is known to be in NP, but it is not known to be solvable in polynomial time. In this paper, we present a polynomial time algorithm for certain interesting classes of the one context unification problem. Our algorithm is presented as an inference system that non-trivially extends the usual inference rules for first-order unification. The algorithm is of independent value as it can be used, with slight modifications, to solve other problems, such as the first-order unification problem that tolerates one clash.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.53"}, {"primary_key": "4470765", "vector": [], "sparse_vector": [], "title": "The Hunt for a Red Spider: Conjunctive Query Determinacy Is Undecidable.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We solve a well known, long-standing open problem in relational databases theory, showing that the conjunctive query determinacy problem (in its \"unrestricted\" version) is undecidable.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.35"}, {"primary_key": "4470766", "vector": [], "sparse_vector": [], "title": "Characterising Choiceless Polynomial Time with First-Order Interpretations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lukasz Kaiser"], "summary": "Choice less Polynomial Time (CPT) is one of the candidates in the quest for a logic for polynomial time. It is a strict extension of fixed-point logic with counting, but to date the question is open whether it expresses all polynomial-time properties of finite structures. We present here alternative characterisations of Choice less Polynomial Time (with and without counting) based on iterated first-order interpretations. The fundamental mechanism of Choice less Polynomial Time is the manipulation of hereditarily finite sets over the input structure by means of set-theoretic operations and comprehension terms. While this is very convenient and powerful for the design of abstract computations on structures, it makes the analysis of the expressive power of CPT rather difficult. We aim to reduce this functional framework operating on higher-order objects to an approach that evaluates formulae on less complex objects. We propose a more model-theoretic formalism, called polynomial-time interpretation logic (PIL), that replaces the machinery of hereditarily finite sets and comprehension terms by traditional first-order interpretations, and handles counting by <PERSON><PERSON><PERSON><PERSON> quantifiers. In our framework, computations on finite structures are captured by iterations of interpretations, and a run is a sequence of states, each of which is a finite structure of a fixed vocabulary. Our main result is that PIL has precisely the same expressive power as Choice less Polynomial Time. We also analyse the structure of PIL and show that many of the logical formalisms or database languages that have been proposed in the quest for a logic for polynomial time reappear as fragments of PIL, obtained by restricting interpretations in a natural way (e.g. By omitting congruences or using only one-dimensional interpretations).", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.68"}, {"primary_key": "4470767", "vector": [], "sparse_vector": [], "title": "Programs for Cheap!", "authors": ["<PERSON>", "<PERSON>"], "summary": "Write down the definition of a recursion operator on a piece of paper. Tell me its type, but be careful not to let me see the operator's definition. I will tell you an optimization theorem that the operator satisfies. As an added bonus, I will also give you a proof of correctness for the optimisation, along with a formal guarantee about its effect on performance. The purpose of this paper is to explain these tricks.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.21"}, {"primary_key": "4470768", "vector": [], "sparse_vector": [], "title": "A Diagrammatic Axiomatisation for Qubit Entanglement.", "authors": ["<PERSON>"], "summary": "Diagrammatic techniques for reasoning about monoidal categories provide an intuitive understanding of the symmetries and connections of interacting computational processes. In the context of categorical quantum mechanics, <PERSON><PERSON><PERSON> and <PERSON><PERSON> suggested that two 3-qubit states, GHZ and W, may be used as the building blocks of a new graphical calculus, aimed at a diagrammatic classification of multipartite qubit entanglement that would highlight the communicational properties of quantum states, and their potential uses in cryptographic schemes. In this paper, we present a full graphical axiomatisation of the relations between GHZ and W: the ZW calculus. This refines a version of the preexisting ZX calculus, while keeping its most desirable characteristics: undirected ness, a large degree of symmetry, and an algebraic underpinning. We prove that the ZW calculus is complete for the category of free abelian groups on a power of two generators - \"qubits with integer coefficients\" - and provide an explicit normalisation procedure.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.59"}, {"primary_key": "4470769", "vector": [], "sparse_vector": [], "title": "Branching Bisimilarity on Normed BPA Is EXPTIME-Complete.", "authors": ["Chao<PERSON> He", "<PERSON><PERSON><PERSON>"], "summary": "We put forward an exponential-time algorithm for deciding branching bisimilarity on nor med BPA (Bacis Process Algebra) systems. The decidability of branching bisimilarity on nor med BPA was once a long-standing open problem which was closed by <PERSON><PERSON>. The EXPTIME-hardness is an inference of a slight modification of the reduction presented by <PERSON>. The result in this paper claims that this problem is EXPTIME-complete.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.26"}, {"primary_key": "4470770", "vector": [], "sparse_vector": [], "title": "Complexity Bounds for Sum-Product Logic via Additive Proof Nets and Petri Nets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We investigate efficient algorithms for the additive fragment of linear logic. This logic is an internal language for categories with finite sums and products, and describes concurrent two-player games of finite choice. In the context of session types, typing disciplines for communication along channels, the logic describes the communication of finite choice along a single channel. We give a simple linear time correctness criterion for unit-free propositional additive proof nets via a natural construction on Petri nets. This is an essential ingredient to linear time complexity of the second author's combinatorial proofs for classical logic. For full propositional additive linear logic, including the units, we give a proof search algorithm that is linear-time in the product of the source and target formula, and an algorithm for proof net correctness that is of the same time complexity. We prove that proof search in first-order additive linear logic is NP-complete.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.18"}, {"primary_key": "4470771", "vector": [], "sparse_vector": [], "title": "Domains of Commutative C-Subalgebras.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Operator algebras provide uniform semantics for deterministic, reversible, probabilistic, and quantum computing, where intermediate results of partial computations are given by commutative sub algebras. We study this setting using domain theory, and show that a given operator algebra is scattered if and only if its associated partial order is, equivalently: continuous (a domain), algebraic, atomistic, quasi-continuous, or quasialgebraic. In that case, conversely, we prove that the Lawson topology, modelling information approximation, allows one to associate an operator algebra to the domain.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.49"}, {"primary_key": "4470772", "vector": [], "sparse_vector": [], "title": "Privacy and the Price of Data.", "authors": ["<PERSON>"], "summary": "Several important application areas in data science involve assigning numbers to (possibly randomized) algorithms. In the case of statistical privacy, it is important to quantify the amount of information leaked by a data processing algorithm. In the case of data marketplaces, it is important to properly set the prices for data queries (which, in general, can be specified by arbitrary algorithms). In both cases, it is also important to quantify application-specific utility of the outputs of an algorithm or query. For example, if a user has a choice of purchasing some combination of query answers, the user's decision should be guided by the consideration of a utility/price trade-off. These numbers cannot be assigned to algorithms in an arbitrary manner - there are common-sense restrictions that must be enforced. For example, the answer to an expensive query should not be derivable from a much cheaper query. Each application has its own set of restrictions but when they are formulated mathematically, common patterns emerge. Thus technical results from one area can often be ported to the others.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.10"}, {"primary_key": "4470773", "vector": [], "sparse_vector": [], "title": "Locally Finite Constraint Satisfaction Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "First-order definable structures with atoms are infinite, but exhibit enough symmetry to be effectively manipulated. We study Constraint Satisfaction Problems (CSPs) where both the instance and the template are definable structures with atoms. As an initial step, we consider locally finite templates, which contain potentially infinitely many finite relations. We argue that such templates occur naturally in Descriptive Complexity Theory. We study CSPs over such templates for both finite and infinite, definable instances. In the latter case even decidability is not obvious, and to prove it we apply results from topological dynamics. For finite instances, we show that some central results from the classical algebraic theory of CSPs still hold: the complexity is determined by polymorphisms of the template, and the existence of certain polymorphisms, such as majority or <PERSON><PERSON><PERSON> polymorphisms, guarantees the correctness of classical algorithms for solving finite CSP instances.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.51"}, {"primary_key": "4470774", "vector": [], "sparse_vector": [], "title": "Automata-Based Abstraction Refinement for µHORS Model Checking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The model checking of higher-order recursion schemes (HORS), aka. Higher-order model checking, is the problem of checking whether the tree generated by a given HORS satisfies a given property. It has recently been studied actively and applied to automated verification of higher-order programs. <PERSON><PERSON> and <PERSON><PERSON><PERSON> studied an extension of higher-order model checking called muHORS model checking, where HORS has been extended with recursive types, so that a wider range of programs, including object-oriented programs and multi-threaded programs, can be precisely modeled and verified. Although the muHORS model checking is undecidable in general, they developed a sound but incomplete procedure for muHORS model checking. Unfortunately, however, their procedure was not scalable enough. Inspired by recent progress of (ordinary) HORS model checking, we propose a new procedure for muHORS model checking, based on automata-based abstraction refinement. We have implemented the new procedure and confirmed that it often outperforms the previous procedure.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.71"}, {"primary_key": "4470775", "vector": [], "sparse_vector": [], "title": "Extending ALCQIO with Trees.", "authors": ["<PERSON><PERSON>", "Mantas Simkus", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the description logic ALCQIO, which extends the standard description logic ALC with nominals, inverses and counting quantifiers. ALCQIO is a fragment of first order logic and thus cannot define trees. We consider the satisfiability problem of ALCQIO over finite structures in which k relations are interpreted as forests of directed trees with unbounded out degrees. We show that the finite satisfiability problem of ALCQIO with forests is polynomial-time reducible to finite satisfiability of ALCQIO. As a consequence, we get that finite satisfiability is NEXPTIME-complete. Description logics with transitive closure constructors or fixed points have been studied before, but we give the first decidability result of the finite satisfiability problem for a description logic that contains nominals, inverse roles, and counting quantifiers and can define trees.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.54"}, {"primary_key": "4470776", "vector": [], "sparse_vector": [], "title": "Universal Covers, Color Refinement, and Two-Variable Counting Logic: Lower Bounds for the Depth.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given a connected graph G and its vertex x, let U(G,x) denote the universal cover of G obtained by unfolding <PERSON> into a tree starting from x. Let T=T(n) be the minimum number such that, for graphs G and H with at most n vertices each, the isomorphism of U(G,x) and U(H,y) surely follows from the isomorphism of these rooted trees truncated at depth T. Motivated by applications in theory of distributed computing, <PERSON> [Discrete Appl. Math. 1995] asks if the value of T(n) is bounded by n. We answer this question in the negative by establishing that T(n)=(2-o(1))n. Our solution uses basic tools of finite model theory such as a bisimulation version of the Immerman-Lander 2-pebble counting game. The graphs G and H we construct for each n to prove the lower bound for T(n) also show some other tight lower bounds. Both having n vertices, G and H can be distinguished in 2-variable counting logic only with quantifier depth (1-o(1))n. It follows that color refinement, the classical procedure used in isomorphism testing and other areas for computing the coarsest equitable partition of a graph, needs (1-o(1))n rounds to achieve color stabilization on each of G and H. Somewhat surprisingly, this number of rounds is not enough for color stabilization on the disjoint union of G and H, where (2-o(1))n rounds are needed.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.69"}, {"primary_key": "4470777", "vector": [], "sparse_vector": [], "title": "Parallelism and Synchronization in an Infinitary Context.", "authors": ["Ugo <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study multitoken interaction machines in the context of a very expressive linear logical system with exponentials, fix points and synchronization. The advantage of such machines is to provide models in the style of the Geometry of Interaction, i.e., An interactive semantics which is close to low-level implementation. On the one hand, we prove that despite the inherent complexity of the framework, interaction is guaranteed to be deadlock-free. On the other hand, the resulting logical system is powerful enough to embed PCF and to adequately model its behaviour, both when call-by-name and when call-by-value evaluation are considered. This is not the case for single-token stateless interactive machines.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.58"}, {"primary_key": "4470778", "vector": [], "sparse_vector": [], "title": "On the Complexity of Linear Arithmetic with Divisibility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the complexity of deciding the truth of first-order existential sentences of linear arithmetic with divisibility over both the integers and the p-adic numbers. We show that if an existential sentence of Presburger arithmetic with divisibility is satisfiable then the smallest satisfying assignment has size at most exponential in the size of the formula, showing that the decision problem for such sentences is in NEXPTIME. Establishing this upper bound requires subtle adaptations to an existing decidability proof of <PERSON><PERSON><PERSON><PERSON>. We consider also the first-order linear theory of the p-adic numbers. Here divisibility can be expressed via the valuation function. The decision problem for existential sentences over the p-adic numbers is an important component of the decision procedure for existential Presburger arithmetic with divisibility. The problem is known to be NP-hard and in EXPTIME, as a second main contribution, we show that this problem lies in the Counting Hierarchy, and therefore in PSPACE.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.67"}, {"primary_key": "4470779", "vector": [], "sparse_vector": [], "title": "Demystifying Reachability in Vector Addition Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "More than 30 years after their inception, the decidability proofs for reachability in vector addition systems (VAS) still retain much of their mystery. These proofs rely crucially on a decomposition of runs successively refined by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, which appears rather magical, and for which no complexity upper bound is known. We first offer a justification for this decomposition technique, by showing that it computes the ideal decomposition of the set of runs, using the natural embedding relation between runs as well quasi ordering. In a second part, we apply recent results on the complexity of termination thanks to well quasi orders and well orders to obtain a cubic Ackermann upper bound for the decomposition algorithms, thus providing the first known upper bounds for general VAS reachability.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.16"}, {"primary_key": "4470780", "vector": [], "sparse_vector": [], "title": "A Cubical Approach to Synthetic Homotopy Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Homotopy theory can be developed synthetically in homotopy type theory, using types to describe spaces, the identity type to describe paths in a space, and iterated identity types to describe higher-dimensional paths.While some aspects of homotopy theory have been developed synthetically and formalized in proof assistants, some seemingly easy examples have proved difficult because the required manipulations of paths becomes complicated.In this paper, we describe a cubical approach to developing homotopy theory within type theory.The identity type is complemented with higher-dimensional cube types, such as a type of squares, dependent on four points and four lines, and a type of three-dimensional cubes, dependent on the boundary of a cube.Path-over-a-path types and higher generalizations are used to describe cubes in a fibration over a cube in the base.These higher-dimensional cube and path-over types can be defined from the usual identity type, but isolating them as independent conceptual abstractions has allowed for the formalization of some previously difficult examples.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.19"}, {"primary_key": "4470781", "vector": [], "sparse_vector": [], "title": "A Fibrational Account of Local States.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One main challenge of the theory of computational effects is to understand how to combine various notions of effects in a meaningful way. Here, we study the particular case of the local state monad, which we would like to express as the result of combining together a family of global state monads parametrized by the number of available registers. To that purpose, we develop a notion of indexed monad which refines and generalizes Power's recent notion of indexed Lawvere theory. One main achievement of the paper is to integrate the block structure necessary to encode allocation as part of the resulting notion of indexed state monad. We then explain how to recover the local state monad from the functorial data provided by our notion of indexed state monad. This reconstruction is based on the guiding idea that an algebra of the indexed state monad should be defined as a section of a 2-categorical notion of fibration associated to the indexed state monad by a <PERSON><PERSON><PERSON><PERSON><PERSON> construction.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.45"}, {"primary_key": "4470782", "vector": [], "sparse_vector": [], "title": "Abstract Hidden Markov Models: A Monadic Account of Quantitative Information Flow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hidden Markov Models, HMM's, are mathematical models of Markov processes whose state is hidden but from which information can leak via channels. They are typically represented as 3-way joint probability distributions. We use HMM's as denotations of probabilistic hidden-state sequential programs, after recasting them as \"abstract\" HMM's, i.e. computations in the Giry monad D, and equipping them with a partial order of increasing security. However to encode the monadic type with hiding over state X we use DX→D 2 X rather than the conventional X→DX. We illustrate this construction with a very small Haskell prototype. We then present uncertainty measures as a generalisation of the extant diversity of probabilistic entropies, and we propose characteristic analytic properties for them. Based on that, we give a \"backwards\", uncertainty-transformer semantics for HMM's, dual to the \"forwards\" abstract HMM's. Finally, we discuss the Dalenius desideratum for statistical databases as an issue in semantic compositionality, and propose a means for taking it into account.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.61"}, {"primary_key": "4470783", "vector": [], "sparse_vector": [], "title": "A Unifying Approach to the Gamma Question.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Gamma question was formulated by <PERSON> et al. In \"Asymptotic density, computable trace ability and 1-randomness\" (2013, available at http://www.math.wisc.edu/~lempp/papers/traceable.pdf). It is related to the recent notion of coarse computability which stems from complexity theory. The Gamma value of an oracle set measures to what extent each set computable with the oracle is approximable, in the sense of density, by a computable set. The closer to 1 this value is, the closer the oracle is to being computable. The Gamma question asks whether this value can be strictly in between 0 and 1/2. We say that an oracle is weakly Schnorr engulfing if it computes a Schnorr test that succeeds on all computable reals. We show that each non weakly Schnorr engulfing oracle has a Gamma value of at least 1/2. Together with a recent result of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, this establishes new examples of such oracles. We also give a unifying approach to oracles with Gamma value 0. We say that an oracle is infinitely often equal with bound h if it computes a function that agrees infinitely often with each computable function bounded by h. We show that every oracle which is infinitely equal with bound 2dn for d>1 has a Gamma value of 0. This provides new examples of such oracles as well. We present a combinatorial characterization of being weakly Schnorr engulfing via traces, which is inspired by the study of cardinal characteristics in set theory.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.60"}, {"primary_key": "4470784", "vector": [], "sparse_vector": [], "title": "Polarised Intermediate Representation of Lambda Calculus with Sums.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The theory of the λ-calculus with extensional sums is more complex than with only pairs and functions. We propose an untyped representation-an intermediate calculus-for the λ-calculus with sums, based on the following principles: 1) Computation is described as the reduction of pairs of an expression and a context; the context must be represented inside-out, 2) operations are represented abstractly by their transition rule, 3) Positive and negative expressions are respectively eager and lazy; this polarity is an approximation of the type. We offer an introduction from the ground up to our approach, and we review the benefits. A structure of alternating phases naturally emerges through the study of normal forms, offering a reconstruction of focusing. Considering further purity assumption, we obtain maximal multifocusing. As an application, we can deduce a syntax-directed algorithm to decide the equivalence of normal forms in the simply-typed λ-calculus with sums, and justify it with our intermediate calculus.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.22"}, {"primary_key": "4470785", "vector": [], "sparse_vector": [], "title": "Bisimilarity in Fresh-Register Automata.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Register automata are a basic model of computation over infinite alphabets.Fresh-register automata extend register automata with the capability to generate fresh symbols in order to model computational scenarios involving name creation.This paper investigates the complexity of the bisimilarity problem for classes of register and fresh-register automata.We examine all main disciplines that have appeared in the literature: general register assignments; assignments where duplicate register values are disallowed; and assignments without duplicates in which registers cannot be empty.In the general case, we show that the problem is EXPTIME-complete.However, the absence of duplicate values in registers enables us to identify inherent symmetries inside the associated bisimulation relations, which can be used to establish a polynomial bound on the depth of Attacker-winning strategies.Furthermore, they enable a highly succinct representation of the corresponding bisimulations.By exploiting results from group theory and computational group theory, we can then show solvability in PSPACE and NP respectively for the latter two register disciplines.In each case, we find that freshness does not affect the complexity class of the problem.The results allow us to close a complexity gap for language equivalence of deterministic register automata.We show that deterministic language inequivalence for the no-duplicates fragment is NP-complete, which disproves an old conjecture of <PERSON><PERSON><PERSON>.Finally, we discover that, unlike in the finite-alphabet case, the addition of pushdown store makes bisimilarity undecidable, even in the case of visibly pushdown storage.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.24"}, {"primary_key": "4470786", "vector": [], "sparse_vector": [], "title": "From Categorical Logic to Facebook Engineering.", "authors": ["Peter <PERSON>;Hearn"], "summary": "I chart a line of development from category-theoretic models of programs and logics to automatic program verification/analysis techniques that are in deployment at Facebook. Our journey takes in a number of concepts from the computer science logician's toolkit -- including categorical logic and model theory, denotational semantics, the Curry-Howard isomorphism, sub structural logic, Hoare Logic and Separation Logic, abstract interpretation, compositional program analysis, the frame problem, and abductive inference.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.11"}, {"primary_key": "4470787", "vector": [], "sparse_vector": [], "title": "Higher-Order Model Checking: An Overview.", "authors": ["<PERSON>"], "summary": "Higher-order model checking is about the model checking of trees generated by recursion schemes. The past fifteen years or so have seen considerable progress in both theory and practice. Advances have been made in determining the expressive power of recursion schemes and other higher-order families of generators, automata-theoretic characterisations of these generator families, and the algorithmics and semantics of higher-order model checking and allied methods of formal analysis. Because the trees generated by recursion schemes are computation trees of higher-order functional programs, higher-order model checking provides a foundation for model checkers of such programming languages as Haskell, F# and Erlang. This paper aims to give an overview of recent developments in higher-order model checking.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.9"}, {"primary_key": "4470788", "vector": [], "sparse_vector": [], "title": "Descriptive Set Theory in the Category of Represented Spaces.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose to extend descriptive set theory (DST) beyond its traditional setting of Polish spaces to the represented spaces. There, we can reformulate DST in terms of endofunctors on the categories of represented spaces and computable or continuous functions. In particular, this approach satisfies the demand for a uniform approach to both classic and effective DST -- computability follows naturally from the setting, rather than having to be explicitly demanded. The previous endeavour to extend DST to the Quasi-Polish spaces is subsumed by this work. In several cases the category-theoretic setting enables new, very succinct proofs, and sheds a new light on why certain results are true. The framework lets us make formal some natural questions not easily approachable by traditional methods.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.48"}, {"primary_key": "4470789", "vector": [], "sparse_vector": [], "title": "Names and Symmetry in Computer Science (Invited Tutorial).", "authors": ["<PERSON>"], "summary": "Nominal sets provide a mathematical theory for some of the key concepts that arise when representing and computing with data involving atomic (or 'pure') names: freshness, abstraction and scoping of names, and finiteness modulo symmetry. This tutorial introduces the notion of nominal set and explains selected applications of it to logic in computer science, to automata, languages and programming.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.12"}, {"primary_key": "4470790", "vector": [], "sparse_vector": [], "title": "Separating Regular Languages with Two Quantifiers Alternations.", "authors": ["<PERSON>"], "summary": "We investigate the quantifier alternation hierarchy of first-order logic over finite words. To do so, we rely on the separation problem. For each level in the hierarchy, this problem takes two regular languages as input and asks whether there exists a formula of the level that accepts all words in the first language and no word in the second one. Usually, obtaining an algorithm that solves this problem requires a deep understanding of the level under investigation. We present such an algorithm for the level Σ 3 (formulas having at most 2 alternations beginning with an existential block). We also obtain as a corollary that one can decide whether a regular language is definable by a Σ 4 formula (formulas having at most 3 alternations beginning with an existential block).", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.28"}, {"primary_key": "4470791", "vector": [], "sparse_vector": [], "title": "Distributed Graph Automata.", "authors": ["<PERSON>"], "summary": "Combining ideas from distributed algorithms and alternating automata, we introduce a new class of finite graph automata that recognize precisely the languages of finite graphs definable in monadic second-order logic. By restricting transitions to be nondeterministic or deterministic, we also obtain two strictly weaker variants of our automata for which the emptiness problem is decidable. As an application, we suggest how suitable graph automata might be useful in formal verification of distributed algorithms, using Floyd-Hoare logic.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.27"}, {"primary_key": "4470792", "vector": [], "sparse_vector": [], "title": "Recent Developments in Quantitative Information Flow (Invited Tutorial).", "authors": ["<PERSON>"], "summary": "In computer security, it is frequently necessary in practice to accept some leakage of confidential information. This motivates the development of theories of Quantitative Information Flow aimed at showing that some leaks are \"small\" and therefore tolerable. We describe the fundamental view of channels as mappings from prior distributions on secrets to hyper-distributions, which are distributions on posterior distributions, and we show how g-leakage provides a rich family of operationally-significant measures of leakage. We also discuss two approaches to achieving robust judgments about leakage: notions of capacity and a robust leakage ordering called composition refinement.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.13"}, {"primary_key": "4470793", "vector": [], "sparse_vector": [], "title": "Nondeterminism in Game Semantics via Sheaves.", "authors": ["<PERSON><PERSON>", "C.<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> have developed a fully abstract game model for nondeterministic Idealised Algol and, at the same time, revealed difficulties in constructing game models for stateless nondeterministic languages and infinite nondeterminism. We propose a novel approach in which a strategy is not a set, but a tree, of plays, and develop a fully abstract game model for a nondeterministic stateless language. Mathematically such a strategy is formalised as a sheaf over an appropriate site of plays. We conclude with a study on the difficulties pointed out by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> in terms of the structure of the coverage of the sites.", "published": "2015-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2015.30"}]