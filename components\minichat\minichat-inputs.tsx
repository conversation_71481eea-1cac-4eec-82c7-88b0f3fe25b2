'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Trash2, StopCircle, Bookmark } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { getAllFoldersAndPapers, genAtSuggestions } from "@/components/collect/collect-data-export";
import { Paper } from '@/components/survey/class-utils';
import { INIT_MESSAGE } from './minichat-messages-list';
import { toast } from 'react-hot-toast';
import { getSinglePaperPDFContent } from './minichat-pdf-deal';
import { promptItems } from './minichat-prompt';

interface MiniChatInputsProps {
  papers: Paper[];
  searchQuery: string;
  messages: any[];
  setMessages: React.Dispatch<React.SetStateAction<any[]>>;
  selectedModel: any;
}

export default function MiniChatInputs({
  papers,
  searchQuery,
  messages,
  setMessages,
  selectedModel,
}: MiniChatInputsProps) {
  const DEFAULT_TEXTAREA_ROWS = 1;
  const MAX_TEXTAREA_HEIGHT_PX = 200;
  
  // 文本输入框引用，用于处理输入框高度调整和@建议框定位
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // @ 功能相关状态
  const [atSuggestions, setAtSuggestions] = useState<any[]>([]); // {type, label, value, ...}
  const [atActiveIndex, setAtActiveIndex] = useState(0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentAtTargets, setCurrentAtTargets] = useState<any[]>([]); // 多标签

  const atListRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 标签栏ref用于动态定位建议框
  const tagBarRef = useRef<HTMLDivElement>(null);

  // 最大选择论文数量
  const maxPaperCount = 100;

  // # prompt 相关状态
  const [promptSuggestions, setPromptSuggestions] = useState<any[]>([]); // {title, description, prompt}
  const [promptActiveIndex, setPromptActiveIndex] = useState(0);
  const promptListRef = useRef<HTMLDivElement>(null);

  // 从消息中过滤掉思考内容，用于发送给API
  const getHistoryMessagesForApi = () => {
    // 只取最新的10条消息
    const recentMessages = messages.slice(-10);
    return recentMessages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  };

  // 合并后的 getSystemPrompt 统一生成系统提示词
  const getSystemPrompt = async () => {
    // 基础模板
    const basePrompt = `你是一个专业的学术助手 🎓，可以帮助用户分析 arXiv 论文。

请注意：
1. 任何情况下引用到某些论文时将论文序号比如 [x] 放到最前边，即最左侧 📌
2. 尽可能多使用表情 😊 🧐 🌟 📊 🔬 或者丰富的 markdown 格式使得结果更加易读且亲和✨
3. 如果论文是空的，请提示用户需要先进行检索才能解析结果，如果用户问了不涉及检索论文的其他问题，也可以直接回答 ⚠️
4. 你无法联网 🔒，也无法主动帮助用户检索，若无检索结果，可以根据用户需求给予检索词的建议，并让用户主动去主界面检索框先进行检索 ❌

请用专业但易懂的语言回答 🗣️，并适当引用相关论文的内容 📖。
所有公式都使用 latex 形式格式化。用 $...$ 包裹公式。`;

    // 每次都主动获取最新收藏夹和论文
    const { folders, papers: allPapers } = getAllFoldersAndPapers();
    
    // 获取PDF内容（如果只有一篇论文）
    let pdfContent = '';
    if (currentAtTargets.length == 1) {
      pdfContent = await getSinglePaperPDFContent(allPapers, currentAtTargets);
    }

    // 有 @ 标签时，优先拼接标签相关内容
    if (currentAtTargets.length > 0) {
      let prompt = basePrompt + pdfContent + '\n';
      currentAtTargets.forEach((target: any, idx: number) => {
        if (target.type === 'all') {
          // 遍历主页面所有论文，带 index
          prompt += `\n[主页面全部论文]`;
          prompt += papers.map((p, i) => 
            `\n  [${i + 1}], 标题: ${p.title} 📝 发表时间: ${p.published} 📅 发表源：${p.source || 'arxiv'} 📚 摘要: ${p.abstract_summary || p.summary}`
          ).join('\n');
        } else if (target.type === 'main-paper') {
          // 单个主页面论文，带 index
          const p = target.paper;
          prompt += `\n[主页面检索结果论文], 序号 [${target.idx}], 标题: ${p.title} 📝 发表时间: ${p.published} 📅 发表源：${p.source || 'arxiv'} 📚 摘要: ${p.abstract_summary || p.summary}`;
        } else if (target.type === 'folder') {
          // 遍历文件夹下所有论文，带 index
          const folderPapers: any[] = allPapers.filter((p: any) => p.folderId === target.value);
          prompt += `\n[收藏夹：${target.folder.name}] 包含如下论文：\n`;
          prompt += folderPapers.map((p: any, i: number) => 
            `序号 [${i + 1}], 标题: ${p.customName || p.title_translation || p.title} 📝 发表时间: ${p.published} 📅 发表源：${p.source} 📚 摘要: ${p.abstract_summary || p.summary}`
          ).join('\n');
        } else if (target.type === 'collected-paper') {
          // 单个收藏夹内论文，直接用 folderIndex
          const p: any = target.paper;
          const folder = folders.find((f: any) => f.id === p.folderId);
          prompt += `\n[收藏夹：${folder ? folder.name : '未分类'}] 内论文，序号 [${p.folderIndex}], 标题: ${p.customName || p.title_translation || p.title} 📝 发表时间: ${p.published} 📅 发表源：${p.source} 📚 摘要: ${p.abstract_summary || p.summary}`;
        }
      });
      return prompt;
    }

    // 没有 @ 标签时，拼接主页面论文信息
    if (papers.length > 0) {
      let prompt = basePrompt + `\n用户的查询词是：${searchQuery || 'none'} 🔍\n以下是根据用户查询词检索到的论文信息，已经按照相关度从高到低排序：\n`;
      prompt += papers.map((paper, index) => `\n  [${index + 1}]. 标题: ${paper.title} 📝 发表时间: ${paper.published} 📅 发表源：${paper.source} 📚 摘要: ${paper.abstract_summary || paper.summary}`).join('\n');
      return prompt;
    } else {
      return basePrompt + '\n用户暂时并未检索任何论文。📭';
    }
  };

  // 在流结束时流添加新消息时的处理
  const addInitialAssistantMessage = () => {
    // 添加一个初始的思考中状态的消息
    setMessages(prev => [...prev, { 
      role: 'assistant', 
      content: '', 
      thinking: '',
      isThinking: true,
      showThinking: true  // 默认展开思考过程
    }]);
  };

  // 监听输入框内容，处理@和#逻辑
  useEffect(() => {
    const atMatch = input.match(/@([\u4e00-\u9fa5\w\d\-\_\s]*)$/);
    if (atMatch) {
      setAtSuggestions(genAtSuggestions(atMatch[1] || '', papers));
      setAtActiveIndex(0);
      setPromptSuggestions([]);
      return;
    }
    const hashMatch = input.match(/#([\u4e00-\u9fa5\w\d\-\_\s]*)$/);
    if (hashMatch) {
      // 只要输入#，就展示全部prompt，可加关键词过滤
      const keyword = hashMatch[1]?.trim();
      let filtered = promptItems;
      if (keyword) {
        filtered = promptItems.filter(item => item.title.includes(keyword) || item.description.includes(keyword));
      }
      setPromptSuggestions(filtered);
      setPromptActiveIndex(0);
      setAtSuggestions([]);
      return;
    }
    setAtSuggestions([]);
    setPromptSuggestions([]);
  }, [input, papers]);

  // 统计当前已选论文标签数量（包括 @ 的文件夹内所有论文）
  const getCurrentPaperCount = () => {
    // 获取所有论文数据
    const { papers: allPapers } = getAllFoldersAndPapers();
    let count = 0;
    currentAtTargets.forEach(t => {
      if (t.type === 'main-paper' || t.type === 'collected-paper') {
        count += 1;
      } else if (t.type === 'folder') {
        // 统计该文件夹下所有论文
        count += allPapers.filter((p: any) => p.folderId === t.value).length;
      }
    });
    return count;
  };

  // 选择@建议，添加到当前标签栏，并关闭@建议列表
  const handleSelectAt = (item: any) => {
    // 限制最多 100 篇论文
    // if ((item.type === 'main-paper' || item.type === 'collected-paper' || item.type === 'all') && getCurrentPaperCount() >= 5) {
    if (getCurrentPaperCount() >= maxPaperCount) {
      // 超过则不添加
      toast.error(`为了更好的提问效果，最多选择 ${maxPaperCount} 篇论文`);
      return;
    }
    // 避免重复
    if (!currentAtTargets.some(t => t.type === item.type && t.value === item.value)) {
      setCurrentAtTargets(prev => [...prev, item]);
    }
    setInput(input.replace(/@([\u4e00-\u9fa5\w\d\-\_\s]*)$/, '')); // 删除@建议
    setAtSuggestions([]); // 关闭@建议列表
  };

  // 删除标签
  const handleRemoveAt = (idx: number) => {
    setCurrentAtTargets(prev => prev.filter((_, i) => i !== idx));
  };

  // @建议列表高亮项自动滚动到可视区
  useEffect(() => {
    if (atListRef.current && atSuggestions.length > 0) {
      const activeItem = atListRef.current.querySelectorAll('.at-suggestion-item')[atActiveIndex];
      if (activeItem) {
        (activeItem as HTMLElement).scrollIntoView({ block: 'nearest', behavior: 'auto' });
      }
    }
  }, [atActiveIndex, atSuggestions]);

  // #建议列表高亮项自动滚动到可视区
  useEffect(() => {
    if (promptListRef.current && promptSuggestions.length > 0) {
      const activeItem = promptListRef.current.querySelectorAll('.prompt-suggestion-item')[promptActiveIndex];
      if (activeItem) {
        (activeItem as HTMLElement).scrollIntoView({ block: 'nearest', behavior: 'auto' });
      }
    }
  }, [promptActiveIndex, promptSuggestions]);

  // 选择prompt，插入到输入框光标处
  const handleSelectPrompt = (item: any) => {
    // 将prompt内容插入到光标处
    if (textareaRef.current) {
      const el = textareaRef.current;
      const start = el.selectionStart;
      const end = el.selectionEnd;
      const newValue = input.slice(0, start - 1) + item.prompt + input.slice(end); // -1去掉#
      setInput(newValue);
      setTimeout(() => {
        el.focus();
        el.selectionStart = el.selectionEnd = start - 1 + item.prompt.length;
      }, 0);
    } else {
      setInput(item.prompt);
    }
    setPromptSuggestions([]);
  };

  // 监听input内容变化，自动调整高度
  useEffect(() => {
    if (textareaRef.current) {
      const el = textareaRef.current;
      el.style.height = 'auto';
      let newHeight = el.scrollHeight;
      if (newHeight > MAX_TEXTAREA_HEIGHT_PX) {
        newHeight = MAX_TEXTAREA_HEIGHT_PX;
      }
      el.style.height = `${newHeight}px`;
    }
  }, [input]);

  // 扩展输入框 onKeyDown 处理#建议选择
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (atSuggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setAtActiveIndex((prev) => (prev + 1) % atSuggestions.length);
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setAtActiveIndex((prev) => (prev - 1 + atSuggestions.length) % atSuggestions.length);
      } else if (e.key === 'Enter') {
        e.preventDefault();
        handleSelectAt(atSuggestions[atActiveIndex]);
      } else if (e.key === 'Escape') {
        setAtSuggestions([]);
      }
    } else if (promptSuggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setPromptActiveIndex((prev) => (prev + 1) % promptSuggestions.length);
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setPromptActiveIndex((prev) => (prev - 1 + promptSuggestions.length) % promptSuggestions.length);
      } else if (e.key === 'Enter') {
        e.preventDefault();
        handleSelectPrompt(promptSuggestions[promptActiveIndex]);
      } else if (e.key === 'Escape') {
        setPromptSuggestions([]);
      }
    } else if (e.key === 'Backspace' && input === '' && currentAtTargets.length > 0) {
      setCurrentAtTargets(prev => [...prev.slice(0, -1)]);
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
    // shift+回车为换行，默认行为即可
  };

  // 点击输入区以外自动关闭@和#建议选择框
  useEffect(() => {
    if (atSuggestions.length === 0 && promptSuggestions.length === 0) return;
    function handleClickOutside(e: MouseEvent) {
      if (
        (atListRef.current && atListRef.current.contains(e.target as Node)) ||
        (promptListRef.current && promptListRef.current.contains(e.target as Node)) ||
        (textareaRef.current && textareaRef.current.contains(e.target as Node))
      ) {
        return;
      }
      setAtSuggestions([]);
      setPromptSuggestions([]);
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [atSuggestions.length, promptSuggestions.length]);

  // 发送消息
  const handleSend = async () => {
    if (!input.trim() && !isStreaming) return;

    // 如果正在流式响应，则中断
    if (isStreaming) {
      console.log('中断流式响应');
      abortControllerRef.current?.abort();
      abortControllerRef.current = null;
      setIsStreaming(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    const userMessage = input.trim();
    setInput('');
    
    // 重置输入框高度
    if (textareaRef.current) {
      textareaRef.current.style.height = `auto`;
    }

    setMessages((prev: any) => [...prev, { role: 'user', content: userMessage }]);
    setIsLoading(true);
    setIsStreaming(true);

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    try {
      const systemPrompt = await getSystemPrompt();
      const response = await fetch('/api/stream-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: userMessage,
          history_messages: getHistoryMessagesForApi(),
          system_prompt: systemPrompt,
          temperature: 0.7,
          model: selectedModel.id,
        }),
        signal,
      });

      if (!response.ok) throw new Error('请求失败');

      const reader = response.body?.getReader();
      if (!reader) throw new Error('无法读取响应');

      // 添加初始的助手消息
      addInitialAssistantMessage();

      let isInThinkingMode = false;
      let thinkingContent = '';
      let regularContent = '';

      while (true) {
        // 检查是否已中断
        if (signal.aborted) {
          console.log('流已被中断');
          break;
        }

        try {
          const { done, value } = await reader.read();
          if (done) break;

          const text = new TextDecoder().decode(value);
          const lines = text.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5);
              
              if (data === '[DONE]') continue;
              
              try {
                const parsed = JSON.parse(data);
                
                if (parsed.content) {
                  // 检查是否是思考内容
                  const thinkMatch = parsed.content.match(/<think>(.*?)<\/think>/s);
                  
                  if (thinkMatch) {
                    // 这是思考内容
                    isInThinkingMode = true;
                    thinkingContent += thinkMatch[1];
                    
                    setMessages((prev: any) => {
                      const newMessages = [...prev];
                      const lastIndex = newMessages.length - 1;
                      if (lastIndex >= 0) {
                        const existingThinking = newMessages[lastIndex].thinking || '';
                        newMessages[lastIndex] = {
                          ...newMessages[lastIndex],
                          thinking: existingThinking + thinkMatch[1],
                          isThinking: true,
                          // 收到思考内容时自动展开
                          showThinking: true
                        };
                      }
                      return newMessages;
                    });
                  } else {
                    // 这是常规内容
                    regularContent += parsed.content;
                    
                    setMessages((prev: any) => {
                      const newMessages = [...prev];
                      const lastIndex = newMessages.length - 1;
                      if (lastIndex >= 0) {
                        newMessages[lastIndex] = {
                          ...newMessages[lastIndex],
                          content: regularContent,
                          isThinking: isInThinkingMode,
                          // 当开始输出正文时，自动关闭思考框
                          showThinking: false
                        };
                      }
                      return newMessages;
                    });
                  }
                }
              } catch (e) {
                console.error('解析响应数据失败:', e);
              }
            }
          }
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') {
            console.log('读取流时中断');
            break;
          }
          throw error;
        }
      }
      
      // 流结束后，移除思考状态但保留思考内容，并自动关闭思考框
      setMessages((prev: any) => {
        const newMessages = [...prev];
        const lastIndex = newMessages.length - 1;
        if (lastIndex >= 0) {
          newMessages[lastIndex] = {
            ...newMessages[lastIndex],
            isThinking: false,
            thinking: newMessages[lastIndex].thinking || thinkingContent,
            showThinking: false
          };
        }
        return newMessages;
      });
      
    } catch (error) {
      // 只有当不是用户主动中断时才显示错误
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('聊天错误:', error);
        setMessages((prev: any) => [...prev, { 
          role: 'assistant', 
          content: '抱歉，发生了错误，请稍后重试。' 
        }]);
      } else {
        console.log('请求被中断:', error);
      }
    } finally {
      setIsStreaming(false);
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  };
  
  // 清空聊天记录
  const handleClear = () => {
    setMessages([{
      role: 'assistant',
      content: INIT_MESSAGE
    }]);
  };

  return (
    <div className="absolute bottom-0 left-0 right-0 px-5 pb-4 bg-gradient-to-t from-background via-background/95 to-transparent rounded-b-2xl">
      {/* 标签栏 */}
      {currentAtTargets.length > 0 && (
        <div ref={tagBarRef} className="flex flex-wrap gap-1 px-1 mb-1" style={{ background: 'transparent', marginBottom: '8px', paddingBottom: 0 }}>
          {currentAtTargets.map((target, idx) => (
            <span key={target.type + '-' + target.value} className="flex items-center bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100 rounded px-2 py-0.5 text-xs max-w-[180px] truncate" title={target.label}>
              {target.type === 'collected-paper' && <Bookmark className="h-3 w-3 mr-1 text-yellow-500" />}
              <span className="truncate">{target.label}</span>
              <button className="ml-1 text-xs text-yellow-700 dark:text-yellow-200 hover:text-red-500" onClick={() => handleRemoveAt(idx)} tabIndex={-1}>&times;</button>
            </span>
          ))}
        </div>
      )}
      {/* @建议列表 */}
      {atSuggestions.length > 0 && (
        <div
          ref={atListRef}
          className="absolute left-5 right-5 z-50 bg-white dark:bg-gray-900 border rounded-lg shadow-lg max-h-60 overflow-y-auto text-xs px-1 py-1"
          style={{ bottom: '100%', marginBottom: '4px' }}
        >
          {atSuggestions.map((item, idx) => (
            <div
              key={item.type + '-' + item.value}
              className={`at-suggestion-item flex justify-between items-center px-2 py-1 cursor-pointer gap-2 ${idx === atActiveIndex ? 'bg-primary/10 dark:bg-primary/20' : ''}`}
              onMouseDown={() => handleSelectAt(item)}
              style={{ fontSize: '13px', lineHeight: '1.2', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
              title={item.label}
            >
              {item.type === 'collected-paper' && <Bookmark className="h-3 w-3 text-yellow-500" />}
              <span className="truncate flex-1">{item.label}</span>
            </div>
          ))}
        </div>
      )}
      {/* #建议列表 */}
      {promptSuggestions.length > 0 && (
        <div
          ref={promptListRef}
          className="absolute left-5 right-5 z-50 bg-white dark:bg-gray-900 border rounded-lg shadow-lg max-h-60 overflow-y-auto text-xs px-1 py-1"
          style={{ bottom: '100%', marginBottom: '4px' }}
        >
          {/* <div className="px-2 py-1 text-[13px] text-gray-500 dark:text-gray-300 border-b mb-1">通用 Prompt 选择（回车插入，支持上下键）</div> */}
          {promptSuggestions.map((item, idx) => (
            <div
              key={item.title}
              className={`prompt-suggestion-item flex flex-col px-2 py-1 cursor-pointer gap-1 ${idx === promptActiveIndex ? 'bg-primary/10 dark:bg-primary/20' : ''}`}
              onMouseDown={() => handleSelectPrompt(item)}
              style={{ fontSize: '13px', lineHeight: '1.2', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
              title={item.description}
            >
              <span className="font-medium truncate">{item.title}</span>
              <span className="text-gray-400 dark:text-gray-500 truncate text-xs">{item.description}</span>
            </div>
          ))}
        </div>
      )}
      {/* 输入框 */}
      <div className="relative bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 rounded-xl shadow-md border border-border/50 flex items-center">
        <Textarea
          ref={textareaRef}
          value={input}
          onChange={(e) => {
            setInput(e.target.value);
            if (textareaRef.current) {
              textareaRef.current.style.height = 'auto';
              let newHeight = textareaRef.current.scrollHeight;
              if (newHeight > MAX_TEXTAREA_HEIGHT_PX) {
                newHeight = MAX_TEXTAREA_HEIGHT_PX;
              }
              textareaRef.current.style.height = `${newHeight}px`;
            }
          }}
          onKeyDown={handleInputKeyDown}
          rows={DEFAULT_TEXTAREA_ROWS}
          placeholder="Shift + Enter 换行，@选择论文，#选择Prompt "
          className="w-full resize-none rounded-xl shadow-sm border-input bg-background px-2 py-2 pb-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 overflow-y-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-track]:my-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-muted-foreground/20 hover:[&::-webkit-scrollbar-thumb]:bg-muted-foreground/30 [&::-webkit-scrollbar]:hidden [&:hover::-webkit-scrollbar]:block [&:focus::-webkit-scrollbar]:block"
          disabled={isLoading && !isStreaming}
        />
        <div className="absolute bottom-2.5 right-2.5 flex gap-1">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleClear} 
            title="清空聊天记录"
            className="h-7 w-7 hover:bg-muted"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button 
            onClick={handleSend} 
            disabled={!input.trim() && !isStreaming}
            variant={isStreaming ? "destructive" : "default"}
            title={isStreaming ? "中断回复" : "发送消息"}
            className="h-7 w-9"
          >
            {isStreaming ? <StopCircle className="h-4 w-4" /> : <Send className="h-4 w-4" />}
          </Button>
        </div>
      </div>
    </div>
  );
}