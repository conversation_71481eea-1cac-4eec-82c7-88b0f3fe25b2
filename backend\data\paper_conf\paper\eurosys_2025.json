[{"primary_key": "161457", "vector": [], "sparse_vector": [], "title": "SuperFE: A Scalable and Flexible Feature Extractor for ML-based Traffic Analysis Applications.", "authors": ["<PERSON><PERSON><PERSON>", "Guanyu Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chunming Hu"], "summary": "The feature extractor component in today's ML-based traffic analysis applications is becoming a key bottleneck. While mainstream software-based approaches can support flexible feature extraction, they fail to scale to multi-100Gbps network speed easily. Meanwhile, hardware-accelerated solutions can scale to high throughput, but cannot flexibly support generic traffic analysis applications. In this paper, we propose SuperFE, a feature extraction framework that allows users to extract traffic features efficiently and flexibly. SuperFE leverages the capabilities of both new-generation programmable switches and SmartNICs, with three key designs. First, SuperFE presents a user-friendly and extensible interface to support customized feature extraction policies, shielding underlying hardware implementation details and complexities. Second, SuperFE introduces a high-performance multi-granularity key-vector cache system in the programmable switches to batch necessary feature metadata for massive amounts of packets. Third, SuperFE exploits the multi-core parallel and hierarchical memory of SoC-based SmartNICs to achieve efficient feature computation with diverse streaming algorithms. Evaluations using our prototype demonstrate that SuperFE enables various state-of-the-art traffic analysis applications to efficiently extract features from multi-100Gbps raw traffic without compromising detection accuracy, and achieves nearly two orders of magnitude higher throughput than the software-based counterparts.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696081"}, {"primary_key": "161458", "vector": [], "sparse_vector": [], "title": "Multiplexing Dynamic Deep Learning Workloads with SLO-awareness in GPU Clusters.", "authors": ["<PERSON><PERSON>", "Chengzhi Lu", "<PERSON><PERSON><PERSON>", "Kejiang Ye", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning (DL) inference services are widely recognized as crucial workloads in large-scale cloud clusters. However, due to the stringent latency requirements, cloud providers often over-provision GPU resources, resulting in underutilization of the available GPU potential. Although co-locating tasks on the same device can enhance utilization, ensuring Service Level Objectives (SLOs) guarantees for multiplexing highly dynamic inference services becomes extremely challenging due to significant resource interference. In this paper, we introduce Mudi, a new SLO-aware system designed to optimize the utilization of GPU resources within large-scale clusters. <PERSON><PERSON> achieves this by efficiently multiplexing DL inference services with training tasks through spatial sharing. The fundamental concept behind <PERSON><PERSON> involves profiling the latency of inference services using a piece-wise linear function that accurately captures resource interference. Leveraging this quantification of interference, <PERSON><PERSON> designs a scalable cluster-wide co-location policy, determining the optimal multiplexing of training tasks and inference services to maximize resource efficiency. Furthermore, <PERSON><PERSON> incorporates adaptive batching and resource scaling mechanisms to rapidly adapt to the dynamic workloads. Experimental results demonstrate that <PERSON><PERSON> improves 42% of GPU resource utilization and achieves up to 2.27× higher training efficiency while satisfying inference SLOs, as compared to state-of-the-art multiplexing methods.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696074"}, {"primary_key": "161459", "vector": [], "sparse_vector": [], "title": "Hourglass: Enabling Efficient Split Federated Learning with Data Parallelism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Federated learning (FL) has emerged as a promising solution for training machine learning (ML) models with privacy preservation. One of the key challenges is the computational burden on clients caused by training large-sized models. To tackle this challenge, researchers are trying to incorporate split learning into federated learning so that an ML model can be partitioned into two parts, one for training on clients and the other on a cloud server or an edge server. In current split FL systems, each client's server-side model partition is trained with an individual GPU on the fed server before model aggregation. This demands massive GPU resources and does not scale in real-world scenarios. This paper presents Hourglass, a new split FL system that trains clients' server-side model partitions on multiple GPUs with data parallelism. Unlike existing systems that maintain one model partition for each client and pass clients' intermediate features through corresponding model partitions, Hourglass maintains model partitions shared by clients and passes their intermediate features through GPUs in groups based on their differences. In this way, Hourglass prevents the overhead incurred by swapping model partitions in and out of GPUs and improves knowledge sharing between clients. Extensive experiments are conducted on four widely-used public datasets to evaluate the performance of Hourglass. The results demonstrate that, compared to state-of-the-art systems, Hourglass accelerates model convergence by up to 35.2x, and improves model accuracy by up to 9.28%.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717467"}, {"primary_key": "161460", "vector": [], "sparse_vector": [], "title": "OHMiner: An Overlap-centric System for Efficient Hypergraph Pattern Mining.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ligang He", "<PERSON>", "Minzhi Cai", "<PERSON><PERSON> Dai", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hypergraph Pattern Mining (HPM) aims to identify all the instances of user-interested subhypergraphs (patterns) in hypergraphs, which has been widely used in various applications. However, existing solutions either need significant enumeration overhead because they extend subhypergraphs at the granularity of vertices, or suffer from massive redundant computations because they often need to repeatedly fetch and process the same incident hyperedges for different vertices. This paper presents an overlap-centric system named <PERSON>Miner to efficiently support HPM. OHMiner proposes an overlap-centric execution model to determine the subhypergraphs isomorphism through computing and comparing overlaps among hyperedges using set operations. This model aims to efficiently handle the vertices that collectively share the same incident hyperedges. To automatically and precisely retrieve an arbitrary pattern's overlapping semantics without performing redundant set computations, OHMiner further proposes a redundancy-free compiler, which constructs an Overlap Intersection Graph (OIG) for the pattern, optimizes the OIG, and generates an overlap-centric execution plan to guide the procedure of HPM. Moreover, OHMiner designs an overlap-centric parallel execution engine, which adopts an incremental overlap-pruned approach to fast validate candidates for HPM. Additionally, it proposes a degree-aware data store to support efficient generation of candidates. Through evaluating <PERSON><PERSON><PERSON> on a broad range of real-world hypergraphs with various patterns, our experimental results show that <PERSON>Miner outperforms the state-of-the-art HPM system by 5.4×-22.2×.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717474"}, {"primary_key": "161461", "vector": [], "sparse_vector": [], "title": "Ra<PERSON>: Secure Fast I/O Primitives Across Trust Boundaries on Intel SGX.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The use of Intel® Software Guard Extensions (SGX) offers robust security measures for shielding applications in untrusted environments. However, the performance overhead experienced by IO-intensive applications within SGX limits widespread adoption. Prior approaches have proposed the use of userspace kernel-bypass libraries such as Data Plane Development Kit (DPDK) inside SGX enclaves to enable direct access to IO devices. However, these solutions often come at the cost of increasing the Trusted Computing Base (TCB) size, expanding the attack surface, and complicating deployment. In this paper, we introduce Rakis, a comprehensive system that securely enables SGX enclave programs to leverage fast IO Linux kernel primitives without modifying user applications. <PERSON><PERSON> prioritizes the security of its TCB components by employing rigorous software testing and verification methods, embodying a security-by-design approach. Importantly, <PERSON><PERSON> achieves performance advantages without sacrificing TCB size or introducing deployment intricacies and demonstrates significant improvements in benchmark tests with a 4.6x increase in UDP network throughput compared to state-of-the-art SGX enclave LibOS (Gramine-SGX). To demonstrate the practical applicability of <PERSON><PERSON>, we evaluate its performance on four real-world programs showcasing an average performance improvement of 2.8x compared to Gramine-SGX across all workloads.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696090"}, {"primary_key": "161462", "vector": [], "sparse_vector": [], "title": "Towards Efficient Flash Caches with Emerging NVMe Flexible Data Placement SSDs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "NVMe Flash-based SSDs are widely deployed in data centers to cache working sets of large-scale web services. As data centers face increasing sustainability demands, such as reduced carbon emissions, efficient management of Flash overprovisioning and endurance has become crucial. Our analysis demonstrates that mixing data with different lifetimes on Flash blocks results in high device garbage collection costs, which either reduce device lifetime or necessitate host overprovisioning. Targeted data placement on Flash to minimize data intermixing and thus device write amplification shows promise for addressing this issue. The NVMe Flexible Data Placement (FDP) proposal is a newly ratified technical proposal aimed at addressing data placement needs while reducing the software engineering costs associated with past storage interfaces, such as ZNS and Open-Channel SSDs. In this study, we explore the feasibility, benefits, and limitations of leveraging NVMe FDP primitives for data placement on Flash media in CacheLib, a popular open-source Flash cache widely deployed and used in Meta's software ecosystem as a caching building block. We demonstrate that targeted data placement in CacheLib using NVMe FDP SSDs helps reduce device write amplification, embodied carbon emissions, and power consumption with almost no overhead to other metrics. Using multiple production traces and their configurations from Meta and Twitter, we show that an ideal device write amplification of ~1 can be achieved with FDP, leading to improved SSD utilization and sustainable Flash cache deployments.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696091"}, {"primary_key": "161463", "vector": [], "sparse_vector": [], "title": "BESA: Extending Bugs Triggered by Runtime Testing via Static Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to limited test cases and execution scenarios, runtime testing often has insufficient code coverage and thus misses many real bugs. To tackle this problem, we propose a novel idea that static analysis of the triggered bug in runtime testing can help extend and detect extra bugs missed by runtime testing. Based on this idea, we develop a new approach named BESA, which can extend null-pointer dereferences found by runtime testing via static analysis. It first collects trace information about the triggered bug in runtime testing, by monitoring PoC (Proof of Concept) execution or analyzing existing failure log. Then, with this trace information, BESA uses a backward propagation analysis based on the call stack of the triggered bug, to effectively identify source variables propagating problematic value to the buggy variable. Finally, according to each source variable, BESA uses a summary-based alias-aware analysis to efficiently track target variables aliased with the buggy variable for detecting extra bugs. We have evaluated BESA on 25 known null-pointer dereferences found by runtime testing in four popular programs (SQLite, VIM, GPAC and Linux kernel). BESA finds 57 extra bugs, and 18 of them are new bugs that have been confirmed.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696089"}, {"primary_key": "161464", "vector": [], "sparse_vector": [], "title": "Eva: Cost-Efficient Cloud-Based Cluster Scheduling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cloud computing offers flexibility in resource provisioning, allowing an organization to host its batch processing workloads cost-efficiently by dynamically scaling the size and composition of a cloud-based cluster - a collection of instances provisioned from the cloud. However, existing schedulers fail to minimize total cost due to suboptimal task and instance scheduling strategies, interference between co-located tasks, and instance provisioning overheads. We present <PERSON>, a scheduler for cloud-based clusters that reduces the overall cost of hosting long-running batch jobs. Eva leverages reservation price from economics to derive the optimal set of instances to provision and task-to-instance assignments. Eva also takes into account performance degradation when co-locating tasks and quantitatively evaluates the trade-off between short-term migration overhead and long-term provision savings when considering a change in cluster configuration. Experiments on AWS EC2 and large-scale trace-driven simulations demonstrate that Eva reduces costs by 42% while incurring only a 15% increase in JCT, compared to provisioning a separate instance for each task.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717483"}, {"primary_key": "161465", "vector": [], "sparse_vector": [], "title": "Groot: Graph-Centric Row Reordering with Tree for Sparse Matrix Multiplications on Tensor Cores.", "authors": ["<PERSON><PERSON><PERSON>", "Jiadong Xie", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sparse matrix multiplications are essential in scientific computing and machine learning applications. Recent researches offload sparse operations, such as sparse matrix-matrix multiplication (SpMM) and sampled dense-dense matrix multiplication (SDDMM), on Tensor Cores (TCs) for improved performance. However, their performance is often limited by the matrix's inherent sparsity and irregularity. In this paper, we find row reordering can potentially improve sparse operations on TCs, but existing reordering techniques exhibit limitations that hinder their effectiveness. To address the issues, we propose Groot, a graph-centric row reordering algorithm with tree. Groot aims to minimize row differences across the matrix, which is proved to be a NP-hard problem. To approximate the optimal solution, <PERSON><PERSON> firstly captures the local structure of the sparse matrix by constructing a k-nearest neighbor graph, where rows are represented as nodes. Then, it extracts a minimum spanning tree from the constructed graph for global structure optimization. Lastly, <PERSON><PERSON> traverses the extracted tree to obtain the final ordering. We evaluate <PERSON><PERSON> using real-world datasets in comparison with state-of-the-art reordering algorithms. Our results show that <PERSON>root significantly enhances the computational intensity of SpMM and SDDMM on TCs, delivering the average speedups of 1.8× and 2.0×, respectively. Furthermore, the performance gains extend broadly to sparse computations on CUDA cores and GNN systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717460"}, {"primary_key": "161466", "vector": [], "sparse_vector": [], "title": "Phantom: Virtualizing Switch Register Resources for Accurate Sketch-based Network Measurement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xi Sun", "<PERSON><PERSON>", "Hongyang Du", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sketches have proven to be useful for measuring traffic. They store measurement results in the registers of data plane switches. However, they suffer from the short of switch register resources, limiting their measurement accuracy. We propose Phantom, a framework that virtualizes register resources on programmable switches. We observe that in addition to original register resources, switches also offer the capability of data recirculation. Such a capability can be utilized to store some measurement data of sketches, enabling Phantom to realize virtual registers. We realize this insight by building <PERSON> on a 64x100 Gbps Tofino switch. Our testbed experiments indicate that Phantom can virtualize O(106) registers under Tbps-level traffic, which brings up to 86% accuracy improvement to sketches.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696077"}, {"primary_key": "161467", "vector": [], "sparse_vector": [], "title": "Themis: Finding Imbalance Failures in Distributed File Systems via a Load Variance Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yu <PERSON>"], "summary": "A distributed file system (DFS) is a file system that spans across multiple file servers or multiple locations. The load balancing mechanism in a DFS is crucial, as it optimizes resource utilization across all nodes and improves response times. However, incorrect load scheduling or implementation errors in load balancing algorithms can lead to system imbalance, hang-ups, and even crashes. Such imbalance failures may be critical and pose a significant threat to the availability and security of distributed file systems. This paper presents a detailed study of real-world imbalance failures in four widely used DFSes, exploring their symptoms and triggering conditions. We found that test cases that incorporate both client requests and system configuration inputs are crucial for exposing these imbalances. However, generating such high-quality test cases is challenging due to the extensive combinations of these two input spaces. Guided by our study, we designed a testing framework named Themis. To efficiently prune the search space, Themis first models both the request and configuration inputs and transforms them into operation sequences. It then employs load variance-guided fuzzing to thoroughly explore the operation sequence and constantly generate test cases that make nodes loaded as differently as possible. Finally, Themis introduces a load detector to monitor the resource usage of each distributed node and precisely identify any imbalances. Themis has detected 10 new imbalance failures in four real-world DFSes, which have been addressed by the respective maintainers.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696082"}, {"primary_key": "161468", "vector": [], "sparse_vector": [], "title": "Marlin: Enabling High-Throughput Congestion Control Testing in Large-Scale Networks.", "authors": ["Yanqing Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cloud providers require high-throughput traffic to test the effectiveness of congestion control (CC) configurations (i.e., CC algorithm selection and their parameter settings) in networks. A network tester capable of evaluating CC configurations needs to fulfill the following requirements: (R1) Capable of generating traffic with CC behaviors. (R2) Ability to customize CC algorithms. (R3) High throughput CC traffic generation. However, existing network testers fail to meet these requirements simultaneously. The paper presents <PERSON><PERSON>, a novel high-throughput network tester designed for CC evaluation. <PERSON><PERSON> leverages a high-throughput, low-programmability device to amplify the traffic generated by a low-throughput, high-programmability device. The low-throughput device is responsible for complex computational tasks, such as running CC and flow scheduling algorithms, and communicates with the high-throughput device at a high frequency using small packets to instruct it to generate high-throughput traffic with CC behaviors. This hybrid approach allows for customizable, high-throughput CC testing. Our experiments demonstrate that <PERSON><PERSON> can accurately emulate CC behaviors and replicate real-world scenarios. <PERSON><PERSON> can generate 1.2 Tbps of CC traffic using a single programmable switch pipeline and one 100 Gbps port of an FPGA NIC, supporting up to 65,536 concurrent flows.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717486"}, {"primary_key": "161469", "vector": [], "sparse_vector": [], "title": "Seal: Towards Diverse Specification Inference for Linux Interfaces from Security Patches.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Wensheng Tang", "<PERSON>"], "summary": "Linux utilizes interfaces as communication protocols across different subsystems while ensuring manageability. These interfaces standardize interactions between various subsystems; however, the absence of complete calling contexts can result in the mishandling of data from other entities, i.e., interaction data, thus incurring vulnerabilities. Even worse, the effectiveness of static bug detectors could be severely hindered due to the lack of interface specifications. Previous solutions, seeking to automate the inference of interface specifications, are tailored to a subset of the interaction data behavior and, hence are deficient in generalizability. This research presents Seal, a framework that leverages security patches to achieve the automatic inference of diverse interface specifications. Those specifications, formulated as value-flow properties, could adeptly characterize interaction data behaviors for individual interfaces and the synergistic relationships among multiple interfaces. Technically, Seal assesses the impact of code changes in program dependencies, abstracts specifications from changed value-flow paths, and detects bugs via reachability analysis. Experiments show Seal attains a precision of 71.9% and the specifications could accommodate various bug types. We utilized Seal to identify 167 unseen bugs in Linux, hidden for an average of 7.7 years. So far, 95 of them are confirmed by Linux maintainers, 56 of which fixed by our patches.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717487"}, {"primary_key": "161470", "vector": [], "sparse_vector": [], "title": "Towards VM Rescheduling Optimization Through Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Yun<PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern industry-scale data centers need to manage a large number of virtual machines (VMs). Due to the continual creation and release of VMs, many small resource fragments are scattered across physical machines (PMs). To handle these fragments, data centers periodically reschedule some VMs to alternative PMs, a practice commonly referred to as VM rescheduling. Despite the increasing importance of VM rescheduling as data centers grow in size, the problem remains understudied. We first show that, unlike most combinatorial optimization tasks, the inference time of VM rescheduling algorithms significantly influences their performance, due to dynamic VM state changes during this period. This causes existing methods to scale poorly. Therefore, we develop a reinforcement learning system for VM rescheduling, VMR2L, which incorporates a set of customized techniques, such as a two-stage framework that accommodates diverse constraints and workload conditions, a feature extraction module that captures relational information specific to rescheduling, as well as a risk-seeking evaluation enabling users to optimize the trade-off between latency and accuracy. We conduct extensive experiments with data from an industry-scale data center. Our results show that VMR2L can achieve a performance comparable to the optimal solution but with a running time of seconds. Code12 and datasets3 are open-sourced.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717476"}, {"primary_key": "161471", "vector": [], "sparse_vector": [], "title": "PET: Proactive Demotion for Efficient Tiered Memory Management.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ko", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tiered memory is a promising approach for increasing main-memory capacity at a lower cost by using DRAM as the upper tier (fast memory) and slower-but-cheap byte-addressable memory as the lower tier (slow memory). A proactive demotion, one of the ways to use tiered memory efficiently, demotes cold data to slow memory even when fast memory has sufficient free space. Prior works have utilized proactive demotion to reduce the high cost of main memory by reducing applications' resident set size in fast memory. Further, proactive demotion helps mitigate severe performance degradation caused by fast memory shortages when there is a spike in demand for hot data. Still, we observe that leveraging memory access locality within the allocation units of applications enables larger fast-memory savings with lower system overhead. We propose a new proactive demotion scheme, PET, which performs proactive demotion for efficient tiered memory management. PET proposes extending the unit of demotion and promotion from the OS page, adopted by prior works, to PET-block (P-block), which reflects the unit in which applications allocate memory. We also provide the mechanisms that carefully select the demotion target P-block and swiftly promote the demoted P-block when the access pattern changes. The prototype of PET on Linux kernel v6.1.44 reduces 39.8% (up to 80.4%) of fast-memory usage with only a 1.7% performance drop on average of the evaluated workloads. Also, it mitigates 31% performance slowdown compared to the default Linux kernel when the system's memory usage is larger than fast-memory capacity, which outperforms state-of-the-art schemes for tiered memory management.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717471"}, {"primary_key": "161472", "vector": [], "sparse_vector": [], "title": "SpInfer: Leveraging Low-Level Sparsity for Efficient Large Language Model Inference on GPUs.", "authors": ["R<PERSON><PERSON> Fan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have demonstrated remarkable capabilities, but their immense scale poses significant challenges in terms of both memory and computational costs. While unstructured pruning offers promising solutions by introducing sparsity to reduce resource requirements, realizing its benefits in LLM inference remains elusive. This is primarily due to the storage overhead of indexing non-zero elements and the inefficiency of sparse matrix multiplication (SpMM) kernels at low sparsity levels (around 50%). In this paper, we present SpInfer, a high-performance framework tailored for sparsified LLM inference on GPUs. SpInfer introduces Tensor-Core-Aware Bitmap Encoding (TCA-BME), a novel sparse format that minimizes indexing overhead by leveraging efficient bitmap-based indexing, optimized for GPU Tensor Core architectures. Furthermore, SpInfer integrates an optimized SpMM kernel with Shared Memory Bitmap Decoding (SMBD) and asynchronous pipeline design to enhance computational efficiency. Experimental results show that SpInfer significantly outperforms state-of-the-art SpMM implementations (up to 2.14× and 2.27× over Flash-LLM and SparTA, respectively) across a range of sparsity levels (30% to 70%), with substantial improvements in both memory efficiency and end-to-end inference speed (up to 1.58×). SpInfer outperforms highly optimized cuBLAS at sparsity levels as low as 30%, marking the first effective translation of unstructured pruning's theoretical advantages into practical performance gains for LLM inference.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717481"}, {"primary_key": "161473", "vector": [], "sparse_vector": [], "title": "TUNA: Tuning Unstable and Noisy Cloud Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Autotuning plays a pivotal role in optimizing the performance of systems, particularly in large-scale cloud deployments. One of the main challenges in performing autotuning in the cloud arises from performance variability. We first investigate the extent to which noise slows autotuning and find that as little as 5% noise can lead to a 2.5x slowdown in converging to the best-performing configuration. We measure the magnitude of noise in cloud computing settings and find that while some components (CPU, disk) have almost no performance variability, there are still sources of significant variability (caches, memory). Furthermore, variability leads to autotuning finding unstable configurations. As many as 63.3% of the configurations selected as \"best\" during tuning can have their performance degrade by 30% or more when deployed. Using this as motivation, we propose a novel approach to improve the efficiency of autotuning systems by (a) detecting and removing outlier configurations and (b) using ML-based approaches to provide a more stable true signal of de-noised experiment results to the optimizer. The resulting system, TUNA (Tuning Unstable and Noisy Cloud Applications) enables faster convergence and robust configurations. Tuning PostgreSQL running mssales, an enterprise production workload, we find that TUNA can lead to 1.88x lower running time on average with 2.58x lower standard deviation compared to traditional sampling methodologies.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717480"}, {"primary_key": "161474", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting SQL Function Bugs: Using Simple Boundary Arguments to Trigger Hundreds of DBMS Bugs.", "authors": ["Jingzhou Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhao", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "Built-in SQL functions are crucial in Database Management Systems (DBMSs), supporting various operations and computations across multiple data types. They are essential for querying, data transformation, and aggregation. Despite their importance, the bugs in SQL functions have caused widespread problems in the real world, from system failures to arbitrary code execution. However, the understanding of the bug characteristics is limited. More importantly, conventional function testing methods struggle to generate semantically correct SQL test cases, while DBMS testing efforts are hard to measure built-in SQL functions. This paper presents a comprehensive study of 318 built-in SQL function bugs, shedding light on their characteristics and root causes. Our investigation reveals that 87.4% of these bugs were caused by improper handling of boundary values of arguments. The boundary values of arguments come from three sources: literal values, type castings, and nested functions. By studying the bugs from three sources, we summarized 10 SQL patterns of bug-inducing queries. Moreover, we designed Soft, a testing tool based on the patterns to test seven widely used DBMSs, including PostgreSQL, MySQL, and ClickHouse. Soft discovered and confirmed 132 previously unknown SQL function bugs. The DBMS vendors took these bugs seriously and fixed 97 bugs in three days. For example, the CTO of ClickHouse commented on one bug: \"We must fix it immediately or get rid of this function.\"", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696064"}, {"primary_key": "161475", "vector": [], "sparse_vector": [], "title": "RoboRebound: Multi-Robot System Defense with Bounded-Time Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Byzantine Fault Tolerance (BFT) is a classic technique for defending distributed systems against a wide range of faults and attacks. However, existing solutions are designed for systems where nodes can interact only by exchanging messages. They are not directly applicable to systems where nodes have sensors and actuators and can also interact in the physical world - perhaps by blocking each other's path or by crashing into each other. In this paper, we take a first stab at extending BFT to this larger class of systems. We focus on multi-robot systems (MRS), an emerging technology that is increasingly being deployed for applications such as target tracking, warehouse logistics, and exploration. An MRS can consist of dozens of interacting robots and is thus a bona-fide distributed system. The classic masking guarantee is not practical in a MRS, but we propose a variant called bounded-time interaction that can be implemented, and we present an algorithm that achieves it, in combination with a few small hardware tweaks. We built a simulator and prototyped wheeled robots to show that our algorithm is effective, and that it has a reasonable overhead.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696079"}, {"primary_key": "161476", "vector": [], "sparse_vector": [], "title": "Fast State Restoration in LLM Serving with HCache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The growing complexity of LLM usage today, e.g., multi-round conversation and retrieval-augmented generation (RAG), makes contextual states (i.e., KV cache) reusable across user requests. Given the capacity constraints of GPU memory, only a limited number of contexts can be cached on GPU for reusing. Existing inference systems typically evict part of the KV cache and restore it by recomputing it from the original tokens or offloading it to host storage for later retrieval, both of which introduce substantial computational or I/O overheads. We propose HCache, a novel LLM state restoration method. Its key idea is to restore LLM states from intermediate activations and thus utilize computational and I/O resources with low overhead. We enhance HCache with two techniques, including i) a bubble-free restoration scheduler that integrates resource-complementary methods to optimize the balance between computation and IO tasks; and ii) a chunk-based storage manager to address the layout mismatch issue (i.e., layer-before-token saving versus token-before-layer restoration). Our evaluations, conducted using real-world tasks, show that HCache reduces the TTFT by up to 1.93× compared to KV offload while consuming 1.92-2.40× less storage space; compared to token recomputation, HCache achieves up to 5.73× reduction in TTFT.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696072"}, {"primary_key": "161477", "vector": [], "sparse_vector": [], "title": "Collaborative Text Editing with Eg-walker: Better, Faster, Smaller.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Collaborative text editing algorithms allow several users to concurrently modify a text file, and automatically merge concurrent edits into a consistent state. Existing algorithms fall in two categories: Operational Transformation (OT) algorithms are slow to merge files that have diverged substantially due to offline editing; CRDTs are slow to load and consume a lot of memory. We introduce Eg-walker, a collaboration algorithm for text that avoids these weaknesses. Compared to existing CRDTs, it consumes an order of magnitude less memory in the steady state, and loading a document from disk is orders of magnitude faster. Compared to OT, merging long-running branches is orders of magnitude faster. In the worst case, the merging performance of Eg-walker is comparable with existing CRDT algorithms. Eg-walker can be used everywhere CRDTs are used, including peer-to-peer systems without a central server. By offering performance that is competitive with centralised algorithms, our result paves the way towards the widespread adoption of peer-to-peer collaboration software.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696076"}, {"primary_key": "161478", "vector": [], "sparse_vector": [], "title": "Optimizing Task Scheduling in Cloud VMs with Accurate vCPU Abstraction.", "authors": ["<PERSON>", "Weiwei Jia", "<PERSON><PERSON>", "Jianchen Shan"], "summary": "The paper shows that task scheduling in Cloud VMs hasn't evolved quickly to handle the dynamic vCPU resources. The existing vCPU abstraction cannot accurately depict the vCPU dynamics in capacity, activity, and topology, and these mismatches can mislead the scheduler, causing performance degradation and system anomalies. The paper proposes a novel solution, vSched, which probes accurate vCPU abstraction through a set of lightweight microbenchmarks (vProbers) without modifying the hypervisor, and leverages the probed information to optimize task scheduling in cloud VMs with three new techniques: biased vCPU selection, intra-VM harvesting, and relaxed work conservation. Our evaluation of vSched's implementation in x86 Linux Kernel demonstrates that it can effectively improve both system throughput and workload latency across various VM types in the dynamic multi-cloud environment.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696092"}, {"primary_key": "161479", "vector": [], "sparse_vector": [], "title": "MetaHG: Enhancing HGNN Systems Leveraging Advanced Metapath Graph Abstraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON>", "<PERSON>", "Xi<PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jingling Xue"], "summary": "Heterogeneous Graph Neural Networks (HGNNs) are pivotal for extracting semantic and structural information from heterogeneous graphs. Traditional HGNN implementations often grapple with the challenges of excessive metapath instances, requiring substantial storage or incurring high instance-matching overhead. These methods typically suffer from redundant instance encoding and costly semantic graph construction. Addressing these issues, we introduce an advanced Metapath Graph (MG) abstraction that encapsulates the structural information of all metapath instances within a compact representation. This approach significantly reduces storage demands, eliminates redundant instance encodings, and foregoes the need for constructing semantic graphs, thereby facilitating rapid HGNN inference. Our software-based system, MetaHG, leverages layerwise encoding and aggregation to avoid redundancies without the necessity of semantic graphs. It incorporates a fast, lightweight partitioning method to efficiently manage large graphs. Distinctively, MetaHG seamlessly integrates with both dynamic HGNNs and homogeneous GNNs, unlike conventional systems. Comparative evaluations demonstrate that MetaHG surpasses the state-of-the-art BFS- and DFS-based HGNN systems, MAGNN and the software implementation of MetaNMP, by 42.5× and 4.53×, respectively, on average.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717492"}, {"primary_key": "161480", "vector": [], "sparse_vector": [], "title": "FlowCheck: Decoupling Checkpointing and Training of Large-Scale Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jianyuan Lu", "<PERSON><PERSON>", "Biao Lyu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Checkpointing is becoming a hotspot of interest in both academia and industry as the primary fault-tolerance method for large model training. However, existing checkpoint designs are tightly coupled with the training process, leading to interruptions that reduce overall training efficiency. To reduce the impact of checkpoints on training, this paper presents FlowCheck, a novel checkpointing system that decouples checkpoint operations from the training process, enabling checkpoint saving without blocking the training. Specifically, FlowCheck updates the checkpoints by extracting complete gradient information from the network traffic of normal training. FlowCheck deploys a traffic-mirroring network to support this design. To utilize mirrored traffic for checkpointing operations, two key challenges need to be addressed. First, we need to achieve precise identification and extraction of gradient packets from training traffic. Second, the transmission on the mirror link is unreliable due to its inability to trigger retransmission upon packet loss. Through two key designs: (1) packet-counting-based traffic identification, and (2) packet redundancy recovery mechanism, FlowCheck implements an efficient checkpointing system using the existing training network and solves the above two challenges. Experiments and estimations verify that FlowCheck achieves checkpoint operations with zero impact on training, and demonstrate that FlowCheck achieves over 98% effective training time under practical fault conditions.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696088"}, {"primary_key": "161481", "vector": [], "sparse_vector": [], "title": "Solid State Drive Targeted Memory-Efficient Indexing for Universal I/O Patterns and Fragmentation Degrees.", "authors": ["<PERSON><PERSON> I<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Juhyung Park", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Thanks to the advance of device scaling technologies, the capacity of SSDs is rapidly increasing. Such increase, however, comes at the cost of a huge index table requiring large DRAM. To provide reasonable performance with less DRAM, various index structures exploiting locality and regularity of I/O references have been proposed. However, they provide deteriorated performance depending on I/O patterns and storage fragmentation. This paper proposes a novel approximate index structure, called AppL, which combines memory-efficient approximate indices and an LSM-tree that has an append-only and sorted nature. AppL reduces the index size to 6-8-bits per entry, which is considerably smaller than the typical index structures requiring 32-64-bits, and maintains such high memory efficiency irrespective of locality and fragmentation. By alleviating memory pressure, AppL achieves 33.6-72.4% shorter read latency and 28.4%-83.4% higher I/O throughput than state-of-the-art techniques.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717478"}, {"primary_key": "161482", "vector": [], "sparse_vector": [], "title": "A House United Within Itself: SLO-Awareness for On-Premises Containerized ML Inference Clusters via Faro.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper tackles the challenge of running multiple ML inference jobs (models) under time-varying workloads, on a constrained on-premises production cluster. Our system Faro takes in latency Service Level Objectives (SLOs) for each job, auto-distills them into utility functions, \"sloppifies\" these utility functions to make them amenable to mathematical optimization, automatically predicts workload via probabilistic prediction, and dynamically makes implicit cross-job resource allocations, in order to satisfy cluster-wide objectives, e.g., total utility, fairness, and other hybrid variants. A major challenge Faro tackles is that using precise utilities and high-fidelity predictors, can be too slow (and in a sense too precise!) for the fast adaptation we require. Faro's solution is to \"sloppify\" (relax) its multiple design components to achieve fast adaptation without overly degrading solution quality. Faro is implemented in a stack consisting of Ray Serve running atop a Kubernetes cluster. Trace-driven cluster deployments show that Faro achieves 2.3$\\times$-23$\\times$ lower SLO violations compared to state-of-the-art systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696071"}, {"primary_key": "161483", "vector": [], "sparse_vector": [], "title": "Introspective Congestion Control for Consistent High Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON>", "Fengyuan Ren", "<PERSON><PERSON><PERSON>"], "summary": "The congestion control (CC) algorithm is expected to achieve consistent high performance under different network environments. Traditionally, classic CCs are designed with the methodology of inferring path conditions to guide the rate adjustment. However, this methodology suffers from wrong path condition inferences in certain cases, which mislead the rate adjustment and lead to performance degradation. To avoid wrong path condition inferences, we develop the projection-based introspective method and design the introspective congestion control (ICC) algorithm in this paper. Specifically, the rate adjustment rules are designed to possess a specialized profile such that the projection of the profile can be distinguished under unchanged path conditions. In this way, the projection, which can be distinguished from the time series of delay signals in the frequency domain, facilitates ICC to extract more information for path condition inferences. Consequently, with the introspection on the projection, ICC can avoid being misled by wrong path condition inferences and thus achieve consistent high performance under different conditions. The advantages of ICC are confirmed through extensive experiments conducted on various locally emulated scenarios, global testbeds over the Internet, and the Alipay platform.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696084"}, {"primary_key": "161484", "vector": [], "sparse_vector": [], "title": "Serverless Cold Starts and Where to Find Them.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper analyzes a month-long trace of 85 billion user requests and 11.9 million cold starts from Huawei's serverless cloud platform. Our analysis spans workloads from five data centers. We focus on cold starts and provide a comprehensive examination of the underlying factors influencing the number and duration of cold starts. These factors include trigger types, request synchronicity, runtime languages, and function resource allocations. We investigate components of cold starts, including pod allocation time, code and dependency deployment time, and scheduling delays, and examine their relationships with runtime languages, trigger types, and resource allocation. We introduce pod utility ratio to measure the pod's useful lifetime relative to its cold start time, giving a more complete picture of cold starts, and see that some pods with long cold start times have longer useful lifetimes. Our findings reveal the complexity and multifaceted origins of the number, duration, and characteristics of cold starts, driven by differences in trigger types, runtime languages, and function resource allocations. For example, cold starts in Region 1 take up to 7 seconds, dominated by dependency deployment time and scheduling. In Region 2, cold starts take up to 3 seconds and are dominated by pod allocation time. Based on this, we identify opportunities to reduce the number and duration of cold starts using strategies for multi-region scheduling. Finally, we suggest directions for future research to address these challenges and enhance the performance of serverless cloud platforms.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696073"}, {"primary_key": "161485", "vector": [], "sparse_vector": [], "title": "Heimdall: Optimizing Storage I/O Admission with Extensive Machine Learning Pipeline.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ka<PERSON>fi <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces Heimdall, a highly accurate and efficient machine learning-powered I/O admission policy for flash storage, designed to operate in a black-box manner. We make domain-specific innovations in various ML stages by introducing accurate period-based labeling, 3-stage noise filtering, in-depth feature engineering, and fine-grained tuning, which together improve the decision accuracy from 67% up to 93%. We perform various deployment optimizations to reach a sub-μs inference latency and a small, 28KB, memory overhead. With 500 unbiased random experiments derived from production traces, we show Heimdall delivers 15-35% lower average I/O latency compared to the state of the art and up to 2x faster to a baseline. Heimdall is ready for user-level, in-kernel, and distributed deployments.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717496"}, {"primary_key": "161486", "vector": [], "sparse_vector": [], "title": "Daredevil: Rescue Your Flash Storage from Inflexible Kernel Storage Stack.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing kernel storage stacks for NVMe SSDs struggle to address performance interference between I/O requests from tenants with different SLAs, leading to the multi-tenancy issue. Addressing this requires separating their I/O requests within the NVMe I/O queues (NQs). However, our analysis reveals that the static CPU core-NQ bindings of current storage stacks restrict their flexibility to achieve this goal. We propose daredevil, a novel kernel storage stack, which addresses this issue by decoupling the static bindings and allowing full connectivity between cores and NQs. Therefore, it grants multi-tenancy control the flexibility to freely route requests among NQs according to their SLAs. Moreover, it incorporates multi-tenancy-aware scheduling on NQs to facilitate efficient request routing. Our evaluation shows that daredevil can reduce I/O request latency by up to 3-170X compared to current kernel storage stacks, while maintaining comparable throughput.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717482"}, {"primary_key": "161487", "vector": [], "sparse_vector": [], "title": "ParallelEVM: Operation-Level Concurrent Transaction Execution for EVM-Compatible Blockchains.", "authors": ["<PERSON><PERSON>", "Hang Feng", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Blockchain systems, especially EVM-compatible ones that serially execute transactions, face a significant limitation in throughput. One promising solution is concurrent transaction execution, which accelerates transaction processing and increases the overall throughput. However, existing concurrency control algorithms fail to obtain adequate speedups in high-contention blockchain workloads, primarily due to their transaction-level conflict resolution strategies. This paper introduces a novel operation-level concurrency control algorithm tailored for blockchains. The crux of our approach is to ensure that only operations depending on conflicts are executed serially, while permitting concurrent execution of the remaining conflict-free operations. In contrast to conventional approaches that either block or abort an entire transaction upon detecting conflicts, our algorithm integrates a redo phase that identifies and re-executes conflicting operations. To facilitate this, we propose the SSA (static single-assignment) operation log, a mechanism to trace operation dependencies, thereby enabling precise conflict identification and efficient re-execution. Our prototype, ParallelEVM, is evaluated using real-world Ethereum blocks. Experimental results show that ParallelEVM achieves an average speedup of 4.28×, a marked improvement over the 2.49× speedup achieved by optimistic concurrency control.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696063"}, {"primary_key": "161488", "vector": [], "sparse_vector": [], "title": "Jupiter: Pushing Speed and Scalability Limitations for Subgraph Matching on Multi-GPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan"], "summary": "Graph pattern matching (GPM) aims to find subgraphs isomorphic to user-specified patterns within a large graph. Due to its ability to reveal potential relationships among entities in complex networks, it is widely applied in various fields, such as mining molecular structures in bioinformatics, detecting fraud in cloud-based e-commerce, and querying knowledge graphs in large language model. The explosion of data brought by the AI era has rendered traditional GPM systems inadequate for real-world needs. Due to the intricate data dependencies of GPM tasks, most SOTA GPM systems currently have limited scalability and performance, they perform well in small graph mining with single node but cannot scale to modern clusters with GPU acceleration. This paper introduces JUPITER, the first system capable of matching patterns on large graph across multi-node GPU clusters, which can handle graphs 10 times larger than SOTAs with the same memory resources. Its core principle is to delegate computation to the data-residing processing unit rather than pulling data to the computation location, which greatly improves communication efficiency. Experimental results show that JUPITER can reduce communication volume by two orders of magnitude compared to SOTA subgraph matching systems, achieving up to 120× speedup and an average of 21.5× speedup.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717491"}, {"primary_key": "161489", "vector": [], "sparse_vector": [], "title": "Understanding the Linux Kernel, Visually.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Understanding the Linux kernel is challenging due to its large and complex program state. While existing kernel debugging tools provide full access to kernel states at arbitrary levels of detail, developers often spend a significant amount of time sifting through redundant information to find what is truly useful. Additionally, the textual results provided by traditional debuggers are often insufficient for expressing high-dimensional information in a readable manner. This paper presents Visualinux, the first debugging framework that can simplify the program state of the Linux kernel to a level that can be visually understood with low programming complexity and effort. Visualinux includes a domain-specific language for specifying simplifications of a kernel object graph, an SQL-like domain-specific language for customizing the simplified object graph, and a panel-based interactive debugger. Evaluation results show that Visualinux can visualize various complex kernel components and efficiently assist developers in diagnosing sophisticated kernel bugs.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696095"}, {"primary_key": "161490", "vector": [], "sparse_vector": [], "title": "FastIOV: Fast Startup of Passthrough Network I/O Virtualization for Secure Containers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Biao Lyu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Single Root I/O Virtualization (SR-IOV) technology has advanced in recent years and can simultaneously satisfy the network requirements of high data plane performance, high deployment density, and fast startup for applications in traditional containers. However, it falls short with secure containers, which have become the mainstream choice in multi-tenant clouds. SR-IOV requires secure containers to use passthrough I/O for higher data plane performance, which hinders the container startup performance and prevents its usage in time-sensitive tasks like serverless computing. In this paper, we advocate that the startup performance of SR-IOV enabled secure containers can be further boosted, making SR-IOV suitable for building a Container Network Interface (CNI) for secure containers. We first dissect the end-to-end concurrent startup process and identify three key bottlenecks that lead to the slow startup, including Virtual Function I/O device set management, Direct Memory Access memory mapping, and Virtual Function (VF) driver initialization. We then propose a CNI named FastIOV that addresses these bottlenecks through lock decomposition, unnecessary mapping skipping, decoupled zeroing, and asynchronous VF driver initialization. Our evaluation shows that FastIOV reduces the overhead of enabling SR-IOV for secure containers by 96.1%, achieving 65.7% and 75.4% reductions in the average and 99th percentile end-to-end startup time.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696066"}, {"primary_key": "161491", "vector": [], "sparse_vector": [], "title": "Fork: A Dual Congestion Control Loop for Small and Large Flows in Datacenters.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lide Suo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many existing transport designs aim to deliver ultra-low latency and high bandwidth for applications in high-speed datacenter networks. However, almost all of them intertwine the control of small and large flows using the same control entity (e.g., sender or receiver) and congestion feedback signal (e.g., ECN or credit), thus bringing significant performance impairments. By contrast, we seek to decouple the rate control of small flows from that of large ones. To this end, we present Fork, a new datacenter transport that relies on two parallel control loops. One sender-driven small flow control loop (SCP) runs at the highest priority with a multi-flow ACK clocking mechanism to achieve low latencies for small flows. Another receiver-driven low-priority large flow control loop (LCP) employs two simple yet effective mechanisms: ECN migration and AIMD credit control, to protect small flow transmission while gracefully utilizing the spare bandwidth left by SCP. We have implemented a Fork prototype based on DPDK, and shown, through both testbed experiments and simulations, that compared to Homa and Aeolus, Fork reduces the average FCT of small flows by up to 81.4% and 67.7%, respectively, while maintaining lower FCTs for large flows.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696101"}, {"primary_key": "161492", "vector": [], "sparse_vector": [], "title": "Garbage Collection Does Not Only Collect Garbage: Piggybacking-Style Defragmentation for Deduplicated Backup Storage.", "authors": ["<PERSON><PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "Tao Lu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yanqi Pan", "<PERSON><PERSON>"], "summary": "Deduplication is widely used in backup storage and reduces storage overhead by allowing backups to share common data chunks. However, it naturally disrupts the sequential layout of backup images, leading to fragmentation, which slows down backup restoration. Existing solutions to this issue often come with trade-offs, either reducing deduplication effectiveness or introducing significant I/O overhead. In this paper, we propose GCCDF, a novel approach that enhances the efficiency of deduplication-based backup storage. It reorders data as part of garbage collection to eliminate fragmentation, avoiding additional I/O costs. During the reordering, it effectively groups related data for better locality and aligns with the storage layout in backup storage. Evaluation results demonstrate that GCCDF significantly improves restoration speed, offsets data migration overhead, and preserves the deduplication ratio.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717493"}, {"primary_key": "161493", "vector": [], "sparse_vector": [], "title": "Ladon: High-Performance Multi-BFT Consensus via Dynamic Global Ordering.", "authors": ["Hanzheng Lyu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multi-BFT consensus runs multiple leader-based consensus instances in parallel, circumventing the leader bottleneck of a single instance. However, it contains an Achilles' heel: the need to globally order output blocks across instances. Deriving this global ordering is challenging because it must cope with different rates at which blocks are produced by instances. Prior Multi-BFT designs assign each block a global index before creation, leading to poor performance. We propose Ladon, a high-performance Multi-BFT protocol that allows varying instance block rates. Our key idea is to order blocks across instances dynamically, which eliminates blocking on slow instances. We achieve dynamic global ordering by assigning monotonic ranks to blocks. We pipeline rank coordination with the consensus process to reduce protocol overhead and combine aggregate signatures with rank information to reduce message complexity. <PERSON>don's dynamic ordering enables blocks to be globally ordered according to their generation, which respects inter-block causality. We implemented and evaluated <PERSON><PERSON> by integrating it with both PBFT and HotStuff protocols. Our evaluation shows that Ladon-PBFT (resp., Ladon-HotStuff) improves the peak throughput of the prior art by $\\approx$8x (resp., 2x) and reduces latency by $\\approx$62% (resp., 23%), when deployed with one straggling replica (out of 128 replicas) in a WAN setting.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696102"}, {"primary_key": "161494", "vector": [], "sparse_vector": [], "title": "Atlas: Towards Real-Time Verification in Large-Scale Networks via a Native Distributed Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhao", "<PERSON>", "<PERSON><PERSON><PERSON>", "Haifeng Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data plane verification (DPV) can be critical in ensuring the network operates correctly. To be useful in practice, they need to be: (1) fast so as to prevent significant packet loss or security violations; (2) scalable so as to accommodate today's large-scale network architecture. Current DPV tools struggle to meet these requirements due to their centralized architecture. To be concrete, there is a bottleneck for a single-point server to perform real-time DPV tasks. Furthermore, a single-point server makes it hard to collect real-time data plane updates from every device in large-scale networks. This paper proposes Atlas, a native distributed data plane verification (DPV) solution, which systematically solves its scalability problem and improves its real-time performance. To achieve this, (1) Atlas designs a hierarchical distributed architecture for DPV in large-scale networks. (2) Atlas proposes a native approach to maximize the parallelism of the architecture. Both dataset-driven simulation experiments and deployments in the wild demonstrate that Atlas is capable of fast and scalable distributed verification. The advantages offered by Atlas continue to expand as network sizes increase. Compared to state-of-the-art solutions, Atlas is 2-7× faster while keeping each component lightweight.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717494"}, {"primary_key": "161495", "vector": [], "sparse_vector": [], "title": "SkyServe: Serving AI Models across Regions and Clouds with Spot Instances.", "authors": ["Zim<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "Recent years have witnessed an explosive growth of AI models. The high cost of hosting AI services on GPUs and their demanding service requirements, make it timely and challenging to lower service costs and guarantee service quality. While spot instances have long been offered with a large discount, spot preemptions have discouraged users from using them to host model replicas when serving AI models. To address this, we propose a simple yet efficient policy, SpotHedge, that leverages spot replicas across different failure domains (e.g., regions and clouds) to ensure availability, lower costs, and high service quality. SpotHedge intelligently spreads spot replicas across different regions and clouds to improve availability and reduce correlated preemptions, over-provisions cheap spot replicas than required as a safeguard against possible preemptions, and dynamically falls back to on-demand replicas when spot replicas become unavailable. We built SkyServe, a system leveraging SpotHedge to efficiently serve AI models over a mixture of spot and on-demand replicas across regions and clouds. We compared SkyServe with both research and production systems on real AI workloads: SkyServe reduces cost by 43% on average while achieving high resource availability compared to using on-demand replicas. Additionally, SkyServe improves P50, P90, and P99 latency by 2.3×, 2.1×, 2.1× on average compared to other research and production systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717459"}, {"primary_key": "161496", "vector": [], "sparse_vector": [], "title": "Moko: Marrying Python with Big Data Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON>"], "summary": "Python stands as the preferred language for data science, thanks to its user-friendly syntax and a robust ecosystem that effortlessly accommodates a variety of data types and workloads, such as relational/tabular data, tensors, and graphs. While Python thrives in smaller data settings, it struggles to scale in distributed big data environments. MOKO is an IR-based execution framework designed to extend Python's reach into the distributed big data domain by generating code that can utilize existing systems such as Spark, Dask, Torch, and GRAPE. <PERSON><PERSON> preserves Python's key features---interoperability, ease of use, and support for multi-model data types and workloads---while enabling efficient execution in a distributed setting. Our evaluation indicates that MOKO can accelerate Python applications by up to 11× across diverse systems, diminish data alignment overhead by 28×, and outperform hand-optimized solutions by 2.5×.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696100"}, {"primary_key": "161497", "vector": [], "sparse_vector": [], "title": "Empower Vision Applications with LoRA LMM.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Wenming Tu", "<PERSON><PERSON> He", "Rui Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Multimodal Models (LMMs) have shown significant progress in various complex vision tasks with the solid linguistic and reasoning capacity inherited from large language models (LMMs). Low-rank adaptation (LoRA) offers a promising method to integrate external knowledge into LMMs, compensating for their limitations on domain-specific tasks. However, the existing LoRA model serving is excessively computationally expensive and causes extremely high latency. In this paper, we present an end-to-end solution that empowers diverse vision tasks and enriches vision applications with LoRA LMMs. Our system, VaLoRA, enables accurate and efficient vision tasks by 1) an accuracy-aware LoRA adapter generation approach that generates LoRA adapters rich in domain-specific knowledge to meet application-specific accuracy requirements, 2) an adaptive-tiling LoRA adapters batching operator that efficiently computes concurrent heterogeneous LoRA adapters, and 3) a flexible LoRA adapter orchestration mechanism that manages application requests and LoRA adapters to achieve the lowest average response latency. We prototype VaLoRA on five popular vision tasks on three LMMs. Experiment results reveal that VaLoRA improves 24-62% of the accuracy compared to the original LMMs and reduces 20-89% of the latency compared to the state-of-the-art LoRA model serving systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717472"}, {"primary_key": "161498", "vector": [], "sparse_vector": [], "title": "LOFT: A Lock-free and Adaptive Learned Index with High Scalability for Dynamic Workloads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In order to mitigate memory overheads and reduce data movements in the critical path, a computational and space-efficient learned index is promising to deliver high performance and complement traditional index structures, which unfortunately works well only in static workloads. In dynamic workloads, the learned indexes incur performance degradation with poor scalability due to inefficient data placement for newly inserted items and intensive lock contention. Furthermore, the model retraining is time-consuming and blocking, which hampers the performance of index operations. In order to meet the needs of dynamic workloads and efficient retraining, we propose LOFT, a highly scalable and adaptive learned index with lock-free design and self-tuning retraining technique to provide high throughput and low latency. LOFT enables all index operations to be concurrently executed in a lock-free manner by using Compare-and-Swap (CAS) primitive and an expanded learned bucket to handle the overflowed data. To minimize the impact of model retraining, LOFT leverages a shadow node to serve the clients' requests and accelerates the retraining process with the aid of index operations. To accommodate dynamic workloads, LOFT determines when and how to perform retraining based on inferred access patterns. Our extensive evaluation on YCSB and real-world workloads demonstrates that LOFT effectively improves the performance by up to 14× than state-of-the-art designs.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717458"}, {"primary_key": "161499", "vector": [], "sparse_vector": [], "title": "CRAVE: Analyzing Cross-Resource Interaction to Improve Energy Efficiency in Systems-on-Chip.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gul Agha"], "summary": "Mobile platforms make use of dynamic voltage and frequency scaling (DVFS) to trade off runtime performance and power consumption for their systems-on-chip (SoCs). State-of-the-art governors in the OS use application-based characteristics to control the SoC's DVFS settings for CPU cores, as well as the GPU in some SoCs. Through experimental characterization of real-world mobile platforms, we find that key SoC components have a complex relationship with one another, which directly affects their performance and power usage. This relationship is dependent on the architecture of the SoC as it is caused by the interaction of processing elements such as the CPU and GPU through a shared main memory. Unfortunately, existing application-oriented governors do not explicitly capture this design-induced relationship. We propose a new governor, called CRAVE, which uses learned design characteristics to control DVFS settings. At design time, CRAVE identifies optimal DVFS settings for the SoC by sampling points across a multivariate space of frequency settings for the three major mobile system components: CPU cores, GPU, and memory. At runtime, CRAVE monitors resource utilization, in a manner similar to that of the existing simple governors that are built into today's OS kernels, and then applies the previously-learned optimal settings. We implement CRAVE on two real mobile platforms: the ODROID-XU4 and the NVIDIA Jetson TX2. Compared to the best built-in Linux governor, CRAVE improves performance by 20% while reducing energy usage by 16% on the TX2, with similar gains on the XU4. CRAVE also shows an improvement over a state-of-the-art application-driven governor, with performance gains of 16% and energy savings of 10%.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717498"}, {"primary_key": "161500", "vector": [], "sparse_vector": [], "title": "Achilles: Efficient TEE-Assisted BFT Consensus via Rollback Resilient Recovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jiangshan Yu", "<PERSON><PERSON><PERSON>"], "summary": "BFT consensus that uses Trusted Execution Environments (TEEs) to improve the system tolerance and performance is gaining popularity. However, existing works suffer from TEE rollback issues, resulting in a tolerance-performance tradeoff. In this paper, we propose Achilles, an efficient TEE-assisted BFT protocol that breaks the tradeoff. The key idea behind <PERSON> is removing the expensive rollback prevention of TEEs from the critical path of committing transactions. To this end, <PERSON> adopts a rollback resilient recovery mechanism, which allows nodes to assist each other in recovering their states. Besides, <PERSON> follows the chaining spirit in modern chained BFT protocols and leverages customized chained commit rules to achieve linear message complexity, end-to-end transaction latency of four communication steps, and fault tolerance for the minority of Byzantine nodes. Achilles is the first TEE-assisted BFT protocol in line with CFT protocols in these metrics. We implement a prototype of Achilles based on Intel SGX and evaluate it in both LAN and WAN, showcasing its outperforming performance compared to several state-of-the-art counterparts.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717457"}, {"primary_key": "161501", "vector": [], "sparse_vector": [], "title": "HawkSet: Automatic, Application-Agnostic, and Efficient Concurrent PM Bug Detection.", "authors": ["<PERSON>", "João <PERSON>", "<PERSON>"], "summary": "Persistent Memory (PM) enables the development of fast, persistent applications without employing costly HDD/SSD-based I/O operations. Since caches are volatile and CPUs may reorder and stall memory accesses for performance, developers must use low-level instructions to ensure a consistent state in case of a crash. Failure to do so can result in data corruption, data loss, or undefined behavior. In concurrent executions, this exposes a new class of bugs. HawkSet is an automatic, application-agnostic, and efficient tool to detect concurrent PM bugs. HawkSet uses lockset analysis, and automatic binary instrumentation to find all the bugs detected by the state-of-the-art tools and 7 previously unknown bugs. This is achieved without requiring application-specific knowledge or models, nor specialized debugging artifacts or guided executions. Compared to the state-of-the-art, HawkSet offers up to a 159x speedup, and consistently detects harder-to-reach bugs, where a rare interleaving is required.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717477"}, {"primary_key": "161502", "vector": [], "sparse_vector": [], "title": "Multi-Grained Specifications for Distributed System Model Checking and Verification.", "authors": ["<PERSON><PERSON>", "Xudong Sun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ma", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents our experience specifying and verifying the correctness of ZooKeeper, a complex and evolving distributed coordination system. We use TLA+ to model finegrained behaviors of Zoo<PERSON>eeper and use the TLC model checker to verify its correctness properties; we also check conformance between the model and code. The fundamental challenge is to balance the granularity of specifications and the scalability of model checking---fine-grained specifications lead to state-space explosion, while coarse-grained specifications introduce model-code gaps. To address this challenge, we write specifications with different granularities for composable modules, and compose them into mixed-grained specifications based on specific scenarios. For example, to verify code changes, we compose fine-grained specifications of changed modules and coarse-grained specifications that abstract away details of unchanged code with preserved interactions. We show that writing multi-grained specifications is a viable practice and can cope with model-code gaps without untenable state space, especially for evolving software where changes are typically local and incremental. We detected six severe bugs that violate five types of invariants and verified their code fixes; the fixes have been merged to ZooKeeper. We also improve the protocol design to make it easy to implement correctly.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696069"}, {"primary_key": "161503", "vector": [], "sparse_vector": [], "title": "Comprehensive Deadlock Prevention for GPU Collective Communication.", "authors": ["<PERSON><PERSON>", "Juncheng Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Distributed deep neural network training necessitates efficient GPU collective communications, which are inherently susceptible to deadlocks. GPU collective deadlocks arise easily in distributed deep learning applications when multiple collectives circularly wait for each other. GPU collective deadlocks pose a significant challenge to the correct functioning and efficiency of distributed deep learning, and no general effective solutions are currently available. Only in specific scenarios, ad-hoc methods, making an application invoke collectives in a consistent order across GPUs, can be used to prevent circular collective dependency and deadlocks. This paper presents DFCCL, a novel GPU collective communication library that provides a comprehensive approach for GPU collective deadlock prevention while maintaining high performance. DFCCL achieves preemption for GPU collectives at the bottom library level, effectively preventing deadlocks even if applications cause circular collective dependency. DFCCL ensures high performance with its execution and scheduling methods for collectives. Experiments show that DFCCL effectively prevents GPU collective deadlocks in various situations. Moreover, extensive evaluations demonstrate that DFCCL delivers performance comparable to or superior to NCCL, the state-of-the-art collective communication library highly optimized for NVIDIA GPUs.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717466"}, {"primary_key": "161504", "vector": [], "sparse_vector": [], "title": "Pegasus: Transparent and Unified Kernel-Bypass Networking for Fast Local and Remote Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>Oberwagner", "<PERSON>", "<PERSON>"], "summary": "Modern software architectures in cloud computing are highly reliant on interconnected local and remote services. Popular architectures, such as the service mesh, rely on the use of independent services or sidecars for a single application. While such modular approaches simplify application development and deployment, they also introduce significant communication overhead since now even local communication that is handled by the kernel becomes a performance bottleneck. This problem has been identified and partially solved for remote communication over fast NICs through the use of kernel-bypass data plane systems. However, existing kernel-bypass mechanisms challenge their practical deployment by either requiring code modification or supporting only a small subset of the network interface. In this paper, we propose Pegasus, a framework for transparent kernel bypass for local and remote communication. By transparently fusing multiple applications into a single process, Pegasus provides an in-process fast path to bypass the kernel for local communication. To accelerate remote communication over fast NICs, Pegasus uses DPDK to directly access the NIC. Pegasus supports transparent kernel bypass for unmodified binaries by implementing core OS services in user space, such as scheduling and memory management, thus removing the kernel from the critical path. Our experiments on a range of real-world applications show that, compared with Linux, Pegasus improves the throughput by 19% to 33% for local communication and 178% to 442% for remote communication, without application changes. Furthermore, Pegasus achieves 222% higher throughput than Linux for co-located, IO-intensive applications that require both local and remote communication, with each communication optimization contributing significantly.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696083"}, {"primary_key": "161505", "vector": [], "sparse_vector": [], "title": "Chrono: Meticulous Hotness Measurement and Flexible Page Migration for Memory Tiering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Hui", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hong Mei"], "summary": "As the memory demand continues to surge, the limitations of DRAM scalability have spurred the development of various new memory technologies in today's data centers. In order to harness the benefits of the heterogeneous memory architecture, tiering has become a widely adopted memory management paradigm. The effectiveness of a tiered memory management system primarily relies on its ability to accurately identify frequently accessed (\"hot\") pages and infrequently accessed (\"cold\") pages, and efficiently relocate them between tiers. However, existing systems rely on coarse-grained frequency measurement schemes that do not align with the performance characteristics of modern memory devices and memory-intensive applications. Additionally, these systems often incorporate rigid rules or manually configured parameters for page classification, resulting in inflexible migration strategies. This paper introduces Chrono, a novel OS-level tiering system that offers precise characterization of page access frequencies in different tiers and enables efficient migration of hot and cold pages. By leveraging timers instead of counters, Chrono achieves meticulous measurement of hot page access frequency with low overhead. This approach allows Chrono to automatically tune its page classification parameters, leading to flexible migration strategies that adapt to various workloads. Furthermore, Chrono includes a dynamic cold page identification subsystem, which balances the utilization and availability of tiered memory. We have implemented and evaluated Chrono on existing tiered memory platforms, and experimental results demonstrate that Chrono outperforms state-of-the-art tiering systems by a large margin.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717462"}, {"primary_key": "161506", "vector": [], "sparse_vector": [], "title": "Empowering WebAssembly with Thin Kernel Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wasm is gaining popularity outside the Web as a well-specified low-level binary format with ISA portability, low memory footprint and polyglot targetability, enabling efficient in-process sandboxing of untrusted code. Despite these advantages, Wasm adoption for new domains is often hindered by the lack of many standard system interfaces which precludes reusability of existing software and slows ecosystem growth. This paper proposes thin kernel interfaces for Wasm, which directly expose OS userspace syscalls without breaking intra-process sandboxing, enabling a new class of virtualization with Wasm as a universal binary format. By virtualizing the bottom layer of userspace, kernel interfaces enable effortless application ISA portability, compiler backend reusability, and armor programs with Wasm's built-in control flow integrity and arbitrary code execution protection. Furthermore, existing capability-based APIs for Wasm, such as WASI, can be implemented as a Wasm module over kernel interfaces, improving reuse, robustness, and portability through better layering. We present an implementation of this concept for two kernels - Linux and Zephyr - by extending a modern Wasm engine and evaluate our system's performance on a number of sophisticated applications which can run for the first time on Wasm.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717470"}, {"primary_key": "161507", "vector": [], "sparse_vector": [], "title": "SeBS-Flow: Benchmarking Serverless Cloud Function Workflows.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Serverless computing has emerged as a prominent paradigm, with a significant adoption rate among cloud customers. While this model offers advantages such as abstraction from the deployment and resource scheduling, it also poses limitations in handling complex use cases due to the restricted nature of individual functions. Serverless workflows address this limitation by orchestrating multiple functions into a cohesive application. However, existing serverless workflow platforms exhibit significant differences in their programming models and infrastructure, making fair and consistent performance evaluations difficult in practice. To address this gap, we propose the first serverless workflow benchmarking suite SeBS-Flow, providing a platform-agnostic workflow model that enables consistent benchmarking across various platforms. SeBS-Flow includes six real-world application benchmarks and four microbenchmarks representing different computational patterns. We conduct comprehensive evaluations on three major cloud platforms, assessing performance, cost, scalability, and runtime deviations. We make our benchmark suite open-source, enabling rigorous and comparable evaluations of serverless workflows over time. Implementation: https://github.com/spcl/serverless-benchmarks Artifact: https://github.com/spcl/sebs-flow-artifact", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717465"}, {"primary_key": "161508", "vector": [], "sparse_vector": [], "title": "Efeu: generating efficient, verified, hybrid hardware/software drivers for I2C devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Writing device drivers is notoriously hard, and driver bugs are a major cause of system failures and vulnerabilities. The problem is particularly acute in bus-based protocols like I2C, where driver correctness is only half the story: correct functioning of the complete subsystem depends on all components on the bus interoperating correctly. Unfortunately, developers cannot control all aspects of a platform, and must interact with existing devices (peripherals and/or hardware bus controllers) which may misbehave. Failures in a protocol like I2C, often used in critical low-level system management, can result in permanent damage to the hardware, whether a server or a satellite. Existing techniques for creating high assurance drivers rarely tackle this interoperability issue. We present Efeu, a framework for implementing verifiably interoperable drivers for I2C devices. Using model checking-based verification, Efeu generates driver implementations in software, reconfigurable logic for FPGAs, and, notably, combinations of both. The split between software and hardware can be varied at implementation time and the hardware/software interface is generated automatically, enabling efficient exploration of the design space. Using Efeu, we design and evaluate a verified I2C driver stack, and demonstrate that Efeu finds optimal hardware/software tradeoffs to favor either throughput, CPU usage or FPGA footprint. For each objective, Efeu generates drivers with performance comparable with hand-optimized hardware/software drivers.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696093"}, {"primary_key": "161509", "vector": [], "sparse_vector": [], "title": "Flex: Fast, Accurate DNN Inference on Low-Cost Edges Using Heterogeneous Accelerator Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Significant b reakthroughs in machine learning (ML) and the advantages of on-device processing have led to edge devices increasingly incorporating accelerators like GPUs, NPUs, and DSPs. However, these accelerators consume energy, prompting users to limit their floating-point precision. Many edge device users are in regions where including high-fidelity accelerators is too costly, leading to low-cost devices with low precision, sacrificing accuracy. Previous work predetermined layer assignments between the CPU and accelerator offline for high accuracy and low latency without considering the input, but we observe that input affects optimal layer assignment. To address this, we present Flex, a system for Fast, Accurate DNN Inference on Low-Cost Edges using Heterogeneous Accelerator eXecution. Leveraging common observations from models on various edge devices, Flex uses a lightweight heuristic and reinforcement learning (RL) to dynamically assign layers across the CPU and accelerator. Experiments show Flex improves average inference time by up to 39%, accuracy by up to 22%, and energy consumption by up to 61% compared to state-of-the-art methods, and is only 4.2% less optimal than the best achievable results.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696067"}, {"primary_key": "161510", "vector": [], "sparse_vector": [], "title": "Occamy: A Preemptive Buffer Management for On-chip Shared-memory Switches.", "authors": ["Danfeng Shan", "Yunguang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Fengyuan Ren"], "summary": "Today's high-speed switches employ an on-chip shared packet buffer. The buffer is becoming increasingly insufficient as it cannot scale with the growing switching capacity. Nonetheless, the buffer needs to face highly intense bursts and meet stringent performance requirements for datacenter applications. This imposes rigorous demand on the Buffer Management (BM) scheme, which dynamically allocates the buffer across queues. However, the de facto BM scheme, designed over two decades ago, is ill-suited to meet the requirements of today's network. In this paper, we argue that shallow-buffer switches, intense bursts, along with dynamic traffic call for a highly agile BM that can quickly adjust the buffer allocation as traffic changes. However, the agility of the current BM is fundamentally limited by its non-preemptive nature. Nonetheless, we find that preemptive BM, considered unrealizable in history, is now feasible on modern switch chips. We propose Occamy1, a preemptive BM that can quickly adjust buffer allocation. Occamy utilizes the redundant memory bandwidth to actively reclaim and reallocate the over-allocated buffer. Testbed experiments and large-scale simulations show that Occamy can improve the end-to-end performance by up to ~55%.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717495"}, {"primary_key": "161511", "vector": [], "sparse_vector": [], "title": "HybridFlow: A Flexible and Efficient RLHF Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Zilingfeng Ye", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yanghua Peng", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Reinforcement Learning from Human Feedback (RLHF) is widely used in Large Language Model (LLM) alignment. Traditional RL can be modeled as a dataflow, where each node represents computation of a neural network (NN) and each edge denotes data dependencies between the NNs. RLHF complicates the dataflow by expanding each node into a distributed LLM training or generation program, and each edge into a many-to-many multicast. Traditional RL frameworks execute the dataflow using a single controller to instruct both intra-node computation and inter-node communication, which can be inefficient in RLHF due to large control dispatch overhead for distributed intra-node computation. Existing RLHF systems adopt a multi-controller paradigm, which can be inflexible due to nesting distributed computation and data communication. We propose HybridFlow, which combines single-controller and multi-controller paradigms in a hybrid manner to enable flexible representation and efficient execution of the RLHF dataflow. We carefully design a set of hierarchical APIs that decouple and encapsulate computation and data dependencies in the complex RLHF dataflow, allowing efficient operation orchestration to implement RLHF algorithms and flexible mapping of the computation onto various devices. We further design a 3D-HybridEngine for efficient actor model resharding between training and generation phases, with zero memory redundancy and significantly reduced communication overhead. Our experimental results demonstrate 1.53$\\times$~20.57$\\times$ throughput improvement when running various RLHF algorithms using HybridFlow, as compared with state-of-the-art baselines. HybridFlow source code will be available at https://github.com/volcengine/verl.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696075"}, {"primary_key": "161512", "vector": [], "sparse_vector": [], "title": "A Hardware-Software Co-Design for Efficient Secure Containers.", "authors": ["Jiacheng Shi", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "VM-level containers provide strong isolation by running each container with its own kernel in a VM. However, they rely on virtualization hardware designed for general-purpose VMs, causing non-negligible performance overhead compared to OS-level containers. This performance gap widens dramatically in nested virtualization scenarios, where secure containers run inside a VM. This paper proposes CKI (Container Kernel Isolation), a hardware-software co-design for efficient secure containers, based on two insights. First, Protection Keys for Supervisor (PKS) facilitates constructing a new privilege level for securely collocating multiple container kernels within the host kernel, without involving non-root ring-0. Second, the general-purpose virtualization mechanisms used by secure containers provide unnecessary features that exceed the actual isolation requirements of containers, especially the two-stage address translation, which is not required for container isolation, introducing avoidable performance overhead. Thus, CKI avoids using the virtualization hardware for running container kernels and removes the unnecessary virtualization mechanism like two-stage address translation. Instead, it uses PKS to construct a new privilege level for isolating multiple container kernels, which allows more efficient cross-privilege interaction; it also uses single-stage address translation for each container while monitoring the page table updates in a lightweight way to ensure cross-container memory isolation. Our experiments on real-world applications demonstrate the efficiency of CKI, reducing the latency of memory-intensive applications by up to 72% and 47% compared with state-of-the-art hardware-assisted virtualization (HVM) and software-based virtualization (PVM), respectively.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717473"}, {"primary_key": "161513", "vector": [], "sparse_vector": [], "title": "MEPipe: Democratizing LLM Training with Memory-Efficient Slice-Level Pipeline Scheduling on Cost-Effective Accelerators.", "authors": ["Zhenbo Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Guanyu Feng", "<PERSON><PERSON><PERSON>"], "summary": "The training of large language models (LLMs) typically needs costly GPUs, such as NVIDIA A100 or H100. They possess substantial high-bandwidth on-chip memory and rapid interconnects like NVLinks. The exorbitant expenses associated with LLM training pose not just an economic challenge but also a societal one, as it restricts the ability to train LLMs from scratch to a selected few organizations. There is a significant interest in democratizing access to LLM training. This paper explores a potential solution by employing innovative parallel strategies on more affordable accelerators. Budget-friendly options like NVIDIA RTX 4090, while considerably less expensive and comparable in computational power to A100, are hindered by their limited memory capacity and reduced interconnect bandwidth, making the effective training of LLMs challenging. Conventional parallel strategies often result in high communication costs or excessive memory usage. Our paper introduces MEPipe, a novel approach that includes a slice-level scheduling method for sequence pipeline parallelism. This method minimizes memory consumption without incurring additional communication overhead. Besides, MEPipe utilizes fine-grained weight gradient computation to reduce idle time and mitigate imbalanced computation among slices. MEPipe has demonstrated up to 1.68× speedup (1.35× on average) on clusters equipped with 64 NVIDIA 4090 GPUs when training Llama models of varying sizes. 35% Model FLOPS Utilization (MFU) is achieved in training Llama 13B model, being 2.5x more cost-effective than A100 clusters.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717469"}, {"primary_key": "161514", "vector": [], "sparse_vector": [], "title": "DPack: Efficiency-Oriented Privacy Budget Scheduling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Asaf Cidon", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning (ML) models can leak information about users, and differential privacy (DP) provides a rigorous way to bound that leakage under a given budget. This DP budget can be regarded as a new type of computing resource in workloads of multiple ML models training on user data. Once it is used, the DP budget is forever consumed. Therefore, it is crucial to allocate it most efficiently to train as many models as possible. This paper presents a scheduler for the privacy resources that optimizes for efficiency. We formulate privacy scheduling as a new type of multidimensional knapsack problem, called privacy knapsack, which maximizes DP budget efficiency. We show that privacy knapsack is NP-hard, hence practical algorithms are necessarily approximate. We develop an approximation algorithm for privacy knapsack, DPack, and evaluate it on microbenchmarks and on a new, synthetic private-ML workload we developed from the Alibaba ML cluster trace. We show that DPack: (1) often approaches the efficiency-optimal schedule, (2) consistently schedules more tasks compared to a state-of-the-art privacy scheduling algorithm that focused on fairness instead of efficiency (1.3-1.7× in Alibaba, 1.0-2.6X in microbenchmarks), but (3) sacrifices some level of fairness for efficiency. Using DPack, DP ML operators should be able to train more models on the same amount of user data while offering the same privacy guarantee to their users.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696096"}, {"primary_key": "161515", "vector": [], "sparse_vector": [], "title": "Achieving Fairness Generalizability for Learning-based Congestion Control with Jury.", "authors": ["<PERSON>", "Xudong Liao", "Decang Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>nchen Wan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Internet congestion control (CC) has long posed a challenging control problem in networking systems, with recent approaches increasingly incorporating deep reinforcement learning (DRL) to enhance adaptability and performance. Despite promising, DRL-based CC schemes often suffer from poor fairness, particularly when applied to network environments unseen during training. This paper introduces Jury, a novel DRL-based CC scheme designed to achieve fairness generalizability. At its heart, Jury decouples the fairness control from the principal DRL model with two design elements: i) By transforming network signals, it provides a universal view of network environments among competing flows, and ii) It adopts a post-processing phase to dynamically module the sending rate based on flow bandwidth occupancy estimation, ensuring large flows behave more conservatively and smaller flows more aggressively, thus achieving a fair and balanced bandwidth allocation. We have fully implemented Jury, and extensive evaluations demonstrate its robust convergence properties and high performance across a broad spectrum of both emulated and real-world network conditions.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696065"}, {"primary_key": "161516", "vector": [], "sparse_vector": [], "title": "Bingo: Radix-based Bias Factorization for Random Walk on Dynamic Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Random walks are a primary means for extracting information from large-scale graphs. While most real-world graphs are inherently dynamic, state-of-the-art random walk engines failed to efficiently support such a critical use case. This paper takes the initiative to build a general random walk engine for dynamically changing graphs with two key principles: (i) This system should support both low-latency streaming updates and high-throughput batched updates. (ii) This system should achieve fast sampling speed while maintaining acceptable space consumption to support dynamic graph updates. Upholding both standards, we introduce Bingo, a GPU-based random walk engine for dynamically changing graphs. First, we propose a novel radix-based bias factorization algorithm to support constant time sampling complexity while supporting fast streaming updates. Second, we present a group-adaption design to reduce space consumption dramatically. Third, we incorporate GPU-aware designs to support high-throughput batched graph updates on massively parallel platforms. Together, Bingo outperforms existing efforts across various applications, settings, and datasets, achieving up to a 271.11x speedup compared to the state-of-the-art efforts.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717456"}, {"primary_key": "161517", "vector": [], "sparse_vector": [], "title": "CAPSys: Contention-aware task placement for data stream processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vasilik<PERSON> Kalavri", "<PERSON>"], "summary": "In the context of streaming dataflow queries, the task placement problem aims to identify a mapping of operator tasks to physical resources in a distributed cluster. We show that task placement not only significantly affects query performance but also the convergence and accuracy of auto-scaling controllers. We propose CAPSys, an adaptive resource controller for dataflow stream processors, that considers auto-scaling and task placement in concert. CAPSys relies on Contention-Aware Placement Search (CAPS), a new placement strategy that ensures compute-intensive, I/O-intensive, and networkintensive tasks are balanced across available resources. We integrate CAPSys with Apache Flink and show that it consistently achieves higher throughput and lower backpressure than Flink's strategies, while it also improves the convergence of the DS2 auto-scaling controller under variable workloads. When compared with the state-of-the-art ODRP placement strategy, CAPSys computes the task placement in orders of magnitude lower time and achieves up to 6× higher throughput.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696085"}, {"primary_key": "161518", "vector": [], "sparse_vector": [], "title": "Byte vSwitch: A High-Performance Virtual Switch for Cloud Networking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lidong Jiang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Virtual switch is a fundamental component of cloud computing as it provides core networking functionalities for VMs and containers. Open vSwitch (OVS) is widely adopted in cloud environments due to its open-source nature, programmability, and rich set of features. At ByteDance, we initially adopted OVS in our public cloud, but as our cloud business grew, its generic design along with its complex code-base quickly became obstacles to improvements. Hence, we developed Byte vSwitch (BVS), a high-performance virtual switch that was specifically designed to address the performance, scalability, serviceability, and operational efficiency needs of our cloud services. More specifically, BVS adopts a simple architecture with an optimized hash table to maximize forwarding performance. In addition, we introduced several optimizations to improve BVS scalability, operability, and serviceability in cloud environments. Our evaluations show that BVS achieves up to 3.3× higher PPS and 25% lower latency compared to OVS. BVS has been deployed at scale across all regions of the ByteDance public cloud for over four years, and this paper presents our experience in designing, deploying, and operating BVS in production.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717479"}, {"primary_key": "161519", "vector": [], "sparse_vector": [], "title": "Deft: A Scalable Tree Index for Disaggregated Memory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Memory disaggregation has become an inexorable trend in data centers and the cloud. By physically separating compute and memory resources into independent pools and connecting them with high-speed networks, memory disaggregation enables high resource utilization and elastic resource scaling. However, traditional tree-based indexes become inefficient on disaggregated memory: (1) large tree nodes can easily saturate network bandwidth due to I/O amplification, yet small tree nodes increase network round trips; (2) expensive concurrency control schemes restrict the scalability. We present Deft, a tree-based index for disaggregated memory that delivers high throughput and scalability. Deft 1) introduces segmented internal nodes and hash-based leaf nodes for fine-grained access patterns to achieve low I/O amplification without increasing the index height; 2) proposes a scalable write-write concurrency control scheme on top of the tailored node structure with a one-sided shared-exclusive lock, improving scalability for concurrent update and insert operations; 3) proposes a lightweight and correct read-write concurrency control scheme, improving scalability for efficient lock-free search operations. Our experimental results demonstrate that Deft achieves high performance and scalability on disaggregated memory, and outperforms state-of-the-art ordered indexes by 2.4-9.5× under various workloads.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696062"}, {"primary_key": "161520", "vector": [], "sparse_vector": [], "title": "T-MAC: CPU Renaissance via Table Lookup for Low-Bit LLM Deployment on Edge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The deployment of Large Language Models (LLMs) on edge devices is increasingly important to enhance on-device intelligence. Weight quantization is crucial for reducing the memory footprint of LLMs on devices. However, low-bit LLMs necessitate mixed precision matrix multiplication (mpGEMM) of low precision weights and high precision activations during inference. Existing systems, lacking native support for mpGEMM, resort to dequantize weights for high precision computation. Such an indirect way can lead to a significant inference overhead. In this paper, we introduce T-MAC, an innovative lookup table(LUT)-based method designed for efficient low-bit LLM (i.e., weight-quantized LLM) inference on CPUs. T-MAC directly supports mpGEMM without dequantization, while simultaneously eliminating multiplications and reducing additions required. Specifically, T-MAC transforms the traditional data-type-centric multiplication to bit-wise table lookup, and enables a unified and scalable mpGEMM solution. Our LUT-based kernels scale linearly to the weight bit-width. Evaluated on low-bit Llama and BitNet models, T-MAC demonstrates up to 4× increase in throughput and 70% reduction in energy consumption compared to llama.cpp. For BitNet-b1.58-3B, T-MAC delivers a token generation throughput of 30 tokens/s with a single core and 71 tokens/s with eight cores on M2-Ultra, and 11 tokens/s on Raspberry Pi 5. T-MAC with LUT-based computing paradigm, paves the way for the practical deployment of low-bit LLMs on resource-constrained edge devices without compromising computational efficiency. The system is open-sourced at https://github.com/microsoft/T-MAC.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696099"}, {"primary_key": "161521", "vector": [], "sparse_vector": [], "title": "HyperAlloc: Efficient VM Memory De/Inflation via Hypervisor-Shared Page-Frame Allocators.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The provisioning of the right amount of DRAM to virtual machines (VMs) is still a major challenge and cost driver in virtualization settings. Many VMs run applications with highly volatile memory demands, which either leads to massive overprovisioning in low-demand phases or poor QoS in high-demand phases. Memory hotplugging and ballooning have become established techniques (in Linux/KVM available via virtio-mem and virtio-balloon) to dynamically de/inflate the physical memory of a VM cooperatively, by having the guests give back unused memory to the hypervisor. However, current VM deflation techniques are either not DMA-safe, preventing the passthrough of important devices like GPUs or NICs, or are not flexible or fast enough to cope with the frequently changing demands of the guest. We present HyperAlloc, a DMA-safe and extremely efficient mechanism for virtual machine de/inflation. The core idea is to provide the hypervisor direct access to the guest's page-frame allocator, greatly reducing the communication overhead. HyperAlloc can shrink virtual machines 362 times faster than virtio-balloon and 10 times faster than virtio-mem while having no measurable impact on the guest's performance. HyperAlloc's automatic reclamation provides for better memory elasticity by reducing the average memory footprint of a clang compilation by 17 percent compared to virtio-balloon's free-page reporting while, again, having no measurable impact on the guest's performance.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717484"}, {"primary_key": "161522", "vector": [], "sparse_vector": [], "title": "Samoyeds: Accelerating MoE Models with Structured Sparsity Leveraging Sparse Tensor Cores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Haibing Guan"], "summary": "The escalating size of Mixture-of-Experts (MoE) based Large Language Models (LLMs) presents significant computational and memory challenges, necessitating innovative solutions to enhance efficiency without compromising model accuracy. Structured sparsity emerges as a compelling strategy to address these challenges by leveraging the emerging sparse computing hardware. Prior works mainly focus on the sparsity in model parameters, neglecting the inherent sparse patterns in activations. This oversight can lead to additional computational costs associated with activations, potentially resulting in suboptimal performance. This paper presents Samoyeds, an innovative acceleration system for MoE LLMs utilizing Sparse Tensor Cores (SpTCs). <PERSON><PERSON><PERSON> is the first to apply sparsity simultaneously to both activations and model parameters. It introduces a bespoke sparse data format tailored for MoE computation and develops a specialized sparse-sparse matrix multiplication kernel. Furthermore, Samoyeds incorporates systematic optimizations specifically designed for the execution of dual-side structured sparse MoE LLMs on SpTCs, further enhancing system performance. Evaluations show that <PERSON><PERSON><PERSON> outperforms SOTA works by up to 1.99× at the kernel level and 1.58× at the model level. Moreover, it enhances memory efficiency, increasing maximum supported batch sizes by 4.41× on average. Additionally, Samoyeds surpasses existing SOTA structured sparse solutions in both model accuracy and hardware portability.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717455"}, {"primary_key": "161523", "vector": [], "sparse_vector": [], "title": "Pre-Stores: Proactive Software-guided Movement of Data Down the Memory Hierarchy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the notion of software pre-storing - the converse of software prefetching. With software pre-fetching, instructions are inserted in the code to asynchronously move data up in the memory hierarchy. With software pre-storing, instructions are inserted to direct the CPU to asynchronously move data down in the memory hierarchy. Pre-storing can be implemented by using existing processor instructions. Software pre-storing provides performance benefits for write-heavy applications, especially with emerging architectures that incorporate memories with diverse characteristics such as, for instance, remote DRAM accessed via a CXL switch or nonvolatile PMEM memory. We identify application scenarios in which software pre-storing is beneficial, and we have developed a tool, DirtBuster, that identifies applications and code regions that can benefit from pre-storing. We evaluate the concept of software pre-storing and the DirtBuster tool on two CPU architectures (ARM and x86) and two types of cacheable memories (PMEM and cache-coherent DRAM accessed through an FPGA). We demonstrate performance improvements for key-value stores, HPC applications, message passing, and Tensorflow, by up to 2.3x.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696097"}, {"primary_key": "161524", "vector": [], "sparse_vector": [], "title": "Hey Hey, My <PERSON>, Skewness Is Here to Stay: Challenges and Opportunities in Cloud Block Store Traffic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuandong Hong", "<PERSON><PERSON><PERSON> Niu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Elastic Block Storage (EBS) has a pivotal role in modern data center infrastructure, providing reliable, high-performance and flexible block storage service to users. In Alibaba Cloud, EBS is the most widely used service and has been supporting the operation of millions of virtual disks. However, even with layers of load balancing and caching, we still observe significant traffic skewness across the EBS stack. This motivates us to comprehensively investigate symptoms and root causes behind the traffic patterns and, more importantly, explore the fixes for the identified issues. In this paper, we collect 310 million IO traces from approximately 60k virtual machines and 140k virtual disks deployed in our EBS. Based on extensive statistical analysis, we examine the traic skewness across multiple components of the EBS stack. We identify four typical symptoms related to the IO virtualization framework, traic throttle, storage cluster management and cache. For each symptom, we further explore the potential solutions along with the challenges.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696068"}, {"primary_key": "161525", "vector": [], "sparse_vector": [], "title": "eNetSTL: Towards an In-kernel Library for High-Performance eBPF-based Network Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Using extended Berkeley Packet Filter (eBPF) to implement networking functions (NFs) has been a promising trend for modern network infrastructure. In this paper, we endeavor to implement 35 representative NFs with eBPF, but encounter inherent problems of either incomplete functionality or performance degradation of up to 49.2%. Conventional solutions like modifying the eBPF infrastructure or implementing functions directly in the kernel can lead to intrusive and unstable modifications. To address these challenges, we present eNetSTL, the first in-kernel library for eBPF-based network functions. At its core, eNetSTL identifies shared performance-critical behaviors among the 35 NFs, and abstracts these behaviors into a minimal and stable set of in-kernel components (containing a memory wrapper, three algorithms, and two data structures). It reduces interaction overhead with eBPF and mitigate safety risks by using Rust and a metadata-assisted verifier. By doing so, eNetSTL minimizes intrusions into the kernel space, ensuring stability and compatibility with current and future requirements of eBPF-based NFs. We demonstrate the capabilities of eNetSTL by presenting three real-world use cases that leverage its comprehensive functionalities. Extensive testbed experiments on seven categories of NFs show that their implementation with eNetSTL outperforms the eBPF counterparts by up to 1.8×, in terms of packet processing rate.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696094"}, {"primary_key": "161526", "vector": [], "sparse_vector": [], "title": "DeltaZip: Efficient Serving of Multiple Full-Model-Tuned LLMs.", "authors": ["<PERSON><PERSON><PERSON>", "Qing<PERSON> Hu", "<PERSON>"], "summary": "Fine-tuning large language models (LLMs) greatly improves model quality for downstream tasks. However, serving many fine-tuned LLMs concurrently is challenging due to the sporadic, bursty, and varying request patterns of different LLMs. To bridge this gap, we present DeltaZip, an LLM serving system that efficiently serves multiple full-parameter fine-tuned models concurrently by aggressively compressing model deltas by up to 10× while maintaining high model quality. The key insight behind this design is that fine-tuning results in small-magnitude changes to the pre-trained model. By co-designing the serving system with the compression algorithm, DeltaZip achieves 2× to 12× improvement in throughput compared to the state-of-the-art systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717468"}, {"primary_key": "161527", "vector": [], "sparse_vector": [], "title": "CacheBlend: Fast Large Language Model Serving for RAG with Cached Knowledge Fusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Cheng", "<PERSON><PERSON><PERSON>", "Kunta<PERSON> Du", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large language models (LLMs) often incorporate multiple text chunks in their inputs to provide the necessary contexts. To speed up the prefill of the long LLM inputs, one can pre-compute the KV cache of a text and re-use the KV cache when the context is reused as the prefix of another LLM input. However, the reused text chunks are not always the input prefix, which makes precomputed KV caches not directly usable since they ignore the text's cross-attention with the preceding texts. Thus, the benefits of reusing KV caches remain largely unrealized. This paper tackles just one challenge: when an LLM input contains multiple text chunks, how to quickly combine their precomputed KV caches in order to achieve the same generation quality as the expensive full prefill (i.e., without reusing KV cache)? This challenge naturally arises in retrieval-augmented generation (RAG) where the input is supplemented with multiple retrieved texts as the context. We present CacheBlend, a scheme that reuses the precomputed KV caches, regardless prefix or not, and selectively recomputes the KV values of a small subset of tokens to partially update each reused KV cache. In the meantime, the small extra delay for recomputing some tokens can be pipelined with the retrieval of KV caches within the same job, allowing CacheBlend to store KV caches in slower devices with more storage capacity while retrieving them without increasing the inference delay. By comparing CacheBlend with the state-of-the-art KV cache reusing schemes on three open-source LLMs of various sizes and four popular benchmark datasets of different tasks, we show that CacheBlend reduces time-to-first-token (TTFT) by 2.2-3.3× and increases the inference throughput by 2.8-5× from full KV recompute without compromising generation quality. The code is available at https://github.com/LMCache/LMCache.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696098"}, {"primary_key": "161528", "vector": [], "sparse_vector": [], "title": "Adios to Busy-Waiting for Microsecond-scale Memory Disaggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "How fast and efficiently page faults are handled determines the performance of paging-based memory disaggregation (MD) systems. Recent MD systems employ busy-waiting in page fault handling to avoid costly interrupt handling and context switching. Upon a page fault, they issue a remote fetch request and busy-wait for the completion of the request rather than yield their execution to other tasks. While these attempts succeed to cut the latency of MD systems to microseconds, they suffer from head-of-line (HOL) blocking that leads to high tail latency and causes RDMA network underutilization. To address the problems, we reload the yield-based mechanism into the page fault handling and propose a new MD system, Adios. While yielding involves the switching overhead, Adios minimizes it by putting the page fault handler and the execution scheduler into a single address space. Then we use newly designed lightweight user-level threads, namely unithread. We also devise a dispatching algorithm that alleviates the imbalance in RDMA queue pairs, assuring lessened queueing delays and improved RDMA network utilization. Our evaluation demonstrates that Adios outperforms an existing state-of-the-art busy-waiting MD system, DiLOS, by up to 1.07-1.64× in throughput and 1.99-10.89× in P99.9 latency on real-world applications.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717475"}, {"primary_key": "161529", "vector": [], "sparse_vector": [], "title": "AlloyStack: A Library Operating System for Serverless Workflow Applications.", "authors": ["<PERSON><PERSON>ng <PERSON>", "<PERSON>", "Laiping <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Serverless workflow applications, composed of multiple serverless functions, are increasingly popular in production. However, inter-function communication and cold start latency remain key performance bottlenecks. This paper introduces AlloyStack, a library operating system (LibOS) tailored for serverless workflows. AlloyStack addresses two major challenges: (1) reducing cold start latency through on-demand OS component loading and (2) minimizing data transfer overhead by enabling functions within the same workflow to share a single address space, eliminating unnecessary data copying. To ensure secure isolation, AlloyStack uses Memory Protection Keys (MPK) to separate user functions from the LibOS while maintaining efficient data sharing. Our evaluation shows that AlloyStack reduces cold start times by 98.5% to just 1.3ms. Compared to SOTA systems, AlloyStack achieves a 7.3× to 38.7× speedup in Rust end-to-end latency and a 4.8× to 78.3× speedup in other languages for intermediate data-intensive workflows.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717490"}, {"primary_key": "161530", "vector": [], "sparse_vector": [], "title": "Stateful Large Language Model Serving with Pensieve.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li"], "summary": "Large Language Models (LLMs) are wildly popular today and it is important to serve them efficiently. Existing LLM serving systems are stateless across requests. Consequently, when LLMs are used in the common setting of multi-turn conversations, a growing log of the conversation history must be processed alongside any request by the serving system at each turn, resulting in repeated processing. In this paper, we design Pensieve, a system optimized for multi-turn conversation LLM serving. Pensieve maintains the conversation state across requests by caching previously processed history to avoid duplicate processing. Pensieve's multi-tier caching strategy can utilize both GPU and CPU memory to efficiently store and retrieve cached data. Pensieve also generalizes the recent PagedAttention kernel to support attention between multiple input tokens with a GPU cache spread over non-contiguous memory. Our evaluation shows that Pensieve can achieve 1.14-3.0× the throughput of vLLM and TensorRT-LLM and significantly reduce latency.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696086"}, {"primary_key": "161531", "vector": [], "sparse_vector": [], "title": "NeuStream: Bridging Deep Learning Serving and Stream Processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern Deep Neural Network (DNN) exhibits a pattern where multiple sub-models are executed, guided by control flows such as loops and switch/merge operations. This dynamic nature introduces complexities in batching the requests of such DNNs for efficient execution on GPUs. In this paper, we present NeuStream, a programming model and runtime system for serving deep learning workloads using stream processing. NeuStream decomposes the inference workflow into modules and forms them into a streaming processing system where a request flows through. Based on such abstraction, NeuStream is able to batch requests at fine-grained module granularity. To maximize serving goodput, NeuStream exploits a two-level scheduling approach to decide the best batching requests and resource allocation for each module while satisfying service level objectives (SLOs). Our evaluation of NeuStream on a set of modern DNNs like Large Language Models (LLM) and diffusion models, etc., shows that NeuStream significantly improves goodput compared to state-of-the-art DNN serving systems.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717489"}, {"primary_key": "161532", "vector": [], "sparse_vector": [], "title": "JABAS: Joint Adaptive Batching and Automatic Scaling for DNN Training on Heterogeneous GPUs.", "authors": ["<PERSON><PERSON><PERSON>chan Yun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adaptive batching is a promising technique to reduce the communication and synchronization overhead for training Deep Neural Network (DNN) models. In this paper, we study how to speed up the training of a DNN model using adaptive batching, without degrading the convergence performance in a heterogeneous GPU cluster. We propose a novel DNN training system, called JABAS (Joint Adaptive Batching and Automatic Scaling). In JABAS, a DNN training job is executed on a DNN training framework called IIDP, which provides the same theoretical convergence rate of distributed SGD in a heterogeneous GPU cluster. To maximize the performance of the job with adaptive batching, JABAS employs adaptive batching and automatic resource scaling jointly. JABAS changes a global batch size every p iterations in a fine-grained manner within an epoch, while auto-scaling to the best GPU allocation for the next epoch in a coarse-grained manner. Using three heterogeneous GPU clusters, we evaluate JABAS for seven DNN models including large language models. Our experimental results demonstrate that JABAS provides 33.3% shorter training time and 54.2% lower training cost than the state-of-the-art adaptive training techniques, on average, without any accuracy loss.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696078"}, {"primary_key": "161533", "vector": [], "sparse_vector": [], "title": "Improving GPU Sharing Performance through Adaptive Bubbleless Spatial-Temporal Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Data centers now allow multiple applications that have lightweight workloads to share a GPU. Existing temporal or spatial sharing systems struggle to provide efficient and accurate quota assignments. We observe that the performance of the multi-user system is often underestimated because of the existence of unused GPU \"bubbles\" and can be enhanced by squeezing the bubbles. Based on this observation, we design Bless, a bubble-less spatial-temporal sharing GPU system that fine-tunes the GPU resource allocation to improve multi-user performance. <PERSON><PERSON> leverages precise computing resource management and fine-grained kernel scheduling to ensure stringent quota guarantees and reduce latency fairly for applications with varying GPU quotas. We implement and evaluate Bless with multiple applications and workloads. Our result shows that Bless achieves 21.1% - 37.3% average latency reduction over the state-of-the-art while guaranteeing the promised quota for all applications.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696070"}, {"primary_key": "161534", "vector": [], "sparse_vector": [], "title": "Overcoming the Last Mile between Log-Structured File Systems and Persistent Memory via Scatter Logging.", "authors": ["<PERSON><PERSON>", "Yanqi Pan", "<PERSON><PERSON>", "Yuchen <PERSON>", "<PERSON>"], "summary": "Log-structured file system (LFS) has been a popular option for persistent memory (PM) for its high write performance and lightweight crash consistency protocols. However, even with PM's byte-addressable I/O interface, existing PM LFSes still maintain contiguous space for log locality while using heavy garbage collection (GC) to synchronously reclaim space, causing up to 50% I/O performance degradation on PM. Thus, there exists a last-mile problem between the contiguous space management of LFS (that induces GC) and the non-contiguous byte-addressability of PM. To overcome this, we propose a novel scatter logging technique called SLOT. The core idea is to efficiently manage non-contiguous log entries on byte-addressable PM to prevent GC in LFS. Specifically, SLOT scatters log entries across PM and manages them in a per-entry granularity, thereby enabling the immediate reallocation of invalidated entries and eliminating GC overheads. SLOT further introduces an array of techniques to exploit PM write buffer efficiency to fully unleash PM I/O performance potential. We implement SlotFS to realize the efficiency of SLOT. Experimental results driven by synthetic and real-world workloads show that SlotFS significantly outperforms state-of-the-art PM file systems. Compared to two representative PM LFSes, NOVA and MadFS, SlotFS achieves 27%-47% and 59%-175% performance improvement under a series of real-world workloads.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717488"}, {"primary_key": "161535", "vector": [], "sparse_vector": [], "title": "Erebor: A Drop-In Sandbox Solution for Private Data Processing in Untrusted Confidential Virtual Machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuancheng Jiang", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Confidential virtual machines (CVMs) are designed to protect data in cloud machines, but they fail in this task in common Software-as-a-Service (SaaS) cloud environments. In such settings, the software stack within a CVM, including service programs and the operating system, that receives and processes data may intentionally disclose it to attackers. We present Erebor, a sandboxing architecture for CVMs that processes client data in secure containers, where restrictions apply to both (a) access by all untrusted outside components and (b) the sandbox's ability to communicate data through memory and software-controlled direct or covert exits. Erebor enables such restrictions through a security monitor design based on intra-kernel privilege isolation for CVM, fully compatible with emerging cloud deployments without requiring host modifications. Under realistic scenarios, such as large language model inference and private information retrieval, Erebor only adds a performance overhead of 4.5%-13.2%, demonstrating its practicality in terms of enabling strong data sandboxing in modern cloud machines.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717464"}, {"primary_key": "161536", "vector": [], "sparse_vector": [], "title": "Cheetah: Metadata Aggregation for Fast Object Storage without Distributed Ordering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Dongsheng Li", "<PERSON><PERSON>"], "summary": "Object stores usually maintain the mapping of objects to data servers' disk volumes (referred to as volume metadata) in a central directory, while storing the object data's in-volume offsets (referred to as offset metadata) together with the data on data servers. Unfortunately, the separation between volume/offset metadata complicates the processing of an object put: to ensure consistency, the multiple writes of the object's volume/offset metadata and object data have to be orchestrated in a particular order, which severely lowers object I/O performance. We propose a write-optimal structure called MetaX that aggregates all metadata of a put, including both volume and offset metadata as well as other meta information such as data checksum and temporary meta-log. Based on MetaX, we design the Cheetah object store, which organizes object storage into rich metadata storage (on meta servers) and raw data storage (on data servers). Cheetah removes the distributed ordering constraint on the multiple metadata/data writes by enforcing local atomicity of writing MetaX, while still ensuring consistency. Evaluation shows that Cheetah significantly outperforms existing object stores.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696080"}, {"primary_key": "161537", "vector": [], "sparse_vector": [], "title": "Enabling Virtual Priority in Data Center Congestion Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In data center networks, various types of traffic with strict performance requirements operate simultaneously, necessitating effective isolation and scheduling through priority queues. However, most switches support only around ten priority queues. Virtual priority can address this limitation by emulating multi-priority queues on a single physical queue, but existing solutions often require complex switch-level scheduling and hardware changes. Our key insight is that virtual priority can be achieved by carefully managing bandwidth contention in a physical queue, which is traditionally handled by congestion control (CC) algorithms. Hence, the virtual priority mechanism needs to be tightly coupled with CC. In this paper, we propose PrioPlus, a CC enhancement algorithm that can be integrated with existing congestion control schemes to enable virtual priority transmission. PrioPlus assigns specific delay ranges to different priority levels, ensuring that flows transmit only when the delay is within the assigned range, effectively meeting virtual priority requirements. Compared to Swift CC with physical priority queues, PrioPlus provides strict priority for high-priority flows without impacting performance sensibly. Meanwhile, it benefits low-priority flows from 25% to 41% as its priority-aware design enhances CC's ability to fully utilize available bandwidth once higher-priority traffic completes. As a result, in coflow and model training scenarios, PrioPlus improves job completion times by 21% and 33%, respectively, compared to Swift with physical priority queues.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717463"}, {"primary_key": "161538", "vector": [], "sparse_vector": [], "title": "Revealing the Unstable Foundations of eBPF-Based Kernel Extensions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "eBPF programs significantly enhance kernel capabilities, but encounter substantial compatibility challenges due to their deep integration with unstable kernel internals. We introduce DepSurf, a tool that identifies dependency mismatches between eBPF programs and kernel images. Our analysis of 25 kernel images spanning 8 years reveals that dependency mismatches are pervasive, stemming from kernel source code evolution, diverse configuration options, and intricate compilation processes. We apply DepSurf to 53 real-world eBPF programs, and find that 83% are impacted by dependency mismatches, underscoring the urgent need for systematic dependency analysis. By identifying these mismatches, DepSurf enables a more robust development and maintenance process for eBPF programs, enhancing their reliability across a wide range of kernels.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717497"}, {"primary_key": "161539", "vector": [], "sparse_vector": [], "title": "Mist: Efficient Distributed Training of Large Language Models via Memory-Parallelism Co-Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Qidong Su", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Various parallelism, such as data, tensor, and pipeline parallelism, along with memory optimizations like activation checkpointing, redundancy elimination, and offloading, have been proposed to accelerate distributed training for Large Language Models. To find the best combination of these techniques, automatic distributed training systems are proposed. However, existing systems only tune a subset of optimizations, due to the lack of overlap awareness, inability to navigate the vast search space, and ignoring the inter-microbatch imbalance, leading to sub-optimal performance. To address these shortcomings, we propose Mist, a memory, overlap, and imbalance-aware automatic distributed training system that comprehensively co-optimizes all memory footprint reduction techniques alongside parallelism. Mist is based on three key ideas: (1) fine-grained overlap-centric scheduling, orchestrating optimizations in an overlapped manner, (2) symbolic-based performance analysis that predicts runtime and memory usage using symbolic expressions for fast tuning, and (3) imbalance-aware hierarchical tuning, decoupling the process into an inter-stage imbalance and overlap aware Mixed Integer Linear Programming problem and an intra-stage Dual-Objective Constrained Optimization problem, and connecting them through Pareto frontier sampling. Our evaluation results show that Mist achieves an average of 1.28× (up to 1.73×) and 1.27× (up to 2.04×) speedup compared to state-of-the-art manual system Megatron-LM and state-of-the-art automatic system Aceso, respectively.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717461"}, {"primary_key": "161540", "vector": [], "sparse_vector": [], "title": "Impeller: Stream Processing on Shared Logs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current stream processing systems provide exactly-once semantics using checkpointing or a combination of logging and checkpointing. These approaches can introduce high overhead, significantly increasing the latency for normal stream processing because maintaining exactly-once semantics requires coordination across distributed nodes and streams to capture a globally consistent state. We observe that modern distributed shared logs offer a promising solution for maintaining exactly-once semantics with a small overhead. We propose Impeller, a stream processing system that uses a distributed shared log for data storage and exactly-once processing. To maintain exactly-once semantics, Impeller includes a novel and efficient progress marking protocol based on string tags and selective reads in a shared log. The key idea is to leverage the log's record-tagging feature to atomically mark progress across all streams. The experiments over the NEXMark benchmark show that Impeller achieves 1.3× to 5.4× lower p50 latency, or 1.3× to 5.0× higher saturation throughput than Kafka Streams.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3717485"}, {"primary_key": "161541", "vector": [], "sparse_vector": [], "title": "SpaceFusion: Advanced Deep Learning Operator Fusion via Space-Mapping Graph.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Haibing Guan"], "summary": "This work proposes SpaceFusion, an advanced scheduler for efficient deep learning operator fusion. First, we develop a novel abstraction, the Space-Mapping Graph (SMG), to holistically model the spatial information of both inter- and intra-operator dependencies. Subsequently, we introduce the spatial and temporal slicers to decompose the fused spaces defined in SMGs, generating fusion schedules by analyzing and transforming dependencies. Finally, we present auto-scheduling methods that use the slicers to automatically create high-performance fusion schedules tailored to specific hardware resource configurations. End-to-end performance evaluations reveal that SpaceFusion achieves up to 8.79x speedup (3.54x on average) over baseline implementations from Huggingface for Transformer models, and a maximum of 2.21x speedup compared to the state-of-the-art manually-tuned implementations powered by FlashAttention.", "published": "2025-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3689031.3696087"}]