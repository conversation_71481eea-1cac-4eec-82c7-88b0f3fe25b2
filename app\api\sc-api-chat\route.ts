import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { API_KEY_SC, BASE_URL_SC } = process.env;
    if (!API_KEY_SC || !BASE_URL_SC) {
      return NextResponse.json({ error: "API key or baseurl not set" }, { status: 500 });
    }

    console.log('API_KEY_SC', API_KEY_SC)
    console.log('BASE_URL_SC', BASE_URL_SC)

    const { imageBase64 } = await req.json();
    console.log('收到的 imageBase64 长度:', imageBase64?.length);
    if (imageBase64) {
      // 计算 base64 图片大致大小（1 字节 = 0.75 个 base64 字符）
      const sizeKB = (imageBase64.length * 0.75) / 1024;
      console.log(`图片大致大小: ${sizeKB.toFixed(2)} KB`);
      if (sizeKB > 1024) {
        console.warn('图片超过 1MB，可能影响识别效果');
      } else {
        console.log('图片未超过 1MB，无需压缩');
      }
    }

    // 构造 API 请求体
    const body = {
      model: "deepseek-ai/deepseek-vl2",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            },
            {
              type: "text",
              text: "请识别图片中的 LaTeX 公式，并只返回 LaTeX 代码, 不要输出任何其他内容。\
              如果图片中没有LaTeX公式，请返回空字符串。同时返回内容中不要添加 ```latex 或 ``` 字符。\
              不要空格，不要无意义的空格，不要识别任何公式中的空格！！！请你输出公式："
            }
          ]
        }
      ],
      stream: false,
      max_tokens: 512,
      enable_thinking: false,
      thinking_budget: 4096,
      min_p: 0.05,
      stop: null,
      temperature: 0.2,
      top_p: 0.5,
      top_k: 10,
      frequency_penalty: 0.5,
      n: 1,
      response_format: { type: "text" }
    };
    console.log('请求体 body:', JSON.stringify(body).slice(0, 500));

    const response = await fetch(`${BASE_URL_SC}/chat/completions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${API_KEY_SC}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();
    console.log('API 返回 data:', JSON.stringify(data).slice(0, 1000));
    // 假设返回格式为 { choices: [{ message: { content: "latex code" } }] }
    let latex = "";
    if (
      data &&
      Array.isArray(data.choices) &&
      data.choices.length > 0 &&
      data.choices[0].message &&
      typeof data.choices[0].message.content === "string"
    ) {
      latex = data.choices[0].message.content;
    } else {
      console.error('API 返回格式异常:', data);
      return NextResponse.json({ error: "API返回格式异常", raw: data }, { status: 500 });
    }

    return NextResponse.json({ latex });
  } catch (err) {
    console.error('接口异常:', err);
    return NextResponse.json({ error: err?.toString() }, { status: 500 });
  }
}
