[{"primary_key": "1231452", "vector": [], "sparse_vector": [], "title": "Bringing Millimeter Wave Technology to Any IoT Device.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advancement of the Internet of Things (IoT), many devices will be connected to the Internet, enabling digital twin and smart home applications. However, currently, these IoT devices are operating at lower frequency bands of the wireless spectrum, typically ranging from a few hundred MHz (such as RFID and LoRa) to a few GHz (such as BLE and WiFi). As a result, the current IoT devices not only place a huge strain on these bands, but also cannot benefit from the large bandwidth available in the higher frequencies of the spectrum such as mmWave bands. In this paper, our goal is to bring mmWave technology to existing IoT devices so they can benefit from the advantages this technology offers, such as high network capacity, low interference, and Space Division Multiple Access. To this end, we design mmPlug, a novel plug-and-play module which is simple and energy-efficient. mmPlug can be easily connected to the antenna port of any IoT device, enabling it to operate in the mmWave band. mm-Plug is compatible with different wireless technologies (such as WiFi, Lora, etc.) and does not require any modification to the circuit, firmware or communication protocols of the existing IoT devices. mmPlug achieves this by a novel design which can seamlessly be connected to the antenna port of the IoT device. We have implemented mmPlug on PCB and empirically evaluated its performance. Our results show that mmPlug enables existing IoT devices (such as WiFi and Lora) to operate at mmWave band while achieving accurate localization, uplink and downlink even when they are more than 30 m far from the access point.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613255"}, {"primary_key": "1231453", "vector": [], "sparse_vector": [], "title": "Nimble: QoS-Aware Resource Management for Edge-Assisted Microservice Environments.", "authors": ["<PERSON><PERSON>"], "summary": "We propose Nimble, a QoS-aware resource management framework for edge-assisted microservice environments. We build a preliminary prototype of Nimble and show its applicability in terms of resource utilization and execution latency for microservices in a small-scale testbed setup.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615738"}, {"primary_key": "1231454", "vector": [], "sparse_vector": [], "title": "MagTracer: Detecting GPU Cryptojacking Attacks via Magnetic Leakage Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinsong Han"], "summary": "GPU cryptojacking is an attack that hijacks GPU resources of victims for cryptocurrency mining. Such attack is becoming an emerging threat to both local hosts and cloud platforms. These attacks result in huge economic losses for the victims due to significant power consumption by cryptomining applications. Unfortunately, there are no adequate solutions to detect such attacks. In this paper, we propose MagTracer, a novel GPU cryptojacking detection system that leverages magnetic leakage signals emanating from GPUs. We make a key observation that GPUs emanate a distinct magnetic signal while mining, which can be attributed to the core feature of all cryptomining algorithms (as they are compute-intensive as well as memory-bounded). We design and implement a proof-of-concept detection system to demonstrate MagTracer's feasibility. We evaluate MagTracer on 14 heterogeneous GPU models and achieve a high average true positive rate of over 98% and a low false positive rate below 0.7% in all cases. Furthermore, our comprehensive evaluation confirms that MagTracer is scalable across different mining applications and robust against several targeted attacks.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613283"}, {"primary_key": "1231455", "vector": [], "sparse_vector": [], "title": "LiT: Fine-grained Toothbrushing Monitoring with Commercial LED Toothbrush.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neglecting proper oral hygiene has proven to potentially lead to severe oral disease, resulting in complications over time. Careful brushing can mitigate the problem, but it is common for individuals to dedicate insufficient time to the various areas of their teeth. We propose LiT to monitor the brushing situation of 16 Bass technique surfaces in real-time. LiT relies on commercial toothbrushes with blue LEDs as a transmitter and requires only 2 low-cost photosensors as receivers on the toothbrush head. However, the transmission channel of light in the oral cavity is unclear. Finding the optimal deployment positions and minimizing the number of photosensors is challenging. To tackle these obstacles, we design the positioning of the 2 photosensors and create a transmission model within the oral cavity to verify the feasibility theoretically. Additionally, obstacles in implementation include separating brushing action accurately, interference of light on the outer surfaces of front teeth, and individual variability. To overcome these challenges, we develop corresponding technologies and a comprehensive framework. Experiments with 16 users show that LiT achieves a highly accurate recognition rate of 95.3% with an error estimate for brushing duration of 6.1%. Furthermore, LiT also proves resilient under user motion and environmental interference.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613287"}, {"primary_key": "1231456", "vector": [], "sparse_vector": [], "title": "Virtual Device Farms for Mobile App Testing at Scale: A Pursuit for Fidelity, Efficiency, and Accessibility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Virtual devices based on device emulation have been widely used in lab research of mobile app testing for their efficiency and low cost. However, it remains controversial to use virtual devices for app testing in industry, given the inherent difficulties of high-fidelity emulation across diverse mobile systems and devices. Hence, mobile app companies still rely on physical device farms or services like AWS Device Farm.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613259"}, {"primary_key": "1231457", "vector": [], "sparse_vector": [], "title": "AgriTera: Accurate Non-Invasive Fruit Ripeness Sensing via Sub-Terahertz Wireless Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ability to assess the quality of fruit and vegetables at scale can revolutionize the agriculture sector and significantly reduce food waste. In this paper, we present AgriTera, a novel solution for accurate non-invasive, and contract-free fruit ripeness sensing via sub-terahertz wireless signals. The key idea is that sugar and water concentrations in fruit (that are associated with fruit ripening) leave unique non-uniform footprints in the wide band spectrum of the reflected signal off of the fruits. AgriTera utilizes the sub-THz bands for its wide bandwidth, sensitivity to water, mm-scale penetration depth, and non-ionizing features that offer high-resolution inferences from the peel as well as the pulp underneath the peel. We develop a chemometric model that translates the reflection spectra to well-known ripeness metrics, namely Dry Matter and Brix. We conduct extensive over-the-air experiments with commercially available sub-THz transceivers. We compare our results with ground truth values captured by a specialized quality sensor and a vision-based scheme that infers ripeness based on changes in the appearance of the fruit. We demonstrate that AgriTera can accurately estimate Brix and Dry Matter in three different types of fruit with an average Normalized RMSE value of 0.55%, an error that yields a negligible impact on taste and is imperceivable by the consumer.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613275"}, {"primary_key": "1231458", "vector": [], "sparse_vector": [], "title": "Battery-free Wideband Spectrum Mapping using Commodity RFID Tags.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>ang <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces RFIMap, a system that aims to inexpensively characterize the spatial and temporal distribution of RF spectrum occupancy of any indoor space at fine granularity (tens of centimeters). RFIMap builds rich wide-band indoor spectrum occupancy maps using low-cost and battery-free commodity RFID tags. RFIMap's spectrum maps have wide-ranging applications such as monitoring ambient interference in smart manufacturing, and smart hospitals. RFIMap relies on the observation that commodity RFID tags naturally reflect ambient transmission at other frequency bands, without any modification. RFIMap uses these reflections to estimate the ambient signal power originally received at these tags. RFIMap further performs a careful modeling of indoor multipath to build a dense spectrum map with fine spatial granularity. Our experiments demonstrate spatial spectrum measurement with 2.15 dB of median error at 2.4 GHz, 4.45 dB of median error at 470-700 MHz TV whitespace band, 2.1 dB of median error at 1.8-1.9 GHz in diverse industrial and university settings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592508"}, {"primary_key": "1231459", "vector": [], "sparse_vector": [], "title": "The Underwater Backscatter Channel: Theory, Link Budget, and Experimental Validation.", "authors": ["Wale<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Underwater backscatter is a recent networking technology that enables net-zero-power communication and sensing in underwater environments. Existing research on underwater backscatter has focused on designing and demonstrating early systems with impressive capabilities; however, what remains critically missing is an end-to-end analysis of the underwater backscatter communication channel, which is necessary to understand the potential of this technology to scale to real-world applications and practical deployments.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613265"}, {"primary_key": "1231461", "vector": [], "sparse_vector": [], "title": "COPSEC: Compliance-Oriented IoT Security and Privacy Evaluation Framework.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A rising number of Internet of Things (IoT) security and privacy threats have been documented over the last few years. However, IoT devices' domain designs are out-of-date and do not take into consideration the changing dangers associated with them. In this paper, we present COPSEC, a novel framework for evaluating whether IoT devices are compliant with security guidelines and privacy regulations. We extract metrics from existing guidelines and regulations and test them on a set of devices by performing hundreds of automated experiments. Our results indicate not only that these devices are not compliant with basic security guidelines, but also that their data collection operations may introduce privacy risks for the users that adopt them.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615747"}, {"primary_key": "1231462", "vector": [], "sparse_vector": [], "title": "Demonstrating Liability and Trust Metrics for Multi-Actor, Dynamic Edge and Cloud Microservices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gürkan Gür"], "summary": "Transitioning edge and cloud computing in 5G networks towards service-based architecture increases their complexity as they become even more dynamic and intertwine more actors or delegation levels. In this paper, we demonstrate the Liability-aware security manager Analysis Service (LAS), a framework that uses machine learning techniques to compute liability and trust indicators for service-based architectures such as cloud microservices. Based on the commitments of Service Providers (SPs) and real-time observations collected by a Root Cause Analysis (RCA) tool GRALAF, the LAS computes three categories of liability and trust indicators, specifically, a Commitment Trust Score, Financial Exposure, and Commitment Trends.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614086"}, {"primary_key": "1231463", "vector": [], "sparse_vector": [], "title": "Automated Spray Control using Deep Learning and Image Processing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rapid and accurate identification of plant diseases is a critical challenge in agriculture, impacting cost, crop health and agricultural productivity. Traditional methods of disease detection heavily rely on manual inspection, which can be time-consuming, subjective, and prone to human error. In this work, we propose an end-to-end system for automated detection of disease with severity level in tomato plants using a fusion of deep learning methods with image processing techniques. Farmers can reduce the needless and excessive use of pesticides by using targeted and optimized treatment plans by precisely measuring the severity level of the disease. We have developed a testbed to show the effectiveness of our proposed end-to-end spray control in terms of accuracy of disease classification, severity detection, and amount of pesticide reduction. In comparison to the traditional spray approach, our suggested setup resulted in 20% pesticide reductions and a 29.68% energy savings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615757"}, {"primary_key": "1231465", "vector": [], "sparse_vector": [], "title": "Accelerating Open RAN Research Through an Enterprise-scale 5G Testbed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open RAN is an emerging paradigm in mobile networks where the Radio Access Network (RAN) functions are disaggregated and virtualized on commodity servers. Despite the importance of Open RAN research, existing platforms often lack the fidelity and stability required to address a wide range of research problems. In response to this limitation, we have developed an enterprise-scale Open RAN testbed aimed at conducting state-of-the-art research in key areas that have received limited attention due to the lack of suitable platforms. In this poster, we provide an overview of the testbed we have created and examples of the research it has enabled, with the hope of catalyzing future open RAN research and innovation.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615745"}, {"primary_key": "1231466", "vector": [], "sparse_vector": [], "title": "COUPLE: Accelerating Video Analytics on Heterogeneous Mobile Processors.", "authors": ["<PERSON><PERSON>o", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning has achieved tremendous success in various fields, but its significant computational demands make inference on mobile devices extremely challenging. To address this issue, we propose the COUPLE system, which enables heterogeneous processors to collaborate on mobile devices for accelerating video analytics. Additionally, we design the Co-Optimize strategy which utilizes the inference results of GPU to mitigate the accuracy loss caused by DSP. Experimental results demonstrate that COUPLE can improve the inference Average Precision by up to 5% compared to existing solutions.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615749"}, {"primary_key": "1231467", "vector": [], "sparse_vector": [], "title": "Improved Decision Module Selection for Hierarchical Inference in Resource-Constrained Edge Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Hierarchical Inference (HI) paradigm has recently emerged as an effective method for balancing inference accuracy, data processing, transmission throughput, and offloading cost. This approach proves particularly efficient in scenarios involving resource-constrained edge devices like micro controller units (MCUs), tasked with executing tinyML inference. Notably, it outperforms strategies such as local inference execution, inference offloading, and split inference (i.e., inference execution distributed between two endpoints). Building upon the HI paradigm, this work explores different techniques aimed at further optimizing inference task execution. We propose three distinct HI approaches and evaluate their utility for image classification.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615732"}, {"primary_key": "1231471", "vector": [], "sparse_vector": [], "title": "Efficient Federated Learning for Modern NLP.", "authors": ["<PERSON><PERSON>ai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transformer-based pre-trained models have revolutionized NLP for superior performance and generality. Fine-tuning pre-trained models for downstream tasks often requires private data, for which federated learning is the de-facto approach (i.e., FedNLP). However, our measurements show that FedNLP is prohibitively slow due to the large model sizes and the resultant high network/computation cost. Towards practical FedNLP, we identify as the key building blocks adapters, small bottleneck modules inserted at a variety of model layers. A key challenge is to properly configure the depth and width of adapters, to which the training speed and efficiency is highly sensitive. No silver-bullet configuration exists: the optimal choice varies across downstream NLP tasks, desired model accuracy, and mobile resources. To automate adapter configuration, we propose AdaFL1, a framework that enhances the existing FedNLP with two novel designs. First, AdaFL progressively upgrades the adapter configuration throughout a training session; the principle is to quickly learn shallow knowledge by only training fewer and smaller adapters at the model's top layers, and incrementally learn deep knowledge by incorporating deeper and larger adapters. Second, AdaFL continuously profiles future adapter configurations by allocating participant devices to trial groups. Extensive experiments show that AdaFL can reduce FedNLP's model convergence delay to no more than several hours, which is up to 155.5× faster compared to vanilla FedNLP and 48× faster compared to strong baselines.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592505"}, {"primary_key": "1231472", "vector": [], "sparse_vector": [], "title": "Federated Few-Shot Learning for Mobile NLP.", "authors": ["<PERSON><PERSON>ai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Natural language processing (NLP) sees rich mobile applications. To support various language understanding tasks, a foundation NLP model is often fine-tuned in a federated, privacy-preserving setting (FL). This process currently relies on at least hundreds of thousands of labeled training samples from mobile clients; yet mobile users often lack willingness or knowledge to label their data. Such an inadequacy of data labels is known as a few-shot scenario; it becomes the key blocker for mobile NLP applications. For the first time, this work investigates federated NLP in the few-shot scenario (FedFSL). By retrofitting algorithmic advances of pseudo labeling and prompt learning, we first establish a training pipeline that delivers competitive accuracy when only 0.05% (fewer than 100) of the training data is labeled and the remaining is unlabeled. To instantiate the workflow, we further present a system FeS, addressing the high execution cost with novel designs. (1) Curriculum pacing, which injects pseudo labels to the training workflow at a rate commensurate to the learning progress; (2) Representational diversity, a mechanism for selecting the most learnable data, only for which pseudo labels will be generated; (3) Co-planning of a model's training depth and layer capacity. Together, these designs reduce the training delay, client energy, and network traffic by up to 46.0$\\times$, 41.2$\\times$ and 3000.0$\\times$, respectively. Through algorithm/system co-design, FFNLP demonstrates that FL can apply to challenging settings where most training samples are unlabeled.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613277"}, {"primary_key": "1231474", "vector": [], "sparse_vector": [], "title": "PowerPhone: Unleashing the Acoustic Sensing Capability of Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic sensing on smartphones has gained extensive attention from both industry and research communities. Prior studies suffer from one fundamental limit, i.e., audio sampling rates on smartphones are constrained at 48 kHz. In this work, we present PowerPhone, a software reconfiguration to support higher sampling rates on both microphones and speakers of smartphones. We reverse-engineered more than 100 smartphones and found that their sampling rates can be reconfigured to 192 kHz. We conducted benchmark experiments and showcased field studies to demonstrate the unleashed sensing capability using our reconfigured smart-phones. First, we improve the sensing resolution from 7 cm to 1cm and enable multi-finger gesture recognition on smart-phones. Second, we push the sensing granularity of subtle movements to 2 μm and show the feasibility of turning the smartphone into a micrometer-level machine vibration meter. Third, we increase the sensing range to 6 m and showcase room-scale human presence detection using a smartphone. Finally, we demonstrate that PowerPhone can enable new applications that were previously infeasible. Specifically, we can detect the home appliance status by analyzing ultrasonic leakages above 24 kHz from the wireless charger while charging a smartphone. Our open-source artifacts can be found at: https://powerphone.github.io.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613270"}, {"primary_key": "1231475", "vector": [], "sparse_vector": [], "title": "An Entangled Quantum Imaging Method Based on Photon Capture Probability in Weak-Turbulence Environment.", "authors": ["Jingyang Cao", "<PERSON>", "<PERSON>"], "summary": "Entangled photon quantum imaging technology has broad application prospects in ocean imaging due to its high resolution, anti-turbulence and anti-interference characteristics. Turbulence and optical attenuation caused by seawater will seriously limit the traditional imaging system, and quantum imaging based on entangled light source can overcome the limitations of low imaging resolution, large impact on marine environment and low confidentiality in traditional underwater target imaging. Based on the propagation characteristics of weak turbulent environment, we propose a new photon capture probability algorithm for marine environment based on entangled photon pairs. Finally, the relevant experimental environment is constructed according to the designed imaging optical path. We conduct extensive experiments to evaluate the effectiveness of our proposed quantum imaging method.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615748"}, {"primary_key": "1231476", "vector": [], "sparse_vector": [], "title": "Catch Me If You Can: Demonstrating Laser Tethering with Highly Mobile Targets.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Conventional wisdom holds that laser-based systems cannot handle mobility due to the strong directionality of laser light. We challenge this belief by presenting Lasertag, a generic system framework that tightly integrates laser steering with optical tracking to maintain laser connectivity with high-velocity targets. Lasertag creates a constantly connected, laser-based tether between the Lasertag core unit and a remote target, irrespective of the target's movement. Key elements of Lasertag include (1) a novel optical design that superimposes the optical paths of a steerable laser beam and an image sensor, (2) a lightweight optical tracking mechanism for passive retroreflective markers, (3) an automated mapping method to translate scene points to laser steering commands, and (4) a predictive steering algorithm that overcomes limited image sensor frame rates and laser steering delays to quadruple the steering rate up to 151 Hz. We demonstrate Lasertag's tethering capabilty with various mobile targets, such as a VR headset worn during active game play, a remotely-controlled moving robot, and more. Lasertag paves the way for laser applications in highly mobile settings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614081"}, {"primary_key": "1231477", "vector": [], "sparse_vector": [], "title": "Revisiting Cardinality Estimation in COTS RFID Systems.", "authors": ["Xingyu Chen", "<PERSON><PERSON>", "<PERSON>", "Yu<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With 30 billion RFID tags sold worldwide in 2021, a common basic functionality needed by RFID-enabled applications is cardinality estimation --- to quickly estimate the number of distinct tags in an RFID system. Although many advanced solutions have been proposed over the past decade, they suffer from one major limitation in practical use: they need to either modify the existing RFID standard or obtain MAC-layer information, both of which however cannot be supported by commercial off-the-shelf (COTS) devices. In this paper, we revisit the counting problem and propose a novel counting scheme called average time duration based counter (ATD) that quickly estimates the number of distinct tags in a standards-compliant manner. Compared with existing work, the competitive advantage of ATD is that it can be directly deployed on a COTS RFID system, with no need for any hardware modifications. In ATD, we found a new and measurable indicator --- the time duration between two adjacent singleton slots, which depends on the number of tags. Following this observation, we derive the theoretical relationship between the time indicator and the number of tags and then give the proof of the estimation as well as its parameter settings. Additionally, we propose a flag-flipping solution to address the overlapping problem in the multi-reader case. We implement ATD in a COTS RFID system with 1000 tags. Experimental results show that ATD is 4.2× faster than the baseline of tag inventory; the performance gain will be further increased in a larger RFID system.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613295"}, {"primary_key": "1231478", "vector": [], "sparse_vector": [], "title": "Enc2: Privacy-Preserving Inference for Tiny IoTs via Encoding and Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Privacy-preserving machine learning (PPML) techniques have allowed remote and private inference for resource-constrained internet-of-things (IoT) devices on the cloud. The main challenge in most of the existing PPML technologies is a severe slowdown in inference latency mainly due to the use of encryption during the computation. To combat this, an emerging method is to leverage encoding as an alternative. While this results in a significant speedup, it imposes the burden of encoding to the resource-constrained IoT/edge device. Despite being feasible for simple workloads where encoding is lightweight, devices with very limited computational capabilities face a tradeoff between latency and privacy when performing complex tasks.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592501"}, {"primary_key": "1231479", "vector": [], "sparse_vector": [], "title": "Unify: Turning BLE/FSK SoC into WiFi SoC.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the potential for connecting billions of WiFi devices to low-power Bluetooth/FSK devices, WiFi-Bluetooth cross-technology communication (CTC) has drawn significant interests from academia and industry. However, state-of-the-art CTC solutions either are limited to one-way communication, or require multiple chips with hardware modifications.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592512"}, {"primary_key": "1231480", "vector": [], "sparse_vector": [], "title": "UBR: User-Centric QoE-Based Rate Adaptation for Dynamic Network Conditions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The prevalence of video streaming applications has led to an escalation in users' demands for high-quality services. Numerous endeavors have been undertaken in the realm of quality-of-experience (QoE) models and adaptive bitrate (ABR) algorithms to fulfill this demand. Nevertheless, the existing QoE models exhibit a significant gap with users' actual experience. ABR algorithms are vulnerable in dynamic network environments. We present an integrated system with an accurate QoE model and an environment-robust adaptation algorithm to ensure high user satisfaction in dynamic network conditions. We define a QoE model that accurately estimates the user's QoE by considering the viewing environment and video content. We then design a meta-reinforcement learning-based adaptation algorithm that adapts to dynamic network conditions. We systematically integrate them, allowing it to update its policy with QoE feedback within a few shots.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615756"}, {"primary_key": "1231484", "vector": [], "sparse_vector": [], "title": "DancingAnt: Body-empowered Wireless Sensing Utilizing Pervasive Radiations from Powerline.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, wireless sensing has attracted lots of research attention with a large range of applications enabled. However, several critical issues still hinder wireless sensing from being adopted in daily use: (a) requiring dedicated devices and (or) dedicated signals; (b) limited sensing coverage; and (c) affecting the original function of the wireless technology (e.g., communication). In this work, we propose a new sensing modality, i.e., leveraging the pervasive powerline leakage for sensing. The key observation is that human body can capture such leaked signals, and the received signals vary with body gestures. We design a cheap ring antenna to collect the powerline leaked signals at human body and establish a body-empowered model to sense body motions. We prototype the proposed system with designs spanning both hardware and software. Comprehensive experiments show that the proposed sensing modality can realize a large range of applications in a different way from existing sensing methods. We showcase the powerful capability of this sensing modality using three typical sensing applications: body gesture recognition, sleep posture sensing, and fall detection.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613272"}, {"primary_key": "1231485", "vector": [], "sparse_vector": [], "title": "MagCode: NFC-Enabled Barcodes for NFC-Disabled Smartphones.", "authors": ["<PERSON><PERSON> Dai", "Z<PERSON><PERSON> An", "Qingrui Pan", "<PERSON><PERSON>"], "summary": "Mobile payment has achieved explosive growth in recent years due to its contactless feature, which lowers the infection risk of COVID-19. In the market, near-field communication (NFC) and barcodes have become the de facto standard technologies for mobile payment. The NFC-based payment outperforms barcode-based payment in terms of security, usability, and convenience. It is especially more user-friendly for the amblyopia group. Unfortunately, NFC functionality is unavailable in nearly half of smartphones in the market nowadays due to the shortage of NFC modules or being disabled for security reasons.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592528"}, {"primary_key": "1231486", "vector": [], "sparse_vector": [], "title": "MagCode: Bringing NFC Feature to All Smartphones.", "authors": ["<PERSON><PERSON> Dai", "Z<PERSON><PERSON> An", "Qingrui Pan", "<PERSON><PERSON>"], "summary": "Mobile payments have experienced a significant surge in recent years, primarily due to their contactless feature that mitigates the risk of COVID-19 transmission. In this landscape, NFC-based payment outperforms barcode-based payment in terms of security, usability, and convenience. It is especially more user-friendly for the amblyopic community. Unfortunately, NFC functionality is unavailable in nearly half of the smartphones in the market nowadays due to the shortage of NFC modules or being disabled for security reasons.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614079"}, {"primary_key": "1231487", "vector": [], "sparse_vector": [], "title": "A Handheld Fine-Grained RFID Localization System with Complex-Controlled Polarization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is much interest in fine-grained RFID localization systems. Existing systems for accurate localization typically require infrastructure, either in the form of extensive reference tags or many antennas (e.g., antenna arrays) to localize RFID tags within their radio range. Yet, there remains a need for fine-grained RFID localization solutions that are in a compact, portable, mobile form, that can be held by users as they walk around areas to map them, such as in retail stores, warehouses, or manufacturing plants.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592504"}, {"primary_key": "1231488", "vector": [], "sparse_vector": [], "title": "GPSMirror: Expanding Accurate GPS Positioning to Shadowed and Indoor Regions with Backscatter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the prevalence of GPS services, they still suffer from intermittent positioning with poor accuracy in partially shadowed regions like urban canyons, flyover shadows, and factories' indoor areas. Existing wisdom relies on hardware modifications of GPS receivers or power-hungry infrastructures requiring continuous plug-in power supply which is hard to provide in outdoor regions and some factories. This paper fills the gap with GPSMirror, the first GPS-strengthening system that works for unmodified smartphones with the assistance of newly-designed GPS backscatter tags. The key enabling techniques in GPSMirror include: (i) a careful hardware design with microwatt-level power consumption that pushes the limit of backscatter sensitivity to re-radiate extremely weak GPS signals with enough coverage approaching the regulation limit; and (ii) a novel GPS positioning algorithm achieving meter-level accuracy in shadowed regions as well as expanding locatable regions under inadequate satellites where conventional algorithms fail. We build a prototype of the GPSMirror tags and conduct comprehensive experiments to evaluate them. Our results show that a GPSMirror tag can provide coverage up to 27.7 m. GPSMirror achieves median positioning accuracy of 3.7 m indoors and 4.6 m in urban canyon environments, respectively.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592511"}, {"primary_key": "1231490", "vector": [], "sparse_vector": [], "title": "APG: Audioplethysmography for Cardiac Monitoring in Hearables.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents Audioplethysmography (APG), a novel cardiac monitoring modality for active noise cancellation (ANC) headphones. APG sends a low intensity ultrasound probing signal using an ANC headphone's speakers and receives the echoes via the on-board feedback microphones. We observed that, as the volume of ear canals slightly changes with blood vessel deformations, the heartbeats will modulate these ultrasound echoes. We built mathematical models to analyze the underlying physics and propose a multi-tone APG signal processing pipeline to derive the heart rate and heart rate variability in both constrained and unconstrained settings. APG enables robust monitoring of cardiac activities using mass-market ANC headphones in the presence of music playback and body motion such as running.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613281"}, {"primary_key": "1231491", "vector": [], "sparse_vector": [], "title": "Towards Spatial Selection Transmission for Low-end IoT devices with SpotSound.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper tries to answer a question: \"Can we achieve spatial-selective transmission on IoT devices?\" A positive answer would enable more secure data transmission among IoT devices. The challenge, however, is how to manipulate signal propagation without relying on beamforming antenna arrays which are usually unavailable on low-end IoT devices.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592496"}, {"primary_key": "1231493", "vector": [], "sparse_vector": [], "title": "PassiveLiFi Demonstration: Rethinking LiFi for Low-Power and Long Range RF Backscatter.", "authors": ["<PERSON><PERSON> Fonseca", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we demonstrate PassiveLiFi, a battery-free system that works at the intersection of Light-Fidelity (LiFi) and Radio-Frequency (RF) backscatter technologies to enable long-range and ultra-low power wireless communications. We show how the PassiveLiFi tag can transmit real measured data through an interactive demo, and we display the received data in a customized graphical user interface. Our demo shows the suitability of PassiveLiFi for implementing real IoT applications, such as monitoring systems for smart agrifood facilities (i.e., greenhouses and vertical farms).", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614083"}, {"primary_key": "1231494", "vector": [], "sparse_vector": [], "title": "Taking 5G RAN Analytics and Control to a New Level.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open RAN, a modular and disaggregated design paradigm for 5G radio access networks (RAN), promises programmability through the RAN Intelligent Controller (RIC). However, due to latency and safety challenges, the telemetry and control provided by the RIC is mainly limited to higher layers and higher time scales (> 10ms), while also relying on predefined service models which are hard to change. We address these issues by proposing Janus, a fully programmable monitoring and control system, specifically designed with the RAN idiosyncrasies in mind, focused on flexibility, efficiency and safety. <PERSON><PERSON> builds on eBPF to allow third-parties to load arbitrary codelets inline in the RAN functions in a provably safe manner. We extend eBPF with a novel bytecode patching algorithm that enforces codelet runtime thresholds, and a safe way to collect user-defined telemetry. We demonstrate <PERSON><PERSON>' flexibility and efficiency by building 3 different classes of applications (18 applications in total) and deploying them on a 100MHz 4×4 MIMO 5G cell without affecting the RAN performance.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592493"}, {"primary_key": "1231495", "vector": [], "sparse_vector": [], "title": "Programmable RAN Platform for Flexible Real-Time Control and Telemetry.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A key transformation of the Radio Access Network (RAN) in 5G is the migration to an Open RAN architecture, that sees the RAN functions virtualized and disaggregated. Open RAN, aims at accelerating innovation through the introduction of a programmable RAN Intelligent Controller (RIC). However, due to latency and safety challenges, the telemetry and control provided by the RIC is mainly limited to higher layers and time scales (>10ms), while also relying on predefined service models which are hard to change. In this work, we demonstrate Minerva, a programmable monitoring and control platform, specifically designed with the RAN idiosyncrasies in mind. Minerva introduces two novel components called Janus and Decima, for the deployment of safe RAN control and telemetry applications by trusted third-parties. Jan<PERSON> enables the deployment of codelets in the RAN functions using userspace eBPF for fast inline control and filtering of data. Decima, enables the deployment of sandboxed real-time control applications, by leveraging data collected from Janus and the OS. We demonstrate the benefits of Minerva through two applications related to interference detection and inter-cell interference mitigation, by leveraging a commercial-grade 5G testbed deployment.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614065"}, {"primary_key": "1231496", "vector": [], "sparse_vector": [], "title": "A Simplified Intelligent Autonomous Obstacle Bypassing Method for Mobile Robots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a demo focusing on developing a robot capable of autonomously bypassing obstacles in cluttered environments using a camera as its sole sensing mechanism. The robot is programmed to follow a predetermined path via a line following module while using a custom object detection model to detect and differentiate obstacles on the road from other objects in the environment. The obstacle detection module based on YOLOv5 architecture can accurately detect obstacles from the surrounding. Upon obstacle detection, the robot initiates an obstacle avoidance maneuver by adjusting its steering based on error measurement, allowing it to navigate around the obstacle smoothly. The proposed design is validated with extensive experimentation, demonstrating its ability to navigate cluttered environments while avoiding obstacles.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614066"}, {"primary_key": "1231498", "vector": [], "sparse_vector": [], "title": "Cancelling Speech Signals for Speech Privacy Protection against Microphone Eavesdropping.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jinsong Han", "<PERSON><PERSON>"], "summary": "Ultrasonic microphone jammers protect speech privacy from being eavesdropped by leveraging microphones' non-linearity. However, existing jammers merely introduce independent noises and are vulnerable to capable adversaries who adopt advanced denoising techniques. We propose a novel jammer, namely <PERSON>c<PERSON><PERSON><PERSON>. It reduces the signal-to-noise ratio (SNR) at the adversary's microphone from two perspectives, i.e., cancelling speech signals and adding noises that are difficult to be removed. It effectively cancels out the protected speech signals at the adversary without compromising the delivery of the signal to the targeted individual. <PERSON><PERSON><PERSON><PERSON><PERSON> further adds coherent noises that are coupled with the speech signals to resist removal by the adversary. Extensive evaluations show that <PERSON><PERSON><PERSON><PERSON><PERSON> can cause a low SNR (-13.6 dB) at the adversary and up to 96.9% of speech signals are unrecognized at the adversary even if state-of-the-art denoising techniques are adopted by the adversary. Comprehensive experiments demonstrate the effectiveness of <PERSON><PERSON><PERSON><PERSON><PERSON> confronted by capable adversaries.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592502"}, {"primary_key": "1231500", "vector": [], "sparse_vector": [], "title": "Chroma: Learning and Using Network Contexts to Reinforce Performance Improving Configurations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Managing network configuration and improving service experience effectively is essential for cellular service providers (CSPs). This is challenging because of cellular networks' large scale and complexity, the wide variety of configuration parameters, and the performance impact tradeoffs resulting across multiple metrics and geographical locations. This paper focuses on learning and using network contexts to recommend performance-improving configurations. While learning contexts, one must carefully account for the configuration parameter dependency, performance impact confusion that can arise due to co-occurring unrelated changes, and uneven change deployment distribution across locations. We present a new solution Chroma that addresses the above challenges. Using real-world data collected from a large operational LTE and 5G cellular service provider, we thoroughly evaluate and demonstrate the efficacy of Chroma. We successfully trial Chroma on an operational cellular network and highlight its benefits in practical settings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613256"}, {"primary_key": "1231501", "vector": [], "sparse_vector": [], "title": "Low-Bandwidth Self-Improving Transmission of Rare Training Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A severe bandwidth mismatch between incoming sensor data rate and wireless backhaul bandwidth often exists on unmanned probes when collecting new training data for machine learning (ML). To overcome this mismatch, we describe a self-improving ML-based transmission system called Hawk. Starting from a weak model that is trained on just a few examples, it seamlessly pipelines semi-supervised learning, active learning, and transfer learning, with asynchronous bandwidth-sensitive data transmission to a distant human for labeling. When a significant number of true positives (TPs) have been labeled, <PERSON> trains an improved model to replace the old model. This iterative workflow, called Live Learning, continues until a sufficient number of TPs have been collected. For very rare events on challenging datasets, and bandwidths as low as 12 kbps, a team of 7 probes using Hawk discovers up to 87% of the TPs that could have been discovered via full preview, transmission and labeling of all mission data. <PERSON> also uses diversity sampling and few-shot learning.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613300"}, {"primary_key": "1231502", "vector": [], "sparse_vector": [], "title": "MintEDGE: Multi-tier sImulator for eNergy-aware sTrategies in Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Edge computing has transformed cellular networks, offering fast response times by moving computing resources to the network's edge. This not only reduces the burden on the Wide Area Network (WAN) but also enables latency-sensitive applications. However, the widespread deployment of edge computing raises concerns regarding its sustainability. In this work, we present MintEDGE, a simulation framework that models a fully configurable edge-enabled cellular network. MintEDGE empowers researchers and practitioners to design and assess energy-saving strategies for edge computing. We discuss the details of the simulator and its customizable elements like user mobility, the possibility to use predictive workload algorithms, and diverse application scenarios at scale. MintEDGE is released under a permissive MIT license.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615727"}, {"primary_key": "1231503", "vector": [], "sparse_vector": [], "title": "MetaStream: Live Volumetric Content Capture, Creation, Delivery, and Rendering in Real Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Tao Han"], "summary": "While recent work explored streaming volumetric content on-demand, there is little effort on live volumetric video streaming that bears the potential of bringing more exciting applications than its on-demand counterpart. To fill this critical gap, in this paper, we propose MetaStream, which is, to the best of our knowledge, the first practical live volumetric content capture, creation, delivery, and rendering system for immersive applications such as virtual, augmented, and mixed reality. To address the key challenge of the stringent latency requirement for processing and streaming a huge amount of 3D data, MetaStream integrates several innovations into a holistic system, including dynamic camera calibration, edge-assisted object segmentation, cross-camera redundant point removal, and foveated volumetric content rendering. We implement a prototype of MetaStream using commodity devices and extensively evaluate its performance. Our results demonstrate that MetaStream achieves low-latency live volumetric video streaming at close to 30 frames per second on WiFi networks. Compared to state-of-the-art systems, MetaStream reduces end-to-end latency by up to 31.7% while improving visual quality by up to 12.5%.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592530"}, {"primary_key": "1231504", "vector": [], "sparse_vector": [], "title": "MASTERKEY: Practical Backdoor Attack Against Speaker Verification Systems.", "authors": ["Han<PERSON> Guo", "<PERSON><PERSON>", "<PERSON><PERSON> Guo", "<PERSON>", "<PERSON><PERSON>"], "summary": "Speaker Verification (SV) is widely deployed in mobile systems to authenticate legitimate users by using their voice traits. In this work, we propose a backdoor attack <PERSON><PERSON><PERSON>, to compromise the SV models. Different from previous attacks, we focus on a real-world practical setting where the attacker possesses no knowledge of the intended victim. To design <PERSON><PERSON><PERSON>, we investigate the limitation of existing poisoning attacks against unseen targets. Then, we optimize a universal backdoor that is capable of attacking arbitrary targets. Next, we embed the speaker's characteristics and semantics information into the backdoor, making it imperceptible. Finally, we estimate the channel distortion and integrate it into the backdoor. We validate our attack on 6 popular SV models. Specifically, we poison a total of 53 models and use our trigger to attack 16,430 enrolled speakers, composed of 310 target speakers enrolled in 53 poisoned models. Our attack achieves 100% attack success rate with a 15% poison rate. By decreasing the poison rate to 3%, the attack success rate remains around 50%. We validate our attack in 3 real-world scenarios, and successfully demonstrate the attack through both over-the-air and over-the-telephony-line scenarios.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613261"}, {"primary_key": "1231505", "vector": [], "sparse_vector": [], "title": "Sign-to-911: Emergency Call Service for Sign Language Users with Assistive AR Glasses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan", "Wei<PERSON>ng Ling", "<PERSON><PERSON>", "<PERSON>", "Hongz<PERSON> Du", "<PERSON><PERSON>"], "summary": "Sign-to-911 offers a compact mobile system solution to fast and runtime American Sign Language (ASL) and English translations. It is designated as 911 call services for ASL users with hearing disabilities upon emergencies. It enables bidirectional translations of ASL-to-English and English-to-ASL. The signer wears the AR glasses, runs Sign-to-911 on his/her smartphone and glasses, and interacts with a 911 operator. The design of Sign-to-911 departs from the popular deep learning based solution paradigm, and adopts simpler traditional AI/machine learning (ML) models. The key is to exploit ASL linguistic features to simplify the model structures and improve accuracy and speed. It further leverages recent component solutions from graphics, vision, natural language processing, and AI/ML. Our evaluation with six ASL signers and 911 call records has confirmed its viability.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613260"}, {"primary_key": "1231507", "vector": [], "sparse_vector": [], "title": "GreenMO: Enabling Virtualized, Sustainable Massive MIMO with a Single RF Chain.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Manide<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the turn of new decade, wireless communications face a major challenge on connecting many more new users and devices, at the same time being energy efficient and minimizing its carbon footprint. However, the current approaches to address the growing number of users and spectrum demands, like Massive MIMO, demand exorbitant energy consumption. The reason is that traditionally Massive MIMO requires a digital beamforming architecture that needs a separate RF chain per antenna, so the power consumption scales with number of antennas. Instead, GreenMO creates a new Massive MIMO architecture with just a single physically laid RF chain, shared by all the antennas and introduces for the first time, the concept of virtualizing the RF chain hardware. That is, GreenMO creates an optimal number of virtual RF chains to serve a given number of spatial streams, depending on channel conditions and network load. Due to efficient, softwarized control over the number of virtual RF chains, GreenMO paves the way for green and flexible massive MIMO. We prototype GreenMO on a PCB with eight antennas and evaluate it with a WARPv3 SDR platform in an office environment. The results demonstrate that GreenMO is 3× more power-efficient than traditional Massive MIMO and 4× more spectrum-efficient than traditional OFDMA systems, while multiplexing 4 spatial streams, and can save upto 50% power in modern 5G NR base stations.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592509"}, {"primary_key": "1231508", "vector": [], "sparse_vector": [], "title": "Cross-modal meta-learning for WiFi-based human activity recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "WiFi-based Human Activity Recognition (HAR) faces challenges in achieving widespread deployment due to its reliance on massive data and limited scalability. However, the emergence of Few-Shot Learning (FSL) provides opportunities to address this issue. In this paper, we propose a cross-modal meta-learning approach based on Model-Agnostic Meta-Learning (MAML) to enable few-shot WiFi-based HAR. The hypothesis is that models can learn \"learning methods\" from thousands of diverse image classification tasks and apply them to WiFi-based HAR. By solely leveraging public image and WiFi signal datasets, the proposed approach trains a model capable of recognizing previously unseen activities with only 5 samples per class, achieving an average accuracy of 88.5% over thousands of tests.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615754"}, {"primary_key": "1231510", "vector": [], "sparse_vector": [], "title": "SenCom: Integrated Sensing and Communication with Practical WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Guanding Yu", "Jinsong Han", "<PERSON><PERSON>"], "summary": "Given the fact that WiFi-based sensing can be realized by reusing WiFi communication facilities and communication frequency bands, integrated sensing and communication (ISAC) is considered a crucial development direction for future WiFi standards, such as IEEE 802.11bf. Traditional WiFi sensing systems extract channel state information (CSI) from customized WiFi packets to quantify the characteristics of the sensing target. This poses challenges for existing WiFi systems originally designed for communication purposes, as it requires high-quality and sufficient CSI measurements. In this paper, we propose SenCom, which extracts CSI from general WiFi packets. SenCom enables CSI calibration across different WiFi communication modes and provides unified CSI measurements for upper-layer sensing applications. We also devise a fitting-resampling scheme to derive evenly sampled CSI with consistent dimensionality, and an incentive strategy to ensure sufficient CSI measurements over time. We build a prototype of SenCom and perform extensive experiments with 15 participants. The results show that SenCom is competent for a variety of sensing tasks, while incurring little compromise to the WiFi communication performance.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613274"}, {"primary_key": "1231511", "vector": [], "sparse_vector": [], "title": "VI-Map: Infrastructure-Assisted Real-Time HD Mapping for Autonomous Driving.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhenyu Yan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "HD map is a key enabling technology towards fully autonomous driving. We propose VI-Map, the first system that leverages roadside infrastructure to enhance real-time HD mapping for autonomous driving. The core concept of VI-Map is to exploit the unique cumulative observations made by roadside infrastructure to build and maintain an accurate and current HD map. This HD map is then fused with on-vehicle HD maps in real time, resulting in a more comprehensive and up-to-date HD map. By extracting concise bird-eye-view features from infrastructure observations and utilizing vectorized map representations, VI-Map incurs low compute and communication overhead. We conducted end-to-end evaluations of VI-Map on a real-world testbed and a simulator. Experiment results show that VI-Map can construct decentimeter-level (up to 0.3 m) HD maps and achieve real-time (up to a delay of 42 ms) map fusion between driving vehicles and roadside infrastructure. This represents a significant improvement of 2.8× and 3× in map accuracy and coverage compared to the state-of-the-art online HD mapping approaches. A video demo of VI-Map on our real-world testbed is available at https://youtu.be/p2RO65R5Ezg.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613280"}, {"primary_key": "1231512", "vector": [], "sparse_vector": [], "title": "The Wisdom of 1, 170 Teams: Lessons and Experiences from a Large Indoor Localization Competition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We organized an online fingerprint-based indoor localization competition in 2021. It attracted 1,170 teams worldwide. The teams were provided with a 60 GB dataset including WiFi, BLE, IMU, and geomagnetic field strength data collected from 204 buildings to build their localization algorithms, which were then evaluated against a separate test dataset. The competition received 28,009 submissions. The top team achieved an average accuracy of 1.50m. This paper reports the lessons we learned from analyzing the submissions, as well as our experiences in organizing the competition, through both qualitatively studying the teams' algorithms and quantitatively characterizing the competition results.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592507"}, {"primary_key": "1231514", "vector": [], "sparse_vector": [], "title": "DNN-based SLAM Tracking Error Online Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Simultaneous localization and mapping (SLAM) takes in sensor data, e.g., camera frames, and estimates the user's trajectory while creating a map of the surrounding environment. However, existing SLAM evaluation methods are not reference-free, requiring ground-truth trajectories collected from external systems that are infeasible for most scenarios. In this demo, we present Deep SLAM Error Estimator (DeepSEE), a framework that collects features from a standard visual SLAM pipeline as multivariate time series and uses an attention-based neural network to estimate the tracking error at run time. We evaluate DeepSEE in a game engine-based virtual environment, which generates the visual input for DeepSEE and provides the ground-truth trajectory. Demo participants can navigate the virtual environment to create their own trajectories and view the online pose error estimation. This demo showcases how DeepSEE can act as a quality-of-service indicator for downstream applications.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614082"}, {"primary_key": "1231515", "vector": [], "sparse_vector": [], "title": "MUSE-Fi: Contactless MUti-person SEnsing Exploiting Near-field Wi-Fi Channel Variation.", "authors": ["Jingzhi Hu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong<PERSON> Wang", "<PERSON>"], "summary": "Having been studied for more than a decade, Wi-Fi human sensing still faces a major challenge in the presence of multiple persons, simply because the limited bandwidth of Wi-Fi fails to provide a suficient range resolution to physically separate multiple subjects. Existing solutions mostly avoid this challenge by switching to radars with GHz bandwidth, at the cost of cumbersome deployments. Therefore, could Wi-Fi human sensing handle multiple subjects remains an open question. This paper presents MUSE-Fi, the first Wi-Fi multi-person sensing system with physical separability. The principle behind MUSE-Fi is that, given a Wi-Fi device (e.g., smartphone) very close to a subject, the near-field channel variation caused by the subject significantly overwhelms variations caused by other distant subjects. Consequently, focusing on the channel state information (CSI) carried by the trafic in and out of this device naturally allows for physically separating multiple subjects. Based on this principle, we propose three sensing strategies for MUSE-Fi: i) uplink CSI, ii) downlink CSI, and iii) downlink beamforming feedback, where we specifically tackle signal recovery from sparse (per-user) trafic under realistic multi-user communication scenarios. Our extensive evaluations clearly demonstrate that MUSE-Fi is able to successfully handle multi-person sensing with respect to three typical applications: respiration monitoring, gesture detection, and activity recognition.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613290"}, {"primary_key": "1231516", "vector": [], "sparse_vector": [], "title": "Re-thinking computation offload for efficient inference on IoT devices with duty-cycled radios.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "While a number of recent efforts have explored the use of \"cloud offload\" to enable deep learning on IoT devices, these have not assumed the use of duty-cycled radios like BLE. We argue that radio duty-cycling significantly diminishes the performance of existing cloud-offload methods. We tackle this problem by leveraging a previously unexplored opportunity to use early-exit offload enhanced with prioritized communication, dynamic pooling, and dynamic fusion of features. We show that our system, FLEET, achieves significant benefits in accuracy, latency, and compute budget compared to state-of-art local early exit, remote processing, and model partitioning schemes across a range of DNN models, datasets, and IoT platforms.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592514"}, {"primary_key": "1231517", "vector": [], "sparse_vector": [], "title": "MilliSign: mmWave-Based Passive Signs for Guiding UAVs in Poor Visibility Conditions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents MilliSign, a guidance system based on a batteryless tag to support unmanned aerial vehicles in all-weather conditions. Conventional batteryless guidance systems using visual signs fail to work in inclement weather due to poor visibility. The need for all-weather operation with long-range readability encourages the use of millimeter wave (mmWave) radar, which poses challenges in providing a wide 3-D read range and low-cost operation. To address these challenges, we introduce a corner reflector (CR) array-based chipless RFID tag and a one-shot slant range reading procedure with COTS mmWave radar. We establish a novel design method for the shape and alignment of CR units to decrease the tag's size and expand the 3-D read range. Additionally, we develop a signal-processing pipeline based on Root-MUSIC to achieve accurate power and spatial estimation, which facilitate automatic tag detection. Our evaluation demonstrates that the tag, measuring 292 mm × 600 mm × 19 mm and storing 8 bits, can be read by mmWave radar from a distance of more than 10 m with a viewing angle of more than 30° in elevation and azimuth. Moreover, its performance remains stable in poor visibility conditions and multipath-rich environments.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613264"}, {"primary_key": "1231518", "vector": [], "sparse_vector": [], "title": "ARA PAWR: Wireless Living Lab for Smart and Connected Rural Communities.", "authors": ["Taimoor Ul Islam", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ataberk Atalar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Ozdal Boyraz", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As the Platform for Advanced Wireless Research (PAWR) in rural broadband, the ARA wireless living lab features the deployment of first-of-its-kind wireless access and backhaul platforms in real-world agriculture and rural settings, and preliminary experiments have demonstrated very promising results, e.g., up to 3.2 Gbps wireless access throughput and more than 10 Gbps throughput across a wireless backhaul link of over 10 km. ARA is expected to be publicly released for broad community use starting in September 2023. Through this demo, we plan to share, for the first time, with the wireless research community the transformative research experiments enabled by ARA. To stimulate discussion and community participation, we will demonstrate a few example experiments ranging from MU-MIMO in TV White Space (TVWS) bands to long-range mmWave and microwave backhaul communications, as well as open-source 5G NR protocol stacks such as srsRAN and OpenAirInterface.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614068"}, {"primary_key": "1231520", "vector": [], "sparse_vector": [], "title": "A Compact and Real-Time Millimeter-wave Experiment Framework with True Mobility Capabilities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>a M. M.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) communications are crucial for unlocking the potential of future communication systems. Phased arrays play a vital role in generating directional beams, mitigating the high path-loss associated with these frequencies. Consequently, implementing beamforming within the radio is necessary to serve users in various directions. Applications such as AR/VR and vehicular communications demand rapid beam direction changes, typically within microseconds, to maintain stable connections when the user is mobile. In this demonstration, we showcase a mmWave setup capable of switching beams at microsecond intervals, along with a compact and portable user equipment setup for conducting experiments with high user mobility.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614087"}, {"primary_key": "1231522", "vector": [], "sparse_vector": [], "title": "Fast, Fine-grained, and Robust Grouping of RFIDs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the design, implementation, and evaluation of TaGroup, a fast, fine-grained, and robust grouping technique for RFIDs. It can achieve a nearly 100% accuracy in distinguishing multiple groups of closely located RFIDs, within only a few seconds. It would benefit many inventory tracking applications, such as self-checkout in retails and packaging quality control in logistics.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592510"}, {"primary_key": "1231523", "vector": [], "sparse_vector": [], "title": "MilliMobile: An Autonomous Battery-free Wireless Microrobot.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Shwetak N. Patel", "<PERSON><PERSON><PERSON>"], "summary": "We present MilliMobile: a first of its kind battery-free autonomous robot capable of operating on harvested solar and RF power. We challenge the conventional assumption that motion and actuation are beyond the capabilities of battery-free devices and demonstrate completely untethered autonomous operation in realistic indoor and outdoor lighting as well as RF power delivery scenarios. We show first that through miniaturizing a robot to gram scale, we can significantly reduce the energy required to move it. Second, we develop methods to produce intermittent motion by discharging a small capacitor (47--150 μF) to move a motor in discrete steps, enabling motion from as little as 50 μW of power or less. We further develop software defined techniques for maximizing power harvesting. MilliMobile operates in the optimal part of the charging curve by varying the charging time to achieve maximum speeds of up to 5.5 mm/s.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613304"}, {"primary_key": "1231525", "vector": [], "sparse_vector": [], "title": "mmSV: mmWave Vehicular Networking using Street View Imagery in Urban Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As we move towards a future of connected and autonomous vehicles, high-speed and low-latency connectivity between vehicles is becoming increasingly important. This paper investigates enabling high data rate mmWave links in vehicle-to-vehicle (V2V) scenarios using street view images. We find that mmWave V2V links in urban settings suffer from frequent and prolonged blockages, resulting in unreliable connection and high beamforming overhead. Our work proposes mmSV, a system that creates 3D reflection profiles from street view images to assist vehicles in finding mmWave reflections from the environment in real-time. mmSV consists of two key components: material identification which identifies materials from street view images to determine their reflectivity and create 3D reflection map, and environment-driven ray-tracing and beamsearching which finds a high-SNR beam using predicted 3D material maps. Our extensive experimental results on the mmWave testbed show that mmSV can provide highly reliable V2V mmWave connectivity with low beamforming overhead.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613291"}, {"primary_key": "1231526", "vector": [], "sparse_vector": [], "title": "AccessWear: Making Smartphone Applications Accessible to Blind Users.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present AccessWear, a system that improves the accessibility of smartphone touchscreen interactions for blind users using smartwatch gestures. Our system design is human-centered, namely, it incorporates the design goals that were learned from a formative user study with 9 blind participants. The formative study showed that blind users liked the idea of using smartwatch gestures as an alternative: 4 participants liked that when using smart-watch gestures, they did not have to bring their expensive phones out in public and 6 participants liked that smart-watch gestures can be performed with one-hand, as the other hand is usually occupied in holding a cane or a guide dog. Even though there are several advantages to smartwatch gestures, our study also shows that gestures performed by blind users have different patterns compared to sighted users, making gesture recognition more challenging. To this end, AccessWear makes two contributions. The first is a gesture recognition system that works specifically for blind users that is lightweight and does not require per-person training. The second is a near-zero-effort gesture replacement system that does not require any changes to the original application. AccessWear uses input virtualization techniques so that a given gesture can replace the touchscreen input seamlessly. We implement AccessWear on an Android smartphone and Android watch. We perform a quantitative and qualitative study with 8 blind participants. Our study shows that AccessWear can recognize gestures with a 92% accuracy and the end-to-end latency when using an alternate gesture was 53 msec on average. The qualitative study shows that when participants perform a task, consisting of a series of gestures, the system is robust, does not have perceived delays, and does not add physical or mental load on the users.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592495"}, {"primary_key": "1231527", "vector": [], "sparse_vector": [], "title": "A Joint Analysis of Input Resolution and Quantization Precision in Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning models have become increasingly prevalent in various domains, necessitating their deployment on resource-constrained devices. Quantization is a promising way to reduce the model complexity in that it keeps model architecture intact and enables the model to operate on specialized hardwares(e.g., NPU, DSP). Input resolution is also essential in making a trade-off between accuracy and computation.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615753"}, {"primary_key": "1231529", "vector": [], "sparse_vector": [], "title": "Brain Sensing with Ultrasound Tomography and Deep Learning Algorithms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ultrasound computer tomography (USCT) represents a medical imaging modality designed to visualize alterations in the speed of ultrasonic waves. The primary objective of the study presented was to devise a lightweight, portable, and cost-effective tomographic device capable of non-invasively capturing internal images of the human brain in real-time. To achieve this aim, a prototype ultrasonic tomograph was developed, comprising a lightweight head hoop integrated with ultrasonic transducers and a tomograph unit. Ultrasonic measurements were transformed into images using a heterogeneous convolutional neural network (CNN). The USCT system was engineered to facilitate wireless communication between the sensors embedded within the wearable head cap and the tomographic apparatus.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615734"}, {"primary_key": "1231530", "vector": [], "sparse_vector": [], "title": "Squint: A Framework for Dynamic Voltage Scaling of Image Sensors Towards Low Power IoT Vision.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Energy-efficient visual sensing is of paramount importance to enable battery-backed low power IoT and mobile applications. Unfortunately, modern image sensors still consume hundreds of milliwatts of power, mainly due to analog readout. This is because current systems always supply a fixed voltage to the sensor's analog circuitry, leading to higher power profiles. In this work, we propose to aggressively scale the analog voltage supplied to the camera as a means to significantly reduce sensor power consumption. To that end, we characterize the power and fidelity implications of analog voltage scaling on three off-the-shelf image sensors. Our characterization reveals that analog voltage scaling reduces sensor power but also degrades image quality. Furthermore, the degradation in image quality situationally affects the task accuracy of vision applications.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613303"}, {"primary_key": "1231531", "vector": [], "sparse_vector": [], "title": "A Framework for Dynamic Voltage Scaling of Image Sensors Towards Low Power IoT Vision.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Energy-efficient visual sensing is crucial for enabling battery-backed low power IoT and mobile applications. However, modern image sensors still exhibit high power consumption, primarily attributed to analog readout. This high power consumption arises from the conventional practice of supplying a fixed voltage to the analog circuitry of image sensors. Towards that end, we propose to aggressively scale analog voltage supplied to the camera as means to significantly reduce sensor power consumption. This demonstration showcases the potential of dynamic voltage scaling on commercial image sensors within an RPi-based video streaming pipeline. We enable users to flexibly configure sensor voltage settings and observe the impact on both image fidelity and sensor power consumption. Furthermore, this demonstration offers a framework for studying the implications of flexible voltage specification in the context of a person tracking application.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614071"}, {"primary_key": "1231532", "vector": [], "sparse_vector": [], "title": "AccuMO: Accuracy-Centric Multitask Offloading in Edge-Assisted Mobile Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Immersive applications such as Augmented Reality (AR) and Mixed Reality (MR) often need to perform multiple latency-critical tasks on every frame captured by the camera, which all require results to be available within the current frame interval. While such tasks are increasingly supported by Deep Neural Networks (DNNs) offloaded to edge servers due to their high accuracy but heavy computation, prior work has largely focused on offloading one task at a time. Compared to offloading a single task, where more frequent offloading directly translates into higher task accuracy, offloading of multiple tasks competes for shared edge server resources, and hence faces the additional challenge of balancing the offloading frequencies of different tasks to maximize the overall accuracy and hence app QoE.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592531"}, {"primary_key": "1231533", "vector": [], "sparse_vector": [], "title": "Dynamic Spectrum Access in Terrestrial and Non-Terrestrial Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic spectrum access (DSA) has shown its benefits in various configurations of coexisting wireless networks. In this work, we discuss the ways for effective DSA between the terrestrial and non-terrestrial (satellite) networks. It is shown that the usage of historical statistical data leads to efficient resource sharing between these two kinds of networks at a limited interference cost.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615726"}, {"primary_key": "1231534", "vector": [], "sparse_vector": [], "title": "A Measurement System for Distributed UWB-based Ranging and Localization in Snow Avalanches.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a systems approach for tracking of mobile systems in an avalanche scenario using an ultra-wideband (UWB)-based ranging and localization system. UWB-based positioning is particularly challenging in outdoor scenarios covering large distances in complex topography. In the long-term, we are interested in tracking the motion of snow avalanches; particularly their inner dynamics are still unknown since they remain hidden for most observation approaches. Our system model considers multiple anchors distributed with inter-node distances in the order of a few hundred meters. Mobile nodes, which move with the avalanche in the field, are tracked via UWB time-of-flight measurements and further supported by inertial measurement units. Our system further integrates LoRa and IEEE 802.11 mesh for configuration and management, with UWB for measurement and reporting. First measurement results in the field show good accuracy and confirm the design choice.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615730"}, {"primary_key": "1231535", "vector": [], "sparse_vector": [], "title": "CoreKube: An Efficient, Autoscaling and Resilient Mobile Core System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the central role mobile core plays in supporting mobile network operations, the efficiency, cost-effective dynamic scalability and resilience of the core control plane are paramount. Achieving these goals, however, presents two main challenges: (i) decoupling core network state from processing; (ii) decoupling control plane processing in the core from its interface to the radio access network (RAN). To overcome them, we present CoreKube, a novel message focused and cloud-native mobile core system design, which features truly stateless workers (processing units) that interface with a common database (to hold the core network state) and with the RAN through a frontend. The fully stateless and generic nature of the workers to process any control plane message enables efficient message handling. Orchestration of containerized CoreKube components using Kubernetes, allows leveraging the latter's autoscaling and self-healing properties. We develop 4G and 5G standard-compliant CoreKube implementations, exploiting the agile development methodology enabled by CoreKube's message focused design. Results from our extensive experimental evaluations over the Powder platform relative to prior art show that CoreKube efficiently processes control plane messages, scales dynamically while using minimal compute resources and recovers seamlessly from failures.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592522"}, {"primary_key": "1231536", "vector": [], "sparse_vector": [], "title": "CoreKube - A Message Focused and Cloud Native Mobile Core System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the central role that the mobile core plays in supporting mobile network operations, the efficiency, cost-effective dynamic scalability and resilience of the core control plane are paramount. Achieving these goals, however, presents two main challenges: (i) decoupling core network state from processing; (ii) decoupling control plane processing in the core from its interface to the radio access network (RAN). To address these challenges, our proposed solution, CoreKube, is based on a novel message-focused and cloud-native design with truly stateless workers that interface with a common database (to hold the core network state) and with the RAN through a frontend in a standard compliant manner. The fully stateless and generic nature of the workers to process any control plane message enables efficient message handling. Orchestration of containerized CoreKube components using Kubernetes allows leveraging the latter's autoscaling and self-healing properties. This demo highlights three key features of CoreKube: dynamic scaling of core in the face of varying control plane traffic while maintaining low user-perceived latency, resilience to failures, and the ability to seamlessly interface with standard-compliant RAN and commodity hardware.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614075"}, {"primary_key": "1231537", "vector": [], "sparse_vector": [], "title": "RAN Simulator Is NOT What You Need: O-RAN Reinforcement Learning for the Wireless Factory.", "authors": ["Ta Dang Kho<PERSON> Le", "<PERSON><PERSON>"], "summary": "As modern manufacturing lines embrace greater modularity and flexibility, the need to transition factory networks from wired to wireless grows. Yet the mission-critical nature of factory networks poses a key challenge - connecting numerous diverse machines with high QoS predictability. After formulating this challenge as predictable RAN optimization via Reinforcement Learning (RL), we highlight a major-yet-overlooked modeling issue: matching the packet handling mechanics of a production/real RAN software. In this paper, we show that these mismatches inside RAN simulators can cause non-trivial QoS gaps in production. Then, we present Twin5G, a novel training solution that brings scalable and near-discrete-time emulations to real RAN software, removing the need for RAN simulators. In a RAN Slicing example, Twin5G-trained policy outperforms simulator-trained and standard RL-trained policies in both QoS achieved (+16%) and predictability (+19%) during tests.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615758"}, {"primary_key": "1231538", "vector": [], "sparse_vector": [], "title": "FarfetchFusion: Towards Fully Mobile Live 3D Telepresence Platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present FarfetchFusion, a fully mobile live 3D telepresence system. Enabling mobile live telepresence is a challenging problem as it requires i) realistic reconstruction of the user and ii) high responsiveness for immersive experience. We first thoroughly analyze the live 3D telepresence pipeline and identify three critical challenges: i) 3D data streaming latency and compression complexity, ii) computational complexity of volumetric fusion-based 3D reconstruction, and iii) inconsistent reconstruction quality due to sparsity of mobile 3D sensors. To tackle the challenges, we propose a disentangled fusion approach, which separates invariant regions and dynamically changing regions with our low-complexity spatio-temporal alignment technique, topology anchoring. We then design and implement an end-to-end system, which achieves realistic reconstruction quality comparable to existing server-based solutions while meeting the real-time performance requirements (<100 ms end-to-end latency, 30 fps throughput, <16 ms motion-to-photon latency) solely relying on mobile computation capability.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592525"}, {"primary_key": "1231539", "vector": [], "sparse_vector": [], "title": "Introducing FreeSpeaker - A Modular Smart Home Hub Prototyping Platform.", "authors": ["<PERSON>", "Jonatan Crystall", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart home speakers have become a commodity item in many households and provide interesting research opportunities in areas like wireless communication and human-computer interaction. Commercial devices do not provide sufficient access for many research tasks. We present a modular smart home hub designed specifically for research purposes. The electronic and mechanical components are designed with reproducibility in mind and can be easily recombined for a project's needs. Additionally, we show applications of the hub in different scenarios.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614080"}, {"primary_key": "1231540", "vector": [], "sparse_vector": [], "title": "A Networking Perspective on Starlink&apos;s Self-Driving LEO Mega-Constellation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Low-earth-orbit (LEO) satellite mega-constellations, such as SpaceX Starlink, are under rocket-fast deployments and promise broadband Internet to remote areas that terrestrial networks cannot reach. For mission safety and sustainable uses of space, Starlink has adopted a proprietary onboard autonomous driving system for its extremely mobile LEO satellites. This paper demystifies and diagnoses its impacts on the LEO mega-constellation and satellite networks. We design a domain-specific method to characterize key components in Starlink's autonomous driving from various public space situational awareness datasets, including continuous orbit maintenance, collision avoidance, and maneuvers between orbital shells. Our analysis shows that, these operations have mixed impacts on the stability and performance of the entire mega-constellation, inter-satellite links, topology, and upper-layer network functions. To this end, we investigate and empirically assess the potential of networking-autonomous driving co-designs for the upcoming satellite networks.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592519"}, {"primary_key": "1231541", "vector": [], "sparse_vector": [], "title": "Task Offloading with Multi-cluster Collaboration for Computing and Network Convergence.", "authors": ["<PERSON>", "<PERSON>", "Zhaojiang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Edge computing servers have been widely deployed in recent years to address the requirements of diverse tasks that are sensitive to delays and computationally intensive. However, due to their independent nature and uneven distribution of service requests, certain clusters may be relatively idle, while others may be overloaded. This situation can result in increased latency for certain tasks, and it prevents the full utilization of resources in the edge clusters. To mitigate this problem, we design and implement a prototype testbed for task offloading, aimed at achieving computing and network convergence. This testbed facilitates collaboration among multiple edge computing clusters. We construct multiple clusters using Intel NUC mini computers and incorporate key enabling technologies into the system. We assess the testbed's performance by employing multiple video processing services that require low latency and high computational capacity. In scenarios with uneven service requests, load balancing can be achieved across the edge computing clusters, resulting in reduced response latency for user tasks.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614064"}, {"primary_key": "1231542", "vector": [], "sparse_vector": [], "title": "Go Beyond RFID: Rethinking the Design of RFID Sensor Tags for Versatile Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "Li Lu"], "summary": "Designing ultra-low power RFID sensor tags is a major challenge, especially when incorporating a micro-controller (MCU) to operate sensors. While simplifying MCU functionality can reduce power consumption, it has limited effect as the fundamental information transformation is necessary for communication between the RFID reader and the sensor. Unfortunately, information transformation requires baseband sampling and processing, which consumes significant power on passive RFID tags. This paper proposes a novel approach that enables the reader to communicate directly with the sensor, eliminating the need for information transformation of MCU. We address the unique challenges posed by the physical and link layers of the EPC Gen2 protocol and introduce GoodID, a cross-layer design for next-generation RFID sensor tags featuring ultra-low power consumption. We prototype the GoodID tag for proof-of-concept and demonstrate significant power benefits through experimental results.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613284"}, {"primary_key": "1231543", "vector": [], "sparse_vector": [], "title": "TherapyPal: Towards a Privacy-Preserving Companion Diagnostic Tool based on Digital Symptomatic Phenotyping.", "authors": ["Huining Li", "<PERSON><PERSON>", "Ruo<PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the demand for precision medicine rapidly grows, companion diagnostics is proposed to monitor and evaluate therapeutic effects for adjusting medicine plans in time. Although a set of clinical companion diagnostics tools (e.g., polymerase chain reaction) have been investigated, they are expensive and only accessible in a lab environment, which hinders the promotion to broader patients. In light of this situation, we take the first steps towards developing a real-world companion diagnostic tool by leveraging mobile technology. In this paper, we present TherapyPal, a privacy-preserving medicine effectiveness computational framework by harnessing semantic hashing-based digital symptomatic phenotyping. Specifically, sensor data captured from daily-life activities is first transformed into spectrograms. Then, we develop a hashing learning network to extract privacy-masked symptomatic phenotypes on smartphones. Afterward, symptomatic hashes at different medicine states are fed to a contrastive learning network in the cloud for treatment effectiveness detection. To evaluate the performance, we conduct a clinical study among 65 Parkinson's disease (PD) patients under dopaminergic drug treatment. The results show that TherapyPal can achieve around 84.1% medicine effectiveness detection accuracy among patients and above 0.925 privacy-masked scores for protecting each private attribute, which validates the reliability and security of TherapyPal to be used as a real-world companion diagnostics tool.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592499"}, {"primary_key": "1231544", "vector": [], "sparse_vector": [], "title": "DroidPerf: Profiling Memory Objects on Android Devices.", "authors": ["<PERSON><PERSON><PERSON>", "Qi<PERSON> Zhao", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Optimizing performance inefficiencies in memory hierarchies is well-known for native languages, such as C and C++. There are few studies, however, on exploring memory inefficiencies in Android Runtime (ART). Running in ART, managed languages, such as Java and Kotlin, employ various abstractions, such as runtime support, ahead-of-time (AOT) compilation, and garbage collection (GC), which hide important execution details from the plain source code.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592503"}, {"primary_key": "1231545", "vector": [], "sparse_vector": [], "title": "CA++: Enhancing Carrier Aggregation Beyond 5G.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Carrier aggregation (CA) is an important component technology in 5G and beyond. It aggregates multiple spectrum fragments to serve a mobile device. However, the current CA suffers under both high mobility and increased spectrum space. The limitations are rooted in its sequential, cell-by-cell operations. In this work, we propose CA++, which departs from the current paradigm and explores a group-based design scheme. We thus propose new algorithms that enable concurrent channel inference by measuring one or few cells but inferring all, while minimizing measurement cost via set cover approximations. Our evaluations have confirmed the effectiveness of CA++. Our solution can also be adapted to fit in the current 5G OFDM PHY and the 3GPP framework.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592500"}, {"primary_key": "1231546", "vector": [], "sparse_vector": [], "title": "SWAM: Revisiting Swap and OOMK for Improving Application Responsiveness on Mobile Devices.", "authors": ["Geunsik Lim", "<PERSON><PERSON><PERSON>", "MyungJoo Ham", "Young Ik Eom"], "summary": "Existing memory reclamation policies on mobile devices may be no longer valid because they have negative effects on the response time of running applications. In this paper, we propose SWAM, a new integrated memory management technique that complements the shortcomings of both the swapping and killing mechanism in mobile devices and improves the application responsiveness. SWAM consists of (1) Adaptive Swap that performs swapping adaptively into memory or storage device while managing the swap space dynamically, (2) OOM Cleaner that reclaims shared object pages in the swap space to secure available memory and storage space, and (3) EOOM Killer that terminates processes in the worst case while prioritizing the lowest initialization cost applications as victim processes first. Experimental results demonstrate that SWAM significantly reduces the number of applications killed by OOMK (6.5x lower), and improves application launch time (36% faster) and response time (41% faster), compared to the conventional schemes.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592518"}, {"primary_key": "1231547", "vector": [], "sparse_vector": [], "title": "A Workload-Aware DVFS Robust to Concurrent Tasks for Mobile Devices.", "authors": ["<PERSON><PERSON> Lin", "<PERSON><PERSON>", "Zhenjiang Li", "<PERSON>"], "summary": "Power governing is a critical component of modern mobile devices, reducing heat generation and extending device battery life. A popular technology of power governing is dynamic voltage and frequency scaling (DVFS), which adjusts the operating frequency of a processor to balance its performance and energy consumption. With the emergence of diverse workloads on mobile devices, traditional DVFS methods that do not consider workload characteristics become suboptimal. Recent application-oriented methods propose dedicated and effective DVFS governors for individual application tasks. Since their approach is only tailored to the targeted task, performance drops significantly when other tasks run concurrently, which is however common on today's mobile devices. In this paper, our key insight is that hardware meta-data, widely used in existing DVFS designs, has great potential to enable capable workload awareness and task concurrency adaptability for DVFS, but they are underexplored. We find that workload characteristics can be described in a hyperspace composed of multiple dimensions derived from these metadata to form a novel workload contextual indicator to profile task dynamics and concurrency. On this basis, we propose a meta-state metric to capture this relationship and design a new solution, GearDVFS. We evaluate it for a rich set of application tasks, and it outperforms state-of-the-art methods.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592524"}, {"primary_key": "1231549", "vector": [], "sparse_vector": [], "title": "TinyRIC: Supercharging O-RAN Base Stations with Real-time Control.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The emergence of latency-critical use cases for cellular networks has necessitated the need for real-time (RT) network performance optimization. While solutions such as the O-RAN architecture provide primitives for non-RT and near-RT control, the notion of RT control is still missing. To that end, in this paper, we introduce TinyRIC, a first-of-its-kind RT control platform for O-RAN base stations. Key highlights include a comprehensive system architecture design, a systems-level implementation in support of flexible user scheduling, and a preliminary over-the-air experimental evaluation to demonstrate the system's performance and feasibility.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615743"}, {"primary_key": "1231550", "vector": [], "sparse_vector": [], "title": "M3A: Multipath Multicarrier Misinformation to Adversaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Di <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wireless channels are vulnerable to eavesdroppers due to their broadcast nature. One approach to thwart an eavesdropper (<PERSON>) is to decrease her SNR, e.g., by reducing the signal in her direction. Unfortunately, such methods are vulnerable to (1) a highly directional <PERSON> that can increase her received signal strength and (2) <PERSON> that is close to the receiver, <PERSON>, or close to the transmitter, <PERSON>. In this paper, we design and experimentally evaluate Multipath Multicarrier Misinformation to Adversaries (M3A), a system for <PERSON> to send data to <PERSON> while simultaneously sending misinformation to <PERSON>. Our approach does not require knowledge of <PERSON>'s channel or location and, with multipath channels, randomly transforms <PERSON>'s symbols even if <PERSON> is located one wavelength-scale distance from <PERSON> (approximately 10 cm) or if <PERSON> is located between <PERSON> and <PERSON> in their direct path (<PERSON> is approximately 1/3 closer to <PERSON>). In particular, our approach is to move each of <PERSON>'s received symbols (over time and across subcarriers), to an independently random transformation as compared to <PERSON>, without <PERSON> or <PERSON> knowing <PERSON>'s location or channel. We realize this by modulating <PERSON>'s per-subcarrier beamforming weights with an i.i.d. random binary sequence, as if <PERSON> had a separate antenna array for each subcarrier, and could randomly turn antennas in each array on and off. We implement M3A on a real-time Massive MIMO testbed and show that <PERSON>3<PERSON> can increase <PERSON>'s bit error rate more than two hundredfold compared to beamforming, even if she is positioned approximately a wavelength away, whether above, below, or beside <PERSON>. Finally, to ensure reliability at <PERSON>, we show that with M3<PERSON>, <PERSON>'s bit error rate is approximately an order of magnitude lower than achieved with prior work.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613282"}, {"primary_key": "1231551", "vector": [], "sparse_vector": [], "title": "X-Plane: A High-Throughput Large-Capacity 5G UPF.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Biao Lyu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud providers, such as AWS and Azure, have started providing 5G services on their cloud infrastructure. In this paper, we present the design and implementation of X-Plane, a system that uses commercial programmable ASICs and DRAM servers on today's cloud infrastructure to implement high-performance 5G User Plane Function (UPF). Building X-Plane is hard because we need to address the following challenges: consistency issues when concurrently accessing UPF state data, slow UE table lookup due to repetitive and numerous Packet Detection Rule (PDR) matching, and the need to handle out-of-order packets from disconnected UEs. X-Plane addresses these challenges by designing three novel technologies: concurrent state data access protocol, fast flow table and paging buffer for handling out-of-order packets. We demonstrate its feasibility and practicality with our implementation on a Tofino-based programmable ASIC. Our evaluation shows that X-Plane can support over ~490Gbps throughput per ASIC pipeline, over 10 million UEs, and finish packet processing within predictable ~4 us on average.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613267"}, {"primary_key": "1231553", "vector": [], "sparse_vector": [], "title": "Cost-effective On-device Continual Learning over Memory Hierarchy with Miro.", "authors": ["Xi<PERSON>ue Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Continual learning (CL) trains NN models incrementally from a continuous stream of tasks. To remember previously learned knowledge, prior studies store old samples over a memory hierarchy and replay them when new tasks arrive. Edge devices that adopt CL to preserve data privacy are typically energy-sensitive and thus require high model accuracy while not compromising energy efficiency, i.e., cost-effectiveness. Our work is the first to explore the design space of hierarchical memory replay-based CL to gain insights into achieving cost-effectiveness on edge devices. We present Miro, a novel system runtime that carefully integrates our insights into the CL framework by enabling it to dynamically configure the CL system based on resource states for the best cost-effectiveness. To reach this goal, Miro also performs online profiling on parameters with clear accuracy-energy trade-offs and adapts to optimal values with low overhead. Extensive evaluations show that Miro significantly outperforms baseline systems we build for comparison, consistently achieving higher cost-effectiveness.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613297"}, {"primary_key": "1231554", "vector": [], "sparse_vector": [], "title": "QfaR: Location-Guided Scanning of Visual Codes from Long Distances.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Visual codes such as QR codes provide a low-cost and convenient communication channel between physical objects and mobile devices, but typically operate when the code and the device are in close physical proximity. We propose a system, called QfaR, which enables mobile devices to scan visual codes across long distances even where the image resolution of the visual codes is extremely low. QfaR is based on location-guided code scanning, where we utilize a crowd-sourced database of physical locations of codes. Our key observation is that if the approximate location of the codes and the user is known, the space of possible codes can be dramatically pruned down. Then, even if every \"single bit\" from the low-resolution code cannot be recovered, QfaR can still identify the visual code from the pruned list with high probability. By applying computer vision techniques, QfaR is also robust against challenging imaging conditions, such as tilt, motion blur, etc. Experimental results with common iOS and Android devices show that QfaR can significantly enhance distances at which codes can be scanned, e.g., 3.6cm-sized codes can be scanned at a distance of 7.5 meters, and 0.5m-sized codes at about 100 meters. QfaR has many potential applications, and beyond our diverse experiments, we also conduct a simple case study on its use for efficiently scanning QR code-based badges to estimate event attendance.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592497"}, {"primary_key": "1231555", "vector": [], "sparse_vector": [], "title": "Softly, Deftly, <PERSON><PERSON> Unfurl Their Splendor: Rolling Flexible Surfaces for Wideband Wireless.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With new frequency bands opening up, emerging wireless IoT devices are capitalizing on an increasingly divergent range of frequencies. However, existing coverage provisioning practice is often tied to specific standards and frequencies. There is little shareable wireless infrastructure for concurrent links on different frequencies, across networks and standards.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592520"}, {"primary_key": "1231556", "vector": [], "sparse_vector": [], "title": "Breaking Mobile Notification-based Authentication with Concurrent Attacks Outside of Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Notification-based authentication is an emerging Two-Factor Authentication (2FA) and passwordless solution that leverages interactive notifications on mobile devices to establish an additional layer of security beyond passwords. This method has gained popularity due to its convenience and ease of deployment in organizational settings. In this work, we aim to evaluate the effectiveness of notification-based authentication systems when a malicious entity is present on the user's computer, such as a keylogger or malicious extension, without compromising the mobile devices or communication channels. Furthermore, we investigate how the lack of information provided to users during the authentication workflow can lead to the approval of malicious authentication requests. Notably, we highlight the vulnerability of cross-service attacks, where an attacker authenticates to Service B while the user is attempting to authenticate to Service A. Our proof-of-concept attack program demonstrates the susceptibility of various notification-based authentication systems, and our user study reveals an alarming 82.2% cross-service attack success rate. These findings suggest a potential vulnerability in notification-based authentication systems, where the attacker compromise user account without compromising possession-factor device, such as smartphones.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613273"}, {"primary_key": "1231557", "vector": [], "sparse_vector": [], "title": "Cross-Modal Perception for Customer Service.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Artificial Intelligence offers cost-effective solutions to improve business processes and ensure more satisfying customer service. The advantage of solutions based on artificial intelligence is the possibility of using the API with mobile or stationary applications and cloud services. The research presented here aims to develop a deep learning model using cross-modal techniques on the example of a multi-tasking network. The main task is to use computer vision on IoT devices using sensors for customer service. Additionally, the solution will be based on distributed systems. Finally, the method of building the multi-tasking model, which will be designed to determine the person in the image and their emotional state, will be verified.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615751"}, {"primary_key": "1231558", "vector": [], "sparse_vector": [], "title": "Experimental Study of Wavy Surface Effects on Uplink Water-Air Optical Camera Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents an experimental study of water-to-air optical camera communication (OCC) in the presence of wavy surface conditions. It evaluates the system performance in terms of the signal-to-noise ratio (SNR), considering various camera exposure times and regions of interest (ROI) in captured images. The results reveal a noticeable reduction in the SNR due to the presence of surface waves. However, the system performance is improved by considering the average value of several illuminated pixels produced by the point spread function as the ROI. Furthermore, the statistical characteristics of the received optical signals in the presence of surface waves are assessed.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615736"}, {"primary_key": "1231559", "vector": [], "sparse_vector": [], "title": "Enabling Edge processing on LoRaWAN architecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRaWAN is a wireless technology that enables high-density deployments of IoT devices. Designed for Low Power Wide Area Networks (LPWAN), LoRaWAN employs large cells to service a potentially extremely high number of devices. The technology enforces a centralized architecture, directing all data generated by the devices to a single network server for data processing. End-to-end encryption is used to guarantee the confidentiality and security of data. In this demo, we present Edge2LoRa, a system architecture designed to incorporate edge processing in LoRaWAN without compromising the security and confidentiality of data. Edge2LoRa maintains backward compatibility and addresses scalability issues arising from handling large amounts of data sourced from a diverse range of devices. The demo provides evidence of the advantages in terms of reduced latency, lower network bandwidth requirements, higher scalability, and improved security and privacy resulting from the application of the Edge processing paradigm to LoRaWAN.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614074"}, {"primary_key": "1231562", "vector": [], "sparse_vector": [], "title": "A Novel Intelligent Management System Architecture for Hybrid VLC/RF Systems in Smart Retail Environment.", "authors": ["<PERSON><PERSON>", "Stefano <PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a novel intelligent management system (IMS) for smart retail environments that integrates various components to optimize user experience, energy efficiency, and seamless connectivity. The proposed architecture incorporates an Intelligent Handover Controller, an Energy Harvesting Module, Adaptive Resource Management, Edge Tracking and Localization. Preliminary testbed results show promising performance in maintaining seamless handovers between VLC and WiFi links, as well as accurate tracking of user mobility based on CSI. This advanced architecture offers the potential for significant improvements in smart retail operations by providing uninterrupted connectivity, targeted marketing, optimized store layouts, and enhanced user experiences.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615725"}, {"primary_key": "1231564", "vector": [], "sparse_vector": [], "title": "XPorter: A Study of the Multi-Port Charger Security on Privacy Leakage and Voice Injection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-port chargers, capable of simultaneously charging multiple mobile devices such as smartphones, have gained immense popularity and sold millions of units in recent years. However, this charging-targeted feature can also pose security and privacy risks by allowing one of the simultaneously charging devices to communicate with another one if not properly designed and implemented as these devices are actually interconnected. Unfortunately, such risks have not been thoroughly investigated and we have identified a novel attack surface in the circuit design of multi-port chargers, which allows an adversary to exploit one port to (i) eavesdrop on the activities of other devices being charged and (ii) inaudibly inject malicious audio commands if the charging device supports voice assistants and USB-C interface.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613293"}, {"primary_key": "1231565", "vector": [], "sparse_vector": [], "title": "Exploiting Contactless Side Channels in Wireless Charging Power Banks for User Privacy Inference via Few-shot Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, power banks for smartphones have begun to support wireless charging. Although these wireless charging power banks appear to be immune to most reported vulnerabilities in either power banks or wireless charging, we have found a new contactless wireless charging side channel in these power banks that leaks user privacy from their wireless charging smartphones without compromising either power banks or victim smartphones. We have proposed BankSnoop to demonstrate the practicality of the newly discovered wireless charging side channel in power banks. Specifically, it leverages the coil whine and magnetic field disturbance emitted by a power bank when wirelessly charging a smartphone and adopts the few-shot learning to recognize the app running on the smartphone and uncover keystrokes. We evaluate the effectiveness of BankSnoop using commodity wireless charging power banks and smartphones, and the results show it achieves over 90% accuracy on average in recognizing app launching and keystrokes. It also presents high adaptability when apply to different smartphone models, power banks, etc., achieving over 85% accuracy with 10-shot learning.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613288"}, {"primary_key": "1231566", "vector": [], "sparse_vector": [], "title": "MU-MIMO, Open-RAN PHY with Linear and Massively Parallelizable Non-Linear Processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-user multiple-input, multiple-output (MU-MIMO) designs can substantially increase the achievable throughput and connectivity capabilities of wireless systems. However, existing MU-MIMO deployments typically employ linear processing that, despite its practical benefits, can leave capacity and connectivity gains unexploited. On the other hand, traditional non-linear processing solutions (e.g., sphere decoders) promise improved throughput and connectivity capabilities, but can be impractical in terms of processing complexity and latency, and with questionable practical benefits that have not been validated in actual system realizations. At the same time, emerging new Open Radio Access Network (Open-RAN) designs call for physical layer (PHY) processing solutions that are also practical in terms of realization, even when implemented purely on software. This work demonstrates the gains that our highly efficient, massively parallelizable, non-linear processing (MPNL) framework can provide, both in the uplink and downlink, when running in real-time and over-the-air, using our new 5G-New Radio (5G-NR) and Open-RAN compliant, software-based PHY. We showcase that our MPNL framework can provide substantial throughput and connectivity gains, compared to traditional, linear approaches, including increased throughput, the ability to halve the number of base-station antennas without any performance loss compared to linear approaches, as well as the ability to support a much larger number of users than base-station antennas, without the need for any traditional Non-Orthogonal Multiple Access (NOMA) techniques, and with overloading factors that can be up to 300%.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614073"}, {"primary_key": "1231567", "vector": [], "sparse_vector": [], "title": "Effective Abnormal Activity Detection on Multivariate Time Series Healthcare Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multivariate time series (MTS) data collected from multiple sensors provide the potential for accurate abnormal activity detection in smart healthcare scenarios. However, anomalies exhibit diverse patterns and become unnoticeable in MTS data. Consequently, achieving accurate anomaly detection is challenging since we have to capture both temporal dependencies of time series and inter-relationships among variables. To address this problem, we propose a Residual-based Anomaly Detection approach, Rs-AD, for effective representation learning and abnormal activity detection. We evaluate our scheme on a real-world gait dataset and the experimental results demonstrate an F1 score of 0.839.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615741"}, {"primary_key": "1231568", "vector": [], "sparse_vector": [], "title": "Intermittent Low-Power Wide Area Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Low-Power Wide Area Networks (LPWAN) offer long-range communication with low energy consumption, making them ideal for IoT applications powered by energy harvesting. However, unpredictable energy harvesting rates can lead to sub-optimal device operation. To tackle this, the intermittent computing paradigm has been proposed. In this paper, we explore the combination of intermittent computing and LPWANs by proposing four different communication solutions based on LoRa and designed to match common application scenarios and requirements. For the solutions, we also present experimental energy estimation models, which we evaluate against measurements.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615729"}, {"primary_key": "1231569", "vector": [], "sparse_vector": [], "title": "A Self-Adaptive Retro-FSO Design for Air-to-Ground Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guanyu Shi", "<PERSON><PERSON>"], "summary": "With the widespread adoption of mobile networks, the demand for onboard aircraft network connectivity is growing at a significant pace. However, existing solutions for this air-to-ground (ATG) communication scenario, such as satellite communication or 4G/5G ATG communication, fall short in meeting the increasing throughput demands. To address this challenge, leveraging free-space optical (FSO) technology presents a promising opportunity. FSO links have the capability to provide ultra-high throughput up to Tbps[1], offering a significant advantage over RF solutions. However, to ensure the robustness of an ATG FSO link, two key challenges need to be addressed: (i) guaranteeing high-probability link establishment, and (ii) minimizing link loss to ensure reliable communication. In this demonstration, we propose a novel FSO architecture specifically optimized for the ATG scenario. Our design incorporates a retroreflector-based pure-optical feedback mechanism and a self-adaptive tracking and pointing mechanism to tackle these two challenges respectively. To validate the feasibility of our hardware design, we have developed a prototype system and conducted a series of preliminary experiments.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614067"}, {"primary_key": "1231570", "vector": [], "sparse_vector": [], "title": "I Beg to Diffract: RF Field Programming With Edges.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a new paradigm in intelligent surface design for field programming and multi-point focusing. We approach this problem from an entirely different vantage point by leveraging edges and the corresponding Geometrical Theory of Diffraction (GTD), allowing us to avoid the use of highly specialized and expensive element designs. More specifically, we show that a lattice of edge elements (i.e., cheap, thin, rectangular metal plates with length long enough as compared to width) can provide a rich repertoire for programming the RF field. When a wave is incident on an edge, a cone of outgoing rays emerges, known as a Keller cone. When considering a lattice of such edge elements, we then have a rich set of \"knobs\" for RF field programming, via changing the orientation of the edge elements and exploiting the exiting Keller cones. We then show how to electromagnetically model and design a practical edge element. We further propose an efficient algorithm to configure the orientations of the edges to achieve the desired multi-point focusing. We build sample prototypes of our proposed paradigm, using off-the-shelf material (i.e., 7 cent metal plates). We then show several real-world experiments in three different indoor areas, where an edge lattice focuses the transmitted wave of a WiFi card of a laptop on up to and including 4 focal points (maximum achieved in the literature albeit with much more expensive element designs). Overall, the paper shows the rich potential of edges for RF field programming.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613266"}, {"primary_key": "1231571", "vector": [], "sparse_vector": [], "title": "PMSat: Optimizing Passive Metasurface for Low Earth Orbit Satellite Communication.", "authors": ["<PERSON><PERSON> Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shi<PERSON> Zheng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Low Earth Orbit (LEO) satellite communication is essential for wireless communication. While manufacturing and launching LEO satellites have become efficient and cost-effective, ground stations remain expensive due to complex designs for handling severe path losses and precise beam tracking. Hence, it is important to develop low cost and high-performance ground stations for widespread adoption of LEO satellite communication. Towards realizing this goal, we design a passive metasurface-enhanced LEO ground station system, named PMSat, combining metasurface's fine-grained beamforming capability with a small-size phased array's adaptive steering and focusing. For uplink, we jointly optimize the phase array codebook and uplink metasurface phase profile, and realize electronic steering by switching the codeword. We further jointly optimize the downlink metasurface phase profile to improve the focusing performance and enhance the received signal strength (RSS) over a wide range of incident angles. Our PMSat prototype consists of a single passive metasurface with 21 × 21 elements for uplink and 22 × 22 for downlink, along with 1 × 4 receiving and 1 × 4 transmitting phased array antennas. The effectiveness of our proposed PMSat is validated through extensive experiments, and results demonstrate that the optimized metasurface improves the SNR by 8.32 dB and 16.57 dB for uplink and downlink, respectively.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613257"}, {"primary_key": "1231572", "vector": [], "sparse_vector": [], "title": "UbiPose: Towards Ubiquitous Outdoor AR Pose Tracking using Aerial Meshes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Tracking the position and orientation, or pose, of a viewing device enables AR applications to accurately embed virtual content in physical spaces. Mobile OSs track pose by matching device camera images against street-level imagery. Thus, pose tracking is often unavailable at off-street pedestrian locations. UbiPose enables pose tracking at such locations using aerial meshes, generated from satellite imagery, that are likely to be more widely available at these locations. However, matching a camera image against an aerial mesh can be error-prone, even with modern neural matchers. These neural components are also compute-intensive. UbiPose contains a novel pose tracking pipeline that runs entirely on a mobile device using fast-path optimizations designed to accept or reject pose estimates in many cases, without sacrificing accuracy. Experiments on real-world traces show that it achieves tracking accuracy comparable to AR pose tracking in iOS in places where that is available, and is able to track pose accurately in places where it is not.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613263"}, {"primary_key": "1231575", "vector": [], "sparse_vector": [], "title": "Towards Low-cost Sensing with Mobile Backscatter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Analog backscatter enables sensing and communication at lower power consumption than digital backscatter. In analog tags, a sensor typically changes the resistance or capacitance value that then translates into a frequency change that is backscattered on top of a carrier. However, to read the sensor data with high resolution, a powerful receiver is required. The resolution of sensor data is limited by the FFT size and computation at the receiver. This limits the use of low-cost radio receivers, like RTL-SDR, that are gaining popularity in the RF community. We propose to use higher order harmonic frequencies, that are inherently generated by square wave backscattering at no extra cost, to derive sensor data using such low-cost radio receivers. We present experimental results that demonstrate the viability of our approach.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615752"}, {"primary_key": "1231576", "vector": [], "sparse_vector": [], "title": "Be-in/Be-out System for a Smart City using iBeacon Devices.", "authors": ["Aneta Poniszewska-Maranda", "<PERSON><PERSON><PERSON>"], "summary": "The paper presents the solution proposal for the problem of detecting the presence of a passenger in a public transport vehicle in order to calculate the correct fare. The work includes an analysis of existing solutions based on Be-in/Be-out model supported by wireless technologies. The proposed solution focuses on simplifying the computational complexity involved in charging users for the number of stops travelled.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615750"}, {"primary_key": "1231577", "vector": [], "sparse_vector": [], "title": "RadarHD: Demonstrating Lidar-like Point Clouds from mmWave Radar.", "authors": ["<PERSON><PERSON><PERSON>", "Tao Jin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Millimeter wave radars can perceive through occlusions like dust, fog, smoke and clothes. But compared to cameras and lidars, their perception quality is orders of magnitude poorer. RadarHD [3] tackles this problem of poor quality by creating a machine learning super resolution pipeline trained against high quality lidar scans to mimic lidar. RadarHD ingests low resolution radar and generates high quality lidar-like point clouds even in occluded settings. RadarHD can also make use of the high quality output for typical robotics tasks like odometry, mapping and classification using conventional lidar workflows. Here, we demonstrate the effectiveness of RadarHD's point clouds against lidar in occluded settings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614077"}, {"primary_key": "1231579", "vector": [], "sparse_vector": [], "title": "UniScatter: a Metamaterial Backscatter Tag for Wideband Joint Communication and Radar Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Millimeter-wave backscatter can simultaneously support high-precision sensing and massive communication and represent one prominent technical evolution in next-generation wireless systems. The backscatter tags should ideally work across a wide mmWave spectrum range with consistent signal strength and angular coverage to accommodate highly diverse application scenarios. However, existing tags made of resonant antennas and RFICs only achieve a few GHz of bandwidth and hardly meet these requirements. In this paper, we present UniScatter, a new backscatter tag structure based on metamaterials. The key design of UniScatter is a graphene-based modulator and a lens-based retroreflector, which have consistent electromagnetic responses across an extensive frequency range and wide angular field-of-view. We have developed a robust fabrication process for UniScatter, and tested it on various mmWave sensing and communication devices. Our field tests show that UniScatter can backscatter signals across a wide frequency band from 24 GHz to 77 GHz with consistently high signal strength and wide angular coverage in 3D space.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592526"}, {"primary_key": "1231580", "vector": [], "sparse_vector": [], "title": "iSAW: Intelligent Super-Resolution-Assisted Adaptive WebRTC Video Streaming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "S<PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High quality video can provide viewers with better visual and immersive experience in video streaming systems. However, traditional such systems with adaptive bitrate algorithms can only provide low-resolution videos when the network conditions deteriorate. To break the strong dependency of video transmission on network, we design and demonstrate iSAW, a super-resolution-assisted intelligent adaptive WebRTC video streaming system, by using two mobile devices and a policy server. iSAW performs a lightweight and scalable super-resolution (SR) model on the mobile devices to enhance the quality of received videos, and leverages an learning-based adaptive algorithm on the policy server to jointly adjust the transmitted video resolution and SR reconstructed video resolution according to dynamic network conditions and client computing power. The enhanced real-time video is smooth and the performance is improved obviously.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614072"}, {"primary_key": "1231581", "vector": [], "sparse_vector": [], "title": "5G MEC Architecture for Vulnerable Road Users Management Through Smart City Data Fusion.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Enhancing the safety of Vulnerable Road Users (VRUs) poses a significant research challenge in the context of connected mobility and a plethora of technological opportunities trying to balance efficiency and widespread applicability. This paper presents a demo focused on applying 5G Multi-Access Edge Computing (MEC) to address this challenge through the combination of commercial mobile devices, public cellular networks, and data fusion between vehicle positioning and city camera infrastructure. The demo showcases the designed system and its experimental evaluation in the Modena Automotive Smart Area (MASA) through the 5G MEC infrastructure of Telecom Italia (TIM) with the aim to build a secure and efficient connected mobility environment.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614070"}, {"primary_key": "1231582", "vector": [], "sparse_vector": [], "title": "Towards Learning an Optimal Metric for Fingerprint-based Localisation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fingerprinting is a common localisation approach that often estimates a device's position by comparing an observed vector to a set of prior vectors labelled with a ground truth location, typically using methods like k-NN. In Wi-Fi fingerprinting, these vectors represent visible access points and their signal strength. Thus, the choice of metric to compare the fingerprints is crucial. In this work, we discuss our main findings regarding the extent to which metrics in the fingerprint vector space preserve relationships among locations in the 2D/3D geometric/real world. In summary, traditional metrics are not optimal on their own, and while combining them into a learned meta-metric offers slight improvements, deep metric learning, i.e., learning similarities in an end-to-end fashion with deep neural networks, appears much more effective. However, this approach has its challenges given that in the literature the problem has only been formulated for binary similarities rather than continuous ones.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615739"}, {"primary_key": "1231583", "vector": [], "sparse_vector": [], "title": "QoS-based RRM Procedure for O-RAN Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we concentrate on modeling and simulation aspects of the Open Radio Access Networks (O-RAN). In detail, we present the solution in the form of an xApp considering the Quality-of-Service (QoS) Based Resource Optimization Use Case (QBRO-UC) within the O-RAN system. The xApp has been developed for the μONOS RAN Intelligent Controller (RIC) contained in the SD-RAN software platform designed by the Open Networking Foundation (ONF). The paper focuses on the mechanisms of Radio Resource Management (RRM) controlled by O-RAN-compliant policies. The paper highlights the capabilities of the proposed Physical Resource Block (PRB) allocation and discusses the limitations of the whole modeling framework.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615737"}, {"primary_key": "1231584", "vector": [], "sparse_vector": [], "title": "Going Beyond Backscatter: Rethinking Low-Power Wireless Transmitters using Tunnel Diodes.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wenqing Yan", "<PERSON><PERSON><PERSON>"], "summary": "A stark disparity exists in the energy consumption for performing transmissions and the tasks of sensing and processing in wireless embedded systems. We present our early work to design a novel transmitter that enables transmissions at a similar energy consumption as other tasks in wireless embedded systems. In particular, the proposed transmitter does not require a carrier emitting device, which is essential to support backscatter transmitters, but has also limited their widespread deployment. The proposed transmitter exploits the capability of tunnel diode oscillators to function as a low-power, self-oscillating mixer. This property enables us to mix a weak baseband signal with a locally generated carrier signal at a peak power consumption below 100 microwatts. Nevertheless, the tunnel diode oscillator trades off the stability for low energy consumption leading to poor link reliability. In this study, we investigate error correction codes to increase link reliability. Our experiments demonstrate the potential of the transmitter to support short-range transmissions with a low energy consumption and enhanced link reliability.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615744"}, {"primary_key": "1231585", "vector": [], "sparse_vector": [], "title": "BEAVIS: Balloon Enabled Aerial Vehicle for IoT and Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "UAVs are becoming versatile and valuable platforms for various applications. However, the main limitation is their flying time. We present BEAVIS, a novel aerial robotic platform striking an unparalleled trade-off between the manoeuvrability of drones and the long lasting capacity of blimps. BEAVIS scores highly in applications where drones enjoy unconstrained mobility yet suffer from limited lifetime. A nonlinear flight controller exploiting novel, unexplored, aerodynamic phenomena to regulate the ambient pressure and enable all translational and yaw degrees of freedom is proposed without direct actuation in the vertical direction. BEAVIS has built-in rotor fault detection and tolerance. We explain the design and the necessary background in detail. We verify the dynamics of BEAVIS and demonstrate its distinct advantages, such as agility, over existing platforms including the degrees of freedom akin to a drone with 11.36x increased lifetime. We exemplify the potential of BEAVIS to become an invaluable platform for many applications.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592498"}, {"primary_key": "1231586", "vector": [], "sparse_vector": [], "title": "MobiSpectral: Hyperspectral Imaging on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hyperspectral imaging systems capture information in multiple wavelength bands across the electromagnetic spectrum. These bands provide substantial details based on the optical properties of the materials present in the captured scene. The high cost of hyperspectral cameras and their strict illumination requirements make the technology out of reach for end-user and small-scale commercial applications. We propose MobiSpectral, which turns a low-cost phone into a simple hyperspectral imaging system, without any changes in the hardware. We design deep learning models that take regular RGB images and near-infrared (NIR) signals (which are used for face identification on recent phones) and reconstruct multiple hyperspectral bands in the visible and NIR ranges of the spectrum. Our experimental results show that MobiSpectral produces accurate bands that are comparable to ones captured by actual hyperspectral cameras. The availability of hyperspectral bands that reveal hidden information enables the development of novel mobile applications that are not currently possible. To demonstrate the potential of MobiSpectral, we use it to identify organic solid foods, which is a challenging food fraud problem that is currently partially addressed by laborious, unscalable, and expensive processes. We collect large datasets in real environments under diverse illumination conditions to evaluate MobiSpectral. Our results show that MobiSpectral can identify organic foods, e.g., apples, tomatoes, kiwis, strawberries, and blueberries, with an accuracy of up to 94% from images taken by phones.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613296"}, {"primary_key": "1231587", "vector": [], "sparse_vector": [], "title": "Scattering from Rough Surfaces in 100+ GHz Wireless Mobile Networks: From Theory to Experiments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The use of frequencies above 100 GHz has received increasing attention due to the large amount of available bandwidth. Given the high chance of signal blockage, reflected paths play a key role in 100+ GHz networks. Interestingly, at these frequencies, the signal wavelength becomes comparable to the height perturbation in common natural surfaces. Hence, the reflection pattern deviates from mirror-like smooth reflection and exhibits rough scattering patterns that consist of non-specular components. This paper presents an in-depth analysis and experimental demonstration of \"rough\" surface scattering and its implications for wireless networking, namely in coverage, mobility resilience, and channel reciprocity. Furthermore, we present a novel framework for estimating the surface roughness level from the unique spectral and spatial signatures seen in the reflection spectra. We perform extensive modeling, simulation, and over-the-air experiments using a broadband sub-THz wireless system employing typical indoor/outdoor surfaces such as tile, brick, redstone, and granite.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613306"}, {"primary_key": "1231588", "vector": [], "sparse_vector": [], "title": "BatMobility: Towards Flying Without Seeing for Autonomous Drones.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unmanned aerial vehicles (UAVs) rely on optical sensors such as cameras and lidar for autonomous operation. However, such optical sensors are error-prone in bad lighting, inclement weather conditions including fog and smoke, and around textureless or transparent surfaces. In this paper, we ask: is it possible to fly UAVs without relying on optical sensors, i.e., can UAVs fly without seeing? We present BatMobility, a lightweight mmWave radar-only perception system for UAVs that eliminates the need for optical sensors. BatMobility enables two core functionalities for UAVs -- radio flow estimation (a novel FMCW radar-based alternative for optical flow based on surface-parallel doppler shift) and radar-based collision avoidance. We build BatMobility using commodity sensors and deploy it as a real-time system on a small off-the-shelf quadcopter running an unmodified flight controller. Our evaluation shows that BatMobility achieves comparable or better performance than commercial-grade optical sensors across a wide range of scenarios.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592532"}, {"primary_key": "1231589", "vector": [], "sparse_vector": [], "title": "When BLE Meets Light: Multi-modal Fusion for Enhanced Indoor Localization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Designing a reliable and highly accurate indoor localization system is challenging due to the non-uniformity of indoor spaces, multipath fading, and satellite signal blockage. To address these issues, we propose a Deep Neural Network-based localization system that combines passive Visible Light Positioning (p-VLP) and Bluetooth Low Energy (BLE) technologies to achieve stable, energy-efficient, and accurate indoor localization. Our solution leverages incremental learning to fuse data from visible light and BLE, overcoming their individual limitations and achieving centimeter-level localization accuracy. We build a prototype using low-cost S9706 hue sensors for p-VLP and low-power nrf52830 BLE boards to collect data simultaneously from both technologies in a 25m2 testbed. Our approach demonstrates a significant localization accuracy improvement of approximately 47% and 64% compared to individual p-VLP and BLE technologies, respectively, achieving a mean localization error of 20 cm.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615746"}, {"primary_key": "1231591", "vector": [], "sparse_vector": [], "title": "Crescendo: Towards Wideband, Real-time, High-Fidelity Spectrum Sensing Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Spectrum sensing systems provide real-time feedback essential for spectrum sharing. However, the growth of spectrum sharing is limited by the capabilities of these spectrum sensors. Sharing a new frequency band is only possible if sensors can detect activity in that band with sufficient time granularity and signal fidelity to meet spectrum sharing policy requirements. In this work, we introduce Crescendo, a system design that shows we can achieve wideband, real-time, high-fidelity spectrum sensing using sweeping spectrum sensors. We first provide an analysis that demonstrates there are operating points of sweeping sensors that can sense multiple popular protocols. Then we demonstrate these sensors can be built in practice with an adaptive gain super-heterodyne RF frontend with high-fidelity LO generation, and evaluate a prototype built with COTS components. In our benchmarks, Crescendo outperforms prior wideband spectrum sensors, achieving a 30 dB increase in dynamic range and 10 dB increase in SNR.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613294"}, {"primary_key": "1231592", "vector": [], "sparse_vector": [], "title": "Real-time Generation of 3-Dimensional Representations of Static Objects using Small Unmanned Aerial Vehicles.", "authors": ["<PERSON><PERSON>", "Bernat Ollé", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in robotics and nanotechnology resulted in a set of miniaturized Unmanned Aerial Vehicles (UAVs). Such small UAVs are envisioned to operate in hard-to-reach areas for enabling applications such as structural monitoring or content capturing. Towards showcasing this vision, we demonstrate a small UAV-supported setup for real-time autonomous generation of 3-Dimensional (3D) representations of static objects. In the setup, a small UAV (i.e., CrazyFlie 2.1) is envisioned to visit a set of locations, acting as a carrier and power source of a camera sensor. At each location, the sensor is expected to take a picture of the object and report it to the station. The station implements a pipeline for 3D reconstruction based on the pictures taken by the UAV.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614078"}, {"primary_key": "1231593", "vector": [], "sparse_vector": [], "title": "LUT-NN: Empower Efficient Neural Network Inference with Centroid Learning and Table Lookup.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "On-device Deep Neural Network (DNN) inference consumes significant computing resources and development efforts. To alleviate that, we propose LUT-NN, the first system to empower inference by table lookup, to reduce inference cost. LUT-NN learns the typical features for each operator, named centroid, and precompute the results for these centroids to save in lookup tables. During inference, the results of the closest centroids with the inputs can be read directly from the table, as the approximated outputs without computations.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613285"}, {"primary_key": "1231594", "vector": [], "sparse_vector": [], "title": "Transmitting, Fast and Slow: Scheduling Satellite Traffic through Space and Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Earth observation Low Earth Orbit (LEO) satellites collect enormous amounts of data that needs to be transferred first to ground stations and then to the cloud, for storage and processing. Satellites today transmit data greedily to ground stations, with full utilization of bandwidth during each contact period. We show that due to the layout of ground stations and orbital characteristics, this approach overloads some ground stations and underloads others, leading to lost throughput and large end-to-end latency for images. We present a new end-to-end scheduler system called Umbra, which plans transfers from large satellite constellations through ground stations to the cloud, by accounting for both spatial and temporal factors, i.e., orbital dynamics, bandwidth constraints, and queue sizes. At the heart of Umbra is a new class of scheduling algorithms called withhold scheduling, wherein the sender (i.e., satellite) selectively under-utilizes some links to ground stations. We show that Umbra's counter-intuitive approach increases throughput by 13--31% & reduces P90 latency by 3--6 ×.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592521"}, {"primary_key": "1231595", "vector": [], "sparse_vector": [], "title": "Magnetic Backscatter for In-body Communication and Localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Implantable and edible medical devices promise to provide continuous, directed, and comfortable healthcare treatments. Communicating with such devices and localizing them is a fundamental, but challenging, mobile networking problem. Recent work has focused on leveraging near field magnetism-based systems to avoid the challenges of attenuation, refraction, and reflection experienced by radio waves. However, these systems suffer from limited range, and require fingerprinting-based localization techniques. We present InnerCompass, a magnetic backscatter system for in-body communication and localization. InnerCompass relies on new magnetism-native design insights that enhance the range of these devices. We design the first analytical model for magnetic-field-based localization, that generalizes across different scenarios. We've implemented InnerCompass and evaluated it in porcine tissue. Our results show that Inner-Compass can communicate at 5 Kbps at a distance of 25 cm, and localize with an accuracy of 5 mm.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613301"}, {"primary_key": "1231596", "vector": [], "sparse_vector": [], "title": "ARCH: Automatic Roaming-contract Handler for Seamless 6G System to Provide Trusted and Highly Available Network Access.", "authors": ["Kíng-Pîng <PERSON>", "Po-Han <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tsung<PERSON><PERSON>"], "summary": "It is expected to be pervasive sensors to integrate physical and digital world. Low-latency and wide-covering networks are hence urged, which lead to costly infrastructure. We are proposing ARCH based on smart contract and Self-sovereign Identity (SSI), to provide telecoms a trusted and transparent multilateral platform sharing their infrastructure to decrease the deployment and agency cost, and carbon emission.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615728"}, {"primary_key": "1231597", "vector": [], "sparse_vector": [], "title": "Experimental Investigation of Angle Diversity Receiver for Vehicular VLC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we explore the use of multiple photodetectors for vehicular visible light communication (VLC) systems with a focus on the so-called Angle-Diversity Receiver (ADR). ADR builds upon the principle of using multiple photodetectors oriented at different reception angles to enable multidirectional signal reception. With ADR, it is possible to receive light rays from the vehicle headlight in challenging mobility conditions such as in the cases of U-turn, left-turn, and right-turn. In our work, we present preliminary results of an experimental verification of ADR-based vehicular VLC system implemented using software-defined radio platforms. Our results demonstrate that a packet delivery ratio (PDR) of more than 99 % is achieved even for T-junction road scenarios where the link is likely to get lost in the case of conventional single transmitter/receiver configurations.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615755"}, {"primary_key": "1231598", "vector": [], "sparse_vector": [], "title": "Vehicular Visible Light Communications with A Solar Panel Receiver.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Roozbeh <PERSON>akdar", "<PERSON><PERSON>"], "summary": "The wide availability of light-emitting diode (LED)-based light sources makes possible the use of visible light communication (VLC) for both indoor and vehicular wireless connectivity. Earlier works on VLC have predominantly used photodetectors as receivers. It is also possible to utilize solar panels as receivers. In this paper, we present an experimental performance evaluation of a vehicular VLC system with a truck headlight as the transmitter and a solar panel as the receiver. First, we characterize the frequency response of two different solar panels and measure their bandwidth. Then, we present the performance of the vehicular VLC system in terms of data rate, signal-to-noise ratio (SNR), and eye diagrams.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615735"}, {"primary_key": "1231599", "vector": [], "sparse_vector": [], "title": "Radio Frequency Neural Networks for Wireless Sensing.", "authors": ["Jingyu Tong", "Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON>", "Sicong <PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensing has attracted considerable attention because it can sense the state of the targets by analyzing the surrounding wireless signals, which has become the key role of the artificial intelligence of things (AIoT). As the number of sensory nodes increases, large amounts of redundant data are exchanged between sensory terminals and the AI cloud. To process such large amounts of data efficiently and decrease power consumption, a machine-learning approach that operates close to or inside sensors must be developed. To this end, we present the radio-frequency neural network (RFNN), a physical neural network taking advantage of a group of transmissive intelligent surfaces (i.e., metasurfaces) to mimic the computations of a fully-connected neural network. The design is spurred by the capability of RFNNs to perform expensive multiplication and additions at the speed of light, with ultra-low power consumption. We prototype RFNN at 5 GHz for WiFi sensing regarding nine wireless sensing tasks. Extensive evaluations demonstrate the comparably equivalent inference ability as the conventional electronic neural networks while consuming less energy.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614063"}, {"primary_key": "1231600", "vector": [], "sparse_vector": [], "title": "Designing, Building, and Characterizing Large-Scale LoRa Networks for Smart City Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa, as a representative Low-Power Wide-Area Network (LPWAN) technology, holds tremendous potential for various Internet of Things (IoT) applications. However, as there are few real large-scale deployments, it is unclear whether and how well LoRa can eventually meet its prospects. In this paper, we demystify the real performance of LoRa by deploying LoRa systems in both campus-scale testbeds and citywide applications. Our LoRa network consisting of 100 gateways and 19,821 LoRa end nodes, covering an area of 130 km2 for 12 applications. Our measurement focuses on following perspectives: (i) Coverage performance of the LoRa network; (ii) Gateway efficiency and deployment optimization; (iii) Validation of two LoRa optimization mechanisms. The results reveal that LoRa performance in urban settings is bottlenecked by the prevalent blind spots, and there is a gap between the gateway efficiency and network coverage for gateway deployment. Our measurement provides insights for large-scale LoRa network deployment and also for future academic research to fully unleash the potential of LoRa.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615731"}, {"primary_key": "1231602", "vector": [], "sparse_vector": [], "title": "NeuriCam: Key-Frame Video Super-Resolution and Colorization for IoT Cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present NeuriCam, a novel deep learning-based system to achieve video capture from low-power dual-mode IoT camera systems. Our idea is to design a dual-mode camera system where the first mode is low power (1.1 mW) but only outputs grey-scale, low resolution and noisy video and the second mode consumes much higher power (100 mW) but outputs color and higher resolution images. To reduce total energy consumption, we heavily duty cycle the high power mode to output an image only once every second. The data for this camera system is then wirelessly sent to a nearby plugged-in gateway, where we run our real-time neural network decoder to reconstruct a higher-resolution color video. To achieve this, we introduce an attention feature filter mechanism that assigns different weights to different features, based on the correlation between the feature map and the contents of the input frame at each spatial location. We design a wireless hardware prototype using off-the-shelf cameras and address practical issues including packet loss and perspective mismatch. Our evaluations show that our dual-camera approach reduces energy consumption by 7x compared to existing systems. Further, our model achieves an average greyscale PSNR gain of 3.7 dB over prior single and dual-camera video super-resolution methods and 5.6 dB RGB gain over prior color propagation methods.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592523"}, {"primary_key": "1231604", "vector": [], "sparse_vector": [], "title": "Software-Defined Wireless Communication Systems for Heterogeneous Architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Future cellular networks will be programmable and increasingly software-defined with APIs to hook into the communication stack closer and closer to the physical layer. This allows operators, for example, to plug-in third-party machine learning algorithms to optimize performance. At the same time, this flexibility implies that compute resources cannot be provisioned statically but have to be distributed dynamically during runtime. While the hardware platforms for such systems are available, we lack suitable software frameworks that help to realize such systems. In this demonstration, we present two Open Source projects that fill this gap: FutureSDR, a portable real-time signal processing framework with native support for accelerators (like GPUs and FGPAs); and IPEC, which enables fully automatic composition of multi-accelerator FPGA designs. We believe that these tools - especially in combination with each other - can provide the base for building research prototypes and allow experimentation with software-defined wireless communication systems.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614084"}, {"primary_key": "1231605", "vector": [], "sparse_vector": [], "title": "ETAG: An Energy-Neutral Ear Tag for Real-Time Body Temperature Monitoring of Dairy Cattle.", "authors": ["<PERSON><PERSON>", "Hanwook Chung", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Heat stress, caused by a warming climate and the increasingly high milk-producing dairy cattle, is one of the major threats to the well-being of dairy cattle as well as the economic, environmental, and social sustainability of dairy farming around the world. Timely identification of cows under heat stress is crucial to improving animal welfare, preventing milk production losses, and preserving water and energy for cooling.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613262"}, {"primary_key": "1231606", "vector": [], "sparse_vector": [], "title": "MigraMEC: Hybrid Testbed for MEC App Migration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-access Edge Computing (MEC) enhances the capabilities of 5G by enabling the computation closer to the end-user for real-time and context-aware services. One of the main challenges of MEC is the migration of the MEC application in the presence of user mobility. MigraMEC is a hybrid testbed that simulates the network scenario and user mobility and emulates the MEC framework by using AdvantEDGE. Moreover, MigraMEC implements two physical MEC Hosts (MEHs) by using an extended version of Kubernetes (K8s). Finally, the MigraMEC controller interacts with both the emulative and experimental environments to ensure an efficient migration of the MEC application. Based on network information, the MigraMEC controller not only enforces the MEH where the MEC application is running but also the Point of Access (PoA) to which the user is connected. In our demonstration, the MEC application is a video streaming service, and the results highlight the need for multiple MEHs and efficient migration to maintain a high user experience.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614069"}, {"primary_key": "1231608", "vector": [], "sparse_vector": [], "title": "Meta-Speaker: Acoustic Source Projection by Exploiting Air Nonlinearity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes Meta-Speaker, an innovative speaker capable of projecting audible sources into the air with a high level of manipulability. Unlike traditional speakers that emit sound waves in all directions, Meta-Speaker can manipulate the granularity of the audible region, down to a single point, and can manipulate the location of the source. Additionally, the source projected by Meta-Speaker is a physical presence in space, allowing both humans and machines to perceive it with spatial awareness. Meta-Speaker achieves this by leveraging the fact that air is a nonlinear medium, which enables the reproduction of audible sources from ultrasounds. Meta-Speaker comprises two distributed ultrasonic arrays, each transmitting a narrow ultrasonic beam. The audible source can be reproduced at the intersection of the beams. We present a comprehensive profiling of Meta-Speaker to validate the high manipulability it offers. We prototype Meta-Speaker and demonstrate its potential through three applications: anchor-free localization with a median error of 0.13 m, location-aware communication with a throughput of 1.28 Kbps, and acoustic augmented reality where users can perceive source direction with a mean error of 9.8 degrees.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613279"}, {"primary_key": "1231609", "vector": [], "sparse_vector": [], "title": "NWDAMaaS: A Containerized Real-Time Data Analytic Framework for 5G Self-Organizing Networks.", "authors": ["<PERSON><PERSON>", "Xinz<PERSON> Cheng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the 5G commercial deployments rapidly proceeding, operating mobile networks efficiently has become a great challenge. Self-organizing networks have been proposed to focus on automatically monitoring, analyzing and optimizing networks. Regarding the growing scale and complexity, 5G self-organizing networks confront the challenge to handle the massive data. Therefore, AI models are urgently needed to enable end-to-end network automation. In this demonstration, benefiting from the open data interfaces of the standardized NWDAF within the 5GC network, we implement a containerized network data analytic framework embedding Docker-based AI model containers into 5G networks. Furthermore, we simulate a use case automatically monitoring and optimizing user-level QoE in real time and simulation results are presented.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614076"}, {"primary_key": "1231610", "vector": [], "sparse_vector": [], "title": "RF-SIFTER: Sifting Signals at Layer-0.5 to Mitigate Wideband Cross-Technology Interference for IoT.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "IoT uplink performance is crucial for a wide variety of IoT applications such as health sensing and industrial control, which demand reliable delivery of sensor data to the cloud. However, due to the limited transmission power budget imposed on many power-constrained IoT devices, IoT uplinks are highly susceptible to cross-technology interference (CTI) caused by coexisting networks. Previous approaches to mitigating CTI have relied on MAC/PHY designs. They suffer from poor performance and limited generality in the presence of wideband CTI sources such as Wi-Fi and RF jammer, which transmit aggressively on large spectrum chunks using diverse radio technologies.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592513"}, {"primary_key": "1231611", "vector": [], "sparse_vector": [], "title": "Wireless Actuation for Soft Electronics-free Robots.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a new primitive that allows soft robots to be physically controlled in a completely non-line-of-sight context using wireless energy - a process we call wireless actuation. Soft robots, which are composed entirely of soft materials and exclude any rigid components, are highly flexible platforms that can change their shape. This paper considers a specific class of soft robots composed of liquid-crystal elastomers (LCE) that are entirely electronics-free and engineered to change shape when heated to 60 °C. Traditionally, such robotic systems must be in line-of-sight of a light source, such as infrared to be moved, or require an external power supply for Joule heating and often take several tens of seconds to heat. We present WASER, a novel RF-based heating platform that allows electronics-free robots to be actuated rapidly (within a few seconds) and potentially in non-line-of-sight. WASER achieves this through innovations in both wireless systems and material science. On the wireless front, WASER develops a new blind beamforming solution that directs high-power wireless energy at fine spatial granularity without electronics on the robot to provide feedback. On the material science front, WASER exhibits heat-responsive shape-morphing and energy-harvesting material functionalities that allow for rapid wireless heating. We implement and evaluate WASER and demonstrate diverse shape-morphing capabilities.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592494"}, {"primary_key": "1231612", "vector": [], "sparse_vector": [], "title": "Experience: A Three-Year Retrospective of Large-scale Multipath Transport Deployment for Mobile Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multipath transport allows the simultaneous use of diverse paths on mobile devices to maximize mobile resource usage. Over the years, we have witnessed several mobile multipath deployment examples by network operators and mobile app providers. However, existing deployment methods require modifications to either the network infrastructure or both endpoints. To lower the bar of the deployment, we present Fleety, a mobile system service that provides the multi-path transport capability with client-only modification. To the best of our knowledge, we are the first to carry out a large-scale mobile multipath deployment that can support hundreds of mobile applications in the cross-ISP setting. This paper is a retrospective of our experience in building and deploying multipath transport for mobile applications. We reveal several practical deployment challenges and share our experience in dealing with them.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592506"}, {"primary_key": "1231613", "vector": [], "sparse_vector": [], "title": "Otter: Simplifying Embedded Sensor Data Collection and Analysis using Large Language Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless embedded systems assist us in collecting data from the physical world, through sensor data analysis, such systems allow us to understand our environment. However, deploying wireless embedded systems and analyzing the collected data remains significantly challenging. This is due to the steep learning curve required to implement custom machine-learning models and other algorithms for data analysis. Furthermore, it is also challenging to program individual embedded devices. The diversity of the available platforms and their capabilities further compounds this problem. In response, we introduce an end-to-end system, called the Otter. It facilitates simple sensor data collection using commodity-embedded platforms. Moreover, it employs a large language model to design a natural language interface for the analysis and extraction of useful information from the sensor data. We present our preliminary work on prototyping this system, applying it to a specific use case of hand gesture detection. Otter represents one of the first systems to leverage the enhanced capabilities of large language models for simplifying wireless embedded system deployments.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615759"}, {"primary_key": "1231614", "vector": [], "sparse_vector": [], "title": "AdaptiveNet: Post-deployment Neural Architecture Adaptation for Diverse Edge Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiaozhou Ye", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning models are increasingly deployed to edge devices for real-time applications. To ensure stable service quality across diverse edge environments, it is highly desirable to generate tailored model architectures for different conditions. However, conventional pre-deployment model generation approaches are not satisfactory due to the difficulty of handling the diversity of edge environments and the demand for edge information. In this paper, we propose to adapt the model architecture after deployment in the target environment, where the model quality can be precisely measured and private edge data can be retained. To achieve efficient and effective edge model generation, we introduce a pretraining-assisted on-cloud model elastification method and an edge-friendly on-device architecture search method. Model elastification generates a high-quality search space of model architectures with the guidance of a developer-specified oracle model. Each subnet in the space is a valid model with different environment affinity, and each device efficiently finds and maintains the most suitable subnet based on a series of edge-tailored optimizations. Extensive experiments on various edge devices demonstrate that our approach is able to achieve significantly better accuracy-latency tradeoffs (e.g. 46.74% higher on average accuracy with a 60% latency budget) than strong baselines with minimal overhead (13 GPU hours in the cloud and 2 minutes on the edge server).", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592529"}, {"primary_key": "1231615", "vector": [], "sparse_vector": [], "title": "XCopy: Boosting Weak Links for Reliable LoRa Communication.", "authors": ["Xianjin Xia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuan<PERSON> Zheng", "<PERSON>"], "summary": "LoRaWAN suffers dramatic performance degradation over a long communication range due to signal attenuation and blockages. To ensure reliable data transfer, LoRaWAN adopts retransmission mechanism where an unacknowledged packet is retransmitted multiple times in the hope of successfully delivering the packet at least once over harsh wireless channels. This retransmission mechanism is ill-suited for LoRa: 1) unsuccessful retransmissions lead to high power consumption for battery-powered LoRa nodes, and 2) a retransmission at another time does not necessarily improve the signal strength over harsh wireless channels.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592516"}, {"primary_key": "1231616", "vector": [], "sparse_vector": [], "title": "Anemoi: A Low-cost Sensorless Indoor Drone System for Automatic Mapping of 3D Airflow Fields.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Kai<PERSON> Hou", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mapping 3D airflow fields is important for many HVAC, industrial, medical, and home applications. However, current approaches are expensive and time-consuming. We present Anemoi, a sub-$100 drone-based system for autonomously mapping 3D airflow fields in indoor environments. Anemoi leverages the effects of airflow on motor control signals to estimate the magnitude and direction of wind at any given point in space. We introduce an exploration algorithm for selecting optimal waypoints that minimize overall airflow estimation uncertainty. We demonstrate through microbenchmarks and real deployments that Anemoi is able to estimate wind speed and direction with errors up to 0.41 m/s and 25.1° lower than the existing state of the art and map 3D airflow fields with an average RMS error of 0.73 m/s.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613292"}, {"primary_key": "1231617", "vector": [], "sparse_vector": [], "title": "A Transformer-based Trajectory Prediction Method.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The periodic trajectories of people provide an insightful and concise explanation over a long moving history, which helps to predict their future movements. In this paper, we present a Transformer based trajectory prediction method to predict the trajectories of people. First, we combine the spatial contexts of a trajectory with the sequential, temporal, and social contexts of a trajectory. Then, we predict the trajectories of people by applying Transformer based model where we multiply the length of query vector and the length of key vector with the scaling factor of the Transformer in order to make the training process more stable. As a result, our proposed Transformer based trajectory prediction model was effective.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615733"}, {"primary_key": "1231618", "vector": [], "sparse_vector": [], "title": "Enabling Resilience in Virtualized RANs with Atlas.", "authors": ["Jiarong Xing", "Junzhi Gong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtualized radio access networks (vRANs), which allow running RAN processing on commodity servers instead of proprietary hardware, are gaining adoption in cellular networks. Two properties of the vRAN's \"Distributed Unit (DU)\" that implements the lower RAN layers---its real-time deadlines and its black-box nature---make it challenging to provide resilience features such as upgrades and failover without long service disruptions. These properties preclude the use of existing resilience techniques like virtual machine migration or state replication that are used for typical workloads. This paper presents Atlas, the first system that provides resilience for the DU. The central insight in Atlas is to repurpose existing cellular mechanisms for wireless resilience, namely handovers and cell reselection, to provide software resilience for the DU. For planned resilience events like upgrades, we design a novel technique that simultaneously serves cells from both the old and new DUs via the same radio, and uses handovers between these cells to migrate user devices. For unplanned failures, we identify deficiencies in existing RAN protocols that disrupt cell reselection after DU failure, and show how we can eliminate these disruptions using a middlebox between the DU and higher layers. Our evaluation with a state-of-the-art 5G vRAN testbed shows that Atlas achieves minimal disruption to cellular connectivity during resilience events, while incurring low overhead.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613276"}, {"primary_key": "1231620", "vector": [], "sparse_vector": [], "title": "From 5G to 6G: It is Time to Sniff the Communications between a Base Station and Core Networks.", "authors": ["Ruoting <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> Ren", "<PERSON>", "<PERSON>"], "summary": "Thanks to mobility and large coverage, 6G mobile networks introduce satellites and unmanned aerial vehicles as aerial base stations (ABS) in the 6G era. Instead of using a wired backhaul in 5G and its predecessor, an ABS leverages a wireless channel to a core network (CN). However, such a wireless channel design introduces new security challenges. In this paper, we present that passive attackers could sniff the ABS-CN wireless channel and identify what users are doing based on deep learning methods. We collect GTP protocol data on our testbed and use convolutional neural networks to classify 5 types of encrypted App traffic, like IG and TikTok. Experiment results proved the effectiveness of the proposed method, revealing the confidential data leakage problem on the 6G wireless ABS-CN channel.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3614085"}, {"primary_key": "1231621", "vector": [], "sparse_vector": [], "title": "Taming Event Cameras with Bio-Inspired Architecture and Algorithm: A Case for Drone Obstacle Avoidance.", "authors": ["<PERSON><PERSON>", "Danyang Li", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fast and accurate obstacle avoidance is crucial to drone safety. Yet existing on-board sensor modules such as frame cameras and radars are ill-suited for doing so due to their low temporal resolution or limited field of view. This paper presents BioDrone, a new design paradigm for drone obstacle avoidance using stereo event cameras. At the heart of BioDrone is two simple yet effective system design inspired by the mammalian visual system, namely, a chiasm-inspired signal processing pipeline for fast event filtering and obstacle detection, and a lateral geniculate nucleus (LGN)-inspired event matching algorithm for accurate obstacle localization. To make BioDrone a practical solution, we further take significant engineering efforts to deploy the software stack on FPGA through software and hardware co-design. The performance comparison with two state-of-the-art event-based obstacle avoidance systems shows BioDrone achieves a consistently high obstacle detection rate of 96.1%. The average localization error of BioDrone is 6.8cm with a 4.7ms latency, outperforming both baselines by over 40%.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613269"}, {"primary_key": "1231622", "vector": [], "sparse_vector": [], "title": "Practically Adopting Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing inertial measurement unit (IMU) based human activity recognition (HAR) approaches still face a major challenge when adopted across users in practice. The severe heterogeneity in IMU data significantly undermines model generalizability in wild adoption. This paper presents UniHAR, a universal HAR framework for mobile devices. To address the challenge of data heterogeneity, we thoroughly study augmenting data with the physics of the IMU sensing process and present a novel adoption of data augmentations for exploiting both unlabeled and labeled data. We consider two application scenarios of UniHAR, which can further integrate federated learning and adversarial training for improved generalization. UniHAR is fully prototyped on the mobile platform and introduces low overhead to mobile devices. Extensive experiments demonstrate its superior performance in adapting HAR models across four open datasets.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613299"}, {"primary_key": "1231623", "vector": [], "sparse_vector": [], "title": "Towards Generalized mmWave-based Human Pose Estimation through Signal Augmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "The unprecedented advance of wireless human sensing is enabled by the proliferation of the deep learning techniques, which, however, rely heavily on the completeness and representativeness of the data patterns contained in the training set. Thus, deep learning based wireless human perception models usually fail when the human subject is conducting activities that are unseen during the model training. To address this problem, we propose a novel wireless signal augmentation framework, named mmGPE, for Generalized mmWave-based Pose Estimation. In mmGPE, we adopt a physical simulator to generate mmWave FMCW signals. However, due to the imperfect simulation of the physical world, there is a big gap between the signals generated by the physical simulator and the real-world signals collected by the mmWave radar. To tackle this challenge, we propose to integrate the physical signal simulation with deep learning techniques. Specifically, we develop a deep learning-based signal refiner in mmGPE that is capable of bridging the gap and generating realistic signal data. Through extensive evaluations on a COTS mmWave testbed, our mmGPE system demonstrates high accuracy in generating human meshes for unseen activities.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613302"}, {"primary_key": "1231624", "vector": [], "sparse_vector": [], "title": "Screen Perturbation: Adversarial Attack and Defense on Under-Screen Camera.", "authors": ["Hanting Ye", "<PERSON><PERSON>", "<PERSON><PERSON> Jia", "<PERSON>"], "summary": "Smartphones are moving towards the fullscreen design for better user experience. This trend forces front cameras to be placed under screen, leading to Under-Screen Cameras (USC). Accordingly, a small area of the screen is made translucent to allow light to reach the USC. In this paper, we utilize the translucent screen's features to inconspicuously modify its pixels, imperceptible to human eyes but inducing perturbations on USC images. These screen perturbations affect deep learning models in image classification and face recognition. They can be employed to protect user privacy, or disrupt the front camera's functionality in the malicious case. We design two methods, one-pixel perturbation and multiple-pixel perturbation, that can add screen perturbations to images captured by USC and successfully fool various deep learning models. Our evaluations, with three commercial full-screen smartphones on testbed datasets and synthesized datasets, show that screen perturbations significantly decrease the average image classification accuracy, dropping from 85% to only 14% for one-pixel perturbation and 5.5% for multiple-pixel perturbation. For face recognition, the average accuracy drops from 91% to merely 1.8% and 0.25%, respectively.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613278"}, {"primary_key": "1231625", "vector": [], "sparse_vector": [], "title": "Enabling Concurrency for Non-orthogonal LoRa Channels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Existing LoRa only supports the concurrency of orthogonal channels but ignores the large number of non-orthogonal channel concurrency opportunities. In this paper, we propose Mc-LoRa that enables LoRa concurrency for non-orthogonal overlapping channels by solving cross-channel collision that happens when chirps with different bandwidths have the same slope in time-frequency domain. Existing single-channel concurrency methods fail to resolve this new collision because the deterministic symbol offset is invalid anymore due to the asymmetric symbol duration. But we find that when wiping a part of collided signals, the amplitude change of target chirp that aligns with the decoding window is predictable, while the collided chirps experience different changes. We accordingly regard the amplitude change ratio before and after wiping as a new decoding feature. We propose a wiper selection method based on our theoretical model to obtain robust features. We also design noise-aware wiper searching and grouping mechanisms to balance the feature accuracy and computing overhead. The experiments show that Mc-LoRa efficiently decodes packets in non-orthogonal overlapping channels and improves the network throughput by up to 3.4× under cross-channel collision, compared with the state-of-the-art single-channel concurrency methods.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613268"}, {"primary_key": "1231626", "vector": [], "sparse_vector": [], "title": "ReMark: Privacy-preserving Fiducial Marker System via Single-pixel Imaging.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cameras are widely adopted as the sensing device in fiducial marker systems. The captured videos however may expose sensitive information to attackers. We propose a privacy-preserving fiducial marker system, ReMark, which is based on retroreflector and single-pixel imaging (SPI), and captures only the minimal information needed for positioning and identifying markers. ReMark is built upon a state-of-the-art light-weight neural network (NN), which recovers the scene in the SPI system. Another light-weight NN is proposed for pose estimation of markers. They are trained with synthesized large datasets, and proved to adapt well to real-world data. The pose estimation NN adopts a specialized output embedding to address the symmetry-related issues, and soft-decision decoding is used to mitigate the distortion in recovered scenes. Detailed evaluation shows that, with 4.8 cm markers at 3.00 m distance and 1.0 W LED power, ReMark achieves a decode error rate of 2.1% with tilt angle ≤ 30°, searching in a dictionary of size 1,000. Evaluation of worst-case scenarios shows that an attacker could hardly acquire sensitive information even with access to raw data.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613289"}, {"primary_key": "1231627", "vector": [], "sparse_vector": [], "title": "A Deep Learning-based VNF Placement Approach for SFC Requests in MEC-NFV Enabled Networks.", "authors": ["<PERSON>", "Xiongyan Tang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Service Function Chain (SFC) has become a popular paradigm to complete mobile services due to the advancements in Mobile Edge Computing (MEC) and Network Function Virtualization (NFV). This new computing and networking paradigm allows Virtual Network Functions (VNFs) to be placed in physical devices within MEC-NFV networks cost-effectively and flexibly. However, most existing VNF placement algorithms are complex, unscalable, and time-consuming. In this paper, we investigate the VNF placement problem in MEC-NFV networks and formulate an optimization model to optimize network resource utilization. We introduce a novel Deep Learning-based VNF Placement Approach (DLVPA) that intelligently selects nodes and places VNFs for SFC requests. Performance evaluations demonstrate that DLVPA can effectively improve network resource utilization.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615742"}, {"primary_key": "1231628", "vector": [], "sparse_vector": [], "title": "Runtime WCET Estimation Using Machine Learning.", "authors": ["Sang<PERSON><PERSON> Yun", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Accurate task execution time estimation is vital for efficient and dependable operation of safety-critical systems. However, modern automotive functions' complexity challenges conventional estimation methods. To address this, we propose a novel technique that combines execution time and job sequence data using a multi-layer perceptron (MLP) neural network. Leveraging MLP's capabilities, our approach achieves impressive 99.7% prediction accuracy with a mere 38.33 μs latency. Integrating our technique into safety-critical systems optimizes resource allocation and scheduling, enhancing performance and reliability. Importantly, our method extends beyond automotive systems, finding potential in diverse safety-critical domains. By precisely estimating task execution time, we enhance operational efficiency and decision-making in complex systems.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3615740"}, {"primary_key": "1231629", "vector": [], "sparse_vector": [], "title": "mmFER: Millimetre-wave Radar based Facial Expression Recognition for Multimedia IoT Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Gu"], "summary": "Facial expression recognition plays a vital role to enable emotional awareness in multimedia Internet of Things applications. Traditional camera or wearable sensor based approaches may compromise user privacy or cause discomfort. Recent device-free approaches open a promising direction by exploring Wi-Fi or ultrasound signals reflected from facial muscle movements, but limitations exist such as poor performance in presence of body motions and not being able to detect multiple targets. To bridge the gap, we propose mmFER, a novel millimeter wave (mmWave) radar based system that extracts facial muscle movements associated with mmWave signals to recognize facial expressions. We propose a novel dual-locating approach based on MIMO that explores spatial information from raw mmWave signals for face localization in space, eliminating ambient noise. In addition, collecting mmWave training data can be very costly in practice, and insufficient training dataset may lead to low accuracy. To overcome, we design a cross-domain transfer pipeline to enable effective and safe model knowledge transformation from image to mmWave. Extensive evaluations demonstrate that mmFER achieves an accuracy of 80.57% on average within a detection range between 0.3m and 2.5m, and it is robust to various real-world settings.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592515"}, {"primary_key": "1231630", "vector": [], "sparse_vector": [], "title": "Quantum Wireless Sensing: Principle, Design and Implementation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zitong Lan", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have witnessed a tremendous amount of interest in wireless sensing, i.e., instead of employing traditional sensors, wireless signal is utilized for sensing purposes. Contact-free wireless sensing has been successfully demonstrated using various RF signals such as WiFi, RFID, LoRa, and mmWave, enabling a large range of applications. However, limited by hardware thermal noise, the granularity of RF sensing is still relatively coarse. In this paper, instead of using the macro signal power/phase for sensing, we propose the first quantum wireless sensing system, which uses the micro energy level of atoms for sensing, improving the sensing granularity by an order of magnitude. The proposed quantum wireless sensing system is capable of utilizing a wide spectrum of frequencies (e.g., 2.4 GHz, 5 GHz and 28 GHz) for sensing. We demonstrate the superior performance of quantum wireless sensing with two widely-used signals, i.e., WiFi and 28 GHz millimeter wave. We show that quantum wireless sensing can push the sensing granularity of WiFi from millimeter level to sub-millimeter level and push the sensing granularity of millimeter wave to micrometer level.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613258"}, {"primary_key": "1231631", "vector": [], "sparse_vector": [], "title": "RF-Search: Searching Unconscious Victim in Smoke Scenes with RF-enabled Drone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Toxic gases inhalation is the most common cause of death in fire scenes, which can make people unconscious and unable to save themselves. Hence, discovering the unconscious victims is crucial to improve their survival rate. In this paper, we propose RF-Search, a victim searching system with RF device mounted on the drone. The challenge mainly comes from the fact that drone motion would overwhelm the subtle vital signs utilized for victim identification. To resolve this problem, we have noted that the physical signature of drone motion has been encoded in stationary object reflections. Leveraging this unique physical signature, we propose to identify the unconscious victim through the spatio-temporal correlation between signals reflected from the victim and the surrounding stationary objects. To extract respiration information of the victim, we propose a motion segmentation module and a motion compensation module to suppress the signal variation caused by drone movement. Extensive experiments have demonstrated that our system could achieve an accuracy of 92.5% for victim identification.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613305"}, {"primary_key": "1231632", "vector": [], "sparse_vector": [], "title": "Robust Real-time Multi-vehicle Collaboration on Asynchronous Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cooperative perception significantly enhances the perception performance of connected autonomous vehicles. Instead of purely relying on local sensors with limited range, it enables multiple vehicles and roadside infrastructures to share sensor data to perceive the environment collaboratively. Through our study, we realize that the performance of cooperative perception systems is limited in real-world deployment due to (1) out-of-sync sensor data during data fusion and (2) inaccurate localization of occluded areas. To address these challenges, we develop RAO, an innovative, effective, and lightweight cooperative perception system that merges asynchronous sensor data from different vehicles through our novel designs of motion-compensated occupancy flow prediction and on-demand data sharing, improving both the accuracy and coverage of the perception system. Our extensive evaluation, including real-world and emulation-based experiments, demonstrates that RAO outperforms state-of-the-art solutions by more than 34% in perception coverage and by up to 14% in perception accuracy, especially when asynchronous sensor data is present. RAO consistently performs well across a wide variety of map topologies and driving scenarios. RAO incurs negligible additional latency (8.5 ms) and low data transmission overhead (10.9 KB per frame), making cooperative perception feasible.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613271"}, {"primary_key": "1231633", "vector": [], "sparse_vector": [], "title": "NeRF2: Neural Radio-Frequency Radiance Fields.", "authors": ["<PERSON><PERSON><PERSON>", "Z<PERSON><PERSON> An", "Qingrui Pan", "<PERSON><PERSON>"], "summary": "Although <PERSON> discovered the physical laws of electromagnetic waves 160 years ago, how to precisely model the propagation of an RF signal in an electrically large and complex environment remains a long-standing problem. The difficulty is in the complex interactions between the RF signal and the obstacles (e.g., reflection, diffraction, etc.). Inspired by the great success of using a neural network to describe the optical field in computer vision, we propose a neural radio-frequency radiance field, NeRF2, which represents a continuous volumetric scene function that makes sense of an RF signal's propagation. Particularly, after training with a few signal measurements, NeRF2 can tell how/what signal is received at any position when it knows the position of a transmitter. As a physical-layer neural network, NeRF2 can take advantage of the learned statistic model plus the physical model of ray tracing to generate a synthetic dataset that meets the training demands of application-layer artificial neural networks (ANNs). Thus, we can boost the performance of ANNs by the proposed turbo-learning, which mixes the true and synthetic datasets to intensify the training. Our experiment results show that turbo-learning can enhance performance with an approximate 50% increase. We also demonstrate the power of NeRF2 in the field of indoor localization and 5G MIMO.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592527"}, {"primary_key": "1231634", "vector": [], "sparse_vector": [], "title": "AutoFed: Heterogeneity-Aware Federated Multimodal Learning for Robust Autonomous Driving.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hong<PERSON> Wang", "<PERSON>"], "summary": "Object detection with on-board sensors (e.g., lidar, radar, and camera) is crucial to autonomous driving (AD), and these sensors complement each other in modalities. While crowdsensing may potentially exploit these sensors (of huge quantity) to derive more comprehensive knowledge, federated learning (FL) appears to be the necessary tool to reach this potential: it enables autonomous vehicles (AVs) to train machine learning models without explicitly sharing raw sensory data. However, the multimodal sensors introduce various data heterogeneity across distributed AVs (e.g., label quantity skews and varied modalities), posing critical challenges to effective FL. To this end, we present AutoFed as a heterogeneity-aware FL framework to fully exploit multimodal sensory data on AVs and thus enable robust AD. Specifically, we first propose a novel model leveraging pseudo labeling to avoid mistakenly treating unlabeled objects as the background. We also propose an autoencoder-based data imputation method to fill missing data modality (of certain AVs) with the available ones. To further reconcile the heterogeneity, we finally present a client selection mechanism based on client model similarities to improve training stability and convergence rate. Our experiments confirm that AutoFed substantially improves over status quo in both precision and recall, while demonstrating strong robustness to adverse weather.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3592517"}, {"primary_key": "1231635", "vector": [], "sparse_vector": [], "title": "SignQuery: A Natural User Interface and Search Engine for Sign Languages with Wearable Sensors.", "authors": ["<PERSON><PERSON>", "Taiting Lu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Search Engines such as Google, Baidu, and Bing have revolutionized the way we interact with the cyber world with a number of applications in recommendations, learning, advertisements, healthcare, entertainment, etc. In this paper, we design search engines for sign languages such as American Sign Language (ASL). Sign languages use hand and body motion for communication with rich grammar, complexity, and vocabulary that is comparable to spoken languages. This is the primary language for the Deaf community with a global population of ≈ 500 million. However, search engines that support sign language queries in native form do not exist currently. While translating a sign language to a spoken language and using existing search engines might be one possibility, this can miss critical information because existing translation systems are either limited in vocabulary or constrained to a specific domain. In contrast, this paper presents a holistic approach where ASL queries in native form as well as ASL videos and textual information available online are converted into a common representation space. Such a joint representation space provides a common framework for precisely representing different sources of information and accurately matching a query with relevant information that is available online. Our system uses low-intrusive wearable sensors for capturing the sign query. To minimize the training overhead, we obtain synthetic training data from a large corpus of online ASL videos across diverse topics. Evaluated over a set of Deaf users with native ASL fluency, the accuracy is comparable with state-of-the-art recommendation systems for Amazon, Netflix, Yelp, etc., suggesting the usability of the system in the real world. For example, the re-call@10 of our system is 64.3%, i.e., among the top ten search results, six of them are relevant to the search query. Moreover, the system is robust to variations in signing patterns, dialects, sensor positions, etc.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613286"}, {"primary_key": "1231637", "vector": [], "sparse_vector": [], "title": "Experience: Large-scale Cellular Localization for Pickup Position Recommendation at Black-hole.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Changcheng Liu", "<PERSON><PERSON><PERSON>", "Zengwei <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Location awareness is the basis for enabling pickup service at ride-hailing platforms. In contrast to the almost pervasive coverage outdoors, indoor localization availability is still sporadic in industry since it largely relies on RF signatures from certain IT infrastructure, e.g., WiFi access points. Based on our 2-year observations at DiDi ride-hailing platform in China, there are 68k orders everyday created at black-hole, i.e., where only cellular signals exist. In this paper, we present the design, development, and deployment of TransparentLoc, a large-scale cellular localization system for pickup position recommendation, and share our 2-year experience with 50 million orders across 13 million devices in 4541 cities to address practical challenges including sparse cell towers, unbalanced user fingerprints, and temporal variations. Our system outperforms the iOS built-in cellular localization system in terms of four major service metrics, regardless of environmental changes, smartphone brands/models, time, and cellular providers.", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361.3613298"}, {"primary_key": "1278084", "vector": [], "sparse_vector": [], "title": "Proceedings of the 29th Annual International Conference on Mobile Computing and Networking, ACM MobiCom 2023, Madrid, Spain, October 2-6, 2023", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Haitham Al-Hassanieh", "<PERSON><PERSON>", "<PERSON>"], "summary": "Open RAN, a modular and disaggregated design paradigm for 5G radio access networks (RAN), promises programmability through the RAN Intelligent Controller (RIC). However, due to latency and safety challenges, the telemetry and control provided by the ...", "published": "2023-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3570361"}]