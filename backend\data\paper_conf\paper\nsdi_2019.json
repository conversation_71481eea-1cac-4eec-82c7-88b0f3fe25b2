[{"primary_key": "3099170", "vector": [], "sparse_vector": [], "title": "Eiffel: Efficient and Flexible Software Packet Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Packet scheduling determines the ordering of packets in a queuing data structure with respect to some ranking function that is mandated by a scheduling policy. It is the core component in many recent innovations in optimizing network performance and utilization. Packet scheduling is used for network resource allocation, meeting network-wide delay objectives, or providing isolation and differentiation of service. Our focus in this paper is on the design and deployment of packet scheduling in software. Software schedulers have several advantages including shorter development cycle and flexibility in functionality and deployment location. We substantially improve software packet scheduling performance, while maintaining its flexibility, by exploiting underlying features of packet ranking; the fact that packet ranks are integers that have predetermined ranges and that many packets will typically have equal rank. This allows us to rely on integer priority queues, compared to existing ranking algorithms, that rely on comparison-based priority queues that assume continuous ranks with infinite range. We introduce Eiffel, a novel programmable packet scheduling system. At the core of Eiffel is an integer priority queue based on the Find First Set (FFS) instruction and designed to support a wide range of policies and ranking functions efficiently. As an even more efficient alternative, we also propose a new approximate priority queue that can outperform FFS-based queues for some scenarios. To support flexibility, <PERSON>iff<PERSON> introduces novel programming abstractions to express scheduling policies that cannot be captured by current, state of the art scheduler programming models. We evaluate Eiffel in a variety of settings and in both Kernel and userspace deployments.  We show that it outperforms state of the art systems by 3-40x in terms of either number of cores utilized for network processing or number of flows given fixed processing capacity.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099171", "vector": [], "sparse_vector": [], "title": "CAUDIT: Continuous Auditing of SSH Servers To Mitigate Brute-Force Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Zbigniew T<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes CAUDIT, an operational system deployed at the National Center for Supercomputing Applications (NCSA) at the University of Illinois. CAUDIT is a fully automated system that enables the identification and exclusion of hosts that are vulnerable to SSH brute-force attacks. Its key features include: 1) a honeypot for attracting SSH-based attacks over a /16 IP address range and extracting key metadata (e.g., source IP, password, SSH-client version, or key fingerprint) from these attacks; 2) executing audits on the live production network by replaying of attack attempts recorded by the honeypot; 3) using the IP addresses recorded by the honeypot to block SSH attack attempts at the network border by using a Black Hole Router (BHR) while significantly reducing the load on NCSA's security monitoring system; and 4) the ability to inform peer sites of attack attempts in real-time to ensure containment of coordinated attacks. The system is composed of existing techniques with custom-built components, and its novelty is its ability to execute at a scale that has not been validated earlier (with thousands of nodes and tens of millions of attack attempts per day). Experience over 463 days shows that CAUDIT successfully blocks an average of 57 million attack attempts on a daily basis using the proposed BHR. This represents a 66 times reduction in the number of SSH attempts compared to the daily average and has reduced the traffic to the NCSA's internal network-security-monitoring infrastructure by 78%.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099172", "vector": [], "sparse_vector": [], "title": "Hydra: a federated resource manager for data-center scale analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Kishore Chaliparambil", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microsoft's internal data lake processes exabytes of data over millions of cores daily on behalf of thousands of tenants. Scheduling this workload requires 10x to 100x more decisions per second than existing, general-purpose resource management frameworks are known to handle. In 2013, we were faced with a growing demand for workload diversity and richer sharing policies that our legacy system could not meet. In this paper, we present Hydra, the resource management infrastructure we built to meet these requirements. Hydra leverages a federated architecture, in which a cluster is comprised of multiple, loosely coordinating subclusters. This allows us to scale by delegating placement of tasks on machines to each sub-cluster, while centrally coordinating only to ensure that tenants receive the right share of resources. To adapt to changing workload and cluster conditions promptly, Hydra's design features a control plane that can push scheduling policies across tens of thousands of nodes within seconds. This feature combined with the federated design allows for great agility in developing, evaluating, and rolling out new system behaviors. We built Hydra by leveraging, extending, and contributing our code to Apache Hadoop YARN. Hydra is currently the primary big-data resource manager at Microsoft. Over the last few years, Hydra has scheduled nearly one trillion tasks that manipulated close to a Zettabyte of production data.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099173", "vector": [], "sparse_vector": [], "title": "TrackIO: Tracking First Responders Inside-Out.", "authors": ["<PERSON><PERSON><PERSON>", "Ayon Chakraborty", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "First responders, a critical lifeline of any society, often find themselves in precarious situations. The ability to track them real-time in unknown indoor environments, would significantly contributes to the success of their mission as well as their safety. In this work, we present the design, implementation and evaluation of TrackIO--a system capable of accurately localizing and tracking mobile responders real-time in large indoor environments. TrackIO leverages the mobile virtual infrastructure offered by unmanned aerial vehicles (UAVs), coupled with the balanced penetration-accuracy tradeoff offered by ultra-wideband (UWB), to accomplish this objective directly from outside, without relying on access to any indoor infrastructure. Towards a practical system, TrackIO incorporates four novel mechanisms in its design that address key challenges to enable tracking responders (i) who are mobile with potentially non-uniform velocities (e.g. during turns), (ii) deep indoors with challenged reachability, (iii) in real-time even for a large network, and (iv) with high accuracy even when impacted by UAV’s position error. TrackIO’s real-world performance reveals that it can track static nodes with a median accuracy of about 1–1.5m and mobile (even running) nodes with a median accuracy of 2–2.5m in large buildings in real-time.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099174", "vector": [], "sparse_vector": [], "title": "Size-aware Sharding For Improving Tail Latencies in In-memory Key-value Stores.", "authors": ["Diego <PERSON>", "<PERSON>"], "summary": "This paper introduces the concept of size-aware sharding to improve tail latencies for in-memory key-value stores, and describes its implementation in the Minos key-value store. Tail latencies are crucial in distributed applications with high fan-out ratios, because overall response time is determined by the slowest response. Size-aware sharding distributes requests for keys to cores according to the size of the item associated with the key. In particular, requests for small and large items are sent to disjoint subsets of cores. Size-aware sharding improves tail latencies by avoiding head-of-line blocking, in which a request for a small item gets queued behind a request for a large item. Alternative size-unaware approaches to sharding such as keyhash-based sharding, request dispatching and stealing do not avoid head-of-line blocking, and therefore exhibit worse tail latencies. The challenge in implementing size-aware sharding is to maintain high throughput by avoiding the cost of software dispatching and by achieving load balancing between different cores. Minos uses hardware dispatch for all requests for small items, which form the very large majority of all requests. It achieves load balancing by adapting the number of cores handling requests for small and large items to their relative presence in the workload. We compare Minos to three state-of-the-art designs of in-memory KV stores. Compared to its closest competitor, Minos achieves a 99th percentile latency that is up to two orders of magnitude lower. Put differently, for a given value for the 99th percentile latency equal to 10 times the mean service time, Minos achieves a throughput that is up to 7.4 times higher.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099175", "vector": [], "sparse_vector": [], "title": "Is advance knowledge of flow sizes a plausible assumption?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent research has proposed several packet, flow, and coflow scheduling methods that could substantially improve data center network performance. Most of this work assumes advance knowledge of flow sizes. However, the lack of a clear path to obtaining such knowledge has also prompted some work on non-clairvoyant scheduling, albeit with more limited performance benefits. We thus investigate whether flow sizes can be known in advance in practice, using both simple heuristics and learning methods. Our systematic and substantial efforts for estimating flow sizes indicate, unfortunately, that such knowledge is likely hard to obtain with high confidence across many settings of practical interest. Nevertheless, our prognosis is ultimately more positive: even simple heuristics can help estimate flow sizes for many flows, and this partial knowledge has utility in scheduling. These results indicate that a presumed lack of advance knowledge of flow sizes is not necessarily prohibitive for highly efficient scheduling, and suggest further exploration in two directions: (a) scheduling under partial knowledge; and (b) evaluating the practical payoff and expense of obtaining more knowledge.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099176", "vector": [], "sparse_vector": [], "title": "Dataplane equivalence and its applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the design and implementation of netdiff, an algorithm that uses symbolic execution to check the equivalence of two network dataplanes modeled in SEFL. We use netdiff to find new bugs in Openstack Neutron, to test the differences between related P4 programs and to check the equivalence of FIB updates in a production network. Our evaluation highlights that equivalence is an easy way to find bugs, scales well to relatively large programs and uncovers subtle issues otherwise difficult to find.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099177", "vector": [], "sparse_vector": [], "title": "Flashield: a Hybrid Key-value Cache that Controls Flash Write Amplification.", "authors": ["<PERSON><PERSON><PERSON>", "Asaf Cidon", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As its price per bit drops, SSD is increasingly becoming the default storage medium for hot data in cloud application databases. Even though SSD’s price per bit is more than 10× lower, and it provides sufficient performance (when accessed over a network) compared to DRAM, the durability of flash has limited its adoption in write-heavy use cases, such as key-value caching. This is because key-value caches need to frequently insert, update and evict small objects. This causes excessive writes and erasures on flash storage, which significantly shortens the lifetime of flash. We present Flashield, a hybrid key-value cache that uses DRAM as a “filter” to control and limit writes to SSD. Flashield performs lightweight machine learning admission control to predict which objects are likely to be read frequently without getting updated; these objects, which are prime candidates to be stored on SSD, are written to SSD in large chunks sequentially. In order to efficiently utilize the cache’s available memory, we design a novel in-memory index for the variable-sized objects stored on flash that requires only 4 bytes per object in DRAM. We describe Flashield’s design and implementation, and evaluate it on real-world traces from a widely used caching service, Memcachier. Compared to state-of-the-art systems that suffer a write amplification of 2.5× or more, Flashield maintains a median write amplification of 0.5× (since many filtered objects are never written to flash at all), without any loss of hit rate or throughput.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099178", "vector": [], "sparse_vector": [], "title": "SIMON: A Simple and Scalable Method for Sensing, Inference and Measurement in Data Center Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network measurement and monitoring have been key to understanding the inner workings  of computer networks and debugging the performance problems of distributed applications.   Despite many products and much research on these topics, in the context of data centers,  performing accurate measurement at scale in near real-time has remained elusive.  On  one hand, switch-based telemetry can give accurate per-packet views, but these must  be assembled across the network and across packets to get network- and application-level  insight: this is not scalable.  On the other hand, purely end-host-based measurement is  naturally scalable but so far has only provided partial views of in-network operation. In this paper, we set out to push the boundary of edge-based measurement by scalably  and accurately reconstructing the full queueing dynamics in the network with  data gathered entirely at the transmit and receive network interface cards (NICs). We begin with a Signal Processing framework for quantifying a key trade-off:  reconstruction accuracy versus the amount of data gathered.  Based on this, we  propose SIMON, an accurate and scalable measurement system for data centers that  reconstructs key network state variables like packet queuing times at switches,  link utilizations, and queue and link compositions at the flow-level.  We then  demonstrate that the function approximation capability of multi-layered neural  networks can speed up SIMON by a factor of 5,000--10,000, enabling it to run in near real-time.  We deployed SIMON in three testbeds with different link speeds,  layers of switching and number of servers; evaluations with NetFPGAs and a  cross-validation technique show that SIMON reconstructs queue-lengths to within  3-5 KBs and link utilizations to less than 1% of actual.  The accuracy and speed  of SIMON enables sensitive A/B tests, which greatly aids the real-time development  of algorithms, protocols, network software and applications.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099179", "vector": [], "sparse_vector": [], "title": "Tiresias: A GPU Cluster Manager for Distributed Deep Learning.", "authors": ["Juncheng Gu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep learning (DL) training jobs bring some unique challenges to existing cluster managers, such as unpredictable training times, an all-or-nothing execution model, and inflexibility in GPU sharing. Our analysis of a large GPU cluster in production shows that existing big data schedulers cause long queueing delays and low overall performance. We present <PERSON><PERSON><PERSON>, a GPU cluster manager tailored for distributed DL training jobs, which efficiently schedules and places DL jobs to reduce their job completion times (JCTs). Given that a DL job’s execution time is often unpredictable, we propose two scheduling algorithms – Discretized Two-Dimensional Gittins index relies on partial information and Discretized Two-Dimensional LAS is information-agnostic – that aim to minimize the average JCT. Additionally, we describe when the consolidated placement constraint can be relaxed, and present a placement algorithm to leverage these observations without any user input. Experiments on the Michigan ConFlux cluster with 60 P100 GPUs and large-scale trace-driven simulations show that Tiresias improves the average JCT by up to 5.5× over an Apache YARN-based resource manager used in production. More importantly, Tiresias’s performance is comparable to that of solutions assuming perfect knowledge.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099180", "vector": [], "sparse_vector": [], "title": "SweepSense: Sensing 5 GHz in 5 Milliseconds with Low-cost Radios.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wireless transmissions occur intermittently across the entire spectrum. For example, WiFi and Bluetooth devices transmit frames across the 100 MHz-wide 2.4 GHz band, and LTE devices transmit frames between 700 MHz and 3.7 GHz). Today, only high-cost radios can sense across the spectrum with sufficient temporal resolution to observe these individual transmissions. We present “SweepSense”, a low-cost radio architecture that senses the entire spectrum with high-temporal resolution by rapidly sweeping across it. Sweeping introduces new challenges for spectrum sensing: SweepSense radios only capture a small number of distorted samples of transmissions. To overcome this challenge, we correct the distortion with self-generated calibration data, and classify the protocol that originated each transmission with only a fraction of the transmission’s samples. We demonstrate that SweepSense can accurately identify four protocols transmitting simultaneously in the 2.4 GHz unlicensed band. We also demonstrate that it can simultaneously monitor the load of several LTE base stations operating in disjoint bands.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099181", "vector": [], "sparse_vector": [], "title": "Scaling Community Cellular Networks with CommunityCellularManager.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Hundreds of millions of people still live beyond the coverage of basic mobile connectivity, primarily in rural areas with low population density. Mobile-network operators (MNOs) traditionally struggle to justify expansion into these rural areas due to the high infrastructure costs necessary to provide service. Community cellular networks, networks built \"by and for\" the people they serve, represent an alternative model that, to an extent, bypasses these business case limitations and enables sustainable rural coverage. Yet despite aligned economic incentives, real deployments of community cellular networks still face significant regulatory, commercial and technical challenges. In this paper, we present CommunityCellularManager (CCM), a system for operating community cellular networks at scale. CCM enables multiple community networks to operate under the control of a single, multi-tenant controller and in partnership with a traditional MNO. CCM preserves flexibility for each community network to operate independently, while allowing the mobile network operator to safely make critical resources such as spectrum and phone numbers available to these networks. We evaluate CCM through a multi-year, large-scale community cellular network deployment in the Philippines in partnership with a traditional MNO, providing basic communication services to over 2,000 people in 15 communities without requiring changes to the existing regulatory framework, and using existing handsets. We demonstrate that CCM can support independent community networks with unique service offerings and operating models while providing a basic level of MNO-defined service. To our knowledge, this represents the largest deployment of community cellular networks to date.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099182", "vector": [], "sparse_vector": [], "title": "NetScatter: Enabling Large-Scale Backscatter Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the first wireless protocol that scales to hundreds of concurrent transmissions from backscatter devices. Our key innovation is a distributed coding mechanism that works below the noise floor, operates on backscatter devices and can decode all the concurrent transmissions at the receiver using a single FFT operation. Our design addresses practical issues such as timing and frequency synchronization as well as the near-far problem. We deploy our design using a testbed of backscatter hardware and show that our protocol scales to concurrent transmissions from 256 devices using a bandwidth of only 500 kHz. Our results show throughput and latency improvements of 14–62x and 15–67x over existing approaches and 1–2 orders of magnitude higher transmission concurrency.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099183", "vector": [], "sparse_vector": [], "title": "Blink: Fast Connectivity Recovery Entirely in the Data Plane.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we explore new possibilities, created by programmable switches, for fast rerouting upon signals triggered by Internet traffic disruptions. We present Blink, a data-driven system exploiting TCP-induced signals to detect failures. The key intuition behind Blink is that a TCP flow exhibits a predictable behavior upon disruption: retransmitting the same packet over and over, at epochs exponentially spaced in time. When compounded over multiple flows, this behavior creates a strong and characteristic failure signal. Blink efficiently analyzes TCP flows, at line rate, to: (i) select flows to track; (ii) reliably and quickly detect major traffic disruptions; and (iii) recover data-plane connectivity, via next-hops compatible with the operator’s policies. We present an end-to-end implementation of Blink in P4 together with an extensive evaluation on real and synthetic traffic traces. Our results indicate that Blink: (i) can achieve sub-second rerouting for realistic Internet traffic; (ii) prevents unnecessary traffic shifts, in the presence of noise; and (iii) scales to protect large fractions of realistic Internet traffic, on existing hardware. We further show the feasibility of Blink by running our system on a real Tofino switch.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099184", "vector": [], "sparse_vector": [], "title": "Performance Contracts for Software Network Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software network functions (NFs), or middleboxes, promise flexibility and easy deployment of network services but face the serious challenge of unexpected performance behaviour. We propose the notion of a performance contract, a construct formulated in terms of performance critical variables, that provides a precise description of NF performance. Performance contracts enable fine-grained prediction and scrutiny of NF performance for arbitrary workloads, without having to run the NF itself. We describe BOLT, a technique and tool for computing such performance contracts for the entire software stack of NFs written in C, including the core NF logic, DPDK packet processing framework, and NIC driver. BOLT takes as input the NF implementation code and outputs the corresponding contract. Under the covers, it combines pre-analysis of a library of stateful NF data structures with automated symbolic execution of the NF’s code. We evaluate BOLT on four NFs—a Maglev-like load balancer, a NAT, an LPM router, and a MAC bridge—and show that its performance contracts predict the dynamic instruction count and memory access count with a maximum gap of 7% between the real execution and the conservatively predicted upper bound. With further engineering, this gap can be reduced.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099185", "vector": [], "sparse_vector": [], "title": "JANUS: Fast and Flexible Deep Learning via Symbolic Graph Execution of Imperative Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gyeong-In Yu", "<PERSON><PERSON>", "Dongjin Shin", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The rapid evolution of deep neural networks is demanding deep learning (DL) frameworks not only to satisfy the requirement of quickly executing large computations, but also to support straightforward programming models for quickly implementing and experimenting with complex network structures. However, existing frameworks fail to excel in both departments simultaneously, leading to diverged efforts for optimizing performance and improving usability. This paper presents JANUS, a system that combines the advantages from both sides by transparently converting an imperative DL program written in Python, the de-facto scripting language for DL, into an efficiently executable symbolic dataflow graph. JANUS can convert various dynamic features of Python, including dynamic control flow, dynamic types, and impure functions, into the symbolic graph operations. Experiments demonstrate that JANUS can achieve fast DL training by exploiting the techniques imposed by symbolic graph-based DL frameworks, while maintaining the simple and flexible programmability of imperative DL frameworks at the same time.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099186", "vector": [], "sparse_vector": [], "title": "Stable and Practical AS Relationship Inference with ProbLink.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge of the business relationships between Autonomous Systems (ASes) is essential to understanding the behavior of the Internet routing system. Despite significant progress in the development of sophisticated relationship inference algorithms, the resulting datasets are impractical for many critical real-world applications, cannot offer adequate predictability in the configuration of routing policies, and suffer from inference oscillations. To achieve more practical and stable relationship inferences we first illuminate the root causes of the contradictions between these shortcomings and the near-perfect validation results of AS-Rank, the state-of-the-art relationship inference algorithm. Using a \"naive\" inference approach as a benchmark, we find that the available validation datasets over-represent AS links with easier inference requirements. We identify which types of links are harder to infer, and we develop appropriate validation subsets to enable more representative evaluation. We then develop a probabilistic algorithm, ProbLink, to overcome the inference barriers for hard links,\nsuch as non-valley-free routing, limited visibility, and non-conventional peering practices.\nTo this end, we identify key interconnection features that provide stochastically informative and highly predictive relationship inference signals.  Compared to AS-Rank, our approach reduces the error rate for all links by 1.6$\\times$, and importantly, by up to 6.1$\\times$ for different types of hard links. We demonstrate the practical significance of our improvements by evaluating their impact on three applications. Compared to the current state-of-the-art, ProbLink increases the precision and recall of route leak detection by 4.1$\\times$ and 3.4$\\times$ respectively, reveals 27% more complex relationships, and increases the precision of predicting the impact of selective advertisements by 34%.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099187", "vector": [], "sparse_vector": [], "title": "Many-to-Many Beam Alignment in Millimeter Wave Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junfeng Guan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter Wave (mmWave) networks can deliver multi-Gbps wireless links that use extremely narrow directional beams. This provides us with a new opportunity to exploit spatial reuse in order to scale network throughput. Exploiting such spatial reuse, however, requires aligning the beams of all nodes in a network. Aligning the beams is a difficult process which is complicated by indoor multipath, which can create interference, as well as by the inefficiency of carrier sense at detecting interference in directional links. This paper presents BounceNet, the first many-to-many millimeter wave beam alignment protocol that can exploit dense spatial reuse to allow many links to operate in parallel in a confined space and scale the wireless throughput with the number of clients. Results from three millimeter wave testbeds show that BounceNet can scale the throughput with the number of clients to deliver a total network data rate of more than 39 Gbps for 10 clients, which is up to 6.6x higher than current 802.11 mmWave standards.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099188", "vector": [], "sparse_vector": [], "title": "Shinjuku: Preemptive Scheduling for μsecond-scale Tail Latency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The recently proposed dataplanes for microsecond scale applications, such as IX and ZygOS, use non-preemptive policies to schedule requests to cores. For the many real-world scenarios where request service times follow distributions with high dispersion or a heavy tail, they allow short requests to be blocked behind long requests, which leads to poor tail latency. Shinjuku is a single-address space operating system that uses hardware support for virtualization to make preemption practical at the microsecond scale. This allows Shinjuku to implement centralized scheduling policies that preempt requests as often as every 5µsec and work well for both light and heavy tailed request service time distributions. We demonstrate that Shinjuku provides significant tail latency and throughput improvements over IX and ZygOS for a wide range of workload scenarios. For the case of a RocksDB server processing both point and range queries, Shinjuku achieves up to 6.6× higher throughput and 88% lower tail latency.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099189", "vector": [], "sparse_vector": [], "title": "Datacenter RPCs can be General and Fast.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is commonly believed that datacenter networking software must sacrifice generality to attain high performance. The popularity of specialized distributed systems designed specifically for niche technologies such as RDMA, lossless networks, FPGAs, and programmable switches testifies to this belief. In this paper, we show that such specialization is not necessary. eRPC is a new general-purpose remote procedure call (RPC) library that offers performance comparable to specialized systems, while running on commodity CPUs in traditional datacenter networks based on either lossy Ethernet or lossless fabrics. eRPC performs well in three key metrics: message rate for small messages; bandwidth for large messages; and scalability to a large number of nodes and CPU cores. It handles packet loss, congestion, and background request execution. In microbenchmarks, one CPU core can handle up to 10 million small RPCs per second, or send large messages at 75 Gbps. We port a production-grade implementation of Raft state machine replication to eRPC without modifying the core Raft source code. We achieve 5.5 microseconds of replication latency on lossy Ethernet, which is faster than or comparable to specialized replication systems that use programmable switches, FPGAs, or RDMA.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099190", "vector": [], "sparse_vector": [], "title": "Correctness and Performance for Stateful Chained Network Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network functions virtualization (NFV) allows\noperators to employ NF chains to realize custom policies, and\ndynamically add instances to meet demand or for failover. NFs maintain\ndetailed per- and cross-flow state which needs careful management,\nespecially during dynamic actions. Crucially, state management must:\n(1) ensure NF chain-wide correctness and (2) have good\nperformance. To this end, we built CHC, an NFV framework that leverages an external state store coupled with state management algorithms and metadata maintenance for correct operation even under a range of failures. Our evaluation shows\nthat CHC can support ~10Gbps per-NF throughput and $<0.6μs increase in median per-NF packet processing latency, and chain-wide correctness at little\nadditional cost.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099191", "vector": [], "sparse_vector": [], "title": "Confluo: Distributed Monitoring and Diagnosis Stack for High-speed Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ion <PERSON>"], "summary": "Confluo is an end-host stack that can be integrated with existing network management tools to enable monitoring and diagnosis of network-wide events using telemetry data distributed across end-hosts, even for high-speed networks. Confluo achieves these properties using a new data structure—Atomic MultiLog—that supports highly-concurrent read-write operations by exploiting two properties specific to telemetry data: (1) once processed by the stack, the data is neither updated nor deleted; and (2) each field in the data has a fixed pre-defined size. Our evaluation results show that, for packet sizes 128B or larger, Confluo executes thousands of triggers and tens of filters at line rate (for 10Gbps links) using a single core.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099192", "vector": [], "sparse_vector": [], "title": "FreeFlow: Software-based Virtual RDMA Networking for Containerized Clouds.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many popular large-scale cloud applications are increasingly using containerization for high resource efficiency and lightweight isolation. In parallel, many data-intensive applications (e.g., data analytics and deep learning frameworks) are adopting or looking to adopt RDMA for high networking performance. Industry trends suggest that these two approaches are on an inevitable collision course. In this paper, we present FreeFlow, a software-based RDMA virtualization framework designed for containerized clouds. FreeFlow realizes virtual RDMA networking purely with a software-based approach using commodity RDMA NICs. Unlike existing RDMA virtualization solutions, FreeFlow fully satisfies the requirements from cloud environments, such as isolation for multi-tenancy, portability for container migrations, and controllability for control and data plane policies. FreeFlow is also transparent to applications and provides networking performance close to bare-metal RDMA with low CPU overhead. In our evaluations with TensorFlow and Spark, FreeFlow provides almost the same application performance as bare-metal RDMA.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099193", "vector": [], "sparse_vector": [], "title": "DETER: Deterministic TCP Replay for Performance Diagnosis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "TCP performance problems are notoriously tricky to diagnose because a subtle choice of TCP parameters or features may lead to completely different performance. A gold standard for diagnosis is to collect packet traces and trace TCP executions. However, it is not easy to use such tools in large-scale data centers where many TCP connections interact with each other. In this paper, we introduce DETER, a deterministic TCP replay tool, which runs lightweight recording all the time at all the hosts and then replay selected collections where operators can collect packet traces and trace TCP executions for diagnosis. The key challenge for deterministic TCP replay is the butterfly effect---a small timing variation causes a chain reaction between TCP and the network that drives the system to a completely different state in the replay. To eliminate the butterfly effect, we propose to replay individual TCP connection separately and capture all the interactions between a connection with the applications and the network. Our evaluation shows that \\system has low recording overhead and can help diagnose many TCP performance problems such as long latency related to zero-window probes, late fast retransmission, frequent retransmission timeout, to problems related to the switch shared buffer.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099194", "vector": [], "sparse_vector": [], "title": "Towards Programming the Radio Environment with Large Arrays of Inexpensive Antennas.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Conventional thinking treats the wireless channel as a given constraint. Therefore, wireless network designs to date center on the problem of the endpoint optimization that best utilizes the channel, for example, via rate and power control at the transmitter or sophisticated decoding mechanisms at the receiver.  We instead explore whether it is possible to reconfigure the environment itself to facilitate wireless communication. In this work, we instrument the environment with a large array of inexpensive antennas (LAIA) and design algorithms to configure them in real time. Our system achieves this level of programmability through rapid adjustments of an on-board phase shifter in each LAIA device.  We design a channel decomposition algorithm to quickly estimate the wireless channel due to the environment alone, which leads us to a process to align the phases of the array elements. Variations of our core algorithm can then optimize wireless channels on the fly for single- and multi-antenna links,  as well as nearby networks operating on adjacent frequency bands. We design and deploy a 36-element passive array in a real indoor home environment. Experiments with this prototype show that, by reconfiguring the wireless environment, we can achieve a 24% TCP throughput improvement on average and a median improvement of 51.4% in Shannon capacity over the baseline single-antenna links.  Over the baseline multi-antenna links, LAIA achieves an improvement of 12.23% to 18.95% in Shannon capacity.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099195", "vector": [], "sparse_vector": [], "title": "3D Backscatter Localization for Fine-Grained Robotics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the design and implementation of TurboTrack, a 3D\nlocalization system for fine-grained robotic tasks. TurboTrack's unique\ncapability is that it can localize backscatter nodes with\nsub-centimeter accuracy without any constraints on their locations or\nmobility. TurboTrack makes two key technical contributions.  First, it\npresents a pipelined architecture that can extract a sensing bandwidth\nfrom every single backscatter packet that is three orders of magnitude\nlarger than the backscatter communication bandwidth. Second, it\nintroduces a Bayesian space-time super-resolution algorithm that\ncombines time series of the sensed bandwidth across multiple antennas\nto enable accurate positioning. Our experiments show that TurboTrack\nsimultaneously achieves a median accuracy of sub-centimeter in each of\nthe x/y/z dimensions and a $99^{th}$ percentile latency less than\n7.5 milliseconds in 3D localization. This enables TurboTrack's real-time\nprototype to achieve fine-grained positioning for agile robotic tasks,\nas we demonstrate in multiple collaborative applications with robotic\narms and nanodrones including indoor tracking, packaging, assembly,\nand handover.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099196", "vector": [], "sparse_vector": [], "title": "Alembic: Automated Model Inference for Stateful Network Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Network operators today deploy a wide range of complex stateful network functions (NFs). They typically only have access to the NFs’ binary executables, configuration interfaces, and manuals from vendors. To ensure correct behavior of NFs, operators use network testing and verification tools, which typically rely on models of the deployed NFs. The effectiveness of these tools depends upon the fidelity of such models. Today, models are handwritten, which can be error prone, tedious, and does not account for implementation-specific artifacts. To address this gap, our goal is to automatically infer behavioral models of stateful NFs for a given configuration. The problem is challenging because NF configurations can contain diverse rule types and the space of dynamic and stateful NF behaviors is large. In this work, we present Alembic, which synthesizes NF models viewed as an ensemble of finite-state machines (FSMs). Alembic consists of an offline stage that learns symbolic FSM representations for each NF rule type and a fast online stage that generates a concrete behavioral model for a given configuration using these symbolic FSMs. We demonstrate that Alembic is accurate, scalable and sheds light on subtle differences across NF implementations.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099197", "vector": [], "sparse_vector": [], "title": "Shenango: Achieving High CPU Efficiency for Latency-sensitive Datacenter Workloads.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Datacenter applications demand microsecond-scale tail latencies and\nhigh request rates from operating systems, and most applications\nhandle loads that have high variance over multiple timescales.\nAchieving these goals in a CPU-efficient way is an open\nproblem. Because of the high overheads of today's kernels, the best\navailable solution to achieve microsecond-scale latencies is\nkernel-bypass networking, which dedicates CPU cores to applications\nfor spin-polling the network card. But this approach wastes CPU: even\nat modest average loads, one must dedicate enough cores for the peak expected load. Shenango achieves comparable latencies but at far greater CPU\nefficiency. It reallocates cores across applications at very fine\ngranularity—every 5 µs—enabling cycles unused by latency-sensitive\napplications to be used productively by batch processing applications. It achieves\nsuch fast reallocation rates with (1) an efficient algorithm that\ndetects when applications would benefit from more cores, and (2) a\nprivileged component called the IOKernel that runs on a dedicated core,\nsteering packets from the NIC and orchestrating\ncore reallocations.  When handling latency-sensitive applications, such as\nmemcached, we found that Shenango achieves tail latency and throughput comparable\nto ZygOS, a state-of-the-art, kernel-bypass network stack, but can linearly\ntrade latency-sensitive application throughput for batch processing application\nthroughput, vastly increasing CPU efficiency.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099198", "vector": [], "sparse_vector": [], "title": "Exploiting Commutativity For Practical Fast Replication.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional approaches to replication require client requests to be ordered before making them durable by copying them to replicas. As a result, clients must wait for two round-trip times (RTTs) before updates complete. In this paper, we show that this entanglement of ordering and durability is unnecessary for strong consistency. Consistent Unordered Replication Protocol (CURP) allows clients to replicate requests that have not yet been ordered, as long as they are commutative. This strategy allows most operations to complete in 1 RTT (the same as an unreplicated system). We implemented CURP in the Redis and RAMCloud storage systems. In RAMCloud, CURP improved write latency by ~2x (14us -> 7.1us) and write throughput by 4x. Compared to unreplicated RAMCloud, CURP's latency overhead for 3-way replication is just 1us (6.1us vs 7.1us). CURP transformed a non-durable Redis cache into a consistent and durable storage system with only a small performance overhead.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099199", "vector": [], "sparse_vector": [], "title": "FlowBlaze: Stateful Packet Processing in Hardware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Michio Honda", "<PERSON>"], "summary": "Programmable NICs allow for better scalability to handle growing network workloads, however, providing an expressive, yet simple, abstraction to program stateful network functions in hardware remains a research challenge. We address the problem with FlowBlaze, an open abstraction for building stateful packet processing functions in hardware. The abstraction is based on Extended Finite State Machines and introduces the explicit definition of flow state, allowing FlowBlaze to leverage flow-level parallelism. FlowBlaze is expressive, supporting a wide range of complex network functions, and easy to use, hiding low-level hardware implementation issues from the programmer. Our implementation of FlowBlaze on a NetFPGA SmartNIC achieves very low latency (on the order of a few microseconds), consumes relatively little power, can hold per-flow state for hundreds of thousands of flows and yields speeds of 40 Gb/s, allowing for even higher speeds on newer FPGA models. Both hardware and software implementations of FlowBlaze are publicly available.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099200", "vector": [], "sparse_vector": [], "title": "Shuffling, Fast and Slow: Scalable Analytics on Serverless Infrastructure.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ion <PERSON>"], "summary": "Serverless computing is poised to fulfill the long-held promise of transparent elasticity and millisecond-level pricing. To achieve this goal, service providers impose a finegrained computational model where every function has a\nmaximum duration, a fixed amount of memory and no persistent local storage. We observe that the fine-grained elasticity of serverless is key to achieve high utilization for general computations such as analytics workloads, but that resource limits make it challenging to implement such applications as they need to move large amounts of data between functions that don’t overlap in time. In this paper, we present Locus, a serverless analytics system that judiciously combines (1) cheap but slow storage with (2) fast but expensive storage, to achieve good performance while remaining cost-efficient. Locus applies a performance model to guide users in selecting the type and the amount of storage to achieve the desired cost-performance trade-off. We evaluate Locus on a number of analytics applications including TPC-DS, CloudSort, Big Data Benchmark and show that Locus can navigate the cost-performance trade-off, leading to 4×-500× performance improvements over slow storage-only baseline and reducing resource usage by up to 59% while achieving comparable performance on a cluster of virtual machines, and within 1.99× slower compared to Redshift.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099201", "vector": [], "sparse_vector": [], "title": "Shoal: A Network Architecture for Disaggregated Racks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hakim Weatherspoon"], "summary": "Disaggregated racks comprise a dense cluster of separate pools of compute, memory and storage blades, all inter-connected through an internal network within a single rack. However, their density poses a unique challenge for the rack’s network: it needs to connect an order of magnitude more nodes than today’s racks without exceeding the rack’s fixed power budget and without compromising on performance. We present Shoal, a power-efficient yet performant intra-rack network fabric built using fast circuit switches. Such switches consume less power as they have no buffers and no packet inspection mechanism, yet can be reconfigured in nanoseconds. Rack nodes transmit according to a static schedule such that there is no in-network contention without requiring a centralized controller. Shoal’s congestion control leverages the physical fabric to achieve fairness and both bounded worst-case network throughput and queuing. We use an FPGA-based prototype, testbed experiments, and simulations to illustrate S<PERSON>al’s mechanisms are practical, and can simultaneously achieve high density and high performance: 71% lower power and comparable or higher performance than today’s network designs.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099202", "vector": [], "sparse_vector": [], "title": "Direct Universal Access: Making Data Center Resources Available to FPGA.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "FPGAs have been deployed at massive scale in data centers. The currently available communication architectures, however, make FPGAs very difficult to utilize resources in data center. In this paper, we present Direct Universal Access (DUA), a communication architecture that provides uniform access for FPGA to heterogeneous data center resources.\nWithout considering machine boundaries, DUA provides global names and a common interface for communicating with various resources, where the underlying network automatically routes traffic and manages resource multiplexing. Our benchmarks show that DUA provides simple and fair-share resource access with small logic area overhead (<10%) and negligible latency (<0.2$\\mu$s). We also build two practical multi-FPGA applications, deep crossing and regular expression matching, on top of DUA to demonstrate the usability and efficiency.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099203", "vector": [], "sparse_vector": [], "title": "Deniable Upload and Download via Passive Participation.", "authors": ["<PERSON>", "Aritra Dhar", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Downloading or uploading controversial information can put users at risk, making them hesitant to access or share such information. While anonymous communication networks (ACNs) are designed to hide communication meta-data, already connecting to an ACN can raise suspicion. In order to enable plausible deniability while providing or accessing controversial information, we design CoverUp: a system that enables users to asynchronously upload and download data. The key idea is to involve visitors from a collaborating website. This website serves a JavaScript snippet, which, after user's consent produces cover traffic for the controversial site / content. This cover traffic is indistinguishable from the traffic of participants interested in the controversial content; hence, they can deny that they actually up- or downloaded any data. CoverUp provides a feed-receiver that achieves a downlink rate of 10 to 50 Kbit/s. The indistinguishability guarantee of the feed-receiver holds against strong global network-level attackers who control everything except for the user's machine. We extend CoverUp to a full upload and download system with a rate of 10 up to 50 Kbit/s. In this case, we additionally need the integrity of the JavaScript snippet, for which we introduce a trusted party. The analysis of our prototype shows a very small timing leakage, even after half a year of continual observation. Finally, as passive participation raises ethical and legal concerns for the collaborating websites and the visitors of the collaborating website, we discuss these concerns and describe how they can be addressed.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099204", "vector": [], "sparse_vector": [], "title": "Loom: Flexible and Efficient NIC Packet Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In multi-tenant cloud data centers, operators need to ensure that competing tenants and applications are isolated from each other and fairly share limited network resources.  With current NICs, operators must either 1) use a single NIC queue and enforce network policy in software, which incurs high CPU overheads and struggles to drive increasing line-rates (100Gbps), or 2) use multiple NIC queues and accept imperfect isolation and policy enforcement.  These problems arise due to inflexible and static NIC packet schedulers and an inefficient OS/NIC interface. To overcome these limitations, we present Loom, a new NIC design that moves all per-flow scheduling decisions out of the OS and into the NIC.  The key aspects of Loom's design are 1) a new network policy abstraction: restricted directed acyclic graphs (DAGs), 2) a programmable hierarchical packet scheduler, and 3) a new expressive and efficient OS/NIC interface that enables the OS to precisely control how the NIC performs packet scheduling while still ensuring low CPU utilization.  Loom is the only multiqueue NIC design that is able to efficiently enforce network policy. We find empirically that <PERSON><PERSON> lowers latency, increases throughput, and improves fairness for collocated applications and tenants.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099205", "vector": [], "sparse_vector": [], "title": "BLAS-on-flash: An Efficient Alternative for Large Scale ML Training and Inference?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many large scale machine learning training and inference tasks are memory-bound rather than compute-bound.  That is, on large data sets, the working set of these algorithms does not fit in memory for jobs that could run overnight on a few multi-core processors. This often forces an expensive redesign of the algorithm to distributed platforms such as parameter servers and Spark. We propose an inexpensive and efficient alternative based on the observation that many ML tasks admit algorithms that can be programmed with linear algebra subroutines. A library that supports BLAS and sparseBLAS interface on large SSD-resident matrices can enable multi-threaded code to scale to industrial scale data sets on a single workstation. We demonstrate that not only can such a library provide near in-memory performance for BLAS, but can also be used to write implementations of complex algorithms such as eigensolvers that outperform in-memory (ARPACK) and distributed (Spark) counterparts. Existing multi-threaded in-memory code can link to our library with minor changes and scale to hundreds of Gigabytes of training or inference data at near in-memory processing speeds. We demonstrate this with two industrial scale use cases arising in ranking and relevance pipelines: training large scale topic models and inference for extreme multi-label learning. This suggests that our approach could be an efficient alternative to expensive big-data compute systems for scaling up structurally complex machine learning tasks.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099206", "vector": [], "sparse_vector": [], "title": "Model-Agnostic and Efficient Exploration of Numerical State Space of Real-World TCP Congestion Control Implementations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The significant impact of TCP congestion control on the Internet highlights the importance of testing the correctness and performance of congestion control algorithm implementations (CCAIs) in various network environments. Many CCAI testing questions can be answered by exploring the numerical state space of CCAIs, which is defined by a group of numerical (and nonnumerical) state variables of the CCAIs. However, the current practices for automated numerical state space exploration are either limited by the approximate abstract CCAI models or inefficient due to the large space of network environment parameters and the complicated relation between the CCAI states and network environment parameters. In this paper, we propose an automated numerical state space exploration method, called ACT, which leverages the model-agnostic feature of random testing and greatly improves its efficiency by guiding random testing under the feedback iteratively obtained in a test. Our experiments on five representative Linux TCP CCAIs show that ACT can more efficiently explore a large numerical state space than manual testing, undirected random testing, and symbolic execution based testing, while without requiring any abstract CCAI models. ACT successfully detects multiple implementation bugs and design issues of these Linux TCP CCAIs, including some new bugs and issues not reported before.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099207", "vector": [], "sparse_vector": [], "title": "NetBouncer: Active Device and Link Failure Localization in Data Center Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dongming Bi", "<PERSON>"], "summary": "The availability of data center services is jeopardized by various network incidents. One of the biggest challenges for network incident handling is to accurately localize the failures, among millions of servers and tens of thousands of network devices. In this paper, we propose NetBouncer, a failure localization system that leverages the IP-in-IP technique to actively probe paths in a data center network. NetBouncer provides a complete failure localization framework which is capable of detecting both device and link failures. It further introduces an algorithm for high accuracy link failure inference that is resilient to real-world data inconsistency by integrating both our troubleshooting domain knowledge and machine learning techniques. NetBouncer has been deployed in Microsoft Azure’s data centers for three years. And in practice, it produced no false positives and only a few false negatives so far.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099208", "vector": [], "sparse_vector": [], "title": "Hyperscan: A Fast Multi-pattern Regex Matcher for Modern CPUs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "KyoungSoo Park", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Regular expression matching serves as a key functionality of modern network security applications. Unfortunately, it often becomes the performance bottleneck as it involves compute-intensive scan of every byte of packet payload. With trends towards increasing network bandwidth and a large ruleset of complex patterns, the performance requirement gets ever more demanding. In this paper, we present Hyperscan, a high performance regular expression matcher for commodity server machines. Hyperscan employs two core techniques for efficient pattern matching. First, it exploits graph decomposition that translates regular expression matching into a series of string and finite automata matching. Unlike existing solutions, string matching becomes a part of regular expression matching, eliminating duplicate operations. Decomposed regular expression components also increase the chance of fast DFA matching as they tend to be smaller than the original pattern. Second, Hyperscan accelerates both string and finite automata matching using SIMD operations, which brings substantial throughput improvement. Our evaluation shows that Hyperscan improves the performance of Snort by a factor of 8.7 for a real traffic trace.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099209", "vector": [], "sparse_vector": [], "title": "Riverbed: Enforcing User-defined Privacy Constraints in Distributed Web Services.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Riverbed is a new framework for building privacy-respecting web services. Using a simple policy language, users define restrictions on how a remote service can process and store sensitive data. A transparent Riverbed proxy sits between a user's front-end client (e.g., a web browser) and the back-end server code. The back-end code remotely attests to the proxy, demonstrating that the code respects user policies; in particular, the server code attests that it executes within a Riverbed-compatible managed runtime that uses IFC to enforce user policies. If attestation succeeds, the proxy releases the user's data, tagging it with the user-defined policies. On the server-side, the Riverbed runtime places all data with compatible policies into the same universe (i.e., the same isolated instance of the full web service). The universe mechanism allows Riverbed to work with unmodified, legacy software; unlike prior IFC systems, Riverbed does not require developers to reason about security lattices, or manually annotate code with labels. Riverbed imposes only modest performance overheads, with worst-case slowdowns of 10% for several real applications.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099210", "vector": [], "sparse_vector": [], "title": "Monoxide: Scale out Blockchains with Asynchronous Consensus Zones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptocurrencies have provided a promising infrastructure for pseudonymous online payments.\nHowever, low throughput has significantly hindered the scalability and\nusability of cryptocurrency systems for increasing numbers of users and transactions.\nAnother obstacle to achieving scalability is that every node is required to duplicate the\ncommunication, storage, and state representation of the entire network. In this paper, we introduce theAsynchronous Consensus Zones, which scales\nblockchain system linearly without compromising decentralization or security.\nWe achieve this by running multiple independent and parallel instances of single-chain consensus\n(zones). The consensus happens independently within each zone with minimized\ncommunication, which partitions the workload of the entire network and ensures moderate burden for each\nindividual node as network grows. We proposeeventual atomicityto ensure transaction atomicity\nacross zones, which guarantees the efficient completion of transaction without the overhead of two-phase\ncommit protocol. We also proposeChu-ko-nu miningto ensure the effective mining power in each zone is\nat the same level of the entire network, and makes an attack on any individual zone as hard as that on the entire network. Our experimental results show the effectiveness of our work. On\na test-bed including 1,200 virtual machines worldwide to support 48,000 nodes, our system deliver\n$1,000\\times$ throughput and capacity over Bitcoin and Ethereum network.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099211", "vector": [], "sparse_vector": [], "title": "Pushing the Range Limits of Commercial Passive RFIDs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper asks: “Can we push the prevailing range limits of commercial passive RFIDs?”. Today’s commercial passive RFIDs report ranges of 5-15 meters at best. This constrains RFIDs to be detected only at specific checkpoints in warehouses, stores and factories today, leaving them outside of communication range beyond these spaces. State-of-the-art approaches to improve the range of RFIDs develop new tag hardware that necessarily sacrifices some of the most attractive features of passive RFIDs such as their low cost, small form-factor or the absence of a battery. We present PushID, a system that exploits collaboration between readers to enhance the range of commercial passive RFID tags, without altering the tags whatsoever. PushID uses distributed MIMO to coherently combine signals across geographically separated RFID readers at the tags. In doing so, it resolves the chicken-or-egg problem of inferring the optimal beamforming parameters to beam energy to a tag without any feedback from the tag itself, which needs this energy to respond in the first place. A prototype evaluation of PushID with 8 distributed RFID readers reveals a range of 64-meters to the closest reader, a 7.4×, 1.2× and 1.6× improvement in range compared to state-of-the-art commercial readers and other two schemes [10, 31].", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099212", "vector": [], "sparse_vector": [], "title": "Zeno: Diagnosing Performance Problems with Temporal Provenance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "When diagnosing a problem in a distributed system, it is sometimes necessary to explain the timing of an event—for instance, why a response has been delayed, or why the network latency is high. Existing tools o er some support for this, typically by tracing the problem to a bottleneck or to an overloaded server. However, locating the bottleneck is merely the first step: the real problem may be some other service that is sending traffic over the bottleneck link, or a misbehaving machine that is overloading the server with requests. These off-path causes do not appear in a conventional trace and will thus be missed by most existing diagnostic tools. In this paper, we introduce a new concept we call temporal provenance that can help with diagnosing timing-related problems. Temporal provenance is inspired by earlier work on provenance-based network debugging; however, in addition to the functional problems that can already be handled with classical provenance, it can also diagnose problems that are related to timing. We present an algorithm for generating temporal provenance and an experimental debugger called <PERSON><PERSON>; our experimental evaluation shows that Zeno can successfully diagnose several realistic performance bugs.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099213", "vector": [], "sparse_vector": [], "title": "End-to-end I/O Monitoring on a Leading Supercomputer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>song Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nosayba <PERSON>", "Haidong Lan", "<PERSON><PERSON>", "Jidong Zhai", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents an effort to overcome the complexities of production-use I/O performance monitoring. We design Beacon, an end-to-end I/O resource monitoring and diagnosis system, for the 40960-node Sunway TaihuLight supercomputer, current ranked world No.3. It simultaneously collects and correlates I/O tracing/profiling data from all the compute nodes, forwarding nodes, storage nodes and metadata servers. With mechanisms such as aggressive online+offline trace compression and distributed caching/storage, it delivers scalable, low-overhead, and sustainable I/O diagnosis under production use. Higher-level per-application I/O performance behaviors are reconstructed from system-level monitoring data to reveal correlations between system performance bottlenecks, utilization symptoms, and application behaviors. Beacon further provides query, statistics, and visualization utilities to users and administrators, allowing comprehensive and in-depth analysis without requiring any code/script modification. With its deployment on TaihuLight for around 18 months, we demonstrate Beacon's effectiveness with a collection of real-world use cases for I/O performance issue identification and diagnosis. It has successfully helped center administrators identify obscure design or configuration flaws, system anomaly occurrences, I/O performance interference, and resource under- or over-provisioning problems. Several of the exposed problems have already been fixed, with others being currently addressed. In addition, we demonstrate Beacon's generality by its recent extension to monitor  interconnection networks, another contention point on supercomputers. Finally, both codes and data collected are to be released.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099214", "vector": [], "sparse_vector": [], "title": "dShark: A General, Easy to Program and Scalable Framework for Analyzing In-network Packet Traces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lihua Yuan"], "summary": "Distributed, in-network packet capture is still the last resort for diagnosing network problems. Despite recent advances in collecting packet traces scalably, effectively utilizing pervasive packet captures still poses important challenges. Arbitrary combinations of middleboxes which transform packet headers make it challenging to even identify the same packet across multiple hops; packet drops in the collection system create ambiguities that must be handled; the large volume of captures, and their distributed nature, make it hard to do even simple processing; and the one-off and urgent nature of problems tends to generate ad-hoc solutions that are not reusable and do not scale. In this paper we propose dShark to address these challenges. dShark allows intuitive groupings of packets across multiple traces that are robust to header transformations and capture noise, offering simple streaming data abstractions for network operators. Using dShark on production packet captures from a major cloud provider, we show that dShark makes it easy to write concise and reusable queries against distributed packet traces that solve many common problems in diagnosing complex networks. Our evaluation shows that dShark can analyze production packet traces with more than 10 Mpps throughput on a commodity server, and has near-linear speedup when scaling out on multiple servers.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099215", "vector": [], "sparse_vector": [], "title": "Understanding Lifecycle Management Complexity of Datacenter Topologies.", "authors": ["<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most recent datacenter topology designs have focused on performance properties such as latency and throughput. In this paper, we explore a new dimension, life cycle management, which attempts to capture operational costs of topologies. Specifically, we consider costs associated with deployment and expansion of topologies and explore how structural properties of two different topology families (Clos and expander graphs as exemplified by Xpander) affect these. We also develop a new topology that has the wiring simplicity of Clos and the expandability of expander graphs using the insights from our study.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099216", "vector": [], "sparse_vector": [], "title": "Minimal Rewiring: Efficient Live Expansion for Clos Data Center Networks.", "authors": ["<PERSON><PERSON> Zhao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Clos topologies have been widely adopted for large-scale data center networks (DCNs), but it has been difficult to support incremental expansions for Clos DCNs. Some prior work has claimed that the structure of Clos topologies hinders incremental expansion. We demonstrate that it is indeed possible to design expandable Clos DCNs, and to expand them while they are carrying live traffic, without incurring packet loss. We use a layer of patch panels between blocks of switches in a Clos DCN, which makes physical rewiring feasible, and we describe how to use integer linear programming (ILP) to minimize the number of patch-panel connections that must be changed, which makes expansions faster and cheaper. We also describe a block-aggregation technique that makes our ILP approach scalable. We tested our \"minimal-rewiring\" solver on two kinds of fine-grained expansions using 2250 synthetic DCN topologies, and found that the solver can handle 99% of these cases while changing under 25% of the connections. Compared to prior approaches, this solver (on average) reduces the average number of \"stages\" per expansion from 4 to 1.29, and reduces the number of wires changed by an order of magnitude or more—a significant improvement to our operational costs, and to our exposure (during expansions) to capacity-reducing faults.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099217", "vector": [], "sparse_vector": [], "title": "Slim: OS Kernel Support for a Low-Overhead Container Overlay Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Containers have become the de facto method for hosting large-scale distributed applications. Container overlay networks are essential to providing portability for containers, yet they impose significant overhead in terms of throughput, latency, and CPU utilization. The key problem is a reliance on packet transformation to implement network virtualization. As a result, each packet has to traverse the network stack twice in both the sender and the receiver’s host OS kernel. We have designed and implemented Slim, a low-overhead container overlay network that implements network virtualization by manipulating connection-level metadata. Our solution maintains compatibility with today’s containerized applications. Evaluation results show that <PERSON> improves the throughput of an in-memory key-value store by 66% while reducing the latency by 42%. <PERSON> reduces the CPU utilization of the in-memory key-value store by 54%. <PERSON> also reduces the CPU utilization of a web server by 28%-40%, a database server by 25%, and a stream processing framework by 11%.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "3099218", "vector": [], "sparse_vector": [], "title": "Stardust: Divide and Conquer in the Data Center Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Building scalable data centers, and network devices that fit within these data centers, has become increasingly hard. With modern switches pushing at the boundary of manufacturing feasibility, being able to build suitable, and scalable network fabrics becomes of critical importance. We introduce Stardust, a fabric architecture for data center scale networks, inspired by network-switch systems. Stardust combines packet switches at the edge and disaggregated cell switches at the network fabric, using scheduled traffic.  Stardust is a distributed solution that attends to the scale limitations of network-switch design, while also offering improved performance and power savings compared with traditional solutions. With ever-increasing networking requirements, Stardust predicts the elimination of packet switches, replaced by cell switches in the network, and smart network hardware at the hosts.", "published": "2019-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}]