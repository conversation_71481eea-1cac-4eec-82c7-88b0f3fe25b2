[{"primary_key": "676473", "vector": [], "sparse_vector": [], "title": "UWBeacon: Lighting up Centimeter-Level Underwater Positioning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Underwater positioning plays a key role in many underwater operations. This paper presents the design, implementation, and evaluation of UWBeacon, a centimeter-level visible light-based underwater positioning system. UWBeacon consists of LED beacons as the light signal transmitter and a camera-based receiver as the target. To address unique challenges in underwater environment such as limited visibility and strong ambient interference, we exploit a novel design that utilizes polarized lights of different colors with different polarization angles for background subtraction. UWBeacon is implemented with commercial-off-the-shelf LEDs and cameras. Comprehensive experiments conducted in various real underwater environments show that UWBeacon can achieve a mean positioning error below 6 cm and an orientation error below 1.5° at a distance of 10 meters.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690689"}, {"primary_key": "676474", "vector": [], "sparse_vector": [], "title": "UAV-Assisted Integrated Sensing and Communication for Emergency Rescue Activities Based on Transfer Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Mao", "<PERSON><PERSON>", "<PERSON>", "Yusheng Ji", "<PERSON>"], "summary": "Joint task scheduling and resource allocation for unmanned aerial vehicle (UAV)-assisted integrated sensing and communication (ISAC) in emergency rescue activities has become an essential and challenging problem. However, the existing works have only considered such a problem for standalone UAV networks without considering the cooperation between UAVs and ground base stations (BSs), nor have they considered the uncertainty in terms of the availability of BSs due to damage/reconstruction in disaster events. In this paper, we consider a novel post-disaster UAV-assisted ISAC system where the UAVs are used to supplement the networking capacity of out-of-service ground BSs while using their radio signals for sensing. We apply transfer learning with deep reinforcement learning (DRL) to learn task scheduling and resource allocation strategies that can rapidly adapt to uncertainty in the environment. Experimental results show that the proposed algorithm outperforms the state-of-the-art in both communication and sensing performance and convergence speed. Moreover, the transfer learning-based DRL shows faster convergence and better robustness when the availability of BSs suddenly changes.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698220"}, {"primary_key": "676475", "vector": [], "sparse_vector": [], "title": "Enhancing Throughput in 5G-NTN through Early RLC Layer Retransmissions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Lower layer retransmission schemes in 5G Non-Terrestrial Networks (5G-NTN), in particular Hybrid Automatic Repeat Request (HARQ) from the Medium Access Control (MAC) layer, are severely hampered by the large Round Trip Times (RTTs) imposed by satellite components. In this demonstration, we show that enabling Radio Link Control (RLC) layer retransmissions can significantly increase throughput without additional processing complexity. Using the OpenAirInterface (OAI) 5G-NTN suite, we showcase the effectiveness of RLC Acknowledged Mode (AM) retransmissions in facilitating early recovery of lost packets and maintaining reasonable Quality of Service (QoS), even in the absence of HARQ feedback from the MAC layer.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698842"}, {"primary_key": "676476", "vector": [], "sparse_vector": [], "title": "Fine-grained Textile Moisture Sensing with Commodity UWB.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "RF sensing has attracted a tremendous amount of attention and achieved promising progress in applications such as human gesture recognition and vital sign monitoring. This paper delves into sensing the moisture level of fabrics---an important metric for smart clothing, wound care, and textile manufacturing. We present TMSense, an innovative contact-free fabric moisture measurement system that leverages UWB signals for sensing. We introduce a set of signal processing methods to tackle the challenge of weak fabric reflections that can be easily overwhelmed by noise interference. Additionally, we adopt a model-driven approach to get rid of reliance on extensive datasets. By exploiting the changes in the dielectric properties induced by moisture in textile fabrics, we establish a theoretical model that bridges the characteristics of the RF signal with the moisture content. Based on this model, we successfully eliminate interfering factors such as target-device distance and target attributes through delicate signal processing and parameter calibration. Comprehensive experiments conducted under various conditions, including different materials, sample forms, and parameter settings, demonstrate an impressively low median error of 1.4% on textile moisture measurements, outperforming commodity moisture sensors on the market.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690679"}, {"primary_key": "676477", "vector": [], "sparse_vector": [], "title": "Robust Respiration Monitoring Under Body Motion Interference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Badii Jouaber", "<PERSON><PERSON> Zhang"], "summary": "In recent years, wireless signals have been extensively investigated for contactless human respiration monitoring. However, most wireless sensing systems encounter challenges when the target exhibits body movements. In this demo, we present a solution to mitigate the impact of body motion on contactless respiration monitoring. By employing novel signal processing techniques, body motion can be first estimated and subsequently eliminated from the signal reflected signal by the chest. We prototype the proposed system using a MIMO mmWave radar. Evaluations in real-world environments demonstrate the effectiveness of the solution.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698835"}, {"primary_key": "676478", "vector": [], "sparse_vector": [], "title": "MSense: Boosting Wireless Sensing Capability Under Motion Interference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Wireless signals have been widely utilized for human sensing. However, wireless sensing systems face a fundamental limitation, i.e., the wireless device must keep static during the sensing process. Also, when sensing fine-grained human motions such as respiration, the human target is required to stay stationary. This is because wireless sensing relies on signal variations for sensing. When device is moving or human body is moving, the signal variation caused by the target area (e.g., chest for respiration sensing) is mixed with the signal variation induced by device or other body parts, failing wireless sensing. In this paper, we propose MSense, a general solution to deal with motion interference from wireless device and/or human body, moving wireless sensing one step forward towards real-life adoption. We establish the sensing model by taking both device motion and interfering body motion into consideration. By extracting the effect of body and device motions through pure signal processing, the motion interference can be removed to achieve accurate target sensing. Comprehensive experiments demonstrate the effectiveness of the proposed scheme. The achieved solution is general and can be applied to different sensing tasks involving both periodic and aperiodic motions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649350"}, {"primary_key": "676479", "vector": [], "sparse_vector": [], "title": "Rethinking Process Management for Interactive Mobile Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Zhenhua Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern mobile systems are featured by their increasing interactivity with users, which however is accompanied by a severe side effect---users constantly suffer from slow UI responsiveness (SUR). To date, the community have limited understandings of this issue for the challenges of comprehensively measuring SUR events on massive mobile devices. As a major Android phone vendor, in this paper we close the knowledge gap by conducting the first large-scale, long-term measurement study on SUR with 47M devices. Our study identifies the critical factors that lead to SUR from the perspectives of device, system, application, and app market. Most importantly, we note that the largest root cause lies in the wide existence of \"hogging\" apps, which persistently occupy an unreasonable amount of system resources by leveraging the optimistic design of Android process management. We have built on the insights to remodel Android process states by fully considering their time-sensitive transitions and the actual behaviors of processes, with remarkable real-world impact---the occurrences of SUR are reduced by 60%, together with 10.7% saving of battery consumption.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649357"}, {"primary_key": "676480", "vector": [], "sparse_vector": [], "title": "QLSel: Demonstrating Efficient High-Fidelity Link Selection for Quantum Networks in the Wild.", "authors": ["Tingting Li", "Zim<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As an emerging technology, quantum networks have the potential to revolutionize secure communication and data transmission technologies. In the Noisy Intermediate-Scale Quantum (NISQ) era, quantum noise causing low fidelity remains challenging in quantum networks. In this paper, we propose QLSel, an efficient selection algorithm for the high-fidelity link in the wild without assumptions about the fidelity distribution. We design the tailored link exploration strategy and link selection probability based on the coefficient of variation and Thompson sampling, to cope with the exploration-exploitation trade-off dilemma in the Multi-Armed Bandit (MAB) problem (for problem modeling). Extensive experiments demonstrate that QLSel significantly outperforms existing representative methods. Our codes and video are available at https://github.com/Secbrain/QLSel.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698853"}, {"primary_key": "676481", "vector": [], "sparse_vector": [], "title": "AutoDroid: LLM-powered Task Automation in Android.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile task automation is an attractive technique that aims to enable voice-based hands-free user interaction with smartphones. However, existing approaches suffer from poor scalability due to the limited language understanding ability and the non-trivial manual efforts required from developers or endusers. The recent advance of large language models (LLMs) in language understanding and reasoning inspires us to rethink the problem from a model-centric perspective, where task preparation, comprehension, and execution are handled by a unified language model. In this work, we introduce AutoDroid, a mobile task automation system capable of handling arbitrary tasks on any Android application without manual efforts. The key insight is to combine the commonsense knowledge of LLMs and domain-specific knowledge of apps through automated dynamic analysis. The main components include a functionality-aware UI representation method that bridges the UI with the LLM, exploration-based memory injection techniques that augment the app-specific domain knowledge of LLM, and a multi-granularity query optimization module that reduces the cost of model inference. We integrate AutoDroid with off-the-shelf LLMs including online GPT-4/GPT-3.5 and on-device Vicuna, and evaluate its performance on a new benchmark for memory-augmented Android task automation with 158 common tasks. The results demonstrated that AutoDroid is able to precisely generate actions with an accuracy of 90.9%, and complete tasks with a success rate of 71.3%, outperforming the GPT-4-powered baselines by 36.4% and 39.7%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649379"}, {"primary_key": "676482", "vector": [], "sparse_vector": [], "title": "Scalable and Sustainable Video Analytics on Edge using Sensor Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sa<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The proliferation of video analytics in applications like autonomous driving, traffic surveillance, and teleoperated vehicles requires on-premise (on edge) execution of deep learning models to meet latency requirements and curb bandwidth usage by limiting frequent offloading of inference tasks. However, constrained by the compute and power availability on the edge, a cheaper model is typically deployed. These shallower models have two major associated problems: 1) using the same model for all cameras/vehicles gives inconsistent accuracy, and 2) trained models are prone to data drift.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695902"}, {"primary_key": "676483", "vector": [], "sparse_vector": [], "title": "Perceptual-Centric Image Super-Resolution using Heterogeneous Processors on Mobile Devices.", "authors": ["<PERSON>", "Xiangyu Yin", "<PERSON> Gu", "<PERSON>"], "summary": "Image super-resolution (SR) is widely used on mobile devices to enhance user experience. However, neural networks used for SR are computationally expensive, posing challenges for mobile devices with limited computing power. A viable solution is to use heterogeneous processors on mobile devices, especially the specialized hardware AI accelerators, for SR computations, but the reduced arithmetic precision on AI accelerators can lead to degraded perceptual quality in upscaled images. To address this limitation, in this paper we present SR For Your Eyes (FYE-SR), a novel image SR technique that enhances the perceptual quality of upscaled images when using heterogeneous processors for SR computations. FYE-SR strategically splits the SR model and dispatches different layers to heterogeneous processors, to meet the time constraint of SR computations while minimizing the impact of AI accelerators on image quality. Experiment results show that FYE-SR outperforms the best baselines, improving perceptual image quality by up to 2×, or reducing SR computing latency by up to 5.6× with on-par image quality.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690698"}, {"primary_key": "676484", "vector": [], "sparse_vector": [], "title": "Energy-based Active Learning for Bringing Beam-induced Domain Gap for 3D Object Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In many real-world applications, 16-beam LiDAR-based 3D object detection (3DOD) is indispensable in scene understanding. However, the absence of well-labeled large-scale 16-beam LiDAR datasets impedes the development of these 3DOD methods. To avoid annotation costs in developing datasets, we proposed an energy-based active learning method for cross-beam domain adaptation, which effectively transfers the knowledge from the existing well-labeled 64-beam counterpart. Specifically, the cross-beam domain gap between the source (64-beam) and the target (16-beam) domain is reduced by aligning the deep features based on an energy-based feature-matching loss term during training. Moreover, the proposed energy-based active learning method enables the sampling strategy to shed light on selecting the most valuable 16-beam target samples to be manually labeled, which are then added to the training set. Experimental results show that our method can effectively transfer the knowledge from the 64-beam domain to the 16-beam one, and successfully learns a high-performance 16-beam 3DOD model with only a small portion of unlabeled data to annotate.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694723"}, {"primary_key": "676485", "vector": [], "sparse_vector": [], "title": "Hidden WiFi Camera Localization via Signal Propagation Path Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hidden WiFi cameras pose significant privacy threats, necessitating effective localization methods. In this work, we introduce CamLoPA, a system designed for the detection and localization of WiFi cameras. CamLoPA achieves this in just 45 seconds of user walking. It begins by analyzing the causal relationship between WiFi traffic and user movement to identify the presence of a snooping camera. Upon detection, CamLoPA utilizes a novel azimuth location model based on WiFi signal propagation path analysis to localize the hidden camera. Comprehensive evaluations demonstrate that CamLoPA can accurately and swiftly detect and localize snooping WiFi cameras with minimal constraints.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698834"}, {"primary_key": "676486", "vector": [], "sparse_vector": [], "title": "ReplayAR: A Tool for Visual Evaluation of Mixed Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In world-locked mixed reality (MR), virtual content is locked in place with respect to the real world. Pose estimation is a key component to create world-locked MR experiences by estimating the device's position and orientation in order to render the virtual content accordingly. Current methods of evaluating world-locked MR include user studies, which are time consuming, and absolute trajectory error (ATE), which does not directly represent what is shown on the user's display. In this work, we propose ReplayAR, a tool that can replay user movement traces and output the corresponding visualizations (renderings) of the MR display. ReplayAR can be used to compare renderings from different MR pose estimation methods side by side, using our proposed Visual Difference metric. We implemented ReplayAR on a Hololens 2 MR headset and used it to evaluate open and closed-source pose estimation methods on standard datasets and our own collected traces. The results suggest that Visual Difference better reflects what is shown on the MR display compared to ATE. We hope that ReplayAR can encourage reproducible evaluation of world-locked MR, and towards this, we release the open-source code.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696213"}, {"primary_key": "676487", "vector": [], "sparse_vector": [], "title": "Environmental-aware Reinforcement Learning-based Scheduler for Trustworthy 6G in the Factory Floor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "6G networks will play a crucial role in Industry 4.0 advancing further smart manufacturing and trustworthy communication. This paper introduces a reinforcement learning scheduler that considers environmental knowledge designed to improve reliability and security aspects of trustworthiness by favoring to serve nodes when in line-of-sight. We evaluate the performance in a factory floor scenario for two different heights of remote-radio heads and various blockage densities. The results show that the proposed method outperforms traditional schedulers such as round robin and proportional fair in terms of reliability, availability and fairness enhancing thus reliability and security aspects of trustworthiness when the environment is prone to a mixture of line-of-sight and non-line-of-sight.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696728"}, {"primary_key": "676488", "vector": [], "sparse_vector": [], "title": "Towards Accurate Sleep Monitoring: Detecting Bed Events Using Millimeter-Wave Technology.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Sanjib Sur"], "summary": "We propose a millimeter-wave (mmWave) wireless signal-based sleep monitoring system aimed at providing information about a person's sleep by detecting sleep events, such as bed entry and exit times, as well as the duration of bed stay. It overcomes the limitations of existing vision-based systems by operating in low-light conditions without invading privacy. It uses spatial-temporal information and signal processing techniques to determine the duration, and our preliminary results indicate that our system can accurately detect bed events.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697441"}, {"primary_key": "676489", "vector": [], "sparse_vector": [], "title": "28 GHz Phased Array Interference Measurements and Modeling for a NOAA Microwave Radiometer in Manhattan.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Yonghua Wu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A microwave radiometer (MWR) at NOAA-CESSRST in Manhattan, NYC has experienced interference from nearby sources operating in the 5G FR2 n257 band (26.50--29.50 GHz). In this poster, we produce interference using a mobile 28 GHz IBM Phased Array Antenna Module (PAAM). The mobile PAAM leverages a software-defined radio which offers flexibility in varying center frequency, modulation, gain, bandwidth, time schedule, and more. In this poster, we show preliminary experiments which successfully created controlled interference to a MWR's 28 GHz channel which lead to distortion in some of the MWR final products, such as water vapor profile. We transmitted a 10 MHz bandwidth OFDM signal with varying amplitude, observing the highly sensitive MWR voltage response to fractional dB increments of the transmitter gain. The mobile PAAM is characterized in an anechoic chamber and MWR measurements are taken at various azimuth angles to help estimate the MWR antenna pattern. Future work will develop a Spectrum Consumption Model to help enable coexistence of MWRs and Beyond-5G networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697463"}, {"primary_key": "676490", "vector": [], "sparse_vector": [], "title": "A Resource Efficient System for On-Smartwatch Audio Processing.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While audio data shows promise in addressing various health challenges, there is a lack of research on on-device audio processing for smartwatches. Privacy concerns make storing raw audio and performing post-hoc analysis undesirable for many users. Additionally, current on-device audio processing systems for smartwatches are limited in their feature extraction capabilities, restricting their potential for understanding user behavior and health. We developed a real-time system for on-device audio processing on smartwatches, which takes an average of 1.78 minutes (SD = 0.07 min) to extract 22 spectral and rhythmic features from a 1-minute audio sample, using a small window size of 25 milliseconds. Using these extracted audio features on a public dataset, we developed and incorporated models into a watch to classify foreground and background speech in real-time. Our Random Forest-based model classifies speech with a balanced accuracy of 80.3%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698866"}, {"primary_key": "676491", "vector": [], "sparse_vector": [], "title": "DRAGON: A DRL-based MIMO Layer and MCS Adapter in Open RAN 5G Networks.", "authors": ["Qing An", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the rapidly evolving field of wireless communication, Multiple Input Multiple Output (MIMO) networks have emerged as a pivotal technology, offering enhanced data rates and spectral efficiency by leveraging multiple antennas at both the transmitter and receiver. The introduction of Open Radio Access Network (O-RAN) architecture has further revolutionized this domain, enabling greater flexibility, scalability, and interoperability through its open interfaces and software-defined approach. This paper presents DRAGON, a novel Deep Reinforcement Learning (DRL)-based framework for joint Layer and Modulation and Coding Scheme (MCS) selection, tailored for downlink single-user MIMO networks under the O-RAN framework. Our approach is designed to be highly scalable, capable of efficiently managing a large number of configuration options in one-shot prediction, including up to 25 MCS and 4 layer choices. The proposed solution has been rigorously evaluated using an O-RAN-based simulation environment, demonstrating up to an 18% performance improvement over the state-of-the-art (SOTA) methods and achieving a best throughput of 87.4% when compared to the collected ground-truth dataset. Furthermore, our method supports real-time prediction, making it viable for practical deployment. In addition to these advancements, we explore the potential integration of our DRL-based solution with real-world platforms and discuss the extension of our approach to handle multi-user (MU) MIMO scenarios, paving the way for broader applications in next-generation wireless networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701549"}, {"primary_key": "676492", "vector": [], "sparse_vector": [], "title": "Reimagining Reminders to Increase Medication Adherence in Older Adults.", "authors": ["In Baek", "<PERSON>"], "summary": "Nonadherence to medication is a growing problem across all demographics, particularly older adults who are the most likely to be taking multiple medications. Nonadherence has yet to be solved by digital health technologies. Forgetfulness is one of the main contributors to unintentional nonadherence, which hundreds of medication management apps and devices seek to address by generating time-based reminders. However, most medications are prescribed to be taken within a time frame, such as \"in the morning\" or \"before breakfast,\" rather than at a specific hour. Similarly, many components of daily living occur within time frames rather than exact times. Timed reminders do not always correlate with prescription instructions nor daily life; this mismatch may lead to a patient's lack of success in reducing forgetfulness. In domains other than medication prescribed \"as-needed,\" instead of timed reminders, have been successfully deployed. Borrowing this notion may lead to medication reminders that are accepted and responded to, reducing forgetfulness and leading to healthier aging in the home.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698116"}, {"primary_key": "676493", "vector": [], "sparse_vector": [], "title": "Feasibility of Smartphones for Accessible, Noninvasive Micronutrient Assessment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Micronutrient imbalance is a global issue, and its detection is invasive and expensive. Despite being largely preventable, imbalances of one or more micronutrients is pervasive and has major downstream health effects [1]. Women, children, and underserved populations in particular bear the greatest burden of micronutrient imbalance [4--6]. However, the true scope of this issue is often unseen and unaddressed because of the barriers to accessible micronutrient status assessment [2]. Status assessment of micronutrients is often done via indirect, subjective dietary logs, in-person clinical examinations, or complex analyses on blood (e.g. liquid chromatography-coupled mass spectrometry). While valuable, these assessments are expensive, flawed, and burdensome on the patient and the clinician.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698861"}, {"primary_key": "676494", "vector": [], "sparse_vector": [], "title": "HORCRUX: Accurate Cross Band Channel Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advancement in Frequency Domain Duplexing (FDD) enables wireless systems to use different frequency bands for uplink and downlink communication without explicit channel feedback information. The current state-of-the-art approaches either estimate the underlying variables in the uplink channel or use an artificial neural network architecture to estimate the downlink channel from the uplink channel. However, such techniques fail to perform accurately in multipath-rich environments and environments unseen during training. This paper presents HORCRUX, a physics-based machine learning system that can be generalized and scaled to any environment while predicting downlink channels with high accuracy and applies to single-antenna and MIMO systems. Our approach uses multiple neural networks, trained on the standard wireless channel model, firstly to divide the uplink channel into smaller sub-channels and secondly to generate coarse estimates for the variables for each of the underlying sub-channels. Finally, we use an efficient and fast optimization framework to get fine-tuned variable estimates to predict the downlink channel. We implement our system using software-defined radios. Our evaluations show that HORCRUX performs ~8 dB better than state of the art in downlink channel prediction accuracy in diverse wireless environments. 1", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649343"}, {"primary_key": "676495", "vector": [], "sparse_vector": [], "title": "ZenseTag: An RFID assisted Twin-Tag Single Antenna COTS Sensor Interface.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sensing allows us to interact with and quantify the natural world. Despite the advancements in sensor versatility, sensing systems still suffer from limited adoption due to their dependence on batteries, complex interfaces, energy-harvesting modules, and readout latency. To address these challenges, we present ZenseTag --- a miniaturized, sticker-like platform that can interface commercial sensors directly with COTS RFID tags. ZenseTag exploits the impedance response of COTS sensors to the measured stimulus at Radio Frequencies, tuned to the UHF RFID band. It combines reliable hardware realization of differential analog sensing with robust software for accurate, low-latency sensor readouts, even in the presence of multipath effects.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698850"}, {"primary_key": "676496", "vector": [], "sparse_vector": [], "title": "Maximizing the Capabilities of Tiny Speech Foundation Models in a Privacy Preserving Manner.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice assistive technologies have given rise to extensive privacy and security concerns. In this paper we investigate whether robust automatic speech recognition (ASR) can be done in a privacy preserving manner on resource constrained devices. For inference, speech recognition systems rely on cloud service providers that host state of the art (SOTA) foundation models (FM) demanding high memory usage and computation complexity. Considering the infeasibility to run these SOTA FM on-device, users are forced to offload their sensitive content to cloud for better inference compromising privacy. Sanitization can be done on speech content at the edge keeping in mind that it has to avoid compromising the ASR accuracy. Our solution is based on the observation that although tiny on-device FM cannot perform robust ASR, they are powerful enough to mask the sensitive segments in speech. Given this observation, we introduce SpeechGuard - a privacy-preserving, hybrid edge-cloud ASR inference engine that is compute inexpensive. We present a novel times-tamp based entity filtering mechanism at the edge and a corresponding confidence score based recovery approach. Our results denote that SpeechGuard offers ASR accuracy and privacy guarantee comparable to existing SOTA FM, while achieving significant speedup and reduction in memory usage.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697457"}, {"primary_key": "676497", "vector": [], "sparse_vector": [], "title": "One-Shot Localization with Random Wavefronts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The next generation of wireless networks will utilize highly directional beams to overcome the path loss at high frequencies, requiring angle inference during link establishment. Furthermore, the integration of location-based services into the wireless infrastructure is rapidly increasing, bringing in a significant demand for an integrated fast localization scheme. In this work, we present a first-of-its-kind one-shot angular localization method that is carried out with a re-configurable architecture that unlocks ISAC functionality. Specifically, we use an electrically tunable metasurface with broadband response to generate wavefronts that are randomized across the angular space with diverse wideband amplitude and phase observations, corresponding to a collection of angle-unique one-shot beacons. Our results show down to 0.26° mean absolute error at 20 dB SNR, an order of magnitude improvement over the recently proposed one-shot solutions based on leaky-wave antennas (LWAs), in addition to having wider area coverage and less stringent bandwidth requirements.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698225"}, {"primary_key": "676498", "vector": [], "sparse_vector": [], "title": "Twinning Commercial Network Traces on Experimental Open RAN Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Salvatore D&apos;Oro", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While the availability of large datasets has been instrumental to advance fields like computer vision and natural language processing, this has not been the case in mobile networking. Indeed, mobile traffic data is often unavailable due to privacy or regulatory concerns. This problem becomes especially relevant in Open Radio Access Network (RAN), where artificial intelligence can potentially drive optimization and control of the RAN, but still lags behind due to the lack of training datasets. While substantial work has focused on developing testbeds that can accurately reflect production environments, the same level of effort has not been put into twinning the traffic that traverse such networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697320"}, {"primary_key": "676499", "vector": [], "sparse_vector": [], "title": "Helping Autonomous Vehicles Maneuver Traffic Anomalies using UWB.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a UWB beacon to be installed on trucks carrying unusual loads to warn approaching autonomous vehicles (AVs) of unconventional cargo. We propose several approaches, including one where the AV only receives UWB messages. We plan to use a QR code to provide secure communication between the AV and the cargo. The proposed system generalizes to static and dynamic traffic anomalies.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697440"}, {"primary_key": "676500", "vector": [], "sparse_vector": [], "title": "Demo: Realtime Neural Whittle Indexing for Scalable Service Guarantees in NextG Cellular Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work presents Windex, a novel light weight whittle index network-driven realtime scheduler for scalable service guarantees in NextG cellular networks. Windex addresses the resource allocation challenge in NextG cellular radio access networks (RAN), where resources must be shared among diverse user applications, each requiring guarantees on throughput and service regularity, taking into account service guarantees, channel quality, and system load. Implemented in a real time intelligent controller (RIC), and evaluating across standardized 3GPP service classes, we demonstrate the least service violations compared to state-of-the-art systems using over-the-air channel traces on a 5G testbed.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3700034"}, {"primary_key": "676501", "vector": [], "sparse_vector": [], "title": "Artificial Intelligence Control Plane for Deterministic Networks Proof-of-Concept.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents the design and implementation of an Artificial Intelligence Control Plane (AICP) for deterministic networks, emphasizing the Proof-of-Concept (PoC) demonstration. The AICP framework integrates AI, digital twin technology, and real-time telemetry to manage complex network environments, ensuring reliable and low-latency communication. The PoC showcases the practical viability of the AICP by dynamically adapting to varying network demands and maintaining stringent to the Key Performance Indicator (KPI)s of the service. Extensive testing and real-world simulations highlight the framework's potential to enhance the efficiency and resilience of industrial communication networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696725"}, {"primary_key": "676502", "vector": [], "sparse_vector": [], "title": "SliceGuard: Secure and Dynamic 5G RAN Slicing with WebAssembly.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "5G enables diverse services through network slicing, allowing multiple virtual networks to share physical infrastructure. However, efficiently managing resources across slices is challenging. This demo presents SliceGuard, a two-level scheduling system that leverages WebAssembly (Wasm) to allow slice owners to run customized schedulers in a secure, platform-independent environment, while the network operator manages inter-slice resource allocation. We demonstrate dynamic slicing for cloud gaming over 5G and show how Wasm enables real-time scheduler updates and fault isolation. This approach enhances flexibility, security, and customization for private 5G networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698862"}, {"primary_key": "676503", "vector": [], "sparse_vector": [], "title": "Representation Similarity: A Better Guidance of DNN Layer Sharing for Edge Computing without Training.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Edge computing has emerged as an alternative to reduce transmission and processing delay and preserve privacy of the video streams. However, the ever-increasing complexity of Deep Neural Networks (DNNs) used in video-based applications (e.g. object detection) exerts pressure on memory-constrained edge devices. Model merging is proposed to reduce the DNNs' memory footprint by keeping only one copy of merged layers' weights in memory. In existing model merging techniques, (i) only architecturally identical layers can be shared; (ii) requires computationally expensive retraining in the cloud; (iii) assumes the availability of ground truth for retraining. The re-evaluation of a merged model's performance, however, requires a validation dataset with ground truth, typically runs at the cloud. Common metrics to guide the selection of shared layers include the size or computational cost of shared layers or representation size. We propose a new model merging scheme by sharing representations (i.e., outputs of layers) at the edge, guided by representation similarity S. We show that S is extremely highly correlated with merged model's accuracy with Pearson Correlation Coefficient |r| > 0.94 than other metrics, demonstrating that representation similarity can serve as a strong validation accuracy indicator without ground truth. We present our preliminary results of the newly proposed model merging scheme with identified challenges, demonstrating a promising research future direction.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695903"}, {"primary_key": "676504", "vector": [], "sparse_vector": [], "title": "Poster: Envisioning a UWB-based Local Human-Machine Interface.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we present a secure Internet-of-Things framework enabled by UWB, which allows users to wirelessly interact with any public and home appliances in a more secure, seamless and efficient manner. we envision an ecosystem of appliances and mobile devices equipped with UWB transceivers, where the appliances are capable of imposing location-based access control in precise and configurable zones and ensuring security against eavesdropping and hijacking by malicious attackers. We propose a location-based access control algorithm that is robust against range spoofing. Further, we propose the network and application layer protocols that enable one generic user-end application to access the control interface of any appliances and adapt to context.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697464"}, {"primary_key": "676505", "vector": [], "sparse_vector": [], "title": "NaviEyes: A Rapid Shopping Navigation System with On-Sale Purchase Planning Based on IoT Localization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper designs and implements a rapid shopping navigation system, called NaviEyes, to minimize shopping time and maximize purchasing success ratio for hypermarket customers through Internet of Things (IoT) localization. The NaviEyes system can provide a shopping trip with the shortest total moving time to purchase all desired products according the crowd density of each passageway/area. In addition, NaviEyes can plan the proper shopping order and fast moving paths between selected on-sale products to minimize the purchasing risk (i.e., one or more selected on-sale products sold out) through avoiding taking long moving time to purchase a selected product with sufficient instances. An Android-based prototype with video-based indoor localization is implemented to verify the feasibility and performance of our NaviEyes system.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697423"}, {"primary_key": "676506", "vector": [], "sparse_vector": [], "title": "MetaBioLiq: A Wearable Passive Metasurface Aided mmWave Sensing Platform for BioFluids.", "authors": ["Baicheng Chen", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human external biofluid (e.g., sweat, urine) contains vast health data that is readily harvestable. Currently, wearable sweat sensors require an electrochemical-based approach that is used in single use, creating environmental pollution as people track their exercise in the wild. Moreover, such solution relies on a battery-powered design, which brings battery health and thermal related issues. We present MetaBioLiq, a 3D printed wireless-readable sweat sensing system that offers continuous monitoring, featuring completely passive, environmentally friendly, and easily accessible. MetaBioLiq is developed upon sweat liquid's resonance upon high frequency RF interaction, with different sweat content driving RF resonance characteristics. To activate such resonance, we design 3D PolyLactic Acid (PLA) structures that capture e-field energy from the air, and tunneling it to the sweat. Once the resonance effect occurs, we analyze return signal from a wireless RF receiver to decouple the sweat's resonance. Lastly, we evaluate MetaBioLiq's performance with 24 artificial sweat samples containing different levels of glucose, electrolytes, and fat. MetaBioLiq proves its effectiveness with 95% liquid level detection performance, and 96% sweat liquid identification performance. We further investigate MetaBioLiq's robustness and reliability, as well as limitations. Overall, MetaBioLiq shows promising results to expand the realm of mobile continuous sensing to microscopic realm untangible in the past.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690687"}, {"primary_key": "676507", "vector": [], "sparse_vector": [], "title": "Resilient Massive Access assisted ISAC in Space-Air-Ground Integrated Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Integrated sensing and communication (ISAC) and space-air-ground integrated networks (SAGIN) have been considered as key technologies of 6G. The challenge of achieving ISAC in uplink massive access scenarios within the SAGIN has become a major research topic. This paper introduces the Resilient Massive Access (RMA) protocol, which deeply integrates S-ALOHA and NOMA, effectively enhancing the system's access success probability. Additionally, a cascaded uplink detection algorithm based on LS and MUSIC (MUSIC-NOMA-TSA) is proposed, enabling signal decoding and target sensing even in the presence of collisions at the receiver. Simulation results demonstrate that, compared to traditional algorithms, the proposed algorithm not only achieves more accurate signal decoding and target sensing but also significantly improves the access success probability.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698230"}, {"primary_key": "676508", "vector": [], "sparse_vector": [], "title": "Pushing Wireless Charging from Station to Travel.", "authors": ["<PERSON><PERSON>", "Bozhong Yu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Pan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless charging has achieved promising progress in recent years. However, the severe bottlenecks are the small charging range and poor flexibility. This paper presents ChargeX to enable smart and long-range wireless charging for small mobile devices. ChargeX incorporates emerging smart metasurface into the magnetic resonance coupling-based wireless charging to extend the charging range and accommodates the mobility of charging device. Unlike previous endeavors in metasurface-assisted wireless charging that focused on simulation, ChargeX makes efforts across software and hardware to meet three crucial requirements for a practical wireless charging system: (i) realize high-freedom and accurate metasurface control under the premise of low loss; (ii) obtain real-time feedback from the receiver and make effective manipulation for transmitted magnetic flux; and (iii) generate a proper AC signal source at the desired frequency band. We developed a prototype of ChargeX, and evaluated its performance through controlled experiments and real-world phone charging. Extensive experiments demonstrate the great potential of ChargeX for long-range and flexible wireless charging with a compact receiver design.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649346"}, {"primary_key": "676509", "vector": [], "sparse_vector": [], "title": "Exploring the Feasibility of Remote Cardiac Auscultation Using Earphones.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The elderly over 65 accounts for 80% of COVID deaths in the United States. In response to the pandemic, the federal, state governments, and commercial insurers are promoting video visits, through which the elderly can access specialists at home over the Internet, without the risk of COVID exposure. However, the current video visit practice barely relies on video observation and talking. The specialist could not assess the patient's health conditions by performing auscultations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649366"}, {"primary_key": "676510", "vector": [], "sparse_vector": [], "title": "ORANSlice: An Open Source 5G Network Slicing Platform for O-RAN.", "authors": ["<PERSON>", "Salvatore D&apos;Oro", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Davide <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network slicing allows Telecom Operators (TOs) to support service provisioning with diverse Service Level Agreements (SLAs). The combination of network slicing and Open Radio Access Network (RAN) enables TOs to provide more customized network services and higher commercial benefits. However, in the current Open RAN community, an open-source end-to-end slicing solution for 5G is still missing. To bridge this gap, we developed ORANSlice, an open-source network slicing-enabled Open RAN system integrated with popular open-source RAN frameworks. ORANSlice features programmable, 3GPP-compliant RAN slicing and scheduling functionalities. It supports RAN slicing control and optimization via xApps on the near-real-time RAN Intelligent Controller (RIC) thanks to an extension of the E2 interface between RIC and RAN, and service models for slicing. We deploy and test ORANSlice on different O-RAN testbeds and demonstrate its capabilities on different use cases, including slice prioritization and minimum radio resource guarantee.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701544"}, {"primary_key": "676511", "vector": [], "sparse_vector": [], "title": "Real-time Wideband Software-defined Radio with Python Programmability based on RFSoC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Next-generation wireless networks necessitate large signal bandwidth to support the growing demands of high data rates, which poses significant challenges in the design of real-time radio platforms. We demonstrate SPEAR, a realtime wideband software-defined radio (SDR) utilizing the Xilinx RFSoC ZCU216 evaluation board. SPEAR leverages a customized \"Streaming Direct Memory Access (DMA)\" IP to address the latency issues associated with DMA control, thereby enabling high bandwidth data streaming in real-time. It also features a Python-based hardware configuration tool and signal processing framework incorporating an OFDM-based Physical layer. We showcase a real-time data link using the direct RF radio architecture between two RFSoC ZCU216 boards, achieving an error vector magnitude (EVM) of 3.2% for 256QAM across a bandwidth of 1.25 GHz.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698855"}, {"primary_key": "676512", "vector": [], "sparse_vector": [], "title": "SPEAR: Software-defined Python-Enhanced RFSoC for Wideband Radio Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Next-generation wireless systems utilize large signal band-widths to meet the growing data rate demands of emerging applications and to provide enhanced resolution for wireless sensing and imaging. This poses significant challenges in the design of the underlying datapaths that carry and transfer signals across different domains, such as between memory and data converters in various software-defined radio (SDR) platforms. In this paper, we present the design and implementation of SPEAR, which is an SDR platform based on the Xilinx RFSoC ZCU216 evaluation board capable of supporting real-time streaming of signals with a bandwidth of up to 1.25 GHz employing the direct RF radio architecture. SPEAR features hardware-assisted direct memory access (DMA) control for real-time data streaming, and a Python-based hardware configuration tool and signal processing framework. Our experiments show that SPEAR can support a real-time bandwidth of up to 1.25 GHz for 256QAM modulation that satisfies the 3GPP error vector magnitude (EVM) requirement of 3.5%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697310"}, {"primary_key": "676513", "vector": [], "sparse_vector": [], "title": "Near-Field Localization With Coprime Array.", "authors": ["<PERSON><PERSON><PERSON>", "Chang<PERSON>ng You", "<PERSON><PERSON>"], "summary": "Large-aperture coprime arrays (CAs) are expected to achieve higher sensing resolution than conventional dense arrays (DAs), yet with lower hardware and energy cost. However, existing CA far-field localization methods cannot be directly applied to near-field scenarios due to channel model mismatch. To address this issue, in this paper, we propose an efficient near-field localization method for CAs. Specifically, we first construct an effective covariance matrix, which allows to decouple the target angle-and-range estimation. Then, a customized two-phase multiple signal classification (MUSIC) algorithm for CAs is proposed, which first detects all possible targets' angles by using an angular-domain MUSIC algorithm, followed by the second phase to resolve the true targets' angles and ranges by devising a range-domain MUSIC algorithm. Finally, we show that the proposed method is able to locate more targets than the subarray-based method as well as achieve lower root mean square error (RMSE) than DAs.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698231"}, {"primary_key": "676514", "vector": [], "sparse_vector": [], "title": "Multi-Agent Target Pursuit Using Perception Uncertainty-Aware Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Existing target pursuit systems are able to coordinate a team of mobile agents to capture or intercept unauthorized targets. Multi-agent reinforcement learning (MARL) further empowers pursuit strategies with the potential to emerge complex behaviors. However, existing solutions lack the ability to handle the perception uncertainty caused by relative position measurement noises, which blurs the understanding of the target's state and complicates the pursuit strategy learning process. This study proposes PUARL, which enhances the learning under the perception uncertainty process by guiding exploration with probabilistic estimation and adapting the policy based on awareness of perception uncertainty. We validate its performance in terms of both accuracy and efficiency. PUARL achieves a success rate increase of 12.3%+ and a reduction in total steps by 58.3%+, outperforming both state-of-the-art heuristic and learning-based solutions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694724"}, {"primary_key": "676515", "vector": [], "sparse_vector": [], "title": "RF-Diffusion: Radio Signal Generation via Time-Frequency Diffusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yuchong Gao", "<PERSON><PERSON>", "<PERSON>"], "summary": "Along with AIGC shines in CV and NLP, its potential in the wireless domain has also emerged in recent years. Yet, existing RF-oriented generative solutions are ill-suited for generating high-quality, time-series RF data due to limited representation capabilities. In this work, inspired by the stellar achievements of the diffusion model in CV and NLP, we adapt it to the RF domain and propose RF-Diffusion. To accommodate the unique characteristics of RF signals, we first introduce a novel Time-Frequency Diffusion theory to enhance the original diffusion model, enabling it to tap into the information within the time, frequency, and complex-valued domains of RF signals. On this basis, we propose a Hierarchical Diffusion Transformer to translate the theory into a practical generative DNN through elaborated design spanning network architecture, functional block, and complex-valued operator, making RF-Diffusion a versatile solution to generate diverse, high-quality, and time-series RF data. Performance comparison with three prevalent generative models demonstrates the RF-Diffusion's superior performance in synthesizing Wi-Fi and FMCW signals. We also showcase the versatility of RF-Diffusion in boosting Wi-Fi sensing systems and performing channel estimation in 5G networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649348"}, {"primary_key": "676516", "vector": [], "sparse_vector": [], "title": "Demo: Metasurface-Enabled NextG mmWave for Roadside Networking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Wall-Street, a smart surface designed for vehicles to boost 5G mmWave connectivity for passengers. It improves mmWave connections in three ways: (1) it steers outdoor mmWave signals into the vehicle, ensuring all users have coverage; (2) it enables the vehicle to receive data from the current cell while measuring signals from potential handover cells, allowing smooth transitions without interrupting service; (3) during handovers, it combines/splits signals from/to both the current and new cells, creating a make-before-break connection. Our demonstration shows Wall-Street's versatile signal manipulation abilities. These include steering single beams, simultaneously reflecting and transmitting beams for neighboring cell measurements with concurrent communication, and combining or splitting beams for seamless cell transitions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698839"}, {"primary_key": "676517", "vector": [], "sparse_vector": [], "title": "DREW: Double-Throughput Emulated WiFi.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bidirectional communication between BLE/FSK devices and WiFi access points (APs) combines the benefits of long battery life, low device cost, and ubiquitous Internet access. However, prior cross-technology communication (CTC) solutions require transmission mixers inside FSK chips, thus not applicable to newer ultra-low-power (ULP) BLE chips, which removes these mixers to conserve power. Furthermore, throughputs of prior CTC solutions are limited to 1Mbps.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649388"}, {"primary_key": "676518", "vector": [], "sparse_vector": [], "title": "Real-Time Enhancement of Low-Quality Video for Constrained Camera Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deploying high-spec cameras in video systems often falls short of user expectations. Leveraging advancements in deep learning, we propose a mobile, lightweight, real-time video enhancement system. Our approach adopts cutting-edge models and introduces novel optimization techniques for real-time streaming, improving low-resolution, grayscale, and low frame-rate videos. Preliminary evaluations show significant improvements in PSNR and SSIM, while visual assessments confirm substantial quality enhancements while maintaining real-time processing requirements.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697451"}, {"primary_key": "676519", "vector": [], "sparse_vector": [], "title": "Poster: Can You Find Me?: Linking Devices Despite Wi-Fi MAC Randomization at MobiCom 2023.", "authors": ["<PERSON>Urtubey", "<PERSON>"], "summary": "We present results for exposing vulnerabilities in MAC address randomization using Wi-Fi network discovery traffic collected at MobiCom 2023. By analyzing probe request sequence numbers and vendor-specific information elements, we successfully link randomized MAC addresses, reducing the observed MAC addresses to linked groups by over 45%. This demonstrates current MAC randomization protocols inadequately protect device privacy during network discovery.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697454"}, {"primary_key": "676520", "vector": [], "sparse_vector": [], "title": "αLiDAR: An Adaptive High-Resolution Panoramic LiDAR System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LiDAR technology holds vast potential across various sectors, including robotics, autonomous driving, and urban planning. However, the performance of current LiDAR sensors is hindered by limited field of view (FOV), low resolution, and lack of flexible focusing capability. We introduce αLiDAR, an innovative LiDAR system that employs controllable actuation to provide a panoramic FOV, high resolution, and adaptable scanning focus. The core concept of αLiDAR is to expand the operational freedom of a LiDAR sensor through the incorporation of a controllable, active rotational mechanism. This modification allows the sensor to scan previously inaccessible blind spots and focus on specific areas of interest in an adaptive manner. By modeling uncertainties in LiDAR rotation process and estimating point-wise uncertainty, αLiDAR can correct point cloud distortions resulted from significant rotation. In addition, by optimizing LiDAR's rotation trajectory, αLiDAR can swiftly adapt to dynamic areas of interest. We developed several prototypes of αLiDAR and conducted comprehensive evaluations in various indoor and outdoor real-world scenarios. Our results demonstrate that αLiDAR achieves centimeter-level pose estimation accuracy, with an average latency of only 37 ms. In two typical LiDAR applications, αLiDAR significantly enhances 3D mapping accuracy, coverage, and density by 8.5×, 2×, and 1.6× respectively, compared to conventional LiDAR sensors. Additionally, αLiDAR's adaptive rotation improves the effective sensing distance by 1.8× and increases the number of perceived objects by 1.9×. A video demonstration of αLiDAR's in action in real world is available at https://youtu.be/x4zc_I_xTaw. The code is available at https://github.com/HViktorTsoi/alpha_lidar.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690708"}, {"primary_key": "676521", "vector": [], "sparse_vector": [], "title": "Demo: 𝛼LiDAR: An Adaptive High-Resolution Panoramic LiDAR System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present αLiDAR, an innovative LiDAR system that incorporates a controllable active rotational mechanism to broaden the field of view (FOV), enhance resolution, and provide adaptable focusing. This system addresses the inherent limitations of traditional LiDAR sensors, such as narrow FOV, low resolution, and lack of flexible focusing capability. By scanning blind spots and dynamically focusing on areas of interest, αLiDAR significantly surpasses conventional LiDAR sensors. Our prototypes, tested under varied real-world conditions, have demonstrated marked improvements in typical LiDAR applications. Specifically, αLiDAR enhances 3D mapping accuracy, coverage, and density by factors of 8.5, 2, and 1.6, respectively. Furthermore, the adaptive rotational mechanism of αLiDAR extends the effective sensing distance by 1.8× and increases object detection by 1.9×. To see αLiDAR in action, visit our video demonstration at https://youtu.be/x4zc_I_xTaw. Both the hardware and software implementations of αLiDAR are open-sourced at https://github.com/HViktorTsoi/alpha_lidar.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698851"}, {"primary_key": "676522", "vector": [], "sparse_vector": [], "title": "EVLeSen: In-Vehicle Sensing with EV-Leaked Signal.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "While out-vehicle sensing has achieved great success with the development of vehicle radar and Lidar systems, invehicle sensing attracts a lot of attention recently. However, the popular camera-based solutions raise privacy concerns and pose requirement on lighting conditions. Researchers recently utilize wireless signals for sensing. However, besides requiring dedicated hardware, the rich multipath in a small cabin space causes severe interference, degrading the sensing reliability. In this paper, we propose a new sensing modality for in-vehicle sensing, leveraging the leaked EM signals from electric vehicles. The key observation is that the human body can capture the leaked signals, and body motions affect the signal variation patterns. Our solution involves designing conductive cloth tags on the seat to effectively collect body-captured signals and adopting a reference tag to deal with interference. Through extensive experiments conducted over 100 hours, covering a driving distance of 4000 kilometers on various real roads, our system, EVLeSen, can achieve over 90% accuracy in recognizing body motions utilizing just the leaked ambient signals.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649389"}, {"primary_key": "676523", "vector": [], "sparse_vector": [], "title": "Experimental Validation of a 3GPP compliant 5G-based Positioning System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The advent of 5G positioning techniques by 3GPP has unlocked possibilities for applications in public safety, vehicular systems, and location-based services. However, these applications demand accurate and reliable positioning performance, which has led to the proposal of newer positioning techniques. To further advance the research on these techniques, in this paper, we develop a 3GPP-compliant 5G positioning testbed, incorporating gNodeBs (gNBs) and User Equipment (UE). The testbed uses New Radio (NR) Positioning Reference Signals (PRS) transmitted by the gNB to generate Time of Arrival (TOA) estimates at the UE. We mathematically model the inter-gNB and UE-gNB time offsets affecting the TOA estimates and examine their impact on positioning performance. Additionally, we propose a calibration method for estimating these time offsets. Furthermore, we investigate the environmental impact on the TOA estimates. Our findings are based on our mathematical model and supported by experimental results.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697324"}, {"primary_key": "676524", "vector": [], "sparse_vector": [], "title": "Lightweight INT on the Tofino programmable switch.", "authors": ["<PERSON><PERSON>", "Leandro C. de <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In-band network telemetry (INT), enabled by programmable data planes and the appearance of programming protocol-independent languages such as P4, emerged as a viable approach for network monitoring. INT allows the collection of fine-grained network information in real-time, increasing network visibility, at the cost of network overhead. Several lightweight INT approaches have been recently proposed that attempt to alleviate the transmission overhead of INT, while maintaining a high degree of monitoring accuracy. However, their impact on the resources of the respective hardware network devices has been hardly investigated as most of the approaches are evaluated via simulation. In this study, we provide proof of concept implementations of two lightweight INT approaches that have been proposed for path tracing on the Intel Tofino ASIC, identifying the challenges of porting the solution to the selected target. We examine their performance, providing an in-depth analysis of resource consumption.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696729"}, {"primary_key": "676525", "vector": [], "sparse_vector": [], "title": "Cost-Effective Soil Carbon Sensing with Wi-Fi and Optical Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Soil carbon is a critical factor in maintaining soil health and combating climate change. Understanding and managing soil carbon levels is essential for sustainable agriculture and environmental protection. However, current methods for measuring soil carbon are time-consuming and costly, hindering efforts to monitor soil health and increase carbon sequestration. In this paper, we propose Scarf, a novel soil carbon sensing approach that combines widely accessible radio frequency (RF) and optical signals to detect soil carbon contents without dedicated hardware. Our key insight is that soil carbon content closely correlates with two indicators: the effective permittivity derived from RF signals and soil lightness determined from soil surface images. We mathematically model the correlations and leverage the non-linear correlation between the two signal modalities to compute soil carbon content. We employ machine learning to model relationships that cannot be captured by traditional mathematical equations. Our experimental results indicate that Scarf can achieve high soil carbon prediction accuracy that is comparable to the state-of-the-art soil carbon sensing techniques which cost US$1000s.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690675"}, {"primary_key": "676526", "vector": [], "sparse_vector": [], "title": "Scarf: Soil Carbon Sensing with Wi-Fi and Optical Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Soil carbon is a key soil property for soil health management and a crucial part of the global carbon cycle. Existing methods for soil carbon determination are too expensive and time-consuming. This paper introduces Scarf, a novel soil carbon sensing technique that leverages Wi-Fi and images and eliminates the need for specialized hardware. Our analysis reveals a strong correlation between soil carbon content and both the soil permittivity obtained from Wi-Fi signals and the soil lightness captured in soil surface images. We develop mathematical models to quantify the relationships between soil carbon content and the two signal modalities, and use the models to estimate soil carbon. We apply machine learning to help handle relationships that mathematical models do not capture. Our experiments demonstrate that Scarf delivers highly accurate soil carbon estimations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697449"}, {"primary_key": "676527", "vector": [], "sparse_vector": [], "title": "Around the Corner mmWave Imaging in Practical Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Junfeng Guan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the design, implementation, and evaluation of RFlect, a mmWave imaging system capable of producing around-the-corner high-resolution images in practical environments. RFlect leverages signals reflected off complex surfaces (e.g., poles, concave surfaces, or composition of multiple surfaces) to image objects that are not in the RF line-of-sight. RFlect models the reflections and introduces reconstruction algorithms for different types of surfaces. It also leverages a novel method for precisely mapping the location and geometry of the reflecting surface. We also derive the theoretical resolution and coverage for different reflecting surface geometries. We built a prototype of RFlect and performed extensive evaluations to demonstrate its ability to reconstruct the shape of objects around the corner, with an average Chamfer Distance of 2cm and 3D F-Score of 88.6%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690671"}, {"primary_key": "676528", "vector": [], "sparse_vector": [], "title": "GPSense: Passive Sensing with Pervasive GPS Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Wireless sensing is gaining increasing attention from both academia and industry. Various wireless signals, such as Wi-Fi, UWB, and acoustic signals, have been leveraged for sensing. While promising in many aspects, two critical limitations still exist: a) limited sensing coverage; and b) the requirement for dedicated sensing signals, which may interfere with the original function of the wireless technology. To address these issues, we propose to utilize GPS signals for sensing, as GPS signals are already pervasive and emitted from satellites 24/7 at pre-allocated frequency bands, causing no interference. To make GPS sensing possible, we reconstruct signals with amplitude and phase information which is critical for sensing using the raw measurements reported by commercial GPS receiver module. We also develop sensing models to tailor the unique properties of GPS signals such as extremely long transmission distance. Finally, we introduce the concept of distributed sensing and design signal processing methods to fuse signals from multiple satellites to improve sensing performance. With all these designs, we prototype the first GPS wireless sensing system on commercial GPS receiver modules. Comprehensive experiments demonstrate that the proposed system can realize meaningful sensing applications such as human activity sensing, passive trajectory tracking, and respiration monitoring.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690674"}, {"primary_key": "676529", "vector": [], "sparse_vector": [], "title": "LoRaTrimmer: Optimal Energy Condensation with Chirp Trimming for LoRa Weak Signal Decoding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa has been widely used for the Internet of Things (IoT) due to its low power consumption and long communication range. The standard LoRa demodulation process condenses the energy of LoRa chirps to combat noise. However, there is an intrinsic frequency jump in real-life LoRa signals that standard demodulation neglects, reducing communication range in practice. We thoroughly study the frequency jump phenomenon and observe that it affects LoRa demodulation mainly in two folds: First, it makes each section of the signal shorter than the standard FFT perception range, introducing additional noise; Second, it induces a random phase jump that causes destructive addition of signal power. To mitigate the influence of frequency jump on LoRa demodulation, we propose LoRaTrimmer, a novel, fast, and noise-resilient LoRa decoding algorithm that optimally condenses LoRa signal power. LoRaTrimmer contains two innovative designs: First, we trim the perception range of FFT at the frequency jump, trimming off the additional noise; Second, we bypass the phase jump induced by frequency jump by probabilistic modeling and add up signal power constructively. Furthermore, we performed theoretical analysis to guarantee the performance of our method. Thorough experiments in various real-life environments show 1.70 to 2.49 dB SNR gain over the state-of-the-art and 3.44 to 3.79 dB SNR gain over FFT-based methods, translating to at most 1.67 times gain of coverage area. LoRaTrimmer is also robust under complex noise patterns, and capable of real-time decoding, with the only overhead being a slight increase in computational cost (0.51 to 3.17 ms per packet, compared with 0.23 to 0.94 ms of baseline methods).", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690681"}, {"primary_key": "676530", "vector": [], "sparse_vector": [], "title": "D-AirPatrol: A Dual-Layer Architecture for Traffic Patrol From the Sky.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this poster, we present our efforts to enhance air patrol for monitoring traffic and violations from the sky. Through a new dual-layer architecture to decouple the foreground and background of aerial views, our solution D-AirPatrol holds promise to make air patrol more accurate and reliable.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697433"}, {"primary_key": "676531", "vector": [], "sparse_vector": [], "title": "Misaligned Over-The-Air Computation of Multi-Sensor Data with Wiener-Denoiser Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In data driven deep learning, distributed sensing and joint computing bring heavy load for computing and communication. To face the challenge, over-the-air computation (OAC) has been proposed for multi-sensor data aggregation, which enables the server to receive a desired function of massive sensing data during communication. However, the strict synchronization and accurate channel estimation constraints in OAC are hard to be satisfied in practice, leading to time and channel-gain misalignment. The paper formulates the misalignment problem as a non-blind image deblurring problem. At the receiver side, we first use the Wiener filter to deblur, followed by a U-Net network designed for further denoising. Our method is capable to exploit the inherent correlations in the signal data via learning, thus outperforms traditional methods in term of accuracy. Our code is available at https://github.com/auto-Dog/MOAC_deep.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694732"}, {"primary_key": "676532", "vector": [], "sparse_vector": [], "title": "Demystifying Privacy in 5G Stand Alone Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ensuring user privacy remains critical in mobile networks, particularly with the rise of connected devices and denser 5G infrastructure. Privacy concerns have persisted across 2G, 3G, and 4G/LTE networks. Recognizing these concerns, the 3rd Generation Partnership Project (3GPP) has made privacy enhancements in 5G Release 15. However, the extent of operator adoption remains unclear, especially as most networks operate in 5G Non Stand Alone (NSA) mode, relying on 4G Core Networks. This study provides the first qualitative and experimental comparison between 5G NSA and Stand Alone (SA) in real operator networks, focusing on privacy enhancements addressing top eight pre-5G attacks based on recent academic literature. Additionally, it evaluates the privacy levels of OpenAirInterface (OAI), a leading open-source software for 5G, against real network deployments for the same attacks. The analysis reveals two new 5G privacy vulnerabilities, underscoring the need for further research and stricter standards.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690696"}, {"primary_key": "676533", "vector": [], "sparse_vector": [], "title": "Experiences of Deploying a Citywide Crowdsourcing Platform to Search for Missing People with Dementia.", "authors": ["<PERSON><PERSON><PERSON>", "Guanyao Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "People with Dementia (PwD) suffer from a high risk of getting lost due to their cognitive deterioration, leading to potential safety hazards and significant search efforts. In this paper, we propose DEmentia Caring System (DECS), an effective crowdsourcing platform to search for missing PwD. Specifically, PwD carry our customized Bluetooth Low Energy (BLE) tags that broadcast BLE packets, which are detected and then uploaded by mobile volunteers via their smartphones. To further enhance search efficiency, DECS deploys BLE gateways as its infrastructure and analyzes PwD's daily spatial-temporal mobility patterns. DECS has been deployed in Hong Kong since 2019, supporting 3,100+ PwD's families with over 45,000 app downloads by volunteers. More importantly, it has successfully served the search for 254 missing cases. This paper reports the unique lessons and experiences learned through our 4-year citywide deployment of DECS.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649356"}, {"primary_key": "676534", "vector": [], "sparse_vector": [], "title": "An End-to-End, Low-Cost, and High-Fidelity 3D Video Pipeline for Mobile Devices.", "authors": ["Tiancheng Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chengfei Lv", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To provide full-body 3D videos of performers showcasing diverse clothes with dynamic movements for e-commerce platforms, we develop an end-to-end, low-cost, and high-fidelity production and deployment pipeline. We first set up a low-cost capture studio with only 24 RGB cameras and embrace fast neural surface reconstruction to produce high-quality meshes without depth information. We then quickly group all the frames with local motion priors, select a keyframe for each group, and accurately register any other frame to the keyframe under the guidance of semantic labels, thereby avoiding transmitting all the frames to mobile devices and loading them into memory. For real-time rendering, we propose an on-device sparse computation method for efficient deformation from keyframes to the other frames. Evaluation over 2 self-captured performances and 8 public performances reveals that the pipeline achieves the reconstruction time of 28 minutes per frame, the average PSNR of 30.4, the average bandwidth requirement of 4.2MB/s, and the on-device frame rate of 60 fps, demonstrating superiority over existing baselines.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690685"}, {"primary_key": "676535", "vector": [], "sparse_vector": [], "title": "Hybrid Data-Driven and Simulation-Driven Prediction of mmWave Network Performance.", "authors": ["<PERSON><PERSON><PERSON>", "Xingyu Chen", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter wave (mmWave) links offer high-bandwidth connectivity for next-generation wireless networks but face challenges in interference management and performance prediction. This paper introduces DDS, a novel hybrid data-driven simulator that accurately predicts link throughput distribution in mmWave networks. DDS leverages readily available physical (PHY) layer measurements and employs a deep reinforcement learning (DRL) framework to interpret the PHY environment and determine appropriate parameters for subsequent simulations. The system integrates a DRL-based parameter tuner with a PHY and MAC layer simulator, bridging the gap between simulation and real-world performance. We conduct comprehensive evaluations of DDS, demonstrating its superior accuracy compared to its baselines. Our experiments validate DDS's effectiveness in enhancing network controller training and deriving optimal network configuration policies in dense mmWave deployments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694725"}, {"primary_key": "676536", "vector": [], "sparse_vector": [], "title": "xDevSM: Streamlining xApp Development With a Flexible Framework for O-RAN E2 Service Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RAN Intelligent Controllers (RICs) are programmable platforms that enable data-driven closed-loop control in the O-RAN architecture. They collect telemetry and data from the RAN, process it in custom applications, and enforce control or new configurations on the RAN. Such custom applications in the Near-Real-Time (RT) RIC are called xApps, and enable a variety of use cases related to radio resource management. Despite numerous open-source and commercial projects focused on the Near-RT RIC, developing and testing xApps that are interoperable across multiple RAN implementations is a time-consuming and technically challenging process. This is primarily caused by the complexity of the protocol of the E2 interface, which enables communication between the RIC and the RAN while providing a high degree of flexibility, with multiple Service Models (SMs) providing plug-and-play functionalities such as data reporting and RAN control. In this paper, we propose xDevSM, an open-source flexible framework for O-RAN service models, aimed at simplifying xApp development for the O-RAN Software Community (OSC) Near-RT RIC. xDevSM reduces the complexity of the xApp development process, allowing developers to focus on the control logic of their xApps and moving the logic of the E2 service models behind simple Application Programming Interfaces (APIs). We demonstrate the effectiveness of this framework by deploying and testing xApps across various RAN software platforms, including OpenAirInterface and srsRAN. This framework significantly facilitates the development and validation of solutions and algorithms on O-RAN networks, including the testing of data-driven solutions across multiple RAN implementations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697325"}, {"primary_key": "676537", "vector": [], "sparse_vector": [], "title": "Towards an Open Mobile Core.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the ongoing evolution of mobile networks, a key emerging focus is on \"openness\": disaggregation to enable multiple vendors. The obvious example of this is OpenRAN, a movement to open the traditionally-single-vendor RAN, to multiple vendors. However, despite the clear benefits that this approach has brought to the RAN, there has been little interest in applying this philosophy to other parts of the mobile network such as the mobile core. We argue that applying the philosophy of openness to the mobile core will result in significant ecosystem improvements to both mobile network operators and governments. In addition, we identify the key challenges for an \"open core\", formulate a set of criteria for evaluating openness of core designs, and assess current alternatives with respect those criteria.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697465"}, {"primary_key": "676538", "vector": [], "sparse_vector": [], "title": "Starlink Performance from Different Perspectives.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The emerging nature of low-earth-orbiting satellite constellations has brought forth an era of global connectivity. Star-link has the potential to offer a truly global, high-performant service. An obvious question arising from this is whether Starlink can replace existing terrestrial services. In this work we describe the key findings from a detailed study of the performance of Starlink, investigating both its potential and shortcomings to act as a \"global ISP\".", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697452"}, {"primary_key": "676539", "vector": [], "sparse_vector": [], "title": "Routing-Aware Shaping for Feasible Multi-Domain Determinism.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Use cases for network determinism may involve the traversal of multiple network domains. A typical example is the edge-cloud virtualization of the controllers for industrial automation, where the end-to-end data path may include a private mobile network and a wired local-area network within the factory premises, a public transport network between the factory and the edge-cloud data center, and a switching fabric within the same data center. Maintaining deterministic guarantees throughout these domains, likely operated by different providers, is a daunting task when each domain relies on its own control and data-plane technologies for satisfaction of its allocated portion of the end-to-end requirements. We propose a universal model for determinism in wired packet networks that simplifies the realization of deterministic services over chains of network domains. The model builds on a novel traffic shaper where routing information constrains the packet scheduling decisions. The shaper, and therefore the provisioning of resources for every deterministic service, is only needed at the ingress edge of each wired domain. We conjecture perdomain latency bounds enforced by the shaper and corroborate them with experimental evidence. The bounds are easy to combine for multi-domain network paths.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696727"}, {"primary_key": "676540", "vector": [], "sparse_vector": [], "title": "Mambas: Maneuvering Analog Multi-User Beamforming using an Array of Subarrays in mmWave Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Zhenzhou Qi", "<PERSON><PERSON><PERSON>"], "summary": "Beyond-5G and 6G wireless networks exploit the millimeter-wave (mmWave) frequency bands to achieve significantly improved data rates, and existing mmWave systems rely on analog single-user beamforming (SUBF) or hybrid multi-user beamforming (MUBF). In this work, we focus on improving the performance of multi-user communication in mmWave networks by exploring analog MUBF using an array of subarrays (ASA) with reduced system overhead and hardware complexity as it eliminates digital beamforming and the need for estimating the channel state information (CSI). We present Mambas, a novel system that maneuvers analog MUBF using an ASA to support simultaneous communication with multiple users located in close proximity, e.g., within the half-power beamwidth of the ASA. In essence, <PERSON><PERSON><PERSON> effectively decouples the user selection, subarray allocation, and beamforming optimization based on a comprehensive understanding of the multi-user support determined by the ASA. We evaluate <PERSON><PERSON><PERSON> using a 28 GHz software-defined radio testbed and show that, compared to existing methods, Mambas can effectively support users that are 2× more closely spaced while achieving an improved sum rate of up to 2×, using only two subarrays. Large-scale ray tracing-based simulations also show that <PERSON>mbas can achieve a sum rate gain of 1.92--3.86× and is able to maintain consistent performance with significantly increased user density.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649390"}, {"primary_key": "676541", "vector": [], "sparse_vector": [], "title": "DeepMon: Wi-Fi Monitoring Using Sub-Nyquist Sampling Rate Receivers with Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Next-generation Wi-Fi networks employ large signal bandwidth to meet the demands of high data rates, which poses challenges to Wi-Fi monitoring systems that typically rely on a full sampling rate receiver (RX) to capture signals at full bandwidth for demodulation and decoding. Interestingly, preambles of Wi-Fi packets contain unencrypted information that can be decoded to extract Wi-Fi Physical (PHY) layer information such as modulation and coding scheme (MCS), transmission time, and PHY service data unit (PSDU) length. In this paper, we propose DeepMon, which leverages low-cost RXs operating at sub-Nyquist sampling rates and deep learning (DL) to identify the Wi-Fi protocol and decode PHY layer packet properties from the Wi-Fi preamble. To evaluate DeepMon, we use PlutoSDR as the low sampling rate RX to collect a dataset of over 390K real-world 802.11a/n/ac Wi-Fi packets for the DL model training and testing. Our experiments show that for Wi-Fi packets with up to 160 MHz bandwidth, an RX running DeepMon at 3 MHz sampling rate (i.e., a downsampling ratio of >50×) can achieve an average bit decoding accuracy of 96.20% for the legacy signal field, corresponding to a mean absolute error of only 0.077 ms for predicting the packet transmission time.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698250"}, {"primary_key": "676542", "vector": [], "sparse_vector": [], "title": "Demo: Scalable and Sustainable Asset Tracking with NextG Cellular Signals.", "authors": ["<PERSON><PERSON><PERSON>", "Aritrik Ghosh", "<PERSON><PERSON><PERSON>"], "summary": "This demonstration presents LiTEfoot, an ultra-low power localization system leveraging ambient cellular signals. To address the limitations of traditional GPS-based tracking systems in terms of power consumption and latency, LiTEfoot employs a non-linear transformation of the cellular spectrum to achieve efficient self-localization. Our design uses a simple envelope detector to realize spectrum folding, enabling the identification of multiple active base stations. The LiTEfoot prototype shows a median localization error of 22 meters in urban areas and 50 meters in rural areas, consuming only 40 μJoules of energy per localization update.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698837"}, {"primary_key": "676543", "vector": [], "sparse_vector": [], "title": "EdgeCloudAI: Edge-Cloud Distributed Video Analytics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in Visual Language Models (VLMs) have significantly enhanced video analytics. VLMs capture complex visual and textual connections. While Convolutional Neural Networks (CNNs) excel in spatial pattern recognition, VLMs provide a global context, making them ideal for tasks like complex incidents and anomaly detection. However, VLMs are much more computationally intensive, posing challenges for large-scale and real-time applications. This paper introduces EdgeCloudAI, a scalable system integrating VLMs and CNNs through edge-cloud computing. Edge-CloudAI performs initial video processing (e.g., CNN) on edge devices and offloads deeper analysis (e.g., VLM) to the cloud, optimizing resource use and reducing latency. We have deployed EdgeCloudAI on the NSF COSMOS testbed in NYC. In this demo, we will demonstrate EdgeCloudAI's performance in detecting user-defined incidents in real-time.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698857"}, {"primary_key": "676544", "vector": [], "sparse_vector": [], "title": "SigmoidOxy: A Light-weight mobile perfusion tool for diabetic foot management.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Diabetic foot ulcers (DFUs) represent a significant global health challenge for the elderly with high mortality rates and complications. While imaging technologies like NIRS and hyperspectral imaging have improved wound assessment in clinical settings, their cost, and large size limit their use in the home and primary care. On the other hand, existing mobile solutions only capture secondary bio-markers like color and wound size. This paper introduces SigmoidOxy (or σ(Oxy)), a novel smartphone-based perfusion tool for DFU management. SigmoidOxy extracts oxygenation information from standard RGB images captured by smartphone cameras by applying hyperspectral reconstruction models to infer oxygenation. We evaluate SigmoidOxy's performance using the SPECTRALPACA dataset [2] finding an Average Persons R of 0.72 and Average Mean Absolute Error of 0.239 when comparing sigmoid oxygenation signals and analyze its sensitivity to ischemia in the DFUC2021 dataset [17].", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698119"}, {"primary_key": "676545", "vector": [], "sparse_vector": [], "title": "Wi-Limb: Recognizing Moving Body Limbs Using a Single WiFi Link.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Utilizing fine grained analysis of wireless signals for human activity recognition has gained a lot of traction recently. The unique changes to the ambient wireless signals caused by different activities made it possible to recognize these fingerprints through deep learning classification methods. Most of the existing work consider a set of physical activities or gestures and try to recognize each one of them as a separate class. However, this makes the classification task harder especially when the number of activities to recognize becomes larger and when these activities include movements from the same body parts. To address that, in this study, we consider the decomposition of each physical activity into the limbs and body parts involved in that activity and study a one-by-one recognition solution. We propose a Generative Adversarial Network (GAN)-based hierarchical method that not only recognizes the involved body limbs and facilitates the recognition of complex activities, but also mitigates the temporal effects in the collected signal data and thus provides a generalized solution. Our experimental evaluation shows that we can recognize unknown physical activities through the proposed hierarchical limb recognition based model with a small Hamming loss and by just using WiFi signal data from a single transmitter and receiver link.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698117"}, {"primary_key": "676546", "vector": [], "sparse_vector": [], "title": "DEMO: SPARC: Spatio-Temporal Adaptive Resource Control for Multi-site Spectrum Management in NextG Cellular Networks.", "authors": ["<PERSON><PERSON><PERSON>", "A<PERSON> J. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work presents SPARC (Spatio-Temporal Adaptive Resource Control), a novel approach for multi-site spectrum management in NextG cellular networks. SPARC addresses the challenge of limited licensed spectrum in dynamic environments. We leverage the O-RAN architecture to develop a multi-timescale RAN Intelligent Controller (RIC) framework, featuring an xApp for near-real-time interference detection and localization, and a μApp for real-time intelligent resource allocation. By utilizing base stations as spectrum sensors, SPARC enables efficient and fine-grained dynamic resource allocation across multiple sites, enhancing signal-to-noise ratio (SNR) by up to 7dB, spectral efficiency by up to 15%, and overall system throughput by up to 20%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701547"}, {"primary_key": "676547", "vector": [], "sparse_vector": [], "title": "AppNet: Application-Aware Networking with O-RAN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The 5G network promised transformative services across various industries, yet its integration has mostly been limited to existing 4G services like IMS-based multimedia and IoT. This paper identifies two key reasons for this underutilization: first, the stringent, multi-dimensional requirements of next-generation verticals like AR/VR, mobile gaming, and robotics, and the limitations of traditional Quality of Service (QoS) approaches in meeting these needs. We argue for a shift towards Quality of Experience (QoE), which better captures user perception and instantaneous application state. To meet stringent QoE demands and optimize network utilization, we emphasize the importance of application state and context awareness within the network. As a solution, we propose AppNet, a novel framework that integrates application awareness into the networking stack via RAN Intelligent Controllers (RICs) of the Open-RAN platform, enabling dynamic QoS adjustments based on application context. This paper highlights 5G private networks as an ideal testing ground, focusing on multi-user scenarios to deliver optimal real-time interactive services at scale.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697427"}, {"primary_key": "676548", "vector": [], "sparse_vector": [], "title": "Enabling Cross-Medium Wireless Networks with Miniature Mechanical Antennas.", "authors": ["<PERSON>", "Z<PERSON><PERSON> An", "<PERSON><PERSON> Dai", "Jingyu Tong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Within the burgeoning 6G wireless network landscape, there is an intensified push toward achieving all-encompassing accessibility through integrated solutions spanning a multitude of domains. Notwithstanding recent advancements, the conventional relay-centric communication paradigms grapple with scalability and optimal performance issues. In this paper, we introduce MeAnt ---a versatile IoT platform uniquely architected to foster seamless cross-medium communication by leveraging the compact design of piezoelectric-based mechanical antennas (Piezo-MAs). By capitalizing on the propagation attributes of medium-frequency radios emitted from Piezo-MAs, MeAnt promises communication across diverse environments such as air, water, soil, concrete, and even biological tissue, all while maintaining a compact antenna footprint. Moreover, in light of challenges such as potential interference from AM broadcasts and the intrinsic unidirectional nature of Piezo-MAs, we have developed a finely crafted full-stack communication protocol. Comprehensive tests underscore the system's proficiency, demonstrating a penetration depth of up to 10 m in cross-medium environments and realizing a throughput of 8.7 kbps.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649387"}, {"primary_key": "676549", "vector": [], "sparse_vector": [], "title": "Delta: A Cloud-assisted Data Enrichment Framework for On-Device Continual Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Jia", "<PERSON><PERSON><PERSON>"], "summary": "In modern mobile applications, users frequently encounter various new contexts, necessitating on-device continual learning (CL) to ensure consistent model performance. While existing research predominantly focused on developing lightweight CL frameworks, we identify that data scarcity is a critical bottleneck for on-device CL. In this work, we explore the potential of leveraging abundant cloud-side data to enrich scarce on-device data, and propose a private, efficient and effective data enrichment framework Delta. Specifically, Delta first introduces a directory dataset to decompose the data enrichment problem into device-side and cloud-side sub-problems without sharing sensitive data. Next, Delta proposes a soft data matching strategy to effectively solve the device-side sub-problem with sparse user data, and an optimal data sampling scheme for cloud server to retrieve the most suitable dataset for enrichment with low computational complexity. Further, Delta refines the data sampling scheme by jointly considering the impact of enriched data on both new and past contexts, mitigating the catastrophic forgetting issue from a new aspect. Comprehensive experiments across four typical mobile computing tasks with varied data modalities demonstrate that <PERSON> could enhance the overall model accuracy by an average of 15.1%, 12.4%, 1.1% and 5.6% for visual, IMU, audio and textual tasks compared with few-shot CL, and consistently reduce the communication costs by over 90% compared to federated CL.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690701"}, {"primary_key": "676550", "vector": [], "sparse_vector": [], "title": "CLOUD-D RF: Cloud-based Distributed Radio Frequency Heterogeneous Spectrum Sensing.", "authors": ["<PERSON>", "<PERSON>", "River Thaboun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In wireless communications, collaborative spectrum sensing is a process that leverages radio frequency (RF) data from multiple RF sensors to make more informed decisions and lower the overall risk of failure in distributed settings. However, most research in collaborative sensing focuses on homogeneous systems using identical sensors, which would not be the case in a real world wireless setting. Instead, due to differences in physical location, each RF sensor would see different versions of signals propagating in the environment, establishing the need for heterogeneous collaborative spectrum sensing. Hence, this paper explores the implementation of collaborative spectrum sensing across heterogeneous sensors, with sensor fusion occurring in the cloud for optimal decision making. We investigate three different machine learning-based fusion methods and test the fused model's ability to perform modulation classification, with a primary goal of optimizing for network bandwidth in regard to next-generation network applications. Our analysis demonstrates that our fusion process is able to optimize the number of features extracted from the heterogeneous sensors according to their varying performance limitations, simulating adverse conditions in a real-world wireless setting.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698249"}, {"primary_key": "676551", "vector": [], "sparse_vector": [], "title": "Gait Speed Estimation from Millimeter-Wave Wireless Sensing.", "authors": ["Zhuangzhuang Gu", "<PERSON><PERSON>", "Sanjib Sur"], "summary": "Gait speed is an important indicator of human health. Monitoring patients' gait speed can help doctors assess the recovery process, but traditional clinician observation fails to track in home scenarios. Compared to vision-based and wearable approaches, radio frequency signals offer an easily deployable and light free solution protecting user privacy in home scenarios. Therefore, we proposed a millimeter-wave (mmWave) system to accurately extract walking periods from collected trials and calculate gait speeds. To evaluate the robustness and reliability of our system and determine the optimal mounting position, we collected data from 5 volunteers with normal walking speeds and imitated various abnormal gait patterns and walking speeds. The results show that the mmWave device mounted near the ground outperforms across all volunteers than the one mounted near the ceiling, achieving an average estimation error of 0.02 m/s in abnormal gait evaluations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697439"}, {"primary_key": "676552", "vector": [], "sparse_vector": [], "title": "WavePurifier: Purifying Audio Adversarial Examples via Hierarchical Diffusion Models.", "authors": ["Han<PERSON> Guo", "<PERSON><PERSON><PERSON><PERSON> Wang", "Bocheng Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose WavePurifier, an audio purification framework to defend against audio adversarial attacks. Audio adversarial attacks craft adversarial examples or perturbations to attack the automated speech recognition (ASR) models. Although existing defense mechanisms can detect such attacks and raise alarms, they fail to recover or maintain benign commands. Consequently, this leads to the denial of users' benign commands. Different than existing defenses, WavePurifier aims to purify adversarial examples, thereby rectifying the user's benign commands. We find that the forward diffusion process of the diffusion model effectively eliminates perturbations, whereas the reverse diffusion process restores benign speech. Based on this, we develop a hierarchical diffusion model to defend against audio adversarial examples. This model is capable of purifying different spectrogram bands to varying degrees. To validate the performance of WavePurifier, we purify the adversarial examples from 3 different adversarial attacks in 140 distinct settings. In total, we collect 78,864 diffused spectrograms and 21,000 purified audios. Then, we evaluate WavePurifier on 2 different ASR models, 4 commercial speech-to-text APIs, 2 real-world attack scenarios, and compare them against 7 existing defense approaches. Our result shows that WavePurifier is a universal framework, demonstrating adaptability across diverse attacks with the same hyperparameters. Notably, WavePurifier outperforms existing methods with the lowest character error rate (CER), word error rate (WER), and a high purification success rate against different attacks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690692"}, {"primary_key": "676553", "vector": [], "sparse_vector": [], "title": "Exploring Biomagnetism for Inclusive Vital Sign Monitoring: Modeling and Implementation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the design, implementation, and evaluation of MagWear, a novel biomagnetism-based system that can accurately and inclusively monitor the heart rate and respiration rate of mobile users with diverse skin tones. MagWear's contributions are twofold. Firstly, we build a mathematical model that characterizes the magnetic coupling effect of blood flow under the influence of an external magnetic field. This model uncovers the variations in accuracy when monitoring vital signs among individuals. Secondly, leveraging insights derived from this mathematical model, we present a softwarehardware co-design that effectively handles the impact of human diversity on the performance of vital sign monitoring, pushing this generic solution one big step closer to real adoptions. We have implemented a prototype of MagWear on a two-layer PCB board and followed IRB protocols to conduct system evaluations. Our extensive experiments involving 30 volunteers demonstrate that MagWear achieves high monitoring accuracy with a mean percentage error (MPE) of 1.55% for heart rate and 1.79% for respiration rate. The head-to-head comparison with Apple Watch 8 further demonstrates MagWear's consistently high performance in different user conditions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649349"}, {"primary_key": "676554", "vector": [], "sparse_vector": [], "title": "3 W&apos;s of smartphone power consumption: Who, Where and How much is draining my battery?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With 6.5 billion smartphones in use worldwide, each relying on a battery for key subsystems like display, compute, and cellular connectivity, previous studies on power consumption often used invalidated indirect estimates that failed to isolate specific hardware usage. We address this by utilizing Google's On Device Power Rails Monitor (ODPM) tool for precise power measurements of individual components. Our findings indicate that connectivity (Wi-Fi, 4G/5G) and screen display are the primary power consumers, as shown with the Google Pixel 7A. We also confirmed similar power consumption trends using an energy estimation method on the Samsung S23+. Given the prevalence of smartphones, we discuss the challenges and opportunities for optimizing power usage.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695905"}, {"primary_key": "676555", "vector": [], "sparse_vector": [], "title": "Automated and Blind Detection of Low Probability of Intercept RF Anomaly Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automated spectrum monitoring necessitates the accurate detection of low probability of intercept (LPI) radio frequency (RF) anomaly signals to identify unwanted interference in wireless networks. However, detecting these unforeseen low-power RF signals is fundamentally challenging due to the scarcity of labeled RF anomaly data. In this paper, we introduce WANDA (Wireless ANomaly Detection Algorithm), an automated framework designed to detect LPI RF anomaly signals in low signal-to-interference ratio (SIR) environments without relying on labeled data. WANDA operates through a two-step process: (i) Information extraction, where a convolutional neural network (CNN) utilizing soft Hirschfeld-Gebelein-Rényi correlation (HGR) as the loss function extracts informative features from RF spectrograms; and (ii) Anomaly detection, where the extracted features are applied to a one-class support vector machine (SVM) classifier to infer RF anomalies. To validate the effectiveness of WANDA, we present a case study focused on detecting unknown Bluetooth signals within the WiFi spectrum using a practical dataset. Experimental results demonstrate that WANDA outperforms other methods in detecting anomaly signals across a range of SIR values (-10 dB to 20 dB).", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698243"}, {"primary_key": "676556", "vector": [], "sparse_vector": [], "title": "XRMan: Towards Real-time Hand-Object Pose Tracking in eXtended Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurate tracking of hand and object poses is essential for immersive XR applications. However, existing methods often struggle with the stringent requirements of XR environments. We present XRMan, a real-time hand-object pose tracking system designed for in-the-wild scenarios. XRMan uses a drift monitoring module for consistent accuracy, while farthest-point sampling and collider-based optimization streamline iterative optimization and reduce latency. Our system reduces end-to-end latency by 38.9% compared to the baseline, with further improvements expected from the collider-based post-optimization module.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697422"}, {"primary_key": "676557", "vector": [], "sparse_vector": [], "title": "Capacitive Sensing-based Eye Tracking for XR Glasses.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Eye tracking technology has become increasingly vital as human-computer interaction utilizing extended reality (XR) technologies becomes more mainstream. Traditional eye tracking systems predominantly rely on cameras. While effective, these systems often suffer from high complexity, power consumption, and significant costs. This paper proposes a novel approach to eye tracking with capacitive sensing technology. Leveraging the principles of capacitance, we envision more accessible and efficient eye tracking solutions that can be integrated into various applications. This paper outlines the process of designing and analyzing a prototype of capacitance-based eye tracking.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697453"}, {"primary_key": "676558", "vector": [], "sparse_vector": [], "title": "m3MIMO: An 8×8 mmWave Multi-User MIMO Testbed for Wireless Research.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present m3MIMO a mmWave fully-digital multi-user multi-input multi-output (MU-MIMO) testbed for advanced wireless research. m3MIMO operates in the 57-64 GHz frequency range and supports up to 1 GHz of bandwidth enabling large data multiplexing in the frequency domain through orthogonal frequency-division multiplexing (OFDM). The testbed features three custom-designed Zynq UltraScale+ RFSoC-based Software Defined Radios (SDRs) empowered with the Pi-Radio fully digital transceivers. Two of these SDRs support eight transmit and receive streams each (8 × 8 MIMO), while the third SDR supports up to four channels. m3MIMO supports three different communication modes: (i) point-to-point (P2P) transmissions; (ii) single-user multi-input multi-output (SU-MIMO), where multiple streams are transmitted to a single end-device; and (iii) MU-MIMO, where two devices are simultaneously served by a single transmitter. To showcase the m3MIMO's versatility, we present two research use cases: tracking-based beamforming and mmWave-based sensing. We will open-source the m3MIMO code along with the relevant use-case datasets, facilitating further analysis1.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697321"}, {"primary_key": "676559", "vector": [], "sparse_vector": [], "title": "See Through Vehicles: Fully Occluded Vehicle Detection with Millimeter Wave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Meng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yubo Yan", "<PERSON><PERSON><PERSON>"], "summary": "A crucial task in autonomous driving is to continuously detect nearby vehicles. Problems thus arise when a vehicle is occluded and becomes \"unseeable\", which may lead to accidents. In this study, we develop mmOVD, a system that can detect fully occluded vehicles by involving millimeter-wave radars to capture the ground-reflected signals passing beneath the blocking vehicle's chassis. The foremost challenge here is coping with ghost points caused by frequent multi-path reflections, which highly resemble the true points. We devise a set of features that can efficiently distinguish the ghost points by exploiting the neighbor points' spatial and velocity distributions. We also design a cumulative clustering algorithm to effectively aggregate the unstable ground-reflected radar points over consecutive frames to derive the bounding boxes of the vehicles.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690658"}, {"primary_key": "676560", "vector": [], "sparse_vector": [], "title": "Extended-Range Two-way Radar Backscatter Communication with Low-Power IoT Tags.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Panch<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces BiScatter2 as an extension of BiScatter [3], an integrated radar backscatter communication and sensing system. BiScatter2 provides simultaneous uplink and downlink communications, and precise radar-based sensing and localization. By refining the signal processing techniques and tag architecture design, BiScatter2 extends the operational range, setting a new baseline for two-way radar backscatter systems. This functionality is enabled through the use of chirp-slope-shift-keying modulation applied to Frequency Modulated Continuous Wave (FMCW) radars. BiScatter2 incorporates passive differential circuitry on backscatter tags for efficient, low-power decoding and extends its coverage range by combining the tag decoder and retro-reflective structure. Our evaluation results show an increase of 46% maximum range under the same throughput in downlink communication, which increases the original work's capability for commercial radars.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697455"}, {"primary_key": "676561", "vector": [], "sparse_vector": [], "title": "Safe Routes, Safer Rides: A Multi-Tiered Approach to Trajectory Anomaly Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The swift expansion of the ride-hailing industry has given rise to pressing safety issues. This study investigates enhancing safety in the ride-hailing industry by developing an anomaly detection system for detecting abnormal vehicle trajectories. This system defines what constitutes an abnormal trajectory and establishes evaluation metrics for detection methods. Traditional methods for anomaly detection, such as real-time monitoring of speed and direction, are inadequate in the face of data heterogeneity and the sheer volume of information. And the proposed anomaly detection system is designed to overcome these limitations: We introduce a multi-tiered strategy, dividing the anomaly detection task into critical points (including origin and destination) analysis and path analyses to effectively identify and categorize abnormal patterns. By concentrating different features or patterns in each tier, the detection capability of this system is effectively enhanced. Combining our evaluation metrics, the system can provide risk assessment for abnormal critical points and trajectories, thus ensuring the safety of both drivers and passengers.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694733"}, {"primary_key": "676562", "vector": [], "sparse_vector": [], "title": "Hornbill: A Portable, Touchless, and Battery-Free Electrochemical Bio-tag for Multi-pesticide Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Chen"], "summary": "Pesticide overuse poses significant risks to human health and environmental integrity. Addressing the limitations of existing approaches, which struggle with the diversity of pesticide compounds, portability issues, and environmental sensitivity, this paper introduces Hornbill. A wireless and battery-free electrochemical bio-tag that integrates the advantages of NFC technology with electrochemical biosensors for portable, precise, and touchless multi-pesticide detection. The basic idea of <PERSON><PERSON> is comparing the distinct electrochemical responses between a pair of biological receptors and different pesticides to construct a unique set of feature fingerprints to make multi-pesticide sensing feasible. To incorporate this idea within small NFC tags, we reengineer the electrochemical sensor, spanning the antenna to the voltage regulator. Additionally, to improve the system's sensitivity and environmental robustness, we carefully design the electrodes by combining microelectrode technology and materials science. Experiments with 9 different pesticides show that <PERSON>bill achieves a mean accuracy of 93% in different concentration environments and its sensitivity and robustness surpass that of commercial electrochemical sensors.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690693"}, {"primary_key": "676563", "vector": [], "sparse_vector": [], "title": "RoboLife: Creating Autonomous Living Robots with Adaptive Human-Like Behavior.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Interactive robots have gained popularity, yet most are limited to motion, conversation, and recognition, lacking the ability to autonomously live and adapt like humans. This paper presents RoboLife, a novel system that empowers robots to perceive and learn from their environment, engage in human-like interactions with users and other robots, and autonomously plan activities. RoboLife introduces an adaptive framework with three core components: perception, human-life simulation, and dynamic learning. These enable robots to adapt, perform tasks, and engage meaningfully with their surroundings. Its key innovation lies in integrating environmental influences, human interactions, and inter-robot communication, allowing robots to form opinions, remember experiences, and autonomously execute activities---effectively living their own lives. The demo is available at https://youtu.be/uz6HtkDkXxY.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698838"}, {"primary_key": "676564", "vector": [], "sparse_vector": [], "title": "Improving On-Device LLMs&apos; Sensory Understanding with Embedding Interpolations.", "authors": ["Kai<PERSON> Hou", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongkai Chen", "Zhenyu Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have shown significant potential in performing inferences on various tasks using heterogeneous sensors with minimal human intervention. Despite their promise, challenges such as high inference overhead and limitations on resource-constrained edge devices remain. Additionally, model hallucinations, particularly those arising from cognitive biases when interpreting numerical data, hinder performance. This work introduces a novel technique, embedding interpolation, to enhance LLMs' understanding of sensor measurements and mitigate inference overhead on edge devices. By computing embeddings through pre-computed boundary embeddings instead of directly from the input, we improve efficiency and accuracy. The effective-ness of this approach is demonstrated through visualizations with image generation models.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697456"}, {"primary_key": "676565", "vector": [], "sparse_vector": [], "title": "Uncovering Problematic Designs Hindering Ubiquitous Cellular Emergency Services Access.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Haitian Yan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cellular networks provide the most accessible emergency services with ubiquitous coverage, yet their emergency-specific designs remain largely unexplored. To systematically explore potential design defects that lead to failures or delays in emergency services, we introduce M911-Verifier, an emergency-specific model checking tool. It reveals many counterintuitive findings regarding the ubiquitous access support for cellular emergency services. Our study shows that, despite sufficient wireless signal coverage, users may still experience prolonged emergency call setup times, call initiation failures, or call drops due to flaws in the design of cellular emergency services. These design defects arise from three major causes: problematic network selection for initiating emergency calls, emergency-unaware call operation, and network escalation forbidden during emergency calls. The impacts of these defects have been experimentally validated across three U.S. carriers and two Taiwan carriers using commodity smartphones. Finally, we propose solutions and evaluate their effectiveness.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690704"}, {"primary_key": "676566", "vector": [], "sparse_vector": [], "title": "Apple v.s. Meta: A Comparative Study on Spatial Tracking in SOTA XR Headsets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Inaccurate spatial tracking in extended reality (XR) headsets can cause virtual object jitter, misalignment, and user discomfort, limiting the headsets' potential for immersive content and natural interactions. We develop a modular testbed to evaluate the tracking performance of commercial XR headsets, incorporating system calibration, tracking data acquisition, and result analysis, and allowing the integration of external cameras and IMU sensors for comparison with open-source VI-SLAM algorithms. Using this testbed, we quantitatively assessed spatial tracking accuracy under various user movements and environmental conditions for the latest XR headsets, Apple Vision Pro and Meta Quest 3. The Apple Vision Pro outperformed the Meta Quest 3, reducing relative pose error (RPE) and absolute pose error (APE) by 33.9% and 14.6%, respectively. While both headsets achieved subcentimeter APE in most cases, they exhibited APE exceeding 10 cm in challenging scenarios, highlighting the need for further improvements in reliability and accuracy.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696215"}, {"primary_key": "676567", "vector": [], "sparse_vector": [], "title": "What you need is a good CSI.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Channel State Information (CSI) is foundational for enabling advanced Wi-Fi sensing applications, yet its efficacy is significantly influenced by environmental factors, hardware variations, and noise. This paper introduces a structured framework designed to rigorously evaluate the quality of CSI, thereby enhancing the performance and reliability of Wi-Fi-based sensing systems. Our evaluation system features a multilayered pipeline, where the first layer assesses fundamental CSI characteristics, including packet loss and amplitude consistency over time, to verify data integrity, and the second layer evaluates the compatibility of CSI with specific applications, such as motion detection. Validation of our framework across various chipset samples demonstrates its utility in improving the accuracy and reliability of CSI-derived sensing and potential in refining data quality for data-driven approaches.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697434"}, {"primary_key": "676568", "vector": [], "sparse_vector": [], "title": "MeatSpec: Enabling Ubiquitous Meat Fraud Inspection through Consumer-Level Spectral Imaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Meat adulteration is a significant problem that can pose health risks economic losses to consumers. Current detection methods are hindered by high costs, limited capabilities, or time-consuming sample preparation, making them only accessible in laboratory tests and can not protect the safety of end-users. This paper introduces MeatSpec, a low-cost and user-friendly system for detecting meat adulteration using spectral imaging, to move the adulteration inspection out of laboratories. MeatSpec employs a multispectral camera to reduce costs while quickly capturing spectral images, but this leads to a decrease in spectral resolution and coverage. To solve this challenge, the system uses spectral reconstruction technology and innovative designs tailored for meat adulteration detection. This includes involving adulteration-related prior information during the reconstruction training phase and incorporating contrastive learning to enlarge the distances among reconstructed samples belonging to various adulteration types. Additionally, we devise distinct feature extractors for different bands based on characteristics of the reconstructed spectra and employ knowledge distillation to mitigate error in full-band reconstructed spectra while capturing features related to adulteration. Experimental evaluations on 347 paired spectral images demonstrate that our system achieves a 91.06% accuracy in detecting multiple adulteration types, merely 7.78% inferior to the expensive professional solution, yet 21.58% superior to the baseline at the same price point.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690666"}, {"primary_key": "676569", "vector": [], "sparse_vector": [], "title": "3in1: System Co-Design for Wireless Power Transfer, Wireless Clocking, and Downlink Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There has been a trend in passive IoT systems to move complexity from tag to reader for delivery of power, clock, and data [1, 2]. However, current designs either cannot deliver all these three simultaneously, or fail to do so in a co-optimized way, which leads to poor operating range and long charging time of passive IoT systems. To address this key bottleneck, we present 3in1, the first system co-design that optimizes Wireless Power Transfer (WPT) efficiency, accurate wireless clocking, and downlink communication simultaneously. We propose several novel designs: (i) an optimized multi-tone WPT waveform compliant with ISM band and FCC regulations; (ii) a robust and accurate wireless clocking mechanism integrated within the optimized waveform; (iii) a multi-tone Binary Phase Shift Keying (BPSK) downlink modulation scheme that maintains wireless clocking accuracy and WPT efficiency; and (iv) an ultra-low power receiver capable of simultaneously extracting power, clock, and data from the optimized waveform. Preliminary results demonstrate that higher WPT efficiency, precise wireless clocking, and downlink communication can be achieved simultaneously within a co-optimized waveform. We believe this paves the way for the next generation of passive IoT system design.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697442"}, {"primary_key": "676570", "vector": [], "sparse_vector": [], "title": "Ultra-WideBand Backscatter Towards Multipath-resilient Passive IoT Localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Passive IoT localization technology offers significant advantages across various applications due to its self-sustaining, maintenance-free, and low-cost nature. However, existing systems cannot effectively address the multipath problem in real-world deployments, leading to unreliable localization results in the severe multipath scenario. We propose to fill the gap with UWB2, the first ultra-wideband backscatter passive IoT system that can achieve centimeter-level 99th percentile accuracy even under severe multipath environments. The demo will illustrate UWB2's performance in localization accuracy, latency, operating range, and power consumption in severe multipath scenarios.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698844"}, {"primary_key": "676571", "vector": [], "sparse_vector": [], "title": "RadCloud: Real-Time High-Resolution Point Cloud Generation Using Low-Cost mmWave Radars for Aerial and Ground Vehicles.", "authors": ["<PERSON>", "Shaocheng Luo", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate RadCloud, a real-time framework for obtaining high-resolution lidar-like 2D point clouds from low-resolution millimeter-wave (mmWave) radar data on resource-constrained platforms commonly found on unmanned aerial and ground vehicles (UAVs and UGVs). Such point clouds can then be used for mapping key features of the environment, route planning and navigation, and other robotics tasks. Rad-Cloud is specifically optimized for UAVs and UGVs by using a radar configuration with 1/4th the range resolution, using a model with 2.25× fewer parameters, and reducing total sensing time by a factor of 250×. The real-time ROS framework will be demonstrated on a UGV and UAV equipped with CPU-only compute platforms in diverse environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698849"}, {"primary_key": "676572", "vector": [], "sparse_vector": [], "title": "Assessing Backscatter Link Quality Through Canopy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backscatter communication is well-suited for agriculture, forest sensing, and environmental monitoring due to its low power and maintenance requirements. It can support a range of applications, including reading sensor values such as soil moisture or nutrient levels, monitoring plant health, tracking animal movements, or detecting forest fires. In these settings, sensors and backscatter nodes are often placed deep in the forest or under the plant canopy, while the reader is positioned on a mobile node such as a drone flying above the canopy. Consequently, signal attenuation caused by canopy blockage remains a significant challenge. This paper explores the feasibility of using backscatter communication in such environments and argues that signal-to-noise ratio (SNR) is not the most reliable metric for evaluating link quality. Instead, we propose a new metric, called template correlation, which provides a more accurate assessment in low-SNR conditions. Our findings demonstrate that backscatter communication, with its low power consumption and minimalist hardware design at the tags, is effective for environmental monitoring, even in the presence of canopy attenuators like grasses or crops.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697428"}, {"primary_key": "676573", "vector": [], "sparse_vector": [], "title": "FlexLink Demo: Flexible Frequency-Dependent Multi-Beamforming with Delay-Phased Array.", "authors": ["<PERSON><PERSON>", "YungYi Sun", "<PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate FlexLink, a novel frequency-dependent multi-beamforming system designed to optimize spectral efficiency in millimeter-wave networks. Traditional phased arrays offer limited support for simultaneous control and data signals, leading to inefficiencies. FlexLink addresses this by using a delay-phase antenna array to produce high-gain beams for both control and data, nearly doubling spectral efficiency for data signals. Experiments demonstrate FlexLink's ability to utilize the entire frequency spectrum effectively, by radiating different frequencies corresponding to control and data signals to different directions concurrently.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698869"}, {"primary_key": "676574", "vector": [], "sparse_vector": [], "title": "Rethinking Channel Coding for Wi-Fi Backscatter Networking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most Wi-Fi backscatter systems have been proposed with mechanisms that decode the data from backscatter tags based on XOR operations. These XOR-based mechanisms require two receivers listening on the Wi-Fi and backscatter channels and consider the channel coding process as an obstacle to decode the backscatter packets. We focus on channel coding, which is a technique that all Wi-Fi systems use to detect and correct errors from the wireless channels. In our backscatter system, the tag reflects ambient Wi-Fi signals at the symbol level to transmit tag data. When the receiver demodulates and decodes these reflected signals, certain patterns of errors can be observed because of the symbol and subcarrier structures according to the OFDM scheme in Wi-Fi. We propose a method for detecting bit errors caused by backscatter tags and investigate the possibility of utilizing them in decoding.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697431"}, {"primary_key": "676575", "vector": [], "sparse_vector": [], "title": "Robust In-Car Child Presence Detection using Commercial WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "In-car child presence detection (CPD) has become a crucial vehicular safety measure to prevent heat-related injuries or fatalities of unattended children. However, existing CPD solutions based on sensors and cameras have limitations in accuracy, coverage, and additional cost. To address these challenges, leveraging existing in-car WiFi facilities has emerged as a promising solution. Nevertheless, current WiFi-based CPD solutions are not robust against environmental impacts and often generate unnecessary detection alerts when an adult is present in the car. In this demonstration, we propose a novel, robust CPD architecture designed to enhance performance under various conditions while minimizing false alarms. Our design incorporates three main systems: presence detection, seat localization, and child vs. adult classification. To validate the system's performance, we developed a prototype using existing WiFi devices operating at 5 GHz and conducted a series of experiments. Please find the project page with the URL: https://sites.google.com/originwirelessai.com/in-car-cpd.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698864"}, {"primary_key": "676576", "vector": [], "sparse_vector": [], "title": "Dual-Function Waveform Design via W-ADPM.", "authors": ["Wenshuai Ji", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Biao Tian", "<PERSON><PERSON>"], "summary": "This paper proposes a novel design algorithm for dual-function radar communication (DFRC) Orthogonal Frequency-Division Multiplexing (OFDM) waveform. The algorithm achieves a tradeoff between detection performance and communication bit error rate (BER) performance. Firstly, the algorithm modulates communication information on the phase of the subcarrier coefficient of OFDM waveform using M-phase-shift keying (MPSK) schemes such as Binary-PSK (BPSK). Subsequently, the waveform minimises the weighted integrated sidelobe level (WISL) of the transmit waveform and the receive mismatch filter while ensuring the BER. Additionally, constraints are placed on constant amplitude, mainlobe energy and signal-to-noise ratio (SNR) loss. To address the non-convex optimization issues arising from algorithm design, a Weight Alternating Direction Method of Penalty (W-ADPM) network-based approach simultaneously optimises the transmit waveform and receives mismatched filters. The simulation experiments demonstrate that the proposed algorithm has better convergence performance for the proposed waveform compared to the Alternating Direction Method of Multipliers (ADMM) algorithm. Besides, compared to traditional matched filters, the jointly transmitted and received mismatched filters proposed in this paper provide better WISL cross-correlation performance while ensuring the BER.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698227"}, {"primary_key": "676577", "vector": [], "sparse_vector": [], "title": "MIMO-OFDM Waveform Optimization for Sparse Dual-Function-Radar-Communication System.", "authors": ["Wenshuai Ji", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Biao Tian"], "summary": "Dual-function radar-communication (DFRC) systems offer a promising solution to mitigate spectrum competition and hardware complexity in 5G/6G communication. Orthogonal Frequency Division Multiplexing (OFDM) technology has been commonly employed in current DFRC signals. However, many DFRC signal sequences exhibit poor range sidelobes, making them unsuitable for weak target detection. In this paper, we design encrypted sparse transmitting waveforms to encrypt signals. In the time domain, the DFRC signal's Peak Side Level (DPSL) is minimized to enhance radar detectability, while simultaneously constraining the communication Bit Error Ratio (BER) and the constant envelope value of the signal to maintain communication quality. To address the non-convex optimization problem, we develop a Block Successive Upper-bound Minimization (BSUM) framework, which alternately updates each communication phase location. This framework aims to lower the dual-function cross- and auto-correlation peak sidelobe levels, referred to as the Block Successive Upper bound Minimization for DFRC DPSL (BSUM-DPSL) algorithm. The proposed algorithm's effectiveness is theoretically validated, and simulation results demonstrate that the effectiveness of designed MIMO-OFDM waveform in comparison with other waveforms.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698226"}, {"primary_key": "676578", "vector": [], "sparse_vector": [], "title": "ZEROECG: Zero-Sensation ECG Monitoring By Exploring RFID MOSFET.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "ECG monitoring during human activities is crucial since many heart attacks occur when people are exercising, driving a car, operating a machine, etc. Unfortunately, existing ECG monitoring devices fail to timely detect abnormal ECG signals during activities due to the need for many cables or a sustained press on devices (e.g., smartwatches). This paper introduces ZeroEcg, a wireless, battery-free, lightweight, electronic-skin-like tag integrated with commodity RFIDs, which can continuously track a user's ECG during activities. By exploring and leveraging the RFID MOSFET switch, which is traditionally used for backscatter modulation, we map the ECG signal to the RFID RSS and phase measurement. It opens a new RFID sensing approach for sensing any physical world variable that can be translated into voltage signals. We model and analyze the RFID MOSFET-based backscatter modulation principle, providing design guidance for other sensing tasks. Real-world results illustrate the effectiveness of ZeroEcg on ECG sensing.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690690"}, {"primary_key": "676579", "vector": [], "sparse_vector": [], "title": "A Robust Beamforming for Intergretd Sensing and Communications Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Junsheng Mu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a robust beamforming design methodology for integrated sensing and communications beamforms. The beamforming design aims to address channel uncertainties in the communication system by optimizing the sensing beamform. Assuming the Channel State Information (CSI) error is elliptically bounded, we investigate the robust integrated sensing and communication (ISAC) beamforming design problem, focusing on minimizing the Cramér-Rao bound (CRB) under a signal-to-noise ratio (SINR) threshold constraint. The problem is addressed through distributed optimization using the S-procedure and solved with the SDR method. Simulation results verify the effectiveness of the proposed CRB_min method.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698222"}, {"primary_key": "676580", "vector": [], "sparse_vector": [], "title": "Wireless Latency Shift Keying.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "IEEE 802.11 (WiFi) only has two modes of trust---complete trust or complete untrust. The lack of nuance leaves no room for sensors that a user does not fully trust but wants to connect to their network, such as a WiFi sensor. Solutions exist, but they require advanced knowledge of network administration. We solve this problem by introducing a new way of modulating data in the latency of the network, called Latency Shift Keying. We use specific characteristics of the WiFi protocol to carefully control the latency of just one device on the network. We build a transmitter, receiver, and modulation scheme that is designed to encode data in the latency of a network. We develop an application, Wicket, that solves the WiFi trust issue using Latency Shift Keying to create a new security association between an untrusted WiFi sensor and a wired device on the trusted network. We evaluate its performance and show that it works in many network conditions and environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649373"}, {"primary_key": "676581", "vector": [], "sparse_vector": [], "title": "BeamArmor5G: Demonstrating MIMO Anti-Jamming and Localization with srsRAN 5G Stack.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid expansion of wireless technologies demands effective management of complex RAN for MIMO in 5G systems. Current architectures and RAN Intelligent Controllers (RICs) lack critical real-time data processing and physical layer functionalities, hindering MIMO advancements. We introduce BeamArmor5G, an open-source MIMO App development framework that addresses these gaps with comprehensive PHY layer capabilities, enabling detailed wireless channel measurement streaming from RAN to the controller. BeamArmor5G is implemented with srsRAN to empower open-source community researchers to advance beamforming, localization, and interference management, significantly enhancing network performance and enabling new 5G applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698870"}, {"primary_key": "676582", "vector": [], "sparse_vector": [], "title": "MIMO-RIC: RAN Intelligent Controller for MIMO xApps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The adoption of MIMO technology in wireless networks enhances spectral efficiency and enables novel functionalities such as wireless sensing and localization. These functionalities can be enabled by Open-RAN architecture to provide high computation, memory, and data-driven inference through a RAN Intelligent Controller (RIC). However, existing RICs focus mainly on higher network layers and lack essential PHY layer functionalities for MIMO. We present MIMO-RIC, an open-source RAN intelligent controller tailored for MIMO applications. We enable the streaming of extensive 3D wireless channel measurements across antennas, subcarriers, and time, from RAN to MIMO-RIC to develop various MIMO apps like beamforming and localization. We implemented MIMO-RIC on the srsRAN open-source platform using ZeroMQ messaging system for efficient, low-latency communication. Our over-the-air experiment setup consisting of USRP radios and commercial user equipment demonstrates effective jammer monitoring, nulling, and user localization applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701548"}, {"primary_key": "676583", "vector": [], "sparse_vector": [], "title": "Your Data, Your Model: A Framework for Training and Deploying Foundational Language Models for Embedded Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large language models are being deployed for various applications, and they are being scaled to larger parameter sizes. However, this makes it challenging to use these models for embedded applications due to the limited computing memory and processing capabilities available on embedded platforms. Consequently, it limits the usage of these models to invocation through function calls to a remotely hosted platform, which introduces latency and privacy challenges. A smaller parameter-sized language model running locally on embedded platforms could mitigate this challenge. However, such smaller models make trade-offs with dataset diversity and parameters, leading to inaccurate prompt responses and high hallucination rates. In response, we present our ongoing work: OTTER, a framework for pre-training custom language models and fine-tuning language models for embedded applications. We observe that smaller parameter-sized models outperform larger ones when curated with appropriate pre-training and fine-tuning datasets. Their smaller size allows these models to perform inference on constrained embedded platforms. Building on this framework, we train a custom model for breathing detection and observe high accuracy.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697466"}, {"primary_key": "676584", "vector": [], "sparse_vector": [], "title": "A Framework for Training and Deploying Foundational Language Models for Embedded Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large language models have attracted a significant recent interest, with an overwhelming efforts and emphasis on scaling their parameter size to support general-purpose and emergent capabilities. However, memory and processing requirements for model inference also scales proportionally with the model's parameter size. This makes it challenging for state-of-the-art, larger models to perform inference locally on edge and mobile devices, even though such models could benefit numerous tasks on these systems. Consequently, these devices are often restricted to accessing larger models through network calls. This approach introduces challenges related to increased inference latency due to network delays and provider capacity. It also raises concerns about sharing private information with third-party vendors. To address these issues, we are developing a system called OTTER. This system tackles the particular problem by enabling the training of smaller yet highly capable foundational language models. As a result of the reduced parameter size, these models can run locally even on constrained edge devices, such as mobile phones and wearables, and are able to provide low-latency responses compared with their larger remotely hosted counterparts. We present our ongoing work describing the framework as applied to training a smaller foundational model for embedded sensing application for tracking a person's breathing with high accuracy comparable to models orders of magnitude larger in size. Our results demonstrate that carefully pre-trained and fine-tuned smaller sized models outperform much larger counterparts for some tasks, while inferring locally on the constrained edge and mobile devices.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695901"}, {"primary_key": "676585", "vector": [], "sparse_vector": [], "title": "Game-Theoretic Power Allocation for Network Slicing in NOMA-Based 5G Networks: A Stackelberg Approach.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper investigates power allocation optimization in 5G networks utilizing Non-Orthogonal Multiple Access (NOMA) and network slicing, with a focus on Ultra-Reliable Low-Latency Communication (URLLC) and Enhanced Mobile Broadband (eMBB) slices. Addressing the challenge of reducing latency in the URLLC slice while boosting throughput in the eMBB slice, a Stackelberg game-theoretic approach is applied to model the hierarchical interactions between network slices. The base stations (BS) in the URLLC and eMBB slices act as leaders, and the users as followers. The study's findings reveal that the proposed method effectively minimizes latency in the URLLC slice and improves throughput in the eMBB slice, highlighting the efficacy of the <PERSON><PERSON><PERSON>berg algorithm in managing power distribution to meet diverse network slice requirements.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695907"}, {"primary_key": "676586", "vector": [], "sparse_vector": [], "title": "LightPure: Realtime Adversarial Image Purification for Mobile Devices Using Diffusion Models.", "authors": ["<PERSON><PERSON><PERSON>", "Seongbin Park", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Autonomous mobile systems increasingly rely on deep neural networks for perception and decision-making. While effective, these systems are vulnerable to adversarial machine learning attacks where small perturbations in the input could significantly impact the outcome of the system. Common countermeasures include leveraging adversarial training and/or data or network transformation. Although widely used, the main drawback of these countermeasures is that they require full and invasive access to the classifiers, which are typically proprietary. Additionally, the cost of training or retraining is often prohibitively expensive for large models. To tackle this, purification models have recently been proposed. The aim is to incorporate a \"purification\" layer before classification, thereby eliminating the necessity to modify the classifier. Despite their effectiveness, state-of-the-art purification methods are compute-intensive, rendering them unsuitable for mobile systems where resources are constrained and large latency is not desired.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690684"}, {"primary_key": "676587", "vector": [], "sparse_vector": [], "title": "Enabling Visual Recognition at Radio Frequency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces PanoRadar, a novel RF imaging system that brings RF resolution close to that of LiDAR, while providing resilience against conditions challenging for optical signals. Our LiDAR-comparable 3D imaging results enable, for the first time, a variety of visual recognition tasks at radio frequency, including surface normal estimation, semantic segmentation, and object detection. PanoRadar utilizes a rotating single-chip mmWave radar, along with a combination of novel signal processing and machine learning algorithms, to create high-resolution 3D images of the surroundings. Our system accurately estimates robot motion, allowing for coherent imaging through a dense grid of synthetic antennas. It also exploits the high azimuth resolution to enhance elevation resolution using learning-based methods. Furthermore, PanoRadar tackles 3D learning via 2D convolutions and addresses challenges due to the unique characteristics of RF signals. Our results demonstrate PanoRadar's robust performance across 12 buildings. Code, datasets, and demo videos are available on our website.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649369"}, {"primary_key": "676588", "vector": [], "sparse_vector": [], "title": "Demo: Enabling Visual Recognition at Radio Frequency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This demo presents PanoRadar, a novel RF imaging system that brings RF resolution close to that of LiDAR, while providing resilience against conditions challenging for optical signals. Our LiDAR-comparable 3D imaging results enable, for the first time, a variety of visual recognition tasks at radio frequency, including surface normal estimation, semantic segmentation, and object detection. PanoRadar utilizes a rotating single-chip mmWave radar, along with a combination of novel signal processing and machine learning algorithms, to create high-resolution 3D images of the surroundings. Our system accurately estimates robot motion, allowing for coherent imaging through a dense grid of synthetic antennas. It also exploits the high azimuth resolution to enhance elevation resolution using learning-based methods. Furthermore, PanoRadar tackles 3D learning via 2D convolutions and addresses challenges due to the unique characteristics of RF signals. This demonstration illustrates the ability of high-resolution RF imaging of PanoRadar. We present the signal processing results to show the high range and azimuth resolution of the system, and the machine learning results for elevation resolution enhancement. Code, datasets, and demo videos are available on our website.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698859"}, {"primary_key": "676589", "vector": [], "sparse_vector": [], "title": "MELTing Point: Mobile Evaluation of Language Transformers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Transformers have recently revolutionized the machine learning (ML) landscape, gradually making their way into everyday tasks and equipping our computers with \"sparks of intelligence\". However, their runtime requirements have prevented them from being broadly deployed on mobile. As personal devices become increasingly powerful at the consumer edge and prompt privacy becomes an ever more pressing issue, we explore the current state of mobile execution of Large Language Models (LLMs). To achieve this, we have created our own automation infrastructure, MELT, which supports the headless execution and benchmarking of LLMs on device, supporting different models, devices and frameworks, including Android, iOS and Nvidia Jetson devices. We evaluate popular instruction fine-tuned LLMs and leverage different frameworks to measure their end-to-end and granular performance, tracing their memory and energy requirements along the way.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690668"}, {"primary_key": "676590", "vector": [], "sparse_vector": [], "title": "MobileGPT: Augmenting LLM with Human-like App Memory for Mobile Task Automation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Oh", "Insik Shin"], "summary": "The advent of large language models (LLMs) has opened up new opportunities in the field of mobile task automation. Their superior language understanding and reasoning capabilities allow users to automate complex and repetitive tasks. However, due to the inherent unreliability and high operational cost of LLMs, their practical applicability is quite limited. To address these issues, this paper introduces MobileGPT1, an innovative LLM-based mobile task automator equipped with a human-like app memory. MobileGPT emulates the cognitive process of humans interacting with a mobile app---explore, select, derive, and recall. This approach allows for a more precise and efficient learning of a task's procedure by breaking it down into smaller, modular sub-tasks that can be re-used, re-arranged, and adapted for various objectives. We implement MobileGPT using online LLMs services (GPT-3.5 and GPT-4) and evaluate its performance on a dataset of 185 tasks across 18 mobile apps. The results indicate that MobileGPT can automate and learn new tasks with 82.7% accuracy, and is able to adapt them to different contexts with near perfect (98.75%) accuracy while reducing both latency and cost by 62.5% and 68.8%, respectively, compared to the GPT-4 powered baseline.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690682"}, {"primary_key": "676591", "vector": [], "sparse_vector": [], "title": "Panopticus: Omnidirectional 3D Object Detection on Resource-constrained Edge Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "3D object detection with omnidirectional views enables safety-critical applications such as mobile robot navigation. Such applications increasingly operate on resource-constrained edge devices, facilitating reliable processing without privacy concerns or network delays. To enable cost-effective deployment, cameras have been widely adopted as a low-cost alternative to LiDAR sensors. However, the compute-intensive workload to achieve high performance of camera-based solutions remains challenging due to the computational limitations of edge devices. In this paper, we present Panopticus, a carefully designed system for omnidirectional and camera-based 3D detection on edge devices. Panopticus employs an adaptive multi-branch detection scheme that accounts for spatial complexities. To optimize the accuracy within latency limits, <PERSON><PERSON><PERSON> dynamically adjusts the model's architecture and operations based on available edge resources and spatial characteristics. We implemented Panopticus on three edge devices and conducted experiments across real-world environments based on the public self-driving dataset and our mobile 360° camera dataset. Experiment results showed that <PERSON><PERSON><PERSON> improves accuracy by 62% on average given the strict latency objective of 33ms. Also, <PERSON>op<PERSON> achieves a 2.1× latency reduction on average compared to baselines.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690688"}, {"primary_key": "676592", "vector": [], "sparse_vector": [], "title": "Emulating GFSK Modulation for Wi-Fi-to-BLE Multicast Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cross technology communication (CTC) facilitates direct communication between heterogeneous wireless technologies in the overlapped frequencies such as the 2.4 GHz ISM band. In this poster, we propose a novel method named WBMC for direct multicast communication from a Wi-Fi device to multiple BLE transceivers. This is achieved by making a Wi-Fi signal appear like BLE's Gaussian frequency shift keying (GFSK) signal over multiple subcarrier groups of Wi-Fi. Uniqueness of WBMC compared to prior Wi-Fi-to-BLE CTC studies is that one Wi-Fi transmission can deliver distinct data to multiple BLE receivers simultaneously. The potential and feasibility of the newly suggested approach is demonstrated through implementation on GNURadio.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697443"}, {"primary_key": "676593", "vector": [], "sparse_vector": [], "title": "Demo: Achieving Self-Interference Cancellation Across Different Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In order to enable the simultaneous transmission and reception of wireless signals on the same frequency, a full-duplex (FD) radio must be capable of suppressing the powerful self-interference (SI) signal emitted from the transmitter and picked up by the receiver. Critically, a major bottleneck in wideband FD deployments is the need for adaptive SI cancellation (SIC) that would allow the FD wireless system to achieve strong cancellation across different settings with distinct electromagnetic environments. In this work, we evaluate the performance of an adaptive wideband FD radio in three different locations and demonstrate that it achieves strong SIC in every location across different bandwidths.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698852"}, {"primary_key": "676594", "vector": [], "sparse_vector": [], "title": "Plug-and-play Indoor GPS Positioning System with the Assistance of Optically Transparent Metasurfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Due to the poor indoor coverage and positioning accuracy, existing indoor GPS positioning systems leverages additional RF infrastructure as relay with known position. However, in practice, learning the relay position requires establishing an additional connection between user and relays, which is user unfriendly and even infeasible. In this paper, we propose GPSWindow, a plug-and-play indoor GPS positioning system without the prior knowledge of the relay position. By attaching optically transparent metasurfaces to windows, GPSWindow focuses the incident signal towards determined direction and provide an indoor continuous GPS signal coverage. We exploit the difference between consecutive satellite measurements and the Doppler shift measurements to recover the satellite-to-user true distance from the measured satellite-metasurface-user distance, and then locate the user using the traditional trilateration positioning method, eliminating the requirement of relay position. We also design an error correction method that leverages IMU on smartphone and the Doppler shift information to enhance GPSWindow in mobile scenarios. Extensive real-world experiments demonstrate that GPSWindow can provide continuous position service and achieve median positioning accuracy of 3.6m in indoor environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690667"}, {"primary_key": "676595", "vector": [], "sparse_vector": [], "title": "Enabling On-Demand Low-Power mmWave Repeaters via Passive Beamforming.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advancements in computing have enabled emerging applications such as telesurgery, robot automation, holographic telepresence, and extended reality, which require gigabitper-second throughput, sub-millisecond latency, and highly reliable wireless connectivity. Millimeter wave (mmWave) technology has promised to enable such connectivity by operating over a large bandwidth in the high-frequency spectrum bands (24 GHz and above). However, due to the short wavelength and high directionality of mmWave signals, mmWave networks have limited coverage and are highly susceptible to blockage. In particular, high-data-rate mmWave networks work reliably only when there is a clear line-of-sight (LOS) path between users and base stations. Unfortunately, due to this problem, mmWave networks have not been able to scale and become ubiquitous. Past work has proposed mmWave repeaters and intelligent surfaces to solve this issue by rerouting signal around blockages. However, these solutions are expensive and complex to build, consume high power, or/and require constant feedback from the network to operate since they use active techniques for beam steering. In this paper, we present the first mmWave repeater which uses passive beamforming technique. Our repeater is low-cost, low-power, and can support multiple users simultaneously. Most importantly, it does not require any feedback from the network to operate. Hence, it can be easily deployed on-demand to solve the coverage and blockage problem of mmWave networks whenever and wherever high-data-rate and low-latency connectivity is needed.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649385"}, {"primary_key": "676596", "vector": [], "sparse_vector": [], "title": "Federated Black-box Prompt Tuning System for Large Language Models on the Edge.", "authors": ["<PERSON><PERSON>", "Jingwei Sun", "<PERSON><PERSON>", "Yuan<PERSON> Zhang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>gua<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) offers a privacy-preserving way to train models across decentralized data. However, fine-tuning pre-trained language models (PLMs) in FL is challenging due to restricted model parameter access, high computational demands, and communication overheads. Our method treats large language models (LLMs) as black-box inference APIs, optimizing prompts with gradient-free methods. This approach, FedBPT, reduces exchanged variables, boosts communication efficiency, and minimizes computational and memory costs. We demonstrate the practical implementation of FedBPT on resource-limited edge devices, showcasing its ability to efficiently achieve collaborative on-device LLM fine-tuning.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698856"}, {"primary_key": "676597", "vector": [], "sparse_vector": [], "title": "Proximal Federated Learning for Body Mass Index Monitoring using Commodity WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Body Mass Index (BMI) is a critical metric for assessing public health and identifying populations at risk for obesity-related conditions. Traditional BMI monitoring methods often raise privacy concerns and require active cooperation from individuals, limiting their applicability in real-world scenarios. This paper introduces a novel approach to BMI monitoring that leverages proximal federated learning (PFL) using commodity WiFi devices. Our method addresses the challenges of data heterogeneity and intermittent connectivity in FL. By our approach, the Adaptive Elastic Stochastic Alternating Direction Method of Multipliers (AESADMM), an optimization algorithm designed to handle data heterogeneity and intermittent connectivity in FL scenarios, our system collects Channel State Information (CSI) from WiFi signals to passively classify BMI based on the impact of different body shapes on signal propagation. This approach ensures privacy preservation and eliminates the need for active participant involvement. Theoretical analysis and empirical results demonstrate the superior accuracy, reduced communication costs, and enhanced scalability of our proposed method compared to existing personalized FL frameworks, showcasing its potential as an effective tool for large-scale BMI monitoring in diverse environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694735"}, {"primary_key": "676598", "vector": [], "sparse_vector": [], "title": "PixelGen: Rethinking Embedded Cameras for Mixed-Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mixed-reality headsets offer new ways to perceive our environment. They employ visible spectrum cameras to capture and display the environment on screens in front of the user's eyes. However, these cameras lead to limitations. Firstly, they capture only a partial view of the environment. They are positioned to capture whatever is in front of the user, thus creating blind spots during complete immersion and failing to detect events outside the restricted field of view. Secondly, they capture only visible light fields, ignoring other fields like acoustics and radio that are also present in the environment. Finally, these power-hungry cameras rapidly deplete the mixed-reality headset's battery. We introduce PixelGen to rethink embedded cameras for mixed-reality headsets. PixelGen proposes to decouple cameras from the mixed-reality headset and balance resolution and fidelity to minimize the power consumption. It employs low-resolution, monochrome image sensors and environmental sensors to capture the surroundings around the headset. This approach reduces the system's communication bandwidth and power consumption. A transformer-based language and image model process this information to overcome resolution trade-offs, thus generating a higher-resolution representation of the environment. We present initial experiments that show PixelGen's viability.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696216"}, {"primary_key": "676599", "vector": [], "sparse_vector": [], "title": "VLocSense: Integrated VLC System for Indoor Passive Localization and Human Sensing.", "authors": ["Jiarong Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Weihua Gui", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The demand for accurate and real-time indoor localization and human sensing is rising with the development of smart environments, with applications in security and smart homes. Effective systems enhance safety, energy efficiency, and user experience by leveraging existing infrastructure, reducing deployment costs, and integrating seamlessly. Traditional methods rely on dedicated hardware, while communication or lighting infrastructure can provide dual-purpose solutions. This research focuses on Visible Light Communication (VLC) technology, which uses visible light for data transmission. Our VLC system utilizes existing lighting infrastructure to transmit data, providing localization and human sensing functionalities. The system design strategically incorporates specific VLC transmitters and receivers to enhance sensing performance. The collected data is processed using advanced algorithms and machine learning models, ensuring robust, real-time, and cost-effective localization with an accuracy of 96.6% and human activity recognition with an accuracy of 98.3%. This multi-functionality system demonstrates the potential for VLC in healthcare monitoring and home automation applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694729"}, {"primary_key": "676600", "vector": [], "sparse_vector": [], "title": "Lightweight Deep Learning for AoA-Based 5G Multi-Source Localization in Low SNR Conditions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingkang Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In future mobile networks, the demand for real-time, accurate localization of multiple signal sources is paramount, but the facilities are often resource-constrained and the deploying environments are complex. In this context, we present a lightweight deep neural network in this work, which is tailored for multi-source angle-of-arrival (AoA) estimation under low signal-to-noise-ratio (SNR) conditions. The network employs mobile inverted bottleneck convolution (MBConv), known for its enhanced feature extraction capabilities and resilience to noise. By leveraging a scale attention mechanism, we effectively integrate the outputs of each layer without the need for neural architecture search. Trained on multi-channel data under low SNR, the network formulates angle estimation as a multi-label classification task. Experimental results confirm that, the proposed network demonstrates superior accuracy in extreme noise conditions and with limited snapshots, outperforming existing methodologies in multi-source scenarios.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698219"}, {"primary_key": "676601", "vector": [], "sparse_vector": [], "title": "FlexNN: Efficient and Adaptive DNN Inference on Memory-Constrained Edge Devices.", "authors": ["Xiangyu Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the popularity of deep neural networks (DNNs) and considerations over network overhead, data privacy, and inference latency, there is a growing interest in deploying DNNs to edge devices in recent years. However, the limited memory becomes a major bottleneck for on-device DNN deployment, making it crucial to reduce the memory footprint of DNN. The mainstream model customization solutions require intensive deployment efforts and may lead to severe accuracy degradation, and existing deep learning (DL) frameworks don't take memory as a priority. Besides, recent works to enhance the memory management scheme cannot be directly applied because of several challenges, including the unbalanced memory footprint across layers, the inevitable overhead of memory management, and the memory budget dynamicity. To tackle these challenges, we introduce FlexNN, an efficient and adaptive memory management framework for DNN inference on memory-constrained devices. FlexNN uses a slicing-loading-computing joint planning approach, to achieve optimal memory utilization and minimal memory management overhead. We implemented FlexNN atop NCNN, and conducted comprehensive evaluations with common model architectures on various devices. The results have shown that our approach is able to adapt to different memory constraints with optimal latency-memory trade-offs. For example, FlexNN can reduce the memory consumption by 93.81% with only a 3.64% increase in latency, as compared with the original NCNN on smartphones.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649391"}, {"primary_key": "676602", "vector": [], "sparse_vector": [], "title": "Stable Hierarchical Routing for Operational LEO Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low Earth Orbit (LEO) satellite mega-constellations promise ubiquitous network services to \"unconnected\" users. But their upcoming global routing for Earth will be unstable due to exhaustive topology updates between satellites and Earth, inside an orbital shell, and across heterogeneous orbital shells. In real LEO networks, these multi-dimensional dynamics are interleaved and complicated by chaotic orbital maneuvers and random failures. They are less predictable than most satellite routing proposals expect and threaten these proposals' availability, efficiency, or resiliency at scale.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649362"}, {"primary_key": "676603", "vector": [], "sparse_vector": [], "title": "&quot;My WiFi is not working!&quot; Augmenting Wireless Awareness in Consumers via XR.", "authors": ["Qiancheng Li", "Xinghua Sun", "<PERSON><PERSON><PERSON>"], "summary": "A general consumer around the world gets general awareness about the causes of mechanical, electrical or digital problems in their house (or neighborhood) and how to fix them through common life experiences or learning from their peers. However, despite similar level of integration into everyday life, the understanding of wireless technologies lags significantly. This bottleneck leads to consumer dissatisfaction and reduced profits for cellular providers.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698847"}, {"primary_key": "676604", "vector": [], "sparse_vector": [], "title": "&quot;My WiFi is not working!&quot; Augmenting Wireless Awareness in Consumers via XR.", "authors": ["Qiancheng Li", "Xinghua Sun", "<PERSON><PERSON><PERSON>"], "summary": "A general consumer around the world gets general awareness about the causes of mechanical, electrical or digital problems in their house (or neighborhood) and how to fix them through common life experiences or learning from their peers. However, despite similar level of integration into everyday life, the understanding of wireless technologies lags significantly. This bottleneck leads to consumer dissatisfaction and reduced profits for cellular providers.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695900"}, {"primary_key": "676605", "vector": [], "sparse_vector": [], "title": "Demo : Synthetic Data for Data-Driven Wireless.", "authors": ["Qiancheng Li", "Xinghua Sun", "<PERSON><PERSON><PERSON>"], "summary": "The fundamental bottleneck in adapting data-driven wireless solutions to real world is the lack of tools for augmenting good quality data which is environment-specific. Indeed, much of the data required for popular data-driven wireless communication and sensing systems requires domain expertise beyond the reach of an average consumer. This demo presents a radical new vision for generating synthetic data in consumer-specific environments by leveraging the power of modern compute.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701545"}, {"primary_key": "676606", "vector": [], "sparse_vector": [], "title": "The Architecture of AI and Communication Integration towards 6G: An O-RAN Evolution.", "authors": ["Zexu Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The evolution of communication architecture shifts towards virtualization and cloud-native network functions, setting the stage for the flexibility and integration of emerging technologies. Artificial Intelligence (AI) and Machine Learning (ML) as intrinsic elements in network design are some of the crucial visions and requirements for 6G. This paper, from the perspective of O-RAN, explores how current network architectures should evolve towards the integration of communication and intelligence in 6G. It begins with a comprehensive analysis and comparison of the AI-related work conducted by various standard organizations. Building on this, an end-to-end AI integration framework is proposed, which leverages AI technologies, data services, and digital twin (DT) technologies to achieve an integrated intelligent 6G communication system. After that, the key enabling technologies for cross-domain AI, service-based RAN, programmable RAN and digital twins are discussed. At last, the paper analyzes the challenges and opportunities for O-RAN evolution.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701550"}, {"primary_key": "676607", "vector": [], "sparse_vector": [], "title": "Practical Adversarial Attack on WiFi Sensing Through Unnoticeable Communication Packet Perturbation.", "authors": ["<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON> Du", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hongbo Liu", "<PERSON><PERSON>"], "summary": "The pervasive use of WiFi has driven the recent research in WiFi sensing, converting communication tech into sensing for applications such as activity recognition, user authentication, and vital sign monitoring. Despite the integration of deep learning into WiFi sensing systems, potential security vulnerabilities to adversarial attacks remain unexplored. This paper introduces the first physical attack focusing on deep learning-based WiFi sensing systems, demonstrating how adversaries can subtly manipulate WiFi packet preambles to affect channel state information (CSI), a critical feature in such systems, and thereby influence underlying deep learning models without disrupting regular communication. To realize the proposed attack in practical scenarios, we rigorously analyze and derive the intricate relationship between the pilot symbol and CSI. A novel mechanism is proposed to facilitate quantitive control of receiver-side CSI through minimal modifications to the pilot symbols of WiFi packets at the transmitter. We further develop a perturbation optimization method based on the Carlini & Wagner (CW) attack and a penalty-based training process to ensure the attack's universal efficacy across various CSI responses and noise. The physical attack is implemented and evaluated in two representative WiFi sensing systems (i.e., activity recognition and user authentication) with 35 participants over 3 months. Extensive experiments demonstrate the remarkable attack success rates of 90.47% and 83.83% for activity recognition and user authentication, respectively.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649367"}, {"primary_key": "676608", "vector": [], "sparse_vector": [], "title": "GazeTrak: Exploring Acoustic-based Eye Tracking on a Glass Frame.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "Sicheng Yin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present GazeTrak, the first acoustic-based eye tracking system on glasses. Our system only needs one speaker and four microphones attached to each side of the glasses. These acoustic sensors capture the formations of the eyeballs and the surrounding areas by emitting encoded inaudible sound towards eyeballs and receiving the reflected signals. These reflected signals are further processed to calculate the echo profiles, which are fed to a customized deep learning pipeline to continuously infer the gaze position. In a user study with 20 participants, GazeTrak achieves an accuracy of 3.6° within the same remounting session and 4.9° across different sessions with a refreshing rate of 83.3 Hz and a power signature of 287.9 mW. Furthermore, we report the performance of our gaze tracking system fully implemented on an MCU with a low-power CNN accelerator (MAX78002). In this configuration, the system runs at up to 83.3 Hz and has a total power signature of 95.4 mW with a 30 Hz FPS.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649376"}, {"primary_key": "676609", "vector": [], "sparse_vector": [], "title": "RFMagus: Programming the Radio Environment With Networked Metasurfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The complexity and volatility of real-world radio environments often hamper wireless networks from achieving optimal performance. Recently, intelligent metasurfaces have been explored to dynamically reshape the radio propagation environment. However, existing systems are limited to standalone metasurfaces, only enabling one-time signal redirection/reshaping effects within their direct line-of-sight. They cannot effectively scale to cover larger areas. In this paper, we propose RFMagus, which employs a network of metasurfaces to overcome the limitation. We carefully optimize the configurations of the networked metasurfaces so that they can cooperatively and coherently propagate the analog signals towards the target regions. We have implemented the networked metasurfaces and deployed them in a variety of real-world environments. Experimental results demonstrate that RFMagus can effectively expand the coverage, improve the throughput, and operate transparently to different wireless standards.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649344"}, {"primary_key": "676610", "vector": [], "sparse_vector": [], "title": "MuDiS: An Audio-independent, Wide-angle, and Leak-free Multi-directional Speaker.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper introduces a novel multi-directional speaker, named MuDiS, which utilizes a parametric array to generate highly focused sound beams in multiple directions. The system capitalizes on air nonlinearity to reproduce sound from ultrasounds, successfully overcoming challenges inherent in traditional parametric arrays, such as transducer size and wavefront shape. It supports three important features simultaneously: independent beams, wide-angle digital steering, and unintended leakage suppression. To address these challenges, we designed a specialized cell structure that connects ultrasonic transducers, redirecting an approximately omnidirectional wavefront with optimal interspacing. An optimization-based algorithm is developed to minimize unintended leakages, and a nonlinear distortion reduction scheme is proposed to enhance sound quality. The paper showcases a prototype demonstrating the system's capabilities as a multidirectional speaker with a wide sound projection angle. Experimental results validate the effectiveness of our approach. The proposed multi-beam projection system rivals the performance of commercially available single-beam projection directional speakers, and improved steering angle and sound fidelity compared to multi-beamforming performance using traditional parametric arrays.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649360"}, {"primary_key": "676611", "vector": [], "sparse_vector": [], "title": "FormerReckoning: Physics Inspired Transformer for Accurate Inertial Navigation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although modern localization methods have achieved remarkable accuracy with various sensors, there are still some circumstances where only proprioceptive sensing works (Inertial Navigation). However, localization and navigation using only IMU sensors (costing less than $1000) still face significant challenges such as low accuracy and large cumulative errors when using traditional filter methods. Furthermore, AI-based approaches, while promising, often yield unpredictable and unreliable outputs. This paper proposes FormerReckoning, an inertial localization estimation framework for wheeled robotics that incorporates physical prompts into a Transformer framework to enhance translation estimation accuracy. Our tests show that FormerReckoning not only reduces mean translation errors to 0.72% but also surpasses all baseline models in performance, demonstrating its potential to provide reliable and precise localization in a cost-effective manner.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694736"}, {"primary_key": "676612", "vector": [], "sparse_vector": [], "title": "LVSC: A Lightweight Video Semantic Communication Method over Wireless Channel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a lightweight video semantic communication (LVSC) method to enable end-to-end wireless video transmissions. The proposed LVSC method cuts down the model parameters through designing a spatial pyramid structure for the residual codec and a one-dimensional CNN-based channel attention model for the joint source-channel coding (JSCC) codec, respectively. Additionally, the Swin Transformer is introduced to the JSCC codec to accelerate the semantic coding. Extensive experiments validate that the proposed LVSC performs better than the baseline methods in terms of PSNR and MS-SSIM while inferring much faster at the same time.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697462"}, {"primary_key": "676613", "vector": [], "sparse_vector": [], "title": "Temperature compensation for humidity sensors using ISSA-BP neural network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High-precision humidity detection is essential in various fields. However, the sensor's output signal is often affected by complex environmental temperature changes. To address these challenges, this paper propose an improved sparrow search algorithm based on the back propagation neural network (ISSA-BP). This method optimizes the initialization process of the traditional algorithm and introduces an edge evolution strategy to enhance its iterative update process, significantly improving both efficiency and accuracy. Experimental results demonstrate that the proposed algorithm reduces the mean absolute percentage error (MAPE) from 4.35% to 0.92% and decreases the convergence time from 1.25 s to 0.65 s, compared to traditional methods.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694726"}, {"primary_key": "676614", "vector": [], "sparse_vector": [], "title": "ParallelSFL: A Novel Split Federated Learning Framework Tackling Heterogeneity Issues.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile devices contribute more than half of the world's web traffic, providing massive and diverse data for powering various federated learning (FL) applications. In order to avoid the communication bottleneck on the parameter server (PS) and accelerate the training of large-scale models on resource-constraint workers in edge computing (EC) system, we propose a novel split federated learning (SFL) framework, termed ParallelSFL. Concretely, we split an entire model into a bottom submodel and a top submodel, and divide participating workers into multiple clusters, each of which collaboratively performs the SFL training procedure and exchanges entire models with the PS. However, considering the statistical and system heterogeneity in edge systems, it is challenging to arrange suitable workers to specific clusters for efficient model training. To address these challenges, we carefully develop an effective clustering strategy by optimizing a utility function related to training efficiency and model accuracy. Specifically, ParallelSFL partitions workers into different clusters under the heterogeneity restrictions, thereby promoting model accuracy as well as training efficiency. Meanwhile, ParallelSFL assigns diverse and appropriate local updating frequencies for each cluster to further address system heterogeneity. Extensive experiments are conducted on a physical platform with 80 NVIDIA Jetson devices, and the experimental results show that ParallelSFL can reduce the traffic consumption by at least 21%, speed up the model training by at least 1.36X, and improve model accuracy by at least 5% in heterogeneous scenarios, compared to the baselines.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690665"}, {"primary_key": "676615", "vector": [], "sparse_vector": [], "title": "ProvCam: A Camera Module with Self-Contained TCB for Producing Verifiable Videos.", "authors": ["<PERSON><PERSON> (Myles) Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Our perception of reality is under constant threat from ever-improving video manipulation techniques, including deep-fakes and generative AI. Therefore, proving authenticity of videos is increasingly important, especially in legal and news contexts. However, it is very challenging to prove it based on post-factum video content analysis.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649383"}, {"primary_key": "676616", "vector": [], "sparse_vector": [], "title": "SPECTRA: A Drone-based Multispectral Sensing Platform for Complex Environment Perception.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In complex environments where visibility is severely compromised, such as smoke-filled areas or dense forests, traditional single-sensor systems often fail to provide accurate and reliable data for robots to autonomously navigate and avoid obstacles effectively, posing significant operational challenges and safety risks. Ground-based sensing platforms are further limited by their restricted mobility, hindering access to remote or hazardous areas. Multimodal sensing, which combines various sensor technologies, offers a robust solution to these challenges. Drones, with their high maneuverability and ability to reach inaccessible locations, can capture detailed data from varied altitudes and perspectives. In this demonstration, we introduce SPECTRA, a drone-based multispectral sensing platform that combines thermal cameras, LiDAR, mmWave, and RGB cameras to reliably perform tasks in challenging environments. SPECTRA is a software-hardware co-design platform that can enable and fuse different sensors locally based on the dynamic environment and interpret user tasks using Large Language Models (LLM). We demonstrate SPECTRA's ability to explore and execute assigned tasks in complex environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698845"}, {"primary_key": "676617", "vector": [], "sparse_vector": [], "title": "M2HO: Mitigating the Adverse Effects of 5G Handovers on TCP.", "authors": ["<PERSON><PERSON>", "Qing <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "The advent of 5G promises high bandwidth with the introduction of mmWave technology recently, paving the way for throughput-sensitive applications. However, our measurements in commercial 5G networks show that frequent handovers in 5G, due to physical limitations of mmWave cells, introduce significant under-utilization of the available bandwidth. By analyzing 5G link-layer and TCP traces, we uncover that improper interactions between these two layers causes multiple inefficiencies during handovers. To mitigate these, we propose M2HO, a novel device-centric solution that can predict and recognize different stages of a handover and perform state-dependent mitigation to markedly improve throughput. M2HO is transparent to the firmware, base stations, servers, and applications. We implement M2HO and our extensive evaluations validate that it yields significant improvements in TCP throughput with frequent handovers.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690680"}, {"primary_key": "676618", "vector": [], "sparse_vector": [], "title": "Hydra: Accurate Multi-Modal Leaf Wetness Sensing with mm-Wave and Camera Fusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>"], "summary": "Leaf Wetness Duration (LWD), the time that water remains on leaf surfaces, is crucial in the development of plant diseases. Existing LWD detection lacks standardized measurement techniques, and variations across different plant characteristics limit its effectiveness. Prior research proposes diverse approaches, but they fail to measure real natural leaves directly and lack resilience in various environmental conditions. This reduces the precision and robustness, revealing a notable practical application and effectiveness gap in real-world agricultural settings. This paper presents Hydra, an innovative approach that integrates millimeter-wave (mm-Wave) radar with camera technology to detect leaf wetness by determining if there is water on the leaf. We can measure the time to determine the LWD based on this detection. Firstly, we design a Convolutional Neural Network (CNN) to selectively fuse multiple mm-Wave depth images with an RGB image to generate multiple feature images. Then, we develop a transformer-based encoder to capture the inherent connection among the multiple feature images to generate a feature map, which is further fed to a classifier for detection. Moreover, we augment the dataset during training to generalize our model. Implemented using a frequency-modulated continuous-wave (FMCW) radar within the 76 to 81 GHz band, Hydra's performance is meticulously evaluated on plants, demonstrating the potential to classify leaf wetness with up to 96% accuracy across varying scenarios. Deploying Hydra in the farm, including rainy, dawn, or poorly light nights, it still achieves an accuracy rate of around 90%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690662"}, {"primary_key": "676619", "vector": [], "sparse_vector": [], "title": "Demo: Real-time mmWave Radar Human Sensing Testbed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ling"], "summary": "Millimeter-wave (mmWave) radar is emerging as a promising sensor for various human sensing tasks. Deep learning is frequently applied in radar-based applications, which typically require extensive data collection and labeling. In this demo, we present a low-cost hardware setup and a cross-platform software pipeline that automatically captures radar data of human activities, labels ground truth, and tests inference models in real time. The effectiveness of the testbed is demonstrated through real-time human pose estimation.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698860"}, {"primary_key": "676620", "vector": [], "sparse_vector": [], "title": "Neuralite: Enabling Wireless High-Resolution Brain-Computer Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuguang Fang", "<PERSON>"], "summary": "Intracortical brain-computer interfaces (iBCIs) promise to sense brain activity at an unprecedented scale and resolution. However, unlocking this potential for practical, untethered applications remains an unsolved challenge. The major barrier is the significant wireless bandwidth required to stream high-resolution brain signals. Existing approaches rely on extensive on-device processing, which is severely constrained by the limited resources of iBCI devices, the complexity of brain signals, and the dynamic nature of neural activity. This paper introduces Neuralite, a wireless iBCI system that integrates high-fidelity brain signal models and effective brain sensing mechanisms within an efficient server-driven streaming framework. By thoroughly characterizing brain signal variability, Neuralite adaptively optimizes streaming under dynamic neural conditions, minimizing bandwidth consumption without imposing excessive burdens on resource-constrained iBCI devices. Experimental results demonstrate that Neuralite significantly reduces bandwidth consumption while preserving neural decoding precision across key iBCI components and representative applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690673"}, {"primary_key": "676621", "vector": [], "sparse_vector": [], "title": "Predicting community case transfer path and processing time using decoder models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "Yuanbo Tang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Government agencies and non-profit organizations often rely on case management systems to process the large influx of community request cases. To improve the efficiency of community case management, it's important to model how a community request case is transferred between different departments within the organization and how long it takes to resolve the case. In this paper, we propose two decoder models to predict the departmental transfer path of a given community case and estimate the total processing time based on the predicted path, trained on historical community case records. We compared our prediction results with those obtained using other common machine learning models on a dataset collected from multiple community platforms in Shenzhen, China. Experiments show that our proposed method significantly outperforms the baselines in transfer path and total processing time prediction.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694738"}, {"primary_key": "676622", "vector": [], "sparse_vector": [], "title": "MuV2: Scaling up Multi-user Mobile Volumetric Video Streaming via Content Hybridization and Sharing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zhenhua Li", "<PERSON>"], "summary": "Volumetric videos offer a unique interactive experience and have the potential to enhance social virtual reality and telepresence. Streaming volumetric videos to multiple users remains a challenge due to its tremendous requirements of network and computation resources. In this paper, we develop MuV2, an edge-assisted multi-user mobile volumetric video streaming system to support important use cases such as tens of students simultaneously consuming volumetric content in a classroom. MuV2 achieves high scalability and good streaming quality through three orthogonal designs: hybridizing direct streaming of 3D volumetric content with remote rendering, dynamically sharing edge-transcoded views across users, and multiplexing encoding tasks of multiple transcoding sessions into a limited number of hardware encoders on the edge. MuV2 then integrates the three designs into a holistic optimization framework. We fully implement MuV2 and experimentally demonstrate that MuV2 can deliver high-quality volumetric videos to over 30 concurrent untethered mobile devices with a single WiFi access point and a commodity edge server.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649364"}, {"primary_key": "676623", "vector": [], "sparse_vector": [], "title": "Stress-GPT: Stress detection with an EEG-based foundation model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stress has emerged and continues to be a regular obstacle in people's lives. When left ignored and untreated, it can lead to many health complications, including an increased risk of death. In this study, we propose a foundation model approach for stress detection without the need to train the model from scratch. Specifically, we utilise the foundation model \"Neuro-GPT\", which was trained on a large open dataset (TUH EEG) with 20,000 EEG recordings. We fine-tune the model for stress detection and evaluate it on a 40-subject open stress dataset. The evaluation results with a fine-tuned Neuro-GPT are promising with an average accuracy of 74.4% in quantifying \"low-stress\" and \"high-stress\". We also conducted experiments to compare the foundation model approach with traditional machine learning methods and highlight several observations for future research in this direction.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698121"}, {"primary_key": "676624", "vector": [], "sparse_vector": [], "title": "EventTracker: 3D Localization and Tracking of High-Speed Object with Event and Depth Fusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ciyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Accurately localizing high-speed dynamic objects in 3D space with low latency is crucial for various robotic applications. Current methods face challenges due to extended exposure times and limited sensor resolution, hindering precise object detection and localization. Event cameras, known for their high temporal resolution and asynchronous nature, offer a promising solution. To leverage the potential of the event camera, we propose EventTracker, a novel framework that integrates event and depth measurements for precise and low-latency 3D localization and tracking of the high-speed dynamic object. EventTracker incorporates a collaborative object detection and tracking algorithm optimized for both event and depth data, overcoming detection and registration challenges. Additionally, a graph-instructed optimization algorithm enhances accuracy by fusing heterogeneous sensor data effectively. Experimental evaluation in dynamic environments demonstrates significant improvements in localization performance compared to baseline methods.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694721"}, {"primary_key": "676625", "vector": [], "sparse_vector": [], "title": "Horus: Enhancing Safe Corners via Integrated Sensing and Communication Enabled by Reconfigurable Intelligent Surface.", "authors": ["Qinpei Luo", "<PERSON><PERSON><PERSON>", "Boya Di"], "summary": "As a key feature that has the potential to enable many advanced applications, the integration of sensing functionality is considered essential in the 6G network, which motivates the design of integrated sensing and communication (ISAC) systems. However, traditional ISAC systems based on non-overlapped resource allocation face the challenge of poor energy and spectral efficiency. In this paper, we implement an ISAC system named Horus based on reconfigurable intelligent surfaces, which provides an energy-efficient solution to sense objects in a wide range of blind areas. With a carefully designed ISAC protocol, <PERSON><PERSON> can transmit sensing information to the receiver with high spectral efficiency. We have verified the ability of the proposed system in two case studies of multi-modal sensing and around-corner radar early warning, respectively. Our demonstration video can be found in [11].", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698224"}, {"primary_key": "676626", "vector": [], "sparse_vector": [], "title": "Chorus: Coordinating Mobile Multipath Scheduling and Adaptive Video Streaming.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Qinghua Wu", "<PERSON><PERSON>", "Zhenyu Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasing bandwidth demands of mobile video streaming pose a challenge in optimizing the Quality of Experience (QoE) for better user engagement. Multipath transmission promises to extend network capacity by utilizing multiple wireless links simultaneously. Previous studies mainly tune the packet scheduler in multipath transmission, expecting higher QoE by accelerating transmission. However, since Adaptive BitRate (ABR) algorithms overlook the impact of multipath scheduling on throughput prediction, multipath adaptive streaming can even experience lower QoE than single-path. This paper proposes Chorus, a cross-layer framework that coordinates multipath scheduling with adaptive streaming to optimize QoE jointly. Chorus establishes two-way feedback control loops between the server and the client. Furthermore, Chorus introduces Coarse-grained Decisions, which assist appropriate bitrate selection by considering the scheduling decision in throughput prediction, and Finegrained Corrections, which meet the predicted throughput by QoE-oriented multipath scheduling. Extensive emulation and real-world mobile Internet evaluations show that Chorus outperforms the state-of-the-art MPQUIC scheduler, improving average QoE by 23.5% and 65.7%, respectively.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649359"}, {"primary_key": "676627", "vector": [], "sparse_vector": [], "title": "mm-O-RAN: Building a Millimeter-wave O-RAN Testbed.", "authors": ["<PERSON><PERSON>a M. M.", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This demo showcases the mm-O-RAN testbed, integrating mmWave arrays with USRP and the OpenAirInterface (OAI) 5G stack. Our system integrates the mmWave array with synchronized Time Division Duplex (TDD) switching and fast beamforming capabilities, using GPIO triggers from the OAI stack. As the first full integration of a mmWave testbed with an OAI 5G stack, mm-O-RAN addresses key challenges in design and implementation, providing a versatile platform for research and experimentation for AI and signal processing algorithms to optimize end-end network performance.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698871"}, {"primary_key": "676628", "vector": [], "sparse_vector": [], "title": "RF-Mediator: Tuning Medium Interfaces with Flexible Metasurfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging wireless IoT applications increasingly venture beyond over-the-air communication, such as deep-tissue networking for medical sensors, air-water communication for oceanography, and soil sensing for agriculture. These applications face the fundamental challenge of significant reflection and power loss at medium interfaces. We present RF-Mediator, a programmable metasurface placed near a medium interface to mask the presence of a physical boundary. Our hardware design comprises a single layer of varactor-based surface elements with specific metallic patterns and wiring. With the biasing voltage tuned element-wise, the surface dynamically mediates between the adjacent media to minimize unwanted reflection and boost transmission through the medium interface. A multi-stage control algorithm efficiently determines the surface configuration to handle all dynamic adaptation needs for medium impedance matching and beamforming jointly. We implement a lightweight and flexible metasurface prototype and experiment with diverse cross-medium setups. Extensive evaluation shows that RF-Mediator provides a median power gain of 8 dB for air-tissue links and up to 30 dB for cross-medium backscatter links.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649353"}, {"primary_key": "676629", "vector": [], "sparse_vector": [], "title": "AutoMS: Automated Service for mmWave Coverage Optimization using Low-cost Metasurfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Shi<PERSON> Zheng", "<PERSON><PERSON> Pan", "<PERSON><PERSON>", "Xingyu Chen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "mmWave networks offer wide bandwidth for high-speed wireless communication but suffer from limited range and susceptibility to blockage. Existing coverage provisioning solutions not only incur high costs but also require significant expert knowledge and manual efforts. In this paper, we present AutoMS, an automated service framework to optimize mmWave coverage by strategically designing and placing low-cost passive metasurfaces. Our approach consists of three key components: (1) joint optimization of metasurface phase configurations and placement as well as access point beamforming codebooks. (2) a fast 3D ray-tracing simulator for accelerated large-scale metasurface channel modeling. (3) a metasurface design amenable to ultra-low-cost hot stamping fabrication, featuring high reflectivity, near 2π phase control, and wideband support. Simulation and testbed experiments show that AutoMS can increase the median received signal strength by 11 dB in target rooms and over 20 dB at previous blind spots, and improve the median throughput by over 3× in real-world scenarios.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649347"}, {"primary_key": "676630", "vector": [], "sparse_vector": [], "title": "Demo: Experimentation with Mobile 28 GHz Phased Array Antenna Modules.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present experiments using mobile 28 GHz Phased Array Antenna Modules (PAAMs), demonstrating their ability to perform beam steering with high granularity. The mobile node contains a 64-element IBM 28 GHz PAAM along with a USRP software defined radio, allowing for configuration of the transmit/receive (TX/RX) parameters. These parameters include beam shape, beam steering, and duty cycling. We demonstrate the capabilities of the mobile PAAMs by forming a wireless OFDM link between two mobile PAAMs. We then showcase the beam steering capabilities of the PAAM by performing beam sweeping on the RX PAAM to find the angle of arrival from the TX PAAM. A simple graphical user interface is presented for configuring the PAAMs. A tutorial is available online for users interested in experimentation with 28 GHz PAAMs*.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698841"}, {"primary_key": "676631", "vector": [], "sparse_vector": [], "title": "Experimenting with Adaptive Bitrate Algorithms for Virtual Reality Streaming over Wi-Fi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Interactive Virtual Reality (VR) streaming over Wi-Fi networks encounters significant challenges due to bandwidth fluctuations caused by channel contention and user mobility. Adaptive BitRate (ABR) algorithms dynamically adjust video encoding bitrate based on the available network capacity, aiming to maximize image quality while mitigating congestion and preserving the user's Quality of Experience (QoE). In this paper, we experiment with ABR algorithms for VR streaming using Air Light VR (ALVR), an open-source VR streaming solution. We extend ALVR with a comprehensive set of metrics that provide a robust characterization of the network state, enabling more informed bitrate adjustments. To demonstrate the utility of these performance indicators, we develop and test the Network-aware Step-wise ABR algorithm for VR streaming (NeSt-VR). Results validate the accuracy of the implemented network performance metrics and demonstrate NeSt-VR's adaptation capabilities.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697322"}, {"primary_key": "676632", "vector": [], "sparse_vector": [], "title": "Performance Analysis of Outdoor Open RAN Deployments in Long-Haul 5G Networks using the OpenRAN@Brasil Testbed.", "authors": ["Weskley V. F. Mauricio", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the OpenRAN@Brasil testbed, designed to address the challenges of Open RAN deployments in Brazil. It is composed by commercial off-the-shelf 5G Open RAN components, distributed over two sites, and interconnected by a 400 km midhaul link. We use this to evaluate end-to-end network performance (latency and throughput) under diverse signal conditions. Results show that the long-haul link increases latency to up to 28 ms, degrading downlink throughput by up to 400 Mbps, while the lower uplink throughput is less affected. These findings highlight the importance of strategic RAN component placement and robust signal quality for optimizing Open RAN performance in challenging environments, particularly within the Brazilian ecosystem.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697312"}, {"primary_key": "676633", "vector": [], "sparse_vector": [], "title": "Cloud-Based Federation Framework and Prototype for Open, Scalable, and Shared Access to NextG and IoT Testbeds.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Zhangyu Guan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Zhaoxi) <PERSON>", "<PERSON><PERSON>", "<PERSON> (Leo) <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we present a new federation framework for Union-Labs, an innovative cloud-based resource-sharing infrastructure designed for next-generation (NextG) and Internet of Things (IoT) over-the-air (OTA) experiments. The framework aims to reduce the federation complexity for testbeds developers by automating tedious backend operations, thereby providing scalable federation and remote access to various wireless testbeds. We first describe the key components of the new federation framework, including the Systems Manager Integration Engine (SMIE), the Automated Script Generator (ASG), and the Database Context Manager (DCM). We then prototype and deploy the new Federation Plane on the Amazon Web Services (AWS) public cloud, demonstrating its effectiveness by federating two wireless testbeds: i) UB NeXT, a 5G-and-beyond (5G+) testbed at the University at Buffalo, and ii) UT IoT, an IoT testbed at the University of Utah1.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697295"}, {"primary_key": "676634", "vector": [], "sparse_vector": [], "title": "Beyond Sensing: A High-Performance Software-Defined LoRa Gateway.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa is about to become the standard for Low-Power Wide-Area Networks (LPWANs), being well suited for many Internet of Things (IoT) and sensor network applications. The success of the technology sparked interest to adopt LoRa for other, more challenging use-cases in industrial automation or control of cyber-physical systems. Regular LoRa gateways are, however, limited in their number of parallel demodulation paths and restricted to a single network. To overcome this limitation, we implement a software-defined, multichannel LoRa receiver that is able to receive all common spreading factors and uplink channel combinations of the LoRaWAN EU868 frequency plan. In addition, our receiver does not rely on hard-coded sync words, enabling a mechanism similar to monitor mode in WLAN networks. Experimental evaluation using Software-Defined Radios (SDRs) confirms superior performance compared to commercial LoRa gateways.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697317"}, {"primary_key": "676635", "vector": [], "sparse_vector": [], "title": "Hydra: Exploiting Multi-Bounce Scattering for Beyond-Field-of-View mmWave Radar.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we ask, \"Can millimeter-wave (mmWave) radars sense objects not directly illuminated by the radar - for instance, objects located outside the transmit beamwidth, behind occlusions, or placed fully behind the radar?\" Traditionally, mmWave radars are limited to sense objects that are directly illuminated by the radar and scatter its signals directly back. In practice, however, radar signals scatter to other intermediate objects in the environment and undergo multiple bounces before being received back at the radar. In this paper, we present Hydra, a framework to explicitly model and exploit multi-bounce paths for sensing. Hydra enables standalone mmWave radars to sense beyond-field-of-view objects without prior knowledge of the environment. We extensively evaluate the localization performance of Hydra with an off-the-shelf mmWave radar in five different environments with everyday objects. Exploiting multi-bounce via Hydra provides 2×-10× improvement in the median beyond-field-of-view localization error over baselines.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690710"}, {"primary_key": "676636", "vector": [], "sparse_vector": [], "title": "Experimental Evaluation of a Multi-Domain TSN Scenario in Industry 4.0.", "authors": ["<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "The advent of 5G deployments and the anticipation of 6G have initiated a concerted effort to define new network and application services that leverage these technologies. A key focus of this effort is deterministic networking, which provides the reliability, time sensitivity, and predictability essential for supporting critical services and applications. This article presents a solution to support multi-domain deterministic communications, integrating various technology-specific deterministic networking solutions---namely IEEE 802.11, IEEE 802.1 TSN, and 3GPP 5G domains---over an IETF DetNet data plane. Our goal is to create a realistic infrastructure for developing the next generation of deterministic services. Key contributions include the design, deployment, and performance characterization of the multi-domain data plane, as well as its integration into a real-life use case such as the remote control of a robotic dog.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696726"}, {"primary_key": "676637", "vector": [], "sparse_vector": [], "title": "Multimodal Strategy To Defend Mobile Devices Against Vishing Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the landscape of Vishing calls, it's crucial to determine if users are unknowingly falling prey to fraudsters' tactics, potentially leading to fraud. This paper introduces the Multimodal Vishing Threat Detection (MmVTD) framework, designed to counter Vishing threats and issue alerts on potential fraudulent activities leveraging three key data modalities: call transcripts, sequences of mobile screenshots, and extracted Optical Character Recognition(OCR) text. The novelty of our approach lies in utilization of transformer encoders to build the learnable context for each modality, with a decoder generating warning content while an MLP head classifies the current call as Vishing or Safe at any given instance. This innovative architecture enhances the model's ability to analyze the diverse data sources and generate effective warning in real-time. Furthermore, the MmVTD framework offers robust protection through multimodal analysis, providing real-time alerts when user action suggest susceptibility fraud. Finally, the MmVTD model achieved an impressive 94.44% accuracy in identifying Vishing and Safe calls on a dedicated dataset. Additionally, it attained a BLEU score of 0.583 in generating cautionary messages, effectively deterring users from potential harmful actions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690683"}, {"primary_key": "676638", "vector": [], "sparse_vector": [], "title": "SURF: Eavesdropping on Underwater Communications from the Air.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper investigates how an airborne node can eavesdrop on the underwater acoustic communication between submerged nodes. Conventionally, such eavesdropping has been assumed impossible as acoustic signals do not cross the water-air boundary. Here, we demonstrate that underwater acoustic communications signals can be picked up and (under certain conditions) decoded using an airborne mmWave radar due to the minute vibrations induced by the communication signals on the water surface. We implemented and evaluated a proof-of-concept prototype of our method and tested it in controlled (pool) and uncontrolled environments (lake). Our results demonstrate that an airborne device can identify the modulation and bitrate of acoustic transmissions from an uncooperative underwater transmitter (victim), and even decode the transmitted symbols. Unlike conventional over-the-air communications, our results indicate that the secrecy of underwater links varies depending on the modulation type and provide insights into the underlying reasons behind these differences. We also highlight the theoretical limitations of such a threat model, and how these results may have a significant impact on the stealthiness of underwater communications, with particular concern to submarine warfare, underwater operations (e.g., oil & gas, search & rescue, mining), and conservation of endangered species. Finally, our investigation uncovers countermeasures that can be used to improve or restore the stealthiness of underwater acoustic communications against such threats.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690663"}, {"primary_key": "676639", "vector": [], "sparse_vector": [], "title": "Snooping Underwater Communications via Low-Cost mmWave Radars.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study examines how an airborne device can intercept underwater acoustic signals exchanged between submerged nodes. It challenges the conventional belief that acoustic communications under the water are safe against eavesdropping since acoustics do not cross the water-air boundary. We show that an airborne mmWave radar can detect and decode underwater acoustic signals by picking up minute surface vibrations induced by these signals. The proof-of-concept was tested in controlled (pool) and uncontrolled (lake) environments, proving that an airborne adversary can identify modulation type, bitrate, and decode symbols from an uncooperative underwater transmitter using its radar sensing capabilities. We demonstrate that the secrecy of underwater links depends on modulation type, providing insights into countermeasures to enhance the security of underwater acoustic communications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697444"}, {"primary_key": "676640", "vector": [], "sparse_vector": [], "title": "HandySense: A Multimodal Collection System for Human Two-Handed Dexterous Manipulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>ue Chai", "Xingting Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "Humanoid robots with dexterous hands have gained significant attention due to their manipulation capabilities. Recent advancements are driven by large-scale real robot data and teleoperation technology, enabling precise operation demonstrations and smooth trajectories. Common methods like virtual reality devices, cameras, wearable gloves, and custom hardware face the inability to capture real information about human-object contact, such as tactile information. In this study, we present HandySense, a multimodal system integrating visual, tactile, motion, and spatial perception for robust and comprehensive two-handed manipulation tracking. HandySense includes RGB-D cameras, visual-inertial tracking cameras, and a motion capture glove with fingertip tactile sensors. Our framework achieved 99.45% accuracy in classifying 12 task stages, exhibiting the potential for large-scale human demonstration data collection and representing a pivotal step towards empowering humanoid robots to execute complex manipulations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694728"}, {"primary_key": "676641", "vector": [], "sparse_vector": [], "title": "WiSenseHub: Architecture to deploy a building-scale WiFi-sensing system.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The smart buildings of the future need to understand the movement and occupancy of the people in the environment. Using cameras to provide this context can be privacy-invasive. Alternatively, installing dedicated hardware to sense the environment can be cost-prohibitive and limit ubiquitous adoption. WiFi-based sensing has hence been championed to provide this building-scale sensing, as it allows for both privacy and is ubiquitously deployed in most buildings. However, industry-translatable research in this space has been challenging as no building-scale systems can provide WiFi sensing data. Consequently, many real-world challenges of deploying these sensing systems remain a mystery. To overcome this veil of mystery, we develop and open-source WiSenseHub, a building-scale WiFi-sensing system. We build our system on commercially available WiFi radios, deploy our backend services to collect data on infinitely scalable AWS cloud or a local server desktop, and build a front-end phone-based interface to collect diverse WiFi sensing data. We deployed multiple WiFi radios in our building and collected data for user devices for over 38 hours.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697313"}, {"primary_key": "676642", "vector": [], "sparse_vector": [], "title": "AraSync: Precision Time Synchronization in Rural Wireless Living Lab.", "authors": ["<PERSON><PERSON>", "Taimoor Ul Islam", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Time synchronization is a critical component in network operation and management, and it is also required by Ultra-Reliable, Low-Latency Communications (URLLC) in next-generation wireless systems such as those of 5G, 6G, and Open RAN. In this context, we design and implement AraSync as an end-to-end time synchronization system in the ARA wireless living lab to enable advanced wireless experiments and applications involving stringent time constraints. We make use of Precision Time Protocol (PTP) at different levels to achieve synchronization accuracy in the order of nanoseconds. Along with fiber networks, AraSync enables time synchronization across the AraHaul wireless x-haul network consisting of long-range, high-capacity mmWave and microwave links. In this paper, we present the detailed design and implementation of AraSync, including its hardware and software components and the PTP network topology. Further, we experimentally characterize the performance of AraSync from spatial and temporal dimensions. Our measurement and analysis of the clock offset and mean path delay show the impact of the wireless channel and weather conditions on the PTP synchronization accuracy.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697318"}, {"primary_key": "676643", "vector": [], "sparse_vector": [], "title": "SeaScan: An Energy-Efficient Underwater Camera for Wireless 3D Color Imaging.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ritik <PERSON>k", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the design, implementation, and evaluation of SeaScan, an energy-efficient camera for 3D imaging of underwater environments. At the core of SeaScan's design is a trinocular lensing system, which employs three ultra-low-power monochromatic image sensors to reconstruct color images. Each of the sensors is equipped with a different filter (red, green, and blue) for color capture. The design introduces multiple innovations to enable reconstructing 3D color images from the captured monochromatic ones. This includes an ML-based cross-color alignment architecture to combine the monochromatic images. It also includes a cross-refractive compensation technique that overcomes the distortion of the wide-angle imaging of the low-power CMOS sensors in underwater environments. We built an end-to-end prototype of SeaScan, including color filter integration, 3D reconstruction, compression, and underwater backscatter communication. Our evaluation in real-world underwater environments demonstrates that SeaScan can capture underwater color images with as little as 23.6 mJ, which represents 37X reduction in energy consumption in comparison to the lowest-energy state-of-the-art underwater imaging system. We also report qualitative and quantitative evaluation of SeaScan's color reconstruction and demonstrate its success in comparison to multiple potential alternative techniques (both geometric and ML-based) in the literature. SeaScan's ability to image underwater environments at such low energy opens up important applications in long-term monitoring for ocean climate change, seafood production, and scientific discovery.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690661"}, {"primary_key": "676644", "vector": [], "sparse_vector": [], "title": "Multimodal Smartphone based IoT Framework for Assisting People with Disabilities in SLAM based Human Robot Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, a novel Multimodal Smartphone based IoT framework is proposed that interconnects human user with robotic system in performing SLAM based Human Robot Interactive tasks. Through this system, humans are added in the loop to build and update the SLAM map where they send commands to add and change locations on top of the 2D SLAM map and navigate to locations using the SLAM map when users issue a navigation command. Preliminary experiments conducted in a lab environment showed that the system can build and update SLAM maps, using user voice and text commands. Adding or changing a location in the SLAM map through user speech and text commands took an average time of 9.7 seconds while the robotic system took an average time of 40.96 seconds to complete a navigation task based on user commands using the SLAM map with an average distance error of 10.4 cm.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697458"}, {"primary_key": "676645", "vector": [], "sparse_vector": [], "title": "Back From The Dead: Enabling Timely Computation and Communication in Batteryless Intermittently-powered Sensor Nodes.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Batteryless sensor nodes powered solely by energy harvesting are a promising alternative to battery-powered sensor nodes. However, since the energy harvesting rate from these sources is often very low, unreliable, and time-varying, nodes cannot sustain a continuous operation, making them intermittently powered with unpredictable wakeup times. Moreover, energy harvesting rates differ significantly across deployment scenarios, influenced by environmental conditions, technology, and source availability. Current platforms are mainly designed to be best-effort with performance and reliability dictated by variations in incident input power. Therefore, application that demand critical end-to-end latency fails to achieve high Quality of Services (QOS) due to nodes not being able to compute and communicate in a timely and coordinated manner. Along with collecting, recording and distributing debug information, we propose a novel platform that enable robust coordination of on-times, timely computing and communication. Preliminary results on parts of our platform show nearly 99% coordination between pairs of sensor nodes and over 10x performance benefits for time critical inference tasks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697461"}, {"primary_key": "676646", "vector": [], "sparse_vector": [], "title": "Data Driven Environment Classification Using Wireless Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Robust classification of the operational environment of wireless devices is becoming increasingly important for wireless network optimization, particularly in a shared spectrum environment. Distinguishing between indoor and outdoor devices can enhance reliability and improve coexistence with existing, outdoor, incumbents. For instance, the unlicensed but shared 6 GHz band (5.925 - 7.125 GHz) enables sharing by imposing lower transmit power for indoor unlicensed devices and a spectrum coordination requirement for outdoor devices. Further, indoor devices are prohibited from using battery power, external antennas, and weatherization to prevent outdoor operations. As these rules may be circumvented, we propose a robust indoor/outdoor classification method by leveraging the fact that the radio-frequency environment faced by a device are quite different indoors and outdoors. We first collect signal strength data from all cellular and Wi-Fi bands that can be received by a smartphone in various environments (indoor interior, indoor near windows, and outdoors), along with GPS accuracy, and then evaluate three machine learning (ML) methods: deep neural network (DNN), decision tree, and random forest to perform classification into these three categories. Our results indicate that the DNN model performs the best, particularly in minimizing the most important classification error, that of classifying outdoor devices as indoor interior devices.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697319"}, {"primary_key": "676647", "vector": [], "sparse_vector": [], "title": "Real-Time Non-Contact Estimation of Running Metrics on Treadmills using Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over half a trillion recreational runners worldwide engage in running for psychological, health, and social benefits. Running metrics are essential for motivation, goal setting, performance improvement, health management, and injury prevention. Although wearable devices like fitness trackers and smartwatches offer various metrics, they often perform poorly on treadmills and can be uncomfortable or restrictive. In this work, we propose a non-contact, real-time, smartphone-based approach to estimate running metrics, including cadence, ground contact time (GCT), and balance, using the sound produced during treadmill running. In collaboration with a licensed running coach, we recruited over 50 subjects with varying levels of running expertise. We collected treadmill running sounds and ground-truth running metrics in different environments. We designed and developed a multi-task learning (MTL) machine learning mobile system to capture the treadmill running sounds and estimate running metrics in situ. Our proposed method shows comparable accuracy in estimating running metrics to commercial off-the-shelf (COTS) wearable devices.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697446"}, {"primary_key": "676648", "vector": [], "sparse_vector": [], "title": "MoiréVision: A Generalized Moiré-based Mechanism for 6-DoF Motion Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ultra-high precision motion sensing leveraging computer vision (CV) is a key technology in many high-precision AR/VR applications such as precise industrial manufacture and image-guided surgery, yet conventional CV can be challenged by moiré-based sensing mechanism, thanks to moiré pattern's high sensitivity to six degrees of freedom (6-DoF) pose changes. Unfortunately, existing moiré-based solutions, in their infancy, cannot deal with complicated curvilinear moiré patterns caused by various perspective angles. In this paper, we propose a generalized moiré-based mechanism, MoiréVision, towards practical adoptions; it relies on high-frequency gratings as visual marker to help extract the fine-grained feature points for ultra-high precision motion sensing. As the foundation of general moiré-based sensing, we propose a formulation to characterize \"uncontrolled\" curvilinear moiré patterns in practical scenarios. To deal with the problem of moiré feature interference in practice, we propose a Gabor-based algorithm to separate overlapped curvilinear moiré patterns from two dimensions. Furthermore, to extract fine-grained feature points for high-precision motion sensing, we propose a bending function-based model and a resolution-enhanced strategy to reconstruct detailed texture of moiré markers and extract moiré feature points at sub-pixel level. Extensive experimental results show that MoiréVision greatly enhances the usability and generalizability of moiré-based sensing systems in real-world applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649374"}, {"primary_key": "676649", "vector": [], "sparse_vector": [], "title": "MoiréVib: Micron-level Vibration Detection based on <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Detection and assessment of micro vibrations are crucial tasks in both industrial settings and daily life. However, vibration sensors attached to the target vibrator may introduce potential resonance, and wireless detection methods suffer from severe multipath interference. Fortunately, moiré-based sensing methods have gained recognition in recent years due to their ability to perceive micro motion changes. In this paper, we propose MoiréVib, a micro-vibration detection solution based on moiré patterns for dynamic and high-frequency environments. We attach a printed marker with periodic gratings to the surface of vibration devices to generate moiré patterns, which can amplify micro vibrations due to their low-frequency magnification effect. However, moiré pattern's changes caused by micro vibrations are often overwhelmed by random pixel-level noises, and the limited frame rate of the camera fails to capture high-frequency moiré features. To deal with these problems, we propose a spectrum-based method to refine and enhance the dynamic and micro moiré features. Additionally, we propose a dual-frame-rate-based fusion mechanism to realize high-frequency reconstruction of moiré features. Extensive experimental results show that MoiréVib can realize a median amplitude detection error of 4.37 μm and achieve frequency detection up to 300Hz with a frame rate range of 10~30 fps.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690700"}, {"primary_key": "676650", "vector": [], "sparse_vector": [], "title": "Zero-Shot Accurate mmWave Antenna Array Calibration in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Upamanyu Madhow"], "summary": "mmWave antenna array calibration is a necessary yet tedious and costly process in manufacturing to capture the non-idealities in phased arrays, in order to obtain codebooks for accurate and stable beam steering. Unfortunately, predefined codebooks provided by manufacturers to steer beams in a given set of directions do not support the arbitrary beam shapes required for various mmWave communication, sensing, and security applications. To create arbitrary beam patterns, one needs to first find the unknown calibration vector for the particular phased array in use. In this paper, we introduce EiCal, a novel zero-shot technique that leverages the beamforming codebook advertised by the manufacturer to extract the calibration vector at zero cost (i.e., with no additional measurements). The key idea is that the unknown desired calibration vector can be obtained via an appropriately designed eigen-decomposition of the given codebook. We experimentally demonstrate the efficacy of EiCal on a 60 GHz mmWave array for two scenarios: angle estimation using compressive pseudorandom beams, and simultaneous steering of beams and nulls. Our results also point to potential simplifications in the calibration process at the manufacturer.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697323"}, {"primary_key": "676651", "vector": [], "sparse_vector": [], "title": "Poster: Do Privacy-Preserving Obfuscation Techniques Degrade the Accuracy of Odometry?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "On-device sensors in mobile systems, e.g., autonomous vehicles and AR/VR, use odometry for real-time positioning, but they risk capturing sensitive data of non-consenting bystanders. Prior works have investigated various privacy-preserving techniques to protect those sensitive data. However, it is still unclear about the impact of such approaches on the accuracy of odometry. In this work, we investigate the impact of various privacy-preserving obfuscation techniques on the accuracy of monocular visual odometry. We focus on three widely used obfuscation methods: Gaussian Blur, Gaussian Noise, and Laplacian Noise, applied to protect bystander privacy. Our investigation reveals that some obfuscation techniques can increase the odometry errors by up to 56.9%, while others surprisingly reduce the errors by up to 66.8%, compared to raw data. Our key findings indicate that data obfuscation primarily affects the duration of tracking loss in ORB-SLAM3, which is the main source of the errors, and successful relocalization immediately following tracking loss plays a crucial role in reducing the overall errors.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697459"}, {"primary_key": "676652", "vector": [], "sparse_vector": [], "title": "Enabling Physical Localization of Uncooperative Cellular Devices.", "authors": ["Taekkyung Oh", "Sangwook Bae", "<PERSON><PERSON>", "Yong<PERSON> Lee", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "In cellular networks, authorities may need to physically locate user devices to track criminals or illegal equipment. This process involves authorized agents tracing devices by monitoring uplink signals with cellular operator assistance. However, tracking uncooperative uplink signal sources remains challenging, even for operators and authorities. Three key challenges persist for fine-grained localization: i) devices must generate sufficient, consistent uplink traffic over time, ii) target devices may transmit uplink signals at very low power, and iii) signals from cellular repeaters may hinder localization of the target device. While these challenges pose significant practical obstacles to localization, they have been largely overlooked in existing research.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690709"}, {"primary_key": "676653", "vector": [], "sparse_vector": [], "title": "ADMarker: A Multi-Modal Federated Learning System for Monitoring Digital Biomarkers of Alzheimer&apos;s Disease.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Li Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shi<PERSON> Cao", "<PERSON>", "<PERSON>", "Zhenyu Yan", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Alzheimer's Disease (AD) and related dementia are a growing global health challenge due to the aging population. In this paper, we present ADMarker, the first end-to-end system that integrates multi-modal sensors and new federated learning algorithms for detecting multidimensional AD digital biomarkers in natural living environments. ADMarker features a novel three-stage multi-modal federated learning architecture that can accurately detect digital biomarkers in a privacy-preserving manner. Our approach collectively addresses several major real-world challenges, such as limited data labels, data heterogeneity, and limited computing resources. We built a compact multi-modality hardware system and deployed it in a four-week clinical trial involving 91 elderly participants. The results indicate that ADMarker can accurately detect a comprehensive set of digital biomarkers with up to 93.8% accuracy and identify early AD with an average of 88.9% accuracy. ADMarker offers a new platform that can allow AD clinicians to characterize and track the complex correlation between multidimensional interpretable digital biomarkers, demographic factors of patients, and AD diagnosis in a longitudinal manner.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649370"}, {"primary_key": "676654", "vector": [], "sparse_vector": [], "title": "Audioradio Target Speech Detection and Extraction with mmWave Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Detecting the target speaker and enhancing speech with high fidelity has been a long-standing problem, especially in challenging acoustic conditions. To address these problems with minimal user cooperation, we have developed multimodal audioradio speech detection (RadioVAD) and enhancement (RadioSES) systems using mmWave modality. This demo presents how these two systems can be run together in real time to extract target speech and filter any type of ambient noise. Our demo uses an mmWave radar and a microphone attached to a laptop to detect and localize target speakers in the field of view, detect the presence of voice to trigger the microphone, and enhance the noisy speech with a deep learning model running in real-time. Our experiments confirm that an audioradio system can detect and isolate high-fidelity target speech, even with interfering speech and noise; while being privacy preserving and environmentally robust.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698863"}, {"primary_key": "676655", "vector": [], "sparse_vector": [], "title": "Crowd Analytics with a Single mmWave Radar.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a novel approach for crowd analytics using a single monostatic mmWave radar. We propose a new mathematical model that infers the crowd size for dynamic and quasi-dynamic crowd behaviors. More specifically, we derive a novel closed-form mathematical expression that describes the statistical dynamics of undercounting due to crowd shadowing. This new methodical finding allows for significantly improved crowd density estimates. For spatially-patterned crowds where the mathematical solution does not extend, we then develop a Temporal Convolutional Network (TCN) which is purely trained on simulated data. We perform extensive testing over a total of 22 experiments, with up to (and including) 21 people and in 4 different areas, including indoors, and the proposed mathematical solution achieves a Mean Absolute Error (MAE) of 1.53. Lastly, we show how our framework can infer anomalies, bottlenecks, and crowd engagement level. Overall, the paper can have a significant impact on crowd management and urban planning.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690664"}, {"primary_key": "676656", "vector": [], "sparse_vector": [], "title": "Predicting the Performance of Cellular Networks: A Latent-resilient Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cellular service providers (CSPs) require predicting the network performance for various reasons such as analyzing the impact of planned configuration changes and large-scale events on the network. Although network configurations are widely considered as key predictors of performance, we claim that they are insufficient for accurately predicting cellular network performance. The cellular networks are impacted by unmeasured external factors (e.g., weather, called latents), therefore, the performance prediction based solely on configurations may result in confounding effects. We show that the Mobility, Access, and Traffic (MAT) metrics should be considered in addition as network performance predictors. Using a large dataset collected from a live cellular network, we validate the claim and show the benefit of using MAT metrics for accurate performance prediction.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697425"}, {"primary_key": "676657", "vector": [], "sparse_vector": [], "title": "CIPAT: Latent-resilient Toolkit for Performance Impact Prediction due to Configuration Tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cellular service providers (CSPs) aim to optimize network performance and enhance user experience by tuning network configurations. However, this process often requires continuous live network testing, which incurs significant operational costs. In this paper, we focus on predicting the impact of configuration changes using historical data, thereby reducing the need for live network tests. A key challenge in developing such a model is accounting for unobserved external factors (e.g., weather, referred to as latents) that can introduce confounding effects between configurations and performance metrics. To address this, we employ intermediate network metrics, called Mobility, Access, and Traffic (MAT) metrics, which are influenced by both configurations and latents, and in turn, affect performance metrics. We introduce Configuration Impact Prediction Analysis Toolkit (CIPAT), a novel two-stage toolkit developed using a comprehensive real-world dataset from live LTE networks. Our evaluation demonstrates that CIPAT enables CSPs to predict the performance impact of proposed configuration changes with up to 86% accuracy and 85% efficacy, thereby reducing the operational costs associated with configuration tuning.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698246"}, {"primary_key": "676658", "vector": [], "sparse_vector": [], "title": "Open-source Resource Control API for real IEEE 802.11 Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The performance of IEEE 802.11-based wireless networks is determined by the multi-parameter resource control of transmit and receive settings. With today's WiFi transceivers, controllable parameters include data rate, power, frame transmission aggregation, and global receiver settings like sensitivity. Despite its importance, research on IEEE 802.11 MAC-layer resource control over the past two decades has primarily relied on simulations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697314"}, {"primary_key": "676659", "vector": [], "sparse_vector": [], "title": "Non-public 5G-NR Unlicensed: Design, Implementation and Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The mobile network operation in the unlicensed spectrum offers opportunities for non-public connectivity services without the financial burden of spectrum licensing. Previous studies regarding unlicensed spectrum operation have mainly relied on analysis or simulation without addressing practical constraints. In this work, we designed, implemented, and evaluated the listen-before-talk protocol as a co-existence medium access mechanism on top of the 5G-NR physical layer. We propose different TDD configurations to enable different sensing frequencies. We enabled OpenAirInterface operation in the n46/n47 bands, a European band dedicated to connected intelligent transportation services. We evaluate the performance of unlicensed 5G-NR under bursty cross-traffic from a different device. The results show that the frequency of cross-traffic bursts affects BLER (Block Error Rate) and throughput more than their duration. The design of the TDD frame to offer more frequent sensing opportunities allows for reduced BLER but negatively impacts achievable throughput in the current setup.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697315"}, {"primary_key": "676660", "vector": [], "sparse_vector": [], "title": "Joint Horizontal and Vertical Federated Learning for Multimodal IoT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multimodal Federated Learning (FL) integrates two crucial research areas in IoT scenarios: utilizing complementary multimodal data to enhance downstream inference performance and conducting decentralized training to safeguard privacy. However, existing studies primarily focus on applying FL methods after multimodal feature fusion, without fundamentally addressing multimodal FL across both feature and sample spaces. A notable tradeoff persists between the computational demands of multimodal information and the limited computing resources in IoT systems. To tackle this challenge, we propose a Joint Horizontal and Vertical (JHV) FL algorithm tailored for multimodal IoT systems. JHV employs vertical FL to distribute computing tasks across multimodal IoT devices (feature space) and horizontal FL to allocate tasks across multiple silos (sample space). Experimental results on two public multimodal datasets show that JHV outperforms three baseline methods, demonstrating its effectiveness for multimodal IoT systems, especially in rapid and accurate downstream tasks like classification and prediction.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698245"}, {"primary_key": "676661", "vector": [], "sparse_vector": [], "title": "CCCG: Self-supervised Color Constancy with Collaborative Generative Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Color constancy provides stable color features for high-level computer vision tasks such as target recognition and autonomous driving. Existing deep learning-based color constancy algorithms using convolutional neural networks remove the illumination and obtain images under standard illumination. However, these methods suffer from insufficient training data and poor robustness in complex scenes. To address these issues, we propose a new paradigm: self-supervised color constancy with a collaborative generative network (CCCG). CCCG transforms the illumination estimation problem into a generation problem, reducing the solution space and enhancing algorithm robustness in complex scenes. Additionally, CCCG employs a self-supervised network structure, reducing dependence on light source label data. CCCG comprises two network structures: the Filter Network (FN) and the Illumination Network (IN). FN extracts features from the image and generates an image under standard illumination. IN incorporates the extracted physical light source information into the output results of FN and verifies the generated image. Experimental results on the Gehler-Shi and NUS-8 datasets show that CCCG outperforms current color constancy methods across various evaluation metrics and can be applied to other computer vision tasks requiring color constancy preprocessing.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694734"}, {"primary_key": "676662", "vector": [], "sparse_vector": [], "title": "ISAC-Facilitated Optimal On-demand Mobile Charging Scheme for IoT-based WRSNs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IoT-based wireless sensor networks (WSNs) face significant energy constraints, which can be alleviated by wireless power transfer (WPT) technology. Integrating WPT with WSNs creates wireless rechargeable sensor networks (WRSNs), where optimizing charging efficiency and scheduling is critical. This paper introduces an ISAC-facilitated optimal on-demand mobile charging scheme for IoT-based WRSNs (IOMSN) with three key components. First, it presents an ISAC-assisted prioritized charging queue, incorporating four attributes with probability distributions: residual energy, traffic load, MCV travel time, and direction angle. Second, it provides ISAC-driven estimations of MCV distance, speed, and location to enhance prioritization, thereby optimizing the charging route and potentially reducing travel costs. Third, a time-allocated partial charging model improves charging efficiency. Numerical results show that the proposed protocol outperforms cutting-edge protocols in energy usage efficiency, travel distance, charging delay, and service time.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698228"}, {"primary_key": "676663", "vector": [], "sparse_vector": [], "title": "Savannah: Efficient mmWave Baseband Processing with Minimal and Heterogeneous Resources.", "authors": ["Zhenzhou Qi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "5G new radio (NR) employs frequency range 2 (FR2) in the millimeter-wave (mmWave) bands, which employs a much shorter slot duration compared to FR1 (sub-7 GHz) systems and, therefore, poses significant challenges for softwarized baseband processing in virtualized radio access networks (vRANs). Existing systems supporting software baseband processing focus on enabling (massive) multiple-input and multiple-output (MIMO) using multi-core edge server(s). These solutions may fail to meet the more stringent processing deadline in FR2 or require more intensive computational resources. In this paper, we present Savannah, an efficient mmWave baseband processing framework using minimal and heterogeneous computing resources including CPU and eASIC. Savannah addresses the challenges associated with baseband processing in FR2 by applying techniques for vectorizing matrix operations and memory access patterns, supporting heterogeneous computation via offloading LDPC decoding to an eASIC, and enabling single-core operation. We show that Savannah, using a single CPU core and the ACC100 accelerator, can support a 2×2 MIMO link with 100 MHz bandwidth, yielding a data rate of up to 487 Mbps.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690707"}, {"primary_key": "676664", "vector": [], "sparse_vector": [], "title": "Savannah: A Real-time Programmable mmWave Baseband Processing Framework.", "authors": ["Zhenzhou Qi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "5G new radio (NR) frequency range 2 (FR2) in the millimeter-wave (mmWave) band has a much shorter baseband processing deadline compared to that in the sub-7 GHz FR1 band. This tight deadline requires an efficient real-time system for baseband processing using minimal computational resources. We demonstrate Savannah, a software framework for efficient mmWave baseband processing using minimal and heterogeneous computing resources, including CPU and eASIC. Savannah vectorizes matrix operations and memory access patterns in multi-input multi-output (MIMO) arithmetic, offloads low-density parity-check (LDPC) coding to an eASIC, and enables single-core operation. We demonstrate that Savannah, using a single CPU core and an eASIC, can support a 2×2 MIMO link with 100 MHz bandwidth under full uplink traffic load, yielding a data rate of up to 487 Mbps.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698843"}, {"primary_key": "676665", "vector": [], "sparse_vector": [], "title": "Pushing the Throughput Limit of OFDM-based Wi-Fi Backscatter Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "The majority of existing Wi-Fi backscatter systems transmit tag data at rates lower than 250 kbps, as the tag data is modulated at OFDM symbol level, allowing for demodulation using commercial Wi-Fi receivers. However, it is necessary to modulate tag data at OFDM sample level to satisfy the requirements for higher throughput. A comprehensive theoretical analysis and experimental investigation conducted in this paper demonstrates that demodulating sample-level modulated tag data using commercial Wi-Fi receivers is unattainable due to excessive computational overhead and demodulation errors. This is because the significant tag information dispersion, loss, and shuffling are caused by Wi-Fi physical layer operations. We conclude that the optimal position for demodulation is the time-domain IQ samples, which do not undergo any Wi-Fi physical layer operations and preserve the intact, ordered, and undispersed information of tag-modulated data, thereby minimizing complexity and maximizing accuracy.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690672"}, {"primary_key": "676666", "vector": [], "sparse_vector": [], "title": "Ultra-Dense Networks with NOMA and SWIPT Interplay for SE and EE in 5G/6G.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Ultra-Dense Networks (UDNs) represent a promising paradigm for meeting the ever-increasing demand for high data rates and connectivity in modern wireless communication systems. This paper investigates the interplay of Non-Orthogonal Multiple Access (NOMA) and Simultaneous Wireless Information and Power Transfer (SWIPT) to enhance the performance of UDNs. We explore Mean Field Game (MFG), Stackelberg, and their combination, with simulation results demonstrating the effectiveness of the NOMA-SWIPT framework in improving the performance metrics of UDNs, spectral efficiency (SE) and energy efficiency (EE), specifically. Additionally, a user clustering algorithm is proposed to enhance the system's performance by improving data rates. This research paves the way for advancements in next-generation wireless communication systems.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695906"}, {"primary_key": "676667", "vector": [], "sparse_vector": [], "title": "SateRIoT: High-performance Ground-Space Networking for Rural IoT.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>"], "summary": "Rural Internet of Things (IoT) systems connect sensors and actuators in remote areas, serving crucial roles in agriculture and environmental monitoring. Given the absence of networking infrastructure for backhaul in these regions, satellite IoT techniques offer a cost-effective solution for connectivity. However, current satellite IoT architectures often struggle to deliver high performance due to temporal and spatial link challenges. This paper presents SateRIoT, a new network architecture with temporal link estimation and spatial link sharing that fully exploits the capability of space low-cost low-earth-orbit (LEO) IoT satellites and ground low-power wide area (LPWA) IoT techniques in rural areas. First, we introduce a bursty link model that predicts the number of transmittable packets within a transmission window, reducing energy waste from failed uplink transmissions. Moreover, we enhance the model by selecting informative features and optimizing the window length. Additionally, we develop a multi-hop flooding protocol that enables gateways to buffer and share data packets across the network while incorporating a priority data queue to avoid duplicate transmissions. We implement SateRIoT with commercial-off-the-shelf (COTS) IoT satellite and LoRa radios, then evaluate its performance based on real deployment and real-world collected traces. The results show that SateRIoT can consume 3.3X less energy consumption for an individual gateway. Moreover, SateRIoT offers up to a 5.6X reduction in latency for a single packet and a 1.9X enhancement in throughput.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690659"}, {"primary_key": "676668", "vector": [], "sparse_vector": [], "title": "Demeter: Reliable Cross-soil LPWAN with Low-cost Signal Polarization Alignment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Dong", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "T<PERSON>xing Li", "<PERSON><PERSON><PERSON>"], "summary": "Soil monitoring plays an essential role in agricultural systems. Rather than deploying sensors' antennas above the ground, burying them in the soil is an attractive way to retain a non-intrusive aboveground space. Low Power Wide-Area Network (LPWAN) has shown its long-distance and low-power features for aboveground Internet-of-Things (IoT) communication, presenting a potential of extending to underground cross-soil communication over a wide area, which however has not been investigated before. The variation of soil conditions brings significant signal polarization misalignment, degrading communication reliability. In this paper, we propose Demeter, a low-cost low-power programmable antenna design to keep reliable cross-soil communication automatically. First, we propose a hardware architecture to enable polarization adjustment on commercial-off-the-shelf (COTS) single-RF-chain LoRa radio. Moreover, we develop a low-power programmable circuit to obtain polarization adjustment. We further design an energy-efficient heuristic calibration algorithm and an adaptive calibration scheduling method to keep signal polarization alignment automatically. We implement Demeter with a customized PCB circuit and COTS devices. Then, we evaluate its performance in various soil types and environmental conditions. The results show that Demeter can achieve up to 11.6 dB SNR gain indoors and 9.94 dB outdoors, 4× horizontal communication distance, at least 20 cm deeper underground deployment, and up to 82% energy consumption reduction per day compared with the standard LoRa.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649358"}, {"primary_key": "676669", "vector": [], "sparse_vector": [], "title": "Demeter-Demo: Demonstrating Cross-soil LPWAN with Low-cost Signal Polarization Alignment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low Power Wide-Area Network (LPWAN) has shown its long-distance and low-power features for aboveground Internet-of-Things (IoT) communication, presenting a potential to extend to underground cross-soil communication over a wide area, which has not been investigated before. The variation of soil conditions brings significant signal polarization misalignment, degrading communication reliability. We propose Demeter, a low-cost, low-power programmable antenna design to keep reliable cross-soil communication automatically. First, we propose a hardware architecture to enable polarization adjustment on commercial-off-the-shelf (COTS) single-RF-chain LoRa radio. Moreover, we develop a low-power programmable circuit to adjust polarization. We further design an energy-efficient heuristic calibration algorithm to keep signal polarization alignment automatically. We demonstrate Demeter in indoor environments. The antenna is buried in a plastic container filled with gardening soil to simulate the node underground. Meanwhile, we use the COTS LoRa gateway as a receiver to show the RSSI and SNR variations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698848"}, {"primary_key": "676670", "vector": [], "sparse_vector": [], "title": "Nomad: Providing Insights into the Spectrum Environment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of transmissions in the RF spectrum demands robust and responsive signal analysis techniques to detect and label malicious activity. Traditional methods struggle to balance sensitivity and accuracy in real-time. This paper introduces Nomad, a modular system that combines global spectral pattern recognition with localized energy detection and classic signal processing methods for efficient and accurate RF analysis. Nomad's innovative architecture facilitates rapid development and deployment, while its intuitive visualizations provide actionable insights into even weak signals within complex RF environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698867"}, {"primary_key": "676671", "vector": [], "sparse_vector": [], "title": "Efficient and Secure Cloud Data Sharing Using CP-ABE Supporting Dynamic Attributes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ciphertext-Policy Attribute-Based Encryption (CP-ABE) is a cryptographic primitive that provides confidentiality and fine-grained access control for data sharing in untrusted cloud environments. It allows data owners to set access policies based on attributes, ensuring only users who meet the criteria can decrypt and access the data. However, existing CP-ABE systems are inherently designed for static attributes and lack efficient support for dynamic user attributes, often necessitating frequent key updates and complex revocation processes. To address these challenges, we propose an enhanced CP-ABE cryptosystem that allows for the dynamic generation of additional key components, which can be efficiently integrated with the user's existing key. The user private key is assigned attribute-level expiration tags, facilitating instant revocation. Additionally, our scheme facilitates the secure offloading of partial decryption of ciphertext to the cloud server using a transformed private key, thus reducing the computational overhead on resource-constrained user devices. Our proposed cryptosystem provides a secure and scalable approach for managing dynamic attributes in cloud environments with rapidly evolving access requirements.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695904"}, {"primary_key": "676672", "vector": [], "sparse_vector": [], "title": "Multi-Client Searchable Encryption with Granular Access Control for Cloud-Assisted IoT.", "authors": ["<PERSON><PERSON><PERSON>", "Satyansh Shukla", "<PERSON><PERSON><PERSON>"], "summary": "Cloud-assisted Internet of Things (CIoT) enhances data accessibility and operational efficiency but encounters various security and privacy challenges. Today, CIoT applications generate large amounts of data in a multi-client accessible environment involving periodic addition of data. These systems require efficient searchable encryption to securely share data with authorized users, enable private keyword searches, and support frequent updates to outsourced data, thus maintaining data integrity and user privacy. Existing symmetric searchable encryption (SSE) schemes enable efficient searches for large datasets but are limited in providing multi-user data sharing. In contrast, attribute-based searchable encryption (ABSE) schemes offer fine-grained access control in collaborative scenarios but suffer from high search latency. In this paper, we present a hybrid searchable encryption scheme that enables secure keyword search with sublinear search efficiency and supports periodic dynamic addition of files in multi-client IoT environments. By providing privacy-preserving keyword searches with forward privacy and keyword-level access control, our approach effectively enhances security and performance for cloud-assisted IoT.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697438"}, {"primary_key": "676673", "vector": [], "sparse_vector": [], "title": "Distill Drops into Data: Event-based Rain-Background Decomposition Network.", "authors": ["Ciyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Event cameras excel in high-speed and high-dynamic-range scenarios but are highly sensitive to rain, which introduces significant noise while also revealing detailed rain features. This paper introduces a novel Event-based Rain-Background Decomposition Network that integrates Spiking Neural Networks (SNNs) and Convolutional Neural Networks (CNNs). By \"Distilling Rain,\" we reconstruct a rain-free background for downstream tasks, and by \"Collecting Rain,\" we extract the physical characteristics of rain. Experimental evaluations demonstrate the network's effectiveness in both background reconstruction and rain modeling. This work extends the capabilities of event cameras by mitigating the adverse effects of rain while also leveraging rain-induced noise to extract valuable environmental data, enhancing their utility in both challenging weather conditions and detailed environmental analysis.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694737"}, {"primary_key": "676674", "vector": [], "sparse_vector": [], "title": "EximCache: Block I/O Caching Based on Latency Sensitivity from the Readahead Framework.", "authors": ["<PERSON><PERSON>", "Jinsung An", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Efficient use of caching space is crucial in block I/O caching schemes due to the high cost of caching media. This paper introduces EximCache, a novel scheme that prioritizes blocks not promptly managed by the OS readahead framework, addressing the performance criticality of block I/Os. Experimental results demonstrate that EximCache reduces caching space usage significantly while imposing minimal performance overhead during application launches and Linux boot.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697435"}, {"primary_key": "676675", "vector": [], "sparse_vector": [], "title": "Low-Latency Task-Oriented Communications with Multi-Round, Multi-Task Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we address task-oriented (or goal-oriented) communications where an encoder at the transmitter learns compressed latent representations of data, which are then transmitted over a wireless channel. At the receiver, a decoder performs a machine learning task, specifically for classifying the received signals. The deep neural networks corresponding to the encoder-decoder pair are jointly trained, taking both channel and data characteristics into account. Our objective is to achieve high accuracy in completing the underlying task while minimizing the number of channel uses determined by the encoder's output size. To this end, we propose a multi-round, multi-task learning (MRMTL) approach for the dynamic update of channel uses in multi-round transmissions. The transmitter incrementally sends an increasing number of encoded samples over the channel based on the feedback from the receiver, and the receiver utilizes the signals from a previous round to enhance the task performance, rather than only considering the latest transmission. This approach employs multi-task learning to jointly optimize accuracy across varying number of channel uses, treating each configuration as a distinct task. By evaluating the confidence of the receiver in task decisions, MRMTL decides on whether to allocate additional channel uses in multiple rounds. We characterize both the accuracy and the delay (total number of channel uses) of MRMTL, demonstrating that it achieves the accuracy close to that of conventional methods requiring large numbers of channel uses, but with reduced delay by incorporating signals from a prior round. We consider the CIFAR-10 dataset, convolutional neural network architectures, and AWGN and Rayleigh channel models for performance evaluation. Our results show that MRMTL significantly improves the efficiency of task-oriented communications, balancing accuracy and latency effectively.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698244"}, {"primary_key": "676676", "vector": [], "sparse_vector": [], "title": "GateHaul: A Gateway Architecture using Backhauling Networks to Address the Connectivity Challenges of Embedded Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Significant efforts to address the energy challenges of embedded systems have resulted in the design of new low-power transmitter architectures. These transmitters utilize backscatter or tunnel diode-based mechanisms to enable low-power transmissions by delegating energy-intensive tasks to external infrastructure. However, their widespread adoption is hindered by the need for specialized deployment setups, such as the precise placement of carrier-emitting devices. To overcome this, we are developing GateHaul-a low-cost gateway architecture that provides the required carrier signal, coordinates with other gateways, collects sensor data, and backhauls the information. Notably, GateHaul can backhaul sensor data without conventional networks utilizing emerging opportunistic backhaul networks. We demonstrate an early prototype of GateHaul that collects information from backscatter and conventional devices and backhauls the collected information using Apple's FindMy network.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698868"}, {"primary_key": "676677", "vector": [], "sparse_vector": [], "title": "CloudRIC: Open Radio Access Network (O-RAN) Virtualization with Shared Heterogeneous Computing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open and virtualized Radio Access Networks (vRANs) are breeding a new market with unprecedented opportunities. However, carrier-grade vRANs today are expensive and energy-hungry, as they rely on hardware accelerators (HAs) that are dedicated to individual distributed units (DUs). In this paper, we argue that sharing pools of heterogeneous processors among DUs leads to more cost- and energy-efficient vRANs. We then design CloudRIC, a system that, powered by lightweight data-driven models, meets specific reliability targets while (i) coordinating access between DUs and heterogeneous computing infrastructure; and (ii) assisting DUs with compute-aware radio scheduling procedures. Experiments on a GPU-accelerated O-Cloud show that CloudRIC can achieve, respectively, 3x and 15x mean gains in energy- and cost-efficiency under real RAN workloads while ensuring 99.999% reliability even in dense scenarios.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649381"}, {"primary_key": "676678", "vector": [], "sparse_vector": [], "title": "CloudRIC demo: Open Radio Access Network (O-RAN) Virtualization with Shared Heterogeneous Computing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open and virtualized Radio Access Networks (vRANs) are breeding a new market with unprecedented opportunities. However, carrier-grade vRANs today are expensive and energy-hungry, as they rely on hardware accelerators (HAs) that are dedicated to individual distributed units (DUs). We demonstrate CloudRIC [17], a system that, powered by lightweight data-driven models, meets specific reliability targets while (i) coordinating access between DUs and heterogeneous computing infrastructure; and (ii) assisting DUs with compute-aware radio scheduling procedures. Using a user-friendly dashboard to control an experimental testbed remotely, we demonstrate that CloudRIC achieves comparable reliability performance to a DU-dedicated platform while offering up to 40x higher cost-efficiency and up to 6x higher energy efficiency when pooling resources for up to 70 DUs.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698858"}, {"primary_key": "676679", "vector": [], "sparse_vector": [], "title": "A Multi-Band mm-Wave Experimental Platform Towards Environment-Aware Beam Management in the Beyond-5G Era.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Berk Acikgöz", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Agile beam management is key to seamless high-speed mm-wave connectivity in the beyond-5G era, given the site-specific spatio-temporal variations of the mm-wave channel. Leveraging non-RF sensor inputs for environment awareness, e.g. via ML techniques, can greatly enhance RF-based beam management. To address the lack of diverse publicly available multi-modal mm-wave datasets for the design of novel beam management approaches and to enable their real-world, real-time evaluation, we present our SDR-based multi-band mm-wave experimental platform which integrates multi-modal sensors towards environment-aware beam management.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697316"}, {"primary_key": "676680", "vector": [], "sparse_vector": [], "title": "A Black-Box Approach for Quantifying Leakage of Trace-Based Correlated Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantification of information leakage is crucial, especially for privacy-preserving systems such as location-based services (LBS) with integrated privacy mechanisms. Existing quantification mainly utilized two approaches: i) the white-box approach, precise but impractical for complex systems, and ii) the black-box approach, practical but struggles with scalability for large output spaces. Recently, Machine Learning (ML) algorithms have been integrated into the black-box approach to effectively approximate information leakage for independent observations with better scalability. However, this method does not provide precise estimates for dependent observations. Intuitively, once a correlated secret is discovered, it becomes easier for an attacker to predict related secrets, leading to an underestimation of information leakage. This paper introduces an ML-based black-box approach to improve the accuracy of information leakage estimation for systems with correlated data, particularly in trace-based scenarios. Our solution uses an ML model for rough estimation and leverages data correlations to refine inferences for more accurate quantification. Evaluation results from three real-world datasets and one collected dataset confirm our solution's effectiveness in accurately and cost-effectively quantifying system leakage for correlated observations.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694722"}, {"primary_key": "676681", "vector": [], "sparse_vector": [], "title": "AeroBridge: Autonomous Drone Handoff System for Emergency Battery Service.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Endrowed<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes an Emergency Battery Service (EBS) for drones in which an EBS drone flies to a drone in the field with a depleted battery and transfers a fresh battery to the exhausted drone. The authors present a unique battery transfer mechanism and drone localization that uses the Cross Marker Position (CMP) method. The main challenges include a stable and balanced transfer that precisely localizes the receiver drone. The proposed EBS drone mitigates the effects of downwash due to the vertical proximity between the drones by implementing diagonal alignment with the receiver, reducing the distance to 0.5 m between the two drones. CFD analysis shows that diagonal instead of perpendicular alignment minimizes turbulence, and the authors verify the actual system for change in output airflow and thrust measurements. The CMP marker-based localization method enables position lock for the EBS drone with up to 0.9 cm accuracy. The performance of the transfer mechanism is validated experimentally by successful mid-air transfer in 5 seconds, where the EBS drone is within 0.5 m vertical distance from the receiver drone, wherein 4m/s turbulence does not affect the transfer process.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649382"}, {"primary_key": "676682", "vector": [], "sparse_vector": [], "title": "Li-FiAR: Networking Augmented-Reality Devices through Visible Light.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We have seen rapid deployment of augmented reality devices. However, these devices currently suffer from limited battery life. They require frequent recharge, especially when capturing and streaming information wirelessly. In particular, these devices stream information over the radio spectrum using transceivers that are power-hungry, and wireless communication dominates the energy budget of these devices. We present our ongoing work to design a system called Li-FiAR, which proposes integrating Li-Fi with augmented reality devices. Specifically, we implement a transmitter and receiver and integrate them with an augmented reality device. We argue that Li-Fi receivers may consume less power than their radio counterparts while benefiting from the spatial nature of light propagation. This enables novel application scenarios for augmented reality. We demonstrate the transmission of images and audio through Li-Fi, presented on an augmented reality device in this work.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695899"}, {"primary_key": "676683", "vector": [], "sparse_vector": [], "title": "Cross-content User Authentication in Virtual Reality.", "authors": ["<PERSON><PERSON>", "Shiqing Luo", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Virtual reality (VR) presents a rapidly evolving virtual world with varying types of visual content. Authenticating users periodically across different types of VR content to safeguard private data is a cornerstone in many sensitive VR applications. Previous studies have performed authentication by verifying users' actions or involuntary responses to certain visual stimuli. However, these methods tie users to either a predefined task or a single type of VR content, failing to support periodic authentication in dynamic VR interactions. In this paper, we propose a cross-content authentication framework that verifies users across different VR content by jointly analyzing eye movements and VR content. Specifically, we decouple displayed VR content from eye movements and generate a content-agnostic embedding determined by cognitive and physiological attributes unique to each user. Experiments with video viewing and text reading in VR show that our system achieves an F-score of 0.92 in cross-content user authentication.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696212"}, {"primary_key": "676684", "vector": [], "sparse_vector": [], "title": "IoTCoder: A Copilot for IoT Application Development.", "authors": ["<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "Existing code Large Language Models are primarily designed for generating simple and general algorithms but are not dedicated to IoT applications. To fill this gap, we present IoTCoder, a coding copilot specifically designed to synthesize programs for IoT application development. IoTCoder features three locally deployed small language models (SLMs): a Task Decomposition SLM that decomposes a complex IoT application into multiple tasks with detailed descriptions, a Requirement Transformation SLM that converts the decomposed tasks described in natural language to well-structured specifications, and a Modularized Code Generation SLM that generates modularized code based on the task specifications. Experiment results show that IoTCoder can synthesize programs adopting more IoT-specific algorithms and outperform state-of-the-art code LLMs in terms of both task accuracy (by more than 24.2% on average) and memory usage (by less than 358.4 MB on average).", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697447"}, {"primary_key": "676685", "vector": [], "sparse_vector": [], "title": "CosMAC: Constellation-Aware Medium Access and Scheduling for IoT Satellites.", "authors": ["<PERSON><PERSON><PERSON>", "Om <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Pico-satellite (picosat) constellations aim to become the de facto connectivity solution for Internet of Things (IoT) devices. These constellations rely on a large number of small picosats and offer global plug-and-play connectivity at low data rates, without the need for Earth-based gateways. As picosat constellations scale, they run into new bottlenecks due to their traditional medium access designs optimized for single (or few) satellite operations. We present CosMAC - a new constellation-scale medium access and scheduling system for picosat networks. CosMAC includes a new overlap-aware medium access approach for uplink from IoT to picosats and a new network layer that schedules downlink traffic from satellites. We empirically evaluate CosMAC using measurements from three picosats and large-scale trace-driven simulations for a 173 picosat network supporting 100k devices. Our results demonstrate that CosMAC can improve the overall network throughput by up to 6.5X over prior state-of-the-art satellite medium access schemes.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690657"}, {"primary_key": "676686", "vector": [], "sparse_vector": [], "title": "Soar: Design and Deployment of A Smart Roadside Infrastructure System for Autonomous Driving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhenyu Yan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, smart roadside infrastructure (SRI) has demonstrated the potential of achieving fully autonomous driving systems. To explore the potential of infrastructure-assisted autonomous driving, this paper presents the design and deployment of Soar, the first end-to-end SRI system specifically designed to support autonomous driving systems. Soar consists of both software and hardware components carefully designed to overcome various system and physical challenges. Soar can leverage the existing operational infrastructure like street lampposts for a lower barrier of adoption. Soar adopts a new communication architecture that comprises a bi-directional multi-hop I2I network and a downlink I2V broadcast service, which are designed based on off-the-shelf 802.11ac interfaces in an integrated manner. Soar also features a hierarchical DL task management framework to achieve desirable load balancing among nodes and enable them to collaborate efficiently to run multiple data-intensive autonomous driving applications. We deployed a total of 18 Soar nodes on existing lampposts on campus, which have been operational for over two years. Our real-world evaluation shows that Soar can support a diverse set of autonomous driving applications and achieve desirable real-time performance and high communication reliability. Our findings and experiences in this work offer key insights into the development and deployment of next-generation smart roadside infrastructure and autonomous driving systems.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649352"}, {"primary_key": "676687", "vector": [], "sparse_vector": [], "title": "SEEN: ML Assisted Cellular Service Diagnosis.", "authors": ["Xiaofeng Shi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the primary channel for users to report and resolve service issues, customer care has historically been a critical and resource-intensive aspect of operating cellular networks. However, owing to the inherent complexity in correlating network events with the service performance experienced by individual users, adoption of data-driven solutions leveraging network data for realtime troubleshooting during customer care calls has remained a challenge to cellular service providers (CSPs). In this work, we propose a novel ML aSsisted cEllular sErvice diagNosis (SEEN) solution that infers the cause of user service issues from performance metrics observed from network and assists care agents during customer care calls. Our extensive evaluations demonstrated that SEEN can accurately identify the root cause of user reported performance issues in >80% cases, without relying on information provided by users. Accurate root cause prediction coupled with automated recommended resolution actions implemented in SEEN, lead to significant reduction in handling time to resolve service issues and in trouble tickets volume, improving customer satisfaction and reducing customer care operational expense. Benefit of SEEN is further demonstrated by field deployment in a large CSP.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690678"}, {"primary_key": "676688", "vector": [], "sparse_vector": [], "title": "IMS is Not That Secure on Your 5G/4G Phones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "IMS (IP Multimedia Subsystem) is vital for delivering IP-based multimedia services in mobile networks. Despite constant upgrades by 3GPP over the past two decades to support heterogeneous radio access networks (e.g., 4G LTE, 5G NR, and Wi-Fi) and enhance IMS security, the focus has primarily been on cellular infrastructure. Consequently, IMS security measures on mobile equipment (ME), such as smartphones, lag behind rapid technological advancements. Our study reveals that mandated IMS security measures on ME fail to keep pace, resulting in new vulnerabilities and attack vectors, including denial of service (DoS) across all networks, named SMS source spoofing, and covert communications over Video-over-IMS attacks. All vulnerabilities and proof-of-concept attacks have been experimentally validated in operational 5G/4G networks across various phone models and network operators. Finally, we propose and prototype standard-compliant remedies for these vulnerabilities.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649377"}, {"primary_key": "676689", "vector": [], "sparse_vector": [], "title": "RECAP: 3D Traffic Reconstruction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "On-vehicle 3D sensing technologies, such as LiDARs and stereo cameras, enable a novel capability, 3D traffic reconstruction. This produces a volumetric video consisting of a sequence of 3D frames capturing the time evolution of road traffic. 3D traffic reconstruction can help trained investigators reconstruct the scene of an accident. In this paper, we describe the design and implementation of RECAP, a system that continuously and opportunistically produces 3D traffic reconstructions from multiple vehicles. RECAP builds upon prior work on point cloud registration, but adapts it to settings with minimal point cloud overlap (both in the spatial and temporal sense) and develops techniques to minimize error and computation time in multi-way registration. On-road experiments and trace-driven simulations show that RECAP can, within minutes, generate highly accurate reconstructions that have 2× or more lower errors than competing approaches.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690691"}, {"primary_key": "676690", "vector": [], "sparse_vector": [], "title": "LCR360: Efficient Head Movement Prediction and Viewport Sharing in 360° Video Streaming.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the realm of 360° video streaming, three paramount challenges arise: accurate prediction of the field of view, efficient bandwidth utilization, and seamless viewport sharing. Current solutions often neglect the latter aspect of 360° videos, thereby necessitating ultra-low latency, high quality, and real-time rendering. The considerable size of 360° videos further intensifies these challenges. To tackle these issues, we introduce LCR360, a deep learning-based neural network solution designed for accurate head movement prediction. Our solution is deployed on E3PO [9], an end-to-end open-source evaluation platform dedicated to 360° video-on-demand streaming. By integrating simulated streams into the Unreal Engine, we provide an immersive experience that enables users to share viewports seamlessly. Moreover, LCR360 surpasses its competitors by ~5% in the S-metric, a measure that combines the MSE of video quality, bandwidth, and storage costs.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696210"}, {"primary_key": "676691", "vector": [], "sparse_vector": [], "title": "Evaluation of the Latency of Machine Learning Random Access DDoS Detection in Open RAN.", "authors": ["<PERSON>", "João Paulo Henriques Sales de Lima", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Pa<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A testbed with open-source tools and commercial equipment is designed to investigate the timing of ML-based xApp operations in DDoS attack detection during the Random Access procedure. Results show high-precision and high-recall ML classifiers operating faster than the Medium Access Control (MAC) layer's Contention Resolution timer. For classifiers with over 97% precision and recall, XGBoost has the fastest execution time and the narrowest estimated run time probability density function.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3701546"}, {"primary_key": "676692", "vector": [], "sparse_vector": [], "title": "SIMD-enabled Physics-inspired MIMO detector for Uplink Multi-user MIMO.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Physics-inspired computation and Ising machines have grown as a new alternative to conventional algorithms and have shown promising performance for several NP-Hard problems. However, the existing state-of-the-art focuses on empirical performance gains and either ignores the practical real-time constraints or makes strong assumptions about practical deployments. In this work, we utilize the SIMD capabilities of Intel Xeon CPU to implement an Ising solver for MIMO detection which meets the real-time processing and timing constraints of an LTE/5G system. We further evaluate the end-to-end performance of our proposed MIMO solver via trace-driven simulations with a hybrid MATLAB/NS3 simulator.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697436"}, {"primary_key": "676693", "vector": [], "sparse_vector": [], "title": "MicroSurf: Guiding Energy Distribution inside Microwave Oven with Metasurfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Pan", "Longyuan Ge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microwave ovens have become an essential cooking appliance owing to their convenience and efficiency. However, microwave ovens suffer from uneven distribution of energy, which causes prolonged delays, unpleasant cooking experiences, and even safety concerns. Despite significant research efforts, current solutions remain inadequate. In this paper, we first conduct measurement studies to understand the energy distribution for 10 microwave ovens and show their energy distribution in both 2D and 3D is very skewed, with notably lower energy levels at the center of the microwave cavity, where food is commonly placed. To tackle this challenge, we propose a novel methodology to enhance the performance of microwave ovens. Our approach begins with the development of a measurement driven model of a microwave oven. We construct a detailed 3D model in the High Frequency Structure Simulator (HFSS) and use real temperature measurements from a microwave to derive critical parameters relevant to the appliance's functionality (e.g., operating frequency, waveguide specifications). We then develop a novel approach that optimizes the design and placement of a low-cost passive metasurface for a given heating objective. Using extensive experiments, we demonstrate the efficacy of our approach across diverse food, optimization objectives, and microwave ovens.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690697"}, {"primary_key": "676694", "vector": [], "sparse_vector": [], "title": "MetaGlucose: Low-cost and Practical Cold Liquid Glucose Level Measurement for Health.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Measuring the glucose concentration in liquids is crucial for ensuring the safety of products for individuals with diabetes. This process not only aids in diabetes management but also highlights the importance of taking additional precautions after a diabetes diagnosis. Currently, glucose test strips are widely used to measure the glucose concentration of liquids. It's safe, effective, easy to use, and a cheap alternative to other complex technology with the same use. However, it has sevral setbacks, such as its inability to accurately measure the glucose concentration of cold liquids (0°C - 10°C). This lack of variability can lead to more inconvenience for individuals than benefits. Therefore, in this paper, we propose the usage of millimeter-wave (mmWave) sensing as a contactless, versatile, and easy method to measure glucose levels in cold liquids accurately.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694720"}, {"primary_key": "676695", "vector": [], "sparse_vector": [], "title": "SiWiS: Fine-grained Human Detection Using Single WiFi Device.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sub-6GHz radio sensing offers several compelling advantages, such as resilience to poor lighting conditions, privacy preservation, and the ability to see through walls. However, in indoor environments, the sub-6GHz ISM spectrum is heavily occupied by WiFi devices, leaving little available spectrum for sensing purposes. In this paper, we introduce SiWiS, a new approach to integrate radio sensing capabilities into individual WiFi devices for fine-grained human activity detection. SiWiS comprises two main components: (i) a new hardware component that can be easily installed on an off-the-shelf WiFi device, and (ii) a dual-branch deep neural network (DNN) optimized for concurrent human mask segmentation and pose estimation. We have built a prototype of SiWiS and installed it on a commercial WiFi router for evaluation. Extensive experimental results demonstrate a significant performance improvement over WiFi channel state information (CSI) based sensing methods. More importantly, zero-shot experiments confirm that SiWiS can be directly transferred to unseen real-world environments.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690703"}, {"primary_key": "676696", "vector": [], "sparse_vector": [], "title": "Enabling Accessible and Ubiquitous Interaction in Next-Generation Wearables: An Unvoiced Speech Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As wearable devices increase, there's a growing need for intuitive, private, and accessible interaction methods. This position paper builds on the research on unvoiced speech interaction and authentication to propose a vision for interaction in next-generation wearables. This paper draws upon our previous work on unvoiced speech interfaces that leverage jaw movements and facial vibrations for command recognition and user authentication. We argue that unvoiced speech interaction can provide a robust, privacy-preserving, and noise-resistant alternative to traditional interfaces, enhancing accessibility and offering discrete interaction in public spaces. We discuss the potential integration of these systems into commercial devices and explore gesture-based interactions as an alternative to touch. Additionally, we discuss the future direction of unvoiced speech interfaces. This paper sets the stage for implementing unvoiced speech and gesture-based interaction in mainstream wearables in our daily interactions with technology.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3695908"}, {"primary_key": "676697", "vector": [], "sparse_vector": [], "title": "Classification of lungs disease with Electrical impedance tomography.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The article discusses research on a wearable medical diagnostic system using electrical impedance tomography. This system aims to diagnose long-term respiratory diseases, particularly COPD, ARDS, pneumothorax, pneumonia, bron-chospasm, and pulmonary hypertension. It seeks to reduce the number of tests needed for accurate diagnoses, thus saving time. The article compares two classification models for distinguishing between diseased and healthy individuals. This approach helps streamline the diagnostic process.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697450"}, {"primary_key": "676698", "vector": [], "sparse_vector": [], "title": "Coordinated Group Cycling for Commuting.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A crucial step for reducing emissions in the transport sector is the shift towards public transportation and bicycling. However, due to the lack of perceived safety, people are reluctant to commute by bicycle. A potential solution to this problem could be group cycling, allowing cyclists to form a group with others in order to ride together. Depending on local traffic laws, such groups allow for cycling next to each other and for special rights for intersection crossing. In this paper, we outline how group cycling commutes may be coordinated using communication capabilities of contemporary smartphones. We showcase how group cycling can reduce waiting times and, thus, improve ride comfort and safety in a simulation-based case study.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697429"}, {"primary_key": "676699", "vector": [], "sparse_vector": [], "title": "TraMSR: Transformer and Mamba based Practical Speech Super-Resolution for Mobile Wearables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jun<PERSON> Xi<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Speech super-resolution techniques offer a promising solution to enhance audio quality in wearable devices, particularly when addressing the challenges of reduced sampling rates necessitated by battery life constraints and network instability. However, existing methods either prove computationally prohibitive for mobile platforms, or lack sufficient performance. We present TraMSR, a novel hybrid model combining transformer and Mamba architectures for acoustic speech super-resolution. TraMSR achieves superior performance while significantly reducing computational demands compared to state-of-the-art methods. Our model outperforms GAN-based approaches by up to 7.3% in Perceptual Evaluation of Speech Quality (PESQ) and 1.8% in Short-Time Objective Intelligibility (STOI), with an order of magnitude smaller memory footprint and up to 465 times faster inference speed.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697460"}, {"primary_key": "676700", "vector": [], "sparse_vector": [], "title": "RF-Egg: An RF Solution for Fine-Grained Multi-Target and Multi-Task Egg Incubation Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Eggs and chickens serve as crucial animal-source proteins in our diets, making large-scale breeding egg incubation an essential undertaking. However, current solutions, i.e., vision-based and sensor-based methods, are primarily designed for egg fertility detection tasks under single-egg settings, which have not yet satisfied the goal of multi-target and multi-task sensing. In this paper, we propose RF-Egg, the first RF-based fine-grained multi-target and multi-task egg incubation sensing system with respect to sensing fertility, incubation status, and early mortality of chicken embryos. RF-Egg leverages the weak coupling effects of RFID tags when interacting with eggs, which induces different impedance changes of RFID tags with the incubation levels of eggs, thereby resulting in a variation of low-level phase readings of the backscatter signals. Regarding the challenge of multi-target profiling interference, we propose a multipath combating algorithm to extract the target-induced signal component based on the built signal model, and address non-uniformity issues across multiple tags. Moreover, we devise three unique feature maps tailored to each task, and then design an Multi-Task Triplet (MTT) network for multitasking. Our evaluation results based on 189 eggs show that RF-Egg achieves an accuracy of 94.4%, 96.1%, and 90.1% for the aforementioned three tasks when supporting 16 targets. Additionally, our extensive field study in a local egg hatchery suggests that RF-Egg presents the potential to be widely deployed in the modern poultry industry.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649378"}, {"primary_key": "676701", "vector": [], "sparse_vector": [], "title": "Enhancing EHR Systems with data from wearables: An end-to-end Solution for monitoring post-Surgical Symptoms in older adults.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile health (mHealth) apps have gained popularity over the past decade for patient health monitoring, yet their potential for timely intervention is underutilized due to limited integration with electronic health records (EHR) systems. Current EHR systems lack real-time monitoring capabilities for symptoms, medication adherence, physical and social functions, and community integration. Existing systems typically rely on static, in-clinic measures rather than dynamic, real-time patient data. This highlights the need for automated, scalable, and human-centered platforms to integrate patient-generated health data (PGHD) within EHR. Incorporating PGHD in a user-friendly format can enhance patient symptom surveillance, ultimately improving care management and post-surgical outcomes. To address this barrier, we have developed an mHealth platform, ROAMM-EHR, to capture real-time sensor data and Patient Reported Outcomes (PROs) using a smartwatch. The ROAMM-EHR platform can capture data from a consumer smartwatch, send captured data to a secure server, and display information within the Epic EHR system using a user-friendly interface, thus enabling healthcare providers to monitor post-surgical symptoms effectively.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698118"}, {"primary_key": "676702", "vector": [], "sparse_vector": [], "title": "SpotLight: Accurate, Explainable and Efficient Anomaly Detection for Open RAN.", "authors": ["Chu<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Open RAN architecture, with disaggregated and virtualized RAN functions communicating over standardized interfaces, promises a diversified and multi-vendor RAN ecosystem. However, these same features contribute to increased operational complexity, making it highly challenging to troubleshoot RAN related performance issues and failures. Tackling this challenge requires a dependable, explainable anomaly detection method that Open RAN is currently lacking. To address this problem, we introduce SpotLight, a tailored system archtecture with a distributed deep generative modeling based method running across the edge and cloud. SpotLight takes in a diverse, fine grained stream of metrics from the RAN and the platform, to continually detect and localize anomalies. It introduces a novel multi-stage generative model to detect potential anomalies at the edge using a light-weight algorithm, followed by anomaly confirmation and an explain-ability phase at the cloud, that helps identify the minimal set of KPIs that caused the anomaly. We evaluate SpotLight using the metrics collected from an enterprise-scale 5G Open RAN deployment in an indoor office building. Our results show that compared to a range of baseline methods, SpotLight yields significant gains in accuracy (13% higher F1 score), explain-ability (2.3 -- 4X reduction in the number of reported KPIs) and efficiency (4 -- 7X bandwidth reduction).", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649380"}, {"primary_key": "676703", "vector": [], "sparse_vector": [], "title": "SpotLight - An Open RAN Anomaly Detection and Identification System.", "authors": ["Chu<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Open RAN architecture, featuring disaggregated and virtualized RAN functions communicating over standardized interfaces, promises a diverse, multi-vendor ecosystem. However, these features also increase operational complexity, complicating the troubleshooting of RAN performance issues and failures. Addressing this challenge requires a reliable, explainable anomaly detection method, which Open RAN currently lacks. To address this problem, we have developed SpotLight, a tailored distributed deep learning method running across the edge and cloud. SpotLight continuously detects and localizes anomalies by analyzing a diverse, fine-grained stream of metrics from the RAN and platform. It employs a novel multi-stage generative model to identify potential anomalies at the edge using a lightweight algorithm, followed by anomaly confirmation and an explainability phase in the cloud, which pinpoints the minimal set of KPIs responsible for the anomaly. In this demo, using a carrier-grade indoor Open RAN testbed with configurable anomaly event generation and replay, we highlight (1) the difficulty of troubleshooting problems in Open RAN and (2) accurate, efficient, and explainable online anomaly detection with SpotLight and corresponding visualization in comparison with prior art.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698854"}, {"primary_key": "676704", "vector": [], "sparse_vector": [], "title": "Gastag: A Gas Sensing Paradigm using Graphene-based Tags.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Gas sensing plays a key role in detecting explosive/toxic gases and monitoring environmental pollution. Existing approaches usually require expensive hardware or high maintenance cost, and are thus ill-suited for large-scale long-term deployment. In this paper, we propose Gastag, a gas sensing paradigm based on passive tags. The heart of Gastag design is embedding a small piece of gas-sensitive material to a cheap RFID tag. When gas concentration varies, the conductivity of gas-sensitive materials changes, impacting the impedance of the tag and accordingly the received signal. To increase the sensing sensitivity and gas concentration range capable of sensing, we carefully select multiple materials and synthesize a new material that exhibits high sensitivity and high surface-to-weight ratio. To enable a long working range, we redesigned the tag antenna and carefully determined the location to place the gas-sensitive material in order to achieve impedance matching. Comprehensive experiments demonstrate the effectiveness of the proposed system. Gastag can achieve a median error of 6.7 ppm for CH4 concentration measurements, 12.6 ppm for CO2 concentration measurements, and 3 ppm for CO concentration measurements, outperforming a lot of commodity gas sensors on the market. The working range is successfully increased to 8.5 m, enabling the coverage of many tags with a single reader, laying the foundation for large-scale deployment.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649365"}, {"primary_key": "676705", "vector": [], "sparse_vector": [], "title": "On the Public Cloud Deployment of Cloud-Native Mobile Core Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leveraging the public cloud for the Core Network (CN) remains uncommon amongst mobile network operators despite the economical and operational benefits. In this paper, we present a holistic feasibility analysis for various methods to deploy a cloud-native CN to AWS. Our findings confirm the economical and technical feasibility, and present a suitable deployment method for further adoption of the public cloud.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697432"}, {"primary_key": "676706", "vector": [], "sparse_vector": [], "title": "TinyRIC-ML: A Lightweight Real Time ML Platform for O-RAN.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the open radio access network (O-RAN) movement driving the evolution of cellular networks towards 6G, real time (RT) RAN control and assurance has emerged as the next frontier in RAN programmability, with in-base station (BS) machine learning (ML) at its core. However, scalability demands associated with production-grade networks necessitate the need for a robust, yet lightweight in-BS ML operations framework to support automated ML workflows. To that end, this paper introduces TinyRIC-ML, a novel lightweight and high-performance ML platform for RT operations within the RAN. Key highlights include a comprehensive system architecture design, a concrete systems-level implementation, and a preliminary over-the-air experimental evaluation to demonstrate the system's performance and feasibility.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697426"}, {"primary_key": "676707", "vector": [], "sparse_vector": [], "title": "Wireless Sensing-based Daily Activity Tracking System Deployment in Low-Income Senior Housing Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Maintaining independence in daily activities and mobility is critical for healthy aging. Older adults who are losing the ability to care for themselves or ambulate are at a high risk of adverse health outcomes and decreased quality of life. It is essential to monitor daily activities and mobility routinely and capture early decline before a clinical symptom arises. Existing solutions use self-reports, or technology-based solutions that depend on cameras or wearables to track daily activities; however, these solutions have different issues (e.g., bias, privacy, burden to carry/recharge them) and do not fit well for seniors. In this study, we discuss a non-invasive, and low-cost wireless sensing-based solution to track the daily activities of low-income older adults. The proposed sensing solution relies on a deep learning-based fine-grained analysis of ambient WiFi signals and it is non-invasive compared to video or wearable-based existing solutions. We deployed this system in real senior housing settings for a week and evaluated its performance. Our initial results show that we can detect a variety of daily activities of the participants with this low-cost system with an accuracy of up to 76.90%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698115"}, {"primary_key": "676708", "vector": [], "sparse_vector": [], "title": "AI-Driven Network Intrusion Detection and Resource Allocation in Real-World O-RAN 5G Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "5G technology, the latest advancement in mobile networks, promises increased data speeds, reduced latency, and enhanced capacity. However, network performance and user experience can be critically impacted by malicious traffic, identified as anomaly traffic and intrusion methods. Addressing these challenges requires optimized resource sharing and robust network security measures. In this paper, we present an AI/ML-driven Network Intrusion Detection framework with dynamic resource allocation and user management within the O-RAN architecture. Our Anomaly Traffic Detector (ATD) enhances network security by mitigating Denial of Service (DoS) attacks through an xApp that classifies network traffic in real-time and dynamically adjusts network resources and user connections. Experimental evaluations show that our system effectively maintains low latency under attack conditions, nearly doubles the throughput for legitimate users, and reduces average CPU usage by up to 15%. We use as reference platforms the OpenAirInterface, and FlexRIC for programming the slice and user connectivity decisions at the RAN level, and evaluate our scheme under real-world settings in a testbed environment.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697311"}, {"primary_key": "676709", "vector": [], "sparse_vector": [], "title": "Demo: Decoding Control Information Passively from Standalone 5G Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON>", "<PERSON>"], "summary": "5G New Radio cellular networks are designed to provide high Quality of Service for application on wirelessly connected devices. However, changing conditions of the wireless last hop can degrade application performance, and the applications have no visibility into the 5G Radio Access Network (RAN). Most 5G network operators run closed networks, limiting the potential for co-design with the wider-area internet and user applications. This paper demonstrates NR-Scope, a passive, incrementally-deployable, and independently-deployable Standalone 5G network telemetry system that can passively measure fine-grained RAN capacity, latency, and retransmission information. Application servers can take advantage of the measurements to achieve better millisecond scale, application-level decisions on offered load and bit rate adaptation than end-to-end latency measurements or end-to-end packet losses currently permit. We demonstrate the performance of NR-Scope by decoding the downlink control information (DCI) for downlink and uplink traffic of a 5G Standalone base station in real-time.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698840"}, {"primary_key": "676710", "vector": [], "sparse_vector": [], "title": "Heterogeneous Graph Neural Network for Cooperative ISAC Beamforming in Cell-Free MIMO Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Integrated sensing and communication (ISAC) is one of the usage scenarios for the sixth generation (6G) wireless networks. In this paper, we study cooperative ISAC in cell-free multiple-input multiple-output (MIMO) systems, where multiple MIMO access points (APs) collaboratively provide communication services and perform multistatic sensing. We formulate an optimization problem for the ISAC beamforming design, which maximizes the achievable sum-rate while guaranteeing the sensing signal-to-noise ratio (SNR) requirement and total power constraint. Learning-based techniques are regarded as a promising approach for addressing such a nonconvex optimization problem. By taking the topology of cell-free MIMO systems into consideration, we propose a heterogeneous graph neural network (GNN), namely SACGNN, for ISAC beamforming design. The proposed SACGNN framework models the cell-free MIMO system for cooperative ISAC as a heterogeneous graph and employs a transformer-based heterogeneous message passing scheme to capture the important information of sensing and communication channels and propagate the information through the graph network. Simulation results demonstrate the performance gain of the proposed SACGNN framework over a conventional null-space projection based scheme and a deep neural network (DNN)-based baseline scheme.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698223"}, {"primary_key": "676711", "vector": [], "sparse_vector": [], "title": "GPMS: Enabling Indoor GNSS Positioning using Passive Metasurfaces.", "authors": ["Yezhou Wang", "<PERSON><PERSON> Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Global Navigation Satellite System (GNSS) is extensively utilized for outdoor positioning and navigation. However, achieving high-precision indoor positioning is challenging due to the significant attenuation of GNSS signals indoors. To address this issue, we propose an innovative indoor GNSS positioning system called GPMS, which uses passive metasurface technology to redirect GNSS signals from outdoors into indoor spaces. These passive metasurfaces are strategically optimized for indoor coverage by steering and scattering the GNSS signals across a wide range of incident angles. We further develop a novel localization algorithm that can determine which metasurface the signal goes through and localize the user using the set of metasurfaces as anchor points. A distinct advantage of our localization algorithm is that it can be implemented on existing mobile devices without any hardware modifications. We implement the prototype of GPMS, and deploy six metasurfaces in two indoor environments, a 10×50 m2 office floor and a 15×20 m2 lecture room, to evaluate system performance. In terms of coverage, our GPMS increases the C/N0 from 9.1 dB-Hz to 23.2 dB-Hz and increases the number of visible satellites from 3.6 to 21.5 in the office floor. In terms of indoor positioning accuracy, our proposed system decreases the absolute positioning error from 30.6 m to 3.2 m in the office floor, and from 11.2 m to 2.7 m in the lecture room, demonstrating the feasibility and benefits of metasurface-assisted GNSS for indoor positioning.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690702"}, {"primary_key": "676712", "vector": [], "sparse_vector": [], "title": "IRS-aided bi-static ISAC system with security constraint.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Tao"], "summary": "Integrated sensing and communication (ISAC), as a promising component in 6G, however, is faced with security challenges that the critical communication information will be leaked to the sensing targets. This paper investigates an intelligent reflecting surface (IRS) aided bi-static ISAC system. The objective is to maximize the sensing signal-to-interference-and-noise ratio (SINR) with security and other constraints. A joint beamforming, reflection and receiving filter design is proposed. Alternating optimization (AO) based iterative algorithm leveraging semi-definite relaxation (SDR), <PERSON><PERSON><PERSON>'s transform, and successive convex approximation (SCA) techniques is utilized to solve the non-convex problem. It is found that the proposed scheme provides significantly better sensing performance with security constraint.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698233"}, {"primary_key": "676713", "vector": [], "sparse_vector": [], "title": "Real-time Respiration Sensing with Pervasive GPS Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The past decade has witnessed a surge of interest in human respiration sensing with various wireless signals to achieve at-home smart health. While promising in many aspects, two critical limitations still exist: a) dedicated sensing signal transmitters are needed; b) existing sensing schemes affect the original function of the wireless technology such as communication. In this demo, we present the GPSense Respiration system, a new kind of contact-free respiration sensing system that breaks the above limitations. GPSense Respiration operates by receiving and analyzing the GNSS signals reflected by the human body without additional pre-deployed signal transmitters. Also, GPSense Respiration system will not affect wireless communications because GNSS signals operate at different frequencies than communication signals. This demo enables respiration monitoring for different users in real-time without any calibration.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698836"}, {"primary_key": "676714", "vector": [], "sparse_vector": [], "title": "Efficient and Personalized Mobile Health Event Prediction via Small Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Healthcare monitoring is crucial for early detection, timely intervention, and the ongoing management of health conditions, ultimately improving individuals' quality of life. Recent research shows that Large Language Models (LLMs) have demonstrated impressive performance in supporting healthcare tasks. However, existing LLM-based healthcare solutions typically rely on cloud-based systems, which raise privacy concerns and increase the risk of personal information leakage. As a result, there is growing interest in running these models locally on devices like mobile phones and wearables to protect users' privacy. Small Language Models (SLMs) are potential candidates to solve privacy and computational issues, as they are more efficient and better suited for local deployment. However, the performance of SLMs in healthcare domains has not yet been investigated. This paper examines the capability of SLMs to accurately analyze health data, such as steps, calories, sleep minutes, and other vital statistics, to assess an individual's health status. Our results show that, TinyLlama, which has 1.1 billion parameters, utilizes 4.31 GB memory, and has 0.48s latency, showing the best performance compared other four state-of-the-art (SOTA) SLMs on various healthcare applications. Our results indicate that SLMs could potentially be deployed on wearable or mobile devices for real-time health monitoring, providing a practical solution for efficient and privacy-preserving healthcare.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698123"}, {"primary_key": "676715", "vector": [], "sparse_vector": [], "title": "Polaris: Accurate, Vision-free Fiducials for Mobile Robots with Magnetic Constellation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fiducial marking is indispensable in mobile robots, including their pose calibration, contextual perception, and navigation. However, existing fiducial markers rely solely on vision-based perception which suffers such limitations as occlusion, energy overhead, and privacy leakage.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690711"}, {"primary_key": "676716", "vector": [], "sparse_vector": [], "title": "Turbocharge Speech Understanding with Pilot Inference.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern speech understanding (SU) runs a sophisticated pipeline: ingesting streaming voice input, the pipeline executes encoder-decoder based deep neural networks repeatedly; by doing so, the pipeline generates tentative outputs (called hypotheses), and periodically scores the hypotheses.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690694"}, {"primary_key": "676717", "vector": [], "sparse_vector": [], "title": "A First Look at Apple&apos;s Stereoscopic Video and its Potential in Live Video Streaming for XR Headsets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the evolution of stereoscopic video technology, live video streaming stands at the threshold of a more engaging and immersive future. Apple's recent innovations in video and Extended Reality (XR) technologies have paved the way for immersive live video experiences. This work characterizes Apple's spatial video and explores its potential in live video streaming, providing insights into the future of video streaming applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697437"}, {"primary_key": "676718", "vector": [], "sparse_vector": [], "title": "Sisyphus: Redefining Low Power for LoRa Receiver.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Z<PERSON><PERSON> Gao", "<PERSON><PERSON>", "Li Lu"], "summary": "Legacy LoRa receiver adopts a superheterodyne architecture with a runtime power consumption of up to 100mW, resulting in its low-power promise can only be delivered in low duty-cycle mode. This paper presents <PERSON><PERSON>phus as an ultra-low-power LoRa receiver, ensuring around-the-clock LoRa availability while extending battery life significantly. To achieve this, we propose a novel receiver design for passive coherent demodulation of LoRa. In this design, we creatively couple LoRa's down-conversion with de-chirping (dc2), leveraging the processing gain brought by chirp spread spectrum (CSS) modulation to boost communication range without the need for additional power supply. Moreover, we exploit the cyclical time-frequency feature intrinsic to LoRa for demodulation, and a low-power analog-digital signal processing circuit with negligible power is devised to replace the existing power-intensive sampling and costly digital computation. We prototype Sisyphus for proof-of-concept, and comprehensive experimental results demonstrate that <PERSON><PERSON><PERSON> can achieve significant power savings compared to legacy LoRa receiver while retaining the anti-interference ability of legacy LoRa. We envision that the design of <PERSON><PERSON><PERSON> can unlock the potential for broader applications of LoRa.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690686"}, {"primary_key": "676719", "vector": [], "sparse_vector": [], "title": "MAMGDT: Enhancing Multi-Agent Systems with Multi-Game Decision Transformer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-agent systems (MAS) introduce the necessity about achieving collaborative goals and individual decision quality simultaneously. In the subfield of multi-agent reinforcement learning, transformer-based methods like MGDT enabled transportable utilization of temporal contexts in decision making for single agents. We introduces Multi-Agent Multi-Game Decision Transformer (MAMGDT) as a workaround for MAS tasks which presents a in-context learning approach to enhance decision-making in complex multi-agent environments. By incorporating causal masking and agent-wise interaction modeling, MAMGDT maintains temporal accuracy and captures the dynamics of agent interactions effectively. With the potential to optimize decision processes, MAMGDT is well-suited for various multi-agent tasks. We carried out demostration of MAMGDT on popular SMACv1 benchmarks with both collaborative and adversarial tasks, where MAMGDT outperformed other approaches.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694719"}, {"primary_key": "676720", "vector": [], "sparse_vector": [], "title": "Mirror Never Lies: Unveiling Reflective Privacy Risks in Glass-laden Short Videos.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Dai", "<PERSON><PERSON>"], "summary": "In the era of ubiquitous short-video sharing, an overlooked yet significant privacy risk has arisen: the accidental disclosure of confidential information through reflective surfaces, such as mirrors, glass, or even polished metal. Such reflections can inadvertently disclose sensitive personal details to a broad audience without the awareness of content creators. Our examination of 100 top-rated TikTok short videos reveals that, on average, 37.2% of frames in each video feature identifiable reflective surfaces, posing potential privacy risks. In this work, we introduce a framework designed to scrutinize reflective privacy risks in glass-laden short videos. At the heart of the framework is the development of a reflection-specific neural radiance field, termed RP-NeRF, which enables reflection-aware ray tracing for precise extraction and reconstruction of the reflective scenes from the surfaces they appear on. A detailed field study on the framework indicates that the precision in detecting human presence and recognizing objects from the reconstructed reflective images reaches as high as 90.8% and 89.6%, respectively, even when dealing with a reflective surface that boasts 90% transparency and a mere 4% reflectance rate. These findings highlight the urgent need for greater awareness and advanced solutions to safeguard privacy in our digital age, especially in light of the significant impact of short-video sharing.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690706"}, {"primary_key": "676721", "vector": [], "sparse_vector": [], "title": "LATTE: Layer Algorithm-aware Training Time Estimation for Heterogeneous Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhenjiang Li"], "summary": "Accurate estimation of on-device model training time is increasingly required for emerging learning paradigms on mobile edge devices, such as heterogeneous federated learning (HFL). HFL usually customizes the model architecture according to the different capabilities of mobile edge devices to ensure efficient use of local data from all devices for training. However, due to oversimplification of latency modeling, existing methods rely on a single coefficient to represent computational heterogeneity, resulting in sub-optimal HFL efficiency. We find that existing methods ignore the important impact of runtime optimization of deep learning frameworks, which we call development-chain diversity. Specifically, layers of a model may have different algorithm implementations, and deep learning frameworks often have different strategies for selecting the algorithm they believe is the best based on a range of runtime factors, resulting in different training latencies and invalid predictions from existing methods. In this paper, in addition to considering this diversity to ensure synchronized completion time of model training, we also study how to select the best algorithm each time to reduce the latency of the per-round training, thereby further improving the overall efficiency of federated training. To this end, we propose LATTE, which consists of a novel selector that identifies the best algorithm at runtime based on relative runtime factors. By further integrating it into our training latency model, LATTE provides accurate training time estimation. We develop LATTE as middleware, compatible with different deep learning frameworks. Extensive results show significantly improved training convergence speed and model accuracy compared to state-of-the-art methods.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690705"}, {"primary_key": "676722", "vector": [], "sparse_vector": [], "title": "Enabling High-rate Backscatter Sensing at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents μTag, an ultra-low-power backscatter sensor that supports high-frequency sensing of a large number of targets simultaneously. The core of μTag is an RF \"gene editing\" technique that embeds both the identity of the sensor and the real-time motion state of the attached target intensively in the transient features of the sensor's RF signal, in a collision-resilient manner. We provide practical techniques which i) generate such \"genetic signal\" with purely analog and extremely simple circuits; and ii) separate the signals from a large scale of sensors reliably. Our experimental results show that our design can support concurrent tracking of 150 targets with a 12kHz per-tag sampling rate. We also demonstrate with multiple sensing applications that μTag can achieve high-speed and large-scale motion tracking and rotation frequency sensing. The PCB power consumption of μTag is 38~107μW, according to the operating frequency of the tag. Our ASIC simulation based on the 40nm CMOS process shows that the power consumption can be further reduced to 0.13~0.52μW.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649351"}, {"primary_key": "676723", "vector": [], "sparse_vector": [], "title": "Deciphering the Enigma of Satellite Computing with COTS Devices: Measurement and Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qing Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the wake of the rapid deployment of large-scale low-Earth orbit satellite constellations, exploiting the full computing potential of Commercial Off-The-Shelf (COTS) devices in these environments has become a pressing issue. However, understanding this problem is far from straightforward due to the inherent differences between the terrestrial infrastructure and the satellite platform in space. In this paper, we take an important step towards closing this knowledge gap by presenting the first measurement study on the thermal control, power management, and performance of COTS computing devices on satellites. Our measurements reveal that the satellite platform and COTS computing devices significantly interplay in terms of the temperature and energy, forming the main constraints on satellite computing. Further, we analyze the critical factors that shape the characteristics of onboard COTS computing devices. We provide guidelines for future research on optimizing the use of such devices for computing purposes. Finally, we have released the datasets to facilitate further study in satellite computing.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649371"}, {"primary_key": "676724", "vector": [], "sparse_vector": [], "title": "Beamforming made Malicious: Manipulating Wi-Fi Traffic via Beamforming Feedback Forgery.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingzhi Hu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "New Wi-Fi systems have leveraged beamforming to manage a significant portion of traffic for achieving high throughput and reliability. Unfortunately, this has amplified certain security risks since beamforming critically relies on the clear-text beamforming feedback information (BFI): though similar risks have been exposed using emulation platforms (e.g., USRP), they have never proven realistic till this day. In this paper, we propose BeamCraft, the first attack to manipulate traffic in commodity Wi-Fi systems; it differs significantly from existing attacks either staying only on emulation platforms with limited real-world applicability or jamming communications by brute force. The core idea of BeamCraft involves corrupting beamforming decisions by injecting crafted BFIs that feed an access point (AP) with erroneous information on channel states. To mount a covert yet purposeful attack, we develop i) a joint location and transmit power selection strategy to evade detection by victims and ii) a novel BFI forgery method to effectively manipulate AP's beamforming decisions. We implement BeamCraft using commodity Wi-Fi devices and perform extensive evaluations with it; the results reveal that BeamCraft effectively manipulates Wi-Fi traffic while maintaining a low exposure rate.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690669"}, {"primary_key": "676725", "vector": [], "sparse_vector": [], "title": "Secure Beamforming and Obstacle Avoidance Trajectory Design for UAV-Assisted ISAC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Unmanned aerial vehicles (UAVs), known for their high flexibility and maneuverability, are regarded as the aerial platforms of future integrated sensing and communication (ISAC) networks. The communication and sensing functions of ISAC share the same spectrum and signal waveform, which often results in communication information being embedded within the sensing waveforms, thereby increasing the risk of information leakage. To enhance the security of UAV-assisted ISAC, we propose a beamforming strategy based on the mutual cooperation between communication and sensing. Specifically, by utilizing the sensing function to process echo signals, we estimate the positions of potential eavesdroppers and obstacles, which supports subsequent obstacle avoidance trajectory planning and physical layer security design. To ensure the transmission secrecy, we introduce artificial noise into the system. By designing the UAV transmit beamforming and the covariance matrix of the artificial noise, we formulate an optimization problem that aims to minimize the signal-to-noise ratio (SNR) received by the eavesdropper. To address this non-convex optimization problem, we propose an optimization algorithm that combines <PERSON><PERSON><PERSON>'s transform and semidefinite relaxation (SDR). Simulation results demonstrate that the SNR of eavesdropper remains at a low level throughout the flight of UAV, validating the effectiveness of the proposed scheme.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698229"}, {"primary_key": "676726", "vector": [], "sparse_vector": [], "title": "Access Point Deployment for Localizing accuracy and User Rate in Cell-Free Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shangqing Shi", "Daz<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Evolving next-generation mobile networks is designed to provide ubiquitous coverage and networked sensing. With utility of multi-view sensing and multi-node joint transmission, cell-free is a promising technique to realize this prospect. This paper aims to tackle the problem of access point (AP) deployment in cell-free systems to balance the sensing accuracy and user rate. By merging the D-optimality with Euclidean criterion, a novel integrated metric is proposed to be the objective function for both max-sum and maxmin problems, which respectively guarantee the overall and lowest performance in multi-user communication and target tracking scenario. To solve the corresponding high dimensional non-convex multi-objective problem, the Soft actor-critic (SAC) is utilized to avoid risk of local optimal result. Numerical results demonstrate that proposed SAC-based APs deployment method achieves 20% of overall performance and 120% of lowest performance.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698221"}, {"primary_key": "676727", "vector": [], "sparse_vector": [], "title": "Scalable Multi-Agent Reinforcement Learning for Effective UAV Scheduling in Multi-Hop Emergency Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xintao Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Utilizing unmanned aerial vehicles (UAVs) as mobile access points can assist urban communication systems in establishing emergency networks in disaster scenarios. However, in large-scale dynamic environments, the extensive exploration space makes effective collaboration among a large number of UAVs challenging. In this paper, to schedule the deployment of UAVs for networking purposes, we propose a novel approach, MAEN, using multi-agent reinforcement learning. The grouping and information sharing mechanisms in MAEN enable the algorithm to easily scale up the number of UAVs to dozens and address the issue of strategy equilibrium. Additionally, a reward decomposition module is designed to handle coordination and task allocation among UAVs. Experimental results demonstrate that the algorithm outperforms existing algorithms in terms of ground device coverage and communication quality.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694730"}, {"primary_key": "676728", "vector": [], "sparse_vector": [], "title": "Dynamic Resource Allocation for ISAC enabled Internet of Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The development of wireless communication technology is reshaping the landscape of intelligent transportation systems, particularly in the realm of internet of vehicles (IoV). Among these, integrated sensing and communications (ISAC) has garnered widespread attention by leveraging shared hardware resources or even spectrum between sensing and communication to achieve integrated benefits. For IoV, parameters such as target position and speed estimated by ISAC can be used as prior information for resource allocation and beamforming to improve communication performance. In this paper, we focus on ISAC-enabled IoV, where radar sensing signals and communication signals are transmitted within different slots of a subframe to avoid interference. We propose a resource allocation scheme to maximize system throughput while meeting the differentiated needs of all users, where spatial division multiple access is dynamically employed based on network load. Simulation outcomes verify the efficiency of the suggested algorithm.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698232"}, {"primary_key": "676729", "vector": [], "sparse_vector": [], "title": "Experience: Practical Challenges for Indoor AR Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper shares the challenges facing today's augmented reality (AR) smartphone applications, particularly in the realm of localization and tracking failure. Our research identifies limitations in current vision-based landmarks such as QR codes and AprilTags, commonly used to aid in localization, and the drawbacks of LiDAR integration in variable lighting conditions, compromising AR's accuracy and functionality. We also examine the constraints of Inertial Measurement Units (IMU) on movement speed, highlighting its impact on the dynamic performance of AR applications. Based on our extensive 316 experimental cases for 113 hours, including 34 case studies with 17 subjects in 2 sites, this paper presents the field with a nuanced analysis of the failure modes inherent in smartphone-based AR localization. We further explore a prototype solution which fuses ultra-wideband (UWB)-based sensing with the vision-based systems to alleviate these failure modes. Our approach addresses the immediate challenges of AR localization and opens avenues for future research and development in creating more spatially aware and interactive digital worlds. All of our demonstration videos, code, and datasets are available here1.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690676"}, {"primary_key": "676730", "vector": [], "sparse_vector": [], "title": "Binary Optical Machine Learning: Million-Scale Physical Neural Networks with Nano Neurons.", "authors": ["<PERSON><PERSON><PERSON>", "Z<PERSON><PERSON> An", "Qingrui Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning excels in advanced inference tasks using electronic neural networks (ENN), but faces energy consumption and limited computation speed challenges. To mitigate this, optical neural networks (ONNs) were developed, utilizing light for computations. However, their high manufacturing costs limited accessibility. In this work, we first introduce the binary optical neural network (BONN) - a streamlined ONN variant with binarized weights, which significantly reduces fabrication complexities and costs. Specifically, we address (i) the development of a binarization weight function aligned with backward-error propagation, and (ii) a simulation-based training for extra-large neural networks housing millions of neurons. We prototype six BONNs, each comprising four 0.8 × 0.8mm2 layers with one million 800 nm diameter neurons. Costs are cut to 0.13 USD per layer, marking a substantial decrease of 769× from previous ONNs. Experimental results reveal BONNs consume 2, 405× less power than leading ENNs while maintaining an average recognition accuracy of 74% across six datasets.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649384"}, {"primary_key": "676731", "vector": [], "sparse_vector": [], "title": "Logan: Loss-tolerant Live Video Analytics System.", "authors": ["<PERSON><PERSON>", "Minkyung Jeong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "KyoungSoo Park", "<PERSON><PERSON>"], "summary": "Cloud-based live video analytics with tight latency bound is gaining importance to support emerging applications such as UAVs and augmented reality. However, existing systems often struggle to meet stringent latency constraints under fluctuating network conditions with packet losses and late-arriving packets. We propose a loss-tolerant live video analytics system called Logan, which effectively accepts packet losses while maintaining high accuracy by utilizing the inherent resilience in DNNs. We design i) Codec-aware Inpainting, which accurately recovers the frame error from packet losses ii) Fast-Forward Recovery that prevents the remaining un-recovered error from propagating over future frames indefinitely. Our results show a 3× improvement (33.2%→99.9%) in SLO satisfaction rate compared to the reliable transmission scheme with <1% accuracy drop under a 5% packet loss rate.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690695"}, {"primary_key": "676732", "vector": [], "sparse_vector": [], "title": "An Architectural Framework for 6G Network Digital Twins System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the context of 6G, Digital Twin technology has the potential to play a crucial role by offering a sophisticated, dynamic model that mirrors the physical world in real-time. Integrating digital twins into 6G enables precise orchestration and optimization of network resources, meeting the diverse and stringent demands of next-generation applications. Furthermore, digital twins provide a platform for continuous learning and AI-driven analysis. Research on the architecture of Network Digital Twins (NDTs) for 5G/6G is still limited, often focusing on partial implementations rather than comprehensive, full-stack approaches. This paper proposes a high-level architectural framework for 6G NDTs system, structured across three layers: the Physical Twin Layer, Digital Twin Layer, and Application and Service Layer. We discuss the challenges of deploying these systems and the importance of selecting appropriate tools, presenting an experimental case study that demonstrates the impact of different tool choices on system performance. Our findings underscore the need for flexible, scalable solutions to fully realize the benefits of NDTs in 6G networks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696730"}, {"primary_key": "676733", "vector": [], "sparse_vector": [], "title": "TransCompressor: LLM-Powered Multimodal Data Compression for Smart Transportation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The incorporation of Large Language Models (LLMs) into smart transportation systems has paved the way for improving data management and operational efficiency. This study introduces TransCompressor, a novel framework that leverages LLMs for efficient compression and decompression of multimodal transportation sensor data. TransCompressor has undergone thorough evaluation with diverse sensor data types, including barometer, speed, and altitude measurements, across various transportation modes like buses, taxis, and Mass Transit Railways (MTRs). Comprehensive evaluation illustrates the effectiveness of TransCompressor in reconstructing transportation sensor data at different compression ratios. The results highlight that, with well-crafted prompts, LLMs can utilize their vast knowledge base to contribute to data compression processes, enhancing data storage, analysis, and retrieval in smart transportation settings.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698120"}, {"primary_key": "676734", "vector": [], "sparse_vector": [], "title": "WiProfile: Unlocking Diffraction Effects for Sub-Centimeter Target Profiling Using Commodity WiFi Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Despite intensive research efforts in radio frequency noncontact sensing, capturing fine-grained geometric properties of objects, such as shape and size, remains an open problem using commodity WiFi devices. Prior attempts are incapable of characterizing object shape or size because they predominantly rely on weak signals reflected off objects in a very small number of directions. In this paper, motivated by the observation that the diffracted signals around an object between two WiFi devices carry the contour information of the object, we formulate the problem of reconstructing the 2D target profile and develop WiProfile, the first WiFi-based system that unlocks the diffraction effects for target profiling. We introduce a CSI-Profile model to characterize the relationship between the CSI measured at different target positions and the target profile in the diffraction zone. With suitable approximations, the inverse problem of deriving the target profile from CSI can be solved by the inverse Fr<PERSON>nel transform. To mitigate CSI measurement errors on commodity WiFi devices, we propose a novel antenna placement strategy. Comprehensive experiments demonstrate that WiProfile can accurately reconstruct profiles with median absolute errors of less than 1 cm under various conditions, and effectively estimate the profiles of everyday objects of diverse shapes, sizes, and materials. We believe this work opens up new directions for fine-grained target imaging using commodity WiFi devices.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649355"}, {"primary_key": "676735", "vector": [], "sparse_vector": [], "title": "A Client Detection and Parameter Correction Algorithm for Clustering Defense in Clustered Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As a new federated learning(FL) paradigm, clustered federated learning (CFL) could effectively address the issue of model training accuracy loss due to different data distribution in FL. However, the introduction of the clustering process also brings new risks. Adversaries can implement model poisoning by adding crafted perturbations with clients' model parameters, potentially resulting in overall clustering failure. To tackle this problem, we propose a client detection and parameter correction framework in this paper. Our approach aims to identify malicious clients by analyzing the difference in vector parameter density distribution between malicious and benign clients. We precisely locate malicious perturbations in the parameters and recover them, enabling the server to effectively utilize benign updates for normal clustering and training within the CFL framework. Experiment results show that our defense algorithm outperforms others, consistently improving training accuracy by an average of 30% under various kinds of attacks.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698247"}, {"primary_key": "676736", "vector": [], "sparse_vector": [], "title": "Asteroid: Resource-Efficient Hybrid Pipeline Parallelism for Collaborative DNN Training on Heterogeneous Edge Devices.", "authors": ["Shengyuan Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "On-device Deep Neural Network (DNN) training has been recognized as crucial for privacy-preserving machine learning at the edge. However, the intensive training workload and limited onboard computing resources pose significant challenges to the availability and efficiency of model training. While existing works address these challenges through native resource management optimization, we instead leverage our observation that edge environments usually comprise a rich set of accompanying trusted edge devices with idle resources beyond a single terminal. We propose Asteroid, a distributed edge training system that breaks the resource walls across heterogeneous edge devices for efficient model training acceleration. Asteroid adopts a hybrid pipeline parallelism to orchestrate distributed training, along with a judicious parallelism planning for maximizing throughput under certain resource constraints. Furthermore, a fault-tolerant yet lightweight pipeline replay mechanism is developed to tame the device-level dynamics for training robustness and performance stability. We implement Asteroid on heterogeneous edge devices with both vision and language models, demonstrating up to 12.2× faster training than conventional parallelism methods and 2.1× faster than state-of-the-art hybrid parallelism methods through evaluations. Furthermore, Asteroid can recover training pipeline 14× faster than baseline methods while preserving comparable throughput despite unexpected device exiting and failure.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649363"}, {"primary_key": "676737", "vector": [], "sparse_vector": [], "title": "Understanding the Impact of Cellular RAN-induced Delay on Video Conferencing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Congestion-control algorithms for video-conferencing applications work well in wired networks but are fragile in cellular networks due to high delay variations and variable capacity in these networks. This paper investigates the causes of delay variations in cellular networks using a cross-layer approach. By measuring a WebRTC application over LTE at both the physical and network layers, we identify the effects of such delay inflation caused by physical-layer resource scheduling and link-layer retransmissions.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697445"}, {"primary_key": "676738", "vector": [], "sparse_vector": [], "title": "Health-MR: A Mixed Reality-Based Patient Registration and Monitor Medical System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Tao Han"], "summary": "In medical procedures such as surgery and diagnosis, it is crucial to provide doctors and nurses with up-to-date patient information. In this paper, we propose Health-MR, a portable Mixed-Reality (MR) system that helps medical staff monitor patient conditions. Health-MR consists of three components: (1) Patient Identification Recognition using face detection, (2) Medical Cloud Database for patient information retrieval, and (3) Non-invasive Heart Rate Measurement via image processing and Fast Fourier Transform (FFT). Our evaluation demonstrates that Health-MR significantly reduces the time needed to query patient information and provides remote, accurate, and real-time heart rate monitoring.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696214"}, {"primary_key": "676739", "vector": [], "sparse_vector": [], "title": "Revolutionizing LoRa Gateway with XGate: Scalable Concurrent Transmission across Massive Logical Channels.", "authors": ["<PERSON><PERSON>", "Xianjin Xia", "<PERSON><PERSON><PERSON>", "Yuan<PERSON> Zheng", "<PERSON> Gu"], "summary": "LoRa is a promising technology that offers ubiquitous low-power IoT connectivity. With the features of multi-channel communication, orthogonal transmission, and spectrum sharing, LoRaWAN is poised to connect millions of IoT devices across thousands of logical channels. However, current LoRa gateways utilize hardwired Rx chains that cover only a small fraction (<1%) of the logical channels, limiting the potential for massive LoRa communications. This paper presents XGate, a novel gateway design that uses a single Rx chain to concurrently receive packets from all logical channels, fundamentally enabling scalable LoRa transmission and flexible network access. Unlike hardwired Rx chains in the current gateway design, XGate allocates resources including software-controlled Rx chains and demodulators based on the extracted meta information of incoming packets. XGate addresses a series of challenges to efficiently detect incoming packets without prior knowledge of their parameter configurations. Evaluations show that XGate boosts LoRa concurrent transmissions by 8.4× than state-of-the-art.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649375"}, {"primary_key": "676740", "vector": [], "sparse_vector": [], "title": "Mobile Foundation Model as Firmware.", "authors": ["<PERSON><PERSON>g Yuan", "<PERSON>", "<PERSON><PERSON>ai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xianqing Jia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the current AI era, mobile devices such as smartphones are tasked with executing a myriad of deep neural networks (DNNs) locally. It presents a complex landscape, as these models are highly fragmented in terms of architecture, operators, and implementations. Such fragmentation poses significant challenges to the co-optimization of hardware, systems, and algorithms for efficient and scalable mobile AI.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649361"}, {"primary_key": "676741", "vector": [], "sparse_vector": [], "title": "Venus: Enhancing QoE of Crowdsourced Live Video Streaming by Exploiting Multiflow Viewer Assistance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Congkai An", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Huadong Ma", "<PERSON><PERSON><PERSON>"], "summary": "Despite the prevalence of Crowdsourced Live Video Streaming (CLVS), video viewers still suffer from low QoE particularly under rush hours, as the existing Content Delivery Network (CDN) is not scalable enough to handle the massive concurrent streaming. The rapid emergence of Web 3.0 provides new incentives for revisiting and applying the classical P2P networking in CLVS. However, the highly dynamic joining or leaving behavior of CLVS viewers frequently interrupts the real-time streaming and leads to low QoE, which demands to retrofit P2P. In this work, we bridge the gap by proposing a reliable P2P-assisted CLVS system named Venus, where viewers can share their streaming content smoothly, without video freeze regardless of viewers leaving. To realize Venus, different from the single-flow sharing in previous P2P video streaming, we design a novel multiflow framework with lightweight redundancy encoding, so as to handle the inherently high viewer dynamics. Correspondingly, we introduce a multiflow scheduler to enable QoE adaption concertedly over heterogeneous multiple flows. Real-world evaluation confirms the benefits of decentralized CLVS streaming, with Venus outperforming the state-of-the-art CDN solution by almost totally eliminating the video stall while enhancing the video quality by 10.2%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649354"}, {"primary_key": "676742", "vector": [], "sparse_vector": [], "title": "Privacy Measurement of Physical Attributes on Voice Anonymity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Various methods have been proposed for protecting the speaker's identity while preserving speech intelligibility. However, existing studies fail to consider the overall tradeoff between speech utility, speaker verification, and inference of voice physical attributes, such as emotion, age, accent, and gender. we propose a tradeoff metric to encapsulate voice biometrics as well as different voice attributes, to study the feasibility of applying cutting-edge voice anonymization solutions to achieve the optimum tradeoff between privacy protection and speech utility.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697448"}, {"primary_key": "676743", "vector": [], "sparse_vector": [], "title": "Inaudible Backdoor Attack via Stealthy Frequency Trigger Injection in Audio Spectrogram.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning-enabled Voice User Interfaces (VUIs) have surpassed human-level performance in acoustic perception tasks. However, the significant cost associated with training these models compels users to rely on third-party data or outsource training services. Such emerging trends have drawn substantial attention to training-phase attacks, particularly backdoor attacks. Such attacks implant hidden trigger patterns (e.g., tones, environmental sounds) into the model during training, thereby manipulating the model's predictions in the inference phase. However, existing backdoor attacks can be easily undermined in practice as the inserted triggers are audible. Users may notice such attacks when listening to the training data and remaining alert for suspicious sounds. In this work, we present a novel audio backdoor attack that exploits completely inaudible triggers in the frequency domain of the audio spectrograms. Specifically, we optimize the trigger to be a frequency-domain pattern with the energy below the noise floor (e.g., background and hardware noises) at any given frequency, thereby rendering the trigger inaudible. To realize such attacks, we design a strategy that automatically generates inaudible triggers in the spectrum supported by commodity playback devices (e.g., smartphones and laptops). We further develop optimization techniques to enhance the trigger's robustness against speech content and onset variations. Experiments on hotword and speaker recognition indicate that our attack can achieve attack success rates of more than 98.2% and 81.0% under digital and physical attack scenarios. The results also demonstrate the trigger's inaudibility with a Signal-to-Noise Ratio (SNR) less than -3.54 dB against background noises. We further verify that our attack can successfully bypass state-of-the-art backdoor defense strategies based on learning and audio processing.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649345"}, {"primary_key": "676744", "vector": [], "sparse_vector": [], "title": "Optimizing Limited-Stop Transit Operations Through Simulation: A Case Study of Shenzhen, China.", "authors": ["<PERSON><PERSON><PERSON>", "Xiancai Tian", "Jinkai Li", "Xi<PERSON>"], "summary": "Limited-stop service represents a paradigm for enhancing efficiency in transportation systems, with applications spanning public transit, logistics, and supply chain management. This service model operates along a designated route featuring a reduced number of stops in comparison to standard services, thereby facilitating expedited transportation. In this research, we utilize accumulated data from Intelligent Transportation Systems for the development and deployment of a simulation-driven framework tailored towards augmenting the operational efficiency of limited-stop services. To illustrate the practical application of this framework, we focus on the paradigm of limited-stop bus services, elucidating the intricate technical facets of the proposed system. The efficacy and viability of this framework have been validated through a series of empirical investigations conducted within the urban bus transit network of Shenzhen, a prominent metropolis situated in China.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3694731"}, {"primary_key": "676745", "vector": [], "sparse_vector": [], "title": "Hydra: Attacking OFDM-base Communication System via Metasurfaces Generated Frequency Harmonics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "While Reconfigurable Intelligent Surfaces (RIS) have been shown to enhance OFDM communication performance, this paper unveils a potential security concern arising from widespread RIS deployment. Malicious actors could exploit vulnerabilities to hijack or deploy rogue RIS, transforming them from communication boosters into attackers. We present a novel attack that disrupts the critical orthogonality property of OFDM subcarriers, severely degrading communication performance. This attack is achieved by manipulating the RIS to generate frequency-shifted reflections/harmonics of the original OFDM signal. We also propose algorithms to simultaneously beamform the multiple RIS-generated frequency-shifted reflections towards selected targets. Extensive experiments conducted in indoor, outdoor, 3D, and office settings demonstrate that Hydra can achieve a 90% throughput reduction in targeted attack scenarios and a 43% throughput reduction in indiscriminate attack scenarios. Furthermore, we validated the effectiveness of our attacks on both the 802.11 protocol and the 5G NR protocol.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690670"}, {"primary_key": "676746", "vector": [], "sparse_vector": [], "title": "Map++: Towards User-Participatory Visual SLAM Systems with Efficient Map Expansion and Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Constructing precise 3D maps is crucial for the development of future map-based systems such as self-driving and navigation. However, generating these maps in complex environments, such as multi-level parking garages or shopping malls, remains a formidable challenge. In this paper, we introduce a participatory sensing approach that delegates map-building tasks to map users, thereby enabling cost-effective and continuous data collection. The proposed method harnesses the collective efforts of users, facilitating the expansion and ongoing update of the maps as the environment evolves. We realized this approach by developing Map++, an efficient system that functions as a plug-and-play extension, supporting participatory map-building based on existing SLAM algorithms. Map++ addresses a plethora of scalability issues in this participatory map-building system by proposing a set of lightweight, application-layer protocols. We evaluated Map++ in four representative settings: an indoor garage, an outdoor plaza, a public SLAM benchmark, and a simulated environment. The results demonstrate that Map++ can reduce traffic volume by approximately 46% with negligible degradation in mapping accuracy, i.e., less than 0.03m compared to the baseline system. It can support approximately $2 \\times$ as many concurrent users as the baseline under the same network bandwidth. Additionally, for users who travel on already-mapped trajectories, they can directly utilize the existing maps for localization and save 47% of the CPU usage.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649386"}, {"primary_key": "676747", "vector": [], "sparse_vector": [], "title": "AutoJournaling: A Context-Aware Journaling System Leveraging MLLMs on Smartphone Screenshots.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Simon D&apos;Alfonso"], "summary": "Journaling offers significant benefits, including fostering self-reflection, enhancing writing skills, and aiding in mood monitoring. However, many people abandon the practice because traditional journaling is time-consuming, and detailed life events may be overlooked if not recorded promptly. Given that smartphones are the most widely used devices for entertainment, work, and socialization, they present an ideal platform for innovative approaches to journaling. Despite their ubiquity, the potential of using digital phenotyping, a method of unobtrusively collecting data from digital devices to gain insights into psychological and behavioral patterns, for automated journal generation has been largely underexplored. In this study, we propose AutoJournaling, the first-of-its-kind system that automatically generates journals by collecting and analyzing screenshots from smartphones. This system captures life events and corresponding emotions, offering a novel approach to digital phenotyping. We evaluated AutoJournaling by collecting screenshots every 3 seconds from three students over five days, demonstrating its feasibility and accuracy. AutoJournaling is the first framework to utilize seamlessly collected screenshots for journal generation, providing new insights into psychological states through digital phenotyping.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698122"}, {"primary_key": "676748", "vector": [], "sparse_vector": [], "title": "QUIC-Enabled Framework for Alleviating Transient Congestion in Time-Critical IoT.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Haifeng Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The real-time control capability of IoT devices is contingent upon the transmission of packets. However, due to the influence of multiple devices accessing the network, the bandwidth available to IoT devices from access points may decline significantly, which causes a surge in the queuing latency and interrupts the transmission. This phenomenon is referred to as transient congestion. To achieve stable high-quality network service, this paper designs RushWay, a QUIC-enabled framework for alleviating transient congestion. RushWay employs stream multiplexing to compress the original packet into the stream frame, thereby reducing the bandwidth required for transmission. Furthermore, RushWay employs an adaptive decision-making algorithm to assess uplink queue conditions and packet latency requirements, thereby alleviating transient congestion. The simulation results demonstrate that RushWay can improve key performance by 13% to 94%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697424"}, {"primary_key": "676749", "vector": [], "sparse_vector": [], "title": "Towards In-context Environment Sensing for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Environment sensing is a fundamental task in mobile augmented reality (AR). However, on-device sensing and computing resources often limit mobile AR sensing capability, making high-quality environment sensing challenging to achieve. In recent years, in-context sensing, a new sensing system design paradigm, has emerged with the promise of achieving accurate, efficient, and robust sensing results. In this work, we first formally define the in-context sensing design paradigm. We summarize its primary challenges as the uncertainty of environmental information availability. To quantify the impact of sensing context data, we present two in-depth case studies that show how it can impact different aspects of mobile AR sensing systems.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3696211"}, {"primary_key": "676750", "vector": [], "sparse_vector": [], "title": "Connecting Foundation Models with the Physical World using Reconfigurable Drone Agents.", "authors": ["<PERSON><PERSON>", "Kai<PERSON> Hou", "Jun<PERSON> Xi<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Foundation models excel in tasks such as content generation, zero-shot classifications, and reasoning. However, they struggle with sensing, interacting, and actuating in the physical world due to their dependence on limited sensors and actuators in providing timely contextual information or physical interactions. This reliance restricts the system's adaptability and coverage. To address these issues and create an embodied AI with foundation models (FMs), we introduce Embodied Reconfigurable Drone Agent (EmbodiedRDA). EmbodiedRDA features a custom drone platform that can autonomously swap payloads to reconfigure itself with a diverse list of sensors and actuators. We designed FM agents to instruct the drone to equip itself with appropriate physical modules, analyze sensor data, make decisions, and control the drone's actions. This enables the system to perform a variety of tasks in dynamic physical environments, bridging the gap between the digital and physical worlds.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698846"}, {"primary_key": "676751", "vector": [], "sparse_vector": [], "title": "Foes or Friends: Embracing Ground Effect for Edge Detection on Lightweight Drones.", "authors": ["<PERSON><PERSON>", "Ciyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Drone-based rapid and accurate environmental edge detection is highly advantageous for tasks such as disaster relief and autonomous navigation. Current methods, using radar or cameras, raise deployment costs and burden lightweight drones with high computational demands. In this paper, we propose AirTouch, a system that transforms the ground effect from a stability \"foe\" in traditional flight control views, into a \"friend\" for accurate and efficient edge detection. Our key insight is that analyzing drone sensor readings and flight commands allows us to detect ground effect changes. Such changes typically indicate the drone flying over an edge, making this information valuable for edge detection. We approach this insight through theoretical analysis, algorithm design, and implementation, fully leveraging the ground effect as a new sensing modality without compromising drone flight stability, thereby achieving accurate and efficient scene edge detection. Extensive evaluations demonstrate that our system achieves a high detection accuracy with mean detection distance errors of 0.051m, outperforming the baseline performance by 86%.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690699"}, {"primary_key": "676752", "vector": [], "sparse_vector": [], "title": "Few-shot Learning and Data Augmentation for Cross-Domain UAV Fingerprinting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a novel approach to cross-domain unmanned aerial vehicle (UAV) authentication using radio frequency (RF) fingerprinting based on prototypical networks (PTNs). UAVs present a unique challenge for RF fingerprinting due to their hovering motion, which creates more diverse signal domains compared to other RF devices like Wi-Fi. This results in a severe domain shift problem, where well-trained models struggle to generalize to unseen domains. To address this issue without incurring significant costs in data collection and model retraining, we employ PTNs, a few-shot learning paradigm that enhances cross-domain performance and system viability. We further improve our method's effectiveness by incorporating fine-tuning with data augmentation, maintaining system viability while improving performance. Comprehensive experimental results demonstrate that our approach significantly mitigates domain shift, achieving up to a 20% improvement in cross-domain accuracy for UAV fingerprinting.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698248"}, {"primary_key": "676753", "vector": [], "sparse_vector": [], "title": "Exploring Visual Explanations for Defending Federated Learning against Poisoning Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes a new visual explanation-based defense mechanism, namely, FedCAMAE, against model poisoning attacks on federated learning (FL), which integrates Layer Class Activation Mapping (LayerCAM) and autoencoder to offer a scientifically more powerful detection capability compared to existing Euclidean distance-based or machine learning-based approaches. Specially, FedCAMAE generates a fine-grained heat map assisted by Layer-CAM for each uploaded local model update, transforming each local model update into a lower-dimensional, visual representation. To accentuate the hidden features of the heat maps, autoencoder is seamlessly embedded into the proposed FedCAMAE, which can refine the the heat maps and enhance their distinguishability, thereby increasing the success rate of identifying anomalous heat maps and malicious local models. We test ResNet-50 and REGNETY-800MF deep learning models with SVHN and CIFAR-100 datasets under Non-Independent and Identically Distributed (Non-IID) setting, respectively. The results demonstrate that Fed-CAMAE offers superior test accuracy of FL global model compared to the state-of-the-art methods. Our code is available at: https://github.com/jjzgeeks/LayerCAM-AE", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3697430"}, {"primary_key": "676754", "vector": [], "sparse_vector": [], "title": "On-Device Speech Filtering for Privacy-Preserving Acoustic Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Acoustic sensing has become increasingly prevalent for mobile and ambient devices for applications such as human activity recognition, health monitoring, and environmental sensing. These approaches develop audio featurization techniques to enable machine learning-based inferences while offering privacy by preventing speech reconstruction. However, recent work [2] has shown that such methods are still vulnerable to speech content recovery when fine-tuned automatic speech recognition (ASR) models are applied. Here, we demonstrate the broad applicability of on-device speech filtering using a detect-and-remove method for acoustic sensing tasks, significantly reducing privacy risks in revealing speech content. Additionally, we introduce an interactive tool to experiment with audio featurization methods, aiding the development of privacy-preserving applications.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3698865"}, {"primary_key": "676755", "vector": [], "sparse_vector": [], "title": "Rethinking Orientation Estimation with Smartphone-equipped Ultra-wideband Chips.", "authors": ["<PERSON><PERSON>", "<PERSON>ang <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While localization has gained a tremendous amount of attention from both academia and industry, much less attention has been paid to equally important orientation estimation. Traditional orientation estimation systems relying on gyroscopes suffer from cumulative errors. In this paper, we propose UWBOrient, the first fine-grained orientation estimation system utilizing ultra-wideband (UWB) modules embedded in smartphones. The proposed system presents an alternative solution that is more accurate than gyroscope estimates and free of error accumulation. We propose to fuse UWB estimates with gyroscope estimates to address the challenge associated with UWB estimation alone and further improve the estimation accuracy. UWBOrient decreases the estimation error from the state-of-the-art 7.6° to 2.7° while maintaining a low latency (20 ms) and low energy consumption (40 mWh). Comprehensive experiments with both iPhone and Android smartphones demonstrate the effectiveness of the proposed system under various conditions including natural motion, dynamic multipath and NLoS. Two real-world applications, i.e., head orientation tracking and 3D reconstruction are employed to showcase the practicality of UWBOrient.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690677"}, {"primary_key": "676756", "vector": [], "sparse_vector": [], "title": "Malicious Attacks against Multi-Sensor Fusion in Autonomous Driving.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yunnan Yu", "Lu <PERSON>", "<PERSON><PERSON>"], "summary": "Multi-sensor fusion has been widely used by autonomous vehicles (AVs) to integrate the perception results from different sensing modalities including LiDAR, camera and radar. Despite the rapid development of multi-sensor fusion systems in autonomous driving, their vulnerability to malicious attacks have not been well studied. Although some prior works have studied the attacks against the perception systems of AVs, they only consider a single sensing modality or a camera-LiDAR fusion system, which can not attack the sensor fusion system based on LiDAR, camera, and radar. To fill this research gap, in this paper, we present the first study on the vulnerability of multi-sensor fusion systems that employ LiDAR, camera, and radar. Specifically, we propose a novel attack method that can simultaneously attack all three types of sensing modalities using a single type of adversarial object. The adversarial object can be easily fabricated at low cost, and the proposed attack can be easily performed with high stealthiness and flexibility in practice. Extensive experiments based on a real-world AV testbed show that the proposed attack can continuously hide a target vehicle from the perception system of a victim AV using only two small adversarial objects.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3649372"}, {"primary_key": "676757", "vector": [], "sparse_vector": [], "title": "Bere: A Novel Video Recommender System for Virtual Reality Using Human Behavioral Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hongbo Guo", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While video recommendation has been studied extensively in regular PC and smartphone settings, such a topic has been rarely discussed in the virtual reality (VR) context so far. On the other hand, as the popularity of VR videos continues to soar, its recommendation will play a crucial part in providing suggestions and guiding users through a deluge of available content. Given this unmet need, in this work, we present Bere, a video recommender system tailored for VR. Our approach leverages viewers' behavioral responses as they engage with VR videos to infer their preferences and thus make future recommendations. We integrate these new behavioral user-video interaction measures into the mainstream recommendation framework and renovate the graph learning-based paradigm to accommodate the new changes. The recommender system is further empowered with a novel domain adaptation approach named CMCCDA to address the data scarcity problem for model training. We also develop an energy-efficient adaptive encoding scheme to reduce the energy consumption on the VR device. We collect a behavioral dataset for video recommendation in VR and demonstrate through extensive evaluation that <PERSON>re significantly outperforms state-of-the-art schemes by up to 68.0% in precision and up to 28.8% in ranking quality.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534.3690660"}, {"primary_key": "725302", "vector": [], "sparse_vector": [], "title": "Proceedings of the 30th Annual International Conference on Mobile Computing and Networking, ACM MobiCom 2024, Washington D.C., DC, USA, November 18-22, 2024", "authors": ["Weisong Shi", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is my pleasure to welcome you to the 12th edition of MobiCom, the premier Conference on mobile computing and communications. We are happy to have this event back on the beautiful West Coast of the United States, after a very successful edition last year in Cologne, Germany. It has been my honor to serve as General Chair.We have an outstanding technical program, starting with three tutorials on three very timely topics (mobility models, delay tolerant routing and RFIDs). The main conference technical sessions feature competitively selected papers in a broad range of mobile computing topics - from sensor networks to urban meshes, cellular services and wireless foundations and models. Two internationally renowned authorities in the wireless and mobile computing fields will deliver invited speeches. Dr <PERSON>, Viterbi Foundation, will be the Keynote speaker. Dr. <PERSON>, Professor, Computer Science Dept, UCLA, and Director of the Center for Embedded Networked Sensing (CENS) at UCLA will be the <PERSON> Lecturer, delivering a talk on Wireless Sensing Systems: From ecosystems to human systems. Jointly with the conference, there will be eight outstanding Workshops that deal with more specialized topics in various areas of mobile, wireless networking and computing: VANET, WISE, WMASH, WINTECH, MOBISHARE, CRAWDED, DIWANS, WUWNET. To complete the picture, there will be a Poster session, and a Student Competition session. The main theme that emerges from these diverse contributions, and the main message to our attendees, is the coming together of very different wireless technologies, from low power sensor radios to ad hoc networks, mesh and cellular networks to enable integrated, pervasive mobile computing. The synergy of these networks will provide the structure that can support and integrate emerging mobile applications ranging from mobile sensor platforms to vehicular networks and broadband mobile Internet services.", "published": "2024-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3636534"}]