'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AnnouncementBarProps {
  message?: string;
}

export default function AnnouncementBar({ message }: AnnouncementBarProps) {
  const [isVisible, setIsVisible] = useState(false);
  
  // 默认消息
  const defaultMessage = '🎉 最新加入 GPT-4o、Claude 4 Sonnet 以及 Gemini 2.5 Flash 模型 （关闭后不再提醒）';
  const displayMessage = message || defaultMessage;
  
  useEffect(() => {
    // 检查本地存储中的关闭状态和时间戳
    const checkAnnouncementStatus = () => {
      const storedStatus = localStorage.getItem('announcementClosed');
      
      if (storedStatus) {
        const { timestamp } = JSON.parse(storedStatus);
        const now = Date.now();
        const oneDayInMs = 999 * 24 * 60 * 60 * 1000;
        // const oneDayInMs = 1000;
        
        // 如果关闭时间不到一天，则保持隐藏
        if (now - timestamp < oneDayInMs) {
          setIsVisible(false);
        } else {
          // 超过一天，重新显示
          setIsVisible(true);
        }
      } else {
        // 没有存储记录，显示公告
        setIsVisible(true);
      }
    };
    
    checkAnnouncementStatus();
  }, []);
  
  const handleClose = () => {
    setIsVisible(false);
    
    // 保存关闭状态和时间戳到本地存储
    localStorage.setItem('announcementClosed', JSON.stringify({
      timestamp: Date.now()
    }));
  };
  
  if (!isVisible) return null;
  
  return (
    <div className="mb-4 mx-auto">
      <div className="px-4 pt-2 flex flex-wrap items-center sm:items-start">
        <div className="text-sm text-gray-700 dark:text-gray-300 underline decoration-gray-400 dark:decoration-gray-600 underline-offset-2 mr-2 min-w-[100px] max-w-[200px] sm:max-w-[300px] md:max-w-none">
          {displayMessage}
        </div>
        <div className="sm:hidden w-full flex justify-center mt-2">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6" 
            onClick={handleClose}
            aria-label="关闭公告"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="hidden sm:block">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6" 
            onClick={handleClose}
            aria-label="关闭公告"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}