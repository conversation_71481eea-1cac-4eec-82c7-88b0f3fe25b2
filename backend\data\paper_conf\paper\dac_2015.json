[{"primary_key": "4390452", "vector": [], "sparse_vector": [], "title": "Information leakage chaff: feeding red herrings to side channel attackers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A prominent threat to embedded systems security is represented by side-channel attacks: they have proven effective in breaching confidentiality, violating trust guarantees and IP protection schemes. State-of-the-art countermeasures reduce the leaked information to prevent the attacker from retrieving the secret key of the cipher. We propose an alternate defense strategy augmenting the regular information leakage with false targets, quite like chaff countermeasures against radars, hiding the correct secret key among a volley of chaff targets. This in turn feeds the attacker with a large amount of invalid keys, which can be used to trigger an alarm whenever the attack attempts a content forgery using them, thus providing a reactive security measure. We realized a LLVM compiler pass able to automatically apply the proposed countermeasure to software implementations of block ciphers. We provide effectiveness and efficiency results on an AES implementation running on an ARM Cortex-M4 showing performance overheads comparable with state-of-the-art countermeasures.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744859"}, {"primary_key": "4390453", "vector": [], "sparse_vector": [], "title": "Verifying inevitability of phase-locking in a charge pump phase lock loop using sum of squares programming.", "authors": ["<PERSON><PERSON><PERSON> ul <PERSON>", "<PERSON>"], "summary": "Phase-locking in a charge pump (CP) phase lock loop (PLL) is said to be inevitable if all possible states of the CP PLL eventually converge to the equilibrium where the input and output phases are in lock. We verify this property for a CP PLL using a mixed deductive and bounded verification methodology. This involves a positivity check of polynomial inequalities (which is an NP-Hard problem) so we use the sound but incomplete Sum of Squares (SOS) relaxation algorithm to provide a numerical solution.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744926"}, {"primary_key": "4390454", "vector": [], "sparse_vector": [], "title": "Mask assignment and synthesis of DSA-MP hybrid lithography for sub-7nm contacts/vias.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Integrating Directed Self Assembly (DSA) and Multiple Patterning (MP) is an attractive option for printing contact and via layers for sub-7nm process nodes. In the DSA-MP hybrid process, an optimized decomposition algorithm is required to perform the MP mask assignment while considering the DSA advantages and limitations. In this paper, we present an optimal Integer Linear Programming (ILP) formulation for the simultaneous DSA grouping and MP decomposition problem for contacts and vias. Then we propose a heuristic and develop an efficient algorithm for solving the same problem. In comparison to the optimal ILP results, the proposed algorithm is 197x faster and results in 16.3% more violations. The proposed algorithm produces 56% fewer violations than the sequential approaches which perform DSA grouping followed by MP decomposition and vice versa.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744868"}, {"primary_key": "4390455", "vector": [], "sparse_vector": [], "title": "Models, abstractions, and architectures: the missing links in cyber-physical systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bridging disparate realms of physical and cyber system components requires models and methods that enable rapid evaluation of design alternatives in cyber-physical systems (CPS). The diverse intellectual traditions of physical and mathematical sciences makes this task exceptionally hard. This paper seeks to explore potential solutions by examining specific examples of CPS applications in automobiles and smart buildings. Both smart buildings and automobiles are complex systems with embedded knowledge across several domains. We present our experiences with development of CPS applications to illustrate the challenges that arise when expertise across domains is integrated into the system, and show that creation of models, abstractions, and architectures that address these challenges are key to next generation CPS applications.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747936"}, {"primary_key": "4390456", "vector": [], "sparse_vector": [], "title": "The SIMON and SPECK lightweight block ciphers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The <PERSON> and <PERSON> families of block ciphers were designed specifically to offer security on constrained devices, where simplicity of design is crucial. However, the intended use cases are diverse and demand flexibility in implementation. Simplicity, security, and flexibility are ever-present yet conflicting goals in cryptographic design. This paper outlines how these goals were balanced in the design of <PERSON> and <PERSON><PERSON><PERSON>.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747946"}, {"primary_key": "4390457", "vector": [], "sparse_vector": [], "title": "Designing time partitions for real-time hypervisor with sufficient temporal independence.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Virtualization techniques for embedded real-time systems, as known from the Integrated Modular Avionics (IMA) architecture of the ARINC653 standard, typically employ a TDMA scheduling to achieve temporal isolation among different virtualized partitions. Due to the fixed TDMA schedule, the worst case interrupt response times are significantly increased. An already proposed technique to mitigate this problem is to allow interrupts within an TDMA schedule, in order to achieve better interrupt response times while maintaining a sufficient degree of temporal independence via monitoring. In this paper we propose a novel approach that optimizes the TDMA schedule based on the partitions internal timing behavior and tasks parameters. The developed optimization algorithm generates a maximum amount of slack within the TDMA cycle. This slack is later used to interpose interrupts, while maintaining the interference with a monitor. We show correctness of the approach and evaluate it in a hypervisor implementation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744820"}, {"primary_key": "4390458", "vector": [], "sparse_vector": [], "title": "Local search algorithms for timing-driven placement under arbitrary delay models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present local search algorithms for timing-driven placement optimization. They find local slack optima for cells under arbitrary delay models and can be applied late in the design flow.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744867"}, {"primary_key": "4390459", "vector": [], "sparse_vector": [], "title": "SuperNet: multimode interconnect architecture for manycore chips.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Designers of the on-chip interconnect for manycore chips are faced with the dilemma of meeting performance, power and reliability requirements for different operational scenarios. In this paper, we propose a multimode on-chip interconnect called SuperNet. This interconnect can be configured to run in three different modes: energy efficient mode; performance mode; and, reliability mode. Our proposed interconnect is based on two parallel multi-vt optimized packet switched network-on-chip (NoC) meshes. We describe the circuit design techniques and architectural modifications required to realize such a multimode interconnect. Our evaluation with diverse set of applications show that the energy efficient mode can save on average 40% NoC power, whereas the performance mode can improve the core IPC by up to 13% on selected high MPKI applications. The reliability mode provides protection against soft errors in the router's data path through byte oriented SECDED codes that can correct up to 8 bit errors and detect up to 16 bit errors in a 64 bit flit, whereas the router's control path is protected through DMR lock step execution.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744912"}, {"primary_key": "4390460", "vector": [], "sparse_vector": [], "title": "Walking a thin line: performance and quality grading vs. yield overcut.", "authors": ["<PERSON>"], "summary": "Product engineers are continually working to balance product cost, schedules and quality. A significant contributor to all three dimensions is enabling the appropriate test coverage to provide the required quality levels while minimizing yield overcut. This challenge is magnified by complexities created by advanced power management and the need for performance grading across various power states. This talk will describe these challenges and approaches that AMD is taking to address them.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747949"}, {"primary_key": "4390461", "vector": [], "sparse_vector": [], "title": "TyTAN: tiny trust anchor for tiny devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded systems are at the core of many security-sensitive and safety-critical applications, including automotive, industrial control systems, and critical infrastructures. Existing protection mechanisms against (software-based) malware are inflexible, too complex, expensive, or do not meet real-time requirements.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744922"}, {"primary_key": "4390462", "vector": [], "sparse_vector": [], "title": "Including variability of physical models into the design automation of cyber-physical systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A good cyber-physical-systems (CPS) design methodology must conduct trade-off analysis of both the physical characteristics of the CPS as well as its cyber sub-system in a holistic manner. This paper presents a design space exploration (DSE) approach for CPSs that emphasizes the variabilities of the physical subsystem and control aspects of the system. We propose the application of parameterizable physical models and automatic recalculation of control algorithm parameters for the explored systems. The resulting parameterizable models can be applied in a systematic simulation-based DSE framework that facilitates the identification of superior system configurations. We applied the proposed design flow to a real non-linear inverted pendulum system with a range of physical and cyber settings. The results show the feasibility and effectiveness of our approach in the design of physical and control parts of CPSs. Our work supplements existing work on cyber system modeling and plays an integral part in the design automation of such systems.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744857"}, {"primary_key": "4390463", "vector": [], "sparse_vector": [], "title": "Hybrid quick error detection (H-QED): accelerator validation and debug using high-level synthesis principles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Post-silicon validation and debug challenges of system-on-chips (SoCs) are getting increasingly difficult. As we reach the limits of Dennard scaling, efforts to improve system performance and energy efficiency have resulted in the integration of a wide variety of complex hardware accelerators in SoCs. Hence, it is essential to address post-silicon validation and debug of hardware accelerators. High-level synthesis (HLS) is a promising technique to rapidly create customized hardware accelerators. In this paper, we present the Hybrid Quick Error Detection (H-QED) approach that overcomes post-silicon validation and debug challenges for hardware accelerators by leveraging HLS techniques. H-QED improves error detection latencies (time elapsed from when a bug is activated to when it manifests as an observable failure) by 2 orders of magnitude and bug coverage 3-fold compared to traditional post-silicon validation techniques. H-QED also uncovered previously unknown bugs in the CHStone benchmark suite, which is widely used by the HLS community. H-QED incurs less than 2% chip-level area overhead with negligible performance impact, and we also introduce techniques to minimize any possible intrusiveness introduced by H-QED.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2753768"}, {"primary_key": "4390464", "vector": [], "sparse_vector": [], "title": "High-level synthesis of error detecting cores through low-cost modulo-3 shadow datapaths.", "authors": ["<PERSON>", "Pranay Vissa", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this study, we propose a low-cost approach to error detection for arithmetic orientated data paths by performing lightweight shadow computations in modulo-3 space for each main computation. By leveraging the binding and scheduling flexibility of high-level synthesis, we detect errors through diverse binding and minimize area cost through intelligent checkpoint scheduling and modulo-3 reducer sharing. We evaluated our technique with 12 high-level synthesis benchmarks using FPGA emulated netlist-level error injection. We observe coverages of 99.13% for stuck-at faults, 99.46% for soft errors, and 99.67% for timing errors with a 25.7% area cost and negligible performance impact. Leveraging error detection latencies on the order of 10 cycles (3 orders of magnitude faster than end result check) for soft errors, we also explore a rollback recovery method with an additional area cost of 28.0%, observing a 175x increase in reliability against soft errors.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744851"}, {"primary_key": "4390465", "vector": [], "sparse_vector": [], "title": "Accelerating real-time embedded scene labeling with convolutional networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today there is a clear trend towards deploying advanced computer vision (CV) systems in a growing number of application scenarios with strong real-time and power constraints. Brain-inspired algorithms capable of achieving record-breaking results combined with embedded vision systems are the best candidate for the future of CV and video systems due to their flexibility and high accuracy in the area of image understanding. In this paper, we present an optimized convolutional network implementation suitable for real-time scene labeling on embedded platforms. We show that our algorithm can achieve up to 96GOp/s, running on the Nvidia Tegra K1 embedded SoC. We present experimental results, compare them to the state-of-the-art, and demonstrate that for scene labeling our approach achieves a 1.5x improvement in throughput when compared to a modern desktop CPU at a power budget of only 11 W.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744788"}, {"primary_key": "4390466", "vector": [], "sparse_vector": [], "title": "A model-based and simulation-assisted FMEDA approach for safety-relevant E/E systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Bogdan<PERSON><PERSON>", "<PERSON>"], "summary": "Certifying an electrical/electronic system as functionally safe requires a range of analysis and assessment procedures, which must be performed during the different design and manufacturing phases. In the automotive context, the ISO 26262 standard prescribes a set of methods, including FMEDA (Failure Modes, Effects, and Diagnostic Analysis), to evaluate the safety integrity level of the product. FMEDA is a well-established technique in the industry, however, it still demands cumbersome and error-prone manual tasks.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747908"}, {"primary_key": "4390467", "vector": [], "sparse_vector": [], "title": "3DIC benefit estimation and implementation guidance from 2DIC implementation.", "authors": ["Wei-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kambiz <PERSON>"], "summary": "Quantification of three-dimensional integrated circuit (3DIC) benefits over corresponding 2DIC implementation for arbitrary designs remains a critical open problem, largely due to nonexistence of any \"golden\" 3DIC flow. Actual design and implementation parameters and constraints affect 2DIC and 3DIC final metrics (power, slack, etc.) in highly non-monotonic ways that are difficult for engineers to comprehend and predict. We propose a novel machine learning-based methodology to estimate 3DIC power benefit (i.e., percentage power reduction) based on corresponding golden 2DIC implementation parameters. The resulting 3D Power Estimation (3DPE) models achieve small prediction errors that are bounded by construction. We are the first to perform a novel stress test of our predictive models across a wide range of implementation and design-space parameters. Further, we explore model-guided implementation of designs in 3D to achieve minimum power: that is, our models recommend a most-promising set of implementation parameters and constraints, and also provide a priori estimates of 3D power benefits, based on a given design's post-synthesis and 2D implementation parameters. We achieve ≤10% error in power benefit prediction across various 3DIC designs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747954"}, {"primary_key": "4390468", "vector": [], "sparse_vector": [], "title": "Achieving SLC performance with MLC flash memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hsiang-Pang Li"], "summary": "Although the Multi-Level-Cell technique is widely adopted by flash-memory vendors to boost the chip density and to lower the cost, it results in serious performance and reliability problems. Different from the past work, a new cell programming method is proposed to not only significantly improve the chip performance but also reduce the potential bit error rate. In particular, a Single-Level-Cell-like programming style is proposed to better explore the threshold-voltage relationship to denote different Multi-Level-Cell bit information, which in turn drastically provides a larger window of threshold voltage similar to that found in Single-Level-Cell chips. It could result in less programming iterations and simultaneously a much less reliability problem in programming flash-memory cells. In the experiments, the new programming style could accelerate the programming speed up to 742% and even reduce the bit error rate up to 471% for Multi-Level-Cell pages.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744790"}, {"primary_key": "4390469", "vector": [], "sparse_vector": [], "title": "TA-FTA: transition-aware functional timing analysis with a four-valued encoding.", "authors": ["Jasper <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Timing analysis becomes profound for modern VLSI designs. Functional timing analysis (FTA) has emerged to eliminate false paths and provide better timing closure than traditional static timing analysis (STA). However, signal transitions effect, such as multiple input switching (MIS), which changes the pin-to-pin delay of a gate as well as the overall circuit delay, has not yet been considered in FTA. Therefore, a Transition-Aware FTA (TA-FTA) engine using a novel four-valued encoding for calculating true delay under the signal-transition effect is developed in this work. However, timing analysis becomes sophisticated once the signal-transition effect is concerned. Therefore, two techniques, cone separation and filtering (CSF) and quadratic dynamic search (QDS), are also proposed to speed up TA-FTA by more than two orders in time. Experimental results shows that after considering the MIS effect, in the benchmark circuits, the delay reported by our TA-FTA increases by 23% on average and by 38% for the worst case.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744914"}, {"primary_key": "4390470", "vector": [], "sparse_vector": [], "title": "EUV and e-beam manufacturability: challenges and solutions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As process nodes continue to shrink, the semiconductor industry faces severe manufacturing challenges. Two most expected technologies may push the limits of next-generation lithography: extreme ultraviolet lithography (EUVL) and electron beam lithography (EBL). EUVL works by emitting intense beams of ultraviolet light that are reflected from a reflective mask into a resist for nanofabrication, while EBL scans focused beams of electrons to directly draw high-resolution feature patterns on a resist without employing any mask. Each of the two technologies encounters unique design challenges and requires solutions for a breakthrough. In this paper, we focus on the design-for-manufacturability issues for EUVL and EBL. We investigate the most critical design challenges of the two technologies, flare and shadowing effects for EUVL, and heating, stitching, fogging, and proximity effects for EBL. Preliminary solutions for these effects are explored, which can contribute to the continuing scaling of the CMOS technology. Finally, we provide future research directions for these key effects.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747925"}, {"primary_key": "4390471", "vector": [], "sparse_vector": [], "title": "Routing-architecture-aware analytical placement for heterogeneous FPGAs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Placement is a crucial stage for FPGA implementation. Most FPGA placers optimize their placement results by minimizing half-perimeter wirelength (HPWL). Due to the segmented routing architecture in FPGAs, however, the HPWL function cannot model routed wirelength and delay well. The mismatch of the HPWL function might lead to inferior routing results. Further, heterogeneous circuit blocks in a modern FPGA make the placement problem more complex. Consequently, it is desirable to consider the segmented routing and heterogeneous circuit architecture for FPGA placement. This paper presents a routing-architecture-aware analytical placement algorithm for heterogeneous FPGAs. Our algorithm proposes a routing-architecture-aware cost function to make placement results adapt to the corresponding routing architecture, and a complex block density model to effectively handle the heterogeneity. Experimental results show that our placer can achieve 9% smaller critical path delay and 5% shorter routed wirelength with shorter runtime, compared to the state-of-the-art academic placer.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744903"}, {"primary_key": "4390472", "vector": [], "sparse_vector": [], "title": "DaTuM: dynamic tone mapping technique for OLED display power saving based on video classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The adoption of the latest OLED (organic light emitting diode) technology does not change the fact that screen is still one of the most energy-consuming modules in modern smartphones. In this work, we found that video streams from the same video category share many common power consumption features on OLED screens. Therefore, we are able to build a Hidden Markov Model (HMM) classifier to categorize videos based on OLED screen power characteristics. Using this HMM classifier, we propose a video classification based dynamic tone mapping (DTM) scheme, namely, DaTuM, to remap output color range and minimize the power-hungry color compositions on OLED screens for power saving. Experiment shows that DaTuM scheme averagely reduces OLED screen power by 17.8% with minimum display quality degradation. Compared to DTM scheme based on official category info provided by the video sources and one state-of-the-art scheme, DaTuM substantially enhances OLED screens' power efficiency and display quality controllability.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744814"}, {"primary_key": "4390473", "vector": [], "sparse_vector": [], "title": "Equivalence among stochastic logic circuits and its application.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Stochastic computing (SC) uses standard logic to process pseudo-random bit-streams denoting probabilities. It implements arithmetic operations by extremely simple and low-power hardware. Despite major new applications, SC's theory and design requirements are poorly understood. We observe that the Boolean functions used in SC take the form f(X) = f(Xv;Xc), where Xv and Xc are inputs with variable and constant probabilities, respectively. Different functions can be equivalent in the sense of implying the same stochastic behavior. We define stochastic equivalence classes (SECs), and investigate their properties and applications. Suitably interpreted, SECs describe all realizable arithmetic functions of interest. While conventional synthesis focuses on finding the best circuit to implement a known function, stochastic circuit optimization first requires finding the best function. We present an SEC-based approach to this problem, which demonstrates the computational richness of SC and leads to significant cost reductions compared to prior designs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744837"}, {"primary_key": "4390474", "vector": [], "sparse_vector": [], "title": "Opportunistic turbo execution in NTC: exploiting the paradigm shift in performance bottlenecks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we investigate an intriguing shifting trend in performance bottlenecks for Near-Threshold Computing (NTC) processors. Our study demonstrates that the traditional memory latency bottleneck is largely superseded by the bottlenecks of Long Latency Datapaths (LLDs) within a processor core. To exploit this paradigm shift, we propose Opportunistic Turbo Execution (OTE). OTE dynamically boosts the performance of LLDs, by several factors, improving both performance and energy efficiency in an NTC core. Using a comprehensive circuit-architectural analysis, we demonstrate a 42.2% improvement in energy efficiency over a recently proposed technique, across a range of benchmarks.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744881"}, {"primary_key": "4390475", "vector": [], "sparse_vector": [], "title": "A SPICE model of flexible transition metal dichalcogenide field-effect transistors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>i <PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the first SPICE model of the transition metal dichalcogenide (TMD) field-effect transistor (FET), which is a promising candidate for flexible electronics. The model supports different transistor design parameters such as width, length, oxide thickness, and various channel materials (MoS2, WSe2, etc.), as well as the applied strain, which enables the evaluation of transistor- and circuit-level behavior under process variation and different levels of bending. We performed SPICE simulations on digital logic gates to explore the design space of both MoS2- and WSe2-based transistors, and to evaluate the projected performance of these circuits under applied strain. Our simulations show that WSe2 circuits outperform MoS2 and Si-based CMOS in terms of energy-delay product (EDP) by 1 order of magnitude or more, depending on applications. Finally, we investigate TMDFET's behavior under process variation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744782"}, {"primary_key": "4390476", "vector": [], "sparse_vector": [], "title": "Optimizing data placement for reducing shift operations on domain wall memories.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "Qingfeng Zhuge", "Penglin Dai", "<PERSON><PERSON>"], "summary": "Domain Wall Memory (DWM) using nanowire with data access port, exhibits extraordinary high density, low power leakage, and low access latency. These properties enable DWM to become an attractive candidate for replacing traditional memories. However, data accesses on DWM may require multiple shift operations before the port points to requested data, resulting in varying access latencies. Data placement, therefore, has a significant impact on the performance of data accesses on DWM. This paper studies compiler-based optimization techniques for data placement on DWM. To the authors' best knowledge, this is the first work addressing data placement problem on DWM. We present an efficient heuristic, called Grouping-Based Data Placement (GBDP), for the data placement problem of a given data access sequence on DWM. The experimental results show that GBDP has a significant performance improvement; for example, GBDP reduces 82% shift operations on an 8-port DWM compared with non-optimized approach.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744883"}, {"primary_key": "4390477", "vector": [], "sparse_vector": [], "title": "Interconnect reliability modeling and analysis for multi-branch interconnect trees.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Electromigration (EM) in VLSI interconnects has become one of the major reliability issues for current and future VLSI technologies. However, existing EM modeling and analysis techniques are mainly developed for a single wire. For practical VLSI chips, the interconnects such as clock and power grid networks typically consist of multi-branch metal segments representing a continuously connected, highly conductive metal (Cu) lines within one layer of metallization, terminating at diffusion barriers. The EM effects in those branches are not independent and they have to be considered simultaneously. In this paper, we demonstrate, for the first time, a first principle based analytic solution of this problem. We investigate the analytic expressions describing the hydrostatic stress evolution in several typical interconnect trees: the straight-line 3-terminal wires, the T-shaped 4-terminal wires and the cross-shaped 5-terminal wires. The new approach solves the stress evolution in a multi-branch tree by de-coupling the individual segments through the proper boundary conditions accounting the interactions between different branches. By using Laplace transformation technique, analytical solutions are obtained for each type of the interconnect trees. The analytical solutions in terms of a set of auxiliary basis functions using the complementary error function agree well with the numerical analysis results. Our analysis further demonstrates that using the first two dominant basis functions can lead to 0.5% error, which is sufficient for practical EM analysis.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747953"}, {"primary_key": "4390478", "vector": [], "sparse_vector": [], "title": "Core vs. uncore: the heart of darkness.", "authors": ["<PERSON>sian<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Even though <PERSON>'s Law continues to provide increasing transistor counts, the rise of the utilization wall limits the number of transistors that can be powered on and results in a large region of dark silicon. Prior studies have proposed energy-efficient core designs to address the \"dark silico\" problem. Nevertheless, the research for addressing dark silicon challenges in uncore components, such as shared cache, on-chip interconnect, etc, that contribute significant on-chip power consumption is largely unexplored. In this paper, we first illustrate that the power consumption of uncore components cannot be ignored to meet the chip's power constraint. We then introduce techniques to design energy-efficient uncore components, including shared cache and on-chip interconnect. The design challenges and opportunities to exploit 3D techniques and non-volatile memory (NVM) in dark-silicon-aware architecture are also discussed.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747916"}, {"primary_key": "4390479", "vector": [], "sparse_vector": [], "title": "Scalable sequence-constrained retention register minimization in power gating design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Retention registers are utilized in power gating design to hold design state during power down and to allow safe and fast system reactivation. Since a retention register consumes more power and costs more area than a non-retention register, it is desirable to minimize the use of retention registers. However, relaxing retention requirement to a minimal subset of registers can be computationally challenging. In this paper, we adopt satisfiability solving for scalable selection of registers whose retention is unnecessary and exploit input sequence constraints to increase the number of non-retention registers. Empirical results on industrial benchmarks show that our proposed methods are efficient and effective in identifying non-retention registers.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744905"}, {"primary_key": "4390480", "vector": [], "sparse_vector": [], "title": "Understanding soft errors in uncore components.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The effects of soft errors in processor cores have been widely studied. However, little has been published about soft errors in uncore components, such as memory subsystem and I/O controllers, of a System-on-a-Chip (SoC). In this work, we study how soft errors in uncore components affect system-level behaviors. We have created a new mixed-mode simulation platform that combines simulators at two different levels of abstraction, and achieves 20,000x speedup over RTL-only simulation. Using this platform, we present the first study of the system-level impact of soft errors inside various uncore components of a large-scale, multi-core SoC using the industrial-grade, open-source OpenSPARC T2 SoC design. Our results show that soft errors in uncore components can significantly impact system-level reliability. We also demonstrate that uncore soft errors can create major challenges for traditional system-level checkpoint recovery techniques. To overcome such recovery challenges, we present a new replay recovery technique for uncore components belonging to the memory subsystem. For the L2 cache controller and the DRAM controller components of OpenSPARC T2, our new technique reduces the probability that an application run fails to produce correct results due to soft errors by more than 100x with 3.32% and 6.09% chip-level area and power impact, respectively.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744923"}, {"primary_key": "4390481", "vector": [], "sparse_vector": [], "title": "Domain wall memory based digital signal processors for area and energy-efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jongsun Park", "<PERSON><PERSON><PERSON>"], "summary": "In many Digital Signal Processing (DSP) applications such as Viterbi decoder and Fast Fourier Transform (FFT), Static Random Access Memory (SRAM) based embedded memory consumes significant portion of area and power. These DSP units are dominated by sequential memory access where SRAM-based memory is inefficient in terms of area and power. We propose spintronic Domain Wall Memory (DWM) based embedded memories for DSP building blocks e.g., survivor-path memories in Viterbi decoder and First-In-First-Out (FIFO) register files in FFT processor that exploit the unique serial access mechanism, non-volatility and small footprint of the memory for area and power saving. Simulations using 65nm technology show that the DWM based Viterbi decoder achieves 66.4 % area and 59.6 % power savings over the conventional SRAM-based implementation. For 8K point FFT processor, the DWM based design shows 60.6 % area and 60.3 % power savings.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744825"}, {"primary_key": "4390482", "vector": [], "sparse_vector": [], "title": "Verification of gate-level arithmetic circuits by function extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The paper presents an algebraic approach to functional verification of gate-level, integer arithmetic circuits. It is based on extracting a unique bit-level polynomial function computed by the circuit directly from its gate-level implementation. The method can be used to verify the arithmetic function computed by the circuit against its known specification, or to extract the arithmetic function implemented by the circuit. Experiments were performed on arithmetic circuits synthesized and mapped onto standard cells using ABC system. The results demonstrate scalability of the method to large arithmetic circuits, such as multipliers, multiply-accumulate, and other elements of arithmetic datapaths with up to 512-bit operands and over 2 Million gates. The procedure has linear runtime and memory complexity, measured by the number of logic gates.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744925"}, {"primary_key": "4390483", "vector": [], "sparse_vector": [], "title": "On-chip interconnection network for accelerator-rich architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern processors have included hardware accelerators to provide high computation capability and low energy consumption. With specific hardware implementation, accelerators can improve performance and reduce energy consumption by orders of magnitude compared to general purpose cores. However, hardware accelerators cannot tolerate memory and communication latency through extensive multi-threading; this increases the demand for efficient memory interface and network-on-chip (NoC) designs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744879"}, {"primary_key": "4390484", "vector": [], "sparse_vector": [], "title": "An Analysis of Accelerator Coupling in Heterogeneous Architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing research on accelerators has emphasized the performance and energy efficiency improvements they can provide, devoting little attention to practical issues such as accelerator invocation and interaction with other on-chip components (e.g. cores, caches). In this paper we present a quantitative study that considers these aspects by implementing seven high-throughput accelerators following three design models: tight coupling behind a CPU, loose out-of-core coupling with Direct Memory Access (DMA) to the LLC, and loose out-of-core coupling with DMA to DRAM. A salient conclusion of our study is that working sets of non-trivial size are best served by loosely-coupled accelerators that integrate private memory blocks tailored to their needs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": ""}, {"primary_key": "4390485", "vector": [], "sparse_vector": [], "title": "Optimal control of PEVs for energy cost minimization and frequency regulation in the smart grid accounting for battery state-of-health degradation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Plug-in electric vehicles (PEVs) are considered the key to reducing the fossil fuel consumption and an important part of the smart grid. The plug-in electric vehicle-to-grid (V2G) technology in the smart grid infrastructure enables energy flow from PEV batteries to the power grid so that the grid stability is enhanced and the peak power demand is shaped. PEV owners will also benefit from V2G technology as they will be able to reduce energy cost through proper PEV charging and discharging scheduling. Moreover, power regulation service (RS) reserves have been playing an increasingly important role in modern power markets. It has been shown that by providing RS reserves, the power grid achieves a better match between energy supply and demand in presence of volatile and intermittent renewable energy generation. This paper addresses the problem of PEV charging under dynamic energy pricing, properly taking into account the degradation of battery state-of-health (SoH) during V2G operations as well as RS provisioning. An overall optimization throughout the whole parking period is proposed for the PEV and an adaptive control framework is presented to dynamically update the optimal charging/discharging decision at each time slot to mitigate the effect of RS tracking error. Experimental results show that the proposed optimal PEV charging algorithm minimizes the combination of electricity cost and battery aging cost in the RS provisioning power market.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744882"}, {"primary_key": "4390486", "vector": [], "sparse_vector": [], "title": "Sequential equivalence checking of clock-gated circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sequential clock-gating can lead to easier equivalence checking problems, compared to the general sequential equivalence checking (SEC) problem. Modern sequential clock-gating techniques introduce control structures to disable unnecessary clocking. This violates combinational equivalence but maintains sequential equivalence between the original and revised circuits. We propose the use of characteristic graphs (CGs) to extract essential LTL clock-gating properties, which when proved imply certain sequential redundancies. The extraction, proof and subsequent removal of the implied redundancies lead to an efficient SEC procedure for clock-gated circuits. Experiments show that the proposed SEC procedure substantially outperforms existing methods in terms of speed and scalability when applied to several difficult cases.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744910"}, {"primary_key": "4390487", "vector": [], "sparse_vector": [], "title": "HAFIX: hardware-assisted flow integrity extension.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Orlando Arias", "<PERSON><PERSON>"], "summary": "Code-reuse attacks like return-oriented programming (ROP) pose a severe threat to modern software on diverse processor architectures. Designing practical and secure defenses against code-reuse attacks is highly challenging and currently subject to intense research. However, no secure and practical system-level solutions exist so far, since a large number of proposed defenses have been successfully bypassed. To tackle this attack, we present HAFIX (Hardware-Assisted Flow Integrity eXtension), a defense against code-reuse attacks exploiting backward edges (returns). HAFIX provides fine-grained and practical protection, and serves as an enabling technology for future control-flow integrity instantiations. This paper presents the implementation and evaluation of HAFIX for the Intel® Siskiyou Peak and SPARC embedded system architectures, and demonstrates its security and efficiency in code-reuse protection while incurring only 2% performance overhead.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744847"}, {"primary_key": "4390488", "vector": [], "sparse_vector": [], "title": "Detailed routing for spacer-is-metal type self-aligned double/quadruple patterning lithography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "As the technology nodes scale down to 22nm and beyond, Double Patterning Lithography (DPL) has been considered as a practical solution for manufacturing process. Compared with Litho-Etch-Litho-Etch (LELE), Self-Aligned Double Patterning (SADP) has better overlay tolerance. Two types of SADP process are popularly used for the state-of-the-art lithography patterning: Spacer-Is-Dielectric (SID) and Spacer-Is-Metal (SIM). Meanwhile, Self-Aligned Quadruple Patterning (SAQP), as a natural extension of SADP, is expected to be one of the major solutions for future process requirement after the 16nm/14nm technology node. In order to have better decomposability of layout patterns, we consider SIM type SADP/SAQP during detailed routing stage. The idea of color pre-assignment is adopted and a graph model is proposed which greatly simplifies the problem and reduces design rule violation. Then, the negotiated congestion based scheme is applied for detailed routing based on our proposed graph model. Compared with other state-of-art works, our approach does not produce any side overlay error and no design rule violation is reported. Meanwhile, a better solution in terms of total wirelength, via count, routability, and runtime is achieved.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744821"}, {"primary_key": "4390489", "vector": [], "sparse_vector": [], "title": "An efficient shift invariant rasterization algorithm for all-angle mask patterns in ILT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Lithography simulation is an essential technique to guide the design of inverse lithography technology (ILT) masks. To reduce the complexity in modern lithography simulation, a widely used approach is to first rasterize the ILT masks before they are inputted to the simulation tools. Currently, there is no high performance technology to handle the rasterization of all-angle polygons, which are very common in modern ILT masks. Traditional rasterization technology is very expensive in term of runtime and memory usage. In this paper, we propose an efficient rasterization algorithm for all-angle polygons based on a pre-computed lookup table (LUT). We expect that the convolution for a majority of large pixels can be performed in a single lookup table query, which decreases the overall runtime dramatically. The experimental results demonstrate that our proposed algorithm can speed up rasterization process by almost 500x while maintaining small variations in CD. Meanwhile, the time for pre-computing the lookup table and the size of it can be kept within very reasonable limits.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744797"}, {"primary_key": "4390490", "vector": [], "sparse_vector": [], "title": "A practical circuit fingerprinting method utilizing observability don&apos;t care conditions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Circuit fingerprinting is a method that adds unique features into each copy of a circuit such that they can be identified for the purpose of tracing intellectual property (IP) piracy. It is challenging to develop effective fingerprinting techniques because each copy of the IP must be made different, which increases the design and manufacturing cost. In this paper, we explore the Observability Don't Care (ODC) conditions to create multiple fingerprinting copies of a design IP (e.g. in the form of gate level layout) with minute changes. More specifically, we find locations in the given circuit layout where we can replace a gate with another gate and some wires without changing the functionality of the circuit. However, as expected, this could introduce design overhead. Our experimental results show that, although we can embed fingerprints of up to 1438 bits, there is an average of 10.9% area increase, 50.5% delay increase, and 9.4% power increase on circuits in the MCNC and ISCAS 85 benchmark suites. We further propose a fingerprinting heuristics under delay constraints to help us reduce area and power overhead.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744780"}, {"primary_key": "4390491", "vector": [], "sparse_vector": [], "title": "Energy efficient MapReduce with VFI-enabled multicore platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In an era when power constraints and data movement are proving to be significant barriers for high-end computing, multicore architectures offer a low-power and highly scalable platform suitable for both data- and compute-intensive applications. MapReduce is a popular framework to facilitate the management and development of big-data workloads. In this work, we demonstrate that by using a wireless NoC-enabled Voltage Frequency Island (VFI)-based multicore platform it is possible to enhance the energy efficiency of MapReduce implementations without paying significant execution time penalties. Our experimental results show that for the benchmarks considered, the designed VFI system can achieve an average of 33.7% energy-delay product (EDP) savings over the standard baseline non-VFI mesh-based system while paying a maximum of 3.22% execution time penalty.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744835"}, {"primary_key": "4390492", "vector": [], "sparse_vector": [], "title": "Virtual to the (near) end: using virtual platforms for continuous integration.", "authors": ["<PERSON>"], "summary": "Continuous integration (CI) is a hot topic in software development today. CI is a critical enabler for Agile methods and higher software development velocity and productivity. However, adopting the practice of Continuous Integration can be difficult, especially when developing software for embedded systems. Practices such as Agile and Continuous Integration are designed to enable engineers to constantly improve and update their products. However, these processes can break down without access to the target system, a way to collaborate with other teams and team members, and the ability to automate tests. This paper outlines how simulation can enable teams to more effectively manage their integration and test practice, using virtual platforms as a key part of the test setup and simulation as a key part of the test strategy.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747948"}, {"primary_key": "4390493", "vector": [], "sparse_vector": [], "title": "Analysis and RTL correlation of instruction set simulators for automotive microcontroller robustness verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasingly complex microcontroller designs for safety-relevant automotive systems require the adoption of new methods and tools to enable a cost-effective verification of their robustness. In particular, costs associated to the certification against the ISO26262 safety standard must be kept low for economical reasons. In this context, simulation-based verification using instruction set simulators (ISS) arises as a promising approach to partially cope with the increasing cost of the verification process as it allows taking design decisions in early design stages when modifications can be performed quickly and with low cost. However, it remains to be proven that verification in those stages provides accurate enough information to be used in the context of automotive microcontrollers. In this paper we analyze the existing correlation between fault injection experiments in an RTL microcontroller description and the information available at the ISS to enable accurate ISS-based fault injection.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744798"}, {"primary_key": "4390494", "vector": [], "sparse_vector": [], "title": "Construction of reconfigurable clock trees for MCMM designs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cheng<PERSON><PERSON><PERSON>"], "summary": "The clock networks of modern circuits must be able to operate in multiple corners and multiple modes (MCMM). Earlier studies on clock network synthesis for MCMM designs focus on the legalization of an initial clock network that has timing violations in different corners or modes. We propose a mode reconfigurable clock tree (MRCT) that is based on a correct-by-construction approach. An MRCT consists of multiple clock trees. Depending on the active mode, the MRCT is reconfigured such that one of the clock trees is activated to deliver the clock signal. To limit the overhead, the bottom part of the network (closer to the clock sinks) is shared among all of the clock trees, and only the top part of the network (closer to the clock source) is mode reconfigurable. The reconfiguration is realized using or-gates and a single one-input-multiple-output demultiplexer. The MRCT is constructed in a bottom-up fashion by iteratively merging subtrees to form larger subtrees. When two subtrees cannot be merged because of mode-incompatible constraints, an or-gate is inserted to separate the incompatible modes. Corner-incompatible constraints are resolved by reducing safety margins of appropriate skew constraints. The experimental results show that for a set of synthesized MCMM circuits with 715 to 13; 216 sequential elements, the proposed approach can achieve high yield.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744811"}, {"primary_key": "4390495", "vector": [], "sparse_vector": [], "title": "Increasing confidence on measurement-based contention bounds for real-time round-robin buses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Contention among tasks concurrently running in a multicore has been deeply studied in the literature specially for on-chip buses. Most of the works so far focus on deriving exact upper-bounds to the longest delay it takes a bus request to be serviced (ubd), when its access is arbitrated using a time-predictable policy such as round-robin. Deriving ubd for a bus can be done accurately when enough timing information is available, which is not often the case for commercial-of-the-shelf (COTS) processors. Hence, ubd is approximated (ubdm) by directly experimenting on the target processor, i.e by measurements. However, using ubdm makes the timing analysis technique to resort on the accuracy of ubdm to derive trustworthy worst-case execution time estimates. Therefore, accurately estimating ubd by means of ubdm is of paramount importance. In this paper, we propose a systematic measurement-based methodology to accurately approximate ubd without knowing the bus latency or any other latency information, being only required that the underlying bus policy is round-robin. Our experimental results prove the robustness of the proposed methodology by testing it on different bus and processor setups.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744858"}, {"primary_key": "4390496", "vector": [], "sparse_vector": [], "title": "Resource usage templates and signatures for COTS multicore processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Upper bounding the execution time of tasks running on multicore processors is a hard challenge. This is especially so with commercial-off-the-shelf (COTS) hardware that conceals its internal operation. The main difficulty stems from the contention effects on access to hardware shared resources (e.g., buses) which cause task's timing behavior to depend on the load that co-runner tasks place on them. This dependence reduces time composability and constrains incremental verification. In this paper we introduce the concepts of resource-usage signatures and templates, to abstract the potential contention caused and incurred by tasks running on a multicore. We propose an approach that employs resource-usage signatures and templates to enable the analysis of individual tasks largely in isolation, with low integration costs, producing execution time estimates per task that are easily composable throughout the whole system integration process. We evaluate the proposal on a 4-core NGMP-like multicore architecture.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744901"}, {"primary_key": "4390497", "vector": [], "sparse_vector": [], "title": "Trends in functional verification: a 2014 industry study.", "authors": ["<PERSON>"], "summary": "Technical publications often make either subjective or unsubstantiated claims about today's functional verification process---such as, 70 percent of a project's overall effort is spent in verification. Yet, there are very few credible industry studies that quantitatively provide insight into the functional verification process in terms of verification technology adoption, effort, and effectiveness. To address this dearth of knowledge, a recent world-wide, double-blind, functional verification study was conducted, covering all electronic industry market segments. To our knowledge, this is the largest independent functional verification study ever conducted. This paper presents the findings from our 2014 study and provides invaluable insight into the state of the electronic industry today in terms of both design and verification trends.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744921"}, {"primary_key": "4390498", "vector": [], "sparse_vector": [], "title": "Mitigating the impact of faults in unreliable memories for error-resilient applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Andreas <PERSON>"], "summary": "Inherently error-resilient applications in areas such as signal processing, machine learning and data analytics provide opportunities for relaxing reliability requirements, and thereby reducing the overhead incurred by conventional error correction schemes. In this paper, we exploit the tolerable imprecision of such applications by designing an energy-efficient fault-mitigation scheme for unreliable data memories to meet target yield. The proposed approach uses a bit-shuffling mechanism to isolate faults into bit locations with lower significance. This skews the bit-error distribution towards the low order bits, substantially limiting the output error magnitude. By controlling the granularity of the shuffling, the proposed technique enables trading-off quality for power, area, and timing overhead. Compared to error-correction codes, this can reduce the overhead by as much as 83% in read power, 77% in read access time, and 89% in area, when applied to various data mining applications in 28nm process technology.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744871"}, {"primary_key": "4390499", "vector": [], "sparse_vector": [], "title": "Integrated power management in IoT devices under wide dynamic ranges of operation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "By the year 2020 it is expected that corresponding to every human being there would be seven connected devices. These connected devices will usher in the Internet of Things (IoTs) and would percolate every aspect of human life, changing the human experience at a fundamental level. In order to power these devices novel strategies would have to be developed as these devices will not only have a dynamic load, due to multiple features, but also dynamic sources if opportunistic energy harvesting is used to supplement the rechargeable battery. For the power delivery network, figures of merit would be to comprehend both the ability to supply the worst case design as well as to maintain high efficiency across a wide dynamic range. To maintain high efficiency for a large range we will need adaptive components on the load side as well as at the energy source. In this work we will discuss the general IoT power delivery network (PDN), current research and the state of the art PDN components, novel designs and control for interface circuits and energy harvesters.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747931"}, {"primary_key": "4390500", "vector": [], "sparse_vector": [], "title": "Hayat: harnessing dark silicon and variability for aging deceleration and balancing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Duo Sun", "<PERSON><PERSON><PERSON>"], "summary": "Elevated power densities result in the so-called Dark Silicon constraint that prohibits simultaneous activation of all the cores in an on-chip system (in the full performance mode) to respect the safe thermal limits, thus enforcing a significant amount of on-chip resources to stay 'dark' (i.e., power-gated). In this paper, we show that how Dark Silicon together with the manufacturing process induced variability can be harnessed to mitigate reliability threats in the nano-era. In particular, we propose a run-time system Hayat* that harnesses Dark Silicon to decelerate and/or balance temperature-dependent aging, while also considering variability in order to improve the overall system performance for a given lifetime. Experimental evaluation across a range of chips to account for process variations illustrates that our Hayat system can provide a significant aging/performance improvement and decelerates the chip aging by 6 months -- 5 years (depending upon the required lifetime constraint) compared to state-of-the-art techniques.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744849"}, {"primary_key": "4390501", "vector": [], "sparse_vector": [], "title": "Robust design of E/E architecture component platforms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Already today, car manufacturers are designing E/E architectures using so-called component platforms. Such a platform comprises the superset of all components that are required to build all acquirable variants of a certain or even multiple car models. To find and optimize such component platforms, each candidate platform has to be evaluated by (a) determining a number of design objectives (monetary cost, etc.) of each car variant when derived from the candidate platform and then (b) approximating the platform's design objectives themselves, e. g., by a weighted sum that includes the expected sales of each variant. But typically, since this optimization has to take place in early design stages, important parameters like the number of expected sales numbers per car variant can only be projected and are, thus, uncertain. To investigate the susceptibility of the optimization to such uncertain parameters, this paper proposes a Monte-Carlo simulation-based method that enables to evaluate the uncertainty of a combined multi-variant objective wrt. parameter variations. By treating the minimization of uncertainty as an additional design objective, not only can the robustness of the derived component platforms be improved but also the confidence of the manufacturer. Moreover, we also propose to treat uncertainty not as a conventional design objective, but to use uncertain objectives: Here, not a single (e. g., mean) value but an interval given by observed upper and lower objective values is used. Experimental results show that the design objectives of an E/E architecture component platform are relatively robust wrt. parameter variations (here expected sales numbers of car variants). Moreover, it will be shown that the difference in expected overall costs between different non-dominated solutions is often much higher than the expected variation in cost as a result of parameter uncertainty", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747941"}, {"primary_key": "4390502", "vector": [], "sparse_vector": [], "title": "Area and performance co-optimization for domain wall memory in application-specific embedded systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "Qingfeng Zhuge", "<PERSON><PERSON>", "Jingtong Hu"], "summary": "Domain Wall Memory (DWM), a recently developed spin-based non-volatile memory technology, inherently offers unprecedented benefits in density by storing multiple bits in the domains of a ferromagnetic nanowire, which logically resembles a bit-serial tape. However, this structure also leads to a unique challenge that the bits must be sequentially accessed by performing \\shift\" operations, resulting in variable and potential higher access latencies. In this paper, we propose a hardware and software co-optimize approach to improve area efficiency and performance for DWM in application-specific embedded systems. For an application-specific embedded system, this technique can obtain a DWM which consists of both micro-cell DWM and macro-cell DWM with minimal area size. Meanwhile, instruction schedule and data allocation with minimal memory access overhead are generated. Experimental results show that the proposed method can minimize the DWM area size while satisfying a system performance constraint.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744800"}, {"primary_key": "4390503", "vector": [], "sparse_vector": [], "title": "Pre-silicon security verification and validation: a formal perspective.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reusable hardware Intellectual Property (IP) based System-on-Chip (SoC) design has emerged as a pervasive design practice in the industry today. The possibility of hardware Trojans and/or design backdoors hiding in the IP cores has raised security concerns. As existing functional testing methods fall short in detecting unspecified (often malicious) logic, formal methods have emerged as an alternative for validation of trustworthiness of IP cores. Toward this direction, we discuss two main categories of formal methods used in hardware trust evaluation: theorem proving and equivalence checking. Specifically, proof-carrying hardware (PCH) and its applications are introduced in detail, in which we demonstrate the use of theorem proving methods for providing high-level protection of IP cores. We also outline the use of symbolic algebra in equivalence checking, to ensure that the hardware implementation is equivalent to its design specification, thus leaving little space for malicious logic insertion.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747939"}, {"primary_key": "4390504", "vector": [], "sparse_vector": [], "title": "Investigation of obfuscation-based anti-reverse engineering for printed circuit boards.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Domenic Forte", "<PERSON><PERSON>"], "summary": "Prior work has shown that printed circuit board (PCB) reverse engineering can be accomplished with inexpensive home solutions as well as state-of-the-art technologies. Once the information of how components on a PCB are connected is determined, an adversary can steal the IP, clone the design, determine points of attack on a system, etc. Existing chip-level obfuscation techniques are not applicable to board level due to the significant differences between chips and PCBs. In this paper, we propose a PCB obfuscation approach that relies on permutation blocks to hide the interconnects among the PCB's circuit components. A detailed framework is provided to implement the proposed approach and evaluate its performance. Potential attacks and countermeasures are also discussed. Results obtained from five industrial reference designs show that it is nearly impossible to break the proposed approach by brute force, even under pessimistic assumptions. Our investigation also reveals that PCBs containing a programmable component with 64 pins (or more) are well-protected by our approach, making it suitable for a large percentage of systems and applications.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744862"}, {"primary_key": "4390505", "vector": [], "sparse_vector": [], "title": "FlexLevel: a novel NAND flash storage system design for LDPC latency reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "LDPC code is introduced in NAND flash memory to handle high BER (bit error rate) incurred by technology scaling. Despite strong error correction capability, LDPC decoding induces long NAND flash read latency. In this work, we propose FlexLevel -- a robust NAND flash storage system design to improve data reliability and read efficiency affected by the LDPC operations. FlexLevel first reduces BER by enlarging noise margins via Vth (threshold voltage) level reduction. It reduces the sensing levels of LDPC but also causes loss of storage capacity. To compensate this capacity loss with minimum impact on read performance, FlexLevel identifies the data with high LDPC overhead and only applies the Vth level reduction technique to those data. Experimental results show that compared with state-of-the-art, FlexLevel can achieve up to 33% read speedup with very moderate capacity loss.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744843"}, {"primary_key": "4390506", "vector": [], "sparse_vector": [], "title": "To collect or not to collect: just-in-time garbage collection for high-performance SSDs with long lifetimes.", "authors": ["Sangwook <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For NAND flash-based storage systems, managing garbage collection (GC) efficiently is a critical requirement to achieve both high performance and long lifetimes. In this paper, we propose a just-in-time GC technique, called JIT-GC, which invokes background GC operations only when necessary depending on future write demands. JIT-GC was motivated by our measurement study, which strongly suggested that deciding when to invoke background GC operations is a key parameter for efficient GC. By accurately estimating the amount of future SSD writes, JIT-GC can choose the best time to invoke a background GC operation. JIT-GC reserves necessary free space in advance so that high write performance can be achieved while it extends the SSD lifetime by preventing premature block erasures. Our evaluations on real SSDs show that JIT-GC can achieve both high performance and long lifetimes, thus overcoming the shortcomings of existing background GC invocation heuristics.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744918"}, {"primary_key": "4390507", "vector": [], "sparse_vector": [], "title": "Transient-simulation guided graph sparsification approach to scalable harmonic balance (HB) analysis of post-layout RF circuits leveraging heterogeneous CPU-GPU computing systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Harmonic Balance (HB) analysis is key to efficient verification of large post-layout RF and microwave integrated circuits (ICs). This paper introduces a novel transient-simulation guided graph sparsification technique, as well as an efficient runtime performance modeling approach tailored for heterogeneous manycore CPU-GPU computing system to build nearly-optimal subgraph preconditioners that can lead to minimum HB simulation runtime. Additionally, we propose a novel heterogeneous parallel sparse block matrix algorithm by taking advantages of the structure of HB Jacobian matrices as well as GPU's streaming multiprocessors to achieve optimal work load balancing during the preconditioning phase of HB analysis. We also show how the proposed preconditioned iterative algorithm can efficiently adapt to heterogeneous computing systems with different CPU and GPU computing capabilities. Extensive experimental results show that our HB solver can achieve up to 20X speedups and 5X memory reduction when compared with the state-of-the-art direct solver highly optimized for eight-core CPUs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744920"}, {"primary_key": "4390508", "vector": [], "sparse_vector": [], "title": "Evaluation of BEOL design rule impacts using an optimal ILP-based detailed router.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Continued technology scaling with more pervasive use of multi-patterning has led to complex design rules and increased difficulty of maintaining high layout densities. Intuitively, emerging constraints such as unidirectional patterning or increased via spacing will decrease achievable density of the final place-and-route solution, worsening die area and product cost. However, no methodology exists for accurate assessment of design rules' impact on physical chip implementation. At the same time, this is a crucial need for early development of BEOL process technologies, particularly with FinFET or future vertical-device architectures where cell footprints can become much smaller than in bulk planar CMOS technologies. In this work, we study impacts of patterning technology choices and associated design rules on physical implementation density, with respect to cost-optimal design rule-correct detailed routing. A key contribution is an Integer Linear Programming (ILP) based optimal router (OptRouter) which considers complex design rules that arise in sub-20nm process technologies. Using OptRouter, we assess wirelength and via count impacts of various design rules (implicitly, patterning technology choices) by analyzing optimal routing solutions of clips (i.e., switchbox instances) extracted from post-detailed route layouts in an advanced technology.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744839"}, {"primary_key": "4390509", "vector": [], "sparse_vector": [], "title": "A global-local optimization framework for simultaneous multi-mode multi-corner clock skew variation reduction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As combinations of signoff corners grow in modern SoCs, minimization of clock skew variation across corners is important. Large skew variation can cause difficulties in multi-corner timing closure because fixing violations at one corner can lead to violations at other corners. Such \"ping-pong\" effects lead to significant power and area overheads and time to signoff. We propose a novel framework encompassing both global and local clock network optimizations to minimize the sum of skew variations across different PVT corners between all sequentially adjacent sink pairs. The global optimization uses linear programming to guide buffer insertion, buffer removal and routing detours. The local optimization is based on machine learning-based predictors of latency change; these are used for iterative optimization with tree surgery, buffer sizing and buffer displacement operators. Our optimization achieves up to 22% total skew variation reduction across multiple testcases implemented in foundry 28nm technology, as compared to a best-practices CTS solution using a leading commercial tool.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744776"}, {"primary_key": "4390510", "vector": [], "sparse_vector": [], "title": "Introduction to stochastic computing and its challenges.", "authors": ["<PERSON>"], "summary": "We give a short overview of stochastic computing (SC) and its uses. SC computes with randomized bit-streams that loosely resemble the neural spike trains of the brain. Its key feature is the use of low-cost and low-power logic elements to implement complex numerical operations in a highly error-tolerant fashion. These advantages must be weighed against SC's inherently slow computing speed and low precision. Although studied sporadically since its invention in the 1960s, SC has regained interest recently as potentially suited to some emerging nanotechnologies, and to applications such as ECC decoding and biomedical image processing. However, a number of major challenges must be overcome if this potential is to be fully realized.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747932"}, {"primary_key": "4390511", "vector": [], "sparse_vector": [], "title": "Parallel circuit simulation using the direct method on a heterogeneous cloud.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "This paper discusses the development of a parallel SPICE circuit simulator using the direct method on a cloud-based heterogeneous cluster, which includes multiple HPC compute nodes with multi-sockets, multicores, and GPUs. A simple model is derived to optimally partition the circuit between the compute nodes. The parallel simulator is divided into four major kernels: Partition Device Model Evaluation (PME), Partition Matrix Factorization (PMF), Interconnection Matrix Evaluation (IME), and Interconnection Matrix Factorization (IMF). Another model is derived to assign each of the kernels to the most suitable execution platform of the Amazon EC2 heterogeneous cloud. The partitioning approach using heterogeneous resources has achieved an order-of-magnitude speedup over optimized multithreaded implementations of SPICE using state of the art KLU and NICSLU packages for matrix solution.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744888"}, {"primary_key": "4390512", "vector": [], "sparse_vector": [], "title": "New trends in dark silicon.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dr", "Santiago Pagani", "<PERSON>"], "summary": "This paper presents new trends in dark silicon reflecting, among others, the deployment of FinFETs in recent technology nodes and the impact of voltage/frquency scaling, which lead to new less-conservative predictions. The focus is on dark silicon from a thermal perspective: we show that it is not simply the chip's total power budget, e.g., the Thermal Design Power (TDP), that leads to the dark silicon problem, but instead it is the power density and related thermal effects. We therefore propose to use Thermal Safe Power (TSP) as a more efficient power budget. It is also shown that sophisticated spatio-temporal mapping decisions result in improved thermal profiles with reduced peak temperatures. Moreover, we discuss the implications of Near-Threshold Computing (NTC) and employment of Boosting techniques in dark silicon systems.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747938"}, {"primary_key": "4390513", "vector": [], "sparse_vector": [], "title": "Verifying SystemC using stateful symbolic simulation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Formal verification of high-level SystemC designs is an important and challenging problem. Recent works have proposed symbolic simulation in combination with Partial Order Reduction (POR) as a promising solution and experimentally demonstrated its potential. However, these symbolic simulation approaches have a fundamental limitation in handling cyclic state spaces. The reason is that they are based on stateless model checking and thus unable to avoid revisiting states in a cycle. In this paper, we propose a novel stateful symbolic simulation approach for SystemC. For the efficient detection of revisited symbolic states, we apply symbolic subsumption checking. Furthermore, our implementation integrates a cycle proviso to preserve the soundness of POR in the presence of cycles. We demonstrate the scalability and the efficiency of the proposed approach using an extensive set of experiments.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744927"}, {"primary_key": "4390514", "vector": [], "sparse_vector": [], "title": "Avoiding transitional effects in dynamic circuit specialisation on FPGAs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic Circuit Specialisation (DCS) is a technique that uses the reconfigurability of an FPGA to optimise a circuit during run-time, thus achieving higher performance and lower resource cost. However, run-time reconfiguration causes transitional effects that form an important problem for DCS. Because of these, the DCS circuit cannot be used while it is being reconfigured. This limits the usability of DCS for streaming applications and other applications that cannot tolerate downtime. For other applications, this results in a loss of performance.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744802"}, {"primary_key": "4390515", "vector": [], "sparse_vector": [], "title": "PASS: priority assignment of real-time tasks with dynamic suspending behavior under fixed-priority scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Self-suspension is becoming an increasingly prominent characteristic in real-time systems such as: (i) I/O-intensive systems, where applications interact intensively with I/O devices, (ii) multi-core processors, where tasks running on different cores have to synchronize and communicate with each other, and (iii) computation offloading systems with coprocessors, like Graphics Processing Units (GPUs). In this paper, we show that rate-monotonic (RM), deadline-monotonic (DM) and laxity-monotonic (LM) scheduling will perform rather poor in dynamic self-suspending systems in terms of speed-up factors. On the other hand, the proposed PASS approach is guaranteed to find a feasible priority assignment on a speed-2 uniprocessor, if one exists on a unit-speed processor. We evaluate the feasibility of the proposed approach via a case study implementation. Furthermore, the effectiveness of the proposed approach is also shown via extensive simulation results.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744891"}, {"primary_key": "4390516", "vector": [], "sparse_vector": [], "title": "Efficient multivariate moment estimation via Bayesian model fusion for analog and mixed-signal circuits.", "authors": ["<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A critical-yet-challenging problem of analog/mixed-signal circuit validation in either pre-silicon or post-silicon stage is to estimate the parametric yield of the performances. In this paper, we propose a novel Bayesian model fusion method for efficient multivariate moment estimation of multiple correlated performance metrics by borrowing the prior knowledge from the early stage. The key idea is to model the multiple performance metrics as a jointly Gaussian distribution and encode the prior knowledge as a normal-Wishart distribution according to the theory of conjugate prior. The late-stage multivariate moments can be accurately estimated by Bayesian inference with very few late-stage samples. Several circuit examples demonstrate that the proposed method can achieve up to 16× cost reduction over the traditional method without surrendering any accuracy.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744832"}, {"primary_key": "4390517", "vector": [], "sparse_vector": [], "title": "Improving worst-case cache performance through selective bypassing and register-indexed cache.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Worst-case execution time (WCET) analysis is a critical part of designing real-time systems that require strict timing guarantees. Data caches have traditionally been challenging to analyze in the context of WCET due to the unpredictability of memory access patterns. In this paper, we present a novel register-indexed cache structure that is designed to be amenable to static analysis. This is based on the idea that absolute addresses may not be known, but by using relative addresses, analysis may be able to guarantee a number of hits in the cache. In addition, we observe that keeping unpredictable memory accesses in caches can increase or decrease WCET depending on the application. Thus, we explore selectively bypassing caches in order to provide lower WCET. Our experimental results show reductions in WCET of up to 35% over the state-of-the-art static analysis.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744855"}, {"primary_key": "4390518", "vector": [], "sparse_vector": [], "title": "Bandwidth-efficient on-chip interconnect designs for GPGPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern computational workloads require abundant thread level parallelism (TLP), necessitating highly-parallel, many-core accelerators such as General Purpose Graphics Processing Units (GPGPUs). GPGPUs place a heavy demand on the on-chip interconnect between the many cores and a few memory controllers (MCs). Thus, traffic is highly asymmetric, impacting on-chip resource utilization and system performance. Here, we analyze the communication demands of typical GPGPU applications, and propose efficient Network-on-Chip (NoC) designs to meet those demands. We show that the proposed schemes improve performance by up to 64.7%. Compared to the best of class prior work, our VC monopolizing and partitioning schemes improve performance by 25%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744803"}, {"primary_key": "4390519", "vector": [], "sparse_vector": [], "title": "Self-correcting STTRAM under magnetic field attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jongsun Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spin-Transfer Torque Random Access Memory (STTRAM) is a possible candidate for universal memory due to its high-speed, low-power, non-volatility, and low cost. Although attractive, STTRAM is susceptible to contactless tampering through malicious exposure to magnetic field with the intention to steal or modify the bitcell content. In this paper, for the first time to our knowledge, we analyze the impact of magnetic attacks on STTRAM using micro-magnetic simulations. Next, we propose a novel array-based sensor to detect the polarity and magnitude of such attacks and then propose two design techniques to mitigate the attack, namely, array sleep with encoding and variable strength Error Correction Code (ECC). Simulation results indicate that the proposed sensor can reliably detect an attack and provide sufficient compensation window (few ns to ~100us) to enable proactive protection measures. Finally, we shows that variable-strength ECC can adapt correction capability to tolerate failures with various strength of an attack.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744909"}, {"primary_key": "4390520", "vector": [], "sparse_vector": [], "title": "GRIP: grammar-based IP integration and packaging for acceleration-rich SoC designs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Increased hardware IP reuse is required to meet the productivity demands for the future complex Systems-on-Chip (SoC). Nowadays, IP integration is enabled using standardized meta-data formats such as IP-XACT. We present a new concept called grammar-based IP integration and packaging (GRIP), which additionally encodes design integration knowledge into a set of graph re-writing rules using standard IP-XACT. These GRIP rules are packaged into a domain-specific library of IP blocks. The library can be supplied by an IP provider along to an SoC architect. An integration tool can automatically use the GRIP rules to search the design space using the integration knowledge of the IP provider. The tool generates all design alternatives with different trade-offs for the SoC architect. We demonstrate the GRIP approach on a computer vision IP library for FPGA-based SoCs. Eighteen functional design alternatives are automatically generated within a few hours using IP integration knowledge encoded by the GRIP rules.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744845"}, {"primary_key": "4390521", "vector": [], "sparse_vector": [], "title": "A Lightweight Early Arbitration Method for Low-Latency Asynchronous 2D-Mesh NoC&apos;s.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A new asynchronous low-latency interconnection network is introduced for a 2D mesh topology. The network-on-chip, named AEoLiAN, contains a fast lightweight monitoring network to notify the routers of incoming traffic, thereby allowing arbitration and channel allocation to be initiated in advance. In contrast, several recent synchronous early arbitration methods require significant resource overhead, including use of hybrid networks, or wide monitoring channels and additional VCs. The proposed approach has much smaller overhead, allowing a finer-grain router-by-router early arbitration, with monitoring and data advancing independently at different speeds. The new router was implemented in 45nm technology using a standard cell library. It had 52% lower area than a similar lightweight synchronous switch, xpipesLite, with no early arbitration capability. Network-level simulations were then performed on 6 diverse synthetic benchmarks in an 8x8 2D mesh network topology, and the performance of the new network was compared to an asynchronous baseline. Considerable improvements in system latency over all benchmarks for moderate traffic were obtained, ranging from 34.4-37.9%. Interestingly, the proposed acceleration technique also enabled throughput gains, ranging from 14.7-27.1% for the top 5 benchmarks. In addition, a zero-load end-to-end latency of only 4.9ns was observed, for the longest network path through 15 routers and 14 hops.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": ""}, {"primary_key": "4390522", "vector": [], "sparse_vector": [], "title": "A 127 fps in full hd accelerator based on optimized AKAZE with efficiency and effectiveness for image feature extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visual feature extraction is a fundamental technique in vision-based application. This paper proposes an effective and efficient VLSI architecture based on optimized accelerated KAZE (AKAZE) for real-time feature extraction. AKAZE is a new feature detection algorithm with strong robustness for object recognition. To extract feature more robustly and reduce hardware resource, a two-dimensional pipeline array named Loop-Snake Architecture is presented. It takes advantage of computational similarity in different octaves and provides flexibility in precision-speed tradeoff on the fly. Furthermore, Polar Local Difference Binary descriptor and the corresponding structure are proposed to greatly reduce the memory bandwidth requirement and improve the speed. The experimental results indicate the optimized algorithm keeps the same accuracy compared with the original algorithm. The whole hardware system achieves 127fps in 1080p resolution at 200 MHz frequency. The throughput is twice faster than the state-of-the-art solutions.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744772"}, {"primary_key": "4390523", "vector": [], "sparse_vector": [], "title": "A low power unsupervised spike sorting accelerator insensitive to clustering initialization in sub-optimal feature space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online unsupervised spike sorting or clustering is an integral component of implantable closed-loop brain-computer-interface systems. Robust clustering performance against various non-idealities such as poor initialization and order-of-arrival of inputs are desirable while meeting the minimal area and power requirements for implants. We explore an online and unsupervised spike-sorting algorithm utilizing a low-overhead feature screening process that improves feature discriminability in the use of sub-optimal features for reducing hardware complexity. Based on the algorithm, an accelerator architecture that performs feature screening and clustering is devised and implemented in a 65-nm high-VTH CMOS, largely improving clustering accuracy even with poor clustering initialization. In the post-layout static timing and power simulation, the power consumption and the area of the accelerator are found to be 2.17 μW/ch and 0.052 μm2/ch, respectively, which are 53% and 25% smaller than the previous designs, while achieving the required throughput of 420 sorting/s at the supply voltage of 300mV.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744779"}, {"primary_key": "4390524", "vector": [], "sparse_vector": [], "title": "ΣVP: host-GPU multiplexing for efficient simulation of multiple embedded GPUs on virtual platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite their proliferation across many embedded platforms, GPUs present still many challenges to embedded-system designers. In particular, GPU-optimized software actually slows down the execution of embedded applications on system simulators. This problem is worse for concurrent simulations of multiple instances of embedded devices equipped with GPUs. To address this challenge, we present ΣVP, a framework to accelerate concurrent simulations of multiple virtual platforms by leveraging the physical GPUs present on the host machine. ΣVP multiplexes the host GPUs to speed up the concurrent simulations without requiring any change to the original GPU-optimized application code. With ΣVP, GPU applications run more than 600 times faster than GPU-software emulation on virtual platforms. We also propose Kernel Interleaving and Kernel Coalescing, two techniques that further speed up the simulation by one order of magnitude. Finally, we show how ΣVP supports simulation-based functional validation and performance/power estimation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744913"}, {"primary_key": "4390525", "vector": [], "sparse_vector": [], "title": "A control-theoretic approach for energy efficient CPU-GPU subsystem in mobile platforms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a control-theoretic approach to optimize the energy consumption of integrated CPU and GPU subsystems for graphic applications. It achieves this via a dynamic management of the CPU and GPU frequencies. To this end, we first model the interaction between the GPU and CPU as a queuing system. Second, we formulate a Multi-Input-Multi-Output state-space closed loop control to ensure robustness and stability. We evaluated this control on an Intel Baytrail-based Android platform. Experimental evaluations show energy savings of 17.4% in the CPU-GPU subsystem with a low performance impact of 0.9%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744773"}, {"primary_key": "4390526", "vector": [], "sparse_vector": [], "title": "Effective model-based mask fracturing for mask cost reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The use of aggressive resolution enhancement techniques like multiple patterning and inverse lithography (ILT) has led to expensive photomasks. Growing mask write time has been a key reason for the cost increase. Moreover, due to scaling, e-beam proximity effects can no longer be ignored. Model-based mask fracturing has emerged as a useful technique to address these critical challenges by allowing overlapping shots and compensating for proximity effects during fracturing itself. However, it has been shown recently that heuristics for model-based mask fracturing can be suboptimal by more than 1:6x on average for ten real ILT shapes, highlighting the need for better heuristics. In this work, we propose a new model-based mask fracturing method that significantly outperforms all the previously reported heuristics. The number of e-beam shots of our method is 23% less than a state-of-the-art prototype version of capability within a commercial EDA tool for e-beam mask shot decomposition (PROTO-EDA) for ten ILT mask shapes. Moreover, our method has an average runtime of less than 1:4s per shape.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744828"}, {"primary_key": "4390527", "vector": [], "sparse_vector": [], "title": "New game, new goal posts: a recent history of timing closure.", "authors": ["<PERSON>"], "summary": "Timing closure is the most critical phase of modern system-on-chip implementation: without timing closure, there is no tapeout. Timing closure is the end result of (i) years of methodology development, script development, signoff recipe development, etc.; (ii) months of block- and top-level final physical implementation; and (iii) a last set of manual noise and DRC fixes, with a final signoff analysis and physical verification. Over the past decade, key aspects of the underlying process and device technologies, modeling standards, EDA tooling, design methodology, and signoff criteria have changed the nature of timing closure. This paper surveys such recent evolutions in timing closure and notes directions for near-term future evolutions.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747937"}, {"primary_key": "4390528", "vector": [], "sparse_vector": [], "title": "Achieving power and reliability sign-off for automotive semiconductor designs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Providing high quality automotive electronics with low power consumption and high operating performance requires excellent engineering methodology using the most accurate models (APL, CMM, .libs etc). Dynamic voltage drop affects power, timing and reliability of a design. In order to see the correct hot spots and decide on the appropriate counter measures without over-design, it is crucial to have the most accurate models. This presentation discusses the accuracy requirements at Infineon and the established dynamic voltage drop sign-off flow based on absolute voltage levels that affect the data integrity, as well as switching (timing) and non-switching (signal noise) signals.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747909"}, {"primary_key": "4390529", "vector": [], "sparse_vector": [], "title": "Parallel execution of AUTOSAR legacy applications on multicore ECUs with timed implicit communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Parallelization of AUTOSAR legacy applications is a fundamental step to exploit the performance of multi-core ECUs (MCEs). However, the migration of an application from a single-core ECU (SCE) to a MCE presents two challenges: first, the extraction of parallelism from an application (composed of tasks) is not always possible due to communication among tasks. Second, reproducing the same data-flow on all target MCEs is required to guarantee the same (predictable) functional behaviour without exhaustive validation and testing efforts. This paper introduces timed implicit communication (TIC) for decoupling task communication to allow parallel execution of producer and consumer, while the same data-flow is achieved on all MCEs. Therefore, AUTOSAR implicit communication is applied at task-level and extended by defined communication times, which are derived from the original SCE configuration. This is realized by storing produced data in a buffer with a publication timestamp attached. TIC is implemented at AUTOSAR RTE level and does not require modification of source code.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744889"}, {"primary_key": "4390530", "vector": [], "sparse_vector": [], "title": "Thermal constrained resource management for mixed ILP-TLP workloads in dark silicon chips.", "authors": ["<PERSON><PERSON>dr", "Santiago Pagani", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In dark silicon chips, a significant amount of on-chip resources cannot be simultaneously powered on and need to stay dark, i.e., power gated, in order to avoid thermal emergencies. This paper presents a resource management technique, called DsRem, that selects the number of active cores jointly with their voltage/frequency (v/f) levels, considering the high Instruction Level Parallelism (ILP) or Thread Level Parallelism (TLP) nature of different applications, in order to maximize the overall system performance. DsRem leverages the positioning of dark cores, to efficiently dissipate the heat generated by the active cores. This facilitates increasing the v/f level of the active cores, which leads to further performance improvement. Compared to state-of-the-art thermal-aware task application mapping, DsRem achieves up to 46% performance gain, while avoiding any thermal emergencies. Additionally, DsRem outperforms the boosting technique with 26%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744916"}, {"primary_key": "4390531", "vector": [], "sparse_vector": [], "title": "Vibration-based secure side channel for medical devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Implantable and wearable medical devices are used for monitoring, diagnosis, and treatment of an ever-increasing range of medical conditions, leading to an improved quality of life for patients. The addition of wireless connectivity to medical devices has enabled post-deployment tuning of therapy and access to device data virtually anytime and anywhere but, at the same time, has led to the emergence of security attacks as a critical concern. While cryptography and secure communication protocols may be used to address most known attacks, the lack of a viable secure connection establishment and key exchange mechanism is a fundamental challenge that needs to be addressed. We propose a vibration-based secure side channel between an external device (medical programmer or smartphone) and a medical device. Vibration is an intrinsically short-range, user-perceptible channel that is suitable for realizing physically secure communication at low energy and size/weight overheads. We identify and address key challenges associated with the vibration channel, and propose a vibration-based wakeup and key exchange scheme, named SecureVibe, that is resistant to battery drain attacks. We analyze the risk of acoustic eavesdropping attacks and propose an acoustic masking countermeasure. We demonstrate and evaluate vibration-based wakeup and key exchange between a smartphone and a prototype medical device in the context of a realistic human body model.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744928"}, {"primary_key": "4390532", "vector": [], "sparse_vector": [], "title": "Design &amp; verification of automotive SoC firmware.", "authors": ["Veit <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Increasing complexity and functionality of automotive SoCs (Systems-on-Chip) leads to a growing part of functionality implemented in firmware. Requirements for short-time-to-market and extensive verification due to safety-critical environments requires firmware development and verification to happen concurrent with hardware development, which is challenging in traditional hardware-centric SoC flows. We present in this paper a set of different techniques part of Infineon's ISO 26262 flow for firmware development, which tackle these challenges and enable us to align firmware development to the same time line as the remaining SoC development.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747918"}, {"primary_key": "4390533", "vector": [], "sparse_vector": [], "title": "Domain-wall memory buffer for low-energy NoCs.", "authors": ["<PERSON>.", "Haifeng Xu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Networks-on-chip (NoCs) have become a leading energy consumer in modern multi-core processors, with a considerable portion of this energy originating from the large number of virtual channel (FIFO) buffers. While emerging memories have been considered for many architectural components such as caches, the asymmetric access properties and relatively small size of network-FIFOs compared to the required peripheral circuitry has led to few such replacements proposed for NoCs. In this paper, we propose control schemes that leverage the\\shift-register\" nature of spintronic domain-wall memory (DWM) to replace conventional memory buffers for the NoC. Our results indicate that the best shift-based scheme utilizes a dual-nanowire approach to ensure that reads and writes can be more effectively aligned with access ports for simultaneous access in the same cycle. Our approach provides a 2.93X speedup over a DWM buffer using a traditional FIFO memory control scheme with a 1.16X savings in energy. Compared to a SRAM-FIFO it exhibits an 8% message latency degradation versus a 56% energy reduction. The resulting approach achieves a 53% reduction in energy delay product compared to SRAM and a 42% reduction in energy delay product versus STT-MRAM.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744826"}, {"primary_key": "4390534", "vector": [], "sparse_vector": [], "title": "Guidelines to design parity protected write-back L1 data cache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Several decades of technology scaling has brought the challenge of soft errors to modern computing systems, and caches are most susceptible to soft errors. While it is straightforward to protect L2 and other lower level caches using error correcting coding (ECC), protecting the L1 data caches poses a challenge. Parity-based protection of L1 data cache is a more power-efficient alternative, however, some questions still linger -- How effective is parity protection for caches? How can we design a parity-based L1 data cache so as to maximize the protection achieved? The goal of this paper is to perform a quantitative evaluation of the protection afforded by various parity-protected cache design alternatives, and formulate guidelines for the design of power-efficient and reliable L1 data caches. Towards this goal, this paper develops an algorithm to accurately model the vulnerability of data in caches, in the presence of various configurations of parity protection, and validate it against extensive fault injection campaigns. We find that, (i) checking parity at reads only (and not at writes) provides 11% more protection with 30% lesser power overheads as compared to that at both reads and writes; and (ii) when implementing parity at the word-level granularity for 53% improved protection as compared to block-level parity implementation, the dirty-bits in the cache should also be implemented at the same granularity, otherwise, there is no improvement in protection. We find several popular commercial processors -- even the ones specifically designed for reliability -- not following these design guidelines, and resulting in sub-optimial designs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744846"}, {"primary_key": "4390535", "vector": [], "sparse_vector": [], "title": "Design for low test pattern counts.", "authors": ["<PERSON><PERSON>", "Elham K. Mo<PERSON>dam", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a new method to design digital circuits for low pattern counts, one of the key factors shaping cost-effective VLSI test schemes. The method identifies the largest conflicts between internal signals that prevent efficient test compaction in ATPG. These locations are modified by inserting conflict-reducing test points (CRTP) to significantly reduce the ATPG-produced pattern counts. Experimental results obtained for large industrial designs with on-chip test compression demonstrate, on average, 3x -- 4x reduction in stuck-at and transition patterns and 3x shorter ATPG times.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744817"}, {"primary_key": "4390536", "vector": [], "sparse_vector": [], "title": "Compositional modeling and analysis of automotive feature product lines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern automotive systems are composed of hundreds of software-implemented features often interacting with physical subsystems under real-time constraints. For efficient management of their development, the features are conceived and realized as product lines involving variability with different variants being deployed in different vehicle classes. The variability information is expressed at different levels of abstraction during the various phases of development, like requirements, design and implementation. We introduce and study a formal model of such feature product lines capable of capturing variability and real-time behavior. We define a notion of conformance to relate the variability at different levels of abstraction and propose a compositional method of verifying conformance of multiple features. The proposed approach naturally extends to hybrid system behaviors consisting of discrete and continuous plant variables. We demonstrate the applicability of the approach by giving a simple paradigmatic example.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747928"}, {"primary_key": "4390537", "vector": [], "sparse_vector": [], "title": "Towards enhancing analog circuits sizing using SMT-based techniques.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an approach for enhancing analog circuit sizing using Satisfiability Modulo Theory (SMT). The circuit sizing problem is encoded using nonlinear constraints. An SMT-based algorithm exhaustively explores the design space, where the biasing-level design variables are conservatively tracked using a collection of hyperrectangles. The device dimensions are then determined by accurately relating biasing to geometry-level design parameters. We demonstrate the feasibility and efficiency of the proposed methodology on a two-stage amplifier and a folded cascode amplifier. Experimental results show that our approach can achieve higher quality in analog synthesis and unrivaled coverage of the design space.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744919"}, {"primary_key": "4390538", "vector": [], "sparse_vector": [], "title": "Evaluating battery aging on mobile devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Battery-related problems in mobile devices have been extensively investigated in both industry and literature. In particular, battery aging is a critical issue, since battery lifetime decreases as usage time increases. Battery aging primarily causes inconvenience to users by necessitating frequent recharging, and also affects the accuracy of power estimations for mobile devices. Evaluating battery aging and its effects has rarely been addressed in prior works. In this paper, we propose an online scheme to quantify the battery aging of mobile devices. Specifically, we estimate the degree of battery aging as a ratio metric based on patterns of charging time. For example, an estimate of 50% indicates that the battery capacity is only half of full capacity, meaning that the battery usage time is only approximately half that of the new battery's. Our scheme works autonomously on mobile devices and does not require any external equipment. The extensive experiments demonstrated that the proposed scheme quantifies battery aging accurately.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744838"}, {"primary_key": "4390539", "vector": [], "sparse_vector": [], "title": "Efficient dynamic information flow tracking on a processor with core debug interface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic information flow tracking (DIFT) is a promising solution to prevent various attacks on software running on a processor. Previous hardware solutions usually mandate drastic change to internal processor architecture. More recent ones to minimize the change have proposed external devices for DIFT. However, these approaches intrinsically suffer from the high overhead to communicate with their external devices. Consequently, they either significantly lose performance, or inevitably make invasive modifications to the processor inside. Our solution also rely on external hardware for DIFT, but unlike theirs, ours exploits the core debug interface (CDI) to tackle the communication issue. CDI is provided in most commercial processors for debugging so that we were able to build our system simply by plugging our hardware to the processor via CDI, precluding the need for altering the processor itself. Experiments show that our hardware efficiently performs DIFT mainly thanks to the support of CDI that helps us cut substantially down the communication costs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744830"}, {"primary_key": "4390540", "vector": [], "sparse_vector": [], "title": "Optimizing stream program performance on CGRA-based systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-Grained Reconfigurable Architectures (CGRAs), often used as coprocessors for DSP and multimedia kernels, can deliver highly energy-efficient execution for compute-intensive kernels. Simultaneously, stream applications, which consist of many actors and channels connecting them, can provide natural representations for DSP applications, and therefore be a good match for CGRAs. We present our results of mapping DSP applications written in StreamIt language to CGRAs, along with our mapping flow. One important challenge in mapping is how to manage the multitude of kernels in the application for the limited local memory of a CGRA, for which we present a novel integer linear programming-based solution. Our evaluation results demonstrate that our software and hardware optimizations can help generate highly efficient mapping of stream applications to CGRAs, enabling far more energy-efficient executions (7× worse to 50× better) compared to using state-of-the-art GP-GPUs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744884"}, {"primary_key": "4390541", "vector": [], "sparse_vector": [], "title": "Complementary communication path for energy efficient on-chip optical interconnects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>;<PERSON>"], "summary": "Optical interconnects are considered to be one of the key solutions for future generation on-chip interconnects. However, energy efficiency is mainly limited by the losses incurred by the optical signals, which considerably reduces the optical power received by the photodetectors. In this paper we propose a differential transmission of the modulated signals, which contributes to improve the transmission of the optical signal power on the receiver side. With this approach, it is possible to reduce the input laser power and increase the energy efficiency of the optical communication. The approach is generic and can be applied to SWSR-, MWSR-, SWMR- and MWMR-like architectures.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744810"}, {"primary_key": "4390542", "vector": [], "sparse_vector": [], "title": "A STT-RAM-based low-power hybrid register file for GPGPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guangyu Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, general-purpose graphics processing units (GPGPUs) have been widely used to accelerate computing in various applications. To store the contexts of thousands of concurrent threads on a GPU, a large static random-access memory (SRAM)-based register file is employed. Due to high leakage power of SRAM, the register file consumes 20% to 40% of the total GPU power consumption. Thus, hybrid memory system, which combines SRAM and the emerging non-volatile memory (NVM), has been employed for register file design on GPUs. Although it has shown strong potential to alleviate the power issue of GPUs, existing hybrid memory solutions might not exploit the intrinsic feature of GPU register file. By leveraging the warp schedule on GPU, this paper proposes a hybrid register architecture which consists of a NVM-based register file and mixed SRAM-based write buffers with a warp-aware write back strategy. Simulation results show that our design can eliminate 64% of write accesses to NVM and reduce power of register file by 66% on average, with only 4.2% performance degradation. After we apply the power gating technique, the register power is further reduced to 25% of SRAM counterpart on average.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744785"}, {"primary_key": "4390543", "vector": [], "sparse_vector": [], "title": "Joint precision optimization and high level synthesis for approximate computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sachin <PERSON>", "<PERSON>"], "summary": "Approximate computing has been recognized as an effective low power technique for applications with intrinsic error tolerance, such as image processing and machine learning. Existing efforts on this front are mostly focused on approximate circuit design, approximate logic synthesis or processor architecture approximation techniques. This work aims at how to make good use of approximate circuits at system and block level. In particular, approximation aware scheduling, functional unit allocation and binding algorithms are developed for data intensive applications. Simple yet credible error models, which are essential for precision control in the optimizations, are investigated. The algorithms are further extended to include bitwidth optimization in fixed point computations. Experimental results, including those from Verilog simulations, indicate that the proposed techniques facilitate desired energy savings under latency and accuracy constraints.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744863"}, {"primary_key": "4390544", "vector": [], "sparse_vector": [], "title": "Merging the interface: power, area and accuracy co-optimization for RRAM crossbar-based mixed-signal computing system.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The invention of resistive-switching random access memory (RRAM) devices and RRAM crossbar-based computing system (RCS) demonstrate a promising solution for better performance and power efficiency. The interfaces between analog and digital units, especially AD/DAs, take up most of the area and power consumption of RCS and are always the bottleneck of mixed-signal computing systems. In this work, we propose a novel architecture, MEI, to minimize the overhead of AD/DA by MErging the Interface into the RRAM crossbar. An optional ensemble method, the Serial Array Adaptive Boosting (SAAB), is also introduced to take advantage of the area and power saved by MEI and boost the accuracy and robustness of RCS. On top of these two methods, a design space exploration is proposed to achieve trade-offs among accuracy, area, and power consumption. Experimental results on 6 diverse benchmarks demonstrate that, compared with the traditional architecture with AD/DAs, MEI is able to save 54.63%~86.14% area and reduce 61.82%~86.80% power consumption under quality guarantees; and SAAB can further improve the accuracy by 5.76% on average and ensure the system performance under noisy conditions.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744870"}, {"primary_key": "4390545", "vector": [], "sparse_vector": [], "title": "Compiler directed automatic stack trimming for efficient non-volatile processors.", "authors": ["Qing&apos;an Li", "<PERSON><PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable devices are becoming increasingly important in our daily lives. Energy harvesting instead of battery is a better power source for these wearable devices due to many advantages. However, harvested energy is often unstable and program execution will be frequently interrupted. Non-volatile processors demonstrate promising advantages to back up volatile state before the system energy is depleted. But Non-volatile processors require additional memory for backing up, thus introducing non-negligible overhead in terms of energy, runtime as well as chip area. In this work, we target at non-volatile register reduction for energy harvesting based wearable devices. This paper proposes to stack trimming the memory footprint via a novel compiler directed method. The evaluation results deliver on average 28.6% reduction of non-volatile register files for backing up stack area, with ultra low runtime overhead.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744809"}, {"primary_key": "4390546", "vector": [], "sparse_vector": [], "title": "High performance dummy fill insertion with coupling and uniformity constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In deep-submicron VLSI manufacturing, dummy fills are widely applied to reduce topographic variations and improve layout pattern uniformity. However, the introduction of dummy fills may impact the wire electrical properties, such as coupling capacitance. Traditional tile-based method for fill insertion usually results in very large number of fills, which increases the cost of layout storage. In advanced technology nodes, solving the tile-based dummy fill design is more and more expensive. In this paper, we propose a high performance dummy fill insertion and sizing framework, where the coupling capacitance issues and density variations are considered simultaneously. The experimental results for ICCAD 2014 contest benchmarks demonstrate the effectiveness of our methods.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744850"}, {"primary_key": "4390547", "vector": [], "sparse_vector": [], "title": "Impact assessment of net metering on smart home cyberattack detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite the increasing popularity of the smart home concept, such a technology is vulnerable to various security threats such as pricing cyberattacks. There are some technical advances in developing detection and defense frameworks against those pricing cyberattacks. However, none of them considers the impact of net metering, which allows the customers to sell the excessively generated renewable energy back to the grid. At a superficial glance, net metering seems to be irrelevant to the cybersecurity, while this paper demonstrates that its implication is actually profound.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747930"}, {"primary_key": "4390548", "vector": [], "sparse_vector": [], "title": "Network footprint reduction through data access and computation placement in NoC-based manycores.", "authors": ["<PERSON>", "Jagadish Kotra", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Targeting network-on-chip based manycores, we propose a novel compiler framework to optimize the network latencies experienced by off-chip data accesses in reaching the target memory controllers. Our framework consists of two main components: data access placement and computation placement. In the data access placement, we separate the data access nodes from the computation nodes, with the goal of minimizing the number of links that need to be visited by the request messages. In the computation placement, we introduce computation decomposition and select appropriate computation nodes, to reduce the amount of data sent in the response messages and also to minimize the number of communication links visited. We performed an experimental evaluation of our proposed approach, and the results show an average execution time improvement of 21.1%, while reducing the network latency by 67.3%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744876"}, {"primary_key": "4390549", "vector": [], "sparse_vector": [], "title": "Vortex: variation-aware training for memristor X-bar.", "authors": ["<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qing Wu", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in development of memristor devices and crossbar integration allow us to implement a low-power on-chip neuromorphic computing system (NCS) with small footprint. Training methods have been proposed to program the memristors in a crossbar by following existing training algorithms in neural network models. However, the robustness of these training methods has not been well investigated by taking into account the limits imposed by realistic hardware implementations. In this work, we present a quantitative analysis on the impact of device imperfections and circuit design constraints on the robustness of two popular training methods -- \"close-loop on-device\" (CLD) and \"open-loop off-device\" (OLD). A novel variation-aware training scheme, namely, Vortex, is then invented to enhance the training robustness of memristor crossbar-based NCS by actively compensating the impact of device variations and optimizing the mapping scheme from computations to crossbars. On average, Vortex can significantly improve the test rate by 29.6% and 26.4%, compared to the traditional OLD and CLD, respectively.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744930"}, {"primary_key": "4390550", "vector": [], "sparse_vector": [], "title": "Ambient energy harvesting nonvolatile processors: from circuit to system.", "authors": ["<PERSON><PERSON>", "<PERSON>ew<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xueqing Li", "Kaisheng Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy harvesting is gaining more and more attentions due to its characteristics of ultra-long operation time without maintenance. However, frequent unpredictable power failures from energy harvesters bring performance and reliability challenges to traditional processors. Nonvolatile processors are promising to solve such a problem due to their advantage of zero leakage and efficient backup and restore operations. To optimize the nonvolatile processor design, this paper proposes new metrics of nonvolatile processors to consider energy harvesting factors for the first time. Furthermore, we explore the nonvolatile processor design from circuit to system level. A prototype of energy harvesting nonvolatile processor is set up and experimental results show that the proposed performance metric meets the measured results by less than 6.27% average errors. Finally, the energy consumption of nonvolatile processor is analyzed under different benchmarks.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747910"}, {"primary_key": "4390551", "vector": [], "sparse_vector": [], "title": "RENO: a high-efficient reconfigurable neuromorphic computing accelerator design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Qing Wu", "<PERSON><PERSON><PERSON>"], "summary": "Neuromorphic computing is recently gaining significant attention as a promising candidate to conquer the well-known <PERSON> bottleneck. In this work, we propose RENO -- a efficient reconfigurable neuromorphic computing accelerator. RENO leverages the extremely efficient mixed-signal computation capability of memristor-based crossbar (MBC) arrays to speedup the executions of artificial neural networks (ANNs). The hierarchically arranged MBC arrays can be configured to a variety of ANN topologies through a mixed-signal interconnection network (M-Net). Simulation results on seven ANN applications show that compared to the baseline general-purpose processor, RENO can achieve on average 178.4x (27.06x) performance speedup and 184.2x (25.23x) energy savings in high-efficient multilayer perception (high-accurate auto-associative memory) implementation. Moreover, in the comparison to a pure digital neural processing unit (D-NPU) and a design with MBC arrays co-operating through a digital interconnection network, RENO still achieves the fastest execution time and the lowest energy consumption with similar computation accuracy.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744900"}, {"primary_key": "4390552", "vector": [], "sparse_vector": [], "title": "A statistical methodology for noise sensor placement and full-chip voltage map generation.", "authors": ["<PERSON><PERSON>", "Shupeng Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hai<PERSON> Qian"], "summary": "Noise margin violation, also known as voltage emergency induced by continuously reducing noise margin and increasing magnitude of current swings, is becoming a severe threat to the correct execution of applications in processors. Noise sensors can be placed in the non-function area of processors to detect such emergencies by monitoring runtime voltage fluctuations. In this work, we aim to accurately predict the voltage droops using a small set of sensors. We achieve our goal in two steps: We first propose a methodology via group lasso approach to select the optimal set of noise sensors, then build a practical model via ordinary least-squares fitting approach to predict the voltage in the function area of the chip, using the selected sensors in non-function area. Experiment results show that when compared to the full-chip voltage transient simulation, the prediction error of our model is much less than 0.01, and compared to prior work, our approach can achieve better error rates of voltage emergency detection (less than half).", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744784"}, {"primary_key": "4390553", "vector": [], "sparse_vector": [], "title": "Cloning your mind: security challenges in cognitive system designs and their solutions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>", "Qing Wu", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the booming of big-data applications, cognitive information processing systems that leverage advanced data processing technologies, e.g., machine learning and data mining, are widely used in many industry fields. Although these technologies demonstrate great processing capability and accuracy in the relevant applications, several security and safety challenges are also emerging against these learning based technologies. In this paper, we will first introduce several security concerns in cognitive system designs. Some real examples are then used to demonstrate how the attackers can potentially access the confidential user data, replicate a sensitive data processing model without being granted the access to the details of the model, and obtain some key features of the training data by using the services publically accessible to a normal user. Based on the analysis of these security challenges, we also discuss several possible solutions that can protect the information privacy and security of cognitive systems during different stages of the usage.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747915"}, {"primary_key": "4390554", "vector": [], "sparse_vector": [], "title": "A spiking neuromorphic design with resistive crossbar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hai <PERSON>", "Qing Wu", "<PERSON><PERSON>"], "summary": "Neuromorphic systems recently gained increasing attention for their high computation efficiency. Many designs have been proposed and realized with traditional CMOS technology or emerging devices. In this work, we proposed a spiking neuromorphic design built on resistive crossbar structures and implemented with IBM 130nm technology. Our design adopts a rate coding scheme where pre- and post-neuron signals are represented by digitalized pulses. The weighting function of pre-neuron signals is executed on the resistive crossbar in analog format. The computing result is transferred into digitalized output spikes via an integrate-and-fire circuit (IFC) as the post-neuron. We calibrated the computation accuracy of the entire system through circuit simulations. The results demonstrated a good match to our analytic modeling. Furthermore, we implemented both feedforward and Hopfield networks by utilizing the proposed neuromorphic design. The system performance and robustness were studied through massive Monte-Carlo simulations based on the application of digital image recognition. Comparing to the previous crossbar-based computing engine that represents data with voltage amplitude, our design can achieve >50% energy savings, while the average probability of failed recognition increase only 1.46% and 5.99% in the feedforward and Hopfield implementations, respectively.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744783"}, {"primary_key": "4390555", "vector": [], "sparse_vector": [], "title": "A reconfigurable analog substrate for highly efficient maximum flow computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the design and analysis of a novel analog reconfigurable substrate that enables fast and efficient computation of maximum flow on directed graphs. The substrate is composed of memristors and standard analog circuit components, where the on/off states of the crossbar switches encode the graph topology. We show that upon convergence, the steady-state voltages in the circuit capture the solution to the maximum flow problem. We also provide techniques to minimize the impacts of variability and non-ideal circuit components on the solution quality, enabling practical implementation of the proposed substrate. Performance evaluation demonstrates orders of magnitude improvements in speed and energy efficiency compared to a standard CPU implementation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744781"}, {"primary_key": "4390556", "vector": [], "sparse_vector": [], "title": "DERA: yet another differential fault attack on cryptographic devices based on error rate analysis.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Feng <PERSON>", "<PERSON><PERSON>"], "summary": "Fault-injection attack is a serious threat to the security of cryptographic devices, and various differential fault analysis (DFA) techniques have been presented in the literature over the years. These attacks differ in terms of the underlining assumption on the fault models, the key distinguisher and the complexity of the associated analytical algorithm. In this work, we propose a new DFA technique that uses the inherent bias of the error rates among different signals as the foundation of the key distinguisher design, namely differential error rate analysis (DERA). Compared to existing DFA solutions, DERA is a more efficient and effective attack, in terms of both temporal and spatial needs for the attack, as demonstrated with FPGA emulation in our experiments.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744816"}, {"primary_key": "4390557", "vector": [], "sparse_vector": [], "title": "Efficient design space exploration of embedded platforms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Embedded systems are developed within a cost-driven process, such that many purpose-build systems share a common embedded platform. For instance, a shared hardware device might be applied in various use cases with different software. Here, the bare hardware is considered as platform while the final hardware/software system is the product that implements a certain set of features. In this paper, we propose an efficient design space exploration methodology that optimizes a set of embedded platforms. For this purpose, we separate the exploration into two stages: (1) A set of candidate platforms is determined within an optimization framework that can cope with different system models, considering the optimization objectives and feature coverage. (2) The final set of target platforms is determined as subset of the candidates by minimizing the expected objectives like cost, area, etc. The experimental results give evidence that our modular approach is more flexible and scalable than state-of-the-art methodologies.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744829"}, {"primary_key": "4390558", "vector": [], "sparse_vector": [], "title": "VWS: a versatile warp scheduler for exploring diverse cache localities of GPGPU applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON>", "Hai <PERSON>"], "summary": "Massive multi-threading of GPGPU demands for efficient usage of caches with limited capacity. In this work, we propose a versatile warp scheduler (VWS) to reduce the cache miss rate in GPGPU. VWS retains the intra-warp cache locality using an efficient per-warp working set estimator and enhances intra-/inter-cooperative thread array (CTA) cache locality through imposing a CTA-aware scheduling policy and a new CTA dispatching mechanism. The significantly improved hit rate of cache hierarchy enables VWS to achieve on average 38.4% and 9.3% IPC improvement across diverse GPGPU applications compared to a widely-used and a state-of-the-art warp schedulers, respectively.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744931"}, {"primary_key": "4390559", "vector": [], "sparse_vector": [], "title": "Blocking unsafe behaviors in control systems through static and dynamic policy enforcement.", "authors": ["<PERSON>"], "summary": "One of the most universally accepted practices in computer security is the use of security policy enforcement. Under a policy enforcement regime, users and programs can only perform actions for which they are authorized by the security policy. Unfortunately, modern control systems fail to make effective use of policy enforcement. In many cases, privilege in control systems is binary--a single password is sufficient to gain most or all privileges within the system. In this paper, we consider the benefits and challenges of enforcing security policies for code running on Programmable Logic Controllers (PLCs). We first summarize two of our previous approaches, which place no trust in the PLC to behave correctly. While these approaches show promise, especially for current PLC architectures, they are lacking in comparison to approaches based on a trustworthy PLC design. Thus, we argue that future PLCs should implement a Trusted Computing Base (TCB). Such a TCB is a small set of trusted hardware and software that is sufficient for enforcing policies directly on the PLC. We also propose a method of doing policy enforcement on PLCs supporting a small TCB, and argue that it is the simpler and more effective means of doing policy enforcement for PLCs. We conclude that future PLCs should support a small TCB.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747913"}, {"primary_key": "4390560", "vector": [], "sparse_vector": [], "title": "Efficient memory partitioning for parallel data access in multidimensional arrays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Memory bandwidth bottlenecks severely restrict parallel access of data from memory arrays. To increase bandwidth, memory partitioning algorithms have been proposed to access multiple memory banks simultaneously. However, previous partitioning schemes propose complex partitioning algorithms, which leads to non-optimal memory bank space utilization and unnecessary storage overhead. In this paper, we develop an efficient memory partitioning strategy with low time complexity and low storage overhead for data access in multidimensional arrays. Experimental results show that our memory partitioning algorithm saves up to 93.7% in the amount of arithmetic operations, 96.9% in execution time and 31.1% in storage overhead, compared to the state-of-the-art approach.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744831"}, {"primary_key": "4390561", "vector": [], "sparse_vector": [], "title": "PACO: fast average-performance estimation for time-randomized caches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Probabilistic timing analysis is a powerful approach to derive worst-case execution time (WCET) estimates, as needed in safety-critical systems, in the presence of high-performance hardware features (e.g., caches). To that end, the timing behavior of certain hardware resources, such as caches, is randomized. Time-randomized (TR) caches allow deriving hit/miss probabilities for each access and probabilistic WCET estimates for the overall program.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744886"}, {"primary_key": "4390562", "vector": [], "sparse_vector": [], "title": "EM attack sensor: concept, circuit, and design-automation methodology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A side-channel attack exploiting EM-field leakage from a cryptographic processor IC is an existing serious threat to our information society. EM radiation during the IC operation is captured by an EM probe and the correlation to the crypto processing is statistically analyzed to reveal the secret information although it is protected in a software (algorithm) domain. This paper presents a reactive hardware (implementation) domain countermeasure against this EM attack, namely EM attack sensor. An on-chip sensor coil detects EM probe approach and reacts to protect the secret information from the tamper attack. The sensor concept and low-cost digital circuit implementation are reviewed, and the detail of the design-automation methodology highly-compatible to standard EDA tools is presented. A small hardware overhead of the sensor is silicon-proven in an actual 0.18μm CMOS test-chip implementation together with a 128bit AES crypto core. The test-chip measurements demonstrate successful sensor operation against the actual EM probe attack.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747923"}, {"primary_key": "4390563", "vector": [], "sparse_vector": [], "title": "Security analysis of automotive architectures using probabilistic model checking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel approach to security analysis of automotive architectures at the system-level. With an increasing amount of software and connectedness of cars, security challenges are emerging in the automotive domain. Our proposed approach enables assessment of the security of architecture variants and can be used by decision makers in the design process. First, the automotive Electronic Control Units (ECUs) and networks are modelled at the system-level using parameters per component, including an exploitability score and patching rates that are derived from an automated or manual assessment. For any specific architecture variant, a Continuous-Time Markov Chain (CTMC) model is determined and analyzed in terms of confidentiality, integrity and availability, using probabilistic model checking. The introduced case study demonstrates the applicability of our approach, enabling, for instance, the exploration of parameters like patch rate targets for ECU manufacturers.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744906"}, {"primary_key": "4390564", "vector": [], "sparse_vector": [], "title": "Evaluation of functional mock-up interface for vehicle power network modeling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper describes the results of an evaluation of vehicle power network modeling using Functional Mock-up Interface (FMI). FMI, developed by the Modelica Association, is a tool-independent standard for a model interface. This study compared simulation results between the vehicle power network model with Functional Mock-up Units (FMUs) connected and the model without FMUs. Conversion adaptors were inserted to generate and connect each FMU because it was necessary to convert physical connections from acausal connections to causal inputs and outputs. Using FMI for Model Exchange, it was confirmed that the results for both simulations showed good matches, without any time delays although algebraic loops which would often occur in electrical circuit modeling were included. This method possesses significant potential to reduce the validation effort using models created by other departments or suppliers.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747926"}, {"primary_key": "4390565", "vector": [], "sparse_vector": [], "title": "Cutting structure-aware analog placement based on self-aligned double patterning with e-beam lithography.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Self-aligned double patterning (SADP) with complementary e-beam lithography (EBL) is one of the most promising hybrid-lithography techniques for sub-20nm designs. The complementary EBL mitigates the deficiencies of using a single cut mask in SADP. However, the low throughput and negative side effects of EBL might significantly increase the manufacturing costs and damage the symmetry properties in analog circuits. In this paper, we present the first work that considers SADP with EBL during analog placement to simultaneously optimize the area, wirelength, overlay errors, and e-beam shots. We first propose an overlay and cut conflict-aware SADP decomposition algorithm to optimize the overlay errors and e-beam shots in a layout. Then, a dynamic programming based module shifting technique is developed based on a symmetry-feasible slicing tree formulation to further minimize the differences of overlay errors and e-beam shots between symmetry modules during analog placement. To explore and obtain a desired placement, an analog placement flow is also presented. Experimental results show that our flow can effectively and efficiently reduce area, overlay errors, and e-beam shots while satisfying the symmetry constraints for analog placement.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744813"}, {"primary_key": "4390566", "vector": [], "sparse_vector": [], "title": "Layout-dependent-effects-aware analytical analog placement.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Layout-dependent effects (LDEs) have become a critical issue in modern analog and mixed-signal circuit designs. The three major sources of LDEs, well proximity, length of oxide diffusion, and oxide-to-oxide spacing, significantly affect the threshold voltage and mobility of devices. In this paper, we propose the first work to consider the three major sources of LDEs during analog placement. We first transform the three LDE models into nonlinear analytical placement models. Then an LDE-aware analytical analog placement algorithm is presented to mitigate the influence of the LDEs while improving circuit performance. Experimental results show that our placement algorithm can effectively and efficiently reduce the LDE-induced variations and improve circuit performance.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744865"}, {"primary_key": "4390567", "vector": [], "sparse_vector": [], "title": "An introduction into fault-tolerant quantum computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide a basic introduction of the core ideas and theory surrounding fault-tolerant quantum computation. Quantum fault-tolerance essentially refers to avoiding the uncontrollable cascade of errors caused by the interaction of quantum-bits. The presented concepts underlay the theoretical framework of large-scale quantum computation and are the driving force for many recent experimental efforts to construct small to medium sized arrays of controllable quantum bits. We examine the basic principles of redundant quantum encoding, required to protect quantum bits from errors generated from both imprecise control and environmental interactions. The novelty of this work consists in the presentation of fault-tolerance principles from a classical distributed computing perspective, as this enables a more straightforward introduction without sacrificing generality. The practicality of fault-tolerant quantum computing is analysed after introducing a metric of scalability and discussing the factors influencing it.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747911"}, {"primary_key": "4390568", "vector": [], "sparse_vector": [], "title": "Pushing multiple patterning in sub-10nm: are we ready?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>"], "summary": "Due to elongated delay of extreme ultraviolet lithography (EUVL), the semiconductor industry has been pushing the 193nm immersion lithopgrahy using multiple patterning to print critical features in 22nm/14nm technology nodes and beyond. Multiple patterning lithography (MPL) poses many new challenges to both mask design and IC physical design. The mask layout decomposition problem has been extensively studied, first on double patterning, then on triple or even quadruple patterning. Meanwhile, many studies have shown that it is very important to consider MPL implications at early physical design stages so that the overall design and manufacturing closure can be reached. In this paper, we provide a comprehensive overview on the state-of-the-art research results for MPL, from synergistic mask synthesis to physical design. We will also discuss the open problems as to pushing multiple patterning in sub-10nm.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747940"}, {"primary_key": "4390569", "vector": [], "sparse_vector": [], "title": "Tier-partitioning for power delivery vs cooling tradeoff in 3D VLSI for mobile applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Kambiz <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Power delivery to the tier farthest away from the package in 3D VLSI is challenging. This is because the current provided by the package on the bottom is (1) first used by other tiers before it reaches the top, and (2) delivered using extremely small-size intra and inter-tier vias. Our solution is a tier partitioning method that assigns power hungry cells to the tier closer to the package, which is farther away from the heat spreader. Our study shows that this approach alleviates the IR-drop, power delivery network (PDN) resource usage, and power consumption in the top tier. Moreover, moving the cells to the bottom tier, unlike popular belief, does not cause any serious thermal issues. This is especially true in mobile applications, where heat is dissipated by both the heat spreader and printed circuit board. In summary, our tier-partitioning leads to 24.66% IR-drop reduction, 28.57% PDN resource reduction, and 4% wirelength reduction, with < 1°C increase in temperature.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744917"}, {"primary_key": "4390570", "vector": [], "sparse_vector": [], "title": "Nautilus: fast automated IP design space search using guided genetic algorithms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's offerings of parameterized hardware IP generators permit very high degrees of performance and implementation customization. Nevertheless, it is ultimately still left to the IP users to set IP parameters to achieve the desired tuning effects. For the average IP user, the knowledge and effort required to navigate a complex IP's design space can significantly offset the productivity gain from using the IP. This paper presents an approach that builds into an IP generator an extended genetic algorithm (GA) to perform automatic IP parameter tuning. In particular, we propose extensions that allow IP authors to embed pertinent designer knowledge to improve GA performance. In the context of several IP generators, our evaluations show that (1) GA is an effective solution to this problem and (2) our modified IP author guided GA can reach the same quality of results up to an order of magnitude faster compared to the basic GA.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744875"}, {"primary_key": "4390571", "vector": [], "sparse_vector": [], "title": "Power-Performance Modelling of Mobile Gaming Workloads on Heterogeneous MPSoCs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Games have emerged as one of the most popular applications on mobile platforms. Recent platforms are now equipped with Heterogeneous Multiprocessor System-on-Chips (HMPSoCs) tightly integrating CPUs and GPUs on the same chip. This configuration enables high-end gaming on the platform but at the cost of high power consumption rapidly draining the underlying limited-capacity battery. The HMPSoCs are capable of independent Dynamic Voltage and Frequency Scaling (DVFS) for CPUs and GPUs for reduction in platform's power consumption. State-of-the-art power manager for mobile games on HMPSoCs oversimplifies the complex CPUGPU interplay. In this paper, we develop power-performance models predicting the impact of DVFS on mobile gaming workloads. Based on our models, we propose an efficient power management strategy and implement it on an Odroid-XU+E mobile platform. Measurements on the platform show that our power manager provides on average 20% increase in performance per watt when compared to the state-of-the-art.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": ""}, {"primary_key": "4390572", "vector": [], "sparse_vector": [], "title": "SoC security architecture: current practices and emerging needs.", "authors": ["<PERSON>"], "summary": "At the era of everything is connected, security has become an essential key question when starting a new System-on-chip architecture. With the proliferation of secure systems, it is expected that many design teams will lack the essential knowledge and time to look at the abundant literature. In this paper, we intend to provide a reasonable approach to tackle this problem and try to convey the essential design rules, design elements and threats to any reader. We cover a possible methodology to collect requirements based on a systematic identification of the assets needing protection and threats against these assets: and make sure the design is appropriately sized and adequately secure. Once the requirements have been collected, the architecture can be laid out and we discuss the main elements that can compose it: trusted execution environment, secure boot, secure software update, secure design-for-test (DFT) components, Random Number Generation (RNG).", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747943"}, {"primary_key": "4390573", "vector": [], "sparse_vector": [], "title": "Design, packaging, and architectural policy co-optimization for DC power integrity in 3D DRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Youn-<PERSON>k Park", "Kwang-Il Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "3D DRAM is the next-generation memory system targeting high bandwidth, low power, and small form factor. This paper presents a cross-domain CAD/architectural platform that addresses DC power noise issues in 3D DRAM targeting stacked DDR3, Wide I/O, and hybrid memory cube technologies. Our design and analysis include both individual DRAM dies and a host logic die that communicates with them in the same stack. Moreover, our comprehensive solutions encompass all major factors in design, packaging, and architecture domains, including power delivery network wire sizing, redistribution layer routing, distributed, and dedicated TSV placement, die bonding style, backside wire bonding, and read policy optimization. We conduct regression analysis and optimization to obtain high quality solutions under noise, cost, and performance tradeoff. Compared with industry standard baseline designs and policies, our methods achieve up to 68.2% IR-drop reduction and 30.6% performance enhancement.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744819"}, {"primary_key": "4390574", "vector": [], "sparse_vector": [], "title": "A generic representation of CCSL time constraints for UML/MARTE models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The complexity of today's embedded and cyber-physical systems is rapidly increasing and makes the consideration of higher levels of abstraction during the design process inevitable. In this context, the impact of modeling languages such as UML and its profiles such as MARTE is growing. Here, CCSL provides a formal description of timing constraints which have to be enforced on the considered system. This builds the basis for many further design steps and can be used e. g. for checking the consistency of the specification, for code generation, or for proving whether the time constraints have correctly been implemented at lower abstraction levels. However, most of the approaches available thus far usually focus on sole design tasks only -- often even without an explicit consideration of the system's functional behavior. In this work, we are aiming for overcoming this drawback by providing a method to automatically generate a generic representation of a set of clock constraints in terms of a transition relation. Afterwards, the resulting transition relation can easily be utilized for the above mentioned design tasks. A discussion on the applicability of the generic description as well as an exemplary evaluation shows the promise of the proposed generic representation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744775"}, {"primary_key": "4390575", "vector": [], "sparse_vector": [], "title": "Model-based testing of automotive software: some challenges and solutions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automotive software has been growing in size, criticality and complexity with each new generation of vehicles. Testing at the model and code level is an important step in validating the software against various types of defects that may be introduced in the development process. Model based testing (MBT) methodology, paves a road towards automation of testing activities. Test generation is a computationally complex task, which requires efficient constraint solving techniques and some guidance from the test engineer when this task cannot be solved by a tool. At the same time, automatic tools can hardly substitute domain testing experts which can develop more effective tests or at least test fragments than any tool. This is why we believe that future test generation tools should support \"tester-in-the-loop\" MBT approaches. In this paper, we provide a brief report on our results in this direction.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747935"}, {"primary_key": "4390576", "vector": [], "sparse_vector": [], "title": "Design automation challenges for scalable quantum architectures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Building a quantum computer that is sufficiently large for solving classically intractable computational problem instances is a grand challenge of today's science. The main fundamental obstacle to construction of scalable quantum computers is the vulnerability of their extremely fragile components to noise and decoherence due to environment interaction. This paper focuses on design of technology-independent quantum circuit architectures that are scalable and reliable as well as well-suited for physical implementation. The considered architectures will be protected against errors by state-of-the-art topological quantum error-correcting (TQEC) codes which combine a number of properties enabling the best scalability among all discussed alternatives. The theory of TQEC is very mature, the suitable hardware has made significant progress in recent years, and therefore the software challenges constitute the research gaps. We explain the abstraction levels which lay the foundation for systematic, top-down design automation methods for TQEC computers, discuss the appropriate optimization objectives and formulate a number of largely open design automation problems in this fields.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747921"}, {"primary_key": "4390577", "vector": [], "sparse_vector": [], "title": "Generation of close-to-functional broadside tests with equal primary input vectors.", "authors": ["<PERSON><PERSON>"], "summary": "This paper describes a procedure for generating close-to-functional broadside tests for transition faults. Such tests avoid overtesting of transition faults and keep the power dissipation within its functional bounds. The procedure has the following features that are not addressed together by existing procedures. (1) The procedure takes into consideration functional constraints on primary input sequences of a circuit that is embedded in a larger design. (2) It generates close-to-functional broadside tests with a measurable proximity to functional operation conditions. (3) It generates tests with equal primary input vectors that are suitable for embedded circuits as well as low-cost testers. Several experimental observations help in addressing the challenge of generating close-to-functional broadside tests with equal primary input vectors under functional constraints on primary input sequences.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744844"}, {"primary_key": "4390578", "vector": [], "sparse_vector": [], "title": "Task scheduling strategies to mitigate hardware variability in embedded shared memory clusters.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Manufacturing and environmental variations cause timing errors that are typically avoided by conservative design guardbands or corrected by circuit level error detection and correction. These measures incur energy and performance penalties. This paper considers methods to reduce this cost by expanding the scope of variability mitigation through the software stack. In particular, we propose workload deployment methods that reduce the likelihood of timing errors in shared memory clusters of processor cores. This and other methods are incorporated in a runtime layer in the OpenMP framework that enables parsimonious countermeasures against timing errors induced by hardware variability. The runtime system \"introspectively\" monitors the costs of tasks execution on various cores and transparently associates descriptive metadata with the tasks. By utilizing the characterized metadata, we propose several policies that enhance the cluster choices for scheduling tasks to cores according to measured hardware variability and system workload. We devise efficient task scheduling strategies for simultaneous management of variability and workload by exploiting centralized and distributed approaches to workload distribution. Both schedulers surpass current state-of-the-art approaches; the distributed (or the centralized) achieves on average 30% (or 17%) energy, and 17% (4%) performance improvement.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744915"}, {"primary_key": "4390579", "vector": [], "sparse_vector": [], "title": "Detecting malicious modifications of data in third-party intellectual property cores.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Globalization of the system-on-chip (SoC) design flow has created opportunities for rogue elements in the intellectual property (IP) vendor companies to insert malicious circuits (a.k.a. hardware Trojans) into their IPs. We propose to formally verify third party IPs (3PIPs) for unauthorized corruption of critical data such as secret key. Our approach develops properties to identify corruption of critical registers. Furthermore, we describe two attacks where computations can be performed on corrupted data without corrupting the critical register. We develop additional properties to detect such attacks. We validate our technique using Trojans in 8051 and RISC processors and AES designs from Trust-Hub.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744823"}, {"primary_key": "4390580", "vector": [], "sparse_vector": [], "title": "Monolayer transition metal dichalcogenide and black phosphorus transistors for low power robust SRAM design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vertical monolayer heterojunction FETs based on transition metal dichalcogenides (TMDCFETs) and planar black phosphorus FETs (BPFETs) have demonstrated excellent sub-threshold swing, high ION/IOFF, and high scalability, making them attractive candidates for post-CMOS memory design. This paper explores TMDCFET and BPFET SRAM design by combining atomistic self-consistent device modeling with SRAM circuit design and simulation. Our simulations show that at low operating voltages, TMDCFET and BPFET SRAMs exhibit significant advantages in static power, dynamic read/write noise margin, and read/write delay over both nominal and read/write-assisted 16nm CMOS SRAMs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744872"}, {"primary_key": "4390581", "vector": [], "sparse_vector": [], "title": "An efficient algorithm for statistical timing yield optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Statistical timing yield optimization algorithms require computation of yield-gradient for gate resizing in every iteration. Numerical yield-gradients account for the effects of fan-in and fan-out gates, but are computationally expensive. In this paper, we formulate a more accurate analytical expression for the yield-gradient (termed effective yield-gradient) that includes these effects. Based on the statistical properties of the path delay variations, we derive a simplified expression for the effective yield gradient that is accurate and results in an improvement in the run-time. Using these simplified expressions, we also propose an algorithm for resizing multiple gates in an iteration. Results on ITC99 and ISCAS85 benchmarks show that the proposed multi-node resizing algorithm results in 83% improvement in the runtime with an average area penalty of 3% and no cost to the final yield achieved.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744796"}, {"primary_key": "4390582", "vector": [], "sparse_vector": [], "title": "Approximate storage for energy efficient spintronic memories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Spintronic memories are promising candidates for future on-chip storage due to their high density, non-volatility and near-zero leakage. However, the energy consumed by read and write operations presents a major challenge to their use as energy-efficient on-chip memory. Leveraging the ability of many applications to tolerate impreciseness in their underlying computations and data, we explore approximate storage as a new approach to improving the energy-efficiency of spintronic memories. We identify and characterize mechanisms in STT-MRAM bit-cells that provide favorable energy-quality trade-offs, i.e., disproportionate energy improvements at the cost of small probabilities of read/write failures. Based on these mechanisms, we design a quality-configurable memory array in which data can be stored to varying levels of accuracy based on application requirements. We integrate the quality-configurable array as a scratchpad in the memory hierarchy of a programmable vector processor and expose it to software by introducing quality-aware load/store instructions within the ISA. We evaluate the energy benefits of our proposal using a device-to-architecture modeling framework and demonstrate 40% and 19.5% improvement in memory energy and overall application energy respectively, for negligible (< 0.5%) quality loss across a suite of recognition and vision applications.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744799"}, {"primary_key": "4390583", "vector": [], "sparse_vector": [], "title": "Correctness and security at odds: post-silicon validation of modern SoC designs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the conflicts between requirements from security and post-silicon validation in SoC designs. Post-silicon validation requires hardware instrumentations to provide observability and controllability during on-field execution; this in turn makes the system prone to security vulnerabilities, resulting in potentially subtle security exploits. Mitigating such threats while ensuring that the system is amenable to post-silicon validation is challenging, involving close collaboration among security, validation, testing, and computer architecture teams. We examine the state of the practice in this area, the trade-offs and compromises made, and their limitations. We also discuss an emerging approach that we are contemplating to address this problem.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2754896"}, {"primary_key": "4390584", "vector": [], "sparse_vector": [], "title": "Execution-driven parallel simulation of PGAS applications on heterogeneous tiled architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a parallel execution-driven simulator for the efficient simulation of heterogeneous tile-based multi-core architectures. Here, the architecture is composed of several tiles connected via a network-on-chip and each tile contains local memory as well as several possibly different types of compute resources. Partitioned Global Address Space (PGAS) is a programming model matching very well the needs for programming of such modern multi-core architectures. In order to provide performance estimations for parallel software and enable architecture design space exploration, fast functional and timing simulation techniques are required. Thus, we present a simulator that meets this requirement by combining a fast direct-execution simulation approach with different parallelization strategies. Here, we propose four novel parallel discrete-event simulation techniques, which map thread-level parallelism within the applications to core-level parallelism on the target architecture and back to thread-level parallelism on the host machine. In order to achieve this, the correct synchronization and activation of the host threads is necessary being the main focus of this paper. Experiments with parallel real-world applications are used to compare the different techniques against each other and demonstrate that 10.4 times faster simulations than a sequential simulation can be achieved on a 12-core Intel Xeon processor.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744840"}, {"primary_key": "4390585", "vector": [], "sparse_vector": [], "title": "Performance analysis of a memristive crossbar PUF design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Physical unclonable functions (PUF) provide a hardware specific unique signature or finger print for an integrated circuit that can be leveraged to mitigate several security vulnerabilities. A dense memristive crossbar PUF is described which utilizes variations in the write-time of memristors as the primary entropy source. For this work, the write-time varies according to six specific device parameters which can be directly measured from fabricated memristors and easily included in an accurate model for circuit simulation. The results presented show strong statistical performance for the proposed design in terms of entropy, uniqueness and uniformity. Furthermore, the nature of sneak path currents in the crossbar structure are leveraged to provide an exponential number of unique configurations for each response bit. Results also show that the proposed crossbar-based PUF provides improved power consumption and smaller area utilization when compared to CMOS-based and other nanoelectronic PUF circuits.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744892"}, {"primary_key": "4390586", "vector": [], "sparse_vector": [], "title": "OSFA: a new paradigm of gate-sizing for power/performance optimizations under multiple operating conditions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern SoCs and microprocessors, e.g., those in smart phones and laptops, typically have multiple operating conditions, such as video streaming, web browsing, standby, and so on. They will have different performance targets and run under different supply voltages. Gate sizing (with threshold voltage assignment) is a fundamental step for power/performance optimization. However, conventional gate sizing algorithms only consider one scenario, e.g., the performance-critical operating condition, which may be over-design for other operating conditions. In this paper, we present a new paradigm of gate sizing, OSFA (One-Size-Fits-All), which performs power/performance optimizations across multiple operating conditions. Based on OSFA, we also adjust the supply voltage targeting overall power optimization. Experimental results on industry-strength benchmarks demonstrate that compared with conventional approach OSFA could provide an average 6.1% reduction in power without performance loss.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744885"}, {"primary_key": "4390587", "vector": [], "sparse_vector": [], "title": "Highly efficient entropy extraction for true random number generators on FPGAs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "True random number generators are essential components in cryptographic hardware. In this work, a novel entropy extraction method is used to improve throughput of jitter-based true random number generators on FPGA. By utilizing ultra-fast carry-logic primitives available on most commercial FPGAs, we have improved the efficiency of the entropy extraction, thereby increasing the throughput, while maintaining a compact implementation. Design steps and techniques are illustrated on an example of a ring-oscillator based true random number generator on Spartan-6 FPGA. In this design, the required accumulation time is reduced by 3 orders of magnitude compared to the most efficient oscillator-based TRNG on the same FPGA. The presented implementation occupies only 67 slices, achieves a throughput of 14.3 Mbps and it is provided with a formal evaluation of security.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744852"}, {"primary_key": "4390588", "vector": [], "sparse_vector": [], "title": "Security and privacy challenges in industrial internet of things.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today, embedded, mobile, and cyberphysical systems are ubiquitous and used in many applications, from industrial control systems, modern vehicles, to critical infrastructure. Current trends and initiatives, such as \"Industrie 4.0\" and Internet of Things (IoT), promise innovative business models and novel user experiences through strong connectivity and effective use of next generation of embedded devices. These systems generate, process, and exchange vast amounts of security-critical and privacy-sensitive data, which makes them attractive targets of attacks. Cyberattacks on IoT systems are very critical since they may cause physical damage and even threaten human lives. The complexity of these systems and the potential impact of cyberattacks bring upon new threats.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747942"}, {"primary_key": "4390589", "vector": [], "sparse_vector": [], "title": "Dynamically adaptive scrubbing mechanism for improved reliability in reconfigurable embedded systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Commercial off-the-shelf (COTS) reconfigurable devices have been recognized as one of the most suitable processing devices to be applied in satellites, since they can satisfy and combine their most important requirements, namely processing performance, reconfigurability and low cost. However, COTS reconfigurable devices, in particular Static-RAM Field Programmable Gate Arrays (FPGAs), can be affected by cosmic radiation, compromising the overall satellite reliability. Scrubbing has been proposed as a mechanism to repair faults in configuration memory. However, the current scrubbing mechanisms are predominantly static and unable to adapt to run-time variations in applications. In this paper, a dynamically adaptive scrubbing mechanism is proposed. Through a window-based scrubbing scheduling, this mechanism adapts the scrubbing process to the reconfigurations and modifications on the FPGA user-design at runtime. Conducted simulation experiments show the feasibility and the efficiency of the proposed solution in terms of system reliability and memory overhead.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744827"}, {"primary_key": "4390590", "vector": [], "sparse_vector": [], "title": "SmartBalance: a sensing-driven linux load balancer for energy efficiency of heterogeneous MPSoCs.", "authors": ["<PERSON><PERSON>", "T<PERSON>go <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to increased demand for higher performance and better energy efficiency, MPSoCs are deploying heterogeneous architectures with architecturally differentiated core types. However, the traditional Linux-based operating system is unable to exploit this heterogeneity since existing kernel load balancing and scheduling approaches lack support for aggressively heterogeneous architectural configurations (e.g. beyond two core types). In this paper we present SmartBalance: a sensing-driven closed-loop load balancer for aggressively heterogeneous MPSoCs that performs load balancing using a sense-predict-balance paradigm. SmartBalance can efficiently manage the chip resources while opportunistically exploiting the workload variations and performance-power trade-offs of different core types. When compared to the standard vanilla Linux kernel load balancer, our per-thread and per-core performance-power-aware scheme shows an improvement in energy efficiency (throughput/Watt) of over 50% for benchmarks from the PARSEC benchmark suite executing on a heterogeneous MPSoC with 4 different core types and over 20% w.r.t. state-of-the-art ARM's global task scheduling (GTS) scheme for octa-core big.Little architecture.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744911"}, {"primary_key": "4390591", "vector": [], "sparse_vector": [], "title": "Automating design-space exploration: optimal deployment of automotive SW-components in an ISO26262 context.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With a growing demand for complex, safety-critical features in automotive vehicles, functional safety is a key issues of automotive software development. Consequently, standards like ISO26262 propose methods and techniques for the systematic development of automotive software. Furthermore, the growing amount of functionality -- including active safety systems or automated driver assistance functions -- on the control of the vehicle dynamics and the correspondingly used more powerful electronic platforms requires methods supporting the development of systems in an increasingly complex design space. In this contribution, an approach is presented that supports the allocation of software functions to hardware elements in an automated fashion, respecting the separation constraints concerning assurances levels.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747912"}, {"primary_key": "4390592", "vector": [], "sparse_vector": [], "title": "Formal methods for semi-autonomous driving.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give an overview of the main challenges in the specification, design, and verification of human cyber-physical systems, with a special focus on semi-autonomous vehicles. We identify unique characteristics of formal modeling, specification, verification and synthesis in this domain. Some initial results and design principles are presented along with directions for future work.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747927"}, {"primary_key": "4390593", "vector": [], "sparse_vector": [], "title": "PRES: pseudo-random encoding scheme to increase the bit flip reduction in the memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Nonvolatile memory technologies such as Phase Change Memory (PCM) and Spin-Transfer Torque Random Access Memory (STT-RAM) are emerging as promising replacements to DRAM. Before deploying STT-RAM and PCM into functional systems, a number of challenges still remain. Specifically, both require relatively high write energy, STT-RAM suffers from high bit error rates and PCM suffers from low endurance. A common solution to overcome those challenges is to minimize the number of bits changed per write. In this work, we introduce Pseudo-Random Encoding Scheme (PRES) to minimize the number of bit changes during memory writes. PRES maps the write data vector into an intermediate highly random set of data vectors. Subsequently, the intermediate data vector that yields the least number of differences when compared to the currently stored data is selected. Our evaluation shows that PRES reduces bit flips by up to 25% over a baseline differential writing scheme. Further, PRES reduces bit flips by 15% over the leading bit-flip minimization scheme, while decreasing encoding and decoding complexities by more than 90%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2755440"}, {"primary_key": "4390594", "vector": [], "sparse_vector": [], "title": "A low latency generic accuracy configurable adder.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High performance approximate adders typically comprise of multiple smaller sub-adders, carry prediction units and error correction units. In this paper, we present a low-latency generic accuracy configurable adder to support variable approximation modes. It provides a higher number of potential configurations compared to state-of-the-art, thus enabling a high degree of design flexibility and trade-off between performance and output quality. An error correction unit is integrated to provide accurate results for cases where high accuracy is required. Furthermore, an associated scheme for error probability estimation allows convenient comparison of different approximate adder configurations without requiring the need to numerically simulate the adder. Our experimental results validate the developed error model and also the lower latency of our generic accuracy configurable adder over state-of-the-art approximate adders. For functional verification and prototyping, we have used a Xilinx Virtex-6 FPGA. Our adder model and synthesizable RTL are made open-source.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744778"}, {"primary_key": "4390595", "vector": [], "sparse_vector": [], "title": "EnAAM: energy-efficient anti-aging for on-chip video memories.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Negative Biased Temperature Instability-induced aging has emerged as one of the critical reliability threats in the nano-era. In this paper, we propose a microarchitectural-level technique for mitigating aging of on-chip SRAM-based memories. The goal is to achieve balanced aging of all memory cells at minimal energy overhead, leading to a longer lifetime. For a configurable and energy-efficient design, we perform aging and energy analysis of different aging balancing circuits. This analysis is leveraged to design a novel energy-efficient anti-aging memory architecture. Our architecture employs a configurable anti-aging controller that leverages the data characteristics to dynamically select (1) at what time instant the aging balancing circuit should be activated, and (2) on which SRAM cells aging balancing should be applied. Our experiments demonstrate significant aging improvements while providing up to 30% energy savings.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744834"}, {"primary_key": "4390596", "vector": [], "sparse_vector": [], "title": "Ensuring functional safety compliance for ISO 26262.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we describe the verification flow to ensure safety compliance for ISO 26262 focusing on digital simulation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747924"}, {"primary_key": "4390597", "vector": [], "sparse_vector": [], "title": "Security aware network controllers for next generation automotive embedded systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern cars incorporate complex distributed computing systems that manage all aspects of vehicle dynamics, comfort, and safety. Increased automation has demanded more complex networking in vehicles, that now contain a hundred or more compute units. As these networks were developed as silos, little attention was given to security early on. However, this has become a key challenge in the automotive domain, as these systems have been shown to be susceptible to various attacks, with sometimes catastrophic consequences. Addressing security in such systems requires consideration of the network and compute units, both hardware and software, and complex real-time constraints. We present an approach that integrates application authentication, message encryption and network access control into a smart network interface, without compromising network determinism. A custom interface with partial reconfiguration support on FPGAs enables seamless integration of security at the interface, offering a level of security not possible with standard layered approaches.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744907"}, {"primary_key": "4390598", "vector": [], "sparse_vector": [], "title": "Compacting privacy-preserving k-nearest neighbor search using logic synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "Siam <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces the first efficient, scalable, and practical method for privacy-preserving k-nearest neighbors (k-NN) search. The approach enables performing the widely used k-NN search in sensitive scenarios where none of the parties reveal their information while they can still cooperatively find the nearest matches. The privacy preservation is based on the Yao's garbled circuit (GC) protocol. In contrast with the existing GC approaches that only accept function descriptions as combinational circuits, we suggest using sequential circuits. This work introduces novel transformations, such that the sequential description can be evaluated by interfacing with the existing GC schemes that only accept combinational circuits. We demonstrate a great efficiency in the memory required for realizing the secure k-NN search. The first-of-a-kind implementation of privacy preserving k-NN, utilizing the Synopsys Design Compiler on a conventional Intel processor demonstrates the applicability, efficiency, and scalability of the suggested methods.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744808"}, {"primary_key": "4390599", "vector": [], "sparse_vector": [], "title": "A timing graph based approach to mode merging.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With shrinking technologies and increasing design complexities, it is common to have a large number of modes (functional, scan, test and so on) and corners (PVT device and interconnect). This leads to an explosion in the number of scenarios (#modes × #corners) that need to be validated for timing. While multiple tactics are required to handle this problem, one essential way to address this is by reducing the number of modes by merging individual modes into superset modes. However, with the overriding necessity to maintain sign-off accuracy, mode merging with high merge-factor is very complex. In this paper, we propose a novel automated timing graph based approach to mode merging that is designed to meet these requirements. By construction, there is an inbuilt validation that the merged constraints correctly model the intent of original constraints. This technology is tested on large industrial designs and the results are provided.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744787"}, {"primary_key": "4390600", "vector": [], "sparse_vector": [], "title": "Nanowire-aware routing considering high cut mask complexity.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One-dimensional nanowires are one of the most promising next-generation lithography technologies for 7 nm process node and beyond. The 1D nanowire process first constructs a 1D nanoarray through template synthesis followed by line-end cutting with additional cut masks. To achieve better yield and manufacturability, the cut patterns shall satisfy specified restricted design rules, and thus it is desirable to develop a novel routing methodology to better address the challenges arising from cut patterns. In this paper, we propose the first nanowire-aware routing system, called NWR, considering high cut-mask complexity based on a two-pass, bottom-up multilevel routing framework. Experimental results show that our nanowire-aware router can effectively and efficiently reduce cut numbers, cut spacing violations, and line-end extension length.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744874"}, {"primary_key": "4390601", "vector": [], "sparse_vector": [], "title": "A synthesis methodology for application-specific logic-in-memory designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "For deeply scaled digital integrated systems, the power required for transporting data between memory and logic can exceed the power needed for computation, thereby limiting the efficacy of synthesizing logic and compiling memory independently. Logic-in-Memory (LiM) architectures address this challenge by embedding logic within the memory block to perform basic operations on data locally for specific functions. While custom smart memories have been successfully constructed for various applications, a fully automated LiM synthesis flow enables architectural exploration that has heretofore not been possible. In this paper we present a tool and design methodology for LiM physical synthesis that performs co-design of algorithms and architectures to explore system level trade-offs. The resulting layouts and timing models can be incorporated within any physical synthesis tool. Silicon results shown in this paper demonstrate a 250x performance improvement and 310x energy savings for a data-intensive application example.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744786"}, {"primary_key": "4390602", "vector": [], "sparse_vector": [], "title": "Design tool chain for cyber-physical systems: lessons learned.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xenofon D<PERSON>", "<PERSON>"], "summary": "Design automation tools evolved to support the principle of \"separation of concerns\" to manage engineering complexity. Accordingly, we find tool suites that are vertically integrated with limited support (even intention) for horizontal integratability (i.e. integration across disciplinary boundaries). CPS challenges these established boundaries and with this - market conditions. The question is how to facilitate reorganization and create the foundation and technologies for composable CPS design tool chains that enables reuse of existing commercial and open source tools? In this paper we describe some of the lessons learned in the design and implementation of a design automation tool suite for complex cyber-physical systems (CPS) in the vehicle domain. The tool suite followed a model- and component-based design approach to match the significant increase in design productivity experienced in several narrowly focused homogeneous domains, such as signal processing, control and aspects of electronic design. The primary challenge in the undertaking was the tremendous heterogeneity of complex cyber-physical systems (CPS), where such as vehicles has not yet been achieved. This paper describes some of the challenges addressed and solution approaches to building a comprehensive design tool suite for complex CPS.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747922"}, {"primary_key": "4390603", "vector": [], "sparse_vector": [], "title": "On using control signals for word-level identification in a gate-level netlist.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work tackles the problem of reverse engineering a gate-level netlist in order to identify groups of wires corresponding to words. It serves as the major step to find high-level modules and analyze their correct functionality in the presence of Hardware Trojans. Our core idea is to find and utilize control signals to more effectively identify words. Specifically, modern designs provide ample opportunities because they contain numerous control signals which are automatically inserted by the CAD tools. But finding control signals is itself an unresolved challenge. We propose a procedure to identify words which at its core finds and utilizes a small subset of relevant control signals by exploiting partial structural similarity. In our experiments, we show the effectiveness of our procedure by showing a high number of identified words with high accuracy using many benchmarks with already-identified words as the reference case.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744878"}, {"primary_key": "4390604", "vector": [], "sparse_vector": [], "title": "Physically aware high level synthesis design flow.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High Level Synthesis (HLS) has many productivity advantages over traditional RTL design, but routing congestion is difficult to resolve due to the lack of physical information in HLS. In this paper we propose a novel design flow by integrating a HLS tool with physically aware logic synthesis technology. Using this approach, one can discover congestion problems early and trace their sources to specific parts of the input SystemC models. This allows designers to resolve the congestion problems before going to the layout design phase. We applied this flow to a large-scale HLS production design with results showing that this flow can significantly improve not only routing congestion but design area and timing as well.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744893"}, {"primary_key": "4390605", "vector": [], "sparse_vector": [], "title": "ElasticCore: enabling dynamic heterogeneity with joint core and voltage/frequency scaling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Heterogeneous architectures have emerged as a promising solution to enhance energy-efficiency by allowing each application to run on a core that matches resource needs more closely than a one-size-fits-all core. In this paper, an ElasticCore platform is described where core resources along with the operating voltage and frequency settings are scaled to match the application behavior at run-time. Furthermore, a linear regression model for power and performance prediction is used to guide the scaling of the core size and the operating voltage and frequency to maximize efficiency. Circuit considerations that further optimize the power efficiency of ElasticCore are also considered. Specifically, the efficiency of both off-chip and on-chip voltage regulators is analyzed for the heterogeneous architecture where the required load current changes dynamically at run-time. A distributed on-chip voltage regulator topology is proposed to accommodate the heterogeneous nature of the ElasticCore. The results indicate that ElasticCore on average achieves close to a 96% efficiency as compared to an architecture implementing the Oracle predictor where the application behavior is perfectly matched at run-time. Moreover, the proposed architecture is 30% more energy-efficient as compared to the BigLitte architecture.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744833"}, {"primary_key": "4390606", "vector": [], "sparse_vector": [], "title": "Revisiting accelerator-rich CMPs: challenges and solutions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Heterogeneous Chip Multiprocessors (CMP)s, which combine processor cores with specialized HW accelerators, are one main approach to high-performance low-power computing. While it is promising for few accelerators, the scalability is a major challenge with increasing number of accelerators. Resources including memory, communication fabric and processor turn into bottlenecks and result in accelerator under-utilization and cripple the performance.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744902"}, {"primary_key": "4390607", "vector": [], "sparse_vector": [], "title": "One-pass logic synthesis for graphene-based Pass-XNOR logic circuits.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Electrostatically controlled graphene P-N junctions are devices built on a single layer graphene sheet that can be turned-ON/OFF via external potential difference. Their electrical behavior resembles a CMOS transmission gate with an embedded XNOR Boolean functionality. Recent works presented an efficient design style, the Pass-XNOR logic (PXL), which allows the implementation of adiabatic logic circuits with ultra low-power features.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744880"}, {"primary_key": "4390608", "vector": [], "sparse_vector": [], "title": "Improving formal timing analysis of switched ethernet by exploiting FIFO scheduling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ethernet is an emerging technology in the automotive domain and is capable to overcome the bandwidth and scalability limits of traditional buses like CAN or FlexRay. Formal performance analysis methods are required to verify the timing, e.g. by providing upper bounds on end-to-end latencies, in safety-critical real-time systems, such as automotive control and advanced driver assistance systems. In many real-time capable Ethernet implementations such as IEEE 802.1Q or AVB, frames can be prioritized and frames of equal priority are scheduled in FIFO order at the switch ouput ports. In this paper, we show how to exploit Ethernet's FIFO scheduling in a compositional formal performance analysis to derive tighter timing guarantees. In an automotive Ethernet setup, our proposed analysis leads to a significant reduction in end-to-end latency guarantees.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744854"}, {"primary_key": "4390609", "vector": [], "sparse_vector": [], "title": "In-circuit temporal monitors for runtime verification of reconfigurable designs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present designs for in-circuit monitoring of custom hardware designs implemented in reconfigurable hardware. The monitors check hardware designs against temporal logic specifications. Compared to previous work, which uses custom hardware to monitor software, our designs can run at higher speeds and make better use of hardware resources, such as shift registers and embedded memory blocks. We evaluate our monitor circuits on example hardware designs targeting FPGA implementation, showing that they have low overhead in terms of circuit area, and can run at the same speed as the circuits they monitor.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744856"}, {"primary_key": "4390610", "vector": [], "sparse_vector": [], "title": "Interleaved multi-bank scratchpad memories: a probabilistic description of access conflicts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shared on-chip memory is common on state-of-the-art multicore platforms. In a number of designs, memory throughput is enhanced by providing multiple independent memory banks and spreading consecutive memory addresses to these (interleaving). This can reduce, but not eliminate, the number of access conflicts. In this paper, we statically analyse the probabilities and frequencies of these access conflicts and calculate the expected throughput for various hardware configurations and software applications. Using two techniques -- the classic occupancy distribution and a Markov model -- we are able to explain most of the underlying conflict mechanisms and to provide accurate estimations. We present the practical consequences for hardware and software design and establish an intuitive understanding of the characteristics of interleaved memory architectures.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744861"}, {"primary_key": "4390611", "vector": [], "sparse_vector": [], "title": "Energy-efficient non-volatile TCAM search engine design using priority-decision in memory technology for DPI.", "authors": ["Hsiang<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tien-Fu Chen"], "summary": "TCAM-based search engines are widely used in regular expression matching across multiple packets. However, the use of priority encoder results in increased energy consumption of pattern updates and search operations. This work, proposes a promising memory technology, called Priority-Decision in Memory (PDM), which eliminates the need for priority encoders and removes restrictions on ordering, meaning that patterns can be stored in an arbitrary order without sorting their lengths. Moreover, we present a Sequential Input-State Search (SIS) scheme to disable the mass of redundant search operations in state segments, based on the analysis distribution of hex signatures in a virus database. Experimental results demonstrate that PDM-based technology can improve update energy consumption of nvTCAM search engines by 36%~67% because most of the energy in the latter is used to reorder. By adopting the SIS-based method to avoid unnecessarily search operations in a TCAM array, the search energy reduction is around 64% of nvTCAM search engines.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744836"}, {"primary_key": "4390612", "vector": [], "sparse_vector": [], "title": "Reliability-aware synthesis for flow-based microfluidic biochips by dynamic-device mapping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "On flow-based biochips, valves that are used to form peristaltic pumps wear out much earlier than valves for transportation since the former are actuated more often, which leads to a reduced lifetime of the chip. In this paper, we introduce a valve-role-changing concept to avoid always using the same valves for peristalsis. Based on this, we generate dynamic devices from a valve-centered architecture to distribute the valve actuation activities evenly and reduce the largest number of valve actuations with even fewer valves. In addition, we propose in situ on-chip storages, which can overlap with other devices, so that less area is needed compared with dedicated storages on traditional chips. Moreover, our method provides good support for assays requiring different volumes and ratios of samples. Experiments show that compared with traditional designs, the largest number of valve actuations can be reduced by 72.97% averagely, while the number of valves is reduced by 10.62%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744899"}, {"primary_key": "4390613", "vector": [], "sparse_vector": [], "title": "b-HiVE: a bit-level history-based error model with value correlation for voltage-scaled integer and floating point units.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "Seda Ogrenci <PERSON>mi<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Existing timing error models for voltage-scaled functional units ignore the effect of history and correlation among outputs, and the variation in the error behavior at different bit locations. We propose b-HiVE, a model for voltage-scaling-induced timing errors that incorporates these attributes and demonstrates their impact on the overall model accuracy. On average across several operations, b-HiVE's estimation is within 1--3% of comprehensive analog simulations, which corresponds to 5--17x higher accuracy (6--10x on average) than error models currently used in approximate computing research. To the best of our knowledge, we present the first bit-level error models of arithmetic units, and the first error models for voltage scaling of bitwise logic operations and floating-point units.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744805"}, {"primary_key": "4390614", "vector": [], "sparse_vector": [], "title": "An efficient algorithm for frequency-weighted balanced truncation of VLSI interconnects in descriptor form.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Balanced truncation of descriptor systems requires computation of spectral projectors and solution of the generalized projected <PERSON><PERSON><PERSON><PERSON> equations, both of which have significant complexity. Frequency-weighted balancing methods are more efficient if the response over a specific frequency range is desired. However, a direct extension of these methods to descriptor systems requires the spectral projectors. In this paper, we propose an efficient frequency-weighted balanced truncation algorithm without finding the spectral projectors. Samples of the frequency-domain solution to the system are used to get an accurate estimate of the improper Gramians. The proper Gramians are computed after adjusting for the contribution of the improper subsystem. Low rank factors of these Gramians are used to obtain a basis that includes the contribution of both the proper and improper subsystems. Congruence transform is used to ensure passivity of RLC interconnect models. Results for standard benchmarks show that the method is accurate and efficient.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744770"}, {"primary_key": "4390615", "vector": [], "sparse_vector": [], "title": "Battery lifetime-aware automotive climate control for electric vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Electric Vehicle (EV) optimization involves stringent constraints on driving range and battery lifetime. Sophisticated embedded systems and huge number of computing resources have enabled researchers to implement advanced Battery Management Systems (BMS) for optimizing the driving range and battery lifetime. However, the Heating, Ventilation, and Air Conditioning (HVAC) control and BMS have not been considered together in this optimization. This paper presents a novel automotive climate control methodology that manages the HVAC power consumption to improve the battery lifetime and driving range. Our experiments demonstrate that the HVAC consumption is considerable and flexible in an EV which significantly influences the driving range and battery lifetime. Hence, this influence on the above-mentioned constraints has been modeled and analyzed precisely, then it has been considered thoroughly in the EV optimization process. Our methodology provides significant improvement in battery lifetime (on average 14%) and average power consumption (on average 39% reduction) compared to the state-of-the-art methodologies.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744804"}, {"primary_key": "4390616", "vector": [], "sparse_vector": [], "title": "Approximate computing and the quest for computing efficiency.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Diminishing benefits from technology scaling have pushed designers to look for new sources of computing efficiency. Multicores and heterogeneous accelerator-based architectures are a by-product of this quest to obtain improvements in the performance of computing platforms at similar or lower power budgets. In light of the need for new innovations to sustain these improvements, we discuss approximate computing, a field that has attracted considerable interest over the last decade. While the core principles of approximate computing---computing efficiently by producing results that are good enough or of sufficient quality---are not new and are shared by many fields from algorithm design to networks and distributed systems, recent e.orts have seen a percolation of these principles to all layers of the computing stack, including circuits, architecture, and software. Approximate computing techniques have also evolved from ad hoc and applicationspecific to more broadly applicable, supported by systematic design methodologies. Finally, the emergence of workloads such as recognition, mining, search, data analytics, inference and vision are greatly increasing the opportunities for approximate computing. We describe the vision and key principles that have guided our work in this area, and outline a holistic cross-layer framework for approximate computing.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2751163"}, {"primary_key": "4390617", "vector": [], "sparse_vector": [], "title": "Scalable-effort classifiers for energy-efficient machine learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Supervised machine-learning algorithms are used to solve classification problems across the entire spectrum of computing platforms, from data centers to wearable devices, and place significant demand on their computational capabilities. In this paper, we propose scalable-effort classifiers, a new approach to optimizing the energy efficiency of supervised machine-learning classifiers. We observe that the inherent classification difficulty varies widely across inputs in real-world datasets; only a small fraction of the inputs truly require the full computational effort of the classifier, while the large majority can be classified correctly with very low effort. Yet, state-of-the-art classification algorithms expend equal effort on all inputs, irrespective of their difficulty. To address this inefficiency, we introduce the concept of scalable-effort classifiers, or classifiers that dynamically adjust their computational effort depending on the difficulty of the input data, while maintaining the same level of accuracy. Scalable effort classifiers are constructed by utilizing a chain of classifiers with increasing levels of complexity (and accuracy). Scalable effort execution is achieved by modulating the number of stages used for classifying a given input. Every stage in the chain contains an ensemble of biased classifiers, where each biased classifier is trained to detect a single class more accurately. The degree of consensus between the biased classifiers' outputs is used to decide whether classification can be terminated at the current stage or not. Our methodology thus allows us to transform any given classification algorithm into a scalable-effort chain. We build scalable-effort versions of 8 popular recognition applications using 3 different classification algorithms. Our experiments demonstrate that scalable-effort classifiers yield 2.79x reduction in average operations per input, which translates to 2.3x and 1.5x improvement in energy for hardware and software implementations, respectively.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744904"}, {"primary_key": "4390618", "vector": [], "sparse_vector": [], "title": "Design and integration challenges of building security hardware IP.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traditional IP design faces challenges imposed by performance requirements, power and area budgets, and cost and time-to-market limitations. The design, verification, and integration of security IP introduces additional challenges due to the need to protect secure keys both within the design and during the product lifecycle. This paper highlights many of these security-specific challenges and describes strategies for addressing them.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747919"}, {"primary_key": "4390619", "vector": [], "sparse_vector": [], "title": "ProPRAM: exploiting the transparent logic resources in non-volatile memory for near data computing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging highly-parallel and big data applications have renewed the research interest in Processing-in-Memory (PIM) architectures. However, moving powerful processing unit into the CMOS-incompatible DRAM chips is not cost-effective for large capacity memory. In this work, we observe that Non-Volatile Memory is often naturally incorporated with basic logics like Data Comparison Write or Flip-n-Write modules that are essential for cell SET/REST operation. In contrast to other conventional PIM or Near Data Computing (NDC) architectures, ProPRAM, as a typical Active NVM, abandons the design approach of moving accelerators or customized processors into memory devices, but begins with exploiting the existing resources inside the memory chips to accelerate the key non-compute-intensive functions for emerging big data applications. With slight hardware and architectural modification, we succeed to expose the transparent peripheral logics to the application layer through instruction set extension and exploit them for in-field bulk data processing with limited hardware cost. Compared to conventional CPU-centric systems, ProPRAM achieves an excellent optimization on energy-efficiency (15x) for important data-intensive micro-benchmarks and kernels.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744896"}, {"primary_key": "4390620", "vector": [], "sparse_vector": [], "title": "RADAR: a case for retention-aware DRAM assembly and repair in future FGR DRAM memory.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Refresh operations consume substantial energy and bandwidth in high-density DRAM memory. To cope with this issue, Fine-Grained Refresh (FGR) is recently proposed to eliminate unnecessary refresh operations caused by minor weak cells. Even JEDEC's DDR4 DRAM specification announces the support of FGR to make DRAM refresh more scalable. Unfortunately, we observe that the effectiveness of FGR is greatly confined by the procedure of refresh-oblivious device integration because all memory devices within a module have to be controlled and refreshed in a lockstep way after the step of assembly. In this work, we firstly propose to intelligently integrate the \"compatible\" devices through a pre-assembly testing and retention-aware matching method. Second, we reuse the reconfiguration structure from yield-oriented remapping mechanism in memory chips and propose Microfix to create a balanced distribution of retention time in memory banks through fine-grained row-address tuning. With this optimization architecture, RADAR, we can eliminate the refresh overhead of produced memory modules by 28% on average.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744897"}, {"primary_key": "4390621", "vector": [], "sparse_vector": [], "title": "Adaptive compressed sensing architecture in wireless brain-computer interface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless sensor nodes advance the brain-computer interface (BCI) from laboratory setup to practical applications. Compressed sensing (CS) theory provides a sub-Nyquist sampling paradigm to improve the energy efficiency of electroencephalography (EEG) signal acquisition. However, EEG is a structure-variational signal with time-varying sparsity, which decreases the efficiency of compressed sensing. In this paper, we present a new adaptive CS architecture to tackle the challenge of EEG signal acquisition. Specifically, we design a dynamic knob framework to respond to EEG signal dynamics, and then formulate its design optimization into a dynamic programming problem. We verify our proposed adaptive CS architecture on a publicly available data set. Experimental results show that our adaptive CS can improve signal reconstruction quality by more than 70% under different energy budgets while only consuming 187.88 nJ/event. This indicates that the adaptive CS architecture can effectively adapt to the EEG signal dynamics in the BCI.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744792"}, {"primary_key": "4390622", "vector": [], "sparse_vector": [], "title": "Selective restore: an energy efficient read disturbance mitigation scheme for future STT-MRAM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "STT-MRAM (Spin-Transfer Torque Magnetic RAM) has recently emerged as one of the most promising memory technologies for constructing large capacity last level cache (LLC) of low power mobile processors. With fast technology scaling, STT-MRAM read operations will become destructive such that post-read restores are inevitable to ensure data reliability. However, frequent restores introduce large energy overheads. In this paper, we propose Selective Restore (SR), an energy efficient scheme to mitigate the restore overheads. Given a L2 cacheline disturbed from a read operation, SR postpones its restore till the cacheline being evicted from the upper level cache L1. Based on the status of the line at the eviction time, SR selectively restores the disturbed cells to achieve energy efficiency. Our experimental results show that SR improves system performance by 5% and reduces dynamic energy consumption by 62%.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744908"}, {"primary_key": "4390623", "vector": [], "sparse_vector": [], "title": "Exploit imbalanced cell writes to mitigate write disturbance in dense phase change memory.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent studies have shown that Phase Change Memory faces significant write disturbance (WD) when scaling in deep submicron regime, i.e., resetting a cell may disturb the values of its adjacent cells if these cells are in amorphous state. A preventive approach to mitigate WD errors is to allocate sufficient inter-cell thermal band. However, this approach greatly reduces chip capacity due to low cell density. A cost effective approach VnC (verify-and-correct), relies on Verification after each write and Correction if errors do happen. Simple VnC improves chip capacity but introduces large performance degradation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744841"}, {"primary_key": "4390624", "vector": [], "sparse_vector": [], "title": "Joint automatic control of the powertrain and auxiliary systems to enhance the electromobility in hybrid electric vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Autonomous driving has become a major goal of automobile manufacturers and an important driver for the vehicular technology. Hybrid electric vehicles (HEVs), which represent a trade-off between conventional internal combustion engine (ICE) vehicles and electric vehicles (EVs), have gained popularity due to their high fuel economy, low pollution, and excellent compatibility with the current fossil fuel dispensing and electric charging infrastructures. To facilitate autonomous driving, an autonomous HEV controller is needed for determining the power split between the powertrain components (including an ICE and an electric motor) while simultaneously managing the power consumption of auxiliary systems (e.g., air-conditioning and lighting systems) such that the overall electromobility is enhanced. Certain (partial) prior knowledge of the future driving profile is useful information for the automatic HEV control. In this paper, methods for predicting driving profile characteristics to enhance HEV power control are first presented. Based on the prediction results and the observed HEV system state (e.g. velocity, battery state-of-charge, propulsion power demand), we propose a reinforcement learning method to determine the power source split between the ICE and electric motor while also controlling the power consumptions of the air-conditioning and lighting systems in the automobile. Experimental results demonstrate significant improvement in the overall HEV system efficiency.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747933"}, {"primary_key": "4390625", "vector": [], "sparse_vector": [], "title": "Novel power grid reduction method based on L1 regularization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Xinyang Yi", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model order reduction exploiting the spectral properties of the admittance matrix, known as the graph Laplacian, to control the approximation accuracy is a promising new class of approaches to power grid analysis. In this paper we introduce a method that allows a dramatic increase in the resulting graph sparsity and can handle large dense input graphs. The method is based on the observation that the information about the realistic ranges of port currents can be used to significantly improve the resulting graph sparsity. In practice, port currents cannot vary unboundedly and the estimates of peak currents are often available early in the design cycle. However, the existing methods including the sampling-based spectral sparsification approach [11] cannot utilize this information.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744877"}, {"primary_key": "4390626", "vector": [], "sparse_vector": [], "title": "Acceleration of control flows on reconfigurable architecture with a composite method.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Control-intensive kernels are becoming the bottleneck that limits the performance of Coarse-Grained Reconfigurable Architecture. Some methods, such as predicated execution, speculative execution, and dual-issue-single-execution, have been proposed to alleviate this problem. But they cannot be always efficient for various control flows. This paper proposes a new architecture, which combines the techniques of triggered instruction and parallel condition, in order to solve the problem completely. The architecture utilizes the basic framework of the triggered instruction to avoid over-serialized execution and branch instruction. Meanwhile, it takes the mechanism of the parallel condition to explore the parallelism between predicate and compute instructions without reconciliation operations. The mechanism of executing multiple instructions that have internal control dependence in parallel is discussed as well. The experiment result shows that the proposed architecture can achieve 20.9% to 140.0% higher performance than that of triggered instruction architecture in terms of cycle count.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744789"}, {"primary_key": "4390627", "vector": [], "sparse_vector": [], "title": "Design tools for oscillator-based computing systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, general-purpose computing schemes have been proposed that use phase relationships to represent Boolean logic levels and employ self-sustaining nonlinear oscillators as latches and registers. Such phase-based systems have superior noise immunity relative to traditional level-encoded logic, hence are of interest for next-generation computing using nanodevices. However, the design of such systems poses special challenges for existing tools. We present a suite of techniques and tools that provide designers with efficient simulation and convenient visualization facilities at all stages of phase logic system design. We demonstrate our tools through a case study of the design of a phase logic finite state machine (FSM). We build this FSM and validate our design tools and processes against measurements. Our plan is to release our tools to the community in open source form.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744818"}, {"primary_key": "4390628", "vector": [], "sparse_vector": [], "title": "Randomness meets feedback: stochastic implementation of logistic map dynamical system.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic Computing (SC) is a digital computation approach that operates on random bit streams to perform complex tasks with much smaller hardware footprint compared to conventional approaches that employ binary radix. For stochastic logic to work, the input random bit streams have to be independent, which is a challenge when implementing system with feedback: outputs that are generated based on input bit streams would be correlated to those streams and cannot be readily combined as inputs to stochastic logic for another iteration of the function. We propose re-randomization techniques for stochastic computing and use the Logistic Map x → r x(1-x) as a case study for dynamical systems in general. We show that complex behaviors such as period-doubling and chaos do indeed occur in digital logic with only a few gates operating on a few 0's and 1's. We employ a number of techniques such as random number generator sharing and using table-lookup pre-computations to significantly reduce the total energy of the computation. Compared to the conventional binary approach, we achieve between 8% and 25% energy consumption.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744898"}, {"primary_key": "4390629", "vector": [], "sparse_vector": [], "title": "System simulation from operational data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ilge Akkaya"], "summary": "System simulation is a valuable tool to unveil inefficiencies and to test new strategies when implementing and revising systems. Often, simulations are parameterized using offline data and heuristic knowledge. Operational data, i.e., data gained through experimentation and observation, can greatly improve the fidelity between the actual system and the simulation. In a traffic scenario, for example, different road conditions or vehicle types can impact the outcome of the simulation and have to be considered during the modeling stage. This paper proposes using machine learning techniques to generate high fidelity simulation models. A traffic simulation case study exemplifies this approach by generating a model for the SUMO traffic simulator from vehicular telemetry data.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747944"}, {"primary_key": "4390630", "vector": [], "sparse_vector": [], "title": "An EDA framework for large scale hybrid neuromorphic computing systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In implementations of neuromorphic computing systems (NCS), memristor and its crossbar topology have been widely used to realize fully connected neural networks. However, many neural networks utilized in real applications often have a sparse connectivity, which is hard to be efficiently mapped to a crossbar structure. Moreover, the scale of the neural networks is normally much larger than that can be offered by the latest integration technology of memristor crossbars. In this work, we propose AutoNCS -- an EDA framework that can automate the NCS designs that combine memristor crossbars and discrete synapse modules. The connections of the neural networks are clustered to improve the utilization of the memristor elements in crossbar structures by taking into account the physical design cost of the NCS. Our results show that AutoNCS can substantially enhance the utilization efficiency of memristor crossbars while reducing the wirelength, area and delay of the physical designs of the NCS.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744795"}, {"primary_key": "4390631", "vector": [], "sparse_vector": [], "title": "What don&apos;t we know about CPS architectures?", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper considers the challenges in the architectural design of cyber-physical systems (CPS). Cyber-physical systems are real-time control and coordination systems that rely on computational infrastructure. We help to elucidate these challenges by comparing cyber-physical system design to system-on-chip design. We then survey CPS architectures and identify several important challenges.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747950"}, {"primary_key": "4390632", "vector": [], "sparse_vector": [], "title": "Layout optimization and template pattern verification for directed self-assembly (DSA).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Guo", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "H.<PERSON><PERSON><PERSON>"], "summary": "Recently, block copolymer directed self-assembly (DSA) has demonstrated great advantages in patterning contacts/vias for the 7 nm technology node and beyond. The high throughput and low process cost of DSA makes it the most promising candidate in patterning tight pitched dense patterns for the next generation lithography. Since DSA is very sensitive to the shapes and distributions of the guiding templates, it is necessary to develop new EDA algorithms and tools to address the patterning rules and constraints of the process. This paper presents a set of DSA-aware optimization techniques targeting the most urgent problems for DSA technology, including layout optimization and template pattern verification.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747934"}, {"primary_key": "4390633", "vector": [], "sparse_vector": [], "title": "Jump test for metallic CNTs in CNFET-based SRAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Naifeng Jing", "Li <PERSON>"], "summary": "SRAMs built on Carbon Nanotube Field Transistors (CNFET) are promising alternatives to conventional CMOS-based SRAMs, due to their advantages in terms of both power consumption and noise margin. However, non-ideal Carbon Nanotube (CNT) fabrication process generates metallic-CNTs (m-CNTs) along with semiconductor-CNTs (s-CNTs), rendering correlated faulty cells along the growth direction of the m-CNTs. Based on this phenomenon, we propose a novel testing algorithm for detecting m-CNTs, wherein consecutive write and read operations jump over multiple cells rather than marching through each and every cell, thereby significantly reducing the testing cost. The proposed jump test can be invoked before the march test to screen out those CNFET-SRAMs doomed to failure, and this can reduce the subsequent test overhead. Experimental results show that the proposed solution is able to achieve a high fault coverage with much less testing cost.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744864"}, {"primary_key": "4390634", "vector": [], "sparse_vector": [], "title": "Fixing the broken time machine: consistency-aware checkpointing for energy harvesting powered non-volatile processor.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jingtong Hu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Energy harvesting has become a favorable alternative to batteries for wearable embedded systems since it is more environmental and user friendly. However, harvested energy is intrinsically unstable, which could frequently interrupt a processor's execution. To tackle this problem, non-volatile processors have been proposed to checkpoint the whole volatile processor state into attached non-volatile memories periodically. When power resumes, the processor can copy the checkpointed state back to volatile memories and continue execution. However, without careful consideration, the process of checkpointing and resuming could cause inconsistency among different memory addresses and lead to irreversible errors. In this paper, we present a consistency aware checkpointing scheme that ensures correctness for all checkpoints. The proposed technique efficiently identifies all possible inconsistency positions in programs and inserts auxiliary code to ensure correctness. Evaluation results show that the proposed checkpointing technique can successfully eliminate inconsistency errors and greatly reduce the checkpointing overhead.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744842"}, {"primary_key": "4390635", "vector": [], "sparse_vector": [], "title": "Adaptive characterization and emulation of delay-based physical unclonable functions using statistical models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>"], "summary": "It is commonly known that physical unclonable functions (PUFs) are hard to predict and hard to emulate. However, in this paper, we propose to use statistical models to adaptively characterize the delay-based PUFs, and use this as a starting point to emulate a delay-based PUF. The essential idea is that for any challenge CA of a delay-based PUF A, there is a high probability of finding a paired challenge CB. When apply CB to another delay-based PUF B, it can produce the same output as applying CA on PUF A. Our simulation results indicate more than 99% correctness for the PUF response prediction using characterization and 96% correctness using emulation. Finally, we implement and test the feasibility of our approach on the Xilinx Spartan-6 Field Programmable Gate Array (FPGA).", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744791"}, {"primary_key": "4390636", "vector": [], "sparse_vector": [], "title": "PARR: pin access planning and regular routing for self-aligned double patterning.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pin access has become one of the most difficult challenges for detailed routing in 14nm technology node and beyond, where double patterning lithography has to be used for manufacturing lower metal layers with tight pitches. Self-aligned double patterning (SADP) provides better control on the line edge roughness and overlay but it has very restrictive design constraints and prefers regular layout patterns. This paper presents a comprehensive pin access planning and regular routing framework (PARR) for SADP friendliness. Our key techniques include pre-computation of both intra-cell and inter-cell pin accessibility, as well as local and global pin access planning to enable the handshaking between standard cell level pin access and detailed routing under SADP constraints. Our experimental results demonstrate that PARR can achieve much better routability and overlay control compared with previous approaches.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744890"}, {"primary_key": "4390637", "vector": [], "sparse_vector": [], "title": "Virtual flash chips: rethinking the layer design of flash devices to improve data recoverability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The market trend of flash memory chips has been going for high density but low reliability. The rapidly increasing bit error rates and emerging reliability issues of the coming triple-level cell (TLC) and even three-dimensional (3D) flash chips would let users take an extremely high risk to store data in such low reliability storage media. With the observations in mind, this paper rethinks the layer design of flash devices and propose a complete paradigm shift to re-configure physical flash chips of potentially massive parallelism into better \"virtual chips\", in order to improve the data recoverability in a modular and low-cost way. The concept of virtual chips is realized at hardware abstraction layer (HAL) without continually complicating the conventional flash management software (i.e., flash translation layer (FTL)). The capability and compatibility of the proposed design are then verified by a series of experiments with encouraging results.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744929"}, {"primary_key": "4390638", "vector": [], "sparse_vector": [], "title": "Criticality-dependency-aware timing characterization and analysis.", "authors": ["<PERSON><PERSON><PERSON>", "King <PERSON>", "<PERSON><PERSON>"], "summary": "For nanometer design, conventional timing analysis may generate over-optimistic results on criticality-dependent paths. A late arrival time at the data input of a flip-flop lengthens the propagation delay from the clock pin to the data output of this flip-flop, thus degrading the timing margins of paths launching from this flip-flop. To remove the optimism, in this paper, we first propose a simple yet effective triangle model to characterize the criticality-dependency effect. Then, we devise a novel criticality-dependency-aware timing analysis flow, which is seamlessly integrated with the common static timing analysis flow. Experimental results show that our approach can effectively analyze the criticality-dependency effect: Based on the proposed triangle model, we can accurately identify all timing-risky flip-flops and capture the induced timing margin degradation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744812"}, {"primary_key": "4390639", "vector": [], "sparse_vector": [], "title": "PACOR: practical control-layer routing flow with length-matching constraint for flow-based microfluidic biochips.", "authors": ["<PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "In flow-based microfluidic biochips, microvalves on the control layer need to be connected to control pins via control channels. In application-specific and portable microfluidic devices, critical microvalves need to switch at the same time for correct functionality. Those microvalves are required to have equal or similar channel lengths to the control pin, so that the control signal can reach them simultaneously. This paper presents a practical control-layer routing flow (PACOR) considering the critical length-matching constraint. Major features of PACOR include: (1) effective candidate Steiner tree construction and selection methods for multiple microvalves based on the deferred-merge embedding (DME) algorithm and maximum weight clique problem (MWCP) formulation, (2) minimum cost flow-based formulation for simultaneous escape routing for improved routability, and (3) minimum-length bounded routing method to detour paths for length matching. Computational simulation results show effectiveness and efficiency of PACOR with promising matching results and 100% routing completion rate.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744887"}, {"primary_key": "4390640", "vector": [], "sparse_vector": [], "title": "Memory heat map: anomaly detection in real-time embedded systems using memory behavior.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce a novel mechanism that identifies abnormal system-wide behaviors using the predictable nature of real-time embedded applications. We introduce Memory Heat Map (MHM) to characterize the memory behavior of the operating system. Our machine learning algorithms automatically (a) summarize the information contained in the MHMs and then (b) detect deviations from the normal memory behavior patterns. These methods are implemented on top of a multicore processor architecture to aid in the process of monitoring and detection. The techniques are evaluated using multiple attack scenarios including kernel rootkits and shellcode. To the best of our knowledge, this is the first work that uses aggregated memory behavior for detecting system anomalies especially the concept of memory heat maps.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744869"}, {"primary_key": "4390641", "vector": [], "sparse_vector": [], "title": "The challenge of interoperability: model-based integration for automotive control software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shin&a<PERSON>s;<PERSON><PERSON>"], "summary": "Model-Based Engineering (MBE) is a promising approach to cope with the challenges of designing the next-generation automotive systems. The increasing complexity of automotive electronics, the platform, distributed real-time embedded software, and the need for continuous evolution from one generation to the next has necessitated highly productive design approaches. However, heterogeneity, interoperability, and the lack of formal semantic underpinning in modeling, integration, validation and optimization make design automation a big challenge, which becomes a hindrance to the wider application of MBE in the industry. This paper briefly presents the interoperability challenges in the context of MBE and summarizes our current contribution to address these challenges with regard to automotive control software systems. A novel model-based formal integration framework is being developed to enable architecture modeling, timing specification, formal semantics, design by contract and optimization in the system-level design. The main advantages of the proposed approach include its pervasive use of formal methods, architecture analysis and design language (AADL) and associated tools, a novel timing annex for AADL with an expressive timing relationship language, a formal contract language to express component-level requirements and validation of component integration, and the resulting high assurance system delivery.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747945"}, {"primary_key": "4390642", "vector": [], "sparse_vector": [], "title": "Leveraging on-chip voltage regulators as a countermeasure against side-channel attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Selçuk Köse"], "summary": "Side-channel attacks have become a significant threat to the integrated circuit security. Circuit level techniques are proposed in this paper as a countermeasure against side-channel attacks. A distributed on-chip power delivery system consisting of multi-level switched capacitor (SC) voltage converters is proposed where the individual interleaved stages are turned on and turned off either based on the workload information or pseudo-randomly to scramble the power consumption profile. In the case that the changes in the workload demand do not trigger the power delivery system to turn on or off individual stages, the active stages are reshuffled with so called converter-reshuffling to insert random spikes in the power consumption profile. An entropy based metric is developed to evaluate the security-performance of the proposed converter-reshuffling technique as compared to three other existing on-chip power delivery schemes. The increase in the power trace entropy with CoRe scheme is also demonstrated with simulation results to further verify the theoretical analysis.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744866"}, {"primary_key": "4390643", "vector": [], "sparse_vector": [], "title": "HARS: a heterogeneity-aware runtime system for self-adaptive multithreaded applications.", "authors": ["Jaeyoung Yun", "Jinsu Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Heterogeneous multi-processing (HMP) is rapidly emerging as a promising solution for high-performance and low-power computing. Despite extensive prior work, system-software support for self-adaptive multithreaded applications has been little explored in the context of HMP. To bridge this gap, we propose HARS, a heterogeneity-aware runtime system for self-adaptive multithreaded applications. HARS continuously monitors the application performance and dynamically adapts the system state to enhance the performance/watt of the target self-adaptive multithreaded applications on HMP systems, while satisfying the user-specified performance goal. We quantify the effectiveness of HARS by demonstrating that HARS achieves significantly higher efficiency than the baseline version with the Linux HMP scheduler and comparable efficiency with that of the static optimal version.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744848"}, {"primary_key": "4390644", "vector": [], "sparse_vector": [], "title": "mTunes: efficient post-silicon tuning of mixed-signal/RF integrated circuits based on Markov decision process.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Uncertainty prevails in IC manufacturing and circuit operation. In particular, process variability has a huge impact on circuit performance, especially for mixed-signal/RF circuits, leading to unacceptable yields. Additionally, environmental uncertainties, such as temperature fluctuation and channel variation, further deteriorate performances in field. To combat variability, circuits are often made reconfigurable by adding tunable knobs to recover circuit performance in the post-manufacturing stage. However, as the number of knobs increases, knob tuning becomes challenging due to the huge search space. In fact, knob-tuning policies can have an observable impact on final performance and power consumption. In this paper, we propose mTunes, a method based on the Markov decision process for dynamically choosing the \"right\" knob tuning sub-routine from a pre-defined set achieving a balance between performance and power constraints. The proposed method has been applied to a reconfigurable RF front-end design, showing 60% improvement in yield compared to static tuning policies.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744873"}, {"primary_key": "4390645", "vector": [], "sparse_vector": [], "title": "DimNoC: a dim silicon approach towards power-efficient on-chip network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The diminishing momentum of Dennard scaling leads to the ever increasing power density of integrated circuits, and a decreasing portion of transistors on a chip that can be switched on simultaneously---a problem recently discovered and known as dark silicon. There has been innovative work to address the \"dark silicon\" problem in the fields of power-efficient core and cache system. However, dark silicon challenges with Network-on-Chip (NoC) are largely unexplored. To address this issue, we propose DimNoC, a \"dim silicon\" approach, which leverages drowsy SRAM and STT-RAM technologies to replace pure SRAM-based NoC buffers. Specifically, we propose two novel hybrid buffer architectures: 1) a Hierarchical Buffer (HB) architecture, which divides the input buffers into a hierarchy of levels with different memory technologies operating at various power states; 2) a Banked Buffer (BB) architecture, which organizes drowsy SRAM and STT-RAM into separate banks in order to hide the long write-latency of STT-RAM. Our experiments show that the proposed DimNoC can achieve 30.9% network energy saving, 20.3% energy-delay product (EDP) reduction, and 7.6% router area decrease compared with the baseline SRAM-based NoC design.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744824"}, {"primary_key": "4390646", "vector": [], "sparse_vector": [], "title": "CMOST: a system-level FPGA compilation framework.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming difficulty is a key challenge to the adoption of FPGAs as a general high-performance computing platform. In this paper we present CMOST, an open-source automated compilation flow that maps C-code to FPGAs for acceleration. CMOST establishes a unified framework for the integration of various system-level optimizations and for different hardware platforms. We also present several novel techniques on integrating optimizations in CMOST, including task-level dependence analysis, block-based data streaming, and automated SDF generation. Experimental results show that automatically generated FPGA accelerators can achieve over 8x speedup and 120x energy gain on average compared to the multi-core CPU results from similar input C programs. CMOST results are comparable to those obtained after extensive manual source-code transformations followed by high-level synthesis.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744807"}, {"primary_key": "4390647", "vector": [], "sparse_vector": [], "title": "Deadline-aware task scheduling for solar-powered nonvolatile sensor nodes with global energy migration.", "authors": ["Daming <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "Tongda Wu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Solar-powered sensor nodes with energy storages are widely used today and promising in the coming trillion sensor era, as they do not require manual battery charging or replacement. The changeable and limited solar power supply seriously affects the deadline miss rates (DMRs) of tasks on these nodes and therefore energy-driven task scheduling is necessary. However, current algorithms focus on the single period (or the current task queue) for high energy utilization and suffer from bad long term DMR. To get better long term DMR, we propose a long term deadline-aware scheduling algorithm with energy migration strategies for distributed super capacitors. Experimental results show that the proposed algorithm reduces the DMR by 27.8% and brings less than 3% of the total energy consumption.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744815"}, {"primary_key": "4390648", "vector": [], "sparse_vector": [], "title": "A cross-layer design exploration of charge-recycled power-delivery in many-layer 3d-IC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "3D-IC technology brings both the opportunities to continue the historical trend of integration-level scaling and the challenges to deliver power reliably and efficiently. Voltage-stacking (V-S), a charge-recycled power delivery scheme that connects the different layers' supply/ground nets into a series stack, provides a scalable solution to the 3D-IC power delivery wall. While prior work has extensively discussed the implementations of V-S at circuit-level, a cross-layer study that examines its system-level implications is missing. In this paper, we start with a circuit implementation of a charge-recycled voltage regulator and build an architecture-level model to study the costs and benefits of utilizing V-S in 3D-IC. Our study shows that by significantly improving the EM-lifetime of C4 and TSV array (e.g., up to 5x) while only marginally increasing the average-case voltage noise (e.g., 0.75% Vdd IR drop), V-S provides a scalable solution for many-layer 3D-IC's power delivery challenge.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744774"}, {"primary_key": "4390649", "vector": [], "sparse_vector": [], "title": "Area-efficient pipelining for FPGA-targeted high-level synthesis.", "authors": ["<PERSON>", "Mingxing Tan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional techniques for pipeline scheduling in high-level synthesis for FPGAs assume an additive delay model where each operation incurs a pre-characterized delay. While a good approximation for some operation types, this fails to consider technology mapping, where a group of logic operations can be mapped to a single look-up table (LUT) and together incur one LUT worth of delay. We propose an exact formulation of the throughput-constrained, mapping-aware pipeline scheduling problem for FPGA-targeted high-level synthesis with area minimization being a primary objective. By taking this cross-layered approach, our technique is able to mitigate the pessimism inherent in static delay estimates and reduce the usage of LUTs and pipeline registers. Experimental results using our method demonstrate improved resource utilization for a number of logic-intensive, real-life benchmarks compared to a state-of-the-art commercial HLS tool for Xilinx FPGAs.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744801"}, {"primary_key": "4390650", "vector": [], "sparse_vector": [], "title": "Design and verification for transportation system security.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cyber-security has emerged as a pressing issue for transportation systems. Studies have shown that attackers can attack modern vehicles from a variety of interfaces and gain access to the most safety-critical components. Such threats become even broader and more challenging with the emergence of vehicle-to-vehicle (V2V) and vehicle-to-infrastructure (V2I) communication technologies. Addressing the security issues in transportation systems requires comprehensive approaches that encompass considerations of security mechanisms, safety properties, resource constraints, and other related system metrics. In this work, we propose an integrated framework that combines hybrid modeling, formal verification, and automated synthesis techniques for analyzing the security and safety of transportation systems and carrying out design space exploration of both in-vehicle electronic control systems and vehicle-to-vehicle communications. We demonstrate the ideas of our framework through a case study of cooperative adaptive cruise control.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747920"}, {"primary_key": "4390651", "vector": [], "sparse_vector": [], "title": "Detecting hardware trojans using backside optical imaging of embedded watermarks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hardware Trojans are a critical security threat to integrated circuits. We propose an optical method to detect and localize Trojans inserted during the chip fabrication stage. We engineer the fill cells in a standard cell library to be highly reflective at near-IR wavelengths so that they can be readily observed in an optical image taken through the backside of the chip. The pattern produced by their locations produces an easily measured watermark of the circuit layout. Replacement, modification or re-arrangement of these cells to add a Trojan can therefore be detected through rapid post-fabrication backside imaging. We evaluate our approach using various hardware blocks where the Trojan circuit area is less than 0.1% of the total area and it consumes less than 2% leakage power of the entire chip. In addition, we evaluate the tolerance of our methodology to background measurement noise and process variation.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744822"}, {"primary_key": "4390652", "vector": [], "sparse_vector": [], "title": "An algorithmic framework for efficient large-scale circuit simulation using exponential integrators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an efficient algorithmic framework for time-domain circuit simulation using exponential integrators. This work addresses several critical issues exposed by previous matrix exponential based circuit simulation research, and makes it capable of simulating stiff nonlinear circuit system at a large scale. In this framework, the system's nonlinearity is treated with exponential Rosenbrock-Euler formulation. The matrix exponential and vector product is computed using invert Krylov subspace method. Our proposed method has several distinguished advantages over conventional formulations (e.g., the well-known backward Euler with Newton-<PERSON> method). The matrix factorization is performed only for the conductance/resistance matrix G, without being performed for the combinations of the capacitance/inductance matrix C and matrix G, which are used in traditional implicit formulations. Furthermore, due to the explicit nature of our formulation, we do not need to repeat LU decompositions when adjusting the length of time steps for error controls. Our algorithm is better suited to solving tightly coupled post-layout circuits in the pursuit for full-chip simulation. Our experimental results validate the advantages of our framework.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744793"}, {"primary_key": "4390653", "vector": [], "sparse_vector": [], "title": "Timing-aware control software design for automotive systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The underlying theories of both control engineering and real-time systems engineering assume idealized system abstractions that mutually neglect central aspects of the other discipline. Control engineering theory, on the one hand, usually assumes jitter free sampling and constant input-output latencies disregarding complex real-world timing effects. Real-time engineering theory, on the other hand, uses abstract performance models that neglect the functional behavior, and derives worst-case situations that have little expressiveness for control functionalities in physically dominated automotive systems. As a consequence, there is a lot of potential for a systematic co-engineering between both disciplines, increasing design efficiency and confidence.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2747947"}, {"primary_key": "4390654", "vector": [], "sparse_vector": [], "title": "Variation aware cross-talk aggressor alignment by mixed integer linear programming.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The accurate analysis of cross-talk noise in VLSI chips must consider the timing when signals can interfere, i.e., the switching windows. It is important to determine the time-alignment of signal switching events that cause maximal cross-talk noise. Moreover, it is necessary to include the effects of process and environmental variations, as they strongly affect the switching windows, and further complicate the computation of the worst possible noise. We present a general formulation of the worst case noise analysis problem as a mixed-integer-linear-program, (MILP). The formulation captures elaborate cross-talk models that include variational switching windows, victim sensitivity windows, discontinuous switching windows, diversely shaped noise pulses, and logic constraints on switching activity. We demonstrate that our technique correctly predicts the worst noise pulse, including cases even where conventional approaches fail.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2744769.2744924"}, {"primary_key": "4519277", "vector": [], "sparse_vector": [], "title": "Proceedings of the 52nd Annual Design Automation Conference, San Francisco, CA, USA, June 7-11, 2015", "authors": [], "summary": "No abstract available.", "published": "2015-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": ""}]