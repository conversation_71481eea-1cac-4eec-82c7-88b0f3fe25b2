[{"primary_key": "4170548", "vector": [], "sparse_vector": [], "title": "Enabling ECN in Multi-Service Multi-Queue Data Centers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent proposals have leveraged Explicit Congestion\nNotification (ECN) to achieve high throughput low latency\ndata center network (DCN) transport. However,\nmost of them implicitly assume each switch port has one\nqueue, making the ECN schemes they designed inapplicable\nto production DCNs where multiple service queues\nper port are employed to isolate different traffic classes\nthrough weighted fair sharing. In this paper, we reveal this problem by leveraging extensive\ntestbed experiments to explore the intrinsic tradeoffs\nbetween throughput, latency, and weighted fair sharing\nin multi-queue scenarios. Using the guideline learned\nfrom the exploration, we design MQ-ECN, a simple yet\neffective solution to enable ECN for multi-service multiqueue\nproduction DCNs. Through a series of testbed\nexperiments and large-scale simulations, we show that\nMQ-ECN breaks the tradeoffs by delivering both high\nthroughput and low latency simultaneously, while still\npreserving weighted fair sharing.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170549", "vector": [], "sparse_vector": [], "title": "DFC: Accelerating String Pattern Matching for Network Applications.", "authors": ["Byungkwon Choi", "Jongwook Chae", "<PERSON>", "KyoungSoo Park", "<PERSON><PERSON> Han"], "summary": "Middlebox services that inspect packet payloads have become\ncommonplace. Today, anyone can sign up for cloudbased\nWeb application firewall with a single click. These\nservices typically look for known patterns that might appear\nanywhere in the payload. The key challenge is that\nexisting solutions for pattern matching have become a\nbottleneck because software packet processing technologies\nhave advanced. The popularization of cloud-based\nservices has made the problem even more critical. This paper presents an efficient multi-pattern string\nmatching algorithm, called DFC. DFC significantly reduces\nthe number of memory accesses and cache misses\nby using small and cache-friendly data structures and\navoids instruction pipeline stalls by minimizing sequential\ndata dependency. Our evaluation shows that DFC\nimproves performance by 2.0 to 3.6 times compared to\nstate-of-the-art on real traffic workload obtained from a\ncommercial network. It also outperforms other algorithms\neven in the worst case. When applied to middlebox applications,\nsuch as network intrusion detection, anti-virus,\nand Web application firewalls, DFC delivers 57-160%\nimprovement in performance.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170550", "vector": [], "sparse_vector": [], "title": "HUG: Multi-Resource Fairness for Correlated and Elastic Demands.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "In this paper, we study how to optimally provide isolation guarantees in multi-resource environments, such as public clouds, where a tenant’s demands on different resources (links) arecorrelated. Unlike prior work such as Dominant Resource Fairness (DRF) that assumes static and fixed demands, we considerelasticdemands. Our approach generalizes canonical max-min fairness to the multi-resource setting with correlated demands, and extends DRF to elastic demands. We consider two natural optimization objectives: isolation guarantee from a tenant’s viewpoint and system utilization (work conservation) from an operator’s perspective. We prove that in non-cooperative environments like public cloud networks, there is a strong tradeoff between optimal isolation guarantee and work conservation when demands are elastic. Even worse, work conservation can even decrease network utilization instead of improving it when demands are inelastic. We identify the root cause behind the tradeoff and present a provably optimal allocation algorithm,High Utilization with Guarantees(HUG), to achieve maximum attainable network utilization without sacrificing the optimal isolation guarantee, strategyproofness, and other useful properties of DRF. In cooperative environments like private datacenter networks, HUG achieves both the optimal isolation guarantee and work conservation. Analyses, simulations, and experiments show that HUG provides better isolation guarantees, higher system utilization, and better tenant-level performance than its counterparts.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170551", "vector": [], "sparse_vector": [], "title": "Cliffhanger: Scaling Performance Cliffs in Web Memory Caches.", "authors": ["Asaf Cidon", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Web-scale applications are heavily reliant on memory cache systems such as Memcached to improve throughput and reduce user latency. Small performance improvements in these systems can result in large end-to-end gains. For example, a marginal increase in hit rate of 1% can reduce the application layer latency by over 35%. However, existing web cache resource allocation policies are workload oblivious and first-come-first-serve. By analyzing measurements from a widely used caching service, Memcachier, we demonstrate that existing cache allocation techniques leave significant room for improvement. We develop Cliffhanger, a lightweight iterative algorithm that runs on memory cache servers, which incrementally optimizes the resource allocations across and within applications based on dynamically changing workloads. It has been shown that cache allocation algorithms underperform when there are performance cliffs, in which minor changes in cache allocation cause large changes in the hit rate. We design a novel technique for dealing with performance cliffs incrementally and locally. We demonstrate that for the Memcachier applications, on average, <PERSON>hang<PERSON> increases the overall hit rate 1.2%, reduces the total number of cache misses by 36.7% and achieves the same hit rate with 45% less memory capacity.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170552", "vector": [], "sparse_vector": [], "title": "Diamond: Nesting the Data Center Network with Wireless Rings in 3D Space.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Xiangyang Li", "<PERSON>", "<PERSON>ng <PERSON>e"], "summary": "The introduction of wireless transmissions into the data\ncenter has been shown to be promising in improving\nthe performance of data center networks (DCN) cost effectively.\nFor high transmission flexibility and performance,\na fundamental challenge is to increase the wireless\navailability and enable fully hybrid and seamless\ntransmissions over both wired and wireless DCN components.\nRather than limiting the number of wireless radios\nby the size of top-of-rack (ToR) switches, we propose\na novel DCN architecture,Diamond, which nests\nthe wired DCN with radios equipped on all servers. To\nharvest the gain allowed by the rich reconfigurable wireless\nresources, we propose the low-cost deployment of\nscalable 3D Ring Reflection Spaces (RRSs) which are interconnected\nwith streamlined wired herringbone to enable\nlarge number of concurrent wireless transmissions\nthrough high-performance multi-reflection of radio signals\nover metal. To increase the number of concurrent\nwireless transmissions within each RRS, we propose a\nprecise reflection method to reduce the wireless interference.\nWe build a 60GHz-based testbed to demonstrate\nthe function and transmission ability of our proposed architecture.\nWe further perform extensive simulations to\nshow the significant performance gain of Diamond, in\nsupporting up to five times higher server-to-server capacity,\nenabling network-wide load balancing, and ensuring\nhigh fault tolerance.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170553", "vector": [], "sparse_vector": [], "title": "Sibyl: A Practical Internet Route Oracle.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Harsha V. Madhyastha", "<PERSON>-<PERSON><PERSON>"], "summary": "Network operators measure Internet routes to troubleshoot problems, and researchers measure routes to characterize the Internet. However, they still rely on decades-old tools like traceroute, BGP route collectors, and Looking Glasses, all of which permit only a single query about Internet routes—what is the path from here to there? This limited interface complicates answering queries about routes such as\"find routes traversing the Level3/AT&T peering in Atlanta,\"to understand the scope of a reported problem there. This paper presentsSibyl,a system that takes rich queries that researchers and operators express as regular expressions, then issues and returns traceroutes that match even if it has never measured a matching path in the past.Sibylachieves this goal in three steps. First, to maximize its coverage of Internet routing,Sibylintegrates together diverse sets of traceroute vantage points that provide complementary views, measuring from thousands of networks in total. Second, because users may not know which measurements will traverse paths of interest, and because vantage point resource constraints keepSibylfrom tracing to all destinations from all sources,Sibyluses historical measurements to predict which new ones are likely to match a query. Finally, based on these predictions,Sibyloptimizes across concurrent queries to decide which measurements to issue given resource constraints. We show thatSibylprovides researchers and operators with the routing information they need—in fact, it matches 76% of the queries that it could match if an oracle told it which measurements to issue.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170554", "vector": [], "sparse_vector": [], "title": "Maglev: A Fast and Reliable Software Network Load Balancer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Maglev is Google’s network load balancer. It is a\nlarge distributed software system that runs on commodity\nLinux servers. Unlike traditional hardware network load\nbalancers, it does not require a specialized physical rack\ndeployment, and its capacity can be easily adjusted by\nadding or removing servers. Network routers distribute\npackets evenly to the Maglev machines via Equal Cost\nMultipath (ECMP); each Maglev machine then matches\nthe packets to their corresponding services and spreads\nthem evenly to the service endpoints. To accommodate\nhigh and ever-increasing traffic, Maglev is specifically\noptimized for packet processing performance. A single\nMaglev machine is able to saturate a 10Gbps link with\nsmall packets. Maglev is also equipped with consistent\nhashing and connection tracking features, to minimize\nthe negative impact of unexpected faults and failures on\nconnection-oriented protocols. Maglev has been serving\nGoogle’s traffic since 2008. It has sustained the rapid\nglobal growth of Google services, and it also provides\nnetwork load balancing for Google Cloud Platform.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170555", "vector": [], "sparse_vector": [], "title": "Bitcoin-NG: A Scalable Blockchain Protocol.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptocurrencies, based on and led by Bitcoin, have shown promise as infrastructure for pseudonymous online payments, cheap remittance, trustless digital asset exchange, and smart contracts. However, Bitcoin-derived blockchain protocols have inherent scalability limits that trade off between throughput and latency, which withhold the realization of this potential. This paper presents Bitcoin-NG (Next Generation), a new blockchain protocol designed to scale. Bitcoin-NG is a Byzantine fault tolerant blockchain protocol that is robust to extreme churn and shares the same trust model as Bitcoin. In addition to Bitcoin-NG, we introduce several novel metrics of interest in quantifying the security and efficiency of Bitcoin-like blockchain protocols. We implement Bitcoin-NG and perform large-scale experiments at 15% the size of the operational Bitcoin system, using unchanged clients of both protocols. These experiments demonstrate that Bitcoin-NG scales optimally, with bandwidth limited only by the capacity of the individual nodes and latency limited only by the propagation time of the network.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170556", "vector": [], "sparse_vector": [], "title": "BUZZ: Testing Context-Dependent Policies in Stateful Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Checking whether a network correctly implements intended policies is challenging even for basic reachability policies (Can <PERSON> talk to Y?) in simple stateless networks with L2/L3 devices. In practice, operators implement more complexcontext-dependentpolicies by composingstatefulnetwork functions; e.g., if the IDS flags X for sending too many failed connections, then subsequent packets from X must be sent to a deep-packet inspection device. Unfortunately, existing approaches in network verification have fundamental expressiveness and scalability challenges in handling such scenarios. To bridge this gap, we present BUZZ, a practical model-based testing framework. BUZZ’s design makes two key contributions: (1) Expressive and scalable models of the data plane, using a novel high-level traffic unit abstraction and by modeling complex network functions as an ensemble of finite-state machines; and (2) A scalable application of symbolic execution to tackle state-space explosion. We show that BUZZ generates test cases for a network with hundreds of network functions within two minutes (five orders of magnitude faster than alternative designs). We also show that BUZZ uncovers a range of both new and known policy violations in SDN/NFV systems.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170557", "vector": [], "sparse_vector": [], "title": "A Scalable Multi-User Uplink for Wi-Fi.", "authors": ["Adriana B. Flores", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile devices have fewer antennas than APs due to size and energy constraints. This antenna asymmetry restricts uplink capacity to theclientantenna array size rather than the AP’s. To overcome antenna asymmetry, multiple clients can be grouped into a simultaneous multiuser transmission to achieve a full rank transmission that matches the number of antennas at the AP. In this paper, we design, implement, and experimentally evaluate MUSE, the first distributed and scalable system to achieve full-rank uplink multi-user capacity without control signaling for channel estimation, channel reporting, or user selection. Our experiments demonstrate full-rank multiplexing gains in the evaluated scenarios that show linear gains as the number of users increase while maintaining constant overhead.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170558", "vector": [], "sparse_vector": [], "title": "Scalable and Private Media Consumption with Popcorn.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe the design, implementation, and evaluation ofPopcorn,a media delivery system that hides clients’ consumption (even from the content distributor). Popcorn relies on a powerful cryptographic primitive: private information retrieval (PIR). With novel refinements that leverage the properties of PIR protocols and media streaming, Popcorn scales to the size of Netflix’s library (8000 movies) and respects current controls on media dissemination. The dollar cost to serve a media object in Popcorn is 3.87× that of a non-private system.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170559", "vector": [], "sparse_vector": [], "title": "An Industrial-Scale Software Defined Internet Exchange Point.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software-Defined Internet Exchange Points (SDXes) promise to significantly increase the flexibility and function of interdomain traffic delivery on the Internet. Unfortunately, current SDX designs cannot yet achieve the scale required for large Internet exchange points (IXPs), which can host hundreds of participants exchanging traffic for hundreds of thousands of prefixes. Existing platforms are indeed too slow and inefficient to operate at this scale, typically requiring minutes to compile policies and millions of forwarding rules in the data plane. We motivate, design, and implement iSDX, the first SDX architecture that can operate at the scale of the largest IXPs. We show that iSDX reduces both policy compilation time and forwarding table size by two orders of magnitude compared to current state-of-the-art SDX controllers. Our evaluation against a trace from one of the largest IXPs in the world found that iSDX can compile a realistic set of policies for 500 IXP participants in less than three seconds. Our public release of iSDX, complete with tutorials and documentation, is already spurring early adoption in operational networks.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170560", "vector": [], "sparse_vector": [], "title": "Simplifying Software-Defined Network Optimization Using SOL.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Realizing the benefits of SDN for many network management applications (e.g., traffic engineering, service chaining, topology reconfiguration) involves addressing complex optimizations that are central to these problems. Unfortunately, such optimization problems require (a) significant manual effort and expertise to express and (b) non-trivial computation and/or carefully crafted heuristics to solve. Our goal is to simplify the deployment of SDN applications usinggeneralhigh-level abstractions for capturing optimization requirements from which we canefficientlygenerate optimal solutions. To this end, we present SOL, a framework that demonstrates that it is possible to simultaneously achieve generality and efficiency. The insight underlying SOL is that many SDN applications can be recast within a unifyingpath-basedoptimization abstraction. Using this, SOL can efficiently generate near-optimal solutions and device configurations to implement them. We show that SOL provides comparable or better scalability than custom optimization solutions for diverse applications, allows a balancing of optimality and route churn per reconfiguration, and interfaces with modern SDN controllers.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170561", "vector": [], "sparse_vector": [], "title": "Consensus in a Box: Inexpensive Coordination in Hardware.", "authors": ["Zsolt István", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Consensus mechanisms for ensuring consistency are some of the most expensive operations in managing large amounts of data. Often, there is a trade off that involves reducing the coordination overhead at the price of accepting possible data loss or inconsistencies. As the demand for more efficient data centers increases, it is important to provide better ways of ensuring consistency without affecting performance. In this paper we show that consensus (atomic broadcast) can be removed from the critical path of performance by moving it to hardware. As a proof of concept, we implement <PERSON>keeper’s atomic broadcast at the network level using an FPGA. Our design uses both TCP and an application specific network protocol. The design can be used to push more value into the network, e.g., by extending the functionality of middleboxes or adding inexpensive consensus to in-network processing nodes. To illustrate how this hardware consensus can be used in practical systems, we have combined it with a mainmemory key value store running on specialized microservers (built as well on FPGAs). This results in a distributed service similar to Zookeeper that exhibits high and stable performance. This work can be used as a blueprint for further specialized designs.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170562", "vector": [], "sparse_vector": [], "title": "CFA: A Practical Prediction System for Video QoE Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Ion <PERSON>", "<PERSON>"], "summary": "Many prior efforts have suggested that Internet video Quality of Experience (QoE) could be dramatically improved by using data-driven prediction of video quality for different choices (e.g., CDN or bitrate) to make optimal decisions. However, building such a prediction system is challenging on two fronts. First, the relationships between video quality and observed session features can be quite complex. Second, video quality changes dynamically. Thus, we need a prediction model that is (a) expressive enough to capture these complex relationships and (b) capable of updating quality predictions in near real-time. Unfortunately, several seemingly natural solutions (e.g., simple machine learning approaches and simple network models) fail on one or more fronts. Thus, the potential benefits promised by these prior efforts remain unrealized. We address these challenges and present the design and implementation of Critical Feature Analytics (CFA). The design of CFA is driven by domain-specific insights that video quality is typically determined by a small subset of critical features whose criticality persists over several tens of minutes. This enables a scalable and accurate workflow where we automatically learn critical features for different sessions on coarse-grained timescales, while updating quality predictions in near real-time. Using a combination of a real-world pilot deployment and trace-driven analysis, we demonstrate that CFA leads to significant improvements in video quality; e.g., 32% less buffering time and 12% higher bitrate than a random decision maker.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170563", "vector": [], "sparse_vector": [], "title": "Passive Wi-Fi: Bringing Low Power to Wi-Fi Transmissions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wi-Fi has traditionally been considered a power-consuming communication system and has not been widely adopting in the sensor network and IoT space. We introduce Passive Wi-Fi that demonstrates for the first time that one can generate 802.11b transmissions using backscatter communication, while consuming 3–4 orders of magnitude lower power than existing Wi-Fi chipsets. Passive Wi-Fi transmissions can be decoded on any Wi-Fi device including routers, mobile phones and tablets. Building on this, we also present a network stack design that enables passive Wi-Fi transmitters to coexist with other devices in the ISM band, without incurring the power consumption of carrier sense and medium access control operations. We build prototype hardware and implement all four 802.11b bit rates on an FPGA platform. Our experimental evaluation shows that passive Wi-Fi transmissions can be decoded on off-the-shelf smartphones and Wi-Fi chipsets over distances of 30–100 feet in various line-of-sight and through-the-wall scenarios. Finally, we design a passive Wi-Fi IC that shows that 1 and 11 Mbps transmissions consume 14.5 and 59.2 µW respectively. This translates to 10000x lower power than existing Wi-Fi chipsets and 1000x lower power than Bluetooth LTE and ZigBee.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170564", "vector": [], "sparse_vector": [], "title": "Paving the Way for NFV: Simplifying Middlebox Modifications Using StateAlyzr.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Important Network Functions Virtualization (NFV) scenarios such as ensuring middlebox fault tolerance or elasticity require redistribution of internal middlebox state. While many useful frameworks exist today for migrating/cloning internal state, they require modications to middlebox code to identify needed state. is process is tedious and manual, hindering the adoption of such frameworks. We present a framework-independent system, StateAlyzr, that embodies novel algorithms adapted from program analysis to provably and automatically identify all state that must be migrated/cloned to ensure consistent middlebox output in the face of redistribution. We find that StateAlyzr reduces man-hours required for code modication by nearly 20✕. We apply StateAlyzr to four open source middleboxes and find its algorithms to be highly precise. We find that a large amount of, but not all, live state matters toward packet processing in these middleboxes. StateAlyzr’s algorithms can reduce the amount of state that needs redistribution by 600- 8000✕ compared to naive schemes.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170565", "vector": [], "sparse_vector": [], "title": "BlowFish: Dynamic Storage-Performance Tradeoff in Data Stores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ion <PERSON>"], "summary": "We present BlowFish, a distributed data store that admits\na smooth tradeoff between storage and performance for\npoint queries. What makes BlowFish unique is its ability\nto navigate along this tradeoff curve efficiently at finegrained\ntime scales with low computational overhead.\nAchieving a smooth and dynamic storage-performance\ntradeoff enables a wide range of applications. We apply\nBlowFish to several such applications from real-world\nproduction clusters: (i) as a data recovery mechanism during\nfailures: in practice, BlowFish requires 5.4× lower\nbandwidth and 2.5× lower repair time compared to stateof-the-art\nerasure codes, while reducing the storage cost\nof replication from 3× to 1.9×; and (ii) data stores with\nspatially-skewed and time-varying workloads (e.g., due\nto object popularity and/or transient failures): we show\nthat navigating the storage-performance tradeoff achieves\nhigher system-wide utility (e.g., throughput) than selectively\ncaching hot objects.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170566", "vector": [], "sparse_vector": [], "title": "Diplomat: Using Delegations to Protect Community Repositories.", "authors": ["<PERSON><PERSON><PERSON>", "Santiago Torres-Arias", "<PERSON>", "<PERSON>"], "summary": "Community repositories, such as Docker Hub, PyPI,\nand RubyGems, are bustling marketplaces that distribute\nsoftware. Even though these repositories use common\nsoftware signing techniques (e.g., GPG and TLS), attackers\ncan still publish malicious packages after a server\ncompromise. This is mainly because a community repository\nmust have immediate access to signing keys in order\nto certify the large number of new projects that are\nregistered each day. This work demonstrates that community repositories\ncan offer compromise-resilience and real-time project\nregistration by employing mechanisms that disambiguate\ntrust delegations. This is done through two delegation\nmechanisms that provide flexibility in the amount of trust\nassigned to different keys. Using this idea we implement\nDiplomat, a software update framework that supports security\nmodels with different security / usability tradeoffs.\nBy leveraging Diplomat, a community repository\ncan achieve near-perfect compromise-resilience while allowing\nreal-time project registration. For example, when\n<PERSON><PERSON> is deployed and configured to maximize security\non <PERSON>'s community repository, less than 1%\nof users will be at risk even if an attacker controls the\nrepository and is undetected for a month. Diplomat is\nbeing integrated by Ruby, CoreOS, Haskell, OCaml, and\nPython, and has already been deployed by Flynn, LEAP,\nand Docker.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170567", "vector": [], "sparse_vector": [], "title": "Embark: Securely Outsourcing Middleboxes to the Cloud.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is increasingly common for enterprises and other organizations to outsource network processing to the cloud. For example, enterprises may outsource firewalling, caching, and deep packet inspection, just as they outsource compute and storage. However, this poses a threat to enterprise confidentiality because the cloud provider gains access to the organization’s traffic. We design and build Embark, the first system that enables a cloud provider to support middlebox outsourcing while maintaining the client’s confidentiality. Embark encrypts the traffic that reaches the cloud and enables the cloud to process the encrypted traffic without decrypting it. Embark supports a wide-range of middleboxes such as firewalls, NATs, web proxies, load balancers, and data ex- filtration systems. Our evaluation shows that Embark supports these applications with competitive performance.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170568", "vector": [], "sparse_vector": [], "title": "XFabric: A Reconfigurable In-Rack Network for Rack-Scale Computers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Rack-scale computers are dense clusters with hundreds of micro-servers per rack. Designed for data center workloads, they can have significant power, cost and performance benefits over current racks. The rack network can be distributed, with small packet switches embedded on each processor as part of a system-on-chip (SoC) design. Ingress/egress traffic is forwarded by SoCs that have direct uplinks to the data center. Such fabrics are not fully provisioned and the chosen topology and uplink placement impacts performance for different workloads. XFabric is a rack-scale network that reconfigures the topology and uplink placement using a circuit-switched physical layer over which SoCs perform packet switching. To satisfy tight power and space requirements in the rack, XFabric does not use a single large circuit switch, instead relying on a set of independent smaller circuit switches. This introduces partial reconfigurability, as some ports in the rack cannot be connected by a circuit. XFabric optimizes the physical topology and manages uplinks, efficiently coping with partial reconfigurability. It significantly outperforms static topologies and has a performance similar to fully reconfigurable fabrics. We demonstrate the benefits of XFabric using flow-based simulations and a prototype built with electrical crosspoint switch ASICs.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170569", "vector": [], "sparse_vector": [], "title": "iCellular: Device-Customized Cellular Network Access on Commodity Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Exploiting multi-carrier access offers a promising direction\nto boost access quality in mobile networks. However,\nour experiments show that, the current practice\ndoes not achieve the full potential of this approach because\nit has not utilized fine-grained, cellular-specific domain\nknowledge. In this work, we proposeiCellular,\nwhich exploits low-level cellular information at the device\nto improve multi-carrier access. Specifically,iCellularis proactive and adaptive in its multi-carrier selection\nby leveraging existing end-device mechanisms and\nstandards-complaint procedures. It performs adaptive\nmonitoring to ensure responsive selection and minimal\nservice disruption, and enhances carrier selection with\nonline learning and runtime decision fault prevention. It\nis readily deployable on smartphones without infrastructure/hardware\nmodifications. We implementiCellularon\ncommodity phones and harness the efforts ofProject Fito assess multi-carrier access over two US carriers: TMobile\nand Sprint. Our evaluation shows that,iCellularboosts the devices with up to 3.74x throughput improvement,\n6.9x suspension reduction, and 1.9x latency decrement\nover the state-of-the-art selection scheme, with\nmoderate CPU, memory and energy overheads.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170570", "vector": [], "sparse_vector": [], "title": "FlowRadar: A Better NetFlow for Data Centers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "NetFlow has been a widely used monitoring tool with\na variety of applications. NetFlow maintains an active\nworking set of flows in a hash table that supports flow\ninsertion, collision resolution, and flow removing. This\nis hard to implement in merchant silicon at data center\nswitches, which has limited per-packet processing\ntime. Therefore, many NetFlow implementations and\nother monitoring solutions have to sample or select a\nsubset of packets to monitor. In this paper, we observe\nthe need to monitor all the flows without sampling in\nshort time scales. Thus, we design FlowRadar, a new\nway to maintain flows and their counters that scales to a\nlarge number of flows with small memory and bandwidth\noverhead. The key idea of FlowRadar is to encode per-\nflow counters with a small memory and constant insertion\ntime at switches, and then to leverage the computing\npower at the remote collector to perform network-wide\ndecoding and analysis of the flow counters. Our evaluation\nshows that the memory usage of FlowRadar is\nclose to traditional NetFlow withperfect hashing. With\nFlowRadar, operators can get better views into their networks\nas demonstrated by two new monitoring applications\nwe build on top of FlowRadar.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170571", "vector": [], "sparse_vector": [], "title": "Be Fast, Cheap and in Control with SwitchKV.", "authors": ["Xiaozhou Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "SwitchKV is a new key-value store system design that combines high-performance cache nodes with resourceconstrained backend nodes to provide load balancing in the face of unpredictable workload skew. The cache nodes absorb the hottest queries so that no individual backend node is over-burdened. Compared with previous designs, SwitchKV exploits SDN techniques and deeply optimized switch hardware to enable efficient contentbased routing. Programmable network switches keep track of cached keys and route requests to the appropriate nodes at line speed, based on keys encoded in packet headers. A new hybrid caching strategy keeps cache and switch forwarding rules updated with low overhead and ensures that system load is always well-balanced under rapidly changing workloads. Our evaluation results demonstrate that SwitchKV can achieve up to 5× throughput and 3× latency improvements over traditional system designs.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170572", "vector": [], "sparse_vector": [], "title": "Exploring Cross-Application Cellular Traffic Optimization with Baidu TrafficGuard.", "authors": ["Zhenhua Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As mobile cellular devices and traffic continue their rapid growth, providers are taking larger steps to optimize traffic, with the hopes of improving user experiences while reducing congestion and bandwidth costs. This paper presents the design, deployment, and experiences with Baidu TrafficGuard, a cloud-based mobile proxy that reduces cellular traffic using a network-layer VPN. The VPN connects a client-side proxy to a centralized traffic processing cloud. TrafficGuard works transparently across heterogeneous applications, and effectively reduces cellular traffic by 36% and overage instances by 10.7 times for roughly 10 million Android users in China. We discuss a large-scale cellular traffic analysis effort, how the resulting insights guided the design of TrafficGuard, and our experiences with a variety of traffic optimization techniques over one year of deployment.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170573", "vector": [], "sparse_vector": [], "title": "StreamScope: Continuous Reliable Distributed Processing of Big Data Streams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "STREAMSCOPE(orSTREAMS) is a reliable distributed stream computation engine that has been deployed in shared 20,000-server production clusters at Microsoft. STREAMS provides a continuous temporal stream model that allows users to express complex stream processing logic naturally and declaratively.STREAMSsupports business-critical streaming applications that can process tens of billions (or tens of terabytes) of input events per day continuously with complex logic involving tens of temporal joins, aggregations, and sophisticated userdefined functions, while maintaining tens of terabytes in-memory computation states on thousands of machines. STREAMSintroduces two abstractions,rVertexandrStream, to manage the complexity in distributed stream computation systems. The abstractions allow efficient and flexible distributed execution and failure recovery, make it easy to reason about correctness even with failures, and facilitate the development, debugging, and deployment of complex multi-stage streaming applications.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170574", "vector": [], "sparse_vector": [], "title": "Efficiently Delivering Online Services over Integrated Infrastructure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Footprint, a system for delivering online services in the \"integrated\" setting, where the same provider operates multiple elements of the infrastructure (e.g., proxies, data centers, and the wide area network). Such integration can boost system efficiency and performance by finely modulating how traffic enters and traverses the infrastructure. But fully realizing its benefits requires managing complex dynamics of service workloads. For instance, when a group of users are directed to a new proxy, their ongoing sessions continue to arrive at the old proxy, and this load at the old proxy declines gradually. Footprint harnesses such dynamics using a high-fidelity model that is also efficient to solve. Simulations based on a partial deployment of Footprint in Microsoft’s infrastructure show that, compared to the current method, it can carry at least 50% more traffic and reduce user delays by at least 30%.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170575", "vector": [], "sparse_vector": [], "title": "Universal Packet Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we address a seemingly simple question:Is there a universal packet scheduling algorithm?More\nprecisely, we analyze (both theoretically and empirically)\nwhether there is a single packet scheduling algorithm that,\nat a network-wide level, can perfectly match the results of\nany given scheduling algorithm. We find that in general\nthe answer is “no”. However, we show theoretically that\nthe classical Least Slack Time First (LSTF) scheduling algorithm\ncomes closest to being universal and demonstrate\nempirically that LSTF can closely replay a wide range of\nscheduling algorithms. We then evaluate whether LSTF\ncan be usedin practiceto meet various network-wide objectives\nby looking at popular performance metrics (such as\naverage FCT, tail packet delays, and fairness); we find that\nLSTF performs comparable to the state-of-the-art for each\nof them. We also discuss how LSTF can be used in conjunction\nwith active queue management schemes (such as\nCoDel and ECN) without changing the core of the network.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170576", "vector": [], "sparse_vector": [], "title": "Compiling Path Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Measuring the flow of traffic along network paths is crucial for many management tasks, including traffic engineering, diagnosing congestion, and mitigating DDoS attacks. We introduce a declarative query language for efficient path-based traffic monitoring. Path queries are specified as regular expressions over predicates on packet locations and header values, with SQLlike “groupby” constructs for aggregating results anywhere along a path. A run-time system compiles queries into a deterministic finite automaton. The automaton’s transition function is then partitioned, compiled into match-action rules, and distributed over the switches. Switches stamp packets with automaton states to track the progress towards fulfilling a query. Only when packets satisfy a query are the packets counted, sampled, or sent to collectors for further analysis. By processing queries in the data plane, users “pay as they go”, as data-collection overhead is limited to exactly those packets that satisfy the query. We implemented our system on top of the Pyretic SDN controller and evaluated its performance on a campus topology. Our experiments indicate that the system can enable “interactive debugging”— compiling multiple queries in a few seconds—while fitting rules comfortably in modern switch TCAMs and the automaton state into two bytes (e.g., a VLAN header).", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170577", "vector": [], "sparse_vector": [], "title": "Polaris: Faster Page Loads Using Fine-grained Dependency Tracking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To load a web page, a browser must fetch and evaluate objects like HTML files and JavaScript source code. Evaluating an object can result in additional objects being fetched and evaluated. Thus, loading a web page requires a browser to resolve a dependency graph; this partial ordering constrains the sequence in which a browser can process individual objects. Unfortunately, many edges in a page’s dependency graph are unobservable by today’s browsers. To avoid violating these hidden dependencies, browsers make conservative assumptions about which objects to process next, leaving the network and CPU underutilized. We provide two contributions. First, using a new measurement platform called Scout that tracks fine-grained data flows across the JavaScript heap and the DOM, we show that prior, coarse-grained dependency analyzers miss crucial edges: across a test corpus of 200 pages, prior approaches miss 30% of edges at the median, and 118% at the 95th percentile. Second, we quantify the benefits of exposing these new edges to web browsers. We introduce <PERSON><PERSON>, a dynamic client-side scheduler that is written in JavaScript and runs on unmodified browsers; using a fully automatic compiler, servers can translate normal pages into ones that load themselves with Polaris. <PERSON>is uses fine-grained dependency graphs to dynamically determine which objects to load, and when. Since <PERSON>is’ graphs have no missing edges, <PERSON><PERSON> can aggressively fetch objects in a way that minimizes network round trips. Experiments in a variety of network conditions show that <PERSON><PERSON> decreases page load times by 34% at the median, and 59% at the 95th percentile.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170578", "vector": [], "sparse_vector": [], "title": "FairRide: <PERSON><PERSON><PERSON><PERSON><PERSON>, Fair <PERSON>ache Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "Memory caches continue to be a critical component to many systems. In recent years, there has been larger amounts of data into main memory, especially in shared environments such as the cloud. The nature of such environments requires resource allocations to provide both performance isolation for multiple users/applications and high utilization for the systems. We study the problem of fair allocation of memory cache for multiple users with shared files. We find that, surprisingly, no memory allocation policy can provide all three desirable properties (isolation-guarantee, strategy-proofness and Paretoefficiency) that are typically achievable by other types of resources, e.g., CPU or network. We also show that there exist policies that achieve any two of the three properties. We find that the only way to achieve both isolation-guarantee and strategy-proofness is throughblocking, which we efficiently adapt in a new policy calledFairRide. We implementFairRidein a popular memorycentric storage system using an efficient form ofblocking, named asexpected delaying, and demonstrate that FairRide can lead to better cache efficiency (2.6× over isolated caches) and fairness in many scenarios.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170579", "vector": [], "sparse_vector": [], "title": "PhyCloak: Obfuscating Sensing from Communication Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recognition of human activities and gestures using preexisting WiFi signals has been shown to be feasible in recent studies. Given the pervasiveness of WiFi signals, this emerging sort of sensing poses a serious privacy threat. This paper is the first to counter the threat of unwanted or even malicious communication based sensing: it proposes a blackbox sensor obfuscation technique PhyCloak which distorts only the physical information in the communication signal that leaks privacy. The data in the communication signal is preserved and, in fact, the throughput of the link is increased with careful design. Moreover, the design allows coupling of the PhyCloak module with legitimate sensors, so that their sensing is preserved, while that of illegitimate sensors is obfuscated. The effectiveness of the design is validated via a prototype implementation on an SDR platform", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170580", "vector": [], "sparse_vector": [], "title": "Ripple II: Faster Communication through Physical Vibration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We envision physical vibration as a new modality of\ndata communication. In NSDI 2015, our paper reported\nthe feasibility of modulating the vibration of a smartphone’s\nvibra-motor. When in physical contact with another\nsmartphone, the accelerometer of the second phone\nwas able to decode the vibrations at around 200 bits/s.\nThis paper builds on our first prototype, but redesigns\nthe entire radio stack to now achieve 30 kbps. The core\nredesign includes (1) a new OFDM-based physical layer\nthat uses themicrophoneas a receiver (instead of the accelerometer),\nand (2) a MAC layer that detects collision\nat the transmitter and performs proactive symbol retransmissions.\nWe also develop two example applications on\ntop of the vibratory radio: (1) a finger ring that transmits\nvibratory passwords through the finger bone to enable\ntouch based authentication, and (2) surface communication\nbetween devices placed on the same table. The\noverall system entails unique challenges and opportunities,\nincluding ambient sound cancellation, OFDM over\nvibrations, back-EMF based carrier sensing, predictive\nretransmissions, bone conduction, etc. We call our systemRipple IIto suggest the continuity from the NSDI\n2015 paper. We close the paper with a video demo that\nstreams music as OFDM packets through vibrations and\nplays it in real time through the receiver’s speaker.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170581", "vector": [], "sparse_vector": [], "title": "Minimizing Faulty Executions of Distributed Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When troubleshooting buggy executions of distributed\nsystems, developers typically start by manually separating\nout events that are responsible for triggering the\nbug (signal) from those that are extraneous (noise). We\npresent DEMi, a tool for automatically performing this\nminimization. We apply DEMi to buggy executions of two\nvery different distributed systems, Raft and Spark, and\nfind that it produces minimized executions that are between\n1X and 4.6X the size of optimal executions.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170582", "vector": [], "sparse_vector": [], "title": "Social Hash: An Assignment Framework for Optimizing Distributed Systems Operations on Social Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Herald Kllapi", "<PERSON>"], "summary": "How objects are assigned to components in a distributed\nsystem can have a significant impact on performance\nand resource usage.Social Hashis a framework for\nproducing, serving, and maintaining assignments of objects\nto components so as to optimize the operations\nof large social networks, such as Facebook’s Social\nGraph. The framework uses a two-level scheme to decouple\ncompute-intensive optimization from relatively\nlow-overhead dynamic adaptation. The optimization at\nthe first level occurs on a slow timescale, and in our applications\nis based on graph partitioning in order to leverage\nthe structure of the social network. The dynamic\nadaptation at the second level takes place frequently to\nadapt to changes in access patterns and infrastructure,\nwith the goal of balancing component loads. We demonstrate the effectiveness of Social Hash with\ntwo real applications. The first assigns HTTP requests\nto individual compute clusters with the goal of minimizing\nthe (memory-based) cache miss rate; Social Hash decreased\nthe cache miss rate of production workloads by\n25%. The second application assigns data records to storage\nsubsystems with the goal of minimizing the number\nof storage subsystems that need to be accessed on multiget\nfetch requests; Social Hash cut the average response\ntime in half on production workloads for one of the storage\nsystems at Facebook.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170583", "vector": [], "sparse_vector": [], "title": "BeamSpy: Enabling Robust 60 GHz Links Under Blockage.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to high directionality and small wavelengths, 60 GHz links are highly vulnerable to human blockage. To overcome blockage, 60 GHz radios can use a phased-array antenna to search for and switch to unblocked beam directions. However, these techniques are reactive, and only trigger after the blockage has occurred, and hence, they take time to recover the link. In this paper, we proposeBeamSpy, that can instantaneously predict the quality of 60 GHz beams, even under blockage, without the costly beam searching.BeamSpycaptures unique spatial and blockage-invariant correlation among beams through a novel prediction model, exploiting which we can immediately select the best alternative beam direction whenever the current beam’s quality degrades. We applyBeamSpyto a run-timefast beam adaptationprotocol, and ablockage-risk assessmentscheme that can guide blockage-resilient link deployment. Our experiments on a reconfigurable 60 GHz platform demonstrate the effectiveness ofBeamSpy'sprediction framework, and its usefulness in enabling robust 60 GHz links.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170584", "vector": [], "sparse_vector": [], "title": "Mind the Gap: Towards a Backpressure-Based Transport Protocol for the Tor Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Tor has become the prime example for anonymous communication\nsystems. With increasing popularity, though,\nTor is also faced with increasing load. In this paper,\nwe tackle one of the fundamental problems in today’s\nanonymity networks: network congestion. We show that\nthe current Tor design is not able to adjust the load appropriately,\nand we argue that finding good solutions to\nthis problem is hard for anonymity overlays in general.\nThis is due to the long end-to-end delay in such networks,\ncombined with limitations on the allowable feedback due\nto anonymity requirements. We introduce a design for\na tailored transport protocol. It combines latency-based\ncongestion control per overlay hop with a backpressure-based\nflow control mechanism for inter-hop signalling.\nThe resulting overlay is able to react locally and thus\nrapidly to varying network conditions. It allocates available\nresources more evenly than the current Tor design;\nthis is beneficial in terms of both fairness and anonymity.\nWe show that it yields superior performance and improved\nfairness—between circuits, and also between the\nanonymity overlay and concurrent applications.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170585", "vector": [], "sparse_vector": [], "title": "VAST: A Unified Platform for Interactive Network Forensics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network forensics and incident response play a vital role in\nsite operations, but for large networks can pose daunting dif-\nficulties to cope with the ever-growing volume of activity and\nresulting logs. On the one hand, logging sources can generate\ntens of thousands of events per second, which a system supporting\ncomprehensive forensics must somehow continually ingest.\nOn the other hand, operators greatly benefit frominteractiveexploration of disparate types of activity when analyzing an\nincident. In this paper, we present the design, implementation, and\nevaluation of VAST (Visibility Across Space and Time), a distributed\nplatform for high-performance network forensics and\nincident response that provides both continuous ingestion of\nvoluminous event streams and interactive query performance.\nVAST leverages a native implementation of the actor model\nto scale both intra-machine across available CPU cores, and\ninter-machine over a cluster of commodity systems.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170586", "vector": [], "sparse_vector": [], "title": "Decimeter-Level Localization with a Single WiFi Access Point.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Chronos, a system that enables a single WiFi access point to localize clients to within tens of centimeters. Such a system can bring indoor positioning to homes and small businesses which typically have a single access point. The key enabler underlying Chronos is a novel algorithm that can compute sub-nanosecond time-of-flight using commodity WiFi cards. By multiplying the time-of- flight with the speed of light, a MIMO access point computes the distance between each of its antennas and the client, hence localizing it. Our implementation on commodity WiFi cards demonstrates that Chronos’s accuracy is comparable to state-of-the-art localization systems, which use four or five access points.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170587", "vector": [], "sparse_vector": [], "title": "Ernest: Efficient Performance Prediction for Large-Scale Advanced Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "Recent workload trends indicate rapid growth in the\ndeployment of machine learning, genomics and scientific\nworkloads on cloud computing infrastructure. However,\nefficiently running these applications on shared infrastructure\nis challenging and we find that choosing the right\nhardware configuration can significantly improve performance\nand cost. The key to address the above challenge\nis having the ability to predict performance of applications\nunder various resource configurations so that we\ncan automatically choose the optimal configuration. Our insight is that a number of jobs have predictable\nstructure in terms of computation and communication.\nThus we can build performance models based on the behavior\nof the job on small samples of data and then predict\nits performance on larger datasets and cluster sizes.\nTo minimize the time and resources spent in building a\nmodel, we use optimal experiment design, a statistical\ntechnique that allows us to collect as few training points\nas required. We have built Ernest, a performance prediction\nframework for large scale analytics and our evaluation\non Amazon EC2 using several workloads shows\nthat our prediction error is low while having a training\noverhead of less than 5% for long-running jobs.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170588", "vector": [], "sparse_vector": [], "title": "Speeding up Web Page Loads with <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Web page loads are slow due to intrinsic inefficiencies in the page load process. Our study shows that the inefficiencies are attributable not only to the contents and structure of the Web pages (e.g., three-fourths of the CSS resources are not used during the initial page load) but also the way that pages are loaded (e.g., 15% of page load times are spent waiting for parsing-blocking resources to be loaded). To address these inefficiencies, this paper presentsShandian(which means lightening in Chinese) that restructures the page load process to speed up page loads.Shandianexercises control over what portions of the page gets communicated and in what order so that the initial page load is optimized. Unlike previous techniques,Shandianworks on demand without requiring a training period, is compatible with existing latency-reducing techniques (e.g., caching and CDNs), supports security features that enforce same-origin policies, and does not impose additional privacy risks. Our evaluations show thatShandianreduces page load times by more than half for both mobile phones and desktops while incurring modest overheads to data usage.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170589", "vector": [], "sparse_vector": [], "title": "Sieve: Cryptographically Enforced Access Control for User Data in Untrusted Clouds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern web services rob users of low-level control over\ncloud storage—a user’s single logical data set is scattered\nacross multiple storage silos whose access controls are\nset by web services, not users. The consequence is that\nusers lack the ultimate authority to determine how their\ndata is shared with other web services. In this paper, we introduce Sieve, a new platform which\nselectively (and securely) exposes user data to web services.\nSieve has a user-centric storage model: each user\nuploads encrypted data to a single cloud store, and by\ndefault, only the user knows the decryption keys. Given\nthis storage model, Sieve defines an infrastructure to support\nrich, legacy web applications. Using attribute-based\nencryption, Sieve allows users to define intuitively understandable\naccess policies that are cryptographically\nenforceable. Using key homomorphism, <PERSON>eve can reencrypt\nuser data on storage providers in situ, revoking\ndecryption keys from web services without revealing new\nkeys to the storage provider. Using secret sharing and\ntwo-factor authentication, <PERSON><PERSON> protects cryptographic\nsecrets against the loss of user devices like smartphones\nand laptops. The result is that users can enjoy rich, legacy\nweb applications, while benefiting from cryptographically\nstrong controls over which data a web service can access.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170590", "vector": [], "sparse_vector": [], "title": "Earp: Principled Storage, Sharing, and Protection for Mobile Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern mobile apps need to store and share structured\ndata, but the coarse-grained access-control mechanisms in\nexisting mobile operating systems are inadequate to help\napps express and enforce their protection requirements. We design, implement, and evaluate a prototype of\nEarp, a new mobile platform that uses the relational model\nas the unified OS-level abstraction for both storage and\ninter-app services. Earp provides apps with structureaware,\nOS-enforced access control, bringing order and\nprotection to the Wild West of mobile data management.", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}, {"primary_key": "4170591", "vector": [], "sparse_vector": [], "title": "AnonRep: Towards Tracking-Resistant Anonymous Reputation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reputation systems help users evaluate information quality\nand incentivize civilized behavior, often by tallying\nfeedback from other users such as \"likes\" or votes and\nlinking these scores to a user’s long-term identity. This\nidentity linkage enables user tracking, however, and appears\nat odds with strong privacy or anonymity. This\npaper presents AnonRep, a practical anonymous reputation\nsystem offering the benefits of reputation without enabling\nlong-term tracking. AnonRep users anonymously\npost messages, which they can verifiably tag with their\nreputation scores without leaking sensitive information.\nAnonRep reliably tallies other users’ feedback (e.g., likes\nor votes) without revealing the user’s identity or exact\nscore to anyone, while maintaining security against score\ntampering or duplicate feedback. A working prototype\ndemonstrates that AnonRep scales linearly with the number\nof participating users. Experiments show that the latency\nfor a user to generate anonymous feedback is less\nthan ten seconds in a 10,000-user anonymity group", "published": "2016-01-01", "category": "nsdi", "pdf_url": "", "sub_summary": "", "source": "nsdi", "doi": ""}]