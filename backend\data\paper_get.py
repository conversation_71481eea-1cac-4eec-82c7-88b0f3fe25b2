import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import os
import arxiv
import demjson3
import time

from tqdm import tqdm
from datetime import datetime
from datetime import timedelta
from typing import List, Dict, Tuple, Union

import requests
import zipfile
import tempfile

class Utils:
    @staticmethod
    def parse_json(json_str):
        return demjson3.decode(json_str)

    @staticmethod
    def load_json_file(filename, 
                       data_dir: Union[list[str], str] = ['data']):
        '''
        加载 json 文件
        Args:
            filename: 文件名
            data_dir: 数据目录，默认为 ['data']
        Returns:
            papers: 论文 json 数据
        '''
        # 如果 data_dir 是字符串，则转换为列表
        data_dir = [data_dir] if isinstance(data_dir, str) else data_dir
        # 拼接路径
        json_path = os.path.join(ROOT_DIR, *data_dir, filename)
        with open(json_path, 'r', encoding='utf-8') as f:
            papers = demjson3.decode(f.read())
        return papers
    
    @staticmethod
    def save_json_file(data, 
                       filename, 
                       data_dir: Union[list[str], str] = ['data']):
        '''
        保存 json 文件
        Args:
            data: 数据
            filename: 文件名
            data_dir: 数据目录，默认为 ['data']
        Returns:
            file_path: 文件路径
        '''
        # 如果 data_dir 是字符串，则转换为列表
        data_dir = [data_dir] if isinstance(data_dir, str) else data_dir
        # 拼接路径
        save_dir = os.path.join(ROOT_DIR, *data_dir)
        # 创建目录
        os.makedirs(save_dir, exist_ok=True)

        file_path = os.path.join(save_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            json_str = demjson3.encode(data, encoding='utf-8', compactly=False).decode('utf-8')
            f.write(json_str)
        return file_path
    
    @staticmethod
    def get_date_range_by_pre(pre_day=0):
        """
        获取当前日期和前一天的日期
        注意:因为 arxiv 的时区问题，需要提前8小时。同时，因为 arixv 似乎不会实时更新当天论文，所以需要提前一天
        Args:
            pre_day: 前 pre_day 天的论文，默认为0，即今天的论文
        Returns:
            yesterday_date: 前一天的日期，格式为 YYYYMMDD
            current_date: 指定日期，格式为 YYYYMMDD
        """
        # 获得当前日期 20230101 形式的字符串
        today = datetime.now().date()
        now = today - timedelta(hours=8) # 由于arxiv的时区问题，需要提前8小时
        now = now - timedelta(days=pre_day)  # 提前 n 天
        current_date = f"{now.year}{now.month:02d}{now.day:02d}"

        # 获得当前日期前一天的日期 20230101 形式的字符串
        yesterday = now - timedelta(days=1)
        yesterday_date = f"{yesterday.year}{yesterday.month:02d}{yesterday.day:02d}"
        return yesterday_date, current_date
    
    @staticmethod
    def get_date_range_by_day(day: str):
        """
        获取指定日期和明天的日期
        Args:
            day: 日期字符串，格式为 YYYYMMDD, 如 20240101
        Returns:
            current_date: 指定日期，格式为 YYYYMMDDTTTT (其中 TTTT 为24小时制的小时分钟)
            tomorrow_date: 明天的日期，格式为 YYYYMMDDTTTT
        """
        date = datetime.strptime(day, "%Y%m%d")
        tomorrow = date + timedelta(days=1)
        today = f"{date.year}{date.month:02d}{date.day:02d}0000"
        tomorrow = f"{tomorrow.year}{tomorrow.month:02d}{tomorrow.day:02d}0000"

        return today, tomorrow

    @staticmethod
    def get_date_list_by_year(year: int=2024):
        """
        获取指定年份的日期列表
        Args:
            year: 年份，如 2024
        Returns:
            date_list: 日期列表，格式为 YYYYMMDD
        """
        date_list = []
        for month in range(1, 13):
            # 获取下个月的第一天
            if month == 12:
                next_month = datetime(year + 1, 1, 1)
            else:
                next_month = datetime(year, month + 1, 1)
            # 当前月的最后一天就是下个月第一天减一天
            last_day = (next_month - timedelta(days=1)).day
            
            for day in range(1, last_day + 1):
                date_list.append(f"{year}{month:02d}{day:02d}")
        return date_list

class ArxivPaper:
    def __init__(self):
        self.client = arxiv.Client()

    def get_paper_by_day(self, day: str, save_path: str):
        '''
        获取指定日期的论文
        Args:
            day: 日期字符串，格式为 YYYYMMDD，如 20240101
            save_path: 保存数据的目录，默认为['data']
        Returns:
            json_path: 保存数据的路径
        '''
        # 检查文件是否已存在
        filename = f"arxiv_papers_{day}.json"
        json_path = os.path.join(ROOT_DIR, save_path, filename)
        if os.path.exists(json_path):
            print(f"文件 {filename} 已存在，跳过获取")
            return json_path, 1

        # 初始化客户端
        client = arxiv.Client()

        # 获得当前日期 20230101 形式的字符串
        # yesterday_date, current_date = Utils.get_date_range_by_pre(pre_day)
        today, tomorrow = Utils.get_date_range_by_day(day)
        print(f"Search from {today} to {tomorrow}")

        # 使用日期范围搜索
        search_query = f"cat:cs.* AND submittedDate:[{today} TO {tomorrow}]" # 搜索昨天到今天提交的论文

        # 使用API进行搜索
        search = arxiv.Search(
            query=search_query,
            max_results=1000,
            sort_by=arxiv.SortCriterion.SubmittedDate
        )

        # 获取结果
        results = client.results(search)
        results_list = list(results)

        # 创建一个列表来存储论文信息
        papers_data = []

        # 遍历每篇论文，提取所需信息
        for paper in results_list:
            paper_info = {
                "title": paper.title,
                "authors": [author.name for author in paper.authors],
                "summary": paper.summary,
                "published": paper.published.isoformat(),
                "updated": paper.updated.isoformat(),
                "doi": paper.doi,
                "primary_category": paper.primary_category,
                "categories": paper.categories,
                "links": [link.href for link in paper.links],
                "pdf_url": paper.pdf_url,
                "source": "arxiv"
            }
            papers_data.append(paper_info)


        # 将数据保存为JSON文件
        print(f"获取 {day} 的论文数量: {len(papers_data)}")
        if len(papers_data) > 0:
            json_path = Utils.save_json_file(papers_data, filename, data_dir=save_path)
            return json_path, len(papers_data)
        else:
            json_path = ''
            return json_path, 0

    def get_paper_by_year(self, year: int, save_path: str):
        """
        获取指定年份的论文
        Args:
            year: 目标年份，如 2024
            save_path: 保存数据的目录
        """
        # 每年的数据放到一个文件夹
        date_list = Utils.get_date_list_by_year(year)

        # 获取指定年份的论文
        arxiv_paper = ArxivPaper()
        zero_days = 0
        for date in date_list:
            for _ in range(3):
                try:
                    json_path, num = arxiv_paper.get_paper_by_day(date, save_path=save_path)
                    print(f"Get papers from {date} successfully")
                    if num == 0:
                        zero_days += 1
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    print(f"Sleep 30 seconds for retry {_ + 1} times")
                    time.sleep(30)
                    continue
            if zero_days > 2:
                print(f"连续 {zero_days} 天没有获取到论文，跳过剩余日期")
                break

class ACLPaper:
    def __init__(self) -> None:
        ''' 初始化 ACL 论文数据 '''
        self.sources = ["acl", "emnlp", "naacl"]
        self.raw_paper_path = os.path.join(ROOT_DIR, 'data', 'paper_acl')
        self.xml_path = os.path.join(self.raw_paper_path, 'acl-anthology-master', 'data', 'xml')

    def get_all_conference_names(self, year=2024):
        """
        获取 data 下所有 acl 系列论文会议的名称
        
        Args:
            year: 目标年份，默认为2024
            
        Returns:
            list: 会议名称列表
        """
        xml_path = self.xml_path
        
        # 确保 xml 目录存在
        if not os.path.exists(xml_path):
            self.clone_acl_anthology()
            
        # 查找指定年份的 xml 文件
        conference_names = []
        year_str = str(year)
        
        for file_name in os.listdir(xml_path):
            if file_name.startswith(year_str) and file_name.endswith('.xml'):
                # 从文件名中提取会议名称，例如 2024.acl.xml -> acl
                parts = file_name.split('.')
                if len(parts) >= 3:
                    conference_name = parts[1]
                    conference_names.append(conference_name)
        
        return conference_names

    def get_all_valid_conference_names(self, year=2024):
        """
        获取 data 下所有 acl 系列论文会议的名称
        并且这些会议对应的文件中论文数量大于0
        """
        conference_names = self.get_all_conference_names(year)
        conference_paths = []
        
        for conference_name in conference_names:
            path = os.path.join(self.xml_path, f"{year}.{conference_name}.xml")
            if os.path.exists(path):
                conference_paths.append(path)

        valid_names = []
        for path, name in zip(conference_paths, conference_names):
            papers = self.get_paper_by_path(path, year)
            if len(papers) > 100:
                valid_names.append(f"{name} - {len(papers)}")
        return valid_names

    def clone_acl_anthology(self):
        import subprocess

        repo_dir = os.path.join(self.raw_paper_path, 'acl-anthology')
        xml_dir = os.path.join(repo_dir, 'data', 'xml')

        if not os.path.exists(repo_dir):
            # clone 仓库
            try:
                os.makedirs(self.raw_paper_path, exist_ok=True)
                subprocess.check_call(['git', 'clone', 'https://github.com/acl-org/acl-anthology.git', repo_dir])
            except Exception as e:
                print(f"克隆 ACL Anthology 仓库失败: {e}")
                raise
        else:
            # pull 最新
            try:
                subprocess.check_call(['git', '-C', repo_dir, 'pull'])
            except Exception as e:
                print(f"更新 ACL Anthology 仓库失败: {e}")
                raise

        return xml_dir

    def get_paper_path(self, sources: list[str]=None, year: int=2010):
        ''' 获取指定来源的论文路径
        Args:
            sources: 论文来源
            start_year: 开始年份
        Returns:
            path_map: 论文路径字典, 格式为 {source: {year: path}}
        '''
        if not sources:
            sources = self.sources

        # 1. 获取论文路径
        xml_path = self.clone_acl_anthology()

        # 2. 加载路径下的所有文件，获取文件路径
        path_map = {source: {} for source in sources}
        files = os.listdir(xml_path)
        for file in files:
            if file.endswith('.xml'):
                cur_year = file.split('.')[0]
                source = file.split('.')[1]
                # 判断 year 是否为数字
                if not cur_year.isdigit():
                    continue
                if int(cur_year) == year and source in sources:
                    path_map[source][cur_year] = os.path.join(xml_path, file)

        return path_map
    
    def get_paper_by_path(self, path: str, source: str):
        ''' 获取指定路径文件的论文列表 '''

        def parse_paper_xml(xml_content: str) -> List[Dict]:
            """
            解析 ACL 论文 XML 内容
            Args:
                xml_content: XML 文件内容
            Returns:
                papers: 论文列表，每个论文包含 title, authors, summary, published, pdf_url 字段
            """
            import xml.etree.ElementTree as ET
            import re
            
            # 解析 XML
            root = ET.fromstring(xml_content)
            papers = []
            
            volumes = root.findall('.//volume')
            if not volumes:
                return papers
            
            def get_text(elem):
                if elem is None:
                    return ''
                elem_str = ET.tostring(elem, encoding='unicode')
                # 删除所有 XML 标签，只保留文本内容
                elem_text = re.sub(r'<[^>]+>', '', elem_str)
                elem_text = elem_text.replace('\n', '').strip()
                return elem_text

            # 遍历所有论文
            for volume_elem in volumes:
                if volume_elem is None or len(volume_elem) == 0: continue
                for paper_elem in volume_elem.findall('.//paper'):
                    if paper_elem is None or len(paper_elem) == 0: continue

                    paper_info = {}
                    # 获取标题
                    title_elem = paper_elem.find('title')
                    if title_elem is not None:
                        paper_info['title'] = get_text(title_elem)
                    
                    # 获取作者
                    authors = []
                    for author_elem in paper_elem.findall('author'):
                        first = author_elem.find('first')
                        last = author_elem.find('last')
                        if first is not None and last is not None:
                            authors.append(f"{first.text} {last.text}")
                    paper_info['authors'] = authors
                    
                    # 获取摘要
                    abstract_elem = paper_elem.find('abstract')
                    if abstract_elem is not None and get_text(abstract_elem) != '':
                        paper_info['summary'] = get_text(abstract_elem)
                    else:
                        paper_info['summary'] = get_text(title_elem)
                    
                    # 获取发布日期
                    meta = volume_elem.find('.//meta')
                    if meta is not None:
                        year = meta.find('year')
                        if year is not None:
                            paper_info['published'] = f"{year.text}-01-01"  # ACL 论文通常没有具体发布日期，使用年份的第一天
                    
                    # 获取 PDF URL
                    url_elem = paper_elem.find('url')
                    doi_elem = paper_elem.find('doi')
                    if url_elem is not None:
                        paper_info['pdf_url'] = f"https://aclanthology.org/{url_elem.text}"
                    elif doi_elem is not None:
                        paper_info['pdf_url'] = f"https://aclanthology.org/{doi_elem.text.split('/')[-1]}"
                    else:
                        paper_info['pdf_url'] = f"none"

                    papers.append(paper_info)
            
            return papers
        
        with open(path, 'r', encoding='utf-8') as f:
            paper = f.read()
        papers = parse_paper_xml(paper)
        for paper in papers:
            paper['source'] = source
            paper['categories'] = [source]
            paper['primary_category'] = source
        return papers

    def get_and_save_papers(self, save_path: list[str] = None, sources: list[str] = None, year: int=2010):
        ''' 获取并保存论文列表到 json 文件 
        Args:
            save_path: 保存路径
            sources: 论文来源
            year: 年份
        '''
        if not sources:
            sources = self.sources

        # 1. 获取论文路径
        path_map = self.get_paper_path(sources=sources, year=year)

        # 2. 获取论文列表并分别保存
        for source in sources:
            for year in path_map[source]:
                papers = self.get_paper_by_path(path_map[source][year], source)
                if len(papers) == 0: continue
                filename = f"{source}_{year}.json"
                Utils.save_json_file(papers, filename, data_dir=save_path)

if __name__ == "__main__":
    # 1. 获取所有会议名称
    acl_paper = ACLPaper()
    conference_names = acl_paper.get_all_valid_conference_names(year=2025)
    # conference_names = acl_paper.get_all_conference_names(year=2024)
    print(len(conference_names),  conference_names)

    # 2. 获取 CVPR 论文
    # cvpr_paper = CVPRPaper(year=2024)
    # cvpr_paper.get_papers()
    # cvpr_paper.save_papers(path="E:\\project\\arxiv-insight\\backend\\data\\paper_cvpr\\paper")

