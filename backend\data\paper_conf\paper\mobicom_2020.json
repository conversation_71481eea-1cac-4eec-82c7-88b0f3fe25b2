[{"primary_key": "2660479", "vector": [], "sparse_vector": [], "title": "MET: a magneto-inductive sensing based electric toothbrushing monitoring system.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Electric toothbrushes are widely used for home oral care, but many users do not achieve desired hygiene results due to insufficient brushing coverage or incorrect brushing techniques. Existing electric toothbrushing monitoring systems fail to detect these issues because they cannot achieve fine-grained position tracking. In this paper, we present a novel electric toothbrushing monitoring system called MET that tracks brushing coverage for all the 15 surfaces of teeth and detects different types of incorrect brushing techniques. This design is inspired by our observation that the motor inside an electric toothbrush generates a unique magnetic field, which can serve as a reliable signal for position and orientation tracking. MET is the first system that tracks both the position and orientation of an unmodified electric motor using magnetic inductive sensing. Experiments with fourteen users show that the average toothbrushing surface recognition accuracy of MET is 85.3%. Moreover, MET is robust to user location changes and posture variations and does not require any training from the users. Experimental results also demonstrate our significant advantages over existing commercial systems.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380896"}, {"primary_key": "2660480", "vector": [], "sparse_vector": [], "title": "Self-reconfigurable micro-implants for cross-tissue wireless and batteryless connectivity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the design, implementation, and evaluation of μmedIC, a fully-integrated wireless and batteryless micro-implanted sensor. The sensor powers up by harvesting energy from RF signals and communicates at near-zero power via backscatter. In contrast to prior designs which cannot operate across various in-body environments, our sensor can self-reconfigure to adapt to different tissues and channel conditions. This adaptation is made possible by two key innovations: a reprogrammable antenna that can tune its energy harvesting resonance to surrounding tissues, and a backscatter rate adaptation protocol that closes the feedback loop by tracking circuit-level sensor hints.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419216"}, {"primary_key": "2660482", "vector": [], "sparse_vector": [], "title": "BeeCast: a collaborative video streaming system.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose BeeCast, a collaborative video streaming system, that allows a group of mobile users to cooperate in streaming video content. The novelty of the proposed system is that it allows each user to watch the same video content on their own device at the same time. This entails proposing a method to exchange the downloaded video chunks among the users using device-to-device communication. The proposed system is composed of two components: BeeBuzzer, and BeePlanner. The BeePlanner component performs chunk scheduling, while the BeeBuzzer component manages and coordinates the chunk exchange among devices.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417314"}, {"primary_key": "2660483", "vector": [], "sparse_vector": [], "title": "Edge-SLAM: edge-assisted visual simultaneous localization and mapping.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recent advances in mobile devices have allowed them to run spatial sensing algorithms such as Visual Simultaneous Localization and Mapping (Visual-SLAM). However, the resource requirements of Visual-SLAM prevents long-operation of such algorithm on mobile devices. We demonstrate Edge-SLAM [2], a system that adapts edge computing into Visual-SLAM through a split architecture. Edge-SLAM offloads the compute-intensive modules of Visual-SLAM to the edge without losing accuracy. Our experiments show that Edge-SLAM architecture reduces the use of computation and memory resources on mobile devices and keeps it constant. Thus, enabling long-operation of Visual-SLAM along with other applications services on mobile devices.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417326"}, {"primary_key": "2660484", "vector": [], "sparse_vector": [], "title": "Deep learning based wireless localization for indoor navigation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Location services, fundamentally, rely on two components: a mapping system and a positioning system. The mapping system provides the physical map of the space, and the positioning system identifies the position within the map. Outdoor location services have thrived over the last couple of decades because of well-established platforms for both these components (e.g. Google Maps for mapping, and GPS for positioning). In contrast, indoor location services haven't caught up because of the lack of reliable mapping and positioning frameworks. Wi-Fi positioning lacks maps and is also prone to environmental errors. In this paper, we present DLoc, a Deep Learning based wireless localization algorithm that can overcome traditional limitations of RF-based localization approaches (like multipath, occlusions, etc.). We augment DLoc with an automated mapping platform, MapFind. MapFind constructs location-tagged maps of the environment and generates training data for DLoc. Together, they allow off-the-shelf Wi-Fi devices like smartphones to access a map of the environment and to estimate their position with respect to that map. During our evaluation, MapFind has collected location estimates of over 105 thousand points under 8 different scenarios with varying furniture positions and people motion across two different spaces covering 2000 sq. Ft. DLoc outperforms state-of-the-art methods in Wi-Fi-based localization by 80% (median & 90th percentile) across the two different spaces.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380894"}, {"primary_key": "2660485", "vector": [], "sparse_vector": [], "title": "Accelerometer-based smartphone eavesdropping.", "authors": ["Zhongjie Ba", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this demonstration, we show that audio signals emitted by a smartphone speaker can be captured by the accelerometer on the same smartphone, and accelerometers on recently released smartphones can cover most of the fundamental frequency band of adult speech. Based on these pivotal observations, we present AccelEve, a new side channel attack that allows smartphone applications to eavesdrop on the smartphone speaker without the requirement of sensitive system permissions. Through analyzing the accelerometer measurements of a smartphone, AccelEve is able to: 1) recognize the speech information (text) carried by the acceleration signal; 2) reconstruct the audio signal played by the smartphone speaker. This demo will present experimental validations for our observations and the proposed system.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417323"}, {"primary_key": "2660486", "vector": [], "sparse_vector": [], "title": "Bleep: motor-enabled audio side-channel for constrained UAVs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Small unmanned autonomous vehicles (UAVs) swarms are becoming ubiquitous in a number of applications (e.g., surveying, monitoring, and situational awareness). Indoor environments may contain metal equipment that temporarily disrupts radio reception. During these momentary interruptions, a small UAV needs to be able to broadcast a 'heartbeat' to indicate that it is not damaged or lost. Considering alternative messaging modalities, we observe that light-based methods require line-of sight, which is not guaranteed when UAVs are moving through a cluttered environment, while a naive sound-based method is easily drowned out by the UAV's own loud motor and propeller noise.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419183"}, {"primary_key": "2660488", "vector": [], "sparse_vector": [], "title": "SociTrack: infrastructure-free interaction tracking through mobile sensor networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social scientists, psychologists, and epidemiologists use empirical human interaction data to research human behaviour, social bonding, and disease spread. Historically, systems measuring interactions have been forced to choose between deployability and measurement fidelity---they operate only in instrumented spaces, under line-of-sight conditions, or provide coarse-grained proximity data. We introduce SociTrack, a platform for autonomous social interaction tracking via wireless distance measurements. Deployments require no supporting infrastructure and provide sub-second, decimeter-accurate ranging information over multiple days. The key insight that enables both deployability and fidelity in one system is to decouple node mobility and network management from range measurement, which results in a novel dual-radio architecture. SociTrack leverages an energy-efficient and scalable ranging protocol that is accurate to 14.8 cm (99th percentile) in complex indoor environments and allows our prototype to operate for 12 days on a 2000 mAh battery. Finally, to validate its deployability and efficacy, SociTrack is used by early childhood development researchers to capture caregiver-infant interactions.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419190"}, {"primary_key": "2660490", "vector": [], "sparse_vector": [], "title": "RFGo: a seamless self-checkout system for apparel stores using RFID.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Retailers are aiming to enhance customer experience by automating the checkout process. The key impediment here is the effort to manually align the product barcode with the scanner, requiring sequential handling of items without blocking the line-of-sight of the laser beam. While recent systems such as Amazon Go eliminate human involvement using an extensive array of cameras, we propose a privacy-preserving alternative, RFGo, that identifies products using passive RFID tags. Foregoing continuous monitoring of customers throughout the store, RFGo scans the products in a dedicated checkout area that is large enough for customers to simply walk in and stand until the scan is complete (in two seconds). Achieving such low-latency checkout is not possible with traditional RFID readers, which decode tags using one antenna at a time. To overcome this, RFGo includes a custom-built RFID reader that simultaneously decodes a tag's response from multiple carrier-level synchronized antennas enabling a large set of tag observations in a very short time. RFGo then feeds these observations to a neural network that accurately distinguishes the products within the checkout area from those that are outside. We build a prototype of RFGo and evaluate its performance in challenging scenarios. Our experiments show that RFGo is extremely accurate, fast and well-suited for practical deployment in apparel stores.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419211"}, {"primary_key": "2660496", "vector": [], "sparse_vector": [], "title": "Homecoming: a wireless homing device for UAVs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unmanned Aerial Vehicles (UAVs) are quickly becoming a viable delivery platform for physical packages with promise to transform the retail industry's supply chains. This work focuses on the last leg of such delivery: physically approaching a customer's landing zone. This has traditionally relied on a combination of GPS and computer-vision to locate and identify a landing zone. Instead of using computer vision, we propose to use ultra-wideband beacons (UWB) to assist in the landing process. The UAV's location relative to the landing zone is continuously measured based on the wireless propagation delay between the UAV and the landing zone's corners. We show that a single pair of wireless devices, one at the UAV and one at the landing zone, suffices to obtain the UAV's location. The landing zone's UWB device, connected to multiple antennas, receives multiple copies of the UAV's signals, that enables a sub-decimeter 3D-localization of the UAV. This helps the UAV's control logic governing the approach and landing process.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418157"}, {"primary_key": "2660502", "vector": [], "sparse_vector": [], "title": "The implementation of stigmergy in network-assisted multi-agent system.", "authors": ["<PERSON><PERSON>", "<PERSON>gpeng Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-agent system (MAS) needs to mobilize multiple simple agents to complete complex tasks. However, it is difficult to coherently coordinate distributed agents by means of limited local information. In this demo, we propose a decentralized collaboration method named as \"stigmergy\" in network-assisted MAS, by exploiting digital pheromones (DP) as an indirect medium of communication and utilizing deep reinforcement learning (DRL) on top. Correspondingly, we implement an experimental platform, where KHEPERA IV robots form targeted specific shapes in a decentralized manner. Experimental results demonstrate the effectiveness and efficiency of the proposed method. Our platform could be conveniently extended to investigate the impact of network factors (e.g., latency, data rate, etc).", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417318"}, {"primary_key": "2660504", "vector": [], "sparse_vector": [], "title": "ThermoWave: a new paradigm of wireless passive temperature monitoring via mmWave sensing.", "authors": ["Baicheng Chen", "Huining Li", "<PERSON><PERSON><PERSON>", "Xingyu Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Temperature sensor is one of the most widespread technologies in the IoT era. Wireless temperature monitoring systems are convenient to deploy and can drive mass applications in the fields of smart home, transportation and logistics. Currently, wireless temperature monitoring products are based on microelectronic and semiconductor components, which are not cost-effective (e.g., a few dollars) and more importantly, generate electronic wastes. In this work, we present ThermoWave, a new paradigm of wireless temperature monitoring that is ecological, battery-less, and ultra-low cost. Specifically, ThermoWave is on the basis of the thermal scattering effect on millimeter-wave (mmWave) signals. Specifically, cholesteryl materials align their molecular patterns at different environmental temperatures, and this temperature-induced pattern change will be modulated and sensed by the scattered mmWave signals. There are three functional modules in the ThermoWave system. The ThermoTag is a cholesteryl material inked film or paper tag that can be conveniently attached to the object of interest to monitor temperature changes. Each ThermoTag costs less than 0.01 dollars. The temperature modulated mmWave scattering will be received by a mmWave-radar based ThermoScanner and demodulated by a software-based temperature decoder ThermoSense, which includes a model-based method (i.e., ThermoDot) for point temperature estimation and a data-driven method (i.e., ThermoNet) for thermal imaging. We prototype and evaluate the ThermoWave system performance in both controlled and real-world setups. Experimental results show that the ThermoWave achieves the precision of ±1.0°F in the range of 30°F to 120°F in a controlled setup. We also investigate the performance in real-world applications, and the ThermoWave can reach the ±3.0°F precision in the temperature estimation. We also test and discuss sustainability, durability, robustness, and cost-effectiveness of the ThermoWave in both design and experiments.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419184"}, {"primary_key": "2660508", "vector": [], "sparse_vector": [], "title": "Sniffing visible light communication through walls.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Visible light communication (VLC) is gaining a significant amount of interest as a new paradigm to meet rapidly increasing demands on wireless capacity required by a digitalized world. VLC is considered as a secure wireless communication scheme because VLC signals can be easily constrained within physical boundaries. In this paper, for the first time, we show that VLC is not as secure as people thought: VLC can be sniffed through walls! The key principle behind this is that in VLC transmissions, a VLC transmitter not only emits visible light signals but also leaks out 'side channel RF signals'. The leaked RF signals can be sniffed by a receiver to decode the VLC transmissions even the receiver is blocked (e.g., by walls) from the VLC transmitter. In this work, we establish a theoretical model to quantify the amplitude of the leaked RF signal and verify the model with comprehensive experiments. We design and implement a VLC sniffing system including receiver coil design, signal processing and frame decoding, spanning across hardware and software. Field studies show that with a cheap receiver design, our system can simultaneously sniff transmissions from multiple VLC transmitters 6.4 meters away with a 14 cm concrete wall in between, where the distance exceeds the communication range of most state-of-the-art VLC systems. By simply twining a wired earphone on the arm, we can sniff the VLC transmission 1.9 meters away.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419187"}, {"primary_key": "2660510", "vector": [], "sparse_vector": [], "title": "iCellSpeed: increasing cellular data speed with device-assisted cell selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose iCellSpeed, an on-device solution to increase data access speed by substantiating unrealized performance potentials. We find that performance potentials are missed in today's mobile networks, as the data speed a user device gets is much lower than what the device could get. The issue is rooted in the current cell selection practice, which misses good candidate cells that offer faster access speed, thus under-utilizing the available capabilities in mobile networks. We design iCellSpeed to facilitate network-controlled cell selection with proactive device-side assistance towards more desirable cells. Our evaluation over AT&T and Verizon confirms its effectiveness. iCellSpeed increases data access speed by more than 10 Mbps at 79% of test locations (> 25Mbps at 29% of locations, up to 80.6 Mbps). It doubles access speed at 62.5% of locations with the gain up to 28.4x. Datasets are available at [9].", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419201"}, {"primary_key": "2660514", "vector": [], "sparse_vector": [], "title": "CarML: distributed machine learning in vehicular clouds.", "authors": ["<PERSON><PERSON>", "Yicheng Shen", "<PERSON>"], "summary": "This paper presents CarML, a distributed machine learning platform built on top of an emerging computing paradigm, vehicular clouds. We discuss our design and technical challenges, followed by our preliminary solutions. We verify the efficacy of our solutions using a customized simulator based on Python and SUMO.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418168"}, {"primary_key": "2660515", "vector": [], "sparse_vector": [], "title": "ScatterMIMO: enabling virtual MIMO with smart surfaces.", "authors": ["Manide<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the last decade, the bandwidth expansion and MIMO spatial multiplexing have promised to increase data throughput by orders of magnitude. However, we are yet to enjoy such improvement in real-world environments, as they lack rich scattering and preclude effective MIMO spatial multiplexing. In this paper, we present ScatterMIMO, which uses smart surface to increase the scattering in the environment, to provide MIMO spatial multiplexing gain. Specifically, smart surface pairs up with a wireless transmitter device say an active AP and re-radiates the same amount of power as any active access point (AP), thereby creating virtual passive APs. ScatterMIMO avoids the synchronization, interference, and power requirements of conventional distributed MIMO systems by leveraging virtual passive APs, allowing its smart surface to provide spatial multiplexing gain, which can be deployed at a very low cost. We show that with optimal placement, these virtual APs can provide signals to their clients with power comparable to real active APs, and can increase the coverage of an AP. Furthermore, we design algorithms to optimize ScatterMIMO's smart surface for each client with minimal measurement overhead and to overcome random per-packet phase offsets during the measurement. Our evaluations show that with commercial off-the-shelf MIMO WiFi (11ac) AP and unmodified clients, ScatterMIMO provides a median throughput improvement of 2 X over the active AP alone.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380887"}, {"primary_key": "2660516", "vector": [], "sparse_vector": [], "title": "Towards flexible wireless charging for medical implants using distributed antenna system.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents the design, implementation and evaluation of In-N-Out, a software-hardware solution for far-field wireless power transfer. In-N-Out can continuously charge a medical implant residing in deep tissues at near-optimal beamforming power, even when the implant moves around inside the human body. To accomplish this, we exploit the unique energy ball pattern of distributed antenna array and devise a backscatter-assisted beamforming algorithm that can concentrate RF energy on a tiny spot surrounding the medical implant. Meanwhile, the power levels on other body parts stay in low level, reducing the risk of overheating. We proto-type In-N-Out on 21 software-defined radios and a printed circuit board (PCB). Extensive experiments demonstrate that In-N-Out achieves 0.37 mW average charging power inside a 10 cm-thick pork belly, which is sufficient to wirelessly power a range of commercial medical devices. Our head-to-head comparison with the state-of-the-art approach shows that In-N-Out achieves 5.4X-18.1X power gain when the implant is stationary, and 5.3X-7.4X power gain when the implant is in motion.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380899"}, {"primary_key": "2660517", "vector": [], "sparse_vector": [], "title": "FaceRevelio: a face liveness detection system for smartphones with a single front camera.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON>"], "summary": "Facial authentication mechanisms are gaining traction on smartphones because of their convenience and increasingly good performance of face recognition systems. However, mainstream systems use traditional 2D face recognition technologies, which are vulnerable to various spoofing attacks. Existing systems perform liveness detection via specialized hardware, such as infrared dot projectors and dedicated cameras. Although effective, such methods do not align well with the smartphone industry's desire to maximize screen space.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419206"}, {"primary_key": "2660520", "vector": [], "sparse_vector": [], "title": "LMAC: efficient carrier-sense multiple access for LoRa.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Current LoRa networks including those following the LoRaWAN specification use the primitive ALOHA mechanism for media access control due to LoRa's lack of carrier sense capability. From our extensive measurements, the Channel Activity Detection (CAD) feature that is recently introduced to LoRa for energy-efficiently detecting preamble chirps, can also detect payload chirps reliably. This sheds light on an efficient carrier-sense multiple access (CSMA) protocol that we call LMAC for LoRa networks. This paper presents the designs of three advancing versions of LMAC that respectively implements CSMA, balances the communication loads among the channels defined by frequencies and spreading factors based on the end nodes' local information and then additionally the gateway's global information. Experiments on a 50-node lab testbed and a 16-node university deployment show that, compared with ALOHA, LMAC brings up to 2.2× goodput improvement and 2.4× reduction of radio energy per successfully delivered frame. Thus, should the LoRaWAN's ALOHA be replaced with LMAC, network performance boosts can be realized.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419200"}, {"primary_key": "2660521", "vector": [], "sparse_vector": [], "title": "Deaf-aid: mobile IoT communication exploiting stealthy speaker-to-gyroscope channel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Muertikepu <PERSON>uermaimaiti", "Jinsong Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Internet of Things (IoT) devices are hindered from communicating with their neighbors by incompatible protocols or electromagnetic interference. Existing solutions adopting physical covert channels have limitations in receiver distinction, additional hardware, conditional placement, or physical contact. Our system, Deaf-Aid, utilizes the stealthy speaker-to-gyroscope channel to build robust protocol-independent communication with automatic receiver identification. Deaf-Aid exploits ultrasonic signals at a frequency corresponding to the target receiver, forcing the gyroscope inside to resonate, so as to convey information. We probe the relationship among axes in a gyroscope to surmount frequency offset ingeniously and support multi-channel communication. Meanwhile, Deaf-Aid identifies the receivers automatically via device fingerprints constituted by the diversity of resonant frequency ranges. Furthermore, we entitle Deaf-Aid the capability of mobile communication which is an essential demand for IoT devices. We address the challenge of accurate signals recovery from motion interference. Extensive evaluations demonstrate that Deaf-Aid yields 47bps with BER lower than 1% under motion interference. To our best knowledge, Deaf-Aid is the first work to enable stealthy mobile IoT communication on the basis of inertial motion sensors.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419210"}, {"primary_key": "2660522", "vector": [], "sparse_vector": [], "title": "Single shot single antenna path discovery in THz networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "THz communication has the potential to realize an order of magnitude increase in data rates due to the availability of wide THz-scale spectral bands. Unfortunately, establishing and managing highly directional beams in THz networks is challenging as links lack the \"pseudo-omni\" reception capability of lower bands and the product of AP-client beam resolution is high due to narrow beams of only a few degrees. In this paper, we present One-shot Path discovEry with a THz RAinbow (OPERA), a novel system that identifies dominant paths between the AP and all clients in order to efficiently steer directional beams. The key idea is to embed path direction into the inherent characteristics of signals traveling along each path. To do so, we exploit a single leaky wave antenna and create a THz Rainbow. A THz Rainbow transmission consists of distinct signals with unique spectral characteristics across the angular domain. Leveraging the spatial-spectral signatures in the THz Rainbow, all receivers can correlate the measured signal with the known transmission signatures to discover the sender's path directions in one-shot. Our experiments demonstrate that OPERA achieves average direction estimates within 2° of ground truth for LOS and reflected paths.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380895"}, {"primary_key": "2660524", "vector": [], "sparse_vector": [], "title": "TinyLink 2.0: integrating device, cloud, and client development for IoT applications.", "authors": ["Gaoyang Guan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The recent years have witnessed the rapid growth of IoT (Internet of Things) applications. A typical IoT application usually consists of three essential parts: the device side, the cloud side, and the client side. The development of a complete IoT application is very difficult for non-expert developers because it involves drastically different technologies and complex interactions between different sides. Unlike traditional IoT development platforms which use separate approaches for these three sides, we present TinyLink 2.0, an integrated IoT development approach with a single coherent language. It achieves high expressiveness for diverse IoT applications by an enhanced IFTTT rule design and a virtual sensor mechanism which helps developers express application logic with machine learning. Moreover, TinyLink 2.0 optimizes the IoT application performance by using both static and dynamic optimizers, especially for resource-constrained IoT devices. We implement TinyLink 2.0 and evaluate it with eight case studies, a user study, and a detailed evaluation of the proposed programming language as well as the performance optimizers. Results show that TinyLink 2.0 can speed up IoT development significantly compared with existing approaches from both industry and academia, while still achieving high expressiveness.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380890"}, {"primary_key": "2660526", "vector": [], "sparse_vector": [], "title": "Contactless seismocardiography via deep learning radars.", "authors": ["Unsoo Ha", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The seismocardiogram (SCG) is a recording of a human heart's mechanical activity. It captures fine-grained cardiovascular events such as the opening and closing of heart valves and the contraction and relaxation of heart chambers. Today, SCG recordings are obtained by strapping an accelerometer at the apex of the heart to measure chest wall vibrations. These recordings can be used to diagnose and monitor various cardiovascular conditions including myocardial infarction (heart attack), coronary heart disease, and ischemia.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419982"}, {"primary_key": "2660527", "vector": [], "sparse_vector": [], "title": "ViVo: visibility-aware mobile volumetric video streaming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we perform a first comprehensive study of mobile volumetric video streaming. Volumetric videos are truly 3D, allowing six degrees of freedom (6DoF) movement for their viewers during playback. Such flexibility enables numerous applications in entertainment, healthcare, education, etc. However, volumetric video streaming is extremely bandwidth-intensive. We conduct a detailed investigation of each of the following aspects for point cloud streaming (a popular volumetric data format): encoding, decoding, segmentation, viewport movement patterns, and viewport prediction. Motivated by the observations from the above study, we propose ViVo, which is to the best of our knowledge the first practical mobile volumetric video streaming system with three visibility-aware optimizations. ViVo judiciously determines the video content to fetch based on how, what and where a viewer perceives for reducing bandwidth consumption of volumetric video streaming. Our evaluations over real wireless networks (including commercial 5G), mobile devices and users indicate that ViVo can save on average 40% of data usage (up to 80%) with virtually no drop in visual quality.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380888"}, {"primary_key": "2660529", "vector": [], "sparse_vector": [], "title": "Using magnetic fingerprints to position cars on multi-layer roads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Today's GPS navigators have difficulty determining altitude on multi-layer roads. In this paper, we first explain how the structures of overpasses and bridges have special magnetic fields. We then propose how to use the magnetometers which come with off-the-shelf smartphones to assist GPS navigators to determine position on multi-layer roads by detecting and matching these magnetic fingerprints. We also present performance evaluation on different multi-layer roads to show the feasibility of the proposed method.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418156"}, {"primary_key": "2660530", "vector": [], "sparse_vector": [], "title": "CLIO: enabling automatic compilation of deep learning pipelines across IoT and cloud.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have seen dramatic advances in low-power neural accelerators that aim to bring deep learning analytics to IoT devices; simultaneously, there have been considerable advances in the design of low-power radios to enable efficient compute offload from IoT devices to the cloud. Neither is a panacea --- deep learning models are often too large for low-power accelerators and bandwidth needs are often too high for low-power radios. While there has been considerable work on deep learning for smartphone-class devices, these methods do not work well for small battery-powered IoT devices that are considerably more resource-constrained.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419215"}, {"primary_key": "2660531", "vector": [], "sparse_vector": [], "title": "Airdropping sensor networks from drones and insects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the first system that can airdrop wireless sensors from small drones and live insects. In addition to the challenges of achieving low-power consumption and long-range communication, airdropping wireless sensors is difficult because it requires the sensor to survive the impact when dropped in mid-air. Our design takes inspiration from nature: small insects like ants can fall from tall buildings and survive because of their tiny mass and size. Inspired by this, we design insect-scale wireless sensors that come fully integrated with an onboard power supply and a lightweight mechanical actuator to detach from the aerial platform. Our system introduces a first-of-its-kind 37 mg mechanical release mechanism to drop the sensor during flight, using only 450 μJ of energy as well as a wireless communication link that can transmit sensor data at 33 kbps up to 1 km. Once deployed, our 98 mg wireless sensor can run for 1.3-2.5 years when transmitting 10-50 packets per hour on a 68 mg battery. We demonstrate attachment to a small 28 mm wide drone and a moth (Manduca sexta) and show that our insect-scale sensors flutter as they fall, suffering no damage on impact onto a tile floor from heights of 22 m.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419981"}, {"primary_key": "2660534", "vector": [], "sparse_vector": [], "title": "SDR receiver using commodity wifi via physical-layer signal reconstruction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the explosive increase in wireless devices, physical-layer signal analysis has become critically beneficial across distinctive domains including interference minimization in network planning, security and privacy (e.g., drone and spycam detection), and mobile health with remote sensing. While SDR is known to be highly effective in realizing such services, they are rarely deployed or used by the end-users due to the costly hardware ~1K USD (e.g., USRP). Low-cost SDRs (e.g., RTL-SDR) are available, but their bandwidth is limited to 2-3 MHz and operation range falls well below 2.4 GHz - the unlicensed band holding majority of the wireless devices. This paper presents SDR-Lite, the first zero-cost, software-only software defined radio (SDR) receiver that empowers commodity WiFi to retrieve the In-phase and Quadrature of an ambient signal. With the full compatibility to pervasively-deployed WiFi infrastructure (without any change to the hardware and firmware), SDR-Lite aims to spread the blessing of SDR receiver functionalities to billions of WiFi users and households to enhance our everyday lives. The key idea of SDR-Lite is to trick WiFi to begin packet reception (i.e., the decoding process) when the packet is absent, so that it accepts ambient signals in the air and outputs corresponding bits. The bits are then reconstructed to the original physical-layer waveform, on which diverse SDR applications are performed. Our comprehensive evaluation shows that the reconstructed signal closely reassembles the original ambient signal (>85% correlation). We extensively demonstrate SDR-Lite effectiveness across seven distinctive SDR receiver applications under three representative categories: (i) RF fingerprinting, (ii) spectrum monitoring, and (iii) (ZigBee) decoding. For instance, in security applications of drone and rogue WiFi AP detection, SDR-Lite achieves 99% and 97% accuracy, which is comparable to USRP.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419189"}, {"primary_key": "2660535", "vector": [], "sparse_vector": [], "title": "mmVib: micrometer-level vibration measurement with mmwave radar.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vibration measurement is a crucial task in industrial systems, where vibration characteristics reflect the health and indicate anomalies of the objects. Previous approaches either work in an intrusive manner or fail to capture the micrometer-level vibrations. In this work, we propose mmVib, a practical approach to measure micrometer-level vibrations with mmWave radar. By introducing a Multi-Signal Consolidation (MSC) model to describe the properties of the reflected signals, we exploit the inherent consistency among those signals to accurately recover the vibration characteristics. We implement a prototype of mmVib, and the experiments show that this design achieves 8.2% relative amplitude error and 0.5% relative frequency error in median. Typically, the median amplitude error is 3.4um for the 100um-amplitude vibration. Compared to two existing approaches, mmVib reduces the 80th-percentile amplitude error by 62.9% and 68.9% respectively.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419202"}, {"primary_key": "2660536", "vector": [], "sparse_vector": [], "title": "Towards 3D human pose construction using wifi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chong Tian", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "This paper presents WiPose, the first 3D human pose construction framework using commercial WiFi devices. From the pervasive WiFi signals, WiPose can reconstruct 3D skeletons composed of the joints on both limbs and torso of the human body. By overcoming the technical challenges faced by traditional camera-based human perception solutions, such as lighting and occlusion, the proposed WiFi human sensing technique demonstrates the potential to enable a new generation of applications such as health care, assisted living, gaming, and virtual reality. WiPose is based on a novel deep learning model that addresses a series of technical challenges. First, WiPose can encode the prior knowledge of human skeleton into the posture construction process to ensure the estimated joints satisfy the skeletal structure of the human body. Second, to achieve cross environment generalization, WiPose takes as input a 3D velocity profile which can capture the movements of the whole 3D space, and thus separate posture-specific features from the static objects in the ambient environment. Finally, WiPose employs a recurrent neural network (RNN) and a smooth loss to enforce smooth movements of the generated skeletons. Our evaluation results on a real-world WiFi sensing testbed with distributed antennas show that WiPose can localize each joint on the human skeleton with an average error of 2.83cm, achieving a 35% improvement in accuracy over the state-of-the-art posture construction model designed for dedicated radar sensors.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380900"}, {"primary_key": "2660537", "vector": [], "sparse_vector": [], "title": "Age of information in wireless networks: from theory to implementation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging applications, such as smart factories and fleets of drones, increasingly rely on sharing time-sensitive information for monitoring and control. In such application domains, it is essential to keep information fresh, as outdated information loses its value and can lead to system failures and safety risks. The Age of Information (AoI) is a performance metric that captures how fresh the information is from the perspective of the destination. In this paper, we show that as the congestion in the wireless network increases, the AoI degrades sharply, leading to outdated information at the destination. Leveraging years of theoretical research, we propose and implement WiFresh: an unconventional architecture that achieves near optimal information freshness in wireless networks, regardless of the level of congestion. Our experimental results show that WiFresh can improve information freshness by two orders of magnitude when compared to an equivalent standard WiFi network.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418171"}, {"primary_key": "2660539", "vector": [], "sparse_vector": [], "title": "Towards quantum belief propagation for LDPC decoding in wireless networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present Quantum Belief Propagation (QBP), a Quantum Annealing (QA) based decoder design for Low Density Parity Check (LDPC) error control codes, which have found many useful applications in Wi-Fi, satellite communications, mobile cellular systems, and data storage systems. QBP reduces the LDPC decoding to a discrete optimization problem, then embeds that reduced design onto quantum annealing hardware. QBP's embedding design can support LDPC codes of block length up to 420 bits on real state-of-the-art QA hardware with 2,048 qubits. We evaluate performance on real quantum annealer hardware, performing sensitivity analyses on a variety of parameter settings. Our design achieves a bit error rate of 10--8 in 20 μs and a 1,500 byte frame error rate of 10--6 in 50 μs at SNR 9 dB over a Gaussian noise wireless channel. Further experiments measure performance over real-world wireless channels, requiring 30 μs to achieve a 1,500 byte 99.99% frame delivery rate at SNR 15-20 dB. QBP achieves a performance improvement over an FPGA based soft belief propagation LDPC decoder, by reaching a bit error rate of 10--8 and a frame error rate of 10--6 at an SNR 2.5--3.5 dB lower. In terms of limitations, QBP currently cannot realize practical protocol-sized (e.g., Wi-Fi, WiMax) LDPC codes on current QA processors. Our further studies in this work present future cost, throughput, and QA hardware trend considerations.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419207"}, {"primary_key": "2660540", "vector": [], "sparse_vector": [], "title": "WhiteHaul: white space spectrum aggregation system for backhaul.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Today almost half the world's population does not have Internet access. This is particularly the case in rural and undeserved regions where providing Internet access infrastructure is challenging and expensive. To this end, we present demonstration of WhiteHaul [5], a low-cost hybrid cross-layer aggregation system for TV White Space (TVWS) based backhaul. WhiteHaul features a custom-designed frequency conversion substrate that efficiently handles multiple noncontiguous chunks of TVWS spectrum using multiple low-cost COTS 802.11n/ac cards but with a single antenna. At the software layer, WhiteHaul uses MPTCP as a link-level tunnel abstraction to efficiently aggregate multiple chunks of the TVWS spectrum via a novel uncoupled, cross-layer congestion control algorithm. This demo illustrates the unique features of the WhiteHaul system based on a prototype implementation employing a modified version of MPTCP Linux Kernel and a custom-designed conversion substrate. Using this prototype, we highlight the performance of the WhiteHaul system under various configurations and network conditions.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417319"}, {"primary_key": "2660541", "vector": [], "sparse_vector": [], "title": "Design of an IoT-based water flow monitoring system.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sabah Pirani", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present the design of a low-cost IoT based approach to monitor the amount of water dispensed at communal clean water collection nodes called Water Filtration Plants. The design of our system caters to the limitations of low-resource settings, such as brown-outs, power surges, data connectivity issues, while our data processing methodology caters to the limitations inherent in the use of low-cost hardware installed in our deployment. Our actionable insights help the water utility of a dense Urban city in Pakistan improve the quality of service of providing clean drinking water to its residents.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418170"}, {"primary_key": "2660543", "vector": [], "sparse_vector": [], "title": "Remote experimentation with open-access full-duplex wireless in the COSMOS testbed.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To support experimentation with full-duplex (FD) wireless, we recently integrated two FlexICoN Gen-2 wideband FD radios in the open-access, city-scale NSF PAWR COSMOS testbed. Each integrated FD radio consists of an antenna, a customized Gen-2 RF self-interference (SI) canceller box, a USRP software-defined radio, and a remotely accessible compute node. The RF SI canceller box includes an RF canceller printed circuit board which emulates an integrated circuit implementation based on the technique of frequency-domain equalization. The Gen-2 canceller box can achieve up to 50 dB RF SI cancellation across 20MHz bandwidth. In this demo, we present the design and implementation of the open-acccess, remotely accessible FD radios that are integrated in the indoor COSMOS Sandbox 2 at Columbia University. We also demonstrate example experiments that are available to researchers, where demo participants can observe the visualized performance of the open-access FD radios.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417324"}, {"primary_key": "2660545", "vector": [], "sparse_vector": [], "title": "Bringing hybrid analog-digital beamforming to commercial MU-MIMO wifi networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Commercial off-the-shelf (COTS) IEEE 802.11ac WiFi systems only use a low number of antennas. This limits the multi-user MIMO (MU-MIMO) performance and, hence, the throughput of such systems, especially in dense environments. We present a unique solution based on hybrid digital-analog (HDA) beamforming to overcome the limitation of COTS WiFi hardware and effectively increase the MU-MIMO gain and to unlock the potential of MU-MIMO in WiFi. We use COTS WiFi hardware in combination with a self-developed beamforming module and novel algorithms for the HDA approach, leveraging the MU-MIMO precoding of the WiFi system. The implemented control and signal processing software is fully transparent to the WiFi part, i.e., no protocol changes are needed. We demonstrate the increase in MU-MIMO gain using unmodified COTS end-user terminals.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417320"}, {"primary_key": "2660547", "vector": [], "sparse_vector": [], "title": "SPINN: synergistic progressive inference of neural networks over device and cloud.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite the soaring use of convolutional neural networks (CNNs) in mobile applications, uniformly sustaining high-performance inference on mobile has been elusive due to the excessive computational demands of modern CNNs and the increasing diversity of deployed devices. A popular alternative comprises offloading CNN processing to powerful cloud-based servers. Nevertheless, by relying on the cloud to produce outputs, emerging mission-critical and high-mobility applications, such as drone obstacle avoidance or interactive applications, can suffer from the dynamic connectivity conditions and the uncertain availability of the cloud. In this paper, we propose SPINN, a distributed inference system that employs synergistic device-cloud computation together with a progressive inference method to deliver fast and robust CNN inference across diverse settings. The proposed system introduces a novel scheduler that co-optimises the early-exit policy and the CNN splitting at run time, in order to adapt to dynamic conditions and meet user-defined service-level requirements. Quantitative evaluation illustrates that SPINN outperforms its state-of-the-art collaborative inference counterparts by up to 2× in achieved throughput under varying network conditions, reduces the server cost by up to 6.8× and improves accuracy by 20.7% under latency constraints, while providing robust operation under uncertain connectivity conditions and significant energy savings compared to cloud-centric execution.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419194"}, {"primary_key": "2660548", "vector": [], "sparse_vector": [], "title": "GROOT: a real-time streaming system of high-fidelity volumetric videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present GROOT, a mobile volumetric video streaming system that delivers three-dimensional data to mobile devices for a fully immersive virtual and augmented reality experience. The system design for streaming volumetric videos should be fundamentally different from conventional 2D video streaming systems. First, the amount of data required to deliver the 3D volume is considerably larger than conventional videos with frames of 2D images, even compared to high-resolution 2D or 360° videos. Second, the 3D data representation, which encodes the surface of objects within the volume, is a sparse and unorganized data structure with varying scales, whereas a conventional video is composed of a sequence of images with the fixed-size 2D grid structure. GROOT is a streaming framework with a novel data structure that enables not only real-time transmission and decoding on mobile devices but also continuous on-demand user view adaptation. Specifically, we modify the conventional octree to introduce the independence of leaf nodes with minimal memory overhead, which enables parallel decoding of highly irregular 3D data. We also developed a suite of techniques to compress color information and filter out 3D points outside of a user's view, which efficiently minimizes the data size and decoding cost. Our extensive evaluation shows that GROOT achieves more stable but faster frame rates compared to any previous method to stream and visualize volumetric videos on mobile devices.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419214"}, {"primary_key": "2660549", "vector": [], "sparse_vector": [], "title": "Toward a secure QR code system by fingerprinting screens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quick response (QR) codes have been widely used in mobile applications, due to its convenience and the pervasive built-in cameras on smartphones. Recently, however, QR codes have been reported suffering attacks for being sniffed just before the QR code is scanned, which lead to financial loss. In this study, we propose ScreenID, for enhancing the QR code security by identifying its authenticity, which embeds a QR code with information of unique screen fingerprint - PWM frequency. PWM frequencies are adjusted to different values by screen manufacturers, therefore can successfully differentiate screens. To improve the estimation accuracy of PWM frequency, ScreenID incorporates a model for the interaction between the camera and screen in the temporal and spatial domains. Extensive experiments demonstrate that ScreenID can differentiate screens of different models, types and manufacturers and thus improve the security of QR codes.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418165"}, {"primary_key": "2660550", "vector": [], "sparse_vector": [], "title": "A seamless virtualized network functions migration mechanism in mobile edge networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile Edge Computing (MEC) is an emerging architecture that supports ultra-low latency and high-bandwidth services by deploying servers at the edge of the network to provide computing and storage resources. Recent studies tend to combine (Network Function Virtualization) NFV with MEC and deploy (Virtualized Network Functions) VNFs on MEC servers to achieve fast access to the edge user equipment (UE). However, to guarantee the QoS requirements of mobile users, it is necessary to migrate VNFs to an advisable edge server when users move across Base Stations (BS). How to choose the target BS for VNFs migration? How to select the path for VNF data migration? How to ensure the QoS of user services during the migration process? To solve these issues, we study the seamless VNFs migration problem in mobile edge networks and formulate it as an ILP model, which aims to minimize the migration delay and cost. Then we propose a migration algorithm based on Dijkstra (MBD) to obtain the migration destination BS and migration paths. We implement the mathematical model in Gurobi and design a Greedy algorithm to compare the performance with the MBD algorithm. The experiment results show the effectiveness and efficiency of our algorithm.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418166"}, {"primary_key": "2660551", "vector": [], "sparse_vector": [], "title": "Experience: aging or glitching? why does android stop responding and what can we do about it?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenhua Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Almost every Android user has unsatisfying experiences regarding responsiveness, in particular Application Not Responding (ANR) and System Not Responding (SNR) that directly disrupt user experience. Unfortunately, the community have limited understanding of the prevalence, characteristics, and root causes of unresponsiveness. In this paper, we make an in-depth study of ANR and SNR at scale based on fine-grained system-level traces crowdsourced from 30,000 Android systems. We find that ANR and SNR occur prevalently on all the studied 15 hardware models, and better hardware does not seem to relieve the problem. Moreover, as Android evolves from version 7.0 to 9.0, there are fewer ANR events but more SNR events. Most importantly, we uncover multifold root causes of ANR and SNR and pinpoint the largest inefficiency which roots in Android's flawed implementation of Write Amplification Mitigation (WAM). We design a practical approach to eliminating this largest root cause; after large-scale deployment, it reduces almost all (>99%) ANR and SNR caused by WAM while only decreasing 3% of the data write speed. In addition, we document important lessons we have learned from this study, and have also released our measurement code/data to the research community.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380897"}, {"primary_key": "2660552", "vector": [], "sparse_vector": [], "title": "Internet-of-microchips: direct radio-to-bus communication with SPI backscatter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Li Lu", "<PERSON>"], "summary": "Energy consumption of Internet-of-Things end devices is a major constraint that limits their long-term and large-scale deployment. Conventionally, the radios and processors used in these end devices are major power consumption that drains at the level of milliwatts (mWs). However, in recent decades, backscatter communication has dramatically reduced the power consumed by the radios in end devices to microwatts (μWs), and thus the processor remains the major bottleneck for energy optimization.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419182"}, {"primary_key": "2660554", "vector": [], "sparse_vector": [], "title": "Nephalai: towards LPWAN C-RAN with physical layer compression.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose Nephelai, a Compressive Sensing-based Cloud Radio Access Network (C-RAN), to reduce the uplink bit rate of the physical layer (PHY) between the gateways and the cloud server for multi-channel LPWANs. Recent research shows that single-channel LPWANs suffer from scalability issues. While multiple channels improve these issues, data transmission is expensive. Furthermore, recent research has shown that jointly decoding raw physical layers that are offloaded by LPWAN gateways in the cloud can improve the signal-to-noise ratio (SNR) of week radio signals. However, when it comes to multiple channels, this approach requires high bandwidth of network infrastructure to transport a large amount of PHY samples from gateways to the cloud server, which results in network congestion and high cost due to Internet data usage. In order to reduce the operation's bandwidth, we propose a novel LPWAN packet acquisition mechanism based on Compressive Sensing with a custom design dictionary that exploits the structure of LPWAN packets, reduces the bit rate of samples on each gateway, and demodulates PHY in the cloud with (joint) sparse approximation. Moreover, we propose an adaptive compression method that takes the Spreading Factor (SF) and SNR into account. Our empirical evaluation shows that up to 93.7% PHY samples can be reduced by Nephelai when SF = 9 and SNR is high without degradation in the packet reception rate (PRR). With four gateways, 1.7x PRR can be achieved with 87.5% PHY samples compressed, which can extend the battery lifetime of embedded IoT devices to 1.7.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419193"}, {"primary_key": "2660555", "vector": [], "sparse_vector": [], "title": "From relative azimuth to absolute location: pushing the limit of PIR sensor based localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pyroelectric infrared (PIR) sensors are considered to be promising devices for device-free localization due to its advantages of low cost, energy efficiency, and the immunity from multi-path fading. However, most of the existing PIR-based localization systems only utilize the binary information of PIR sensors and therefore require a large number of carefully deployed PIR sensors. A few works directly map the raw data of PIR sensors to one's location using machine learning approaches. However, these data-driven approaches require abundant training data and suffer from environmental change. In this paper, we propose PIRATES, a PIR-based device-free localization system based on the raw data of PIR sensors. The key of PIRATES is to extract a new type of location information called azimuth change. The extraction of the azimuth change relies on the physical properties of PIR sensors. Therefore, no abundant training data are needed and the system is robust to environmental change. Through experiments, we demonstrate that PIRATES can achieve higher localization accuracy than the state-of-the-art approaches. In addition, the information of the azimuth change can be easily incorporated with other information of PIR signals (e.g. amplitude) to improve the localization accuracy.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380878"}, {"primary_key": "2660556", "vector": [], "sparse_vector": [], "title": "Hybrid communication and storage system with user privacy preservation for public management, analysis and prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a hybrid system of combining mobile sensor nodes and fixed sensor nodes for robust and effective public management, analysis, and prediction. In the proposed system, user privacy protection (on device, edge, and cloud) is implemented via the design of multiple levels of data sensitivity protection: User activities and location data are anonymized, consented, and aggregated (via edge computing at public infrastructures) before uploading for analysis; Specially designed event tokens are distributed for information/notification flowing and matching, and sharing user sensitive data is avoided; Event tokens also link hierarchical user groups combined with blockchain based technologies. Our system facilitates matching between user private data and public data without compromising privacy.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418164"}, {"primary_key": "2660558", "vector": [], "sparse_vector": [], "title": "Ghost calls from operational 4G call systems: IMS vulnerability, call DoS attack, and countermeasure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "IMS (IP Multimedia Subsystem) is an essential framework for providing 4G/5G multimedia services. It has been deployed worldwide to support two call services: VoLTE (Voice over LTE) and VoWi-Fi (Voice over Wi-Fi). VoWi-Fi enables telephony calls over the Wi-Fi network to complement VoLTE. In this work, we uncover that the VoWi-Fi signaling session can be hijacked to maliciously manipulate the IMS call operation. An adversary can easily make ghost calls to launch a stealthy call DoS (Denial of Service) attack against specific cellular users. Only phone numbers, but not any malware or network information, are required from the victims. This sophisticated attack harnesses a design defect of the IMS call state machine, but not simply flooding or a crash trigger. To stealthily detect attackable phones at run time, we exploit a vulnerability of the 4G network infrastructure, call information leakage, which we explore using machine learning. We validate these vulnerabilities in operational 4G networks of 4 top-tier carriers across Asia and North America countries with 7 phone brands. Our result shows that the call DoS attack can prevent the victims from receiving incoming calls up to 99.0% time without user awareness. We finally propose and evaluate recommended solutions.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380885"}, {"primary_key": "2660569", "vector": [], "sparse_vector": [], "title": "Hummingbird: energy efficient GPS receiver for small satellites.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "T. Venkata Prabhakar"], "summary": "Global Positioning System is a widely adopted localization technique. With the increasing demand for small satellites, the need for a low-power GPS for satellites is also increasing. To enable many state-of-the-art applications, the exact position of the satellites is necessary. However, building low-power GPS receivers which operate in low earth orbit pose significant challenges. This is mainly due to the high speed (~7.8 km/s) of small satellites. While duty-cycling the receiver is a possible solution, the high relative Doppler shift between the GPS satellites and the small satellite contributes to the increase in Time To First Fix (TTFF), thus increasing the energy consumption. Further, if the GPS receiver is tumbling along with the small satellite on which it is mounted, longer TTFF may lead to no GPS fix due to disorientation of the receiver antenna. In this paper, we elucidate the design of a low-cost, low-power GPS receiver for small satellite applications. We also propose an energy optimization algorithm called F3to improve the TTFF which is the main contributor to the energy consumption during cold start. With simulations and in-orbit evaluation from a launched nanosatellite with our μGPS and high-end GPS simulators, we show that up to 96.16% of energy savings (consuming only ~ 1/25th energy compared to the state of the art) can be achieved using our algorithm without compromising much (~10 m) on the navigation accuracy. The TTFF achieved is at most 33 s.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380886"}, {"primary_key": "2660570", "vector": [], "sparse_vector": [], "title": "TSFCC: high availability service function chain composition approach in mobile network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network function virtualization (NFV) plays a vital role in 5G mobile networks. Concatenating virtual network functions (VNFs) into service function chains (SFCs) provides flexible and diverse network support for intelligent applications. However, the mobile network connection is very unreliable. A reasonable SFC composition mechanism is essential for stable service providing. This paper proposes a high availability service function chain composition approach, TSFCC. TSFCC includes real-time road marking strategy, bi-composition mechanism, and VNF reallocation mechanism. Evaluation results prove that TSFCC can adapt to the mobile network environment and provide users with efficient and highly available SFC service.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418163"}, {"primary_key": "2660571", "vector": [], "sparse_vector": [], "title": "Billion-scale federated learning on mobile clients: a submodel design with tunable privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chengfei Lv", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning was proposed with an intriguing vision of achieving collaborative machine learning among numerous clients without uploading their private data to a cloud server. However, the conventional framework requires each client to leverage the full model for learning, which can be prohibitively inefficient for large-scale learning tasks and resource-constrained mobile devices. Thus, we proposed a submodel framework, where clients download only the needed parts of the full model, namely, submodels, and then upload the submodel updates. Nevertheless, the \"position\" of a client's truly required submodel corresponds to its private data, while the disclosure of the true position to the cloud server during interactions inevitably breaks the tenet of federated learning. To integrate efficiency and privacy, we designed a secure federated submodel learning scheme coupled with a private set union protocol as a cornerstone. The secure scheme features the properties of randomized response, secure aggregation, and Bloom filter, and endows each client with customized plausible deniability (in terms of local differential privacy) against the position of its desired submodel, thereby protecting private data. We further instantiated the scheme with Alibaba's e-commerce recommendation, implemented a prototype system, and extensively evaluated over 30-day Taobao user data. Empirical results demonstrate the feasibility and scalability of the proposed scheme as well as its remarkable advantages over the conventional federated learning framework, from model accuracy and convergency, practical communication, computation, and storage overhead.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419188"}, {"primary_key": "2660574", "vector": [], "sparse_vector": [], "title": "Performance bottlenecks identification in cloudified mobile networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recent trend towards cloudifying mobile networks brings more flexibility and shortens deployment times. However, it results in an architecture spanning several independent layers from the bare metal to the service level thus complicating troubleshooting and service assurance. In this work, we experimentally explore whether we can accurately and efficiently identify bottlenecks across the different locations of the network and layers of the cloudified architecture. Our findings confirm the complexity of this task and lead us to promising solutions through the use of Machine Learning.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418158"}, {"primary_key": "2660575", "vector": [], "sparse_vector": [], "title": "Experience: advanced network operations in (Un)-connected remote communities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Internet Para Todos program is working to provide sustainable mobile broadband to 100 M unconnected people in Latin America. In this paper we present our commercial deployment in thousands remote small communities and describe the unique experience of maintaining this infrastructure. We describe the challenges related to managing operations containing the cost in these extreme geographical conditions. We also analyze operational data to understand outage patterns and present typical operational issues in this unique remote community environment. Finally, we present an extension of the operations support system (OSS) leveraging advanced analytics and machine learning with the goal of optimizing network maintenance while reducing costs.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380893"}, {"primary_key": "2660578", "vector": [], "sparse_vector": [], "title": "EarSense: earphones as a teeth activity sensor.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper finds that actions of the teeth, namely tapping and sliding, produce vibrations in the jaw and skull. These vibrations are strong enough to propagate to the edge of the face and produce vibratory signals at an earphone. By re-tasking the earphone speaker as an input transducer - a software modification in the sound card - we are able to sense teeth-related gestures across various models of ear/headphones. In fact, by analyzing the signals at the two earphones, we show the feasibility of also localizing teeth gestures, resulting in a human-to-machine interface. Challenges range from coping with weak signals, distortions due to different teeth compositions, lack of timing resolution, spectral dispersion, etc. We address these problems with a sequence of sensing techniques, resulting in the ability to detect 6 distinct gestures in real-time. Results from 18 volunteers exhibit robustness, even though our system - EarSense - does not depend on per-user training. Importantly, EarSense also remains robust in the presence of concurrent user activities, like walking, nodding, cooking and cycling. Our ongoing work is focused on detecting teeth gestures even while music is being played in the earphone; once that problem is solved, we believe EarSense could be even more compelling.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419197"}, {"primary_key": "2660580", "vector": [], "sparse_vector": [], "title": "Challenge: COSMOS: A city-scale programmable testbed for experimentation with advanced wireless.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Panagi<PERSON><PERSON>", "<PERSON>"], "summary": "This paper focuses on COSMOS - Cloud enhanced Open Software defined MObile wireless testbed for city-Scale deployment. The COSMOS testbed is being deployed in West Harlem (New York City) as part of the NSF Platforms for Advanced Wireless Research (PAWR) program. It will enable researchers to explore the technology \"sweet spot\" of ultra-high bandwidth and ultra-low latency in the most demanding real-world environment. We describe the testbed's architecture, the design and deployment challenges, and the experience gained during the design and pilot deployment. Specifically, we describe COSMOS' computing and network architectures, the critical building blocks, and its programmability at different layers. The building blocks include software-defined radios, 28 GHz millimeter-wave phased array modules, optical transport network, core and edge cloud, and control and management software. We describe COSMOS' deployment phases in a dense urban environment, the research areas that could be studied in the testbed, and specific example experiments. Finally, we discuss our experience with using COSMOS as an educational tool.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380891"}, {"primary_key": "2660581", "vector": [], "sparse_vector": [], "title": "Redefining passive in backscattering with commodity devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The recent innovation of frequency-shifted (FS) backscatter allows for backscattering with commodity devices, which are inherently half-duplex. However, their reliance on oscillators for generating the frequency-shifting signal on the tag, forces them to incur the transient phase of the oscillator before steady-state operation. We show how the oscillator's transient phase can pose a fundamental limitation for battery-less tags, resulting in significantly low bandwidth efficiencies, thereby limiting their practical usage.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380880"}, {"primary_key": "2660582", "vector": [], "sparse_vector": [], "title": "Bringing temperature-awareness to millimeter-wave networks.", "authors": ["<PERSON><PERSON>", "Sanjib Sur", "<PERSON><PERSON>"], "summary": "Millimeter-wave devices operate at very high frequency and ultra-wide bandwidth. They consume more energy, dissipate more power, and heat up faster. So, millimeter-wave (mmWave) would exacerbate the device overheating problem in the future. In this work, we first perform a thermal characterization of mmWave devices: it reveals that after only 10 s. of data transfer at 1.9 Gbps, the antenna temperature reaches 68°C; it reduces the link throughput by 21%, increases the standard deviation by 6×, and takes 130 s. to dissipate the heat completely. We then propose <PERSON><PERSON><PERSON> to bring temperature-awareness in mmWave networks; <PERSON><PERSON><PERSON> maintains relatively high throughput performance and cools down the devices substantially. Our testbed experiments in static conditions show that <PERSON><PERSON><PERSON> reaches a median peak temperature just 1°C above the optimal with less than 10% throughput sacrifice only.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418159"}, {"primary_key": "2660584", "vector": [], "sparse_vector": [], "title": "WiChronos: energy-efficient modulation for long-range, large-scale wireless networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless communication over long distances has become the bottleneck for battery-powered, large-scale deployments. Currently used low-power protocols such as Zigbee and Bluetooth Low Energy have limited communication range, whereas long-range communication strategies used in cellular and satellite networks are heavy on energy consumption. Methods that use narrow-band communication such as LoRa, SigFox, and NB-IoT have low spectral efficiency, leading to scalability issues. The goal of this work is to develop a communication framework that can satisfy the following requirements: (1) Increased battery life, (2) Longer communication range, (3) Scalability in a wireless network. In this work, we propose, design, and prototype WiChronos, a communication paradigm that encodes information in the time interval between two narrowband symbols in order to drastically reduce the energy consumption in a wide area network with a large number of senders. We leverage the low data-rate and relaxed latency requirements of such applications to achieve the desired features identified above. Based on our prototype using off-the-shelf components, WiChronos achieves an impressive 60% improvement in battery life compared to state-of-the-art LPWAN technologies at distances of over 800 meters. We also show that more than 1000 WiChronos senders can co-exist with less than 5% probability of collisions under low traffic conditions.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380898"}, {"primary_key": "2660585", "vector": [], "sparse_vector": [], "title": "WiChronos: energy-efficient modulation for long-range, large-scale wireless networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Power efficient wireless communication has become a bottleneck for long range and large scale deployments. We propose and prototype WiChronos, an energy efficient modulation technique for long range wireless communication in a large scale network. Using off-the-shelf (OTS) components, we demonstrate that WiChronos achieves an impressive 60% improvement in battery life compared to state-of-the-art LPWAN technologies at distances over 800 meters. We also show that more than 1000 WiChronos senders co-exist with less than 5% probability of collisions in low traffic conditions.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417322"}, {"primary_key": "2660586", "vector": [], "sparse_vector": [], "title": "A hyperlocal mobile web for the next 3 billion users.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite increasing mobile Internet penetration in developing regions, growing web page complexity and the lack of optimization from remote content providers make the web experience poor in these areas. The high relative bandwidth cost, poor network performance, and lack of relevant local content combine to dampen the demand for the Internet and services it enables. In this paper we propose GAIUS, a content ecosystem enabling efficient creation and dissemination of locally relevant web content. At its core, GAIUS consists of the following innovations: a locally sustainable content ecosystem, and MAML, a web specification language that simplifies web pages to reduce costs and lower barriers for content creation.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417316"}, {"primary_key": "2660587", "vector": [], "sparse_vector": [], "title": "Service-oriented intelligent and extensible RAN.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Network slicing is considered to be the enabler for a coexistence of a multitude of services on a multi-tenant 5G infrastructure. It is supported through software-defined radio access networking (SD-RAN), bringing programmability to the network in order to enhance performance according to the needs of slice owners. However, SD-RAN so far remained limited to a mere reconfiguration of the base station. In this work, we demonstrate a prototype of a service-oriented RAN on top of the OpenAirInterface and Mosaic5G platforms that brings programmability and extensibility to the RAN with a range of network applications for the purpose of intelligent slicing. We implemented a slice control and management framework, and plug a traffic analysis application that significantly improves the performance of slice users. We observe an improvement of 30% in application round-trip time with negligible variability for the considered traffic. Further, we demonstrate how to extend control plane functionality from a network store to improve slice performance.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417321"}, {"primary_key": "2660591", "vector": [], "sparse_vector": [], "title": "Constructing 3-dimensional 5G coverage map for real-time airborne missions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "With recent deployments of the fifth generation (5G) network and advances in unmanned aerial vehicles, diverse airborne missions that provide high resolution aerial imagery in real-time are possible. However, a reliable inference of 3D cellular coverage is required to provide seamless imagery. As a part of intercontinental 5G testbed activities within Korea-EU 5G Project (PriMO-5G), we construct 3D 5G coverage map manually, to present insights regarding 3D coverage and how to construct its map efficiently. We then devise algorithms for constructing 3D coverage map simultaneously with real-time airborne missions in a cost-effective manner.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418160"}, {"primary_key": "2660596", "vector": [], "sparse_vector": [], "title": "Voice localization using nearby wall reflections.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice assistants such as Amazon Echo (Alexa) and Google Home use microphone arrays to estimate the angle of arrival (AoA) of the human voice. This paper focuses on adding user localization as a new capability to voice assistants. For any voice command, we desire Alexa to be able to localize the user inside the home. The core challenge is two-fold: (1) accurately estimating the AoAs of multipath echoes without the knowledge of the source signal, and (2) tracing back these AoAs to reverse triangulate the user's location.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380884"}, {"primary_key": "2660597", "vector": [], "sparse_vector": [], "title": "DMM: fast map matching for cellular data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Map matching for cellular data is to transform a sequence of cell tower locations to a trajectory on a road map. It is an essential processing step for many applications, such as traffic optimization and human mobility analysis. However, most current map matching approaches are based on Hidden Markov Models (HMMs) that have heavy computation overhead to consider high-order cell tower information. This paper presents a fast map matching framework for cellular data, named as DMM, which adopts a recurrent neural network (RNN) to identify the most-likely trajectory of roads given a sequence of cell towers. Once the RNN model is trained, it can process cell tower sequences as making RNN inference, resulting in fast map matching speed. To transform DMM into a practical system, several challenges are addressed by developing a set of techniques, including spatial-aware representation of input cell tower sequences, an encoder-decoder framework for map matching model with variable-length input and output, and a reinforcement learning based model for optimizing the matched outputs. Extensive experiments on a large-scale anonymized cellular dataset reveal that DMM provides high map matching accuracy (precision 80.43% and recall 85.42%) and reduces the average inference time of HMM-based approaches by 46.58×.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3421461"}, {"primary_key": "2660598", "vector": [], "sparse_vector": [], "title": "Experience: towards automated customer issue resolution in cellular networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cellular service carriers often employ reactive strategies to assist customers who experience non-outage related individual service degradation issues (e.g., service performance degradations that do not impact customers at scale and are likely caused by network provisioning issues for individual devices). Customers need to contact customer care to request assistance before these issues are resolved. This paper presents our experience with PACE (ProActive customer CarE), a novel, proactive system that monitors, troubleshoots and resolves individual service issues, without having to rely on customers to first contact customer care for assistance. PACE seeks to improve customer experience and care operation efficiency by automatically detecting individual (non-outage related) service issues, prioritizing repair actions by predicting customers who are likely to contact care to report their issues, and proactively triggering actions to resolve these issues. We develop three machine learning-based prediction models, and implement a fully automated system that integrates these prediction models and takes resolution actions for individual customers. We conduct a large-scale trace-driven evaluation using real-world data collected from a major cellular carrier in the US, and demonstrate that PACE is able to predict customers who are likely to contact care due to non-outage related individual service issues with high accuracy. We further deploy PACE into this cellular carrier network. Our field trial results show that PACE is effective in proactively resolving non-outage related individual customer service issues, improving customer experience, and reducing the need for customers to report their service issues.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419203"}, {"primary_key": "2660601", "vector": [], "sparse_vector": [], "title": "Millimeter-wave full duplex radios.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "mm-Wave has emerged as an attractive high-speed wireless communication paradigm owing to the high available bandwidth at mm-wave frequencies. Full-Duplex has the potential to double the available capacity in the mm-wave bands by enabling simultaneous radio transmission and reception. While full-duplex has been extensively studied in sub-6 GHz bands, this paper exposes the unique challenges in porting this capability to mm-wave frequencies.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380879"}, {"primary_key": "2660603", "vector": [], "sparse_vector": [], "title": "SpiroSonic: monitoring human lung function via acoustic sensing on commodity smartphones.", "authors": ["<PERSON>ng<PERSON><PERSON> Song", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Respiratory diseases have been a significant public health challenge. Efficient disease evaluation and monitoring call for daily spirometry tests, as an effective way of pulmonary function testing, out of clinic. This requirement, however, is hard to be satisfied due to the large size and high costs of current spirometry equipments. In this paper, we present SpiroSonic, a new system design that uses commodity smartphones to support complete, accurate yet reliable spirometry tests in regular home settings with various environmental and human factors. SpiroSonic measures the humans' chest wall motion via acoustic sensing and interprets such motion into lung function indices, based on the clinically validated correlation between them. We implemented SpiroSonic as a smartphone app, and verified SpiroSonic's monitoring error over healthy humans as <3%. Clinical studies further show that SpiroSonic reaches 5%-10% monitoring error among 83 pediatric patients. Given that the error of in-clinic spirometry is usually around 5%, SpiroSonic can be reliably used for disease tracking and evaluation out of clinic.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419209"}, {"primary_key": "2660606", "vector": [], "sparse_vector": [], "title": "5G edge enhanced mobile augmented reality.", "authors": ["<PERSON><PERSON> Su", "<PERSON><PERSON>", "Pan Hui"], "summary": "Mobile Augmented Reality (MAR) provides a unique experience where the physical world is augmented with virtual annotations. MAR involves computation-heavy algorithms that could potentially be offloaded to edge servers on 5G networks, which significantly enhances MAR with reduced communication latency and more stable network connections, therefore leading to seamless MAR user experiences. In this demo, we show a running MAR system deployed on a 5G edge test bed and present latency results.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417315"}, {"primary_key": "2660607", "vector": [], "sparse_vector": [], "title": "Re-identification of mobile devices using real-time bidding advertising networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Advertisers gather data about users and their mobile devices through ads placed within Android and iOS apps. Most of the time, location, device, and app information are linked to the same device using a unique advertising ID (Ad ID). If the Ad ID is not available, advertisers can still use geo-coordinates or IP address to infer links in data gathered from different ad placements.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419205"}, {"primary_key": "2660608", "vector": [], "sparse_vector": [], "title": "A reliable intelligent routing mechanism in 5G core networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One of the main goal of 5G networks is to provide ultra-reliable low latency service to users. When users keep accessing the system, the traffic in 5G core network (5GC) may be congested. Thus, we propose a load balance algorithm to select the best traffic data routing path based on the traffic loading in the 5GC. The proposed algorithm is implemented in a 5G testbed called free5GC. The experimental results show that our proposed algorithm outperforms the traditional round-robin load balance algorithm in many performance metrics.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418167"}, {"primary_key": "2660609", "vector": [], "sparse_vector": [], "title": "C-14: assured timestamps for drone videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Inexpensive and highly capable unmanned aerial vehicles (aka drones) have enabled people to contribute high-quality videos at a global scale. However, a key challenge exists for accepting videos from untrusted sources: establishing when a particular video was taken. Once a video has been received or posted publicly, it is evident that the video was created before that time, but there are no current methods for establishing how long it was made before that time.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419196"}, {"primary_key": "2660612", "vector": [], "sparse_vector": [], "title": "Slicing-enabled private 4G/5G network for industrial wireless applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Private deployments of 4G and 5G networks in industrial environments are beneficial from various aspects. Private 4G/5G networks typically face the challenge of supporting heterogeneous industrial applications. This technology demonstration highlights the importance of network slicing in private 4G/5G networks. It shows that network slicing is crucial for performance guarantees in multiservice co-existence scenarios. With network slicing, our private 4G/5G network successfully supports closed-loop control, event-driven control and video streaming applications.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417325"}, {"primary_key": "2660613", "vector": [], "sparse_vector": [], "title": "Tunnel emitter: tunnel diode based low-power carrier emitters for backscatter tags.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Backscatter enables transmissions at orders of magnitude lower energy consumption when compared to conventional radio transmitters. Backscatter tags achieve this by the reflection or absorption of carrier signal generated from emitter devices. However, backscatter systems are limited by these emitter devices, as they are significantly energy-expensive when compared to the tags. While backscatter tags can operate without requiring batteries, relying on the minuscule amounts of energy harvested from the ambient environment. However, the emitter devices, are commonly tethered to an external power supply or operate on large batteries.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419199"}, {"primary_key": "2660614", "vector": [], "sparse_vector": [], "title": "Renovating road signs for infrastructure-to-vehicle networking: a visible light backscatter communication and networking approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kunta<PERSON> Du", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional road signs convey very concise and static visual information to human drivers, and bear retroreflective coating for better visibility at night. This paper introduces RetroI2V - a novel infrastructure-to-vehicle (I2V) communication and networking system that renovates conventional road signs to convey additional and dynamic information to vehicles while keeping intact their original functionality. In particular, RetroI2V exploits the retroreflective coating of road signs and establishes visible light backscattering communication (VLBC), and further coordinates multiple concurrent VLBC sessions among road signs and approaching vehicles. RetroI2V features a suite of novel VLBC designs including late-polarization, complementary optical signaling and polarization-based differential reception which are crucial to avoid flickering and achieve long VLBC range, as well as a decentralized MAC protocol that make practical multiple access in highly mobile and transient I2V settings. Experimental results from our prototyped system show that RetroI2V supports up to 101 m communication range and efficient multiple access at scale.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380883"}, {"primary_key": "2660616", "vector": [], "sparse_vector": [], "title": "Understanding and embracing the complexities of the molecular communication channel in liquids.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Molecular communication has recently gained a lot of interest due to its potential to enable micro-implants to communicate by releasing molecules into the bloodstream. In this paper, we aim to explore the molecular communication channel through theoretical and empirical modeling in order to achieve a better understanding of its characteristics, which tend to be more complex in practice than traditional wireless and wired channels. Our study reveals two key new characteristics that have been overlooked by past work. Specifically, the molecular communication channel exhibits non-causal inter-symbol-interference and a long delay spread, that extends beyond the channel coherence time, which limit decoding performance. To address this, we design, μ-Link a molecular communication protocol and decoder that accounts for these new insights. We build a testbed to experimentally validate our findings and show that μ-Link can improve the achievable data rates with significantly lower bit error rates.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419191"}, {"primary_key": "2660617", "vector": [], "sparse_vector": [], "title": "Demystifying millimeter-wave V2X: towards robust and efficient directional connectivity under high mobility.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) networking represents a core technology to meet the demanding bandwidth requirements of emerging connected vehicles. However, the feasibility of mmWave vehicle-to-everything (V2X) connectivity has long been questioned. One major doubt lies in how the highly directional mmWave links can sustain under high mobility. In this paper, we present the first comprehensive reality check of mmWave V2X networks. We deploy an experimental testbed to mimic a typical mmWave V2X scenario, and customize a COTS mmWave radio to enable microscopic investigation of the channel and the link. We further construct a high-fidelity 3D ray-tracer to reproduce the mmWave characteristics at scale. With this toolset, we study the mmWave V2X coverage, mobility and blockage, codebook/beam management, and spatial multiplexing. Our measurement debunks some common misperceptions of mmWave V2X networks. In particular, due to the constrained roadway network structures, we find the beam management can be handled easily by the often-denounced beam scanning schemes, as long as the codebook is properly designed. Blockage can be almost eliminated through proper basestation deployment and cooperation. Highly effective spatial multiplexing can be realized even without sophisticated MIMO radios. Our work points to possible ways to realize efficient and reliable mmWave networks under high mobility, while maintaining the simplicity of standard network protocols.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419208"}, {"primary_key": "2660618", "vector": [], "sparse_vector": [], "title": "X-Array: approximating omnidirectional millimeter-wave coverage using an array of phased arrays.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) networks are conventionally considered to bear a fundamental coverage limitation, due to the directional beams and limited field-of-view (FoV) of the phased array antennas. In this paper, we explore an array of phased arrays (APA) architecture, which aggregates co-located phased arrays with complementary FoVs to approximate WiFi-like omni-directional coverage. We found that straightforwardly activating all the arrays may even hamper network performance. To fully exploit the APA's potential, we propose X-Array, which jointly selects the arrays and beams, and applies a dynamic co-phasing mechanism to ensure different arrays' signals enhance each other. X-Array also incorporates a link recovery mechanism to identify alternative arrays/beams that can efficiently recover the link from outage. We have implemented X-Array on a commodity 802.11ad APA radio. Our experiments demonstrate that X-Array can approach omni-directional coverage and maintain high performance in spite of link dynamics.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380882"}, {"primary_key": "2660619", "vector": [], "sparse_vector": [], "title": "What you wear know how you feel: an emotion inference system with multi-modal wearable devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emotions show high significance on human health. Automatic emotion recognition is helpful for monitoring psychological disorders, mental problems and exploring behavioral mechanisms. Existing approaches adopt costly and bulky specialized hardware such as EEG/ECG helmet, possess privacy risks, or with low accuracy and user experience. With the increasing popularity of wearables, people tend to equip multiple smart devices, which provides potential opportunity for emotion perception. In this paper, we present a pervasive and portable system called MW-Emotion to recognize common emotional states with multi-modal wearable devices. However, ubiquitous wearable devices perceive shallow information which is not obviously related to human emotions. MW-Emotion excavates intrinsic mapping relationship between emotions and sensing data. Our experiments show that MW-Emotion can recognize different emotion states with a relatively high accuracy of 83.1%.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418161"}, {"primary_key": "2660623", "vector": [], "sparse_vector": [], "title": "A query engine for zero-streaming cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Low-cost wireless cameras are growing rapidly. With the help of advanced machine learning models (e.g., CNNs), those videos exhibit high business and social values, e.g., for retailing planning [18], wildlife study [21], and traffic monitoring [19, 25]. However, with high compute need, traditional video analytics systems [14, 15, 26, 27] require all videos to be uploaded to a backend server, which stresses the scarce network bandwidth between cameras and servers.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417317"}, {"primary_key": "2660624", "vector": [], "sparse_vector": [], "title": "TouchPass: towards behavior-irrelevant on-touch user authentication on smartphones leveraging vibrations.", "authors": ["Xiangyu Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With increasing private and sensitive data stored in mobile devices, secure and effective mobile-based user authentication schemes are desired. As the most natural way to contact with mobile devices, finger touches have shown potentials for user authentication. Most existing approaches utilize finger touches as behavioral biometrics for identifying individuals, which are vulnerable to spoofer attacks. To resist attacks for on-touch user authentication on mobile devices, this paper exploits physical characters of touching fingers by investigating active vibration signal transmission through fingers, and we find that physical characters of touching fingers present unique patterns on active vibration signals for different individuals. Based on the observation, we propose a behavior-irrelevant on-touch user authentication system, TouchPass, which leverages active vibration signals on smartphones to extract only physical characters of touching fingers for user identification. TouchPass first extracts features that mix physical characters of touching fingers and behavior biometrics of touching behaviors from vibration signals generated and received by smartphones. Then, we design a Siamese network-based architecture with a specific training sample selection strategy to reconstruct the extracted signal features to behavior-irrelevant features and further build a behavior-irrelevant on-touch user authentication scheme leveraging knowledge distillation. Our extensive experiments validate that TouchPass can accurately authenticate users and defend various attacks.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380901"}, {"primary_key": "2660625", "vector": [], "sparse_vector": [], "title": "SmartPatch: a patch prioritization framework for SCADA chain in smart grid.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Supervisory Control and Data Acquisition (SCADA) systems are the industrial control systems and operational infrastructure that can monitor and control the electricity grid. Electricity grids are increasingly transforming from the one-directional way of generating, transmitting, and distributing electricity to smart grids that are multi-directional in the way they monitor, automate, and remotely operate the power sector. SCADA systems are increasingly under cyber attacks illustrating growing vulnerabilities to the smart grids. The U.S. power industry notes the importance of SCADA chain cyber risks and the need to take proactive measures (timely patching of vulnerabilities) to mitigate the risks. However, not all vulnerabilities are always exploited by attackers; and not all vulnerabilities can be patched in resource-constrained scenarios. Therefore, the patch sequence needs to be strategic and efficient.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418162"}, {"primary_key": "2660627", "vector": [], "sparse_vector": [], "title": "TransLoc: transparent indoor localization with uncertain human participation for instant delivery.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Instant delivery is an important urban service in recent years because of the increasing demand. An important issue for delivery platforms is to keep updating the status of couriers especially the real-time locations, which is challenging when they are in an indoor environment. We argue the previous indoor localization techniques cannot be applied in the instant delivery scenario because they require extra deployed infrastructures and extensive labor work. In this work, we perform the couriers' indoor localization transparently in a predictive manner without extra actions of couriers by existing data from the platform including order progress reports and couriers' trajectories. Specifically, we present TransLoc to predict couriers' indoor locations by addressing two challenges including uncertain reporting behaviors and uncertain indoor mobility behaviors. Our key idea lies in two insights (i) couriers' behaviors are consistent in indoor/outdoor environments; (ii) localization, as a spatial inference problem, could be converted to a temporal inference problem. We evaluate TransLoc on 565 couriers from an instant delivery company, which improves baselines by at most 72%, and achieves a competitive result compared to a label-extensive approach. As a case study, we apply TransLoc to optimize the order dispatching strategy, which reduces the delivery time by 24%.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419198"}, {"primary_key": "2660628", "vector": [], "sparse_vector": [], "title": "Ear-AR: indoor acoustic augmented reality on earphones.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper aims to use modern earphones as a platform for acoustic augmented reality (AAR). We intend to play 3D audio-annotations in the user's ears as she moves and looks at AAR objects in the environment. While companies like Bose and Microsoft are beginning to release such capabilities, they are intended for outdoor environments. Our system aims to explore the challenges indoors, without requiring any infrastructure deployment. Our core idea is two-fold. (1) We jointly use the inertial sensors (IMUs) in earphones and smartphones to estimate a user's indoor location and gazing orientation. (2) We play 3D sounds in the earphones and exploit the human's responses to (re)calibrate errors in location and orientation. We believe this fusion of IMU and acoustics is novel, and could be an important step towards indoor AAR. Our system, Ear-AR, is tested on 7 volunteers invited to an AAR exhibition - like a museum - that we set up in our building's lobby and lab. Across 60 different test sessions, the volunteers browsed different subsets of 24 annotated objects as they walked around. Results show that Ear-AR plays the correct audio-annotations with good accuracy. The user-feedback is encouraging and points to further areas of research and applications.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419213"}, {"primary_key": "2660629", "vector": [], "sparse_vector": [], "title": "Joltik: enabling energy-efficient &quot;future-proof&quot; analytics on low-power wide-area networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensors have enabled a number of key applications. Due to their energy constraints, wireless sensors today communicate occasional short samples or pre-determined summary statistics of the data they collect. This means that computing every additional statistic at high fidelity incurs additional communication and energy overhead. This paper presents Joltik, a framework enabling general, future-proof, and energy-efficient analytics for low power wireless sensors. Joltik is general in that it summarizes sensed data from low-power devices without making assumptions on which specific statistical metric(s) are desired at the cloud and is future-proof, meaning it supports new, unforeseen metrics. Joltik is built upon recent theoretical advances in universal sketching, which can enable a Joltik sensor node to report a compact summary of observed data to enable a large class of statistical summaries. We address key system design and implementation challenges with respect to communication, memory, and computation bottlenecks that arise in practically realizing the potential benefits of universal sketching in the low-power regime. We present a proof-of-concept testbed evaluation of <PERSON><PERSON><PERSON> in LoRaWAN NUCLEO-L476RG boards and sensors. Across a range of realistic datasets, Joltik provides up to a 24.6× reduction in energy cost compared to transmitting raw data and outperforms many natural alternatives (e.g., sub-sampling, custom sketches, compressed sensing, and lossy compression) in terms of energy-accuracy trade-offs.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419204"}, {"primary_key": "2660630", "vector": [], "sparse_vector": [], "title": "Understanding power consumption of NB-IoT in the wild: tool and large-scale measurement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have seen a widespread deployment of NB-IoT networks for massive machine-to-machine communication in the emerging 5G era. Unfortunately, the key aspects of NB-IoT networks, such as radio access performance and power consumption have not been well-understood due to lack of effective tools and closed nature of operational cellular infrastructure. In this paper, we develop NB-Scope - the first hardware NB-IoT diagnostic tool that supports fine-grained fusion of power and protocol traces. We then conduct a large-scale field measurement study consisting of 30 nodes deployed at over 1,200 locations in 3 regions during a period of three months. Our in-depth analysis of the collected 49 GB traces showed that NB-IoT nodes yield significantly imbalanced energy consumption in the wild, up to a ratio of 75:1, which may lead to short battery lifetime and frequent network partition. Such a high performance variance can be attributed to several key factors including diverse network coverage levels, long tail power profile, and excessive control message repetitions. We then explore the optimization of NB-IoT base station settings on a software-defined eNodeB testbed, and suggest several important design aspects that can be considered by future NB-IoT specifications and chipsets.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419212"}, {"primary_key": "2660631", "vector": [], "sparse_vector": [], "title": "NEMO: enabling neural-enhanced video streaming on commodity mobile devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Juncheol Ye", "<PERSON><PERSON> Han"], "summary": "The demand for mobile video streaming has experienced tremendous growth over the last decade. However, existing methods of video delivery fall short of delivering high-quality video. Recent advances in neural super-resolution have opened up the possibility of enhancing video quality by leveraging client-side computation. Unfortunately, mobile devices cannot benefit from this because it is too expensive in computation and power-hungry.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419185"}, {"primary_key": "2660632", "vector": [], "sparse_vector": [], "title": "EagleEye: wearable camera-based person identification in crowded urban spaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present EagleEye, an AR-based system that identifies missing person (or people) in large, crowded urban spaces. Designing EagleEye involves critical technical challenges for both accuracy and latency. Firstly, despite recent advances in Deep Neural Network (DNN)-based face identification, we observe that state-of-the-art models fail to accurately identify Low-Resolution (LR) faces. Accordingly, we design a novel Identity Clarification Network to recover missing details in the LR faces, which enhances true positives by 78% with only 14% false positives. Furthermore, designing EagleEye involves unique challenges compared to recent continuous mobile vision systems in that it requires running a series of complex DNNs multiple times on a high-resolution image. To tackle the challenge, we develop Content-Adaptive Parallel Execution to optimize complex multi-DNN face identification pipeline execution latency using heterogeneous processors on mobile and cloud. Our results show that EagleEye achieves 9.07X faster latency compared to naive execution, with only 108 KBytes of data offloaded.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380881"}, {"primary_key": "2660633", "vector": [], "sparse_vector": [], "title": "Heimdall: mobile GPU coordination platform for augmented reality applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Heimdall, a mobile GPU coordination platform for emerging Augmented Reality (AR) applications. Future AR apps impose an explored challenging workload: i) concurrent execution of multiple Deep Neural Networks (DNNs) for physical world and user behavior analysis, and ii) seamless rendering in presence of the DNN execution for immersive user experience. Existing mobile deep learning frameworks, however, fail to support such workload: multi-DNN GPU contention slows down inference latency (e.g., from 59.93 to 1181 ms), and rendering-DNN GPU contention degrades frame rate (e.g., from 30 to ≈12 fps). Multi-tasking for desktop GPUs (e.g., parallelization, preemption) cannot be applied to mobile GPUs as well due to limited architectural support and memory bandwidth. To tackle the challenge, we design a Pseudo-Preemption mechanism which i) breaks down the bulky DNN into smaller units, and ii) prioritizes and flexibly schedules concurrent GPU tasks. We prototyped Heimdall over various mobile GPUs (i.e., recent Adreno series) and multiple AR app scenarios that involve combinations of 8 state-of-the-art DNNs. Our extensive evaluation shows that <PERSON><PERSON><PERSON><PERSON> enhances the frame rate from ≈12 to ≈30 fps while reducing the worst-case DNN inference latency by up to ≈15 times compared to the baseline multi-threading approach.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419192"}, {"primary_key": "2660634", "vector": [], "sparse_vector": [], "title": "Throughput optimization VNF placement for mapping SFC requests in MEC-NFV enabled networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network function virtualization (NFV) and mobile edge computing (MEC) enable internet service providers (ISPs) to deploy service function chains (SFCs) to achieve the convenience and performance benefit without incurring high service delay, capital expenditures, and operating expenses. In MEC-NFV networks, network services are deployed in the form of service function chains (SFCs), each consisting of an ordered set of virtual network functions (VNFs). In this paper, we focus on the VNF placement problem in MEC-NFV enabled networks, aiming to optimize the throughput of SFC requests (SFCRs). First, we involve the sharing mechanism of VNF instances in the problem formulations, which can improve network resource utilization and save more node resources. Then we formulate the problem mathematically and propose a correlation-based mapping algorithm to map SFCRs in the network. Moreover, we design an adjustment algorithm to optimize the mapped SFCRs. Evaluation results show that our proposed solution efficiently improves the throughput of SFCRs compared with the benchmarks.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3418169"}, {"primary_key": "2660636", "vector": [], "sparse_vector": [], "title": "Microscope: mobile service traffic decomposition for network slicing as a service.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The growing diversification of mobile services imposes requirements on network performance that are ever more stringent and heterogeneous. Network slicing aligns mobile network operation to this context, by enabling operators to isolate and customize network resources on a per-service basis. A key input for provisioning resources to slices is real-time information about the traffic demands generated by individual services. Acquiring such knowledge is however challenging, as legacy approaches based on in-depth inspection of traffic streams have high computational costs, which inflate with the widening adoption of encryption over data and control traffic. In this paper, we present a new approach to service-level demand estimation for slicing, which hinges on decomposition, i.e., the inference of per-service demands from traffic aggregates. By operating on total traffic volumes only, our approach overcomes the complexity and limitations of legacy traffic classification techniques, and provides a suitable input to recent 'Network Slice as a Service' (NSaaS) models. We implement decomposition through Microscope, a novel framework that uses deep learning to infer individual service demands from complex spatiotemporal features hidden in traffic aggregates. Microscope (i) transforms traffic data collected in irregular radio access deployments in a format suitable for convolutional learning, and (ii) can accommodate a variety of neural network architectures, including original 3D Deformable Convolutional Neural Networks (3D-DefCNNs) that we explicitly design for decomposition. Experiments with measurement data collected in an operational network demonstrate that Microscope accurately estimates per-service traffic demands with relative errors below 1.2%. Further, tests in practical NSaaS management use cases show that resource allocations informed by decomposition yield affordable costs for the mobile network operator.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419195"}, {"primary_key": "2660637", "vector": [], "sparse_vector": [], "title": "PDLens: smartphone knows drug effectiveness among Parkinson&apos;s via daily-life activity fusion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Huining Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Drug effectiveness management is a complicated and challenging task in chronic diseases, like Parkinson's Disease (PD). Drug effectiveness control is not only linked to personal out-of-pocket cost but also affecting the quality of life among patients with chronic symptoms. In the current practice, although that health and medical professionals still play a key role in the personalized treatment plan, the critical decision on drug selection falls upon the individual report when patients call in or visit the clinics. Unfortunately, most of the patients with chronic diseases either fail to report their day-to-day symptoms or have a limited access to medical resources due to economic constraints. In this paper, we present PDLens, a first smartphone-based system to detect drug effectiveness among Parkinson's in daily life. Specifically, PDLens can extract digital behavioral markers related to PD drug responses from everyday activities, including phone calls, standing, and walking. PDLens models the PD symptom severity on drug treatment and detects the change of severity scores before and after drug intake. A ranking-based multi-view deep neural network is developed to decide the drug effectiveness upon the symptom severity changes. To validate the performance of PDLens, we conduct a pilot study with 81 PD patients and monitor their smartphone activities and severity changes over 33693 drug intake events across six (6) months. Compared with the standard clinical drug effectiveness test developed by Motor Disorder Society, results reveal that PDLens is a promising tool to facilitate drug effectiveness detection among PD patients in their daily lives.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380889"}, {"primary_key": "2660638", "vector": [], "sparse_vector": [], "title": "OnRL: improving mobile video telephony via online reinforcement learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>uo<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma", "Xiao<PERSON> Chen"], "summary": "Machine learning models, particularly reinforcement learning (RL), have demonstrated great potential in optimizing video streaming applications. However, the state-of-the-art solutions are limited to an \"offline learning\" paradigm, i.e., the RL models are trained in simulators and then are operated in real networks. As a result, they inevitably suffer from the simulation-to-reality gap, showing far less satisfactory performance under real conditions compared with simulated environment. In this work, we close the gap by proposing OnRL, an online RL framework for real-time mobile video telephony. OnRL puts many individual RL agents directly into the video telephony system, which make video bitrate decisions in real-time and evolve their models over time. OnRL then aggregates these agents to form a high-level RL model that can help each individual to react to unseen network conditions. Moreover, OnRL incorporates novel mechanisms to handle the adverse impacts of inherent video traffic dynamics, and to eliminate risks of quality degradation caused by the RL model's exploration attempts. We implement OnRL on a mainstream operational video telephony system, Alibaba Taobao-live. In a month-long evaluation with 543 hours of video sessions from 151 real-world mobile users, OnRL outperforms the prior algorithms significantly, reducing video stalling rate by 14.22% while maintaining similar video quality.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3419186"}, {"primary_key": "2660639", "vector": [], "sparse_vector": [], "title": "M-Cube: a millimeter-wave massive MIMO software radio.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) technologies represent a cornerstone for emerging wireless network infrastructure, and for RF sensing systems in security, health, and automotive domains. Through a MIMO array of phased arrays with hundreds of antenna elements, mmWave can boost wireless bit-rates to 100+ Gbps, and potentially achieve near-vision sensing resolution. However, the lack of an experimental platform has been impeding research in this field. This paper fills the gap with M3 (M-Cube), the first mmWave massive MIMO software radio. M3 features a fully reconfigurable array of phased arrays, with up to 8 RF chains and 288 antenna elements. Despite the orders of magnitude larger antenna arrays, its cost is orders of magnitude lower, even when compared with state-of-the-art single RF chain mmWave software radios. The key design principle behind M3 is to hijack a low-cost commodity 802.11ad radio, separate the control path and data path inside, regenerate the phased array control signals, and recreate the data signals using a programmable baseband. Extensive experiments have demonstrated the effectiveness of the M3 design, and its usefulness for research in mmWave massive MIMO communication and sensing.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3380892"}, {"primary_key": "2660640", "vector": [], "sparse_vector": [], "title": "M-cube: an open-source millimeter-wave MIMO software radio for wireless communication and sensing applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) technologies represent a cornerstone for emerging wireless network infrastructure, and for RF sensing systems in security, health, and automotive domains. Through a MIMO array of phased arrays with hundreds of antenna elements, mmWave can boost wireless bit-rates to 100+ Gbps, and potentially achieve near-vision sensing resolution. However, the lack of an experimental platform has been impeding research in this field. We propose to fill the gap with M3 (M-Cube), the first mmWave massive MIMO software radio. M3 features a fully reconfigurable array of phased arrays, with up to 8 RF chains and 256 antenna elements. Despite the orders of magnitude larger antenna arrays, its cost is orders of magnitude lower, even when compared with state-of-the-art single RF chain mmWave software radios. In this demo, we will show M3's hardware modules, and demonstrate its usage in mmWave MIMO communication and sensing.", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224.3417327"}, {"primary_key": "2701920", "vector": [], "sparse_vector": [], "title": "MobiCom &apos;20: The 26th Annual International Conference on Mobile Computing and Networking, London, United Kingdom, September 21-25, 2020", "authors": [], "summary": "Pyroelectric infrared (PIR) sensors are considered to be promising devices for device-free localization due to its advantages of low cost, energy efficiency, and the immunity from multi-path fading. However, most of the existing PIR-based localization ...", "published": "2020-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3372224"}]