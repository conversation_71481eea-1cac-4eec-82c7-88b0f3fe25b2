[{"primary_key": "2466523", "vector": [], "sparse_vector": [], "title": "A Glimpse into the Past, Present, and Future of Engineering Interactive Computing Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The area of Engineering Interactive Computing Systems (EICS) is defined as the intersection between Software Engineering (SE) and Human-Computer Interaction (HCI). In this paper, we provide an overview of what EICS is and how it is positioned with respect to other venues in HCI, such as CHI, UIST, and IUI, highlighting its legacy and paying homage to past scientific events from which EICS emerged. We also take this opportunity to deliver a comparative analysis of the past, present, and perhaps future research and development questions raised in EICS by analyzing the EICS papers published from its first edition to this one, based on global and chronological word clouds, phrase nets, and topic modelling.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394973"}, {"primary_key": "2466615", "vector": [], "sparse_vector": [], "title": "ImageSense: An Intelligent Collaborative Ideation Tool to Support Diverse Human-Computer Partnerships.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Professional designers create mood boards to explore, visualize, and communicate hard-to-express ideas. We present ImageCascade, an intelligent, collaborative ideation tool that combines individual and shared work spaces, as well as collaboration with multiple forms of intelligent agents. In the collection phase, ImageCascade offers fluid transitions between serendipitous discovery of curated images via ImageCascade, combined text- and image-based Semantic search, and intelligent AI suggestions for finding new images. For later composition and reflection, ImageCascade provides semantic labels, generated color palettes, and multiple tag clouds to help communicate the intent of the mood board. A study of nine professional designers revealed nuances in designers' preferences for designer-led, system-led, and mixed-initiative approaches that evolve throughout the design process. We discuss the challenges in creating effective human-computer partnerships for creative activities, and suggest directions for future research.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392850"}, {"primary_key": "2466622", "vector": [], "sparse_vector": [], "title": "Socio-Spatial Comfort: Using Vision-based Analysis to Inform User-Centred Human-Building Interactions.", "authors": ["Bokyung Lee", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A well-designed workplace has a direct and significant impact on our work experiences and productivity. In this paper, we investigate how office interior layouts influence the way we socially experience office buildings. We extend the previous work that examined static social formations of office workers by looking at their dynamic movements during informal desk visiting interactions. With a month of video data collected in the office, we implemented a vision-based analysis system that enables us to examine how people occupy space in social contexts in relation to desk configurations. The results showed that both social territoriality and approach path highlight social comfort in human-building interactions, which are different from efficiency or path optimization. From these findings, we propose the concepts of socio-spatial comfort: social buffers, privacy buffers, and varying proxemics to inform a user-centered way of designing human building interactions and architecture.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432937"}, {"primary_key": "2466732", "vector": [], "sparse_vector": [], "title": "Model-based Testing of Interactive Systems using Interaction Sequences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Testing is an important part of the software engineering process to help ensure that systems will behave as expected. In this paper we investigate interactive system testing, taking into consideration the different components of the system. Interactive systems have three different components, the interactive, functional and overlap. The interactive component is the interface of the interactive system, the functional the underlying instructions of the interactive system, and the overlap component the point at which the interactive and functional components intersect. The interactive and functional components are often tested separately, however, problems can occur where these components overlap. Therefore, in this paper we present a model-based testing approach specifically designed to inspect the overlap component behaviour and to ensure that it behaves as expected.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397873"}, {"primary_key": "2466763", "vector": [], "sparse_vector": [], "title": "&quot;An Ideal Human&quot;: Expectations of AI Teammates in Human-AI Teaming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Driven by state-of-the-art AI technologies, human-AI collaboration has become an important area in computer-supported teamwork research. While human-AI collaboration has been investigated in various domains, more research is needed to explore human perceptions and expectations of AI teammates in human-AI teaming. To achieve an in-depth understanding of how people perceive AI teammates and what they expect from AI teammates in human-AI teaming, we conducted a survey with 213 participants and a follow-up interview with 20 participants. Considering the context-dependency of teamwork, we chose to study human-AI teaming in the context of multiplayer online games as a case study. This study shows that people have mixed feelings toward AI teammates but hold a positive attitude toward future collaboration with AI teammates in general. Our findings highlight people's expectations for AI teammates in a rapidly changing collaborative environment (e.g., instrumental skills for in-game tasks, shared understanding between humans and AI, communication capabilities, human-like behaviors and performance), as well as factors that impact people's willingness to team up with AI teammates (e.g., pre-existing attitudes toward AI, previous collaboration experience with humans). We contribute to CSCW by shedding light on how AI should be structured in human-AI teaming to support highly complex collaborative activities in CSCW environments.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432945"}, {"primary_key": "2466508", "vector": [], "sparse_vector": [], "title": "Human-AI Collaboration in a Cooperative Game Setting: Measuring Social Perception and Outcomes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Human-AI interaction is pervasive across many areas of our day to day lives. In this paper, we investigate human-AI collaboration in the context of a collaborative AI-driven word association game with partially observable information. In our experiments, we test various dimensions of subjective social perceptions (rapport, intelligence, creativity and likeability) of participants towards their partners when participants believe they are playing with an AI or with a human. We also test subjective social perceptions of participants towards their partners when participants are presented with a variety of confidence levels. We ran a large scale study on Mechanical Turk (n=164) of this collaborative game. Our results show that when participants believe their partners were human, they found their partners to be more likeable, intelligent, creative and having more rapport and use more positive words to describe their partner's attributes than when they believed they were interacting with an AI partner. We also found no differences in game outcome including win rate and turns to completion. Drawing on both quantitative and qualitative findings, we discuss AI agent transparency, include design implications for tools incorporating or supporting human-AI collaboration, and lay out directions for future research. Our findings lead to implications for other forms of human-AI interaction and communication.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415167"}, {"primary_key": "2466527", "vector": [], "sparse_vector": [], "title": "My Team Will Go On: Differentiating High and Low Viability Teams through Team Interaction.", "authors": ["Hancheng Cao", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "N&apos;godjigui Junior Diarrassouba", "<PERSON>", "<PERSON>"], "summary": "Understanding team viability --- a team's capacity for sustained and future success --- is essential for building effective teams. In this study, we aggregate features drawn from the organizational behavior literature to train a viability classification model over a dataset of 669 10-minute text conversations of online teams. We train classifiers to identify teams at the top decile (most viable teams), 50th percentile (above a median split), and bottom decile (least viable teams), then characterize the attributes of teams at each of these viability levels. We find that a lasso regression model achieves an accuracy of .74--.92 AUC ROC under different thresholds of classifying viability scores. From these models, we identify the use of exclusive language such as 'but' and 'except', and the use of second person pronouns, as the most predictive features for detecting the most viable teams, suggesting that active engagement with others' ideas is a crucial signal of a viable team. Only a small fraction of the 10-minute discussion, as little as 70 seconds, is required for predicting the viability of team interaction. This work suggests opportunities for teams to assess, track, and visualize their own viability in real time as they collaborate.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432929"}, {"primary_key": "2466536", "vector": [], "sparse_vector": [], "title": "Exploring User Defined Gestures for Ear-Based Interactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The human ear is highly sensitive and accessible, making it especially suitable for being used as an interface for interacting with smart earpieces or augmented glasses. However, previous works on ear-based input mainly address gesture sensing technology and researcher-designed gestures. This paper aims to bring more understandings of gesture design. Thus, for a user elicitation study, we recruited 28 participants, each of whom designed gestures for 31 smart device-related tasks. This resulted in a total of 868 gestures generated. Upon the basis of these gestures, we compiled a taxonomy and concluded the considerations underlying the participants' designs that also offer insights into their design rationales and preferences. Thereafter, based on these study results, we propose a set of user-defined gestures and share interesting findings. We hope this work can shed some light on not only sensing technologies of ear-based input, but also the interface design of future wearable interfaces.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427314"}, {"primary_key": "2466552", "vector": [], "sparse_vector": [], "title": "Un Grano de Arena: Infrastructural Care, Social Media Platforms, and the Venezuelan Humanitarian Crisis.", "authors": ["<PERSON><PERSON>"], "summary": "Venezuela is in the midst of a humanitarian crisis. In addition to food and medicinal shortages, violent crime has risen dramatically since 2014, spurring a mass exodus from the country. In order to cope with persistent material, informational, and digital infrastructural breakdowns that their friends and family in Venezuela are facing, members of the Venezuelan diaspora have turned to social media platforms to support people they left behind. Through semi-structured interviews and participant observation, I uncover the ways participants form a critical infrastructure for people in Venezuela. I describe participants' actions as infrastructural care --- infrastructural action as a form of caring for others at a distance through the ongoing management of resources, relationships, and infrastructures. Infrastructural care consists of relational, negotiated, and dialectic actions that provide critical support while also generating ongoing tensions as participants are geographically separated from the crisis and, through their involvement, are forced to confront their own experiences of trauma. In addition to proposing the lens of infrastructural care, this paper contributes to our understandings of the ways people cope with an ongoing humanitarian crisis at a distance and how social media platforms fit in with wider ecologies of efforts.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432946"}, {"primary_key": "2466559", "vector": [], "sparse_vector": [], "title": "A Dozen Stickers on a Mailbox: Physical Encounters and Digital Interactions in a Local Sharing Community.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many non-profit peer-to-peer exchange arrangements and profit-driven, multi-sided online marketplaces leverage underutilized resources, such as tools, to optimize their use to capacity. They often rely on a digital platform in pursuit of their social aspirations and/or economic objectives. We report on a field study of a local sharing community that employs a set of stickers illustrating different household items, typically placed on community members' mailboxes, along with complementary digital tools. The stickers are used to communicate the availability of resources among neighbors to facilitate social encounters and to encourage sustainable use and re-use of shared resources. Through in-depth qualitative interviews with sixteen participants, we describe the opportunities and limitations of this approach to peer-to-peer exchange. We offer insights for designers of resource sharing communities into facilitating face-to-face encounters and the online interactions needed to support them.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432939"}, {"primary_key": "2466572", "vector": [], "sparse_vector": [], "title": "Data Migrations: Exploring the Use of Social Media Data as Evidence for Human Rights Advocacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Social media platforms offer a rich repository of crowdsourced information that has the potential to monitor human rights violations. The challenge is to quantify, interpret, and situate such unstructured data streams in the broader context, which remains under-investigated in existing CSCW research. Addressing these challenges demands computational solutions to extract large volumes of data in conjunction with human intervention to transition the data streams into the offline context to render them usable and actionable. Following an iterative human-in-the-loop computational approach, we explore whether citizen reports of abductions concentrated on Facebook groups can be useful to complete official records on the ongoing crisis of disappearances in Mexico. We conceptualize three key practices of the process of transitioning the data from online to offline, followed by seven qualitative characteristics of the data streams that contribute to each stage of the process. Our research contributes with an initial understanding of the challenges and opportunities of migrating the local knowledge from online communities to be used as evidence by organizations seeking to address institutional failures.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434177"}, {"primary_key": "2466573", "vector": [], "sparse_vector": [], "title": "UX-Painter: An Approach to Explore Interaction Fixes in the Browser.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Usability and user interaction improvement is a central task in web development to guarantee the success of a web application. However, designers are barely able to keep up with the current development cycle because their practices are too costly, while interaction issues accumulate in applications that end-users keep suffering. In this work, we propose a method for designers to rapidly explore solutions through visual programming to the interaction problems of an application under development, even when it has been already deployed. The method is realized by a tool called UX-Painter, an exploratory tool for designers to apply quick fixes to interaction issues at the client-side of a web application without the need of any script programming knowledge. The palette of available fixes in UX-Painter are client-side web refactorings, i.e., changes to web page elements that solve specific user interaction problems without changing the underlying functionality. UX-Painter allows designers to quickly set up new versions of a web application by combining refactorings to create alternative designs for user testing or an inspection review. UX-Painter also provides the means to communicate design improvements, as a sequence of refactorings with clear semantics. We show the feedback provided by interviews with designers about UX-Painter's functionality and the results of a user test about its usability.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397877"}, {"primary_key": "2466591", "vector": [], "sparse_vector": [], "title": "Human Factors in Model Interpretability: Industry Practices, Challenges, and Needs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As the use of machine learning (ML) models in product development and data-driven decision-making processes became pervasive in many domains, people's focus on building a well-performing model has increasingly shifted to understanding how their model works. While scholarly interest in model interpretability has grown rapidly in research communities like HCI, ML, and beyond, little is known about how practitioners perceive and aim to provide interpretability in the context of their existing workflows. This lack of understanding of interpretability as practiced may prevent interpretability research from addressing important needs, or lead to unrealistic solutions. To bridge this gap, we conducted 22 semi-structured interviews with industry practitioners to understand how they conceive of and design for interpretability while they plan, build, and use their models. Based on a qualitative analysis of our results, we differentiate interpretability roles, processes, goals and strategies as they exist within organizations making heavy use of ML models. The characterization of interpretability work that emerges from our analysis suggests that model interpretability frequently involves cooperation and mental model comparison between people in different roles, often aimed at building trust not only between people and models but also between people within the organization. We present implications for design that discuss gaps between the interpretability challenges that practitioners face in their practice and approaches proposed in the literature, highlighting possible research directions that can better address real-world needs.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392878"}, {"primary_key": "2466593", "vector": [], "sparse_vector": [], "title": "Toward Interactively Balancing the Screen Time of Actors Based on Observable Phenotypic Traits in Live Telecast.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Several prominent studies have shown that the imbalanced on-screen exposure of observable phenotypic traits like gender and skin-tone in movies, TV shows, live telecasts, and other visual media can reinforce gender and racial stereotypes in society. Researchers and human rights organizations alike have long been calling to make media producers more aware of such stereotypes. While awareness among media producers is growing, balancing the presence of different phenotypes in a video requires substantial manual effort and can typically only be done in the post-production phase. The task becomes even more challenging in the case of a live telecast where video producers must make instantaneous decisions with no post-production phase to refine or revert a decision. In this paper, we propose Screen-Balancer, an interactive tool that assists media producers in balancing the presence of different phenotypes in a live telecast. The design of Screen-Balancer is informed by a field study conducted in a professional live studio. Screen-Balancer analyzes the facial features of the actors to determine phenotypic traits using facial detection packages; it then facilitates real-time visual feedback for interactive moderation of gender and skin-tone distributions. To demonstrate the effectiveness of our approach, we conducted a user study with 20 participants and asked them to compose live telecasts from a set of video streams simulating different camera angles, and featuring several male and female actors with different skin-tones. The study revealed that the participants were able to reduce the difference of screen times of male and female actors by 43%, and that of light-skinned and dark-skinned actors by 44%, thus showing the promise and potential of using such a tool in commercial production systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415225"}, {"primary_key": "2466602", "vector": [], "sparse_vector": [], "title": "MAMAS: Supporting Parent-Child Mealtime Interactions Using Automated Tracking and Speech Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hwajung Hong"], "summary": "Many parents of young children find it challenging to deal with their children's eating problems, and parent--child mealtime interaction is fundamental in forming children's healthy eating habits. In this paper, we present the results of a three-week study through which we deployed a mealtime assistant application, MAMAS, for monitoring parent--child mealtime conversation and food intake with 15 parent--child pairs. Our findings indicate that the use of MAMAS helped 1) increase children's autonomy during mealtime, 2) enhance parents' self-awareness of their words and behaviors, 3) promote the parent--child relationship, and 4) positively influence the mealtime experiences of the entire family. The study also revealed some challenges in eating behavior interventions due to the complex dynamics of childhood eating problems. Based on the findings, we discuss how a mealtime assistant application can be better designed for parents and children with challenging eating behaviors.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392876"}, {"primary_key": "2466611", "vector": [], "sparse_vector": [], "title": "Conceptual Metaphors Impact Perceptions of Human-AI Collaboration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Fei-Fei", "<PERSON>", "<PERSON>"], "summary": "With the emergence of conversational artificial intelligence (AI) agents, it is important to understand the mechanisms that influence users' experiences of these agents. We study a common tool in the designer's toolkit: conceptual metaphors. Metaphors can present an agent as akin to a wry teenager, a toddler, or an experienced butler. How might a choice of metaphor influence our experience of the AI agent? Sampling metaphors along the dimensions of warmth and competence---defined by psychological theories as the primary axes of variation for human social perception---we perform a study (N=260) where we manipulate the metaphor, but not the behavior, of a Wizard-of-Oz conversational agent. Following the experience, participants are surveyed about their intention to use the agent, their desire to cooperate with the agent, and the agent's usability. Contrary to the current tendency of designers to use high competence metaphors to describe AI products, we find that metaphors that signal low competence lead to better evaluations of the agent than metaphors that signal high competence. This effect persists despite both high and low competence agents featuring human-level performance and the wizards being blind to condition. A second study confirms that intention to adopt decreases rapidly as competence projected by the metaphor increases. In a third study, we assess effects of metaphor choices on potential users' desire to try out the system and find that users are drawn to systems that project higher competence and warmth. These results suggest that projecting competence may help attract new users, but those users may discard the agent unless it can quickly correct with a lower competence metaphor. We close with a retrospective analysis that finds similar patterns between metaphors and user attitudes towards past conversational agents such as <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415234"}, {"primary_key": "2466617", "vector": [], "sparse_vector": [], "title": "Mediating Community-AI Interaction through Situated Explanation: The Case of AI-Led Moderation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Artificial intelligence (AI) has become prevalent in our everyday technologies and impacts both individuals and communities. The explainable AI (XAI) scholarship has explored the philosophical nature of explanation and technical explanations, which are usually driven by experts in lab settings and can be challenging for laypersons to understand. In addition, existing XAI research tends to focus on the individual level. Little is known about how people understand and explain AI-led decisions in the community context. Drawing from XAI and activity theory, a foundational HCI theory, we theorize how explanation is situated in a community's shared values, norms, knowledge, and practices, and how situated explanation mediates community-AI interaction. We then present a case study of AI-led moderation, where community members collectively develop explanations of AI-led decisions, most of which are automated punishments. Lastly, we discuss the implications of this framework at the intersection of CSCW, HCI, and XAI.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415173"}, {"primary_key": "2466630", "vector": [], "sparse_vector": [], "title": "&quot;It&apos;s all about conversation&quot;: Challenges and Concerns of Faculty and Students in the Arts, Humanities, and the Social Sciences about Education at Scale.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As colleges and universities continue their commitment to increasing access to higher education through offering education online and at scale, attention on teaching open-ended subjects online and at scale, mainly the arts, humanities, and the social sciences, remains limited. While existing work in scaling open-ended courses primarily focuses on the evaluation and feedback of open-ended assignments, there is a lack of understanding of how to effectively teach open-ended, university-level courses at scale. To better understand the needs of teaching large-scale, open-ended courses online effectively in a university setting, we conducted a mixed-methods study with university instructors and students, using surveys and interviews, and identified five critical pedagogical elements that distinguish the teaching and learning experiences in an open-ended course from that in a non-open-ended course. An overarching theme for the five elements was the need to support students' self-expression. We further uncovered open challenges and opportunities when incorporating the five critical pedagogical elements into large-scale, open-ended courses online in a university setting, and suggested six future research directions: (1) facilitate in-depth conversations, (2) create a studio-friendly environment, (3) adapt to open-ended assessment, (4) scale individual open-ended feedback, (5) establish trust for self-expression, and (6) personalize instruction and harness the benefits of student diversity.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432915"}, {"primary_key": "2466637", "vector": [], "sparse_vector": [], "title": "Flex-ER: A Platform to Evaluate Interaction Techniques for Immersive Visualizations.", "authors": ["María<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Extended Reality (XR) systems (which encapsulate AR, VR and MR) is an emerging field which enables the development of novel visualization and interaction techniques. To develop and to assess such techniques, researchers and designers have to face choices in terms of which development tools to adopt, and with very little information about how such tools support some of the very basic tasks for information visualization, such as selecting data items, linking and navigating. As a solution, we propose Flex-ER, a flexible web-based environment that enables users to prototype, debug and share experimental conditions and results. Flex-ER enables users to quickly switch between hardware platforms and input modalities by using a JSON specification that supports both defining interaction techniques and tasks at a low cost. We demonstrate the flexibility of the environment through three task design examples: brushing, linking and navigating. A qualitative user study suggest that Flex-ER can be helpful to prototype and explore different interaction techniques for immersive analytics.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427323"}, {"primary_key": "2466650", "vector": [], "sparse_vector": [], "title": "Interaction with Virtual Content using Augmented Reality: A User Study in Assembly Procedures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Assembly procedures are a common task in several domains of application. Augmented Reality (AR) has been considered as having great potential in assisting users while performing such tasks. However, poor interaction design and lack of studies, often results in complex and hard to use AR systems. This paper considers three different interaction methods for assembly procedures (Touch gestures in a mobile device; Mobile Device movements; 3D Controllers and See-through HMD). It also describes a controlled experiment aimed at comparing acceptance and usability between these methods in an assembly task using Lego blocks. The main conclusions are that participants were faster using the 3D controllers and Video see-through HMD. Participants also preferred the HMD condition, even though some reported light symptoms of nausea, sickness and/or disorientation, probably due to limited resolution of the HMD cameras used in the video see-through setting and some latency issues. In addition, although some research claims that manipulation of virtual objects with movements of the mobile device can be considered as natural, this condition was the least preferred by the participants.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427324"}, {"primary_key": "2466661", "vector": [], "sparse_vector": [], "title": "Between Subjectivity and Imposition: Power Dynamics in Data Annotation for Computer Vision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The interpretation of data is fundamental to machine learning. This paper investigates practices of image data annotation as performed in industrial contexts. We define data annotation as a sense-making practice, where annotators assign meaning to data through the use of labels. Previous human-centered investigations have largely focused on annotators? subjectivity as a major cause of biased labels. We propose a wider view on this issue: guided by constructivist grounded theory, we conducted several weeks of fieldwork at two annotation companies. We analyzed which structures, power relations, and naturalized impositions shape the interpretation of data. Our results show that the work of annotators is profoundly informed by the interests, values, and priorities of other actors above their station. Arbitrary classifications are vertically imposed on annotators, and through them, on data. This imposition is largely naturalized. Assigning meaning to data is often presented as a technical matter. This paper shows it is, in fact, an exercise of power with multiple implications for individuals and society.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415186"}, {"primary_key": "2466680", "vector": [], "sparse_vector": [], "title": "Changing Roles and Contexts: Symbolic Interactionism in the Sharing of Food and Eating Practices between Remote, Intergenerational Family Members.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cooking and eating together is a prominent social experience amongst families. Older adults and their adult children who live apart often communicate about these experiences to stay aware of each other's health and wellbeing. In this paper, we examine current practices surrounding the communication of eating habits and meal preparation between older adults and their adult children living apart. We interviewed 18 older parents and nine adult children to understand their experiences. While most participants found the sharing of eating experiences to be rewarding and enlightening of family health behaviors, family roles and contexts could create tensions around this type of conversation. Applying the lens of symbolic interactionism theory, we examine how changing roles and contexts influence the conversation of eating and meal preparation and how participants manage tensions. We discuss future design opportunities to support family collaboration around food and eating, accounting for the transition of roles and contexts.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392848"}, {"primary_key": "2466694", "vector": [], "sparse_vector": [], "title": "Multitouch Interaction with Parallel Coordinates on Large Vertical Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a multitouch vocabulary for interacting with parallel coordinates plots on wall-sized displays. The gesture set relies on principles such as two-finger range definition, a functional distinction of background and foreground for applying the Hold-and-Move concept to wall-sized displays as well as fling-based interaction for triggering and controlling long-range movements. Our implementation demonstrates that out-of-reach problems and limitations regarding multitouch technology and display size can be tackled by the coherent integration of our multitouch gestures. Expert reviews indicate that our gesture vocabulary helps to solve typical analysis tasks that require interaction beyond arms' reach, and it also shows how often certain gestures were used.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427327"}, {"primary_key": "2466696", "vector": [], "sparse_vector": [], "title": "Religion and Sustainability: Lessons of Sustainable Computing from Islamic Religious Communities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "While persuasion has often been considered an important design tool for achieving sustainable behavior, a growing scholarship is criticizing it for its narrow focus on individuals and an overarching economic worldview. This criticism is often based on the limitations of economic-rationales that many persuasive design efforts hold and cannot fully capture the values of people who reside outside the modern scientific world - especially where values originate from and are shaped by religiosity and spirituality. We join this discourse and argue that such a narrow view of persuasion sidelines the theological roots. Based on our six-month long ethnography with the Islamic communities in a Bangladeshi city, Kushtia, we describe how 'motivation' and 'habit' are built there - two of the basic components of persuasion. Drawing from a rich body of literature on the sociology of religions and theology, we highlight how Islamic values are closely tied to the idea of persuasion and reflect a vision of sustainable living. We further discuss how such a deeper understanding of religious values can help design for sustainable living and broaden the scope of CSCW literature in the various domains.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415199"}, {"primary_key": "2466698", "vector": [], "sparse_vector": [], "title": "Simple and Steady Interactions Win the Healthy Mentality: Designing a Chatbot Service for the Elderly.", "authors": ["Hyeyoung Ryu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>"], "summary": "Although suicide among older adults is increasing in severity in the aging among South Koreans, older adult depression and anxiety, which are two crucial factors affecting suicide in this population, are not adequately addressed by the status quo. Existing products and services emphasize physical needs; however, they still lack a user-oriented approach. Moreover, offers for mental health care are costly and exclude potential users from the development process, thus inhibiting older adults from having better access to such services. To address this problem, we designed and developed a mental health care chatbot, Yeon<PERSON>bot, to decrease anxiety and depression among older adults. Yeonheebot initiates conversations regarding daily care for the well-being of older adults and provides features related to their interests as initiatives for using the mental health care chatbot. To examine whether a short-term, yet repeating conversation with the agent could help reduce anxiety and depression among older adults, we conducted a 2-week field study with 25 users. The results indicate that the constant use of Yeonheebot decreased the levels of anxiety and depression by 36% and 18%, respectively. Our research implies that simple and repeated interactions could help Korean older adults cope with anxiety and depression.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415223"}, {"primary_key": "2466701", "vector": [], "sparse_vector": [], "title": "&quot;I just shared your responses&quot;: Extending Communication Privacy Management Theory to Interactions with Conversational Agents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Malte F. <PERSON>", "Natalya N<PERSON>"], "summary": "Conversational agents are increasingly becoming integrated into everyday technologies and can collect large amounts of data about users. As these agents mimic interpersonal interactions, we draw on communication privacy management theory to explore people's privacy expectations with conversational agents. We conducted a 3x3 factorial experiment in which we manipulated agents' social interactivity and data sharing practices to understand how these factors influence people's judgments about potential privacy violations and their evaluations of agents. Participants perceived agents that shared response data with advertisers more negatively compared to agents that shared such data with only their companies; perceptions of privacy violations did not differ between agents that shared data with their companies and agents that did not share information at all. Participants also perceived the socially interactive agent's sharing practices less negatively than those of the other agents, highlighting a potential privacy vulnerability that users are exposed to in interactions with socially interactive conversational agents.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375188"}, {"primary_key": "2466726", "vector": [], "sparse_vector": [], "title": "Improving Social Awareness Through DANTE: Deep Affinity Network for Clustering Conversational Interactants.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a data-driven approach to detect conversational groups by identifying spatial arrangements typical of these focused social encounters. Our approach uses a novel Deep Affinity Network (DANTE) to predict the likelihood that two individuals in a scene are part of the same conversational group, considering their social context. The predicted pair-wise affinities are then used in a graph clustering framework to identify both small (e.g., dyads) and large groups. The results from our evaluation on multiple, established benchmarks suggest that combining powerful deep learning methods with classical clustering techniques can improve the detection of conversational groups in comparison to prior approaches. Finally, we demonstrate the practicality of our approach in a human-robot interaction scenario. Our efforts show that our work advances group detection not only in theory, but also in practice.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392824"}, {"primary_key": "2466733", "vector": [], "sparse_vector": [], "title": "Breaking the Accessibility Barrier in Non-Visual Interaction with PDF Forms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "PDF forms are ubiquitous. Businesses big and small, government agencies, health and educational institutions and many others have all embraced PDF forms. People use PDF forms for providing information to these entities. But people who are blind frequently find it very difficult to fill out PDF forms with screen readers, the standard assistive software that they use for interacting with computer applications. Firstly, many of the them are not even accessible as they are non-interactive and hence not editable on a computer. Secondly, even if they are interactive, it is not always easy to associate the correct labels with the form fields, either because the labels are not meaningful or the sequential reading order of the screen reader misses the visual cues that associate the correct labels with the fields. In this paper we present a solution to the accessibility problem of PDF forms. We leverage the fact that many people with visual impairments are familiar with web browsing and are proficient at filling out web forms. Thus, we create a web form layer over the PDF form via a high fidelity transformation process that attempts to preserve all the spatial relationships of the PDF elements including forms, their labels and the textual content. Blind people only interact with the web forms, and the filled out web form fields are transparently transferred to the corresponding fields in the PDF form. An optimization algorithm automatically adjusts the length and width of the PDF fields to accommodate arbitrary size field data. This ensures that the filled out PDF document does not have any truncated form-field values, and additionally, it is readable. A user study with fourteen users with visual impairments revealed that they were able to populate more form fields than the status quo and the self-reported user experience with the proposed interface was superior compared to the status quo.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397868"}, {"primary_key": "2466744", "vector": [], "sparse_vector": [], "title": "Evaluating an Interactive Memory Analysis Tool: Findings from a Cognitive Walkthrough and a User Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Memory analysis tools are essential for finding and fixing anomalies in the memory usage of software systems (e.g., memory leaks). Although numerous tools are available, hardly any empirical studies exist on their usefulness for developers in typical usage scenarios. Instead, most evaluations are limited to reporting performance metrics. We thus conducted a study to empirically assess the usefulness of the interactive memory analysis tool AntTracks Analyzer. Specifically, we first report findings from assessing the tool using a cognitive walkthrough, guided by the Cognitive Dimensions of Notations Framework. We then present the results of a qualitative user study involving 14 subjects who used AntTracks to detect and resolve memory anomalies. We report lessons learned from the study and implications for developers of interactive memory analysis tools. We hope that our results will help researchers and developers of memory analysis tools in defining, selecting, and improving tool capabilities.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394977"}, {"primary_key": "2466747", "vector": [], "sparse_vector": [], "title": "Understanding Gesture and Speech Multimodal Interactions for Manipulation Tasks in Augmented Reality Using Unconstrained Elicitation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This research establishes a better understanding of the syntax choices in speech interactions and of how speech, gesture, and multimodal gesture and speech interactions are produced by users in unconstrained object manipulation environments using augmented reality. The work presents a multimodal elicitation study conducted with 24 participants. The canonical referents for translation, rotation, and scale were used along with some abstract referents (create, destroy, and select). In this study time windows for gesture and speech multimodal interactions are developed using the start and stop times of gestures and speech as well as the stoke times for gestures. While gestures commonly precede speech by 81 ms we find that the stroke of the gesture is commonly within 10 ms of the start of speech. Indicating that the information content of a gesture and its co-occurring speech are well aligned to each other. Lastly, the trends across the most common proposals for each modality are examined. Showing that the disagreement between proposals is often caused by a variation of hand posture or syntax. Allowing us to present aliasing recommendations to increase the percentage of users' natural interactions captured by future multimodal interactive systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427330"}, {"primary_key": "2466756", "vector": [], "sparse_vector": [], "title": "Human-Machine Cooperative Video Anomaly Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jiax<PERSON> Gu", "Qingyang Li", "<PERSON>"], "summary": "It is still a challenge to detect anomalous events in video sequences in the field of computer vision due to heavy object occlusions, varying crowded densities and complex situations. To address this, we propose a novel human-machine cooperative approach which uses human feedback on anomaly confirmation to inform and enhance video anomaly detection. Specifically, we analyze the spatio-temporal characteristics of sequential frames of a video from the appearance and motion perspective from which spatial and temporal features are identified and extracted. We then develop a convolutional autoencoder neural network to compute an abnormal score based on reconstruction errors. In this process, a group of experts will provide human feedback to a certain proportion of classified frames to be incorporated into the model, and also the final judgment for the event anomalies for training and classification. The proposed approach is evaluated on 3 publicly available surveillance datasets, showing improved accuracy and competitive performance (93.7% AUC) with respect to the best performance (90.6% AUC) of the state-of-the-art approaches. The approach has not been previously seen to the best of our knowledge.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434183"}, {"primary_key": "2466494", "vector": [], "sparse_vector": [], "title": "School&apos;s Back: Scaffolding Reminiscence in Social Virtual Reality with Older Adults.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Thuong N. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social virtual reality (social VR) is an emerging technology that has the potential to support unique social experiences for groups of older adults. In this paper we explore the use of social VR to support group reminiscence, an activity that has been shown to have a positive impact on the lives of older adults. We developed School Days, a bespoke social VR application that enables groups of geographically dispersed older adults to meet in a virtual environment to reminisce about their school experiences. We conducted a user study over the course of 5 months with 16 participants aged 70--81 to evaluate how School Days supported reminiscence. In this paper, we focus on how the use of reminiscence scaffolding features in School Days impacted on the older adults' ability to participate more fully in the reminiscence activities. Our results illustrate the value of social VR for connecting older adults over distance, and contribute new knowledge of how virtual environments can be designed to scaffold reminiscence; how techniques such as 3D conversation starters and individual artefacts can be used to scaffold reminiscence; and how pre-recorded holographic stories (Avacasts) can be used to introduce new perspectives and prompt self-reflection. We contribute five design reflections aimed at guiding the design of future reminiscence tools in social VR.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434176"}, {"primary_key": "2466495", "vector": [], "sparse_vector": [], "title": "Holy Tweets: Exploring the Sharing of the Quran on Twitter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While social media offer users a platform for self-expression, identity exploration, and community management, among other functions, they also offer space for religious practice and expression. In this paper, we explore social media spaces as they subtend new forms of religious experiences and rituals. We present a mixed-method study to understand the practice of sharing Quran verses on Arabic Twitter in their cultural context by combining a quantitative analysis of the most shared Quran verses, the topics covered by these verses, and the modalities of sharing, with a qualitative study of users' goals. This analysis of a set of 2.6 million tweets containing Quran verses demonstrates that online religious expression in the form of sharing Quran verses both extends offline religious life and supports new forms of religious expression including goals such as doing good deeds, giving charity, holding memorials, and showing solidarity. By analysing the responses on a survey, we found that our Arab Muslim respondents conceptualize social media platforms as everlasting, at least beyond their lifetimes, where they consider them to be effective for certain religious practices, such as reciting Quran, supplication (dua), and ceaseless charity. Our quantitative analysis of the most shared verses of the Quran underlines this commitment to religious expression as an act of worship, highlighting topics such as the hereafter, God's mercy, and sharia law. We note that verses on topics such as jihad are shared much less often, contradicting some media representation of Muslim social media use and practice.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415230"}, {"primary_key": "2466496", "vector": [], "sparse_vector": [], "title": "Effect of Physical Challenging Activity on Tactile Texture Recognition for Mobile Surface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous research demonstrated the ability for users to accurately recognize tactile textures on mobile surface. However, the experiments were only run in a lab setting and the ability for users to recognize tactile texture in a real-world environment remains unclear. In this paper, we investigate the effects of physical challenging activities on tactile textures recognition. We consider five conditions: (1) seated on an office, (2) standing in an office, (3) seated in the tramway, (4) standing in the tramway and (5) walking in the street. Our findings indicate that when walking, performances deteriorated compared to the remainder conditions. However, despite this deterioration, the recognition rate stay higher than 82% suggesting that tactile texture could be effectively recognized and used by users in different physical challenges activities including walking.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427318"}, {"primary_key": "2466497", "vector": [], "sparse_vector": [], "title": "Tangible Privacy: Towards User-Centric Sensor Designs for Bystander Privacy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sensor-enabled computers in the form of 'IoT' devices such as home security cameras and voice assistants are increasingly becoming pervasive in our environment. With the embedded cameras and microphones in these devices, this 'invasion' of our everyday spaces can pose significant threats to the privacy of bystanders. Because of their complex functionality, even when people attempt privacy measures (such as asking the owner to \"turn the camera off\"), these devices may still record information because of the lack of a 'real' off button. With the ambiguities of current designs, a bystander's perceived privacy can diverge from their actual privacy. Indeed, being able to assess one's actual privacy is a key aspect in managing one's privacy according to <PERSON><PERSON>'s theory of boundary regulation, and current designs fall short in assuring people of their privacy. To understand how people as bystanders manage their privacy with IoT devices, we conducted an interview study about people's perceptions of and behaviors around current IoT devices. We find that although participants' behaviors line up with <PERSON><PERSON>'s theory of boundary regulation, in the face of uncertainty about their privacy, they desire or engage in various 'tangible' workarounds. Based on our findings, we identify and introduce the concept of 'tangible privacy' as being essential to boundary regulation with IoT devices. We argue that IoT devices should be designed in a way that clearly and unambiguously conveys sensor states to people around them and make actionable design recommendations to provide strong privacy assurances to bystanders.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415187"}, {"primary_key": "2466498", "vector": [], "sparse_vector": [], "title": "&quot;We want to push the industry via communication&quot;... Designing Communication Measures to Foster Gender Diversity in a Video Game Company.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Participation of women in IT is still low and companies wonder which external communication measures are necessary to attract more female personnel. To gain a richer understanding of adequate gender sensitive ways of communicating towards girls and women, one needs to take into account contextual challenges. Following a Participatory Action Research approach, we conducted a qualitative field study in a video game company in a large city in Germany, identified areas of concern, and sketched out implications for gender-sensitive communication measures together with our participants. Findings show that addressing gender stereotypes, making role models visible, and using adequate channels is relevant. Some problems might be solved via short-term solutions, but the majority require a long-term perspective. Our lessons learned leave implications for companies in the IT sector who want to foster gender sensitive external communication measures and can contribute to the realization of more gender balanced working environments.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375196"}, {"primary_key": "2466499", "vector": [], "sparse_vector": [], "title": "Online Community-based Design of Free and Open Source Software for Transgender Voice Training.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper describes Project Spectra, a collective of open source developers that aims to build free and open source voice training technology for transgender people. We demonstrate how a design prioritizing the agency of trans users was made possible through sustained community collaboration. Using an autoethnographic approach, we discuss our community-based design process, which was documented with memos, online meetings and text conversations, sketches, and other data sources. We illustrate how we articulated our values as a group: deciding our programming framework (including a Statement of Principles), elaborating our \"Experience Goals\" (the feelings we wanted our design to elicit), and determining the features we wanted to implement in our app. We conclude with a reflection on the benefits and challenges of conducting community-based design research through an open-source organizational model.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434167"}, {"primary_key": "2466500", "vector": [], "sparse_vector": [], "title": "To Plan or Not to Plan? A Mixed-Methods Diary Study Examining When, How and Why Knowledge Work Planning is Inaccurate.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reliable and accurate planning is essential for modern knowledge workers. However, there is limited insight about when, how and why planning is inaccurate, and the circumstances in which those inaccuracies are troublesome. To investigate this, we asked 20 academics to keep a diary for a single work day. They estimated the duration of the tasks they wanted to achieve at the start of the day and noted down in detail the tasks they actually achieved during the day. Semi-structured interviews were conducted to complement this diary data. The diaries showed that some tasks, such as email and coding, were more susceptible to time underestimation bias while other tasks, such as writing and planning, were more susceptible to time overestimation bias in planning. Based on interviews, a typology of common reasons for delays in planned daily work is presented. It suggests that vague and optimistic planning leads to the observed discrepancy between planned and actual work. Finally, interviews suggested that participants adopted four planning strategies that vary in the frequency of planning, from minimal planning to daily, weekly and multi-level planning. We close by discussing ways support systems for accurate planning can be better designed for different use cases.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432921"}, {"primary_key": "2466501", "vector": [], "sparse_vector": [], "title": "Misinformation as a Window into Prejudice: COVID-19 and the Information Environment in India.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the aftermath of the COVID-19 crisis, there has been a massive amount of misinformation both related to the condition, and a range of linked social and economic issues. We present a mixed methods study of misinformation debunked by Indian fact checking agencies since January 2020. Alongside this, we present an analysis of what politicians in India have been discussing in the overlapping period. We find that affective issues dominate misinformation, especially in the period following the lockdown in India. Furthermore, we find that communal prejudice emerges as a central part of the misinformation environment, something that is reflected in the political speech around the same period.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432948"}, {"primary_key": "2466502", "vector": [], "sparse_vector": [], "title": "Saudi Arabian Parents&apos; Perception of Online Marital Matchmaking Technologies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Finding a date or a spouse online is usually considered an individualistic endeavor in Western cultures. This presents a challenge for collectivist non-Western cultures such as Saudi Arabia where choosing a spouse is viewed as a union of two families with parents of both spouses being heavily involved. Our work aims to investigate how Saudi Arabian parents view the utilization of technology by their young adults to seek potential spouses online. We report our findings of interviews conducted with 16 Saudi Arabian parents (8 fathers, 6 mothers and 1 couple). We generate qualitative themes that provide insights about how parents wanted to preserve their values, integrate technology into the traditional process and protect their young adults from potential harms. These themes lead to implications for designing suitable marital matchmaking technologies in Saudi Arabia and opportunities for future work.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432910"}, {"primary_key": "2466503", "vector": [], "sparse_vector": [], "title": "The Effects of Predictive Features of Mobile Keyboards on Text Entry Speed and Errors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile users rely on typing assistant mechanisms such as prediction and autocorrect. Previous studies on mobile keyboards showed decreased performance for heavy use of word prediction, which identifies a need for more research to better understand the effectiveness of predictive features for different users. Our work aims at such a better understanding of user interaction with autocorrections and the prediction panel while entering text, in particular when these approaches fail. We present a crowd-sourced mobile text entry study with 170 participants. Our mobile web application simulates autocorrection and word prediction to capture user behaviours around these features. We found that using word prediction saves an average of 3.43 characters per phrase but also adds an average of two seconds compared to actually typing the word, resulting in a negative effect on text entry speed. We also identified that the time to fix wrong autocorrections is on average 5.5 seconds but that autocorrection does not have a significant effect on typing speed.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427311"}, {"primary_key": "2466504", "vector": [], "sparse_vector": [], "title": "Middle-Aged Video Consumers&apos; Beliefs About Algorithmic Recommendations on YouTube.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vero Vanden Abeele", "<PERSON>", "<PERSON><PERSON>"], "summary": "User beliefs about algorithmic systems are constantly co-produced through user interaction and the complex socio-technical systems that generate recommendations. Identifying these beliefs is crucial because they influence how users interact with recommendation algorithms. With no prior work on user beliefs of algorithmic video recommendations, practitioners lack relevant knowledge to improve the user experience of such systems. To address this problem, we conducted semi-structured interviews with middle-aged YouTube video consumers to analyze their user beliefs about the video recommendation system. Our analysis revealed different factors that users believe influence their recommendations. Based on these factors, we identified four groups of user beliefs: Previous Actions, Social Media, Recommender System, and Company Policy. Additionally, we propose a framework to distinguish the four main actors that users believe influence their video recommendations: the current user, other users, the algorithm, and the organization. This framework provides a new lens to explore design suggestions based on the agency of these four actors. It also exposes a novel aspect previously unexplored: the effect of corporate decisions on the interaction with algorithmic recommendations. While we found that users are aware of the existence of the recommendation system on YouTube, we show that their understanding of this system is limited.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415192"}, {"primary_key": "2466505", "vector": [], "sparse_vector": [], "title": "CrowdMOT: Crowdsourcing Strategies for Tracking Multiple Objects in Videos.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Crowdsourcing is a valuable approach for tracking objects in videos in a more scalable manner than possible with domain experts. However, existing frameworks do not produce high quality results with non-expert crowdworkers, especially for scenarios where objects split. To address this shortcoming, we introduce a crowdsourcing platform called CrowdMOT, and investigate two micro-task design decisions: (1) whether to decompose the task so that each worker is in charge of annotating all objects in a sub-segment of the video versus annotating a single object across the entire video, and (2) whether to show annotations from previous workers to the next individuals working on the task. We conduct experiments on a diversity of videos which show both familiar objects (aka - people) and unfamiliar objects (aka - cells). Our results highlight strategies for efficiently collecting higher quality annotations than observed when using strategies employed by today's state-of-art crowdsourcing system.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434175"}, {"primary_key": "2466506", "vector": [], "sparse_vector": [], "title": "Understanding the Homepreneurship Opportunities Afforded by Social Networking and Personal Fabrication Technologies.", "authors": ["<PERSON>"], "summary": "The decreased cost and increased usability of personal fabrication technologies has enabled a new generation of crafters to integrate digital designs and computationally created artifacts into physically-based practices. With the simultaneous ubiquity of e-commerce and social networking channels, these technologies have enabled many crafters to transform their hobbies into home-based businesses. To understand the opportunities and challenges that fusing social networking platforms, personal fabrication equipment, and e-commerce have afforded these homepreneurs, an online survey and follow-up interviews were conducted with crafters who use hobbyist cutting plotters to personalize and sell goods online. The findings uncovered an emerging group of homepreneurs, i.e., mompreneurs, who use these technologies for supplemental income for their families and highlighted the emotional and opportunistic benefits that such personalized, at-home manufacturing affords. They also highlighted the workflows and lifestyle implications of using these technologies to run home-based businesses, the multi-faceted usage and dependence these homepreneurs have on online social platforms such as Facebook, the complex software toolchains that are used, and the commonplace practice of appropriating designs from others that occurs in this community.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415170"}, {"primary_key": "2466507", "vector": [], "sparse_vector": [], "title": "Watched, but Moving: Platformization of Beauty Work and Its Gendered Mechanisms of Control.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Women gig workers face unique challenges in on-demand platforms as gendered aspects of class, caste, and labor participation intersect with moments of control experienced on the job. Through in-depth interviews with 19 beauty workers on on-demand home service platforms, we explore how the platformization of informal beauty work in India has ruptured dominant socio-cultural structures of control that have traditionally shaped women's mobility and access to work. This paper maps the ways in which women beauty gig workers experience and are impacted by algorithmic and bureaucratic management practices prevalent in the gig economy, in the context of home service platforms in Bangalore. We find that platform control impacts lives in myriad ways, beyond the conditions of work. Women workers negotiate their identities and sense of agency through the visibility afforded by platform control mechanisms. Yet, despite these subversions, being on a platform does not fundamentally change the socio-cultural logic that restricts women's lives in India. These mechanisms work to entrench power asymmetries between customers and workers, as well as maintain them between the platform and the worker.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432949"}, {"primary_key": "2466509", "vector": [], "sparse_vector": [], "title": "Conducting Risky Research with Teens: Co-designing for the Ethical Treatment and Protection of Adolescents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The methods in which we study the online experiences of adolescents should be evidence-based and informed by youth. This is especially true when studying sensitive topics, such as the online risk behaviors of minors. We directly engaged 20 adolescents (ages 12-18) in the co-design of two different research methodologies (i.e., diary studies and analyzing social media trace data) for conducting adolescent online safety research. We also interviewed 13 of their parents to understand their perspectives. Overall, teens wanted to share their personal experiences and benefit society, while parents wanted researchers to tackle a topic that they felt was a prevalent problem for teens. Yet, they both had significant concerns regarding data privacy of the sensitive disclosures made by teens during such studies. Teens' feared getting in trouble. Participants emphasized the importance of developing a trusting relationship with the researcher to overcome these concerns. Participants also saw the potential for using the research study as a tool for risk-reporting and mitigation, where researchers could act as liaisons between the teens and other parties (e.g., counselors, law enforcement, parents) to share pertinent risk details and facilitate resources or even help teens directly by giving them strategies for mitigating online risks they encountered during the study. Our research delves into important ethical considerations for conducting risk-focused research with adolescents and uncovers the critical need for designing risk-based research for youth protection. We provide researchers with heuristic guidelines for conducting ethical research with vulnerable populations (i.e., adolescents) and keeping participants safe while doing so.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432930"}, {"primary_key": "2466510", "vector": [], "sparse_vector": [], "title": "To &quot;See&quot; is to Stereotype: Image Tagging Algorithms, Gender Recognition, and the Accuracy-Fairness Trade-off.", "authors": ["<PERSON><PERSON>", "Kyriakos Kyriakou", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine-learned computer vision algorithms for tagging images are increasingly used by developers and researchers, having become popularized as easy-to-use \"cognitive services.\" Yet these tools struggle with gender recognition, particularly when processing images of women, people of color and non-binary individuals. Socio-technical researchers have cited data bias as a key problem; training datasets often over-represent images of people and contexts that convey social stereotypes. The social psychology literature explains that people learn social stereotypes, in part, by observing others in particular roles and contexts, and can inadvertently learn to associate gender with scenes, occupations and activities. Thus, we study the extent to which image tagging algorithms mimic this phenomenon. We design a controlled experiment, to examine the interdependence between algorithmic recognition of context and the depicted person's gender. In the spirit of auditing to understand machine behaviors, we create a highly controlled dataset of people images, imposed on gender-stereotyped backgrounds. Our methodology is reproducible and our code publicly available. Evaluating five proprietary algorithms, we find that in three, gender inference is hindered when a background is introduced. Of the two that \"see\" both backgrounds and gender, it is the one whose output is most consistent with human stereotyping processes that is superior in recognizing gender. We discuss the accuracy--fairness trade-off, as well as the importance of auditing black boxes in better understanding this double-edged sword.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432931"}, {"primary_key": "2466511", "vector": [], "sparse_vector": [], "title": "Autogrip: Enabling Force Feedback Devices to Self-Attach to End-Users&apos; Fingers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Autogrip is a new thimble that enables force feedback devices to autonomously attach themselves to a finger. Although self-attachment is a simple concept, it has never been explored in the space of force feedback devices where current thimble solutions require complex attachment procedures and often swapping between interchangeable parts. Self-attachment is advantageous in many applications such as: immersive spaces, multi-user, walk up and use contexts, and especially multi-point force feedback systems as it can allow a lone user to quickly attach multiple devices to fingers on both hands - a difficult task with current thimbles. We present the design of our open-source contraption, Autogrip, a one-size-fits-all thimble that retro-fits to existing force feedback devices, enabling them to automatically attach themselves to a fingertip. We demonstrate Autogrip by retrofitting it to a Phantom 1.5 and a 4-finger Mantis system. We report preliminary user-testing results that indicated Autogrip was three times faster to attach than a typical method. We also present further refinements based on user feedback.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427312"}, {"primary_key": "2466512", "vector": [], "sparse_vector": [], "title": "Do Multilingual Users Prefer Chat-bots that Code-mix? Let&apos;s Nudge and Find Out!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kalika Bali", "<PERSON><PERSON><PERSON>"], "summary": "Despite their pervasiveness, current text-based conversational agents (chatbots) are predominantly monolingual, while users are often multilingual. It is well-known that multilingual users mix languages while interacting with others, as well as in their interactions with computer systems (such as query formulation in text-/voice-based search interfaces and digital assistants). Linguists refer to this phenomenon as code-mixing or code-switching. Do multilingual users also prefer chatbots that can respond in a code-mixed language over those which cannot? In order to inform the design of chatbots for multilingual users, we conduct a mixed-method user-study (N=91) where we examine how conversational agents, that code-mix and reciprocate the users' mixing choices over multiple conversation turns, are evaluated and perceived by bilingual users. We design a human-in-the-loop chatbot with two different code-mixing policies -- (a) always code-mix irrespective of user behavior, and (b) nudge with subtle code-mixed cues and reciprocate only if the user, in turn, code-mixes. These two are contrasted with a monolingual chatbot that never code-mixed. Users are asked to interact with the bots, and provide ratings on perceived naturalness and personal preference. They are also asked open-ended questions around what they (dis)liked about the bots. Analysis of the chat logs, users' ratings, and qualitative responses reveal that multilingual users strongly prefer chatbots that can code-mix. We find that self-reported language proficiency is the strongest predictor of user preferences. Compared to the Always code-mix policy, Nudging emerges as a low-risk low-gain policy which is equally acceptable to all users. Nudging as a policy is further supported by the observation that users who rate the code-mixing bot higher typically tend to reciprocate the language mixing pattern of the bot. These findings present a first step towards developing conversational systems that are more human-like and engaging by virtue of adapting to the users' linguistic style.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392846"}, {"primary_key": "2466513", "vector": [], "sparse_vector": [], "title": "&quot;So-called privacy breeds evil&quot;: Narrative Justifications for Intimate Partner Surveillance in Online Forums.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A growing body of research suggests that intimate partner abusers use digital technologies to surveil their partners, including by installing spyware apps, compromising devices and online accounts, and employing social engineering tactics. However, to date, this form of privacy violation, called intimate partner surveillance (IPS), has primarily been studied from the perspective of victim-survivors. We present a qualitative study of how potential perpetrators of IPS harness the emotive power of sharing personal narratives to validate and legitimise their abusive behaviours. We analysed 556 stories of IPS posted on publicly accessible online forums dedicated to the discussion of sexual infidelity. We found that many users share narrative posts describing IPS as they boast about their actions, advise others on how to perform IPS without detection, and seek suggestions for next steps to take. We identify a set of common thematic story structures, justifications for abuse, and outcomes within the stories that provide a window into how these individuals believe their behaviour to be justified. Using these stories, we develop a four-stage framework that captures the change in a potential perpetrator's approach to IPS. We use our findings and framework to guide a discussion of efforts to combat abuse, including how we can identify crucial moments where interventions might be safely applied to prevent or deescalate IPS.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432909"}, {"primary_key": "2466514", "vector": [], "sparse_vector": [], "title": "LanguageLogger: A Mobile Keyboard Application for Studying Language Use in Everyday Text Communication in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a concept and tool for studying language use in everyday mobile text communication (e.g. chats). Our approach for the first time enables researchers to collect comprehensive data on language use during unconstrained natural typing (i.e. no study tasks) without logging readable messages to preserve privacy. We achieve this with a combination of three customisable text abstraction methods that run directly on participants' phones. We report on our implementation as an Android keyboard app and two evaluations: First, we simulate text reconstruction attempts on a large text corpus to inform conditions for minimising privacy risks. Second, we assess people's experiences in a two-week field deployment (N=20). We release our app as an open source project to the community to facilitate research on open questions in HCI, Linguistics and Psychology. We conclude with concrete ideas for future studies in these areas.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397872"}, {"primary_key": "2466515", "vector": [], "sparse_vector": [], "title": "Chatbot-based Emotion Management for Distributed Teams: A Participatory Design Study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fueled by the pervasion of tools like Slack or Microsoft Teams, the usage of text-based communication in distributed teams has grown massively in organizations. This brings distributed teams many advantages, however, a critical shortcoming in these setups is the decreased ability of perceiving, understanding and regulating emotions. This is problematic because better team members? abilities of emotion management positively impact team-level outcomes like team cohesion and team performance, while poor abilities diminish communication flow and well-being. Leveraging chatbot technology in distributed teams has been recognized as a promising approach to reintroduce and improve upon these abilities. In this article we present three chatbot designs for emotion management for distributed teams. In order to develop these designs, we conducted three participatory design workshops which resulted in 153 sketches. Subsequently, we evaluated the designs following an exploratory evaluation with 27 participants. Results show general stimulating effects on emotion awareness and communication efficiency. Further, they report emotion regulation and increased compromise facilitation through social and interactive design features, but also perceived threats like loss of control. With some design features adversely impacting emotion management, we highlight design implications and discuss chatbot design recommendations for enhancing emotion management in teams.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415189"}, {"primary_key": "2466516", "vector": [], "sparse_vector": [], "title": "Sociocultural Dimensions of Tracking Health and Taking Care.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The field of personal health informatics has received increasing attention within the CSCW and HCI communities as health tracking becomes more affordable, accessible, and pervasive. Chronic disease management, in particular, presents tremendous potential for intervention given patients' ability to now actively participate in their care through tracking. The focus on 'personal' in health informatics, however, obfuscates the role of other cultural and ecological factors that might shape health tracking behaviors, and important information from alternative sources could be ignored by virtue of being subjective, complex, or simply hard to collect. To dig deeper into these negative spaces that may go untracked, uncover potential sources of important health information, and more completely understand current tracking practices, we embarked on an interview study with patients with cardiac diseases in Bangalore, India. In this paper, we present these patients' current health management approaches that are culturally situated, identifying both motivations and barriers to tracking, their attitudes towards online information, as well as cultural and ecological influences on their perceptions of cardiac care. We then discuss the interplay between our findings and current notions of, and approaches towards, patient empowerment and datafication of health.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415200"}, {"primary_key": "2466517", "vector": [], "sparse_vector": [], "title": "Exploring the Design Space of Badge Based Input.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we explore input with wearables that can be attached and detached at will from any of our regular clothes. These wearables do not cause any permanent effect on our clothing and are suitable to be worn anywhere, thus making them very similar to badges we wear. To explore this idea of non-permanent badge input, we studied various methods to fasten objects to our clothing and organise them in the form of a design space. We leverage this synthesis, along with literature and existing products to present possible interaction gestures these badge-based wearables can enable.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427313"}, {"primary_key": "2466518", "vector": [], "sparse_vector": [], "title": "Investigating Differences in Crowdsourced News Credibility Assessment: Raters, Tasks, and Expert Criteria.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Misinformation about critical issues such as climate change and vaccine safety is oftentimes amplified on online social and search platforms. The crowdsourcing of content credibility assessment by laypeople has been proposed as one strategy to combat misinformation by attempting to replicate the assessments of experts at scale. In this work, we investigate news credibility assessments by crowds versus experts to understand when and how ratings between them differ. We gather a dataset of over 4,000 credibility assessments taken from 2 crowd groups---journalism students and Upwork workers---as well as 2 expert groups---journalists and scientists---on a varied set of 50 news articles related to climate science, a topic with widespread disconnect between public opinion and expert consensus. Examining the ratings, we find differences in performance due to the makeup of the crowd, such as rater demographics and political leaning, as well as the scope of the tasks that the crowd is assigned to rate, such as the genre of the article and partisanship of the publication. Finally, we find differences between expert assessments due to differing expert criteria that journalism versus science experts use---differences that may contribute to crowd discrepancies, but that also suggest a way to reduce the gap by designing crowd tasks tailored to specific expert criteria. From these findings, we outline future research directions to better design crowd processes that are tailored to specific crowds and types of content.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415164"}, {"primary_key": "2466519", "vector": [], "sparse_vector": [], "title": "Supporting Youth Activists? Strategic Use of Social Media: A Qualitative Investigation of Design Opportunities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Catherine D&apos;<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Social Networking Sites (SNS) provide a platform for engaging youth in activism (e.g., by helping mobilize civic action). While youth typically employ casual approaches to online activism (i.e., quick actions, such as broadcast posts to advertise social justice events), more strategic practices (i.e., those that are more creative and informed) can increase the likelihood of successful online campaigns. However, little work has examined how youth activists can be supported to use SNS more strategically. To address this research gap, we conducted interviews with youth activists, exploring how youth made sense of social network visualizations and their perspectives on how such tools could support their activism efforts. Our findings characterize how participants made inferences about followers? identities based on their hashtag use, and how they used those inferences in outreach decisions. We conclude with design implications for future research in this area.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415180"}, {"primary_key": "2466520", "vector": [], "sparse_vector": [], "title": "A Dramaturgical Approach to Online Activism within Youth Empowerment Organizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social Networking Sites (SNS) offer youth activists and youth empowerment organizations (where adults help youth address community issues) opportunities for civic action. Impression management is critical to youth empowerment organizations' work online, as they attempt to influence the opinions of their audience. However, there is a dearth of research characterizing online impression management in the context of youth empowerment organizations. To address this research gap, we conducted a qualitative study investigating the use of SNS in a youth empowerment organization. Using <PERSON><PERSON><PERSON>'s dramaturgical model, we characterized how youth tried to hack SNS algorithms, and their desire to better identify their audience. Our findings reveal how youth use SNS to create authentic images and connections with their audience. On the other hand, we discuss adults' desire to convey a curated organizational image and challenges that arose. We conclude with design implications for tools that support impression management online for youth activists.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415193"}, {"primary_key": "2466521", "vector": [], "sparse_vector": [], "title": "Making it Work, or Not: A Longitudinal Study of Career Trajectories Among Online Freelancers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Online freelancing is growing rapidly. However, despite this rapid growth, we have a limited understanding of online freelancers' long-term experiences and evolution, or how online freelancing influences freelancers' broader career goals. To address this gap, we interviewed a set of online freelancers at two time periods, two and a half years apart. We found that long-term engagement with online freelancing involves a unique set of financial, emotional, relational, and reputational burdens that represent the overhead of maintaining an online freelancing career. We found that this overhead influenced online freelancers' participation and perceptions of online freelancing over time, as well as the strategies some freelancers employed to manage their careers. Our findings further highlight how online freelance platforms can afford unique career development opportunities over a longer period of time, including career exploration and transition, entrepreneurial training and reputation, and skills transfer. Based on our findings, we present policy and design implications to increase the sustainability and accessibility of online freelancing.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432925"}, {"primary_key": "2466522", "vector": [], "sparse_vector": [], "title": "&quot;I can&apos;t get round&quot;: Recruiting Assistance in Mobile Robotic Telepresence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Via audiovisual communications and a controllable physical embodiment, Mobile Robotic telePresence (MRP) systems aim to support enhanced collaboration between remote and local members of a given setting. But MRP systems also put the remote user in positions where they frequently rely on the help of local partners. Getting or 'recruiting' such help can be done with various verbal and embodied actions ranging in explicitness. In this paper, we look at how such recruitment occurs in video data drawn from an experiment where pairs of participants (one local, one remote) performed a timed searching task. We find a prevalence of implicit recruitment methods and outline obstacles to effective recruitment that emerge due to communicative asymmetries that are built into MRP design. In a future where remote work becomes widespread, assistance through remote work technology like MRPs needs close examination at a fundamental interactional level, taking into account how communicative asymmetries are at play in everyday use of such technologies.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432947"}, {"primary_key": "2466524", "vector": [], "sparse_vector": [], "title": "Flexible Automatic Support for Web Accessibility Validation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatic support for web accessibility validation needs to evolve for several reasons. The increasingly recognised importance of accessibility implies that various stakeholders, with different expertise, look at it from different viewpoints and have different requirements regarding the types of outputs they expect. The technologies used to support Web application access are evolving along with the associated accessibility guidelines. We present a novel tool that aims to provide flexible and open support for addressing such issues. We describe the design of its main features, including support for recent guidelines and tailored results presentations, and report on first technical and empirical validations that have provided positive feedback.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397871"}, {"primary_key": "2466525", "vector": [], "sparse_vector": [], "title": "Origins of Algorithmic Instabilities in Crowdsourced Ranking.", "authors": ["<PERSON>", "<PERSON>", "Raissa M. D&apos;Souza", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Crowdsourcing systems aggregate decisions of many people to help users quickly identify high-quality options, such as the best answers to questions or interesting news stories. A long-standing issue in crowdsourcing is how option quality and human judgement heuristics interact to affect collective outcomes, such as the perceived popularity of options. We address this limitation by conducting a controlled experiment where subjects choose between two ranked options whose quality can be independently varied. We use this data to construct a model that quantifies how judgement heuristics and option quality combine when deciding between two options. The model reveals popularity-ranking can be unstable: unless the quality difference between the two options is sufficiently high, the higher quality option is not guaranteed to be eventually ranked on top. To rectify this instability, we create an algorithm that accounts for judgement heuristics to infer the best option and rank it first. This algorithm is guaranteed to be optimal if data matches the model. When the data does not match the model, however, simulations show that in practice this algorithm performs better or at least as well as popularity-based and recency-based ranking for any two-choice question. Our work suggests that algorithms relying on inference of mathematical models of user behavior can substantially improve outcomes in crowdsourcing systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415237"}, {"primary_key": "2466526", "vector": [], "sparse_vector": [], "title": "Trust, Identity, Privacy, and Security Considerations for Designing a Peer Data Sharing Platform Between People Living With HIV.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Resulting from treatment advances, the Human Immunodeficiency Virus (HIV) is now a long-term condition, and digital solutions are being developed to support people living with HIV in self-management. Sharing their health data with their peers may support self-management, but the trust, identity, privacy and security (TIPS) considerations of people living with HIV remain underexplored. Working with a peer researcher who is expert in the lived experience of HIV, we interviewed 26 people living with HIV in the United Kingdom (UK) to investigate how to design a peer data sharing platform. We also conducted rating activities with participants to capture their attitudes towards sharing personal data. Our mixed methods study showed that participants were highly sophisticated in their understanding of trust and in their requirements for robust privacy and security. They indicated willingness to share digital identity attributes, including gender, age, medical history, health and well-being data, but not details that could reveal their personal identity. Participants called for TIPS measures to foster and to sustain responsible data sharing within their community. These findings can inform the development of trustworthy and secure digital platforms that enable people living with HIV to share data with their peers and provide insights for researchers who wish to facilitate data sharing in other communities with stigmatised health conditions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415244"}, {"primary_key": "2466528", "vector": [], "sparse_vector": [], "title": "Eco-InfoVis at Work: Role-based Eco-Visualizations for the Industrial Context.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Currently, there is a broad range of studies dealing with the design and visualization of energy consumption data for the domestic and increasingly for the office context. However, studies addressing the industrial context are quite rare, and due to the diversity of machines, processes, tasks, personal motivations, teams and the specific organizational culture of companies, it is not sufficient to provide only consumption data. For an adequate consideration of these factors, detailed design guidelines and system concepts are currently missing. However, this study shows the potential that a common understanding of consumption data can emerge through suitable visualization to support everyday work and possibilities of data sharing. Therefore, we show exemplarily how a design can be derived from empirically collected requirements and how a system concept can look like that enrich current eco-feedback design research for the industrial context.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375182"}, {"primary_key": "2466529", "vector": [], "sparse_vector": [], "title": "Implicit Diversity in Image Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Studies have shown that the people depicted in image search results tend to be of majority groups with respect to socially salient attributes such as gender or race. This skew goes beyond that which already exists in the world - i.e., the search results for images of people are more imbalanced than the ground truth would suggest. For example, <PERSON> et al. showed that although 28% of CEOs in the U.S. are women, only 10% of the top 100 results for \"CEO\" in Google Image Search are women. Similar observations abound across search terms and across socially salient attributes. Most existing approaches to correct for this kind of bias assume that the images of people include labels denoting the relevant socially salient attributes. These labels are explicitly used to either change the dataset, adjust the training of the algorithm, and/or in the execution of the method. However, such labels are often unknown. Further, using machine learning techniques to infer these labels may often not be possible within acceptable accuracy ranges and may not be desirable due to the additional biases this process could incur. As observed in prior work, alternate approaches consider the diversity of image features, which often do not translate to images of visibly diverse people. We develop a novel approach that takes as input a visibly diverse control set of images of people and uses this set as part of a procedure to select a set of images of people in response to a query. The goal is to have a resulting set that is more visibly diverse in a manner that emulates the diversity depicted in the control set. It accomplishes this by evaluating the similarity of the images selected by a black-box algorithm with the images in the diversity control set, and incorporating this \"diversity score\" into the final selection process. Importantly, this approach does not require images to be labelled at any point; effectively, it gives a way to implicitly diversify the set of images selected. We provide two variants of our approach: the first is a modification of the well known MMR algorithm to incorporate the diversity scores, and the second is a more efficient variant that does not consider within-list redundancy. We evaluate these approaches empirically on two image datasets: 1) a new dataset we collect which contains the top 100 Google Image results for 96 occupations, for which we evaluate gender and skin-tone diversity with respect to occupations and 2) the well-known CelebA dataset containing images of celebrities for which we can evaluate gender diversity with respect to facial features such as \"smiling\" or \"glasses\". Both of our approaches produce image sets that significantly improve the visible diversity of the results (i.e., include a larger fraction of anti-stereotypical images) with respect to current Google Image Search results and other state-of-the-art algorithms for diverse image summarization. Further, they seem to come at a minimal cost to accuracy. These empirical results demonstrate the effectiveness of simple label-independent interventions to diversify image search.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415210"}, {"primary_key": "2466530", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> and the Impaired Cyborg: Assistive Technologies, Accessibility, and Access.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper examines software piracy in the Global South from an accessibility lens, using the bio-technical metaphor of the 'cyborg.' Drawing on qualitative interviews with people with visual impairment (VI) from India and Peru, the paper interrogates the intimate relationships that users have with assistive technologies (ATs). It outlines the effectiveness of ATs in allowing users to actively control and shape their own lives and identities, and describes the various modalities that regulate the human body, technology, and human body-technology linkages. The paper argues that software piracy, when looked through the lens of the 'cyborg,' is an act of self-making that is motivated by a desire to gain autonomy and independence, i.e., it can be understood as a way to overcome the barriers that undermine access to the technological self. Further, software piracy allows a shift in the distribution of power from those who control and regulate the assistive technologies to the cyborgs themselves.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432941"}, {"primary_key": "2466531", "vector": [], "sparse_vector": [], "title": "Trustworthiness Perceptions of Social Media Resources Named after a Crisis Event.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "People often create social media accounts and pages named after crisis events. We call such accounts and pages Crisis Named Resources (CNRs). CNRs share information about crisis events and are followed by many. Yet, they also appear suddenly (at crisis onset) and in most cases, the owners are unknown. Thus, it can be challenging for audiences in particular to know whether to trust (or not trust) these CNRs and the information they provide. In this study, we conducted surveys and interviews with members of the public and experts in crisis informatics, emergency response, and communication studies to evaluate the trustworthiness of CNRs named after the 2017 Hurricane Irma. Findings showed that participants evaluated trustworthiness based on their perceptions of a CNR's content, information source, profile, and owner. Findings also show that if people perceive that a CNR owner has prior experience in crisis response, can help the public to respond to the event, understands the situation, has the best interests of affected individuals in mind, or will correct misinformation, they tend to trust that CNR. Participant demographics and expertise showed no effect on perceptions of trustworthiness.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392849"}, {"primary_key": "2466532", "vector": [], "sparse_vector": [], "title": "&quot;I was afraid, but now I enjoy being a streamer!&quot;: Understanding the Challenges and Prospects of Using Live Streaming for Online Education.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The outbreak of COVID-19 has led to a sharp transition from offline to online education in many countries and areas. This transition heightens the intensity of existing challenges of online education, such as student attendance and education equality. During this time of uncertainty, the vast disparities in teachers? online experience and technical backgrounds, students' education level and their families' economic status, and schools' support, further pose new challenges to teachers and students. In this work, we study how Chinese teachers and students addressed challenges during this transition. We interviewed 15 teachers and 18 students from diverse backgrounds at varying education levels (K-12 and college). Our work makes timely and new contributions to the literature of online education. For example, our results showed that teachers applied Live Video Streaming (LVS) on multiple social media platforms and re-purposed different entertainment features to deliver online teaching for better student engagement; some teachers came to enjoy this new form of instruction after being resistant to it in the beginning, and students developed a better sense of intimacy with their teachers after experiencing certain online interactions. Our work also reveals the remaining challenges and prospects of LVS-based online education and sheds light on the future design of collaborative technologies for online education.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432936"}, {"primary_key": "2466533", "vector": [], "sparse_vector": [], "title": "Understanding the Role of Intermediaries in Online Social E-commerce: An Exploratory Study of Beidian.", "authors": ["<PERSON><PERSON><PERSON>", "Hancheng Cao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social e-commerce, as a new form of social computing based marketing platforms, utilizes existing real-world social relationships for promotions and sales of products. It has been growing rapidly in recent years and attracted tens of millions of users in China. A key group of actors who enable market transactions on these platforms are intermediaries who connect producers with consumers by sharing information with and recommending products to their real-world social contacts. Despite their crucial role, the nature and behavior of these intermediaries on these social e-commerce platforms has not been systematically analyzed. Here we address this knowledge gap through a mixed method study. Leveraging 9 months' all-round behavior of about 40 million users on Beidian -- one of the largest social e-commerce sites in China, alongside with qualitative evidence from online forums and interviews, we examine characteristics of intermediaries, identify their behavioral patterns and uncover strategies and mechanisms that make successful intermediaries. We demonstrate that intermediaries on social e-commerce sites act as local trend detectors and \"social grocers''. Furthermore, successful intermediaries are highly dedicated whenever best sellers appear and broaden items for promotion. To the best of our knowledge, this paper presents the first large-scale analysis on the emerging role of intermediaries in social e-commerce platforms, which provides potential insights for the design and management of social computing marketing platforms.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415185"}, {"primary_key": "2466534", "vector": [], "sparse_vector": [], "title": "From Lost to Found: Discover Missing UI Design Semantics through Recovering Missing Tags.", "authors": ["Chunyang Chen", "<PERSON><PERSON> Feng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sheng<PERSON> Zhao"], "summary": "Design sharing sites provide UI designers with a platform to share their works and also an opportunity to get inspiration from others' designs. To facilitate management and search of millions of UI design images, many design sharing sites adopt collaborative tagging systems by distributing the work of categorization to the community. However, designers often do not know how to properly tag one design image with compact textual description, resulting in unclear, incomplete, and inconsistent tags for uploaded examples which impede retrieval, according to our empirical study and interview with four professional designers. Based on a deep neural network, we introduce a novel approach for encoding both the visual and textual information to recover the missing tags for existing UI examples so that they can be more easily found by text queries. We achieve 82.72% accuracy in the tag prediction. Through a simulation test of 5 queries, our system on average returns hundreds more results than the default Dribbble search, leading to better relatedness, diversity and satisfaction.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415194"}, {"primary_key": "2466535", "vector": [], "sparse_vector": [], "title": "Towards Supporting Programming Education at Scale via Live Streaming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON> Dong"], "summary": "Live streaming, which allows streamers to broadcast their work to live viewers, is an emerging practice for teaching and learning computer programming. Participation in live streaming is growing rapidly, despite several apparent challenges, such as a general lack of training in pedagogy among streamers and scarce signals about a stream's characteristics (e.g., difficulty, style, and usefulness) to help viewers decide what to watch. To understand why people choose to participate in live streaming for teaching or learning programming, and how they cope with both apparent and non-obvious challenges, we interviewed 14 streamers and viewers about their experience with live streaming programming. Among other results, we found that the casual and impromptu nature of live streaming makes it easier to prepare than pre-recorded videos, and viewers have the opportunity to shape the content and learning experience via real-time communication with both the streamer and each other. Nonetheless, we identified several challenges that limit the potential of live streaming as a learning medium. For example, streamers voiced privacy and harassment concerns, and existing streaming platforms do not adequately support viewer-streamer interactions, adaptive learning, and discovery and selection of streaming content. Based on these findings, we suggest specialized tools to facilitate knowledge sharing among people teaching and learning computer programming online, and we offer design recommendations that promote a healthy, safe, and engaging learning environment.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434168"}, {"primary_key": "2466537", "vector": [], "sparse_vector": [], "title": "Country Differences in Social Comparison on Social Media.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Social comparison is a common focus in discussions of online social media use, and differences in its frequency, causes, and outcomes may arise from country or cultural differences. To understand how these differences play a role in experiences of social comparison on Facebook, a survey of 37,729 people across 18 countries was paired with respondents' activity on Facebook. The findings were augmented with 39 in-person interviews in three countries. Social comparison frequency was more strongly predicted by country than by age, gender, and Facebook activity combined, indicating that country differences are important to consider when studying social comparison. Women's and men's experiences differed greatly between countries. Exposure to high feedback counts on friends' posts was associated with more frequent social comparison, but only in some countries. Design interventions that account for such country differences may be more effective at reducing the negative outcomes of social comparison.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434179"}, {"primary_key": "2466538", "vector": [], "sparse_vector": [], "title": "Building Community Knowledge In Online Competitions: Motivation, Practices and Challenges.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge building is a prevalent feature in open online systems, but it is challenging to motivate participants to contribute and to maintain quality in the participants' contributions. Open online competitions, where participants compete for prizes with knowledge artifacts, offer a potential design model for online systems to incentivize community knowledge building activities. However, while there is evidence that participants contribute to public knowledge and share it during competitions, it remains unclear how and why they do so. In this study, through interviews with 14 participants in Kaggle Competitions, we investigate participants' motivation, practices, and challenges when contributing to community knowledge under a competitive structure. We find that competitive mechanisms impact expert and beginner participants very differently in their public knowledge building behaviors. Experts contribute to shared knowledge in order to compete for reputation, while they tend to form their own niches and only share knowledge artifacts that are abstract and not usable by less experienced participants. Beginners are often driven away from contributing to shared knowledge because of their vulnerable social image. We leverage Scardamalia's framework for Knowledge Building Communities to discuss the different challenges and opportunities that competitive design brings to expert and beginner participants. We offer design implications for effectively implementing competitive mechanisms that could benefit both expert and beginner participants in future knowledge building systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415250"}, {"primary_key": "2466539", "vector": [], "sparse_vector": [], "title": "Critique Me: Exploring How Creators Publicly Request Feedback in an Online Critique Community.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>w Liu", "<PERSON>"], "summary": "Creative workers frequently turn to online critique communities for feedback on their work. While past research has focused primarily on how to yield better feedback from providers, less is known about the strategies feedback seekers use to engage providers and request feedback. We present two studies to explore the feedback exchange dynamics between feedback requesters and providers in the subreddit community, r/design\\_critiques. In Study 1, we interviewed 12 community members and found that while creators have strategies to request feedback, they expressed uncertainty about whether and how to include details about the design context, personal background, and specific feedback needs. In Study 2, through a mixed-method analysis, we identified how specific request strategies impact the quantity and quality of community feedback, and found several key, but undervalued strategies: signaling as a novice, critiquing one's own design, and providing design variants. These strategies led to better community response, but were rarely used. We offer design implications around how to leverage these insights to improve online feedback exchange", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415232"}, {"primary_key": "2466540", "vector": [], "sparse_vector": [], "title": "PACM HCI V4 ISS, November 2020 - Editorial.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "It is our great pleasure to welcome you to this issue of the Proceedings of the ACM on Human Computer Interaction, the first to focus on the contributions from the research community Interactive Surfaces and Spaces (ISS). Interactive Surfaces and Spaces increasingly pervade our everyday life, appearing in various sizes, shapes, and application contexts, offering a rich variety of ways to interact. This diverse research community explores the design, development and use of new and emerging tabletop, digital surface, interactive spaces and multi-surface technologies. The call for articles for this issue on ISS attracted 87 submissions, from all over the world. After the first round of reviewing, 26 (29.9%) articles with minor revisions were invited to the Revise and Resubmit phase, and 39 (44.8%) articles with major revisions for the next full PACMHCI ISS review cycle in 2021 (total of 65 articles, 74.7%). The editorial committee worked hard over the two iterations of the review process to arrive at final decisions. In the end, 25 articles (28%) were accepted. All authors of the accepted articles are invited to present at the ISS conference from November 8-11, 2020. This issue exists because of the dedicated volunteer effort of 31 senior editors who served as Associate Chairs (ACs), and 146 expert reviewers to ensure high quality and insightful reviews for all articles in both rounds. Reviewers and committee members were kept constant for papers that submitted to both rounds.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427310"}, {"primary_key": "2466541", "vector": [], "sparse_vector": [], "title": "I Share, You Care: Private Status Sharing and Sender-Controlled Notifications in Mobile Instant Messaging.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jin<PERSON>ung Oh", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While mobile instant messaging (MIM) facilitates ubiquitous interpersonal communication, its constant connectivity could build the expectation of an immediate response to messages, and its notifications flood could cause interruptions at inopportune moments. We examine two design concepts for MIM-private status sharing and sender-controlled notifications-that aim to lower the pressure for an immediate reply and reduce unnecessary interruptions by untimely notifications. Private status sharing reactively reveals a customized status with a selected partner(s) only when the partner has sent a message. Sender-controlled notifications give senders the control of choosing whether to send a notification for their own messages. We built MyButler, an Android app prototype that instantiates these two concepts and integrated it with KakaoTalk, a commercial MIM app. During a two-week field study with 11 pairs (5 couples and 6 friend pairs), participants expressed themselves through a total of 210 different statuses, 64.3% of which indicated the current activity or task of the user. Participants reported that private status sharing enabled them to explain their unavailability and relieved the pressure and expectations for timely attendance. We reveal more findings on the types of privately shared statuses and their roles in MIM communication; the in-situ behaviors and patterns of using sender-controlled notifications; and the motivations of MIM users in choosing whether to alert their messages. In terms of message notifications, senders chose to send 25.4% of the messages without any notification. We found that senders' decisions to alert are affected by the receiver's status, their own status to chat, and the possibility of message content exposure to others through notifications. Based on our findings, we draw insights into how the concepts of private status sharing and sender-controlled notifications can be applied in future designs and explorations.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392839"}, {"primary_key": "2466542", "vector": [], "sparse_vector": [], "title": "The Role of Conversational Grounding in Supporting Symbiosis Between People and Digital Assistants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In \"smart speaker'' digital assistant systems such as Google Home, there is no visual user interface, so people must learn about the system's capabilities and limitations by experimenting with different questions and commands. However, many new users give up quickly and limit their use to a few simple tasks. This is a problem for both the user and the system. Users who stop trying out new things cannot learn about new features and functionality, and the system receives less data upon which to base future improvements. Symbiosis---a mutually beneficial relationship---between AI systems like digital assistants and people is an important aspect of developing systems that are partners to humans and not just tools. In order to better understand requirements for symbiosis, we investigated the relationship between the types of digital assistant responses and users' subsequent questions, focusing on identifying interactions that were discouraging to users when speaking with a digital assistant. We conducted a user study with 20 participants who completed a series of information seeking tasks using the Google Home, and analyzed transcripts using a method based on applied conversation analysis. We found that the most common response from the Google Home, a version of \"Sorry, I'm not sure how to help'', provided no feedback for participants to build on when forming their next question. However, responses that provided somewhat strange but tangentially related answers were actually more helpful for conversational grounding, which extended the interaction. We discuss the connection between grounding and symbiosis, and present recommendations for requirements for forming partnerships with digital assistants.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392838"}, {"primary_key": "2466543", "vector": [], "sparse_vector": [], "title": "ProtoChat: Supporting the Conversation Design Process with Crowd Feedback.", "authors": ["<PERSON><PERSON><PERSON>", "Toni-<PERSON>", "Jeongeon Park", "<PERSON><PERSON><PERSON> Shin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Similar to a design process for designing graphical user interfaces, conversation designers often apply an iterative design process by defining a conversation flow, testing with users, reviewing user data, and improving the design. While it is possible to iterate on conversation design with existing chatbot prototyping tools, there still remain challenges in recruiting participants on-demand and collecting structured feedback on specific conversational components. These limitations hinder designers from running rapid iterations and making informed design decisions. We posit that involving a crowd in the conversation design process can address these challenges, and introduce ProtoChat, a crowd-powered chatbot design tool built to support the iterative process of conversation design. ProtoChat makes it easy to recruit crowd workers to test the current conversation within the design tool. ProtoChat's crowd-testing tool allows crowd workers to provide concrete and practical feedback and suggest improvements on specific parts of the conversation. With the data collected from crowd-testing, ProtoChat provides multiple types of visualizations to help designers analyze and revise their design. Through a three-day study with eight designers, we found that ProtoChat enabled an iterative design process for designing a chatbot. Designers improved their design by not only modifying the conversation design itself, but also adjusting the persona and getting UI design implications beyond the conversation design itself. The crowd responses were helpful for designers to explore user needs, contexts, and diverse response formats. With ProtoChat, designers can successfully collect concrete evidence from the crowd and make decisions to iteratively improve their conversation design.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432924"}, {"primary_key": "2466544", "vector": [], "sparse_vector": [], "title": "Are You One of Us?: Current Hiring Practices Suggest the Potential for Class Biases in Large Tech Companies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "To better understand how we can broaden participation in computing, this exploratory study examines the interview process for elite internships at established technology companies. Through conducting 36 interviews with evaluators at technology companies, we find that in addition to technical competence, evaluators often assess internship applicants based on explicit and implicit signals of industrial fit, organizational fit, and individual fit. These evaluative criteria are reminiscent of prior literature linking biases in hiring to social class background. By reflecting on how our findings relate to previous studies, we suggest that evaluators? assessments of fit are potentially linked to social class background, and this might be an invisible factor contributing to hiring biases at technology companies. Given that hiring only culturally similar employees can have negative individual, organizational, and societal consequences, we propose strategies for evaluators to broaden their evaluation perspectives and to enact inclusive interviewing practices. We conclude with a call for further research on the role of social class background in the hiring process.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415214"}, {"primary_key": "2466545", "vector": [], "sparse_vector": [], "title": "&quot;The Personal is Political&quot;: Hackathons as Feminist Consciousness Raising.", "authors": ["Catherine D&apos;<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Initially conceived as problem-focused programming events, hackathons have expanded to encompass a range of issue areas, stakeholders and activities. There have been important critiques of hackathons in relation to their format and structure, their epistemological assumptions, and their outputs and impacts. Scholars working in Feminist HCI have proposed design considerations for more inclusive hackathons that focus on social justice outcomes for marginalized groups. Evaluative work on hackathons has assessed entrepreneurial contributions, skill development, and affective impacts, but largely absent from the analysis is a view of long-term personal impacts on participants. What kinds of lasting impacts (if any) do issue-focused hackathons have on participants themselves? In this paper, we describe a post-hoc qualitative study with participants and organizers of a postpartum health hackathon in the U.S., one year after the event took place. Our goals were to understand people's motivations for participating, what impact (if any) their participation had on their lives, and how (if at all) their participation shaped how they now understand postpartum health. Our findings indicate that the hackathon functioned as a space of \"feminist consciousness raising\" in that it provided space for navigating and sharing personal experiences, contextualizing and connecting those experiences to structural oppression, and developing participants' self- and collective-efficacy to create design interventions and enact social change. Feminist consciousness raising is not just \"awareness-raising\", but rather a specific historic and contemporary practice which we describe and situate in relation to personal experiences of oppression around stigmatized topics. With these findings, we situate feminist consciousness raising in relation to the literature on hackathons and feminist HCI, speculate which aspects of the design of the event led to it fostering feminist consciousness raising, and generate recommendations for how to intentionally bring feminist consciousness raising to the design of hackathons and innovation events.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415221"}, {"primary_key": "2466546", "vector": [], "sparse_vector": [], "title": "DG3: Exploiting Gesture Declarative Models for Sample Generation and Online Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we introduce DG3, an end-to-end method for exploiting gesture interaction in user interfaces. The method allows to declaratively model stroke gestures and their sub-parts, generating the training samples for the recognition algorithm. In addition, we extend the algorithms of the $-family for supporting the online (i.e., real-time ) stroke recognition and their parts, as declared in the models. Finally, we show that the method outperforms existing approaches for online recognition and has comparable accuracy with offline methods after a few gesture segments.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397870"}, {"primary_key": "2466547", "vector": [], "sparse_vector": [], "title": "Towards Domain-independent Complex and Fine-grained Gesture Recognition with RFID.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gesture recognition plays a fundamental role in emerging Human-Computer Interaction (HCI) paradigms. Recent advances in wireless sensing show promise for device-free and pervasive gesture recognition. Among them, RFID has gained much attention given its low-cost, light-weight and pervasiveness, but pioneer studies on RFID sensing still suffer two major problems when it comes to gesture recognition. The first is they are only evaluated on simple whole-body activities, rather than complex and fine-grained hand gestures. The second is they can not effectively work without retraining in new domains, i.e. new users or environments. To tackle these problems, in this paper, we propose RFree-GR, a domain-independent RFID system for complex and fine-grained gesture recognition. First of all, we exploit signals from the multi-tag array to profile the sophisticated spatio-temporal changes of hand gestures. Then, we elaborate a Multimodal Convolutional Neural Network (MCNN) to aggregate information across signals and abstract complex spatio-temporal patterns. Furthermore, we introduce an adversarial model to our deep learning architecture to remove domain-specific information while retaining information relevant to gesture recognition. We extensively evaluate RFree-GR on 16 commonly used American Sign Language (ASL) words. The average accuracy for new users and environments (new setup and new position) are $89.03%$, $90.21%$ and $88.38%$, respectively, significantly outperforming existing RFID based solutions, which demonstrates the superior effectiveness and generalizability of RFree-GR.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427315"}, {"primary_key": "2466548", "vector": [], "sparse_vector": [], "title": "Advisory Service Support that Works: Enhancing Service Quality with a Mixed-reality System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern information technology promises to improve service encounters through automated documentation or better decision traceability. At the same time, research suggests a negative impact of technology on human-to-human advisory services: including the possibility that computers might degrade the quality of interpersonal communication and reinforce unpleasant behaviors. Consequently, despite obvious improvements, information technology might have a negative impact on how the participants perceive the service. This might imply serious consequences for the service provider: dissatisfied clients, ineffective information exchange, and/or lack of transparency. This scenario slows down the diffusion of computers into advisory services in banks and insurance companies, and so designing systems for use in interpersonal services remains a challenge. This article provides evidence that LivePaper, a system designed alongside the material practices of a financial advisory encounter, helps to improve important service quality dimensions, making the services not only more pleasant for the participants, but also improving key marketing and business metrics of the service. In experimental advisory services, the sessions supported with LivePaper outperformed conventional services with regard to overall bank service quality and satisfaction, salesperson listening and interaction rating scores, as well as information transparency. This shows that a carefully designed system not only preserves the perceived quality of a service, but might improve it objectively, and has implications for the marketing and business value of the service.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415191"}, {"primary_key": "2466549", "vector": [], "sparse_vector": [], "title": "Decolonizing Tactics as Collective Resilience: Identity Work of AAPI Communities on Reddit.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Asian American and Pacific Islander (AAPI) communities use online platforms like Reddit to build capacity for resilience from white hegemony. We conduct interviews with 21 moderators of AAPI subreddits to understand how sociotechnical systems contour and contribute to the marginalization of online communities. We examine marginalization through the analytic framework of decolonization and uncover the threats and tactics that AAPI redditors encounter and employ to decolonize their collective identity. We find that moderators of AAPI subreddits develop collective resilience within their online communities by reclaiming space to confront brigade invasion, recording collective memory to circumvent systemic erasure, and revising cultural narratives to deconstruct colonial mentality. We describe how algorithmic configurations within sociotechnical systems reaffirm existing hegemonic values and discuss how users and designers of sociotechnical systems may work toward resisting white hegemony.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392881"}, {"primary_key": "2466550", "vector": [], "sparse_vector": [], "title": "Good for the Many or Best for the Few?: A Dilemma in the Design of Algorithmic Advice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Oded Nov"], "summary": "Applications in a range of domains, including route planning and well-being, offer advice based on the social information available in prior users' aggregated activity. When designing these applications, is it better to offer: a) advice that if strictly adhered to is more likely to result in an individual successfully achieving their goal, even if fewer users will choose to adopt it? or b) advice that is likely to be adopted by a larger number of users, but which is sub-optimal with regard to any particular individual achieving their goal? We identify this dilemma, characterized as Goal-Directed vs. Adoption-Directed advice, and investigate the design questions it raises through an online experiment undertaken in four advice domains (financial investment, making healthier lifestyle choices, route planning, training for a 5k run), with three user types, and across two levels of uncertainty. We report findings that suggest a preference for advice favoring individual goal attainment over higher user adoption rates, albeit with significant variation across advice domains; and discuss their design implications.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415239"}, {"primary_key": "2466551", "vector": [], "sparse_vector": [], "title": "Gender Differences in Graphic Design Q&amp;As: How Community and Site Characteristics Contribute to Gender Gaps in Answering Questions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Question and answer (Q&amp;A) sites can capture a range of user perspectives on using complex, feature-rich software. Little is known, however, on who is contributing to the sites. We look at contribution diversity from the perspective of gender in a domain with near gender parity: graphic design. Through content analysis of 330 answers from two popular Q&amp;A sites and semi-structured interviews with 24 graphic designers, we examine who is contributing, what content, how the community shows appreciation towards their answers, and perceived motivations and barriers to participation. We find that despite gender balance in the field, women contribute far less frequently than men. We also see gender differences in contribution styles and user appreciation. Our interviews shed further light on how Q&amp;A community cultures might be impacting men and women differently and how design choices made by the sites? developers might be exacerbating these differences. We suggest implications for design for improving gender inclusivity.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415184"}, {"primary_key": "2466553", "vector": [], "sparse_vector": [], "title": "Social Norm Vulnerability and its Consequences for Privacy and Safety in an Online Community.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Within online communities, social norms that both set expectations for and regulate behavior can be vital to the overall welfare of the community--particularly in the context of the privacy and safety of its members. For communities where the cost of regulatory failure can be high, it is important to understand both the conditions under which norms might be effective, and when they might fail. As a case study, we consider transformative fandom, a creative community dedicated to reimagining existing media in often subversive ways. In part due to the marginalized status of many members, there are strong, longstanding norms to protect the community. Through an interview study with 25 fandom participants, we investigate social norms that have been largely effective over time at maintaining member privacy and safety, but also break down under certain circumstances. Catalysts for these breakdowns include tensions between sub-communities and an increasing presence of outsiders, though most prominently, we identify a disconnect between the norms the community needs to support and the design of the platforms they occupy.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415226"}, {"primary_key": "2466554", "vector": [], "sparse_vector": [], "title": "Exploring Design Principles for Sharing of Personal Informatics Data on Ephemeral Social Media.", "authors": ["<PERSON>", "Siyun Ji", "<PERSON>", "Griffin D&apos;Haenens", "<PERSON><PERSON>", "<PERSON>"], "summary": "People often do not receive the engagement or responses they desire when they share on broad social media platforms. Sharers are hesitant to share trivial accomplishments, and the emphasis on data often results posts that audiences find repetitive or unengaging. Ephemeral social media's focus on self-authored content and sharing trivial accomplishments has the potential to ameliorate these challenges. We explore design principles for incorporating personal informatics data like steps, heart rate, or duration in data-driven stickers as a first step towards integrating these data into ephemeral social media. We examine the effect of a sticker's presentation style, domain, domain-relevance, and background through three surveys with 506 total participants. We uncover the importance of domain-relevant backgrounds and stickers, identify the situational value of stickers styled as analogies, embellished, and badges, and demonstrate that data-driven stickers can make ephemeral content more informative and entertaining, discussing implications for platforms and tools.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415166"}, {"primary_key": "2466555", "vector": [], "sparse_vector": [], "title": "I Can&apos;t Breathe: Reflections from Black Women in CSCW and HCI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, three Black women in HCI and CSCW share their experiences of being Black women academics enduring a global pandemic that is disportionately impacting the Black community while simultaneously experiencing the civil unrest due to racial injustice and police brutality. Using Black feminist epistemologies as a theoretical framework and auto-ethnography and testimonial authority as both methodology and epistemic resistance, the authors exercise epistemic agency to testify to their lived intersectional experiences and the various fronts on which they fight to be seen, to be heard, and to live. Additionally, they advocate for more inclusionary policies of Black women and other marginalized populations within the CSCW and HCI communities. We conclude with a call to action for both communities to: 1) stand in solidarity with Blacks in computing; and 2) acknowledge, disavow, and dismantle Whiteness and oppressive power structures in the field of computing, specifically HCI and CSCW.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432933"}, {"primary_key": "2466556", "vector": [], "sparse_vector": [], "title": "Exposing Error in Poverty Management Technology: A Method for Auditing Government Benefits Screening Tools.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Public benefits programs help people afford necessities like food, housing, and healthcare. In the US, such programs are means-tested: applicants must complete long forms to prove financial distress before receiving aid. Online benefits screening tools provide a gloss of such forms, advising households about their eligibility prior to completing full applications. If incorrectly implemented, screening tools may discourage qualified households from applying for benefits. Unfortunately, errors in screening tools are difficult to detect because they surface one at a time and difficult to contest because unofficial determinations do not generate a paper trail. We introduce a method for auditing such tools in four steps: 1) generate test households, 2) automatically populate screening questions with household information and retrieve determinations, 3) translate eligibility guidelines into computer code to generate ground truth determinations, and 4) identify conflicting determinations to detect errors. We illustrated our method on a real screening tool with households modeled from census data. Our method exposed major errors with corresponding examples to reproduce them. Our work provides a necessary corrective to an already arduous benefits application process.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392874"}, {"primary_key": "2466557", "vector": [], "sparse_vector": [], "title": "Impact of Hand Used on One-Handed Back-of-Device Performance.", "authors": ["Zhu<PERSON> Fan", "<PERSON><PERSON><PERSON>"], "summary": "One-handed Back-of-Device (BoD) interaction proved to be desired and sometimes unavoidable with a mobile touchscreen device, for both preferred and non-preferred hands. Although users? two hands are asymmetric, the impact of this asymmetry on the performance of mobile interaction has been little studied so far. Research on one-handed BoD interaction mostly focused on the preferred hand, even though users cannot avoid in real life to handle their phone with their non-preferred hand. To better design one-handed BoD interaction tailored for each hand, the identification and measure of the impact of their asymmetry are critical. In this paper, we study the impact on the performance of the asymmetry between the preferred and the non-preferred hands when interacting with one hand in the back of a mobile touch surface. Empirical data indicates that users' preferred hand performs better than the non-preferred hand in target acquisition tasks, for both time (+10%) and accuracy (+20%). In contrast, for steering tasks, we found little difference in performance between users' preferred and non-preferred hands. These results are useful for the HCI community to design mobile interaction techniques tailored for each hand only when it is necessary. We present implications for research and design directly based on the findings of the study, in particular, to reduce the impact of the asymmetry between hands and improve the performance of both hands for target acquisition.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427316"}, {"primary_key": "2466558", "vector": [], "sparse_vector": [], "title": "CrowdCO-OP: Sharing Risks and Rewards in Crowdsourcing.", "authors": ["Shaoyang Fan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Paid micro-task crowdsourcing has gained in popularity partly due to the increasing need for large-scale manually labelled datasets which are often used to train and evaluate Artificial Intelligence systems. Modern paid crowdsourcing platforms use a piecework approach to rewards, meaning that workers are paid for each task they complete, given that their work quality is considered sufficient by the requester or the platform. Such an approach creates risks for workers; their work may be rejected without being rewarded, and they may be working on poorly rewarded tasks, in light of the disproportionate time required to complete them. As a result, recent research has shown that crowd workers may tend to choose specific, simple, and familiar tasks and avoid new requesters to manage these risks. In this paper, we propose a novel crowdsourcing reward mechanism that allows workers to share these risks and achieve a standardized hourly wage equal for all participating workers. Reward-focused workers can thereby take up challenging and complex HITs without bearing the financial risk of not being rewarded for completed work. We experimentally compare different crowd reward schemes and observe their impact on worker performance and satisfaction. Our results show that 1) workers clearly perceive the benefits of the proposed reward scheme, 2) work effectiveness and efficiency are not impacted as compared to those of the piecework scheme, and 3) the presence of slow workers is limited and does not disrupt the proposed cooperation-based approaches.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415203"}, {"primary_key": "2466560", "vector": [], "sparse_vector": [], "title": "&apos;Yes, I comply!&apos;: Motivations and Practices around Research Data Management and Reuse across Scientific Fields.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As science becomes increasingly data-intensive, the requirements for comprehensive Research Data Management (RDM) grow. This often overwhelms scientists, requiring more workload and training. The failure to conduct effective RDM leads to producing research artefacts that cannot be reproduced or reused. Past research placed high value on supporting data science workers, but focused mainly on data production, collection, processing, and sensemaking. In order to understand practices and needs of data science workers in relation to documentation, preservation, sharing, and reuse, we conducted a cross-domain study with 15 scientists and data managers from diverse scientific domains. We identified five core concepts which describe requirements, drivers, and boundaries in the development of commitment for RDM, essential for generating reproducible research artefacts: Practice, Adoption, Barriers, Education, and Impact. Based on those concepts, we introduce a stage-based model of personal RDM commitment evolution. The model can be used to drive the design of future systems that support a transition to open science. We discuss infrastructure, policies, and motivations involved at the stages and transitions in the model. Our work supports designers in understanding the constraints and challenges involved in designing for reproducibility in an age of data-driven science.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415212"}, {"primary_key": "2466561", "vector": [], "sparse_vector": [], "title": "The New Reality of Reproducibility: The Role of Data Work in Scientific Research.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although reproducibility--the idea that a valid scientific experiment can be repeated with similar results--is integral to our understanding of good scientific practice, it has remained a difficult concept to define precisely. Across scientific disciplines, the increasing prevalence of large datasets, and the computational techniques necessary to manage and analyze those datasets, has prompted new ways of thinking about reproducibility. We present findings from a qualitative study of a NSF--funded two-week workshop developed to introduce an interdisciplinary group of domain scientists to data-management techniques for data-intensive computing, with a focus on reproducible science. Our findings suggest that the introduction of data-related activities promotes a new understanding of reproducibility as a mechanism for local knowledge transfer and collaboration, particularly as regards efficient software reuse.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392840"}, {"primary_key": "2466562", "vector": [], "sparse_vector": [], "title": "How We Write with Crowds.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Writing is a common task for crowdsourcing researchers exploring complex and creative work. To better understand how we write with crowds, we conducted both a literature review of crowd-writing systems and structured interviews with designers of such systems. We argue that the cognitive process theory of writing described by <PERSON> (1981), originally proposed as a theory of how solo writers write, offers a useful analytic lens for examining the design of crowd-writing systems. This lens enabled us to identify system design challenges that are inherent to the process of writing as well as design challenges that are introduced by crowdsourcing. The findings present both similarities and differences between how solo writers write versus how we write with crowds. To conclude, we discuss how the research community might apply and transcend the cognitive process model to identify opportunities for future research in crowd-writing systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432928"}, {"primary_key": "2466563", "vector": [], "sparse_vector": [], "title": "Conformity of Eating Disorders through Content Moderation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For individuals with mental illness, social media platforms are considered spaces for sharing and connection. However, not all expressions of mental illness are treated equally on these platforms. Different aggregates of human and technical control are used to report and ban content, accounts, and communities. Through two years of digital ethnography, including online observation and interviews, with people with eating disorders, we examine the experience of content moderation. We use a constructivist grounded theory approach to analysis that shows how practices of moderation across different platforms have particular consequences for members of marginalized groups, who are pressured to conform and compelled to resist. Above all, we argue that platform moderation is enmeshed with wider processes of conformity to specific versions of mental illness. Practices of moderation reassert certain bodies and experiences as 'normal' and valued, while rejecting others. At the same time, navigating and resisting these normative pressures further inscribes the marginal status of certain individuals. We discuss changes to the ways that platforms handle content related to eating disorders by drawing on the concept of multiplicity to inform design.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392845"}, {"primary_key": "2466564", "vector": [], "sparse_vector": [], "title": "Moving Across Lands: Online Platform Migration in Fandom Communities.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "When online platforms rise and fall, sometimes communities fade away, and sometimes they pack their bags and relocate to a new home. To explore the causes and effects of online community migration, we examine transformative fandom, a longstanding, technology-agnostic community surrounding the creation, sharing, and discussion of creative works based on existing media. For over three decades, community members have left and joined many different online spaces, from Usenet to Tumblr to platforms of their own design. Through analysis of 28 in-depth interviews and 1,886 survey responses from fandom participants, we traced these migrations, the reasons behind them, and their impact on the community. Our findings highlight catalysts for migration that provide insights into factors that contribute to success and failure of platforms, including issues surrounding policy, design, and community. Further insights into the disruptive consequences of migrations (such as social fragmentation and lost content) suggest ways that platforms might both support commitment and better support migration when it occurs.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392847"}, {"primary_key": "2466565", "vector": [], "sparse_vector": [], "title": "Using Data to Approach the Unknown: Patients? and Healthcare Providers? Data Practices in Fertility Challenges.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Medical practices have always been data oriented: healthcare providers decisions are based on data, both clinically generated and patient reported. With the increased use of patient-generated health data (PGHD), other types of data are entering providers? practices and influencing patient-provider collaboration. We studied the use of PGHD and its related data practices in the context of fertility, a health concern that is uncertain, complex, and data intensive. We interviewed 14 patients who are facing or have faced challenges to conceive and 5 healthcare providers specialized in infertility. Our findings show that patients and providers use PGHD in different ways but with the common goal of exploring 'the unknown' generated by the uncertainties of fertility. Providers use patients? data in a rational protocol, aiming to identify possible causes of infertility and define a treatment course. Patients use data in a much more emotional way, learning about their bodies while struggling with data interpretation challenges. By analyzing these data practices, we discuss the principles behind their differences and describe how they have individual benefits for each specific group. We then suggest that fertility technologies need to consider such principles, highlight the existing boundary between patients' and providers' data practices, and focus on bridging instead of merging them in order to facilitate collaboration and maintain their independent benefits.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432926"}, {"primary_key": "2466566", "vector": [], "sparse_vector": [], "title": "Measuring the Diversity of Facebook Reactions to Research.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online and in the real world, communities are bonded together by emotional consensus around core issues. Emotional responses to scientific findings often play a pivotal role in these core issues. When there is too much diversity of opinion on topics of science, emotions flare up and give rise to conflict. This conflict threatens positive outcomes for research. Emotions have the power to shape how people process new information. They can color the public's understanding of science, motivate policy positions, even change lives. And yet little work has been done to evaluate the public's emotional response to science using quantitative methods. In this paper, we use a dataset of responses to scholarly articles on Facebook to analyze the dynamics of emotional valence, intensity, and diversity. We present a novel way of weighting click-based reactions that increases their comprehensibility, and use these weighted reactions to develop new metrics of aggregate emotional responses. We use our metrics along with LDA topic models and statistical testing to investigate how users' emotional responses differ from one scientific topic to another. We find that research articles related to gender, genetics, or agricultural/environmental sciences elicit significantly different emotional responses from users than other research topics. We also find that there is generally a positive response to scientific research on Facebook, and that articles generating a positive emotional response are more likely to be widely shared---a conclusion that contradicts previous studies of other social media platforms.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375192"}, {"primary_key": "2466567", "vector": [], "sparse_vector": [], "title": "Mitigating Exploitation: Indie Game Developers&apos; Reconfigurations of Labor in Technology.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Much HCI research seeks to contribute to technological agendas that lead to more just and participative labor relations and practices, yet that research also raises concerns about forms of exploitation associated with them. In this paper, we explore how U.S. independent [indie] game developers' socio-technological practices inject forms of labor, capital, and production into the game development industry. Our findings highlight that indie game development 1) seeks to promote an alternative to business models of game development that depend on free and immaterial labor; 2) builds offline networks at different scales to develop collectives that can sustain their production; and 3) emphasizes how distributed collaboration, co-creation, and the use of free tools and middleware make game production more widely accessible. The research contributes to HCI research that seeks to explicate and mitigate emerging forms of exploitation enabled by new technologies and processes. Our critical review of indie developers' practices and strategies also extends the current conceptualization of labor and technology in CSCW.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392864"}, {"primary_key": "2466568", "vector": [], "sparse_vector": [], "title": "Body, <PERSON><PERSON>, and Me: The Presentation and Perception of Self in Social Virtual Reality.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Self-presentation in online digital social spaces has been a long standing research interest in HCI and CSCW. As online social spaces evolve towards more embodied digital representations, it is important to understand how users construct and experience their self and interact with others' self in new and more complicated ways, as it may introduce new opportunities and unseen social consequences. Using findings of an interview study (N=30), in this paper we report an in-depth empirical investigation of the presentation and perception of self in Social Virtual Reality (VR) - 3D virtual spaces where multiple users can interact with one another through VR head-mounted displays and full-body tracked avatars. This study contributes to the growing body of CSCW literature on social VR by offering empirical evidence of how social VR platforms afford new phenomena and approaches of novel identity practices and by providing potential design implications to further support such practices. We also expand the existing research agenda in CSCW on the increasing complexity of people's self-presentation in emerging novel sociotechnical systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432938"}, {"primary_key": "2466569", "vector": [], "sparse_vector": [], "title": "&quot;Pro-Amateur&quot;-Driven Technological Innovation: Participation and Challenges in Indie Game Development.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The phenomenon of end-user driven technological practices such as DIY making, hacking, crafting, and open design/manufacturing is shaping debates in HCI and CSCW about participatory innovation dynamics. However, prior research also reveals two limitations, namely, unequal participation in decision-making and the neglect of middle-tier \"pro-amateur\" end users. In this paper, we use independent [indie] game development as a case to explore the above-mentioned two key issues. Specifically, we highlight the importance of small teams, \"crafting,\" and \"democracy\" in supporting and facilitating middle-tier end-users' engagement with technology. Our focus on indie game developers, an understudied group of middle-tier end users in HCI and CSCW, offers new empirical evidence of the dynamic process through which pro-amateurs can participate in technological innovation. Understanding their practices and the socio-technological challenges that they face, therefore, informs the design of more participatory technologies that both allow hobbyists' and experts' innovation and support the technological practices performed by users who are at the middle-tier. This not only promotes the democratization of technology and bottom-up innovation but also adds nuance to existing literature on end-user driven technological practices.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375184"}, {"primary_key": "2466570", "vector": [], "sparse_vector": [], "title": "Side-Crossing Menus: Enabling Large Sets of Gestures for Small Surfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Supporting many gestures on small surfaces allows users to interact remotely with complex environments such as smart homes, large remote displays, or virtual reality environments, and switching between them (e.g., AR setup in a smart home). Providing eyes-free gestures in these contexts is important as this avoids disrupting the user's visual attention. However, very few techniques enable large sets of commands on small wearable devices supporting the user's mobility and even less provide eyes-free interaction. We present Side-Crossing Menus (SCM), a gestural technique enabling large sets of gestures on %small interactive surfaces like a smartwatch. Contrary to most gestural techniques, SCM relies on broad and shallow menus that favor small and rapid gestures. We demonstrate with a first experiment that users can efficiently perform these gestures eyes-free aided with tactile cues; 95% accuracy after training 20 minutes on a representative set of 30 gestures among 172. In a second experiment, we focus on the learning of SCM gestures and do not observe significant differences with conventional Multi-stroke Marking Menus in gesture accuracy and recall rate. As both techniques utilize contrasting menu structures, our results indicate that SCM is a compelling alternative for enhancing the input capabilities of small surfaces.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427317"}, {"primary_key": "2466571", "vector": [], "sparse_vector": [], "title": "Information Summary for Chronic Disease Treatment: A Pediatric Hospital Case in China.", "authors": ["Jiaojiao Fu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The electronic medical record (EMR) systems face many challenges in supporting chronic disease treatment, especially in medical information summary. Many solutions have recently been proposed for hospitals in developed countries. However, these solutions maybe not suitable for hospitals in developing countries because their workflow and patterns may be quite different due to their resource limitations, especially in shortage of physicians. Investigating the information summary alternatives in treating chronic diseases in such hospitals can shed light on EMR system design, especially on that for developing countries. We study one of the best pediatric hospitals in China. It suffers from a severe shortage of physicians. We introduce its information summary alternative, \\fs. In particular, we study how and why pediatricians treat chronic diseases with the sheet. Our work unveils the intense work patterns and their corresponding information requirements of hospitals in China. We also demonstrate the characteristics of the \\fs\\ and accordingly discuss how EMR systems can be optimized.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415248"}, {"primary_key": "2466574", "vector": [], "sparse_vector": [], "title": "Explainable Active Learning (XAL): Toward AI Explanations as Interfaces for Machine Teachers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The wide adoption of Machine Learning (ML) technologies has created a growing demand for people who can train ML models. Some advocated the term \"machine teacher'' to refer to the role of people who inject domain knowledge into ML models. This \"teaching'' perspective emphasizes supporting the productivity and mental wellbeing of machine teachers through efficient learning algorithms and thoughtful design of human-AI interfaces. One promising learning paradigm is Active Learning (AL), by which the model intelligently selects instances to query a machine teacher for labels, so that the labeling workload could be largely reduced. However, in current AL settings, the human-AI interface remains minimal and opaque. A dearth of empirical studies further hinders us from developing teacher-friendly interfaces for AL algorithms. In this work, we begin considering AI explanations as a core element of the human-AI interface for teaching machines. When a human student learns, it is a common pattern to present one's own reasoning and solicit feedback from the teacher. When a ML model learns and still makes mistakes, the teacher ought to be able to understand the reasoning underlying its mistakes. When the model matures, the teacher should be able to recognize its progress in order to trust and feel confident about their teaching outcome. Toward this vision, we propose a novel paradigm of explainable active learning (XAL), by introducing techniques from the surging field of explainable AI (XAI) into an AL setting. We conducted an empirical study comparing the model learning outcomes, feedback content and experience with XAL, to that of traditional AL and coactive learning (providing the model's prediction without explanation). Our study shows benefits of AI explanation as interfaces for machine teaching--supporting trust calibration and enabling rich forms of teaching feedback, and potential drawbacks--anchoring effect with the model judgment and additional cognitive workload. Our study also reveals important individual factors that mediate a machine teacher's reception to AI explanations, including task knowledge, AI experience and Need for Cognition. By reflecting on the results, we suggest future directions and design implications for XAL, and more broadly, machine teaching through AI explanations.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432934"}, {"primary_key": "2466575", "vector": [], "sparse_vector": [], "title": "Toward a Grassroots Culture of Technology Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Information and Communication Technologies (ICTs) can support grassroots social movements toward greateroutreach and better day-to-day communication. In this paper, we present the results of action research with alarge-scale grassroots social movement, the Southern Movement Assembly (SMA), exploring their uses andperceptions of ICTs. We find that the ICTs chosen by the SMA are often at odds with their grassroots cultureof inclusion and participation-which results in inequitable sociotechnical realities within the movement. Forexample, people with technical skills gain more power as they start to control organizational processes. Astechnical skills are most commonly associated with racial, gender, and socioeconomic privileges, this leads toinequitable participation. We conclude by calling for a grassroots culture of technology practice rooted inanalyses of systemic exclusion with a continuous effort to center the marginalized voices and experienceswith technology.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392862"}, {"primary_key": "2466576", "vector": [], "sparse_vector": [], "title": "&quot;I run the world&apos;s largest historical outreach project and it&apos;s on a cesspool of a website.&quot; Moderating a Public Scholarship Site on Reddit: A Case Study of r/AskHistorians.", "authors": ["<PERSON>"], "summary": "Online communities provide important functions in their participants' lives, from providing spaces to discuss topics of interest to supporting the development of close, personal relationships. Volunteer moderators play key roles in maintaining these spaces, such as creating and enforcing rules and modeling normative behavior. While these users play important governance roles in online spaces, less is known about how the work they do is impacted by platform design and culture. r/AskHistorians, a Reddit-based question and answer forum dedicated to providing users with academic-level answers to questions about history, provides an interesting case study on the impact of design and culture because of its unique rules and their strict enforcement by moderators. In this article I use interviews with r/AskHistorians moderators and community members, observation, and the full comment log of a highly upvoted thread to describe the impact of Reddit's design and culture on moderation work. Results show that visible moderation work that is often interpreted as censorship, and the default masculine whiteness of Reddit create challenges for moderators who use the subreddit as a public history site. Nonetheless, r/AskHistorians moderators have carved a space on Reddit where, through their public scholarship work, the community serves as a model for combating misinformation by building trust in academic processes.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392822"}, {"primary_key": "2466577", "vector": [], "sparse_vector": [], "title": "A Taxonomy of Team-Assembly Systems: Understanding How People Use Technologies to Form Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> S<PERSON> Contractor"], "summary": "The emergence of team-assembly technologies has brought with it new challenges in designing and implementing socio-technical systems. Our understanding of how systems shape the team-assembly processes is still limited. How do systems enable users to find teammates? How do users make decisions when using these systems? And what factors explain the characteristics of the teams assembled? Building on existing literature from CSCW, computer science, and management science, we propose a taxonomy to characterize how systems influence team assembly. This taxonomy argues that two dimensions determine how systems shape team assembly: (i) users? agency, to what extent the system enables its users to exercise their agency, and (ii) users? participation, how many users the system allows to participate in the team-formation process. The intersection of these two dimensions manifest four types of teams enabled by systems: self-assembled teams, staffed teams, optimized teams, and augmented teams. We characterize each one of these types of teams, considering their qualities, advantages, and challenges. To contextualize these types of teams, we map the current literature of team-assembly systems using a scoping literature review. Lastly, we discuss ways through which these two dimensions alter users' behavior, team diversity, and team composition. This paper provides theoretical implications and research questions for future systems that reconfigure the organization of people into teams.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415252"}, {"primary_key": "2466578", "vector": [], "sparse_vector": [], "title": "An Internet-less World?: Expected Impacts of a Complete Internet Outage with Implications for Preparedness and Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "In HCI research, non-use of systems, especially involuntary loss of use, is a relatively neglected topic. How do people perceive the impacts on their lives should the Internet go out for an extended period of time? Would it be a minor inconvenience or Armageddon? We took a Futures Studies approach to conduct a conceptualized future, scenario-based study with 754 responses to a survey of mostly students at three universities and used framing theory to help explicate the findings from qualitative data analysis. Our data show that most respondents do believe a long-term complete Internet outage is possible; however, perceptions in terms of what the impacts would be range widely. Most participants employ an egocentric frame to understand the impacts in terms of changes in their personal relationships and activities, with both gains and losses envisioned. A minority frame their visions in terms of the societal level, with severe infrastructure problems resulting. This paper is the first to explore perceptions of the potential impacts of an Internet outage and provide implications for preparedness and design.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375183"}, {"primary_key": "2466579", "vector": [], "sparse_vector": [], "title": "SketchADoodle: Touch-surface Multi-stroke Gesture Handling by <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Touch-surface multi-stroke gestures, as well as freehand drawings, are typically acquired by devices and sensors as a suite of timestamped points on a plane. This Cartesian coordinate system, although useful for computation like complexity analysis, gesture classification and recognition, becomes complex and inefficient when gestures need to be visualized and directly manipulated for editing. To address these challenges, a new mathematical representation of these gestures via a Bézier curve is defined to initiate a model-based approach for gesture direct manipulation (e.g., cut, copy, paste, translate, scale, rotate, deform, crop, compose, decompose). SketchADoodle, an Android-based mobile application for drawing, gesturing, demonstrates how the pseudo-code of the Bézier-based operations are engineered for real-time direct manipulation. We release the programming code for further development of gesture-based user interfaces based on Bézier curves.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397875"}, {"primary_key": "2466580", "vector": [], "sparse_vector": [], "title": "Party Politics, Values and the Design of Social Media Services: Implications of political elites&apos; values and ideologies to mitigating of political polarisation through design.", "authors": ["Kirsikka Grön", "<PERSON><PERSON>"], "summary": "There have been several attempts to support political engagement through novel social media services. While the political nature of any technologies is widely acknowledged in human-computer interaction, such considerations have been less studied when designing for political engagement in social media services. We used speculative design probes to gauge how political elites perceive alternative social media interfaces designed to increase media diversity and decrease political polarisation. We show how elites? reactions to design probes could be rooted into party ideologies. Based on these, we discuss directions how social media services could be designed. Our underlying contribution is wider. We show how studying elites can provide insights about politics embedded into proposed novel social media services for political engagement. Second, we demonstrate what elites? perspective provide fruitful way to expand and rethink concepts that have been used to study ordinary people before. Finally, we discuss potential challenges of replacing economic interests with political interest in social media service design and provide a richer account on the discussion about social media regulation. Therefore, we pave the way to more research to critically study elites within social computing scholarship and highlight the importance of understanding political motivations which can guide the development of social media.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415175"}, {"primary_key": "2466581", "vector": [], "sparse_vector": [], "title": "Personalised Services in Social Situations: Principal-Agent Relationships in Account Sharing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a qualitative study of how personal accounts on online services, such as Tinder, Netflix and Spotify, may be shared in particular social situations. We draw from agency theory's focus on principal-agent relationships and <PERSON><PERSON><PERSON>'s work on frames in analysing situations where others are allowed to use personal accounts, either for a shared purpose or on behalf of the account owner. We deploy <PERSON><PERSON><PERSON>'s concepts of regrounding to understand how interests behind activities are transformed and brackets to draw attention to the boundaries of different frames, and how these are incurred or broken in situations that exceed personal account use. Based on a set of 43 written descriptions of account sharing, we depict how employing others to act as agents to use one's personal accounts may lead to playful or serious use. Additionally, we discuss consequentiality of sharing personalised services, considering both what services might reveal about the account owner and how sharing takes place in the context of relationships. We contribute by illustrating how users' relationships with personalised services are complicated by the different interests that are served when accounts are shared.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432918"}, {"primary_key": "2466582", "vector": [], "sparse_vector": [], "title": "Trans Time: Safety, Privacy, and Content Warnings on a Transgender-Specific Social Media Site.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trans people often use social media to connect with others, find and share resources, and post transition-related content. However, because most social media platforms are not built with trans people in mind and because online networks include people who may not accept one's trans identity, sharing trans content can be difficult. We studied Trans Time, a social media site developed particularly for trans people to document transition and build community. We interviewed early Trans Time users (n = 6) and conducted focus groups with potential users (n = 21) to understand how a trans-specific site uniquely supports its users. We found that Trans Time has the potential to be a safe space, encourages privacy, and effectively enables its users to selectively view content using content warnings. Together, safety, privacy, and content warnings create an online space where trans people can simultaneously build community, find support, and express both the mundanity and excitement of trans life. Yet in each of these areas, we also learned ways that the site can improve. We provide implications for how social media sites may better support trans users, as well as insular communities of people from other marginalized groups.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415195"}, {"primary_key": "2466583", "vector": [], "sparse_vector": [], "title": "The Patient Advice System: A Technology Probe Study to Enable Peer Support in the Hospital.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although peer support technologies are critical resources for patients managing health conditions, they do not address the needs of patients in the hospital (i.e., inpatients) or the unique design constraints of this healthcare setting. To examine how the design of these technologies can meet the needs of inpatients, we conducted a technology probe study with 30 pediatric and adult inpatients. We created the Patient Advice System (PAS) to enable peer support in the hospital setting, then studied how participants used and perceived it during their stay. Inpatients used the PAS to exchange emotional support and share peer advice on a range of topics (e.g., adjusting to the hospital, communicating with providers). They identified several benefits (e.g., fostered connections) and challenges (e.g., competing clinical priorities) with using the PAS in the real-world context of their hospital stay. Based on our findings, we discuss three design opportunities-highlighting local expertise, designing for dynamic engagement, and providing alternative modes of peer support-for future peer support technologies to empower inpatients and overcome the difficulties they face within the hospital.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415183"}, {"primary_key": "2466584", "vector": [], "sparse_vector": [], "title": "ORES: Lowering Barriers with Participatory Machine Learning in Wikipedia.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Algorithmic systems---from rule-based bots to machine learning classifiers---have a long history of supporting the essential work of content moderation and other curation work in peer production projects. From counter-vandalism to task routing, basic machine prediction has allowed open knowledge projects like Wikipedia to scale to the largest encyclopedia in the world, while maintaining quality and consistency. However, conversations about how quality control should work and what role algorithms should play have generally been led by the expert engineers who have the skills and resources to develop and modify these complex algorithmic systems. In this paper, we describe ORES: an algorithmic scoring service that supports real-time scoring of wiki edits using multiple independent classifiers trained on different datasets. ORES decouples several activities that have typically all been performed by engineers: choosing or curating training data, building models to serve predictions, auditing predictions, and developing interfaces or automated agents that act on those predictions. This meta-algorithmic system was designed to open up socio-technical conversations about algorithms in Wikipedia to a broader set of participants. In this paper, we discuss the theoretical mechanisms of social change ORES enables and detail case studies in participatory machine learning around ORES from the 5 years since its deployment.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415219"}, {"primary_key": "2466585", "vector": [], "sparse_vector": [], "title": "Agency and Extraction in Emerging Industrial Drone Applications: Imaginaries of Rwandan Farm Workers and Community Members.", "authors": ["<PERSON>", "Carleen F. Maitland", "<PERSON>", "<PERSON>", "Fraterne Kagame", "<PERSON>"], "summary": "Rapidly diffusing 'industry 4.0' technologies stand to impact a broad range of stakeholders. Prior to implementation, forward looking formative analyses can identify systems and policy designs to promote equitable benefit. We investigate this potential through an analysis of stakeholders to a potential drone implementation on a small commercial farm in Rwanda. Translating stakeholders' imaginaries within a post-colonial frame, we identify hopes and concerns related to agency and influenced by global and local systems of power. The findings highlight constraints that recommend system designs promoting local agency and control and policies designed to balance local data management against potentially 'extractive' multinational data transfer processes.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432932"}, {"primary_key": "2466586", "vector": [], "sparse_vector": [], "title": "Combating Misinformation in Bangladesh: Roles and Responsibilities as Perceived by Journalists, Fact-checkers, and Users.", "authors": ["Md Mahfuzul <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "There has been a growing interest within CSCW community in understanding the characteristics of misinformation propagated through computational media, and the devising techniques to address the associated challenges. However, most work in this area has been concentrated on the cases in the western world leaving a major portion of this problem unaddressed that is situated in the Global South. This paper aims to broaden the scope of this discourse by focusing on this problem in the context of Bangladesh, a country in the Global South. The spread of misinformation on Facebook in Bangladesh, a country with a population over 163 million, has resulted in chaos, hate attacks, and killings. By interviewing journalists, fact-checkers, in addition to surveying the general public, we analyzed the current state of verifying misinformation in Bangladesh. Our findings show that most people in the `news audience' want the news media to verify the authenticity of online information that they see online. However, the newspaper journalists say that fact-checking online information is not a part of their job, and it is also beyond their capacity given the amount of information being published online everyday. We further find that the voluntary fact-checkers in Bangladesh are not equipped with sufficient infrastructural support to fill in this gap. We show how our findings are connected to some of the core concerns of CSCW community around social media, collaboration, infrastructural politics, and information inequality. From our analysis, we also suggest several pathways to increase the impact of fact-checking efforts through collaboration, technology design, and infrastructure development.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415201"}, {"primary_key": "2466587", "vector": [], "sparse_vector": [], "title": "On the Misinformation Beat: Understanding the Work of Investigative Journalists Reporting on Problematic Information Online.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Journalists are increasingly investigating and reporting on problematic online content such as misinformation, disinformation, and conspiracy theories, leading to the creation of a new misinformation beat. The process of collecting, analyzing, and reporting on this kind of data is complex and nuanced. It is especially challenging as online actors attempt to undermine their work. Through in-depth interviews with twelve journalists, we explore how they investigate and report on online misinformation and disinformation. Our findings reveal some of the unique challenges of reporting on this beat, as well as the ways in which reporters overcome those challenges. We highlight and discuss how journalistic values could be better embedded into the design of tools to support their work, the power dynamics between social media companies and journalists, and the promise of collaborations as a way to support and educate journalists on this beat. This work provides contextual knowledge to researchers looking to better support investigative journalists - on the misinformation beat and beyond - as their work becomes more entangled in sociotechnical systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415204"}, {"primary_key": "2466588", "vector": [], "sparse_vector": [], "title": "CrowdCog: A Cognitive Skill based System for Heterogeneous Task Assignment and Recommendation in Crowdsourcing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While crowd workers typically complete a variety of tasks in crowdsourcing platforms, there is no widely accepted method to successfully match workers to different types of tasks. Researchers have considered using worker demographics, behavioural traces, and prior task completion records to optimise task assignment. However, optimum task assignment remains a challenging research problem due to limitations of proposed approaches, which in turn can have a significant impact on the future of crowdsourcing. We present 'CrowdCog', an online dynamic system that performs both task assignment and task recommendations, by relying on fast-paced online cognitive tests to estimate worker performance across a variety of tasks. Our work extends prior work that highlights the effect of workers' cognitive ability on crowdsourcing task performance. Our study, deployed on Amazon Mechanical Turk, involved 574 workers and 983 HITs that span across four typical crowd tasks (Classification, Counting, Transcription, and Sentiment Analysis). Our results show that both our assignment method and recommendation method result in a significant performance increase (5% to 20%) as compared to a generic or random task assignment. Our findings pave the way for the use of quick cognitive tests to provide robust recommendations and assignments to crowd workers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415181"}, {"primary_key": "2466589", "vector": [], "sparse_vector": [], "title": "Evaluation of Skill Improvement by Combining Skill and Difficulty Levels During Paper-cutting Production.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In artworks such as paper-cutting, it is important to combine the skill of the artist with the difficulty of the rough sketch to improve the skill. We developed a system to measure the distances and widths of cutting lines and patterning the pictures based on the steering law. We quantitatively evaluated the difficulty level of the pictures. We developed an interactive system to support the improvement of cutting pressure control, which is one of the skills for making paper-cutting. Furthermore, we analyzed users' psychological state when making a paper cutting based on the psychological flow state. The flow state is a highly focused state that tends to be shown when the user's skills balance with the difficulty of the task. On the other hand, since it is difficult to analyze skills and difficulty quantitatively, many researchers evaluate them by qualitative analysis. In this paper, we set \"skill\" as the cutting ability and \"challenge\" as the difficulty level of the picture. We evaluated the improvement of the user's skill and the flow state through quantitative analysis. We developed a questionnaire to evaluate the psychological state of a paper-cutting process based on the existing flow state questionnaire. In this paper, we describe the changes in skill improvement due to the combination of user skill and picture difficulty, with \"skill\" being the cutting ability and \"challenge\" being the difficulty of the picture.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427319"}, {"primary_key": "2466590", "vector": [], "sparse_vector": [], "title": "Speculative Data Work &amp; Dashboards: Designing Alternative Data Visions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies data work in an organizational context, and suggests speculative data work as a useful concept and the speculative dashboard as a design concept, to better understand and support cooperative work. Drawing on fieldwork in a Danish public sector organisation, the paper identifies and conceptualizes the speculative data work performed around processes of digitalization and the push to become data-driven. The speculative dashboard is proposed as a design concept and opportunity for design, using practices from speculative design and research to facilitate speculation about data?its sources, visualizations, practices and infrastructures. It does so by hacking the 'genre' of the business intelligence data dashboard, and using it as a framework for the juxtaposition of different kinds of data, facilitating and encouraging speculation on alternative visions for data types and use. The paper contributes an empirical study of organizational use of and attitudes towards data, informing a novel design method and concept for co-speculating on alternative visions of and for organizational data.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434173"}, {"primary_key": "2466592", "vector": [], "sparse_vector": [], "title": "ShiSha: Enabling Shared Perspective With Face-to-Face Collaboration Using Redirected Avatars in Virtual Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The importance of remote collaboration grows in an interconnected world as the reasons to avoid travel increase. The spatial rendering and collaboration capabilities of virtual and augmented reality systems are well suited for tasks such as support or training. Users can take a shared perspective to build a common understanding. Also, users may engage in face-to-face cooperation to support interpersonal communication. However, a shared perspective and face-to-face collaboration are both desirable but naturally exclude each other. We place all users at the same location to provide a shared perspective. To avoid overlapping body parts, the avatars of the other connected users are shifted to the side. A redirected body pose modification corrects the resulting inconsistencies. The implemented system is compared to a baseline of two users standing in the same location and working with overlapping avatars. The results of a user study show that the proposed modifications provide an easy to use, efficient collaboration and yield higher co-presence and the feeling of teamwork. Applying redirection techniques to other users opens up novel ways to increase social presence for local or remote collaboration.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432950"}, {"primary_key": "2466594", "vector": [], "sparse_vector": [], "title": "VizSciFlow: A Visually Guided Scripting Framework for Supporting Complex Scientific Data Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Scientific workflow management systems such as Galaxy, Taverna and Workspace, have been developed to automate scientific workflow management and are increasingly being used to accelerate the specification, execution, visualization, and monitoring of data-intensive tasks. For example, the popular bioinformatics platform Galaxy is installed on over 168 servers around the world and the social networking space myExperiment shares almost 4,000 Galaxy scientific workflows among its 10,665 members. Most of these systems offer graphical interfaces for composing workflows. However, while graphical languages are considered easier to use, graphical workflow models are more difficult to comprehend and maintain as they become larger and more complex. Text-based languages are considered harder to use but have the potential to provide a clean and concise expression of workflow even for large and complex workflows. A recent study showed that some scientists prefer script/text-based environments to perform complex scientific analysis with workflows. Unfortunately, such environments are unable to meet the needs of scientists who prefer graphical workflows. In order to address the needs of both types of scientists and at the same time to have script-based workflow models because of their underlying benefits, we propose a visually guided workflow modeling framework that combines interactive graphical user interface elements in an integrated development environment with the power of a domain-specific language to compose independently developed and loosely coupled services into workflows. Our domain-specific language provides scientists with a clean, concise, and abstract view of workflow to better support workflow modeling. As a proof of concept, we developed VizSciFlow, a generalized scientific workflow management system that can be customized for use in a variety of scientific domains. As a first use case, we configured and customized VizSciFlow for the bioinformatics domain. We conducted three user studies to assess its usability, expressiveness, efficiency, and flexibility. Results are promising, and in particular, our user studies show that VizSciFlow is more desirable for users to use than either Python or Galaxy for solving complex scientific problems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394976"}, {"primary_key": "2466595", "vector": [], "sparse_vector": [], "title": "From Paper Flight Strips to Digital Strip Systems: Changes and Similarities in Air Traffic Control Work Practices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To increase capacity and safety in air traffic control, digital strip systems have superseded paper strips in lower airspace control centers in Europe. Previous ethnographic studies on paper strip systems anticipated a radical change in work practices with digital strip systems, but we are not aware of any studies that evaluated these predictions. We carried out contextual inquiries with controllers and focused on face-to-face and radio communication, interactions with the digital strip system and the workspace in general. In turn, we contribute (1) detailed descriptions of controllers' work practices, such as using tacit information from radio communication and 'standard advocates vs. tinkerers' operation modes, (2) respective implications for design and (3) discuss how the observed work practices are similar or different from the reported practices in the literature of the two preceding decades. Our key insights are, that documentation speed is faster with digital strips, although a high load in the case of radio frequency persists. Controllers retrieve tacit information from the radio communication and combine it with scattered cues from several displays to form empathic decisions that sometimes exceed the standard protocol. We conclude that the role of tacit information holds opportunities for future flight systems and should be considered in a holistic approach to individualized workspaces for controllers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392833"}, {"primary_key": "2466596", "vector": [], "sparse_vector": [], "title": "It&apos;s the Wild, Wild West: Lessons Learned From IRB Members&apos; Risk Perceptions Toward Digital Research Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Digital technology that is prevalent in people's everyday lives, including smart home devices, mobile apps and social media, increasingly lack regulations for how the user data can be collected, used or disseminated. The CSCW and the larger computing community continue to evaluate and understand the potential negative impacts of research involving digital technologies. As more research involves digital data, Institutional Review Boards (IRBs) take on the difficult task of evaluating and determining risks--likelihood of potential harms--from digital research. Learning more about IRBs' role in concretizing harm and its likelihood will help us critically examine the current approach to regulating digital research, and has implications for how researchers can reflect on their own data practices. We interviewed 22 U.S.-based IRB members and found that, for the interviewees, \"being digital\" added a risk. Being digital meant increasing possibilities of confidentiality breach, unintended collection of sensitive information, and unauthorized data reuse. Concurrently, interviewees found it difficult to pinpoint the direct harms that come out of those risks. The ambiguous, messy, and situated contexts of digital research data did not fit neatly into current human subjects research protection protocols. We discuss potential solutions for understanding risks and harms of digital technology and implications for the responsibilities of the CSCW and the larger computing community in conducting digital research.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392868"}, {"primary_key": "2466597", "vector": [], "sparse_vector": [], "title": "Measuring Misinformation in Video Search Platforms: An Audit Study on YouTube.", "authors": ["<PERSON><PERSON><PERSON>", "Prerna Juneja", "<PERSON><PERSON><PERSON>"], "summary": "Search engines are the primary gateways of information. Yet, they do not take into account the credibility of search results. There is a growing concern that YouTube, the second largest search engine and the most popular video-sharing platform, has been promoting and recommending misinformative content for certain search topics. In this study, we audit YouTube to verify those claims. Our audit experiments investigate whether personalization (based on age, gender, geolocation, or watch history) contributes to amplifying misinformation. After shortlisting five popular topics known to contain misinformative content and compiling associated search queries representing them, we conduct two sets of audits-Search-and Watch-misinformative audits. Our audits resulted in a dataset of more than 56K videos compiled to link stance (whether promoting misinformation or not) with the personalization attribute audited. Our videos correspond to three major YouTube components: search results, Up-Next, and Top 5 recommendations. We find that demographics, such as, gender, age, and geolocation do not have a significant effect on amplifying misinformation in returned search results for users with brand new accounts. On the other hand, once a user develops a watch history, these attributes do affect the extent of misinformation recommended to them. Further analyses reveal a filter bubble effect, both in the Top 5 and Up-Next recommendations for all topics, except vaccine controversies; for these topics, watching videos that promote misinformation leads to more misinformative video recommendations. In conclusion, YouTube still has a long way to go to mitigate misinformation on its platform.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392854"}, {"primary_key": "2466598", "vector": [], "sparse_vector": [], "title": "Investigating How Smartphone Movement is Affected by Lying Down Body Posture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Itiro <PERSON>"], "summary": "In this paper, we investigated how \"lying down'' body postures affected the use of the smartphone user interface (UI) design. Extending previous research that studied body postures, handgrips, and the movement of the smartphone. We have done this in three steps; (1) An online survey that examined what type of lying down postures, participants, utilized when operating a smartphone; (2) We broke down these lying down postures in terms of body angle (i.e., users facing down, facing up, and on their side) and body support; (3) We conducted an experiment questioning the effects that these body angles and body supports had on the participants' handgrips. What we found was that the smartphone moves the most (is the most unstable) in the \"facing up (with support)'' condition. Additionally, we discovered that the participants preferred body posture was those that produced the least amount of motion (more stability) with their smartphones.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427320"}, {"primary_key": "2466599", "vector": [], "sparse_vector": [], "title": "Shifting forms of Engagement: Volunteer Learning in Online Citizen Science.", "authors": ["<PERSON>", "Carsten S. Ø<PERSON>lund", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Peer production projects involve people in many tasks, from editing articles to analyzing datasets. To facilitate mastery of these practices, projects offer a number of learning resources, ranging from project-defined FAQsto individually-oriented search tools and communal discussion boards. However, it is not clear which project resources best support participant learning, overall and at different stages of engagement. We draw on<PERSON><PERSON><PERSON><PERSON>'s framework of forms of presence to distinguish three types of engagement with learning resources:authoritative, agent-centered and communal. We assigned resources from the Gravity Spy citizen-science into these three categories and analyzed trace data recording interactions with resources using a mixed-effects logistic regression with volunteer performance as an outcome variable. The findings suggest that engagement with authoritative resources (e.g., those constructed by project organizers) facilitates performance initially. However, as tasks become more difficult, volunteers seek and benefit from engagement with their own agent-centered resources and community-generated resources. These findings suggest a broader scope for the design of learning resources for peer production", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392841"}, {"primary_key": "2466600", "vector": [], "sparse_vector": [], "title": "&quot;(We) Can Talk It Out...&quot;: Designing for Promoting Conflict-Resolution Skills in Youth on a Moderated Minecraft Server.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online multiplayer games like Minecraft, gaining increasing popularity among present-day youth, include rich contexts for social interactions but are also rife with interpersonal conflict among players. Research shows that a variety of socio-technical mechanisms (e.g., server rules, chat filters, use of in-game controls to ban players, etc.) aim to limit and/or eliminate social conflict in games like Minecraft. However, avoiding social conflict need not necessarily always be a useful approach. Broadly defined in CSCW literature as a phenomenon that may arise even amidst mutual cooperation, social conflict can yield positive outcomes depending on how it is managed (e.g., [<PERSON> et al.,1993]). In fact, the aforementioned approaches to avoid conflict may not be helpful as they do not help youth understand how to address similar interpersonal differences that may occur in other social settings. Furthermore, prior research has established the value of developing conflict-resolution skills during early adolescence within safe settings, such as school/after-school wellness and prevention interventions (e.g.,[<PERSON><PERSON>, 1982], [<PERSON><PERSON> et al., 1998]), for later success in any given interpersonal relationship. While games like Minecraft offer authentic contexts for encountering social conflict, little work thus far has explored how to help youth develop conflict-resolution skills by design interventions within online interest-driven settings. Drawing from prior literature in CSCW, youth wellness and prevention programs, we translated offline evidence-based strategies into the design of an online, after-school program that was run within a moderated Minecraft server. The online program, titled Survival Lab, was designed to promote problem-solving and conflict-resolution skills in youth (ages 8-14 years). We conducted a field study for six months (30 youth participants, four college-age moderators, and one high-school volunteer aged 15 years) using in-game observations and digital trace ethnographic approaches. Our study data reveals that participating youth created community norms and developed insightful solutions to conflicts in Survival Lab. Our research offers three key takeaways. Firstly, online social games like Minecraft lend themselves as feasible settings for the translation of offline evidence-based design strategies in promoting the development of conflict-resolution and other social competencies among youth. Secondly, the design features that support structured and unstructured play while enabling freedom of choice for youth to engage as teams and/or individuals are viable for collective or community-level outcomes. Third and finally, moderators, as caring adults and near-peer mentors, play a vital role in facilitating the development of conflict-resolution skills and interest-driven learning among youth. We discuss the implications of our research for translating offline design to online play-based settings as sites and conclude with recommendations for future work.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392855"}, {"primary_key": "2466601", "vector": [], "sparse_vector": [], "title": "CommunityClick: Capturing and Reporting Community Feedback from Town Halls to Improve Inclusivity Share on.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Local governments still depend on traditional town halls for community consultation, despite problems such as a lack of inclusive participation for attendees and difficulty for civic organizers to capture attendees' feedback in reports. Building on a formative study with 66 town hall attendees and 20 organizers, we designed and developed CommunityClick, a communitysourcing system that captures attendees' feedback in an inclusive manner and enables organizers to author more comprehensive reports. During the meeting, in addition to recording meeting audio to capture vocal attendees' feedback, we modify iClickers to give voice to reticent attendees by allowing them to provide real-time feedback beyond a binary signal. This information then automatically feeds into a meeting transcript augmented with attendees' feedback and organizers' tags. The augmented transcript along with a feedback-weighted summary of the transcript generated from text analysis methods is incorporated into an interactive authoring tool for organizers to write reports. From a field experiment at a town hall meeting, we demonstrate how CommunityClick can improve inclusivity by providing multiple avenues for attendees to share opinions. Additionally, interviews with eight expert organizers demonstrate CommunityClick's utility in creating more comprehensive and accurate reports to inform critical civic decision-making. We discuss the possibility of integrating CommunityClick with town hall meetings in the future as well as expanding to other domains.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432912"}, {"primary_key": "2466603", "vector": [], "sparse_vector": [], "title": "Roles in the Discussion: An Analysis of Social Support in an Online Forum for People with Dementia.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social computing provides a variety of challenges and opportunities for people who are experiencing cognitive decline. Following a recent diagnosis of dementia, older adults sometimes engage in online communities designated for people with dementia. In this work, we analyzed all original posts from a seven-year period on a forum for persons with dementia to understand 1) who posts in this forum and 2) what kinds of social support posters seek to gain from this engagement. Our analyses indicate that, in addition to people with dementia, a variety of dementia-related stakeholders create original posts in this forum. Our results suggest that seeking and offering social support are key activities for all forum users, not just those with dementia for whom the forum was designated. Given these findings, we offer design implications for online spaces for vulnerable communities, with a focus on design that allows for external stakeholder participation while still maintaining the privacy and safety of vulnerable members of the community.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415198"}, {"primary_key": "2466604", "vector": [], "sparse_vector": [], "title": "Remote Communication in Wilderness Search and Rescue: Implications for the Design of Emergency Distributed-Collaboration Tools for Network-Sparse Environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wilderness search and rescue (WSAR) requires careful communication between workers in different locations. To understand the contexts from which WSAR workers communicate and the challenges they face, we interviewed WSAR workers and observed a mock-WSAR scenario. Our findings illustrate that WSAR workers face challenges in maintaining a shared mental model. This is primarily done through distributed communication using two-way radios and cell phones for text and photo messaging; yet both implicit and explicit communication suffer. WSAR workers send messages for various reasons and share different types of information with varying levels of urgency. This warrants the use of multiple communication modalities and information streams. However, bringing in more modalities introduces the risk of information overload, and thus WSAR workers today still primarily communicate remotely via the radio. Our work demonstrates opportunities for technology to provide implicit communication and awareness remotely, and to help teams maintain a shared mental model even when synchronous realtime communication is sparse. Furthermore, technology should be designed to bring together multiple streams of information and communication while making sure that they are presented in ways that aid WSAR workers rather than overwhelming them.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375190"}, {"primary_key": "2466605", "vector": [], "sparse_vector": [], "title": "Learning from Family Mysteries: Accounting for Untold Stories in Family Memory Practices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given the importance and social significance of passing down family stories to each generation, why do important family stories not get told? How should designers of digital family storytelling platforms address missing or incomplete parts of narratives? Drawing from the results of an interview-based, practice-oriented inquiry, we argue that non-telling should be considered an important and integral part of family storytelling. Our findings show that non-telling is not simply silence. Non-telling allows family members to observe protective and discretionary values essential to the identity-making and relational goals of family storytelling. We also show ways that a person's reticence is situated and may change over time. In our discussion, we provide design strategies for family storytelling technologies to make room for silence and incorporate the values, purposes, and practices of non-telling.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432920"}, {"primary_key": "2466606", "vector": [], "sparse_vector": [], "title": "Through the Looking Glass: Study of Transparency in Reddit&apos;s Moderation Practices.", "authors": ["Prerna Juneja", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transparency in moderation practices is crucial to the success of an online community. To meet the growing demands of transparency and accountability, several academics came together and proposed the Santa Clara Principles on Transparency and Accountability in Content Moderation (SCP). In 2018, Reddit, home to uniquely moderated communities called subreddits, announced in its transparency report that the company is aligning its content moderation practices with the SCP. But do the moderators of subreddit communities follow these guidelines too? In this paper, we answer this question by employing a mixed-methods approach on public moderation logs collected from 204 subreddits over a period of five months, containing more than 0.5M instances of removals by both human moderators and AutoModerator. Our results reveal a lack of transparency in moderation practices. We find that while subreddits often rely on AutoModerator to sanction newcomer posts based on karma requirements and moderate uncivil content based on automated keyword lists, users are neither notified of these sanctions, nor are these practices formally stated in any of the subreddits' rules. We interviewed 13 Reddit moderators to hear their views on different facets of transparency and to determine why a lack of transparency is a widespread phenomenon. The interviews reveal that moderators' stance on transparency is divided, there is a lack of standardized process to appeal against content removal and Reddit's app and platform design often impede moderators' ability to be transparent in their moderation practices.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375197"}, {"primary_key": "2466607", "vector": [], "sparse_vector": [], "title": "Probing User Perceptions of On-Skin Notification Displays.", "authors": ["<PERSON><PERSON><PERSON> (Cindy) <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "On-skin displays are emerging as a wearable form factor for the display of information; however, the perception of using such devices in public could determine whether they are eventually adopted or rejected. This study investigated the means by which on-skin notification displays are perceived by the general public. We adopted a mixed-methods approach to the analysis of results from an online survey (n = 254) and in-lab interviews (n = 36) pertaining to the novel form factor, device materiality, and envisioned use cases. The study was conducted in the US and Taiwan in order to examine cross-cultural attitudes toward device usage. The results of this structured examination provide valuable insights into the design of on-skin notification displays for everyday use across cultures.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432943"}, {"primary_key": "2466608", "vector": [], "sparse_vector": [], "title": "Gestatten: Estimation of User&apos;s Attention in Mobile MOOCs From Eye Gaze and Gaze Gesture Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid proliferation of Massive Open Online Courses (MOOC) has resulted in many-fold increase in sharing the global classrooms through customized online platforms, where a student can participate in the classes through her personal devices, such as personal computers, smartphones, tablets, etc. However, in the absence of direct interactions with the students during the delivery of the lectures, it becomes difficult to judge their involvements in the classroom. In academics, the degree of student's attention can indicate whether a course is efficacious in terms of clarity and information. An automated feedback can hence be generated to enhance the utility of the course. The precision of discernment in the context of human attention is a subject of surveillance. However, visual patterns indicating the magnitude of concentration can be deciphered by analyzing the visual emphasis and the way an individual visually gesticulates, while contemplating the object of interest. In this paper, we develop a methodology called Gestsatten which captures the learner's attentiveness from his visual gesture patterns. In this approach, the learner's visual gestures are tracked along with the region of focus. We consider two aspects in this approach -- first, we do not transfer learner's video outside her device, so we apply in-device computing to protect her privacy; second, considering the fact that a majority of the learners use handheld devices like smartphones to observe the MOOC videos, we develop a lightweight approach for in-device computation. A three level estimation of learner's attention is performed based on these information. We have implemented and tested Gestatten over 48 participants from different age groups, and we observe that the proposed technique can capture the attention level of a learner with high accuracy (average absolute error rate is 8.68%), which meets her ability to learn a topic as measured through a set of cognitive tests.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394974"}, {"primary_key": "2466609", "vector": [], "sparse_vector": [], "title": "CAPath: 3D-Printed Interfaces with Conductive Points in Grid Layout to Extend Capacitive Touch Inputs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a 3D-printed interface, CAPath, in which conductive contact points are in a grid layout. This structure allows not only specific inputs (e.g., scrolling or pinching) but also general 2D inputs and gestures that fully leverage the \"touch surface.\" We provide the requirements to fabricate the interface and implement a designing system to generate 3D objects in the conductive grid structure. The CAPath interface can be utilized in the uniquely shaped interfaces and opens up further application fields that cannot currently be accessed with existing passive touch extensions. Our contributions also include an evaluation for the recognition accuracy of the touch operations with the implemented interfaces. The results show that our technique is promising to fabricate customizable touch-sensitive interactive objects.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427321"}, {"primary_key": "2466610", "vector": [], "sparse_vector": [], "title": "Towards Rapid Prototyping of Foldable Graphical User Interfaces with Flecto.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Whilst new patents and announcements advertise the technical availability of foldable displays, which are capable to be folded to some extent, there is still a lack of fundamental and applied understanding of how to model, to design, and to prototype graphical user interfaces for these devices before actually implementing them. Without waiting for their off-the-shelf availability and without being tied to any physical foldable mechanism, Flecto defines a model, an associated notation, and a supporting software for prototyping graphical user interfaces running on foldable displays, such as foldable smartphone or assemblies of foldable surfaces. For this purpose, we use an extended notation of the Yoshizawa-Randlett diagramming system, used to describe the folds of origami models, to characterize a foldable display and define possible interactive actions based on its folding operations. A guiding method for rapidly prototyping foldable user interfaces is devised and supported by Flecto, a design environment where foldable user interfaces are simulated in 3D environment instead of in physical reality. We report on a case study to demonstrate Flecto in action and we gather the feedback from users on Flecto, using Microsoft Product Reaction Cards.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427322"}, {"primary_key": "2466612", "vector": [], "sparse_vector": [], "title": "PACMHCI V4 CSCW2 Oct 2020 Editorial.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "We are delighted to welcome you to this issue of the Proceedings of the ACM on Human-Computer Interaction, which contains scholarship from the Computer-Supported Cooperative Work and Social Computing (CSCW) community. This is the second issue that represents the new quarterly submission model. This issue has 91 papers accepted from the January 2020 round (16 previously accepted papers from the January 2020 round were published in a prior issue and another 19 accepted this round will be published in a later issue). This represents an overall 40.6% acceptance rate from the 310 submissions in January 2020. This issue represents the contributions of external reviewers, Associate Chairs, and the dedicated Editors, who were essential to carrying through the review process, especially during a global pandemic. As Papers Chairs, we are delighted to continue shaping and disseminating CSCW's tradition of high-quality scholarship.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415161"}, {"primary_key": "2466613", "vector": [], "sparse_vector": [], "title": "PACMHCI V4 CSCW3 December 2020 Continued Editorial.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432907"}, {"primary_key": "2466614", "vector": [], "sparse_vector": [], "title": "Countering Fake News: A Comparison of Possible Solutions Regarding User Acceptance and Effectiveness.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Since the emergence of so-called fake news on the internet and in social media, platforms such as Facebook have started to take countermeasures, and researchers have begun looking into this phenomenon from a variety of perspectives. A large number of scientific work has investigated ways to detect fake news automatically. Less attention has been paid to the subsequent step, i.e., what to do when you are aware of the inaccuracy of claims in social media. This work takes a user-centered approach on means to counter identified mis- and disinformation in social media. We conduct a three-step study design on how approaches in social media should be presented to respect the users' needs and experiences and how effective they are. As our first step, in an online survey representative for some factors to the German adult population, we enquire regarding their strategies on handling information in social media, and their opinion regarding possible solutions --- focusing on the approach of displaying a warning on inaccurate posts. In a second step, we present five potential approaches for countermeasures identified in related work to interviewees for qualitative input. We discuss (1) warning, (2) related articles, (3) reducing the size, (4) covering, and (5) requiring confirmation. Based on the interview feedback, as the third step of this study, we select, improve, and examine four promising approaches on how to counter misinformation. We conduct an online experiment to test their effectiveness on the perceived accuracy of false headlines and also ask for the users' preferences. In this study, we find that users welcome warning-based approaches to counter fake news and are somewhat critical with less transparent methods. Moreover, users want social media platforms to explain why a post was marked as disputed. The results regarding effectiveness are similar: Warning-based approaches are shown to be effective in reducing the perceived accuracy of false headlines. Moreover, adding an explanation to the warning leads to the most significant results. In contrast, we could not find a significant effect on one of Facebook's current approaches (reduced post size and fact-checks in related articles).", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415211"}, {"primary_key": "2466616", "vector": [], "sparse_vector": [], "title": "Personal Data and Power Asymmetries in U.S. Collegiate Sports Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collaborations increasingly draw on personal data. We examine personal-data-supported collaborations in a high stakes, high-performance environment: collegiate sports. We conducted 22 interviews with people from four common roles within collegiate sports teams: athletes, sport coaches, athletic trainers, and strength and conditioning coaches. Using boundary negotiating artifacts as a lens for analysis, we describe an ecology of personal data in collaborations among these four roles. We use this ecology to highlight tensions and foreground issues of power asymmetry in these collaborations. To characterize these power asymmetries in the collaborative use of personal data, we propose an extension of boundary negotiating artifacts: extraction artifacts.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375185"}, {"primary_key": "2466618", "vector": [], "sparse_vector": [], "title": "Emotion Regulation in eSports Gaming: A Qualitative Study of League of Legends.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today eSports gaming is enjoying growing popularity in the world and much attention from various research areas, including CSCW. eSports gaming is a highly competitive environment commonly associated with negative emotions such as anxiety and stress. However, little attention has been paid to emotion regulation in eSports gaming. In this study, we empirically investigated how players experience emotion and regulate emotions in League of Legends, one of the largest eSports games today. We identify four emotive factors, as well as emotion regulation strategies that players deploy to manage the emotions of their selves, teammates, and opponents. We further report on how they use emotion regulation in emotional self-care and emotional leadership. Building upon this set of findings, we discuss how the competitive eSports gaming context conditions emotion regulation in League of Legends, foreground emotion regulation expertise in competitive gaming, and derive implications for designing emotion regulation technologies.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415229"}, {"primary_key": "2466619", "vector": [], "sparse_vector": [], "title": "Towards Building Community Collective Efficacy for Managing Digital Privacy and Security within Older Adult Communities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Older adults are increasingly becoming adopters of digital technologies, such as smartphones; however, this population remains particularly vulnerable to digital privacy and security threats. To date, most research on technology used among older adults focuses on helping individuals overcome their discomfort or lack of expertise with technology to protect them from such threats. Instead, we are interested in how communities of older adults work together to collectively manage their digital privacy and security. To do this, we surveyed 67 individuals across two older adult communities (59 older adults and eight employees or volunteers) and found that the community's collective efficacy for privacy and security was significantly correlated with the individuals' self-efficacy, power usage of technology, and their sense of community belonging. Community collective efficacy is a group's mutual belief in its ability to achieve a shared goal. Using social network analysis, we further unpacked these relationships to show that many older adults interact with others who have similar technological expertise, and closer-knit older adult communities that have low technology expertise (i.e., low power usage and self-efficacy) may increase their community collective efficacy for privacy and security by embedding facilitators (e.g., employees or volunteers) who have more technical expertise within their communities. Our work demonstrates how both peer influence and outside expertise can be leveraged to support older adults in managing their digital privacy and security.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432954"}, {"primary_key": "2466620", "vector": [], "sparse_vector": [], "title": "Towards a Reliable Ground Truth for Drowsiness: A Complexity Analysis on the Example of Driver Fatigue.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The increasing number and complexity of advanced driver assistance systems (ADAS) pave the way for fully automated driving. Automated vehicles are said to increase road safety and prevent human-made (fatal) accidents, amongst others. In the lower levels of automation, however, the driver is still responsible as a fallback authority. As a consequence, systems that reliably monitor the driver's state, especially the risk factor drowsiness, become increasingly essential to ensure the driver's ability to take over control from the vehicle on time. In research, the use of supervised machine learning for drowsiness detection is the prevalent method. As the ground truth for drowsiness is both application- and user-dependent, and no golden standard exists for its definition, measures are usually applied in the form of observer ratings. Also, in this work, observer ratings were investigated with regard to the required level of detail/complexity. To this end, video data, recorded within a simulator study (N = 30) comprised of each 45-minute manual and automated driving sessions, were evaluated by trained raters. Correlation analysis results show that - depending on the number of drowsiness levels - a comparable ground truth can be generated by reducing the rating frequency and thus the rating complexity by a factor of five. The knowledge gained can be used in future studies in this research area, the collection of a reliable and valid ground truth of drowsiness, as well as for improving the process in developing interactive drowsiness detection systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394980"}, {"primary_key": "2466621", "vector": [], "sparse_vector": [], "title": "Effects of Shared Gaze on Audio- Versus Text-Based Remote Collaborations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Remote collaborations are becoming ubiquitous, but, despite their many advantages, face unique challenges compared to collocated collaborations. Visualizing the collaborator's point of gaze on a shared screen has been explored as a promising way to alleviate some of these limitations by increasing shared awareness. However, prior studies on shared gaze have not considered the medium of communication and have only studied its effect on audio. This paper presents a study that compares the effects of shared gaze on collaboration performance during audio- and text-based communication. We find that for text, shared gaze improved task correctness and led collaborators to look at and talk more about shared content. Similar trends are found for gaze-augmented voice communication, but contrary to the slower performance in text, it also saw improvements in completion time as well as in cognitive workload. Our findings demonstrate the differences in how shared gaze impacts audio- versus text-based communication and highlight the need to further understand the nuances of the medium of communication when designing novel tools to support remote collaborators.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415207"}, {"primary_key": "2466623", "vector": [], "sparse_vector": [], "title": "Life Review Using a Life Metaphoric Game to Promote Intergenerational Communication.", "authors": ["<PERSON><PERSON><PERSON>", "Hyunyoung Oh", "Chungkon Shi", "<PERSON> <PERSON><PERSON>"], "summary": "This study explores whether a life metaphoric game can effectively facilitate communication between senior participants and young adult partners during a life review activity. Life review provides older adults with opportunities to organize their past experiences, rediscover the meaning of life, and prepare for their future life and eventual death. We held workshops in which 33 senior participants (ages 51-85) co-played a commercial game, titled \"Long Journey of Life,\" paired with young adult partners (ages 19-23). The young partners asked senior participants about their associated memories during the life review activity. Through inductive thematic analysis, we found seven communication themes during the life review activity: reminiscence, future and death preparation, life advice, appreciation and evaluation, small talks involving self-disclosure, interpretation of metaphors, and explanation of game control methods. We investigated the relationships between communication themes and game design elements that promote conversation. In addition, we identified how participants' responses to the game differ depending on the player's characteristics and generation. Game design suggestions for an effective intergenerational life review using digital games were offered.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415169"}, {"primary_key": "2466624", "vector": [], "sparse_vector": [], "title": "Co-Design and Evaluation of an Intelligent Decision Support System for Stroke Rehabilitation Assessment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> i Badia"], "summary": "Clinical decision support systems have the potential to improve work flows of experts in practice (e.g. therapist's evidence-based rehabilitation assessment). However, the adoption of these systems is challenging, and the gains of these systems have not fully demonstrated yet. In this paper, we identified the needs of therapists to assess patient's functional abilities (e.g. alternative perspectives with quantitative information on patient's exercise motions). As a result, we co-designed and developed an intelligent decision support system that automatically identifies salient features of assessment using reinforcement learning to assess the quality of motion and generate patient-specific analysis. We evaluated this system with seven therapists using the dataset from 15 patients performing three exercises. The results show that therapists have higher usage intent on our system than a traditional system without patient-specific analysis ($p &lt; 0.05$). While presenting richer information ($p &lt; 0.10$), our system significantly reduces therapists' effort on assessment ($p &lt; 0.10$) and improves their agreement on assessment from 0.66 to 0.71 F1-scores ($p &lt; 0.01$). This work discusses the importance of human centered design and development of a machine learning-based decision support system that presents contextually relevant information and salient explanations on its prediction for better adoption in practice.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415227"}, {"primary_key": "2466625", "vector": [], "sparse_vector": [], "title": "Designing a Chatbot as a Mediator for Promoting Deep Self-Disclosure to a Real Mental Health Professional.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Chatbots are becoming increasingly popular. One promising application for chatbots is to elicit people's self-disclosure of their personal experiences, thoughts, and feelings. As receiving one's deep self-disclosure is critical for mental health professionals to understand people's mental status, chatbots show great potential in the mental health domain. However, there is a lack of research addressing if and how people self-disclose sensitive topics to a real mental health professional (MHP) through a chatbot. In this work, we designed, implemented and evaluated a chatbot that offered three chatting styles; we also conducted a study with 47 participants who were randomly assigned into three groups where each group experienced the chatbot's self-disclosure at varying levels respectively. After using the chatbot for a few weeks, participants were introduced to a MHP and were asked if they would like to share their self-disclosed content with the MHP. If they chose to share, the participants had the option of changing (adding, deleting, and editing) the content they self-disclosed to the chatbot. Comparing participants' self-disclosure data the week before and the week after sharing with the MHP, our results showed that, within each group, the depth of participants' self-disclosure to the chatbot remained after sharing with the MHP; participants exhibited deeper self-disclosure to the MHP through a more self-disclosing chatbot; further, through conversation log analysis, we found that some participants made different edits on their self-disclosed content before sharing it with the MHP. Participants' interview and survey feedback suggested an interaction between participants' trust in the chatbot and their trust in the MHP, which further explained participants' self-disclosure behavior.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392836"}, {"primary_key": "2466626", "vector": [], "sparse_vector": [], "title": "Patterns of Patient and Caregiver Mutual Support Connections in an Online Health Community.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online health communities offer the promise of support benefits to users, in particular because these communities enable users to find peers with similar experiences. Building mutually supportive connections between peers is a key motivation for using online health communities. However, a user's role in a community may influence the formation of peer connections. In this work, we study patterns of peer connections between two structural health roles: patient and non-professional caregiver. We examine user behavior in an online health community---CaringBridge.org---where finding peers is not explicitly supported. This context lets us use social network analysis methods to explore the growth of such connections in the wild and identify users' peer communication preferences. We investigated how connections between peers were initiated, finding that initiations are more likely between two authors who have the same role and who are close within the broader communication network. Relationships---patterns of repeated interactions---are also more likely to form and be more interactive when authors have the same role. Our results have implications for the design of systems supporting peer communication, e.g. peer-to-peer recommendation systems.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434184"}, {"primary_key": "2466627", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Script Sharing in GUI-based Programming-by-Demonstration Systems.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An important concern in end user development (EUD) is accidentally embedding personal information in program artifacts when sharing them. This issue is particularly important in GUI-based programming-by-demonstration (PBD) systems due to the lack of direct developer control of script contents. Prior studies reported that these privacy concerns were the main barrier to script sharing in EUD. We present a new approach that can identify and obfuscate the potential personal information in GUI-based PBD scripts based on the uniqueness of information entries with respect to the corresponding app GUI context. Compared with the prior approaches, ours supports broader types of personal information beyond explicitly pre-specified ones, requires minimal user effort, addresses the threat of re-identification attacks, and can work with third-party apps from any task domain. Our approach also recovers obfuscated fields locally on the script consumer's side to preserve the shared scripts' transparency, readability, robustness, and generalizability. Our evaluation shows that our approach (1) accurately identifies the potential personal information in scripts across different apps in diverse task domains; (2) allows end-user developers to feel comfortable sharing their own scripts; and (3) enables script consumers to understand the operation of shared scripts despite the obfuscated fields.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392869"}, {"primary_key": "2466628", "vector": [], "sparse_vector": [], "title": "3 Stars on Yelp, 4 Stars on Google Maps: A Cross-Platform Examination of Restaurant Ratings.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Even though a restaurant may receive different ratings across review platforms, people often see only one rating during a local search (e.g. 'best burgers near me'). In this paper, we examine the differences in ratings between two commonly used review platforms-Google Maps and Yelp. We found that restaurant ratings on Google Maps are, on average, 0.7 stars higher than those on Yelp, with the increase being driven in large part by higher ratings for chain restaurants on Google Maps. We also found extensive diversity i¬¬n top-ranked restaurants by geographic region across platforms. For example, for a given metropolitan area, there exists little overlap in its top ten lists of restaurants on Google Maps and Yelp. Our results problematize the use of a single review platform in local search and have implications for end users of ratings and local search technologies. We outline concrete design recommendations to improve communication of restaurant evaluation and discuss the potential causes for the divergence we observed.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432953"}, {"primary_key": "2466629", "vector": [], "sparse_vector": [], "title": "Studying Politeness across Cultures using English Twitter and Mandarin Weibo.", "authors": ["Mingyang Li", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modeling politeness across cultures helps to improve intercultural communication by uncovering what is considered appropriate and polite. We study the linguistic features associated with politeness across American English and Mandarin Chinese. First, we annotate 5,300 Twitter posts from the United States (US) and 5,300 Sina Weibo posts from China for politeness scores. Next, we develop an English and Chinese politeness feature set, 'PoliteLex'. Combining it with validated psycholinguistic dictionaries, we study the correlations between linguistic features and perceived politeness across cultures. We find that on Mandarin Weibo, future-focusing conversations, identifying with a group affiliation, and gratitude are considered more polite compared to English Twitter. Death-related taboo topics, use of pronouns (with the exception of honorifics), and informal language are associated with higher impoliteness on Mandarin Weibo than on English Twitter. Finally, we build language-based machine learning models to predict politeness with an F1 score of 0.886 on Mandarin Weibo and 0.774 on English Twitter.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415190"}, {"primary_key": "2466631", "vector": [], "sparse_vector": [], "title": "How Developers Talk About Personal Data and What It Means for User Privacy: A Case Study of a Developer Forum on Reddit.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While online developer forums are major resources of knowledge for application developers, their roles in promoting better privacy practices remain underexplored. In this paper, we conducted a qualitative analysis of a sample of 207 threads (4772 unique posts) mentioning different forms of personal data from the /r/androiddev forum on Reddit. We started with bottom-up open coding on the sampled posts to develop a typology of discussions about personal data use and conducted follow-up analyses to understand what types of posts elicited in-depth discussions on privacy issues or mentioned risky data practices. Our results show that Android developers rarely discussed privacy concerns when talking about a specific app design or implementation problem, but often had active discussions around privacy when stimulated by certain external events representing new privacy-enhancing restrictions from the Android operating system, app store policies, or privacy laws. Developers often felt these restrictions could cause considerable cost yet fail to generate any compelling benefit for themselves. Given these results, we present a set of suggestions for Android OS and the app store to design more effective methods to enhance privacy, and for developer forums(e.g., /r/androiddev) to encourage more in-depth privacy discussions and nudge developers to think more about privacy.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432919"}, {"primary_key": "2466632", "vector": [], "sparse_vector": [], "title": "Spontaneous, Yet Studious: Esports Commentators&apos; Live Performance and Self-Presentation Practices.", "authors": ["Ling<PERSON> Li", "Jirassaya Uttarapong", "<PERSON>", "<PERSON><PERSON>"], "summary": "Esports commentating, the practice of reporting, explaining, and elaborating live competitive gameplay to spectators, is a centerpiece of esports as a rapidly growing spectator sport and an essential component of today's growing gaming/live streaming ecosystem. In particular, esports commentators face three unique challenges: the preparation for conducting real time commentating on highly dynamic esports games; the complexity of in-the-moment decisions during the game's broadcast; and the balance between personal self-presentation and professional content presentation. Yet this emerging and novel sociotechnical practice and how these challenges affect esports commentators' live performance and self-presentation practices has received relatively little research attention in HCI and CSCW. In this paper, we endeavor to address these limitations by empirically analyzing 19 esports commentators' sociotechnical practices. Our findings highlight the complex interaction dynamics involved in esports commentating as well as the importance of professionalism and social presence in esports commentators' self-presentation. In order to execute seamless and spontaneous commentary, commentators must be studious and engage in much prior research and have a clear sense of self. We contribute to the growing CSCW literature on esports commentating practices by revealing esports commentators' unique decision making process presenting information and their self-image.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415174"}, {"primary_key": "2466633", "vector": [], "sparse_vector": [], "title": "Successful Online Socialization: Lessons from the Wikipedia Education Program.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Attracting and retaining newcomers is critical and challenging for online production communities such as Wikipedia, both because volunteers need specialized training and are likely to leave before being integrated into the community. In response to these challenges, the Wikimedia Foundation started the Wiki Education Project (Wiki Ed), an online program in which college students edit Wikipedia articles as class assignments. The Wiki Ed program incorporates many components of institutional socialization, a process many conventional organizations successfully use to integrate new employees through formalized on-boarding practices. Research has not adequately investigated whether Wiki Ed and similar programs are effective ways to integrate volunteers in online communities, and, if so, the mechanisms involved. This paper evaluates the Wiki Ed program by comparing 16,819 student editors in 770 Wiki Ed classes with new editors who joined Wikipedia in the conventional way. The evaluation shows that the Wiki Ed students did more work, improved articles more, and were more committed to Wikipedia. For example, compared to new editors who joined Wikipedia in the conventional way they were twice as likely to still be editing Wikipedia a year after their Wiki Ed class was finished. Further, students in classrooms that encouraged joint activity, a key component of institutional socialization, produced better quality work than those in classrooms where students worked independently. These findings are consistent with an interpretation that the Wiki Ed program was successful because it incorporated elements of institutionalized socialization.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392857"}, {"primary_key": "2466634", "vector": [], "sparse_vector": [], "title": "Leveraging Peer Support for Mature Immigrants Learning to Write in Informal Contexts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For adult newcomers to countries such as Canada, learning language is more than an academic task. Language proficiency is their gateway to long-term economic and social stability, but limited access to resources contributes to systemic inequities which disproportionately place immigrants at socioeconomic disadvantages. Many new immigrants rely heavily on informal peer-networks to pursue avenues of success within an unfamiliar and inadequate system. To explore how we could leverage such a peer-based approach to meet their needs for feedback and support when learning to write in English, we deployed a peer-based writing app with 16 participants. Post-deployment focus groups and analysis of writing artifacts reveal that the design of writing support tools should present transparent feedback from both peers and automated sources, foster community through semi-structured discussions, incorporate guided review, and scaffold affective development. We discuss how incorporating these elements into the design of community learning platforms can address the language literacy needs of diverse immigrant learners and foster more positive experiences for newcomers as they negotiate their evolving identities.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415177"}, {"primary_key": "2466635", "vector": [], "sparse_vector": [], "title": "Collecting the Public Perception of AI and Robot Rights.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Cha"], "summary": "Whether to give rights to artificial intelligence (AI) and robots has been a sensitive topic since the European Parliament proposed advanced robots could be granted \"electronic personalities.\" Numerous scholars who favor or disfavor its feasibility have participated in the debate. This paper presents an experiment (N=1270) that 1) collects online users' first impressions of 11 possible rights that could be granted to autonomous electronic agents of the future and 2) examines whether debunking common misconceptions on the proposal modifies one's stance toward the issue. The results indicate that even though online users mainly disfavor AI and robot rights, they are supportive of protecting electronic agents from cruelty (i.e., favor the right against cruel treatment). Furthermore, people's perceptions became more positive when given information about rights-bearing non-human entities or myth-refuting statements. The style used to introduce AI and robot rights significantly affected how the participants perceived the proposal, similar to the way metaphors function in creating laws. For robustness, we repeated the experiment over a more representative sample of U.S. residents (N=164) and found that perceptions gathered from online users and those by the general population are similar.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415206"}, {"primary_key": "2466636", "vector": [], "sparse_vector": [], "title": "Go&amp;Grow: An Evaluation of a Pervasive Social Exergame for Caregivers of Loved Ones with <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Caregivers of persons with dementia (PWD) experience higher rates of stress, social isolation, and poor mental and physical health compared to non-caregiving populations. There is a vital need for engaging, sustainable, and scalable resources to support social, physical, and emotional wellbeing amongst caregivers of PWD. To explore this open design space, we designed and conducted a 6-week mixed-method evaluation of Go&amp;Grow, a pervasive social exergame in which flowers grow as users increase physical activity and interact with other caregivers of PWD. Our findings showed that using Go&amp;Grow helped participants relieve stress, increase physical activity, and develop empathy for and patience towards the loved one with dementia that they cared for. At the same time, tension arose as some caregivers desired to learn about the life challenges that Go&amp;Grow users faced, while others hesitated to share such content. We discuss our findings and recommendations for future technology that promotes caregivers? time for themselves, understanding of PWD, and connections with other caregivers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415222"}, {"primary_key": "2466638", "vector": [], "sparse_vector": [], "title": "Tensions between Access and Control in Makerspaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Makerspaces have complex access control requirements and are increasingly protected through digital access control mechanisms (e.g., keycards, transponders). However, it remains unclear how space administrators craft access control policies, how existing technical infrastructures support and fall short of access needs, and how these access control policies impact end-users in a makerspace. We bridge this gap through a mixed-methods, multi-stakeholder study. Specifically, we conducted 16 semi-structured interviews with makerspace administrators across the U.S. along with a survey of 48 makerspace end-users. We found four factors influenced administrators' construction of access control policies: balancing safety versus access; logistics; prior experience; and, the politics of funding. Moreover, administrators often made situational exceptions to their policies: e.g., during demand spikes, to maintain a good relationship with their staff, and if they trusted the user(s) requesting an exception. Conversely, users expressed frustration with the static nature of access control policies, wishing for negotiability and for social nuance to be factored into access decisions. The upshot is that existing mechanisms for access control in makerspaces are often inappropriately static and socially unaware.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432914"}, {"primary_key": "2466639", "vector": [], "sparse_vector": [], "title": "Opportunistic Collective Experiences: Identifying Shared Situations and Structuring Shared Activities at Distance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite many available social technologies for connecting at a distance, we don't always find opportunities to actively engage in shared experiences and activities with friends and loved ones, even though this kind of interaction is associated with increased social closeness. To better support active engagement in shared experiences and activities while also making it convenient to find opportunities for interacting in this way, our work explores the design of Opportunistic Collective Experiences (OCEs), or social experiences powered by computer programs that identify opportune moments when users share situations across distance and structure shared activities in those situations. To support interacting with, programming, and executing OCEs, we developed Cerebro, a computational platform that consists of a mobile app that supports users? social interaction, an API for expressing the situations and activities that make up the interactional opportunity, and an opportunistic execution engine that checks for interactional opportunities and executes them when possible. Through a 20 day deployment study tested with groups of geographically-distributed college alumni (N=21), we found that OCEs promoted opportunities for active engagement; facilitated interactions that were socially connecting by structuring ways to engage in shared experiences and activities; and made actively engaging easier by identifying situations appropriate for interacting and structuring how to engage in activities in these situations. We contribute to CSCW (1) a novel interaction that facilitates engaging in shared experiences and activities at distance during coincidental moments; and (2) the design of systems to interact with, program, and execute these kinds of interactions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434178"}, {"primary_key": "2466640", "vector": [], "sparse_vector": [], "title": "&quot;Learning for the Rise of China&quot;: Exploring Uses and Gratifications of State-Owned Online Platform.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In January 2019, the Chinese Communist Party launched the online platform XueXi QiangGuo, which translates into \"Learning for the Rise of China.\" Within two months, XueXi became the top-downloaded item of the month on Apple's App Store in China. In response, we conducted an interview study with 28 active XueXi users to investigate their uses and gratifications of this state-owned online platform. Our results reveal seven key motivations: compliance, self-status seeking, general information seeking, job support, entertainment, patriotism, and learning. This state-owned platform introduced a new model for official information dissemination and political communication through direct surveillance and monitoring, leveraging and fostering emotional attachment, and offering heterogeneous apolitical content. We discuss the intended and unintended ramifications of these components, highlighting the importance of future CSCW research to critically engage with pluralist political narratives situated in varied societies, especially non-Western democracies.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392835"}, {"primary_key": "2466641", "vector": [], "sparse_vector": [], "title": "Emotional Amplification During Live-Streaming: Evidence from Comments During and After News Events.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Live streaming services allow people to concurrently consume and comment on media events with other people in real time. <PERSON><PERSON><PERSON>'s theory of \"collective effervescence\" suggests that face-to-face encounters in ritual events conjure emotional arousal, so people often feel happier and more excited while watching events like the Super Bowl with family and friends through the television than if they were alone. Does a stronger emotional intensity also occur in live streaming? Using a large-scale dataset of comments posted to news and media events on YouTube, we address this question by examining emotional intensity in live comments versus those produced retrospectively. Results reveal that live comments are overall more emotionally intense than retrospective comments across all temporal periods and all event types examined. Findings support the emotional amplification hypothesis and provide preliminary evidence for shared attention theory in explaining the amplification effect. These findings have important implications for live streaming platforms to optimize resources for content moderation and to improve psychological well-being for content moderators, and more broadly as society grapples with using technology to stay connected during social distancing required by the COVID-19 pandemic.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392853"}, {"primary_key": "2466642", "vector": [], "sparse_vector": [], "title": "Stuck in the middle with you: The Transaction Costs of Corporate Employees Hiring Freelancers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Corporations are increasingly empowering employees to hire on-demand workers via freelance platforms. We interviewed full-time employees of a global technology company who hired freelancers as part of their job responsibilities. While there has been prior work describing freelancers' perspectives there has been little research on those that hire them, the \"clients\", especially in the corporate context. We found that while freelance platforms reduce many administrative burdens, there are number of conditions in which using freelance platforms in a corporate context creates high transaction costs and power asymmetries that make it difficult for clients to negotiate work rights and responsibilities. This leads corporate employee clients to feel \"stuck in the middle\" between their employer, the platform, and the freelancer. Ultimately, these transactions costs are a potential barrier to wider adoption. If corporations want to leverage the value of the freelance economy then better guardrails, guidelines, and perhaps even creative technology solutions will be needed.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392842"}, {"primary_key": "2466643", "vector": [], "sparse_vector": [], "title": "Unpacking Sharing in the Peer-to-Peer Economy: The Impact of Shared Needs and Backgrounds on Ride-Sharing.", "authors": ["Ning F. Ma", "<PERSON>"], "summary": "In recent years, the peer-to-peer economy has grown exponentially, particularly in the ride-sharing sector. This growth has been accompanied by a muddying between the sharing and gig economy, and it has become unclear when an activity is sharing a resource vs. providing a service. To unpack this difference, we studied two successful carpooling groups (university students traveling home and commuting among professionals), which we contrast with previous literature on ride-hailing apps (e.g., Uber). The two communities that we studied differ in that: professionals, had more routine ride-sharing needs based on their commute; and students, arranged rides to return home for school breaks or long weekends. We detail how common needs and backgrounds impacted how carpoolers treated each other. Leveraging these findings, we outline design paths for both the sharing and gig economies to better realize the ideas of the sharing economy.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392865"}, {"primary_key": "2466644", "vector": [], "sparse_vector": [], "title": "Social App Accessibility for Deaf Signers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social media platforms support the sharing of written text, video, and audio. All of these formats may be inaccessible to people who are deaf or hard of hearing (DHH), particularly those who primarily communicate via sign language, people who we call Deaf signers. We study how Deaf signers engage with social platforms, focusing on how they share content and the barriers they face. We employ a mixed-methods approach involving seven in-depth interviews and a survey of a larger population (n = 60). We find that Deaf signers share the most in written English, despite their desire to share in sign language. We further identify key areas of difficulty in consuming content (e.g., lack of captions for spoken content in videos) and producing content (e.g., captioning signed videos, signing into a phone camera) on social media platforms. Our results both provide novel insights into social media use by Deaf signers and reinforce prior findings on DHH communication more generally, while revealing potential ways to make social media platforms more accessible to Deaf signers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415196"}, {"primary_key": "2466645", "vector": [], "sparse_vector": [], "title": "Not Another Medication Adherence App: Critical Reflections on Addressing Public HIV-related Stigma Through Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>ja Zdziarsk<PERSON>", "<PERSON><PERSON><PERSON> Min", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "People living with HIV experience a high level of stigma in our society. Public HIV-related stigma often leads to anxiety and depression and hinders access to social support and proper medical care. Technologies for HIV, however, have been mainly designed for treatment management and medication adherence rather than for helping people cope with public HIV-related stigma specifically. Drawing on empirical data obtained from semi-structured interviews and design activities with eight social workers and 29 people living with HIV, we unpack the ways in which needs for privacy and trust, intimacy, and social support create tensions around key coping strategies. Reflecting on these tensions, we present design implications and opportunities to empower people living with HIV to cope with public HIV-related stigma at the individual level.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434171"}, {"primary_key": "2466646", "vector": [], "sparse_vector": [], "title": "&quot;Talking without a Voice&quot;: Understanding Non-verbal Communication in Social Virtual Reality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Exploring communication dynamics in digital social spaces such as massively multiplayer online games and 2D/3D virtual worlds has been a long standing concern in HCI and CSCW. As online social spaces evolve towards more natural embodied interaction, it is important to explore how non-verbal communication can be supported in more nuanced ways in these spaces and introduce new social interaction consequences. In this paper we especially focus on understanding novel non-verbal communication in social virtual reality (VR). We report findings of two empirical studies. Study 1 collected observational data to explore the types of non-verbal interactions being used naturally in social VR. Study 2 was an interview study (N=30) that investigated people's perceptions of non-verbal communication in social VR as well as the resulting interaction outcomes. This study helps address the limitations in prior literature on non-verbal communication dynamics in online social spaces. Our findings on what makes non-verbal communication in social VR unique and socially desirable extend our current understandings of the role of non-verbal communication in social interaction. We also highlight potential design implications that aim at better supporting non-verbal communication in social VR.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415246"}, {"primary_key": "2466647", "vector": [], "sparse_vector": [], "title": "Using the Crowd to Prevent Harmful AI Behavior.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hiram Temple", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To prevent harmful AI behavior, people need to specify constraints that forbid undesirable actions. Unfortunately, this is a complex task, since writing rules that distinguish harmful from non-harmful actions tends to be quite difficult in real-world situations. Therefore, such decisions have historically been made by a small group of powerful AI companies and developers, with limited community input. In this paper, we study how to enable a crowd of non-AI experts to work together to communicate high-quality, reliable constraints to AI systems. We first focus on understanding how humans reason about temporal dynamics in the context of AI behavior, finding through experiments on a novel game-based testbed that participants tend to adopt a long-term notion of harm, even in uncertain situations that do not affect them directly. Building off of this insight, we explore task design for long-term constraint specification, developing new filtering approaches and new methods of promoting user reflection. Next, we develop a novel rule-based interface which allows people to craft rules in an accessible fashion without programming knowledge. We test our approaches on a real-world AI problem in the domain of education, and find that our new filtering mechanisms and interfaces significantly improve constraint quality and human efficiency. We also demonstrate how these systems can be applied to other real-world AI problems (e.g. in social networks).", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415168"}, {"primary_key": "2466648", "vector": [], "sparse_vector": [], "title": "The Situated, Relational, and Evolving Nature of Epilepsy Diagnosis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An understanding of medical diagnosis as it is practiced is essential for those seeking to support it using intelligent systems. Through the case of epilepsy, we show that diagnosis is a situated, relational, and evolving process that accounts for information well beyond the patient's physiology, even for physiological phenomena like seizures. Through observations and interviews with neurologists, we show that the meaning of brainwaves and other physiological data depends upon a range of patient-specific and contextual factors, such as age, comorbidities, and mealtimes. Further, we show that diagnosis is partly determined by social factors such as the activities of caregivers and other clinicians, and environmental factors such as faulty electrical wiring. Additionally, diagnostic classifications can evolve in response to new information: events that were once considered seizures can be reinterpreted as clinically irrelevant and vice versa. We contribute a broader sociotechnical perspective to literature on intelligent decision making in healthcare and discuss implications for the design of decision support systems that can better support the work of medical diagnosis.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432916"}, {"primary_key": "2466649", "vector": [], "sparse_vector": [], "title": "Wikipedia Edit-a-thons as Sites of Public Pedagogy.", "authors": ["<PERSON> March", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wikipedia edit-a-thon events provide a targeted approach toward incorporating new knowledge into the online encyclopedia while also offering pathways toward new editor participation. Through the analysis of interviews with 13 edit-a-thon facilitators, however, we find motivations for running edit-a-thons extend far beyond adding content and editors. In this paper, we uncover how a range of personal and institutional values inspire these event facilitators toward fulfilling broader goals including fostering information literacy and establishing community relationships outside of Wikipedia. Along with reporting motivations, values, and goals, we also describe strategies facilitators adopt in their practice. Next, we discuss challenges faced by facilitators as they organize edit-a-thons. We situate our findings within two complementary theoretical lenses-information ecologies and public pedagogy to guide our interpretation. Finally, we suggest new ways in which edit-a-thons, as well as similar peer production events and communities, can be understood, studied, and evaluated.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415171"}, {"primary_key": "2466651", "vector": [], "sparse_vector": [], "title": "Sochiatrist: Signals of Affect in Messaging Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sachin <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Messaging is a common mode of communication, with conversations written informally between individuals. Interpreting emotional affect from messaging data can lead to a powerful form of reflection or act as a support for clinical therapy. Existing analysis techniques for social media commonly use LIWC and VADER for automated sentiment estimation. We correlate LIWC, VADER, and ratings from human reviewers with affect scores from 25 participants. We explore differences in how and when each technique is successful. Results show that human review does better than VADER, the best automated technique, when humans are judging positive affect ($r_s=0.45$ correlation when confident, $r_s=0.30$ overall). Surprisingly, human reviewers only do slightly better than VADER when judging negative affect ($r_s=0.38$ correlation when confident, $r_s=0.29$ overall). Compared to prior literature, VADER correlates more closely with PANAS scores for private messaging than public social media. Our results indicate that while any technique that serves as a proxy for PANAS scores has moderate correlation at best, there are some areas to improve the automated techniques by better considering context and timing in conversations.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415182"}, {"primary_key": "2466652", "vector": [], "sparse_vector": [], "title": "Hate begets Hate: A Temporal Study of Hate Speech.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the ongoing debate on 'freedom of speech' vs. 'hate speech,' there is an urgent need to carefully understand the consequences of the inevitable culmination of the two, i.e., 'freedom of hate speech' over time. An ideal scenario to understand this would be to observe the effects of hate speech in an (almost) unrestricted environment. Hence, we perform the first temporal analysis of hate speech on Gab.com, a social media site with very loose moderation policy. We first generate temporal snapshots of Gab from millions of posts and users. Using these temporal snapshots, we compute an activity vector based on DeGroot model to identify hateful users. The amount of hate speech in Gab is steadily increasing and the new users are becoming hateful at an increased and faster rate. Further, our analysis analysis reveals that the hate users are occupying the prominent positions in the Gab network. Also, the language used by the community as a whole seem to correlate more with that of the hateful users as compared to the non-hateful ones. We discuss how, many crucial design questions in CSCW open up from our work.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415163"}, {"primary_key": "2466653", "vector": [], "sparse_vector": [], "title": "The Impact of Thinking-Aloud on Usability Inspection.", "authors": ["<PERSON>", "<PERSON>", "Alastair Irons"], "summary": "This study compared the results of a usability inspection conducted under two separate conditions: An explicit concurrent think-aloud that required explanations and silent working. 12 student analysts inspected two travel websites thinking-aloud and working in silence to produce a set of problem predictions. Overall, the silent working condition produced more initial predictions, but the think-aloud condition yielded a greater proportion of accurate predictions as revealed by falsification testing. The analysts used a range of problem discovery methods with system searching being favoured by the silent working condition and the more active, goal playing discovery method in the think-aloud condition. Thinking-aloud was also associated with a broader spread of knowledge resources.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397876"}, {"primary_key": "2466654", "vector": [], "sparse_vector": [], "title": "Intersectional AI: A Study of How Information Science Students Think about Ethics and Their Impact.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Recent literature has demonstrated the limited and, in some instances, waning role of ethical training in computing classes in the US. The capacity for artificial intelligence (AI) to be inequitable or harmful is well documented, yet it's an issue that continues to lack apparent urgency or effective mitigation. The question we raise in this paper is how to prepare future generations to recognize and grapple with the ethical concerns of a range of issues plaguing AI, particularly when they are combined with surveillance technologies in ways that have grave implications for social participation and restriction?from risk assessment and bail assignment in criminal justice, to public benefits distribution and access to housing and other critical resources that enable security and success within society. The US is a mecca of information and computer science (IS and CS) learning for Asian students whose experiences as minorities renders them familiar with, and vulnerable to, the societal bias that feeds AI bias. Our goal was to better understand how students who are being educated to design AI systems think about these issues, and in particular, their sensitivity to intersectional considerations that heighten risk for vulnerable groups. In this paper we report on findings from qualitative interviews with 20 graduate students, 11 from an AI class and 9 from a Data Mining class. We find that students are not predisposed to think deeply about the implications of AI design for the privacy and well-being of others unless explicitly encouraged to do so. When they do, their thinking is focused through the lens of personal identity and experience, but their reflections tend to center on bias, an intrinsic feature of design, rather than on fairness, an outcome that requires them to imagine the consequences of AI. While they are, in fact, equipped to think about fairness when prompted by discussion and by design exercises that explicitly invite consideration of intersectionality and structural inequalities, many need help to do this empathy 'work.' Notably, the students who more frequently reflect on intersectional problems related to bias and fairness are also more likely to consider the connection between model attributes and bias, and the interaction with context. Our findings suggest that experience with identity-based vulnerability promotes more analytically complex thinking about AI, lending further support to the argument that identity-related ethics should be integrated into IS and CS curriculums, rather than positioned as a stand-alone course.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415218"}, {"primary_key": "2466655", "vector": [], "sparse_vector": [], "title": "Rare, but Valuable: Understanding Data-centered Talk in News Website Comment Sections.", "authors": ["<PERSON>", "Lu Sun", "<PERSON><PERSON>", "<PERSON>"], "summary": "News websites can facilitate global discussions about civic issues, but the financial cost and burden of moderating these forums has forced many to disable their commenting systems. In this paper, we consider the role that data visualizations play in online discussion around a civic issue, through an analysis of how people talk about climate change data in the comment threads at three news websites (i.e., Breitbart news, the Guardian, the New York Times). We find that out of 6,525 comments, only 2.4% reference data visualizations in the articles. While rare, the paper presents illustrative examples of how people refer to data---their collection, analysis, and visual representation---to engage with an article's narrative. Using text classification techniques we identify several features related to the content of comments that contain data-centered talk, such as article cosine similarity, hyperlinks, and comparison terms. Finally, we discuss potential ways that newsrooms might apply this analysis to promote data literacy, data science, and to foster community around shared experiences.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415245"}, {"primary_key": "2466656", "vector": [], "sparse_vector": [], "title": "Collaborative Tabletops for Blind People: The Effect of Auditory Design on Workspace Awareness.", "authors": ["<PERSON>", "Sofia Reis", "<PERSON>", "<PERSON>"], "summary": "Interactive tabletops offer unique collaborative features, particularly their size, geometry, orientation and, more importantly, the ability to support multi-user interaction. Although previous efforts were made to make interactive tabletops accessible to blind people, the potential to use them in collaborative activities remains unexplored. In this paper, we present the design and implementation of a multi-user auditory display for interactive tabletops, supporting three feedback modes that vary on how much information about the partners' actions is conveyed. We conducted a user study with ten blind people to assess the effect of feedback modes on workspace awareness and task performance. Furthermore, we analyze the type of awareness information exchanged and the emergent collaboration strategies. Finally, we provide implications for the design of future tabletop collaborative tools for blind users.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427325"}, {"primary_key": "2466657", "vector": [], "sparse_vector": [], "title": "A Framework for Understanding the Relationship between Social Media Discourse and Mental Health.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Over 35% of the world's population uses social media. Platforms like Facebook, Twitter, and Instagram have radically influenced the way individuals interact and communicate. These platforms facilitate both public and private communication with strangers and friends alike, providing rich insight into an individual's personality, health, and wellbeing. To date, many researchers have employed a variety of methods for extracting mental health-centric features from digital text communication (DTC) data, including natural language processing, social network analysis, and extraction of temporal discourse patterns. However, none have explored a hierarchical framework for extracting features from private messages with the goal of unifying approaches across methodological domains. Furthermore, while analyses of large, public corpora abound in existing literature, limited work has been done to explore the relationship between of private textual communications, personality traits, and symptoms of mental illness. We present a framework for constructing rich feature spaces from digital text communications. We then demonstrate the efficacy of our framework by applying it to a dataset of private Facebook messages in a college student population (N=103). Our results reveal key individual differences in temporal and relational behaviors, as well as language usage in relation to validated measures of trait-level anxiety, loneliness, and personality. This work represents a critical step forward in linking features of private social media messages to validated measures of mental health, wellbeing, and personality.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415215"}, {"primary_key": "2466658", "vector": [], "sparse_vector": [], "title": "Image Wishlist: Context and Images in Commons-Based Peer Production Communities.", "authors": ["<PERSON>", "<PERSON>"], "summary": "One promise of commons-based peer production systems is that they provide content (e.g., text, images, sounds, lines of code) for anyone to use, edit, and distribute. However, when some kinds of content are stripped of context and made available for reuse, there are several knock-on effects. In this paper, we discuss how these knock-on effects can complicate the illustration of Wikipedia's encyclopedic articles. We focus on the particularly difficult case of illustrating articles about female anatomy and women's health. Through our analysis, we demonstrate several barriers to illustrating these kinds of articles, including a lack of representative images on Wikimedia Commons, complex issues of consent, and editorial disagreements about the intended audiences for such images. We argue that these barriers are, in part, the unanticipated effects of decontextualization and subsequent recontextualization as images are removed from their original contexts and made available in Wikimedia Commons. Drawing on prior work in language acquisition and archival studies, we critique the design of commons-based peer production systems and suggest ways in which they might be re-designed to account better for the nuances of context. Finally, we raise questions about the costs of recontextualization in peer production systems in general.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415249"}, {"primary_key": "2466659", "vector": [], "sparse_vector": [], "title": "The Illusion of Choice in Discussing Cybersecurity Safeguards Between Older Adults with Mild Cognitive Impairment and Their Caregivers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zoya Trendafilova"], "summary": "Although continual online interaction is beneficial for an older adult with mild cognitive impairment, cybersecurity risks can become more pronounced. Prior work has touted the benefit of shared decision-making between care recipients and caregivers who may want to instill cybersecurity safeguards, particularly in the area of online safety and security. In this study we investigated the current online safety and security decision-making practices of care recipients with mild cognitive impairment and their spousal caregivers. We identified a gap between optative and actual decision-making: whereas couples expressed their desire to engage in shared decision-making, the actuality was most caregivers were compelled to take sole action. We further determined that shared-decision making was not feasible as there was a lack of suitable safeguarding options along a spectrum of care for the couple to choose from. We relate these findings to prior work that highlights similar challenges and discuss how there needs to be more offered than simply an illusion of choice.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415235"}, {"primary_key": "2466660", "vector": [], "sparse_vector": [], "title": "Supporting Storytelling With Evidence in Holistic Review Processes: A Participatory Design Approach.", "authors": ["<PERSON>", "Tya <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Review processes involve complex and often subjective decision-making tasks in which individual reviewers must read and rate submissions, such as a college application, along many relevant dimensions and typically with a rubric in mind. A common part of the work is committee review, where individual reviewers meet to discuss the merits of a particular submission in order to recommend an accept or reject decision. Prior work indicates that visualization and sensemaking support may be beneficial in such processes where reviewers must present the \"story\" of the applicant under question. We conducted a series of participatory design workshops with reviewers in the domain of holistic college admissions to better understand the challenges and opportunities regarding storytelling. Based on these workshops, we contribute a characterization for how reviewers in this domain construct visual stories, we provide guidance for designing for evidence capture and storytelling, and we draw parallels and distinctions between this domain and other reviewing domains.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392870"}, {"primary_key": "2466662", "vector": [], "sparse_vector": [], "title": "Data Analysts and Their Software Practices: A Profile of the Sabermetrics Community and Beyond.", "authors": ["<PERSON>", "<PERSON>-Hill", "<PERSON>"], "summary": "For modern data analytics, practices from software development are increasingly necessary to manage data, but they must be incorporated alongside other statistical and scientific skills. Therefore, we ask: how does a community recontextualize software development through the unique pressures of their work? To answer this, we explore the analytic community around baseball, or sabermetrics. To discover software development's place in the search for robust statistical insight in sports, we interview 10 participants in the sabermetric community and survey over 120 more data analysts, both in baseball and not. We explore how their work lives at the intersection of science and entertainment, and as a consequence, baseball data serves as an accessible yet deep subject to practice analytic skills. Software development exists within an iterative research process that cycles between defining rigorous statistical methods and preserving the flexibility to chase interesting problems. In this question-driven process, members of the community inhabit several overlapping roles of intentional work, in which software development can become the priority to support research and statistical infrastructure, and we discuss the way that the community can foster the balance of these skills.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392859"}, {"primary_key": "2466663", "vector": [], "sparse_vector": [], "title": "Understanding the Challenges for Bangladeshi Women to Participate in #MeToo Movement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A series of events in October 2017 led to the initiation of an unprecedented global feminist movement over various social media platforms, where using the hashtag #MeToo (or some variants of it), women across the world publicly shared their untold stories of being sexually harassed. We conducted an anonymous online survey (n=180) and an interview study (n=30) to understand the participation of Bangladeshi women in this movement. Our study concurs that while Bangladeshi women, who are regular users of social media, supported the spirit of this movement; did not participate in it, even though they had many bitter experiences. Our analysis shows that their non-participation was largely influenced by a cultural difference, patriarchy, perceived futility and lack of hope, and a reliance on alternatives. We discuss how our findings of women's use of technology platforms, which is conditioned and limited by male-dominated and conservative Bangladeshi society, relates to the broader issues in feminism that the GROUP community is interested in.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375195"}, {"primary_key": "2466664", "vector": [], "sparse_vector": [], "title": "OpenUIDL, A User Interface Description Language for Runtime Omni-Channel User Interfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We extend the concept of cross-device user interfaces into the new, more general, concept of omni-channel user interfaces to better reflect the technological variety offered for developing multi-target user interfaces for interactive applications. We present a model-based approach for developing runtime omni-channel user interfaces for multi-target applications, which consists of: (1) OpenUIDL, a user interface description language for describing omni-channel user interfaces with its semantics by a meta-model and its syntax based on JSON, (2) the definition of a step-wise approach for producing runtime interactive applications based onOpenUIDLwith integration into the development life cycle, (3) the development of a cloud-based, OpenUIDL compliant, Interactive Development Environment that supports the application and the enactment of the step-wise approach and its illustration on several multi-target user interfaces.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397874"}, {"primary_key": "2466665", "vector": [], "sparse_vector": [], "title": "On the Desiderata for Online Altruism: Nudging for Equitable Donations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Asia J. Biega", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online donation platforms help equalize access to opportunity and funding in cases where inequalities exist. In the context of public school education in the United States, for instance, financial inequalities have been shown to be reflected in the educational system, since schools are primarily funded through local property taxes. In response, private charitable donation platforms such as DonorsChoose.org have emerged seeking to alleviate systemic inequalities. Yet, the question remains of how effective these platforms are in redressing existing funding inequalities across school districts. Our analysis of donation data from DonorsChoose shows that such platforms may in fact be ineffective in mitigating existing inequalities or may even exacerbate them. In this paper, we explore how online educational charities could direct more funding towards more impoverished schools without compromising their donors' freedom of choice with respect to donation targets. Seeking to answer this question, we draw on the line of work on choice architectures in behavioral economics and pose a novel research question on the impact of interface design on equity in socio-technical systems. Through controlled experiments, we demonstrate how simple interface design interventions - such as modifying default rankings or displaying additional information about schools - might lead to changes in donation distributions helping platforms direct more funding towards schools in need. Going beyond online educational charities, we hope that our work will bring attention to the role of interface design nudges in the social requirements of online altruism.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415197"}, {"primary_key": "2466666", "vector": [], "sparse_vector": [], "title": "Designing Digital Safe Spaces for Peer Support and Connectivity in Patriarchal Contexts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores the opportunities and challenges in designing peer-support mechanisms for low-income,low-literate women in Pakistan, a patriarchal and religious context where women's movements, social relationsand access to digital technologies are restricted. Through a qualitative, empirical study with 21 participantswe examine the cultural and patriarchal framework where shame and fear of defamation restrict the seekingof support for personal narratives around taboo subjects like abortion, sexual harassment, rape and domesticabuse. Based on our qualitative data we also conduct a second qualitative study using a technology probe with15 low-income, low-literate women to explore the specific design of peer-support technologies for supportseeking and the sharing of sensitive and taboo narratives in a deeply patriarchal society. The design concernsraised by our participants regarding privacy, anonymity and safety provide CSCW researchers with valuableguidelines about designing for social connections and support for vulnerable populations within a particularcontext.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415217"}, {"primary_key": "2466667", "vector": [], "sparse_vector": [], "title": "Data Integration as Coordination: The Articulation of Data Work in an Ocean Science Collaboration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent CSCW research on the collaborative design and development of research infrastructures for the natural sciences has increasingly focused on the challenges of open data sharing. This qualitative study describes and analyzes how multidisciplinary, geographically distributed ocean scientists are integrating highly diverse data as part of an effort to develop a new research infrastructure to advance science. This paper identifies different kinds of coordination that are necessary to align processes of data collection, production, and analysis. Some of the hard work to integrate data is undertaken before data integration can even become a technical problem. After data integration becomes a technical problem, social and organizational means continue to be critical for resolving differences in assumptions, methods, practices, and priorities. This work calls attention to the diversity of coordinative, social, and organizational practices and concerns that are needed to integrate data and also how, in highly innovative work, the process of integrating data also helps to define scientific problem spaces themselves.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432955"}, {"primary_key": "2466668", "vector": [], "sparse_vector": [], "title": "WProfX: A Fine-grained Visualization Tool for Web Page Loads.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Web page performance is crucial in today's Internet ecosystem, and Web developers use various developer tools to analyze their page load performance. However, existing tools cannot be used to identify the critical bottlenecks during the page load process. In this work, we design an online tool called WProfX that allows Web developers to visually identify bottlenecks in their page structure. The key to WProfX is that unlike existing Web performance tools, WProfX not only visualizes the page load activity timings, but also extracts the dependencies between the activities. Using the dependency structure, WProfX identifies the critical bottleneck activities. This lets a developer quickly identify why their page is loading slow and conduct what-if analyses to study the effect of different optimizations. WProfX uses low-level tracing information exposed by most major browsers to extract the relationship between page load activities. The result is that WProfX works with most major browsers and newer browser versions. WProfX visualizes the page load process as a dependency graph of semantically meaningful Web activities and identifies the critical bottlenecks. We evaluate WProfX with 14 Web developers who perform three what-if analysis tasks involving identifying the page load bottleneck and evaluating the effect of a page optimization. All the participants were able to complete the tasks with WProfX, compared to less than 60% when using the popular developer tools available today. WProfX is currently being used by Web developers in a large telecom and at a Silicon Valley startup.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394975"}, {"primary_key": "2466669", "vector": [], "sparse_vector": [], "title": "Investigating Paradigms of Group Territory in Multiple Display Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multiple-display environments (MDEs) have promise in helping co-located sensemaking tasks by supporting searching, organizing, and discussion tasks. Co-located sensemaking occurs when two or more sensemakers forage for useful information within a dataset, creating and leveraging knowledge structures individually and together. Group territories in MDEs support communicating and assembling findings, but questions remain regarding how to best represent individual sensemaking efforts in the group territory to support the sensemaking collaboration. This paper empirically examines exploration of a large Twitter dataset using three group territory paradigms: parallel, connected, and merged. Results reveal that merging group work increases task complexity while separating individuals' sections in the group territory supports monitoring, and more interactions are performed when individual work is not connected in the group territory.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375193"}, {"primary_key": "2466670", "vector": [], "sparse_vector": [], "title": "What Happens to All These Hackathon Projects?: Identifying Factors to Promote Hackathon Project Continuation.", "authors": ["<PERSON>", "Irene-<PERSON>", "<PERSON>"], "summary": "Time-based events, such as hackathons and codefests, have become a global phenomenon attracting thousands of participants to hundreds of events every year. While research on hackathons has grown considerably, there is still limited insight into what happens to hackathon projects after the event itself has ended. While case studies have provided rich descriptions of hackathons and their aftermath, we add to this literature a large-scale quantitative study of continuation across hackathons in a variety of domains. Our findings indicate that a considerable number of projects get continued after a hackathon has ended. Our results also suggest that short- and long-term continuation are different phenomena. While short-term continuation is associated with technical preparation, number of technologies used in a project and winning a hackathon, long-term continuation is predicated on skill diversity among team members, their technical capabilities in relationship to the technologies and their intention to expand the reach of a project. Moreover, we found intensive short-term activity to be associated with a lower likelihood of long-term project continuation.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415216"}, {"primary_key": "2466671", "vector": [], "sparse_vector": [], "title": "How to Support Newcomers in Scientific Hackathons - An Action Research Study on Expert Mentoring.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Time-bounded events such as hackathons have become a global phenomenon. Scientific communities in particular show growing interest in organizing them to attract newcomers and develop technical artifacts to expand their code base. Current hackathon approaches presume that participants have sufficient expertise to work on projects on their own. They only provide occasional support by domain experts serving as mentors which might not be sufficient for newcomers. Drawing from work on workplace and educational mentoring, we developed and evaluated an approach where each hackathon team is supported by a community member who serves in a mentor role that goes beyond providing occasional support. Evaluating this approach, we found that teams who took ownership of their projects, set achievable goals early while building social ties with their mentor and receiving learning-oriented support reported positive perceptions related to their project and an increased interest in the scientific community that organized the hackathon. Our work thus contributes to our understanding of mentoring in hackathons, an area which has not been extensively studied. It also proposes a feasible approach for scientific communities to attract and integrate newcomers which is crucial for their long-term survival.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392830"}, {"primary_key": "2466672", "vector": [], "sparse_vector": [], "title": "Narrative Maps: An Algorithmic Approach to Represent and Extract Information Narratives.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Narratives are fundamental to our perception of the world and are pervasive in all activities that involve the representation of events in time. Yet, modern online information systems do not incorporate narratives in their representation of events occurring over time. This article aims to bridge this gap, combining the theory of narrative representations with the data from modern online systems. We make three key contributions: a theory-driven computational representation of narratives, a novel extraction algorithm to obtain these representations from data, and an evaluation of our approach. In particular, given the effectiveness of visual metaphors, we employ a route map metaphor to design a narrative map representation. The narrative map representation illustrates the events and stories in the narrative as a series of landmarks and routes on the map. Each element of our representation is backed by a corresponding element from formal narrative theory, thus providing a solid theoretical background to our method. Our approach extracts the underlying graph structure of the narrative map using a novel optimization technique focused on maximizing coherence while respecting structural and coverage constraints. We showcase the effectiveness of our approach by performing a user evaluation to assess the quality of the representation, metaphor, and visualization. Evaluation results indicate that the Narrative Map representation is a powerful method to communicate complex narratives to individuals. Our findings have implications for intelligence analysts, computational journalists, and misinformation researchers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432927"}, {"primary_key": "2466673", "vector": [], "sparse_vector": [], "title": "&apos;Who Knows What&apos;: Audience Targeting for Question Asking on Facebook.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Social networking sites are becoming increasingly popular venues for seeking information. To the extent that users can understand who knows what in their networks and target those friends appropriately, they can make effective use of the site as a knowledge base of information. This paper explores how targeting one's Facebook network when asking questions influences the breadth and quality of answers they receive. An experiment (N = 64) was conducted in which participants posted questions to their Facebook networks in four broadcast level conditions: status update to their full networks, status update to a custom subset of their networks, posting on a friend's Timeline, and sending a direct message. Results indicate that posting a question more broadly results in more information, which is moderated by perceptions of Facebook as a transactive memory system and as a source of social capital. However, informational and social value of responses is greatest when posting to a custom subset of their network. These results suggest that while targeting specific individuals may be the most effective means of gathering information in offline networks, the broadcast affordance of Facebook may be a more useful way to gather information on the site.?", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375191"}, {"primary_key": "2466674", "vector": [], "sparse_vector": [], "title": "When Are Search Completion Suggestions Problematic?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Problematic web search query completion suggestions-perceived as biased, offensive, or in some other way harmful-can reinforce existing stereotypes and misbeliefs, and even nudge users towards undesirable patterns of behavior. Locating such suggestions is difficult, not only due to the long-tailed nature of web search, but also due to differences in how people assess potential harms. Grounding our study in web search query logs, we explore when system-provided suggestions might be perceived as problematic through a series of crowd-experiments where we systematically manipulate: the search query fragments provided by users, possible user search intents, and the list of query completion suggestions. To examine why query suggestions might be perceived as problematic, we contrast them to an inventory of known types of problematic suggestions. We report our observations around differences in the prevalence of a) suggestions that are problematic on their own versus b) suggestions that are problematic for the query fragment provided by a user, for both common informational needs and in the presence of web search voids-topics searched by few to no users. Our experiments surface a rich array of scenarios where suggestions are considered problematic, including due to the context in which they were surfaced. Compounded by the elusive nature of many such scenarios, the prevalence of suggestions perceived as problematic only for certain user inputs, raises concerns about blind spots due to data annotation practices that may lead to some types of problematic suggestions being overlooked.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415242"}, {"primary_key": "2466675", "vector": [], "sparse_vector": [], "title": "CrowdUI: Supporting Web Design with the Crowd.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Web design is a complex and challenging task. It involves making many design decisions that materialise preconceived notions of user needs that may or may not be true. In this paper, we investigate supporting the co-design of a website with visual feedback elicited from the website's community of users. Website users can express their needs by re-arranging and modifying the website's layout and design. To explore and validate this idea, we present CrowdUI, a web-based tool that enables members of the community of a website to visually express their design improvement ideas, frustrations and needs, and to send this feedback to the person in charge of designing or maintaining the website. CrowdUI is validated in a study with 45 users of a popular social media and networking website. Second, our qualitative evaluation with 60 experienced web developers shows that CrowdUI is able to elicit diverse and meaningful feedback. Put together, our results suggest that CrowdUI's approach constitutes a productive setting for eliciting visual feedback from the user community as a complement to traditional ways of eliciting feedback and participatory design. Finally, based on our experiences, we discuss a design space for crowdsourced web design and provide design recommendations for similar future tools.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394978"}, {"primary_key": "2466676", "vector": [], "sparse_vector": [], "title": "The Effect of Sociocultural Variables on Sarcasm Communication Online.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online social networks (OSN) play an essential role for connecting people and allowing them to communicate online. OSN users share their thoughts, moments, and news with their network. The messages they share online can include sarcastic posts, where the intended meaning expressed by the written text is different from the literal one. This could result in miscommunication. Previous research in psycholinguistics has studied the sociocultural factors the might lead to sarcasm misunderstanding between speakers and listeners. However, there is a lack of such studies in the context of OSN. In this paper we fill this gap by performing a quantitative analysis on the influence of sociocultural variables, including gender, age, country, and English language nativeness, on the effectiveness of sarcastic communication online. We collect examples of sarcastic tweets directly from the authors who posted them. Further, we ask third-party annotators of different sociocultural backgrounds to label these tweets for sarcasm. Our analysis indicates that age, English language nativeness, and country are significantly influential and should be considered in the design of future social analysis tools that either study sarcasm directly, or look at related phenomena where sarcasm may have an influence. We also make observations about the social ecology surrounding sarcastic exchanges on OSNs. We conclude by suggesting ways in which our findings can be included in future work.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392834"}, {"primary_key": "2466677", "vector": [], "sparse_vector": [], "title": "Recognizing 3D Trajectories as 2D Multi-stroke Gestures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While end users can acquire full 3D gestures with many input devices, they often capture only 3D trajectories, which are 3D uni-path, uni-stroke single-point gestures performed in thin air. Such trajectories with their $(x,y,z)$ coordinates could be interpreted as three 2D stroke gestures projected on three planes,\\ie, $XY$, $YZ$, and $ZX$, thus making them admissible for established 2D stroke gesture recognizers. To investigate whether 3D trajectories could be effectively and efficiently recognized, four 2D stroke gesture recognizers, \\ie, \\$P, \\$P+, \\$Q, and <PERSON><PERSON>, are extended to the third dimension: $\\$P^3$, $\\$P+^3$, $\\$Q^3$, and Rubine-Sheng, an extension of <PERSON>e for 3D with more features. Two new variations are also introduced: $\\F for flexible cloud matching and FreeHandUni for uni-path recognition. Rubine3D, another extension of Rubine for 3D which projects the 3D gesture on three orthogonal planes, is also included. These seven recognizers are compared against three challenging datasets containing 3D trajectories, \\ie, SHREC2019 and 3DTCGS, in a user-independent scenario, and 3DMadLabSD with its four domains, in both user-dependent and user-independent scenarios, with varying number of templates and sampling. Individual recognition rates and execution times per dataset and aggregated ones on all datasets show a highly significant difference of $\\$P+^3$ over its competitors. The potential effects of the dataset, the number of templates, and the sampling are also studied.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427326"}, {"primary_key": "2466678", "vector": [], "sparse_vector": [], "title": "Raising the Responsible Child: Collaborative Work in the Use of Activity Trackers for Children.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Commercial activity trackers are increasingly being designed for children as young as 3 years old. However, we have limited understanding of family use practices around these trackers. To provide an overall view of how families naturally use activity trackers towards collaborative management of family health, we systematically identified 9 trackers designed for children available on 4 consumer electronics retailers. Our data is composed of 2,628 user reviews both from the consumer retailers (for the wearables) and mobile application stores (for the associated apps). Our findings indicate children's and parents' collaborative use of these technologies beyond health and wellness. Parents state that their children enjoy practicing independence and rewards while contributing to family health management and daily life requirements. Parents expect these devices to ease their life and to teach their children to become more responsible for their health, daily tasks, and schedule. However, the current designs give limited agency on child's side and require parents' active participation for wearable-app coordination. For these reasons, they do not fully address parents' expectations in decreasing their workload. On the other hand, they have the potential to facilitate family interaction with challenges structured around the data reported through trackers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415228"}, {"primary_key": "2466679", "vector": [], "sparse_vector": [], "title": "Topical Focus of Political Campaigns and its Impact: Findings from Politicians&apos; Hashtag Use during the 2019 Indian Elections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kalika Bali", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We studied the topical preferences of social media campaigns of India's two main political parties by examining the tweets of 7382 politicians during the key phase of campaigning between Jan - May of 2019 in the run up to the 2019 general election. First, we compare the use of self-promotion and opponent attack, and their respective success online by categorizing 1208 most commonly used hashtags accordingly into the two categories. Second, we classify the tweets applying a qualitative typology to hashtags on the subjects of nationalism, corruption, religion and development. We find that the ruling BJP tended to promote itself over attacking the opposition whereas the main challenger INC was more likely to attack than promote itself. Moreover, while the INC gets more retweets on average, the BJP dominates Twitter's trends by flooding the online space with large numbers of tweets. We consider the implications of our findings hold for political communication strategies in democracies across the world.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392860"}, {"primary_key": "2466681", "vector": [], "sparse_vector": [], "title": "&quot;Pick Someone Who Can Kick Your Ass&quot; - Moneywork in Financial Third Party Access.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper explores some of the new possibilities for financial third party access that are enabled by \"open banking\". The term open banking is used to designate the availability of banks' customer data through application programming interfaces (APIs). Financial third party access refers to the mechanisms that facilitate the engagement of others in the management of our personal finances. Engaging trusted others in personal finances may be especially valuable for individuals experiencing financial hardship or life circumstances that place their financial stability at risk. We deployed a new third party access tool enabled by the UK Open Banking APIs for 90 days with 14 people who self-identified as living with a mental health condition. The tool, which was developed by a financial technology startup founded by the second author, allowed participants to select a trusted \"ally\" who was notified when certain transactions took place in participants' bank accounts. During the deployment, the 14 participants and 8 of their \"allies\" took part in a diary study and pre- and post-deployment interviews. The experiences of our participants reveal the inadequacy and shortcomings of existing formal third party access mechanisms, and the moneywork involved in financial third party access. We argue that focusing on this moneywork can help us design flexible, proportionate and practice-sensitive services for financial third party access that move beyond discourses of protection and control in order to enable meaningful financial collaboration.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432917"}, {"primary_key": "2466682", "vector": [], "sparse_vector": [], "title": "What Makes People Join Conspiracy Communities?: Role of Social Factors in Conspiracy Engagement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Widespread conspiracy theories, like those motivating anti-vaccination attitudes or climate change denial, propel collective action, and bear society-wide consequences. Yet, empirical research has largely studied conspiracy theory adoption as an individual pursuit, rather than as a socially mediated process. What makes users join communities endorsing and spreading conspiracy theories? We leverage longitudinal data from 56 conspiracy communities on Reddit to compare individual and social factors determining which users join the communities. Using a quasi-experimental approach, we first identify 30K future conspiracists?(FC) and30K matched non-conspiracists?(NC). We then provide empirical evidence of the importance of social factors across six dimensions relative to the individual factors by analyzing 6 million Reddit comments and posts. Specifically, in social factors, we find that dyadic interactions with members of the conspiracy communities and marginalization outside of the conspiracy communities are the most important social precursors to conspiracy joining-even outperforming individual factor baselines. Our results offer quantitative backing to understand social processes and echo chamber effects in conspiratorial engagement, with important implications for democratic institutions and online communities.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432922"}, {"primary_key": "2466683", "vector": [], "sparse_vector": [], "title": "Divided We Stand: The Collaborative Work of Patients and Providers in an Enigmatic Chronic Disease.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In chronic conditions, patients and providers need support in understanding and managing illness over time. Focusing on endometriosis, an enigmatic chronic condition, we conducted interviews with specialists and focus groups with patients to elicit their work in care specifically pertaining to dealing with an enigmatic disease, both independently and in partnership, and how technology could support these efforts. We found that the work to care for the illness, including reflecting on the illness experience and planning for care, is significantly compounded by the complex nature of the disease: enigmatic condition means uncertainty and frustration in care and management; the multi-factorial and systemic features of endometriosis without any guidance to interpret them overwhelm patients and providers; the different temporal resolutions of this chronic condition confuse both patients and providers; and patients and providers negotiate medical knowledge and expertise in an attempt to align their perspectives. We note how this added complexity demands that patients and providers work together to find common ground and align perspectives, and propose three design opportunities (considerations to construct a holistic picture of the patient, design features to reflect and make sense of the illness, and opportunities and mechanisms to correct misalignments and plan for care) and implications to support patients and providers in their care work. Specifically, the enigmatic nature of endometriosis necessitates complementary approaches from human-centered computing and artificial intelligence, and thus opens a number of future research avenues.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434170"}, {"primary_key": "2466684", "vector": [], "sparse_vector": [], "title": "DreamCatcher: Exploring How Parents and School-Age Children can Track and Review Sleep Information Together.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parents and their school-age children can impact one another's sleep. Most sleep-tracking tools, however, are designed for adults and make it difficult for parents and children to track together. To examine how to design a family-centered sleep tracking tool, we designed DreamCatcher. DreamCatcher is an in-home, interactive, shared display that aggregates data from wrist-worn sleep sensors and self-reported mood. We deployed DreamCatcher as a probe to examine the design space of tracking sleep as a family. Ten families participated in the study probe between 15 and 50 days. This study uses a family systems perspective to explore research questions regarding the feasibility of children actively tracking health data alongside their parents and the effects of tracking and sharing on family dynamics. Our results indicate that children can be active tracking contributors and that having parents and children track together encourages turn-taking and working together. However, there were also moments when family members, in particular parents, felt discomfort from sharing their sleep and mood with other family members. Our research contributes to a growing understanding of designing family centered health-informatics tools to support the combined needs of parents and children.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392882"}, {"primary_key": "2466685", "vector": [], "sparse_vector": [], "title": "Entering Doors, Evading Traps: Benefits and Risks of Visibility During Transgender Coming Outs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Coming out and being visible online can offer transgender and/or non-binary people benefits not found elsewhere. However, it also can expose them to negative reactions and bad experiences. Through an analysis of 15 semi-structured interviews, we investigate the experiences of transgender and/or non-binary people coming out across social media sites (SMSs). We found that participants employed strategies around disclosure and visibility to limit the consequences of coming out and to access support. Using trans theory on visibility, we discuss how online spaces present metaphorical \"doors\" to resources, support, and recognition---but can also be \"traps\" for those that do not meet the expectations of the space. We discuss how visibility empowered participants to create \"trapdoors\" to new spaces within SMSs where they could create positive outcomes for themselves and their communities. We close with considerations for designers as they create online spaces, and present a broader call to action for the HCI community around designing online spaces.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434181"}, {"primary_key": "2466686", "vector": [], "sparse_vector": [], "title": "Pulling Back the Curtain on the Wizards of Oz.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Wizard of Oz method is an increasingly common practice in HCI and CSCW studies as part of iterative design processes for interactive systems. Instead of designing a fully-fledged system, the 'technical work' of key system components is completed by human operators yet presented to study participants as if computed by a machine. However, little is known about how Wizard of Oz studies are interactionally and collaboratively achieved in situ by researchers and participants. By adopting an ethnomethodological perspective, we analyse our use of the method in studies with a voice-controlled vacuum robot and two researchers present. We present data that reveals how such studies are organised and presented to participants and unpack the coordinated orchestration work that unfolds 'behind the scenes' to complete the study. We examine how the researchers attend to participant requests and technical breakdowns, and discuss the performative, collaborative, and methodological nature of their work. We conclude by offering insights from our application of the approach to others in the HCI and CSCW communities for using the method.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432942"}, {"primary_key": "2466687", "vector": [], "sparse_vector": [], "title": "Estimating Conversational Styles in Conversational Microtask Crowdsourcing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Crowdsourcing marketplaces have provided a large number of opportunities for online workers to earn a living. To improve satisfaction and engagement of such workers, who are vital for the sustainability of the marketplaces, recent works have used conversational interfaces to support the execution of a variety of crowdsourcing tasks. The rationale behind using conversational interfaces stems from the potential engagement that conversation can stimulate. Prior works in psychology have also shown that conversational styles can play an important role in communication. There are unexplored opportunities to estimate a worker's conversational style with an end goal of improving worker satisfaction, engagement and quality. Addressing this knowledge gap, we investigate the role of conversational styles in conversational microtask crowdsourcing. To this end, we design a conversational interface which supports task execution, and we propose methods to estimate the conversational style of a worker. Our experimental setup was designed to empirically observe how conversational styles of workers relate with quality-related outcomes. Results show that even a naive supervised classifier can predict the conversation style with high accuracy (80%), and crowd workers with an Involvement conversational style provided a significantly higher output quality, exhibited a higher user engagement and perceived less cognitive task load in comparison to their counterparts. Our findings have important implications on task design with respect to improving worker performance and their engagement in microtask crowdsourcing.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392837"}, {"primary_key": "2466688", "vector": [], "sparse_vector": [], "title": "Daughters of Men: Saudi Women&apos;s Sociotechnical Agency Practices in Addressing Domestic Abus e.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While domestic abuse is an all too common experience for women worldwide, how people experience the abuse and their resources to deal with the abuse differ. In this qualitative, interview-based study, we examine Saudi women's domestic safety concerns living in Saudi Arabia and the United States. Based on non-Western Islamic feminist views of agency, we identify three key practices, focused on how women resist or deal with their domestic violence. For each practice, we highlight how interwoven cultural, religious, and political contexts impact Saudi women's ability to recognize and deal with domestic abuse. We attend to technology's role in enabling or hindering women's agency. These practices include: 1) recognizing abuse, where women identify abusive situations, 2) managing abuse, where women find ways to cope with ongoing or anticipated abuse within their constraints and resources, and 3) seeking non-abusive futures, where women decide how to mitigate the abuse or leave their abuser. Given domestic violence's complicated nature, we highlight several key design recommendations based on women's values.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432923"}, {"primary_key": "2466689", "vector": [], "sparse_vector": [], "title": "Unequal Impacts of Augmented Reality on Learning and Collaboration During Robot Programming with Peers.", "authors": ["<PERSON><PERSON><PERSON>", "Vivek Hv", "<PERSON>"], "summary": "Augmented reality (AR) applications are growing in popularity in educational settings. While the effects of AR experiences on learning have been widely studied, there is relatively less research on understanding the impact of AR on the dynamics of co-located collaborative learning, specifically in the context of novices programming robots. Educational robotics are a powerful learning context because they engage students with problem solving, critical thinking, STEM (Science, Technology, Engineering, Mathematics) concepts, and collaboration skills. However, such collaborations can suffer due to students having unequal access to resources or dominant peers. In this research we investigate how augmented reality impacts learning and collaboration while peers engage in robot programming activities. We use a mixed methods approach to measure how participants are learning, manipulating resources, and engaging in problem solving activities with peers. We investigate how these behaviors are impacted by the presence of augmented reality visualizations, and by participants? proximity to resources. We find that augmented reality improved overall group learning and collaboration. Detailed analysis shows that AR strongly helps one participant more than the other, by improving their ability to learn and contribute while remaining engaged with the robot. Furthermore, augmented reality helps both participants maintain a common ground and balance contributions during problem solving activities. We discuss the implications of these results for designing AR and non-AR collaborative interfaces.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432944"}, {"primary_key": "2466690", "vector": [], "sparse_vector": [], "title": "MixTAPE: Mixed-initiative Team Action Plan Creation Through Semi-structured Notes, Automatic Task Generation, and Task Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Checklists and action plans are a proven mechanism for project-based collaboration. Synthesizing project-specific plans is challenging, as project managers must consider multiple sources of information, from structured surveys to semi-structured conversations with stakeholders. In a needfinding study with project managers, we identified challenges in creating action plans for teams. We built MixTAPE, a mixed-initiative system that addressed these challenges with three components: a semi-structured note-taking interface for capturing stakeholder conversations, a plan generator for automatically combining multi-source information into action plans, and classification models for assigning and prioritizing action items. We evaluated MixTAPE in an observational study of 32 website design projects. Compared to a previously unstructured process, MixTAPE generated 1.45X as many tasks that are more consistent, while reducing the plan creation time by 33.70%. Through interviews and surveys, we found that participants rate MixTAPE highly across several measures. Based on our findings, we discuss the implications and opportunities for mixed-initiative action plan creation.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415240"}, {"primary_key": "2466691", "vector": [], "sparse_vector": [], "title": "A Seat at the Table: Black Feminist Thought as a Critical Framework for Inclusive Game Design.", "authors": ["<PERSON><PERSON><PERSON>", "India Irish"], "summary": "Game-based second language (L2) learning represents an ideal alternative to foreign language classroom instruction. However, despite a diverse player demographic, the design of L2 games is often not informed by players representative of marginalized populations, especially women of color (i.e., Black women). Such oversight in the design process contributes to games that perpetuate gendered and racist stereotypes, and therefore, are less appealing to women of color. In response this dilemma, we utilize Black Feminist Thought (BFT) as a critical framework to engage Black women, a marginalized and understudied population within the gaming subculture, and more broadly, the Computer Supported Cooperative Work (CSCW) community in game design. Twenty-five Black women take on multiple roles as game designers, foreign language instructor, and informants who represent both producers and consumers throughout the conceptualization phase of a L2 mobile game prototype. Applying BFT principles, we leverage Black women\\textquotesingle s lived intersectional experiences to transform the traditional game design process into a more inclusive design experience for Black women. In the context of games that support L2 learning, our findings reveal that Black women appreciate games that: 1. provide authentic cultural experiences; 2. feature intersectional game characters that reflect real life experiences; 3. accurately portray the diversity of Black women's bodies; and 4. provide opportunities for players to customize game assets. As a disruptor to traditional game design, BFT makes salient oppressive design practices within the gaming culture that also extend to the larger CSCW community, signifying the need to embrace more inclusive design practices that benefit Black women and other marginalized populations.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415188"}, {"primary_key": "2466692", "vector": [], "sparse_vector": [], "title": "A Gameful Organizational Assimilation Process: Insights from World of Warcraft for Gamification Design.", "authors": ["<PERSON><PERSON>"], "summary": "A central process of virtual organization design relates to how newcomers are assimilated into organizational dynamics. Research on organizational assimilation has traditionally investigated 'serious' organizational contexts. Nonetheless, video games can offer insights on how such assimilation can be effectively supported. In this article, I propose to look at World of Warcraft (WoW) to understand how individuals can be successfully integrated into online organizations. Through an ethnographic research, made up of participant observation and 36 semi-structured interviews, I explore how specific game design elements support organizational assimilation into WoW raiding guilds. This shows how designers elicit extremely engaging organizational dynamics that encourage players to identify with their organizations. Based on these findings, I propose design considerations for gamifying virtual organizations that draw from the structure of WoW raiding guilds.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434172"}, {"primary_key": "2466693", "vector": [], "sparse_vector": [], "title": "&quot;It&apos;s easier than causing confrontation&quot;: Sanctioning Strategies to Maintain Social Norms and Privacy on Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sanctions play an essential role in enforcing and sustaining social norms. On social networking sites (SNS), sanctions allow individuals to shape community norms on appropriate privacy respecting behaviors. Existing theories of privacy assume the use of such sanctions but do not examine the extent and effectiveness of sanctioning behaviors. We conducted a qualitative interview study of young adults (N=23), and extend research on collective boundary regulation by studying sanctions in the context of popular SNS. Through a systematization of sanctioning strategies, we find that young adults prefer to use indirect and invisible sanctions to preserve strong-tie relationships. Such sanctions are not always effective in helping the violator understand the nature of their normative violation. We offer suggestions on supporting online sanctioning that make norms more visible and signal violations in ways that avoid direct confrontation to reduce the risk of harming on-going social relationships.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392827"}, {"primary_key": "2466695", "vector": [], "sparse_vector": [], "title": "Content Creation in Later Life: Reconsidering Older Adults&apos; Digital Participation and Inclusion.", "authors": ["<PERSON><PERSON><PERSON>", "Thomas <PERSON>", "<PERSON>"], "summary": "In increasingly digitalised societies, government initiatives to ensure that public services remain accessible for everyone typically focus on the digital inclusion of older adults. However, by solely viewing older adults as passive recipients or consumers of services, digital inclusion strategies under-emphasise the concept of digital participation. Highlighting the importance of older adults as active contributors in a digital society, we investigated the potential of content creation to increase older adults' digital skills whilst also strengthening their digital participation. Through a workshop and interviews involving three different groups of older content producers, we show that content creation can stimulate older adults' digital participation. We report on challenges faced by the content creators, including time constraints, lack of professional support and the preference to create content collaboratively. We propose that by facilitating collaborative content creation activities, local communities can better support older adults' digital participation and facilitate inclusion across different life domains.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434166"}, {"primary_key": "2466697", "vector": [], "sparse_vector": [], "title": "Evaluating News Media Reports on the &apos;Blue Whale Challenge&apos; for Adherence to Suicide Prevention Safe Messaging Guidelines.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Blue Whale Challenge (BWC) is an online viral \"game\" that allegedly encourages youth and young adults towards self-harming behaviors that could eventually lead to suicide. The BWC can be situated within a larger phenomenon of viral online self-harm challenges, which may be propagated through both social media and news sources. Research has established that suicide is a global public health issue that is known to be influenced by media reporting. Violation of safe messaging guidelines has been shown to increase imitative suicides, particularly in youth and young adults. Given the confirmed effects of news media reporting, we analyzed 150 digital newspaper articles reporting on the BWC to assess whether they adhered to suicide prevention safe messaging guidelines. Overall, 81% of the articles violated at least one contagion-related guideline, most commonly normalizing suicide, discussing means of suicide, and sensationalizing. Even though the majority (91%) of the articles adhered to at least one health-promotion guideline, such as emphasizing prevention, the articles did not follow these guidelines on a deep and comprehensive level. Through thematic analysis, we also found evidence of potential misinformation in reporting, where the articles unequivocally attributed many suicides to the BWC with little or no evidence. Additionally, articles often stated an individual's reason for participating in the challenge without interviewing the individual or those close to the individual, another aspect of potential misinformation due to lack of evidence. A contribution of the current study is the synthesis of safe messaging guidelines that can be used in future research. This study contributes to the understanding of news reporting practices regarding suicide and self-harm in regard to the BWC and similar online challenges. We discuss how sensationalized news media reports on the BWC could unintentionally propagate suicide contagion effects that normalize self-harming behaviors among youth. We then examine implications for practice and policy, such using automated approaches to aid reporters in adhering to safe messaging guidelines.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392831"}, {"primary_key": "2466699", "vector": [], "sparse_vector": [], "title": "Urban Accessibility as a Socio-Political Problem: A Multi-Stakeholder Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Traditionally, urban accessibility is defined as the ease of reaching destinations. Studies on urban accessibility for pedestrians with mobility disabilities (e.g., wheelchair users) have primarily focused on understanding the challenges that the built environment imposes and how they overcome them. In this paper, we move beyond physical barriers and focus on socio-political challenges in the civic ecosystem that impedes accessible infrastructure development. Using a multi-stakeholder approach, we interviewed five primary stakeholder groups (N=25): (1) people with mobility disabilities, (2) caregivers, (3) accessibility advocates, (4) department officials, and (5) policymakers. We discussed their current accessibility assessment and decision-making practices. We identified the key needs and desires of each group, how they differed, and how they interacted with each other in the civic ecosystem to bring about change. We found that people, politics, and money were intrinsically tied to underfunded accessibility improvement projects \"without continued support from the public and the political leadership, existing funding may also disappear. Using the insights from these interviews, we explore how may technology enhance our stakeholders\" decision-making processes and facilitate accessible infrastructure development.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432908"}, {"primary_key": "2466700", "vector": [], "sparse_vector": [], "title": "Eliciting Sketched Expressions of Command Intentions in an IDE.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software engineers routinely use sketches (informal, ad-hoc drawings) to visualize and communicate complex ideas for colleagues or themselves. We hypothesize that sketching could also be used as a novel interaction modality in integrated software development environments (IDEs), allowing developers to express desired source code manipulations by sketching right on top of the IDE, rather than remembering keyboard shortcuts or using a mouse to navigate menus and dialogs. For an initial assessment of the viability of this idea, we conducted an elicitation study that prompted software developers to express a number of common IDE commands through sketches. For many of our task prompts, we observed considerable agreement in how developers would express the respective commands through sketches, suggesting that further research on a more formal sketch-based visual command language for IDEs would be worthwhile.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427328"}, {"primary_key": "2466702", "vector": [], "sparse_vector": [], "title": "&quot;Am I doing this all wrong?&quot;.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Running a household requires a large amount of labor, from ensuring multiple bills are paid to organizing important documents. Failure to manage such information can have critical consequences for the financial and psychological well-being of the family; however, little is known about how families manage the full scale of information encountered in the home. In this paper, we introduce family information management (FIM) as a set of overarching practices involved in managing and coordinating household-related information. To understand how families engage in FIM, we conducted in-depth interviews with members of ten families, which included guided tours of their information archives. We found that families struggle to organize, store, retrieve, and share information, and that there are significant socioemotional costs to this work. We propose opportunities for designing technologies to support FIM and argue that, given the numerous challenges and unmet needs, the understudied area of FIM deserves further investment of research and design efforts.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415209"}, {"primary_key": "2466703", "vector": [], "sparse_vector": [], "title": "Characterizing Stage-aware Writing Assistance for Collaborative Document Authoring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Writing is a complex non-linear process that begins with a mental model of intent, and progresses through an outline of ideas, to words on paper (and their subsequent refinement). Despite past research in understanding writing, Web-scale consumer and enterprise collaborative digital writing environments are yet to greatly benefit from intelligent systems that understand the stages of document evolution, providing opportune assistance based on authors' situated actions and context. In this paper, we present three studies that explore temporal stages of document authoring. We first survey information workers at a large technology company about their writing habits and preferences, concluding that writers do in fact conceptually progress through several distinct phases while authoring documents. We also explore, qualitatively, how writing stages are linked to document lifespan. We supplement these qualitative findings with an analysis of the longitudinal user interaction logs of a popular digital writing platform over several million documents. Finally, as a first step towards facilitating an intelligent digital writing assistant, we conduct a preliminary investigation into the utility of user interaction log data for predicting the temporal stage of a document. Our results support the benefit of tools tailored to writing stages, identify primary tasks associated with these stages, and show that it is possible to predict stages from anonymous interaction logs. Together, these results argue for the benefit and feasibility of more tailored digital writing assistance.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434180"}, {"primary_key": "2466704", "vector": [], "sparse_vector": [], "title": "Maps Around Me: 3D Multiview Layouts in Immersive Spaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visual exploration of maps often requires a contextual understanding at multiple scales and locations. Multiview map layouts, which present a hierarchy of multiple views to reveal detail at various scales and locations, have been shown to support better performance than traditional single-view exploration on desktop displays. This paper investigates the extension of such layouts of 2D maps into 3D immersive spaces, which are not limited by the real-estate barrier of physical screens and support sensemaking through spatial interaction. Based on our initial implementation of immersive multiview maps, we conduct an exploratory study with 16 participants aimed at understanding how people place and view such maps in immersive space. We observe the layouts produced by users performing map exploration search, comparison and route-planning tasks. Our qualitative analysis identifies patterns in layoutgeometry (spherical, spherical cap, planar),overview-detail relationship (central window, occluding, coordinated) andinteraction strategy. Based on these observations, along with qualitative feedback from a user walkthrough session, we identify implications and recommend features for immersive multiview map systems. Our main findings are that participants tend to prefer and arrange multiview maps in a spherical cap layout around them and that they often rearrange the views during tasks.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427329"}, {"primary_key": "2466705", "vector": [], "sparse_vector": [], "title": "Methods for Generating Typologies of Non/use.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Prior studies of technology non-use demonstrate the need for approaches that go beyond a simple binary distinction between users and non-users. This paper proposes a set of two different methods by which researchers can identify types of non/use$^{1}$ relevant to the particular sociotechnical settings they are studying. These methods are demonstrated by applying them to survey data about Facebook non/use. The results demonstrate that the different methods proposed here identify fairly comparable types of non/use. They also illustrate how the two methods make different trade offs between the granularity of the resulting typology and the total sample size. The paper also demonstrates how the different typologies resulting from these methods can be used in predictive modeling, allowing for the two methods to corroborate or disconfirm results from one another. The discussion considers implications and applications of these methods, both for research on technology non/use and for studying social computing more broadly.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392832"}, {"primary_key": "2466706", "vector": [], "sparse_vector": [], "title": "How We&apos;ve Taught Algorithms to See Identity: Constructing Race and Gender in Image Databases for Facial Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Race and gender have long sociopolitical histories of classification in technical infrastructures-from the passport to social media. Facial analysis technologies are particularly pertinent to understanding how identity is operationalized in new technical systems. What facial analysis technologies can do is determined by the data available to train and evaluate them with. In this study, we specifically focus on this data by examining how race and gender are defined and annotated in image databases used for facial analysis. We found that the majority of image databases rarely contain underlying source material for how those identities are defined. Further, when they are annotated with race and gender information, database authors rarely describe the process of annotation. Instead, classifications of race and gender are portrayed as insignificant, indisputable, and apolitical. We discuss the limitations of these approaches given the sociohistorical nature of race and gender. We posit that the lack of critical engagement with this nature renders databases opaque and less trustworthy. We conclude by encouraging database authors to address both the histories of classification inherently embedded into race and gender, as well as their positionality in embedding such classifications.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392866"}, {"primary_key": "2466707", "vector": [], "sparse_vector": [], "title": "Data and Power: Archival Appraisal Theory as a Framework for Data Preservation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Digital data pervades everyday life, from personal photos shared on social media to voice commands for Amazon Alexa. A widespread industry culture of 'move fast and break things,' however, has compelled data management practices that prioritize profit over preservation. This paper draws from archival theories of appraisal to foreground control, power, subjectivity, and emotion in computing practices that treat data storage as a neutral or objective cost-center. We draw on postmodern archival appraisal theory that recognizes the archive as a powerful and subjective curator of identity and memory. The theoretical basis of archival decision practices, in turn, establishes the value of the archival record and thus the need to save it. With three primary issues of appraisal theory as a framework, we report on an interview study with adults (N=17), ages 51-72, who are in a transitional life-stage that focuses them on their experiences and memories that are worth keeping or discarding. We sketch implications for data management paths that forefront legacy, life transitions, precarity, and control.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415233"}, {"primary_key": "2466708", "vector": [], "sparse_vector": [], "title": "Robots in Groups and Teams: A Literature Review.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Malte F. <PERSON>"], "summary": "Autonomous robots are increasingly placed in contexts that require them to interact with groups of people rather than just a single individual. Interactions with groups of people introduce nuanced challenges for robots, since robots? actions influence both individual group members and complex group dynamics. We review the unique roles robots can play in groups, finding that small changes in their nonverbal behavior and personality impacts group behavior and, by extension, influences ongoing interpersonal interactions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415247"}, {"primary_key": "2466709", "vector": [], "sparse_vector": [], "title": "Reconsidering Self-Moderation: the Role of Research in Supporting Community-Based Models for Online Content Moderation.", "authors": ["<PERSON>"], "summary": "Research in online content moderation has a long history of exploring different forms that moderation can take, including both user-driven moderation models on community-based platforms like Wikipedia, Facebook Groups, and Reddit, and centralized corporate moderation models on platforms like Twitter and Instagram. In this work I review different approaches to moderation research with the goal of providing a roadmap for researchers studying community self-moderation. I contrast community-based moderation research with platforms and policies-focused moderation research, and argue that the former has an important role to play in shaping discussions about the future of online moderation. I provide six guiding questions for future research that, if answered, can support the development of a form of user-driven moderation that is widely implementable across a variety of social spaces online, offering an alternative to the corporate moderation models that dominate public debate and discussion.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415178"}, {"primary_key": "2466710", "vector": [], "sparse_vector": [], "title": "Quality of and Attention to Instructions in Telementoring.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yuanyuan Feng", "<PERSON><PERSON>", "Adrian Park", "<PERSON>", "<PERSON>"], "summary": "There is a long-standing interest in CSCW on distributed instruction - both in how it differs from collocated instruction as well as the design of tools to reduce any deficiencies. In this study, we leveraged the unique environment of laparoscopic surgery to compare the efficacy and mechanism of instruction in a collocated and distributed condition. By implementing the same instructional technology in both conditions, we are able to evaluate the effect of distance on instruction without the confounding variable of medium of instruction. Surprisingly, our findings revealed trainees perceived a higher perceived quality of instruction in the distributed condition. Further investigation suggests that in a distributed learning environment, trainees change their behavior to attend more to the provided instructions resulting in this higher perceived quality of instruction. Finally, we discuss our findings with regards to media compensation theory, and we provide both social and technical insights on how to better support a distributed instructional process.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415236"}, {"primary_key": "2466711", "vector": [], "sparse_vector": [], "title": "Learning from Tweets: Opportunities and Challenges to Inform Policy Making During Dengue Epidemic.", "authors": ["<PERSON><PERSON>", "Shahinul Hoque Ony", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>"], "summary": "Social media platforms are widely used by people to report, access, and share information during outbreaks and epidemics. Although government agencies and healthcare institutions in developed regions are increasingly relying on social media to develop epidemic forecasts and outbreak response, there is a limited understanding of how people in developing regions interact on social media during outbreaks and what useful insights this dataset could offer during public health crises. In this work, we examined 28,688 tweets to identify public health issues during dengue epidemic in Bangladesh and found several insights, such as irregularities in dengue diagnosis and treatment, shortage of blood supply for Rh negative blood groups, and high local transmission of dengue during Eid-ul-Adha, that impact disease preparedness and outbreak response. We discuss the opportunities and challenges in analyzing tweets and outline how government agencies and healthcare institutions can use social media health data to inform policy making during public health crises.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392875"}, {"primary_key": "2466712", "vector": [], "sparse_vector": [], "title": "&apos;I Can&apos;t Even Buy Apples If I Don&apos;t Use Mobile Pay?&apos;: When Mobile Payments Become Infrastructural in China.", "authors": ["<PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite slow adoption in the US, mobile payments are thede facto solution for hundreds of millions of users in China for everything from paying bills to riding buses, from sending virtual \"Red Packets'' to buying money-market funds. In this paper, we use the theoretical lens of infrastructure to study users' interactions with ubiquitous mobile payment systems in China, focusing on Alipay and WeChat Pay, the two dominant apps on the market. Based on data from a survey (n=466) and follow-up interviews (n=12) with users in China, we describe the diverse usage patterns across physical, social, and digital ubiquity, and a series of challenges people face. Reflecting on the lessons we learned from the Chinese case -- in particular, problems and pitfalls -- we discuss some implications both for design and for policy. Our findings have important implications for other countries that have been moving towards greater adoption of mobile payments.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415241"}, {"primary_key": "2466713", "vector": [], "sparse_vector": [], "title": "Designing Alternative Representations of Confusion Matrices to Support Non-Expert Public Understanding of Algorithm Performance.", "authors": ["<PERSON> Shen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ensuring effective public understanding of algorithmic decisions that are powered by machine learning techniques has become an urgent task with the increasing deployment of AI systems into our society. In this work, we present a concrete step toward this goal by redesigning confusion matrices for binary classification to support non-experts in understanding the performance of machine learning models. Through interviews (n=7) and a survey (n=102), we mapped out two major sets of challenges lay people have in understanding standard confusion matrices: the general terminologies and the matrix design. We further identified three sub-challenges regarding the matrix design, namely, confusion about the direction of reading the data, layered relations and quantities involved. We then conducted an online experiment with 483 participants to evaluate how effective a series of alternative representations target each of those challenges in the context of an algorithm for making recidivism predictions. We developed three levels of questions to evaluate users' objective understanding. We assessed the effectiveness of our alternatives for accuracy in answering those questions, completion time, and subjective understanding. Our results suggest that (1) only by contextualizing terminologies can we significantly improve users' understanding and (2) flow charts, which help point out the direction of reading the data, were most useful in improving objective understanding. Our findings set the stage for developing more intuitive and generally understandable representations of the performance of machine learning models.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415224"}, {"primary_key": "2466714", "vector": [], "sparse_vector": [], "title": "From Virtual Strangers to IRL Friends: Relationship Development in Livestreaming Communities on Twitch.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Accounts of the social experience within livestreaming channels vary widely, from the frenetic \"crowdroar\" offered in some channels to the close-knit, \"participatory communities\" within others. What kinds of livestreaming communities enable the types of meaningful conversation and connection that support relationship development, and how? In this paper, we explore how personal relationships develop within Twitch, a popular livestreaming service. Interviews with 21 pairs who met initially within Twitch channels illustrate how interactions originating in Twitch's text-based, pseudonymous chat environment can evolve into close relationships, marked by substantial trust and support. Consistent with <PERSON><PERSON>'s hyperpersonal model, these environments facilitate self-disclosure and conversation by reducing physical cues and emphasizing common ground, while frequent, low-stakes interaction allow relationships to deepen over time. Our findings also highlight boundaries of the hyperpersonal model. As group size increases, participants leverage affordances for elevated visibility to spark interactions; as relationships deepen, they incorporate complementary media channels to increase intimacy. Often, relationships become so deep through purely computer-mediated channels that face-to-face meetings become yet another step in a continuum of relationship development. Findings from a survey of 1,367 members of Twitch communities demonstrate how the suitability of these spaces as venues for relational interaction decreases as communities increase in size. Together, these findings illustrate vividly how hyperpersonal interaction functions in the context of real online communities. We consider implications for the design and management of online communities, including their potential for supporting \"strong bridges,\" relationships which combine the benefits of strong ties and network bridges.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415165"}, {"primary_key": "2466715", "vector": [], "sparse_vector": [], "title": "Real-Time Interruption Management System for Efficient Distributed Collaboration in Multi-tasking Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Interruption dissemination in proactive systems remains a challenge for efficient human-machine collaboration, especially in real-time distributed collaborative environments. In this paper a real-time interruption management system (IMS) is proposed that leverages speech information, the most commonly used and available means of communication within collaborative distributed environments. The key aspect of this paper includes a proposed real-time IMS system that leverages lexical affirmation cues to infer the end of a task or task boundary as a candidate interruption time. The performance results show the proposed real-time lexical Affirmation Cues based Interruption Management System (ACE-IMS) outperforms the current baseline real-time IMS system within the existing literature. ACE-IMS has the potential of reducing disruptive interruptions without incurring excessive missed opportunities to disseminate interruptions by utilizing only the most frequently used mode of human communication: voice. Thereby, providing a promising new baseline to further the system development of real-time interruption management systems within the ever-growing distributed collaborative domain.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392844"}, {"primary_key": "2466716", "vector": [], "sparse_vector": [], "title": "Ensuring the Consistency between User Requirements and Task Models: A Behavior-Based Automated Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Evaluating and ensuring the consistency between user requirements and modeling artifacts is a long-time issue for model-based software design. Conflicts in requirements specifications can lead to many design errors and have a decisive impact on the quality of systems under development. This article presents an approach based on Behavior-Driven Development (BDD) to provide automated assessment for task models, which are intended to model the flow of user and system tasks in an interactive system. The approach has been evaluated by exploiting user requirements described by a group of experts in the domain of business trips. Such requirements gave rise to a set of BDD stories that have been used to automatically assess scenarios extracted from task models that were reengineered from an existing web system for booking business trips. The results have shown our approach, by performing a static analysis of the source files, was able to identify different types of inconsistencies between the user requirements and the set of task models analyzed.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394979"}, {"primary_key": "2466717", "vector": [], "sparse_vector": [], "title": "&quot;<PERSON> Hope This Is Helpful&quot;: Understanding Crowdworkers&apos; Challenges and Motivations for an Image Description Task.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AI image captioning challenges encourage broad participation in designing algorithms that automatically create captions for a variety of images and users. To create large datasets necessary for these challenges, researchers typically employ a shared crowdsourcing task design for image captioning. This paper discusses findings from our thematic analysis of 1,064 comments left by Amazon Mechanical Turk workers using this task design to create captions for images taken by people who are blind. Workers discussed difficulties in understanding how to complete this task, provided suggestions of how to improve the task, gave explanations or clarifications about their work, and described why they found this particular task rewarding or interesting. Our analysis provides insights both into this particular genre of task as well as broader considerations for how to employ crowdsourcing to generate large datasets for developing AI algorithms.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415176"}, {"primary_key": "2466718", "vector": [], "sparse_vector": [], "title": "For You, or For&quot;You&quot;?: Everyday LGBTQ+ Encounters with TikTok.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Online communities provide spaces for people who are vulnerable and underserved to seek support and build community, such as LGBTQ+ people. Today, some online community spaces are mediated by algorithms. Scholarship has found that algorithms have become deeply embedded in the systems that mediate our routine engagements with the world. Yet, little is known about how these systems impact those who are most vulnerable in society. In this paper, we focus on people's everyday experiences with one algorithmic system, the short video sharing application TikTok. TikTok recently received press that it was suppressing and oppressing the identities of its growing LGBTQ+ user population through algorithmic and human moderation of LGBTQ+ creators and content related to LGBTQ+ identity. Through an interview study with 16 LGBTQ+ TikTok users, we explore people's everyday engagements and encounters with the platform. We find that TikTok's For You Page algorithm constructs contradictory identity spaces that at once support LGBTQ+ identity work and reaffirm LGBTQ+ identity, while also transgressing and violating the identities of individual users. We also find that people are developing self-organized practices in response to these transgressions and violations. We discuss the implications of algorithmic systems on people's identity work, and introduce the concept of algorithmic exclusion, and explore how people are building resilience following moments of algorithmic exclusion.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432951"}, {"primary_key": "2466719", "vector": [], "sparse_vector": [], "title": "Informational Friction as a Lens for Studying Algorithmic Aspects of Privacy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper addresses challenges in conceptualizing privacy posed by algorithmic systems that can infer sensitive information from seemingly innocuous data. This type of privacy is of imminent concern due to the rapid adoption of machine learning and artificial intelligence systems in virtually every industry. In this paper, we suggest informational friction, a concept from <PERSON><PERSON><PERSON><PERSON>'s ethics of information, as a valuable conceptual lens for studying algorithmic aspects of privacy. Informational friction describes the amount of work required for one agent to access or alter the information of another. By focusing on amount of work, rather than the type of information or manner in which it is collected, informational friction can help to explain why automated analyses should raise privacy concerns independently of, and in addition to, those associated with data collection. As a demonstration, this paper analyze law enforcement use of facial recognition, andFacebook's targeted advertising model using informational friction and demonstrate risks inherent to these systems which are not completely identified in another popular framework, <PERSON><PERSON><PERSON>'s Contextual Integrity.The paper concludes with a discussion of broader implications, both for privacy research and for privacy regulation.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415172"}, {"primary_key": "2466720", "vector": [], "sparse_vector": [], "title": "C-Reference: Improving 2D to 3D Object Pose Estimation Accuracy via Crowdsourced Joint Object Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Converting widely-available 2D images and videos, captured using an RGB camera, to 3D can help accelerate the training of machine learning systems in spatial reasoning domains ranging from in-home assistive robots to augmented reality to autonomous vehicles. However, automating this task is challenging because it requires not only accurately estimating object location and orientation, but also requires knowing currently unknown camera properties (e.g., focal length). A scalable way to combat this problem is to leverage people's spatial understanding of scenes by crowdsourcing visual annotations of 3D object properties. Unfortunately, getting people to directly estimate 3D properties reliably is difficult due to the limitations of image resolution, human motor accuracy, and people's 3D perception (i.e., humans do not \"see\" depth like a laser range finder). In this paper, we propose a crowd-machine hybrid approach that jointly uses crowds' approximate measurements of multiple in-scene objects to estimate the 3D state of a single target object. Our approach can generate accurate estimates of the target object by combining heterogeneous knowledge from multiple contributors regarding various different objects that share a spatial relationship with the target object. We evaluate our joint object estimation approach with 363 crowd workers and show that our method can reduce errors in the target object's 3D location estimation by over 40%, while requiring only $35$% as much human time. Our work introduces a novel way to enable groups of people with different perspectives and knowledge to achieve more accurate collective performance on challenging visual annotation tasks.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392858"}, {"primary_key": "2466721", "vector": [], "sparse_vector": [], "title": "On Moderating Software Crowdsourcing Challenges.", "authors": ["<PERSON><PERSON><PERSON><PERSON> R. <PERSON>. <PERSON>", "Letícia S. Machado", "<PERSON> R. <PERSON>"], "summary": "Crowdsourcing divides a task into small pieces that are carried out by the crowd. In Software Engineering, crowdsourcing divides the software development tasks of to be carried out online by the crow and is simply called Software Crowdsourcing (SW CS). Most SW CS platforms support this emerging software development strategy and operate within a framework of competition among the crowd. Competitive SW CS platforms intentionally minimize communication and collaboration among the parties involved (customer, platform, and crowd) while they compete in the software development tasks. The goal of this paper is to investigate platform moderators in SW CS challenges. Platform moderators are individuals who work for the SW CS platforms to mediate customer and crowd. A qualitative analysis of the content of the communication forums hosted on the TopCoder platform was performed to analyze the messages exchanged by the platform moderators and the crowd. Our empirical results indicate that co-pilots enforce and, at the same time, extend the limitations of the documentation associated with the tasks to support crowd members, provide technical help to crowd members during the competitions, and engage the crowd in the challenges. Co-pilots are organized, work diligently, worrying about being fair, and, at the same time, seeking to find a balance between autonomy and dependency on the customer. We conclude by providing insights to improve the design of software crowdsourcing platforms.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375194"}, {"primary_key": "2466722", "vector": [], "sparse_vector": [], "title": "Parallel Journeys of Patients with Cancer and Depression: Challenges and Opportunities for Technology-Enabled Collaborative Care.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Depression is common but under-treated in patients with cancer, despite being a major modifiable contributor to morbidity and early mortality. Integrating psychosocial care into cancer services through the team-based Collaborative Care Management (CoCM) model has been proven to be effective in improving patient outcomes in cancer centers. However, there is currently a gap in understanding the challenges that patients and their care team encounter in managing co-morbid cancer and depression in integrated psycho-oncology care settings. Our formative study examines the challenges and needs of CoCM in cancer settings with perspectives from patients, care managers, oncologists, psychiatrists, and administrators, with a focus on technology opportunities to support CoCM. We find that: (1) patients with co-morbid cancer and depression struggle to navigate between their cancer and psychosocial care journeys, and (2) conceptualizing co-morbidities as separate and independent care journeys is insufficient for characterizing this complex care context. We then propose the parallel journeys framework as a conceptual design framework for characterizing challenges that patients and their care team encounter when cancer and psychosocial care journeys interact. We use the challenges discovered through the lens of this framework to highlight and prioritize technology design opportunities for supporting whole-person care for patients with co-morbid cancer and depression.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392843"}, {"primary_key": "2466723", "vector": [], "sparse_vector": [], "title": "Seeing in Context: Traditional Visual Communication Practices in Rural Bangladesh.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is a risk that modern practices of information communication and visualization in human-computer interaction can sideline communities due to their prioritization of scientific rationality. Such ideological hegemony can complicate interactions with data and computers, especially for low-literate communities in the global south. Through a six-month long ethnographic study with Nakshi-<PERSON><PERSON> makers, Hindu Idol makers, and witchcraft practitioners, we investigated how rural practitioners use their own forms of representation and narrative in record keeping, social and religious storytelling, and information mediated decision making. We find that traditionally developed approaches towards presenting and communicating information often make use of concrete units to represent entities and connect to designers' cultural practices and the physical location. Further, we identify how medium has significant influence in meaning-making. Often these strategies and conventions are passed down through generations within the community. In this paper, we discuss how this rural tradition differs from the modern information communication practices, discussing how an understanding of traditional practices for representing information can be useful in developing more accessible, and culturally appropriate modern tools and technologies for the people of rural Bangladesh and similar communities.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432913"}, {"primary_key": "2466724", "vector": [], "sparse_vector": [], "title": "Real Differences between OT and CRDT in Correctness and Complexity for Consistency Maintenance in Co-Editors.", "authors": ["<PERSON>", "Chengzheng Sun", "<PERSON><PERSON><PERSON>", "Weiwei Cai"], "summary": "OT (Operational Transformation) was invented for supporting real-time co-editors in the late 1980s and has evolved to become core techniques widely used in today's working co-editors and adopted in industrial products. CRDT (Commutative Replicated Data Type) for co-editors was first proposed around 2006, under the name of WOOT (WithOut Operational Transformation). Follow-up CRDT variations are commonly labeled as \"post-OT\" techniques capable of making concurrent operations natively commutative in co-editors. On top of that, CRDT solutions have made broad claims of superiority over OT solutions, and often portrayed OT as an incorrect and inefficient technique. Over one decade later, however, CRDT is rarely found in working co-editors; OT remains the choice for building the vast majority of today's co-editors. Contradictions between the reality and CRDT's purported advantages have been the source of much confusion and debate among co-editing researcher sand developers. To seek truth from facts, we set out to conduct a comprehensive and critical review on representative OT and CRDT solutions and co-editors based on them. From this work, we have made important discoveries about OT and CRDT, and revealed facts and evidences that refute CRDT claims over OT on all accounts. These discoveries help explain the underlying reasons for the choice between OT and CRDT in the real world. We report these results in a series of three articles. In the second article of this series, we reveal the differences between OT and CRDT in their basic approaches to realizing the same general transformation and how such differences had resulted in different challenges and consequential correctness and complexity issues. Moreover, we reveal hidden complexity and algorithmic flaws with representative CRDT solutions, and discuss common myths and facts related to correctness and complexity of OT and CRDT.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392825"}, {"primary_key": "2466725", "vector": [], "sparse_vector": [], "title": "Real Differences between OT and CRDT under a General Transformation Framework for Consistency Maintenance in Co-Editors.", "authors": ["Chengzheng Sun", "<PERSON>", "<PERSON><PERSON><PERSON>", "Weiwei Cai", "<PERSON><PERSON><PERSON>"], "summary": "OT (Operational Transformation) was invented for supporting real-time co-editors in the late 1980s and has evolved to become a collection of core techniques widely used in today's working co-editors and adopted in major industrial products. CRDT (Commutative Replicated Data Type) for co-editors was first proposed around 2006, under the name of WOOT (WithOut Operational Transformation). Follow-up CRDT variations are commonly labeled as \"post-OT\" techniques capable of making concurrent operations natively commutativity in co-editors. On top of that, CRDT solutions have made broad claims of superiority over OT solutions, and routinely portrayed OT as an incorrect, complex and inefficient technique. Over one decade later, however, CRDT is rarely found in working co-editors, and OT remains the choice for building the vast majority of today's co-editors. Contradictions between the reality and CRDT's purported advantages have been the source of much confusion and debate in co-editing research and developer communities. Have the vast majority of co-editors been unfortunate in choosing the faulty and inferior OT, or those CRDT claims are false? What are the real differences between OT and CRDT for co-editors? What are the key factors and underlying reasons behind the choices between OT and CRDT in the real world? A thorough examination of these questions is relevant not only to researchers who are exploring the frontiers of co-editing technologies and systems, but also to practitioners who are seeking viable techniques to build real world applications. To seek truth from facts, we set out to conduct a comprehensive and critical review on representative OT and CRDT solutions and working co-editors based on them. From this work, we have made important discoveries about OT and CRDT, and revealed facts and evidences that refute CRDT claims over OT on all accounts. We report our discoveries in a series of articles and the current article is the first one in this series. In this paper, we present a general transformation framework for consistency maintenance in co-editors, which was distilled from dissecting and examining representative OT and CRDT solutions (and other alternative solutions) during this work, and report our discoveries under the guidance of this framework. In particular, we reveal that CRDT is like OT in following a general transformation approach, but achieves the same transformation indirectly, in contrast to OT direct transformation approach; and CRDT is not natively commutative for concurrent co-editing operations, but has to achieve the same OT commutativity indirectly as well, with consequential correctness and complexity issues. Uncovering the hidden transformation nature and demystifying the commutativity property of CRDT provides much-needed clarity about what CRDT really is and is not to co-editing, and serves as the foundation to explore the real differences between OT and CRDT in correctness, complexity, implementation, and real world applications, which are reported in follow-up articles. We hope discoveries from this work help clear up common misconceptions and confusions surrounding OT and CRDT, and accelerate progress in co-editing technology for real world applications.?", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375186"}, {"primary_key": "2466727", "vector": [], "sparse_vector": [], "title": "Food Aid Technology: The Experience of a Syrian Refugee Community in Coping with Food Insecurity.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Over half of Syrian refugee households in Lebanon are food insecure with some reliant on an electronic voucher (e-voucher) system for food aid. The interplay between the digitisation of food aid, within the socio-technical context of refugees, and community collaborative practices is yet to be investigated. Through design engagements and interviews with refugees and shop owners we explore the experiences of a Syrian refugee community in Lebanon using the e-voucher system. We provide insights into the socio-technical environment in which the e-voucher system is dispensing aid, the information and power asymmetries experienced, refugee collaborative coping practices and how they interplay with the e-voucher system. We highlight the need for: (1) expanding refugee digital capabilities to encompass understandings of aid technologies and identifying trusted intermediaries and (2) for technologies to support in upholding humanitarian principles and mitigating power and information asymmetries. Lastly, we call for CSCW researchers and humanitarian innovators to consider how humanitarian technologies can enable refugee collaborative practices and adopt everyday security as a lens for designing aid technologies. The paper contributes to CSCW knowledge regarding the interplay between aid technologies and refugees? socio-technical contexts and practices that provides a basis for future technological designs for collaborative technologies for refugee food security.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415205"}, {"primary_key": "2466728", "vector": [], "sparse_vector": [], "title": "A System for Interleaving Discussion and Summarization in Online Collaboration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In many instances of online collaboration, ideation and deliberation about what to write happen separately from the synthesis of the deliberation into a cohesive document. However, this may result in a final document that has little connection to the discussion that came before. In this work, we present interleaved discussion and summarization, a process where discussion and summarization are woven together in a single space, and collaborators can switch back and forth between discussing ideas and summarizing discussion until it results in a final document that incorporates and references all discussion points. We implement this process into a tool called Wikum+ that allows groups working together on a project to create living summaries-artifacts that can grow as new collaborators, ideas, and feedback arise and shrink as collaborators come to consensus. We conducted studies where groups of six people each collaboratively wrote a proposal using Wikum+ and a proposal using a messaging platform along with Google Docs. We found that Wikum+'s integration of discussion and summarization helped users be more organized, allowing for light-weight coordination and iterative improvements throughout the collaboration process. A second study demonstrated that in larger groups, Wikum+ is more inclusive of all participants and more comprehensive in the final document compared to traditional tools.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432940"}, {"primary_key": "2466729", "vector": [], "sparse_vector": [], "title": "&quot;They Just Don&apos;t Get It&quot;: Towards Social Technologies for Coping with Interpersonal Racism.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over 35% of Americans belong to racial minority groups. Racism targeting these individuals results in a range of harmful physical, psychological, and practical consequences. The present work aims to shed light on the current sense-making and support-seeking practices exhibited by targets of racism, as well as to identify the core needs and barriers that future socio-technical interventions could potentially address. The long-term goal of this work is to understand how CSCW researchers and designers could best support members of marginalized groups to make sense of and to seek support for experiences with racism. Narrative episode interviews with targets of racism revealed a number of key entry points for intervention. For example, participants' personal stories confirmed that uncertainty, both about the nature and consequences of the experience of racism, is a key motivator for support-seeking. In addition, despite the need for support, participants largely do not trust public forms of social media for support-seeking. We discuss how participants' accounts of the complex labor involved in determining who \"gets it\" in identifying potential supporters, and in navigating the complexities of trust and agency in sharing their experiences, present clear implications for the design of new socio-technical platforms for members of racial minority groups.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392828"}, {"primary_key": "2466730", "vector": [], "sparse_vector": [], "title": "Can you Turn it Off?: The Spatial and Social Context of Mobile Disturbance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Contemporary mobile devices continuously interrupt people with notifications in various and changing physical environments. As different places can have different social setting, understanding how disturbing an interruption might be to people around the user is not a straightforward task. To understand how users perceive disturbance in their social environment, we analyze the results of a 3-week user study with 50 participants using the experience sampling method and log analysis. We show that perceptions of disturbance are strongly related to the social norms surrounding the place, such as whether the place is considered private or public, even when controlling for the number of people around the user. Furthermore, users' perceptions of disturbance are also related to the activity carried out on the phone, and the subjective perceptions of isolation from other people in the space. We conclude the paper by discussing how our findings can be used to design new mobile devices that are aware of the social norms and their users' environmental context.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415162"}, {"primary_key": "2466731", "vector": [], "sparse_vector": [], "title": "Hidden Figures: Roles and Pathways of Successful OSS Contributors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open Source Software (OSS) development is a collaborative endeavor where expert developers, distributed around the globe create software solutions. Given this characteristic, OSS communities have been studied as technical communities, where stakeholders join and evolve in their careers based on their (often voluntary) code contributions to the project. However, the OSS landscape is slowly changing with more people and companies getting involved in OSS. This means that projects now need people in non-technical roles and activities to keep the project sustainable and evolving. In this paper, we focus on understanding the roles and activities that are part of the current OSS landscape and the different career pathways in OSS. By conducting and analyzing 17 interviews with OSS contributors who are well known in the community, we provide empirical evidence of the existence and importance of community-centric roles (e.g advocate, license manager, community founder) in addition to the well-known project-centric ones (e.g maintainer, core member). However, the community-centric roles typically remain hidden, since these roles may not leave traces in software repositories typically analyzed by researchers. We found that people can build a career in OSS through different roles and activities, with different backgrounds, including those not related to writing software. Furthermore, people's career pathways are fluid, moving between project and community-centric roles. Our work highlights that communities and researchers need to take action to acknowledge the importance of these varied roles, making these roles visible and well-recognized, which can ultimately help attract and retain more people in the OSS projects.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415251"}, {"primary_key": "2466734", "vector": [], "sparse_vector": [], "title": "ProtoTeams: Supporting Team Dating in Co-Located Settings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Team dating, or small-group interactions, can expose people to diverse perspectives and inform the potential for longer-term collaboration. However, rapidly configuring groups and facilitating interactions among strangers can be difficult, especially in co-located settings. We present ProtoTeams, a system that leverages personal mobile devices to support rapid group formation, to facilitate group activities, and to collect data about the potential for future collaboration. We report on a field study where 406 students in eight different project-based classes used ProtoTeams to interact with classmates through multiple rounds of brief discussion activities before selecting teammates for a term project. We found that the system enables groups to form in about one minute, allows for meaningful interactions with a diverse range of peers, and can significantly influence subsequent teammate selection. We discuss design implications and challenges for in-person team dating in classrooms and other contexts.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434182"}, {"primary_key": "2466735", "vector": [], "sparse_vector": [], "title": "&quot;At the End of the Day Facebook Does What ItWants&quot;: How Users Experience Contesting Algorithmic Content Moderation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Interest has grown in designing algorithmic decision making systems for contestability. In this work, we study how users experience contesting unfavorable social media content moderation decisions. A large-scale online experiment tests whether different forms of appeals can improve users' experiences of automated decision making. We study the impact on users' perceptions of the Fairness, Accountability, and Trustworthiness of algorithmic decisions, as well as their feelings of Control (FACT). Surprisingly, we find that none of the appeal designs improve FACT perceptions compared to a no appeal baseline. We qualitatively analyze how users write appeals, and find that they contest the decision itself, but also more fundamental issues like the goal of moderating content, the idea of automation, and the inconsistency of the system as a whole. We conclude with suggestions for -- as well as a discussion of the challenges of -- designing for contestability.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415238"}, {"primary_key": "2466736", "vector": [], "sparse_vector": [], "title": "Birds of a Caste - How Caste Hierarchies Manifest in Retweet Behavior of Indian Politicians.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Hindu caste system plays an important role in the socio-political landscape of India. In recent years, Indian politicians have moved a lot of direct communication online. Social media is now an important space for the articulation and performance of their political positions. In this paper, we study ways in which the political performance of caste relations can be captured from the online connections and messaging of parliamentarians in India. We run tests of odds ratios among the members of LokSabha (lower house) of India to find the extent to which their engagement is insular to their own caste group versus other groups. We observe that in the LokSabha network, Members of Parliament (MPs) have higher odds of getting retweeted by others whose caste is the same or closer to their own in the caste hierarchy. The findings of this research shed light on an understudied, yet critical, social relation of caste in the study of political behavior on social media.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432911"}, {"primary_key": "2466737", "vector": [], "sparse_vector": [], "title": "Toward Accurate Sensing with Knitted Fabric: Applications and Technical Considerations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fabric sensors have been introduced to enable flexible touch-based interaction. We advance the technical capabilities of a scalable and low-profile knitted capacitive touch sensing system by introducing methods to improve its touch localization accuracy. The sensor hardware design tends toward minimalism by using a single conductive yarn and two external connections located at each endpoint. Fewer connectors simplify the textile system integration, but this comes at the expense of reduced signal information output from the system. The electrical continuity of the sensing element, essential to the process of knitting, also increases the uncertainty of localizing touch. We propose using Bode analysis to measure changes in signal due to capacitive touch, as well as design a new algorithm, MSD, which retains the most significant aspects of the signal in terms of touch location identification. We do not classify location of touch, but focus on an invariant signal representation. To evaluate our methods, we introduce ELD, a distance metric to compute the similarity of pairs of key-presses, generalizable to computing distances of tensors of varying lengths. Our experiments show that the proposed sensing method results in high-fidelity signals. Furthermore, the sparse representation of key-presses produced by MSD significantly increases separability between different touch locations. Possible applications based on these sensors are also illustrated through prototypes and use case descriptions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3394981"}, {"primary_key": "2466738", "vector": [], "sparse_vector": [], "title": "Editor&apos;s Welcome.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is our pleasure to welcome you to this PACMHCI GROUP issue. For over 25 years, the GROUP research community has supported the development of robust scholarship at the intersection of Computer Supported Cooperative Work, Human Computer Interaction, Computer Supported Collaborative Learning and Socio-Technical Studies. This volume is the latest product of our efforts to validate and integrate the strong work happening within this broadly-conceived community. We hope that the range of papers presented here reflects our intent to be international, interdisciplinary, and inclusive, both in our organization of the review process as well as within the set of accepted papers. This PACMHCI journal volume in the GROUP series features studies of collaboration in multiple settings, including social media, online communities and game development. It also showcases architectures and frameworks for collaboration, and sociotechnical studies in domains ranging from sports to fake news. As Editors, we are particularly pleased to continue the legacy of presenting and disseminating the new and exciting work being developed in our community. We come together every two years to creatively display and build off of ideas from a wide range of disciplinary and topical areas including computer science, design, engineering, information science, management science, sociology, work and labor studies, and values-in-design, among others.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375181"}, {"primary_key": "2466739", "vector": [], "sparse_vector": [], "title": "Looking for a Deal?: Visual Social Attention during Negotiations via Mixed Media Videoconferencing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sidney D&apos;Mello"], "summary": "Whereas social visual attention has been examined in computer-mediated (e.g., shared screen) or video-mediated (e.g., FaceTime) interaction, it has yet to be studied in mixed-media interfaces that combine video of the conversant along with other UI elements. We analyzed eye gaze of 37 dyads (74 participants) who were tasked with negotiating the price of a new car (as a buyer and seller) using mixed-media video conferencing under competitive or cooperative negotiation instructions (experimental manipulation). We used multidimensional recurrence quantification analysis to extract spatio-temporal patterns corresponding to mutual gaze (individuals look at each other), joint attention (individuals focus on the same elements of the interface), and gaze aversion (an individual looks at their partner, who is looking elsewhere). Our results indicated that joint attention predicted the sum of points attained by the buyer and seller (i.e., the joint score). In contrast, gaze aversion was associated with faster time to complete the negotiation, but with a lower joint score. Unexpectedly, mutual gaze was highly infrequent and unrelated to the negotiation outcomes and none of the gaze patterns predicted subjective perceptions of the negotiation. There were also no effects of gender composition or negotiation condition on the gaze patterns or negotiation outcomes. Our results suggest that social visual attention may operate differently in mixed-media collaborative interfaces than in face-to-face interaction. As mixed-media collaborative interfaces gain prominence, our work can be leveraged to inform the design of gaze-sensitive user interfaces that support remote negotiations among other tasks.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434169"}, {"primary_key": "2466740", "vector": [], "sparse_vector": [], "title": "Sketchy: Drawing Inspiration from the Crowd.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In-person user studies show that designers draw inspiration by looking at their peers' work while sketching. To recreate this behavior in a virtual environment, we developed Sketchy, a web-based drawing application where users sketch in virtual rooms and use the \"Peek'' functionality to gain ideas from their peers' sketches in real-time. To assess if \"Peek'' supports individual creativity through finding inspiration, students from a Human-Computer Interaction class sketched user interface design tasks in two studies. Study 1 compares creativity measures with and without <PERSON><PERSON> between two groups of students, where self-reports reveal <PERSON><PERSON> increases satisfaction with their final sketch and better supports individual creativity. Study 2 took place in a large classroom, where 90 students, all with Peek enabled, completed different design tasks. Peeking led students to report an intention to change their sketch 18% of the time in Study 1 and 17% of the time in Study 2. Student designers were influenced by sketches that seem closer to completion, contain more details, and are carefully drawn. They were also about three times more likely to clear their canvas and start over if they found a sketch inspirational. Furthermore, sketches created by students with more sketching and design experience influence less experienced student designers. This work explores the directions and benefits of incorporating digital peeking to support individual creativity within a student designer's classroom experience to create more satisfactory final sketches.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415243"}, {"primary_key": "2466741", "vector": [], "sparse_vector": [], "title": "More Than a Click: Exploring College Students&apos; Decision-Making Processes in Online News Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Fake news can be disseminated on social media platforms due to users' inability to distinguish it from true news as well as the nature of heuristic-driven online sharing. We use in-depth semi-structured interviews with 20 college student social media users to help build a picture of their sharing processes that can help platform designers and operators better combat fake news dissemination. Participants reflected on specific recently shared news pieces, including how the stories attracted attention, why they shared them, and their behavior after sharing. Interview results revealed different cognitive processes people engaged: before, during and after sharing. Before sharing, participants assessed the news content and evaluated the social value of sharing the particular news piece. During sharing, the audience of the post was selected. After sharing, users examined the feedback from others and revisited previous sharing decisions. The study highlights that decisions about news sharing involve a myriad of different considerations throughout different cognitive stages. Based on these findings, we discuss design implications for combating the sharing of fake news.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375189"}, {"primary_key": "2466742", "vector": [], "sparse_vector": [], "title": "In Their Shoes: A Structured Analysis of Job Demands, Resources, Work Experiences, and Platform Commitment of Crowdworkers in China.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Despite the growing interest in crowdsourcing, this new labor model has recently received severe criticism. The most important point of this criticism is that crowdworkers are often underpaid and overworked. This severely affects job satisfaction and productivity. Although there is a growing body of evidence exploring the work experiences of crowdworkers in various countries, there have been a very limited number of studies to the best of our knowledge exploring the work experiences of Chinese crowdworkers. In this paper we aim to address this gap. Based on a framework of well-established approaches, namely the Job Demands-Resources model, the Work Design Questionnaire, the Oldenburg Burnout Inventory, the Utrecht Work Engagement Scale, and the Organizational Commitment Questionnaire, we systematically study the work experiences of 289 crowdworkers who work for ZBJ.com - the most popular Chinese crowdsourcing platform. Our study examines these crowdworker experiences along four dimensions: (1) crowdsourcing job demands, (2) job resources available to the workers, (3) crowdwork experiences, and (4) platform commitment. Our results indicate significant differences across the four dimensions based on crowdworkers' gender, education, income, job nature, and health condition. Further, they illustrate that different crowdworkers have different needs and threshold of demands and resources and that this plays a significant role in terms of moderating the crowdwork experience and platform commitment. Overall, our study sheds light to the work experiences of the Chinese crowdworkers and at the same time contributes to furthering understandings related to the work experiences of crowdworkers.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3375187"}, {"primary_key": "2466743", "vector": [], "sparse_vector": [], "title": "How Experts Detect Phishing Scam Emails.", "authors": ["<PERSON>"], "summary": "Phishing scam emails are emails that pretend to be something they are not in order to get the recipient of the email to undertake some action they normally would not. While technical protections against phishing reduce the number of phishing emails received, they are not perfect and phishing remains one of the largest sources of security risk in technology and communication systems. To better understand the cognitive process that end users can use to identify phishing messages, I interviewed 21 IT experts about instances where they successfully identified emails as phishing in their own inboxes. IT experts naturally follow a three-stage process for identifying phishing emails. In the first stage, the email recipient tries to make sense of the email, and understand how it relates to other things in their life. As they do this, they notice discrepancies: little things that are \"off'' about the email. As the recipient notices more discrepancies, they feel a need for an alternative explanation for the email. At some point, some feature of the email --- usually, the presence of a link requesting an action --- triggers them to recognize that phishing is a possible alternative explanation. At this point, they become suspicious (stage two) and investigate the email by looking for technical details that can conclusively identify the email as phishing. Once they find such information, then they move to stage three and deal with the email by deleting it or reporting it. I discuss ways this process can fail, and implications for improving training of end users about phishing.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415231"}, {"primary_key": "2466745", "vector": [], "sparse_vector": [], "title": "Parallel Worlds: Repeated Initializations of the Same Team to Improve Team Viability.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "N&apos;godjigui Junior Diarrassouba", "<PERSON><PERSON>", "<PERSON>"], "summary": "A team's early interactions are influential: small behaviors cascade, driving the team either toward successful collaboration or toward fracture. Would a team be more viable if it could undo initial interactional missteps and try again? We introduce a technique that supports online and remote teams in creating multiple parallel worlds: the same team meets many times, led to believe that each convening is with a new team due to pseudonym masking while actual membership remains static. Afterward, the team moves forward with the parallel world with the highest viability by using the same pseudonyms and conversation history from that instance. In two experiments, we find that this technique improves team viability: teams that are reconvened from the highest-viability parallel world are significantly more viable than the same group meeting in a new parallel world. Our work suggests parallel worlds can help teams start off on the right foot - and stay there.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392877"}, {"primary_key": "2466746", "vector": [], "sparse_vector": [], "title": "Quantifying the Effect of Social Presence on Online Social Conformity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Social conformity occurs when individuals in group settings change their personal opinion to be in agreement with the majority's position. While recent literature frequently reports on conformity in online group settings, the causes for online conformity are yet to be fully understood. This study aims to understand how social presencei.e., the sense of being connected to others via mediated communication, influences conformity among individuals placed in online groups while answering subjective and objective questions. Acknowledging its multifaceted nature, we investigate three aspects of online social presence: user representation (generic vs.user-specific avatars), interactivity (discussion vs.no discussion ), and response visibility (public vs.private ). Our results show an overall conformity rate of 30% and main effects from task objectivity, group size difference between the majority and the minority, and self-confidence on personal answer. Furthermore, we observe an interaction effect between interactivity and response visibility, such that conformity is highest in the presence of peer discussion and public responses, and lowest when these two elements are absent. We conclude with a discussion on the implications of our findings in designing online group settings, accounting for the effects of social presence on conformity.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392863"}, {"primary_key": "2466748", "vector": [], "sparse_vector": [], "title": "Combating the Spread of Coronavirus by Modeling Fomites with Depth Cameras.", "authors": ["<PERSON>"], "summary": "Coronavirus is thought to spread through close contact from person to person. While it is believed that the primary means of spread is by inhaling respiratory droplets or aersols, it may also be spread by touching inanimate objects such as doorknobs and handrails that have the virus on it (\"fomites''). The Centers for Disease Control and Prevention (CDC) therefore recommends individuals maintain \"social distance'' of more than six feet between one another. It further notes that an individual may be infected by touching a fomite and then touching their own mouth, nose or possibly their eyes. We propose the use of computer vision techniques to combat the spread of coronavirus by sounding an audible alarm when an individual touches their own face, or when multiple individuals come within six feet of one another or shake hands. We further propose using depth cameras to track where people touch parts of their physical environment throughout the day, and a simple model of disease spread among potential fomites. Projection mapping techniques can be used to display likely fomites in realtime, while headworn augmented reality systems can be used by custodial staff to perform more effective cleaning of surfaces. Such techniques may find application in particularly vulnerable settings such as schools, long-term care facilities and physician offices.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427331"}, {"primary_key": "2466749", "vector": [], "sparse_vector": [], "title": "Subtletee: Augmenting Posture Awareness for Beginner Golfers.", "authors": ["Mikolaj P<PERSON> Wozniak", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "While the sport of golf offers unique opportunities for people of all ages to be active, it also requires mastering complex movements before it can be fully enjoyed. Extensive training is required to begin achieving any success, which may demotivate prospective players away from the sport. To aid beginner golfers and enable them to practice without supervision, we designed Subtletee---a system that enhances the bodily awareness of golfers by providing feedback on the position of their feet and elbows. We first identified key problems golf beginners face through expert interviews. We then evaluated different feedback modalities for Subtletee---visual, tactile and auditory in an experiment with 20 beginner golfers. We found that providing proprioception feedback increased the users' performance on the driving range, with tactile feedback providing the most benefits. Our work provides insights on delivering feedback during physical activity that requires high precision movements.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427332"}, {"primary_key": "2466750", "vector": [], "sparse_vector": [], "title": "Better Feedback from Nicer People: Narrative Empathy and Ingroup Framing Improve Feedback Exchange.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Online feedback exchange platforms enable content creators to collect a diverse set of design feedback quickly. However, creators can experience low quality and harsh feedback when using such platforms. In this paper, we leverage the empathy of the feedback provider to address both these issues. Specifically, we tested two narrative-based empathy arousal interventions: a negative experience and a design process narrative. We also examined whether ingroup framing further enhances the effects of empathy arousal. In a 3x2 online experiment, participants (n=205) wrote feedback on a poster design after experiencing one of the intervention conditions or a control condition. Our results show both the design process narrative and ingroup framing conditions significantly increased the feedback quality and effort invested in the task. The negative experience narrative condition had similar effects and participants reported significantly increased disapproval towards harsh feedback. We discuss the implications of our results for the design of feedback exchange platforms.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432935"}, {"primary_key": "2466751", "vector": [], "sparse_vector": [], "title": "Understanding and Predicting the Burst of Burnout via Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Job burnout is a special type of work-related stress that is prevalent in our modern society, and constant burnout is extremely harmful for people's physical health and emotional wellbeing. Traditional studies for burnout mainly rely on surveys/questionnaires, which have revealed several interesting findings but are of high cost and very time consuming. With the prevalence of social networking applications, we aim to re-investigate the burnout phenomenon in a novel perspective. In this paper, we collected a dataset consisting of 1532 burnout Weibo users with their postings. Based on the previous literature, we propose a number of hypotheses about what might be the burst signal of the burnout from the perspective of language, time and interaction. Furthermore, extensive correlation analysis is conducted to investigate if these hypotheses are supported, which leads to a number of interesting findings. Finally, we develop machine learning models to predict the burst of burnout based on extracted features and achieve a relatively high accuracy, which reveals potential implications in early-stage intervention.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3434174"}, {"primary_key": "2466752", "vector": [], "sparse_vector": [], "title": "Using Cultural Probes in HCI4D/ICTD: A Design Case Study from Bungoma, Kenya.", "authors": ["<PERSON>"], "summary": "Cultural probes have long been used in HCI to provide designers with glimpses into the local cultures for which they are designing, and thereby inspire novel design proposals. HCI4D/ICTD researchers are increasingly interested in more deeply understanding local cultures in the developing regions where they work, in designing technologies that are not strictly related to socioeconomic development, and in considering new design approaches. However, few use this subjective, design-led method in their research. In this paper, I present a case study detailing my experience designing and deploying cultural probes in Bungoma, Kenya. Returns from my comment cards and digital camera activities draw attention to probe recipients' unique experiences and to Bungoma's distinctive characteristics; they also inspired a series of speculative design proposals. My experience motivates a discussion that elaborates on how a cultural probes approach can benefit HCI4D/ICTD research by raising questions about generalizability, objectivity, and the pursuit of a single solution in design. More broadly, I offer a case study demonstrating an alternative way to approach design in HCI4D/ICTD.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392873"}, {"primary_key": "2466753", "vector": [], "sparse_vector": [], "title": "Exploring Photography in Rural Kenyan Households: Considering &quot;Relational Objects&quot; in CSCW and HCI.", "authors": ["<PERSON>", "April <PERSON>", "<PERSON>"], "summary": "Domestic and personal photography are topics of longstanding interest to CSCW and HCI researchers. In this paper, we explore these topics in Bungoma County, Kenya. We used interview and observation methods to investigate how photographs are taken, displayed, organized, and shared, in 23 rural households. To more deeply understand participants' photography practices, we also gave them digital cameras, observed what they did with them, and asked them to engage in a photo-elicitation exercise. Our findings draw attention to the ways photographs are \"relational objects\" - that is, material objects that support the maintenance, reproduction, and transformation of social relations. We then describe these relations: economic (i.e., working as cameramen producing and distributing printed images); family (i.e., parents and children using printed images to tell family histories); and community (i.e., people using printed images to present an idealized self). The introduction of digital cameras into these households did not appear to change these practices; instead, it reinforced them. We discuss how considering relational objects in CSCW/HCI is useful for balancing the technologically determinist perspectives that are the basis of many prior studies of photography in these fields. In particular, we detail how considering the concept provides new perspectives on materiality, as well an alternative to the individualistic perspective, which underlies these communities' understanding of photography.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392852"}, {"primary_key": "2466754", "vector": [], "sparse_vector": [], "title": "Exploring Antecedents and Consequences of Toxicity in Online Discussions: A Case Study on Reddit.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Toxicity in online discussions has been an intriguing phenomenon and an important problem. In this paper, we seek to better understand toxicity dynamics in online discussions via a case study on Reddit that explores the antecedents and consequences of toxicity in text. We inspected two dimensions of toxicity: language toxicity, i.e. how toxic the text itself is; and toxicity elicitation, i.e. how much toxicity it elicits in its response. Through regression analyses on Reddit comments, we found that both author propensity and toxicity in discussion context were strong positive antecedents of language toxicity; meanwhile, language toxicity significantly increased the volume and user evaluation of the discussion in some sub-communities, while toxicity elicitation showed mixed effects. We then discuss how our results help understand and regulate toxicity in online discussions by interpreting the complicated triggers and outcomes of toxicity.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415179"}, {"primary_key": "2466755", "vector": [], "sparse_vector": [], "title": "Rethinking the Dual Gaussian Distribution Model for Predicting Touch Accuracy in On-screen-start Pointing Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The dual Gaussian distribution hypothesis has been used to predict the success rate of target pointing on touchscreens. <PERSON><PERSON> and <PERSON><PERSON> evaluated their success-rate prediction model in off-screen-start pointing tasks. However, we found that their prediction model could also be used for on-screen-start pointing tasks. We discuss the reasons why and empirically validate our hypothesis in a series of four experiments with various target sizes and distances. The prediction accuracy of <PERSON><PERSON> and <PERSON><PERSON>'s model was high in all of the experiments, with a 10-point absolute (or 14.9% relative) prediction error at worst. Also, we show that there is no clear benefit to integrating the target distance when predicting the endpoint variability and success rate.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427333"}, {"primary_key": "2466757", "vector": [], "sparse_vector": [], "title": "Expanding Side Touch Input on Mobile Phones: Finger Reachability and Two-Dimensional Taps and Flicks using the Index and Thumb.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate the performance of one-handed touch input on the side of a mobile phone. A first experiment examines grip change and subjective preference when reaching for side targets using different fingers. Results show all locations can be reached with at least one finger, but the thumb and index are most preferred and require less grip change for positions along the sides. Two following experiments examine taps and flicks using the thumb and index finger in a new two-dimensional input space. A side-touch sensor is simulated with a combination of capacitive sensing and motion tracking to distinguish touches on the lower, middle, or upper edges. When tapping, index and thumb speeds are similar with thumb more accurate and comfortable, and the lower edge is most reliable with the middle edge most comfortable. When flicking with the thumb, the upper edge is fast and rated highly.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427334"}, {"primary_key": "2466758", "vector": [], "sparse_vector": [], "title": "Understanding Women&apos;s Remote Collaborative Programming Experiences: The Relationship between Dialogue Features and Reported Perceptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, remote collaboration has become increasingly common both in the workplace and in the classroom. It is imperative that we understand and support remote collaborative problem solving, particularly understanding the experiences of people from historically marginalized groups whose intellectual contributions are essential for addressing the pressing needs society faces. This paper reports on a study in which 58 introductory computer science students constructed code remotely with a partner following either predefined structured roles (driver and navigator in pair programming) or without predefined structured roles. Between the structured-role and unstructured-role conditions, participants? normalized learning gain, Intrinsic Motivation Inventory scores, and system usability scores were not significantly different. However, regardless of the collaboration condition, women reported significantly higher levels of stress, lower levels of perceived competence, and less perceived choice compared to men. Because computer science is a context in which women have been historically marginalized, we next examined the relationship between student gender and collaborative dialogues by extracting lexical and sentiment features from the textual messages partners exchanged. Results reveal that dialogue features, such as number of utterances, utterance length, and partner sentiment, significantly correlated with women's reports of stress, perceived competence, or perceived choice. These findings provide insight on women's experiences in remote programming, suggest that dialogue features can predict their collaborative experiences, and hold implications for designing systems that help provide collaborative experiences in which everyone can thrive.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3432952"}, {"primary_key": "2466759", "vector": [], "sparse_vector": [], "title": "Designing Mid-Air Haptic Gesture Controlled User Interfaces for Cars.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present advancements in the design and development of in-vehicle infotainment systems that utilize gesture input and ultrasonic mid-air haptic feedback. Such systems employ state-of-the-art hand tracking technology and novel haptic feedback technology and promise to reduce driver distraction while performing a secondary task therefore cutting the risk of road accidents. In this paper, we document design process considerations during the development of a mid-air haptic gesture-enabled user interface for human-vehicle-interactions. This includes an online survey, business development insights, background research, and an agile framework component with three prototype iterations and user-testing on a simplified driving simulator. We report on the reasoning that led to the convergence of the chosen gesture-input and haptic-feedback sets used in the final prototype, discuss the lessons learned, and give hints and tips that act as design guidelines for future research and development of this technology in cars.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3397869"}, {"primary_key": "2466760", "vector": [], "sparse_vector": [], "title": "Vision Skills Needed to Answer Visual Questions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The task of answering questions about images has garnered attention as a practical service for assisting populations with visual impairments as well as a visual Turing test for the artificial intelligence community. Our first aim is to identify the common vision skills needed for both scenarios. To do so, we analyze the need for four vision skills--object recognition, text recognition, color recognition, and counting--on over 27,000 visual questions from two datasets representing both scenarios. We next quantify the difficulty of these skills for both humans and computers on both datasets. Finally, we propose a novel task of predicting what vision skills are needed to answer a question about an image. Our results reveal (mis)matches between aims of real users of such services and the focus of the AI community. We conclude with a discussion about future directions for addressing the visual question answering task.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415220"}, {"primary_key": "2466761", "vector": [], "sparse_vector": [], "title": "Mobile Collocated Gaming: Collaborative Play and Meaning-Making on a University Campus.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many mobile games are designed to be placeless, so that mobile device owners could play anytime, anywhere. But does such design erase the sense of place in mobile gaming? To investigate the spatiality of mobile gaming, we conducted an ethnographic study of mobile gaming in a Chinese university. We found mobile gaming as a form of collocated interaction, where participants collectively created meanings around distinctive places on the campus including dormitory, classroom, and laboratory, and engaged in collaborative play. Their mobile collocated gaming was shaped by the social, organizational, and cultural contexts of places. The spatiality of mobile collocated gaming entails both appropriation of the norms and expectations of places and creation of new meanings to negotiate tensions between mobile gaming and places. We further discuss the spatiality of mobile collocated gaming and broader social and cultural conditions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415213"}, {"primary_key": "2466762", "vector": [], "sparse_vector": [], "title": "Quantifying the Causal Effects of Conversational Tendencies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"], "summary": "Understanding what leads to effective conversations can aid the design of better computer-mediated communication platforms. In particular, prior observational work has sought to identify behaviors of individuals that correlate to their conversational efficiency. However, translating such correlations to causal interpretations is a necessary step in using them in a prescriptive fashion to guide better designs and policies. In this work, we formally describe the problem of drawing causal links between conversational behaviors and outcomes. We focus on the task of determining a particular type of policy for a text-based crisis counseling platform: how best to allocate counselors based on their behavioral tendencies exhibited in their past conversations. We apply arguments derived from causal inference to underline key challenges that arise in conversational settings where randomized trials are hard to implement. Finally, we show how to circumvent these inference challenges in our particular domain, and illustrate the potential benefits of an allocation policy informed by the resulting prescriptive information.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415202"}, {"primary_key": "2466764", "vector": [], "sparse_vector": [], "title": "How do Data Science Workers Collaborate? Roles, Workflows, and Tools.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today, the prominence of data science within organizations has given rise to teams of data science workers collaborating on extracting insights from data, as opposed to individual data scientists working alone. However, we still lack a deep understanding of how data science workers collaborate in practice. In this work, we conducted an online survey with 183 participants who work in various aspects of data science. We focused on their reported interactions with each other (e.g., managers with engineers) and with different tools (e.g., Jupyter Notebook). We found that data science teams are extremely collaborative and work with a variety of stakeholders and tools during the six common steps of a data science workflow (e.g., clean data and train model). We also found that the collaborative practices workers employ, such as documentation, vary according to the kinds of tools they use. Based on these findings, we discuss design implications for supporting data science team collaborations and future research directions.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392826"}, {"primary_key": "2466765", "vector": [], "sparse_vector": [], "title": "Configuring Audiences: A Case Study of Email Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When people communicate with each other, their choice of what to say is tied to their perceptions of the audience. For many communication channels, people have some ability to explicitly specify their audience members and the different roles they can play. While existing accounts of communication behavior have largely focused on how people tailor the content of their messages, we focus on the configuring of the audience as a complementary family of decisions in communication. We formulate a general description of audience configuration choices, highlighting key aspects of the audience that people could configure to reflect a range of communicative goals. We then illustrate these ideas via a case study of email usage-a realistic domain where audience configuration choices are particularly fine-grained and explicit in how email senders fill the To and Cc address fields. In a large collection of enterprise emails, we explore how people configure their audiences, finding salient patterns relating a sender's choice of configuration to the types of participants in the email exchange, the content of the message, and the nature of the subsequent interactions. Our formulation and findings show how analyzing audience configurations can enrich and extend existing accounts of communication behavior, and frame research directions on audience configuration decisions in communication and collaboration.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3392871"}, {"primary_key": "2466766", "vector": [], "sparse_vector": [], "title": "VibHand: On-Hand Vibrotactile Interface Enhancing Non-Visual Exploration of Digital Graphics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visual graphics are widely spread in digital media and are useful in many contexts of daily life. However, access to this type of graphical information remains a challenging task for people with visual impairments (VI). In this study, we designed and evaluated an on-hand vibrotactile interface that enables users with VI to explore digital graphics presented on tablets. We first conducted a set of exploratory tests with both people with VI and blindfolded (BF) people to investigate several design factors. We then conducted a comparative experiment to verify that on-hand vibrotactile cues (indicating direction and progression) can enhance the non-visual exploration of digital graphics. The results based on 12 participants with VI and 12 BF participants confirmed the usability of the technique and revealed that the visual status of the users does not impact graphics identification and comparison tasks.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3427335"}, {"primary_key": "2466767", "vector": [], "sparse_vector": [], "title": "Supporting Women in Online Dating with a Messaging Interface that Improves their Face-to-Face Meeting Decisions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a study of a messaging interface prototype for online dating intended to improve women's face-to-face meeting decisions, and therefore the capacity to manage the gendered risks involved with such meetings. The interface prompts users with discussion topics that are potentially more valuable for user evaluation than the impression management-motivated topics often chosen by men. These topics come in the form of first-date scenarios that messaging partners either agree or disagree on. Through a mixed-methods study utilizing speed dating events, daters used the interface to interact before meeting face-to-face. Results indicate that women's face-to-face meeting decisions improve when the interface prompts them to discuss scenarios involving agreement of opinion. Men's decisions are worsened by the same interface variant, potentially due to the displayed agreement being misinterpreted as a signal of compatibility. The study ultimately stresses that designs intended for women, and at-risk groups more broadly, must also be assessed with other user demographics-namely those that pose a risk-to identify unforeseen implications.", "published": "2020-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3415208"}]