[{"primary_key": "4359580", "vector": [], "sparse_vector": [], "title": "Building a Scientific Concept Hierarchy Database (SCHBase).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1059"}, {"primary_key": "4359581", "vector": [], "sparse_vector": [], "title": "If all you have is a bit of the Bible: Learning POS taggers for truly low-resource languages.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2044"}, {"primary_key": "4359582", "vector": [], "sparse_vector": [], "title": "A System Demonstration of a Framework for Computer Assisted Pronunciation Training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we demonstrate a system implementation of a framework for computer assisted pronunciation training for second language learner (L2).This framework supports an iterative improvement of the automatic pronunciation error recognition and classification by allowing integration of annotated error data.The annotated error data is acquired via an annotation tool for linguists.This paper will give a detailed description of the annotation tool and explains the error types.Furthermore, it will present the automatic error recognition method and the methods for automatic visual and audio feedback.This system demonstrates a novel approach to interactive and individualized learning for pronunciation training.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4001"}, {"primary_key": "4359583", "vector": [], "sparse_vector": [], "title": "Generating High Quality Proposition Banks for Multilingual Semantic Role Labeling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Semantic role labeling (SRL) is crucial to natural language understanding as it identifies the predicate-argument structure in text with semantic labels. Unfortunately, resources required to construct SRL models are expensive to obtain and simply do not exist for most languages. In this paper, we present a two-stage method to enable the construction of SRL models for resourcepoor languages by exploiting monolingual SRL and multilingual parallel data. Experimental results show that our method outperforms existing methods. We use our method to generate Proposition Banks with high to reasonable quality for 7 languages in three language families and release these resources to the research community.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1039"}, {"primary_key": "4359584", "vector": [], "sparse_vector": [], "title": "Unsupervised Decomposition of a Multi-Author Document Based on Naive-Bayesian Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2082"}, {"primary_key": "4359585", "vector": [], "sparse_vector": [], "title": "Aligning Opinions: Cross-Lingual Opinion Mining with Dependencies.", "authors": ["Mariana S. C. Almeida", "Cláudia <PERSON>", "<PERSON>", "<PERSON>", "André F. T<PERSON>"], "summary": "Mariana S. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1040"}, {"primary_key": "4359586", "vector": [], "sparse_vector": [], "title": "Zoom: a corpus of natural language descriptions of map locations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2012"}, {"primary_key": "4359587", "vector": [], "sparse_vector": [], "title": "Leveraging Linguistic Structure For Open Domain Information Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1034"}, {"primary_key": "4359588", "vector": [], "sparse_vector": [], "title": "Knowledge Portability with Semantic Expansion of Ontology Labels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1069"}, {"primary_key": "4359589", "vector": [], "sparse_vector": [], "title": "Learning Word Representations from Scarce and Noisy Data with Embedding Subspaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1104"}, {"primary_key": "4359590", "vector": [], "sparse_vector": [], "title": "Summarization of Multi-Document Topic Hierarchies using Submodular Mixtures.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of summarizing DAG-structured topic hierarchies over a given set of documents. Example applications include automatically generating Wikipedia disambiguation pages for a set of articles, and generating candidate multi-labels for preparing machine learning datasets (e.g., for text classification, functional genomics, and image classification). Unlike previous work, which focuses on clustering the set of documents using the topic hierarchy as features, we directly pose the problem as a submodular optimization problem on a topic hierarchy using the documents as features. Desirable properties of the chosen topics include document coverage, specificity, topic diversity, and topic homogeneity, each of which, we show, is naturally modeled by a submodular function. Other information, provided say by unsupervised approaches such as LDA and its variants, can also be utilized by defining a submodular function that expresses coherence between the chosen topics and this information. We use a large-margin framework to learn convex mixtures over the set of submodular components. We empirically evaluate our method on the problem of automatically generating Wikipedia disambiguation pages using human generated clusterings as ground truth. We find that our framework improves upon several baselines according to a variety of standard evaluation metrics including the Jaccard Index, F1 score and NMI, and moreover, can be scaled to extremely large scale problems.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1054"}, {"primary_key": "4359591", "vector": [], "sparse_vector": [], "title": "WikiKreator: Improving Wikipedia Stubs Automatically.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1084"}, {"primary_key": "4359592", "vector": [], "sparse_vector": [], "title": "Non-projective Dependency-based Pre-Reordering with Recurrent Neural Network for Machine Translation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1082"}, {"primary_key": "4359593", "vector": [], "sparse_vector": [], "title": "Thread-Level Information for Comment Classification in Community Question Answering.", "authors": ["<PERSON>", "<PERSON>", "Giovanni Da San Martino", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2113"}, {"primary_key": "4359594", "vector": [], "sparse_vector": [], "title": "A Methodology for Evaluating Timeline Generation Algorithms based on Deep Semantic Units.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2137"}, {"primary_key": "4359595", "vector": [], "sparse_vector": [], "title": "Automatic Identification of Rhetorical Questions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Joonsuk Park"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2122"}, {"primary_key": "4359596", "vector": [], "sparse_vector": [], "title": "Abstractive Multi-Document Summarization via Phrase Selection and Merging.", "authors": ["Li<PERSON> Bing", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1153"}, {"primary_key": "4359597", "vector": [], "sparse_vector": [], "title": "Labeled Grammar Induction with Minimal Supervision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2143"}, {"primary_key": "4359598", "vector": [], "sparse_vector": [], "title": "Probing the Linguistic Strengths and Limitations of Unsupervised Grammar Induction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1135"}, {"primary_key": "4359599", "vector": [], "sparse_vector": [], "title": "Non-Linear Text Regression with a Deep Convolutional Neural Network.", "authors": ["Zsolt Bitvai", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2030"}, {"primary_key": "4359600", "vector": [], "sparse_vector": [], "title": "Unsupervised Cross-Domain Word Representation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1071"}, {"primary_key": "4359601", "vector": [], "sparse_vector": [], "title": "IMI -- A Multilingual Semantic Annotation Environment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Semantic annotated parallel corpora, though rare, play an increasingly important role in natural language processing.These corpora provide valuable data for computational tasks like sense-based machine translation and word sense disambiguation, but also to contrastive linguistics and translation studies.In this paper we present the ongoing development of a web-based corpus semantic annotation environment that uses the Open Multilingual Wordnet (<PERSON> and Foster, 2013) as a sense inventory.The system includes interfaces to help coordinating the annotation project and a corpus browsing interface designed specifically to meet the needs of a semantically annotated corpus.The tool was designed to build the NTU-Multilingual Corpus (Tan and Bond, 2012).For the past six years, our tools have been tested and developed in parallel with the semantic annotation of a portion of this corpus in Chinese, English, Japanese and Indonesian.The annotation system is released under an open source license (MIT).", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4002"}, {"primary_key": "4359602", "vector": [], "sparse_vector": [], "title": "Model Adaptation for Personalized Opinion Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2126"}, {"primary_key": "4359603", "vector": [], "sparse_vector": [], "title": "Matrix and Tensor Factorization Methods for Natural Language Processing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5005"}, {"primary_key": "4359604", "vector": [], "sparse_vector": [], "title": "A Generalisation of Lexical Functions for Composition in Distributional Semantics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1028"}, {"primary_key": "4359605", "vector": [], "sparse_vector": [], "title": "Seed-Based Event Trigger Labeling: How far can event descriptions get us?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ng <PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2061"}, {"primary_key": "4359606", "vector": [], "sparse_vector": [], "title": "How Far are We from Fully Automatic High Quality Grammatical Error Correction?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1068"}, {"primary_key": "4359607", "vector": [], "sparse_vector": [], "title": "Generative Incremental Dependency Parsing with Neural Networks.", "authors": ["Jan Buys", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2142"}, {"primary_key": "4359608", "vector": [], "sparse_vector": [], "title": "A Unified Multilingual Semantic Representation of Concepts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1072"}, {"primary_key": "4359609", "vector": [], "sparse_vector": [], "title": "A Framework for the Construction of Monolingual and Cross-lingual Word Similarity Datasets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2001"}, {"primary_key": "4359610", "vector": [], "sparse_vector": [], "title": "Learning Summary Prior Representation for Extractive Summarization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2136"}, {"primary_key": "4359611", "vector": [], "sparse_vector": [], "title": "The Media Frames Corpus: Annotations of Frames Across Issues.", "authors": ["Dallas Card", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2072"}, {"primary_key": "4359612", "vector": [], "sparse_vector": [], "title": "Linguistic Template Extraction for Recognizing Reader-Emotion and Emotional Resonance Writing Assistance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2127"}, {"primary_key": "4359613", "vector": [], "sparse_vector": [], "title": "Text to 3D Scene Generation with Rich Lexical Grounding.", "authors": ["Angel <PERSON>", "<PERSON>", "Manolis Savva", "<PERSON>", "<PERSON>"], "summary": "The ability to map descriptions of scenes to 3D geometric representations has many applications in areas such as art, education, and robotics. However, prior work on the text to 3D scene generation task has used manually specified object categories and language that identifies them. We introduce a dataset of 3D scenes annotated with natural language descriptions and learn from this data how to ground textual descriptions to physical objects. Our method successfully grounds a variety of lexical terms to concrete referents, and we show quantitatively that our method improves 3D scene generation over previous work using purely rule-based methods. We evaluate the fidelity and plausibility of 3D scenes generated with our grounding approach through human judgments. To ease evaluation on this task, we also introduce an automated metric that strongly correlates with human judgments.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1006"}, {"primary_key": "4359614", "vector": [], "sparse_vector": [], "title": "Exploring the Planet of the APEs: a Comparative Study of State-of-the-art Methods for MT Automatic Post-Editing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2026"}, {"primary_key": "4359615", "vector": [], "sparse_vector": [], "title": "Unsupervised Learning and Modeling of Knowledge and Intent for Spoken Dialogue Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Spoken dialogue systems (SDS) are rapidly appearing in various smart devices (smartphone, smart-TV, in-car navigating system, etc).The key role in a successful SDS is a spoken language understanding (SLU) component, which parses user utterances into semantic concepts in order to understand users' intentions.However, such semantic concepts and their structure are manually created by experts, and the annotation process results in extremely high cost and poor scalability in system development.Therefore, the dissertation focuses on improving SDS generalization and scalability by automatically inferring domain knowledge and learning structures from unlabeled conversations through a matrix factorization (MF) technique.With the automatically acquired semantic concepts and structures, we further investigate whether such information can be utilized to effectively understand user utterances and then show the feasibility of reducing human effort during SDS development.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3001"}, {"primary_key": "4359616", "vector": [], "sparse_vector": [], "title": "Learning to Map Dependency Parses to Abstract Meaning Representations.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Meaning Representation (AMR) is a semantic representation language used to capture the meaning of English sentences.In this work, we propose an AMR parser based on dependency parse rewrite rules.This approach transfers dependency parses into AMRs by integrating the syntactic dependencies, semantic arguments, named entity and co-reference information.A dependency parse to AMR graph aligner is also introduced as a preliminary step for designing the parser.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3007"}, {"primary_key": "4359617", "vector": [], "sparse_vector": [], "title": "Representation Based Translation Evaluation Metrics.", "authors": ["Boxing Chen", "<PERSON><PERSON>"], "summary": "<PERSON> Chen, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2025"}, {"primary_key": "4359618", "vector": [], "sparse_vector": [], "title": "Revisiting Word Embedding for Contrasting Meaning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1011"}, {"primary_key": "4359619", "vector": [], "sparse_vector": [], "title": "Learning to Adapt Credible Knowledge in Cross-lingual Sentiment Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1041"}, {"primary_key": "4359620", "vector": [], "sparse_vector": [], "title": "Lifelong Learning for Sentiment Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2123"}, {"primary_key": "4359621", "vector": [], "sparse_vector": [], "title": "Chinese Zero Pronoun Resolution: A Joint Unsupervised Discourse-Aware Model Rivaling State-of-the-Art Resolvers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose an unsupervised probabilistic model for zero pronoun resolution. To our knowledge, this is the first such model that (1) is trained on zero pronouns in an unsupervised manner; (2) jointly identifies and resolves anaphoric zero pronouns; and (3) exploits discourse information provided by a salience model. Experiments demonstrate that our unsupervised model significantly outperforms its state-of-the-art unsupervised counterpart when resolving the Chinese zero pronouns in the OntoNotes corpus.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2053"}, {"primary_key": "4359622", "vector": [], "sparse_vector": [], "title": "Gated Recursive Neural Network for Chinese Word Segmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1168"}, {"primary_key": "4359623", "vector": [], "sparse_vector": [], "title": "Matrix Factorization with Knowledge Graph Propagation for Unsupervised Spoken Language Understanding.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1047"}, {"primary_key": "4359624", "vector": [], "sparse_vector": [], "title": "User Based Aggregation for Biterm Topic Model.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2080"}, {"primary_key": "4359625", "vector": [], "sparse_vector": [], "title": "Improving Distributed Representation of Word Sense via WordNet Gloss Composition and Context Clustering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2003"}, {"primary_key": "4359626", "vector": [], "sparse_vector": [], "title": "Event Extraction via Dynamic Multi-Pooling Convolutional Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1017"}, {"primary_key": "4359627", "vector": [], "sparse_vector": [], "title": "Synthetic Word Parsing Improves Chinese Word Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2043"}, {"primary_key": "4359628", "vector": [], "sparse_vector": [], "title": "Parsing Paraphrases with Joint Inference.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1118"}, {"primary_key": "4359629", "vector": [], "sparse_vector": [], "title": "Scalable Semantic Parsing with Partial Ontologies.", "authors": ["Euns<PERSON> Choi", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1127"}, {"primary_key": "4359630", "vector": [], "sparse_vector": [], "title": "It Depends: Dependency Parser Comparison Using A Web-based Evaluation Tool.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1038"}, {"primary_key": "4359631", "vector": [], "sparse_vector": [], "title": "Learning language through pictures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2019"}, {"primary_key": "4359632", "vector": [], "sparse_vector": [], "title": "Automatic Discrimination between Cognates and Borrowings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2071"}, {"primary_key": "4359633", "vector": [], "sparse_vector": [], "title": "Entity-Centric Coreference Resolution with Model Stacking.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1136"}, {"primary_key": "4359634", "vector": [], "sparse_vector": [], "title": "OMWEdit - The Integrated Open Multilingual Wordnet Editing System.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Wordnets play a central role in many natural language processing tasks. This paper introduces a multilingual editing system for the Open Multilingual Wordnet (OMW: Bond and Foster, 2013). Wordnet development, like most lexicographic tasks, is slow and expensive. Moving away from the original Princeton Wordnet (<PERSON>, 1998) development workflow, wordnet creation and expansion has increasingly been shifting towards an automated and/or interactive system facilitated task. In the particular case of human edition/expansion of wordnets, a few systems have been developed to aid the lexicographers’ work. Unfortunately, most of these tools have either restricted licenses, or have been designed with a particular language in mind. We present a webbased system that is capable of multilingual browsing and editing for any of the hundreds of languages made available by the OMW. All tools and guidelines are freely available under an open license.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4013"}, {"primary_key": "4359635", "vector": [], "sparse_vector": [], "title": "Sieve-Based Entity Linking for the Biomedical Domain.", "authors": ["Jennifer D&apos;Souza", "<PERSON>"], "summary": "We examine a key task in biomedical text processing, normalization of disorder mentions. We present a multi-pass sieve approach to this task, which has the advantage of simplicity and modularity. Our approach is evaluated on two datasets, one comprising clinical reports and the other comprising biomedical abstracts, achieving state-of-the-art results.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2049"}, {"primary_key": "4359636", "vector": [], "sparse_vector": [], "title": "Gaussian LDA for Topic Models with Word Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1077"}, {"primary_key": "4359637", "vector": [], "sparse_vector": [], "title": "Painless Labeling with Application to Text Mining.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2066"}, {"primary_key": "4359638", "vector": [], "sparse_vector": [], "title": "Language Models for Image Captioning: The Quirks and What Works.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2017"}, {"primary_key": "4359639", "vector": [], "sparse_vector": [], "title": "In-tool Learning for Selective Manual Annotation in Large Corpora.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel approach to the selective annotation of large corpora through the use of machine learning.Linguistic search engines used to locate potential instances of an infrequent phenomenon do not support ranking the search results.This favors the use of high-precision queries that return only a few results over broader queries that have a higher recall.Our approach introduces a classifier used to rank the search results and thus helping the annotator focus on those results with the highest potential of being an instance of the phenomenon in question, even in low-precision queries.The classifier is trained in an in-tool fashion, except for preprocessing relying only on the manual annotations done by the users in the querying tool itself.To implement this approach, we build upon CSniper 1 , a web-based multi-user search and annotation tool.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4003"}, {"primary_key": "4359640", "vector": [], "sparse_vector": [], "title": "Multi-Task Learning for Multiple Language Translation.", "authors": ["Daxiang Dong", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1166"}, {"primary_key": "4359641", "vector": [], "sparse_vector": [], "title": "Question Answering over Freebase with Multi-Column Convolutional Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1026"}, {"primary_key": "4359642", "vector": [], "sparse_vector": [], "title": "Unifying Bayesian Inference and Vector Space Models for Improved Decipherment.", "authors": ["Qing Dou", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1081"}, {"primary_key": "4359643", "vector": [], "sparse_vector": [], "title": "Efficient Methods for Inferring Large Sparse Topic Hierarchies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1075"}, {"primary_key": "4359644", "vector": [], "sparse_vector": [], "title": "A Data-Driven, Factorization Parser for CCG Dependency Structures.", "authors": ["Yan<PERSON><PERSON>", "Weiwei Sun", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1149"}, {"primary_key": "4359645", "vector": [], "sparse_vector": [], "title": "Low Resource Dependency Parsing: Cross-lingual Parameter Sharing in a Neural Network Parser.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2139"}, {"primary_key": "4359646", "vector": [], "sparse_vector": [], "title": "Neural CRF Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1030"}, {"primary_key": "4359647", "vector": [], "sparse_vector": [], "title": "Training a Natural Language Generator From Unaligned Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1044"}, {"primary_key": "4359648", "vector": [], "sparse_vector": [], "title": "Transition-Based Dependency Parsing with Stack Long Short-Term Memory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1033"}, {"primary_key": "4359649", "vector": [], "sparse_vector": [], "title": "Multiple Many-to-Many Sequence Alignment for Combining String-Valued Variables: A G2P Experiment.", "authors": ["Steffen Eger"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1088"}, {"primary_key": "4359650", "vector": [], "sparse_vector": [], "title": "Describing Images using Inferred Visual Dependency Representations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1005"}, {"primary_key": "4359651", "vector": [], "sparse_vector": [], "title": "Non-distributional Word Vector Representations.", "authors": ["Manaal Faruqui", "<PERSON>"], "summary": "Data-driven representation learning for words is a technique of central importance in NLP. While indisputably useful as a source of features in downstream tasks, such vectors tend to consist of uninterpretable components whose relationship to the categories of traditional lexical semantic theories is tenuous at best. We present a method for constructing interpretable word vectors from hand-crafted linguistic resources like WordNet, FrameNet etc. These vectors are binary (i.e, contain only 0 and 1) and are 99.9% sparse. We analyze their performance on state-of-the-art evaluation methods for distributional models of word vectors and find they are competitive to standard distributional approaches.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2076"}, {"primary_key": "4359652", "vector": [], "sparse_vector": [], "title": "Sparse Overcomplete Word Vector Representations.", "authors": ["Manaal Faruqui", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1144"}, {"primary_key": "4359653", "vector": [], "sparse_vector": [], "title": "Parsing as Reduction.", "authors": ["<PERSON>", "André F. T<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1147"}, {"primary_key": "4359654", "vector": [], "sparse_vector": [], "title": "Early and Late Combinations of Criteria for Reranking Distributional Thesauri.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2077"}, {"primary_key": "4359655", "vector": [], "sparse_vector": [], "title": "KeLP: a Kernel-based Learning Platform for Natural Language Processing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Kernel-based learning algorithms have been shown to achieve state-of-the-art results in many Natural Language Processing (NLP) tasks. We present KELP, a Java framework that supports the implementation of both kernel-based learning algorithms and kernel functions over generic data representation, e.g. vectorial data or discrete structures. The framework has been designed to decouple kernel functions and learning algorithms: once a new kernel function has been implemented it can be adopted in all the available kernelmachine algorithms. The platform includes different Online and Batch Learning algorithms for Classification, Regression and Clustering, as well as several Kernel functions, ranging from vector-based to structural kernels. This paper will show the main aspects of the framework by applying it to different NLP tasks.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4004"}, {"primary_key": "4359656", "vector": [], "sparse_vector": [], "title": "Structural Representations for Learning Relations between Pairs of Texts.", "authors": ["<PERSON>", "Giovanni Da San Martino", "<PERSON>"], "summary": "This paper studies the use of structural representations for learning relations between pairs of short texts (e.g., sentences or paragraphs) of the kind: the second text answers to, or conveys exactly the same information of, or is implied by, the first text. Engineering effective features that can capture syntactic and semantic relations between the constituents composing the target text pairs is rather complex. Thus, we define syntactic and semantic structures representing the text pairs and then apply graph and tree kernels to them for automatically engineering features in Support Vector Machines. We carry out an extensive comparative analysis of stateof-the-art models for this type of relational learning. Our findings allow for achieving the highest accuracy in two different and important related tasks, i.e., Paraphrasing Identification and Textual Entailment Recognition.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1097"}, {"primary_key": "4359657", "vector": [], "sparse_vector": [], "title": "Spectral Semi-Supervised Discourse Relation Classification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2015"}, {"primary_key": "4359658", "vector": [], "sparse_vector": [], "title": "Low-Rank Tensors for Verbs in Compositional Distributional Semantics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2120"}, {"primary_key": "4359659", "vector": [], "sparse_vector": [], "title": "Discourse-sensitive Automatic Identification of Generic Expressions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1123"}, {"primary_key": "4359660", "vector": [], "sparse_vector": [], "title": "Rhetoric Map of an Answer to Compound Queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2112"}, {"primary_key": "4359661", "vector": [], "sparse_vector": [], "title": "deltaBLEU: A Discriminative Metric for Generation Tasks with Intrinsically Diverse Targets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Yangfeng Ji", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2073"}, {"primary_key": "4359662", "vector": [], "sparse_vector": [], "title": "Multi-modal Visualization and Search for Text and Prosody Annotations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present ICARUS for intonation, an interactive tool to browse and search automatically derived descriptions of fundamental frequency contours.It offers access to tonal features in combination with other annotation layers like part-ofspeech, syntax or coreference and visualizes them in a highly customizable graphical interface with various playback functions.The built-in search allows multilevel queries, the construction of which can be done graphically or textually, and includes the ability to search F 0 contours based on various similarity measures.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4005"}, {"primary_key": "4359663", "vector": [], "sparse_vector": [], "title": "One Tense per Scene: Predicting <PERSON><PERSON> in Chinese Conversations.", "authors": ["Tao Ge", "<PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2110"}, {"primary_key": "4359664", "vector": [], "sparse_vector": [], "title": "Bring you to the past: Automatic Generation of Topically Relevant Event Chronicles.", "authors": ["Tao Ge", "<PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1056"}, {"primary_key": "4359665", "vector": [], "sparse_vector": [], "title": "Dialogue Management based on Sentence Clustering.", "authors": ["Wendong Ge", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2131"}, {"primary_key": "4359666", "vector": [], "sparse_vector": [], "title": "Simplifying Lexical Simplification: Do We Need Simplified Corpora?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2011"}, {"primary_key": "4359667", "vector": [], "sparse_vector": [], "title": "A Unified Kernel Approach for Learning Typed Sentence Rewritings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many high level natural language processing problems can be framed as determining if two given sentences are a rewriting of each other. In this paper, we propose a class of kernel functions, referred to as type-enriched string rewriting kernels, which, used in kernel-based machine learning algorithms, allow to learn sentence rewritings. Unlike previous work, this method can be fed external lexical semantic relations to capture a wider class of rewriting rules. It also does not assume preliminary syntactic parsing but is still able to provide a unified framework to capture syntactic structure and alignments between the two sentences. We experiment on three different natural sentence rewriting tasks and obtain state-of-the-art results for all of them.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1091"}, {"primary_key": "4359668", "vector": [], "sparse_vector": [], "title": "I do not disagree: leveraging monolingual alignment to detect disagreement in dialogue.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2016"}, {"primary_key": "4359669", "vector": [], "sparse_vector": [], "title": "An Efficient Dynamic Oracle for Unrestricted Non-Projective Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2042"}, {"primary_key": "4359670", "vector": [], "sparse_vector": [], "title": "Structured Belief Propagation for NLP.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5002"}, {"primary_key": "4359671", "vector": [], "sparse_vector": [], "title": "Improving Evaluation of Machine Translation Quality Estimation.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1174"}, {"primary_key": "4359672", "vector": [], "sparse_vector": [], "title": "A convex and feature-rich discriminative approach to dependency grammar induction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1133"}, {"primary_key": "4359673", "vector": [], "sparse_vector": [], "title": "Graph parsing with s-graph grammars.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1143"}, {"primary_key": "4359674", "vector": [], "sparse_vector": [], "title": "Efficient Learning for Undirected Topic Models.", "authors": ["Jiatao Gu", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2027"}, {"primary_key": "4359675", "vector": [], "sparse_vector": [], "title": "Dependency length minimisation effects in short spans: a large-scale analysis of adjective placement in complex noun phrases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2078"}, {"primary_key": "4359676", "vector": [], "sparse_vector": [], "title": "Cross-lingual Dependency Parsing Based on Distributed Representations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Che", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1119"}, {"primary_key": "4359677", "vector": [], "sparse_vector": [], "title": "Semantically Smooth Knowledge Graph Embedding.", "authors": ["<PERSON> Guo", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper considers the problem of embedding Knowledge Graphs (KGs) consisting of entities and relations into lowdimensional vector spaces. Most of the existing methods perform this task based solely on observed facts. The only requirement is that the learned embeddings should be compatible within each individual fact. In this paper, aiming at further discovering the intrinsic geometric structure of the embedding space, we propose Semantically Smooth Embedding (SSE). The key idea of SSE is to take full advantage of additional semantic information and enforce the embedding space to be semantically smooth, i.e., entities belonging to the same semantic category will lie close to each other in the embedding space. Two manifold learning algorithms Laplacian Eigenmaps and Locally Linear Embedding are used to model the smoothness assumption. Both are formulated as geometrically based regularization terms to constrain the embedding task. We empirically evaluate SSE in two benchmark tasks of link prediction and triple classification, and achieve significant and consistent improvements over state-of-the-art methods. Furthermore, SSE is a general framework. The smoothness assumption can be imposed to a wide variety of embedding models, and it can also be constructed using other information besides entities’ semantic categories.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1009"}, {"primary_key": "4359678", "vector": [], "sparse_vector": [], "title": "Pairwise Neural Machine Translation Evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1078"}, {"primary_key": "4359679", "vector": [], "sparse_vector": [], "title": "NEED4Tweet: A Twitterbot for Tweets Named Entity Extraction and Disambiguation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this demo paper, we present NEED4Tweet, a Twitterbot for named entity extraction (NEE) and disambiguation (NED) for Tweets.The straightforward application of state-of-the-art extraction and disambiguation approaches on informal text widely used in Tweets, typically results in significantly degraded performance due to the lack of formal structure; the lack of sufficient context required; and the seldom entities involved.In this paper, we introduce a novel framework that copes with the introduced challenges.We rely on contextual and semantic features more than syntactic features which are less informative.We believe that disambiguation can help to improve the extraction process.This mimics the way humans understand language.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4006"}, {"primary_key": "4359680", "vector": [], "sparse_vector": [], "title": "Successful Data Mining Methods for NLP.", "authors": ["Jiawei Han", "<PERSON>ng <PERSON>", "Yizhou Sun"], "summary": "<PERSON><PERSON><PERSON>, Heng Ji, Yizhou Sun. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5001"}, {"primary_key": "4359681", "vector": [], "sparse_vector": [], "title": "Language Identification and Modeling in Specialized Hardware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Baron<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2063"}, {"primary_key": "4359682", "vector": [], "sparse_vector": [], "title": "Can Natural Language Processing Become Natural Language Coaching?", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1120"}, {"primary_key": "4359683", "vector": [], "sparse_vector": [], "title": "Visual Error Analysis for Entity Linking.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present the Visual Entity Explorer (VEX), an interactive tool for visually exploring and analyzing the output of entity linking systems. VEX is designed to aid developers in improving their systems by visualizing system results, gold annotations, and various mention detection and entity linking error types in a clear, concise, and customizable manner.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4007"}, {"primary_key": "4359684", "vector": [], "sparse_vector": [], "title": "A Web-based Collaborative Evaluation Tool for Automatically Learned Relation Extraction Patterns.", "authors": ["<PERSON><PERSON>", "Hong Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Patterns extracted from dependency parses of sentences are a major source of knowledge for most state-of-the-art relation extraction systems, but can be of low quality in distantly supervised settings.We present a linguistic annotation tool that allows human experts to analyze and categorize automatically learned patterns, and to identify common error classes.The annotations can be used to create datasets that enable machine learning approaches to pattern quality estimation.We also present an experimental pattern error analysis for three semantic relations, where we find that between 24% and 61% of the learned dependency patterns are defective due to preprocessing or parsing errors, or due to violations of the distant supervision assumption.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4008"}, {"primary_key": "4359685", "vector": [], "sparse_vector": [], "title": "Discriminative Preordering Meets Kendall&apos;s Tau Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2023"}, {"primary_key": "4359686", "vector": [], "sparse_vector": [], "title": "Demographic Factors Improve Classification Performance.", "authors": ["<PERSON>"], "summary": "Extra-linguistic factors influence language use, and are accounted for by speakers and listeners. Most natural language processing (NLP) tasks to date, however, treat language as uniform. This assumption can harm performance. We investigate the effect of including demographic information on performance in a variety of text-classification tasks. We find that by including age or gender information, we consistently and significantly improve performance over demographic-agnostic models. These results hold across three text-classification tasks in five languages.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1073"}, {"primary_key": "4359687", "vector": [], "sparse_vector": [], "title": "Tagging Performance Correlates with Author Age.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2079"}, {"primary_key": "4359688", "vector": [], "sparse_vector": [], "title": "Entity Hierarchy Embedding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1125"}, {"primary_key": "4359689", "vector": [], "sparse_vector": [], "title": "Context-Dependent Translation Selection Using Convolutional Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhengdong Lu", "Hang Li", "Qing<PERSON><PERSON> Chen"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2088"}, {"primary_key": "4359690", "vector": [], "sparse_vector": [], "title": "Learning Topic Hierarchies for Wikipedia Categories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2057"}, {"primary_key": "4359691", "vector": [], "sparse_vector": [], "title": "What You Need to Know about Chinese for Chinese Language Processing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5008"}, {"primary_key": "4359692", "vector": [], "sparse_vector": [], "title": "Non-linear Learning for Statistical Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Chen", "Xinyu Dai", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1080"}, {"primary_key": "4359693", "vector": [], "sparse_vector": [], "title": "Scalable Large-Margin Structured Learning: Theory and Algorithms.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Much of NLP tries to map structured input (sentences) to some form of structured output (tag sequences, parse trees, semantic graphs, or translated/paraphrased/compressed sentences). Thus structured prediction and its learning algorithm are of central importance to us NLP researchers. However, when applying machine learning to structured domains, we often face scalability issues for two reasons:", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5006"}, {"primary_key": "4359694", "vector": [], "sparse_vector": [], "title": "SensEmbed: Learning Sense Embeddings for Word and Relational Similarity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1010"}, {"primary_key": "4359695", "vector": [], "sparse_vector": [], "title": "Deep Unordered Composition Rivals Syntactic Methods for Text Classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1162"}, {"primary_key": "4359696", "vector": [], "sparse_vector": [], "title": "Driving ROVER with Segment-based ASR Quality Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1106"}, {"primary_key": "4359697", "vector": [], "sparse_vector": [], "title": "On Using Very Large Target Vocabulary for Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Cho", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1001"}, {"primary_key": "4359698", "vector": [], "sparse_vector": [], "title": "Content Models for Survey Generation: A Factoid-Based Evaluation.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Drago<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1043"}, {"primary_key": "4359699", "vector": [], "sparse_vector": [], "title": "Knowledge Graph Embedding via Dynamic Mapping Matrix.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1067"}, {"primary_key": "4359700", "vector": [], "sparse_vector": [], "title": "A Computational Approach to Automatic Prediction of Drunk-Texting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Balamurali A. R.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2100"}, {"primary_key": "4359701", "vector": [], "sparse_vector": [], "title": "Harnessing Context Incongruity for Sarcasm Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2124"}, {"primary_key": "4359702", "vector": [], "sparse_vector": [], "title": "Evaluation Dataset and System for Japanese Lexical Simplification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We have constructed two research resources of Japanese lexical simplification. One is a simplification system that supports reading comprehension of a wide range of readers, including children and language learners. The other is a dataset for evaluation that enables open discussions with other systems. Both the system and the dataset are made available providing the first such resources for the Japanese language.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3006"}, {"primary_key": "4359703", "vector": [], "sparse_vector": [], "title": "Who caught a cold ? - Identifying the subject of a symptom.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1160"}, {"primary_key": "4359704", "vector": [], "sparse_vector": [], "title": "Towards a Contextual Pragmatic Model to Detect Irony in Tweets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes an approach to capture the pragmatic context needed to infer irony in tweets.We aim to test the validity of two main hypotheses: (1) the presence of negations, as an internal propriety of an utterance, can help to detect the disparity between the literal and the intended meaning of an utterance, (2) a tweet containing an asserted fact of the form N ot(P 1 ) is ironic if and only if one can assess the absurdity of P 1 .Our first results are encouraging and show that deriving a pragmatic contextual model is feasible.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2106"}, {"primary_key": "4359705", "vector": [], "sparse_vector": [], "title": "Predicting Salient Updates for Disaster Summarization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1155"}, {"primary_key": "4359706", "vector": [], "sparse_vector": [], "title": "Simple Learning and Compositional Application of Perceptually Grounded Word Meanings for Incremental Reference Resolution.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1029"}, {"primary_key": "4359707", "vector": [], "sparse_vector": [], "title": "Grounding Semantics in Olfactory Perception.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2038"}, {"primary_key": "4359708", "vector": [], "sparse_vector": [], "title": "Exploiting Image Generality for Lexical Entailment Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2020"}, {"primary_key": "4359709", "vector": [], "sparse_vector": [], "title": "Compact Lexicon Selection with Spectral Methods.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2132"}, {"primary_key": "4359710", "vector": [], "sparse_vector": [], "title": "Pre-training of Hidden-Unit CRFs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2032"}, {"primary_key": "4359711", "vector": [], "sparse_vector": [], "title": "New Transfer Learning Techniques for Disparate Label Sets.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1046"}, {"primary_key": "4359712", "vector": [], "sparse_vector": [], "title": "Disease Event Detection based on Deep Modality Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social media has attracted attention because of its potential for extraction of information of various types.For example, information collected from Twitter enables us to build useful applications such as predicting an epidemic of influenza.However, using text information from social media poses challenges for event detection because of the unreliable nature of user-generated texts, which often include counter-factual statements.Consequently, this study proposes the use of modality features to improve disease event detection from Twitter messages, or \"tweets\".Experimental results demonstrate that the combination of a modality dictionary and a modality analyzer improves the F1-score by 3.5 points.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3005"}, {"primary_key": "4359713", "vector": [], "sparse_vector": [], "title": "The Impact of Listener Gaze on Predicting Reference Resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2133"}, {"primary_key": "4359714", "vector": [], "sparse_vector": [], "title": "Semantic Role Labeling Improves Incremental Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1115"}, {"primary_key": "4359715", "vector": [], "sparse_vector": [], "title": "Frame-Semantic Role Labeling with Heterogeneous Annotations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2036"}, {"primary_key": "4359716", "vector": [], "sparse_vector": [], "title": "A Dual-Layer Semantic Role Labeling System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe a well-performed semantic role labeling system that further extracts concepts (smaller semantic expressions) from unstructured natural language sentences language independently.A dual-layer semantic role labeling (SRL) system is built using Chinese Treebank and Propbank data.Contextual information is incorporated while labeling the predicate arguments to achieve better performance.Experimental results show that the proposed approach is superior to CoNLL 2009 best systems and comparable to the state of the art with the advantage that it requires no feature engineering process.Concepts are further extracted according to templates formulated by the labeled semantic roles to serve as features in other NLP tasks to provide semantically related cues and potentially help in related research problems.We also show that it is easy to generate a different language version of this system by actually building an English system which performs satisfactory.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4009"}, {"primary_key": "4359717", "vector": [], "sparse_vector": [], "title": "Deep Questions without Deep Understanding.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1086"}, {"primary_key": "4359718", "vector": [], "sparse_vector": [], "title": "Aspect-Level Cross-lingual Sentiment Classification with Constrained SMT.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2128"}, {"primary_key": "4359719", "vector": [], "sparse_vector": [], "title": "Improving social relationships in face-to-face human-agent interactions: when the agent wants to know user&apos;s likes and dislikes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1103"}, {"primary_key": "4359720", "vector": [], "sparse_vector": [], "title": "Learning Relational Features with Backward Random Walks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1065"}, {"primary_key": "4359721", "vector": [], "sparse_vector": [], "title": "Document Level Time-anchoring for TimeLine Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "German Rigau"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, German Rigau. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2059"}, {"primary_key": "4359722", "vector": [], "sparse_vector": [], "title": "Unsupervised Prediction of Acceptability Judgements.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1156"}, {"primary_key": "4359723", "vector": [], "sparse_vector": [], "title": "Hubness and Pollution: Delving into Cross-Space Mapping for Zero-Shot Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1027"}, {"primary_key": "4359724", "vector": [], "sparse_vector": [], "title": "A Simultaneous Recognition Framework for the Spoken Language Understanding Module of Intelligent Personal Assistant Software on Smart Phones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Ko", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2134"}, {"primary_key": "4359725", "vector": [], "sparse_vector": [], "title": "TR9856: A Multi-word Term Relatedness Benchmark.", "authors": ["<PERSON><PERSON>", "Liat Ein-Dor", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2069"}, {"primary_key": "4359726", "vector": [], "sparse_vector": [], "title": "Coupled Sequence Labeling on Heterogeneous Annotations: POS Tagging as a Case Study.", "authors": ["Zheng<PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1172"}, {"primary_key": "4359727", "vector": [], "sparse_vector": [], "title": "Sentence-level Emotion Classification with Label and Context Dependence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1101"}, {"primary_key": "4359728", "vector": [], "sparse_vector": [], "title": "Semi-Stacking for Semi-supervised Sentiment Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "In this paper, we address semi-supervised sentiment learning via semi-stacking, which integrates two or more semi-supervised learning algorithms from an ensemble learning perspective. Specifically, we apply metalearning to predict the unlabeled data given the outputs from the member algorithms and propose N-fold cross validation to guarantee a suitable size of the data for training the meta-classifier. Evaluation on four domains shows that such a semi-stacking strategy performs consistently better than its member algorithms.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2005"}, {"primary_key": "4359729", "vector": [], "sparse_vector": [], "title": "Improving Named Entity Recognition in Tweets via Detecting Non-Standard Words.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1090"}, {"primary_key": "4359730", "vector": [], "sparse_vector": [], "title": "A Hierarchical Neural Autoencoder for Paragraphs and Documents.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1107"}, {"primary_key": "4359731", "vector": [], "sparse_vector": [], "title": "A Hierarchical Knowledge Representation for Expert Finding on Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2102"}, {"primary_key": "4359732", "vector": [], "sparse_vector": [], "title": "Implicit Role Linking on Chinese Discourse: Exploiting Explicit Roles and Frame-to-Frame Relations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Qinghua Chai"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1122"}, {"primary_key": "4359733", "vector": [], "sparse_vector": [], "title": "IWNLP: Inverse Wiktionary for Natural Language Processing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Nowadays, there are a lot of natural language processing pipelines that are based on training data created by a few experts. This paper examines how the proliferation of the internet and its collaborative application possibilities can be practically used for NLP. For that purpose, we examine how the German version of Wiktionary can be used for a lemmatization task. We introduce IWNLP, an opensource parser for Wiktionary, that reimplements several MediaWiki markup language templates for conjugated verbs and declined adjectives. The lemmatization task is evaluated on three German corpora on which we compare our results with existing software for lemmatization. With Wiktionary as a resource, we obtain a high accuracy for the lemmatization of nouns and can even improve on the results of existing software for the lemmatization of nouns.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2068"}, {"primary_key": "4359734", "vector": [], "sparse_vector": [], "title": "A system for fine-grained aspect-based sentiment analysis of Chinese.", "authors": ["<PERSON><PERSON>"], "summary": "This paper presents a pipeline for aspectbased sentiment analysis of Chinese texts in the automotive domain. The input to the pipeline is a string of Chinese characters; the output is a set of relationships between evaluations and their targets. The main goal is to demonstrate how knowledge about sentence structure can increase the precision, insight value and granularity of the output. We formulate the task of sentiment analysis in two steps, namely unit identification and relation extraction. In unit identification, we identify fairly well-delimited linguistic units which describe features, emotions and evaluations. In relation extraction, we discover the relations between evaluations and their “target” features.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4010"}, {"primary_key": "4359735", "vector": [], "sparse_vector": [], "title": "Learning Semantic Word Embeddings based on Ordinal Knowledge Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1145"}, {"primary_key": "4359736", "vector": [], "sparse_vector": [], "title": "Robust Multi-Relational Clustering via L1-Norm Symmetric Nonnegative Matrix Factorization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2065"}, {"primary_key": "4359737", "vector": [], "sparse_vector": [], "title": "A Dependency-Based Neural Network for Relation Classification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2047"}, {"primary_key": "4359738", "vector": [], "sparse_vector": [], "title": "Constrained Semantic Forests for Improved Discriminative Semantic Parsing.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2121"}, {"primary_key": "4359739", "vector": [], "sparse_vector": [], "title": "Entity Retrieval via Entity Factoid Hierarchy.", "authors": ["<PERSON>lian<PERSON> Lu", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1050"}, {"primary_key": "4359740", "vector": [], "sparse_vector": [], "title": "Point Process Modelling of Rumour Dynamics in Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2085"}, {"primary_key": "4359741", "vector": [], "sparse_vector": [], "title": "Addressing the Rare Word Problem in Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "Oriol Vinyals", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1002"}, {"primary_key": "4359742", "vector": [], "sparse_vector": [], "title": "Accurate Linear-Time Chinese Word Segmentation via Embedding Matching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1167"}, {"primary_key": "4359743", "vector": [], "sparse_vector": [], "title": "Dependency-based Convolutional Neural Networks for Sentence Embedding.", "authors": ["Ming<PERSON> Ma", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2029"}, {"primary_key": "4359744", "vector": [], "sparse_vector": [], "title": "Discontinuous Incremental Shift-reduce Parsing.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1116"}, {"primary_key": "4359745", "vector": [], "sparse_vector": [], "title": "Multi-Pass Decoding With Complex Feature Guidance for Statistical Machine Translation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2091"}, {"primary_key": "4359746", "vector": [], "sparse_vector": [], "title": "Transferring Coreference Resolvers with Posterior Regularization.", "authors": ["André F. T<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1138"}, {"primary_key": "4359747", "vector": [], "sparse_vector": [], "title": "Plug Latent Structures and Play Coreference Resolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present cort, a modular toolkit for devising, implementing, comparing and analyzing approaches to coreference resolution.The toolkit allows for a unified representation of popular coreference resolution approaches by making explicit the structures they operate on.Several of the implemented approaches achieve state-ofthe-art performance.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4011"}, {"primary_key": "4359748", "vector": [], "sparse_vector": [], "title": "Automatic Keyword Extraction on Twitter.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2105"}, {"primary_key": "4359749", "vector": [], "sparse_vector": [], "title": "Evaluating Machine Translation Systems with Second Language Proficiency Tests.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2024"}, {"primary_key": "4359750", "vector": [], "sparse_vector": [], "title": "Automatic Identification of Age-Appropriate Ratings of Song Lyrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2096"}, {"primary_key": "4359751", "vector": [], "sparse_vector": [], "title": "Encoding Source Language with Convolutional Neural Network for Machine Translation.", "authors": ["<PERSON><PERSON> Meng", "Zhengdong Lu", "<PERSON><PERSON><PERSON>", "Hang Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1003"}, {"primary_key": "4359752", "vector": [], "sparse_vector": [], "title": "SCHNAPPER: A Web Toolkit for Exploratory Relation Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present SCHN ¨ APPER, a web toolkit for Exploratory Relation Extraction (ERE). The tool allows users to identify relations of interest in a very large text corpus in an exploratory and highly interactive fashion. With this tool, we demonstrate the easeof-use and intuitive nature of ERE, as well as its applicability to large corpora. We show how users can formulate exploratory, natural language-like pattern queries that return relation instances. We also show how automatically computed suggestions are used to guide the exploration process. Finally, we demonstrate how users create extractors with SCHN ¨ APPER once a relation of interest is identified.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4012"}, {"primary_key": "4359753", "vector": [], "sparse_vector": [], "title": "Parse Imputation for Dependency Annotations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1134"}, {"primary_key": "4359754", "vector": [], "sparse_vector": [], "title": "Automatic disambiguation of English puns.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1070"}, {"primary_key": "4359755", "vector": [], "sparse_vector": [], "title": "Dependency Recurrent Neural Language Models for Sentence Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2084"}, {"primary_key": "4359756", "vector": [], "sparse_vector": [], "title": "Environment-Driven Lexicon Induction for High-Level Instructions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1096"}, {"primary_key": "4359757", "vector": [], "sparse_vector": [], "title": "Orthogonality of Syntax and Semantics within Distributional Spaces.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1126"}, {"primary_key": "4359758", "vector": [], "sparse_vector": [], "title": "Improving Pivot Translation by Remembering the Pivot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2094"}, {"primary_key": "4359759", "vector": [], "sparse_vector": [], "title": "SACRY: Syntax-based Automatic Crossword puzzle Resolution sYstem.", "authors": ["<PERSON>", "Massimo <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present our Crossword Puzzle Resolution System (SACRY), which exploits syntactic structures for clue reranking and answer extraction. SACRY uses a database (DB) containing previously solved CPs in order to generate the list of candidate answers. Additionally, it uses innovative features, such as the answer position in the rank and aggregated information such as the min, max and average clue reranking scores. Our system is based on WebCrow, one of the most advanced systems for automatic crossword puzzle resolution. Our extensive experiments over our two million clue dataset show that our approach highly improves the quality of the answer list, enabling the achievement of unprecedented results on the complete CP resolution tasks, i.e., accuracy of 99.17%.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4014"}, {"primary_key": "4359760", "vector": [], "sparse_vector": [], "title": "KB-LDA: Jointly Learning a Knowledge Base of Hierarchy, Relations, and Facts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1140"}, {"primary_key": "4359761", "vector": [], "sparse_vector": [], "title": "Multi-domain Dialog State Tracking using Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Milica <PERSON>ic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Tsung<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dialog state tracking is a key component of many modern dialog systems, most of which are designed with a single, welldefined domain in mind. This paper shows that dialog data drawn from different dialog domains can be used to train a general belief tracking model which can operate across all of these domains, exhibiting superior performance to each of the domainspecific models. We propose a training procedure which uses out-of-domain data to initialise belief tracking models for entirely new domains. This procedure leads to improvements in belief tracking performance regardless of the amount of in-domain data available for training the model.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2130"}, {"primary_key": "4359762", "vector": [], "sparse_vector": [], "title": "Detecting Deceptive Opinion Spam using Linguistics, Behavioral and Statistical Modeling.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5007"}, {"primary_key": "4359763", "vector": [], "sparse_vector": [], "title": "Efficient Top-Down BTG Parsing for Machine Translation Preordering.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1021"}, {"primary_key": "4359764", "vector": [], "sparse_vector": [], "title": "A Knowledge-Intensive Model for Prepositional Phrase Attachment.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1036"}, {"primary_key": "4359765", "vector": [], "sparse_vector": [], "title": "Ground Truth for Grammaticality Correction Metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2097"}, {"primary_key": "4359766", "vector": [], "sparse_vector": [], "title": "Machine Comprehension with Discourse Relations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1121"}, {"primary_key": "4359767", "vector": [], "sparse_vector": [], "title": "Joint Dependency Parsing and Multiword Expression Tokenization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1108"}, {"primary_key": "4359768", "vector": [], "sparse_vector": [], "title": "Compositional Vector Space Models for Knowledge Base Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1016"}, {"primary_key": "4359769", "vector": [], "sparse_vector": [], "title": "Identifying Cascading Errors using Constraints in Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1111"}, {"primary_key": "4359770", "vector": [], "sparse_vector": [], "title": "Tea Party in the House: A Hierarchical Ideal Point Topic Model and Its Application to Republican Legislators in the 112th Congress.", "authors": ["V<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1139"}, {"primary_key": "4359771", "vector": [], "sparse_vector": [], "title": "Event Detection and Domain Adaptation with Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2060"}, {"primary_key": "4359772", "vector": [], "sparse_vector": [], "title": "Semantic Representations for Domain Adaptation: A Case Study on the Tree Kernel-based Method for Relation Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1062"}, {"primary_key": "4359773", "vector": [], "sparse_vector": [], "title": "Topic Modeling based Sentiment Analysis on Social Media for Stock Market Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1131"}, {"primary_key": "4359774", "vector": [], "sparse_vector": [], "title": "Generative Event Schema Induction with Entity Disambiguation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1019"}, {"primary_key": "4359775", "vector": [], "sparse_vector": [], "title": "Linguistic Harbingers of Betrayal: A Case Study on an Online Strategy Game.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1159"}, {"primary_key": "4359776", "vector": [], "sparse_vector": [], "title": "On the Importance of Ezafe Construction in Persian Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2144"}, {"primary_key": "4359777", "vector": [], "sparse_vector": [], "title": "UNRAVEL - A Decipherment Toolkit.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2090"}, {"primary_key": "4359778", "vector": [], "sparse_vector": [], "title": "Tibetan Unknown Word Identification from News Corpora for Supporting Lexicon-based Tibetan Word Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2074"}, {"primary_key": "4359779", "vector": [], "sparse_vector": [], "title": "Syntax-based Simultaneous Translation through Prediction of Unseen Syntactic Constituents.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1020"}, {"primary_key": "4359780", "vector": [], "sparse_vector": [], "title": "Why discourse affects speakers&apos; choice of referring expressions.", "authors": ["Naho Orita", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1158"}, {"primary_key": "4359781", "vector": [], "sparse_vector": [], "title": "Word Order Typology through Multilingual Word Alignment.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2034"}, {"primary_key": "4359782", "vector": [], "sparse_vector": [], "title": "Joint Case Argument Identification for Japanese Predicate Argument Structure Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Existing methods for Japanese predicate argument structure (PAS) analysis identify case arguments of each predicate without considering interactions between the target PAS and others in a sentence. However, the argument structures of the predicates in a sentence are semantically related to each other. This paper proposes new methods for Japanese PAS analysis to jointly identify case arguments of all predicates in a sentence by (1) modeling multiple PAS interactions with a bipartite graph and (2) approximately searching optimal PAS combinations. Performing experiments on the NAIST Text Corpus, we demonstrate that our joint analysis methods substantially outperform a strong baseline and are comparable to previous work.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1093"}, {"primary_key": "4359783", "vector": [], "sparse_vector": [], "title": "LEXenstein: A Framework for Lexical Simplification.", "authors": ["<PERSON>", "Lucia <PERSON>"], "summary": "Lexical Simplification consists in replacing complex words in a text with simpler alternatives.We introduce LEXenstein, the first open source framework for Lexical Simplification.It covers all major stages of the process and allows for easy benchmarking of various approaches.We test the tool's performance and report comparisons on different datasets against the state of the art approaches.The results show that combining the novel Substitution Selection and Substitution Ranking approaches introduced in LEXenstein is the most effective approach to Lexical Simplification.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4015"}, {"primary_key": "4359784", "vector": [], "sparse_vector": [], "title": "Generating overspecified referring expressions: the role of discrimination.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an experiment to compare a standard, minimally distinguishing algorithm for the generation of relational referring expressions with two alternatives that produce overspecified descriptions. The experiment shows that discrimination which normally plays a major role in the disambiguation task is also a major influence in referential overspecification, even though disambiguation is in principle not relevant.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2013"}, {"primary_key": "4359785", "vector": [], "sparse_vector": [], "title": "Compositional Semantic Parsing on Semi-Structured Tables.", "authors": ["Panupong <PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1142"}, {"primary_key": "4359786", "vector": [], "sparse_vector": [], "title": "Adding Semantics to Data-Driven Paraphrasing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1146"}, {"primary_key": "4359787", "vector": [], "sparse_vector": [], "title": "Domain-Specific Paraphrase Extraction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2010"}, {"primary_key": "4359788", "vector": [], "sparse_vector": [], "title": "PPDB 2.0: Better paraphrase ranking, fine-grained entailment relations, word embeddings, and style classification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2070"}, {"primary_key": "4359789", "vector": [], "sparse_vector": [], "title": "FrameNet+: Fast Paraphrastic Tripling of FrameNet.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2067"}, {"primary_key": "4359790", "vector": [], "sparse_vector": [], "title": "An Effective Neural Network Model for Graph-based Dependency Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "Tao Ge", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1031"}, {"primary_key": "4359791", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Chinese Name Matching and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2062"}, {"primary_key": "4359792", "vector": [], "sparse_vector": [], "title": "Modeling Argument Strength in Student Essays.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1053"}, {"primary_key": "4359793", "vector": [], "sparse_vector": [], "title": "Jointly optimizing word representations for lexical and sentential tasks with the C-PHRASE model.", "authors": ["Nghia The Pham", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1094"}, {"primary_key": "4359794", "vector": [], "sparse_vector": [], "title": "A Multitask Objective to Inject Lexical Contrast into Distributional Semantics.", "authors": ["Nghia The Pham", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2004"}, {"primary_key": "4359795", "vector": [], "sparse_vector": [], "title": "A Data Sharing and Annotation Service Infrastructure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper reports on and demonstrates META-SHARE/QT21, a prototype implementation of a data sharing and annotation service platform, which was based on the META-SHARE infrastructure. META-SHARE, which has been designed for sharing datasets and tools, is enhanced with a processing layer for annotating textual content with appropriate NLP services that are documented with the appropriate metadata. In META-SHARE/QT21 pre-defined processing workflows are offered to the users; each workflow is a pipeline of atomic NLP services/tools (e.g. sentence splitting, part-of-speech tagging). Currently, workflows for annotating monolingual and bilingual resources of various formats are provided (e.g. XCES, TXT, TMX). From the legal framework point of view, a simple operational model is adopted by which only openly licensed datasets can be processed by openly licensed services.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4017"}, {"primary_key": "4359796", "vector": [], "sparse_vector": [], "title": "Corpus Patterns for Semantic Processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5004"}, {"primary_key": "4359797", "vector": [], "sparse_vector": [], "title": "An analysis of the user occupational class through Twitter content.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1169"}, {"primary_key": "4359798", "vector": [], "sparse_vector": [], "title": "Low-Rank Regularization for Sparse Conjunctive Feature Spaces: An Application to Named Entity Classification.", "authors": ["Audi Primadhanty", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>y, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1013"}, {"primary_key": "4359799", "vector": [], "sparse_vector": [], "title": "Leveraging Compounds to Improve Noun Phrase Translation from Chinese and German.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a method to improve the translation of polysemous nouns, leveraging on their previous occurrence as the head of a compound noun phrase.First, the occurrences are identified through pattern matching rules, which detect occurrences of an XY compound followed closely by a potentially coreferent occurrence of Y , such as \"Mooncakes . . .cakes . ..\".Second, two strategies are proposed to improve the translation of the second occurrence of Y : re-using the cached translation of Y from the XY compound, or post-editing the translation of Y using the head of the translation of XY .Experiments are performed on Chineseto-English and German-to-French statistical machine translation, with about 250 occurrences of XY /Y , from the WIT3 and Text+Berg corpora.The results and their analysis suggest that while the overall BLEU scores increase only slightly, the translations of the targeted polysemous nouns are significantly improved.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3002"}, {"primary_key": "4359800", "vector": [], "sparse_vector": [], "title": "Sharing annotations better: RESTful Open Annotation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Annotations are increasingly created and shared online and connected with web resources such as databases of real-world entities.Recent collaborative efforts to provide interoperability between online annotation tools and resources have introduced the Open Annotation (OA) model, a general framework for representing annotations based on web standards.Building on the OA model, we propose to share annotations over a minimal web interface that conforms to the Representational State Transfer architectural style and uses the JSON for Linking Data representation (JSON-LD).We introduce tools supporting this approach and apply it to several existing annotation clients and servers, demonstrating direct interoperability between tools and resources that were previously unable to exchange information.The specification and tools are available from http://restoa.github.io/.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4016"}, {"primary_key": "4359801", "vector": [], "sparse_vector": [], "title": "Feature Selection in Kernel Space: A Case Study on Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1114"}, {"primary_key": "4359802", "vector": [], "sparse_vector": [], "title": "Learning Tag Embeddings and Tag-specific Composition Functions in Recursive Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1132"}, {"primary_key": "4359803", "vector": [], "sparse_vector": [], "title": "Language to Code: Learning Semantic Parsers for If-This-Then-That Recipes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1085"}, {"primary_key": "4359804", "vector": [], "sparse_vector": [], "title": "Twitter User Geolocation Using a Unified Text and Network Prediction Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2104"}, {"primary_key": "4359805", "vector": [], "sparse_vector": [], "title": "Sentiment and Belief: How to Think about, Represent, and Annotate Private States.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing: Tutorial Abstracts. 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-5003"}, {"primary_key": "4359806", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Models of Aspect-Sentiment for Online Course Discussion Forums.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1008"}, {"primary_key": "4359807", "vector": [], "sparse_vector": [], "title": "Using prosodic annotations to improve coreference resolution of spoken text.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2014"}, {"primary_key": "4359808", "vector": [], "sparse_vector": [], "title": "Improving distant supervision using inference learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2045"}, {"primary_key": "4359809", "vector": [], "sparse_vector": [], "title": "KLcpos3 - a Language Similarity Measure for Delexicalized Parser Transfer.", "authors": ["<PERSON>", "Zdenek <PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2040"}, {"primary_key": "4359810", "vector": [], "sparse_vector": [], "title": "AutoExtend: Extending Word Embeddings to Embeddings for Synsets and Lexemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1173"}, {"primary_key": "4359811", "vector": [], "sparse_vector": [], "title": "Measuring idiosyncratic interests in children with autism.", "authors": ["<PERSON><PERSON><PERSON>", "Emily <PERSON>&apo<PERSON>;hommeaux", "<PERSON>", "<PERSON>"], "summary": "A defining symptom of autism spectrum disorder (ASD) is the presence of restricted and repetitive activities and interests, which can surface in language as a perseverative focus on idiosyncratic topics. In this paper, we use semantic similarity measures to identify such idiosyncratic topics in narratives produced by children with and without ASD. We find that neurotypical children tend to use the same words and semantic concepts when retelling the same narrative, while children with ASD, even when producing accurate retellings, use different words and concepts relative not only to neurotypical children but also to other children with ASD. Our results indicate that children with ASD not only stray from the target topic but do so in idiosyncratic ways according to their own restricted interests.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2035"}, {"primary_key": "4359812", "vector": [], "sparse_vector": [], "title": "Text Categorization as a Graph Classification Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1164"}, {"primary_key": "4359813", "vector": [], "sparse_vector": [], "title": "How Well Do Distributional Models Capture Different Types of Semantic Knowledge?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2119"}, {"primary_key": "4359814", "vector": [], "sparse_vector": [], "title": "JoBimViz: A Web-based Visualization for Graph-based Distributional Semantic Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces a web-based visualization framework for graph-based distributional semantic models.The visualization supports a wide range of data structures, including term similarities, similarities of contexts, support of multiword expressions, sense clusters for terms and sense labels.In contrast to other browsers of semantic resources, our visualization accepts input sentences, which are subsequently processed with languageindependent or language-dependent ways to compute term-context representations.Our web demonstrator currently contains models for multiple languages, based on different preprocessing such as dependency parsing and n-gram context representations.These models can be accessed from a database, the web interface and via a RESTful API.The latter facilitates the quick integration of such models in research prototypes.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4018"}, {"primary_key": "4359815", "vector": [], "sparse_vector": [], "title": "Learning Answer-Entailing Structures for Machine Comprehension.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1024"}, {"primary_key": "4359816", "vector": [], "sparse_vector": [], "title": "Learning Hybrid Representations to Retrieve Semantically Equivalent Questions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2114"}, {"primary_key": "4359817", "vector": [], "sparse_vector": [], "title": "Classifying Relations by Ranking with Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1061"}, {"primary_key": "4359818", "vector": [], "sparse_vector": [], "title": "End-to-end Argument Generation System in Debating.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qinghua Sun", "<PERSON><PERSON><PERSON>"], "summary": "We introduce an argument generation system in debating, one that is based on sentence retrieval.Users can specify a motion such as This house should ban gambling, and a stance on whether the system agrees or disagrees with the motion.Then the system outputs three argument paragraphs based on \"values\" automatically decided by the system.The \"value\" indicates a topic that is considered as a positive or negative for people or communities, such as health and education.Each paragraph is related to one value and composed of about seven sentences.An evaluation over 50 motions from a popular debate website showed that the generated arguments are understandable in 64 paragraphs out of 150.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4019"}, {"primary_key": "4359819", "vector": [], "sparse_vector": [], "title": "Semantic Structure Analysis of Noun Phrases using Abstract Meaning Representation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2140"}, {"primary_key": "4359820", "vector": [], "sparse_vector": [], "title": "Vector-space calculation of semantic surprisal for predicting word pronunciation duration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1074"}, {"primary_key": "4359821", "vector": [], "sparse_vector": [], "title": "Unsupervised extractive summarization via coverage maximization with syntactic and semantic concepts.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2138"}, {"primary_key": "4359822", "vector": [], "sparse_vector": [], "title": "Towards Debugging Sentiment Lexicons.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1099"}, {"primary_key": "4359823", "vector": [], "sparse_vector": [], "title": "String-to-Tree Multi Bottom-up Tree Transducers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1079"}, {"primary_key": "4359824", "vector": [], "sparse_vector": [], "title": "Statistical Machine Translation Features with Multitask Tensor Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a three-pronged approach to improving Statistical Machine Translation (SMT), building on recent success in the application of neural networks to SMT. First, we propose new features based on neural networks to model various nonlocal translation phenomena. Second, we augment the architecture of the neural network with tensor layers that capture important higher-order interaction among the network units. Third, we apply multitask learning to estimate the neural network parameters jointly. Each of our proposed methods results in significant improvements that are complementary. The overall improvement is +2.7 and +1.8 BLEU points for Arabic-English and ChineseEnglish translation over a state-of-the-art system that already includes neural network features.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1004"}, {"primary_key": "4359825", "vector": [], "sparse_vector": [], "title": "Distributional Neural Networks for Automatic Resolution of Crossword Puzzles.", "authors": ["<PERSON><PERSON><PERSON>", "Massimo <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2033"}, {"primary_key": "4359826", "vector": [], "sparse_vector": [], "title": "Neural Responding Machine for Short-Text Conversation.", "authors": ["<PERSON><PERSON>", "Zhengdong Lu", "Hang Li"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1152"}, {"primary_key": "4359827", "vector": [], "sparse_vector": [], "title": "Automatic Spontaneous Speech Grading: A Novel Feature Derivation Technique using the Crowd.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1105"}, {"primary_key": "4359828", "vector": [], "sparse_vector": [], "title": "Learning Cross-lingual Word Embeddings via Matrix Co-factorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Maosong Sun"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2093"}, {"primary_key": "4359829", "vector": [], "sparse_vector": [], "title": "Radical Embedding: Delving <PERSON> to Chinese Radicals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Languages using Chinese characters are mostly processed at word level. Inspired by recent success of deep learning, we delve deeper to character and radical levels for Chinese language processing. We propose a new deep learning technique, called “radical embedding”, with justifications based on Chinese linguistics, and validate its feasibility and utility through a set of three experiments: two in-house standard experiments on short-text categorization (STC) and Chinese word segmentation (CWS), and one in-field experiment on search ranking. We show that radical embedding achieves comparable, and sometimes even better, results than competing methods.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2098"}, {"primary_key": "4359830", "vector": [], "sparse_vector": [], "title": "Perceptually Grounded Selectional Preferences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1092"}, {"primary_key": "4359831", "vector": [], "sparse_vector": [], "title": "Inverted indexing for cross-lingual NLP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel, count-based approach to obtaining inter-lingual word representations based on inverted indexing of Wikipedia. We present experiments applying these representations to 17 datasets in document classification, POS tagging, dependency parsing, and word alignment. Our approach has the advantage that it is simple, computationally efficient and almost parameter-free, and, more importantly, it enables multi-source crosslingual learning. In 14/17 cases, we improve over using state-of-the-art bilingual embeddings.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1165"}, {"primary_key": "4359832", "vector": [], "sparse_vector": [], "title": "Online Multitask Learning for Machine Translation Quality Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1022"}, {"primary_key": "4359833", "vector": [], "sparse_vector": [], "title": "Multi-level Translation Quality Prediction with QuEst++.", "authors": ["Lucia <PERSON>", "<PERSON>", "Carolina Scarton"], "summary": "This paper presents QUEST++ , an open source tool for quality estimation which can predict quality for texts at word, sentence and document level.It also provides pipelined processing, whereby predictions made at a lower level (e.g. for words) can be used as input to build models for predictions at a higher level (e.g.sentences).QUEST++ allows the extraction of a variety of features, and provides machine learning algorithms to build and test quality estimation models.Results on recent datasets show that QUEST++ achieves state-of-the-art performance.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4020"}, {"primary_key": "4359834", "vector": [], "sparse_vector": [], "title": "Joint Models of Disagreement and Stance in Online Debate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Online debate forums present a valuable opportunity for the understanding and modeling of dialogue. To understand these debates, a key challenge is inferring the stances of the participants, all of which are interrelated and dependent. While collectively modeling users’ stances has been shown to be effective (<PERSON> et al., 2012c; <PERSON> and <PERSON>, 2013), there are many modeling decisions whose ramifications are not well understood. To investigate these choices and their effects, we introduce a scalable unified probabilistic modeling framework for stance classification models that 1) are collective, 2) reason about disagreement, and 3) can model stance at either the author level or at the post level. We comprehensively evaluate the possible modeling choices on eight topics across two online debate corpora, finding accuracy improvements of up to 11.5 percentage points over a local classifier. Our results highlight the importance of making the correct modeling choices for online dialogues, and having a unified probabilistic modeling framework that makes this possible.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1012"}, {"primary_key": "4359835", "vector": [], "sparse_vector": [], "title": "A Deeper Exploration of the Standard PB-SMT Approach to Text Simplification and its Evaluation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2135"}, {"primary_key": "4359836", "vector": [], "sparse_vector": [], "title": "Open IE as an Intermediate Structure for Semantic Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Mausam"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2050"}, {"primary_key": "4359837", "vector": [], "sparse_vector": [], "title": "Retrieval of Research-level Mathematical Information Needs: A Test Collection and Technical Terminology Experiment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2055"}, {"primary_key": "4359838", "vector": [], "sparse_vector": [], "title": "WA-Continuum: Visualising Word Alignments across Multiple Parallel Sentences Simultaneously.", "authors": ["<PERSON>", "Lucia <PERSON>"], "summary": "Word alignment (WA) between a pair of sentences in the same or different languages is a key component of many natural language processing tasks. It is commonly used for identifying the translation relationships between words and phrases in parallel sentences from two different languages. WA-Continuum is a tool designed for the visualisation of WAs. It was initially built to aid research studying WAs and ways to improve them. The tool relies on the automated mark-up of WAs, as typically produced by WA tools. Different from most previous work, it presents the alignment information graphically in a WA matrix that can be easily understood by users, as opposed to text connected by lines. The key features of the tool are the ability to visualise WA matrices for multiple parallel aligned sentences simultaneously in a single place, coupled with powerful search and selection components to find and inspect particular sentences as required.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4021"}, {"primary_key": "4359839", "vector": [], "sparse_vector": [], "title": "A Strategic Reasoning Model for Generating Alternative Answers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1052"}, {"primary_key": "4359840", "vector": [], "sparse_vector": [], "title": "Model-based Word Embeddings from Decompositions of Count Matrices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1124"}, {"primary_key": "4359841", "vector": [], "sparse_vector": [], "title": "Learning Dynamic Feature Selection for Fast Sequential Prediction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1015"}, {"primary_key": "4359842", "vector": [], "sparse_vector": [], "title": "A Context-Aware Topic Model for Statistical Machine Translation.", "authors": ["Jinsong Su", "<PERSON><PERSON>", "<PERSON>", "Xianpei Han", "<PERSON><PERSON>", "<PERSON><PERSON> Yao", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1023"}, {"primary_key": "4359843", "vector": [], "sparse_vector": [], "title": "On metric embedding for boosting semantic similarity computations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2002"}, {"primary_key": "4359844", "vector": [], "sparse_vector": [], "title": "Learning Word Representations by Jointly Modeling Syntagmatic and Paradigmatic Relations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1014"}, {"primary_key": "4359845", "vector": [], "sparse_vector": [], "title": "Event-Driven Headline Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dong-Hong Ji"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1045"}, {"primary_key": "4359846", "vector": [], "sparse_vector": [], "title": "A Unified Learning Framework of Skip-Grams and Global Vectors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2031"}, {"primary_key": "4359847", "vector": [], "sparse_vector": [], "title": "A Language-Independent Feature Schema for Inflectional Morphology.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2111"}, {"primary_key": "4359848", "vector": [], "sparse_vector": [], "title": "Document Classification by Inversion of Distributed Language Representations.", "authors": ["<PERSON>"], "summary": "The goal of this note is to point out that any distributed representation can be turned into a classifier through inversion via <PERSON><PERSON> rule. The approach is simple and modular, in that it will work with any language representation whose training can be formulated as optimizing a probability model. In our application to 2 million sentences from Yelp reviews, we also find that it performs as well as or better than complex purpose-built algorithms.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2008"}, {"primary_key": "4359849", "vector": [], "sparse_vector": [], "title": "Improved Semantic Representations From Tree-Structured Long Short-Term Memory Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1150"}, {"primary_key": "4359850", "vector": [], "sparse_vector": [], "title": "Lexical Comparison Between Wikipedia and Twitter Corpora by Using Word Embeddings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2108"}, {"primary_key": "4359851", "vector": [], "sparse_vector": [], "title": "Word-based Japanese typed dependency parsing with grammatical function analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2039"}, {"primary_key": "4359852", "vector": [], "sparse_vector": [], "title": "Learning Semantic Representations of Users and Products for Document Level Sentiment Classification.", "authors": ["Duyu Tang", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1098"}, {"primary_key": "4359853", "vector": [], "sparse_vector": [], "title": "Optimal Shift-Reduce Constituent Parsing with Structured Perceptron.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1148"}, {"primary_key": "4359854", "vector": [], "sparse_vector": [], "title": "A Convolution Kernel Approach to Identifying Comparisons in Text.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1037"}, {"primary_key": "4359855", "vector": [], "sparse_vector": [], "title": "Transition-based Dependency DAG Parsing Using Dynamic Oracles.", "authors": ["Alper Tokgöz", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In most of the dependency parsing studies, dependency relations within a sentence are often presented as a tree structure. Whilst the tree structure is sufficient to represent the surface relations, deep dependencies which may result to multi-headed relations require more general dependency structures, namely Directed Acyclic Graphs (DAGs). This study proposes a new dependency DAG parsing approach which uses a dynamic oracle within a shift-reduce transitionbased parsing framework. Although there is still room for improvement on performance with more feature engineering, we already obtain competitive performances compared to static oracles as a result of our initial experiments conducted", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3004"}, {"primary_key": "4359856", "vector": [], "sparse_vector": [], "title": "Joint Graphical Models for Date Selection in Timeline Summarization.", "authors": ["Giang Binh Tran", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1154"}, {"primary_key": "4359857", "vector": [], "sparse_vector": [], "title": "A Frame of Mind: Using Statistical Models for Detection of Framing and Agenda Setting Campaigns.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1157"}, {"primary_key": "4359858", "vector": [], "sparse_vector": [], "title": "Lexicon Stratification for Translating Out-of-Vocabulary Words.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2021"}, {"primary_key": "4359859", "vector": [], "sparse_vector": [], "title": "MT Quality Estimation for Computer-assisted Translation: Does it Really Help?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2087"}, {"primary_key": "4359860", "vector": [], "sparse_vector": [], "title": "Inducing Word and Part-of-Speech with <PERSON><PERSON><PERSON><PERSON><PERSON> Hidden Semi-Markov Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1171"}, {"primary_key": "4359861", "vector": [], "sparse_vector": [], "title": "A Domain-independent Rule-based Framework for Event Extraction.", "authors": ["<PERSON>-Escárcega", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe the design, development, and API of ODIN (Open Domain INformer), a domainindependent, rule-based event extraction (EE) framework.The proposed EE approach is: simple (most events are captured with simple lexico-syntactic patterns), powerful (the language can capture complex constructs, such as events taking other events as arguments, and regular expressions over syntactic graphs), robust (to recover from syntactic parsing errors, syntactic patterns can be freely mixed with surface, token-based patterns), and fast (the runtime environment processes 110 sentences/second in a real-world domain with a grammar of over 200 rules).We used this framework to develop a grammar for the biochemical domain, which approached human performance.Our EE framework is accompanied by a web-based user interface for the rapid development of event grammars and visualization of matches.The ODIN framework and the domain-specific grammars are available as open-source code.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4022"}, {"primary_key": "4359862", "vector": [], "sparse_vector": [], "title": "Stacked Ensembles of Information Extractors for Knowledge-Base Population.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1018"}, {"primary_key": "4359863", "vector": [], "sparse_vector": [], "title": "The NL2KR Platform for building Natural Language Translation Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>tta Baral"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1087"}, {"primary_key": "4359864", "vector": [], "sparse_vector": [], "title": "The Users Who Say &apos;Ni&apos;: Audience Identification in Chinese-language Restaurant Reviews.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2052"}, {"primary_key": "4359865", "vector": [], "sparse_vector": [], "title": "Learning to Explain Entity Relationships in Knowledge Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of explaining relationships between pairs of knowledge graph entities with human-readable descriptions. Our method extracts and enriches sentences that refer to an entity pair from a corpus and ranks the sentences according to how well they describe the relationship between the entities. We model this task as a learning to rank problem for sentences and employ a rich set of features. When evaluated on a large set of manually annotated sentences, we find that our method significantly improves over state-of-the-art baseline models.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1055"}, {"primary_key": "4359866", "vector": [], "sparse_vector": [], "title": "Bilingual Word Embeddings from Non-Parallel Document-Aligned Data Applied to Bilingual Lexicon Induction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2118"}, {"primary_key": "4359867", "vector": [], "sparse_vector": [], "title": "Sparse, Contextually Informed Models for Irony Detection: Exploiting User Communities, Entities and Sentiment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1100"}, {"primary_key": "4359868", "vector": [], "sparse_vector": [], "title": "BrailleSUM: A News Summarization System for the Blind and Visually Impaired People.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2095"}, {"primary_key": "4359869", "vector": [], "sparse_vector": [], "title": "Machine Comprehension with Syntax, Frames, and Semantics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2115"}, {"primary_key": "4359870", "vector": [], "sparse_vector": [], "title": "Building a Semantic Parser Overnight.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "How do we build a semantic parser in a new domain starting with zero training examples? We introduce a new methodology for this setting: First, we use a simple grammar to generate logical forms paired with canonical utterances. The logical forms are meant to cover the desired set of compositional operators, and the canonical utterances are meant to capture the meaning of the logical forms (although clumsily). We then use crowdsourcing to paraphrase these canonical utterances into natural utterances. The resulting data is used to train the semantic parser. We further study the role of compositionality in the resulting paraphrases. Finally, we test our methodology on seven domains and show that we can build an adequate semantic parser in just a few hours.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1129"}, {"primary_key": "4359871", "vector": [], "sparse_vector": [], "title": "Joint Information Extraction and Reasoning: A Scalable Statistical Relational Learning Approach.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1035"}, {"primary_key": "4359872", "vector": [], "sparse_vector": [], "title": "Sentiment-Aspect Extraction based on Restricted Boltzmann Machines.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1060"}, {"primary_key": "4359873", "vector": [], "sparse_vector": [], "title": "genCNN: A Convolutional Architecture for Word Sequence Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "Zhengdong Lu", "Hang Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1151"}, {"primary_key": "4359874", "vector": [], "sparse_vector": [], "title": "Emotion Detection in Code-switching Texts via Bilingual and Sentimental Information.", "authors": ["<PERSON>hong<PERSON> Wang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2125"}, {"primary_key": "4359875", "vector": [], "sparse_vector": [], "title": "Predicting Polarities of Tweets by Composing Word Embeddings with Long Short-Term Memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1130"}, {"primary_key": "4359876", "vector": [], "sparse_vector": [], "title": "Learning Lexical Embeddings with Syntactic and Lexicographic Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2075"}, {"primary_key": "4359877", "vector": [], "sparse_vector": [], "title": "Feature Optimization for Constituent Parsing via Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1110"}, {"primary_key": "4359878", "vector": [], "sparse_vector": [], "title": "A Long Short-Term Memory Model for Answer Sentence Selection in Question Answering.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2116"}, {"primary_key": "4359879", "vector": [], "sparse_vector": [], "title": "Trans-dimensional Random Fields for Language Modeling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1076"}, {"primary_key": "4359880", "vector": [], "sparse_vector": [], "title": "Extended Topic Model for Word Dependency.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2083"}, {"primary_key": "4359881", "vector": [], "sparse_vector": [], "title": "SOLAR: Scalable Online Learning Algorithms for Ranking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1163"}, {"primary_key": "4359882", "vector": [], "sparse_vector": [], "title": "Boosting Transition-based AMR Parsing with Refined Actions and Auxiliary Analyzers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2141"}, {"primary_key": "4359883", "vector": [], "sparse_vector": [], "title": "Semantic Clustering and Convolutional Neural Network for Short Text Categorization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hong<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2058"}, {"primary_key": "4359884", "vector": [], "sparse_vector": [], "title": "Transition-based Neural Constituent Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1113"}, {"primary_key": "4359885", "vector": [], "sparse_vector": [], "title": "What&apos;s in a Domain? Analyzing Genre and Topic Differences in Statistical Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2092"}, {"primary_key": "4359886", "vector": [], "sparse_vector": [], "title": "Using Tweets to Help Sentence Compression for News Highlights Generation.", "authors": ["Zhongyu Wei", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2009"}, {"primary_key": "4359887", "vector": [], "sparse_vector": [], "title": "Learning Representations for Text-level Discourse Parsing.", "authors": ["<PERSON>"], "summary": "In the proposed doctoral work we will design an end-to-end approach for the challenging NLP task of text-level discourse parsing.Instead of depending on mostly hand-engineered sparse features and independent components for each subtask, we propose a unified approach completely based on deep learning architectures.To train more expressive representations that capture communicative functions and semantic roles of discourse units and relations between them, we will jointly learn all discourse parsing subtasks at different layers of our architecture and share their intermediate representations.By combining unsupervised training of word embeddings with our layer-wise multi-task learning of higher representations we hope to reach or even surpass performance of current state-of-the-art methods on annotated English corpora.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-3003"}, {"primary_key": "4359888", "vector": [], "sparse_vector": [], "title": "Structured Training for Neural Network Transition-Based Parsing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1032"}, {"primary_key": "4359889", "vector": [], "sparse_vector": [], "title": "Multi-Objective Optimization for the Joint Disambiguation of Nouns and Named Entities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present a novel approach to joint word sense disambiguation (WSD) and entity linking (EL) that combines a set of complementary objectives in an extensible multi-objective formalism. During disambiguation the system performs continuous optimization to find optimal probability distributions over candidate senses. The performance of our system on nominal WSD as well as EL improves state-ofthe-art results on several corpora. These improvements demonstrate the importance of combining complementary objectives in a joint model for robust disambiguation.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1058"}, {"primary_key": "4359890", "vector": [], "sparse_vector": [], "title": "Robust Subgraph Generation Improves Abstract Meaning Representation Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1095"}, {"primary_key": "4359891", "vector": [], "sparse_vector": [], "title": "Learning Anaphoricity and Antecedent Ranking Features for Coreference Resolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1137"}, {"primary_key": "4359892", "vector": [], "sparse_vector": [], "title": "Storybase: Towards Building a Knowledge Base for News Events.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To better organize and understand online news information, we propose Storybase 1 , a knowledge base for news events that builds upon Wikipedia current events and daily Web news.It first constructs stories and their timelines based on Wikipedia current events and then detects and links daily news to enrich those Wikipedia stories with more comprehensive events.We encode events and develop efficient event clustering and chaining techniques in an event space.We demonstrate Storybase with a news events search engine that helps find historical and ongoing news stories and inspect their dynamic timelines.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4023"}, {"primary_key": "4359893", "vector": [], "sparse_vector": [], "title": "Efficient Disfluency Detection with Transition-based Parsing.", "authors": ["Shuangzhi Wu", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1048"}, {"primary_key": "4359894", "vector": [], "sparse_vector": [], "title": "Tracking unbounded Topic Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1170"}, {"primary_key": "4359895", "vector": [], "sparse_vector": [], "title": "Co-training for Semi-supervised Sentiment Classification Based on Dual-view Bags-of-words Representation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Xin-<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1102"}, {"primary_key": "4359896", "vector": [], "sparse_vector": [], "title": "Learning Hidden Markov Models with Distributed State Representations for Domain Adaptation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2086"}, {"primary_key": "4359897", "vector": [], "sparse_vector": [], "title": "Reducing infrequent-token perplexity via variational corpora.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alok N. Cho<PERSON>hary"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2101"}, {"primary_key": "4359898", "vector": [], "sparse_vector": [], "title": "CCG Supertagging with a Recurrent Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2041"}, {"primary_key": "4359899", "vector": [], "sparse_vector": [], "title": "A Lexicalized Tree Kernel for Open Information Extraction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2046"}, {"primary_key": "4359900", "vector": [], "sparse_vector": [], "title": "Tweet Normalization with Syllables.", "authors": ["<PERSON>", "Yunqing Xia", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1089"}, {"primary_key": "4359901", "vector": [], "sparse_vector": [], "title": "A Distributed Representation Based Query Expansion Approach for Image Captioning.", "authors": ["<PERSON><PERSON>", "Erkut Erdem", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2018"}, {"primary_key": "4359902", "vector": [], "sparse_vector": [], "title": "Tackling Sparsity, the Achilles Heel of Social Networks: Language Model Smoothing via Social Regularization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaohua Hu"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2103"}, {"primary_key": "4359903", "vector": [], "sparse_vector": [], "title": "Deep Markov Neural Network for Sequential Data Classification.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2006"}, {"primary_key": "4359904", "vector": [], "sparse_vector": [], "title": "Learning the Semantics of Manipulation Action.", "authors": ["Yezhou Yang", "<PERSON><PERSON><PERSON>", "Cornelia <PERSON>ü<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we present a formal computational framework for modeling manipulation actions. The introduced formalism leads to semantics of manipulation action and has applications to both observing and understanding human manipulation actions as well as executing them with a robotic mechanism (e.g. a humanoid robot). It is based on a Combinatory Categorial Grammar. The goal of the introduced framework is to: (1) represent manipulation actions with both syntax and semantic parts, where the semantic part employs $\\lambda$-calculus; (2) enable a probabilistic semantic parsing schema to learn the $\\lambda$-calculus representation of manipulation action from an annotated action corpus of videos; (3) use (1) and (2) to develop a system that visually observes manipulation actions and understands their meaning while it can reason beyond observations using propositional logic and axiom schemata. The experiments conducted on a public available large manipulation action dataset validate the theoretical framework and our implementation.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1066"}, {"primary_key": "4359905", "vector": [], "sparse_vector": [], "title": "S-MART: Novel Tree-based Structured Learning Algorithms Applied to Tweet Entity Linking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1049"}, {"primary_key": "4359906", "vector": [], "sparse_vector": [], "title": "Recovering dropped pronouns from Chinese text messages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2051"}, {"primary_key": "4359907", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Role Identification in Teamwork Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1161"}, {"primary_key": "4359908", "vector": [], "sparse_vector": [], "title": "Semantic Analysis and Helpfulness Prediction of Text for Online Product Reviews.", "authors": ["<PERSON><PERSON><PERSON>", "Yaowei Yan", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2007"}, {"primary_key": "4359909", "vector": [], "sparse_vector": [], "title": "WriteAhead: Mining Grammar Patterns in Corpora for Assisted Writing.", "authors": ["Tzu-<PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper describes WriteAhead, a resource-rich, Interactive Writing Environment that provides L2 learners with writing prompts, as well as ”get it right” advice, to helps them write fluently and accurately. The method involves automatically analyzing reference and learner corpora, extracting grammar patterns with example phrases, and computing dubious, overused patterns. At run-time, as the user types (or mouses over) a word, the system automatically retrieves and displays grammar patterns and examples, most relevant to the word. The user can opt for patterns from a general corpus, academic corpus, learner corpus, or commonly overused dubious patterns found in a learner corpus. WriteAhead proactively engages the user with steady, timely, and spot-on information for effective assisted writing. Preliminary experiments show that <PERSON>rite<PERSON><PERSON> fulfills the design goal of fostering learner independence and encouraging self-editing, and is likely to induce better writing, and improve writing skills in the long run.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4024"}, {"primary_key": "4359910", "vector": [], "sparse_vector": [], "title": "Automatic Detection of Sentence Fragments.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present and evaluate a method for automatically detecting sentence fragments in English texts written by non-native speakers. Our method combines syntactic parse tree patterns and parts-of-speech information produced by a tagger to detect this phenomenon. When evaluated on a corpus of authentic learner texts, our best model achieved a precision of 0.84 and a recall of 0.62, a statistically significant improvement over baselines using non-parse features, as well as a popular grammar checker.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2099"}, {"primary_key": "4359911", "vector": [], "sparse_vector": [], "title": "Semantic Parsing via Staged Query Graph Generation: Question Answering with Knowledge Base.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1128"}, {"primary_key": "4359912", "vector": [], "sparse_vector": [], "title": "MultiGranCNN: An Architecture for General Matching of Text Chunks on Multiple Levels of Granularity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1007"}, {"primary_key": "4359913", "vector": [], "sparse_vector": [], "title": "Embedding Methods for Fine Grained Entity Type Classification.", "authors": ["<PERSON>", "<PERSON>", "Nevena Lazic"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2048"}, {"primary_key": "4359914", "vector": [], "sparse_vector": [], "title": "A Hassle-Free Unsupervised Domain Adaptation Method Using Instance Similarity Features.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jing <PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2028"}, {"primary_key": "4359915", "vector": [], "sparse_vector": [], "title": "Co-Simmate: Quick Retrieving All Pairwise Co-Simrank Scores.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2054"}, {"primary_key": "4359916", "vector": [], "sparse_vector": [], "title": "Detecting Deceptive Groups Using Conversations and Network Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1083"}, {"primary_key": "4359917", "vector": [], "sparse_vector": [], "title": "Predicting Valence-Arousal Ratings of Words Using a Weighted Graph Method.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2129"}, {"primary_key": "4359918", "vector": [], "sparse_vector": [], "title": "Recurrent Neural Network based Rule Sequence Model for Statistical Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2022"}, {"primary_key": "4359919", "vector": [], "sparse_vector": [], "title": "Semantic Interpretation of Superlative Expressions via Structured Knowledge Bases.", "authors": ["<PERSON><PERSON>", "Yansong Feng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2037"}, {"primary_key": "4359920", "vector": [], "sparse_vector": [], "title": "Context-aware <PERSON><PERSON><PERSON> Morph Decoding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yizhou Sun", "Jiawei Han", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1057"}, {"primary_key": "4359921", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Mutantur, <PERSON><PERSON>: Connecting Past with Present by Finding Corresponding Terms across Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "So<PERSON>v S<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1063"}, {"primary_key": "4359922", "vector": [], "sparse_vector": [], "title": "The Fixed-Size Ordinally-Forgetting Encoding Method for Neural Network Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2081"}, {"primary_key": "4359923", "vector": [], "sparse_vector": [], "title": "The Discovery of Natural Typing Annotations: User-produced Potential Chinese Word Delimiters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Chuyuan Wei", "Shiping Tang"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2109"}, {"primary_key": "4359924", "vector": [], "sparse_vector": [], "title": "Encoding Distributional Semantics into Triple-Based Knowledge Ranking for Document Enrichment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1051"}, {"primary_key": "4359925", "vector": [], "sparse_vector": [], "title": "Learning to Mine Query Subtopics from Query Log.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Le Sun", "Xianpei Han"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2056"}, {"primary_key": "4359926", "vector": [], "sparse_vector": [], "title": "Learning Word Reorderings for Hierarchical Phrase-based Statistical Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2089"}, {"primary_key": "4359927", "vector": [], "sparse_vector": [], "title": "Annotation and Classification of an Email Importance Corpus.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2107"}, {"primary_key": "4359928", "vector": [], "sparse_vector": [], "title": "A Computationally Efficient Algorithm for Learning Topical Collocation Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1141"}, {"primary_key": "4359929", "vector": [], "sparse_vector": [], "title": "Learning Bilingual Sentiment Word Embeddings for Cross-language Sentiment Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Shi", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1042"}, {"primary_key": "4359930", "vector": [], "sparse_vector": [], "title": "Answer Sequence Learning with Neural Networks for Answer Selection in Community Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qing<PERSON><PERSON> Chen", "Buzhou Tang", "<PERSON><PERSON>"], "summary": "In this paper, the answer selection problem in community question answering (CQA) is regarded as an answer sequence labeling task, and a novel approach is proposed based on the recurrent architecture for this problem. Our approach applies convolution neural networks (CNNs) to learning the joint representation of questionanswer pair firstly, and then uses the joint representation as input of the long shortterm memory (LSTM) to learn the answer sequence of a question for labeling the matching quality of each answer. Experiments conducted on the SemEval 2015 CQA dataset shows the effectiveness of our approach.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2117"}, {"primary_key": "4359931", "vector": [], "sparse_vector": [], "title": "Learning Continuous Word Embedding with Metadata for Question Retrieval in Community Question Answering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Tingting He", "<PERSON>", "Po Hu"], "summary": "Community question answering (cQA) has become an important issue due to the popularity of cQA archives on the web. This paper is concerned with the problem of question retrieval. Question retrieval in cQA archives aims to find the existing questions that are semantically equivalent or relevant to the queried questions. However, the lexical gap problem brings about new challenge for question retrieval in cQA. In this paper, we propose to learn continuous word embeddings with metadata of category information within cQA pages for question retrieval. To deal with the variable size of word embedding vectors, we employ the framework of fisher kernel to aggregated them into the fixedlength vectors. Experimental results on large-scale real world cQA data set show that our approach can significantly outperform state-of-the-art translation models and topic-based models for question re-", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1025"}, {"primary_key": "4359932", "vector": [], "sparse_vector": [], "title": "End-to-end learning of semantic role labeling using recurrent neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1109"}, {"primary_key": "4359933", "vector": [], "sparse_vector": [], "title": "A Neural Probabilistic Structured-Prediction Model for Transition-Based Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1117"}, {"primary_key": "4359934", "vector": [], "sparse_vector": [], "title": "A Re-ranking Model for Dependency Parser with Recursive Convolutional Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1112"}, {"primary_key": "4359935", "vector": [], "sparse_vector": [], "title": "NiuParser: A Chinese Syntactic and Semantic Parsing Toolkit.", "authors": ["Jing<PERSON> Zhu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new toolkit -NiuParserfor Chinese syntactic and semantic analysis.It can handle a wide range of Natural Language Processing (NLP) tasks in Chinese, including word segmentation, partof-speech tagging, named entity recognition, chunking, constituent parsing, dependency parsing, and semantic role labeling.The NiuParser system runs fast and shows state-of-the-art performance on several benchmarks.Moreover, it is very easy to use for both research and industrial purposes.Advanced features include the Software Development Kit (SDK) interfaces and a multi-thread implementation for system speed-up.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-4025"}, {"primary_key": "4359936", "vector": [], "sparse_vector": [], "title": "Cross-lingual Transfer of Named Entity Recognizers without Parallel Corpora.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 2: Short Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-2064"}, {"primary_key": "4359937", "vector": [], "sparse_vector": [], "title": "Negation and Speculation Identification in Chinese Language.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 53rd Annual Meeting of the Association for Computational Linguistics and the 7th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2015.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.3115/V1/P15-1064"}, {"primary_key": "4516183", "vector": [], "sparse_vector": [], "title": "Fourier Series Formalization in ACL2(r).", "authors": ["<PERSON>uong K. Chau", "<PERSON>", "<PERSON>."], "summary": "We formalize some basic properties of Fourier series in the logic of ACL2(r), which is a variant of ACL2 that supports reasoning about the real and complex numbers by way of non-standard analysis. More specifically, we extend a framework for formally evaluating definite integrals of real-valued, continuous functions using the Second Fundamental Theorem of Calculus. Our extended framework is also applied to functions containing free arguments. Using this framework, we are able to prove the orthogonality relationships between trigonometric functions, which are the essential properties in Fourier series analysis. The sum rule for definite integrals of indexed sums is also formalized by applying the extended framework along with the First Fundamental Theorem of Calculus and the sum rule for differentiation. The Fourier coefficient formulas of periodic functions are then formalized from the orthogonality relations and the sum rule for integration. Consequently, the uniqueness of Fourier sums is a straightforward corollary. We also present our formalization of the sum rule for definite integrals of infinite series in ACL2(r). Part of this task is to prove the Dini Uniform Convergence Theorem and the continuity of a limit function under certain conditions. A key technique in our proofs of these theorems is to apply the overspill principle from non-standard analysis.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.4"}, {"primary_key": "4516194", "vector": [], "sparse_vector": [], "title": "Second-Order Functions and Theorems in ACL2.", "authors": ["<PERSON>"], "summary": "SOFT ('Second-Order Functions and Theorems') is a tool to mimic second-order functions and theorems in the first-order logic of ACL2. Second-order functions are mimicked by first-order functions that reference explicitly designated uninterpreted functions that mimic function variables. First-order theorems over these second-order functions mimic second-order theorems universally quantified over function variables. Instances of second-order functions and theorems are systematically generated by replacing function variables with functions. SOFT can be used to carry out program refinement inside ACL2, by constructing a sequence of increasingly stronger second-order predicates over one or more target functions: the sequence starts with a predicate that specifies requirements for the target functions, and ends with a predicate that provides executable definitions for the target functions.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.3"}, {"primary_key": "4516199", "vector": [], "sparse_vector": [], "title": "Perfect Numbers in ACL2.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A perfect number is a positive integer n such that n equals the sum of all positive integer divisors of n that are less than n. That is, although n is a divisor of n, n is excluded from this sum. Thus 6 = 1 + 2 + 3 is perfect, but 12 < 1 + 2 + 3 + 4 + 6 is not perfect. An ACL2 theory of perfect numbers is developed and used to prove, in ACL2(r), this bit of mathematical folklore: Even if there are infinitely many perfect numbers the series of the reciprocals of all perfect numbers converges.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.5"}, {"primary_key": "4516261", "vector": [], "sparse_vector": [], "title": "Reasoning About LLVM Code Using Codewalker.", "authors": ["<PERSON>"], "summary": "This paper reports on initial experiments using <PERSON>'s <PERSON>walker to reason about programs compiled to the Low-Level Virtual Machine (LLVM) intermediate form. Previously, we reported on a translator from LLVM to the applicative subset of Common Lisp accepted by the ACL2 theorem prover, producing executable ACL2 formal models, and allowing us to both prove theorems about the translated models as well as validate those models by testing. That translator provided many of the benefits of a pure decompilation into logic approach, but had the disadvantage of not being verified. The availability of Codewalker as of ACL2 7.0 has provided an opportunity to revisit this idea, and employ a more trustworthy decompilation into logic tool. Thus, we have employed the Codewalker method to create an interpreter for a subset of the LLVM instruction set, and have used <PERSON>walker to analyze some simple array-based C programs compiled to LLVM form. We discuss advantages and limitations of the Codewalker-based method compared to the previous method, and provide some challenge problems for future Codewalker development.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.7"}, {"primary_key": "4516286", "vector": [], "sparse_vector": [], "title": "Proving Skipping Refinement with ACL2s.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe three case studies illustrating the use of ACL2s to prove the correctness of optimized reactive systems using skipping refinement. Reasoning about reactive systems using refinement involves defining an abstract, high-level specification system and a concrete, low-level system. Next, one shows that the behaviors of the implementation system are allowed by the specification system. Skipping refinement allows us to reason about implementation systems that can \"skip\" specification states due to optimizations that allow the implementation system to take several specification steps at once. Skipping refinement also allows implementation systems to, i.e., to take several steps before completing a specification step. We show how ACL2s can be used to prove skipping refinement theorems by modeling and proving the correctness of three systems: a JVM-inspired stack machine, a simple memory controller, and a scalar to vector compiler transformation.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.9"}, {"primary_key": "4516351", "vector": [], "sparse_vector": [], "title": "Stateman: Using Metafunctions to Manage Large Terms Representing Machine States.", "authors": ["<PERSON>"], "summary": "When ACL2 is used to model the operational semantics of computing machines, machine states are typically represented by terms recording the contents of the state components. When models are realistic and are stepped through thousands of machine cycles, these terms can grow quite large and the cost of simplifying them on each step grows. In this paper we describe an ACL2 book that uses HIDE and metafunctions to facilitate the management of large terms representing such states. Because the metafunctions for each state component updater are solely responsible for creating state expressions (i.e., \"writing\") and the metafunctions for each state component accessor are solely responsible for extracting values (i.e., \"reading\") from such state expressions, they can maintain their own normal form, use HIDE to prevent other parts of ACL2 from inspecting them, and use honsing to uniquely represent state expressions. The last feature makes it possible to memoize the metafunctions, which can improve proof performance in some machine models. This paper describes a general-purpose ACL2 book modeling a byte-addressed memory supporting \"mixed\" reads and writes. By \"mixed\" we mean that reads need not correspond (in address or number of bytes) with writes. Verified metafunctions simplify such \"read-over-write\" expressions while hiding the potentially large state expression. A key utility is a function that determines an upper bound on the value of a symbolic arithmetic expression, which plays a role in resolving writes to addresses given by symbolic expressions. We also report on a preliminary experiment with the book, which involves the production of states containing several million function calls.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.8"}, {"primary_key": "4516370", "vector": [], "sparse_vector": [], "title": "Extending ACL2 with SMT Solvers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present our extension of ACL2 with Satisfiability Modulo Theories (SMT) solvers using ACL2's trusted clause processor mechanism. We are particularly interested in the verification of physical systems including Analog and Mixed-Signal (AMS) designs. ACL2 offers strong induction abilities for reasoning about sequences and SMT complements deduction methods like ACL2 with fast nonlinear arithmetic solving procedures. While SAT solvers have been integrated into ACL2 in previous work, SMT methods raise new issues because of their support for a broader range of domains including real numbers and uninterpreted functions. This paper presents Smtlink, our clause processor for integrating SMT solvers into ACL2. We describe key design and implementation issues and describe our experience with its use.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.6"}, {"primary_key": "4516414", "vector": [], "sparse_vector": [], "title": "Fix Your Types.", "authors": ["Sol Swords", "<PERSON>"], "summary": "When using existing ACL2 datatype frameworks, many theorems require type hypotheses. These hypotheses slow down the theorem prover, are tedious to write, and are easy to forget. We describe a principled approach to types that provides strong type safety and execution efficiency while avoiding type hypotheses, and we present a library that automates this approach. Using this approach, types help you catch programming errors and then get out of the way of theorem proving.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192.2"}, {"primary_key": "4521441", "vector": [], "sparse_vector": [], "title": "Proceedings Thirteenth International Workshop on the ACL2 Theorem Prover and Its Applications, Austin, Texas, USA, 1-2 October 2015.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This volume contains the proceedings of the Thirteenth International Workshop on the ACL2 Theorem Prover and Its Applications, ACL2 2015, a two-day workshop held in Austin, Texas, USA, on October 1-2, 2015. ACL2 workshops occur at approximately 18-month intervals and provide a major technical forum for researchers to present and discuss improvements and extensions to the theorem prover, comparisons of ACL2 with other systems, and applications of ACL2 in formal verification. ACL2 is a state-of-the-art automated reasoning system that has been successfully applied in academia, government, and industry for specification and verification of computing systems and in teaching computer science courses. In 2005, <PERSON>, <PERSON>, and <PERSON> were awarded the 2005 ACM Software System Award for their work on ACL2 and the other theorem provers in the <PERSON><PERSON><PERSON> family.", "published": "2015-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.192"}]