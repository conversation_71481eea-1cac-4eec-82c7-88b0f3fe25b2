[{"primary_key": "4179498", "vector": [], "sparse_vector": [], "title": "Decomposed Reachability Analysis for Nonlinear Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce an approach to conservatively abstract a nonlinear continuous system by a hybrid automaton whose continuous dynamics are given by a decomposition of the original dynamics. The decomposed dynamics is in the form of a set of lower-dimensional ODEs with time-varying uncertainties whose ranges are defined by the hybridization domains. We propose several techniques in the paper to effectively compute abstractions and flowpipe overapproximations. First, a novel method is given to reduce the overestimation accumulation in a Taylor model flowpipe construction scheme. Then we present our decomposition method, as well as the framework of on-the-fly hybridization. A combination of the two techniques allows us to handle much larger, nonlinear systems with comparatively large initial sets. Our prototype implementation is compared with existing reachability tools for offline and online flowpipe construction on challenging benchmarks of dimensions ranging from 7 to 30. Our code has successfully passed the artifact evaluation.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.011"}, {"primary_key": "4179499", "vector": [], "sparse_vector": [], "title": "Timeline: An Operating System Abstraction for Time-Aware Applications.", "authors": ["<PERSON><PERSON>", "Sandeep D&apos;Souza", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Having a shared and accurate sense of time is critical to distributed Cyber-Physical Systems (CPS) and the Internet of Things (IoT). Thanks to decades of research in clock technologies and synchronization protocols, it is now possible to measure and synchronize time across distributed systems with unprecedented accuracy. However, applications have not benefited to the same extent due to limitations of the system services that help manage time, and hardware-OS and OS-application interfaces through which timing information flows to the application. Due to the importance of time awareness in a broad range of emerging applications, running on commodity platforms and operating systems, it is imperative to rethink how time is handled across the system stack. We advocate the adoption of a holistic notion of Quality of Time (QoT) that captures metrics such as resolution, accuracy, and stability. Building on this notion we propose an architecture in which the local perception of time is a controllable operating system primitive with observable uncertainty, and where time synchronization balances applications' timing demands with system resources such as energy and bandwidth. Our architecture features an expressive application programming interface that is centered around the abstraction of a timeline - a virtual temporal coordinate frame that is defined by an application to provide its components with a shared sense of time, with a desired accuracy and resolution. The timeline abstraction enables developers to easily write applications whose activities are choreographed across time and space. Leveraging open source hardware and software components, we have implemented an initial Linux realization of the proposed timeline-driven QoT stack on a standard embedded computing platform. Results from its evaluation are also presented.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.027"}, {"primary_key": "4179500", "vector": [], "sparse_vector": [], "title": "Schedulability Analysis for a General Model of Mixed-Criticality Recurrent Real-Time Tasks.", "authors": ["<PERSON><PERSON>"], "summary": "In their widely-cited survey on mixed-criticality systems, <PERSON> and <PERSON> describe a very general model for representing mixed-criticality sporadic tasks. In this general model multiple estimates, at differing levels of assurance, are specified for each of the three parameters -- worst-case execution time (WCET), relative deadline, and period -- characterizing a 3-parameter sporadic task. The preemptive uniprocessor scheduling of systems of such tasks is considered. A scheduling algorithm is presented, proved correct, and quantitatively characterized via the speedup factor metric for dual-criticality systems of such tasks. To our knowledge, this is the first work to conduct any form of analysis of task systems that are represented using this general model.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.012"}, {"primary_key": "4179501", "vector": [], "sparse_vector": [], "title": "The Federated Scheduling of Systems of Mixed-Criticality Sporadic DAG Tasks.", "authors": ["<PERSON><PERSON>"], "summary": "Under the federated approach to multiprocessor scheduling, each individual task is either restricted to execute upon a single processor (as in partitioned scheduling), or has exclusive access to all the processors upon which it may execute. The federated scheduling of a mixed-criticality collection of independent recurrent tasks is studied here. A model is proposed for mixed-criticality recurrent tasks that extends the (previously-proposed) implicit-deadline sporadic DAG tasks model to account for mixed criticalities. A federated scheduling algorithm for systems of such tasks is presented and proved correct, and a quantitative evaluation of its efficacy derived via the widely-used speedup factor metric.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.030"}, {"primary_key": "4179502", "vector": [], "sparse_vector": [], "title": "Average probabilistic response time analysis of tasks with multiple probabilistic parameters.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The authors propose to study the average response time distribution of tasks owing to the pessimism introduced by the analysis of the synchronous case and unsafe response time obtained by simulation. In this regard, the problem we address is twofold. First, we need to determine a relevant and safe probabilistic feasibility interval that is representative of the systems behaviour over its entire lifetime. Second, we need to compute the response time distribution of any job of a task within this feasibility interval in order to combine them into an average distribution. This is a complex problem due to the fact that job arrivals are variable as well as their execution times.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.046"}, {"primary_key": "4179503", "vector": [], "sparse_vector": [], "title": "A Framework for Supporting Real-Time Applications on Dynamic Reconfigurable FPGAs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Computing platforms are evolving towards heterogeneous architectures including processors of different types and field programmable gate arrays (FPGAs), used as hardware accelerators for speeding up specific functions. The increasing capacity and performance of modern FPGAs, with their partial reconfiguration capabilities, have made them attractive in several application domains, including space applications.This paper proposes a framework for supporting the development of safety-critical real-time systems that exploit hardware accelerators developed through FPGAs with dynamic partial reconfiguration capabilities.A model is first presented and then used to derive a response-time analysis to verify the schedulability of a real-time task set under given constraints and assumptions. Although the analysis is based on a generic model, the proposed framework has been conceived to account for several real-world constraints present on today's platforms and has been practically validated on the Zynq platform, showing that it can actually be supported by state-of-the-art technologies. Finally, a number of experiments are reported to evaluate the worst-case performance of the proposed approach on synthetic workload.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.010"}, {"primary_key": "4179504", "vector": [], "sparse_vector": [], "title": "A Blocking Bound for Nested FIFO Spin Locks.", "authors": ["<PERSON>", "Björn B. Brandenburg", "<PERSON>"], "summary": "Bounding worst-case blocking delays due to lock contention is a fundamental problem in the analysis of multiprocessor real-time systems. However, virtually all fine-grained (i.e., non-asymptotic) analyses published to date make a simplifying (but impractical) assumption: critical sections must not be nested. This paper overcomes this fundamental limitation and presents the first fine-grained blocking bound for nested non-preemptive FIFO spin locks under partitioned fixed-priority scheduling. To this end, a new analysis method is introduced, based on a graph abstraction that reflects all possible resource conflicts and transitive delays.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.036"}, {"primary_key": "4179505", "vector": [], "sparse_vector": [], "title": "Global Scheduling Not Required: Simple, Near-Optimal Multiprocessor Real-Time Scheduling with Semi-Partitioned Reservations.", "authors": ["Björn B. Brandenburg", "Mahircan Gul"], "summary": "Prior work has identified several optimal algorithms for scheduling independent, implicit-deadline sporadic (or periodic) real-time tasks on identical multiprocessors. These algorithms, however, are subject to high conceptual complexity and typically incur considerable runtime overheads. This paper establishes that, empirically, near-optimal schedulability can also be achieved with a far simpler approach that combines three well-known techniques (reservations, semi-partitioned scheduling, and period transformation) with some novel task-placement heuristics.In large-scale schedulability experiments, the proposed approach is shown to achieve near-optimal hard real-time schedulability (99+% schedulable utilization) across a wide range of processor and task counts. With an implementation in LITMUSRT, the proposed approach is shown to be practical and to incur only low runtime overheads, comparable to a conventional partitioned scheduler. It is further shown that basic slack management techniques can help to avoid more than 50% of all migrations of semi-partitioned reservations if tasks execute on average for less than their provisioned worst-case execution time.Two main conclusions are drawn: pragmatically speaking, global scheduling is not required to support static workloads of independent, implicit-deadline sporadic (or periodic) tasks; and since such simple workloads are well supported, future research on multiprocessor real-time scheduling should consider more challenging workloads (e.g., adaptive workloads, dynamic task arrivals or mode changes, shared resources, precedence constraints, etc.).", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.019"}, {"primary_key": "4179506", "vector": [], "sparse_vector": [], "title": "Systems with Dynamic Real-Time Guarantees in Uncertain and Faulty Execution Environments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In many practical real-time systems, the physical environment and the system platform can impose uncertain execution behaviour to the system. For example, if transient faults are detected, the execution time of a task instance can be increased due to recovery operations. Such fault recovery routines make the system very vulnerable with respect to meeting hard real-time deadlines. In theory and in practical systems, this problem is often handled by aborting not so important tasks to guarantee the response time of the more important tasks. However, for most systems such faults occur rarely and the results of not so important tasks might still be useful, even if they are a bit late. This implicates to not abort these not so important tasks but keep them running even if faults occur, provided that the more important tasks still meet their hard real time properties. In this paper, we present Systems with Dynamic Real-Time Guarantees to model this behaviour and determine if the system can provide full timing guarantees or limited timing guarantees without any online adaptation after a fault occurred. We present a schedulability test, provide an algorithm for optimal priority assignment, determine the maximum interval length until the system will again provide full timing guarantees and explain how we can monitor the system state online. The approaches presented in this paper can also be applied to mixed criticality systems with dual criticality levels.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.037"}, {"primary_key": "4179507", "vector": [], "sparse_vector": [], "title": "Computational Complexity and Speedup Factors Analyses for Self-Suspending Tasks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In computing systems, an execution entity (job/process/task) may suspend itself when it has to wait for some activities to continue/finish its execution. For real-time embedded systems, such self-suspending behavior has been shown to cause substantial performance/schedulability degradation in the literature. There are two commonly adopted self-suspending sporadic task models in real-time systems: 1) dynamic self-suspension and 2) segmented self-suspension sporadic task models. A dynamic self-suspending sporadic task is specified with an upper bound on the maximum suspension time for a job (task instance), which allows a job to dynamically suspend itself as long as the suspension upper bound is not violated. By contrast, a segmented self-suspending sporadic task has a predefined execution and suspension pattern in an interleaving manner.Even though some seemingly positive results have been reported for self-suspending task systems, the computational complexity and the theoretical quality (with respect to speedup factors) of fixed-priority preemptive scheduling have not been reported. This paper proves that the schedulability analysis for fixed-priority preemptive scheduling even with only one segmented self-suspending task as the lowest-priority task is coNP-hard in the strong sense. For dynamic self-suspending task systems, we show that the speedup factor for any fixed-priority preemptive scheduling, compared to the optimal schedules, is not bounded by a constant or by the number of tasks, if the suspension time cannot be reduced by speeding up. Such a statement of unbounded speedup factors can also be proved for earliest-deadline-first (EDF), least-laxity-first (LLF), and earliest-deadline-zero-laxity (EDZL) scheduling algorithms. However, if the suspension time can be reduced by speeding up coherently or the suspension time of each task is not comparable with (i.e., sufficiently smaller than) its relative deadline, then we successfully show that rate-monotonic scheduling has a constant speedup factor, with respect to the optimal schedules, for implicit-deadline task systems.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.039"}, {"primary_key": "4179508", "vector": [], "sparse_vector": [], "title": "Value-Based Task Scheduling for Nonvolatile Processor-Based Embedded Devices.", "authors": ["<PERSON><PERSON><PERSON>", "Tai<PERSON>ng Cheng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Energy harvesting wearable sensor nodes offer low maintenance and high mobility, but suffer from unstable power supply and insufficient energy. Featuring low standby power and instant backup and restore operations, nonvolatile processors have emerged as one of the most promising technologies for the improvement of energy harvesting wearable devices. However, device quality of service (QoS) is still limited by lack of sufficient energy. To address this problem, this paper attempts to maximize QoS by optimizing task scheduling for DVFS-enabled nonvolatile processor-based embedded devices. First, we model the task scheduling problem as an optimization problem to maximize the total value (which represents QoS) given available harvested energy and time. We then prove the problem to be NP-hard. In addition, we propose a pseudopolynomial-time optimal algorithm based on dynamic programming, as well as an approximation algorithm that allows for a trade-off between running time and total value. We evaluate algorithm performance on an ultra-lowpower platform produced by Texas Instruments, and conduct extensive simulations with different system models and task sets. The results demonstrate that the platform can execute optimal sequences derived by the dynamic-programming algorithm, with differences between simulation results and real traces of less than 1%. Also, our approximate algorithm executes approximately 10 5 times faster than the dynamic-programming algorithm at a value degradation cost of less than 3%.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.032"}, {"primary_key": "4179509", "vector": [], "sparse_vector": [], "title": "k2Q: A Quadratic-Form Response Time and Schedulability Analysis Framework for Utilization-Based Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a general response-time analysis and schedulability-test framework, called k2Q (k to Q). It provides automatic constructions of closed-form quadratic bounds or utilization bounds for a wide range of applications in real-time systems under fixed-priority scheduling. The key of the framework is a k-point schedulability test or a k-point response time analysis that is based on the utilizations and the execution times of k-1 higher-priority tasks. The natural condition of k2Q is a quadratic form for testing the schedulability or analyzing the response time. The response time analysis and the schedulability analysis provided by the framework can be viewed as a \"blackbox'' interface that can result in sufficient utilization-based analysis. Since the framework is independent from the task and platform models, it can be applied to a wide range of applications.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.041"}, {"primary_key": "4179510", "vector": [], "sparse_vector": [], "title": "Reconciling the Tension Between Hardware Isolation and Data Sharing in Mixed-Criticality, Multicore Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent work involving a mixed-criticality framework called MC2 has shown that, by combining hardware-management techniques and criticality-aware task provisioning, capacity loss can be significantly reduced when supporting real-time workloads on multicore platforms. However, as in most other prior research on multicore hardware management, tasks were assumed in that work to not share data. Data sharing is problematic in the context of hardware management because it can violate the isolation properties hardware-management techniques seek to ensure. Clearly, for research on such techniques to have any practical impact, data sharing must be permitted. Towards this goal, this paper presents a new version of MC2 that permits tasks to share data within and across criticality levels through shared memory. Several techniques are presented for mitigating capacity loss due to data sharing. The effectiveness of these techniques is demonstrated by means of a large-scale, overhead-aware schedulability study driven by micro-benchmark data.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.015"}, {"primary_key": "4179511", "vector": [], "sparse_vector": [], "title": "Preemptive Uniprocessor EDF Schedulability Analysis with Preemption Costs Considered.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores preemption-costs cognizant schedulability and sustainability issues in the uniprocessor EDF scheduling of sporadic task systems.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.047"}, {"primary_key": "4179512", "vector": [], "sparse_vector": [], "title": "Enabling Predictable Wireless Data Collection in Severe Energy Harvesting Environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Micro-powered wireless embedded devices are widely used in many application domains. Their efficiency in practice, however, is significantly constrained by the dual limitations of low harvesting rates and tiny energy buffer. Recent research presents a network stack that efficiently fragments a large packet into many smaller packets that can fit within the available energy in the energy buffer of limited size. While this fragmentation technique represents a major step forward in solving the minuscule energy budget problem, it also introduces a tremendous practical challenge where potentially many fragmented packets belonging to different devices may contend for the communication channel. Designing purely heuristic-based packet transmission protocol is undesirable because the resulting per-packet and end-to-end transmission delay are unknown, thus causing unpredictable system performance which is unacceptable for many applications with real-time constraints. In this paper, we first formulate this packet transmission scheduling problem considering physical properties of the charging and transmission processes. We then develop a novel packet prioritization and transmission protocol NERF that yields tight and predictable delay bounds for transmitting packets from multiple micropowered devices to a charger. We have implemented our protoco on top of the WISP 4.1 platform and the SPEEDWAY RFID READER, and conducted validation experiments. Our experiments validate the correctness of our implementation and show that NERF can reduce the total collection delay by 40% when compared to an existing protocol ALOHA. We have also performed extensive data trace-driven simulations. Simulation results demonstrate the effectiveness of our proposed protocol. On average, our protocol yields an over 30%improvement in terms of runtime transmission delay compared to existing methods, while being able to guarantee tight and provable response time bounds.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.024"}, {"primary_key": "4179513", "vector": [], "sparse_vector": [], "title": "Closing the Loop for the Selective Conversion Approach: A Utilization-Based Test for Hard Real-Time Suspending Task Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies the problem of scheduling hard real-time sporadic suspending task systems under global earliest-deadline-first. A novel selective suspension-tocomputation conversion approach has been developed, with the fundamental idea of selecting and converting a limited set of jobs' suspensions into computation to eliminate suspension-induced pessimism in the analysis. To the best of our knowledge, this approach yields the first utilization-based test for globally-scheduled suspending task systems, which analytically dominates the suspension-oblivious approach and dramatically improves schedulability upon existing tests by over 50% on average, as shown by experiments. We believe this paper closes the loop on applying the methodology of selective suspension-to-computation conversion to analyze realtime suspending task systems.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.040"}, {"primary_key": "4179514", "vector": [], "sparse_vector": [], "title": "Energy-Aware Real-Time Task Scheduling on Local/Shared Memory Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "<PERSON>"], "summary": "The rapid development of the Internet of Things (IoT) has increased the requirement on the processing capabilities of sensors, mobile phones and smart devices. Meanwhile, energy efficiency techniques are in desperate need as most devices in the IoT systems are battery powered. Following the above two trends, this work explores the memory system energy efficiency for a general multi-core architecture. This architecture integrates a local memory in each processing core, with a large off-chip memory shared among multiple cores. Decisions need to be made on whether tasks will be executed with the shared memory or the local memory to minimize the total energy consumption within real-time constraints. This paper proposes optimal schemes as well as a polynomial-time approximation algorithm with constant ratio. The complexity analysis of the problem for different task and system models is also presented. Experimental results show that the proposed approximation algorithm performs close to the optimal solution in average.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.034"}, {"primary_key": "4179515", "vector": [], "sparse_vector": [], "title": "Dynamic Budget Management with Service Guarantees for Mixed-Criticality Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many existing studies on mixed-criticality (MC) scheduling assume that low-criticality budgets for high-criticality applications are known apriori. These budgets are primarily used as guidance to determine when the scheduler should switch the system mode from low to high. Based on this key observation, in this paper we propose a dynamic MC scheduling model under which low-criticality budgets for individual high-criticality applications are determined at runtime as opposed to being fixed offline. To ensure sufficient budget for high-criticality applications at all times, we use offline schedulability analysis to determine a system-wide total low-criticality budget allocation for all the high-criticality applications combined. This total budget is used as guidance in our model to determine the need for a mode-switch. The runtime strategy then distributes this total budget among the various applications depending on their execution requirement and with the objective of postponing mode-switch as much as possible. We show that this runtime strategy is able to postpone mode-switches for a longer time than any strategy that uses a fixed low-criticality budget allocation for each application. Finally, since we are able to control the total budget allocation for high-criticality applications before mode-switch, we also propose techniques to determine these budgets considering system-wide objectives such as schedulability and service guarantee for low-criticality applications.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.014"}, {"primary_key": "4179516", "vector": [], "sparse_vector": [], "title": "Right on Time Distributed Shared Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The demand for real-time data storage in distributed control systems (DCSs) is growing. Yet, providing real-time DCS guarantees is challenging, especially when more and more sensor and actuator devices are connected to industrial plants and message loss needs to be taken into account.In this paper, we investigate how to build a shared memory abstraction for DCSs as a first step towards implementing different shared storage systems in a DCS context. We first prove that, in the presence of host crashes and message losses, the necessary guarantees of such an abstraction are impossible to implement using a traditional approach that has no access to the internals of existing DCS services, e.g., a modular approach where algorithms are built on top of existing software blocks like failure detectors. We propose a white-box approach that utilizes messages of existing services in any DCS as the sole means of communication. More precisely, we present TapeWorm, an algorithm that attaches itself to the heartbeat messages of the failure detector component in DCSs. We prove that TapeWorm implements the desired shared memory guarantees for applications running on a DCS. We also analyze the performance of TapeWorm and we showcase ways of adapting TapeWorm to various application needs and workloads.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.038"}, {"primary_key": "4179517", "vector": [], "sparse_vector": [], "title": "Exploring Opportunistic Execution for Integrating Security into Legacy Hard Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rakesh B. Bobba", "<PERSON><PERSON><PERSON>"], "summary": "Due to physical isolation as well as use of proprietary hardware and protocols, traditional real-time systems (RTS) were considered to be invulnerable to security breaches and external attacks. This assumption is being challenged by recent attacks that highlight vulnerabilities in RTS. Besides, a straightforward integration of security mechanisms might compromise the safety and predictability guarantees of such systems. In this paper, we focus on integrating security mechanisms into RTS (especially legacy RTS) and define a metric to measure the effectiveness of such integration. We combine opportunistic execution with hierarchical scheduling to maintain compatibility with legacy systems while still providing flexibility. The proposed approach is shown to increase the security posture of RTS without impacting their temporal (and hence, safety) constraints.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.021"}, {"primary_key": "4179518", "vector": [], "sparse_vector": [], "title": "Resource-Oriented Partitioned Scheduling in Multiprocessor Systems: How to Partition and How to Share?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "When concurrent real-time tasks have to access shared resources, to prevent race conditions, the synchronization and resource access must ensure mutual exclusion, e.g., by using semaphores. That is, no two concurrent accesses to one shared resource are in their critical sections at the same time. For uniprocessor systems, the priority ceiling protocol (PCP) has been widely accepted and supported in real-time operating systems. However, it is still arguable whether there exists a preferable approach for resource sharing in multiprocessor systems. In this paper, we show that the proposed resource-oriented partitioned scheduling using PCP combined with a reasonable allocation algorithm can achieve a non-trivial speedup factor guarantee. Specifically, we prove that our task mapping and resource allocation algorithm has a speedup factor 11-6/(m+1) on a platform comprising m processors, where a task may request at most one shared resource and the number of requests on any resource by any single job is at most one. Our empirical investigations show that the proposed algorithm is highly effective in terms of task sets deemed schedulable.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.020"}, {"primary_key": "4179519", "vector": [], "sparse_vector": [], "title": "Real-Time Data and Energy Management in Microgrids.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Microgrids are desired in remote areas, such as islands and under developed countries. However, given the limited capacities of local energy generation and storage in such a community, it is extremely challenging for an isolated microgrid to balance the power demand and generation in real-time with dynamically changing energy demand. Meanwhile, more and more sensing devices (such as smart meters) are deployed in individual homes to monitor real-time energy data, which can be helpful for homes and microgrid to better schedule the workload and generation. However, it is still difficult to conduct real-time distributed control due to the unreliable sensing devices and communications between sensing devices and controllers. To address these issues in microgrids, we designed a novel approach for the system to i) process the collected sensing data, ii) reconstruct the missing data caused by sensing error or unreliable communication, and iii) predict the future demand for real-time distributed control with missing data in extreme situations. The control center then decides the operations of the local generator and each home decides the scheduling of the flexible workload of appliances based on the collected and predicted data. We conducted extensive experiments and simulations with real world energy consumption data from 100 homes for one year. The evaluation results show that our design can recover the missing data with more than 99% accuracy and our distributed control can balance power demand and generation in real-time and reduce the operational cost by 23%.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.017"}, {"primary_key": "4179520", "vector": [], "sparse_vector": [], "title": "End-to-End Real-Time Guarantees in Wireless Cyber-Physical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Huang", "<PERSON>", "<PERSON><PERSON>"], "summary": "In cyber-physical systems (CPS), the communication among the sensing, actuating, and computing elements is often subject to hard real-time constraints. Real-time communication among wireless network interfaces and real-time scheduling for complex, dynamic applications have been intensively studied. Despite these major efforts, there is still a significant gap to fill. In particular, the integration of several real-time components to provide end-to-end real-time guarantees between interfaces of distributed applications in wireless CPS is an unsolved problem. We thus present a distributed protocol that considers the complete transmission chain including peripheral busses, memory accesses, networking interfaces, and the wireless real-time protocol. Our protocol provably guarantees that message buffers along this chain do not overflow and that all messages received at the destination application interface meet their end-to-end deadlines. To achieve this while being adaptive to unpredictable changes in the system and the real-time traffic requirements, our protocol establishes at run-time a set of contracts among all major elements of the transmission chain based on a worst-case delay and buffer analysis of the overall system. Using simulations, we validate that our analytic bounds are both safe and tight.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.025"}, {"primary_key": "4179521", "vector": [], "sparse_vector": [], "title": "On the Decomposition-Based Global EDF Scheduling of Parallel Real-Time Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Real-time systems are shifting from single-core to multi-core processors, on which software must be parallelized to fully utilize the additional computation power. Recently different types of scheduling algorithms and analysis techniques have been proposed for parallel real-time tasks modeled as directed acyclic graphs (DAG). However, this field is still much less mature than traditional real-time scheduling of sequential tasks. In this paper, we study the decomposition-based scheduling for parallel real-time tasks, where a task graph is transferred to a set of independent sporadic tasks. In particular, we proposed a new decomposition strategy that better explores the feature of each task, represented by its structure characteristic value, to improve schedulability. The structure characteristic values do not only provide a clear guidance in task decomposition, but also can be directly used for schedulability tests, as well as to quantify the suboptimality of our scheduling algorithm in terms of capacity augmentation bounds. We conduct comprehensive experiments to evaluate the real-time performance of our proposed scheduling algorithm, against the state-of-the-art scheduling and analysis methods of different types. Experiment results show that our method consistently outperforms all of the previous methods under different parameter settings.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.031"}, {"primary_key": "4179522", "vector": [], "sparse_vector": [], "title": "Reducing Deadline Misses and Power Consumption in Real-Time Databases.", "authors": ["<PERSON>young<PERSON><PERSON>"], "summary": "In data-intensive real-time embedded applications, it is desirable to process data service requests in a timely manner using fresh data, consuming less power. However, related work is relatively scarce. In this paper, we present an effective approach to decrease both the deadline miss ratio and power consumption by merging similar real-time transactions, while systematically adapting the data freshness. In a simulation study, our approach considerably reduces deadline misses and power consumptions compared to the state-of-the-art baselines, supporting the required data freshness.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.033"}, {"primary_key": "4179523", "vector": [], "sparse_vector": [], "title": "Sporadic Decision-Centric Data Scheduling with Normally-off Sensors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Amotz Bar-Noy", "<PERSON>"], "summary": "The Internet of Things heralds a new generation of data-centric applications, where controllers connect to large numbers of heterogeneous sensing devices. We consider a model, where the control loop does not execute periodically. Instead, controllers are prompted by contextual cues to make one-off decisions, resulting in sporadic activations. Since the need for data arises only sporadically, sensors do not sample data continuously. Rather, they are normally off (e.g., to save energy), but are activated by the controller on demand, when data is needed. Collected data has validity intervals, after which it must be re-sampled, since the measured value may change. Once a decision is made based on the data, sensors are turned off again. We call this model sporadic decision-centric data scheduling with normally-off sensors. It gives rise to novel scheduling problems because of the way the timing of activation of different sensors affects load attributed to data sampling; the shorter the interval between activation of a given sensor and the time a corresponding decision is made, the lower the number of samples taken by that sensor to support the decision, and thus decision cost. The paper defines the aforementioned decision-centric data scheduling problem and derives the optimal scheduling policy, called EDEF-LVF, for this task model. Simulation results confirm the superiority of EDEF-LVF compared to several baselines.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.022"}, {"primary_key": "4179524", "vector": [], "sparse_vector": [], "title": "Offline Guarantee and Online Management of Power Demand and Supply in Cyber-Physical Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Since modern electric systems require to support various power-demand operations for user applications and system maintenance, they need advanced power management that jointly considers power demand by the operations and power supply from various sources, such as batteries, solar panels, and supercapacitors. In this paper, we develop a power scheduling framework for a reliable energy storage system with multiple power-supply sources and multiple power-demand operations. First, we provide an offline power-supply guarantee such that every power-demand operation completes its execution in time while the sum of power required by individual operations does not exceed the total power supplied by the entire energy storage system at any time. We find similarities between this and a real-time scheduling problem, and make a power-supply guarantee using real-time scheduling techniques. Second, we propose online power management that efficiently utilizes the surplus power (available at run-time) for system performance improvement. Our experimental results on a prototype demonstrate that the proposed framework not only guarantees the required power supply, but also enhances system performance by up to 33.1%.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.018"}, {"primary_key": "4179525", "vector": [], "sparse_vector": [], "title": "Closing the loop: towards control-aware design of adaptive real-time systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes a continuous toolchain from the original control system model to the resulting schedules. As a first step towards control-aware yet adaptive system design, we focused on a global, context-sensitive analysis of control and data flow across control-application layers and threads of execution. This step is of vital importance as these dependencies manifest differently on the various levels of abstraction. Consequently, we extract semantics and internal dependencies directly from control-system models by an extended version of the Real-Time Systems Compiler (RTSC), which is capable of analyzing and transforming real-time applications on the source code level.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.042"}, {"primary_key": "4179526", "vector": [], "sparse_vector": [], "title": "REVERT: Runtime Verification for Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Real-time systems are becoming more complex and open, thus increasing their development and verification costs. Although several static verification tools have been proposed over the last decades, they suffer from scalability and precision problems. As a result, the tools fail to cover all the necessary safety properties for realistic real-time applications involving a large number of components and tasks. Runtime verification (RV) is a formal technique that verifies properties during system execution with the support of monitors. The monitors are generated from formal languages using correct-by-construction generation methods. In this paper, we propose REVERT, a framework developed with a focus on the verification of functional and non-functional properties with timing constraints. The contribution of this work is twofold: (i) a domain-specific specification language allowing the definition of requirements for real-time applications; (ii) a novel mechanism to generate monitors, with state-space and time guarantees, capable of identifying and reacting to timing properties defined with the proposed specification language.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.044"}, {"primary_key": "4179527", "vector": [], "sparse_vector": [], "title": "On-Line Event-Driven Scheduling for Electric Vehicle Charging via Park-and-Charge.", "authors": ["Fanxin Kong", "<PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON>"], "summary": "Large-scale charging stations become indispensable infrastructure to support the rapid proliferation of electric vehicles. Their operation modes have drawn great attention from both academia and industry. One promising mode called park-and-charge has been recently introduced. This new mode allows customers to park their electric vehicles at a parking lot, where the vehicles are charged during the parking time. Several small-scale experiments, such as the V-Charge project and General Motors' E-Motor plant, have demonstrated its potential. A key enabler for deploying this mode to large-scale stations is effective and efficient charging load scheduling methods. Most existing works confine to the time-driven scheduling policy due to their sole focus on the charging service. Applying their solutions to the park-and-charge mode would jeopardize the unitization of charging resource or cause frequent charging mode switching. This inapplicability motivates us to explore the feasibility and benefits of exploiting the event-driven scheduling policy in park-and-charge systems. Further, to better characterize charging load in this mode, we propose to adopt a metered model, by which a system gains value in proportion to the served charging demand. To be specific, the objective of this paper is to carry out both theoretical and experimental analysis for event-driven algorithms adapted to this metered model. We leverage both the competitive analysis and resource augmentation to demonstrate the non-constant and constant performance bounds for the earliest-deadline-first and highest-value-first algorithms respectively. Moreover, we provide a stronger theoretical result, i.e., the performance bound for the whole class of work-conserving scheduling algorithms. Through extensive simulations, we validate the proposed theoretical results and further provide interesting findings from the in-depth analysis of the simulation results.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.016"}, {"primary_key": "4179528", "vector": [], "sparse_vector": [], "title": "Fast and accurate cycle estimation through hybrid instruction set simulation for embedded systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Insik Shin"], "summary": "In this paper, we propose an accurate cycle estimation framework which allows to use multiple instruction set simulators to simulate not only processors but also diverse peripheral devices. An instruction set simulator runs on a host machine to mimic functional behaviors of instructions running on a target hardware. It allows to estimate the execution time of software in a fast and accurate way and validate a system even when its target hardware does not yet exist or is not available.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.049"}, {"primary_key": "4179529", "vector": [], "sparse_vector": [], "title": "Randomized Work Stealing for Large Scale Soft Real-Time Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chenyang Lu"], "summary": "Recent years have witnessed the convergence of two important trends in real-time systems: growing computational demand of applications and the adoption of processors with more cores. As real-time applications now need to exploit parallelism to meet their real-time requirements, they face a new challenge of scaling up computations on a large number of cores. Randomized work stealing has been adopted as a highly scalable scheduling approach for general-purpose computing. In work stealing, each core steals work from a randomly chosen core in a decentralized manner. Compared to centralized greedy schedulers, work stealing may seem unsuitable for real-time computing due to the non-predictable nature of random stealing. Surprisingly, our experiments with benchmark programs found that random work stealing (in Cilk Plus) delivers tighter distributions in task execution times than a centralized greedy scheduler (in GNU OpenMP).To support scalable soft real-time computing, we develop Real-Time Work-Stealing platform (RTWS), a real-time extension to the widely used Cilk Plus concurrency platform. RTWS employs federated scheduling to allocate cores to multiple parallel real-time tasks offline, while leveraging the work stealing scheduler to schedule each task on its dedicated cores online. RTWS supports parallel programs written in Cilk Plus and requires only task parameters that can be readily measured using existing Cilk Plus tools. Experimental results show that RTWS outperforms Real-Time OpenMP in term of deadline miss ratio, relative response time and resource efficiency on a 32-core system.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.028"}, {"primary_key": "4179530", "vector": [], "sparse_vector": [], "title": "EDF-VD Scheduling of Mixed-Criticality Systems with Degraded Quality Guarantees.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies real-time scheduling of mixed-criticality systems where low-criticality tasks are still guaranteed some service in the high-criticality mode, with reduced execution budgets. First, we present a utilization-based schedulability test for such systems under EDF-VD scheduling. Second, we quantify the suboptimality of EDF-VD (with our test condition) in terms of speedup factors. In general, the speedup factor is a function with respect to the ratio between the amount of resource required by different types of tasks in different criticality modes, and reaches 4/3 in the worst case. Furthermore, we show that the proposed utilization-based schedulability test and speedup factor results apply to the elastic mixed-criticality model as well. Experiments show effectiveness of our proposed method and confirm the theoretical suboptimality results.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.013"}, {"primary_key": "4179531", "vector": [], "sparse_vector": [], "title": "Time-Accurate ASM as a Refinement Scheme for Worst-Case Execution Time Estimation in Hard Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Vladimir<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce a new formal modeling language that enriches the ground Abstract State Machine (ASM) formalism with the notion of time. Our temporal language is part of a larger HiTAsm language that also features hierarchical abstractions and is custom tailored for WCET analysis. The main differences with other ASM time languages is that HiTAsm correctness is proved using the same ASM metalanguage in which it is later integrated. The main purpose of this paper is to introduce the time in the HiTAsm and to prove its correctness.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.051"}, {"primary_key": "4179532", "vector": [], "sparse_vector": [], "title": "Integrating the calculation of preemption and persistence related cache overhead.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we highlight the pessimism of independently calculating cache-related preemption delays (CRPDs) and cache persistence reload overheads (CPROs). We propose a first solution to reduce that pessimism by integrating the calculation of CRPDs and CPROs. However, the proposed result is limited to the useful memory blocks (UCB)-union and CPRO-union approaches. Two methods that are known to be simple but pessimistic.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.045"}, {"primary_key": "4179533", "vector": [], "sparse_vector": [], "title": "BUNDLE: Real-Time Multi-threaded Scheduling to Reduce Cache Contention.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Research on hard real-time systems and their models has predominately focused upon single-threaded tasks. When multi-threaded tasks are introduced to the classical real-time model the individual threads are treated as distinct tasks, one for each thread. These artificial tasks often share the deadline, period, and worst case execution time of their parent task. In the presence of instruction and data caches this view is overly pessimistic, failing to account for the execution time benefit of cache hits when multiple threads of execution share a memory address space.This work takes a new perspective on instruction caches. Treating the cache as a benefit to schedulability for a single task with m threads. To realize the \"inter-thread cache benefit\" a new scheduling algorithm, BUNDLE, and accompanying method for calculating the worst-case execution time (WCET) including cache overhead (WCET+O) method are proposed. BUNDLE permits threads to execute across conflict free regions, and blocks those threads that would create an unnecessary cache conflict. The WCET bound is determined for the entire set of m threads, rather than treating each thread as a distinct task. Both the scheduler and WCET+O method rely on the calculation of conflict free regions which are found by a static analysis method of the task object. By virtue of this perspective the system's total execution execution time is reduced and is reflected in a tighter WCET+O bound compared to the techniques applied to the classical model. Obtaining this tighter bound requires the integration of three typically independent areas: WCET, schedulability, and cache-related preemption delay analysis.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.035"}, {"primary_key": "4179534", "vector": [], "sparse_vector": [], "title": "Real-Time Capabilities of HSA Compliant COTS Platforms.", "authors": ["Nandinbaatar Tsog", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "During recent years, the interest in using heterogeneous computing architecture in industrial applications has increased dramatically. These architectures provide the computational power that makes them attractive for many industrial applications. However, most of these existing heterogeneous architectures suffer from the following limitations: difficulties of heterogeneous parallel programming and high communication cost between the computing units. To overcome these disadvantages, several leading hardware manufacturers have formed the HSA Foundation to develop a new hardware architecture: Heterogeneous System Architecture (HSA). In this paper, we investigate the suitability of using HSA for real-time embedded systems. A preliminary experimental study has been conducted to measure massive computing power and timing predictability of HSA.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.043"}, {"primary_key": "4179535", "vector": [], "sparse_vector": [], "title": "Exploiting Power Grid for Accurate and Secure Clock Synchronization in Industrial IoT.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Desynchronized clocks among nodes in industrial Internet of Things (IoT) can degrade system performance and even lead to safety incidents. Clock synchronization protocols based on network message exchanges, though widely used in current industrial systems, are susceptible to delay attacks against the packet transmission. This vulnerability cannot be solved by conventional security measures such as encryption, and remains an open problem. This paper proposes to use the sine voltage waveform of a utility power grid to synchronize \"things\" connected to the same grid. Our experiments demonstrate that minute fluctuations of the voltage's cycle length encode fine-grained global time information in a city-scale utility grid. Based on this key result, we develop a clock synchronization approach that achieves sub-ms accuracy and is provably secure against packet delay attacks. Implementation results show that our approach achieves an average synchronization error of 0.1 ms between two IoT nodes that are 10 km apart. When the proposed system is deployed within the same floor of a building, the error reduces to 10 us.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.023"}, {"primary_key": "4179536", "vector": [], "sparse_vector": [], "title": "Timing-anomaly free dynamic scheduling of task-based parallel applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of estimating the makespan for parallel applications is overly pessimistic bounds either due to load imbalance issues plaguing static scheduling methods or due to timing anomalies plaguing dynamic scheduling methods for multicore architectures. While dynamic scheduling (where tasks are not pre-assigned) offers better utilization of the processors, it suffers from timing anomalies. This paper contributes with a new timing-anomaly-free dynamic scheduling algorithm, called the Out-of-(priority)-order Lazy (O-Lazy) that offers safe and tighter estimation of the makespan of parallel applications.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.050"}, {"primary_key": "4179537", "vector": [], "sparse_vector": [], "title": "Towards code metrics for benchmarking timing analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-Preik<PERSON>"], "summary": "Comprehensive evaluations of the effectiveness of worst-case execution time (WCET) analyzers require a selection of benchmarks that pose a challenge to these tools. In this paper, we identify pitfalls that are associated with selecting such benchmarks based on complexity metrics (e.g., the number of loops contained in a program), which in part are caused by the fact that complexity measures are not necessarily stable in the face of compiler optimizations. To address these problems, we are developing a tool that automatically assesses the resilience of a benchmark against compiler optimizations by tracking complexity measures across different optimization levels. In combination with information on the data dependency of control flows, which is also provided by our tool, this allows users to find and discard benchmarks that appear challenging for WCET analyzers at the source-code level, but in fact are trivial at the machine-code level where the actual analysis is performed.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.048"}, {"primary_key": "4179538", "vector": [], "sparse_vector": [], "title": "On the Dominance of Minimum-Parallelism Multiprocessor Supply.", "authors": ["<PERSON><PERSON> Yang", "<PERSON>"], "summary": "Many approaches have been proposed to enable disparate real-time software components to share a physical multiprocessor platform by giving each component the \"illusion\" of executing on a dedicated virtual platform. Such an illusion is supported by specifying a supply interface that indicates how computation time is made available to a component over time. A number of approaches for defining such interfaces have been proposed: so many that sifting through them all can be confusing for the practitioner. In the case of soft real-time applications, one particular proposed interface-minimum-parallelism (MP) supply-has been shown to enable the co-scheduling of different components with no utilization loss. In the case of hard real-time applications, it follows from prior work that MP supply easily dominates other choices if the simplifying assumption is made that supply is allocated on different processors using a common, synchronized allocation period. The main contribution of this paper is to show that the dominance of MP supply is retained if this simplifying assumption is removed, provided the period of allocation is defined properly. This result suggests that MP supply should be the focus in future work on real-time multiprocessor virtualization.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.029"}, {"primary_key": "4179539", "vector": [], "sparse_vector": [], "title": "MARACAS: A Real-Time Multicore VCPU Scheduling Framework.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper describes a multicore scheduling and load-balancing framework called MARACAS, to address shared cache and memory bus contention. It builds upon prior work centered around the concept of virtual CPU (VCPU) scheduling. Threads are associated with VCPUs that have periodically replenished time budgets. VCPUs are guaranteed to receive their periodic budgets even if they are migrated between cores. A load balancing algorithm ensures VCPUs are mapped to cores to fairly distribute surplus CPU cycles, after ensuring VCPU timing guarantees. MARACAS uses surplus cycles to throttle the execution of threads running on specific cores when memory contention exceeds a certain threshold. This enables threads on other cores to make better progress without interference from co-runners. Our scheduling framework features a novel memory-aware scheduling approach that uses performance counters to derive an average memory request latency. We show that latency-based memory throttling is more effective than rate-based memory access control in reducing bus contention. MARACAS also supports cache-aware scheduling and migration using page recoloring to improve performance isolation amongst VCPUs. Experiments show how MARACAS reduces multicore resource contention, leading to improved task progress.", "published": "2016-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2016.026"}]