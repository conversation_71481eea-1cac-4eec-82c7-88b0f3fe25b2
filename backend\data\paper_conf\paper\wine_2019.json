[{"primary_key": "3132749", "vector": [], "sparse_vector": [], "title": "On the Convergence of Swap Dynamics to Pareto-Optimal Matchings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study whether Pareto-optimal stable matchings can be reached via pairwise swaps in one-to-one matching markets with initial assignments. We consider housing markets, marriage markets, and roommate markets as well as three different notions of swap rationality. Our main results are as follows. While it can be efficiently determined whether a Pareto-optimal stable matching can be reached when defining swaps via blocking pairs, checking whether this is the case forallsuch sequences is computationally intractable. When defining swaps such that all involved agents need to be better off, even deciding whether a Pareto-optimal stable matching can be reached viasomesequence is intractable. This confirms and extends a conjecture made by <PERSON><PERSON><PERSON> et al. (2015), who have furthermore shown that convergence to a Pareto-optimal matching is guaranteed in housing markets with single-peaked preferences. We show that in marriage and roommate markets, single-peakedness is not sufficient for this to hold, but the stronger restriction of one-dimensional Euclidean preferences is.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_8"}, {"primary_key": "3132750", "vector": [], "sparse_vector": [], "title": "Scheduling Games with Machine-Dependent Priority Lists.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a scheduling game in which jobs try to minimize their completion time by choosing a machine to be processed on. Each machine uses an individual priority list to decide on the order according to which the jobs on the machine are processed. We characterize four classes of instances in which a pure Nash equilibrium (NE) is guaranteed to exist, and show by means of an example, that none of these characterizations can be relaxed. We then bound the performance of Nash equilibria for each of these classes with respect to the makespan of the schedule and the sum of completion times. We also analyze the computational complexity of several problems arising in this model. For instance, we prove that it is NP-hard to decide whether a NE exists, and that even for instances with identical machines, for which a NE is guaranteed to exist, it is NP-hard to approximate the best NE within a factor of\\(2-\\frac{1}{m}-\\epsilon \\)for every\\(\\epsilon >0\\). In addition, we study a generalized model in which players’ strategies are subsets of resources, where each resource has its own priority list over the players. We show that in this general model, even unweighted symmetric games may not have a pure NE, and we bound the price of anarchy with respect to the total players’ costs.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_21"}, {"primary_key": "3132751", "vector": [], "sparse_vector": [], "title": "Awareness of Voter Passion Greatly Improves the Distortion of Metric Social Choice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop new voting mechanisms for the case where voters and candidates are located in an arbitrary unknown metric space, and the goal is to choose a candidate minimizing social cost: the total distance of the voters to this candidate. Previous work has often assumed that only the ordinal preferences of the voters are known (instead of their true costs), and focused on minimizing distortion: the quality of the chosen candidate as compared to the best possible candidate. In this paper, we instead assume that a (very small) amount of information is known about the voter preferencestrengths, not just about their ordinal preferences. We provide mechanisms with much better distortion when this extra information is known as compared to mechanisms which use only ordinal information. We quantify tradeoffs between the amount of information known about preference strengths and the achievable distortion. We further provide advice about which type of information about preference strengths seems to be the most useful. Finally, we conclude by quantifying theideal candidate distortion, which compares the quality of the chosen outcome with the best possible candidate that could ever exist, instead of only the best candidate that is actually in the running.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_1"}, {"primary_key": "3132752", "vector": [], "sparse_vector": [], "title": "Autobidding with Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aranyak <PERSON>hta"], "summary": "Autobidding is becoming increasingly important in the domain of online advertising, and has become a critical tool used by many advertisers for optimizing their ad campaigns. We formulate fundamental questions around the problem of bidding for performance under very general affine cost constraints. We design optimal single-agent bidding strategies for the general bidding problem, in multi-slot truthful auctions. The novel contribution is to show a strong connection between bidding and auction design, in that the bidding formula is optimal if and only if the underlying auction is truthful. Next, we move from the single-agent view to a full-system view: What happens when all advertisers adopt optimal autobidding? We prove that in general settings, there exists an equilibrium between the bidding agents for all the advertisers. As our main result, we prove aPrice of Anarchybound: For any number of general affine constraints, the total value (conversions) obtained by the advertisers in the bidding-agent equilibrium is no less than 1/2 of what we could generate via a centralized ad allocation scheme, one which does not consider any auction incentives or provide any per-advertiser guarantee.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_2"}, {"primary_key": "3132754", "vector": [], "sparse_vector": [], "title": "Response Prediction for Low-Regret Agents.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Companies like Google and Microsoft run billions of auctions every day to sell advertising opportunities. Any change to the rules of these auctions can have a tremendous effect on the revenue of the company and the welfare of the advertisers and the users. Therefore, any change requires careful evaluation of its potential impacts. Currently, such impacts are often evaluated by running simulations or small controlled experiments. This, however, misses the important factor that the advertisers respond to changes. Our goal is to build a theoretical framework for predicting the actions of an agent (the advertiser) that is optimizing her actions in an uncertain environment. We model this problem using a variant of the multi-armed bandit setting where playing an arm is costly. The cost of each arm changes over time and is publicly observable. The value of playing an arm is drawn stochastically from a static distribution and is observed by the agent and not by us. We, however, observe the actions of the agent. Our main result is that assuming the agent is playing a strategy with a regret of at mostf(T) within the firstTrounds, we can learn to play the multi-armed bandits game (without observing the rewards) in such a way that the regret of our selected actions is at most\\(O(k^4(f(T)+1)\\log (T))\\), wherekis the number of arms.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_3"}, {"primary_key": "3132755", "vector": [], "sparse_vector": [], "title": "On the Price of Anarchy for High-Price Links.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study Nash equilibria and the price of anarchy in the classic model of Network Creation Games introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> in 2003. This is a selfish network creation model where players correspond to nodes in a network and each of them can create links to the other\\(n-1\\)players at a prefixed price\\(\\alpha > 0\\). The player’s goal is to minimise the sum of her cost buying edges and her cost for using the resulting network. One of the main conjectures for this model states that the price of anarchy, i.e. the relative cost of the lack of coordination, is constant for all\\(\\alpha \\). This conjecture has been confirmed for\\(\\alpha = O(n^{1-\\delta })\\)with\\(\\delta \\ge 1/\\log n\\)and for\\(\\alpha > 4n-13\\). The best known upper bound on the price of anarchy for the remaining range is\\(2^{O(\\sqrt{\\log n})}\\). We give new insights into the structure of the Nash equilibria for\\(\\alpha > n\\)and we enlarge the range of the parameter\\(\\alpha \\)for which the price of anarchy is constant. Specifically, we prove that for any small\\(\\epsilon >0\\), the price of anarchy is constant for\\(\\alpha > n(1+\\epsilon )\\)by showing that any biconnected component of any non-trivial Nash equilibrium, if it exists, has at most a constant number of nodes.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_23"}, {"primary_key": "3132756", "vector": [], "sparse_vector": [], "title": "Computing Equilibria of Prediction Markets via Persuasion.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Haifeng Xu"], "summary": "We study the computation of equilibria in prediction markets in perhaps the most fundamental special case with two players and three trading opportunities. To do so, we show equivalence of prediction market equilibria with those of a simpler signaling game with commitment introduced by <PERSON> and <PERSON><PERSON><PERSON> [18]. We then extend their results by giving computationally efficient algorithms for additional parameter regimes. Our approach leverages a new connection between prediction markets and Bayesian persuasion, which also reveals interesting conceptual insights.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_4"}, {"primary_key": "3132758", "vector": [], "sparse_vector": [], "title": "Fair and Efficient Cake Division with Connected Pieces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The classic cake-cutting problem provides a model for addressing fair and efficient allocation of a divisible, heterogeneous resource (metaphorically, the cake) among agents with distinct preferences. Focusing on a standard formulation of cake cutting, in which each agent must receive a contiguous piece of the cake, this work establishes algorithmic and hardness results for multiple fairness/efficiency measures. First, we consider the well-studied notion of envy-freeness and develop an efficient algorithm that finds a cake division (with connected pieces) wherein the envy is multiplicatively within a factor of\\(3 + o(1)\\). The same algorithm in fact achieves an approximation ratio of\\(3 + o(1)\\)for the problem of finding cake divisions with as large a Nash social welfare (\\(\\mathrm {NSW}\\)) as possible.\\(\\mathrm {NSW}\\)is another standard measure of fairness and this work also establishes a connection between envy-freeness and\\(\\mathrm {NSW}\\): approximately envy-free cake divisions (with connected pieces) always have near-optimal Nash social welfare. Furthermore, we develop an approximation algorithm for maximizing the\\(\\rho \\)-mean welfare–this unifying objective, with different values of\\(\\rho \\), interpolates between notions of fairness (\\(\\mathrm {NSW}\\)) and efficiency (average social welfare). Finally, we complement these algorithmic results by proving that maximizing\\(\\mathrm {NSW}\\)(and, in general, the\\(\\rho \\)-mean welfare) is\\(\\mathrm {APX}\\)-hard in the cake-division context.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_5"}, {"primary_key": "3132760", "vector": [], "sparse_vector": [], "title": "A New Approach to Fair Distribution of Welfare.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider transferable-utility profit-sharing games that arise from settings in which agents need to jointly choose one of several alternatives, and may use transfers to redistribute the welfare generated by the chosen alternative. One such setting is the Shared–Rental problem, in which students jointly rent an apartment and need to decide which bedroom to allocate to each student, depending on the student’s preferences. Many solution concepts have been proposed for such settings, ranging from mechanisms without transfers, such as Random Priority and the Eating mechanism, to mechanisms with transfers, such as envy free solutions, the <PERSON><PERSON><PERSON><PERSON> value, and the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bargaining solution. We seek a solution concept that satisfies three natural properties, concerning efficiency, fairness and decomposition. We observe that every solution concept known (to us) fails to satisfy at least one of the three properties. We present a new solution concept, designed so as to satisfy the three properties. A certain submodularity condition (which holds in interesting special cases such as the Shared-Rental setting) implies both existence and uniqueness of our solution concept.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_6"}, {"primary_key": "3132761", "vector": [], "sparse_vector": [], "title": "From <PERSON> to <PERSON><PERSON><PERSON><PERSON> and <PERSON>: Recurrence and Cycles in Evolutionary and Algorithmic Game Theory.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Replicator dynamics, the continuous-time analogue of Multiplicative Weights Updates, is the main dynamic in evolutionary game theory. In simple evolutionary zero-sum games, such as Rock-Paper-Scissors, replicator dynamic is periodic [39], however, its behavior in higher dimensions is not well understood. We provide a complete characterization of its behavior in zero-sum evolutionary games. We prove that, if and only if, the system has an interior Nash equilibrium, the dynamics exhibit Poincaré recurrence, i.e., almost all orbits come arbitrary close to their initial conditions infinitely often. If no interior equilibria exist, then all interior initial conditions converge to the boundary. Specifically, the strategies that are not in the support of any equilibrium vanish in the limit of all orbits. All recurrence results furthermore extend to a class of games that generalize both graphical polymatrix games as well as evolutionary games, establishing a unifying link between evolutionary and algorithmic game theory. We show that two degrees of freedom, as in Rock-Paper-Scissors, is sufficient to prove periodicity.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_7"}, {"primary_key": "3132762", "vector": [], "sparse_vector": [], "title": "Hotelling Games with Random Tolerance Intervals.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The classical Hotelling game is played on a line segment whose points represent uniformly distributed clients. Thenplayers of the game are servers who need to place themselves on the line segment, and once this is done, each client gets served by the player closest to it. The goal of each player is to choose its location so as to maximize the number of clients it attracts. In this paper we study a variant of the Hotelling game where each clientvhas atolerance interval, randomly distributed according to some density functionf, andvgets served by the nearest among the playerseligiblefor it, namely, those that fall within its interval. (If no such player exists, thenvabstains.) It turns out that this modification significantly changes the behavior of the game and its states of equilibria. In particular, it may serve to explain why players sometimes prefer to “spread out,” rather than to cluster together as dictated by the classical Hotelling game. We consider two variants of the game:symmetricgames, where clients have the same tolerance range to their left and right, andasymmetricgames, where the left and right ranges of each client are determined independently of each other. We characterize the Nash equilibria of the 2-player game. For\\(n\\ge 3\\)players, we characterize a specific class of strategy profiles, referred to ascanonical profiles, and show that these profiles are the only ones that may yield Nash equilibria in our game. Moreover, the canonical profile, if exists, is uniquely defined for everynandf. In the symmetric setting, we give simple conditions for the canonical profile to be a Nash equilibrium, and demonstrate their application for several distributions. In the asymmetric setting, the conditions for equilibria are more complex; still, we derive a full characterization for the Nash equilibria of the exponential distribution. Finally, we show that for some distributions the simple conditions given for the symmetric setting are sufficient also for the asymmetric setting.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_9"}, {"primary_key": "3132764", "vector": [], "sparse_vector": [], "title": "Mix and Match: <PERSON>ov Chains and Mixing Times for Matching in Rideshare.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Rideshare platforms such as Uber and Lyft dynamically dispatch drivers to match riders’ requests. We model the dispatching process in rideshare as a Markov chain that takes into account the geographic mobility of both drivers and riders over time. Prior work explores dispatch policies in the limit of such Markov chains; we characterize when this limit assumption is valid, under a variety of natural dispatch policies. We give explicit bounds on convergence in general, and exact (including constants) convergence rates for special cases. Then, on simulated and real transit data, we show that our bounds characterize convergence rates—even when the necessary theoretical assumptions are relaxed. Additionally these policies compare well against a standard reinforcement learning algorithm which optimizes for profit without any convergence properties.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_10"}, {"primary_key": "3132765", "vector": [], "sparse_vector": [], "title": "Persuasion and Incentives Through the Lens of Duality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Lagrangian duality underlies both classical and modern mechanism design. In particular, the dual perspective often permits simple and detail-free characterizations of optimal and approximately optimal mechanisms. This paper applies this same methodology to a close cousin of traditional mechanism design, one which shares conceptual and technical elements with its more mature relative: the burgeoning field of persuasion. The dual perspective permits us to analyze optimal persuasion schemes both in settings which have been analyzed in prior work, as well as for natural generalizations which we are the first to explore in depth. Most notably, we permit combining persuasion policies with payments, which serve to augment the persuasion power of the scheme. In both single and multi-receiver settings, as well as under a variety of constraints on payments, we employ duality to obtain structural insights, as well as tractable and simple characterizations of optimal policies.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_11"}, {"primary_key": "3132766", "vector": [], "sparse_vector": [], "title": "Convergence and Hardness of Strategic Schelling Segregation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The phenomenon of residential segregation was captured by <PERSON><PERSON><PERSON>’s famous segregation model where two types of agents are placed on a grid and an agent is content with her location if the fraction of her neighbors which have the same type as her is at least\\(\\tau \\), for some\\(0<\\tau <1\\). Discontent agents simply swap their location with a randomly chosen other discontent agent or jump to a random empty cell. We analyze a generalized game-theoretic model of <PERSON><PERSON><PERSON> segregation which allows more than two agent types and more general underlying graphs modeling the residential area. For this we show that both aspects heavily influence the dynamic properties and the tractability of finding an optimal placement. We map the boundary of when improving response dynamics (IRD) are guaranteed to converge and we prove several sharp threshold results where guaranteed IRD convergence suddenly turns into a strong non-convergence result: a violation of weak acyclicity. In particular, we show threshold results also for <PERSON><PERSON><PERSON>’s original model, which is in contrast to the standard assumption in many empirical papers. In case of convergence we show that IRD find equilibria quickly.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_12"}, {"primary_key": "3132768", "vector": [], "sparse_vector": [], "title": "Automated Optimal OSP Mechanisms for Set Systems - The Case of Small Domains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Obviously strategyproof (OSP) mechanisms have recently come to the fore as a tool to deal with imperfect rationality. They, in fact, incentivize people with no contingent reasoning skills to “follow the protocol” and be honest. However, their exact power is still to be determined. For example, even for settings relatively well understood, such as binary allocation problems, it is not clear when optimal solutions can be computed with OSP mechanisms. We here consider this question for the large class of set system problems, where selfish agents with imperfect rationality own elements whose cost can take one among few values. In our main result, we give a characterization of the instances for which the optimum is possible. The mechanism we provide uses a combination of ascending and descending auctions, thus extending to a large class of settings a design paradigm for OSP mechanisms recently introduced in [9]. Finally, we dig deeper in the characterizing property and observe that the set of conditions can be quickly verified algorithmically. The combination of our mechanism and algorithmic characterization gives rise to the first example of automated mechanism design for OSP.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_13"}, {"primary_key": "3132769", "vector": [], "sparse_vector": [], "title": "The Pareto Frontier of Inefficiency in Mechanism Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the trade-off between the Price of Anarchy (PoA) and the Price of Stability (PoS) in mechanism design, in the prototypical problem of unrelated machine scheduling. We give bounds on the space of feasible mechanisms with respect to the above metrics, and observe that two fundamental mechanisms, namely the First-Price (FP) and the Second-Price (SP), lie on the two opposite extrema of this boundary. Furthermore, for the natural class of anonymous task-independent mechanisms, we completely characterize the PoA/PoS Pareto frontier; we design a class of optimal mechanisms\\(\\mathcal {SP}_\\alpha \\)that lieexactlyon this frontier. In particular, these mechanisms range smoothly, with respect to parameter\\(\\alpha \\ge 1\\)across the frontier, between the First-Price (\\(\\mathcal {SP}_1\\)) and Second-Price (\\(\\mathcal {SP}_\\infty \\)) mechanisms. En route to these results, we also provide a definitive answer to an important question related to the scheduling problem, namely whether non-truthful mechanisms can provide better makespan guarantees in the equilibrium, compared to truthful ones. We answer this question in the negative, by proving that the Price of Anarchy ofallscheduling mechanisms is at leastn, wherenis the number of machines.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_14"}, {"primary_key": "3132770", "vector": [], "sparse_vector": [], "title": "On the Price of Anarchy of Cost-Sharing in Real-Time Scheduling Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study cost-sharing games in real-time scheduling systems where the operation cost of the server at any given time is a function of its load. We focus on monomial cost functions and consider both the case when the degree is less than one (inducing positive externalities for the jobs) and when it is greater than one (inducing negative externalities for the jobs). For the former case, we provide tight price of anarchy bounds which show that the price of anarchy grows to infinity as a polynomial of the number of jobs in the game. For the latter, we observe that existing results provide constant and tight (asymptotically in the degree of the monomial) bounds on the price of anarchy. We then switch our attention to improving the price of anarchy by means of a simple coordination mechanism that has no knowledge of the instance. We show that our mechanism reduces the price of anarchy of games withnjobs and unit server costs from\\(\\varTheta (\\sqrt{n})\\)to 2. We also show that for a restricted class of instances a similar improvement is achieved for monomial server costs. This is not the case, however, for unrestricted instances of monomial costs for which we prove that the price of anarchy remains super-constant for our mechanism.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_15"}, {"primary_key": "3132774", "vector": [], "sparse_vector": [], "title": "The Classes PPA-k: Existence from Arguments Modulo k.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The complexity classes PPA-k,\\(k \\ge 2\\), have recently emerged as the main candidates for capturing the complexity of important problems in fair division, in particular Alon’sNecklace-Splittingproblem withkthieves. Indeed, the problem with two thieves has been shown complete for PPA\\(=\\)PPA-2. In this work, we present structural results which provide a solid foundation for the further study of these classes. Namely, we investigate the classes PPA-kin terms of (i) equivalent definitions, (ii) inner structure, (iii) relationship to each other and to other TFNP classes, and (iv) closure under Turing reductions.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_16"}, {"primary_key": "3132776", "vector": [], "sparse_vector": [], "title": "On the Approximability of Simple Mechanisms for MHR Distributions.", "authors": ["Yaonan Jin", "<PERSON><PERSON>", "<PERSON>"], "summary": "We focus on a canonical Bayesian mechanism design setting: a seller wants to sell a single item tonbidders, whose values are drawn i.i.d. from a monotone-hazard-rate distribution. In the literature, three mechanisms receive particular attention: the revenue-optimal mechanismMyerson Auction(OPT), the welfare-optimal mechanismSecond-Price Auction(SPA), and the most widely-used mechanismAnonymous Pricing(AP). In terms of revenue, we investigate how well the later two mechanisms can approximateMyerson Auction. OPTvs.AP: over all\\(n \\in \\mathbb {N}_{\\ge 1}\\), the supremum ratio is 1.27, and the worst-case distribution is exponential-like. This answers an open question of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (WINE 18), who proved an asymptotically tight bound of\\(1 + \\varTheta \\big (\\frac{\\log \\log n}{\\log n}\\big )\\)for large\\(n \\in \\mathbb {N}_{\\ge 1}\\). Thus, the approximability ofAPis well understood. OPTvs.SPA: for each\\(n \\ge 2\\), this ratio is upper-bounded by\\(\\big (1 - (1 - 1 / e)^{n - 1}\\big )^{-1} = 1 + 2^{-O(n)}\\); an asymptotically matching lower bound can be reached by a truncated exponential distribution. This result settles an open problem asked of Allouah and Besbes (EC 18), who attained the supremum ratio of 1.40 over all\\(n \\ge 2\\). Both bounds together supplement the seminal result of Bulow and Klemperer (Am. Econ. Rev. 96).", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_17"}, {"primary_key": "3132778", "vector": [], "sparse_vector": [], "title": "Topological Price of Anarchy Bounds for Clustering Games on Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider clustering games in which the players are embedded in a network and want to coordinate (or anti-coordinate) their choices with their neighbors. Recent studies show that even very basic variants of these games exhibit a large Price of Anarchy. Our main goal is to understand how structural properties of the network topology impact the inefficiency of these games. We derivetopological boundson the Price of Anarchy for different classes of clustering games. These topological bounds provide a more informative assessment of the inefficiency of these games than the corresponding (worst-case) Price of Anarchy bounds. As one of our main results, we derive (tight) bounds on the Price of Anarchy for clustering games on Erdős-Rényi random graphs, which, depending on the graph density, stand in stark contrast to the known Price of Anarchy bounds.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_18"}, {"primary_key": "3132779", "vector": [], "sparse_vector": [], "title": "Outsourcing Computation: The Minimal Refereed Mechanism.", "authors": ["Yuqing Kong", "<PERSON>", "<PERSON>", "Biaosh<PERSON><PERSON>"], "summary": "We consider a setting where a verifier with limited computation power delegates a resource intensive computation task—which requires a\\(T\\times S\\)computation tableau—to two provers where the provers are rational in that each prover maximizes their own payoff—taking into account losses incurred by the cost of computation. We design a mechanism called the Minimal Refereed Mechanism (MRM) such that if the verifier has\\(O(\\log S + \\log T)\\)time and\\(O(\\log S + \\log T)\\)space computation power, then both provers will provide a honest result without the verifier putting any effort to verify the results. The amount of computation required for the provers (and thus the cost) is a multiplicative\\(\\log \\)S-factor more than the computation itself, making this schema efficient especially for low-space computations.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_19"}, {"primary_key": "3132780", "vector": [], "sparse_vector": [], "title": "On Core-Selecting and Core-Competitive Mechanisms for Binary Single-Parameter Auctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Our work concerns the class of core-selecting mechanisms, as introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [3]. Such mechanisms have been known to possess good revenue guarantees and some of their variants have been used in practice especially for spectrum and other public sector auctions. Despite their popularity, it has also been demonstrated that these auctions are generally non-truthful. As a result, current research has focused either on identifying core-selecting mechanisms with minimal incentives to deviate from truth-telling, such as the family of Minimum-Revenue Core-Selecting (MRCS) rules, or on proposing truthful mechanisms whose revenue is competitive against core outcomes. Our results contribute to both of these directions. We start with studying the core polytope in more depth and provide new properties and insights, related to the effects of unilateral deviations from a given profile. We then utilize these properties in two ways. First, we propose a truthful mechanism that is\\(O(\\log {n})\\)-competitive against the MRCS benchmark. Our result is the first deterministic core-competitive mechanism for binary single-parameter domains. Second, we study the existence ofnon-decreasingpayment rules, meaning that the payment of each bidder is a non-decreasing function of her bid. This property has been advocated by the core-related literature but it has remained an open question if there exist MRCS non-decreasing mechanisms. We answer the question in the affirmative, by describing a subclass of rules with this property.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_20"}, {"primary_key": "3132783", "vector": [], "sparse_vector": [], "title": "Optimal Search Segmentation Mechanisms for Online Platform Markets.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online platforms, such as Airbnb,hotels.com, Amazon, Uber and Lyft, can control and optimize many aspects of product search to improve the efficiency of marketplaces. Here we focus on a common model, called the discriminatory control model, where the platform chooses to display a subset of sellers who sell products at prices determined by the market and a buyer is interested in buying a single product from one of the sellers. Under the commonly-used model for single product selection by a buyer, called the multinomial logit model, and the Bertrand game model for competition among sellers, we show the following result: to maximize social welfare, the optimal strategy for the platform is to display all products; however, to maximize revenue, the optimal strategy is to only display a subset of the products whose qualities are above a certain threshold. This threshold depends on the quality of all products, and can be computed in linear time in the number of products.", "published": "2019-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-35389-6_22"}]