[{"primary_key": "1264580", "vector": [], "sparse_vector": [], "title": "InteractionAdapt: Interaction-driven Workspace Adaptation for Situated Virtual Reality Environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) has the potential to transform how we work: it enables flexible and personalized workspaces beyond what is possible in the physical world. However, while most VR applications are designed to operate in a single empty physical space, work environments are often populated with real-world objects and increasingly diverse due to the growing amount of work in mobile scenarios. In this paper, we present InteractionAdapt, an optimization-based method for adapting VR workspaces for situated use in varying everyday physical environments, allowing VR users to transition between real-world settings while retaining most of their personalized VR environment for efficient interaction to ensure temporal consistency and visibility. InteractionAdapt leverages physical affordances in the real world to optimize UI elements for the respectively most suitable input technique, including on-surface touch, mid-air touch and pinch, and cursor control. Our optimization term thereby models the trade-off across these interaction techniques based on experimental findings of 3D interaction in situated physical environments. Our two evaluations of InteractionAdapt in a selection task and a travel planning task established its capability of supporting efficient interaction, during which it produced adapted layouts that participants preferred to several baselines. We further showcase the versatility of our approach through applications that cover a wide range of use cases.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606717"}, {"primary_key": "1264581", "vector": [], "sparse_vector": [], "title": "WavoID: Robust and Secure Multi-modal User Identification via mmWave-voice Mechanism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing deployment of voice-controlled devices in homes and enterprises, there is an urgent demand for voice identification to prevent unauthorized access to sensitive information and property loss. However, due to the broadcast nature of sound wave, a voice-only system is vulnerable to adverse conditions and malicious attacks. We observe that the cooperation of millimeter waves (mmWave) and voice signals can significantly improve the effectiveness and security of user identification. Based on the properties, we propose a multi-modal user identification system (named WavoID) by fusing the uniqueness of mmWave-sensed vocal vibration and mic-recorded voice of users. To estimate fine-grained waveforms, WavoID splits signals and adaptively combines useful decomposed signals according to correlative contents in both mmWave and voice. An elaborated anti-spoofing module in WavoID comprising biometric bimodal information defend against attacks. WavoID produces and fuses the response maps of mmWave and voice to improve the representation power of fused features, benefiting accurate identification, even facing adverse circumstances. We evaluate WavoID using commercial sensors on extensive experiments. WavoID has significant performance on user identification with over 98% accuracy on 100 user datasets.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606775"}, {"primary_key": "1264582", "vector": [], "sparse_vector": [], "title": "Papeos: Augmenting Research Papers with Talk Videos.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Research consumption has been traditionally limited to the reading of academic papers-a static, dense, and formally written format. Alternatively, pre-recorded conference presentation videos, which are more dynamic, concise, and colloquial, have recently become more widely available but potentially under-utilized. In this work, we explore the design space and benefits for combining academic papers and talk videos to leverage their complementary nature to provide a rich and fluid research consumption experience. Based on formative and co-design studies, we present Papeos, a novel reading and authoring interface that allow authors to augment their papers by segmenting and localizing talk videos alongside relevant paper passages with automatically generated suggestions. With Papeos, readers can visually skim a paper through clip thumbnails, and fluidly switch between consuming dense text in the paper or visual summaries in the video. In a comparative lab study (n=16), <PERSON><PERSON><PERSON> reduced mental load, scaffolded navigation, and facilitated more comprehensive reading of papers.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606770"}, {"primary_key": "1264583", "vector": [], "sparse_vector": [], "title": "Cells, Generators, and Lenses: Design Framework for Object-Oriented Interaction with Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have become the backbone of numerous writing interfaces with the goal of supporting end-users across diverse writing tasks. While LLMs reduce the effort of manual writing, end-users may need to experiment and iterate with various generation configurations (e.g., inputs and model parameters) until results meet their goals. However, these interfaces are not designed for experimentation and iteration, and can restrict how end-users track, compare, and combine configurations. In this work, we present \"cells, generators, and lenses\", a framework to designing interfaces that support interactive objects that embody configuration components (i.e., input, model, output). Interface designers can apply our framework to produce interfaces that enable end-users to create variations of these objects, combine and recombine them into new configurations, and compare them in parallel to efficiently iterate and experiment with LLMs. To showcase how our framework generalizes to diverse writing tasks, we redesigned three different interfaces—story writing, copywriting, and email composing—and, to demonstrate its effectiveness in supporting end-users, we conducted a comparative study (N=18) where participants used our interactive objects to generate and experiment more. Finally, we investigate the usability of the framework through a workshop with designers (N=3) where we observed that our framework served as both bootstrapping and inspiration in the design process.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606833"}, {"primary_key": "1264584", "vector": [], "sparse_vector": [], "title": "InkBrush: A Flexible and Controllable Authoring Tool for 3D Ink Painting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a stylized representation of traditional painting art, ink painting has been widely used in most of Asian countries. Art creations with ink painting strokes in 3D space have shown potential in animation and games. We propose a sketch-based authoring tool for drawing ink painting style strokes in 3D space. With automatic strip-based modeling and procedural texture generation, users can easily create ink painting style artworks by drawing sketches. Our system generates ink effects in real time and provides a simple non-linear workflow. User evaluations show that novice users can easily use the system and quickly create 3D ink paintings.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615782"}, {"primary_key": "1264585", "vector": [], "sparse_vector": [], "title": "Towards Real-time Computer Vision and Augmented Reality to Support Low Vision Sports: A Demonstration of ARTennis.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Individuals with low vision (LV) can experience vision-related challenges when participating in sports, especially those with fast-moving objects. We introduce ARTennis, a prototype for wearable augmented reality (AR) that utilizes real-time computer vision (CV) to enhance the visual saliency of tennis balls. Preliminary findings indicate that while ARTennis is helpful, combining both visual and auditory cues may be more effective. As AR and CV technologies continue to improve, we expect head-worn AR to broaden the inclusivity of sports.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615815"}, {"primary_key": "1264586", "vector": [], "sparse_vector": [], "title": "PaperToPlace: Transforming Instruction Documents into Spatialized and Context-Aware Mixed Reality Experiences.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While paper instructions are one of the mainstream medium for sharing knowledge, consuming such instructions and translating them into activities are inefficient due to the lack of connectivity with physical environment. We present PaperToPlace, a novel workflow comprising an authoring pipeline, which allows the authors to rapidly transform and spatialize existing paper instructions into MR experience, and a consumption pipeline, which computationally place each instruction step at an optimal location that is easy to read and do not occlude key interaction areas. Our evaluations of the authoring pipeline with 12 participants demonstrated the usability of our workflow and the effectiveness of using a machine learning based approach to help extracting the spatial locations associated with each steps. A second within-subject study with another 12 participants demonstrates the merits of our consumption pipeline by reducing efforts of context switching, delivering the segmented instruction steps and offering the hands-free affordances.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606832"}, {"primary_key": "1264587", "vector": [], "sparse_vector": [], "title": "Computational Design of Personalized Wearable Robotic Limbs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearable robotic limbs (WRLs) augment human capabilities through robotic structures that attach to the user's body. While WRLs are intensely researched and various device designs have been presented, it remains difficult for non-roboticists to engage with this exciting field. We aim to empower interaction designers and application domain experts to explore novel designs and applications by rapidly prototyping personalized WRLs that are customized for different tasks, different body locations, or different users. In this paper, we present WRLKit, an interactive computational design approach that enables designers to rapidly prototype a personalized WRL without requiring extensive robotics and ergonomics expertise. The body-aware optimization approach starts by capturing the user's body dimensions and dynamic body poses. Then, an optimized fabricable structure of the WRL is generated for a desired mounting location and workspace of the WRL, to fit the user's body and intended task. The results of a user study and several implemented prototypes demonstrate the practical feasibility and versatility of WRLKit.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606748"}, {"primary_key": "1264588", "vector": [], "sparse_vector": [], "title": "Introducing Augmented Post-it: An AR Prototype for Engaging Body Movements in Online GPT-Supported Brainstorming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent web-based brainstorming tools utilize pre-trained language models to support ideation. However, body movements such as walking and arm motion, which are known to enhance creativity, are generally limited in web-based brainstorming. Thus, we propose \"Augmented Post-it,\" a novel augmented reality (AR) interaction system that enables body movements while brainstorming using a generative pre-trained transformer (GPT). With Augmented Post-it, ideas uttered by users are structured via GPT and extended into a spatial representation that encourages divergent thinking. This study contributes to future GPT-based AR brainstorming by extending the thinking ability of users in ways that engage their body movements to enhance creativity.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616693"}, {"primary_key": "1264589", "vector": [], "sparse_vector": [], "title": "Hypothesizer: A Hypothesis-Based Debugger to Find and Test Debugging Hypotheses.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When software defects occur, developers begin the debugging process by formulating hypotheses to explain the cause. These hypotheses guide the investigation process, determining which evidence developers gather to accept or reject the hypothesis, such as parts of the code and program state developers examine. However, existing debugging techniques do not offer support in finding relevant hypotheses, leading to wasted time testing hypotheses and examining code that ultimately does not lead to a fix. To address this issue, we introduce a new type of debugging tool, the hypothesis-based debugger, and an implementation of this tool in Hypothesizer. Hypothesis-based debuggers support developers from the beginning of the debugging process by finding relevant hypotheses until the defect is fixed. To debug using Hypothesizer, developers first demonstrate the defect, generating a recording of the program behavior with code execution, user interface events, network communications, and user interface changes. Based on this information and the developer's descriptions of the symptoms, Hypothesizer finds relevant hypotheses, analyzes the code to identify relevant evidence to test the hypothesis, and generates an investigation plan through a timeline view. This summarizes all evidence items related to the hypothesis, indicates whether the hypothesis is likely to be true by showing which evidence items were confirmed in the recording, and enables the developer to quickly check evidence in the recording by viewing code snippets for each evidence item. A randomized controlled experiment with 16 professional developers found that, compared to traditional debugging tools and techniques such as breakpoint debuggers and Stack Overflow, Hypothesizer dramatically improved the success rate of fixing defects by a factor of five and decreased the time to debug by a factor of three.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606781"}, {"primary_key": "1264590", "vector": [], "sparse_vector": [], "title": "FlavourFrame: Visualizing Tasting Experiences.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present FlavourFrame, a canvas-based app that helps tasters capture and visualize their perceptions during mindful tasting experiences. Taste perceptions are difficult to document because they are subjective, multisensory, and ephemeral; and everyday people have limited dedicated vocabulary to describe such experiences. Our customizable tool is designed to help novice and experienced tasters structure and record tasting experiences. FlavourFrame superimposes visual and text layers to personalize visual and word-based expression of flavor experience. Through autoethnographic reflections, we generated sample data and identified strengths and limitations of the prototype.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616640"}, {"primary_key": "1264591", "vector": [], "sparse_vector": [], "title": "Spellburst: A Node-based Interface for Exploratory Creative Coding with Natural Language Prompts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Creative coding tasks are often exploratory in nature. When producing digital artwork, artists usually begin with a high-level semantic construct such as a \"stained glass filter\" and programmatically implement it by varying code parameters such as shape, color, lines, and opacity to produce visually appealing results. Based on interviews with artists, it can be effortful to translate semantic constructs to program syntax, and current programming tools don't lend well to rapid creative exploration. To address these challenges, we introduce Spellburst, a large language model (LLM) powered creative-coding environment. Spellburst provides (1) a node-based interface that allows artists to create generative art and explore variations through branching and merging operations, (2) expressive prompt-based interactions to engage in semantic programming, and (3) dynamic prompt-driven interfaces and direct code editing to seamlessly switch between semantic and syntactic exploration. Our evaluation with artists demonstrates Spellburst's potential to enhance creative coding practices and inform the design of computational creativity tools that bridge semantic and syntactic spaces.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606719"}, {"primary_key": "1264592", "vector": [], "sparse_vector": [], "title": "ChainForge: An open-source visual programming environment for prompt engineering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prompt engineering for large language models (LLMs) is a critical to effectively leverage their capabilities. However, due to the inherent stochastic and opaque nature of LLMs, prompt engineering is far from an exact science. Crafting prompts that elicit the desired responses still requires a lot of trial and error to gain a nuanced understanding of a model's strengths and limitations for one's specific task context and target application. To support users in sensemaking around the outputs of LLMs, we create ChainForge, an open-source visual programming environment for prompt engineering. ChainForge is publicly available, both on the web (https://chainforge.ai) and as a locally installable Python package hosted on PyPI. We detail some features of ChainForge and how we iterated the design in response to internal and external feedback.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616660"}, {"primary_key": "1264593", "vector": [], "sparse_vector": [], "title": "Bringing Context-Aware Completion Suggestions to Arbitrary Text Entry Interfaces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Large language models (LLMs) can predict \"obvious\" next steps that users will take in text entry fields, especially the tedious components of tasks like software engineering or email composition. These models are not only useful in large, unbroken text fields, however. We present OmniFill, a browser extension that detects text entry fields and offers \"autofill\"-style suggestions based on context from the browsing session. The system constructs an LLM prompt that includes three main components: (a) a description of the active tab's text fields and their current values, (b) information from the user's recent web browsing context, and (c) a history, if available, of the user's prior submissions to the web form (alongside those submissions' associated browsing context). Suggestions from the LLM's response are offered to the user to be automatically typed into each corresponding text field. We offer a motivating example of a time-saving interaction and discuss the broader utility of interface-agnostic LLM integrations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615825"}, {"primary_key": "1264594", "vector": [], "sparse_vector": [], "title": "Color Field: Developing Professional Vision by Visualizing the Effects of Color Filters.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Color filters are ubiquitous across visual digital media due to their transformative effect. However, it can be difficult to understand how a color filter will affect an image, especially for novices. In order to become experts, we argue that novices need to develop <PERSON>'s notion of Professional Vision [29]. Then, they can \"see\" and interpret their work in terms of their domain knowledge like experts. Using the theory of Professional Vision, we present two design objectives for systems that aim to help users develop expertise. These goals were used to develop Color Field, an interactive visualization of color filters as a vector field over the Hue-Saturation-Lightness color space. We conducted an exploratory user study in which five color grading novices and four experts were asked to analyze color filters. We found that Color Field enabled multiple strategies to make sense of filters (e.g. reviewing the overall shape of the vector field) and discuss them (e.g. using spatial language). We conclude with other applications of Color Field and future work to leverages Professional Vision in HCI.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606828"}, {"primary_key": "1264595", "vector": [], "sparse_vector": [], "title": "Architecting Novel Interactions with Generative AI Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The new generation of generative AI models offers interactive opportunities that may fulfill long-standing aspirations in human-computer interaction and open doors to new forms of interaction that we have yet to imagine. The UIST community has a unique vantage point that can lead to critical contributions in envisioning a future of interactive computing that appropriately leverages the power of these new generative AI models. However, we are only just beginning to understand the research area that exists at the intersection of interaction and generative AI. By bringing together members of the UIST community interested in this intersection, we seek to initiate discussions on the potential of generative AI in architecting new forms of interactions. Key topics of interest include the exploration of novel categories of interactions made possible by generative AI, the development of methods for enabling more powerful and direct user control of generative AI, and the identification of model and architecture requirements for generative AI in interaction literature. The workshop will foster community building and produce concrete deliverables, including a research agenda, model/architecture requirements, and a simulated debate generated by a generative agent architecture.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3617431"}, {"primary_key": "1264596", "vector": [], "sparse_vector": [], "title": "Integrating a LLM into an Automatic Dance Practice Support System: Breathing Life Into The Virtual Coach.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We propose using a LLM to breathe life into our existing dance learning app in the form of an AI dance coach persona. The current app guides the user through dance practice plans, using the webcam and pose estimation to give feedback. Using the LLM, voice recognition, speech synthesis, and affect recognition, we plan to transform the interface from a mechanical click-on-screen experience to a hands-free speak-with-the-coach interaction. In particular, we'll use the LLM to announce coaching guidance, provide encouragement, communicate feedback, and interpret the user's spoken intent.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625119"}, {"primary_key": "1264597", "vector": [], "sparse_vector": [], "title": "LearnThatDance: Augmenting TikTok Dance Challenge Videos with an Interactive Practice Support System Powered by Automatically Generated Lesson Plans.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demo we showcase an interactive application to support the learning of \"TikTok dance challenge\" short dance choreographies. Our system utilizes dance challenge videos as the information source, performing music analysis and pose estimation to segment the dance into learnable chunks and generate a practice plan that implements motor learning techniques such as incremental part-learning and fading guidance. These plans are presented in a web app that implements video demonstration, augmented webcam mirroring, practice recording/review functionality, and both concurrent and terminal feedback. By operating on a ubiquitous information source, generating the lessons automatically, and requiring only a web browser and webcam in the user interface, our system is a step towards significantly expanding the reach of dance choreography learning and a platform for further research into dance HCI.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615801"}, {"primary_key": "1264598", "vector": [], "sparse_vector": [], "title": "Developing Action-Oriented Systems for Manual-Computational Craft Workflows.", "authors": ["<PERSON><PERSON>"], "summary": "Manual crafts typically involve action-oriented workflows, wherein the craftsperson performs a series of repeated actions to create beautiful intricate artifacts. Despite working with similar materials, digital fabrication workflows often implement methods that differ fundamentally from manual ones. In my dissertation work, I aim to develop action-oriented digital fabrication systems that implement abstractions derived from craft domain-expert knowledge. I theorize that action-oriented systems have the potential to (1) support the integration of computational tools with manual practices, (2) enable complex design tasks that would be challenging to achieve through other means, and (3) leverage the unique properties of materials through fine control over machine toolpath. To investigate this theory, I developed three digital fabrication systems, which I evaluated by producing various artifacts. Additionally, I propose analyzing my work and HCI system research in general through the lenses of three theoretical frameworks that reflect on materiality, i.e. new materialism, indigenous epistemologies, and post-colonial theory on skill mastery. I believe this analysis can offer alternative perspectives that promote a more inclusive understanding of the relationship between humans, materials, and technology in the context of digital fabrication.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616708"}, {"primary_key": "1264599", "vector": [], "sparse_vector": [], "title": "Promptify: Text-to-Image Generation through Interactive Prompt Exploration with Large Language Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Text-to-image generative models have demonstrated remarkable capabilities in generating high-quality images based on textual prompts. However, crafting prompts that accurately capture the user's creative intent remains challenging. It often involves laborious trial-and-error procedures to ensure that the model interprets the prompts in alignment with the user's intention. To address these challenges, we present Promptify, an interactive system that supports prompt exploration and refinement for text-to-image generative models. Promptify utilizes a suggestion engine powered by large language models to help users quickly explore and craft diverse prompts. Our interface allows users to organize the generated images flexibly, and based on their preferences, Promptify suggests potential changes to the original prompt. This feedback loop enables users to iteratively refine their prompts and enhance desired features while avoiding unwanted ones. Our user study shows that Promptify effectively facilitates the text-to-image workflow, allowing users to create visually appealing images on their first attempt while requiring significantly less cognitive load than a widely-used baseline tool.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606725"}, {"primary_key": "1264600", "vector": [], "sparse_vector": [], "title": "Chemical interfaces: new methods for interfacing with the human senses.", "authors": ["<PERSON><PERSON>"], "summary": "Since the advent of computer interfaces, significant strides have been made toward providing high-fidelity visual, auditory, and haptic experiences. However, stimulating the senses of smell, taste, and temperature has sorely stagnated over the last century, primarily due to the field's dependence on adapted methods from mechanical and robotic engineering. I posit that novel interfacing techniques are necessary to engage the human senses fully. My research explores the integration of these underutilized yet crucial senses by investigating a new class of devices termed \"chemical interfaces.\" These devices manipulate the human senses by interfacing with our perceptions' biochemical cascades through carefully selected chemicals. Unlike conventional approaches originating from robotics or mechanical engineering, chemical interfaces provide distinctive affordances. My research illustrates several such affordances, including reduced power consumption for thermal feedback, miniaturized versatile mechanisms for haptics, and new interactions for taste via chemical selectivity. My approach lays a promising foundation for incorporating these rich senses into our digital interactions and, maybe in the future, influencing how we engage with our food and the air we breathe in our everyday life.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616711"}, {"primary_key": "1264601", "vector": [], "sparse_vector": [], "title": "Taste Retargeting via Chemical Taste Modulators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Prior research has explored modifying taste through electrical stimulation. While promising, such interfaces often only elicit taste changes while in contact with the user's tongue (e.g., cutlery with electrodes), making them incompatible with eating and swallowing real foods. Moreover, most interfaces cannot selectively alter basic tastes, but only the entire flavor profile (e.g., cannot selectively alter bitterness). To tackle this, we propose taste retargeting, a method of altering taste perception by delivering chemical modulators to the mouth before eating. These modulators temporarily change the response of taste receptors to foods, selectively suppressing or altering basic tastes. Our first study identified six accessible taste modulators that suppress salty, umami, sweet, or bitter and transform sour into sweet. Using these findings, we demonstrated an interactive application of this technique with the example of virtual reality, which we validated in our second study. We found that taste retargeting reduced the flavor mismatch between a food prop and other virtual foods.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606818"}, {"primary_key": "1264602", "vector": [], "sparse_vector": [], "title": "Starrypia: An AR Gamified Music Adjuvant Treatment Application for Children with Autism Based on Combined Therapy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present Starrypia, a lightweight gamified music adjuvant treatment application to improve the symptoms of mild autistic children, eliminating the geographical and time constraints faced by traditional treatment. Adopting ABA (Applied Behavior Analysis) behavioral theory as the principle, Starrypia follows the stimulus-response-reinforcement-pause process and incorporates music therapy and sensory integration. Based on AR, Starrypia provides multi-sensory intervention through music generated by BiLSTM deep model, 3D visual scenes, touch interaction to keep children focused and calm. We conducted a controlled experiment on 20 children to test <PERSON><PERSON><PERSON>'s effectiveness and attraction. Children's pre-test and post-test scores on two autism rating scales and performance during the test were applied to measure their abilities and engagement. Experimental results indicated that children showed great interest in <PERSON><PERSON><PERSON> and presented evident symptom remission and advance in overall abilities after 4 weeks of use. In conclusion, Starrypia is practicable in both therapeutic effect and user experience, and conspicuously instrumental in promoting sensory ability.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606755"}, {"primary_key": "1264603", "vector": [], "sparse_vector": [], "title": "Chandelier: Interaction Design With Surrounding Mid-Air Tangible Interface.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Hung", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Chandelier is a mid-air tangible interface where a user is surrounded in the center by 120 pendants that levitate independently and orbit in 5 concentric circumferences, where each pendant is touch-enabled and color-changeable by default. We explore interactions with <PERSON><PERSON><PERSON> such as change blindness and repurposing formations from immersive experiences to mitigate the limitation of the hardware systems. We discuss the extent of Surrounding Mid-Air interactions in tangible interfaces and the design factors that could be brought into experiences of future levitation interfaces.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616695"}, {"primary_key": "1264604", "vector": [], "sparse_vector": [], "title": "SoundBlender: Manipulating Sounds for Accessible Mixed-Reality Awareness.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sounds are everywhere, from real-world content to virtual audio presented by hearing devices, which create a mixed-reality soundscape that entails rich but intricate information. However, sounds often overlap and conflict in priorities, which makes them hard to perceive and differentiate. This is exacerbated in mixed-reality settings, where real-world and virtual sounds can conflict with each other. This may exacerbate the awareness of mixed reality for blind people who heavily rely on audio information in their everyday life. To address this, we present a sound rendering framework SoundBlender, consisting of six sound manipulators for users to better organize and manipulate real and virtual sounds across time and space: Ambience Builder, Feature Shifter, Earcon Generator, Prioritizer, Spatializer, and Stylizer. We demonstrate how the sound manipulators can increase mixed-reality awareness through a simulated working environment, and a meeting application.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615787"}, {"primary_key": "1264605", "vector": [], "sparse_vector": [], "title": "Robust Finger Interactions with COTS Smartwatches via Unsupervised Siamese Adaptation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable devices like smartwatches and smart wristbands have gained substantial popularity in recent years. However, their small interfaces create inconvenience and limit computing functionality. To fill this gap, we propose ViWatch, which enables robust finger interactions under deployment variations, and relies on a single IMU sensor that is ubiquitous in COTS smartwatches. To this end, we design an unsupervised Siamese adversarial learning method. We built a real-time system on commodity smartwatches and tested it with over one hundred volunteers. Results show that the system accuracy is about 97% over a week. In addition, it is resistant to deployment variations such as different hand shapes, finger activity strengths, and smartwatch positions on the wrist. We also developed a number of mobile applications using our interactive system and conducted a user study where all participants preferred our un-supervised approach to supervised calibration. The demonstration of ViWatch is shown at https://youtu.be/N5-ggvy2qfI.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606794"}, {"primary_key": "1264606", "vector": [], "sparse_vector": [], "title": "MIWA: Mixed-Initiative Web Automation for Better User Control and Confidence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the era of Big Data, web automation is frequently used by data scientists, domain experts, and programmers to complete time-consuming data collection tasks. However, developing web automation scripts requires familiarity with a programming language and HTML, which remains a key learning barrier for non-expert users. We provide MIWA, a mixed-initiative web automation system that enables users to create web automation scripts by demonstrating what content they want from the targeted websites. Compared to existing web automation tools, MIWA helps users better understand a generated script and build trust in it by (1) providing a step-by-step explanation of the script's behavior with visual correspondence to the target website, (2) supporting greater autonomy and control over web automation via step-through debugging and fine-grained demonstration refinement, and (3) automatically detecting potential corner cases that are handled improperly by the generated script. We conducted a within-subjects user study with 24 participants and compared MIWA with Rousillon, a state-of-the-art web automation tool. Results showed that, compared to Rousillon, MIWA reduced the task completion time by half while helping participants gain more confidence in the generated script.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606720"}, {"primary_key": "1264607", "vector": [], "sparse_vector": [], "title": "Unveiling the Tricks: Automated Detection of Dark Patterns in Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON>", "Ji<PERSON>u Sun", "<PERSON><PERSON> Feng", "<PERSON><PERSON><PERSON>", "Qinghua Lu", "<PERSON><PERSON>", "Chunyang Chen"], "summary": "Mobile apps bring us many conveniences, such as online shopping and communication, but some use malicious designs called dark patterns to trick users into doing things that are not in their best interest. Many works have been done to summarize the taxonomy of these patterns and some have tried to mitigate the problems through various techniques. However, these techniques are either time-consuming, not generalisable or limited to specific patterns. To address these issues, we propose UIGuard, a knowledge-driven system that utilizes computer vision and natural language pattern matching to automatically detect a wide range of dark patterns in mobile UIs. Our system relieves the need for manually creating rules for each new UI/app and covers more types with superior performance. In detail, we integrated existing taxonomies into a consistent one, conducted a characteristic analysis and distilled knowledge from real-world examples and the taxonomy. Our UIGuard consists of two components, Property Extraction and Knowledge-Driven Dark Pattern Checker. We collected the first dark pattern dataset, which contains 4,999 benign UIs and 1,353 malicious UIs of 1,660 instances spanning 1,023 mobile apps. Our system achieves a superior performance in detecting dark patterns (micro averages: 0.82 in precision, 0.77 in recall, 0.79 in F1 score). A user study involving 58 participants further showed that UIGuard significantly increases users' knowledge of dark patterns. We demonstrated potential use cases of our work, which can benefit different stakeholders, and serve as a training tool for raising awareness of dark patterns.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606783"}, {"primary_key": "1264608", "vector": [], "sparse_vector": [], "title": "AirCharge: Amplifying Ungrounded Impact Force by Accumulating Air Propulsion Momentum.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Impact events, which generate directional forces with extremely short impulse durations and large force magnitudes, are prevalent in both virtual reality (VR) games and real-world experiences. However, despite recent advancement in ungrounded force feedback technologies, such as air jet propulsion and propellers, these technologies remain 5-100x weaker and 10-500x slower compared to real-world impact events. For instance, they can only achieve 4N with a minimal duration of 50-500ms compared to the 20-400N forces generated within 1-5ms for baseball, ping-pong, drumming, and tennis. To overcome these limitations, we present AirCharge, a novel haptic device that accumulates air propulsion momentum to generate instantaneous, directional impact forces. By mounting compressed air jets on rotating swingarms, AirCharge can amplify impact force magnitude by more than 10x while matching real-world impulse duration of 3ms. To support high-frequency impacts, we explored and evaluated a series of device designs, culminating in a novel reciprocating dual-swingarm design that leverages a reversing bevel gearbox to eliminate gyro effects and to achieve impact feedback of up to 10Hz. User experience evaluation (n = 16) showed that AirCharge significantly enhanced realism and is preferred by participants compared to air jets without the charging mechanism.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606768"}, {"primary_key": "1264609", "vector": [], "sparse_vector": [], "title": "From Gap to Synergy: Enhancing Contextual Understanding through Human-Machine Collaboration in Personalized Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weinan Shi", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents LangAware, a collaborative approach for constructing personalized context for context-aware applications. The need for personalization arises due to significant variations in context between individuals based on scenarios, devices, and preferences. However, there is often a notable gap between humans and machines in the understanding of how contexts are constructed, as observed in trigger-action programming studies such as IFTTT. LangAware enables end-users to participate in establishing contextual rules in-situ using natural language. The system leverages large language models (LLMs) to semantically connect low-level sensor detectors to high-level contexts and provide understandable natural language feedback for effective user involvement. We conducted a user study with 16 participants in real-life settings, which revealed an average success rate of 87.50% for defining contextual rules in a variety of 12 campus scenarios, typically accomplished within just two modifications. Furthermore, users reported a better understanding of the machine's capabilities by interacting with LangAware.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606741"}, {"primary_key": "1264610", "vector": [], "sparse_vector": [], "title": "TaleStream: Supporting Story Ideation with Trope Knowledge.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Story ideation is a critical part of the story-writing process. It is challenging to support computationally due to its exploratory and subjective nature. Tropes, which are recurring narrative elements across stories, are essential in stories as they shape the structure of narratives and our understanding of them. In this paper, we propose to use tropes as an intermediate representation of stories to approach story ideation. We present TaleStream, a canvas system that uses tropes as building blocks of stories while providing steerable suggestions of story ideas in the form of tropes. Our trope suggestion methods leverage data from the tvtropes.org wiki. We find that 97% of the time, trope suggestions generated by our methods provide better story ideation materials than random tropes. Our system evaluation suggests that TaleStream can support writers' creative flow and greatly facilitates story development. Tropes, as a rich lexicon of narratives with available examples, play a key role in TaleStream and hold promise for story-creation support systems.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606807"}, {"primary_key": "1264611", "vector": [], "sparse_vector": [], "title": "TouchType-GAN: Modeling Touch Typing with Generative Adversarial Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Models that can generate touch typing tasks are important to the development of touch typing keyboards. We propose TouchType-GAN, a Conditional Generative Adversarial Network that can simulate locations and time stamps of touch points in touch typing. TouchType-GAN takes arbitrary text as input to generate realistic touch typing both spatially (i.e., (x, y) coordinates of touch points) and temporally (i.e., timestamps of touch points). TouchType-GAN introduces a variational generator that estimates Gaussian Distributions for every target letter to prevent mode collapse. Our experiments on a dataset with 3k typed sentences show that TouchType-GAN outperforms existing touch typing models, including the Rotational Dual Gaussian model [36] for simulating the distribution of touch points, and the Finger-Fitts Euclidean Model [30] for simulating typing time. Overall, our research demonstrates that the proposed GAN structure can learn the distribution of user typed touch points, and the resulting TouchType-GAN can also estimate typing movements. TouchType-GAN can serve as a valuable tool for designing and evaluating touch typing input systems.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606760"}, {"primary_key": "1264612", "vector": [], "sparse_vector": [], "title": "Augmented Math: Authoring AR-Based Explorable Explanations by Augmenting Static Math Textbooks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Augmented Math, a machine learning-based approach to authoring AR explorable explanations by augmenting static math textbooks without programming. To augment a static document, our system first extracts mathematical formulas and figures from a given document using optical character recognition (OCR) and computer vision. By binding and manipulating these extracted contents, the user can see the interactive animation overlaid onto the document through mobile AR interfaces. This empowers non-technical users, such as teachers or students, to transform existing math textbooks and handouts into on-demand and personalized explorable explanations. To design our system, we first analyzed existing explorable math explanations to identify common design strategies. Based on the findings, we developed a set of augmentation techniques that can be automatically generated based on the extracted content, which are 1) dynamic values, 2) interactive figures, 3) relationship highlights, 4) concrete examples, and 5) step-by-step hints. To evaluate our system, we conduct two user studies: preliminary user testing and expert interviews. The study results confirm that our system allows more engaging experiences for learning math concepts.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606827"}, {"primary_key": "1264613", "vector": [], "sparse_vector": [], "title": "PromptPaint: Steering Text-to-Image Generation Through Paint Medium-like Interactions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While diffusion-based text-to-image (T2I) models provide a simple and powerful way to generate images, guiding this generation remains a challenge. For concepts that are difficult to describe through language, users may struggle to create prompts. Moreover, many of these models are built as end-to-end systems, lacking support for iterative shaping of the image. In response, we introduce PromptPaint, which combines T2I generation with interactions that model how we use colored paints. PromptPaint allows users to go beyond language to mix prompts that express challenging concepts. Just as we iteratively tune colors through layered placements of paint on a physical canvas, PromptPaint similarly allows users to apply different prompts to different canvas areas and times of the generative process. Through a set of studies, we characterize different approaches for mixing prompts, design trade-offs, and socio-technical challenges for generative models. With PromptPaint we provide insight into future steerable generative tools.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606777"}, {"primary_key": "1264614", "vector": [], "sparse_vector": [], "title": "DeckFlow: A Card Game Interface for Exploring Generative Model Flows.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent Generative AI models have been shown to be substantially useful in different fields, often bridging modal gaps, such as text-prompted image or human motion generation. However, their accompanying interfaces do not sufficiently support iteration and interaction between models, and due to the computational intensity of generative technology, can be unforgiving to user errors and missteps. We propose DeckFlow, a no-code interface for multimodal generative workflows which encourages rapid iteration and experimentation between disparate models. DeckFlow emphasizes the persistence of output, the maintenance of generation settings and dependencies, and continual steering through user-defined concept groups. Taking design cues from Card Games and Affinity Diagrams, DeckFlow is aimed to lower the barrier for non-experts to explore and interact with generative AI.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615821"}, {"primary_key": "1264615", "vector": [], "sparse_vector": [], "title": "WorldSmith: Iterative and Expressive Prompting for World Building with a Generative AI.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crafting a rich and unique environment is crucial for fictional world-building, but can be difficult to achieve since illustrating a world from scratch requires time and significant skill. We investigate the use of recent multi-modal image generation systems to enable users iteratively visualize and modify elements of their fictional world using a combination of text input, sketching, and region-based filling. WorldSmith enables novice world builders to quickly visualize a fictional world with layered edits and hierarchical compositions. Through a formative study (4 participants) and first-use study (13 participants) we demonstrate that WorldSmith offers more expressive interactions with prompt-based models. With this work, we explore how creatives can be empowered to leverage prompt-based generative AI as a tool in their creative process, beyond current \"click-once\" prompting UI paradigms.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606772"}, {"primary_key": "1264616", "vector": [], "sparse_vector": [], "title": "Demonstration of A Figma Plugin to Simulate A Large-Scale Network for Prototyping Social Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Prototyping novel social computing systems is a challenge in the field of Social Computing. Rapid experimentation with novel social network sites can offer valuable insights into their pro-social benefits before their public release to a large audience. In this demo, we present SocialSketch, a Figma Plugin to simulate a crowd in a social network. This demo introduces a plugin for Figma, a no-code interactive prototyping tool, enabling the creation of profile frames and prototype links for large crowds based on realistic network models. Privacy-protective profile content is generated using AI. The plugin aids UX designers, researchers, and students in prototyping social apps and exploring social system design.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615780"}, {"primary_key": "1264617", "vector": [], "sparse_vector": [], "title": "LiveLocalizer: Augmenting Mobile Speech-to-Text with Microphone Arrays, Optimized Localization and Beamforming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Speech-to-text capabilities on mobile devices have proven helpful for language translation, note-taking, hearing and speech accessibility, and meeting transcripts. However, their usefulness is constrained by being unable to distinguish between multiple speakers, track which direction speech is coming from, and provide acceptable performance in noisy environments.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615789"}, {"primary_key": "1264618", "vector": [], "sparse_vector": [], "title": "SmartPoser: Arm Pose Estimation with a Smartphone and Smartwatch Using UWB and IMU Data.", "authors": ["<PERSON>", "Vimal Mollyn", "<PERSON>"], "summary": "The ability to track a user's arm pose could be valuable in a wide range of applications, including fitness, rehabilitation, augmented reality input, life logging, and context-aware assistants. Unfortunately, this capability is not readily available to consumers. Systems either require cameras, which carry privacy issues, or utilize multiple worn IMUs or markers. In this work, we describe how an off-the-shelf smartphone and smartwatch can work together to accurately estimate arm pose. Moving beyond prior work, we take advantage of more recent ultra-wideband (UWB) functionality on these devices to capture absolute distance between the two devices. This measurement is the perfect complement to inertial data, which is relative and suffers from drift. We quantify the performance of our software-only approach using off-the-shelf devices, showing it can estimate the wrist and elbow joints with a median positional error of 11.0 cm, without the user having to provide training data.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606821"}, {"primary_key": "1264619", "vector": [], "sparse_vector": [], "title": "Demonstrating BrightMarkers: Fluorescent Tracking Markers Embedded in 3D Printed Objects.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Jamison John O&apos;Keefe", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this demonstration, we showcase BrightMarker, a fabrication method that uses fluorescent filaments to embed easily trackable markers in 3D printed color objects. By employing an infrared-fluorescent filament that emits light at a wavelength higher than the incident light, our optical detection setup filters out all the noise to only have the markers present in the infrared camera image. The high contrast of the markers allows us to robustly track them when objects are in motion.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615977"}, {"primary_key": "1264620", "vector": [], "sparse_vector": [], "title": "BrightMarker: 3D Printed Fluorescent Markers for Object Tracking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Jamison John O&apos;Keefe", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing invisible object tagging methods are prone to low resolution, which impedes tracking performance. We present BrightMarker, a fabrication method that uses fluorescent filaments to embed easily trackable markers in 3D printed color objects. By using an infrared-fluorescent filament that \"shifts\" the wavelength of the incident light, our optical detection setup filters out all the noise to only have the markers present in the infrared camera image. The high contrast of the markers allows us to track them robustly regardless of the moving objects' surface color.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606758"}, {"primary_key": "1264621", "vector": [], "sparse_vector": [], "title": "Experiencing Visual Blocks for ML: Visual Prototyping of AI Pipelines.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate Visual Blocks for ML, a visual programming platform that facilitates rapid prototyping of ML-based multimedia applications. As the public version of Rapsai [3], we further integrated large language models and custom APIs into the platform. In this demonstration, we will showcase how to build interactive AI pipelines in a few drag-and-drops, how to perform interactive data augmentation, and how to integrate pipelines into Colabs. In addition, we demonstrate a wide range of community-contributed pipelines in Visual Blocks for ML, covering various aspects including interactive graphics, chains of large language models, computer vision, and multi-modal applications. Finally, we encourage students, designers, and ML practitioners to contribute ML pipelines through https://github.com/google/visualblocks/tree/main/pipelines to inspire creative use cases. Visual Blocks for ML is available at http://visualblocks.withgoogle.com.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615817"}, {"primary_key": "1264622", "vector": [], "sparse_vector": [], "title": "Towards Generating UI Design Feedback with LLMs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Feedback on user interface (UI) mockups is crucial for the design process, and designers often seek and leverage feedback to improve their UIs. However, human feedback is not always readily available. Given the recent emergence of LLMs, which have been shown to be proficient in rule-based reasoning, we explore the potential of LLMs to provide feedback automatically. In particular, we investigate automating heuristic evaluation, which currently entails a human expert assessing how well a UI adheres to a given set of design guidelines. We build an LLM-based heuristic evaluation plugin for Figma, which designers can use to evaluate their UI mockups. The plugin queries the LLM with the guidelines and a JSON representation of the UI mockup and then renders the identified guideline violations as constructive suggestions for design improvements. Future work is needed to study what types of usability problems can be successfully identified by LLM-driven heuristic evaluation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615810"}, {"primary_key": "1264623", "vector": [], "sparse_vector": [], "title": "RadarFoot: Fine-grain Ground Surface Context Awareness for Smart Shoes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Everyday, billions of people use footwear for walking, running, or exercise. Of emerging interest are \"smart footwear\", which help users track gait, count steps or even analyse performance. However, such nascent footwear lack fine-grain ground surface context awareness, which could allow them to adapt to the conditions and create usable functions and experiences. Hence, this research aims to recognize the walking surface using a radar sensor embedded in a shoe, enabling ground context-awareness. Using data collected from 23 participants from an in-the-wild setting, we developed several classification models. We show that our model can detect five common terrain types with an accuracy of 80.0% and further ten terrain types with an accuracy of 66.3%, while moving. Importantly, it can detect the gait motion types such as 'walking', 'stepping up', 'stepping down', 'still', with an accuracy of 90%. Finally, we present potential use cases and insights for future work based on such ground-aware smart shoes.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606738"}, {"primary_key": "1264624", "vector": [], "sparse_vector": [], "title": "Style2Fab: Functionality-Aware Segmentation for Fabricating Personalized 3D Models with Generative AI.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With recent advances in Generative AI, it is becoming easier to automatically manipulate 3D models. However, current methods tend to apply edits to models globally, which risks compromising the intended functionality of the 3D model when fabricated in the physical world. For example, modifying functional segments in 3D models, such as the base of a vase, could break the original functionality of the model, thus causing the vase to fall over. We introduce a method for automatically segmenting 3D models into functional and aesthetic elements. This method allows users to selectively modify aesthetic segments of 3D models, without affecting the functional segments. To develop this method we first create a taxonomy of functionality in 3D models by qualitatively analyzing 1000 models sourced from a popular 3D printing repository, Thingiverse. With this taxonomy, we develop a semi-automatic classification method to decompose 3D models into functional and aesthetic elements. We propose a system called Style2Fab that allows users to selectively stylize 3D models without compromising their functionality. We evaluate the effectiveness of our classification method compared to human-annotated data, and demonstrate the utility of Style2Fab with a user study to show that functionality-aware segmentation helps preserve model functionality.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606723"}, {"primary_key": "1264625", "vector": [], "sparse_vector": [], "title": "Demonstration of Style2Fab: Functionality-Aware Segmentation for Fabricating Personalized 3D Models with Generative AI.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With recent advances in Generative AI, it is becoming easier to automatically manipulate 3D models. However, current methods tend to apply edits to models globally, which risks compromising the intended functionality of the 3D model when fabricated in the physical world. For example, modifying functional segments in 3D models, such as the base of a vase, could break the original functionality of the model, thus causing the vase to fall over. We introduce Style2Fab, a system for automatically segmenting 3D models into functional and aesthetic elements, and selectively modifying the aesthetic segments, without affecting the functional segments. Style2Fab uses a semi-automatic classification method to decompose 3D models into functional and aesthetic elements, and differentiable rendering to selectively stylize the functional segments. We demonstrate the functionality of this tool with six application examples across domains of Home Interior Design, Medical Applications, and Personal Accessories.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615769"}, {"primary_key": "1264626", "vector": [], "sparse_vector": [], "title": "Laseroma: A Small-Sized, Light-Weight, and Low-Cost Olfactory Display Releasing Multiple Odors through Pointed Heating.", "authors": ["<PERSON><PERSON>", "Yujing Tian", "<PERSON><PERSON>"], "summary": "In this paper, we propose Laseroma, a tiny, lightweight, and low-cost Olfactory Display (OD) that can release a maximum of eleven types of odors through pointed heating. Laseroma can be easily attached to Head-Mounted Devices (HMDs) for creating smell-enhanced and controllable Immersive Experiences. Laseroma mainly consists of (1) a laser diode for rapidly generating the pointed heat, (2) a modular and replaceable odor strip which carries eleven miniature odor reservoirs, and (3) a stepper motor for rotating the odor strip for switching the target aroma. We illustrated and articulated the system design, including the hardware, structural mechanism, materials, and fabrication process. We also conducted a preliminary study by testing the scent release performance through the perceived time and odor-release duration of a single scent container.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616691"}, {"primary_key": "1264627", "vector": [], "sparse_vector": [], "title": "VoxelHap: A Toolkit for Constructing Proxies Providing Tactile and Kinesthetic Haptic Feedback in Virtual Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Experiencing virtual environments is often limited to abstract interactions with objects. Physical proxies allow users to feel virtual objects, but are often inaccessible. We present the VoxelHap toolkit which enables users to construct highly functional proxy objects using Voxels and Plates. Voxels are blocks with special functionalities that form the core of each physical proxy. Plates increase a proxy's haptic resolution, such as its shape, texture or weight. Beyond providing physical capabilities to realize haptic sensations, VoxelHap utilizes VR illusion techniques to expand its haptic resolution. We evaluated the capabilities of the VoxelHap toolkit through the construction of a range of fully functional proxies across a variety of use cases and applications. In two experiments with 24 participants, we investigate a subset of the constructed proxies, studying how they compare to a traditional VR controller. First, we investigated VoxelHap's combined haptic feedback and second, the trade-offs of using ShapePlates. Our findings show that VoxelHap's proxies outperform traditional controllers and were favored by participants.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606722"}, {"primary_key": "1264628", "vector": [], "sparse_vector": [], "title": "Turn-It-Up: Rendering Resistance for Knobs in Virtual Reality through Undetectable Pseudo-Haptics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Rendering haptic feedback for interactions with virtual objects is an essential part of effective virtual reality experiences. In this work, we explore providing haptic feedback for rotational manipulations, e.g., through knobs. We propose the use of a Pseudo-Haptic technique alongside a physical proxy knob to simulate various physical resistances. In a psychophysical experiment with 20 participants, we found that designers can introduce unnoticeable offsets between real and virtual rotations of the knob, and we report the corresponding detection thresholds. Based on these, we present the Pseudo-Haptic Resistance technique to convey physical resistance while applying only unnoticeable pseudo-haptic manipulation. Additionally, we provide a first model of how C/D gains correspond to physical resistance perceived during object rotation, and outline how our results can be translated to other rotational manipulations. Finally, we present two example use cases that demonstrate the versatility and power of our approach.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606787"}, {"primary_key": "1264629", "vector": [], "sparse_vector": [], "title": "PressurePick: Muscle Tension Estimation for Guitar Players Using Unobtrusive Pressure Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When learning to play an instrument, it is crucial for the learner’s muscles to be in a relaxed state when practicing. Identifying, which parts of a song lead to increased muscle tension requires self-awareness during an already cognitively demanding task. In this work, we investigate unobtrusive pressure sensing for estimating muscle tension while practicing songs with the guitar. First, we collected data from twelve guitarists. Our apparatus consisted of three pressure sensors (one on each side of the guitar pick and one on the guitar neck) to determine the sensor that is most suitable for automatically estimating muscle tension. Second, we extracted features from the pressure time series that are indicative of muscle tension. Third, we present the hardware and software design of our PressurePick prototype, which is directly informed by the data collection and subsequent analysis.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606742"}, {"primary_key": "1264630", "vector": [], "sparse_vector": [], "title": "Video2Action: Reducing Human Interactions in Action Annotation of App Tutorial Videos.", "authors": ["<PERSON><PERSON> Feng", "Chunyang Chen", "<PERSON><PERSON><PERSON>"], "summary": "Tutorial videos of mobile apps have become a popular and compelling way for users to learn unfamiliar app features. To make the video accessible to the users, video creators always need to annotate the actions in the video, including what actions are performed and where to tap. However, this process can be time-consuming and labor-intensive. In this paper, we introduce a lightweight approach Video2Action, to automatically generate the action scenes and predict the action locations from the video by using image-processing and deep-learning methods. The automated experiments demonstrate the good performance of Video2Action in acquiring actions from the videos, and a user study shows the usefulness of our generated action cues in assisting video creators with action annotation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606778"}, {"primary_key": "1264631", "vector": [], "sparse_vector": [], "title": "E4UnityIntegration-MIT: An Open-Source Unity Plug-in for Collecting Physiological Data using Empatica E4 during Gameplay.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physiological measurement of player experience (PX) during gameplay has been of increasing interest within game research circles. A commonly-used non-invasive wearable device for physiological measurement is the Empatica E4 wristband, which offers multiple physiological metrics, ranging from electrodermal activity to heart rate. That said, the E4's integration with popular game engines such as Unity 3D presents certain challenges due to non-obvious critical bugs in the library and limited documentation applicability within the Unity context. In this paper, we present an open-source Unity plug-in designed to mitigate the challenges associated with integrating the E4 into Unity projects: E4UnityIntegration-MIT. The plug-in exposes the E4's API for interfacing with Unity C# scripts, thereby enabling realtime data collection and monitoring. E4UnityIntegration-MIT also provides the affordance of saving the E4 data into an external file for data analysis purposes.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616627"}, {"primary_key": "1264632", "vector": [], "sparse_vector": [], "title": "FibeRobo: Fabricating 4D Fiber Interfaces by Continuous Drawing of Temperature Tunable Liquid Crystal Elastomers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present FibeRobo, a thermally-actuated liquid crystal elastomer (LCE) fiber that can be embedded or structured into textiles and enable silent and responsive interactions with shape-changing, fiber-based interfaces. Three definitive properties distinguish FibeRobo from other actuating threads explored in HCI. First, they exhibit rapid thermal self-reversing actuation with large displacements (∼40%) without twisting. Second, we present a reproducible UV fiber drawing setup that produces hundreds of meters of fiber with a sub-millimeter diameter. Third, FibeRobo is fully compatible with existing textile manufacturing machinery such as weaving looms, embroidery, and industrial knitting machines. This paper contributes to developing temperature-responsive LCE fibers, a facile and scalable fabrication pipeline with optional heating element integration for digital control, mechanical characterization, and the establishment of higher hierarchical textile structures and design space. Finally, we introduce a set of demonstrations that illustrate the design space FibeRobo enables.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606732"}, {"primary_key": "1264633", "vector": [], "sparse_vector": [], "title": "ChromaNails: Re-Programmable Multi-Colored High-Resolution On-Body Interfaces using Photochromic Nail Polish.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We demonstrate ChromaNails, a physical nail reprogramming device that enables high-resolution multi-color textures on fingernails using photochromic nail polish for on-body interaction. Our ChromaNails reprogrammer uses a miniature RGB projector and a UV light source to project different wavelengths of light onto our photochromic nail polish. We create this nail polish by mixing cyan, magenta, and yellow (CMY) photochromic dye into a base substrate polish. This enables us to control the saturation and desaturation of the CMY particles inside our nail polish to various colors inside the CMY color space. Our integrated user interface enables laypeople to select their preferred color texture and adapts to various nail shapes. We demonstrate the usefulness of ChromaNails for on-body interaction through four application examples on reprogrammable fingernail QR codes, on-body calendars, security, and fashion.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615824"}, {"primary_key": "1264634", "vector": [], "sparse_vector": [], "title": "How To Eat Garlic Without Causing Bad Breath: Taste reproduction using a taste sensor and presentation of taste and aroma using a fork device.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study taste reproduction using taste sensors, with a particular emphasis on accurately measuring and reproducing the taste of garlic while reducing the associated bad breath. We measured and reproduced the taste of garlic using a combination of odorless substances. We also developed a fork integrated with a mechanism for releasing the aroma of allicin to provide controlled exposure to the nose for a complete eating experience. Through our experiments, we successfully achieved identical taste and smell experiences, effectively eliminating the occurrence of bad breath. To validate our findings, participants tasted a dish of spaghetti and reported that the culinary experience was very similar to that of normal garlic-infused dishes, with no detectable bad breath.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616659"}, {"primary_key": "1264635", "vector": [], "sparse_vector": [], "title": "Lorgnette: Creating Malleable Code Projections.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Projections of computer languages are tools that help users interact with representations that better fit their needs than plain text. We collected 62 projections from the literature and from a design workshop and found that 60% of them can be implemented using a table, a graph or a form. However, projections are often hardcoded for specific languages and situations, and in most cases only the developers of a code editor can create or adapt projections, leaving no room for appropriation by their users. We introduce lorgnette, a new framework for letting programmers augment their code editor with projections. We demonstrate five examples that use lorgnette to create projections that can be reused in new contexts. We discuss how this approach could help democratise projections and conclude with future work.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606817"}, {"primary_key": "1264636", "vector": [], "sparse_vector": [], "title": "Tyche: In Situ Analysis of Random Testing Effectiveness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated testing tools have adapted to increasing program complexity by reducing the user's role in the testing process. Approaches like property-based testing supplement traditional unit-testing with a mode declarative approach: rather than write traditional input-output examples, the user writes executable specifications of their programs. The testing framework then exercises those specifications with randomly generated values.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615788"}, {"primary_key": "1264637", "vector": [], "sparse_vector": [], "title": "SynthoGestures: A Novel Framework for Synthetic Dynamic Hand Gesture Generation for Driving Scenarios.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Creating a diverse and comprehensive dataset of hand gestures for dynamic human-machine interfaces in the automotive domain can be challenging and time-consuming. To overcome this challenge, we propose using synthetic gesture datasets generated by virtual 3D models. Our framework utilizes Unreal Engine to synthesize realistic hand gestures, offering customization options and reducing the risk of overfitting. Multiple variants, including gesture speed, performance, and hand shape, are generated to improve generalizability. In addition, we simulate different camera locations and types, such as RGB, infrared, and depth cameras, without incurring additional time and cost to obtain these cameras. Experimental results demonstrate that our proposed framework, SynthoGestures\\footnote{\\url{https://github.com/amrgomaaelhady/SynthoGestures}}, improves gesture recognition accuracy and can replace or augment real-hand datasets. By saving time and effort in the creation of the data set, our tool accelerates the development of gesture recognition systems for automotive applications.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616635"}, {"primary_key": "1264638", "vector": [], "sparse_vector": [], "title": "Constraint-Driven Robotic Surfaces, At Human-Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Robotic surfaces, whose form and function are under computational control, offer exciting new possibilities for environments that can be customized to fit user-specific needs. When these surfaces can be reprogrammed, a once-static structure can be repurposed to serve multiple different roles over time. In this paper, we introduce such a system. This is an architectural-scale robotic surface, which is able to begin in a neutral state, assume a desired functional shape, and later return to its neutral (flat) position. The surface can then assume a completely different functional shape, all under program control.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606740"}, {"primary_key": "1264639", "vector": [], "sparse_vector": [], "title": "VizAbility: Multimodal Accessible Data Visualization with Keyboard Navigation and Conversational Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Data visualization serves as a crucial tool for communicating important information in our society. Yet, as visualizations grow more complex, they become less accessible to individuals with visual impairments. Traditional accessibility approaches like alternative text and data tables often fall short of capturing the full potential of data visualization. To bridge this gap, we introduce VizAbility, a novel multimodal accessible system that combines keyboard navigation with conventional interaction, enabling individuals with visual impairments to actively engage with and explore data visualizations. We built an LLM-based pipeline that classifies user queries and synthesizes underlying data, chart structure, user locality, and web-based information to answer the queries. Our preliminary evaluation using real-world questions from blind individuals demonstrates the significant potential of VizAbility.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616669"}, {"primary_key": "1264640", "vector": [], "sparse_vector": [], "title": "Mirrorverse: Live Tailoring of Video Conferencing Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Clemens Nylandsted Klokmose"], "summary": "How can we let users adapt video-based meetings as easily as they rearrange furniture in a physical meeting room? We describe a design space for video conferencing systems that includes a five-step \"ladder of tailorability,\" from minor adjustments to live reprogramming of the interface. We then present Mirrorverse and show how it applies the principles of computational media to support live tailoring of video conferencing interfaces to accommodate highly diverse meeting situations. We present multiple use scenarios, including a virtual workshop, an online yoga class, and a stand-up team meeting to evaluate the approach and demonstrate its potential for new, remote meetings with fluid transitions across activities.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606767"}, {"primary_key": "1264641", "vector": [], "sparse_vector": [], "title": "EyeClick: A Robust Two-Step Eye-Hand Interaction for Text Entry in Augmented Reality Glasses.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Efficient text entry is a crucial aspect of the user experience for augmented reality (AR) head-mounted displays (HMD). Eye-tracking for virtual keyboard interaction is a popular choice for AR text entry, as it is intuitive, privacy-preserving, and socially acceptable. However, as AR HMDs move toward more consumer-friendly form factors and price points, technical constraints necessitate trade-offs that result in a limited field of view (FoV) and reduced eye-tracking accuracy.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615814"}, {"primary_key": "1264642", "vector": [], "sparse_vector": [], "title": "Sparkybot: An Embodied AI Agent-Powered Robot with Customizable Characters andInteraction Behavior for Children.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sparkybot is a novel robotic platform designed to enrich children-robot interaction by leveraging embodied agent technology. The platform allows children to customize the interaction characters and behaviors of the robot, providing a more personalized and engaging experience. Two scenarios are demonstrated to showcase how children can customize and interact with the robot. Our goal is to provide a platform that enables children to interact with robots in a more creative and engaging way.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615804"}, {"primary_key": "1264643", "vector": [], "sparse_vector": [], "title": "Melody Slot Machine on iPhone: Dial-type Interface for Morphed Melody: Dial-type Interface for Morphed Melody.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present Melody Slot Machine on iPhone, an iPhone application using the melodic morphing method on the basis of the Generative Theory of Tonal Music (GTTM). We previously developed a demonstration system called Melody Slot Machine to introduce the melodic morphing method and presented it at international conferences and exhibitions. Since in-person demonstrations were reduced due to the Covid-19 Pandemic, we implemented an application with the same functionality and made it available for downloading to experience it. Our Melody Slot Machine on iPhone currently has two contents available for download, and we plan to add more contents in the future.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615764"}, {"primary_key": "1264644", "vector": [], "sparse_vector": [], "title": "The View from MARS: Empowering Game Stream Viewers with Metadata Augmented Real-time Streaming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present MARS (Metadata Augmented Real-time Streaming), a system that enables game-aware streaming interfaces for Twitch. Current streaming interfaces provide a video stream of gameplay and a chat channel for conversation, but do not allow viewers to interact with game content independently from the steamer or other viewers. With MARS, a Unity game's metadata is rendered in real-time onto a Twitch viewer's interface. The metadata can then power viewer-side interfaces that are aware of the streamer's game activity and provide new capacities for viewers. Use cases include providing contextual information (e.g. clicking on a unit to learn more), improving accessibility (e.g. slowing down text presentation speed), and supporting novel stream-based game designs (e.g. asymmetric designs where the viewers know more than the streamer). We share the details of MARS' architecture and capabilities in this paper, and showcase a working prototype for each of our three proposed use cases.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606753"}, {"primary_key": "1264645", "vector": [], "sparse_vector": [], "title": "Parametric Haptics: Versatile Geometry-based Tactile Feedback Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Haptic feedback is important for immersive, assistive, or multimodal interfaces, but engineering devices that generalize across applications is notoriously difficult. To address the issue of versatility, we propose Parametric Haptics, geometry-based tactile feedback devices that are customizable to render a variety of tactile sensations. To achieve this, we integrate the actuation mechanism with the tactor geometry into passive 3D printable patches, which are then connected to a generic wearable actuation interface consisting of micro gear motors. The key benefit of our approach is that the 3D-printed patches are modular, can consist of varying numbers and shapes of tactors, and that the tactors can be grouped and moved by our actuation geometry over large areas of the skin. The patches are soft, thin, conformable, and easy to customize to different use cases, thus potentially enabling a large design space of diverse tactile sensations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606766"}, {"primary_key": "1264646", "vector": [], "sparse_vector": [], "title": "Sketchnote: Sketch-Based Visualization of Problem Decomposition in Block-Based Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Block-based programming effectively supports processes based on the syntactic and conceptual knowledge of programming; however, its effectiveness is limited to processes that require strategic knowledge. To resolve the problem, we present Sketchnote, which visualizes the problem decomposition process with sketching and multi-layered structure in block-based programming. Sketchnote allows programmers to sketch in the block attachment slots before inserting the actual code blocks. The multi-layer structure of Sketchnote also visualizes the hierarchy of code while extending the brick metaphor of block-based programming.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616654"}, {"primary_key": "1264647", "vector": [], "sparse_vector": [], "title": "Demonstrating Swarm Robots Capable of Cooperative Transitioning between Table and Wall.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Swarm User Interfaces (Swarm UIs) enable manipulation of user environment through the dynamic arrangement of small robots. However, the operation range of Swarm UIs are limited to a single plane due to their locomotion constraints, which are typically two-wheel-propelled. Here, We present a proof-of-concept design for swarm robots, which enables them to cooperatively transition between horizontal and vertical surfaces. Notably, this design requires only passive mechanical structure and does not rely on any powered electrical components. We demonstrate several application examples to showcase the feasibility of the robots.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615763"}, {"primary_key": "1264648", "vector": [], "sparse_vector": [], "title": "A Multi-modal Toolkit to Support DIY Assistive Technology Creation for Blind and Low Vision People.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fan", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design and build A11yBits, a tangible toolkit that empowers blind and low vision (BLV) people to easily create personalized do-it-yourself assistive technologies (DIY-ATs). A11yBits includes (1) a series of Sensing modules to detect both environmental information and user commands, (2) a set of Feedback modules to send multi-modal feedback, and (3) two Base modules (Sensing Base and Feedback Base) to power and connect the sensing and feedback modules. The toolkit enables accessible and easy assembly via a \"plug-and-play\" mechanism. BLV users can select and assemble their preferred modules to create personalized DIY-ATs.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616646"}, {"primary_key": "1264649", "vector": [], "sparse_vector": [], "title": "Living Papers: A Language Toolkit for Augmented Scholarly Communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Computing technology has deeply shaped how academic articles are written and produced, yet article formats and affordances have changed little over centuries. The status quo consists of digital files optimized for printed paper—ill-suited to interactive reading aids, accessibility, dynamic figures, or easy information extraction and reuse. Guided by formative discussions with scholarly communication researchers and publishing tool developers, we present Living Papers, a language toolkit for producing augmented academic articles that span print, interactive, and computational media. Living Papers articles may include formatted text, references, executable code, and interactive components. Articles are parsed into a standardized document format from which a variety of outputs are generated, including static PDFs, dynamic web pages, and extraction APIs for paper content and metadata. We describe Living Papers' architecture, document model, and reactive runtime, and detail key aspects such as citation processing and conversion of interactive components to static content. We demonstrate the use and extension of Living Papers through examples spanning traditional research papers, explorable explanations, information extraction, and reading aids such as enhanced citations, cross-references, and equations. Living Papers is available as an extensible, open source platform intended to support both article authors and researchers of augmented reading and writing experiences.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606791"}, {"primary_key": "1264650", "vector": [], "sparse_vector": [], "title": "Demonstrating HUGO, a High-Resolution Tactile Emulator for Complex Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Wolf", "<PERSON><PERSON>-Manor"], "summary": "We demonstrate HUGO, a novel device developed to deliver enhanced tactile feedback and facilitate interaction with real-world surfaces. The device aims to overcome the limitations of existing cutaneous feedback devices, which often provide a restricted range of sensations and are primarily tested on simple synthetic surfaces. HUGO was meticulously designed through a human-centered process to enable users to experience realistic touch sensations encountered in various real-world scenarios. HUGO utilizes a parallel manipulator and a pin-array mechanism that operate concurrently at a frequency of up to 200Hz to simulate both coarse and fine geometrical features. By employing a high operation frequency and decomposing the tactile feedback into distinct features, HUGO enables a more accurate replication of tactile experiences associated with different surfaces. The demonstration will showcase HUGO's capabilities in providing authentic haptic feedback. This includes facilitating social interactions, enhancing e-commerce experiences, and improving gaming interactions through realistic haptic engagement with real-world surfaces.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615772"}, {"primary_key": "1264651", "vector": [], "sparse_vector": [], "title": "KnitScript: A Domain-Specific Scripting Language for Advanced Machine Knitting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knitting machines can fabricate complex fabric structures using robust industrial fabrication machines. However, machine knitting's full capabilities are only available through low-level programming languages that operate on individual machine operations. We present KnitScript, a domain-specific machine knitting scripting language that supports computationally driven knitting designs. KnitScript provides a comprehensive virtual model of knitting machines, giving access to machine-level capabilities as they are needed while automating a variety of tedious and error-prone details. Programmers can extend KnitScript with Python programs to create more complex programs and user interfaces. We evaluate the expressivity of KnitScript through a user study where nine machine knitters used KnitScript code to modify knitting patterns. We demonstrate the capabilities of KnitScript through three demonstrations where we create: a program for generating knitted figures of randomized trees, a parameterized hat template that can be modified with accessibility features, and a pattern for a parametric mixed-material lampshade. KnitScript advances the state of machine-knitting research by providing a platform to develop and share complex knitting algorithms, design tools, and patterns. 1", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606789"}, {"primary_key": "1264652", "vector": [], "sparse_vector": [], "title": "FluencyAR: Augmented Reality Language Immersion.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "FluencyAR is an augmented reality second language learning tool centered around the concepts of language immersion and self-talk. For many second language learners, advancing into upper levels of fluency can be difficult without sufficient opportunities to practice. Traditional solutions of tutoring or finding exchange partners are often inconvenient or limiting. FluencyAR provides situational conversation practice in highly self-directed practice sessions that imitate environments where the target language is dominant. We utilize augmented reality to allow users to practice their target language with immediate feedback at any time, and from any location. Using ChatGPT and the physical space of the user, we can produce unique and challenging conversation prompts relative to a user's surroundings, ensuring that sessions remain interesting.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616670"}, {"primary_key": "1264653", "vector": [], "sparse_vector": [], "title": "Engraft: An API for Live, Rich, and Composable Programming.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Live & rich tools can support a diversity of domain-specific programming tasks, from visualization authoring to data wrangling. Real-world programming, however, requires performing multiple tasks in concert, calling for the use of multiple tools alongside conventional code. Programmers lack environments capable of composing live & rich tools to support these situations. To enable this composition, we contribute Engraft, a component-based API that allows live & rich tools to be embedded within larger environments like computational notebooks. Through recursive embedding of components, Engraft enables several new forms of composition: not only embedding tools inside environments, but also embedding environments within each other and embedding tools and environments in the outside world, including conventional codebases. We demonstrate Engraft with examples from diverse domains, including web-application development, command-line scripting, and physics education. By providing composability, Engraft can help cultivate a cycle of use and innovation in live & rich programming.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606733"}, {"primary_key": "1264654", "vector": [], "sparse_vector": [], "title": "Context-Aware Sit-Stand Desk for Promoting Healthy and Productive Behaviors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sol Ie Lim", "<PERSON>"], "summary": "To mitigate the risk of chronic diseases caused by prolonged sitting, sit-stand desks are promoted as an effective intervention to foster healthy behaviors among knowledge workers by allowing periodic posture switching between sitting and standing. However, conventional systems either let users manually switch the mode, and some research visited automated notification systems with pre-set time intervals. While this regular notification can promote healthy behaviors, such notification can act as external interruptions that hinder individuals' working productivity. Notably, knowledge workers are known to be reluctant to change their physical postures when concentrating. To address these issues, we propose considering work context based on their screen activities to encourage computer users to alternate their postures when it can minimize disruption, promoting healthy and productive behaviors. To that end, we are in the process of building a context-aware sit-stand desk that can promote healthy and productive behaviors. To that end, we have completed two modules: an application that monitors users' computer's ongoing activities and a sensor module that can measure the height of sit-stand desks for data collection. The collected data includes computer activities, measured desk height, and their willingness to switch to standing modes and will be used to build an LSTM prediction model to suggest optimal time points for posture changes, accompanied by appropriate desk height. In this work, we acknowledge previous relevant research, outline ongoing deployment efforts, and present our plan to validate the effectiveness of our approach via user studies.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616694"}, {"primary_key": "1264655", "vector": [], "sparse_vector": [], "title": "Memory Sandbox: Transparent and Interactive Memory Management for Conversational Agents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The recent advent of large language models (LLM) has resulted in high-performing conversational agents such as ChatGPT. These agents must remember key information from an ongoing conversation to provide responses that are contextually relevant to the user. However, these agents have limited memory and can be distracted by irrelevant parts of the conversation. While many strategies exist to manage conversational memory, users currently lack affordances for viewing and controlling what the agent remembers, resulting in a poor mental model and conversational breakdowns. In this paper, we present Memory Sandbox, an interactive system and design probe that allows users to manage the conversational memory of LLM-powered agents. By treating memories as data objects that can be viewed, manipulated, recorded, summarized, and shared across conversations, Memory Sandbox provides interaction affordances for users to manage how the agent should 'see' the conversation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615796"}, {"primary_key": "1264656", "vector": [], "sparse_vector": [], "title": "GenAssist: Making Image Generation Accessible.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Blind and low vision (BLV) creators use images to communicate with sighted audiences. However, creating or retrieving images is challenging for BLV creators as it is difficult to use authoring tools or assess image search results. Thus, creators limit the types of images they create or recruit sighted collaborators. While text-to-image generation models let creators generate high-fidelity images based on a text description (i.e. prompt), it is difficult to assess the content and quality of generated images. We present GenAssist, a system to make text-to-image generation accessible. Using our interface, creators can verify whether generated image candidates followed the prompt, access additional details in the image not specified in the prompt, and skim a summary of similarities and differences between image candidates. To power the interface, GenAssist uses a large language model to generate visual questions, vision-language models to extract answers, and a large language model to summarize the results. Our study with 12 BLV creators demonstrated that GenAssist enables and simplifies the process of image selection and generation, making visual authoring more accessible to all.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606735"}, {"primary_key": "1264657", "vector": [], "sparse_vector": [], "title": "HoloBots: Augmenting Holographic Telepresence with Mobile Robots for Tangible Remote Collaboration in Mixed Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces HoloBots, a mixed reality remote collaboration system that augments holographic telepresence with synchronized mobile robots. Beyond existing mixed reality telepresence, HoloBots lets remote users not only be visually and spatially present, but also physically engage with local users and their environment. HoloBots allows the users to touch, grasp, manipulate, and interact with the remote physical environment as if they were co-located in the same shared space. We achieve this by synchronizing holographic user motion (Hololens 2 and Azure Kinect) with tabletop mobile robots (Sony Toio). Beyond the existing physical telepresence, HoloBots contributes to an exploration of broader design space, such as object actuation, virtual hand physicalization, world-in-miniature exploration, shared tangible interfaces, embodied guidance, and haptic communication. We evaluate our system with twelve participants by comparing it with hologram-only and robot-only conditions. Both quantitative and qualitative results confirm that our system significantly enhances the level of co-presence and shared experience, compared to the other conditions.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606727"}, {"primary_key": "1264658", "vector": [], "sparse_vector": [], "title": "FluxTangible: Simple and Dynamic Haptic Tangible with Bumps and Vibrations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To add haptic feedback and tactile cues of a physical interface to interaction with mobile touch displays, several passive systems have been proposed as Tangible User Interfaces (TUIs). The haptic feedback from such passive objects can be customized by changing their shape and structure but cannot be changed dynamically. Therefore, we propose a simple TUI that can provide dynamic haptic feedback on a touch device. We use an electromagnet embedded in the tangible object and a magnetic sheet fixed on the back of a touch device. An electric current flowing through the electromagnet and the customizable magnetic pattern printed on the magnetic sheet together produce magnetic interference. The magnetic interference caused by these magnetic objects is actively generated during interaction with the touch device to provide dynamic haptic feedback of both bumps and vibrations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615762"}, {"primary_key": "1264659", "vector": [], "sparse_vector": [], "title": "Smart-Pikachu: Extending Interactivity of Stuffed Animals with Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose Smart-Pikachu, a stuffed animal equipped with sensing and actuation to explore the use of large language models (LLM's) with sensor data inputs. The augmentation of pressure sensing will allow for the LLM to interpret various interactions such as hugs and handshakes with the user. Furthermore, the actuation capabilities will extend our system's interactivity by providing physical feedback to the user. We will also incorporate text-to-speech output from the LLM to add another mode of interaction between the system and user. In this Student Innovation Challenge, we intend to explore applications at the intersection of sensing and interaction through LLM's and demonstrate an extension of LLMs' multimodal capabilities.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625219"}, {"primary_key": "1264660", "vector": [], "sparse_vector": [], "title": "AmplifiedCoaster: Virtual Roller Coaster Experience using Motorized Ramps and Personal Mobility Vehicle.", "authors": ["Shunta <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A novel virtual reality (VR) ride consisting of a head-mounted display (HMD) and an electric wheelchair was developed for a virtual roller coaster experience. The VR ride was integrated with an electric wheeled ramp to amplify the perception of virtual ascent and descent. The ascending and descending motion on a slope was replicated continuously with varying curvatures. The system features an electric wheelchair and two motorized ramps with a fixed 10-degree angle, which the wheelchair can drive on and off to simulate ascending and descending in VR. Moreover, by adjusting the relative speed and position of the wheelchair and ramps, we can simulate pitch angle curvature in VR. We are investigating several methods for providing multiple ascent and descent experiences, and introduce one of them.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616662"}, {"primary_key": "1264661", "vector": [], "sparse_vector": [], "title": "Use of an AI-powered Rewriting Support Software in Context with Other Tools: A Study of Non-Native English Speakers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Academic writing in English can be challenging for non-native English speakers (NNESs). AI-powered rewriting tools can potentially improve NNESs' writing outcomes at a low cost. However, whether and how NNESs make valid assessments of the revisions provided by these algorithmic recommendations remains unclear. We report a study where NNESs leverage an AI-powered rewriting tool, <PERSON><PERSON>, to polish their drafted academic essays. We examined the participants' interactions with the tool via user studies and interviews. Our data reveal that most participants used <PERSON><PERSON> in combination with other tools, such as machine translation (MT), and those who used MT had different ways of understanding and evaluating <PERSON><PERSON>'s suggestions than those who did not. Based on these findings, we assert that NNESs' quality assessment in AI-powered rewriting tools is influenced by the simultaneous use of multiple tools, offering valuable insights into the design of future rewriting tools for NNESs.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606810"}, {"primary_key": "1264662", "vector": [], "sparse_vector": [], "title": "Front Row: Automatically Generating Immersive Audio Representations of Tennis Broadcasts for Blind Viewers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Basel Hindi", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Blind and low-vision (BLV) people face challenges watching sports due to the lack of accessibility of sports broadcasts. Currently, BLV people rely on descriptions from TV commentators, radio announcers, or their friends to understand the game. These descriptions, however, do not allow BLV viewers to visualize the action by themselves. We present Front Row, a system that automatically generates an immersive audio representation of sports broadcasts, specifically tennis, allowing BLV viewers to more directly perceive what is happening in the game. Front Row first recognizes gameplay from the video feed using computer vision, then renders players' positions and shots via spatialized (3D) audio cues. User evaluations with 12 BLV participants show that Front Row gives BLV viewers a more accurate understanding of the game compared to TV and radio, enabling viewers to form their own opinions on players' moods and strategies. We discuss future implications of Front Row and illustrate several applications, including a Front Row plug-in for video streaming platforms to enable BLV people to visualize the action in sports videos across the Web.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606830"}, {"primary_key": "1264663", "vector": [], "sparse_vector": [], "title": "Ubi-TOUCH: Ubiquitous Tangible Object Utilization through Consistent Hand-object interaction in Augmented Reality.", "authors": ["<PERSON><PERSON>", "Jingyu Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Utilizing everyday objects as tangible proxies for Augmented Reality (AR) provides users with haptic feedback while interacting with virtual objects. Yet, existing methods focus on the attributes of the objects, constraining the possible proxies and yielding inconsistency in user experience. Therefore, we propose Ubi-TOUCH, an AR system that assists users in seeking a wider range of tangible proxies for AR applications based on the hand-object interaction (HOI) they desire. Given the target interaction with a virtual object, the system scans the users' vicinity and recommends object proxies with similar interactions. Upon user selection, the system simultaneously tracks and maps users' physical HOI to the virtual HOI, adaptively optimizing object 6 DoF and the hand gesture to provide consistency between the interactions. We showcase promising use cases of Ubi-TOUCH, such as remote tutorials, AR gaming, and Smart Home control. Finally, we evaluate the performance and usability of Ubi-TOUCH with a user study.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606793"}, {"primary_key": "1264664", "vector": [], "sparse_vector": [], "title": "HRTF Estimation in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Head Related Transfer Functions (HRTFs) play a crucial role in creating immersive spatial audio experiences. However, HRTFs differ significantly from person to person, and traditional methods for estimating personalized HRTFs are expensive, time-consuming, and require specialized equipment. We imagine a world where your personalized HRTF can be determined by capturing data through earbuds in everyday environments. In this paper, we propose a novel approach for deriving personalized HRTFs that only relies on in-the-wild binaural recordings and head tracking data. By analyzing how sounds change as the user rotates their head through different environments with different noise sources, we can accurately estimate their personalized HRTF. Our results show that our predicted HRTFs closely match ground-truth HRTFs measured in an anechoic chamber. Furthermore, listening studies demonstrate that our personalized HRTFs significantly improve sound localization and reduce front-back confusion in virtual environments. Our approach offers an efficient and accessible method for deriving personalized HRTFs and has the potential to greatly improve spatial audio experiences.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606782"}, {"primary_key": "1264665", "vector": [], "sparse_vector": [], "title": "SoundMist: Novel Interface for Spatial Auditory Experience.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a novel method called 'spraying sound' for immersive auditory and spatial experiences. Our prototype SoundMist disperses sound into the surrounding space, enhancing immersion and spatial perception. Through user tests with 11 participants, we demonstrate the effectiveness of this approach in enriching the auditory experience and expanding possibilities for spatial auditory interactions. This research opens up opportunities to explore immersive spatial experiences with sound and makes a contribution by proposing a novel method of sound-space interaction through a system that sprays sound in a spatial context.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616622"}, {"primary_key": "1264666", "vector": [], "sparse_vector": [], "title": "Reprogrammable Digital Metamaterials for Interactive Devices.", "authors": ["Yu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present digital mechanical metamaterials that enable multiple computation loops and reprogrammable logic functions, making a significant step towards passive yet interactive devices. Our materials consist of many cells that transmit signals using an embedded bistable spring. When triggered, the bistable spring displaces and triggers the next cell. We integrate a recharging mechanism to recharge the bistable springs, enabling multiple computation rounds. Between the iterations, we enable reprogramming the logic functions after fabrication. We demonstrate that such materials can trigger a simple controlled actuation anywhere in the material to change the local shape, texture, stiffness, and display. This enables large-scale interactive and functional materials with no or a small number of external actuators. We showcase the capabilities of our system with various examples: a haptic floor with tunable stiffness for different VR scenarios, a display with easy-to-reconfigure messages after fabrication, or a tactile notification integrated into users' desktops.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606752"}, {"primary_key": "1264667", "vector": [], "sparse_vector": [], "title": "Demonstrating 1D-Touch: NLP-Assisted Coarse Text Selection via a Semi-Direct Gesture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Fuling Sun", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing text selection techniques on touchscreen focus on improving the control for moving the carets. Coarse-grained text selection on word- and phrase- levels have not received much support beyond word-snapping and entity recognition. We introduce 1D-Touch, a novel text selection method that complements the carets-based sub-word selection by facilitating the selection of words and larger semantic units. This method employs a simple vertical slide gesture to expand and contract a selection area from a word. The expansion can be by words or by semantic chunks ranging from sub-phrases to sentences, as implemented in two variants of our technique named WordTouch and ChunkTouch. This approach shifts the concept of text selection, away from defining a range by locating the first and last characters, towards a dynamic process of expanding and contracting a textual entity. While the full paper (expected to appear at the ACM ISS 2023) details the evaluation, this demonstration showcases 1D-Touch with a few applications of coarse-grained text selection, to engage the audience in discussions about its effectiveness and applications, as well as its integration with existing character-level selection techniques.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615797"}, {"primary_key": "1264668", "vector": [], "sparse_vector": [], "title": "Graphologue: Exploring Large Language Model Responses with Interactive Diagrams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large language models (LLMs) have recently soared in popularity due to their ease of access and the unprecedented ability to synthesize text responses to diverse user questions. However, LLMs like ChatGPT present significant limitations in supporting complex information tasks due to the insufficient affordances of the text-based medium and linear conversational structure. Through a formative study with ten participants, we found that LLM interfaces often present long-winded responses, making it difficult for people to quickly comprehend and interact flexibly with various pieces of information, particularly during more complex tasks. We present Graphologue, an interactive system that converts text-based responses from LLMs into graphical diagrams to facilitate information-seeking and question-answering tasks. Graphologue employs novel prompting strategies and interface designs to extract entities and relationships from LLM responses and constructs node-link diagrams in real-time. Further, users can interact with the diagrams to flexibly adjust the graphical presentation and to submit context-specific prompts to obtain more information. Utilizing diagrams, Graphologue enables graphical, non-linear dialogues between humans and LLMs, facilitating information exploration, organization, and comprehension.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606737"}, {"primary_key": "1264669", "vector": [], "sparse_vector": [], "title": "SUPREYES: <PERSON><PERSON><PERSON> Resolutin for EYES Using Implicit Neural Representation Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce SUPREYES – a novel self-supervised method to increase the spatio-temporal resolution of gaze data recorded using low(er)-resolution eye trackers. Despite continuing advances in eye tracking technology, the vast majority of current eye trackers – particularly mobile ones and those integrated into mobile devices – suffer from low-resolution gaze data, thus fundamentally limiting their practical usefulness. SUPREYES learns a continuous implicit neural representation from low-resolution gaze data to up-sample the gaze data to arbitrary resolutions. We compare our method with commonly used interpolation methods on arbitrary scale super-resolution and demonstrate that SUPREYES outperforms these baselines by a significant margin. We also test on the sample downstream task of gaze-based user identification and show that our method improves the performance of original low-resolution gaze data and outperforms other baselines. These results are promising as they open up a new direction for increasing eye tracking fidelity as well as enabling new gaze-based applications without the need for new eye tracking equipment.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606780"}, {"primary_key": "1264670", "vector": [], "sparse_vector": [], "title": "Touch&apos;n&apos;Draw: Rapid 3D Sketching with Fluent Bimanual Coordination.", "authors": ["Taegyu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In perspective drawing, designers express 3D shapes by drawing auxiliary lines that construct surfaces and drawing design curves on them. However, drawing auxiliary lines can be challenging, and too many of them can make the drawing difficult to understand. To address these issues, we present a novel 3D sketching system that allows the user to quickly and easily create instant auxiliary lines and instant sketch surfaces for drawing desired 3D curves with fluent bimanual touch and pen interactions. We produced a concept sketch using our system to showcase its potential usefulness.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616700"}, {"primary_key": "1264671", "vector": [], "sparse_vector": [], "title": "Double-Sided Tactile Interactions for Grasping in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For grasping, tactile stimuli to multiple fingertips are crucial for realistic shape rendering and precise manipulation. Pinching is particularly important in virtual reality since it is frequently used to grasp virtual objects. However, the interaction space of tactile feedback around pinching is underexplored due to a lack of means to provide co-located but different stimulation to finger pads. We propose a double-sided electrotactile device with a thin and flexible form factor to fit within pinched fingerpads, comprising two overlapping 3 × 3 electrode arrays. Using this new tactile interface, we define a new concept of double-sided tactile interactions with three feedback modes: (1) single-sided stimulation, (2) simultaneous double-sided stimulation, and (3) spatiotemporal double-sided stimulation. Through two user studies, we (1) demonstrate that participants can accurately discriminate between single-sided and double-sided stimulation and find a qualitative difference in tactile sensation; and (2) confirm the occurrence of apparent tactile motion between fingers and present optimal parameters for continuous or discrete movements. Based on these findings, we demonstrate five VR applications to exemplify how double-sided tactile interactions can produce spatiotemporal movement of a virtual object between fingers and enrich touch feedback for UI operation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606798"}, {"primary_key": "1264672", "vector": [], "sparse_vector": [], "title": "TrainerTap: Weightlifting Support System Prototype Simulating Personal Trainer&apos;s Tactile and Auditory Guidance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Working out alone at the gym often lacks the quality and intensity of exercises compared to the training session with a personal trainer. To narrow this gap, we introduce TrainerTap, which simulates the personal trainer's presence during solitary weightlifting workouts. TrainerTap replicates the trainer's manual interventions of tapping the trainee's body parts to capture their attention on target muscles and provides auditory guidance to support executing the movements at a consistent tempo.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616644"}, {"primary_key": "1264673", "vector": [], "sparse_vector": [], "title": "Towards Flexible and Robust User Interface Adaptations With Multiple Objectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Clemens Nylandsted Klokmose", "<PERSON>"], "summary": "This paper proposes a new approach for online UI adaptation that aims to overcome the limitations of the most commonly used UI optimization method involving multiple objectives: weighted sum optimization. Weighted sums are highly sensitive to objective formulation, limiting the effectiveness of UI adaptations. We propose ParetoAdapt, an adaptation approach that uses online multi-objective optimization with a posteriori articulated preferences---that is, articulation of preferences after the optimization has concluded---to make UI adaptation robust to incomplete and inaccurate objective formulations. It offers users a flexible way to control adaptations by selecting from a set of Pareto optimal adaptation proposals and adjusting them to fit their needs. We showcase the feasibility and flexibility of ParetoAdapt by implementing an online layout adaptation system in a state-of-the-art 3D UI adaptation framework. We further evaluate its robustness and run-time in simulation-based experiments that allow us to systematically change the accuracy of the estimated user preferences. We conclude by discussing how our approach may impact the usability and practicality of online UI adaptations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606799"}, {"primary_key": "1264674", "vector": [], "sparse_vector": [], "title": "User Interface Constraints to Influence User Behaviour when Reading and Writing.", "authors": ["<PERSON><PERSON>"], "summary": "Constraints are fundamental to human-centered design. Although by definition, constraints \"limit\" or \"restrict\" the capability of software, when designed correctly, they can have enabling characteristics as well. In my dissertation, I seek to understand how user interface constraints can influence user behaviour when reading and writing text. First, I discuss a document reader with auto-scrolling to facilitate time-bounded reading for increased focus. Second, I contribute the idea of limiting how much text can be highlighted in a document to encourage readers to think more about what is truly important in the document. Lastly, I discuss how constraining an AI writing assistant through prompts with varying levels of detail may improve a writer's feelings of ownership. Through these three projects, my dissertation will contribute novel constraints-based interaction techniques that can be integrated into new or existing systems, which is of interest to the UIST community and the HCI community more broadly.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616710"}, {"primary_key": "1264675", "vector": [], "sparse_vector": [], "title": "Transferable Microgestures Across Hand Posture and Location Constraints: Leveraging the Middle, Ring, and Pinky Fingers.", "authors": ["<PERSON><PERSON>", "Parastoo Abtahi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Microgestures can enable auxiliary input when the hands are occupied. Although prior work has evaluated the comfort of microgestures performed by the index finger and thumb, these gestures cannot be performed while the fingers are constrained by specific hand locations or postures. As the hand can be freely positioned with no primary posture, partially constrained while forming a pose, or highly constrained while grasping an object at a specific location, we leverage the middle, ring, and pinky fingers to provide additional opportunities for auxiliary input across varying levels of hand constraints. A design space and applications demonstrate how such microgestures can transfer across hand location and posture constraints. An online study evaluated their comfort and effort and a lab study evaluated their use for task-specific microinteractions. The results revealed that many middle finger microgestures were comfortable, and microgestures performed while forming a pose were preferred over baseline techniques.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606713"}, {"primary_key": "1264676", "vector": [], "sparse_vector": [], "title": "Mo2Hap: Rendering VR Performance Motion Flow to Upper-body Vibrotactile Haptic Feedback.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a unique haptic rendering framework that transforms the performer's actions into wearable vibrotactile feedback for an immersive virtual reality (VR) performance experience. To capture essential movements from the virtual performer, we propose a method called Motion Salient Triangle. Motion Salient Triangle is a real-time 3D polygon that computes haptic characteristics (intensity, location) based on motion skeletal data. Here, we employ an entire upper-body haptic system that provides vibrotactile feedback on the torso, back, and shoulders. This haptic rendering pipeline enable audiences to experience immersive VR performance by accommodating the performer's motions on top of motion-to-haptic feedback.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615775"}, {"primary_key": "1264677", "vector": [], "sparse_vector": [], "title": "Event-Based Pupil Tracking Using Bright and Dark Pupil Effect.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time high-speed gaze estimation can enable next-generation gaze-based interaction. The event camera, which captures intensity variations at high frequency, has been employed to this end. However, pupil tracking based only on events is difficult because events are sparse and limited. We propose high-speed pupil tracking using an event camera based on the bright and dark pupil effect. Two illumination sources generate events in the pupil area, and the pupil center is determined in real time at over 2000 Hz without requiring complete image from the events. We implemented gaze target estimation using smooth-pursuit eye movements and confirmed high-speed pupil tracking that may reduce the delay in gaze-based interaction.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616657"}, {"primary_key": "1264678", "vector": [], "sparse_vector": [], "title": "Conductive, Ferromagnetic and Bendable 3D Printed Hair for Designing Interactive Objects: Conductive, Ferromagnetic and Bendable 3D Printed Hair.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The hair structure, a characteristic object that can be formed using 3D printers, is used to enrich the expressivity and haptic sensation of a printed object. However, in conventional 3D printing techniques, the hair structure is printed using a uniform and general plastic such as Poly-Lactic Acid (PLA). In this study, we attempt to print the hair structure using conductive and magnetic iron filaments, commonly used to allow an Fused Deposition Modeling (FDM) printer to create a functional object, to extend the possibility of the 3D printed hair technique. Furthermore, we planted the printed hair in the soft resin used for resin crafts. We provide detailed material information and validate the printability of each filament. With these methods, we demonstrate applications, such as hairy devices that can detect when a human touches hair, brushes attracted by magnet arrays, and flexible attachment of hair structures to the human body.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615823"}, {"primary_key": "1264679", "vector": [], "sparse_vector": [], "title": "Synergi: A Mixed-Initiative System for Scholarly Synthesis and Sensemaking.", "authors": ["Hyeonsu B. Kang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficiently reviewing scholarly literature and synthesizing prior art are crucial for scientific progress. Yet, the growing scale of publications and the burden of knowledge make synthesis of research threads more challenging than ever. While significant research has been devoted to helping scholars interact with individual papers, building research threads scattered across multiple papers remains a challenge. Most top-down synthesis (and LLMs) make it difficult to personalize and iterate on the output, while bottom-up synthesis is costly in time and effort. Here, we explore a new design space of mixed-initiative workflows. In doing so we develop a novel computational pipeline, Synergi, that ties together user input of relevant seed threads with citation graphs and LLMs, to expand and structure them, respectively. Synergi allows scholars to start with an entire threads-and-subthreads structure generated from papers relevant to their interests, and to iterate and customize on it as they wish. In our evaluation, we find that <PERSON><PERSON><PERSON><PERSON> helps scholars efficiently make sense of relevant threads, broaden their perspectives, and increases their curiosity. We discuss future design implications for thread-based, mixed-initiative scholarly synthesis support tools.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606759"}, {"primary_key": "1264680", "vector": [], "sparse_vector": [], "title": "Scene Responsiveness for Visuotactile Illusions in Mixed Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Manipulating their environment is one of the fundamental actions that humans, and actors more generally, perform. Yet, today's mixed reality systems enable us to situate virtual content in the physical scene but fall short of expanding the visual illusion to believable environment manipulations. In this paper, we present the concept and system of Scene Responsiveness, the visual illusion that virtual actions affect the physical scene. Using co-aligned digital twins for coherence-preserving just-in-time virtualization of physical objects in the environment, Scene Responsiveness allows actors to seemingly manipulate physical objects as if they were virtual. Based on Scene Responsiveness, we propose two general types of end to-end illusionary experiences that ensure visuotactile consistency through the presented techniques of object elusiveness and object rephysicalization. We demonstrate how our Daydreaming illusion enables virtual characters to enter the scene through a physically closed door and vandalize the physical scene, or users to enchant and summon far-away physical objects. In a user evaluation of our Copperfield illusion, we found that Scene Responsiveness can be rendered so convincingly that it lends itself to magic tricks. We present our system architecture and conclude by discussing the implications of scene-responsive mixed reality for gaming and telepresence.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606825"}, {"primary_key": "1264681", "vector": [], "sparse_vector": [], "title": "LensTouch: Touch Input on Lens Surfaces of Smart Glasses.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart glasses are promising devices that allow the user to experience augmented reality (AR) and watch movies as on a big screen. As their design is primarily focused on their function as output devices, their input functionality is limited. We propose LensTouch, which enhances the input vocabulary by using touches on the lens of the smart glasses as inputs. The user can place his/her finger on the lens while viewing both the finger and the image displayed. An experiment shows the user can select the target quickly and/or accurately depending on the setting.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615792"}, {"primary_key": "1264682", "vector": [], "sparse_vector": [], "title": "Pantœnna: Mouth pose estimation for ar/vr headsets using low-profile antenna and impedance characteristic sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Methods for faithfully capturing a user's holistic pose have immediate uses in AR/VR, ranging from multimodal input to expressive avatars. Although body-tracking has received the most attention, the mouth is also of particular importance, given that it is the channel for both speech and facial expression. In this work, we describe a new RF-based approach for capturing mouth pose using an antenna integrated into the underside of a VR/AR headset. Our approach side-steps privacy issues inherent in camera-based methods, while simultaneously supporting silent facial expressions that audio-based methods cannot. Further, compared to bio-sensing methods such as EMG and EIT, our method requires no contact with the wearer's body and can be fully self-contained in the headset, offering a high degree of physical robustness and user practicality. We detail our implementation along with results from two user studies, which show a mean 3D error of 2.6 mm for 11 mouth keypoints across worn sessions without re-calibration.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606805"}, {"primary_key": "1264683", "vector": [], "sparse_vector": [], "title": "Supporting Independence of Autistic Adults through Mobile and Virtual Reality Technologies.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Pervasive symptoms in autistic individuals, such as facing more frequent challenges in social situations, pose significant obstacles in their pursuit of an independent life in adulthood. Although much research has proposed computer-assisted programs (e.g., smartphone apps and VR-based systems), there is a significant lack of systems designed for autistic adults and their independence, and the preferences or characteristics of autistic individuals are not carefully reflected in the design process. Thus, in my dissertation, I focus on two requirements of autistic adults for supporting their independence: (1) an independent and healthy lifestyle and (2) positive social skill practice. These requirements were externalized in two gamified mobile apps (PuzzleWalk and RoutineAid) and two VR-based systems (VISTA and V-DAT). My research aims to design and develop mobile/VR systems and derive design guidelines to support the independence of autistic adults.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616706"}, {"primary_key": "1264684", "vector": [], "sparse_vector": [], "title": "SwarmFidget: Exploring Programmable Actuated Fidgeting with Swarm Robots.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the concept of programmable actuated fidgeting, a type of fidgeting that involves devices integrated with actuators, sensors, and computing to enable a customizable interactive fidgeting experience. In particular, we explore the potential of a swarm of tabletop robots as an instance of programmable actuated fidgeting as robots are becoming increasingly available. Through ideation sessions among researchers and feedback from the participants, we formulate the design space for SwarmFidget, where swarm robots are used to facilitate programmable actuated fidgeting. To gather user impressions, we conducted an exploratory study where we introduced the concept of SwarmFidget to twelve participants and had them experience and provide feedback on six example fidgeting interactions. Our study demonstrates the potential of SwarmFidget for facilitating fidgeting interaction and provides insights and guidelines for designing effective and engaging fidgeting interactions with swarm robots. We believe our work can inspire future research in the area of programmable actuated fidgeting and open up new opportunities for designing novel swarm robot-based fidgeting systems.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606746"}, {"primary_key": "1264685", "vector": [], "sparse_vector": [], "title": "V-DAT (Virtual Reality Data Analysis Tool): Supporting Self-Awareness for Autistic People from Multimodal VR Sensor Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hwajung Hong", "<PERSON><PERSON><PERSON> Han"], "summary": "Virtual reality (VR) has become a valuable tool for social and educational purposes for autistic people, as it provides flexible environmental support to create a variety of experiences. A growing body of recent research has examined the behaviors of autistic people using sensor-based data to better understand autistic people and investigate the effectiveness of VR. Comprehensive analysis of the various signals that can be easily collected in the VR environment can promote understanding of autistic people. While this quantitative evidence has the potential to help both autistic people and others (e.g., autism experts) to understand behaviors of autistic people, existing studies have focused on single signal analysis and have not determined the acceptability of signal analysis results from the autistic person's point of view. To facilitate the use of multiple sensor signals in VR for autistic people and experts, we introduce V-DAT (Virtual Reality Data Analysis Tool), designed to support a VR sensor data handling pipeline. V-DAT takes into account four sensor modalities—head position and rotation, eye movement, audio, and physiological signals—that are actively used in current VR research for autistic people. We explain the characteristics and processing methods of the data for each modality as well as the analysis with comprehensive visualizations of V-DAT. We also conduct a case study to investigate the feasibility of V-DAT as a way of broadening understanding of autistic people from the perspectives of both autistic people and autism experts. Finally, we discuss issues with the process of V-DAT development and complementary measures for the applicability and scalability of a sensor data management system for autistic people.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606797"}, {"primary_key": "1264686", "vector": [], "sparse_vector": [], "title": "STAR: Smartphone-analogous Typing in Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Parastoo Abtahi", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While text entry is an essential and frequent task in Augmented Reality (AR) applications, devising an efficient and easy-to-use text entry method for AR remains an open challenge. This research presents STAR, a smartphone-analogous AR text entry technique that leverages a user's familiarity with smartphone two-thumb typing. With STAR, a user performs thumb typing on a virtual QWERTY keyboard that is overlain on the skin of their hands. During an evaluation study of STAR, participants achieved a mean typing speed of 21.9 WPM (i.e., 56% of their smartphone typing speed), and a mean error rate of 0.3% after 30 minutes of practice. We further analyze the major factors implicated in the performance gap between STAR and smartphone typing, and discuss ways this gap could be narrowed.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606803"}, {"primary_key": "1264687", "vector": [], "sparse_vector": [], "title": "Virtual Rolling Temple: Expanding the Vertical Input Space of a Smart Glasses Touchpad.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart glasses have not favored two-dimensional (2D) GUI. Such a trend may have originated from the limitations of smart glasses in display and input devices. While the display restriction is rapidly being resolved nowadays, 1D GUI is still the majority, indicating that the touch input device is the possible bottleneck. To tackle this issue by expanding the vertical input space of the temple touchpad, we propose the Virtual Rolling Temple (VRT). The concept is to perform 2D gestures by moving the hand in any direction while keep touching the prototype as if the temple rotates. The VRT touchpad is as thin as the spectacles' temples, but it provides the users with input space approximately equivalent to an 80 × 80 mm square touchpad. This is 8 and 13.9 times larger than Google Glass and VUZIX M400, respectively. To validate the concept of the VRT, we constructed three demo scenarios: 2D Pointing, 2D Menu, and 2D Gesture, to cover different types of general 2D input for smart glasses.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615813"}, {"primary_key": "1264688", "vector": [], "sparse_vector": [], "title": "Telextiles: End-to-end Remote Transmission of Fabric Tactile Sensation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The tactile sensation of textiles is critical in determining the comfort of clothing. For remote use, such as online shopping, users cannot physically touch the textile of clothes, making it difficult to evaluate its tactile sensation. Tactile sensing and actuation devices are required to transmit the tactile sensation of textiles. The sensing device needs to recognize different garments, even with hand-held sensors. In addition, the existing actuation device can only present a limited number of known patterns and cannot transmit unknown tactile sensations of textiles. To address these issues, we propose Telextiles, an interface that can remotely transmit tactile sensations of textiles by creating a latent space that reflects the proximity of textiles through contrastive self-supervised learning. We confirm that textiles with similar tactile features are located close to each other in the latent space through a two-dimensional plot. We then compress the latent features for known textile samples into the 1D distance and apply the 16 textile samples to the rollers in the order of the distance. The roller is rotated to select the textile with the closest feature if an unknown textile is detected.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606764"}, {"primary_key": "1264689", "vector": [], "sparse_vector": [], "title": "Telextiles: End-to-end Remote Transmission of Fabric Tactile Sensation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The tactile sensation of textiles is critical in determining the comfort of clothing. In remote scenarios such as online shopping, sensors need to distinguish different garments even with hand-held sensors, and current actuation devices can only present a limited number of known patterns and cannot transmit unknown tactile sensations. We propose Telextiles, an interface for remotely transmitting textile tactile sensations, which uses contrastive self-supervised learning to create a latent space that reflects the relative proximity of textiles. We convert the latent features into a scalar for the one-dimensional structure of the roller. We then select 16 equidistant samples from this line to represent different regions of the latent space. The roller rotates to select the textile with the closest feature. We also show visually the relationship between a textile touched by a remote user and a set of textiles previously registered in our system.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615770"}, {"primary_key": "1264690", "vector": [], "sparse_vector": [], "title": "Snap&apos;N&apos;Go: An Extendable Framework for Evaluating Mechanisms in Spatial Crowdsourcing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given the challenges of evaluating the effect of task allocation mechanisms and their associated incentives on the behavior of participants in spatial crowdsourcing, there is a need for an evaluation framework that simplifies the process of benchmarking such mechanisms with real crowds. In this demo, we present Snap'N'Go, a spatial crowdsourcing application that is modeled as a scavenger-hunt game, which is integrated within a larger modular framework that can be easily extended to evaluate various task allocation mechanisms and incentive models, without affecting the user-experience. Moreover, we present the details of the initial launch of the framework, in which we benchmark multiple mechanisms, and discuss how it can be easily extended for various experimental settings.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615794"}, {"primary_key": "1264691", "vector": [], "sparse_vector": [], "title": "Improving Mobile Reading Experiences While Walking Through Automatic Adaptations and Prompted Customization.", "authors": ["Junhan Kong", "Tianyuan Cai", "<PERSON><PERSON>"], "summary": "Increasingly more people are consuming information on the go, and yet walking can significantly affect the ability to read text documents on mobile devices. In this work, we propose a system that automatically detects when a user is walking while reading on mobile devices to suggest automatic adaptations and recommendations to improve reading experiences. The user can also customize these suggested adaptations in real time, which our system uses to offer future recommendations. We ran a preliminary user study to evaluate our prototype and identify challenges and opportunities of mixed-initiative adaptations for reading on the go.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616666"}, {"primary_key": "1264692", "vector": [], "sparse_vector": [], "title": "Reusing Cardboard for Packaging Boxes with a Computational Design System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a computational design system for packaging boxes, which enables reconstruction of cardboard into packaging structures that precisely fit the size of the objects to be shipped. Given a 3D model of the object and the structure of the cardboard to be reused, our system suggests the optimal net of a new box, which only requires minimum cuts and folds for fabrication by leveraging the edges of the input cardboard. To achieve this, we implemented a GUI visualizing the input models and the optimal net computed by our optimization algorithm. As a demonstration, we showed three design examples, with evaluation results of our method for reducing the length of cuts and folds.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616692"}, {"primary_key": "1264693", "vector": [], "sparse_vector": [], "title": "EChat: An Emotion-Aware Adaptive UI for a Messaging App.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "While online forums provide a convenient platform for people to interact anonymously with others who share similar interests, they have to deal with large amounts of hate speech and inappropriate content, often posted by users in the heat of the moment. This can have a negative impact on the psychological state of other forum users and moderators, who are tasked to identify and delete such content. We investigate a preventative approach to this problem with the design of EChat, a proof-of-concept augmentation to online forums that helps users attend to their emotional state. The user's current emotional state is detected using facial emotion recognition, and the aesthetics of the UI are adapted to reflect this emotion. In case of an emotion with negative valence such as anger or sadness, the UI aesthetic is gradually transitioned to one that evokes a more positive emotion. Semi-structured interviews with EChat users confirm the potential of emotion-aware design to reduce hateful content, and also highlight important design considerations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616698"}, {"primary_key": "1264694", "vector": [], "sparse_vector": [], "title": "LayerShift: Reconfigurable Layer Expression Using Robotic Transparent Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a new robotic display equipped with a transparent display, whose position and posture can be controlled, and a design space that uses it. Robotic displays have been researched in which the display is mounted on a movable robotic arm to extend the expression through motion expression in addition to visual expression. In this paper, we propose combining a transparent display with a robotic display to extend the design space of the robotic display through the characteristics of transparent display. We implement a prototype and propose a design space for a single display and multiple cooperating displays.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615812"}, {"primary_key": "1264695", "vector": [], "sparse_vector": [], "title": "Representing the Timbre of Traditional Musical Instruments Based On Contemporary Instrumental Samples Using DDSP.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Taeyoung Ko", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This project explores the potential of Differentiable Digital Signal Processing (DDSP) to represent and synthesize the timbre of five different notes of the Korean traditional musical instrument, Geomungo, using digital instrumental samples of the bass guitar, which has a similar mechanism to produce the sound. To evaluate the feasibility and quality of the digital recreation process, we compared hand-played Geomungo audio samples with digitally recreated audio samples using DDSP. The MFCC, spectral contrast, chroma features, and raw signal comparison, were used for assessment. Our findings show the possibility of applying DDSP to represent and synthesize the nuances of pitch and dynamics for expressive aspects of Geomungo's five different notes effectively. We also propose three audio features that can be used to evaluate the results quantitatively under the context of neural sound synthesis.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616678"}, {"primary_key": "1264696", "vector": [], "sparse_vector": [], "title": "TacNote: Tactile and Audio Note-Taking for Non-Visual Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blind and visually impaired (BVI) people primarily rely on non-visual senses to interact with a physical environment. Doing so requires a high cognitive load to perceive and memorize the presence of a large set of objects, such as at home or in a learning setting. In this work, we explored opportunities to enable object-centric note-taking by using a 3D printing pen for interactive, personalized tactile annotations. We first identified the benefits and challenges of self-created tactile graphics in a formative diary study. Then, we developed TacNote, a system that enables BVI users to annotate, explore, and memorize critical information associated with everyday objects. Using TacNote, the users create tactile graphics with a 3D printing pen and attach them to the target objects. They capture and organize the physical labels by using TacNote's camera-based mobile app. In addition, they can specify locations, ordering, and hierarchy via finger-pointing interaction and receive audio feedback. Our user study with ten BVI participants showed that TacNote effectively alleviated the memory burden, offering a promising solution for enhancing users' access to information.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606784"}, {"primary_key": "1264697", "vector": [], "sparse_vector": [], "title": "An Interactive System for Drawing Cars in Perspective.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Taegyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel interactive system for drawing cars in perspective. Our harmonious set of pen and touch interactions based on traditional tools and techniques can help car designers naturally transition from 2D sketching to 3D sketching and allow them to sketch cars in 3D intuitively and iteratively. The pilot test shows that our system is easy to learn and use.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616701"}, {"primary_key": "1264698", "vector": [], "sparse_vector": [], "title": "Stereoscopic Viewing and Monoscopic Touching: Selecting Distant Objects in VR Through a Mobile Device.", "authors": ["<PERSON><PERSON>", "Taegyu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this study, we explore a new way to complementarily utilize the immersive visual output of VR and the physical haptic input of a smartphone. In particular, we focus on interacting with distant virtual objects using a smartphone in a through-plane manner and present a novel selection technique that overcomes the binocular parallax that occurs in such an arrangement. In our proposed technique, when a user in the stereoscopic viewing mode needs to perform a distant selection, the user brings the fingertip near the screen of the mobile device, triggering a smoothly animated transition to the monoscopic touching mode. Using a novel proof-of-concept implementation that utilizes a transparent acrylic panel, we conducted a user study and found that the proposed technique is significantly quicker, more precise, more direct, and more intuitive compared to the ray casting baseline. Subsequently, we created VR applications that explore the rich and interesting use cases of the proposed technique.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606809"}, {"primary_key": "1264699", "vector": [], "sparse_vector": [], "title": "Towards Designing a Context-Aware Multimodal Voice Assistant for Pronoun Disambiguation: A Demonstration of GazePointAR.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Voice assistants (VAs) like <PERSON><PERSON> and <PERSON><PERSON> have transformed how humans interact with technology; however, their inability to consider a user's spatiotemporal context, such as surrounding objects, drammatically limits natural dialogue. In this demo paper, we introduce GazePointAR, a wearable augmented reality (AR) system that resolves ambiguity in speech queries using eye gaze, pointing gesture, and conversation history. With GazePointAR, a user can ask \"what's over there?\" or \"how do I solve this math problem?\" simply by looking and/or pointing. We describe GazePointAR's design and highlight supported use cases.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615819"}, {"primary_key": "1264700", "vector": [], "sparse_vector": [], "title": "Electro-actuated Materials for Future Haptic Interfaces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Electro-actuated materials (EAMs) have received wide attention within material science and soft robotics for their ability to dynamically change physical properties, such as shape and stiffness, in response to electrical stimuli. While researchers have begun exploring the haptic characteristics of EAMs, their integration into Human-Computer Interaction (HCI) shows challenges, including limited commercial availability and a lack of interdisciplinary knowledge exchange. This workshop specifically focuses on electrostatic (ES), soft electrohydraulic (SEH), and electroosmotic (EO) actuators. By bringing together researchers in the field, we aim to facilitate the exchange of findings, techniques, fabrication practices, and tacit knowledge within the HCI community. The workshop combines interactive demos, focused discussions, and hands-on ideation, providing a platform to explore the haptic potential of EAMs, identify key challenges and opportunities, and envision how these programmable materials can unlock new haptic interactions and interfaces.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3617434"}, {"primary_key": "1264701", "vector": [], "sparse_vector": [], "title": "Beyond the Artifact: Power as a Lens for Creativity Support Tools.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> O&apos;<PERSON>", "<PERSON>"], "summary": "Researchers who build creativity support tools (CSTs) define abstractions and software representations that align with user needs to give users the power to accomplish tasks. However, these specifications also structure and limit how users can and should think, act, and express themselves. Thus, tool designers unavoidably exert power over their users by enacting a \"normative ground\" through their tools. Drawing on interviews with 11 creative practitioners, tool designers, and CST researchers, we offer a definition of empowerment in the context of creative practice, build a preliminary theory of how power relationships manifest in CSTs, and explain why researchers have had trouble addressing these concepts in the past. We re-examine CST literature through a lens of power and argue that mitigating power imbalances at the level of technical design requires enabling users in both vertical movement along levels of abstraction as well as horizontal movement between tools through interoperable representations. A lens of power is one possible orientation that lets us recognize the methodological shifts required towards building \"artistic support tools.\"", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606831"}, {"primary_key": "1264702", "vector": [], "sparse_vector": [], "title": "Using LLMs to Customize the UI of Webpages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "LLMs have capabilities to understand natural language and code, which makes them a great candidate for user-driven customization of webpages. A process that focuses on natural language can be useful for those who are less technologically literate. In this paper, we explore the potential of using LLMs to modify webpages, and what kinds of opportunities and challenges that come with it. We observe that specific prompts referring to color or targeted components can succeed, vague requests and any complex website tend to perform poorly.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616671"}, {"primary_key": "1264703", "vector": [], "sparse_vector": [], "title": "AwakenFlora: Exploring Proactive Smell Experience in Virtual Reality through Mid-Air Gestures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hebo Gong", "<PERSON><PERSON><PERSON>"], "summary": "We explored mid-air gestural interactions for proactive smell experiences in Virtual Reality (VR). With a wearable scent-delivery device, a set of gestures that interact with virtual objects are mapped to corresponding olfactory tasks, i.e., scent release, scent intensity adjustment, and scent switch. We conducted a user study for preliminary validation, revealing significant advantages of gestural interactions over the traditional handle controller for scent release. Our findings demonstrate the potential of gestural interactions in enhancing proactive smell experiences in VR and contribute insights into how proactive gestural input can benefit engaging olfactory experiences in virtual environments.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616667"}, {"primary_key": "1264704", "vector": [], "sparse_vector": [], "title": "BrushLens: Hardware Interaction Proxies for Accessible Touchscreen Interface Actuation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Touchscreen devices, designed with an assumed range of user abilities and interaction patterns, often present challenges for individuals with diverse abilities to operate independently. Prior efforts to improve accessibility through tools or algorithms necessitated alterations to touchscreen hardware or software, making them inapplicable for the large number of existing legacy devices. In this paper, we introduce BrushLens, a hardware interaction proxy that performs physical interactions on behalf of users while allowing them to continue utilizing accessible interfaces, such as screenreaders and assistive touch on smartphones, for interface exploration and command input. BrushLens maintains an interface model for accurate target localization and utilizes exchangeable actuators for physical actuation across a variety of device types, effectively reducing user workload and minimizing the risk of mistouch. Our evaluations reveal that BrushLens lowers the mistouch rate and empowers visually and motor impaired users to interact with otherwise inaccessible physical touchscreens more effectively.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606730"}, {"primary_key": "1264705", "vector": [], "sparse_vector": [], "title": "ShadowTouch: Enabling Free-Form Touch-Based Hand-to-Surface Interaction with Wrist-Mounted Illuminant by Shadow Projection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chi Hsia", "<PERSON><PERSON> Fan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present ShadowTouch, a novel sensing method to recognize the subtle hand-to-surface touch state for independent fingers based on optical auxiliary. ShadowTouch mounts a forward-facing light source on the user's wrist to construct shadows on the surface in front of the fingers when the corresponding fingers are close to the surface. With such an optical design, the subtle vertical movements of near-surface fingers are magnified and turned to shadow features cast on the surface, which are recognizable for computer vision algorithms. To efficiently recognize the touch state of each finger, we devised a two-stage CNN-based algorithm that first extracted all the fingertip regions from each frame and then classified the touch state of each region from the cropped consecutive frames. Evaluations showed our touch state detection algorithm achieved a recognition accuracy of 99.1% and an F-1 score of 96.8% in the leave-one-out cross-user evaluation setting. We further outlined the hand-to-surface interaction space enabled by ShadowTouch's sensing capability from the aspects of touch-based interaction, stroke-based interaction, and out-of-surface information and developed four application prototypes to showcase ShadowTouch's interaction potential. The usability evaluation study showed the advantages of ShadowTouch over threshold-based techniques in aspects of lower mental demand, lower effort, lower frustration, more willing to use, easier to use, better integrity, and higher confidence.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606785"}, {"primary_key": "1264706", "vector": [], "sparse_vector": [], "title": "Soundify: Matching Sound Effects to Video.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the art of video editing, sound helps add character to an object and immerse the viewer within a space. Through formative interviews with professional editors (N=10), we found that the task of adding sounds to video can be challenging. This paper presents Soundify, a system that assists editors in matching sounds to video. Given a video, Soundify identifies matching sounds, synchronizes the sounds to the video, and dynamically adjusts panning and volume to create spatial audio. In a human evaluation study (N=889), we show that Soundify is capable of matching sounds to video out-of-the-box for a diverse range of audio categories. In a within-subjects expert study (N=12), we demonstrate the usefulness of Soundify in helping video editors match sounds to video with lighter workload, reduced task completion time, and improved usability.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606823"}, {"primary_key": "1264707", "vector": [], "sparse_vector": [], "title": "Relay: A collaborative UI model for design handoff.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The design handoff process refers to the stage in the user interface (UI) design process where a designer gives their finished design to a developer for implementation. However, design decisions are lost when developers struggle to interpret and implement the designer's original intent. To address this problem, we built a system called Relay that utilizes concrete UI models to capture design intent. To our knowledge, Relay is the first system described in the academic literature to take artifacts from an existing design tool and generate a UI model.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616624"}, {"primary_key": "1264708", "vector": [], "sparse_vector": [], "title": "Palette-PrintAR: an augmented reality fluidic design tool for multicolor resin 3D printing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "poster Share on Palette-PrintAR: an augmented reality fluidic design tool for multicolor resin 3D printing Authors: <AUTHORS>", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616684"}, {"primary_key": "1264709", "vector": [], "sparse_vector": [], "title": "Riffle: Reactive Relational State for Local-First Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The reactive paradigm for developing user interfaces promises both simplicity and scalability, but existing frameworks usually compromise one for the other. We present Riffle, a reactive state management system that achieves both simplicity and scalability by managing the entire state of a web application in a client-side persistent relational database. Data transformations over the application state are defined in a graph of reactive relational queries, providing developers with a simple spreadsheet-like reactivity model. Domain state and UI state are unified within the same system, and efficient incremental query maintenance ensures the UI remains responsive. We present a formative case study of using Riffle to build a music management application with complex data and stringent performance requirements.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606801"}, {"primary_key": "1264710", "vector": [], "sparse_vector": [], "title": "RadarVR: Exploring Spatiotemporal Visual Guidance in Cinematic VR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In cinematic VR, viewers can only see a limited portion of the scene at any time. As a result, they may miss important events outside their field of view. While there are many techniques which offer spatial guidance (where to look), there has been little work on temporal guidance (when to look). Temporal guidance offers viewers a look-ahead time and allows viewers to plan their head motion for important events. This paper introduces spatiotemporal visual guidance and presents a new widget, RadarVR, which shows both spatial and temporal information of regions of interest (ROIs) in a video. Using RadarVR, we conducted a study to investigate the impact of temporal guidance and explore trade-offs between spatiotemporal and spatial-only visual guidance. Results show spatiotemporal feedback allows users to see a greater percentage of ROIs, with 81% more seen from their initial onset. We discuss design implications for future work in this space.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606734"}, {"primary_key": "1264711", "vector": [], "sparse_vector": [], "title": "Experiencing Visual Captions: Augmented Communication with Real-time Visuals using Large Language Models.", "authors": ["<PERSON>ng<PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xiang &apos;Anthony&apos; Chen", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We demonstrate Visual Captions, a real-time system that integrates with a video conferencing platform to enrich verbal communication. Visual Captions leverages a fine-tuned large language model to proactively suggest visuals that are relevant to the context of the ongoing conversation. We implemented Visual Captions as a user-customizable Chrome plugin with three levels of AI proactivity: Auto-display (AI autonomously adds visuals), Auto-suggest (AI proactively recommends visuals), and On-demand-suggest (AI suggests visuals when prompted). We showcase the usage of Visual Captions in open-vocabulary settings, and how the addition of visuals based on the context of conversations could improve comprehension of complex or unfamiliar concepts. In addition, we demonstrate three approaches people can interact with the system with different levels of AI proactivity. Visual Captions is open-sourced at https://github.com/google/archat.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615978"}, {"primary_key": "1264712", "vector": [], "sparse_vector": [], "title": "PrintedCircuit Board (PCB) Probe Tester (PCBPT) - a Compact Desktop Systemthat Helps with Automatic PCBDebugging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "PCB debugging can be tricky. For example, if we want to use an oscilloscope to measure signals of interest in the PCB, we need to locate them in the schematic and select the appropriate pad for each signal on the PCB layout on which to put the oscilloscope probes. This process hence requires frequent switching between the schematics and the PCB layouts. Moreover, our hands may not be precise and stable enough to accurately place the probes on the pads without causing short circuits with adjacent pins, which can lead to further issues. Additionally, if multiple signals need to be tested, two hands will not be enough. Probe hook clips can be used, but this often necessitates the use of extension wires that must be soldered onto the targeted pads. To streamline the debugging process, we introduce the PCBPT (PCB Probing Tester). This innovative solution seamlessly bridges from schematic to test equipment by using a robotic probe and actuated board holder. By selecting signals of interest directly from a GUI, users can instantly monitor the output on an oscilloscope, significantly improving the effectiveness of the debugging process.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615800"}, {"primary_key": "1264713", "vector": [], "sparse_vector": [], "title": "Augmented Photogrammetry: 3D Object Scanning and Appearance Editing in Mobile Augmented Reality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel approach, Augmented Photogrammetry, for scanning and editing the appearance of physical objects in augmented reality (AR). Our work provides a user-friendly and efficient technique for enabling customizable appearance modifications in real time on arbitrary objects scanned from a user's physical environment. We accomplish this by integrating Structure from Motion (SfM), instance segmentation, and machine learning into a unified pipeline. Our streamlined process enables users to easily select a physical object and specify its desired appearance. We believe our mobile AR approach holds promise for applications in interior design, virtual prototyping, and content creation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616638"}, {"primary_key": "1264714", "vector": [], "sparse_vector": [], "title": "ecoEDA: Recycling E-waste During Electronics Design.", "authors": ["<PERSON>", "Beza Desta", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The amount of e-waste generated by discarding devices is enormous but options for recycling remain limited. However, inside a discarded device (from consumer devices to one's own prototypes), an electronics designer could find dozens to thousands of reusable components, including microcontrollers, sensors, voltage regulators, etc. Despite this, existing electronic design tools assume users will buy all components anew. To tackle this, we propose ecoEDA, an interactive tool that enables electronics designers to explore recycling electronic components during the design process. We accomplish this via (1) creating suggestions to assist users in identifying and designing with recycled components; and (2) maintaining a library of useful data relevant to reuse (e.g., allowing users to find which devices contain which components). Through example use-cases, we demonstrate how our tool can enable various pathways to recycling e-waste. To evaluate it, we conducted a user study where participants used our tool to create an electronic schematic with components from torn-down e-waste devices. We found that participants' designs made with ecoEDA featured an average of 66% of recycled components. Last, we reflect on challenges and opportunities for building software that promotes e-waste reuse.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606745"}, {"primary_key": "1264715", "vector": [], "sparse_vector": [], "title": "Sustainflatable: Harvesting, Storing and Utilizing Ambient Energy for Pneumatic Morphing Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While the majority of pneumatic interfaces are powered and controlled by traditional electric pumps and valves, alternative sustainable energy-harnessing technology has been attracting attention. This paper presents a novel solution to this challenge with the development of the Sustainflatable system, a self-sustaining pneumatic system that can harvest renewable energy sources such as wind, water flow, moisture, and sunlight, convert the energy into compressed air, and store it for later use in a programmable and intelligent way. The system is completely electronic-free, incorporating customized energy harvesting pumps, storage units with variable volume-pressure characteristics, and tailored valves that operate autonomously. Additionally, the paper provides a design tool to guide the development of the system and includes several environmental applications to showcase its capabilities.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606721"}, {"primary_key": "1264716", "vector": [], "sparse_vector": [], "title": "Demonstrating Sustainflatable: Harvesting, Storing and Utilizing Ambient Energy for Pneumatic Morphing Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While the majority of pneumatic interfaces are powered and controlled by traditional electric pumps and valves, alternative sustainable energy harnessing technology has been attracting attention. This paper presents a novel solution to this challenge with the development of the Sustainflatable system, a self-sustaining pneumatic system that can harvest renewable energy sources such as wind, waterflow, moisture, and sunlight, convert the energy into compressed air, and store it for later use in a programmable and intelligent way. The system is completely electronic-free, incorporating customized energy harvesting pumps, storage units with variable volume-pressure characteristics, and tailored valves that operate autonomously. Additionally, the paper provides a design tool to guide the development of the system and includes several environmental applications to showcase its capabilities.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615765"}, {"primary_key": "1264717", "vector": [], "sparse_vector": [], "title": "MagKnitic: Machine-knitted Passive and Interactive Haptic Textiles with Integrated Binary Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce MagKnitic, a novel approach to integrate passive force feedback and binary sensing into fabrics via digital machine knitting. Our approach utilizes digital fabrication technology to enable haptic interfaces that are soft, flexible, lightweight, and conform to the user's body shape. Despite these characteristics, our interfaces provide diverse, interactive, and responsive force feedback, expanding the design space for haptic experiences.MagKnitic provides scalable and customizable passive haptic sensations by utilizing the attractive force between ferromagnetic yarns and permanent magnets, both of which are seamlessly integrated into knitted fabrics. Moreover, we present a binary sensing capability based on the resistance drop resulting from the activated electrical path between the integrated magnets and ferromagnetic yarn upon direct contact. We offer parametric design templates for users to customize MagKnitic layouts and patterns. With various design layouts and combinations, MagKnitic supports passive haptics interactions of linear, polar, angular, planar, radial, and user-defined motions. We perform a technical evaluation of the passive force feedback and the binary sensing capabilities with different machine knitting layouts and patterns, embedded magnet sizes, and interaction distances. In addition, we conduct two user studies to validate the effectiveness of MagKnitic. Finally, we demonstrate various application scenarios, including wearable input interfaces, game controllers, passive VR/AR wearables, and interactive furniture coverings.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606765"}, {"primary_key": "1264718", "vector": [], "sparse_vector": [], "title": "Intelligent Textiles for Physical Human-Environment Interactions.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Physical human-environment interaction is a fundamental aspect of our daily lives, involving the constant use of our sensory and motor systems to extract, process, and communicate information. Capturing, modeling, and augmenting these physical interactions are crucial for enhancing human well-being and promoting intelligent system designs. However, the pervasive and diverse nature of these interactions poses challenges that require scalable and adaptable systems. To address these challenges, I adopt an integrated approach that combines digital fabrication and machine learning techniques. The approach involves developing a digital design and fabrication pipeline to integrate sensing and actuation capabilities into textile-based platforms, and capturing diverse datasets on human-environment interactions to enable intelligent and adaptive applications. The dissertation showcases past and ongoing works on intelligent textile-based sensing and actuating platforms that embody this approach, including tactile sensing garments, an intelligent carpet for human pose estimation, programmable textile-based actuators for assistive wearables, and smart gloves for adaptive tactile interaction transfer. Moving forward, I aim to explore applications of the developed systems in healthcare, robotics, and human behaviors intervention, and expand to diverse sensing and actuation modalities.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616704"}, {"primary_key": "1264719", "vector": [], "sparse_vector": [], "title": "Exploring Locomotion Methods with Upright Redirected Views for VR Users in Reclining &amp; Lying Positions.", "authors": ["<PERSON><PERSON><PERSON>", "Chenyang Cai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhigeng Pan", "<PERSON><PERSON>", "<PERSON>"], "summary": "Using VR in reclining & lying positions is getting common for users, but upward views caused by posture have to be redirected to be parallel to the ground as when users are standing. This affects users' locomotion performances in VR due to potential physical restrictions, and the visual-vestibular-proprioceptive conflict. This paper is among the first to investigate the suited locomotion methods and how reclining & lying positions and redirection affect them in such conditions. A user-elicitation study was carried out to construct a set of locomotion methods based on users' preferences when they were in different reclining & lying positions. A second study developed user-preferred 'tapping' and 'chair rotating' gestures, by evaluating their performances at various body reclining angles, we measured the general impacts of posture and redirection. The results showed that these methods worked effectively, but exposed some shortcomings, and users performed worst at 45° reclining angles. Finally, four upgraded methods were designed and verified to improve the locomotion performances.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606714"}, {"primary_key": "1264720", "vector": [], "sparse_vector": [], "title": "Sketching Proteins with Bare Hands in VR.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Taegyu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent developments in AI have made it possible to design new proteins that are crucial to meeting humanity's needs. However, tools for exploring the 3D structures of proteins in the early stages of AI-based protein design are lacking, leading to many preventable trials and errors and much wasted time and efforts in the design process. To address this, we propose a novel VR interaction system that enables synthetic biologists to intuitively author the 3D structures of proteins with their bare hands.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616675"}, {"primary_key": "1264721", "vector": [], "sparse_vector": [], "title": "SketchingRelatedWork: Finding and Organizing Papers through Inking a Node-Link Diagram.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Taegyu <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Writing an academic paper requires significant time and effort to find, read, and organize many related papers, which is a complex knowledge task. We present a novel interactive system that allows users to perform these tasks quickly and easily on the 2D canvas with pen and multitouch inputs. Our system turns users' sketches and handwriting into a node-link diagram of papers and citations that users can iteratively expand in situ toward constructing a coherent narrative when writing Related Work sections.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616685"}, {"primary_key": "1264722", "vector": [], "sparse_vector": [], "title": "ProactiveAgent: Personalized Context-Aware Reminder System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce ProactiveAgent, a proactive application that harnesses the capabilities of large language models (LLMs) and personal agents to provide context-aware, personalized reminders and suggestions. By assimilating real-time environmental data, user histories, and verbal interactions, the system discerns user intent and offers tailored recommendations. The application captures visual activity and spoken interactions, integrating them into short and long-term memory storage for context-rich decision support. We propose scenarios where ProactiveAgent could be valuable: suggesting snack options depending on the time, offering culinary options based on dietary preferences, and even guiding users in their daily tasks. In envisioned use cases, ProactiveAgent could potentially track user attributes during their shopping experience, such as time spent on items and other cues, leading towards insightful product recommendations. Our work represents a potential advancement in the realm of personalized assistance, merging LLM strengths with personal agent technologies to enhance user decision-making in dynamic real-world scenarios.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625115"}, {"primary_key": "1264723", "vector": [], "sparse_vector": [], "title": "Automated Conversion of Music Videos into Lyric Videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hijung Valentina Shin", "<PERSON><PERSON><PERSON>"], "summary": "Musicians and fans often produce lyric videos, a form of music videos that showcase the song's lyrics, for their favorite songs. However, making such videos can be challenging and time-consuming as the lyrics need to be added in synchrony and visual harmony with the video. Informed by prior work and close examination of existing lyric videos, we propose a set of design guidelines to help creators make such videos. Our guidelines ensure the readability of the lyric text while maintaining a unified focus of attention. We instantiate these guidelines in a fully automated pipeline that converts an input music video into a lyric video. We demonstrate the robustness of our pipeline by generating lyric videos from a diverse range of input sources. A user study shows that lyric videos generated by our pipeline are effective in maintaining text readability and unifying the focus of attention.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606757"}, {"primary_key": "1264724", "vector": [], "sparse_vector": [], "title": "Sonic Storyteller: Augmenting Oral Storytelling with Spatial Sound Effects.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Traditional oral storytelling has been predominantly limited to a live audience, augmented with voice acting, sound effects, and gestures. With new technologies, the reach of oral storytelling has expanded to allow remote listeners to tune in to radio programs and more recently to listen to podcasts and audio books, asynchronously. Digital tools offer easy-to-use applications that enable anyone the ability to not only narrate a story, but also to augment it in different ways, from adding sound effects in podcasts to narrating audio books with voice effects. In this work, we present a proof-of-concept system that allows oral storytellers to augment their storytelling with spatial sound effects. We present results from a preliminary study (n=12) that found the spatial sound effects to be vivid and immersive, indicating potential for semi-automated ways of augmenting oral storytelling.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616642"}, {"primary_key": "1264725", "vector": [], "sparse_vector": [], "title": "Statslator: Interactive Translation of NHST and Estimation Statistics Reporting Styles in Scientific Documents.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Inferential statistics are typically reported using p-values (NHST) or confidence intervals on effect sizes (estimation). This is done using a range of styles, but some readers have preferences about how statistics should be presented and others have limited familiarity with alternatives. We propose a system to interactively translate statistical reporting styles in existing documents, allowing readers to switch between interval estimates, p-values, and standardized effect sizes, all using textual and graphical reports that are dynamic and user customizable. Forty years of CHI papers are examined. Using only the information reported in scientific documents, equations are derived and validated on simulated datasets to show that conversions between p-values and confidence intervals are accurate. The system helps readers interpret statistics in a familiar style, compare reports that use different styles, and even validate the correctness of reports. Code and data: https://osf.io/x4ue7", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606762"}, {"primary_key": "1264726", "vector": [], "sparse_vector": [], "title": "ThermalRouter: Enabling Users to Design Thermally-Sound Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Shan-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Users often 3D model enclosures that interact with significant heat sources, such as electronics or appliances that generate heat (e.g., CPU, motor, lamps, etc.). While parts made by users might function well aesthetically or structurally, they are rarely thermally-sound. This happens because heat transfer is non-intuitive; thus, engineering thermal solutions is not straightforward. To tackle this, we developed ThermalRouter, a CAD plugin that assists with improving the thermal performance of their models. ThermalRouter automatically converts regions of the model to be made from thermally-conductive materials (such as nylon or metallic-silicone). These regions act as heat channels, branching away from hotspots to dissipate heat. The key is that ThermalRouter automatically simulates the thermal performance of many possible heat channel configurations and presents the user with the most thermally-sound design (e.g., lowest temperature). Furthermore, it allows users to customize by balancing costs, indicating non-modifiable geometry, etc. Most importantly, ThermalRouter achieves this without requiring manual labor to set up or parse the results of complex thermal simulations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606747"}, {"primary_key": "1264727", "vector": [], "sparse_vector": [], "title": "Demonstration of Masonview: Content-Driven Viewport Management.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The web is now rich with information and functionality, allowing users to only use portions of webpages to complete their tasks. However, current desktop interfaces only allow us to manage entire windows, leading to inefficient use of screen space and suboptimal workflows. We present Masonview, a content-driven viewport management system that provides mechanisms to detach desired elements from their webpages as viewports and compose these views into UI mashups with viewplates. We demonstrate how these mechanisms enable more free-form organization and management of content.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615827"}, {"primary_key": "1264728", "vector": [], "sparse_vector": [], "title": "Odyssey: An Interactive Workbench for Expert-Driven Floating-Point Expression Rewriting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, researchers have proposed a number of automated tools to identify and improve floating-point rounding error in mathematical expressions. However, users struggle to effectively apply these tools. In this paper, we work with novices, experts, and tool developers to investigate user needs during the expression rewriting process. We find that users follow an iterative design process. They want to compare expressions on multiple input ranges, integrate and guide various rewriting tools, and understand where errors come from. We organize this investigation's results into a three-stage workflow and implement that workflow in a new, extensible workbench dubbed Odyssey. Odyssey enables users to: (1) diagnose problems in an expression, (2) generate solutions automatically or by hand, and (3) tune their results. Odyssey tracks a working set of expressions and turns a state-of-the-art automated tool \"inside out,\" giving the user access to internal heuristics, algorithms, and functionality. In a user study, Odyssey enabled five expert numerical analysts to solve challenging rewriting problems where state-of-the-art automated tools fail. In particular, the experts unanimously praised <PERSON>'s novel support for interactive range modification and local error visualization.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606819"}, {"primary_key": "1264729", "vector": [], "sparse_vector": [], "title": "Electric Salt: Tableware Design for Enhancing Taste of Low-Salt Foods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This study focused on an optimal tableware design using electro-taste technology to enhance saltiness. Based on various design and usability considerations, we created prototypes and conducted interviews with users to obtain suggestions for the optimal design, and identified the requirements for designing electro-taste tableware.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616626"}, {"primary_key": "1264730", "vector": [], "sparse_vector": [], "title": "AGI is Coming... Is HCI Ready?", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3624510"}, {"primary_key": "1264731", "vector": [], "sparse_vector": [], "title": "TactTongue: Prototyping ElectroTactile Stimulations on the Tongue.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The tongue is a remarkable human organ with a high concentration of taste receptors and an exceptional ability to sense touch. This work uses electro-tactile stimulation to explore the intricate interplay between tactile perception and taste rendering on the tongue. To facilitate this exploration, we utilized a flexible, high-resolution electro-tactile prototyping platform that can be administered in the mouth. We have created a design tool that abstracts users from the low-level stimulation parameters, enabling them to focus on higher-level design objectives. Through this platform, we present the results of three studies. Our first study evaluates the design tool's qualitative and formative aspects. In contrast, the second study measures the qualitative attributes of the sensations produced by our device, including tactile sensations and taste. In the third study, we demonstrate the ability of our device to sense touch input through the tongue when placed on the hard palate region in the mouth. Finally, we present a range of application demonstrators that span diverse domains, including accessibility, medical surgeries, and extended reality. These demonstrators showcase the versatility and potential of our platform, highlighting its ability to enable researchers and practitioners to explore new ways of leveraging the tongue's unique capabilities. Overall, this work presents new opportunities to deploy tongue interfaces and has broad implications for designing interfaces that incorporate the tongue as a sensory organ.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606829"}, {"primary_key": "1264732", "vector": [], "sparse_vector": [], "title": "iKnowde: Interactive Learning Path Generation System Based on Knowledge Dependency Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> Sugita", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hidet<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a system that addresses the challenges faced by novice learners by identifying and tracking their learning topics and current knowledge status, and providing a suitable, dynamically updated learning path. The system represents the dependencies between learning objects using a directed graph, and utilizes a binary questionnaire interface to continuously determine and update the user's learning topics and knowledge status. This system enables users to clearly understand where they are in their learning process and what they should learn next, thereby improving learning efficiency, motivation, and self-efficiency. We conducted an experiment involving 9 participants, and our results implied that the proposed system is beneficial for beginners, particularly in reducing learners' cognitive load and enhancing their motivation and self-efficacy.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616628"}, {"primary_key": "1264733", "vector": [], "sparse_vector": [], "title": "4-Frame Manga Drawing Support System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This project proposes a 4-frame manga drawing support system that assists users in creating drawings. The proposed system recognizes each frame of an unfinished manga drawn by a user and proposes successive frames, considering the content recognized till then, e.g., the storyline, frame composition, and punchline. The system updates the proposal as the manga drawing proceeds. The proposed system comprises four modules for 1) recognizing the user's drawings, 2) generating four sentences that describe the storyline, 3) generating images, each of which corresponds to a manga frame from the above sentences, and 4) choosing the user-preferred manga candidate from a number of potential choices. The proposed system does not require AI to generate the 4-frame manga; instead, the user draws the 4-frame manga with the help of the system. In other words, the user decides whether to accept or reject the AI's proposal.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625218"}, {"primary_key": "1264734", "vector": [], "sparse_vector": [], "title": "Using Personal Situated Analytics (PSA) to Interpret Recorded Meetings.", "authors": ["Ash<PERSON><PERSON> G<PERSON>", "<PERSON>"], "summary": "Applications in immersive environments have gained popularity for training, learning, and recreational tasks. Due to the increasing availability of sensors and data-capturing devices, research is extending the use of immersive environments to support visual analytics processes, including sensemaking and strategic immersion for interaction and task completion. In this work, we propose Personal Situated Analytics (PSA) framework to embed users into recorded meetings with support for multiple degrees of immersion in the Reality-Virtuality spectrum. Our proposed framework encompasses various stages such as tracking, data capturing, data cleaning, data synchronization, prototype building, and deploying the final product to end-user hardware. We evaluate this framework on a data analysis scenario between human subjects and a conversational AI agent. Our pilot study (n=12) using this framework compares user experiences when using two different devices: Hololens2 and Quest2.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616697"}, {"primary_key": "1264735", "vector": [], "sparse_vector": [], "title": "Going Incognito in the Metaverse: Achieving Theoretically Optimal Privacy-Usability Tradeoffs in VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual reality (VR) telepresence applications and the so-called \"metaverse\" promise to be the next major medium of human-computer interaction. However, with recent studies demonstrating the ease at which VR users can be profiled and deanonymized, metaverse platforms carry many of the privacy risks of the conventional internet (and more) while at present offering few of the defensive utilities that users are accustomed to having access to. To remedy this, we present the first known method of implementing an \"incognito mode\" for VR. Our technique leverages local ε-differential privacy to quantifiably obscure sensitive user data attributes, with a focus on intelligently adding noise when and where it is needed most to maximize privacy while minimizing usability impact. Our system is capable of flexibly adapting to the unique needs of each VR application to further optimize this trade-off. We implement our solution as a universal Unity (C#) plugin that we then evaluate using several popular VR applications. Upon faithfully replicating the most well-known VR privacy attack studies, we show a significant degradation of attacker capabilities when using our solution.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606754"}, {"primary_key": "1264736", "vector": [], "sparse_vector": [], "title": "ModBand: Design of a Modular Headband for Multimodal Data Collection and Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Collecting multimodal user data during physical tasks such as cooking, maintenance, or physical rehab is crucial to enable the design of better AI models, interfaces, and applications. However, this is a challenging task with external cameras and sensors due to user movement, self-occlusions and diversity of data streams during task performance. In this work, we present ModBand, a wearable sensor headband with an accompanying software pipeline to collect and visualize data such as facial images, pupillometry, egocentric video, and heart rate during physical task performance. ModBand can be modified, extended, and used both as a standalone device or integrated with existing head-mounted AR devices for AI-based task guidance. Our modular design incorporates cost-effective fabrication methods, such as 3D printing, and enables convenient integration or exclusion of sensors to support custom data collection needs.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616682"}, {"primary_key": "1264737", "vector": [], "sparse_vector": [], "title": "Biohybrid Devices: Prototyping Interactive Devices with Growable Materials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Living bio-materials are increasingly used in HCI for fabricating objects by growing. However, how to integrate electronics to make these objects interactive still needs to be clarified. This paper presents an exploration of the fabrication design space of Biohybrid Interactive Devices, a class of interactive devices fabricated by merging electronic components and living organisms. From the exploration of this space using bacterial cellulose, we outline a fabrication framework centered on the biomaterials' life cycle phases. We introduce a set of novel fabrication techniques for embedding conductive elements, sensors, and output components through biological (e.g. bio-fabrication and bio-assembling) and digital processes. We demonstrate the combinatory aspect of the framework by realizing three tangible, wearable, and shape-changing interfaces. Finally, we discuss the sustainability of our approach, its limitations, and the implications for bio-hybrid systems in HCI.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606774"}, {"primary_key": "1264738", "vector": [], "sparse_vector": [], "title": "On-the-fly Editing of Emoji Elements for Mobile Messaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This poster presents an on-the-fly emoji editing method to extend the range of emotional expressions of off-the-shelf emojis when typing a message on a mobile. The proposed system allows for the quick editing of accessory elements of facial emojis via simple swipe or flick gestures to reflect the user's emotional intensity.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616630"}, {"primary_key": "1264739", "vector": [], "sparse_vector": [], "title": "Visualizing Spacecraft Magnetic Fields on the Web and in VR.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spacecraft magnetic fields are very complex in nature, and they must be understood and minimized, as they can mask or even mimic signals of interest. Current approaches simulate the 3D nature of the magnetic field and then visualize it in 2D images. However, limited 2D views hide much of the information in the complex spacecraft magnetic field. We describe a prototype system that allows for both an interactive 3D web experience as well as an immersive virtual reality (VR) experience to view and manipulate the 3D spacecraft magnetic field, allowing engineers and scientists to trace field lines, real-time in 3D. A preliminary user study validates the usefulness of the tool and guides further development.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616618"}, {"primary_key": "1264740", "vector": [], "sparse_vector": [], "title": "Physical-Digital Programming.", "authors": ["<PERSON> O&apos;<PERSON>"], "summary": "In exploratory digital fabrication, artists, scientists, and engineers use machines and code to prototype object forms and ways of making that do not currently exist. However, prior research has yet to provide expressive agency for makers doing this work, which includes rapid experimentation, safe adjustments and detailed, accurate control of machines and data. To address this, I frame exploratory fabrication as physical-digital programming: handling physical contingencies within core features of a programming language. I posit that physical-digital programming will let exploratory makers build digital fabrication workflows that are otherwise prohibitively difficult to construct, share, and improve. To test this hypothesis, I built three systems that prototype components of physical-digital programming: a common, multimodal programming environment; a formal grammar of machine functionality; and a method of generating task-specific visualizations of machine behavior. In addition, I am working on a fourth system intended to synchronize code assumptions with physical-world states through user-defined axioms and augmented reality. These tools will help both novice and experienced makers use programming to solve emerging problems that require physical automation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616709"}, {"primary_key": "1264741", "vector": [], "sparse_vector": [], "title": "AudiLens: Configurable LLM-Generated Audiences for Public Speech Practice.", "authors": ["Jeongeon Park", "<PERSON><PERSON><PERSON>"], "summary": "AudiLens is a large-language model (LLM)-based audience simulator for public speech practice that allows speakers to generate and configure a group of generated audiences, and use them to receive feedback on their speech during and after the practice in multiple aspects. AudiLens leverages the capability of LLMs in being able to generate a diverse set of personas and being able to simulate human behavior, and provide flexibility to the speaker in terms of practicing their speech with multiple sets of audience groups in multiple speech formats. We demonstrate the use of AudiLens in two scenarios—giving a tutorial and debating.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625114"}, {"primary_key": "1264742", "vector": [], "sparse_vector": [], "title": "Generative Agents: Interactive Simulacra of Human Behavior.", "authors": ["<PERSON><PERSON>", "Joseph <PERSON>;<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Believable proxies of human behavior can empower interactive applications ranging from immersive environments to rehearsal spaces for interpersonal communication to prototyping tools. In this paper, we introduce generative agents: computational software agents that simulate believable human behavior. Generative agents wake up, cook breakfast, and head to work; artists paint, while authors write; they form opinions, notice each other, and initiate conversations; they remember and reflect on days past as they plan the next day. To enable generative agents, we describe an architecture that extends a large language model to store a complete record of the agent's experiences using natural language, synthesize those memories over time into higher-level reflections, and retrieve them dynamically to plan behavior. We instantiate generative agents to populate an interactive sandbox environment inspired by The Sims, where end users can interact with a small town of twenty-five agents using natural language. In an evaluation, these generative agents produce believable individual and emergent social behaviors. For example, starting with only a single user-specified notion that one agent wants to throw a Valentine's Day party, the agents autonomously spread invitations to the party over the next two days, make new acquaintances, ask each other out on dates to the party, and coordinate to show up for the party together at the right time. We demonstrate through ablation that the components of our agent architecture—observation, planning, and reflection—each contribute critically to the believability of agent behavior. By fusing large language models with computational interactive agents, this work introduces architectural and interaction patterns for enabling believable simulations of human behavior.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606763"}, {"primary_key": "1264743", "vector": [], "sparse_vector": [], "title": "SculptAR: Direct Manipulations of Machine Toolpaths in Augmented Reality for 3D Clay Printing.", "authors": ["<PERSON>", "Ana Cárdenas Gasca", "<PERSON>", "<PERSON>"], "summary": "Specifying designs for additive manufacturing using machine toolpaths unlocks design attributes such as surface textures and shapes determined by material and machine constrains compared to higher level representations like 3D geometries. Current methodologies for authoring these designs necessitate a high level of programming or geometric understanding, posing a significant barrier to entry and limited control. Additionally, the confinement of these workflows within computer screens obscures the comprehension of material and dimensional constraints. To bridge this gap, we demonstrate the direct manipulation of machine toolpaths in Augmented Reality for clay 3D printing. Our application relies on hand interactions to edit path control points. We also provide a set of options that allow the user to control how their changes to one control points are broadcast to others to determine surface shapes and textures. By leveraging AR interactions in a physical context, our proposal aims to leverage existing physical workflows and enable practitioners to apply their understanding of material properties and visual understanding of physical 3D objects.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615822"}, {"primary_key": "1264744", "vector": [], "sparse_vector": [], "title": "Storyfier: Exploring Vocabulary Learning Support with Text Generation Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junkai Zhu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vocabulary learning support tools have widely exploited existing materials, e.g., stories or video clips, as contexts to help users memorize each target word. However, these tools could not provide a coherent context for any target words of learners' interests, and they seldom help practice word usage. In this paper, we work with teachers and students to iteratively develop Storyfier, which leverages text generation models to enable learners to read a generated story that covers any target words, conduct a story cloze test, and use these words to write a new story with adaptive AI assistance. Our within-subjects study (N=28) shows that learners generally favor the generated stories for connecting target words and writing assistance for easing their learning workload. However, in the read-cloze-write learning sessions, participants using Storyfier perform worse in recalling and using target words than learning with a baseline tool without our AI features. We discuss insights into supporting learning tasks with generative models.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606786"}, {"primary_key": "1264745", "vector": [], "sparse_vector": [], "title": "The Ultimate Interface.", "authors": ["<PERSON>"], "summary": "This talk will explore the possibilities of what emerging AI architectures could mean for interaction. My perspective and examples are based on personal opinions, informed by observations and experience gained from building advanced interactions over the last few decades. While I aim to provide insights, speculations, and suggest some essential conditions for creating such an ultimate interface, my insights may not be exhaustive. I invite open discussion and debate on this subject, recognizing that the conditions that I propose might be necessary but perhaps not sufficient in themselves.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3624511"}, {"primary_key": "1264746", "vector": [], "sparse_vector": [], "title": "DiLogics: Creating Web Automation Programs with Diverse Logics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Knowledge workers frequently encounter repetitive web data entry tasks, like updating records or placing orders. Web automation increases productivity, but translating tasks to web actions accurately and extending to new specifications is challenging. Existing tools can automate tasks that perform the same logical trace of UI actions (e.g., input text in each field in order), but do not support tasks requiring different executions based on varied input conditions. We present DiLogics, a programming-by-demonstration system that utilizes NLP to assist users in creating web automation programs that handle diverse specifications. DiLogics first semantically segments input data to structured task steps. By recording user demonstrations for each step, DiLogics generalizes the web macros to novel but semantically similar task requirements. Our evaluation showed that non-experts can effectively use DiLogics to create automation programs that fulfill diverse input instructions. DiLogics provides an efficient, intuitive, and expressive method for developing web automation programs satisfying diverse specifications.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606822"}, {"primary_key": "1264747", "vector": [], "sparse_vector": [], "title": "SwarmFidget: Exploring Programmable Actuated Fidgeting with Swarm Robots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fidgeting is a common behavior that one tends to engage in during periods of inattention or mind wandering. Although attempts were undertaken to enhance fidget devices with advanced technology, such as sensors and displays, no works exist that explored fidgeting with actuated devices. To fill this gap, we introduce the concept of programmable actuated fidgeting and the design space for SwarmFidget. Programmable actuated fidgeting is a type of fidgeting that involves devices integrated with actuators, sensors, and computing to enable a dynamic and customizable interactive fidgeting experience. SwarmFidget is an instance of a platform where tabletop swarm robots are used to facilitate programmable actuated fidgeting. To engage with actuated fidgets, users can input commands through various modalities such as touch or gesture, and the actuators in the fidgeting device will respond in a programmable manner to provide haptic, visual, or audio feedback.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615806"}, {"primary_key": "1264748", "vector": [], "sparse_vector": [], "title": "Reframe: An Augmented Reality Storyboarding Tool for Character-Driven Analysis of Security &amp; Privacy Concerns.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While current augmented reality (AR) authoring tools lower the technical barrier for novice AR designers, they lack explicit guidance to consider potentially harmful aspects of AR with respect to security & privacy (S&P). To address potential threats in the earliest stages of AR design, we developed Reframe, a digital storyboarding tool for designers with no formal training to analyze S&P threats. We accomplish this through a frame-based authoring approach, which captures and enhances storyboard elements that are relevant for threat modeling, and character-driven analysis tools, which personify S&P threats from an underlying threat model to provide simple abstractions for novice AR designers. Based on evaluations with novice AR designers and S&P experts, we find that Reframe enables designers to analyze threats and propose mitigation techniques that experts consider good quality. We discuss how Reframe can facilitate collaboration between designers and S&P professionals and propose extensions to Reframe to incorporate additional threat models.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606750"}, {"primary_key": "1264749", "vector": [], "sparse_vector": [], "title": "AirTied: Automatic Personal Fabrication of Truss Structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present AirTied, a device that fabricates truss structures in a fully automatic fashion. AirTied achieves this by unrolling a 20cm-wide inflatable plastic tube and tying nodes into it. AirTied creates nodes by holding onto a segment of tube, stacking additional tube segments on top of it, tying them up, and releasing the result. The resulting structures are material-efficient and light as well as sturdy, as we demonstrate by creating a 6m-tower. Unlike the prior art, AirTied requires no scaffolding and no building blocks, bringing automated truss construction into the reach of personal fabrication.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606820"}, {"primary_key": "1264750", "vector": [], "sparse_vector": [], "title": "Pneunocchio: A playful nose augmentation for facilitating embodied representation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Cosmos) Wang", "<PERSON>", "Florian &apos;Floyd&apos; Mueller"], "summary": "Prior research has offered a plethora of wearables centred around sensing bodily actions ranging from more explicit data, such as movement and physiological response, to implicit information, such as ocular and brain activity. Bodily augmentations that physically extend the user's body along with altering body schema and image have been proposed recently as well, owing to factors such as accessibility and improving communication. However, these attempts have usually consisted of uncomfortable interfaces that either restrict the user's movement or are intrusive in nature. In this work, we present Pneunocchio, a playful nose augmentation based on the lore of Pinocchio. Pneunocchio consists of a pneumatic-based inflatable that a user wears on their nose to play a game of two truths and a lie. With our work, we aim to explore expressive bodily augmentations that respond to a player's physiological state that can alter the perception of their body while serving as an expressive match for a current part of the body.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616651"}, {"primary_key": "1264751", "vector": [], "sparse_vector": [], "title": "VRoxy: Enabling Remote Collaboration in Large Spaces Beyond Local Boundaries via a VR-Driven Robotic Proxy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent research in robotic proxies has demonstrated that one can automatically reproduce many non-verbal cues important in co-located collaboration. However, they often require a symmetrical hardware setup in each location. We present the VRoxy system, designed to enable access to remote spaces through a robotic embodiment, using a VR headset in a much smaller space, such as a personal office. VRoxy maps small movements in VR space to larger movements in the physical space of the robot, allowing the user to navigate large physical spaces easily. Using VRoxy, the VR user can quickly explore and navigate in a low-fidelity rendering of the remote space. Upon the robot's arrival, the system uses the feed of a 360 camera to support real-time interactions. The system also facilitates various interaction modalities by rendering the micro-mobility around shared spaces, head and facial animations, and pointing gestures on the proxy. We demonstrate how our system can accommodate mapping multiple physical locations onto a unified virtual space. In a formative study, users could complete a design decision task where they navigated and collaborated in a complex 7.5m x 5m layout using a 3m x 2m VR space.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606743"}, {"primary_key": "1264752", "vector": [], "sparse_vector": [], "title": "GestureCanvas: A Programming by Demonstration System for Prototyping Compound Freehand Interaction in VR.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dongwook Yoon"], "summary": "As the use of hand gestures becomes increasingly prevalent in virtual reality (VR) applications, prototyping Compound Freehand Interactions (CFIs) effectively and efficiently has become a critical need in the design process. Compound Freehand Interaction (CFI) is a sequence of freehand interactions where each sub-interaction in the sequence conditions the next. Despite the need for interactive prototypes of CFI in the early design stage, creating them is effortful and remains a challenge for designers since it requires a highly technical workflow that involves programming the recognizers, system responses and conditionals for each sub-interaction. To bridge this gap, we present GestureCanvas, a freehand interaction-based immersive prototyping system that enables a rapid, end-to-end, and code-free workflow for designing, testing, refining, and subsequently deploying CFI by leveraging the three pillars of interaction models: event-driven state machine, trigger-action authoring, and programming by demonstration. The design of GestureCanvas includes three novel design elements — (i) appropriating the multimodal recording of freehand interaction into a CFI authoring workspace called Design Canvas, (ii) semi-automatic identification of the input trigger logic from demonstration to reduce the manual effort of setting up triggers for each sub-interaction, (iii) on the fly testing for independently validating the input conditionals in-situ. We validate the workflow enabled by GestureCanvas through an interview study with professional designers and evaluate its usability through a user study with non-experts. Our work lays the foundation for advancing research on immersive prototyping systems allowing even highly complex gestures to be easily prototyped and tested within VR environments.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606736"}, {"primary_key": "1264753", "vector": [], "sparse_vector": [], "title": "LingoLand: An AI-Assisted Immersive Game for Language Learning.", "authors": ["<PERSON>"], "summary": "Immersion with a foreign language is key to increased motivation, satisfaction, and learning success. However, this can be hindered by anxiety and lack of access to immersive settings. LingoLand will address this by immersing people in foreign language learning environments designed to mimic real scenarios. Using generative machine learning, LingoLand presents players with personalized missions where they can freely interact with game characters, all endlessly patient and supportive of new language learners. Players receive instant feedback through a natural, voice-based interaction. Through LingoLand, players gain an understanding of different cultures while building practical language skills in a fun way.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625117"}, {"primary_key": "1264754", "vector": [], "sparse_vector": [], "title": "Generative Facial Expressions and Eye Gaze Behavior from Prompts for Multi-Human-Robot Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nonverbal cues such as eye gaze and facial expressions play critical roles in conveying intent, regulating conversation, and fostering engagement. A robot's ability to effectively deploy these behaviors can significantly enhance human-robot collaboration. We describe a simple zero-shot learning approach to generate facial expression and gaze shifting behaviors to control a social robot conversing with an individual or group. An initial prompt provides instructions to a pre-trained large language model on how the model can control a robot's facial expression and eye gaze behaviors during a conversation. To demonstrate this, we describe a proof-of-concept implementation using the robot Furhat. This simple and easily customizable approach can be used to improve perception of a robot's social presence in multi-human-robot interactions.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616623"}, {"primary_key": "1264755", "vector": [], "sparse_vector": [], "title": "Olio: A Semantic Search Interface for Data Repositories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Search and information retrieval systems are becoming more expressive in interpreting user queries beyond the traditional weighted bag-of-words model of document retrieval. For example, searching for a flight status or a game score returns a dynamically generated response along with supporting, pre-authored documents contextually relevant to the query. In this paper, we extend this hybrid search paradigm to data repositories that contain curated data sources and visualization content. We introduce a semantic search interface, <PERSON><PERSON>, that provides a hybrid set of results comprising both auto-generated visualization responses and pre-authored charts to blend analytical question-answering with content discovery search goals. We specifically explore three search scenarios - question-and-answering, exploratory search, and design search over data repositories. The interface also provides faceted search support for users to refine and filter the conventional best-first search results based on parameters such as author name, time, and chart type. A preliminary user evaluation of the system demonstrates that <PERSON><PERSON>'s interface and the hybrid search paradigm collectively afford greater expressivity in how users discover insights and visualization content in data repositories.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606806"}, {"primary_key": "1264756", "vector": [], "sparse_vector": [], "title": "Fluid Reality: High-Resolution, Untethered Haptic Gloves using Electroosmotic Pump Arrays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual and augmented reality headsets are making significant progress in audio-visual immersion and consumer adoption. However, their haptic immersion remains low, due in part to the limitations of vibrotactile actuators which dominate the AR/VR market. In this work, we present a new approach to create high-resolution shape-changing fingerpad arrays with 20 haptic pixels/cm2. Unlike prior pneumatic approaches, our actuators are low-profile (5mm thick), low-power (approximately 10mW/pixel), and entirely self-contained, with no tubing or wires running to external infrastructure. We show how multiple actuator arrays can be built into a five-finger, 160-actuator haptic glove that is untethered, lightweight (207g, including all drive electronics and battery), and has the potential to reach consumer price points at volume production. We describe the results from a technical performance evaluation and a suite of eight user studies, quantifying the diverse capabilities of our system. This includes recognition of object properties such as complex contact geometry, texture, and compliance, as well as expressive spatiotemporal effects.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606771"}, {"primary_key": "1264757", "vector": [], "sparse_vector": [], "title": "Knowledge Compass: A Question Answering System Guiding Students with Follow-Up Question Recommendations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pedagogical question-answering (QA) systems have been utilized for providing individual support in online learning courses. However, existing systems often neglect the education practice of guiding and encouraging students to think of relevant questions for deeper and more comprehensive learning. To address this gap, we introduce Knowledge Compass, an interactive QA system. The system can recommend follow-up questions that provide potential further explorations of the topics students ask about. Additionally, the system applies a course outline visualization and a set of interactive features for students to track the relationship between their questions and the course content.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615785"}, {"primary_key": "1264758", "vector": [], "sparse_vector": [], "title": "OperAR: Using an Augmented Reality Agent to Enhance Children&apos;s Interactive Intangible Cultural Heritage Experience of the Peking Opera.", "authors": ["Hongning Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a traditional Chinese theatrical performance, the Peking Opera is a world intangible cultural heritage. However, appreciating and understanding the traditional arts of Peking Opera requires rich life experience and knowledge about traditional Chinese culture, which has led to its lack of recognition and support from younger Chinese audiences. To expand the audience for the traditional Chinese cultural heritage of Peking Opera and stimulate young people's interest in learning about this art form, we introduce \"OperAR\" as an augmented reality game system that teaches children about the history and culture of the Peking Opera and how to perform Peking Opera using a cartoon virtual agent and a set of physical cards based on Peking Opera costumes. The pilot study results show the potential benefits of OperAR in enhancing children's interactive experiences in virtual scenarios in addition to stimulating their interest in learning about Peking Opera.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616690"}, {"primary_key": "1264759", "vector": [], "sparse_vector": [], "title": "ZINify: Transforming Research Papers into Engaging Zines with Large Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Research papers are a vital building block for scientific discussion. While these papers follow effective structures for the relevant community, they are unable to cater to novice readers and express otherwise creative ideas in creative mediums. To this end, we propose ZINify, the first approach to automatically transform research papers into engaging zines using large language models (LLM) and text-to-image generators. Following zine's long history of supporting independent, creative expression, we propose a technique that can work with authors to build more engaging, marketable, and unconventional content that is based on their research. We believe that our work will help make research more engaging and accessible to all while helping papers stand out in crowded online venues.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625118"}, {"primary_key": "1264760", "vector": [], "sparse_vector": [], "title": "SleeveIO: Modular and Reconfigurable Platform for Multimodal Wearable Haptic Feedback Interactions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "SleeveIO is a modular and reconfigurable hardware platform for rapid prototyping of multimodal wearable haptic feedback interactions. SleeveIO features engineered machine-knitted sleeve and band substrates, and five categories of haptic feedback actuator modules including vibrotactors, bellows, muscles, suction/puffing cups, and quad-chamber actuators. A universal magnetic attachment mechanism unifies the different types of actuators, enabling countless multimodal haptic experiences involving combinations of different actuator types in different configurations. SleeveIO is compatible with a variety of hardware/software control platforms, such as FlowIO [42], which enables individual control of each haptic actuator and makes the system battery-powered and untethered. This paper presents the SleeveIO platform in detail along with replication resources, a novel generalized approach to making different types of haptic actuators modular and interoperable, new application possibilities enabled by SleeveIO, and a pilot assessment of the viability of the platform as a whole and each module individually.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606739"}, {"primary_key": "1264761", "vector": [], "sparse_vector": [], "title": "Decomposable Interactive Systems.", "authors": ["<PERSON>"], "summary": "As sustainability becomes an increasingly pressing concern across disciplines, the design and fabrication communities within HCI are rapidly discovering and sharing a wealth of novel materials, tools, and workflows, allowing us to make physical artifacts that are more eco-friendly than ever before. Still, sustainability and functionality are often at odds with one another when it comes to the design of interactive systems, with most systems still relying on conventional electronic components that must be extracted and individually handled at end of life. My work offers approaches for designing decomposable interactive systems that are made with materials that are widely available, safe, and even edible, empowering the \"everyday designer\" to make sustainable systems for applications that do not demand long operation times or high power. Enabled by the growing ecosystem of decomposable materials and systems, I also propose new opportunities for designing for unmaking, a counterpart to making that opens the opaque, industrial processes of recycling and composting as rich design spaces to encourage further engagement and critical reflection around themes of sustainability, materiality, and consumption.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616705"}, {"primary_key": "1264762", "vector": [], "sparse_vector": [], "title": "SPEERLoom: An Open-Source Loom Kit for Interdisciplinary Engagement in Math, Engineering, and Textiles.", "authors": ["<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Weaving is a fabrication process that is grounded in mathematics and engineering: from the binary, matrix-like nature of the pattern drafts weavers have used for centuries, to the punch card programming of the first Jacquard looms. This intersection of disciplines provides an opportunity to ground abstract mathematical concepts in a concrete and embodied art, viewing this textile art through the lens of engineering. Currently, available looms are not optimized to take advantage of this opportunity to increase mathematics learning by providing hands-on interdisciplinary learning in collegiate classrooms. In this work, we present SPEERLoom: an open-source, robotic Jacquard loom kit designed to be a tool for interweaving cloth fabrication, mathematics, and engineering to support interdisciplinary learning in the classroom. We discuss the design requirements and subsequent design of SPEERLoom. We also present the results of a pilot study in a post-secondary class finding that SPEERLoom supports hands-on, interdisciplinary learning of math, engineering, and textiles.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606724"}, {"primary_key": "1264763", "vector": [], "sparse_vector": [], "title": "Structured Light Speckle: Joint Ego-Centric Depth Estimation and Low-Latency Contact Detection via Remote Vibrometry.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite advancements in egocentric hand tracking using head-mounted cameras, contact detection with real-world objects remains challenging, particularly for the quick motions often performed during interaction in Mixed Reality. In this paper, we introduce a novel method for detecting touch on discovered physical surfaces purely from an egocentric perspective using optical sensing. We leverage structured laser light to detect real-world surfaces from the disparity of reflections in real-time and, at the same time, extract a time series of remote vibrometry sensations from laser speckle motions. The pattern caused by structured laser light reflections enables us to simultaneously sample the mechanical vibrations that propagate through the user's hand and the surface upon touch.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606749"}, {"primary_key": "1264764", "vector": [], "sparse_vector": [], "title": "Usable and Fast Interactive Mental Face Reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce an end-to-end interactive system for mental face reconstruction – the challenging task of visually reconstructing a face image a person only has in their mind. In contrast to existing methods that suffer from low usability and high mental load, our approach only requires the user to rank images over multiple iterations according to the perceived similarity with their mental image. Based on these rankings, our mental face reconstruction system extracts image features in each iteration, combines them into a joint feature vector, and then uses a generative model to visually reconstruct the mental image. To avoid the need for collecting large amounts of human training data, we further propose a computational user model that can simulate human ranking behaviour using data from an online crowd-sourcing study (N=215). Results from a 12-participant user study show that our method can reconstruct mental images that are visually similar to existing approaches but has significantly higher usability, lower perceived workload, and is faster. In addition, results from a third 22-participant lineup study in which we validated our reconstructions on a face ranking task show a identification rate of , which is in line with prior work. These results represent an important step towards new interactive intelligent systems that can robustly and effortlessly reconstruct a user's mental image.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606795"}, {"primary_key": "1264765", "vector": [], "sparse_vector": [], "title": "Laser-PoweredVibrotactileRendering.", "authors": ["<PERSON><PERSON>", "Yong<PERSON> Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We investigate the feasibility of a vibrotactile device that is both battery-free and electronic-free. Our approach leverages lasers as a wireless power transfer and haptic control mechanism, which can drive small actuators commonly used in AR/VR and mobile applications with DC or AC signals. To validate the feasibility of our method, we developed a proof-of-concept prototype that includes low-cost eccentric rotating mass (ERM) motors and linear resonant actuators (LRAs) connected to photovoltaic (PV) cells. This prototype enabled us to capture laser energy from any distance across a room. Through different vibration patterns rendered using either a single motor or two motors, we demonstrate the effectiveness of our approach in generating vibration patterns.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615795"}, {"primary_key": "1264766", "vector": [], "sparse_vector": [], "title": "Sensecape: Enabling Multilevel Exploration and Sensemaking with Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "People are increasingly turning to large language models (LLMs) for complex information tasks like academic research or planning a move to another city. However, while they often require working in a nonlinear manner -- e.g., to arrange information spatially to organize and make sense of it, current interfaces for interacting with LLMs are generally linear to support conversational interaction. To address this limitation and explore how we can support LLM-powered exploration and sensemaking, we developed Sensecape, an interactive system designed to support complex information tasks with an LLM by enabling users to (1) manage the complexity of information through multilevel abstraction and (2) seamlessly switch between foraging and sensemaking. Our within-subject user study reveals that Sensecape empowers users to explore more topics and structure their knowledge hierarchically, thanks to the externalization of levels of abstraction. We contribute implications for LLM-based workflows and interfaces for information tasks.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606756"}, {"primary_key": "1264767", "vector": [], "sparse_vector": [], "title": "Waste Genie: A Web-Based Educational Technology for Sustainable Waste Management.", "authors": ["Qiming Sun", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we proposed and designed a web-based educational technology to support sustainable living. It is called Waste Genie. Waste Genie integrates formal and informal learning features to help users learn about waste management. A field trial was conducted to evaluate its effectiveness, and the results indicated the platform was well-received by the users.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616696"}, {"primary_key": "1264768", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>bor-Environment Observer: An Intelligent Agent for Immersive Working Companionship.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Human-computer symbiosis is a crucial direction for the development of artificial intelligence. As intelligent systems become increasingly prevalent in our work and personal lives, it is important to develop strategies to support users across physical and virtual environments. While technological advances in personal digital devices, such as personal computers and virtual reality devices, can provide immersive experiences, they can also disrupt users' awareness of their surroundings and enhance the frustration caused by disturbances. In this paper, we propose a joint observation strategy for artificial agents to support users across virtual and physical environments. We introduce a prototype system, neighbor-environment observer (NEO), that utilizes non-invasive sensors to assist users in dealing with disruptions to their immersive experience. System experiments evaluate NEO from different perspectives and demonstrate the effectiveness of the joint observation strategy. A user study is conducted to evaluate its usability. The results show that NEO could lessen users' workload with the learned user preference. We suggest that the proposed strategy can be applied to various smart home scenarios.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606728"}, {"primary_key": "1264769", "vector": [], "sparse_vector": [], "title": "PURRtentio: Implementing a Smart Litter Box for Feline Urinalysis with Electrochemical Biosensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> L<PERSON>", "<PERSON><PERSON>"], "summary": "Traditional collection and analysis of feline urine samples for health monitoring are invasive, expensive, and infrequent. This paper introduces PURRtentio, a novel litter box system utilizing an electrochemical biosensor to monitor analytes in feline urine. The system comprises a DIY biosensor, potentiostat, microcontroller, distance sensor, and mobile application. Performance validation compared PURRtentio with an industry-grade potentiostat. PURRtentio presents an innovative and non-invasive approach for consistent monitoring of chemistry elements in feline urine, enabling early detection and management of cat's health conditions. This technology has the potential to revolutionize feline health monitoring, providing a solution for veterinarians and pet owners.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615820"}, {"primary_key": "1264770", "vector": [], "sparse_vector": [], "title": "XR and AI: AI-Enabled Virtual, Augmented, and Mixed Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This workshop aims to unite experts and practitioners in XR and AI to envision the future of AI-enabled virtual, augmented, and mixed reality experiences. Our expansive discussion includes a variety of key topics: Generative XR, Large Language Models (LLMs) for XR, Adaptive and Context-Aware XR, Explainable AI for XR, and harnessing AI to enhance and prototype XR experiences. We aim to identify the opportunities and challenges of how recent advances of AI could bring new XR experiences, which cannot be done before, with a keen focus on the seamless blending of our digital and physical worlds.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3617432"}, {"primary_key": "1264771", "vector": [], "sparse_vector": [], "title": "Virtual Buddy: Redefining Conversational AI Interactions for Individuals with Hand Motor Disabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Advances in artificial intelligence have transformed the paradigm of human-computer interaction, with the development of conversational AI systems playing a pivotal role. These systems employ technologies such as natural language processing and machine learning to simulate intelligent and human-like conversations. Driven by the personal experience of an individual with a neuromuscular disease who faces challenges with leaving home and contends with limited hand-motor control when operating digital systems, including conversational AI platforms, we propose a method aimed at enriching their interaction with conversational AI. Our prototype allows the creation of multiple agent personas based on hobbies and interests, to support topic-based conversations. In contrast with existing systems, such as Replika, that offer a 1:1 relation with a virtual agent, our design enables one-to-many relationships, easing the process of interaction for this individual by reducing the need for constant data input. We can imagine our prototype potentially helping others who are in a similar situation with reduced typing/input ability.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616680"}, {"primary_key": "1264772", "vector": [], "sparse_vector": [], "title": "Projectoroid: A Mobile Robot-Based SAR Display Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces \"Projectoroid,\" a mobile robot equipped with a projector that facilitates mobile spatial augmented reality (SAR) displays, and examines the potential of SAR deployment via a mobile robot by comparing and evaluating content display methods. Projectoroid converts its real-world position and orientation calculated by its internal sensors into the position and orientation of a camera in virtual space. Consequently, it can project the image captured within the virtual space onto the real-world floor. Thus, Projectoroid is capable of revealing a segment of an expansive virtual space into the real world based on its position and orientation. In this paper, we refer to the degree to which people recognize the correspondence between the virtual space and the real world from the projected image as the \"sense of reveal.\" We believe that an increase in the sense of reveal would heighten people's interest in the content from the virtual world. The results of our user study show that the sense of reveal is amplified as the outline of the projected image becomes more circular.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615793"}, {"primary_key": "1264773", "vector": [], "sparse_vector": [], "title": "Sacriface: A Simple and Versatile Support Structure for 3D Printing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Digital fabrication serves as a potent instrument for facilitating interaction between the real and digital realms. However, the process is becoming increasingly complex amidst its development. We present Sacriface, a simple and versatile support structure for 3D printing which allows us to obtain further efficiency and flexibility in 3D printing. We simplified the strategy of expanding support structures taking advantage of a stable, growing overhang that gradually recovers its original shape as printing thickness increases. Sacriface is effortlessly designed and printable using an unmodified 3D printer, which enhances user customization.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616649"}, {"primary_key": "1264774", "vector": [], "sparse_vector": [], "title": "Interactive Benefits from Switching Electrical to Magnetic Muscle Stimulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Electrical muscle stimulation (EMS) became a popular method for force-feedback without mechanical-actuators. While much has been written about the advantages of EMS, not much work has investigated circumventing its key limitations: (1) as impulses traverse the skin, they cause an uncomfortable \"tingling\"; (2) impulses are delivered via gelled-electrodes, which not only require direct skin contact (must be worn under clothes); but, also (3) dry up after a few hours. To tackle these, we explore switching from electrical to magnetic muscle stimulation (MMS), via electromagnetic fields generated by coils. The first advantage is that MMS coils do not require direct skin contact and can actuate up to 5 cm away (Study#1)—this enables applications not possible with EMS, such as stimulation over the clothes and without ever replacing electrodes. Second, and more important, MMS results in ∼50 % less discomfort caused by tingling than EMS (Study#2). We found that reducing this tingling discomfort has two downstream effects for interactive systems: (1) participants rated MMS force-feedback as more realistic than that of EMS (Study#3); and (2) participants could more accurately perceive the pose actuated by the interactive system (Study#4). Finally, we demonstrated applications where our proposed switch from EMS to MMS improves user experience, including for VR feedback, gaming, and pose-control.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606812"}, {"primary_key": "1264775", "vector": [], "sparse_vector": [], "title": "VizPI: A Real-Time Visualization Tool for Enhancing Peer Instruction in Large-Scale Programming Lectures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Peer instruction (PI) has shown significant potential in facilitating student engagement and collaborative learning. However, the implementation of PI for large-scale programming lectures has proven challenging due to difficulties in monitoring student engagement, discussion topics, and code changes. This paper introduces VizPI, an interactive web tool that enables instructors to conduct, monitor, and assess PI for programming exercises in real-time. With features that visualize the progress of student discussions and code submissions, VizPI allows for more effective oversight of PI activities and the provision of personalized feedback at scale. Our work aims to transform the pedagogical approach to PI in programming education, making it more engaging and adaptable to student needs.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616632"}, {"primary_key": "1264776", "vector": [], "sparse_vector": [], "title": "PColorizor: Re-coloring Ancient Chinese Paintings with Ideorealm-congruent Poems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Color restoration of ancient Chinese paintings plays a significant role in Chinese culture protection and inheritance. However, traditional color restoration is challenging and time-consuming because it requires professional restorers to conduct detailed literature reviews on numerous paintings for reference colors. After that, they have to fill in the inferred colors on the painting manually. In this paper, we present PColorizor, an interactive system that integrates advanced deep-learning models and novel visualizations to ease the difficulties of color restoration. PColorizor is established on the principle of poem-painting congruence. Given a color-faded painting, we employ both explicit and implicit color guidance implied by ideorealm-congruent poems to associate reference paintings. We propose a mountain-like visualization to facilitate efficient navigation of the color schemes extracted from the reference paintings. This visual representation allows users to easily see the color distribution over time at both the ideorealm and imagery levels. Moreover, we demonstrate the ideorealm understood by deep learning models through visualizations to bridge the communication gap between human restorers and deep learning models. We also adopt intelligent color-filling techniques to accelerate manual color restoration further. To evaluate PColorizor, we collaborate with domain experts to conduct two case studies to collect their feedback. The results suggest that PColorizor could be beneficial in enabling the effective restoration of color-faded paintings.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606814"}, {"primary_key": "1264777", "vector": [], "sparse_vector": [], "title": "An Adaptable Workflow for Manual-Computational Ceramic Surface Ornamentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Surface ornamentation is a rich component of ceramic manufacture wherein craftspeople use multiple methods to create intricate patterns on vessels. Computational fabrication can extend manual ceramic ornamentation through procedural pattern generation and automated fabrication; however, to be effective in traditional ceramics, computational fabrication systems must remain compatible with existing processes and materials. We contribute an interactive design workflow, CeramWrap, in which craftspeople can procedurally design and fabricate decorative patterned stencils tailored to radially symmetrical vessels. Our approach extends manual techniques through a workflow where craftspeople design and edit repetitive motifs directly on a 3D digital model of a vessel and then interactively adjust the unrolling of the 3D design to a 2D format suitable for digitally fabricating stencils and templates. Through a series of example artifacts, we demonstrate how our workflow generalizes across multiple vessel geometries, supports manual and digital clay fabrication, and is adaptable to different surface ornamentation methods.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606726"}, {"primary_key": "1264778", "vector": [], "sparse_vector": [], "title": "Aisen - Web-Based Gaze-Tracking Assistive Communication Interface with Word Cards Generated by LLMs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>sen is an innovative web-based communication tool that integrates the WebGazer.js library [5] with advanced large language models (LLMs). Designed as an affordable communication solution for those with communication challenges, <PERSON><PERSON> facilitates expression through a unique word-selection interface. Rather than using a traditional keyboard, <PERSON><PERSON> introduces a two-tiered \"word card\" system. This system includes a static set of cards tailored to the patient's specific needs and a \"dynamic\" set where cards are intelligently generated by LLMs based on user input and preferences. Our research delineated three specific user personas, emphasizing <PERSON><PERSON>'s applicability for elderly patients. The platform integrates an eye-tracking mechanism, a gaze-responsive interface, and a word card repository enriched with LLMs. This endeavor highlights the transformative potential of web-enabled eye-tracking and LLMs in enhancing communication for individuals with impairments.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625116"}, {"primary_key": "1264779", "vector": [], "sparse_vector": [], "title": "Dynamic Toolchains: Software Infrastructure for Digital Fabrication Workflows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "New digital fabrication workflows require both software development and digital/physical material exploration. To support digital fabrication workflow development, we contribute infrastructure that prioritizes extensibility and iteration. Dynamic Toolchains are dataflow programs with event-driven feedback between interactive, stateful modules. We contribute a browser-based dataflow environment for running Dynamic Toolchains, a library of fabrication-oriented front- and back-end modules for design and machine control, and a development framework for building custom modules. Furthermore, we show how our infrastructure supports unconventional fabrication workflows with demonstrations that include interactive watercolor painting, map plotting, machine knitting, audio embroidery, textured 3d printing, and computer-controlled milling. These demonstrations show how our infrastructure supports multiple kinds of engagement including reuse, remix, and extension. Finally, we discuss how this work contributes to broader conversations in HCI on creativity across the digital/physical divide.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606802"}, {"primary_key": "1264780", "vector": [], "sparse_vector": [], "title": "FeetThrough: Electrotactile Foot Interface that Preserves Real-World Sensations.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Haptic interfaces have been extended to the feet to enhance foot-based activities, such as guidance while walking or stepping on virtual textures. Most feet haptics use mechanical actuators, namely vibration motors. However, we argue that vibration motors are not the ideal actuators for all feet haptics. Instead, we demonstrate that electrotactile stimulation provides qualities that make it a powerful feet-haptic interface: (1) Users wearing electrotactile can not only feel the stimulation but can also better feel the terrain under their feet—this is critical as our feet are also responsible for the balance on uneven terrains and stairs—electrotactile achieves this improved \"feel-through\" effect because it is thinner than vibrotactile actuators, at 0.1 mm in our prototype; (2) While a single vibrotactile actuator will also vibrate surrounding skin areas, we found improved two-point discrimination thresholds for electrotactile; (3) Electrotactile can be applied directly to soles, insoles or socks, enabling new applications such as barefoot interactive experiences or without requiring users to have custom-shoes with built-in vibration motors. Finally, we demonstrate applications in which electrotactile feet interfaces allow users to feel not only virtual information but also the real terrain under their shoes, such as a VR experience where users walk on ground props and a tactile navigation system that augments the ground with virtual tactile paving to assist pedestrians in low-vision situations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606808"}, {"primary_key": "1264781", "vector": [], "sparse_vector": [], "title": "TaleMate: Collaborating with Voice Agents for Parent-Child Joint Reading Experiences.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Joint reading is a key activity for early learners, with caregiver-child interactions such as questioning and feedback playing an essential role in children's cognitive and linguistic development. However, for some parents, actively engaging children in storytelling can be challenging. To address this, we introduce TaleMate—a platform designed to enhance shared reading by leveraging conversational agents that have been shown to support children's engagement and learning. TaleMate enables a dynamic, participatory reading experience where parents and children can choose which characters they wish to embody. Moreover, the system navigates the challenges posed by digital reading tools, such as decreased parent-child interaction, and builds upon the benefits of traditional and digital reading techniques. TaleMate offers an innovative approach to fostering early reading habits, bridging the gap between traditional joint reading practices and the digital reading landscape.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616699"}, {"primary_key": "1264782", "vector": [], "sparse_vector": [], "title": "Semantic Hearing: Programming Acoustic Scenes with Binaural Hearables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Imagine being able to listen to the birds chirping in a park without hearing the chatter from other hikers, or being able to block out traffic noise on a busy street while still being able to hear emergency sirens and car honks. We introduce semantic hearing, a novel capability for hearable devices that enables them to, in real-time, focus on, or ignore, specific sounds from real-world environments, while also preserving the spatial cues. To achieve this, we make two technical contributions: 1) we present the first neural network that can achieve binaural target sound extraction in the presence of interfering sounds and background noise, and 2) we design a training methodology that allows our system to generalize to real-world use. Results show that our system can operate with 20 sound classes and that our transformer-based network has a runtime of 6.56 ms on a connected smartphone. In-the-wild evaluation with participants in previously unseen indoor and outdoor scenarios shows that our proof-of-concept system can extract the target sounds and generalize to preserve the spatial cues in its binaural output. Project page with code: https://semantichearing.cs.washington.edu", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606779"}, {"primary_key": "1264783", "vector": [], "sparse_vector": [], "title": "Demonstration of <PERSON><PERSON>: A Joy-based Brain-Computer Interface (BCI) with Wearable Skin Conformal Polymer Electrodes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We designed <PERSON><PERSON>, a joy-based electroencephalography (EEG) brain-computer interface (BCI). Users interact with <PERSON><PERSON> by imagining joyous thoughts and images that alter their prefrontal EEG asymmetries. These asymmetries control their character's movement in an endless runner video game, where joyous thoughts cause left prefrontal asymmetry that leads to receiving a reward. In this demonstration, we present <PERSON><PERSON> with a wearable, dry skin conformal polymer electrode EEG headband. We conducted a pilot evaluation (11 participants, 3 training sessions per participant) to assess neurofeedback efficacy and workload. We observed that our participants were able to perform relative left activation significantly greater than right activation and create single-session improvements in resting baseline asymmetry. We also report on perceived user demand, effort and performance.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615803"}, {"primary_key": "1264784", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: a Joy-based Brain-Computer Interface (BCI).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The size and cost of electroencephalography (EEG) headsets have been decreasing at a steadfast pace. Prefrontal cortical activity is a promising input source that is also important for affect regulation. We created <PERSON><PERSON>, a joy-based EEG brain-computer interface (BCI) which uses prefrontal asymmetries associated with joyful thoughts as input to an endless runner game where the user's character collects coins in response. In a lab study (20 participants, 15 training sessions per participant, up to two weeks of training), we found that our experiment group instructed to imagine positive music, winning awards, and similar strategies, demonstrated significantly greater ability in activating asymmetry compared to our placebo and control groups. In our analysis, <PERSON><PERSON> demonstrates the ability for prefrontal asymmetries to be used as input to an affective BCI and builds upon prior work in this area. Training these asymmetries can teach mental strategies that have applications in mental health.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606761"}, {"primary_key": "1264785", "vector": [], "sparse_vector": [], "title": "Demo of Z-Ring: Context-Aware Subtle Input Using Single-Point Bio-Impedance Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel"], "summary": "This paper presents Z-Ring, a novel wearable device that uses radio frequency (RF) based sensing to offer unique capabilities for human-computer interaction, including subtle input, object recognition, user identification, and passive surface interaction. With only a single sensing modality, Z-Ring achieves diverse and concurrent interactions that can enhance the user experience. We illustrate the potential of Z-Ring to enable seamless context-aware interactions via a custom music player application. In the future, we plan to expand Z-Ring's functionality with user customization and explore usage for additional applications.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615809"}, {"primary_key": "1264786", "vector": [], "sparse_vector": [], "title": "Substiports: User-Inserted Ad Hoc Objects as Reusable Structural Support for Unmodified FDM 3D Printers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We contribute a technical solution to reduce print time and material with unmodified fused deposition modelling printers. The approach uses ad hoc objects inserted by a user during printing as a replacement for printed support of overhanging structures. Examples of objects include household items like books, toy bricks, and custom mechanisms like a screw jack. A software-only system is integrated into existing slicing software to analyze generated support print paths, search a library of objects to find suitable replacements, optimize combinations of replacement objects, and make necessary adjustments to impacted printing layers and paths. During printing, the user is prompted to insert objects with the help of lightweight printed holders to guide placement and prevent movement. Instructions printed on the build-plate help identify and position objects. A technical evaluation measures performance and benefits with different sets of ad hoc objects and different levels of user involvement.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606718"}, {"primary_key": "1264787", "vector": [], "sparse_vector": [], "title": "Democratizing Content Creation and Consumption through Human-AI Copilot Systems.", "authors": ["<PERSON>"], "summary": "Content creation and consumption play vital roles in our lives. However, creating high-quality content can be challenging for beginners, while navigating through and consuming vast amounts of media content can be overwhelming and cumbersome. My Ph.D. research focuses on democratizing content creation and improving content consumption experiences for everyday users. I achieve this by designing and evaluating interactive AI systems that serve as copilots, assisting users with tedious tasks. I explore various media modalities, such as video, audio, text, and images, and investigate how their interplay can address problems in individual modalities. This paper offers a comprehensive overview of my research agenda, including recent contributions, on-going progress, and future directions.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616707"}, {"primary_key": "1264788", "vector": [], "sparse_vector": [], "title": "SketchSearch: Fine-tuning Reference Maps to Create Exercises In Support of Video-based Learning for Surgeons.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Video-based surgical coaching involves mentors reviewing surgery footage with trainees. Although effective, its use is sporadic due to time constraints. We propose AI-augmented coaching through SketchSearch, allowing experts to create exercises with automated feedback in surgery videos for self-learning. Surgeons often seek specific scenes for teaching, relying on visual cues. SketchSearch simplifies this through a three-step process: key frame extraction, template reference maps creation via image segmentation, and fine-tuning for frame retrieval.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615816"}, {"primary_key": "1264789", "vector": [], "sparse_vector": [], "title": "EmTex: Prototyping Textile-Based Interfaces through An Embroidered Construction Kit.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Nianding Ye", "<PERSON><PERSON>", "Xiaohua Sun", "<PERSON><PERSON>"], "summary": "As electronic textiles have become more advanced in sensing, actuating, and manufacturing, incorporating smartness into fabrics has become of special interest to ubiquitous computing and interaction researchers and designers. However, innovating smart textile interfaces for numerous input and output modalities usually requires expert-level knowledge of specific materials, fabrication, and protocols. This paper presents EmTex, a construction kit based on embroidered textiles, patterned with dedicated sensing, actuating, and connecting components to facilitate the design and prototyping of smart textile interfaces. With machine embroidery, EmTex is compatible with a wide range of threads and underlay fabrics, proficient in various stitches to control the electric parameters, and capable of integrating versatile and reliable interaction functionalities with aesthetic patterns and precise designs. EmTex consists of 28 textile-based sensors, actuators, connectors, and displays, presented with standardized visual and tactile effects. Along with a visual programming tool, EmTex enables the prototyping of everyday textile interfaces for diverse life-living scenarios, that embody their touch input, and visual and haptic output properties. With EmTex, we conducted a workshop and invited 25 designers and makers to create freeform textile interfaces. Our findings revealed that EmTex helped the participants explore novel interaction opportunities with various smart textile prototypes. We also identified challenges EmTex shall face for practical use in promoting the design innovation of smart textiles.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606815"}, {"primary_key": "1264790", "vector": [], "sparse_vector": [], "title": "Interactive Flexible Style Transfer for Vector Graphics.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vector graphics are an industry-standard way to represent and share visual designs. Designers frequently source and incorporate styles from existing designs into their work. Unfortunately, popular design tools are not well suited for this task. We present VST, Vector Style Transfer, a novel design tool for flexibly transferring visual styles between vector graphics. The core of VST lies in leveraging automation while respecting designers' tastes and the subjectivity inherent to style transfer. In VST, designers tune a cross-design element correspondence and customize which style attributes to change. We report results from a user study in which designers used VST to control style transfer between several designs, including designs participants created with external tools beforehand. VST shows that enabling design correspondence tuning and customization is one way to support interactive, flexible style transfer.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606751"}, {"primary_key": "1264791", "vector": [], "sparse_vector": [], "title": "CurveCrafter: A System for Animated Curve Manipulation.", "authors": ["<PERSON>", "<PERSON>", "Haldean Brown", "Ilene L. E", "<PERSON>"], "summary": "Linework on 3D animated characters is an important aspect of stylized looks for films. We present CurveCrafter, a system allowing animators to create new lines on 3D models and to edit the shape and opacity of silhouette curves. Our tools allow users to draw, redraw, erase, edit and retime user created curves. Silhouette curves can have their shape edited or reverted, and their opacity erased or revealed. Our algorithm for propagating edits over tracked silhouette curves ensures temporal consistency even as curves expand and merge. Five professional animators used our system to animate lines on three shots with different characters. Additionally, the effects lead from the short film <PERSON> used our system to more easily recreate edits on a film shot. CurveCrafter was able to successfully enhance the resulting animations with additional linework.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606792"}, {"primary_key": "1264792", "vector": [], "sparse_vector": [], "title": "AR-Enhanced Workouts: Exploring Visual Cues for At-Home Workout Videos in AR Environment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, with growing health consciousness, at-home workout has become increasingly popular for its convenience and safety. Most people choose to follow video guidance during exercising. However, our preliminary study revealed that fitness-minded people face challenges when watching exercise videos on handheld devices or fixed monitors, such as limited movement comprehension due to static camera angles and insufficient feedback. To address these issues, we reviewed popular workout videos, identified user requirements, and came up with an augmented reality (AR) solution. Following a user-centered iterative design process, we proposed a design space of AR visual cues for workouts and implemented an AR-based application. Specifically, we captured users' exercise performance with pose-tracking technology and provided feedback via AR visual cues. Two user experiments showed that incorporating AR visual cues could improve movement comprehension and enable users to adjust their movements based on real-time feedback. Finally, we presented several suggestions to inspire future design and apply AR visual cues to sports training.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606796"}, {"primary_key": "1264793", "vector": [], "sparse_vector": [], "title": "Never-ending Learning of User Interfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning models have been trained to predict semantic information about user interfaces (UIs) to make apps more accessible, easier to test, and to automate. Currently, most models rely on datasets of static screenshots that are labeled by human annotators, a process that is costly and surprisingly error-prone for certain tasks. For example, workers labeling whether a UI element is \"tappable\" from a screenshot must guess using visual signifiers, and do not have the benefit of tapping on the UI element in the running app and observing the effects. In this paper, we present the Never-ending UI Learner, an app crawler that automatically installs real apps from a mobile app store and crawls them to infer semantic properties of UIs by interacting with UI elements, discovering new and challenging training examples to learn from, and continually updating machine learning models designed to predict these semantics. The Never-ending UI Learner so far has crawled for more than 5,000 device-hours, performing over half a million actions on 6,000 apps to train three computer vision models for i) tappability prediction, ii) draggability prediction, and iii) screen similarity.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606824"}, {"primary_key": "1264794", "vector": [], "sparse_vector": [], "title": "FFL: A Language and Live Runtime for Styling and Labeling Typeset Math Formulas.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>a <PERSON>", "<PERSON>"], "summary": "As interest grows in learning math concepts in fields like data science and machine learning, it is becoming more important to help broad audiences engage with math notation. In this paper, we explore how authoring tools can help authors better style and label formulas to support their readability. We introduce a markup language for augmenting formulas called FFL, or \"Formula Formatting Language,\" which aims to lower the threshold to stylize and diagram formulas. The language is designed to be concise, writable, readable, and integrable into web-based document authoring environments. It was developed with an accompanying runtime that supports live application of augmentations to formulas. Our lab study shows that FFL improves the speed and ease of editing augmentation markup, and the readability of augmentation markup compared to baseline LaTeX tools. These results clarify the role tooling can play in supporting the explanation of math notation.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606731"}, {"primary_key": "1264795", "vector": [], "sparse_vector": [], "title": "RealityCanvas: Augmented Reality Sketching for Embedded and Responsive Scribble Animation Effects.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Kyzyl Monteiro", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce RealityCanvas, a mobile AR sketching tool that can easily augment real-world physical motion with responsive hand-drawn animation. Recent research in AR sketching tools has enabled users to not only embed static drawings into the real world but also dynamically animate them with physical motion. However, existing tools often lack the flexibility and expressiveness of possible animations, as they primarily support simple line-based geometry. To address this limitation, we explore both expressive and improvisational AR sketched animation by introducing a set of responsive scribble animation techniques that can be directly embedded through sketching interactions: 1) object binding, 2) flip-book animation, 3) action trigger, 4) particle effects, 5) motion trajectory, and 6) contour highlight. These six animation effects were derived from the analysis of 172 existing video-edited scribble animations. We showcase these techniques through various applications, such as video creation, augmented education, storytelling, and AR prototyping. The results of our user study and expert interviews confirm that our tool can lower the barrier to creating AR-based sketched animation, while allowing creative, expressive, and improvisational AR sketching experiences.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606716"}, {"primary_key": "1264796", "vector": [], "sparse_vector": [], "title": "CrossTalk: Intelligent Substrates for Language-Oriented Interaction in Video-Based Communication and Collaboration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the advances and ubiquity of digital communication media such as videoconferencing and virtual reality, they remain oblivious to the rich intentions expressed by users. Beyond transmitting audio, videos, and messages, we envision digital communication media as proactive facilitators that can provide unobtrusive assistance to enhance communication and collaboration. Informed by the results of a formative study, we propose three key design concepts to explore the systematic integration of intelligence into communication and collaboration, including the panel substrate, language-based intent recognition, and lightweight interaction techniques. We developed CrossTalk, a videoconferencing system that instantiates these concepts, which was found to enable a more fluid and flexible communication and collaboration experience.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606773"}, {"primary_key": "1264797", "vector": [], "sparse_vector": [], "title": "AutoSurveyGPT: GPT-Enhanced Automated Literature Discovery.", "authors": ["<PERSON>"], "summary": "In this work, we introduce AutoSurveyGPT, a novel framework for literature discovery. Designed to accommodate brief user-provided descriptions of academic papers, ideas, or proposals, this system is capable of autonomously extracting keywords for subsequent exploration within scholarly search engines. By leveraging large language model like GPT-4, the system further evaluates the relevance of the retrieved papers to the user-provided idea. This process, based on examining the introduction and related work sections, drives a repeating cycle of creating new keywords and finding more papers. The system generates a list of related papers, effectively aiding researchers in their search for relevant work. The open-source code for this tool is available on GitHub https://github.com/a554b554/AutoSurveyGPT.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616648"}, {"primary_key": "1264798", "vector": [], "sparse_vector": [], "title": "Wakey-Wakey: Animate Text by Mimicking Characters in a GIF.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With appealing visual effects, kinetic typography (animated text) has prevailed in movies, advertisements, and social media. However, it remains challenging and time-consuming to craft its animation scheme. We propose an automatic framework to transfer the animation scheme of a rigid body on a given meme GIF to text in vector format. First, the trajectories of key points on the GIF anchor are extracted and mapped to the text's control points based on local affine transformation. Then the temporal positions of the control points are optimized to maintain the text topology. We also develop an authoring tool that allows intuitive human control in the generation process. A questionnaire study provides evidence that the output results are aesthetically pleasing and well preserve the animation patterns in the original GIF, where participants were impressed by a similar emotional semantics of the original GIF. In addition, we evaluate the utility and effectiveness of our approach through a workshop with general users and designers.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606813"}, {"primary_key": "1264799", "vector": [], "sparse_vector": [], "title": "Future Paradigms for Sustainable Making.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This workshop provides the first opportunity for the UIST community to discuss sustainability challenges and opportunities in rapid prototyping. We will discuss key issues such as waste generation from intermediate prototypes, strategies for sustainable materials, circular prototyping (e.g., promoting re-use of components), knowledge sharing infrastructures (e.g., open-sourcing hardware), and so forth. The goal is to identify potential HCI research directions that can foster a more sustainable \"making\" environment inside of labs and beyond.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3617433"}, {"primary_key": "1264800", "vector": [], "sparse_vector": [], "title": "3D Printing Magnetophoretic Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a pipeline for printing interactive and always-on magnetophoretic displays using affordable Fused Deposition Modeling (FDM) 3D printers. Using our pipeline, an end-user can convert the surface of a 3D shape into a matrix of voxels. The generated model can be sent to an FDM 3D printer equipped with an additional syringe-based injector. During the printing process, an oil and iron powder-based liquid mixture is injected into each voxel cell, allowing the appearance of the once-printed object to be editable with external magnetic sources. To achieve this, we made modifications to the 3D printer hardware and the firmware. We also developed a 3D editor to prepare printable models. We demonstrate our pipeline with a variety of examples, including a printed Stanford bunny with customizable appearances, a small espresso mug that can be used as a post-it note surface, a board game figurine with a computationally updated display, and a collection of flexible wearable accessories with editable visuals.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606804"}, {"primary_key": "1264801", "vector": [], "sparse_vector": [], "title": "Demonstration of 3D Printed Magnetophoretic Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this demonstration, we showcase a suite of 3D printed magnetophoretic displays using the techniques proposed in [8]. These examples include a printed Stanford bunny with customizable appearances, a small espresso mug that can double as a post-it note surface, a board game figurine with a computationally updated display, and a collection of flexible wearable accessories with editable visuals. We will demonstrate the 3D printer design as well as the primary process of printing these magnetophoretic displays.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615771"}, {"primary_key": "1264802", "vector": [], "sparse_vector": [], "title": "XCreation: A Graph-based Crossmodal Generative Creativity Support Tool.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiang &apos;Anthony&apos; Chen"], "summary": "Creativity Support Tools (CSTs) aid in the efficient and effective composition of creative content, such as picture books. However, many existing CSTs only allow for mono-modal creation, whereas previous studies have become theoretically and technically mature to support multi-modal innovative creations. To overcome this limitation, we introduce XCreation, a novel CST that leverages generative AI to support cross-modal storybook creation. Nevertheless, directly deploying AI models to CSTs can still be problematic as they are mostly black-box architectures that are not comprehensible to human users. Therefore, we integrate an interpretable entity-relation graph to intuitively represent picture elements and their relations, improving the usability of the underlying generative structures. Our between-subject user study demonstrates that XCreation supports continuous plot creation with increased creativity, controllability, usability, and interpretability. XCreation is applicable to various scenarios, including interactive storytelling and picture book creation, thanks to its multimodal nature.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606826"}, {"primary_key": "1264803", "vector": [], "sparse_vector": [], "title": "ScentCarving: Fabricating Thin, Multi-layered and Paper-Based Scent Release through Laser Printing.", "authors": ["<PERSON><PERSON>", "<PERSON>yi <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differentiating from commonly-seen Olfactory Displays (ODs) that utilize rigid mechanisms within HCI, we propose ScentCarving, a controllable, lightweight, and flexible odor-release mechanism through a thin, multi-layered structure. ScentCarving consists of four layers, including the (1) paper-based substrate, (2) odor layer containing the ink-based aroma, (3) odor-sealing layer by introducing a thermoplastic material, and (4) a heating module using conductive cooper to soften the sealing layer and release the scent. ScentCarving also involves an easy-to-access Odor Printing technique capable of engraving fragrances onto thin and flexible substrates. We conducted a small-scale user study to test the scent-releasing behavior in terms of smell distance and responsive time.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616673"}, {"primary_key": "1264804", "vector": [], "sparse_vector": [], "title": "VegaProf: Profiling Vega Visualizations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Çaga<PERSON><PERSON>"], "summary": "Domain-specific languages (DSLs) for visualization aim to facilitate visualization creation by providing abstractions that offload implementation and execution details from users to the system layer. Therefore, DSLs often execute user-defined specifications by transforming them into intermediate representations (IRs) in successive lowering operations.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606790"}, {"primary_key": "1264805", "vector": [], "sparse_vector": [], "title": "CubeSense++: Smart Environment Sensing with Interaction-Powered Corner Reflector Mechanisms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart environment sensing provides valuable contextual information by detecting occurrences of events such as human activities and changes of object status, enabling computers to collect personal and environmental informatics to perform timely responses to user's needs. Conventional approaches either rely on tags that require batteries and frequent maintenance, or have limited detection capabilities bounded by only a few coarsely predefined activities. In response, this paper explores corner reflector mechanisms that encode user interactions with everyday objects into structured responses to millimeter wave radar, which has the potential for integration into smart environment entities such as speakers, light bulbs, thermostats, and autonomous vehicles. We presented the design space of 3D printed reflectors and gear mechanisms, which are low-cost, durable, battery-free, and can retrofit to a wide array of objects. These mechanisms convert the kinetic energy from user interactions into rotational motions of corner reflectors which we computationally designed with a genetic algorithm. We built an end-to-end radar detection pipeline to recognize fine-grained activity information such as state, direction, rate, count, and usage based on the characteristics of radar responses. We conducted studies for multiple instrumented objects in both indoor and outdoor environments, with promising results demonstrating the feasibility of the proposed approach.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606744"}, {"primary_key": "1264806", "vector": [], "sparse_vector": [], "title": "Demonstrating SuperMagneShape: Interactive Usage of a Passive Pin-Based Shape-Changing Display.", "authors": ["<PERSON><PERSON>"], "summary": "We propose SuperMagneShape, a method for creating interactive applications using MagneShape. MagneShape is a magnetically-actuated pin-based shape-changing display. It utilizes the forces produced by its magnetic components to control the levitation heights of passive magnetic pins, eliminating the need for an electric actuator for each pin. Although the MagneShape pin array is simple and inexpensive, rapid change of the magnetic pattern that drives the device is challenging. Proper display of shapes and characters requires appropriate magnetic patterns generated by a dedicated pattern generator and a time-consuming magnetization process. To minimize the complexity of the process, as well as the time taken between input and output, we designed a high-density pin array and a magnetic belt conveyor system. When the user handwrites a magnetic pattern in the shape of the letter \"A\" on a section of the magnetic belt, the imprinted magnetic pattern is conveyed under the high-density pin array and causes the pin array to display an \"A\" shape moving along it. We have also implemented games where the hand-drawn shape presents physical action as it is conveyed under the pin array.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615768"}, {"primary_key": "1264807", "vector": [], "sparse_vector": [], "title": "Edible Lenticular Lens Design System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Lenticular lenses are widely known as optical elements that change color depending on the viewing angle. By realizing this phenomenon in edible materials, it is possible to create a new gastronomic experience that significantly changes the appearance of food. In this study, we propose a system that supports the workflow from the design to the fabrication of edible lenticular lenses. The proposed system consists of lenticular lens design software and fabrication hardware. Users can design a visual effect of lenticular lenses by software simulation and fabricate the lenses by the knife cutting method using the hardware of the proposed system. In this study, the fabricated lenses were compared with the rendered ones. Furthermore, we confirm that the fabrication of the lenses is highly accurate and requires a short time.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616656"}, {"primary_key": "1264808", "vector": [], "sparse_vector": [], "title": "Skinergy: Machine-Embroidered Silicone-Textile Composites as On-Skin Self-Powered Input Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose Skinergy for self-powered on-skin input sensing, a step towards prolonged on-skin device usages. In contrast to prior on-skin gesture interaction sensors, Skinergy’s sensor operation does not require external power. Enabled by the triboelectric nanogenerator (TENG) phenomenon, the machine-embroidered silicone-textile composite sensor converts mechanical energy from the input interaction into electrical energy. Our proof-of-concept untethered sensing system measures the voltages of generated electrical signals which are then processed for a diverse set of sensing tasks: discrete touch detection, multi-contact detection, contact localization, and gesture recognition. Skinergy is fabricated with off-the-shelf materials. The aesthetic and functional designs can be easily customized and digitally fabricated. We characterize Skinergy and conduct a 10-participant user study to (1) evaluate its gesture recognition performance and (2) probe user perceptions and potential applications. Skinergy achieves 92.8% accuracy for a 11-class gesture recognition task. Our findings reveal that human factors (e.g., individual differences in skin properties, and aesthetic preferences) are key considerations in designing self-powered on-skin sensors for human inputs.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606729"}, {"primary_key": "1264809", "vector": [], "sparse_vector": [], "title": "CriTrainer: An Adaptive Training Tool for Critical Paper Reading.", "authors": ["Kangyu Yuan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning to read scientific papers critically, which requires first grasping their main ideas and then raising critical thoughts, is important yet challenging for novice researchers. The traditional ways to develop critical paper reading (CPR) skills, e.g., checking general tutorials or taking reading courses, often can not provide individuals with adaptive and accessible support. In this paper, we first derive user requirements of a CPR training tool based on literature and a survey study (N=52). Then, we develop CriTrainer , an interactive tool for CPR training. It leverages text summarization techniques to train readers' skills in grasping the paper's main ideas. It further utilizes template-based generated questions to help them learn how to raise critical thoughts. A mixed-design study (N=24) shows that compared to a baseline tool with general CPR guidance, students trained by <PERSON>riTrainer perform better in independently raising critical thinking questions on a new paper. We conclude with design considerations for CPR training tools.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606816"}, {"primary_key": "1264810", "vector": [], "sparse_vector": [], "title": "Learning Custom Experience Ontologies via Embedding-based Feedback Loops.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Organizations increasingly rely on behavioral analytics tools like Google Analytics to monitor their digital experiences. Making sense of the data these tools capture, however, requires manual event tagging and filtering — often a tedious process. Prior approaches have trained machine learning models to automatically tag interaction data, but draw from fixed digital experience vocabularies which cannot be easily augmented or customized. This paper introduces a novel machine learning interaction pattern that generates customized tag predictions for organizations. The approach employs a general user experience word embedding to bootstrap an initial set of predictions, which can then be refined and customized by users to adapt the underlying vector space, iteratively improving the quality of future predictions. The paper presents a needfinding study that grounds the design choices of the system, and describes a real-world deployment as part of UserTesting.com that demonstrates the efficacy of the approach.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606715"}, {"primary_key": "1264811", "vector": [], "sparse_vector": [], "title": "Towards Image Design Space Exploration in Spreadsheets with LLM Formulae.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>-<PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Users of Text-to-Image (TTI) models like DALL•E and Stable Diffusion typically engage in a lot of iteration, exploring a design space with two main inputs: (1) prompt text spanning image content and style; and (2) stochastic (e.g., random seeds) and other opaque (e.g., classifier-free guidance) variables. Here, we demo an early prototype interface using a spreadsheet metaphor to enable exploration and display of multiple input changes simultaneously, and affording prompt-crafting using spreadsheet formula construction. New LLM-based functions aid rapid exploration of the prompt text input space, by generating new variations on existing prompts and context-relevant lists of prompt keyword options.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615790"}, {"primary_key": "1264812", "vector": [], "sparse_vector": [], "title": "PoseVEC: Authoring Adaptive Pose-aware Effects using Visual Programming and Demonstrations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Pose-aware visual effects where graphics assets and animations are rendered reactively to the human pose have become increasingly popular, appearing on mobile devices, the web, or even head-mounted displays like AR glasses. Yet, creating such effects still remains difficult for novices. In a traditional video editing workflow, a creator could utilize keyframes to create expressive but non-adaptive results which cannot be reused for other videos. Alternatively, programming-based approaches allow users to develop interactive effects, but are cumbersome for users to quickly express their creative intents. In this work, we propose a lightweight visual programming workflow for authoring adaptive and expressive pose effects. By combining a programming by demonstration paradigm with visual programming, we simplify three key tasks in the authoring process: creating pose triggers, designing animation parameters, and rendering. We evaluated our system with a qualitative user study and a replicated example study, finding that all participants can create effects efficiently.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606788"}, {"primary_key": "1264813", "vector": [], "sparse_vector": [], "title": "FocusFlow: Leveraging Focal Depth for Gaze Interaction in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Current gaze input methods for VR headsets predominantly utilize the gaze ray as a pointing cursor, often neglecting depth information in it. This study introduces FocusFlow, a novel gaze interaction technique that integrates focal depth into gaze input dimensions, facilitating users to actively shift their focus along the depth dimension for interaction. A detection algorithm to identify the user's focal depth is developed. Based on this, a layer-based UI is proposed, which uses focal depth changes to enable layer switch operations, offering an intuitive hands-free selection method. We also designed visual cues to guide users to adjust focal depth accurately and get familiar with the interaction process. Preliminary evaluations demonstrate the system's usability, and several potential applications are discussed. Through FocusFlow, we aim to enrich the input dimensions of gaze interaction, achieving more intuitive and efficient human-computer interactions on headset devices.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615818"}, {"primary_key": "1264814", "vector": [], "sparse_vector": [], "title": "VISAR: A Human-AI Argumentative Writing Assistant with Visual Programming and Rapid Draft Prototyping.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "In argumentative writing, writers must brainstorm hierarchical writing goals, ensure the persuasiveness of their arguments, and revise and organize their plans through drafting. Recent advances in large language models (LLMs) have made interactive text generation through a chat interface (e.g., ChatGPT) possible. However, this approach often neglects implicit writing context and user intent, lacks support for user control and autonomy, and provides limited assistance for sensemaking and revising writing plans. To address these challenges, we introduce VISAR, an AI-enabled writing assistant system designed to help writers brainstorm and revise hierarchical goals within their writing context, organize argument structures through synchronized text editing and visual programming, and enhance persuasiveness with argumentation spark recommendations. VISAR allows users to explore, experiment with, and validate their writing plans using automatic draft prototyping. A controlled lab study confirmed the usability and effectiveness of VISAR in facilitating the argumentative writing planning process.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606800"}, {"primary_key": "1264815", "vector": [], "sparse_vector": [], "title": "PEANUT: A Human-AI Collaborative Tool for Annotating Audio-Visual Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Audio-visual learning seeks to enhance the computer's multi-modal perception leveraging the correlation between the auditory and visual modalities. Despite their many useful downstream tasks, such as video retrieval, AR/VR, and accessibility, the performance and adoption of existing audio-visual models have been impeded by the availability of high-quality datasets. Annotating audio-visual datasets is laborious, expensive, and time-consuming. To address this challenge, we designed and developed an efficient audio-visual annotation tool called Peanut. Peanut's human-AI collaborative pipeline separates the multi-modal task into two single-modal tasks, and utilizes state-of-the-art object detection and sound-tagging models to reduce the annotators' effort to process each frame and the number of manually-annotated frames needed. A within-subject user study with 20 participants found that <PERSON><PERSON> can significantly accelerate the audio-visual data annotation process while maintaining high annotation accuracy.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606776"}, {"primary_key": "1264816", "vector": [], "sparse_vector": [], "title": "Haptic Rendering of Neural Radiance Fields.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The neural radiance field (NeRF) is attracting increasing attentions from researchers in various fields. While NeRF has produced visually plausible results and found its potential applications in virtual reality, users are only allowed to rotate the camera to observe the scene represented as NeRF. We study the haptic interaction with NeRF models in this paper to enable the experience of touching objects reconstructed by NeRF. Existing haptic rendering algorithms do not work well for NeRF-represented models because NeRF is often noisy. We propose a stochastic haptic rendering method to deal with the collision response between the haptic proxy and NeRF. We validate our method with complex NeRF models and experimental results show the efficacy of our proposed algorithm.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606811"}, {"primary_key": "1264817", "vector": [], "sparse_vector": [], "title": "Narratron: Collaborative Writing and Shadow-playing of Children Stories with Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shadow puppetry or shadow play, allows bodily participation into the process of linguistic storytelling, while the potential of multi-modal interaction through shadow plays in existing large-language-model-based creative tools has not been fully discovered. We propose Narratron, a generative story-making tool that co-creates and co-performs children stories from shadow using Claude 2 model. To achieve Narratron, our system is designed to recognize hand gestural inputs as main character and to develop story plot in accordance with character change. Through our system, we seek to stimulate creativity in shadow play storytelling and to facilitate a multi-modal human-AI collaboration.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625120"}, {"primary_key": "1264818", "vector": [], "sparse_vector": [], "title": "Towards Trauma-Informed Data Donation of Sexual Experience in Online Dating to Improve Sexual Risk Detection AI.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sexual risk detection AI has been touted as a scalable solution for computer-mediated sexual violence. Data donation is a user-centered approach to producing ecologically valid datasets for sexual risk detection AI: voluntarily providing personal data that is representative of risk. However, the act of donating intimate sexual experience data can itself be traumatizing. We propose Ube: a trauma-informed sexual experience data donation app for online daters that is developed jointly with sexual violence experts and care practitioners. Cognitive walkthroughs of Ube with these experts elucidated several design approaches to mitigate retraumatization during data donation, including a conversational agent and mental health checks.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616689"}, {"primary_key": "1264819", "vector": [], "sparse_vector": [], "title": "Demonstration of ChromoCloth: Re-Programmable Multi-Color Textures through Flexible and Portable Light Source.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this demo, we present ChromoCloth, a flexible and portable light source for reprogrammable multi-color texture on photochromic objects, whose color can be reprogrammed with external light sources. While prior work used external projectors to trigger the color change, ChromoCloth initiates the color change by covering the object. ChromoCloth consists of a textile substrate, 3D printed diffusive housing glued on top of the substrate and a flexible LED strip that is weaved through the housings.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3615811"}, {"primary_key": "1264820", "vector": [], "sparse_vector": [], "title": "BioWeave: Weaving Thread-Based Sweat-Sensing On-Skin Interfaces.", "authors": ["<PERSON><PERSON>", "Nadine El Nesr", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "There has been a growing interest in developing and fabricating wearable sweat sensors in recent years, as sweat contains various analytes that can provide non-invasive indications of various conditions in the body. Although recent HCI research has been looking into wearable sensors for understanding health conditions, textile-based wearable sweat sensors remain underexplored. We present BioWeave, a woven thread-based sweat-sensing on-skin interface. Through weaving single-layer and multi-layer structures, we combine sweat-sensing threads with versatile fiber materials. We identified a design space consisting of colorimetric and electrochemical sensing approaches, targeting biomarkers including pH, glucose, and electrolytes. We explored 2D and 3D weaving structures for underexplored body locations to seamlessly integrate sweat-sensing thread into soft wearable interfaces. We developed five example applications to demonstrate the design capability offered. The BioWeave sensing interface can provide seamless integration into everyday textile-based wearables and offers the unobtrusive analysis of health conditions.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586183.3606769"}, {"primary_key": "1264821", "vector": [], "sparse_vector": [], "title": "Docent: Digital Operation-Centric Elicitation of Novice-friendly Tutorials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays, novice users often turn to digital tutorials for guidance in software. However, searching and utilizing the tutorial remains a challenge due to the request for proper problem articulation, extensive searches and mind-intensive follow-through. We introduce \"Docent\", a system designed to bridge this knowledge-seeking gap. Powered by Large Language Models (LLMs), Docent takes vague user input and recent digital operation contexts to reason, seek, and present the most relevant tutorials in-situ. We assume that <PERSON><PERSON> smooths the user experience and facilitates learning of the software.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3625121"}, {"primary_key": "1264822", "vector": [], "sparse_vector": [], "title": "iTutor: A Generative Tutorial System for Teaching the Elders to Use Smartphone Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present iTutor, a generative tutorial system for promoting smartphone use proficiency among elders. iTutor is unique because it can dynamically generate tutorials based on current operation goals and UI context, which we achieved through leveraging prompt engineering to large language models (LLMs). Our evaluations showed potential for this approach, as we yielded 78.6% accuracy in the instruction generation process. We conclude by providing the roadmap for further development.", "published": "2023-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3586182.3616663"}]