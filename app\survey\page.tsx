'use client';

import { useState, useEffect, useRef } from 'react';
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { MessageSquare } from "lucide-react"
import { Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Paper } from '@/components/survey/class-utils';
import { Toaster } from 'react-hot-toast';

import { useCollectionManager, CollectionSidebar, CollectionState } from "@/components/collect/collect";
import { useSearch } from "@/components/survey/use-search";
import { useFilters } from "@/components/survey/use-filters";
import { SquareFunction } from "lucide-react";

import AnnouncementBar from "@/components/survey/announcement-bar"
import PaperModal from "@/components/survey/paper-modal"
import GridPaper from "@/components/survey/grid-paper";
import WechatPopover from '@/components/survey/wechat-popover';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import MiniChat from "@/components/minichat/minichat";
import ConferenceInfo from "@/components/survey/conference-info"
import SearchBar from "@/components/survey/search-bar";
import FormulaEditorModal from "@/components/latex/FormulaEditorModal";
import DonatePopover from '@/components/survey/donate-popover';

export default function SurveyPage() {
  const [selectedPaper, setSelectedPaper] = useState<Paper | null>(null);
  
  // 使用筛选器 hook
  const {
    selectedYears,
    setSelectedYears,
    isYearsOpen,
    setIsYearsOpen,
    sourceType,
    setSourceType,
    selectedConferences,
    setSelectedConferences,
    selectedJournals,
    setSelectedJournals,
    isConferencesOpen,
    setIsConferencesOpen,
    isJournalsOpen,
    setIsJournalsOpen,
    isMoreOptionsOpen,
    setIsMoreOptionsOpen,
    yearOptions,
    handleYearChange,
    handleConferenceChange,
    handleJournalChange,
    getSourceParam
  } = useFilters();

  // 使用搜索 hook
  const {
    searchQuery,
    setSearchQuery,
    searchResults,
    setSearchResults,
    isSearching,
    searchState,
    isTitleTranslation,
    setIsTitleTranslation,
    handleSearch
  } = useSearch({ selectedYears, getSourceParam });

  // 添加收藏栏相关状态
  const { 
    isCollectionOpen, 
    toggleCollection, 
    isPaperCollected, 
    addToCollection, 
    removeFromCollection,
    collection,
    updateCollection
  } = useCollectionManager();

  // 排序相关状态
  const [sortOrder, setSortOrder] = useState<'relevance' | 'date'>('relevance');
  const [sortedIndices, setSortedIndices] = useState<number[]>([]);
  const [viewedPapers, setViewedPapers] = useState<Set<string>>(new Set());

  // 引用
  const yearSelectorRef = useRef<HTMLDivElement>(null);
  const conferencesSelectorRef = useRef<HTMLDivElement>(null);
  const journalsSelectorRef = useRef<HTMLDivElement>(null);
  const sourceSelectorRef = useRef<HTMLDivElement>(null);
  const [isSourceSelectorOpen, setIsSourceSelectorOpen] = useState(false);

  // 修改年份/会议/期刊多选下拉框点击外部关闭的效果
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (yearSelectorRef.current && !yearSelectorRef.current.contains(event.target as Node)) {
        if (isYearsOpen) {
          setIsYearsOpen(false);
        }
      }
      
      if (conferencesSelectorRef.current && !conferencesSelectorRef.current.contains(event.target as Node)) {
        if (isConferencesOpen) {
          setIsConferencesOpen(false);
        }
      }

      if (journalsSelectorRef.current && !journalsSelectorRef.current.contains(event.target as Node)) {
        if (isJournalsOpen) {
          setIsJournalsOpen(false);
        }
      }

      if (sourceSelectorRef.current && !sourceSelectorRef.current.contains(event.target as Node)) {
        if (isSourceSelectorOpen) {
          setIsSourceSelectorOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isYearsOpen, isConferencesOpen, isJournalsOpen, isSourceSelectorOpen]);

  // 在 useEffect 中初始化 localStorage 相关的状态
  useEffect(() => {
    // 初始化已查看的论文
    const storedPapers = localStorage.getItem('viewedPapers');
    if (storedPapers) {
      const { papers, timestamp } = JSON.parse(storedPapers);
      // 检查是否过期（一年）
      if (Date.now() - timestamp < 365 * 24 * 60 * 60 * 1000) {
        setViewedPapers(new Set(papers));
      }
    }
  }, []);

  const handlePaperClick = (paper: Paper) => {
    setSelectedPaper(paper);
    setViewedPapers(prev => {
      const newSet = new Set(prev);
      newSet.add(paper.title);
      localStorage.setItem('viewedPapers', JSON.stringify({
        papers: Array.from(newSet),
        timestamp: Date.now()
      }));
      return newSet;
    });
  };

  // 在组件中添加 useEffect 来处理排序
  useEffect(() => {
    if (searchResults.length === 0) {
      setSortedIndices([]);
      return;
    }
    
    const indices = Array.from({ length: searchResults.length }, (_, i) => i);
    if (sortOrder === 'date') {
      indices.sort((a, b) => {
        const dateA = searchResults[a].published.split('-').slice(0, 3).join('-');
        const dateB = searchResults[b].published.split('-').slice(0, 3).join('-');
        return new Date(dateB).getTime() - new Date(dateA).getTime();
      });
    } 
    setSortedIndices(indices);
  }, [searchResults, sortOrder]);

  const { theme, setTheme } = useTheme();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatWidth, setChatWidth] = useState(600);
  const [isMobile, setIsMobile] = useState(false);
  const [collectionWidth, setCollectionWidth] = useState(300);

  // 检测屏幕宽度 - 只在客户端执行
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 处理收藏/取消收藏
  const handleCollect = async (paper: Paper) => {
    if (isPaperCollected(paper.title)) {
      removeFromCollection(paper.title);
    } else {
      await addToCollection(paper);
    }
  };

  // 处理收藏更新
  const handleCollectionUpdate = (updatedCollection: CollectionState) => {
    updateCollection(updatedCollection);
  };

  const [isConferenceInfoOpen, setIsConferenceInfoOpen] = useState(false);
  const [isFormulaModalOpen, setIsFormulaModalOpen] = useState(false);
  const [latexContent, setLatexContent] = useState("");

  return (
    <div className="flex flex-col min-h-screen">
      <AnnouncementBar />
      <Toaster />
      <div className="flex p-7 lg:p-14 flex-grow">


      {/* 添加主题切换按钮、会议信息按钮、公式编辑器按钮 */}
      <div className="fixed top-4 right-4 flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsFormulaModalOpen(true)}
          // className="ml-2"
        >
          <SquareFunction className="h-4 w-4" />
          <span className="sr-only">公式编辑器</span>
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsConferenceInfoOpen(true)}
        >
          <Info className="size-8" />
          <span className="sr-only">会议信息</span>
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
        >
          <Sun className="size-8 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute size-8 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">切换主题</span>
        </Button>
      </div>

      {/* 添加悬浮的聊天按钮 */}
      <div className={`fixed bottom-8 right-8 z-50 ${isChatOpen ? 'hidden' : 'block'} ${isCollectionOpen && isMobile ? 'hidden' : ''}`}>
        <Button
          variant="default"
          size="icon"
          onClick={() => setIsChatOpen(!isChatOpen)}
          className="rounded-full shadow-lg hover:shadow-xl transition-all duration-300 w-12 h-12"
        >
          <MessageSquare className="size-8" />
          <span className="sr-only">打开聊天</span>
        </Button>
      </div>

      {/* 添加悬浮的收藏按钮（仅在移动端且聊天未打开时显示） */}
      <div className={`fixed bottom-8 left-8 z-50 ${isCollectionOpen ? 'hidden' : 'block'} ${isChatOpen && isMobile ? 'hidden' : ''} ${!isMobile ? 'hidden' : ''}`}>
        <Button
          variant="default"
          size="icon"
          onClick={toggleCollection}
          className="rounded-full shadow-lg hover:shadow-xl transition-all duration-300 w-12 h-12"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
          </svg>
          <span className="sr-only">打开收藏</span>
        </Button>
      </div>

      {/* 收藏栏组件 */}
      <CollectionSidebar
        isOpen={isCollectionOpen}
        onToggle={toggleCollection}
        addToCollection={addToCollection}
        isPaperCollected={isPaperCollected}
        collection={collection}
        onCollectionUpdate={handleCollectionUpdate}
        width={collectionWidth}
        onWidthChange={setCollectionWidth}
        isMobile={isMobile}
      />
      
      {/* 主内容区域 */}
      <div 
        className={`flex flex-col items-center w-full ml-auto mr-auto transition-all duration-300 
          ${isChatOpen && !isMobile ? 'mr-[400px]' : ''} 
          ${isChatOpen && isMobile ? 'hidden' : 'block'} 
          ${isCollectionOpen && !isMobile ? 'ml-[300px]' : ''} 
          ${isCollectionOpen && isMobile ? 'hidden' : 'block'}`
        } 
        style={{ 
          marginRight: isChatOpen && !isMobile ? `${chatWidth}px` : '0',
          marginLeft: isCollectionOpen && !isMobile ? `${collectionWidth}px` : '0'
        }}
      >
        <div className={`flex flex-col justify-center items-center py-4 ${searchResults.length > 0 ? 'mb-0' : 'mb-[24vh]'} mt-[16vh] w-full`}>

          {/* 标题 */}
          <p className="text-center font-bold text-3xl lg:text-6xl mt-2">
            arXiv Insight
          </p>
          <p className="text-center font-bold text-md mb-8">
            多 LLM 驱动的论文语义检索
          </p>

          {/* 搜索栏组件 */}
          <SearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            isSearching={isSearching}
            searchState={searchState}
            isTitleTranslation={isTitleTranslation}
            setIsTitleTranslation={setIsTitleTranslation}
            handleSearch={handleSearch}
            sourceType={sourceType}
            setSourceType={setSourceType}
            selectedYears={selectedYears}
            yearOptions={yearOptions}
            handleYearChange={handleYearChange}
            isYearsOpen={isYearsOpen}
            setIsYearsOpen={setIsYearsOpen}
            selectedConferences={selectedConferences}
            handleConferenceChange={handleConferenceChange}
            isConferencesOpen={isConferencesOpen}
            setIsConferencesOpen={setIsConferencesOpen}
            selectedJournals={selectedJournals}
            handleJournalChange={handleJournalChange}
            isJournalsOpen={isJournalsOpen}
            setIsJournalsOpen={setIsJournalsOpen}
            isMoreOptionsOpen={isMoreOptionsOpen}
            setIsMoreOptionsOpen={setIsMoreOptionsOpen}
            yearSelectorRef={yearSelectorRef}
            conferencesSelectorRef={conferencesSelectorRef}
            journalsSelectorRef={journalsSelectorRef}
            sourceSelectorRef={sourceSelectorRef}
            isSourceSelectorOpen={isSourceSelectorOpen}
            setIsSourceSelectorOpen={setIsSourceSelectorOpen}
          />

          {/* 排序选择器 */}
          {searchResults.length > 0 && (
          <div className="max-w-6xl w-full mx-auto my-4 ">
            <div className="flex items-center gap-2 ">
              <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as 'relevance' | 'date')}>
                <SelectTrigger className="w-[140px] h-10 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-800">
                  <SelectValue placeholder="选择排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">按相关度排序</SelectItem>
                  <SelectItem value="date">按日期排序</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>)}

          {/* 论文卡片 */}
          <div className={`max-w-6xl w-full mx-auto`}>
            <div className="grid gap-5 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {(sortedIndices.length > 0 ? sortedIndices : searchResults.map((_, i) => i)).map((index) => {
                const paper = searchResults[index];
                return (
                  <GridPaper
                    key={`${paper?.title ?? ''}-${index}`}
                    paper={paper}
                    onPaperClick={handlePaperClick}
                    isViewed={viewedPapers.has(paper?.title ?? '')}
                    index={index}
                    isTitleTranslation={isTitleTranslation}
                    onCollect={handleCollect}
                    isCollected={isPaperCollected(paper?.title ?? '')}
                  />
                );
              })}
            </div>
          </div>
        </div>

        {/* 备案号 + 服务提供商 + 交流群 + 打赏 */}
        <div className={`${searchResults.length === 0 ? 'mt-auto' : 'mt-4'} flex flex-col items-center py-2 ${searchResults.length === 0 ? 'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60' : ''}`}>
          <div className="flex items-center space-x-2 text-sm">
            <a 
              href="https://beian.miit.gov.cn/" 
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-gray-700"
            >
              豫ICP备2025106078号-1
            </a>
            {/* <div className="hidden md:flex items-center space-x-2"> */}
            <div className="flex items-center space-x-2">
              <span className="text-black-500 text-sm hover:text-gray-700">
                |
              </span>  
              <WechatPopover />
              <span className="text-black-500 text-sm hover:text-gray-700">
                |
              </span>
              <DonatePopover />
            </div>
          </div>
        </div>
      </div>

      {/* 聊天对话框 */}
      <MiniChat 
        papers={searchResults} 
        isOpen={isChatOpen} 
        onClose={() => setIsChatOpen(false)}
        width={chatWidth}
        onWidthChange={setChatWidth}
        searchQuery={searchQuery}
      />

      {/* 弹窗 */}
      {selectedPaper && (
        <PaperModal 
          paper={selectedPaper} 
          onClose={() => setSelectedPaper(null)}
        />
      )}

      {/* 会议信息弹窗 */}
      <ConferenceInfo 
        isOpen={isConferenceInfoOpen} 
        onClose={() => setIsConferenceInfoOpen(false)} 
      />

      {/* 公式编辑器弹窗 */}
      <FormulaEditorModal
        isOpen={isFormulaModalOpen}
        onClose={() => setIsFormulaModalOpen(false)}
        latex={latexContent}
        setLatex={setLatexContent}
      />
      </div>
    </div>
  );
}
