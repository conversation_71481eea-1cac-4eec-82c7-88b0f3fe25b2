[{"primary_key": "3004579", "vector": [], "sparse_vector": [], "title": "Ring Signatures: Logar<PERSON><PERSON>-<PERSON><PERSON>, No Setup - from Standard Assumptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ring signatures allow for creating signatures on behalf of an ad hoc group of signers, hiding the true identity of the signer among the group. A natural goal is to construct a ring signature scheme for which the signature size is short in the number of ring members. Moreover, such a construction should not rely on a trusted setup and be proven secure under falsifiable standard assumptions. Despite many years of research this question is still open. In this paper, we present the first construction of size-optimal ring signatures which do not rely on a trusted setup or the random oracle heuristic. Specifically, our scheme can be instantiated from standard assumptions and the size of signatures grows only logarithmically in the number of ring members. We also extend our techniques to the setting of linkable ring signatures, where signatures created using the same signing key can be linked.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_10"}, {"primary_key": "3004580", "vector": [], "sparse_vector": [], "title": "Improved Bootstrapping for Approximate Homomorphic Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since <PERSON><PERSON> et al. introduced a homomorphic encryption scheme for approximate arithmetic (Asiacrypt ’17), it has been recognized as suitable for important real-life usecases of homomorphic encryption, including training of machine learning models over encrypted data. A follow up work by <PERSON><PERSON> et al. (Eurocrypt ’18) described an approximate bootstrapping procedure for the scheme. In this work, we improve upon the previous bootstrapping result. We improve the amortized bootstrapping time per plaintext slot by two orders of magnitude, from\\(\\sim \\)1 s to\\(\\sim \\)0.01 s. To achieve this result, we adopt a smart level-collapsing technique for evaluating DFT-like linear transforms on a ciphertext. Also, we replace the Taylor approximation of the sine function with a more accurate and numerically stable Chebyshev approximation, and design a modified version of the Paterson-<PERSON> algorithm for fast evaluation of Chebyshev polynomials over encrypted data.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_2"}, {"primary_key": "3004581", "vector": [], "sparse_vector": [], "title": "Reversible Proofs of Sequential Work.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Proofs of sequential work (PoSW) are proof systems where a prover, upon receiving a statement\\(\\chi \\)and a time parameterTcomputes a proof\\(\\phi (\\chi ,T)\\)which is efficiently and publicly verifiable. The proof can be computed inTsequential steps, but not much less, even by a malicious party having large parallelism. A PoSW thus serves as a proof thatTunits of time have passed since\\(\\chi \\)was received. PoSW were introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [MMV11], a simple and practical construction was only recently proposed by <PERSON> and <PERSON> [CP18]. In this work we construct a new simple PoSW in the random permutation model which is almost as simple and efficient as [CP18] but conceptually very different. Whereas the structure underlying [CP18] is a hash tree, our construction is based on skip lists and has the interesting property that computing the PoSW is a reversible computation. The fact that the construction is reversible can potentially be used for new applications like constructingproofs of replication. We also show how to “embed” the sloth function of <PERSON><PERSON> and <PERSON><PERSON><PERSON> [LW17] into our PoSW to get a PoSW where one additionally can verify correctness of the output much more efficiently than recomputing it (though recent constructions of “verifiable delay functions” subsume most of the applications this construction was aiming at).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_10"}, {"primary_key": "3004582", "vector": [], "sparse_vector": [], "title": "Uncovering Algebraic Structures in the MPC Landscape.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Anand", "<PERSON><PERSON><PERSON>"], "summary": "A fundamental problem in the theory of secure multi-party computation (MPC) is to characterize functions withmore than 2 partieswhich admit MPC protocols with information-theoretic security against passive corruption. This question has seen little progress since the work of <PERSON><PERSON> and <PERSON><PERSON> (1996), which demonstrated difficulties in resolving it. In this work, we make significant progress towards resolving this question in the important case of aggregating functionalities, in whichmparties\\(P_1,\\dots ,P_m\\)hold inputs\\(x_1,\\dots ,x_m\\)and an aggregating party\\(P_0\\)must learn\\(f(x_1,\\dots ,x_m)\\). We uncover a rich class of algebraic structures that are closely related to secure computability, namely, “Commuting Permutations Systems” (CPS) and its variants. We present an extensive set of results relating these algebraic structures among themselves and to MPC, including new protocols, impossibility results and separations. Our results include a necessary algebraic condition and slightly stronger sufficient algebraic condition for a function to admit information-theoretically secure MPC protocols. We also introduce and study new models of minimally interactive MPC (called UNIMPC and), which not only help in understanding our positive and negative results better, but also open up new avenues for studying the cryptographic complexity landscape of multi-party functionalities. Our positive results include novel protocols in these models, which may be of independent practical interest. Finally, we extend our results to a definition that requires UC security as well as semi-honest security (which we termstrong security). In this model we are able to carry out the characterization ofallcomputable functions, except for a gap in the case of aggregating functionalities.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_14"}, {"primary_key": "3004583", "vector": [], "sparse_vector": [], "title": "A Quantum-Proof Non-malleable Extractor - With Application to Privacy Amplification Against Active Quantum Adversaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In privacy amplification, two mutually trusted parties aim to amplify the secrecy of an initial shared secretXin order to establish a shared private keyKby exchanging messages over an insecure communication channel. If the channel is authenticated the task can be solved in a single round of communication using a strong randomness extractor; choosing a quantum-proof extractor allows one to establish security against quantum adversaries. In the case that the channel is not authenticated, this simple solution is no longer secure. Nevertheless, <PERSON><PERSON> and <PERSON><PERSON>s (STOC’09) showed that the problem can be solved in two rounds of communication using a non-malleable extractor, a stronger pseudo-random construction than a strong extractor. We give the first construction of a non-malleable extractor that is secure against quantum adversaries. The extractor is based on a construction by <PERSON> (FOCS’12), and is able to extract from source of min-entropy rates larger than 1 / 2. Combining this construction with a quantum-proof variant of the reduction of <PERSON><PERSON> and <PERSON><PERSON><PERSON>, due to <PERSON> and <PERSON> (unpublished) we obtain the first privacy amplification protocol secure against active quantum adversaries.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_16"}, {"primary_key": "3004584", "vector": [], "sparse_vector": [], "title": "Continuous Non-Malleable Codes in the 8-Split-State Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-malleable codes (NMCs), introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [20], provide a useful message integrity guarantee in situations where traditional error-correction (and even error-detection) is impossible; for example, when the attacker can completely overwrite the encoded message. NMCs have emerged as a fundamental object at the intersection of coding theory and cryptography. In particular, progress in the study of non-malleable codes and the related notion of non-malleable extractors has led to new insights and progress on even more fundamental problems like the construction of multi-source randomness extractors. A large body of the recent work has focused on various constructions of non-malleable codes in the split-state model. Many variants of NMCs have been introduced in the literature, e.g., strong NMCs, super strong NMCs and continuous NMCs. The most general, and hence also the most useful notion among these is that of continuous non-malleable codes, that allows for continuous tampering by the adversary. We present the first efficient information-theoretically secure continuously non-malleable code in the constant split-state model. We believe that our main technical result could be of independent interest and some of the ideas could in future be used to make progress on other related questions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_18"}, {"primary_key": "3004585", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation Without Multilinear Maps: New Methods for Bootstrapping and Instantiation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Constructing indistinguishability obfuscation (\\(\\mathsf{iO}\\)) [17] is a central open question in cryptography. We provide new methods to make progress towards this goal. Our contributions may be summarized as follows: Bootstrapping.In a recent work, <PERSON> and <PERSON><PERSON> [71] (LT) show that\\(\\mathsf{iO}\\)may be constructed using (i) Functional Encryption (\\(\\mathsf {FE}\\)) for polynomials of degreeL, (ii) Pseudorandom Generators (\\(\\mathsf{PRG}\\)) withblockwise localityLand polynomial expansion, and (iii) Learning With Errors (\\(\\mathsf{LWE}\\)). Since there exist constructions of\\(\\mathsf {FE}\\)for quadratic polynomials from standard assumptions on bilinear maps [16,68], the ideal scenario would be to set\\(L=2\\), yielding\\(\\mathsf{iO}\\)from widely believed assumptions Unfortunately, it was shown soon after [18,73] that\\(\\mathsf{PRG}\\)with block locality 2 and the expansion factor required by the LT construction, concretely\\(\\varOmega (n \\cdot 2^{b(3+\\epsilon )})\\), wherenis the input length andbis the block length, do not exist. In the worst case, these lower bounds rule out 2-block local\\(\\mathsf{PRG}\\)with stretch\\(\\varOmega (n \\cdot 2^{b(2+\\epsilon )})\\). While [18,73] provided strong negative evidence for constructing\\(\\mathsf{iO}\\)based on bilinear maps, they could not rule out the possibility completely; a tantalizing gap has remained. Given the current state of lower bounds, the existence of 2 block local\\(\\mathsf{PRG}\\)with expansion factor\\(\\varOmega (n \\cdot 2^{b(1+\\epsilon )})\\)remains open, although this stretch does not suffice for the LT bootstrapping, and is hence unclear to be relevant for\\(\\mathsf{iO}\\). In this work, we improve the state of affairs as follows. Weakening requirements on Boolean PRGs:In this work, we show that the narrow window of expansion factors left open by lower boundsdosuffice for\\(\\mathsf{iO}\\). We show a new method to construct\\(\\mathsf {FE}\\)for\\(\\mathsf {NC}_1\\)from (i)\\(\\mathsf {FE}\\)for degreeLpolynomials, (ii)\\(\\mathsf{PRG}\\)s of block localityLand expansion factor\\(\\tilde{\\varOmega }(n \\cdot 2^{b(1+\\epsilon )})\\), and (iii)\\(\\mathsf{LWE}\\)(or\\(\\mathsf{RLWE}\\)). Broadening class of sufficient randomness generators: Our bootstrapping theorem may be instantiated with a broader class of pseudorandom generators than hitherto considered for\\(\\mathsf{iO}\\), and may circumvent lower bounds known for the arithmetic degree of\\(\\mathsf{iO}\\)-sufficient\\(\\mathsf{PRG}\\)s [18,73]; in particular, these may admit instantiations with arithmetic degree 2, yielding\\(\\mathsf{iO}\\)with the additional assumptions of\\(\\mathsf{SXDH}\\)on Bilinear maps and\\(\\mathsf{LWE}\\). In more detail, we may use the following two classes of\\(\\mathsf{PRG}\\): Non-Boolean PRGs: We may use pseudorandom generators whose inputs and outputs need not be Boolean but may be integers restricted to a small (polynomial) range. Additionally, the outputs are not required to be pseudorandom but must only satisfy a milder indistinguishability property (We note that our notion of non Boolean PRGs is qualitatively similar to the notion of\\(\\varDelta \\)RGs defined in the concurrent work of Ananth, Jain and Sahai [9]. We emphasize that the methods of [9] and the present work are very different, but both works independently discover the same notion of weak PRG as sufficient for building\\(\\mathsf{iO}\\).). Correlated Noise Generators: We introduce an even weaker class of pseudorandom generators, which we call correlated noise generators (\\(\\mathsf{CNG}\\)) which may not only be non-Boolean but are required to satisfy an even milder (seeming) indistinguishability property than\\(\\varDelta \\)RG. Assumptions and Efficiency.Our bootstrapping theorems can be based on the hardness of the Learning With Errors problem or its ring variant (\\(\\mathsf{LWE}/ \\mathsf{RLWE}\\)) and can compile\\(\\mathsf {FE}\\)for degreeLpolynomials directly to\\(\\mathsf {FE}\\)for\\(\\mathsf {NC}_1\\). Previous work compiles\\(\\mathsf {FE}\\)for degreeLpolynomials to\\(\\mathsf {FE}\\)for\\(\\mathsf {NC}_0\\)to\\(\\mathsf {FE}\\)for\\(\\mathsf {NC}_1\\)to\\(\\mathsf{iO}\\)[12,45,68,72]. Our method for bootstrapping to\\(\\mathsf {NC}_1\\)does not go via randomized encodings as in previous works, which makes it simpler and more efficient than in previous works. Instantiating Primitives.In this work, we provide the first direct candidate of\\(\\mathsf {FE}\\)for constant degree polynomials from new assumptions on lattices. Our construction is new and does not go via multilinear maps or graded encoding schemes as all previous constructions. Together with the bootstrapping step above, this yields acompletely new candidate for\\(\\mathsf{iO}\\)(as well as\\(\\mathsf {FE}\\)for\\(\\mathsf {NC}_1\\)), which makes no use of multilinear or even bilinear maps. Our construction is based on the ring learning with errors assumption (\\(\\mathsf{RLWE}\\)) as well as new untested assumptions on NTRU rings. We provide a detailed security analysis and discuss why previously known attacks in the context of multilinear maps, especially zeroizing and annihilation attacks, do not appear to apply to our setting. We caution that our construction must yet be subject to rigorous cryptanalysis by the community before confidence can be gained in its security. However, we believe that the significant departure from known multilinear map based constructions opens up a new and potentially fruitful direction to explore in the quest for\\(\\mathsf{iO}\\). Our construction is based entirely on lattices, due to which one may hope for post quantum security. Note that this feature is not enjoyed by instantiations that make any use of bilinear maps even if secure instances of weak PRGs, as identified by the present work, the follow-up by Lin and Matt [69] and the independent work by Ananth, Jain and Sahai [9] are found.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_7"}, {"primary_key": "3004586", "vector": [], "sparse_vector": [], "title": "On Quantum Advantage in Information Theoretic Single-Server PIR.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Or Sattath"], "summary": "In (single-server) Private Information Retrieval (PIR), a server holds a large database\\({\\mathtt {DB}}\\)of sizen, and a client holds an index\\(i \\in [n]\\)and wishes to retrieve\\({\\mathtt {DB}}[i]\\)without revealingito the server. It is well known that information theoretic privacy even against an “honest but curious” server requires\\(\\varOmega (n)\\)communication complexity. This is true even if quantum communication is allowed and is due to the ability of such an adversarial server to execute the protocol on a superposition of databases instead of on a specific database (“input purification attack”). Nevertheless, there have been some proposals of protocols that achieve sub-linear communication and appear to provide some notion of privacy. Most notably, a protocol due to <PERSON> (ToC 2012) with communication complexity\\(O(\\sqrt{n})\\), and a protocol by <PERSON><PERSON><PERSON><PERSON> et al. (QIC 2016) with communication complexity\\(O(\\log (n))\\), andO(n) shared entanglement. We show that, in a sense, input purification is the only potent adversarial strategy, and protocols such as the two protocols above are secure in a restricted variant of the quantum honest but curious (a.k.a specious) model. More explicitly, we propose a restricted privacy notion calledanchored privacy, where the adversary is forced to execute on a classical database (i.e. the execution is anchored to a classical database). We show that for measurement-free protocols, anchored security against honest adversarial servers implies anchored privacy even against specious adversaries. Finally, we prove that even with (unlimited) pre-shared entanglement it is impossible to achieve security in the standard specious model with sub-linear communication, thus further substantiating the necessity of our relaxation. This lower bound may be of independent interest (in particular recalling that PIR is a special case of Fully Homomorphic Encryption).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_8"}, {"primary_key": "3004587", "vector": [], "sparse_vector": [], "title": "Minicrypt Primitives with Algebraic Structure and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Algebraic structure lies at the heart of Cryptomania as we know it. An interesting question is the following: instead of building (Cryptomania) primitives from concrete assumptions, can we build them fromsimpleMinicrypt primitives endowed with some additionalalgebraicstructure? In this work, we affirmatively answer this question by adding algebraic structure to the following Minicrypt primitives: One-Way Function (OWF) Weak Unpredictable Function (wUF) Weak Pseudorandom Function (wPRF) The algebraic structure that we consider is group homomorphism over the input/output spaces of these primitives. We also consider a “bounded” notion of homomorphism where the primitive only supports an a priori bounded number of homomorphic operations in order to capture lattice-based and other “noisy” assumptions. We show that these structured primitives can be used to construct many cryptographic protocols. In particular, we prove that: (Bounded)Homomorphic OWFs(HOWFs) imply collision-resistant hash functions, Schnorr-style signatures and chameleon hash functions. (Bounded)Input-Homomorphic weak UFs(IHwUFs) imply CPA-secure PKE, non-interactive key exchange, trapdoor functions, blind batch encryption (which implies anonymous IBE, KDM-secure and leakage-resilient PKE), CCA2 deterministic PKE, and hinting PRGs (which in turn imply transformation of CPA to CCA security for ABE/1-sided PE). (Bounded)Input-Homomorphic weak PRFs(IHwPRFs) imply PIR, lossy trapdoor functions, OT and MPC (in the plain model). In addition, we show how to realize any CDH/DDH-based protocol with certain properties in a generic manner using IHwUFs/IHwPRFs, and how to instantiate such a protocol from many concrete assumptions. We also consider primitives with substantially richer structure, namelyRing IHwPRFsandL-composable IHwPRFs. In particular, we show the following: Ring IHwPRFs with certain properties imply FHE. 2-composable IHwPRFs imply (black-box) IBE, andL-composable IHwPRFs imply non-interactive\\((L+1)\\)-party key exchange. Our framework allows us to categorize many cryptographic protocols based on which structured Minicrypt primitive implies them. In addition, it potentially makes showing theexistenceof many cryptosystems from novel assumptions substantially easier in the future.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_3"}, {"primary_key": "3004588", "vector": [], "sparse_vector": [], "title": "The General Sieve <PERSON> and New Records in Lattice Reduction.", "authors": ["<PERSON>", "Léo Du<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose the General Si<PERSON> (G6K, pronounced /e.si.ka/), an abstract stateful machine supporting a wide variety of lattice reduction strategies based on sieving algorithms. Using the basic instruction set of this abstract stateful machine, we first give concise formulations of previous sieving strategies from the literature and then propose new ones. We then also give a light variant of BKZ exploiting the features of our abstract stateful machine. This encapsulates several recent suggestions (<PERSON><PERSON> at Eurocrypt 2018; <PERSON><PERSON><PERSON><PERSON> and <PERSON> at PQCrypto 2018) to move beyond treating sieving as a blackbox SVP oracle and to utilise strong lattice reduction as preprocessing for sieving. Furthermore, we propose new tricks to minimise the sieving computation required for a given reduction quality with mechanisms such as recycling vectors between sieves, on-the-fly lifting and flexible insertions akin to Deep LLL and recent variants of Random Sampling Reduction. Moreover, we provide a highly optimised, multi-threaded and tweakable implementation of this machine which we make open-source. We then illustrate the performance of this implementation of our sieving strategies by applying G6K to various lattice challenges. In particular, our approach allows us to solve previously unsolved instances of the Darmstadt SVP (151, 153, 155) and LWE (e.g. (75, 0.005)) challenges. Our solution for the SVP-151 challenge was found 400 times faster than the time reported for the SVP-150 challenge, the previous record. For exact-SVP, we observe a performance crossover between G6K and FPLLL’s state of the art implementation of enumeration at dimension 70.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_25"}, {"primary_key": "3004589", "vector": [], "sparse_vector": [], "title": "The Double Ratchet: Security Notions, Proofs, and Modularization for the Signal Protocol.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Signal is a famous secure messaging protocol used by billions of people, by virtue of many secure text messaging applications including Signal itself, WhatsApp, Facebook Messenger, Skype, and Google Allo. At its core it uses the concept of “double ratcheting,” where every message is encrypted and authenticated using a fresh symmetric key; it has many attractive properties, such as forward security, post-compromise security, and “immediate (no-delay) decryption,” which had never been achieved in combination by prior messaging protocols. While the formal analysis of the Signal protocol, and ratcheting in general, has attracted a lot of recent attention, we argue that none of the existing analyses is fully satisfactory. To address this problem, we give a clean and general definition ofsecure messaging, which clearly indicates the types of security we expect, including forward security, post-compromise security, and immediate decryption. We are the first to explicitly formalize and model the immediate decryption property, which implies (among other things) that parties seamlessly recover if a given message is permanently lost—a property not achieved by any of the recent “provable alternatives to Signal.” We build a modular “generalized Signal protocol” from the following components: (a)continuous key agreement (CKA), a clean primitive we introduce and which can be easily and generically built from public-key encryption (not just <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as is done in the current Signal protocol) and roughly models “public-key ratchets;” (b)forward-secure authenticated encryption with associated data (FS-AEAD), which roughly captures “symmetric-key ratchets;” and (c) a two-input hash function that is a pseudorandom function (resp. generator with input) in its first (resp. second) input, which we term PRF-PRNG. As a result, in addition to instantiating our framework in a way resulting in the existing, widely-used Diffie-Hellman based Signal protocol, we can easily get post-quantum security and not rely on random oracles in the analysis.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_5"}, {"primary_key": "3004590", "vector": [], "sparse_vector": [], "title": "Two Round Information-Theoretic MPC with Malicious Security.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We provide the first constructions oftwo roundinformation-theoretic (IT) secure multiparty computation (MPC) protocols in the plain model that tolerate any\\(t<n/2\\)malicious corruptions. Our protocols satisfy the strongest achievable standard notions of security in two rounds in different communication models. Previously, IT-MPC protocols in the plain model either required a larger number of rounds, or a smaller minority of corruptions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_19"}, {"primary_key": "3004591", "vector": [], "sparse_vector": [], "title": "Secret-Sharing Schemes for General and Uniform Access Structures.", "authors": ["<PERSON>", "<PERSON>", "Oriol <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A secret-sharing scheme allows some authorized sets of parties to reconstruct a secret; the collection of authorized sets is called the access structure. For over 30 years, it was known that any (monotone) collection of authorized sets can be realized by a secret-sharing scheme whose shares are of size\\(2^{n-o(n)}\\)and until recently no better scheme was known. In a recent breakthrough, <PERSON> and <PERSON><PERSON><PERSON> (STOC 2018) have reduced the share size to\\(O(2^{0.994n})\\). Our first contribution is improving the exponent of secret sharing down to 0.892. For the special case of linear secret-sharing schemes, we get an exponent of 0.942 (compared to 0.999 of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>). Motivated by the construction of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, we study secret-sharing schemes for uniform access structures. An access structure isk-uniform if all sets of size larger thankare authorized, all sets of size smaller thankare unauthorized, and each set of sizekcan be either authorized or unauthorized. The construction of <PERSON> and <PERSON> starts from protocols for conditional disclosure of secrets, constructs secret-sharing schemes for uniform access structures from them, and combines these schemes in order to obtain secret-sharing schemes for general access structures. Our second contribution in this paper is constructions of secret-sharing schemes for uniform access structures. We achieve the following results: A secret-sharing scheme fork-uniform access structures for large secrets in which the share size is\\(O(k^2)\\)times the size of the secret. A linear secret-sharing scheme fork-uniform access structures for a binary secret in which the share size is\\(\\tilde{O}(2^{h(k/n)n/2})\\)(wherehis the binary entropy function). By counting arguments, this construction is optimal (up to polynomial factors). A secret-sharing scheme fork-uniform access structures for a binary secret in which the share size is\\(2^{\\tilde{O}(\\sqrt{k \\log n})}\\). Our third contribution is a construction of ad-hoc PSM protocols, i.e., PSM protocols in which only a subset of the parties will compute a function on their inputs. This result is based on ideas we used in the construction of secret-sharing schemes fork-uniform access structures for a binary secret.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_15"}, {"primary_key": "3004592", "vector": [], "sparse_vector": [], "title": "Degree 2 is Complete for the Round-Complexity of Malicious MPC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Rotem Tsabary"], "summary": "We show, via a non-interactive reduction, that the existence of a secure multi-party computation (MPC) protocol for degree-2 functions implies the existence of a protocol with thesame round complexityfor general functions. Thus showing that when considering the round complexity of MPC, it is sufficient to consider very simple functions. Our completeness theorem applies in various settings: information theoretic and computational, fully malicious and malicious with various types of aborts. In fact, we give a master theorem from which all individual settings follow as direct corollaries. Our basic transformation does not require any additional assumptions and incurs communication and computation blow-up which is polynomial in the number of players and in\\(S,2^D\\), whereS,Dare the circuit size and depth of the function to be computed. Using one-way functions as an additional assumption, the exponential dependence on the depth can be removed. As a consequence, we are able to push the envelope on the state of the art in various settings of MPC, including the following cases. 3-round perfectly-secure protocol (with guaranteed output delivery) against an active adversary that corrupts less than 1/4 of the parties. 2-round statistically-secure protocol that achieves security with “selective abort” against an active adversary that corrupts less than half of the parties. Assuming one-way functions, 2-round computationally-secure protocol that achieves security with (standard) abort against an active adversary that corrupts less than half of the parties. This gives a new and conceptually simpler proof to the recent result of <PERSON><PERSON><PERSON> et al. (Crypto 2018). Technically, our non-interactive reduction draws from the encoding method of Apple<PERSON>, Brak<PERSON><PERSON> and <PERSON><PERSON><PERSON>y (TCC 2018). We extend these methods to ones that can be meaningfully analyzed even in the presence of malicious adversaries.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_18"}, {"primary_key": "3004593", "vector": [], "sparse_vector": [], "title": "Durandal: A Rank Metric Based Signature Scheme.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a variation of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> approach to devising signature schemes that is adapted to rank based cryptography. This new approach enables us to obtain a randomization of the signature, which previously seemed difficult to derive for code-based cryptography. We provide a detailed analysis of attacks and an EUF-CMA proof for our scheme. Our scheme relies on the security of the Ideal Rank Support Learning and the Ideal Rank Syndrome problems and a newly introduced problem: Product Spaces Subspaces Indistinguishability, for which we give a detailed analysis. Overall the parameters we propose are efficient and comparable in terms of signature size to the Dilithium lattice-based scheme, with a signature size of 4 kB for a public key of size less than 20 kB.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_25"}, {"primary_key": "3004594", "vector": [], "sparse_vector": [], "title": "Locality-Preserving Oblivious RAM.", "authors": ["<PERSON><PERSON>", "T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Oblivious RAMs, introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [JACM’96], compile any RAM program into one that is “memory oblivious”, i.e., the access pattern to the memory is independent of the input. All previous ORAM schemes, however, completely break thelocalityof data accesses (for instance, by shuffling the data to pseudorandom positions in memory). In this work, we initiate the study oflocality-preserving ORAMs—ORAMs that preserve locality of the accessed memory regions, while leaking only the lengths of contiguous memory regions accessed. Our main results demonstrate the existence of a locality-preserving ORAM with poly-logarithmic overhead both in terms of bandwidth and locality. We also study the tradeoff between locality, bandwidth and leakage, and show that any scheme that preserves locality and does not leak the lengths of the contiguous memory regions accessed, suffers from prohibitive bandwidth. To the best of our knowledge, before our work, the only works combining locality and obliviousness were for symmetric searchable encryption [e.g., <PERSON> and <PERSON> (EUROCRYPT’14), <PERSON><PERSON><PERSON> et al. (STOC’16)]. Symmetric search encryption ensures obliviousness if each keyword is searched only once, whereas ORAM provides obliviousness to any input program. Thus, our work generalizes that line of work to the much more challenging task of preserving locality in ORAMs.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_8"}, {"primary_key": "3004595", "vector": [], "sparse_vector": [], "title": "Unbounded Dynamic Predicate Compositions in Attribute-Based Encryption.", "authors": ["Nuttapong Attrapadung"], "summary": "We present several transformations that combine a set of attribute-based encryption (ABE) schemes for simpler predicates into a new ABE scheme for more expressive composed predicates. Previous proposals for predicate compositions of this kind, the most recent one being that of <PERSON><PERSON><PERSON><PERSON> <PERSON>.at Crypto’17, can be consideredstatic(or partially dynamic), meaning that the policy (or its structure) that specifies a composition must be fixed at the setup. Contrastingly, our transformations aredynamicandunbounded: they allow a user to specify an arbitrary and unbounded-size composition policy right into his/her own key or ciphertext. We propose transformations for three classes of composition policies, namely, the classes of any monotone span programs, any branching programs, and any deterministic finite automata. These generalized policies are defined over arbitrary predicates, hence admittingmodularcompositions. One application from modularity is a new kind of ABE for which policies can be “nested” over ciphertext and key policies. As another application, we achieve the first fully secure completely unbounded key-policy ABE for non-monotone span programs, in a modular and clean manner, under the q-ratio assumption. Our transformations work inside a generic framework for ABE called symbolic pair encoding, proposed by <PERSON><PERSON><PERSON> and <PERSON> at Eurocrypt’17. At the core of our transformations, we observe and exploit an unbounded nature of the symbolic property so as to achieve unbounded-size policy compositions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_2"}, {"primary_key": "3004596", "vector": [], "sparse_vector": [], "title": "Session Resumption Protocols and Efficient Forward Security for TLS 1.3 0-RTT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The TLS 1.3 0-RTT mode enables a client reconnecting to a server to send encrypted application-layer data in “0-RTT” (“zero round-trip time”), without the need for a prior interactive handshake. This fundamentally requires the server to reconstruct the previous session’s encryption secrets upon receipt of the client’s first message. The standard techniques to achieve this areSession Cachesor, alternatively,Session Tickets. The former provides forward security and resistance against replay attacks, but requires a large amount of server-side storage. The latter requires negligible storage, but provides no forward security and is known to be vulnerable to replay attacks. In this paper, we first formally definesession resumption protocolsas an abstract perspective on mechanisms like Session Caches and Session Tickets. We give a new generic construction that provably provides forward security and replay resilience, based on puncturable pseudorandom functions (PPRFs). This construction can immediately be used in TLS 1.3 0-RTT and deployed unilaterally by servers, without requiring any changes to clients or the protocol. We then describe two new constructions of PPRFs, which are particularly suitable for use for forward-secure and replay-resilient session resumption in TLS 1.3. The first construction is based on the strong RSA assumption. Compared to standard Session Caches, for “128-bit security” it reduces the required server storage by a factor of almost 20, when instantiated in a way such that key derivation and puncturing together are cheaper on average than one full exponentiation in an RSA group. Hence, a 1 GB Session Cache can be replaced with only about 51 MBs of storage, which significantly reduces the amount of secure memory required. For larger security parameters or in exchange for more expensive computations, even larger storage reductions are achieved. The second construction combines a standard binary tree PPRF with a new “domain extension” technique. For a reasonable choice of parameters, this reduces the required storage by a factor of up to 5 compared to a standard Session Cache. It employs only symmetric cryptography, is suitable for high-traffic scenarios, and can serve thousands of tickets per second.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_5"}, {"primary_key": "3004597", "vector": [], "sparse_vector": [], "title": "Revisiting Non-Malleable Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A threshold secret sharing scheme (with thresholdt) allows a dealer to share a secret among a set of parties such that any group oftor more parties can recover the secret and no group of at most\\(t-1\\)parties learn any information about the secret. A non-malleable threshold secret sharing scheme, introduced in the recent work of <PERSON><PERSON> and <PERSON> (STOC’18), additionally protects a threshold secret sharing scheme when its shares are subject to tampering attacks. Specifically, it guarantees that the reconstructed secret from the tampered shares is either the original secret or something that is unrelated to the original secret. In this work, we continue the study of threshold non-malleable secret sharing against the class of tampering functions that tamper each share independently. We focus on achieving greaterefficiencyand guaranteeing astrongersecurity property. We obtain the following results: Rate Improvement.We give the first construction of a threshold non-malleable secret sharing scheme that has rate\\(> 0\\). Specifically, for every\\(n,t \\ge 4\\), we give a construction of at-out-of-nnon-malleable secret sharing scheme with rate\\(\\varTheta (\\frac{1}{t\\log ^2 n})\\). In the prior constructions, the rate was\\(\\varTheta (\\frac{1}{n\\log m})\\)wheremis the length of the secret and thus, the rate tends to 0 as\\(m \\rightarrow \\infty \\). Furthermore, we also optimize the parameters of our construction and give a concretely efficient scheme. Multiple Tampering.We give the first construction of a threshold non-malleable secret sharing scheme secure in the stronger setting of bounded tampering wherein the shares are tampered by multiple (but bounded in number) possibly different tampering functions. The rate of such a scheme is\\(\\varTheta (\\frac{1}{k^3t\\log ^2 n})\\)wherekis an apriori bound on the number of tamperings. We complement this positive result by proving that it is impossible to have a threshold non-malleable secret sharing scheme that is secure in the presence of an apriori unbounded number of tamperings. General Access Structures.We extend our results beyond threshold secret sharing and give constructions of rate-efficient, non-malleable secret sharing schemes for more general monotone access structures that are secure against multiple (bounded) tampering attacks.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_20"}, {"primary_key": "3004598", "vector": [], "sparse_vector": [], "title": "Misuse Attacks on Post-quantum Cryptosystems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many post-quantum cryptosystems which have been proposed in the National Institute of Standards and Technology (NIST) standardization process follow the same meta-algorithm, but in different algebras or different encoding methods. They usually propose two constructions, one being weaker and the other requiring a random oracle. We focus on the weak version of nine submissions to NIST. Submitters claim no security when the secret key is used several times. In this paper, we analyze how easy it is to run a key recovery under multiple key reuse. We mount a classical key recovery under plaintext checking attacks (i.e., with a plaintext checking oracle saying if a given ciphertext decrypts well to a given plaintext) and a quantum key recovery under chosen ciphertext attacks. In the latter case, we assume quantum access to the decryption oracle.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_26"}, {"primary_key": "3004599", "vector": [], "sparse_vector": [], "title": "Non-Malleable Codes Against Bounded Polynomial Time Tampering.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct efficient non-malleable codes (NMC) that are (computationally) secure against tampering by functions computable in anyfixedpolynomial time. Our construction is in the plain (no-CRS) model and requires the assumptions that (1)\\(\\mathbf {E}\\)is hard for\\(\\mathbf {NP}\\)circuits of some exponential\\(2^{\\beta n}\\)(\\(\\beta >0\\)) size (widely used in the derandomization literature), (2) sub-exponential trapdoor permutations exist, and (3)\\(\\mathbf {P}\\)-certificates with sub-exponential soundness exist. While it is impossible to construct NMC secure againstarbitrarypolynomial-time tampering (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ICS ’10), the existence of NMC secure against\\(O(n^c)\\)-time tampering functions (for anyfixedc), was shown (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, ITCS ’14) via a probabilistic construction. An explicit construction was given (<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>s, Eurocrypt ’14) assuming anuntamperableCRS with length longer than the runtime of the tampering function. In this work, we show that under computational assumptions, we can bypass these limitations. Specifically, under the assumptions listed above, we obtain non-malleable codes in the plain model against\\(O(n^c)\\)-time tampering functions (for any fixedc), with codeword length independent of the tampering time bound. Our new construction of NMC draws a connection with non-interactive non-malleable commitments. In fact, we show that in the NMC setting, it suffices to have a much weaker notion calledquasi non-malleable commitments—these are non-interactive, non-malleable commitments in the plain model, in which the adversary runs in\\(O(n^c)\\)-time, whereas the honest parties may run in longer (polynomial) time. We then construct a 4-tag quasi non-malleable commitment from any sub-exponential OWF and the assumption that\\(\\mathbf {E}\\)is hard for some exponential size\\(\\mathbf {NP}\\)-circuits, and use tag amplification techniques to support an exponential number of tags.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_17"}, {"primary_key": "3004600", "vector": [], "sparse_vector": [], "title": "DLCT: A New Tool for Differential-Linear Cryptanalysis.", "authors": ["<PERSON><PERSON>ya Bar-On", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Differential cryptanalysis and linear cryptanalysis are the two best-known techniques for cryptanalysis of block ciphers. In 1994, <PERSON><PERSON> and <PERSON><PERSON> introduced the differential-linear (DL) attack based on dividing the attacked cipherEinto two subciphers\\(E_0\\)and\\(E_1\\)and combining a differential characteristic for\\(E_0\\)with a linear approximation for\\(E_1\\)into an attack on the entire cipherE. The DL technique was used to mount the best known attacks against numerous ciphers, including the AES finalist Serpent, ICEPOLE, COCONUT98, <PERSON><PERSON>y, CTC2, and 8-round DES. Several papers aimed at formalizing the DL attack, and formulating assumptions under which its complexity can be estimated accurately. These culminated in a recent work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Journal of Cryptology, 2017) which obtained an accurate expression under the sole assumption that the two subciphers\\(E_0\\)and\\(E_1\\)are independent. In this paper we show that in many cases, dependency between the two subcipher s significantly affects the complexity of the DL attack, and in particular, can be exploited by the adversary to make the attack more efficient. We present the Differential-Linear Connectivity Table (DLCT) which allows us to take into account the dependency between the two subciphers, and to choose the differential characteristic in\\(E_0\\)and the linear approximation in\\(E_1\\)in a way that takes advantage of this dependency. We then show that the DLCT can be constructed efficiently using the Fast Fourier Transform. Finally, we demonstrate the strength of the DLCT by using it to improve differential-linear attacks on ICEPOLE and on 8-round DES, and to explain published experimental results on Serpent and on the CAESAR finalist Ascon which did not comply with the standard differential-linear framework.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_11"}, {"primary_key": "3004601", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Meets Program Obfuscation, Revisited.", "authors": ["<PERSON>az <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop attacks on the security of variants of pseudo-random generators computed by quadratic polynomials. In particular we give a general condition for breaking the one-way property of mappings where every output is a quadratic polynomial (over the reals) of the input. As a corollary, we break the degree-2 candidates for security assumptions recently proposed for constructing indistinguishability obfuscation by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (ePrint 2018) and <PERSON><PERSON><PERSON> (ePrint 2018). We present conjectures that would imply our attacks extend to a wider variety of instances, and in particular offer experimental evidence that they break assumption of <PERSON><PERSON><PERSON> (ePrint 2018). Our algorithms use semidefinite programming, and in particular, results on low-rank recovery (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON> 2007) and matrix completion (<PERSON> 2009).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_8"}, {"primary_key": "3004602", "vector": [], "sparse_vector": [], "title": "New Techniques for Obfuscating Conjunctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A conjunction is a function\\(f(x_1,\\dots ,x_n) = \\bigwedge _{i \\in S} l_i\\)where\\(S \\subseteq [n]\\)and each\\(l_i\\)is\\(x_i\\)or\\(\\lnot x_i\\). <PERSON> et al. (CRYPTO 2018) recently proposed obfuscating conjunctions by embedding them in the error positions of a noisy Reed-Solomon codeword and placing the codeword in a group exponent. They prove distributional virtual black box (VBB) security in the generic group model for random conjunctions where\\(|S| \\ge 0.226n\\). While conjunction obfuscation is known from LWE [31,47], these constructions rely on substantial technical machinery. In this work, we conduct an extensive study ofsimpleconjunction obfuscation techniques. We abstract the <PERSON> et al. scheme to obtain an equivalent yet more efficient “dual” scheme that can handle conjunctions over exponential size alphabets. This scheme admits a straightforward proof of generic group security, which we combine with a novel combinatorial argument to obtain distributional VBB security for |S| ofany size. If we replace the Reed-Solomon code with arandom binary linear code, we can prove security from standard LPN and avoid encoding in a group. This addresses an open problem posed by <PERSON> et al. to prove security of this simple approach in the standard model. We give a new construction that achievesinformation theoreticdistributional VBB security and weak functionality preservation for\\(|S| \\ge n - n^\\delta \\)and\\(\\delta < 1\\). Assuming discrete log and\\(\\delta < 1/2\\), we satisfy a stronger notion of functionality preservation for computationally bounded adversaries while still achieving information theoretic security.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_22"}, {"primary_key": "3004603", "vector": [], "sparse_vector": [], "title": "Aurora: Transparent Succinct Arguments for R1CS.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Madars Virza", "<PERSON>"], "summary": "We design, implement, and evaluate a zero knowledge succinct non-interactive argument (SNARG) for Rank-1 Constraint Satisfaction (R1CS), a widely-deployed NP language undergoing standardization. Our SNARG has a transparent setup, is plausibly post-quantum secure, and uses lightweight cryptography. A proof attesting to the satisfiability ofnconstraints has size\\(O(\\log ^2 n)\\); it can be produced with\\(O(n \\log n)\\)field operations and verified withO(n). At 128 bits of security, proofs are less than\\({250}\\,\\mathrm{kB}\\)even for several million constraints, more than\\(10{\\times }\\)shorter than prior SNARGs with similar features. A key ingredient of our construction is a new Interactive Oracle Proof (IOP) for solving aunivariateanalogue of the classical sumcheck problem [LFKN92], originally studied formultivariatepolynomials. Our protocol verifies the sum of entries of a Reed–Solomon codeword over any subgroup of a field. We also provide\\(\\texttt {libiop}\\), a library for writing IOP-based arguments, in which a toolchain of transformations enables programmers to write new arguments by writing simple IOP sub-components. We have used this library to specify our construction and prior ones, and plan to open-source it.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_4"}, {"primary_key": "3004604", "vector": [], "sparse_vector": [], "title": "Quantum Circuits for the CSIDH: Optimizing Quantum Evaluation of Isogenies.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Choosing safe post-quantum parameters for the new CSIDH isogeny-based key-exchange system requires concrete analysis of the cost of quantum attacks. The two main contributions to attack cost are the number of queries in hidden-shift algorithms and the cost of each query. This paper analyzes algorithms for each query, introducing several new speedups while showing that some previous claims were too optimistic for the attacker. This paper includes a full computer-verified simulation of its main algorithm down to the bit-operation level.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_15"}, {"primary_key": "3004605", "vector": [], "sparse_vector": [], "title": "Distributional Collision Resistance Beyond One-Way Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "Distributional collision resistance is a relaxation of collision resistance that only requires that it is hard to sample a collision (x,y) wherexis uniformly random andyis uniformly random conditioned on colliding withx. The notion lies between one-wayness and collision resistance, but its exact power is still not well-understood. On one hand, distributional collision resistant hash functions cannot be built from one-way functions in a black-box way, which may suggest that they are stronger. On the other hand, so far, they have not yielded any applications beyond one-way functions. Assuming distributional collision resistant hash functions, we constructconstant-roundstatistically hiding commitment scheme. Such commitments are not known based on one-way functions, and are impossible to obtain from one-way functions in a black-box way. Our construction relies on the reduction from inaccessible entropy generators to statistically hiding commitments by <PERSON><PERSON><PERSON> et al. (STOC ’09). In the converse direction, we show that two-message statistically hiding commitments imply distributional collision resistance, thereby establishing a loose equivalence between the two notions. A corollary of the first result is that constant-round statistically hiding commitments are implied by average-case hardness in the class\\({\\textsf {SZK}}\\)(which is known to imply distributional collision resistance). This implication seems to be folklore, but to the best of our knowledge has not been proven explicitly. We provide yet another proof of this implication, which is arguably more direct than the one going through distributional collision resistance.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_23"}, {"primary_key": "3004606", "vector": [], "sparse_vector": [], "title": "Homomorphic Secret Sharing from Lattices Without FHE.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Homomorphic secret sharing (HSS) is an analog of somewhat- or fully homomorphic encryption (S/FHE) to the setting of secret sharing, with applications including succinct secure computation, private manipulation of remote databases, and more. While HSS can be viewed as a relaxation of S/FHE, the only constructions from lattice-based assumptions to date buildatopspecific forms of threshold or multi-key S/FHE. In this work, we present new techniques directly yielding efficient 2-party HSS for polynomial-size branching programs from a range of lattice-based encryption schemes,without S/FHE. More concretely, we avoid the costlykey-switchingandmodulus-reductionsteps used in S/FHE ciphertext multiplication, replacing them with a newdistributed decryptionprocedure for performing “restricted” multiplications of an input with a partial computation value. Doing so requires new methods for handling the blowup of “noise” in ciphertexts in a distributed setting, and leverages several properties of lattice-based encryption schemes together with new tricks in share conversion. The resulting schemes support a superpolynomial-size plaintext space and negligible correctness error, with share sizes comparable to SHE ciphertexts, but cost of homomorphic multiplication roughly one order of magnitude faster. Over certain rings, our HSS can further support some level of packed SIMD homomorphic operations. We demonstrate the practical efficiency of our schemes within two application settings, where we compare favorably with current best approaches: 2-server private database pattern-match queries, and secure 2-party computation of low-degree polynomials.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_1"}, {"primary_key": "3004607", "vector": [], "sparse_vector": [], "title": "Worst-Case Hardness for LPN and Cryptographic Hashing via Code Smoothing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a worst case decoding problem whose hardness reduces to that of solving the Learning Parity with Noise (LPN) problem, in some parameter regime. Prior to this work, no worst case hardness result was known for LPN (as opposed to syntactically similar problems such as Learning with Errors). The caveat is that this worst case problem is only mildly hard and in particular admits a quasi-polynomial time algorithm, whereas the LPN variant used in the reduction requires extremely high noise rate of\\(1/2-1/\\mathrm{poly}(n)\\). Thus we can only show that “very hard” LPN is harder than some “very mildly hard” worst case problem. We note that LPN with noise\\(1/2-1/\\mathrm{poly}(n)\\)already implies symmetric cryptography. Specifically, we consider the (n,m,w)-nearest codeword problem ((n,m,w)-NCP) which takes as input a generating matrix for a binary linear code inmdimensions and rankn, and a target vector which is very close to the code (Hamming distance at mostw), and asks to find the codeword nearest to the target vector. We show that for balanced (unbiased) codes and for relative error\\(w/m \\approx {\\log ^2 n}/{n}\\), (n,m,w)-NCP can be solved given oracle access to an LPN distinguisher with noise ratio\\(1/2-1/\\mathrm{poly}(n)\\). Our proof relies on a smoothing lemma for codes which we show to have further implications: We show that (n,m,w)-NCP with the aforementioned parameters lies in the complexity class\\(\\mathrm {{Search}\\hbox {-}\\mathcal {BPP}}^\\mathcal {SZK}\\)(i.e. reducible to a problem that has a statistical zero knowledge protocol) implying that it is unlikely to be\\(\\mathcal {NP}\\)-hard. We then show that the hardness of LPN with very low noise rate\\(\\log ^2(n)/n\\)implies the existence of collision resistant hash functions (our aforementioned result implies that in this parameter regime LPN is also in\\(\\mathcal {BPP}^\\mathcal {SZK}\\)).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_21"}, {"primary_key": "3004608", "vector": [], "sparse_vector": [], "title": "bison Instantiating the Whitened Swap-Or-Not Construction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give the first practical instance –bison– of the Whitened Swap-Or-Not construction. After clarifying inherent limitations of the construction, we point out that this way of building block ciphers allows easy and very strong arguments against differential attacks.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_20"}, {"primary_key": "3004609", "vector": [], "sparse_vector": [], "title": "Consensus Through Herding.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "State Machine Replication (SMR) is an important abstraction for a set of nodes to agree on an ever-growing, linearly-ordered log of transactions. In decentralized cryptocurrency applications, we would like to design SMR protocols that (1) resist adaptive corruptions; and (2) achieve small bandwidth and small confirmation time. All past approaches towards constructing SMR fail to achieve either small confirmation time or small bandwidth under adaptive corruptions (without resorting to strong assumptions such as the erasure model or proof-of-work). We propose a novel paradigm for reaching consensus that departs significantly from classical approaches. Our protocol is inspired by a social phenomenon called herding, where people tend to make choices considered as the social norm. In our consensus protocol, leader election and voting are coalesced into a single (randomized) process: in every round, every node tries to cast a vote for what it views as themost popularitem so far: such a voting attempt is not always successful, but rather, successful with a certain probability. Importantly, the probability that the node is elected to vote forvis independent from the probability it is elected to vote for\\(v' \\ne v\\). We will show how to realize such a distributed, randomized election process using appropriate, adaptively secure cryptographic building blocks. We show that amazingly, not only can this new paradigm achieve consensus (e.g., on a batch of unconfirmed transactions in a cryptocurrency system), but it also allows us to derive the first SMR protocol which, even under adaptive corruptions, requires only polylogarithmically many rounds and polylogarithmically many honest messages to be multicast to confirm each batch of transactions; and importantly, we attain these guarantees under standard cryptographic assumptions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_24"}, {"primary_key": "3004610", "vector": [], "sparse_vector": [], "title": "Distributed Differential Privacy via Shuffling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of designing scalable, robust protocols for computing statistics about sensitive data. Specifically, we look at how best to design differentially private protocols in a distributed setting, where each user holds a private datum. The literature has mostly considered two models: the “central” model, in which a trusted server collects users’ data in the clear, which allows greater accuracy; and the “local” model, in which users individually randomize their data, and need not trust the server, but accuracy is limited. Attempts to achieve the accuracy of the central model without a trusted server have so far focused on variants of cryptographic multiparty computation (MPC), which limits scalability. In this paper, we initiate the analytic study of ashuffled modelfor distributed differentially private algorithms, which lies between the local and central models. This simple-to-implement model, a special case of the ESA framework of [5], augments the local model with an anonymous channel that randomly permutes a set of user-supplied messages. For sum queries, we show that this model provides the power of the central model while avoiding the need to trust a central server and the complexity of cryptographic secure function evaluation. More generally, we give evidence that the power of the shuffled model lies strictly between those of the central and local models: for a natural restriction of the model, we show that shuffled protocols for a widely studiedselectionproblem require exponentially higher sample complexity than do central-model protocols.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_13"}, {"primary_key": "3004611", "vector": [], "sparse_vector": [], "title": "Founding Secure Computation on Blockchains.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the foundations of secure computation in theblockchain-hybrid model, where a blockchain – modeled as aglobalfunctionality – is available as an Oracle to all the participants of a cryptographic protocol. We demonstrate both destructive and constructive applications of blockchains: We show that classical rewinding-based simulation techniques used in many security proofs fail againstblockchain-activeadversaries that have read and post access to a global blockchain. In particular, we show that zero-knowledge (ZK) proofs with black-box simulation are impossible against blockchain-active adversaries. Nevertheless, we show that achieving security against blockchain-active adversaries is possible if the honest parties are also blockchain active. We construct an\\(\\omega (1)\\)-round ZK protocol with black-box simulation. We show that this result is tight by proving the impossibility of constant-round ZK with black-box simulation. Finally, we demonstrate a novel application of blockchains to overcome the known impossibility results for concurrent secure computation in the plain model. We construct a concurrent self-composable secure computation protocol for general functionalities in the blockchain-hybrid model based on standard cryptographic assumptions. We develop a suite of techniques for constructing secure protocols in the blockchain-hybrid model that we hope will find applications to future research in this area.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_13"}, {"primary_key": "3004612", "vector": [], "sparse_vector": [], "title": "Verifier-on-a-Leash: New Schemes for Verifiable Delegated Quantum Computation, with Quasilinear Resources.", "authors": ["<PERSON>", "Alex <PERSON>l Grilo", "<PERSON>", "<PERSON>"], "summary": "The problem of reliably certifying the outcome of a computation performed by a quantum device is rapidly gaining relevance. We present two protocols for a classical verifier to verifiably delegate a quantum computation to two non-communicating but entangled quantum provers. Our protocols have near-optimal complexity in terms of the total resources employed by the verifier and the honest provers, with the total number of operations of each party, including the number of entangled pairs of qubits required of the honest provers, scaling as\\(O(g\\log g)\\)for delegating a circuit of sizeg. This is in contrast to previous protocols, whose overhead in terms of resources employed, while polynomial, is far beyond what is feasible in practice. Our first protocol requires a number of rounds that is linear in the depth of the circuit being delegated, and is blind, meaning neither prover can learn the circuit or its input. The second protocol is not blind, but requires only a constant number of rounds of interaction. Our main technical innovation is an efficient rigidity theorem which allows a verifier to test that two entangled provers perform measurements specified by an arbitrarym-qubit tensor product of single-qubit Clifford observables on their respective halves ofmshared EPR pairs, with a robustness that is independent ofm. Our two-prover classical-verifier delegation protocols are obtained by combining this rigidity theorem with a single-prover quantum-verifier protocol for the verifiable delegation of a quantum computation, introduced by <PERSON><PERSON>.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_9"}, {"primary_key": "3004613", "vector": [], "sparse_vector": [], "title": "A Note on the Communication Complexity of Multiparty Computation in the Correlated Randomness Model.", "authors": ["<PERSON><PERSON>"], "summary": "Secure multiparty computation (\\(\\mathsf {MPC}\\)) addresses the challenge of evaluating functions on secret inputs without compromising their privacy. A central question in multiparty computation is to understand the amount of communication needed to securely evaluate a circuit of sizes. In this work, we revisit this fundamental question in the setting of information-theoretically secure\\(\\mathsf {MPC}\\)in the correlated randomness model, where a trusted dealer distributes correlated random coins, independent of the inputs, to all parties before the start of the protocol. This setting is of strong theoretical interest, and has led to the most practically efficient\\(\\mathsf {MPC}\\)protocols known to date. While it is known that protocols with optimal communication (proportional to input plus output size) can be obtained from the LWE assumption, and that protocols with sublinear communicationo(s) can be obtained from the DDH assumption, the question of constructing protocols witho(s) communication remains wide open for the important case of information-theoretic\\(\\mathsf {MPC}\\)in the correlated randomness model; all known protocols in this model requireO(s) communication in the online phase. In this work, we exhibit the first generic multiparty computation protocol in the correlated randomness model with communication sublinear in the circuit size, for a large class of circuits. More precisely, we show the following: any size-slayeredcircuit (whose nodes can be partitioned into layers so that any edge connects adjacent layers) can be evaluated with\\(O(s/\\log \\log s)\\)communication. Our results holds for both boolean and arithmetic circuits, in the honest-but-curious setting, and do not assume honest majority. For boolean circuits, we extend our results to handle malicious corruption.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_17"}, {"primary_key": "3004614", "vector": [], "sparse_vector": [], "title": "Designated-Verifier Pseudorandom Generators, and Their Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We provide a generic construction of non-interactive zero-knowledge (NIZK) schemes. Our construction is a refinement of <PERSON><PERSON> and <PERSON><PERSON>’s (FOCS 2000) implementation of the hidden bits model using verifiable pseudorandom generators (VPRGs). Our refinement simplifies their construction and relaxes the necessary assumptions considerably. As a result of this conceptual improvement, we obtain interesting new instantiations: A designated-verifier NIZK (with unbounded soundness) based on the computational Diffie-Hellman (CDH) problem. If a pairing is available, this NIZK becomes publicly verifiable. This constitutes the first fully secure CDH-based designated-verifier NIZKs (and more generally, the first fully secure designated-verifier NIZK from a non-generic assumption which does not already imply publicly-verifiable NIZKs), and it answers an open problem recently raised by <PERSON> and <PERSON> (CRYPTO 2018). A NIZK based on the learning with errors (LWE) assumption, and assuming a non-interactive witness-indistinguishable (NIWI) proof system for bounded distance decoding (BDD). This simplifies and improves upon a recent NIZK from LWE that assumes a NIZK for BDD (<PERSON><PERSON> et al., PKC 2019).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_20"}, {"primary_key": "3004615", "vector": [], "sparse_vector": [], "title": "Linear Equivalence of Block Ciphers with Partial Non-Linear Layers: Application to LowMC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "\\(\\textsc {LowMC}\\)is a block cipher family designed in 2015 by <PERSON><PERSON> et al. It is optimized for practical instantiations of multi-party computation, fully homomorphic encryption, and zero-knowledge proofs.\\(\\textsc {LowMC}\\)is used in the\\(\\textsc {Picnic}\\)signature scheme, submitted to NIST’s post-quantum standardization project and is a substantial building block in other novel post-quantum cryptosystems. Many\\(\\textsc {LowMC}\\)instances use a relatively recent design strategy (initiated by <PERSON> et al. at CHES 2013) of applying the non-linear layer to only a part of the state in each round, where the shortage of non-linear operations is partially compensated by heavy linear algebra. Since the high linear algebra complexity has been a bottleneck in several applications, one of the open questions raised by the designers was to reduce it, without introducing additional non-linear operations (or compromising security). In this paper, we consider\\(\\textsc {LowMC}\\)instances with block sizen, partial non-linear layers of size\\(s \\le n\\)andrencryption rounds. We redesignLowMC’s linear components in a way that preserves its specification, yet improvesLowMC’s performance in essentially every aspect. Most of our optimizations are applicable to all SP-networks with partial non-linear layers and shed new light on this relatively new design methodology. Our main result shows that when\\(s < n\\), each\\(\\textsc {LowMC}\\)instance belongs to a large class of equivalent instances that differ in their linear layers. We then select arepresentative instancefrom this class for which encryption (and decryption) can be implemented much more efficiently than for an arbitrary instance. This yields a new encryption algorithm that is equivalent to the standard one, but reduces the evaluation time and storage of the linear layers from\\(r \\cdot n^2\\)bits to about\\(r \\cdot n^2 - (r-1)(n-s)^2\\). Additionally, we reduce the size ofLowMC’s round keys and constants and optimize its key schedule and instance generation algorithms. All of these optimizations give substantial improvements for smallsand a reasonable choice ofr. Finally, we formalize the notion of linear equivalence of block ciphers and prove the optimality of some of our results. Comprehensive benchmarking of our optimizations in various\\(\\textsc {LowMC}\\)applications (such as\\(\\textsc {Picnic}\\)) reveals improvements by factors that typically range between 2x and 40x in runtime and memory consumption.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_12"}, {"primary_key": "3004616", "vector": [], "sparse_vector": [], "title": "Multi-target Attacks on the Picnic Signature Scheme and Related Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Picnic is a signature scheme that was presented at ACM CCS 2017 by <PERSON> et al. and submitted to NIST’s post-quantum standardization project. Among all submissions to NIST’s project, Picnic is one of the most innovative, making use of recent progress in construction of practically efficient zero-knowledge (ZK) protocols for general circuits. In this paper, we devise multi-target attacks on Picnic and its underlying ZK protocol, ZKB++. Given access toSsignatures, produced by a single or by several users, our attack can (information theoretically) recover the\\(\\kappa \\)-bit signing key of a user in complexity of about\\(2^{\\kappa - 7}/S\\). This is faster than Picnic’s claimed\\(2^{\\kappa }\\)security against classical (non-quantum) attacks by a factor of\\(2^7 \\cdot S\\)(as each signature contains about\\(2^7\\)attack targets). Whereas in most multi-target attacks, the attacker can easily sort and match the available targets, this is not the case in our attack on Picnic, as different bits of information are available for each target. Consequently, it is challenging to reach the information theoretic complexity in a computational model, and we had to perform cryptanalytic optimizations by carefully analyzing ZKB++ and its underlying circuit. Our best attack for\\(\\kappa = 128\\)has time complexity of\\(T = 2^{77}\\)for\\(S = 2^{64}\\). Alternatively, we can reach the information theoretic complexity of\\(T = 2^{64}\\)for\\(S = 2^{57}\\), given that all signatures are produced with the same signing key. Our attack exploits a weakness in the way that the Picnic signing algorithm uses a pseudo-random generator. The weakness is fixed in the recent Picnic 2.0 version. In addition to our attack on Picnic, we show that a recently proposed improvement of the ZKB++ protocol (due to Katz, Kolesnikov and Wang) is vulnerable to a similar multi-target attack.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_24"}, {"primary_key": "3004617", "vector": [], "sparse_vector": [], "title": "Incremental Proofs of Sequential Work.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A proof of sequential work allows a prover to convince a verifier that a certain amount of sequential steps have been computed. In this work we introduce the notion ofincremental proofs of sequential workwhere a prover can carry on the computation done by the previous prover incrementally, without affecting the resources of the individual provers or the size of the proofs. To date, the most efficient instance of proofs of sequential work [<PERSON> and <PERSON>, Eurocrypt 2018] forNsteps require the prover to have\\(\\sqrt{N}\\)memory and to run for\\(N + \\sqrt{N}\\)steps. Using incremental proofs of sequential work we can bring down the prover’s storage complexity to\\(\\log N\\)and its running time toN. We propose two different constructions of incremental proofs of sequential work: Our first scheme requires a single processor and introduces a poly-logarithmic factor in the proof size when compared with the proposals of <PERSON> and <PERSON>. Our second scheme assumes\\(\\log N\\)parallel processors but brings down the overhead of the proof size to a factor of 9. Both schemes are simple to implement and only rely on hash functions (modelled as random oracles).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_11"}, {"primary_key": "3004618", "vector": [], "sparse_vector": [], "title": "Beyond Birthday Bound Secure MAC in Faulty Nonce Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Encrypt-then-MAC (EtM) is a popular mode for authenticated encryption (AE). Unfortunately, almost all designs following theEtMparadigm, including the AE suites forTLS, are vulnerable against nonce misuse. A single repetition of the nonce value reveals the hash key, leading to a universal forgery attack. There are only two authenticated encryption schemes following theEtMparadigm which can resist nonce misuse attacks, theGCM-RUP(CRYPTO-17) and the\\(\\mathsf {GCM/2}^{+} \\)(INSCRYPT-12). However, they are secure only up to the birthday bound in the nonce respecting setting, resulting in a restriction on the data limit for a single key. In this paper we show thatnEHtM, a nonce-based variant ofEHtM(FSE-10) constructed using a block cipher, has a beyond birthday bound (BBB) unforgeable security that gracefully degrades under nonce misuse. We combinenEHtMwith theCENC(FSE-06) mode of encryption using theEtMparadigm to realize a nonce-based AE,CWC+.CWC+is very close (requiring only a few more xor operations) to theCWCAE scheme (FSE-04) and it not only provides BBB security but also gracefully degrading security on nonce misuse.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_15"}, {"primary_key": "3004619", "vector": [], "sparse_vector": [], "title": "Multi-party Virtual State Channels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smart contracts are self-executing agreements written in program code and are envisioned to be one of the main applications of blockchain technology. While they are supported by prominent cryptocurrencies such as Ethereum, their further adoption is hindered by fundamental scalability challenges. For instance, in Ethereum contract execution suffers from a latency of more than 15 s, and the total number of contracts that can be executed per second is very limited.State channel networksare one of the core primitives aiming to address these challenges. They form a second layer over the slow and expensive blockchain, thereby enabling instantaneous contract processing at negligible costs. In this work we present the first complete description of a state channel network that exhibits the following key features. First, it supports virtual multi-party state channels, i.e. state channels that can be created and closedwithoutblockchain interaction and that allow contracts with any number of parties. Second, the worst case time complexity of our protocol isconstantfor arbitrary complex channels. This is in contrast to the existing virtual state channel construction that has worst case time complexity linear in the number of involved parties. In addition to our new construction, we provide a comprehensive model for the modular design and security analysis of our construction.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_21"}, {"primary_key": "3004620", "vector": [], "sparse_vector": [], "title": "Towards Optimal Robust Secret Sharing with Security Against a Rushing Adversary.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Robust secret sharing enables the reconstruction of a secret-shared message in the presence of up tot(out ofn)incorrectshares. The most challenging case is when\\(n = 2t+1\\), which is the largesttfor which the task is still possible, up to a small error probability\\(2^{-\\kappa }\\)and with some overhead in the share size. Recently, <PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [3] proposed a scheme with an (almost) optimal overhead of\\(\\widetilde{O}(\\kappa )\\). This seems to answer the open question posed by <PERSON><PERSON><PERSON> et al. [6] who proposed a scheme with overhead of\\(\\widetilde{O}(n+\\kappa )\\)and asked whether the linear dependency onnwas necessary or not. However, a subtle issue with <PERSON> et al.’s solution is that it (implicitly) assumes anon-rushingadversary, and thus it satisfies aweakernotion of security compared to the scheme by <PERSON><PERSON><PERSON> et al. [6], or to the classical scheme by <PERSON><PERSON> and <PERSON> [13]. In this work, we almost close this gap. We propose a new robust secret sharing scheme that offers full security against a rushing adversary, and that has an overhead of\\(O(\\kappa n^\\varepsilon )\\), where\\(\\varepsilon > 0\\)is arbitrary but fixed. This\\(n^\\varepsilon \\)-factor is obviously worse than the\\(\\mathrm {polylog}(n)\\)-factor hidden in the\\(\\widetilde{O}\\)notation of the scheme of <PERSON> et al. [3], but it greatly improves on the linear dependency onnof the best known scheme that features security against a rushing adversary (when\\(\\kappa \\)is substantially smaller thann). A small variation of our scheme has the same\\(\\widetilde{O}(\\kappa )\\)overhead as the scheme of Bishop et al.andachieves security against a rushing adversary, but suffers from a (slightly) superpolynomial reconstruction complexity.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_16"}, {"primary_key": "3004621", "vector": [], "sparse_vector": [], "title": "SeaSign: Compact Isogeny Signatures from Class Group Actions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a new signature scheme for isogenies that combines the class group actions of CSIDH with the notion of Fiat-Shamir with aborts. Our techniques allow to have signatures of size less than one kilobyte at the 128-bit security level, even with tight security reduction (to a non-standard problem) in the quantum random oracle model. Hence our signatures are potentially shorter than lattice signatures, but signing and verification are currently very expensive.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_26"}, {"primary_key": "3004622", "vector": [], "sparse_vector": [], "title": "Tight Proofs of Space and Replication.", "authors": ["<PERSON>"], "summary": "We construct a concretely practicalproof-of-space(PoS) with arbitrarilytightsecurity based on stacked depth robust graphs and constant-degree expander graphs. Aproof-of-space(PoS) is an interactive proof system where a prover demonstrates that it is persistently using space to store information. A PoS is arbitrarily tight if the honest prover uses exactlyNspace and for any\\(\\epsilon > 0\\)the construction can be tuned such that no adversary can pass verification using less than\\((1-\\epsilon ) N\\)space. Most notably, the degree of the graphs in our construction are independent of\\(\\epsilon \\), and the number of layers is only\\(O(\\log (1/\\epsilon ))\\). The proof size is\\(O(d/\\epsilon )\\). The degreeddepends on the depth robust graphs, which are only required to maintain\\(\\varOmega (N)\\)depth in subgraphs on 80% of the nodes. Our tight PoS is also secure against parallel attacks. Tight proofs of space are necessary forproof-of-replication(PoRep), which is a publicly verifiable proof that the prover is dedicating unique resources to storing one or more retrievable replicas of a specified file. Our main PoS construction can be used as a PoRep, but data extraction is as inefficient as replica generation. We present a second variant of our construction calledZigZag PoRepthat has fast/parallelizable data extraction compared to replica generation and maintains the same space tightness while only increasing the number of levels by roughly a factor two.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_12"}, {"primary_key": "3004623", "vector": [], "sparse_vector": [], "title": "Aggregate Cash Systems: A Cryptographic Investigation of Mimblewimble.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mimblewimble is an electronic cash system proposed by an anonymous author in 2016. It combines several privacy-enhancing techniques initially envisioned for Bitcoin, such as Confidential Transactions (<PERSON>, 2015), non-interactive merging of transactions (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, 2014), and cut-through of transaction inputs and outputs (<PERSON>, 2013). As a remarkable consequence, coins can be deleted once they have been spent while maintaining public verifiability of the ledger, which is not possible in Bitcoin. This results in tremendous space savings for the ledger and efficiency gains for new users, who must verify their view of the system. In this paper, we provide a provable-security analysis for Mimblewimble. We give a precise syntax and formal security definitions for an abstraction of Mimblewimble that we call anaggregate cash system. We then formally prove the security of Mimblewimble in this definitional framework. Our results imply in particular that two natural instantiations (with Pedersen commitments and Schnorr or BLS signatures) are provably secure against inflation and coin theft under standard assumptions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_22"}, {"primary_key": "3004624", "vector": [], "sparse_vector": [], "title": "Proof-of-Stake Protocols for Privacy-Aware Blockchains.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Proof-of-stake (PoS)protocols are emerging as one of the most promising alternative to the wastefulproof-of-work (PoW)protocols for consensus in Blockchains (or distributed ledgers). However, current PoS protocols inherently disclose both theidentityand thewealthof the stakeholders, and thus seem incompatible with privacy-preserving cryptocurrencies (such as ZCash, Monero, etc.). In this paper we initiate the formal study for PoS protocols with privacy properties. Our results include: A (theoretical) feasibility result showing that it is possible to construct a general class ofprivate PoS (PPoS)protocols; and to add privacy to a wide class of PoS protocols, A privacy-preserving version of a popular PoS protocol, Ouroboros Praos. Towards our result, we define the notion ofanonymous verifiable random function, which we believe is of independent interest.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_23"}, {"primary_key": "3004625", "vector": [], "sparse_vector": [], "title": "New Techniques for Efficient Trapdoor Functions and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We develop techniques for constructing trapdoor functions (TDFs) with short image size and advanced security properties. Our approach builds on the recent framework of Garg and Hajiabadi [CRYPTO 2018]. As applications of our techniques, we obtain The first construction of deterministic-encryption schemes for block-source inputs (both for the CPA and CCA cases) based on the Computational Di<PERSON><PERSON>-Hellman (CDH) assumption. Moreover, by applying our efficiency-enhancing techniques, we obtain CDH-based schemes with ciphertext size linear in plaintext size. The first construction of lossy TDFs based on the Decisional Diffie-Hellman (DDH) assumption with image size linear in input size, while retaining the lossiness rate of [Peikert-Waters STOC 2008]. Prior to our work, all constructions of deterministic encryption based even on the stronger DDH assumption incurred a quadratic gap between the ciphertext and plaintext sizes. Moreover, all DDH-based constructions of lossy TDFs had image size quadratic in the input size. At a high level, we break the previous quadratic barriers by introducing a novel technique for encoding input bits via hardcore output bits with the use of erasure-resilient codes. All previous schemes used group elements for encoding input bits, resulting in quadratic expansions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_2"}, {"primary_key": "3004626", "vector": [], "sparse_vector": [], "title": "Building an Efficient Lattice Gadget Toolkit: Subgaussian Sampling and More.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many advanced lattice cryptography applications require efficient algorithms for inverting the so-called “gadget” matrices, which are used to formally describe a digit decomposition problem that produces an output with specific (statistical) properties. The common gadget inversion problems are the classical (often binary) digit decomposition, subgaussian decomposition, Learning with Errors (LWE) decoding, and discrete Gaussian sampling. In this work, we build and implement an efficient lattice gadget toolkit that provides a general treatment of gadget matrices and algorithms for their inversion/sampling. The main contribution of our work is a set of new gadget matrices and algorithms for efficient subgaussian sampling that have a number of major theoretical and practical advantages over previously known algorithms. Another contribution deals with efficient algorithms for LWE decoding and discrete Gaussian sampling in the Residue Number System (RNS) representation. We implement the gadget toolkit in PALISADE and evaluate the performance of our algorithms both in terms of runtime and noise growth. We illustrate the improvements due to our algorithms by implementing a concrete complex application, key-policy attribute-based encryption (KP-ABE), which was previously considered impractical for CPU systems (except for a very small number of attributes). Our runtime improvements for the main bottleneck operation based on subgaussian sampling range from 18x (for 2 attributes) to 289x (for 16 attributes; the maximum number supported by a previous implementation). Our results are applicable to a wide range of other advanced applications in lattice cryptography, such as GSW-based homomorphic encryption schemes, leveled fully homomorphic signatures, other forms of ABE, some program obfuscation constructions, and more.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_23"}, {"primary_key": "3004627", "vector": [], "sparse_vector": [], "title": "An Algebraic Approach to Maliciously Secure Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Private set intersection (PSI) is an important area of research and has been the focus of many works over the past decades. It describes the problem of finding an intersection between the input sets of at least two parties without revealing anything about the input sets apart from their intersection. In this paper, we present a new approach to compute the intersection between sets based on a primitive called Oblivious Linear Function Evaluation (OLE). On an abstract level, we use this primitive to efficiently add two polynomials in a randomized way while preserving the roots of the added polynomials. Setting the roots of the input polynomials to be the elements of the input sets, this directly yields an intersection protocol with optimal asymptotic communication complexity\\(O(m\\kappa )\\). We highlight that the protocol is information-theoretically secure against a malicious adversary assuming OLE. We also present a natural generalization of the 2-party protocol for the fully malicious multi-party case. Our protocol does away with expensive (homomorphic) threshold encryption and zero-knowledge proofs. Instead, we use simple combinatorial techniques to ensure the security. As a result we get a UC-secure protocol with asymptotically optimal communication complexity\\(O((n^2+nm)\\kappa )\\), wherenis the number of parties,mis the set size and\\(\\kappa \\)is the security parameter. Apart from yielding an asymptotic improvement over previous works, our protocols are also conceptually simple and require only simple field arithmetic. Along the way we develop techniques that might be of independent interest.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_6"}, {"primary_key": "3004628", "vector": [], "sparse_vector": [], "title": "Correlated-Source Extractors and Cryptography with Correlated-Random Tapes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider the setting where a party uses correlated random tapes across multiple executions of a cryptographic algorithm. We ask if the security properties could still be preserved in such a setting. As examples, we introduce the notion ofcorrelated-tape zero knowledge, and,correlated-tape multi-party computation, where, the zero-knowledge property, and, the ideal/real model security must still be preserved even if a party uses correlated random tapes in multiple executions. Our constructions are based on a new type of randomness extractor which we callcorrelated-source extractors. Correlated-source extractors can be seen as a dual of non-malleable extractors, and, allow an adversary to choose several tampering functions which are applied to the randomness source. Correlated-source extractors guarantee that even given the output of the extractor on the tampered sources, the output on the original source is still uniformly random. Given (seeded) correlated-source extractors, and,resettably-securecomputation protocols, we show how to directly get a positive result for both correlated-tape zero-knowledge and correlated-tape multi-party computation in the CRS model. This is tight considering the known impossibility results on cryptography with imperfect randomness. Our main technical contribution is an explicit construction of a correlated-source extractor where the length of the seed is independent of the number of tamperings. Additionally, we also provide a (non-explicit) existential result for correlated source extractors with almost optimal parameters.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_19"}, {"primary_key": "3004629", "vector": [], "sparse_vector": [], "title": "Simple Schemes in the Bounded Storage Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The bounded storage model promises unconditional security proofs against computationally unbounded adversaries, so long as the adversary’s space is bounded. In this work, we develop simple new constructions of two-party key agreement, bit commitment, and oblivious transfer in this model. In addition to simplicity, our constructions have several advantages over prior work, including an improved number of rounds and enhanced correctness. Our schemes are based on <PERSON><PERSON>’s lower bound for learning parities.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_17"}, {"primary_key": "3004630", "vector": [], "sparse_vector": [], "title": "Private Anonymous Data Access.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider a scenario where a server holds a huge database that it wants to make accessible to a large group of clients. After an initial setup phase, clients should be able to read arbitrary locations in the database while maintainingprivacy(the server does not learn which locations are being read) andanonymity(the server does not learn which client is performing each read). This should hold even if the server colludes with a subset of the clients. Moreover, the run-time of both the server and the client during each read operation should be low, ideally only poly-logarithmic in the size of the database and the number of clients. We call this notionPrivate Anonymous Data Access(PANDA). PANDA simultaneously combines aspects ofPrivate Information Retrieval(PIR) andOblivious RAM(ORAM). PIR has no initial setup, and allows anybody to privately and anonymously access a public database, but the server’s run-time is linear in the data size. On the other hand, ORAM achieves poly-logarithmic server run-time, but requires an initial setup after which only a single client with a secret key can access the database. The goal of PANDA is to get the best of both worlds: allow many clients to privately and anonymously access the database as in PIR, while having an efficient server as in ORAM. In this work, we constructbounded-collusionPANDA schemes, where the efficiency scales linearly with a bound on the number of corrupted clients that can collude with the server, but is otherwise poly-logarithmic in the data size and the total number of clients. Our solution relies on standard assumptions, namely the existence of fully homomorphic encryption, and combines techniques from both PIR and ORAM. We also extend PANDA to settings where clients canwriteto the database.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_9"}, {"primary_key": "3004631", "vector": [], "sparse_vector": [], "title": "A Modular Treatment of Blind Signatures from Identification Schemes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a modular security treatment of blind signatures derived from linear identification schemes in the random oracle model. To this end, we present a general framework that captures several well known schemes from the literature and allows to prove their security. Our modular security reduction introduces a new security notion for identification schemes called One-More-Man In the Middle Security which we show equivalent to the classical One-More-Unforgeability notion for blind signatures. We also propose a generalized version of the Forking Lemma due to <PERSON><PERSON> and <PERSON> (CCS 2006) and show how it can be used to greatly improve the understandability of the classical security proofs for blind signatures schemes by <PERSON><PERSON><PERSON> and Stern (Journal of Cryptology 2000).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_12"}, {"primary_key": "3004632", "vector": [], "sparse_vector": [], "title": "Attacks only Get Better: How to Break FF3 on Large Domains.", "authors": ["Viet Tung Hoang", "<PERSON>", "<PERSON>"], "summary": "We improve the attack of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CRYPTO’17) on NIST Format-Preserving Encryption standard FF3, reducing the running time from\\(O(N^5)\\)to\\(O(N^{17/6})\\)for domain\\(\\mathbb {Z}_N \\times \\mathbb {Z}_N\\). Concretely, DV’s attack needs about\\(2^{50}\\)operations to recover encrypted 6-digit PINs, whereas ours only spends about\\(2^{30}\\)operations. In realizing this goal, we provide a pedagogical example of how to use distinguishing attacks to speed up slide attacks. In addition, we improve the running time of <PERSON><PERSON>’s known-plaintext attack on 4-round Feistel of domain\\(\\mathbb {Z}_N \\times \\mathbb {Z}_N\\)from\\(O(N^3)\\)time to just\\(O(N^{5/3})\\)time. We also generalize our attacks to a general domain\\(\\mathbb {Z}_M \\times \\mathbb {Z}_N\\), allowing one to recover encrypted SSNs using about\\(2^{50}\\)operations. Finally, we provide some proof-of-concept implementations to empirically validate our results.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_4"}, {"primary_key": "3004633", "vector": [], "sparse_vector": [], "title": "Covert Security with Public Verifiability: Faster, Leaner, and Simpler.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The notion ofcovert securityfor secure two-party computation serves as a compromise between the traditional semi-honest and malicious security definitions. Roughly, covert security ensures that cheating behavior is detected by the honest party with reasonable probability (say, 1/2). It provides more realistic guarantees than semi-honest security with significantly less overhead than is required by malicious security. The rationale for covert security is that it dissuades cheating by parties that care about their reputation and do not want to risk being caught. But a much stronger disincentive is obtained if the honest party can generate a publicly verifiablecertificatewhen cheating is detected. While the corresponding notion of publicly verifiable covert (PVC) security has been explored, existing PVC protocols are complex and less efficient than the best covert protocols, and have impractically large certificates. We propose a novel PVC protocol that significantly improves on prior work. Our protocol uses only “off-the-shelf” primitives (in particular, it avoids signed oblivious transfer) and, for deterrence factor 1/2, has only 20–40% overhead compared to state-of-the-artsemi-honestprotocols. Our protocol also has, for the first time,constant-sizecertificates of cheating (e.g., 354 bytes long at the 128-bit security level). As our protocol offers strong security guarantees with low overhead, we suggest that it is the best choice for many practical applications of secure two-party computation.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_4"}, {"primary_key": "3004634", "vector": [], "sparse_vector": [], "title": "Tight Time-Memory Trade-Offs for Symmetric Encryption.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Concrete security proofs give upper bounds on the attacker’s advantage as a function of its time/query complexity. Cryptanalysis suggests however that other resource limitations – most notably, the attacker’s memory – could make the achievable advantage smaller, and thus these proven bounds too pessimistic. Yet, handling memory limitations has eluded existing security proofs. This paper initiates the study of time-memory trade-offs for basic symmetric cryptography. We show that schemes like counter-mode encryption, which are affected by the Birthday Bound, becomemore secure(in terms of time complexity) as the attacker’s memory is reduced. One key step of this work is a generalization of the Switching Lemma: For adversaries withSbits of memory issuingqdistinct queries, we prove ann-to-nbit random function indistinguishable from a permutation as long as\\(S \\times q \\ll 2^n\\). This result assumes a combinatorial conjecture, which we discuss, and implies right away trade-offs for deterministic, stateful versions of CTR and OFB encryption. We also show an unconditional time-memory trade-off for the security ofrandomizedCTR based on a secure PRF. Via the aforementioned conjecture, we extend the result to assuming a PRP instead, assuming only one-block messages are encrypted. Our results solely rely on standard PRF/PRP security of an underlying block cipher. We frame the core of our proofs within a general framework of indistinguishability for streaming algorithms which may be of independent interest.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_16"}, {"primary_key": "3004635", "vector": [], "sparse_vector": [], "title": "How to Leverage Hardness of Constant-Degree Expanding Polynomials over \\mathbb R R to build i풪 i O.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we introduce and constructD-restricted Functional Encryption (FE) for any constant\\(D \\ge 3\\), based only on the SXDH assumption over bilinear groups. This generalizes the notion of 3-restricted FE recently introduced and constructed by <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON><PERSON> 2018) in the generic bilinear group model. A\\(D=(d+2)\\)-restricted FE scheme is a secret key FE scheme that allows an encryptor to efficiently encrypt a message of the form\\(M=(\\varvec{x},\\varvec{y},\\varvec{z})\\). Here,\\(\\varvec{x}\\in \\mathbb {F}_{\\mathbf {p}}^{d\\times n}\\)and\\(\\varvec{y},\\varvec{z}\\in \\mathbb {F}_{\\mathbf {p}}^n\\). Function keys can be issued for a function\\(f=\\varSigma _{\\varvec{I}= (i_1,..,i_d,j,k)}\\ c_{\\varvec{I}}\\cdot \\varvec{x}[1,i_1] \\cdots \\varvec{x}[d,i_d] \\cdot \\varvec{y}[j]\\cdot \\varvec{z}[k]\\)where the coefficients\\(c_{\\varvec{I}}\\in \\mathbb {F}_{\\mathbf {p}}\\). Knowing the function key and the ciphertext, one can learn\\(f(\\varvec{x},\\varvec{y},\\varvec{z})\\), if this value is bounded in absolute value by some polynomial in the security parameter andn. The security requirement is that the ciphertext hides\\(\\varvec{y}\\)and\\(\\varvec{z}\\), although it is not required to hide\\(\\varvec{x}\\). Thus\\(\\varvec{x}\\)can be seen as a public attribute. D-restricted FE allows for useful evaluation of constant-degree polynomials, while only requiring the SXDH assumption over bilinear groups. As such, it is a powerful tool for leveraging hardness that exists in constant-degree expanding families of polynomials over\\(\\mathbb {R}\\). In particular, we build upon the work of Ananth et al. to show how to build indistinguishability obfuscation (\\(i\\mathcal {O}\\)) assuming only SXDH over bilinear groups, LWE, and assumptions relating to weak pseudorandom properties of constant-degree expanding polynomials over\\(\\mathbb {R}\\).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_9"}, {"primary_key": "3004636", "vector": [], "sparse_vector": [], "title": "Efficient Ratcheting: Almost-Optimal Guarantees for Secure Messaging.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the era of mass surveillance and information breaches, privacy of Internet communication, and messaging in particular, is a growing concern. As secure messaging protocols are executed on the not-so-secure end-user devices, and because their sessions are long-lived, they aim to guarantee strong security even if secret states and local randomness can be exposed. The most basic security properties, including forward secrecy, can be achieved using standard techniques such as authenticated encryption. Modern protocols, such as Signal, go one step further and additionally provide the so-called backward secrecy, or healing from state exposures. These additional guarantees come at the price of a moderate efficiency loss (they require public-key primitives). On the opposite side of the security spectrum are the works by <PERSON><PERSON><PERSON> and <PERSON><PERSON> and by Poettering and Rösler, which characterize the optimal security a secure-messaging scheme can achieve. However, their proof-of-concept constructions suffer from an extreme efficiency loss compared to Signal. Moreover, this caveat seems inherent. This paper explores the area in between: our starting point are the basic, efficient constructions, and then we ask how far we can go towards the optimal security without losing too much efficiency. We present a construction with guarantees much stronger than those achieved by Signal, and slightly weaker than optimal, yet its efficiency is closer to that of Signal (only standard public-key cryptography is used). On a technical level, achieving optimal guarantees inherently requires key-updating public-key primitives, where the update information is allowed to be public. We consider secret update information instead. Since a state exposure temporally breaks confidentiality, we carefully design such secretly-updatable primitives whose security degrades gracefully if the supposedly secret update information leaks.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_6"}, {"primary_key": "3004637", "vector": [], "sparse_vector": [], "title": "Computationally Volume-Hiding Structured Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of structured encryption schemes with computationally-secure leakage. Specifically, we focus on the design of volume-hiding encrypted multi-maps; that is, of encrypted multi-maps that hide the response length to computationally-bounded adversaries. We describe the first volume-hiding STE schemes that do not rely on naïve padding; that is, padding all tuples to the same length. Our first construction has efficient query complexity and storage but can be lossy. We show, however, that the information loss can be bounded with overwhelming probability for a large class of multi-maps (i.e., with lengths distributed according to a Zipf distribution). Our second construction is not lossy and can achieve storage overhead that is asymptotically better than naïve padding for Zipf-distributed multi-maps. We also show how to further improve the storage when the multi-map is highlyconcentratedin the sense that it has a large number of tuples with a large intersection. We achieve these results by leveraging computational assumptions; not just for encryption but, more interestingly, to hide the volumes themselves. Our first construction achieves this using a pseudo-random function whereas our second construction achieves this by relying on the conjectured hardness of the planted densest subgraph problem which is a planted variant of the well-studied densest subgraph problem. This assumption was previously used to design public-key encryptions schemes (<PERSON><PERSON> et al.,STOC ’10) and to study the computational complexity of financial products (<PERSON><PERSON><PERSON> et al.,ICS ’10).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_7"}, {"primary_key": "3004638", "vector": [], "sparse_vector": [], "title": "Group Signatures Without NIZK: From Lattices in the Standard Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a group signature scheme, users can anonymously sign messages on behalf of the group they belong to, yet it is possible to trace the signer when needed. Since the first proposal of lattice-based group signatures in the random oracle model by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (ASIACRYPT 2010), the realization of them in the standard model from lattices has attracted much research interest, however, it has remained unsolved. In this paper, we make progress on this problem by giving the first such construction. Our schemes satisfy CCA-selfless anonymity and full traceability, which are the standard security requirements for group signatures proposed by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (EUROCRYPT 2003) with a slight relaxation in the anonymity requirement suggested by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (SCN 2004). We emphasize that even with this relaxed anonymity requirement, all previous group signature constructions rely on random oracles or NIZKs, where currently NIZKs are not known to be implied from lattice-based assumptions. We propose two constructions that provide tradeoffs regarding the security assumption and efficiency: Our first construction is proven secure assuming the standard LWE and the SIS assumption. The sizes of the public parameters and the signatures grow linearly in the number of users in the system. Our second construction is proven secure assuming the standard LWE and the subexponential hardness of the SIS problem. The sizes of the public parameters and the signatures are independent of the number of users in the system. Technically, we obtain the above schemes by combining a secret key encryption scheme with additional properties and a special type of attribute-based signature (ABS) scheme, thus bypassing the utilization of NIZKs. More specifically, we introduce the notion ofindexedABS, which is a relaxation of standard ABS. The above two schemes are obtained by instantiating the indexed ABS with different constructions. One is a direct construction we propose and the other is based on previous work.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_11"}, {"primary_key": "3004639", "vector": [], "sparse_vector": [], "title": "Designated Verifier/Prover and Preprocessing NIZKs from Diffie-Hellman Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a non-interactive zero-knowledge (NIZK) proof, a prover can non-interactively convince a verifier of a statement without revealing any additional information. Thus far, numerous constructions of NIZKs have been provided in the common reference string (CRS) model (CRS-NIZK) from various assumptions, however, it still remains a long standing open problem to construct them from tools such as pairing-free groups or lattices. Recently, <PERSON> and <PERSON> (CRYPTO’18) made great progress regarding this problem and constructed the first lattice-based NIZK in a relaxed model called NIZKs in the preprocessing model (PP-NIZKs). In this model, there is a trusted statement-independent preprocessing phase where secret information are generated for the prover and verifier. Depending on whether those secret information can be made public, PP-NIZK captures CRS-NIZK, designated-verifier NIZK (DV-NIZK), and designated-prover NIZK (DP-NIZK) as special cases. It was left as an open problem by <PERSON> and <PERSON> whether we can construct such NIZKs from weak paring-free group assumptions such as DDH. As a further matter, all constructions of NIZKs from Di<PERSON><PERSON>-<PERSON>man (DH) type assumptions (regardless of whether it is over a paring-free or paring group) require the proof size to have a multiplicative-overhead\\(|C| \\cdot \\mathsf {poly}(\\kappa )\\), where |C| is the size of the circuit that computes the\\(\\mathbf {NP}\\)relation. In this work, we make progress of constructing (DV, DP, PP)-NIZKs with varying flavors from DH-type assumptions. Our results are summarized as follows: DV-NIZKs for\\(\\mathbf {NP}\\)from the CDH assumption over pairing-free groups. This is the first construction of such NIZKs on pairing-free groups and resolves the open problem posed by Kim and Wu (CRYPTO’18). DP-NIZKs for\\(\\mathbf {NP}\\)with short proof size from a DH-type assumption over pairing groups. Here, the proof size has an additive-overhead\\(|C|+\\mathsf {poly}(\\kappa )\\)rather then an multiplicative-overhead\\(|C| \\cdot \\mathsf {poly}(\\kappa )\\). This is the first construction of such NIZKs (including CRS-NIZKs) that does not rely on the LWE assumption, fully-homomorphic encryption, indistinguishability obfuscation, or non-falsifiable assumptions. PP-NIZK for\\(\\mathbf {NP}\\)with short proof size from the DDH assumption over pairing-free groups. This is the first PP-NIZK that achieves a short proof size from a weak and static DH-type assumption such as DDH. Similarly to the above DP-NIZK, the proof size is\\(|C|+\\mathsf {poly}(\\kappa )\\). This too serves as a solution to the open problem posed by Kim and Wu (CRYPTO’18). Along the way, we construct two new homomorphic authentication (HomAuth) schemes which may be of independent interest.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_22"}, {"primary_key": "3004640", "vector": [], "sparse_vector": [], "title": "(R)CCA Secure Updatable Encryption with Integrity Protection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "An updatable encryption scheme allows a data host to update ciphertexts of a client from an old to a new key, given so-called update tokens from the client. Rotation of the encryption key is a common requirement in practice in order to mitigate the impact of key compromises over time. There are two incarnations of updatable encryption: One is ciphertext-dependent, i.e. the data owner has to (partially) download all of his data and derive a dedicated token per ciphertext. <PERSON><PERSON><PERSON> et al. (CRYPTO’17) proposed CCA and CTXT secure schemes in this setting. The other, more convenient variant is ciphertext-independent, i.e., it allows a single token to updateallciphertexts. However, so far, the broader functionality of tokens in this setting comes at the price of considerably weaker security: the existing schemes by <PERSON><PERSON> et al. (CRYPTO’13) and <PERSON><PERSON> and <PERSON><PERSON><PERSON> (EUROCRYPT’18) only achieve CPA security and provide no integrity protection. Arguably, when targeting the scenario of outsourcing data to an untrusted host, plaintext integrity should be a minimal security requirement. Otherwise, the data host may alter or inject ciphertexts arbitrarily. Indeed, the schemes from BLMR13 and LT18 suffer from this weakness, and even EPRS17 only provides integrity against adversaries which cannot arbitrarily inject ciphertexts. In this work, we provide the first ciphertext-independentupdatable encryption schemes with security beyond CPA, in particular providing strong integrity protection. Our constructions and security proofs of updatable encryption schemes are surprisingly modular. We give a generic transformation that allows key-rotation and confidentiality/integrity of the scheme to be treated almost separately, i.e., security of the updatable scheme is derived from simple properties of its static building blocks. An interesting side effect of our generic approach is that it immediately implies the unlinkability of ciphertext updates that was introduced as an essential additional property of updatable encryption by EPRS17 and LT18.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_3"}, {"primary_key": "3004641", "vector": [], "sparse_vector": [], "title": "XOR-Counts and Lightweight Multiplication with Fixed Elements in Binary Finite Fields.", "authors": ["<PERSON><PERSON>"], "summary": "XOR-metrics measure the efficiency of certain arithmetic operations in binary finite fields. We prove some new results about two different XOR-metrics that have been used in the past. In particular, we disprove a conjecture from [10]. We consider implementations of multiplication with one fixed element in a binary finite field. Here we achieve a complete characterization of all elements whose multiplication matrix can be implemented using exactly 2 XOR-operations, confirming a conjecture from [2]. Further, we provide new results and examples in more general cases, showing that significant improvements in implementations are possible.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_10"}, {"primary_key": "3004642", "vector": [], "sparse_vector": [], "title": "Compact Adaptively Secure ABE for \\mathsf NC1 from k-Lin.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present compact attribute-based encryption (ABE) schemes for\\(\\mathsf {NC^1}\\)that are adaptively secure under thek-Lin assumption with polynomial security loss. Our KP-ABE scheme achieves ciphertext size that is linear in the attribute length and independent of the policy size even in the many-use setting, and we achieve an analogous efficiency guarantee for CP-ABE. This resolves the central open problem posed by <PERSON><PERSON><PERSON> and <PERSON> (CRYPTO 2011). Previous adaptively secure constructions either impose an attribute “one-use restriction” (or the ciphertext size grows with the policy size), or requireq-type assumptions.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_1"}, {"primary_key": "3004643", "vector": [], "sparse_vector": [], "title": "From Collisions to Chosen-Prefix Collisions Application to Full SHA-1.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A chosen-prefix collision attack is a stronger variant of a collision attack, where an arbitrary pair of challenge prefixes are turned into a collision. Chosen-prefix collisions are usually significantly harder to produce than (identical-prefix) collisions, but the practical impact of such an attack is much larger. While many cryptographic constructions rely on collision-resistance for their security proofs, collision attacks are hard to turn into break of concrete protocols, because the adversary has a limited control over the colliding messages. On the other hand, chosen-prefix collisions have been shown to break certificates (by creating a rogue CA) and many internet protocols (TLS, SSH, IPsec). In this article, we propose new techniques to turn collision attacks into chosen-prefix collision attacks. Our strategy is composed of two phases: first a birthday search that aims at taking the random chaining variable difference (due to the chosen-prefix model) to a set of pre-defined target differences. Then, using a multi-block approach, carefully analysing the clustering effect, we map this new chaining variable difference to a colliding pair of states using techniques developed for collision attacks. We apply those techniques toMD5andSHA-1, and obtain improved attacks. In particular, we have a chosen-prefix collision attack againstSHA-1with complexity between\\(2^{66.9}\\)and\\(2^{69.4}\\)(depending on assumptions about the cost of finding near-collision blocks), while the best-known attack has complexity\\(2^{77.1}\\). This is within a small factor of the complexity of the classical collision attack onSHA-1(estimated as\\(2^{64.7}\\)). This represents yet another warning that industries and users have to move away from usingSHA-1as soon as possible.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_18"}, {"primary_key": "3004644", "vector": [], "sparse_vector": [], "title": "Preimage Attacks on Round-Reduced Keccak-224/256 via an Allocating Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present new preimage attacks on standardKeccak-224 andKeccak-256 that are reduced to 3 and 4 rounds. An allocating approach is used in the attacks, and the whole complexity is allocated to two stages, such that fewer constraints are considered and the complexity is lowered in each stage. Specifically, we are trying to find a 2-block preimage, instead of a 1-block one, for a given hash value, and the first and second message blocks are found in two stages, respectively. Both the message blocks are constrained by a set of newly proposed conditions on the middle state, which are weaker than those brought by the initial values and the hash values. Thus, the complexities in the two stages are both lower than that of finding a 1-block preimage directly. Together with the basic allocating approach, an improved method is given to balance the complexities of two stages, and hence, obtains the optimal attacks. As a result, we present the best theoretical preimage attacks onKeccak-224 andKeccak-256 that are reduced to 3 and 4 rounds. Moreover, we practically found a (second) preimage for 3-roundKeccak-224 with a complexity of\\(2^{39.39}\\).", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_19"}, {"primary_key": "3004645", "vector": [], "sparse_vector": [], "title": "On Finding Quantum Multi-collisions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ak-collision for a compressing hash functionHis a set ofkdistinct inputs that all map to the same output. In this work, we show that for any constantk,\\(\\varTheta \\left( N^{\\frac{1}{2}(1-\\frac{1}{2^k-1})}\\right) \\)quantum queries are both necessary and sufficient to achieve ak-collision with constant probability. This improves on both the best prior upper bound (<PERSON><PERSON><PERSON> et al., ASIACRYPT 2017) and provides the first non-trivial lower bound, completely resolving the problem.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_7"}, {"primary_key": "3004646", "vector": [], "sparse_vector": [], "title": "Symbolic Encryption with Pseudorandom Keys.", "authors": ["<PERSON><PERSON>"], "summary": "We give an efficient decision procedure that, on input two (acyclic) expressions making arbitrary use of common cryptographic primitives (namely, encryption and pseudorandom generators), determines (in polynomial time) if the two expressions produce computationally indistinguishable distributions for any cryptographic instantiation satisfying the standard security notions of pseudorandomness and indistinguishability under chosen plaintext attack. The procedure works by mapping each expression to a symbolic pattern that captures, in a fully abstract way, the information revealed by the expression to a computationally bounded observer. Our main result shows that if two expressions are mapped to different symbolic patterns, then there are secure pseudorandom generators and encryption schemes for which the two distributions can be distinguished with overwhelming advantage. At the same time if any two (acyclic) expressions are mapped to the same pattern, then the associated distributions are indistinguishable.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_3"}, {"primary_key": "3004647", "vector": [], "sparse_vector": [], "title": "Approx-SVP in Ideal Lattices with Pre-processing.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe an algorithm to solve the approximate Shortest Vector Problem for lattices corresponding to ideals of the ring of integers of an arbitrary number fieldK. This algorithm has a pre-processing phase, whose run-time is exponential in\\(\\log |\\varDelta |\\)with\\(\\varDelta \\)the discriminant ofK. Importantly, this pre-processing phase depends only onK. The pre-processing phase outputs an “advice”, whose bit-size is no more than the run-time of the query phase. Given this advice, the query phase of the algorithm takes as input any idealIof the ring of integers, and outputs an element ofIwhich is at most\\(\\exp (\\widetilde{O}((\\log |\\varDelta |)^{\\alpha +1}/n))\\)times longer than a shortest non-zero element ofI(with respect to the Euclidean norm of its canonical embedding). This query phase runs in time and space\\(\\exp (\\widetilde{O}( (\\log |\\varDelta |)^{\\max (2/3, 1-2\\alpha )}))\\)in the classical setting, and\\(\\exp (\\widetilde{O}((\\log |\\varDelta |)^{1-2\\alpha }))\\)in the quantum setting. The parameter\\(\\alpha \\)can be chosen arbitrarily in [0, 1 / 2]. Both correctness and cost analyses rely on heuristic assumptions, whose validity is consistent with experiments. The algorithm builds upon the algorithms from Crameret al.[EUROCRYPT 2016] and Crameret al.[EUROCRYPT 2017]. It relies on the framework from Buchmann [Séminaire de théorie des nombres 1990], which allows to merge them and to extend their applicability from prime-power cyclotomic fields to all number fields. The cost improvements are obtained by allowing precomputations that depend on the field only.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_24"}, {"primary_key": "3004648", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Differentially Private RAMs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this work, we study privacy-preserving storage primitives that are suitable for use in data analysis on outsourced databases within the differential privacy framework. The goal in differentially private data analysis is to disclose global properties of a group without compromising any individual’s privacy. Typically, differentially private adversaries only ever learn global properties. For the case of outsourced databases, the adversary also views the patterns of access to data. Oblivious RAM (ORAM) can be used to hide access patterns but ORAM might be excessive as in some settings it could be sufficient to be compatible with differential privacy and only protect the privacy of individual accesses. We consider\\((\\epsilon ,\\delta )\\)-Differentially Private RAM, a weakening of ORAM that only protects individual operations and seems better suited for use in data analysis on outsourced databases. As differentially private RAM has weaker security than ORAM, there is hope that we can bypass the\\(\\varOmega (\\log (nb/c))\\)bandwidth lower bounds for ORAM by <PERSON> and <PERSON> [CRYPTO ’18] for storing an array ofnb-bit entries and a client withcbits of memory. We answer in the negative and present an\\(\\varOmega (\\log (nb/c))\\)bandwidth lower bound for privacy budgets of\\(\\epsilon = O(1)\\)and\\(\\delta \\le 1/3\\). Theinformation transfertechnique used for ORAM lower bounds does not seem adaptable for use with the weaker security guarantees of differential privacy. Instead, we prove our lower bounds by adapting thechronogramtechnique to our setting. To our knowledge, this is the first work that uses the chronogram technique for lower bounds on privacy-preserving storage primitives.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17653-2_14"}, {"primary_key": "3004649", "vector": [], "sparse_vector": [], "title": "Efficient Circuit-Based PSI with Linear Communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new protocol for computing a circuit which implements the private set intersection functionality (PSI). Using circuits for this task is advantageous over the usage of specific protocols for PSI, since many applications of PSI do not need to compute the intersection itself but rather functions based on the items in the intersection. Our protocol is thefirst circuit-based PSI protocol to achieve linear communication complexity. It is also concretely more efficient than all previous circuit-based PSI protocols. For example, for sets of size\\(2^{20}\\)it improves the communication of the recent work of <PERSON><PERSON> et al. (EUROCRYPT’18) by more than 10 times, and improves the run time by a factor of 2.8x in the LAN setting, and by a factor of 5.8x in the WAN setting. Our protocol is based on the usage of a protocol for computing oblivious programmable pseudo-random functions (OPPRF), and more specifically on our technique to amortize the cost of batching together multiple invocations of OPPRF.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_5"}, {"primary_key": "3004650", "vector": [], "sparse_vector": [], "title": "Reusable Designated-Verifier NIZKs for all NP from CDH.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Non-interactive zero-knowledge proofs (NIZKs) are a fundamental cryptographic primitive. Despite a long history of research, we only know how to construct NIZKs under a few select assumptions, such as the hardness of factoring or using bilinear maps. Notably, there are no known constructions based on either the computational or decisional Di<PERSON><PERSON>-<PERSON>man (CDH/DDH) assumption without relying on a bilinear map. In this paper, we study a relaxation of NIZKs in thedesignated verifiersetting (DV-NIZK), in which the public common-reference string is generated together with a secret key that is given to the verifier in order to verify proofs. In this setting, we distinguish betweenone-timeandreusableschemes, depending on whether they can be used to prove only a single statement or arbitrarily many statements. For reusable schemes, the main difficulty is to ensure that soundness continues to hold even when the malicious prover learns whether various proofs are accepted or rejected by the verifier. One-time DV-NIZKs are known to exist for general NP statements assuming only public-key encryption. However, prior to this work, we did not have any construction of reusable DV-NIZKs for general NP statements from any assumption under which we didn’t already also have standard NIZKs. In this work, we construct reusable DV-NIZKs for general NP statements under the CDH assumption, without requiring a bilinear map. Our construction is based on thehidden-bits paradigm, which was previously used to construct standard NIZKs. We define a cryptographic primitive called ahidden-bits generator (HBG), along with a designated-verifier variant (DV-HBG), which modularly abstract out how to use this paradigm to get both standard NIZKs and reusable DV-NIZKs. We construct a DV-HBG scheme under the CDH assumption by relying on techniques from the Cramer-Shoup hash-proof system, and this yields our reusable DV-NIZK for general NP statements under CDH. We also consider a strengthening of DV-NIZKs to themalicious designated-verifiersetting (MDV-NIZK) where the setup consists of an honestly generated common random string and the verifier then gets to choose his own (potentially malicious) public/secret key pair to generate/verify proofs. We construct MDV-NIZKs under the “one-more CDH” assumption without relying on bilinear maps.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_21"}, {"primary_key": "3004651", "vector": [], "sparse_vector": [], "title": "Efficient Verifiable Delay Functions.", "authors": ["<PERSON>"], "summary": "We construct a verifiable delay function (VDF). A VDF is a function whose evaluation requires running a given number of sequential steps, yet the result can be efficiently verified. They have applications in decentralised systems, such as the generation of trustworthy public randomness in a trustless environment, or resource-efficient blockchains. To construct our VDF, we actually build atrapdoorVDF. A trapdoor VDF is essentially a VDF which can be evaluated efficiently by parties who know a secret (the trapdoor). By setting up this scheme in a way that the trapdoor is unknown (not even by the party running the setup, so that there is no need for a trusted setup environment), we obtain a simple VDF. Our construction is based on groups of unknown order such as an RSA group, or the class group of an imaginary quadratic field. The output of our construction is very short (the result and the proof of correctness are each a single element of the group), and the verification of correctness is very efficient.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_13"}, {"primary_key": "3004652", "vector": [], "sparse_vector": [], "title": "An Analysis of NIST SP 800-90A.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We investigate the security properties of the three deterministic random bit generator (DRBG) mechanisms in NIST SP 800-90A [2]. The standard received considerable negative attention due to the controversy surrounding the now retracted\\(\\mathsf{{DualEC\\text {-}DRBG}}\\), which appeared in earlier versions. Perhaps because of the attention paid to the DualEC, the other algorithms in the standard have received surprisingly patchy analysis to date, despite widespread deployment. This paper addresses a number of these gaps in analysis, with a particular focus on\\(\\mathsf{{HASH\\text {-}DRBG}}\\)and\\(\\mathsf{{HMAC\\text {-}DRBG}}\\). We uncover a mix of positive and less positive results. On the positive side, we prove (with a caveat) the robustness [13] of\\(\\mathsf{{HASH\\text {-}DRBG}}\\)and\\(\\mathsf{{HMAC\\text {-}DRBG}}\\)in the random oracle model (ROM). Regarding the caveat, we show that if an optional input is omitted, then – contrary to claims in the standard—\\(\\mathsf{{HMAC\\text {-}DRBG}}\\)does not even achieve the (weaker) property of forward security. We then conduct a more informal and practice-oriented exploration of flexibility in the standard. Specifically, we argue that these DRBGs have the property that partial state leakage may lead security to break down in unexpected ways. We highlight implementation choices allowed by the overly flexible standard that exacerbate both the likelihood, and impact, of such attacks. While our attacks are theoretical, an analysis of two open source implementations of\\(\\mathsf{{CTR\\text {-}DRBG}}\\)shows that these potentially problematic implementation choices are made in the real world.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17656-3_6"}, {"primary_key": "3004653", "vector": [], "sparse_vector": [], "title": "On ELFs, Deterministic Encryption, and Correlated-Input Security.", "authors": ["<PERSON>"], "summary": "We construct deterministic public key encryption secure for anyconstantnumber ofarbitrarily correlated computationally unpredictablemessages. Prior works required either random oracles or non-standard knowledge assumptions. In contrast, our constructions are based on the exponential hardness of DDH, which is plausible in elliptic curve groups. Our central tool is a newtrapdoored extremely lossy function, which modifies extremely lossy functions by adding a trapdoor.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_1"}, {"primary_key": "3004654", "vector": [], "sparse_vector": [], "title": "Quantum Lightning Never Strikes the Same State Twice.", "authors": ["<PERSON>"], "summary": "Public key quantum money can be seen as a version of the quantum no-cloning theorem that holds even when the quantum states can be verified by the adversary. In this work, we investigatequantum lightningwhere no-cloning holdseven when the adversary herself generates the quantum state to be cloned. We then study quantum money and quantum lightning, showing the following results: We demonstrate the usefulness of quantum lightning beyond quantum money by showing several potential applications, such as generating random strings with a proof of entropy, to completely decentralized cryptocurrency without a block-chain, where transactions is instant and local. We give Either/Or results for quantum money/lightning, showing that either signatures/hash functions/commitment schemes meet very strong recently proposed notions of security, or they yield quantum money or lightning. Given the difficulty in constructing public key quantum money, this suggests that natural schemes do attain strong security guarantees. We show that instantiating the quantum money scheme of <PERSON><PERSON> and Christiano [STOC’12] withindistinguishability obfuscationthat is secure against quantum computers yields a secure quantum money scheme. This construction can be seen as an instance of our Either/Or result for signatures, giving the first separation between two security notions for signatures from the literature. Finally, we give a plausible construction for quantum lightning, which we prove secure under an assumption related to the multi-collision resistance of degree-2 hash functions. Our construction is inspired by our Either/Or result for hash functions, and yields the first plausible standard model instantiation of anon-collapsingcollision resistant hash function. This improves on a result of Unruh [Eurocrypt’16] which is relative to a quantum oracle.", "published": "2019-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-17659-4_14"}]