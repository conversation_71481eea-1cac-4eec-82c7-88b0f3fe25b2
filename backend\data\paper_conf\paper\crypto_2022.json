[{"primary_key": "1637566", "vector": [], "sparse_vector": [], "title": "Password-Authenticated Key Exchange from Group Actions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present two provably secure password-authenticated key exchange (PAKE) protocols based on a commutative group action. To date the most important instantiation of isogeny-based group actions is given by CSIDH. To model the properties more accurately, we extend the framework of cryptographic group actions (<PERSON><PERSON><PERSON> et al., ASIACRYPT 2020) by the ability of computing the quadratic twist of an elliptic curve. This property is always present in the CSIDH setting and turns out to be crucial in the security analysis of our PAKE protocols. Despite the resemblance, the translation of Diffie-Hellman based PAKE protocols to group actions either does not work with known techniques or is insecure (“How not to create an isogeny-based PAKE”, <PERSON><PERSON><PERSON><PERSON><PERSON> et al., ACNS 2020). We overcome the difficulties mentioned in previous work by using a “bit-by-bit” approach, where each password bit is considered separately. Our first protocol\\(\\textsf{X}\\text {-}\\textsf{GA}\\text {-}\\textsf{PAKE}_\\ell \\)can be executed in a single round. Both parties need to send two set elements for each password bit in order to prevent offline dictionary attacks. The second protocol\\(\\mathsf {Com\\text {-}GA\\text {-}PAKE}_\\ell \\)requires only one set element per password bit, but one party has to send a commitment on its message first. We also discuss different optimizations that can be used to reduce the computational cost. We provide comprehensive security proofs for our base protocols and deduce security for the optimized versions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_24"}, {"primary_key": "1637567", "vector": [], "sparse_vector": [], "title": "An Algebraic Framework for Silent Preprocessing with Trustless Setup and Active Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, number-theoretic assumptions including DDH, DCR and QR have been used to build powerful tools for secure computation, in the form ofhomomorphic secret-sharing(HSS), which leads to secure two-party computation protocols with succinct communication, andpseudorandom correlation functions(PCFs), which allow non-interactive generation of a large quantity of correlated randomness. In this work, we present a group-theoretic framework for these classes of constructions, which unifies their approach to computing distributed discrete logarithms in various groups. We cast existing constructions in our framework, and also present new constructions, including one based on class groups of imaginary quadratic fields. This leads to the first construction of two-party homomorphic secret sharing for branching programs from class group assumptions. Using our framework, we also obtain pseudorandom correlation functions for generating oblivious transfer and vector-OLE correlations from number-theoretic assumptions. These have atrustless, public-key setupwhen instantiating our framework using class groups. Previously, such constructions either needed a trusted setup in the form of an RSA modulus with unknown factorisation, or relied on multi-key fully homomorphic encryption from the learning with errors assumption. We also show how to upgrade our constructions to achieve active security using appropriate zero-knowledge proofs. In the random oracle model, this leads to a one-round, actively secure protocol for setting up the PCF, as well as a 3-round, actively secure HSS-based protocol for secure two-party computation of branching programs with succinct communication.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_15"}, {"primary_key": "1637568", "vector": [], "sparse_vector": [], "title": "Multi-input Attribute Based Encryption and Predicate Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by several new and natural applications, we initiate the study of multi-input predicate encryption (\\(\\textsf{miPE}\\)) and further develop multi-input attribute based encryption (\\(\\textsf{miABE}\\)). Our contributions are: Formalizing Security:We provide definitions for\\(\\textsf{miABE}\\)and\\(\\textsf{miPE}\\)in the symmetric key setting and formalize security in the standardindistinguishability(IND) paradigm, againstunboundedcollusions. Two-input\\({\\textsf{ABE}}\\)for\\({\\textsf{NC}}_1\\)from\\(\\textsf{LWE}\\)and Pairings:We provide the first constructions for two-inputkey-policy\\({\\textsf{ABE}}\\)for\\({\\textsf{NC}}_1\\)from\\(\\textsf{LWE}\\)and pairings. Our construction leverages a surprising connection between techniques recently developed by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Eurocrypt, 2020) in the context of succinctsingle-inputciphertext-policy\\({\\textsf{ABE}}\\), to the seemingly unrelated problem oftwo-inputkey-policy\\({\\textsf{ABE}}\\). Similarly to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, our construction is proven secure in the bilinear generic group model. By leveraging inner product functional encryption and using (a variant of) the KOALA knowledge assumption, we obtain a construction in the standard model analogously to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (TCC, 2020). Heuristic two-input\\({\\textsf{ABE}}\\)for\\(\\textsf{P}\\)from Lattices:We show that techniques developed for succinct single-input ciphertext-policy\\({\\textsf{ABE}}\\)by Brakerski and Vaikuntanathan (ITCS 2022) can also be seen from the lens of\\(\\textsf{miABE}\\)and obtain the first two-input key-policy\\({\\textsf{ABE}}\\)from lattices for\\(\\textsf{P}\\). Heuristic three-input\\({\\textsf{ABE}}\\)and\\({\\textsf{PE}}\\)for\\({\\textsf{NC}}_1\\)from Pairings and Lattices:We obtain the firstthree-input\\({\\textsf{ABE}}\\)for\\({\\textsf{NC}}_1\\)by harnessing the powers of both the Agrawal-Yamada and the Brakerski-Vaikuntanathan constructions. Multi-input\\({\\textsf{ABE}}\\)to multi-input\\({\\textsf{PE}}\\)via Lockable Obfuscation:We provide a generic compiler that lifts multi-input\\({\\textsf{ABE}}\\)to multi-input\\({\\textsf{PE}}\\)by relying on the hiding properties of Lockable Obfuscation (\\(\\textsf{LO}\\)) by Wichs-Zirdelis and Goyal-Koppula-Waters (FOCS 2018), which can be based on\\(\\textsf{LWE}\\). Our compiler generalises such a compiler for single-input setting to the much more challenging setting of multiple inputs. By instantiating our compiler with our new two and three-input\\({\\textsf{ABE}}\\)schemes, we obtain the first constructions of two and three-input\\({\\textsf{PE}}\\)schemes. Our constructions of multi-input\\({\\textsf{ABE}}\\)provide the first improvement to the compression factor ofnon-trivially exponentially efficient Witness Encryptiondefined by Brakerski et al. (SCN 2018) without relying on compact functional encryption or indistinguishability obfuscation. We believe that the unexpected connection between succinct single-input ciphertext-policy\\({\\textsf{ABE}}\\)and multi-input key-policy\\({\\textsf{ABE}}\\)may lead to a new pathway for witness encryption. We remark that our constructions provide the first candidates for a nontrivial class of\\({\\textsf{miFE}}\\)without needing\\(\\textsf{LPN}\\)or low depth\\(\\textsf{PRG}\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_21"}, {"primary_key": "1637569", "vector": [], "sparse_vector": [], "title": "Time-Space Lower Bounds for Finding Collisions in Merkle-Damgård Hash Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the problem of findingB-block-long collisions in Merkle-Damgård Hash Functions in the auxiliary-input random oracle model, in which an attacker gets a piece ofS-bit advice about the random oracle and makesToracle queries. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 2020), based on the work of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON> (EUROCRYPT 2018), showed a simple attack for\\(2\\le B\\le T\\)(with respect to a random salt). The attack achieves advantage\\(\\widetilde{\\varOmega }(STB/2^n+T^2/2^n)\\)wherenis the output length of the random oracle. They conjectured that this attack is optimal. However, this so-called STB conjecture was only proved for\\(B\\approx T\\)and\\(B=2\\). Very recently, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (CRYPTO 22) confirmed STB conjecture for all constant values ofB, and provided an\\(\\widetilde{O}(S^4TB^2/2^n+T^2/2^n)\\)bound for all choices ofB. In this work, we prove an\\(\\widetilde{O}((STB/2^n)\\cdot \\max \\{1,ST^2/2^n\\}+ T^2/2^n)\\)bound for every\\(2< B < T\\). Our bound confirms the STB conjecture for\\(ST^2\\le 2^n\\), and is optimal up to a factor ofSfor\\(ST^2>2^n\\)(note as\\(T^2\\)is always at most\\(2^n\\), otherwise finding a collision is trivial by the birthday attack). Our result subsumes all previous upper bounds for all ranges of parameters except for\\(B=\\widetilde{O}(1)\\)and\\(ST^2>2^n\\). We obtain our results by adopting and refining the technique of Chung, Guo, Liu, and Qian (FOCS 2020). Our approach yields more modular proofs and sheds light on how to bypass the limitations of prior techniques. Along the way, we obtain a considerably simpler and illuminating proof for\\(B=2\\), recovering the main result of Akshima, Cash, Drucker and Wee.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_7"}, {"primary_key": "1637570", "vector": [], "sparse_vector": [], "title": "Lattice-Based SNARKs: Publicly Verifiable, Preprocessing, and Recursively Composable - (Extended Abstract).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "A succinct non-interactive argument of knowledge (SNARK) allows a prover to produce a short proof that certifies the veracity of a certain NP-statement. In the last decade, a large body of work has studied candidate constructions that are secure against quantum attackers. Unfortunately, no known candidate matches the efficiency and desirable features of (pre-quantum) constructions based on bilinear pairings. In this work, we make progress on this question. We propose the first lattice-based SNARK that simultaneously satisfies many desirable properties: It (i) is tentatively post-quantum secure, (ii) is publicly-verifiable, (iii) has a logarithmic-time verifier and (iv) has a purely algebraic structure making it amenable to efficient recursive composition. Our construction stems from a general technical toolkit that we develop to translate pairing-based schemes to lattice-based ones. At the heart of our SNARK is a new lattice-based vector commitment (VC) scheme supporting openings to constant-degree multivariate polynomial maps, which is a candidate solution for the open problem of constructing VC schemes with openings to beyond linear functions. However, the security of our constructions is based on a new family of lattice-based computational assumptions which naturally generalises the standard Short Integer Solution (SIS) assumption.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_4"}, {"primary_key": "1637571", "vector": [], "sparse_vector": [], "title": "On the Insider Security of MLS.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "TheMessaging Layer Security(MLS) protocol is an open standard for end-to-end (E2E) secure group messaging being developed by the IETF, poised for deployment to consumers, industry, and government. It is designed to provide E2E privacy and authenticity for messages in long-lived sessions whenever possible, despite the participation (at times) of malicious insiders that can adaptively interact with the PKI at will, actively deviate from the protocol, leak honest parties’ states, and fully control the network. The core of the MLS protocol (from which it inherits essentially all of its efficiency and security properties) is aContinuous Group Key Agreement(CGKA) protocol. It provides asynchronous E2Egroup managementby allowing group members to agree on a fresh independent symmetric key after every change to the group’s state (e.g. when someone joins/leaves the group). In this work, we make progress towards a precise understanding of the insider security of MLS (Draft 12). On the theory side, we overcome several subtleties to formulate the first notion of insider security for CGKA (or group messaging). Next, we isolate the core components of MLS to obtain a CGKA protocol we dubInsider Secure TreeKEM(ITK). Finally, we give a rigorous security proof for ITK. In particular, this work also initiates the study of insider secure CGKA and group messaging protocols. Along the way we give three new (very practical) attacks on MLS and corresponding fixes. (Those fixes have now been included into the standard.) We also describe a second attack against MLS-like CGKA protocols proven secure under all previously considered security notions (including those designed specifically to analyze MLS). These attacks highlight the pitfalls in simplifying security notions even in the name of tractability.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_2"}, {"primary_key": "1637572", "vector": [], "sparse_vector": [], "title": "On the Feasibility of Unclonable Encryption, and More.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Unclonable encryption, first introduced by <PERSON><PERSON> and <PERSON> (TQC’20), is a one-time encryption scheme with the following security guarantee: any non-local adversary\\((\\mathcal {A},\\mathcal {B},\\mathcal {C})\\)cannot simultaneously distinguish encryptions of two equal length messages. This notion is termed as unclonable indistinguishability. Prior works focused on achieving a weaker notion of unclonable encryption, where we required that any non-local adversary\\((\\mathcal {A},\\mathcal {B},\\mathcal {C})\\)cannot simultaneously recover the entire messagem. Seemingly innocuous, understanding the feasibility of encryption schemes satisfying unclonable indistinguishability (even for 1-bit messages) has remained elusive. We make progress towards establishing the feasibility of unclonable encryption. We show that encryption schemes satisfying unclonable indistinguishability exist unconditionally in the quantum random oracle model. Towards understanding the necessity of oracles, we present a negative result stipulating that a large class of encryption schemes cannot satisfy unclonable indistinguishability. Finally, we also establish the feasibility of another closely related primitive: copy-protection for single-bit output point functions. Prior works only established the feasibility of copy-protection for multi-bit output point functions or they achieved constant security error for single-bit output point functions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_8"}, {"primary_key": "1637573", "vector": [], "sparse_vector": [], "title": "Cryptography from Pseudorandom Quantum States.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pseudorandom states, introduced by <PERSON>, <PERSON> and <PERSON> (Crypto’18), are efficiently-computable quantum states that are computationally indistinguishable from Haar-random states. One-way functions imply the existence of pseudorandom states, but <PERSON><PERSON><PERSON><PERSON> (TQC’20) recently constructed an oracle relative to which there are no one-way functions but pseudorandom states still exist. Motivated by this, we study the intriguing possibility of basing interesting cryptographic tasks on pseudorandom states. We construct, assuming the existence of pseudorandom state generators that map a\\(\\lambda \\)-bit seed to a\\(\\omega (\\log \\lambda )\\)-qubit state, (a) statistically binding and computationally hiding commitments and (b) pseudo one-time encryption schemes. A consequence of (a) is that pseudorandom states are sufficient to construct maliciously secure multiparty computation protocols in the dishonest majority setting. Our constructions are derived via a new notion calledpseudorandom function-like states(PRFS), a generalization of pseudorandom states that parallels the classical notion of pseudorandom functions. Beyond the above two applications, we believe our notion can effectively replace pseudorandom functions in many other cryptographic applications.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_8"}, {"primary_key": "1637574", "vector": [], "sparse_vector": [], "title": "Quadratic Multiparty Randomized Encodings Beyond Honest Majority and Their Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Or Karni", "<PERSON><PERSON><PERSON>"], "summary": "Multiparty randomized encodings (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, SICOMP 2021) reduce the task of securely computing a complicated multiparty functionalityfto the task of securely computing a simpler functionalityg. The reduction is non-interactive and preserves information-theoretic security against a passive (semi-honest) adversary, also referred to asprivacy. The special case of a degree-2 encodingg(2MPRE) has recently found several applications to secure multiparty computation (MPC) with either information-theoretic security or making black-box access to cryptographic primitives. Unfortunately, as all known constructions are based on information-theoretic MPC protocols in the plain model, they can only be private with an honest majority. In this paper, we break the honest-majority barrier and present the first construction of general 2MPRE that remains secure in the presence of a dishonest majority. Our construction encodes everyn-party functionalityfby a 2MPRE that tolerates at most\\(t=\\lfloor 2n/3\\rfloor \\)passive corruptions. We derive several applications including: (1) The first non-interactive client-server MPC protocol with perfect privacy against any coalition of a minority of the servers and up totof thenclients; (2) Completeness of 3-party functionalities under non-interactivet-private reductions; and (3) A single-roundt-private reduction from general-MPC to an ideal oblivious transfer (OT). These positive results partially resolve open questions that were posed in several previous works. We also show thatt-private 2MPREs are necessary for solving (2) and (3), thus establishing new equivalence theorems between these three notions. Finally, we present a new approach for constructing fully-private 2MPREs based on multi-round protocols in the OT-hybrid model that achieveperfect privacyagainst active attacks. Moreover, by slightly restricting the power of the active adversary, we derive an equivalence between these notions. This forms a surprising, and quite unique, connection between a non-interactive passively-private primitive to an interactive actively-private primitive.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_16"}, {"primary_key": "1637575", "vector": [], "sparse_vector": [], "title": "Verifiable Relation Sharing and Multi-verifier Zero-Knowledge in Two Rounds: Trading NIZKs with Honest Majority - (Extended Abstract).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce the problem ofVerifiable Relation Sharing(VRS) where a client (prover) wishes to share a vector of secret data items amongkservers (the verifiers) while proving in zero-knowledge that the shared data satisfies some properties. This combined task of sharing and proving generalizes notions like verifiable secret sharing and zero-knowledge proofs over secret-shared data. We study VRS from a theoretical perspective and focus on its round complexity. As our main contribution, we show that every efficiently-computable relation can be realized by a VRS with an optimal round complexity of two rounds where the first round is input-independent (offline round). The protocol achieves full UC-security against an active adversary that is allowed to corrupt anyt-subset of the parties that may include the client together with some of the verifiers. For a small (logarithmic) number of parties, we achieve an optimal resiliency threshold of\\(t<0.5(k+1)\\), and for a large (polynomial) number of parties, we achieve an almost-optimal resiliency threshold of\\(t<0.5(k+1)(1-\\epsilon )\\)for an arbitrarily small constant\\(\\epsilon >0\\). Both protocols can be based on sub-exponentially hard injective one-way functions. If the parties have an access to a collision resistance hash function, we can derivestatistical everlasting security, i.e., the protocols are secure against adversaries that are computationally bounded during the protocol execution and become computationally unbounded after the protocol execution. Previous 2-round solutions achieve smaller resiliency thresholds and weaker security notions regardless of the underlying assumptions. As a special case, our protocols give rise to 2-round offline/online constructions of multi-verifier zero-knowledge proofs (MVZK). Such constructions were previously obtained under the same type of assumptions that are needed for NIZK, i.e., public-key assumptions or random-oracle type assumptions (Abe et al., Asiacrypt 2002; Groth and Ostrovsky, Crypto 2007; Boneh et al., Crypto 2019; Yang, and Wang, Eprint 2022). Our work shows, for the first time, that in the presence of an honest majority these assumptions can be replaced with more conservative “Minicrypt”-type assumptions like injective one-way functions and collision-resistance hash functions. Indeed, our MVZK protocols provide a round-efficient substitute for NIZK in settings where honest-majority is present. Additional applications are also presented.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_2"}, {"primary_key": "1637576", "vector": [], "sparse_vector": [], "title": "Parallel Repetition of (k1, đots , kμ )-Special-Sound Multi-round Interactive Proofs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In many occasions, the knowledge error\\(\\kappa \\)of an interactive proof is not small enough, and thus needs to be reduced. This can be done generically by repeating the interactive proof in parallel. While there have been many works studying the effect of parallel repetition on thesoundness errorof interactive proofs and arguments, the effect of parallel repetition on theknowledge errorhas largely remained unstudied. Only recently it was shown that thet-fold parallel repetition ofanyinteractive protocol reduces the knowledge error from\\(\\kappa \\)down to\\(\\kappa ^t +\\nu \\)for any non-negligible term\\(\\nu \\). This generic result is suboptimal in that it does not give the knowledge error\\(\\kappa ^t\\)that one would expect for typical protocols, and, worse, the knowledge error remains non-negligible. In this work we show that indeed thet-fold parallel repetition of any\\((k_1,\\dots ,k_{\\mu })\\)-special-sound multi-round public-coin interactive proof optimally reduces the knowledge error from\\(\\kappa \\)down to\\(\\kappa ^t\\). At the core of our results is an alternative, in some sense more fine-grained, measure of quality of a dishonest prover than its success probability, for which we show that it characterizes when knowledge extraction is possible. This new measure then turns out to be very convenient when it comes to analyzing the parallel repetition of such interactive proofs. While parallel repetition reduces the knowledge error, it is easily seen toincreasethecompleteness error. For this reason, we generalize our result to the case ofs-out-of-tthreshold parallel repetition, where the verifier accepts ifsout oftof the parallel instances are accepting. An appropriately chosen thresholdsallows both the knowledge error and completeness error to be reduced simultaneously.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_15"}, {"primary_key": "1637577", "vector": [], "sparse_vector": [], "title": "On the Impossibility of Key Agreements from Quantum Random Oracles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the following question, first publicly posed by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 2018. Can parties\\(\\textsf{A},\\textsf{B}\\)with quantum computing power and classical communication rely only on a random oracle (that can be queried in quantum superposition) to agree on a key that is private from eavesdroppers? We make the first progress on the question above and prove the following. When onlyoneof the parties\\(\\textsf{A}\\)is classical and the other party\\(\\textsf{B}\\)is quantum powered, as long as they ask a total ofdoracle queries and agree on a key with probability 1, then there is always a way to break the key agreement by asking\\(O(d^2)\\)number ofclassicaloracle queries. When both parties can make quantum queries to the random oracle, we introduce a natural conjecture, which if true would imply attacks with\\({\\text {poly}}(d)\\)classicalqueries to the random oracle. Our conjecture, roughly speaking, states that the multiplication of any two degree-dreal-valued polynomials over the Boolean hypercube of influence at most\\(\\delta =1/{\\text {poly}}(d)\\)is nonzero. We then prove our conjecture for exponentially small influences, which leads to an (unconditional) classical\\(2^{O(md)}\\)-query attack on any such key agreement protocol, wheremis the oracle’s output length. Since our attacks are classical, we then ask whether it is always possible to find classical attacks on key agreements with imperfect completeness in the quantum random oracle model. We prove a barrier for this approach, by showing that if the folklore “Simulation Conjecture” (first formally stated by <PERSON><PERSON> and <PERSON>bainis in 2009) about the possibility of simulating efficient-query quantum algorithms using efficient-query classical algorithms is false, then there is in fact such a secure key agreement in the quantum random oracle model that cannot be broken classically.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_6"}, {"primary_key": "1637578", "vector": [], "sparse_vector": [], "title": "Improving Support-Minors Rank Attacks: Applications to Gđisplaystyle eMSS and Rainbow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>"], "summary": "The Support-Minors (SM) method has opened new routes to attack multivariate schemes with rank properties that were previously impossible to exploit, as shown by the recent attacks of [9,40] on the Round 3 NIST candidates G\\(\\displaystyle e\\)MSS and Rainbow respectively. In this paper, we study this SM approach more in depth and we propose a greatly improved attack on G\\(\\displaystyle e\\)MSS based on this Support-Minors method. Even though G\\(\\displaystyle e\\)MSS was already affected by [40], our attack affects it even more and makes it completely unfeasible to repair the scheme by simply increasing the size of its parameters or even applying the recent projection technique from [36] whose purpose was to make G\\(\\displaystyle e\\)MSS immune to [40]. For instance, our attack on the G\\(\\displaystyle e\\)MSS128 parameter set has estimated time complexity\\(\\displaystyle 2^{72}\\), and repairing the scheme by applying [36] would result in a signature with slower signing time by an impractical factor of\\(\\displaystyle 2^{14}\\). Another contribution is to suggest optimizations that can reduce memory access costs for an XL strategy on a large SM system using the Block-<PERSON>iedemann algorithm as subroutine when these costs are a concern. In a memory cost model based on [7], we show that the rectangular MinRank attack from [9] may indeed reduce the security for all Round 3 Rainbow parameter sets below their targeted security strengths, contradicting the lower bound claimed by [41] using the same memory cost model.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_13"}, {"primary_key": "1637579", "vector": [], "sparse_vector": [], "title": "(Nondeterministic) Hardness vs. Non-malleability.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON>"], "summary": "We present the first truly explicit constructions ofnon-malleable codesagainst tampering by bounded polynomial size circuits. These objects imply unproven circuit lower bounds and our construction is secure provided\\(\\textsf{E}\\)requires exponential size nondeterministic circuits, an assumption from the derandomization literature. Prior works on NMC for polysize circuits, either required an untamperable CRS [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ITCS’14; <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Wichs EUROCRYPT’14] or very strong cryptographic assumptions [<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> EUROCRYPT’18; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Pass CRYPTO’21]. Both of works in the latter category only achieve non-malleability with respect to efficient distinguishers and, more importantly, utilize cryptographic objects for which no provably secure instantiations are known outside the random oracle model. In this sense, none of the prior yields fully explicit codes from non-heuristic assumptions. Our assumption is not known to imply the existence of one-way functions, which suggests that cryptography is unnecessary for non-malleability against this class. Technically, security is shown bynon-deterministicallyreducing polynomial size tampering to split-state tampering. The technique is general enough that it allows us to construct the firstseedless non-malleable extractors[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> T<PERSON>’14] for sources sampled by polynomial size circuits [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>’00] (resp. recognized by polynomial size circuits [Shaltiel CC’11]) and tampered by polynomial size circuits. Our construction is secure assuming\\(\\textsf{E}\\)requires exponential size\\(\\varSigma _4\\)-circuits (resp.\\(\\varSigma _3\\)-circuits), this assumption is the state-of-the-art for extracting randomness from such sources (without non-malleability). Several additional results are included in the full version of this paper [Eprint 2022/070]. First, we observe that non-malleable codes and non-malleable secret sharing [Goyal, Kumar STOC’18] are essentially equivalent with respect to polynomial size tampering. In more detail, assuming\\(\\textsf{E}\\)is hard for exponential size nondeterministic circuits, any efficient secret sharing scheme can be made non-malleable against polynomial size circuit tampering. Second, we observe that the fact that our constructions only achieve inverse polynomial (statistical) security is inherent. Extending a result from [Applebaum, Artemenko, Shaltiel, Yang CC’16] we show it is impossible to do better using black-box reductions. However, we extend the notion of relative error from [Applebaum, Artemenko, Shaltiel, Yang CC’16] to non-malleable extractors and show that they can be constructed from similar assumptions. Third, we observe that relative-error non-malleable extractors can be utilized to render a broad class of cryptographic primitives tamper and leakage resilient, while preserving negligible security guarantees.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_6"}, {"primary_key": "1637580", "vector": [], "sparse_vector": [], "title": "Superposition Meet-in-the-Middle Attacks: Updates on Fundamental Security of AES-like Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yi Tu"], "summary": "The Meet-in-the-Middle approach is one of the most powerful cryptanalysis techniques, demonstrated by its applications in preimage attacks on the fullMD4,MD5,Tiger,HAVAL, andHaraka-512 v2hash functions, and key recovery of the full block cipherKTANTAN. The success relies on the separation of a primitive into two independent chunks, where each active cell of the state is used to represent only one chunk or is otherwise considered unusable once mixed. We observe that some of such cells are linearly mixed and can be as useful as the independent ones. This leads to the introduction of superposition states and a whole suite of accompanied techniques, which we incorporate into the MILP-based search framework proposed by <PERSON><PERSON><PERSON> al.at EUROCRYPT 2021 and <PERSON>et al.at CRYPTO 2021, and find applications on a wide range ofAES-like hash functions and block ciphers.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_3"}, {"primary_key": "1637581", "vector": [], "sparse_vector": [], "title": "Succinct Classical Verification of Quantum Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct a classically verifiablesuccinctinteractive argument for quantum computation (BQP) with communication complexity and verifier runtime that are poly-logarithmic in the runtime of the BQP computation (and polynomial in the security parameter). Our protocol is secure assuming the post-quantum security of indistinguishability obfuscation (iO) and Learning with Errors (LWE). This is the first succinct argument for quantum computationin the plain model; prior work (<PERSON><PERSON><PERSON>, TCC ’20) requires both a long common reference string and non-black-box use of a hash function modeled as a random oracle. At a technical level, we revisit the framework for constructing classically verifiable quantum computation (<PERSON><PERSON><PERSON>, FOCS ’18). We give a self-contained, modular proof of security for <PERSON><PERSON><PERSON>’s protocol, which we believe is of independent interest. Our proof readily generalizes to a setting in which the verifier’s first message (which consists of many public keys) iscompressed. Next, we formalize this notion of compressed public keys; we view the object as a generalization of constrained/programmable PRFs and instantiate it based on indistinguishability obfuscation. Finally, we compile the above protocol into a fully succinct argument using a (sufficiently composable) succinct argument of knowledge for NP. Using our framework, we achieve several additional results, including Succinct arguments for QMA (given multiple copies of the witness), Succinctnon-interactivearguments for BQP (or QMA) in the quantum random oracle model, and Succinct batch arguments for BQP (or QMA) assuming post-quantum LWE (without iO).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_7"}, {"primary_key": "1637582", "vector": [], "sparse_vector": [], "title": "Moz$\\mathbb {Z}_{2k}$arella: Efficient Vector-OLE and Zero-Knowledge Proofs over $\\mathbb {Z}_{2k}$.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero-knowledge proof systems are usually designed to support computations for circuits over\\(\\mathbb {F}_2\\)or\\(\\mathbb {F}_p\\)for largep, but not for computations over\\(\\mathbb {Z}_{2^k}\\), which all modern CPUs operate on. Although\\(\\mathbb {Z}_{2^k}\\)-arithmetic can be emulated using prime moduli, this comes with an unavoidable overhead. Recently, <PERSON><PERSON> et al. (CCS 2021) suggested a candidate construction for a designated-verifier zero-knowledge proof system that natively runs over\\(\\mathbb {Z}_{2^k}\\). Unfortunately, their construction requires preprocessed random vector oblivious linear evaluation (VOLE) to be instantiated over\\(\\mathbb {Z}_{2^k}\\). Currently, it is not known how to efficiently generate such random VOLE in large quantities. In this work, we present a maliciously secure, VOLE extension protocol that can turn a short seed-VOLE over\\(\\mathbb {Z}_{2^k}\\)into a much longer, pseudorandom VOLE over the same ring. Our construction borrows ideas from recent protocols over finite fields, which we non-trivially adapt to work over\\(\\mathbb {Z}_{2^k}\\). Moreover, we show that the approach taken by the QuickSilver zero-knowledge proof system (<PERSON> et al. CCS 2021) can be generalized to support computations over\\(\\mathbb {Z}_{2^k}\\). This new VOLE-based proof system, which we callQuarkSilver, yields better efficiency than the previous zero-knowledge protocols suggested by Baum et al. Furthermore, we implement both our VOLE extension and our zero-knowledge proof system, and show that they can generate 13–50 million VOLEs per second for\\({64}\\,{\\textrm{bit}}\\)to\\({256}\\,{\\textrm{bit}}\\)rings, and evaluate\\({1.3}\\,\\textrm{million}\\)\\({64}\\,{\\textrm{bit}}\\)multiplications per second in zero-knowledge.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_12"}, {"primary_key": "1637583", "vector": [], "sparse_vector": [], "title": "Constructing and Deconstructing Intentional Weaknesses in Symmetric Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deliberately weakened ciphers are of great interest in political discussion on law enforcement, as in the constantly recurring crypto wars, and have been put in the spotlight of academics by recent progress. A paper at Eurocrypt 2021 showed a strong indication that the security of the widely-deployed stream cipherGEA-1was deliberately and secretly weakened to 40 bits in order to fulfill European export restrictions that have been in place in the late 1990s. However, no explanation of how this could have been constructed was given. On the other hand, we have seen theMALICIOUSdesign framework, published at CRYPTO 2020, that allows to construct tweakable block ciphers with a backdoor, where the difficulty of recovering the backdoor relies on well-understood cryptographic assumptions. The constructed tweakable block cipher however is rather unusual and very different from, say, general-purpose ciphers like the AES. In this paper, we pick up both topics. ForGEA-1we thoroughly explain how the weakness was constructed, solving the main open question of the work mentioned above. By generalizingMALICIOUSwe – for the first time – construct backdoored tweakable block ciphers that follow modern design principles for general-purpose block ciphers, i.e., more natural-looking deliberately weakened tweakable block ciphers.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_25"}, {"primary_key": "1637584", "vector": [], "sparse_vector": [], "title": "Better than Advertised Security for Non-interactive Threshold Signatures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a unified syntax, and a hierarchy of definitions of security of increasing strength, for non-interactive threshold signature schemes. These are schemes having a single-round signing protocol, possibly with one prior round of message-independent pre-processing. We fit FROST1 and BLS, which are leading practical schemes, into our hierarchy, in particular showing they meet stronger security definitions than they have been shown to meet so far. We also fit in our hierarchy a more efficient version FROST2 of FROST1 that we give. These definitions and results, for simplicity, all assume trusted key generation. Finally, we prove the security of FROST2 with key generation performed by an efficient distributed key generation protocol.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_18"}, {"primary_key": "1637585", "vector": [], "sparse_vector": [], "title": "Breaking Rainbow Takes a Weekend on a Laptop.", "authors": ["<PERSON>"], "summary": "This work introduces new key recovery attacks against the Rainbow signature scheme, which is one of the three finalist signature schemes still in the NIST Post-Quantum Cryptography standardization project. The new attacks outperform previously known attacks for all the parameter sets submitted to NIST and make a key-recovery practical for the SL 1 parameters. Concretely, given a Rainbow public key for the SL 1 parameters of the second-round submission, our attack returns the corresponding secret key after on average 53 h (one weekend) of computation time on a standard laptop.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_16"}, {"primary_key": "1637586", "vector": [], "sparse_vector": [], "title": "Provably Secure Reflection Ciphers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper provides the first analysis of reflection ciphers such as<PERSON><PERSON><PERSON><PERSON><PERSON> a provable security viewpoint. As a first contribution, we initiate the study of key-alternating reflection ciphers in the ideal permutation model. Specifically, we prove the security of the two-round case and give matching attacks. The resulting security bound takes form\\(\\mathcal {O}(qp^2/2^{2n}+q^2/2^n)\\), where\\(q\\)is the number of construction evaluations and\\(p\\)is the number of direct adversarial queries to the underlying permutation. Since the two-round construction already achieves an interesting security lower bound, this result can also be of interest for the construction of reflection ciphers based on a single public permutation. Our second contribution is a generic key-length extension method for reflection ciphers. It provides an attractive alternative to theFXconstruction, which is used byPrinceand other concrete key-alternating reflection ciphers. We show that our construction leads to better security with minimal changes to existing designs. The security proof is in the ideal cipher model and relies on a reduction to the two-round Even-Mansour cipher with a single round key. In order to obtain the desired result, we sharpen the bad-transcript analysis and consequently improve the best-known bounds for the single-key Even-Mansour cipher with two rounds. This improvement is enabled by a new sum-capture theorem that is of independent interest.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_9"}, {"primary_key": "1637587", "vector": [], "sparse_vector": [], "title": "Differential Cryptanalysis in the Fixed-Key Model.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A systematic approach to the fixed-key analysis of differential probabilities is proposed. It is based on the propagation of ‘quasidifferential trails’, which keep track of probabilistic linear relations on the values satisfying a differential characteristic in a theoretically sound way. It is shown that the fixed-key probability of a differential can be expressed as the sum of the correlations of its quasidifferential trails. The theoretical foundations of the method are based on an extension of the difference-distribution table, which we call the quasidifferential transition matrix. The role of these matrices is analogous to that of correlation matrices in linear cryptanalysis. This puts the theory of differential and linear cryptanalysis on an equal footing. The practical applicability of the proposed methodology is demonstrated by analyzing several differentials forRECTANGLE,KNOT,SpeckandSimon. The analysis is automated and applicable to other SPN and ARX designs. Several attacks are shown to be invalid, most others turn out to work only for some keys but can be improved for weak-keys.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_23"}, {"primary_key": "1637588", "vector": [], "sparse_vector": [], "title": "A More Complete Analysis of the Signal Double Ratchet Algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Seminal works by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and Steb<PERSON> [EuroS &P 2017] and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> [EUROCRYPT 2019] provided the first formal frameworks for studying the widely-used Signal Double Ratchet (\\(\\textsf{DR}\\)for short) algorithm. In this work, we develop a new Universally Composable (UC) definition\\(\\mathcal {F}_{\\textsf{DR}}\\)that we show is provably achieved by the\\(\\textsf{DR}\\)protocol. Our definition captures not only the security and correctness guarantees of the\\(\\textsf{DR}\\)already identified in the prior state-of-the-art analyses of <PERSON><PERSON><PERSON><PERSON><PERSON> al.and <PERSON><PERSON><PERSON> al., but alsomoreguarantees that are absent from one orbothof these works. In particular, we constructsixdifferent modified versions of the\\(\\textsf{DR}\\)protocol, all of which are insecure according to our definition\\(\\mathcal {F}_{\\textsf{DR}}\\), but remain secure according to one (or both) of their definitions. For example, our definition is the first to fully capture CCA-style attacks possible immediately after a compromise—attacks that, as we show, the\\(\\textsf{DR}\\)protocol provably resists, but were not fully captured by prior definitions. We additionally show that multiple compromises of a party in a short time interval, which the\\(\\textsf{DR}\\)is expected to be able to withstand, as we understand from its whitepaper, nonetheless introduce a new non-trivial (albeit minor) weakness of the\\(\\textsf{DR}\\). Since the definitions in the literature (including our\\(\\mathcal {F}_{\\textsf{DR}}\\)above) do not capture security against this more nuanced scenario, we define a new stronger definition\\(\\mathcal {F}_{\\textsf{TR}}\\)that does. Finally, we provide aminimalistic modificationto the\\(\\textsf{DR}\\)(that we call the\\(\\text {Triple Ratchet}\\), or\\(\\textsf{TR}\\)for short) and show that the resulting protocol securely realizes the stronger functionality\\(\\mathcal {F}_{\\textsf{TR}}\\). Remarkably, the modification incurs no additional communication cost and virtually no additional computational cost. We also show that these techniques can be used to improve communication costs in other scenarios, e.g. practical Updatable Public Key Encryption schemes and the re-randomized TreeKEM protocol of Alwenet al.[CRYPTO 2020] for Secure Group Messaging.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_27"}, {"primary_key": "1637589", "vector": [], "sparse_vector": [], "title": "Constructive Post-Quantum Reductions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Is it possible to convert classical reductions into post-quantum ones? It is customary to argue that while this is problematic in the interactive setting, non-interactive reductions do carry over. However, when considering quantum auxiliary input, this conversion results in anon-constructivepost-quantum reduction that requires duplicating the quantum auxiliary input, which is in general inefficient or even impossible. This violates the win-win premise of provable cryptography: an attack against a cryptographic primitive should lead to an algorithmic advantage. We initiate the study of constructive quantum reductions and present positive and negative results for converting large classes of classical reductions to the post-quantum setting in a constructive manner. We show that any non-interactive non-adaptive reduction from assumptions with a polynomial solution space (such as decision assumptions) can be made post-quantum constructive. In contrast, assumptions with super-polynomial solution space (such as general search assumptions) cannot be generally converted. Along the way, we make several additional contributions: We put forth a framework for reductions (or general interaction) withstatefulsolvers for a computational problem, that may change their internal state between consecutive calls. We show that such solvers can still be utilized. This framework and our results are meaningful even in the classical setting. A consequence of our negative result is that quantum auxiliary input that is useful against a problem with a super-polynomial solution space cannot be generically “restored” post-measurement. This shows that the novel rewinding technique of <PERSON><PERSON><PERSON> et al. (FOCS 2021) is tight in the sense that it cannot be extended beyond a polynomial measurement space.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_22"}, {"primary_key": "1637590", "vector": [], "sparse_vector": [], "title": "Statistically Sender-Private OT from LPN and Derandomization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct a two-message oblivious transfer protocol with statistical sender privacy (SSP OT) based on the Learning Parity with Noise (LPN) Assumption and a standard Nisan-Wigderson style derandomization assumption. Beyond being of interest on their own, SSP OT protocols have proven to be a powerful tool toward minimizing the round complexity in a wide array of cryptographic applications from proofs systems, through secure computation protocols, to hard problems in statistical zero knowledge (SZK). The protocol is plausibly post-quantum secure. The only other constructions with plausible post quantum security are based on the Learning with Errors (LWE) Assumption. Lacking the geometric structure of LWE, our construction and analysis rely on a different set of techniques. Technically, we first construct an SSP OT protocol in the common random string model from LPN alone, and then derandomize the common random string. Most of the technical difficulty lies in the first step. Here we prove a robustness property of the inner product randomness extractor to a certain type of linear splitting attacks. A caveat of our construction is that it relies on the so calledlow noise regimeof LPN. This aligns with our current complexity-theoretic understanding of LPN, which only in the low noise regime is known to imply hardness in SZK.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_21"}, {"primary_key": "1637591", "vector": [], "sparse_vector": [], "title": "Sustained Space and Cumulative Complexity Trade-Offs for Data-Dependent Memory-Hard Functions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Memory-hard functions (MHFs) are a useful cryptographic primitive which can be used to design egalitarian proof of work puzzles and to protect low entropy secrets like passwords against brute-force attackers. Intuitively, a memory-hard function is a function whose evaluation costs are dominated by memory costs even if the attacker uses specialized hardware (FPGAs/ASICs), and several cost metrics have been proposed to quantify this intuition. For example, space-time cost looks at the product of running time and the maximum space usage over the entire execution of an algorithm. <PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2015) observed that the space-time cost of evaluating a function multiple times may not scale linearly in the number of instances being evaluated and introduced the stricter requirement that a memory-hard function has high cumulative memory complexity (CMC) to ensure that an attacker’s amortized space-time costs remain large even if the attacker evaluates the function on multiple different inputs in parallel. <PERSON><PERSON> et al. (EUROCRYPT 2018) observed that the notion of CMC still gives the attacker undesirable flexibility in selecting space-time tradeoffs e.g., while the MHF\\(\\texttt{Scrypt}\\)has maximal CMC\\(\\varOmega (N^2)\\), an attacker could evaluate the function with constantO(1) memory in time\\(O(N^2)\\). <PERSON><PERSON> et al. introduced an even stricter notion of Sustained Space complexity and designed an MHF which has\\(s=\\varOmega (N/\\log N)\\)sustained complexity\\(t=\\varOmega (N)\\)i.e., any algorithm evaluating the function in the parallel random oracle model must have at least\\(t=\\varOmega (N)\\)steps where the memory usage is at least\\(\\varOmega (N/\\log N)\\). In this work, we use dynamic pebbling games and dynamic graphs to explore tradeoffs between sustained space complexity and cumulative memory complexity for data-dependent memory-hard functions such as Argon2id and\\(\\texttt{Scrypt}\\). We design our own dynamic graph (dMHF) with the property thatanydynamic pebbling strategy either (1) has\\(\\varOmega (N)\\)rounds with\\(\\varOmega (N)\\)space, or (2) has CMC\\(\\varOmega (N^{3-\\epsilon })\\)—substantially larger than\\(N^2\\). For Argon2id we show thatanydynamic pebbling strategy either(1) has\\(\\varOmega (N)\\)rounds with\\(\\varOmega (N^{1-\\epsilon })\\)space, or (2) has CMC\\(\\omega (N^2)\\). We also present a dynamic version of DRSample (Alwen et al. 2017) for whichanydynamic pebbling strategy either (1) has\\(\\varOmega (N)\\)rounds with\\(\\varOmega (N/\\log N)\\)space, or (2) has CMC\\(\\varOmega (N^3/\\log N)\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_8"}, {"primary_key": "1637592", "vector": [], "sparse_vector": [], "title": "On Codes and Learning with Errors over Function Fields.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is a long standing open problem to find search to decision reductions for structured versions of the decoding problem of linear codes. Such results in the lattice-based setting have been carried out using number fields: Polynomial–LWE, Ring–\\(\\textsf{LWE}\\), Module–\\(\\textsf{LWE}\\)and so on. We propose a function field version of the\\(\\textsf{LWE}\\)problem. This new framework leads to another point of view on structured codes,e.g.quasi-cyclic codes, strengthening the connection between lattice-based and code-based cryptography. In particular, we obtain the first search to decision reduction for structured codes. Following the historical constructions in lattice–based cryptography, we instantiate our construction with function fields analogues of cyclotomic fields, namelyCarlitzextensions, leading to search to decision reductions on various versions of Ring-\\(\\textsf{LPN}\\), which have applications to secure multiparty computation and to an authentication protocol.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_18"}, {"primary_key": "1637593", "vector": [], "sparse_vector": [], "title": "Threshold Signatures with Private Accountability.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Existing threshold signature schemes come in two flavors: (i)fully private, where the signature reveals nothing about the set of signers that generated the signature, and (ii)accountable, where the signature completely identifies the set of signers. In this paper we propose a new type of threshold signature, called TAPS, that is a hybrid of privacy and accountability. A TAPS signature is fully private from the public’s point of view. However, an entity that has a secret tracing key can trace a signature to the threshold of signers that generated it. A TAPS makes it possible for an organization to keep its inner workings private, while ensuring that signers are accountable for their actions. We construct a number of TAPS schemes. First, we present a generic construction that builds a TAPS from any accountable threshold signature. This generic construction is not efficient, and we next focus on efficient schemes based on standard assumptions. We build two efficient TAPS schemes (in the random oracle model) based on the Schnorr signature scheme. We conclude with a number of open problems relating to efficient TAPS.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_19"}, {"primary_key": "1637594", "vector": [], "sparse_vector": [], "title": "MuSig-L: Lattice-Based Multi-signature with Single-Round Online Phase.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-signatures are protocols that allow a group of signers to jointly produce a single signature on the same message. In recent years, a number of practical multi-signature schemes have been proposed in the discrete-log setting, such asMuSig2(CRYPTO’21) andDWMS(CRYPTO’21). The main technical challenge in constructing a multi-signature scheme is to achieve a set of several desirable properties, such as (1) security in the plain public-key (PPK) model, (2) concurrent security, (3) low online round complexity, and (4) key aggregation. However, previous lattice-based, post-quantum counterparts to Schnorr multi-signatures fail to satisfy these properties. In this paper, we introduceMuSig-L, a lattice-based multi-signature scheme simultaneously achieving these design goals for the first time. Unlike the recent, round-efficient proposal of <PERSON><PERSON><PERSON><PERSON> et al. (PKC’21), which had to rely on lattice-based trapdoor commitments, we do not require any additional primitive in the protocol, while being able to prove security from the standard module-SIS and LWE assumptions. The resulting output signature of our scheme therefore looks closer to the usual Fiat–Shamir-with-abort signatures.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_10"}, {"primary_key": "1637595", "vector": [], "sparse_vector": [], "title": "Some Easy Instances of Ideal-SVP and Implications on the Partial Vandermonde Knapsack Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "In this article, we generalize the works of <PERSON> et al. (Eurocrypt’21) and <PERSON> et al. (ArXiv’21) and provide asimplecondition under which an ideal lattice defines an easy instance of the shortest vector problem. Namely, we show that the more automorphisms stabilize the ideal, the easier it is to find a short vector in it. This observation was already made for prime ideals in Galois fields, and we generalize it toanyideal (whose prime factors are not ramified) ofanynumber field. We then provide a cryptographic application of this result by showing that particular instances of the partial Vandermonde knapsack problem, also known as partial Fourier recovery problem, can be solved classically in polynomial time. As a proof of concept, we implemented our attack and managed to solve those particular instances for concrete parameter settings proposed in the literature. For random instances, we can halve the lattice dimension with non-negligible probability.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_17"}, {"primary_key": "1637596", "vector": [], "sparse_vector": [], "title": "Correlated Pseudorandomness from Expand-Accumulate Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A pseudorandom correlation generator (PCG) is a recent tool for securely generating useful sources of correlated randomness, such as random oblivious transfers (OT) and vector oblivious linear evaluations (VOLE), with low communication cost. We introduce a simple new design for PCGs based on so-calledexpand-accumulatecodes, which first apply a sparse random expander graph to replicate each message entry, and then accumulate the entries by computing the sum of each prefix. Our design offers the following advantages compared to state-of-the-art PCG constructions: Competitive concrete efficiency backed by provable security against relevant classes of attacks; An offline-online mode that combines near-optimal cache-friendliness with simple parallelization; Concretely efficient extensions to pseudorandom correlationfunctions, which enable incremental generation of new correlation instances on demand, and to new kinds of correlated randomness that include circuit-dependent correlations. To further improve the concrete computational cost, we propose a method for speeding up a full-domain evaluation of a puncturable pseudorandom function (PPRF). This is independently motivated by other cryptographic applications of PPRFs.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_21"}, {"primary_key": "1637597", "vector": [], "sparse_vector": [], "title": "Programmable Distributed Point Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Adistributed point function(DPF) is a cryptographic primitive that enables compressed additive sharing of a secret unit vector across two or more parties. Despite growing ubiquity within applications and notable research efforts, the best 2-party DPF construction to date remains the tree-based construction from (<PERSON> et al., CCS’16), with no significantly new approaches since. We present a new framework for 2-party DPF construction, which applies in the setting of feasible (polynomial-size) domains. This captures in particular all DPF applications in which the keys are expanded to the full domain. Our approach is motivated by a strengthened notion we put forth, ofprogrammableDPF (PDPF): in which a short, input-independent “offline” key can be reused for sharing many point functions. PDPF from OWF.We construct a PDPF for feasible domains from the minimal assumption that one-way functions exist, where the second “online” key size is polylogarithmic in the domain sizeN. Our approach offers multiple new efficiency features and applications: Privately puncturable PRFs.Our PDPF gives the first OWF-basedprivatelypuncturable PRFs (for feasible domains) with sublinear keys. O(1)-round distributed DPF Gen.We obtain a (standard) DPF with polylog-size keys that admits an analog of Do<PERSON><PERSON>-<PERSON><PERSON> (CCS’17) distributed key generation, requiring onlyO(1) rounds (versus\\(\\log N\\)). PCG with 1 short key.Compressing useful correlations for secure computation, where one key is of minimal size. This provides up to exponential communication savings in some application scenarios.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_5"}, {"primary_key": "1637598", "vector": [], "sparse_vector": [], "title": "Simon&apos;s Algorithm and Symmetric Crypto: Generalizations and Automatized Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we deepen our understanding of how to apply <PERSON>’s algorithm to break symmetric cryptographic primitives. On the one hand, we automate the search for new attacks. Using this approach we automatically find the first efficient key-recovery attacks against constructions like 5-round MISTY L-FK or 5-round Feistel-FK (with internal permutation) using <PERSON>’s algorithm. On the other hand, we study generalizations of <PERSON>’s algorithm using non-standard Hadamard matrices, with the aim to expand the quantum symmetric cryptanalysis toolkit with properties other than the periods. Our main conclusion here is that none of these generalizations can accomplish that, and we conclude that exploiting non-standard Hadamard matrices with quantum computers to break symmetric primitives will require fundamentally new attacks.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_26"}, {"primary_key": "1637599", "vector": [], "sparse_vector": [], "title": "Universally Composable End-to-End Secure Messaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mayank Varia"], "summary": "We model and analyze the Signal end-to-end messaging protocol within the UC framework. In particular: We formulate an ideal functionality that captures end-to-end secure messaging, in a setting with PKI and an untrusted server, against an adversary that has full control over the network and can adaptively and momentarily compromise parties at any time and obtain their entire internal states. In particular our analysis captures the forward secrecy and recovery-of-security properties of Signal and the conditions under which they break. We model the main components of the Signal architecture (PKI and long-term keys, the backbone continuous-key-exchange or “asymmetric ratchet,” epoch-level symmetric ratchets, authenticated encryption) as individual ideal functionalities that are realized and analyzed separately and then composed using the UC and Global-State UC theorems. We show how the ideal functionalities representing these components can be realized using standard cryptographic primitives under minimal hardness assumptions. Our modeling introduces additional innovations that enable arguing about the security of Signal irrespective of the underlying communication medium, as well as secure composition of dynamically generated modules that share state. These features, together with the basic modularity of the UC framework, will hopefully facilitate the use of both Signal-as-a-whole and its individual components within cryptographic applications. Two other features of our modeling are the treatment of fully adaptive corruptions, and making minimal use of random oracle abstractions. In particular, we show how to realize continuous key exchange in the plain model, while preserving security against adaptive corruptions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_1"}, {"primary_key": "1637600", "vector": [], "sparse_vector": [], "title": "The Gap Is Sensitive to Size of Preimages: Collapsing Property Doesn&apos;t Go Beyond Quantum Collision-Resistance for Preimages Bounded Hash Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As an enhancement of quantum collision-resistance, the collapsing property of hash functions proposed by <PERSON><PERSON><PERSON> (EUROCRYPT 2016) emphasizes the hardness for distinguishing a superposition state of a hash value from a collapsed one. The collapsing property trivially implies the quantum collision-resistance. However, it remains to be unknown whether there is a reduction from the collapsing hash functions to the quantum collision-resistant hash functions. In this paper, we further study the relations between these two properties and derive two intriguing results as follows: Firstly, when the size of preimages of each hash value is bounded by some polynomial, we demonstrate that the collapsing property and the collision-resistance must hold simultaneously. This result is proved via a semi-black-box manner by taking advantage of the invertibility of a unitary quantum circuit. Next, we further consider the relations between these two properties in the exponential-sized preimages case. By giving a construction of polynomial bounded hash functions, which preserves the quantum collision-resistance, we show the existence of collapsing hash functions is implied by the quantum collision-resistant hash functions when the size of preimages is not too large to the expected value. Our results indicate that the gap between these two properties is sensitive to the size of preimages. As a corollary, our results also reveal the non-existence of polynomial bounded equivocal collision-resistant hash functions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_19"}, {"primary_key": "1637601", "vector": [], "sparse_vector": [], "title": "PI-Cut-<PERSON><PERSON> and <PERSON>: Compact Blind Signatures via Parallel Instance Cut-and-Choose and More.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Blind signature schemes are one of the best-studied tools for privacy-preserving authentication. Unfortunately, known constructions of provably secure blind signatures either rely on non-standard hardness assumptions, or require parameters that grow linearly with the number of concurrently issued signatures, or involve prohibitively inefficient general techniques such as general secure two-party computation. Recently, <PERSON>, <PERSON> and <PERSON> (ASIACRYPT’21) gave a technique that, for the security parametern, transforms blind signature schemes secure for\\(O(\\log n)\\)concurrent executions of the blind signing protocol into ones that are secure for any\\(\\textsf{poly}(n)\\)concurrent executions. This transform has two drawbacks that we eliminate in this paper: 1) the communication complexity of the resulting blind signing protocol grows linearly with the number of signing interactions; 2) the resulting schemes inherit a very loose security bound from the underlying scheme and, as a result, require impractical parameter sizes. In this work, we give an improved transform for obtaining a secure blind signing protocol tolerating any\\(\\textsf{poly}(n)\\)concurrent executions from one that is secure for\\(O(\\log n)\\)concurrent executions. While preserving the advantages of the original transform, the communication complexity of our new transform only grows logarithmically with the number of interactions. Under theCDHandRSAassumptions, we improve on this generic transform in terms of concrete efficiency and give (1) a BLS-based blind signature scheme over a standard-sized group where signatures are of size roughly 3 KB and communication per signature is roughly 120 KB; and (2) an Okamoto-Guillou-Quisquater-based blind signature scheme with signatures and communication of roughly 9 KB and 8 KB, respectively.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_1"}, {"primary_key": "1637602", "vector": [], "sparse_vector": [], "title": "Short Leakage Resilient and Non-malleable Secret Sharing Schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leakage resilient secret sharing (LRSS) allows a dealer to share a secret amongstnparties such that any authorized subset of the parties can recover the secret from their shares, while an adversary that obtains shares of any unauthorized subset of parties along with bounded leakage from the other shares learns no information about the secret. Non-malleable secret sharing (NMSS) provides a guarantee that even shares that are tampered by an adversary will reconstruct to either the original message or something independent of it. The most important parameter of LRSS and NMSS schemes is the size of each share. For LRSS, in thelocal leakage model(i.e., when the leakage functions on each share are independent of each other and bounded), <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CRYPTO 2019), gave a scheme for threshold access structures with share size of approximately\\((3\\cdot \\mathsf {message \\ length} \\ + \\mu )\\), where\\(\\mu \\)is the number of bits of leakage tolerated from every share. For the case of NMSS, the best known result (again due to the above work) has share size of\\((11\\cdot \\mathsf {message \\ length})\\). In this work, we build LRSS and NMSS schemes with much improved share size. Additionally, our LRSS scheme obtains optimal share and leakage size. In particular, we get the following results: We build an information-theoretic LRSS scheme for threshold access structures with a share size of\\((\\mathsf {message \\ length} \\ + \\mu )\\). As an application of the above result, we obtain an NMSS with a share size of\\((4\\cdot \\mathsf {message \\ length})\\). Further, for the special case of sharing random messages, we obtain a share size of\\((2\\cdot \\mathsf {message \\ length})\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_7"}, {"primary_key": "1637603", "vector": [], "sparse_vector": [], "title": "Post-quantum Simulatable Extraction with Minimal Assumptions: Black-Box and Constant-Round.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "From the minimal assumption of post-quantum semi-honest oblivious transfers, we build the first\\(\\varepsilon \\)-simulatabletwo-party computation (2PC) against quantum polynomial-time (QPT) adversaries that is both constant-round and black-box (for both the construction and security reduction). A recent work by <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (FOCS’21) shows that post-quantum 2PC with standard simulation-based security is impossible in constant rounds, unless either\\({\\textbf{NP}}\\subseteq \\textbf{BQP}\\)or relying on non-black-box simulation. The\\(\\varepsilon \\)-simulatability we target is a relaxation of the standard simulation-based security that allows for an arbitrarily small noticeable simulation error\\(\\varepsilon \\). Moreover, when quantum communication is allowed, we can further weaken the assumption to post-quantum secure one-way functions (PQ-OWFs), while maintaining the constant-round and black-box property. Our techniques also yield the following set ofconstant-round and black-boxtwo-party protocols secure against QPT adversaries, only assuming black-box access to PQ-OWFs: extractable commitments for which the extractor is also an\\(\\varepsilon \\)-simulator; \\(\\varepsilon \\)-zero-knowledge commit-and-prove whose commit stage is extractable with\\(\\varepsilon \\)-simulation; \\(\\varepsilon \\)-simulatable coin-flipping; \\(\\varepsilon \\)-zero-knowledge arguments of knowledge for\\({\\textbf{NP}}\\)for which the knowledge extractor is also an\\(\\varepsilon \\)-simulator; \\(\\varepsilon \\)-zero-knowledge arguments for\\(\\textbf{QMA}\\). At the heart of the above results is a black-box extraction lemma showing how to efficiently extract secrets from QPT adversaries while disturbing their quantum states in a controllable manner, i.e., achieving\\(\\varepsilon \\)-simulatability of the after-extraction state of the adversary.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_18"}, {"primary_key": "1637604", "vector": [], "sparse_vector": [], "title": "Low Communication Complexity Protocols, Collision Resistant Hash Functions and Secret Key-Agreement Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We    study communication complexity in computational settings where bad inputs may exist, but they should be hard to find for any computationally bounded adversary. We define a model where there is a source of public randomness but the inputs are chosen by a computationally bounded adversarial participantafter seeing the public randomness. We show that breaking the known communication lower bounds of the private coins model in this setting is closely connected to known cryptographic assumptions. We consider the simultaneous messages model and the interactive communication model and show that for any non trivial predicate (with no redundant rows, such as equality): Breaking the\\( \\varOmega (\\sqrt{n}) \\)bound in the simultaneous message case or the\\( \\varOmega (\\log n) \\)bound in the interactive communication case, implies the existence of distributional collision-resistant hash functions (dCRH). This is shown using techniques from <PERSON><PERSON> and <PERSON><PERSON> [BK97]. Note that with a CRH the lower bounds can be broken. There are no protocols of constant communication in this preset randomness settings (unlike the plain public randomness model). The other model we study is that of a stateful “free talk”, where participants can communicate freelybeforethe inputs are chosen and may maintain a state, and the communication complexity is measured only afterwards. We show that efficient protocols for equality in this model imply secret key-agreement protocols in a constructive manner. On the other hand, secret key-agreement protocols imply optimal (in terms of error) protocols for equality.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_9"}, {"primary_key": "1637605", "vector": [], "sparse_vector": [], "title": "CHIP and CRISP: Protecting All Parties Against Compromise Through Identity-Binding PAKEs.", "authors": ["Cas Cremers", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in password-based authenticated key exchange (PAKE) protocols can offer stronger security guarantees for globally deployed security protocols. Notably, the OPAQUE protocol [Eurocrypt2018] realizes Strong Asymmetric PAKE (saPAKE), strengthening the protection offered by aPAKE to compromised servers: after compromising an saPAKE server, the adversary still has to perform a full brute-force search to recover any passwords or impersonate users. However, (s)aPAKEs do not protect client storage, and can only be applied in the so-calledasymmetricsetting, in which some parties, such as servers, do not communicate with each other using the protocol. Nonetheless, passwords are also widely used insymmetricsettings, where a group of parties share a password and can all communicate (e.g., Wi-Fi with client devices, routers, and mesh nodes; or industrial IoT scenarios). In these settings, the (s)aPAKE techniques cannot be applied, and the state-of-the-art still involves handling plaintext passwords. In this work, we propose the notions of(strong) identity-binding PAKEsthat improve this situation: they protect against compromise ofanyparty, and can also be applied in the symmetric setting. We propose counterparts to state-of-the-art security notions from the asymmetric setting in the UC model, and construct protocols that provably realize them. Our constructions bind the local storage of all parties to abstract identities, building on ideas from identity-based key exchange, but without requiring a third party. Our first protocol, CHIP, generalizes the security of aPAKE protocols to all parties, forcing the adversary to perform a brute-force search to recover passwords or impersonate others. Our second protocol, CRISP, additionally renders any adversarial pre-computation useless, thereby offering saPAKE-like guarantees for all parties, instead of only the server. We evaluate prototype implementations of our protocols and show that even though they offer stronger security for real-world use cases, their performance is in line with, or even better than, state-of-the-art protocols.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_23"}, {"primary_key": "1637606", "vector": [], "sparse_vector": [], "title": "Overloading the Nonce: Rugged PRPs, Nonce-Set AEAD, and Order-Resilient Channels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new security notion that lies right in between pseudorandom permutations (PRPs) and strong pseudorandom permutations (SPRPs). We call this new security notion and any (tweakable) cipher that satisfies it arugged pseudorandom permutation(RPRP). Rugged pseudorandom permutations lend themselves to some interesting applications, have practical benefits, and lead to novel cryptographic constructions. Our focus is on variable-length tweakable RPRPs, and analogous to the encode-then-encipher paradigm of Bellare and Rogaway, we can generically transform any such cipher into different AEAD schemes with varying security properties. However, the benefit of RPRPs is that they can be constructed more efficiently as they are weaker primitives than SPRPs (the notion traditionally required by the encode-then-encipher paradigm). We can construct RPRPs using only two layers of processing, whereas SPRPs typically require three layers of processing over the input data. We also identify a new transformation that yields RUP-secure AEAD schemes with more compact ciphertexts than previously known. Further extending this approach, we arrive at a new generalized notion of authenticated encryption and a matching construction, which we refer to asnonce-set AEAD. Nonce-set AEAD is particularly well-suited in the context of secure channels, like QUIC and DTLS, that operate over unreliable transports and employ a window mechanism at the receiver’s end of the channel. We conclude by presenting a generic construction for transforming a nonce-set AEAD scheme into an order-resilient secure channel. Our channel construction sheds new light on order-resilient channels and additionally leads to more compact ciphertexts when instantiated from RPRPs.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_10"}, {"primary_key": "1637607", "vector": [], "sparse_vector": [], "title": "Authenticated Garbling from Simple Correlations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the problem of constant-round malicious secure two-party computation by considering the use ofsimple correlations, namely sources of correlated randomness that can be securely generated with sublinear communication complexity and good concrete efficiency. The current state-of-the-art protocol of <PERSON> et al. (Crypto 2018) achieves malicious security by realizing a variant of theauthenticated garblingfunctionality of <PERSON> et al. (CCS 2017). Given oblivious transfer correlations, the communication cost of this protocol (with 40 bits of statistical security) is comparable to roughly 10 garbled circuits (GCs). This protocol inherently requires more than 2 rounds of interaction. In this work, we use other kinds of simple correlations to realize the authenticated garbling functionality with better efficiency. Concretely, we get the following reduced costs in the random oracle model: Using variants of both vector oblivious linear evaluation (VOLE) and multiplication triples (MT), we reduce the cost to 1.31 GCs. Using only variants of VOLE, we reduce the cost to 2.25 GCs. Using only variants of MT, we obtain anon-interactive(i.e., 2-message) protocol with cost comparable to 8 GCs. Finally, we show that by using recent constructions of pseudorandom correlation generators (<PERSON> et al., CCS 2018, Crypto 2019, 2020), the simple correlations consumed by our protocols can be securely realized without forming an efficiency bottleneck.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_3"}, {"primary_key": "1637608", "vector": [], "sparse_vector": [], "title": "Efficient NIZKs and Signatures from Commit-and-Open Protocols in the QROM.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Commit-and-open\\(\\Sigma \\)-protocols are a popular class of protocols for constructing non-interactive zero-knowledge arguments and digital-signature schemes via the Fiat-Shamir transformation . Instantiated with hash-based commitments, the resulting non-interactive schemes enjoy tight online-extractability in the random oracle model. Online extractability improves the tightness of security proofs for the resulting digital-signature schemes by avoiding lossy rewinding or forking-lemma based extraction. In this work, we prove tight online extractability in the quantum random oracle model (QROM), showing that the construction supports post-quantum security. First, we consider the default case where committing is done by element-wise hashing. In a second part, we extend our result to Merkle-tree based commitments. Our results yield a significant improvement of the provable post-quantum security of the digital-signature scheme Picnic. Our analysis makes use of a recent framework by <PERSON> et al. [CFHL21] for analysing quantum algorithms in the QROM using purely classical reasoning. Therefore, our results can to a large extent be understood and verified without prior knowledge of quantum information science.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_25"}, {"primary_key": "1637609", "vector": [], "sparse_vector": [], "title": "Triangulating Rebound Attack on AES-like Hashing.", "authors": ["<PERSON><PERSON> Dong", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rebound attack was introduced by <PERSON><PERSON><PERSON> al.at FSE 2009 to fulfill a heavy middle round of a differential path for free, utilizing the degree of freedom from states. The inbound phase was extended to 2 rounds by the Super-Sbox technique invented by <PERSON><PERSON><PERSON><PERSON>.at ASIACRYPT 2009 and <PERSON> and <PERSON> at FSE 2010. In ASIACRYPT 2010, <PERSON><PERSON>et al.further reduced the requirement of memory by introducing the non-full-active Super-Sbox. In this paper, we further develop this line of research by introducingSuper-Inbound, which is able to connect multiple 1-round or 2-round (non-full-active) Super-Sbox inbound phases by utilizing fully the degrees of freedom from both states and key, yet without the use of large memory. This essentially extends the inbound phase by up to 3 rounds. We applied this technique to find classic or quantum collisions on severalAES-like hash functions, and improved the attacked round number by 1 to 5 in targets includingAES-128 andSKINNYhashing modes,Saturnin-Hash, andGrøstl-512. To demonstrate the correctness of our attacks, the semi-free-start collision on 6-roundAES-128-MMO/MPwith estimated time complexity\\(2^{24}\\)in classical setting was implemented and an example pair was found instantly on a standard PC.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_4"}, {"primary_key": "1637610", "vector": [], "sparse_vector": [], "title": "Snapshot-Oblivious RAMs: Sub-logarithmic Efficiency for Short Transcripts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Oblivious RAM (ORAM) is a powerful technique to prevent harmful data breaches. Despite tremendous progress in improving the concrete performance of ORAM, it remains too slow for use in many practical settings; recent breakthroughs in lower bounds indicate this inefficiency is inherent for ORAM and even some natural relaxations. This work introduces snapshot-oblivious RAMs, a new secure memory access primitive. Snapshot-oblivious RAMs bypass lower bounds by providing security only for transcripts whose length (call it\\(c\\)) is fixed and known ahead of time. Intuitively, snapshot-oblivious RAMs provide strong security for attacks of short duration, such as the snapshot attacks targeted by many encrypted databases. We give an ORAM-style definition of this new primitive, and present several constructions. The underlying design principle of our constructions is to store the history of recent operations in a data structure that can be accessed obliviously. We instantiate this paradigm with data structures that remain on the client, giving a snapshot-oblivious RAM with constant bandwidth overhead. We also show how these data structures can be stored on the server and accessed using oblivious memory primitives. Our most efficient instantiation achieves\\(\\mathcal{O}(\\log c)\\)bandwidth overhead. By extending recent ORAM lower bounds, we show this performance is asymptotically optimal. Along the way, we define a newhash queuedata structure—essentially, a dictionary whose elements can be modified in a first-in-first-out fashion—which may be of independent interest.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_6"}, {"primary_key": "1637611", "vector": [], "sparse_vector": [], "title": "More Efficient Dishonest Majority Secure Computation over $\\mathbb {Z}_{2k}$ via Galois Rings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work we present a novel actively secure multiparty computation protocol in the dishonest majority setting, where the computation domain is a ring of the type\\(\\mathbb {Z}_{2^k}\\). Instead of considering an “extension ring” of the form\\(\\mathbb {Z}_{2^{k+\\kappa }}\\)as in SPD\\(\\mathbb {Z}_{2^k}\\)(<PERSON><PERSON><PERSON> et al., CRYPTO 2018) and its derivatives, we make use of an actual ring extension, or more precisely, a Galois ring extension\\(\\mathbb {Z}_{p^k}[\\texttt{X}]/(h(\\texttt{X}))\\)of large enough degree, in order to ensure that the adversary cannot cheat except with negligible probability. These techniques have been used already in the context of honest majority MPC over\\(\\mathbb {Z}_{p^k}\\), and to the best of our knowledge, our work constitutes the first study of the benefits of these tools in the dishonest majority setting. Making use of Galois ring extensions requires great care in order to avoid paying an extra overhead due to the use of larger rings. To address this, reverse multiplication-friendly embeddings (RMFEs) have been used in the honest majority setting (e.g. <PERSON><PERSON><PERSON><PERSON> et al., CRYPTO 2018), and more recently in the dishonest majority setting for computation over\\(\\mathbb {Z}_2\\)(<PERSON><PERSON><PERSON><PERSON> and Gun<PERSON>en, TCC 2020). We make use of the recent RMFEs over\\(\\mathbb {Z}_{p^k}\\)from (Cramer et al., CRYPTO 2021), together with adaptations of some RMFE optimizations introduced in (Abspoel et al., ASIACRYPT 2021) in the honest majority setting, to achieve an efficient protocol that only requires in its online phase\\(12.4k(n-1)\\)bits of amortized communication complexity and one round of communication for each multiplication gate. We also instantiate the necessary offline phase using Oblivious Linear Evaluation (OLE) by generalizing the approach based on Oblivious Transfer (OT) proposed in MASCOT (Keller et al., CCS 2016). To this end, and as an additional contribution of potential independent interest, we present a novel technique using Multiplication-Friendly Embeddings (MFEs) to achieve OLE over Galois ring extensions using black-box access to an OLE protocol over the base ring\\(\\mathbb {Z}_{p^k}\\)without paying a quadratic cost in terms of the extension degree. This generalizes the approach in MASCOT based on Correlated OT Extension. Finally, along the way we also identify a bug in a central proof in MASCOT, and we implicitly present a fix in our generalized proof.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_14"}, {"primary_key": "1637612", "vector": [], "sparse_vector": [], "title": "Shorter Hash-and-Sign Lattice-Based Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lattice-based digital signature schemes following the hash-and-sign design paradigm of Gentry, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (GPV) tend to offer an attractive level of efficiency, particularly when instantiated with structured compact trapdoors. In particular, NIST postquantum finalistFalconis both quite fast for signing and verification and quite compact: NIST notes that it has the smallest bandwidth (as measured in combined size of public key and signature) of all round 2 digital signature candidates. Nevertheless, whileFalcon–512, for instance, compares favorably to ECDSA–384 in terms of speed, its signatures are well over 10 times larger. For applications that store large number of signatures, or that require signatures to fit in prescribed packet sizes, this can be a critical limitation. In this paper, we explore several approaches to further improve the size of hash-and-sign lattice-based signatures, particularly instantiated over NTRU lattices likeFalconand its recent variantMitaka. In particular, while GPV signatures are usually obtained by sampling lattice points according to somesphericaldiscrete Gaussian distribution, we show that it can be beneficial to sample instead according to a suitably chosenellipsoidaldiscrete Gaussian: this is because only half of the sampled Gaussian vector is actually output as the signature, while the other half is recovered during verification. Making the half that actually occurs in signatures shorter reduces signature size at essentially no security loss (in a suitable range of parameters). Similarly, we show that reducing the modulusqwith respect to which signatures are computed can improve signature size as well as verification key size almost “for free”; this is particularly true for constructions likeFalconandMitakathat do not make substantial use of NTT-based multiplication (and rely instead on transcendental FFT). Finally, we show that the Gaussian vectors in signatures can be represented in a more compact way with appropriate coding-theoretic techniques, improving signature size by an additional 7 to 14%. All in all, we manage to reduce the size of, e.g.,Falconsignatures by 30–40% at the cost of only 4–6 bits of Core-SVP security.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_9"}, {"primary_key": "1637613", "vector": [], "sparse_vector": [], "title": "Partial Key Exposure Attacks on BIKE, Rainbow and NTRU.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a so-called partial key exposure attack one obtains some information about the secret key, e.g. via some side-channel leakage. This information might be a certain\n\n\n fraction of the secret key bits (erasure model) or some erroneous version of the secret key (error model). The goal is to recover the secret key from the leaked information. There is a common belief that, as opposed to e.g. the RSA cryptosystem, most post-quantum cryptosystems are usually resistant against partial key exposure attacks. We strongly question this belief by constructing partial key exposure attacks on code-based, multivariate, and lattice-based schemes (BIKE, Rainbow and NTRU). Our attacks exploit the redundancy that modern PQ cryptosystems inherently use for efficiency reasons. The application and development of techniques from information set decoding plays a crucial role for achieving our results. On the theoretical side, we show non-trivial information leakage bounds that allow for a polynomial time key recovery attack. As an example, for all schemes the knowledge of a constant fraction of the secret key bits suffices to reconstruct the full key in polynomial time. Even if we no longer insist on polynomial time attacks, most of our attacks extend well and remain feasible up to large erasure and error rates. In the case of BIKE for example we obtain attack complexities around 60 bits when half of the secret key bits are erased, or a quarter of the secret key bits are faulty. Our results show that even highly error-prone key leakage of modern PQ cryptosystems may lead to full secret key recoveries.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_12"}, {"primary_key": "1637614", "vector": [], "sparse_vector": [], "title": "Syndrome Decoding in the Head: Shorter Signatures from Zero-Knowledge Proofs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Zero-knowledge proofs of knowledge are useful tools to design signature schemes. The ongoing effort to build a quantum computer urges the cryptography community to develop new secure cryptographic protocols based on quantum-hard cryptographic problems. One of the few directions is code-based cryptography for which the strongest problem is thesyndrome decoding(SD) for random linear codes. This problem is known to beNP-hard and the cryptanalysis state of the art has been stable for many years. A zero-knowledge protocol for this problem was pioneered by Stern in 1993. Since its publication, many articles proposed optimizations, implementation, or variants. In this paper, we introduce a new zero-knowledge proof for the syndrome decoding problem on random linear codes. Instead of using permutations like most of the existing protocols, we rely on the MPC-in-the-head paradigm in which we reduce the task of proving the low Hamming weight of the SD solution to proving some relations between specific polynomials. Specifically, we propose a 5-round zero-knowledge protocol that proves the knowledge of a vectorxsuch that\\(y=Hx\\)and\\({\\text {wt}}(x)\\le w\\)and which achieves a soundness error closed to 1/Nfor anarbitraryN. While turning this protocol into a signature scheme, we achieve a signature size of 11–12 KB for 128-bit security when relying on the hardness of the SD problem on binary fields. Using larger fields (like\\(\\mathbb {F}_{2^8}\\)), we can produce fast signatures of around 8 KB. This allows us to outperform Picnic3 and to be competitive with SPHINCS+, both post-quantum signature candidates in the ongoing NIST standardization effort. Moreover, our scheme outperforms all the existing code-based signature schemes for the common “signature size\\(+\\)public key size” metric.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_19"}, {"primary_key": "1637615", "vector": [], "sparse_vector": [], "title": "Maliciously Secure Massively Parallel Computation for All-but-One Corruptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Massive Parallel Computing (MPC) model gained wide adoption over the last decade. By now, it is widely accepted as the right model for capturing the commonly used programming paradigms (such as MapReduce, Hadoop, and Spark) that utilize parallel computation power to manipulate and analyze huge amounts of data. Motivated by the need to perform large-scale data analytics in a privacy-preserving manner, several recent works have presented generic compilers that transform algorithms in the MPC model into secure counterparts, while preserving various efficiency parameters of the original algorithms. The first paper, due to <PERSON> et al. (ITCS ’20), focused on the honest majority setting. Later, <PERSON> et al. (TCC ’20) considered the dishonest majority setting. The latter work presented a compiler that transforms generic MPC algorithms into ones which are secure againstsemi-honestattackers that may control all but one of the parties involved. The security of their resulting algorithm relied on the existence of a PKI and also on rather strong cryptographic assumptions: indistinguishability obfuscation and the circular security of certain LWE-based encryption systems. In this work, we focus on the dishonest majority setting, following <PERSON> et al. In this setting, the known compilers do not achieve the standard security notion calledmalicioussecurity, where attackers can arbitrarily deviate from the prescribed protocol. In fact, we show that unless very strong setup assumptions as made (such as aprogrammablerandom oracle), it is provablyimpossibleto withstand malicious attackers due to the stringent requirements on space and round complexity. As our main contribution, we complement the above negative result by designing the first general compiler for malicious attackers in the dishonest majority setting. The resulting protocols withstand all-but-one corruptions. Our compiler relies on a simple PKI and a (programmable) random oracle, and is proven secure assuming LWE and SNARKs. Interestingly, even with such strong assumptions, it is rather non-trivial to obtain a secure protocol.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_24"}, {"primary_key": "1637616", "vector": [], "sparse_vector": [], "title": "Ofelimos: Combinatorial Optimization via Proof-of-Useful-Work - A Provably Secure Blockchain Protocol.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Minimizing the energy cost and carbon footprint of the Bitcoin blockchain and related protocols is one of the most widely identified open questions in the cryptocurrency space. Substituting the proof-of-work (PoW) primitive in Nakamoto’s longest-chain protocol with aproof of useful work(PoUW) has been long theorized as an ideal solution in many respects but, to this day, the concept still lacks a convincingly secure realization. In this work we put forthOfelimos, a novel PoUW-based blockchain protocol whose consensus mechanism simultaneously realizes a decentralized optimization-problem solver. Our protocol is built around a novel local search algorithm, which we call Doubly Parallel Local Search (DPLS), that is especially crafted to suit implementation as the PoUW component of our blockchain protocol. We provide a thorough security analysis of our protocol and additionally present metrics that reflect the usefulness of the system. DPLS can be used to implement variants of popular local search algorithms such as WalkSAT that are used for real world combinatorial optimization tasks. In this way, our work paves the way for safely using blockchain systems as generic optimization engines for a variety of hard optimization problems for which a publicly verifiable solution is desired.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_12"}, {"primary_key": "1637617", "vector": [], "sparse_vector": [], "title": "Time-Space Tradeoffs for Sponge Hashing: Attacks and Limitations for Short Collisions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sponge hashing is a novel alternative to the popular Merkle-Damgård hashing design. The sponge construction has become increasingly popular in various applications, perhaps most notably, it underlies the SHA-3 hashing standard. Sponge hashing is parametrized by two numbers,randc(bitrate and capacity, respectively), and by a fixed-size permutation on\\(r+c\\)bits. In this work, we study the collision resistance of sponge hashing instantiated with a random permutation by adversaries with arbitraryS-bit auxiliary advice input about the random permutation that makeTonline queries. Recent work by <PERSON><PERSON> et al. (CRYPTO ’18) showed that such adversaries can find collisions (with respect to a randomc-bit initialization vector) with advantage\\(\\varTheta (ST^2/2^c + T^2/ 2^{r})\\). Although the above attack formally breaks collision resistance in some range of parameters, its practical relevance is limited since the resulting collision is very long (on the order ofTblocks). Focusing on the task of findingshortcollisions, we study the complexity of finding aB-block collision for a given parameter\\(B\\ge 1\\). We give several new attacks and limitations. Most notably, we give a new attack that results in a single-block collision and has advantage In certain range of parameters (e.g.,\\(ST^2>2^c\\)), our attack outperforms the previously-known best attack. To the best of our knowledge, this is the first natural application for which sponge hashing isprovably less securethan the corresponding instance of Merkle-Damgård hashing. Our attack relies on a novel connection between single-block collision finding in sponge hashing and the well-studied function inversion problem. We also give a general attack that works for any\\(B\\ge 2\\)and has advantage\\(\\varOmega ({STB}/{2^{c}} + {T^2}/{2^{\\min \\{r,c\\}}})\\), adapting an idea of Akshima et al. (CRYPTO ’20). We complement the above attacks with bounds on the best possible attacks. Specifically, we prove that there is a qualitative jump in the advantage of best possible attacks for finding unbounded-length collisions and those for finding very short collisions. Most notably, we prove (via a highly non-trivial compression argument) that the above attack is optimal for\\(B=2\\)in some range of parameters.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_5"}, {"primary_key": "1637618", "vector": [], "sparse_vector": [], "title": "Structure-Aware Private Set Intersection, with Applications to Fuzzy Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In two-party private set intersection (PSI), <PERSON> holds a setX, <PERSON> holds a setY, and they learn (only) the contents of\\(X \\cap Y\\). We introducestructure-aware PSIprotocols, which take advantage of situations where <PERSON>’s setXis publicly known to have a certain structure. The goal of structure-aware PSI is to have communication that scales with thedescription sizeof <PERSON>’s set, rather itscardinality. We introduce a new generic paradigm for structure-aware PSI based on function secret-sharing (FSS). In short, if there exists compact FSS for a class of structured sets, then there exists a semi-honest PSI protocol that supports this class of input sets, with communication cost proportional only to the FSS share size. Several prior protocols for efficient (plain) PSI can be viewed as special cases of our new paradigm, with an implicit FSS for unstructured sets. Our PSI protocol can be instantiated from a significantly weaker flavor of FSS, which has not been previously studied. We develop several improved FSS techniques that take advantage of these relaxed requirements, and which are in some cases exponentially better than existing FSS. Finally, we explore in depth a natural application of structure-aware PSI. If <PERSON>’s setXis the union of many radius-\\(\\delta \\)balls in some metric space, then an intersection betweenXandYcorresponds tofuzzy PSI, in which the parties learn which of their points are within distance\\(\\delta \\). In structure-aware PSI, the communication cost scales with the number of balls in <PERSON>’s set, rather than their total volume. Our techniques lead to efficient fuzzy PSI for\\(\\ell _\\infty \\)and\\(\\ell _1\\)metrics (and approximations of\\(\\ell _2\\)metric) in high dimensions. We implemented this fuzzy PSI protocol for 2-dimensional\\(\\ell _\\infty \\)metrics. For reasonable input sizes, our protocol requires 45–60% less time and 85% less communication than competing approaches that simply reduce the problem to plain PSI.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_12"}, {"primary_key": "1637619", "vector": [], "sparse_vector": [], "title": "On Time-Space Tradeoffs for Bounded-Length Collisions in Merkle-Damgård Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study\n the power of preprocessing adversaries in finding bounded-length collisions in the widely used Merkle-<PERSON> (MD) hashing in the random oracle model. Specifically, we consider adversaries with arbitraryS-bit advice about the random oracle and can make at mostTqueries to it. Our goal is to characterize the advantage of such adversaries in finding aB-block collision in an MD hash function constructed using the random oracle with range sizeNas the compression function (given a random salt). The answer to this question is completely understood for very large values ofB(essentially\\(\\varOmega (T)\\)) as well as for\\(B=1,2\\). For\\(B\\approx T\\), <PERSON><PERSON> et al. (EUROCRYPT ’18) gave matching upper and lower bounds of\\(\\tilde{\\varTheta }(ST^2/N)\\). <PERSON><PERSON><PERSON> et al. (CRYPTO ’20) observed that the attack of <PERSON><PERSON> et al. could be adapted to work for any value of\\(B>1\\), giving an attack with advantage\\(\\tilde{\\varOmega }(STB/N + T^2/N)\\). Unfortunately, they could only prove that this attack is optimal for\\(B=2\\). Their proof involves a compression argument with exhaustive case analysis and, as they claim, a naive attempt to generalize their bound to larger values of B (even for\\(B=3\\)) would lead to an explosion in the number of cases needed to be analyzed, making it unmanageable. With the lack of a more general upper bound, they formulated theSTB conjecture, stating that the best-possible advantage is\\(\\tilde{O}(STB/N + T^2/N)\\)for any\\(B>1\\). In this work, we confirm the STB conjecture in many new parameter settings. For instance, in one result, we show that the conjecture holds for all constant values ofB. Further, using combinatorial properties of graphs, we are able to confirm the conjecture even for super constant values ofB, as long as some restriction is made onS. For instance, we confirm the conjecture for all\\(B \\leqslant T^{1/4}\\)as long as\\(S \\leqslant T^{1/8}\\). Technically, we develop structural characterizations for bounded-length collisions in MD hashing that allow us to give a compression argument in which the number of cases needed to be handled does not explode.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_6"}, {"primary_key": "1637620", "vector": [], "sparse_vector": [], "title": "Tight Bounds on the Randomness Complexity of Secure Multiparty Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the question of minimizing therandomness complexityof protocols for secure multiparty computation (MPC) in the setting of perfect information-theoretic security. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (SIAM J. Discret. Math., 1997) studied the case ofn-party semi-honest MPC for the XOR function with security threshold\\(t<n\\), showing that\\(O(t^2\\log (n/t))\\)random bits are sufficient and\\(\\varOmega (t)\\)random bits are necessary. Their positive result was obtained via a non-explicit protocol, whose existence was proved using the probabilistic method. We essentially close the question by proving an\\(\\varOmega (t^2)\\)lower bound on the randomness complexity of XOR, matching the previous upper bound up to a logarithmic factor (or constant factor when\\(t=\\varOmega (n)\\)). We also obtain anexplicitprotocol that uses\\(O(t^2\\cdot \\log ^2n)\\)random bits, matching our lower bound up to a polylogarithmic factor. We extend these results from XOR to generalsymmetricBoolean functions and to addition over a finite Abelian group, showing how to amortize the randomness complexity over multiple additions. Finally, combining our techniques with recent randomness-efficient constructions of private circuits, we obtain an explicit protocol for evaluating a general circuitCusing only\\(O(t^2\\cdot \\log |C|)\\)random bits, by employing additional “helper parties” who do not contribute any inputs. This upper bound too matches our lower bound up to a logarithmic factor.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_17"}, {"primary_key": "1637621", "vector": [], "sparse_vector": [], "title": "Sharing Transformation and Dishonest Majority MPC with Packed Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON>"], "summary": "In the last few years, the efficiency of secure multi-party computation (MPC) in the dishonest majority setting has increased by several orders of magnitudes starting with the SPDZ protocol family which offers a speedy information-theoretic online phase in the prepossessing model. However, state-of-the-artn-party MPC protocols in the dishonest majority setting incur online communication complexity per multiplication gate which is linear in the number of parties, i.e.O(n), per gate across all parties. In this work, we construct the first MPC protocols in the preprocessing model for dishonest majority with sub-linear communication complexity per gate in the number of partiesn. To achieve our results, we extend the use of packed secret sharing to the dishonest majority setting. For a constant fraction of corrupted parties (i.e. if 99 percent of the parties are corrupt), we can achieve a communication complexity ofO(1) field elements per multiplication gate across all parties. At the crux of our techniques lies a new technique calledsharing transformation. The sharing transformation technique allows us to transform shares under one type of linear secret sharing scheme into another, and even perform arbitrary linear maps on the secrets of (packed) secret sharing schemes with optimal communication complexity. This technique can be of independent interest since transferring shares from one type of scheme into another (e.g., for degree reduction) is ubiquitous in MPC. Furthermore, we introduce what we callsparsely packed Shamir sharingwhich allows us to address the issue of network routing efficiently, andpacked Beaver tripleswhich is an extension of the widely used technique of Beaver triples for packed secret sharing (for dishonest majority).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_1"}, {"primary_key": "1637622", "vector": [], "sparse_vector": [], "title": "Locally Verifiable Signature and Key Aggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aggregate signatures (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>crypt 2003) enable compressing a set ofNsignatures onNdifferent messages into a short aggregate signature. This reduces the space complexity of storing the signatures from linear inNto a fixed constant (that depends only on the security parameter). However, verifying the aggregate signature requires access to allNmessages, resulting in the complexity of verification being at least\\(\\varOmega (N)\\). In this work, we introduce the notion oflocally verifiableaggregate signatures that enableefficient verification: given a short aggregate signature\\(\\sigma \\)(corresponding to a set\\(\\mathcal {M}\\)ofNmessages), the verifier can check whether a particular messagemis in the set, in time independent ofN. Verification doesnotrequire knowledge of the entire set\\(\\mathcal {M}\\). We demonstrate many natural applications of locally verifiable aggregate signature schemes: in the context of certificate transparency logs; in blockchains; and for redacting signatures, even when all the original signatures are produced by a single user. We provide two constructions of single-signer locally verifiable aggregate signatures, the first based on the RSA assumption and the second on the bilinear <PERSON><PERSON><PERSON><PERSON><PERSON> inversion assumption, both in the random oracle model. As an additional contribution, we introduce the notion of compressing cryptographic keys in identity-based encryption (IBE) schemes, show applications of this notion, and construct an IBE scheme where the secret keys forNidentities can be compressed into a single aggregate key, which can then be used to decrypt ciphertexts sent to any of theNidentities.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_26"}, {"primary_key": "1637623", "vector": [], "sparse_vector": [], "title": "Block-Cipher-Based Tree Hashing.", "authors": ["Aldo <PERSON>ing"], "summary": "First of all we take a thorough look at an error in a paper by <PERSON><PERSON><PERSON> et al. (ToSC 2018) which looks at minimal requirements for tree-based hashing based on multiple primitives, including block ciphers. This reveals that the error is more fundamental than previously shown by <PERSON><PERSON> et al. (ToSC 2020), which is mainly interested in its effect on the security bounds. It turns out that the cause for the error is due to an essential oversight in the interaction between the different oracles used in the indifferentiability proofs. In essence, it reduces the claim from the normal indifferentiability setting to the weaker sequential indifferentiability one. As a matter of fact, this error appeared in multiple earlier indifferentiability papers, including the optimal indifferentiability of the sum of permutations (EUROCRYPT 2018) and the recent\\(\\textrm{ABR}^+\\)construction (EUROCRYPT 2021). We discuss in detail how this oversight is caused and how it can be avoided. We next demonstrate how the negative effects on the security bound of the construction by <PERSON><PERSON><PERSON> et al. can be resolved. Instead of only allowing a truncated output, we generalize the construction to allow foranyfinalization function and investigate the security of this for five different types of finalization. Our findings, among others, show that the security of the SHA-2 mode does not degrade if the feed-forward is dropped and that the modern BLAKE3 construction is secure in principle but that its use of the extendable output requires its counter used for random access to be public. Finally, we introduce the tree sponge, a generalization of the sequential sponge construction with parallel absorbing and squeezing.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_8"}, {"primary_key": "1637624", "vector": [], "sparse_vector": [], "title": "Lower Bound on SNARGs in the Random Oracle Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "E<PERSON>"], "summary": "Succinct non-interactive arguments (SNARGs) have become a fundamental primitive in the cryptographic community. The focus of this work is constructions of SNARGs in the Random Oracle Model (ROM). Such SNARGs enjoy post-quantum security and can be deployed using lightweight cryptography to heuristically instantiate the random oracle. A ROM-SNARG is\\((t,\\varepsilon )\\)-soundif no\\(t\\)-query malicious prover can convince the verifier to accept a false statement with probability larger than\\(\\varepsilon \\). Recently, Chiesa-Yogev (CRYPTO ’21) presented a ROM-SNARG of length\\({\\varTheta }(\\log (t/\\varepsilon ) \\cdot \\log t)\\)(ignoring\\(\\log n\\)factors, fornbeing the instance size). This improvement, however, is still far from the (folklore) lower bound of\\(\\varOmega (\\log (t/\\varepsilon ))\\). Assuming therandomized exponential-time hypothesis, we prove a tight lower bound of\\({\\varOmega }(\\log (t/\\varepsilon ) \\cdot \\log t)\\)for the length of\\((t,\\varepsilon )\\)-sound ROM-SNARGs. Our lower bound holds for constructions with non-adaptive verifiers and strong soundness notion calledsalted soundness, restrictions that hold forallknown constructions (ignoring contrived counterexamples). We prove our lower bound by transforming any short ROM-SNARG (of the considered family) into a same length ROM-SNARG in which the verifier asks only afeworacles queries, and then apply the recent lower bound of Chiesa-Yogev (TCC ’20) for such SNARGs.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_4"}, {"primary_key": "1637625", "vector": [], "sparse_vector": [], "title": "Certified Everlasting Zero-Knowledge Proof for QMA.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In known constructions of classical zero-knowledge protocols for\\({\\textbf {NP}}\\), either zero-knowledge or soundness holds only against computationally bounded adversaries. Indeed, achieving both statistical zero-knowledge and statistical soundness at the same time with classical verifier is impossible for\\({\\textbf {NP}}\\)unless the polynomial-time hierarchy collapses, and it is also believed to be impossible even with a quantum verifier. In this work, we introduce a novel compromise, which we call the certified everlasting zero-knowledge proof for\\({\\textbf {QMA}}\\). It is a computational zero-knowledge proof for\\({\\textbf {QMA}}\\), but the verifier issues a classical certificate that shows that the verifier has deleted its quantum information. If the certificate is valid, even an unbounded malicious verifier can no longer learn anything beyond the validity of the statement. We construct a certified everlasting zero-knowledge proof for\\({\\textbf {QMA}}\\). For the construction, we introduce a new quantum cryptographic primitive, which we call commitment with statistical binding and certified everlasting hiding, where the hiding property becomes statistical once the receiver has issued a valid certificate that shows that the receiver has deleted the committed information. We construct commitment with statistical binding and certified everlasting hiding from quantum encryption with certified deletion by <PERSON>bent and Islam [TCC 2020] (in a black-box way), and then combine it with the quantum sigma-protocol for\\({\\textbf {QMA}}\\)by <PERSON>bent and Grilo [FOCS 2020] to construct the certified everlasting zero-knowledge proof for\\({\\textbf {QMA}}\\). Our constructions are secure in the quantum random oracle model. Commitment with statistical binding and certified everlasting hiding itself is of independent interest, and there will be many other useful applications beyond zero-knowledge.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_9"}, {"primary_key": "1637626", "vector": [], "sparse_vector": [], "title": "Practical Statistically-Sound Proofs of Exponentiation in Any Group.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A proof of exponentiation (PoE) in a group\\({\\mathbb {G}}\\)of unknown order allows a prover to convince a verifier that a tuple\\((x,q,T,y)\\in {\\mathbb {G}}\\times {\\mathbb {N}}\\times {\\mathbb {N}}\\times {\\mathbb {G}}\\)satisfies\\(x^{q^T}=y\\). This primitive has recently found exciting applications in the constructions of verifiable delay functions and succinct arguments of knowledge. The most practical PoEs only achieve soundness either under computational assumptions, i.e., they are arguments (<PERSON><PERSON><PERSON>, Journal of Cryptology 2020), or in groups that come with the promise of not having any small subgroups (<PERSON><PERSON><PERSON>, ITCS 2019). The only statistically-sound PoE ingeneralgroups of unknown order is due to <PERSON> et al. (CRYPTO 2021), and can be seen as an elaborate parallel repetition of <PERSON><PERSON><PERSON>’s PoE: to achieve\\(\\lambda \\)bits of security, say\\(\\lambda =80\\), the number of repetitions required (and thus the blow-up in communication) is as large as\\(\\lambda \\). In this work, we propose a statistically-sound PoE for the case where the exponentqis the product of all primes up to some boundB. We show that, in this case, it suffices to run only\\(\\lambda /\\log (B)\\)parallel instances of <PERSON><PERSON><PERSON>’s PoE, which reduces the concrete proof-size compared to Block et al. by an order of magnitude. Furthermore, we show that in the known applications where PoEs are used as a building block such structured exponents are viable. Finally, we also discuss batching of our PoE, showing that many proofs (for the same\\(\\mathbb {G}\\)andqbut differentxandT) can be batched by adding only a single element to the proof per additional statement.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_13"}, {"primary_key": "1637627", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Property Preserving Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Property-preserving hashing (PPH)consists of a family of compressing hash functionshsuch that, for any two inputsx,y, we can correctly identify whether some propertyP(x,y) holds given only the digestsh(x),h(y). In a basic PPH, correctness should hold with overwhelming probability over the choice ofhwhenx,yare worst-case values chosen a-priori and independently ofh. In an adversariallyrobustPPH (RPPH), correctness must hold even whenx,yare chosen adversarially and adaptively depending onh. Here, we study (R)PPH for the property that theHamming distancebetweenxandyis at mostt. The notion of (R)PPH was introduced by <PERSON>, <PERSON> and <PERSON><PERSON> (ITCS ’19), and further studied by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (Eurocrypt ’21) and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> (Eurocrypt ’22). In this work, we obtain improved constructions that are conceptually simpler, have nearly optimal parameters, and rely on more general assumptions than prior works. Our results are: We construct information-theoretic non-robust PPH for Hamming distance via syndrome list-decoding of linear error-correcting codes. We provide a lower bound showing that this construction is essentially optimal. We make the above construction robust with little additional overhead, by relying on homomorphic collision-resistant hash functions, which can be constructed from either the discrete-logarithm or the short-integer-solution assumptions. The resulting RPPH achieves improved compression compared to prior constructions, and is nearly optimal. We also show an alternate construction of RPPH for Hamming distance under the minimal assumption that standard collision-resistant hash functions exist. The compression is slightly worse than our optimized construction using homomorphic collision-resistance, but essentially matches the prior state of the art constructions from specific algebraic assumptions. Lastly, we study a new notion of randomized robust PPH (R2P2H) for Hamming distance, which relaxes RPPH by allowing the hashing algorithm itself to be randomized. We give an information-theoretic construction with optimal parameters.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_16"}, {"primary_key": "1637628", "vector": [], "sparse_vector": [], "title": "Faster Sounder Succinct Arguments and sfIOPs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Succinct arguments allow a prover to convince a verifier that a given statement is true, using an extremely short proof. A major bottleneck that has been the focus of a large body of work is in reducing the overhead incurred by the prover in order to prove correctness of the computation. By overhead we refer to the cost of proving correctness, divided by the cost of the original computation. In this work, for a large class of Boolean circuits\\(C=C(x,w)\\), we construct succinct arguments for the language\\(\\{ x : \\exists w\\; C(x,w)=1\\}\\), with\\(2^{-\\lambda }\\)soundness error, and with prover overhead\\(\\textrm{polylog}(\\lambda )\\). This result relies on the existence of (sub-exponentially secure) linear-size computable collision-resistant hash functions. The class of Boolean circuits that we can handle includes circuits with a repeated sub-structure, which arise in natural applications such as batch computation/verification, hashing and related block chain applications. The succinct argument is obtained by constructinginteractive oracle proofsfor the same class of languages, with\\(\\textrm{polylog}(\\lambda )\\)prover overhead, and soundness error\\(2^{-\\lambda }\\). Prior to our work, the best\\(\\textsf{IOP}\\)s for Boolean circuits either had prover overhead of\\(\\textrm{polylog}(|C|)\\)based on efficient\\(\\textsf{PCP}\\)s due to Ben Sassonet al.(STOC, 2013) or\\(\\textrm{poly}(\\lambda )\\)due to Rothblum and Ron-Zewi (STOC, 2022).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_17"}, {"primary_key": "1637629", "vector": [], "sparse_vector": [], "title": "Formal Verification of Saber&apos;s Public-Key Encryption Scheme in EasyCrypt.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we consider the formal verification of the public-key encryption scheme of <PERSON><PERSON>, one of the selected few post-quantum cipher suites currently considered for potential standardization. We formally verify this public-key encryption scheme’ssecurity and\\(\\delta \\)-correctness properties, i.e., the properties required to transform the public-key encryption scheme into ansecure and\\(\\delta \\)-correct key encapsulation mechanism, in EasyCrypt. To this end, we initially devise hand-written proofs for these properties that are significantly more detailed and meticulous than the presently existing proofs. Subsequently, these hand-written proofs serve as a guideline for the formal verification. The results of this endeavor comprise hand-written and computer-verified proofs which demonstrate that <PERSON><PERSON>’s public-key encryption scheme indeed satisfies the desired security and correctness properties.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_22"}, {"primary_key": "1637630", "vector": [], "sparse_vector": [], "title": "Beyond the Csiszár-<PERSON><PERSON> Bo<PERSON>: Best-Possible Wiretap Coding via Obfuscation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Awiretap coding scheme(<PERSON><PERSON><PERSON>, Bell Syst. Tech. J. 1975) enables <PERSON> to reliably communicate a messagemto an honest <PERSON> by sending an encodingcover a noisy channel\\(\\textsf{ChB}\\), while at the same time hidingmfrom <PERSON> who receivescover another noisy channel\\(\\textsf{ChE}\\). Wiretap coding is clearly impossible when\\(\\textsf{ChB}\\)is adegradedversion of\\(\\textsf{ChE}\\), in the sense that the output of\\(\\textsf{ChB}\\)can be simulated using only the output of\\(\\textsf{ChE}\\). A classic work of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (IEEE Trans. Inf. Theory, 1978) shows that the converse does not hold. This follows from their full characterization of the channel pairs\\((\\textsf{ChB},\\textsf{ChE})\\)that enable information-theoretic wiretap coding. In this work, we show that in fact the conversedoeshold when consideringcomputational security; that is, wiretap coding against a computationally bounded Eve is possibleif and only if\\(\\textsf{ChB}\\)is not a degraded version of\\(\\textsf{ChE}\\). Our construction assumes the existence of virtual black-box (VBB) obfuscation of specific classes of “evasive” functions that generalize fuzzy point functions, and can be heuristically instantiated using indistinguishability obfuscation. Finally, our solution has the appealing feature of beinguniversalin the sense that <PERSON>’s algorithm depends only on\\(\\textsf{ChB}\\)and not on\\(\\textsf{ChE}\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_20"}, {"primary_key": "1637631", "vector": [], "sparse_vector": [], "title": "A New Approach to Efficient Non-Malleable Zero-Knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-malleable zero-knowledge, originally introduced in the context of man-in-the-middle attacks, serves as an important building block to protect against concurrent attacks where different protocols may coexist and interleave. While this primitive admits almost optimal constructions in the plain model, they areseveralorders of magnitude slower in practice than standalone zero-knowledge. This is in sharp contrast to non-malleablecommitmentswhere practical constructions (under the DDH assumption) have been known for a while. We present a new approach for constructing efficient non-malleable zero-knowledge for all languages in\\(\\mathcal{N}\\mathcal{P}\\), based on a new primitive calledinstance-based non-malleable commitment(\\(\\textsf{IB}\\text {-}\\textsf{NMC}\\)). We show how to construct practical\\(\\textsf{IB}\\text {-}\\textsf{NMC}\\)by leveraging the fact thatsimulatorsofsub-linearzero-knowledge protocols can be much faster than the honest prover algorithm. With an efficient implementation of\\(\\textsf{IB}\\text {-}\\textsf{NMC}\\), our approach yields the first general-purpose non-malleable zero-knowledge protocol that achieves practical efficiencyin the plain model. All of our protocols can be instantiated from symmetric primitives such as block-ciphers and collision-resistant hash functions, have reasonable efficiency in practice, and are general-purpose. Our techniques also yield the first efficient non-malleable commitment schemewithout public-key assumptions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_14"}, {"primary_key": "1637632", "vector": [], "sparse_vector": [], "title": "Public-Coin 3-Round Zero-Knowledge from Learning with <PERSON><PERSON><PERSON> and Keyless Multi-Collision-Resistant <PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We construct a public-coin 3-round zero-knowledge argument for NP assuming (i) the sub-exponential hardness of the learning with errors (LWE) problem and (ii) the existence of keyless multi-collision-resistant hash functions against slightly super-polynomial-time adversaries. These assumptions are almost identical to those that were used recently to obtain a private-coin 3-round zero-knowledge argument [<PERSON><PERSON><PERSON> et al., STOC 2018]. (The difference is that we assume sub-exponential hardness instead of quasi-polynomial hardness for the LWE problem.)", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_16"}, {"primary_key": "1637633", "vector": [], "sparse_vector": [], "title": "log *-Round Game-Theoretically-Fair Leader Election.", "authors": ["<PERSON><PERSON>", "Shin&<PERSON><PERSON>s;<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is well-known that in the presence of majority coalitions,strongly faircoin toss is impossible. A line of recent works have shown that by relaxing the fairness notion to game theoretic, we can overcome this classical lower bound. In particular, <PERSON> et al. (CRYPTO’21) showed how to achieve approximately (game-theoretically) fair leader election in the presence of majority coalitions, with round complexity as small as\\(O(\\log \\log n)\\)rounds. In this paper, we revisit the round complexity of game-theoretically fair leader election. We construct\\(O(\\log ^* n)\\)rounds leader election protocols that achieve\\((1-o(1))\\)-approximate fairness in the presence of\\((1-o(1)) n\\)-sized coalitions. Our protocols achieve the same round-fairness trade-offs as <PERSON> et al.’s and have the advantage of being conceptually simpler. Finally, we also obtain game-theoretically fair protocols for committee election which might be of independent interest.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_14"}, {"primary_key": "1637634", "vector": [], "sparse_vector": [], "title": "Nova: Recursive Zero-Knowledge Arguments from Folding Schemes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new approach to realize incrementally verifiable computation (IVC), in which the prover recursively proves the correct execution of incremental computations of the form\\(y=F^{(\\ell )}(x)\\), whereFis a (potentially non-deterministic) computation,xis the input,yis the output, and\\(\\ell > 0\\). Unlike prior approaches to realize IVC, our approach avoids succinct non-interactive arguments of knowledge (SNARKs) entirely and arguments of knowledge in general. Instead, we introduce and employfolding schemes, a weaker, simpler, and more efficiently-realizable primitive, which reduces the task of checking two instances in some relation to the task of checking a single instance. We construct a folding scheme for a characterization ofNPand show that it implies an IVC scheme with improved efficiency characteristics: (1) the “recursion overhead” (i.e., the number of steps that the prover proves in addition to proving the execution ofF) is a constant and it is dominated by two group scalar multiplications expressed as a circuit (this is the smallest recursion overhead in the literature), and (2) the prover’s work at each step is dominated by two multiexponentiations of sizeO(|F|), providing the fastest prover in the literature. The size of a proof isO(|F|) group elements, but we show that using a variant of an existing zkSNARK, the prover can prove the knowledge of a valid proof succinctly and in zero-knowledge with\\(O(\\log {|F|})\\)group elements. Finally, our approach neither requires a trusted setup nor FFTs, so it can be instantiated efficiently with any cycles of elliptic curves where DLOG is hard.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_13"}, {"primary_key": "1637635", "vector": [], "sparse_vector": [], "title": "Tight Preimage Resistance of the Sponge Construction.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The cryptographic sponge is a popular method for hash function design. The construction is in the ideal permutation model proven to be indifferentiable from a random oracle up to the birthday bound in the capacity of the sponge. This result in particular implies that, as long as the attack complexity does not exceed this bound, the sponge construction achieves a comparable level of collision, preimage, and second preimage resistance as a random oracle. We investigate these state-of-the-art bounds in detail, and observe that while the collision and second preimage security bounds are tight, the preimage boundis not tight. We derive an improved and tight preimage security bound for the cryptographic sponge construction. The result has direct implications for various lightweight cryptographic hash functions. For example, the NIST Lightweight Cryptography finalist Ascon-Hash does not generically achieve\\(2^{128}\\)preimage security as claimed, but even\\(2^{192}\\)preimage security. Comparable improvements are obtained for the modes of Spongent, PHOTON, ACE, Subterranean 2.0, and QUARK, among others.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_7"}, {"primary_key": "1637636", "vector": [], "sparse_vector": [], "title": "Securing Approximate Homomorphic Encryption Using Differential Privacy.", "authors": ["Baiyu Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work of <PERSON> and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2021) has shown that the traditional formulation ofindistinguishability under chosen plaintext attack(\\(\\ensuremath {\\textsf {IND\\text {-}CPA}}\\)) is not adequate to capture the security ofapproximatehomomorphic encryption against passive adversaries, and identified a stronger\\(\\ensuremath {\\textsf {IND\\text {-}CPA}^{\\textsf {D}}}\\)security definition (\\(\\ensuremath {\\textsf {IND\\text {-}CPA}}\\)with decryption oracles) as the appropriate security target for approximate encryption schemes. We show how to transform any approximate homomorphic encryption scheme achieving the weak\\(\\ensuremath {\\textsf {IND\\text {-}CPA}}\\)security definition, into one which is provably\\(\\ensuremath {\\textsf {IND\\text {-}CPA}^{\\textsf {D}}}\\)secure, offering strong guarantees against realistic passive attacks. The method works by postprocessing the output of the decryption function with a mechanism satisfying an appropriate notion ofdifferential privacy (DP), adding an amount of noise tailored to the worst-case error growth of the homomorphic computation. We apply these results to the approximate homomorphic encryption scheme of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (CKKS, Asiacrypt 2017), proving that adding Gaussian noise to the output ofCKKSdecryption suffices to achieve\\(\\ensuremath {\\textsf {IND\\text {-}CPA}^{\\textsf {D}}}\\)security. We precisely quantify how much Gaussian noise must be added by proving nearly matching upper and lower bounds, showing that one cannot hope to significantly reduce the amount of noise added in this post-processing step. As an additional contribution, we present and use a finer grained definition of bit security that distinguishes between a computational security parameter (c) and a statistical one (s). Based on our upper and lower bounds, we propose parameters for the counter-measures recently adopted by open-source libraries implementing CKKS. Lastly, we investigate the plausible claim that smaller DP noise parameters might suffice to achieve\\(\\ensuremath {\\textsf {IND\\text {-}CPA}^{\\textsf {D}}}\\)-security for schemes supporting more accurate (dynamic, key dependent) estimates of ciphertext noise during decryption. Perhaps surprisingly, we show that this claim is false, and that DP mechanisms with noise parameters tailored to the error present in a given ciphertext, rather than worst-case error, are vulnerable to\\(\\ensuremath {\\textsf {IND\\text {-}CPA}^{\\textsf {D}}}\\)attacks.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_20"}, {"primary_key": "1637637", "vector": [], "sparse_vector": [], "title": "Two-Round MPC Without Round Collapsing Revisited - Towards Efficient Malicious Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent works have made exciting progress on the construction of round optimal,two-round, Multi-Party Computation (MPC) protocols. However, most proposals so far are still complex and inefficient. In this work, we improve the simplicity and efficiency of two-round MPC in the setting with dishonest majority and malicious security. Our protocols make use of the Random Oracle (\\({\\textsf{RO}}\\)) and a generalization of the Oblivious Linear Evaluation (\\(\\textsf{OLE}\\)) correlated randomness, called tensor\\(\\textsf{OLE}\\), over a finite field\\(\\mathbb {F}\\), and achieve the following: MPC for Boolean Circuits:Our two-round, maliciously secure MPC protocols for computing Boolean circuits, has overall (asymptotic) computational cost\\(O(S\\cdot n^3 \\cdot \\log |\\mathbb {F}|)\\), whereSis the size of the circuit computed,nthe number of parties, and\\(\\mathbb {F}\\)a field of characteristic two. The protocols also make black-box calls to a Pseudo-Random Function (PRF). MPC for Arithmetic Branching Programs (ABPs):Our two-round, information theoretically and maliciously secure protocols for computing ABPs over a general field\\(\\mathbb {F}\\)has overall computational cost\\(O(S^{1.5}\\cdot n^3\\cdot \\log |\\mathbb {F}|)\\), whereSis the size of ABP computed. Both protocols achieve security levels inverse proportional to the size of the field\\(|\\mathbb {F}|\\). Our construction is built upon the simple two-round MPC protocols of [Lin-Liu-Wee TCC’20], which are only semi-honest secure. Our main technical contribution lies in ensuring malicious security using simple and lightweight checks, which incur only a constant overhead over the complexity of the protocols by Lin, Liu, and Wee. In particular, in the case of computing Boolean circuits, our malicious MPC protocols have the same complexity (up to a constant overhead) as (insecurely) computing Yao’s garbled circuits in a distributed fashion. Finally, as an additional contribution, we show how to efficiently generate tensor\\(\\textsf{OLE}\\)correlation in fields of characteristic two using OT.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_13"}, {"primary_key": "1637638", "vector": [], "sparse_vector": [], "title": "Oblivious Message Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Anonymous message delivery systems, such as private messaging services and privacy-preserving payment systems, need a mechanism for recipients to retrieve the messages addressed to them without leaking metadata or letting their messages be linked. Recipients could download all posted messages and scan for those addressed to them, but communication and computation costs are excessive at scale. We show how untrusted servers can detect messages on behalf of recipients, and summarize these into a compact encrypted digest that recipients can easily decrypt. These servers operate obliviously and do not learn anything about which messages are addressed to which recipients. Privacy, soundness, and completeness hold even if everyone but the recipient is adversarial and colluding (unlike in prior schemes). Our starting point is an asymptotically-efficient approach, using Fully Homomorphic Encryption and homomorphically-encoded Sparse Random Linear Codes. We then address the concrete performance using bespoke tailoring of lattice-based cryptographic components, alongside various algebraic and algorithmic optimizations. This reduces the digest size to a few bits per message scanned. Concretely, the servers’ cost is\\({\\sim }\\$1\\)per million messages scanned, and the resulting digests can be decoded by recipients in under\\({\\sim }\\)20 ms. Our schemes can thus practically attain the strongest form of receiver privacy for current applications such as privacy-preserving cryptocurrencies.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_26"}, {"primary_key": "1637639", "vector": [], "sparse_vector": [], "title": "Lattice-Based Zero-Knowledge Proofs and Applications: Shorter, Simpler, and More General.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a much-improved practical protocol, based on the hardness of Module-SIS and Module-LWE problems, for proving knowledge of a short vector\\(\\vec s\\)satisfying\\(A\\vec s=\\vec t\\bmod q\\). The currently most-efficient technique for constructing such a proof works by showing that the\\(\\ell _\\infty \\)norm of\\(\\vec s\\)is small. It creates a commitment to a polynomial vector\\(\\textbf{m}\\)whose CRT coefficients are the coefficients of\\(\\vec s\\)and then shows that (1)\\(A\\cdot \\textsf{CRT}(\\textbf{m})=\\vec t\\bmod \\,q\\)and (2) in the case that we want to prove that the\\(\\ell _\\infty \\)norm is at most 1, the polynomial product\\((\\textbf{m}- \\boldsymbol{1})\\cdot \\textbf{m}\\cdot (\\textbf{m}+\\boldsymbol{1})\\)equals to 0. While these schemes are already quite practical, the requirement of using the CRT embedding and only being naturally adapted to proving the\\(\\ell _\\infty \\)-norm, somewhat hinders the efficiency of this approach. In this work, we show that there is a more direct and more efficient way to prove that the coefficients of\\(\\vec s\\)have a small\\(\\ell _2\\)norm which does not require an equivocation with the\\(\\ell _\\infty \\)norm, nor any conversion to the CRT representation. We observe that the inner product between two vectors\\(\\vec r\\)and\\(\\vec s\\)can be made to appear as a coefficient of a product (or sum of products) between polynomials which are functions of\\(\\vec r\\)and\\(\\vec s\\). Thus, by using a polynomial product proof system and hiding all but one coefficient, we are able to prove knowledge of the inner product of two vectors (or of a vector with itself) moduloq. Using a cheap, “approximate range proof”, one can then lift the proof to be over\\(\\mathbb {Z}\\)instead of\\(\\mathbb {Z}_q\\). Our protocols for proving short norms work over all (interesting) polynomial rings, but are particularly efficient for rings like\\(\\mathbb {Z}[X]/(X^n+1)\\)in which the function relating the inner product of vectors and polynomial products happens to be a “nice” automorphism. The new proof system can be plugged into constructions of various lattice-based privacy primitives in a black-box manner. As examples, we instantiate a verifiable encryption scheme and a group signature scheme which are more than twice as compact as the previously best solutions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_3"}, {"primary_key": "1637640", "vector": [], "sparse_vector": [], "title": "Formalizing Delayed Adaptive Corruptions and the Security of Flooding Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many decentralized systems rely on flooding protocols for message dissemination. In such a protocol, the sender of a message sends it to a randomly selected set of peers. These peers again send the message to their randomly selected peers, until every network participant has received the message. This type of protocols clearly fail in face of an adaptive adversary who can simply corrupt all peers of the sender and thereby prevent the message from being delivered. Nevertheless, flooding protocols are commonly used within protocols that aim to be cryptographically secure, most notably in blockchain protocols. While it is possible to revert to static corruptions, this gives unsatisfactory security guarantees, especially in the setting of a blockchain that is supposed to run for an extended period of time. To be able to provide meaningful security guarantees in such settings, we give precise semantics to what we call\\(\\delta \\)-delayed adversariesin the Universal Composability (UC) framework. Such adversaries can adaptively corrupt parties, but there is a delay of time\\(\\delta \\)from when an adversary decides to corrupt a party until they succeed in overtaking control of the party. Within this model, we formally prove the intuitive result that flooding protocols are secure against\\(\\delta \\)-delayed adversaries when\\(\\delta \\)is at least the time it takes to send a message from one peer to another plus the time it takes the recipient to resend the message. To this end, we show how to reduce the adaptive setting with a\\(\\delta \\)-delayed adversary to a static experiment with an Erdős-Rényi graph. Using the established theory of Erdős-Rényi graphs, we provide upper bounds on the propagation time of the flooding functionality for different neighborhood sizes of the gossip network. More concretely, we show the following for security parameter\\(\\kappa \\), point-to-point channels with delay at most\\(\\varDelta \\), andnparties in total, with a sufficiently delayed adversary that can corrupt any constant fraction of the parties: If all parties send to\\(\\varOmega (\\kappa )\\)parties on average, then we can realize a flooding functionality with maximal delay\\(\\mathcal {O}\\bigl (\\varDelta \\cdot \\log (n) \\bigr )\\); and if all parties send to\\(\\varOmega \\bigl ( \\sqrt{\\kappa n} \\bigr )\\)parties on average, we can realize a flooding functionality with maximal delay\\(\\mathcal {O}(\\varDelta )\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_14"}, {"primary_key": "1637641", "vector": [], "sparse_vector": [], "title": "Dynamic Local Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this article, we tackle for the first time the problem ofdynamicmemory-efficient Searchable Symmetric Encryption (SSE). In the term “memory-efficient” SSE, we encompass both the goals oflocalSSE, andpage-efficientSSE. The centerpiece of our approach is a novel connection between those two goals. We introduce a map, called the Generic Local Transform, which takes as input apage-efficientSSE scheme with certain special features, and outputs an SSE scheme with stronglocalityproperties. We obtain several results. (1) First, for page-efficient SSE with page sizep, we build adynamicscheme with storage efficiency\\(\\mathcal {O}({1})\\)and page efficiency\\(\\widetilde{\\mathcal {O}}\\left( {\\textrm{log}\\, \\textrm{log}\\, (N/p)}\\right) \\), called\\(\\textsf{LayeredSSE}\\). The main technical innovation behind\\(\\textsf{LayeredSSE}\\)is a novel weighted extension of the two-choice allocation process, of independent interest. (2) Second, we introduce the Generic Local Transform, and combine it with\\(\\textsf{LayeredSSE}\\)to build adynamicSSE scheme with storage efficiency\\(\\mathcal {O}({1})\\), locality\\(\\mathcal {O}({1})\\), and read efficiency\\(\\widetilde{\\mathcal {O}}\\left( {\\textrm{log}\\,\\textrm{log}\\, N}\\right) \\), under the condition that the longest list is of size\\(\\mathcal {O}({N^{1-1/\\textrm{log}\\, \\textrm{log}\\, \\lambda }})\\). This matches, in every respect, the purelystaticconstruction of Asharov et al. presented at STOC 2016: dynamism comes at no extra cost. (3) Finally, by applying the Generic Local Transform to a variant of the Tethys scheme by Bossuat et al. from Crypto 2021, we build an unconditional static SSE with storage efficiency\\(\\mathcal {O}({1})\\), locality\\(\\mathcal {O}({1})\\), and read efficiency\\(\\mathcal {O}({\\textrm{log}^\\varepsilon N})\\), for an arbitrarily small constant\\(\\varepsilon > 0\\). To our knowledge, this is the construction that comes closest to the lower bound presented by Cash and Tessaro at Eurocrypt 2014.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_4"}, {"primary_key": "1637642", "vector": [], "sparse_vector": [], "title": "Quantum Commitments and Signatures Without One-Way Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the classical world, the existence of commitments is equivalent to the existence of one-way functions. In the quantum setting, on the other hand, commitments are not known to imply one-way functions, but all known constructions of quantum commitments use at least one-way functions. Are one-way functions really necessary for commitments in the quantum world? In this work, we show that non-interactive quantum commitments (for classical messages) with computational hiding and statistical binding exist if pseudorandom quantum states exist. Pseudorandom quantum states are sets of quantum states that are efficiently generated but their polynomially many copies are computationally indistinguishable from the same number of copies of Haar random states [<PERSON>, <PERSON>, and <PERSON>, CRYPTO 2018]. It is known that pseudorandom quantum states exist even if\\(\\textbf{BQP}=\\textbf{QMA}\\)(relative to a quantum oracle) [<PERSON><PERSON><PERSON><PERSON>, TQC 2021], which means that pseudorandom quantum states can exist even if no quantum-secure classical cryptographic primitive exists. Our result therefore shows that quantum commitments can exist even if no quantum-secure classical cryptographic primitive exists. In particular, quantum commitments can exist even if no quantum-secure one-way function exists. In this work, we also consider digital signatures, which are other fundamental primitives in cryptography. We show that one-time secure digital signatures with quantum public keys exist if pseudorandom quantum states exist. In the classical setting, the existence of digital signatures is equivalent to the existence of one-way functions. Our result, on the other hand, shows that quantum signatures can exist even if no quantum-secure classical cryptographic primitive (including quantum-secure one-way functions) exists.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_10"}, {"primary_key": "1637643", "vector": [], "sparse_vector": [], "title": "Secret Can Be Public: Low-Memory AEAD Mode for High-Order Masking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new AEAD mode of operation for an efficient countermeasure against side-channel attacks. Our mode achieves the smallest memory with high-order masking, by minimizing the states that are duplicated in masking. Ans-bit key-dependent state is necessary for achievings-bit security, and the conventional schemes always protect the entiresbits with masking. We reduce the protected state size by introducing anunprotectedstate in the key-dependent state: we protect only a half and give another half to a side-channel adversary. Ensuring independence between the unprotected and protected states is the key technical challenge since mixing these states reveals the protected state to the adversary. We propose a new mode\\(\\textsf{HOMA}\\)that achievess-bit security using a tweakable block cipher with thes/2-bit block size. We also propose a new primitive for instantiating\\(\\textsf{HOMA}\\)with\\(s=128\\)by extending the SKINNY tweakable block cipher to a 64-bit plaintext block, a 128-bit key, and a\\((256+3)\\)-bit tweak. We make hardware performance evaluation by implementing\\(\\textsf{HOMA}\\)with high-order masking for\\(d \\le 5\\). For any\\(d > 0\\),\\(\\textsf{HOMA}\\)outperforms the current state-of-the-art\\(\\textsf{PFB}\\_\\textsf{Plus}\\)by reducing the circuit area larger than that of the entire S-box.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_11"}, {"primary_key": "1637644", "vector": [], "sparse_vector": [], "title": "Succinct Interactive Oracle Proofs: Applications and Limitations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Interactive Oracle Proofs(\\(\\textsf{IOP}\\)s) are a new type of proof-system that combines key properties of interactive proofs and\\(\\textsf{PCP}\\)s:\\(\\textsf{IOP}\\)s enable a verifier to be convinced of the correctness of a statement by interacting with an untrusted prover while reading just a few bits of the messages sent by the prover.\\(\\textsf{IOP}\\)s have become very prominent in the design of efficient proof-systems in recent years. In this work we studysuccinct\\(\\textsf{IOP}\\)s, which are\\(\\textsf{IOP}\\)s in which the communication complexity is polynomial (or even linear) in the original witness. While there are strong impossibility results for the existence of succinct\\(\\textsf{PCP}\\)s (i.e.,\\(\\textsf{PCP}\\)s whose length is polynomial in the witness), it is known that the rich class of\\(\\textsf{NP}\\)relations that are decidable in small space have succinct\\(\\textsf{IOP}\\)s. In this work we show both new applications, and limitations, for succinct\\(\\textsf{IOP}\\)s: First, using one-way functions, we show how to compile\\(\\textsf{IOP}\\)s into zero-knowledgeproofs, while nearly preserving the proof length. This complements a recent line of work, initiated by <PERSON>(TCC, 2016B), who compile\\(\\textsf{IOP}\\)s into super-succinct zero-knowledgearguments. Applying the compiler to the state-of-the-art succinct\\(\\textsf{IOP}\\)s yields zero-knowledge proofs for bounded-space\\(\\textsf{NP}\\)relations, with communication that is nearly equal to the original witness length. This yields the shortest known zero-knowledge proofs from the minimal assumption of one-way functions. Second, we give a barrier for obtaining succinct\\(\\textsf{IOP}\\)s for more general\\(\\textsf{NP}\\)relations. In particular, we show that if a language has a succinct\\(\\textsf{IOP}\\), then it can be decided inspacethat is proportionate only to the witness length, after a bounded-time probabilistic preprocessing. We use this result to show that under a simple and plausible (but to the best of our knowledge, new) complexity-theoretic conjecture, there is no succinct\\(\\textsf{IOP}\\)for\\(\\textsf{CSAT}\\).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_18"}, {"primary_key": "1637645", "vector": [], "sparse_vector": [], "title": "Multimodal Private Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Multimodal Private Signature (MPS) - an anonymous signature system that offers a novel accountability feature: it allows a designated opening authority to learnsome partial information\\(\\textsf{op}\\)about the signer’s identity\\(\\textsf{id}\\), and nothing beyond. Such partial information can flexibly be defined as\\(\\textsf{op} = \\textsf{id}\\)(as in group signatures), or as\\(\\textsf{op} = \\textbf{0}\\)(like in ring signatures), or more generally, as\\(\\textsf{op} = G_j(\\textsf{id})\\), where\\(G_j(\\cdot )\\)is a certain disclosing function. Importantly, the value of\\(\\textsf{op}\\)is known in advance by the signer, and hence, the latter can decide whether she/he wants to disclose that piece of information. The concept of MPS significantly generalizes the notion of tracing in traditional anonymity-oriented signature primitives, and can enable various new and appealing privacy-preserving applications. We formalize the definitions and security requirements for MPS. We next present a generic construction to demonstrate the feasibility of designing MPS in a modular manner and from commonly used cryptographic building blocks (ordinary signatures, public-key encryption and NIZKs). We also provide an efficient construction in the standard model based on pairings, and a lattice-based construction in the random oracle model.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_27"}, {"primary_key": "1637646", "vector": [], "sparse_vector": [], "title": "Practical Sublinear Proofs for R1CS from Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a practical sublinear-size zero-knowledge proof system for Rank-1 Constraint Satisfaction (R1CS) based on lattices. The proof size scales asymptotically with the square root of the witness size. Concretely, the size becomes 2–3 times smaller than <PERSON><PERSON><PERSON> (ACM CCS 2017), which also exhibits square root scaling, for large instances of R1CS. At the core lies an interactive variant of the Schwartz-Zippel Lemma that might be of independent interest.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_5"}, {"primary_key": "1637647", "vector": [], "sparse_vector": [], "title": "Public Randomness Extraction with Ephemeral Roles and Worst-Case Corruptions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We distill a simple information-theoretic model for randomness extraction motivated by the task of generating publicly verifiable randomness in blockchain settings and which is closely related toYou-Only-Speak-Once (YOSO)protocols (CRYPTO 2021). With the goal of avoiding denial-of-service attacks, parties speak only once and in sequence by broadcasting a public value and forwarding secret values to future parties. Additionally, an unbounded adversary can corrupt any chosen subset of at mosttparties. In contrast, existing YOSO protocols only handle random corruptions. As a notable example, considering worst-case corruptions allows us to reduce trust in the role assignment mechanism, which is assumed to be perfectly random in YOSO. We study the maximum corruption thresholdtwhich allows for unconditional randomness extraction in our model: With respect to feasibility, we give protocols fortcorruptions and\\(n=6t+1\\)or\\(n=5t\\)parties depending on whether the adversary learns secret values forwarded to corrupted parties immediately once they are sent or only once the corrupted party is executed, respectively. Both settings are motivated by practical implementations of secret value forwarding. To design such protocols, we go beyond the committee-based approach that is sufficient for random corruptions in YOSO but turns out to be sub-optimal for chosen corruptions. To complement our protocols, we show that low-error randomness extraction is impossible with corruption thresholdtand\\(n\\le 4t\\)parties in both settings above. This also provides a separation between chosen and random corruptions, since the latter allows for randomness extraction with close ton/2 random corruptions.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_5"}, {"primary_key": "1637648", "vector": [], "sparse_vector": [], "title": "Rotational Differential-Linear Distinguishers of ARX Ciphers with Arbitrary Output Linear Masks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON><PERSON>", "<PERSON>"], "summary": "The rotational differential-linear attacks, proposed at EUROCRYPT 2021, is a generalization of differential-linear attacks by replacing the differential part of the attacks with rotational differentials. At EUROCRYPT 2021, <PERSON> et al. presented a method based on <PERSON><PERSON><PERSON> et al.’s technique (FSE 2013) for evaluating the rotational differential-linear correlations for the special cases where the output linear masks are unit vectors. With this method, some powerful (rotational) differential-linear distinguishers with output linear masks being unit vectors againstFRIET,Xoodoo, andAlzettewere discovered. However, how to compute the rotational differential-linear correlations for arbitrary output masks was left open. In this work, we partially solve this open problem by presenting an efficient algorithm for computing the (rotational) differential-linear correlation of modulo additions for arbitrary output linear masks, based on which a technique for evaluating the (rotational) differential-linear correlation of ARX ciphers is derived. We apply the technique toAlzette,SipHash,ChaCha, andSPECK. As a result, significantly improved (rotational) differential-linear distinguishers includingdeterministicones are identified. All results of this work are practical and experimentally verified to confirm the validity of our methods. In addition, we try to explain the experimental distinguishers employed in FSE 2008, FSE 2016, and CRYPTO 2020 againstChaCha. The predicted correlations are close to the experimental ones.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_1"}, {"primary_key": "1637649", "vector": [], "sparse_vector": [], "title": "A New Framework for More Efficient Round-Optimal Lattice-Based (Partially) Blind Signature via Trapdoor Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Blind signatures, proposed by <PERSON><PERSON> (CRYPTO’82), are interactive protocols between a signer and a user, where a user can obtain a signature without revealing the message to be signed. Recently, <PERSON><PERSON> et al. (EUROCRYPT’20) observed that all efficient lattice-based blind signatures following the blueprint of the original blind signature by <PERSON><PERSON><PERSON><PERSON> (ASIACRYPT’10) have a flawed security proof. This puts us in a situation where all known lattice-based blind signatures have at least two of the following drawbacks: heuristic security; 1 MB or more signature size; only supporting bounded polynomially many signatures, or being based on non-standard assumptions. In this work, we construct the firstround-optimal(i.e., two-round) lattice-based blind signature with a signature size roughly 100 KB that supports unbounded polynomially many signatures and is provably secure under standard assumptions. Even if we allow non-standard assumptions and more rounds, ours provide the shortest signature size while simultaneously supporting unbounded polynomially many signatures. The main idea of our work is revisiting the generic blind signature construction by <PERSON><PERSON><PERSON> (CRYPTO’06) and optimizing thecommit-then-openproof using techniques tailored to lattices. Our blind signature is also the first construction to have a formal security proof in thequantumrandom oracle model. Finally, our blind signature extends naturally topartiallyblind signatures, where the user and signer can include an agreed-upon public string in the message.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_11"}, {"primary_key": "1637650", "vector": [], "sparse_vector": [], "title": "Le Mans: Dynamic and Fluid MPC for Dishonest Majority.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Most MPC protocols require the set of parties to be active for the entire duration of the computation. Deploying MPC for use cases such as complex and resource-intensive scientific computations increases the barrier of entry for potential participants. The model of Fluid MPC (Crypto 2021) tackles this issue by giving parties the flexibility to participate in the protocol only when their resources are free. As such, the set of parties is dynamically changing over time. In this work, we extend Fluid MPC, which only considered an honest majority, to the setting where the majority of participants at any point in the computation may be corrupt. We do this by presenting variants of the SPDZ protocol, which support dynamic participants. Firstly, we describe auniversal preprocessingfor SPDZ, which allows a set ofnparties to compute some correlated randomness, such that later on, any subset of the parties can use this to take part in an online secure computation. We complement this with aDynamic SPDZonline phase, designed to work with our universal preprocessing, as well as a protocol for securely realising the preprocessing. Our preprocessing protocol is designed to efficiently use pseudorandom correlation generators, thus, the parties’ storage and communication costs can be almost independent of the function being evaluated. We then extend this to support afluid online phase, where the set of parties can dynamically evolve during the online phase. Our protocol achievesmaximal fluidityand security with abort, similarly to the previous, honest majority construction. Achieving this requires a careful design and techniques to guarantee a small state complexity, allowing us to switch between committees efficiently.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_25"}, {"primary_key": "1637651", "vector": [], "sparse_vector": [], "title": "Implicit White-Box Implementations: White-Boxing ARX Ciphers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Since the first white-box implementation of AES published twenty years ago, no significant progress has been made in the design of secure implementations against an attacker with full control of the device. Designing white-box implementations of existing block ciphers is a challenging problem, as all proposals have been broken. Only two white-box design strategies have been published this far: the CEJO framework, which can only be applied to ciphers with small S-boxes, and self-equivalence encodings, which were only applied to AES. In this work we propose implicit implementations, a new design of white-box implementations based on implicit functions, and we show that current generic attacks that break CEJO or self-equivalence implementations are not successful against implicit implementations. The generation and the security of implicit implementations are related to the self-equivalences of the non-linear layer of the cipher, and we propose a new method to obtain self-equivalences based on the CCZ-equivalence. We implemented this method and many other functionalities in a new open-source toolBoolCrypt, which we used to obtain for the first time affine, linear, and even quadratic self-equivalences of the permuted modular addition. Using the implicit framework and these self-equivalences, we describe for the first time a practical white-box implementation of a generic Addition-Rotation-XOR (ARX) cipher, and we provide an open-source tool to easily generate implicit implementations of ARX ciphers.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_2"}, {"primary_key": "1637652", "vector": [], "sparse_vector": [], "title": "Collision-Resistance from Multi-Collision-Resistance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Collision-resistant hash functions (\\(\\textsf{CRH}\\)) are a fundamental and ubiquitous cryptographic primitive. Several recent works have studied a relaxation of\\(\\textsf{CRH}\\)calledt-way multi-collision-resistant hash functions(\\(t\\text {-}\\textsf{MCRH}\\)). These are families of functions for which it is computationally hard to find at-way collision, even though such collisions are abundant (and even\\((t-1)\\)-way collisions may be easy to find). The case of\\(t=2\\)corresponds to standard\\(\\textsf{CRH}\\), but it is natural to studyt-\\(\\textsf{MCRH}\\)for larger values oft. Multi-collision-resistance seems to be a qualitatively weaker property than standard collision-resistance. Nevertheless, in this work we show anon-blackboxtransformation of any moderately shrinkingt-\\(\\textsf{MCRH}\\), for\\(t \\in \\{3,4\\}\\), into an (infinitely often secure)\\(\\textsf{CRH}\\). This transformation is non-constructive – we can prove the existence of a\\(\\textsf{CRH}\\)but cannot explicitly point out a construction. Our result partially extends to larger values oft. In particular, we show that for suitable values of\\(t>t'\\), we can transform at-\\(\\textsf{MCRH}\\)into a\\(t'\\)-\\(\\textsf{MCRH}\\), at the cost of reducing the shrinkage of the resulting hash function family and settling for infinitely often security. This result utilizes the list-decodability properties of Reed-Solomon codes.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_17"}, {"primary_key": "1637653", "vector": [], "sparse_vector": [], "title": "SoftSpokenOT: Quieter OT Extension from Small-Field Silent VOLE in the Minicrypt Model.", "authors": ["<PERSON>"], "summary": "Given a small number of base oblivious transfers (OTs), how does one generate a large number of extended OTs as efficiently as possible? The answer has long been the seminal work of IKNP (<PERSON><PERSON> et al., Crypto 2003) and the family of protocols it inspired, which only use Minicrypt assumptions. Recently, <PERSON> et al. (Crypto 2019) proposed the Silent-OT technique that improves on IKNP, but at the cost of a much stronger, non-Minicrypt assumption: the learning parity with noise (LPN) assumption. We present SoftSpokenOT, the first OT extension to improve on IKNP’s communication cost in the Minicrypt model. While IKNP requires security parameter\\(\\lambda \\)bits of communication for each OT, SoftSpokenOT only needs\\(\\lambda / k\\)bits, for anyk, at the expense of requiring\\(2^{k-1} / k\\)times the computation. For small values ofk, this tradeoff is favorable since IKNP-style protocols are network-bound. We implemented SoftSpokenOT and found that our protocol gives almost a\\(5{\\times }\\)speedup over IKNP in the LAN setting. Our technique is based on a novel silent protocol for vector oblivious linear evaluation (VOLE) over polynomial-sized fields. We created a framework to build maliciously secure\\(\\left( {\\begin{array}{c}N\\\\ 1\\end{array}}\\right) \\)-OT extension from this VOLE, revisiting and improving the existing work for each step. Along the way, we found several flaws in the existing work, including a practical attack against the consistency check of Patra et al. (NDSS 2017).", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_23"}, {"primary_key": "1637654", "vector": [], "sparse_vector": [], "title": "Accelerating the Delfs-Galbraith Algorithm with Fast Subfield Root Detection.", "authors": ["Maria <PERSON>-Real Santos", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a new algorithm for finding an isogeny from a given supersingular elliptic curve\\(E/\\mathbb {F}_{p^2}\\)to a subfield elliptic curve\\(E'/\\mathbb {F}_p\\), which is the bottleneck step of the <PERSON><PERSON>–G<PERSON>braith algorithm for the general supersingular isogeny problem. Our core ingredient is a novel method of rapidly determining whether a polynomial\\(f \\in L[X]\\)has any roots in a subfield\\(K \\subset L\\), while avoiding expensive root-finding algorithms. In the special case when\\(f=\\Upphi _{\\ell ,p}(X,j) \\in \\mathbb {F}_{p^2}[X]\\), i.e., whenfis the\\(\\ell \\)-th modular polynomial evaluated at a supersingularj-invariant, this provides a means of efficiently determining whether there is an\\(\\ell \\)-isogeny connecting the corresponding elliptic curve to a subfield curve. Together with the traditional Delfs–Galbraith walk, inspecting many\\(\\ell \\)-isogenous neighbours in this way allows us to search through a larger proportion of the supersingular set per unit of time. Though the asymptotic\\(\\tilde{O}(p^{1/2})\\)complexity of our improved algorithm remains unchanged from that of the original <PERSON><PERSON>–<PERSON>ith algorithm, our theoretical analysis and practical implementation both show a significant reduction in the runtime of the subfield search. This sheds new light on the concrete hardness of the general supersingular isogeny problem (i.e. the foundational problem underlying isogeny-based cryptography), and has immediate implications on the bit-security of schemes like B-SIDH and SQISign for which Delfs–Galbraith is the best known classical attack.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_10"}, {"primary_key": "1637655", "vector": [], "sparse_vector": [], "title": "Simplified MITM Modeling for Permutations: New (Quantum) Attacks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Meet-in-the-middle (MITM) is a general paradigm where internal states are computed along two independent paths (‘forwards’ and ‘backwards’) that are then matched. Over time, MITM attacks improved using more refined techniques and exploiting additional freedoms and structure, which makes it more involved to find and optimize such attacks. This has led to the use of detailed attack models for generic solvers to automatically search for improved attacks, notably a MILP model developed by <PERSON><PERSON> et al. at EUROCRYPT 2021. In this paper, we study a simpler MILP modeling combining a greatly reduced attack representation as input to the generic solver, together with a theoretical analysis that, for any solution, proves the existence and complexity of a detailed attack. This modeling allows to find both classical and quantum attacks on a broad class of cryptographic permutations. First,Present-like constructions, with the permutations from theSpongenthash functions: we improve the MITM step in distinguishers by up to 3 rounds. Second, AES-like designs: despite being much simpler than <PERSON><PERSON> et al.’s, our model allows to recover the best previous results. The only limitation is that we do not use degrees of freedom from the key schedule. Third, we show that the model can be extended to target more permutations, like Feistel networks. In this context we give new Guess-and-determine attacks on reducedSimpira v2andSparkle. Finally, using our model, we find several new quantum preimage and pseudo-preimage attacks (e.g.Haraka v2,Simpira v2...) targeting the same number of rounds as the classical attacks.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_24"}, {"primary_key": "1637656", "vector": [], "sparse_vector": [], "title": "Semi-quantum Tokenized Signatures.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Quantum tokenized signature schemes (<PERSON><PERSON><PERSON> and <PERSON>, QCrypt 2017) allow a sender to generate and distribute quantum unclonable states which grant their holder a one-time permission to sign in the name of the sender. Such schemes are a strengthening of public-key quantum money schemes, as they imply public-key quantum money where some channels of communication in the system can be made classical. An even stronger primitive is semi-quantum tokenized signatures, where the sender is classical and can delegate the generation of the token to a (possibly malicious) quantum receiver. Semi-quantum tokenized signature schemes imply a powerful version of public-key quantum money satisfying two key features: The bank is classical and the scheme can execute on a completely classical communication network. In addition, the bank isstatelessand after the creation of a banknote, does not hold any information nor trapdoors except the balance of accounts in the system. Such quantum money scheme solves the main open problem presented by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (AFT 2019). Furthermore, the classical-communication transactions between users in the system aredirectand do not need to go through the bank. This enables the transactions to be both classical and private. While fully-quantum tokenized signatures (where the sender is quantum and generates the token by itself) are known based on quantum-secure indistinguishability obfuscation and injective one-way functions, the semi-quantum version is not known under any computational assumption. In this work we construct a semi-quantum tokenized signature scheme based on quantum-secure indistinguishability obfuscation and the sub-exponential hardness of the Learning with Errors problem. In the process, we show new properties of quantum coset states and a new hardness result on indistinguishability obfuscation of classical subspace membership circuits.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_11"}, {"primary_key": "1637657", "vector": [], "sparse_vector": [], "title": "Candidate Witness Encryption from Lattice Techniques.", "authors": ["Rotem Tsabary"], "summary": "Witness encryption (WE), first introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> in [GGSW13], is an encryption scheme where messages are encrypted with respect to instances of an\\(\\textbf{NP}\\)relation, such that in order to decrypt one needs to know a valid witness for the instance that is associated with the ciphertext. Despite of significant efforts in the past decade to construct WE from standard assumptions, to the best of our knowledge all of the existing WE candidates either rely directly on iO or use techniques that also seem to imply iO in the same way that they seem to imply WE. In this work we propose a new hardness assumption with regard to lattice trapdoors and show a witness encryption candidate which is secure under it. Contrary to previous WE candidates, our technique is trivially broken when one tries to convert it to iO, which suggests that the security relies on a different mechanism. We view the gap between WE and iO as an analogue to the gap between ABE and FE and thus potentially significant. Intuitively, the assumption says that“the best an attacker can do with a trapdoor sample is to use it semi-honestly”– i.e. that LWE with respect to a public matrix\\(\\textbf{A}\\), given as auxiliary information a trapdoor sample\\(\\textbf{K}\\leftarrow \\textbf{A}^{\\textsf{TD}}(\\mathbf {{B}})\\), is as hard as LWE with respect to the public matrix\\([\\textbf{A}|\\mathbf {{B}}]\\)and no auxiliary information. In order to formally utilize the assumption we define a notion of LWE oracles with generic distributions of public matrices and auxiliary information. This model allows to bound the hardness of LWE with respect to one distribution as a function of the hardness of LWE with respect to another distribution. Repeated arguments of this flavor can be used as a sequence of hybrids in order to gradually change the challenge that an adversary is facing while keeping track on the security loss in each step of the proof. Typically security proofs of LWE-based systems implicitly make arguments of this flavor for distributions that are indistinguishable, while our model allows to make relaxed arguments that in some cases suffice for the proof requirements.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15802-5_19"}, {"primary_key": "1637658", "vector": [], "sparse_vector": [], "title": "Gossiping for Communication-Efficient Broadcast.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Byzantine \n\nBroadcast is crucial for many cryptographic protocols such as secret sharing, multiparty computation and blockchain consensus. In this paper we applygossiping(propagating a message by sending to a few random parties who in turn do the same, until the message is delivered) and propose new communication-efficient protocols, under dishonest majority, for Single-Sender Broadcast (BC) and Parallel Broadcast (PBC), improving the state-of-the-art in several ways. As our warm-up result, we present a randomized protocol for BC which achieves\\(O(n^2\\kappa ^2)\\)communication complexity from plain public key setup assumptions. This is the first protocol with subcubic communication in this setting, but operates only against static adversaries. Using ideas from our BC protocol, we move to our central contribution and present two protocols for PBC that are secure against adaptive adversaries. To the best of our knowledge we are the first to study PBCspecifically: All previous approaches for Parallel Broadcast naively runninstances of single-sender Broadcast, increasing the communication complexity by an undesirable factor ofn. Our insight of avoiding black-box invocations of BC is particularly crucial for achieving our asymptotic improvements. In particular: Our first PBC protocol achieves\\(\\tilde{O}(n^3\\kappa ^2)\\)communication complexity and relies only on plain public key setup assumptions. Our second PBC protocol uses trusted setup and achieves nearly optimal communication complexity\\(\\tilde{O}(n^2\\kappa ^4)\\). Both PBC protocols yield an almost linear improvement over the best known solutions involvingnparallel invocations of the respective BC protocols such as those of Dolev and Strong (SIAM Journal on Computing, 1983) and Chan et al. (Public Key Cryptography, 2020). Central to our PBC protocols is a new problem that we define and solve, which we name “Converge”. In Converge, parties must run an adaptively-secure andefficientprotocol such that by the end of the protocol, all honest parties that remain possess a superset of the union of the initial honest parties’ inputs.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_15"}, {"primary_key": "1637659", "vector": [], "sparse_vector": [], "title": "Batch Arguments for sfNP and More from Standard Bilinear Group Assumptions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Non-interactive batch arguments for\\(\\textsf{NP}\\)provide a way to amortize the cost of\\(\\textsf{NP}\\)verification across multiple instances. They enable a prover to convince a verifier of multiple\\(\\textsf{NP}\\)statements with communication much smaller than the total witness length and verification time much smaller than individually checking each instance. In this work, we give the first construction of a non-interactive batch argument for\\(\\textsf{NP}\\)from standard assumptions on groups with bilinear maps (specifically, from either the subgroup decision assumption in composite-order groups or from the\\(k\\)-\\(\\textsf{Lin}\\)assumption in prime-order groups for any\\(k \\ge 1\\)). Previously, batch arguments for\\(\\textsf{NP}\\)were only known from\\(\\textsf{LWE}\\), or a combination of multiple assumptions, or from non-standard/non-falsifiable assumptions. Moreover, our work introduces a newdirectapproach for batch verification and avoids heavy tools like correlation-intractable hash functions or probabilistically-checkable proofs common to previous approaches. As corollaries to our main construction, we obtain the first publicly-verifiable non-interactive delegation scheme for RAM programs (i.e., a succinct non-interactive argument (SNARG) for\\(\\textsf{P}\\)) with a CRS of sublinear size (in the running time of the RAM program), as well as the first aggregate signature scheme (supporting bounded aggregation) from standard assumptions on bilinear maps.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_15"}, {"primary_key": "1637660", "vector": [], "sparse_vector": [], "title": "Orion: Zero Knowledge Proof with Linear Prover Time.", "authors": ["Tiancheng Xie", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Zero-knowledge proof is a powerful cryptographic primitive that has found various applications in the real world. However, existing schemes with succinct proof size suffer from a high overhead on the proof generation time that is super-linear in the size of the statement represented as an arithmetic circuit, limiting their efficiency and scalability in practice. In this paper, we presentOrion, a new zero-knowledge argument system that achievesO(N) prover time of field operations and hash functions and\\(O(\\log ^2 N)\\)proof size.Orionis concretely efficient and our implementation shows that the prover time is 3.09 s and the proof size is 1.5 MB for a circuit with\\(2^{20}\\)multiplication gates. The prover time is the fastest among all existing succinct proof systems, and the proof size is an order of magnitude smaller than a recent scheme proposed in <PERSON><PERSON>ne<PERSON> et al. 2021. In particular, we develop two new techniques leading to the efficiency improvement. (1) We propose a new algorithm to test whether a random bipartite graph is a lossless expander graph or not based on the densest subgraph algorithm. It allows us to sample lossless expanders with an overwhelming probability. The technique improves the efficiency and/or security of all existing zero-knowledge argument schemes with a linear prover time. The testing algorithm based on densest subgraph may be of independent interest for other applications of expander graphs. (2) We develop an efficient proof composition scheme, code switching, to reduce the proof size from square root to polylogarithmic in the size of the computation. The scheme is built on the encoding circuit of a linear code and shows that the witness of a second zero-knowledge argument is the same as the message in the linear code. The proof composition only introduces a small overhead on the prover time.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15985-5_11"}, {"primary_key": "1637661", "vector": [], "sparse_vector": [], "title": "Public-Key Watermarking Schemes for Pseudorandom Functions.", "authors": ["Rupeng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A software watermarking scheme can embed a message into a program while preserving its functionality. The embedded message can be extracted later by an extraction algorithm, and no one could remove it without significantly changing the functionality of the program. A watermarking scheme is public key if neither the marking procedure nor the extraction procedure needs a watermarking secret key. Prior constructions of watermarking schemes mainly focus on watermarking pseudorandom functions (PRFs), and the major open problem in this direction is to construct a public-key watermarkable PRF. In this work, we solve the open problem via constructing public-key watermarkable PRFs with different trade-offs from various assumptions, ranging from standard lattice assumptions to the existence of indistinguishability obfuscation. To achieve the results, we first construct watermarking schemes in a weaker model, where the extraction algorithm is provided with a “hint” about the watermarked PRF key. Then we upgrade the constructions to standard watermarking schemes using a robust unobfuscatable PRF. We also provide the first construction of robust unobfuscatable PRF in this work, which is of independent interest.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15979-4_22"}, {"primary_key": "1637662", "vector": [], "sparse_vector": [], "title": "Augmented Random Oracles.", "authors": ["<PERSON>"], "summary": "We propose a new paradigm for justifying the security of random oracle-based protocols, which we call the Augmented Random Oracle Model (AROM). We show that the AROM captures a wide range of important random oracle impossibility results. Thus a proof in the AROM implies some resiliency to such impossibilities. We then consider three ROM transforms which are subject to impossibilities: Fiat-Shamir (FS), Fujisaki-Okamoto (FO), and Encrypt-with-Hash (EwH). We show in each case how to obtain security in the AROM by strengthening the building blocks or modifying the transform. Along the way, we give a couple other results. We improve the assumptions needed for the FO and EwH impossibilities from indistinguishability obfuscation to circularly secure LWE; we argue that our AROM still captures this improved impossibility. We also demonstrate that there is no “best possible” hash function, by giving a pair of security properties, both of which can be instantiated in the standard model separately, which cannot be simultaneously satisfied by a single hash function.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_2"}, {"primary_key": "1637663", "vector": [], "sparse_vector": [], "title": "To Label, or Not To Label (in Generic Groups).", "authors": ["<PERSON>"], "summary": "Generic groups are an important tool for analyzing the feasibility and in-feasibility of group-based cryptosystems. There are two distinct wide-spread versions of generic groups, <PERSON><PERSON><PERSON>’s and Ma<PERSON>’s, the main difference being whether or not group elements are given explicit labels. The two models are often treated as equivalent. In this work, however, we demonstrate that the models are in fact quite different, and care is needed when stating generic group results: We show that numerous textbook constructions arenotcaptured by <PERSON><PERSON>, but are captured by <PERSON><PERSON><PERSON>. In the other direction, any construction captured by <PERSON><PERSON>iscaptured by <PERSON><PERSON><PERSON>. For constructions that exist in both models, we show that security is equivalent for “single stage” games, but Shoup security is strictly stronger than Maurer security for some “multi-stage” games. The existing generic group un-instantiability results do not apply to <PERSON><PERSON>. We fill this gap with a new un-instantiability result. We explain how the known black box separations between generic groups and identity-based encryption do not fully apply to <PERSON><PERSON><PERSON>, and resolve this by providing such a separation. We give a new un-instantiability result for thealgebraicgroup model.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_3"}, {"primary_key": "1637664", "vector": [], "sparse_vector": [], "title": "New Constructions of Collapsing Hashes.", "authors": ["<PERSON>"], "summary": "Collapsing is a post-quantum strengthening of collision resistance, needed to lift many classical results to the quantum setting. Unfortunately, the only existing standard-model proofs of collapsing hashes require LWE. We construct the first collapsing hashes from the quantum hardness of any one of the following problems: LPN in a variety of low noise or high-hardness regimes, essentially matching what is known for collision resistance from LPN. Finding cycles on exponentially-large expander graphs, such as those arising from isogenies on elliptic curves. The “optimal” hardness of finding collisions inanyhash function. Thepolynomialhardness of finding collisions, assuming a certain plausible regularity condition on the hash. As an immediate corollary, we obtain the first statistically hiding post-quantum commitments and post-quantum succinct arguments (of knowledge) under the same assumptions. Our results are obtained by a general theorem which shows how to construct a collapsing hash\\(H'\\)from a post-quantum collision-resistant hash functionH, regardless of whether or not<PERSON><PERSON><PERSON> is collapsing, assumingHsatisfies a certain regularity condition we call “semi-regularity”.", "published": "2022-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-15982-4_20"}]