[{"primary_key": "2580253", "vector": [], "sparse_vector": [], "title": "On a Generalization of Substitution-Permutation Networks: The HADES Design Strategy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Keyed and unkeyed cryptographic permutations often iterate simple round functions. Substitution-permutation networks (SPNs) are an approach that is popular since the mid 1990s. One of the new directions in the design of these round functions is to reduce the substitution (S-Box) layer from a full one to a partial one, uniformly distributed over all the rounds. LowMC and Zorro are examples of this approach. A relevant freedom in the design space is to allow for a highly non-uniform distribution of S-Boxes. However, choosing rounds that are so different from each other is very rarely done, as it makes security analysis and implementation much harder. We develop the design strategyHadesand an analysis framework for it, which despite this increased complexity allows for security arguments against many classes of attacks, similar to earlier simpler SPNs. The framework builds upon the wide trail design strategy, and it additionally allows for security arguments against algebraic attacks, which are much more of a concern when algebraically simple S-Boxes are used. Subsequently, this is put into practice by concrete instances and benchmarks for a use case that generally benefits from a smaller number of S-Boxes and showcases the diversity of design options we support: A candidate cipher natively working with objects in\\(\\text {GF}(p)\\), for securing data transfers with distributed databases using secure multiparty computation (MPC). Compared to the currently fastest design MiMC, we observe significant improvements in online bandwidth requirements and throughput with a simultaneous reduction of preprocessing effort, while having a comparable online latency.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_23"}, {"primary_key": "2580254", "vector": [], "sparse_vector": [], "title": "Lightweight Authenticated Encryption Mode Suitable for Threshold Implementation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes tweakable block cipher (TBC) based modes\\(\\mathsf {PFB\\_Plus}\\)and\\(\\mathsf {PFB}\\omega \\)that are efficient in threshold implementations (TI). Lettbe an algebraic degree of a target function, e.g.\\(t=1\\)(resp.\\(t>1\\)) for linear (resp. non-linear) function. Thed-th order TI encodes the internal state into\\(d t + 1\\)shares. Hence, the area size increases proportionally to the number of shares. This implies that TBC based modes can be smaller than block cipher (BC) based modes in TI because TBC requiress-bit block to ensures-bit security, e.g.PFBandRomulus, while BC requires 2s-bit block. However, even with those TBC based modes, the minimum we can reach is 3 shares ofs-bit state with\\(t=2\\)and the first-order TI (\\(d=1\\)). Our first design\\(\\mathsf {PFB\\_Plus}\\)aims to break the barrier of the 3s-bit state in TI. The block size of an underlying TBC iss/2 bits and the output of TBC is linearly expanded tosbits. This expanded state requires only 2 shares in the first-order TI, which makes the total state size 2.5sbits. We also provide rigorous security proof of\\(\\mathsf {PFB\\_Plus}\\). Our second design\\(\\mathsf {PFB}\\omega \\)further increases a parameter\\(\\omega \\): a ratio of the security levelsto the block size of an underlying TBC. We prove security of\\(\\mathsf {PFB}\\omega \\)for any\\(\\omega \\)under some assumptions for an underlying TBC and for parameters used to update a state. Next, we show a concrete instantiation of\\(\\mathsf {PFB\\_Plus}\\)for 128-bit security. It requires a TBC with 64-bit block, 128-bit key and 128-bit tweak, while no existing TBC can support it. We design a new TBC by extendingSKINNYand provide basic security evaluation. Finally, we give hardware benchmarks of\\(\\mathsf {PFB\\_Plus}\\)in the first-order TI to show that TI of\\(\\mathsf {PFB\\_Plus}\\)is smaller than that ofPFBby more than one thousand gates and is the smallest within the schemes having 128-bit security.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_24"}, {"primary_key": "2580255", "vector": [], "sparse_vector": [], "title": "Double-Base Chains for Scalar Multiplications on Elliptic Curves.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Bao Li"], "summary": "Double-base chains (DBCs) are widely used to speed up scalar multiplications on elliptic curves. We present three results of DBCs. First, we display a structure of the set containing all DBCs and propose an iterative algorithm to compute the number of DBCs for a positive integer. This is the first polynomial time algorithm to compute the number of DBCs for positive integers. Secondly, we present an asymptotic lower bound on average Hamming weights of DBCs\\(\\frac{\\log n}{8.25}\\)for a positive integern. This result answers an open question about the Hamming weights of DBCs. Thirdly, we propose a new algorithm to generate an optimal DBC for any positive integer. The time complexity of this algorithm is\\(\\mathscr {O}\\left( \\left( \\log n\\right) ^2 \\log \\log n\\right) \\)bit operations and the space complexity is\\(\\mathscr {O}\\left( \\left( \\log n\\right) ^{2}\\right) \\)bits of memory. This algorithm accelerates the recoding procedure by more than 6 times compared to the state-of-the-art Bernstein, Chu<PERSON><PERSON><PERSON><PERSON>, and <PERSON>’s work. The Hamming weights of optimal DBCs are over 60% smaller than those of NAFs. Scalar multiplication using our optimal DBC is about 13% faster than that using non-adjacent form on elliptic curves over large prime fields.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_18"}, {"primary_key": "2580256", "vector": [], "sparse_vector": [], "title": "How to Extract Useful Randomness from Unreliable Sources.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "For more than 30 years, cryptographers have been looking for public sources of uniform randomness in order to use them as a set-up to run appealing cryptographic protocols without relying on trusted third parties. Unfortunately, nowadays it is fair to assess that assuming the existence of physical phenomena producing public uniform randomness is far from reality. It is known that uniform randomness cannot be extracted from a single weak source. A well-studied way to overcome this is to consider several independent weak sources. However, this means we must trust the various sampling processes of weak randomness from physical processes. Motivated by the above state of affairs, this work considers a set-up where players can access multiplepotentialsources of weak randomness, several of which may be jointly corrupted by a computationally unbounded adversary. We introduceSHELA(Somewhere Honest Entropic Look Ahead) sources to model this situation. We show that there is no hope of extracting uniform randomness from aSHELAsource. Instead, we focus on the task ofSomewhere-Extraction(i.e., outputting several candidate strings, some of which are uniformly distributed – yet we do not know which). We give explicit constructions ofSomewhere-ExtractorsforSHELAsources with good parameters. Then, we present applications of the above somewhere-extractor where the public uniform randomness can be replaced by the output of such extraction from corruptible sources, greatly outperforming trivial solutions. The output of somewhere-extraction is also useful in other settings, such as a suitable source of random coins for many randomized algorithms. In another front, we comprehensively study the problem ofSomewhere-Extractionfrom aweaksource, resulting in a series of bounds. Our bounds highlight the fact that, in most regimes of parameters (including those relevant for applications),SHELAsources significantly outperformweaksources of comparable parameters both when it comes to the process ofSomewhere-Extraction, and in the task of amplification of success probability in randomized algorithms. Moreover, the low quality of somewhere-extraction from weak sources excludes its use in various efficient applications.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_13"}, {"primary_key": "2580257", "vector": [], "sparse_vector": [], "title": "Optimal Broadcast Encryption from Pairings and LWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON> and <PERSON> (CRYPTO 2014) used multilinear maps to provide a solution to the long-standing problem of public-key broadcast encryption (BE) where all parameters in the system are small. In this work, we improve their result by providing a solution that uses onlybilinearmaps and Learning With Errors (LWE). Our scheme is fully collusion-resistant against any number of colluders, and can be generalized to an identity-based broadcast system with short parameters. Thus, we reclaim the problem of optimal broadcast encryption from the land of “Obfustopia”. Our main technical contribution is a ciphertext policy attribute based encryption (CP-ABE) scheme which achieves special efficiency properties – its ciphertext size, secret key size, and public key size are all independent of the size of the circuits supported by the scheme. We show that this special CP-ABE scheme implies BE with optimal parameters; but it may also be of independent interest. Our constructions rely on a novel interplay of bilinear maps and LWE, and are proven secure in the generic group model.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_2"}, {"primary_key": "2580258", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation Without Maps: Attacks and Fixes for Noisy Linear FE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Candidates ofIndistinguishability Obfuscation(\\(\\mathsf {iO}\\)) can be categorized as “direct” or “bootstrapping based”. Direct constructions rely on high degree multilinear maps [28,29] and provide heuristic guarantees, while bootstrapping based constructions [2,7,33,36,38,39] rely, in the best case, onbilinearmaps as well as new variants of the Learning With Errors (\\(\\mathsf {LWE}\\)) assumption and pseudorandom generators. Recent times have seen exciting progress in the construction of indistinguishability obfuscation (\\(\\mathsf {iO}\\)) from bilinear maps (along with other assumptions) [2,7,33,38]. As a notable exception, a recent work by Agrawal [2] provided a construction for\\(\\mathsf {iO}\\)without usinganymaps. This work identified a new primitive, calledNoisy Linear Functional Encryption(\\(\\mathsf {NLinFE}\\)) that provably suffices for\\(\\mathsf {iO}\\)and gave a direct construction of\\(\\mathsf {NLinFE}\\)from new assumptions on lattices. While a preliminary cryptanalysis for the new assumptions was provided in the original work, the author admitted the necessity of performing significantly more cryptanalysis before faith could be placed in the security of the scheme. Moreover, the author did not suggest concrete parameters for the construction. In this work, we fill this gap by undertaking the task of thorough cryptanalytic study of\\(\\mathsf {NLinFE}\\). We design two attacks that let the adversary completely break the security of the scheme. Our attacks are completely new and unrelated to attacks that were hitherto used to break other candidates of\\(\\mathsf {iO}\\). To achieve this, we develop new cryptanalytic techniques which (we hope) will inform future designs of the primitive of\\(\\mathsf {NLinFE}\\). From the knowledge gained by our cryptanalytic study, we suggest modifications to the scheme. We provide a new scheme which overcomes the vulnerabilities identified before. We also provide a thorough analysis of all the security aspects of this scheme and argue why plausible attacks do not work. We additionally provide concrete parameters with which the scheme may be instantiated. We believe the security of\\(\\mathsf {NLinFE}\\)stands on significantly firmer footing as a result of this work.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_5"}, {"primary_key": "2580259", "vector": [], "sparse_vector": [], "title": "On Instantiating the Algebraic Group Model from Falsifiable Assumptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide a standard-model implementation (of a relaxation) of the algebraic group model (<PERSON><PERSON>, [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, CRYPTO 2018]). Specifically, we show that every algorithm that uses our group is algebraic, and hence “must know” a representation of its output group elements in terms of its input group elements. Here, “must know” means that a suitable extractor can extract such a representation efficiently. We stress that our implementation relies only on falsifiable assumptions in the standard model, and in particular does not use any knowledge assumptions. As a consequence, our group allows to transport a number of results obtained in the AGM into the standard model, under falsifiable assumptions. For instance, we show that in our group, several Diffie-Hellman-like assumptions (including computational <PERSON><PERSON><PERSON><PERSON><PERSON>) are equivalent to the discrete logarithm assumption. Furthermore, we show that our group allows to prove the <PERSON>hnorr signature scheme tightly secure in the random oracle model. Our construction relies on indistinguishability obfuscation, and hence should not be considered as a practical group itself. However, our results show that the AGM is a realistic computational model (since it can be instantiated in the standard model), and that results obtained in the AGM are also possible with standard-model groups.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_4"}, {"primary_key": "2580260", "vector": [], "sparse_vector": [], "title": "Quantum-Access-Secure Message Authentication via Blind-Unforgeability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Formulating and designing authentication of classical messages in the presence of adversaries with quantum query access has been a longstanding challenge, as the familiar classical notions of unforgeability do not directly translate into meaningful notions in the quantum setting. A particular difficulty is how to fairly capture the notion of “predicting an unqueried value” when the adversary can query in quantum superposition. We propose a natural definition of unforgeability against quantum adversaries calledblind unforgeability. This notion defines a function to be predictable if there exists an adversary who can use “partially blinded” oracle access to predict values in the blinded region. We support the proposal with a number of technical results. We begin by establishing that the notion coincides with EUF-CMA in the classical setting and go on to demonstrate that the notion is satisfied by a number of simple guiding examples, such as random functions and quantum-query-secure pseudorandom functions. We then show the suitability of blind unforgeability for supporting canonical constructions and reductions. We prove that the “hash-and-MAC” paradigm and the Lamport one-time digital signature scheme are indeed unforgeable according to the definition. To support our analysis, we additionally define and study a new variety of quantum-secure hash functions calledBernoulli-preserving. Finally, we demonstrate that blind unforgeability is strictly stronger than a previous definition of Boneh and Zhandry [EUROCRYPT ’13, CRYPTO ’13] and resolve an open problem concerning this previous definition by constructing an explicit function family which is forgeable yet satisfies the definition.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_27"}, {"primary_key": "2580261", "vector": [], "sparse_vector": [], "title": "Efficient Simulation of Random States and Random Unitaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of efficiently simulating random quantum states and random unitary operators, in a manner which is convincing to unbounded adversaries with black-box oracle access. This problem has previously only been considered for restricted adversaries. Against adversaries with an a priori bound on the number of queries, it is well-known thatt-designs suffice. Against polynomial-time adversaries, one can use pseudorandom states (PRS) and pseudorandom unitaries (PRU), as defined in a recent work of <PERSON>, <PERSON>, and <PERSON>; unfortunately, no provably secure construction is known for PRUs. In our setting, we are concerned with unbounded adversaries. Nonetheless, we are able to give stateful quantum algorithms which simulate the ideal object in both settings of interest. In the case of Haar-random states, our simulator is polynomial-time, has negligible error, and can also simulate verification and reflection through the simulated state. This yields an immediate application to quantum money: a money scheme which is information-theoretically unforgeable and untraceable. In the case of Haar-random unitaries, our simulator takes polynomial space, but simulates both forward and inverse access with zero error. These results can be seen as the first significant steps in developing a theory of lazy sampling for random quantum objects.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_26"}, {"primary_key": "2580262", "vector": [], "sparse_vector": [], "title": "Security of Hedged Fiat-Shamir Signatures Under Fault Attacks.", "authors": ["Diego F. <PERSON>a", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deterministic generation of per-signature randomness has been a widely accepted solution to mitigate the catastrophic risk of randomness failure in Fiat–Shamir type signature schemes. However, recent studies have practically demonstrated that such de-randomized schemes, including EdDSA, are vulnerable to differential fault attacks, which enable adversaries to recover the entire secret signing key, by artificially provoking randomness reuse or corrupting computation in other ways. In order to balance concerns of both randomness failures and the threat of fault injection, some signature designs are advocating a “hedged” derivation of the per-signature randomness, by hashing the secret key, message, and a nonce. Despite the growing popularity of the hedged paradigm in practical signature schemes, to the best of our knowledge, there has been no attempt to formally analyze the fault resilience of hedged signatures. We perform a formal security analysis of the fault resilience of signature schemes constructed via the Fiat–Shamir transform. We propose a model to characterize bit-tampering fault attacks, and investigate their impact across different steps of the signing operation. We prove that, for some types of faults, attacks are mitigated by the hedged paradigm, while attacks remain possible for others. As concrete case studies, we then apply our results to XEdDSA, a hedged version of EdDSA used in the Signal messaging protocol, and to Picnic2, a hedged Fiat–Shamir signature scheme in Round 2 of the NIST Post-Quantum standardization process.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_23"}, {"primary_key": "2580263", "vector": [], "sparse_vector": [], "title": "OptORAMa: Optimal Oblivious RAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Oblivious RAM (ORAM), first introduced in the ground-breaking work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC ’87 and J. ACM ’96) is a technique for provably obfuscating programs’ access patterns, such that the access patterns leak no information about the programs’ secret inputs. To compile a general program to an oblivious counterpart, it is well-known that\\(\\varOmega (\\log N)\\)amortized blowup is necessary, whereNis the size of the logical memory. This was shown in Gold<PERSON>ich and <PERSON><PERSON><PERSON><PERSON><PERSON>’s original ORAM work for statistical security and in a somewhat restricted model (the so calledballs-and-binsmodel), and recently by <PERSON> and <PERSON> (CRYPTO ’18) for computational security. A long standing open question is whether there exists anoptimalORAM construction that matches the aforementioned logarithmic lower bounds (without making large memory word assumptions, and assuming a constant number of CPU registers). In this paper, we resolve this problem and present the first secure ORAM with\\(O(\\log N)\\)amortized blowup, assuming one-way functions. Our result is inspired by and non-trivially improves on the recent beautiful work of <PERSON> et al. (FOCS ’18) who gave a construction with\\(O(\\log N\\cdot \\log \\log N)\\)amortized blowup, assuming one-way functions. One of our building blocks of independent interest is a linear-time deterministic oblivious algorithm for tight compaction: Given an array ofnelements where some elements are marked, we permute the elements in the array so that all marked elements end up in the front of the array. OurO(n) algorithm improves the previously best known deterministic or randomized algorithms whose running time is\\(O(n \\cdot \\log n)\\)or\\(O(n \\cdot \\log \\log n)\\), respectively.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_14"}, {"primary_key": "2580264", "vector": [], "sparse_vector": [], "title": "Everybody&apos;s a Target: Scalability in Public-Key Encryption.", "authors": ["Benedik<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For\\(1\\le m \\le n\\), we consider a naturalm-out-of-nmulti-instance scenario for a public-key encryption (PKE) scheme. An adversary, givennindependent instances of PKE, wins if he breaks at leastmout of theninstances. In this work, we are interested in thescaling factorof PKE schemes,\\(\\mathrm {SF}\\), which measures how well the difficulty of breakingmout of theninstances scales inm. That is, a scaling factor\\(\\mathrm {SF}=\\ell \\)indicates that breakingmout ofninstances is at least\\(\\ell \\)times more difficult than breaking one single instance. A PKE scheme with small scaling factor hence provides an ideal target for mass surveillance. In fact, the Logjam attack (CCS 2015) implicitly exploited, among other things, an almost constant scaling factor of ElGamal over finite fields (with shared group parameters). For Hashed ElGamal over elliptic curves, we use the generic group model to describe how the scaling factor depends on the scheme’s granularity. In low granularity, meaning each public key contains its independent group parameter, the scheme has optimal scaling factor\\(\\mathrm {SF}=m\\); In medium and high granularity, meaning all public keys share the same group parameter, the scheme still has a reasonable scaling factor\\(\\mathrm {SF}=\\sqrt{m}\\). Our findings underline that instantiating ElGamal over elliptic curves should be preferred to finite fields in a multi-instance scenario. As our main technical contribution, we derive new generic-group lower bounds of\\(\\varOmega (\\sqrt{m p})\\)on the complexity of both them-out-of-nGap Discrete Logarithm and them-out-of-nGap Computational Diffie-Hellman problem over groups of prime orderp, extending a recent result by Yun (EUROCRYPT 2015). We establish the lower bound by studying the hardness of a related computational problem which we call the search-by-hypersurface problem.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_16"}, {"primary_key": "2580265", "vector": [], "sparse_vector": [], "title": "Statistical ZAP Arguments.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON>or (FOCS’00) first introduced and constructed two message public coin witness indistinguishable proofs (ZAPs) for NP based on trapdoor permutations. Since then, ZAPs have also been obtained based on the decisional linear assumption on bilinear maps, and indistinguishability obfuscation, and have proven extremely useful in the design of several cryptographic primitives. However, all known constructions of two-message public coin (or even publicly verifiable) proof systems only guarantee witness indistinguishability against computationally bounded verifiers. In this paper, we construct the first public coin two message witness indistinguishable (WI) arguments for NP withstatisticalprivacy, assuming quasi-polynomial hardness of the learning with errors (LWE) assumption. We also show that the same protocol has a super-polynomial simulator (SPS), which yields the first public-coin SPS statistical zero knowledge argument. Prior to this, there were no known constructions of two-message publicly verifiable WI protocols under lattice assumptions, even satisfying the weaker notion of computational witness indistinguishability.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_22"}, {"primary_key": "2580266", "vector": [], "sparse_vector": [], "title": "TNT: How to Tweak a Block Cipher.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we proposeTweak-aNd-Tweak(\\(\\mathsf {TNT}\\)for short) mode, which builds a tweakable block cipher from three independent block ciphers.\\(\\mathsf {TNT}\\)handles the tweak input by simply XOR-ing the unmodified tweak into the internal state of block ciphers twice. Due to its simplicity,\\(\\mathsf {TNT}\\)can also be viewed as a way of turning a block cipher into a tweakable block cipher by dividing the block cipher into three chunks, and adding the tweak at the two cutting points only.\\(\\mathsf {TNT}\\)is proven to be of beyond-birthday-bound\\(2^{2n/3}\\)security, under the assumption that the three chunks are independent securen-bit SPRPs. It clearly brings minimum possible overhead to both software and hardware implementations. To demonstrate this, an instantiation namedTNT-AESwith\\(6 \\),\\(6 \\),\\(6 \\)rounds ofAESas the underlying block ciphers is proposed. Besides the inherent proven security bound and tweak-independent rekeying feature of the\\(\\mathsf {TNT}\\)mode, the performance ofTNT-AESis comparable with all existing TBCs designed through modular methods.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_22"}, {"primary_key": "2580267", "vector": [], "sparse_vector": [], "title": "An Algebraic Attack on Rank Metric Code-Based Cryptosystems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Maxime Bros", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Rank metric decoding problem is the main problem considered in cryptography based on codes in the rank metric. Very efficient schemes based on this problem or quasi-cyclic versions of it have been proposed recently, such as those in the submissions ROLLO and RQC currently at the second round of the NIST Post-Quantum Cryptography Standardization Process. While combinatorial attacks on this problem have been extensively studied and seem now well understood, the situation is not as satisfactory for algebraic attacks, for which previous work essentially suggested that they were ineffective for cryptographic parameters. In this paper, starting from <PERSON><PERSON><PERSON> and <PERSON><PERSON>’s algebraic modelling of the problem into a system of polynomial equations, we show how to augment this system with easily computed equations so that the augmented system is solved much faster via Gr<PERSON>bner bases. This happens because the augmented system has solving degreer,\\(r+1\\)or\\(r+2\\)depending on the parameters, whereris the rank weight, which we show by extending results from <PERSON>erbelet al.(PQCrypto 2019) on systems arising from the MinRank problem; with target rankr, <PERSON><PERSON><PERSON><PERSON> al.lower the solving degree to\\(r+2\\), and even less for some favorable instances that they call “superdetermined”. We give complexity bounds for this approach as well as practical timings of an implementation usingmagma. This improves upon the previously known complexity estimates for both <PERSON><PERSON><PERSON><PERSON> basis and (non-quantum) combinatorial approaches, and for example leads to an attack in 200 bits on ROLLO-I-256 whose claimed security was 256 bits.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_3"}, {"primary_key": "2580268", "vector": [], "sparse_vector": [], "title": "Evolving Ramp Secret Sharing with a Small Gap.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Evolving secret-sharing schemes, introduced by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (TCC 2016b), are secret-sharing schemes in which there is no a-priory upper bound on the number of parties that will participate. The parties arrive one by one and when a party arrives the dealer gives it a share; the dealer cannot update this share when other parties arrive. Motivated by the fact that when the number of parties is known, ramp secret-sharing schemes are more efficient than threshold secret-sharing schemes, we study evolving ramp secret-sharing schemes. Specifically, we study evolving (b(j),g(j))-ramp secret-sharing schemes, where\\(g,b: \\mathbb {N}\\rightarrow \\mathbb {N}\\)are non-decreasing functions. In such schemes, any set of parties that for somejcontainsg(j) parties from the first parties that arrive can reconstruct the secret, and any set such that for everyjcontains less thanb(j) parties from the firstjparties that arrive cannot learn any information about the secret. We focus on the case that the gap is small, namely\\(g(j)-b(j)=j^{\\beta }\\)for\\(0<\\beta <1\\). We show that there is an evolving ramp secret-sharing scheme with gap\\(t^{\\beta }\\), in which the share size of thej-th party is\\(\\tilde{O}(j^{4-\\frac{1}{\\log ^2 {1/\\beta }}})\\). Furthermore, we show that our construction results in much better share size for fixed values of\\(\\beta \\), i.e., there is an evolving ramp secret-sharing scheme with gap\\(\\sqrt{j}\\), in which the share size of thej-th party is\\(\\tilde{O}(j)\\). Our construction should be compared to the best known evolvingg(j)-threshold secret-sharing schemes (i.e., when\\(b(j)=g(j)-1\\)) in which the share size of thej-th party is\\(\\tilde{O}(j^4)\\). Thus, our construction offers a significant improvement for every constant\\(\\beta \\), showing that allowing a gap between the sizes of the authorized and unauthorized sets can reduce the share size. In addition, we present an evolving (k/2,k)-ramp secret-sharing scheme for a constantk(which can be very big), where any set of parties of size at leastkcan reconstruct the secret and any set of parties of size at mostk/2 cannot learn any information about the secret. The share size of thej-th party in our construction is\\(O(\\log k\\log j)\\). This is an improvement over the best known evolvingk-threshold secret-sharing schemes in which the share size of thej-th party is\\(O(k\\log j)\\).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_19"}, {"primary_key": "2580269", "vector": [], "sparse_vector": [], "title": "Tornado: Automatic Generation of Probing-Secure Masked Bitsliced Implementations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptographic implementations deployed in real world devices often aim at (provable) security against the powerful class of side-channel attacks while keeping reasonable performances. Last year at Asiacrypt, a new formal verification tool namedtightPROVEwas put forward to exactly determine whether a masked implementation is secure in the well-deployed probing security model for any given security ordert. Also recently, a compiler named<PERSON><PERSON><PERSON><PERSON> proposed to automatically generate bitsliced implementations of cryptographic primitives. This paper goes one step further in the security and performances achievements with a new automatic tool namedTornado. In a nutshell, from the high-level description of a cryptographic primitive,Tornadoproduces a functionally equivalent bitsliced masked implementation at any desired order proven secure in the probing model, but additionally in the so-calledregister probing modelwhich much better fits the reality of software implementations. This framework is obtained by the integration ofUsubawith\\(\\mathsf {tightPROVE}^+\\), which extendstightPROVEwith the ability to verify the security of implementations in the register probing model and to fix them with inserting refresh gadgets at carefully chosen locations accordingly. We demonstrateTornadoon the lightweight cryptographic primitives selected to the second round of the NIST competition and which somehow claimed to be masking friendly. It advantageously displays performances of the resulting masked implementations for several masking orders and prove their security in the register probing model.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_11"}, {"primary_key": "2580270", "vector": [], "sparse_vector": [], "title": "Separate Your Domains: NIST PQC KEMs, Oracle Cloning and Read-Only Indifferentiability.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is convenient and common for schemes in the random oracle model to assume access to multiple random oracles (ROs), leaving to implementations the task—we call it oracle cloning—of constructing them from a single RO. The first part of the paper is a case study of oracle cloning in KEM submissions to the NIST Post-Quantum Cryptography standardization process. We give key-recovery attacks on some submissions arising from mistakes in oracle cloning, and find other submissions using oracle cloning methods whose validity is unclear. Motivated by this, the second part of the paper gives a theoretical treatment of oracle cloning. We give a definition of what is an “oracle cloning method” and what it means for such a method to “work,” in a framework we call read-only indifferentiability, a simple variant of classical indifferentiability that yields security not only for usage in single-stage games but also in multi-stage ones. We formalize domain separation, and specify and study many oracle cloning methods, including common domain-separating ones, giving some general results to justify (prove read-only indifferentiability of) certain classes of methods. We are not only able to validate the oracle cloning methods used in many of the unbroken NIST PQC KEMs, but also able to specify and validate oracle cloning methods that may be useful beyond that.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_1"}, {"primary_key": "2580271", "vector": [], "sparse_vector": [], "title": "Security Under Message-Derived Keys: Signcryption in iMessage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At the core of Apple’s iMessage is a signcryption scheme that involves symmetric encryption of a message under a key that is derived from the message itself. This motivates us to formalize a primitive we call Encryption under Message-Derived Keys (EMDK). We prove security of the EMDK scheme underlying iMessage. We use this to prove security of the signcryption scheme itself, with respect to definitions of signcryption we give that enhance prior ones to cover issues peculiar to messaging protocols. Our provable-security results are quantitative, and we discuss the practical implications for iMessage.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_17"}, {"primary_key": "2580272", "vector": [], "sparse_vector": [], "title": "Sigma Protocols for MQ, PKP and SIS, and Fishy Signature Schemes.", "authors": ["<PERSON>"], "summary": "This work presents sigma protocols to prove knowledge of: a solution to a system of quadratic polynomials, a solution to an instance of the Permuted Kernel Problem and a witness for a variety of lattice statements (including SIS). Our sigma protocols have soundness error 1/\\(q'\\), where\\(q'\\)is any number bounded by the size of the underlying finite field. This is much better than existing proofs, which have soundness error 2/3 or\\((q'+1)/2q'\\). The prover and verifier time our proofs are\\(O(q')\\). We achieve this by first constructing so-calledsigma protocols with helper, which are sigma protocols where the prover and the verifier are assisted by a trusted third party, and then eliminating the helper from the proof with a “cut-and-choose” protocol. We apply the Fiat-<PERSON><PERSON><PERSON> transform to obtain signature schemes with security proof in the QROM. We show that the resulting signature schemes, which we call the “MUltivariate quaDratic FIat-SHamir” scheme (MUDFISH) and the “ShUffled Solution to Homogeneous linear SYstem FIat-SHamir” scheme (SUSHSYFISH), are more efficient than existing signatures based on the MQ problem and the Permuted Kernel Problem. Our proof system can be used to improve the efficiency of applications relying on (generalizations of) <PERSON>’s protocol. We show that the proof size of our SIS proof is smaller than that of <PERSON>’s protocol by an order of magnitude and that our proof is more efficient than existing post-quantum secure SIS proofs.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_7"}, {"primary_key": "2580273", "vector": [], "sparse_vector": [], "title": "On the Quantum Complexity of the Continuous Hidden Subgroup Problem.", "authors": ["<PERSON><PERSON>", "Léo Du<PERSON>", "<PERSON>"], "summary": "The Hidden Subgroup Problem (HSP) aims at capturing all problems that are susceptible to be solvable in quantum polynomial time following the blueprints of <PERSON><PERSON>’s celebrated algorithm. Successful solutions to this problems over various commutative groups allow to efficiently perform number-theoretic tasks such as factoring or finding discrete logarithms. The latest successful generalization (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. STOC 2014) considers the problem of finding a full-rank lattice as the hidden subgroup of the continuous vector space\\(\\mathbb {R}^m\\), even for large dimensionsm. It unlocked new cryptanalytic algorithms (Biasse-Song SODA 2016, <PERSON><PERSON><PERSON> et al. EUROCRYPT 2016 and 2017), in particular to find mildly short vectors in ideal lattices. The cryptanalytic relevance of such a problem raises the question of a more refined and quantitative complexity analysis. In the light of the increasing physical difficulty of maintaining a large entanglement of qubits, the degree of concern may be different whether the above algorithm requires only linearly many qubits or a much larger polynomial amount of qubits. This is the question we start addressing with this work. We propose a detailed analysis of (a variation of) the aforementioned HSP algorithm, and conclude on its complexity as a function of all the relevant parameters. Our modular analysis is tailored to support the optimization of future specialization to cases of cryptanalytic interests. We suggest a few ideas in this direction.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_12"}, {"primary_key": "2580274", "vector": [], "sparse_vector": [], "title": "Quantum Security Analysis of CSIDH.", "authors": ["<PERSON>", "<PERSON>"], "summary": "CSIDH is a recent proposal for post-quantum non-interactive key-exchange, based on supersingular elliptic curve isogenies. It is similar in design to a previous scheme by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, but aims at an improved balance between efficiency and security. In the proposal, the authors suggest concrete parameters in order to meet some desired levels of quantum security. These parameters are based on the hardness of recovering a hidden isogeny between two elliptic curves, using a quantum subexponential algorithm of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. This algorithm combines two building blocks: first, a quantum algorithm for recovering a hidden shift in a commutative group. Second, a computation in superposition of all isogenies originating from a given curve, which the algorithm calls as a black box. In this paper, we give a comprehensive security analysis of CSIDH. Our first step is to revisit three quantum algorithms for the abelian hidden shift problem from the perspective of non-asymptotic cost, with trade-offs between their quantum and classical complexities. Second, we complete the non-asymptotic study of the black box in the hidden shift algorithm. We give a quantum procedure that evaluates CSIDH-512 using less than 40 000 logical qubits. This allows us to show that the parameters proposed by the authors of CSIDH do not meet their expected quantum security.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_17"}, {"primary_key": "2580275", "vector": [], "sparse_vector": [], "title": "Hardness of LWE on General Entropic Distributions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The hardness of the Learning with Errors (LWE) problem is by now a cornerstone of the cryptographic landscape. In many of its applications the so called “LWE secret” is not sampled uniformly, but comes from a distribution with some min-entropy. This variant, known as “Entropic LWE”, has been studied in a number of works, starting with <PERSON> et al. (ICS 2010). However, so far it was only known how to prove the hardness of Entropic LWE for secret distributions supported inside a ball of small radius. In this work we resolve the hardness of Entropic LWE with arbitrary long secrets, in the following sense. We show an entropy bound that guarantees the security of arbitrary Entropic LWE. This bound is higher than what is required in the ball-bounded setting, but we show that this is essentially tight. Tightness is shown unconditionally for highly-composite moduli, and using black-box impossibility for arbitrary moduli. Technically, we show that the entropic hardness of LWE relies on a simple to describe lossiness property of the distribution of secrets itself. This is simply the probability of recovering a random sample from this distributions, given\\(s+e\\), whereeis Gaussian noise (i.e. the quality of the distribution of secrets as an error correcting code for Gaussian noise). We hope that this characterization will make it easier to derive entropic LWE results more easily in the future. We also use our techniques to show new results for the ball-bounded setting, essentially showing that under a strong enough assumption even polylogarithmic entropy suffices.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_19"}, {"primary_key": "2580276", "vector": [], "sparse_vector": [], "title": "Candidate iO from Homomorphic Encryption Schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new approach to construct general-purpose indistinguishability obfuscation (iO). Our construction is obtained via a new intermediate primitive that we callsplit fully-homomorphic encryption(split FHE), which we show to be sufficient for constructing iO. Specifically, split FHE is FHE where decryption takes the following two-step syntactic form: (i) Asecretdecryption step uses the secret key and produces ahintwhich is (asymptotically) shorter than the length of the encrypted message, and (ii) apublicdecryption step that only requires the ciphertext and the previously generated hint (and not the entire secret key), and recovers the encrypted message. In terms of security, the hints for a set of ciphertexts should not allow one to violate semantic security for any other ciphertexts. Next, we show ageneric candidateconstruction of split FHE based on three building blocks: (i) A standard FHE scheme with linear decrypt-and-multiply (which can be instantiated with essentially all LWE-based constructions), (ii) a linearly homomorphic encryption scheme with short decryption hints (such as the Damgård-Jurik encryption scheme, based on the DCR problem), and (iii) a cryptographic hash function (which can be based on a variety of standard assumptions). Our approach isheuristicin the sense that our construction is not provably secure and makes implicit assumptions about the interplay between these underlying primitives. We show evidence that this construction is secure by providing an argument in an appropriately defined oracle model. We view our construction as a big departure from the state-of-the-art constructions, and it is in fact quite simple.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_4"}, {"primary_key": "2580277", "vector": [], "sparse_vector": [], "title": "Transparent SNARKs from DARK Compilers.", "authors": ["Benedikt <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct a new polynomial commitment scheme for univariate and multivariate polynomials over finite fields, with logarithmic size evaluation proofs and verification time, measured in the number of coefficients of the polynomial. The underlying technique is aDiophantine Argument of Knowledge(DARK), leveraging integer representations of polynomials and groups of unknown order. Security is shown from the strong RSA and the adaptive root assumptions. Moreover, the scheme does not require a trusted setup if instantiated with class groups. We apply this new cryptographic compiler to a restricted class of algebraic linear IOPs, which we callPolynomial IOPs, to obtain doubly-efficient public-coin interactive arguments of knowledge for any NP relation with succinct communication. With linear preprocessing, the online verifier’s work is logarithmic in the circuit complexity of the relation. There are many existing examples of Polynomial IOPs (PIOPs) dating back to the first PCP (BFLS, STOC’91). We present a generic compilation of any PIOP using our DARK polynomial commitment scheme. In particular, compiling the PIOP fromPLONK(GWC, ePrint’19), an improvement onSonic(MBKM, CCS’19), yields a public-coin interactive argument with quasi-linear preprocessing, quasi-linear (online) prover time, logarithmic communication, and logarithmic (online) verification time in the circuit size. Applying Fiat-Shamir results in a SNARK, which we call. Supersonicis also concretely efficient with 10 KB proofs and under 100 ms verification time for circuits with 1 million gates (estimated for 120-bit security). Most importantly, this SNARK istransparent: it does not require a trusted setup. We obtain zk-SNARKs by applying a hiding variant of our polynomial commitment scheme with zero-knowledge evaluations.Supersonicis the first complete zk-SNARK system that has both a practical prover time as well as asymptoticallylogarithmicproof size and verification time. The full version of the paper is available online [19].", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_24"}, {"primary_key": "2580278", "vector": [], "sparse_vector": [], "title": "Rational Isogenies from Irrational Endomorphisms.", "authors": ["W<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce a polynomial-time algorithm to compute a connecting\\(\\mathcal {O}\\)-ideal between two supersingular elliptic curves over\\(\\mathbb {F}_p\\)with common\\(\\mathbb {F}_p\\)-endomorphism ring\\(\\mathcal {O}\\), given a description of their full endomorphism rings. This algorithm provides a reduction of the security of the CSIDH cryptosystem to the problem of computing endomorphism rings of supersingular elliptic curves. A similar reduction for SIDH appeared at Asiacrypt 2016, but relies on totally different techniques. Furthermore, we also show that any supersingular elliptic curve constructed using the complex-multiplication method can be located precisely in the supersingular isogeny graph by explicitly deriving a path to a known base curve. This result prohibits the use of such curves as a building block for a hash function into the supersingular isogeny graph.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_18"}, {"primary_key": "2580279", "vector": [], "sparse_vector": [], "title": "Marlin: Preprocessing zkSNARKs with Universal and Updatable SRS.", "authors": ["<PERSON>", "Yuncong Hu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a methodology to construct preprocessing zkSNARKs where the structured reference string (SRS) is universal and updatable. This exploits a novel use ofholography[<PERSON><PERSON> et al., STOC 1991], where fast verification is achieved provided the statement being checked is given in encoded form. We use our methodology to obtain a preprocessing zkSNARK where the SRS has linear size and arguments have constant size. Our construction improves on <PERSON> [<PERSON> et al., CCS 2019], the prior state of the art in this setting, in all efficiency parameters: proving is an order of magnitude faster and verification is thrice as fast, even with smaller SRS size and argument size. Our construction is most efficient when instantiated in the algebraic group model (also used by Sonic), but we also demonstrate how to realize it under concrete knowledge assumptions. We implement and evaluate our construction. The core of our preprocessing zkSNARK is an efficientalgebraic holographic proof(AHP) for rank-1 constraint satisfiability (R1CS) that achieves linear proof length and constant query complexity.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_26"}, {"primary_key": "2580280", "vector": [], "sparse_vector": [], "title": "Fractal: Post-quantum and Transparent Recursive Proofs from Holography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new methodology to efficiently realize recursive composition of succinct non-interactive arguments of knowledge (SNARKs). Prior to this work, the only known methodology relied on pairing-based SNARKs instantiated on cycles of pairing-friendly elliptic curves, an expensive algebraic object. Our methodology does not rely on any special algebraic objects and, moreover, achieves new desirable properties: it ispost-quantumand it istransparent(the setup is public coin). We exploit the fact that recursive composition is simpler for SNARKs withpreprocessing, and the core of our work is obtaining a preprocessing zkSNARK for rank-1 constraint satisfiability (R1CS) that is post-quantum and transparent. We obtain this latter by establishing a connection between holography and preprocessing in the random oracle model, and then constructing a holographic proof for R1CS. We experimentally validate our methodology, demonstrating feasibility in practice. (The full version of this work is available athttps://ia.cr/2019/1076.)", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_27"}, {"primary_key": "2580281", "vector": [], "sparse_vector": [], "title": "Broadcast-Optimal Two-Round MPC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An intensive effort by the cryptographic community to minimize the round complexity of secure multi-party computation (MPC) has recently led to optimal two-round protocols from minimal assumptions. Most of the proposed solutions, however, make use of a broadcast channel in every round, and it is unclear if the broadcast channel can be replaced by standard point-to-point communication in a round-preserving manner, and if so, at what cost on the resulting security. In this work, we provide a complete characterization of the trade-off between number of broadcast rounds and achievable security level for two-round MPC tolerating arbitrarily many active corruptions. Specifically, we consider all possible combinations of broadcast and point-to-point rounds against the three standard levels of security for maliciously secure MPC protocols, namely, security with identifiable, unanimous, and selective abort. For each of these notions and each combination of broadcast and point-to-point rounds, we provide either a tight feasibility or an infeasibility result of two-round MPC. Our feasibility results hold assuming two-round OT in the CRS model, whereas our impossibility results hold given any correlated randomness.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_28"}, {"primary_key": "2580282", "vector": [], "sparse_vector": [], "title": "Side-Channel Masking with Pseudo-Random Generator.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High-order masking countermeasures against side-channel attacks usually require plenty of randomness during their execution. For security againsttprobes, the classical ISW countermeasure requires\\(\\mathcal{O}(t^2 s)\\)random bits, wheresis the circuit size. However running a True Random Number Generator (TRNG) can be costly in practice and become a bottleneck on embedded devices. In [IKL+13] the authors introduced the notion ofrobustpseudo-random number generator (PRG), which must remain secure even against an adversary who can probe at mosttwires. They showed that when embedding a robust PRG within a private circuit, the number of random bits can be reduced to\\(\\mathcal{\\tilde{O}}(t^{4})\\), that is independent of the circuit sizes(up to a logarithmic factor). Using bipartite expander graphs, this can be further reduced to\\(\\mathcal{\\tilde{O}}(t^{3+\\varepsilon })\\); however the resulting construction is impractical. In this paper we describe a construction where the number of random bits is only\\(\\mathcal{\\tilde{O}}(t^2)\\)for security againsttprobes, without expander graphs; moreover the running time of each pseudo-random generation goes down from\\(\\mathcal{\\tilde{O}}(t^{4})\\)to\\(\\mathcal{\\tilde{O}}(t)\\). Our technique consists in using multiple independent PRGs instead of a single one. We show that for ISW circuits, the robustness property of the PRG is not required anymore, which leads to simple and efficient constructions. For example, for AES we only need 48 bytes of randomness to get second-order security (\\(t=2\\)), instead of 2880 in the original Rivain-Prouff countermeasure. As a first feasibility result, we have implemented our countermeasure on an ARM-based embedded device with a relatively slow TRNG, and obtained a\\(50\\%\\)speed-up compared to Rivain-Prouff.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_12"}, {"primary_key": "2580283", "vector": [], "sparse_vector": [], "title": "Private Information Retrieval with Sublinear Online Time.", "authors": ["<PERSON>-<PERSON>", "<PERSON>"], "summary": "We present the first protocols for private information retrieval that allow fast (sublinear-time) database lookups without increasing the server-side storage requirements. To achieve these efficiency goals, our protocols work in an offline/online model. In anofflinephase, which takes place before the client has decided which database bit it wants to read, the client fetches a short string from the servers. In a subsequentonlinephase, the client can privately retrieve its desired bit of the database by making a second query to the servers. By pushing the bulk of the server-side computation into the offline phase (which is independent of the client’s query), our protocols allow the online phase to complete very quickly—in time sublinear in the size of the database. Our protocols can provide statistical security in the two-server setting and computational security in the single-server setting. Finally, we prove that, in this model, our protocols are optimal in terms of the trade-off they achieve between communication and running time.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_3"}, {"primary_key": "2580284", "vector": [], "sparse_vector": [], "title": "Non-interactive Zero-Knowledge in Pairing-Free Groups from Weaker Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide new constructions of non-interactive zero-knowledge arguments (\\(\\textsf {NIZKs}\\)) for NP from discrete-logarithm-style assumptions over cyclic groups, without relying on pairings. A previous construction from (<PERSON><PERSON> et al., Eurocrypt’18) achieves such\\(\\textsf {NIZKs}\\)under the assumption that no efficient adversary can break the key-dependent message (KDM) security of (additive) ElGamal with respect to all (even inefficient) functions over groups of size\\(2^\\lambda \\), with probability better than\\(\\mathsf {poly} (\\lambda )/2^{\\lambda }\\). This is an extremely strong, non-falsifiable assumption. In particular, even mild (polynomial) improvements over the current best known attacks on the discrete logarithm problem would already contradict this assumption. (<PERSON><PERSON> et al. STOC’19) describe how to improve the assumption to rely only on KDM security with respect to all efficient functions, therefore obtaining an assumption that is (in spirit) falsifiable. Our first construction improves this state of affairs. We provide a construction of\\(\\textsf {NIZKs}\\)for NP under the CDH assumption together with the assumption that no efficient adversary can break the key-dependent message one-wayness of <PERSON>G<PERSON><PERSON> with respect toefficientfunctions over groups of size\\(2^\\lambda \\), with probability better than\\(\\mathsf {poly} (\\lambda )/2^{c\\lambda }\\)(denoted\\(2^{-c\\lambda }\\)-\\(\\mathsf {OW\\text {-}KDM}\\)), for a constant\\(c = 3/4\\). Unlike the previous assumption, our assumption leaves an exponential gap between the best known attack and the required security guarantee. We also analyse whether we could build\\(\\textsf {NIZKs}\\)when CDH does not hold. As a second contribution, we construct aninfinitely often\\(\\textsf {NIZK}\\)argument system for NP (where soundness and zero-knowledge are only guaranteed to hold for infinitely many security parameters), under the\\(2^{-c\\lambda }\\)-\\(\\mathsf {OW\\text {-}KDM}\\)security of ElGamal with\\(c = 28/29+o(1)\\), together with the existence of low-depth pseudorandom generators.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_15"}, {"primary_key": "2580285", "vector": [], "sparse_vector": [], "title": "Blackbox Secret Sharing Revisited: A Coding-Theoretic Approach with Application to Expansionless Near-Threshold Schemes.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Ablackboxsecret sharing (BBSS) scheme works in exactly the same way for all finite Abelian groupsG; it can be instantiated for any such groupGandonlyblack-box access to its group operations and to random group elements is required. A secret is a single group element and each of thenplayers’ shares is a vector of such elements. Share-computation and secret-reconstruction is by integer linear combinations. These do not depend onG, and neither do the privacy and reconstruction parameterst,r. This classical, fundamental primitive was introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 1989) in their context of “threshold cryptography.” The expansion factor is the total number of group elements in a full sharing divided byn. For threshold BBSS witht-privacy (\\(1\\le t \\le n-1\\)),\\(t+1\\)-reconstruction and arbitraryn, constructions with minimal expansion\\(O(\\log n)\\)exist (CRYPTO 2002, 2005). These results are firmly rooted in number theory; each makes (different) judicious choices of orders in number fields admitting a vector of elements of very large length (in the number field degree) whose corresponding Vandermonde-determinant is sufficiently controlled so as to enable BBSS by a suitable adaptation of <PERSON><PERSON><PERSON>’s scheme. Alternative approaches generally lead to very large expansion. The state of the art of BBSS has not changed for the last 17 years. Our contributions are two-fold. (1) We introduce a novel, nontrivial, effective construction of BBSS based oncoding theoryinstead of number theory. For threshold-BBSS we also achieve minimal expansion factor\\(O(\\log n)\\). (2) Our method is more versatile. Namely, we show, for the first time, BBSS that isnear-threshold, i.e.,\\(r-t\\)is an arbitrarily small constant fraction ofn,andthat has expansion factorO(1), i.e., individual share-vectors ofconstantlength (“asymptotically expansionless”). Threshold can be concentrated essentially freely across full range. We also show expansion is minimal for near-threshold and that such BBSS cannot be attained by previous methods. Our general construction is based on a well-known mathematical principle, the local-global principle. More precisely, we first construct BBSS over local rings through either Reed-Solomon or algebraic geometry codes. We then “glue” these schemes together in a dedicated manner to obtain a global secret sharing scheme, i.e., defined over the integers, which, as we finally prove using novel insights, has the desired BBSS properties. Though our main purpose here is advancing BBSS for its own sake, we also briefly address possible protocol applications.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_18"}, {"primary_key": "2580286", "vector": [], "sparse_vector": [], "title": "(One) Failure Is Not an Option: Bootstrapping the Search for Failures in Lattice-Based Encryption Schemes.", "authors": ["Jan<PERSON><PERSON>&apo<PERSON>;An<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Lattice-based encryption schemes are often subject to the possibility of decryption failures, in which valid encryptions are decrypted incorrectly. Such failures, in large number, leak information about the secret key, enabling an attack strategy alternative to pure lattice reduction. Extending the “failure boosting” technique of <PERSON><PERSON> et al. in PKC 2019, we propose an approach that we call “directionalfailure boosting” that uses previously found “failing ciphertexts” to accelerate the search for new ones. We analyse in detail the case where the lattice is defined over polynomial ring modules quotiented by\\(\\langle X^{N} + 1 \\rangle \\)and demonstrate it on a simple Mod-LWE-based scheme parametrizedà laKyber768/Saber. We show that for a given secret key (single-target setting), the cost of searching for additional failing ciphertexts after one or more have already been found, can be sped up dramatically. We thus demonstrate that, in this single-target model, these schemes should be designed so that it is hard to even obtain one decryption failure. Besides, in a wider security model where there are many target secret keys (multi-target setting), our attack greatly improves over the state of the art.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_1"}, {"primary_key": "2580287", "vector": [], "sparse_vector": [], "title": "Tight Time-Space Lower Bounds for Finding Multiple Collision Pairs and Their Applications.", "authors": ["<PERSON><PERSON>"], "summary": "We consider acollision search problem(CSP), where given a parameterC, the goal is to findCcollision pairs in a random function\\(f:[N] \\rightarrow [N]\\)(where\\([N] = \\{0,1,\\ldots ,N-1\\})\\)usingSbits of memory. Algorithms for CSP have numerous cryptanalytic applications such as space-efficient attacks on double and triple encryption. The best known algorithm for CSP isparallel collision search(PCS) published by <PERSON> and <PERSON>, which achieves the time-space tradeoff\\(T^2 \\cdot S = \\tilde{O}(C^2 \\cdot N)\\). In this paper, we prove that any algorithm for CSP satisfies\\(T^2 \\cdot S = \\tilde{\\varOmega }(C^2 \\cdot N)\\), hence the best known time-space tradeoff is optimal (up to poly-logarithmic factors inN). On the other hand, we give strong evidence that proving similar unconditional time-space tradeoff lower bounds on CSP applications (such as breaking double and triple encryption) may be very difficult, and would imply a breakthrough in complexity theory. Hence, we propose a new restricted model of computation and prove that under this model, the best known time-space tradeoff attack on double encryption is optimal.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_15"}, {"primary_key": "2580288", "vector": [], "sparse_vector": [], "title": "On the Streaming Indistinguishability of a Random Permutation and a Random Function.", "authors": ["<PERSON><PERSON>"], "summary": "An adversary withSbits of memory obtains a stream ofQelements that are uniformly drawn from the set\\(\\{1,2,\\ldots ,N\\}\\), either with or without replacement. This corresponds to samplingQelements using either a random function or a random permutation. The adversary’s goal is to distinguish between these two cases. This problem was first considered by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT 2019), which proved that the adversary’s advantage is upper bounded by\\(\\sqrt{Q \\cdot S/N}\\). <PERSON><PERSON><PERSON> and <PERSON><PERSON> used this bound as a streaming switching lemma which allowed proving that known time-memory tradeoff attacks on several modes of operation (such as counter-mode) are optimal up to a factor of\\(O(\\log N)\\)if\\(Q \\cdot S \\approx N\\). However, the bound’s proof assumed an unproven combinatorial conjecture. Moreover, if\\(Q \\cdot S \\ll N\\)there is a gap between the upper bound of\\(\\sqrt{Q \\cdot S/N}\\)and the\\(Q \\cdot S/N\\)advantage obtained by known attacks. In this paper, we prove a tight upper bound (up to poly-logarithmic factors) of\\(O(\\log Q \\cdot Q \\cdot S/N)\\)on the adversary’s advantage in the streaming distinguishing problem. The proof does not require a conjecture and is based on a hybrid argument that gives rise to a reduction from the unique-disjointness communication complexity problem to streaming.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_15"}, {"primary_key": "2580289", "vector": [], "sparse_vector": [], "title": "Extracting Randomness from Extractor-Dependent Sources.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We revisit the well-studied problem of extracting nearly uniform randomness from an arbitrary source of sufficient min-entropy. Strong seeded extractors solve this problem by relying on a public random seed, which is unknown to the source. Here, we consider a setting where the seed is reused over time andthe source may depend on prior calls to the extractor with the same seed. Can we still extract nearly uniform randomness? In more detail, we assume the seed is chosen randomly, but the source can make arbitrary oracle queries to the extractor with the given seed before outputting a sample. We require that the sample has entropy and differs from any of the previously queried values. The extracted output should look uniform even to a distinguisher that gets the seed. We consider two variants of the problem, depending on whether the source only outputs the sample, or whether it can also output some correlated publicauxiliary informationthat preserves the sample’s entropy. Our results are: Without Auxiliary Information: We show that everypseudo-random function(PRF) with a sufficiently high security level is a good extractor in this setting, even if the distinguisher is computationally unbounded. We further show that the source necessarily needs to be computationally bounded and that such extractors imply one-way functions. With Auxiliary Information: We construct secure extractors in this setting, as long as both the source and the distinguisher are computationally bounded. We give several constructions based on different intermediate primitives, yielding instantiations based on the DDH, DLIN, LWE or DCR assumptions. On the negative side, we show that one cannot prove security against computationally unbounded distinguishers in this setting under any standard assumption via a black-box reduction. Furthermore, even when restricting to computationally bounded distinguishers, we show that there exist PRFs that are insecure as extractors in this setting and that a large class of constructions cannot be proven secure via a black-box reduction from standard assumptions.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_12"}, {"primary_key": "2580290", "vector": [], "sparse_vector": [], "title": "Two-Round Oblivious Transfer from CDH or LPN.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show a new general approach for constructing maliciously-secure two-round oblivious transfer (OT). Specifically, we provide a generic sequence of transformations to upgrade a very basic notion of two-round OT, which we callelementary OT, to UC-secure OT. We then give simple constructions of elementary OT under the Computational Di<PERSON><PERSON>-<PERSON> (CDH) assumption or the Learning Parity with Noise (LPN) assumption, yielding the first constructions of malicious (UC-secure) two-round OT under these assumptions. Since two-round OT is complete for two-round 2-party and multi-party computation in the malicious setting, we also achieve the first constructions of the latter under these assumptions.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_26"}, {"primary_key": "2580291", "vector": [], "sparse_vector": [], "title": "Integral Matrix Gram Root and <PERSON><PERSON><PERSON>pling Without Floats.", "authors": ["Léo Du<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many advanced lattice based cryptosystems require to sample lattice points from Gaussian distributions. One challenge for this task is that all current algorithms resort to floating-point arithmetic (FPA) at some point, which has numerous drawbacks in practice: it requires numerical stability analysis, extra storage for high-precision, lazy/backtracking techniques for efficiency, and may suffer from weak determinism which can completely break certain schemes. In this paper, we give techniques to implement Gaussian sampling over general lattices without using FPA. To this end, we revisit the approach of <PERSON><PERSON><PERSON><PERSON>, using perturbation sampling. <PERSON><PERSON><PERSON><PERSON>’s approach uses continuous Gaussian sampling and some decomposition\\(\\mathbf {\\Sigma }= \\mathbf {A}\\mathbf {A}^t\\)of the target covariance matrix\\(\\mathbf {\\Sigma }\\). The suggested decomposition, e.g. the <PERSON><PERSON><PERSON> decomposition, gives rise to a square matrix\\(\\mathbf {A}\\)with real (not integer) entries. Our idea, in a nutshell, is to replace this decomposition by an integral one. While there is in general no integer solution if we restrict\\(\\mathbf {A}\\)to being a square matrix, we show that such a decomposition can be efficiently found by allowing\\(\\mathbf {A}\\)to be wider (say\\(n \\times 9n\\)). This can be viewed as an extension of <PERSON><PERSON><PERSON>’s four-square theorem to matrices. In addition, we adapt our integral decomposition algorithm to the ring setting: for power-of-2 cyclotomics, we can exploit the tower of rings structure for improved complexity and compactness.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_21"}, {"primary_key": "2580292", "vector": [], "sparse_vector": [], "title": "Secure Multi-party Quantum Computation with a Dishonest Majority.", "authors": ["<PERSON><PERSON><PERSON>", "Alex <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The cryptographic task of secure multi-party (classical) computation has received a lot of attention in the last decades. Even in the extreme case where a computation is performed betweenkmutually distrustful players, and security is required even for the single honest player if all other players are colluding adversaries, secure protocols are known. For quantum computation, on the other hand, protocols allowing arbitrary dishonest majority have only been proven for\\(k=2\\). In this work, we generalize the approach taken by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (CRYPTO 2012) in the two-party setting to devise a secure, efficient protocol for multi-party quantum computation for any number of playersk, and prove security against up to\\(k-1\\)colluding adversaries. The quantum round complexity of the protocol for computing a quantum circuit of\\(\\{\\mathsf {CNOT}, \\mathsf {T} \\}\\)depthdis\\(O(k \\cdot (d + \\log n))\\), wherenis the security parameter. To achieve efficiency, we develop a novel public verification protocol for the Clifford authentication code, and a testing protocol for magic-state inputs, both using classical multi-party computation.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_25"}, {"primary_key": "2580293", "vector": [], "sparse_vector": [], "title": "New Slide Attacks on Almost Self-similar Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The slide attack is a powerful cryptanalytic tool which can break iterated block ciphers with a complexity that does not depend on their number of rounds. However, it requires complete self similarity in the sense that all the rounds must be identical. While this can be the case in Feistel structures, this rarely happens in SP networks since the last round must end with an additional post-whitening subkey. In addition, in many SP networks the final round has additional asymmetries – for example, in AES the last round omits the MixColumns operation. Such asymmetry in the last round can make it difficult to utilize most of the advanced tools which were developed for slide attacks, such as deriving from one slid pair additional slid pairs by repeatedly re-encrypting their ciphertexts. Consequently, almost all the successful applications of slide attacks against real cryptosystems (e.g., FF3, GOST, SHACAL-1) had targeted Feistel structures rather than SP networks. In this paper we overcome this “last round problem” by developing four new types of slide attacks. We demonstrate their power by applying them to many types of AES-like structures (with and without linear mixing in the last round, with known or secret S-boxes, with periodicity of 1, 2 and 3 in their subkeys, etc). In most of these cases, the time complexity of our attack is close to\\(2^{n/2}\\), the smallest possible complexity for most slide attacks. Our new slide attacks have several unique properties: The first usesslid setsin which each plaintext from the first set forms a slid pair with some plaintext from the second set, but without knowing the exact correspondence. The second makes it possible to create from several slid pairs an exponential number of new slid pairs which form a hypercube spanned by the given pairs. The third has the unusual property that it is always successful, and the fourth can use known messages instead of chosen messages, with only slightly higher time complexity.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_10"}, {"primary_key": "2580294", "vector": [], "sparse_vector": [], "title": "The Retracing Boomerang Attack.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Boomerang attacks are extensions of differential attacks, that make it possible to combine two unrelated differential properties of the first and second part of a cryptosystem with probabilitiespandqinto a new differential-like property of the whole cryptosystem with probability\\(p^2q^2\\)(since each one of the properties has to be satisfied twice). In this paper we describe a new version of boomerang attacks which uses the counterintuitive idea of throwing out most of the data in order to force equalities between certain values on the ciphertext side. In certain cases, this creates a correlation between the four probabilistic events, which increases the probability of the combined property to\\(p^2q\\)and increases the signal to noise ratio of the resultant distinguisher. We call this variant aretracing boomerang attacksince we make sure that the boomerang we throw follows the same path on its forward and backward directions. To demonstrate the power of the new technique, we apply it to the case of 5-round AES. This version of AES was repeatedly attacked by a large variety of techniques, but for twenty years its complexity had remained stuck at\\(2^{32}\\). At Crypto’18 it was finally reduced to\\(2^{24}\\)(for full key recovery), and with our new technique we can further reduce the complexity of full key recovery to the surprisingly low value of\\(2^{16.5}\\)(i.e., only 90, 000 encryption/decryption operations are required for a full key recovery on half the rounds of AES). In addition to improving previous attacks, our new technique unveils a hidden relationship between boomerang attacks and two other cryptanalytic techniques, the yoyo game and the recently introduced mixture differentials.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_11"}, {"primary_key": "2580295", "vector": [], "sparse_vector": [], "title": "SPARKs: Succinct Parallelizable Arguments of Knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of aSuccinct Parallelizable Argument of Knowledge(SPARK). This is an argument system with the following three properties for computing and proving a timeT(non-deterministic) computation: The prover’s (parallel) running time is\\(T + \\mathrm {poly}\\!\\log T\\). (In other words, the prover’s running time is essentiallyTfor large computation times!) The prover uses at most\\(\\mathrm {poly}\\!\\log T\\)processors. The communication complexity and verifier complexity are both\\(\\mathrm {poly}\\!\\log T\\). While the third property is standard in succinct arguments, the combination of all three is desirable as it gives a way to leverage moderate parallelism in favor of near-optimal running time. We emphasize that even a factor two overhead in the prover’s parallel running time is not allowed. Our main results are the following, all for non-deterministic polynomial-time RAM computation. We construct (1) an (interactive) SPARK based solely on the existence of collision-resistant hash functions, and (2) a non-interactive SPARK based on any collision-resistant hash function and any SNARK with quasi-linear overhead (as satisfied by recent SNARK constructions).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_25"}, {"primary_key": "2580296", "vector": [], "sparse_vector": [], "title": "Continuous Verifiable Delay Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of acontinuous verifiable delay function(cVDF): a functiongwhich is (a) iteratively sequential—meaning that evaluating the iteration\\(g^{(t)}\\)ofg(on a random input) takes time roughlyttimes the time to evaluateg, even with many parallel processors, and (b) (iteratively) verifiable—the output of\\(g^{(t)}\\)can be efficiently verified (in time that is essentially independent oft). In other words, the iterated function\\(g^{(t)}\\)is a verifiable delay function (VDF) (<PERSON> et al., CRYPTO ’18), having the property that intermediate steps of the computation (i.e.,\\(g^{(t')}\\)for\\(t'<t\\)) are publicly and continuously verifiable. We demonstrate that cVDFs have intriguing applications: (a) they can be used to construct\\(\\textit{public randomness beacons}\\)that only require an initial random seed (and no further unpredictable sources of randomness), (b) enable\\(\\textit{outsourceable VDFs}\\)where any part of the VDF computation can be verifiably outsourced, and (c) have deep complexity-theoretic consequences: in particular, they imply the existence ofdepth-robust moderately-hardNash equilibrium problem instances, i.e. instances that can be solved in polynomial time yet require a high sequential running time. Our main result is the construction of a cVDF based on the repeated squaring assumption and the soundness of the Fiat-Shamir (FS) heuristic for\\(\\textit{constant-round proofs}\\). We highlight that when viewed as a (plain) VDF, our construction requires a weaker FS assumption than previous ones (earlier constructions require the FS heuristic for either super-logarithmic round proofs, or for arguments).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_5"}, {"primary_key": "2580297", "vector": [], "sparse_vector": [], "title": "Low Weight Discrete Logarithm and Subset Sum in 20.65n with Polynomial Memory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose two heuristic polynomial memory collision finding algorithms for the low Hamming weight discrete logarithm problem in any abelian groupG. The first one is a direct adaptation of the <PERSON><PERSON><PERSON><PERSON> (BCJ) algorithm for subset sum to the discrete logarithm setting. The second one significantly improves on this adaptation for all possible weights using a more involved application of the representation technique together with some new Markov chain analysis. In contrast to other low weight discrete logarithm algorithms, our second algorithm’s time complexity interpolates to <PERSON><PERSON>’s\\(|G|^{\\frac{1}{2}}\\)bound for general discrete logarithm instances. We also introduce a new heuristic subset sum algorithm with polynomial memory that improves on BCJ’s\\(2^{0.72n}\\)time bound for random subset sum instances\\(a_1, \\ldots , a_n, t \\in \\mathbb {Z}_{2^n}\\). Technically, we introduce a novel nested collision finding for subset sum – inspired by the NestedRho algorithm from Crypto ’16 – that recursively produces collisions. We first show how to instantiate our algorithm with run time\\(2^{0.649n}\\). Using further tricks, we are then able to improve its complexity down to\\(2^{0.645n}\\).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_4"}, {"primary_key": "2580298", "vector": [], "sparse_vector": [], "title": "Signatures from Sequential-OR Proofs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "OR-proofs enable a prover to show that it knows the witness for one of many statements, or that one out of many statements is true. OR-proofs are a remarkably versatile tool, used to strengthen security properties, design group and ring signature schemes, and achieve tight security. The common technique to build OR-proofs is based on an approach introduced by <PERSON><PERSON><PERSON>, <PERSON>g<PERSON><PERSON>, and Schoenmakers (CRYPTO’94), where the prover splits the verifier’s challenge into random shares and computes proofs for each statement in parallel. In this work we study a different, less investigated OR-proof technique, highlighted by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (ASIACRYPT’02). The difference is that the prover now computes the individual proofs sequentially. We show that such sequential OR-proofs yield signature schemes which can be proved secure in the non-programmable random oracle model. We complement this positive result with a black-box impossibility proof, showing that the same is unlikely to be the case for signatures derived from traditional OR-proofs. We finally argue that sequential-OR signature schemes can be proved secure in the quantum random oracle model, albeit with very loose bounds and by programming the random oracle.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_8"}, {"primary_key": "2580299", "vector": [], "sparse_vector": [], "title": "Improving Key-Recovery in Linear Attacks: Application to 28-Round PRESENT.", "authors": ["<PERSON>Guti<PERSON>", "<PERSON>Plase<PERSON>"], "summary": "Linear cryptanalysis is one of the most important tools in use for the security evaluation of symmetric primitives. Many improvements and refinements have been published since its introduction, and many applications on different ciphers have been found. Among these upgrades, <PERSON><PERSON> et al. proposed in 2007 an acceleration of the key-recovery part of Algorithm 2 for last-round attacks based on the FFT. In this paper we present a generalized, matrix-based version of the previous algorithm which easily allows us to take into consideration an arbitrary number of key-recovery rounds. We also provide efficient variants that exploit the key-schedule relations and that can be combined with multiple linear attacks. Using our algorithms we provide some new cryptanalysis on PRESENT, including, to the best of our knowledge, the first attack on 28 rounds.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_9"}, {"primary_key": "2580300", "vector": [], "sparse_vector": [], "title": "Key Recovery from Gram-Schmidt Norm Leakage in Hash-and-Sign Signatures over NTRU Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we initiate the study of side-channel leakage in hash-and-sign lattice-based signatures, with particular emphasis on the two efficient implementations of the original GPV lattice-trapdoor paradigm for signatures, namely NIST second-round candidateFalconand its simpler predecessor DLP. Both of these schemes implement the GPV signature scheme over NTRU lattices, achieving great speed-ups over the general lattice case. Our results are mainly threefold. First, we identify a specific source of side-channel leakage in most implementations of those schemes, namely, the one-dimensional Gaussian sampling steps within lattice Gaussian sampling. It turns out that the implementations of these steps often leak the Gram–Schmidt norms of the secret lattice basis. Second, we elucidate the link between this leakage and the secret key, by showing that the entire secret key can be efficiently reconstructed solely from those Gram–Schmidt norms. The result makes heavy use of the algebraic structure of the corresponding schemes, which work over a power-of-two cyclotomic field. Third, we concretely demonstrate the side-channel attack against DLP (but notFalcondue to the different structures of the two schemes). The challenge is that timing information only provides an approximation of the Gram–Schmidt norms, so our algebraic recovery technique needs to be combined with pruned tree search in order to apply it to approximate values. Experimentally, we show that around\\(2^{35}\\)DLP traces are enough to reconstruct the entire key with good probability.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_2"}, {"primary_key": "2580301", "vector": [], "sparse_vector": [], "title": "Blind Schnorr Signatures and Signed ElGamal Encryption in the Algebraic Group Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Schnorr blind signing protocol allows blind issuing of Schnorr signatures, one of the most widely used signatures. Despite its practical relevance, its security analysis is unsatisfactory. The only known security proof is informal and in the combination of the generic group model (GGM) and the random oracle model (ROM) assuming that the “ROS problem” is hard. The situation is similar for (Schnorr-)signed ElGamal encryption, a simple CCA2-secure variant of ElGamal. We analyze the security of these schemes in the algebraic group model (AGM), an idealized model closer to the standard model than the GGM. We first prove tight security of Schnorr signatures from the discrete logarithm assumption (DL) in the AGM+ROM. We then give a rigorous proof for blind Schnorr signatures in the AGM+ROM assuming hardness of the one-more discrete logarithm problem and ROS. As ROS can be solved in sub-exponential time using <PERSON>’s algorithm, we propose a simple modification of the signing protocol, which leaves the signatures unchanged. It is therefore compatible with systems that already use Schnorr signatures, such as blockchain protocols. We show that the security of our modified scheme relies on the hardness of a problem related to ROS that appears much harder. Finally, we give tight reductions, again in the AGM+ROM, of the CCA2 security of signed ElGamal encryption to DDH and signed hashed ElGamal key encapsulation to <PERSON>.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_3"}, {"primary_key": "2580302", "vector": [], "sparse_vector": [], "title": "Resource-Restricted Cryptography: Revisiting MPC Bounds in the Proof-of-Work Era.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional bounds on synchronous Byzantine agreement (BA) and secure multi-party computation (MPC) establish that in absence of a private correlated-randomness setup, such as a PKI, protocols can tolerate up to\\(t<n/3\\)of the parties being malicious. The introduction of “Nakamoto style” consensus, based on Proof-of-Work (PoW) blockchains, put forth a somewhat different flavor of BA, showing that even a majority of corrupted parties can be tolerated as long as the majority of the computation resources remain at honest hands. This assumption on honest majority of some resource was also extended to other resources such as stake, space, etc., upon which blockchains achieving Nakamoto-style consensus were built that violated the\\(t<n/3\\)bound in terms of number of party corruptions. The above state of affairs begs the question of whether the seeming mismatch is due to different goals and models, or whether the resource-restricting paradigm can be generically used to circumvent then/3 lower bound. In this work we study this question and formally demonstrate how the above paradigm changes the rules of the game in cryptographic definitions. First, we abstract the core properties that the resource-restricting paradigm offers by means of a functionalitywrapper, in the UC framework, which when applied to a standard point-to-point network restricts the ability (of the adversary) to send new messages. We show that such a wrapped network can be implemented using the resource-restricting paradigm—concretely, using PoWs and honest majority of computing power—and that the traditional\\(t<n/3\\)impossibility results fail when the parties have access to such a network. Our construction is in thefreshCommon Reference String (CRS) model—i.e., it assumes a CRS which becomes available to the parties at the same time as to the adversary. We then present constructions for BA and MPC, which given access to such a network tolerate\\(t<n/2\\)corruptions without assuming a private correlated randomness setup. We also show how to remove the freshness assumption from the CRS by leveraging the power of a random oracle. Our MPC protocol achieves the standard notion of MPC security, where parties might have dedicated roles, as is for example the case in Oblivious Transfer protocols. This is in contrast to existing solutions basing MPC on PoWs, which associate roles to pseudonyms but do not link these pseudonyms with the actual parties.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_5"}, {"primary_key": "2580303", "vector": [], "sparse_vector": [], "title": "Formalizing Data Deletion in the Context of the Right to Be Forgotten.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The right of an individual to request the deletion of their personal data by an entity that might be storing it – referred to asthe right to be forgotten– has been explicitly recognized, legislated, and exercised in several jurisdictions across the world, including the European Union, Argentina, and California. However, much of the discussion surrounding this right offers only an intuitive notion of what it means for it to be fulfilled – of what it means for such personal data to be deleted. In this work, we provide a formal definitional framework for the right to be forgotten using tools and paradigms from cryptography. In particular, we provide a precise definition of what could be (or should be) expected from an entity that collects individuals’ data when a request is made of it to delete some of this data. Our framework captures most, though not all, relevant aspects of typical systems involved in data processing. While it cannot be viewed as expressing the statements of current laws (especially since these are rather vague in this respect), our work offers technically precise definitions that represent possibilities for what the law could reasonably expect, and alternatives for what future versions of the law could explicitly require. Finally, with the goal of demonstrating the applicability of our framework and definitions, we consider various natural and simple scenarios where the right to be forgotten comes up. For each of these scenarios, we highlight the pitfalls that arise even in genuine attempts at implementing systems offering deletion guarantees, and also describe technological solutions that provably satisfy our definitions. These solutions bring together techniques built by various communities.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_13"}, {"primary_key": "2580304", "vector": [], "sparse_vector": [], "title": "Low Error Efficient Computational Extractors in the CRS Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, there has been exciting progress on building two-source extractors for sources with low min-entropy. Unfortunately, all known explicit constructions of two-source extractors in the low entropy regime suffer from non-negligible error, and building such extractors with negligible error remains an open problem. We investigate this problem in the computational setting, and obtain the following results. We construct an explicit 2-source extractor, and even an explicit non-malleable extractor, with negligible error, for sources with low min-entropy, under computational assumptions in the Common Random String (CRS) model. More specifically, we assume that a CRS is generated once and for all, and allow the min-entropy sources to depend on the CRS. We obtain our constructions by using the following transformations. Building on the technique of [5], we show a general transformation for converting any computational 2-source extractor (in the CRS model) into a computational non-malleable extractor (in the CRS model), for sources with similar min-entropy. We emphasize that the resulting computational non-malleable extractor is resilient toarbitrarily manytampering attacks (a property that is impossible to achieve information theoretically). This may be of independent interest. This transformation uses cryptography, and relies on the sub-exponential hardness of the Decisional Di<PERSON><PERSON> (DDH) assumption. Next, using the blueprint of [1], we give a transformation converting our computational non-malleable extractor (in the CRS model) into a computational 2-source extractor for sources with low min-entropy (in the CRS model). Our 2-source extractor works for unbalanced sources: specifically, we require one of the sources to be larger than a specific polynomial in the other. This transformation does not incur any additional assumptions. Our analysis makes a novel use of the leakage lemma of Gentry and Wichs [18].", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_14"}, {"primary_key": "2580305", "vector": [], "sparse_vector": [], "title": "Private Aggregation from Fewer Anonymous Messages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Consider the setup wherenparties are each given an element\\(x_i\\)in the finite field\\(\\mathbb {F}_q\\)and the goal is to compute the sum\\(\\sum _i x_i\\)in a secure fashion and with as little communication as possible. We study this problem in theanonymized modelof <PERSON><PERSON> et al. (FOCS 2006) where each party may broadcast anonymous messages on an insecure channel. We present a new analysis of the one-round “split and mix” protocol of <PERSON><PERSON> et al. In order to achieve the same security parameter, our analysis reduces the required number of messages by a\\(\\varTheta (\\log n)\\)multiplicative factor. We also prove lower bounds showing that the dependence of the number of messages on the domain size, the number of parties, and the security parameter is essentially tight. Using a reduction of <PERSON><PERSON> et al. (2019), our improved analysis of the protocol of <PERSON><PERSON> et al. yields, in the same model, an\\(\\left( \\varepsilon , \\delta \\right) \\)-differentially private protocol for aggregation that, for any constant\\(\\varepsilon > 0\\)and any\\(\\delta = \\frac{1}{\\text {poly}(n)}\\), incurs only a constant error and requires only aconstant number of messagesper party. Previously, such a protocol was known only for\\(\\varOmega (\\log n)\\)messages per party.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_27"}, {"primary_key": "2580306", "vector": [], "sparse_vector": [], "title": "On the Memory-Tightness of <PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the memory-tightness of security reductions in public-key cryptography, focusing in particular on <PERSON><PERSON>. We prove that anystraightline(i.e., without rewinding) black-box reduction needs memory which grows linearly with the number of queries of the adversary it has access to, as long as this reduction treats the underlying group generically. This makes progress towards proving a conjecture by <PERSON><PERSON><PERSON><PERSON> al.(CRYPTO 2017), and is also the first lower bound on memory-tightness for a concrete cryptographic scheme (as opposed to generalized reductions across security notions). Our proof relies on compression arguments in the generic group model.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_2"}, {"primary_key": "2580307", "vector": [], "sparse_vector": [], "title": "Adaptively Secure ABE for DFA from k-Lin and More.", "authors": ["Junqing Gong", "<PERSON><PERSON><PERSON>e"], "summary": "In this work, we present: the first adaptively secure ABE for DFA from thek-Lin assumption in prime-order bilinear groups; this resolves one of open problems posed by <PERSON> [CRYPTO’12]; the first ABE for NFA from thek-Lin assumption, provided the number of accepting paths is smaller than the order of the underlying group; the scheme achieves selective security; the first compact adaptively secure ABE (supporting unbounded multi-use of attributes) for branching programs from thek-Lin assumption, which generalizes and simplifies the recent result of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for boolean formula (NC1) [EUROCRYPT’19]. Our adaptively secure ABE for DFA relies on a new combinatorial mechanism avoiding the exponential security loss in the number of states when naively combining two recent techniques from CRYPTO’19 and EUROCRYPT’19. This requires us to design a selectively secure ABE for NFA; we give a construction which is sufficient for our purpose and of independent interest. Our ABE for branching programs leverages insights from our ABE for DFA.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_10"}, {"primary_key": "2580308", "vector": [], "sparse_vector": [], "title": "Statistical Zaps and New Oblivious Transfer Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of achievingstatistical privacyin interactive proof systems and oblivious transfer – two of the most well studied two-party protocols – when limited rounds of interaction are available. Statistical Zaps:We give the first construction of statistical Zaps, namely, two-round statistical witness-indistinguishable (WI) protocols with apublic-coinverifier. Our construction achieves computational soundness based on the quasi-polynomial hardness of learning with errors assumption. Three-Round Statistical Receiver-Private Oblivious Transfer:We give the first construction of a three-round oblivious transfer (OT) protocol – in the plain model – that achieves statistical privacy for receivers and computational privacy for senders against malicious adversaries, based onpolynomial-timeassumptions. The round-complexity of our protocol is optimal. We obtain our first result by devising a public-coin approach to compress sigma protocols, without relying on trusted setup. To obtain our second result, we devise a general framework via a new notion ofstatistical hash commitmentsthat may be of independent interest.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_23"}, {"primary_key": "2580309", "vector": [], "sparse_vector": [], "title": "Modeling for Three-Subset Division Property Without Unknown Subset - Improved Cube Attacks Against Trivium and Grain-128AEAD.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A division property is a generic tool to search for integral distinguishers, and automatic tools such as MILP or SAT/SMT allow us to evaluate the propagation efficiently. In the application to stream ciphers, it enables us to estimate the security of cube attacks theoretically, and it leads to the best key-recovery attacks against well-known stream ciphers. However, it was reported that some of the key-recovery attacks based on the division property degenerate to distinguishing attacks due to the inaccuracy of the division property. Three-subset division property (without unknown subset) is a promising method to solve this inaccuracy problem, and a new algorithm using automatic tools for the three-subset division property was recently proposed at Asiacrypt2019. In this paper, we first show that this state-of-the-art algorithm is not always efficient and we cannot improve the existing key-recovery attacks. Then, we focus on the feature of the three-subset division property without unknown subset and propose another new efficient algorithm using automatic tools. Our algorithm is more efficient than existing algorithms, and it can improve existing key-recovery attacks. In the application toTrivium, we show a 841-round key-recovery attack. We also show that a 855-round key-recovery attack, which was proposed at CRYPTO2018, has a critical flaw and does not work. As a result, our 841-round attack becomes the best key-recovery attack. In the application to Grain-128AEAD, we show that the known 184-round key-recovery attack degenerates to distinguishing attacks. Then, the distinguishing attacks are improved up to 189 rounds, and we also show the best key-recovery attack against 190 rounds.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_17"}, {"primary_key": "2580310", "vector": [], "sparse_vector": [], "title": "Which Languages Have 4-Round Fully Black-Box Zero-Knowledge Arguments from One-Way Functions?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We prove that if a language\\(\\mathcal{L}\\)has a 4-round fully black-box zero-knowledge argument with negligible soundness based on one-way functions, then\\(\\overline{\\mathcal{L}} \\in \\mathsf {MA}\\). Since\\(\\mathsf {coNP}\\subseteq \\mathsf {MA}\\)implies that the polynomial hierarchy collapses, our result implies that\\(\\mathsf {NP}\\)-complete languages are unlikely to have 4-round fully black-box zero-knowledge arguments based on one-way functions. In TCC 2018, <PERSON><PERSON><PERSON> and <PERSON>enki<PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> demonstrated 4-round fully black-box zero-knowledge arguments for all languages in\\(\\mathsf {NP}\\)based on injective one-way functions. Their results also imply a 5-round protocol based on one-way functions. In essence, our result resolves the round complexity of fully black-box zero-knowledge arguments based on one-way functions.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_20"}, {"primary_key": "2580311", "vector": [], "sparse_vector": [], "title": "The Price of Active Security in Cryptographic Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct the first actively-secure Multi-Party Computation (MPC) protocols with anarbitrarynumber of parties in the dishonest majority setting, for anarbitraryfield\\(\\mathbb {F}\\)withconstant communication overheadover the “passive-GMW” protocol (Goldreich, Micali and Wigderson, STOC ‘87). Our protocols rely on passive implementations of Oblivious Transfer (OT) in the boolean setting and Oblivious Linear function Evaluation (OLE) in the arithmetic setting. Previously, such protocols were only known over sufficiently large fields (<PERSON><PERSON> et al. STOC ‘14) or a constant number of parties (<PERSON><PERSON> et al. CRYPTO ‘08). Conceptually, our protocols are obtained via a new compiler from a passively-secure protocol for a distributed multiplication functionality\\(\\mathcal{F}_{\\scriptscriptstyle \\mathrm {MULT}}\\), to an actively-secure protocol for general functionalities. Roughly,\\(\\mathcal{F}_{\\scriptscriptstyle \\mathrm {MULT}}\\)is parameterized by a linear-secret sharing scheme\\(\\mathcal{S}\\), where it takes\\(\\mathcal{S}\\)-shares of two secrets and returns\\(\\mathcal{S}\\)-shares of their product. We show that our compilation is concretely efficient for sufficiently large fields, resulting in an overhead of 2 when securely computing natural circuits. Our compiler has two additional benefits: (1) it can rely onanypassive implementation of\\(\\mathcal{F}_{\\scriptscriptstyle \\mathrm {MULT}}\\), which, besides the standard implementation based on OT (for boolean) and OLE (for arithmetic) allows us to rely on implementations based on threshold cryptosystems (Cramer et al. Eurocrypt ‘01); and (2) it can rely on weaker-than-passive (i.e., imperfect/leaky) implementations, which in some parameter regimes yield actively-secure protocols with overhead less than 2. Instantiating this compiler with an “honest-majority” implementations of\\(\\mathcal{F}_{\\scriptscriptstyle \\mathrm {MULT}}\\), we obtain the first honest-majority protocol with optimal corruption threshold for boolean circuits with constant communication overhead over the best passive protocol (Damgård and Nielsen, CRYPTO ‘07).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_7"}, {"primary_key": "2580312", "vector": [], "sparse_vector": [], "title": "Stacked Garbling for Disjunctive Zero-Knowledge Proofs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Zero-knowledge (ZK) proofs (ZKP) have received wide attention, focusing on non-interactivity, short proof size, and fast verification time. We focus on the fastest total proof time, in particular for large Boolean circuits. Under this metric, Garbled Circuit (GC)-based ZKP (<PERSON><PERSON><PERSON><PERSON> et al., [JKO], CCS 2013) remained the state-of-the-art technique due to the low-constant linear scaling of computing the garbling. We improve GC-ZKP for proof statements with conditional clauses. Our communication is proportional to the longest branch rather than to the entire proof statement. This is most useful when the number\\(m \\)of branches is large, resulting in up to factor\\(m \\times \\)improvement over JKO. In our proof-of-conceptillustrative application, prover\\(\\mathsf {P}\\)demonstrates knowledge of a bug in a codebase consisting ofany numberof snippets ofactual C code. Our computation cost is linear in the size of the codebase and communication isconstant in the number of snippets. That is, we require only enough communication for a single largest snippet! Ourconceptual contributionisstacked garbling for ZK, a privacy-free circuit garbling scheme that can be used with the JKO GC-ZKP protocol to construct more efficient ZKP. Given a Boolean circuit\\(\\mathcal {C}\\)and computational security parameter\\(\\kappa \\), our garbling is\\(L\\cdot \\kappa \\)bits long, whereLis the length of the longest execution path in\\(\\mathcal {C}\\). All prior concretely efficient garbling schemes produce garblings of size\\(|\\mathcal {C} |\\cdot \\kappa \\). The computational cost of our scheme is not increased over prior state-of-the-art. We implement our GC-ZKP and demonstrate significantly improved (\\(m \\times \\)over JKO) ZK performance for functions with branching factor\\(m \\). Compared with recent ZKP (STARK, Libra, KKW, Ligero, Aurora, Bulletproofs), our scheme offers much better proof times for larger circuits (35-\\(1000\\times \\)or more, depending on circuit size and compared scheme). For our illustrative application, we consider four C code snippets, each of about 30–50 LOC; one snippet allows an invalid memory dereference. The entire proof takes 0.15 s and communication is 1.5 MB.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_19"}, {"primary_key": "2580313", "vector": [], "sparse_vector": [], "title": "Finding Hash Collisions with Quantum Computers by Using Differential Trails with Smaller Probability than Birthday Bound.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we spot light on dedicated quantum collision attacks on concrete hash functions, which has not received much attention so far. In the classical setting, the generic complexity to find collisions of ann-bit hash function is\\(O(2^{n/2})\\), thus classical collision attacks based on differential cryptanalysis such as rebound attacks build differential trails with probability higher than\\(2^{-n/2}\\). By the same analogy, generic quantum algorithms such as the BHT algorithm find collisions with complexity\\(O(2^{n/3})\\). With quantum algorithms, a pair of messages satisfying a differential trail with probabilitypcan be generated with complexity\\(p^{-1/2}\\). Hence, in the quantum setting, some differential trails with probability up to\\(2^{-2n/3}\\)that cannot be exploited in the classical setting may be exploited to mount a collision attack in the quantum setting. In particular, the number of attacked rounds may increase. In this paper, we attack two international hash function standards: AES-MMO and Whirlpool. For AES-MMO, we present a 7-round differential trail with probability\\(2^{-80}\\)and use it to find collisions with a quantum version of the rebound attack, while only 6 rounds can be attacked in the classical setting. For Whirlpool, we mount a collision attack based on a 6-round differential trail from a classical rebound distinguisher with a complexity higher than the birthday bound. This improves the best classical attack on 5 rounds by 1. We also show that those trails are optimal in our approach. Our results have two important implications. First, there seems to exist a common belief that classically secure hash functions will remain secure against quantum adversaries. Indeed, several second-round candidates in the NIST post-quantum competition use existing hash functions, say SHA-3, as quantum secure ones. Our results disprove this common belief. Second, our observation suggests that differential trail search should not stop with probability\\(2^{-n/2}\\)but should consider up to\\(2^{-2n/3}\\). Hence it deserves to revisit the previous differential trail search activities.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_9"}, {"primary_key": "2580314", "vector": [], "sparse_vector": [], "title": "Combiners for Functional Encryption, Unconditionally.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Functional encryption (FE) combiners allow one to combine many candidates for a functional encryption scheme, possibly based on different computational assumptions, into another functional encryption candidate with the guarantee that the resulting candidate is secure as long as at least one of the original candidates is secure. The fundamental question in this area is whether FE combiners exist. There have been a series of works <PERSON><PERSON><PERSON> et al. (CRYPTO ’16), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (EUROCRYPT ’17), <PERSON><PERSON><PERSON> et al. (TCC ’19) on constructing FE combiners from various assumptions. We give the firstunconditionalconstruction of combiners for functional encryption, resolving this question completely. Our construction immediately implies an unconditional universal functional encryption scheme, an FE scheme that is secure if such an FE scheme exists. Previously such results either relied on algebraic assumptions or required subexponential security assumptions.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_6"}, {"primary_key": "2580315", "vector": [], "sparse_vector": [], "title": "Implementing Grover Oracles for Quantum Key Search on AES and LowMC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>’s search algorithm gives a quantum attack against block ciphers by searching for a key that matches a small number of plaintext-ciphertext pairs. This attack uses\\(O(\\sqrt{N})\\)calls to the cipher to search a key space of sizeN. Previous work in the specific case of AES derived the full gate cost by analyzing quantum circuits for the cipher, but focused on minimizing the number of qubits. In contrast, we study the cost of quantum key search attacks under a depth restriction and introduce techniques that reduce the oracle depth, even if it requires more qubits. As cases in point, we design quantum circuits for the block ciphers AES and LowMC. Our circuits give a lower overall attack cost in both the gate count and depth-times-width cost models. In NIST’s post-quantum cryptography standardization process, security categories are defined based on the concrete cost of quantum key search against AES. We present new, lower cost estimates for each category, so our work has immediate implications for the security assessment of post-quantum cryptography. As part of this work, we release Q# implementations of the full Grover oracle for AES-128, -192, -256 and for the three LowMC instantiations used in Picnic, including unit tests and code to reproduce our quantum resource estimates. To the best of our knowledge, these are the first two such full implementations and automatic resource estimations.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_10"}, {"primary_key": "2580316", "vector": [], "sparse_vector": [], "title": "Efficient Constructions for Almost-Everywhere Secure Computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem ofalmost-everywhere reliable message transmission; a key component in designing efficient and secure Multi-party Computation (MPC) protocols for sparsely connected networks. The goal is to design low-degree networks which allow a large fraction of honest nodes to communicate reliably even when a small constant fraction of nodes experience byzantine corruption and deviate arbitrarily from the assigned protocol. In this paper, we achieve a\\(\\log \\)-degree network with a polylogarithmic work complexity protocol, thereby improving over the state-of-the-art result of Chandranet al.(ICALP 2010) who required a polylogarithmic-degree network and had a linear work complexity. In addition, we also achieve: A work efficient version of Dwork et al.’s (STOC 1986) butterfly network. An improvement upon the state of the art protocol of <PERSON><PERSON><PERSON> and <PERSON> (Information Processing Letters 1996) in the randomized corruption model—both in work-efficiency and in resilience.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_6"}, {"primary_key": "2580317", "vector": [], "sparse_vector": [], "title": "Compact NIZKs from Standard Assumptions on Bilinear Maps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A non-interactive zero-knowledge (NIZK) protocol enables a prover to convince a verifier of the truth of a statement without leaking any other information by sending a single message. The main focus of this work is on exploring short pairing-based NIZKs for all\\(\\mathbf{NP} \\)languages based on standard assumptions. In this regime, the seminal work of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (<PERSON><PERSON>12) (GOS-NIZK) is still considered to be the state-of-the-art. Although fairly efficient, one drawback of GOS-NIZK is that the proof size ismultiplicativein the circuit size computing the\\(\\mathbf{NP} \\)relation. That is, the proof size grows by\\(O(|C|\\kappa )\\), whereCis the circuit for the\\(\\mathbf{NP} \\)relation and\\(\\kappa \\)is the security parameter. By now, there have been numerous follow-up works focusing on shortening the proof size of pairing-based NIZKs, however, thus far, all works come at the cost of relying either on a non-standard knowledge-type assumption or a non-staticq-type assumption. Specifically, improving the proof size of the original GOS-NIZK under the same standard assumption has remained as an open problem. Our main result is a construction of a pairing-based NIZK for all of\\(\\mathbf{NP} \\)whose proof size isadditivein |C|, that is, the proof size only grows by\\(|C| +\\mathsf {poly}(\\kappa )\\), based on the decisional linear (DLIN) assumption. Since the DLIN assumption is the same assumption underlying GOS-NIZK, our NIZK is a strict improvement on their proof size. As by-products of our main result, we also obtain the following two results: (1) We construct aperfectly zero-knowledgeNIZK (NIPZK) for\\(\\mathbf{NP} \\)relations computable in\\(\\mathbf{NC} ^1\\)with proof size\\(|w| \\cdot \\mathsf {poly}(\\kappa )\\)where |w| is the witness length based on the DLIN assumption. This is the first pairing-based NIPZK for a non-trivial class of\\(\\mathbf{NP} \\)languages whose proof size is independent of |C| based on a standard assumption. (2) We construct a universally composable (UC) NIZK for\\(\\mathbf{NP} \\)relations computable in\\(\\mathbf{NC} ^1\\)in the erasure-free adaptive setting whose proof size is\\(|w| \\cdot \\mathsf {poly}(\\kappa )\\)from the DLIN assumption. This is an improvement over the recent result of Katsumata, Nishimaki, Yamada, and Yamakawa (CRYPTO’19), which gave a similar result based on a non-staticq-type assumption. The main building block for all of our NIZKs is a constrained signature scheme withdecomposable online-offline efficiency. This is a property which we newly introduce in this paper and construct from the DLIN assumption. We believe this construction is of an independent interest.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_13"}, {"primary_key": "2580318", "vector": [], "sparse_vector": [], "title": "Key-Homomorphic Pseudorandom Functions from LWE with Small Modulus.", "authors": ["<PERSON>"], "summary": "Pseudorandom functions (PRFs) are fundamental objects in cryptography that play a central role in symmetric-key cryptography. Although PRFs can be constructed from one-way functions generically, these black-box constructions are usually inefficient and require deep circuits to evaluate compared to direct PRF constructions that rely on specific algebraic assumptions. From lattices, one can directly construct PRFs from the Learning with Errors (LWE) assumption (or its ring variant) using the result of <PERSON>, <PERSON><PERSON>, and <PERSON> (Eurocrypt 2012) and its subsequent works. However, all existing PRFs in this line of work rely on the hardness of the LWE problem where the associated modulus is super-polynomial in the security parameter. In this work, we provide two new PRF constructions from the LWE problem. In each of these constructions, each focuses on either minimizing the depth of its evaluation circuit or providing key-homomorphism while relying on the hardness of the LWE problem with either a polynomial modulus or nearly polynomial modulus. Along the way, we introduce a new variant of the LWE problem called the Learning with Rounding and Errors (LWRE) problem. We show that for certain settings of parameters, the LWRE problem is as hard as the LWE problem. We then show that the hardness of the LWRE problem naturally induces a pseudorandom synthesizer that can be used to construct a low-depth PRF. The techniques that we introduce to study the LWRE problem can then be used to derive variants of existing key-homomorphic PRFs whose security can be reduced from the hardness of the LWE problem with a much smaller modulus.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_20"}, {"primary_key": "2580319", "vector": [], "sparse_vector": [], "title": "Tight Security Bounds for Double-Block Hash-then-Sum MACs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we study the security of deterministic MAC constructions with a double-block internal state, captured by thedouble-block hash-then-sum(\\(\\mathsf {DbHtS}\\)) paradigm. Most\\(\\mathsf {DbHtS}\\)constructions, including\\(\\mathsf {PolyMAC}\\),\\(\\mathsf {SUM\\text {-}ECBC}\\),\\(\\mathsf {PMAC\\text {-}Plus}\\),\\(\\mathsf {3kf9}\\)and\\(\\mathsf {LightMAC\\text {-}Plus}\\), have been proved to be pseudorandom up to\\(2^{\\frac{2n}{3}}\\)queries when they are instantiated with ann-bit block cipher, while the best known generic attacks require\\(2^{\\frac{3n}{4}}\\)queries. We close this gap by proving the PRF-security of\\(\\mathsf {DbHtS}\\)constructions up to\\(2^{\\frac{3n}{4}}\\)queries (ignoring the maximum message length). The core of the security proof is to refine Mirror theory that systematically estimates the number of solutions to a system of equations and non-equations, and apply it to prove the security of the finalization function. Then we identify security requirements of the internal hash functions to ensure 3n/4-bit security of the resulting constructions when combined with the finalization function. Within this framework, we prove the security of\\(\\mathsf {DbHtS}\\)whose internal hash function is given as the concatenation of a universal hash function using two independent keys. This class of constructions include\\(\\mathsf {PolyMAC}\\)and\\(\\mathsf {SUM\\text {-}ECBC}\\). Moreover, we prove the security of\\(\\mathsf {PMAC\\text {-}Plus}\\),\\(\\mathsf {3kf9}\\)and\\(\\mathsf {LightMAC\\text {-}Plus}\\)up to\\(2^{\\frac{3n}{4}}\\)queries.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_16"}, {"primary_key": "2580320", "vector": [], "sparse_vector": [], "title": "Measure-Rewind-Measure: Tighter Quantum Random Oracle Model Proofs for One-Way to Hiding and CCA Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Shifeng Sun"], "summary": "We introduce a new technique called ‘Measure-Rewind-Measure’ (MRM) to achieve tighter security proofs in the quantum random oracle model (QROM). We first apply our MRM technique to derive a new security proof for a variant of the ‘double-sided’ quantum One-Way to Hiding Lemma (O2H) of <PERSON><PERSON> et al. [TCC 2019] which, for the first time, avoids the square-root advantage loss in the security proof. In particular, it bypasses a previous ‘impossibility result’ of <PERSON>, <PERSON> and <PERSON> [IACR eprint 2019]. We then apply our new O2H Lemma to give a new tighter security proof for the Fuji<PERSON>-<PERSON> transform for constructing a strong (\\({\\mathsf {IND}}{\\text {-}}{\\mathsf {CCA}}\\)) Key Encapsulation Mechanism (KEM) from a weak (\\({\\mathsf {IND}}{\\text {-}}{\\mathsf {CPA}}\\)) public-key encryption scheme satisfying a mild injectivity assumption.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_24"}, {"primary_key": "2580321", "vector": [], "sparse_vector": [], "title": "New Constructions of Statistical NIZKs: Dual-Mode DV-NIZKs and More.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "Non-interactive zero-knowledge proofs (NIZKs) are important primitives in cryptography. A major challenge since the early works on NIZKs has been to construct NIZKs with astatisticalzero-knowledge guarantee against unbounded verifiers. In the common reference string (CRS) model, such “statistical NIZK arguments” are currently known from\\(k\\text {-}\\mathsf {Lin} \\)in a pairing-group and from\\(\\mathsf {LWE}\\). In the (reusable) designated-verifier model (DV-NIZK), where a trusted setup algorithm generates a reusable verification key for checking proofs, we also have a construction from\\(\\mathsf {DCR}\\). If we relax our requirements tocomputationalzero-knowledge, we additionally have NIZKs from factoring and\\(\\mathsf {CDH}\\)in a pairing group in the CRS model, and from nearlyallassumptions that imply public-key encryption (e.g.,\\(\\mathsf {CDH}\\),\\(\\mathsf {LPN}\\),\\(\\mathsf {LWE}\\)) in the designated-verifier model. Thus, there still remains a gap in our understanding of statistical NIZKs in both the CRS and the designated-verifier models. In this work, we develop new techniques for constructing statistical NIZK arguments. First, we construct statistical DV-NIZK arguments from the\\(k\\text {-}\\mathsf {Lin} \\)assumption inpairing-freegroups, the\\(\\mathsf {QR}\\)assumption, and the\\(\\mathsf {DCR}\\)assumption. These are the first constructions in pairing-free groups and from\\(\\mathsf {QR}\\)that satisfy statistical zero-knowledge. All of our constructions are secure even if the verification key is chosen maliciously (i.e., they are “malicious-designated-verifier” NIZKs), and moreover, they satisfy a “dual-mode” property where the CRS can be sampled from two computationally indistinguishable distributions: one distribution yieldsstatistical DV-NIZK argumentswhile the other yieldscomputational DV-NIZK proofs. We then show how to adapt our\\(k\\text {-}\\mathsf {Lin} \\)construction in a pairing group to obtain newpublicly-verifiablestatistical NIZK arguments from pairings with aqualitatively weakerassumption than existing constructions of pairing-based statistical NIZKs. Our constructions follow the classic paradigm of Feige, Lapidot, and Shamir (FLS). While the FLS framework has traditionally been used to construct computational (DV)-NIZK proofs, we newly show that the same framework can be leveraged to construct dual-mode (DV)-NIZKs.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_14"}, {"primary_key": "2580322", "vector": [], "sparse_vector": [], "title": "Compact Adaptively Secure ABE from k-Lin: Beyond NC1 and Towards NL.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a new general framework for constructingcompactandadaptively secureattribute-based encryption (ABE) schemes fromk-Lin in asymmetric bilinear pairing groups. Previously, the only construction [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, Eurocrypt ’19] that simultaneously achieves compactness and adaptive security from static assumptions supports policies represented byBoolean formulae. Our framework enables supporting more expressive policies represented byarithmetic branching programs. Our framework extends to ABE for policies represented by uniform models of computation such as Turing machines. Such policies enjoy the feature of being applicable to attributes of arbitrary lengths. We obtain the first compact adaptively secure ABE for deterministic and non-deterministic finite automata (DFA and NFA) fromk-Lin, previously unknown from any static assumptions. Beyond finite automata, we obtain the first ABE for large classes of uniform computation, captured by deterministic and non-deterministiclogspaceTuring machines (the complexity classes\\(\\mathsf {L}\\)and\\(\\mathsf {NL}\\)) based onk-Lin. Our ABE scheme has compact secret keys of size linear in the description size of the Turing machineM. The ciphertext size grows linearly in the input length, but also linearly in the time complexity, and exponentially in the space complexity. Irrespective of compactness, we stress that our scheme is the first that supports large classes of Turing machines based solely on standard assumptions. In comparison, previous ABE for general Turing machines all rely on strong primitives related to indistinguishability obfuscation.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_9"}, {"primary_key": "2580323", "vector": [], "sparse_vector": [], "title": "Statistical ZAPR Arguments from Bilinear Maps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON> (FOCS ’00) defined ZAPs as 2-message witness-indistinguishable proofs that are public-coin. We relax this toZAPs with private randomness(ZAPRs), where the verifier can use private coins to sample the first message (independently of the statement being proved), but the proof must remain publicly verifiable given only the protocol transcript. In particular, ZAPRs arereusable, meaning that the first message can be reused for multiple proofs without compromising security. Known constructions of ZAPs from trapdoor permutations or bilinear maps are only computationally WI (and statistically sound). Two recent results of Badrinarayana<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [EUROCRYPT ’20] construct the firststatistical ZAP arguments, which are statistically WI (and computationally sound), from the quasi-polynomial LWE assumption. Here, we constructstatistical ZAPR argumentsfrom the quasi-polynomial decision-linear (DLIN) assumption on groups with a bilinear map. Our construction relies on a combination of several tools, including the Groth-Ostrovsky-Sahai NIZK and NIWI [EUROCRYPT ’06, CRYPTO ’06, JACM ’12], “sometimes-binding statistically hiding commitments” [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EUROCRYPT ’18] and the “MPC-in-the-head” technique [<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>-<PERSON>, STOC ’07].", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_21"}, {"primary_key": "2580324", "vector": [], "sparse_vector": [], "title": "Succinct Non-interactive Secure Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "We present the firstmaliciously secureprotocol forsuccinct non-interactive secure two-party computation(SNISC): Each player sends just a single message whose length is (essentially) independent of the running time of the function to be computed. The protocol does not require any trusted setup, satisfies superpolynomial-time simulation-based security (SPS), and is based on (subexponential) security of the Learning With Errors (LWE) assumption. We donotrely on SNARKs or “knowledge of exponent”-type assumptions. Since the protocol is non-interactive, the relaxation to SPS security is needed, as standard polynomial-time simulation is impossible; however, a slight variant of our main protocol yields a SNISC with polynomial-time simulation in the CRS model.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_8"}, {"primary_key": "2580325", "vector": [], "sparse_vector": [], "title": "Mind the Composition: Birthday Bound Attacks on EWCDMD and SoKAC21.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In an early version of CRYPTO’17, <PERSON><PERSON><PERSON> and <PERSON><PERSON> proposedEWCDMD, a dual ofEWCDM, and showedn-bit security, wherenis the block size of the underlying block cipher. In CRYPTO’19, <PERSON> et al. proposed permutation based designSoKAC21and showed 2n/3-bit security, wherenis the input size of the underlying permutation. In this paper we show birthday bound attacks onEWCDMDandSoKAC21, invalidating their security claims. Both attacks exploit an inherent composition nature present in the constructions. Motivated by the above two attacks exploiting the composition nature, we consider some generic relevant composition based constructions of ideal primitives (possibly in the ideal permutation and random oracle model) and present birthday bound distinguishers for them. In particular, we demonstrate a birthday bound distinguisher against (1) a secret random permutation followed by a public random function and (2) composition of two secret random functions. Our distinguishers forSoKAC21andEWCDMDare direct consequences of (1) and (2) respectively.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_8"}, {"primary_key": "2580326", "vector": [], "sparse_vector": [], "title": "Optimal Merging in Quantum k-xor and k-xor-sum Algorithms.", "authors": ["<PERSON>Plase<PERSON>", "<PERSON>"], "summary": "Thek-xor or Generalized Birthday Problem aims at finding, givenklists of bit-strings, ak-tuple among them XORing to 0. If the lists are unbounded, the best classical (exponential) time complexity has withstood since <PERSON>’s CRYPTO 2002 paper. If the lists are bounded (of the same size) and such that there is a single solution, thedissection algorithmsof Dinuret al.(CRYPTO 2012) improve the memory usage over a simple meet-in-the-middle. In this paper, we study quantum algorithms for thek-xor problem. With unbounded lists and quantum access, we improve previous work by <PERSON><PERSON> al.(ASIACRYPT 2018) for almost allk. Next, we extend our study to lists of any size and with classical access only. We define a set of “merging trees” which represent the best known strategies for quantum and classical merging ink-xor algorithms, and prove that our method is optimal among these. Our complexities are confirmed by a Mixed Integer Linear Program that computes the best strategy for a givenk-xor problem. All our algorithms apply also when considering modular additions instead of bitwise xors. This framework enables us to give new improved quantumk-xor algorithms for allkand list sizes. Applications include the subset-sum problem, LPN with limited memory and the multiple-encryption problem.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_11"}, {"primary_key": "2580327", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Leakage-Resilient Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Threshold secret sharing allows a dealer to split a secret intonshares such that any authorized subset of cardinality at leasttof those shares efficiently reveals the secret, while at the same time any unauthorized subset of cardinality less thantcontains no information about the secret. Leakage-resilience additionally requires that the secret remains hidden even if one is given a bounded amount of additional leakage from every share. In this work, we study leakage-resilient secret sharing schemes and prove a lower bound on the share size and the required amount of randomness of any information-theoretically secure scheme. We prove that for any information-theoretically secure leakage-resilient secret sharing scheme either the amount of randomness across all shares or the share size has to be linear inn. More concretely, for a secret sharing scheme withp-bit long shares,\\(\\ell \\)-bit leakage per share, where\\(\\widehat{t}\\)shares uniquely define the remaining\\(n - \\widehat{t}\\)shares, it has to hold that We use this lower bound to gain further insights into a question that was recently posed by <PERSON><PERSON><PERSON><PERSON> et al. (CRYPTO’18), who ask to what extend existing regular secret sharing schemes already provide protection against leakage. The authors proved that <PERSON><PERSON><PERSON>’s secret sharing is 1-bit leakage-resilient for reconstruction thresholds\\(t \\ge 0.85n\\)and conjectured that it is also 1-bit leakage-resilient for any other threshold that is a constant fraction of the total number of shares. We do not disprove their conjecture, but show that it is the best one could possibly hope for. Concretely, we show that for large enoughnand any constant\\(0< c < 1\\)it holds that Shamir’s secret sharing scheme isnotleakage-resilient for. In contrast to the setting with information-theoretic security, we show that our lower bound does not hold in the computational setting. That is, we show how to construct a leakage-resilient secret sharing scheme in the random oracle model that is secure against computationally bounded adversaries and violates the lower bound stated above.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_20"}, {"primary_key": "2580328", "vector": [], "sparse_vector": [], "title": "He Gives <PERSON><PERSON><PERSON><PERSON> on the CSIDH.", "authors": ["<PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> proposedCSIDH(pronounced “sea-side”) as a candidate post-quantum “commutative group action.” It has attracted much attention and interest, in part because it enables noninteractive Diffie–Hellman-like key exchange with quite small communication. Subsequently, CSIDH has also been used as a foundation for digital signatures. In 2003–04, <PERSON><PERSON><PERSON> and then <PERSON><PERSON> gave asymptotically subexponential quantum algorithms for “hidden shift” problems, which can be used to recover the CSIDH secret key from a public key. In late 2011, <PERSON><PERSON><PERSON> gave a follow-up quantum algorithm called thecollimation sieve(“c-sieve” for short), which improves the prior ones, in particular by using exponentially less quantum memory and offering more parameter tradeoffs. While recent works have analyzed the concrete cost of the original algorithms (and variants) against CSIDH, nothing of this nature was previously available for the c-sieve. This work fills that gap. Specifically, we generalize <PERSON><PERSON><PERSON>’s collimation sieve to work for arbitrary finite cyclic groups, provide some practical efficiency improvements, give a classical (i.e., non-quantum) simulator, run experiments for a wide range of parameters up to the actual CSIDH-512 group order, and concretely quantify the complexity of the c-sieve against CSIDH. Our main conclusion is that the proposed CSIDH parameters provide relatively little quantum security beyond what is given by the cost of quantumly evaluating the CSIDH group action itself (on a uniform superposition). For example, the cost of CSIDH-512 key recovery is only about\\(2^{16}\\)quantum evaluations using\\(2^{40}\\)bits of quantumly accessibleclassicalmemory (plus relatively small other resources). This improves upon a prior estimate of\\(2^{32.5}\\)evaluations and\\(2^{31}\\)qubits ofquantummemory, for a variant of <PERSON><PERSON><PERSON>’s original sieve. Under the plausible assumption that quantum evaluation does not cost much more than what is given by a recent “best case” analysis, CSIDH-512 can therefore be broken using significantly less than\\(2^{64}\\)quantum T-gates. This strongly invalidates its claimed NIST level 1 quantum security, especially when accounting for the MAXDEPTH restriction. Moreover, under analogous assumptions for CSIDH-1024 and -1792, which target higher NIST security levels, except near the high end of the MAXDEPTH range even these instantiations fall short of level 1.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_16"}, {"primary_key": "2580329", "vector": [], "sparse_vector": [], "title": "PSI from PaXoS: Fast, Malicious Private Set Intersection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a 2-party private set intersection (PSI) protocol which provides security against malicious participants, yet is almost as fast as the fastest knownsemi-honestPSI protocol of <PERSON><PERSON><PERSON> et al. (CCS 2016). Our protocol is based on a new approach for two-party PSI, which can be instantiated to provide security against either malicious or semi-honest adversaries. The protocol is unique in that the only difference between the semi-honest and malicious versions is an instantiation with different parameters for a linear error-correction code. It is also the first PSI protocol which is concretely efficient while having linear communication and security against malicious adversaries, while running in the OT-hybrid model (assuming a non-programmable random oracle). State of the art semi-honest PSI protocols take advantage of cuckoo hashing, but it has proven a challenge to use cuckoo hashing for malicious security. Our protocol is the first to use cuckoo hashing for malicious-secure PSI. We do so via a new data structure, called a probe-and-XOR of strings (\\(\\mathsf{PaXoS}\\)), which may be of independent interest. This abstraction captures important properties of previous data structures, most notably garbled Bloom filters. While an encoding by a garbled Bloom filter is larger by a factor of\\(\\varOmega (\\lambda )\\)than the original data, we describe a significantly improved\\(\\mathsf{PaXoS}\\)based on cuckoo hashing that achieves constant rate while being no worse in other relevant efficiency measures.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45724-2_25"}, {"primary_key": "2580330", "vector": [], "sparse_vector": [], "title": "Generic-Group Delay Functions Require Hidden-Order Groups.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite the fundamental importance of delay functions, underlying both the classic notion of a time-lock puzzle and the more recent notion of a verifiable delay function, the only known delay function that offers both sufficient structure for realizing these two notions and a realistic level of practicality is the “iterated squaring” construction of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>. This construction, however, is based on rather strong assumptions in groups of hidden orders, such as the RSA group (which requires a trusted setup) or the class group of an imaginary quadratic number field (which is still somewhat insufficiently explored from the cryptographic perspective). For more than two decades, the challenge of constructing delay functions in groups of known orders, admitting a variety of well-studied instantiations, has eluded the cryptography community. In this work we prove that there are no constructions of generic-group delay functions in cyclic groups of known orders: We show that for any delay function that does not exploit any particular property of the representation of the underlying group, there exists an attacker that completely breaks the function’s sequentiality when given the group’s order. As any time-lock puzzle and verifiable delay function give rise to a delay function, our result holds for these two notions we well, and explains the lack of success in resolving the above-mentioned long-standing challenge. Moreover, our result holds even if the underlying group is equipped with ad-linear map, for any constant\\(d \\ge 2\\)(and even for super-constant values ofdunder certain conditions).", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45727-3_6"}, {"primary_key": "2580331", "vector": [], "sparse_vector": [], "title": "Fault Template Attacks on Block Ciphers Exploiting Fault Propagation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fault attacks (FA) are one of the potent practical threats to modern cryptographic implementations. Over the years the FA techniques have evolved, gradually moving towards the exploitation of device-centric properties of the faults. In this paper, we exploit the fact that activation and propagation of a fault through a given combinational circuit (i.e., observability of a fault) is data-dependent. Next, we show that this property of combinational circuits leads to powerfulFault Template Attacks (FTA), even for implementations having dedicated protections against both power and fault-based vulnerabilities. The attacks found in this work are applicable even if the fault injection is made at the middle rounds of a block cipher, which are out of reach for most of the other existing fault analysis strategies. Quite evidently, they also work for a known-plaintext scenario. Moreover, the middle round attacks are entirely blind in the sense that no access to the ciphertexts (correct/faulty) or plaintexts are required. The adversary is only assumed to have the power of repeating an unknown plaintext several times. Practical validation over a hardware implementation of SCA-FA protected PRESENT, and simulated evaluation on a public software implementation of protected AES prove the efficacy of the proposed attacks.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_22"}, {"primary_key": "2580332", "vector": [], "sparse_vector": [], "title": "Mathematics and Cryptography: A Marriage of Convenience? - Invited Talk.", "authors": ["<PERSON>"], "summary": "Mathematics and cryptography have a long history together, with the ups and downs inherent in any long relationship. Whether it is a marriage of convenience or a love match, their progeny have lives of their own and have had an impact on the world. This invited lecture will briefly recall some high points from the past, give speculation and encouragement for the future of this marriage, and give counseling on how to improve communication, resolve conflicts, and play well together, based on personal experience and lessons learned.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_1"}, {"primary_key": "2580333", "vector": [], "sparse_vector": [], "title": "Friet: An Authenticated Encryption Scheme with Built-in Fault Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work we present a duplex-based authenticated encryption scheme\\(\\textsc {Friet}\\)based on a new permutation called\\(\\textsc {Friet-P}\\). We designed\\(\\textsc {Friet-P}\\)with a novel approach for cryptographic permutations and block ciphers that takes fault-attack resistance into account and that we introduce in this paper. In this method, we build a permutation\\({f_C}\\)to beembeddedin a larger one,\\(f\\). First, we define\\(f\\)as a sequence of steps that allabidea chosen error-correcting code\\(C\\), i.e., that map\\(C\\)-codewords to\\(C\\)-codewords. Then, we embed\\({f_C}\\)in\\(f\\)by first encoding its input to an element ofC, applying\\(f\\)and then decoding back fromC. This last step detects a fault when the output of\\(f\\)is not in\\(C\\). We motivate the design of the permutation we use in\\(\\textsc {Friet}\\)and report on performance in soft- and hardware. We evaluate the fault-detection capabilities of the software and simulated hardware implementations with attacks. Finally, we perform a leakage evaluation. Our code is available athttps://github.com/thisimon/Friet.git.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_21"}, {"primary_key": "2580334", "vector": [], "sparse_vector": [], "title": "Impossibility Results for Lattice-Based Functional Encryption Schemes.", "authors": ["<PERSON><PERSON>"], "summary": "Functional Encryption denotes a form of encryption where a master secret key-holder can control which functions a user can evaluate on encrypted data. Learning With Errors (LWE) (<PERSON><PERSON>, STOC’05) is known to be a useful cryptographic hardness assumption which implies strong primitives such as, for example, fully homomorphic encryption (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FOCS’11) and lockable obfuscation (<PERSON><PERSON> et al., <PERSON><PERSON><PERSON> et al., FOCS’17). Despite its stre ngth, however, there is just a limited number of functional encryption schemes which can be based on LWE. In fact, there are functional encryption schemes which can be achieved by using pairings but for which no secure instantiations from lattice-based assumptions are known: function-hiding inner product encryption (<PERSON>, <PERSON><PERSON> et al., CRYPTO’17) and compact quadratic functional encryption (<PERSON><PERSON><PERSON> et al., CRYPTO’18). This raises the question whether there are some mathematical barriers which hinder us from realizing function-hiding and compact functional encryption schemes from lattice-based assumptions as LWE. To study this problem, we prove an impossibility result for function-hiding functional encryption schemes which meet some algebraic restrictions at ciphertext encryption and decryption. Those restrictions are met by a lot of attribute-based, identity-based and functional encryption schemes whose security stems from LWE. Therefore, we see our results as important indications why it is hard to construct new functional encryption schemes from LWE and which mathematical restrictions have to be overcome to construct secure lattice-based functional encryption schemes for new functionalities.", "published": "2020-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-45721-1_7"}]