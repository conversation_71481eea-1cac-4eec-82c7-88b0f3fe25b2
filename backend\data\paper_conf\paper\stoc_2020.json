[{"primary_key": "2685911", "vector": [], "sparse_vector": [], "title": "A robust version of Hegedus&apos;s lemma, with applications.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>'s lemma is the following combinatorial statement regarding polynomials over finite fields. Over a field F of characteristic p > 0 and for q a power of p, the lemma says that any multilinear polynomial P∈ F[x 1,…,x n ] of degree less than q that vanishes at all points in {0,1} n of Hamming weight k∈ [q,n−q] must also vanish at all points in {0,1} n of weight k + q. This lemma was used by <PERSON><PERSON><PERSON><PERSON> (2009) to give a solution to <PERSON><PERSON><PERSON>'s problem, an extremal problem about set systems; by <PERSON><PERSON>, <PERSON> and <PERSON> (2018) to improve the best-known multilinear circuit lower bounds; and by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> (2019) to prove optimal lower bounds against depth-2 threshold circuits for computing some symmetric functions. In this paper, we formulate a robust version of <PERSON><PERSON><PERSON><PERSON>'s lemma. Informally, this version says that if a polynomial of degree o(q) vanishes at most points of weight k, then it vanishes at many points of weight k+q. We prove this lemma and give the following three different applications.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384328"}, {"primary_key": "2685912", "vector": [], "sparse_vector": [], "title": "Testing noisy linear functions for sparsity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the following basic inference problem: there is an unknown high-dimensional vector w ∈ ℝ n , and an algorithm is given access to labeled pairs (x,y) where x ∈ ℝ n is a measurement and y = w · x + noise. What is the complexity of deciding whether the target vector w is (approximately) k-sparse? The recovery analogue of this problem — given the promise that w is sparse, find or approximate the vector w — is the famous sparse recovery problem, with a rich body of work in signal processing, statistics, and computer science.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384239"}, {"primary_key": "2685913", "vector": [], "sparse_vector": [], "title": "All non-trivial variants of 3-LDT are equivalent.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The popular 3-SUM conjecture states that there is no strongly subquadratic time algorithm for checking if a given set of integers contains three distinct elements that sum up to zero. A closely related problem is to check if a given set of integers contains distinct x 1, x 2, x 3 such that x 1+x 2=2x 3. This can be reduced to 3-SUM in almost-linear time, but surprisingly a reverse reduction establishing 3-SUM hardness was not known.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384275"}, {"primary_key": "2685914", "vector": [], "sparse_vector": [], "title": "On the <PERSON><PERSON><PERSON> conjecture for submodular valuations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider incentive compatible mechanisms for a domain that is very close to the domain of scheduling n unrelated machines: the single exception is that the valuation of just one machine is submodular. For the scheduling problem with such cost functions, we give a lower bound of Ω(√n) on the approximation ratio of incentive compatible deterministic mechanisms. This is a strong information-theoretic impossibility result on the approximation ratio of mechanisms that provides strong evidence for the <PERSON><PERSON> conjecture. This is the first non-constant lower bound that assumes no restriction on the mechanism side; in contrast, all previous general results hold for only special classes of mechanisms such as local, strongly monotone, and anonymous mechanisms. Our approach is based on a novel multi-player characterization of appropriately selected instances that allows us to focus on particular type of algorithms, linear mechanisms, and it is a potential stepping stone towards the full resolution of the conjecture.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384299"}, {"primary_key": "2685915", "vector": [], "sparse_vector": [], "title": "Estimating normalizing constants for log-concave distributions: algorithms and lower bounds.", "authors": ["<PERSON><PERSON> Ge", "<PERSON>", "<PERSON><PERSON><PERSON> Lu"], "summary": "Estimating the normalizing constant of an unnormalized probability distribution has important applications in computer science, statistical physics, machine learning, and statistics. In this work, we consider the problem of estimating the normalizing constant Z=∫ℝ d e −f(x) d x to within a multiplication factor of 1 ± ε for a µ-strongly convex and L-smooth function f, given query access to f(x) and ∇ f(x). We give both algorithms and lowerbounds for this problem. Using an annealing algorithm combined with a multilevel Monte Carlo method based on underdamped Langevin dynamics, we show that O(d 4/3κ + d 7/6κ7/6/ε2) queries to ∇ f are sufficient, where κ= L / µ is the condition number. Moreover, we provide an information theoretic lowerbound, showing that at least d 1−o(1)/ε2−o(1) queries are necessary. This provides a first nontrivial lowerbound for the problem.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384289"}, {"primary_key": "2685916", "vector": [], "sparse_vector": [], "title": "Online primal dual meets online matching with stochastic rewards: configuration LP to the rescue.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (FOCS 2012) introduce the problem of online matching with stochastic rewards, where edges are associated with success probabilities and a match succeeds with the probability of the corresponding edge. It is one of the few online matching problems that have defied the randomized online primal dual framework by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (SODA 2013) thus far. This paper unlocks the power of randomized online primal dual in online matching with stochastic rewards by employing the configuration linear program rather than the standard matching linear program used in previous works. Our main result is a 0.572 competitive algorithm for the case of vanishing and unequal probabilities, improving the best previous bound of 0.534 by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (SODA 2015) and, in fact, is even better than the best previous bound of 0.567 by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (FOCS 2012) for the more restricted case of vanishing and equal probabilities. For vanishing and equal probabilities, we get a better competitive ratio of 0.576. Our results further generalize to the vertex-weighted case due to the intrinsic robustness of the randomized online primal dual analysis.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384294"}, {"primary_key": "2685917", "vector": [], "sparse_vector": [], "title": "(Semi)Algebraic proofs over ±1 variables.", "authors": ["<PERSON>"], "summary": "One of the major open problems in proof complexity is to prove lower bounds on AC 0[p]-Frege proof systems. As a step toward this goal <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> in a recent paper suggested to prove lower bounds on the size for Polynomial Calculus over the {± 1} basis. In this paper we show a technique for proving such lower bounds and moreover we also give lower bounds on the size for Sum-of-Squares over the {± 1} basis.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384288"}, {"primary_key": "2685918", "vector": [], "sparse_vector": [], "title": "Fast hashing with strong concentration bounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous work on tabulation hashing by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> from STOC'11 on simple tabulation and from SODA'13 on twisted tabulation offered Chernoff-style concentration bounds on hash based sums, e.g., the number of balls/keys hashing to a given bin, but under some quite severe restrictions on the expected values of these sums. The basic idea in tabulation hashing is to view a key as consisting of c=O(1) characters, e.g., a 64-bit key as c=8 characters of 8-bits. The character domain ς should be small enough that character tables of size |ς| fit in fast cache. The schemes then use O(1) tables of this size, so the space of tabulation hashing is O(|ς|). However, the concentration bounds by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> only apply if the expected sums are g‰ |ς|. To see the problem, consider the very simple case where we use tabulation hashing to throw n balls into m bins and want to analyse the number of balls in a given bin. With their concentration bounds, we are fine if n=m, for then the expected value is 1. However, if m=2, as when tossing n unbiased coins, the expected value n/2 is ≫ |ς| for large data sets, e.g., data sets that do not fit in fast cache. To handle expectations that go beyond the limits of our small space, we need a much more advanced analysis of simple tabulation, plus a new tabulation technique that we call tabulation-permutation hashing which is at most twice as slow as simple tabulation. No other hashing scheme of comparable speed offers similar Chernoff-style concentration bounds.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384259"}, {"primary_key": "2685919", "vector": [], "sparse_vector": [], "title": "New hardness results for planar graph problems in p and an algorithm for sparsest cut.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Sparsest Cut is a fundamental optimization problem that have been extensively studied. For planar inputs the problem is in P and can be solved in Õ(n 3 ) time if all vertex weights are 1. Despite a significant amount of effort, the best algorithms date back to the early 90's and can only achieve O(log n)-approximation in Õ(n) time or 3.5-approximation in Õ(n 2 ) time [<PERSON>, STOC92]. Our main result is an Ω(n 2−ε ) lower bound for Sparsest Cut even in planar graphs with unit vertex weights, under the (min, +)-Convolution conjecture, showing that approxima- tions are inevitable in the near-linear time regime. To complement the lower bound, we provide a 3.3-approximation in near-linear time, improving upon the 25-year old result of <PERSON> in both time and accuracy. We also show that our lower bound is not far from optimal by observing an exact algorithm with running time Õ(n 5/2 ) improving upon the Õ(n 3 ) algorithm of Park and Phillips [STOC93]. Our lower bound accomplishes a repeatedly raised challenge by being the first fine-grained lower bound for a natural planar graph problem in P. Building on our construction we prove near-quadratic lower bounds under SETH for variants of the closest pair problem in planar graphs, and use them to show that the popular Average-Linkage procedure for Hierarchical Clustering cannot be simulated in truly subquadratic time. At the core of our constructions is a diamond-like gadget that also settles the complexity of Diameter in distributed planar networks. We prove an Ω(n/ log n) lower bound on the number of communication rounds required to compute the weighted diameter of a network in the CONGET model, even when the underlying graph is planar and all nodes are D = 4 hops away from each other. This is the first poly(n) lower bound in the planar-distributed setting, and it complements the recent poly(D, log n) upper bounds of Li and Parter [STOC 2019] for (exact) unweighted diameter and for (1 + ε) approximate weighted diameter.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384310"}, {"primary_key": "2685920", "vector": [], "sparse_vector": [], "title": "Semi-algebraic proofs, IPS lower bounds, and the τ-conjecture: can a natural number be negative?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce the binary value principle which is a simple subset-sum instance expressing that a natural number written in binary cannot be negative, relating it to central problems in proof and algebraic complexity. We prove conditional superpolynomial lower bounds on the Ideal Proof System (IPS) refutation size of this instance, based on a well-known hypothesis by <PERSON><PERSON> and <PERSON><PERSON><PERSON> about the hardness of computing factorials, where IPS is the strong algebraic proof system introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018). Conversely, we show that short IPS refutations of this instance bridge the gap between sufficiently strong algebraic and semi-algebraic proof systems. Our results extend to full-fledged IPS the paradigm introduced in <PERSON> et al. (2016), whereby lower bounds against subsystems of IPS were obtained using restricted algebraic circuit lower bounds, and demonstrate that the binary value principle captures the advantage of semi-algebraic over algebraic reasoning, for sufficiently strong systems. Specifically, we show the following:", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384245"}, {"primary_key": "2685921", "vector": [], "sparse_vector": [], "title": "Improved analysis of higher order random walks and applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The motivation of this work is to extend the techniques of higher order random walks on simplicial complexes to analyze mixing times of Markov chains for combinatorial problems. Our main result is a sharp upper bound on the second eigenvalue of the down-up walk on a pure simplicial complex, in terms of the second eigenvalues of its links. We show some applications of this result in analyzing mixing times of Markov chains, including sampling independent sets of a graph and sampling common independent sets of two partition matroids.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384317"}, {"primary_key": "2685922", "vector": [], "sparse_vector": [], "title": "Improved bounds for the sunflower lemma.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A sunflower with r petals is a collection of r sets so that the intersection of each pair is equal to the intersection of all. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> proved the sunflower lemma: for any fixed r, any family of sets of size w, with at least about w w sets, must contain a sunflower. The famous sunflower conjecture is that the bound on the number of sets can be improved to c w for some constant c. In this paper, we improve the bound to about (logw) w . In fact, we prove the result for a robust notion of sunflowers, for which the bound we obtain is tight up to lower order terms.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384234"}, {"primary_key": "2685923", "vector": [], "sparse_vector": [], "title": "Quadratic speedup for finding marked vertices by quantum walks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A quantum walk algorithm can detect the presence of a marked vertex on a graph quadratically faster than the corresponding random walk algorithm (<PERSON><PERSON><PERSON><PERSON>, FOCS 2004). However, quantum algorithms that actually find a marked element quadratically faster than a classical random walk were only known for the special case when the marked set consists of just a single vertex, or in the case of some specific graphs. We present a new quantum algorithm for finding a marked vertex in any graph, with any set of marked vertices, that is (up to a log factor) quadratically faster than the corresponding classical random walk, resolving a question that had been open for 15 years.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384252"}, {"primary_key": "2685924", "vector": [], "sparse_vector": [], "title": "One-shot signatures and applications to hybrid quantum/classical authentication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We define the notion of one-shot signatures, which are signatures where any secret key can be used to sign only a single message, and then self-destructs. While such signatures are of course impossible classically, we construct one-shot signatures using quantum no-cloning. In particular, we show that such signatures exist relative to a classical oracle, which we can then heuristically obfuscate using known indistinguishability obfuscation schemes.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384304"}, {"primary_key": "2685925", "vector": [], "sparse_vector": [], "title": "Parallel approximate undirected shortest paths via low hop emulators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a (1+ε)-approximate parallel algorithm for computing shortest paths in undirected graphs, achieving poly(logn) depth and m poly(logn) work for n-nodes m-edges graphs. Although sequential algorithms with (nearly) optimal running time have been known for several decades, near-optimal parallel algorithms have turned out to be a much tougher challenge. For (1+ε)-approximation, all prior algorithms with poly(logn) depth perform at least Ω(mn c ) work for some constant c>0. Improving this long-standing upper bound obtained by <PERSON> (STOC'94) has been open for 25 years.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384321"}, {"primary_key": "2685926", "vector": [], "sparse_vector": [], "title": "Entanglement subvolume law for 2d frustration-free spin systems.", "authors": ["<PERSON><PERSON><PERSON>", "Itai Arad", "<PERSON>"], "summary": "Let H be a frustration-free Hamiltonian describing a 2D grid of qudits with local interactions, a unique ground state, and local spectral gap lower bounded by a positive constant. For any bipartition defined by a vertical cut of length L running from top to bottom of the grid, we prove that the corresponding entanglement entropy of the ground state of H is upper bounded by Õ(L 5/3). For the special case of a 1D chain, our result provides a new area law which improves upon prior work, in terms of the scaling with qudit dimension and spectral gap. In addition, for any bipartition of the grid into a rectangular region A and its complement, we show that the entanglement entropy is upper bounded as Õ(|∂ A|5/3) where ∂ A is the boundary of A. This represents a subvolume bound on entanglement in frustration-free 2D systems. In contrast with previous work, our bounds depend on the local (rather than global) spectral gap of the Hamiltonian. We prove our results using a known method which bounds the entanglement entropy of the ground state in terms of certain properties of an approximate ground state projector (AGSP). To this end, we construct a new AGSP which is based on a robust polynomial approximation of the AND function and we show that it achieves an improved trade-off between approximation error and entanglement.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384292"}, {"primary_key": "2685927", "vector": [], "sparse_vector": [], "title": "Better secret sharing via robust conditional disclosure of secrets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A secret-sharing scheme allows to distribute a secret s among n parties such that only some predefined \"authorized\" sets of parties can reconstruct the secret, and all other \"unauthorized\" sets learn nothing about s. For over 30 years, it was known that any (monotone) collection of authorized sets can be realized by a secret-sharing scheme whose shares are of size 2 n−o(n) and until recently no better scheme was known. In a recent breakthrough, <PERSON> and <PERSON><PERSON> (STOC 2018) have reduced the share size to 20.994n+o(n), which was later improved to 20.892n+o(n) by <PERSON><PERSON> et al. (EUROCRYPT 2019).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384293"}, {"primary_key": "2685928", "vector": [], "sparse_vector": [], "title": "Separating the communication complexity of truthful and non-truthful combinatorial auctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove the first separation in the approximation guarantee achievable by truthful and non-truthful combinatorial auctions with polynomial communication. Specifically, we prove that any truthful auction guaranteeing a (34−1240+є)-approximation for two buyers with XOS valuations over m items requires exp(Ω(ε2 · m)) communication whereas a non-truthful auction by <PERSON><PERSON> [<PERSON><PERSON> 2009] is already known to achieve a 34-approximation in (m) communication.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384267"}, {"primary_key": "2685929", "vector": [], "sparse_vector": [], "title": "Exploration with limited memory: streaming algorithms for coin tossing, noisy comparisons, and multi-armed bandits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Consider the following abstract coin tossing problem: Given a set of n coins with unknown biases, find the most biased coin using a minimal number of coin tosses. This is a common abstraction of various exploration problems in theoretical computer science and machine learning and has been studied extensively over the years. In particular, algorithms with optimal sample complexity (number of coin tosses) have been known for this problem for quite some time.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384341"}, {"primary_key": "2685930", "vector": [], "sparse_vector": [], "title": "A lower bound for parallel submodular minimization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study submodular function minimization in the adaptive complexity model. Seminal work by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> shows that with oracle access to a function f, the problem of submodular minimization can be solved exactly with poly(n) queries to f. A long line of work has since then been dedicated to the acceleration of submodular minimization. In particular, recent work obtains a (strongly) polynomial time algorithm with Õ(n 3) query complexity. A natural way to accelerate computation is via parallelization, though very little is known about the extent to which submodular minimization can be parallelized.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384287"}, {"primary_key": "2685931", "vector": [], "sparse_vector": [], "title": "Online vector balancing and geometric discrepancy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider an online vector balancing question where T vectors, chosen from an arbitrary distribution over [−1,1] n , arrive one-by-one and must be immediately given a ± sign. The goal is to keep the discrepancy—the ℓ∞-norm of any signed prefix-sum—as small as possible. A concrete example of this question is the online interval discrepancy problem where T points are sampled one-by-one uniformly in the unit interval [0,1], and the goal is to immediately color them ± such that every sub-interval remains always nearly balanced. As random coloring incurs Ω(T 1/2) discrepancy, while the worst-case offline bounds are Θ(√n log(T/n)) for vector balancing and 1 for interval balancing, a natural question is whether one can (nearly) match the offline bounds in the online setting for these problems. One must utilize the stochasticity as in the worst-case scenario it is known that discrepancy is Ω(T 1/2) for any online algorithm.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384280"}, {"primary_key": "2685932", "vector": [], "sparse_vector": [], "title": "Stochastic matching with few queries: (1-ε) approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Suppose that we are given an arbitrary graph G=(V, E) and know that each edge in E is going to be realized independently with some probability p. The goal in the stochastic matching problem is to pick a sparse subgraph Q of G such that the realized edges in Q, in expectation, include a matching that is approximately as large as the maximum matching among the realized edges of G. The maximum degree of Q can depend on p, but not on the size of G. This problem has been subject to extensive studies over the years and the approximation factor has been improved gradually from 0.5 to eventually 2/3 which is a known barrier. In this work, we analyze a natural sampling-based algorithm and show that it can obtain a (1−є) approximation, for any constant є > 0. A key and of possible independent interest component of our analysis is an algorithm that constructs a matching on a stochastic graph, which among some other important properties, guarantees that each vertex is matched independently from the vertices that are sufficiently far. This allows us to bypass a previously known barrier towards achieving (1−є) approximation based on existence of dense Ruzsa-Szemerédi graphs.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384340"}, {"primary_key": "2685933", "vector": [], "sparse_vector": [], "title": "Contention resolution without collision detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper focuses on the contention resolution problem on a shared communication channel that does not support collision detection. A shared communication channel is a multiple access channel, which consists of a sequence of synchronized time slots. Players on the channel may attempt to broadcast a packet (message) in any time slot. A player's broadcast succeeds if no other player broadcasts during that slot. If two or more players broadcast in the same time slot, then the broadcasts collide and both broadcasts fail. The lack of collision detection means that a player monitoring the channel cannot differentiate between the case of two or more players broadcasting in the same slot (a collision) and zero players broadcasting. In the contention-resolution problem, players arrive on the channel over time, and each player has one packet to transmit. The goal is to coordinate the players so that each player is able to successfully transmit its packet within reasonable time. However, the players can only communicate via the shared channel by choosing to either broadcast or not. A contention-resolution protocol is measured in terms of its throughput (channel utilization). Previous work on contention resolution that achieved constant throughput assumed that either players could detect collisions, or the players' arrival pattern is generated by a memoryless (non-adversarial) process.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384305"}, {"primary_key": "2685934", "vector": [], "sparse_vector": [], "title": "Optimal time and space leader election in population protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Population protocols are a model of distributed computing, where n agents with limited computational power and memory perform randomly scheduled pairwise interactions. A fundamental problem in this setting is that of leader election, where all agents start from the same state, and they seek to reach and maintain a global state where exactly one agent is in a dedicated leader state. A significant amount of work has been devoted to the study of the time and space complexity of this problem. <PERSON><PERSON><PERSON> et al. (SODA'17) have shown that Ω(loglogn) states per agent are needed in order to elect a leader in fewer than Θ(n 2) expected interactions. Moreover, Ω(nlogn) expected interactions are required regardless of the number of states (<PERSON><PERSON> and Masuzawa, 2019). On the upper bound side, Gas<PERSON><PERSON><PERSON> and Stachowiak (SODA'18) have presented the first protocol that uses an optimal, Θ(loglogn), number or states and elects a leader in O(n log2 n) expected interactions. This running time was subsequently improved to O(n lognloglogn) (<PERSON><PERSON><PERSON> et al., SPAA'19).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384312"}, {"primary_key": "2685935", "vector": [], "sparse_vector": [], "title": "Improved bounds for perfect sampling of k-colorings in graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a randomized algorithm that takes as input an undirected n-vertex graph G with maximum degree Δ and an integer k > 3Δ, and returns a random proper k-coloring of G. The distribution of the coloring is perfectly uniform over the set of all proper k-colorings; the expected running time of the algorithm is poly(k,n)=O(nΔ2· log(k)). This improves upon a result of <PERSON><PERSON> (STOC 1998) who obtained a polynomial time perfect sampling algorithm for k>Δ2+2Δ. Prior to our work, no algorithm with expected running time poly(k,n) was known to guarantee perfectly sampling with sub-quadratic number of colors in general.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384244"}, {"primary_key": "2685936", "vector": [], "sparse_vector": [], "title": "Unbounded lower bound for k-server against weak adversaries.", "authors": ["<PERSON><PERSON>", "Jaroslaw Byrka", "<PERSON>", "Lukasz Jez"], "summary": "We study the resource augmented version of the k-server problem, also known as the k-server problem against weak adversaries or the (h,k)-server problem. In this setting, an online algorithm using k servers is compared to an offline algorithm using h servers, where h ≤ k. For uniform metrics, it has been known since the seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1985) that for any є>0, the competitive ratio drops to a constant if k=(1+є) · h. This result was later generalized to weighted stars (<PERSON> 1994) and trees of bounded depth (<PERSON> et al. 2017). The main open problem for this setting is whether a similar phenomenon occurs on general metrics. We resolve this question negatively. With a simple recursive construction, we show that the competitive ratio is at least Ω(loglogh), even as k→∞. Our lower bound holds for both deterministic and randomized algorithms. It also disproves the existence of a competitive algorithm for the infinite server problem on general metrics.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384306"}, {"primary_key": "2685937", "vector": [], "sparse_vector": [], "title": "Post-quantum zero knowledge in constant rounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct a constant-round zero-knowledge classical argument for NP secure against quantum attacks. We assume the existence of Quantum Fully-Homomorphic Encryption and other standard primitives, known based on the Learning with Errors Assumption for quantum algorithms. As a corollary, we also obtain a constant-round zero-knowledge quantum argument for QMA.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384324"}, {"primary_key": "2685938", "vector": [], "sparse_vector": [], "title": "Efficient sampling and counting algorithms for the Potts model on ℤᵈ at all temperatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For d ≥ 2 and all q≥ q 0(d) we give an efficient algorithm to approximately sample from the q-state ferromagnetic Potts and random cluster models on the torus (ℤ / n ℤ ) d for any inverse temperature β≥ 0. This stands in contrast to Markov chain mixing time results: the <PERSON><PERSON><PERSON> dynamics mix slowly at and below the critical temperature, and the <PERSON><PERSON><PERSON><PERSON>–<PERSON> dynamics mix slowly at the critical temperature. We also provide an efficient algorithm (an FPRAS) for approximating the partition functions of these models.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384271"}, {"primary_key": "2685939", "vector": [], "sparse_vector": [], "title": "Constant-factor approximation of near-linear edit distance in near-linear time.", "authors": ["<PERSON>", "<PERSON>via<PERSON>"], "summary": "We show that the edit distance between two strings of length n can be computed via a randomized algorithm within a factor of f(є) in n 1+є time as long as the edit distance is at least n 1−δ for some δ(є) > 0.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384282"}, {"primary_key": "2685940", "vector": [], "sparse_vector": [], "title": "Solving tall dense linear programs in nearly linear time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we provide an O(nd+d 3) time randomized algorithm for solving linear programs with d variables and n constraints with high probability. To obtain this result we provide a robust, primal-dual O(√d)-iteration interior point method inspired by the methods of <PERSON> and <PERSON> (2014, 2019) and show how to efficiently implement this method using new data-structures based on heavy-hitters, the <PERSON> lemma, and inverse maintenance. Interestingly, we obtain this running time without using fast matrix multiplication and consequently, barring a major advance in linear system solving, our running time is near optimal for solving dense linear programs among algorithms that do not use fast matrix multiplication.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384309"}, {"primary_key": "2685941", "vector": [], "sparse_vector": [], "title": "Top-k-convolution and the quest for near-linear output-sensitive subset sum.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the classical SubsetSum problem we are given a set X and a target t, and the task is to decide whether there exists a subset of X which sums to t. A recent line of research has resulted in (t · poly (logt))-time algorithms, which are (near-)optimal under popular complexity-theoretic assumptions. On the other hand, the standard dynamic programming algorithm runs in time O(n · |S(X,t)|), where S(X,t) is the set of all subset sums of X that are smaller than t. All previous pseudopolynomial algorithms actually solve a stronger task, since they actually compute the whole set S(X,t).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384308"}, {"primary_key": "2685942", "vector": [], "sparse_vector": [], "title": "Breaching the 2-approximation barrier for connectivity augmentation: a reduction to Steiner tree.", "authors": ["Jaroslaw Byrka", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The basic goal of survivable network design is to build a cheap network that maintains the connectivity between given sets of nodes despite the failure of a few edges/nodes. The Connectivity Augmentation Problem (CAP) is arguably one of the most basic problems in this area: given a k(-edge)-connected graph G and a set of extra edges (links), select a minimum cardinality subset A of links such that adding A to G increases its edge connectivity to k+1. Intuitively, one wants to make an existing network more reliable by augmenting it with extra edges. The best known approximation factor for this NP-hard problem is 2, and this can be achieved with multiple approaches (the first such result is in [<PERSON><PERSON> and Jáj<PERSON>'81]).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384301"}, {"primary_key": "2685943", "vector": [], "sparse_vector": [], "title": "Efficient construction of directed hopsets and parallel approximate shortest paths.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The approximate single-source shortest-path problem is as follows: given a graph with nonnegative edge weights and a designated source vertex s, return estimates of the distances from s to each other vertex such that the estimate falls between the true distance and (1+є) times the distance. This paper provides the first nearly work-efficient parallel algorithm with sublinear span (also called depth) for the approximate shortest-path problem on directed graphs. Specifically, for constant є and polynomially-bounded edge weights, our algorithm has work Õ(m) and span n 1/2+o(1). Several algorithms were previously known for the case of undirected graphs, but none of the techniques seem to translate to the directed setting.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384270"}, {"primary_key": "2685944", "vector": [], "sparse_vector": [], "title": "Approximating text-to-pattern Hamming distances.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit a fundamental problem in string matching: given a pattern of length m and a text of length n, both over an alphabet of size σ, compute the Hamming distance (i.e., the number of mismatches) between the pattern and the text at every location. Several randomized (1+ε)-approximation algorithms have been proposed in the literature (e.g., by <PERSON><PERSON> (Inf. Proc. Lett., 1993), <PERSON><PERSON> (FOCS 1998), and <PERSON><PERSON> and <PERSON> (SOSA 2018)), with running time of the form O(ε−O(1) nlognlogm), all using fast Fourier transform (FFT). We describe a simple randomized (1+ε)-approximation algorithm that is faster and does not need FFT. Combining our approach with additional ideas leads to numerous new results (all Monte-Carlo randomized) in different settings:", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384266"}, {"primary_key": "2685945", "vector": [], "sparse_vector": [], "title": "Extractors for adversarial sources via extremal hypergraphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Randomness extraction is a fundamental problem that has been studied for over three decades. A well-studied setting assumes that one has access to multiple independent weak random sources, each with some entropy. However, this assumption is often unrealistic in practice. In real life, natural sources of randomness can produce samples with no entropy at all or with unwanted dependence. Motivated by this and applications from cryptography, we initiate a systematic study of randomness extraction for the class of adversarial sources defined as follows.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384339"}, {"primary_key": "2685946", "vector": [], "sparse_vector": [], "title": "XOR lemmas for resilient functions against polynomials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A major challenge in complexity theory is to explicitly construct functions that have small correlation with low-degree polynomials over F2. We introduce a new technique to prove such correlation bounds with F2 polynomials. Using this technique, we bound the correlation of an XOR of Majorities with constant degree polynomials. In fact, we prove a more general XOR lemma that extends to arbitrary resilient functions. We conjecture that the technique generalizes to higher degree polynomials as well.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384242"}, {"primary_key": "2685947", "vector": [], "sparse_vector": [], "title": "Distance sensitivity oracles with subcubic preprocessing time and fast query time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the first distance sensitivity oracle (DSO) with subcubic preprocessing time and poly-logarithmic query time for directed graphs with integer weights in the range [−M,M].", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384253"}, {"primary_key": "2685948", "vector": [], "sparse_vector": [], "title": "Constant girth approximation for directed graphs in subquadratic time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide a Õ(m√n) time algorithm that computes a 3-multiplicative approximation of the girth of a n-node m-edge directed graph with non-negative edge lengths. This is the first algorithm which approximates the girth of a directed graph up to a constant multiplicative factor faster than All-Pairs Shortest Paths (APSP) time, i.e. O(mn). Additionally, for any integer k ≥ 1, we provide a deterministic algorithm for a O(kloglogn)-multiplicative approximation to the girth in directed graphs in Õ(m 1+1/k ) time. Combining the techniques from these two results gives us an algorithm for a O(klogk)-multiplicative approximation to the girth in directed graphs in Õ(m 1+1/k ) time. Our results naturally also provide algorithms for improved constructions of roundtrip spanners, the analog of spanners in directed graphs.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384330"}, {"primary_key": "2685949", "vector": [], "sparse_vector": [], "title": "Efficiently learning structured distributions from untrusted batches.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem, introduced by <PERSON><PERSON> and <PERSON><PERSON>, of learning from untrusted batches. Here, we assume m users, all of whom have samples from some underlying distribution over 1, …, n. Each user sends a batch of k i.i.d. samples from this distribution; however an є-fraction of users are untrustworthy and can send adversarially chosen responses. The goal of the algorithm is to learn in total variation distance. When k = 1 this is the standard robust univariate density estimation setting and it is well-understood that (є) error is unavoidable. Suprisingly, <PERSON><PERSON> and <PERSON><PERSON> gave an estimator which improves upon this rate when k is large. Unfortunately, their algorithms run in time which is exponential in either n or k.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384337"}, {"primary_key": "2685950", "vector": [], "sparse_vector": [], "title": "Learning mixtures of linear regressions in subexponential time via Fourier moments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of learning a mixture of linear regressions (MLRs). An MLR is specified by k nonnegative mixing weights p 1, …, p k summing to 1, and k unknown regressors w 1,...,w k ∈ℝ d . A sample from the MLR is drawn by sampling i with probability p i , then outputting (x, y) where y = ⟨ x, w i ⟩ + η, where η∼(0,ς2) for noise rate ς. Mixtures of linear regressions are a popular generative model and have been studied extensively in machine learning and theoretical computer science. However, all previous algorithms for learning the parameters of an MLR require running time and sample complexity scaling exponentially with k.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384333"}, {"primary_key": "2685951", "vector": [], "sparse_vector": [], "title": "Smoothed complexity of local max-cut and binary max-CSP.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Emmanouil V<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the smoothed complexity of the FLIP algorithm for local Max-Cut is at most φ n O(√logn), where n is the number of nodes in the graph and φ is a parameter that measures the magnitude of perturbations applied on its edge weights. This improves the previously best upper bound of φ n O(logn) by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Our result is based on an analysis of long sequences of flips, which shows that it is very unlikely for every flip in a long sequence to incur a positive but small improvement in the cut weight. We also extend the same upper bound on the smoothed complexity of FLIP to all binary Maximum Constraint Satisfaction Problems.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384325"}, {"primary_key": "2685952", "vector": [], "sparse_vector": [], "title": "Sharp threshold results for computational complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We establish several \"sharp threshold\" results for computational complexity. For certain tasks, we can prove a resource lower bound of n c for c ≥ 1 (or obtain an efficient circuit-analysis algorithm for n c size), there is strong intuition that a similar result can be proved for larger functions of n, yet we can also prove that replacing \"n c \" with \"n c+ε\" in our results, for any ε > 0, would imply a breakthrough n ω(1) lower bound. We first establish such a result for Hardness Magnification. We prove (among other results) that for some c, the Minimum Circuit Size Problem for (logn) c -size circuits on length-n truth tables (MCSP[(logn) c ]) does not have n 2−o(1)-size probabilistic formulas. We also prove that an n 2+ε lower bound for MCSP[(logn) c ] (for any ε > 0 and c ≥ 1) would imply major lower bound results, such as NP does not have n k -size formulas for all k, and #SAT does not have log-depth circuits. Similar results hold for time-bounded Kolmogorov complexity. Note that cubic size lower bounds are known for probabilistic De Morgan formulas (for other functions). Next we show a sharp threshold for Quantified Derandomization (QD) of probabilistic formulas: (a) For all α, ε > 0, there is a deterministic polynomial-time algorithm that finds satisfying assignments to every probabilistic formula of n 2−2α−ε size with at most 2 n α falsifying assignments. (b) If for some α, ε > 0, there is such an algorithm for probabilistic formulas of n 2−α+ε-size and 2 n α unsatisfying assignments, then a full derandomization of NC 1 follows: a deterministic poly-time algorithm additively approximating the acceptance probability of any polynomial-size formula. Consequently, NP does not have n k -size formulas, for all k. Finally we show a sharp threshold result for Explicit Obstructions, inspired by Mulmuley's notion of explicit obstructions from GCT. An explicit obstruction against S(n)-size formulas is a poly-time algorithm A such that A(1 n ) outputs a list {(x i ,f(x i ))} i ∈ [poly(n)] ⊆ {0,1} n × {0,1}, and every S(n)-size formula F is inconsistent with the (partially defined) function f. We prove that for all ε > 0, there is an explicit obstruction against n 2−ε-size formulas, and prove that there is an explicit obstruction against n 2+ε-size formulas for some ε > 0 if and only if there is an explicit obstruction against all polynomial-size formulas. This in turn is equivalent to the statement that E does not have 2 o(n)-size formulas, a breakthrough in circuit complexity.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384283"}, {"primary_key": "2685953", "vector": [], "sparse_vector": [], "title": "Strong average-case lower bounds from non-trivial derandomization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ren"], "summary": "We prove that for all constants a, NQP = NTIME[n polylog(n)] cannot be (1/2 + 2−log a n )-approximated by 2log a n -size ACC 0 ∘ THR circuits ( ACC 0 circuits with a bottom layer of THR gates). Previously, it was even open whether E NP can be (1/2+1/√n)-approximated by AC 0[⊕] circuits. As a straightforward application, we obtain an infinitely often ( NE ∩ coNE)/1-computable pseudorandom generator for poly-size ACC 0 circuits with seed length 2logє n , for all є > 0.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384279"}, {"primary_key": "2685954", "vector": [], "sparse_vector": [], "title": "Algorithms for heavy-tailed statistics: regression, covariance estimation, and beyond.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study polynomial-time algorithms for linear regression and covariance estimation in the absence of strong (Gaussian) assumptions on the underlying distributions of samples, making assumptions instead about only finitely-many moments. We focus on how many samples are required to perform estimation and regression with high accuracy and exponentially-good success probability in the face of heavy-tailed data.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384329"}, {"primary_key": "2685955", "vector": [], "sparse_vector": [], "title": "On the need for large quantum depth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Near-term quantum computers are likely to have small depths due to short coherence time and noisy gates. A natural approach to leverage these quantum computers is interleaving them with classical computers. Understanding the capabilities and limits of this hybrid approach is an essential topic in quantum computation. Most notably, the quantum Fourier transform can be implemented by a hybrid of logarithmic-depth quantum circuits and a classical polynomial-time algorithm. Therefore, it seems possible that quantum polylogarithmic depth is as powerful as quantum polynomial depth in the presence of classical computation.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384291"}, {"primary_key": "2685956", "vector": [], "sparse_vector": [], "title": "Sampling-based sublinear low-rank matrix arithmetic framework for dequantizing quantum machine learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tongyang Li", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an algorithmic framework for quantum-inspired classical algorithms on close-to-low-rank matrices, generalizing the series of results started by <PERSON>'s breakthrough quantum-inspired algorithm for recommendation systems [STOC'19]. Motivated by quantum linear algebra algorithms and the quantum singular value transformation (SVT) framework of <PERSON><PERSON> et al. [STOC'19], we develop classical algorithms for SVT that run in time independent of input dimension, under suitable quantum-inspired sampling assumptions. Our results give compelling evidence that in the corresponding QRAM data structure input model, quantum SVT does not yield exponential quantum speedups. Since the quantum SVT framework generalizes essentially all known techniques for quantum linear algebra, our results, combined with sampling lemmas from previous work, suffices to generalize all recent results about dequantizing quantum machine learning algorithms. In particular, our classical SVT framework recovers and often improves the dequantization results on recommendation systems, principal component analysis, supervised clustering, support vector machines, low-rank regression, and semidefinite program solving. We also give additional dequantization results on low-rank Hamiltonian simulation and discriminant analysis. Our improvements come from identifying the key feature of the quantum-inspired input model that is at the core of all prior quantum-inspired results: ℓ2-norm sampling can approximate matrix products in time independent of their dimension. We reduce all our main results to this fact, making our exposition concise, self-contained, and intuitive.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384314"}, {"primary_key": "2685957", "vector": [], "sparse_vector": [], "title": "Catalytic approaches to the tree evaluation problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The study of branching programs for the Tree Evaluation Problem (TreeEval), introduced by <PERSON><PERSON> et al. (TOCT 2012), remains one of the most promising approaches to separating L from P. Given a label in [k] at each leaf of a complete binary tree and an explicit function in [k]2 → [k] for recursively computing the value of each internal node from its children, the problem is to compute the value at the root node. (While the original problem allows an arbitrary-degree tree, we focus on binary trees.) The problem is parameterized by the alphabet size k and the height h of the tree. A branching program implementing the straightforward recursive algorithm uses Θ((k + 1) h ) states, organized into 2 h −1 layers of width up to k h . Until now no better deterministic algorithm was known.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384316"}, {"primary_key": "2685958", "vector": [], "sparse_vector": [], "title": "Computations with greater quantum depth are strictly more powerful (relative to an oracle).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A conjecture of <PERSON><PERSON><PERSON> (arXiv:quant-ph/0508124) states that any polynomial-time quantum computation can be simulated by polylogarithmic-depth quantum computation interleaved with polynomial-depth classical computation. Separately, <PERSON><PERSON> conjectured that there exists an oracle $\\mathcal{O}$ such that $\\textrm{BQP}^{\\mathcal{O}} \\neq (\\textrm{BPP}^\\textrm{BQNC})^{\\mathcal{O}}$. These conjectures are intriguing allusions to the unresolved potential of combining classical and low-depth quantum computation. In this work we show that the Welded Tree Problem, which is an oracle problem that can be solved in quantum polynomial time as shown by <PERSON><PERSON> et al. (arXiv:quant-ph/0209131), cannot be solved in $\\textrm{BPP}^{\\textrm{BQNC}}$, nor can it be solved in the class that <PERSON><PERSON><PERSON> describes. This proves <PERSON><PERSON>'s oracle separation conjecture and provides a counterpoint to <PERSON><PERSON><PERSON>'s conjecture relative to the Welded Tree oracle problem. More precisely, we define two complexity classes, $\\textrm{HQC}$ and $\\textrm{JC}$ whose languages are decided by two different families of interleaved quantum-classical circuits. $\\textrm{HQC}$ contains $\\textrm{BPP}^\\textrm{BQNC}$ and is therefore relevant to <PERSON><PERSON>'s conjecture, while $\\textrm{JC}$ captures the model of computation that <PERSON>zsa considers. We show that the Welded Tree Problem gives an oracle separation between either of $\\{\\textrm{JC}, \\textrm{HQC}\\}$ and $\\textrm{BQP}$. Therefore, even when interleaved with arbitrary polynomial-time classical computation, greater \"quantum depth\" leads to strictly greater computational ability in this relativized setting.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384269"}, {"primary_key": "2685959", "vector": [], "sparse_vector": [], "title": "A scaling-invariant algorithm for linear programming whose running time depends only on the constraint matrix.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "László A. <PERSON>"], "summary": "Following the breakthrough work of <PERSON><PERSON><PERSON> (Oper. Res. '86) in the bit-complexity model, <PERSON><PERSON><PERSON> and <PERSON> (Math. Prog. '96) gave the first exact algorithm for linear programming in the real model of computation with running time depending only on the constraint matrix. For solving a linear program (LP) max c x, Ax = b, x ≥ 0, A ∈ m × n , <PERSON><PERSON><PERSON> and <PERSON> developed a primal-dual interior point method using a 'layered least squares' (LLS) step, and showed that O(n 3.5 log(χ A +n)) iterations suffice to solve (LP) exactly, where χ A is a condition measure controlling the size of solutions to linear systems related to A.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384326"}, {"primary_key": "2685960", "vector": [], "sparse_vector": [], "title": "Interaction is necessary for distributed learning with privacy or communication constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Local differential privacy (LDP) is a model where users send privatized data to an untrusted central server whose goal it to solve some data analysis task. In the non-interactive version of this model the protocol consists of a single round in which a server sends requests to all users then receives their responses. This version is deployed in industry due to its practical advantages and has attracted significant research interest.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384315"}, {"primary_key": "2685961", "vector": [], "sparse_vector": [], "title": "The power of factorization mechanisms in local and central differential privacy.", "authors": ["<PERSON>", "Aleksan<PERSON>", "<PERSON>"], "summary": "We give new characterizations of the sample complexity of answering linear queries (statistical queries) in the local and central models of differential privacy:", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384297"}, {"primary_key": "2685962", "vector": [], "sparse_vector": [], "title": "Interactive error resilience beyond 2/7.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Interactive error correcting codes can protect interactive communication protocols against a constant fraction of adversarial errors, while incurring only a constant multiplicative overhead in the total communication. What is the maximum fraction of errors that such codes can protect against?", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384320"}, {"primary_key": "2685963", "vector": [], "sparse_vector": [], "title": "Concentration on the Boolean hypercube via pathwise stochastic analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop a new technique for proving concentration inequalities which relate between the variance and influences of Boolean functions.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384230"}, {"primary_key": "2685964", "vector": [], "sparse_vector": [], "title": "Does learning require memorization? a short tale about a long tail.", "authors": ["<PERSON><PERSON>"], "summary": "State-of-the-art results on image recognition tasks are achieved using over-parameterized learning algorithms that (nearly) perfectly fit the training set and are known to fit well even random labels. This tendency to memorize seemingly useless training data labels is not explained by existing theoretical analyses. Memorization of the training data also presents significant privacy risks when the training data contains sensitive personal information and thus it is important to understand whether such memorization is necessary for accurate learning.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384290"}, {"primary_key": "2685965", "vector": [], "sparse_vector": [], "title": "Private stochastic convex optimization: optimal rates in linear time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study differentially private (DP) algorithms for stochastic convex optimization: the problem of minimizing the population loss given i.i.d. samples from a distribution over convex loss functions. A recent work of <PERSON><PERSON> et al. (2019) has established the optimal bound on the excess population loss achievable given n samples. Unfortunately, their algorithm achieving this bound is relatively inefficient: it requires O(min{n 3/2, n 5/2/d}) gradient computations, where d is the dimension of the optimization problem.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384335"}, {"primary_key": "2685966", "vector": [], "sparse_vector": [], "title": "The one-way communication complexity of submodular maximization with applications to streaming and robustness.", "authors": ["<PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the classical problem of maximizing a monotone submodular function subject to a cardinality constraint, which, due to its numerous applications, has recently been studied in various computational models. We consider a clean multi-player model that lies between the offline and streaming model, and study it under the aspect of one-way communication complexity. Our model captures the streaming setting (by considering a large number of players), and, in addition, two player approximation results for it translate into the robust setting. We present tight one-way communication complexity results for our model, which, due to the above-mentioned connections, have multiple implications in the data stream and robust setting.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384286"}, {"primary_key": "2685967", "vector": [], "sparse_vector": [], "title": "Fast sampling and counting k-SAT solutions in the local lemma regime.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give new algorithms based on Markov chains to sample and approximately count satisfying assignments to k-uniform CNF formulas where each variable appears at most d times. For any k and d satisfying kd<n o(1) and k≥ 20logk + 20logd + 60, the new sampling algorithm runs in close to linear time, and the counting algorithm runs in close to quadratic time.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384255"}, {"primary_key": "2685968", "vector": [], "sparse_vector": [], "title": "AND testing and robust judgement aggregation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A function f∶{0,1} n → {0,1} is called an approximate AND-homomorphism if choosing x,y∈n uniformly at random, we have that f(x∧ y) = f(x)∧ f(y) with probability at least 1−ε, where x∧ y = (x 1∧ y 1,…,x n ∧ y n ). We prove that if f∶ {0,1} n → {0,1} is an approximate AND-homomorphism, then f is δ-close to either a constant function or an AND function, where δ(ε) → 0 as ε→ 0. This improves on a result of <PERSON><PERSON><PERSON>, who proved a similar statement in which δ depends on n.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384254"}, {"primary_key": "2685969", "vector": [], "sparse_vector": [], "title": "Hitting topological minors is FPT.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Topological Minor Deletion (TM-Deletion) problem, the input consists of an undirected graph G, a family of undirected graphs F and an integer k. The task is to determine whether G contains a set of vertices S of size at most k, such that the graph G∖ S obtained from G by removing the vertices of S, contains no graph from F as a topological minor. We give an algorithm forTM-Deletion with running time f(h ⋆,k)· |V(G)|4. Here h ⋆ is the maximum size of a graph in F and f is a computable function of h ⋆ and k. This is the first fixed parameter tractable algorithm (FPT) for the problem. In fact, even for the restricted case of planar inputs the first FPT algorithm was found only recently by <PERSON><PERSON><PERSON> et al. [SODA 2020]. For this case we improve upon the algorithm of <PERSON><PERSON><PERSON> et al. [SODA 2020] by designing an FPT algorithm with explicit dependence on k and h ⋆.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384318"}, {"primary_key": "2685970", "vector": [], "sparse_vector": [], "title": "Bare quantum simultaneity versus classical interactivity in communication complexity.", "authors": ["<PERSON>"], "summary": "A relational bipartite communication problem is presented that has an efficient quantum simultaneous-messages protocol, but no efficient classical two-way protocol.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384243"}, {"primary_key": "2685971", "vector": [], "sparse_vector": [], "title": "Does preprocessing help in fast sequence comparisons?", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "We study edit distance computation with preprocessing: the preprocessing algorithm acts on each string separately, and then the query algorithm takes as input the two preprocessed strings. This model is inspired by scenarios where we would like to compute edit distance between many pairs in the same pool of strings.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384300"}, {"primary_key": "2685972", "vector": [], "sparse_vector": [], "title": "Data structures meet cryptography: 3SUM with preprocessing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sunoo Park", "<PERSON><PERSON>"], "summary": "This paper shows several connections between data structure problems and cryptography against preprocessing attacks. Our results span data structure upper bounds, cryptographic applications, and data structure lower bounds, as summarized next.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384342"}, {"primary_key": "2685973", "vector": [], "sparse_vector": [], "title": "Automating cutting planes is NP-hard.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that Cutting Planes (CP) proofs are hard to find: Given an unsatisfiable formula F, It is -hard to find a CP refutation of F in time polynomial in the length of the shortest such refutation; and unless Gap-Hitting-Set admits a nontrivial algorithm, one cannot find a tree-like CP refutation of F in time polynomial in the length of the shortest such refutation.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384248"}, {"primary_key": "2685974", "vector": [], "sparse_vector": [], "title": "Interactive shallow Clifford circuits: quantum advantage against NC¹ and beyond.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Recent work of <PERSON><PERSON><PERSON><PERSON> et al. and follow-up work by <PERSON><PERSON> et al. demonstrates a quantum advantage for shallow circuits: constant-depth quantum circuits can perform a task which constant-depth classical (i.e., 0) circuits cannot. Their results have the advantage that the quantum circuit is fairly practical, and their proofs are free of hardness assumptions (e.g., factoring is classically hard, etc.). Unfortunately, constant-depth classical circuits are too weak to yield a convincing real-world demonstration of quantum advantage. We attempt to hold on to the advantages of the above results, while increasing the power of the classical model. Our main result is a two-round interactive task which is solved by a constant-depth quantum circuit (using only Clifford gates, between neighboring qubits of a 2D grid, with <PERSON><PERSON> measurements), but such that any classical solution would necessarily solve -hard problems. This implies a more powerful class of constant-depth classical circuits (e.g., 0[p] for any prime p) unconditionally cannot perform the task. Furthermore, under standard complexity-theoretic conjectures, log-depth circuits and log-space Turing machines cannot perform the task either. Using the same techniques, we prove hardness results for weaker complexity classes under more restrictive circuit topologies. Specifically, we give 0 interactive tasks on 2 × n and 1 × n grids which require classical simulations of power 1 and 0[6], respectively. Moreover, these hardness results are robust to a small constant fraction of error in the classical simulation.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384332"}, {"primary_key": "2685975", "vector": [], "sparse_vector": [], "title": "Caching with time windows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the (weighted) Paging with Time Windows problem, which is identical to the classical weighted paging problem but where each page request only needs to be served by a given deadline. This problem arises in many practical applications of online caching, such as the deadline I/O scheduler in the Linux kernel and video-on-demand streaming. From a theoretical perspective, this generalizes the caching problem to allow delayed service, a line of work that has recently gained traction in online algorithms (e.g., <PERSON><PERSON> et al. STOC '16, <PERSON><PERSON> et al. STOC '17, <PERSON><PERSON> and <PERSON><PERSON><PERSON> FOCS '19, etc.).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384277"}, {"primary_key": "2685976", "vector": [], "sparse_vector": [], "title": "The <PERSON><PERSON><PERSON><PERSON> algorithm is optimal for k-cut.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "In the k-cut problem, we are given an edge-weighted graph and want to find the least-weight set of edges whose deletion breaks the graph into k connected components. Algorithms due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> showed how to find such a minimum k-cut in time approximately O(n 2k−2). The best lower bounds come from conjectures about the solvability of the k-clique problem and a reduction from k-clique to k-cut, and show that solving k-cut is likely to require time Ω(n k ). Our recent results have given special-purpose algorithms that solve the problem in time n 1.98k + O(1), and ones that have better performance for special classes of graphs (e.g., for small integer weights).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384285"}, {"primary_key": "2685977", "vector": [], "sparse_vector": [], "title": "Optimally resilient codes for list-decoding from insertions and deletions.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give a complete answer to the following basic question: \"What is the maximal fraction of deletions or insertions tolerable by q-ary list-decodable codes with non-vanishing information rate?\"", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384262"}, {"primary_key": "2685978", "vector": [], "sparse_vector": [], "title": "Arikan meets Shannon: polar codes with near-optimal convergence to channel capacity.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Let W be a binary-input memoryless symmetric (BMS) channel with Shannon capacity I(W) and fix any α > 0. We construct, for any sufficiently small δ > 0, binary linear codes of block length O(1/δ2+α) and rate I(W)−δ that enable reliable communication on W with quasi-linear time encoding and decoding. <PERSON>'s noisy coding theorem established the existence of such codes (without efficient constructions or decoding) with block length O(1/δ2). This quadratic dependence on the gap δ to capacity is known to be the best possible. Our result thus yields a constructive version of <PERSON>'s theorem with near-optimal convergence to capacity as a function of the block length. This resolves a central theoretical challenge associated with the attainment of Shannon capacity. Previously such a result was only known for the binary erasure channel.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384323"}, {"primary_key": "2685979", "vector": [], "sparse_vector": [], "title": "New algorithms and hardness for incremental single-source shortest paths in directed graphs.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "In the dynamic Single-Source Shortest Paths (SSSP) problem, we are given a graph G=(V,E) subject to edge insertions and deletions and a source vertex sg V, and the goal is to maintain the distance d(s,t) for all tg V. Fine-grained complexity has provided strong lower bounds for exact partially dynamic SSSP and approximate fully dynamic SSSP [ESA'04, FOC<PERSON>'14, ST<PERSON>'15]. Thus much focus has been directed towards finding efficient partially dynamic (1+\")-approximate SSSP algorithms [STOC'14, ICALP'15, SODA'14, FOCS'14, ST<PERSON>'16, SODA'17, ICALP'17, ICALP'19, STOC'19, SODA'20, SODA'20]. Despite this rich literature, for directed graphs there are no known deterministic algorithms for (1+\")-approximate dynamic SSSP that perform better than the classic ES-tree [JACM'81]. We present the first such algorithm. We present a deterministic data structure for incremental SSSP in weighted directed graphs with total update time Õ(n2 logW/\"O(1)) which is near-optimal for very dense graphs; here W is the ratio of the largest weight in the graph to the smallest. Our algorithm also improves over the best known partially dynamic randomized algorithm for directed SSSP by <PERSON><PERSON><PERSON> et al. [STOC'14, ICA<PERSON>'15] if m=ω(n1.1). Complementing our algorithm, we provide improved conditional lower bounds. <PERSON><PERSON><PERSON> et al. [STOC'15] showed that under the OMv Hypothesis, the partially dynamic exact s-t Shortest Path problem in undirected graphs requires amortized update or query time m1/2-o(1), given polynomial preprocessing time. Under a new hypothesis about finding Cliques, we improve the update and query lower bound for algorithms with polynomial preprocessing time to m0.626-o(1). Further, under the k-Cycle hypothesis, we show that any partially dynamic SSSP algorithm with O(m2-\") preprocessing time requires amortized update or query time m1-o(1), which is essentially optimal. All previous conditional lower bounds that come close to our bound [ESA'04,FOCS'14] only held for \"combinatorial\" algorithms, while our new lower bound does not make such restrictions.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384236"}, {"primary_key": "2685980", "vector": [], "sparse_vector": [], "title": "Classical algorithms, correlation decay, and complex zeros of partition functions of quantum many-body systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a quasi-polynomial time classical algorithm that estimates the partition function of quantum many-body systems at temperatures above the thermal phase transition point. It is known that in the worst case, the same problem is NP-hard below this point. Together with our work, this shows that the transition in the phase of a quantum system is also accompanied by a transition in the hardness of approximation. We also show that in a system of n particles above the phase transition point, the correlation between two observables whose distance is at least log(n) decays exponentially. We can improve the factor of log(n) to a constant when the Hamiltonian has commuting terms or is on a 1D chain. The key to our results is a characterization of the phase transition and the critical behavior of the system in terms of the complex zeros of the partition function. Our work extends a seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> on the equivalence between the decay of correlations and the analyticity of the free energy in classical spin models. On the algorithmic side, our result extends the scope of a recent approach due to <PERSON><PERSON><PERSON> for solving classical counting problems to quantum many-body systems.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384322"}, {"primary_key": "2685981", "vector": [], "sparse_vector": [], "title": "Unexpected hardness results for Kolmogorov complexity under uniform reductions.", "authors": ["<PERSON><PERSON>"], "summary": "Hardness of computing the Kolmogorov complexity of a given string is closely tied to a security proof of hitting set generators, and thus understanding hardness of Kolmogorov complexity is one of the central questions in complexity theory. In this paper, we develop new proof techniques for showing hardness of computing Kolmogorov complexity under surprisingly efficient reductions, which were previously conjectured to be impossible. It is known that the set R K of Kolmogorov-random strings is PSPACE-hard under polynomial-time Turing reductions, i.e., PSPACE ⊂ P R K , and that NEXP ⊂ NP R K , which was conjectured to be tight by <PERSON><PERSON> (CiE 2012). We prove that EXP NP ⊂ P R K , which simultaneously improves these hardness results and refutes the conjecture of <PERSON><PERSON> under the plausible assumption that EXP NP ≠ NEXP. At the core of our results is a new security proof of a pseudorandom generator via a black-box uniform reduction, which overcomes an impossibility result of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (RANDOM/APPROX 2008).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384251"}, {"primary_key": "2685982", "vector": [], "sparse_vector": [], "title": "Non-signaling proofs with o(√ log n) provers are in PSPACE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-signaling proofs, motivated by quantum computation, have found applications in cryptography and hardness of approximation. An important open problem is characterizing the power of non-signaling proofs. It is known that non-signaling proofs with two provers are characterized by PSPACE and that non-signaling proofs with poly(n)-provers are characterized by EXP. However, the power of k-prover non-signaling proofs, for 2<k<(n) remained an open problem.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384246"}, {"primary_key": "2685983", "vector": [], "sparse_vector": [], "title": "Fully-dynamic planarity testing in polylogarithmic time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given a dynamic graph subject to insertions and deletions of edges, a natural question is whether the graph presently admits a planar embedding. We give a deterministic fully-dynamic algorithm for general graphs, running in amortized O(log3 n) time per edge insertion or deletion, that maintains a bit indicating whether or not the graph is presently planar. This is an exponential improvement over the previous best algorithm [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, 1996] which spends amortized O(gn) time per update.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384249"}, {"primary_key": "2685984", "vector": [], "sparse_vector": [], "title": "Coresets for clustering in Euclidean spaces: importance sampling is nearly optimal.", "authors": ["<PERSON><PERSON><PERSON>", "Nisheeth K. <PERSON>"], "summary": "Given a collection of n points in ℝ d , the goal of the (k,z)-clustering problem is to find a subset of k \"centers\" that minimizes the sum of the z-th powers of the Euclidean distance of each point to the closest center. Special cases of the (k,z)-clustering problem include the k-median and k-means problems. Our main result is a unified two-stage importance sampling framework that constructs an ε-coreset for the (k,z)-clustering problem. Compared to the results for (k,z)-clustering in [<PERSON> and <PERSON>, STOC 2011], our framework saves a ε2 d factor in the coreset size. Compared to the results for (k,z)-clustering in [<PERSON><PERSON> and <PERSON>, FOCS 2018], our framework saves a poly(k) factor in the coreset size and avoids the exp(k/ε) term in the construction time. Specifically, our coreset for k-median (z=1) has size Õ(ε−4 k) which, when compared to the result in [<PERSON><PERSON> and <PERSON>, STOC 2018], saves a k factor in the coreset size. Our algorithmic results rely on a new dimensionality reduction technique that connects two well-known shape fitting problems: subspace approximation and clustering, and may be of independent interest. We also provide a size lower bound of Ω(k· min{2 z/20,d }) for a 0.01-coreset for (k,z)-clustering, which has a linear dependence of size on k and an exponential dependence on z that matches our algorithmic results.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384296"}, {"primary_key": "2685985", "vector": [], "sparse_vector": [], "title": "Implementing geometric complexity theory: on the separation of orbit closures via symmetries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding the difference between group orbits and their closures is a key difficulty in geometric complexity theory (GCT): While the GCT program is set up to separate certain orbit closures (i.e., prove non-containment of one orbit closure in the other), many beautiful mathematical properties are only known for the group orbits, in particular close relations with symmetry groups and invariant spaces, while the orbit closures seem much more difficult to understand. However, in order to prove lower bounds in algebraic complexity theory, considering group orbits is not enough.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384257"}, {"primary_key": "2685986", "vector": [], "sparse_vector": [], "title": "Positive semidefinite programming: mixed, parallel, and width-independent.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give the first approximation algorithm for mixed packing and covering semidefinite programs (SDPs) with polylogarithmic dependence on width. Mixed packing and covering SDPs constitute a fundamental algorithmic primitive with applications in combinatorial optimization, robust learning, and quantum complexity. The current approximate solvers for positive semidefinite programming can handle only pure packing instances, and technical hurdles prevent their generalization to a wider class of positive instances. For a given multiplicative accuracy of є, our algorithm takes O(log3(ndρ) · є−3) parallelizable iterations, where n, d are the problem dimensions and ρ is a width parameter of the instance, generalizing or improving all previous parallel algorithms in the positive linear and semidefinite programming literature. When specialized to pure packing SDPs, our algorithm's iteration complexity is O(log2 (nd) · є−2), a slight improvement and derandomization of the state-of-the-art due to <PERSON><PERSON><PERSON> et. al. '16, <PERSON><PERSON> et. al. '16, and <PERSON> et. al. '15. For several common structured instances, the iterations of our algorithm run in nearly-linear time. In doing so, we give matrix analytic techniques to overcome obstacles that have stymied prior approaches to this problem, as stated in <PERSON><PERSON> et. al. '16 and <PERSON><PERSON><PERSON> et. al. '16. Crucial to our analysis are a simplification of existing algorithms for mixed positive linear programs, achieved by removing an asymmetry from modifying covering constraints, and a suite of matrix inequalities with proofs based on analyzing the Schur complements of matrices in a higher dimension. We hope that our algorithm and techniques open the door to improved solvers for positive semidefinite programming and its applications.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384338"}, {"primary_key": "2685987", "vector": [], "sparse_vector": [], "title": "An improved cutting plane method for convex optimization, convex-concave games, and its applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a separation oracle for a convex set K ⊂ ℝ n that is contained in a box of radius R, the goal is to either compute a point in K or prove that K does not contain a ball of radius є. We propose a new cutting plane algorithm that uses an optimal O(n log(κ)) evaluations of the oracle and an additional O(n 2) time per evaluation, where κ = nR/є.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384284"}, {"primary_key": "2685988", "vector": [], "sparse_vector": [], "title": "Approximately stable committee selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the committee selection problem, we are given m candidates, and n voters. Candidates can have different weights. A committee is a subset of candidates, and its weight is the sum of weights of its candidates. Each voter expresses an ordinal ranking over all possible committees. The only assumption we make on preferences is monotonicity: If S ⊆ S′ are two committees, then any voter weakly prefers S′ to S.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384238"}, {"primary_key": "2685989", "vector": [], "sparse_vector": [], "title": "Separations and equivalences between turnstile streaming and linear sketching.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A longstanding observation, which was partially proven by <PERSON>, <PERSON>, and <PERSON> in 2014, and extended by <PERSON>, <PERSON>, <PERSON>, and <PERSON> in 2016, is that any turnstile streaming algorithm can be implemented as a linear sketch (the reverse is trivially true). We study the relationship between turnstile streaming and linear sketching algorithms in more detail, giving both new separations and new equivalences between the two models.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384278"}, {"primary_key": "2685990", "vector": [], "sparse_vector": [], "title": "A phase transition and a quadratic time unbiased estimator for network reliability.", "authors": ["<PERSON>"], "summary": "We improve the time for approximating network (un)reliability to (n 2). We do so not with a new algorithm, but with a deeper analysis and tweaking of algorithms from our previous work. In particular, we show that once a graph's failure probability shrinks below 1/2, the graph rapidly transitions to a regime where even the expected number of cut failures is small, and in fact is almost exactly the same as the graph's failure probability. That is, we are very unlikely to ever see more than one cut fail. This lets us treat these cut failures as essentially independent, making it easier to estimate their likelihood. The contribution of this paper is not just the improved time bound, but also this clearer understanding of the evolution of a graph's reliability. Our results rely on some new methods for analyzing the distribution of cut failures conditioned on the failure of a particular cut, as well as new insights into the evolution of a graph's connectivity as edges are randomly added over time. Some of our results apply more broadly, to all monotone reliability systems.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384336"}, {"primary_key": "2685991", "vector": [], "sparse_vector": [], "title": "An improved approximation algorithm for TSP in the half integral case.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design a 1.49993-approximation algorithm for the metric traveling salesperson problem (TSP) for instances in which an optimal solution to the subtour linear programming relaxation is half-integral. These instances received significant attention over the last decade due to a conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> stating that half-integral LP solutions have the largest integrality gap over all fractional solutions. So, if the conjecture of <PERSON><PERSON> et al. holds true, our result shows that the integrality gap of the subtour polytope is bounded away from 3/2.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384273"}, {"primary_key": "2685992", "vector": [], "sparse_vector": [], "title": "Constant factor approximations to edit distance on far input pairs in nearly linear time.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For any T ≥ 1, there are constants R=R(T) ≥ 1 and ζ=ζ(T)>0 and a randomized algorithm that takes as input an integer n and two strings x,y of length at most n, and runs in time O(n 1+1/T ) and outputs an upper bound U on the edit distance of edit(x,y) that with high probability, satisfies U ≤ R(edit(x,y)+n 1−ζ). In particular, on any input with edit(x,y) ≥ n 1−ζ the algorithm outputs a constant factor approximation with high probability. A similar result has been proven independently by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (this proceedings).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384307"}, {"primary_key": "2685993", "vector": [], "sparse_vector": [], "title": "Walking randomly, massively, and efficiently.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a set of techniques that allow for efficiently generating many independent random walks in the Massively Parallel Computation (MPC) model with space per machine strongly sublinear in the number of vertices. In this space-per-machine regime, many natural approaches to graph problems struggle to overcome the Θ(log n) MPC round complexity barrier, where n is the number of vertices. Our techniques enable achieving this for PageRank—one of the most important applications of random walks—even in more challenging directed graphs, as well as for approximate bipartiteness and expansion testing.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384303"}, {"primary_key": "2685994", "vector": [], "sparse_vector": [], "title": "Strong self-concordance and sampling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Santosh S<PERSON>"], "summary": "Motivated by the <PERSON><PERSON> walk, we develop aspects of the interior-point theory for sampling in high dimension. Specifically, we introduce the notions of strong self-concordance and symmetry for a barrier. These properties imply that the <PERSON><PERSON> walk defined using a strongly self-concordant barrier with symmetry parameter ν mixes in Õ(nν) steps from a warm start for a convex body in ℝ n . For many natural barriers, ν is roughly bounded by ν, the standard self-concordance parameter. We also show that these properties hold for the Lee-Sidford barrier. As a consequence, we obtain the first walk that mixes in Õ(n 2) steps for an arbitrary polytope in ℝ n . Strong self-concordance for other barriers leads to an interesting (and unexpected) connection — for the universal and entropic barriers, it is implied by the KLS conjecture.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384272"}, {"primary_key": "2685995", "vector": [], "sparse_vector": [], "title": "Three-in-a-tree in near linear time.", "authors": ["Kai<PERSON><PERSON>", "Hsueh-I Lu", "<PERSON><PERSON><PERSON>"], "summary": "The three-in-a-tree problem is to determine if a simple undirected graph contains an induced subgraph which is a tree connecting three given vertices. Based on a beautiful characterization that is proved in more than twenty pages, <PERSON><PERSON><PERSON> and <PERSON> [Combinatorica 2010] gave the previously only known polynomial-time algorithm, running in $O(mn^2)$ time, to solve the three-in-a-tree problem on an $n$-vertex $m$-edge graph. Their three-in-a-tree algorithm has become a critical subroutine in several state-of-the-art graph recognition and detection algorithms. In this paper we solve the three-in-a-tree problem in $\\tilde{O}(m)$ time, leading to improved algorithms for recognizing perfect graphs and detecting thetas, pyramids, beetles, and odd and even holes. Our result is based on a new and more constructive characterization than that of <PERSON><PERSON><PERSON> and <PERSON>. Our new characterization is stronger than the original, and our proof implies a new simpler proof for the original characterization. The improved characterization gains the first factor $n$ in speed. The remaining improvement is based on dynamic graph algorithms.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384235"}, {"primary_key": "2685996", "vector": [], "sparse_vector": [], "title": "A spectral approach to network design.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a spectral approach to design approximation algorithms for network design problems. We observe that the underlying mathematical questions are the spectral rounding problems, which were studied in spectral sparsification and in discrepancy theory. We extend these results to incorporate additional linear constraints, and show that they can be used to significantly extend the scope of network design problems that can be solved. Our algorithm for spectral rounding is an iterative randomized rounding algorithm based on the regret minimization framework. In some settings, this provides an alternative spectral algorithm to achieve constant factor approximation for survivable network design, and partially answers a question of Bansal about survivable network design with concentration property. We also show that the spectral rounding results have many other applications, including weighted experimental design and additive spectral sparsification.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384313"}, {"primary_key": "2685997", "vector": [], "sparse_vector": [], "title": "On the computability of continuous maximum entropy distributions with applications.", "authors": ["<PERSON>", "Nisheeth K. <PERSON>"], "summary": "We initiate a study of the following problem: Given a continuous domain Ω along with its convex hull K, a point A ∈ K and a prior measure µ on Ω, find the probability density over Ω whose marginal is A and that minimizes the KL-divergence to µ. This framework gives rise to several extremal distributions that arise in mathematics, quantum mechanics, statistics, and theoretical computer science. Our technical contributions include a polynomial bound on the norm of the optimizer of the dual problem that holds in a very general setting and relies on a \"balance\" property of the measure µ on Ω, and exact algorithms for evaluating the dual and its gradient for several interesting settings of Ω and µ. Together, along with the ellipsoid method, these results imply polynomial-time algorithms to compute such KL-divergence minimizing distributions in several cases. Applications of our results include: 1) an optimization characterization of the Goemans-Williamson measure that is used to round a positive semidefinite matrix to a vector, 2) the computability of the entropic barrier for polytopes studied by <PERSON><PERSON><PERSON> and <PERSON>, and 3) a polynomial-time algorithm to compute the barycentric quantum entropy of a density matrix that was proposed as an alternative to von Neumann entropy by <PERSON> and <PERSON> in the 1970s: this corresponds to the case when Ω is the set of rank one projection matrices and µ corresponds to the Haar measure on the unit sphere. Our techniques generalize to the setting of rank k projections using the <PERSON><PERSON><PERSON><PERSON> formula, and are applicable even beyond, to adjoint orbits of compact Lie groups.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384302"}, {"primary_key": "2685998", "vector": [], "sparse_vector": [], "title": "Faster parallel algorithm for approximate shortest path.", "authors": ["<PERSON>"], "summary": "We present the first m polylog(n) work, polylog(n) time algorithm in the PRAM model that computes (1+є)-approximate single-source shortest paths on weighted, undirected graphs. This improves upon the breakthrough result of <PERSON> [JACM'00] that achieves O(m 1+є0 ) work and polylog(n) time. While most previous approaches, including <PERSON>'s, leveraged the power of hopsets, our algorithm builds upon the recent developments in continuous optimization, studying the shortest path problem from the lens of the closely-related minimum transshipment problem. To obtain our algorithm, we demonstrate a series of near-linear work, polylogarithmic-time reductions between the problems of approximate shortest path, approximate transshipment, and ℓ1-embeddings, and establish a recursive algorithm that cycles through the three problems and reduces the graph size on each cycle. As a consequence, we also obtain faster parallel algorithms for approximate transshipment and ℓ1-embeddings with polylogarithmic distortion. The minimum transshipment algorithm in particular improves upon the previous best m 1+o(1) work sequential algorithm of Sherman [SODA'17].", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384268"}, {"primary_key": "2685999", "vector": [], "sparse_vector": [], "title": "A polynomial lower bound on adaptive complexity of submodular maximization.", "authors": ["Wenzheng Li", "<PERSON>", "<PERSON>"], "summary": "In large-data applications, it is desirable to design algorithms with a high degree of parallelization. In the context of submodular optimization, adaptive complexity has become a widely-used measure of an algorithm's \"sequentiality\". Algorithms in the adaptive model proceed in rounds, and can issue polynomially many queries to a function f in each round. The queries in each round must be independent, produced by a computation that depends only on query results obtained in previous rounds.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384311"}, {"primary_key": "2686000", "vector": [], "sparse_vector": [], "title": "Faster energy maximization for faster maximum flow.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we provide an algorithm which given any m-edge n-vertex directed graph with integer capacities at most U computes a maximum s-t flow for any vertices s and t in m 11/8+o(1) U 1/4 time with high probability. This running time improves upon the previous best of Õ(m 10/7 U 1/7) (<PERSON><PERSON><PERSON> 2016), Õ(m √n logU) (<PERSON> 2014), and O(mn) (<PERSON><PERSON> 2013) when the graph is not too dense or has large capacities.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384247"}, {"primary_key": "2686001", "vector": [], "sparse_vector": [], "title": "Lower bound for succinct range minimum query.", "authors": ["<PERSON><PERSON><PERSON>", "Huacheng Yu"], "summary": "Given an integer array A[1..n], the Range Minimum Query problem (RMQ) asks to preprocess A into a data structure, supporting RMQ queries: given a,b∈ [1,n], return the index i∈[a,b] that minimizes A[i], i.e., argmin i∈[a,b] A[i]. This problem has a classic solution using O(n) space and O(1) query time by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> (STOC, 1984) and <PERSON><PERSON>, <PERSON> (SICOMP, 1984). The best known data structure by <PERSON>, <PERSON> (SICOMP, 2011) and <PERSON>, <PERSON> (TALG, 2014) uses 2n+n/(logn/t) t +Õ(n 3/4) bits and answers queries in O(t) time, assuming the word-size is w=Θ(logn). In particular, it uses 2n+n/polylogn bits of space as long as the query time is a constant.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384260"}, {"primary_key": "2686002", "vector": [], "sparse_vector": [], "title": "An exponential time parameterized algorithm for planar disjoint paths.", "authors": ["<PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Disjoint Paths problem, the input is an undirected graph G on n vertices and a set of k vertex pairs, {s i ,t i } i=1 k , and the task is to find k pairwise vertex-disjoint paths such that the i'th path connects s i to t i . In this paper, we give a parameterized algorithm with running time 2 O(k 2) n O(1) for Planar Disjoint Paths, the variant of the problem where the input graph is required to be planar. Our algorithm is based on the unique linkage/treewidth reduction theorem for planar graphs by <PERSON> et al. [JCTB 2017], the algebraic co-homology based technique developed by <PERSON><PERSON><PERSON><PERSON><PERSON> [SICOMP 1994] for Disjoint Paths on directed planar graphs, and one of the key combinatorial insights developed by <PERSON><PERSON> et al. [FOCS 2013] in their algorithm for Disjoint Paths on directed planar graphs. To the best of our knowledge our algorithm is the first parameterized algorithm to exploit that the treewidth of the input graph is small in a way completely different from the use of dynamic programming.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384250"}, {"primary_key": "2686003", "vector": [], "sparse_vector": [], "title": "Decision list compression by mild random restrictions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A decision list is an ordered list of rules. Each rule is specified by a term, which is a conjunction of literals, and a value. Given an input, the output of a decision list is the value corresponding to the first rule whose term is satisfied by the input. Decision lists generalize both CNFs and DNFs, and have been studied both in complexity theory and in learning theory.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384241"}, {"primary_key": "2686004", "vector": [], "sparse_vector": [], "title": "Non-adaptive adaptive sampling on turnstile streams.", "authors": ["<PERSON><PERSON><PERSON>", "Ilya <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Adaptive sampling is a useful algorithmic tool for data summarization problems in the classical centralized setting, where the entire dataset is available to the single processor performing the computation. Adaptive sampling repeatedly selects rows of an underlying matrix A∈ℝ n× d , where n≫ d, with probabilities proportional to their distances to the subspace of the previously selected rows. Intuitively, adaptive sampling seems to be limited to trivial multi-pass algorithms in the streaming model of computation due to its inherently sequential nature of assigning sampling probabilities to each row only after the previous iteration is completed. Surprisingly, we show this is not the case by giving the first one-pass algorithms for adaptive sampling on turnstile streams and using space poly(d,k,logn), where k is the number of adaptive sampling rounds to be performed.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384331"}, {"primary_key": "2686005", "vector": [], "sparse_vector": [], "title": "The program-size complexity of self-assembled paths.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove a Pumping Lemma for the noncooperative abstract Tile Assembly Model, a model central to the theory of algorithmic self-assembly since the beginning of the field. This theory suggests, and our result proves, that small differences in the nature of adhesive bindings between abstract square molecules gives rise to vastly different expressive capabilities. In the cooperative abstract Tile Assembly Model, square tiles attach to each other using multi-sided cooperation of one, two or more sides. This precise control of tile binding is directly exploited for algorithmic tasks including growth of specified shapes using very few tile types, as well as simulation of Turing machines and even self-simulation of self-assembly systems. But are cooperative bindings required for these computational tasks? The definitionally simpler noncooperative (or Temperature 1) model has poor control over local binding events: tiles stick if they bind on at least one side. This has led to the conjecture that it is impossible for it to exhibit precisely controlled growth of computationally-defined shapes. Here, we prove such an impossibility result. We show that any planar noncooperative system that attempts to grow large algorithmically-controlled tile-efficient assemblies must also grow infinite non-algorithmic (pumped) structures with a simple closed-form description, or else suffer blocking of intended algorithmic structures. Our result holds for both directed and nondirected systems, and gives an explicit upper bound of (8|T|)4|T|+1(5|σ| + 6), where |T| is the size of the tileset and |σ| is the size of the seed assembly, beyond which any path of tiles is pumpable or blockable.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384263"}, {"primary_key": "2686006", "vector": [], "sparse_vector": [], "title": "The impossibility of efficient quantum weak coin flipping.", "authors": ["<PERSON>"], "summary": "How can two parties with competing interests carry out a fair coin flip across a quantum communication channel? This problem (quantum weak coin-flipping) was formalized more than 15 years ago, and, despite some phenomenal theoretical progress, practical quantum coin-flipping protocols with vanishing bias have proved hard to find. In the current work we show that there is a reason that practical weak quantum coin-flipping is difficult: any quantum weak coin-flipping protocol with bias є must use at least exp( Ω (1/√є )) rounds of communication. This is a large improvement over the previous best known lower bound of Ω ( log log(1/є )) due to <PERSON><PERSON><PERSON><PERSON> from 2004. Our proof is based on a theoretical construction (the two-variable profile function) which may find further applications.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384276"}, {"primary_key": "2686007", "vector": [], "sparse_vector": [], "title": "Dynamic algorithms for LIS and distance to monotonicity.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we provide new approximation algorithms for dynamic variations of the longest increasing subsequence (LIS) problem, and the complementary distance to monotonicity (DTM) problem. In this setting, operations of the following form arrive sequentially: (i) add an element, (ii) remove an element, or (iii) substitute an element for another. At every point in time, the algorithm has an approximation to the longest increasing subsequence (or distance to monotonicity). We present a (1+є)-approximation algorithm for DTM with polylogarithmic worst-case update time and a constant factor approximation algorithm for LIS with worst-case update time Õ(n є) for any constant є > 0.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384240"}, {"primary_key": "2686008", "vector": [], "sparse_vector": [], "title": "Explicit near-Ramanujan graphs of every degree.", "authors": ["<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "For every constant d ≥ 3 and є > 0, we give a deterministic poly(n)-time algorithm that outputs a d-regular graph on Θ(n) vertices that is є-near-Ramanujan; i.e., its eigenvalues are bounded in magnitude by 2√d−1 + є (excluding the single trivial eigenvalue of d).", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384231"}, {"primary_key": "2686009", "vector": [], "sparse_vector": [], "title": "Lifting sum-of-squares lower bounds: degree-2 to degree-4.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The degree-4 Sum-of-Squares (SoS) SDP relaxation is a powerful algorithm that captures the best known polynomial time algorithms for a broad range of problems including MaxCut, Sparsest Cut, all MaxCSPs and tensor PCA. Despite being an explicit algorithm with relatively low computational complexity, the limits of degree-4 SoS SDP are not well understood. For example, existing integrality gaps do not rule out a (2−)-algorithm for Vertex Cover or a (0.878+)-algorithm for MaxCut via degree-4 SoS SDPs, each of which would refute the notorious Unique Games Conjecture.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384319"}, {"primary_key": "2686010", "vector": [], "sparse_vector": [], "title": "Weighted min-cut: sequential, cut-query, and streaming algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "Danupon <PERSON>"], "summary": "Consider the following 2-respecting min-cut problem. Given any weighted graph G and its spanning tree T, find the minimum cut among the cuts that contain at most two edges in T. This problem is an important subroutine in <PERSON><PERSON>'s celebrated randomized near-linear-time min-cut algorithm [STOC'96]. We present a new approach for this problem which can be easily implemented in many settings, leading to the following randomized min-cut algorithms for weighted graphs.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384334"}, {"primary_key": "2686011", "vector": [], "sparse_vector": [], "title": "Bipartite TSP in o(1.9999ⁿ) time, assuming quadratic time matrix multiplication.", "authors": ["<PERSON><PERSON>"], "summary": "The symmetric traveling salesman problem (TSP) is the problem of finding the shortest Hamiltonian cycle in an edge-weighted undirected graph. In 1962 <PERSON><PERSON>, and independently <PERSON> and <PERSON>, showed that TSP instances with n cities can be solved in O(n 22 n ) time. Since then it has been a notorious problem to improve the runtime to O((2−є) n ) for some constant є>0. In this work we establish the following progress: If (s× s)-matrices can be multiplied in s 2+o(1) time, than all instances of TSP in bipartite graphs can be solved in O(1.9999 n ) time by a randomized algorithm with constant error probability. We also indicate how our methods may be useful to solve TSP in non-bipartite graphs.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384264"}, {"primary_key": "2686012", "vector": [], "sparse_vector": [], "title": "Detecting and counting small patterns in planar graphs in subexponential parameterized time.", "authors": ["<PERSON><PERSON>"], "summary": "We resolve the fine-grained parameterized complexity of detecting and counting small patterns in planar graphs, assuming the Exponential Time Hypothesis. Given an n-vertex planar graph G and a k-vertex pattern graph P, we compute the number of (induced) copies of P in G in time", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384261"}, {"primary_key": "2686013", "vector": [], "sparse_vector": [], "title": "Fooling Gaussian PTFs via local hyperconcentration.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a pseudorandom generator that fools degree-d polynomial threshold functions over n-dimensional Gaussian space with seed length d O(logd) · logn. All previous generators had a seed length with at least a 2 d dependence on d.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384281"}, {"primary_key": "2686014", "vector": [], "sparse_vector": [], "title": "How to lose at Monte Carlo: a simple dynamical system whose typical statistical behavior is non-computable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the simplest non-linear discrete dynamical systems, given by the logistic maps f a (x)=ax(1−x) of the interval [0,1]. We show that there exist real parameters a∈ (0,4) for which almost every orbit of f a has the same statistical distribution in [0,1], but this limiting distribution is not Turing computable. In particular, the <PERSON> method cannot be applied to study these dynamical systems.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384237"}, {"primary_key": "2686015", "vector": [], "sparse_vector": [], "title": "Polylogarithmic-time deterministic network decomposition and distributed derandomization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a simple polylogarithmic-time deterministic distributed algorithm for network decomposition. This improves on a celebrated 2 O(√logn)-time algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC'92] and settles a central and long-standing question in distributed graph algorithms. It also leads to the first polylogarithmic-time deterministic distributed algorithms for numerous other problems, hence resolving several well-known and decades-old open problems, including <PERSON><PERSON>'s question about the deterministic complexity of maximal independent set [FOCS'87; SICOMP'92]—which had been called the most outstanding problem in the area.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384298"}, {"primary_key": "2686016", "vector": [], "sparse_vector": [], "title": "Near-optimal fully dynamic densest subgraph.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang"], "summary": "We give the first fully dynamic algorithm which maintains a (1−є)-approximate densest subgraph in worst-case time poly(logn, є−1) per update. Dense subgraph discovery is an important primitive for many real-world applications such as community detection, link spam detection, distance query indexing, and computational biology. We approach the densest subgraph problem by framing its dual as a graph orientation problem, which we solve using an augmenting path-like adjustment technique. Our result improves upon the previous best approximation factor of (1/4 − є) for fully dynamic densest subgraph [<PERSON><PERSON><PERSON> et. al., STOC '15]. We also extend our techniques to solving the problem on vertex-weighted graphs with similar runtimes.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384327"}, {"primary_key": "2686017", "vector": [], "sparse_vector": [], "title": "Combinatorial list-decoding of Reed-Solomon codes beyond the Johnson radius.", "authors": ["Chong <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "List-decoding of Reed-Solomon (RS) codes beyond the so called Johnson radius has been one of the main open questions in coding theory and theoretical computer science since the work of <PERSON><PERSON><PERSON> and Sudan. It is now known by the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, using techniques from high dimensional probability, that over large enough alphabets there exist RS codes that are indeed list-decodable beyond this radius.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384295"}, {"primary_key": "2686018", "vector": [], "sparse_vector": [], "title": "Towards a better understanding of randomized greedy matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There has been a long history for studying randomized greedy matching algorithms since the work by <PERSON> and <PERSON>(RSA 1991). We follow this trend and consider the problem formulated in the oblivious setting, in which the algorithm makes (random) decisions that are essentially oblivious to the input graph. We revisit the Modified Randomized Greedy (MRG) algorithm by <PERSON><PERSON><PERSON> et al.(RSA 1995) which is proved to be (0.5+epsilon)-approximate. In particular, we study a weaker version of the algorithm named Random Decision Order (RDO) that in each step, randomly picks an unmatched vertex and matches it to an arbitrary neighbor if exists. We prove the RDO algorithm is 0.639-approximate and 0.531-approximate for bipartite graphs and general graphs respectively. As a corollary, we substantially improve the approximation ratio of MRG. Furthermore, we generalize the RDO algorithm to the edge-weighted case and prove that it achieves a 0.501 approximation ratio. This result solves the open question by <PERSON> et al.(SICOMP 2018) about the existence of an algorithm that beats greedy in this setting. As a corollary, it also solves the open questions by <PERSON><PERSON><PERSON><PERSON> et al.(SODA 2019) in the stochastic setting.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384265"}, {"primary_key": "2686019", "vector": [], "sparse_vector": [], "title": "An improved approximation algorithm for ATSP.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the constant-factor approximation algorithm for the asymmetric traveling salesman problem by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [STOC 2018]. We improve on each part of this algorithm. We avoid the reduction to irreducible instances and thus obtain a simpler and much better reduction to vertebrate pairs. We also show that a slight variant of their algorithm for vertebrate pairs has a much smaller approximation ratio. Overall we improve the approximation ratio from 506 to 22+ε for any ε > 0. This also improves the upper bound on the integrality ratio from 319 to 22.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384233"}, {"primary_key": "2686020", "vector": [], "sparse_vector": [], "title": "Reducing path TSP to TSP.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a black-box reduction from the path version of the Traveling Salesman Problem (Path TSP) to the classical tour version (TSP). More precisely, we show that given an α-approximation algorithm for TSP, then, for any ε >0, there is an (α+ε)-approximation algorithm for the more general Path TSP. This reduction implies that the approximability of Path TSP is the same as for TSP, up to an arbitrarily small error. This avoids future discrepancies between the best known approximation factors achievable for these two problems, as they have existed until very recently.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384256"}, {"primary_key": "2686021", "vector": [], "sparse_vector": [], "title": "Rounding dynamic matchings against an adaptive adversary.", "authors": ["<PERSON>"], "summary": "We present a new dynamic matching sparsification scheme. From this scheme we derive a framework for dynamically rounding fractional matchings against adaptive adversaries. Plugging in known dynamic fractional matching algorithms into our framework, we obtain numerous randomized dynamic matching algorithms which work against adaptive adversaries. In contrast, all previous randomized algorithms for this problem assumed a weaker, oblivious, adversary. Our dynamic algorithms against adaptive adversaries include, for any constant є >0, a (2+є)-approximate algorithm with constant update time or polylog worst-case update time, as well as (2−δ)-approximate algorithms in bipartite graphs with arbitrarily-small polynomial update time. All these results achieve polynomially better update time to approximation trade-offs than previously known to be achievable against adaptive adversaries.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384258"}, {"primary_key": "2686022", "vector": [], "sparse_vector": [], "title": "Nearly optimal static Las Vegas succinct dictionary.", "authors": ["Huacheng Yu"], "summary": "Given a set S of n (distinct) keys from key space [U], each associated with a value from Σ, the static dictionary problem asks to preprocess these (key, value) pairs into a data structure, supporting value-retrieval queries: for any given x∈ [U], valRet(x) must return the value associated with x if x∈ S, or return ⊥ if x∉ S. The special case where |Σ|=1 is called the membership problem. The \"textbook\" solution is to use a hash table, which occupies linear space and answers each query in constant time. On the other hand, the minimum possible space to encode all (key, value) pairs is only OPT:= ⌈lg2(", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384274"}, {"primary_key": "2686023", "vector": [], "sparse_vector": [], "title": "QCSP monsters and the demise of the chen conjecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a surprising classification for the computational complexity of the Quantified Constraint Satisfaction Problem over a constraint language Γ, QCSP(Γ), where Γ is a finite language over 3 elements which contains all constants. In particular, such problems are either in P, NP-complete, co-NP-complete or PSpace-complete. Our classification refutes the hitherto widely-believed Chen Conjecture.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713.3384232"}, {"primary_key": "2702482", "vector": [], "sparse_vector": [], "title": "Proceedings of the 52nd Annual ACM SIGACT Symposium on Theory of Computing, STOC 2020, Chicago, IL, USA, June 22-26, 2020", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The papers in this volume were presented at the Fifty Second Annual ACM Symposium on Theory of Computing (STOC 2020), sponsored by the ACM Special Interest Group on Algorithms and Computation Theory (SIGACT). The conference was originally planned to be held in Chicago, but due to the COVID-19 pandemic it was held online. The papers were presented both as pre-recorded video talks, and during online sessions held between June 22-26, 2020. STOC 2020 was part of Theory Fest, that also included several workshops.", "published": "2020-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3357713"}]