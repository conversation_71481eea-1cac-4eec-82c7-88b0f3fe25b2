[{"primary_key": "1769608", "vector": [], "sparse_vector": [], "title": "SpinOcchietto: A Wearable Skin-Slip Haptic Device for Rendering Width and Motion of Objects Gripped Between the Fingertips.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Various haptic feedback techniques have been explored to enable users to interact with their virtual surroundings using their hands. However, investigation on interactions with virtual objects slipping against the skin using skin-slip haptic feedback is still at its early stages. Prior skin-slip virtual reality (VR) haptic display implementations involved bulky actuation mechanisms and were not suitable for multi-finger and bimanual interactions. As a solution to this limitation, we present SpinOcchietto, a wearable skin-slip haptic feedback device using spinning discs for rendering the width and movement of virtual objects gripped between the fingertips. SpinOcchietto was developed to miniaturize and simplify SpinOcchio[1], a 6-DoF handheld skin-slip haptic display. With its smaller, lighter, and wearable form factor, SpinOcchietto enables users with a wide range of hand sizes to interact with virtual objects with their thumb and index fingers while freeing the rest of the hand. Users can perceive the speed of virtual objects slipping against the fingertips and can use varying grip strengths to grab and release the objects. Three demo applications were developed to showcase the different types of virtual object interactions enabled by the prototype.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558651"}, {"primary_key": "1769609", "vector": [], "sparse_vector": [], "title": "BO as Assistant: Using Bayesian Optimization for Asynchronously Generating Design Suggestions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many design tasks involve parameter adjustment, and designers often struggle to find desirable parameter value combinations by manipulating sliders back and forth. For such a multi-dimensional search problem, Bayesian optimization (BO) is a promising technique because of its intelligent sampling strategy; in each iteration, BO samples the most effective points considering both exploration (i.e., prioritizing unexplored regions) and exploitation (i.e., prioritizing promising regions), enabling efficient searches. However, existing BO-based design frameworks take the initiative in the design process and thus are not flexible enough for designers to freely explore the design space using their domain knowledge. In this paper, we propose a novel design framework, BO as Assistant, which enables designers to take the initiative in the design process while also benefiting from BO's sampling strategy. The designer can manipulate sliders as usual; the system monitors the slider manipulation to automatically estimate the design goal on the fly and then asynchronously provides unexplored-yet-promising suggestions using BO's sampling strategy. The designer can choose to use the suggestions at any time. This framework uses a novel technique to automatically extract the necessary information to run BO by observing slider manipulation without requesting additional inputs. Our framework is domain-agnostic, demonstrated by applying it to photo color enhancement, 3D shape design for personal fabrication, and procedural material design in computer graphics.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545664"}, {"primary_key": "1769610", "vector": [], "sparse_vector": [], "title": "ICEBOAT: An Interactive User Behavior Analysis Tool for Automotive User Interfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we present ICEBOAT an interactive tool that enables automotive UX experts to explore how users interact with In-Vehicle Information Systems (IVISs). Based on large naturalistic driving data continuously collected from production line vehicles, ICEBOAT visualizes drivers' interactions and driving behavior on different levels of detail. Hence, it allows to easily compare different user flows based on performance- and safety-related metrics.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558739"}, {"primary_key": "1769611", "vector": [], "sparse_vector": [], "title": "DEEP: 3D Gaze Pointing in Virtual Reality Leveraging Eyelid Movement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Wenjing Tang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gaze-based target suffers from low input precision and target occlusion. In this paper, we explored to leverage the continuous eyelid movement to support high-efficient and occlusion-robust dwell-based gaze pointing in virtual reality. We first conducted two user studies to examine the users' eyelid movement pattern both in unintentional and intentional conditions. The results proved the feasibility of leveraging intentional eyelid movement that was distinguishable with natural movements for input. We also tested the participants' dwelling pattern for targets with different sizes and locations. Based on these results, we propose DEEP, a novel technique that enables the users to see through occlusions by controlling the aperture angle of their eyelids and dwell to select the targets with the help of a probabilistic input prediction model. Evaluation results showed that DEEP with dynamic depth and location selection incorporation significantly outperformed its static variants, as well as a naive dwelling baseline technique. Even for 100% occluded targets, it could achieve an average selection speed of 2.5s with an error rate of 2.3%.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545673"}, {"primary_key": "1769612", "vector": [], "sparse_vector": [], "title": "Point Cloud Capture and Editing for AR Environmental Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a tablet-based system for AR environmental design using point clouds. It integrates point cloud capture and editing in a single AR workflow to help users quickly prototype design ideas in their spatial context. We hypothesize that point clouds are well suited for prototyping, as they can be captured rapidly and then edited immediately on the capturing device in situ. Our system supports a variety of point cloud editing operations in AR, including selection, transformation, hole filling, drawing, and animation. This enables a wide range of design applications for objects, interior environments, buildings, and landscapes.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558636"}, {"primary_key": "1769613", "vector": [], "sparse_vector": [], "title": "HingeCore: Laser-Cut Foamcore for Fast Assembly.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present HingeCore, a novel type of laser-cut 3D structure made from sandwich materials, such as foamcore. The key design element behind HingeCore is what we call a finger hinge, which we produce by laser-cutting foamcore \"half-way\". The primary benefit of finger hinges is that they allow for very fast assembly, as they allow models to be assembled by folding and because folded hinges stay put at the intended angle, based on the friction between fingers alone, which eliminates the need for glue or tabs. Finger hinges are also highly robust, with some 5mm foamcore models withstanding 62kg. We present HingeCoreMaker, a stand-alone software tool that automatically converts 3D models to HingeCore layouts, as well as an integration into a 3D modeling tool for laser cutting (Kyub [7]). We have used HingeCoreMaker to fabricate design objects, including speakers, lamps, and a life-size bust, as well as structural objects, such as functional furniture. In our user study, participants assembled HingeCore layouts 2.9x faster than layouts generated using the state-of-the-art for plate-based assembly (Roadkill [1]).", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545618"}, {"primary_key": "1769614", "vector": [], "sparse_vector": [], "title": "spaceR: Knitting Ready-Made, Tactile, and Highly Responsive Spacer-Fabric Force Sensors for Continuous Input.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With spaceR, we present both design and implementation of a resistive force-sensor based on a spacer fabric knit. Due to its softness and elasticity, our sensor provides an appealing haptic experience. It enables continuous input with high precision due to its innate haptic feedback and can be manufactured ready-made on a regular two-bed weft knitting machine, without requiring further post-processing steps. For our multi-component knit, we add resistive yarn to the filler material, in order to achieve a highly sensitive and responsive pressure sensing textile. Sensor resistance drops by ~90% when actuated with moderate finger pressure of 2 N, making the sensor accessible also for straightforward readout electronics. We discuss related manufacturing parameters and their effect on shape and electrical characteristics and explore design opportunities to harness visual and tactile affordances. Finally, we demonstrate several application scenarios by implementing diverse spaceR variations, including analog rocker- and four-way directional buttons, and show the possibility of mode-switching by tracking temporal data.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545694"}, {"primary_key": "1769615", "vector": [], "sparse_vector": [], "title": "LeviCircuits: Adhoc Electrical Circuit Prototyping using Ultrasound Levitation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "poster LeviCircuits: Adhoc Electrical Circuit Prototyping using Ultrasound Levitation Authors: <AUTHORS>", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561352"}, {"primary_key": "1769616", "vector": [], "sparse_vector": [], "title": "Wikxhibit: Using HTML and Wikidata to Author Applications that Link Data Across the Web.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wikidata is a companion to Wikipedia that captures a substantial part of the information about most Wikipedia entities in machine-readable structured form. In addition to directly representing information from Wikipedia itself, Wikidata also cross-references how additional information about these entities can be accessed through APIs on hundreds of other websites.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545706"}, {"primary_key": "1769617", "vector": [], "sparse_vector": [], "title": "FLEX-SDK: An Open-Source Software Development Kit for Creating Social Robots.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>n <PERSON>", "<PERSON>"], "summary": "We present FLEX-SDK: an open-source software development kit that allows creating a social robot from two simple tablet screens. FLEX-SDK involves tools for designing the robot face and its facial expressions, creating screens for input/output interactions, controlling the robot through a Wizard-of-Oz interface, and scripting autonomous interactions through a simple text-based programming interface. We demonstrate how this system can be used to replicate an interaction study and we present nine case studies involving controlled experiments, observational studies, participatory design sessions, and outreach activities in which our tools were used by researchers and participants to create and interact with social robots. We discuss common observations and lessons learned from these case studies. Our work demonstrates the potential of FLEX-SDK to lower the barrier to entry for Human-Robot Interaction research.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545707"}, {"primary_key": "1769618", "vector": [], "sparse_vector": [], "title": "Shadow Play using Ultrasound Levitated Props.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shadow play is a traditional culture to communicate narratives. However, its inheritance is in danger, and traditional methods have limitations in their expression. We propose a novel system to perform a shadow play by levitating props using an ultrasound speaker array. Our system computes an ideal position of levitating props to create a shadow of the desired image. Shadow play will be performed by displaying a sequence of images as shadows. Since the performance is automated, our work makes shadow play accessible to people for generations. Also, our system allows 6 DoF and floating movement of props, which expands the limit of expression. Through this system, we aim to enhance shadow plays informatically and aesthetically.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561349"}, {"primary_key": "1769619", "vector": [], "sparse_vector": [], "title": "Notational Programming for Notebook Environments: A Case Study with Quantum Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tapan <PERSON>"], "summary": "We articulate a vision for computer programming that includes pen-based computing, a paradigm we term notational programming. Notational programming blurs contexts: certain typewritten variables can be referenced in handwritten notation and vice-versa. To illustrate this paradigm, we developed an extension, Notate, to computational notebooks which allows users to open drawing canvases within lines of code. As a case study, we explore quantum programming and designed a notation, Qaw, that extends quantum circuit notation with abstraction features, such as variable-sized wire bundles and recursion. Results from a usability study with novices suggest that users find our core interaction of implicit cross-context references intuitive, but suggests further improvements to debugging infrastructure, interface design, and recognition rates. Throughout, we discuss questions raised by the notational paradigm, including a shift from 'recognition' of notations to 'reconfiguration' of practices and values around programming, and from 'sketching' to writing and drawing, or what we call 'notating.'", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545619"}, {"primary_key": "1769620", "vector": [], "sparse_vector": [], "title": "Over-The-Shoulder Training Between Redundant Wearable Sensors for Unified Gesture Interactions.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Seongkook Heo"], "summary": "Wearable computers are now prevalent, and it is not uncommon to see people wearing multiple wearable devices. These wearable devices are often equipped with sensors to detect the user's interactions and context. As more devices are worn on the user's body, there is an increasing redundancy between the sensors. For example, swiping gestures on a headphone are detected by its touch sensor, but the movement it caused can also be measured by the sensors in a smartwatch or smart rings. We present a new mechanism to train a gesture recognition model using redundant sensor data so that measurements from other sensors can be used to detect gestures performed on another device even if the device is missing. Our preliminary study with 13 participants revealed that a unified gesture recognition model for touch gestures achieved accuracy for 25 gestures (5 gestures × 5 scenarios) where gestures were trained by leveraging the available sensors.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558725"}, {"primary_key": "1769621", "vector": [], "sparse_vector": [], "title": "AUIT - the Adaptive User Interfaces Toolkit for Designing XR Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Adaptive user interfaces can improve experiences in Extended Reality (XR) applications by adapting interface elements according to the user’s context. Although extensive work explores different adaptation policies, XR creators often struggle with their implementation, which involves laborious manual scripting. The few available tools are underdeveloped for realistic XR settings where it is often necessary to consider conflicting aspects that affect an adaptation. We fill this gap by presenting AUIT, a toolkit that facilitates the design of optimization-based adaptation policies. AUIT allows creators to flexibly combine policies that address common objectives in XR applications, such as element reachability, visibility, and consistency. Instead of using rules or scripts, specifying adaptation policies via adaptation objectives simplifies the design process and enables creative exploration of adaptations. After creators decide which adaptation objectives to use, a multi-objective solver finds appropriate adaptations in real-time. A study showed that AUIT allowed creators of XR applications to quickly and easily create high-quality adaptations.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545651"}, {"primary_key": "1769622", "vector": [], "sparse_vector": [], "title": "Summarizing Sets of Related ML-Driven Recommendations for Improving File Management in Cloud Storage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> U<PERSON>"], "summary": "Personal cloud storage systems increasingly offer recommendations to help users retrieve or manage files of interest. For example, Google Drive's Quick Access predicts and surfaces files likely to be accessed. However, when multiple, related recommendations are made, interfaces typically present recommended files and any accompanying explanations individually, burdening users. To improve the usability of ML-driven personal information management systems, we propose a new method for summarizing related file-management recommendations. We generate succinct summaries of groups of related files being recommended. Summaries reference the files' shared characteristics. Through a within-subjects online study in which participants received recommendations for groups of files in their own Google Drive, we compare our summaries to baselines like visualizing a decision tree model or simply listing the files in a group. Compared to the baselines, participants expressed greater understanding and confidence in accepting recommendations when shown our novel recommendation summaries.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545704"}, {"primary_key": "1769623", "vector": [], "sparse_vector": [], "title": "ARfy: A Pipeline for Adapting 3D Scenes to Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Virtual content placement in physical scenes is a crucial aspect of augmented reality (AR). This task is particularly challenging when the virtual elements must adapt to multiple target physical environments that are unknown during development. AR authors use strategies such as manual placement performed by end-users, automated placement powered by author-defined constraints, and procedural content generation to adapt virtual content to physical spaces. Although effective, these options require human effort or annotated virtual assets. As an alternative, we present ARfy, a pipeline to support the adaptive placement of virtual content from pre-existing 3D scenes in arbitrary physical spaces. ARfy does not require intervention by end-users or asset annotation by AR authors. We demonstrate the pipeline capabilities using simulations on a publicly available indoor space dataset. ARfy automatically makes any generic 3D scene AR-ready and provides evaluation tools to facilitate future research on adaptive virtual content placement.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558697"}, {"primary_key": "1769624", "vector": [], "sparse_vector": [], "title": "OmniScribe: Authoring Immersive Audio Descriptions for 360° Videos.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Blind people typically access videos via audio descriptions (AD) crafted by sighted describers who comprehend, select, and describe crucial visual content in the videos. 360° video is an emerging storytelling medium that enables immersive experiences that people may not possibly reach in everyday life. However, the omnidirectional nature of 360° videos makes it challenging for describers to perceive the holistic visual content and interpret spatial information that is essential to create immersive ADs for blind people. Through a formative study with a professional describer, we identified key challenges in describing 360° videos and iteratively designed OmniScribe, a system that supports the authoring of immersive ADs for 360° videos. OmniScribe uses AI-generated content-awareness overlays for describers to better grasp 360° video content. Furthermore, OmniScribe enables describers to author spatial AD and immersive labels for blind users to consume the videos immersively with our mobile prototype. In a study with 11 professional and novice describers, we demonstrated the value of OmniScribe in the authoring workflow; and a study with 8 blind participants revealed the promise of immersive AD over standard AD for 360° videos. Finally, we discuss the implications of promoting 360° video accessibility.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545613"}, {"primary_key": "1769625", "vector": [], "sparse_vector": [], "title": "ARDW: An Augmented Reality Workbench for Printed Circuit Board Debugging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aspen Tng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel"], "summary": "Debugging printed circuit boards (PCBs) can be a time-consuming process, requiring frequent context switching between PCB design files (schematic and layout) and the physical PCB. To assist electrical engineers in debugging PCBs, we present ARDW, an augmented reality workbench consisting of a monitor interface featuring PCB design files, a projector-augmented workspace for PCBs, tracked test probes for selection and measurement, and a connected test instrument. The system supports common debugging workflows for augmented visualization on the physical PCB as well as augmented interaction with the tracked probes. We quantitatively and qualitatively evaluate the system with 10 electrical engineers from industry and academia, finding that ARDW speeds up board navigation and provides engineers with greater confidence in debugging. We discuss practical design considerations and paths for improvement to future systems. A video demo of the system may be accessed here: https://youtu.be/RbENbf5WIfc .", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545684"}, {"primary_key": "1769626", "vector": [], "sparse_vector": [], "title": "HapTag: A Compact Actuator for Rendering Push-Button Tactility on Soft Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Lin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As touch interactions become ubiquitous in the field of human computer interactions, it is critical to enrich haptic feedback to improve efficiency, accuracy, and immersive experiences. This paper presents HapTag, a thin and flexible actuator to support the integration of push button tactile renderings to daily soft surfaces. Specifically, HapTag works under the principle of hydraulically amplified electroactive actuator (HASEL) while being optimized by embedding a pressure sensing layer, and being activated with a dedicated voltage appliance in response to users' input actions, resulting in fast response time, controllable and expressive push-button tactile rendering capabilities. HapTag is in a compact formfactor and can be attached, integrated, or embedded on various soft surfaces like cloth, leather, and rubber. Three common push button tactile patterns were adopted and implemented with HapTag. We validated the feasibility and expressiveness of HapTag by demonstrating a series of innovative applications under different circumstances.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545644"}, {"primary_key": "1769627", "vector": [], "sparse_vector": [], "title": "Environmental physical intelligence: Seamlessly deploying sensors and actuators to our everyday life.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> has predicted the third generation of computing would result in individuals interacting with many computing devices and ultimately can \"weave themselves into the fabric of everyday life until they are indistinguishable from it\" [17]. However, how to achieve this seamlessness and what associated interaction should be developed are still under investigation. On the other hand, the material composition, structures and operating logic of a variety of physical objects existing in everyday life determine how we interact with them [13]. The intelligence of the built environment does not only rely on the encoded computational abilities within the \"brain\" (like the controllers of home appliances), but also the physical intelligence encoded in their \"body\" (e.g., materials, mechanical structures). In my research, I work on creating computational materials with different encoded material properties (e.g., conductivity, transparency, water-solubility, self-assembly, etc.) that can be seamlessly integrated into our living environment to enrich different modalities of information communication.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558525"}, {"primary_key": "1769628", "vector": [], "sparse_vector": [], "title": "Synthesis-Assisted Video Prototyping From a Document.", "authors": ["<PERSON>", "<PERSON> Dong", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video productions commonly start with a script, especially for talking head videos that feature a speaker narrating to the camera. When the source materials come from a written document – such as a web tutorial, it takes iterations to refine content from a text article to a spoken dialogue, while considering visual compositions in each scene. We propose Doc2Video, a video prototyping approach that converts a document to interactive scripting with a preview of synthetic talking head videos. Our pipeline decomposes a source document into a series of scenes, each automatically creating a synthesized video of a virtual instructor. Designed for a specific domain – programming cookbooks, we apply visual elements from the source document, such as a keyword, a code snippet or a screenshot, in suitable layouts. Users edit narration sentences, break or combine sections, and modify visuals to prototype a video in our Editing UI. We evaluated our pipeline with public programming cookbooks. Feedback from professional creators shows that our method provided a reasonable starting point to engage them in interactive scripting for a narrated instructional video.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545676"}, {"primary_key": "1769629", "vector": [], "sparse_vector": [], "title": "Shadowed Speech: an Audio Feedback System which Slows Down Speech Rate.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In oral communication, it is important to speak at a speed appropriate for the situation. However, we need a lot of training in order to control our speech rate as intended. This paper proposes a speech rate control system which enables the user to speak at a pace closer to the target rate using Delayed Auditory Feedback (DAF). We implement a prototype and confirm that the proposed system can slow down the speech rate when the user speaks too fast without giving any instructions to the speaker on how to respond to the audio feedback.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558640"}, {"primary_key": "1769630", "vector": [], "sparse_vector": [], "title": "TouchVR: A Modality for Instant VR Experience.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In near future, we envision instant and ubiquitous access to the VR worlds. However, existing highly portable VR devices usually lack rich and convenient input modality. In response, we introduce TouchVR, a system that enables BoD interaction in instant VR supported by mobile HMDs.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558650"}, {"primary_key": "1769631", "vector": [], "sparse_vector": [], "title": "FormSense: A Fabrication Method to Support Shape Exploration of Interactive Prototypes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "When exploring the shape of interactive objects, existing prototyping methods can conflict with the iterative process. In this paper, we present FormSense: a simple, fast, and modifiable fabrication method to support the exploration of shape when prototyping interactive objects. FormSense enables touch and pressure sensing through a multi-layer coating approach and a custom touch sensor built from commodity electronic components. We use FormSense to create four interactive prototypes of diverse geometries and materials.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558730"}, {"primary_key": "1769632", "vector": [], "sparse_vector": [], "title": "Artistic User Expressions in AI-powered Creativity Support Tools.", "authors": ["<PERSON>"], "summary": "Novel AI algorithms introduce a new generation of AI-powered Creativity Support Tools (AI-CSTs). These tools can inspire and surprise users with algorithmic outputs that the users could not expect. However, users can struggle to align their intentions with unexpected algorithmic behaviors. My dissertation research studies how user expressions in art-making AI-CSTs need to be designed. With an interview study with 14 artists and a literature survey on 111 existing CSTs, I first isolate three requirements: 1) allow users to express under-constrained intentions, 2) enable the tool and the user to co-learn the user expressions and the algorithmic behaviors, and 3) allow easy and expressive iteration. Based on these requirements, I introduce two tools, 1) Artinter, which learns how the users express their visual art concepts within their communication process for art commissions, and 2) TaleBrush, which facilitates the under-constrained and iterative expression of user intents through sketching-based story generation. My research provides guidelines for designing user expression interactions for AI-CSTs while demonstrating how they can suggest new designs of AI-CSTs.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558531"}, {"primary_key": "1769633", "vector": [], "sparse_vector": [], "title": "One-Dimensional Eye-Gaze Typing Interface for People with Locked-in Syndrome.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "People with Locked-in syndrome (LIS) suffer from complete loss of voluntary motor functions for speech or hand-writing. They are mentally intact, retaining only the control of vertical eye movements and blinking. In this work, we present a one-dimensional typing interface controlled exclusively by vertical eye movements and dwell-time for them to communicate at will. Hidden Markov Model and Bigram Models are used as auto-completion on both word and sentence level. We conducted two preliminary user studies on non-disabled users. The typing interface achieved 3.75 WPM without prediction and 11.36 WPM with prediction.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558732"}, {"primary_key": "1769634", "vector": [], "sparse_vector": [], "title": "Beyond Text Generation: Supporting Writers with Continuous Automatic Text Summaries.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a text editor to help users plan, structure and reflect on their writing process. It provides continuously updated paragraph-wise summaries as margin annotations, using automatic text summarization. Summary levels range from full text, to selected (central) sentences, down to a collection of keywords. To understand how users interact with this system during writing, we conducted two user studies (N=4 and N=8) in which people wrote analytic essays about a given topic and article. As a key finding, the summaries gave users an external perspective on their writing and helped them to revise the content and scope of their drafted paragraphs. People further used the tool to quickly gain an overview of the text and developed strategies to integrate insights from the automated summaries. More broadly, this work explores and highlights the value of designing AI tools for writers, with Natural Language Processing (NLP) capabilities that go beyond direct text generation and correction.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545672"}, {"primary_key": "1769635", "vector": [], "sparse_vector": [], "title": "DiscoBand: Multiview Depth-Sensing Smartwatch Strap for Hand, Body and Environment Tracking.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Real-time tracking of a user's hands, arms and environment is valuable in a wide variety of HCI applications, from context awareness to virtual reality. Rather than rely on fixed and external tracking infrastructure, the most flexible and consumer-friendly approaches are mobile, self-contained, and compatible with popular device form factors (e.g., smartwatches). In this vein, we contribute DiscoBand, a thin sensing strap not exceeding 1 cm in thickness. Sensors operating so close to the skin inherently face issues with occlusion. To help overcome this, our strap uses eight distributed depth sensors imaging the hand from different viewpoints, creating a sparse 3D point cloud. An additional eight depth sensors image outwards from the band to track the user's body and surroundings. In addition to evaluating arm and hand pose tracking, we also describe a series of supplemental applications powered by our band's data, including held object recognition and environment mapping.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545634"}, {"primary_key": "1769636", "vector": [], "sparse_vector": [], "title": "Project Primrose: Reflective Light-Diffuser Modules for Non-Emissive Flexible Display Systems.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in smart materials have enabled displays to move beyond planar surfaces into the fabric of everyday life. We propose reflective light-diffuser modules for non-emissive flexible display systems. Our system leverages reflective-backed polymer-dispersed liquid crystal (PDLC), an electroactive material commonly used in smart window applications. This low-power non-emissive material can be cut to any shape, and dynamically diffuses light. We present the design & fabrication of two exemplar artifacts, a canvas and a handbag, that use the reflective light-diffuser modules. We also describe our content authoring pipeline and interaction modalities. We hope this work inspires future designers of flexible displays.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545625"}, {"primary_key": "1769637", "vector": [], "sparse_vector": [], "title": "InfraredTags Demo: Invisible AR Markers and Barcodes Using Infrared Imaging and 3D Printing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We showcase InfraredTags, which are 2D markers and barcodes imperceptible to the naked eye that can be 3D printed as part of objects, and detected rapidly by low-cost near-infrared cameras. We achieve this by printing objects from an infrared-transmitting filament, which infrared cameras can see through, and by having air gaps inside for the tag's bits, which appear at a different intensity in the infrared image.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558660"}, {"primary_key": "1769638", "vector": [], "sparse_vector": [], "title": "Detecting Changes in User Emotions During Bicycle Riding by Sampling Facial Images.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Panote <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the context of mobility as a Service (MaaS), bicycles are an important mode of transport for the first and last mile between the home and other transport modalities. Also, due to covid-19 bicycle users such as food delivery drivers and commuters to work are increasing. To investigate driving experience of bicycle users in context and improve MaaS service quality, we propose and describe a method to automatically detect changes in user emotions during bicycle riding by sampling facial images using a smartphone. We describe the proposed method and how we plan to use it in the future.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558710"}, {"primary_key": "1769639", "vector": [], "sparse_vector": [], "title": "Grid-Coding: An Accessible, Efficient, and Structured Coding Paradigm for Blind and Low-Vision Programmers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sighted programmers often rely on visual cues (e.g., syntax coloring, keyword highlighting, code formatting) to perform common coding activities in text-based languages (e.g., Python). Unfortunately, blind and low-vision (BLV) programmers hardly benefit from these visual cues because they interact with computers via assistive technologies (e.g., screen readers), which fail to communicate visual semantics meaningfully. Prior work on making text-based programming languages and environments accessible mostly focused on code navigation and, to some extent, code debugging, but not much toward code editing, which is an essential coding activity.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545620"}, {"primary_key": "1769640", "vector": [], "sparse_vector": [], "title": "Towards using Involuntary Body Gestures for Measuring the User Engagement in VR Gaming.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Understanding the degree of user engagement in a VR game is vital to provide a better gaming experience. While prior work has suggested self-reports, and biological signal-based methods, measuring game engagement remains a challenge due to its complex nature. In this work, we provide a preliminary exploration of using involuntary body gestures to measure user engagement in VR gaming. Based on data collected from 27 participants performing multiple VR games, we demonstrate a relationship between foot gesture-based models for measuring arousal and physiological responses while engaging in VR games. Our findings show the possibility of using involuntary body gestures to measure engagement.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558691"}, {"primary_key": "1769641", "vector": [], "sparse_vector": [], "title": "GANzilla: User-Driven Direction Discovery in Generative Adversarial Networks.", "authors": ["<PERSON><PERSON>", "Xiang &apos;Anthony&apos; Chen"], "summary": "Generative Adversarial Network (GAN) is widely adopted in numerous application areas, such as data preprocessing, image editing, and creativity support. However, GAN's 'black box' nature prevents non-expert users from controlling what data a model generates, spawning a plethora of prior work that focused on algorithm-driven approaches to extract editing directions to control GAN. Complementarily, we propose a GANzilla: a user-driven tool that empowers a user with the classic scatter/gather technique to iteratively discover directions to meet their editing goals. In a study with 12 participants, GANzilla users were able to discover directions that (i) edited images to match provided examples (closed-ended tasks) and that (ii) met a high-level goal, e.g., making the face happier, while showing diversity across individuals (open-ended tasks).", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545638"}, {"primary_key": "1769642", "vector": [], "sparse_vector": [], "title": "VLOGS: Virtual Laboratory Observation Tool for Monitoring a Group of Students.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Virtual laboratories (VLs) enable students to conduct lab experiments in the virtual world using Virtual Reality (VR) technology, providing benefits in areas such as availability, safety as well as scalability. While there are existing platforms that provide VLs with rich content as well as research works on promoting effective learning in VLs, less attention has been paid on VLs from a teaching perspective. Students usually learn and practice in VL sessions with limited help from the instructors. Instructors, on the other hand, could only receive limited information on the performance of the students and could not provide timely feedback to facilitate students' learning. In this work, we present a prototype virtual laboratory monitoring tool, created using a design thinking approach, for addressing teaching needs when conducting a virtual laboratory session simultaneously for multiple students, similar to that in a physical lab environment.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558738"}, {"primary_key": "1769643", "vector": [], "sparse_vector": [], "title": "Flaticulation: Laser Cutting Joints with Articulated Angles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present Flaticulation, a method to laser cut joints that clutch two cut-in-place flat boards at designated articulated angles. We discover special T-patterns added on the shared edge of two pieces allowing them to be clutched at a bending angle. We analyze the structure and propose a parametric model regarding the T-pattern under laser cutting to predict the joint articulated angle. We validate our proposed model by measuring real prototypes and conducting stress-strain analysis to understand their structural strength. Finally, we provide a user interface for our example applications, including fast assembling unfolded 3D polygonal models and adding detent mechanisms for functional objects such as a mouse and reconfigurable objects such as a headphone.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545695"}, {"primary_key": "1769644", "vector": [], "sparse_vector": [], "title": "UltraBots: Large-Area Mid-Air Haptics for VR with Robotically Actuated Ultrasound Transducers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce UltraBots, a system that combines ultrasound haptic feedback and robotic actuation for large-area mid-air haptics for VR. Ultrasound haptics can provide precise mid-air haptic feedback and versatile shape rendering, but the interaction area is often limited by the small size of the ultrasound devices, restricting the possible interactions for VR. To address this problem, this paper introduces a novel approach that combines robotic actuation with ultrasound haptics. More specifically, we will attach ultrasound transducer arrays to tabletop mobile robots or robotic arms for scalable, extendable, and translatable interaction areas. We plan to use Sony Toio robots for 2D translation and/or commercially available robotic arms for 3D translation. Using robotic actuation and hand tracking measured by a VR HMD (ex: Oculus Quest), our system can keep the ultrasound transducers underneath the user's hands to provide on-demand haptics. We demonstrate applications with workspace environments, medical training, education and entertainment.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561350"}, {"primary_key": "1769645", "vector": [], "sparse_vector": [], "title": "HapticPuppet: A Kinesthetic Mid-air Multidirectional Force-Feedback Drone-based Interface.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Providing kinesthetic force-feedback for human-scale interactions is challenging due to the relatively large forces needed. Therefore, robotic actuators are predominantly used to deliver this kind of haptic feedback; however, they offer limited flexibility and spatial resolution. In this work, we introduce HapticPuppet, a drone-based force-feedback interface which can exert multidirectional forces onto the human body. This can be achieved by attaching strings to different parts of the human body such as fingers, hands or ankles, which can then be affixed to multiple coordinated drones - puppeteering the user. HapticPuppet opens up a wide range of potential applications in virtual, augmented and mixed reality, exercising, physiotherapy, remote collaboration as well as haptic guidance.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558694"}, {"primary_key": "1769646", "vector": [], "sparse_vector": [], "title": "Towards Semantically Aware Word Cloud Shape Generation.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Word clouds are a data visualization technique that showcases a subset of words from a body of text in a cluster form, where a word's font size encodes some measure of its relative importance—typically frequency—in the text. This technique is primarily used to help viewers glean the most pertinent information from long text documents and to compare and contrast different pieces of text. Despite their popularity, previous research has shown that word cloud designs are often not optimally suited for analytical tasks such as summarization or topic understanding. We propose a solution for generating more effective visualization technique that shapes the word cloud to reflect the key topic(s) of the text. Our method automates the processes of manual image selection and masking required from current word cloud tools to generate shaped word clouds, better allowing for quick summarization. We showcase two approaches using classical and state-of-the-art methods. Upon successfully generating semantically shaped word clouds using both methods, we performed preliminary evaluations with 5 participants. We found that although most participants preferred shaped word clouds over regular ones, the shape can be distracting and detrimental to information extraction if it is not directly relevant to the text or contains graphical imperfections. Our work has implications on future semantically-aware word cloud generation tools as well as efforts to balance visual appeal of word clouds with their effectiveness in textual comprehension.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558724"}, {"primary_key": "1769647", "vector": [], "sparse_vector": [], "title": "MoonBuddy: A Voice-based Augmented Reality User Interface That Supports Astronauts During Extravehicular Activities.", "authors": ["<PERSON>", "<PERSON><PERSON> (Ron) Chew", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Anita) Sun", "<PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON>"], "summary": "As NASA pursues Artemis missions to the moon and beyond, it is essential to equip astronauts with the appropriate human-autonomy enabling technology necessary for the elevated demands of lunar surface exploration and extreme terrestrial access. We present MoonBuddy, an application built for the Microsoft HoloLens 2 that utilizes Augmented Reality (AR) and voice-based interaction to assist astronauts in communication, navigation, and documentation on future lunar extravehicular activities (EVAs), with the goal of reducing cognitive load and increasing task completion. User testing results for MoonBuddy under simulated lunar conditions have been positive overall, with participants indicating that the application was easy to use and helpful in completing the required tasks.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558690"}, {"primary_key": "1769648", "vector": [], "sparse_vector": [], "title": "HapticLever: Kinematic Force Feedback using a 3D Pantograph.", "authors": ["<PERSON>"], "summary": "HapticLever is a new kinematic approach for VR haptics which uses a 3D pantograph to stiffly render large-scale surfaces using small-scale proxies. The HapticLever approach does not consume power to render forces, but rather puts a mechanical constraint on the end effector using a small-scale proxy surface. The HapticLever approach provides stiff force feedback when the user interacts with a static virtual surface, but allows the user to move their arm freely when moving through free virtual space. We present the problem space, the related work, and the HapticLever design approach.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558736"}, {"primary_key": "1769649", "vector": [], "sparse_vector": [], "title": "DATALEV: Acoustophoretic Data Physicalisation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Here, we demonstrate DataLev, a data physicalisation platform with a physical assembly pipeline that allows us to computationally assemble 3D physical charts using acoustically levitated contents. DataLev consists of several enhancement props that allow us to incorporate high-resolution projection, different 3D printed artifacts and multi-modal interaction. DataLev supports reconfigurable and dynamic physicalisations that we animate and illustrate for different chart types. Our work opens up new opportunities for data storytelling using acoustic levitation.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558638"}, {"primary_key": "1769650", "vector": [], "sparse_vector": [], "title": "A Triangular Actuating Device Stand that Dynamically Adjusts Mobile Screen&apos;s Position.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This demo presents a triangular actuating device stand that can automatically adjust the height and tilt angle of a mounted mobile device (e.g., smartphone) to adapt to the user's varying interaction needs (e.g., touch, browsing, and viewing). We employ a unique mechanism to deform the stand's triangular shape with two extendable reel actuators, which enables us to reposition the mobile screen mounted on the hypotenuse side. Each actuator is managed by the mobile device and controls the height and base of the stand's triangular shape, respectively. To demonstrate the potential of our new actuating device stand, we present two types of interaction scenarios: manual device repositioning based on the user's postures or gestures captured by the device's front camera and automatic device repositioning that adapt to the on-screen contents the user will interact with (i.e., touch-based menus, web browsers, illustrator, and video viewers).", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558637"}, {"primary_key": "1769651", "vector": [], "sparse_vector": [], "title": "Augmented Chironomia for Presenting Data to Remote Audiences.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To facilitate engaging and nuanced conversations around data, we contribute a touchless approach to interacting directly with visualization in remote presentations. We combine dynamic charts overlaid on a presenter's webcam feed with continuous bimanual hand tracking, demonstrating interactions that highlight and manipulate chart elements appearing in the foreground. These interactions are simultaneously functional and deictic, and some allow for the addition of \"rhetorical flourish\", or expressive movement used when speaking about quantities, categories, and time intervals. We evaluated our approach in two studies with professionals who routinely deliver and attend presentations about data. The first study considered the presenter perspective, where 12 participants delivered presentations to a remote audience using a presentation environment incorporating our approach. The second study considered the audience experience of 17 participants who attended presentations supported by our environment. Finally, we reflect on observations from these studies and discuss related implications for engaging remote audiences in conversations about data.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545614"}, {"primary_key": "1769652", "vector": [], "sparse_vector": [], "title": "Music Scope Pad: Video Selecting Interface by Natural Movement in VR Space.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper describes a novel video selecting interface that enables us to select videos without having to click a mouse or touch a screen. Existing video players enable us to see and hear only one video at a time, and thus we have to play pieces individually to select the one we want to hear from numerous new videos such as music videos, which involves a large number of mouse and screen-touch operations. The main advantage of our video selecting interface is that it detects natural movements, such as head or hand movements when users are listening to sounds and they can focus on a particular sound source that they want to hear. By moving their head left or right, users can hear the source from a frontal position as the tablet detects changes in the direction they are facing. By putting their hand behind their ear, users can focus on a particular sound source.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558647"}, {"primary_key": "1769653", "vector": [], "sparse_vector": [], "title": "MetamorphX: An Ungrounded 3-DoF Moment Display that Changes its Physical Properties through Rotational Impedance Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Humans can estimate the properties of wielded objects (e.g., inertia and viscosity) using the force applied to the hand. We focused on this mechanism and aimed to represent the properties of wielded objects by dynamically changing the force applied to the hand. We propose MetamorphX, which uses control moment gyroscopes (CMGs) to generate ungrounded, 3-degrees of freedom moment feedback. The high-response moments obtained CMGs allow the inertia and viscosity of motion to be set to the desired values via impedance control. A technical evaluation indicated that our device can generate a moment with a 60-ms delay. The inertia and viscosity of motion were varied by 0.01 kgm2 and 0.1 Ns, respectively. Additionally, we demonstrated that our device can dynamically change the inertia and viscosity of motion through virtual reality applications.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545650"}, {"primary_key": "1769654", "vector": [], "sparse_vector": [], "title": "Kinergy: Creating 3D Printable Motion using Embedded Kinetic Energy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Kinergy—an interactive design tool for creating self-propelled motion by harnessing the energy stored in 3D printable springs. To produce controllable output motions, we introduce 3D printable kinetic units, a set of parameterizable designs that encapsulate 3D printable springs, compliant locks, and transmission mechanisms for three non-periodic motions—instant translation, instant rotation, continuous translation—and four periodic motions—continuous rotation, reciprocation, oscillation, intermittent rotation. Kinergy allows the user to create motion-enabled 3D models by embedding kinetic units, customize output motion characteristics by parameterizing embedded springs and kinematic elements, control energy by operating the specialized lock, and preview the resulting motion in an interactive environment. We demonstrate the potential of our techniques via example applications from spring-loaded cars to kinetic sculptures and close with a discussion of key challenges such as geometric constraints.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545636"}, {"primary_key": "1769655", "vector": [], "sparse_vector": [], "title": "SilentWhisper: faint whisper speech using wearable microphone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice interaction is a fundamental human capacity, and we can use voice user interfaces just speaking. However, in public spaces, we are hesitant to use them because of consideration for their surroundings and low privacy. Silent speech, a method that recognizes the movement of speech in silence, has been proposed as a solution to this problem, and it allows us to maintain our privacy when speaking. However, existing silent speech interfaces are burdensome because the sensor must be kept in contact with the face and mouth, and commands must be prepared for each user. In this study, we propose a method to input whispered speech at a quiet volume that cannot be heard by others using a pin microphone. Experimental results show that a recognition rate was 13.9% WER and 6.4% CER for 210 phrases. We showed that privacy-preserving vocal input is possible by whispering voices which are not comprehensible to others.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558715"}, {"primary_key": "1769656", "vector": [], "sparse_vector": [], "title": "Scholastic: Graphical Human-AI Collaboration for Inductive and Interpretive Text Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Interpretive scholars generate knowledge from text corpora by manually sampling documents, applying codes, and refining and collating codes into categories until meaningful themes emerge. Given a large corpus, machine learning could help scale this data sampling and analysis, but prior research shows that experts are generally concerned about algorithms potentially disrupting or driving interpretive scholarship. We take a human-centered design approach to addressing concerns around machine-assisted interpretive research to build Scholastic, which incorporates a machine-in-the-loop clustering algorithm to scaffold interpretive text analysis. As a scholar applies codes to documents and refines them, the resulting coding schema serves as structured metadata which constrains hierarchical document and word clusters inferred from the corpus. Interactive visualizations of these clusters can help scholars strategically sample documents further toward insights. Scholastic demonstrates how human-centered algorithm design and visualizations employing familiar metaphors can support inductive and interpretive research methodologies through interactive topic modeling and document clustering.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545681"}, {"primary_key": "1769657", "vector": [], "sparse_vector": [], "title": "Using Annotations for Sensemaking About Code.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Developers spend significant amounts of time finding, relating, navigating, and, more broadly, making sense of code. While sensemaking, developers must keep track of many pieces of information including the objectives of their task, the code locations of interest, their questions and hypotheses about the behavior of the code, and more. Despite this process being such an integral aspect of software development, there is little tooling support for externalizing and keeping track of developers' information, which led us to develop Catseye – an annotation tool for lightweight notetaking about code. Catseye has advantages over traditional methods of externalizing code-related information, such as commenting, in that the annotations retain the original context of the code while not actually modifying the underlying source code, they can support richer interactions such as lightweight versioning, and they can be used as navigational aids. In our investigation of developers' notetaking processes using Catseye, we found developers were able to successfully use annotations to support their code sensemaking when completing a debugging task.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545667"}, {"primary_key": "1769658", "vector": [], "sparse_vector": [], "title": "Scrapbook: Screenshot-Based Bookmarks for Effective Digital Resource Curation across Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Modern knowledge workers typically need to use multiple resources, such as documents, web pages, and applications, at the same time. This complexity in their computing environments forces workers to restore various resources in the course of their work. However, conventional curation methods like bookmarks, recent document histories, and file systems place limitations on effective retrieval. Such features typically work only for resources of one type within one application, ignoring the interdependency between resources needed for a single task. In addition, text-based handles do not provide rich cues for users to recognize their associated resources. Hence, the need to locate and reopen relevant resources can significantly hinder knowledge workers' productivity. To address these issues, we designed and developed Scrapbook, a novel application for digital resource curation across applications that uses screenshot-based bookmarks. Scrapbook extracts and stores all the metadata (URL, file location, and application name) of windows visible in a captured screenshot to facilitate restoring them later. A week-long field study indicated that screenshot-based bookmarks helped participants curate digital resources. Additionally, participants reported that multimodal—visual and textual—data helped them recall past computer activities and reconstruct working contexts efficiently.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545678"}, {"primary_key": "1769659", "vector": [], "sparse_vector": [], "title": "Touchibo: Multimodal Texture-Changing Robotic Platform for Shared Human Experiences.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Ana Pa<PERSON>", "<PERSON>"], "summary": "Touchibo is a modular robotic platform for enriching interpersonal communication in human-robot group activities, suitable for children with mixed visual abilities. Touchibo incorporates several modalities, including dynamic textures, scent, audio, and light. Two prototypes are demonstrated for supporting storytelling activities and mediating group conversations between children with and without visual impairment. Our goal is to provide an inclusive platform for children to interact with each other, perceive their emotions, and become more aware of how they impact others.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558643"}, {"primary_key": "1769660", "vector": [], "sparse_vector": [], "title": "Puppeteer: Manipulating Human Avatar Actions with Intuitive Hand Gestures and Upper-Body Postures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Puppeteer, an input prototype system that allows players directly control their avatars through intuitive hand gestures and upper-body postures. We selected 17 avatar actions discovered in the pilot study and conducted a gesture elicitation study to invite 12 participants to design best representing hand gestures and upper-body postures for each action. Then we implemented a prototype system using the MediaPipe framework to detect keypoints and a self-trained model to recognize 17 hand gestures and 17 upper-body postures. Finally, three applications demonstrate the interactions enabled by <PERSON><PERSON>pet<PERSON>.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558689"}, {"primary_key": "1769661", "vector": [], "sparse_vector": [], "title": "Breathing Life Into Biomechanical User Models.", "authors": ["Aleksi <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Forward biomechanical simulation in HCI holds great promise as a tool for evaluation, design, and engineering of user interfaces. Although reinforcement learning (RL) has been used to simulate biomechanics in interaction, prior work has relied on unrealistic assumptions about the control problem involved, which limits the plausibility of emerging policies. These assumptions include direct torque actuation as opposed to muscle-based control; direct, privileged access to the external environment, instead of imperfect sensory observations; and lack of interaction with physical input devices. In this paper, we present a new approach for learning muscle-actuated control policies based on perceptual feedback in interaction tasks with physical input devices. This allows modelling of more realistic interaction tasks with cognitively plausible visuomotor control. We show that our simulated user model successfully learns a variety of tasks representing different interaction methods, and that the model exhibits characteristic movement regularities observed in studies of pointing. We provide an open-source implementation which can be extended with further biomechanical models, perception models, and interactive environments.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545689"}, {"primary_key": "1769662", "vector": [], "sparse_vector": [], "title": "Early Usability Evaluation of a Relational Agent for the COVID-19 Pandemic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Relational agents (RAs) have shown effectiveness in various health interventions with and without healthcare professionals (HCPs) and hospital facilities. RAs have not been widely researched in COVID-19 context, although they can give health interventions during the pandemic. Addressing this gap, this work presents an early usability evaluation of a prototypical RA, which is iteratively designed and developed in collaboration with infected patients (n=21) and two groups of HCPs (n=19, n=16) to aid COVID-19 patients at various stages about four main tasks: testing guidance, support during self-isolation, handling emergency situations, and promoting post-infection mental well-being. The prototype obtained an average score of 58.82 on the system usability scale (SUS) after being evaluated by 98 people. This result implies that the suggested design still needs to be improved for greater usability and adoption.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558702"}, {"primary_key": "1769663", "vector": [], "sparse_vector": [], "title": "TipTrap: A Co-located Direct Manipulation Technique for Acoustically Levitated Content.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Acoustic levitation has emerged as a promising approach for mid-air displays, by using multiple levitated particles as 3D voxels, cloth and thread props, or high-speed tracer particles, under the promise of creating 3D displays that users can see, hear and feel with their bare eyes, ears and hands. However, interaction with this mid-air content always occurred at a distance, since external objects in the display volume (e.g. user's hands) can disturb the acoustic fields and make the particles fall. This paper proposes TipTrap, a co-located direct manipulation technique for acoustically levitated particles. TipTrap leverages the reflection of ultrasound on the users' skin and employs a closed-loop system to create functional acoustic traps 2.1 mm below the fingertips, and addresses its 3 basic stages: selection, manipulation and deselection. We use Finite-Differences Time Domain (FDTD) simulations to explain the principles enabling TipTrap, and explore how finger reflections and user strategies influence the quality of the traps (e.g. approaching direction, orientation and tracking errors), and use these results to design our technique. We then implement the technique, characterizing its performance with a robotic hand setup and finish with an exploration of the ability of TipTrap to manipulate different types of levitated content.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545675"}, {"primary_key": "1769664", "vector": [], "sparse_vector": [], "title": "Exploring the Learnability of Program Synthesizers by Novice Programmers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern program synthesizers are increasingly delivering on their promise of lightening the burden of programming by automatically generating code, but little research has addressed how we can make such systems learnable to all. In this work, we ask: What aspects of program synthesizers contribute to and detract from their learnability by novice programmers? We conducted a thematic analysis of 22 observations of novice programmers, during which novices worked with existing program synthesizers, then participated in semi-structured interviews. Our findings shed light on how their specific points in the synthesizer design space affect these tools' learnability by novice programmers, including the type of specification the synthesizer requires, the method of invoking synthesis and receiving feedback, and the size of the specification. We also describe common misconceptions about what constitutes meaningful progress and useful specifications for the synthesizers, as well as participants' common behaviors and strategies for using these tools. From this analysis, we offer a set of design opportunities to inform the design of future program synthesizers that strive to be learnable by novice programmers. This work serves as a first step toward understanding how we can make program synthesizers more learnable by novices, which opens up the possibility of using program synthesizers in educational settings as well as developer tooling oriented toward novice programmers.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545659"}, {"primary_key": "1769665", "vector": [], "sparse_vector": [], "title": "PerSign: Personalized Bangladeshi Sign Letters Synthesis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bangladeshi Sign Language (BdSL)—like other sign languages— is tough to learn for general people, especially when it comes to expressing letters. In this poster, we propose PerSign, a system that can reproduce a person's image by introducing sign gestures in it. We make this operation \"personalized\", which means the generated image keeps the person's initial image profile–face, skin tone, attire, background—unchanged while altering the hand, palm, and finger positions appropriately. We use an image-to-image translation technique and build a corresponding unique dataset to accomplish the task. We believe the translated image can reduce the communication gap between signers1 and non-signers without having prior knowledge of BdSL.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558712"}, {"primary_key": "1769666", "vector": [], "sparse_vector": [], "title": "DIY Graphics Tab: A Cost-Effective Alternative to Graphics Tablet for Educators.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recording lectures is a normal task for online educators, and a graphics tablet is a great tool for that. However, it is very expensive for many instructors. In this paper, we propose an alternative called \"DIY Graphics Tab\" that functions largely in the same way as a graphic tab, but requires only a pen, paper, and laptop's webcam. Our system takes images of writings on a paper with a webcam and outputs the contents. The task is not straightforward since there are obstacles, such as hand occlusion, paper movements, lighting, and perspective distortion due to the viewing angle. A pipeline is used that applies segmentation and post-processing for generating appropriate output from input frames. We also conducted user experience evaluations from the teachers.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558718"}, {"primary_key": "1769667", "vector": [], "sparse_vector": [], "title": "Empowering domain experts to author valid statistical analyses.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Reliable statistical analyses are critical for making scientific discoveries, guiding policy, and informing decisions. To author reliable statistical analyses, integrating knowledge about the domain, data, statistics, and programming is necessary. However, this is an unrealistic expectation for many analysts who may possess domain expertise but lack statistical or programming expertise, including many researchers, policy makers, and other data scientists. How can our statistical software help these analysts? To address this need, we first probed into the cognitive and operational processes involved in authoring statistical analyses and developed the theory of hypothesis formalization. Authoring statistical analyses is a dual-search process that requires grappling with assumptions about conceptual relationships and iterating on statistical model implementations. This led to our key insight: statistical software needs to help analysts translate what they know about their domain and data into statistical modeling programs. To do so, statistical software must provide programming interfaces and interaction models that allow statistical non-experts to express their analysis goals accurately and reflect on their domain knowledge and data. Thus far, we have developed two such systems that embody this insight: Tea and Tisane. Ongoing work on rTisane explores new semantics for more accurately eliciting analysis intent and conceptual knowledge. Additionally, we are planning a summative evaluation of rTisane to assess our hypothesis that this new way of authoring statistical analyses makes domain experts more aware of their implicit assumptions, able to author and understand nuanced statistical models that answer their research questions, and avoid previous analysis mistakes.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558530"}, {"primary_key": "1769668", "vector": [], "sparse_vector": [], "title": "Sketched Reality: Sketching Bi-Directional Interactions Between Virtual and Physical Worlds with AR and Actuated Tangible UI.", "authors": ["<PERSON><PERSON><PERSON>", "Kyzyl Monteiro", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>g Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces Sketched Reality, an approach that combines AR sketching and actuated tangible user interfaces (TUI) for bi-directional sketching interaction. Bi-directional sketching enables virtual sketches and physical objects to \"affect\" each other through physical actuation and digital computation. In the existing AR sketching, the relationship between virtual and physical worlds is only one-directional — while physical interaction can affect virtual sketches, virtual sketches have no return effect on the physical objects or environment. In contrast, bi-directional sketching interaction allows the seamless coupling between sketches and actuated TUIs. In this paper, we employ tabletop-size small robots (Sony Toio) and an iPad-based AR sketching tool to demonstrate the concept. In our system, virtual sketches drawn and simulated on an iPad (e.g., lines, walls, pendulums, and springs) can move, actuate, collide, and constrain physical Toio robots, as if virtual sketches and the physical objects exist in the same space through seamless coupling between AR and robot motion. This paper contributes a set of novel interactions and a design space of bi-directional AR sketching. We demonstrate a series of potential applications, such as tangible physics education, explorable mechanism, tangible gaming for children, and in-situ robot programming via sketching.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545626"}, {"primary_key": "1769669", "vector": [], "sparse_vector": [], "title": "Using a Dual-Camera Smartphone to Recognize Imperceptible 2D Barcodes Embedded in Videos.", "authors": ["<PERSON><PERSON>", "Kota Araki", "<PERSON><PERSON>", "S<PERSON>o Fu<PERSON>", "<PERSON><PERSON>"], "summary": "Invisible screen-camera communication is promising in that it does not interfere with the video viewing experience. In the imperceptible color vibration method, which displays two colors of the same luminance alternately at high speed for each pixel, embedded information is decoded by taking the difference between distant frames on the time axis. Therefore, the interframe differences of the original video contents affect the decoding performance. In this study, we propose a decoding method which utilizes simultaneously captured images using a dual-camera smartphone with different exposure times. This allows taking the color difference between the frames that are close to each other on the time axis. The feasibility of this approach is demonstrated through several application examples.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558672"}, {"primary_key": "1769670", "vector": [], "sparse_vector": [], "title": "DAWBalloon: An Intuitive Musical Interface Using Floating Balloons.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Kodai Ito", "<PERSON><PERSON>"], "summary": "The development of music synthesis technology has created a way to enjoy listening to music and actively manipulate it. However, it is difficult for an amateur to combine sounds or change pitches to operate a DAW (Digital Audio Workstation). Therefore, we focused on ultrasonic levitation and haptic feedback to develop an appropriate interface for DAW. We propose \"DAWBalloon\", a system that uses ultrasonic levitation arrays to visualize rhythms using floating balloons as a metaphor for musical elements and to combine sounds by manipulating the balloons. DAWBalloon realizes the intuitive manipulation of sounds in three dimensions, even for people without knowledge of music.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561354"}, {"primary_key": "1769671", "vector": [], "sparse_vector": [], "title": "Threddy: An Interactive System for Personalized Thread-based Exploration and Organization of Scientific Literature.", "authors": ["Hyeonsu B. Kang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reviewing the literature to understand relevant threads of past work is a critical part of research and vehicle for learning. However, as the scientific literature grows the challenges for users to find and make sense of the many different threads of research grow as well. Previous work has helped scholars to find and group papers with citation information or textual similarity using standalone tools or overview visualizations. Instead, in this work we explore a tool integrated into users' reading process that helps them with leveraging authors' existing summarization of threads, typically in introduction or related work sections, in order to situate their own work's contributions. To explore this we developed a prototype that supports efficient extraction and organization of threads along with supporting evidence as scientists read research articles. The system then recommends further relevant articles based on user-created threads. We evaluate the system in a lab study and find that it helps scientists to follow and curate research threads without breaking out of their flow of reading, collect relevant papers and clips, and discover interesting new articles to further grow threads.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545660"}, {"primary_key": "1769672", "vector": [], "sparse_vector": [], "title": "Tie Memories to E-souvenirs: Hybrid Tangible AR Souvenirs in the Museum.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traditional physical souvenirs in museums have three major limits: monotonous interaction, lack of personalization, and disconnection to the exhibition. To conquer these problems and to make personalized souvenirs a part of the visiting experience, we create a hybrid tangible Augmented Reality(AR) souvenir that combines a physical firework launcher and AR models. An application called AR Firework is designed for customizing the hybrid souvenir as well as interactive learning in an exhibition in the wild. Multiple interaction methods including mobile user interface, hand gestures, and voice are adopted to create a multi-sensory product. As the first research to propose tangible AR souvenirs, we find that they establish a long-lasting connection between visitors and their personal visiting experiences. This paper promotes the understanding of personalization, socialization and tangible AR.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558722"}, {"primary_key": "1769673", "vector": [], "sparse_vector": [], "title": "FeedLens: Polymorphic Lenses for Personalizing Exploratory Search over Knowledge Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The vast scale and open-ended nature of knowledge graphs (KGs) make exploratory search over them cognitively demanding for users. We introduce a new technique, polymorphic lenses, that improves exploratory search over a KG by obtaining new leverage from the existing preference models that KG-based systems maintain for recommending content. The approach is based on a simple but powerful observation: in a KG, preference models can be re-targeted to recommend not only entities of a single base entity type (e.g., papers in the scientific literature KG, products in an e-commerce KG), but also all other types (e.g., authors, conferences, institutions; sellers, buyers). We implement our technique in a novel system, FeedLens, which is built over Semantic Scholar, a production system for navigating the scientific literature KG. FeedLens reuses the existing preference models on Semantic Scholar—people's curated research feeds—as lenses for exploratory search. Semantic Scholar users can curate multiple feeds/lenses for different topics of interest, e.g., one for human-centered AI and another for document embeddings. Although these lenses are defined in terms of papers, FeedLens re-purposes them to also guide search over authors, institutions, venues, etc. Our system design is based on feedback from intended users via two pilot surveys (n = 17 and n = 13, respectively). We compare FeedLens and Semantic Scholar via a third (within-subjects) user study (n = 15) and find that FeedLens increases user engagement while reducing the cognitive effort required to complete a short literature review task. Our qualitative results also highlight people's preference for this more effective exploratory search experience enabled by FeedLens.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545631"}, {"primary_key": "1769674", "vector": [], "sparse_vector": [], "title": "Magic Drops: Food 3D Printing of Colored Liquid Balls by Ultrasound Levitation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the concept of \"Magic Drops\", the process which is all using ultrasound levitation to mix multiple liquid drops into a single one in the air and move it specified position and let it free fall below. For molecular gastronomy application, mixture drops with sodium alginate solution are free-fall into a container filled with calcium lactate solution. With this, drops encased in a calcium alginate film are formed in the container, these are edible and the color and flavor of mixture are also controlled through the process. We will also demonstrate stack these drops to create larger edible structure. Our novel mixture drop control technology has other potential applications such as painting techniques and drug development. Thus, we believe that this concept will become a new technology for mixing liquids in the future.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561348"}, {"primary_key": "1769675", "vector": [], "sparse_vector": [], "title": "AIx speed: Playback Speed Optimization using Listening Comprehension of Speech Recognition Models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, more and more time has been spent watching videos for online seminars, lectures, and entertainment. In order to improve time efficiency, people often adjust the playback speed to a speed that suits them best. However, it is troublesome to adjust the optimal speed for each video and even more challenging to change and adjust the speed for each speaker within a single video. Therefore, we propose \"AIx speed,\" a system that maximizes the playback speed within the range where the speech recognition model can recognize and flexibly adjusts the playback speed for the entire video. This system makes it possible to set a flexible playback speed that balances playback time and content comprehension, compared to fixing the playback speed for the entire video.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558727"}, {"primary_key": "1769676", "vector": [], "sparse_vector": [], "title": "EtherPose: Continuous Hand Pose Tracking with <PERSON><PERSON><PERSON><PERSON><PERSON> Antenna Impedance Characteristic Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "EtherPose is a continuous hand pose tracking system employing two wrist-worn antennas, from which we measure the real-time dielectric loading resulting from different hand geometries (i.e., poses). Unlike worn camera-based methods, our RF approach is more robust to occlusion from clothing and avoids capturing potentially sensitive imagery. Through a series of simulations and empirical studies, we designed a proof-of-concept, worn implementation built around compact vector network analyzers. Sensor data is then interpreted by a machine learning backend, which outputs a fully-posed 3D hand. In a user study, we show how our system can track hand pose with a mean Euclidean joint error of 11.6 mm, even when covered in fabric. We also studied 2DOF wrist angle and micro-gesture tracking. In the future, our approach could be miniaturized and extended to include more and different types of antennas, operating at different self resonances.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545665"}, {"primary_key": "1769677", "vector": [], "sparse_vector": [], "title": "Top-Levi: Multi-User Interactive System Using Acoustic Levitation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Top-Levi is a public multi-user interactive system that requires a pair of users to cooperate with an audience around them. Based on acoustic levitation technology, Top-Levi leverages a unique attribute of dynamic physical 3D contents displayed and animated in the air: such systems inherently provide different visual information to users depending on where they are around the device. In Top-Levi, there are two primary users on opposite (left/right) sides of the device, and audience members to the front. Each sees different instructions displayed on a floating cube. Their collective task is to cooperate to move the cube from a start point to a final destination by synchronizing their responses to the instructions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561347"}, {"primary_key": "1769678", "vector": [], "sparse_vector": [], "title": "RCSketch: Sketch, Build, and Control Your Dream Vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present RCSketch, a system that lets children sketch their dream vehicles in 3D, build moving structures of those vehicles, and control them from multiple viewpoints. As a proof of concept, we implemented our system and designed five vehicles that could perform a wide variety of realistic movements.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558688"}, {"primary_key": "1769679", "vector": [], "sparse_vector": [], "title": "Self-Supervised Approach for Few-shot Hand Gesture Recognition.", "authors": ["<PERSON><PERSON>"], "summary": "Data-driven machine learning approaches have become increasingly used in human-computer interaction (HCI) tasks. However, compared with traditional machine learning tasks, for which large datasets are available and maintained, each HCI project needs to collect new datasets because HCI systems usually propose new sensing or use cases. Such datasets tend to be lacking in amount and lead to low performance or place a burden on participants in user studies. In this paper, taking hand gesture recognition using wrist-worn devices as a typical HCI task, I propose a self-supervised approach that achieves high performance with little burden on the user. The experimental results showed that hand gesture recognition was achieved with a very small number of labeled training samples (five samples with 95% accuracy for 5 gestures and 10 samples with 95% accuracy for 10 gestures). The results support the story that when the user wants to design 5 new gestures, he/she can activate the feature in less than 2 minutes. I discuss the potential of this self-supervised framework for the HCI community.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558707"}, {"primary_key": "1769680", "vector": [], "sparse_vector": [], "title": "We-toon: A Communication Support System between Writers and Artists in Collaborative Webtoon Sketch Revision.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gwanmo Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Jinwook Seo"], "summary": "We present a communication support system, namely We-toon, that can bridge the webtoon writers and artists during sketch revision (i.e., character design and draft revision). In the highly iterative design process between the webtoon writers and artists, writers often have difficulties in precisely articulating their feedback on sketches owing to their lack of drawing proficiency. This drawback makes the writers rely on textual descriptions and reference images found using search engines, leading to indirect and inefficient communications. Inspired by a formative study, we designed We-toon to help writers revise webtoon sketches and effectively communicate with artists. Through a GAN-based image synthesis and manipulation, We-toon can interactively generate diverse reference images and synthesize them locally on any user-provided image. Our user study with 24 professional webtoon authors demonstrated that We-toon outperforms the traditional methods in terms of communication effectiveness and the writers' satisfaction level related to the revised image.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545612"}, {"primary_key": "1769681", "vector": [], "sparse_vector": [], "title": "Thermoformable Shell for Repeatable Thermoforming.", "authors": ["<PERSON><PERSON><PERSON> Ko", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a thermoformable shell called TF-Shell that allows repeatable thermoforming. Due to the low thermal conductivity of typical printing materials like polylactic acid (PLA), thermoforming 3D printed objects is largely limited. Through embedding TF-Shell, users can thermoform target parts in diverse ways. Moreover, the deformed structures can be restored by reheating. In this demo, we introduce the TF-Shell and demonstrate four thermoforming behaviors with the TF-Shell embedded figure. With our approach, we envision bringing the value of hands-on craft to digital fabrication.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558632"}, {"primary_key": "1769682", "vector": [], "sparse_vector": [], "title": "Prototyping Soft Devices with Interactive Bioplastics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designers and makers are increasingly interested in leveraging bio-based and bio-degradable 'do-it-yourself' (DIY) materials for sustainable prototyping. Their self-produced bioplastics possess compelling properties such as self-adhesion but have so far not been functionalized to create soft interactive devices, due to a lack of DIY techniques for the fabrication of functional electronic circuits and sensors. In this paper, we contribute a DIY approach for creating Interactive Bioplastics that is accessible to a wide audience, making use of easy-to-obtain bio-based raw materials and familiar tools. We present three types of conductive bioplastic materials and their formulation: sheets, pastes and foams. Our materials enable additive and subtractive fabrication of soft circuits and sensors. Furthermore, we demonstrate how these materials can substitute conventional prototyping materials, be combined with off-the-shelf electronics, and be fed into a sustainable material 'life-cycle' including disassembly, re-use, and re-melting of materials. A formal characterization of our conductors highlights that they are even on-par with commercially available carbon-based conductive pastes.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545623"}, {"primary_key": "1769683", "vector": [], "sparse_vector": [], "title": "Rapid Prototyping Dynamic Robotic Fibers for Tunable Movement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Liquid crystal elastomers (LCEs) are promising shape-changing actuators for soft robotics in human–computer interaction (HCI). Current LCE manufacturing processes, such as fiber-drawing, extrusion, and 3D printing, face limitations on form-giving and accessibility. We introduce a novel rapid-prototyping approach for thermo-responsive LCE fiber actuators based on vacuum molding extrusion. Our contributions are threefold, a) a vacuum fiber molding (VFM) machine, b) LCE actuators with customizable fiber shapes c) open-source hackability of the machine. We build and test the VFM machine to generate shape-changing movements from four fiber actuators (pincer, curl, ribbon, and hook), and we look at how these new morphologies bridge towards soft robotic device integration.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558696"}, {"primary_key": "1769684", "vector": [], "sparse_vector": [], "title": "What&apos;s Cooking? Olfactory Sensing Using Off-the-Shelf Components.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present Project Sniff, a hardware and software exploration into olfactory sensing with an application in digital communication and social presence. Our initial results indicate that a simple hardware design using off-the-shelf sensors and the application of supervised learning to the sensor data allows us to detect several common household scents reliably. As part of this exploration, we developed a scent-sensing IoT prototype and placed it in the kitchen area to sense \"what's cooking?\", and share the olfactory information via a Slack bot. We conclude by outlining our plans for future steps and potential applications of this research.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558687"}, {"primary_key": "1769685", "vector": [], "sparse_vector": [], "title": "Personalized Game Difficulty Prediction Using Factorization Machines.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The accurate and personalized estimation of task difficulty provides many opportunities for optimizing user experience. However, user diversity makes such difficulty estimation hard, in that empirical measurements from some user sample do not necessarily generalize to others. In this paper, we contribute a new approach for personalized difficulty estimation of game levels, borrowing methods from content recommendation. Using factorization machines (FM) on a large dataset from a commercial puzzle game, we are able to predict difficulty as the number of attempts a player requires to pass future game levels, based on observed attempt counts from earlier levels and levels played by others. In addition to performance and scalability, FMs offer the benefit that the learned latent variable model can be used to study the characteristics of both players and game levels that contribute to difficulty. We compare the approach to a simple non-personalized baseline and a personalized prediction using Random Forests. Our results suggest that FMs are a promising tool enabling game designers to both optimize player experience and learn more about their players and the game.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545624"}, {"primary_key": "1769686", "vector": [], "sparse_vector": [], "title": "Calligraphy Z: A Fabricatable Pen Plotter for Handwritten Strokes with Z-Axis Pen Pressure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the current age, the use of desktop publishing software and printing presses makes it possible to produce various expressions. On the other hand, it is difficult to perfectly replicate the ink grazing and subtle pressure fluctuations that occur when using a writing implement to output characters on a printer. In this study, we reproduce such incidental brushstrokes by using a writing implement to output text layout created on software. To replicate slight variations in strokes, we developed Calligraphy Z, a system that consists of a writing device and an application. The writing device controls the vertical position of the writing tool in addition to the writing position, thus producing handwritten-like character output, and an application that generates G-code for the device operation from user input. With the application, users can select their favorite fonts, input words, and adjust the layout to operate the writing device using several types of extended font data with writing pressure data prepared in advance. After developing our system, we compared the strokes of several writing implements to select the most suitable one for Calligraphy Z. We also conducted evaluations of the identification of characters output by Calligraphy Z and those output by a printing machine. We found participants in the evaluation experiment perceive the features of handwritten characters, such as ink blotting and fine blurring of strokes, in the characters by our system.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558657"}, {"primary_key": "1769687", "vector": [], "sparse_vector": [], "title": "Interactive 3D Zoetrope with a Strobing Flashlight.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a 3D printed zoetrope mounted on a bike wheel where users can watch the 3D figures come to life in front of their eyes. Each frame of our animation is a 9 by 16 cm 3D fabricated diorama containing a small scene. A strobed flashlight synced with the spinning of the wheel shows the viewer each frame at just the right time, creating the illusion of 3D motion. The viewer can hold and shine the flashlight into the scene, illuminating each frame from their own point of view. Our zoetrope is modular and can have different 16 frame animations substituted in and out for fast prototyping of many cinematography, fabrication, and strobe lighting techniques. Our interactive truly 3D movie experience will push the zoetrope format to tell more complex stories and better engage viewers.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558663"}, {"primary_key": "1769688", "vector": [], "sparse_vector": [], "title": "Fuse: In-Situ Sensemaking Support in the Browser.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "People spend a significant amount of time trying to make sense of the internet, collecting content from a variety of sources and organizing it to make decisions and achieve their goals. While humans are able to fluidly iterate on collecting and organizing information in their minds, existing tools and approaches introduce significant friction into the process. We introduce Fuse, a browser extension that externalizes users' working memory by combining low-cost collection with lightweight organization of content in a compact card-based sidebar that is always available. Fuse helps users simultaneously extract key web content and structure it in a lightweight and visual way. We discuss how these affordances help users externalize more of their mental model into the system (e.g., saving, annotating, and structuring items) and support fast reviewing and resumption of task contexts. Our 22-month public deployment and follow-up interviews provide longitudinal insights into the structuring behaviors of real-world users conducting information foraging tasks.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545693"}, {"primary_key": "1769689", "vector": [], "sparse_vector": [], "title": "Gustav: Cross-device Cross-computer Synchronization of Sensory Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Temporal synchronization of behavioral and physiological signals collected through different devices (and sometimes through different computers) is a longstanding challenge in HCI, neuroscience, psychology, and related areas. Previous research has proposed to synchronize sensory signals using (1) dedicated hardware; (2) dedicated software; or (3) alignment algorithms. All these approaches are either vendor-locked, non-generalizable, or difficult to adopt in practice. We propose a simple but highly efficient alternative: instrument the stimulus presentation software by injecting supervisory event-related timestamps, followed by a post-processing step over the recorded log files. Armed with this information, we introduce <PERSON>, our approach to orchestrate the recording of sensory signals across devices and computers. <PERSON> ensures that all signals coincide exactly with the duration of each experiment condition, with millisecond precision. <PERSON> is publicly available as open source software.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558723"}, {"primary_key": "1769690", "vector": [], "sparse_vector": [], "title": "Sketch-Based Design of Foundation Paper Pieceable Quilts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Foundation paper piecing is a widely used quilt-making technique in which fabric pieces are sewn onto a paper guide to facilitate construction. But, designing paper pieceable quilt patterns is challenging because the sewing process imposes constraints on both the geometry and sewing order of the fabric pieces. Based on a formative study with expert quilt designers, we develop a novel sketch-based tool for designing such quilt patterns. Our tool lets designers sketch a partial design as a set of edges, which may intersect but do not have to form closed polygons, and our tool automatically completes it into a fully paper pieceable pattern. We contribute a new sketch-completion algorithm that extends the input sketched edges into a planar mesh composed of closed polygonal faces representing fabric pieces, determines a paper pieceable sewing order for the faces, and breaks complicated sketches into independently paper pieceable sections when necessary. A partial input design often admits multiple visually different completions. Thus, our tool lets designers specify completion heuristics, which are based on current quilt design practices, to control the appearance of the completed quilt. Initial user evaluations with novice and expert quilt designers suggest that our tool fits within current design workflows and greatly facilitates designing foundation paper pieceable quilts by allowing users to focus on the visual design rather than tedious constraint checks.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545643"}, {"primary_key": "1769691", "vector": [], "sparse_vector": [], "title": "WireSketch: Bimanual Interactions for 3D Curve Networks in VR.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Taegyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "3D content authoring in immersive environments has the advantage of allowing users to see a design result on its actual scale in real time. We present a system to intuitively create and modify 3D curve networks using bimanual gestures in virtual reality (VR). Our system provides a rich vocabulary of interactions in which both hands are used harmoniously following simple and intuitive grammar, and supports comprehensive manipulation of 3D curve networks.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558726"}, {"primary_key": "1769692", "vector": [], "sparse_vector": [], "title": "SleepGuru: Personalized Sleep Planning System for Real-life Actionability and Negotiability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Widely-accepted sleep guidelines advise regular bedtimes and sleep hygiene. An individual's adherence is often viewed as a matter of self-regulation and anti-procrastination. We pose a question from a different perspective: What if it comes to a matter of one's social or professional duty that mandates irregular daily life, making it incompatible with the premise of standard guidelines? We propose SleepGuru, an individually actionable sleep planning system featuring one's real-life compatibility and extended forecast. Adopting theories on sleep physiology, <PERSON>G<PERSON> builds a personalized predictor on the progression of the user's sleep pressure over a course of upcoming schedules and past activities sourced from her online calendar and wearable fitness tracker. Then, SleepGuru service provides individually actionable multi-day sleep schedules which respect the user's inevitable real-life irregularities while regulating her week-long sleep pressure. We elaborate on the underlying physiological principles and mathematical models, followed by a 3-stage study and deployment. We develop a mobile user interface providing individual predictions and adjustability backed by cloud-side optimization. We deploy SleepGuru in-the-wild to 20 users for 8 weeks, where we found positive effects of SleepGuru in sleep quality, compliance rate, sleep efficiency, alertness, long-term followability, and so on.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545709"}, {"primary_key": "1769693", "vector": [], "sparse_vector": [], "title": "LV-Linker: Supporting Fine-grained User Interaction Analyses by Linking Smartphone Log and Recorded Video Data.", "authors": ["<PERSON><PERSON>", "Sang<PERSON><PERSON> Lee", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data-driven mobile design as an important UI/UX research technique often requires analyzing recorded screen video data and time-series usage log data, because it helps to obtain a deeper understanding of fine-grained usage behaviors. However, there is a lack of interactive tools that support simultaneously navigation of both mobile usage log and video data. In this paper, we propose LV-Linker (Log and Video Linker), a web-based data viewer system for synchronizing both smartphone usage log and video data to help researchers quickly to analyze and easily understand user behaviors. We conducted a preliminary user study and evaluated the benefits of linking both data by measuring task completion time, helpfulness, and subjective task workload. Our results showed that offering linked navigation significantly lowers the task completion time and task workload, and promotes data understanding and analysis fidelity.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558714"}, {"primary_key": "1769694", "vector": [], "sparse_vector": [], "title": "NFCStack: Identifiable Physical Building Blocks that Support Concurrent Construction and Frictionless Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose NFCStack, which is a physical building block system that supports stacking and frictionless interaction and is based on near-field communication (NFC). This system consists of a portable station that can support and resolve the order of three types of passive identifiable stackable: bricks, boxes, and adapters. The bricks support stable and sturdy physical construction, whereas the boxes support frictionless tangible interactions. The adapters provide an interface between the aforementioned two types of stackable and convert the top of a stack into a terminal for detecting interactions between NFC-tagged objects. In contrast to existing systems based on NFC or radio-frequency identification technologies, NFCStack is portable, supports simultaneous interactions, and resolves stacking and interaction events responsively, even when objects are not strictly aligned. Evaluation results indicate that the proposed system effectively supports 12 layers of rich-ID stacking with the three types of building block, even if every box is stacked with a 6-mm offset. The results also indicate possible generalized applications of the proposed system, including 2.5-dimensional construction. The interaction styles are described using several educational application examples, and the design implications of this research are explained.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545658"}, {"primary_key": "1769695", "vector": [], "sparse_vector": [], "title": "RemoteLab: A VR Remote Study Toolkit.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "User studies play a critical role in human subject research, including human-computer interaction. Virtual reality (VR) researchers tend to conduct user studies in-person at their laboratory, where participants experiment with novel equipment to complete tasks in a simulated environment, which is often new to many. However, due to social distancing requirements in recent years, VR research has been disrupted by preventing participants from attending in-person laboratory studies. On the other hand, affordable head-mounted displays are becoming common, enabling access to VR experiences and interactions outside traditional research settings. Recent research has shown that unsupervised remote user studies can yield reliable results, however, the setup of experiment software designed for remote studies can be technically complex and convoluted. We present a novel open-source Unity toolkit, RemoteLab, designed to facilitate the preparation of remote experiments by providing a set of tools that synchronize experiment state across multiple computers, record and collect data from various multimedia sources, and replay the accumulated data for analysis. This toolkit facilitates VR researchers to conduct remote experiments when in-person experiments are not feasible or increase the sampling variety of a target population and reach participants that otherwise would not be able to attend in-person.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545679"}, {"primary_key": "1769696", "vector": [], "sparse_vector": [], "title": "Wemoji: Towards Designing Complementary Communication Systems in Augmented Reality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Augmented Reality (AR) can enable new forms of self-expression and communication. However, little is known about how AR experiences should be designed to complement face-to-face communication. We present an initial set of insights derived from the iterative design of a mobile AR app called Wemoji. It enables people to emote or react to one another by spawning visual AR effects in a shared physical space. As an additional communication medium, it can help add volume and dimension to what is exchanged. We outline a design space for AR complementary communication systems, and offer a set of insights from initial testing that points towards how AR can be used to enhance same-time same-place social interactions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558699"}, {"primary_key": "1769697", "vector": [], "sparse_vector": [], "title": "Extending Computational Abstractions with Manual Craft for Visual Art Tools.", "authors": ["<PERSON><PERSON>"], "summary": "Programming and computation are powerful tools for manipulating visual forms, but working with these abstractions is challenging for artists who are accustomed to direct manipulation and manual control. The goal of my research is to develop visual art tools that extend programmatic capabilities with manual craft. I do so by exposing computational abstractions as transparent materials that artists may directly manipulate and observe in a process that accommodates their non-linear workflows. Specifically, I conduct empirical research to identify challenges professional artists face when using existing software tools—as well as programming their own—to make art. I apply principles derived from these findings in two projects: an interactive programming environment that links code, numerical information, and program state to artists' ongoing artworks, and an algorithm that automates the rigging of character clothing to bodies to allow for more flexible and customizable 2D character illustrations. Evaluating these interactions, my research promotes authoring tools that support arbitrary execution by adapting to the existing workflows of artists.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558527"}, {"primary_key": "1769698", "vector": [], "sparse_vector": [], "title": "Color-to-Depth Mappings as <PERSON><PERSON><PERSON> Cues in Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "Yikai Cui", "<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON><PERSON>", "Yukang Yan", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite significant improvements to Virtual Reality (VR) technologies, most VR displays are fixed focus and depth perception is still a key issue that limits the user experience and the interaction performance. To supplement humans' inherent depth cues (e.g., retinal blur, motion parallax), we investigate users' perceptual mappings of distance to virtual objects' appearance to generate visual cues aimed to enhance depth perception. As a first step, we explore color-to-depth mappings for virtual objects so that their appearance differs in saturation and value to reflect their distance. Through a series of controlled experiments, we elicit and analyze users' strategies of mapping a virtual object's hue, saturation, value and a combination of saturation and value to its depth. Based on the collected data, we implement a computational model that generates color-to-depth mappings fulfilling adjustable requirements on confusion probability, number of depth levels, and consistent saturation/value changing tendency. We demonstrate the effectiveness of color-to-depth mappings in a 3D sketching task, showing that compared to single-colored targets and strokes, with our mappings, the users were more confident in the accuracy without extra cognitive load and reduced the perceived depth error by 60.8%. We also implement four VR applications and demonstrate how our color cues can benefit the user experience and interaction performance in VR.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545646"}, {"primary_key": "1769699", "vector": [], "sparse_vector": [], "title": "ShadowAstro: Levitating Constellation Silhouette for Spatial Exploration and Learning.", "authors": ["<PERSON><PERSON>g Li", "<PERSON><PERSON> Gao", "<PERSON>"], "summary": "We introduce ShadowAstro, a system that uses the levitating particles' casted shadow to produce a constellation pattern. In contrast to the traditional approach of making astronomical observations via AR, planetarium, and computer screens, we intend to use the shadows created by each levitated bead to construct the silhouette of constellations - a natural geometrical pattern that can be represented by a set of particles. In this proposal, we show that <PERSON><PERSON><PERSON> can help users inspect the 12 constellations on the ecliptic plane and augment users' experience with a projector that will serve as the light source. Through this, we draw a future vision, where <PERSON><PERSON><PERSON> can serve as an interactive tool with educational purposes or an art installation in museum. We believe the concept of designing interactions between the levitated objects and their casted shadows will provide a brand new experience to end user.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561345"}, {"primary_key": "1769700", "vector": [], "sparse_vector": [], "title": "RemoconHanger: Making Head Rotation in Remote Person using the Hanger Reflex.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For remote collaboration, it is essential to intuitively grasp the situation and spatial location. However, the difficulty in grasping information about the remote user's orientation can hinder remote communication. For example, if a remote user turns his or her head to the right to operate a device on the right, and this sensation cannot be shared, the image sent by the remote user suddenly appears to flow laterally, and it will lose the positional relationship like Figure 1 (left). Therefore, we propose a device using the \"hanger reflex\" to experience the sensation of head rotation intuitively. The \"hanger reflex\" is a phenomenon in which the head turns unconsciously when a wire hanger is placed on the head. It has been verified that the sensation of turning is produced by the distribution of pressure exerted by a device worn on the head. This research aims to construct a mechanism to verify its effectiveness for telecommunication that can unconsciously experience the remote user's rotation sensation using the hanger reflex phenomenon. An inertial measurement unit(IMU) grasps the remote user's rotation information like Figure 1 (right).", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558700"}, {"primary_key": "1769701", "vector": [], "sparse_vector": [], "title": "TangibleGrid: Tangible Web Layout Design for Blind Users.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present TangibleGrid, a novel device that allows blind users to understand and design the layout of a web page with real-time tangible feedback. We conducted semi-structured interviews and a series of co-design sessions with blind users to elicit insights that guided the design of TangibleGrid. Our final prototype contains shape-changing brackets representing the web elements and a baseboard representing the web page canvas. Blind users can design a web page layout through creating and editing web elements by snapping or adjusting tangible brackets on top of the baseboard. The baseboard senses the brackets' type, size, and location, verbalizes the information, and renders the web page on the client browser. Through a formative user study, we found that blind users could understand a web page layout through TangibleGrid. They were also able to design a new web layout from scratch without the help of sighted people.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545627"}, {"primary_key": "1769702", "vector": [], "sparse_vector": [], "title": "RealityTalk: Real-Time Speech-Driven Augmented Presentation for AR Live Storytelling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present RealityTalk, a system that augments real-time live presentations with speech-driven interactive virtual elements. Augmented presentations leverage embedded visuals and animation for engaging and expressive storytelling. However, existing tools for live presentations often lack interactivity and improvisation, while creating such effects in video editing tools require significant time and expertise. RealityTalk enables users to create live augmented presentations with real-time speech-driven interactions. The user can interactively prompt, move, and manipulate graphical elements through real-time speech and supporting modalities. Based on our analysis of 177 existing video-edited augmented presentations, we propose a novel set of interaction techniques and then incorporated them into RealityTalk. We evaluate our tool from a presenter's perspective to demonstrate the effectiveness of our system.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545702"}, {"primary_key": "1769703", "vector": [], "sparse_vector": [], "title": "Thumble: One-Handed 3D Object Manipulation Using a Thimble-Shaped Wearable Device in Virtual Reality.", "authors": ["Changsung Lim", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Conventional controllers or hand-tracking interactions in VR cause hand fatigue while manipulating 3D objects because repetitive wrist rotation and hand movements are often required. As a solution to this inconvenience, we propose Thumble, a novel wearable input device worn on the thumb for modifying the orientation of 3D objects. Thumble can rotate the 3D objects depending on the orientation of the thumb and using the thumb pad as an input surface on which the index finger rubs to control the direction and degree of rotations. Therefore, it requires minimal motion of the wrist and the arm. Through the informal user study, we collected the subjective feedback of users and found that <PERSON>humble has less hand movement than a conventional VR controller.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558703"}, {"primary_key": "1769704", "vector": [], "sparse_vector": [], "title": "Automated Filament Inking for Multi-color FFF 3D Printing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel system for low-cost multi-color Fused Filament Fabrication (FFF) 3D printing, allowing for the creation of customizable colored filament using a pre-processing approach. We developed an open-source device to automatically ink filament using permanent markers. Our device can be built using 3D printed parts and off-the-shelf electronics. An accompanying web-based interface allows users to view GCODE toolpaths for a multi-color print and quickly generate filament color profiles. Taking a pre-processing approach makes this system compatible with the majority of desktop 3D printers on the market, as the processed filament behaves no differently from conventional filaments. Furthermore, inked filaments can be produced economically, reducing the need for excessive purchasing of material to expand color options. We demonstrate the efficacy of our system by fabricating monochromatic objects, objects with gradient colors, objects with bi-directional properties, as well as multi-color objects with up to four colors in a single print.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545654"}, {"primary_key": "1769705", "vector": [], "sparse_vector": [], "title": "Wigglite: Low-cost Information Collection and Triage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Consumers conducting comparison shopping, researchers making sense of competitive space, and developers looking for code snippets online all face the challenge of capturing the information they find for later use without interrupting their current flow. In addition, during many learning and exploration tasks, people need to externalize their mental context, such as estimating how urgent a topic is to follow up on, or rating a piece of evidence as a \"pro\" or \"con,\" which helps scaffold subsequent deeper exploration. However, current approaches incur a high cost, often requiring users to select, copy, context switch, paste, and annotate information in a separate document without offering specific affordances that capture their mental context. In this work, we explore a new interaction technique called \"wiggling,\" which can be used to fluidly collect, organize, and rate information during early sensemaking stages with a single gesture. Wiggling involves rapid back-and-forth movements of a pointer or up-and-down scrolling on a smartphone, which can indicate the information to be collected and its valence, using a single, light-weight gesture that does not interfere with other interactions that are already available. Through implementation and user evaluation, we found that wiggling helped participants accurately collect information and encode their mental context with a 58% reduction in operational cost while being 24% faster compared to a common baseline.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545661"}, {"primary_key": "1769706", "vector": [], "sparse_vector": [], "title": "Opal: Multimodal Image Generation for News Illustration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Advances in multimodal AI have presented people with powerful ways to create images from text. Recent work has shown that text-to-image generations are able to represent a broad range of subjects and artistic styles. However, finding the right visual language for text prompts is difficult. In this paper, we address this challenge with Opal, a system that produces text-to-image generations for news illustration. Given an article, Opal guides users through a structured search for visual concepts and provides a pipeline allowing users to generate illustrations based on an article's tone, keywords, and related artistic styles. Our evaluation shows that Opal efficiently generates diverse sets of news illustrations, visual assets, and concept ideas. Users with Opal generated two times more usable results than users without. We discuss how structured exploration can help users better understand the capabilities of human AI co-creative systems.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545621"}, {"primary_key": "1769707", "vector": [], "sparse_vector": [], "title": "CrossA11y: Identifying Video Accessibility Issues via Cross-modal Grounding.", "authors": ["<PERSON>ng<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "Dingzeyu Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "Authors make their videos visually accessible by adding audio descriptions (AD), and auditorily accessible by adding closed captions (CC). However, creating AD and CC is challenging and tedious, especially for non-professional describers and captioners, due to the difficulty of identifying accessibility problems in videos. A video author will have to watch the video through and manually check for inaccessible information frame-by-frame, for both visual and auditory modalities. In this paper, we present CrossA11y, a system that helps authors efficiently detect and address visual and auditory accessibility issues in videos. Using cross-modal grounding analysis, CrossA11y automatically measures accessibility of visual and audio segments in a video by checking for modality asymmetries. CrossA11y then displays these segments and surfaces visual and audio accessibility issues in a unified interface, making it intuitive to locate, review, script AD/CC in-place, and preview the described and captioned video immediately. We demonstrate the effectiveness of CrossA11y through a lab study with 11 participants, comparing to existing baseline.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545703"}, {"primary_key": "1769708", "vector": [], "sparse_vector": [], "title": "Integrating Living Organisms in Devices to Implement Care-based Interactions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Researchers have been exploring how incorporating care-based interactions can change the user's attitude & relationship towards an interactive device. This is typically achieved through virtual care where users care for digital entities. In this paper, we explore this concept further by investigating how physical care for a living organism, embedded as a functional component of an interactive device, also changes user-device relationships. Living organisms differ as they require an environment conducive to life, which in our concept, the user is responsible for providing by caring for the organism (e.g., feeding it). We instantiated our concept by engineering a smartwatch that includes a slime mold that physically conducts power to a heart rate sensor inside the device, acting as a living wire. In this smartwatch, the availability of heart-rate sensing depends on the health of the slime mold—with the user's care, the slime mold becomes conductive and enables the sensor; conversely, without care, the slime mold dries and disables the sensor (resuming care resuscitates the slime mold). To explore how our living device was perceived by users, we conducted a study where participants wore our slime mold-integrated smartwatch for 9-14 days. We found that participants felt a sense of responsibility, developed a reciprocal relationship, and experienced the organism's growth as a source of affect. Finally, to allow engineers and designers to expand on our work, we abstract our findings into a set of technical and design recommendations when engineering an interactive device that incorporates this type of care-based relationship.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545629"}, {"primary_key": "1769709", "vector": [], "sparse_vector": [], "title": "Exploring Sensory Conflict Effect Due to Upright Redirection While Using VR in Reclining &amp; Lying Positions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chenyang Cai", "<PERSON><PERSON>", "Zhigeng Pan", "<PERSON>"], "summary": "When users use Virtual Reality (VR) in nontraditional postures, such as while reclining or lying in relaxed positions, their views lean upwards and need to be corrected, to make sure they see upright contents and perceive the interactions as if they were standing. Such upright redirection is excepted to cause visual-vestibular-proprioceptive conflict, affecting users' internal perceptions (e.g., body ownership, presence, simulator sickness) and external perceptions (e.g., egocentric space perception) in VR. Different body reclining angles may affect vestibular sensitivity and lead to the dynamic weighting of multi-sensory signals in the sensory integration. In the paper, we investigated the impact of upright redirection on users' perceptions, with users' physical bodies tilted at various angles backward and views upright redirected accordingly. The results showed that upright redirection led to simulator sickness, confused self-awareness, weak upright illusion, and increased space perception deviations to various extents when users are at different reclining positions, and the situations were the worst at the 45° conditions. Based on these results, we designed some illusion-based and sensory-based methods, that were shown effective in reducing the impact of sensory conflict through preliminary evaluations.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545692"}, {"primary_key": "1769710", "vector": [], "sparse_vector": [], "title": "Demonstrating a Fabricatable Bioreactor Toolkit for Small-Scale Biochemical Automation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Biological and chemical engineering creates novel materials through custom workflows. Supporting such materials development through systems research such as toolkits and software is increasingly of interest to HCI. Bioreactors are widely used systems which can grow materials, converting feedstock into valuable products through fermentation. However, integrated bioreactors are difficult to design and program. We present a modular toolkit for developing custom bioreactors. Our toolkit contains custom hardware and software for adding chemicals, monitoring the mixture, and refining outputs. We demonstrate our bioreactor toolkit with a beer brewing application, an automated process which involves several biochemical reactions that are comparable to other synthetic biology processes.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558661"}, {"primary_key": "1769711", "vector": [], "sparse_vector": [], "title": "DeltaPen: A Device with Integrated High-Precision Translation and Rotation Sensing on Passive Surfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present DeltaPen, a pen device that operates on passive surfaces without the need for external tracking systems or active sensing surfaces. DeltaPen integrates two adjacent lens-less optical flow sensors at its tip, from which it reconstructs accurate directional motion as well as yaw rotation. DeltaPen also supports tilt interaction using a built-in inertial sensor. A pressure sensor and high-fidelity haptic actuator complements our pen device while retaining a compact form factor that supports mobile use on uninstrumented surfaces. We present a processing pipeline that reliably extracts fine-grained pen translations and rotations from the two optical flow sensors. To asses the accuracy of our translation and angle estimation pipeline, we conducted a technical evaluation in which we compared our approach with ground-truth measurements of participants' pen movements during typical pen interactions. We conclude with several example applications that leverage our device's capabilities. Taken together, we demonstrate novel input dimensions with DeltaPen that have so far only existed in systems that require active sensing surfaces or external tracking.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545655"}, {"primary_key": "1769712", "vector": [], "sparse_vector": [], "title": "HomeView: Automatically Building Smart Home Digital Twins With Augmented Reality Headsets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Digital twins have demonstrated great capabilities in the industrial setting, but the cost of building them prohibits their usage in home environments. We present HomeView, a system that automatically builds and maintains a digital twin using data from Augmented Reality (AR) headsets and Internet of Things (IoT) devices. We evaluated the system in a simulator and found it performs better than the baseline algorithm. The user feedback on programming IoT devices also suggests that contextual information rendered by HomeView is preferable to text descriptions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558709"}, {"primary_key": "1769713", "vector": [], "sparse_vector": [], "title": "SenSequins: Smart Textile Using 3D Printed Conductive Sequins.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this research, we used traditional sequin embroidery as the basis and a 3D printer to expand the design space of sequin materials and structures, by developing a new 2.5D smart conductive sequin textile with multiple sensing and interactions as well as providing users with a customizing system for automated design and manufacturing.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545688"}, {"primary_key": "1769714", "vector": [], "sparse_vector": [], "title": "Expert Goggles: Detecting and Annotating Visualizations using a Machine Learning Classifier.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Data visualizations are an increasingly common way to communicate information online. However, not everyone has the data literacy skills necessary to interpret complex visualizations effectively. We present Expert Goggles, a Chrome browser extension that provides just-in-time guidance to help non-experts interpret the visualizations they encounter on the web. Expert Goggles uses a machine learning classifier to automatically determine the visualization type and uses this context to deliver relevant learning materials. We discuss how this approach to automatically detect the context and provide just-in-time support might transform everyday experiences into informal learning opportunities.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558627"}, {"primary_key": "1769715", "vector": [], "sparse_vector": [], "title": "FullPull : A Stretchable UI to Input Pulling Strength on Touch Surfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kodai Ito", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Touch surfaces are used as input interfaces for many devices such as smartphones, tablets, and smartwatches. However, the flexibility of the input surface is low, and the possible input operations are limited to planar ones such as touch and swipe. In contrast, in the field of HCI, there has been much research on increasing the number of input interactions by attaching augmented devices with various physical characteristics to the touch surface. However, most of these interactions are limited to operations where pressure is applied to the input surface. In this study, we propose FullPull, which consists of a rubber tube filled with conductive ink and a suction cup to attach the rubber tube to the surface. FullPull allows users to input pulling depth and strength on the touch surface. We implemented a prototype FullPull device which can be attached to an existing capacitive touch surface and can be pulled by a user. We then evaluated the accuracy of tensile strength estimation of the implemented device. The results showed that the outflow current value when stretched could be classified into four tensile strength levels.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558775"}, {"primary_key": "1769716", "vector": [], "sparse_vector": [], "title": "Mimic: In-Situ Recording and Re-Use of Demonstrations to Support Robot Teleoperation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Remote teleoperation is an important robot control method when they cannot operate fully autonomously. Yet, teleoperation presents challenges to effective and full robot utilization: controls are cumbersome, inefficient, and the teleoperator needs to actively attend to the robot and its environment. Inspired by end-user programming, we propose a new interaction paradigm to support robot teleoperation for combinations of repetitive and complex movements. We introduce Mimic, a system that allows teleoperators to demonstrate and save robot trajectories as templates, and re-use them to execute the same action in new situations. Templates can be re-used through (1) macros—parametrized templates assigned to and activated by buttons on the controller, and (2) programs—sequences of parametrized templates that operate autonomously. A user study in a simulated environment showed that after initial set up time, participants completed manipulation tasks faster and more easily compared to traditional direct control.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545639"}, {"primary_key": "1769717", "vector": [], "sparse_vector": [], "title": "PassengXR: A Low Cost Platform for Any-Car, Multi-User, Motion-Based Passenger XR Experiences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present PassengXR, an open-source toolkit for creating passenger eXtended Reality (XR) experiences in Unity. XR allows travellers to move beyond the physical limitations of in-vehicle displays, rendering immersive virtual content based on - or ignoring - vehicle motion. There are considerable technical challenges to using headsets in moving environments: maintaining the forward bearing of IMU-based headsets; conflicts between optical and inertial tracking of inside-out headsets; obtaining vehicle telemetry; and the high cost of design given the necessity of testing in-car. As a consequence, existing vehicular XR research typically relies on controlled, simple routes to compensate. PassengXR is a cost-effective open-source in-car passenger XR solution. We provide a reference set of COTS hardware that enables the broadcasting of vehicle telemetry to multiple headsets. Our software toolkit then provides support to correct vehicle-headset alignment, and then create a variety of passenger XR experiences, including: vehicle-locked content; motion- and location-based content; and co-located multi-passenger applications. PassengXR also supports the recording and playback of vehicle telemetry, assisting offline design without resorting to costly in-car testing. Through an evaluation-by-demonstration, we show how our platform can assist practitioners in producing novel, multi-user passenger XR experiences.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545657"}, {"primary_key": "1769718", "vector": [], "sparse_vector": [], "title": "interiqr: Unobtrusive Edible Tags using Food 3D Printing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present interiqr, a method that utilizes the infill parameter in the 3D printing process to embed information inside the food that is difficult to recognize with the human eye. Our key idea is to utilize the air space or secondary materials to generate a specific pattern inside the food without changing the model geometry. As a result, our method exploits the patterns that appear as hidden edible tags to store the data and simultaneously adds them to a 3D printing pipeline. Our contribution also includes the framework that connects the user with a data-embedding interface through the food 3D printing process, and the decoding system allows the user to decode the information inside the 3D printed food through backlight illumination and a simple image processing technique. Finally, we evaluate the usability of our method under different settings and demonstrate our method through the example application scenarios.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545669"}, {"primary_key": "1769719", "vector": [], "sparse_vector": [], "title": "&quot;Inconsistent Performance&quot;: Understanding Concerns of Real-World Users on Smart Mobile Health Applications Through Analyzing App Reviews.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While smart mobile health apps that adapt to users' progressive individual needs are proliferating, many of them struggle to fulfill their promises due to an inferior user experience. Understanding the concerns of real-world users related to those apps, and their smart components in particular, could help advance the app design to attract and retain users. In this paper, we target this issue through a preliminary thematic analysis of 120 user reviews of six smart health apps. We found that accuracy, customizability, and convenience of data input are primary concerns raised in real-world user reviews. Many concerns on the smart components are related to the trust issue of the users towards the apps. However, several important aspects such as privacy and fairness were rarely discussed in the reviews. Overall, our study provides insights that can inspire further investigations to support the design of smart mobile health apps.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558698"}, {"primary_key": "1769720", "vector": [], "sparse_vector": [], "title": "ShrinkCells: Localized and Sequential Shape-Changing Actuation of 3D-Printed Objects via Selective Heating.", "authors": ["<PERSON><PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The unique behaviors of thermoplastic polymers enable shape-changing interfaces made of 3D printed objects that do not require complex electronics integration. While existing techniques rely on external trigger, such as heat, applied globally on a 3D printed object initiating all at once the shape-changing response (e.g., hot water, heat gun, oven), independent control of multiple parts of the object becomes nearly impossible. We introduce ShrinkCells, a set of shape-changing actuators that enables localized heat to shrink or bend, through combining the properties of two materials — conductive PLA is used to generate localized heat which selectively triggers the shrinking of a Shape Memory Polymer. The unique benefit of ShrinkCells is their capability of triggering simultaneous or sequential shape transformations for different geometries using a single power supply. This results in 3D printed rigid structures that actuate in sequence, avoiding self-collisions when unfolding. We contribute to the body of literature on 4D fabrication by a systematic investigation of selective heating with two different materials, the design and evaluation of the ShrinkCells shape-changing primitives, and applications demonstrating the usage of these actuators.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545670"}, {"primary_key": "1769721", "vector": [], "sparse_vector": [], "title": "Improving 3D-Editing Workflows via Acoustic Levitation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We outline how to improve common 3D-editing workflows such as modeling or character animation by utilizing an acoustic levitation kit as an interactive 3D display. Our proposed system allows users to directly interact with models in 3D space and perform multi-point gestures to manipulate them. Editing of complex 3D objects can be enabled by combining the 3D display with an LCD, projector or HMD to display additional context.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561353"}, {"primary_key": "1769722", "vector": [], "sparse_vector": [], "title": "Muscle Synergies Learning with Electrical Muscle Stimulation for Playing the Piano.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When playing scales on the piano, playing all notes evenly is a basic technique to improve the quality of music. However, it is difficult for beginners to do this because they need to achieve appropriate muscle synergies of the forearm and shoulder muscles, i.e., pressing keys as well as sliding their hands sideways. In this paper, we propose a system using electrical muscle stimulation (EMS) to teach beginners how to improve their muscle synergies while playing scales. We focus on \"thumb-under\" method and assist with it by applying EMS to the deltoid muscle. We conducted a user study to investigate whether our EMS-based system can help beginners learn new muscle synergies in playing ascending scales. We divided the participants into two groups: an experimental group that practiced with EMS and a control group that practiced without EMS. The results showed that practicing with EMS was more effective in improving the evenness of scales than without EMS and that the muscle synergies changed after practicing.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545666"}, {"primary_key": "1769723", "vector": [], "sparse_vector": [], "title": "DigituSync: A Dual-User Passive Exoskeleton Glove That Adaptively Shares Hand Gestures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We engineered DigituSync, a passive-exoskeleton that physically links two hands together, enabling two users to adaptively transmit finger movements in real-time. It uses multiple four-bar linkages to transfer both motion and force, while still preserving congruent haptic feedback. Moreover, we implemented a variable-length linkage that allows adjusting the force transmission ratio between the two users and regulates the amount of intervention, which enables users to customize their learning experience. DigituSync's benefits emerge from its passive design: unlike existing haptic devices (motor-based exoskeletons or electrical muscle stimulation), DigituSync has virtually no latency and does not require batteries/electronics to transmit or adjust movements, making it useful and safe to deploy in many settings, such as between students and teachers in a classroom. We validated DigituSync by means of technical evaluations and a user study, demonstrating that it instantly transfers finger motions and forces with the ability of adaptive force transmission, which allowed participants to feel more control over their own movements and to feel the teacher's intervention was more responsive. We also conducted two exploratory sessions with a music teacher and deaf-blind users, which allowed us to gather experiential insights from the teacher's side and explore Di<PERSON>uSync in applications.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545630"}, {"primary_key": "1769724", "vector": [], "sparse_vector": [], "title": "Mixels: Fabricating Interfaces using Programmable Magnetic Pixels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present Mixels, programmable magnetic pixels that can be rapidly fabricated using an electromagnetic printhead mounted on an off-the-shelve 3-axis CNC machine. The ability to program magnetic material pixel-wise with varying magnetic force enables Mixels to create new tangible, tactile, and haptic interfaces. To facilitate the creation of interactive objects with Mixels, we provide a user interface that lets users specify the high-level magnetic behavior and that then computes the underlying magnetic pixel assignments and fabrication instructions to program the magnetic surface. Our custom hardware add-on based on an electromagnetic printhead and hall effect sensor clips onto a standard 3-axis CNC machine and can both write and read magnetic pixel values from magnetic material. Our evaluation shows that our system can reliably program and read magnetic pixels of various strengths, that we can predict the behavior of two interacting magnetic surfaces before programming them, that our electromagnet is strong enough to create pixels that utilize the maximum magnetic strength of the material being programmed, and that this material remains magnetized when removed from the magnetic plotter.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545698"}, {"primary_key": "1769725", "vector": [], "sparse_vector": [], "title": "Transtiff: A Stick Interface with Various Stiffness by Artificial Muscle Mechanism.", "authors": ["<PERSON><PERSON><PERSON>", "Kodai Ito", "<PERSON><PERSON>"], "summary": "We manipulate stick objects, such as chopsticks and pens in our daily life. The senses of the human hand are extremely sensitive and can acquire detailed information. By perceiving changes in the feel of the finger, we perceive the characteristic of the object when we manipulate sticks, such as pens and brushes. Therefore, we can extend the tactile experience of touching something by controlling a stick's grasping sensation. In this study, we propose Transtiff which has a joint that changes its stiffness dynamically to a stick object that generally cannot bend. We use a piston mechanism that uses a small motor to compress the liquid in a flexible tube to control the stiffness of the joint.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558777"}, {"primary_key": "1769726", "vector": [], "sparse_vector": [], "title": "Demonstrating ex-CHOCHIN: Shape/Texture-changing cylindrical interface with deformable origami tessellation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate ex-CHOCHIN which is a cylindrical shape/texture-changing display inspired by the chochin, a traditional Japanese foldable lantern. Ex-CHOCHIN achieves complex control over origami such as local deformation and control in the intermediate process of folding by attaching multiple mechanisms to the origami tessellation. It thereby results in flexible deformations that can be adapted to a wide range of shapes, a one-continuous surface without gaps, and even changes in texture. It creates several deformed shapes from a crease pattern, allowing flexible deformation to function as a display. We have also produced an application using ex-CHOCHIN.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558626"}, {"primary_key": "1769727", "vector": [], "sparse_vector": [], "title": "WaddleWalls: Room-scale Interactive Partitioning System using a Swarm of Robotic Partitions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose WaddleWalls, a room-scale interactive partitioning system using a swarm of robotic partitions that allows occupants to interactively reconfigure workspace partitions to satisfy their privacy and interaction needs. The system can automatically arrange the partitions' layout designed by the user on demand. The user specifies the target partition's position, orientation, and height using the controller's 3D manipulations. In this work, we discuss the design considerations of the interactive partition system and implement WaddleWalls' proof-of-concept prototype assembled with off-the-shelf materials. We demonstrate the functionalities of WaddleWalls through several application scenarios in an open-planned office environment. We also conduct an initial user evaluation that compares WaddleWalls with conventional wheeled partitions, finding that WaddleWalls allows effective workspace partitioning and mitigates the physical and temporal efforts needed to fulfill ad hoc social and privacy requirements. Finally, we clarify the feasibility, potential, and future challenges of WaddleWalls through an interview with experts.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545615"}, {"primary_key": "1769728", "vector": [], "sparse_vector": [], "title": "InterWeave: Presenting Search Suggestions in Context Scaffolds Information Search and Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Web search is increasingly used to satisfy complex, exploratory information goals. Exploring and synthesizing information into knowledge can be slow and cognitively demanding due to a disconnect between search tools and sense-making workspaces. Our work explores how we might integrate contextual query suggestions within a person's sensemaking environment. We developed InterWeave a prototype that leverages a human wizard to generate contextual search guidance and to place the suggestions within the emergent structure of a searchers' notes. To investigate how weaving suggestions into the sensemaking workspace affects a user's search and sensemaking behavior, we ran a between-subjects study (n=34) where we compare InterWeave's in context placement with a conventional list of query suggestions. InterWeave's approach not only promoted active searching, information gathering and knowledge discovery, but also helped participants keep track of new suggestions and connect newly discovered information to existing knowledge, in comparison to presenting suggestions as a separate list. These results point to directions for future work to interweave contextual and natural search guidance into everyday work.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545696"}, {"primary_key": "1769729", "vector": [], "sparse_vector": [], "title": "ASTREL: Prototyping Shape-changing Interface with Variable Stiffness Soft Robotics Module.", "authors": ["<PERSON>"], "summary": "Prototyping a shape-changing interface is challenging because it requires knowledge of both electronics and mechanical engineering. In this study, we introduced a prototyping platform using a soft pneumatic artificial muscle(PAMs) and modular 3D printed reinforcement. To facilitate a wide variety of applications we propose six types of reinforcement modules capable of either shape deformation and/or variable stiffness. Users can create an approximate prototype using lego-built modules with magnetic connectors. A modeling toolkit can then be used to recreate and customize the prototype structure. After 3D printing, the shape-changing interface can be assembled by threading the PAMs through holes in the reinforcement. We envision that this prototyping platform can be useful in shape-changing interface exploration, where researchers can create working prototypes easily, rapidly, and at a low cost.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558733"}, {"primary_key": "1769730", "vector": [], "sparse_vector": [], "title": "Social Simulacra: Creating Populated Prototypes for Social Computing Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social computing prototypes probe the social behaviors that may arise in an envisioned system design. This prototyping practice is currently limited to recruiting small groups of people. Unfortunately, many challenges do not arise until a system is populated at a larger scale. Can a designer understand how a social system might behave when populated, and make adjustments to the design before the system falls prey to such challenges? We introduce social simulacra, a prototyping technique that generates a breadth of realistic social interactions that may emerge when a social computing system is populated. Social simulacra take as input the designer's description of a community's design—goal, rules, and member personas—and produce as output an instance of that design with simulated behavior, including posts, replies, and anti-social behaviors. We demonstrate that social simulacra shift the behaviors that they generate appropriately in response to design changes, and that they enable exploration of \"what if?\" scenarios where community members or moderators intervene. To power social simulacra, we contribute techniques for prompting a large language model to generate thousands of distinct community members and their social interactions with each other; these techniques are enabled by the observation that large language models' training data already includes a wide variety of positive and negative behavior on social media platforms. In evaluations, we show that participants are often unable to distinguish social simulacra from actual community behavior and that social computing designers successfully refine their social computing designs when using social simulacra.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545616"}, {"primary_key": "1769731", "vector": [], "sparse_vector": [], "title": "ForceSight: Non-Contact Force Sensing with Laser Speckle Imaging.", "authors": ["<PERSON><PERSON><PERSON> Pei", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Force sensing has been a key enabling technology for a wide range of interfaces such as digitally enhanced body and world surfaces for touch interactions. Additionally, force often contains rich contextual information about user activities and can be used to enhance machine perception for improved user and environment awareness. To sense force, conventional approaches rely on contact sensors made of pressure-sensitive materials such as piezo films/discs or force-sensitive resistors. We present ForceSight, a non-contact force sensing approach using laser speckle imaging. Our key observation is that object surfaces deform in the presence of force. This deformation, though very minute, manifests as observable and discernible laser speckle shifts, which we leverage to sense the applied force. This non-contact force-sensing capability opens up new opportunities for rich interactions and can be used to power user-/environment-aware interfaces. We first built and verified the model of laser speckle shift with surface deformations. To investigate the feasibility of our approach, we conducted studies on metal, plastic, wood, along with a wide variety of materials. Additionally, we included supplementary tests to fully tease out the performance of our approach. Finally, we demonstrated the applicability of ForceSight with several demonstrative example applications.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545622"}, {"primary_key": "1769732", "vector": [], "sparse_vector": [], "title": "CircuitAssist: Automatically Dispensing Electronic Components to Facilitate Circuit Building.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When learning how to build circuits, one of the challenges novice makers face is how to identify the components needed for the circuit. Many makerspaces are stocked with a variety of electronic components that look visually similar or have similar names. Thus, novice makers may pick the wrong component, which creates a non-functional circuit although the wires are correctly connected. To address this issue, we present CircuitAssist, an actuated electronics component shelf connected to a tutorial system that dispenses electronic components for the maker in the order that they occur in the tutorial. The shelf contains dispensers for each component type with a custom release mechanism actuated by a servo motor that dispenses one component at a time. Makers can work with CircuitAssist by either following one of the provided tutorials or by directly selecting a component they need from the user interface.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558671"}, {"primary_key": "1769733", "vector": [], "sparse_vector": [], "title": "Diffscriber: Describing Visual Design Changes to Support Mixed-Ability Collaborative Presentation Authoring.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visual slide-based presentations are ubiquitous, yet slide authoring tools are largely inaccessible to people who are blind or visually impaired (BVI). When authoring presentations, the 9 BVI presenters in our formative study usually work with sighted collaborators to produce visual slides based on the text content they produce. While BVI presenters valued collaborators' visual design skill, the collaborators often felt they could not fully review and provide feedback on the visual changes that were made. We present Diffscriber, a system that identifies and describes changes to a slide's content, layout, and style for presentation authoring. Using our system, BVI presentation authors can efficiently review changes to their presentation by navigating either a summary of high-level changes or individual slide elements. To learn more about changes of interest, presenters can use a generated change hierarchy to navigate to lower-level change details and element styles. BVI presenters using Diffscriber were able to identify slide design changes and provide feedback more easily as compared to using only the slides alone. More broadly, Diffscriber illustrates how advances in detecting and describing visual differences can improve mixed-ability collaboration.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545637"}, {"primary_key": "1769734", "vector": [], "sparse_vector": [], "title": "Hands-On: Using Gestures to Control Descriptions of a Virtual Environment for People with Visual Impairments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Virtual reality (VR) uses three main senses to relay information: sight, sound, and touch. People with visual impairments (PVI) rely primarily on auditory and haptic feedback to receive information in VR. While researchers have explored several approaches to make navigation and perception of objects more accessible in VR, none of them offer a natural way to request descriptions of objects, nor control of the flow of auditory information. In this demonstration, we present a haptic glove that PVI can use to request object descriptions in VR with their hands through familiar hand gestures. We contribute designs for a set of hand gestures that allow PVI to interactively get descriptions of the VR environment. We plan to conduct an user study where we will have PVI interact with a VR environment using these gestures to request audio descriptions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558669"}, {"primary_key": "1769735", "vector": [], "sparse_vector": [], "title": "Explorations of Wrist Haptic Feedback for AR/VR Interactions with Tasbi.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Marcia O&<PERSON>;<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most widespread haptic feedback devices for augmented and virtual reality (AR/VR) fall into one of two categories: simple hand-held controllers with a single vibration actuator, or complex glove systems with several embedded actuators. In this work, we explore haptic feedback on the wrist for interacting with virtual objects. We use Tasbi, a compact bracelet device capable of rendering complex multisensory squeeze and vibrotactile feedback. Leveraging <PERSON><PERSON><PERSON>'s haptic rendering, and using standard visual and audio rendering of a head mounted display, we present several interactions that tightly integrate sensory substitutive haptics with visual and audio cues. Interactions include push/pull buttons, rotary knobs, textures, rigid body weight and inertia, and several custom bimanual manipulations such as shooting an arrow from a bow. These demonstrations suggest that wrist-based haptic feedback substantially improves virtual hand-based interactions in AR/VR compared to no haptic feedback.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558658"}, {"primary_key": "1769736", "vector": [], "sparse_vector": [], "title": "Knitted Force Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demo, we present two types of knitted resistive force sensors for both pressure and strain sensing. They can be manufactured ready-made on a two-bed weft knitting machine, without requiring further post-processing steps. Due to their softness, elasticity, and breathability our sensors provide an appealing haptic experience. We show their working principle, discuss their advantages and limitations, and elaborate on different areas of application. They are presented as standalone demonstrators, accompanied by exemplary applications to provide insights into their haptic qualities and sensing capabilities.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558656"}, {"primary_key": "1769737", "vector": [], "sparse_vector": [], "title": "PSST: Enabling Blind or Visually Impaired Developers to Author Sonifications of Streaming Sensor Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the first toolkit that equips blind and visually impaired (BVI) developers with the tools to create accessible data displays. Called PSST (Physical computing Streaming Sensor data Toolkit), it enables BVI developers to understand the data generated by sensors from a mouse to a micro:bit physical computing platform. By assuming visual abilities, earlier efforts to make physical computing accessible fail to address the need for BVI developers to access sensor data. PSST enables BVI developers to understand real-time, real-world sensor data by providing control over what should be displayed, as well as when to display and how to display sensor data. PSST supports filtering based on raw or calculated values, highlighting, and transformation of data. Output formats include tonal sonification, nonspeech audio files, speech, and SVGs for laser cutting. We validate PSST through a series of demonstrations and a user study with BVI developers.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545700"}, {"primary_key": "1769738", "vector": [], "sparse_vector": [], "title": "SemanticOn: Specifying Content-Based Semantic Conditions for Web Automation Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Data scientists, researchers, and clerks often create web automation programs to perform repetitive yet essential tasks, such as data scraping and data entry. However, existing web automation systems lack mechanisms for defining conditional behaviors where the system can intelligently filter candidate content based on semantic filters (e.g., extract texts based on key ideas or images based on entity relationships). We introduce SemanticOn, a system that enables users to specify, refine, and incorporate visual and textual semantic conditions in web automation programs via two methods: natural language description via prompts or information highlighting. Users can coordinate with SemanticOn to refine the conditions as the program continuously executes or reclaim manual control to repair errors. In a user study, participants completed a series of conditional web automation tasks. They reported that SemanticOn helped them effectively express and refine their semantic intent by utilizing visual and textual conditions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545691"}, {"primary_key": "1769739", "vector": [], "sparse_vector": [], "title": "ARnnotate: An Augmented Reality Interface for Collecting Custom Dataset of 3D Hand-Object Interaction Pose Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Xiyun Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vision-based 3D pose estimation has substantial potential in hand-object interaction applications and requires user-specified datasets to achieve robust performance. We propose ARnnotate, an Augmented Reality (AR) interface enabling end-users to create custom data using a hand-tracking-capable AR device. Unlike other dataset collection strategies, ARnnotate first guides a user to manipulate a virtual bounding box and records its poses and the user's hand joint positions as the labels. By leveraging the spatial awareness of AR, the user manipulates the corresponding physical object while following the in-situ AR animation of the bounding box and hand model, while ARnnotate captures the user's first-person view as the images of the dataset. A 12-participant user study was conducted, and the results proved the system's usability in terms of the spatial accuracy of the labels, the satisfactory performance of the deep neural networks trained with the data collected by ARnnotate, and the users' subjective feedback.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545663"}, {"primary_key": "1769740", "vector": [], "sparse_vector": [], "title": "DualVoice: Speech Interaction that Discriminates between Normal and Whispered Voice Input.", "authors": ["<PERSON>"], "summary": "Interactions based on automatic speech recognition (ASR) have become widely used, with speech input being increasingly utilized to create documents. However, as there is no easy way to distinguish between commands being issued and text required to be input in speech, misrecognitions are difficult to identify and correct, meaning that documents need to be manually edited and corrected. The input of symbols and commands is also challenging because these may be misrecognized as text letters. To address these problems, this study proposes a speech interaction method called DualVoice, by which commands can be input in a whispered voice and letters in a normal voice. The proposed method does not require any specialized hardware other than a regular microphone, enabling a complete hands-free interaction. The method can be used in a wide range of situations where speech recognition is already available, ranging from text input to mobile/wearable computing. Two neural networks were designed in this study, one for discriminating normal speech from whispered speech, and the second for recognizing whisper speech. A prototype of a text input system was then developed to show how normal and whispered voice can be used in speech text input. Other potential applications using DualVoice are also discussed.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545685"}, {"primary_key": "1769741", "vector": [], "sparse_vector": [], "title": "Demonstration of Geppetteau: Enabling haptic perceptions of virtual fluids in various vessel profiles using a string-driven haptic interface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Shiling Dai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Liquids sloshing around in vessels produce unique unmistakable tactile sensations of handling fluids in daily life, laboratory environments, and industrial contexts. Providing nuanced congruent tactile sensations would enrich interactions of handling fluids in virtual reality (VR). To this end, we introduce Geppetteau, a novel string-driven weight-shifting mechanism capable of providing a continuous spectrum of perceivable tactile sensations of handling virtual liquids in VR vessels. <PERSON><PERSON><PERSON><PERSON>'s weight-shifting actuation system can be housed in 3D-printable shells, adapting to varying vessel shapes and sizes. A variety of different fluid behaviors can be felt using our haptic interface. In this work, <PERSON><PERSON><PERSON><PERSON> assumes the shape of conical, spherical, cylindrical, and cuboid flasks, widening the range of augmentable shapes beyond the state-of-the-art of existing mechanical systems.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558611"}, {"primary_key": "1769742", "vector": [], "sparse_vector": [], "title": "SomaFlatables: Supporting Embodied Cognition through Pneumatic Bladders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Florian &apos;Floyd&apos; Mueller"], "summary": "Applying the theory of Embodied Cognition through design allows us to create computational interactions that engage our bodies by modifying our body schema. However, in HCI, most of these interactive experiences have been stationed around creating sensing-based systems that leverage our body's position and movement to offer an experience, such as games using Nintendo Wii and Xbox Kinect. In this work, we created two pneumatic inflatables-based prototypes that actuate our body to support embodied cognition in two scenarios by altering the user's body schema. We call these \"SomaFlatables\" and demonstrate the design and implementation of these inflatables based prototypes that can move and even extend our bodies, allowing for novel bodily experiences. Furthermore, we discuss the future work and limitations of the current implementation.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558705"}, {"primary_key": "1769743", "vector": [], "sparse_vector": [], "title": "Amplified Carousel: Amplifying the Perception of Vertical Movement using Optical Illusion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the spread of virtual reality (VR) attractions, vector generation techniques that enhance the sense of realism are gaining attention. Additionally, mixed reality (MR) attractions, which overlay VR onto a real-world display, are expected to become more prevalent in the future. However, with MR, it is impossible to move all the coordinates of the visual stimuli to generate proper vection effects. Therefore, we have created an optical illusion method that provides a three-dimensional impression of a two-dimensional visual stimulation. The technique amplifies the sensation of vertical movement by placing the illusion on the floor.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558735"}, {"primary_key": "1769744", "vector": [], "sparse_vector": [], "title": "Methods of Gently Notifying Pedestrians of Approaching Objects when Listening to Music.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many people now listen to music with earphones while walking, and are less likely to notice approaching people, cars, etc. Many methods of detecting approaching objects and notifying pedestrians have been proposed, but few have focused on low urgency situations or music listeners, and many notification methods are unpleasant. Therefore, in this work, we propose methods of gently notifying pedestrians listening to music of approaching objects using environmental sound. We conducted experiments in a virtual environment to assess directional perception accuracy and comfort. Our results show the proposed method allows participants to detect the direction of approaching objects as accurately as explicit notification methods, with less discomfort.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558728"}, {"primary_key": "1769745", "vector": [], "sparse_vector": [], "title": "AirLogic: Embedding Pneumatic Computation and I/O in 3D Models to Fabricate Electronics-Free Interactive Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Kim"], "summary": "Researchers have developed various tools and techniques towards the vision of on-demand fabrication of custom, interactive devices. Recent work has 3D-printed artefacts like speakers, electromagnetic actuators, and hydraulic robots. However, these are non-trivial to instantiate as they require post-fabrication mechanical- or electronic assembly. We introduce AirLogic: a technique to create electronics-free, interactive objects by embedding pneumatic input, logic processing, and output widgets in 3D-printable models. AirLogic devices can perform basic computation on user inputs and create visible, audible, or haptic feedback; yet they do not require electronic circuits, physical assembly, or resetting between uses. Our library of 13 exemplar widgets can embed AirLogic-style computational capabilities in existing 3D models. We evaluate our widgets' performance - quantifying the loss of airflow (1) in each widget type, (2) based on printing orientation, and (3) from internal object geometry. Finally, we present five applications that illustrate AirLogic's potential.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545642"}, {"primary_key": "1769746", "vector": [], "sparse_vector": [], "title": "TrackItPipe: A Fabrication Pipeline To Incorporate Location and Rotation Tracking Into 3D Printed Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing convergence of the digital and physical world creates a growing urgency to integrate 3D printed physical tangibles with virtual environments. A precise position and rotation tracking are essential to integrate such physical objects with a virtual environment. However, available 3D models commonly do not provide tracking support on their composition, which requires modifications by CAD experts. This poses a challenge for users with no prior CAD experience. This work presents TrackItPipe, a fabrication pipeline supporting users by semi-automatically adding tracking capabilities for 3D printable tangibles tailored to environmental requirements. TrackItPipe integrates modifications to the 3D model, produces the respective tangibles for 3D printing, and provides integration scripts for Mixed Reality. Using TrackItPipe, users can rapidly equip objects with tracking capabilities.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558719"}, {"primary_key": "1769747", "vector": [], "sparse_vector": [], "title": "Detecting Input Recognition Errors and User Errors using Gaze Dynamics in Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Gesture-based recognition systems are susceptible to input recognition errors and user errors, both of which negatively affect user experiences and can be frustrating to correct. Prior work has suggested that user gaze patterns following an input event could be used to detect input recognition errors and subsequently improve interaction. However, to be useful, error detection systems would need to detect various types of high-cost errors. Furthermore, to build a reliable detection model for errors, gaze behaviour following these errors must be manifested consistently across different tasks. Using data analysis and machine learning models, this research examined gaze dynamics following input events in virtual reality (VR). Across three distinct point-and-select tasks, we found differences in user gaze patterns following three input events: correctly recognized input actions, input recognition errors, and user errors. These differences were consistent across tasks, selection versus deselection actions, and naturally occurring versus experimentally injected input recognition errors. A multi-class deep neural network successfully discriminated between these three input events using only gaze dynamics, achieving an AUC-ROC-OVR score of 0.78. Together, these results demonstrate the utility of gaze in detecting interaction errors and have implications for the design of intelligent systems that can assist with adaptive error recovery.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545628"}, {"primary_key": "1769748", "vector": [], "sparse_vector": [], "title": "KineCAM: An Instant Camera for Animated Photographs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Catalina Monsalve Rodriguez", "<PERSON>", "<PERSON><PERSON>"], "summary": "The kinegram is a classic animation technique that involves sliding a striped overlay across an interlaced image to create the effect of frame-by-frame motion. While there are known tools for generating kinegrams from pre-existing videos and images, there exists no system for capturing and fabricating kinegrams in situ. To bridge this gap, we created KineCAM, an open source1 instant camera that captures and prints animated photographs in the form of kinegrams. KineCAM combines the form factor of instant cameras with the expressiveness of animated photographs to explore and extend creative applications for instant photography.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558621"}, {"primary_key": "1769749", "vector": [], "sparse_vector": [], "title": "Chatbots Facilitating Consensus-Building in Asynchronous Co-Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Consensus-building is an essential process for the success of co-design projects. To build consensus, stakeholders need to discuss conflicting needs and viewpoints, converge their ideas toward shared interests, and grow their willingness to commit to group decisions. However, managing group discussions is challenging in large co-design projects with multiple stakeholders. In this paper, we investigate the interaction design of a chatbot that can mediate consensus-building conversationally. By interacting with individual stakeholders, the chatbot collects ideas to satisfy conflicting needs and engages stakeholders to consider others' viewpoints, without having stakeholders directly interact with each other. Results from an empirical study in an educational setting (N = 12) suggest that the approach can increase stakeholders' commitment to group decisions and maintain the effect even on the group decisions that conflict with personal interests. We conclude that chatbots can facilitate consensus-building in small-to-medium-sized projects, but more work is needed to scale up to larger projects.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545671"}, {"primary_key": "1769750", "vector": [], "sparse_vector": [], "title": "Bodyweight Exercise based Exergame to Induce High Intensity Interval Training.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Song", "<PERSON><PERSON>"], "summary": "Exergames have been proposed as an attractive way of making exercise fun; however, most of them do not reach the recommended intensity. Although HCI research has explored how exergame can be designed to follow High Intensity Interval Training (HIIT) that is effective exercise consisting of intermittent vigorous activity and short rest or low-intensity exercise, there are limited studies on designing bodyweight exercise (BWE) based exergame to follow HIIT. In this paper, we propose BWE based exergame to encourage users to maintain high intensity exercise. Our initial study (n=10) showed that the exergame had a significant effect on enjoyment, while the ratio of incorrect posture (ex., squat) also increased due to participants' concentration on the exergame, which imply future design implications of BWE based exergames.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558692"}, {"primary_key": "1769751", "vector": [], "sparse_vector": [], "title": "Search with Space: Find and Visualize Furniture in Your Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online shopping in the home category enables quick and convenient access to large catalog of products. In particular, users can simultaneously browse for functional requirements, such as size and material, while evaluating aesthetic fit, such as color and style, across hundreds of product offerings. However, the typical user flow requires navigating to an e-commerce retailer's website first, setting the search/filter parameters that may be generic, and then landing on product pages, one at a time, to make a decision. Upon purchase, \"does not fit\" is among the top reasons for returning a product. Amalgamating the above information, we present Search with Space, a novel interactive approach that a) inputs the user's space as a search parameter to b) filter for product matches that will physically fit, and c) visualize these matches in the user's space at true scale and in a format that facilitates simultaneous comparison. Briefly, the user leverages augmented reality (AR) to set a proxy 3d product in the desired location, updates the proxy's dimensions, and takes photos from preset angles. Using spatial information captured with AR, a web-based gallery page is curated with all the product matches that will physically fit and products are shown at true scale in their original photos. The user may now browse products visualized in the context of their space and evaluate based on their shopping criteria, share the gallery page with designers or partners for asynchronous feedback, re-use the photos for a different product class, or re-capture their space with different criteria altogether. Search with Space inverts the typical user journey by starting with the user's space and maintaining that context across all touch points with the catalog.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558740"}, {"primary_key": "1769752", "vector": [], "sparse_vector": [], "title": "LipLearner: Customizing Silent Speech Commands from Voice Input using One-shot Lipreading.", "authors": ["Zixiong Su", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present LipLearner, a lipreading-based silent speech interface that enables in-situ command customization on mobile devices. By leveraging contrastive learning to learn efficient representations from existing datasets, it performs instant fine-tuning for unseen users and words using one-shot learning. To further minimize the labor of command registration, we incorporate speech recognition to automatically learn new commands from voice input. Conventional lipreading systems provide limited pre-defined commands due to the time cost and user burden of data collection. In contrast, our technique provides expressive silent speech interaction with minimal data requirements. We conducted a pilot experiment to investigate the real-time performance of LipLearner, and the result demonstrates that an average accuracy of is achievable with only one training sample for each command.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558737"}, {"primary_key": "1769753", "vector": [], "sparse_vector": [], "title": "Demonstrating p5.fab: Direct Control of Digital Fabrication Machines from a Creative Coding Environment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine settings and tuning are critical for digital fabrication outcomes. However, exploring these parameters is non-trivial. We seek to enable exploration of the full design space of digital fabrication. To do so, we built p5.fab, a system for controlling digital fabrication machines from the creative coding environment p5.js and informed by a qualitative study of 3D printer maintenance practices. p5.fab prioritizes material exploration, fine-tuned control, and iteration in fabrication workflows. We demonstrate p5.fab with examples of 3D prints that cannot be made with traditional 3D printing software, including delicate bridging structures and prints on top of existing objects.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558646"}, {"primary_key": "1769754", "vector": [], "sparse_vector": [], "title": "CodeToon: Story Ideation, Auto Comic Generation, and Structure Mapping for Code-Driven Storytelling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent work demonstrated how we can design and use coding strips, a form of comic strips with corresponding code, to enhance teaching and learning in programming. However, creating coding strips is a creative, time-consuming process. Creators have to generate stories from code (code->story) and design comics from stories (story->comic). We contribute CodeToon, a comic authoring tool that facilitates this code-driven storytelling process with two mechanisms: (1) story ideation from code using metaphor and (2) automatic comic generation from the story. We conducted a two-part user study that evaluates the tool and the comics generated by participants to test whether CodeToon facilitates the authoring process and helps generate quality comics. Our results show that CodeToon helps users create accurate, informative, and useful coding strips in a significantly shorter time. Overall, this work contributes methods and design guidelines for code-driven storytelling and opens up opportunities for using art to support computer science education.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545617"}, {"primary_key": "1769755", "vector": [], "sparse_vector": [], "title": "Anywhere Hoop: Virtual Free Throw Training System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To complete successfully a high percentage of free throws in basketball, the shooter must achieve a stable trajectory with the ball. A player must practice shooting repeatedly to shoot a ball with a stable trajectory. However, traditional practice methods require the preparation of a real basketball hoop, which has made it difficult for some players to prepare a practice environment. We propose a training method for free throws using a virtual basketball hoop. In this paper, we present an implementation of the proposed method and experimental results showing its effectiveness.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558639"}, {"primary_key": "1769756", "vector": [], "sparse_vector": [], "title": "Exploring Virtual Object Translation in Head-Mounted Augmented Reality for Upper Limb Motor Rehabilitation with Motor Performance and Eye Movement Characteristics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Head-mounted augmented reality (AR) technology is currently employed in upper limb motor rehabilitation, and the degrees of freedom (DoF) of virtual object translation modes become critical for rehabilitation tasks in AR settings. Since motor performance is the primary focus of motor rehabilitation, this study assessed it across different translation modes (1DoF and 3DoF) via task efficiency and accuracy analysis. In addition, eye movement characteristics were used to further illustrate motor performance. This research revealed 1DoF and 3DoF modes showing their own benefits in upper limb motor rehabilitation tasks. Finally, this study recommended selecting or integrating these two translation modes for future rehabilitation task design.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558734"}, {"primary_key": "1769757", "vector": [], "sparse_vector": [], "title": "X-Bridges: Designing Tunable Bridges to Enrich 3D Printed Objects&apos; Deformation and Stiffness.", "authors": ["<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "Junzhe Ji", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bridges are unique structures appeared in fused deposition modeling (FDM) that make rigid prints flexible but not fully explored. This paper presents X-Bridges, an end-to-end workflow that allows novice users to design tunable bridges that can enrich 3D printed objects' deformable and physical properties. Specifically, we firstly provide a series of deformation primitives (e.g. bend, twist, coil, compress and stretch) with three versions of stiffness (loose, elastic, stable) based on parametrized bridging experiments. Embedding the printing parameters, a design tool is developed to modify the imported 3D model, evaluate optimized printing parameters for bridges, preview shape-changing process, and generate the G-code file for 3D printing. Finally, we demonstrate the design space of X-Bridges through a set of applications that enable foldable, resilient, and interactive shape-changing objects.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545710"}, {"primary_key": "1769758", "vector": [], "sparse_vector": [], "title": "Look over there! Investigating Saliency Modulation for Visual Guidance with Augmented Reality Glasses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Augmented Reality has traditionally been used to display digital overlays in real environments. Many AR applications such as remote collaboration, picking tasks, or navigation require highlighting physical objects for selection or guidance. These highlights use graphical cues such as outlines and arrows. Whilst effective, they greatly contribute to visual clutter, possibly occlude scene elements, and can be problematic for long-term use. Substituting those overlays, we explore saliency modulation to accentuate objects in the real environment to guide the user's gaze. Instead of manipulating video streams, like done in perception and cognition research, we investigate saliency modulation of the real world using optical-see-through head-mounted displays. This is a new challenge, since we do not have full control over the view of the real environment. In this work we provide our specific solution to this challenge, including built prototypes and their evaluation.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545633"}, {"primary_key": "1769759", "vector": [], "sparse_vector": [], "title": "Extail: a Kinetic Inconspicuous Wareable Hair Extension Device.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearable devices that present the wearer's information have been designed to stand out when worn, making it difficult to conceal their wear. Therefore, we have been working on developing and evaluating a dynamic expression of hair to realize the presentation of the wearer's information in an inconspicuous wearable device. In the precedents of hair interaction, the hairstyles and expressions to which the technique can be applied are limited. In this paper, we focus on the output mechanism and present Extail, a hair extension type device with a control mechanism that moves bundled hair like a tail. The results of a questionnaire survey on the correspondence between the movement of hair bundles and emotional expression were generally consistent with the results of the evaluation of tail devices in related studies.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558648"}, {"primary_key": "1769760", "vector": [], "sparse_vector": [], "title": "A bonding technique for electric circuit prototyping using conductive transfer foil and soldering iron.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Several electric circuit prototyping techniques have been proposed. While most focus on creating the conductive traces, we focus on the bonding technique needed for this kind of circuit. Our technique is an extension of existing work in that we use the traces themselves as the bonding material for the components. The bonding process is not soldering but yields joints of adequate connectivity. A hot soldering iron is used to activate the traces and bond the component to the circuit. Simple circuits are created on MDF, paper, and acrylic board to show the feasibility of the technique. It is also confirmed that the resistance of the contact points is sufficiently low.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558670"}, {"primary_key": "1769761", "vector": [], "sparse_vector": [], "title": "LUNAChair: Remote Wheelchair System that Links Up a Remote Caregiver and Wheelchair Surroundings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce LUNAChair, a remote control and communication system that uses omnidirectional video to connect a remote caregiver to a wheelchair user and a third person around the wheelchair. With the recent growing need for wheelchairs, much of the wheelchair research has focused on wheelchair control, such as fully automatic driving and remote operation. For wheelchair users, conversations with caregivers and third persons around them are also important. Therefore, we propose a system that connects a wheelchair user and a remote caregiver using omnidirectional cameras, which allows the remote caregiver to control the wheelchair while observing both the wheelchair user and his/her surroundings. Moreover, the system facilitates communication by using gaze and hand pointing estimation from an omnidirectional video.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558729"}, {"primary_key": "1769762", "vector": [], "sparse_vector": [], "title": "Designing a Hairy Haptic Display using 3D Printed Hairs and Perforated Plates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Haptic displays that can convey various material sensations and physical properties on conventional 2D displays or in virtual reality (VR) environments, have been widely explored in the field of human-computer interaction (HCI). We introduce a fabrication technique of haptic apparatus using 3D printed hairs, which can stimulate the users' sensory perception with hair-like bristles mimicking furry animals. Design parameters that determine 3D printed hair's properties such as length, density, and direction, can affect the stiffness and roughness of the contact area between the hair tip and the user's sensory receptors on their skin, thus changing stimulation patterns. To further explore the expressivity of this apparatus, we present a haptic display built with controlling mechanisms. The device is constructed by threading many 3D printed hairs through a perforated plate, manipulating the length and direction of hairs via the connected inner actuator. We present the design specifications including printing parameters, assembly, and electronics through a demonstration of prototypes, and future works.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558655"}, {"primary_key": "1769763", "vector": [], "sparse_vector": [], "title": "Involuntary Exhalation Control by Facial Vibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Breathing affects physical and mental health as well as skills in playing sports and musical instruments. Previous studies proposed various methods using sensory stimuli to assist users with controlling their breathing voluntarily by paying attention to it. However, focusing on breathing is difficult when they play sports or instruments because there are many factors to focus on. Therefore, we propose a wearable system that can control the user's exhalation involuntarily by facial vibration; pushing air from the cheeks independent of the user's voluntary breathing. Our system can control the exhaled air velocity and duration by changing the frequency, amplitude, and duration of the facial vibration. We consider our system will help novices acquire advanced skills in playing wind instruments like circular breathing.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558693"}, {"primary_key": "1769764", "vector": [], "sparse_vector": [], "title": "Integrating Real-World Distractions into Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the proliferation of consumer-level virtual reality (VR) devices, users started experiencing VR in less controlled environments, such as in social gatherings and public areas. While the current VR hardware provides an increasingly immersive experience, it ignores stimuli originating from the physical surroundings that distract users from the VR experience. To block distractions from the outside world, many users wear noise-canceling headphones. However, this is insufficient to block loud or transient sounds (e.g., drilling or hammering) and, especially, multi-modal distractions (e.g., air drafts, temperature shifts from an A/C, construction vibrations, or food smells). To tackle this, we explore a new concept, where we directly integrate the distracting stimuli from the user's physical surroundings into their virtual reality experience to enhance presence. Using our approach, an otherwise distracting wind gust can be directly mapped to the sway of trees in a VR experience that already contains trees. Using our novel approach, we demonstrate how to integrate a range of distractive stimuli into the VR experience, such as haptics (temperature, vibrations, touch), sounds, and smells. To validate our approach, we conducted three user studies and a technical evaluation. First, to validate our key principle, we conducted a controlled study where participants were exposed to distractions while playing a VR game. We found that our approach improved users' sense of presence, compared to wearing noise-canceling headphones. From these results, we engineered a sensing module that detects a set of simple distractive signals (e.g., sounds, winds, and temperature shifts). We validated our hardware in a technical evaluation and in an out-of-lab study where participants played VR games in an uncontrolled environment. Moreover, to gather the perspective of VR content creators that might one day utilize a system inspired by our findings, we invited game designers to use our approach and collected their feedback and VR designs. Finally, we present design considerations for mapping distracting external stimuli and discuss ethical considerations of integrating real-world stimuli into virtual reality.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545682"}, {"primary_key": "1769765", "vector": [], "sparse_vector": [], "title": "Prolonging VR Haptic Experiences by Harvesting Kinetic Energy from the User.", "authors": ["Shan-<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new technical approach to implement untethered VR haptic devices that contain no battery, yet can render on-demand haptic feedback. The key is that via our approach, a haptic device charges itself by harvesting the user's kinetic energy (i.e., movement)—even without the user needing to realize this. This is achieved by integrating the energy-harvesting with the virtual experience, in a responsive manner. Whenever our batteryless haptic device is about to lose power, it switches to harvesting mode (by engaging its clutch to a generator) and, simultaneously, the VR headset renders an alternative version of the current experience that depicts resistive forces (e.g., rowing a boat in VR). As a result, the user feels realistic haptics that corresponds to what they should be feeling in VR, while unknowingly charging the device via their movements. Once the haptic device's supercapacitors are charged, they wake up its microcontroller to communicate with the VR headset. The VR experience can now use the recently harvested power for on-demand haptics, including vibration, electrical or mechanical force-feedback; this process can be repeated, ad infinitum. We instantiated a version of our concept by implementing an exoskeleton (with vibration, electrical & mechanical force-feedback) that harvests the user's arm movements. We validated it via a user study, in which participants, even without knowing the device was harvesting, rated its' VR experience as more realistic & engaging than with a baseline VR setup. Finally, we believe our approach enables haptics for prolonged uses, especially useful in untethered VR setups, since devices capable of haptic feedback are traditionally only reserved for situations with ample power. Instead, with our approach, a user who engages in hours-long VR and grew accustomed to finding a battery-dead haptic device that no longer works, will simply resurrect the haptic device with their movement.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545635"}, {"primary_key": "1769766", "vector": [], "sparse_vector": [], "title": "Garnish into Thin Air.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose Garnish into Thin Air, dynamic and three-dimensional food presentation with acoustic levitation. In contrast to traditional plating on the dishes, we make the whole garnishing process an interactive experience to stimulate users' appetite by leveraging acoustic levitation's capacity to decorate edibles dynamically in mid-air. To achieve Garnish into Thin Air, our system is built to orchestrate a range of edible materials, such as flavored droplets, edible beads, and rice paper cutouts. We demonstrate Garnish into Thin Air with two examples, including a glass of cocktail named \"The Floral Party\" and a plate of dessert called \"The Winter Twig\".", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561351"}, {"primary_key": "1769767", "vector": [], "sparse_vector": [], "title": "Designing Tools for Autodidactic Learning of Skills.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the last decade, HCI researchers have designed and engineered several systems to lower the entry barrier for beginners and support novices in learning hands-on creative skills, such as motor skills, fabrication, circuit prototyping, and design.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558526"}, {"primary_key": "1769768", "vector": [], "sparse_vector": [], "title": "The Reflective Maker: Using Reflection to Support Skill-learning in Makerspaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, while HCI researchers have developed several systems that leverage the use of reflection for skill learning, the use of reflection-based learning of maker skills remains unexplored. We present ReflectiveMaker - a toolkit for experts and educators to design reflection exercises for novice learners in makerspaces. We describe the three components of our toolkit: (a) a designer interface to author the reflection prompts during fabrication activities, (b) a set of fabrication tools to sense the user's activities and (c) a reflection diary interface to record the user's reflections and analyze data on their learning progress. We then outline future work and envision a range of application scenarios.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558716"}, {"primary_key": "1769769", "vector": [], "sparse_vector": [], "title": "Demonstrating Dynamic Toolchains for Machine Control.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Humans are increasingly able to work side-by-side with desktop-scale digital fabrication machines. However, much of the software for controlling these machines does not support live, interactive exploration of their capabilities. We present Dynamic Toolchains, an extensible development framework for building parametric machine control interfaces from reusable modules. Toolchains are built and run in a live environment, removing the repetitive import and export bottleneck between software programs. This enables humans to easily explore how they can use machine precision to manipulate physical materials and achieve unique aesthetic outcomes. In this demonstration, we build a toolchain for computer-controlled watercolor painting and show how it facilitates rapid iteration on brush stroke patterns.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558662"}, {"primary_key": "1769770", "vector": [], "sparse_vector": [], "title": "ConfusionLens: Dynamic and Interactive Visualization for Performance Analysis of Multiclass Image Classifiers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Building higher-quality image classification models requires better performance analysis (PA) methods to help understand their behaviors. We propose ConfusionLens, a dynamic and interactive visualization interface that augments a conventional confusion matrix with focus+context visualization. This interface makes it possible to adaptively provide relevant information for different kinds of PA tasks. Specifically, it allows users to seamlessly switch table layouts among three views (overall view, class-level view, and between-class view) while observing all of the instance images in a single screen. This paper presents a ConfusionLens prototype that supports hundreds of instances and its several extensions to further support practical PA tasks, such as activation map visualization and instance sorting/filtering.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558631"}, {"primary_key": "1769771", "vector": [], "sparse_vector": [], "title": "Interactive Public Displays and Wheelchair Users: Between Direct, Personal and Indirect, Assisted Interaction.", "authors": ["Radu<PERSON><PERSON>", "<PERSON>vid<PERSON><PERSON><PERSON><PERSON><PERSON>", "Laura<PERSON><PERSON>"], "summary": "We examine accessible interactions for wheelchair users and public displays with three studies. In a first study, we conduct a Systematic Literature Review, from which we report very few scientific papers on this topic and a preponderant focus on touch input. In a second study, we conduct a Systematic Video Review using YouTube as a data source, and unveil accessibility challenges for public displays and several input modalities alternative to direct touch. In a third study, we conduct semi-structured interviews with eleven wheelchair users to understand their experience interacting with public displays and to collect their preferences for more accessible input modalities. Based on our findings, we propose the \"assisted interaction\" phase to extend <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>'s four-phase interaction model with public displays, and the \"ability\" dimension for cross-device interaction design to support, via users' personal mobile devices, independent use of interactive public displays.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545662"}, {"primary_key": "1769772", "vector": [], "sparse_vector": [], "title": "Computational Design of Active Kinesthetic Garments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garments with the ability to provide kinesthetic force-feedback on-demand can augment human capabilities in a non-obtrusive way, enabling numerous applications in VR haptics, motion assistance, and robotic control. However, designing such garments is a complex, and often manual task, particularly when the goal is to resist multiple motions with a single design. In this work, we propose a computational pipeline for designing connecting structures between active components - one of the central challenges in this context. We focus on electrostatic (ES) clutches that are compliant in their passive state while strongly resisting elongation when activated. Our method automatically computes optimized connecting structures that efficiently resist a range of pre-defined body motions on demand. We propose a novel dual-objective optimization approach to simultaneously maximize the resistance to motion when clutches are active, while minimizing resistance when inactive. We demonstrate our method on a set of problems involving different body sites and a range of motions. We further fabricate and evaluate a subset of our automatically created designs against manually created baselines using mechanical testing and in a VR pointing study.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545674"}, {"primary_key": "1769773", "vector": [], "sparse_vector": [], "title": "RealityLens: A User Interface for Blending Customized Physical World View into Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Research has enabled virtual reality (VR) users to interact with the physical world by blending the physical world view into the virtual environment. However, current solutions are designed for specific use cases and hence are not capable of covering users' varying needs for accessing information about the physical world. This work presents RealityLens, a user interface that allows users to peep into the physical world in VR with the reality lenses they deployed for their needs. For this purpose, we first conducted a preliminary study with experienced VR users to identify users' needs for interacting with the physical world, which led to a set of features for customizing the scale, placement, and activation method of a reality lens. We evaluated the design in a user study (n=12) and collected the feedback of participants engaged in two VR applications while encountering a range of interventions from the physical world. The results show that users' VR presence tends to be better preserved when interacting with the physical world with the support of the RealityLens interface.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545686"}, {"primary_key": "1769774", "vector": [], "sparse_vector": [], "title": "UltraBat: An Interactive 3D Side-Scrolling Game using Ultrasound Levitation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present UltraBat, an interactive 3D side-scrolling game inspired by <PERSON><PERSON><PERSON>, in which the game character, a bat, is physically levitated in mid-air using ultrasound. Players aim to navigate the bat through a stalagmite tunnel that scrolls to one side as the bat travels, which is implemented using a pin-array display to create a shape-changing passage.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3561344"}, {"primary_key": "1769775", "vector": [], "sparse_vector": [], "title": "Echofluid: An Interface for Remote Choreography Learning and Co-creation Using Machine Learning Techniques.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>on"], "summary": "Born from physical activities, dance carries beyond mere body movement. Choreographers interact with audiences' perceptions through the kinaesthetics, creativity, and expressivity of whole-body performance, inviting them to construct experience, emotion, culture, and meaning together. Computational choreography support can bring endless possibilities into this one of the most experiential and creative artistic forms. While various interactive and motion technologies have been developed and adopted to support creative choreographic processes, little work has been done in exploring incorporating machine learning in a choreographic system, and few remote dance teaching systems in particular have been suggested. In this exploratory work, we proposed Echofluid-a novel AI-based choreographic learning and support system that allows student dancers to compose their own AI models for learning, evaluation, exploration, and creation. In this poster, we present the design, development and ongoing validation process of Echofluid, and discuss the possibilities of applying machine learning in collaborative art and dance as well as the opportunities of augmenting interactive experiences between the performers and audiences with emerging technologies.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558708"}, {"primary_key": "1769776", "vector": [], "sparse_vector": [], "title": "Record Once, Post Everywhere: Automatic Shortening of Audio Stories for Social Media.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ham J. Mysore"], "summary": "Following the prevalence of short-form video, short-form voice content has emerged on social media platforms like Twitter and Facebook. A challenge that creators face is hard constraints on the content length. If the initial recording is not short enough, they need to re-record or edit their content. Both are time-consuming, and the latter, if supported, can have a learning curve. Moreover, creators need to manually create multiple versions to publish content on platforms with different length constraints. To simplify this process, we present ROPE1 (Record Once, Post Everywhere). Creators can record voice content once, and our system will automatically shorten it to all length limits by removing parts of the recording for each target. We formulate this as a combinatorial optimization problem and propose a novel algorithm that automatically selects optimal sentence combinations from the original content to comply with each length constraint. Creators can customize the algorithmically shortened content by specifying sentences to include or exclude. Our system can also use the user-specified constraints to recompute and provides a new version. We conducted a user study comparing ROPE with a sentence-based manual editing baseline. The results show that ROPE can generate high-quality edits, alleviating the cognitive loads of creators for shortening content. While our system and user study address short-form voice content specifically, we believe that the same concept can also be applied to other media such as video with narration and dialog.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545680"}, {"primary_key": "1769777", "vector": [], "sparse_vector": [], "title": "TaskScape: Fostering Holistic View on To-do List With Tracking Plan and Emotion.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Despite advancements with intelligence and connectivity in the workspace, productivity tools, such as to-do list applications, still, measure workers' performance by a binary state—completed, yet completed, and thus the number of tasks completed. Such quantitative measurements can often overlook human values and individual well-being. While concepts such as positive computing and digital well-being are on the rise in the HCI community, few systems have been proposed to effectively integrate holistic considerations for mental and emotional well-being into productivity tools. In this work, we depart from the classic task list management tool and explore the construction of well-being-centered to-do list software. We propose a task management system–TaskScape—, which allow users to have awareness on the following two aspects: (1) how they plan and complete tasks and (2) how they feel towards their work. With the proposed system, we will investigate if having holistic view on their tasks can facilitate reflection on what they work on, how they stick to their plans, and how their tasks portfolio support their emotional well-being, nudging users to reflect upon their work, planning performance, and their emotional values towards their work. In this poster, we share the design, development, and ongoing validation progress of TaskScape, which is aimed to nudge workers to holistically view work productivity, reminding users that work is more than just work but life.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558720"}, {"primary_key": "1769778", "vector": [], "sparse_vector": [], "title": "iThem: Programming Internet of Things Beyond Trigger-Action Pattern.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With emerging technologies bringing Internet of Things (IoT) devices into domestic environments, trigger-action programming such as IFTTT with its simple if-this-then-that pattern provides an effective way for end-users to connect fragmented intelligent services and program their own smart home/work space automation. While the simplicity of trigger-action programming can be effective for non-programmers with its straightforward concepts and graphical user interface, it does not allow the algorithmic expressivity that a programming language has. For instance, the simple if-this-then-that structure cannot cover complex algorithms that arise from real world scenarios involving multiple conditions or keeping track of a sequence of conditions (e.g., incrementing counters, triggering one action if two conditions are both true). In this exploratory work, we take an alternative approach by creating a programmable channel between application programming interfaces (APIs), which allows programmers to preserve states and to use them to write complex algorithms. We propose iThem, which stands for intelligence of them—internet of things, that allow programmers to author any complex algorithms that can connect different IoT services and fully unleash the freedom of a general programming language. In this poster, we share the design, development, and ongoing validation progress of iThem, which piggybacks on existing programmable IoT system IFTTT, and which allows for a programmable channel that connects triggers and actions in IFTTT with versatility.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558776"}, {"primary_key": "1769779", "vector": [], "sparse_vector": [], "title": "Little Garden: An augmented reality game for older adults to promote body movement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongning Shi", "Yaqing Chai", "<PERSON><PERSON><PERSON>"], "summary": "Physical activity is one of the most effective ways to help older adults stay healthy, but traditional training methods for older adults use single tasks and are boring, often making it difficult for the elderly to achieve good exercise results. In contrast to existing digital games, games based on augmented reality technology have the potential to promote physical activity in the elderly. This paper presents Little Garden, an interactive augmented-reality game designed for older adults. It uses projective augmented reality technology, physical card manipulation, virtual social scenarios to increase user engagement and motor initiation. The pilot data show that the game system promotes physical engagement and provides a good user experience. We believe that augmented reality technology provides a new approach to interface design for age-appropriate user-interface experiences.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558731"}, {"primary_key": "1769780", "vector": [], "sparse_vector": [], "title": "VRhook: A Data Collection Tool for VR Motion Sickness Research.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sur<PERSON>"], "summary": "Despite the increasing popularity of VR games, one factor hindering the industry's rapid growth is motion sickness experienced by the users. Symptoms such as fatigue and nausea severely hamper the user experience. Machine Learning methods could be used to automatically detect motion sickness in VR experiences, but generating the extensive labeled dataset needed is a challenging task. It needs either very time consuming manual labeling by human experts or modification of proprietary VR application source codes for label capturing. To overcome these challenges, we developed a novel data collection tool, VRhook, which can collect data from any VR game without needing access to its source code. This is achieved by dynamic hooking, where we can inject custom code into a game's run-time memory to record each video frame and its associated transformation matrices. Using this, we can automatically extract various useful labels such as rotation, speed, and acceleration. In addition, VRhook can blend a customized screen overlay on top of game contents to collect self-reported comfort scores. In this paper, we describe the technical development of VRhook, demonstrate its utility with an example, and describe directions for future research.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545656"}, {"primary_key": "1769781", "vector": [], "sparse_vector": [], "title": "iWood: Makeable Vibration Sensor for Interactive Plywood.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "iWood is interactive plywood that can sense vibration based on triboelectric effect. As a material, iWood survives common woodworking operations, such as sawing, screwing, and nailing and can be used to create furniture and artifacts. Things created using iWood inherit its sensing capability and can detect a variety of user input and activities based on their unique vibration patterns. Through a series of experiments and machine simulations, we carefully chose the size of the sensor electrodes, the type of triboelectric materials, and the bonding method of the sensor layers to optimize the sensitivity and fabrication complexity. The sensing performance of iWood was evaluated with 4 gestures and 12 daily activities carried out on a table, nightstand, and cutting board, all created using iWood. Our result suggested over 90% accuracies for activity and gesture recognition.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545640"}, {"primary_key": "1769782", "vector": [], "sparse_vector": [], "title": "iMarker: Instant and True-to-scale AR with Invisible Markers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Augmented Reality (AR) has been widely used in modern mobile devices for various applications. To achieve a stable and precise AR experience, mobile devices are equipped with various sensors (e.g., dual camera, LiDAR) to increase the robustness of camera tracking. Those sensors largely increased the cost of mobile devices and are usually not available on low-cost devices. We propose a novel AR system that leverage the advance of marker-based camera tracking to produce fast and true-to-scale AR rendering on any device with a single camera. Our method enables the computer monitor to be the host of AR markers, without taking up valuable screen space nor impacting the user experience. Unlike traditional marker-based methods, we utilize the difference between human vision and camera system, making AR markers to be invisible to human vision. We propose an efficient algorithm that allows the mobile device to detect those markers accurately and later recover the camera pose for AR rendering. Since the markers are invisible to human vision, we can embed them on any website and the user will not notice the existence of these markers. We also conduct extensive experiments that evaluate the efficacy of our method. The experimental results show that our method is faster and has a more accurate scale of the virtual objects compared to the state-of-the-art AR solution.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558721"}, {"primary_key": "1769783", "vector": [], "sparse_vector": [], "title": "Towards Future Health and Well-being: Bridging Behavior Modeling and Intervention.", "authors": ["<PERSON><PERSON>"], "summary": "With the advent of always-available, ubiquitous devices with powerful passive sensing and active interaction capabilities, the opportunities to integrate AI into this ecosystem have matured, providing an unprecedented opportunity to understand and support user well-being. A wide array of research has demonstrated the potential to detect risky behaviors and address health concerns, using human-centered ML to understand longitudinal, passive behavior logs. Unfortunately, it is difficult to translate these findings into deployable applications without better approaches to providing human-understandable relationship explanations between behavior features and predictions; and generalizing to new users and new time periods. My past work has made significant headway in addressing modeling accuracy, interpretability, and robustness. Moreover, my ultimate goal is to build deployable, intelligent interventions for health and well-being that make use of succeeding ML-based behavior models. I believe that just-in-time interventions are particularly well suited to ML support. I plan to test the value of ML for providing users with a better, interpretable, and robust experience in supporting their well-being.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558524"}, {"primary_key": "1769784", "vector": [], "sparse_vector": [], "title": "Phrase-Gesture Typing on Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "Yankang <PERSON>g", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study phrase-gesture typing, a gesture typing method that allows users to type short phrases by swiping through all the letters of the words in a phrase using a single, continuous gesture. Unlike word-gesture typing, where text needs to be entered word by word, phrase-gesture typing enters text phrase by phrase. To demonstrate the usability of phrase-gesture typing, we implemented a prototype called PhraseSwipe. Our system is composed of a frontend interface designed specifically for typing through phrases and a backend phrase-level gesture decoder developed based on a transformer-based neural language model. Our decoder was trained using five million phrases of varying lengths of up to five words, chosen randomly from the Yelp Review Dataset. Through a user study with 12 participants, we demonstrate that participants could type using PhraseSwipe at an average speed of 34.5 WPM with a Word Error Rate of 1.1%.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545683"}, {"primary_key": "1769785", "vector": [], "sparse_vector": [], "title": "Sharing Heartbeat: Toward Conducting Heartrate and Speech Rhythm through Tactile Presentation of Pseudo-heartbeats.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Currently, the ongoing COVID-19 pandemic makes physical contact, such as handshakes, difficult. However, physical contact is effective in strengthening the bonds between people. In this study, we aim to compensate for the physical contact lost during the COVID-19 pandemic by presenting a pseudo-heartbeat through a speaker to reproduce entrainment and the synchronized state of heartbeats induced by physiological synchronization. We evaluated the effects of the device in terms of speech rhythm and heart rate. The experimental results showed that a presentation of 80 BPM significantly reduced the difference in heart rate between the two participants, bringing them closer to a synchronized heart rate state. The heart rates of participants were significantly lower when 45 BPM and 80 BPM were presented than when no stimulus was given. Furthermore, when 45 BPM was presented, the silent periods between conversations were significantly more extended than when no stimulus was given. This result indicates that this device can intentionally create the entrainment phenomenon and a synchronized heart rate state, thereby producing the same effect of physical contact communication without contact.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558704"}, {"primary_key": "1769786", "vector": [], "sparse_vector": [], "title": "M&amp;M: Molding and Melting Method Using a Replica Diffraction Grating Film and a Laser for Decorating Chocolate with Structural Color.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Chocolate is a great food loved around the world. Methods to decorate chocolate with patterns drawn in structural colors have been developed; however, the methods which requires precision molds with nanoscale processing takes a lot of cost and time. In this paper, I propose a new method to decorate chocolate with structural color using a laser engraving machine and a replica diffraction grating film. The proposed method is composed of two simple steps: 1) molding chocolate on a replica diffraction grating film and 2) melting chocolate with structural color with a laser to draw a design. The proposed method allows creation of chocolates decorated with structural color with only a simple manufacturing process and inexpensive equipment without special precision molds.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558642"}, {"primary_key": "1769787", "vector": [], "sparse_vector": [], "title": "Photographic Lighting Design with Photographer-in-the-Loop Bayesian Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is important for photographers to have the best possible lighting configuration at the time of shooting; otherwise, they need post-processing on images, which may cause artifacts and deterioration. Thus, photographers often struggle to find the best possible lighting configuration by manipulating lighting devices, including light sources and modifiers, in a trial-and-error manner. In this paper, we propose a novel computational framework to support photographers. This framework assumes that every lighting device is programmable; that is, its adjustable parameters (e.g., orientation, intensity, and color temperature) can be set using a program. Using our framework, photographers do not need to learn how the parameter values affect the resulting lighting, and even do not need to determine the strategy of the trial-and-error process; instead, photographers need only concentrate on evaluating which lighting configuration is more desirable among options suggested by the system. The framework is enabled by our novel photographer-in-the-loop Bayesian optimization, which is sample-efficient (i.e., the number of required evaluation steps is small) and which can also be guided by providing a rough painting of the desired lighting configuration if any. We demonstrate how the framework works in both simulated virtual environments and a physical environment, suggesting that it could find pleasing lighting configurations quickly in around 10 iterations. Our user study suggests that the framework enables the photographer to concentrate on the look of captured images rather than the parameters, compared with the traditional manual lighting workflow.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545690"}, {"primary_key": "1769788", "vector": [], "sparse_vector": [], "title": "Concept-Annotated Examples for Library Comparison.", "authors": ["Li<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Programmers often rely on online resources—such as code examples, documentation, blogs, and Q&A forums—to compare similar libraries and select the one most suitable for their own tasks and contexts. However, this comparison task is often done in an ad-hoc manner, which may result in suboptimal choices. Inspired by Analogical Learning and Variation Theory, we hypothesize that rendering many concept-annotated code examples from different libraries side-by-side can help programmers (1) develop a more comprehensive understanding of the libraries' similarities and distinctions and (2) make more robust, appropriate library selections. We designed a novel interactive interface, ParaLib, and used it as a technical probe to explore to what extent many side-by-side concepted-annotated examples can facilitate the library comparison and selection process. A within-subjects user study with 20 programmers shows that, when using ParaLib, participants made more consistent, suitable library selections and provided more comprehensive summaries of libraries' similarities and differences.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545647"}, {"primary_key": "1769789", "vector": [], "sparse_vector": [], "title": "ReCapture: AR-Guided Time-lapse Photography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present ReCapture, a system that leverages AR-based guidance to help users capture time-lapse data with hand-held mobile devices. ReCapture works by repeatedly guiding users back to the precise location of previously captured images so they can record time-lapse videos one frame at a time without leaving their camera in the scene. Building on previous work in computational re-photography, we combine three different guidance modes to enable parallel hand-held time-lapse capture in general settings. We demonstrate the versatility of our system on a wide variety of subjects and scenes captured over a year of development and regular use, and explore different visualizations of unstructured hand-held time-lapse data.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545641"}, {"primary_key": "1769790", "vector": [], "sparse_vector": [], "title": "Fibercuit: Prototyping High-Resolution Flexible and Kirigami Circuits with a Fiber Laser Engraver.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Prototyping compact devices with unique form factors often requires the PCB manufacturing process to be outsourced, which can be expensive and time-consuming. In this paper, we present Fibercuit, a set of rapid prototyping techniques to fabricate high-resolution, flexible circuits on-demand using a fiber laser engraver. We showcase techniques that can laser cut copper-based composites to form fine-pitch conductive traces, laser fold copper substrates that can form kirigami structures, and laser solder surface-mount electrical components using off-the-shelf soldering pastes. Combined with our software pipeline, an end user can design and fabricate flexible circuits which are dual-layer and three-dimensional, thereby exhibiting a wide range of form factors. We demonstrate Fibercuit by showcasing a set of examples, including a custom dice, flex cables, custom end-stop switches, electromagnetic coils, LED earrings and a circuit in the form of kirigami crane.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545652"}, {"primary_key": "1769791", "vector": [], "sparse_vector": [], "title": "SpiceWare: Simulating Spice Using Thermally Adjustable Dinnerware to Bridge Cultural Gaps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Preference and tolerance towards spicy food may vary depending on culture, location, upbringing, personality and even gender. Due to this, spicy food can often effect the social interaction on the dining table, especially if it is presented as a cultural dish. We propose SpiceWare, a thermally adjustable spoon that alters the perception of spice to improve cross-cultural communication. SpiceWare is a 3D-printed aluminium spoon that houses a thermal peltier that provides thermal feedback up to 45°C which can alter the taste perception of the user. As an initial evaluation, we conducted a workshop among participants of varying cultural backgrounds and observe their interaction when dining on spicy food. We found that the overall interaction was perceived to be more harmonious, and we discuss potential future works on improving the system.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558701"}, {"primary_key": "1769792", "vector": [], "sparse_vector": [], "title": "Reconfigurable Elastic Metamaterials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a novel design for materials that are reconfigurable by end-users. Conceptually, we propose decomposing such reconfigurable materials into (1) a generic, complex material consisting of engineered microstructures (known as metamaterials) designed to be purchased and (2) a simple configuration geometry that can be fabricated by end-users to fit their individual use cases. Specifically, in this paper we investigate reconfiguring our material's elasticity, such that it can cover existing objects and thereby augment their material properties. Users can configure their materials by generating the configuration geometry using our interactive editor, 3D printing it using commonly available filaments (e. g., PLA), and pressing it onto the generic material for local coupling. We characterize the mechanical properties of our reconfigurable elastic metamaterial and showcase the material's applicability as, e.g., augmentation for haptic props in virtual reality, a reconfigurable shoe sole for different activities, or a battleship-like ball game.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545649"}, {"primary_key": "1769793", "vector": [], "sparse_vector": [], "title": "MagneShape: A Non-electrical Pin-Based Shape-Changing Display.", "authors": ["<PERSON><PERSON>"], "summary": "Pin-based shape-changing displays can present dynamic shape changes by actuating a number of pins. However, the use of many linear actuators to achieve this makes the electrical structure and mechanical construction of the display complicated. We propose a simple pin-based shape-changing display that outputs shape and motions without any electronic elements. Our display consists of magnetic pins in a pin housing, with a magnetic sheet underneath it. The magnetic sheet has a specific magnetic pattern on its surface, and each magnetic pin has a magnet at its lower end. The repulsive force generated between the magnetic sheet and the magnetic pin levitates the pin vertically, and the height of the pin-top varies depending on the magnetic pattern. This paper introduces the basic structure of the display and compares several fabrication methods for the magnetic pins, to highlight the applicability of this method. We have also demonstrated some applications and discussed future possibilities.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545645"}, {"primary_key": "1769794", "vector": [], "sparse_vector": [], "title": "Demonstrating Finger-Based Dexterous Phone Gestures.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This hands-on demonstration enables participants to experience single-handed \"dexterous gestures\", a novel approach for the physical manipulation of a phone using the fine motor skills of fingers. A recognizer is developed for variations of \"full\" and \"half\" gestures that spin (yaw axis), rotate (roll axis), and flip (pitch axis), all detected using the built-in phone IMU sensor. A functional prototype demonstrates how recognized dexterous gestures can be used to interact with a variety of current smartphone applications.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558645"}, {"primary_key": "1769795", "vector": [], "sparse_vector": [], "title": "Flexel: A Modular Floor Interface for Room-Scale Tactile Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human environments are physically supported by floors, which prevent people and furniture from gravitational pull. Since our body motions continuously generate vibrations and loads that propagate into the ground, measurement of these expressive signals leads to unobtrusive activity sensing. In this study, we present Flexel, a modular floor interface for room-scale tactile sensing. By paving a room with floor interfaces, our system can immediately begin to infer touch locations, track user locations, recognize foot gestures, and detect object locations. Through a series of exploratory studies, we determined the preferable hardware design that adheres to construction conventions, as well as the optimal sensor density that mediates the trade-off between cost and performance. We summarize our findings into design guidelines that are generalizable to other floor interfaces. Finally, we provide example applications for room-scale tactile sensing enabled by our Flexel system.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545699"}, {"primary_key": "1769796", "vector": [], "sparse_vector": [], "title": "Optimizing the Timing of Intelligent Suggestion in Virtual Reality.", "authors": ["<PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intelligent suggestion techniques can enable low-friction selection-based input within virtual or augmented reality (VR/AR) systems. Such techniques leverage probability estimates from a target prediction model to provide users with an easy-to-use method to select the most probable target in an environment. For example, a system could highlight the predicted target and enable a user to select it with a simple click. However, as the probability estimates can be made at any time, it is unclear when an intelligent suggestion should be presented. Earlier suggestions could save a user time and effort but be less accurate. Later suggestions, on the other hand, could be more accurate but save less time and effort. This paper thus proposes a computational framework that can be used to determine the optimal timing of intelligent suggestions based on user-centric costs and benefits. A series of studies demonstrated the value of the framework for minimizing task completion time and maximizing suggestion usage and showed that it was both theoretically and empirically effective at determining the optimal timing for intelligent suggestions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545632"}, {"primary_key": "1769797", "vector": [], "sparse_vector": [], "title": "Silent subwoofer system using myoelectric stimulation to presents the acoustic deep bass experiences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study demonstrates a portable, low-noise system that utilizes electrical muscle stimulation (EMS) to present a body-sensory acoustic experience similar to that experienced during live concerts. Twenty-four participants wore head-mounted displays (HMDs), headphones, and the proposed system and experienced a live concert in a virtual reality (VR) space to evaluate the system. We found that the system was not inferior to a system with loudspeakers and subwoofers, where ambient noise concerns precision in rhythm and harmony. These results could be explained by the user perceiving the EMS experience as a single signal when the EMS stimulation is presented in conjunction with visual and acoustic stimuli (e.g., the kicking of a bass drum, the bass sound generated from the kicking, and the acoustic sensation caused by the bass sound). The proposed method offers a novel EMS-based body-sensory acoustic experience, and the results of this study may lead to an improved experience not only for live concerts in VR space but also for everyday music listening.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558653"}, {"primary_key": "1769798", "vector": [], "sparse_vector": [], "title": "Demonstration of Lenticular Objects: 3D Printed Objects with Lenticular Lens Surfaces That Can Change their Appearance Depending on the Viewpoint.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present Lenticular Objects, which are 3D objects that appear differently from different viewpoints. We accomplish this by 3D printing lenticular lenses across the curved surface of objects and computing underlying surface color patterns, which enables to generate different appearances to the user at each viewpoint.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558628"}, {"primary_key": "1769799", "vector": [], "sparse_vector": [], "title": "Seeing our Blind Spots: Smart Glasses-based Simulation to Increase Design Students&apos; Awareness of Visual Impairment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As the population ages, many will acquire visual impairments. To improve design for these users, it is essential to build awareness of their perspective during everyday routines, especially for design students.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545687"}, {"primary_key": "1769800", "vector": [], "sparse_vector": [], "title": "ELAXO : Rendering Versatile Resistive Force Feedback for Fingers Grasping and Twisting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Haptic feedback not only enhances immersion in virtual reality (VR) but also delivers experts' haptic sensation tips in VR training, e.g., properly clamping a tenon and mortise joint or tightening a screw in the assembly of VR factory training, which could even improve the training performance. However, various and complicated manipulation is in different scenarios. Although haptic feedback of virtual objects' shape, stiffness or resistive force in pressing or grasping is achieved by previous research, rotational resistive force when twisting or turning virtual objects is seldom discussed or explored, especially for a wearable device. Therefore, we propose a wearable device, ELAXO, to integrate continuous resistive force and continuous rotational resistive force with or without resilience in grasping and twisting, respectively. ELAXO is an exoskeleton with rings, mechanical brakes and elastic bands. The brakes achieve shape rendering and switch between with and without resilience modes for the resistive force. The detachable and rotatable rings and elastic bands render continuous resistive force in grasping and twisting. We conducted a just noticeable difference (JND) study to understand users' distinguishability in the four conditions, resistive force and rotational resistive force with and without resilience, separately. A VR study was then performed to verify that the versatile resistive force feedback from ELAXO enhances the VR experiences.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545677"}, {"primary_key": "1769801", "vector": [], "sparse_vector": [], "title": "RIDS: Implicit Detection of a Selection Gesture Using Hand Motion Dynamics During Freehand Pointing in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Freehand interactions with augmented and virtual reality are growing in popularity, but they lack reliability and robustness. Implicit behavior from users, such as hand or gaze movements, might provide additional signals to improve the reliability of input. In this paper, the primary goal is to improve the detection of a selection gesture in VR during point-and-click interaction. Thus, we propose and investigate the use of information contained within the hand motion dynamics that precede a selection gesture. We built two models that classified if a user is likely to perform a selection gesture at the current moment in time. We collected data during a pointing-and-selection task from 15 participants and trained two models with different architectures, i.e., a logistic regression classifier was trained using predefined hand motion features and a temporal convolutional network (TCN) classifier was trained using raw hand motion data. Leave-one-subject-out cross-validation PR-AUCs of 0.36 and 0.90 were obtained for each model respectively, demonstrating that the models performed well above chance (=0.13). The TCN model was found to improve the precision of a noisy selection gesture by 11.2% without sacrificing recall performance. An initial analysis of the generalizability of the models demonstrated above-chance performance, suggesting that this approach could be scaled to other interaction tasks in the future.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545701"}, {"primary_key": "1769802", "vector": [], "sparse_vector": [], "title": "A11yBoard: Using Multimodal Input and Output to Make Digital Artboards Accessible to Blind Users.", "authors": ["<PERSON><PERSON><PERSON> (Jerry) <PERSON>", "<PERSON>"], "summary": "We present A11yBoard, an interactive multimodal system that makes interpreting and authoring digital artboards, such as presentation slides or vector drawings, accessible to blind and low-vision (BLV) users. A11yBoard combines a web-based application with a mobile touch screen device such as a smartphone or tablet. The artboard is mirrored from the PC onto the touch screen, enabling spatial exploration of the artboard via touch and gesture. In addition, speech recognition and non-speech audio are used for input and output, respectively. Finally, keyboard input is used with a custom search-driven command line interface to access various commands and properties. These modalities combine into a rich, accessible system in which artboard contents, such as shapes, lines, text boxes, and images, can be interpreted, generated, and manipulated with ease. With A11yBoard, BLV users can not only consume accessible content, but create their own as well.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558695"}, {"primary_key": "1769803", "vector": [], "sparse_vector": [], "title": "Fringer: A Finger-Worn Passive Device Enabling Computer Vision Based Force Sensing Using Moiré Fringes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Seongkook Heo"], "summary": "Despite the importance of utilizing forces when interacting with objects, sensing force interactions without active force sensors is challenging. We introduce Fringer, a finger sleeve that physically visualizes the force to allow a camera to estimate the force without using any active sensors. The sleeve has stripe-pattern slits, a sliding paper with stripe pattern, and a compliant layer that converts force into sliding paper movements. The patterns of the slit and the paper have different frequencies to create Moiré fringes, which can magnify the small displacement caused by the compliant layer compression for webcams to easily capture such displacement.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558706"}, {"primary_key": "1769804", "vector": [], "sparse_vector": [], "title": "Bayesian Hierarchical Pointing Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Bayesian hierarchical models are probabilistic models that have hierarchical structures and use Bayesian methods for inferences. In this paper, we extend <PERSON><PERSON>' law to be a Bayesian hierarchical pointing model and compare it with the typical pooled pointing models (i.e., treating all observations as the same pool), and the individual pointing models (i.e., building an individual model for each user separately). The Bayesian hierarchical pointing models outperform pooled and individual pointing models in predicting the distribution and the mean of pointing movement time, especially when the training data are sparse. Our investigation also shows that both noninformative and weakly informative priors are adequate for modeling pointing actions, although the weakly informative prior performs slightly better than the noninformative prior when the training data size is small. Overall, we conclude that the expected advantages of Bayesian hierarchical models hold for the pointing tasks. Bayesian hierarchical modeling should be adopted a more principled and effective approach of building pointing models than the current common practices in HCI which use pooled or individual models.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545708"}, {"primary_key": "1769805", "vector": [], "sparse_vector": [], "title": "Exploiting and Guiding User Interaction in Interactive Machine Teaching.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Humans are talented with the ability to perform diverse interactions in the teaching process. However, when humans want to teach AI, existing interactive systems only allow humans to perform repetitive labeling, causing an unsatisfactory teaching experience. My Ph.D. research studies Interactive Machine Teaching (IMT), an emerging field of HCI research that aims to enhance humans' teaching experience in the AI creation process. My research builds IMT systems that exploit and guide user interaction and shows that such in-depth integration of human interaction can benefit both AI models and user experience.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558529"}, {"primary_key": "1769806", "vector": [], "sparse_vector": [], "title": "INTENT: Interactive Tensor Transformation Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "Man To Tang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a growing interest in adopting Deep Learning (DL) given its superior performance in many domains. However, modern DL frameworks such as TensorFlow often come with a steep learning curve. In this work, we propose INTENT, an interactive system that infers user intent and generates corresponding TensorFlow code on behalf of users. INTENT helps users understand and validate the semantics of generated code by rendering individual tensor transformation steps with intermediate results and element-wise data provenance. Users can further guide INTENT by marking certain TensorFlow operators as desired or undesired, or directly manipulating the generated code. A within-subjects user study with 18 participants shows that users can finish programming tasks in TensorFlow more successfully with only half the time, compared with a variant of INTENT that has no interaction or visualization support.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545653"}, {"primary_key": "1769807", "vector": [], "sparse_vector": [], "title": "Gesture-aware Interactive Machine Teaching with In-situ Object Annotations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Interactive Machine Teaching (IMT) systems allow non-experts to easily create Machine Learning (ML) models. However, existing vision-based IMT systems either ignore annotations on the objects of interest or require users to annotate in a post-hoc manner. Without the annotations on objects, the model may misinterpret the objects using unrelated features. Post-hoc annotations cause additional workload, which diminishes the usability of the overall model building process. In this paper, we develop LookHere, which integrates in-situ object annotations into vision-based IMT. LookHere exploits users' deictic gestures to segment the objects of interest in real time. This segmentation information can be additionally used for training. To achieve the reliable performance of this object segmentation, we utilize our custom dataset called HuTics, including 2040 front-facing images of deictic gestures toward various objects by 170 people. The quantitative results of our user study showed that participants were 16.3 times faster in creating a model with our system compared to a standard IMT system with a post-hoc annotation process while demonstrating comparable accuracies. Additionally, models created by our system showed a significant accuracy improvement ($\\Delta mIoU=0.466$) in segmenting the objects of interest compared to those without annotations.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545648"}, {"primary_key": "1769808", "vector": [], "sparse_vector": [], "title": "Design and Fabricate Personal Health Sensing Devices.", "authors": ["<PERSON><PERSON>"], "summary": "With the development of low-cost electronics, rapid prototyping techniques, as well as widely available mobile devices (e.g. mobile phones, smart watches), users are able to develop their own basic interactive functional applications, either on top of existing device platforms, or as stand-alone devices. However, the boundary for creating personal health sensing devices, both function prototyping and fabrication -wise, are still high. In this paper, I present my works on designing and fabricating personal health sensing devices with rapid function prototyping techniques and novel sensing technologies. Through these projects and ongoing future research, I am working towards my vision that everyone can design and fabricate highly-customized health sensing devices based on their body form and desired functions.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526114.3558528"}, {"primary_key": "1769809", "vector": [], "sparse_vector": [], "title": "MuscleRehab: Improving Unsupervised Physical Rehabilitation by Monitoring and Visualizing Muscle Engagement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Unsupervised physical rehabilitation traditionally has used motion tracking to determine correct exercise execution. However, motion tracking is not representative of the assessment of physical therapists, which focus on muscle engagement. In this paper, we investigate if monitoring and visualizing muscle engagement during unsupervised physical rehabilitation improves the execution accuracy of therapeutic exercises by showing users whether they target the right muscle groups. To accomplish this, we use wearable electrical impedance tomography (EIT) to monitor muscle engagement and visualize the current state on a virtual muscle-skeleton avatar. We use additional optical motion tracking to also monitor the user's movement. We conducted a user study with 10 participants that compares exercise execution while seeing muscle + motion data vs. motion data only, and also presented the recorded data to a group of physical therapists for post-rehabilitation analysis. The results indicate that monitoring and visualizing muscle engagement can improve both the therapeutic exercise accuracy during rehabilitation, and post-rehabilitation evaluation for physical therapists.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545705"}, {"primary_key": "1769810", "vector": [], "sparse_vector": [], "title": "MechARspace: An Authoring System Enabling Bidirectional Binding of Augmented Reality with Toys in Real-time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Augmented Reality (AR), which blends physical and virtual worlds, presents the possibility of enhancing traditional toy design. By leveraging bidirectional virtual-physical interactions between humans and the designed artifact, such AR-enhanced toys can provide more playful and interactive experiences for traditional toys. However, designers are constrained by the complexity and technical difficulties of the current AR content creation processes. We propose MechARspace, an immersive authoring system that supports users to create toy-AR interactions through direct manipulation and visual programming. Based on the elicitation study, we propose a bidirectional interaction model which maps both ways: from the toy inputs to reactions of AR content, and also from the AR content to the toy reactions. This model guides the design of our system which includes a plug-and-play hardware toolkit and an in-situ authoring interface. We present multiple use cases enabled by MechARspace to validate this interaction model. Finally, we evaluate our system with a two-session user study where users first recreated a set of predefined toy-AR interactions and then implemented their own AR-enhanced toy designs.", "published": "2022-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3526113.3545668"}]