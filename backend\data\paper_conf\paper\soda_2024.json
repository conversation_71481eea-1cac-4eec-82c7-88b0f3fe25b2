[{"primary_key": "705609", "vector": [], "sparse_vector": [], "title": "The Time Complexity of Fully Sparse Matrix Multiplication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "What is the time complexity of matrix multiplication of sparse integer matrices with min nonzeros in the input and mout nonzeros in the output? This paper provides improved upper bounds for this question for almost any choice of min vs. mout, and provides evidence that these new bounds might be optimal up to further progress on fast matrix multiplication.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.167"}, {"primary_key": "705610", "vector": [], "sparse_vector": [], "title": "Vertical Decomposition in 3D and 4D with Applications to Line Nearest-Neighbor Searching in 3D.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Vertical decomposition is a widely used general technique for decomposing the cells of arrangements of semi-algebraic sets in ℝd into constant-complexity subcells. In this paper, we settle in the affirmative a few long-standing open problems involving the vertical decomposition of substructures of arrangements for d = 3,4: (i) Let S be a collection of n semi-algebraic sets of constant complexity in ℝ3, and let U(m) be an upper bound on the complexity of the union U(S') of any subset S' ⊆ S of size at most m. We prove that the complexity of the vertical decomposition of the complement of U(S) is O* (n2 + U(n)) (where the O* (·) notation hides subpolynomial factors). We also show that the complexity of the vertical decomposition of the entire arrangement A(S) is O*(n2 + X), where X is the number of vertices in A(S). (ii) Let F be a collection of n trivariate functions whose graphs are semi-algebraic sets of constant complexity. We show that the complexity of the vertical decomposition of the portion of the arrangement A(F) in ℝ4 lying below the lower envelope of F is O*(n3).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.8"}, {"primary_key": "705611", "vector": [], "sparse_vector": [], "title": "Fast Approximation Algorithms for Piercing Boxes by Points.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let B = (b1,…, bn} be a set of n axis-aligned boxes in ℝd where d ≥ 2 is a constant. The piercing problem is to compute a smallest set of points N ∪ ℝd that hits every box in B, i.e., N ∩ bi ≠ ϕ, for i = 1,…, n. The problem is known to be NP-Hard. Let p := p (B), the piercing number be the minimum size of a piercing set of B. We first present a randomized O(log log p)-approximation algorithm with expected running time O(nd/2 polylog(n)). Next, we show that the expected running time can be improved to near-linear using a sampling-based technique, if p = O(n1/(d-1)). Specifically, in the plane, the improved running time is O(n log p), assuming p < n/ logΩ(1) n. Finally, we study the dynamic version of the piercing problem where boxes can be inserted or deleted. For boxes in ℝ2, we obtain a randomized O(log log p)-approximation algorithm with O(n1/2 polylog(n)) amortized expected update time for insertion or deletion of boxes. For squares in ℝ2, the update time can be improved to O(n1/3 polylog(n)).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.174"}, {"primary_key": "705612", "vector": [], "sparse_vector": [], "title": "Near-Optimal Min-Sum Motion Planning for Two Square Robots in a Polygonal Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let W ⊂ ℝ2 be a planar polygonal environment (i.e., a polygon potentially with holes) with a total of n vertices, and let A, B be two robots, each modeled as an axis-aligned unit square, that can translate inside W. Given source and target placements sA,tA,sB, tB ∈ W of A and B, respectively, the goal is to compute a collision-free-motion plan π*, i.e., a motion plan that continuously moves A from sA to tA and B from sB to tB so that A and B remain inside W and do not collide with each other during the motion. Furthermore, if such a plan exists, then we wish to return a plan that minimizes the sum of the lengths of the paths traversed by the robots. Given W,sA,tA,sB,tB and a parameter ɛ > 0, we present an n2ɛ-°(1) log n-time (1 + ɛ)-approximation algorithm for this problem. We are not aware of any polynomial-time algorithm for this problem, nor do we know whether the problem is NP-Hard. Our result is the first polynomial-time (1 + ɛ)-approximation algorithm for an optimal motion-planning problem involving two robots moving in a polygonal environment.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.176"}, {"primary_key": "705613", "vector": [], "sparse_vector": [], "title": "Parallel Approximate Maximum Flows in Near-Linear Work and Polylogarithmic Depth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a parallel algorithm for the $(1-\\epsilon)$-approximate maximum flow problem in capacitated, undirected graphs with $n$ vertices and $m$ edges, achieving $O(\\epsilon^{-3}\\text{polylog} n)$ depth and $O(m \\epsilon^{-3} \\text{polylog} n)$ work in the PRAM model. Although near-linear time sequential algorithms for this problem have been known for almost a decade, no parallel algorithms that simultaneously achieved polylogarithmic depth and near-linear work were known. At the heart of our result is a polylogarithmic depth, near-linear work recursive algorithm for computing congestion approximators. Our algorithm involves a recursive step to obtain a low-quality congestion approximator followed by a \"boosting\" step to improve its quality which prevents a multiplicative blow-up in error. Similar to Peng [SODA'16], our boosting step builds upon the hierarchical decomposition scheme of R\\\"acke, <PERSON>, and T\\\"aubig [SODA'14]. A direct implementation of this approach, however, leads only to an algorithm with $n^{o(1)}$ depth and $m^{1+o(1)}$ work. To get around this, we introduce a new hierarchical decomposition scheme, in which we only need to solve maximum flows on subgraphs obtained by contracting vertices, as opposed to vertex-induced subgraphs used in R\\\"acke, <PERSON>, and T\\\"aubig [SODA'14]. In particular, we are able to directly extract congestion approximators for the subgraphs from a congestion approximator for the entire graph, thereby avoiding additional recursion on those subgraphs. Along the way, we also develop a parallel flow-decomposition algorithm that is crucial to achieving polylogarithmic depth and may be of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.140"}, {"primary_key": "705614", "vector": [], "sparse_vector": [], "title": "Fast and Accurate Approximations of the Optimal Transport in Semi-Discrete and Discrete Settings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a d-dimensional continuous (resp. discrete) probability distribution μ and a discrete distribution ν, the semi-discrete (resp. discrete) optimal transport (OT) problem asks for computing a minimum-cost plan to transport mass from μ to ν; we assume n to be the number of points in the support of the discrete distributions. In this paper, we present three approximation algorithms for the OT problem with strong provable guarantees.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.159"}, {"primary_key": "705615", "vector": [], "sparse_vector": [], "title": "Odd Cycle Transversal on P5-free Graphs in Quasi-polynomial Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Paloma T. Lima", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An independent set in a graph G is a set of pairwise non-adjacent vertices. A graph G is bipartite if its vertex set can be partitioned into two independent sets. In the Odd Cycle Transversal problem, the input is a graph G along with a weight function w associating a rational weight with each vertex, and the task is to find a smallest weight vertex subset S in G such that G — S is bipartite; the weight of . We show that Odd Cycle Transversal admits an algorithm with running time on graphs excluding P5 (a path on five vertices) as an induced subgraph. The problem was previously known to be polynomial time solvable on P4-free graphs and NP-hard on P6-free graphs [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, Algorithmica 2020]. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> [Algorithmica 2019] posed the existence of a polynomial time algorithm on P5-free graphs as an open problem, this was later re-stated by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [Dagstuhl Reports, 9(6): 2019] and by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [SIDMA 2021], who gave an algorithm with running time . While our time algorithm falls short of completely resolving the complexity status of Odd Cycle Transversal on P5-free graphs it shows that the problem is not NP-hard unless every problem in NP is solvable in quasi-polynomial time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.189"}, {"primary_key": "705616", "vector": [], "sparse_vector": [], "title": "2-Approximation for Prize-Collecting Steiner Forest.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Approximation algorithms for the prize-collecting Steiner forest problem (PCSF) have been a subject of research for over three decades, starting with the seminal works of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> [1, 2] and <PERSON><PERSON><PERSON> and <PERSON> [14, 15] on Steiner forest and prize-collecting problems. In this paper, we propose and analyze a natural deterministic algorithm for PCSF that achieves a 2-approximate solution in polynomial time. This represents a significant improvement compared to the previously best known algorithm with a 2.54-approximation factor developed by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [19] in 2006. Furthermore, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [24] have established an integrality gap of at least 9/4 for the natural LP relaxation for PCSF. However, we surpass this gap through the utilization of a combinatorial algorithm and a novel analysis technique. Since 2 is the best known approximation guarantee for Steiner forest problem [2] (see also [15]), which is a special case of PCSF, our result matches this factor and closes the gap between the Steiner forest problem and its generalized version, PCSF.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.25"}, {"primary_key": "705617", "vector": [], "sparse_vector": [], "title": "Breaking the 3/4 Barrier for Approximate Maximin Share.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the fundamental problem of fairly allocating a set of indivisible goods among n agents with additive valuations using the desirable fairness notion of maximin share (MMS). MMS is the most popular share-based notion, in which an agent finds an allocation fair to her if she receives goods worth at least her MMS value. An allocation is called MMS if all agents receive at least their MMS value. However, since MMS allocations need not exist when n > 2, a series of works showed the existence of approximate MMS allocations with the current best factor of . The recent work [3] showed the limitations of existing approaches and proved that they cannot improve this factor to 3/4 + Ω(1). In this paper, we bypass these barriers to show the existence of ()-MMS allocations by developing new reduction rules and analysis techniques.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.4"}, {"primary_key": "705618", "vector": [], "sparse_vector": [], "title": "Partial Coloring Complex, Vertex Decomposability and Tverberg&apos;s Theorem with Constraints.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel family of simplicial complexes associated with the graph coloring problem. They include many well-known simplicial complexes such as chessboard complexes and crosspolytopes. We then study conditions under which these complexes become vertex decomposable and hence shellable. The connectivity of these complexes is also investigated. We apply these results to <PERSON><PERSON><PERSON>'s theorem with constraints and also to the chromatic number of certain Kneser-type hypergraphs and improve upon existing facts. Notably, we prove a conjecture of <PERSON><PERSON> and <PERSON> on Tverberg graphs.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.49"}, {"primary_key": "705619", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Shortest Path Reporting Against an Adaptive Adversary.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algebraic data structures are the main subroutine for maintaining distances in fully dynamic graphs in subquadratic time. However, these dynamic algebraic algorithms generally cannot maintain the shortest paths, especially against adaptive adversaries. We present the first fully dynamic algorithm that maintains the shortest paths against an adaptive adversary in subquadratic update time. This is obtained via a combinatorial reduction that allows reconstructing the shortest paths with only a few distance estimates. Using this reduction, we obtain the following:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.108"}, {"primary_key": "705620", "vector": [], "sparse_vector": [], "title": "Delaunay Bifiltrations of Functions on Point Clouds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Delaunay filtration D.(X) of a point cloud X ⊂ ℝd is a central tool of computational topology. Its use is justified by the topological equivalence of D. (X) and the offset (i.e., union-of-balls) filtration of X. Given a function γ : X → ℝ, we introduce a Delaunay bifiltration DC.(γ) that satisfies an analogous topological equivalence, ensuring that DC. (γ) topologically encodes the offset filtrations of all sublevel sets of γ, as well as the topological relations between them. DC.(γ) is of size , which for d odd matches the worst-case size of D. (X). Adapting the Bowyer-Watson algorithm for computing Delaunay triangulations, we give a simple, practical algorithm to compute DC.(γ) in time Our implementation, based on CGAL, computes DC. (γ) with modest overhead compared to computing D. (X), and handles tens of thousands of points in ℝ3 within seconds.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.173"}, {"primary_key": "705621", "vector": [], "sparse_vector": [], "title": "AG codes have no list-decoding friends: Approaching the generalized Singleton bound requires exponential alphabets.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "A simple, recently observed generalization of the classical Singleton bound to list-decoding asserts that rate R codes are not list-decodable using list-size L beyond an error fraction (the Singleton bound being the case of L = 1, i.e., unique decoding). We prove that in order to approach this bound for any fixed L > 1, one needs exponential alphabets. Specifically, for every L > 1 and R ∈ (0,1), if a rate R code can be list-of-L decoded up to error fraction , then its alphabet must have size at least exp(ΩL,R(1/ɛ)). This is in sharp contrast to the situation for unique decoding where certain families of rate R algebraic-geometry (AG) codes over an alphabet of size O(1/ɛ2) are unique-decodable up to error fraction (1 — R — ɛ)/2.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.55"}, {"primary_key": "705622", "vector": [], "sparse_vector": [], "title": "Universality of Spectral Independence with Applications to Fast Mixing in Spin Glasses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "We study Glauber dynamics for sampling from discrete distributions μ on the hypercube {±1}n. Recently, techniques based on spectral independence have successfully yielded optimal O(n) relaxation times for a host of different distributions μ. We show that spectral independence is universal: a relaxation time of O(n) implies spectral independence.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.181"}, {"primary_key": "705623", "vector": [], "sparse_vector": [], "title": "Conflict Checkable and Decodable Codes and Their Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Let C be an error-correcting code over a large alphabet q of block length n, and assume that, a possibly corrupted, codeword c is distributively stored among n servers where the ith entry is being held by the ith server. Suppose that every pair of servers publicly announce whether the corresponding coordinates are \"consistent\" with some legal codeword or \"conflicted\". What type of information about c can be inferred from this consistency graph? Can we check whether errors occurred and if so, can we find the error locations and effectively decode? We initiate the study of conflict-checkable and conflict-decodable codes and prove the following main results:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.56"}, {"primary_key": "705624", "vector": [], "sparse_vector": [], "title": "Quantum Worst-Case to Average-Case Reductions for All Linear Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Subramanian"], "summary": "We study the problem of constructing worst-case algorithms from average-case algorithms. Prior to this work, such reductions were only known for a small number of specific problems or restricted computational models. In contrast, we show that for quantum computation, all linear problems admit worst-case to average-case reductions. Specifically, we provide an explicit and efficient transformation of quantum algorithms that are only correct on a small (even sub-constant) fraction of their inputs into ones that are correct on all inputs. En route, we obtain a tight Ω(n2) lower bound on the average-case quantum query complexity of the Matrix-Vector Multiplication problem.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.90"}, {"primary_key": "705625", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Matching: -Approximation in Polylog Update Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study maximum matchings in fully dynamic graphs, which are graphs that undergo both edge insertions and deletions. Our focus is on algorithms that estimate the size of maximum matching after each update while spending a small time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.109"}, {"primary_key": "705626", "vector": [], "sparse_vector": [], "title": "An Improved Classical Singular Value Transformation for Quantum Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The field of quantum machine learning (QML) produces many proposals for attaining quantum speedups for tasks in machine learning and data analysis. Such speedups can only manifest if classical algorithms for these tasks perform significantly slower than quantum ones. We study quantum-classical gaps in QML through the quantum singular value transformation (QSVT) framework. QSVT, introduced by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and Wiebe [GSLW19], unifies all major types of quantum speedup [MRTC21]; in particular, a wide variety of QML proposals are applications of QSVT on low-rank classical data. We challenge these proposals by providing a classical algorithm that matches the performance of QSVT in this regime up to a small polynomial overhead.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.86"}, {"primary_key": "705627", "vector": [], "sparse_vector": [], "title": "<PERSON> meets <PERSON><PERSON> and Matroids: Algorithms and Reductions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study the relation of two fundamental problems in scheduling and fair allocation: makespan minimization on unrelated parallel machines and max-min fair allocation, also known as the Santa Claus problem. For both of these problems the best approximation factor is a notorious open question; more precisely, whether there is a better-than-2 approximation for the former problem and whether there is a constant approximation for the latter.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.100"}, {"primary_key": "705628", "vector": [], "sparse_vector": [], "title": "Euclidean Bottleneck Steiner Tree is Fixed-Parameter Tractable.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the Euclidean Bottleneck Steiner Tree problem, the input consists of a set of n points in ℝ2 called terminals and a parameter k, and the goal is to compute a Steiner tree that spans all the terminals and contains at most k points of ℝ2 as Steiner points such that the maximum edge-length of the Steiner tree is minimized, where the length of a tree edge is the Euclidean distance between its two endpoints. The problem is well-studied and is known to be NP-hard. In this paper, we give a kO(k)nO(1)-time algorithm for Euclidean Bottleneck Steiner Tree, which implies that the problem is fixed-parameter tractable (FPT). This settles an open question explicitly asked by <PERSON><PERSON> et al. [Algorithmica, 2011], who showed that the ℓ1 and ℓ∞ variants of the problem are FPT. Our approach can be generalized to the problem with ℓp metric for any rational 1 ≤ ρ ≤ ∞, or even other metrics on ℝ2.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.27"}, {"primary_key": "705629", "vector": [], "sparse_vector": [], "title": "Fair Price Discrimination.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A seller is pricing identical copies of a good to a stream of unit-demand buyers. Each buyer has a value on the good as his private information. The seller only knows the empirical value distribution of the buyer population and chooses the revenue-optimal price. We consider a widely studied third-degree price discrimination model where an information intermediary with perfect knowledge of the arriving buyer's value sends a signal to the seller, hence changing the seller's posterior and inducing the seller to set a personalized posted price. Prior work of <PERSON>, <PERSON>, and <PERSON> (American Economic Review, 2015) has shown the existence of a signaling scheme that preserves seller revenue, while always selling the item, hence maximizing consumer surplus. In a departure from prior work, we ask whether the consumer surplus generated is fairly distributed among buyers with different values. To this end, we aim to maximize functions of buyers' welfare that reward more balanced surplus allocations.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.96"}, {"primary_key": "705630", "vector": [], "sparse_vector": [], "title": "Dynamic Algorithms for Matroid Submodular Maximization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Submodular maximization under matroid and cardinality constraints are classical problems with a wide range of applications in machine learning, auction theory, and combinatorial optimization. In this paper, we consider these problems in the dynamic setting where (1) we have oracle access to a monotone submodular function f : 2V → ℝ+ and (2) we are given a sequence S of insertions and deletions of elements of an underlying ground set V.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.125"}, {"primary_key": "705631", "vector": [], "sparse_vector": [], "title": "Power of Posted-price Mechanisms for Prophet Inequalities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the power of posted pricing mechanisms for Bayesian online optimization problems subject to combinatorial feasibility constraints. When the objective is to maximize social welfare, the problem is widely studied in the literature on prophet inequalities. While most (though not all) existing algorithms for prophet inequalities are implemented using a pricing mechanism, whether or not this can be done in general is unknown, and was formally left as an open question by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2017, SICOMP 2020). Understanding the power and limitations of posted prices is important from a mechanism design perspective because any posted price mechanism is truthful, and is also interesting in its own right as it can guide future research on prophet inequalities.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.163"}, {"primary_key": "705632", "vector": [], "sparse_vector": [], "title": "The Minority Dynamics and the Power of Synchronicity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the minority-opinion dynamics over a fully-connected network of n nodes with binary opinions. Upon activation, a node receives a sample of opinions from a limited number of neighbors chosen uniformly at random. Each activated node then adopts the opinion that is least common within the received sample.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.144"}, {"primary_key": "705633", "vector": [], "sparse_vector": [], "title": "Computations with polynomial evaluation oracle: ruling out superlinear SETH-based lower bounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The field of fine-grained complexity aims at proving conditional lower bounds on the time complexity of computational problems. One of the most popular and successfully used assumptions, Strong Exponential Time Hypothesis (SETH), implies that SAT cannot be solved in 2(1-ɛ)n time. In recent years, it has been proved that known algorithms for many problems are optimal under SETH. Despite the wide applicability of SETH, for many problems, there are no known SETH-based lower bounds, so the quest for new reductions continues.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.73"}, {"primary_key": "705634", "vector": [], "sparse_vector": [], "title": "Matrix Perturbation: <PERSON><PERSON><PERSON><PERSON> in the Infinity Norm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Perturbation theory is developed to analyze the impact of noise on data and has been an essential part of numerical analysis. Recently, it has played an important role in designing and analyzing matrix algorithms. One of the most useful tools in this subject, the <PERSON><PERSON><PERSON><PERSON> sine theorem, provides an ℓ2 error bound on the perturbation of the leading singular vectors (and spaces).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.34"}, {"primary_key": "705635", "vector": [], "sparse_vector": [], "title": "Nibbling at Long Cycles: Dynamic (and Static) Edge Coloring in Optimal Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of maintaining a (1 + ɛ)∆-edge coloring in a dynamic graph G with n nodes and maximum degree at most Δ. The state-of-the-art update time is Oɛ(polylog(n)), by <PERSON><PERSON>, <PERSON> and <PERSON> [SODA'19] and by <PERSON><PERSON> [STOC'23], and more precisely O(log7 n/ɛ2), where Δ = Ω(log2 n/ɛ2).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.122"}, {"primary_key": "705636", "vector": [], "sparse_vector": [], "title": "Online Duet between Metric Embeddings and Minimum-Weight Perfect Matchings.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Csaba D. T<PERSON>"], "summary": "Low-distortional metric embeddings are a crucial component in the modern algorithmic toolkit. In an online metric embedding, points arrive sequentially and the goal is to embed them into a simple space irrevocably, while minimizing the distortion. Our first result is a deterministic online embedding of a general metric into Euclidean space with distortion if the metric has doubling dimension d), solving affirmatively a conjecture by <PERSON> and <PERSON> (2020), and quadratically improving the dependence on the aspect ratio Φ from <PERSON> et al. (2010). Our second result is a stochastic embedding of a metric space into trees with expected distortion O(d·log Φ), generalizing previous results (<PERSON><PERSON> et al. (2010), <PERSON><PERSON> et al. (2020)).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.162"}, {"primary_key": "705637", "vector": [], "sparse_vector": [], "title": "Sparse Regular Expression Matching.", "authors": ["<PERSON>", "Inge Li Gørtz"], "summary": "A regular expression specifies a set of strings formed by single characters combined with concatenation, union, and Kleene star operators. Given a regular expression R and a string Q, the regular expression matching problem is to decide if Q matches any of the strings specified by R. Regular expressions are a fundamental concept in formal languages and regular expression matching is a basic primitive for searching and processing data. A standard textbook solution [<PERSON>, CACM 1968] constructs and simulates a nondeterministic finite automaton, leading to an O(nm) time algorithm, where n is the length of Q and m is the length of R. Despite considerable research efforts only polylogarithmic improvements of this bound are known. Recently, conditional lower bounds provided evidence for this lack of progress when <PERSON><PERSON> and <PERSON><PERSON> [FOCS 2016] proved that, assuming the strong exponential time hypothesis (SETH), regular expression matching cannot be solved in O((nm)1−ϵ), for any constant ϵ > 0. Hence, the complexity of regular expression matching is essentially settled in terms of n and m. In this paper, we take a new approach and introduce a density parameter, ∆, that captures the amount of nondeterminism in the NFA simulation on Q. The density is at most nm + 1 but can be significantly smaller. Our main result is a new algorithm that solves regular expression matching in (equation presented) time. This essentially replaces nm with ∆ in the complexity of regular expression matching. We complement our upper bound by a matching conditional lower bound that proves that we cannot solve regular expression matching in time O(∆1−ϵ) for any constant ϵ > 0 assuming SETH. The key technical contribution in the result is a new linear space representation of the classic position automaton that supports fast state-set transition computation in near-linear time in the size of the input and output state sets. To achieve this we develop several new insights and techniques of independent interest, including new structural properties of the parse trees of regular expression, a decomposition of state-set transitions based on parse trees, and a fast batched predecessor data structure.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.120"}, {"primary_key": "705638", "vector": [], "sparse_vector": [], "title": "Fault-Tolerant Spanners against Bounded-Degree Edge Failures: <PERSON><PERSON><PERSON> More Faults, Almost For Free.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a new and stronger notion of fault-tolerant graph structures whose size bounds depend on the degree of the failing edge set, rather than the total number of faults. For a subset of faulty edges F ⊆ G, the faulty-degree deg(F) is the largest number of faults in F incident to any given vertex. For example, a matching F has deg(F) = 1 while |F| might be as large as n/2.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.93"}, {"primary_key": "705639", "vector": [], "sparse_vector": [], "title": "Factoring Pattern-Free Permutations into Separable ones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We show that for any permutation π there exists an integer kπ such that every permutation avoiding π as a pattern factorises as the composition of at most kπ separable permutations. In other words, every strict class C of permutations is contained in a bounded power of the class of separable permutations. This factorisation can be computed in linear time, for any fixed π.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.30"}, {"primary_key": "705640", "vector": [], "sparse_vector": [], "title": "Small But Unwieldy: A Lower Bound on Adjacency Labels for Small Classes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that for any natural number s, there is a constant γ and a subgraph-closed class having, for any natural n, at most γn graphs on n vertices up to isomorphism, but no adjacency labeling scheme with labels of size at most s log n. In other words, for every s, there is a small -even tiny - monotone class without universal graphs of size ns. Prior to this result, it was not excluded that every small class has an almost linear universal graph, or equivalently a labeling scheme with labels of size (1 + o(1))log n. The existence of such a labeling scheme, a scaled-down version of the recently disproved Implicit Graph Conjecture, was repeatedly raised [<PERSON><PERSON><PERSON><PERSON> and <PERSON>, ESA '07; <PERSON><PERSON><PERSON><PERSON> et al., JACM '21; <PERSON><PERSON><PERSON> et al., SIDMA '22; <PERSON><PERSON> et al., Comb. Theory '22]. Furthermore, our small monotone classes have unbounded twin-width, thus simultaneously disprove the already-refuted Small conjecture; but this time with a self-contained proof, not relying on elaborate group-theoretic constructions.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.44"}, {"primary_key": "705641", "vector": [], "sparse_vector": [], "title": "Tight approximability of MAX 2-SAT and relatives, under UGC.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> showed that the approximation ratio β ≈ 0.94016567 obtained by the MAX 2-SAT approximation algorithm of <PERSON><PERSON>, Livnat and Zwick (LLZ) is optimal modulo the Unique Games Conjecture (UGC) and modulo a Simplicity Conjecture that states that the worst performance of the algorithm is obtained on so called simple configurations. We prove <PERSON><PERSON><PERSON>'s conjecture, thereby showing the optimality of the LLZ approximation algorithm, relying only on the Unique Games Conjecture. Our proof uses a combination of analytic and computational tools.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.53"}, {"primary_key": "705642", "vector": [], "sparse_vector": [], "title": "Incremental Approximate Maximum Flow on Undirected Graphs in Subpolynomial Update Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide an algorithm which, with high probability, maintains a (1 — ɛ)-approximate maximum flow on an undirected graph undergoing m-edge additions in amortized mo(1)ɛ-3 time per update. To obtain this result, we provide a more general algorithm that solves what we call the incremental, thresholded, p-norm flow problem that asks to determine the first edge-insertion in an undirected graph that causes the minimum ℓp-norm flow to decrease below a given threshold in value. Since we solve this thresholded problem, our data structure succeeds against an adaptive adversary that can only see the data structure's output. Furthermore, since our algorithm holds for p = 2, we obtain improved algorithms for dynamically maintaining the effective resistance between a pair of vertices in an undirected graph undergoing edge insertions.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.106"}, {"primary_key": "705643", "vector": [], "sparse_vector": [], "title": "On Dynamic Graph Algorithms with Predictions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic algorithms operate on inputs undergoing updates, e.g., insertions or deletions of edges or vertices. After processing each update, the algorithm has to answer queries regarding the current state of the input data. We study dynamic algorithms in the model of algorithms with predictions (also known as learning-augmented algorithms). We assume the algorithm is given imperfect predictions regarding future updates, and we ask how such predictions can be used to improve the running time. In other words, we study the complexity of dynamic problems parameterized by the prediction accuracy. This can be seen as a model interpolating between classic online dynamic algorithms - which know nothing about future updates - and offline dynamic algorithms with the whole update sequence known upfront, which is similar to having perfect predictions. Our results give smooth tradeoffs between these two extreme settings.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.126"}, {"primary_key": "705644", "vector": [], "sparse_vector": [], "title": "The Sharp Power Law of Local Search on Expanders.", "authors": ["Simina Brânzei", "<PERSON><PERSON>", "<PERSON>"], "summary": "Local search is a powerful heuristic in optimization and computer science, the complexity of which has been studied in the white box and black box models. In the black box model, we are given a graph G = (V, E) and oracle access to a function f : V → ℝ. The local search problem is to find a vertex v that is a local minimum, i.e. with f(v) ≤ f (u) for all (u, v) ∈ E, using as few queries to the oracle as possible. The query complexity is well understood on the grid and the hypercube, but much less is known beyond.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.71"}, {"primary_key": "705645", "vector": [], "sparse_vector": [], "title": "Approximating Subset Sum Ratio faster than Subset Sum.", "authors": ["<PERSON>"], "summary": "Subset Sum Ratio is the following optimization problem: Given a set of n positive numbers I, find disjoint subsets X, Y ⊆ I minimizing the ratio max{Σ(X)/Σ(Y), Σ(Y)/Σ(X)}, where Σ(Z) denotes the sum of all elements of Z. Subset Sum Ratio is an optimization variant of the Equal Subset Sum problem. It was introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> in '92 and is known to admit an FPTAS [Baz<PERSON>, Santha, Tuza '98]. The best approximation schemes before this work had running time O(n4/ɛ) [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> '18], Õ(n2,3/ɛ2,6) and Õ(n2/ɛ3) [<PERSON><PERSON><PERSON><PERSON><PERSON> et al. '22].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.50"}, {"primary_key": "705646", "vector": [], "sparse_vector": [], "title": "Faster Sublinear-Time Edit Distance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the fundamental problem of approximating the edit distance of two strings. After an extensive line of research led to the development of a constant-factor approximation algorithm in almost-linear time, recent years have witnessed a notable shift in focus towards sublinear-time algorithms. Here, the task is typically formalized as the (k, K)-gap edit distance problem: Distinguish whether the edit distance of two strings is at most k or more than K.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.117"}, {"primary_key": "705647", "vector": [], "sparse_vector": [], "title": "Dynamic Dynamic Time Warping.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Dynamic Time Warping (DTW) distance is a popular similarity measure for polygonal curves (i.e., sequences of points). It finds many theoretical and practical applications, especially for temporal data, and is known to be a robust, outlier-insensitive alternative to the Fréchet distance. For static curves of at most n points, the DTW distance can be computed in O(n2) time in constant dimension. This tightly matches a SETH-based lower bound, even for curves in R 1. In this work, we study dynamic algorithms for the DTW distance. Here, the goal is to design a data structure that can be efficiently updated to accommodate local changes to one or both curves, such as inserting or deleting vertices and, after each operation, reports the updated DTW distance. We give such a data structure with update and query time O(n1.5 log n), where n is the maximum length of the curves. As our main result, we prove that our data structure is conditionally optimal, up to subpolynomial factors. More precisely, we prove that, already for curves in R 1, there is no dynamic algorithm to maintain the DTW distance with update and query time O(n1.5−δ) for any constant δ > 0, unless the Negative-k-Clique Hypothesis fails. In fact, we give matching upper and lower bounds for various trade-offs between update and query time, even in cases where the lengths of the curves differ.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.10"}, {"primary_key": "705648", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for the Weighted Nash Social Welfare via Convex and Non-Convex Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In an instance of the weighted Nash Social Welfare problem, we are given a set of m indivisible items, G, and n agents, A, where each agent i ∈ A has a valuation vij ≥ 0 for each item j ∈ G. In addition, every agent i has a non-negative weight wi such that the weights collectively sum up to 1. The goal is to find an assignment σ : G → A that maximizes . When all the weights equal to , the problem reduces to the classical Nash Social Welfare problem, which has recently received much attention. In this work, we present a -approximation algorithm for the weighted Nash Social Welfare problem, where denotes the KL-divergence between the distribution w and the uniform distribution on [n].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.52"}, {"primary_key": "705649", "vector": [], "sparse_vector": [], "title": "Maintaining Matroid Intersections Online.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Maintaining a maximum bipartite matching online while minimizing augmentations is a well studied problem, motivated by content delivery, job scheduling, and hashing. A breakthrough result of <PERSON>, <PERSON>, and <PERSON><PERSON> (SODA 2018) resolved this problem up to a logarithmic factors. However, to model other problems in scheduling and resource allocation, we may need a richer class of combinatorial constraints (e.g., matroid constraints).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.149"}, {"primary_key": "705650", "vector": [], "sparse_vector": [], "title": "A (3 + ɛ)-approximation algorithm for the minimum sum of radii problem with outliers and extensions for generalized lower bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Clustering is a fundamental problem setting with applications in many different areas. For a given set of points in a metric space and an integer k, we seek to partition the given points into k clusters. For each computed cluster, one typically defines one point as the center of the cluster. A natural objective is to minimize the sum of the cluster center's radii, where we assign the smallest radius r to each center such that each point in the cluster is at a distance of at most r from the center. The best-known polynomial time approximation ratio for this problem is 3.389. In the setting with outliers, i.e., we are given an integer m and allow up to m points that are not in any cluster, the best-known approximation factor is 12.365.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.69"}, {"primary_key": "705651", "vector": [], "sparse_vector": [], "title": "On the Hardness of PosSLP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem PosSLP involves determining whether an integer computed by a given straight-line program is positive. This problem has attracted considerable attention within the field of computational complexity as it provides a complete characterization of the complexity associated with numerical computation. However, non-trivial lower bounds for PosSLP remain unknown. In this paper, we demonstrate that PosSLP ∈ BPP would imply that NP ⊆ BPP, under the assumption of a conjecture concerning the complexity of the radical of a polynomial proposed by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (STOC'2018). Our proof builds upon the established NP-hardness of determining if a univariate polynomial computed by an SLP has a real root, as demonstrated by <PERSON><PERSON><PERSON> and <PERSON> (JDA'2005).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.75"}, {"primary_key": "705652", "vector": [], "sparse_vector": [], "title": "A (3 + ɛ)-Approximate Correlation Clustering Algorithm in Dynamic Streams.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Grouping together similar elements in datasets is a common task in data mining and machine learning. In this paper, we study streaming and parallel algorithms for correlation clustering, where each pair of elements is labeled either similar or dissimilar. The task is to partition the elements and the objective is to minimize disagreements, that is, the number of dissimilar elements grouped together and similar elements that get separated.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.101"}, {"primary_key": "705653", "vector": [], "sparse_vector": [], "title": "Breaking 3-Factor Approximation for Correlation Clustering in Polylogarithmic Rounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we study parallel algorithms for the correlation clustering problem, where every pair of two different entities is labeled with similar or dissimilar. The goal is to partition the entities into clusters to minimize the number of disagreements with the labels. Currently, all efficient parallel algorithms have an approximation ratio of at least 3. In comparison with the 1.994 + ɛ ratio achieved by polynomial-time sequential algorithms [25], a significant gap exists.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.143"}, {"primary_key": "705654", "vector": [], "sparse_vector": [], "title": "A Whole New Ball Game: A Primal Accelerated Method for Matrix Games and Minimizing the Maximum of Smooth Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We design algorithms for minimizing maxi∈[n] fi(x) over a d-dimensional Euclidean or simplex domain. When each fi is 1-Lipschitz and 1-smooth, our method computes an ɛ-approximate solution using Õ(nɛ-1/3 + ɛ-2) gradient and function evaluations, and Õ(nɛ-4/3) additional runtime. For large n, our evaluation complexity is optimal up to polylogarithmic factors. In the special case where each fi is linear—which corresponds to finding a near-optimal primal strategy in a matrix game—our method finds an ɛ-approximate solution in runtime Õ(n(d/ɛ)2/3 +nd+dɛ-2). For n > d and this improves over all existing first-order methods. When additionally d = ω(n8/11) our runtime also improves over all known interior point methods.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.130"}, {"primary_key": "705655", "vector": [], "sparse_vector": [], "title": "Beyond the Quadratic Time Barrier for Network Unreliability.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> (STOC 1995) gave the first FPTAS for the network (un)reliability problem, setting in motion research over the next three decades that obtained increasingly faster running times, eventually leading to a Õ(n2)-time algorithm (<PERSON><PERSON>, STOC 2020). This represented a natural culmination of this line of work because the algorithmic techniques used can enumerate Θ(n2) (near)-minimum cuts. In this paper, we go beyond this quadratic barrier and obtain a faster FPTAS for the network unreliability problem. Our algorithm runs in m1+o(1) + Õ)(n1.5) time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.62"}, {"primary_key": "705656", "vector": [], "sparse_vector": [], "title": "Tight Lower Bound on Equivalence Testing in Conditional Sampling Model.", "authors": ["Diptarka Chakraborty", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the equivalence testing problem where the goal is to determine if the given two unknown distributions on [n] are equal or ɛ-far in the total variation distance in the conditional sampling model (CFGM, SICOMP16; CRS, SICOMP15) wherein a tester can get a sample from the distribution conditioned on any subset. Equivalence testing is a central problem in distribution testing, and there has been a plethora of work on this topic in various sampling models.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.153"}, {"primary_key": "705657", "vector": [], "sparse_vector": [], "title": "Sorting Pattern-Avoiding Permutations via 0-1 Matrices Forbidding Product Patterns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Sorrachai <PERSON>i"], "summary": "We consider the problem of comparison-sorting an n-permutation S that avoids some k-permutation π. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [CGK + 15b] prove that when S is sorted by inserting the elements into the GreedyFuture [DHI+09] binary search tree, the running time is linear in the extremal function Ex(Pπ ⊗ (∴), n). This is the maximum number of 1s in an n × n 0-1 matrix avoiding Pπ ⊗ (∴), where Pπ is the k × k permutation matrix of π, and Pπ ⊗ (∴) is the 2k × 3k Kronecker product of Pπ and the \"hat\" pattern (∴). The same time bound can be achieved by sorting S with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s SmoothHeap [KS20].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.7"}, {"primary_key": "705658", "vector": [], "sparse_vector": [], "title": "An Optimal Algorithm for Higher-Order Voronoi Diagrams in the Plane: The Usefulness of Nondeterminism.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present the first optimal randomized algorithm for constructing the order-k Voronoi diagram of n points in two dimensions. The expected running time is O(n log n + nk), which improves the previous, two-decades- old result of <PERSON> (SoCG'99) by a 2O(log* k) factor. To obtain our result, we (i) use a recent decision-tree technique of <PERSON> and <PERSON> (SODA'22) in combination with <PERSON>'s cutting construction, to reduce the problem to verifying an order-k Voronoi diagram, and (ii) solve the verification problem by a new divide-and-conquer algorithm using planar-graph separators.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.156"}, {"primary_key": "705659", "vector": [], "sparse_vector": [], "title": "Shortcut Partitions in Minor-Free Graphs: Steiner Point Removal, Distance Oracles, Tree Covers, and More.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The notion of shortcut partition, introduced recently by <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> [CCL+23], is a new type of graph partition into low-diameter clusters. Roughly speaking, the shortcut partition guarantees that for every two vertices u and v in the graph, there exists a path between u and v that intersects only a few clusters. They proved that any planar graph admits a shortcut partition and gave several applications, including a construction of tree cover for arbitrary planar graphs with stretch 1 + ɛ and O(1) many trees for any fixed ɛ ∈ (0,1). However, the construction heavily exploits planarity in multiple steps, and is thus inherently limited to planar graphs.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.191"}, {"primary_key": "705660", "vector": [], "sparse_vector": [], "title": "Fully Scalable Massively Parallel Algorithms for Embedded Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the massively parallel computation (MPC) model, which is a theoretical abstraction of large- scale parallel processing models such as MapReduce. In this model, assuming the widely believed 1-vs-2-cycles conjecture, solving many basic graph problems in O(1) rounds with a strongly sublinear memory size per machine is impossible. We improve on the recent work of <PERSON><PERSON> and <PERSON> [SODA 2023] that bypass this barrier for problems when a planar embedding of the graph is given. In the previous work, on graphs of size n with O(n/S) machines, the memory size per machine needs to be at least S = n2/3+Ω(1), whereas we extend their work to the fully scalable regime, where the memory size per machine can be S = nδ for any constant 0 < δ < 1. We thus give the first constant round fully scalable algorithms for embedded planar graphs for the problems of (i) connectivity and (ii) minimum spanning tree (MST).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.155"}, {"primary_key": "705661", "vector": [], "sparse_vector": [], "title": "Improved Approximations for Ultrametric Violation Distance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the ultrametric violation distance problem introduced by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> [FOCS, 2022]. Given pairwise distances as input, the goal is to modify the minimum number of distances so as to make it a valid ultrametric. In other words, this is the problem of fitting an ultrametric to given data, where the quality of the fit is measured by the norm of the error; variants of the problem for the ℓ∞ and ℓ1 norms are well-studied in the literature.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.68"}, {"primary_key": "705662", "vector": [], "sparse_vector": [], "title": "A Quasi-Monte Carlo Data Structure for Smooth Kernel Evaluations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the kernel density estimation (KDE) problem one is given a kernel K(x, y) and a dataset P of points in a high dimensional Euclidean space, and must prepare a small space data structure that can quickly answer density queries: given a point q, output a (1 + ɛ)-approximation to . The classical approach to KDE (and the more general problem of matrix vector multiplication for kernel matrices) is the celebrated fast multipole method of <PERSON><PERSON> and <PERSON> [1983]. The fast multipole method combines a basic space partitioning approach with a multidimensional Taylor expansion, which yields a ≈ logd(n/ɛ) query time (exponential in the dimension d). A recent line of work initiated by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [2017] achieved polynomial dependence on d via a combination of random sampling and randomized space partitioning, with <PERSON><PERSON> et al. [2018] giving an efficient data structure with query time ≈ polylog(1/µ)/ɛ2 for smooth kernels.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.184"}, {"primary_key": "705663", "vector": [], "sparse_vector": [], "title": "Breaking the Metric Voting Distortion Barrier.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following well studied problem of metric distortion in social choice. Suppose we have an election with n voters and m candidates who lie in a shared metric space. We would like to design a voting rule that chooses a candidate whose average distance to the voters is small. However, instead of having direct access to the distances in the metric space, each voter gives us a ranked list of the candidates in order of distance. Can we design a rule that regardless of the election instance and underlying metric space, chooses a candidate whose cost differs from the true optimum by only a small factor (known as the distortion)?", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.65"}, {"primary_key": "705664", "vector": [], "sparse_vector": [], "title": "Composition of nested embeddings with an application to outlier removal.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the design of embeddings into Euclidean space with outliers. Given a metric space (X, d) and an integer k, the goal is to embed all but k points in X (called the \"outliers\") into ℓ2 with the smallest possible distortion c. Finding the optimal distortion c for a given outlier set size k, or alternately the smallest k for a given target distortion c are both NP-hard problems. In fact, it is UGC-hard to approximate k to within a factor smaller than 2 even when the metric sans outliers is isometrically embeddable into ℓ2. We consider bi-criteria approximations. Our main result is a polynomial time algorithm that approximates the outlier set size to within an O(log2 k) factor and the distortion to within a constant factor.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.66"}, {"primary_key": "705665", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Approximate Dual-Failure Replacement Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a directed graph G = (V, E, ω) on n vertices with positive edge weights as well as two designated terminals s, t ∈ V, our goal is to compute the shortest path from s to t avoiding any pair of presumably failed edges f1,f2 ∈ E, which is a natural generalization of the classical replacement path problem which considers single edge failures only.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.91"}, {"primary_key": "705666", "vector": [], "sparse_vector": [], "title": "Adaptive Out-Orientations with Applications.", "authors": ["<PERSON>", "Aleksander Bj<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give improved algorithms for maintaining edge-orientations of a fully-dynamic graph, such that the maximum out-degree is bounded. On one hand, we show how to orient the edges such that maximum outdegree is proportional to the arboricity α of the graph, in, either, an amortised update time of O(log2 nlog α), or a worst-case update time of O(log3 nlog α). On the other hand, motivated by applications including dynamic maximal matching, we obtain a different trade-off. Namely, the improved update time of either O(log nlog α), amortised, or O(log2 nlog α), worst-case, for the problem of maintaining an edge-orientation with at most O(α + log n) out-edges per vertex. Finally, all of our algorithms naturally limit the recourse to be polylogarithmic in n and α. Our algorithms adapt to the current arboricity of the graph, and yield improvements over previous work: Firstly, we obtain deterministic algorithms for maintaining a (1 + ε) approximation of the maximum subgraph density, ρ, of the dynamic graph. Our algorithms have update times of O(ε−6 log3 nlog ρ) worst-case, and O(ε−4 log2 nlog ρ) amortised, respectively. We may output a subgraph H of the input graph where its density is a (1 + ε) approximation of the maximum subgraph density in time linear in the size of the subgraph. These algorithms have improved update time compared to the O(ε−6 log4 n) algorithm by <PERSON><PERSON> and <PERSON> from STOC 2020. Secondly, we obtain an O(ε−6 log3 nlog α) worst-case update time algorithm for maintaining a (1 + ε)OPT + 2 approximation of the optimal out-orientation of a graph with adaptive arboricity α, improving the O(ε−6α2 log3 n) algorithm by Christiansen and Rotenberg from ICALP 2022. This yields the first worst-case polylogarithmic dynamic algorithm for decomposing into O(α) forests. Thirdly, we obtain arboricity-adaptive fully-dynamic deterministic algorithms for a variety of problems including maximal matching, ∆ + 1 colouring, and matrix vector multiplication. All update times are worst-case O(α + log2 nlog α), where α is the current arboricity of the graph. For the maximal matching problem, the state-of-the-art deterministic algorithms by Kopelowitz, Krauthgamer, Porat, and Solomon from ICALP 2014 runs in time O(α2 + log2 n), and by Neiman and Solomon from STOC 2013 runs in time O(√m). We give improved running times whenever the arboricity α ∈ ω(log n√log log n).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.110"}, {"primary_key": "705667", "vector": [], "sparse_vector": [], "title": "Combinatorial Approach for Factorization of Variance and Entropy in Spin Systems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a simple combinatorial framework for establishing approximate tensorization of variance and entropy in the setting of spin systems (a.k.a. undirected graphical models) based on balanced separators of the underlying graph. Such approximate tensorization results immediately imply as corollaries many important structural properties of the associated Gibbs distribution, in particular rapid mixing of the <PERSON><PERSON><PERSON> dynamics for sampling. We prove approximate tensorization by recursively establishing block factorization of variance and entropy with a small balanced separator of the graph. Our approach goes beyond the classical canonical path method for variance and the recent spectral independence approach, and allows us to obtain new rapid mixing results. As applications of our approach, we show that:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.179"}, {"primary_key": "705668", "vector": [], "sparse_vector": [], "title": "Mildly Exponential Lower Bounds on Tolerant Testers for Monotonicity, Unateness, and Juntas.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give the first super-polynomial (in fact, mildly exponential) lower bounds for tolerant testing (equivalently, distance estimation) of monotonicity, unateness, and juntas with a constant separation between the \"yes\" and \"no\" cases. Specifically, we give", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.151"}, {"primary_key": "705669", "vector": [], "sparse_vector": [], "title": "Fast Sampling of b-Matchings and b-Edge Covers.", "authors": ["<PERSON><PERSON><PERSON>", "Yuzhou Gu"], "summary": "For an integer b ≥ 1, a b-matching (resp. b-edge cover) of a graph G = (V, E) is a subset S ⊆ E of edges such that every vertex is incident with at most (resp. at least) b edges from S. We prove that for any b ≥ 1 the simple <PERSON><PERSON><PERSON> dynamics for sampling (weighted) b-matchings and b-edge covers mixes in O(n log n) time on all n-vertex bounded-degree graphs. This significantly improves upon previous results which have worse running time and only work for b-matchings with b ≤ 7 and for b-edge covers with b ≤ 2.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.178"}, {"primary_key": "705670", "vector": [], "sparse_vector": [], "title": "Smoothed Complexity of SWAP in Local Graph Partitioning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Emmanouil V<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give the first quasipolynomial upper bound φnpolylog(n) for the smoothed complexity of the SWAP algorithm for local Graph Partitioning (also known as Bisection Width) under the full perturbation model, where n is the number of nodes in the graph and φ is a parameter that measures the magnitude of perturbations applied on its edge weights. More generally, we show that the same quasipolynomial upper bound holds for the smoothed complexity of the 2-FLIP algorithm for any binary Maximum Constraint Satisfaction Problem, including local Max-Cut, for which similar bounds were only known for 1-FLIP. Our results are based on an analysis of a new notion of useful cycles in the multigraph formed by long sequences of double flips, showing that it is unlikely for every double flip in a long sequence to incur a positive but small improvement in the cut weight.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.182"}, {"primary_key": "705671", "vector": [], "sparse_vector": [], "title": "Faster Algor<PERSON>ms for Bounded Knapsack and Bounded Subset Sum Via Fine-Grained Proximity Results.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate pseudopolynomial-time algorithms for Bounded Knapsack and Bounded Subset Sum. Recent years have seen a growing interest in settling their fine-grained complexity with respect to various parameters. For Bounded Knapsack, the number of items n and the maximum item weight wmax are two of the most natural parameters that have been studied extensively in the literature. The previous best running time in terms of n and wmax is [<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '21]. There is a conditional lower bound of (n + wmax)2-o(1) based on (min, +)-convolution hypothesis [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '17]. We narrow the gap significantly by proposing an -time algorithm. Our algorithm works for both 0-1 Knapsack and Bounded Knapsack. Note that in the regime where wmax ≈ n, our algorithm runs in Õ(n12/5) time, while all the previous algorithms require Ω(n3) time in the worst case.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.171"}, {"primary_key": "705672", "vector": [], "sparse_vector": [], "title": "Uniformity Testing over Hypergrids with Subcube Conditioning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give an algorithm for testing uniformity of distributions supported on hypergrids [m1] × · · · × [mn], which makes many queries to a subcube conditional sampling oracle with m = maximi. When m is a constant, our algorithm is nearly optimal and strengthens the algorithm of <PERSON><PERSON> et al. (SODA 2021) which has the same query complexity but works for hypercubes {±1}n only.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.152"}, {"primary_key": "705673", "vector": [], "sparse_vector": [], "title": "An Ω~(√log T ) Lower Bound for Steiner Point Removal.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Steiner point removal (SPR) problem, we are given a (weighted) graph G and a subset T of its vertices called terminals, and the goal is to compute a (weighted) graph H on T that is a minor of G, such that the distance between every pair of terminals is preserved to within some small multiplicative factor, that is called the stretch of H.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.26"}, {"primary_key": "705674", "vector": [], "sparse_vector": [], "title": "On (1 + ɛ)-Approximate Flow Sparsifiers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a large graph G with a subset |T| = k of its vertices called terminals, a quality-q flow sparsifier is a small graph G' that contains T and preserves all multicommodity flows that can be routed between terminals in T, to within factor q. The problem of constructing flow sparsifiers with good (small) quality and (small) size has been a central problem in graph compression for decades.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.63"}, {"primary_key": "705675", "vector": [], "sparse_vector": [], "title": "Solving Fréchet Distance Problems by Algebraic Geometric Methods.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study several polygonal curve problems under the Fr<PERSON>chet distance via algebraic geometric methods. Let 𝕏dm and 𝕏dk be the spaces of all polygonal curves of m and k vertices in ℝd, respectively. We assume that k ≤ m. Let be the set of ranges in 𝕏dm for all possible metric balls of polygonal curves in 𝕏dk under the <PERSON><PERSON><PERSON> distance. We prove a nearly optimal bound of O(dk log(km)) on the VC dimension of the range space (𝕏dm, ), improving on the previous O(d2k2 log(dkm)) upper bound and approaching the current Ω(dk log k) lower bound. Our upper bound also holds for the weak Fréchet distance. We also obtain exact solutions that are hitherto unknown for the curve simplification, range searching, nearest neighbor search, and distance oracle problems.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.158"}, {"primary_key": "705676", "vector": [], "sparse_vector": [], "title": "Triangulations Admit Dominating Sets of Size 2n/7.", "authors": ["Aleksander B. G. Christiansen", "<PERSON>", "<PERSON>"], "summary": "We show that every planar triangulation on n > 10 vertices has a dominating set of size 2n/7 = n/3.5. This approaches the n/4 bound conjectured by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [12], and improves significantly on the previous best bound of 17n/53 ≈ n/3.117 by <PERSON><PERSON><PERSON><PERSON> [18]. From our proof it follows that every 3-connected n-vertex near-triangulation (except for 3 sporadic examples) has a dominating set of size n/3.5. On the other hand, for 3-connected near-triangulations, we show a lower bound of 3(n−1)/11 ≈ n/3.666, demonstrating that the conjecture by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [12] cannot be strengthened to 3-connected near-triangulations. Our proof uses a penalty function that, aside from the number of vertices, penalises vertices of degree 2 and specific constellations of neighbours of degree 3 along the boundary of the outer face. To facilitate induction, we not only consider near-triangulations, but a wider class of graphs (skeletal triangulations), allowing us to delete vertices more freely. Our main technical contribution is a set of attachments, that are small graphs we inductively attach to our graph, in order both to remember whether existing vertices are already dominated, and that serve as a tool in a divide and conquer approach. Along with a well-chosen potential function, we thus both remove and add vertices during the induction proof. We complement our proof with a constructive algorithm that returns a dominating set of size ≤ 2n/7. Our algorithm has a quadratic running time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.47"}, {"primary_key": "705677", "vector": [], "sparse_vector": [], "title": "Sparse induced subgraphs in P6-free graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that a number of computational problems that ask for the largest sparse induced subgraph satisfying some property definable in CMSO2 logic, most notably Feedback Vertex Set, are polynomial-time solvable in the class of P6-free graphs. This generalizes the work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> on the Maximum Weight Independent Set problem in P6-free graphs [SODA 2019, TALG 2022], and of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> on problems in P5-free graphs [SODA 2021].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.190"}, {"primary_key": "705678", "vector": [], "sparse_vector": [], "title": "A Faster Combinatorial Algorithm for Maximum Bipartite Matching.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The maximum bipartite matching problem is among the most fundamental and well-studied problems in combinatorial optimization. A beautiful and celebrated combinatorial algorithm of <PERSON><PERSON> and <PERSON> [26] shows that maximum bipartite matching can be solved in O(m√n) time on a graph with n vertices and m edges. For the case of very dense graphs, a different approach based on fast matrix multiplication was subsequently developed [27, 39], that achieves a running time of O(n2.371). For the next several decades, these results represented the fastest known algorithms for the problem until in 2013, a ground-breaking work of <PERSON><PERSON> [36] gave a significantly faster algorithm for sparse graphs. Subsequently, a sequence of works developed increasingly faster algorithms for solving maximum bipartite matching, and more generally directed maximum flow, culminating in a spectacular recent breakthrough [9] that gives an m1+o(1) time algorithm for maximum bipartite matching (and more generally, for min cost flows). These more recent developments collectively represented a departure from earlier combinatorial approaches: they all utilized continuous techniques based on interior-point methods for solving linear programs.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.79"}, {"primary_key": "705679", "vector": [], "sparse_vector": [], "title": "A PTAS for ℓ0-Low Rank Approximation: Solving Dense CSPs over Reals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Suprovat Ghoshal", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the ℓ0-Low Rank Approximation problem, where the input consists of a matrix A ∈ ℝnR×nc and an integer k, and the goal is to find a matrix B of rank at most k that minimizes ‖A — B‖0, which is the number of entries where A and B differ. For any constant k and ɛ > 0, we present a polynomial time (1 + ɛ)- approximation time for this problem, which significantly improves the previous best poly(k)-approximation.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.35"}, {"primary_key": "705680", "vector": [], "sparse_vector": [], "title": "Distances and shortest paths on graphs of bounded highway dimension: simple, fast, dynamic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s algorithm is the standard method for computing shortest paths on arbitrary graphs. However, it is slow for large graphs, taking at least linear time. It has been long known that for real world road networks, creating a hierarchy of well-chosen shortcuts allows fast distance and path computation, with exact distance queries seemingly being answered in logarithmic time. However, these methods were but heuristics until the work of <PERSON> et al. [JACM 2016], where they defined a graph parameter called highway dimension which is constant for real-world road networks, and showed that in graphs of constant highway dimension, a shortcut hierarchy exists that guarantees shortest distance computation takes O(log(U+| V|)) time and O(V log(U +| V|)) space, where U is the ratio of the smallest to largest edge, and |V| is the number of vertices. The problem is that they were unable to efficiently compute the hierarchy of shortcuts. Here we present a simple and efficient algorithm to compute the needed hierarchy of shortcuts in time and space O(V log(U + |V|)), as well as supporting updates in time O(log(U + |V|)).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.95"}, {"primary_key": "705681", "vector": [], "sparse_vector": [], "title": "Equilibrium Dynamics in Market Games with Exchangeable and Divisible Resources.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study a market game with n ≥ 2 players competing over m ≥ 1 divisible resources of different finite capacities. Resources are traded via the proportional sharing mechanism, where players are price-anticipating, meaning that they can influence the prices with their bids. Additionally, each player has an initial endowment of the resources which are sold at market prices. Although the players' total profit functions may be discontinuous in the bids, we prove existence and uniqueness of pure Nash equilibria of the resulting market game. Then, we study a discrete dynamic arising from repeatedly taking the (unique) equilibrium resource allocation as initial endowments for the next market game. We prove that the total utility value of the dynamic converges to either an optimal allocation value (maximizing total utility over the allocation space) or to a restricted optimal allocation value, where the restriction is defined by fixing some tight resources which are exclusively allocated to a single player. As a corollary, it follows that for strictly concave utility functions, the aggregated allocation vector of the dynamic converges to the unique (possibly restricted) optimal aggregated allocation, and for linear utility functions, we even get convergence of the dynamic to a (possibly restricted) optimal solution in the (non-aggregated) original allocation space.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.20"}, {"primary_key": "705682", "vector": [], "sparse_vector": [], "title": "Breaking the k/ log k Barrier in Collective Tree Exploration via Tree-Mining.", "authors": ["<PERSON><PERSON>"], "summary": "In collective tree exploration, a team of k mobile agents is assigned to go through all edges of an unknown tree as fast as possible. An edge of the tree is revealed to the team when one agent becomes adjacent to that edge. The agents start from the root and all move synchronously along one adjacent edge in each round. Communication between the agents is unrestricted, and they are, therefore, centrally controlled by a single exploration algorithm. The algorithm's guarantee is typically compared to the number of rounds required by the agents to go through all edges if they had known the tree in advance. This quantity is at least max{2n/k, 2D} where n is the number of nodes and D is the tree depth. Since the introduction of the problem by [11, 12], two types of guarantees have emerged: the first takes the form r(k)(n/k + D), where r(k) is called the competitive ratio, and the other takes the form 2n/k + f (k, D), where f (k, D) is called the competitive overhead. In this paper, we present the first algorithm with linear-in-D competitive overhead, thereby reconciling both approaches. Specifically, our bound is in 2n/k + O(klog2(k)-1 D) and leads to a competitive ratio in . This is the first improvement over O(k/In k) since the introduction of the problem, twenty years ago. Our algorithm is developed for an asynchronous generalization of collective tree exploration (ACTE). It belongs to a broad class of locally-greedy exploration algorithms that we define. We show that the analysis of locally-greedy algorithms can be seen through the lens of a 2-player game that we call the tree-mining game and which could be of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.148"}, {"primary_key": "705683", "vector": [], "sparse_vector": [], "title": "Dynamic algorithms for k-center on graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we give the first efficient algorithms for the k-center problem on dynamic graphs undergoing edge updates. In this problem, the goal is to partition the input into k sets by choosing k centers such that the maximum distance from any data point to its closest center is minimized. It is known that it is NP-hard to get a better than 2 approximation for this problem.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.123"}, {"primary_key": "705684", "vector": [], "sparse_vector": [], "title": "Parameterized algorithms for block-structured integer programs with large entries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study two classic variants of block-structured integer programming. Two-stage stochastic programs are integer programs of the form {Aix + Diyi = bi for all i = 1,…, n}, where <PERSON> and <PERSON> are bounded-size matrices. Intuitively, this form corresponds to the setting when after setting a small set of global variables x, the program can be decomposed into a possibly large number of bounded-size subprograms. On the other hand, n-fold programs are integer programs of the form and Diyi = bi for all i = 1,…,n}, where again Ci and Di are bounded-size matrices. This form is natural for knapsack-like problems, where we have a large number of variables partitioned into small-size groups, each group needs to obey some set of local constraints, and there are only a few global constraints that link together all the variables.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.29"}, {"primary_key": "705685", "vector": [], "sparse_vector": [], "title": "A polynomial-time OPTɛ-approximation algorithm for maximum independent set of connected subgraphs in a planar graph.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the Maximum Independent Set of Objects problem, we are given an n-vertex planar graph G and a family D of N objects, where each object is a connected subgraph of G. The task is to find a subfamily F ⊆ D of maximum cardinality that consists of pairwise disjoint objects. This problem is NP-hard and is equivalent to the problem of finding the maximum number of pairwise disjoint polygons in a given family of polygons in the plane.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.23"}, {"primary_key": "705686", "vector": [], "sparse_vector": [], "title": "Count on CFI graphs for #P-hardness.", "authors": ["<PERSON><PERSON>"], "summary": "A homomorphism between graphs H and G, possibly with vertex-colors, is a function f : V(ℋ) → V(G) that preserves colors and edges. Many interesting graph parameters are finite linear combinations p(·) = Ση αH hom(ℋ, ·) of homomorphism counts from fixed pattern graphs H; this includes (induced) subgraph counts for fixed patterns. Interpreting graph parameters as linear combinations of homomorphism counts has proven to be useful in understanding their computational complexity, as it is known that such linear combinations are as hard to evaluate as their hardest terms, whose complexity in turn is governed by the treewidth of the pattern graph. More formally, given oracle access to a linear combination of homomorphism counts p as above and a graph S with coefficient αs ≠ 0, it is possible to compute hom(S, G) for any n-vertex input graph G in 2|E(S)|poly(s, n) time, where s is the maximum size of graphs in the defining linear combination of p. This reduction runs in polynomial time when p and S are fixed or small in comparison to G; this is the relevant setting in several results based on this reduction.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.74"}, {"primary_key": "705687", "vector": [], "sparse_vector": [], "title": "Strongly Polynomial <PERSON><PERSON> to High Precision.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The frame scaling problem is: given vectors , marginals , and precision ɛ > 0, find left and right scalings such that (v1,…,vn) := (Lu1r1,…, Lunrn) simultaneously satisfies and , up to error ɛ. This problem has appeared in a variety of fields throughout linear algebra and computer science. In this work, we give a strongly polynomial algorithm for frame scaling with log(1/ɛ) convergence. This answers a question of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (STOC 2023), who gave the first strongly polynomial randomized algorithm with poly(1/ɛ) convergence for Forster transformation, the special case . Our algorithm is deterministic, applies for general marginals , and requires O(n3 log(n/ɛ)) iterations as compared to the O(n5d11/ɛ5) iterations of DTK. By lifting the framework of <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (Combinatorica 2000) for matrix scaling to the frame setting, we are able to simplify both the algorithm and analysis. Our main technical contribution is to generalize the potential analysis of LSW to the frame setting and compute an update step in strongly polynomial time that achieves geometric progress in each iteration. In fact, we can adapt our results to give an improved analysis of strongly polynomial matrix scaling, reducing the O(n5 log(n/ɛ)) iteration bound of LSW to O(n3 log(n/ɛ)). Additionally, we give a bound on the size of approximate scaling solutions, which involves condition measure studied in the linear programming literature, and may be of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.36"}, {"primary_key": "705688", "vector": [], "sparse_vector": [], "title": "Grammar Boosting: A New Technique for Proving Lower Bounds for Computation over Compressed Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computation over compressed data is a new paradigm in the design of algorithms and data structures that can reduce space usage and speed up computation by orders of magnitude. One of the most frequently employed compression frameworks, capturing many practical compression methods (such as the <PERSON><PERSON><PERSON>-<PERSON><PERSON> family, dictionary methods, and others), is grammar compression. In this framework, a string T of length N is represented as a context-free grammar of size n whose language contains only the string T. In this paper, we focus on studying the limitations of these techniques. Previous work focused on proving lower bounds for algorithms and data structures operating over grammars constructed using algorithms that achieve the approximation ratio ρ = O (polylog N) (since finding the smallest grammar representation is NP-hard, every polynomial-time grammar compressor can be viewed as an approximation algorithm). Unfortunately, for many grammar compressors we either have ρ = ω (polylog N) or it is not known whether ρ = O(polylog N) holds. In their seminal paper, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and Shelat [IEEE Trans. Inf. Theory 2005] studied seven popular grammar compression algorithms: Re<PERSON>air, Greedy, LongestMatch, Sequential, Bisection, LZ78, and α-Balanced. Only one of them (α-Balanced) is known to achieve ρ = O(polylog N).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.121"}, {"primary_key": "705689", "vector": [], "sparse_vector": [], "title": "Integer Programming with GCD Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the non-linear extension of integer programming with greatest common divisor constraints of the form gcd(f, g) ~ d, where f and g are linear polynomials, d is a positive integer, and ~ is a relation among ≤, = ≠, = and ≥. We show that the feasibility problem for these systems is in NP, and that an optimal solution minimizing a linear objective function, if it exists, has polynomial bit length. To show these results, we identify an expressive fragment of the existential theory of the integers with addition and divisibility that admits solutions of polynomial bit length. It was shown by <PERSON><PERSON><PERSON><PERSON> [Trans. Am. Math. Soc., 235, pp. 271-283, 1978] that this theory adheres to a local-to-global principle in the following sense: a formula Φ is equi-satisfiable with a formula Ψ in this theory such that <PERSON> has a solution if and only if <PERSON> has a solution modulo every prime p. We show that in our fragment, only a polynomial number of primes of polynomial bit length need to be considered, and that the solutions modulo prime numbers can be combined to yield a solution to Φ of polynomial bit length. As a technical by-product, we establish a Chinese-remainder-type theorem for systems of congruences and non-congruences showing that solution sizes do not depend on the magnitude of the moduli of non-congruences.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.128"}, {"primary_key": "705690", "vector": [], "sparse_vector": [], "title": "Edge-Coloring Algorithms for Bounded Degree Multigraphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we consider algorithms for edge-coloring multigraphs G of bounded maximum degree, i.e., Δ (G) = O(1). <PERSON>'s theorem states that any multigraph of maximum degree Δ can be properly edge- colored with ⌊3Δ/2⌋ colors. Our main results include algorithms for computing such colorings. We design deterministic and randomized sequential algorithms with running time O(n log n) and O(n), respectively. This is the first improvement since the O(n2) algorithm in <PERSON>'s original paper, and our randomized algorithm is optimal up to constant factors. We also develop distributed algorithms in the LOCAL model of computation. Namely, we design deterministic and randomized LOCAL algorithms with running time Õ(log5 n) and O(log2 n), respectively. The deterministic sequential algorithm is a simplified extension of earlier work of <PERSON><PERSON><PERSON> et al. in edge-coloring simple graphs. The other algorithms apply the entropy compression method in a similar way to recent work by the author and <PERSON><PERSON><PERSON><PERSON>, where the authors design algorithms for <PERSON><PERSON>'s theorem for simple graphs. We also extend those results to <PERSON><PERSON>'s theorem for multigraphs.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.77"}, {"primary_key": "705691", "vector": [], "sparse_vector": [], "title": "Controlling Tail Risk in Online Ski-Rental.", "authors": ["<PERSON>", "Sungjin Im", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The classical ski-rental problem admits a textbook 2-competitive deterministic algorithm, and a simple randomized algorithm that is e/e-1-competitive in expectation. The randomized algorithm, while optimal in expectation, has a large variance in its performance: it has more than a 37% chance of competitive ratio exceeding 2, and the change of the competitive ratio exceeding n is Θ(1/n)!", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.147"}, {"primary_key": "705692", "vector": [], "sparse_vector": [], "title": "Time-Space Lower Bounds for Bounded-Error Computation in the Random-Query Model.", "authors": ["<PERSON><PERSON>"], "summary": "The random-query model was introduced by <PERSON><PERSON> <PERSON> <PERSON><PERSON> at ITCS 2020 as a new model of space-bounded computation. In this model, a branching program of length T and width 2S attempts to compute a function f : {0, 1}n → {0, 1}. However, instead of receiving direct access to the input bits (x1,…, xn), the input is given in pairs of the form (ij, xij) ∈ {1, …, n} × {0, 1} for j = 1, 2,…, T, where the indices i1,…,iT are chosen at random from a pre-fixed distribution.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.103"}, {"primary_key": "705693", "vector": [], "sparse_vector": [], "title": "The Identity Problem in nilpotent groups of bounded class.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Let G be a unitriangular matrix group of nilpotency class at most ten. We show that the Identity Problem (does a semigroup contain the identity matrix?) and the Group Problem (is a semigroup a group?) are decidable in polynomial time for finitely generated subsemigroups of G. Our decidability results also hold when G is an arbitrary finitely generated nilpotent group of class at most ten. This extends earlier work of <PERSON><PERSON> et al. on commutative matrix groups (SODA'96) and work of <PERSON> et al. on SL(2, ℤ) (SODA'17). Furthermore, we formulate a sufficient condition for the generalization of our results to nilpotent groups of class d > 10. For every such d, we exhibit an effective procedure that verifies this condition in case it is true.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.138"}, {"primary_key": "705694", "vector": [], "sparse_vector": [], "title": "Fast Algorithms for Separable Linear Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON> Ye"], "summary": "In numerical linear algebra, considerable effort has been devoted to obtaining faster algorithms for linear systems whose underlying matrices exhibit structural properties. A prominent success story is the method of generalized nested dissection [<PERSON><PERSON><PERSON>-Tar<PERSON>'79] for separable matrices. On the other hand, the majority of recent developments in the design of efficient linear program (LP) solvers have not leveraged the ideas underlying these faster linear system solvers nor exploited the separable structure of the constraint matrix.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.127"}, {"primary_key": "705695", "vector": [], "sparse_vector": [], "title": "Fast 2-Approximate All-Pairs Shortest Paths.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we revisit the classic approximate All-Pairs Shortest Paths (APSP) problem in undirected graphs. For unweighted graphs, we provide an algorithm for 2-approximate APSP in Õ(n2.5-r + nω(r)) time, for any r ∈ [0,1]. This is O(n2.032) time, using known bounds for rectangular matrix multiplication nω(r) [<PERSON>, Urruti<PERSON>, SODA 2018]. Our result improves on the Õ(n2·25) bound of [<PERSON><PERSON><PERSON>, STOC 2023], and on the bound of [<PERSON><PERSON><PERSON>, <PERSON>, SICOMP 2010] for graphs with m ≥ n1·532 edges.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.169"}, {"primary_key": "705696", "vector": [], "sparse_vector": [], "title": "Edge-disjoint paths in expanders: online with removals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of finding edge-disjoint paths between given pairs of vertices in a sufficiently strong d-regular expander graph G with n vertices. In particular, we describe a deterministic, polynomial time algorithm which maintains an initially empty collection of edge-disjoint paths P in G and fulfills any series of two types of requests:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.161"}, {"primary_key": "705697", "vector": [], "sparse_vector": [], "title": "The Grid-Minor Theorem Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove that for every planar graph X of treedepth h, there exists a positive integer c such that for every X-minor-free graph G, there exists a graph H of treewidth at most f (h) such that G is isomorphic to a subgraph of H ⊠ Kc. This is a qualitative strengthening of the Grid-Minor Theorem of <PERSON> and <PERSON> (<PERSON>, 1986), and treedepth is the optimal parameter in such a result. As an example application, we use this result to improve the upper bound for weak coloring numbers of graphs excluding a given graph as a minor.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.48"}, {"primary_key": "705698", "vector": [], "sparse_vector": [], "title": "Combinatorial Contracts Beyond Gross Substitutes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the combinatorial contracting problem of <PERSON><PERSON><PERSON> et al. [13], in which a principal seeks to incentivize an agent to take a set of costly actions. In their model, there is a binary outcome (the agent can succeed or fail), and the success probability and the costs depend on the set of actions taken. The optimal contract is linear, paying the agent an α fraction of the reward. For gross substitutes (GS) rewards and additive costs, they give a poly-time algorithm for finding the optimal contract. They use the properties of GS functions to argue that there are poly-many \"critical values\" of α, and that one can iterate through all of them efficiently in order to find the optimal contract.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.5"}, {"primary_key": "705699", "vector": [], "sparse_vector": [], "title": "Determinantal Sieving.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new, remarkably powerful tool to the toolbox of algebraic FPT algorithms, determinantal sieving. Given a polynomial P (x1,…,xn) over a field 𝔽 of characteristic 2, on a set of variables X = [x1,. ..,xn}, and a linear matroid M = (X, I) over 𝔽 of rank k, in 2k evaluations of P we can sieve for those terms in the monomial expansion of P which are multilinear and whose support is a basis for M. The known tools of multilinear detection and constrained multilinear detection then correspond to the case where M is a uniform matroid and the truncation of a disjoint union of uniform matroids, respectively. More generally, let the odd support of a monomial m be the set of variables which have odd degree in m. Using 2k evaluations of P, we can sieve for those terms m whose odd support spans M. Applying this framework to well-known efficiently computable polynomial families allows us to simplify, generalize and improve on a range of algebraic FPT algorithms, such as:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.16"}, {"primary_key": "705700", "vector": [], "sparse_vector": [], "title": "Deterministic Byzantine Agreement with Adaptive O(n · f) Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a deterministic synchronous protocol for binary Byzantine Agreement against a corrupt minority with adaptive O(n · f) communication complexity, where f is the exact number of corruptions. Our protocol improves the previous best-known deterministic Byzantine Agreement protocol developed by <PERSON>ose and Ren (DISC 2021), whose communication complexity is quadratic, independent of the exact number of corruptions. Our approach combines two distinct primitives that we introduce and implement with O(n · f) communication, Reliable Voting and Weak Byzantine Agreement. In Reliable Voting, all honest parties agree on the same value only if all honest parties start with that value, but there is no agreement guarantee in the general case. In Weak Byzantine Agreement we achieve agreement, but validity requires that the inputs to the protocol satisfy certain properties. Our Weak Byzantine Agreement protocol is an adaptation of the recent Cohen et al. protocol (OPODIS 2022), in which we identify and address various issues.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.43"}, {"primary_key": "705701", "vector": [], "sparse_vector": [], "title": "Optimally Repurposing Existing Algorithms to Obtain Exponential-Time Approximations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The goal of this paper is to understand how exponential-time approximation algorithms can be obtained from existing polynomial-time approximation algorithms, existing parameterized exact algorithms, and existing parameterized approximation algorithms. More formally, we consider a monotone subset minimization problem over a universe of size n (e.g., VERTEX COVER or FEEDBACK VERTEX Set). We have access to an algorithm that finds an α-approximate solution in time ck · nO(1) if a solution of size k exists (and more generally, an extension algorithm that can approximate in a similar way if a set can be extended to a solution with k further elements). Our goal is to obtain a dn · nO(1) time β-approximation algorithm for the problem with d as small as possible. That is, for every fixed α,c,β ≥ 1, we would like to determine the smallest possible d that can be achieved in a model where our problem-specific knowledge is limited to checking the feasibility of a solution and invoking the α-approximate extension algorithm. Our results completely resolve this question:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.13"}, {"primary_key": "705702", "vector": [], "sparse_vector": [], "title": "Rationality-Robust Information Design: Bayesian Persuasion under Quantal Response.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Classic mechanism/information design imposes the assumption that agents are fully rational, meaning each of them always selects the action that maximizes her expected utility. Yet many empirical evidence suggests that human decisions may deviate from this full rationality assumption. In this work, we attempt to relax the full rationality assumption with bounded rationality. Specifically, we formulate the bounded rationality of an agent by adopting the quantal response model (<PERSON><PERSON> and <PERSON>, 1995).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.19"}, {"primary_key": "705703", "vector": [], "sparse_vector": [], "title": "On Deterministically Approximating Total Variation Distance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Total variation distance (TV distance) is an important measure for the difference between two distributions.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.70"}, {"primary_key": "705704", "vector": [], "sparse_vector": [], "title": "Deterministic Sparse Pattern Matching via the Baur-Strassen Theorem.", "authors": ["<PERSON>"], "summary": "How fast can you test whether a constellation of stars appears in the night sky? This question can be modeled as the computational problem of testing whether a set of points P can be moved into (or close to) another set Q under some prescribed group of transformations. Problems of this kind are subject to intensive study in computational geometry and enjoy countless theoretical and practical applications.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.119"}, {"primary_key": "705705", "vector": [], "sparse_vector": [], "title": "The Effect of Sparsity on k-Dominating Set and Related First-Order Graph Properties.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit the classic k-Dominating Set problem. Besides its importance as perhaps the most natural W[2]-complete problem, it is among the first problems for which a tight nk-o(1) conditional lower bound (for all sufficiently large k), based on the Strong Exponential Time Hypothesis (SETH), was shown (<PERSON><PERSON><PERSON> and <PERSON>, SODA 2007). Notably, however, the underlying reduction creates dense graphs, raising the question: how much does the sparsity of the graph affect its fine-grained complexity?", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.168"}, {"primary_key": "705706", "vector": [], "sparse_vector": [], "title": "On Approximability of Steiner Tree in ℓp-metrics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> C. S."], "summary": "In the Continuous Steiner Tree problem (CST), we are given as input a set of points (called terminals) in a metric space and ask for the minimum-cost tree connecting them. Additional points (called Steiner points) from the metric space can be introduced as nodes in the solution. In the Discrete Steiner Tree problem (DST), we are given in addition to the terminals, a set of facilities, and any solution tree connecting the terminals can only contain the Steiner points from this set of facilities.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.67"}, {"primary_key": "705707", "vector": [], "sparse_vector": [], "title": "A Distributed Palette Sparsification Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The celebrated palette sparsification result of [<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> SODA'19] shows that to compute a Δ + 1 coloring of the graph, where Δ denotes the maximum degree, it suffices if each node limits its color choice to O(log n) independently sampled colors in {1, 2,…, Δ + 1}. They showed that it is possible to color the resulting sparsified graph—the spanning subgraph with edges between neighbors that sampled a common color, which are only Õ(n) edges—and obtain a Δ + 1 coloring for the original graph. However, to compute the actual coloring, that information must be gathered at a single location for centralized processing. We seek instead a local algorithm to compute such a coloring in the sparsified graph. The question is if this can be achieved in poly (log n) distributed rounds with small messages.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.142"}, {"primary_key": "705708", "vector": [], "sparse_vector": [], "title": "Tree Containment Above Minimum Degree is FPT.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "According to the classic <PERSON><PERSON><PERSON><PERSON>'s Lemma from 1977, a graph of minimum degree δ(G) contains every tree on δ(G) + 1 vertices. Our main result is the following algorithmic \"extension\" of <PERSON><PERSON><PERSON><PERSON>'s Lemma: For any n-vertex graph G, integer k, and a tree T on at most δ(G) + k vertices, deciding whether G contains a subgraph isomorphic to T, can be done in time f (k) · nO(1) for some function f of k only.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.15"}, {"primary_key": "705709", "vector": [], "sparse_vector": [], "title": "Learning Hard-Constrained Models with <PERSON> Sample.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of estimating the parameters of a Markov Random Field with hard-constraints using a single sample. As our main running examples, we use the k-SAT and the proper coloring models, as well as general H-coloring models; for all of these we obtain both positive and negative results. In contrast to the soft-constrained case, we show in particular that single-sample estimation is not always possible, and that the existence of an estimator is related to the existence of non-satisfiable instances.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.114"}, {"primary_key": "705710", "vector": [], "sparse_vector": [], "title": "Faster Rectangular Matrix Multiplication by Combination Loss Analysis.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON> and <PERSON> (FOCS 2023) recently obtained the improved upper bound on the exponent of square matrix multiplication ω < 2.3719 by introducing a new approach to quantify and compensate the \"combination loss\" in prior analyses of powers of the Coppersmith-Winograd tensor. In this paper we show how to use this new approach to improve the exponent of rectangular matrix multiplication as well. Our main technical contribution is showing how to combine this analysis of the combination loss and the analysis of the fourth power of the Coppersmith-Winograd tensor in the context of rectangular matrix multiplication developed by <PERSON> and <PERSON><PERSON> (SODA 2018).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.133"}, {"primary_key": "705711", "vector": [], "sparse_vector": [], "title": "Oracle Efficient Online Multicalibration and Omniprediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A recent line of work has shown a surprising connection between multicalibration, a multi- group fairness notion, and omniprediction, a learning paradigm that provides simultaneous loss minimization guarantees for a large family of loss functions [20, 19, 21, 18]. Prior work studies omniprediction in the batch setting. We initiate the study of omniprediction in the online adversarial setting. Although there exist algorithms for obtaining notions of multicalibration in the online adversarial setting [23], unlike batch algorithms, they work only for small finite classes of benchmark functions F, because they require enumerating every function f ∈ F at every round. In contrast, omniprediction is most interesting for learning theoretic hypothesis classes F, which are generally continuously (or at least exponentially) large.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.98"}, {"primary_key": "705712", "vector": [], "sparse_vector": [], "title": "Bandit Algorithms for Prophet Inequality and Pandora&apos;s Box.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Prophet Inequality and Pandora's Box problems are fundamental stochastic problem with applications in Mechanism Design, Online Algorithms, Stochastic Optimization, Optimal Stopping, and Operations Research. A usual assumption in these works is that the probability distributions of the n underlying random variables are given as input to the algorithm. Since in practice these distributions need to be learned under limited feedback, we initiate the study of such stochastic problems in the Multi-Armed Bandits model.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.18"}, {"primary_key": "705713", "vector": [], "sparse_vector": [], "title": "Exact Community Recovery in the Geometric SBM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of exact community recovery in the Geometric Stochastic Block Model (GSBM), where each vertex has an unknown community label as well as a known position, generated according to a Poisson point process in ℝd. Edges are formed independently conditioned on the community labels and positions, where vertices may only be connected by an edge if they are within a prescribed distance of each other. The GSBM thus favors the formation of dense local subgraphs, which commonly occur in real-world networks, a property that makes the GSBM qualitatively very different from the standard Stochastic Block Model (SBM). We propose a linear-time algorithm for exact community recovery, which succeeds down to the information-theoretic threshold, confirming a conjecture of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. The algorithm involves two phases. The first phase exploits the density of local subgraphs to propagate estimated community labels among sufficiently occupied subregions, and produces an almost-exact vertex labeling. The second phase then refines the initial labels using a Poisson testing procedure. Thus, the GSBM enjoys local to global amplification just as the SBM, with the advantage of admitting an information-theoretically optimal, linear-time algorithm.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.78"}, {"primary_key": "705714", "vector": [], "sparse_vector": [], "title": "A Parameterized Family of Meta-Submodular Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Submodular function maximization has found a wealth of new applications in recent years. The related supermodular maximization models also offer an abundance of applications, but they appeared to be highly intractable even under simple cardinality constraints. Hence, while there are well-developed tools for maximizing a submodular function subject to a matroid constraint, there is much less work on the corresponding supermodular maximization problems.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.51"}, {"primary_key": "705715", "vector": [], "sparse_vector": [], "title": "New Approximation Bounds for Small-Set Vertex Expansion.", "authors": ["Suprovat Ghoshal", "<PERSON>"], "summary": "The vertex expansion of graph is a fundamental graph parameter. Given a graph G = (V, E) and a parameter δ ∈ (0, 1/2], its δ-SSVE is defined as", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.84"}, {"primary_key": "705716", "vector": [], "sparse_vector": [], "title": "Near-Optimal Quantum Algorithms for Bounded Edit Distance and Lempel-Ziv Factorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Measuring sequence similarity and compressing texts are among the most fundamental tasks in string algorithms. In this work, we develop near-optimal quantum algorithms for the central problems in these two areas: computing the edit distance of two strings [<PERSON><PERSON><PERSON><PERSON>, 1965] and building the Le<PERSON>el-<PERSON>iv factorization of a string [<PERSON><PERSON> & <PERSON>, 1977], respectively.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.118"}, {"primary_key": "705717", "vector": [], "sparse_vector": [], "title": "Sub-Exponential Lower Bounds for Branch-and-Bound with General Disjunctions via Interpolation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper investigates linear programming based branch-and-bound using general disjunctions, also known as stabbing planes, for solving integer programs. We derive the first sub-exponential lower bound (in the encoding length L of the integer program) for the size of a general branch-and-bound tree for a particular class of (compact) integer programs, namely 2Ω(L1/12-ɛ) for every ɛ > 0. This is achieved by showing that general branch-and-bound admits quasi-feasible monotone real interpolation, which allows us to utilize sub-exponential lower-bounds for monotone real circuits separating the so-called clique-coloring pair. The same ideas also prove that refuting Θ(log(n))-CNFs requires size 2nΩ(1) branch-and-bound trees with high probability by considering the closely related notion of infeasibility certificates introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [18]. One important ingredient of the proof of our interpolation result is that for every general branch-and-bound tree proving integer-freeness of a product P × Q of two polytopes P and Q, there exists a closely related branch-and-bound tree for showing integer-freeness of P or one showing integer-freeness of Q. Moreover, we prove that monotone real circuits can perform binary search efficiently.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.132"}, {"primary_key": "705718", "vector": [], "sparse_vector": [], "title": "New Explicit Constant-Degree Lossless Expanders.", "authors": ["<PERSON>"], "summary": "We present a new explicit construction of onesided bipartite lossless expanders of constant degree, with arbitrary constant ratio between the sizes of the two vertex sets. Our construction is simpler to state and analyze than the only prior construction of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2002), and achieves improved parameters.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.177"}, {"primary_key": "705719", "vector": [], "sparse_vector": [], "title": "Revenue Maximization for Buyers with Costly Participation.", "authors": ["Yannai <PERSON><PERSON>", "<PERSON>", "Yingkai Li", "<PERSON>"], "summary": "We study mechanisms for selling a single item when buyers have private costs for participating in the mechanism. An agent's participation cost can also be interpreted as an outside option value that she must forego to participate. This substantially changes the revenue maximization problem, which becomes non- convex in the presence of participation costs. For multiple buyers, we show how to construct a (2 + ɛ)- approximately revenue-optimal mechanism in polynomial time. Our approach makes use of a many-buyers-to-single-buyer reduction, and in the single-buyer case our mechanism improves to an FPTAS. We also bound the menu size and the sample complexity for the optimal single-buyer mechanism. Moreover, we show that posting a single price in the single-buyer case is in fact optimal under the assumption that either (1) the participation cost is independent of the value, and the value distribution has decreasing marginal revenue or monotone hazard rate; or (2) the participation cost is a concave function of the value. When there are multiple buyers, we show that sequential posted pricing guarantees a large fraction of the optimal revenue under similar conditions.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.3"}, {"primary_key": "705720", "vector": [], "sparse_vector": [], "title": "Set Covering with Our Eyes Wide Shut.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the stochastic set cover problem (<PERSON><PERSON> et al., FOCS '08), we are given a collection S of m sets over a universe U of size N, and a distribution D over elements of U. The algorithm draws n elements one-by-one from D and must buy a set to cover each element on arrival; the goal is to minimize the total cost of sets bought during this process. A universal algorithm a priori maps each element u ∈ U to a set S(u) such that if U ⊆ U is formed by drawing n times from distribution D, then the algorithm commits to outputting S(U). <PERSON><PERSON> et al. gave an O(log mN)-competitive universal algorithm for this stochastic set cover problem.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.160"}, {"primary_key": "705721", "vector": [], "sparse_vector": [], "title": "Poly-logarithmic Competitiveness for the k-Taxi Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The online k-taxi problem generalizes the k-server problem, requiring servers to move between source-sink pairs in an n-point metric space, and the cost is the overhead incurred. In the deterministic setting, the problem has a lower bound on the competitiveness of Ω(2k), showing that it is significantly harder than k-server. Randomized algorithms are known with competitiveness O(2k log n) (by <PERSON><PERSON> and <PERSON><PERSON>), (by <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>), where Δ is the aspect ratio of the n-point metric space), and O((n log k)2 log n) (by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>). The best lower bound known is Ω(log2 k) which is inherited from the k-server problem, obtained in a recent breakthrough by <PERSON><PERSON><PERSON>, <PERSON>ester, and <PERSON><PERSON><PERSON>, showing a large gap in our understanding of problems that go slightly beyond the metrical task system framework.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.146"}, {"primary_key": "705722", "vector": [], "sparse_vector": [], "title": "Prior-Independent Auctions for Heterogeneous Bidders.", "authors": ["<PERSON>", "Aranyak <PERSON>hta", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the design of prior-independent auctions in a setting with heterogeneous bidders. In particular, we consider the setting of selling to n bidders whose values are drawn from n independent but not necessarily identical distributions. We work in the robust auction design regime, where we assume the seller has no knowledge of the bidders' value distributions and must design a mechanism that is prior-independent. While there have been many strong results on prior-independent auction design in the i.i.d. setting, not much is known for the heterogeneous setting, even though the latter is of significant practical importance. Unfortunately, no prior-independent mechanism can hope to always guarantee any approximation to <PERSON><PERSON>'s revenue in the heterogeneous setting; similarly, no prior-independent mechanism can consistently do better than the second-price auction. In light of this, we design a family of (parametrized) randomized auctions which approximates at least one of these benchmarks: For heterogeneous bidders with regular value distributions, our mechanisms either achieve a good approximation of the expected revenue of an optimal mechanism (which knows the bidders' distributions) or exceeds that of the second-price auction by a certain multiplicative factor. The factor in the latter case naturally trades off with the approximation ratio of the former case. We show that our mechanism is optimal for such a trade-off between the two cases by establishing a matching lower bound. Our result extends to selling k identical items to heterogeneous bidders with an additional O(ln2 k)-factor in our trade-off between the two cases.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.1"}, {"primary_key": "705723", "vector": [], "sparse_vector": [], "title": "Improved Roundtrip Spanners, Emulators, and Directed Girth Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON>"], "summary": "Roundtrip spanners are the analog of spanners in directed graphs, where the roundtrip metric is used as a notion of distance. Recent works have shown existential results of roundtrip spanners nearly matching the undirected case, but the time complexity for constructing roundtrip spanners is still widely open.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.166"}, {"primary_key": "705724", "vector": [], "sparse_vector": [], "title": "Randomized Communication and Implicit Representations for Matrices and Graphs of Small Sign-Rank.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove a characterization of the structural conditions on matrices of sign-rank 3 and unit disk graphs (UDGs) which permit constant-cost public-coin randomized communication protocols. Therefore, under these conditions, these graphs also admit implicit representations.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.72"}, {"primary_key": "705725", "vector": [], "sparse_vector": [], "title": "Dependent rounding with strong negative-correlation, and scheduling on unrelated machines to minimize completion time.", "authors": ["<PERSON>"], "summary": "We describe a new dependent-rounding algorithmic framework for bipartite graphs. Given a fractional assignment y of values to edges of graph G = (U ∪ V,E) the algorithms return an integral solution Y such that each right-node v ∈ V has at most one neighboring edge f with Yf = 1, and where the variables Ye also satisfy broad nonpositive-correlation properties. In particular, for any edges e1,e2 sharing a left-node u ∈ U, the variables Ye1, Ye2 have strong negative-correlation properties, i.e. the expectation of Ye1 Ye2 is significantly below ye1 ye2.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.81"}, {"primary_key": "705726", "vector": [], "sparse_vector": [], "title": "Cactus Representations in Polylogarithmic Max-flow via Maximal Isolating Mincuts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "A cactus representation of a graph, introduced by <PERSON><PERSON> et al. in 1976, is an edge sparsifier of O(n) size that exactly captures all global minimum cuts of the graph. It is a central combinatorial object that has been a key ingredient in almost all algorithms for the connectivity augmentation problems and for maintaining minimum cuts under edge insertions (e.g. [<PERSON><PERSON> et al. SICOMP'97], [<PERSON><PERSON> et al. SODA'22], [He<PERSON>inger ICALP'95]). This sparsifier was generalized to Steiner cactus for a vertex set T, which can be seen as a vertex sparsifier of O(|T|) size that captures all partitions of T corresponding to a T-Steiner minimum cut, and also hypercactus, an analogous concept in hypergraphs. These generalizations further extend the applications of cactus to the Steiner and hypergraph settings.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.60"}, {"primary_key": "705727", "vector": [], "sparse_vector": [], "title": "Cactus Representation of Minimum Cuts: Derandomize and Speed up.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "Given an undirected weighted graph with n vertices and m edges, we give the first deterministic m1+o(1)-time algorithm for constructing the cactus representation of all global minimum cuts. This improves the current n2+o(1)-time state-of-the-art deterministic algorithm, which can be obtained by combining ideas implicitly from three papers [22, 27, 12]. The known explicitly stated deterministic algorithm has a runtime of Õ(mn) [9, 34]. Using our technique, we can even speed up the fastest randomized algorithm of [23] whose running time is at least Ω(m log4 n) to O(m log3 n).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.61"}, {"primary_key": "705728", "vector": [], "sparse_vector": [], "title": "Bin Packing under Random-Order: Breaking the Barrier of 3/2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "K. V. N. <PERSON>"], "summary": "Best-Fit is one of the most prominent and practically used algorithms for the bin packing problem, where a set of items with associated sizes needs to be packed in the minimum number of unit-capacity bins. <PERSON><PERSON> [SODA '96] studied online bin packing under random-order arrival, where the adversary chooses the list of items, but the items arrive one by one according to an arrival order drawn uniformly randomly from the set of all permutations of the items. <PERSON><PERSON>'s seminal result established an upper bound of $1.5$ and a lower bound of $1.08$ on the random-order ratio of Best-Fit, and it was conjectured that the true ratio is $\\approx 1.15$. The conjecture, if true, will also imply that Best-Fit (on randomly permuted input) has the best performance guarantee among all the widely-used simple algorithms for (offline) bin packing. This conjecture has remained one of the major open problems in the area, as highlighted in the recent survey on random-order models by <PERSON> and <PERSON> [Beyond the Worst-Case Analysis of Algorithms '20]. Recently, <PERSON><PERSON> et al. [Algorithmica '21] improved the upper bound to $1.25$ for the special case when all the item sizes are greater than $1/3$, and they improve the lower bound to $1.1$. <PERSON><PERSON><PERSON><PERSON><PERSON> et al. [ICALP '22] obtained an improved result for the special case when all the item sizes lie in $(1/4, 1/2]$, which corresponds to the $3$-partition problem. The upper bound of $3/2$ for the general case, however, has remained unimproved. In this paper, we make the first progress towards the conjecture, by showing that Best-Fit achieves a random-order ratio of at most $1.5 - \\varepsilon$, for a small constant $\\varepsilon>0$. Furthermore, we establish an improved lower bound of $1.144$ on the random-order ratio of Best-Fit, nearly reaching the conjectured ratio.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.145"}, {"primary_key": "705729", "vector": [], "sparse_vector": [], "title": "Deterministic Near-Linear Time Minimum Cut in Weighted Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In 1996, <PERSON><PERSON> [<PERSON>r96] gave a startling randomized algorithm that finds a minimum-cut in a (weighted) graph in time O(m log3 n) which he termed near-linear time meaning linear (in the size of the input) times a polylogarthmic factor. In this paper, we give the first deterministic algorithm which runs in near-linear time for weighted graphs.Previously, the breakthrough results of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [<PERSON>T19] gave a near-linear time algorithm for simple graphs (which was improved to have running time O(m log2 n log log n) in [HRW20].) The main technique here is a clustering procedure that perfectly preserves minimum cuts. Recently, <PERSON> [<PERSON><PERSON>] gave an m1+o(1) deterministic minimum-cut algorithm for weighted graphs; this form of running time has been termed \"almost-linear\". <PERSON> uses almost-linear time deterministic expander decompositions which do not perfectly preserve minimum cuts, but he can use these clusterings to, in a sense, \"derandomize\" the methods of <PERSON><PERSON>.In terms of techniques, we provide a structural theorem that says there exists a sparse clustering that preserves minimum cuts in a weighted graph with o(1) error. In addition, we construct it deterministically in near linear time. This was done exactly for simple graphs in [KT19, HRW20] and with polylogarithmic error for weighted graphs in [Li21]. Extending the techniques in [KT19, HRW20] to weighted graphs presents significant challenges, and moreover, the algorithm can only polylogarithmically approximately preserve minimum cuts. A remaining challenge is to reduce the polylogarithmic-approximate clusterings to 1 + o(1/ log n)-approximate so that they can be applied recursively as in [Li21] over O(log n) many levels. This is an additional challenge that requires building on properties of tree-packings in the presence of a wide range of edge weights to, for example, find sources for local flow computations which identify minimum cuts that cross clusters.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.111"}, {"primary_key": "705730", "vector": [], "sparse_vector": [], "title": "A Unifying Framework for Differentially Private Sums under Continual Observation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the problem of maintaining a differentially private decaying sum under continual observation. We give a unifying framework and an efficient algorithm for this problem for any sufficiently smooth function. Our algorithm is the first differentially private algorithm that does not have a multiplicative error for polynomially decaying weights. Our algorithm improves on all prior works on differentially private decaying sums under continual observation and recovers exactly the additive error for the special case of continual counting from <PERSON><PERSON><PERSON> et al. (SODA 2023) as a corollary.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.38"}, {"primary_key": "705731", "vector": [], "sparse_vector": [], "title": "New SDP Roundings and Certifiable Approximation for Cubic Optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give new rounding schemes for SDP relaxations for the problems of maximizing cubic polynomials over the unit sphere and the n-dimensional hypercube. In both cases, the resulting algorithms yield a multiplicative approximation in 2O(k) poly(n) time. In particular, we obtain a approximation in polynomial time. For the unit sphere, this improves on the rounding algorithms of [5] that need quasi-polynomial time to obtain a similar approximation guarantee. Over the n-dimensional hypercube, our results match the guarantee of a search algorithm of K<PERSON> and Naor [19] that obtains a similar approximation ratio via techniques from convex geometry. Unlike their method, our algorithm obtains an upper bound on the integrality gap of SDP relaxations for the problem and as a result, also yields a certificate on the optimum value of the input instance. Our results naturally generalize to homogeneous polynomials of higher degree and imply improved algorithms for approximating satisfiable instances of Max-3SAT.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.83"}, {"primary_key": "705732", "vector": [], "sparse_vector": [], "title": "Robust Sparsification for Matroid Intersection with Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Matroid intersection is a classical optimization problem where, given two matroids over the same ground set, the goal is to find the largest common independent set. In this paper, we show that there exists a certain \"sparsifer\": a subset of elements, of size O(|Sopt| · 1/ɛ), where Sopt denotes the optimal solution, that is guaranteed to contain a 3/2 + ɛ approximation, while guaranteeing certain robustness properties. We call such a small subset a Density Constrained Subset (DCS), which is inspired by the Edge-Degree Constrained, Subgraph, (EDCS) [<PERSON> and <PERSON>, 2015], originally designed for the maximum cardinality matching problem in a graph. Our proof is constructive and hinges on a greedy decomposition of matroids, which we call the density-based decomposition. We show that this sparsifier has certain robustness properties that can be used in one-way communication and random-order streaming models.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.104"}, {"primary_key": "705733", "vector": [], "sparse_vector": [], "title": "Positivity Certificates for Linear Recurrences.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider linear recurrences with polynomial coefficients of Poincar<PERSON> type and with a unique simple dominant eigenvalue. We give an algorithm that proves or disproves positivity of solutions provided the initial conditions satisfy a precisely defined genericity condition. For positive sequences, the algorithm produces a certificate of positivity that is a data-structure for a proof by induction. This induction works by showing that an explicitly computed cone is contracted by the iteration of the recurrence.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.37"}, {"primary_key": "705734", "vector": [], "sparse_vector": [], "title": "A Nearly Linear-Time Distributed Algorithm for Exact Maximum Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a randomized Õ(µ(G))-round algorithm for the maximum cardinality matching problem in the CONGEST model, where µ(G) means the maximum size of a matching of the input graph G. The proposed algorithm substantially improves the current best worst-case running time. The key technical ingredient is a new randomized algorithm of finding an augmenting path of length ℓ with high probability within Õ(ℓ) rounds, which positively settles an open problem left in the prior work by <PERSON><PERSON> and <PERSON> [DISC'20].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.141"}, {"primary_key": "705735", "vector": [], "sparse_vector": [], "title": "Optimal thresholds for Latin squares, Steiner Triple Systems, and edge colorings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a graph G, a random (k, n)-list assignment L for edges of G is an assignment of an independent, uniformly random set of colors to each edge e and a proper L-list coloring of G is a proper edge-coloring where the color of an edge e belongs to L(e). We show that for a random (O(log n), n)-list assignment L for edges of the complete bipartite graph Kn,n, there is a an L-list coloring of Kn,n with high probability. We also prove analogous results for the thresholds of Steiner triple systems and Latin squares in random (binomial) hypergraphs. All of our results are optimal up to absolute constants, and resolve several related conjectures of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.57"}, {"primary_key": "705736", "vector": [], "sparse_vector": [], "title": "Linear-Sized Sparsifiers via Near-Linear Time Discrepancy Theory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Discrepancy theory has provided powerful tools for producing higher-quality objects which \"beat the union bound\" in fundamental settings throughout combinatorics and computer science. However, this quality has often come at the price of more computationally-expensive algorithms. We introduce a new framework for bridging this gap, by allowing for the efficient implementation of discrepancy-theoretic primitives. Our framework repeatedly solves regularized optimization problems to low accuracy to approximate the partial coloring method of [Rot17], and simplifies and generalizes recent work of [JSS23] on fast algorithms for <PERSON>'s theorem. In particular, our framework only requires that the discrepancy body of interest has exponentially large Gaussian measure and is expressible as a sublevel set of a symmetric, convex function. We combine this framework with new tools for proving Gaussian measure lower bounds to give improved algorithms for a variety of sparsification and coloring problems.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.186"}, {"primary_key": "705737", "vector": [], "sparse_vector": [], "title": "Massively Parallel Algorithms for High-Dimensional Euclidean Minimum Spanning Tree.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the classic Euclidean Minimum Spanning Tree (MST) problem in the Massively Parallel Computation (MPC) model. Given a set X ⊂ ℝd of n points, the goal is to produce a spanning tree for X with weight within a small factor of optimal. Euclidean MST is one of the most fundamental hierarchical geometric clustering algorithms, and with the proliferation of enormous high-dimensional data sets, such as massive transformer-based embeddings, there is now a critical demand for efficient distributed algorithms to cluster such data sets.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.139"}, {"primary_key": "705738", "vector": [], "sparse_vector": [], "title": "The Hierarchy of Hereditary Sorting Operators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following general model of a sorting procedure: we fix a hereditary permutation class C, which corresponds to the operations that the procedure is allowed to perform in a single step. The input of sorting is a permutation π of the set [n] = {1, 2,…,n}, i.e., a sequence where each element of [n] appears once. In every step, the sorting procedure picks a permutation σ of length n from C, and rearranges the current permutation of numbers by composing it with σ. The goal is to transform the input π into the sorted sequence 1, 2,…,n in as few steps as possible.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.59"}, {"primary_key": "705739", "vector": [], "sparse_vector": [], "title": "Convex Minimization with Integer Minima in Õ(n4) Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a convex function f on ℝn with an integer minimizer, we show how to find an exact minimizer of f using O(n2 log n) calls to a separation oracle and O(n4 log n) time. The previous best polynomial time algorithm for this problem given in [Jiang, SODA 2021, JACM 2022] achieves O(n2 log log n/ log n) oracle complexity. However, the overall runtime of <PERSON>'s algorithm is at least , due to expensive sub-routines such as the Lenstra-Lenstra-Lovász (LLL) algorithm [<PERSON>, <PERSON>, <PERSON>, Math. Ann. 1982] and random walk based cutting plane method [<PERSON>si<PERSON>, <PERSON>empal<PERSON>, JACM 2004]. Our significant speedup is obtained by a nontrivial combination of a faster version of the LLL algorithm due to [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, ISSAC 2016] that gives similar guarantees, the volumetric center cutting plane method (CPM) by [<PERSON><PERSON><PERSON>, FOCS 1989] and its fast implementation given in [<PERSON>, <PERSON>, <PERSON>, <PERSON>, STOC 2020].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.129"}, {"primary_key": "705740", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Min-Cut of Superconstant Size in Subpolynomial Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a deterministic fully dynamic algorithm with subpolynomial worst-case time per graph update such that after processing each update of the graph, the algorithm outputs a minimum cut of the graph if the graph has a cut of size at most c for some c = (log n)o(1). Previously, the best update time was Oe(√n) for any c > 2 and c = O(log n) [28].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.107"}, {"primary_key": "705741", "vector": [], "sparse_vector": [], "title": "Cliquewidth and Dimension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that every poset with bounded cliquewidth and with sufficiently large dimension contains the standard example of dimension k as a subposet. This applies in particular to posets whose cover graphs have bounded treewidth, as the cliquewidth of a poset is bounded in terms of the treewidth of the cover graph. For the latter posets, we prove a stronger statement: every such poset with sufficiently large dimension contains the Kelly example of dimension k as a subposet. Using this result, we obtain a full characterization of the minor-closed graph classes C such that posets with cover graphs in C have bounded dimension: they are exactly the classes excluding the cover graph of some Kelly example. Finally, we consider a variant of poset dimension called Boolean dimension, and we prove that posets with bounded cliquewidth have bounded Boolean dimension.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.58"}, {"primary_key": "705742", "vector": [], "sparse_vector": [], "title": "Online Robust Mean Estimation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This We study the problem of high-dimensional robust mean estimation in an online setting. Specifically, we consider a scenario where n sensors are measuring some common, ongoing phenomenon. At each time step t = 1, 2,. ., T, the ith sensor reports its readings for that time step. The algorithm must then commit to its estimate μt for the true mean value of the process at time t. We assume that most of the sensors observe independent samples from some common distribution X, but an ɛ-fraction of them may instead behave maliciously. The algorithm wishes to compute a good approximation μ to the true mean μ* := E[X]. We note that if the algorithm is allowed to wait until time T to report its estimate, this reduces to the well-studied problem of robust mean estimation. However, the requirement that our algorithm produces partial estimates as the data is coming in substantially complicates the situation.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.115"}, {"primary_key": "705743", "vector": [], "sparse_vector": [], "title": "Max s, t-Flow Oracles and Negative Cycle Detection in Planar Digraphs.", "authors": ["<PERSON>"], "summary": "We study the maximum s, t-flow oracle problem on planar directed graphs where the goal is to design a data structure answering max s, t-flow value (or equivalently, min s, t-cut value) queries for arbitrary source- target pairs (s, t). For the case of polynomially bounded integer edge capacities, we describe an exact max s, t-flow oracle with truly subquadratic space and preprocessing, and sublinear query time. Moreover, if (1 — ɛ)-approximate answers are acceptable, we obtain a static oracle with near-linear preprocessing and Õ(n3/4) query time and a dynamic oracle supporting edge capacity updates and queries in Õ(n6/7) worst-case time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.64"}, {"primary_key": "705744", "vector": [], "sparse_vector": [], "title": "Exact Shortest Paths with Rational Weights on the Word RAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Exact computation of shortest paths in weighted graphs has been traditionally studied in one of two settings. First, one can assume that the edge weights are real numbers and all the performed operations on reals (typically comparisons and additions) take constant time. Classical <PERSON><PERSON>'s and Bellman-<PERSON> algorithms have been described in this setting.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.92"}, {"primary_key": "705745", "vector": [], "sparse_vector": [], "title": "Arborescences, Colorful Forests, and Popularity.", "authors": ["Telike<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Our input is a directed, rooted graph G = (V ∪ {r}, E) where each vertex in V has a partial order preference over its incoming edges. The preferences of a vertex extend naturally to preferences over arborescences rooted at r. We seek a popular arborescence in G, i.e., one for which there is no \"more popular\" arborescence. Popular arborescences have applications in liquid democracy or collective decision making; however, they need not exist in every input instance. The popular arborescence problem is to decide if a given input instance admits a popular arborescence or not. We show a polynomial-time algorithm for this problem, whose computational complexity was not known previously.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.131"}, {"primary_key": "705746", "vector": [], "sparse_vector": [], "title": "Code Sparsification and its Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> (<PERSON>) Putterman", "Madhu <PERSON>"], "summary": "We introduce a notion of code sparsification that generalizes the notion of cut sparsification in graphs. For a (linear) code C ⊆ 𝔽nq of dimension k a (1 ± ɛ)-sparsification of size s is given by a weighted set S ⊆ [n] with |S| ≤ s such that for every codeword c ∈ C the projection c|s of c to the set S has (weighted) hamming weight which is a (1 ± ɛ) approximation of the hamming weight of c. We show that for every code there exists a (1 ± ɛ)-sparsification of size s = Õ(k log(q)/ɛ2). This immediately implies known results on graph and hypergraph cut sparsification up to polylogarithmic factors (with a simple unified proof) — the former follows from the well-known fact that cuts in a graph form a linear code over 𝔽2, while the latter is obtained by a simple encoding of hypergraph cuts. Further, by connections between the eigenvalues of the Laplacians of C<PERSON>ley graphs over to the weights of codewords, we also give the first proof of the existence of spectral Cayley graph sparsifiers over by Cayley graphs, i.e., where we sparsify the set of generators to nearly-optimal size. Additionally, this work can be viewed as a continuation of a line of works on building sparsifiers for constraint satisfaction problems (CSPs); this result shows that there exist near-linear size sparsifiers for CSPs over 𝔽p-valued variables whose unsatisfying assignments can be expressed as the zeros of a linear equation modulo a prime p. As an application we give a full characterization of ternary Boolean CSPs (CSPs where the underlying predicate acts on three Boolean variables) that allow for near-linear size sparsification. This makes progress on a question posed by Kogan and Krauthgamer (ITCS 2015) asking which CSPs allow for near-linear size sparsifiers (in the number of variables).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.185"}, {"primary_key": "705747", "vector": [], "sparse_vector": [], "title": "Simple Delegated Choice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies delegation in a model of discrete choice. In the delegation problem, an uninformed principal must consult an informed agent to make a decision. Both the agent and principal have preferences over the decided-upon action which vary based on the state of the world, and which may not be aligned. The principal may commit to a mechanism, which maps reports of the agent to actions. When this mechanism is deterministic, it can take the form of a menu of actions, from which the agent simply chooses upon observing the state. In this case, the principal is said to have delegated the choice of action to the agent.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.21"}, {"primary_key": "705748", "vector": [], "sparse_vector": [], "title": "An Unconditional Lower Bound for Two-Pass Streaming Algorithms for Maximum Matching Approximation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we give the first unconditional space lower bound for two-pass streaming algorithms for Maximum Bipartite Matching approximation. We show that every randomized two-pass streaming algorithm that computes a -approximation to Maximum Bipartite Matching, for any constant ɛ > 0, requires space , where n is the number of vertices of the input graph.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.102"}, {"primary_key": "705749", "vector": [], "sparse_vector": [], "title": "Induced-Minor-Free Graphs: Separator <PERSON>, Subexponential Algorithms, and Improved Hardness of Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A graph G contains a graph H as an induced minor if H can be obtained from G by vertex deletions and edge contractions. The class of H-induced-minor-free graphs generalizes the class of H-minor-free graphs, but unlike H-minor-free graphs, it can contain dense graphs. We show that if an n-vertex m-edge graph G does not contain a graph H as an induced minor, then it has a balanced vertex separator of size , where the OH(·)-notation hides factors depending on H. More precisely, our upper bound for the size of the balanced separator is . We give an algorithm for finding either an induced minor model of H in G or such a separator in randomized polynomial-time. We apply this to obtain subexponential time algorithms on H-induced-minor-free graphs for a large class of problems including maximum independent set, minimum feedback vertex set, 3-coloring, and planarization.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.188"}, {"primary_key": "705750", "vector": [], "sparse_vector": [], "title": "Fully dynamic approximation schemes on planar and apex-minor-free graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The classic technique of <PERSON> [<PERSON><PERSON> '94] is the most fundamental approach for designing approximation schemes on planar, or more generally topologically-constrained graphs, and it has been applied in a myriad of different variants and settings throughout the last 30 years. In this work we propose a dynamic variant of <PERSON>'s technique, where instead of finding an approximate solution in a given static graph, the task is to design a data structure for maintaining an approximate solution in a fully dynamic graph, that is, a graph that is changing over time by edge deletions and edge insertions. Specifically, we address the two most basic problems — Maximum Weight Independent Set and Minimum Weight Dominating Set — and we prove the following: for a fully dynamic n-vertex planar graph G, one can", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.12"}, {"primary_key": "705751", "vector": [], "sparse_vector": [], "title": "Computing the 5-Edge-Connected Components in Linear Time.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We provide a deterministic algorithm for computing the 5-edge-connected components of an undirected multigraph in linear time. There were probably good indications that this computation can be performed in linear time, but no such algorithm was actually known prior to this work. Thus, our paper answers a theoretical question, and sheds light on the possibility that a solution may exist for general k. Furthermore, although the algorithm that we provide is quite extensive and broken up into several pieces, it can have an almost-linear time implementation with the use of elementary data structures. A key component in our algorithm is an oracle for answering connectivity queries for pairs of vertices in the presence of at most four edge-failures. Specifically, the oracle has size O(n), it can be constructed in linear time, and it answers connectivity queries in the presence of at most four edge-failures in O(1) time, where n denotes the number of vertices of the graph. We note that this is a result of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.76"}, {"primary_key": "705752", "vector": [], "sparse_vector": [], "title": "Viderman&apos;s algorithm for quantum LDPC codes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Quantum low-density parity-check (LDPC) codes, a class of quantum error correcting codes, are considered a blueprint for scalable quantum circuits. To use these codes, one needs efficient decoding algorithms. In the classical setting, there are multiple efficient decoding algorithms available, including <PERSON><PERSON><PERSON>'s algorithm (<PERSON><PERSON><PERSON>, TOCT 2013). <PERSON><PERSON><PERSON>'s algorithm for classical LDPC codes essentially reduces the error- correction problem to that of erasure-correction, by identifying a small envelope L that is guaranteed to contain the error set.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.88"}, {"primary_key": "705753", "vector": [], "sparse_vector": [], "title": "Deterministic Algorithms for Low Degree Factors of Constant Depth Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For every constant d, we design a subexponential time deterministic algorithm that takes as input a multivariate polynomial f given as a constant depth algebraic circuit over the field of rational numbers, and outputs all irreducible factors of f of degree at most d together with their respective multiplicities. Moreover, if f is a sparse polynomial, then the algorithm runs in quasipolynomial time.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.137"}, {"primary_key": "705754", "vector": [], "sparse_vector": [], "title": "Optimality of Glauber dynamics for general-purpose Ising model sampling and free energy approximation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2020) showed that Glauber dynamics mixes rapidly for general Ising models so long as the difference between the largest and smallest eigenvalues of the coupling matrix is at most 1 — ɛ for any fixed ɛ > 0. We give evidence that Glauber dynamics is in fact optimal for this \"generalpurpose sampling\" task. Namely, we give an average-case reduction from hypothesis testing in a Wishart negatively-spiked matrix model to approximately sampling from the Gibbs measure of a general Ising model for which the difference between the largest and smallest eigenvalues of the coupling matrix is at most 1 + ɛ for any fixed ɛ > 0. Combined with results of <PERSON><PERSON>, <PERSON>, and <PERSON> (2019) that analyze low-degree polynomial algorithms to give evidence for the hardness of the former spiked matrix problem, our results in turn give evidence for the hardness of general-purpose sampling improving on Glauber dynamics. We also give a similar reduction to approximating the free energy of general Ising models, and again infer evidence that simulated annealing algorithms based on Glauber dynamics are optimal in the general-purpose setting.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.180"}, {"primary_key": "705755", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Consistent k-Center Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the consistent k-center clustering problem. In this problem, the goal is to maintain a constant factor approximate k-center solution during a sequence of n point insertions and deletions while minimizing the recourse, i.e., the number of changes made to the set of centers after each point insertion or deletion. Previous works by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [ICML '12] and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [SODA '21] showed that in the incremental setting, where deletions are not allowed, one can obtain k · polylog(n)/n amortized recourse for both k-center and k-median, and demonstrated a matching lower bound. However, no algorithm for the fully dynamic setting achieves less than the trivial O(k) changes per update, which can be obtained by simply reclustering the full dataset after every update.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.124"}, {"primary_key": "705756", "vector": [], "sparse_vector": [], "title": "Fast Algorithms for Directed Graph Partitioning Using Flows and Reweighted Eigenvalues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider a new semidefinite programming relaxation for directed edge expansion, which is obtained by adding triangle inequalities to the reweighted eigenvalue formulation. Applying the matrix multiplicative weight update method on this relaxation, we derive almost linear-time algorithms to achieve O (√log n)- approximation and Cheeger-type guarantee for directed edge expansion, as well as an improved cut-matching game for directed graphs. This provides a primal-dual flow-based framework to obtain the best known algorithms for directed graph partitioning. The same approach also works for vertex expansion and for hypergraphs, providing a simple and unified approach to achieve the best known results for different expansion problems and different algorithmic techniques.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.22"}, {"primary_key": "705757", "vector": [], "sparse_vector": [], "title": "VC Set Systems in Minor-free (Di)Graphs and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A recent line of work on VC set systems in minor-free (undirected) graphs, starting from <PERSON> and <PERSON> [<PERSON>19], who constructed a new VC set system for planar graphs, has given surprising algorithmic results [LP19, Le23, DHV20, FHMWN20]. In this work, we initialize a more systematic study of VC set systems for minor-free graphs and their applications in both undirected graphs and directed graphs (a.k.a digraphs). More precisely:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.192"}, {"primary_key": "705758", "vector": [], "sparse_vector": [], "title": "Recovering the original simplicity: succinct and deterministic quantum algorithm for the welded tree problem.", "authors": ["Guanzhong Li", "Lvzhou Li", "<PERSON><PERSON><PERSON>"], "summary": "This work revisits quantum algorithms for the well-known welded tree problem, proposing a very succinct quantum algorithm based on the simplest coined quantum walks. It simply iterates the naturally defined coined quantum walk operator for a predetermined time and finally measure, where the predetermined time can be efficiently computed on classical computers. Then, the algorithm returns the correct answer deterministically, and achieves exponential speedups over any classical algorithm. The significance of the results may be seen as follows. (i) Our algorithm is rather simple compared with the one in (<PERSON> and <PERSON>, STOC'2023), which not only breaks the stereotype that coined quantum walks can only achieve quadratic speedups over classical algorithms, but also demonstrates the power of the simplest quantum walk model. (ii) Our algorithm theoretically achieves certainty of success, which is not possible with existing methods. Thus, it becomes one of the few examples that exhibit exponential separation between deterministic (exact) quantum and randomized query complexities, which may also change people's perception that since quantum mechanics is inherently probabilistic, it impossible to have a deterministic quantum algorithm with exponential speedups for the welded tree problem.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.87"}, {"primary_key": "705759", "vector": [], "sparse_vector": [], "title": "Dynamic Dictionary with Subconstant Wasted Bits per Key.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu", "<PERSON><PERSON><PERSON>"], "summary": "Dictionaries have been one of the central questions in data structures. A dictionary data structure maintains a set of key-value pairs under insertions and deletions such that given a query key, the data structure efficiently returns its value. The state-of-the-art dictionaries [4] store n key-value pairs with only O(n log(k) n) bits of redundancy, and support all operations in O(k) time, for k ≤ log* n. It was recently shown to be optimal [16].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.9"}, {"primary_key": "705760", "vector": [], "sparse_vector": [], "title": "Fast Fourier transform via automorphism groups of rational function fields.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Fast Fourier Transform (FFT) over a finite field 𝔽q computes evaluations of a given polynomial of degree less than n at a specifically chosen set of n distinct evaluation points in 𝔽q. If q or q — 1 is a smooth number, then the divide-and-conquer approach leads to the fastest known FFT algorithms. Depending on the type of group that the set of evaluation points forms, these algorithms are classified as multiplicative (Math of Comp. 1965) and additive (<PERSON><PERSON><PERSON> 2014) FFT algorithms. In this work, we provide a unified framework for FFT algorithms that include both multiplicative and additive FFT algorithms as special cases, and beyond: our framework also works when q + 1 is smooth, while all known results require q or q — 1 to be smooth. For the new case where q + 1 is smooth (this new case was not considered before in literature as far as we know), we show that if n is a divisor of q + 1 that is B-smooth for a real B > 0, then our FFT needs O(Bn log n) arithmetic operations in 𝔽q. Our unified framework is a natural consequence of introducing the algebraic function fields into the study of FFT.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.135"}, {"primary_key": "705761", "vector": [], "sparse_vector": [], "title": "Optimal Bounds on Private Graph Approximation.", "authors": ["Jingcheng Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an efficient ɛ-differentially private algorithm, that given a simple weighted n-vertex, m-edge graph G with a maximum unweighted degree Δ(G) ≤ n - 1, outputs a synthetic graph which approximates the spectrum with Õ(min{Δ(G), √n}) bound on the purely additive error. To the best of our knowledge, this is the first ɛ-differentially private algorithm with a non-trivial additive error for approximating the spectrum of the graph. One of our subroutines also precisely simulates the exponential mechanism over a non-convex set, which could be of independent interest given the recent interest in sampling from a log-concave distribution defined over a convex set. As a direct application of our result, we give the first non-trivial bound on approximating all-pairs effective resistances by a synthetic graph, which also implies approximating hitting/commute time and cover time of random walks on the graph. Given the significance of effective resistance in understanding the statistical properties of a graph, we believe our result would have further implications.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.39"}, {"primary_key": "705762", "vector": [], "sparse_vector": [], "title": "Minimization is Harder in the Prophet World.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study I.<PERSON><PERSON><PERSON><PERSON> prophet inequalities for cost minimization, where the problem is to pick a cost from a sequence X1,…, Xn drawn independently from a known distribution in an online manner, and compete against the prophet who can see all the realizations upfront and select the minimum. In contrast to the well-studied rewards maximization setting where a simple threshold strategy achieves a competitive ratio of ≈ 0.745 for all distributions, the cost minimization setting turns out to be much more complex.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.17"}, {"primary_key": "705763", "vector": [], "sparse_vector": [], "title": "Meta-theorems for Parameterized Streaming Algorithms‡.", "authors": ["<PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The streaming model was introduced to parameterized complexity independently by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [MFCS14] and by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [SODA15]. Subsequently, it was broadened by <PERSON><PERSON><PERSON>, <PERSON>rmode, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [SPAA15] and by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [SODA16]. Despite its strong motivation, the applicability of the streaming model to central problems in parameterized complexity has remained, for almost a decade, quite limited. Indeed, due to simple Ω(n)-space lower bounds for many of these problems, the kO(1) · polylog(n)-space requirement in the model is too strict.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.28"}, {"primary_key": "705764", "vector": [], "sparse_vector": [], "title": "Shannon meets Gray: Noise-robust, Low-sensitivity Codes with Applications in Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Integer data is typically made differentially private by adding noise from a Discrete Laplace (or Discrete Gaussian) distribution. We study the setting where differential privacy of a counting query is achieved using bit-wise randomized response, i.e., independent, random bit flips on the encoding of the query answer. Binary error-correcting codes transmitted through noisy channels with independent bit flips are well-studied in information theory. However, such codes are unsuitable for differential privacy since they have (by design) high sensitivity, i.e., neighbouring integers have encodings with a large Hamming distance. Gray codes show that it is possible to create an efficient sensitivity 1 encoding, but are also not suitable for differential privacy due to lack of noise-robustness. Our main result is that it is possible, with a constant rate code, to simultaneously achieve the sensitivity of Gray codes and the noise-robustness of error-correcting codes (down to the noise level required for differential privacy). An application of this new encoding of the integers is an asymptotically faster, space-optimal differentially private data structure for histograms.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.40"}, {"primary_key": "705765", "vector": [], "sparse_vector": [], "title": "Random Embeddings of Graphs: The Expected Number of Faces in Most Graphs is Logarithmic.", "authors": ["Jesse <PERSON> Loth", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A random 2-cell embedding of a connected graph G in some orientable surface is obtained by choosing a random local rotation around each vertex. Under this setup, the number of faces or the genus of the corresponding 2-cell embedding becomes a random variable. Random embeddings of two particular graph classes - those of a bouquet of n loops and those of n parallel edges connecting two vertices - have been extensively studied and are well-understood. However, little is known about more general graphs despite their important connections with central problems in mainstream mathematics and in theoretical physics (see [<PERSON><PERSON>, Graphs on surfaces and their applications, Springer 2004]). There are also tight connections with problems in computing (random generation, approximation algorithms). The results of this paper, in particular, explain why <PERSON> Carlo methods (see, e.g., [<PERSON> & <PERSON>, Local maxima in graded graphs of imbeddings, Ann. NY Acad. Sci 1979] and [<PERSON>, Local extrema in genus stratified graphs, JGT 1991]) cannot work for approximating the minimum genus of graphs.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.46"}, {"primary_key": "705766", "vector": [], "sparse_vector": [], "title": "The Cost of Parallelizing Boosting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the cost of parallelizing weak-to-strong boosting algorithms for learning, following the recent work of <PERSON><PERSON><PERSON> and <PERSON>. Our main results are two-fold:•First, we prove a tight lower bound, showing that even \"slight\" parallelization of boosting requires an exponential blow-up in the complexity of training.Specifically, let γ be the weak learner's advantage over random guessing. The famous AdaBoost algorithm produces an accurate hypothesis by interacting with the weak learner for Õ(1/γ2)1 rounds where each round runs in polynomial time.<PERSON><PERSON><PERSON> and <PERSON> showed that \"significant\" parallelization must incur exponential blow-up: Any boosting algorithm either interacts with the weak learner for Ω(1/γ) rounds or incurs an exp(d/γ) blow-up in the complexity of training, where d is the VC dimension of the hypothesis class. We close the gap by showing that any boosting algorithm either has Ω(1/γ2) rounds of interaction or incurs a smaller exponential blow-up of exp(d).•Complementing our lower bound, we show that there exists a boosting algorithm using Õ(1/(tγ2)) rounds, and only suffer a blow-up of exp(d · t2).Plugging in t = ω(1), this shows that the smaller blow-up in our lower bound is tight. More interestingly, this provides the first trade-off between the parallelism and the total work required for boosting.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.112"}, {"primary_key": "705767", "vector": [], "sparse_vector": [], "title": "Higher-Order Cheeger Inequality for Partitioning with Buffers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a new generalization of the higher-order <PERSON><PERSON><PERSON> inequality for partitioning with buffers. Consider a graph G = (V, E). The buffered expansion of a set S ⊆ V with a buffer B ⊆ V \\ S is the edge expansion of S after removing all the edges from set S to its buffer B. An ɛ-buffered k-partitioning is a partitioning of a graph into disjoint components Pi and buffers Bi, in which the size of buffer Bi for Pi is small relative to the size of Pi: |Bi| ≤ ɛ|Pi|. The buffered expansion of a buffered partition is the maximum of buffered expansions of the k sets Pi with buffers Bi. Let be the buffered expansion of the optimal ɛ-buffered k-partitioning, then for every δ > 0,", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.80"}, {"primary_key": "705768", "vector": [], "sparse_vector": [], "title": "Shortest Disjoint Paths on a Grid.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The well-known k-disjoint paths problem involves finding pairwise vertex-disjoint paths between k specified pairs of vertices within a given graph if they exist. In the shortest k-disjoint paths problem one looks for such paths of minimum total length. Despite nearly 50 years of active research on the k-disjoint paths problem, many open problems and complexity gaps still persist. A particularly well-defined scenario, inspired by VLSI design, focuses on infinite rectangular grids where the terminals are placed at arbitrary grid points. While the decision problem in this context remains NP-hard, no prior research has provided any positive results for the optimization version. The main result of this paper is a fixed-parameter tractable (FPT) algorithm for this scenario. It is important to stress that this is the first result achieving the FPT complexity of the shortest disjoint paths problem in any, even very restricted classes of graphs where we do not put any restriction on the placements of the terminals.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.14"}, {"primary_key": "705769", "vector": [], "sparse_vector": [], "title": "Robust 1-bit Compressed Sensing with Iterative Hard Thresholding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In 1-bit compressed sensing, the aim is to estimate a k-sparse unit vector x ∈ Sn-1 within an e error (in ℓ2) from minimal number of linear measurements that are quantized to just their signs, i.e., from measurements of the form y = sign((a, x}). In this paper, we study a noisy version where a fraction of the measurements can be flipped, potentially by an adversary. In particular, we analyze the Binary Iterative Hard Thresholding (BIHT) algorithm, a proximal gradient descent on a properly defined loss function used for 1-bit compressed sensing, in this noisy setting. It is known from recent results that, with noiseless measurements, BIHT provides an estimate within ɛ error. This result is optimal and universal, meaning one set of measurements work for all sparse vectors. In this paper, we show that BIHT also provides better results than all known methods for the noisy setting. We show that when up to τ-fraction of the sign measurements are incorrect (adversarial error), with the same number of measurements as before, BIHT agnostically provides an estimate of x within an Õ(ɛ + τ) error, maintaining the universality of measurements. This establishes stability of iterative hard thresholding in the presence of measurement error. To obtain the result, we use the restricted approximate invertibility of Gaussian matrices, as well as a tight analysis of the high-dimensional geometry of the adversarially corrupted measurements.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.105"}, {"primary_key": "705770", "vector": [], "sparse_vector": [], "title": "On the Unreasonable Effectiveness of Single Vector K<PERSON>ov Methods for Low-Rank Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Krylov subspace methods are a ubiquitous tool for computing near-optimal rank k approximations of large matrices. While \"large block\" Krylov methods with block size at least k give the best known theoretical guarantees, block size one (a single vector) or a small constant is often preferred in practice. Despite their popularity, we lack theoretical bounds on the performance of such \"small block\" Krylov methods for low-rank approximation.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.32"}, {"primary_key": "705771", "vector": [], "sparse_vector": [], "title": "Adversarial Low Degree Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In the t-online-erasure model in property testing, an adversary is allowed to erase t values of a queried function for each query the tester makes. This model was recently formulated by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, who showed that the properties of linearity of functions as well as quadraticity can be tested in Ot (1) many queries: O(log(t)) for linearity and 22O(t) for quadraticity. They asked whether the more general property of low-degreeness can be tested in the online erasure model, whether better testers exist for quadraticity, and if similar results hold when \"erasures\" are replaced with \"corruptions\".", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.154"}, {"primary_key": "705772", "vector": [], "sparse_vector": [], "title": "Dynamically Maintaining the Persistent Homology of Time Series.", "authors": ["<PERSON><PERSON> Montesano", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a dynamic data structure for maintaining the persistent homology of a time series of real numbers. The data structure supports local operations, including the insertion and deletion of an item and the cutting and concatenating of lists, each in time O(log n + k), in which n counts the critical items and k the changes in the augmented persistence diagram. To achieve this, we design a tailor-made tree structure with an unconventional representation, referred to as banana tree, which may be useful in its own right.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.11"}, {"primary_key": "705773", "vector": [], "sparse_vector": [], "title": "Detecting Hidden Communities by Power Iterations with Connections to Vanilla Spectral Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Community detection in the stochastic block model is one of the central problems of graph clustering. Since its introduction by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Social Networks, 1983), many subsequent papers have made great strides in solving and understanding this model. However, despite the long history of study, there are still unsolved challenges.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.33"}, {"primary_key": "705774", "vector": [], "sparse_vector": [], "title": "Sublinear Time Low-Rank Approximation of Toeplitz Matrices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a sublinear time algorithm for computing a near optimal low-rank approximation to any positive semidefinite (PSD) Toeplitz matrix T ∈ ℝd×d, given noisy access to its entries. In particular, given entrywise query access to T + E for an arbitrary noise matrix E ∈ ℝd×d, integer rank k≤d, and error parameter δ > 0, our algorithm runs in time poly(k, log(d/δ)) and outputs (in factored form) a Toeplitz matrix with rank poly(k, log(d/δ)) satisfying, for some fixed constant C,", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.183"}, {"primary_key": "705775", "vector": [], "sparse_vector": [], "title": "Adjacency Sketches in Adversarial Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "An adjacency sketching or implicit labeling scheme for a family F of graphs is a method that defines for any n vertex G ∈ F an assignment of labels to each vertex in G, so that the labels of two vertices tell you whether or not they are adjacent. The goal is to come up with labeling schemes that use as few bits as possible to represent the labels. By using randomness when assigning labels, it is sometimes possible to produce adjacency sketches with much smaller label sizes, but this comes at the cost of introducing some probability of error. Both deterministic and randomized labeling schemes have been extensively studied, as they have applications for distributed data structures and deeper connections to universal graphs and communication complexity. The main question of interest is which graph families have schemes using short labels, usually O(log n) in the deterministic case or constant for randomized sketches.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.41"}, {"primary_key": "705776", "vector": [], "sparse_vector": [], "title": "Gap Amplification for Reconfiguration Problems.", "authors": ["<PERSON><PERSON>"], "summary": "Combinatorial reconfiguration is an emerging field of theoretical computer science that studies the reachability between a pair of feasible solutions for a particular combinatorial problem. We study the hardness of accomplishing \"approximate\" reconfigurability, which affords to relax the feasibility of solutions. For example, in Minmax Set Cover Reconfiguration, given a pair of covers Cs and Ct for a set system F, we aim to transform Cs into Ct by repeatedly adding or removing a single set of F so as to minimize the maximum size of covers during transformation. The recent study by <PERSON><PERSON><PERSON> (STACS 2023) [Ohs23b] gives evidence that a host of reconfiguration problems are PSPACE-hard to approximate assuming the Reconfiguration Inapproximability Hypothesis (RIH), which postulates that a gap version of Maxmin CSP Reconfiguration is PSPACE-hard. One limitation of this approach is that inapproximability factors are not explicitly shown, so that even a 1.00 · · · 001-approximation algorithm for Minmax Set Cover Reconfiguration may not be ruled out, whereas it admits 2-approximation as per <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (Theor. Comput. Sci., 2011) [IDHPSU+11].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.54"}, {"primary_key": "705777", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Black Box Polynomial Root-finders.", "authors": ["<PERSON>"], "summary": "Univariate polynomial root-finding has been studied for four millennia and very intensively in the last decades. Our novel nearly optimal Las Vegas randomized root-finders approximate all zeros of a polynomial almost as fast as one accesses its coefficients with the precision required for the solution within a prescribed error bound.1 Moreover, our root-finders can be applied to a black box polynomial, defined by an oracle (that is, black box subroutine) for its evaluation rather than by its coefficients. Such root-finders are particularly fast for polynomials that can be evaluated fast, e.g., the sum of a few shifted monomials, but the only other known black box root-finder is the pioneering one by <PERSON> and <PERSON> at FOCS 2016, and it only approximates the absolutely largest root of a real-rooted polynomial. Our deterministic divide and conquer algorithm of ACM STOC 1995 is the only other known nearly optimal polynomial root-finder, and it extensively uses the coefficients, is quite involved, and has never been implemented, while according to extensive numerical experiments with standard test polynomials, already initial implementations of our new root-finders compete with user's choice package of root-finding subroutine MPSolve and supersede it more and more significantly where the degree of a polynomial grows large. Our root-finders are readily extended to support approximation of the eigenvalues of a matrix within a record Las Vegas expected bit operation time bound. Our auxiliary algorithms and techniques for computations with black box polynomials can be of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.136"}, {"primary_key": "705778", "vector": [], "sparse_vector": [], "title": "Combinatorial Stationary Prophet Inequalities.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Numerous recent papers have studied the tension between thickening and clearing a market in (uncertain, online) long-time horizon Markovian settings. In particular, (<PERSON><PERSON><PERSON> and <PERSON>ritaç EC'20, <PERSON><PERSON> et al. WINE'20, <PERSON><PERSON> et al. EC'22) studied what the latter referred to as the Stationary Prophet Inequality Problem, due to its similarity to the classic finite-time horizon prophet inequality problem. These works all consider unit-demand buyers. Mirroring the long line of work on the classic prophet inequality problem subject to combinatorial constraints, we initiate the study of the stationary prophet inequality problem subject to combinatorially-constrained buyers.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.164"}, {"primary_key": "705779", "vector": [], "sparse_vector": [], "title": "On the hardness of finding balanced independent sets in random bipartite graphs.", "authors": ["<PERSON>", "Yuzhou Wang"], "summary": "We consider the algorithmic problem of finding large balanced independent sets in sparse random bipartite graphs, and more generally the problem of finding independent sets with specified proportions of vertices on each side of the bipartition. In a bipartite graph it is trivial to find an independent set of density at least half (take one of the partition classes). In contrast, in a random bipartite graph of average degree d, the largest balanced independent sets (containing equal number of vertices from each class) are typically of density . Can we find such large balanced independent sets in these graphs efficiently? By utilizing the overlap gap property and the low-degree algorithmic framework, we prove that local and low- degree algorithms (even those that know the bipartition) cannot find balanced independent sets of density greater than for any ɛ > 0 fixed and d large but constant. This factor 2 statistical-computational gap between what exists and what local algorithms can achieve is analogous to the gap for finding large independent sets in (non-bipartite) random graphs. Our results therefor suggest that this gap is pervasive in many models, and that hard computational problems can lurk inside otherwise tractable ones. A particularly striking aspect of the gap in bipartite graphs is that the algorithm achieving the lower bound is extremely simple and can be implemented as a 1-local algorithm and a degree-1 polynomial (a linear function).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.85"}, {"primary_key": "705780", "vector": [], "sparse_vector": [], "title": "On the Extremal Functions of Acyclic Forbidden 0-1 Matrices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The extremal theory of forbidden 0-1 matrices studies the asymptotic growth of the function Ex(P, n), which is the maximum weight of a matrix A ∈ {0,1}n×n whose submatrices avoid a fixed pattern P ∈ {0, 1}k×1. This theory has been wildly successful at resolving problems in combinatorics [Kla00, MT04, C<PERSON>12], discrete and computational geometry [<PERSON><PERSON><PERSON>90, <PERSON>gg15, <PERSON>S96, PS91, <PERSON>t92, BG91], structural graph theory [GM14, BGK+21, BKTW22] and the analysis of data structures [Pet10, KS20], particularly corollaries of the dynamic optimality conjecture [CGK + 15b, CGK + 15a, CGJ+23, CPY24].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.45"}, {"primary_key": "705781", "vector": [], "sparse_vector": [], "title": "Optimal rates for ranking a permuted isotonic matrix in polynomial time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a ranking problem where we have noisy observations from a matrix with isotonic columns whose rows have been permuted by some permutation π*. This encompasses many models, including crowd-labeling and ranking in tournaments by pair-wise comparisons. In this work, we provide an optimal and polynomial-time procedure for recovering π*, settling an open problem in [8]. As a byproduct, our procedure is used to improve the state-of-the art for ranking problems in the stochastically transitive model (SST). Our approach is based on iterative pairwise comparisons by suitable data-driven weighted means of the columns. These weights are built using a combination of spectral methods with new dimension-reduction techniques. In order to deal with the important case of missing data, we establish a new concentration inequality for sparse and centered rectangular Wishart-type matrices.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.116"}, {"primary_key": "705782", "vector": [], "sparse_vector": [], "title": "School Redistricting: Wiping Unfairness Off the Map.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce and study the problem of designing an equitable school redistricting map, which we formalize as that of assigning n students to school attendance zones in a way that is fair to various demographic groups. Drawing on methodology from fair division, we consider the demographic groups as players and seats in schools as homogeneous goods. Due to geographic constraints, not every school can be assigned to every student. This raises new obstacles, rendering some classic fairness criteria infeasible. Nevertheless, we show that it is always possible to find an almost proportional allocation among g demographic groups if we are allowed to add O(g log g) extra seats. For any fixed g, we show that such an allocation can be found in polynomial time, obtaining a runtime of O(n2 log n) in the special (but practical) case where g ≤ 3.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.97"}, {"primary_key": "705783", "vector": [], "sparse_vector": [], "title": "Faster exact and approximation algorithms for packing and covering matroids via push-relabel.", "authors": ["<PERSON>"], "summary": "Matroids are a fundamental object of study in combinatorial optimization. Three closely related and important problems involving matroids are maximizing the size of the union of k independent sets (that is, k-fold matroid union), computing k disjoint bases (a.k.a. matroid base packing), and covering the elements by k bases (a.k.a. matroid base covering). These problems generalize naturally to integral and real-valued capacities on the elements. This work develops faster exact and/or approximation problems for these and some other closely related problems such as optimal reinforcement and matroid membership. We obtain improved running times both for general matroids in the independence oracle model and for the graphic matroid. The main thrust of our improvements comes from developing a faster and unifying push-relabel algorithm for the integer-capacitated versions of these problems, building on previous work by <PERSON> and <PERSON> [24]. We then build on this algorithm in two directions. First we develop a faster augmenting path subroutine for k-fold matroid union that, when appended to an approximation version of the push-relabel algorithm, gives a faster exact algorithm for some parameters of k. In particular we obtain a subquadratic-query running time in the uncapacitated setting for the three basic problems listed above. We also obtain faster approximation algorithms for these problems with real-valued capacities by reducing to small integral capacities via randomized rounding. To this end, we develop a new randomized rounding technique for base covering problems in matroids that may also be of independent interest.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.82"}, {"primary_key": "705784", "vector": [], "sparse_vector": [], "title": "Quotient sparsification for submodular functions.", "authors": ["<PERSON>"], "summary": "Graph sparsification has been an important topic with many structural and algorithmic consequences. Recently hypergraph sparsification has come to the fore and has seen exciting progress. In this paper we take a fresh perspective and show that they can be both be derived as corollaries of a general theorem on sparsifying matroids and monotone submodular functions.Quotients of matroids and monotone submodular functions generalize k-cuts in graphs and hypergraphs. We show that a weighted ground set of a monotone submodular function f can be sparsified while approximately preserving the weight of every quotient of f with high probability in randomized polynomial time.This theorem conceptually unifies cut sparsifiers for undirected graphs [7] with other interesting applications. One basic application is to reduce the number of elements in a matroid while preserving the weight of every quotient of the matroid. For hypergraphs, the theorem gives an alternative approach to the hypergraph cut sparsifiers obtained recently in [12], that also preserves all k-cuts. Another application is to reduce the number of points in a set system while preserving the weight of the union of every collection of sets. We also present algorithms that sparsify hypergraphs and set systems in nearly linear time, and sparsify matroids in nearly linear time and queries in the rank oracle model.* Dept. of Computer Science, Purdue University, West Lafayette, IN 47907. Supported in part by NSF grant CCF-2129816.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.187"}, {"primary_key": "705785", "vector": [], "sparse_vector": [], "title": "Flip Graph Connectivity for Arrangements of Pseudolines and Pseudocircles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Birgit Vogtenhuber"], "summary": "Flip graphs of combinatorial and geometric objects are at the heart of many deep structural insights and connections between different branches of discrete mathematics and computer science. They also provide a natural framework for the study of reconfiguration problems. We study flip graphs of arrangements of pseudolines and of arrangements of pseudocircles, which are combinatorial generalizations of lines and circles, respectively. In both cases we consider triangle flips as local transformation and prove conjectures regarding their connectivity.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.172"}, {"primary_key": "705786", "vector": [], "sparse_vector": [], "title": "Impossibilities for Obviously Strategy-Proof Mechanisms.", "authors": ["<PERSON><PERSON>"], "summary": "We explore the approximation power of deterministic obviously strategy-proof mechanisms in auctions, where the objective is welfare maximization. A trivial ascending auction on the grand bundle guarantees an approximation of min{m, n} for all valuation classes, where m is the number of items and n is the number of bidders. We focus on two classes of valuations considered \"simple\": additive valuations and unit-demand valuations. For additive valuations, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [EC'17] have shown that exact welfare maximization is impossible. No impossibilities are known for unit-demand valuations.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.2"}, {"primary_key": "705787", "vector": [], "sparse_vector": [], "title": "Efficient Quantum State Synthesis with One Query.", "authors": ["<PERSON>"], "summary": "We present a polynomial-time quantum algorithm making a single query (in superposition) to a classical oracle, such that for every state |ψ〉 there exists a choice of oracle that makes the algorithm construct an exponentially close approximation of |ψ〉. Previous algorithms for this problem either used a linear number of queries and polynomial time, or a constant number of queries and polynomially many ancillae but no nontrivial bound on the runtime. As corollaries we do the following:", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.89"}, {"primary_key": "705788", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Point Selections and Halving Hyperplanes in Higher Dimensions.", "authors": ["<PERSON><PERSON>"], "summary": "Let (P, E) be a (d + 1)-uniform geometric hypergraph, where P is an n-point set in general position in ℝd and is a collection of d-dimensional simplices with vertices in P, for 0 0. This is a dramatic improvement in all dimensions d ≥ 3, over the previous lower bounds of the general form ɛ(cd)d+1nd+1, which date back to the seminal 1991 work of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.157"}, {"primary_key": "705789", "vector": [], "sparse_vector": [], "title": "How Many Neurons Does it Take to Approximate the Maximum?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the size of a neural network needed to approximate the maximum function over d inputs, in the most basic setting of approximating with respect to the L2 norm, for continuous distributions, for a network that uses ReLU activations. We provide new lower and upper bounds on the width required for approximation across various depths. Our results establish new depth separations between depth 2 and 3, and depth 3 and 5 networks, as well as providing a depth 𝒪(log(log(d))) and width 𝒪(d) construction which approximates the maximum function. Our depth separation results are facilitated by a new lower bound for depth 2 networks approximating the maximum function over the uniform distribution, assuming an exponential upper bound on the size of the weights. Furthermore, we are able to use this depth 2 lower bound to provide tight bounds on the number of neurons needed to approximate the maximum by a depth 3 network. Our lower bounds are of potentially broad interest as they apply to the widely studied and used max function, in contrast to many previous results that base their bounds on specially constructed or pathological functions and distributions.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.113"}, {"primary_key": "705790", "vector": [], "sparse_vector": [], "title": "Faster Approximate All Pairs Shortest Paths.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The all pairs shortest path problem (APSP) is one of the foundational problems in computer science. For weighted dense graphs on n vertices, no truly sub-cubic algorithms exist to compute APSP exactly even for undirected graphs. This is popularly known as the APSP conjecture and has played a prominent role in developing the field of fine-grained complexity. The seminal results of <PERSON><PERSON><PERSON> and <PERSON><PERSON> show that using fast matrix multiplication (FMM) it is possible to compute APSP on unweighted undirected graphs exactly in Õ(nω) time, and can be approximated within (1 + ɛ) factor in weighted undirected graphs in time Õ(nω) respectively. Here ω is the exponent of FMM, which currently stands at ω = 2.37188. Moreover even for unweighted undirected graphs, it is not possible to obtain a (2 — ɛ)-multiplicative approximation of APSP for any ɛ > 0 in o(nω) time. Since 2000, a result by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> gave the best 2 approximation algorithm for APSP in unweighted undirected graphs in time Õ(n7/3). This result was recently improved by <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> to Õ(n2.2593) using fast min-plus product for bounded-difference matrices which uses FMM as a subroutine (the stated bound here uses new results for computing such min-plus products by Du<PERSON>). In fact both these results obtain a +2-additive approximation. Recently, <PERSON><PERSON><PERSON> (STOC, 2023) improved the previous bounds for multiplicative 2-approximation of APSP in unweighted undirected graphs giving the best known bound of Õ(n2.25). All these algorithms are deterministic. Roditty also considers estimating shortest paths for all paths of length ≥ k for k ≥ 4, and gives improved bounds when the underlying graph is sparse using randomization. Though for dense graphs, the best known bounds still remained at those provided by Dor et al. more than two decades back.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.170"}, {"primary_key": "705791", "vector": [], "sparse_vector": [], "title": "A Tight Bound for Testing Partition Properties.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A partition property of order k asks if a graph can be partitioned into k vertex sets of prescribed sizes so that the densities between any pair of sets falls within a prescribed range. This family of properties has been extensively studied in various areas of research ranging from theoretical computer science to statistical physics. Our main result is that every partition property of order k is testable with query complexity poly(k/ɛ). We thus obtain an exponential improvement (in k) over the (1/ɛ)O(k) bound obtained by <PERSON><PERSON>, <PERSON> and <PERSON> in their seminal FOCS 1996 paper. We further prove that our bound is tight in the sense that it cannot be made sub-polynomial in either k or ɛ.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.150"}, {"primary_key": "705792", "vector": [], "sparse_vector": [], "title": "Improved Approximation Algorithms for the Joint Replenishment Problem with Outliers, and with Fairness Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The joint replenishment problem (JRP) is a classical inventory management problem. We consider a natural generalization with outliers, where we are allowed to reject (that is, not service) a subset of demand points. In this paper, we are motivated by issues of fairness - if we do not serve all of the demands, we wish to \"spread out the pain\" in a balanced way among customers, communities, or any specified market segmentation. One approach is to constrain the rejections allowed, and to have separate bounds for each given customer. In our most general setting, we consider a set of C features, where each demand point has an associated rejection cost for each feature, and we have a given bound on the allowed rejection cost incurred in total for each feature. This generalizes an extensively studied model of fairness introduced in earlier work on the Colorful k—Center problem in which (analogously) each demand point has a given color, and we bound the number of rejections of each color class. In the JRP, we seek to balance the cost incurred by a fixed ordering overhead with the cost of maintaining on-hand inventory over a longer period in advance of when it is needed. More precisely, there is a given set of item types, for which there is specified demand over a finite, discrete-time horizon, and placing any order at a given time incurs a general ordering cost and item-specific ordering costs (independent of the total demand serviced); in addition, for each unit of demand held in inventory for an interval of time, there is a corresponding item-specific holding cost incurred; the aim is to minimize the total cost.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.99"}, {"primary_key": "705793", "vector": [], "sparse_vector": [], "title": "Single-Source Unsplittable Flows in Planar Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The single-source unsplittable flow (SSUF) problem asks to send flow from a common source to different terminals with unrelated demands, each terminal being served through a single path. One of the most heavily studied SSUF objectives is to minimize the violation of some given arc capacities. A seminal result of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> showed that, whenever a fractional flow exists respecting the capacities, then there is an unsplittable one violating the capacities by at most the maximum demand. <PERSON><PERSON><PERSON> conjectured a very natural cost version of the same result, where the unsplittable flow is required to be no more expensive than the fractional one. This intriguing conjecture remains open. More so, there are arguably no non-trivial graph classes for which it is known to hold.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.24"}, {"primary_key": "705794", "vector": [], "sparse_vector": [], "title": "Sorting and Selection in Rounds with Adversarial Comparisons.", "authors": ["<PERSON>"], "summary": "We continue the study of selection and sorting of n numbers under the adversarial comparator model, where comparisons can be adversarially tampered with if the arguments are sufficiently close.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.42"}, {"primary_key": "705795", "vector": [], "sparse_vector": [], "title": "Untangling Graphs on Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Consider a graph drawn on a surface (for example, the plane minus a finite set of obstacle points), possibly with crossings. We provide an algorithm to decide whether such a drawing can be untangled, namely, if one can slide the vertices and edges of the graph on the surface (avoiding the obstacles) to remove all crossings; in other words, whether the drawing is homotopic to an embedding. While the problem boils down to planarity testing when the surface is the sphere or the disk (or equivalently the plane without any obstacle), the other cases have never been studied before, except when the input graph is a cycle, in an abundant literature in topology and more recently by <PERSON><PERSON><PERSON> and <PERSON> [SoC<PERSON> 2017, J. <PERSON> 2019], who gave a near-linear algorithm for this problem.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.175"}, {"primary_key": "705796", "vector": [], "sparse_vector": [], "title": "On Supermodular Contracts and Dense Subgraphs.", "authors": ["<PERSON><PERSON>-Campo Vuong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the combinatorial contract design problem, introduced and studied by <PERSON><PERSON><PERSON> et al. (2021, 2022), in both the single and multi-agent settings. Prior work has examined the problem when the principal's utility function is submodular or XOS in the actions chosen by the agent(s). We complement this emerging literature with an examination of the problem when the principal's utility is supermodular. Our results apply to the unconstrained contract design problem in the binary outcome case (i.e., the principal's task succeeds or fails), and to the linear contract design problem more generally.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.6"}, {"primary_key": "705797", "vector": [], "sparse_vector": [], "title": "Representative set statements for delta-matroids and the Mader delta-matroid.", "authors": ["<PERSON>"], "summary": "The representative sets lemma for linear matroids has many powerful surprising applications in parameterized complexity, including improved FPT dynamic programming algorithms (<PERSON><PERSON><PERSON> et al., JACM 2016) and polynomial kernelization and sparsification results for graph separation problems (<PERSON><PERSON><PERSON> and <PERSON>ström, JACM 2020). However, its application can be sporadic, as it presupposes the existence of a linear matroid encoding a property relevant to the problem at hand. Correspondingly, although its application led to several new kernelizations (e.g., Almost 2-SAT and restricted variants of MuLTIWAY Cut), there are also several problems left open (e.g., the general case of MuLTIWAY Cut).", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.31"}, {"primary_key": "705798", "vector": [], "sparse_vector": [], "title": "Simpler and Higher Lower Bounds for Shortcut Sets.", "authors": ["Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "We study the well-known shortcut set problem: how much can one decrease the diameter of a directed graph by adding a small set of shortcuts from the transitive closure of the graph.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.94"}, {"primary_key": "705799", "vector": [], "sparse_vector": [], "title": "New Bounds for Matrix Multiplication: from Alpha to Omega.", "authors": ["Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The main contribution of this paper is a new improved variant of the laser method for designing matrix multiplication algorithms. Building upon the recent techniques of [<PERSON><PERSON>, <PERSON>, <PERSON>, FOCS 2023], the new method introduces several new ingredients that not only yield an improved bound on the matrix multiplication exponent ω, but also improve the known bounds on rectangular matrix multiplication by [<PERSON> and <PERSON>rruti<PERSON>, SODA 2018].", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.134"}, {"primary_key": "705800", "vector": [], "sparse_vector": [], "title": "Edge-weighted Online Stochastic Matching: Beating.", "authors": ["Shuyi Yan"], "summary": "We study the edge-weighted online stochastic matching problem. Since [6] introduced the online stochastic matching problem and proposed the (1 − 1/ε)-competitive Suggested Matching algorithm, there has been no improvement in the edge-weighted setting. In this paper, we introduce the first algorithm beating the 1 − 1/ε barrier in this setting, achieving a competitive ratio of 0.645. Under the LP proposed by [13], we design an algorithmic preprocessing, dividing all edges into two classes. Then we use different matching strategies to improve the performance on edges in one class in the early stage and on edges in another class in the late stage, while keeping the matching events of different edges highly independent. By balancing them, we finally guarantee the matched probability of every single edge.", "published": "2024-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977912.165"}]