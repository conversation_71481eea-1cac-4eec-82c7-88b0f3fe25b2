[{"primary_key": "3385016", "vector": [], "sparse_vector": [], "title": "Multi-Input Functional Encryption for Inner Products: Function-Hiding Realizations and Constructions Without Pairings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present new constructions of multi-input functional encryption (MIFE) schemes for the inner-product functionality that improve the state of the art solution of <PERSON><PERSON><PERSON><PERSON> al.(Eurocrypt 2017) in two main directions. First, we put forward a novel methodology to convert single-input functional encryption for inner products into multi-input schemes for the same functionality. Our transformation is surprisingly simple, general and efficient. In particular, it does not require pairings and it can be instantiated withallknown single-input schemes. This leads to two main advances. First, we enlarge the set of assumptions this primitive can be based on, notably, obtaining new MIFEs for inner products from plain DDH, LWE, and Decisional Composite Residuosity. Second, we obtain the first MIFE schemes from standard assumptions where decryption works efficiently even for messages of super-polynomial size. Our second main contribution is the first function-hiding MIFE scheme for inner products based on standard assumptions. To this end, we show how to extend the original, pairing-based, MIFE by <PERSON><PERSON><PERSON><PERSON> al.in order to make it function hiding, thus obtaining a function-hiding MIFE from the MDDH assumption.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_20"}, {"primary_key": "3385017", "vector": [], "sparse_vector": [], "title": "A New Public-Key Cryptosystem via Mersenne Numbers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we propose a new public-key cryptosystem whose security is based on the computational intractability of the following problem: Given a Mersenne number\\(p = 2^n - 1\\), wherenis a prime, a positive integerh, and twon-bit integersT,R, decide whether their existn-bit integersF,Geach of Hamming weight less thanhsuch that\\(T = F\\cdot R + G\\)modulop.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_16"}, {"primary_key": "3385018", "vector": [], "sparse_vector": [], "title": "Non-Interactive Zero-Knowledge Proofs for Composite Statements.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The two most common ways to design non-interactive zero-knowledge (NIZK) proofs are based on Sigma protocols and QAP-based SNARKs. The former is highly efficient for proving algebraic statements while the latter is superior for arithmetic representations. Motivated by applications such as privacy-preserving credentials and privacy-preserving audits in cryptocurrencies, we study the design of NIZKs for composite statements that compose algebraic and arithmetic statements in arbitrary ways. Specifically, we provide a framework for proving statements that consist of ANDs, ORs and function compositions of a mix of algebraic and arithmetic components. This allows us to explore the full spectrum of trade-offs between proof size, prover cost, and CRS size/generation cost. This leads to proofs for statements of the form: knowledge ofxsuch that\\(SHA(g^x)=y\\)for some publicywhere the prover’s work is 500 times fewer exponentiations compared to a QAP-based SNARK at the cost of increasing the proof size to 2404 group and field elements. In application to anonymous credentials, our techniques result in 8 times fewer exponentiations for the prover at the cost of increasing the proof size to 298 elements.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_22"}, {"primary_key": "3385019", "vector": [], "sparse_vector": [], "title": "Round-Optimal Secure Multiparty Computation with Honest Majority.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the exact round complexity of secure multiparty computation (MPC) in the honest majority setting. We construct severalround-optimaln-party protocols, tolerating any\\(t<\\frac{n}{2}\\)corruptions. Security with abort:We give the first construction of two round MPC for general functions that achieves security with abort againstmaliciousadversaries in the plain model. The security of our protocol only relies on one-way functions. Guaranteed output delivery:We also construct protocols that achieve security with guaranteed output delivery:(i)Againstfail-stopadversaries, we construct two round MPC either in the (bare) public-key infrastructure model with no additional assumptions, or in the plain model assuming two-round semi-honest oblivious transfer. In three rounds, however, we can achieve security assuming only one-way functions.(ii)Againstmaliciousadversaries, we construct three round MPC in the plain model, assuming public-key encryption and Zaps. Previously, such protocols were only known based on specific learning assumptions and required the use of common reference strings. All of our results are obtained via general compilers that may be of independent interest.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_14"}, {"primary_key": "3385020", "vector": [], "sparse_vector": [], "title": "Private Circuits: A Modular Approach.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of protecting general computations against constant-rate random leakage. That is, the computation is performed by a randomized boolean circuit that maps a randomly encoded input to a randomly encoded output, such that even if the value of every wire is independently leaked with some constant probability\\(p > 0\\), the leakage reveals essentially nothing about the input. In this work we provide a conceptually simple, modular approach for solving the above problem, providing a simpler and self-contained alternative to previous constructions of <PERSON><PERSON><PERSON> (STOC 2011) and <PERSON><PERSON><PERSON> et al. (Eurocrypt 2016). We also obtain several extensions and generalizations of this result. In particular, we show that for every leakage probability\\(p<1\\), there is a finite basis\\(\\mathbb {B}\\)such that leakage-resilient computation with leakage probabilitypcan be realized using circuits over the basis\\(\\mathbb {B}\\). We obtain similar positive results for the stronger notion ofleakage tolerance, where the input is not encoded, but the leakage from the entire computation can be simulated given random\\(p'\\)-leakage of input values alone, for any\\(p<p'<1\\). Finally, we complement this by a negative result, showing that for every basis\\(\\mathbb {B}\\)there is some leakage probability\\(p<1\\)such that for any\\(p'<1\\), leakage tolerance as abovecannotbe achieved in general. We show that our modular approach is also useful for protecting computations againstworst caseleakage. In this model, we require that leakage of any\\(\\mathbf{t}\\)(adversarially chosen) wires reveal nothing about the input. By combining our construction with a previous derandomization technique of Ishai et al. (ICALP 2013), we show that security in this setting can be achieved with\\(O(\\mathbf{t}^{1+\\varepsilon })\\)random bits, for every constant\\(\\varepsilon > 0\\). This (near-optimal) bound significantly improves upon previous constructions that required more than\\(\\mathbf{t}^{3}\\)random bits.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_15"}, {"primary_key": "3385021", "vector": [], "sparse_vector": [], "title": "Lower Bounds on Lattice Enumeration with Extreme Pruning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At Eurocrypt ’10, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> introduced lattice enumeration with extreme pruning: this algorithm is implemented in state-of-the-art lattice reduction software and used in challenge records. They showed that extreme pruning provided an exponential speed-up over full enumeration. However, no limit on its efficiency was known, which was problematic for long-term security estimates of lattice-based cryptosystems. We prove the first lower bounds on lattice enumeration with extreme pruning: if the success probability is lower bounded, we can lower bound the global running time taken by extreme pruning. Our results are based on geometric properties of cylinder intersections and some form of isoperimetry. We discuss their impact on lattice security estimates.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_21"}, {"primary_key": "3385022", "vector": [], "sparse_vector": [], "title": "On the Complexity of Compressing Obfuscation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Indistinguishability obfuscation has become one of the most exciting cryptographic primitives due to its far reaching applications in cryptography and other fields. However, to date, obtaining a plausibly secure construction has been an illusive task, thus motivating the study of seemingly weaker primitives that imply it, with the possibility that they will be easier to construct. In this work, we provide a systematic study of compressing obfuscation, one of the most natural and simple to describe primitives that is known to imply indistinguishability obfuscation when combined with other standard assumptions. A compressing obfuscator is roughly an indistinguishability obfuscator that outputs just a slightly compressed encoding of the truth table. This generalizes notions introduced by <PERSON> et al. (PKC 2016) and <PERSON><PERSON><PERSON> et al. (TCC 2016) by allowing for a broader regime of parameters. We view compressing obfuscation as an independent cryptographic primitive and show various positive and negative results concerning its power and plausibility of existence, demonstrating significant differences from full-fledged indistinguishability obfuscation. First, we show that as a cryptographic building block, compressing obfuscation is weak. In particular, when combined with one-way functions, it cannot be used (in a black-box way) to achieve public-key encryption, even under (sub-)exponential security assumptions. This is in sharp contrast to indistinguishability obfuscation, which together with one-way functions implies almost all cryptographic primitives. Second, we show that to construct compressing obfuscation with perfect correctness, one only needs to assume its existence with a very weak correctness guarantee and polynomial hardness. Namely, we show a correctness amplification transformation with optimal parameters that relies only on polynomial hardness assumptions. This implies a universal construction assuming only polynomially secure compressing obfuscation with approximate correctness. In the context of indistinguishability obfuscation, we know how to achieve such a result only under sub-exponential security assumptions together with derandomization assumptions. Lastly, we characterize the existence of compressing obfuscation withstatisticalsecurity. We show that in some range of parameters and for some classes of circuits such an obfuscatorexists, whereas it is unlikely to exist with better parameters or for larger classes of circuits. These positive and negative results reveal a deep connection between compressing obfuscation and various concepts in complexity theory and learning theory.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_26"}, {"primary_key": "3385023", "vector": [], "sparse_vector": [], "title": "Tight Tradeoffs in Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A searchable symmetric encryption (SSE) scheme enables a client to store data on an untrusted server while supporting keyword searches in a secure manner. Recent experiments have indicated that the practical relevance of such schemes heavily relies on the tradeoff between theirspace overhead,locality(the number of non-contiguous memory locations that the server accesses with each query), andread efficiency(the ratio between the number of bits the server reads with each query and the actual size of the answer). These experiments motivated <PERSON> and Tessaro (EUROCRYPT ’14) and <PERSON><PERSON><PERSON> et al. (STOC ’16) to construct SSE schemes offering various such tradeoffs, and to prove lower bounds for natural SSE frameworks. Unfortunately, the best-possible tradeoff has not been identified, and there are substantial gaps between the existing schemes and lower bounds, indicating that a better understanding of SSE is needed. We establish tight bounds on the tradeoff between the space overhead, locality and read efficiency of SSE schemes within two general frameworks that capture the memory access pattern underlying all existing schemes. First, we introduce the “pad-and-split” framework, refining that of <PERSON> and <PERSON>ro while still capturing the same existing schemes. Within our framework we significantly strengthen their lower bound, proving that any scheme with localityLmust use space\\(\\varOmega ( N \\log N / \\log L )\\)for databases of sizeN. This is a tight lower bound, matching the tradeoff provided by the scheme of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (SIGMOD ’17) which is captured by our pad-and-split framework. Then, within the “statistical-independence” framework of <PERSON><PERSON><PERSON> et al. we show that their lower bound is essentially tight: We construct a scheme whose tradeoff matches their lower bound within an additive\\(O(\\log \\log \\log N)\\)factor in its read efficiency, once again improving upon the existing schemes. Our scheme offers optimal space and locality, and nearly-optimal read efficiency that depends on the frequency of the queried keywords: For a keyword that is associated with\\(n = N^{1 - \\epsilon (n)}\\)document identifiers, the read efficiency is\\(\\omega (1) \\cdot {\\epsilon }(n)^{-1}+ O(\\log \\log \\log N)\\)when retrieving its identifiers (where the\\(\\omega (1)\\)term may be arbitrarily small, and\\(\\omega (1) \\cdot {\\epsilon }(n)^{-1}\\)is the lower bound proved by Asharov et al.). In particular, for any keyword that is associated with at most\\(N^{1 - 1/o(\\log \\log \\log N)}\\)document identifiers (i.e., for any keyword that is not exceptionally common), we provide read efficiency\\(O(\\log \\log \\log N)\\)when retrieving its identifiers.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_14"}, {"primary_key": "3385024", "vector": [], "sparse_vector": [], "title": "Constrained PRFs for \\mathrmNC1 in Traditional Groups.", "authors": ["Nuttapong Attrapadung", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose new constrained pseudorandom functions (CPRFs) intraditional groups. Traditional groups mean cyclic and multiplicative groups of prime order that were widely used in the 1980s and 1990s (sometimes called “pairing free” groups). Our main constructions are as follows. We propose a selectively single-key secure CPRF forcircuits with depth\\(O(\\log n)\\)(that is,NC\\(^1\\)circuits) in traditional groupswherenis the input size. It is secure under theL-decisional Di<PERSON><PERSON>-<PERSON> inversion (L-DDHI) assumption in the group of quadratic residues\\(\\mathbb {QR}_q\\)and the decisional <PERSON><PERSON><PERSON><PERSON><PERSON>man (DDH) assumption in a traditional group of orderqin the standard model. We propose a selectively single-keyprivate bit-fixingCPRF intraditional groups. It is secure under the DDH assumption in any prime-order cyclic groupin the standard model. We proposeadaptivelysingle-key secure CPRF forNC\\(^1\\)and private bit-fixing CPRF in the random oracle model. To achieve the security in the standard model, we develop a new technique using correlated-input secure hash functions.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_19"}, {"primary_key": "3385025", "vector": [], "sparse_vector": [], "title": "Promise Zero Knowledge and Its Applications to Round Optimal MPC.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We devise a newpartitioned simulationtechnique for MPC where the simulator uses different strategies for simulating the view of aborting adversaries and non-aborting adversaries. The protagonist of this technique is a new notion ofpromise zero knowledge(ZK) where the ZK property only holds against non-aborting verifiers. We show how to realize promise ZK in three rounds in the simultaneous-message model assuming polynomially hard DDH (or QR or N\\(^{th}\\)-Residuosity). We demonstrate the following applications of our new technique: We construct the first round-optimal (i.e., four round) MPC protocol for general functions based on polynomially hard DDH (or QR or N\\(^{th}\\)-Residuosity). We further show how to overcome the four-round barrier for MPC by constructing a three-round protocol for “list coin-tossing” – a slight relaxation of coin-tossing that suffices for most conceivable applications – based on polynomially hard DDH (or QR or N\\(^{th}\\)-Residuosity). This result generalizes to randomized input-less functionalities. Previously, four round MPC protocols required sub-exponential-time hardness assumptions and no multi-party three-round protocols were known for any relaxed security notions with polynomial-time simulation against malicious adversaries. In order to base security on polynomial-time standard assumptions, we also rely upon aleveled rewinding securitytechnique that can be viewed as a polynomial-time alternative to leveled complexity leveraging for achieving “non-malleability” across different primitives.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_16"}, {"primary_key": "3385026", "vector": [], "sparse_vector": [], "title": "Proofs of Work From Worst-Case Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give Proofs of Work (PoWs) whose hardness is based on well-studiedworst-caseassumptions from fine-grained complexity theory. This extends the work of (<PERSON> et al., STOC ’17), that presents PoWs that are based on the Orthogonal Vectors, 3SUM, and All-Pairs Shortest Path problems. These, however, were presented as a ‘proof of concept’ of provably secure PoWs and did not fully meet the requirements of a conventional PoW: namely, it was not shown that multiple proofs could not be generated faster than generating each individually. We use the considerablealgebraic structureof these PoWs to prove that this non-amortizability of multiple proofs does in fact hold and further show that the PoWs’ structure can be exploited in ways previous heuristic PoWs could not. This creates full PoWs that are provably hard from worst-case assumptions (previously, PoWs were either only based on heuristic assumptions or on much stronger cryptographic assumptions (<PERSON><PERSON><PERSON> et al., ITCS ’16)) while still retaining significant structure to enable extra properties of our PoWs. Namely, we show that the PoWs of (<PERSON> et al., STOC ’17) can be modified to have much faster verification time, can be proved in zero knowledge, and more. Finally, as our PoWs are based on evaluating low-degree polynomials originating from average-case fine-grained complexity, we prove anaverage-case direct sum theoremfor the problem of evaluating these polynomials, which may be of independent interest. For our context, this implies the required non-amortizability of our PoWs.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_26"}, {"primary_key": "3385027", "vector": [], "sparse_vector": [], "title": "Improved Key Recovery Attacks on Reduced-Round AES with Practical Data and Memory Complexities.", "authors": ["<PERSON><PERSON>ya Bar-On", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Determining the security of AES is a central problem in cryptanalysis, but progress in this area had been slow and only a handful of cryptanalytic techniques led to significant advancements. At Eurocrypt 2017 <PERSON> et al. presented a novel type of distinguisher for AES-like structures, but so far all the published attacks which were based on this distinguisher were inferior to previously known attacks in their complexity. In this paper we combine the technique of <PERSON><PERSON> et al. with several other techniques to obtain the best known key recovery attack on 5-round AES in the single-key model, reducing its overall complexity from about\\(2^{32}\\)to about\\(2^{22.5}\\). Extending our techniques to 7-round AES, we obtain the best known attacks on AES-192 which use practical amounts of data and memory, breaking the record for such attacks which was obtained 18 years ago by the classical Square attack.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_7"}, {"primary_key": "3385028", "vector": [], "sparse_vector": [], "title": "Indifferentiable Authenticated Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study Authenticated Encryption with Associated Data (AEAD) from the viewpoint of composition in arbitrary (single-stage) environments. We use the indifferentiability framework to formalize the intuition that a “good” AEAD scheme should have random ciphertexts subject to decryptability. Within this framework, we can then apply the indifferentiability composition theorem to show that such schemes offer extra safeguards wherever the relevant security properties are not known, or cannot be predicted in advance, as in general-purpose crypto libraries and standards. We show, on the negative side, that generic composition (in many of its configurations) and well-known classical and recent schemes fail to achieve indifferentiability. On the positive side, we give a provably indifferentiable Feistel-based construction, which reduces the round complexity from at least 6, needed for blockciphers, to only 3 for encryption. This result is not too far off the theoretical optimum as we give a lower bound that rules out the indifferentiability ofanyconstruction with less than 2 rounds.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_7"}, {"primary_key": "3385029", "vector": [], "sparse_vector": [], "title": "Combiners for Backdoored Random Oracles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sogol Ma<PERSON>heri"], "summary": "We formulate and study the security of cryptographic hash functions in thebackdoored random-oracle(BRO) model, whereby a big brother designs a “good” hash function, but can also seearbitrary functionsof its table via backdoor capabilities. This model captures intentional (and unintentional) weaknesses due to the existence of collision-finding or inversion algorithms, but goes well beyond them by allowing, for example, to search for structured preimages. The latter can easily break constructions that are secure under random inversions. BROs make the task of bootstrapping cryptographic hardness somewhat challenging. Indeed, with only a single arbitrarily backdoored function no hardness can be bootstrapped as any construction can be inverted. However, when two (or more) independent hash functions are available, hardness emergeseven with unrestricted and adaptive access to all backdoor oracles. At the core of our results lie new reductions from cryptographic problems to thecommunication complexitiesof various two-party tasks. Along the way we establish a communication complexity lower bound for set-intersection for cryptographically relevant ranges of parameters and distributions and where set-disjointness can be easy.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_10"}, {"primary_key": "3385030", "vector": [], "sparse_vector": [], "title": "Sub-linear Lattice-Based Zero-Knowledge Arguments for Arithmetic Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose the first zero-knowledge argument with sub-linear communication complexity for arithmetic circuit satisfiability over a prime\\({p}\\)whose security is based on the hardness of the short integer solution (SIS) problem. For a circuit with\\({N}\\)gates, the communication complexity of our protocol is\\(O\\left( \\sqrt{{N}{\\lambda }\\log ^3{{N}}}\\right) \\), where\\({\\lambda }\\)is the security parameter. A key component of our construction is a surprisingly simple zero-knowledge proof for pre-images of linear relations whose amortized communication complexity depends only logarithmically on the number of relations being proved. This latter protocol is a substantial improvement, both theoretically and in practice, over the previous results in this line of research of <PERSON><PERSON><PERSON><PERSON> et al. (CRYPTO 2012), <PERSON><PERSON> et al. (CRYPTO 2016), <PERSON><PERSON><PERSON> et al. (EUROCRYPT 2017) and <PERSON> and <PERSON> (CRYPTO 2017), and we believe it to be of independent interest.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_23"}, {"primary_key": "3385031", "vector": [], "sparse_vector": [], "title": "Cryptanalysis via Algebraic Spans.", "authors": ["<PERSON><PERSON>", "Arkadius <PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a method for obtaining provable polynomial time solutions of problems in nonabelian algebraic cryptography. This method is widely applicable, easier to apply, and more efficient than earlier methods. After demonstrating its applicability to the major classic nonabelian protocols, we use this method to cryptanalyze the Triple Decomposition key exchange protocol, the only classic group theory based key exchange protocol that could not be cryptanalyzed by earlier methods.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_9"}, {"primary_key": "3385032", "vector": [], "sparse_vector": [], "title": "On the Local Leakage Resilience of Linear Secret Sharing Schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the following basic question: to what extent are standard secret sharing schemes and protocols for secure multiparty computation that build on them resilient to leakage? We focus on a simplelocal leakagemodel, where the adversary can apply an arbitrary function of a bounded output length to the secret state of each party, but cannot otherwise learn joint information about the states. We show that additive secret sharing schemes and high-threshold instances of <PERSON><PERSON><PERSON>’s secret sharing scheme are secure under local leakage attacks when the underlying field is of a large prime order and the number of parties is sufficiently large. This should be contrasted with the fact that any linear secret sharing scheme over a small characteristic field is clearly insecure under local leakage attacks, regardless of the number of parties. Our results are obtained via tools from Fourier analysis and additive combinatorics. We present two types of applications of the above results and techniques. As a positive application, we show that the “GMW protocol” for honest-but-curious parties, when implemented using shared products of random field elements (so-called “Beaver Triples”), is resilient in the local leakage model for sufficiently many parties and over certain fields. This holds even when the adversary hasfull accessto a constant fraction of the views. As a negative application, we rule out multi-party variants of the share conversion scheme used in the 2-party homomorphic secret sharing scheme of <PERSON> et al. (Crypto 2016).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_18"}, {"primary_key": "3385033", "vector": [], "sparse_vector": [], "title": "From Laconic Zero-Knowledge to Public-Key Cryptography - Extended Abstract.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since its inception, public-key encryption (\\(\\mathsf {PKE}\\)) has been one of the main cornerstones of cryptography. A central goal in cryptographic research is to understand the foundations of public-key encryption and in particular, base its existence on a natural and generic complexity-theoretic assumption. An intriguing candidate for such an assumption is the existence of a cryptographically hard language. In this work we prove that public-key encryption can be based on the foregoing assumption, as long as the (honest) prover in the zero-knowledge protocol isefficientandlaconic. That is, messages that the prover sends should be efficiently computable (given thewitness) and short (i.e., of sufficiently sub-logarithmic length). Actually, our result is stronger and only requires the protocol to be zero-knowledge for anhonest-verifierand sound against computationally bounded cheating provers. Languages inwith such laconic zero-knowledge protocols are known from a variety of computational assumptions (e.g., Quadratic Residuocity, Decisional Diffie-Hellman, Learning with Errors, etc.). Thus, our main result can also be viewed as giving a unifying framework for constructing\\(\\mathsf {PKE}\\)which, in particular, captures many of the assumptions that were already known to yield\\(\\mathsf {PKE}\\). We also show several extensions of our result. First, that a certain weakening of our assumption on laconic zero-knowledge is actuallyequivalentto\\(\\mathsf {PKE}\\), thereby giving a complexity-theoretic characterization of\\(\\mathsf {PKE}\\). Second, a mild strengthening of our assumption also yields a (2-message) oblivious transfer protocol.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_23"}, {"primary_key": "3385034", "vector": [], "sparse_vector": [], "title": "A Simple Obfuscation Scheme for Pattern-Matching with Wildcards.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a simple and efficient method for obfuscating pattern matching with wildcards. In other words, we construct a way to check an input against a secret pattern, which is described in terms of prescribed values interspersed with unconstrained “wildcard” slots. As long as the support of the pattern is sufficiently sparse and the pattern itself is chosen from an appropriate distribution, we prove that a polynomial-time adversary cannot find a matching input, except with negligible probability. We rely upon the generic group heuristic (in a regular group, with no multilinearity). Previous work [9,10,32] provided less efficient constructions based on multilinear maps or LWE.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_25"}, {"primary_key": "3385035", "vector": [], "sparse_vector": [], "title": "Verifiable Delay Functions.", "authors": ["<PERSON>", "<PERSON>", "Benedikt <PERSON>", "<PERSON>"], "summary": "We study the problem of building averifiable delay function(VDF). A\\(\\text {VDF}\\)requires a specified number of sequential steps to evaluate, yet produces a unique output that can be efficiently and publicly verified.\\(\\text {VDF}\\)s have many applications in decentralized systems, including public randomness beacons, leader election in consensus protocols, and proofs of replication. We formalize the requirements for\\(\\text {VDF}\\)s and present new candidate constructions that are the first to achieve an exponential gap between evaluation and verification time.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_25"}, {"primary_key": "3385036", "vector": [], "sparse_vector": [], "title": "Threshold Cryptosystems from Threshold Fully Homomorphic Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a general approach to adding a threshold functionality to a large class of (non-threshold) cryptographic schemes. A threshold functionality enables a secret key to be split into a number of shares, so that only a threshold of parties can use the key, without reconstructing the key. We begin by constructing athresholdfully-homomorphic encryption scheme (ThFHE) from the learning with errors (LWE) problem. We next introduce a new concept, called auniversal thresholdizer, from which many threshold systems are possible. We show how to construct a universal thresholdizer from our ThFHE. A universal thresholdizer can be used to add threshold functionality to many systems, such as CCA-secure public-key encryption (PKE), signature schemes, pseudorandom functions, and others primitives. In particular, by applying this paradigm to a (non-threshold) lattice signature system, we obtain the first single-round threshold signature scheme from LWE.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_19"}, {"primary_key": "3385037", "vector": [], "sparse_vector": [], "title": "Fast Homomorphic Evaluation of Deep Discretized Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The rise of machine learning as a service multiplies scenarios where one faces a privacy dilemma: either sensitive user data must be revealed to the entity that evaluates the cognitive model (e.g., in the Cloud), or the model itself must be revealed to the user so that the evaluation can take place locally. Fully Homomorphic Encryption (FHE) offers an elegant way to reconcile these conflicting interests in the Cloud-based scenario and also preserve non-interactivity. However, due to the inefficiency of existing FHE schemes, most applications prefer to use Somewhat Homomorphic Encryption (SHE), where the complexity of the computation to be performed has to be known in advance, and the efficiency of the scheme depends on this global complexity. In this paper, we present a new framework for homomorphic evaluation of neural networks, that we callFHE–DiNN, whose complexity is strictly linear in the depth of the network and whose parameters can be set beforehand. To obtain this scale-invariance property, we rely heavily on the bootstrapping procedure. We refine the recent FHE construction by Chi<PERSON>ttiet al.(ASIACRYPT 2016) in order to increase the message space and apply the sign function (that we use to activate the neurons in the network) during the bootstrapping. We derive some empirical results, using TFHE library as a starting point, and classify encrypted images from the MNIST dataset with more than 96% accuracy in less than 1.7 s. Finally, as a side contribution, we analyze and introduce some variations to the bootstrapping technique of Chillottiet al.that offer an improvement in efficiency at the cost of increasing the storage requirements.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_17"}, {"primary_key": "3385038", "vector": [], "sparse_vector": [], "title": "Must the Communication Graph of MPC Protocols be an Expander?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure multiparty computation (MPC) on incomplete communication networks has been studied within two primary models: (1) Where a partial network is fixed a priori, and thus corruptions can occur dependent on its structure, and (2) Where edges in the communication graph are determined dynamically as part of the protocol. Whereas a rich literature has succeeded in mapping out the feasibility and limitations of graph structures supporting secure computation in the fixed-graph model (including strong classical lower bounds), these bounds do not apply in the latter dynamic-graph setting, which has recently seen exciting new results, but remains relatively unexplored. In this work, we initiate a similar foundational study of MPC within the dynamic-graph model. As a first step, we investigate the property of graphexpansion. All existing protocols (implicitly or explicitly) yield communication graphs which are expanders, but it is not clear whether this is inherent. Our results consist of two types: Upper bounds: We demonstrate secure protocols whose induced communication graphs arenotexpanders, within a wide range of settings (computational, information theoretic, with low locality, and adaptive security), each assuming some form of input-independent setup. Lower bounds: In the setting without setup and adaptive corruptions, we demonstrate that for certain functionalities,noprotocol can maintain a non-expanding communication graph against all adversarial strategies. Our lower bound relies only on protocol correctness (not privacy), and requires a surprisingly delicate argument.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_9"}, {"primary_key": "3385039", "vector": [], "sparse_vector": [], "title": "Limits of Practical Sublinear Secure Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "Secure computations on big data call for protocols that have sublinear communication complexity in the input length. While fully homomorphic encryption (FHE) provides a general solution to the problem, employing it on a large scale is currently quite far from being practical. This is also the case for secure computation tasks that reduce to weaker forms of FHE such as “somewhat homomorphic encryption” or single-server private information retrieval (PIR). Quite unexpectedly, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (Eurocrypt 2004), <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (Asiacrypt 2005), and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (Asiacrypt 2015) have shown that in several natural instances of secure computation on big data, there are practical sublinear communication protocols that only require sublinear local computation and minimize the use of expensive public-key operations. This raises the question of whether similar protocols exist for other natural problems. In this paper we put forward a framework for separating “practical” sublinear protocols from “impractical” ones, and establish a methodology for identifying “provably hard” big-data problems that do not admit practical protocols. This is akin to the use of NP-completeness to separate hard algorithmic problems from easy ones. We show that while the previous protocols of <PERSON><PERSON><PERSON><PERSON> et al., <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> and Venkitasubramaniam are indeed classified as being “practical” in this framework, slight variations of the problems they solve and other natural computational problems on big data are hard. Our negative results are established by showing that the problem at hand is “PIR-hard” in the sense thatanysecure protocol for the problem implies PIR on a large database. This imposes a barrier on the local computational cost of secure protocols for the problem. We also identify a new natural relaxation of PIR that we callsemi-PIR,which is useful for establishing “intermediate hardness” of several practically motivated secure computation tasks. We show that semi-PIR impliesslightlysublinear PIR via an adaptive black-box reduction and that ruling out a stronger black-box reduction would imply a major breakthrough in complexity theory. We also establish information-theoretic separations between semi-PIR and PIR, showing that some problems that we prove to be semi-PIR-hard are not PIR-hard.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_11"}, {"primary_key": "3385040", "vector": [], "sparse_vector": [], "title": "Quantum FHE (Almost) As Secure As Classical.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Fully homomorphic encryption schemes (FHE) allow to apply arbitrary efficient computation to encrypted data without decrypting it first. In Quantum FHE (QFHE) we may want to apply an arbitraryquantumlyefficient computation to (classical or quantum) encrypted data. We present a QFHE scheme with classical key generation (and classical encryption and decryption if the encrypted message is itself classical) with comparable properties to classical FHE. Security relies on the hardness of the learning with errors (LWE) problem with polynomial modulus, which translates to the worst case hardness of approximating short vector problems in lattices to within apolynomialfactor. Up to polynomial factors, this matches the best known assumption for classical FHE. Similarly to the classical setting, relying on LWE alone only impliesleveledQFHE (where the public key length depends linearly on the maximal allowed evaluation depth). An additionalcircular securityassumption is required to support completely unbounded depth. Interestingly, our circular security assumption is the same assumption that is made to achieve unbounded depthmulti-keyclassical FHE. Technically, we rely on the outline of <PERSON><PERSON><PERSON> (arXiv 2017) which achieves this functionality by relying on super-polynomial LWE modulus and on a new circular security assumption. We observe a connection between thefunctionalityof evaluating quantum gates and thecircuit privacyproperty of classical homomorphic encryption. While this connection is not sufficient to imply QFHE by itself, it leads us to a path that ultimately allows using classical FHE schemes with polynomial modulus towards constructing QFHE with the same modulus.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_3"}, {"primary_key": "3385041", "vector": [], "sparse_vector": [], "title": "Amortized Complexity of Information-Theoretically Secure MPC Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A fundamental and widely-applied paradigm due to <PERSON> and <PERSON> (STOC 1992) on Shamir-secret-sharing based generaln-player MPC shows how one may trade theadversary thresholdtagainstamortizedcommunication complexity, by using a so-called packed version of <PERSON><PERSON><PERSON>’s scheme. For e.g. the BGW-protocol (with active security), this trade-off means that if\\(t + 2k -2 < n/3\\), thenkparallelevaluations of thesamearithmetic circuit on different inputs can be performed at the overall cost corresponding to asingleBGW-execution. In this paper we propose a novel paradigm for amortized MPC that offers adifferenttrade-off, namely with the size of the field of the circuit which is securely computed, instead of the adversary threshold. Thus, unlike the Franklin-Yung paradigm, this leaves the adversary thresholdunchanged. Therefore, for instance, this paradigm may yield constructions enjoying the maximal adversary threshold\\(\\lfloor (n-1)/3 \\rfloor \\)in the BGW-model (secure channels, perfect security, active adversary, synchronous communication). Our idea is to compile an MPC for a circuit over an extension field to a parallel MPC of the same circuit but with inputs defined over itsbase fieldand with thesameadversary threshold. Key technical handles are our notion ofreverse multiplication-friendly embeddings(RMFE) and our proof, by algebraic-geometric means, that these areconstant-rate, as well as efficient auxiliary protocols for creating “subspace-randomness” with good amortized complexity. In the BGW-model, we show that the latter can be constructed by combining our tensored-up linear secret sharing with protocols based on hyper-invertible matrices á la Beerliova-Hirt (or variations thereof). Along the way, we suggest alternatives for hyper-invertible matrices with the same functionality but which can be defined over a large enough constant size field, which we believe is of independent interest. As a demonstration of the merits of the novel paradigm, we show that, in the BGW-model and with an optimal adversary threshold\\(\\lfloor (n-1)/3 \\rfloor \\), it is possible to securely compute abinary circuitwith amortized complexityO(n) ofbits per gate per instance. Known results would give\\(n \\log n\\)bits instead. By combining our result with the Franklin-Yung paradigm, and assuming asub-optimal adversary(i.e., an arbitrarily small\\(\\epsilon >0\\)fraction below 1/3), this is improved toO(1) bits instead ofO(n).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_14"}, {"primary_key": "3385042", "vector": [], "sparse_vector": [], "title": "GGH15 Beyond Permutation Branching Programs: Proofs, Attacks, and Candidates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We carry out a systematic study of the GGH15 graded encoding scheme used withgeneralbranching programs. This is motivated by the fact that general branching programs are more efficient than permutation branching programs and also substantially more expressive in the read-once setting. Our main results are as follows: Proofs.We present new constructions of private constrained PRFs and lockable obfuscation, for constraints (resp. functions to be obfuscated) that are computable by general branching programs. Our constructions are secure under LWE with subexponential approximation factors. Previous constructions of this kind crucially rely on the permutation structure of the underlying branching programs. Using general branching programs allows us to obtain more efficient constructions for certain classes of constraints (resp. functions), while posing new challenges in the proof, which we overcome using new proof techniques. Attacks.We extend the previous attacks on indistinguishability obfuscation (iO) candidates that use GGH15 encodings. The new attack simply uses the rank of a matrix as the distinguisher, so we call it a “rank attack”. The rank attack breaks, among others, the iO candidate for general read-once branching programs by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (CCS 2017). Candidate Witness Encryption and iO.Drawing upon insights from our proofs and attacks, we present simple candidates for witness encryption and iO that resist the existing attacks, using GGH15 encodings. Our candidate for witness encryption crucially exploits the fact that formulas in conjunctive normal form (CNFs) can be represented by general,read-oncebranching programs.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_20"}, {"primary_key": "3385043", "vector": [], "sparse_vector": [], "title": "Cryptanalyses of Branching Program Obfuscations over GGH13 Multilinear Map from the NTRU Problem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose cryptanalyses of all existing indistinguishability obfuscation (iO) candidates based on branching programs (BP) over GGH13 multilinear map for all recommended parameter settings. To achieve this, we introduce two novel techniques,program convertingusing NTRU-solver andmatrix zeroizing, which can be applied to a wide range of obfuscation constructions and BPs compared to previous attacks. We then prove that, for the suggested parameters, the existing general-purpose BP obfuscations over GGH13 do not have the desired security. Especially, the first candidate indistinguishability obfuscation with input-unpartitionable branching programs (FOCS 2013) and the recent BP obfuscation (TCC 2016) are not secure against our attack when they use the GGH13 with recommended parameters. Previously, there has been no known polynomial time attack for these cases. Our attack shows that the lattice dimension of GGH13 must be set much larger than previous thought in order to maintain security. More precisely, the underlying lattice dimension of GGH13 should be set to\\(n=\\tilde{\\varTheta }( \\kappa ^2 \\lambda )\\)to rule out attacks from the subfield algorithm for NTRU where\\(\\kappa \\)is the multilinearity level and\\(\\lambda \\)the security parameter.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_7"}, {"primary_key": "3385044", "vector": [], "sparse_vector": [], "title": "Fast Large-Scale Honest-Majority MPC for Malicious Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Protocols for secure multiparty computation enable a set of parties to compute a function of their inputs without revealing anything but the output. The security properties of the protocol must be preserved in the presence of adversarial behavior. The two classic adversary models considered aresemi-honest(where the adversary follows the protocol specification but tries to learn more than allowed by examining the protocol transcript) andmalicious(where the adversary may follow any arbitrary attack strategy). Protocols for semi-honest adversaries are often far more efficient, but in many cases the security guarantees are not strong enough. In this paper, we present new protocols for securely computing any functionality represented by an arithmetic circuit. We utilize a new method for verifying that the adversary does not cheat, that yields a cost of justtwicethat of semi-honest protocols in some settings. Our protocols are information-theoretically secure in the presence of a malicious adversaries, assuming an honest majority. We present protocol variants for small and large fields, and show how to efficiently instantiate them based on replicated secret sharing and Shamir sharing. As with previous works in this area aiming to achieve high efficiency, our protocol issecure with abortand does not achieve fairness, meaning that the adversary may receive output while the honest parties do not. We implemented our protocol and ran experiments for different numbers of parties, different network configurations and different circuit depths. Our protocol significantly outperforms the previous best for this setting (<PERSON><PERSON> and <PERSON>, CCS 2017); for a large number of parties, our implementation runs almost an order of magnitude faster than theirs.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_2"}, {"primary_key": "3385045", "vector": [], "sparse_vector": [], "title": "Provable Security of (Tweakable) Block Ciphers Based on Substitution-Permutation Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Aishwarya Thiruvengadam", "<PERSON><PERSON>"], "summary": "Substitution-Permutation Networks(SPNs) refer to a family of constructions which build awn-bit block cipher fromn-bit public permutations (often called S-boxes), which alternate keyless and “local” substitution steps utilizing such S-boxes, with keyed and “global” permutation steps which are non-cryptographic. Many widely deployed block ciphers are constructed based on the SPNs, but there are essentially no provable-security results about SPNs. In this work, we initiate a comprehensive study of the provable security of SPNs as (possibly tweakable)wn-bit block ciphers, when the underlyingn-bit permutation is modeled as a public random permutation. When the permutation step islinear(which is the case for most existing designs), we show that 3 SPN rounds are necessary and sufficient for security. On the other hand, even 1-round SPNs can be secure when non-linearity is allowed. Moreover, 2-round non-linear SPNs can achieve “beyond-birthday” (up to\\(2^{2n/3}\\)adversarial queries) security, and, as the number of non-linear rounds increases, our bounds are meaningful for the number of queries approaching\\(2^n\\). Finally, our non-linear SPNs can be madetweakableby incorporating the tweak into the permutation layer, and provide good multi-user security. As an application, our construction can turn two publicn-bit permutations (or fixed-key block ciphers) into a tweakable block cipher working onwn-bit inputs, 6n-bit key and ann-bit tweak (for any\\(w\\ge 2\\)); the tweakable block cipher provides security up to\\(2^{2n/3}\\)adversarial queries in the random permutation model, while only requiringwcalls to each permutation, and 3wfield multiplications for eachwn-bit input.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_24"}, {"primary_key": "3385046", "vector": [], "sparse_vector": [], "title": "Non-Uniform Bounds in the Random-Permutation, Ideal-Cipher, and Generic-Group Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The random-permutation model (RPM) and the ideal-cipher model (ICM) are idealized models that offer a simple and intuitive way to assess the conjectured standard-model security of many important symmetric-key and hash-function constructions. Similarly, the generic-group model (GGM) captures generic algorithms against assumptions in cyclic groups by modeling encodings of group elements as random injections and allows to derive simple bounds on the advantage of such algorithms. Unfortunately, both well-known attacks, e.g., based on rainbow tables (<PERSON><PERSON>, IEEE Transactions on Information Theory ’80), and more recent ones, e.g., against the discrete-logarithm problem (<PERSON><PERSON><PERSON> and <PERSON>, EUROCRYPT ’18), suggest that the concrete security bounds one obtains from such idealized proofs are oftencompletely inaccurateif one considersnon-uniformorpreprocessingattacks in the standard model. To remedy this situation, this work defines the auxiliary-input (AI) RPM/ICM/GGM, which capture both non-uniform and preprocessing attacks by allowing an attacker to leak an arbitrary (bounded-output) function of the oracle’s function table; derives thefirstnon-uniform bounds for a number of important practical applications in the AI-RPM/ICM, including constructions based on the Merkle-Damgård and sponge paradigms, which underly the SHA hashing standards, and for AI-RPM/ICM applications with computational security; and using simpler proofs, recovers the AI-GGM security bounds obtained by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> against preprocessing attackers, for a number of assumptions related to cyclic groups, such as discrete logarithms and Diffie-Hellman problems, and provides new bounds for two assumptions. An important step in obtaining these results is to port the tools used in recent work by Coretti et al. (EUROCRYPT ’18) from the ROM to the RPM/ICM/GGM, resulting in very powerful and easy-to-use tools for proving security bounds against non-uniform and preprocessing attacks.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_23"}, {"primary_key": "3385047", "vector": [], "sparse_vector": [], "title": "SPDℤ2k: Efficient MPC mod 2k for Dishonest Majority.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most multi-party computation protocols allow secure computation of arithmetic circuits over a finite field, such as the integers modulo a prime. In the more natural setting of integer computations modulo\\(2^{k}\\), which are useful for simplifying implementations and applications, no solutions with active security are known unless the majority of the participants are honest. We present a new scheme for information-theoretic MACs that are homomorphic modulo\\(2^k\\), and are as efficient as the well-known standard solutions that are homomorphic over fields. We apply this to construct an MPC protocol for dishonest majority in the preprocessing model that has efficiency comparable to the well-known SPDZ protocol (<PERSON><PERSON><PERSON><PERSON> et al., CRYPTO 2012), with operations modulo\\(2^k\\)instead of over a field. We also construct a matching preprocessing protocol based on oblivious transfer, which is in the style of the MASCOT protocol (<PERSON> et al., CCS 2016) and almost as efficient.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_26"}, {"primary_key": "3385048", "vector": [], "sparse_vector": [], "title": "Yet Another Compiler for Active Security or: Efficient MPC Over Arbitrary Rings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a very simple yet very powerful idea for turning any passively secure MPC protocol into an actively secure one, at the price of reducing the threshold of tolerated corruptions. Our compiler leads to a very efficient MPC protocols for the important case of secure evaluation of arithmetic circuits over arbitrary rings (e.g., the natural case of\\({\\mathbb {Z}}_{2^{\\ell }}\\!\\)) for a small number of parties. We show this by giving a concrete protocol in the preprocessing model for the popular setting with three parties and one corruption. This is the first protocol for secure computation over rings that achieves active security with constant overhead.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_27"}, {"primary_key": "3385049", "vector": [], "sparse_vector": [], "title": "Encrypt or Decrypt? To Make a Single-Key Beyond Birthday Secure Nonce-Based MAC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At CRYPTO 2016, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> have proposed a highly secure nonce-based MAC called Encry<PERSON> <PERSON><PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON> (\\(\\textsf {EWCDM}\\)) construction, as\\(\\textsf {E}_{K_2}\\bigl (\\textsf {E}_{K_1}(N)\\oplus N\\oplus \\textsf {H}_{K_h}(M)\\bigr )\\)for a nonceNand a messageM. This construction achieves roughly\\(2^{2n/3}\\)bit MAC security with the assumption that\\(\\textsf {E}\\)is a PRP securen-bit block cipher and\\(\\textsf {H}\\)is an almost xor universaln-bit hash function. In this paper we propose Decrypted <PERSON><PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON> (\\(\\textsf {DWCDM}\\)) construction, which is structurally very similar to its predecessor\\(\\textsf {EWCDM}\\)except that the outer encryption call is replaced by decryption. The biggest advantage of\\(\\textsf {DWCDM}\\)is that we can make a truly single key MAC: the two block cipher calls can use the same block cipher key\\(K=K_1=K_2\\). Moreover, we can derive the hash key as\\(K_h=\\textsf {E}_K(1)\\), as long as\\(|K_h|=n\\). Whether we use encryption or decryption in the outer layer makes a huge difference; using the decryption instead enables us to apply an extended version of the mirror theory by Patarin to the security analysis of the construction.\\(\\textsf {DWCDM}\\)is secure beyond the birthday bound, roughly up to\\(2^{2n/3}\\)MAC queries and\\(2^n\\)verification queries against nonce-respecting adversaries.\\(\\textsf {DWCDM}\\)remains secure up to\\(2^{n/2}\\)MAC queries and\\(2^n\\)verification queries against nonce-misusing adversaries.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_21"}, {"primary_key": "3385050", "vector": [], "sparse_vector": [], "title": "Searchable Encryption with Optimal Locality: Achieving Sublogarithmic Read Efficiency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose the first linear-space searchable encryption scheme with constant locality andsublogarithmicread efficiency, strictly improving the previously best known read efficiency bound (<PERSON><PERSON><PERSON> et al., STOC 2016) from\\(\\varTheta (\\log N \\log \\log N)\\)to\\(O(\\log ^{\\gamma } N)\\)where\\(\\gamma =\\frac{2}{3}+\\delta \\)for any fixed\\(\\delta >0\\)and whereNis the number of keyword-document pairs. Our scheme employs four different allocation algorithms for storing the keyword lists, depending on the size of the list considered each time. For our construction we develop (i) new probability bounds for the offline two-choice allocation problem; (ii) and a new I/O-efficient oblivious RAM with\\(\\tilde{O}(n^{1/3})\\)bandwidth overhead and zero failure probability, both of which can be of independent interest.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_13"}, {"primary_key": "3385051", "vector": [], "sparse_vector": [], "title": "An Optimal Distributed Discrete Log Protocol with Applications to Homomorphic Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The distributed discrete logarithm (DDL) problem was introduced by <PERSON> et al. at CRYPTO 2016. A protocol solving this problem was the main tool used in the share conversion procedure of their homomorphic secret sharing (HSS) scheme which allows non-interactive evaluation of branching programs among two parties over shares of secret inputs. Letgbe a generator of a multiplicative group\\(\\mathbb {G}\\). Given a random group element\\(g^{x}\\)and an unknown integer\\(b \\in [-M,M]\\)for a smallM, two partiesAandB(that cannot communicate) successfully solve DDL if\\(A(g^{x}) - B(g^{x+b}) = b\\). Otherwise, the parties err. In the DDL protocol of <PERSON> et al.,AandBrun in timeTand have error probability that is roughly linear inM/T. Since it has a significant impact on the HSS scheme’s performance, a major open problem raised by <PERSON> et al. was to reduce the error probability as a function ofT. In this paper we devise a new DDL protocol that substantially reduces the error probability to\\(O(M \\cdot T^{-2})\\). Our new protocol improves the asymptotic evaluation time complexity of the HSS scheme by <PERSON> et al. on branching programs of sizeSfrom\\(O(S^2)\\)to\\(O(S^{3/2})\\). We further show that our protocol is optimal up to a constant factor for all relevant cryptographic group families, unless one can solve the discrete logarithm problem in ashortinterval of lengthRin time\\(o(\\sqrt{R})\\). Our DDL protocol is based on a new type of random walk that is composed of several iterations in which the expected step length gradually increases. We believe that this random walk is of independent interest and will find additional applications.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_8"}, {"primary_key": "3385052", "vector": [], "sparse_vector": [], "title": "Rasta: A Cipher with Low ANDdepth and Few ANDs per Bit.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent developments in multi party computation (MPC) and fully homomorphic encryption (FHE) promoted the design and analysis of symmetric cryptographic schemes that minimize multiplications in one way or another. In this paper, we propose with Ra<PERSON>a design strategy for symmetric encryption that has ANDdepthdand at the same time only needsdANDs per encrypted bit. Even for very low values ofdbetween 2 and 6 we can give strong evidence that attacks may not exist. This contributes to a better understanding of the limits of what concrete symmetric-key constructions can theoretically achieve with respect to AND-related metrics, and is to the best of our knowledge the first attempt that minimizes both metrics simultaneously. Furthermore, we can give evidence that for choices ofdbetween 4 and 6 the resulting implementation properties may well be competitive by testing our construction in the use-case of removing the large ciphertext-expansion when using the BGV scheme.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_22"}, {"primary_key": "3385053", "vector": [], "sparse_vector": [], "title": "Fast Message Franking: From Invisible Salamanders to Encryptment.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Message franking enables cryptographically verifiable reporting of abusive messages in end-to-end encrypted messaging. <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> recently formalized the needed underlying primitive, what they call compactly committing authenticated encryption (AE), and analyze security of a number of approaches. But all known secure schemes are still slow compared to the fastest standard AE schemes. For this reason Facebook Messenger uses AES-GCM for franking of attachments such as images or videos. We show how to break Facebook’s attachment franking scheme: a malicious user can send an objectionable image to a recipient but that recipient cannot report it as abuse. The core problem stems from use of fast but non-committing AE, and so we build the fastest compactly committing AE schemes to date. To do so we introduce a new primitive, called encryptment, which captures the essential properties needed. We prove that, unfortunately, schemes with performance profile similar to AES-GCM won’t work. Instead, we show how to efficiently transform Merkle-Damgärd-style hash functions into secure encryptments, and how to efficiently build compactly committing AE from encryptment. Ultimately our main construction allows franking using just a single computation of SHA-256 or SHA-3. Encryptment proves useful for a variety of other applications, such as remotely keyed AE and concealments, and our results imply the first single-pass schemes in these settings as well.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_6"}, {"primary_key": "3385054", "vector": [], "sparse_vector": [], "title": "Dissection-BKW.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The slightly subexponential algorithm of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (BKW) provides a basis for assessing LPN/LWE security. However, its huge memory consumption strongly limits its practical applicability, thereby preventing precise security estimates for cryptographic LPN/LWE instantiations. We provide the first time-memory trade-offs for the BKW algorithm. For instance, we show how to solve LPN in dimensionkin time\\(2^{\\frac{4}{3} \\frac{k}{\\log k} }\\)and memory\\(2^{\\frac{2}{3} \\frac{k}{\\log k} }\\). Using the Dissection technique due to <PERSON><PERSON> et al. (Crypto ’12) and a novel, slight generalization thereof, we obtain fine-grained trade-offs for any available (subexponential) memory while the running timeremains subexponential. Reducing the memory consumption of BKW below its running time also allows us to propose a first quantum version QBKW for the BKW algorithm.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_22"}, {"primary_key": "3385055", "vector": [], "sparse_vector": [], "title": "Fast Distributed RSA Key Generation for Semi-honest and Malicious Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present two new, highly efficient, protocols for securely generating a distributed RSA key pair in the two-party setting. One protocol is semi-honestly secure and the other maliciously secure. Both are constant round and do not rely on any specific number-theoretic assumptions and improve significantly over the state-of-the-art by allowing a slight leakage (which we show to not affect security). For our maliciously secure protocol our most significant improvement comes from executing most of the protocol in a “strong” semi-honest manner and then doing a single, light, zero-knowledge argument of correct execution. We introduce other significant improvements as well. One such improvement arrives in showing that certain, limited leakage does not compromise security, which allows us to use lightweight subprotocols. Another improvement, which may be of independent interest, comes in our approach for multiplying two large integers using OT, in the malicious setting, without being susceptible to a selective-failure attack. Finally, we implement our malicious protocol and show that its performance is an order of magnitude better than the best previous protocol, which provided onlysemi-honestsecurity.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_12"}, {"primary_key": "3385056", "vector": [], "sparse_vector": [], "title": "A Key-Recovery Attack on 855-round Trivium.", "authors": ["Ximing Fu", "<PERSON><PERSON>", "<PERSON><PERSON> Dong", "<PERSON><PERSON>"], "summary": "In this paper, we propose a key-recovery attack on Trivium reduced to 855 rounds. As the output is a complex Boolean polynomial over secret key and IV bits and it is hard to find the solution of the secret keys, we propose a novel nullification technique of the Boolean polynomial to reduce the output Boolean polynomial of 855-round Trivium. Then we determine the degree upper bound of the reduced nonlinear boolean polynomial and detect the right keys. These techniques can be applicable to most stream ciphers based on nonlinear feedback shift registers (NFSR). Our attack on 855-round Trivium costs time complexity\\(2^{77}\\). As far as we know, this is the best key-recovery attack on round-reduced Trivium. To verify our attack, we also give some experimental data on 721-round reduced Trivium.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_6"}, {"primary_key": "3385057", "vector": [], "sparse_vector": [], "title": "The Algebraic Group Model and its Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the most important and successful tools for assessing hardness assumptions in cryptography is the Generic Group Model (GGM). Over the past two decades, numerous assumptions and protocols have been analyzed within this model. While a proof in the GGM can certainly provide some measure of confidence in an assumption, its scope is rather limited since it does not capture group-specific algorithms that make use of the representation of the group. To overcome this limitation, we propose the Algebraic Group Model (AGM), a model that lies in between the Standard Model and the GGM. It is the first restricted model of computation covering group-specific algorithms yet allowing to derive simple and meaningful security statements. To prove its usefulness, we show that several important assumptions, among them the Computational <PERSON><PERSON><PERSON>, the Strong <PERSON>, and the interactive LRSW assumptions, are equivalent to the Discrete Logarithm (DLog) assumption in the AGM. On the more practical side, we prove tight security reductions for two important schemes in the AGM to DLog or a variant thereof: the BLS signature scheme and <PERSON>roth’s zero-knowledge SNARK (EUROCRYPT 2016), which is the most efficient SNARK for which only a proof in the GGM was known. Our proofs are quite simple and therefore less prone to subtle errors than those in the GGM. Moreover, in combination with known lower bounds on the Discrete Logarithm assumption in the GGM, our results can be used to derive lower bounds for all the above-mentioned results in the GGM.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_2"}, {"primary_key": "3385058", "vector": [], "sparse_vector": [], "title": "Trapdoor Functions from the Computational Diffie-Hellman Assumption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Trapdoor functions (TDFs) are a fundamental primitive in cryptography. Yet, the current set of assumptions known to imply TDFs is surprisingly limited, when compared to public-key encryption. We present a new general approach for constructing TDFs. Specifically, we give a generic construction of TDFs from any Chameleon Encryption (<PERSON><PERSON><PERSON><PERSON> and Garg [CRYPTO’17]) satisfying a novel property which we call recyclability. By showing how to adapt current Computational Diffie-Hellman (CDH) based constructions of chameleon encryption to yield recyclability, we obtain the first construction of TDFs with security proved under the CDH assumption. While TDFs from the Decisional Diffie-Hellman (DDH) assumption were previously known, the possibility of basing them on CDH had remained open for more than 30 years.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_13"}, {"primary_key": "3385059", "vector": [], "sparse_vector": [], "title": "Limits on the Power of Garbling Techniques for Public-Key Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Understanding whether public-key encryption can be based on one-way functions is a fundamental open problem in cryptography. The seminal work of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC’89] shows that black-box constructions of public-key encryption from one-way functions are impossible. However, this impossibility result leaves open the possibility of using non-black-box techniques for achieving this goal. One of the most powerful classes of non-black-box techniques, which can be based on one-way functions (OWFs) alone, is <PERSON>’s garbled circuit technique [FOCS’86]. As for the non-black-box power of this technique, the recent work of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [CRYPTO’17] shows that the use of garbling allows us to circumvent known black-box barriers in the context of identity-based encryption. We prove that garbling of circuits that have OWF (or even random oracle) gates in them are insufficient for obtaining public-key encryption. Additionally, we show that this model also captures (non-interactive) zero-knowledge proofs for relations with OWF gates. This indicates that currently known OWF-based non-black-box techniques are perhaps insufficient for realizing public-key encryption.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_12"}, {"primary_key": "3385060", "vector": [], "sparse_vector": [], "title": "On the Round Complexity of OT Extension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that any OT extension protocol based on one-way functions (or more generally any symmetric-key primitive) either requires an additional round compared to the base OTs or must make a non-black-box use of one-way functions. This result also holds in the semi-honest setting or in the case of certain setup models such as the common random string model. This implies that OT extension in any secure computation protocol must come at the price of an additional round of communication or the non-black-box use of symmetric key primitives. Moreover, we observe that our result is tight in the sense that positive results can indeed be obtained using non-black-box techniques or at the cost of one additional round of communication.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_19"}, {"primary_key": "3385061", "vector": [], "sparse_vector": [], "title": "Two-Round Multiparty Secure Computation Minimizing Public Key Operations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We show new constructions of semi-honest and malicious two-round multiparty secure computation protocols using only (a fixed)\\(\\mathsf {poly}(n,\\lambda )\\)invocations of a two-round oblivious transfer protocol (which use expensive public-key operations) and\\(\\mathsf {poly}(\\lambda , |C|)\\)cheaper one-way function calls, where\\(\\lambda \\)is the security parameter,nis the number of parties, andCis the circuit being computed. All previously known two-round multiparty secure computation protocols required\\(\\mathsf {poly}(\\lambda ,|C|)\\)expensive public-key operations.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_10"}, {"primary_key": "3385062", "vector": [], "sparse_vector": [], "title": "Adaptive Garbled RAM from Laconic Oblivious Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give a construction of an adaptive garbled RAM scheme. In the adaptive setting, a client first garbles a “large” persistent database which is stored on a server. Next, the client can provide garbling of multiple adaptively and adversarially chosen RAM programs that execute and modify the stored database arbitrarily. The garbled database and the garbled program should reveal nothing more than the running time and the output of the computation. Furthermore, the sizes of the garbled database and the garbled program grow only linearly in the size of the database and the running time of the executed program respectively (up to poly logarithmic factors). The security of our construction is based on the assumption that laconic oblivious transfer (<PERSON> et al., CRYPTO 2017) exists. Previously, such adaptive garbled RAM constructions were only known using indistinguishability obfuscation or in random oracle model. As an additional application, we note that this work yields the first constant round secure computation protocol for persistent RAM programs in the malicious setting from standard assumptions. Prior works did not support persistence in the malicious setting.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_18"}, {"primary_key": "3385063", "vector": [], "sparse_vector": [], "title": "Practical and Tightly-Secure Digital Signatures and Authenticated Key Exchange.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tight security is increasingly gaining importance in real-world cryptography, as it allows to choose cryptographic parameters in a way that is supported by a security proof, without the need to sacrifice efficiency by compensating the security loss of a reduction with larger parameters. However, for many important cryptographic primitives, including digital signatures and authenticated key exchange (AKE), we are still lacking constructions that are suitable for real-world deployment. We construct the first truly practical signature scheme with tight security in a real-world multi-user setting with adaptive corruptions. The scheme is based on a new way of applying the Fiat-Shamir approach to construct tightly-secure signatures from certain identification schemes. Then we use this scheme as a building block to construct the first practical AKE protocol with tight security. It allows the establishment of a key within 1 RTT in a practical client-server setting, provides forward security, is simple and easy to implement, and thus very suitable for practical deployment. It is essentially the “signed Diffie-Hellman” protocol, but with an additional message, which is crucial to achieve tight security. This additional message is used to overcome a technical difficulty in constructing tightly-secure AKE protocols. For a theoretically-sound choice of parameters and a moderate number of users and sessions, our protocol has comparable computational efficiency to the simple signed Diffie-Hellman protocol with EC-DSA, while for large-scale settings our protocol has even better computational performance, at moderately increased communication complexity.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_4"}, {"primary_key": "3385064", "vector": [], "sparse_vector": [], "title": "Non-malleable Secret Sharing for General Access Structures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON> (STOC’18) recently introduced the notion ofnon-malleable secret sharing. Very roughly, the guarantee they seek is the following: the adversary may potentially tamper with all of the shares, and still,eitherthe reconstruction procedure outputs the original secret,or, the original secret is “destroyed” and the reconstruction outputs a string which is completely “unrelated” to the original secret. Prior works on non-malleable codes in the 2 split-state model imply constructions which can be seen as 2-out-of-2 non-malleable secret sharing (NMSS) schemes. <PERSON><PERSON> and <PERSON> proposed constructions oft-out-of-nNMSS schemes. These constructions have already been shown to have a number of applications in cryptography. We continue this line of research and construct NMSS for more general access structures. We give a generic compiler that converts any statistical (resp. computational) secret sharing scheme realizing any access structure into another statistical (resp. computational) secret sharing scheme that not only realizes the same access structure but also ensures statistical non-malleability against a computationally unbounded adversary who tampers each of the shares arbitrarily and independently. Instantiating with known schemes we get unconditional NMMS schemes that realize any access structures generated by polynomial size monotone span programs. Similarly, we also obtain conditional NMMS schemes realizing access structure in\\(\\mathbf {monotone \\;P}\\)(resp.\\(\\mathbf {monotone \\;NP}\\)) assuming one-way functions (resp. witness encryption). Towards considering more general tampering models, we also propose a construction ofn-out-of-nNMSS. Our construction is secure even if the adversary could divide the shares into any two (possibly overlapping) subsets and then arbitrarily tamper the shares in each subset. Our construction is based on a property of inner product and an observation that the inner-product based construction of Aggarwal, Dodis and Lovett (STOC’14) is in fact secure against a tampering class that is stronger than 2 split-states. We also show applications of our construction to the problem of non-malleable message transmission.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_17"}, {"primary_key": "3385065", "vector": [], "sparse_vector": [], "title": "Risky Traitor Tracing and New Differential Privacy Negative Results.", "authors": ["<PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON>", "<PERSON>"], "summary": "In this work we seek to construct collusion-resistant traitor tracing systems with small ciphertexts from standard assumptions that also move toward practical efficiency. In our approach we will hold steadfast to the principle of collusion resistance, but relax the requirement on catching a traitor from a successful decoding algorithm. We define af-risky traitor tracing system as one where the probability of identifying a traitor is\\(f(\\lambda ,n)\\)times the probability a successful box is produced. We then go on to show how to build such systems from prime order bilinear groups with assumptions close to those used in prior works. Our core system achieves, for any\\(k > 0\\),\\(f(\\lambda ,n) \\approx \\frac{k}{n + k - 1}\\)where ciphertexts consists of\\((k + 4)\\)group elements and decryption requires\\((k + 3)\\)pairing operations. At first glance the utility of such a system might seem questionable since thefwe achieve for short ciphertexts is relatively small. Indeed an attacker in such a system can more likely than not get away with producing a decoding box. However, we believe this approach to be viable for four reasons: A risky traitor tracing system will provide deterrence against risk averse attackers. In some settings the consequences of being caught might bear a high cost and an attacker will have to weigh his utility of producing a decryptionDbox against the expected cost of being caught. Consider a broadcast system where we want to support low overhead broadcast encrypted communications, but will periodically allow for a more expensive key refresh operation. We refer to an adversary produced algorithm that maintains the ability to decrypt across key refreshes as a persistent decoder. We show how if we employ a risky traitor tracing systems in this setting, even for a smallf, we can amplify the chances of catching such a “persistent decoder” to be negligibly close to 1. In certain resource constrained settings risky traitor tracing provides a best tracing effort where there are no other collusion-resistant alternatives. For instance, suppose we had to support 100 K users over a radio link that had just 10 KB of additional resources for extra ciphertext overhead. None of the existing\\(\\sqrt{N}\\)bilinear map systems can fit in these constraints. On the other hand a risky traitor tracing system provides a spectrum of tracing probability versus overhead tradeoffs and can be configured to at least give some deterrence in this setting. Finally, we can capture impossibility results for differential privacy from\\(\\frac{1}{n}\\)-risky traitor tracing. Since our ciphertexts are short (\\(O(\\lambda )\\)), we get the negative result which matches what one would get plugging in the obfuscation based tracing system Boneh-Zhandry [9] solution into the prior impossibility result of Dwork et al. [14].", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_16"}, {"primary_key": "3385066", "vector": [], "sparse_vector": [], "title": "Updatable and Universal Common Reference Strings with Applications to zk-SNARKs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "By design, existing (pre-processing) zk-SNARKs embed a secret trapdoor in a relation-dependent common reference strings (CRS). The trapdoor is exploited by a (hypothetical) simulator to prove the scheme is zero knowledge, and the secret-dependent structure facilitates a linear-size CRS and linear-time prover computation. If known by a real party, however, the trapdoor can be used to subvert the security of the system. The structured CRS that makes zk-SNARKs practical also makes deploying zk-SNARKS problematic, as it is difficult to argue why the trapdoor would not be available to the entity responsible for generating the CRS. Moreover, for pre-processing zk-SNARKs a new trusted CRS needs to be computed every time the relation is changed. In this paper, we address both issues by proposing a model where a number of users can update a universal CRS. The updatable CRS model guarantees security if at least one of the users updating the CRS is honest. We provide both a negative result, by showing that zk-SNARKs with private secret-dependent polynomials in the CRS cannot be updatable, and a positive result by constructing a zk-SNARK based on a CRS consisting only of secret-dependent monomials. The CRS is of quadratic size, is updatable, and is universal in the sense that it can be specialized into one or more relation-dependent CRS of linear size with linear-time prover computation.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_24"}, {"primary_key": "3385067", "vector": [], "sparse_vector": [], "title": "Round-Optimal Secure Multi-Party Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Secure multi-party computation (MPC) is a central cryptographic task that allows a set of mutually distrustful parties to jointly compute some function of their private inputs where security should hold in the presence of a malicious adversary that can corrupt any number of parties. Despite extensive research, the precise round complexity of this “standard-bearer” cryptographic primitive is unknown. Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, in EUROCRYPT 2016 demonstrated that the round complexity of any MPC protocol relying on black-box proofs of security in the plain model must be at least four. Following this work, independently <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, CRYPTO 2017 and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and Polychroniadou, TCC 2017 made progress towards solving this question and constructed four-round protocols based on non-polynomial time assumptions. More recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in TCC 2017 closed the gap for two-party protocols by constructing a four-round protocol from polynomial-time assumptions. In another work, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and V<PERSON>nti TCC 2017 showed how to design a four-round multi-party protocol for the specific case of multi-party coin-tossing. In this work, we resolve this question by designing a four-round actively secure multi-party (two or more parties) protocol for general functionalities under standard polynomial-time hardness assumptions with a black-box proof of security.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_17"}, {"primary_key": "3385068", "vector": [], "sparse_vector": [], "title": "Faster Homomorphic Linear Transformations in HElib.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "HElibis a software library that implements homomorphic encryption (HE), with a focus on effective use of “packed” ciphertexts. An important operation is applying a known linear map to a vector of encrypted data. In this paper, we describe several algorithmic improvements that significantly speed up this operation: in our experiments, our new algorithms are 30–75 times faster than those previously implemented inHElibfor typical parameters. One application that can benefit from faster linear transformations is bootstrapping (in particular, “thin bootstrapping” as described in [Chen and <PERSON>, Eurocrypt 2018]). In some settings, our new algorithms for linear transformations result in a\\(6{\\times }\\)speedup for the entire thin bootstrapping operation. Our techniques also reduce the size of the large public evaluation key, often using 33%–50% less space than the previous HElib implementation. We also implemented a new tradeoff that enables a drastic reduction in size, resulting in a\\(25{\\times }\\)factor or more for some parameters, paying only a penalty of a 2–\\(4{\\times }\\)times slowdown in running time (and giving up some parallelization opportunities).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_4"}, {"primary_key": "3385069", "vector": [], "sparse_vector": [], "title": "TinyKeys: A New Approach to Efficient Multi-Party Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new approach to designing concretely efficient MPC protocols with semi-honest security in the dishonest majority setting. Motivated by the fact that within the dishonest majority setting the efficiency of most practical protocolsdoes not depend on the number of honest parties, we investigate how to construct protocols which improve in efficiency as the number of honest parties increases. Our central idea is to take a protocol which is secure for\\(n-1\\)corruptions and modify it to use short symmetric keys, with the aim of basing security on the concatenation of all honest parties’ keys. This results in a more efficient protocol tolerating fewer corruptions, whilst also introducing an LPN-style syndrome decoding assumption. We first apply this technique to a modified version of the semi-honest GMW protocol, using OT extension with short keys, to improve the efficiency of standard GMW with fewer corruptions. We also obtain more efficient constant-round MPC, using BMR-style garbled circuits with short keys, and present an implementation of the online phase of this protocol. Our techniques start to improve upon existing protocols when there are around\\(n=20\\)parties with\\(h=6\\)honest parties, and as these increase we obtain up to a 13 times reduction (for\\(n=400, h=120\\)) in communication complexity for our GMW variant, compared with the best-known GMW-based protocol modified to use the same threshold.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_1"}, {"primary_key": "3385070", "vector": [], "sparse_vector": [], "title": "On Tightly Secure Non-Interactive Key Exchange.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the reduction loss of security reductions for non-interactive key exchange (NIKE) schemes. Currently, no tightly secure NIKE schemes exist, and in fact <PERSON><PERSON> et al. (EUROCRYPT 2016) provide a lower bound (of\\(\\varOmega (n^2)\\), where\\(n\\)is the number of parties an adversary interacts with) on the reduction loss for a large class of NIKE schemes. We offer two results: the first NIKE scheme with a reduction loss of\\(n/2\\)that circumvents the lower bound of <PERSON><PERSON> et al., but is of course still far from tightly secure. Second, we provide a generalization of <PERSON><PERSON> et al.’s lower bound to a larger class of NIKE schemes (that also covers our NIKE scheme), with an adapted lower bound of\\(n/2\\)on the reduction loss. Hence, in that sense, the reduction for our NIKE scheme is optimal.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_3"}, {"primary_key": "3385071", "vector": [], "sparse_vector": [], "title": "The Curse of Small Domains: New Attacks on Format-Preserving Encryption.", "authors": ["Viet Tung Hoang", "<PERSON>", "<PERSON>"], "summary": "Format-preserving encryption (FPE) produces ciphertexts which have the same format as the plaintexts. Building secure FPE is very challenging, and recent attacks (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, CCS ’16; <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, CRYPTO ’17) have highlighted security deficiencies in the recent NIST SP800-38G standard. This has left the question open of whether practical schemes with high security exist. In this paper, we continue the investigation of attacks against FPE schemes. Our first contribution are new known-plaintext message recovery attacks against Feistel-based FPEs (such as FF1/FF3 from the NIST SP800-38G standard) which improve upon previous work in terms of amortized complexity in multi-target scenarios, where multiple ciphertexts are to be decrypted. Our attacks are also qualitatively better in that they make no assumptions on the correlation between the targets to be decrypted and the known plaintexts. We also surface a new vulnerability specific to FF3 and how it handles odd length domains, which leads to a substantial speedup in our attacks. We also show the first attacks against non-Feistel based FPEs. Specifically, we show a strong message-recovery attack for FNR, a construction proposed by Cisco which replaces two rounds in the Feistel construction with a pairwise-independent permutation, following the paradigm by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON>, ’99). We also provide a strong ciphertext-only attack against a variant of the DTP construction by <PERSON> and <PERSON>, which is deployed by Protegrity within commercial applications. All of our attacks show that existing constructions fall short of achieving desirable security levels. For Feistel and the FNR schemes, our attacks become feasible on small domains, e.g., 8 bits, for suggested round numbers. Our attack against the DTP construction is practical even for large domains. We provide proof-of-concept implementations of our attacks that verify our theoretical findings.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_8"}, {"primary_key": "3385072", "vector": [], "sparse_vector": [], "title": "Optimal Channel Security Against Fine-Grained State Compromise: The Safety of Messaging.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We aim to understand the best possible security of a (bidirectional) cryptographic channel against an adversary that may arbitrarily and repeatedly learn the secret state of either communicating party. We give a formal security definition and a proven-secure construction. This construction provides better security against state compromise than the Signal Double Ratchet Algorithm or any other known channel construction. To facilitate this we define and construct new forms of public-key encryption and digital signatures that update their keys over time.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_2"}, {"primary_key": "3385073", "vector": [], "sparse_vector": [], "title": "Pseudorandom Quantum States.", "authors": ["Zhengfeng Ji", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose the concept of pseudorandom quantum states, which appear random to any quantum polynomial-time adversary. It offers acomputationalapproximation to perfectly random quantum states analogous in spirit to cryptographic pseudorandom generators, as opposed tostatisticalnotions of quantum pseudorandomness that have been studied previously, such as quantumt-designs analogous tot-wise independent distributions. Under the assumption that quantum-secure one-way functions exist, we present efficient constructions of pseudorandom states, showing that our definition is achievable. We then prove several basic properties of pseudorandom states, which show the utility of our definition. First, we show a cryptographic no-cloning theorem: no efficient quantum algorithm can create additional copies of a pseudorandom state, when given polynomially-many copies as input. Second, as expected for random quantum states, we show that pseudorandom quantum states are highly entangled on average. Finally, as a main application, we prove that any family of pseudorandom states naturally gives rise to a private-key quantum money scheme.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_5"}, {"primary_key": "3385074", "vector": [], "sparse_vector": [], "title": "IND-CCA-Secure Key Encapsulation Mechanism in the Quantum Random Oracle Model, Revisited.", "authors": ["<PERSON><PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the gradual progress of NIST’s post-quantum cryptography standardization, the Round-1 KEM proposals have been posted for public to discuss and evaluate. Among the IND-CCA-secure KEM constructions, mostly, an IND-CPA-secure (or OW-CPA-secure) public-key encryption (PKE) scheme is first introduced, then some generic transformations are applied to it. All these generic transformations are constructed in the random oracle model (ROM). To fully assess the post-quantum security, security analysis in the quantum random oracle model (QROM) is preferred. However, current works either lacked a QROM security proof or just followed <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s proof technique (TCC-B 2016) and modified the original transformations by adding an additional hash to the ciphertext to achieve the QROM security. In this paper, by using a novel proof technique, we present QROM security reductions for two widely used generic transformations without suffering any ciphertext overhead. Meanwhile, the security bounds are much tighter than the ones derived by utilizing <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s proof technique. Thus, our QROM security proofs not only provide a solid post-quantum security guarantee for NIST Round-1 KEM schemes, but also simplify the constructions and reduce the ciphertext sizes. We also provide QROM security reductions for Hofheinz-Hövelmanns-Kiltz modular transformations (TCC 2017), which can help to obtain a variety of combined transformations with different requirements and properties.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_4"}, {"primary_key": "3385075", "vector": [], "sparse_vector": [], "title": "Structured Encryption and Leakage Suppression.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Structured encryption (STE) schemes encrypt data structures in such a way that they can be privately queried. One aspect of STE that is still poorly understood is its leakage. In this work, we describe a general framework to design STE schemes that do not leak the query/search pattern (i.e., if and when a query was previously made). Our framework consists of two compilers. The first can be used to make any dynamic STE schemerebuildablein the sense that the encrypted structures it produces can be rebuilt efficiently using onlyO(1) client storage. The second transforms any rebuildable scheme that leaks the query/search pattern into a new scheme that does not. Our second compiler is a generalization of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s square root oblivious RAM (ORAM) solution but does not make use of black-box ORAM simulation. We show that our framework produces STE schemes with query complexity that is asymptotically better than ORAM simulation in certain (natural) settings and comparable to special-purpose oblivious data structures. We use our framework to design a new STE scheme that is “almost” zero-leakage in the sense that it reveals an, intuitively-speaking, small amount of information. We also show how the scheme can be used to achieve zero-leakage queries when one can tolerate a probabilistic guarantee of correctness. This construction results from applying our compilers to a new STE scheme we design called thepiggyback scheme. This scheme is a general-purpose STE construction (in the sense that it can encrypt any data structure) that leaks the search/query pattern but hides the response length on non-repeating queries.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_12"}, {"primary_key": "3385076", "vector": [], "sparse_vector": [], "title": "Optimizing Authenticated Garbling for Faster Secure Two-Party Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON> et al. (CCS 2017) recently proposed a protocol for malicious secure two-party computation that represents the state-of-the-art with regard to concrete efficiency in both the single-execution and amortized settings, with or without preprocessing. We show here several optimizations of their protocol that result in a significant improvement in the overall communication and running time. Specifically: We show how to make the “authenticated garbling” at the heart of their protocol compatible with the half-gate optimization of <PERSON><PERSON><PERSON> et al. (Eurocrypt 2015). We also show how to avoid sending an information-theoretic MAC for each garbled row. These two optimizations give up to a 2.6\\(\\times \\)improvement in communication, and make the communication of the online phase essentially equivalent to that of state-of-the-artsemi-honestsecure computation. We show various optimizations to their protocol for generating AND triples that, overall, result in a 1.5\\(\\times \\)improvement in the communication and a 2\\(\\times \\)improvement in the computation for that step.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_13"}, {"primary_key": "3385077", "vector": [], "sparse_vector": [], "title": "Non-Malleable Codes for Partial Functions with Manipulation Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-malleable codes were introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (ICS ’10) and its main application is the protection of cryptographic devices against tampering attacks on memory. In this work, we initiate a comprehensive study on non-malleable codes for the class of partial functions, that read/write on an arbitrary subset of codeword bits with specific cardinality. Our constructions are efficient in terms of information rate, while allowing the attacker to access asymptotically almost the entire codeword. In addition, they satisfy a notion which is stronger than non-malleability, that we call non-malleability with manipulation detection, guaranteeing that any modified codeword decodes to either the original message or to\\(\\bot \\). Finally, our primitive implies All-Or-Nothing Transforms (AONTs) and as a result our constructions yield efficient AONTs under standard assumptions (only one-way functions), which, to the best of our knowledge, was an open question until now. In addition to this, we present a number of additional applications of our primitive in tamper resilience.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_20"}, {"primary_key": "3385078", "vector": [], "sparse_vector": [], "title": "Multi-Theorem Preprocessing NIZKs from Lattices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Non-interactive zero-knowledge (NIZK) proofs are fundamental to modern cryptography. Numerous NIZK constructions are known in both the random oracle and the common reference string (CRS) models. In the CRS model, there exist constructions from several classes of cryptographic assumptions such as trapdoor permutations, pairings, and indistinguishability obfuscation. Notably absent from this list, however, are constructions from standardlatticeassumptions. While there has been partial progress in realizing NIZKs from lattices for specific languages, constructing NIZK proofs (and arguments) for all of\\(\\mathsf {NP}\\)from standard lattice assumptions remains open. In this work, we make progress on this problem by giving the first construction of amulti-theoremNIZK argument for\\(\\mathsf {NP}\\)from standard lattice assumptions in thepreprocessingmodel. In the preprocessing model, a (trusted) setup algorithm generates proving and verification keys. The proving key is needed to construct proofs and the verification key is needed to check proofs. In the multi-theorem setting, the proving and verification keys should be reusable for an unbounded number of theorems without compromising soundness or zero-knowledge. Existing constructions of NIZKs in the preprocessing model (or even the designated-verifier model) that rely on weaker assumptions like one-way functions or oblivious transfer are only secure in a single-theorem setting. Thus, constructing multi-theorem NIZKs in the preprocessing model does not seem to be inherently easier than constructing them in the CRS model. We begin by constructing a multi-theorem preprocessing NIZK directly from context-hiding homomorphic signatures. Then, we show how to efficiently implement the preprocessing step using a new cryptographic primitive calledblind homomorphic signatures. This primitive may be of independent interest. Finally, we show how to leverage our new lattice-based preprocessing NIZKs to obtain new malicious-secure MPC protocols purely from standard lattice assumptions.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_25"}, {"primary_key": "3385079", "vector": [], "sparse_vector": [], "title": "On Distributional Collision Resistant Hashing.", "authors": ["<PERSON><PERSON>", "E<PERSON>"], "summary": "Collision resistant hashing is a fundamental concept that is the basis for many of the important cryptographic primitives and protocols. Collision resistant hashing is a family of compressing functions such that no efficient adversary can findanycollision given a random function in the family. In this work we study a relaxation of collision resistance calleddistributionalcollision resistance, introduced by <PERSON><PERSON> and <PERSON><PERSON> (STOC ’06). This relaxation of collision resistance only guarantees that no efficient adversary, given a random function in the family, cansamplea pair (x,y) wherexis uniformly random andyis uniformly random conditioned on colliding withx. Our first result shows that distributional collision resistance can be based on the existence ofmulti-collision resistance hash (with no additional assumptions). Multi-collision resistance is another relaxation of collision resistance which guarantees that an efficient adversary cannot find any tuple of\\(k>2\\)inputs that collide relative to a random function in the family. The construction is non-explicit, non-black-box, and yields an infinitely-often secure family. This partially resolves a question of <PERSON> et al. (EUROCRYPT ’18). We further observe that in a black-box model such an implication (from multi-collision resistance to distributional collision resistance) does not exist. Our second result is a construction of a distributional collision resistant hash from the average-case hardness of SZK. Previously, this assumption was not known to imply any form of collision resistance (other than the ones implied by one-way functions).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_11"}, {"primary_key": "3385080", "vector": [], "sparse_vector": [], "title": "Hardness of Non-interactive Differential Privacy from One-Way Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A central challenge in differential privacy is to design computationally efficient non-interactive algorithms that can answer large numbers ofstatistical querieson a sensitive dataset. That is, we would like to design a differentially private algorithm that takes a dataset\\(D \\in X^n\\)consisting of some small number of elementsnfrom some large data universeX, and efficiently outputs a summary that allows a user to efficiently obtain an answer to any query in some large familyQ. Ignoring computational constraints, this problem can be solved even whenXandQare exponentially large andnis just a small polynomial; however, all algorithms with remotely similar guarantees run in exponential time. There have been several results showing that, under the strong assumption of indistinguishability obfuscation, no efficient differentially private algorithm exists whenXandQcan be exponentially large. However, there are no strong separations between information-theoretic and computationally efficient differentially private algorithms under any standard complexity assumption. In this work we show that, if one-way functions exist, there is no general purpose differentially private algorithm that works whenXandQare exponentially large, andnis an arbitrary polynomial. In fact, we show that this result holds even ifXis just subexponentially large (assuming only polynomially-hard one-way functions). This result solves an open problem posed by <PERSON><PERSON><PERSON> in his recent survey [52].", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_15"}, {"primary_key": "3385081", "vector": [], "sparse_vector": [], "title": "Yes, There is an Oblivious RAM Lower Bound!", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An Oblivious RAM (ORAM) introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [JACM’96] is a (possibly randomized) RAM, for which the memory access pattern reveals no information about the operations performed. The main performance metric of an ORAM is the bandwidth overhead, i.e., the multiplicative factor extra memory blocks that must be accessed to hide the operation sequence. In their seminal paper introducing the ORAM, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> proved an amortized\\(\\varOmega (\\lg n)\\)bandwidth overhead lower bound for ORAMs with memory sizen. Their lower bound is very strong in the sense that it applies to the “offline” setting in which the ORAM knows the entire sequence of operations ahead of time. However, as pointed out by <PERSON> and <PERSON><PERSON> [ITCS’16] in the paper “Is there an oblivious RAM lower bound?”, there are two caveats with the lower bound of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>: (1) it only applies to “balls in bins” algorithms, i.e., algorithms where the ORAM may only shuffle blocks around and not apply any sophisticated encoding of the data, and (2), it only applies to statistically secure constructions. <PERSON> and <PERSON><PERSON> showed that removing the “balls in bins” assumption would result in super linear lower bounds for sorting circuits, a long standing open problem in circuit complexity. As a way to circumventing this barrier, they also proposed a notion of an “online” ORAM, which is an ORAM that remains secure even if the operations arrive in an online manner. They argued that most known ORAM constructions work in the online setting as well. Our contribution is an\\(\\varOmega (\\lg n)\\)lower bound on the bandwidth overhead of any online ORAM, even if we require only computational security and allow arbitrary representations of data, thus greatly strengthening the lower bound of Goldreich and Ostrovsky in the online setting. Our lower bound applies to ORAMs with memory sizenand any word size\\(r \\ge 1\\). The bound therefore asymptotically matches the known upper bounds when\\(r = \\varOmega (\\lg ^2 n)\\).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_18"}, {"primary_key": "3385082", "vector": [], "sparse_vector": [], "title": "Generic Attacks Against Beyond-Birthday-Bound MACs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we study the security of several recent MAC constructions with provable security beyond the birthday bound. We consider block-cipher based constructions with a double-block internal state, such asSUM-ECBC,PMAC+,3kf9,GCM-SIV2, and some variants (LightMAC+,1kPMAC+). All these MACs have a security proof up to\\(2^{2n/3}\\)queries, but there are no known attacks with less than\\(2^{n}\\)queries. We describe a new cryptanalysis technique for double-block MACs based on finding quadruples of messages with four pairwise collisions in halves of the state. We show how to detect such quadruples inSUM-ECBC,PMAC+,3kf9,GCM-SIV2and their variants with\\(\\mathcal {O}(2^{3n/4})\\)queries, and how to build a forgery attack with the same query complexity. The time complexity of these attacks is above\\(2^n\\), but it shows that the schemes do not reach full security in the information theoretic model. Surprisingly, our attack onLightMAC+also invalidates a recent security proof by <PERSON><PERSON>. Moreover, we give a variant of the attack againstSUM-ECBCandGCM-SIV2with time and data complexity\\(\\tilde{\\mathcal {O}}(2^{6n/7})\\). As far as we know, this is the first attack with complexity below\\(2^n\\)against a deterministic beyond-birthday-bound secure MAC. As a side result, we also give a birthday attack against1kf9, a single-key variant of3kf9that was withdrawn due to issues with the proof.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_11"}, {"primary_key": "3385083", "vector": [], "sparse_vector": [], "title": "Lattice-Based Zero-Knowledge Arguments for Integer Relations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "San Ling", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We provide lattice-based protocols allowing to prove relations among committed integers. While the most general zero-knowledge proof techniques can handle arithmetic circuits in the lattice setting, adapting them to prove statements over the integers is non-trivial, at least if we want to handle exponentially large integers while working with a polynomial-size modulusq. For a polynomialL, we provide zero-knowledge arguments allowing a prover to convince a verifier that committedL-bit bitstringsx,yandzare the binary representations of integersX,YandZsatisfying\\(Z=X+Y\\)over\\(\\mathbb {Z}\\). The complexity of our arguments is only linear inL. Using them, we construct arguments allowing to prove inequalities\\(X<Z\\)among committed integers, as well as arguments showing that a committedXbelongs to a public interval\\([\\alpha ,\\beta ]\\), where\\(\\alpha \\)and\\(\\beta \\)can be arbitrarily large. Our range arguments have logarithmic cost (i.e., linear inL) in the maximal range magnitude. Using these tools, we obtain zero-knowledge arguments showing that a committed elementXdoesnotbelong to a public setSusing\\(\\widetilde{\\mathcal {O}}(n \\cdot \\log |S|)\\)bits of communication, wherenis the security parameter. We finally give a protocol allowing to argue that committedL-bit integersX,YandZsatisfy multiplicative relations\\(Z=XY\\)over the integers, with communication cost subquadratic inL. To this end, we use our protocol for integer addition to prove the correct recursive execution of Karatsuba’s multiplication algorithm. The security of our protocols relies on standard lattice assumptions with polynomial modulus and polynomial approximation factor.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_24"}, {"primary_key": "3385084", "vector": [], "sparse_vector": [], "title": "<PERSON> on WCS is Tight - Repairing Luykx-Preneel Optimal Forgeries.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In Eurocrypt 2018, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> described hash-key-recovery and forgery attacks against polynomial hash based <PERSON><PERSON><PERSON> (WCS) authenticators. Their attacks require\\(2^{n/2}\\)message-tag pairs and recover hash-key with probability about\\(1.34\\, \\times \\, 2^{-n}\\)wherenis the bit-size of the hash-key. <PERSON> in Eurocrypt 2005 had provided an upper bound (known as Bernstein bound) of the maximum forgery advantages. The bound says that all adversaries making\\(O(2^{n/2})\\)queries of WCS can have maximum forgery advantage\\(O(2^{-n})\\). So, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> essentially analyze WCS in a range of query complexities where WCS is known to be perfectly secure. Here we revisit the bound and found that WCS remains secure against all adversaries making\\(q \\ll \\sqrt{n} \\times 2^{n/2}\\)queries. So it would be meaningful to analyze adversaries with beyond birthday bound complexities. In this paper, we show thatthe Bernstein bound is tightby describing two attacks (one in the“chosen-plaintext model”and other in the“known-plaintext model”) whichrecover the hash-key (hence forges) with probability at leastbased on\\(\\sqrt{n} \\times 2^{n/2}\\)message-tag pairs. We also extend the forgery adversary to the Galois Counter Mode (or GCM). More precisely, werecover the hash-key of GCM with probability at least\\(\\frac{1}{2}\\)based on only\\(\\sqrt{\\frac{n}{\\ell }} \\times 2^{n/2}\\)encryption queries, where\\(\\ell \\)is the number of blocks present in encryption queries.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_8"}, {"primary_key": "3385085", "vector": [], "sparse_vector": [], "title": "Continuously Non-Malleable Codes in the Split-State Model from Minimal Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "At ICS 2010, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced the notion ofnon-malleable codes, a weaker form of error-correcting codes guaranteeing that the decoding of a tampered codeword either corresponds to the original message or to an unrelated value. The last few years established non-malleable codes as one of the recently invented cryptographic primitives with the highest impact and potential, with very challenging open problems and applications. In this work, we focus on so-calledcontinuouslynon-malleable codes in the split-state model, as proposed by <PERSON><PERSON> al.(TCC 2014), where a codeword is made of two shares and an adaptive adversary makes a polynomial number of attempts in order to tamper the target codeword, where each attempt is allowed to modify the two shares independently (yet arbitrarily). Achieving continuous non-malleability in the split-state model has been so far very hard. Indeed, the only known constructions require strong setup assumptions (i.e., the existence of a common reference string) and strong complexity-theoretic assumptions (i.e., the existence of non-interactive zero-knowledge proofs and collision-resistant hash functions). As our main result, we construct a continuously non-malleable code in the split-state model without setup assumptions, requiring only one-to-one one-way functions (i.e., essentially optimal computational assumptions). Our result introduces several new ideas that make progress towards understanding continuous non-malleability, and shows interesting connections with protocol-design and proof-approach techniques used in other contexts (e.g., look-ahead simulation in zero-knowledge proofs, non-malleable commitments, and leakage resilience).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_21"}, {"primary_key": "3385086", "vector": [], "sparse_vector": [], "title": "On the Exact Round Complexity of Secure Three-Party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We settle the exact round complexity of three-party computation (3PC) in honest-majority setting, for a range of security notions such as selective abort, unanimous abort, fairness and guaranteed output delivery. Selective abort security, the weakest in the lot, allows the corrupt parties to selectively deprive some of the honest parties of the output. In the mildly stronger version of unanimous abort, either all or none of the honest parties receive the output. Fairness implies that the corrupted parties receive their output only if all honest parties receive output and lastly, the strongest notion of guaranteed output delivery implies that the corrupted parties cannot prevent honest parties from receiving their output. It is a folklore that the implication holds from the guaranteed output delivery to fairness to unanimous abort to selective abort. We focus on two network settings– pairwise-private channels without and with a broadcast channel. In the minimal setting of pairwise-private channels, 3PC with selective abort is known to be feasible in just two rounds, while guaranteed output delivery is infeasible to achieve irrespective of the number of rounds. Settling the quest for exact round complexity of 3PC in this setting, we show that three rounds are necessary and sufficient for unanimous abort and fairness. Extending our study to the setting with an additional broadcast channel, we show that while unanimous abort is achievable in just two rounds, three rounds are necessary and sufficient for fairness and guaranteed output delivery. Our lower bound results extend for any number of parties in honest majority setting and imply tightness of several known constructions. The fundamental concept of garbled circuits underlies all our upper bounds. Concretely, our constructions involve transmitting and evaluating only constant number of garbled circuits. Assumption-wise, our constructions rely on injective (one-to-one) one-way functions.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_15"}, {"primary_key": "3385087", "vector": [], "sparse_vector": [], "title": "Quantum Attacks Against Indistinguishablility Obfuscators Proved Secure in the Weak Multilinear Map Model.", "authors": ["<PERSON>-<PERSON>"], "summary": "We present a quantum polynomial time attack against the GMMSSZ branching program obfuscator of <PERSON><PERSON><PERSON> et al. (TCC’16), when instantiated with the GGH13 multilinear map of <PERSON><PERSON><PERSON> et al. (EUROCRYPT’13). This candidate obfuscator was proved secure in the weak multilinear map model introduced by <PERSON> et al. (CRYPTO’16). Our attack uses the short principal ideal solver of <PERSON><PERSON><PERSON> et al. (EUROCRYPT’16), to recover a secret element of the GGH13 multilinear map in quantum polynomial time. We then use this secret element to mount a (classical) polynomial time mixed-input attack against the GMMSSZ obfuscator. The main result of this article can hence be seen as a classical reduction from the security of the GMMSSZ obfuscator to the short principal ideal problem (the quantum setting is then only used to solve this problem in polynomial time). As an additional contribution, we explain how the same ideas can be adapted to mount a quantum polynomial time attack against the DGGMM obfuscator of <PERSON><PERSON> et al. (ePrint 2016), which was also proved secure in the weak multilinear map model.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96878-0_6"}, {"primary_key": "3385088", "vector": [], "sparse_vector": [], "title": "Towards Bidirectional Ratcheted Key Exchange.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Ratcheted key exchange (RKE) is a cryptographic technique used in instant messaging systems like Signal and the WhatsApp messenger for attaining strong security in the face of state exposure attacks. RKE received academic attention in the recent works of <PERSON><PERSON><PERSON> et al. (EuroS&P 2017) and <PERSON><PERSON> et al. (CRYPTO 2017). While the former is analytical in the sense that it aims primarily at assessing the security that one particular protocoldoesachieve (which might be weaker than the notion that itshouldachieve), the authors of the latter develop and instantiate a notion of security from scratch, independently of existing implementations. Unfortunately, however, their model is quite restricted, e.g. for considering only unidirectional communication and the exposure of only one of the two parties. In this article we resolve the limitations of prior work by developing alternative security definitions, for unidirectional RKE as well as for RKE where both parties contribute. We follow a purist approach, aiming at finding strong yet convincing notions that cover a realistic communication model with fully concurrent operation of both participants. We further propose secure instantiations (as the protocols analyzed or proposed by <PERSON><PERSON><PERSON> et al. and <PERSON><PERSON> et al. turn out to be weak in our models). While our scheme for the unidirectional case builds on a generic KEM as the main building block (differently to prior work that requires explicitly <PERSON><PERSON><PERSON><PERSON>), our schemes for bidirectional RKE require a stronger, HIBE-like component.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_1"}, {"primary_key": "3385089", "vector": [], "sparse_vector": [], "title": "CAPA: The Spirit of Beaver Against Physical Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we introduce two things: On one hand we introduce the Tile-Probe-and-Fault model, a model generalising the wire-probe model of <PERSON><PERSON> et al. extending it to cover both more realistic side-channel leakage scenarios on a chip and also to cover fault and combined attacks. Secondly we introduce CAPA: a combined Countermeasure Against Physical Attacks. Our countermeasure is motivated by our model, and aims to provide security against higher-order SCA, multiple-shot FA and combined attacks. The tile-probe-and-fault model leads one to naturally look (by analogy) at actively secure multi-party computation protocols. Indeed, CAPA draws much inspiration from the MPC protocol SPDZ. So as to demonstrate that the model, and the CAPA countermeasure, are not just theoretical constructions, but could also serve to build practical countermeasures, we present initial experiments of proof-of-concept designs using the CAPA methodology. Namely, a hardware implementation of the KATAN and AES block ciphers, as well as a software bitsliced AES S-box implementation. We demonstrate experimentally that the design can resist second-order DPA attacks, even when the attacker is presented with many hundreds of thousands of traces. In addition our proof-of-concept can also detect faults within our model with high probability in accordance to the methodology.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_5"}, {"primary_key": "3385090", "vector": [], "sparse_vector": [], "title": "Simplifying Game-Based Definitions - Indistinguishability up to Correctness and Its Application to Stateful AE.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Often the simplest way of specifying game-based cryptographic definitions is apparently barred because the adversary would have some trivial win. Disallowing or invalidating these wins can lead to complex or unconvincing definitions. We suggest a generic way around this difficulty. We call itindistinguishability up to correctness, or IND\\(\\vert \\)C. Given games\\({{\\text {G}}}\\)and\\({{\\text {H}}}\\)and a correctness condition\\({{\\text {C}}}\\)we define an advantage measure\\({\\mathbf {Adv}_{{{\\text {G}}},{{\\text {H}}},{{\\text {C}}}}^{{\\text {indc}}}}\\)wherein\\({{{\\text {G}}}}\\)/\\({{{\\text {H}}}}\\)distinguishing attacks are effaced to the extent that they are inevitable due to\\({{\\text {C}}}\\). We formalize this in the language oforacle silencing, an alternative to exclusion-style and penalty-style definitions. We apply our ideas to a domain where game-based definitions have been cumbersome: stateful authenticated-encryption (sAE). We rework existing sAE notions and encompass new ones, like replay-free AE permitting a specified degree of out-of-order message delivery.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_1"}, {"primary_key": "3385091", "vector": [], "sparse_vector": [], "title": "Out-of-Band Authentication in Group Messaging: Computational, Statistical, Optimal.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Extensive efforts are currently put into securing messaging platforms, where a key challenge is that of protecting against man-in-the-middle attacks when setting up secure end-to-end channels. The vast majority of these efforts, however, have so far focused on securinguser-to-usermessaging, and recent attacks indicate that the security ofgroupmessaging is still quite fragile. We initiate the study of out-of-band authentication in the group setting, extending the user-to-user setting where messaging platforms (e.g., Telegram and WhatsApp) protect against man-in-the-middle attacks by assuming that users have access to an external channel for authenticating one short value (e.g., two users who recognize each other’s voice can compare a short value). Inspired by the frameworks of Vaudenay (CRYPTO ’05) and <PERSON><PERSON> et al. (CRYPTO ’06) in the user-to-user setting, we assume that users communicate over a completely-insecure channel, and that a group administrator can out-of-band authenticate one short message to all users. An adversary may read, remove, or delay this message (for all or for some of the users), but cannot undetectably modify it. Within our framework we establish tight bounds on the tradeoff between the adversary’s success probability and the length of the out-of-band authenticated message (which is a crucial bottleneck given that the out-of-band channel is of low bandwidth). We consider both computationally-secure and statistically-secure protocols, and for each flavor of security we construct an authentication protocol and prove a lower bound showing that our protocol achieves essentially the best possible tradeoff. In particular, considering groups that consist of an administrator andkadditional users, for statistically-secure protocols we show that at least\\((k+1)\\cdot (\\log (1/\\epsilon ) - \\varTheta (1))\\)bits must be out-of-band authenticated, whereas for computationally-secure ones\\(\\log (1/\\epsilon ) + \\log k\\)bits suffice, where\\(\\epsilon \\)is the adversary’s success probability. Moreover, instantiating our computationally-secure protocol in the random-oracle model yields an efficient and practically-relevant protocol (which, alternatively, can also be based on any one-way function in the standard model).", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_3"}, {"primary_key": "3385092", "vector": [], "sparse_vector": [], "title": "Correcting Subverted Random Oracles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The random oracle methodology has proven to be a powerful tool for designing and reasoning about cryptographic schemes, and can often act as an effective bridge between theory and practice. In this paper, we focus on the basic problem of correcting faulty—or adversarially corrupted—random oracles, so that they can be confidently applied for such cryptographic purposes. We prove that a simple construction can transform a “subverted” random oracle—which disagrees with the original one at a negligible fraction of inputs—into a construction that isindifferentiablefrom a random function. Our results permit future designers of cryptographic primitives in typical kleptographic settings (i.e., with adversaries who may subvert the implementation of cryptographic algorithms but undetectable via blackbox testing) to use random oracles as a trusted black box, in spite of not trusting the implementation. Our analysis relies on a general rejection re-sampling lemma which is a tool of possible independent interest.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_9"}, {"primary_key": "3385093", "vector": [], "sparse_vector": [], "title": "Fast Correlation Attack Revisited - Cryptanalysis on Full Grain-128a, Grain-128, and Grain-v1.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A fast correlation attack (FCA) is a well-known cryptanalysis technique for LFSR-based stream ciphers. The correlation between the initial state of an LFSR and corresponding key stream is exploited, and the goal is to recover the initial state of the LFSR. In this paper, we revisit the FCA from a new point of view based on a finite field, and it brings a new property for the FCA when there are multiple linear approximations. Moreover, we propose a novel algorithm based on the new property, which enables us to reduce both time and data complexities. We finally apply this technique to the Grain family, which is a well-analyzed class of stream ciphers. There are three stream ciphers, Grain-128a, Grain-128, and Grain-v1 in the Grain family, and Grain-v1 is in the eSTREAM portfolio and Grain-128a is standardized by ISO/IEC. As a result, we break them all, and especially for Grain-128a, the cryptanalysis on its full version is reported for the first time.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96881-0_5"}, {"primary_key": "3385094", "vector": [], "sparse_vector": [], "title": "Improved Division Property Based Cube Attacks Exploiting Algebraic Properties of Superpoly.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The cube attack is an important technique for the cryptanalysis of symmetric key primitives, especially for stream ciphers. Aiming at recovering some secret key bits, the adversary reconstructs a superpoly with the secret key bits involved, by summing over a set of the plaintexts/IV which is called a cube. Traditional cube attack only exploits linear/quadratic superpolies. Moreover, for a long time after its proposal, the size of the cubes has been largely confined to an experimental range, e.g., typically 40. These limits were first overcome by the division property based cube attacks proposed by <PERSON><PERSON> et al. at CRYPTO 2017. Based on MILP modelled division property, for a cube (index set)I, they identify the small (index) subsetJof the secret key bits involved in the resultant superpoly. During the precomputation phase which dominates the complexity of the cube attacks,\\(2^{|I|+|J|}\\)encryptions are required to recover the superpoly. Therefore, their attacks can only be available when the restriction\\(|I|+|J|<n\\)is met. In this paper, we introduced several techniques to improve the division property based cube attacks by exploiting various algebraic properties of the superpoly. We propose the “flag” technique to enhance the preciseness of MILP models so that the proper non-cube IV assignments can be identified to obtain a non-constant superpoly. A degree evaluation algorithm is presented to upper bound the degree of the superpoly. With the knowledge of its degree, the superpoly can be recovered without constructing its whole truth table. This enables us to explore larger cubesI’s even if\\(|I|+|J|\\ge n\\). We provide a term enumeration algorithm for finding the monomials of the superpoly, so that the complexity of many attacks can be further reduced. As an illustration, we apply our techniques to attack the initialization of several ciphers. To be specific, our key recovery attacks have mounted to 839-roundTrivium, 891-round Kreyvium, 184-round Grain-128a and 750-roundAcornrespectively.", "published": "2018-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-96884-1_10"}]