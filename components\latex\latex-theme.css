/* 编辑器基础样式 */
.latex-editor {
  position: relative;
  min-height: 300px;
  overflow: hidden;
}

.latex-editor textarea {
  font-family: 'geist-mono', 'Fira Mono', 'JetBrains Mono', monospace;
  font-size: 1rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
}

.latex-editor textarea::selection {
  background-color: rgba(100, 100, 255, 0.3);
}

.highlight-layer {
  font-family: 'geist-mono', 'Fira Mono', 'JetBrains Mono', monospace;
  font-size: 1rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 亮色主题语法高亮 */
.highlight-layer[data-theme="light"] .token.comment {
  color: #6a737d;
}

.highlight-layer[data-theme="light"] .token.punctuation {
  color: #008000;
}

.highlight-layer[data-theme="light"] .token.operator {
  color: #005cc5;
}

.highlight-layer[data-theme="light"] .token.keyword {
  color: #d73a49;
}

.highlight-layer[data-theme="light"] .token.function {
  color: #6f42c1;
}

.highlight-layer[data-theme="light"] .token.string {
  color: #032f62;
}

.highlight-layer[data-theme="light"] .token.number {
  color: #005cc5;
}

/* 暗色主题语法高亮 */
.highlight-layer[data-theme="dark"] .token.comment {
  color: #8b949e;
}

.highlight-layer[data-theme="dark"] .token.punctuation {
  color: #00ff00;
}

.highlight-layer[data-theme="dark"] .token.operator {
  color: #79c0ff;
}

.highlight-layer[data-theme="dark"] .token.keyword {
  color: #ff7b72;
}

.highlight-layer[data-theme="dark"] .token.function {
  color: #d2a8ff;
}

.highlight-layer[data-theme="dark"] .token.string {
  color: #a5d6ff;
}

.highlight-layer[data-theme="dark"] .token.number {
  color: #79c0ff;
}

/* LaTeX特定语法高亮 */
.highlight-layer .token.latex-environment {
  color: #ff6bff;
}

.highlight-layer .token.latex-command {
  color: #005cc5; /* Blue */
  font-weight: 500;
}

.highlight-layer[data-theme="dark"] .token.latex-command {
  color: #005cc5; /* Blue */
  font-weight: 500;
}

.highlight-layer .token.latex-math {
  color: #ff6bff;
}

.highlight-layer[data-theme="dark"] .token.latex-math {
  color: #ff9cff;
}