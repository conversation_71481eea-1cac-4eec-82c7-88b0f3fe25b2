[{"primary_key": "1135161", "vector": [], "sparse_vector": [], "title": "Detect, Pack and Batch: Perfectly-Secure MPC with Linear Communication and Constant Expected Time.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that perfectly-secure optimally-resilient secure Multi-Party Computation (MPC) for a circuit withCgates and depthDcan be obtained in\\({\\mathcal {O}}((Cn+n^4 + Dn^2)\\log n)\\)communication complexity and\\({\\mathcal {O}}(D)\\)expected time. For\\(D \\ll n\\)and\\(C\\ge n^3\\), this is thefirstperfectly-secure optimal-resilient MPC protocol withlinearcommunication complexity per gate andconstantexpected time complexity per layer. Compared to state-of-the-art MPC protocols in the player elimination framework [<PERSON><PERSON><PERSON> and Hirt TCC’08, and <PERSON><PERSON>, <PERSON>, and <PERSON> CRYPTO’19], for\\(C>n^3\\)and\\(D \\ll n\\), our results significantly improve the run time from\\(\\varTheta (n+D)\\)to expected\\({\\mathcal {O}}(D)\\)while keeping communication complexity at\\({\\mathcal {O}}(Cn\\log n)\\). Compared to state-of-the-art MPC protocols that obtain an expected\\({\\mathcal {O}}(D)\\)time complexity [<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>21], for\\(C>n^3\\), our results significantly improve the communication complexity from\\({\\mathcal {O}}(Cn^4\\log n)\\)to\\({\\mathcal {O}}(Cn\\log n)\\)while keeping the expected run time at\\({\\mathcal {O}}(D)\\). One salient part of our technical contribution is centered around a new primitive we calldetectable secret sharing. It is perfectly-hiding, weakly-binding, and has the property that either reconstruction succeeds, or\\({\\mathcal {O}}(n)\\)parties are (privately) detected. On the one hand, we show that detectable secret sharing is sufficiently powerful to generate multiplication triplets needed for MPC. On the other hand, we show how to sharepsecrets via detectable secret sharing with communication complexity of just\\({\\mathcal {O}}(n^4\\log n+p \\log n)\\). When sharing\\(p\\ge n^4\\)secrets, the communication cost is amortized to just\\({\\mathcal {O}}(1)\\)per secret. Our second technical contribution is a new Verifiable Secret Sharing protocol that can sharepsecrets at just\\({\\mathcal {O}}(n^4\\log n+pn\\log n)\\)word complexity. When sharing\\(p\\ge n^3\\)secrets, the communication cost is amortized to just\\({\\mathcal {O}}(n)\\)per secret. The best prior required\\({\\mathcal {O}}(n^3)\\)communication per secret.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_9"}, {"primary_key": "1135162", "vector": [], "sparse_vector": [], "title": "An Incremental PoSW for General Weight Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A proof of sequential work (PoSW) scheme allows the prover to convince a verifier that it computed a certain number of computational steps sequentially. Very recently, graph-labeling PoSW schemes, found applications in light-client blockchain protocols, most notably bootstrapping. A bootstrapping protocol allows a light client, with minimal information about the blockchain, to hold a commitment to its stable prefix. An incremental PoSW (iPoSW) scheme allows the prover to non-trivially increment proofs: given\\(\\chi ,\\pi _1\\)and integers\\(N_1,N_2\\)such that\\(\\pi _1\\)is a valid proof for\\(N_1\\), it generates a valid proof\\(\\pi \\)for\\(N_1+N_2\\). In this work, we construct an iPoSW scheme based on the skiplist-based PoSW scheme of <PERSON><PERSON><PERSON> et al. and prove its security in the random oracle model by employing the powerful on-the-fly sampling technique of <PERSON><PERSON> et al. Moreover, unlike the iPoSW scheme of <PERSON><PERSON> et al., ours is the first iPoSW scheme which is suitable for constructing incremental non-interactive arguments of chain knowledge (SNACK) schemes, which are at the heart of space and time efficient blockchain light-client protocols. In particular, our scheme works for general weight distributions, which we characterize as incrementally sampleable distributions. Our general treatment recovers the distribution underlying the scheme of <PERSON><PERSON><PERSON><PERSON> et al. as well as the distribution underlying SNACK-enabled bootstrapping application as special cases. In realizing our general construction, we develop a new on-the-fly sampling technique.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_10"}, {"primary_key": "1135163", "vector": [], "sparse_vector": [], "title": "Fine-Grained Non-interactive Key-Exchange: Constructions and Lower Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we initiate a study ofK-NIKE protocols in thefine-grainedsetting, in which there is apolynomialgap between the running time of the honest parties and that of the adversary. Our goal is to show the possibility, or impossibility, of basing such protocols on weaker assumptions than those ofK-NIKE for\\(K \\ge 3\\). Our contribution is threefold. We show that random oracles can be used to obtain fine-grainedK-NIKE protocols foreveryconstantK. In particular, we show how to generalize <PERSON><PERSON><PERSON>’s two-party protocol toKparties in such a way that the honest parties asknqueries each, while the adversary needs\\(n^{K/(K-1)}\\)queries to the random oracle to find the key. We then improve the security by further using algebraic structures, while avoiding pairings. In particular, we show that there is a 4-party NIKE in <PERSON><PERSON><PERSON>’s generic group model with aquadraticgap between the number of queries by the honest parties vs. that of the adversary. Finally, we show a limitation of using purely algebraic methods for obtaining 3-NIKE. In particular, we show that anyn-query 3-NIKE protocol in <PERSON><PERSON>’s generic group model can be broken by a\\(O(n^2)\\)-query attacker. Maurer’s GGM is more limited compared with <PERSON><PERSON><PERSON>’s both for the parties and the adversary, as there are no explicit labels for the group elements. Despite being more limited, this model still captures the <PERSON><PERSON><PERSON> protocol. Prior to our work, it was open to break 3-NIKE protocols in Maurer’s model withanypolynomial number of queries.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_3"}, {"primary_key": "1135164", "vector": [], "sparse_vector": [], "title": "A New Framework for Quantum Oblivious Transfer.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new template for building oblivious transfer from quantum information that we call the “fixed basis” framework. Our framework departs from prior work (e.g., <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, FOCS’88) by fixing thecorrectchoice of measurement basis used by each player, except for some hiddentrapqubits that are intentionally measured in a conjugate basis. We instantiate this template in the quantum random oracle model (QROM) to obtain simple protocols that implement, with security against malicious adversaries: Non-interactiverandom-input bit OT in a model where parties share EPR pairs a priori. Two-round random-input bit OT without setup, obtained by showing that the protocol above remains secure even if the (potentially malicious) OT receiver sets up the EPR pairs. Three-round chosen-input string OT from BB84 states without entanglement or setup. This improves upon natural variations of the CK88 template that require at least five rounds. Along the way, we develop technical tools that may be of independent interest. We prove that natural functions like XOR enableseedlessrandomness extraction from certain quantum sources of entropy. We also use idealized (i.e. extractable and equivocal) bit commitments, which we obtain by proving security of simple and efficient constructions in the QROM.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_13"}, {"primary_key": "1135165", "vector": [], "sparse_vector": [], "title": "Public Key Encryption with Secure Key Leasing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce the notion of public key encryption with secure key leasing (PKE-SKL). Our notion supports the leasing of decryption keys so that a leased key achieves the decryption functionality but comes with the guarantee that if the quantum decryption key returned by a user passes a validity test, then the user has lost the ability to decrypt. Our notion is similar in spirit to the notion of secure software leasing (SSL) introduced by <PERSON><PERSON><PERSON> and <PERSON> (Eurocrypt 2021) but captures significantly more general adversarial strategies. (In more detail, our adversary is not restricted to use an honest evaluation algorithm to run pirated software.) Our results can be summarized as follows: Definitions:We introduce the definition of PKE with secure key leasing and formalize a security notion that we call indistinguishability against key leasing attacks (IND-KLA security). We also define a one-wayness notion for PKE-SKL that we call OW-KLA security and show that an OW-KLA secure PKE-SKL scheme can be lifted to an IND-KLA secure one by using the (quantum) Goldreich-Levin lemma. Constructing IND-KLA PKE with Secure Key Leasing:We provide a construction of OW-KLA secure PKE-SKL (which implies IND-KLA secure PKE-SKL as discussed above) by leveraging a PKE scheme that satisfies a new security notion that we callconsistent or inconsistent security against key leasing attacks (CoIC-KLA security). We then construct a CoIC-KLA secure PKE scheme using 1-key Ciphertext-Policy Functional Encryption (CPFE) that in turn can be based on any IND-CPA secure PKE scheme. Identity Based Encryption, Attribute Based Encryption and Functional Encryption with Secure Key Leasing:We provide definitions of secure key leasing in the context of advanced encryption schemes such as identity based encryption (IBE), attribute-based encryption (ABE) and functional encryption (FE). Then we provide constructions by combining the above PKE-SKL with standard IBE, ABE and FE schemes. Notably, our definitions allow the adversary to requestdistinguishingkeys in the security game, namely, keys that distinguish the challenge bit by simply decrypting the challenge ciphertext, as long as it returns them (and they pass the validity test) before it sees the challenge ciphertext. All our constructions satisfy this stronger definition, albeit with the restriction that only a bounded number of such keys is allowed to the adversary in the IBE and ABE (but not FE) security games. Prior to our work, the notion of single decryptor encryption (SDE) has been studied in the context of PKE (Georgiou and Zhandry, Eprint 2020) and FE (Kitigawa and Nishimaki, Asiacrypt 2022) but all their constructions rely on strong assumptions including indistinguishability obfuscation. In contrast, our constructions do not require any additional assumptions, showing that PKE/IBE/ABE/FE can be upgraded to support secure key leasing for free.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_20"}, {"primary_key": "1135166", "vector": [], "sparse_vector": [], "title": "Broadcast, Trace and Revoke with Optimal Parameters from Polynomial Hardness.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abroadcast, trace and revokesystem generalizes broadcast encryption as well as traitor tracing. In such a scheme, an encryptor can specify a list\\(L \\subseteq N\\)of revoked users so that (i) users inLcan no longer decrypt ciphertexts, (ii) ciphertext size is independent ofL, (iii) a pirate decryption box supports tracing of compromised users. The “holy grail” of this line of work is a construction which resists unbounded collusions, achieves all parameters (including public and secret key) sizes independent of |L| and |N|, and is based on polynomial hardness assumptions. In this work we make the following contributions: Public Trace Setting:We provide a construction which (i) achieves optimal parameters, (ii) supports embedding identities (from an exponential space) in user secret keys, (iii) relies on polynomial hardness assumptions, namely compact functional encryption (\\(\\textsf{FE}\\)) and a key-policy attribute based encryption (\\({\\textsf{ABE}}\\)) with special efficiency properties, and (iv) enjoys adaptive security with respect to the revocation list. The previous best known construction by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and Zhandry (Eurocrypt 2016) which achieved optimal parameters and embedded identities, relied on indistinguishability obfuscation, which is considered an inherently subexponential assumption and achieved only selective security with respect to the revocation list. Secret Trace Setting:We provide the first construction with optimal ciphertext, public and secret key sizes and embedded identities from any assumption outside Obfustopia. In detail, our construction relies on Lockable Obfuscation which can be constructed using\\(\\textsf{LWE}\\)(Goyal, Koppula, Waters and Wichs, Zirdelis, Focs 2017) and two\\({\\textsf{ABE}}\\)schemes: (i) the key-policy scheme with special efficiency properties by Boneh et al. (Eurocrypt 2014) and (ii) a ciphertext-policy\\({\\textsf{ABE}}\\)for\\(\\textsf{P}\\)which was recently constructed by Wee (Eurocrypt 2022) using a new assumption calledevasive and tensor\\(\\textsf{LWE}\\). This assumption, introduced to build an\\({\\textsf{ABE}}\\), is believed to be much weaker than lattice based assumptions underlying\\(\\textsf{FE}\\)or\\(\\textsf{iO}\\)– in particular it is required even for lattice based broadcast, without trace. Moreover, by relying on subexponential security of\\(\\textsf{LWE}\\), both our constructions can also support asuper-polynomialsized revocation list, so long as it allows efficient representation and membership testing. Ours is the first work to achieve this, to the best of our knowledge.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_20"}, {"primary_key": "1135167", "vector": [], "sparse_vector": [], "title": "Caveat Implementor! Key Recovery Attacks on MEGA.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "MEGA is a large-scale cloud storage and communication platform that aims to provide end-to-end encryption for stored data. A recent analysis by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (IEEE S &P 2023) invalidated these security claims by presenting practical attacks against MEGA that could be mounted by the MEGA service provider. In response, the MEGA developers added lightweight sanity checks on the user RSA private keys used in MEGA, sufficient to prevent the previous attacks. We analyse these new sanity checks and show how they themselves can be exploited to mount novel attacks on MEGA that recover a target user’s RSA private key with only slightly higher attack complexity than the original attacks. We identify the presence of an ECB encryption oracle under a target user’s master key in the MEGA system; this oracle provides our adversary with the ability to partially overwrite a target user’s RSA private key with chosen data, a powerful capability that we use in our attacks.\n We then present two distinct types of attack, each type exploiting different error conditions arising in the sanity checks and in subsequent cryptographic processing during MEGA ’s user authentication procedure. The first type appears to be novel and exploits the manner in which the MEGA code handles modular inversion when recomputing\\(u=q^{-1} \\bmod p\\). The second can be viewed as a small subgroup attack (<PERSON> and <PERSON>, EUROCRYPT 1996, <PERSON> and <PERSON>, CRYPTO 1998). We prototype the attacks and show that they work in practice. As a side contribution, we show how to improve the RSA key recovery attack of <PERSON>end<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> against the unpatched version of MEGA to require only 2 logins instead of the original 512. We conclude by discussing wider lessons about secure implementation of cryptography that our work surfaces.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_7"}, {"primary_key": "1135168", "vector": [], "sparse_vector": [], "title": "Actively Secure Arithmetic Computation and VOLE with Constant Computational Overhead.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of two-party secure arithmetic computation where the goal is to evaluate an arithmetic circuit over a finite field\\(\\mathbb {F}\\)in the presence of an active (aka malicious) adversary. In the passive setting, <PERSON><PERSON> et al. (Crypto 2017) constructed a protocol that only makes aconstant(amortized) number of field operations per gate. This protocol uses the underlying field\\(\\mathbb {F}\\)as a black box, makes black-box use of (standard) oblivious transfer, and its security is based on arithmetic analogs of well-studied cryptographic assumptions. We present an actively-secure variant of this protocol that achieves, for the first time, all the above features. The protocol relies on the same assumptions and adds only a minor overhead in computation and communication. Along the way, we construct a highly-efficient Vector Oblivious Linear Evaluation (VOLE) protocol and present several practical and theoretical optimizations, as well as a prototype implementation. Our most efficient variant can achieve an asymptotic rate of 1/4 (i.e., for vectors of lengthwwe send roughly 4welements of\\(\\mathbb {F}\\)), which is only slightly worse than the passively-secure protocol whose rate is 1/3. The protocol seems to be practically competitive over fast networks, even for relatively small fields\\(\\mathbb {F}\\)and relatively short vectors. Specifically, our VOLE protocol has 3 rounds, and even for 10K-long vectors, it has an amortized cost per entry of less than 4 OT’s and less than 300 arithmetic operations. Most of these operations (about 200) can be pre-processed locally in an offline non-interactive phase. (Better constants can be obtained for longer vectors.) Some of our optimizations rely on a novel intractability assumption regarding the non-malleability of noisy linear codes, that may be of independent interest. Our technical approach employs two new ingredients. First, we present a new information-theoretic construction of Conditional Disclosure of Secrets (CDS) and show how to use it in order to immunize the VOLE protocol of Applebaum et al. against active adversaries. Second, by using elementary properties of low-degree polynomials, we show that, for some simple arithmetic functionalities, one can easily upgrade Yao’s garbled-circuit protocol to the active setting with a minor overhead while preserving the round complexity.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_7"}, {"primary_key": "1135169", "vector": [], "sparse_vector": [], "title": "Weighted Oblivious RAM, with Applications to Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing Oblivious RAM protocols do not support the storage of data items of variable size in a non-trivial way. While the study of ORAM for items of variable size is of interest in and of itself, it is also motivated by the need for more performant and more secure Searchable Symmetric Encryption (SSE) schemes. In this article, we introduce the notion ofweightedORAM, which supports the storage of blocks of different sizes. We introduce a framework to build efficient weighted ORAM schemes, based on an underlying standard ORAM satisfying a certain suitability criterion. This criterion is fulfilled by various Tree ORAM schemes, including Simple ORAM and Path ORAM. We deduce several instantiations of weighted ORAM, with very little overhead compared to standard ORAM. As a direct application, we obtain efficient SSE constructions with attractive security properties.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_15"}, {"primary_key": "1135170", "vector": [], "sparse_vector": [], "title": "New Ways to Garble Arithmetic Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The beautiful work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [FOCS’11] initiated the study of arithmetic variants of <PERSON>’s garbled circuits. An arithmetic garbling scheme is an efficient transformation that converts an arithmetic circuit\\(C: \\mathcal {R}^n \\rightarrow \\mathcal {R}^m\\)over a ring\\(\\mathcal {R}\\)into a garbled circuit\\(\\widehat{C}\\)andnaffine functions\\(L_i\\)for\\(i \\in [n]\\), such that\\(\\widehat{C}\\)and\\(L_i(x_i)\\)reveals only the outputC(x) and no other information ofx. AIK presented the first arithmetic garbling scheme supporting computation over integers from a bounded (possibly exponentially large) range, based on Learning With Errors (LWE). In contrast, convertingCinto a Boolean circuit and applying <PERSON>’s garbled circuit treats the inputs as bit strings instead of ring elements, and hence is not “arithmetic”. In this work, we present new ways to garble arithmetic circuits, which improve the state-of-the-art on efficiency, modularity, and functionality. To measure efficiency, we define the rate of a garbling scheme as the maximal ratio between the bit-length of the garbled circuit\\(|\\widehat{C}|\\)and that of the computation tableau\\(|C|\\ell \\)in the clear, where\\(\\ell \\)is the bit length of wire values (e.g., <PERSON>’s garbled circuit has rate\\(O(\\lambda )\\)). We present the firstconstant-ratearithmetic garbled circuit for computation over large integers based on the Decisional Composite Residuosity (DCR) assumption, significantly improving the efficiency of the schemes of Applebaum, Ishai, and Kushilevitz. We construct an arithmetic garbling scheme for modular computation over\\(\\mathcal {R}= \\mathbb {Z}_p\\)for any integer modulusp, based on either DCR or LWE. The DCR-based instantiation achieves rate\\(O(\\lambda )\\)for largep. Furthermore, our construction is modular and makes black-box use of the underlying ring and a simplekey extensiongadget. We describe a variant of the first scheme supporting arithmetic circuits over bounded integers that are augmented with Boolean computation (e.g., truncation of an integer value, and comparison between two values), while keeping theconstant ratewhen garbling the arithmetic part. To the best of our knowledge, constant-rate (Boolean or arithmetic) garbling was only achieved before using the powerful primitive of indistinguishability obfuscation, or for restricted circuits with small depth.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_1"}, {"primary_key": "1135171", "vector": [], "sparse_vector": [], "title": "Disorientation Faults in CSIDH.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We investigate a new class of fault-injection attacks against the CSIDH family of cryptographic group actions. Ourdisorientation attackseffectively flip the direction of some isogeny steps. We achieve this by faulting a specific subroutine, connected to the <PERSON>re symbol or Elligator computations performed during the evaluation of the group action. These subroutines are present in almost all known CSIDH implementations. Post-processing a set of faulty samples allows us to infer constraints on the secret key. The details are implementation specific, but we show that in many cases, it is possible to recover the full secret key with only a modest number of successful fault injections and modest computational resources. We provide full details for attacking the original CSIDH proof-of-concept software as well as the CTIDH constant-time implementation. Finally, we present a set of lightweight countermeasures against the attack and discuss their security.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_11"}, {"primary_key": "1135172", "vector": [], "sparse_vector": [], "title": "XOCB: Beyond-Birthday-Bound Secure Authenticated Encryption Mode with Rate-One Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new block cipher mode of operation for authenticated encryption (AE), dubbed\\(\\textsf{XOCB}\\), that has the following features: (1) beyond-birthday-bound (BBB) security based on the standard pseudorandom assumption of the internal block cipher if the maximum block length is sufficiently smaller than the birthday bound, (2) rate-1 computation, and (3) supporting any block cipher with any key length. Namely,\\(\\textsf{XOCB}\\)has effectively the same efficiency as the seminal\\(\\textsf{OCB}\\)while having stronger quantitative security without any change in the security model or the required primitive in\\(\\textsf{OCB}\\). Although numerous studies have been conducted in the past, our\\(\\textsf{XOCB}\\)is the first mode of operation to achieve these multiple goals simultaneously.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_18"}, {"primary_key": "1135173", "vector": [], "sparse_vector": [], "title": "Truncated Boomerang Attacks and Application to AES-Based Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The boomerang attack is a cryptanalysis technique that combines two short differentials instead of using a single long differential. It has been applied to many primitives, and results in the best known attacks against several AES-based ciphers (Kiasu-BC, Deoxys-BC). In this paper, we introduce a general framework for boomerang attacks with truncated differentials. We show that the use of truncated differentials provides a significant improvement over the best boomerang attacks in the literature. In particular, we take into account structures on the plaintext and ciphertext sides, and include an analysis of the key recovery step. On 6-round AES, we obtain a competitive structural distinguisher with complexity\\(2^{87}\\)and a key recovery attack with complexity\\(2^{61}\\). The truncated boomerang attack is particularly effective against tweakable AES variants. We apply it to 8-round Kiasu-BC, resulting in the best known attack with complexity\\(2^{83}\\)(rather than\\(2^{103}\\)). We also show an interesting use of the 6-round distinguisher on the full TNT-AES, a tweakable block cipher using 6-round AES as a building block. Finally, we apply this framework to Deoxys-BC, using a MILP model to find optimal trails automatically. We obtain the best attacks against round-reduced versions of all variants of Deoxys-BC.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_1"}, {"primary_key": "1135174", "vector": [], "sparse_vector": [], "title": "End-to-End Secure Messaging with Traceability Only for Illegal Content.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "As end-to-end encrypted messaging services become widely adopted, law enforcement agencies have increasingly expressed concern that such services interfere with their ability to maintain public safety. Indeed, there is a direct tension between preserving user privacy and enabling content moderation on these platforms. Recent research has begun to address this tension, proposing systems that purport to strike a balance between the privacy of “honest” users and traceability of “malicious” users. Unfortunately, these systems suffer from a lack of protection against malicious or coerced service providers. In this work, we address the privacy vs. content moderation question through the lens of pre-constrained cryptography [<PERSON><PERSON><PERSON> et al., ITCS 2022]. We introduce the notion ofset pre-constrained(\\(\\textsf{SPC}\\))group signaturesthat guarantees security againstmalicious key generators.\\(\\textsf{SPC}\\)group signatures offer the ability to trace users in messaging systems who originate pre-defined illegal content (such as child sexual abuse material), while providing security against malicious service providers. We construct concretely efficient protocols for\\(\\textsf{SPC}\\)group signatures, and demonstrate the real-world feasibility of our approach via an implementation. The starting point for our solution is the recently introduced Apple PSI system, which we significantly modify to improve security and expand functionality.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_2"}, {"primary_key": "1135175", "vector": [], "sparse_vector": [], "title": "Supersingular Curves You Can Trust.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Generating a supersingular elliptic curve such that nobody knows its endomorphism ring is a notoriously hard task, despite several isogeny-based protocols relying on such an object. A trusted setup is often proposed as a workaround, but several aspects remain unclear. In this work, we develop the tools necessary to practically run such a distributed trusted-setup ceremony. Our key contribution is the first statistically zero-knowledge proof of isogeny knowledge that is compatible with any base field. To prove statistical ZK, we introduce isogeny graphs with Borel level structure and prove they have the Ramanujan property. Then, we analyze the security of a distributed trusted-setup protocol based on our ZK proof in the simplified universal composability framework. Lastly, we develop an optimized implementation of the ZK proof, and we propose a strategy to concretely deploy the trusted-setup protocol.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_14"}, {"primary_key": "1135176", "vector": [], "sparse_vector": [], "title": "Just How Hard Are Rotations of $\\mathbb {Z}n$? Algorithms and Cryptography with the Simplest Lattice.", "authors": ["<PERSON><PERSON>", "Atul Ganju", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We study the computational problem of finding a shortest non-zero vector in a rotation of\\(\\mathbb {Z}^n\\), which we call\\(\\mathbb {Z}\\)SVP. It has been a long-standing open problem to determine if a polynomial-time algorithm for\\(\\mathbb {Z}\\)SVP exists, and there is by now a beautiful line of work showing how to solve it efficiently in certain very special cases. However, despite all of this work, the fastest known algorithm that is proven to solve\\(\\mathbb {Z}\\)SVP is still simply the fastest known algorithm for solving SVP (i.e., the problem of finding shortest non-zero vectors inarbitrarylattices), which runs in\\(2^{n + o(n)}\\)time. We therefore set aside the (perhaps impossible) goal of finding an efficient algorithm for\\(\\mathbb {Z}\\)SVP and instead ask what else we can say about the problem. E.g., can we findanynon-trivial speedup over the best known SVP algorithm? And, if\\(\\mathbb {Z}\\)SVP actuallyishard, then what consequences would follow? Our results are as follows. We show that\\(\\mathbb {Z}\\)SVP is in a certain sense strictly easier than SVP on arbitrary lattices. In particular, we show how to reduce\\(\\mathbb {Z}\\)SVP to anapproximateversion of SVP in the same dimension (in fact, even to approximateuniqueSVP, for any constant approximation factor). Such a reduction seems very unlikely to work for SVP itself, so we view this as a qualitative separation of\\(\\mathbb {Z}\\)SVP from SVP. As a consequence of this reduction, we obtain a\\(2^{n/2 + o(n)}\\)-time algorithm for\\(\\mathbb {Z}\\)SVP, i.e., the first non-trivial speedup over the best known algorithm for SVP on general lattices. (In fact, this reduction works for a more general class of lattices—semi-stable lattices with not-too-large\\(\\lambda _1\\).) We show a simple public-key encryption scheme that is secure if (an appropriate variant of)\\(\\mathbb {Z}\\)SVP is actually hard. Specifically, our scheme is secure if it is difficult to distinguish (in the worst case) a rotation of\\(\\mathbb {Z}^n\\)fromeithera lattice with all non-zero vectors longer than\\(\\sqrt{n/\\log n}\\)ora lattice with smoothing parameter significantly smaller than the smoothing parameter of\\(\\mathbb {Z}^n\\). The latter result has an interesting qualitative connection with reverse Minkowski theorems, which in some sense say that “\\(\\mathbb {Z}^n\\)has the largest smoothing parameter.” We show a distribution of bases\\(\\textbf{B}\\)for rotations of\\(\\mathbb {Z}^n\\)such that, if\\(\\mathbb {Z}\\)SVP is hard foranyinput basis, then\\(\\mathbb {Z}\\)SVP is hard on input\\(\\textbf{B}\\). This gives a satisfying theoretical resolution to the problem of sampling hard bases for\\(\\mathbb {Z}^n\\), which was studied by Blanks and Miller [9]. This worst-case to average-case reduction is also crucially used in the analysis of our encryption scheme. (In recent independent work that appeared as a preprint before this work, Ducas and van Woerden showed essentially the same thing for general lattices [15], and they also used this to analyze the security of a public-key encryption scheme. Similar ideas also appeared in [5,11,20] in different contexts.) We perform experiments to determine how practical basis reduction performs on bases of\\(\\mathbb {Z}^n\\)that are generated in different ways and how heuristic sieving algorithms perform on\\(\\mathbb {Z}^n\\). Our basis reduction experiments complement and add to those performed by Blanks and Miller, as we work with a larger class of algorithms (i.e., larger block sizes) and study the “provably hard” distribution of bases described above. Our sieving experiments confirm that heuristic sieving algorithms perform as expected on\\(\\mathbb {Z}^n\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_9"}, {"primary_key": "1135177", "vector": [], "sparse_vector": [], "title": "A Lower Bound on the Length of Signatures Based on Group Actions and Generic Isogenies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give the first black box lower bound for signature protocols that can be described as group actions, which include many based on isogenies. We show that, for a large class of signature schemes making black box use of a (potentially non-abelian) group action, the signature length must be\\(\\varOmega (\\lambda ^2/\\log \\lambda )\\). Our class of signatures generalizes all known signatures that derive security exclusively from the group action, and our lower bound matches the state of the art, showing that the signature length cannot be improved without deviating from the group action framework.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_18"}, {"primary_key": "1135178", "vector": [], "sparse_vector": [], "title": "Finding Many Collisions via Reusable Quantum Walks - Application to Lattice Sieving.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a random functionfwith domain\\([2^n]\\)and codomain\\([2^m]\\), with\\(m \\ge n\\), a collision offis a pair of distinct inputs with the same image. Collision finding is an ubiquitous problem in cryptanalysis, and it has been well studied using both classical and quantum algorithms. Indeed, the quantum query complexity of the problem is well known to be\\(\\varTheta (2^{m/3})\\), and matching algorithms are known for any value ofm. The situation becomes different when one is looking formultiplecollision pairs. Here, for\\(2^k\\)collisions, a query lower bound of\\(\\varTheta (2^{(2k+m)/3})\\)was shown by <PERSON> and <PERSON> (EUROCRYPT 2019). A matching algorithm is known, but only for relatively small values ofm, when many collisions exist. In this paper, we improve the algorithms for this problem and, in particular, extend the range of admissible parameters where the lower bound is met. Our new method relies on achained quantum walkalgorithm, which might be of independent interest. It allows to extract multiple solutions of an MNRS-style quantum walk, without having to recompute it entirely: after finding and outputting a solution, the current state is reused as the initial state of another walk. As an application, we improve the quantum sieving algorithms for the shortest vector problem (SVP), with a complexity of\\(2^{0.2563d + o(d)}\\)instead of the previous\\(2^{0.2570d + o(d)}\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_8"}, {"primary_key": "1135179", "vector": [], "sparse_vector": [], "title": "Better Steady than Speedy: Full Break of SPEEDY-7-192.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>Plase<PERSON>"], "summary": "Differential attacks are among the most important families of cryptanalysis against symmetric primitives. Since their introduction in 1990, several improvements to the basic technique as well as many dedicated attacks against symmetric primitives have been proposed. Most of the proposed improvements concern the key-recovery part. However, when designing a new primitive, the security analysis regarding differential attacks is often limited to finding the best trails over a limited number of rounds with branch and bound techniques, and a poor heuristic is then applied to deduce the total number of rounds a differential attack could reach. In this work we analyze the security of theSPEEDYfamily of block ciphers against differential cryptanalysis and show how to optimize many of the steps of the key-recovery procedure for this type of attacks. For this, we implemented a search for finding optimal trails for this cipher and their associated multiple probabilities under some constraints and applied non-trivial techniques to obtain optimal data and key-sieving. This permitted us to fully breakSPEEDY-7-192, the 7-round variant ofSPEEDYsupposed to provide 192-bit security. Our work demonstrates among others the need to better understand the subtleties of differential cryptanalysis in order to get meaningful estimates on the security offered by a cipher against these attacks.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_2"}, {"primary_key": "1135180", "vector": [], "sparse_vector": [], "title": "Oblivious Transfer with Constant Computational Overhead.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Thecomputational overheadof a cryptographic task is the asymptotic ratio between the computational cost of securely realizing the task and that of realizing the task with no security at all. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (STOC 2008) showed that secure two-party computation of Boolean circuits can be realized withconstantcomputational overhead, independent of the desired level of security, assuming the existence of an oblivious transfer (OT) protocol and a local pseudorandom generator (PRG). However, this only applies to the case of semi-honest parties. A central open question in the area is the possibility of a similar result formaliciousparties. This question is open even for the simpler task of securely realizing many instances of a constant-size function, such as OT of bits. We settle the question in the affirmative for the case of OT, assuming: (1) a standard OT protocol, (2) a slightly stronger “correlation-robust\" variant of a local PRG, and (3) a standard sparse variant of the Learning Parity with Noise (LPN) assumption. An optimized version of our construction requires fewer than 100 bit operations per party per bit-OT. For 128-bit security, this improves over the best previous protocols by 1–2 orders of magnitude. We achieve this by constructing a constant-overheadpseudorandom correlation generator(PCG) for the bit-OT correlation. Such a PCG generatesNpseudorandom instances of bit-OT by locally expanding short, correlated seeds. As a result, we get an end-to-end protocol for generatingNpseudorandom instances of bit-OT witho(N) communication,O(N) computation, and security that scales sub-exponentially withN. Finally, we present applications of our main result to realizing other secure computation tasks with constant computational overhead. These include protocols for general circuits with a relaxed notion of security against malicious parties, protocols for realizingNinstances of natural constant-size functions, and reducing the main open question to a potentially simpler question about fault-tolerant computation.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_10"}, {"primary_key": "1135181", "vector": [], "sparse_vector": [], "title": "Sublinear-Communication Secure Multiparty Computation Does Not Require FHE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure computation enables mutually distrusting parties to jointly compute a function on their secret inputs, while revealing nothing beyond the function output. A long-running challenge is understanding the required communication complexity of such protocols—in particular, when communication can besublinearin the circuit representation size of the desired function. Significant advances have been made affirmatively answering this question within thetwo-partysetting, based on a variety of structures and hardness assumptions. In contrast, in themulti-partysetting, only one general approach is known: using Fully Homomorphic Encryption (FHE). This remains the state of affairs even for just three parties, with two corruptions. We present a framework for achieving secure sublinear-communication\\((N+1)\\)-party computation, building from a particular form of Function Secret Sharing for onlyNparties. In turn, we demonstrate implications to sublinear secure computation for various function classes in the 3-party and 5-party settings based on an assortment of assumptions not known to imply FHE.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_6"}, {"primary_key": "1135182", "vector": [], "sparse_vector": [], "title": "A New Algebraic Approach to the Regular Syndrome Decoding Problem and Implications for PCG Constructions.", "authors": ["<PERSON>", "Morten Øygarden"], "summary": "The Regular Syndrome Decoding (RSD) problem, a variant of the Syndrome Decoding problem with a particular error distribution, was introduced almost 20 years ago by Augotet al.. In this problem, the error vector is divided into equally sized blocks, each containing a single noisy coordinate. More recently, the last five years have seen increased interest in this assumption due to its use in MPC and ZK applications. Generally referred to as “LPN with regular noise\" in this context, the assumption allows to achieve better efficiency when compared to plain LPN. In all previous works of cryptanalysis, it has not been shown how to exploit the special feature of this problem in an attack. We present the first algebraic attack on RSD. Based on a careful theoretical analysis of the underlying polynomial system, we propose concrete attacks that are able to take advantage of the regular noise distribution. In particular, we can identify several examples of concrete parameters where our techniques outperform other algorithms.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_14"}, {"primary_key": "1135183", "vector": [], "sparse_vector": [], "title": "Short Signatures from Regular Syndrome Decoding in the Head.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new candidate post-quantum digital signature scheme from the regular syndrome decoding (RSD) assumption, an established variant of the syndrome decoding assumption which asserts that it is hard to find\\(w \\)-regular solutions to systems of linear equations over\\(\\mathbb {F}_2\\)(a vector is regular if it is a concatenation of\\(w \\)unit vectors). Our signature is obtained by introducing and compiling a new 5-round zero-knowledge proof system constructed using the MPC-in-the-head paradigm. At the heart of our result is an efficient MPC protocol in the preprocessing model that checks correctness of a regular syndrome decoding instance by using a share ring-conversion mechanism. The analysis of our construction is non-trivial and forms a core technical contribution of our work. It requires careful combinatorial analysis and combines several new ideas, such as analyzing soundness in a relaxed setting where a cheating prover is allowed to use any witnesssufficiently closeto a regular vector. We complement our analysis with an in-depth overview of existing attacks against RSD. Our signatures are competitive with the best-known code-based signatures, ranging from 12.52 KB (fast setting, with signing time of the order of a few milliseconds on a single core of a standard laptop) to about 9 KB (short setting, with estimated signing time of the order of 15 ms).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_19"}, {"primary_key": "1135184", "vector": [], "sparse_vector": [], "title": "Functional Commitments for All Functions, with Transparent Setup and from SIS.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Afunctional commitmentscheme enables a user to concisely commit to a function from a specified family, then later concisely and verifiably reveal values of the function at desired inputs. Useful special cases, which have seen applications across cryptography, include vector commitments and polynomial commitments. To date, functional commitments have been constructed (under falsifiable assumptions) only for functions that are essentiallylinear, with one recent exception that works for arbitrarily complex functions. However, that scheme operates in a strong and non-standard model, requiring an online, trusted authority to generate special keys for any opened function inputs. In this work, we give the first functional commitment scheme for nonlinear functions—indeed, forall functionsof any bounded complexity—under a standard setup and a falsifiable assumption. Specifically, the setup is “transparent,” requiring only public randomness (and not any trusted entity), and the assumption is the hardness of the standard Short Integer Solution (SIS) lattice problem. Our construction also has other attractive features, including:stateless updatesvia generic composability; excellentasymptotic efficiencyfor the verifier, and also for the committer in important special cases like vector and polynomial commitments, via preprocessing; andpost-quantum security, since it is based on SIS.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_10"}, {"primary_key": "1135185", "vector": [], "sparse_vector": [], "title": "An Efficient Key Recovery Attack on SIDH.", "authors": ["W<PERSON><PERSON>", "<PERSON>"], "summary": "We present an efficient key recovery attack on the Supersingular Isogeny Diffie–<PERSON>man protocol (SIDH). The attack is based on <PERSON><PERSON>’s “reducibility criterion” for isogenies from products of elliptic curves and strongly relies on the torsion point images that <PERSON> and <PERSON> exchange during the protocol. If we assume knowledge of the endomorphism ring of the starting curve then the classical running time is polynomial in the input size (heuristically), apart from the factorization of a small number of integers that only depend on the system parameters. The attack is particularly fast and easy to implement if one of the parties uses 2-isogenies and the starting curve comes equipped with a non-scalar endomorphism of very small degree; this is the case for SIKE, the instantiation of SIDH that recently advanced to the fourth round of NIST’s standardization effort for post-quantum cryptography. Our Magma implementation breaksSIKEp434, which aims at security level 1, in about ten minutes on a single core.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_15"}, {"primary_key": "1135186", "vector": [], "sparse_vector": [], "title": "Reverse Firewalls for Oblivious Transfer Extension and Applications to Zero-Knowledge.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the setting of subversion, an adversary tampers with the machines of the honest parties thus leaking the honest parties’ secrets through the protocol transcript. The work of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT’15) introduced the idea of reverse firewalls (RF) to protect against tampering of honest parties’ machines. All known constructions in the RF framework rely on the malleability of the underlying operations in order for the RF to rerandomize/sanitize the transcript. RFs are thus limited to protocols that offer some structure, and hence based on public-key operations. In this work, we initiate the study ofefficientMultiparty Computation (MPC) protocols in the presence of tampering. In this regard, We construct thefirstOblivious Transfer (OT) extension protocol in the RF setting. We obtain\\(\\textsf {poly}(\\kappa )\\)maliciously-secure OTs using\\(\\mathcal {O}(\\kappa )\\)public key operations and\\(\\mathcal {O}(1)\\)inexpensive symmetric key operations, where\\(\\kappa \\)is the security parameter. We construct thefirstZero-knowledge protocol in the RF setting where each multiplication gate can be proven using\\(\\mathcal {O}(1)\\)symmetric key operations. We achieve this using our OT extension protocol and by extending the ZK protocol of Quicksilver (<PERSON>, <PERSON>, <PERSON> and <PERSON>,CCS’21) to the RF setting. Along the way, we introduce new ideas for malleable interactive proofs that could be of independent interest. We define a notion offull malleabilityfor Sigma protocols that unlike prior notions allow modifying the instance as well, in addition to the transcript. We construct new protocols that satisfy this notion, construct RFs for such protocols and use them in constructing our OT extension. The key idea of our work is to demonstrate that correlated randomness may be obtained in an RF-friendly waywithouthaving to rerandomize the entire transcript. This enables us to avoid expensive public-key operations that grow with the circuit-size.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_9"}, {"primary_key": "1135187", "vector": [], "sparse_vector": [], "title": "Deniable Authentication When Signing Keys Leak.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deniable Authentication is a highly desirable property for secure messaging protocols: it allows a sender <PERSON> to authentically transmit messages to a designated receiver <PERSON> in such a way that only <PERSON> gets convinced that <PERSON> indeed sent these messages. In particular, it guarantees that even if <PERSON> tries to convince a (non-designated) party <PERSON> that <PERSON> sent some message, and even if <PERSON> gives <PERSON> his own secret key, <PERSON> will not be convinced: as far as <PERSON> knows,<PERSON> could be making it all up! In this paper we study Deniable Authentication in the setting where <PERSON> can additionally obtain <PERSON>’s secret key. Informally, we want that knowledge of <PERSON>’s secret key does not help <PERSON> in learning whether <PERSON> sent any messages, even if <PERSON> does not have <PERSON>’s secret key and even if <PERSON> cooperates with <PERSON> by giving her his own secret key. This stronger flavor of Deniable Authentication was not considered before and is particularly relevant forGroup Messaging as it gives users stronger deniability guarantees. Our main contribution is a scalable “” () scheme—a technical formalization of Deniable Authentication that is particularly useful for secure messaging for its confidentiality guarantees—that provides this stronger deniability guarantee. At its core lie new\\(\\mathrm { M{\\textrm{DVS}}}\\)(Multi-Designated Verifier Signature) and\\(\\text {PKEBC}\\)(Public Key Encryption for Broadcast) scheme constructions: our\\(\\mathrm { M{\\textrm{DVS}}}\\)is not only secure with respect to the new deniability notions, but it is also the first to be tightly secure under standard assumptions; our\\(\\text {PKEBC}\\)—which is also of independent interest—is the first with ciphertext sizes and encryption and decryption times that grow only linearly in the number of receivers. This is a significant improvement upon the construction given by Maurer et al. (EUROCRYPT ’22), where ciphertext sizes and encryption and decryption times are quadratic in the number of receivers.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_3"}, {"primary_key": "1135188", "vector": [], "sparse_vector": [], "title": "HyperPlonk: Plonk with Linear-Time Prover and High-Degree Custom Gates.", "authors": ["<PERSON><PERSON>", "Benedikt <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Plonk is a widely used succinct non-interactive proof system that uses univariate polynomial commitments. Plonk is quite flexible: it supports circuits with low-degree “custom” gates as well as circuits with lookup gates (a lookup gate ensures that its input is contained in a predefined table). For large circuits, the bottleneck in generating a Plonk proof is the need for computing a large FFT. We presentHyperPlonk, an adaptation of Plonk to the boolean hypercube, using multilinear polynomial commitments.HyperPlonkretains the flexibility of Plonk but provides several additional benefits. First, it avoids the need for an FFT during proof generation. Second, and more importantly, it supports custom gates of much higher degree than Plonk without harming the running time of the prover. Both of these can dramatically speed up the prover’s running time. SinceHyperPlonkrelies on multilinear polynomial commitments, we revisit two elegant constructions: one fromOrionand one fromVirgo. We show how to reduce theOrionopening proof size to less than 10 KB (an almost factor 1000 improvement) and show how to make theVirgoFRI-based opening proof simpler and shorter (This is an extended abstract. The full version is available on EPRINT[22]).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_17"}, {"primary_key": "1135189", "vector": [], "sparse_vector": [], "title": "Proof-Carrying Data from Arithmetized Random Oracles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Jack <PERSON>&<PERSON>;<PERSON>", "<PERSON>"], "summary": "Proof-carrying data (PCD) is a powerful cryptographic primitive that allows mutually distrustful parties to perform distributed computation in an efficiently verifiable manner. Known constructions of PCD are obtained by recursively-composing SNARKs or related primitives. SNARKs with desirable properties such as transparent setup are constructed in the random oracle model. However, using such SNARKs to construct PCD requires heuristically instantiating the oracle and using it in a non-black-box way. [<PERSON><PERSON><PERSON>] constructed SNARKs in the low-degree random oracle model, circumventing this issue, but instantiating their model in the real world appears difficult. In this paper, we introduce a new model: thearithmetized random oracle model(AROM). We provide a plausible standard-model (software-only) instantiation of the AROM, and we construct PCD in the AROM, given only a standard-model collision-resistant hash function. Furthermore, our PCD construction is for arbitrary-depth compliance predicates. We obtain our PCD construction by showing how to construct SNARKs in the AROM for computations that query the oracle, given an accumulation scheme for oracle queries in the AROM. We then construct such an accumulation scheme for the AROM. We give an efficient “lazy sampling” algorithm (anemulator) for the ARO up to some error. Our emulator enables us to prove the security of cryptographic constructs in the AROM and that zkSNARKs in the ROM also satisfy zero-knowledge in the AROM. The algorithm is non-trivial, and relies on results in algebraic query complexity and the combinatorial nullstellensatz.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_13"}, {"primary_key": "1135190", "vector": [], "sparse_vector": [], "title": "Black-Box Separations for Non-interactive Classical Commitments in a Quantum World.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Commitments are fundamental in cryptography. In the classical world, commitments are equivalent to the existence of one-way functions. It is also known that the most desired form of commitments in terms of their round complexity, i.e.,non-interactivecommitments,cannotbe built from one-way functions in a black-box way [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Crypto’12]. However, if one allows the parties to use quantum computationandcommunication, it is known that non-interactive commitments (to classical bits) are in fact possible [Koshiba-Odaira, Arxiv’11 and <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, TCC’21]. We revisit the assumptions behind non-interactive commitments in a quantum world and study whether they can be achieved using quantum computation andclassicalcommunication based on a black-box use of one-way functions. We prove that doing so is impossible unless the Polynomial Compatibility Conjecture [<PERSON><PERSON><PERSON> et al. Crypto’22] is false. We further extend our impossibility to protocols with quantum decommitments. This complements the positive result of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [TCC’21], as they only required a classical decommitment message. Because non-interactive commitments can be based on injective one-way functions, assuming the Polynomial Compatibility Conjecture, we also obtain a black-box separation between one-way functions and injective one-way functions (e.g., one-way permutations) even when the construction and the security reductions are allowed to be quantum. This improves the separation of <PERSON> and <PERSON><PERSON> [Theoretical Computer Science’21] in which they only allowed thesecurity reductionto be quantum. At a technical level, we prove that sampling oracles at random from “sufficiently large” sets (of oracles) will make them one-way against polynomial quantum-query adversaries who also get arbitrary polynomial-size quantum advice about the oracle. This gives a natural generalization of the recent results of Hhan et al. [Asiacrypt’19] and Chung et al. [FOCS’20].", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_6"}, {"primary_key": "1135191", "vector": [], "sparse_vector": [], "title": "Proof of Mirror Theory for a Wide Range of $\\xi _{\\max }$.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In CRYPTO’03, <PERSON><PERSON><PERSON> conjectured a lower bound on the number of distinct solutions\\((P_1, \\ldots , P_{q}) \\in (\\{0,1\\}^{n})^{q}\\)satisfying a system of equations of the form\\(X_i \\oplus X_j = \\lambda _{i,j}\\)such that\\(P_1, P_2, \\ldots \\),\\(P_{q}\\)are pairwise distinct. This result is known as “\\(P_i \\oplus P_j\\)Theorem for any\\(\\xi _{\\max }\\)” or alternatively asMirror Theory for general\\(\\xi _{\\max }\\), which was later proved by <PERSON><PERSON><PERSON> in ICISC’05. Mirror theory for general\\(\\xi _{\\max }\\)stands as a powerful tool to provide a high-security guarantee for many blockcipher-(or even ideal permutation-) based designs. Unfortunately, the proof of the result contains gaps that are non-trivial to fix. In this work, we present the first complete proof of the\\(P_i \\oplus P_j\\)theorem for a wide range of\\(\\xi _{\\max }\\), typically up to order\\(O(2^{n/4}/\\sqrt{n})\\). Furthermore, our proof approach is made simpler by using a new type of equation, dubbed link-deletion equation, that roughly corresponds to half of the so-called orange equations from earlier works. As an illustration of our result, we also revisit the security proofs of two optimally secure blockcipher-based pseudorandom functions, andn-bit security proof for six round Feistel cipher, and provide updated security bounds.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_16"}, {"primary_key": "1135192", "vector": [], "sparse_vector": [], "title": "Constrained Pseudorandom Functions from Homomorphic Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose and analyze a simple strategy for constructing 1-key constrained pseudorandom functions (CPRFs) from homomorphic secret sharing. In the process, we obtain the following contributions: first, we identify desirable properties for the underlying HSS scheme for our strategy to work. Second, we show that (most of) recent existing HSS schemes satisfy these properties, leading to instantiations of CPRFs for various constraints and from various assumptions. Notably, we obtain the first (1-key selectively secure, private) CPRFs for inner-product and (1-key selectively secure) CPRFs for\\(\\textsf{NC}^1\\)from the DCR assumption, and more. Last, we revisit two applications of HSS equipped with these additional properties to secure computation: we obtain secure computation in the silent preprocessing model with one party being able to precompute its whole preprocessing material before even knowing the other party, and we construct one-sided statistically secure computation with sublinear communication for restricted forms of computation.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_7"}, {"primary_key": "1135193", "vector": [], "sparse_vector": [], "title": "Actively Secure Half-Gates with Minimum Overhead Under Duplex Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Actively secure two-party computation (2PC) is one of the canonical building blocks in modern cryptography. One main goal for designing actively secure 2PC protocols is to reduce the communication overhead, compared to semi-honest 2PC protocols. In this paper, we propose a new actively secure constant-round 2PC protocol with one-way communication of\\(2\\kappa +5\\)bits per AND gate (for\\(\\kappa \\)-bit computational security and any statistical security), essentially matching the one-way communication of semi-honest half-gates protocol. This is achieved by two new techniques: The recent compression technique by <PERSON><PERSON><PERSON> et al. (Crypto 2022) shows that a relaxed preprocessing is sufficient for authenticated garbling that does not reveal masked wire values to the garbler. We introduce a new form of authenticated bits and propose a new technique of generating authenticated AND triples to reduce the one-way communication of preprocessing from\\(5\\rho +1\\)bits to 2 bits per AND gate for\\(\\rho \\)-bit statistical security. Unfortunately, the above compressing technique is only compatible with a less compact authenticated garbled circuit of size\\(2\\kappa +3\\rho \\)bits per AND gate. We designed a new authenticated garbling that does not use information theoretic MACs but rather dual execution without leakage to authenticate wire values in the circuit. This allows us to use a more compact half-gates based authenticated garbled circuit of size\\(2\\kappa +1\\)bits per AND gate, and meanwhile keep compatible with the compression technique. Our new technique can achieve one-way communication of\\(2\\kappa +5\\)bits per AND gate. Our technique of yielding authenticated AND triples can also be used to optimize the two-way communication (i.e., the total communication) by combining it with the authenticated garbled circuits by Dittmer et al., which results in an actively secure 2PC protocol with two-way communication of\\(2\\kappa +3\\rho +4\\)bits per AND gate.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_2"}, {"primary_key": "1135194", "vector": [], "sparse_vector": [], "title": "One-Hot Conversion: Towards Faster Table-Based A2B Conversion.", "authors": ["Jan<PERSON><PERSON>&apo<PERSON>;An<PERSON>"], "summary": "Arithmetic to Boolean masking (A2B) conversion is a crucial technique in the masking of lattice-based post-quantum cryptography. It is also a crucial part of building a masked comparison which is one of the hardest to mask building blocks for active secure lattice-based encryption. We first present a new method, called one-hot conversion, to efficiently convert from higher-order arithmetic masking to Boolean masking using a variant of the higher-order table-based conversion of <PERSON><PERSON> et al. Secondly, we specialize our method to perform arithmetic to 1-bit Boolean functions. Our one-hot function can be applied to masking lattice-based encryption building blocks such as masked comparison or to determine the most significant bit of an arithmetically masked variable. In our benchmarks on a Cortex M4 processor, a speedup of 15 times is achieved over state-of-the-art table-based A2B conversions, bringing table-based A2B conversions within the performance range of the Boolean circuit-based A2B conversions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_21"}, {"primary_key": "1135195", "vector": [], "sparse_vector": [], "title": "Minimizing Setup in Broadcast-Optimal Two Round MPC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we consider two-round secure computation protocols which use different communication channels in different rounds: namely, protocols where broadcast is available in neither round, both rounds, only the first round, or only the second round. The prior works of <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (Eurocrypt 2020) and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> (Crypto 2021) give tight characterizations of which security guarantees are achievable for various thresholds in each communication structure . In this work, we introduce a new security notion, namely,selective identifiable abort, which guarantees that every honest party either obtains the output, or aborts identifying one corrupt party (where honest parties may potentially identify different corrupted parties). We investigate what broadcast patterns in two-round MPC allow achieving this guarantee across various settings (such as with or without PKI, with or without an honest majority). Further, we determine what is possible in the honest majority settingwithouta PKI, closing a question left open by Damgå<PERSON><PERSON> al.We show that without a PKI, having an honest majority does not make it possible to achieve stronger security guarantees compared to the dishonest majority setting. However, if two-thirds of the parties are guaranteed to be honest,identifiable abortis additionally achievable using broadcast only in the second round. We use fundamentally different techniques from the previous works to avoid relying on private communication in the first round when a PKI is not available, since assuming such private channels without the availability of public encryption keys is unrealistic. We also show that, somewhat surprisingly, the availability of private channels in the first round does not enable stronger security guarantees unless the corruption threshold is one.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_5"}, {"primary_key": "1135196", "vector": [], "sparse_vector": [], "title": "Spartan and Bulletproofs are Simulation-Extractable (for Free!).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Increasing deployment of advanced zero-knowledge proof systems, especially zkSNARKs, has raised critical questions about their security against real-world attacks. Two classes of attacks of concern in practice areadaptive soundnessattacks, where an attacker can prove false statements by choosing its public input after generating a proof, andmalleabilityattacks, where an attacker can use a valid proof to create another valid proof it could not have created itself. Prior work has shown thatsimulation-extractability(\\(\\textsf{SIM}\\text {-}\\textsf{EXT} \\)), a strong notion of security for proof systems, rules out these attacks. In this paper, we prove that two transparent, discrete-log-based zkSNARKs, Spartan and Bulletproofs, are simulation-extractable (\\(\\textsf{SIM}\\text {-}\\textsf{EXT} \\)) in the random oracle model if the discrete logarithm assumption holds in the underlying group. Since these assumptions are required to prove standard security properties for Spartan and Bulletproofs, our results show that\\(\\textsf{SIM}\\text {-}\\textsf{EXT} \\)is, surprisingly, “for free” with these schemes. Our result is the first\\(\\textsf{SIM}\\text {-}\\textsf{EXT} \\)proof for Spartan and encompasses both linear- and sublinear-verifier variants. Our result for Bulletproofs encompasses both the aggregate range proof and arithmetic circuit variants, and is the first to not rely on the algebraic group model (AGM), resolving an open question posed by <PERSON><PERSON><PERSON> et al. (EUROCRYPT’22). As part of our analysis, we develop a generalization of the tree-builder extraction theorem of Attema et al. (TCC’22), which may be of independent interest.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_18"}, {"primary_key": "1135197", "vector": [], "sparse_vector": [], "title": "On the Hardness of the Finite Field Isomorphism Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The finite field isomorphism\\((\\textsf{FFI})\\)problem was introduced in PKC’18, as an alternative to average-case lattice problems (like\\(\\textsf{LWE}\\),\\(\\textsf{SIS}\\), or\\(\\textsf{NTRU}\\)). As an application, the same paper used the\\(\\textsf{FFI}\\)problem to construct a fully homomorphic encryption scheme. In this work, we prove that the decision variant of the\\(\\textsf{FFI}\\)problem can be solved in polynomial time for any field characteristics\\(q= \\varOmega (\\beta n^2)\\), where\\(q,\\beta ,n\\)parametrize the\\(\\textsf{FFI}\\)problem. Then we use our result from the\\(\\textsf{FFI}\\)distinguisher to propose polynomial-time attacks on the semantic security of the fully homomorphic encryption scheme. Furthermore, for completeness, we also study the search variant of the\\(\\textsf{FFI}\\)problem and show how to state it as aq-ary lattice problem, which was previously unknown. As a result, we can solve the search problem for some previously intractable parameters using a simple lattice reduction approach.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_12"}, {"primary_key": "1135198", "vector": [], "sparse_vector": [], "title": "Fully Adaptive Decentralized Multi-Authority ABE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Decentralized multi-authority attribute-based encryption (\\(\\textsf{MA}\\text {-}\\textsf{ABE}\\)) is a distributed generalization of standard (ciphertext-policy) attribute-based encryption where there is no trusted central authority: any party can become an authority and issue private keys, and there is no requirement for any global coordination other than the creation of an initial set of common reference parameters. We present the first multi-authority attribute-based encryption schemes that are provably fully-adaptively secure. Namely, our construction is secure against an attacker that may corrupt some of the authorities as well as perform key queries adaptively throughout the life-time of the system. Our main construction relies on a prime order bilinear group where thek-linear assumption holds as well as on a random oracle. Along the way, we present a conceptually simpler construction relying on a composite order bilinear group with standard subgroup decision assumptions as well as on a random oracle. Prior to this work, there was no construction that could resist adaptive corruptions of authorities, no matter the assumptions used. In fact, we point out that even standard complexity leveraging style arguments do not work in the multi-authority setting.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_15"}, {"primary_key": "1135199", "vector": [], "sparse_vector": [], "title": "Efficient Detection of High Probability Statistical Properties of Cryptosystems via Surrogate Differentiation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A central problem in cryptanalysis is to find all the significant deviations from randomness in a givenn-bit cryptographic primitive. Whennis small (e.g., an 8-bit S-box), this is easy to do, but for largen, the only practical way to find such statistical properties was to exploit the internal structure of the primitive and to speed up the search with a variety of heuristic rules of thumb. However, such bottom-up techniques can miss many properties, especially in cryptosystems which are designed to have hidden trapdoors. In this paper we consider the top-down version of the problem in which the cryptographic primitive is given as a structureless black box, and reduce the complexity of the best known techniques for finding all its significant differential and linear properties by a large factor of\\(2^{n/2}\\). Our main new tool is the idea of usingsurrogate differentiation. In the context of finding differential properties, it enables us to simultaneously find information about all the differentials of the form\\(f(x) \\oplus f(x \\oplus \\alpha )\\)in all possible directions\\(\\alpha \\)by differentiatingfin a single randomly chosen direction\\(\\gamma \\)(which is unrelated to the\\(\\alpha \\)’s). In the context of finding linear properties, surrogate differentiation can be combined in a highly effective way with the Fast Fourier Transform. For 64-bit cryptographic primitives, this technique makes it possible to automatically find in about\\(2^{64}\\)time all their differentials with probability\\(p \\ge 2^{-32}\\)and all their linear approximations with bias\\(|p| \\ge 2^{-16}\\)(using\\(2^{64}\\)memory); previous algorithms for these problems required at least\\(2^{96}\\)time. Similar techniques can be used to significantly improve the best known time complexities of finding related key differentials, second-order differentials, and boomerangs. In addition, we show how to run variants of these algorithms which require no memory, and how to detect such statistical properties even in trapdoored cryptosystems whose designers specifically try to evade our techniques.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_4"}, {"primary_key": "1135200", "vector": [], "sparse_vector": [], "title": "On Differential Privacy and Adaptive Data Analysis with Bounded Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the space complexity of the two related fields ofdifferential privacyandadaptive data analysis. Specifically, Under standard cryptographic assumptions, we show that there exists a problemPthat requires exponentially more space to be solved efficiently with differential privacy, compared to the space needed without privacy. To the best of our knowledge, this is the first separation between the space complexity of private and non-private algorithms. The line of work on adaptive data analysis focuses on understanding the number ofsamplesneeded for answering a sequence of adaptive queries. We revisit previous lower bounds at a foundational level, and show that they are a consequence of a space bottleneck rather than a sampling bottleneck. To obtain our results, we define and construct an encryption scheme with multiple keys that is built to withstand a limited amount of key leakage in a very particular way.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_2"}, {"primary_key": "1135201", "vector": [], "sparse_vector": [], "title": "End-to-End Encrypted Zoom Meetings: Proving Security and Strengthening Liveness.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In May 2020, Zoom Video Communications, Inc. (Zoom) announced a multi-step plan to comprehensively support end-to-end encrypted (E2EE) group video calls and subsequently rolled out basic E2EE support to customers in October 2020. In this work we provide the first formal security analysis of Zoom’s E2EE protocol, and also lay foundation to the general problem of E2EE group video communication. We observe that the vast security literature analyzing asynchronous messaging does not translate well to synchronous video calls. Namely, while strong forms of forward secrecy and post compromise security are less important for (typically short-lived) video calls, variouslivenessproperties become crucial. For example, mandating that participants quickly learn of updates to the meeting roster and key, media streams being displayed are recent, and banned participants promptly lose any access to the meeting. Our main results are as follows: Propose a new notion ofleader-based continuous group key agreement with liveness, which accurately captures the E2EE properties specific to the synchronous communication scenario. Prove security of the core of Zoom’s E2EE meetings protocol in the above well-defined model. Propose ways to strengthen Zoom’s liveness properties by simple modifications to the original protocol, which have since been deployed in production.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_6"}, {"primary_key": "1135202", "vector": [], "sparse_vector": [], "title": "Speak Much, Remember <PERSON>: Cryptography in the Bounded Storage Model, Revisited.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of thebounded storage model (BSM)is to construct unconditionally secure cryptographic protocols, by only restricting the storage capacity of the adversary, but otherwise giving it unbounded computational power. Here, we consider astreamingvariant of the BSM, where honest parties can stream huge amounts of data to each other so as to overwhelm the adversary’s storage, even while their own storage capacity is significantly smaller than that of the adversary. Prior works showed several impressive results in this model, includingkey agreementandoblivious transfer, but only as long as adversary’s storage\\(m = O(n^2)\\)is at most quadratically larger than the honest user storagen. Moreover, the work of <PERSON><PERSON><PERSON><PERSON> and Maurer (DM) also gave a seemingly matching lower bound, showing that key agreement in the BSM is impossible when\\(m > n^2\\). In this work, we observe that the DM lower bound only applies to a significantly more restricted version of the BSM, and does not apply to the streaming variant. Surprisingly, we show that it is possible to construct key agreement and oblivious transfer protocols in the streaming BSM, where the adversary’s storage can be significantly larger, and even exponential\\(m = 2^{O(n)}\\). The only price of accommodating larger values ofmis that the round and communication complexities of our protocols grow accordingly, and we provide lower bounds to show that an increase in rounds and communication is necessary. As an added benefit of our work, we also show that our oblivious transfer (OT) protocol in the BSM satisfies a simulation-based notion of security. In contrast, even for the restricted case of\\(m = O(n^2)\\), prior solutions only satisfied a weaker indistinguishability based definition. As an application of our OT protocol, we get general multiparty computation (MPC) in the BSM that allows for up to exponentially large gaps betweenmandn, while also achieving simulation-based security.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_4"}, {"primary_key": "1135203", "vector": [], "sparse_vector": [], "title": "Efficient Laconic Cryptography from Learning with Errors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Laconic cryptography is an emerging paradigm that enables cryptographic primitives with sublinear communication complexity in just two messages. In particular, a two-message protocol between <PERSON> and <PERSON> is calledlaconicif its communication and computation complexity are essentially independent of the size of <PERSON>’s input. This can be thought of as a dual notion of fully-homomorphic encryption, as it enables “Bob-optimized” protocols. This paradigm has led to tremendous progress in recent years. However, all existing constructions of laconic primitives are considered only oftheoretical interest: They all rely on non-black-box cryptographic techniques, which are highly impractical. This work shows that non-black-box techniques are not necessary for basic laconic cryptography primitives. We propose acompletely algebraicconstruction of laconic encryption, a notion that we introduce in this work, which serves as the cornerstone of our framework. We prove that the scheme is secure under the standard Learning With Errors assumption (with polynomial modulus-to-noise ratio). We provide proof-of-concept implementations for the first time for laconic primitives, demonstrating the construction is indeed practical: For a database size of\\(2^{50}\\), encryption and decryption are in the order of single digitmilliseconds. Laconic encryption can be used as a black box to construct other laconic primitives. Specifically, we show how to construct: Laconic oblivious transfer Registration-based encryption scheme Laconic private-set intersection protocol All of the above have essentially optimal parameters and similar practical efficiency. Furthermore, our laconic encryption can be preprocessed such that the online encryption step is entirely combinatorial and therefore much more efficient. Using similar techniques, we also obtain identity-based encryption with an unbounded identity space and tight security proof (in the standard model).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_14"}, {"primary_key": "1135204", "vector": [], "sparse_vector": [], "title": "SuperPack: Dishonest Majority MPC with Constant Online Communication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON>", "Chen<PERSON>"], "summary": "In this work we present a novel actively secure dishonest majority MPC protocol,SuperPack, whose efficiency improves as the number ofhonestparties increases. Concretely, let\\(0<\\epsilon <1/2\\)and consider an adversary that corrupts\\(t<n(1-\\epsilon )\\)out ofnparties.SuperPackrequires\\(6/\\epsilon \\)field elements of online communication per multiplication gate across all parties, assuming circuit-dependent preprocessing, and\\(10/\\epsilon \\)assuming circuit-independent preprocessing. In contrast, most of previous works such as SPDZ (Damgårdet al., ESORICS 2013) and its derivatives perform the same regardless of whether there is only one honest party, or a constant (non-majority) fraction of honest parties. The only exception is due to Goyalet al.(CRYPTO 2022), which achieves\\(58/\\epsilon + 96/\\epsilon ^2\\)field elements assuming circuit-independent preprocessing. Our work improves this result substantially by a factor of at least 25 in the circuit-independent preprocessing model. Practically, we also compare our work with the best concretely efficient online protocol Turbospeedz (Ben-Efraimet al., ACNS 2019), which achieves\\(2(1-\\epsilon )n\\)field elements per multiplication gate among all parties. Our online protocol improves over Turbospeedz asngrows, and as\\(\\epsilon \\)approaches 1/2. For example, if there are\\(90\\%\\)corruptions (\\(\\epsilon =0.1\\)), with\\(n=50\\)our online protocol is\\(1.5\\times \\)better than Turbospeedz and with\\(n=100\\)this factor is\\(3\\times \\), but for\\(70\\%\\)corruptions (\\(\\epsilon =0.3\\)) with\\(n=50\\)our online protocol is\\(3.5\\times \\)better, and for\\(n=100\\)this factor is\\(7\\times \\). Our circuit-dependent preprocessing can be instantiated from OLE/VOLE. The amount of OLE/VOLE correlations required in our work is a factor of\\(\\approx \\epsilon n/2\\)smaller than these required by Le Mans (Rachuri and Scholl, CRYPTO 2022) leveraged to instantiate the preprocessing of Turbospeedz. Our dishonest majority protocol relies on packed secret-sharing and leverages ideas from the honest majorityTurboPack(Escuderoet al., CCS 2022) protocol to achieve concrete efficiency for any circuit topology, not only SIMD. We implement bothSuperPackand Turbospeedz and verify with experimental results that our approach indeed leads to more competitive runtimes in distributed environments with a moderately large number of parties.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_8"}, {"primary_key": "1135205", "vector": [], "sparse_vector": [], "title": "New Time-Memory Trade-Offs for Subset Sum - Improving ISD in Theory and Practice.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose new time-memory trade-offs for the random subset sum problem defined on\\((a_1,\\ldots ,a_n,t)\\)over\\(\\mathbb {Z}_{2^n}\\). Our trade-offs yield significant running time improvements for every fixed memory limit\\(M\\ge 2^{0.091n}\\). Furthermore, we interpolate to the running times of the fastest known algorithms when memory is not limited. Technically, our design introduces a pruning strategy to the construction by <PERSON><PERSON><PERSON><PERSON> (BCJ) that allows for an exponentially small success probability. We compensate for this reduced probability by multiple randomized executions. Our main improvement stems from the clever reuse of parts of the computation in subsequent executions to reduce the time complexity per iteration. As an application of our construction, we derive the first non-trivial time-memory trade-offs for Information Set Decoding (ISD) algorithms. Our new algorithms improve on previous (implicit) trade-offs asymptotically as well as practically. Moreover, our optimized implementation also improves onrunning time, due to reduced memory access costs. We demonstrate this by obtaining a new record computation in decoding quasi-cyclic codes (QC-3138). Using our newly obtained data points we then extrapolate the hardness of suggested parameter sets for the NIST PQC fourth round candidates <PERSON><PERSON><PERSON><PERSON><PERSON>, BIKE and HQC, lowering previous estimates by up to 6 bits and further increasing their reliability.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_13"}, {"primary_key": "1135206", "vector": [], "sparse_vector": [], "title": "New Algorithms for the Deuring Correspondence - Towards Practical and Secure SQISign Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Deuring correspondence defines a bijection between isogenies of supersingular elliptic curves and ideals of maximal orders in a quaternion algebra. We present a new algorithm to translate ideals of prime-power norm to their corresponding isogenies — a central task of the effective Deuring correspondence. The new method improves upon the algorithm introduced in 2021 by <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> as a building-block of the SQISign signature scheme. SQISign is the most compact post-quantum signature scheme currently known, but is several orders of magnitude slower than competitors, the main bottleneck of the computation being the ideal-to-isogeny translation. We implement the new algorithm and apply it to SQISign, achieving a more than two-fold speedup in key generation and signing with a new choice of parameter. Moreover, after adapting the state-of-the-art\\(\\mathbb {F}_{p^2}\\)multiplication algorithms by <PERSON><PERSON> to implement SQISign’s underlying extension field arithmetic and adding various improvements, we push the total speedups to over three times for signing and four times for verification. In a second part of the article, we advance cryptanalysis by showing a very simple distinguisher against one of the assumptions used in SQISign. We present a way to impede the distinguisher through a few changes to the generic KLPT algorithm. We formulate a new assumption capturing these changes, and provide an analysis together with experimental evidence for its validity.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_23"}, {"primary_key": "1135207", "vector": [], "sparse_vector": [], "title": "Maliciously-Secure MrNISC in the Plain Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study strong versions of round-optimal MPC. A recent work of <PERSON><PERSON><PERSON><PERSON> and <PERSON> (TCC ’20) identified a version of secure multiparty computation (MPC), termedMultiparty reusable Non-Interactive Secure Computation(MrNISC), that combines at the same time several fundamental aspects of secure computation with standard simulation security into one primitive: round-optimality, succinctness, concurrency, and adaptivity. In more detail, MrNISC is essentially a two-round MPC protocol where the first round of messages serves as a reusable commitment to the private inputs of participating parties. Using these commitments, any subset of parties can later compute any function of their choice on their respective inputs by broadcasting one message each. Anyone who sees these parties’ commitments and evaluation messages (even an outside observer) can learn the function output and nothing else. Importantly, the input commitments can be computed without knowing anything about other participating parties (neither their identities nor their number) and they are reusable across any number of computations. By now, there are several known MrNISC protocols from either (bilinear) group-based assumptions or from LWE. They all satisfy semi-malicious security (in the plain model) and require trusted setup assumptions in order to get malicious security. We are interested inmaliciouslysecure MrNISC protocolsin the plain model, without trusted setup. Since the standard notion of polynomial simulation is un-achievable in less than four rounds, we focus on security withsuper-polynomial-time simulation (SPS). Our main result is the first maliciously secure SPS MrNISC in the plain model. The result is obtained by generically compiling any semi-malicious MrNISC and the security of our compiler relies on several well-studied assumptions of an indistinguishability obfuscator, DDH over\\({\\mathbb Z} ^*_{p}\\)and asymmetric pairing groups, and a time-lock puzzle (all of which need to be sub-exponentially hard). As a special case, we obtain the first 2-round maliciously secure SPS MPC based on well-founded assumptions. This MPC is also concurrently self-composable and its first message is short (i.e., its size is independent of the number of the participating parties) and reusable throughout any number of computations. Prior to our work, for two round maliciously secure MPC, neither concurrent MPC nor reusable MPC nor MPC with first message independent in the number of parties was known from any set of assumptions. Of independent interest is one of our building blocks: the first construction of a one-round non-malleable commitment scheme from well-studied assumptions, avoiding keyless hash functions and non-standard hardness amplification assumptions. The full version of this paper can be found at [26].", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_4"}, {"primary_key": "1135208", "vector": [], "sparse_vector": [], "title": "How to Compress Encrypted Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the task of obliviously compressing a vector comprised ofnciphertexts of size\\(\\xi \\)bits each, where at mosttof the corresponding plaintexts are non-zero. This problem commonly features in applications involving encrypted outsourced storages, such as searchable encryption or oblivious message retrieval. We present two new algorithms with provable worst-case guarantees, solving this problem by using only homomorphic additions and multiplications by constants. Both of our new constructions improve upon the state of the art asymptotically and concretely. Our first construction, based on sparse polynomials, is perfectly correct and the first to achieve an asymptotically optimal compression rate by compressing the input vector into\\(\\mathcal {O}({t \\xi })\\)bits. Compression can be performed homomorphically by performing\\(\\mathcal {O}({n \\log n})\\)homomorphic additions and multiplications by constants. The main drawback of this construction is a decoding complexity of\\(\\varOmega (\\sqrt{n})\\). Our second construction is based on a novel variant of invertible bloom lookup tables and is correct with probability\\(1-2^{-\\kappa }\\). It has a slightly worse compression rate compared to our first construction as it compresses the input vector into\\(\\mathcal {O}({\\xi \\kappa t /\\log t})\\)bits, where\\(\\kappa \\ge \\log t\\). In exchange, both compression and decompression of this construction are highly efficient. The compression complexity is dominated by\\(\\mathcal {O}({n \\kappa /\\log t})\\)homomorphic additions and multiplications by constants. The decompression complexity is dominated by\\(\\mathcal {O}({\\kappa t /\\log t})\\)decryption operations and equally many inversions of a pseudorandom permutation.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_19"}, {"primary_key": "1135209", "vector": [], "sparse_vector": [], "title": "M-SIDH and MD-SIDH: Countering SIDH Attacks by Masking Information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The SIDH protocol is an isogeny-based key exchange protocol using supersingular isogenies, designed by <PERSON><PERSON> and <PERSON> in 2011. The protocol underlies the SIKE algorithm which advanced to the fourth round of NIST’s post-quantum standardization project in May 2022. The algorithm was considered very promising: indeed the most significant attacks against SIDH were meet-in-the-middle variants with exponential complexity, and torsion point attacks which only applied to unbalanced parameters (and in particular, not to SIKE). This security picture dramatically changed in August 2022 with new attacks by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>. Like prior attacks on unbalanced versions, these new attacks exploit torsion point information provided in the SIDH protocol. Crucially however, the new attacks embed the isogeny problem into a similar isogeny problem in a higher dimension to also affect the balanced parameters. As a result of these works, the SIKE algorithm is now fully broken both in theory and in practice. Given the considerable interest attracted by SIKE and related protocols in recent years, it is natural to seek countermeasures to the new attacks. In this paper, we introduce two such countermeasures based on partially hiding the isogeny degrees and torsion point information in the SIDH protocol. We present a preliminary analysis of the resulting schemes including non-trivial generalizations of prior attacks. Based on this analysis we suggest parameters for our M-SIDH variant with public key sizes of 4434, 7037 and 9750 bytes respectively for NIST security levels 1, 3, 5.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_10"}, {"primary_key": "1135210", "vector": [], "sparse_vector": [], "title": "Multi-key and Multi-input Predicate Encryption from Learning with Errors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We put forward two natural generalizations of predicate encryption (PE), dubbedmulti-keyandmulti-inputPE. More in details, our contributions are threefold. Definitions.We formalize security of multi-key PE and multi-input PE following the standard indistinguishability paradigm, and modeling security both against malicious senders (i.e., corruption of encryption keys) and malicious receivers (i.e., collusions). Constructions.We construct adaptively secure multi-key and multi-input PE supporting the conjunction of poly-many arbitrary single-input predicates, assuming the sub-exponential hardness of the learning with errors (LWE) problem. Applications.We show that multi-key and multi-input PE for expressive enough predicates suffices for interesting cryptographic applications, including non-interactive multi-party computation (NI-MPC) and matchmaking encryption (ME). In particular, plugging in our constructions of multi-key and multi-input PE, under the sub-exponential LWE assumption, we obtain the first ME supportingarbitrary policieswith unbounded collusions, as well as robust (resp. non-robust) NI-MPC for so-calledall-or-nothingfunctions satisfying a non-trivial notion of reusability and supporting a constant (resp. polynomial) number of parties. Prior to our work, both of these applications required much heavier tools such as indistinguishability obfuscation or compact functional encryption.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_19"}, {"primary_key": "1135211", "vector": [], "sparse_vector": [], "title": "Optimal Security for Keyed Hash Functions: Avoiding Time-Space Tradeoffs for Finding Collisions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptographic hash functions map data of arbitrary size to a fixed size digest, and are one of the most commonly used cryptographic objects. As it is infeasible to design an individual hash function for every input size, variable-input length hash functions are built by designing and bootstrapping a single fixed-input length function that looks sufficiently random. To prevent trivial preprocessing attacks, applications often require not just a single hash function but rather a family of keyed hash functions. The most well-known methods for designing variable-input length hash function families from a fixed idealized function are the Merkle-Damgård and Sponge designs. The former underlies the SHA-1 and SHA-2 constructions and the latter underlies SHA-3. Unfortunately, recent works (<PERSON><PERSON> et al. EUROCRYPT 2018, <PERSON><PERSON> et al. CRYPTO 2018) show non-trivial time-space tradeoff attacks for finding collisions for both. Thus, this forces a parameter blowup (i.e., efficiency loss) for reaching a certain desired level of security. We ask whether it is possible to build families of keyed hash functions which areprovablyresistant to any non-trivial time-space tradeoff attacks for finding collisions, without incurring significant efficiency costs. We present several new constructions of keyed hash functions that are provably resistant to any non-trivial time-space tradeoff attacks for finding collisions. Our constructions provide various tradeoffs between their efficiency and the range of parameters where they achieve optimal security for collision resistance. Our main technical contribution is proving optimal security bounds for converting a hash function with a fixed-sized input to a keyed hash function with (potentially larger) fixed-size input. We then use this keyed function as the underlying primitive inside the standard Merkle-Damgård and Merkle tree constructions. We strongly believe that this paradigm of using a keyed inner hash function in these constructions is the right one, for which non-uniform security has not been analyzed prior to this work.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_15"}, {"primary_key": "1135212", "vector": [], "sparse_vector": [], "title": "Witness-Succinct Universally-Composable SNARKs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero-knowledge Succinct Non-interactive ARguments of Knowledge (zkSNARKs) are becoming an increasingly fundamental tool in many real-world applications where the proof compactness is of the utmost importance, including blockchains. A proof of security for SNARKs in the Universal Composability (UC) framework (<PERSON>etti, FOCS’01) would rule out devastating malleability attacks. To retain security of SNARKs in the UC model, one must show theirsimulation-extractabilitysuch that the knowledge extractor is bothblack-boxandstraight-line, which would imply that proofs generated by honest provers arenon-malleable. However, existing simulation-extractability results on SNARKs either lack some of these properties, or alternatively have to sacrificewitness succinctnessto prove UC security. In this paper, we provide a compiler lifting any simulation-extractable NIZKAoK into a UC-secure one in the global random oracle model, importantly, while preserving the same level of witness succinctness. Combining this with existing zkSNARKs, we achieve, to the best of our knowledge, the first zkSNARKs simultaneously achieving UC-security and constant sized proofs.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_11"}, {"primary_key": "1135213", "vector": [], "sparse_vector": [], "title": "On Non-uniform Security for Black-Box Non-interactive CCA Commitments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We obtain a black-box construction of non-interactive CCA commitments against non-uniform adversaries. This makes black-box use of an appropriate base commitment scheme for small tag spaces, variants of sub-exponential hinting PRG (<PERSON><PERSON><PERSON> and Waters, Crypto 2019) and variants of keyless sub-exponentially collision-resistant hash function with security against non-uniform adversaries (<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>eth, STOC 2018 and <PERSON><PERSON><PERSON> and <PERSON>, TCC 2018). All prior works on non-interactive non-malleable or CCA commitments without setup first construct a “base” scheme for a relatively small identity/tag space, and then build a tag amplification compiler to obtain commitments for an exponential-sized space of identities. Prior black-box constructions either add multiple rounds of interaction (<PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, FOCS 2012) or only achieve security against uniform adversaries (<PERSON><PERSON>g, <PERSON><PERSON><PERSON>, Lu and Waters, Eurocrypt 2021). Our key technical contribution is a novel tag amplification compiler for CCA commitments that replaces the non-interactive proof of consistency required in prior work. Our construction satisfies the strongest known definition of non-malleability, i.e., CCA2 (chosen commitment attack) security. In addition to only making black-box use of the base scheme, our construction replaces sub-exponential NIWIs with sub-exponential hinting PRGs, which can be obtained based on assumptions such as (sub-exponential) CDH or LWE.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_7"}, {"primary_key": "1135214", "vector": [], "sparse_vector": [], "title": "On Polynomial Functions Mo<PERSON><PERSON> pe and <PERSON><PERSON> Bootstrapping for Homomorphic Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we perform a systematic study of functions\\(f: \\mathbb {Z}_{p^e} \\rightarrow \\mathbb {Z}_{p^e}\\)and categorize those functions that can be represented by a polynomial with integer coefficients. More specifically, we cover the following properties: necessary and sufficient conditions for the existence of an integer polynomial representation; computation of such a representation; and the complete set of equivalent polynomials that represent a given function. As an application, we use the newly developed theory to speed up bootstrapping for the BGV and BFV homomorphic encryption schemes. The crucial ingredient underlying our improvements is the existence of null polynomials, i.e. non-zero polynomials that evaluate to zero in every point. We exploit the rich algebraic structure of these null polynomials to find better representations of the digit extraction function, which is the main bottleneck in bootstrapping. As such, we obtain sparse polynomials that have 50% fewer coefficients than the original ones. In addition, we propose a new method to decompose digit extraction as a series of polynomial evaluations. This lowers the time complexity from\\(\\mathcal {O}(\\sqrt{pe})\\)to\\(\\mathcal {O}(\\sqrt{p}\\root ^4 \\of {e})\\)for digit extraction modulo\\(p^e\\), at the cost of a slight increase in multiplicative depth. Overall, our implementation inHElibshows a significant speedup of a factor up to 2.6 over the state-of-the-art.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_9"}, {"primary_key": "1135215", "vector": [], "sparse_vector": [], "title": "Generic Attack on Duplex-Based AEAD Modes Using Random Function Statistics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Duplex-based authenticated encryption modes with a sufficiently large key length are proven to be secure up to the birthday bound\\(2^{\\frac{c}{2}}\\), wherecis the capacity. However this bound is not known to be tight and the complexity of the best known generic attack, which is based on multicollisions, is much larger: it reaches\\(\\frac{2^c}{\\alpha }\\)where\\(\\alpha \\)represents a small security loss factor. There is thus an uncertainty on the true extent of security beyond the bound\\(2^{\\frac{c}{2}}\\)provided by such constructions. In this paper, we describe a new generic attack against several duplex-based AEAD modes. Our attack leverages random functions statistics and produces a forgery in time complexity\\(\\mathcal {O}(2^{\\frac{3c}{4}})\\)using negligible memory and no encryption queries. Furthermore, for some duplex-based modes, our attack recovers the secret key with a negligible amount of additional computations. Most notably, our attack breaks a security claim made by the designers of the NIST lightweight competition candidateXoodyak. This attack is a step further towards determining the exact security provided by duplex-based constructions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_12"}, {"primary_key": "1135216", "vector": [], "sparse_vector": [], "title": "Speed-Stacking: Fast Sublinear Zero-Knowledge Proofs for Disjunctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Building on recentcompilersfor efficient disjunctive composition (e.g.an OR of multiple clauses) of zero-knowledge proofs (e.g.<PERSON><PERSON><PERSON>.[EUROCRYPT’22]) we propose a new compiler that, when applied to sublinear-sized proofs, can result in sublinear-size disjunctive zero-knowledge withsublinearproving times (without meaningfully increasing proof sizes). Our key observation is that simulation in sublinear-sizezero-knowledgeproof systems can be much faster (both concretely and asymptotically) than the honest prover. We study applying our compiler to two classes of\\(O(\\log n)\\)-round protocols: interactive oracle proofs, specifically Aurora [EUROCRYPT’19] and Fractal [EUROCRYPT’20], and folding arguments, specifically Compressed\\(\\varSigma \\)-protocols [CR-YPTO’20, CRYPTO’21] and Bulletproofs [S &P’18]. This study validates that the compiler can lead to significant savings. For example, applying our compiler to Fractal enables us to prove a disjunction of\\(\\ell \\)clauses, each of sizeN, with only\\(O((N+\\ell ) \\cdot \\text {polylog}(N))\\)computation, versus\\(O(\\ell N \\cdot \\text {polylog}(N))\\)when proving the disjunction directly. We also find that our compiler offers a new lens through which to understand zero-knowledge proofs, evidenced by multiple examples of protocols with the same “standalone” complexity that each behave very differently when stacked.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_12"}, {"primary_key": "1135217", "vector": [], "sparse_vector": [], "title": "Traitor Tracing with N1/3-Size Ciphertexts and O(1)-<PERSON><PERSON> from k-Lin.", "authors": ["Junqing Gong", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present a pairing-based traitor tracing scheme forNusers with This is the first pairing-based scheme to achieve\\(| \\textsf{pk}| \\cdot | \\textsf{sk}| \\cdot | \\textsf{ct}| = o(N)\\). Our construction relies on the (bilateral)k-Lin assumption, and achieves private tracing and full collusion resistance. Our result simultaneously improves upon the sizes of\\( \\textsf{pk}, \\textsf{ct}\\)in Boneh–Sahai–Waters [Eurocrypt ’06] and the size of\\( \\textsf{sk}\\)in Zhandry [Crypto ’20], while further eliminating the reliance on the generic group model in the latter work.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_21"}, {"primary_key": "1135218", "vector": [], "sparse_vector": [], "title": "From Farfalle to Megafono via Ciminion: The PRF Hydra for MPC Applications.", "authors": ["<PERSON>", "Morten Øygarden", "<PERSON>", "<PERSON> Walch"], "summary": "The area of multi-party computation (MPC) has recently increased in popularity and number of use cases. At the current state of the art, Ciminion, a Farfalle-like cryptographic function, achieves the best performance in MPC applications involving symmetric primitives. However, it has a critical weakness. Its security highly relies on the independence of its subkeys, which is achieved by using an expensive key schedule. Many MPC use cases involving symmetric pseudo-random functions (PRFs) rely on secretly shared symmetric keys, and hence the expensive key schedule must also be computed in MPC. As a result, Ciminion’s performance is significantly reduced in these use cases. In this paper we solve this problem. Following the approach introduced by Ciminion’s designers, we present a novel primitive in symmetric cryptography calledMegafono.Megafonois a keyed extendable PRF, expanding a fixed-length input to an arbitrary-length output. Similar to Farfalle, an initial keyed permutation is applied to the input, followed by an expansion layer, involving the parallel application of keyed ciphers. The main novelty regards the expansion of the intermediate/internal state for “free\", by appending the sum of the internal states of the first permutation to its output. The combination of this and other modifications, together with the impossibility for the attacker to have access to the input state of the expansion layer, makeMegafonovery efficient in the target application. As a concrete example, we present the PRFHydra, an instance ofMegafonobased on theHadesstrategy and on generalized versions of the Lai–Massey scheme. Based on an extensive security analysis, we implementHydrain an MPC framework. The results show that it outperforms all MPC-friendly schemes currently published in the literature.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_9"}, {"primary_key": "1135219", "vector": [], "sparse_vector": [], "title": "Impossibility of Indifferentiable Iterated Blockciphers from 3 or Less Primitive Calls.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Dongdai Lin"], "summary": "Virtually all modern blockciphers areiterated. In this paper, we ask: to construct a secure iterated blockcipher “non-trivially”, how many calls to random functions and permutations are necessary? When security meansindistinguishability from a random permutation, optimality is achieved by the Even-Mansour scheme using 1 call to a public permutation. We seek for the arguably strongest securityindifferentiability from an ideal cipher, a notion introduced by <PERSON> et al. (TCC 2004) and popularized by <PERSON><PERSON> et al. (JoC, 2014). We provide the first generic negative result/lower bounds: when the key is not too short, no iterated blockcipher making 3 calls is (statistically) indifferentiable. This proves optimality for a 4-call positive result of <PERSON> et al. (Eprint 2016). Furthermore, using 1 or 2 calls, even indifferentiable iterated blockciphers with polynomial\\(\\text {keyspace}\\)are impossible. To prove this, we develop an abstraction of idealized iterated blockciphers and establish various basic properties, and apply Extremal Graph Theory results to prove the existence of certain (generalized) non-random properties such as the boomerang and yoyo.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_14"}, {"primary_key": "1135220", "vector": [], "sparse_vector": [], "title": "Half-Tree: Halving the Cost of Tree Expansion in COT and DPF.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GGM tree is widely used in the design of correlated oblivious transfer (COT), subfield vector oblivious linear evaluation (sVOLE), distributed point function (DPF), and distributed comparison function (DCF). Often, the cost associated with GGM tree dominates the computation and communication of these protocols. In this paper, we propose a suite of optimizations that can reduce this cost by half. Halving the cost of COT and sVOLE.Our COT protocol introduces extra correlation to each level of a GGM tree used by the state-of-the-art COT protocol. As a result, it reduces both the number of AES calls and the communication by half. Extending this idea to sVOLE, we are able to achieve similar improvement with either halved computation or halved communication. Halving the cost of DPF and DCF.We propose improved two-party protocols for the distributed generation of DPF/DCF keys. Our tree structures behind these protocols lead to more efficient full-domain evaluation and halve the communication and the round complexity of the state-of-the-art DPF/DCF protocols. All protocols are provably secure in the random-permutation model and can be accelerated based on fixed-key AES-NI. We also improve the state-of-the-art schemes of puncturable pseudorandom function (PPRF), DPF, and DCF, which are of independent interest in dealer-available scenarios.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_12"}, {"primary_key": "1135221", "vector": [], "sparse_vector": [], "title": "Finding the Impossible: Automated Search for Full Impossible-Differential, Zero-Correlation, and Integral Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Impossible differential (ID), zero-correlation (ZC), and integral attacks are a family of important attacks on block ciphers. For example, the impossible differential attack was the first cryptanalytic attack on 7 rounds of AES. Evaluating the security of block ciphers against these attacks is very important but also challenging: Finding these attacks usually implies a combinatorial optimization problem involving many parameters and constraints that is very hard to solve using manual approaches. Automated solvers, such as Constraint Programming (CP) solvers, can help the cryptanalyst to find suitable attacks. However, previous CP-based methods focus on finding only the ID, ZC, and integral distinguishers, often only in a limited search space. Notably, none can be extended to a unified optimization problem for finding full attacks, including efficient key-recovery steps. In this paper, we present a new CP-based method to search for ID, ZC, and integral distinguishers and extend it to a unified constraint optimization problem for finding full ID, ZC, and integral attacks. To show the effectiveness and usefulness of our method, we applied it to several block ciphers, includingSKINNY,CRAFT,SKINNYe-v2, andSKINNYee. For the ISO standard block cipherSKINNY, we significantly improve all existing ID, ZC, and integral attacks. In particular, we improve the integral attacks onSKINNY-n-3nandSKINNY-n-2nby 3 and 2 rounds, respectively, obtaining the best cryptanalytic results on these variants in the single-key setting. We improve the ZC attack onSKINNY-n-n(SKINNY-n-2n) by 2 (resp. 1) rounds. We also improve the ID attacks on all variants ofSKINNY. Particularly, we improve the time complexity of the best previous single-tweakey (related-tweakey) ID attack onSKINNY-128-256 (resp.SKINNY-128-384) by a factor of\\(2^{22.57}\\)(resp.\\(2^{15.39}\\)). OnCRAFT, we propose a 21-round (20-round) ID (resp. ZC) attack, which improves the best previous single-tweakey attack by 2 (resp. 1) rounds. Using our new model, we also provide several practical integral distinguishers for reduced-roundSKINNY,CRAFT, andDeoxys-BC. Our method is generic and applicable to other strongly aligned block ciphers.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_5"}, {"primary_key": "1135222", "vector": [], "sparse_vector": [], "title": "On Valiant&apos;s Conjecture - Impossibility of Incrementally Verifiable Computation from Random Oracles.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In his landmark paper at TCC 2008 <PERSON> introduced the notion of “incrementally verifiable computation” which enables a prover to incrementally compute a succinct proof of correct execution of a (potentially) long running process. The paper later won the 2019 TCC test of time award. The construction was proven secure in the random oracle model without any further computational assumptions. However, the overall proof was given using a non-standard version of the random-oracle methodology where sometimes the hash function is a random oracle and sometimes it has a short description as a circuit. <PERSON><PERSON> clearly noted that this model is non-standard, but conjectured that the standard random oracle methodology would not suffice. This conjecture has been open for 14 years. We prove that if the proof system can receive a long witness as input in an incremental manner and is also zero-knowledge then the conjecture is true. <PERSON><PERSON>’s original construction does not have these properties but can easily be extended to have them in his model. We relate our result to recent possibility and impossibility results for SNARKs and incrementally verifiable computation.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_15"}, {"primary_key": "1135223", "vector": [], "sparse_vector": [], "title": "Almost Tight Multi-user Security Under Adaptive Corruptions &amp; Leakages in the Standard Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider tight multi-user security under adaptive corruptions, where the adversary can adaptively corrupt some users and obtain their secret keys. We propose generic constructions for a bunch of primitives, and the instantiations from the matrix decisional Di<PERSON><PERSON>-<PERSON> (MDDH) assumptions yield the following schemes: the first digital signature (SIG) scheme achieving almost tightstrongEUF-CMA security in the multi-user setting with adaptive corruptions in the standard model; the first public-key encryption (PKE) scheme achieving almost tight IND-CCA security in the multi-user multi-challenge setting with adaptive corruptions in the standard model; the first signcryption (SC) scheme achieving almost tight privacy and authenticity under CCA attacks in the multi-user multi-challenge setting with adaptive corruptions in the standard model. As byproducts, our SIG and SC naturally derive the first strongly secure message authentication code (MAC) and the first authenticated encryption (AE) schemes achieving almost tight multi-user security under adaptive corruptions in the standard model. We further optimize constructions of SC, MAC and AE to admit better efficiency. Furthermore, we consider key leakages besides corruptions, as a natural strengthening of tight multi-user security under adaptive corruptions. This security considers a more natural and more complete “all-or-part-or-nothing” setting, where secret keys of users are either fully exposed to adversary (“all”), or completely hidden to adversary (“nothing”), orpartiallyleaked to adversary (“part”), and it protects the uncorrupted users even with bounded key leakages. All our schemes additionally support bounded key leakages and enjoy full compactness. This yields the first SIG, PKE, SC, MAC, AE schemes achieving almost tight multi-user security under both adaptive corruptions and leakages.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_5"}, {"primary_key": "1135224", "vector": [], "sparse_vector": [], "title": "Non-interactive Blind Signatures for Random Messages.", "authors": ["<PERSON><PERSON>"], "summary": "Blind signatures allow a signer to issue signatures on messages chosen by the signature recipient. The main property is that the recipient’s message is hidden from the signer. There are many applications, including <PERSON><PERSON>’s e-cash system and Privacy Pass, where no special distribution of the signed message is required, and the message can be random. Interestingly, existing notions do not consider this practical use case separately. In this paper, we show that constraining the recipient’s choice over the message distribution spawns a surprising new primitive that improves the well-established state-of-the-art. We formalize this concept by introducing the notion of non-interactive blind signatures (\\(\\textsf{NIBS}\\)). Informally, the signer can create a presignature with a specific recipient in mind, identifiable via a public key. The recipient can use her secret key to finalize it and receive a blind signature on a random message determined by the finalization process. The key idea is that online interaction between the signer and recipient is unnecessary. We show an efficient instantiation of\\(\\textsf{NIBS}\\)in the random oracle model from signatures on equivalence classes. The exciting part is that, in this case, for the recipient’s public key, we can use preexisting keys for Schnorr, ECDSA signatures, El-Gamal encryption scheme or even the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> key exchange. Reusing preexisting public keys allows us to distribute anonymous tokens similarly to cryptocurrency airdropping. Additional contributions include tagged non-interactive blind signatures (\\(\\textsf{TNIBS}\\)) and their efficient instantiation. A generic construction in the random oracle or common reference string model based on verifiable random functions, standard signatures, and non-interactive proof systems.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_25"}, {"primary_key": "1135225", "vector": [], "sparse_vector": [], "title": "Rai-Choo! Evolving Blind Signatures to the Next Level.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Blind signatures are a fundamental tool for privacy-preserving applications. Known constructions of concurrently secure blind signature schemes either are prohibitively inefficient or rely on non-standard assumptions, even in the random oracle model. A recent line of work (ASIACRYPT ‘21, CRYPTO ‘22) initiated the study of concretely efficient schemes based on well-understood assumptions in the random oracle model. However, these schemes still have several major drawbacks: 1) The signer is required to keep state; 2) The computation grows linearly with the number of signing interactions, making the schemes impractical; 3) The schemes require at least five moves of interaction. In this paper, we introduce a blind signature scheme that eliminate<PERSON>lof the above drawbacks at the same time. Namely, we show a round-optimal, concretely efficient, concurrently secure, and stateless blind signature scheme in which communication and computation are independent of the number of signing interactions. Our construction also naturally generalizes to the partially blind signature setting. Our scheme is based on the\\(\\textsf{CDH}\\)assumption in the asymmetric pairing setting and can be instantiated using a standard BLS curve. We obtain signature and communication sizes of 9 KB and 36 KB, respectively. To further improve the efficiency of our scheme, we show how to obtain a scheme with better amortized communication efficiency. Our approachbatchesthe issuing of signatures for multiple messages.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_26"}, {"primary_key": "1135226", "vector": [], "sparse_vector": [], "title": "Password-Authenticated TLS via OPAQUE and Post-Handshake Authentication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "OPAQUE is an Asymmetric Password-Authenticated Key Exchange (aPAKE) protocol being standardized by the IETF (Internet Engineering Task Force) as a more secure alternative to the traditional “password-over-TLS” mechanism prevalent in current practice. OPAQUE defends against a variety of vulnerabilities of password-over-TLS by dispensing with reliance on PKI and TLS security, and ensuring that the password is never visible to servers or anyone other than the client machine where the password is entered. In order to facilitate the use of OPAQUE in practice, integration of OPAQUE with TLS is needed. The main proposal for standardizing such integration uses the Exported Authenticators (TLS-EA) mechanism of TLS 1.3 that supports post-handshake authentication and allows for a smooth composition with OPAQUE. We refer to this composition as TLS-OPAQUE and present a detailed security analysis for it in the Universal Composability (UC) framework. Our treatment is general and includes the formalization of components that are needed in the analysis of TLS-OPAQUE but are of wider applicability as they are used in many protocols in practice. Specifically, we provide formalizations in the UC model of the notions of post-handshake authentication and channel binding. The latter, in particular, has been hard to implement securely in practice, resulting in multiple protocol failures, including major attacks against prior versions of TLS. Ours is the first treatment of these notions in a computational model with composability guarantees. We complement the theoretical work with a detailed discussion of practical considerations for the use and deployment of TLS-OPAQUE in real-world settings and applications.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_4"}, {"primary_key": "1135227", "vector": [], "sparse_vector": [], "title": "From the Hardness of Detecting Superpositions to Cryptography: Quantum Public Key Encryption and Commitments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON> et al. (arXiv:2009.07450) showed that detecting interference between two orthogonal states is as hard as swapping these states. While their original motivation was from quantum gravity, we show its applications in quantum cryptography. We construct the first public key encryption scheme from cryptographicnon-abeliangroup actions. Interestingly, the ciphertexts of our scheme are quantum even if messages are classical. This resolves an open question posed by <PERSON> et al. (TCC ’19). We construct the scheme through a new abstraction called swap-trapdoor function pairs, which may be of independent interest. We give a simple and efficient compiler that converts the flavor of quantum bit commitments. More precisely, for any prefix\\(\\textrm{X},\\textrm{Y}\\in \\{\\text {computationally, statistically, perfectly}\\}\\), if the base scheme is X-hiding and Y-binding, then the resulting scheme is Y-hiding and X-binding. Our compiler calls the base scheme only once. Previously, all known compilers call the base schemes polynomially many times (<PERSON><PERSON><PERSON> et al., Eurocrypt ’01 and Yan, Asiacrypt ’22). For the security proof of the conversion, we generalize the result of <PERSON><PERSON> et al. by considering quantum auxiliary inputs.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_22"}, {"primary_key": "1135228", "vector": [], "sparse_vector": [], "title": "Registered Attribute-Based Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Attribute-based encryption (ABE) generalizes public-key encryption and enables fine-grained control to encrypted data. However, ABE upends the traditional trust model of public-key encryption by requiring a singletrustedauthority to issue decryption keys. If an adversary compromises the central authority and exfiltrates its secret key, then the adversary can decrypteveryciphertext in the system. This work introduces registered ABE, a primitive that allows users to generate secret keys on their own and then register the associated public key with a “key curator” along with their attributes. The key curator aggregates the public keys from the different users into a singlecompactmaster public key. To decrypt, users occasionally need to obtain helper decryption keys from the key curator which they combine with their own secret keys. We require that the size of the aggregated public key, the helper decryption keys, the ciphertexts, as well as the encryption/decryption times to be polylogarithmic in the number of registered users. Moreover, the key curator is entirelytransparentand maintains no secrets. Registered ABE generalizes the notion of registration-based encryption (RBE) introduced by <PERSON><PERSON><PERSON> et al. (TCC 2018), who focused on the simpler setting of identity-based encryption. We construct a registered ABE scheme that supports an a priori bounded number of users and policies that can be described by a linear secret sharing scheme (e.g., monotone Boolean formulas) from assumptions on composite-order pairing groups. Our approach deviates sharply from previous techniques for constructing RBE and only makesblack-boxuse of cryptography. All existing RBE constructions (a weaker notion than registered ABE) rely on heavy non-black-box techniques. The encryption and decryption costs of our construction are comparable to those of vanilla pairing-based ABE. Two limitations of our scheme are that it requires a structured reference string whose size scales quadratically with the number of users (and linearly with the size of the attribute universe) and the running time of registration scales linearly with the number of users. Finally, as a feasibility result, we construct a registered ABE scheme that supports general policies and an arbitrary number of users from indistinguishability obfuscation and somewhere statistically binding hash functions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_17"}, {"primary_key": "1135229", "vector": [], "sparse_vector": [], "title": "Black-Box Reusable NISC with Random Oracles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We revisit the problem ofreusablenon-interactive secure computation (NISC). A standard NISC protocol for a sender-receiver functionalityfenables the receiver to encrypt its inputxsuch that any sender, on inputy, can send back a message revealing onlyf(x,y). Security should hold even when either party can be malicious. AreusableNISC protocol has the additional feature that the receiver’s message can be safely reused for computing multiple outputs\\(f(x,y_i)\\). Here security should hold even when a malicious sender can learn partial information about the honest receiver’s outputs in each session. We present the first reusable NISC protocol for general functionsfthat only makes ablack-boxuse of any two-message oblivious transfer protocol, along with a random oracle. All previous reusable NISC protocols either made a non-black-box use of cryptographic primitives (<PERSON><PERSON><PERSON> et al. ICALP 2002) or alternatively required a stronger arithmetic variant of oblivious transfer and were restricted tofin\\(\\textsf{NC}^1\\)or similar classes (Chase et al. Crypto 2019). Our result is obtained via a general compiler from standard NISC to reusable NISC that makes use of special type of honest-majority protocols for secure multiparty computation. Finally, we extend the above main result to reusabletwo-sidedNISC, in which two parties can encrypt their inputs in the first round and then reveal different functions of their inputs in multiple sessions. This extension either requires an additional (black-box) use of additively homomorphic commitment or alternatively requires the parties to maintain a state between sessions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_3"}, {"primary_key": "1135230", "vector": [], "sparse_vector": [], "title": "Let Attackers Program Ideal Models: Modularity and Composability for Adaptive Compromise.", "authors": ["<PERSON>"], "summary": "We show that the adaptive compromise security definitions of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Crypto ’20) cannot be applied in several natural use-cases. These include proving multi-user security from single-user security, the security of the cascade PRF, and the security of schemes sharing the same ideal primitive. We provide new variants of the definitions and show that they resolve these issues with composition. Extending these definitions to the asymmetric settings, we establish the security of the modular KEM/DEM and Fujisaki-Okamoto approaches to public key encryption in the full adaptive compromise setting. This allows instantiations which are more efficient and standard than prior constructions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_4"}, {"primary_key": "1135231", "vector": [], "sparse_vector": [], "title": "On the Optimal Succinctness and Efficiency of Functional Encryption and Attribute-Based Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the best-possible (asymptotic) efficiency of functional encryption (FE) and attribute-based encryption (ABE) by proving inherent space-time trade-offs and constructing nearly optimal schemes. We consider the general notion of partially hiding functional encryption (PHFE), capturing both FE and ABE, and the most efficient computation model of random-access machine (RAM). In PHFE, a secret key\\(\\textsf{sk}_f\\)is associated with a functionf, whereas a ciphertext\\(\\textsf{ct}_x(y)\\)is tied to a public inputxand encrypts a private inputy. Decryption revealsf(x,y) and nothing else abouty. We present the first PHFE for RAM solely based on the necessary assumption of FE for circuits. Significantly improving upon the efficiency of prior schemes, our construction achieves nearly optimal succinctness and computation time: Its secret key\\(\\textsf{sk}_f\\)is ofconstant size(optimal), independent of the function description length |f|, i.e.,\\({|\\textsf{sk}_f|={\\text {poly}}(\\lambda )}\\). Its ciphertext\\(\\textsf{ct}_x(y)\\)israte-2in the private input length |y| (nearly optimal) andindependentof the public input length |x| (optimal), i.e.,\\({|\\textsf{ct}_x(y)|=2|y|+{\\text {poly}}(\\lambda )}\\). Decryption time islinearin theinstancerunning timeTof the RAM computation, plus the function and public/private input lengths, i.e.,\\({T_\\textsf{Dec}=(T+|f|+|x|+|y|){\\text {poly}}(\\lambda )}\\). As a corollary, we obtain the first ABE with both keys and ciphertexts being constant-size, while enjoying the best-possible decryption time matching the lower bound by Luo [ePrint ’22]. We also separately achieve several other optimal ABE subject to the known lower bound. We study the barriers to further efficiency improvements. We prove the first unconditional space-time trade-offs for (PH-)FE: Nosecure (PH-)FE can have\\(|\\textsf{sk}_f|\\)and\\(T_\\textsf{Dec}\\)bothsublinear in |f|. Nosecure PHFE can have\\(|\\textsf{ct}_x(y)|\\)and\\(T_\\textsf{Dec}\\)bothsublinear in |x|. Our lower bounds apply even to the weakest secret-key 1-key 1-ciphertext selective schemes. Furthermore, we show a conditional barrier towards the optimal decryption time\\({T_\\textsf{Dec}=T{\\text {poly}}(\\lambda )}\\)while keeping linear size dependency — any such (PH-)FE scheme implies doubly efficient private information retrieval (DE-PIR) with linear-size preprocessed database, for which so far there is no candidate.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_16"}, {"primary_key": "1135232", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Cryptanalysis of the Subspace Flooding Assumption for Post-quantum i풪.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Indistinguishability Obfuscation\\((i\\mathcal {O})\\)is a highly versatile primitive implying a myriad advanced cryptographic applications. Up until recently, the state of feasibility of\\(i\\mathcal {O}\\)was unclear, which changed with works (Jain-<PERSON>-<PERSON> STOC 2021, Jain-<PERSON>-<PERSON>hai Eurocrypt 2022) showing that\\(i\\mathcal {O}\\)can be finally based upon well-studied hardness assumptions. Unfortunately, one of these assumptions is broken in quantum polynomial time. Luckily, the line work of <PERSON><PERSON><PERSON><PERSON> et al. Eurocrypt 2020, Gay-Pass STOC 2021, Wichs-Wee Eurocrypt 2021, <PERSON><PERSON><PERSON><PERSON> et al. ePrint 2021, <PERSON><PERSON><PERSON> et al. TCC 2021 simultaneously created new pathways to construct\\(i\\mathcal {O}\\)with plausible post-quantum security from new assumptions, namely a new form of circular security of LWE in the presence of leakages. At the same time, effective cryptanalysis of this line of work has also begun to emerge (<PERSON> et al. Crypto 2021). It is important to identify the simplest possible conjectures that yield post-quantum\\(i\\mathcal {O}\\)and can be understood through known cryptanalytic tools. In that spirit, and in light of the cryptanalysis of <PERSON> et al., recently <PERSON><PERSON><PERSON> et al. gave an elegant construction of\\(i\\mathcal {O}\\)from a fully-specified and simple-to-state assumption along with a thorough initial cryptanalysis. Our work gives a polynomial-time distinguisher on their “final assumption” for their scheme. Our algorithm is extremely simple to describe: Solve a carefully designed linear system arising out of the assumption. The argument of correctness of our algorithm, however, is nontrivial. We also analyze the “T-sum” version of the same assumption described by Devadas et al. and under a reasonable conjecture rule out the assumption for any value ofTthat implies\\(i\\mathcal {O}\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_8"}, {"primary_key": "1135233", "vector": [], "sparse_vector": [], "title": "SNARGs and PPAD Hardness from the Decisional Diffie-Hellman Assumption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We construct succinct non-interactive arguments (SNARGs) for bounded-depth computations assuming that the decisional Diffie-Hellman (DDH) problem is sub-exponentially hard. This is the first construction of such SNARGs from a <PERSON><PERSON><PERSON><PERSON><PERSON> assumption. Our SNARG is alsounambiguous: for every (true) statementx, it is computationally hard to find any accepting proof forxother than the proof produced by the prescribed prover strategy. We obtain our result by showing how to instantiate the Fiat-Shamir heuristic, under DDH, for a variant of the Goldwasser-Kalai-Rothblum (GKR) interactive proof system. Our new technical contributions are (1) giving a\\(TC^0\\)circuit family for finding roots of cubic polynomials over a special family of characteristic-2 fields (<PERSON><PERSON>-Viola, STACS 2006) and (2) constructing a variant of the GKR protocol whose invocations of the sumcheck protocol (Lund-Fortnow-Karl<PERSON>-<PERSON>, STOC 1990) only involve degree 3 polynomials over said fields. Along the way, since we can instantiate the Fiat-Shamir heuristic for certain variants of the sumcheck protocol, we also show the existence of (sub-exponentially) hard problems in the complexity class\\(\\textsf{PPAD}\\), assuming the sub-exponential hardness of DDH. Previous\\(\\textsf{PPAD}\\)hardness results required either bilinear maps or the learning with errors assumption.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_16"}, {"primary_key": "1135234", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Blueprints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "If everyone were to use anonymous credentials for all access control needs, it would be impossible to trace wrongdoers, by design. This would make legitimate controls, such as tracing illicit trade and terror suspects, impossible to carry out. Here, we propose a privacy-preserving blueprint capability that allows an auditor to publish an encoding\\(\\textsf{pk}_{\\textsf{A}}\\)of the function\\(f(x,\\cdot )\\)for a publicly known functionfand a secret inputx. For example,xmay be a secret watchlist, andf(x,y) may returnyif\\(y\\in x\\). On input her datayand the auditor’s\\(\\textsf{pk}_{\\textsf{A}}\\), a user can compute an escrow\\(Z\\)such that anyone can verify that\\(Z\\)was computed correctly from the user’s credential attributes, and moreover, the auditor can recoverf(x,y) from\\(Z\\). Our contributions are: We define securef-blueprint systems; our definition is designed to provide a modular extension to anonymous credential systems. We show that securef-blueprint systems can be constructed for all functionsffrom fully homomorphic encryption and NIZK proof systems. This result is of theoretical interest but is not efficient enough for practical use. We realize an optimal blueprint system under the DDH assumption in the random-oracle model for the watchlist function.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_20"}, {"primary_key": "1135235", "vector": [], "sparse_vector": [], "title": "Asymmetric Group Message Franking: Definitions and Constructions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As online group communication scenarios become more and more common these years, malicious or unpleasant messages are much easier to spread on the internet. Message franking is a crucial cryptographic mechanism designed for content moderation in online end-to-end messaging systems, allowing the receiver of a malicious message to report the message to the moderator. Unfortunately, the existing message franking schemes only consider 1-1 communication scenarios. In this paper, we systematically explore message franking in group communication scenarios. We introduce the notion of asymmetric group message franking (AGMF), and formalize its security requirements. Then, we provide a framework of constructing AGMF from a new primitive, called. We also give a construction ofbased on the DDH assumption. Plugging the concretescheme into our AGMF framework, we obtain a DDH-based AGMF scheme, which supports message franking in group communication scenarios.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_3"}, {"primary_key": "1135236", "vector": [], "sparse_vector": [], "title": "Pitfalls and Shortcomings for Decompositions and Alignment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we, for the first time, study the question under which circumstances decomposing a round function of a Substitution-Permutation Network is possibleuniquely. More precisely, we provide necessary and sufficient criteria for the non-linear layer on when a decomposition is unique. Our results in particular imply that, when cryptographically strong S-boxes are used, the decomposition is indeed unique. We then apply our findings to the notion of alignment, pointing out that the previous definition allows for primitives that are both aligned and unaligned simultaneously. As a second result, we present experimental data that shows that alignment might only have limited impact. For this, we compare aligned and unaligned versions of the cipher PRESENT.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_11"}, {"primary_key": "1135237", "vector": [], "sparse_vector": [], "title": "Efficient FHEW Bootstrapping with Small Evaluation Keys, and Applications to Threshold Homomorphic Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rakyong Choi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There are two competing approaches to bootstrap the FHEW fully homomorphic encryption scheme (Du<PERSON> and Micci<PERSON>, Eurocrypt 2015) and its variants: the original AP/FHEW method, which supports arbitrary secret key distributions, and the improved GINX/TFHE method, which uses much smaller evaluation keys, but is directly applicable only to binary secret keys, restricting the scheme’s applicability. In this paper, we present a new bootstrapping procedure for FHEW-like encryption schemes that achieves the best features of both methods: support for arbitrary secret key distributions at no additional runtime costs, while using small evaluation keys. (Support for arbitrary secret keys is critical in a number of important applications, like threshold and some multi-key homomorphic encryption schemes.) As an added benefit, our new bootstrapping procedure results in smaller noise growth than both AP and GINX, regardless of the key distribution. Our improvements are both theoretically significant (offering asymptotic savings, up to a\\(O(\\log n)\\)multiplicative factor, either on the running time or public evaluation key size), and practically relevant. For example, for a concrete 128-bit target security level, we show how to decrease the evaluation key size of the best previously known scheme by more than 30%, while also slightly reducing the running time. We demonstrate the practicality of the proposed methods by building a prototype implementation within the PALISADE/OpenFHE open-source homomorphic encryption library. We provide optimized parameter sets and implementation results showing that the proposed algorithm has the best performance among all known FHEW bootstrapping methods in terms of runtime and key size. We illustrate the benefits of our method by sketching a simple construction of threshold homomorphic encryption based on FHEW.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_8"}, {"primary_key": "1135238", "vector": [], "sparse_vector": [], "title": "Non-uniformity and Quantum Advice in the Quantum Random Oracle Model.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "QROM (quantum random oracle model), introduced by <PERSON><PERSON> et al. (Asiacrypt 2011), captures all generic algorithms. However, it fails to describe non-uniform quantum algorithms with preprocessing power, which receives a piece of bounded classical or quantum advice. As non-uniform algorithms are largely believed to be the right model for attackers, starting from the work by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (QIC 2015), a line of works investigates non-uniform security in the random oracle model. <PERSON>, <PERSON>, <PERSON>, and <PERSON> (FOCS 2020) provide a framework and establish non-uniform security for many cryptographic applications. Although they achieve nearly optimal bounds for many applications with classical advice, their bounds for quantum advice are far from tight. In this work, we continue the study on quantum advice in the QROM. We provide a new idea that generalizes the previous multi-instance framework, which we believe is more quantum-friendly and should be the quantum analog of multi-instance games. To this end, wematchthe bounds withquantum adviceto those withclassical adviceby <PERSON> et al., showing quantum advice is almost as good/bad as classical advice for many natural security games in the QROM. Finally, we show that for some contrived games in the QROM, quantum advice can be exponentially better than classical advice for some parameter regimes. To our best knowledge, it provides an evidence of a general separation between quantum and classical advice relative to an unstructured oracle.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_5"}, {"primary_key": "1135239", "vector": [], "sparse_vector": [], "title": "Coefficient Grouping: Breaking <PERSON><PERSON>ri and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Libo Wang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose an efficient technique called coefficient grouping to evaluate the algebraic degree of the FHE-friendly cipherChaghri, which has been accepted for ACM CCS 2022. It is found that the algebraic degree increases linearly rather than exponentially. As a consequence, we can construct a 13-round distinguisher with time and data complexity of\\(2^{63}\\)and mount a 13.5-round key-recovery attack. In particular, a higher-order differential attack on 8 rounds ofChaghrican be achieved with time and data complexity of\\(2^{38}\\). Hence, it indicates that the full 8 rounds are far from being secure. Furthermore, we also demonstrate the application of our coefficient grouping technique to the design of secure cryptographic components. As a result, a countermeasure is found forC<PERSON>ghriand it has little overhead compared with the original design. Since more and more symmetric primitives defined over a large finite field are emerging, we believe our new technique can have more applications in the future research.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_10"}, {"primary_key": "1135240", "vector": [], "sparse_vector": [], "title": "Another Round of Breaking and Making Quantum Money: - How to Not Build It from Lattices, and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work provides both negative and positive results for publicly verifiable quantum money. In the first part, we give a general theorem, showing that a certain natural class of quantum money schemes from lattices cannot be secure. We use this theorem to break the recent quantum money proposal of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> ([KLS22]). In the second part, we propose a framework for building quantum money and quantum lightning we callinvariant moneywhich abstracts and formalizes some ideas of quantum money from knots [FGH+12] and its precedent work [LAF+10]. In addition to formalizing this framework, we provide concrete hard computational problems loosely inspired by classical knowledge-of-exponent assumptions, whose hardness would imply the security ofquantum lightning, a strengthening of quantum money where not even the bank can duplicate banknotes. We discuss potential instantiations of our framework, including an oracle construction using cryptographic group actions and instantiations from rerandomizable functional encryption, isogenies over elliptic curves, and knots.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_21"}, {"primary_key": "1135241", "vector": [], "sparse_vector": [], "title": "Batch Bootstrapping I: - A New Framework for SIMD Bootstrapping in Polynomial Modulus.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this series of work, we aim at improving the bootstrapping paradigm for fully homomorphic encryption (FHE). Our main goal is to show that the amortized cost of bootstrapping within a polynomial modulus only requires\\(\\tilde{O}(1)\\)FHE multiplications. To achieve this, we develop substantial algebraic techniques in two papers. Particularly, the first one (this work) proposes a new mathematical framework for batch homomorphic computation that is compatible with the existing bootstrapping methods of AP14/FHEW/TFHE. To show that our overall method requires only a polynomial modulus, we develop a critical algebraic analysis over noise growth, which might be of independent interest. Overall, the framework yields an amortized complexity\\(\\tilde{O}(\\lambda ^{0.75})\\)FHE multiplications, where\\(\\lambda \\)is the security parameter. This improves the prior methods of AP14/FHEW/TFHE, which required\\(O(\\lambda )\\)FHE multiplications in amortization. Developing many substantial new techniques based on the foundation of this work, the sequel (Bootstrapping II, Eurocrypt 2023) shows how to further improve the recursive bootstrapping method of MS18 (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, ICALP 2018), yielding a substantial theoretical improvement that can potentially lead to more practical methods.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_11"}, {"primary_key": "1135242", "vector": [], "sparse_vector": [], "title": "Batch Bootstrapping II: - Bootstrapping in Polynomial Modulus only Requires Õ(1) FHE Multiplications in Amortization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This work continues the exploration of the batch framework proposed inBatch Bootstrapping I(<PERSON> and <PERSON>, Eurocrypt 2023). By further designing novel batch homomorphic algorithms based on the batch framework, this work shows how to bootstrap\\(\\lambda \\)LWE input ciphertexts within a polynomial modulus, using\\(\\tilde{O}(\\lambda )\\)FHE multiplications. This implies an amortized complexity\\(\\tilde{O}(1)\\)FHE multiplications per input ciphertext, significantly improving our first work (whose amortized complexity is\\(\\tilde{O}(\\lambda ^{0.75})\\)) and the theoretical state of the art MS18 (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, ICALP 2018), whose amortized complexity is\\(O(3^{1/\\epsilon } \\cdot \\lambda ^{\\epsilon })\\), for any arbitrary constant\\(\\epsilon \\). We believe that all our new homomorphic algorithms might be useful in general applications, and thus can be of independent interests.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_12"}, {"primary_key": "1135243", "vector": [], "sparse_vector": [], "title": "Analysis of RIPEMD-160: New Collision Attacks and Finding Characteristics with MILP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The hash functionRIPEMD-160is an ISO/IEC standard and is being used to generate the bitcoin address together withSHA-256. Despite the fact that many hash functions in the MD-SHA hash family have been broken,RIPEMD-160remains secure and the best collision attack could only reach up to 34 out of 80 rounds, which was published at CRYPTO 2019. In this paper, we propose a new collision attack onRIPEMD-160that can reach up to 36 rounds with time complexity\\(2^{64.5}\\). This new attack is facilitated by a new strategy to choose the message differences and new techniques to simultaneously handle the differential conditions on both branches. Moreover, different from all the previous work onRIPEMD-160, we utilize a MILP-based method to search for differential characteristics, where we construct a model to accurately describe the signed difference transitions through its round function. As far as we know, this is the first model targeting the signed difference transitions for the MD-SHA hash family. Indeed, we are more motivated to design this model by the fact that many automatic tools to search for such differential characteristics are not publicly available and implementing them from scratch is too time-consuming and difficult. Hence, we expect that this can be an alternative easy tool for future research, which only requires to write down some simple linear inequalities.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_7"}, {"primary_key": "1135244", "vector": [], "sparse_vector": [], "title": "A Direct Key Recovery Attack on SIDH.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an attack on SIDH utilising isogenies between polarized products of two supersingular elliptic curves. In the case of arbitrary starting curve, our attack (discovered independently from [8]) has subexponential complexity, thus significantly reducing the security of SIDH and SIKE. When the endomorphism ring of the starting curve is known, our attack (here derived from [8]) has polynomial-time complexity assuming the generalised Riemann hypothesis. Our attack applies to any isogeny-based cryptosystem that publishes the images of points under the secret isogeny, for example Séta [13] and B-SIDH [11]. It does not apply to CSIDH [9], CSI-FiSh [3], or SQISign [14].", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_16"}, {"primary_key": "1135245", "vector": [], "sparse_vector": [], "title": "Non-adaptive Universal One-Way Hash Functions from Arbitrary One-Way Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work we give the firstnon-adaptiveconstruction ofuniversal one-way hash functions(UOWHFs) from arbitrary one-way functions. Our construction uses\\(O(n^9)\\)calls to the one-way function, has a key of length\\(O(n^{10})\\), and can be implemented in NC1 assuming the underlying one-way function is in NC1. Prior to this work, the best UOWHF construction used\\(O(n^{13})\\)adaptivecalls and a key of size\\(O(n^5)\\)(<PERSON><PERSON><PERSON>, <PERSON>nstein, <PERSON>ingold, <PERSON><PERSON><PERSON> and <PERSON>e [Eurocrypt ’10]). By the result of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [FOCS ’04], the above implies the existence of UOWHFs in NC0, given the existence of one-way functions in NC1. We also show that the PRG construction of Haitner, Reingold and Vadhan (HRV, [STOC ’10]), with small modifications, yields a relaxed notion of UOWHFs, which is a function family which can be (inefficiently) converted to UOWHF by changing the functions on a negligible fraction of the inputs. In order to analyze this construction, we introduce the notion ofnext-bit unreachable entropy, which replaces the next-bit pseudoentropy notion used by HRV.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_17"}, {"primary_key": "1135246", "vector": [], "sparse_vector": [], "title": "Effective and Efficient Masking with Low Noise Using Small-Mersenne-Prime Ciphers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Embedded devices used in security applications are natural targets for physical attacks. Thus, enhancing their side-channel resistance is an important research challenge. A standard solution for this purpose is the use of Boolean masking schemes, as they are well adapted to current block ciphers with efficient bitslice representations. Boolean masking guarantees that the security of an implementation grows exponentially in the number of shares under the assumption that leakages are sufficiently noisy (and independent). Unfortunately, it has been shown that this noise assumption is hardly met on low-end devices. In this paper, we therefore investigate techniques to mask cryptographic algorithms in such a way that their resistance can survive an almost complete lack of noise. Building on seed theoretical results of <PERSON><PERSON><PERSON> et al., we put forward that arithmetic encodings in prime fields can reach this goal. We first exhibit the gains that such encodings lead to thanks to a simulated information theoretic analysis of their leakage (with up to six shares). We then provide figures showing that on platforms where optimized arithmetic adders and multipliers are readily available (i.e., most MCUs and FPGAs), performing masked operations in small to medium Mersenne-prime fields as opposed to binary extension fields will not lead to notable implementation overheads. We compile these observations into a newAES-like block cipher, calledAES-prime, which is well-suited to illustrate the remarkable advantages of masking in prime fields. We also confirm the practical relevance of our findings by evaluating concrete software (ARM Cortex-M3) and hardware (Xilinx Spartan-6) implementations. Our experimental results show that security gains over Boolean masking (and, more generally, binary encodings) can reach orders of magnitude despite the same amount of information being leaked per share.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_20"}, {"primary_key": "1135247", "vector": [], "sparse_vector": [], "title": "The Return of the SDitH.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a code-based signature scheme based on the well-known syndrome decoding (SD) problem. The scheme builds upon a recent line of research which uses the Multi-Party-Computation-in-the-Head (MPCitH) approach to construct efficient zero-knowledge proofs, such as Syndrome Decoding in the Head (SDitH), and builds signature schemes from them using the Fiat-<PERSON><PERSON><PERSON> transform. At the heart of our proposal is a new approach, Hypercube-MPCitH, to amplify the soundness of any MPC protocol that uses additive secret sharing. An MPCitH protocol withNparties can be repeatedDtimes using parallel composition to reach the same soundness as a protocol run with\\(N^D\\)parties. However, the former comes withDtimes higher communication costs, often mainly contributed by the usage ofD‘auxiliary’ states (which in general have a significantly bigger impact on size than random states). Instead of that, we begin by generating\\(N^D\\)shares, arranged into aD-dimensional hypercube of sideNcontaining only one ‘auxiliary’ state. We derive from this hypercubeDsharings of sizeNwhich are used to runDinstances of anNparty MPC protocol. Hypercube-MPCitH leads to a protocol with\\(1/N^D\\)soundness error, requiring\\(N^D\\)offline computation, but withonly\\(N\\cdot D\\)online computation, andonly1 ‘auxiliary’. As the (potentially offline) share generation phase is generally inexpensive, this leads to trade-offs that are superior to just using parallel composition. Our novel method of share generation and aggregation not only improves certain MPCitH protocols in general but also shows in concrete improvements of signature schemes. Specifically, we apply it to the work of Feneuil, Joux, and Rivain (CRYPTO’22) on code-based signatures, and obtain a new signature scheme that achieves a 8.1x improvement in global runtime and a 30x improvement in online runtime for their shortest signatures size (8,481 Bytes). It is also possible to leverage the fact that most computations are offline to define parameter sets leading to smaller signatures: 6,784 Bytes for 26 ms offline and 5,689 Bytes for 320 ms offline. For NIST security level 1, online signature cost is around 3 million cycles (<1 ms on commodity processors), regardless of signature size.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_20"}, {"primary_key": "1135248", "vector": [], "sparse_vector": [], "title": "Context Discovery and Commitment Attacks - How to Break CCM, EAX, SIV, and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A line of recent work has highlighted the importance of context commitment security, which asks that authenticated encryption with associated data (AEAD) schemes will not decrypt the same adversarially-chosen ciphertext under two different, adversarially-chosen contexts (secret key, associated data, and nonce). Despite a spate of recent attacks, many open questions remain around context commitment; most obviously nothing is known about the commitment security of important schemes such as CCM, EAX, and SIV. We resolve these open questions, and more. Our approach is to, first, introduce a new framework that helps us more granularly define context commitment security in terms of what portions of a context are adversarially controlled. We go on to formulate a new security notion, called context discoverability, which can be viewed as analogous to preimage resistance from the hashing literature. We show that unrestricted context commitment security (the adversary controls all of the two contexts) implies context discoverability security for a class of schemes encompassing most schemes used in practice. Then, we show new context discovery attacks against a wide set of AEAD schemes, including CCM, EAX, SIV, GCM, and OCB3, and, by our general result, this gives new unrestricted context commitment attacks against them. Finally, we explore the case of restricted context commitment security for the original SIV mode, for which no prior attack techniques work (including our context discovery based ones). We are nevertheless able to give a novel\\(\\mathcal {O}(2^{n/3})\\)attack using <PERSON>’s k-tree algorithm for the generalized birthday problem.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_13"}, {"primary_key": "1135249", "vector": [], "sparse_vector": [], "title": "Complete Characterization of Broadcast and Pseudo-signatures from Correlations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unconditionally secure broadcast is feasible among parties connected by pairwise secure links only if there is a strict two-thirds majority of honest parties when no additional resources are available. This limitation may be circumvented when the parties have recourse to additional resources such as correlated randomness. <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (CRYPTO 2004) attempted to characterize the conditions on correlated randomness shared among three parties which would enable them to realize broadcast. Due to a gap in their impossibility argument, it turns out that their characterization is incorrect. Using a novel construction we show that broadcast is feasible under a considerably larger class of correlations. In fact, we realize pseudo-signatures, which are information theoretic counterparts of digital signatures using which unconditionally secure broadcast may be obtained. We also obtain a matching impossibility result thereby characterizing the class of correlations on which three-party broadcast (and pseudo-signatures) can be based. Our impossibility proof, which extends the well-know argument of <PERSON>, <PERSON> and <PERSON> (Distr. Comp., 1986) to the case where parties observe correlated randomness, maybe of independent interest.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30617-4_19"}, {"primary_key": "1135250", "vector": [], "sparse_vector": [], "title": "Chopsticks: Fork-Free Two-Round Multi-signatures from Non-interactive Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-signatures have been drawing lots of attention in recent years, due to their applications in cryptocurrencies. Most early constructions require three-round signing, and recent constructions have managed to reduce the round complexity to two. However, their security proofs are mostly based on non-standard, interactive assumptions (e.g. one-more assumptions) and come with a huge security loss, due to multiple uses of rewinding (aka the Forking Lemma). This renders the quantitative guarantees given by the security proof useless. In this work, we improve the state of the art by proposing two efficient two-round multi-signature schemes from the (standard, non-interactive) Decisional Diffie-Hellman (DDH) assumption. Both schemes are proven secure in the random oracle model without rewinding. We do not require any pairing either. Our first scheme supports key aggregation but has a security loss linear in the number of signing queries, and our second scheme is thefirsttightly secure construction. A key ingredient in our constructions is a new homomorphic dual-mode commitment scheme for group elements, that allows to equivocate for messages of a certain structure. The definition and efficient construction of this commitment scheme is of independent interest.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_21"}, {"primary_key": "1135251", "vector": [], "sparse_vector": [], "title": "NanoGRAM: Garbled RAM with $\\widetilde{O}(\\log N)$ Overhead.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new garbled RAM construction called NanoGRAM, which achieves an amortized cost of\\(\\widetilde{O}(\\lambda \\cdot (W \\log N + \\log ^3 N))\\)bits per memory access, where\\(\\lambda \\)is the security parameter,Wis the block size, andNis the total number of blocks, and\\(\\widetilde{O}(\\cdot )\\)hides\\({\\textsf{poly}} \\log \\log \\)factors. For sufficiently large blocks where\\(W = \\varOmega (\\log ^2 N)\\), our scheme achieves\\(\\widetilde{O}(\\lambda \\cdot W \\log N)\\)cost per memory access, where the dependence onNis optimal (barring\\({\\textsf{poly}} \\log \\log \\)factors), in terms of the evaluator’s runtime. Our asymptotical performance matches even theinteractivestate-of-the-art (modulo\\({\\textsf{poly}} \\log \\log \\)factors), that is, running Circuit ORAM atop garbled circuit, and yet we remove the logarithmic number of interactions necessary in this baseline. Furthermore, we achieve asymptotical improvement over the recent work of <PERSON> et al. (Eurocrypt ’22). Our scheme adopts the same assumptions as the mainstream literature on practical garbled circuits, i.e., circular correlation-robust hashes or a random oracle. We evaluate the concrete performance of NanoGRAM and compare it with a couple of baselines that are asymptotically less efficient. We show that NanoGRAM starts to outperform the naïve linear-scan garbled RAM at a memory size of\\(N = 2^9\\)and starts to outperform the recent construction of Heath et al. at\\(N = 2^{13}\\). Finally, as a by product, we also show the existence of a garbled RAM scheme assuming only one-way functions, with an amortized cost of\\(\\widetilde{O}(\\lambda ^2 \\cdot (W \\log N + \\log ^3 N))\\)per memory access. Again, the dependence onNis nearly optimal for blocks of size\\(W = \\varOmega (\\log ^2 N)\\)bits.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_16"}, {"primary_key": "1135252", "vector": [], "sparse_vector": [], "title": "Lower Bound Framework for Differentially Private and Oblivious Data Structures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In recent years, there has been significant work in studying data structures that provide privacy for the operations that are executed. These primitives aim to guarantee that observable access patterns to physical memory do not reveal substantial information about the queries and updates executed on the data structure. Multiple recent works, including <PERSON> and <PERSON> [Crypto’18], <PERSON><PERSON> and <PERSON>o [Eurocrypt’19], <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al.[TCC’19] and <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [Crypto’21], have shown that logarithmic overhead is required to support even basic RAM (array) operations for various privacy notions including obliviousness and differential privacy as well as different choices of sizes for RAM blocksband memory cells\\(\\omega \\). We continue along this line of work and present the first logarithmic lower bounds for differentially private RAMs (DPRAMs) that apply regardless of the sizes of blocksband cells\\(\\omega \\). This is the first logarithmic lower bounds for DPRAMs when blocks are significantly smaller than cells, that is\\(b \\ll \\omega \\). Furthermore, we present new logarithmic lower bounds for differentially private variants of classical data structure problems including sets, predecessor (successor) and disjoint sets (union-find) for which sub-logarithmic plaintext constructions are known. All our lower bounds extend to the multiple non-colluding servers setting. We also address an unfortunate issue with this rich line of work where the lower bound techniques are difficult to use and require customization for each new result. To make the techniques more accessible, we generalize our proofs into a framework that reduces proving logarithmic lower bounds to showing that a specific problem satisfies two simple, minimal conditions. We show our framework is easy-to-use as all the lower bounds in our paper utilize the framework and hope our framework will spur more usage of these lower bound techniques.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_17"}, {"primary_key": "1135253", "vector": [], "sparse_vector": [], "title": "Meet-in-the-Middle Preimage Attacks on Sponge-Based Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Meet-in-the-Middle (MitM) attack has been widely applied to preimage attacks on <PERSON><PERSON><PERSON><PERSON> (MD) hashing. In this paper, we introduce a generic framework of the MitM attack on sponge-based hashing. We find certain bit conditions can significantly reduce the diffusion of the unknown bits and lead to longer MitM characteristics. To find good or optimal configurations of MitM attacks, e.g., the bit conditions, the neutral sets, and the matching points, we introduce the bit-level MILP-based automatic tools onKeccak,AsconandXoodyak. To reduce the scale of bit-level models and make them solvable in reasonable time, a series of properties of the targeted hashing are considered in the modelling, such as the linear structure and CP-kernel forKeccak, the Boolean expression of Sbox forAscon. Finally, we give an improved 4-round preimage attack onKeccak-512/SHA3, and break a nearly 10 years’ cryptanalysis record. We also give the first preimage attacks on 3-/4-roundAscon-XOFand 3-roundXoodyak-XOF.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_6"}, {"primary_key": "1135254", "vector": [], "sparse_vector": [], "title": "Breaking SIDH in Polynomial Time.", "authors": ["<PERSON>"], "summary": "We show that we can break SIDH in (classical) polynomial time, even with a random starting curve\\(E_0\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_17"}, {"primary_key": "1135255", "vector": [], "sparse_vector": [], "title": "Unique-Path Identity Based Encryption with Applications to Strongly Secure Messaging.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hierarchical Identity Based Encryption(HIBE) is a well studied, versatile tool used in many cryptographic protocols. Yet, since the performance of all known HIBE constructions is broadly considered prohibitive, some real-world applications avoid relying on HIBE at the expense of security. A prominent example for this is secure messaging:Strongly securemessaging protocols are provably equivalent toKey-Updatable Key Encapsulation Mechanisms(KU-KEMs; <PERSON> et al., Asiacrypt 2020); so far, all KU-KEM constructions rely onadaptive unbounded-depth HIBE (Poettering and <PERSON><PERSON>sler, <PERSON> and Stepanovs, both CRYPTO 2018). By weakening security requirements for better efficiency, many messaging protocols dispense with using HIBE. In this work, we aim to gainbetter efficiency without sacrificing security. For this, we observe that applications like messaging only need a restricted variant of HIBE for strong security. This variant, that we callUnique-PathIdentity Based Encryption (UPIBE), restricts HIBE by requiring that each secret key can delegate at most one subordinate secret key. However, in contrast to fixed secret key delegation in Forward-Secure Public Key Encryption, the delegation in UPIBE, as in HIBE, is uniquely determined by variable identity strings from an exponentially large space. We investigate this mild but surprisingly effective restriction and show that it offers substantial complexity and performance advantages. More concretely, we generically buildbounded-depthUPIBE from onlybounded-collusion IBEin the standard model; and we generically buildadaptive unbounded-depth UPIBE from onlyselective bounded-depth HIBE in the random oracle model. These results significantly extend the range of underlying assumptions and efficient instantiations. We conclude with a rigorous performance evaluation of our UPIBE design. Beyond solving challenging open problems by reducing complexity and improving efficiency of KU-KEM and strongly secure messaging protocols, we offer a new definitional perspective on the bounded-collusion setting.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_1"}, {"primary_key": "1135256", "vector": [], "sparse_vector": [], "title": "Indistinguishable Predictions and Multi-group Fair Learning.", "authors": ["<PERSON>"], "summary": "Prediction algorithms assign numbers to individuals that are popularly understood as individual “probabilities”—what is the probability that an applicant will repay a loan? Automated predictions increasingly form the basis for life-altering decisions, and this raises a host of concerns. Concerns about thefairnessof the resulting predictions are particularly alarming: for example, the predictor might perform poorly on a protected minority group. We survey recent developments in formalizing and addressing such concerns. Inspired by the theory of computational indistinguishability, the recently proposed notion ofOutcome Indistinguishability (OI)[Dworket al., STOC 2021] requires that thepredicteddistribution of outcomes cannot be distinguished from the real-world distribution. Outcome Indistinguishability is a strong requirement for obtaining meaningful predictions. Happily, it can be obtained: techniques from the algorithmic fairness literature [<PERSON><PERSON><PERSON><PERSON><PERSON> al., ICML 2018] yield algorithms for learning OI predictors from real-world outcome data. Returning to the motivation of addressing fairness concerns, Outcome Indistinguishability can be used to provide robust and general guarantees for protected demographic groups [Rothblum and Yona, ICML 2021]. This gives algorithms that can learn a single predictor that “performs well” for every group in a given rich collectionGof overlapping subgroups. Performance is measured using a loss function, which can be quite general and can itself incorporate fairness concerns.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_1"}, {"primary_key": "1135257", "vector": [], "sparse_vector": [], "title": "Randomized Half-Ideal Cipher on Groups with Applications to UC (a)PAKE.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An Ideal Cipher (IC) is a cipher where each key defines a random permutation on the domain. Ideal Cipher on a group has many attractive applications, e.g., theEncrypted Key Exchange(EKE) protocol for Password Authenticated Key Exchange (PAKE) [8], or asymmetric PAKE (aPAKE) [31,33]. However, known constructions for IC on a group domain all have drawbacks, including key leakage from timing information [12], requiring 4 hash-onto-group operations if IC is an 8-round Feistel [22], and limiting the domain to half the group [9] or using variable-time encoding [39,47] if IC is implemented via (quasi-) bijections from groups to bitstrings [33]. We propose an IC relaxation called a(Randomized) Half-Ideal Cipher(HIC), and we show that HIC on a group can be realized by amodified 2-round Feistel(m2F), at a cost of 1 hash-onto-group operation, which beats existing IC constructions in versatility and computational cost. HIC weakens IC properties by letting part of the ciphertext be non-random, but we exemplify that it can be used as a drop-in replacement for IC by showing that EKE [8] and aPAKE of [33] realize respectively UC PAKE and UC aPAKE even if they use HIC instead of IC. The m2F construction can also serve as IC domain extension, because m2F constructs HIC on domainDfrom an RO-indifferentiable hash ontoDand an IC on\\(2{\\kappa }\\)-bit strings, for\\({\\kappa }\\)a security parameter. One application of such extender is a modular lattice-based UC PAKE using EKE instantiated with HIC and anonymous lattice-based KEM.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_5"}, {"primary_key": "1135258", "vector": [], "sparse_vector": [], "title": "Exploiting Non-full Key Additions: Full-Fledged Automatic Demirci-Selçuk Meet-in-the-Middle Cryptanalysis of SKINNY.", "authors": ["<PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Demirci-Selçuk meet-in-the-middle (DS-MITM) attack is a sophisticated variant of differential attacks. Due to its sophistication, it is hard to efficiently find the best DS-MITM attacks on most ciphersexceptfor AES. Moreover, the current automatic tools only capture the most basic version of DS-MITM attacks, and the critical techniques developed for enhancing the attacks (e.g., differential enumeration and key-dependent-sieve) still rely on manual work. In this paper, we develop a full-fledged automatic framework integrating all known techniques (differential enumeration, key-dependent-sieve, and key bridging, etc.) for the DS-MITM attack that can produce key-recovery attacks directly rather than only search for distinguishers. Moreover, we develop a new technique that is able to exploit partial key additions to generate more linear relations beneficial to the attacks. We apply the framework to the SKINNY family of block ciphers and significantly improved results are obtained. In particular, all known DS-MITM attacks on the respective versions of SKINNY are improved by at least 2 rounds, and the data, memory, or time complexities of some attacks are reduced even compared to previous best attacks penetrating less rounds.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_3"}, {"primary_key": "1135259", "vector": [], "sparse_vector": [], "title": "Threshold and Multi-signature Schemes from Linear Hash Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper gives new constructions of two-round multi-signa-tures and threshold signatures for which security relies solely on either the hardness of the (plain) discrete logarithm problem or the hardness of RSA, in addition to assuming random oracles. Their signing protocol is partially non-interactive, i.e., the first round of the signing protocol is independent of the message being signed. We obtain our constructions by generalizing the most efficient discrete-logarithm based schemes,\\(\\textsf{MuSig2}\\)(<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, CRYPTO ’21) and\\(\\textsf{FROST}\\)(<PERSON><PERSON><PERSON> and <PERSON>, SAC ’20), to work with suitably defined linear hash functions. While the original schemes rely on the stronger and more controversial one-more discrete logarithm assumption, we show that suitable instantiations of the hash functions enable security to be based on either the plain discrete logarithm assumption or on RSA. The signatures produced by our schemes are equivalent to those obtained from Ok<PERSON><PERSON>’s identification schemes (CRYPTO ’92). More abstractly, our results suggest a general framework to transform schemes secure under OMDL into ones secure under the plain DL assumption and, with some restrictions, under RSA.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_22"}, {"primary_key": "1135260", "vector": [], "sparse_vector": [], "title": "Revisiting BBS Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "BBS signatures were implicitly proposed by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CRYPTO ’04) as part of their group signature scheme, and explicitly cast as stand-alone signatures by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CRYPTO ’04). A provably secure version, called BBS+, was then devised by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (SCN ’06), and is currently the object of a standardization effort which has led to a recent RFC draft. BBS+ signatures are suitable for use within anonymous credential and DAA systems, as their algebraic structure enables efficient proofs of knowledge of message-signature pairs that support partial disclosure. BBS+ signatures consist of one group element and two scalars. As our first contribution, we prove that a variant of BBS+ producing shorter signatures, consisting only of one group element and one scalar, is also secure. The resulting scheme is essentially the original BBS proposal, which was lacking a proof of security. Here we show it satisfies, under theq-SDH assumption, the same provable security guarantees as BBS+. We also provide a complementary tight analysis in the algebraic group model, which heuristically justifies instantiations with potentially shorter signatures. Furthermore, we devise simplified and shorter zero-knowledge proofs of knowledge of a BBS message-signature pair that support partial disclosure of the message. Over the BLS12-381 curve, our proofs are 896 bits shorter than the prior proposal by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (TRUST ’16), which is also adopted by the RFC draft. Finally, we show that BBS satisfies one-more unforgeability in the algebraic group model in a scenario, arising in the context of credentials, where the signer can be asked to sign arbitrary group elements, meant to be commitments, without seeing their openings.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30589-4_24"}, {"primary_key": "1135261", "vector": [], "sparse_vector": [], "title": "Unbounded Quadratic Functional Encryption and More from Pairings.", "authors": ["<PERSON><PERSON>"], "summary": "We propose the first unbounded functional encryption (FE) scheme for quadratic functions and its extension, in which the sizes of messages to be encrypted are not a priori bounded. Prior to our work, all FE schemes for quadratic functions are bounded, meaning that the message length is fixed at the setup. In the first scheme, encryption takes\\(\\{x_{i}\\}_{i \\in S_{c}}\\), key generation takes\\(\\{c_{i,j}\\}_{i,j \\in S_{k}}\\), and decryption outputs\\(\\sum _{i,j \\in S_{k}} c_{i,j}x_{i}x_{j}\\)if and only if\\(S_{k} \\subseteq S_{c}\\), where the sizes of\\(S_{c}\\)and\\(S_{k}\\)can be arbitrary. Our second scheme is the extension of the first scheme to partially-hiding FE that computes an arithmetic branching program on a public input and a quadratic function on a private input. Concretely, encryption takes a public input\\(\\textbf{u}\\)in addition to\\(\\{x_{i}\\}_{i \\in S_{c}}\\), a secret key is associated with arithmetic branching programs\\(\\{f_{i,j}\\}_{i,j \\in S_{k}}\\), and decryption yields\\(\\sum _{i,j \\in S_{k}} f_{i,j}(\\textbf{u})x_{i}x_{j}\\)if and only if\\(S_{k} \\subseteq S_{c}\\). Both our schemes are based on pairings and secure in the simulation-based model under the standard MDDH assumption.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_18"}, {"primary_key": "1135262", "vector": [], "sparse_vector": [], "title": "Worst-Case Subexponential Attacks on PRGs of Constant Degree or Constant Locality.", "authors": ["<PERSON><PERSON>"], "summary": "In this work, we will give new attacks on the pseudorandomness of algebraic pseudorandom number generators (PRGs) of polynomial stretch. Our algorithms apply to a broad class of PRGs and are in the case of general local PRGs faster than currently known attacks. At the same time, in contrast to most algebraic attacks, subexponential time and space bounds will be proven for our attacks without making any assumptions of the PRGs or assuming any further conjectures. Therefore, we yield in this text the first subexponential distinguishing attacks on PRGs from constant-degree polynomials and close current gaps in the subexponential cryptoanalysis of lightweight PRGs. Concretely, against PRGs\\(F : \\mathbb {Z}_q^{n} \\rightarrow \\mathbb {Z}_q^{m}\\)that are computed by polynomials of degreedover a field\\(\\mathbb {Z}_q\\)and have a stretch of\\(m = n^{1+e}\\)we give an attack with space and time complexities\\(n^{O(n^{1 - \\frac{e}{d-1}})}\\)and noticeable advantage\\(1 - {O(n^{1 - \\frac{e}{d-1}}/{q})}\\). Ifqlies in\\(O(n^{1 - \\frac{e}{d-1}})\\), we give a second attack with the same space and time complexities whose advantage is at least\\(q^{-O(n^{1 - \\frac{e}{d-1}})}\\). IfFis of constantlocalitydandqis constant, we construct a third attack that has a space and time complexity of\\(\\exp (O(n^{1 - \\frac{e'}{(q-1)d-1}}))\\)and noticeable advantage\\(1-O(n^{-\\frac{e'}{(q-1)d-1}})\\)for every constant\\(e' < e\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_2"}, {"primary_key": "1135263", "vector": [], "sparse_vector": [], "title": "Succinct Vector, Polynomial, and Functional Commitments from Lattices.", "authors": ["<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "Vector commitment schemes allow a user to commit to a vector of values\\(\\textbf{x}\\in \\{0,1\\}^\\ell \\)and later, open up the commitment to a specific set of positions. Both the size of the commitment and the size of the opening should be succinct (i.e., polylogarithmic in the length\\(\\ell \\)of the vector). Vector commitments and their generalizations to polynomial commitments and functional commitments are key building blocks for many cryptographic protocols. We introduce a new framework for constructing non-interactive lattice-based vector commitments and their generalizations. A simple instantiation of our framework yields a new vector commitment scheme from the standard short integer solution (SIS) assumption that supportsprivateopenings and large messages. We then show how to use our framework to obtain the first succinctfunctionalcommitment scheme that supports openings with respect to arbitrary bounded-depth Boolean circuits. In this scheme, a user commits to a vector\\(\\textbf{x}\\in \\{0,1\\}^\\ell \\), and later on, open the commitment to any function\\(f(\\textbf{x})\\). Both the commitmentandthe opening are non-interactive and succinct: namely, they have size\\(\\textsf{poly}(\\lambda , d, \\log \\ell )\\), where\\(\\lambda \\)is the security parameter anddis thedepthof the Boolean circuit computingf. Previous constructions of functional commitments could only support constant-degree polynomials, or require a trustedonlineauthority, or rely on non-falsifiable assumptions. The security of our functional commitment scheme is based on a new falsifiable family of “basis-augmented” SIS assumptions (\\(\\textsf{BASIS}\\)) we introduce in this work. We also show how to use our vector commitment framework to obtain (1) a polynomial commitment scheme where the user can commit to a polynomial\\(f \\in \\mathbb {Z}_{q}[x]\\)and subsequently open the commitment to an evaluation\\(f(x) \\in \\mathbb {Z}_{q}\\); and (2) an aggregatable vector (resp., functional) commitment where a user can take a set of openings to multiple indices (resp., function evaluations) and aggregate them into asingleshort opening. Both of these extensions rely on the same\\(\\textsf{BASIS}\\)assumption we use to obtain our succinct functional commitment scheme.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_13"}, {"primary_key": "1135264", "vector": [], "sparse_vector": [], "title": "Privately Puncturing PRFs from Lattices: Adaptive Security and Collusion Resistant Pseudorandomness.", "authors": ["Rupeng <PERSON>"], "summary": "A private puncturable pseudorandom function (PRF) enables one to create a constrained version of a PRF key, which can be used to evaluate the PRF at all but some punctured points. In addition, the constrained key reveals no information about the punctured points and the PRF values on them. Existing constructions of private puncturable PRFs are only proven to be secure against a restricted adversary that must commit to the punctured points before viewing any information. It is an open problem to achieve the more natural adaptive security, where the adversary can make all its choices on-the-fly. In this work, we solve the problem by constructing an adaptively secure private puncturable PRF from standard lattice assumptions. To achieve this goal, we present a new primitive called explainable hash, which allows one to reprogram the hash function on a given input. The new primitive may find further applications in constructing more cryptographic schemes with adaptive security. Besides, our construction has collusion resistant pseudorandomness, which requires that even given multiple constrained keys, no one could learn the values of the PRF at the punctured points. Private puncturable PRFs with collusion resistant pseudorandomness were only known from multilinear maps or indistinguishability obfuscations in previous works, and we provide the first solution from standard lattice assumptions.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_6"}, {"primary_key": "1135265", "vector": [], "sparse_vector": [], "title": "Lower Bounds for (Batch) PIR with Private Preprocessing.", "authors": ["<PERSON>"], "summary": "In this paper, we study(batch) private information retrieval with private preprocessing. Private information retrieval (PIR) is the problem where one or more servers hold a database ofnbits and a client wishes to retrieve thei-th bit in the database from the server(s). InPIR with private preprocessing(also known as offline-online PIR), the client is able to compute a privater-bit hint in an offline stage that may be leveraged to perform retrievals accessing at mosttentries. For privacy, the client wishes to hide indexifrom an adversary that has compromised some of the servers. In thebatch PIRsetting, the client performs queries to retrieve the contents of multiple entries simultaneously. We present a tight characterization for the trade-offs between hint sizerand number of accessed entriestduring queries. For any PIR scheme that enables clients to perform batch retrievals ofkentries, we prove a lower bound of\\(tr = \\varOmega (nk)\\)when\\(r \\ge k\\). When\\(r < k\\), we prove that\\(t = \\varOmega (n)\\). Our lower bounds hold when the scheme errs with probability at most 1/15 and against PPT adversaries that only compromise one out of\\(\\ell \\)servers for any\\(\\ell = O(1)\\). Our work also closes the multiplicative logarithmic gap for the single query setting\\((k = 1)\\)as our lower bound matches known constructions. Our lower bounds hold in the model where each database entry is stored without modification but each entry may be replicated arbitrarily. Finally, we show connections between PIR and the online matrix-vector (\\(\\textsf{OMV}\\)) conjecture from fine-grained complexity. We present barriers for proving lower bounds for two-server PIR schemes in general computational models as they would immediately imply the\\(\\textsf{OMV}\\)conjecture.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_18"}, {"primary_key": "1135266", "vector": [], "sparse_vector": [], "title": "Collision Attacks on Round-Reduced SHA-3 Using Conditional Internal Differentials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mei<PERSON> Liu"], "summary": "The KECCAK hash function was selected by NIST as the winner of the\\(\\texttt {SHA-3}\\)competition in 2012 and became the\\(\\texttt {SHA-3}\\)hash standard of NIST in 2015. On account of\\(\\texttt {SHA-3}\\)’s importance in theory and applications, the analysis of its security has attracted increasing attention. In the\\(\\texttt {SHA-3}\\)family,\\(\\texttt {SHA3-512}\\)shows the strongest resistance against collision attacks: the theoretical attacks of\\(\\texttt {SHA3-512}\\)only extend to four rounds by solving polynomial systems with 64 times faster than the birthday attack. Yet for the\\(\\texttt {SHA-3}\\)instanceSHAKE256there are no results on collision attacks that we are aware of in the literatures. In this paper, we study the collision attacks against round-reduced\\(\\texttt {SHA-3}\\). Inspired by the work of <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> in 2013, we propose a variant of birthday attack and improve the internal differential cryptanalysis by abstracting new concepts such asdifferential transition conditionsanddifference conditions table. With the help of these techniques, we develop new collision attacks on round-reduced\\(\\texttt {SHA-3}\\)using conditional internal differentials. More exactly, the initial messages constrained by linear conditions pass through the first two rounds of internal differential, and their corresponding inputs entering the last two rounds are divided into different subsets for collision search according to the values of linear conditions. Together with an improved target internal difference algorithm (TIDA), collision attacks on up to 5 rounds of all the six\\(\\texttt {SHA-3}\\)functions are obtained. In particular, collision attacks on 4-round\\(\\texttt {SHA3-512}\\)and 5-round\\(\\texttt {SHAKE256}\\)are achieved with complexity of\\(2^{237}\\)and\\(2^{185}\\)respectively. As far as we know, this is the best collision attack on reduced\\(\\texttt {SHA3-512}\\), and it is the first collision attack on reduced\\(\\texttt {SHAKE256}\\).", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_8"}, {"primary_key": "1135267", "vector": [], "sparse_vector": [], "title": "Improved Power Analysis Attacks on Falcon.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Falcon is one of the three post-quantum signature schemes selected for standardization by NIST. Due to its low bandwidth and high efficiency, Falcon is seen as an attractive option for quantum-safe embedded systems. In this work, we study Falcon’s side-channel resistance by analysing its Gaussian samplers. Our results are mainly twofold. The first result is an improved key recovery exploiting the leakage within the base sampler investigated by <PERSON> et al. (CHES 2022). Instead of resorting to the fourth moment as in former parallelepiped-learning attacks, we work with the second order statistics covariance and use its spectral decomposition to recover the secret information. Our approach substantially reduces the requirement for measurements and computation resources:\\(220\\,000\\)traces is sufficient to recover the secret key of Falcon-512 within half an hour with a probability of\\(\\approx 25\\%\\). As a comparison, even with\\(10^6\\)traces, the former attack still needs about 1000 h hours CPU time of lattice reduction for a full key recovery. In addition, our approach is robust to inaccurate leakage classification, which is another advantage over parallelepiped-learning attacks. Our second result is a practical power analysis targeting the integer Gaussian sampler of Falcon. The analysis relies on the leakage of random sign flip within the integer Gaussian sampling. This leakage was exposed in 2018 by <PERSON> and <PERSON>, but it is not considered in Falcon’s implementation and unexploited for side-channel analysis until now. We identify the leakage within the reference implementation of <PERSON> on an ARM Cortex-M4 STM32F407IGT6 microprocessor. We also show that this single bit of leakage is in effect enough for practical key recovery: with\\(170\\,000\\)traces one can fully recover the key of Falcon-512 within half an hour. Furthermore, combining the sign leakage and the aforementioned leakage, one can recover the key with only\\(45\\,000\\)signature measurements in a short time. As a by-product, we also extend our power analysis to Mitaka which is a recent variant of Falcon. The same leakages exist within the integer Gaussian samplers of Mitaka, and they can also be used to mount key recovery attacks. Nevertheless, the key recovery in Mitaka requires much more traces than it does in Falcon, due to their different lattice Gaussian samplers.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30634-1_19"}, {"primary_key": "1135268", "vector": [], "sparse_vector": [], "title": "Optimal Single-Server Private Information Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct a single-server pre-processing Private Information Retrieval (PIR) scheme with optimal bandwidth and server computation (up to poly-logarithmic factors), assuming hardness of the Learning With Errors (LWE) problem. Our scheme achieves amortized\\(\\widetilde{O}_{\\lambda }(\\sqrt{n})\\)server and client computation and\\(\\widetilde{O}_\\lambda (1)\\)bandwidth per query, completes in a single roundtrip, and requires\\(\\widetilde{O}_\\lambda (\\sqrt{n})\\)client storage. In particular, we achieve a significant reduction in bandwidth over the state-of-the-art scheme by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (Eurocrypt’22): their scheme requires as much as\\(\\widetilde{O}_{\\lambda }(\\sqrt{n})\\)bandwidth per query, with comparable computational and storage overhead as ours.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_14"}, {"primary_key": "1135269", "vector": [], "sparse_vector": [], "title": "A Theory of Composition for Differential Obliviousness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Differential obliviousness (DO) is a privacy notion which guarantees that the access patterns of a program satisfies differential privacy. Differential obliviousness was studied in a sequence of recent works as a relaxation of full obliviousness. Earlier works showed that DO not only allows us to circumvent the logarithmic-overhead barrier of fully oblivious algorithms, in many cases, it also allows us to achieve polynomial speedup over full obliviousness, since it avoids “padding to the worst-case” behavior of fully oblivious algorithms. Despite the promises of differential obliviousness (DO), a significant barrier that hinders its broad application is the lack of composability. In particular, when we apply one DO algorithm to the output of another DO algorithm, the composed algorithm may no longer be DO (with reasonable parameters). Specifically, the outputs of the first DO algorithm on two neighboring inputs may no longer be neighboring, and thus we cannot directly benefit from the DO guarantee of the second algorithm. In this work, we are the first to explore a theory of composition for differentially oblivious algorithms. We propose a refinement of the DO notion called\\((\\epsilon , \\delta )\\)-neighbor-preserving-DO, or\\((\\epsilon , \\delta )\\)-NPDO for short, and we prove that our new notion indeed provides nice compositional guarantees. In this way, the algorithm designer can easily track the privacy loss when composing multiple DO algorithms. We give several example applications to showcase the power and expressiveness of our new NPDO notion. One of these examples is a result of independent interest: we use the compositional framework to prove an optimal privacy amplification theorem for the differentially oblivious shuffle model. In other words, we show that for a class of distributed differentially private mechanisms in the shuffle-model, one can replace the perfectly secure shuffler with a DO shuffler, and nonetheless enjoy almost the same privacy amplification enabled by a shuffler.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30620-4_1"}, {"primary_key": "1135270", "vector": [], "sparse_vector": [], "title": "Endemic Oblivious Transfer via Random Oracles, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The notion of Endemic Oblivious Transfer (EOT) was introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CCS’19). EOT offers a weaker security guarantee than the conventional random OT; namely, the malicious parties can fix their outputs arbitrarily. The authors presented a 1-round UC-secure EOT protocol under a tailor-made and non-standard assumption, Choose-and-Open DDH, in the RO model. In this work, we systematically study EOT in the UC/GUC framework. We present a new 1-round UC-secure EOT construction in the RO model under the DDH assumption. Under the GUC framework, we propose the first 1-round EOT construction under the CDH assumption in the Global Restricted Observable RO (GroRO) model proposed by <PERSON><PERSON><PERSON> al.(CCS’14). We also provide an impossibility result, showing there existno1-round GUC-secure EOT protocols in the Global Restricted Programmable RO (GrpRO) model proposed by <PERSON><PERSON><PERSON>.(Eurocrypt’18). Subsequently, we provide the first round-optimal (2-round) EOT protocol with adaptive security under the DDH assumption in the GrpRO model. Finally, we investigate the relations between EOT and other cryptographic primitives. As side products, we present the first 2-round GUC-secure commitment in the GroRO model as well as a separation between the GroRO and the GrpRO models, which may be of independent interest.", "published": "2023-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-30545-0_11"}]