[{"primary_key": "3517156", "vector": [], "sparse_vector": [], "title": "Lip-Interact: Improving Mobile Device Interaction with Silent Speech Commands.", "authors": ["<PERSON>", "<PERSON>", "Weinan Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Lip-Interact, an interaction technique that allows users to issue commands on their smartphone through silent speech. Lip-Interact repurposes the front camera to capture the user's mouth movements and recognize the issued commands with an end-to-end deep learning model. Our system supports 44 commands for accessing both system-level functionalities (launching apps, changing system settings, and handling pop-up windows) and application-level functionalities (integrated operations for two apps). We verify the feasibility of Lip-Interact with three user experiments: evaluating the recognition accuracy, comparing with touch on input efficiency, and comparing with voiced commands with regards to personal privacy and social norms. We demonstrate that Lip-Interact can help users access functionality efficiently in one step, enable one-handed input when the other hand is occupied, and assist touch to make interactions more fluent.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242599"}, {"primary_key": "3517157", "vector": [], "sparse_vector": [], "title": "Screen-Camera Communication via Matrix Barcode Utilizing Imperceptible Color Vibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "S<PERSON>o Fu<PERSON>", "<PERSON><PERSON>"], "summary": "Communication between screens and cameras has attracted attention as a ubiquitous information source, motivated by the widespread use of smartphones and the increase of public advertising and information screens. We propose embedding matrix barcodes into images projected on displays by utilizing imperceptible color vibration. This approach maintains the visual experience as the barcodes are imperceptible and can be implemented on almost any display and camera for the technology to be pervasive. In fact, the color vibration can be generated by ordinary 60 Hz LCDs and captured by 120 fps smartphone cameras. To illustrate the technology capabilities, we present scenarios of potential practical applications.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271638"}, {"primary_key": "3517158", "vector": [], "sparse_vector": [], "title": "Crowdsourcing Similarity Judgments for Agreement Analysis in End-User Elicitation Studies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "End-user elicitation studies are a popular design method, but their data require substantial time and effort to analyze. In this paper, we present Crowdsensus, a crowd-powered tool that enables researchers to efficiently analyze the results of elicitation studies using subjective human judgment and automatic clustering algorithms. In addition to our own analysis, we asked six expert researchers with experience running and analyzing elicitation studies to analyze an end-user elicitation dataset of 10 functions for operating a web-browser, each with 43 voice commands elicited from end-users for a total of 430 voice commands. We used Crowdsensus to gather similarity judgments of these same 430 commands from 410 online crowd workers. The crowd outperformed the experts by arriving at the same results for seven of eight functions and resolving a function where the experts failed to agree. Also, using Crowdsensus was about four times faster than using experts.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242621"}, {"primary_key": "3517159", "vector": [], "sparse_vector": [], "title": "ZEUSSS: Zero Energy Ubiquitous Sound Sensing Surface Leveraging Triboelectric Nanogenerator and Analog Backscatter Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "ZEUSSS (Zero Energy Ubiquitous Sound Sensing Surface), allows physical objects and surfaces to be instrumented with a thin, self-sustainable material that provides acoustic sensing and communication capabilities. We have built a prototype ZEUSSS tag using minimal hardware and flexible electronic components, extending our original self-sustaining SATURN microphone with a printed, flexible antenna to support passive communication via analog backscatter. ZEUSSS enables objects to have ubiquitous wire-free battery-free audio based context sensing, interaction, and surveillance capabilities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266108"}, {"primary_key": "3517160", "vector": [], "sparse_vector": [], "title": "Sprout: Crowd-Powered Task Design for Crowdsourcing.", "authors": ["<PERSON>", "Mausam", "<PERSON>"], "summary": "While crowdsourcing enables data collection at scale, ensuring high-quality data remains a challenge. In particular, effective task design underlies nearly every reported crowdsourcing success, yet remains difficult to accomplish. Task design is hard because it involves a costly iterative process: identifying the kind of work output one wants, conveying this information to workers, observing worker performance, understanding what remains ambiguous, revising the instructions, and repeating the process until the resulting output is satisfactory. To facilitate this process, we propose a novel meta-workflow that helps requesters optimize crowdsourcing task designs and <PERSON><PERSON><PERSON>, our open-source tool, which implements this workflow. <PERSON><PERSON><PERSON> improves task designs by (1) eliciting points of confusion from crowd workers, (2) enabling requesters to quickly understand these misconceptions and the overall space of questions, and (3) guiding requesters to improve the task design in response. We report the results of a user study with two labeling tasks demonstrating that requesters strongly prefer <PERSON>p<PERSON> and produce higher-rated instructions compared to current best practices for creating gated instructions (instructions plus a workflow for training and testing workers). We also offer a set of design recommendations for future tools that support crowdsourcing task design.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242598"}, {"primary_key": "3517161", "vector": [], "sparse_vector": [], "title": "GridDrones: A Self-Levitating Physical Voxel Lattice for Interactive 3D Surface Deformations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present GridDrones, a self-levitating programmable matter platform that can be used for representing 2.5D voxel grid relief maps capable of rendering unsupported structures and 3D transformations. GridDrones consists of cube-shaped nanocopters that can be placed in a volumetric 1xnxn midair grid, which is demonstrated here with 15 voxels. The number of voxels and scale is only limited by the size of the room and budget. Grid deformations can be applied interactively to this voxel lattice by manually selecting a set of voxels, then assigning a continuous topological relationship between voxel sets that determines how voxels move in relation to each other and manually drawing out selected voxels from the lattice structure. Using this simple technique, it is possible to create unsupported structures that can be translated and oriented freely in 3D. Shape transformations can also be recorded to allow for simple physical shape morphing animations. This work extends previous work on selection and editing techniques for 3D user interfaces.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242658"}, {"primary_key": "3517162", "vector": [], "sparse_vector": [], "title": "Immersive Trip Reports.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Since the advent of consumer photography, tourists and hikers have made photo records of their trips to share later. Aside from being kept as memories, photo presentations such as slideshows are also shown to others who have not visited the location to try to convey the experience.However, a slideshow alone is limited in conveying the broader spatial context, and thus the feeling of presence in beautiful natural scenery is lost. We address this by presenting the photographs as part of an immersive experience. We introduce an automated pipeline for aligning photographs with a digital terrain model. From this geographic registration, we produce immersive presentations which are viewed either passively as a video, or interactively in virtual reality. Our experimental evaluation verifies that this new mode of presentation successfully conveys the spatial context of the scene and is enjoyable to users.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242653"}, {"primary_key": "3517163", "vector": [], "sparse_vector": [], "title": "Unimanual Pen+Touch Input Using Variations of Precision Grip Postures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new pen input space by forming postures with the same hand that also grips the pen while writing, drawing, or selecting. The postures contact the multitouch surface around the pen to enable detection without special sensors. A formative study investigates the effectiveness, accuracy, and comfort of 33 candidate postures in controlled tasks. The results indicate a useful subset of postures. Using raw capacitive sensor data captured in the study, a convolutional neural network is trained to recognize 10 postures in real time. This recognizer is used to create application demonstrations for pen-based document annotation and vector drawing. A small usability study shows the approach is feasible.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242652"}, {"primary_key": "3517164", "vector": [], "sparse_vector": [], "title": "Sense.Seat: Inducing Improved Mood and Cognition through Multisensorial Priming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Frederica Gonçalves"], "summary": "User interface software and technologies have been evolving significantly and rapidly. This poster presents a breakthrough user experience that leverages multisensorial priming and embedded interaction and introduces an interactive piece of furniture called Sense.Seat. Sensory stimuli such as calm colors, lavender and other scents as well as ambient soundscapes have been traditionally used to spark creativity and promote well-being. Sense.Seat is the first computational multisensorial seat that can be digitally controlled and vary the frequency and intensity of visual, auditory and olfactory stimulus. It is a new user interface shaped as a seat or pod that primes the user for inducing improved mood and cognition, therefore improving the work environment.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266105"}, {"primary_key": "3517165", "vector": [], "sparse_vector": [], "title": "Augmented Collaboration in Shared Space Design with Shared Attention and Manipulation.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Cha", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Woon<PERSON><PERSON> Woo"], "summary": "Augmented collaboration in a shared house design scenario has been studied widely with various approaches. However, those studies did not consider human perception. Our goal is to lower the user's perceptual load for augmented collaboration in shared space design scenarios. Applying attention theories, we implemented shared head gaze, shared selected object, and collaborative manipulation features in our system in two different versions with HoloLens. To investigate whether user perceptions of the two different versions differ, we conducted an experiment with 18 participants (9 pairs) and conducted a survey and semi-structured interviews. The results did not show significant differences between the two versions, but produced interesting insights. Based on the findings, we provide design guidelines for collaborative AR systems.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266086"}, {"primary_key": "3517166", "vector": [], "sparse_vector": [], "title": "Wall-based Space Manipulation Technique for Efficient Placement of Distant Objects in Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Jinwook Seo"], "summary": "We present a wall-based space manipulation (WSM) technique that enables users to efficiently select and move distant objects by dynamically squeezing their surrounding space in augmented reality. Users can bring a target object closer by dragging a solid plane behind the object and squeezing the space between them and the plane so that they can select and move the object more delicately and efficiently. We furthermore discuss the unique design challenges of WSM, including the dimension of space reduction and the recognition of the reduced space in relation to the real space. We conducted a user evaluation to verify how WSM improves the performance of the hand-centered object manipulation technique on the HoloLens for moving near objects far away and vice versa. The results indicate that WSM overall performed consistently well and significantly improved efficiency while alleviating arm fatigue.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242631"}, {"primary_key": "3517167", "vector": [], "sparse_vector": [], "title": "FacePush: Introducing Normal Force on Face with Head-Mounted Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chia-En <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents FacePush, a Head-Mounted Display (HMD) integrated with a pulley system to generate normal forces on a user's face in virtual reality (VR). The mechanism of FacePush is obtained by shifting torques provided by two motors that press upon a user's face via utilization of a pulley system. FacePush can generate normal forces of varying strengths and apply those to the surface of the face. To inform our design of FacePush for noticeable and discernible normal forces in VR applications, we conducted two studies to iden- tify the absolute detection threshold and the discrimination threshold for users' perception. After further consideration in regard to user comfort, we determined that two levels of force, 2.7 kPa and 3.375 kPa, are ideal for the development of the FacePush experience via implementation with three applications which demonstrate use of discrete and continuous normal force for the actions of boxing, diving, and 360 guidance in virtual reality. In addition, with regards to a virtual boxing application, we conducted a user study evaluating the user experience in terms of enjoyment and realism and collected the user's feedback.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242588"}, {"primary_key": "3517168", "vector": [], "sparse_vector": [], "title": "Rousillon: Scraping Distributed Hierarchical Web Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programming by Demonstration (PBD) promises to enable data scientists to collect web data. However, in formative interviews with social scientists, we learned that current PBD tools are insufficient for many real-world web scraping tasks. The missing piece is the capability to collect hierarchically-structured data from across many different webpages. We present Rousillon, a programming system for writing complex web automation scripts by demonstration. Users demonstrate how to collect the first row of a 'universal table' view of a hierarchical dataset to teach <PERSON><PERSON><PERSON><PERSON> how to collect all rows. To offer this new demonstration model, we developed novel relation selection and generalization algorithms. In a within-subject user study on 15 computer scientists, users can write hierarchical web scrapers 8 times more quickly with Rousillon than with traditional programming.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242661"}, {"primary_key": "3517169", "vector": [], "sparse_vector": [], "title": "Transparent Mask: Face-Capturing Head-Mounted Display with IR Pass Filters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual reality (VR) using a head-mounted display (HMD) have been rapidly becoming popular. Lots of HMD products and various VR applications such as games, training tools and communication services have been released in recent years. However, there is a well-known problem that the user's face is covered by the HMD preventing the facial expression from being captured. This strongly restricts VR applications. For example, users wearing HMDs normally cannot exchange their face images. This degrades communication quality in virtual spaces because facial expressions are an important element of human communication.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271632"}, {"primary_key": "3517170", "vector": [], "sparse_vector": [], "title": "An Interactive Pipeline for Creating Visual Blends.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visual blends are an advanced graphic design technique to draw users' attention to a message. They blend together two objects in a way that is novel and useful in conveying a message symbolically. This demo presents an interactive pipeline for creating visual blends that follows the iterative design process. Our pipeline decomposes the process into both computational techniques and human microtasks. It allows users to collaboratively generate visual blends with steps involving brainstorming, synthesis, and iteration. Our demo allows individual users to see how existing visual blends were made, edit or improve existing visual blends, and create new visual blends.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271646"}, {"primary_key": "3517171", "vector": [], "sparse_vector": [], "title": "reMi: Translating Ambient Sounds of Moment into Tangible and Shareable Memories through Animated Paper.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a tangible memory notebook--reMi--that records the ambient sounds and translates them into a tangible and shareable memory using animated paper. The paper replays the recorded sounds and deforms its shape to generate synchronized motions with the sounds. Computer-mediated communication interfaces have allowed us to share, record and recall memories easily through visual records. However, those digital visual-cues that are trapped behind the device's 2D screen are not the only means to recall a memory we experienced with more than the sense of vision. To develop a new way to store, recall and share a memory, we investigate how tangible motion of a paper that represents sound can enhance the \"reminiscence\".", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266109"}, {"primary_key": "3517172", "vector": [], "sparse_vector": [], "title": "Idyll: A Markup Language for Authoring and Publishing Interactive Articles on the Web.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The web has matured as a publishing platform: news outlets regularly publish rich, interactive stories while technical writers use animation and interaction to communicate complex ideas. This style of interactive media has the potential to engage a large audience and more clearly explain concepts, but is expensive and time consuming to produce. Drawing on industry experience and interviews with domain experts, we contribute design tools to make it easier to author and publish interactive articles. We introduce Idyll, a novel \"compile-to-the-web\" language for web-based interactive narratives. Idyll implements a flexible article model, allowing authors control over document style and layout, reader-driven events (such as button clicks and scroll triggers), and a structured interface to JavaScript components. Through both examples and first-use results from undergraduate computer science students, we show how <PERSON><PERSON><PERSON> reduces the amount of effort and custom code required to create interactive articles.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242600"}, {"primary_key": "3517173", "vector": [], "sparse_vector": [], "title": "Vizir: A Domain-Specific Graphical Language for Authoring and Operating Airport Automations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automation is one of the key solutions proposed and adopted by international Air Transport research programs to meet the challenges of increasing air traffic. For automation to be safe and usable, it needs to be suitable to the activity it supports, both when authoring it and when operating it. Here we present Vizir, a Domain-Specific Graphical Language and an Environment for authoring and operating airport automations. We used a participatory-design process with Air Traffic Controllers to gather requirements for Vizir and to design its features. Vizir combines visual interaction-oriented programming constructs with activity-related geographic areas and events. Vizir offers explicit human-control constructs, graphical substrates and means to scale-up with multiple automations. We propose a set of guidelines to inspire designers of similar usable hybrid human-automation systems.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242623"}, {"primary_key": "3517174", "vector": [], "sparse_vector": [], "title": "Assembly-aware Design of Printable Electromechanical Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "From smart toys and household appliances to personal robots, electromechanical devices play an increasingly important role in our daily lives. Rather than relying on gadgets that are mass-produced, our goal is to enable casual users to custom-design such devices based on their own needs and preferences. To this end, we present a computational design system that leverages the power of digital fabrication and the emergence of affordable electronics such as sensors and microcontrollers. The input to our system consists of a 3D representation of the desired device's shape, and a set of user-preferred off-the-shelf components. Based on this input, our method generates an optimized, 3D printable enclosure that can house the required components. To create these designs automatically, we formalize a new spatio-temporal model that captures the entire assembly process, including the placement of the components within the device, mounting structures and attachment strategies, the order in which components must be inserted, and collision-free assembly paths. Using this model as a technical core, we then leverage engineering design guidelines and efficient numerical techniques to optimize device designs. In a user study, which also highlights the challenges of designing such devices, we find our system to be effective in reducing the entry barriers faced by casual users in creating such devices. We further demonstrate the versatility of our approach by designing and fabricating three devices with diverse functionalities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242655"}, {"primary_key": "3517175", "vector": [], "sparse_vector": [], "title": "A Demonstration of VRSpinning: Exploring the Design Space of a 1D Rotation Platform to Increase the Perception of Self-Motion in VR.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demonstration we introduce VRSpinning, a seated locomotion approach based around stimulating the user's vestibular system using a rotational impulse to induce the perception of linear self-motion. Currently, most approaches for locomotion in VR use either concepts like teleportation for traveling longer distances or present a virtual motion that creates a visual-vestibular conflict, which is assumed to cause simulator sickness. With our platform we evaluated two designs for using the rotation of a motorized swivel chair to alleviate this, wiggle and impulse. Our evaluation showed that impulse, using short rotation bursts matched with the visual acceleration, can significantly reduce simulator sickness and increase the perception of self-motion compared to no physical motion.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271645"}, {"primary_key": "3517176", "vector": [], "sparse_vector": [], "title": "Maestro: Designing a System for Real-Time Orchestration of 3D Modeling Workshops.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Instructors of 3D design workshops for children face many challenges, including maintaining awareness of students' progress, helping students who need additional attention, and creating a fun experience while still achieving learning goals. To help address these challenges, we developed Maestro, a workshop orchestration system that visualizes students' progress, automatically detects and draws attention to common challenges faced by students, and provides mechanisms to address common student challenges as they occur. We present the design of <PERSON><PERSON>, and the results of a case-study evaluation with an experienced facilitator and 13 children. The facilitator appreciated <PERSON><PERSON>'s real-time indications of which students were successfully following her tutorial demonstration, and recognized the system's potential to \"extend her reach\" while helping struggling students. Participant interaction data from the study provided support for our follow-along detection algorithm, and the capability to remind students to use 3D navigation.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242606"}, {"primary_key": "3517177", "vector": [], "sparse_vector": [], "title": "Artistic Vision: Providing Contextual Guidance for Capture-Time Decisions.", "authors": ["<PERSON>"], "summary": "With the increased popularity of cameras, more and more people are interested in learning photography. People are willing to invest in expensive cameras as a medium for their artistic expression, but few have access to in-person classes. Inspired by critique sessions common in in-person art practice classes, we propose design principles for creative learning. My dissertation research focuses on designing new interfaces and interactions that provide contextual in-camera feedback to aid users in learning visual elements of photography. We interactively visualize results of image processing algorithms as additional information for the user to make more informed and intentional decisions during capture. In this paper, we describe our design principles, and apply these principles in the design of two guided photography interfaces: one to explore lighting options for a portrait, and one to refine contents and composition of a photo.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266128"}, {"primary_key": "3517178", "vector": [], "sparse_vector": [], "title": "SurfaceStreams: A Content-Agnostic Streaming Toolkit for Interactive Surfaces.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present SurfaceStreams, an open-source toolkit for recording and sharing visual content among multiple heterogeneous display-camera systems. SurfaceStreams clients support on-the-fly background removal and rectification on a range of different capture devices (Kinect & RealSense depth cameras, SUR40 sensor, plain webcam). After preprocessing, the raw data is compressed and sent to the SurfaceStreams server, which can dynamically receive streams from multiple clients, overlay them using the removed background as mask, and deliver the merged result back to the clients for display. We discuss an exemplary usage scenario (3-way shared interactive tabletop surface) and present results from a preliminary performance evaluation.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266085"}, {"primary_key": "3517179", "vector": [], "sparse_vector": [], "title": "Investigation into Natural Gestures Using EMG for &quot;SuperNatural&quot; Interaction in VR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Can natural interaction requirements be fulfilled while still harnessing the \"supernatural\" fantasy of Virtual Reality (VR)? In this work we used off the shelf Electromyogram (EMG) sensors as an input device which can afford natural gestures to preform the \"supernatural\" task of growing your arm in VR. We recorded 18 participants preforming a simple retrieval task in two phases; an initial and a learning phase where the stretch arm was disabled and enabled respectively. The results show that the gestures used in the initial phase are different than the main gestures used to retrieve an object in our system and that the times taken to complete the learning phase are highly variable across participants.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266115"}, {"primary_key": "3517180", "vector": [], "sparse_vector": [], "title": "Game Design for Users with Constraint: Exergame for Older Adults with Cognitive Impairment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In order to design serious games, attention needs to be paid to the target users. One important application of serious games is the design of games for older adults with dementia. Interfaces and activities in games designed for this group of users should be conducted by considering both the cognitive and physical limitations of these people, which may be challenging. We overcome these challenges by using the advantages of new head mounted display virtual reality (HMD-VR) technology and the knowledge of experts. The results of a preliminary three-week exercise involving participants with dementia shows that our design approach has been successful in achieving an interesting environment and could engage participants in the game.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266124"}, {"primary_key": "3517181", "vector": [], "sparse_vector": [], "title": "Collaborative Virtual Reality for Low-Latency Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In collaborative virtual environments, users must often perform tasks requiring coordinated action between multiple parties. Some cases are symmetric, in which users work together on equal footing, while others are asymmetric, in which one user may have more experience or capabilities than another (e.g., one may guide another in completing a task). We present a multi-user virtual reality system that supports interactions of both these types. Two collaborating users, whether co-located or remote, simultaneously manipulate the same virtual objects in a physics simulation, in tasks that require low latency networking to perform successfully. We are currently applying this approach to motor rehabilitation, in which a therapist and patient work together.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271643"}, {"primary_key": "3517182", "vector": [], "sparse_vector": [], "title": "Mixed-Reality for Object-Focused Remote Collaboration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we outline the design of a mixed-reality system to support object-focused remote collaboration. Here, being able to adjust collaborators' perspectives on the object as well as understand one another's perspective is essential to support effective collaboration over distance. We propose a low-cost mixed-reality system that allows users to: (1) quickly align and understand each other's perspective; (2) explore objects independently from one another, and (3) render gestures in the remote's workspace. In this work, we focus on the expert's role and we introduce an interaction technique allowing users to quickly manipulation 3D virtual objects in space.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266102"}, {"primary_key": "3517183", "vector": [], "sparse_vector": [], "title": "The Exploratory Labeling Assistant: Mixed-Initiative Label Curation with Large Document Collections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we define the concept of exploratory labeling: the use of computational and interactive methods to help analysts categorize groups of documents into a set of unknown and evolving labels. While many computational methods exist to analyze data and build models once the data is organized around a set of predefined categories or labels, few methods address the problem of reliably discovering and curating such labels in the first place. In order to move first steps towards bridging this gap, we propose an interactive visual data analysis method that integrates human-driven label ideation, specification and refinement with machine-driven recommendations. The proposed method enables the user to progressively discover and ideate labels in an exploratory fashion and specify rules that can be used to automatically match sets of documents to labels. To support this process of ideation, specification, as well as evaluation of the labels, we use unsupervised machine learning methods that provide suggestions and data summaries. We evaluate our method by applying it to a real-world labeling problem as well as through controlled user studies to identify and reflect on patterns of interaction emerging from exploratory labeling activities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242596"}, {"primary_key": "3517184", "vector": [], "sparse_vector": [], "title": "Ownershift: Facilitating Overhead Interaction in Virtual Reality with an Ownership-Preserving Hand Space Shift.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Ownershift, an interaction technique for easing overhead manipulation in virtual reality, while preserving the illusion that the virtual hand is the user's own hand. In contrast to previous approaches, this technique does not alter the mapping of the virtual hand position for initial reaching movements towards the target. Instead, the virtual hand space is only shifted gradually if interaction with the overhead target requires an extended amount of time. While users perceive their virtual hand as operating overhead, their physical hand moves gradually to a less strained position at waist level. We evaluated the technique in a user study and show that Ownershift significantly reduces the physical strain of overhead interactions, while only slightly reducing task performance and the sense of body ownership of the virtual hand.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242594"}, {"primary_key": "3517185", "vector": [], "sparse_vector": [], "title": "SweatSponse: Closing the Loop on Notification Delivery Using Skin Conductance Responses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today\"s smartphone notification systems are incapable of determining whether a notification has been successfully perceived without explicit interaction from the user. When the system incorrectly assumes that a notification has not been perceived, it may repeat it redundantly, disrupting the user (e.g., phone ringing). Or, when it assumes that a notification was perceived, and therefore fails to repeat it, the notification will be missed altogether (e.g., text message). We introduce SweatSponse, a feedback loop using skin conductance responses (SCR) to infer the perception of smartphone notifications just after their presentation. Early results from a laboratory study suggest that notifications induce SCR and that they could be used to better infer perception of smartphone notifications in real-time.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266084"}, {"primary_key": "3517186", "vector": [], "sparse_vector": [], "title": "The Right Content at the Right Time: Contextual Examples for Just-in-time Creative Learning.", "authors": ["<PERSON><PERSON>"], "summary": "People often run into barriers when doing creative tasks with software because it is difficult to translate goals into concrete actions. While expert-made tutorials, examples, and documentation abound online, finding the most relevant content and adapting it to one's own situation and task is a challenge. My research introduces techniques for exposing relevant examples to novices in the context of their own workflows. These techniques are embodied in three systems. The first, RePlay, helps people find solutions when stuck by automatically locating relevant moments from expert-made videos. The second, DiscoverySpace, helps novices get started by mining and recommending expert-made software macros. The third, CritiqueKit, helps novices improve their work by providing ambient guidance and recommendations. Preliminary experiments with RePlay suggest that contextual video clips help people complete targeted tasks. Controlled experiments with DiscoverySpace and CritiqueKit demonstrate that software macros prevent novices from losing confidence, and ambient guidance improves novice output. My research illustrates the power of user communities to support creative learning.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266127"}, {"primary_key": "3517187", "vector": [], "sparse_vector": [], "title": "SilentVoice: Unnoticeable Voice Input by Ingressive Speech.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "SilentVoice is a new voice input interface device that penetrates the speech-based natural user interface (NUI) in daily life. The proposed \"ingressive speech\" method enables placement of a microphone very close to the front of the mouth without suffering from pop-noise, capturing very soft speech sounds with a good S/N ratio. It realizes ultra-small (less than 39dB(A)) voice leakage, allowing us to use voice input without annoying surrounding people in public and mobile situations as well as offices and homes. By measuring airflow direction, SilentVoice can easily be separated from normal utterances with 98.8% accuracy; no activation words are needed. It can be used for voice-activated systems with a specially trained voice recognizer; evaluation results yield word error rates (WERs) of 1.8% (speaker-dependent condition), and 7.0% (speaker-independent condition) with a limited dictionary of 85 command sentences. A whisper-like natural voice can also be used for real-time voice communication.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242603"}, {"primary_key": "3517188", "vector": [], "sparse_vector": [], "title": "CrowdMuse: An Adaptive Crowd Brainstorming System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online crowds, with their large numbers and diversity, show great potential for creativity, particularly during large-scale brainstorming sessions. Research has explored different ways of augmenting this creativity, such as showing ideators some form of inspiration to get them to explore more categories or generate more ideas. The mechanisms used to select which inspirations are shown to ideators thus far have been focused on characteristics of the inspirations rather than on ideators. This can hinder their effect, as creativity research has shown that ideators have unique cognitive structures and may therefore be better inspired by some ideas rather than others. We introduce CrowdMuse, an adaptive system for supporting large scale brainstorming. The system models ideators based on their past ideas and adapts the system views and inspiration mechanisms accordingly. An evaluation of this system could inform how to better individually support ideators.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266112"}, {"primary_key": "3517189", "vector": [], "sparse_vector": [], "title": "A Stretch-Flexible Textile Multitouch Sensor for User Input on Inflatable Membrane Structures &amp; Non-Planar Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a textile sensor, capable of detecting multi-touch and multi-pressure input on non-planar surfaces and demonstrate how such sensors can be fabricated and integrated into pressure stabilized membrane envelopes (i.e. inflatables). Our sensor design is both stretchable and flexible/bendable and can conform to various three-dimensional surface geometries and shape-changing surfaces. We briefly outline an approach for basic signal acquisition from such sensors and how they can be leveraged to measure internal air-pressure of inflatable objects without specialized air-pressure sensors. We further demonstrate how standard electronic circuits can be integrated with malleable inflatable objects without the need for rigid enclosures for mechanical protection.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271647"}, {"primary_key": "3517190", "vector": [], "sparse_vector": [], "title": "Indutivo: Contact-Based, Object-Driven Interactions with Inductive Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present Indutivo, a contact-based inductive sensing technique for contextual interactions. Our technique recognizes conductive objects (metallic primarily) that are commonly found in households and daily environments, as well as their individual movements when placed against the sensor. These movements include sliding, hinging, and rotation. We describe our sensing principle and how we designed the size, shape, and layout of our sensor coils to optimize sensitivity, sensing range, recognition and tracking accuracy. Through several studies, we also demonstrated the performance of our proposed sensing technique in environments with varying levels of noise and interference conditions. We conclude by presenting demo applications on a smartwatch, as well as insights and lessons we learned from our experience.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242662"}, {"primary_key": "3517191", "vector": [], "sparse_vector": [], "title": "Artificial Motion Guidance: an Intuitive Device based on Pneumatic Gel Muscle (PGM).", "authors": ["<PERSON><PERSON><PERSON>", "S<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a wearable soft exoskeleton sleeve based on PGM. The sleeve consists of 4 PGMs is controlled by a computing system and can actuate 4 different movements (hand extension, flexion, pronation and supination). Depending on how strong the actuation is, the user feels a slight force (haptic feedback) or the hand moves (if the users relaxes the muscles). The paper gives details about the system implementation, the interaction space and some ideas about application scenarios.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271644"}, {"primary_key": "3517192", "vector": [], "sparse_vector": [], "title": "Crowd-AI Systems for Non-Visual Information Access in the Real World.", "authors": ["<PERSON><PERSON>"], "summary": "The world is full of information, interfaces and environments that are inaccessible to blind people. When navigating indoors, blind people are often unaware of key visual information, such as posters, signs, and exit doors. When accessing specific interfaces, blind people cannot independently do so without at least first learning their layout and labeling them with sighted assistance. My work investigates interactive systems that integrates computer vision, on-demand crowdsourcing, and wearables to amplify the abilities of blind people, offering solutions for real-time environment and interface navigation. My work provides more options for blind people to access information and increases their freedom in navigating the world.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266133"}, {"primary_key": "3517193", "vector": [], "sparse_vector": [], "title": "Asterisk and Obelisk: Motion Codes for Passive Tagging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine readable passive tags for tagging physical objects are ubiquitous today. We propose Motion Codes, a passive tagging mechanism that is based on the kinesthetic motion of the user's hand. Here, the tag comprises of a visual pattern that is displayed on a physical surface. To scan the tag and receive the encoded information, the user simply traces their finger over the pattern. The user wears an inertial motion sensing (IMU) ring on the finger that records the traced pattern. We design two motion code schemes, Asterisk and Obelisk that rely on directional vector data processed from the IMU. We evaluate both schemes for the effects of orientation, size, and data density on their accuracies. We further conduct an in-depth analysis of the sources of motion deviations in the ring data as compared to the ground truth finger movement data. Overall, Asterisk achieves a 95% accuracy for an information capacity of 16.8 million possible sequences.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242637"}, {"primary_key": "3517194", "vector": [], "sparse_vector": [], "title": "Designing Inherent Interactions on Wearable Devices.", "authors": ["<PERSON><PERSON>"], "summary": "Wearable devices are becoming important computing devices to personal users. They have shown promising applications in multiple domains. However, designing interactions on smartwears remains challenging as the miniature sized formfactors limit both its input and output space. My thesis research proposes a new paradigm of Inherent Interaction on smartwears, with the idea of seeking interaction opportunities from users daily activities. This is to help bridging the gap between novel smartwear interactions and real-life experiences shared among users. This report introduces the concept of Inherent Interaction with my previous and current explorations in the category.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266130"}, {"primary_key": "3517195", "vector": [], "sparse_vector": [], "title": "HydroRing: Supporting Mixed Reality Haptics Using Liquid Flow.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current haptic devices are often bulky and rigid, making them unsuitable for ubiquitous interaction and scenarios where the user must also interact with the real world. To address this gap, we propose HydroRing, an unobtrusive, finger-worn device that can provide the tactile sensations of pressure, vibration, and temperature on the fingertip, enabling mixed-reality haptic interactions. Different from previous explorations, HydroRing in active mode delivers sensations using liquid travelling through a thin, flexible latex tube worn across the fingerpad, and has minimal impact on a user's dexterity and their perception of stimuli in passive mode. Two studies evaluated participants' ability to perceive and recognize sensations generated by the device, as well as their ability to perceive physical stimuli while wearing the device. We conclude by exploring several applications leveraging this mixed-reality haptics approach.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242667"}, {"primary_key": "3517196", "vector": [], "sparse_vector": [], "title": "Trans-scale Playground: An Immersive Visual Telexistence System for Human Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a novel telexistence system and design methods for telexistence studies to explore spatialscale deconstruction. There have been studies on the experience of dwarf-sized or giant-sized telepresence have been conducted over a period of many years. In this study, we discuss the scale of movements, image transformation, technical components of telepresence robots, and user experiences of telexistence-based spatial transformations. We implemented two types of telepresence robots with an omnidirectional stereo camera setup for a spatial trans-scale experience, wheeled robots, and quadcopters. These telepresence robots provide users with a trans-scale experience for a distance ranging from 15 cm to 30 m. We conducted user studies for different camera positions on robots and for different image transformation method.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266103"}, {"primary_key": "3517197", "vector": [], "sparse_vector": [], "title": "DextrES: Wearable Haptic Feedback for Grasping in VR via a Thin Form-Factor Electrostatic Brake.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce DextrES, a flexible and wearable haptic glove which integrates both kinesthetic and cutaneous feedback in a thin and light form factor (weight is less than 8g). Our approach is based on an electrostatic clutch generating up to 20 N of holding force on each finger by modulating the electrostatic attraction between flexible elastic metal strips to generate an electrically-controlled friction force. We harness the resulting braking force to rapidly render on-demand kinesthetic feedback. The electrostatic brake is mounted onto the the index finger and thumb via modular 3D printed articulated guides which allow the metal strips to glide smoothly. Cutaneous feedback is provided via piezo actuators at the fingertips. We demonstrate that our approach can provide rich haptic feedback under dexterous articulation of the user's hands and provides effective haptic feedback across a variety of different grasps. A controlled experiment indicates that DextrES improves the grasping precision for different types of virtual objects. Finally, we report on results of a psycho-physical study which identifies discrimination thresholds for different levels of holding force.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242657"}, {"primary_key": "3517198", "vector": [], "sparse_vector": [], "title": "Towards a Symbiotic Human-Machine Depth Sensor: Exploring 3D Gaze for Object Reconstruction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Eye tracking is expected to become an integral part of future augmented reality (AR) head-mounted displays (HMDs) given that it can easily be integrated into existing hardware and provides a versatile interaction modality. To augment objects in the real world, AR HMDs require a three-dimensional understanding of the scene, which is currently solved using depth cameras. In this work we aim to explore how 3D gaze data can be used to enhance scene understanding for AR HMDs by envisioning a symbiotic human-machine depth camera, fusing depth data with 3D gaze information. We present a first proof of concept, exploring to what extend we are able to recognise what a user is looking at by plotting 3D gaze data. To measure 3D gaze, we implemented a vergence-based algorithm and built an eye tracking setup consisting of a Pupil Labs headset and an OptiTrack motion capture system, allowing us to measure 3D gaze inside a 50x50x50 cm volume. We show first 3D gaze plots of \"gazed-at\" objects and describe our vision of a symbiotic human-machine depth camera that combines a depth camera and human 3D gaze information.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266119"}, {"primary_key": "3517199", "vector": [], "sparse_vector": [], "title": "FDSense: Estimating Young&apos;s <PERSON><PERSON><PERSON> and <PERSON><PERSON>ness of End Effectors to Facilitate Kinetic Interaction on Touch Surfaces.", "authors": ["Sanghwa Hong", "<PERSON><PERSON><PERSON><PERSON>", "Seongkook Heo", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We make touch input by physically colliding an end effector (e.g., a body part or a stylus) with a touch surface. Prior studies have examined the use of kinematic variables of collision between objects, such as position, velocity, force, and impact. However, the nature of the collision can be understood more thoroughly by considering the known physical relationships that exist between directly measurable variables (i.e., kinetics). Based on this collision kinetics, this study proposes a novel touch technique called FDSense. By simultaneously observing the force and contact area measured from the touchpad, FDSense allows estimation of the Young's modulus and stiffness of the object being contacted. Our technical evaluation showed that FDSense could effectively estimate the Young's modulus of end effectors made of various materials, and the stiffness of each part of the human hand. Two applications using FDSense were demonstrated, for digital painting and digital instruments, where the result of the expression varies significantly depending on the elasticity of the end effector. In a following informal study, participants assessed the technique positively.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242644"}, {"primary_key": "3517200", "vector": [], "sparse_vector": [], "title": "Orecchio: Extending Body-Language through Actuated Static and Dynamic Auricular Postures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiang &apos;Anthony&apos; Chen", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose using the auricle - the visible part of the ear - as a means of expressive output to extend body language to convey emotional states. With an initial exploratory study, we provide an initial set of dynamic and static auricular postures. Using these results, we examined the relationship between emotions and auricular postures, noting that dynamic postures involving stretching the top helix in fast (e.g., 2Hz) and slow speeds (1Hz) conveyed intense and mild pleasantness while static postures involving bending the side or top helix towards the center of the ear were associated with intense and mild unpleasantness. Based on the results, we developed a prototype (called Orrechio) with miniature motors, custom-made robotic arms and other electronic components. A preliminary user evaluation showed that participants feel more comfortable using expressive auricular postures with people they are familiar with, and that it is a welcome addition to the vocabulary of human body language.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242629"}, {"primary_key": "3517201", "vector": [], "sparse_vector": [], "title": "SynchronizAR: Instant Synchronization for Spontaneous and Spatial Collaborations in Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>"], "summary": "We present SynchronizAR, an approach to spatially register multiple SLAM devices together without sharing maps or involving external tracking infrastructures. SynchronizAR employs a distance based indirect registration which resolves the transformations between the separate SLAM coordinate systems. We attach an Ultra-Wide Bandwidth~(UWB) based distance measurements module on each of the mobile AR devices which is capable of self-localization with respect to the environment. As users move on independent paths, we collect the positions of the AR devices in their local frames and the corresponding distance measurements. Based on the registration, we support to create a spontaneous collaborative AR environment to spatially coordinate users' interactions. We run both technical evaluation and user studies to investigate the registration accuracy and the usability towards spatial collaborations. Finally, we demonstrate various collaborative AR experience using SynchronizAR.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242595"}, {"primary_key": "3517202", "vector": [], "sparse_vector": [], "title": "Multitasking with Play Write, a Mobile Microproductivity Writing Tool.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile devices offer people the opportunity to get useful tasks done during time previously thought to be unusable. Because mobile devices have small screens and are often used in divided attention scenarios, people are limited to using them for short, simple tasks; complex tasks like editing a document present significant challenges in this environment. In this paper we demonstrate how a complex task requiring focused attention can be adapted to the fragmented way people work while mobile by decomposing the task into smaller, simpler microtasks. We introduce Play Write, a microproductivity tool that allows people to edit Word documents from their phones via such microtasks. When participants used Play Write while simultaneously watching a video, we found that they strongly preferred its microtask-based editing approach to the traditional editing experience offered by Mobile Word. Play Write made participants feel more productive and less stressed, and they completed more edits with it. Our findings suggest microproductivity tools like Play Write can help people be productive in divided attention scenarios.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242611"}, {"primary_key": "3517203", "vector": [], "sparse_vector": [], "title": "Gaze-guided Image Classification for Reflecting Perceptual Class Ambiguity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite advances in machine learning and deep neural networks, there is still a huge gap between machine and human image understanding. One of the causes is the annotation process used to label training images. In most image categorization tasks, there is a fundamental ambiguity between some image categories and the underlying class probability differs from very obvious cases to ambiguous ones. However, current machine learning systems and applications usually work with discrete annotation processes and the training labels do not reflect this ambiguity. To address this issue, we propose an new image annotation framework where labeling incorporates human gaze behavior. In this framework, gaze behavior is used to predict image labeling difficulty. The image classifier is then trained with sample weights defined by the predicted difficulty. We demonstrate our approach's effectiveness on four-class image classification tasks.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266090"}, {"primary_key": "3517204", "vector": [], "sparse_vector": [], "title": "Wireless Analytics for 3D Printed Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the first wireless physical analytics system for 3D printed objects using commonly available conductive plastic filaments. Our design can enable various data capture and wireless physical analytics capabilities for 3D printed objects, without the need for electronics. To achieve this goal, we make three key contributions: (1) demonstrate room scale backscatter communication and sensing using conductive plastic filaments, (2) introduce the first backscatter designs that detect a variety of bi-directional motions and support linear and rotational movements, and (3) enable data capture and storage for later retrieval when outside the range of the wireless coverage, using a ratchet and gear system. We validate our approach by wirelessly detecting the opening and closing of a pill bottle, capturing the joint angles of a 3D printed e-NABLE prosthetic hand, and an insulin pen that can store information to track its use outside the range of a wireless receiver.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242639"}, {"primary_key": "3517205", "vector": [], "sparse_vector": [], "title": "Pop-up Robotics: Facilitating HRI in Public Spaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human-Robot Interaction (HRI) research in public spaces often encounters delays and restrictions due to several factors, including the need for sophisticated technology, regulatory approvals, and public or community support. To remedy these concerns, we suggest HRI can apply the core philosophy of Tactical Urbanism, a concept from urban planning, to catalyze HRI in public spaces, provide community feedback and information on the feasibility of future implementations of robots in the public, and also create social impact and forge connections with the community while spreading awareness about robots as a public resource. As a case study, we share tactics used and strategies followed to conduct a pop-up style study of 'A robotic mailbox to support and raise awareness about homelessness.' We discuss benefits and challenges of the pop-up approach and recommend using it to enable the social studies of HRI not only to match but to precede, the fast-paced technological advancement and deployment of robots.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266125"}, {"primary_key": "3517206", "vector": [], "sparse_vector": [], "title": "DynamicSlide: Reference-based Interaction Techniques for Slide-based Lecture Videos.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hijung Valentina Shin", "<PERSON><PERSON>"], "summary": "Presentation slides play an important role in online lecture videos. Slides convey the main points of the lecture visually, while the instructor's narration adds detailed verbal explanations to each item in the slide. We call the link between a slide item and the corresponding part of the narration a reference. In order to assess the feasibility of reference-based interaction techniques for watching videos, we introduce DynamicSlide, a video processing system that automatically extracts references from slide-based lecture videos and a video player. The system incorporates a set of reference-based techniques: emphasizing the current item in the slide that is being explained, enabling item-based navigation, and enabling item-based note-taking. Our pipeline correctly finds 79% of the references in a set of five videos with 141 references. Results from a user study suggest that DynamicSlide's features improve the learner's video browsing and navigation experience.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266089"}, {"primary_key": "3517207", "vector": [], "sparse_vector": [], "title": "Haptopus: Haptic VR Experience Using Suction Mechanism Embedded in Head-mounted Display.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the spread of VR experiences using HMD, many proposals have been made to improve the experiences by providing tactile information to the fingertips. However, there are problems, such as difficulty attaching and detaching the devices and hindrances to free finger movement. To solve these issues, we developed \"Haptopus,\" which embeds a tactile display in the HMD and presents tactile sensations to the face. In this paper, we conducted a preliminary investigation on the best suction pressure and compared Haptopus to conventional tactile presentation approaches. As a result, we confirmed that Haptopus improves the quality of the VR experience.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271634"}, {"primary_key": "3517208", "vector": [], "sparse_vector": [], "title": "Head Pose Classification by using Body-Conducted Sound.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vibrations generated by human activity have been used for recognizing human behavior and developing user interfaces; however, it is difficult to estimate static poses that do not generate a vibration. This can be solved using active acoustic sensing; however, this method is not suitable for emitting some vibrations around the head in terms of the influence of audition. Therefore, we propose a method for estimating head poses using body-conducted sound naturally and regularly generated in the human body. The support vector classification recognizes vertical and horizontal directions of the head, and we confirmed the feasibility of the proposed method through experiments.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266094"}, {"primary_key": "3517209", "vector": [], "sparse_vector": [], "title": "I Know What You Want: Using Gaze Metrics to Predict Personal Interest.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "In daily communications, we often use interpersonal cues - telltale facial expressions and body language - to moderate responses to our conversation partners. While we are able to interpret gaze as a sign of interest or reluctance, conventional user interfaces do not yet possess this possible benefit. In our work, we evaluate to what degree fixation-based gaze metrics can be used to infer a user's personal interest in the displayed content. We report on a study (N=18) where participants were presented with a grid array of different images, whilst being recorded for gaze behavior. Our system calculated a ranking for shown images based on gaze metrics. We found that all metrics are effective indicators of the participants' interest by analyzing their agreement with regard to the system's ranking. In an evaluation in a museum, we found that this translates to in-the-wild scenarios despite environmental constraints, such as limited data accuracy.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266116"}, {"primary_key": "3517210", "vector": [], "sparse_vector": [], "title": "PrintMotion: Actuating Printed Objects Using Actuators Equipped in a 3D Printer.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel use for desktop 3D printers using actuators equipped in the printers. The actuators control an extruder and a build-plate mounted on a fused deposition modeling (FDM) 3D printer, moving them horizontally or vertically. Our technique enables actuation of 3D-printed objects on the build-plate by controlling the actuators, and people can interact with them by connecting interface devices to the 3D printer. In this work, we describe how to actuate printed objects using the actuators and present several objects illustrated by our technique.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271627"}, {"primary_key": "3517211", "vector": [], "sparse_vector": [], "title": "resources2city Explorer: A System for Generating Interactive Walkable Virtual Cities out of File Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present resources2city Explorer (R2CE), a tool for representing file systems as interactive, walkable virtual cities. R2CE visualizes file systems based on concepts of spatial, 3D information processing. For this purpose, it extends the range of functions of conventional file browsers considerably. Visual elements in a city generated by R2CE represent (relations of) objects of the underlying file system. The paper describes the functional spectrum of R2CE and illustrates it by visualizing a sample of 940 files.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266122"}, {"primary_key": "3517212", "vector": [], "sparse_vector": [], "title": "Fostering Design Process of Shape-Changing Interfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Kim"], "summary": "Shape-changing interfaces match forms and haptics with functions and bring affordances to devices. I believe that shape-changing interfaces will be increasingly available to end-users in the future. To increase acceptance of shape-changing interfaces by end-users, we need to provide designers with design criteria and framework closely grounded on their current skills and needs. Also, we need to provide them with prototyping tools to enable quick assessment of ideas in the physical world. In this paper, I introduce the three threads of my Ph.D. research in the direction of providing the design tools. First, I advance existing shape-changing interface taxonomies to broaden design vocabulary and systemize design framework, based on the classification of everyday objects. Second, I conduct a study with end-users to suggest interaction techniques and design guidelines for shape-changing interfaces from their current practice. Lastly, I develop a physical prototyping tool for shape-changing interfaces to shorten prototyping iterations based on well-known Lego-like bricks.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266131"}, {"primary_key": "3517213", "vector": [], "sparse_vector": [], "title": "Facilitating Document Reading by Linking Text and Tables.", "authors": ["<PERSON><PERSON>", "Enamul <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Document authors commonly use tables to support arguments presented in the text. But, because tables are usually separate from the main body text, readers must split their attention between different parts of the document. We present an interactive document reader that automatically links document text with corresponding table cells. Readers can select a sentence (or tables cells) and our reader highlights the relevant table cells (or sentences). We provide an automatic pipeline for extracting such references between sentence text and table cells for existing PDF documents that combines structural analysis of tables with natural language processing and rule-based matching. On a test corpus of 330 (sentence, table) pairs, our pipeline correctly extracts 48.8% of the references. An additional 30.5% contain only false negatives (FN) errors -- the reference is missing table cells. The remaining 20.7% contain false positives (FP) errors -- the reference includes extraneous table cells and could therefore mislead readers. A user study finds that despite such errors, our interactive document reader helps readers match sentences with corresponding table cells more accurately and quickly than a baseline document reader.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242617"}, {"primary_key": "3517214", "vector": [], "sparse_vector": [], "title": "Touch180: Finger Identification on Mobile Touchscreen using Fisheye Camera and Convolutional Neural Network.", "authors": ["<PERSON><PERSON>", "Keun-Woo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Touch180, a computer vision based solution for identifying fingers on a mobile touchscreen with a fisheye camera and deep learning algorithm. As a proof-of-concept research, this paper focused on robustness and high accuracy of finger identification. We generated a new dataset for Touch180 configuration, which is named as Fisheye180. We trained a CNN (Convolutional Neural Network)-based network utilizing touch locations as auxiliary inputs. With our novel dataset and deep learning algorithm, finger identification result shows 98.56% accuracy with VGG16 model. Our study will serve as a step stone for finger identification on a mobile touchscreen.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266091"}, {"primary_key": "3517215", "vector": [], "sparse_vector": [], "title": "FTIR-based Touch Pad for Smartphone-based HMD Enhancement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose to equip smartphone-based HMDs (SbHMDs) with an additional touch pad. SbHMDs are a low cost approach to allowing users to experience virtual reality (VR). Current SbHMDs, however, provide poor input functionality and sometimes external devices are necessary to enhance the VR experience. Our proposal uses frustrated total internal reflection (FTIR) to realize a touch pad on the external surfaces of the HMD case; no special devices are needed. As simple FTIR approaches do not suit SbHMDs due to the spatial relation between camera and light, we design an arrangement of acrylic plates and mirror suitable for smartphone's built-in camera and torch-light. It extends the input vocabulary SbHMDs to include touch location, gestures, and also pressure.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271641"}, {"primary_key": "3517216", "vector": [], "sparse_vector": [], "title": "Pushables: A DIY Approach for Fabricating Customizable and Self-Contained Tactile Membrane Dome Switches.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Momentary switches are important building blocks to prototype novel physical user interfaces and enable tactile, explicit and eyes-free interactions. Unfortunately, typical representatives, such as push-buttons or pre-manufactured membrane switches, often do not fulfill individual design requirements and lack customization options for rapid prototyping. With this work, we present Pushables, a DIY fabrication approach for producing thin, bendable and highly customizable membrane dome switches. Therefore, we contribute a three-stage fabrication pipeline that describes the production and assembly on the basis of prototyping methods with different skill levels making our approach suitable for technology-enthusiastic makers, researchers, fab labs and others who require custom membrane switches in small quantities. To demonstrate the wide applicability of Pushables, we present application examples from ubiquitous, mobile and wearable computing.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266082"}, {"primary_key": "3517217", "vector": [], "sparse_vector": [], "title": "DroneCTRL: A Tangible Remote Input Control for Quadcopters.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent research has presented quadcopters to enable mid-air interaction. Using quadcopters to provide tactile feedback, navigation, or user input are the current scope of related work. However, most quadcopter steering systems are complicated to use for non-expert users or require an expensive tracking system for autonomous flying. Safety-critical scenarios require trained and expensive personnel to navigate quadcopters through crucial flight paths within narrow spaces. To simplify the input and manual operation of quadcopters, we present DroneCTRL, a tangible pointing device to navigate quadcopters. DroneCTRL resembles a remote control including optional visual feedback by a laser pointer and tangibility to improve the quadcopter control usability for non-expert users. In a preliminary user study, we compare the efficiency of hardware and software-based controller with DroneCTRL. Our results favor the usage of DroneCTRL with and without visual feedback to achieve more precision and accuracy.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266121"}, {"primary_key": "3517218", "vector": [], "sparse_vector": [], "title": "Effects of an Adaptive Modality Selection Algorithm for Navigation Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Portable electronic navigation systems are often used for directional guidance when humans need to navigate terrain quickly and accurately. Prior work in this field has focused on using either the visual or haptic sensory modality for providing such guidance, and results have indicated that either option may be preferable depending upon the user's specific needs. However, conventional methods involve selecting a single modality based on which will work best with the task the user is most likely to perform and using this modality throughout the duration of the navigation. In this paper, we describe the design and results of a study intended to evaluate the effectiveness of an adaptive modality selection algorithm that dynamically selects a navigation system's directional guidance modality while considering both task-specific benefits and the time-varying effects of switching cost, stimulus-specific adaptation, and habituation. Our findings indicate that use of this algorithm can improve user performance in the presence of multiple simultaneous tasks.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242610"}, {"primary_key": "3517219", "vector": [], "sparse_vector": [], "title": "TrussFormer: 3D Printing Large Kinetic Structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Nico Ring", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present TrussFormer, an integrated end-to-end system that allows users to 3D print large-scale kinetic structures, i.e., structures that involve motion and deal with dynamic forces. TrussFormer builds on TrussFab, from which it inherits the ability to create static large-scale truss structures from 3D printed connectors and PET bottles. TrussFormer adds movement to these structures by placing linear actuators into them: either manually, wrapped in reusable components called assets, or by demonstrating the intended movement. TrussFormer verifies that the resulting structure is mechanically sound and will withstand the dynamic forces resulting from the motion. To fabricate the design, <PERSON>russFormer generates the underlying hinge system that can be printed on standard desktop 3D printers. We demonstrate <PERSON>russ<PERSON>orm<PERSON> with several example objects, including a 6 legged walking robot and a 4m tall animatronics dinosaur with 5 degrees of freedom.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242607"}, {"primary_key": "3517220", "vector": [], "sparse_vector": [], "title": "Engagement Learning: Expanding Visual Knowledge by Engaging Online Participants.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Fei-Fei", "<PERSON>"], "summary": "Most artificial intelligence (AI) systems to date have focused entirely on performance, and rarely if at all on their social interactions with people and how to balance the AIs' goals against their human collaborators'. Learning quickly from interactions with people poses both social challenges and is unresolved technically. In this paper, we introduce engagement learning: a training approach that learns to trade off what the AI needs---the knowledge value of a label to the AI---against what people are interested to engage with---the engagement value of the label. We realize our goal with ELIA (Engagement Learning Interaction Agent), a conversational AI agent who's goal is to learn new facts about the visual world by asking engaging questions of people about the photos they upload to social media. Our current deployment of ELIA on Instagram receives a response rate of 26%.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266110"}, {"primary_key": "3517221", "vector": [], "sparse_vector": [], "title": "EyeExpress: Expanding Hands-free Input Vocabulary using Eye Expressions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The muscles surrounding the human eye are capable of performing a wide range of expressions such as squinting, blinking, frowning, and raising eyebrows. This work explores the use of these ocular expressions to expand the input vocabularies of hands-free interactions. We conducted a series of user studies: 1) to understand which eye expressions users could consistently perform among all possible expressions, 2) to explore how these expressions can be used for hands-free interactions through a user-defined design process. Our study results showed that most participants could consistently perform 9 of the 18 possible eye expressions. Also, in the user define study the participants used the eye expressions to create hands-free interactions for the state-of-the-art augmented reality (AR) head-mounted displays.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266123"}, {"primary_key": "3517222", "vector": [], "sparse_vector": [], "title": "Active Authentication on Smartphone using Touch Pressure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smartphone user authentication is still an open challenge because the balance between both security and usability is indispensable. To balance between them, active authentication is one way to overcome the problem. In this paper, we tackle to improve the accuracy of active authentication by adopting online learning with touch pressure. In recent years, it becomes easy to use the smartphones equipped with pressure sensor so that we have confirmed the effectiveness of adopting the touch pressure as one of the features to authenticate. Our experiments adopting online AROW algorithm with touch pressure show that equal error rate (EER), where the miss rate and false rate are equal, is reduced up to one-fifth by adding touch pressure feature. Moreover, we have confirmed that training with the data from both sitting posture and prone posture archives the best when testing variety of postures including sitting, standing and prone, which achieves EER up to 0.14%.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266113"}, {"primary_key": "3517223", "vector": [], "sparse_vector": [], "title": "Blocks-to-CAD: A Cross-Application Bridge from Minecraft to 3D Modeling.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Learning a new software application can be a challenge, requiring the user to enter a new environment where their existing knowledge and skills do not apply, or worse, work against them. To ease this transition, we propose the idea of cross-application bridges that start with the interface of a familiar application, and gradually change their interaction model, tools, conventions, and appearance to resemble that of an application to be learned. To investigate this idea, we developed Blocks-to-CAD, a cross-application bridge from Minecraft-style games to 3D solid modeling. A user study of our system demonstrated that our modifications to the game did not hurt enjoyment or increase cognitive load, and that players could successfully apply knowledge and skills learned in the game to tasks in a popular 3D solid modeling application. The process of developing Blocks-to-CAD also revealed eight design strategies that can be applied to design cross-application bridges for other applications and domains.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242602"}, {"primary_key": "3517224", "vector": [], "sparse_vector": [], "title": "Ubicoustics: Plug-and-Play Acoustic Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite sound being a rich source of information, computing devices with microphones do not leverage audio to glean useful insights about their physical and social context. For example, a smart speaker sitting on a kitchen countertop cannot figure out if it is in a kitchen, let alone know what a user is doing in a kitchen - a missed opportunity. In this work, we describe a novel, real-time, sound-based activity recognition system. We start by taking an existing, state-of-the-art sound labeling model, which we then tune to classes of interest by drawing data from professional sound effect libraries traditionally used in the entertainment industry. These well-labeled and high-quality sounds are the perfect atomic unit for data augmentation, including amplitude, reverb, and mixing, allowing us to exponentially grow our tuning data in realistic ways. We quantify the performance of our approach across a range of environments and device categories and show that microphone-equipped computing devices already have the requisite capability to unlock real-time activity recognition comparable to human accuracy.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242609"}, {"primary_key": "3517225", "vector": [], "sparse_vector": [], "title": "InfiniTouch: Finger-Aware Interaction on Fully Touch Sensitive Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smartphones are the most successful mobile devices and offer intuitive interaction through touchscreens. Current devices treat all fingers equally and only sense touch contacts on the front of the device. In this paper, we present InfiniTouch, the first system that enables touch input on the whole device surface and identifies the fingers touching the device without external sensors while keeping the form factor of a standard smartphone. We first developed a prototype with capacitive sensors on the front, the back and on three sides. We then conducted a study to train a convolutional neural network that identifies fingers with an accuracy of 95.78% while estimating their position with a mean absolute error of 0.74cm. We demonstrate the usefulness of multiple use cases made possible with InfiniTouch, including finger-aware gestures and finger flexion state as an action modifier.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242605"}, {"primary_key": "3517226", "vector": [], "sparse_vector": [], "title": "Designing Interactive Behaviours Beyond the Desktop.", "authors": ["<PERSON>"], "summary": "As interactions move beyond the desktop, interactive behaviours (effects of actions as they happen, or once they happen) are becoming increasingly complex. This complexity is due to the variety of forms that objects might take, and the different inputs and sensors capturing information, and the ability to create nuanced responses to those inputs. Current interaction design tools do not support much of this rich behaviour authoring. In my work I create prototyping tools that examine ways in which designers can create interactive behaviours. Thus far, I have created two prototyping tools: Pineal and Astral, which examine how to create physical forms based on a smart object's behaviour, and how to reuse existing desktop infrastructures to author different kinds of interactive behaviour. I also contribute conceptual elements, such as how to create smart objects using mobile devices, their sensors and outputs, instead of using custom electronic circuits, as well as devising evaluation strategies used in HCI toolkit research which directly informs my approach to evaluating my tools.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266132"}, {"primary_key": "3517227", "vector": [], "sparse_vector": [], "title": "Perceptual Switch for Gaze Selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the main drawbacks of the fixation-based gaze interfaces is that they are unable to distinguish top-down attention (or selection, a gaze with a purpose) from stimulus driven bottom-up attention (or navigation, a stare without any intentions) without time durations or unnatural eye movements. We found that using the bistable image called the <PERSON><PERSON>'s cube as a button user interface (UI) helps to remedy the limitation. When users switch two rivaling percepts of the <PERSON><PERSON>'s cube at will, unique eye movements are triggered and these characteristics can be used to indicate a button press or a selecting action. In this paper, we introduce (1) the cognitive phenomenon called \"percept switch\" for gaze interaction, and (2) propose \"perceptual switch\" or the Neck<PERSON>'s cube user interface (UI) which uses \"percept switch\" as the indication of a selection. Our preliminary experiment confirms that perceptual switch can be used to distinguish voluntary gaze selection from random navigation, and discusses that the visual elements of the <PERSON><PERSON>'s cube such as size and biased visual cues could be adjusted for the optimal use of individual users.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266107"}, {"primary_key": "3517228", "vector": [], "sparse_vector": [], "title": "Designing Socially Acceptable Hand-to-Face Input.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable head-mounted displays combine rich graphical output with an impoverished input space. Hand-to-face gestures have been proposed as a way to add input expressivity while keeping control movements unobtrusive. To better understand how to design such techniques, we describe an elicitation study conducted in a busy public space in which pairs of users were asked to generate unobtrusive, socially acceptable hand-to-face input actions. Based on the results, we describe five design strategies: miniaturizing, obfuscating, screening, camouflaging and re-purposing. We instantiate these strategies in two hand-to-face input prototypes, one based on touches to the ear and the other based on touches of the thumbnail to the chin or cheek. Performance assessments characterize time and error rates with these devices. The paper closes with a validation study in which pairs of users experience the prototypes in a public setting and we gather data on the social acceptability of the designs and reflect on the effectiveness of the different strategies.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242642"}, {"primary_key": "3517229", "vector": [], "sparse_vector": [], "title": "Montage: A Video Prototyping System to Reduce Re-Shooting and Increase Re-Usability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Video prototypes help capture and communicate interaction with paper prototypes in the early stages of design. However, designers sometimes find it tedious to create stop-motion videos for continuous interactions and to re-shoot clips as the design evolves. We introduce Montage, a proof-of-concept implementation of a computer-assisted process for video prototyping. Montage lets designers progressively augment video prototypes with digital sketches, facilitating the creation, reuse and exploration of dynamic interactions. Montage uses chroma keying to decouple the prototyped interface from its context of use, letting designers reuse or change them independently. We describe how Montage enhances video prototyping by combining video with digital animated sketches, encourages the exploration of different contexts of use, and supports prototyping of different interaction styles.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242613"}, {"primary_key": "3517230", "vector": [], "sparse_vector": [], "title": "Self-Powered Gesture Recognition with Ambient Light.", "authors": ["<PERSON><PERSON>", "T<PERSON>xing Li", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a self-powered module for gesture recognition that utilizes small, low-cost photodiodes for both energy harvesting and gesture sensing. Operating in the photovoltaic mode, photodiodes harvest energy from ambient light. In the meantime, the instantaneously harvested power from individual photodiodes is monitored and exploited as clues for sensing finger gestures in proximity. Harvested power from all photodiodes are aggregated to drive the whole gesture-recognition module including the micro-controller running the recognition algorithm. We design robust, lightweight algorithm to recognize finger gestures in the presence of ambient light fluctuations. We fabricate two prototypes to facilitate user's interaction with smart glasses and smart watch. Results show 99.7%/98.3% overall precision/recall in recognizing five gestures on glasses and 99.2%/97.5% precision/recall in recognizing seven gestures on the watch. The system consumes 34.6 µW/74.3 µW for the glasses/watch and thus can be powered by the energy harvested from ambient light. We also test system's robustness under varying light intensities, light directions, and ambient light fluctuations, where the system maintains high recognition accuracy (> 96%) in all tested settings.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242635"}, {"primary_key": "3517231", "vector": [], "sparse_vector": [], "title": "OmniEyeball: Spherical Display Equipped With Omnidirectional Camera And Its Application For 360-Degree Video Communication.", "authors": ["Zhengqing Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hideaki <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose OmniEyeball (OEB), which is a novel interactive 360° image I/O system. It integrates the spherical display system with an omnidirectional camera to enable both capturing the 360° panoramic live streaming video as well as displaying it. We also present its unique application for symmetric 360° video communication by utilizing two OEB terminals, which may solve the narrow field-of-view problem in video communication. In addition, we designed a vision-based touch detection technique as well as some features to support 360° video communication.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266092"}, {"primary_key": "3517232", "vector": [], "sparse_vector": [], "title": "RFIMatch: Distributed Batteryless Near-Field Identification Using RFID-Tagged Magnet-Biased Reed Switches.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jr-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a technique enabling distributed batteryless near-field identification (ID) between two passive radio frequency ID (RFID) tags. Each conventional ultra-high-frequency (UHF) RFID tag is modified by connecting its antenna and chip to a reed switch and then attaching a magnet to one of the reed switch's terminals, thus transforming it into an always-on switch. When the two modules approach each other, the magnets counteract each other and turn off both switches at the same time. The coabsence of IDs thus indicates a unique interaction event. In addition to sensing, the module also provides native haptic feedback through magnetic repulsion force, enabling users to perceive the system's state eyes-free, without physical constraints. Additional visual feedback can be provided through an energy-harvesting module and a light emitting diode. This specific hardware design supports contactless, orientation-invariant sensing, with a form factor compact enough for embedded and wearable use in ubiquitous computing applications.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242620"}, {"primary_key": "3517233", "vector": [], "sparse_vector": [], "title": "One Button to Rule Them All: Rendering Arbitrary Force-Displacement Curves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physical buttons provide rich force characteristics during the travel range, which are commonly described in the form of force-displacement curves. These force characteristics play an important role in the users' experiences while pressing a button. However, due to lack of proper tools to dynamically render various force-displacement curves, little literature has tried iterative button design improvement. This paper presents Button Simulator, a low-cost 3D printed physical button capable of displaying any force-displacement curves, with limited average error offset around .034 N. By reading the force-displacement curves of existing push-buttons, we can easily replicate the force characteristics from any buttons onto our Button Simulator. One can even go beyond existing buttons and design non-existent ones as the form of arbitrary force-displacement curves; then use Button Simulator to render the sensation. This project will be open-sourced and the implementation details will be released. Our system can be a useful tool for future researchers, designers, and makers to investigate rich and dynamic button\"s force design.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266118"}, {"primary_key": "3517234", "vector": [], "sparse_vector": [], "title": "Touch+Finger: Extending Touch-based User Interface Capabilities with &quot;Idle&quot; Finger Gestures in the Air.", "authors": ["Hyunchul Lim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Oh", "SoHyun Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present Touch+Finger, a new interaction technique that augments touch input with multi-finger gestures for rich and expressive interaction. The main idea is that while one finger is engaged in a touch event, a user can leverage the remaining fingers, the \"idle\" fingers, to perform a variety of hand poses or in-air gestures to extend touch-based user interface capabilities. To fully understand the use of these idle fingers, we constructed a design space based on conventional touch gestures (i.e., single- and multi-touch gestures) and inter- action period (i.e., before and during touch). Considering the design space, we investigated the possible movement of the idle fingers and developed a total of 20 Touch+Finger gestures. Using ring-like devices to track the motion of the idle fingers in the air, we evaluated the Touch+Finger gestures on both recognition accuracy and ease of use. They were classified with a recognition accuracy of over 99% and received positive and negative comments from 8 participants. We suggested 8 interaction techniques with Touch+Finger gestures that demonstrate extended touch-based user interface capabilities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242651"}, {"primary_key": "3517235", "vector": [], "sparse_vector": [], "title": "Ply: A Visual Web Inspector for Learning from Professional Webpages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Eleanor O&apo<PERSON>;Rourke"], "summary": "While many online resources teach basic web development, few are designed to help novices learn the CSS concepts and design patterns experts use to implement complex visual features. Professional webpages embed these design patterns and could serve as rich learning materials, but their stylesheets are complex and difficult for novices to understand. This paper presents <PERSON>ly, a CSS inspection tool that helps novices use their visual intuition to make sense of professional webpages. We introduce a new visual relevance testing technique to identify properties that have visual effects on the page, which <PERSON><PERSON> uses to hide visually irrelevant code and surface unintuitive relationships between properties. In user studies, <PERSON><PERSON> helped novice developers replicate complex web features 50% faster than those using Chrome Developer Tools, and allowed novices to recognize and explain unfamiliar concepts. These results show that visual inspection tools can support learning from complex professional webpages, even for novice developers.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242660"}, {"primary_key": "3517236", "vector": [], "sparse_vector": [], "title": "Adasa: A Conversational In-Vehicle Digital Assistant for Advanced Driver Assistance Features.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advanced Driver Assistance Systems (ADAS) come equipped on most modern vehicles and are intended to assist the driver and enhance the driving experience through features such as lane keeping system and adaptive cruise control. However, recent studies show that few people utilize these features for several reasons. First, ADAS features were not common until recently. Second, most users are unfamiliar with these features and do not know what to expect. Finally, the interface for operating these features is not intuitive. To help drivers understand ADAS features, we present a conversational in-vehicle digital assistant that responds to drivers' questions and commands in natural language. With the system prototyped herein, drivers can ask questions or command using unconstrained natural language in the vehicle, and the assistant trained by using advanced machine learning techniques, coupled with access to vehicle signals, responds in real-time based on conversational context. Results of our system prototyped on a production vehicle are presented, demonstrating its effectiveness in improving driver understanding and usability of ADAS.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242593"}, {"primary_key": "3517237", "vector": [], "sparse_vector": [], "title": "Learning Design Semantics for Mobile Apps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, researchers have developed black-box approaches to mine design and interaction data from mobile apps. Although the data captured during this interaction mining is descriptive, it does not expose the design semantics of UIs: what elements on the screen mean and how they are used. This paper introduces an automatic approach for generating semantic annotations for mobile app UIs. Through an iterative open coding of 73k UI elements and 720 screens, we contribute a lexical database of 25 types of UI components, 197 text button concepts, and 135 icon classes shared across apps. We use this labeled data to learn code-based patterns to detect UI components and to train a convolutional neural network that distinguishes between icon classes with 94% accuracy. To demonstrate the efficacy of our approach at scale, we compute semantic annotations for the 72k unique UIs in the Rico dataset, assigning labels for 78% of the total visible, non-redundant elements.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242650"}, {"primary_key": "3517238", "vector": [], "sparse_vector": [], "title": "Increasing Walking in VR using Redirected Teleportation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Teleportation is a popular locomotion technique that lets users safely navigate beyond the confines of available positional tracking space without inducing VR sickness. Because available walking space is limited and teleportation is faster than walking, a risk with using teleportation is that users might end up abandoning walking input and only relying on teleportation, which is considered detrimental to presence. We present redirected teleportation; an improved version of teleportation that uses iterative non-obtrusive reorientation and repositioning using a portal to redirect the user back to the center of the tracking space, where available walking space is larger. A user study compares the effectiveness, accuracy, and usability of redirected teleportation with regular teleportation using a navigation task in three different environments. Results show that redirected teleportation allows for a better utilization of available tracking space than regular teleportation, as it requires significantly fewer teleportations, while users walk more and use a larger portion of the available tracking space.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242601"}, {"primary_key": "3517239", "vector": [], "sparse_vector": [], "title": "RollingStone: Using Single Slip Taxel for Enhancing Active Finger Exploration with a Virtual Reality Controller.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose using a single slip tactile pixel on virtual reality controllers to produce sensations of finger sliding and textures. When a user moves the controller on a virtual surface, we add a slip opposite to the movement, creating an illusion of a finger that is sliding on the surface, while varying the slip feedback changes lateral forces on fingertip. When coupled with hand motion the lateral forces can be used to create perceptions of artificial textures. RollingStone has been implemented as a prototype VR controller consisting of a ball-based slip display positioned under the user's fingertip. Within the slip display, a pair of motors actuates the ball, which is capable of gener- ating both short- and long-term two-degree-of-freedom slip feedback. An exploratory study was conducted to ensure that changing the relative motion between the finger and the ball could alter the perceptions conveying the properties of a tex- ture. The following two perception-based studies examined the minimum changes in speed of slip and angle of slip that are detectable by users. The results help us to design haptic patterns as well as our prototype applications. Finally, our preliminary user evaluation indicated that participants wel- comed RollingStone as a useful addition to the range of VR controllers.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242627"}, {"primary_key": "3517240", "vector": [], "sparse_vector": [], "title": "Kaleidoscope: An RDF-based Exploratory Data Analysis Tool for Ideation Outcomes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Evaluating and selecting ideas is a critical and time-consuming step in collaborative ideation, making computational support for this task a desired research goal. However, existing automatic approaches to idea selection might eliminate valuable ideas. In this work we combine automatic approaches with human sensemaking. Kaleidoscope is an exploratory data analytics tool based on semantic technologies. It supports users in exploring and annotating existing ideas interactively. In the following, we present key design principles of Kaleidoscope. Based on qualitative feedback collected on a prototype, we identify potential improvements and describe future work.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266106"}, {"primary_key": "3517241", "vector": [], "sparse_vector": [], "title": "Scenograph: Fitting Real-Walking VR Experiences into Various Tracking Volumes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "When developing a real-walking virtual reality experience, designers generally create virtual locations to fit a specific tracking volume. Unfortunately, this prevents the resulting experience from running on a smaller or differently shaped tracking volume. To address this, we present a software system called Scenograph. The core of Scenograph is a tracking volume-independent representation of real-walking experiences. Scenograph instantiates the experience to a tracking volume of given size and shape by splitting the locations into smaller ones while maintaining narrative structure. In our user study, participants' ratings of realism decreased significantly when existing techniques were used to map a 25m2 experience to 9m2 and an L-shaped 8m2 tracking volume. In contrast, ratings did not differ when Scenograph was used to instantiate the experience.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242648"}, {"primary_key": "3517242", "vector": [], "sparse_vector": [], "title": "Scaling Notifications Beyond Alerts: From Subtly Drawing Attention up to Forcing the User to Take Action.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON>", "Bodo Urban"], "summary": "Research has been done in sophisticated notifications, still, devices today mainly stick to a binary level of information, while they are either attention drawing or silent. We propose scalable notifications, which adjust the intensity level reaching from subtle to obtrusive and even going beyond that level while forcing the user to take action. To illustrate the technical feasibility and validity of this concept, we developed three prototypes. The prototypes provided mechano-pressure, thermal, and electrical feedback, which were evaluated in different lab studies. Our first prototype provides subtle poking through to high and frequent pressure on the user's spine, which significantly improves back posture. In a second scenario, the user is able to perceive the overuse of a drill by an increased temperature on the palm of a hand until the heat is intolerable, forcing the user to eventually put down the tool. The last application comprises of a speed control in a driving simulation, while electric muscle stimulation on the users' legs, conveys information on changing the car's speed by a perceived tingling until the system forces the foot to move involuntarily. In conclusion, all studies' findings support the feasibility of our concept of a scalable notification system, including the system forcing an intervention.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266096"}, {"primary_key": "3517243", "vector": [], "sparse_vector": [], "title": "WiFröst: Bridging the Information Gap for Debugging of Networked Embedded Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise in prevalence of Internet of Things (IoT) technologies has encouraged more people to prototype and build custom internet connected devices based on low power microcontrollers. While well-developed tools exist for debugging network communication for desktop and web applications, it can be difficult for developers of networked embedded systems to figure out why their network code is failing due to the limited output affordances of embedded devices. This paper presents WiFröst, a new approach for debugging these systems using instrumentation that spans from the device itself, to its communication API, to the wireless router and back-end server. WiFröst automatically collects this data, displays it in a web-based visualization, and highlights likely issues with an extensible suite of checks based on analysis of recorded execution traces.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242668"}, {"primary_key": "3517244", "vector": [], "sparse_vector": [], "title": "Juggling 4.0: Learning Complex Motor Skills with Augmented Reality Through the Example of Juggling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning new motor skills is a problem that people are constantly confronted with (e.g. to learn a new kind of sport). In our work, we investigate to which extent the learning process of a motor sequence can be optimized with the help of Augmented Reality as a technical assistant. Therefore, we propose an approach that divides the problem into three tasks: (1) the tracking of the necessary movements, (2) the creation of a model that calculates possible deviations and (3) the implementation of a visual feedback system. To evaluate our approach, we implemented the idea by using infrared depth sensors and an Augmented Reality head-mounted device (HoloLens). Our results show that the system can give an efficient assistance for the correct height of a throw with one ball. Furthermore, it provides a basis for the support of a complete juggling sequence.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266099"}, {"primary_key": "3517245", "vector": [], "sparse_vector": [], "title": "DisplayBowl: A Bowl-Shaped Display for Omnidirectional Videos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce DisplayBowl which is a concept of a bowl shaped hemispherical display for showing omnidirectional images. This display provides three-way observation for omnidirectional images. DisplayBowl allows users to observe an omnidirectional image by looking the image from above. In addition, users can see it with a first-person-viewpoint, by looking into the inside of the hemispherical surface from diagonally above. Furthermore, by observing both the inside and the outside of the hemispherical surface at the same time from obliquely above, it is possible to observe it by a pseudo third-person-viewpoint, like watching the drone obliquely from behind. These ways of viewing solve the problem of inability of pilots controlling a remote vehicle such as a drone to notice what happens behind them, which happen with conventional displays such as flat displays and head mounted displays.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266114"}, {"primary_key": "3517246", "vector": [], "sparse_vector": [], "title": "Wearable Haptic Device that Presents the Haptics Sensation Corresponding to Three Fingers on the Forearm.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this demonstration, as an attempt of a new haptic presentation method for objects in virtual reality (VR) environment, we show a device that presents the haptic sensation of the fingertip on the forearm, not on the fingertip. This device adopts a five-bar linkage mechanism and it is possible to present the strength, direction of force. Compared with a fingertip mounted type displays, it is possible to address the issues of their weight and size which hinder the free movement of fingers. We have confirmed that the experiences in the VR environment is improved compared with without haptics cues situation regardless of without presenting haptics information directly to the fingertip.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271633"}, {"primary_key": "3517247", "vector": [], "sparse_vector": [], "title": "Porta: Profiling Software Tutorials Using Operating-System-Wide Activity Tracing.", "authors": ["Alok Mysore", "<PERSON>"], "summary": "It can be hard for tutorial creators to get fine-grained feedback about how learners are actually stepping through their tutorials and which parts lead to the most struggle. To provide such feedback for technical software tutorials, we introduce the idea of tutorial profiling, which is inspired by software code profiling. We prototyped this idea in a system called Porta that automatically tracks how users navigate through a tutorial webpage and what actions they take on their computer such as running shell commands, invoking compilers, and logging into remote servers. Porta surfaces this trace data in the form of profiling visualizations that augment the tutorial with heatmaps of activity hotspots and markers that expand to show event details, error messages, and embedded screencast videos of user actions. We found through a user study of 3 tutorial creators and 12 students who followed their tutorials that Porta enabled both the tutorial creators and the students to provide more specific, targeted, and actionable feedback about how to improve these tutorials. Porta opens up possibilities for performing user testing of technical documentation in a more systematic and scalable way.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242633"}, {"primary_key": "3517248", "vector": [], "sparse_vector": [], "title": "Next-Point Prediction for Direct Touch Using Finite-Time Derivative Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "End-to-end latency in interactive systems is detrimental to performance and usability, and comes from a combination of hardware and software delays. While these delays are steadily addressed by hardware and software improvements, it is at a decelerating pace. In parallel, short-term input prediction has shown promising results in recent years, in both research and industry, as an addition to these efforts. We describe a new prediction algorithm for direct touch devices based on (i) a state-of-the-art finite-time derivative estimator, (ii) a smoothing mechanism based on input speed, and (iii) a post-filtering of the prediction in two steps. Using both a pre-existing dataset of touch input as benchmark, and subjective data from a new user study, we show that this new predictor outperforms the predictors currently available in the literature and industry, based on metrics that model user-defined negative side-effects caused by input prediction. In particular, we show that our predictor can predict up to 2 or 3 times further than existing techniques with minimal negative side-effects.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242646"}, {"primary_key": "3517249", "vector": [], "sparse_vector": [], "title": "Believe it or not: Designing a Human-AI Partnership for Mixed-Initiative Fact-Checking.", "authors": ["<PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fact-checking, the task of assessing the veracity of claims, is an important, timely, and challenging problem. While many automated fact-checking systems have been recently proposed, the human side of the partnership has been largely neglected: how might people understand, interact with, and establish trust with an AI fact-checking system? Does such a system actually help people better assess the factuality of claims? In this paper, we present the design and evaluation of a mixed-initiative approach to fact-checking, blending human knowledge and experience with the efficiency and scalability of automated information retrieval and ML. In a user study in which participants used our system to aid their own assessment of claims, our results suggest that individuals tend to trust the system: participant accuracy assessing claims improved when exposed to correct model predictions. However, this trust perhaps goes too far: when the model was wrong, exposure to its predictions often degraded human accuracy. Participants given the option to interact with these incorrect predictions were often able improve their own performance. This suggests that transparent models are key to facilitating effective human interaction with fallible AI models.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242666"}, {"primary_key": "3517250", "vector": [], "sparse_vector": [], "title": "Wearable Kinesthetic I/O Device for Sharing Muscle Compliance.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a wearable kinesthetic I/O device, which is able to measure and intervene in multiple muscle activities simultaneously through the same electrodes. The developed system includes an I/O module, capable of measuring the electromyogram (EMG) of four muscle tissues, while applying electrical muscle stimulation (EMS) at the same time. The developed wearable system is configured in a scalable manner for achieving 1) high stimulus frequency (up to 70 Hz), 2) wearable dimensions in which the device can be placed along the limbs, and 3) flexibility of the number of I/O electrodes (up to 32 channels). In a pilot user study, which shared the wrist compliance between two persons, participants were able to recognize the level of their confederate's wrist joint compliance using a 4-point Likert scale. The developed system would benefit a physical therapist and a patient, during hand rehabilitation, using a peg board for sharing their wrist compliance and grip force, which are usually difficult to be observed in a visual contact.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266100"}, {"primary_key": "3517251", "vector": [], "sparse_vector": [], "title": "SoundBender: Dynamic Acoustic Control Behind Obstacles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ultrasound manipulation is growing in popularity in the HCI community with applications in haptics, on-body interaction, and levitation-based displays. Most of these applications share two key limitations: a) the complexity of the sound fields that can be produced is limited by the physical size of the transducers, and b) no obstacles can be present between the transducers and the control point. We present SoundBender, a hybrid system that overcomes these limitations by combining the versatility of phased arrays of Transducers (PATs) with the precision of acoustic metamaterials. In this paper, we explain our approach to design and implement such hybrid modulators (i.e. to create complex sound fields) and methods to manipulate the field dynamically (i.e. stretch, steer). We demonstrate our concept using self-bending beams enabling both levitation and tactile feedback around an obstacle and present example applications enabled by SoundBender.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242590"}, {"primary_key": "3517252", "vector": [], "sparse_vector": [], "title": "Magneto-Haptics: Embedding Magnetic Force Feedback for Physical Interactions.", "authors": ["<PERSON><PERSON>"], "summary": "We present magneto-haptics, a design approach of haptic sensations powered by the forces present among permanent magnets during active touch. Magnetic force has not been efficiently explored in haptic design because it is not intuitive and there is a lack of methods to associate or visualize magnetic force with haptic sensations, especially for complex magnetic patterns. To represent the haptic sensations of magnetic force intuitively, magneto-haptics formularizes haptic potential from the distribution of magnetic force along the path of motion. It provides a rapid way to compute the relationship between the magnetic phenomena and the haptic mechanism. Thus, we can convert a magnetic force distribution into a haptic sensation model, making the design of magnet-embedded haptic sensations more efficient. We demonstrate three applications of magneto-haptics through interactive interfaces and devices. We further verify our theory by evaluating some magneto-haptic designs through experiments.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242615"}, {"primary_key": "3517253", "vector": [], "sparse_vector": [], "title": "Hybrid Watch User Interfaces: Collaboration Between Electro-Mechanical Components and Analog Materials.", "authors": ["<PERSON>"], "summary": "We introduce programmable material and electro-mechanical control to enable a set of hybrid watch user interfaces that symbiotically leverage the joint strengths of electro-mechanical hands and a dynamic watch dial. This approach enables computation and connectivity with existing materials to preserve the inherent physical qualities and abilities of traditional analog watches. We augment the watch's mechanical hands with micro-stepper motors for control, positioning and mechanical expressivity. We extend the traditional watch dial with programmable pigments for non-emissive dynamic patterns. Together, these components enable a unique set of interaction techniques and user interfaces beyond their individual capabilities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271650"}, {"primary_key": "3517254", "vector": [], "sparse_vector": [], "title": "I/O Braid: Scalable Touch-Sensitive Lighted Cords Using Spiraling, Repeating Sensing Textiles and Fiber Optics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce I/O Braid, an interactive textile cord with embedded sensing and visual feedback. I/O Braid senses proximity, touch, and twist through a spiraling, repeating braiding topology of touch matrices. This sensing topology is uniquely scalable, requiring only a few sensing lines to cover the whole length of a cord. The same topology allows us to embed fiber optic strands to integrate co-located visual feedback. We provide an overview of the enabling braiding techniques, design considerations, and approaches to gesture detection. These allow us to derive a set of interaction techniques, which we demonstrate with different form factors and capabilities. Our applications illustrate how I/O Braid can invisibly augment everyday objects, such as touch-sensitive headphones and interactive drawstrings on garments, while enabling discoverability and feedback through embedded light sources.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242638"}, {"primary_key": "3517255", "vector": [], "sparse_vector": [], "title": "I/O Braid: Scalable Touch-Sensitive Lighted Cords Using Spiraling, Repeating Sensing Textiles and Fiber Optics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce I/O Braid, an interactive textile cord with embedded sensing and visual feedback. I/O Braid senses proximity, touch, and twist through a spiraling, repeating braiding topology of touch matrices. This sensing topology is uniquely scalable, requiring only a few sensing lines to cover the whole length of a cord. The same topology allows us to embed fiber optic strands to integrate co-located visual feedback. We provide an overview of the enabling braiding techniques, design considerations, and approaches to gesture detection. These allow us to derive a set of interaction techniques, which we demonstrate with different form factors and capabilities. Our applications illustrate how I/O Braid can invisibly augment everyday objects, such as touch-sensitive headphones and interactive drawstrings on garments, while enabling discoverability and feedback through embedded light sources.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271651"}, {"primary_key": "3517256", "vector": [], "sparse_vector": [], "title": "Arboretum and Arbility: Improving Web Accessibility Through a Shared Browsing Architecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many web pages developed today require navigation by visual interaction-seeing, hovering, pointing, clicking, and dragging with the mouse over dynamic page content. These forms of interaction are increasingly popular as developer trends have moved from static, logically structured pages to dynamic, interactive pages. However, they are also often inaccessible to blind web users who tend to rely on keyboard-based screen readers to navigate the web. Despite existing web accessibility standards, engineering web pages to be equally accessible via both keyboard and visuomotor mouse-based interactions is often not a priority for developers. Improving access to this kind of visual and interactive web content has been a long-standing goal of HCI researchers, but the barriers have proven to be too varied and unpredictable to be overcome by some of the proposed solutions: promoting guidelines and best practices, automatically generating accessible versions of pre-exisiting web pages, or developing human-assisted solutions, such as screen and cursor-sharing, which tend to diminish an end user's agency. In this paper we present a real-time, collaborative approach to helping blind web users overcome inaccessible parts of existing web pages. We introduce *Arboretum*, a new architecture that enables any web user to seamlessly hand off controlled parts of their browsing session to remote users, while maintaining control over the interface via a \"propose and accept/reject\" mechanism. We illustrate the benefit of Arboretum by using it to implement *Arbility*, a browser that allows blind users to hand off targeted visual interaction tasks to remote crowd workers. We evaluate the entire system in a study with 9 blind web users, showing that Arbility allows them to interact with web content that was previously difficult to access via a screen reader alone.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242649"}, {"primary_key": "3517257", "vector": [], "sparse_vector": [], "title": "The Immersive Bubble Chart: a Semantic and Virtual Reality Visualization for Big Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Telmo <PERSON>", "<PERSON>"], "summary": "In this paper, we introduce the Immersive Bubble Chart, a visualization for hierarchical datasets presented in a virtual reality (VR) world. Users get immersed into the visualization and interact with the bubbles using gestures with a view to overcoming some limitations of 2D visualizations due to the capabilities and interaction affordances of the devices. The technological advances in VR give the possibility to design malleable and extensible representations and more natural and engaging interactions. Using the Oculus Touch controllers, the users can grab and move the bubbles, throw them away or bump two of them for creating a cluster. We have tested the Immersive Bubble Chart with the hierarchical clusters of semantically related terms generated from Twitter.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271642"}, {"primary_key": "3517258", "vector": [], "sparse_vector": [], "title": "Unlimited Electric Gum: A Piezo-based Electric Taste Apparatus Activated by Chewing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Herein, we propose \"unlimited electric gum,\" an electric taste device that will enable users to perceive taste for as long the user is chewing the gum. We developed an in-mouth type novel electric taste-imparting apparatus using a piezoelectric element so that the piezoelectric effect is stimulated by chewing. This enabled the design of a device that does not require cables around a user's lips or batteries in their mouth. In this paper, we introduce this device and report our experimental and exhibition results.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271635"}, {"primary_key": "3517259", "vector": [], "sparse_vector": [], "title": "Knobology 2.0: Giving <PERSON><PERSON>pe to the Haptic Force Feedback of Interactive Knobs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present six rotary knobs, each with a distinct shape, that provide haptic force feedback on rotation. The knob shapes were evaluated in relation to twelve haptic feedback stimuli. The stimuli were designed as a combination of the most relevant perceptual parameters of force feedback; acceleration, friction, detent amplitude and spacing. The results indicate that there is a relationship between the shape of a knob and its haptic feedback. The perceived functionality can be dynamically altered by changing its shape and haptic feedback. This work serves as basis for the design of dynamic interface controls that can adapt their shape and haptic feel to the content that is controlled. In our demonstration, we show the six distinct knobs shapes with the different haptic feedback stimuli. Attendees can experience the interaction with the different knob shapes in relation the stimuli and design stimuli with a graphical editor.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271649"}, {"primary_key": "3517260", "vector": [], "sparse_vector": [], "title": "Aalto Interface Metrics (AIM): A Service and Codebase for Computational GUI Evaluation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aalto Interface Metrics (AIM) pools several empirically validated models and metrics of user perception and attention into an easy-to-use online service for the evaluation of graphical user interface (GUI) designs. Users input a GUI design via URL, and select from a list of 17 different metrics covering aspects ranging from visual clutter to visual learnability. AIM presents detailed breakdowns, visualizations, and statistical comparisons, enabling designers and practitioners to detect shortcomings and possible improvements. The web service and code repository are available at interfacemetrics.aalto.fi.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266087"}, {"primary_key": "3517261", "vector": [], "sparse_vector": [], "title": "Non-Linear Editing of Text-Based Screencasts.", "authors": ["Jungkook Park", "Yeong Hoon Park", "<PERSON>"], "summary": "Screencasts, where recordings of a computer screen are broadcast to a large audience on the web, are becoming popular as an online educational tool. To provide rich interactions with the text within screencasts, there are emerging platforms that support text-based screencasts by recording every character insertion and deletion from the creator and reconstructing its playback on the viewer's screen. However, these platforms lack support for non-linear editing of screencasts, which involves manipulating a sequence of text editing operations. Since text editing operations are tightly coupled in sequence, modifying an arbitrary part of the sequence often creates ambiguity that yields multiple possible results that require user's choice for resolution. We present an editing tool with a non-linear editing algorithm for text-based screencasts. The tool allows users to edit any arbitrary part of a text-based screencast while preserving the overall consistency of the screencast. In an exploratory user study, all subjects successfully carried out a variety of screencast editing tasks using our prototype screencast editor.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242654"}, {"primary_key": "3517262", "vector": [], "sparse_vector": [], "title": "Post-literate Programming: Linking Discussion and Code in Software Development Teams.", "authors": ["Soya Park", "<PERSON>", "<PERSON>"], "summary": "The literate programming paradigm presents a program interleaved with natural language text explaining the code's rationale and logic. While this is great for program readers, the labor of creating literate programs deters most program authors from providing this text at authoring time. Instead, as we determine through interviews, developers provide their design rationales after the fact, in discussions with collaborators. We propose to capture these discussions and incorporate them into the code. We have prototyped a tool to link online discussion of code directly to the code it discusses. Incorporating these discussions incrementally creates post-literate programs that convey information to future developers.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266098"}, {"primary_key": "3517263", "vector": [], "sparse_vector": [], "title": "RESi: A Highly Flexible, Pressure-Sensitive, Imperceptible Textile Interface Based on Resistive Yarns.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present RESi (Resistive tExtile Sensor Interfaces), a novel sensing approach enabling a new kind of yarn-based, resistive pressure sensing. The core of RESi builds on a newly designed yarn, which features conductive and resistive properties. We run a technical study to characterize the behaviour of the yarn and to determine the sensing principle. We demonstrate how the yarn can be used as a pressure sensor and discuss how specific issues, such as connecting the soft textile sensor with the rigid electronics can be solved. In addition, we present a platform-independent API that allows rapid prototyping. To show its versatility, we present applications developed with different textile manufacturing techniques, including hand sewing, machine sewing, and weaving. RESi is a novel technology, enabling textile pressure sensing to augment everyday objects with interactive capabilities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242664"}, {"primary_key": "3517264", "vector": [], "sparse_vector": [], "title": "ElectricItch: Skin Irritation as a Feedback Modality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Grabbing users' attention is a fundamental aspect of interactive systems. However, there is a disconnect between the ways our devices notify us and how our bodies do so naturally. In this paper, we explore the body's modality of itching as a way to provide such natural feedback. We create itching sensations via low-current electric stimulation, which allows us to quickly generate this sensation on demand. In a first study we explore the design space around itching and how changes in stimulation parameters influence the resulting sensation. In a second study we compare vibration feedback and itching integrated in a smartwatch form factor. We find that we can consistently induce itching sensations and that these are perceived as more activating and interrupting than vibrotactile stimuli.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242647"}, {"primary_key": "3517265", "vector": [], "sparse_vector": [], "title": "Authoring and Verifying Human-Robot Interactions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As social agents, robots designed for human interaction must adhere to human social norms. How can we enable designers, engineers, and roboticists to design robot behaviors that adhere to human social norms and do not result in interaction breakdowns? In this paper, we use automated formal-verification methods to facilitate the encoding of appropriate social norms into the interaction design of social robots and the detection of breakdowns and norm violations in order to prevent them. We have developed an authoring environment that utilizes these methods to provide developers of social-robot applications with feedback at design time and evaluated the benefits of their use in reducing such breakdowns and violations in human-robot interactions. Our evaluation with application developers (N=9) shows that the use of formal-verification methods increases designers' ability to identify and contextualize social-norm violations. We discuss the implications of our approach for the future development of tools for effective design of social-robot applications.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242634"}, {"primary_key": "3517266", "vector": [], "sparse_vector": [], "title": "HoloRoyale: A Large Scale High Fidelity Augmented Reality Game.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent years saw an explosion in Augmented Reality (AR) experiences for consumers. These experiences can be classified based on the scale of the interactive area (room vs city/global scale) , or the fidelity of the experience (high vs low). Experiences that target large areas, such as campus or world scale, commonly have only rudimentary interactions with the physical world, and suffer from registration errors and jitter. We classify these experiences as large scale and low fidelity. On the other hand, various room sized experiences feature realistic interaction of virtual content with the real world. We classify these experiences as small scale and high fidelity. Our work is the first to explore the domain of large scale high fidelity (LSHF) AR experiences. We build upon the small scale high fidelity capabilities of the Microsoft HoloLens to allow LSHF interactions. We demonstrate the capabilities of our system with a game specifically designed for LSHF interactions, handling many challenges and limitations unique to the domain of LSHF AR through the game design. Our contributions are twofold: - The lessons learned during the design and development of a system capable of LSHF AR interactions. ­ Identification of a set of reusable game elements specific to LSHF AR, including mechanisms for addressing spatiotemporal inconsistencies and crowd control. \\We believe our contributions will be fully applicable not only to games, but all LSHF AR experiences.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271637"}, {"primary_key": "3517267", "vector": [], "sparse_vector": [], "title": "Robots For Us: Organizational and Community Perspectives on the Collaborative Design of Ubiquitous Robots.", "authors": ["<PERSON><PERSON>"], "summary": "Robots are expected to become ubiquitous in the near future, working with people in various environments, including homes, schools, hospitals, and offices. As physically and socially interactive technologies, robots present new opportunities for embodied interaction and active as well as passive sensing in these contexts. They have also been shown to psychologically impact individuals, affect group and organizational dynamics, and modify our concepts and experiences of work, care, and social relationships. Designing robots for increasingly ubiquitous everyday use requires understanding how robots are perceived, and can be adopted and supported in open-ended, natural social circumstances. This, in turn, calls for design and evaluation methodologies that go beyond the dyadic and small group interactions in laboratories that have largely been the focus of research in human-robot interaction. In this talk, I will present alternative perspectives on the design and evaluation of socially interactive robotic technologies in real-world contexts, focusing on several case studies of socially assistive robots in eldercare. I will first discuss how older adults make sense of robots for use in their homes, in relation to the broader social contexts in which they live, as part of collaborative design activities, and in the course of month-long implementations of robots in their homes. These in-home studies bring up various issues relating to the types of data older adults and the clinicians who work with them would like to collect, related privacy concerns, impacts on other people in the home, and how robot designs can support the relationships older adults hope to have with and through robots. Secondly, I will explore the institutional and community-based use and design of robots in different eldercare facilities, including a nursing home, a retirement community, and an intergenerational daycare. These studies bring out how robots fit into and affect the institutional and group dynamics of interaction, and also allow us to explore how robots might be envisioned as technologies that can support not only individual, but community-level goals. Through these case studies of robots, as emerging ubiquitous interactive technologies, I will bring out themes that can inform the design and study of pervasive systems more broadly, including collaborative design, the use of data collected during social interactions with and around technologies, related ethical concerns, and the need for incorporating the aims of groups, institutions, and communities in the design of intelligent interactive technologies.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3273052"}, {"primary_key": "3517268", "vector": [], "sparse_vector": [], "title": "A WOZ Study of Feedforward Information on an Ambient Display in Autonomous Cars.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We describe the development and user testing of an ambient display for autonomous vehicles. Instead of providing feedback about driving actions, once executed, it communicates driving decisions in advance, via light signals in passengers\" peripheral vision. This ambient display was tested in an WoZ-based on-the-road-driving simulation of a fully autonomous vehicle. Findings from a preliminary study with 14 participants suggest that such a display might be particularly useful to communicate upcoming inertia changes for passengers.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266111"}, {"primary_key": "3517269", "vector": [], "sparse_vector": [], "title": "MetaArms: Body Remapping Using Feet-Controlled Artificial Arms.", "authors": ["<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce MetaArms, wearable anthropomorphic robotic arms and hands with six degrees of freedom operated by the user's legs and feet. Our overall research goal is to re-imagine what our bodies can do with the aid of wearable robotics using a body-remapping approach. To this end, we present an initial exploratory case study. MetaArms' two robotic arms are controlled by the user's feet motion, and the robotic hands can grip objects according to the user's toes bending. Haptic feedback is also presented on the user's feet that correlate with the touched objects on the robotic hands, creating a closed-loop system. We present formal and informal evaluations of the system, the former using a 2D pointing task according to <PERSON>tts' Law. The overall throughput for 12 users of the system is reported as 1.01 bits/s (std 0.39). We also present informal feedback from over 230 users. We find that MetaArms demonstrate the feasibility of body-remapping approach in designing robotic limbs that may help us re-imagine what the human body could do.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242665"}, {"primary_key": "3517270", "vector": [], "sparse_vector": [], "title": "Comfortable and Efficient Travel Techniques in VR.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Locomotion,the most basic interaction in Virtual Environments (VE), enables users to move around the virtual world. Locomotion in Virtual Reality (VR) is a problem which has not been solved completely since existing techniques have a specific set of requirements and limitations. In addition, the uncertainty about the impact that virtual cues have on users perception complicates the development of better locomotion interfaces. A broadly applicable locomotion technique that is easy to use and addresses the issues of presence, cybersickness and fatigue has yet to be developed. Though optical flow and vestibular cues are dominant in navigation, other cues such as auditory, arm feedback, wind, etc. play a role. The proposed research aims to evaluate and improve upon a set of locomotion techniques for different modes of locomotion in virtual scenarios, as well as the transitions between them. The outcome measures of the evaluations of the different scenarios are usefulness for spatial orientation, presence, fatigue, cybersickness and user preference. The envisioned contribution of my thesis is research towards the design of a locomotion technique that is easy to use and addresses the shortcomings of current implementations.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266126"}, {"primary_key": "3517271", "vector": [], "sparse_vector": [], "title": "MetaArms: Body Remapping Using Feet-Controlled Artificial Arms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce MetaArms, wearable anthropomorphic robotic arms and hands with six degrees of freedom operated by the user's legs and feet. Our overall research goal is to re-imagine what our bodies can do with the aid of wearable robotics using a body-remapping approach. To this end, we present an initial exploratory case study. MetaArms' two robotic arms are controlled by the user's feet motion, and the robotic hands can grip objects according to the user's toes bending. Haptic feedback is also presented on the user's feet that correlate with the touched objects on the robotic hands, creating a closed-loop system. Using this system, users can experience an expanded number of arms interaction in which there legs are mapped into the artificial limbs. MetaArms provided initial indications for the sense of limbs alteration.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271628"}, {"primary_key": "3517272", "vector": [], "sparse_vector": [], "title": "DualPanto: A Haptic Device that Enables Blind Users to Continuously Interact with Virtual Worlds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new haptic device that enables blind users to continuously interact with spatial virtual environments that contain moving objects, as is the case in sports or shooter games. Users interact with DualPanto by operating the me handle with one hand and by holding on to the it handle with the other hand. Each handle is connected to a pantograph haptic input/output device. The key feature is that the two handles are spatially registered with respect to each other. When guiding their avatar through a virtual world using the me handle, spatial registration enables users to track moving objects by having the device guide the output hand. This allows blind players of a 1-on-1 soccer game to race for the ball or evade an opponent; it allows blind players of a shooter game to aim at an opponent and dodge shots. In our user study, blind participants reported very high enjoyment when using the device to play (6.5/7).", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242604"}, {"primary_key": "3517273", "vector": [], "sparse_vector": [], "title": "Demonstrating Gamepad with Programmable Haptic Texture Analog Buttons.", "authors": ["Young<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate a haptic feedback method to generate multiple virtual textures on analog buttons of the gamepad. The method utilizes the haptic illusion evoked from proper haptic cues in respect of the analog button's movement to change the perceived physical property of the button. Two types of analog buttons, joystick and trigger button on the gamepad is augmented with localized haptic feedback. We implemented two virtual textures for each type of analog button, and these textures could be programmatically controlled reflecting the dynamic game situations. We also demonstrate a two-player shooter game to show the dynamic texture representation of customized gamepad could enrich the game experience.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271648"}, {"primary_key": "3517274", "vector": [], "sparse_vector": [], "title": "OptRod: Constructing Interactive Surface with Multiple Functions and Flexible Shape by Projected Image.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this demonstration, we propose OptRod, constructing interactive surface with multiple functions and flexible shape by projected image. A PC generates images as control signals and projects them to the bottom of OptRods by a projector or LCD. An OptRod receives the light and converts its brightness into a control signal for the attached output device. By using multiple OptRods, the PC can simultaneously operate many output devices without any signal lines. Moreover, we can arrange surfaces of various shapes easily by combining multiple OptRods. OptRod supports various functions by replacing the device unit connected to OptRod.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271639"}, {"primary_key": "3517275", "vector": [], "sparse_vector": [], "title": "D-Aquarium: A Digital Aquarium to Reduce Perceived Waiting Time at Children&apos;s Hospital.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Son", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Patients waiting for long to use medical services become more physically and psychologically anxious than do people waiting to use general services. Since children feel more anxiety and fear in a hospital, it is necessary to reduce their perceived waiting time by disturbing their awareness of time and dispersing their attention. We present the D-Aquarium, a computer-based digital aquarium that provides psychological stability to pediatric patients and reduces their perceived waiting time by using distractions to alleviate their psychological anxiety and interfere with their perception of time.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266117"}, {"primary_key": "3517276", "vector": [], "sparse_vector": [], "title": "Haptic Feedback to the Palm and Fingers for Improved Tactile Perception of Large Objects.", "authors": ["<PERSON><PERSON><PERSON>", "Jaeyoung Park"], "summary": "When one manipulates a large or bulky object, s/he utilizes tactile information at both fingers and the palm. Our goal is to efficiently convey contact information to a user's hand during interaction with a virtual object. We propose a haptic system that can provide haptic feedback to thumb/middle finger/index finger and on a palm. Our interface design utilizes a novel compact mechanism to provide haptic information to the palm. Also, we propose a haptic rendering strategy to calculate haptic feedback continuously. We demonstrate that cutaneous feedback on the palm improves the haptic perception of a large virtual object compared to when there is only kinesthetic feedback to the fingers.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242656"}, {"primary_key": "3517277", "vector": [], "sparse_vector": [], "title": "Turbulence Ahead - A 3D Web-Based Aviation Weather Visualizer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although severe aircraft accidents have been reduced in the last decades, the number of injuries and fatalities caused by turbulence is still rising. Current aviation weather products are unable to provide a holistic and intuitive view of the overall weather situation, especially in terms of turbulence forecasts. This work introduces an interactive 3D prototype developed with a user-centered design approach. The prototype focuses on the visualization of significant weather charts, which are utilized during flight preparation. An online user study is conducted to compare the prototype with today's 2D paper maps. A total of 64 pilots from an internationally operating airline participated in the study. Among the major findings of the study is that the prototype significantly decreased the cognitive load, and enhanced spatial awareness and usability. To determine the spatial awareness, a novel similarity measure for spatial configurations of aviation weather data is introduced.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242624"}, {"primary_key": "3517278", "vector": [], "sparse_vector": [], "title": "Augmenting Human Hearing Through Interactive Auditory Mediated Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To filter and shut out an increasingly loud environment, many resort to the use of personal audio technology. They drown out unwanted sounds, by wearing headphones. This uniform interaction with all surrounding sounds can have a negative impact on social relations and situational awareness. Leveraging mediation through smarter headphones, users gain more agency over their sense of hearing: For instance by being able to selectively alter the volume and other features of specific sounds, without losing the ability to add media. In this work, we propose the vision of interactive auditory mediated reality (AMR). To understand users' attitude and requirements, we conducted a week-long event sampling study (n = 12), where users recorded and rated sources (n = 225) which they would like to mute, amplify or turn down. The results indicate that besides muting, a distinct, \"quiet-but-audible\" volume exists. It caters to two requirements at the same time: aesthetics/comfort and information acquisition.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266104"}, {"primary_key": "3517279", "vector": [], "sparse_vector": [], "title": "Companion - A Software Toolkit for Digitally Aided Pen-and-Paper Tabletop Roleplaying.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Companion, a software tool tailored towards improving and digitally supporting the pen-and-paper tabletop role-playing experience. Pen-and-paper role-playing games (P&P RPG) are a concept known since the early 1970s. Since then, the genre has attracted a massive community of players while branching out into several genres and P&P RPG systems to choose from. Due to the highly interactive and dynamic nature of the game, a participants individual impact on narrative and interactive aspects of the game is extremely high. The diversity of scenarios within this context unfold a variety of players needs, as well as factors limiting and enhancing game-play. Companion offers an audio management workspace for creation and playback of soundscapes based on visual layouting. It supports interactive image presentation and map exploration which can incorporate input from any device providing TUIO tracking data. Additionally, a mobile app was developed to be used as a remote control for media activation on the desktop host.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266097"}, {"primary_key": "3517280", "vector": [], "sparse_vector": [], "title": "TakeToons: Script-driven Performance Animation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Performance animation is an expressive method for animating characters through human performance. However, character motion is only one part of creating animated stories. The typical workflow also involves writing a script, coordinating actors, and editing recorded performances. In most cases, these steps are done in isolation with separate tools, which introduces friction and hinders iteration. We propose TakeToons, a script-driven approach that allows authors to annotate standard scripts with relevant animation events like character actions, camera positions, and scene backgrounds. We compile this script into a story model that persists throughout the production process and provides a consistent structure for organizing and assembling recorded performances and propagating script or timing edits to existing recordings. TakeToons enables writing, performing and editing to happen in an integrated and interleaved manner that streamlines production and facilitates iteration. Informal feedback from professional animators suggests that our approach can benefit many existing workflows supporting individual authors and production teams with many different contributors.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242618"}, {"primary_key": "3517281", "vector": [], "sparse_vector": [], "title": "Dynablock: Dynamic 3D Printing for Instant and Reconstructable Shape Formation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces Dynamic 3D Printing, a fast and reconstructable shape formation system. Dynamic 3D Printing can assemble an arbitrary three-dimensional shape from a large number of small physical elements. Also, it can disassemble the shape back to elements and reconstruct a new shape. Dynamic 3D Printing combines the capabilities of 3D printers and shape displays: Like conventional 3D printing, it can generate arbitrary and graspable three-dimensional shapes, while allowing shapes to be rapidly formed and reformed as in a shape display. To demonstrate the idea, we describe the design and implementation of Dynablock, a working prototype of a dynamic 3D printer. Dynablock can form a three-dimensional shape in seconds by assembling 3,000 9 mm blocks, leveraging a 24 x 16 pin-based shape display as a parallel assembler. Dynamic 3D printing is a step toward achieving our long-term vision in which 3D printing becomes an interactive medium, rather than the means for fabrication that it is today. In this paper, we explore possibilities for this vision by illustrating application scenarios that are difficult to achieve with conventional 3D printing or shape display systems.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242659"}, {"primary_key": "3517282", "vector": [], "sparse_vector": [], "title": "Scout: Mixed-Initiative Exploration of Design Variations through High-Level Design Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although the exploration of variations is a key part of interface design, current processes for creating variations are mostly manual. We present Scout, a system that helps designers explore many variations rapidly through mixed-initiative interaction with high-level constraints and design feedback. Past constraint-based layout systems use low-level spatial constraints and mostly produce only a single design. <PERSON> advances upon these systems by introducing high-level constraints based on design concepts (e.g. emphasis). With <PERSON>, we have formalized several high-level constraints into their corresponding low-level spatial constraints to enable rapidly generating many designs through constraint solving and program synthesis.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271626"}, {"primary_key": "3517283", "vector": [], "sparse_vector": [], "title": "Reversing Voice-Related Biases Through Haptic Reinforcement.", "authors": ["Feras Al Taha", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Biased perceptions of others are known to negatively influence the outcomes of social and professional interactions in many regards. Theses biases can be informed by a multitude of non-verbal cues such as voice pitch and voice volume. This project explores how haptic effects, generated from speech, could attenuate listeners' perceived voice-related biases formed from a speaker's voice pitch. Promising preliminary results collected during a decision-making task suggest that the speech to haptic mapping and vibration delivery mechanism employed does attenuate voice-related biases. Accordingly, it is anticipated that such a system could be introduced in the workplace to equalize people's contribution opportunities and to create a more inclusive environment by reversing voice-related biases.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266101"}, {"primary_key": "3517284", "vector": [], "sparse_vector": [], "title": "Haptic Interface Using Tendon Electrical Stimulation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This demonstration corresponds to our previous paper, which deals with our finding that a proprioceptive force sensation can be presented by electrical stimulation from the skin surface to the tendon region (Tendon Electrical Stimulation: TES). We showed that TES can elicit a force sensation, and adjusting the current parameters can control the amount of the sensation. Unlike electrical muscle stimulation (EMS), which can also present force sensation by stimulating motor nerves to contract muscles, TES is thought to present a proprioceptive force sensation by stimulating receptors or sensory nerves responsible for recognizing the magnitude of the muscle contraction existing inside the tendon. In the demo, we offer the occasion for trying TES.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271640"}, {"primary_key": "3517285", "vector": [], "sparse_vector": [], "title": "The Science and Practice of Transitions.", "authors": ["<PERSON>"], "summary": "We tend to set ourselves up to thrive in a particular state while ignoring the transitions between states. But there is magic in the transitions; they are where unexpected and interesting things happen. There is an opportunity for our user interfaces to better support the transitions we make. In this talk I will share some of what I have learned from years of productivity research about how to successfully transition between tasks over the course of a day, and reflect on how these findings might be extended to help us understand how we, as academics and practitioners, can successfully transition through the various contexts and roles that we hold in a lifetime.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242669"}, {"primary_key": "3517286", "vector": [], "sparse_vector": [], "title": "PuPoP: Pop-up Prop on Palm for Virtual Reality.", "authors": ["Shan-<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The sensation of being able to feel the shape of an object when grasping it in Virtual Reality (VR) enhances a sense of presence and the ease of object manipulation. Though most prior works focus on force feedback on fingers, the haptic emulation of grasping a 3D shape requires the sensation of touch using the entire hand. Hence, we present Pop-up Prop on Palm (PuPoP), a light-weight pneumatic shape-proxy interface worn on the palm that pops several airbags up with predefined primitive shapes for grasping. When a user's hand encounters a virtual object, an airbag of appropriate shape, ready for grasping, is inflated by way of the use of air pumps; the airbag then deflates when the object is no longer in play. Since PuPoP is a physical prop, it can provide the full sensation of touch to enhance the sense of realism for VR object manipulation. For this paper, we first explored the design and implementation of PuPoP with multiple shape structures. We then conducted two user studies to further understand its applicability. The first study shows that, when in conflict, visual sensation tends to dominate over touch sensation, allowing a prop with a fixed size to represent multiple virtual objects with similar sizes. The second study compares PuPoP with controllers and free-hand manipulation in two VR applications. The results suggest that utilization of dynamically-changing PuPoP, when grasped by users in line with the shapes of virtual objects, enhances enjoyment and realism. We believe that PuPoP is a simple yet effective way to convey haptic shapes in VR.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242628"}, {"primary_key": "3517287", "vector": [], "sparse_vector": [], "title": "MobiLimb: Augmenting Mobile Devices with a Robotic Limb.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we explore the interaction space of MobiLimb, a small 5-DOF serial robotic manipulator attached to a mobile device. It (1) overcomes some limitations of mobile devices (static, passive, motionless); (2) preserves their form factor and I/O capabilities; (3) can be easily attached to or removed from the device; (4) offers additional I/O capabilities such as physical deformation and (5) can support various modular elements such as sensors, lights or shells. We illustrate its potential through three classes of applications: As a tool, MobiLimb offers tangible affordances and an expressive controller that can be manipulated to control virtual and physical objects. As a partner, it reacts expressively to users' actions to foster curiosity and engagement or assist users. As a medium, it provides rich haptic feedback such as strokes, pat and other tactile stimuli on the hand or the wrist to convey emotions during mediated multimodal communications.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242626"}, {"primary_key": "3517288", "vector": [], "sparse_vector": [], "title": "AmbientLetter: Letter Presentation Method for Discreet Notification of Unknown Spelling when Handwriting.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a technique to support writing activity in a confidential manner with a pen-based device. Autocorrect and predictive conversion do not work when writing by hand, and looking up unknown spelling is sometimes embarrassing. Therefore, we propose AmbientLetter which seamlessly and discretely presents the forgotten spelling to the user in scenarios where handwriting is necessary. In this work, we describe the system structure and the technique used to conceal the user\"s getting the information.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266093"}, {"primary_key": "3517289", "vector": [], "sparse_vector": [], "title": "Designing Groundless Body Channel Communication Systems: Performance and Implications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Novel interactions that capacitively couple electromagnetic (EM) fields between devices and the human body are gaining more attention in the human-computer interaction community. One class of these techniques is Body Channel Communication (BCC), a method that overlays physical touch with digital information. Despite the number of published capacitive sensing and communication prototypes, there exists no guideline on how to design such hardware or what are the application limitations and possibilities. Specifically, wearable (groundless) BCC has been proven in the past to be extremely challenging to implement. Additionally, the exact behavior of the human body as an EM-field medium is still not fully understood today. Consequently, the application domain of BCC technology could not be fully explored. This paper addresses this problem. Based on a recently published general purpose wearable BCC system, we first present a thorough evaluation of the impact of various technical parameter choices and an exhaustive channel characterization of the human body as a host for BCC. Second, we discuss the implications of these results for the application design space and present guidelines for future wearable BCC systems and their applications. Third, we point out an important observation of the measurements, namely that BCC can employ the whole body as user interface (and not just hands or feet). We sketch several applications with these novel interaction modalities.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242622"}, {"primary_key": "3517290", "vector": [], "sparse_vector": [], "title": "Extending a Reactive Expression Language with Data Update Actions for End-User Application Authoring.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mavo is a small extension to the HTML language that empowers non-programmers to create simple web applications. Authors can mark up any normal HTML document with attributes that specify data elements that <PERSON><PERSON> makes editable and persists. But while applications authored with Mavo allow users to edit individual data items, they do not offer any programmatic data actions that can act in customizable ways on large collections of data simultaneously or that modify data according to a computation. We explore an extension to the Mavo language that enables non-programmers to author these richer data update actions. We show that it lets authors create a more powerful set of applications than they could previously, while adding little additional complexity to the authoring process. Through user evaluations, we assess how closely our data update syntax matches how novice authors would instinctively express such actions, and how well they are able to use the syntax we provided.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242663"}, {"primary_key": "3517291", "vector": [], "sparse_vector": [], "title": "4DMesh: 4D Printing Morphing Non-Developable Mesh Surfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present 4DMesh, a method of combining shrinking and bending thermoplastic actuators with customized geometric algorithms to 4D print and morph centimeter- to meter-sized functional non-developable surfaces. We will share two end-to-end inverse design algorithms. With our tools, users can input CAD models of target surfaces and produce respective printable files. The flat sheet printed can morph into target surfaces when triggered by heat. This system saves shipping and packaging costs, in addition to enabling customizability for the design of relatively large non-developable structures. We designed a few functional artifacts to leverage the advantage of non-developable surfaces for their unique functionalities in aesthetics, mechanical strength, geometric ergonomics and other functionalities. In addition, we demonstrated how this technique can potentially be adapted to customize molds for industrial parts (e.g., car, boat, etc.) in the future.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242625"}, {"primary_key": "3517292", "vector": [], "sparse_vector": [], "title": "ElectroTutor: Test-Driven Physical Computing Tutorials.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A wide variety of tools for creating physical computing systems have been developed, but getting started in this domain remains challenging for novices. In this paper, we introduce test-driven physical computing tutorials, a novel application of interactive tutorial systems to better support users in building and programming physical computing systems. These tutorials inject interactive tests into the tutorial process to help users verify and understand individual steps before proceeding. We begin by presenting a taxonomy of the types of tests that can be incorporated into physical computing tutorials. We then present ElectroTutor, a tutorial system that implements a range of tests for both the software and physical aspects of a physical computing system. A user study suggests that ElectroTutor can improve users' success and confidence when completing a tutorial, and save them time by reducing the need to backtrack and troubleshoot errors made on previous tutorial steps.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242591"}, {"primary_key": "3517293", "vector": [], "sparse_vector": [], "title": "Interactive Tangrami: Rapid Prototyping with Modular Paper-folded Electronics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Prototyping interactive objects with personal fabrication tools like 3D printers requires the maker to create subsequent design artifacts from scratch which produces unnecessary waste and does not allow to reuse functional components. We present Interactive Tangrami, paper-folded and reusable building blocks (Tangramis) that can contain various sensor input and visual output capabilities. We propose a digital design toolkit that lets the user plan the shape and functionality of a design piece. The software manages the communication to the physical artifact and streams the interaction data via the Open Sound protocol (OSC) to an application prototyping system (e.g. MaxMSP). The building blocks are fabricated digitally with a rapid and inexpensive ink-jet printing method. Our systems allows to prototype physical user interfaces within minutes and without knowledge of the underlying technologies. We demo its usefulness with two application examples.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271630"}, {"primary_key": "3517294", "vector": [], "sparse_vector": [], "title": "Shape-Aware Material: Interactive Fabrication with ShapeMe.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Makers often create both physical and digital prototypes to explore a design, taking advantage of the subtle feel of physical materials and the precision and power of digital models. We introduce ShapeMe, a novel smart material that captures its own geometry as it is physically cut by an artist or designer. ShapeMe includes a software toolkit that lets its users generate customized, embeddable sensors that can accommodate various object shapes. As the designer works on a physical prototype, the toolkit streams the artist's physical changes to its digital counterpart in a 3D CAD environment. We use a rapid, inexpensive and simple-to-manufacture inkjet printing technique to create embedded sensors. We successfully created a linear predictive model of the sensors' lengths, and our empirical tests of ShapeMe show an average accuracy of 2 to 3 mm. We present two application scenarios for modeling multi-object constructions, such as architectural models, and 3D models consisting of multiple layers stacked one on top of each other. ShapeMe demonstrates a novel technique for integrating digital and physical modeling, and suggests new possibilities for creating shape-aware materials.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242619"}, {"primary_key": "3517295", "vector": [], "sparse_vector": [], "title": "A Mixed-Initiative Interface for Animating Static Pictures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present an interactive tool to animate the visual elements of a static picture, based on simple sketch-based markup. While animated images enhance websites, infographics, logos, e-books, and social media, creating such animations from still pictures is difficult for novices and tedious for experts. Creating automatic tools is challenging due to ambiguities in object segmentation, relative depth ordering, and non-existent temporal information. With a few user drawn scribbles as input, our mixed initiative creative interface extracts repetitive texture elements in an image, and supports animating them. Our system also facilitates the creation of multiple layers to enhance depth cues in the animation. Finally, after analyzing the artwork during segmentation, several animation processes automatically generate kinetic textures that are spatio-temporally coherent with the source image. Our results, as well as feedback from our user evaluation, suggest that our system effectively allows illustrators and animators to add life to still images in a broad range of visual styles.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242612"}, {"primary_key": "3517296", "vector": [], "sparse_vector": [], "title": "Evaluation of Interaction Techniques for a Virtual Reality Reading Room in Diagnostic Radiology.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>rn M<PERSON>"], "summary": "Today, radiologists diagnose three dimensional medical data using two dimensional displays. When designing environments with optimal conditions for such a process various aspects like contrast, screen reflection and background light have to be considered. As shown in previous research, applying virtual environments in combination with a Head-Mounted Display for diagnostic imaging provides potential benefits to reduce issues of bad posture and diagnostic mistakes. However, there is little research in exploring the usability and user experience of such beneficial environments. In this work we designed and evaluated different means of interaction to increase radiologists' performance. Therefore we created a virtual reality radiology reading room and employed it to evaluate three different interaction techniques. These allow a direct, semi-direct and indirect manipulation for performing scrolling- and windowing- tasks which are the most important for a radiologist. A study including nine radiologists was conducted and evaluated using the User Experience Questionnaire. Results indicate that direct manipulation is the preferred interaction technique, it outscored the other two control possibilities in attractiveness and pragmatic quality.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242636"}, {"primary_key": "3517297", "vector": [], "sparse_vector": [], "title": "Tacttoo: A Thin and Feel-Through Tattoo for On-Skin Tactile Output.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces Tacttoo, a feel-through interface for electro-tactile output on the user's skin. Integrated in a temporary tattoo with a thin and conformal form factor, it can be applied on complex body geometries, including the fingertip, and is scalable to various body locations. At less than 35µm in thickness, it is the thinnest tactile interface for wearable computing to date. Our results show that Tacttoo retains the natural tactile acuity similar to bare skin while delivering high-density tactile output. We present the fabrication of customized Tacttoo tattoos using DIY tools and contribute a mechanism for consistent electro-tactile operation on the skin. Moreover, we explore new interactive scenarios that are enabled by Tacttoo. Applications in tactile augmented reality and on-skin interaction benefit from a seamless augmentation of real-world tactile cues with computer-generated stimuli. Applications in virtual reality and private notifications benefit from high-density output in an ergonomic form factor. Results from two psychophysical studies and a technical evaluation demonstrate Tacttoo's functionality, feel-through properties and durability.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242645"}, {"primary_key": "3517298", "vector": [], "sparse_vector": [], "title": "cARe: An Augmented Reality Support System for Dementia Patients.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Symptoms of progressing dementia like memory loss, impaired executive function and decreasing motivation can gradually undermine instrumental activities of daily living (IADL) such as cooking. Assisting technologies in form of augmented reality (AR) have previously been applied to support cognitively impaired users during IADLs. In most cases, instructions were provided locally via projection or a head-mounted display (HMD) but lacked an incentive mechanism and the flexibility to support a broad range of use-cases. To provide users and therapists with a holistic solution, we propose cARe, a framework that can be easily adapted by therapists to various use-cases without any programming knowledge. Users are then guided through manual processes with localized visual and auditory cues that are rendered by an HMD. Our ongoing user study indicates that users are more comfortable and successful in cooking with cARe as compared to a printed recipe, which promises a more dignified and autonomous living for dementia patients.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266095"}, {"primary_key": "3517299", "vector": [], "sparse_vector": [], "title": "Face/On: Actuating the Facial Contact Area of a Head-Mounted Display for Increased Immersion.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demonstration, we introduce Face/On, an embedded feedback device that leverages the contact area between the user's face and a virtual reality (VR) head-mounted display (HMD) to provide rich haptic feedback in virtual environments (VEs). Head-worn haptic feedback devices have been explored in previous work to provide directional cues via grids of actuators and localized feedback on the users' skin. Most of these solutions were immersion breaking due to their encumbering and uncomfortable design and build around a single actuator type, thus limiting the overall fidelity and flexibility of the haptic feedback. We present Face/On, a VR HMD face cushion with three types of discreetly embedded actuators that provide rich haptic feedback without encumbering users with invasive instrumentation on the body. By combining vibro-tactile and thermal feedback with electrical muscle stimulation (EMS), Face/On can simulate a wide range of scenarios and benefit from synergy effects between these feedback types.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271631"}, {"primary_key": "3517300", "vector": [], "sparse_vector": [], "title": "Spacetime: Enabling Fluid Individual and Collaborative Editing in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual Reality enables users to explore content whose physics are only limited by our creativity. Such limitless environments provide us with many opportunities to explore innovative ways to support productivity and collaboration. We present Spacetime, a scene editing tool built from the ground up to explore the novel interaction techniques that empower single user interaction while maintaining fluid multi-user collaboration in immersive virtual environment. We achieve this by introducing three novel interaction concepts: the Container, a new interaction primitive that supports a rich set of object manipulation and environmental navigation techniques, Parallel Objects, which enables parallel manipulation of objects to resolve interaction conflicts and support design workflows, and Avatar Objects, which supports interaction among multiple users while maintaining an individual users' agency. Evaluated by professional Virtual Reality designers, Spacetime supports powerful individual and fluid collaborative workflows.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242597"}, {"primary_key": "3517301", "vector": [], "sparse_vector": [], "title": "Mindgame: Mediating People&apos;s EEG Alpha Band Power through Reinforcement Learning.", "authors": ["Tong<PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Mindgame, a reinforcement learning optimized neurofeedback mindfulness system. To avoid the potential bias and difficulties of designing mapping between neural signal and output, we adopt a trial-and-error learning method to explore the preferred mapping. In a pilot study we assess the effectiveness of Mindgame in mediating people's EEG alpha band. All participants' alpha band change towards the desired direction.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266083"}, {"primary_key": "3517302", "vector": [], "sparse_vector": [], "title": "Ultra-Low-Power Mode for Screenless Mobile Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smartphones are now a central technology in the daily lives of billions, but it relies on its battery to perform. Battery optimization is thereby a crucial design constraint in any mobile OS and device. However, even with new low-power methods, the ever-growing touchscreen remains the most power-hungry component. We propose an Ultra-Low-Power Mode (ULPM) for mobile devices that allows for touch interaction without visual feedback and exhibits significant power savings of up to 60% while allowing to complete interactive tasks. We demonstrate the effectiveness of the screenless ULPM in text-entry tasks, camera usage, and listening to videos, showing only a small decrease in usability for typical users.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242614"}, {"primary_key": "3517303", "vector": [], "sparse_vector": [], "title": "CamTrackPoint: Camera-Based Pointing Stick Using Transmitted Light through Finger.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present CamTrackPoint, a new input interface that can be controlled by finger gestures captured by front or rear cameras of a mobile device. CamTrackPoint mounts a 3D-printed ring on the camera's bezel, and it senses the movements of the user's finger by tracking the light passed through the finger. The proposed method provides mobile devices with a new input interface that offers physical force feedback like a pointing stick. The cost of our method is low as it needs only a simple ring-shaped part on the camera bezel. Moreover, the ring doesn't disturb the functions of the camera, unless a user uses the interface. We implement a prototype for a smartphone; two CamTrackPoint rings are made for the front and rear cameras. We evaluate its performance and characteristics in an experiment. The proposed technique provides smooth scrolling and would give better game experience on the available smartphone.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242641"}, {"primary_key": "3517304", "vector": [], "sparse_vector": [], "title": "AccordionFab: Fabricating Inflatable 3D Objects by Laser Cutting and Welding Multi-Layered Sheets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a method to create 3D inflatable objects by laminating plastic layers. AccordionFab is a fabrication method in which the user can prototype multi-layered inflatable structures rapidly with a common laser cutter. Our key finding is that it is possible to selectively weld the two uppermost plastic sheets out of the stacked sheets by defocusing the laser and inserting the heat-resistant paper below the desired welding layer. As the contribution of our research, we investigated the optimal distance between the lens and the workpiece for cutting and welding and developed an attachment which supports welding process. Next, we developed a mechanism of changing the thickness and bending angle of multi-layered objects and created a simulation software. Using these techniques, the user can create various prototypes such as personal furniture that fits user's body and packing containers that fit the contents.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3271636"}, {"primary_key": "3517305", "vector": [], "sparse_vector": [], "title": "VR Grabbers: Ungrounded Haptic Retargeting for Precision Grabbing Tools.", "authors": ["<PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Haptic feedback in VR is important for realistic simulation in virtual reality. However, recreating the haptic experience for hand tools in VR traditionally requires hardware with precise actuators, adding complexity to the system. We propose Ungrounded Haptic Retargeting, an interaction technique that provides a realistic haptic experience for grabbing tools using only passive mechanisms. This technique leverages the ungrounded feedback inherent in grabbing tools combined with dynamic visual adjustments of their position in virtual reality to create an illusion of physical presence for virtual objects. To demonstrate the capabilities of this technique, we created VR Grabbers, an exemplary passive VR controller, similar to training chopsticks, with haptic feedback for precise object selection and manipulation. We conducted two user studies based on VR Grabbers. The first study probed the perceptual limits of the illusion; we found that the maximum position difference between the virtual and physical world acceptable to the user is (-1.48, 1.95) cm. The second study showed that task performance of the VR Grabbers controller with Ungrounded Haptic Retargeting enabled outperforms the same controller with Ungrounded Haptic Retargeting disabled.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242643"}, {"primary_key": "3517306", "vector": [], "sparse_vector": [], "title": "ShareSpace: Facilitating Shared Use of the Physical Space by both VR Head-Mounted Display and External Users.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Currently, \"walkable\" virtual reality (VR) is achieved by dedicating a room-sized space for VR activities, which is not shared with non-HMD users engaged in their own activities. To achieve the goal of allowing shared use of space for all users while overcoming the obvious difficulty of integrating use with those immersed in a VR experience, we present ShareSpace, a system that allows external users to communicate their needs for physical space to those wearing an HMD and immersed in their VR experience. ShareSpace works by allowing external users to place \"shields\" in the virtual environment by using a set of physical shield tools. A pad visualizer helps this process by allowing external users to examine the arrangement of virtual shields. We also discuss interaction techniques that minimize the interference between the respective activities of the HMD wearers and the other users of the same physical space. To evaluate our design, a user study was conducted to collect user feedback from participants in four trial scenarios. The results indicate that our ShareSpace system allows users to perform their respective activities with improved engagement and safety. In addition, this study shows that while the HMD users did perceive a considerable degree of interference due to the internal visual indications from the ShareSpace system, they were still more engaged in their VR experience than when interrupted by direct external physical interference initiated by external users.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242630"}, {"primary_key": "3517307", "vector": [], "sparse_vector": [], "title": "Enabling Single-Handed Interaction in Mobile and Wearable Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile and wearable computing are increasingly pervasive as people carry and use personal devices in everyday life. Screen sizes of such devices are becoming larger and smaller to accommodate both intimate and practical uses. Some mobile device screens are becoming larger to accommodate new experiences (e.g., phablet, tablet, eReader), whereas screen sizes on wearable devices are becoming smaller to allow them to fit into more places (e.g., smartwatch, wrist-band and eye-wear). However, these trends are making it difficult to use such devices with only one hand due to their placement, limited thumb reach and the fat-finger problem. This is especially true as there are many occasions when a user's other hand is occupied (encumbered) or not available. This thesis work explores, creates and studies novel interaction techniques that enable effective single-hand usage on mobile and wearable devices, empowering users to achieve more with their smart devices when only one hand is available.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266129"}, {"primary_key": "3517308", "vector": [], "sparse_vector": [], "title": "Phonoscape: Auralization of Photographs using Stereophonic Auditory Icons.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we developed an auditory display method which improves the comprehension of photograph to apply the support system for person with visual impairment. The auralization method is constructed by object recognition, auditory iconization and stereophonic techniques. Through the experiments, the enhancement of intelligibility and discriminability was confirmed compared to the image-to-speech reading machine method.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266120"}, {"primary_key": "3517309", "vector": [], "sparse_vector": [], "title": "MoSculp: Interactive Visualization of Shape and Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a system that allows users to visualize complex human motion via 3D motion sculptures---a representation that conveys the 3D structure swept by a human body as it moves through space. Given an input video, our system computes the motion sculptures and provides a user interface for rendering it in different styles, including the options to insert the sculpture back into the original video, render it in a synthetic scene or physically print it. To provide this end-to-end workflow, we introduce an algorithm that estimates that human's 3D geometry over time from a set of 2D images and develop a 3D-aware image-based rendering approach that embeds the sculpture back into the scene. By automating the process, our system takes motion sculpture creation out of the realm of professional artists, and makes it applicable to a wide range of existing video material. By providing viewers with 3D information, motion sculptures reveal space-time motion information that is difficult to perceive with the naked eye, and allow viewers to interpret how different parts of the object interact over time. We validate the effectiveness of this approach with user studies, finding that our motion sculpture visualizations are significantly more informative about motion than existing stroboscopic and space-time visualization methods.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242592"}, {"primary_key": "3517310", "vector": [], "sparse_vector": [], "title": "Fusion: Opportunistic Web Prototyping with UI Mashups.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Modern web development is rife with complexity at all layers, ranging from needing to configure backend services to grappling with frontend frameworks and dependencies. To lower these development barriers, we introduce a technique that enables people to prototype opportunistically by borrowing pieces of desired functionality from across the web without needing any access to their underlying codebases, build environments, or server backends. We implemented this technique in a browser extension called Fusion, which lets users create web UI mashups by extracting components from existing unmodified webpages and hooking them together using transclusion and JavaScript glue code. We demonstrate the generality and versatility of Fusion via a case study where we used it to create seven UI mashups in domains such as programming tools, data science, web design, and collaborative work. Our mashups include replicating portions of prior HCI systems (Blueprint for in-situ code search and DS.js for in-browser data science), extending the p5.js IDE for Processing with real-time collaborative editing, and integrating Python Tutor code visualizations into static tutorials. These UI mashups each took less than 15 lines of JavaScript glue code to create with Fusion.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242632"}, {"primary_key": "3517311", "vector": [], "sparse_vector": [], "title": "Vibrosight: Long-Range Vibrometry for Smart Environment Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart and responsive environments rely on the ability to detect physical events, such as appliance use and human activities. Currently, to sense these types of events, one must either upgrade to \"smart\" appliances, or attach aftermarket sensors to existing objects. These approaches can be expensive, intrusive and inflexible. In this work, we present Vibrosight, a new approach to sense activities across entire rooms using long-range laser vibrometry. Unlike a microphone, our approach can sense physical vibrations at one specific point, making it robust to interference from other activities and noisy environments. This property enables detection of simultaneous activities, which has proven challenging in prior work. Through a series of evaluations, we show that Vibrosight can offer high accuracies at long range, allowing our sensor to be placed in an inconspicuous location. We also explore a range of additional uses, including data transmission, sensing user input and modes of appliance operation, and detecting human movement and activities on work surfaces.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242608"}, {"primary_key": "3517312", "vector": [], "sparse_vector": [], "title": "Robust Annotation of Mobile Application Interfaces in Methods for Accessibility Repair and Enhancement.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Accessibility issues in mobile apps make those apps difficult or impossible to access for many people. Examples include elements that fail to provide alternative text for a screen reader, navigation orders that are difficult, or custom widgets that leave key functionality inaccessible. Social annotation techniques have demonstrated compelling approaches to such accessibility concerns in the web, but have been difficult to apply in mobile apps because of the challenges of robustly annotating interfaces. This research develops methods for robust annotation of mobile app interface elements. Designed for use in runtime interface modification, our methods are based in screen identifiers, element identifiers, and screen equivalence heuristics. We implement initial developer tools for annotating mobile app accessibility metadata, evaluate our current screen equivalence heuristics in a dataset of 2038 screens collected from 50 mobile apps, present three case studies implementing runtime repair of common accessibility issues, and examine repair of real-world accessibility issues in 26 apps. These contributions overall demonstrate strong opportunities for social annotation in mobile accessibility.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242616"}, {"primary_key": "3517313", "vector": [], "sparse_vector": [], "title": "FingerArc and FingerChord: Supporting Novice to Expert Transitions with Guided Finger-Aware Shortcuts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Keyboard shortcuts can be more efficient than graphical input, but they are underused by most users. To alleviate this, we present \"Guided Finger-Aware Shortcuts\" to reduce the gulf between graphical input and shortcut activation. The interaction technique works by recognising when a special hand posture is used to press a key, then allowing secondary finger movements to select among related shortcuts if desired. Novice users can learn the mappings through dynamic visual guidance revealed by holding a key down, but experts can trigger shortcuts directly without pausing. Two variations are described: FingerArc uses the angle of the thumb, and FingerChord uses a second key press. The techniques are motivated by an interview study identifying factors hindering the learning, use, and exploration of keyboard shortcuts. A controlled comparison with conventional keyboard shortcuts shows the techniques encourage overall shortcut usage, make interaction faster, less error-prone, and provide advantages over simply adding visual guidance to standard shortcuts.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3242587.3242589"}, {"primary_key": "3517314", "vector": [], "sparse_vector": [], "title": "Shared Autonomy for an Interactive AI System.", "authors": ["<PERSON>", "Tong Mu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Across many domains, interactive systems either make decisions for us autonomously or yield decision-making authority to us and play a supporting role. However, many settings, such as those in education or the workplace, benefit from sharing this autonomy between the user and the system, and thus from a system that adapts to them over time. In this paper, we pursue two primary research questions: (1) How do we design interfaces to share autonomy between the user and the system? (2) How does shared autonomy alter a user\"s perception of a system? We present SharedKeys, an interactive shared autonomy system for piano instruction that plays different video segments of a piece for students to emulate and practice. Underlying our approach to shared autonomy is a mixed-observability Markov decision process that estimates a user\"s desired autonomy level based on her performance and attentiveness. Pilot studies revealed that students sharing autonomy with the system learned more quickly and perceived the system as more intelligent.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3266037.3266088"}, {"primary_key": "3531456", "vector": [], "sparse_vector": [], "title": "The 31st Annual ACM Symposium on User Interface Software and Technology, UIST 2018, Berlin, Germany, October 14-17, 2018", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Welcome to the 31st Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 14th to October 17th, 2018, in Berlin, Germany. UIST is the premier forum for presenting innovative research on software and technology for humancomputer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas, including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing, and computer-supported collaborative work. UIST 2018 received 375 technical paper submissions. After a thorough review process, the program committee accepted 80 papers (21.3%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, a second member of the program committee provided an additional review. We then asked the authors to submit a rebuttal addressing the reviewers' concerns. The program committee met in person at Microsoft Research in Redmond, WA, USA on June 22nd and 23rd, 2018, to select the papers to invite for the program. After acceptance, authors provided a final revision addressing the committee's comments. Three papers were recognized by the reviewers and the program committee as Best Paper and four received Honorable Mention. In addition to papers, our program includes 44 posters, 25 demonstrations, and 8 student presentations in the thirteenth annual Doctoral Symposium. Our program also features the ninth annual Student Innovation Contest. In this year's contest, we are partnering with Makeblock to enable teams from all over the world push the boundaries of input and output technology by creating novel human-robot interaction techniques.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": ""}, {"primary_key": "3531457", "vector": [], "sparse_vector": [], "title": "The 31st Annual ACM Symposium on User Interface Software and Technology Adjunct Proceedings, UIST 2018, Berlin, Germany, October 14-17, 2018", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Welcome to the 31st Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 14th to October 17th, 2018, in Berlin, Germany. UIST is the premier forum for presenting innovative research on software and technology for humancomputer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas, including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing, and computer-supported collaborative work. UIST 2018 received 375 technical paper submissions. After a thorough review process, the program committee accepted 80 papers (21.3%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, a second member of the program committee provided an additional review. We then asked the authors to submit a rebuttal addressing the reviewers' concerns. The program committee met in person at Microsoft Research in Redmond, WA, USA on June 22nd and 23rd, 2018, to select the papers to invite for the program. After acceptance, authors provided a final revision addressing the committee's comments. Three papers were recognized by the reviewers and the program committee as Best Paper and four received Honorable Mention. In addition to papers, our program includes 44 posters, 25 demonstrations, and 8 student presentations in the thirteenth annual Doctoral Symposium. Our program also features the ninth annual Student Innovation Contest. In this year's contest, we are partnering with Makeblock to enable teams from all over the world push the boundaries of input and output technology by creating novel human-robot interaction techniques.", "published": "2018-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": ""}]