[{"primary_key": "3878167", "vector": [], "sparse_vector": [], "title": "The Price of Uncertainty in Present-Biased Planning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract The tendency to overestimate immediate utility is a common cognitive bias. As a result people behave inconsistently over time and fail to reach long-term goals. Behavioral economics tries to help affected individuals by implementing external incentives. However, designing robust incentives is often difficult due to imperfect knowledge of the parameter $$\\beta \\in (0,1]$$ β ∈ ( 0 , 1 ] quantifying a person’s present bias. Using the graphical model of <PERSON> and <PERSON> [8], we approach this problem from an algorithmic perspective. Based on the assumption that the only information about $$\\beta $$ β is its membership in some set $$B \\subset (0,1]$$ B ⊂ ( 0 , 1 ] , we distinguish between two models of uncertainty: one in which $$\\beta $$ β is fixed and one in which it varies over time. As our main result we show that the conceptual loss of efficiency incurred by incentives in the form of penalty fees is at most 2 in the former and $$1 + \\max B/\\min B$$ 1 + max B / min B in the latter model. We also give asymptotically matching lower bounds and approximation algorithms.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_23"}, {"primary_key": "3878168", "vector": [], "sparse_vector": [], "title": "On Budget-Feasible Mechanism Design for Symmetric Submodular Objectives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a class of procurement auctions with a budget constraint, where an auctioneer is interested in buying resources from a set of agents. The auctioneer would like to select a subset of the resources so as to maximize his valuation function, without exceeding his budget. As the resources are owned by strategic agents, our overall goal is to design mechanisms that are truthful, budget-feasible, and obtain a good approximation to the optimal value. Previous results on budget-feasible mechanisms have considered mostly monotone valuation functions. In this work, we mainly focus onsymmetric submodularvaluations, a prominent class of non-monotone submodular functions that includescut functions. We begin with a purely algorithmic result, obtaining a\\(\\frac{2e}{e-1}\\)-approximation for maximizing symmetric submodular functions under a budget constraint. We then proceed to propose truthful, budget feasible mechanisms (both deterministic and randomized), paying particular attention on the Budgeted Max Cut problem. Our results significantly improve the known approximation ratios for these objectives, while establishing polynomial running time for cases where only exponential mechanisms were known. At the heart of our approach lies an appropriate combination of local search algorithms with results for monotone submodular valuations, applied to the derived local optima.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_1"}, {"primary_key": "3878169", "vector": [], "sparse_vector": [], "title": "Don&<PERSON><PERSON>s;t <PERSON>: Leveraging Community Structure to Find High Quality Seed Sets for Influence Maximization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of maximizing the spread of influence in a social network by choosing a fixed number of initial seeds — a central problem in the study of network cascades. The majority of existing work on this problem, formally referred to as theinfluence maximization problem, is designed for submodular cascades. Despite the empirical evidence that many cascades are non-submodular, little work has been done focusing on non-submodular influence maximization. We propose a new heuristic for solving the influence maximization problem and show via simulations on real-world and synthetic networks that our algorithm outputs more influential seed sets than the state-of-the-art greedy algorithm in many natural cases, with average improvements of 7% for submodular cascades, and 55% for non-submodular cascades. Our heuristic uses a dynamic programming approach on a hierarchical decomposition of the social network to leverage the relation between the spread of cascades and the community structure of social networks. We present “worst-case” theoretical results proving that in certain settings our algorithm outputs seed sets that are a factor of\\(\\varTheta (\\sqrt{n})\\)more influential than those of the greedy algorithm, wherenis the number of nodes in the network.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_2"}, {"primary_key": "3878171", "vector": [], "sparse_vector": [], "title": "Information Retention in Heterogeneous Majority Dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A dynamicsretainsa specific information about thestartingstate of a networked multi-player system if this information can be computed from the state of the system also after several rounds of the dynamics. Information retention has been studied for the function that returns the majority of the states in systems in which players have states in\\(\\{0,1\\}\\)and the system evolves according to the majority dynamics: each player repeatedly updates its state to match the local majority among neighbors only. Positive and negative results have been given for probabilistic settings in which the initial states of the players are chosen at random and in worst-case settings in which the initial state is chosen non-deterministically. In this paper, we study the (lack of) retention of information on the majority state (that is, which states appear in more players) for a generalization of the majority dynamics that we callheterogeneousmajority dynamics. Here, each playerxchanges its state from the initial state\\(\\mathbf {b}(x)\\in \\{0,1\\}\\)to the opposite state\\(1-\\mathbf {b}(x)\\)only if there is a surplus greater than\\(a_x\\)of neighbors that express that opinion. The non-negative player-dependent parameter\\(a_x\\)is called thestubbornnessofx. We callstubbornthe players which never change opinion when they are part of the majority. We give a complete characterization of the graphs that do not retain information about the starting majority; i.e., they admit a starting state for which the heterogeneous majority dynamics takes the system from a majority of 0’s to a majority of 1’s. We call this phenomenon “minority becomes Majority” (ormbM) and our main result shows that it occurs in all graphs provided that at least one player is non-stubborn. In other words, either no player in the majority will ever change its state (because they are all stubborn) or there is a starting configuration in which information regarding the majority is not retained and minority becomes Majority. Our results are closely related todiscrete preference games, a game-theoretic model of opinion formation in social networks: an interplay of internal belief (corresponding to the initial state of the player) and of social pressure (described by the heterogeneous majority dynamics). Our results show that, because oflocalstrategic decisions, theglobalmajority can besubverted.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_3"}, {"primary_key": "3878172", "vector": [], "sparse_vector": [], "title": "The Strategy of Experts for Repeated Predictions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the behavior of experts who seek to make predictions with maximum impact on an audience. At a known future time, a certain continuous random variable will be realized. A public prediction gradually converges to the outcome, and an expert has access to a more accurate prediction. We study when the expert should reveal his information, when his reward is based on a proper scoring rule (e.g., is proportional to the change in log-likelihood of the outcome). In <PERSON><PERSON> et al. (2016), we analyzed the case where the expert may make a single prediction. In this paper, we analyze the case where the expert is allowed to revise previous predictions. This leads to a rather different set of dilemmas for the strategic expert. We find that it is optimal for the expert to always tell the truth, and to make a new prediction whenever he has a new signal. We characterize the expert’s expectation for his total reward, and show asymptotic limits.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_4"}, {"primary_key": "3878173", "vector": [], "sparse_vector": [], "title": "Shapley Facility Location Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Facility location games have been a topic of major interest in economics, operations research and computer science, starting from the seminal work by <PERSON>ling. Spatial facility location models have successfully predicted the outcome of competition in a variety of scenarios. In a typical facility location game, users/customers/voters are mapped to a metric space representing their preferences, and each player picks a point (facility) in that space. In most facility location games considered in the literature, users are assumed to act deterministically: given the facilities chosen by the players, users are attracted to their nearest facility. This paper introduces facility location games with probabilistic attraction, dubbedShapley facility location games, due to a surprising connection to the Shapley value. The specific attraction function we adopt in this model is aligned with the recent findings of the behavioral economics literature on choice prediction. Given this model, our first main result is that Shapley facility location games are potential games; hence, they possess pure Nash equilibrium. Moreover, the latter is true for any compact user space, any user distribution over that space, and any number of players. Note that this is in sharp contrast to Hotelling facility location games. In our second main result we show that under the assumption that players can compute an approximate best response, approximate equilibrium profiles can be learned efficiently by the players via dynamics. Our third main result is a bound on the Price of Anarchy of this class of games, as well as showing the bound is tight. Ultimately, we show that player payoffs coincide with their Shapley value in a coalition game, where coalition gains are the social welfare of the users.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_5"}, {"primary_key": "3878174", "vector": [], "sparse_vector": [], "title": "Coordination Mechanisms, Cost-Sharing, and Approximation Algorithms for Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We reveal a connection between coordination mechanisms for unrelated machine scheduling and cost-sharing protocols. Using this connection, we interpret three coordination mechanisms from the recent literature as Shapley-value-based cost-sharing protocols, thus providing a unifying justification regarding why these mechanisms induce potential games. More importantly, this connection provides a template for designing novel coordination mechanisms, as well as approximation algorithms for the underlying optimization problem. The designer need only decide the total cost to be suffered on each machine, and then the S<PERSON>pley value can be used to induce games guaranteed to possess a potential function; these games can, in turn, be used to design algorithms. To verify the power of this approach, we design a combinatorial algorithm that achieves an approximation guarantee of 1.81 for the problem of minimizing the total weighted completion time for unrelated machines. To the best of our knowledge, this is the best approximation guarantee among combinatorial polynomial-time algorithms for this problem.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_6"}, {"primary_key": "3878175", "vector": [], "sparse_vector": [], "title": "A Dynamics for Advertising on Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Nisheeth K. <PERSON>"], "summary": "We study the following question facing businesses in the world of online advertising: how should an advertising budget be spent when there are competing products? Broadly, there are two primary modes of advertising: (i) the equivalent of billboards in the real-world and (search or display) ads online that convert a percentage of the population that sees them, and (ii) social campaigns where the goal is to select a set of initial adopters who influence others to buy via their social network. Prior work towards the above question has largely focused on developing models to understand the effect of one mode or the other. We present a stochastic dynamics to model advertising in social networks that allows both and incorporates the three primary forces at work in such advertising campaigns: (1) the type of campaign – which can combine buying ads and seed selection, (2) the topology of the social network, and (3) the relative quality of the competing products. This model allows us to study the evolution of market share of multiple products with different qualities competing for the same set of users, and the effect that different advertising campaigns can have on the market share. We present theoretical results to understand the long-term behavior of the parameters on the market share and complement them with empirical results that give us insights about the, harder to mathematically understand, short-term behavior of the model.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_7"}, {"primary_key": "3878177", "vector": [], "sparse_vector": [], "title": "Limiting User&apos;s Sybil Attack in Resource Sharing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiang Yan"], "summary": "In this work, we discuss the sybil attack to a sharing economic system where each participant contributes its own resource for all to share. We are interested in the robustness of the market equilibrium mechanism in withstanding such an attack, in terms of the incentive ratio to measure how much one could gain by splitting its identity and reconstructing its communication connections with others. On one hand, weshow that no player can increase more than\\(\\sqrt{2}\\)times of their original share from the market equilibrium solution, by characterizing the worst case under which strategic agent can obtain the maximum utility gain after manipulation. On the other hand, such a bound of\\(\\sqrt{2}\\)is proved to be tight by constructing a proper instance, for which this bound is reached.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_8"}, {"primary_key": "3878178", "vector": [], "sparse_vector": [], "title": "Mechanism Design with Efficiency and Equality Considerations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we consider the problem of allocating a set of homogenous resources (goods) among multiple strategic players to balance the efficiency and equality from a game-theoretic perspective. For two very general classes of efficiency measures and equality measures, we develop a general truthful mechanism framework which optimally maximizes the resource holder’s efficiency while guaranteeing certain equality levels. We fully characterize the optimal allocation rule. Based on the characterizations, we show the optimal allocation and corresponding truthful payments can be computed in polynomial time, which means the truthful mechanism is computationally feasible.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_9"}, {"primary_key": "3878179", "vector": [], "sparse_vector": [], "title": "The Asymptotic Behavior of the Price of Anarchy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper examines the behavior of the price of anarchy as a function of the traffic inflow in nonatomic congestion games with multiple origin-destination (O/D) pairs. Empirical studies in real-world networks show that the price of anarchy is close to 1 in both light and heavy traffic, thus raising the question: can these observations be justified theoretically? We first show that this is not always the case: the price of anarchy may remain bounded away from 1 for all values of the traffic inflow, even in simple three-link networks with a single O/D pair and smooth, convex costs. On the other hand, for a large class of cost functions (including all polynomials), the price of anarchydoesconverge to 1 in both heavy and light traffic conditions, and irrespective of the network topology and the number of O/D pairs in the network.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_10"}, {"primary_key": "3878180", "vector": [], "sparse_vector": [], "title": "Fixed Price Approximability of the Optimal Gain from Trade.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bilateral tradeis a fundamental economic scenario comprising a strategically acting buyer and seller (holding an item), each holding valuations for the item, drawn from publicly known distributions. It was recently shown that the only mechanisms that are simultaneously dominant strategy incentive compatible, strongly budget balanced, and ex-post individually rational, arefixed pricemechanisms, i.e., mechanisms that are parametrised by a pricep, and trade occurs if and only if the valuation of the buyer is at leastpand the valuation of the seller is at mostp. Thegain from trade (GFT)is the increase in welfare that results from applying a mechanism. We study the GFT achievable by fixed price mechanisms. We explore this question for both the bilateral trade setting and adouble auctionsetting where there are multiple i.i.d. unit demand buyers and sellers. We first identify a fixed price mechanism that achieves a GFT of at least 2 /rtimes the optimum, whereris the probability that the seller’s valuation does not exceed that of the buyer’s valuation. This extends a previous result by McAfee. Subsequently, we improve this approximation factor in an asymptotic sense, by showing that a more sophisticated rule for setting the fixed price results in a GFT within a factor\\(O(\\log (1/r))\\)of the optimum. This is asymptotically the best approximation factor possible. For the double auction setting, we present a fixed price mechanism that achieves for all\\(\\epsilon > 0\\)a gain from trade of at least\\((1-\\epsilon )\\)times the optimum with probability\\(1 - 2/e^{\\#T \\epsilon ^2 /2}\\), where\\(\\#T\\)is the expected number of trades of the mechanism. This can be interpreted as a “large market” result: Full efficiency is achieved in the limit, as the market gets thicker.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_11"}, {"primary_key": "3878184", "vector": [], "sparse_vector": [], "title": "Sequential Deliberation for Social Choice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sukolsak Sakshuwong"], "summary": "Social choice is a normative study of designing protocols for collective decision making. However, in instances where the underlying decision space is too large or complex for ordinal voting, standard voting methods may be impractical. How then can we design a protocol - preferably decentralized, simple, scalable, and not requiring any special knowledge of the decision space - to reach consensus? We propose sequential deliberation as a natural solution to this problem. In this iterative method, successive pairs of agents bargain over the decision space using the previous decision as a disagreement alternative. We show that sequential deliberation finds a 1.208-approximation to the optimal social cost when the space of preferences define a median graph, coming very close to this value with only a small constant number of agents sampled from the population. We also give lower bounds on simpler classes of mechanisms to justify our design choices. We further show that sequential deliberation is ex-post Pareto efficient and has truthful reporting as an equilibrium of the induced extensive form game. Finally, we prove that for general metric spaces, the first and second moment of the distribution of social cost of the outcomes produced by sequential deliberation are also bounded by constants.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_13"}, {"primary_key": "3878185", "vector": [], "sparse_vector": [], "title": "Computing Approximate Pure Nash Equilibria in Shapley Value Weighted Congestion Games.", "authors": ["<PERSON>", "<PERSON>", "Grammateia Kotsialou", "<PERSON>"], "summary": "We study the computation of approximate pure Nash equilibria in Shapley value (SV) weighted congestion games, introduced in [19]. This class of games considers weighted congestion games in which Shapley values are used as an alternative (to proportional shares) for distributing the total cost of each resource among its users. We focus on the interesting subclass of such games with polynomial resource cost functions and present an algorithm that computes approximate pure Nash equilibria with a polynomial number of strategy updates. Since computing a single strategy update is hard, we apply sampling techniques which allow us to achieve polynomial running time. The algorithm builds on the algorithmic ideas of [7], however, to the best of our knowledge, this is the first algorithmic result on computation of approximate equilibria using other than proportional shares as player costs in this setting. We present a novel relation that approximates the Shapley value of a player by her proportional share and vice versa. As side results, we upper bound the approximate price of anarchy of such games and significantly improve the best known factor for computing approximate pure Nash equilibria in weighted congestion games of [7].", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_14"}, {"primary_key": "3878186", "vector": [], "sparse_vector": [], "title": "Socially Optimal Mining Pools.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mining for Bitcoins is a high-risk high-reward activity. Miners, seeking to reduce their variance and earn steadier rewards, collaborate in so-calledpooling strategieswhere they jointly mine for Bitcoins. Whenever some pool participant is successful, the earned rewards are appropriately split among all pool participants. Currently a dozen of different pooling strategies are in use for Bitcoin mining. We here propose a formal model of utility and social optimality for Bitcoin mining (and analogous mining systems) based on the theory of discounted expected utility, and next study pooling strategies that maximize the utility of participating miners in this model. We focus on pools that achieve a steady-state utility, where the utility per unit of work of all participating miners converges to a common value. Our main result shows that one of the pooling strategies actually employed in practice—the so-calledgeometric pay pool—achieves the optimal steady-state utility for miners when its parameters are set appropriately. Our results apply not only to Bitcoin mining pools, but any other form of pooled mining or crowdsourcing computations where the participants engage in repeated random trials towards a common goal, and where “partial” solutions can be efficiently verified.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_15"}, {"primary_key": "3878187", "vector": [], "sparse_vector": [], "title": "Cascades and Myopic Routing in Nonhomogeneous Kleinberg&apos;s Small World Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>’s small world model [20] simulates social networks with both strong and weak ties. In his original paper, <PERSON><PERSON> showed how the distribution of weak-ties, parameterized by\\(\\gamma \\), influences the efficacy of myopic routing on the network. Recent work on social influence byk-complex contagion models discovered that the distribution of weak-ties also impacts the spreading rate in a crucial manner on <PERSON><PERSON>’s small world model [15]. In both cases the parameter of\\(\\gamma = 2\\)proves special: when\\(\\gamma \\)is anything but 2 the properties no longer hold. In this work, we propose a natural generalization of <PERSON><PERSON>’s small world model to allow node heterogeneity: instead of a single global parameter\\(\\gamma \\), each node has a personalized parameter\\(\\gamma \\)chosen independently from a distribution\\(\\mathcal {D}\\). In contrast to the original model, we show that this model enables myopic routing andk-complex contagions on a large range of the parameter space, improving the robustness of the model. Moreover, we show that our generalization is supported by real-world data. Analysis of four different social networks shows that the nodes do not show homogeneity in terms of the variance of the lengths of edges incident to the same node.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_27"}, {"primary_key": "3878188", "vector": [], "sparse_vector": [], "title": "Design of an Optimal Frequency Reward Program in the Face of Competition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We optimize the design of a frequency reward program against traditional pricing in a competitive duopoly, where customers measure their utilities in rational economic terms. We assume two kinds of customers: myopic and strategic [19]. Every customer has a prior loyalty bias [6] toward the reward program merchant, a parameter drawn from a known distribution, indicating an additional probability of choosing the reward program merchant over the traditional pricing merchant. Under this model, we characterize the customer behavior: the loyalty bias increases the switching costs [11] of strategic customers until a tipping point, after which they strictly prefer and adopt the reward program merchant. Subsequently, we optimize the reward parameters to maximize the revenue objective of the reward program merchant. We show that under mild assumptions, the optimal parameters for the reward program design to maximize the revenue objective correspond exactly to minimizing the tipping point of customers and are independent of the customer population parameters. Moreover, we characterize the conditions for the reward program to be better when the loyalty bias distribution is uniform - a minimum fraction of population needs to be strategic, and the loyalty bias needs to be in an optimal range. If the bias is high, the reward program creates loss in revenues, as customers effectively gain rewards for “free”, whereas a low value of bias leads to loss in market share to the competing merchant. In short, if a merchant can estimate the customer population parameters, our framework and results provide theoretical guarantees on the pros and cons of running a reward program against traditional pricing.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_16"}, {"primary_key": "3878189", "vector": [], "sparse_vector": [], "title": "A Characterization of Undirected Graphs Admitting Optimal Cost Shares.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a seminal paper, <PERSON> et al. [7] studied cost sharing protocols for network design with the objective to implement a low-cost Steiner forest as a Nash equilibrium of an induced cost-sharing game. One of the most intriguing open problems to date is to understand the power of budget-balanced and separable cost sharing protocols in order to induce low-cost Steiner forests. In this work, we focus onundirectednetworks and analyze topological properties of the underlying graph so that anoptimal Steiner forestcan be implemented as a Nash equilibrium (by some separable cost sharing protocol)independentof the edge costs. We term a graphefficientif the above stated property holds. As our main result, we give a complete characterization of efficient undirected graphs for two-player network design games: an undirected graph is efficient if and only if it does not contain (at least) one out offew forbidden subgraphs. Our characterization implies that several graph classes are efficient: generalized series-parallel graphs, fan and wheel graphs and graphs with small cycles.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_17"}, {"primary_key": "3878190", "vector": [], "sparse_vector": [], "title": "Scale Effects in Web Search.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tao Qin", "<PERSON>"], "summary": "It is a well-known statistical property that learning tends to slow down with each additional data point. Thus even if scale effects are important in web search, they could be important in a range that any viable entrant could easily achieve. In this paper we address these questions using browsing logs that give click-through-rates by query on two major search engines. An ideal experiment would be to fix the “query difficulty” and exogenously provide more or less historical data. We approximate the ideal experiment by finding queries that were not previously observed. Of these “new queries”, some grow to be moderately popular, having 1000–2000 clicks in a calendar year. We examine ranking quality during the lifespan of the query and find statistically significant improvement on the order of 2–3% and learning faster at lower levels of data. We are careful to rule out alternate explanations for this pattern. In particular, we show that the effect is not explained by new, more relevant documents entering the landscape, rather it is mainly shifting the most relevant documents to the top of the ranking. We thus conclude they represent direct scale effects. Finally, we show that scale helps link new queries to existing queries with ample historical data by forming edges in the query document bipartite graph. This “indirect knowledge” is shown to be important for “deflating uniqueness” and improving ranking.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_21"}, {"primary_key": "3878191", "vector": [], "sparse_vector": [], "title": "Approximate Efficiency in Matching Markets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a measure of approximate ex-ante Pareto efficiency in matching markets. According to this measure, a lottery over matchings is\\(\\gamma \\)-approximately efficient if there is no alternate lottery in which each agent’s ex-ante expected utility increases by an\\(\\gamma \\)factor. A mechanism is\\(\\gamma \\)-approximately efficient if every lottery produced in equilibrium is\\(\\gamma \\)-approximately efficient. We argue this is the natural extension of approximate efficiency in transferable-utility settings to our nontransferable-utility setting. Using this notion, we are able to quantify the intuited efficiency improvement of the so-calledBoston mechanismand the recently-proposedchoice-augmented deferred acceptance mechanismover therandom serial dictatorship mechanism. Furthermore, we provide the first formal statement and analysis of theRaffle mechanism, which is conceptually simpler than the Boston mechanism and has a comparable efficiency guarantee.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_18"}, {"primary_key": "3878192", "vector": [], "sparse_vector": [], "title": "Routing Games over Time with FIFO Policy.", "authors": ["<PERSON><PERSON>"], "summary": "We study atomic routing games where every agent travels both along its decided edges and through time. The agents arriving on an edge are first lined up in afirst-in-first-outqueue and may wait: an edge is associated with a capacity, which defines how many agents-per-time-step can pop from the queue’s head and enter the edge, to transit for a fixed delay. We show that the best-response optimization problem is not approximable, and that deciding the existence of a Nash equilibrium is complete for the second level of the polynomial hierarchy. Then, we drop the rationality assumption, introduce a behavioral concept based on GPS navigation, and study its worst-case efficiency ratio to coordination.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_19"}, {"primary_key": "3878193", "vector": [], "sparse_vector": [], "title": "A Performance-Based Scheme for Pricing Resources in the Cloud.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid growth of the cloud computing marketplace, the issue of pricing resources in the cloud has been the subject of much study in recent years. In this paper, we identify and study a new issue: how to price resources in the cloud so that the customer’s risk is minimized, while at the same time ensuring that the provider accrues his fair share. We do this by correlating the revenue stream of the customer to the prices charged by the provider. We show that our mechanism is incentive compatible in that it is in the best interest of the customer to provide his true revenue as a function of the resources rented. We next add another restriction to the price function, i.e., that it be linear. This removes the distortion that creeps in when the customer has to pay more money for less resources. Our algorithms for both the schemes mentioned above are efficient.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_20"}, {"primary_key": "3878194", "vector": [], "sparse_vector": [], "title": "On Strong Equilibria and Improvement Dynamics in Network Creation Games.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study strong equilibria in network creation games. These form a classical and well-studied class of games where a set of players form a network by buying edges to their neighbors at a cost of a fixed parameter\\(\\alpha \\). The cost of a player is defined to be the cost of the bought edges plus the sum of distances to all the players in the resulting graph. We identify and characterize various structural properties of strong equilibria, which lead to a characterization of the set of strong equilibria for all\\(\\alpha \\)in the range (0, 2). For\\(\\alpha > 2\\), <PERSON><PERSON><PERSON> et al. [4] prove that a star graph in which every leaf buys one edge to the center node is a strong equilibrium, and conjecture that in factanystar is a strong equilibrium. We resolve this conjecture in the affirmative. Additionally, we show that when\\(\\alpha \\)is large enough (\\(\\ge 2n\\)) there exist non-star trees that are strong equilibria. For the strong price of anarchy, we provide precise expressions when\\(\\alpha \\)is in the range (0, 2), and we prove a lower bound of 3/2 when\\(\\alpha \\ge 2\\). Lastly, we aim to characterize under which conditions (coalitional) improvement dynamics may converge to a strong equilibrium. To this end, we study the (coalitional) finite improvement property and (coalitional) weak acyclicity property. We prove various conditions under which these properties do and do not hold. Some of these results also hold for the class of pure Nash equilibria.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_12"}, {"primary_key": "3878195", "vector": [], "sparse_vector": [], "title": "Simple Pricing Schemes for the Cloud.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The problem of pricing the cloud has attracted much recent attention due to the widespread use of cloud computing and cloud services. From a theoretical perspective, several mechanisms that provide strong efficiency or fairness guarantees and desirable incentive properties have been designed. However, these mechanisms often rely on a rigid model, with several parameters needing to be precisely known in order for the guarantees to hold. In this paper, we consider a stochastic model and show that it is possible to obtain good welfare and revenue guarantees with simple mechanisms that do not make use of the information on some of these parameters. In particular, we prove that a mechanism that sets the same price per time step for jobs of any length achieves at least\\(50\\%\\)of the welfare and revenue obtained by a mechanism that can set different prices for jobs of different lengths, and the ratio can be improved if we have more specific knowledge of some parameters. Similarly, a mechanism that sets the same price for all servers even though the servers may receive different kinds of jobs can provide a reasonable welfare and revenue approximation compared to a mechanism that is allowed to set different prices for different servers.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_22"}, {"primary_key": "3878197", "vector": [], "sparse_vector": [], "title": "Routing Games in the Wild: Efficiency, Equilibration and Regret - Large-Scale Field Experiments in Singapore.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Routing games are amongst the most well studied domains of game theory. How relevant are these theoretical models and results to capturing the reality of everyday traffic? We focus on a semantically rich dataset that captures detailed information about the daily behavior of thousands of Singaporean commuters and examine the following basic questions: Does the traffic equilibrate? Is the system behavior consistent with latency minimizing agents? Is the resulting system efficient? The answers to all three questions are shown to be largely positive. Finally, in order to capture the efficiency of the traffic network in a way that agrees with our everyday intuition we introduce a new metric, thestress of catastrophe, which reflects the combined inefficiencies of both tragedy of the commons as well as price of anarchy effects.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_24"}, {"primary_key": "3878198", "vector": [], "sparse_vector": [], "title": "Dynamic Pricing in Competitive Markets.", "authors": ["<PERSON><PERSON>"], "summary": "Dynamic pricing of goods in a competitive environment to maximize revenue is a natural objective and has been a subject of research over the years. In this paper, we focus on a class of markets exhibiting the substitutes property with sellers having divisible and replenishable goods. Depending on the prices chosen, each seller observes a certain demand which is satisfied subject to the supply constraint. The goal of the seller is to price her good dynamically so as to maximize her revenue. For the static market case, when the consumer utility satisfies the gross substitutes CES property, we give a\\(O(\\sqrt{T})\\)regret bound on the maximum loss in revenue of a seller using a modified version of the celebrated Online Gradient Descent algorithm by <PERSON><PERSON><PERSON> [17]. For a more specialized set of consumer utilities satisfying the iso-elasticity condition, we show that when each seller uses a regret-minimizing algorithm satisfying a certain technical property, the regret with respect to\\((1-\\alpha )\\)times optimal revenue is bounded as\\(O(T^{1/4} / \\sqrt{\\alpha })\\). We extend this result to markets with dynamic supplies and prove a corresponding dynamic regret bound, whose guarantee deteriorates smoothly with the inherent instability of the market. As a side-result, we also extend the previously known convergence results of these algorithms in a general game to the dynamic setting.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_25"}, {"primary_key": "3878199", "vector": [], "sparse_vector": [], "title": "Beyond Worst-Case (In)approximability of Nonsubmodular Influence Maximization.", "authors": ["<PERSON>", "Biaosh<PERSON><PERSON>"], "summary": "We consider the problem of maximizing the spread of influence in a social network by choosing a fixed number of initial seeds, formally referred to as theinfluence maximization problem. It admits a\\((1-1/e)\\)-factor approximation algorithm if the influence function issubmodular. Otherwise, in the worst case, the problem is NP-hard to approximate to within a factor of\\(N^{1-\\varepsilon }\\), whereNis the number of vertices in the graph. This paper studies whether this worst-case hardness result can be circumvented by making assumptions about either the underlying network topology or the cascade model. All of our assumptions are motivated by many real life social network cascades. First, we present strong inapproximability results for a very restricted class of networks called the(stochastic) hierarchical blockmodel, a special case of the well-studied(stochastic) blockmodelin which relationships between blocks admit a tree structure. We also provide a dynamic-program based polynomial time algorithm which optimally computes a directed variant of the influence maximization problem on hierarchical blockmodel networks. Our algorithm indicates that the inapproximability result is due to the bidirectionality of influence between agent-blocks. Second, we present strong inapproximability results for a class of influence functions that are “almost” submodular, called2-quasi-submodular. Our inapproximability results hold even for any 2-quasi-submodularffixed in advance. This result also indicates that the “threshold” between submodularity and nonsubmodularity is sharp, regarding the approximability of influence maximization.", "published": "2017-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-319-71924-5_26"}]