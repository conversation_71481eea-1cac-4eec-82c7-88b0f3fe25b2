import { useState, useEffect } from 'react';
import { Paper } from './class-utils';
import { toast } from 'react-hot-toast';

interface UseSearchProps {
  selectedYears: string[];
  getSourceParam: () => string;
}

export const useSearch = ({ selectedYears, getSourceParam }: UseSearchProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Paper[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchState, setSearchState] = useState<'搜索中...' | '翻译中...'>('搜索中...');
  const [isTitleTranslation, setIsTitleTranslation] = useState(true);

  // 检查所有搜索结果的摘要精炼
  useEffect(() => {
    const refineAbstracts = async () => {
      const needRefinement = searchResults.map((paper, index) => ({
        index,
        needsRefinement: !paper.abstract_summary,
        title: paper.title,
        summary: paper.summary
      })).filter(item => item.needsRefinement);

      if (needRefinement.length === 0) return;

      try {
        setIsSearching(true);
        setSearchState("翻译中...")

        const polish_prompt = 
          "\n\n请按照以下要求进行摘要精炼:" +
          "\n1. 你的任务是为用户提供一个简洁清晰的论文内容概述" + 
          "\n2. 输入包含两部分：search_query(用户搜索关键词)和summary(论文摘要)" +
          "\n3. 输出abstract_summary应该:" +
          "\n   - 主要基于summary内容进行精炼，突出论文的独特贡献和发现" +
          "\n   - 如果summary中提到了与search_query相关的内容，应适当体现，但不要过分强调" +
          "\n   - 不要将search_query作为论文的主题领域，应该使用论文本身的研究领域" +
          "\n   - 不要为了迎合search_query而添加原文中不存在的内容" +
          "\n4. 精炼时要注意:" +
          "\n   - 保持客观准确，不要过度解读" +
          "\n   - 突出本篇论文的特色，便于用户区分不同论文" +
          "\n   - 使用清晰的语言结构，便于快速阅读理解";
        
        const refinedResponse = await fetch('/api/abstract-refinement-multi', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(
            needRefinement.map(item => polish_prompt + "search_query: " + searchQuery + " \n\n summary: " + item.summary)
          ),
        });

        if (!refinedResponse.ok) {
          throw new Error(`摘要精炼请求失败: ${refinedResponse.statusText}`);
        }

        const data = await refinedResponse.json();
        
        if (!data || !Array.isArray(data)) {
          console.error('摘要精炼返回数据格式错误:', data);
          return;
        }

        if (data.length !== needRefinement.length) {
          console.error('摘要精炼返回数量不匹配');
          return;
        }

        setSearchResults(prev => {
          const newResults = [...prev];
          needRefinement.forEach((item, arrayIndex) => {
            if (data[arrayIndex]) {
              newResults[item.index] = {
                ...newResults[item.index],
                abstract_summary: data[arrayIndex]
              };
            }
          });
          return newResults;
        });

      } catch (error) {
        console.error('摘要精炼失败:', error);
      } finally {
        setIsSearching(false);
        setSearchState("搜索中...")
      }
    };
    refineAbstracts();
  }, [searchResults, searchQuery]);

  // 检查所有搜索结果的标题翻译
  useEffect(() => {
    const translateTitles = async () => {
      if (!isTitleTranslation) return;

      const needTranslation = searchResults.map((paper, index) => ({
        index,
        needsTranslation: !paper.title_translation,
        title: paper.title
      })).filter(item => item.needsTranslation);

      if (needTranslation.length === 0) return;

      try {
        setIsSearching(true);
        setSearchState("翻译中...")

        const translationResponse = await fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: needTranslation.map(item => item.title)
          }),
        });

        if (!translationResponse.ok) {
          throw new Error(`标题翻译请求失败: ${translationResponse.statusText}`);
        }

        const data = await translationResponse.json();
        
        if (!data || !data.translation) {
          console.error('标题翻译返回数据格式错误:', data);
          return;
        }

        const translations = Array.isArray(data.translation) 
          ? data.translation 
          : JSON.parse(data.translation);

        if (translations.length !== needTranslation.length) {
          console.error('标题翻译返回数量不匹配');
          return;
        }

        setSearchResults(prev => {
          const newResults = [...prev];
          needTranslation.forEach((item, arrayIndex) => {
            if (translations[arrayIndex]) {
              newResults[item.index] = {
                ...newResults[item.index],
                title_translation: translations[arrayIndex]
              };
            }
          });
          return newResults;
        });

      } catch (error) {
        console.error('标题翻译失败:', error);
      } finally {
        setIsSearching(false);
        setSearchState("搜索中...")
      }
    };
    translateTitles();
  }, [searchResults, isTitleTranslation]);

  // 保存标题翻译设置到本地存储
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('isTitleTranslation', JSON.stringify(isTitleTranslation));
    }
  }, [isTitleTranslation]);



  const handleSearch = async (showSearchingIndicator = true) => {
    if (!searchQuery.trim()) return;
    
    if (showSearchingIndicator) setIsSearching(true);
    try {
      const response = await fetch(
        `/api/semantic-search?query=${encodeURIComponent(searchQuery)}&page=1&limit=128&years=${selectedYears.join(',')}&deepsearch=true&source=${encodeURIComponent(getSourceParam())}`
      );
      if (!response.ok) throw new Error('搜索请求失败');
      const results = await response.json();
      
      // 更新搜索结果，同时确保所有 PDF URL 使用 HTTPS
      const updatedResults = results.map((paper: Paper) => ({
        ...paper,
        pdf_url: paper.pdf_url.startsWith('https') ? paper.pdf_url : paper.pdf_url.replace(/^http/, 'https')
      }));
      
      setSearchResults(updatedResults);
    } catch (error) {
      console.error('搜索或摘要精炼错误:', error);
      toast.error('处理失败，请稍后重试');
    } finally {
      if (showSearchingIndicator) setIsSearching(false);
    }
  };

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    setSearchResults,
    isSearching,
    searchState,
    isTitleTranslation,
    setIsTitleTranslation,
    handleSearch
  };
};