[{"primary_key": "2689290", "vector": [], "sparse_vector": [], "title": "Interactive Program Synthesis by Augmented Examples.", "authors": ["<PERSON><PERSON><PERSON>", "London Lowmanstone", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Programming-by-example (PBE) has become an increasingly popular component in software development tools, human-robot interaction, and end-user programming. A long-standing challenge in PBE is the inherent ambiguity in user-provided examples. This paper presents an interaction model to disambiguate user intent and reduce the cognitive load of understanding and validating synthesized programs. Our model provides two types of augmentations to user-given examples: 1) semantic augmentation where a user can specify how different aspects of an example should be treated by a synthesizer via light-weight annotations, and 2) data augmentation where the synthesizer generates additional examples to help the user understand and validate synthesized programs. We implement and demonstrate this interaction model in the domain of regular expressions, which is a popular mechanism for text processing and data wrangling and is often considered hard to master even for experienced programmers. A within-subjects user study with twelve participants shows that, compared with only inspecting and annotating synthesized programs, interacting with augmented examples significantly increases the success rate of finishing a programming task with less time and increases users? confidence of synthesized programs.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415900"}, {"primary_key": "2689291", "vector": [], "sparse_vector": [], "title": "Interactive Exploration and Refinement of Facial Expression using Manifold Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Posing expressive 3D faces is extremely challenging. Typical facial rigs have upwards of 30 controllable parameters, that while anatomically meaningful, are hard to use due to redundancy of expression, unrealistic configurations, and many semantic and stylistic correlations between the parameters. We propose a novel interface for rapid exploration and refinement of static facial expressions, based on a data-driven face manifold of natural expressions. Rapidly explored face configurations are interactively projected onto this manifold of meaningful expressions. These expressions can then be refined using a 2D embedding of nearby faces, both on and off the manifold. Our validation is fourfold: we show expressive face creation using various devices; we verify that our learnt manifold transcends its training face, to expressively control very different faces; we perform a crowd-sourced study to evaluate the quality of manifold face expressions; and we report on a usability study that shows our approach is an effective interactive tool to author facial expression.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415877"}, {"primary_key": "2689292", "vector": [], "sparse_vector": [], "title": "Remo: Generating Interactive Tutorials by Demonstration for Online Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "People with limited digital literacy struggle to keep up with our increasing dependence on websites for everyday tasks like paying bills or booking flight tickets online. They often get in-person assistance from their friends and family but such help may not always be possible. Remote assistance from peers such as phone calls, or written instructions via email or text messages can be useful. However, remote methods of assistance may lead to communication issues between the helper and the help-seeker, due to a lack of shared visual context; a helper cannot see the help-seeker's screen. Moreover, help-seekers are often unacquainted with the terminology associated with web navigation. In order to bridge the gap between in-person support and remote help, we develop Remo, a web browser extension, which will allow helpers to create interactive tutorials by demonstration. These tutorials will be embedded within a web page and will make use of visual cues to direct users to specific parts of the page. <PERSON>mo aims to provide opportunities for people with limited digital literacy to complete online tasks by following the step-by-step and task specific tutorials generated by their peers. Using Remo, we anticipate that the target population will be able to get personalized assistance, similar to in-person support, and eventually learn how to complete broader online tasks independently.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416194"}, {"primary_key": "2689293", "vector": [], "sparse_vector": [], "title": "Direction-of-Voice (DoV) Estimation for Intuitive Speech Interaction with Smart Devices Ecosystems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Future homes and offices will feature increasingly dense ecosystems of IoT devices, such as smart lighting, speakers, and domestic appliances. Voice input is a natural candidate for interacting with out-of-reach and often small devices that lack full-sized physical interfaces. However, at present, voice agents generally require wake-words and device names in order to specify the target of a spoken command (e.g., 'Hey <PERSON><PERSON>, kitchen lights to full bright-ness'). In this research, we explore whether speech alone can be used as a directional communication channel, in much the same way visual gaze specifies a focus. Instead of a device's microphones simply receiving and processing spoken commands, we suggest they also infer the Direction of Voice (DoV). Our approach innately enables voice commands with addressability (i.e., devices know if a command was directed at them) in a natural and rapid manner. We quantify the accuracy of our implementation across users, rooms, spoken phrases, and other key factors that affect performance and usability. Taken together, we believe our DoV approach demonstrates feasibility and the promise of making distributed voice interactions much more intuitive and fluid.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415588"}, {"primary_key": "2689294", "vector": [], "sparse_vector": [], "title": "Modeling and Reducing Spatial Jitter caused by Asynchronous Input and Output Rates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Jitter in interactive systems occurs when visual feedback is perceived as unstable or trembling even though the input signal is smooth or stationary. It can have multiple causes such as sensing noise, or feedback calculations introducing or exacerbating sensing imprecisions. Jitter can however occur even when each individual component of the pipeline works perfectly, as a result of the differences between the input frequency and the display refresh rate. This asynchronicity can introduce rapidly-shifting latencies between the rendered feedbacks and their display on screen, which can result in trembling cursors or viewports. % This paper contributes a better understanding of this particular type of jitter. We first detail the problem from a mathematical standpoint, from which we develop a predictive model of jitter amplitude as a function of input and output frequencies, and a new metric to measure this spatial jitter. Using touch input data gathered in a study, we developed a simulator to validate this model and to assess the effects of different techniques and settings with any output frequency. The most promising approach, when the time of the next display refresh is known, is to estimate (interpolate or extrapolate) the user's position at a fixed time interval before that refresh. % When input events occur at 125~Hz, as is common in touch screens, we show that an interval of 4 to 6~ms works well for a wide range of display refresh rates. This method effectively cancels most of the jitter introduced by input/output asynchronicity, while introducing minimal imprecision or latency.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415833"}, {"primary_key": "2689295", "vector": [], "sparse_vector": [], "title": "EasyEG: A 3D-printable Brain-Computer Interface.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Brain-Computer Interfaces (BCIs) are progressively adopted by the consumer market, making them available for a variety of use-cases. However, off-the-shelf BCIs are limited in their adjustments towards individual head shapes, evaluation of scalp-electrode contact, and extension through additional sensors. This work presents EasyEG, a BCI headset that is adaptable to individual head shapes and offers adjustable electrode-scalp contact to improve measuring quality. EasyEG consists of 3D-printed and low-cost components that can be extended by additional sensing hardware, hence expanding the application domain of current BCIs. We conclude with use-cases that demonstrate the potentials of our EasyEG headset.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416189"}, {"primary_key": "2689296", "vector": [], "sparse_vector": [], "title": "TileCode: Creation of Video Games on Gaming Handhelds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present TileCode, a video game creation environment that runs on battery-powered microcontroller-based gaming handhelds. Our work is motivated by the popularity of retro video games, the availability of low-cost gaming handhelds loaded with many such games, and the concomitant lack of a means to create games on the same handhelds. With TileCode, we seek to close the gap between the consumers and creators of video games and to motivate more individuals to participate in the design and creation of their own games. The TileCode programming model is based on tile maps and provides a visual means for specifying the context around a sprite, how a sprite should move based on that context, and what should happen upon sprite collisions. We demonstrate that a variety of popular video games can be programmed with TileCode using 10-15 visual rules and compare/contrast with block-based versions of the same games implemented using MakeCode Arcade.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415839"}, {"primary_key": "2689297", "vector": [], "sparse_vector": [], "title": "Context-Based 3D Grids for Augmented Reality User Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Accurate 3D registration of real and virtual objects is a crucial step in AR, especially when manipulating those objects in space. Previous work simplifies mid-air 3D manipulations by removing one or more degrees of freedom by constraining motion using automatic algorithms. However, when designing objects, limiting the user's actions can affect their creativity. To solve this problem, we present a new system called Context-based 3D Grids that allows users to do precise mid-air 3D manipulations without constraining their actions. Our system creates 3D grids for each object in the scene that change depending on the object pose. Users can display additional reference frames inside the virtual environment using natural hand gestures that are commonly used when designing an object. Our goal is to help users visualize more clearly the spatial relation and the differences in pose and size of the objects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416190"}, {"primary_key": "2689298", "vector": [], "sparse_vector": [], "title": "UIST+CSCW: A Celebration of Systems Research in Collaborative and Social Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This joint panel between UIST and CSCW brings together leading researchers at the intersection of the conferences-systems researchers in collaborative and social computing-to engage in a discussion and retrospective. Pairs of panelists will represent each decade since the founding of the conferences, sharing a brief retrospective that surveys the most influential papers of that decade, the zeitgeist of the problems that were popular that decade and why, and what each decade's work has to say to the decades that came before and after. The panel is intended as a space to celebrate advances in the field, and reflect on the burdens and opportunities that it faces ahead.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3422374"}, {"primary_key": "2689299", "vector": [], "sparse_vector": [], "title": "CoVR: A Large-Scale Force-Feedback Robotic Interface for Non-Deterministic Scenarios in VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present CoVR, a novel robotic interface providing strong kinesthetic feedback (100 N) in a room-scale VR arena. It consists of a physical column mounted on a 2D Cartesian ceiling robot (XY displacements) with the capacity of (1) resisting to body-scaled users' actions such as pushing or leaning; (2) acting on the users by pulling or transporting them as well as (3) carrying multiple potentially heavy objects (up to 80kg) that users can freely manipulate or make interact with each other. We describe its implementation and define a trajectory generation algorithm based on a novel user intention model to support non-deterministic scenarios, where the users are free to interact with any virtual object of interest with no regards to the scenarios' progress. A technical evaluation and a user study demonstrate the feasibility and usability of CoVR, as well as the relevance of whole-body interactions involving strong forces, such as being pulled through or transported.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415891"}, {"primary_key": "2689300", "vector": [], "sparse_vector": [], "title": "SurfaceFleet: Exploring Distributed Interactions Unbounded from Device, Application, User, and Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowledge work increasingly spans multiple computing surfaces. Yet in status quo user experiences, content as well as tools, behaviors, and workflows are largely bound to the current device-running the current application, for the current user, and at the current moment in time. SurfaceFleet is a system and toolkit that uses resilient distributed programming techniques to explore cross-device interactions that are unbounded in these four dimensions of device, application, user, and time. As a reference implementation, we describe an interface built using SurfaceFleet that employs lightweight, semi-transparent UI elements known as Applets. Applets appear always-on-top of the operating system, application windows, and (conceptually) above the device itself. But all connections and synchronized data are virtualized and made resilient through the cloud. For example, a sharing Applet known as a Portfolio allows a user to drag and drop unbound Interaction Promises into a document. Such promises can then be fulfilled with content asynchronously, at a later time (or multiple times), from another device, and by the same or a different user.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415874"}, {"primary_key": "2689301", "vector": [], "sparse_vector": [], "title": "Mesh: Scaffolding Comparison Tables for Online Decision Making.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While there is an enormous amount of information online for making decisions such as choosing a product, restaurant, or school, it can be costly for users to synthesize that information into confident decisions. Information for users' many different criteria needs to be gathered from many different sources into a structure where they can be compared and contrasted. The usefulness of each criterion for differentiating potential options can be opaque to users, and evidence such as reviews may be subjective and conflicting, requiring users to interpret each under their personal context. We introduce Mesh, which scaffolds users to iteratively build up a better understanding of both their criteria and options by evaluating evidence gathered across sources in the context of consumer decision-making. Mesh bridges the gap between decision support systems that typically have rigid structures and the fluid and dynamic process of exploratory search, changing the cost structure to provide increasing payoffs with greater user investment. Our lab and field deployment studies found evidence that Mesh significantly reduces the costs of gathering and evaluating evidence and scaffolds decision-making through personalized criteria enabling users to gain deeper insights from data.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415865"}, {"primary_key": "2689302", "vector": [], "sparse_vector": [], "title": "C-Face: Continuously Reconstructing Facial Expressions by Deep Learning Contours of the Face with Ear-mounted Miniature Cameras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "C-Face (Contour-Face) is an ear-mounted wearable sensing technology that uses two miniature cameras to continuously reconstruct facial expressions by deep learning contours of the face. When facial muscles move, the contours of the face change from the point of view of the ear-mounted cameras. These subtle changes are fed into a deep learning model which continuously outputs 42 facial feature points representing the shapes and positions of the mouth, eyes and eyebrows. To evaluate C-Face, we embedded our technology into headphones and earphones. We conducted a user study with nine participants. In this study, we compared the output of our system to the feature points outputted by a state of the art computer vision library (Dlib) from a font facing camera. We found that the mean error of all 42 feature points was 0.77 mm for earphones and 0.74 mm for headphones. The mean error for 20 major feature points capturing the most active areas of the face was 1.43 mm for earphones and 1.39 mm for headphones. The ability to continuously reconstruct facial expressions introduces new opportunities in a variety of applications. As a demonstration, we implemented and evaluated C-Face for two applications: facial expression detection (outputting emojis) and silent speech recognition. We further discuss the opportunities and challenges of deploying C-Face in real-world applications.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415879"}, {"primary_key": "2689303", "vector": [], "sparse_vector": [], "title": "Automatic Video Creation From a Web Page.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Creating marketing videos from scratch can be challenging, especially when designing for multiple platforms with different viewing criteria. We present URL2Video, an automatic approach that converts a web page into a short video given temporal and visual constraints. URL2Video captures quality materials and design styles extracted from a web page, including fonts, colors, and layouts. Using constraint programming, URL2Video's design engine organizes the visual assets into a sequence of shots and renders to a video with user-specified aspect ratio and duration. Creators can review the video composition, modify constraints, and generate video variation through a user interface. We learned the design process from designers and compared our automatically generated results with their creation through interviews and an online survey. The evaluation shows that URL2Video effectively extracted design elements from a web page and supported designers by bootstrapping the video creation process.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415814"}, {"primary_key": "2689304", "vector": [], "sparse_vector": [], "title": "BodyPrinter: Fabricating Circuits Directly on the Skin at Arbitrary Locations Using a Wearable Compact Plotter.", "authors": ["Youngkyung Choi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "On-body electronics and sensors offer the opportunity to seamlessly augment the human with computing power. Accordingly, numerous previous work investigated methods that exploit conductive materials and flexible substrates to fabricate circuits in the form of wearable devices, stretchable patches, and stickers that can be attached to the skin. For all these methods, the fabrication process involves several manual steps, such as designing the circuit in software, constructing conductive patches, and manually placing these physical patches on the body. In contrast, in this work, we propose to fabricate electronics directly on the skin. We present BodyPrinter, a wearable conductive-ink deposition machine, that prints flexible electronics directly on the body using skin-safe conductive ink. The paper describes our system in detail and, through a series of examples and a technical evaluation, we show how direct on-body fabrication of electronic circuits and sensors can further enhance the human body.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415840"}, {"primary_key": "2689305", "vector": [], "sparse_vector": [], "title": "Reactive Video: Adaptive Video Playback Based on User Motion for Supporting Physical Activity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Videos are a convenient platform to begin, maintain, or improve a fitness program or physical activity. Traditional video systems allow users to manipulate videos through specific user interface actions such as button clicks or mouse drags, but have no model of what the user is doing and are unable to adapt in useful ways. We present adaptive video playback, which seamlessly synchronises video playback with the user's movements, building upon the principle of direct manipulation video navigation. We implement adaptive video playback in Reactive Video, a vision-based system which supports users learning or practising a physical skill. The use of pre-existing videos removes the need to create bespoke content or specially authored videos, and the system can provide real-time guidance and feedback to better support users when learning new movements. Adaptive video playback using a discrete Bayes and particle filter are evaluated on a data set collected of participants performing tai chi and radio exercises. Results show that both approaches can accurately adapt to the user's movements, however reversing playback can be problematic.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415591"}, {"primary_key": "2689306", "vector": [], "sparse_vector": [], "title": "Actuation Plate for Multi-Layered in-Vehicle Control Panel.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High-fidelity localized feedback has the potential of providing new and unique levels of interaction with a given device. Achieving this in a cost-effective reproducible manner has been a challenge in modern technology. Past experiments have shown that by using the principles of constructive wave interference introduced by time offsets it is possible to achieve a position of increased vibration displacement at any given location. As new interface form factors increasingly incorporate curved surfaces, we now show that these same techniques can successfully be applied and mechanically coupled with a universal actuation plate.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416179"}, {"primary_key": "2689307", "vector": [], "sparse_vector": [], "title": "Design Justice and User Interface Design.", "authors": ["<PERSON>-<PERSON><PERSON>"], "summary": "In this keynote talk, Dr<PERSON> will explore the theory and practice of design justice, discuss how design affordances, disaffordances, and dysaffordances distribute benefits and burdens unequally according to users? location within the matrix of domination (white supremacy, heteropatriarchy, ableism, capitalism, and settler colonialism), and invite us to consider how user interface designers can intentionally contribute to building ?a better world?, a world where many worlds fit; linked worlds of collective liberation and ecological sustainability.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3422379"}, {"primary_key": "2689308", "vector": [], "sparse_vector": [], "title": "JustCorrect: Intelligent Post Hoc Text Correction Techniques on Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Correcting errors in entered text is a common task but usually difficult to perform on mobile devices due to tedious cursor navigation steps. In this paper, we present JustCorrect, an intelligent post hoc text correction technique for smartphones. To make a correction, the user simply types the correct text at the end of their current input, and JustCorrect will automatically detect the error and apply the correction in the form of an insertion or a substitution. In this way, manual navigation steps are bypassed, and the correction can be committed with a single tap. We solved two critical problems to support JustCorrect: (1) Correction Algorithm: we propose an algorithm that infers the user's correction intention from the last typed word. (2) Input Modalities: our study revealed that both tap and gesture were suitable input modalities for performing JustCorrect. Based on our findings, we integrated JustCorrect into a soft keyboard. Our user studies show that using JustCorrect reduces the text correction time by 12.8% over the stock Android keyboard and by 9.7% over the \"Type, then Correct\" text correction technique by <PERSON> et al. (2019). Overall, JustCorrect complements existing post hoc text correction techniques, making error correction more automatic and intelligent.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415857"}, {"primary_key": "2689309", "vector": [], "sparse_vector": [], "title": "Haptics with Input: Back-EMF in Linear Resonant Actuators to Enable Touch, Pressure and Environmental Awareness.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's wearable and mobile devices typically use separate hardware components for sensing and actuation. In this work, we introduce new opportunities for the Linear Resonant Actuator (LRA), which is ubiquitous in such devices due to its capability for providing rich haptic feedback. By leveraging strategies to enable active and passive sensing capabilities with LRAs, we demonstrate their benefits and potential as self-contained I/O devices. Specifically, we use the back-EMF voltage to classify if the LRA is tapped, touched, as well as how much pressure is being applied. The back-EMF sensing is already integrated into many motor and LRA drivers. We developed a passive low-power tap sensing method that uses just 37.7 uA. Furthermore, we developed active touch and pressure sensing, which is low-power, quiet (2 dB), and minimizes vibration. The sensing method works with many types of LRAs. We show applications, such as pressure-sensing side-buttons on a mobile phone. We have also implemented our technique directly on an existing mobile phone's LRA to detect if the phone is handheld or placed on a soft or hard surface. Finally, we show that this method can be used for haptic devices to determine if the LRA makes good contact with the skin. Our approach can add rich sensing capabilities to the ubiquitous LRA actuators without requiring additional sensors or hardware.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415823"}, {"primary_key": "2689310", "vector": [], "sparse_vector": [], "title": "DepthLab: Real-time 3D Interaction with Depth Maps for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile devices with passive depth sensing capabilities are ubiquitous, and recently active depth sensors have become available on some tablets and AR/VR devices. Although real-time depth data is accessible, its rich value to mainstream AR applications has been sorely under-explored. Adoption of depth-based UX has been impeded by the complexity of performing even simple operations with raw depth data, such as detecting intersections or constructing meshes. In this paper, we introduce DepthLab, a software library that encapsulates a variety of depth-based UI/UX paradigms, including geometry-aware rendering (occlusion, shadows), surface interaction behaviors (physics-based collisions, avatar path planning), and visual effects (relighting, 3D-anchored focus and aperture effects). We break down the usage of depth into localized depth, surface depth, and dense depth, and describe our real-time algorithms for interaction and rendering tasks. We present the design process, system, and components of DepthLab to streamline and centralize the development of interactive depth features. We have open-sourced our software at https://github.com/googlesamples/arcore-depth-lab to external developers, conducted performance evaluation, and discussed how DepthLab can accelerate the workflow of mobile AR designers and developers. With DepthLab we aim to help mobile developers to effortlessly integrate depth into their AR experiences and amplify the expression of their creative vision.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415881"}, {"primary_key": "2689311", "vector": [], "sparse_vector": [], "title": "Experiencing Real-time 3D Interaction with Depth Maps for Mobile Augmented Reality in DepthLab.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We demonstrate DepthLab, a playground for interactive augmented reality experiences leveraging the shape and depth of the physical environment on a mobile phone. Based on the ARCore Depth API, DepthLab encapsulates a variety of depth-based UI/UX paradigms, including geometry-aware rendering (occlusion, shadows, texture decals), surface interaction behaviors (physics, collision detection, avatar path planning), and visual effects (relighting, 3D-anchored focus and aperture effects, 3D photos). We have open-sourced our software at https://github.com/googlesamples/arcore-depth-lab to facilitate future research and development in depth-aware mobile AR experiences. With DepthLab, we aim to help mobile developers to effortlessly integrate depth into their AR experiences and amplify the expression of their creative vision.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416136"}, {"primary_key": "2689312", "vector": [], "sparse_vector": [], "title": "Enhancing Vibrotactile Signal Propagation using Sub-Surface 3D-Printed Waveguides.", "authors": ["<PERSON>", "Hong Z. Tan", "<PERSON><PERSON><PERSON>"], "summary": "The mediation of haptic feedback largely depends on physical properties of the surface of interaction and its internal structure. Most mobile devices today are assembled using a wide range of components with varied physical properties that can limit the propagation of tactile signals. For that reason, it is important to understand how common materials used in assembling mobile devices relay vibration signals and develop techniques of effectively mediating haptic feedback uniformly throughout the entire device. This research compares three off-the-shelf waveguide materials and one custom-designed 3D-printed ABS structure for creating feedback signals on flat interaction surfaces. Preliminary results indicate that by altering the internal structure of the interaction surface we can reduce attenuation and integration of the applied signal and improving the overall haptic experience.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416182"}, {"primary_key": "2689313", "vector": [], "sparse_vector": [], "title": "The Virtual Reality Questionnaire Toolkit.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we present the VRQuestionnaireToolkit, which enables the research community to easily collect subjective measures within virtual reality (VR). We contribute a highly customizable and reusable open-source toolkit which can be integrated in existing VR projects rapidly. The toolkit comes with a pre-installed set of standard questionnaires such as NASA TLX, SSQ and SUS Presence questionnaire. Our system aims to lower the entry barrier to use questionnaires in VR and to significantly reduce development time and cost needed to run pre-, in between- and post-study questionnaires.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416188"}, {"primary_key": "2689314", "vector": [], "sparse_vector": [], "title": "Small-Step Live Programming by Example.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Live programming is a paradigm in which the programming environment continually displays runtime values. Program synthesis is a technique that can generate programs or program snippets from examples. \\deltextThis paper presents a new programming paradigm called Synthesis-Aided Live Programming that combines these two prior ideas in a synergistic way. When using Synthesis-Aided Live Programming, programmers can change the runtime values displayed by the live \\addtextPrevious works that combine the two have taken a holistic approach to the way examples describe the behavior of functions and programs. This paper presents a new programming paradigm called Small-Step Live Programming by Example that lets the user apply Programming by Example locally. When using Small-Step Live Programming by Example, programmers can change the runtime values displayed by the live visualization to generate local program snippets. % Live programming and program % synthesis work perfectly together because the live programming environment % reifies values, which makes it easy for programmers to provide the examples % needed by the synthesizer. We implemented this new paradigm in a tool called \\toolname, and performed a user study on $13$ programmers. Our study finds that Small-Step Live Programming by Example with \\toolname helps users solve harder problems faster, and that for certain types of queries, users prefer it to searching the web. Additionally, we identify the \\usersynthgap, in which users' mental models of the tool do not match its ability, and needs to be taken into account in the design of future synthesis tools.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415869"}, {"primary_key": "2689315", "vector": [], "sparse_vector": [], "title": "DefeXtiles: 3D Printing Quasi-Woven Fabric via Under-Extrusion.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present DefeXtiles, a rapid and low-cost technique to produce tulle-like fabrics on unmodified fused deposition modeling (FDM) printers. The under-extrusion of filament is a common cause of print failure, resulting in objects with periodic gap defects. In this paper, we demonstrate that these defects can be finely controlled to quickly print thinner, more flexible textiles than previous approaches allow. Our approach allows hierarchical control from micrometer structure to decameter form and is compatible with all common 3D printing materials.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415876"}, {"primary_key": "2689316", "vector": [], "sparse_vector": [], "title": "Predicting Visual Importance Across Graphic Design Types.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Peter <PERSON>;<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a Unified Model of Saliency and Importance (UMSI), which learns to predict visual importance in input graphic designs, and saliency in natural images, along with a new dataset and applications. Previous methods for predicting saliency or visual importance are trained individually on specialized datasets, making them limited in application and leading to poor generalization on novel image classes, while requiring a user to know which model to apply to which input. UMSI is a deep learning-based model simultaneously trained on images from different design classes, including posters, infographics, mobile UIs, as well as natural images, and includes an automatic classification module to classify the input. This allows the model to work more effectively without requiring a user to label the input. We also introduce Imp1k, a new dataset of designs annotated with importance information. We demonstrate two new design interfaces that use importance prediction, including a tool for adjusting the relative importance of design elements, and a tool for reflowing designs to new aspect ratios while preserving visual importance.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415825"}, {"primary_key": "2689317", "vector": [], "sparse_vector": [], "title": "ReMap: Lowering the Barrier to Help-Seeking with Multimodal Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "People often seek help online while using complex software. Currently, information search takes users' attention away from the task at hand by creating a separate search task. This paper investigates how multimodal interaction can make in-task help-seeking easier and faster. We introduce ReMap, a multimodal search interface that helps users find video assistance while using desktop and web applications. Users can speak search queries, add application-specific terms deictically (e.g., \"how to erase this\"), and navigate search results via speech, all without taking their hands (or mouse) off their current task. Thirteen participants who used ReMap in the lab found that it helped them stay focused on their task while simultaneously searching for and using learning videos. Users' experiences with ReMap also raised a number of important challenges with implementing system-wide context-aware multimodal assistance.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415592"}, {"primary_key": "2689318", "vector": [], "sparse_vector": [], "title": "Appliancizer: Transforming Web Pages into Electronic Gadgets.", "authors": ["<PERSON>", "Devon J<PERSON>", "<PERSON>"], "summary": "Making electronic gadgets that meet today's consumer standards has become a difficult task. Electronic gadgets are expected to have displays with visually appealing interfaces and, at the same time, be physically and screen-interactive, making the development process of these devices time-consuming and challenging. To address this problem, we have created Appliancizer, an online synthesis tool that can automatically generate sophisticated electronic devices from web pages. Appliancizer takes advantage of the similarities between software and physical interfaces to reduce development steps and allow the rapid development of electronic devices. By matching the interface of hardware components with the interface of graphical HTML elements found on web pages, our tool allows a designer to transform HTML elements from a digital to a tangible interface without changing the application source code. Finally, a modular design enables our tool to automatically combine the circuit design and low-level hardware code of selected hardware components into a complete design. Attendees can interact with our online tool and produce manufacturable PCBs from web pages.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416158"}, {"primary_key": "2689319", "vector": [], "sparse_vector": [], "title": "ARCAR: On-Road Driving in Mixed Reality by Volvo Cars.", "authors": ["Flor<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "ARCAR is a proof-of-concept headset-based mixed reality experience, for use by a driver in full control of a moving vehicle. The functional prototype has been implemented using a high end video pass-through mixed reality headset, and tested on roads (closed to external traffic) at conventional driving speeds. Our implementation embodies acceptable solutions to a number of current challenges that have been in the way of in-car XR applications, and enables a wealth of research. ARCAR is intended to serve as a research platform in the near future, enabling investigations on topics which include safety, design, perceived quality, and consumer applications.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416186"}, {"primary_key": "2689320", "vector": [], "sparse_vector": [], "title": "Acustico: Surface Tap Detection and Localization using Wrist-based Acoustic TDOA Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we present Acustico, a passive acoustic sensing approach that enables tap detection and 2D tap localization on uninstrumented surfaces using a wrist-worn device. Our technique uses a novel application of acoustic time differences of arrival (TDOA) analysis. We adopt a sensor fusion approach by taking both 'surface waves' (i.e., vibrations through surface) and 'sound waves' (i.e., vibrations through air) into analysis to improve sensing resolution. We carefully design a sensor configuration to meet the constraints of a wristband form factor. We built a wristband prototype with four acoustic sensors, two accelerometers and two microphones. Through a 20-participant study, we evaluated the performance of our proposed sensing technique for tap detection and localization. Results show that our system reliably detects taps with an F1-score of 0.9987 across different environmental noises and yields high localization accuracies with root-mean-square-errors of 7.6mm (X-axis) and 4.6mm (Y-axis) across different surfaces and tapping techniques.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415901"}, {"primary_key": "2689321", "vector": [], "sparse_vector": [], "title": "REACH+: Extending the Reachability of Encountered-type Haptics Devices through Dynamic Redirection in VR.", "authors": ["<PERSON>", "Parastoo Abtahi", "<PERSON>"], "summary": "Encountered-type haptic devices (EHDs) face a number of challenges when physically embodying content in a virtual environment, including workspace limits and device latency. To address these issues, we propose REACH+, a framework for dynamic visuo-haptic redirection to improve the perceived performance of EHDs during physical interaction in VR. Using this approach, we estimate the user's arrival time to their intended target and redirect their hand to a point within the EHD's spatio-temporally reachable space. We present an evaluation of this framework implemented with a desktop mobile robot in a 2D target selection task, tested at four robot speeds (20, 25, 30 and 35 cm/s). Results suggest that REACH+ can improve the performance of lower-speed EHDs, increasing their rate of on-time arrival to the point of contact by up to 25% and improving users? self-reported sense of realism.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415870"}, {"primary_key": "2689322", "vector": [], "sparse_vector": [], "title": "FileWeaver: Flexible File Management with Automatic Dependency Tracking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowledge management and sharing involves a variety of specialized but isolated software tools, tied together by the files that these tools use and produce. We interviewed 23 scientists and found that they all had difficulties using the file system to keep track of, re-find and maintain consistency among related but distributed information. We introduce FileWeaver, a system that automatically detects dependencies among files without explicit user action, tracks their history, and lets users interact directly with the graphs representing these dependencies and version history. Changes to a file can trigger recipes, either automatically or under user control, to keep the file consistent with its dependants. Users can merge variants of a file, e.g. different output formats, into a polymorphic file, or morph, and automate the management of these variants. By making dependencies among files explicit and visible, FileWeaver facilitates the automation of workflows by scientists and other users who rely on the file system to manage their data.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415830"}, {"primary_key": "2689323", "vector": [], "sparse_vector": [], "title": "Designing Low-Cost Sports Prosthetics with Advanced 3D Printing Techniques.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Prosthetic limbs are an extension of the human body and should allow for the same function and control that a natural limb provides. Most prosthetics aim to allow an individual to return to a baseline quality of life, but few allow the individual to return to sports. Sport prosthetics can be difficult to produce due to the specificity of the designs and associated costs. Some prosthetics are becoming more accessible with the availability of 3D-printing. In this work, we create a prototype basketball prosthetic hand to enable a more natural shot. This prototype uses 3D printed springs for energy return and 3D printed electronics for integrated strain sensing that can be mapped to haptic feedback for the user. Combining these technologies will lead to a sport prosthetic limb whose technology advancements can be applied to future prosthetic designs.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416145"}, {"primary_key": "2689324", "vector": [], "sparse_vector": [], "title": "Designing Representations for Digital Documents.", "authors": ["<PERSON>"], "summary": "Hundreds of millions of users work with digital documents for their everyday tasks but the user interfaces have not fundamentally changed since they were first designed in the late seventies [10]. We focus on two examples of today's 'extreme users' of documents, legal professionals and scientists. Based on their work practices, we designed two document-centered tools: Textlets supports constraints and consistency within documents; FileWeaver automatically tracks dependencies across documents. I then describe two research directions that emerged from our empirical studies and recent literature: collaboration in documents, e.g. Jupyter notebook and direct manipulation for computational documents such as LaTeX. This dissertation will enhance our understanding of how today's users work with documents; demonstrate novel tools and expand the fundamental theory of interaction.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415805"}, {"primary_key": "2689325", "vector": [], "sparse_vector": [], "title": "View-Dependent Effects for 360° Virtual Reality Video.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "\"View-dependent effects'' have parameters that change with the user's view and are rendered dynamically at runtime. They can be used to simulate physical phenomena such as exposure adaptation, as well as for dramatic purposes such as vignettes. We present a technique for adding view-dependent effects to 360 degree video, by interpolating spatial keyframes across an equirectangular video to control effect parameters during playback. An in-headset authoring tool is used to configure effect parameters and set keyframe positions. We evaluate the utility of view-dependent effects with expert 360 degree filmmakers and the perception of the effects with a general audience. Results show that experts find view-dependent effects desirable for their creative purposes and that these effects can evoke novel experiences in an audience.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415846"}, {"primary_key": "2689326", "vector": [], "sparse_vector": [], "title": "AAR: Augmenting a Wearable Augmented Reality Display with an Actuated Head-Mounted Projector.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Current wearable AR devices create an isolated experience with a limited field of view, vergence-accommodation conflicts, and difficulty communicating the virtual environment to observers. To address these issues and enable new ways to visualize, manipulate, and share virtual content, we introduce Augmented Augmented Reality (AAR) by combining a wearable AR display with a wearable spatial augmented reality projector. To explore this idea, a system is constructed to combine a head-mounted actuated pico projector with a Hololens AR headset. Projector calibration uses a modified structure from motion pipeline to reconstruct the geometric structure of the pan-tilt actuator axes and offsets. A toolkit encapsulates a set of high-level functionality to manage content placement relative to each augmented display and the physical environment. Demonstrations showcase ways to utilize the projected and head-mounted displays together, such as expanding field of view, distributing content across depth surfaces, and enabling bystander collaboration.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415849"}, {"primary_key": "2689327", "vector": [], "sparse_vector": [], "title": "PQual: Automating Web Pages Qualitative Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The increasing complexity of web pages has brought a number of solutions to offer simpler or lighter versions of these pages. The qualitative evaluation of the new versions is commonly carried out relying on user studies. In addition to the associated costs, running user studies might become extremely challenging due to health, travel, and financial constraints. Moreover, user studies are prone to subjectivity, which makes it difficult to compare the results of different studies. In this paper, we propose PQual, a tool that enables the automation of the qualitative evaluation of web pages using computer vision. Results show that PQual computes comparable similarity scores to the scores provided by human raters. In addition, it can effectively evaluate all the functionality of a web page, whereas humans might skip many of the functional elements during the evaluation.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416163"}, {"primary_key": "2689328", "vector": [], "sparse_vector": [], "title": "DataHop: Spatial Data Exploration in Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Virtual reality has recently been adopted for use within the domain of visual analytics because it can provide users with an endless workspace within which they can be actively engaged and use their spatial reasoning skills for data analysis. However, virtual worlds need to utilize layouts and organizational schemes that are meaningful to the user and beneficial for data analysis. This paper presents DataHop, a novel visualization system that enables users to lay out their data analysis steps in a virtual environment. With a Filter, a user can specify the modification they wish to perform on one or more input data panels (i.e., containers of points), along with where output data panels should be placed in the virtual environment. Using this simple tool, highly intricate and useful visualizations may be generated and traversed by harnessing a user's spatial abilities. An exploratory study conducted with six virtual reality users evaluated the usability, affordances, and performance of DataHop for data analysis tasks, and found that spatially mapping one's workflow can be beneficial when exploring multidimensional datasets.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415878"}, {"primary_key": "2689329", "vector": [], "sparse_vector": [], "title": "Designing, Controlling, and Fabricating In-Place Augmented Structures.", "authors": ["<PERSON>"], "summary": "Emerging 3D printing technology has enabled the rapid development of physical objects. However, 3D-printed objects are rarely interactive and adding interactivity to printed objects is inherently challenging. To boost 3D printing for a wider spectrum of applications, I introduce in-place augmented structures, a class of 3D printable parametric structures that can be integrated with physical objects and spaces for augmented behaviors. In my research, I explore how 3D printing can support interaction (e.g., sensing and actuation) by creating novel design techniques and building interactive design tools that enable end-users to design and control desired behaviors. With these techniques and tools, I fabricate the in-place structures with readily available fabrication techniques and demonstrate my approach with a suite of applications across different domains.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415804"}, {"primary_key": "2689330", "vector": [], "sparse_vector": [], "title": "Democratizing the Production of Interactive Hardware.", "authors": ["<PERSON>"], "summary": "The development of new hardware can be split into two phases: prototyping and production. A wide variety of tools and techniques have empowered people to build prototypes during the first phase, but the transition to production is still complex, costly and prone to failure. This means the second phase often requires an up-front commitment to large volume production in order to be viable. I believe that new tools and techniques can democratize hardware production. Imagine \"DevOps for hardware\" - everything from circuit simulation tools to re-usable hardware test jig designs; and from test-driven development for hardware to telepresence for remote factory visits. Supporting low volume production and organic scaling in this way would spur innovation and increase consumer choice. I encourage the UIST community to join me in pursuit of this vision.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3422877"}, {"primary_key": "2689331", "vector": [], "sparse_vector": [], "title": "KnitGIST: A Programming Synthesis Toolkit for Generating Functional Machine-Knitting Textures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic knitting machines are robust, digital fabrication devices that enable rapid and reliable production of attractive, functional objects by combining stitches to produce unique physical properties. However, no existing design tools support optimization for desirable physical and aesthetic knitted properties. We present KnitGIST (Generative Instantiation Synthesis Toolkit for knitting), a program synthesis pipeline and library for generating hand- and machine-knitting patterns by intuitively mapping objectives to tactics for texture design. KnitGIST generates a machine-knittable program in a domain-specific programming language.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415590"}, {"primary_key": "2689332", "vector": [], "sparse_vector": [], "title": "MYND: Unsupervised Evaluation of Novel BCI Control Strategies on Consumer Hardware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Neurophysiological laboratory studies are often constraint to immediate geographical surroundings and access to equipment may be temporally restricted. Limitations of ecological validity, scalability, and generalizability of findings pose a significant challenge for the development of brain-computer interfaces (BCIs), which ultimately need to function in any context, on consumer-grade hardware. We introduce MYND: An open-source framework that couples consumer-grade recording hardware with an easy-to-use application for the unsupervised evaluation of BCI control strategies. Subjects are guided through experiment selection, hardware fitting, recording, and data upload in order to self-administer multi-day studies that include neurophysiological recordings and questionnaires at home. As a use case, thirty subjects evaluated two BCI control strategies \"Positive memories\" and \"Music imagery\" by using a four-channel electroencephalogram (EEG) with MYND. Neural activity in both control strategies could be decoded with an average offline accuracy of 68.5% and 64.0% across all days.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415844"}, {"primary_key": "2689333", "vector": [], "sparse_vector": [], "title": "Bubble Visualization Overlay in Online Communication for Increased Speed Awareness and Better Turn Taking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we explore the use of a real-time speech visualization overlay to help native English speakers (NES) reflect on their speech speed and allow them to understand how English as a Foreign Language speakers (EFLS) perceive their speech. Our visual system generates a sequence of bubbles based on the speaking speed overlaid close to the user's mouth; the faster the speaking speed, the denser the bubble sequence, and vice versa. The results suggest that the presence of the speech visualization helps NES to understand their speech speed, and subsequently, it helps EFLS to feel comfortable speaking during the online group discussion.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416185"}, {"primary_key": "2689334", "vector": [], "sparse_vector": [], "title": "Scrapbook: Screenshot-based Bookmark for Effective Curation of Digital Resources.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Modern users typically open multiple software, websites, and documents for daily tasks. Retrieving previously used digital resources, such as web pages, documents, local files, and software, for knowledge workers is inevitable, but can be time-consuming. People may fail to remember where the resource is. In addition, sometimes, users need to retrieve digital resources across multiple applications to resume a computing task. However, the current methods - bookmarks, file systems, and recent history - for curating such resources are limited in their capacity and dispersed over multiple applications. To address the above problems, we test the idea of curating digital resources by developing Scrapbook, a software that allows users to curate digital resources with screenshots. Scrapbook allows users to take a screenshot of their computer screen and stores metadata of captured applications in the screenshot. Later, users can utilize multimodal (visual and textual) information to retrieve information that they want to recall or to restore the working context of a certain task. In this poster, we acknowledge previous relevant research, introduce ongoing efforts of developing Scrapbook, and share our plan to validate the idea via a user study.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416181"}, {"primary_key": "2689335", "vector": [], "sparse_vector": [], "title": "MonoEye: Multimodal Human Motion Capture System Using A Single Ultra-Wide Fisheye Camera.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present MonoEye, a multimodal human motion capture system using a single RGB camera with an ultra-wide fisheye lens, mounted on the user's chest. Existing optical motion capture systems use multiple cameras, which are synchronized and require camera calibration. These systems also have usability constraints that limit the user's movement and operating space. Since the MonoEye system is based on a wearable single RGB camera, the wearer's 3D body pose can be captured without space and environment limitations. The body pose, captured with our system, is aware of the camera orientation and therefore it is possible to recognize various motions that existing egocentric motion capture systems cannot recognize. Furthermore, the proposed system captures not only the wearer's body motion but also their viewport using the head pose estimation and an ultra-wide image. To implement robust multimodal motion capture, we design three deep neural networks: BodyPoseNet, HeadPoseNet, and CameraPoseNet, that estimate 3D body pose, head pose, and camera pose in real-time, respectively. We train these networks with our new extensive synthetic dataset providing 680K frames of renderings of people with a wide range of body shapes, clothing, actions, backgrounds, and lighting conditions. To demonstrate the interactive potential of the MonoEye system, we present several application examples from common body gestural to context-aware interactions.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415856"}, {"primary_key": "2689336", "vector": [], "sparse_vector": [], "title": "LightTouch: Passive Gadgets for Extending Interactions on Capacitive Touchscreens by Automating Touch Inputs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present LightTouch, a passive gadget to enhance touch interactions on unmodified capacitive touchscreens. It simulates finger operations such as tapping, swiping, or multi-touch gestures using conductive materials and photoresistors embedded inside the objects. The touchscreen emits visible light and the photoresistor senses the level of this light, which changes its resistance value. By controlling the screen brightness, it connects or disconnects the path between the GND and the touchscreen, thus allowing the touch inputs to be controlled. In contrast to conventional physical extensions for touchscreens, our technique does not require continuous finger contact on the conductive part nor the use of batteries. Our technique opens up new possibilities for touch interaction such as for enhancing the trackability of tangibles beyond the simple automation of touch inputs.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416169"}, {"primary_key": "2689337", "vector": [], "sparse_vector": [], "title": "Electrolysis Ion Display on Wet Surfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Itiro <PERSON>"], "summary": "We present a novel method to render color patterns using electrolysis applied onto open wet surfaces. By implementing electrodes within a wet object and electrifying them, electrolysis can occur and generate ions. Using color indicators reacting to such ions, we can create a color-forming display. By applying common techniques, such as a printed circuit board, arbitrary patterns can be displayed by computer control. By reversing the polarity of electrodes and varying the number of ions, it is possible to fade the existing pattern and display a contrasting color. The proposed method can be used to create a color-forming display using various objects more common in everyday life than those used in conventional substantial displays.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416172"}, {"primary_key": "2689338", "vector": [], "sparse_vector": [], "title": "ShARe: Enabling Co-Located Asymmetric Multi-User Interaction for Augmented Reality Head-Mounted Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Head-Mounted Displays (HMDs) are the dominant form of enabling Virtual Reality (VR) and Augmented Reality (AR) for personal use. One of the biggest challenges of HMDs is the exclusion of people in the vicinity, such as friends or family. While recent research on asymmetric interaction for VR HMDs has contributed to solving this problem in the VR domain, AR HMDs come with similar but also different problems, such as conflicting information in visualization through the HMD and projection. In this work, we propose ShARe, a modified AR HMD combined with a projector that can display augmented content onto planar surfaces to include the outside users (non-HMD users). To combat the challenge of conflicting visualization between augmented and projected content, ShARe visually aligns the content presented through the AR HMD with the projected content using an internal calibration procedure and a servo motor. Using marker tracking, non-HMD users are able to interact with the projected content using touch and gestures. To further explore the arising design space, we implemented three types of applications (collaborative game, competitive game, and external visualization). ShARe is a proof-of-concept system that showcases how AR HMDs can facilitate interaction with outside users to combat exclusion and instead foster rich, enjoyable social interactions.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415843"}, {"primary_key": "2689339", "vector": [], "sparse_vector": [], "title": "WIKA: A Projected Augmented Reality Workbench for Interactive Kinetic Art.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gyeongwon Yun", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Iterative artistic exploration, mechanism building, and interaction programming are essential processes of prototyping interactive kinetic art (IKA). However, scattered tools and interwoven workflows across digital and physical worlds make the task difficult. We present WIKA, an integrated environment supporting the whole creation process of IKA in the form of a layered picture frame in a single workspace. A projected AR system with a mobile device efficiently makes an interactive tabletop. The projected information connected with physical components (e.g. sensors and motors) enables the programming and simulation on the workspace. Physical components are applied from the initial phase of prototyping using an AR plate, and this supports the iterative trial-and-error process by bridging the workflow. A user study shows that WIKA enabled non-experts to create diverse IKA with their ideas. A tangible interaction and projected information enable the iterative and rapid creation. The method that integrates the hardware and software in the physical environment can be applied to other prototyping tools that support the creation of interactive and kinetic elements.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415880"}, {"primary_key": "2689340", "vector": [], "sparse_vector": [], "title": "mage: Fluid Moves Between Code and Graphical Work in Computational Notebooks.", "authors": ["<PERSON>", "<PERSON><PERSON> Ren", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We aim to increase the flexibility at which a data worker can choose the right tool for the job, regardless of whether the tool is a code library or an interactive graphical user interface (GUI). To achieve this flexibility, we extend computational notebooks with a new API mage, which supports tools that can represent themselves as both code and GUI as needed. We discuss the design of mage as well as design opportunities in the space of flexible code/GUI tools for data work. To understand tooling needs, we conduct a study with nine professional practitioners and elicit their feedback on mage and potential areas for flexible code/GUI tooling. We then implement six client tools for mage that illustrate the main themes of our study findings. Finally, we discuss open challenges in providing flexible code/GUI interactions for data workers.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415842"}, {"primary_key": "2689341", "vector": [], "sparse_vector": [], "title": "SchemaBoard: Supporting Correct Assembly of Schematic Circuits using Dynamic In-Situ Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Seungwoo Je", "Youngkyung Choi", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Assembling circuits on breadboards using reference designs is a common activity among makers. While tools like Fritzing offer a simplified visualization of how components and wires are connected, such pictorial depictions of circuits are rare in formal educational materials and the vast bulk of online technical documentation. Electronic schematics are more common but are perceived as challenging and confusing by novice makers. To improve access to schematics, we propose SchemaBoard, a system for assisting makers in assembling and inspecting circuits on breadboards from schematic source materials. SchemaBoard uses an LED matrix integrated underneath a working breadboard to visualize via light patterns where and how components should be placed, or to highlight elements of circuit topology such as electrical nets and connected pins. This paper presents a formative study with 16 makers, the SchemaBoard system, and a summative evaluation with an additional 16 users. Results indicate that SchemaBoard is effective in reducing both the time and the number of errors associated with building a circuit based on a reference schematic, and for inspecting the circuit for correctness after its assembly.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415887"}, {"primary_key": "2689342", "vector": [], "sparse_vector": [], "title": "OddEyeCam: A Sensing Technique for Body-Centric Peephole Interaction Using WFoV RGB and NFoV Depth Cameras.", "authors": ["<PERSON><PERSON><PERSON>", "Keun-Woo Park", "<PERSON><PERSON><PERSON>"], "summary": "The space around the body not only expands the interaction space of a mobile device beyond its small screen, but also enables users to utilize their kinesthetic sense. Therefore, body-centric peephole interaction has gained considerable attention. To support its practical implementation, we propose OddEyeCam, which is a vision-based method that tracks the 3D location of a mobile device in an absolute, wide, and continuous manner with respect to the body of a user in both static and mobile environments. OddEyeCam tracks the body of a user using a wide-view RGB camera and obtains precise depth information using a narrow-view depth camera from a smartphone close to the body. We quantitatively evaluated OddEyeCam through an accuracy test and two user studies. The accuracy test showed the average tracking accuracy of OddEyeCam was 4.17 and 4.47cm in 3D space when a participant is standing and walking, respectively. In the frst user study, we implemented various interaction scenarios and observed that OddEyeCam was well received by the participants. In the second user study, we observed that the peephole target acquisition task performed using our system followed Fitts? law. We also analyzed the performance of OddEyeCam using the obtained measurements and observed that the participants completed the tasks with suffcient speed and accuracy.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415889"}, {"primary_key": "2689343", "vector": [], "sparse_vector": [], "title": "Ambre: Augmented Metaphor Based Representation System for Electricity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rise of the Maker Movement and STEM education, toolkit research for beginners in electronics has become popular. Most of them are focused on the efficient creation and debugging of electrical circuits. Therefore, users were implicitly required to understanding of the nature of electricity and some prerequisites knowledge. In this paper, we propose tangible interface to express electrical properties such as voltage and resistance. This system aims to bring a deep interest and understanding of the nature of electricity to the beginners explicitly. In this research, we discuss the interaction between the laws of electricity and another several physical laws, present an example of a prototype implementation, and describe the results of a preliminary user questionnaire.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416171"}, {"primary_key": "2689344", "vector": [], "sparse_vector": [], "title": "Modeling Two Dimensional Touch Pointing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modeling touch pointing is essential to touchscreen interface development and research, as pointing is one of the most basic and common touch actions users perform on touchscreen devices. Finger-Fitts Law [4] revised the conventional Fitts' law into a 1D (one-dimensional) pointing model for finger touch by explicitly accounting for the fat finger ambiguity (absolute error) problem which was unaccounted for in the original <PERSON><PERSON>' law. We generalize Finger-Fitts law to 2D touch pointing by solving two critical problems. First, we extend two of the most successful 2D Fitts law forms to accommodate finger ambiguity. Second, we discovered that using nominal target width and height is a conceptually simple yet effective approach for defining amplitude and directional constraints for 2D touch pointing across different movement directions. The evaluation shows our derived 2D Finger-Fitts law models can be both principled and powerful. Specifically, they outperformed the existing 2D Fitts' laws, as measured by the regression coefficient and model selection information criteria (e.g., Akaike Information Criterion) considering the number of parameters. Finally, 2D Finger-Fitts laws also advance our understanding of touch pointing and thereby serve as the basis for touch interface designs.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415871"}, {"primary_key": "2689345", "vector": [], "sparse_vector": [], "title": "ARcheoBox: Engaging with Historical Artefacts Through Augmented Reality and Tangible Interactions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Museum visitors can often be distanced from directly engaging with historical artefacts due to their fragile nature. ARcheoBox is a novel interface that lets users physically pick up a digitized version of a real artefact and inspect it closely from all angles through a screen. The system combines augmented reality and tangible interactions to remove barriers between visitors and artefacts. We piloted our prototype with eight archaeological experts at a local exhibition centre. The results indicate that this is a promising way to remove barriers between humans and historical artefacts, and that it allows expert users to inspect, understand and interact with these artefacts in a way not previously possible.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416173"}, {"primary_key": "2689346", "vector": [], "sparse_vector": [], "title": "Janus Screen: A Screen with Switchable Projection Surfaces Using Polarizers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the area of ubiquitous computing, there is research that use surfaces of elements in the environment such as windows and walls as screens. It is necessary to install an opaque screen and projectors on both sides of the screen in order to switch projection surfaces, i.e., front, back, or both sides. In this paper, we propose Janus Screen, a novel screen system with polarizers that allow switching of the projection surface to the front, back, or both sides using only projectors on one side. Janus Screen employs projectors equipped with polarizers and a screen with multiple layers. We implement a prototype and confirm that Janus Screen can selectively switch the projection surface. Moreover, we discuss unique characteristics and possible applications of the proposed system.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416144"}, {"primary_key": "2689347", "vector": [], "sparse_vector": [], "title": "Haptic PIVOT: On-Demand Handhelds in VR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present PIVOT, a wrist-worn haptic device that renders virtual objects into the user's hand on demand. Its simple design comprises a single actuated joint that pivots a haptic handle into and out of the user's hand, rendering the haptic sensations of grasping, catching, or throwing an object anywhere in space. Unlike existing hand-held haptic devices and haptic gloves, PIVOT leaves the user's palm free when not in use, allowing users to make unencumbered use of their hand. PIVOT also enables rendering forces acting on the held virtual objects, such as gravity, inertia, or air-drag, by actively driving its motor while the user is firmly holding the handle. When wearing a PIVOT device on both hands, they can add haptic feedback to bimanual interaction, such as lifting larger objects. In our user study, participants (n=12) evaluated the realism of grabbing and releasing objects of different shape and size with mean score 5.19 on a scale from 1 to 7, rated the ability to catch and throw balls in different directions with different velocities (mean=5.5), and verified the ability to render the comparative weight of held objects with 87% accuracy for ~100g increments.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415854"}, {"primary_key": "2689348", "vector": [], "sparse_vector": [], "title": "TransceiVR: Bridging Asymmetrical Communication Between VR Users and External Collaborators.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual Reality (VR) users often need to work with other users, who observe them outside of VR using an external display. Communication between them is difficult; the VR user cannot see the external user's gestures, and the external user cannot see VR scene elements outside of the VR user's view. We carried out formative interviews with experts to understand these asymmetrical interactions and identify their goals and challenges. From this, we identify high-level system design goals to facilitate asymmetrical interactions and a corresponding space of implementation approaches based on the level of programmatic access to a VR application. We present TransceiVR, a system that utilizes VR platform APIs to enable asymmetric communication interfaces for third-party applications without requiring source code access. TransceiVR allows external users to explore the VR scene spatially or temporally, to annotate elements in the VR scene at correct depths, and to discuss via a shared static virtual display. An initial co-located user evaluation with 10 pairs shows that our system makes asymmetric collaborations in VR more effective and successful in terms of task time, error rate, and task load index. An informal evaluation with a remote expert gives additional insight on utility of features for real world tasks.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415827"}, {"primary_key": "2689349", "vector": [], "sparse_vector": [], "title": "Let It Rip! Using Velcro for Acoustic Labeling.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an early stage prototype of an acoustic labeling system using Velcro, a two-sided household adhesive product. We create labels by varying the shape of Velcro pieces to produce distinct sounds when the two sides are separated, and we use an automatic audio classification pipeline to detect and classify small sets of labels. We evaluate our classifier on four sets of three simple Velcro labels, present a demo highlighting potential use cases of these labels, and discuss future applications.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416175"}, {"primary_key": "2689350", "vector": [], "sparse_vector": [], "title": "Swipe&amp;Switch: Text Entry Using Gaze Paths and Context Switching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Swipe-based methods for text entry by gaze allow users to swipe through the letters of a word by gaze, analogous to how they can swipe with a finger on a touchscreen keyboard. Two challenges for these methods are: (1) gaze paths do not possess clear start and end positions, and (2) it is difficult to design text editing features. We introduce Swipe&Switch, a text-entry interface that uses swiping and switching to improve gaze-based interaction. The interface contains three context regions, and detects the start/end of a gesture and emits text editing commands (e.g., word insertion, deletion) when a user switches focus between these regions. A user study showed that Swipe&Switch provides a better user experience and higher text entry rate over a baseline, EyeSwipe.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416193"}, {"primary_key": "2689351", "vector": [], "sparse_vector": [], "title": "IBUKI: Gesture Input Method Based on Breathing.", "authors": ["<PERSON><PERSON><PERSON>", "Takuya Indo"], "summary": "In this paper, we propose a method for gesture input which uses a sensor attached to a mask to detect the user's breathing and trajectory of his or her breath. To prevent the spread of infectious diseases, it is important to keep our hands clean and avoid spreading the virus to our homes. Using a smartphone after touching various objects in public facilities increases the risk of infection. Our research focuses on operating devices such as smartphones using breathing gestures rather than touching the devices directly. The gestures were performed while wearing a commercial mask with a breathing sensor and a 9-DoF IMU attached. The breathing sensor detects inhalation and exhalation patterns, and the 9-DoF IMU estimates the breath direction and movement trajectory by the user's head. The breathing gesture was estimated by combining the information from the two sensors. Through prototyping, we determined that our method was able to estimate the gestures. We also provide an example application of this interface.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416134"}, {"primary_key": "2689352", "vector": [], "sparse_vector": [], "title": "Adapting Usability Heuristics to the Context of Mobile Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Augmented reality (AR) is an emerging technology in mobile app design during recent years. However, usability challenges in these apps are prominent. There are currently no established guidelines for designing and evaluating interactions in AR as there are in traditional user interfaces. In this work, we aimed to examine the usability of current mobile AR applications and interpreting classic usability heuristics in the context of mobile AR. Particularly, we focused on AR home design apps because of their popularity and ability to incorporate important mobile AR interaction schemas. Our findings indicated that it is important for the designers to consider the unfamiliarity of AR technology to the vast users and to take technological limitations into consideration when designing mobile AR apps. Our work serves as a first step for establishing more general heuristics and guidelines for mobile AR.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416167"}, {"primary_key": "2689353", "vector": [], "sparse_vector": [], "title": "Omni: Volumetric Sensing and Actuation of Passive Magnetic Tools for Dynamic Haptic Feedback.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Omni, a self-contained 3D haptic feedback system that is capable of sensing and actuating an untethered, passive tool containing only a small embedded permanent magnet. Omni enriches AR, VR and desktop applications by providing an active haptic experience using a simple apparatus centered around an electromagnetic base. The spatial haptic capabilities of Omni are enabled by a novel gradient-based method to reconstruct the 3D position of the permanent magnet in midair using the measurements from eight off-the-shelf hall sensors that are integrated into the base. Omni's 3 DoF spherical electromagnet simultaneously exerts dynamic and precise radial and tangential forces in a volumetric space around the device. Since our system is fully integrated, contains no moving parts and requires no external tracking, it is easy and affordable to fabricate. We describe Omni's hardware implementation, our 3D reconstruction algorithm, and evaluate the tracking and actuation performance in depth. Finally, we demonstrate its capabilities via a set of interactive usage scenarios.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415589"}, {"primary_key": "2689354", "vector": [], "sparse_vector": [], "title": "Optimal Control for Electromagnetic Haptic Guidance Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce an optimal control method for electromagnetic haptic guidance systems. Our real-time approach assists users in pen-based tasks such as drawing, sketching or designing. The key to our control method is that it guides users, yet does not take away agency. Existing approaches force the stylus to a continuously advancing setpoint on a target trajectory, leading to undesirable behavior such as loss of haptic guidance or unintended snapping. Our control approach, in contrast, gently pulls users towards the target trajectory, allowing them to always easily override the system to adapt their input spontaneously and draw at their own speed. To achieve this flexible guidance, our optimization iteratively predicts the motion of an input device such as a pen, and adjusts the position and strength of an underlying dynamic electromagnetic actuator accordingly. To enable real-time computation, we additionally introduce a novel and fast approximate model of an electromagnet. We demonstrate the applicability of our approach by implementing it on a prototypical hardware platform based on an electromagnet moving on a bi-axial linear stage, as well as a set of applications. Experimental results show that our approach is more accurate and preferred by users compared to open-loop and time-dependent closed-loop approaches.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415593"}, {"primary_key": "2689355", "vector": [], "sparse_vector": [], "title": "Tsugite: Interactive Design and Fabrication of Wood Joints.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Tsugite - an interactive system for designing and fabricating wood joints for frame structures. To design and manually craft such joints is difficult and time consuming. Our system facilitates the creation of custom joints by a modeling interface combined with computer numerical control (CNC) fabrication. The design space is a 3D grid of voxels that enables efficient geometrical analysis and combinatorial search. The interface has two modes: manual editing and gallery. In the manual editing mode, the user edits a joint while receiving real-time graphical feedback and suggestions provided based on performance metrics including slidability, fabricability, and durability with regard to the direction of fiber. In the gallery mode, the user views and selects feasible joints that have been pre-calculated. When a joint design is finalized, it can be manufactured with a 3-axis CNC milling machine using a specialized path planning algorithm that ensures joint assemblability by corner rounding. This system was evaluated via a user study and by designing and fabricating joint samples and functional furniture.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415899"}, {"primary_key": "2689356", "vector": [], "sparse_vector": [], "title": "Newspaper Navigator: Open Faceted Search for 1.5 Million Images.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Despite the marked improvements in image classification and recognition tasks over the past decade, affordances and interfaces for searching images have remained largely unchanged on the web. In this demo, we present Newspaper Navigator, an open faceted search system for 1.5 million historic newspaper photographs. In contrast to standard faceted search, which requires facets to be pre-selected and applied to images, Newspaper Navigator empowers users to specify their own facets in an open-domain fashion during the search process by selecting relevant examples and iteratively training a machine learner. With Newspaper Navigator, users can quickly sort 1.5 million images according to dynamically-specified facets such as \"baseball player'' and \"oval-shaped portrait.'' Newspaper Navigator also drives facet exploration by suggesting related keyword search queries for a user to perform. Our demo walks through examples of searching with Newspaper Navigator and highlights the facet learning and exploration affordances.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416143"}, {"primary_key": "2689357", "vector": [], "sparse_vector": [], "title": "LamiFold: Fabricating Objects with Integrated Mechanisms Using a Laser cutter Lamination Workflow.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present LamiFold, a novel design and fabrication workflow for making functional mechanical objects using a laser cutter. Objects fabricated with LamiFold embed advanced rotary, linear, and chained mechanisms, including linkages that support fine-tuning and locking position. Laser cutting such mechanisms without LamiFold requires designing for and embedding off-the-shelf parts such as springs, bolts, and axles for gears. The key to laser cutting our functional mechanisms is the selective cutting and gluing of stacks of sheet material. Designing mechanisms for this workflow is non-trivial, therefore we contribute a set of mechanical primitives that are compatible with our lamination workflow and can be combined to realize advanced mechanical systems. Our software design environment facilitates the process of inserting and composing our mechanical primitives and realizing functional laser-cut objects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415885"}, {"primary_key": "2689358", "vector": [], "sparse_vector": [], "title": "Focused Live Programming with Loop Seeds.", "authors": ["<PERSON><PERSON>"], "summary": "Live programming is a paradigm in which the programmer can visualize the runtime values of the program each time the program changes. The promise of live programming depends on using test cases to run the program and thereby provide these runtime values. In this paper we show that in some situations test cases are insufficient in a fundamental way, in that there are no test inputs that can drive certain incomplete loops to produce useful data, a problem we call the loop-datavoid problem. The problem stems from the fact that useful data inside the loop might only be produced after the loop has been fully written. To solve this problem, we propose a paradigm called Focused Live Programming with Loop Seeds, in which the programmer provides hypothetical values to start a loop iteration, and then the programming environment focuses the live visualization on this hypothetical loop iteration. We introduce the loop-datavoid problem, present our proposed solution, explain it in detail, and then present the results of a user study.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415834"}, {"primary_key": "2689359", "vector": [], "sparse_vector": [], "title": "Multi-Modal Interactive Task Learning from Demonstrations and Natural Language Instructions.", "authors": ["<PERSON>-<PERSON>"], "summary": "Interactive task learning (ITL) allows end users to 'teach' an intelligent agent new tasks, the corresponding task conditions,and the relevant concepts. This paper presents my research on expanding the applicability, generalizability, robustness, expressiveness, and script sharability of ITL systems usinga multi-modal approach. My research demonstrates that a multi-modal ITL approach that combines programming by demonstration and natural language instructions can empower users without significant programming expertise to extend intelligent agents for their own app-based computing tasks.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415803"}, {"primary_key": "2689360", "vector": [], "sparse_vector": [], "title": "Romeo: A Design Tool for Embedding Transformable Parts in 3D Models to Robotically Augment Default Functionalities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiang &apos;Anthony&apos; Chen"], "summary": "Reconfiguring shapes of objects enables transforming existing passive objects with robotic functionalities, e.g., a transformable coffee cup holder can be attached to a chair's armrest, a piggy bank can reach out an arm to 'steal' coins. Despite the advance in end-user 3D design and fabrication, it remains challenging for non-experts to create such 'transformables' using existing tools due to the requirement of specific engineering knowledge such as mechanisms and robotic design.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415826"}, {"primary_key": "2689361", "vector": [], "sparse_vector": [], "title": "Multi-Modal Repairs of Conversational Breakdowns in Task-Oriented Dialogs.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A major problem in task-oriented conversational agents is the lack of support for the repair of conversational breakdowns. Prior studies have shown that current repair strategies for these kinds of errors are often ineffective due to: (1) the lack of transparency about the state of the system's understanding of the user's utterance; and (2) the system's limited capabilities to understand the user's verbal attempts to repair natural language understanding errors. This paper introduces SOVITE, a new multi-modal speech plus direct manipulation interface that helps users discover, identify the causes of, and recover from conversational breakdowns using the resources of existing mobile app GUIs for grounding. SOVITE displays the system's understanding of user intents using GUI screenshots, allows users to refer to third-party apps and their GUI screens in conversations as inputs for intent disambiguation, and enables users to repair breakdowns using direct manipulation on these screenshots. The results from a remote user study with 10 users using SOVITE in 7 scenarios suggested that SOVITE's approach is usable and effective.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415820"}, {"primary_key": "2689362", "vector": [], "sparse_vector": [], "title": "HapLinkage: Prototyping Haptic Proxies for Virtual Hand Tools Using Linkage Mechanism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Haptic simulation of hand tools like wrenches, pliers, scissors and syringes are beneficial for finely detailed skill training in VR, but designing for numerous hand tools usually requires an expert-level knowledge of specific mechanism and protocol. This paper presents HapLinkage, a prototyping framework based on linkage mechanism, that provides typical motion templates and haptic renderers to facilitate proxy design of virtual hand tools. The mechanical structures can be easily modified, for example, to scale the size, or to change the range of motion by selectively changing linkage lengths. Resistant, stop, release, and restoration force feedback are generated by an actuating module as part of the structure. Additional vibration feedback can be generated with a linear actuator. HapLinkage enables easy and quick prototypting of hand tools for diverse VR scenarios, that embody both of their kinetic and haptic properties. Based on interviews with expert designers, it was confirmed that HapLinkage is expressive in designing haptic proxy of hand tools to enhance VR experiences. It also identified potentials and future development of the framework.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415812"}, {"primary_key": "2689363", "vector": [], "sparse_vector": [], "title": "Polymorphic Blocks: Unifying High-level Specification and Low-level Control for Circuit Board Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mainstream board-level circuit design tools work at the lowest level of design --- schematics and individual components. While novel tools experiment with higher levels of design, abstraction often comes at the expense of the fine-grained control afforded by low-level tools. In this work, we propose a hardware description language (HDL) approach that supports users at multiple levels of abstraction from broad system architecture to subcircuits and component selection. We extend the familiar hierarchical block diagram with polymorphism to include abstract-typed blocks (e.g., generic resistor supertype) and electronics modeling (i.e., currents and voltages). Such an approach brings the advantages of reusability and encapsulation from object-oriented programming, while addressing the unique needs of electronics designers such as physical correctness verification. We discuss the system design, including fundamental abstractions, the block diagram construction HDL, and user interfaces to inspect and fine-tune the design; demonstrate example designs built with our system; and present feedback from intermediate-level engineers who have worked with our system.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415860"}, {"primary_key": "2689364", "vector": [], "sparse_vector": [], "title": "PoseTween: Pose-driven Tween Animation.", "authors": ["<PERSON><PERSON>", "Hongbo Fu", "<PERSON>ew<PERSON><PERSON><PERSON>"], "summary": "Augmenting human action videos with visual effects often requires professional tools and skills. To make this more accessible by novice users, existing attempts have focused on automatically adding visual effects to faces and hands, or let virtual objects strictly track certain body parts, resulting in rigid-looking effects. We present PoseTween, an interactive system that allows novice users to easily add vivid virtual objects with their movement interacting with a moving subject in an input video. Our key idea is to leverage the motion of the subject to create pose-driven tween animations of virtual objects. With our tool, a user only needs to edit the properties of a virtual object with respect to the subject's movement at keyframes, and the object is associated with certain body parts automatically. The properties of the object at intermediate frames are then determined by both the body movement and the interpolated object keyframe properties, producing natural object movements and interactions with the subject. We design a user interface to facilitate editing of keyframes and previewing animation results. Our user study shows that PoseTween significantly requires less editing time and fewer keyframes than using the traditional tween animation in making pose-driven tween animations for novice users.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415822"}, {"primary_key": "2689365", "vector": [], "sparse_vector": [], "title": "GyroSuite: General-Purpose Interactions for Handheld Perspective Corrected Displays.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Handheld Perspective-Corrected Displays (HPCDs) are physical objects that have a notable volume and that display a virtual 3D scene on their entire surface. Being handheld, they create the illusion of holding the scene in a physical container (the display). This has strong benefits for the intuitiveness of 3D interaction: manipulating objects of the virtual scene amounts to physical manipulations of the display. HPCDs have been limited so far to technical demonstrators and experimental tools to assess their merits. However, they show great potential as interactive systems for actual 3D applications. This requires that novel interactions be created to go beyond object manipulation and to offer general-purpose services such as menu command selection and continuous parameter control. Working with a two-handed spherical HPCD, we report on the design and informal evaluations of various interaction techniques for distant object selection, scene scaling, menu interaction and continuous parameter control. In particular, our design leverages the efficient two-handed control of the rotations of the display. We demonstrate how some of these techniques can be assemble in a self-contained anatomy learning application. <PERSON><PERSON> participants used the application in a qualitative user experiment. Most participants used the application effortlessly without any training or explanations.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415893"}, {"primary_key": "2689366", "vector": [], "sparse_vector": [], "title": "E-seed: Shape-Changing Interfaces that Self Drill.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As sensors and interactive devices become ubiquitous and transition outdoors and into the wild, we are met with the challenge of mass deployment and actuation. We present E-seed, a biomimetic platform that consumes little power to deploy, harvests energy from nature to install, and functions autonomously in the field. Each seed can individually self-drill into a substrate by harvesting moisture fluctuations in its ambient environment. As such, E-seed acts as a shape-changing interface to autonomously embed functional devices and interfaces into the soil, with the potential of aerial deployment in hard-to-reach locations. Our system is constructed primarily from wood veneer, making it lightweight, inexpensive, and biodegradable. In this paper, we detail our fabrication process and showcase demos that leverage the E-seed platform as a self-drilling interface. We envision that possible applications include soil sensors, sampling, and environmental monitoring for agriculture and reforestation.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415855"}, {"primary_key": "2689367", "vector": [], "sparse_vector": [], "title": "Iteratively Adapting Avatars using Task-Integrated Optimisation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Virtual Reality allows users to embody avatars that do not match their real bodies. Earlier work has selected changes to the avatar arbitrarily and it therefore remains unclear how to change avatars to improve users' performance. We propose a systematic approach for iteratively adapting the avatar to perform better for a given task based on users' performance. The approach is evaluated in a target selection task, where the forearms of the avatar are scaled to improve performance. A comparison between the optimised and real arm lengths shows a significant reduction in average tapping time by 18.7%, for forearms multiplied in length by 5.6. Additionally, with the adapted avatar, participants moved their real body and arms significantly less, and subjective measures show reduced physical demand and frustration. In a second study, we modify finger lengths for a linear tapping task to achieve a better performing avatar, which demonstrates the generalisability of the approach.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415832"}, {"primary_key": "2689368", "vector": [], "sparse_vector": [], "title": "Taste Display that Reproduces Tastes Measured by a Taste Sensor.", "authors": ["<PERSON><PERSON>"], "summary": "Aiming for the creation and development of taste media, a taste display was developed in this study that can reproduce tastes measured using taste sensors. By performing iontophoresis on five gels, which contain dissolved electrolytes that reproduce the five basic tastes, the quantity of ions that contact the tongue was controlled. A tasteless gel was added, so that the sum of the currents flowing in the six gels could be kept constant, ensuring a uniform amount of stimulation on the tongue. The measured tastes could be successfully reproduced through calibration, in which the indicated taste levels were matched with the taste-sensor measurements. Furthermore, video-editing software was adapted to edit taste information as well as recorded audio and video. In addition, effector and equalizer prototypes were built that can not only reproduce the recorded tastes in their original states but also adjust the tastes to match individual preferences.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415852"}, {"primary_key": "2689369", "vector": [], "sparse_vector": [], "title": "Dynamic Difficulty Adjustment via Fast User Adaptation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic difficulty adjustment (DDA) is a technology that adapts a game's challenge to match the player's skill. It is a key element in game development that provides continuous motivation and immersion to the player. However, conventional DDA methods require tuning in-game parameters to generate the levels for various players. Recent DDA approaches based on deep learning can shorten the time-consuming tuning process, but require sufficient user demo data for adaptation. In this paper, we present a fast user adaptation method that can adjust the difficulty of the game for various players using only a small amount of demo data by applying a meta-learning algorithm. In the video game environment user test (n=9), our proposed DDA method outperformed a typical deep learning-based baseline method.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3418578"}, {"primary_key": "2689370", "vector": [], "sparse_vector": [], "title": "Toward Self-Directed Navigation for People with Visual Impairments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Audio navigation tools have the potential to help visually impaired people (VIPs) navigate independently, but today's audio navigation tools merely guide users to destinations rather than give users the full freedom to explore and navigate environments themselves. To address this problem, we present NavStick, a self-directed audio navigation tool for virtual environments. NavStick repurposes a game controller's thumbstick to allow a user to survey their immediate surroundings by \"scrubbing\" the thumbstick in a circular fashion. We also describe a user study that we are performing to compare NavStick with today's guide-based audio navigation tools.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416156"}, {"primary_key": "2689371", "vector": [], "sparse_vector": [], "title": "Mechanical Shells: Physical Add-ons for Extending and Reconfiguring the Interactivities of Actuated TUIs.", "authors": ["<PERSON>"], "summary": "In this paper, I introduce a concept of mechanical shells, which are physical add-ons that can adaptively augment, extend, and reconfigure the interactivities of self-actuated tangible user interfaces (TUIs). While a variety of research explores actuated and shape-changing interfaces for providing dynamic physical affordance and tangible displays, the concept of mechanical shell intends to overcome the constraint of existing generic actuated TUI hardware thereby enabling greater versatility and expression. This paper overviews the mechanical shell concept, describes project examples, outlines a research framework, and suggests open space for future research.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415801"}, {"primary_key": "2689372", "vector": [], "sparse_vector": [], "title": "HERMITS: Dynamically Reconfiguring the Interactivity of Self-propelled TUIs with Mechanical Shell Add-ons.", "authors": ["<PERSON>", "<PERSON>", "Jordan L. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce HERMITS, a modular interaction architecture for self-propelled Tangible User Interfaces (TUIs) that incorporates physical add-ons, referred to as mechanical shells. The mechanical shell add-ons are intended to be dynamically reconfigured by utilizing the locomotion capability of self-propelled TUIs (e.g. wheeled TUIs, swarm UIs). We developed a proof-of-concept system that demonstrates this novel architecture using two-wheeled robots and a variety of mechanical shell examples. These mechanical shell add-ons are passive physical attatchments that extend the primitive interactivities (e.g. shape, motion and light) of the self-propelled robots.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415831"}, {"primary_key": "2689373", "vector": [], "sparse_vector": [], "title": "Unmasked.", "authors": ["<PERSON><PERSON> <PERSON><PERSON> Nam", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Due to the COVID-19 pandemic, wearing a mask to cover one's mouth is recommended in public spaces to prevent the spread of the virus. Wearing masks hinders our ability to express ourselves, as it is hard to read facial expressions much less lips behind a mask. We present Unmasked, an expressive interface using lip tracking to enhance communication while wearing a mask. Unmasked uses three different methods - either accelerometers, LEDs with a camera tracking, or streaming video - to make speaking while wearing a mask more expressive. Unmasked aims to improve communication during conversations while wearing a mask. This device will help people express themselves while wearing a mask by tracking their mouth movements and displaying their facial expressions on an LCD mounted on the front of the mask. By enhancing communication while wearing a mask, this prototype makes social distancing less disruptive and more bearable, metaphorically closing some of the distance between us.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416137"}, {"primary_key": "2689374", "vector": [], "sparse_vector": [], "title": "FlashAttention: Data-centric Interaction for Data Transformation Using Programming-by-Example.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programming-by-example (PBE) can be a powerful tool for reducing manual work in repetitive data transformation tasks. However, having few examples often leaves ambiguity and may cause undesirable data transformation by the system. This ambiguity can be resolved by allowing the user to directly edit the synthesized programs, but this is difficult for non-programmers. Here, we present a novel approach: data-centric interaction for data transformation, where users resolve the ambiguity in data transformation by examining and modifying the output rather than the program. The key idea is focusing on the given set of data the user wants to transform instead of pursuing the synthesized program's completeness. Our system provides interactive visualization that allows users to efficiently examine and fix the transformed outputs, which is much simpler than understanding and modifying the program itself. Our experiment shows that the number of candidates is much smaller than the number of synthesized programs, which implies the effectiveness of the proposed method.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416187"}, {"primary_key": "2689375", "vector": [], "sparse_vector": [], "title": "poimo: Portable and Inflatable Mobility Devices Customizable for Personal Physical Characteristics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the recent growth in popularity of personal mobility devices (e.g., e-scooters and e-skateboards), they still suffer from limited safety and narrow design form factors, due to their rigid structures. On the other hand, inflatable interfaces studied in human-computer interaction can achieve large volume change by simple inflation/deflation. Inflatable structure also offers soft and safe interaction owing to material compliance and diverse fabrication methods that lead to a wide range of forms and aesthetics. In this paper, we propose poimo, a new family of POrtable and Inflatable MObility devices, which consists of inflatable frames, inflatable wheels, and inflatable steering mechanisms made of a mass-manufacturable material called drop-stitch fabric. First, we defined the basic material properties of a drop-stitch inflatable structure that is sufficiently strong to carry a person while simultaneously allowing soft deformation and deflation for storage and portability. We then implemented an interactive design system that can scan the user's desired riding posture to generate a customized personal mobility device and can add the user's shape and color preferences. To demonstrate the custom-design capability and mobility, we designed several 3D models using our system and built physical samples for two basic templates: a motorcycle and a wheelchair. Finally, we conducted an online user study to examine the usability of the design system and share lessons learned for further improvements in the design and fabrication of poimo.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415894"}, {"primary_key": "2689376", "vector": [], "sparse_vector": [], "title": "HandMorph: a Passive Exoskeleton that Miniaturizes Grasp.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shan-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We engineered an exoskeleton, which we call HandMorph, that approximates the experience of having a smaller grasping range. It uses mechanical links to transmit motion from the wearer's fingers to a smaller hand with five anatomically correct fingers. The result is that HandMorph miniaturizes a wearer's grasping range while transmitting haptic feedback.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415875"}, {"primary_key": "2689377", "vector": [], "sparse_vector": [], "title": "Pop-up Print: Rapidly 3D Printing Mechanically Reversible Objects in the Folded State.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite recent advancements in 3D printing technology, which allows users to rapidly produce 3D objects, printing tall and/or large objects still consumes more time and large amount of support material. In order to address these problems, we propose Pop-up Print, a method to 3D print an object in a compact \"folded\" state and then unfold it after printing to achieve the final artifact. Using this method, we can reduce the object's print height and volume, which directly affects the printing time and support material consumption. In addition, thanks to the reversibility of folding/unfolding, we can reversibly minimize the printed object's volume when unused for storage or transportation, and expand it only in use. To achieve Pop-up Print, we first conducted an experiment using selected printed sample objects with several parameters, in order to determine suitable crease patterns that make both the unfolded and folded state mechanically stable. Based on this result, we developed an interactive design tool to convert 3D models - such as a Stanford Bunny or a <PERSON><PERSON><PERSON>'s cone - to the folded shape. Our design tool allows users to decide non-intuitive parameters that may affect the form's mechanical stability, while maintaining both functional crease patterns and the object's original form factor. Finally, we demonstrate the feasibility of our method through several examples of folded objects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415853"}, {"primary_key": "2689378", "vector": [], "sparse_vector": [], "title": "Wearable Subtitles: Augmenting Spoken Communication with Lightweight Eyewear for All-day Captioning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile solutions can help transform speech and sound into visual representations for people who are deaf or hard-of-hearing (DHH). However, where handheld phones present challenges, head-worn displays (HWDs) could further communication through privately transcribed text, hands-free use, improved mobility, and socially acceptable interactions.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415817"}, {"primary_key": "2689379", "vector": [], "sparse_vector": [], "title": "MechanoBeat: Monitoring Interactions with Everyday Objects using 3D Printed Harmonic Oscillators and Ultra-Wideband Radar.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we present MechanoBeat, a 3D printed mechanical tag that oscillates at a unique frequency upon user interaction. With the help of an ultra-wideband (UWB) radar array, MechanoBeat can unobtrusively monitor interactions with both stationary and mobile objects. MechanoBeat consists of small, scalable, and easy-to-install tags that do not require any batteries, silicon chips, or electronic components. Tags can be produced using commodity desktop 3D printers with cheap materials. We develop an efficient signal processing and deep learning method to locate and identify tags using only the signals reflected from the tag vibrations. MechanoBeat is capable of detecting simultaneous interactions with high accuracy, even in noisy environments. We leverage UWB radar signals' high penetration property to sense interactions behind walls in a non-line-of-sight (NLOS) scenario. A number of applications using MechanoBeat have been explored and the results have been presented in the paper.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415902"}, {"primary_key": "2689380", "vector": [], "sparse_vector": [], "title": "DeepFisheye: Near-Surface Multi-Finger Tracking Technology Using Fisheye Camera.", "authors": ["Keunwoo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Near-surface multi-finger tracking (NMFT) technology expands the input space of touchscreens by enabling novel interactions such as mid-air and finger-aware interactions. We present DeepFisheye, a practical NMFT solution for mobile devices, that utilizes a fisheye camera attached at the bottom of a touchscreen. DeepFisheye acquires the image of an interacting hand positioned above the touchscreen using the camera and employs deep learning to estimate the 3D position of each fingertip. We created two new hand pose datasets comprising fisheye images, on which our network was trained. We evaluated DeepFisheye's performance for three device sizes. DeepFisheye showed average errors with approximate value of 20 mm for fingertip tracking across the different device sizes. Additionally, we created simple rule-based classifiers that estimate the contact finger and hand posture from DeepFisheye's output. The contact finger and hand posture classifiers showed accuracy of approximately 83 and 90%, respectively, across the device sizes.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415818"}, {"primary_key": "2689381", "vector": [], "sparse_vector": [], "title": "Augmenting Physical Buttons with Vibrotactile Feedback for Programmable Feels.", "authors": ["Chaeyong Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Physical buttons provide clear haptic feedback when pressed and released, but their responses are unvarying. Physical buttons can be powered by force actuators to produce unlimited click sensations, but the cost is substantial. An alternative can be augmenting physical buttons with simple and inexpensive vibration actuators. When pushed, an augmented button generates a vibration overlayed on the button's original kinesthetic response, under the general framework of haptic augmented reality. We explore the design space of augmented buttons while changing vibration frequency, amplitude, duration, and envelope. We then visualize the perceptual structure of augmented buttons by estimating a perceptual space for 7 physical buttons and 40 augmented buttons. Their sensations are also assessed against adjectives, and results are mapped into the perceptual space to identify meaningful perceptual dimensions. Our results contribute to understanding the benefits and limitations of programmable vibration-augmented physical buttons with emphasis on their feels.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415837"}, {"primary_key": "2689382", "vector": [], "sparse_vector": [], "title": "Rescribe: Authoring and Automatically Editing Audio Descriptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Audio descriptions make videos accessible to those who cannot see them by describing visual content in audio. Producing audio descriptions is challenging due to the synchronous nature of the audio description that must fit into gaps of other video content. An experienced audio description author will produce content that fits narration necessary to understand, enjoy, or experience the video content into the time available. This can be especially tricky for novices to do well. In this paper, we introduce a tool, Rescribe, that helps authors create and refine their audio descriptions. Using Rescribe, authors first create a draft of all the content they would like to include in the audio description. Rescribe then uses a dynamic programming approach to optimize between the length of the audio description, available automatic shortening approaches, and source track lengthening approaches. Authors can iteratively visualize and refine the audio descriptions produced by Rescribe, working in concert with the tool. We evaluate the effectiveness of Rescribe through interviews with blind and visually impaired audio description users who give feedback on Rescribe results. In addition, we invite novice users to create audio descriptions with Rescribe and another tool, finding that users produce audio descriptions with fewer placement errors using Rescribe.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415864"}, {"primary_key": "2689383", "vector": [], "sparse_vector": [], "title": "Autocomplete Animated Sculpting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Keyframe-based sculpting provides unprecedented freedom to author animated organic models, which can be difficult to create with other methods such as simulation, scripting, and rigging. However, sculpting animated objects can require significant artistic skill and manual labor, even more so than sculpting static 3D shapes or drawing 2D animations, which are already quite challenging.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415884"}, {"primary_key": "2689384", "vector": [], "sparse_vector": [], "title": "Counterweight: Diversifying News Consumption.", "authors": ["<PERSON>", "<PERSON>", "Yugo H<PERSON>", "Xiang &apos;Anthony&apos; Chen"], "summary": "The bias of news articles can strongly affect the opinions and behaviors of readers, especially if they do not consume sets of articles that represent diverse political perspectives. To mitigate media bias and diversify news consumption, we developed Counterweight---a browser extension that presents different perspectives by recommending articles relevant to the current topic. We provide a platform to encourage a more diversified consumption of news and mitigate the negative effects of media bias.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416154"}, {"primary_key": "2689385", "vector": [], "sparse_vector": [], "title": "Sonoflex: Embroidered Speakers Without Permanent Magnets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Sonoflex, a thin-form, embroidered dynamic speaker made without using a permanent magnet. Our design consists of two flat spiral coils, stacked on top of each other, and is based on an isolated, thin (0.15 mm) enameled copper wire. Our approach allows for thin, lightweight, and textile speakers and does not require high voltage as in electrostatic speakers. We show how the speaker can be designed and fabricated and evaluate its acoustic properties as a function of manufacturing parameters (size, turn counts, turn spacing, and substrate materials). The experiment results revealed that we can produce audible sound with a broad frequency range (1.5 kHz - 20 kHz) with the embroidered speaker with a diameter of 50 mm. We conclude the paper by presenting several applications such as audible notifications and near-ultrasound communication.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415888"}, {"primary_key": "2689386", "vector": [], "sparse_vector": [], "title": "Shape Memory Alloy Haptic Compression Garment for Media Augmentation in Virtual Reality Environment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compression-based haptics instrumented into soft, wearable systems have the potential to enhance/augment gaming and entertainment experiences. Here, we present a computer-mediated Shape Memory Alloy (SMA) garment capable of delivering spatially and temporally dynamic compression and thermal stimuli for the purposes of enhancing a movie-watching affective experience based on 4 different emotions: sadness, love, gratitude, fear. Preliminary findings, still pending publication, were obtained from an MTurk survey (n=53) that seek to gather how different emotions can/should be represented physically through a haptic garment. A proposed study in a virtual reality (VR) environment is also introduced as a next step to evaluate how the presence/absence of haptics changes a user's video-watching experience, including immersion, enjoyment, presence, and felt emotional intensity, evaluated through objective (biometrics) and subjective (self-report questionnaire and qualitative interview) measures.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416177"}, {"primary_key": "2689387", "vector": [], "sparse_vector": [], "title": "Learning from the Past - Do Historical Data Help to Improve Progress Indicators in Web Surveys?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As a typical part of the interface of web surveys, progress indicators show the degree of completion for participants. These progress indicators influence the dropout and response behavior as various studies suggest. For this reason, the indicator should be chosen carefully. However, calculating the progress in adaptive surveys with many branches is often difficult. Recently related work has provided algorithms for such surveys based on different prediction strategies and has identified the Root Mean Squared Error as a valuable measure to compare different strategies. However, all previously mentioned strategies have shown poor predictions in some cases. In this paper, we present a new strategy which learns from historical data. A simulation study with 10k randomly generated surveys shows its benefits and its limits. As an example of application, we confirm our results of the simulation by comparing different prediction strategies for two large real-world surveys.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415838"}, {"primary_key": "2689388", "vector": [], "sparse_vector": [], "title": "Personalizing 3D Free-Hand Input for Intuitive Smartphone Augmented Reality Interactions.", "authors": ["<PERSON>"], "summary": "My research goal is to build practical and intuitive 3D free-hand interactions for augmented reality (AR) on smartphones and to explore relevant behavioral-data-driven interaction techniques. In service of that goal, I have developed two preliminary AR systems on the smartphone: a Portal-ble system powered by visual, auditory, and haptic feedback and a set of grabbing accommodations that allows the user's hand to interact with AR contents directly. I have also developed a Portalware system that incorporates a smartphone-wearable interaction scheme to expand visual feedback beyond the smartphone's display and have leveraged this system to improve 3D mid-air sketching. My next steps are to use behavioral data such as device motion, hand motion and user postures to create dynamic and personalized interaction systems that facilitate intuitive AR interaction. Ultimately, these individually-tailored systems unlock new interaction possibilities for the general public and expand the usage scenarios for smartphone AR applications.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415806"}, {"primary_key": "2689389", "vector": [], "sparse_vector": [], "title": "Decoding Surface Touch Typing from Hand-Tracking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a novel text decoding method that enables touch typing on an uninstrumented flat surface. Rather than relying on physical keyboards or capacitive touch, our method takes as input hand motion of the typist, obtained through hand-tracking, and decodes this motion directly into text. We use a temporal convolutional network to represent a motion model that maps the hand motion, represented as a sequence of hand pose features, into text characters. To enable touch typing without the haptic feedback of a physical keyboard, we had to address more erratic typing motion due to drift of the fingers. Thus, we incorporate a language model as a text prior and use beam search to efficiently combine our motion and language models to decode text from erratic or ambiguous hand motion. We collected a dataset of 20 touch typists and evaluated our model on several baselines, including contact-based text decoding and typing on a physical keyboard. Our proposed method is able to leverage continuous hand pose information to decode text more accurately than contact-based methods and an offline study shows parity (73 WPM, 2.38% UER) with typing on a physical keyboard. Our results show that hand-tracking has the potential to enable rapid text entry in mobile environments.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415816"}, {"primary_key": "2689390", "vector": [], "sparse_vector": [], "title": "Cody: An Interactive Machine Learning System for Qualitative Coding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Qualitative coding, the process of assigning labels to text as part of qualitative analysis, is time-consuming and repetitive, especially for large datasets. While available QDAS sometimes allows the semi-automated extension of annotations to unseen data, recent user studies revealed critical issues. In particular, the integration of automated code suggestions into the coding process is not transparent and interactive. In this work, we present <PERSON>, a system for semi-automated qualitative coding that suggests codes based on human-defined coding rules and supervised machine learning (ML). Suggestions and rules can be revised iteratively by users in a lean interface that provides explanations for code suggestions. In a preliminary evaluation, 42% of all documents could be coded automatically based on code rules. Cody is the first coding system to allow users to define query-style code rules in combination with supervised ML. Thereby, users can extend manual annotations to unseen data to improve coding speed and quality.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416195"}, {"primary_key": "2689391", "vector": [], "sparse_vector": [], "title": "Nimble: Mobile Interface for a Visual Question Answering Augmented by Gestures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Virtual Assistants are becoming increasingly popular. However voice-only systems providing limited functionality and minimal variability are often unusable because the exposed behavior is either fully deterministic or essentially ambiguous for supporting human-like dialogues. This paper introduces a Nimble, solution that allows users to ask short questions and to get answers about objects selected from a scene by natural pointing gestures. With a modified Visual Question Answering model we have shown how the integration of gestures to the attention mechanism can reduce questions? ambiguity while sustaining the same accuracy level of the system. We performed this by modifying the model's attention scores using gestures fused with linguistic information.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416153"}, {"primary_key": "2689392", "vector": [], "sparse_vector": [], "title": "Tilt-Responsive Techniques for Digital Drawing Boards.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Drawing boards offer a self-stable work surface that is continuously adjustable. On digital displays, such as the Microsoft Surface Studio, these properties open up a class of techniques that sense and respond to tilt adjustments. Each display posture-whether angled high, low, or somewhere in-between-affords some activities, but not others. Because what is appropriate also depends on the application and task, we explore a range of app-specific transitions between reading vs. writing (annotation), public vs. personal, shared person-space vs. task-space, and other nuances of input and feedback, contingent on display angle. Continuous responses provide interactive transitions tailored to each use-case. We show how a variety of knowledge work scenarios can use sensed display adjustments to drive context-appropriate transitions, as well as technical software details of how to best realize these concepts. A preliminary remote user study suggests that techniques must balance effort required to adjust tilt, versus the potential benefits of a sensed transition.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415861"}, {"primary_key": "2689393", "vector": [], "sparse_vector": [], "title": "Portable Laser Cutting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Laser-cut 3D models found in repositories tend to be basic and trivial-models build over long periods of time and by multiple designers are few/non existent. I argue that this is caused by a lack of an exchange format that would allow continuing the work. At first glance, it may seem like such a format already exist, as laser cut models are already widely shared in the form of 2D cutting plans. However, such files are susceptible to variations in cutter properties (aka kerf) and do not allow modifying the model in any meaningful way (no adjustment of material thickness, no parametric changes, etc.). My first take on the challenge is to see how far we can get by still building on the de-facto standard, i.e., 2D cutting plans. springFit [7] and kerf-canceling mechanisms [6] tackle the challenge by rewriting 2D cutting plans, replacing non-portable elements with portable ones (shown in Figure 1). However, this comes at a cost of extra incisions, reducing the structural integrity of models and impacting aesthetic qualities and rare mechanisms or joints may go undetected. I thus take a more radical approach, which is to move on to a 3D exchange format (kyub [1]). This eliminates these challenges as it guarantees portability by gener-ating a new machine-specific 2D file for the local machine. Instead, it raises the question of compatibility: Files already exist in 2D-how to get them into 3D? I demonstrate a software tool that automatically reconstructs the 3D geometry of the model encoded in a 2D cutting plan, allows modifying it using a 3D editor, and re-encodes it to a 2D cutting plan. I demonstrate how this approach allows me to make a much wider range of modifications, including scaling, changing material thickness, and even remixing mod-els. The transition from sharing machine-oriented 2D cutting files, to 3D files, enables users worldwide to collaborate, share, and reuse. And thus, to move on from users starting thousands of trivial models from scratch to collaborating on big complex projects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415802"}, {"primary_key": "2689394", "vector": [], "sparse_vector": [], "title": "Kerf-Canceling Mechanisms: Making Laser-Cut Mechanisms Operate across Different Laser Cutters.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Ingo Apel", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Getting laser-cut mechanisms, such as those in micro-scopes, robots, vehicles, etc., to work, requires all their components to be dimensioned precisely. This precision, however, tends to be lost when fabricating on a differ-ent laser cutter, as it is likely to remove more or less mate-rial (aka 'kerf'). We address this with what we call kerf-canceling mechanisms. Kerf-canceling mechanisms replace laser-cut bearings, sliders, gear pairs, etc. Unlike their tradi-tional counterparts, however, they keep working when manufactured on a different laser cutter and/or with different kerf. Kerf-canceling mechanisms achieve this by adding an additional wedge element per mechanism. We have created a software tool KerfCanceler that locates traditional mecha-nisms in cutting plans and replaces them with their kerf-canceling counterparts. We evaluated our tool by converting 17 models found online to kerf-invariant models; we evaluated kerf-canceling bearings by testing with kerf values ranging from 0mm and 0.5mm and find that they perform reliably independent of this kerf.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415895"}, {"primary_key": "2689395", "vector": [], "sparse_vector": [], "title": "ElaStick: A Handheld Variable Stiffness Display for Rendering Dynamic Haptic Response of Flexible Object.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Haptic controllers have an important role in providing rich and immersive Virtual Reality (VR) experiences. While previous works have succeeded in creating handheld devices that simulate dynamic properties of rigid objects, such as weight, shape, and movement, recreating the behavior of flexible objects with different stiffness using ungrounded controllers remains an open challenge. In this paper we present ElaStick, a variable-stiffness controller that simulates the dynamic response resulting from shaking or swinging flexible virtual objects. This is achieved by dynamically changing the stiffness of four custom elastic tendons along a joint that effectively increase and reduce the overall stiffness of a perceived object in 2-DoF. We show that with the proposed mechanism, we can render stiffness with high precision and granularity in a continuous range between 10.8 and 71.5Nmm/degree. We estimate the threshold of the human perception of stiffness with a just-noticeable difference (JND) study and investigate the levels of immersion, realism and enjoyment using a VR application.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415862"}, {"primary_key": "2689396", "vector": [], "sparse_vector": [], "title": "Geno: A Developer Tool for Authoring Multimodal Interaction on Existing Web Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "Xiang &apos;Anthony&apos; Chen"], "summary": "Supporting voice commands in applications presents significant benefits to users. However, adding such support to existing GUI-based web apps is effort-consuming with a high learning barrier, as shown in our formative study, due to the lack of unified support for creating multi-modal interfaces. We develop Geno---a developer tool for adding the voice input modality to existing web apps without requiring significate NLP expertise. Geno provides a unified workflow for developers to specify functionalities to support by voice (intents), create language models for detecting intents and the relevant information (parameters) from user utterances, and fulfill the intents by either programmatically invoking the corresponding functions or replaying GUI actions on the web app. Geno further supports references to GUI context in voice commands (e.g., \"add this to the playlist\"). In a study, developers with little NLP expertise were able to add the multi-modal support for two existing web apps using Geno.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415848"}, {"primary_key": "2689397", "vector": [], "sparse_vector": [], "title": "Interaction Interferences: Implications of Last-Instant System State Changes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study interaction interferences, situations where an unexpected change occurs in an interface immediately before the user performs an action, causing the corresponding input to be misinterpreted by the system. For example, a user tries to select an item in a list, but the list is automatically updated immediately before the click, causing the wrong item to be selected. First, we formally define interaction interferences and discuss their causes from behavioral and system-design perspectives. Then, we report the results of a survey examining users' perceptions of the frequency, frustration, and severity of interaction interferences. We also report a controlled experiment, based on state-of-the-art experimental protocols from neuroscience, that explores the minimum time interval, before clicking, below which participants could not refrain from completing their action. Finally, we discuss our findings and their implications for system design, paving the way for future work.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415883"}, {"primary_key": "2689398", "vector": [], "sparse_vector": [], "title": "Authr: A Task Authoring Environment for Human-Robot Teams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Collaborative robots promise to transform work across many industries and promote human-robot teaming as a novel paradigm. However, realizing this promise requires the understanding of how existing tasks, developed for and performed by humans, can be effectively translated into tasks that robots can singularly or human-robot teams can collaboratively perform. In the interest of developing tools that facilitate this process we present Authr, an end-to-end task authoring environment that assists engineers at manufacturing facilities in translating existing manual tasks into plans applicable for human-robot teams and simulates these plans as they would be performed by the human and robot. We evaluated Authr with two user studies, which demonstrate the usability and effectiveness of Authr as an interface and the benefits of assistive task allocation methods for designing complex tasks for human-robot teams. We discuss the implications of these findings for the design of software tools for authoring human-robot collaborative plans.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415872"}, {"primary_key": "2689399", "vector": [], "sparse_vector": [], "title": "Sneak Pique: Exploring Autocompletion as a Data Discovery Scaffold for Supporting Visual Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "Enamul <PERSON>", "<PERSON><PERSON>", "Angel <PERSON>"], "summary": "Natural language interaction has evolved as a useful modality to help users explore and interact with their data during visual analysis. Little work has been done to explore how autocompletion can help with data discovery while helping users formulate analytical questions. We developed a system called \\system as a design probe to better understand the usefulness of autocompletion for visual analysis. We ran three Mechanical Turk studies to evaluate user preferences for various text- and visualization widget-based autocompletion design variants for helping with partial search queries. Our findings indicate that users found data previews to be useful in the suggestions. Widgets were preferred for previewing temporal, geospatial, and numerical data while text autocompletion was preferred for categorical and hierarchical data. We conducted an exploratory analysis of our system implementing this specific subset of preferred autocompletion variants. Our insights regarding the efficacy of these autocompletion suggestions can inform the future design of natural language interfaces supporting visual analysis.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415813"}, {"primary_key": "2689400", "vector": [], "sparse_vector": [], "title": "Tangible Web Layout Design for Blind and Visually Impaired People: An Initial Investigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this poster, we explore the potential of using a tangible user interface (TUI) to enable blind and visually impaired (BVI) developers to design web layouts without assistance. We conducted a semi-structured interview and a co-design session with a blind participant to elicit insights that guided the design of a TUI prototype named <PERSON>rs<PERSON>. Our initial prototype contains 3D printed tactile beads that represent HTML elements, and a 3D printed base that represents the web page layout. BVI users can add an HTML element to the layout by placing tactile beads on top of the base. The base senses the type and location of the beads and renders the HTML element in the corresponding location on the client browser. The poster concludes with a discussion and enumerates future work.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416178"}, {"primary_key": "2689401", "vector": [], "sparse_vector": [], "title": "FS-Pad: Video Game Interactions Using Force Feedback Gamepad.", "authors": ["Young<PERSON>", "Keun-Woo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Force feedback has not been fully explored in modern gaming environments where a gamepad is the main interface. We developed various game interaction scenarios where force feedback through the thumbstick of the gamepad can be effective, and categorized them into five themes. We built a haptic device and control system that can support all presented interactions. The resulting device, FS-Pad, has sufficient fidelity to be used as a haptic game interaction design tool. To verify the presented interactions and effectiveness of the FS-Pad, we conducted a user study with game players, developers, and designers. The subjects used an FS-Pad while playing a demo game and were then interviewed. Their feedback revealed the actual needs for the presented interactions as well as insight into the potential design of game interactions when applying FS-Pad.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415850"}, {"primary_key": "2689402", "vector": [], "sparse_vector": [], "title": "Design Adjectives: A Framework for Interactive Model-Guided Exploration of Parameterized Design Spaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many digital design tasks require a user to set a large number of parameters. Gallery-based interfaces provide a way to quickly evaluate examples and explore the space of potential designs, but require systems to predict which designs from a high-dimensional space are the right ones to present to the user. In this paper we present the design adjectives framework for building parameterized design tools in high dimensional design spaces. The framework allows users to create and edit design adjectives, machine-learned models of user intent, to guide exploration through high-dimensional design spaces. We provide a domain-agnostic implementation of the design adjectives framework based on Gaussian process regression, which is able to rapidly learn user intent from only a few examples. Learning and sampling of the design adjective occurs at interactive rates, making the system suitable for iterative design workflows. We demonstrate use of the design adjectives framework to create design tools for three domains: materials, fonts, and particle systems. We evaluate these tools in a user study showing that participants were able to easily explore the design space and find designs that they liked, and in professional case studies that demonstrate the framework's ability to support professional design concepting workflows.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415866"}, {"primary_key": "2689403", "vector": [], "sparse_vector": [], "title": "The Augmented Conversation and the Amplified World.", "authors": ["<PERSON>"], "summary": "Human communication mediated by computers and Augmented Reality devices will enable us to dynamically express, share and explore new ideas with each other via live simulations as easily as we talk about the weather. This collaboration provides a \"shared truth\" - what you see is exactly what I see, I see you perform an action as you do it, and we both see exactly the same dynamic transformation of this shared information space. When you express an idea, the computer, a full participant in this conversation, instantly makes it real for both of us enabling us to critique and negotiate the meaning of it. This shared virtual world will be as live, dynamic, pervasive, and visceral as the physical.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3422878"}, {"primary_key": "2689404", "vector": [], "sparse_vector": [], "title": "Towards Multi-Wheel Input Device for Non-Visual Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While sighted users leverage both keyboard and mouse input devices to interact with desktops, non-visual users, i.e., users who are blind, cannot use a mouse as it only provides feedback through a visual cursor. As a result, these users rely on keyboard-only interaction, which is often cumbersome, inefficient, and error-prone. Prior work has shown that using a small, rotary input device benefits blind users significantly, as it simulates mouse-like operations. In this paper, we extend this prior work by proposing Wheeler, a multi-wheel based input device that provides simultaneous access to UI elements at three different hierarchies, to facilitate rapid navigation and mouse-like interaction. We designed <PERSON> from scratch in multiple iterations and assembled it using 3D printed models and commercially available electronics. A preliminary user study with six blind-folded sighted users revealed its potential to become an essential input device for blind users, as well as a training and learning tool.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416168"}, {"primary_key": "2689405", "vector": [], "sparse_vector": [], "title": "PViMat: A Self-Powered Portable and Rollable Large Area Gestural Interface Using Indoor Light.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present PViMat, a portable photovoltaic mat that can recognise six unique useful dynamic hover gestures up to 30 cm above the surface over a large area with more than 97% accuracy. We utilised an off-the-shelf portable and rollable outdoor solar tape and employed it to harvest indoor natural and artificial light energy to trickle charge a LiPo battery for self-powering. We demonstrate a low-power operation technique of continuous charging with DC photocurrent and simultaneous event-driven gesture recognition with AC photocurrent. The PViMat prototype harvests 30 mW in a general livingroom light level of 300 lux and consumes 0.8 mW per gesture. Lastly, we propose applications of PViMat with its large-area, flexible and rollable form-factors and the gesture set.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416192"}, {"primary_key": "2689406", "vector": [], "sparse_vector": [], "title": "bARefoot: Generating Virtual Materials using Motion Coupled Vibration in Shoes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many features of materials can be experienced through tactile cues, even using one's feet. For example, one can easily distinguish between moss and stone without looking at the ground. However, this type of material experience is largely not supported in AR and VR applications. We present bARefoot, a prototype shoe providing tactile impulses tightly coupled to motor actions. This enables generating virtual material experiences such as compliance, elasticity, or friction. To explore the parameter space of such sensorimotor coupled vibrations, we present a design tool enabling rapid design of virtual materials. We report initial explorations to increase understanding of how parameters can be optimized for generating compliance, and to examine the effect of dynamic parameters on material experiences. Finally, we present a series of use cases that demonstrate the potential of bARefoot for VR and AR.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415828"}, {"primary_key": "2689407", "vector": [], "sparse_vector": [], "title": "WireTruss: A Fast-Modifiable Prototyping Method Through 3D Printing.", "authors": ["<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Digital fabrication allows users to produce physical objects from digital models. However, conventional fabrication processes are mostly irreversible: once an object is fabricated, it is detached from its original virtual model and cannot be physically changed. In this work, we propose WireTruss, a novel approach for rapid prototyping truss mesh structures, which can be rapidly fabricated, easily assembled and manually modified to support an intuitive design iteration. We developed a parametric design tool that first simplifies the object into a truss graph composed of multi-way joints, and then calculates the route of the wire which is inherently a Euler path. Furthermore, WireTruss can be demonstrated its practical usability through a series of application cases.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416196"}, {"primary_key": "2689408", "vector": [], "sparse_vector": [], "title": "FabricFit: Transforming Form-Fitting Fabrics.", "authors": ["<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Textiles encompass a wide variety and rich characteristics that ranges from soft to tough, foldable to rigid, cuttable to stiff, elastic to plastic. On the other hand, 3D printing thermoplastic (e.g., PLA) materials exhibit controlled deformation at a certain transition temperature. In this paper, we present a method of fabricating form-fitting composite textiles by printing PLA on different fabric substrates, designing different structures and transforming the fabric-PLA composite to form different textures.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416198"}, {"primary_key": "2689409", "vector": [], "sparse_vector": [], "title": "RealitySketch: Embedding Responsive Graphics and Visualizations in AR through Dynamic Sketching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present RealitySketch, an augmented reality interface for sketching interactive graphics and visualizations. In recent years, an increasing number of AR sketching tools enable users to draw and embed sketches in the real world. However, with the current tools, sketched contents are inherently static, floating in mid air without responding to the real world. This paper introduces a new way to embed dynamic and responsive graphics in the real world. In RealitySketch, the user draws graphical elements on a mobile AR screen and binds them with physical objects in real-time and improvisational ways, so that the sketched elements dynamically move with the corresponding physical motion. The user can also quickly visualize and analyze real-world phenomena through responsive graph plots or interactive visualizations. This paper contributes to a set of interaction techniques that enable capturing, parameterizing, and visualizing real-world motion without pre-defined programs and configurations. Finally, we demonstrate our tool with several application scenarios, including physics education, sports training, and in-situ tangible interfaces.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415892"}, {"primary_key": "2689410", "vector": [], "sparse_vector": [], "title": "RealitySketch: Embedding Responsive Graphics and Visualizations in AR with Dynamic Sketching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present RealitySketch, an augmented reality interface for sketching interactive graphics and visualizations. In recent years, an increasing number of AR sketching tools enable users to draw and embed sketches in the real world. However, with the current tools, sketched contents are inherently static, floating in mid air without responding to the real world. This paper introduces a new way to embed dynamic and responsive graphics in the real world. In RealitySketch, the user draws graphical elements on a mobile AR screen and binds them with physical objects in real-time and improvisational ways, so that the sketched elements dynamically move with the corresponding physical motion. The user can also quickly visualize and analyze real-world phenomena through responsive graph plots or interactive visualizations. This paper contributes to a set of interaction techniques that enable capturing, parameterizing, and visualizing real-world motion without pre-defined programs and configurations. Finally, we demonstrate our tool with several application scenarios, including physics education, sports training, and in-situ tangible interfaces.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416155"}, {"primary_key": "2689411", "vector": [], "sparse_vector": [], "title": "Haptic Reproduction by Pneumatic Control Method Based on Load-Displacement Profile.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is known that the pressure and contact area change contribute to hard and soft perception as a cutaneous sensation. In this study, we propose a novel method of haptic presentation based on the profiles of physical objects by using the load-displacement measurement. We fabricated a pneumatic haptic system with an elastic membrane that enables controlled pressure stimuli. We verified that the proposed method is capable of reproducing various profiles by comparing with physical objects. As a result, we found that our system could well reproduce the load-displacement profiles of a sponge and a button specimen.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416138"}, {"primary_key": "2689412", "vector": [], "sparse_vector": [], "title": "Transmissive LED Touch Display for Engineered Marble.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a thin display panel system for converting an existing kitchen or vanity tabletop with an engineered marble into a smart touch display. The display system, which consists of an LED matrix panel including IR-LEDs and photodiodes, will be placed on the rear side of a tabletop of a kitchen or washbasins. The normal LED light is used for a visual display transmitted with a thick opaque resin, and the infrared light is used for detecting touch areas and objects using the same resin panel. This display panel can be used for various applications and can be touched with wet and/or dirty hands. In this paper, we describe the system overview, the optical characteristics of blurred display via an engineered marble plate, and an touch-sensing experiment. We also provide some applications of the smart touch display.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416162"}, {"primary_key": "2689413", "vector": [], "sparse_vector": [], "title": "TelemetRing: A Batteryless and Wireless Ring-shaped Keyboard using Passive Inductive Telemetry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "TelemetRing is a batteryless and wireless ring-shaped keyboard that supports command and text entry in daily lives by detecting finger typing on various surfaces. The proposed inductive telemetry approach eliminates bulky batteries or capacitors from the ring part. Each ring consists of a sensor coil (the ring part itself), 1-DoF piezoelectric accelerometer, and varactor diode; moreover, it has different resonant frequencies. Typing shocks slightly shift the resonant frequency, and these are detected by a wrist-mounted readout coil. 5-bit chord keyboard is realized by attaching five sensor rings on five fingers. Our evaluation shows that the prototype achieved the tiny (6 g, 3.5 cm^3) ring sensor and 89.7% of typing detection ratio.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415873"}, {"primary_key": "2689414", "vector": [], "sparse_vector": [], "title": "Programmable Filament: Printed Filaments for Multi-material 3D Printing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "From full-color objects to functional capacitive artifacts, 3D printing multi-materials became essential to broaden the application areas of digital fabrication. We present Programmable Filament, a novel technique that enables multi-material printing using a commodity FDM 3D printer, requiring no hardware upgrades. Our technique builds upon an existing printing technique in which multiple filament segments are printed and spliced into a single threaded filament. We propose an end-to-end pipeline for 3D printing an object in multi-materials, with an introduction of the design systems for end-users. Optimized for low-cost, single-nozzle FDM 3D printers, the system is built upon our computational analysis and experiments to enhance its validity over various printers and materials to design and produce a programmable filament. Finally, we discuss application examples and speculate the future with its potential, such as custom filament manufacturing on-demand.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415863"}, {"primary_key": "2689415", "vector": [], "sparse_vector": [], "title": "GrabAR: Occlusion-aware Grabbing Virtual Objects in AR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Chi-Wing Fu", "<PERSON>-<PERSON>"], "summary": "Existing augmented reality (AR) applications often ignore the occlusion between real hands and virtual objects when incorporating virtual objects in user's views. The challenges come from the lack of accurate depth and mismatch between real and virtual depth. This paper presents GrabAR1, a new approach that directly predicts the real-and-virtual occlusion and bypasses the depth acquisition and inference. Our goal is to enhance AR applications with interactions between hand (real) and grabbable objects (virtual). With paired images of hand and object as inputs, we formulate a compact deep neural network that learns to generate the occlusion mask. To train the network, we compile a large dataset, including synthetic data and real data. We then embed the trained network in a prototyping AR system to support real-time grabbing of virtual objects. Further, we demonstrate the performance of our method on various virtual objects, compare our method with others through two user studies, and showcase a rich variety of interaction scenarios, in which we can use bare hand to grab virtual objects and directly manipulate them.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415835"}, {"primary_key": "2689416", "vector": [], "sparse_vector": [], "title": "Towards Enabling Eye Contact and Perspective Control in Video Conference.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Eye contact is an important part of in-person communication. However, in modern remote communication - video conference calls, non-verbal cues through eye contact are lost. This is because it is not possible to make eye contact in a video call; to make it look like a person is making an eye contact, the user has to look at the camera directly, but then this means that the user is not looking at the visual of the interlocutor on the screen. In our research, we aim to build a hardware and software system that helps the users to make eye contact in video conference and change their perspective, placing a moving camera behind a semi-transparent screen. The system keeps the position of camera right behind the remote user's eye position on screen tracked by a computer vision algorithm, so users will be able to make eye contact during the video call. We believe the system will lead to better user experience in remote conference calls. In this work, we share our motivation, design choices, the current progress, and a plan to evaluate the system.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416197"}, {"primary_key": "2689417", "vector": [], "sparse_vector": [], "title": "Brain Relevance Feedback for Interactive Image Generation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Brain-computer interfaces (BCIs) are increasingly used to perform simple operations such as a moving a cursor, but have remained of limited use for more complex tasks. In our new approach to BCI, we use brain relevance feedback to control a generative adversarial network (GAN). We obtained EEG data from 31 participants who viewed face images while concentrating on particular facial features. Following, an EEG relevance classifier was trained and propagated as feedback on the latent image representation provided by the GAN. Estimates for individual vectors matching the relevant criteria were iteratively updated to optimize an image generation process towards mental targets. A double-blind evaluation showed high performance (86.26% accuracy) against random feedback (18.71%), and not significantly lower than explicit feedback (93.30%). Furthermore, we show the feasibility of the method with simultaneous task targets demonstrating BCI operation beyond individual task constraints. Thus, brain relevance feedback can validly control a generative model, overcoming a critical limitation of current BCI approaches.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415821"}, {"primary_key": "2689418", "vector": [], "sparse_vector": [], "title": "HaPouch: Soft and Wearable Haptic Display Devices using Liquid-to-gas Phase Change Actuator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conventional pneumatic haptic displays need a complex and weight system because air is fed through a tube from an air compressor. To address this problem, we propose a haptic display that uses a liquid-to-gas phase change actuator and Peltier device. The actuator is composed of a thin plastic pouch containing a low boiling point liquid. The temperature of the liquid is controlled by a Peltier device in close contact with an actuator. This approach allows soft actuators to be inflated without the use of a compressor and tubes, allowing us to realize a small and light haptic display. We implemented the haptic display that has a pouch of 10 mm square and evaluated the characteristics of it. We confirmed that the maximum output force reached almost 1.5 N, which is a similar level to off-the-shelf wearable haptic display.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416183"}, {"primary_key": "2689419", "vector": [], "sparse_vector": [], "title": "Papercode: Generating Paper-Based User Interfaces for Code Review, Annotation, and Teaching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Paper can be a powerful and flexible user interface that lets programmers read through large amounts of code. Using off-the-shelf equipment, how can we generate a paper-based UI that supports code review, annotation, and teaching? To address this question, we ran formative studies and developed Papercode, a system that formats source code for printing on standard paper. Users can interact with that code on paper, make freehand annotations, then transfer annotations back to the computer by taking photos with a normal phone camera. Papercode optimizes source code for on-paper readability with tunable heuristics such as code-aware line wraps and page breaks, quick references to function and global definitions, moving comments and short function calls into margins, and topologically sorting functions in dependency order.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416191"}, {"primary_key": "2689420", "vector": [], "sparse_vector": [], "title": "Dual Phone AR: Using a Second Phone as a Controller for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile Augmented Reality applications have become increasingly popular, however the possible interactions with AR content are largely limited to on-screen gestures and spatial movement. There has been a renewed interest in designing interaction methods for mobile AR that go beyond the screen. Mobile phones present a rich range of input, output, and tracking capabilities, and have been used as controllers for Virtual Reality and head-mounted Augmented Reality applications. In this project, we explore the use of a second phone as a controller for Mobile AR. We developed ARTWO, an application that showcases Handheld Dual Phone AR through a series of small demos in which a second phone can be used to perform basic tasks such as pointing, selecting, and drawing, in the context of real use cases. We believe that the Dual Phone AR approach can help address many of the issues faced when using conventional mobile AR interactions, and also serves as a stepping stone to the general use of phones with head-mounted AR systems in the near future.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416139"}, {"primary_key": "2689421", "vector": [], "sparse_vector": [], "title": "FLAVA: Find, Localize, Adjust and Verify to Annotate LiDAR-based Point Clouds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dahua Lin"], "summary": "Recent years have witnessed the rapid progress of perception algorithms on top of LiDAR, a widely adopted sensor for autonomous driving systems. These LiDAR-based solutions are typically data hungry, requiring a large amount of data to be labeled for training and evaluation. However, annotating this kind of data is very challenging due to the sparsity and irregularity of point clouds and more complex interaction involved in this procedure. To tackle this problem, we propose FLAVA, a systematic approach to minimizing human interaction in the annotation process. Specifically, we divide the annotation pipeline into four parts: find, localize, adjust and verify. In addition, we carefully design the UI for different stages of the annotation procedure, thus keeping the annotators to focus on the aspects that are most important to each stage. Furthermore, our system also greatly reduces the amount of interaction by introducing a lightweight yet effective mechanism to propagate the annotation results. Experimental results show that our method can remarkably accelerate the procedure and improve the annotation quality.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416176"}, {"primary_key": "2689422", "vector": [], "sparse_vector": [], "title": "CAPturAR: An Augmented Reality Tool for Authoring Human-Involved Context-Aware Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xiyun Hu", "<PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>"], "summary": "Recognition of human behavior plays an important role in context-aware applications. However, it is still a challenge for end-users to build personalized applications that accurately recognize their own activities. Therefore, we present CAPturAR, an in-situ programming tool that supports users to rapidly author context-aware applications by referring to their previous activities. We customize an AR head-mounted device with multiple camera systems that allow for non-intrusive capturing of user's daily activities. During authoring, we reconstruct the captured data in AR with an animated avatar and use virtual icons to represent the surrounding environment. With our visual programming interface, users create human-centered rules for the applications and experience them instantly in AR. We further demonstrate four use cases enabled by CAPturAR. Also, we verify the effectiveness of the AR-HMD and the authoring workflow with a system evaluation using our prototype. Moreover, we conduct a remote user study in an AR simulator to evaluate the usability.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415815"}, {"primary_key": "2689423", "vector": [], "sparse_vector": [], "title": "Slice of Light: Transparent and Integrative Transition Among Realities in a Multi-HMD-User Environment.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chia-En <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work presents Slice of Light, a visualization design created to enhance transparency and integrative transition between realities of Head-Mounted Display (HMD) users sharing the same physical environment. Targeted at reality-guests, Slice of Light's design enables guests to view other HMD users' interactions contextualized in their own virtual environments while allowing the guests to navigate among these virtual environments. In this paper, we detail our visualization design and the implementation. We demonstrate Slice of Light with a block-world construction scenario that involves a multi-HMD-user environment. VR developer and HCI expert participants were recruited to evaluate the scenario, and responded positively to Slice of Light. We discuss their feedback, our design insights, and the limitations of this work.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415868"}, {"primary_key": "2689424", "vector": [], "sparse_vector": [], "title": "HMD Light: Sharing In-VR Experience via Head-Mounted Projector for Asymmetric Interaction.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yuan-<PERSON><PERSON> Ye", "<PERSON><PERSON>"], "summary": "We present HMD Light, a proof-of-concept Head-Mounted Display (HMD) implementation that reveals the Virtual Reality (VR) user's experience in the physical environment to facilitate communication between VR and external users in a mobile VR context. While previous work externalized the VR user's experience through an on-HMD display, HMD Light places the display into the physical environment to enable larger display and interaction area. This work explores the interaction design space of HMD Light and presents four applications to demonstrate its versatility. Our exploratory user study observed participant pairs experience applications with HMD Light and evaluated usability, accessibility and social presence between users. From the results, we distill design insights for HMD Light and asymmetric VR collaboration.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415847"}, {"primary_key": "2689425", "vector": [], "sparse_vector": [], "title": "ElastiLinks: Force Feedback between VR Controllers with Dynamic Points of Application of Force.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Force feedback is commonly used to enhance realism in virtual reality (VR). However, current works mainly focus on providing different force types or patterns, but do not investigate how a proper point of application of force (PAF), which means where the resultant force is applied to, affects users' experience. For example, users perceive resistive force without torque when pulling a virtual bow, but with torque when pulling a virtual slingshot. Therefore, we propose a set of handheld controllers, ElastiLinks, to provide force feedback between controllers with dynamic PAFs.A rotatable track on each controller provides a dynamic PAF, and two common types of force feedback, resistive force and impact, are produced by two links, respectively. We performed a force perception study to ascertain users' resistive and impact force level distinguishability between controllers. Based on the results, we conducted another perception study to understand users' distinguishability of PAF offset and rotation differences. Finally, we performed a VR experience study to prove that force feedback with dynamic PAFs enhances VR experience.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415836"}, {"primary_key": "2689426", "vector": [], "sparse_vector": [], "title": "IoT Stickers: Enabling Lightweight Modification of Everyday Objects.", "authors": ["<PERSON><PERSON>"], "summary": "Internet-of-Things (IoT) devices promise to enhance even the most mundane of objects with computational properties by seamlessly coupling the virtual world to the physical. However, IoT's associated costs and cumbersome setup limits its extension to many everyday tasks and objects, such as those in the home. To address these issues, my dissertation work will enable IoT Stickers'a book of inexpensive, battery-free sensors and composition patterns'to support customizing everyday objects with software and web services using stickers. Using RFID-based paper mechanisms, IoT Stickers integrates common sensors and web services with interactive stickers through a trigger-action architecture. This integration enables computational services to be tailored to everyday activities by setting parameters to be passed to the sticker's actions and composing the stickers together. Thus, IoT Stickers demonstrates a way to associate IoT services with a dramatically wider set of objects and tasks.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3415807"}, {"primary_key": "2689427", "vector": [], "sparse_vector": [], "title": "Back-Hand-Pose: 3D Hand Pose Estimation for a Wrist-worn Camera via Dorsum Deformation Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The automatic recognition of how people use their hands and fingers in natural settings -- without instrumenting the fingers -- can be useful for many mobile computing applications. To achieve such an interface, we propose a vision-based 3D hand pose estimation framework using a wrist-worn camera. The main challenge is the oblique angle of the wrist-worn camera, which makes the fingers scarcely visible. To address this, a special network that observes deformations on the back of the hand is required. We introduce DorsalNet, a two-stream convolutional neural network to regress finger joint angles from spatio-temporal features of the dorsal hand region (the movement of bones, muscle, and tendons). This work is the first vision-based real-time 3D hand pose estimator using visual features from the dorsal hand region. Our system achieves a mean joint-angle error of 8.81 degree for user-specific models and 9.77 degree for a general model. Further evaluation shows that our system outperforms previous work with an average of 20% higher accuracy in recognizing dynamic gestures, and achieves a 75% accuracy of detecting 11 different grasp types. We also demonstrate 3 applications which employ our system as a control device, an input device, and a grasped object recognizer.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415897"}, {"primary_key": "2689428", "vector": [], "sparse_vector": [], "title": "ZebraSense: A Double-sided Textile Touch Sensor for Smart Clothing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>"], "summary": "ZebraSense is a novel dual-sided woven touch sensor that can recognize and differentiate interactions on the top and bottom surfaces of the sensor. ZebraSense is based on an industrial multi-layer textile weaving technique, yet it enables a novel capacitive sensing paradigm, where each sensing element contributes to touch detection on both surfaces of the sensor simultaneously. Unlike the common \"sensor sandwich\" approach used in previous work, ZebraSense inherently minimizes the number of sensing elements, which drastically simplifies both sensor construction and its integration into soft goods, while preserving maximum sensor resolution. The experimental evaluation confirmed the validity of our approach and demonstrated that ZebraSense is a reliable, efficient, and accurate solution for detecting user gestures in various dual-sided interaction scenarios, allowing for new use cases in smart apparel, home decoration, toys, and other textile objects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415886"}, {"primary_key": "2689429", "vector": [], "sparse_vector": [], "title": "B2: Bridging Code and Interactive Visualization in Computational Notebooks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data scientists have embraced computational notebooks to author analysis code and accompanying visualizations within a single document. Currently, although these media may be interleaved, they remain siloed: interactive visualizations must be manually specified as they are divorced from the analysis provenance expressed via dataframes, while code cells have no access to users' interactions with visualizations, and hence no way to operate on the results of interaction. To bridge this divide, we present B2, a set of techniques grounded in treating data queries as a shared representation between the code and interactive visualizations. B2 instruments data frames to track the queries expressed in code and synthesize corresponding visualizations. These visualizations are displayed in a dashboard to facilitate interactive analysis. When an interaction occurs, B2 reifies it as a data query and generates a history log in a new code cell. Subsequent cells can use this log to further analyze interaction results and, when marked as reactive, to ensure that code is automatically recomputed when new interaction occurs. In an evaluative study with data scientists, we find that B2 promotes a tighter feedback loop between coding and interacting with visualizations. All participants frequently moved from code to visualization and vice-versa, which facilitated their exploratory data analysis in the notebook.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415851"}, {"primary_key": "2689430", "vector": [], "sparse_vector": [], "title": "Capacitivo: Contact-Based Object Recognition on Interactive Fabrics using Capacitive Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present Capacitivo, a contact-based object recognition technique developed for interactive fabrics, using capacitive sensing. Unlike prior work that has focused on metallic objects, our technique recognizes non-metallic objects such as food, different types of fruits, liquids, and other types of objects that are often found around a home or in a workplace. To demonstrate our technique, we created a prototype composed of a 12 x 12 grid of electrodes, made from conductive fabric attached to a textile substrate. We designed the size and separation between the electrodes to maximize the sensing area and sensitivity. We then used a 10-person study to evaluate the performance of our sensing technique using 20 different objects, which yielded a 94.5% accuracy rate. We conclude this work by presenting several different application scenarios to demonstrate unique interactions that are enabled by our technique on fabrics.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415829"}, {"primary_key": "2689431", "vector": [], "sparse_vector": [], "title": "Crosspower: Bridging Graphics and Linguistics.", "authors": ["<PERSON><PERSON>"], "summary": "Despite the ubiquity of direct manipulation techniques available in computer-aided design applications, creating digital content remains a tedious and indirect task. This is because applications require users to perform numerous low-level editing operations rather than allowing them to directly indicate high-level design goals. Yet, the creation of graphic content, such as videos, animations, and presentations often begins with a description of design goals in natural language, such as screenplays, scripts, outlines. Therefore, there is an opportunity for language-oriented authoring, i.e., leveraging the information found in the structure of a language to facilitate the creation of graphic content. We present a systematic exploration of the identification, graphic description, and interaction with various linguistic structures to assist in the creation of visual content. The prototype system, Crosspower, and its proposed interaction techniques, enables content creators to indicate and customize their desired visual content in a flexible and direct manner.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415845"}, {"primary_key": "2689432", "vector": [], "sparse_vector": [], "title": "Crosscast: Adding Visuals to Audio Travel Podcasts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Audio travel podcasts are a valuable source of information for travelers. Yet, travel is, in many ways, a visual experience and the lack of visuals in travel podcasts can make it difficult for listeners to fully understand the places being discussed. We present Crosscast: a system for automatically adding visuals to audio travel podcasts. Given an audio travel podcast as input, Crosscast uses natural language processing and text mining to identify geographic locations and descriptive keywords within the podcast transcript. Crosscast then uses these locations and keywords to automatically select relevant photos from online repositories and synchronizes their display to align with the audio narration. In a user evaluation, we find that 85.7% of the participants preferred Crosscast generated audio-visual travel podcasts compared to audio-only travel podcasts.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415882"}, {"primary_key": "2689433", "vector": [], "sparse_vector": [], "title": "Hydrauio: Extending Interaction Space on the Pen through Hydraulic Sensing and Haptic Output.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We have explored a fluid-based interface(Hydrauio) on the pen body to extend interaction space of human-pen interaction. Users could perform finger gestures on the pen for input and also receive haptic feedback of different profiles from the fluid surface. The user studies showed that Hydrauio could achieve an accuracy of more than 92% for finger gesture recognition and users could distinguish different haptic profiles with an accuracy of more than 95%. Finally, we present application scenarios to demonstrate the potential of Hydrauio to extend interaction space of human-pen interaction.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416180"}, {"primary_key": "2689434", "vector": [], "sparse_vector": [], "title": "Video-Annotated Augmented Reality Assembly Tutorials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a system for generating and visualizing interactive 3D Augmented Reality tutorials based on 2D video input, which allows viewpoint control at runtime. Inspired by assembly planning, we analyze the input video using a 3D CAD model of the object to determine an assembly graph that encodes blocking relationships between parts. Using an assembly graph enables us to detect assembly steps that are otherwise difficult to extract from the video, and generally improves object detection and tracking by providing prior knowledge about movable parts. To avoid information loss, we combine the 3D animation with relevant parts of the 2D video so that we can show detailed manipulations and tool usage that cannot be easily extracted from the video. To further support user orientation, we visually align the 3D animation with the real-world object by using texture information from the input video. We developed a presentation system that uses commonly available hardware to make our results accessible for home use and demonstrate the effectiveness of our approach by comparing it to traditional video tutorials.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415819"}, {"primary_key": "2689435", "vector": [], "sparse_vector": [], "title": "Pleasant Locomotion - Towards Reducing Cybersickness using fNIRS during Walking Events in VR.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Moving in virtual reality without causing cybersickness is still an unsolved and difficult problem, especially if the virtual space is much larger than the real space and the virtual reality environment asks for quick movements. Many methods to reduce cybersickness are proposed but most of them also reduce immersion. In this paper, we explore the use of fNIRS as an additional modality to detect the level of cybersickness for movement events in VR. We try to mitigate the sickness an individual feels by narrowing the field of vision based on the sickness level detected via measuring increased deoxygenated hemoglobin (Hb) with fNIRS. Our overall goal is to reduce cybersickness in virtual reality applications using physiological signals and appropriate adjustments with little to no impact on immersion.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416184"}, {"primary_key": "2689436", "vector": [], "sparse_vector": [], "title": "Servo-Gaussian Model to Predict Success Rates in Manual Tracking: Path Steering and Pursuit of 1D Moving Target.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a Servo-Gaussian model to predict success rates in continuous manual tracking tasks. Two tasks were conducted to validate this model: path steering and pursuit of a 1D moving target. We hypothesized that (1) hand movements follow the servo-mechanism model, (2) submovement endpoints form a bivariate Gaussian distribution, thus enabling us to predict the success rate at which a submovement endpoint falls inside the tolerance, and (3) the success rate for a whole trial can be predicted if the number of submovements is known. The cross-validation showed R^2>0.92 and MAE0.95 and MAE<6.5% for pursuit tasks. These results demonstrate that our proposed model delivers high prediction accuracy even for unknown datasets.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415896"}, {"primary_key": "2689437", "vector": [], "sparse_vector": [], "title": "DoThisHere: Multimodal Interaction to Improve Cross-Application Tasks on Mobile Devices.", "authors": ["<PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many computing tasks, such as comparison shopping, two-factor authentication, and checking movie reviews, require using multiple apps together. On large screens, \"windows, icons, menus, pointer\" (WIMP) graphical user interfaces (GUIs) support easy sharing of content and context between multiple apps. So, it is straightforward to see the content from one application and write something relevant in another application, such as looking at the map around a place and typing walking instructions into an email. However, although today's smartphones also use GUIs, they have small screens and limited windowing support, making it hard to switch contexts and exchange data between apps.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415841"}, {"primary_key": "2689438", "vector": [], "sparse_vector": [], "title": "SimuLearn: Fast and Accurate Simulator to Support Morphing Materials Design and Workflows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Morphing materials allow us to create new modalities of interaction and fabrication by leveraging the materials? dynamic behaviors. Yet, despite the ongoing rapid growth of computational tools within this realm, current developments are bottlenecked by the lack of an effective simulation method. As a result, existing design tools must trade-off between speed and accuracy to support a real-time interactive design scenario. In response, we introduce SimuLearn, a data-driven method that combines finite element analysis and machine learning to create real-time (0.61 seconds) and truthful (97% accuracy) morphing material simulators. We use mesh-like 4D printed structures to contextualize this method and prototype design tools to exemplify the design workflows and spaces enabled by a fast and accurate simulation method. Situating this work among existing literature, we believe SimuLearn is a timely addition to the HCI CAD toolbox that can enable the proliferation of morphing materials.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415867"}, {"primary_key": "2689439", "vector": [], "sparse_vector": [], "title": "ZoomWalls: Dynamic Walls that Simulate Haptic Infrastructure for Room-scale VR World.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We focus on the problem of simulating the haptic infrastructure of a virtual environment (i.e. walls, doors). Our approach relies on multiple ZoomWalls---autonomous robotic encounter-type haptic wall-shaped props---that coordinate to provide haptic feedback for room-scale virtual reality. Based on a user's movement through the physical space, ZoomWall props are coordinated through a predict-and-dispatch architecture to provide just-in-time haptic feedback for objects the user is about to touch. To refine our system, we conducted simulation studies of different prediction algorithms, which helped us to refine our algorithmic approach to realize the physical ZoomWall prototype. Finally, we evaluated our system through a user experience study, which showed that participants found that ZoomWalls increased their sense of presence in the VR environment. ZoomWalls represents an instance of autonomous mobile reusable props, which we view as an important design direction for haptics in VR.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415859"}, {"primary_key": "2689440", "vector": [], "sparse_vector": [], "title": "Skyline: Interactive In-Editor Computational Performance Profiling for Deep Neural Network Training.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Training a state-of-the-art deep neural network (DNNs) is a computationally-expensive and time-consuming process, which incentivizes deep learning developers to debug their DNNs for computational performance. However, effectively performing this debugging requires intimate knowledge about the underlying software and hardware systems-something that the typical deep learning developer may not have. To help bridge this gap, we present Skyline: a new interactive tool for DNN training that supports in-editor computational performance profiling, visualization, and debugging. Skyline's key contribution is that it leverages special computational properties of DNN training to provide (i) interactive performance predictions and visualizations, and (ii) directly manipulatable visualizations that, when dragged, mutate the batch size in the code. As an in-editor tool, Skyline allows users to leverage these diagnostic features to debug the performance of their DNNs during development. An exploratory qualitative user study of Skyline produced promising results; all the participants found Skyline to be useful and easy to use.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415890"}, {"primary_key": "2689441", "vector": [], "sparse_vector": [], "title": "Shifting &amp; Warping: A Case for the Combined Use of Dynamic Passive Haptics and Haptic Retargeting in VR.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Passive haptic feedback for virtual reality can provide immersive sensations but suffers from scalability issues. To tackle these, two independent concepts have been proposed previously: Dynamic Passive Haptic Feedback (DPHF), leveraging actuated props that change their physical state, and Haptic Retargeting, redirecting the user's hand during interaction. While past research on both techniques reported promising results, up to now, these concepts remained isolated. This paper advocates the combined use of DPHF and Haptic Retargeting. We introduce two thought experiments showcasing that the combination of both techniques in an example scenario is beneficial to solve two central challenges of prop-based VR haptics: haptic similarity and co-location of proxies and virtual objects.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416166"}, {"primary_key": "2689442", "vector": [], "sparse_vector": [], "title": "PolicyKit: Building Governance in Online Communities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The software behind online community platforms encodes a governance model that represents a strikingly narrow set of governance possibilities focused on moderators and administrators. When online communities desire other forms of government, such as ones that take many members? opinions into account or that distribute power in non-trivial ways, communities must resort to laborious manual effort. In this paper, we present PolicyKit, a software infrastructure that empowers online community members to concisely author a wide range of governance procedures and automatically carry out those procedures on their home platforms. We draw on political science theory to encode community governance into policies, or short imperative functions that specify a procedure for determining whether a user-initiated action can execute. Actions that can be governed by policies encompass everyday activities such as posting or moderating a message, but actions can also encompass changes to the policies themselves, enabling the evolution of governance over time. We demonstrate the expressivity of PolicyKit through implementations of governance models such as a random jury deliberation, a multi-stage caucus, a reputation system, and a promotion procedure inspired by Wikipedia's Request for Adminship (RfA) process.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415858"}, {"primary_key": "2689443", "vector": [], "sparse_vector": [], "title": "FlowMatic: An Immersive Authoring Tool for Creating Interactive Scenes in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Immersive authoring is a paradigm that makes Virtual Reality (VR) application development easier by allowing programmers to create VR content while immersed in the virtual environment. In this paradigm, programmers manipulate programming primitives through direct manipulation and get immediate feedback on their program's state and output. However, existing immersive authoring tools have a low ceiling; their programming primitives are intuitive but can only express a limited set of static relationships between elements in a scene. In this paper, we introduce FlowMatic, an immersive authoring tool that raises the ceiling of expressiveness by allowing programmers to specify reactive behaviors---behaviors that react to discrete events such as user actions, system timers, or collisions. FlowMatic also introduces primitives for programmatically creating and destroying new objects, for abstracting and re-using functionality, and for importing 3D models. Importantly, FlowMatic uses novel visual representations to allow these primitives to be represented directly in VR. We also describe the results of a user study that illustrates the usability advantages of FlowMatic relative to a 2D authoring tool and we demonstrate its expressiveness through several example applications that would be impossible to implement with existing immersive authoring tools. By combining a visual program representation with expressive programming primitives and a natural User Interface (UI) for authoring programs, FlowMatic shows how programmers can build fully interactive virtual experiences with immersive authoring.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415824"}, {"primary_key": "2689444", "vector": [], "sparse_vector": [], "title": "Situated Learning of Soft Skills with an Interactive Agent in Virtual Reality via Multimodal Feedback.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, customer virtual reality (VR) devices can offer high-quality presence experiences in a relatively low cost, which opens opportunities for popularizing situated learning. We investigate situated learning of soft skills, in particular, elevator pitches, through simulating an interactive agent with multimodal feedback in VR. A virtual agent acts as a coach and offers both verbal and nonverbal feedback to users. We developed a prototype system and conducted a quantitative experiment to investigate the potential of situated learning in VR, especially on how users perceive the virtual agent. We summarize design considerations on developing interactive agents with multimodal feedback in VR to facilitate situated learning of soft skills.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416174"}, {"primary_key": "2689445", "vector": [], "sparse_vector": [], "title": "MorphSensor: A 3D Electronic Design Tool for Reforming Sensor Modules.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ji<PERSON>ng <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MorphSensor is a 3D electronic design tool that enables designers to morph existing sensor modules of pre-defined two-dimensional shape into free-form electronic component arrangements that better integrate with the three-dimensional shape of a physical prototype. MorphSensor builds onto existing sensor module schematics that already define the electronic components and the wiring required to build the sensor. Since MorphSensor maintains the wire connections throughout the editing process, the sensor remains fully functional even when designers change the electronic component layout on the prototype geometry. We detail the MorphSensor editor that supports designers in re-arranging the electronic components, and discuss a fabrication pipeline based on customized PCB footprints for making the resulting freeform sensor. We then demonstrate the capabilities of our system by morphing a range of sensor modules of different complexity and provide a technical evaluation of the quality of the resulting free-form sensors.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337.3415898"}, {"primary_key": "2689446", "vector": [], "sparse_vector": [], "title": "Demonstration of MorphSensor: A 3D Electronic Design Tool for Reforming Sensor Modules.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ji<PERSON>ng <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MorphSensor is a 3D electronic design tool that enables designers to morph existing sensor modules of pre-defined two-dimensional shape into free-form electronic component arrangements that better integrate with the three-dimensional shape of a physical prototype. MorphSensor builds onto existing sensor module schematics that already define the electronic components and the wiring required to build the sensor. Since MorphSensor maintains the wire connections throughout the editing process, the sensor remains fully functional even when designers change the electronic component layout on the prototype geometry. We present the MorphSensor editor that supports designers in re-arranging the electronic components, and discuss a fabrication pipeline based on customized PCB footprints for making the resulting freeform sensor.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350.3416135"}, {"primary_key": "2702554", "vector": [], "sparse_vector": [], "title": "UIST &apos;20: The 33rd Annual ACM Symposium on User Interface Software and Technology, Virtual Event, USA, October 20-23, 2020", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We are honored to welcome an unusual audience to a remarkable event -- the 33rd ACM Symposium on User Interface Software and Technology. UIST is always special to its loyal and ever-expanding community. 2020, however, will resonate in our memories as the year of the COVID-19 pandemic. During one unprecedented week in March the world went into self-isolation, and in many ways remains there in October. Conferences such as UIST have had to transform overnight to operate in cyberspace, and yet still serve their vital function of meaningfully connecting their communities. Turning UIST into a virtual meeting has been an extraordinary design journey. We were on track for a “normal” meeting in Minneapolis's friendly downtown where we expected to revel in talks, stimulating conversation, hands-on demos, golden October weather and local food. By May, we had to accept that there was no alternative to going virtual. Since then we've been working to implement a completely different event — one confined to the perimeters of our computer screens. In striving to imbue our meeting with the social interactions that come for free in a physical event, we are at the frontiers of technical and social HCI. We honor and applaud our organizing team, who are to the last person tireless, patient, creative, resourceful, full of humor and uncomplaining of the redoubled effort in tackling this monumental challenge. It has been inspiring. 2020 is also the year when, following a period of #MeToo awakening to issues of gender equity and justice, the world was shocked into awareness and the beginnings of accountability to other groups who, because of race, gender identification, disability and other dimensions of human diversity, have been shaped by systemic and long-term injustice.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379337"}, {"primary_key": "2702555", "vector": [], "sparse_vector": [], "title": "UIST &apos;20 Adjunct: The 33rd Annual ACM Symposium on User Interface Software and Technology, Virtual Event, USA, October 20-23, 2020", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We are honored to welcome an unusual audience to a remarkable event -- the 33rd ACM Symposium on User Interface Software and Technology. UIST is always special to its loyal and ever-expanding community. 2020, however, will resonate in our memories as the year of the COVID-19 pandemic. During one unprecedented week in March the world went into self-isolation, and in many ways remains there in October. Conferences such as UIST have had to transform overnight to operate in cyberspace, and yet still serve their vital function of meaningfully connecting their communities. Turning UIST into a virtual meeting has been an extraordinary design journey. We were on track for a 'normal' meeting in Minneapolis's friendly downtown where we expected to revel in talks, stimulating conversation, hands-on demos, golden October weather and local food. By May, we had to accept that there was no alternative to going virtual. Since then we've been working to implement a completely different event - one confined to the perimeters of our computer screens. In striving to imbue our meeting with the social interactions that come for free in a physical event, we are at the frontiers of technical and social HCI. We honor and applaud our organizing team, who are to the last person tireless, patient, creative, resourceful, full of humor and uncomplaining of the redoubled effort in tackling this monumental challenge. It has been inspiring. 2020 is also the year when, following a period of #MeToo awakening to issues of gender equity and justice, the world was shocked into awareness and the beginnings of accountability to other groups who, because of race, gender identification, disability and other dimensions of human diversity, have been shaped by systemic and long-term injustice.", "published": "2020-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3379350"}]