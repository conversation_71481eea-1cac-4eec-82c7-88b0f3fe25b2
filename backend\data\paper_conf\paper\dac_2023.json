[{"primary_key": "1119941", "vector": [], "sparse_vector": [], "title": "Architecting Selective Refresh based Multi-Retention Cache for Heterogeneous System (ARMOUR).", "authors": ["<PERSON><PERSON><PERSON>", "Shounak Chakraborty", "<PERSON>"], "summary": "The increasing use of chiplets, and the demand for high-performance yet low-power systems, will result in heterogeneous systems that combine both CPUs and accelerators (e.g., general-purpose GPUs). Chiplet based designs also enable the inclusion of emerging memory technologies, since such technologies can reside on a separate chiplet without requiring complex integration in existing high-performance process technologies. One such emerging memory technology is spin-transfer torque (STT) memory, which has the potential to replace SRAM as the last-level cache (LLC). STT-RAM has the advantage of high density, non-volatility, and reduced leakage power, but suffers from a higher write latency and energy, as compared to SRAM. However, by relaxing the retention time, the write latency and energy can be reduced at the cost of the STT-RAM becoming more volatile. The retention time and write latency/energy can be traded against each other by creating an LLC with multiple retention zones. With a multi-retention LLC, the challenge is to direct the memory accesses to the most advantageous zone, to optimize for overall performance and energy efficiency. We propose ARMOUR, a mechanism for efficient management of memory accesses to a multi-retention LLC, where based on the initial requester (CPU or GPU) the cache blocks are allocated in the high (CPU) or low (GPU) retention zone. Furthermore, blocks that are about to expire are either refreshed (CPU) or written back (GPU). In addition, ARMOUR evicts CPU blocks with an estimated short lifetime, which further improves cache performance by reducing cache pollution. Our evaluation shows that ARMOUR improves average performance by 28.9% compared to a baseline STT-RAM based LLC and reduces the energy-delay product (EDP) by 74.5% compared to an iso-area SRAM LLC.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247878"}, {"primary_key": "1119942", "vector": [], "sparse_vector": [], "title": "PertNAS: Architectural Perturbations for Memory-Efficient Neural Architecture Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Differentiable Neural Architecture Search (NAS) relies on aggressive weight-sharing to reduce its search cost. This leads to GPU-memory bottlenecks that hamper the algorithm's scalability. To resolve these bottlenecks, we propose a perturbations-based evolutionary approach that significantly reduces the memory cost while largely maintaining the efficiency benefits of weight-sharing. Our approach makes minute changes to compact neural architectures and measures their impact on performance. In this way, it extracts high-quality motifs from the search space. We utilize these perturbations to perform NAS in compact models evolving over time to traverse the search space. Our method disentangles GPU-memory consumption from search space size, offering exceptional scalability to large search spaces. Results show competitive accuracy on multiple benchmarks, including CIFAR10, ImageNet2012, and NASBench-301. Specifically, our approach improves accuracy on ImageNet and NASBench-301 by 0.3% and 0.87%, respectively. Furthermore, the memory consumption of search is reduced by roughly 80% against state-of-the-art weight-shared differentiable NAS works while achieving a search time of only 6 GPU hours.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247756"}, {"primary_key": "1119943", "vector": [], "sparse_vector": [], "title": "Cryogenic In-Memory Matrix-Vector Multiplication using Ferroelectric Superconducting Quantum Interference Device (FE-SQUID).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Next-generation quantum computing (QC) systems, comprising thousands of qubits, are envisioned to accommodate the quantum substrate (qubits) and classical components (control processor, and a digital memory block) in a cryogenic (< 4 Kelvin) environment. Such homogeneous integration will pave the way for superconducting interconnects and reduce the noise arising from thermal gradient. However, in the existing QC systems, cryogenic control processors and memory blocks are still operated following the von <PERSON> architecture. This leads to significant performance overhead due to the repetitive data movement between physically distinct memory and processing units. Thus, it becomes challenging to implement computationally expensive machine learning (ML) algorithms for efficient error correction and control of qubits in a QC. In-memory implementation of ML algorithms at cryogenic temperature can be a game-changer for a practical QC. Here, we demonstrate a unique technique for cryogenic in-memory matrix vector multiplication (MVM), the most frequently performed operation in ML algorithms, utilizing a ferroelectric superconducting quantum interference device (FE-SQUID)-based memory array. FE-SQUID is a promising cryogenic memory device thanks to its non-volatile nature, voltage-controlled switching, scalability, and compatibility with commercially available superconducting device fabrication processes. Moreover, due to having separate read-write paths, the read operation can be optimized without imposing any limit on the read bias and hence, multiple levels of read current with notable separation can be used to map the inputs for the MVM operation. We use an experimentally-calibrated compact model for FE-SQUID to design and test our proposed system. We evaluate FE-SQUID-based in-memory MVM by performing several classification tasks using MNIST handwritten digits, fashion, and emotion datasets. We achieve 93.83%, 80.49%, and 92.5% accuracy for handwritten digits, fashion, and sentiment classifications, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247669"}, {"primary_key": "1119944", "vector": [], "sparse_vector": [], "title": "FPDsim: A Structural Simulator For Power Grid Analysis Of Flat Panel Display.", "authors": ["Chengtao An", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "Yangfeng Su", "<PERSON>", "<PERSON><PERSON>"], "summary": "As the resolution continues to increase, the scale of the power grid of the flat panel display (FPD) becomes huge. This imposes a severe computational challenge for analysis and storage. In this paper, based on the highly periodic FPD structure, we present an efficient simulator which contains a novel stamping scheme and a fast structural solver. Experiments on real industrial cases show that compared to the conventional stamping scheme, our proposed stamping scheme achieves up to 118× speedup in 74× less memory; compared to the state-of-art direct solver, <PERSON><PERSON><PERSON>, our proposed structural solver achieves up to 17× speedup in 5× less memory.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247770"}, {"primary_key": "1119945", "vector": [], "sparse_vector": [], "title": "R-TOSS: A Framework for Real-Time Object Detection using Semi-Structured Pruning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Object detectors used in autonomous vehicles can have high memory and computational overheads. In this paper, we introduce a novel semi-structured pruning framework called R-TOSS that overcomes the shortcomings of state-of-the-art model pruning techniques. Experimental results on the JetsonTX2 platform show that R-TOSS has a compression rate of 4.4× on the YOLOv5 object detector with a 2.15× speedup in inference time and 57.01% decrease in energy usage. R-TOSS also enables 2.89× compression on RetinaNet with a 1.86× speedup in inference time and 56.31% decrease in energy usage. We also demonstrate significant improvements compared to various state-of-the-art pruning techniques.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247917"}, {"primary_key": "1119946", "vector": [], "sparse_vector": [], "title": "A Comprehensive Automated Exploration Framework for Systolic Array Designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many researchers studying the performance tuning of systolic arrays have based their works on oversimplified assumptions like considering only divisors for loop tiling or pruning based on off-chip data communication to reduce the design space. In this paper, we present a comprehensive design space exploration tool named Odyssey for systolic array optimization. Odyssey results show that limiting tiling factors to only divisors of the problem size can cause up to 39% performance loss, and pruning the design space based on off-chip data movement can miss optimal designs. We tested Odyssey using various matrix multiplication and convolution kernels and validated the results with FPGA implementations.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248016"}, {"primary_key": "1119947", "vector": [], "sparse_vector": [], "title": "Map-and-Conquer: Energy-Efficient Mapping of Dynamic Neural Nets onto Heterogeneous MPSoCs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Heterogeneous MPSoCs comprise diverse processing units of varying compute capabilities. To date, the mapping strategies of neural networks (NNs) onto such systems are yet to exploit the full potential of processing parallelism, made possible through both the intrinsic NNs' structure and underlying hardware composition. In this paper, we propose a novel framework to effectively map NNs onto heterogeneous MPSoCs in a manner that enables them to leverage the underlying processing concurrency. Specifically, our approach identifies an optimal partitioning scheme of the NN along its 'width' dimension, which facilitates deployment of concurrent NN blocks onto different hardware computing units. Additionally, our approach contributes a novel scheme to deploy partitioned NNs onto the MPSoC as dynamic multi-exit networks for additional performance gains. Our experiments on a standard MPSoC platform have yielded dynamic mapping configurations that are 2.1x more energy-efficient than the GPU-only mapping while incurring 1.7x less latency than DLA-only mapping.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247722"}, {"primary_key": "1119948", "vector": [], "sparse_vector": [], "title": "Fine-Grained QoS Control via Tightly-Coupled Bandwidth Monitoring and Regulation for FPGA-based Heterogeneous SoCs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded systems are increasingly adopting heterogeneous templates integrating hardware accelerators and application-specific processors, which poses novel challenges. In particular, it is difficult to have accurate control of task activities in Commercial Off-the-shelf (COTS) System on Chips (SoCs), due to complex main memory sharing mechanisms among different computing engines. To address this problem, bandwidth regulation approaches based on monitoring and throttling are widely adopted. Existing solutions, however, are either too coarse-grained, limiting the control over computing engines activities, or platform-dependent, addressing the problem only for specific SoCs. In this paper we propose an innovative, fine-grained and platform-independent approach that can accurately control main memory bandwidth usage in an FPGA-based Heterogeneous System on Chip (HeSoC). Experimental results conducted on the Xilinx Zynq UltraScale+ platform demonstrate that our approach enables solutions not feasible with state-of-the-art bandwidth regulation methods.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247840"}, {"primary_key": "1119949", "vector": [], "sparse_vector": [], "title": "PUFFER: A Routability-Driven Placement Framework via Cell Padding with Multiple Features and Strategy Exploration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingyu Tong", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Placement is a critical stage in VLSI physical design, especially for routability optimization. Due to the large scale and high integration introduced by the advanced semiconductor manufacturing technology, there remains a significant challenge in routability in the placement stage, which will affect the subsequent routing process. This paper proposes a placement framework, called PUFFER, to optimize routability by cell padding and strategy exploration. The framework first estimates congestion by imitating the behaviors of routing detours and clustered cell spreading. Then it calculates cell padding based on multiple features inspired by the characteristics of convolutional and graph neural networks. Besides, it applies a Bayesian-based method to explore a better placement strategy. Compared with a commercial tool and the state-of-the-art academic <PERSON><PERSON>l<PERSON><PERSON> place<PERSON>, experiments on industrial benchmarks show that our framework achieves the best routability on average, with a 2.7× speedup over the commercial tool.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119950", "vector": [], "sparse_vector": [], "title": "Improving Standard-Cell Design Flow using Factored Form Optimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Xu"], "summary": "Factored form is a powerful multi-level representation of a Boolean function that readily translates into an implementation of the function in CMOS technology. In particular, the number of literals in a factored form correlates strongly with the number of transistors in the CMOS implementation. This paper develops novel methods for optimizing factored forms while working on the efficient and-inverter graph (AIG) representation of combinational logic. This is in contrast to the traditional logic synthesis based on logic networks, and other AIG-based methods that minimize the AIG nodes count. Experiments show that applying these methods helps to reduce the area after technology mapping by an additional 2.8% on average, compared to a high-effort area-oriented baseline. It is expected that deploying these methods as part of an industrial standard-cell design flow will reduce design costs and power consumption. Additionally, this work enables efficient transistor-level logic synthesis of large designs with various applications in design automation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247905"}, {"primary_key": "1119951", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: RL-LPO: Reinforcement Learning Based Leakage Power Optimization Framework with Graph Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leakage power optimization based on threshold voltage (V th ) assignment poses great challenge in circuit design due to its tremendous solution space. In this paper, a Reinforcement Learning-based Leakage Power Optimization framework (RL-LPO) is first-ever proposed to formulate V th assignment as a reinforcement learning (RL) process by learning timing and physical characteristics of each circuit instance with Graph Neural Networks (GNN). The proposed RL-LPO was validated by the IWLS2005 and Opencores benchmark circuits with TSMC 28nm technology and experimental results demonstrate that our work achieves better leakage power optimization by additional 3% reduction on average than the commercial tool PrimeTime with 6.7× speed up when being transferred to unseen circuits with negligible timing degradation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247707"}, {"primary_key": "1119952", "vector": [], "sparse_vector": [], "title": "Towards A Formally Verified Fully Homomorphic Encryption Compute Engine.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a scalable approach for formally verifying the correctness of the Compute Engine (CE) against its ISA (Instruction Set Architecture) specification in an FHE (Fully Homomorphic Encryption) accelerator, critical to many applications where safety and security of information is of vital importance. It combines algorithmic verification of the micro-architecture modules in the CE against their functional specifications and implementation verification of the CE hardware against its micro-architecture algorithmic specifications. The correctness of the CE is guaranteed by treating micro-architecture modules as semantic-preserving program transformations and leveraging the composability of the semantic-preserving properties well established in compiler design and verification.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247836"}, {"primary_key": "1119953", "vector": [], "sparse_vector": [], "title": "VideoFlip: Adversarial Bit Flips for Reducing Video Service Quality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video compression is a critical component in streaming applications that maximizes users' quality of experience (QoE) while satisfying the underlying bandwidth constraints. In this paper, we demonstrate the first hardware-based fault-injection attack on video compression, dubbed VideoFlip. The attack identifies and alters a few bits inside the video compression model to achieve one or more of the following adversarial goals: (1) increasing the bitrate and (2) degrading the quality of the compressed video. Extensive evaluations show VideoFlip irresistibly downgrades video compression performance for various models and video datasets while being resilient to contemporary defenses against hardware-based bit flips.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247863"}, {"primary_key": "1119954", "vector": [], "sparse_vector": [], "title": "HDSuper: Algorithm-Hardware Co-design for Light-weight High-quality Super-Resolution Accelerator.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Super-resolution (SR) networks have been gradually applied to embedded devices with good-quality image reconstruction. However, the hardware performance and power efficiency are limited by a large number of algorithm parameters, computation complexity, and hardware resources, obstructing the development of a high-quality SR accelerator. This paper proposes an end-to-end platform with a lightweight super-resolution network (LSR) and an efficient, high-quality super-resolution architecture HDSuper, to perform algorithm-hardware co-design for the SR accelerator. For algorithm design, we employ depth-wise separable convolution and pixelshuffle to reduce network size and computation complexity by considering the hardware constraints. For hardware design, we provide a unified computing core (UCC) combined with an efficient flattening-and-allocation (F-A) mapping strategy to support various operators with high computational utilization. We adopt the patch training method to reduce the external memory access of the hardware architecture. Based on the evaluation, the proposed algorithm achieves high-quality image reconstruction with 37.44dB PSNR. Finally, we implement the image reconstruction in FPGA demonstration, achieving high-quality image reconstruction with 2.08W power consumption under the lowest hardware resources compared to the state-of-the-art works.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247683"}, {"primary_key": "1119955", "vector": [], "sparse_vector": [], "title": "G-QED: Generalized QED Pre-silicon Verification beyond Non-Interfering Hardware Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Brandon A. D&apos;Agostino", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hardware accelerators (HAs) underpin high-performance and energy-efficient digital systems. Correctness of these systems thus depends on the correctness of constituent HAs. Self-consistency-based pre-silicon verification techniques, like A-QED (Accelerator Quick Error Detection), provide a quick and provably thorough HA verification framework that does not require extensive design-specific properties or a full functional specification. However, A-QED is limited to verifying HAs which are non-interfering – i.e., they produce the same result for a given input independent of its context within a sequence of inputs. We present a new technique called G-QED (Generalized QED) which goes beyond non-interfering HAs while retaining A-QED's benefits. Our extensive results as well as a detailed industrial case study show that: G-QED is highly thorough in detecting critical bugs in well-verified designs that otherwise escape traditional verification flows while simultaneously improving verification productivity 18-fold (from 370 person days to 21 person days). These results are backed by theoretical guarantees of soundness and completeness.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247903"}, {"primary_key": "1119956", "vector": [], "sparse_vector": [], "title": "Lightning Talk: The Next Wave of High-level Synthesis.", "authors": ["<PERSON><PERSON>"], "summary": "Recent works established new High-Level Synthesis (HLS) solutions translating AI models described in PyTorch to customized AI accelerators automatically. By adopting PyTorch as input for AI designs (instead of traditional C/C++ for HLS), the lines of code and design simulation time can be reduced by about 10× and 100×, respectively. Such AI model-to-RTL flows pave the way for a new wave of HLS that could drive the high-productivity designs of AI circuits with high-density, high-energy efficiency, low cost, and short design cycle. And such high-level model-to-RTL flows can be expanded to other non-AI domains. Meanwhile, we are also facing existing and new challenges for such HLS solutions, such as ensuring the correctness of the high-level design, accommodating accurate low-level timing/energy information, handling the complexity of 3D circuits and/or chiplet-based design flows, and achieving all these in a highly scalable manner. In this paper, we share the state-of-the-art solutions, limitations, and new opportunities facing the emergence of a new wave of the next-generation HLS.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119957", "vector": [], "sparse_vector": [], "title": "Graph-Based Simultaneous Placement and Routing for Two-Dimensional Directed Self-Assembly Technology.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two-dimensional directed self-assembly (2D-DSA) is an emerging lithography technology for advanced process nodes. We can determine the orientations of double posts to guide block copolymers to form feasible 2D guiding template patterns by the 2D-DSA process. This paper presents the first work to handle the 2D-DSA simultaneous placement and routing problem. We first propose a novel graph to model feasible guiding templates with a constant-time update scheme for each double-post assignment. Based on a graph model, we then present an algorithm for 2D-DSA simultaneous placement and routing, with a broadcast-based cost function for 2D-DSA cell placement and a graph-based scheme for DSA-compliant routing. Finally, we employ a strongly effective region property to minimize the cuts in the final layout. Experimental results show that our algorithm can efficiently generate a 2D-DSA placement and routing solution with high routability and a low cut number.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119958", "vector": [], "sparse_vector": [], "title": "Toward Parallelism-Optimal Topology Generation for Wavelength-Routed Optical NoC Designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The wavelength-routed optical network-on-chip (WRONoC) emerges as a promising solution for multi-core system communication, providing high-bandwidth, high-speed, and low-power transmission. As the number of cores in a WRONoC increases, however, some WRONoC topologies could be infeasible with bandwidth and crosstalk constraints if bit-level parallelism is not considered during topology generation. Previous work optimized the parallelism only for the radius selection of microring resonators but not for topology generation. To remedy this drawback, we present a parallelism-aware WRONoC topology generation flow. The proposed flow guarantees to generate a parallelism-optimal topology for full connectivity; and a parallelism-optimal topology for customized connectivity if the netlist meets certain conditions. Compared with the state-of-the-art methods, experimental results show a 67.5% improvement in parallelism.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247996"}, {"primary_key": "1119959", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Analytical Placement for 3D ICs with Multiple Manufacturing Technologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a high-quality 3D placement algorithm to determine the positions of standard cells and inter-die vias to optimize wirelength considering multiple manufacturing technologies for different dies. The algorithm consists of three major novel techniques: (1) a multi-technologies weighted-average (MTWA) wirelength model, (2) a weighted inter-die-connection cost controlling the net-degree distribution of the cut set, and (3) a via-cell co-optimization technique to further improve the quality of placement solutions. Compared with the winners at the 2022 CAD Contest at ICCAD on 3D Placement with D2D Vertical Connections, our placer achieves the best results for all nontrivial cases.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247864"}, {"primary_key": "1119960", "vector": [], "sparse_vector": [], "title": "UpPipe: A Novel Pipeline Management on In-Memory Processors for RNA-seq Quantification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "RNA sequence quantification is an important analysis method to measure transcript abundances. A key overhead in RNA-seq quantification is to map a set of RNA reads to multiple reference transcripts, i.e., transcriptome. Besides, the performance of RNA-seq quantification is strictly limited by the excessive amounts of data movement between CPU and memory, i.e., memory wall problem on the conventional architecture. As the first publicly commercial processing-in-memory (PIM) system, UPMEM DPU, is proposed, the PIM gradually becomes a promising solution to overcome the memory wall problem. DPUs show great potential to accelerate data-intensive workloads by minimizing off-chip data movement between CPU and memory. Thus, this paper aims to improve the performance of RNA-seq quantification by fully exploiting the strengths of DPU. To achieve that, we propose a novel DPU-aware pipeline design \"UpPipe\" built on the software layer to address the hardware constraints of DPU. To the best of our knowledge, this is the first work to enable pipeline management on the DPU system. The evaluation results demonstrate the feasibility of our proposed design and provide a comprehensive study on how to utilize the limited hardware resources of DPUs efficiently.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247915"}, {"primary_key": "1119961", "vector": [], "sparse_vector": [], "title": "Unified Agile Accuracy Assessment in Computing-in-Memory Neural Accelerators by Layerwise Dynamical Isometry.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deploying neural networks (NN) on computing-in-memory (CIM) neural accelerators incurs additional hardware factors in the test accuracy, which add substantial extra evaluation overhead. This work takes the first step to quantitatively analyze how information propagates in CIM neural accelerators as well as how additional CIM factors influence that information propagation. From our analysis, we propose a new metric named Unified-QCN that is theoretically linked to the test accuracy according to layerwise dynamical isometry (LDI), providing us with a compass to avoid direct time-consuming simulations. Our method consistently delivers high correlations with the test accuracy for various NN backbones on different datasets.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247782"}, {"primary_key": "1119962", "vector": [], "sparse_vector": [], "title": "IFHE: Intermediate-Feature Heterogeneity Enhancement for Image Synthesis in Data-Free Knowledge Distillation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data-free knowledge distillation (DFKD) explores training a compact student network only by a pre-trained teacher without real data. Prevailing DFKD methods mainly consist of image synthesis and knowledge distillation. The synthesized images are crucial to enhance the student network performance. However, the images synthesized by existing methods cause high homogeneity on intermediate features, incurring undesired distillation performance. To address this problem, we propose the Intermediate-Feature Heterogeneity Enhancement (IFHE) method, which effectively enhances the heterogeneity of synthesized images by minimizing the loss between intermediate features and pre-set labels of the synthesized images Our IFHE outperforms the SOTA results on CIFAR-10/100 datasets of representative networks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247717"}, {"primary_key": "1119963", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Weight Decay is ALL You Need for Neural Network Sparsification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The heuristic iterative pruning strategy has been widely used for neural network sparsification. However, it is challenging to identify the right connections to remove at each pruning iteration with only a one-shot evaluation of weight magnitude, especially at the early pruning stage. The erroneously removed connections, unfortunately, can hardly be recovered. In this work, we propose a weight decay strategy as a substitute for pruning, which let the \"insignificant\" weights moderately decay instead of being directly clamped to zero. At the end of the training, the vast majority of redundant weights will naturally become close to zero, making it easier to identify which connections could be removed safely. Experimental results show that the proposed weight decay method can achieve an ultra-high sparsity of 99%. Compared to the current pruning strategy, the model size is further reduced by 34%, improving the compression rate from 69× to 106× at the same accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247950"}, {"primary_key": "1119964", "vector": [], "sparse_vector": [], "title": "Physics-Informed Optical Kernel Regression Using Complex-valued Neural Fields.", "authors": ["Guo<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Lithography is fundamental to integrated circuit fabrication, necessitating large computation overhead. The advancement of machine learning (ML)-based lithography models alleviates the trade-offs between manufacturing process expense and capability. However, all previous methods regard the lithography system as an image-to-image black box mapping, utilizing network parameters to learn by rote mappings from massive mask-to-aerial or mask-to-resist image pairs, resulting in poor generalization capability. In this paper, we propose a new ML-based paradigm disassembling the rigorous lithographic model into non-parametric mask operations and learned optical kernels containing determinant source, pupil, and lithography information. By optimizing complex-valued neural fields to perform optical kernel regression from coordinates, our method can accurately restore lithography system using a small-scale training dataset with fewer parameters, demonstrating superior generalization capability as well. Experiments show that our framework can use 31% of parameters while achieving 69× smaller mean squared error with 1.3× higher throughput than the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247680"}, {"primary_key": "1119965", "vector": [], "sparse_vector": [], "title": "AutoDCIM: An Automated Digital CIM Compiler.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Digital Computing-in-Memory (DCIM) is an emerging architecture that integrates digital logic into memory for efficient AI computing. However, current DCIM designs heavily rely on manual efforts. This increases DCIM design time and limits the optimization space, making it challenging to satisfy the user specifications of diverse AI applications. This paper presents AutoDCIM, the first automated DCIM compiler. Au-toDCIM takes the user specifications as inputs and generates a DCIM macro architecture with an optimized layout. AutoDCIM's template-based generation balances handcrafted cell design and agile macro development. AutoDCIM's layout exploration loop analyzes diverse DCIM array partitioning schemes to satisfy user specifications. The auto-generated DCIM macros present competitive efficiency results in comparison with state-of-the-art silicon-verified DCIM macros.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247976"}, {"primary_key": "1119966", "vector": [], "sparse_vector": [], "title": "Property-Based Timing Analysis and Optimization for Complex Cyber-Physical Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This lightning talk introduces the motivations of the needs of formal properties that can be used modularly to compose safe and tight analysis and optimization for the scheduler design and schedulability test problems for cyber-physical real-time systems. The key challenge is the correct and precise translation from different schedule functions to proper mathematical properties that can be further used for property-based modulable designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247807"}, {"primary_key": "1119967", "vector": [], "sparse_vector": [], "title": "Lift: Exploiting Hybrid Stacked Memory for Energy-Efficient Processing of Graph Convolutional Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Graph Convolutional Networks (GCNs) are powerful learning approaches for graph-structured data. GCNs are both computing- and memory-intensive. The emerging 3D-stacked computation-in-memory (CIM) architecture provides a promising solution to process GCNs efficiently. The CIM architecture can provide near-data computing, thereby reducing data movement between computing logic and memory. However, previous works do not fully exploit the CIM architecture in both dataflow and mapping, leading to significant energy consumption.This paper presents Lift, an energy-efficient GCN accelerator based on 3D CIM architecture using software and hardware co-design. At the hardware level, Lift introduces a hybrid architecture to process vertices with different characteristics. Lift adopts near-bank processing units with a push-based dataflow to process vertices with strong re-usability. A dedicated unit is introduced to reduce massive data movement caused by high-degree vertices. At the software level, Lift adopts a hybrid mapping to further exploit data locality and fully utilize the hybrid computing resources. The experimental results show that the proposed scheme can significantly reduce data movement and energy consumption compared with representative schemes.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119968", "vector": [], "sparse_vector": [], "title": "Mixed-cell-height Placement with Minimum-Implant-Area and Drain-to-Drain Abutment Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In modern circuits, mixed-cell-height standard cells have been prevailing to meet various requirements and achieve better trade-offs among timing, power, and routability. Besides, the constraints of the minimum-implant-area (MIA) and drain-to-drain-abutment (DDA) arise as emerging challenges at advanced technology nodes. In this paper, we present an algorithm to address the mixed-cell-height placement problem with MIA and DDA constraints in three major stages: (1) post-global placement, (2) legalization, and (3) detailed placement. In the post-global stage, we first align mixed-cell-height standard cells to the desired rows by conjugate gradient method with dynamic step size and then reorder them by the shortest path algorithm to distribute the source nodes evenly. In the legalization stage, we propose a two-step combination algorithm to cluster cells and repack the clusters to minimize the wirelength, after which we presented a queue based method to address the inter-row MIA violations. In the detailed placement, an MIA-aware DDA reduction algorithm is adopted after multi-region partitioning to eliminate DDA violations without introducing MIA violations. Experimental results show that our algorithm can resolve all MIA and almost all DDA violations with a 13% reduction in displacement, a 4% reduction in HPWL, and 25% less runtime compared with the state-of-the-art work.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247857"}, {"primary_key": "1119969", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning-based Analog Circuit Optimizer using gm/ID for Sizing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Designing analog circuits incurs high time costs because designers must consider numerous design variables or trade-off relationships of circuit performance based on a lot of knowledge and experience. To reduce design time, various machine learning methods have been used to optimize analog circuits by learning the correlation between the device size and the circuit performance. However, it is difficult to train the correlation because of its high non-linearity and wide design space. In this paper, this study proposes a new framework to optimize analog circuit designs by combining reinforcement learning (RL) and the sensitivity analysis with g m /I D sizing, which is more intuitive for interpreting circuit performance. Furthermore, the universal value function approximator (UVFA), previously proposed in RL, is modified more simply to make it easier to find the target design. Additionally, the dataset is rearranged and sampled by the criteria that are established based on the principle of circuit operation, which helps to orient the agent to learn the circuit operation. Using the proposed methods, we optimize three types of differential amplifiers with common mode feedback circuits and obtain the best circuit design. Compared to baseline, we find the optimal point using modified UVFA, and moreover, reduce the number of iterations by 42.2%, 39.5%, and 37.5%, respectively, for the three test cases.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247739"}, {"primary_key": "1119970", "vector": [], "sparse_vector": [], "title": "RQ-DNN: Reliable Quantization for Fault-tolerant Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JaeHwa <PERSON>on", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) are deployed in many real-time and safety-critical applications such as autonomous vehicles and medical diagnosis. In such applications, quantization is used to compress the model for storage and computation reduction. However, recent research has shown that faults in memory can cause a significant drop in DNN accuracy and conventional quantization methods focus only on model compression. This paper proposes a novel method that performs model quantization while remarkably improving the fault-tolerance of the model. It can be incorporated with other hardware approaches such as Error Correcting Code to further improve fault-tolerance. The proposed method reduces possible error patterns that negatively impact classification accuracy by modifying weight distributions and applying a novel masking-based clipping function. Experimental results show that the proposed method enhances the fault-tolerance of the quantized DNN, which can tolerate 1803× higher bit error rates than the conventional method.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247670"}, {"primary_key": "1119971", "vector": [], "sparse_vector": [], "title": "AVX Timing Side-Channel Attacks against Address Space Layout Randomization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seungwon Shin"], "summary": "Modern x86 processors support an AVX instruction set to boost performance. However, this extension may cause security issues. We discovered that there are vulnerable properties in implementing masked load/store instructions. Based on this, we present a novel AVX timing side-channel attack that can defeat address space layout randomization. We demonstrate the significance of our attack by showing User and Kernel ASLR breaks on the recent Intel and AMD processors in various environments, including cloud computing systems, an SGX enclave (a fine-grained ASLR break), and major operating systems. We further demonstrate that our attack can be used to infer user behavior, such as Bluetooth events and mouse movements. We highlight that stronger isolation or more fine-grained randomization should be adopted to successfully mitigate our presented attacks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247741"}, {"primary_key": "1119972", "vector": [], "sparse_vector": [], "title": "ALMOST: Adversarial Learning to Mitigate Oracle-less ML Attacks via Synthesis Tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Oracle-less machine learning (ML) attacks have broken various logic locking schemes. Regular synthesis, which is tailored for area-power-delay optimization, yields netlists where key-gate localities are vulnerable to learning. Thus, we call for security-aware logic synthesis. We propose ALMOST, a framework for adversarial learning to mitigate oracle-less ML attacks via synthesis tuning. ALMOST uses a simulated-annealing-based synthesis recipe generator, employing adversarially trained models that can predict state-of-the-art attacks' accuracies over wide ranges of recipes and key-gate localities. Experiments on ISCAS benchmarks confirm the attacks' accuracies drops to around 50% for ALMOST-synthesized circuits, all while not undermining design optimization.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247921"}, {"primary_key": "1119973", "vector": [], "sparse_vector": [], "title": "SimLL: Similarity-Based Logic Locking Against Machine Learning Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logic locking is a promising technique for protecting integrated circuit designs while outsourcing their fabrication. Recently, graph neural network (GNN)-based link prediction attacks have been developed which can successfully break all the multiplexer-based locking techniques that were expected to be learning-resilient. We present SimLL, a novel similarity-based locking technique which locks a design using multiplexers and shows robustness against the existing structure-exploiting oracle-less learning-based attacks. Aiming to confuse the machine learning (ML) models, SimLL introduces key-controlled multiplexers between logic gates or wires that exhibit high levels of topological and functional similarity. Empirical results show that SimLL can degrade the accuracy of existing ML-based attacks to approximately 50%, resulting in a negligible advantage over random guessing.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119974", "vector": [], "sparse_vector": [], "title": "Any-Angle Routing for Redistribution Layers in 2.5D IC Packages.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Redistribution layers (RDLs) are widely applied for signal transmissions in advanced packages. Traditional redistribution layer (RDL) routers use only 90- and 135-degree turns for routing. With technological advances, routing in RDLs can be any obtuse angle, leading to larger routing solution spaces and shorter total wirelength. This paper proposes the first any-angle routing algorithm in the literature for multiple RDLs. We first give a novel global routing algorithm with accurate routing resource estimation. A multi-net access point adjustment method is then proposed based on dynamic programming and our partial net separation scheme. Finally, we develop an efficient tile routing algorithm to obtain valid routes with fixed access points. Experimental results show that our algorithm can achieve a 15.7% shorter wirelength compared with a traditional RDL router.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1119975", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Scaling Up Quantum Compilation - Challenges and Opportunities.", "authors": ["<PERSON>"], "summary": "The past decade witnessed exciting advances in quantum computing (QC) platforms. For example, superconducting qubit systems of over a hundred qubits have been developed and are now commercially available [1] , [2] . Trapped ion systems in long chains or quantum charge-coupled device (CCD) [3] architectures have also been developed, reaching very low gate errors [4] , [5] . Recently, neutral atoms trapped in arrays of optical tweezers emerged as a very promising experimental platform with hundreds of qubits at room temperature for programmable quantum simulations and quantum computation [6] , [7] . Figure 1 illustrates the rapid growth of the capacity of these QC platforms in recent years. With IBM's recent announcement of the plan for building a 100,000-qubit quantum processing unit (QPU) [8] , there is a strong need to scale up the capacity and performance of quantum compilation tools to match the rapid advances of quantum hardware.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247677"}, {"primary_key": "1119976", "vector": [], "sparse_vector": [], "title": "Automating Constraint-Aware Datapath Optimization using E-Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Numerical hardware design requires aggressive optimization, where designers exploit branch constraints, creating optimization opportunities that are valid only on a sub-domain of input space. We developed an RTL optimization tool that automatically learns the consequences of conditional branches and exploits that knowledge to enable deep optimization. The tool deploys custom built program analysis based on abstract interpretation theory, which when combined with a data-structure known as an e-graph simplifies complex reasoning about program properties. Our tool fully-automatically discovers known floating-point architectures from the computer arithmetic literature and out-performs baseline EDA tools, generating up to 33% faster and 41% smaller circuits.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247797"}, {"primary_key": "1119977", "vector": [], "sparse_vector": [], "title": "A Fast Secure Deletion Strategy for High-Density Flash Memory through WOM-v Codes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "High bit-density NAND flash memory, such as quadruple-level cell (QLC) flash, has been widely adopted in emerging storage systems. As voltage-based write-once-memory (WOM-v) codes reduce the erase count of a flash block before storing new data, WOM-v codes are proven feasible to extend the lifetime of high-density flash memory significantly. To ensure data security, secure deletion is widely employed in flash-based storage system.In this paper, we propose FSD, a fast secure deletion strategy for high-density flash memory that cooperates secure deletion with WOM-v codes. FSD classifies request data into secure, and unsecure data by considering data privacy. When secure data deletion command issues to the storage device, FSD cooperates unsecure data program operation to cover secure data. The results show that FSD improves the I/O performance of storage system by 70.37% over the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247911"}, {"primary_key": "1119978", "vector": [], "sparse_vector": [], "title": "Efficient Transformer Inference with Statically Structured Sparse Attention.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Self-attention matrices of Transformers are often highly sparse because the relevant context of each token is typically limited to just a few other tokens in the sequence. To reduce the computational burden of self-attention on Transformer inference, we propose static, structured, sparse attention masks that split attention matrices into dense regions, skipping computations outside these regions while reducing computations inside these regions. To support the proposed mask structure, we design an entropy-aware finetuning algorithm to naturally encourage attention sparsity while maximizing task accuracy. Furthermore, we extend a typical dense deep learning accelerator to efficiently exploit our structured sparsity pattern. Compared to a dense baseline, we achieve 56.6% reduction in energy consumption, 58.9% performance improvement with <1% accuracy loss and 2.6% area overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247993"}, {"primary_key": "1119979", "vector": [], "sparse_vector": [], "title": "Discerning Limitations of GNN-based Attacks on Logic Locking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning (ML)-based attacks have revealed the possibility of utilizing neural networks to break locked circuits without needing functional chips (Oracle). Among ML approaches, GNN (graph neural networks)-based attacks are the most potent tools that attackers can employ as they exploit graph structures inherent to a circuit's netlist. Although promising, in this paper, we reveal that GNNs have some impediments in attacking locked circuits. We investigate the limits of the state-of-the-art GNN-based attacks against logic locking and show that we can drastically decrease the accuracy of these attacks by utilizing these limitations in the locking process.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247847"}, {"primary_key": "1119980", "vector": [], "sparse_vector": [], "title": "Formal Verification of Restoring Dividers made Fast and Simple.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The paper describes a formal verification method for hardware implementation of restoring divider circuits. The method is based on setting select signals to predefined constants to reduce the design to easily verifiable circuit components, followed by their verification using standard equivalence checking and SAT. It is then concluded by a global proof that the composition of those components indeed implements a divider. In contrast to previous approaches, the verification is done on a functional level without any reverse engineering of the internal structure. The results show significant improvement in verification time compared to other methods. The proposed approach can also be used in debugging by localizing the source of a bug. This feature is currently not available in the existing verification tools and will be a subject of future work.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247789"}, {"primary_key": "1119981", "vector": [], "sparse_vector": [], "title": "HTVM: Efficient Neural Network Deployment On Heterogeneous TinyML Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Optimal deployment of deep neural networks (DNNs) on state-of-the-art Systems-on-Chips (SoCs) is crucial for tiny machine learning (TinyML) at the edge. The complexity of these SoCs makes deployment non-trivial, as they typically contain multiple heterogeneous compute cores with limited, programmer-managed memory to optimize latency and energy efficiency. We propose HTVM – a compiler that merges TVM with DORY to maximize the utilization of heterogeneous accelerators and minimize data movements. HTVM allows deploying the MLPerf™ Tiny suite on DIANA, an SoC with a RISC-V CPU, and digital and analog compute-in-memory AI accelerators, at 120x improved performance over plain TVM deployment.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247664"}, {"primary_key": "1119982", "vector": [], "sparse_vector": [], "title": "PIMA-LPN: Processing-in-memory Acceleration for Efficient LPN-based Post-Quantum Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning parity with noise (LPN) is under intensive research in building advanced cryptography suites and protocols. However, in LPN-based cryptography, the transmission of the large matrices between the memory and the processor units generally incurs a significant latency overhead. In this work, we propose PIMA-LPN, a processing-in-memory (PIM) accelerator for LPN cryptography. Specifically, our PIM architecture can carry out the entire computations of LPN in memory. In this experiment, we demonstrate that PIMA-LPN can be 20.86× ~ 216.8× faster than existing CPU and FPGA implementations of LPN cryptography. Furthermore, we show that using PIMA-LPN, LPN cryptography can achieve similar computational efficiency compared to the post-quantum cryptography standard (i.e., CRYSTALS-Kyber) with 15.4x fewer memory units.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247891"}, {"primary_key": "1119983", "vector": [], "sparse_vector": [], "title": "HAIMA: A Hybrid SRAM and DRAM Accelerator-in-Memory Architecture for Transformer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Through the attention mechanism, Transformer-based large-scale deep neural networks (LSDNNs) have demonstrated remarkable achievements in artificial intelligence applications such as natural language processing and computer vision. The matrix-matrix multiplication operation (MMMO) in Transformer makes data movement dominate the inference overhead over computation. A solution for efficient data movement during Transformer inference is to embed arithmetic logic units (ALUs) into the memory array, hence an accelerator-in-memory architecture (AIMA). Existing work along this direction has not considered the heterogeneity of parallelism and resource requirements among Transformer layers. This increases the inference latency and lowers the resource utilization, which is critical for the embedded systems domain. To this end, we propose HAIMA, a hybrid AIMA and the parallel dataflow for Transformer, which exploit the cooperation between SRAM and DRAM to accelerate different MMMOs. Compared to the state-of-the-art Newton and TransPIM, our proposed hardware-software co-design achieves 1.4x-1.5x speedup, and solves the problem of resource under-utilization when DRAM-based AIMA performs the light-weight MMMOs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247913"}, {"primary_key": "1119984", "vector": [], "sparse_vector": [], "title": "A Model-Specific End-to-End Design Methodology for Resource-Constrained TinyML Hardware.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Fengyun Yan", "<PERSON><PERSON><PERSON>", "<PERSON>", "Le Ye", "<PERSON><PERSON>"], "summary": "Tiny machine learning (TinyML) becomes appealing as it enables machine learning on resource-constrained devices with ultra low energy and small form factor. In this paper, a model-specific end-to-end design methodology is presented for TinyML hardware design. First, we introduce an end-to-end system evaluation method using Roofline models, which considering both AI and other general-purpose computing to guide the architecture design choices. Second, to improve the efficiency of AI computation, we develop an enhanced design space exploration framework, TinyScale, to enable optimal low-voltage operation for energy-efficient TinyML. Finally, we present a use case driven design selection method to search the optimal hardware design across a set of application use cases. Our model-specific design methodology is evaluated on both TSMC 22nm and 55nm technology for MLPerf Tiny benchmark and a keyword spotting (KWS) SoC design. With the help of our end-to-end design methodology, an optimal TinyML hardware can be automatically explored with significant energy and EDP improvements for a diverse of TinyML use cases.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247791"}, {"primary_key": "1119985", "vector": [], "sparse_vector": [], "title": "Accelerating DNN Inference with Heterogeneous Multi-DPU Engines.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Deep Learning Processor (DPU) programmable engine released by the official Xilinx Vitis AI toolchain has become one of the commercial off-the-shelf (COTS) solutions for Convolutional Neural Networks (CNNs) inference on Xilinx FPGAs. While modern FPGA devices generally have enough hardware resources to accommodate multi-DPUs simultaneously, the Xilinx toolchain currently only supports the deployment of multiple homogeneous DPUs engines that running independent inference tasks (task-level parallelism). In this work, we demonstrate that deployment of multiple heterogeneous DPU engines makes better resource efficiency for a given FPGA device. Moreover, we show that pipelined execution of a CNN inference task over heterogeneous multi-DPU engines may further improve overall inference throughput with carefully designed CNN layers-to-DPU mapping and scheduling. Finally, for a given CNN model and an FPGA device, we propose a comprehensive framework that automatically determines the optimal heterogeneous DPU deployment, and adaptively chooses the execution scheme between task-level and pipelined parallelism. Compared with the state-of-the-art solution with homogeneous multi-DPU engines and network-level parallelism, the proposed framework shows an average improvement of 13% (up-to 19%) and 6.6% (up-to 10%) on the Xilinx Zynq UltraScale+ MPSoC ZCU104 and ZCU102 platforms, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247793"}, {"primary_key": "1119986", "vector": [], "sparse_vector": [], "title": "Lightning Talk: The New Era of Computational Cognitive Intelligence.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The triple whammy of variability in platforms (e.g., process variability), applications (e.g., dynamic use cases in autonomous systems), and the environment (e.g., context) renders ineffective the classical computational/algorithmic/numerical computing paradigm in dealing with the inherent runtime dynamism and uncertainty faced by emerging systems. We posit that this requires a fundamental change from classical \"static\" computing to a new era that deploys a computational cognitive intelligence (CCI) paradigm that is able to learn and evolve at runtime. The CCI paradigm empowers systems to be adaptable and evolvable by exploiting biologically-inspired cognitive intelligence principles.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247865"}, {"primary_key": "1119987", "vector": [], "sparse_vector": [], "title": "Lightning Talk 6: Bringing Together Foundation Models and Edge Devices.", "authors": ["<PERSON>", "Yung-<PERSON><PERSON><PERSON> Lu"], "summary": "Deep learning models have been widely used in natural language processing and computer vision. These models require heavy computation, large memory, and massive amounts of training data. Deep learning models may be deployed on edge devices when transferring data to cloud is infeasible or undesirable. Running these models on edge devices require significant improvement in the efficiency by reducing the models' resource demands. Existing methods to improve efficiency often require new architectures and retraining. The recent trend in machine learning is to create general-purpose models (called foundation models). These pre-trained models can be repurposed for different applications. This paper reviews the methods for improving efficiency of machine learning models, the rise of foundation models, challenges and possible solutions improving efficiency of pre-trained models. Future solutions for better efficiency should focus on improving existing trained models with no or limited training.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247694"}, {"primary_key": "1119988", "vector": [], "sparse_vector": [], "title": "When Monte-Carlo Dropout Meets Multi-Exit: Optimizing Bayesian Neural Networks on FPGA.", "authors": ["Hongxiang Fan", "<PERSON>", "<PERSON>", "Zhiqiang Que", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bayesian Neural Networks (BayesNNs) have demonstrated their capability of providing calibrated prediction for safety-critical applications such as medical imaging and autonomous driving. However, the high algorithmic complexity and the poor hardware performance of BayesNNs hinder their deployment in real-life applications. To bridge this gap, this paper proposes a novel multi-exit Monte-Carlo Dropout (MCD)-based BayesNN that achieves well-calibrated predictions with low algorithmic complexity. To further reduce the barrier to adopting BayesNNs, we propose a transformation framework that can generate FPGA-based accelerators for multi-exit MCD-based BayesNNs. Several novel optimization techniques are introduced to improve hardware performance. Our experiments demonstrate that our auto-generated accelerator achieves higher energy efficiency than CPU, GPU, and other state-of-the-art hardware implementations. Our code is publicly available at: https://github.com/os-hxfan/MCME_FPGA_Acc.git", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247954"}, {"primary_key": "1119989", "vector": [], "sparse_vector": [], "title": "AmgR: Algebraic Multigrid Accelerated on ReRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiaoz<PERSON> Hu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Solving systems of linear equations is a fundamental problem in scientific computing, which has been extensively researched for decades. One of the most well-known solvers is Algebraic Multigrid (AMG), which is widely used in high performance computing due to its good scalability. But currently accelerating AMG relies on the traditional von Neumann architecture of storage and computation separation, which leads to a large data transmission overhead. In this work, we propose a ReRAM-based processing-in-memory (PIM) architecture named AmgR, which overcomes the limitations of the traditional von Neumann architecture for AMG acceleration.However, accelerating AMG on ReRAM is non-trivial, because (1) AMG has many computing kernels of various types; (2) there are irregular operations that cannot be directly performed using matrix-vector multiplication suitable for ReRAM, i.e., aggregation operation; (3) ReRAM has poor write endurance, and a lot of data during AMG acceleration needs to be rewritten into ReRAM, resulting in high write cost. To address these issues, firstly, we propose a flexible architecture, which can realize each kernel of AMG and is reused by many kernels to improve resource utilization. Secondly, we propose a dedicated unit to realize the aggregation operation. Finally, we present a new mapping strategy to greatly reduce the number of data handling and writes. The experimental results show that the performance of AmgR is improved by an average of one and two orders of magnitude compared to HYPRE on the CPU and AmgX on the GPU, respectively, while the energy consumption is reduced by an average of two and three orders of magnitude.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247881"}, {"primary_key": "1119990", "vector": [], "sparse_vector": [], "title": "vPIM: Efficient Virtual Address Translation for Scalable Processing-in-Memory Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "3D-stacked memory technologies make it possible to integrate computation logic into the memory stack to reduce data movement between CPU and memory, enabling processing-in-memory (PIM). PIM systems scale in capacity and bandwidth by connecting multiple PIM stacks through a memory network. They also need to be programmable, where having virtual memory support is critical. Existing address translation schemes, however, are not optimized for a scalable PIM system. In this work, we propose VPIM, a virtual address translation scheme for scalable, multi-stack PIM systems. VPIM optimizes contention of the memory network in a PIM system and reduces translation time with pre-translation. Our evaluation shows a speedup of 4.4× and 1.7× compared to conventional radix and cuckoo hash page tables in eight memory-intensive workloads.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247745"}, {"primary_key": "1119991", "vector": [], "sparse_vector": [], "title": "Robust Tickets Can Transfer Better: Drawing More Transferable Subnetworks in Transfer Learning.", "authors": ["Yonggan Fu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transfer learning leverages feature representations of deep neural networks (DNNs) pretrained on source tasks with rich data to empower effective finetuning on downstream tasks. However, the pre-trained models are often prohibitively large for delivering generalizable representations, which limits their deployment on edge devices with constrained resources. To close this gap, we propose a new transfer learning pipeline, which leverages our finding that robust tickets can transfer better, i.e., subnetworks drawn with properly induced adversarial robustness can win better transferability over vanilla lottery ticket subnetworks. Extensive experiments and ablation studies validate that our proposed transfer learning pipeline can achieve enhanced accuracy-sparsity trade-offs across both diverse downstream tasks and sparsity patterns, further enriching the lottery ticket hypothesis.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247920"}, {"primary_key": "1119992", "vector": [], "sparse_vector": [], "title": "Invited: Algorithms and Architectures for Accelerating Long Read Sequence Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Genome sequencing is continuing to revolutionize the medical, forensics, agricultural, and biosecurity fields. The enormous amounts of data from modern sequencing technology, require significant time and cost to obtain accurate results using general-purpose computing resources. The emergence of third-generation sequencers called nanopore sequencers offers small, cheap, and portable genome sequencing capabilities. However, the bottleneck during computational analysis limits scalability and portability. This paper introduces the necessity for one, optimization of existing algorithms; two, efficient utilization of existing heterogeneous systems; and three, novel algorithms and domain-specific architectures for rapid in situ analysis of third-generation sequencing data. State-of-the-art examples are described and future challenges are outlined.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247772"}, {"primary_key": "1119993", "vector": [], "sparse_vector": [], "title": "RoSE: Robust Analog Circuit Parameter Optimization with Sampling-Efficient Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "Weidong Cao", "<PERSON><PERSON>"], "summary": "Design automation of analog circuits has been a long-standing challenge in the integrated circuit field. Recently, multiple methods based on learning or optimization have demonstrated great promise in automating device sizing for analog circuits. However, they often ignore the strong susceptibility of analog circuits to process, voltage, and temperature (PVT) variations or suffer from low sampling efficiency to train algorithms. To address these critical limitations, this paper proposes RoSE, the first Robust analog circuit parameter optimization framework with high Sampling Efficience by synergistically combining Bayesian Optimization (BO) and reinforcement learning (RL). Its core is to use the fast convergence of BO to find an optimized starting point for the backbone RL agent to notably improve its sampling efficiency during the learning process. With this pre-optimization, we further leverage the RL's superior optimization ability to achieve robust device sizing by incorporating sufficient features of PVT variations into the representation learning loop. Experimental results of our proposed method on exemplary circuits show 3.25×∼16× improvement of sampling efficiency and 6.8× ∼ 24× improvement of figure-of-merit (FoM, defined with design efficiency and design accuracy) as compared to prior methods.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247991"}, {"primary_key": "1119994", "vector": [], "sparse_vector": [], "title": "Energy-Efficient On-Chip Training for Customized Home-based Rehabilitation Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>z<PERSON> An", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Rehabilitation is an essential process for patients suffering from motor disorders. It is generally performed by experts in a clinical environment. Home-based rehabilitation systems allow patients to perform rehabilitation without going to clinics, thus, reducing the commute and healthcare costs. Human joint estimation allows visualization of body movements required for rehabilitation. However, the estimations can be inaccurate if they are not customized for the specific patient. Therefore, we propose a personalized rehabilitation system customized to new patients utilizing energy-efficient on-chip training with in-memory acceleration. Experiments show that it customizes to new patients successfully with an average 28.01% lower error and provides energy-efficient estimates of human joint coordinates with 611.1× lower inference energy and 14.0× faster training.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247777"}, {"primary_key": "1119995", "vector": [], "sparse_vector": [], "title": "Power Side-Channel Vulnerability Assessment of Lightweight Cryptographic Scheme, XOODYAK.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work presents a power side-channel analysis (SCA) of a lightweight cryptography (LWC) algorithm, XOODYAK, implemented on an FPGA. First, we perform generic leakage detection tests for two phases of authenticated encryption with associated data (AEAD) mode, namely INITIALIZE, and ABSORB. Second, we develop novel hypothetical attack models for correlation power analysis (CPA) and demonstrate a success rate (SR) of 92%/82% and minimum-traces-to-disclosure (MTD)=13K/38K on the INITIALIZE/ABSORB phases, respectively. Third, we evaluate ABSORB against Profiled SCA using convolutional neural network (CNN), and achieve SR=96%/64% and MTD=2K/16K on the test set for the same/different keys used for training, respectively. Finally, we suggest low-overhead countermeasures to protect against these SCA attacks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247916"}, {"primary_key": "1119996", "vector": [], "sparse_vector": [], "title": "Condense: A Framework for Device and Frequency Adaptive Neural Network Models on the Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zhenglun Kong", "Minghai Qin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the popularity of battery-powered edge computing, an important yet under-explored problem is the supporting of DNNs for diverse edge devices. On the one hand, different edge platforms have various runtime requirements and computation/memory capabilities. Deploying the same DNN model is unsatisfiable, while designing a specialized DNN for each platform is prohibitively expensive. On the other hand, for a single edge device, DVFS is leveraged to prolong the battery, incurring significant inference speed variation for the same DNN and consequently poor user experience. To tackle this, we propose Condense, a framework providing a single adaptive model that can be reconfigured (switch to various sub-networks with different computations/parameters) instantly for diverse devices and execution frequencies without any retraining. Experiments demonstrate that Condense can simultaneously provide vast high-accuracy sub-networks with different computations and parameters corresponding to various sparsity ratios to support diverse edge devices with different runtime requirements, and reduce the speed variation under varying frequencies on each device, with a memory cost of only one set of weights.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247713"}, {"primary_key": "1119997", "vector": [], "sparse_vector": [], "title": "HammerDodger: A Lightweight Defense Framework against RowHammer Attack on DNNs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "RowHammer attacks have become a serious security problem on deep neural networks (DNNs). Some carefully induced bit-flips degrade the prediction accuracy of DNN models to random guesses. This work proposes a lightweight defense framework that detects and mitigates adversarial bit-flip attacks. We employ a dynamic channel-shuffling obfuscation scheme to present moving targets to the attack, and develop a logits-based model integrity monitor with negligible performance loss. The parameters and architecture of DNN models remain unchanged, which ensures lightweight deployment and makes the framework compatible with commodity models. We demonstrate that our framework can protect various DNN models against RowHammer attacks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247671"}, {"primary_key": "1119998", "vector": [], "sparse_vector": [], "title": "Smart Detection of Obfuscated Thermal Covert Channel Attacks in Many-core Processors.", "authors": ["<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>dr", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In thermal covert channel (TCC) attacks, malicious applications seek to leak private information in a stealthy and hard-to-detect manner. State-of-the-art approaches for TCC detection employ the Discrete Fourier Transform (DFT) combined with heuristics to identify possible channels. However, as we demonstrate in this paper, these approaches are limited when detecting short-duration attacks, where an attacker intentionally halts the transmission for a time interval to avoid the detection. In order to overcome this limitation of the state-of-the-art solutions, we propose the first detection method for short-duration TCC attacks. Our solution, Dotecca, is a machine learning-based technique that employs short windows of time-domain measurements instead of the DFT to detect TCCs. To evaluate our solution, we introduce a new obfuscated short-duration attack that disguises as a regular application from the perspective of a DFT spectrum. Our experiments show that the new obfuscated attack is able to remain undetected even under advanced DFT-based state-of-the-art detection approaches, reducing their detection accuracy to about 18 %. In contrast, our smart detection approach is able to detect state-of-the-art and new obfuscated attacks with an accuracy of 99 %. Moreover, our solution reduces the overhead of the DFT-based state-of-the-art solution by more than 14 ×.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247844"}, {"primary_key": "1119999", "vector": [], "sparse_vector": [], "title": "Chiplets: How Small is too Small?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As chiplet systems increase in popularity, it is important to revisit the tradeoffs for converting a monolithic design to a chiplet system. Chip yield, reusability, performance binning, and floorplanning push us toward smaller chiplets. Meanwhile, inter-chiplet interconnect and assembly overheads push us toward larger chips both in terms of power and cost. This work explores the impacts of these considerations on the minimum chiplet size that makes sense. We examine the case of a large design that could be built as a single monolithic system on chip (SoC) or as a system of chiplets and show that optimal chiplet size depends on a wide range of parameters. Our analysis indicates that the smallest chiplet sizes that are viable cost-wise depends both on technology node and on type of logic. The optimal point appears to be 50-150mm 2 in 40nm and 40-80mm 2 in 7nm for microprocessor type logic. For random logic, the optimal point increases beyond 200mm 2 in both cases. This makes the case for chipletization weaker in all but the largest SoCs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247947"}, {"primary_key": "1120000", "vector": [], "sparse_vector": [], "title": "Lightweight Structural Choices Operator for Technology Mapping.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Technology mapping quality heavily depends on the subject graph structure. To overcome structural biases, operators construct choice nodes to enable mappings with improved node and level counts. Nevertheless, state-of-the-art structural choice operators scale poorly with graph size.We present the lightweight structural choices (LCH) operator that incorporates equivalencies by processing only subparts of the graph. We propose multiple heuristics that rely on specific node extraction orders and subpart sizes to extract non-overlapping components. Compared to state-of-the-art methods on EPFL circuits, LCH is 2.35x faster enduring a small sacrifice in node count (3%) and level reduction (2%).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247838"}, {"primary_key": "1120001", "vector": [], "sparse_vector": [], "title": "VITAL: Vision Transformer Neural Networks for Accurate Smartphone Heterogeneity Resilient Indoor Localization.", "authors": ["Danish Gufran", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wi-Fi fingerprinting-based indoor localization is an emerging embedded application domain that leverages existing Wi-Fi access points (APs) in buildings to localize users with smartphones. Unfortunately, the heterogeneity of wireless transceivers across diverse smartphones carried by users has been shown to reduce the accuracy and reliability of localization algorithms. In this paper, we propose a novel framework based on vision transformer neural networks called VITAL that addresses this important challenge. Experiments indicate that VITAL can reduce the uncertainty created by smartphone heterogeneity while improving localization accuracy from 41% to 68% over the best-known prior works. We also demonstrate the generalizability of our approach and propose a data augmentation technique that can be integrated into most deep learning-based localization frameworks to improve accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247684"}, {"primary_key": "1120002", "vector": [], "sparse_vector": [], "title": "STRIVE: Enabling Choke Point Detection and Timing Error Resilience in a Low-Power Tensor Processing Unit.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rapid growth in Deep Neural Network (DNN) workloads has increased the energy footprint of the Artificial Intelligence (AI) computing realm. For optimum energy efficiency, we propose operating a DNN hardware in the Low-Power Computing (LPC) region. However, operating at LPC causes increased delay sensitivity to Process Variation (PV). Delay faults are an intriguing consequence of PV. In this paper, we demonstrate the vulnerability of DNNs to delay variations, substantially lowering the prediction accuracy. To overcome delay faults, we present STRIVE—a post-fabrication fault detection and reactive error reduction technique. We also introduce a time-borrow correction technique to ensure error-free DNN computation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247879"}, {"primary_key": "1120003", "vector": [], "sparse_vector": [], "title": "ExploreFault: Identifying Exploitable Fault Models in Block Ciphers with Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Satwik <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (JV) <PERSON><PERSON><PERSON>"], "summary": "Exploitable fault models for block ciphers are typically cipher-specific, and their identification is essential for evaluating and certifying fault attack-protected implementations. However, identifying exploitable fault models has been a complex manual process. In this work, we utilize reinforcement learning (RL) to identify exploitable fault models generically and automatically. In contrast to the several weeks/months of tedious analyses required from experts, our RL-based approach identifies exploitable fault models for protected/unprotected AES and GIFT ciphers within 12 hours. Notably, in addition to all existing fault models, we identify/discover a novel fault model for GIFT, illustrating the power and promise of our approach in exploring new attack avenues.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247953"}, {"primary_key": "1120004", "vector": [], "sparse_vector": [], "title": "General-Purpose Gate-Level Simulation with Partition-Agnostic Parallelism.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Gate-level simulation with delay annotation is a both critical and time-consuming task in the circuit design flow. It is highly nontrivial to parallelize a simulation process, especially on designs with arbitrary general-purpose sequential elements such as latches, gated clocks, and scan chains. Current works on parallelizing gate-level simulation are fundamentally incompatible with these design elements and are highly reliant on circuit partitioning to achieve the best performance. In this paper, we propose a general-purpose gate-level simulation engine with partition-agnostic parallelism. We propose a general sequential behavior encoding technique and a fast event scheduling algorithm for general-purpose simulation tasks. Experimental results have shown up to 30× speed-up over commercial simulation engines.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247907"}, {"primary_key": "1120005", "vector": [], "sparse_vector": [], "title": "How to Boost Deep Neural Networks for Computer Vision.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "As the range of neural network applications has exploded, various model compression techniques have been developed to increase the accuracy of neural networks under the resource constraints given by the hardware platform and the performance constraints required by users. In this perspective paper, the current status and future prospects of individual techniques are briefly summarized. And it presents the importance of understanding the characteristics of the hardware platform and the systematic methodology of applying these techniques harmoniously.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247892"}, {"primary_key": "1120006", "vector": [], "sparse_vector": [], "title": "Hardware Support for Durable Atomic Instructions for Persistent Parallel Programming.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Persistent memory is emerging as an attractive main memory fabric capable of hosting persistent data. However, its programmability is hampered by the lack of persistent synchronization primitives. Atomic instructions are immensely useful for higher-level synchronization (locks and barriers) and for supporting lock-free data structures, but they have no durable/persistent version. In this paper, we propose a new approach to solve the problem: durable atomic instructions (DAIs). We show that DAIs can be supported with minor hardware support (low-cost modifications to the cache coherence protocol), and simultaneously achieve high performance, scalability, and crash consistency.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247729"}, {"primary_key": "1120007", "vector": [], "sparse_vector": [], "title": "SPET: Transparent SRAM Allocation and Model Partitioning for Real-time DNN Tasks on Edge TPU.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Oh"], "summary": "Deep neural networks (DNNs) have been deployed in many safety-critical real-time embedded systems. To support DNN tasks in real-time, most previous studies focused on GPU or CPU. However, Edge TPU has not yet been studied for real-time guarantees. This paper presents a real-time DNNs framework for Edge TPU to satisfy multiple DNN inference tasks' timing requirements. The proposed framework provides 1) SRAM allocation and model partitioning techniques and 2) a MIP-based algorithm that determines the amount of SRAM and the number of segments for each task. The experiment result shows that our framework provides 79% higher schedulability than the existing Edge TPU system.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247661"}, {"primary_key": "1120008", "vector": [], "sparse_vector": [], "title": "A Convolution Neural Network Accelerator Design with Weight Mapping and Pipeline Optimization.", "authors": ["Lixia Han", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The pipeline is an efficient solution to boost performance in non-volatile memory based computing in memory (nvCIM) convolution neural network (CNN) accelerators. However, the previous works seldom focus on pipeline optimization from the perspective of the whole system, especially overlooking the effect of buffer access. In this work, we propose a high-performance NVM-based CNN accelerator with a balanced pipeline design, which takes account of both the macro computing and the buffer access. At the operator level, a matrix-based weight mapping method is proposed to reduce buffer access delay. At the macro level, decoupled access and execution design is introduced to shorten the single-layer latency. At the system level, a hybrid inter/intra-tile design is presented to balance the overall latency across CNN layers. With the collaboration among three methods, we construct a well-balanced pipeline for the nvCIM accelerator at a smaller hardware cost. Experiments show that our pipeline design can achieve 3.7х, 7.5х, and 3.5х throughput improvement for recognition of ImageNet with ResNet18, VGG19, and ResNet34 models, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120009", "vector": [], "sparse_vector": [], "title": "BLITZCRANK: Factor Graph Accelerator for Motion Planning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Factor graph is a graph representing the factorization of a probability distribution function and serves as a perfect abstraction in many autonomous machine computing stacks, such as planning, localization, tracking and control, which are challenging tasks for autonomous systems with real-time and energy constraints.In this paper, we present BLITZCRANK, an accelerator for motion planning algorithms using the abstraction of a factor graph. By formulating motion planning as a factor graph inference, we successfully reduce the scale of the problem and utilize the inherent matrix sparsity. BLITZCRANK is able to realize the user-defined optimal design by finding the optimal order of the factor graph inference. With a domain specific balancing order, BLITZCRANK achieves up to 7.4× speed up and 29.7× energy reduction compared to the software implementation on Intel CPU.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247780"}, {"primary_key": "1120010", "vector": [], "sparse_vector": [], "title": "TD-Magic: From Pictures of Timing Diagrams To Formal Specifications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce TD-Magic, the first neuro-symbolic approach for translating an image of a timing-diagram (TD) to a formal specification. We overcome the lack of labelled data for supervised learning, by first developing a synthetic data generator of labelled TDs. We then use object detection techniques to identify rising and failing edges, OCR to recognise the text, and image processing algorithms to capture synchronisation patterns. Finally, we use semantic interpretation to analyse the extracted features and generate the associated formal specification. Our experiments on industrial TDs show high translation accuracy opening the way to more sophisticated requirements-extraction algorithms from pictures.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247685"}, {"primary_key": "1120011", "vector": [], "sparse_vector": [], "title": "OpenDRC: An Efficient Open-Source Design Rule Checking Engine with Hierarchical GPU Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zheng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Design rule checking (DRC) is an essential procedure in physical verification, yet few open-source DRC tools are accessible in academia. To fill in the gap, we present OpenDRC, an open-source DRC engine that aims for extremely high efficiency. OpenDRC maintains hierarchical layouts with layer-wise bounding volume hierarchies and performs adaptive row-based partition to identify independent regions for check pruning and/or parallel processing. For common design rules, OpenDRC provides a sequential mode that runs cell-level sweeplines, and a parallel mode that launches edge-based GPU check kernels. Experiments demonstrate that OpenDRC outperforms state-of-the-art multi-threading and GPU-accelerated design rule checkers. The source code is available at https://github.com/opendrc/opendrc.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247734"}, {"primary_key": "1120012", "vector": [], "sparse_vector": [], "title": "Late Breaking Results From Hybrid Design Automation for Field-coupled Nanotechnologies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent breakthroughs in atomically precise manufacturing are paving the way for Field-coupled Nanocomputing (FCN) to become a real-world post-CMOS technology. This drives the need for efficient and scalable physical design automation methods. However, due to the problem's NP-completeness, existing solutions either generate designs of high quality, but are not scalable, or generate designs in negligible time but of poor quality. In an attempt to balance scalability and quality, we created and evaluated a hybrid approach that combines the best of established design methods and deep reinforcement learning. This paper summarizes the obtained results.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247933"}, {"primary_key": "1120013", "vector": [], "sparse_vector": [], "title": "Algorithms and Hardware for Efficient Processing of Logic-based Neural Networks.", "authors": ["Jingkai Hong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent efforts to improve the performance of neural network (NN) accelerators that meet today's application requirements have given rise to a new trend of logic-based NN inference relying on fixed-function combinational logic (FFCL). This paper presents an innovative optimization methodology for compiling and mapping NNs utilizing FFCL into a logic processor. The presented method maps FFCL blocks to a set of Boolean functions where Boolean operations in each function are mapped to high-performance, low-latency, parallelized processing elements. Graph partitioning and scheduling algorithms are presented to handle FFCL blocks that cannot straightforwardly fit the logic processor. Our experimental evaluations across several datasets and NNs demonstrate the superior performance of our framework in terms of the inference throughput compared to prior art NN accelerators. We achieve 25x higher throughput compared with the XNOR-based accelerator for VGG16 model that can be amplified 5x deploying the graph partitioning and merging algorithms.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247667"}, {"primary_key": "1120014", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Power and Performance Reconciliation - from Tradeoff to Win-Win.", "authors": ["<PERSON>"], "summary": "Power and performance are the two most important objectives for many digital IC designs. However, they often conflict with each other and the reconciliation between them is a continuous challenge. This paper presents a comprehensive overview of various techniques that can address this challenge and discusses their respective advantages and disadvantages. These techniques can also offer insights for handling other competing design objectives.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247854"}, {"primary_key": "1120015", "vector": [], "sparse_vector": [], "title": "Battle Against Fluctuating Quantum Noise: Compression-Aided Framework to Enable Robust Quantum Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, we have been witnessing the scale-up of superconducting quantum computers; however, the noise of quantum bits (qubits) is still an obstacle for real-world applications to leveraging the power of quantum computing. Although there exist error mitigation or error-aware designs for quantum applications, the inherent fluctuation of noise (a.k.a., instability) can easily collapse the performance of error-aware designs. What's worse, users can even not be aware of the performance degradation caused by the change in noise. To address both issues, in this paper we use Quantum Neural Network (QNN) as a vehicle to present a novel compression-aided framework, namely QuCAD, which will adapt a trained QNN to fluctuating quantum noise. In addition, with the historical calibration (noise) data, our framework will build a model repository offline, which will significantly reduce the optimization time in the online adaption process. Emulation results on an earthquake detection dataset show that QuCAD can achieve 14.91% accuracy gain on average in 146 days over a noise-aware training approach. For the execution on a 7-qubit IBM quantum processor, ibm-jakarta, QuCAD can consistently achieve 12.52% accuracy gain on earthquake detection.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247922"}, {"primary_key": "1120016", "vector": [], "sparse_vector": [], "title": "Efficient Non-Linear Adder for Stochastic Computing with Approximate Spatial-Temporal Sorting Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>zhen Lai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "End-to-end stochastic computing (SC) enables fault-tolerant and area-efficient neural acceleration by conducting non-linear addition, including accumulation and activation functions, in SC bitstreams. However, existing non-linear adder designs suffer from a high hardware cost, accounting for a major portion of the datapath power and area, and may also have limited computation accuracy and flexibility. In this paper, we propose an accurate yet efficient non-linear adder design. We analyze the redundancy in existing designs and propose a parameterized approximate non-linear adder design space. By systematic design space exploration, we develop non-linear adders that are significantly more efficient than existing designs with negligible computation error. We further propose a spatial-temporal architecture to improve the design flexibility and efficiency for a wide range of network sizes. To support state-of-the-art networks, e.g., ResNet18, we demonstrate that our design can reduce the datapath area by 2.16× compared with the baseline designs. Our design can also reduce the area-delay product (ADP) of the non-linear adder by 4.13× and 23.29× for large and small convolution layers in ResNet18, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247826"}, {"primary_key": "1120017", "vector": [], "sparse_vector": [], "title": "Bumblebee: A MemCache Design for Die-stacked and Off-chip Heterogeneous Memory Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Wei<PERSON> Chen", "<PERSON><PERSON><PERSON>"], "summary": "Emerging die-stacked memories can provide higher bandwidth than traditional off-chip DRAM and serve as an off-chip DRAM cache or part of OS-visible memory (POM). This paper presents Bumblebee, a new hybrid memory architecture combining the advantages of both DRAM cache and POM. The ratio of DRAM cache to POM is adjustable in real time to better exploit both temporal and spatial locality benefits for different memory access patterns. Our evaluations indicate at least 35.2% performance improvement and 10.9% ∼ 20.1% less memory dynamic energy consumption for Bumblebee over state-of-the-art designs, as well as orders of magnitude less metadata storage space.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248000"}, {"primary_key": "1120018", "vector": [], "sparse_vector": [], "title": "BWA-NIMC: Budget-based Workload Allocation for Hybrid Near/In-Memory-Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "To enable efficient computation for convolutional neural networks, in-memory-computing (IMC) is proposed to perform computation within memory. However, the non-ideality significantly degrades the accuracy of IMC. In this work, we leverage a hybrid near/in-memory-computing architecture (NIMC) that allocates sensitive weights to error-free NMC and computes remained weights with high-efficient IMC. We further propose a Budget-based Workload Allocation for NIMC (BWA-NIMC). Specifically, we consider the resource difference between NMC and IMC to effectively allocate workloads under a targeted resource budget. Simulation results show that BWA-NIMC improves the accuracy by 18.38-48.54% under limited budgets (e.g., energy and latency) compared with prior works.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247819"}, {"primary_key": "1120019", "vector": [], "sparse_vector": [], "title": "Neurogenesis Dynamics-inspired Spiking Neural Network Training Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Biologically inspired Spiking Neural Networks (SNNs) have attracted significant attention for their ability to provide extremely energy-efficient machine intelligence through event-driven operation and sparse activities. As artificial intelligence (AI) becomes ever more democratized, there is an increasing need to execute SNN models on edge devices. Existing works adopt weight pruning to reduce SNN model size and accelerate inference. However, these methods mainly focus on how to obtain a sparse model for efficient inference, rather than training efficiency. To overcome these drawbacks, in this paper, we propose a Neurogenesis Dynamics-inspired Spiking Neural Network training acceleration framework, NDSNN. Our framework is computational efficient and trains a model from scratch with dynamic sparsity without sacrificing model fidelity. Specifically, we design a new drop-and-grow strategy with decreasing number of non-zero weights, to maintain extreme high sparsity and high accuracy. We evaluate NDSNN using VGG-16 and ResNet-19 on CIFAR-10, CIFAR-100 and TinyImageNet. Experimental results show that NDSNN achieves up to 20.52% improvement in accuracy on Tiny-ImageNet using ResNet-19 (with a sparsity of 99%) as compared to other SOTA methods (e.g., Lottery Ticket Hypothesis (LTH), SET-SNN, RigL-SNN). In addition, the training cost of NDSNN is only 40.89% of the LTH training cost on ResNet-19 and 31.35% of the LTH training cost on VGG-16 on CIFAR-10.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247810"}, {"primary_key": "1120020", "vector": [], "sparse_vector": [], "title": "Optimizing Data Reuse for CGRA Mapping Using Polyhedral-based Loop Transformations.", "authors": ["<PERSON><PERSON>", "Da<PERSON> Liu"], "summary": "Coarse-Grained Reconfigurable Arrays (CGRA) can provide high energy efficiency while keeping moderate flexibility. With flexible connections, modern CGRAs are allowed to construct register chains on demand such that data reuse could be achieved. However, existing works put little effort into loop transformations for better data reuse. Therefore, this paper proposes an efficient loop transformation approach considering data reuse for the overall performance. Using reduced polyhedral formulation and Dynamical Programming (DP) based searching, loop structures could be thoroughly and efficiently explored for optimized solutions. The experimental results show that our approach can achieve 1.11-1.15 × speedup compared to the state-of-the-art approach.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248011"}, {"primary_key": "1120021", "vector": [], "sparse_vector": [], "title": "Dynamic Sparse Training via Balancing the Exploration-Exploitation Trade-off.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over-parameterization of deep neural networks (DNNs) has shown high prediction accuracy for many applications. Although effective, the large number of parameters hinders its popularity on resource-limited devices and has an outsize environmental impact. Sparse training (using a fixed number of nonzero weights in each iteration) could significantly mitigate the training costs by reducing the model size. However, existing sparse training methods mainly use either random-based or greedy-based drop-and-grow strategies, resulting in local minimal and low accuracy. In this work, to assist explainable sparse training, we propose important weights Exploitation and coverage Exploration to characterize Dynamic Sparse Training (DST-EE), and provide quantitative analysis of these two metrics. We further design an acquisition function and provide the theoretical guarantees for the proposed method and clarify its convergence property. Experimental results show that sparse models (up to 98% sparsity) obtained by our proposed method outperform the SOTA sparse training methods on a wide variety of deep learning tasks. On VGG-19 / CIFAR-100, ResNet-50 / CIFAR-10, ResNet-50 / CIFAR-100, our method has even higher accuracy than dense models. On ResNet-50 / ImageNet, the proposed method has up to 8.2% accuracy improvement compared to SOTA sparse training methods.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247716"}, {"primary_key": "1120022", "vector": [], "sparse_vector": [], "title": "INVITED: Generalizing the ISA to the ILA: A Software/Hardware Interface for Accelerator-rich Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the Instruction-Level Abstraction (ILA) as a formal software/hardware interface for accelerator-rich platforms. The ILA provides a common framework for formal functional specification of processors and accelerator behavior, verifying their implementations, and reasoning about software-hardware interactions of programs with accelerators. The ILA-MCM specification extends the ILA to enable reasoning about interactions of accelerators with other compute engines through shared memory. The 3LA compilation flow for accelerators uses the ILA model to bridge the gap between fine-grain compiler intrinsics and coarse-grain accelerator operations.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247894"}, {"primary_key": "1120023", "vector": [], "sparse_vector": [], "title": "MeG2: In-Memory Acceleration for Genome Graphs Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>", "Pengcheng Yao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Genome graphs analysis has emerged as an effective means to enable mapping DNA fragments (known as reads) to the reference genome. It replaces the traditional linear reference with a graph-based representation to augment the genetic variations and diversity information, significantly improving the quality of genotyping. The in-depth characterization of genome graphs analysis uncovers that it is bottlenecked by the irregular seed index access and the intensive alignment operation, stressing both the memory system and computing resources.Based on these observations, we propose MeG 2 , a lightweight, commodity DRAM-compliant, processing-in-memory architecture to accelerate genome graphs analysis. MeG 2 is specifically integrated with the capabilities of both near-memory processing and bitwise in-situ computation. Specifically, MeG 2 leverages the low access latency of near-memory processing with the index-centric offload mechanism to alleviate the irregular memory access in the seeding procedure, and harnesses the row-parallel capacity of in-situ computation with the distance-aware technique to exploit the intensive computational parallelism in the alignment process. Results show that MeG 2 outperforms the CPU-, GPU-, and ASIC-based genome graphs analysis solutions by 502× (30.2×), 272× (15.1× ), and 5.5× (8.3×) for short (long) reads, while reducing energy consumption by 1628× (85.6×), 1443× (77.1×), and 7.8× (11.7×), respectively. We also demonstrate that MeG 2 offers significant improvements over existing PIM-based genome sequence analysis accelerators.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247956"}, {"primary_key": "1120024", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: COPPER: Computation Obfuscation by Producing Permutations for Encoding Randomly.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Deployed embedded devices face security risks due to increased ease of physical access to the devices by unauthorized users. Capable adversaries can intercept a device to recover the data in memory, including results of performed sensitive computations. Device owners require data confidentiality on their physically insecure devices. To satisfy this goal we implement a novel method, COPPER (Computation Obfuscation by Producing Permutations for Encoding Randomly), to create data which never exists on the device digitally in plaintext format and which is subsequently used for computation. In this paper we utilize COPPER to calculate a moving average computation on encoded data. 1", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247703"}, {"primary_key": "1120025", "vector": [], "sparse_vector": [], "title": "ActiWate: Adaptive and Design-agnostic Active Watermarking for IP Ownership in Modern SoCs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Watermarking offers a viable solution to combat IP piracy and illegal re-use. However, watermarking verification techniques rely heavily on manual testing by verification engineers and ignore the possibility of having a rogue SoC design house. To automate the watermarking-based verification process and to be against wider attacks (e.g., rogue design house), this paper presents ActiWate, which conducts automatic self-verification by communicating with various peripherals within the SoC. Showing its resilience against removal and spoofing attacks, ActiWate is architectured to be an IP/SoC-agnostic watermarking and our experiments demonstrate its versatility by implementing it on multiple RISC-V SoCs with different components/peripherals.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247688"}, {"primary_key": "1120026", "vector": [], "sparse_vector": [], "title": "Sparse Hamming Graph: A Customizable Network-on-Chip Topology.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Chips with hundreds to thousands of cores require scalable networks-on-chip (NoCs). Customization of the NoC topology is necessary to reach the diverse design goals of different chips. We introduce sparse Hamming graph, a novel NoC topology with an adjustable cost-performance trade-off that is based on four NoC topology design principles we identified. To efficiently customize this topology, we develop a toolchain that leverages approximate floorplanning and link routing to deliver fast and accurate cost and performance predictions. We demonstrate how to use our methodology to achieve desired cost-performance trade-offs while outperforming established topologies in cost, performance, or both.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120027", "vector": [], "sparse_vector": [], "title": "HexaMesh: Scaling to Hundreds of Chiplets with an Optimized Chiplet Arrangement.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "2.5D integration is an important technique to tackle the growing cost of manufacturing chips in advanced technology nodes. This poses the challenge of providing high-performance inter-chiplet interconnects (ICIs). As the number of chiplets grows to tens or hundreds, it becomes infeasible to hand-optimize their arrangement in a way that maximizes the ICI performance. In this paper, we propose HexaMesh, an arrangement of chiplets that outperforms a grid arrangement both in theory (network diameter reduced by 42%; bisection bandwidth improved by 130%) and in practice (latency reduced by 19%; throughput improved by 34%). MexaMesh enables large-scale chiplet designs with high-performance ICIs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248006"}, {"primary_key": "1120028", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Bridging Neuro-Dynamics and Cognition.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Brain-inspired computing models have shown great potential to outperform today's deep learning solutions in terms of robustness and energy efficiency. Particularly, Spiking Neural Networks (SNNs) and HyperDimensional Computing (HDC) have shown promising results in enabling efficient and robust cognitive learning. Despite the success, these two brain-inspired models have different strengths. While SNN mimics the physical properties of the human brain, HDC models the brain on a more abstract and functional level. Their design philosophies demonstrate complementary patterns that motivate their combination. With the help of the classical psychological model of memory, we aim to explore the difference between Spiking neural networks and hyperdimensional computing and how they can combine to develop a more advanced cognitive learning model.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247931"}, {"primary_key": "1120029", "vector": [], "sparse_vector": [], "title": "Stochastic-HMDs: Adversarial-Resilient Hardware Malware Detectors via Undervolting.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Machine learning-based hardware malware detectors (HMDs) offer a potential game changing advantage in defending systems against malware. However, HMDs suffer from adversarial attacks, can be effectively reverse-engineered and subsequently be evaded, allowing malware to hide from detection. We address this issue by proposing novel HMDs (Stochastic-HMDs), which leverage approximate computing (AC) to harden HMDs against adversarial evasion attacks. Stochastic-HMDs introduce stochastic noise into the computations within the model to build an efficient and low-cost moving-target defense. Specifically, we use controlled undervolting, i.e., scaling the supply voltage below nominal level, to deliberately induce stochastic timing violations in the HMDs' computations during inference (detection). We show that such technique makes HMDs more resilient to adversarial attacks, especially to reverse-engineering and transferability. Our thorough empirical results substantiate that Stochastic-HMDs offer effective defense against adversarial attacks along with by-product power savings, without requiring any changes to the hardware/software nor to the HMDs' model, i.e., no retraining or fine tuning is needed. In particular, Stochastic-HMDs can detect more than 94% of the evasive malware with a negligible (i.e., < 2%) accuracy loss, along with ~15% power savings.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247980"}, {"primary_key": "1120030", "vector": [], "sparse_vector": [], "title": "PATRONoC: Parallel AXI Transport Reducing Overhead for Networks-on-Chip targeting Multi-Accelerator DNN Platforms at the Edge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nazareno Bruschi", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging deep neural network (DNN) applications require high-performance multi-core hardware acceleration with large data bursts. Classical network-on-chips (NoCs) use serial packet-based protocols suffering from significant protocol translation overheads towards the endpoints. This paper proposes PATRONoC, an open-source fully AXI-compliant NoC fabric to better address the specific needs of multi-core DNN computing platforms. Evaluation of PATRONoC in a 2D-mesh topology shows 34 % higher area efficiency compared to a state-of-the-art classical NoC at 1 GHz. PATRONoC's throughput outperforms a baseline NoC by 2-8× on uniform random traffic and provides a high aggregated throughput of up to 350 GiB/s on synthetic and DNN workload traffic.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247800"}, {"primary_key": "1120031", "vector": [], "sparse_vector": [], "title": "Quixote: Improving Fidelity of Quantum Program by Independent Execution of Controlled Gates.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Won Woo Ro"], "summary": "NISQ (noisy intermediate-scale quantum) computers are vulnerable to errors, which limit the size of verifiable quantum circuits. For large quantum circuits, it is more difficult to obtain reliable results due to errors. A circuit partitioning approach can improve fidelity by separating and reducing the size of circuits processed at once in NISQ devices. In this paper, we propose Quixote (quantum independent execution architecture), that can execute quantum circuits independently as subcircuits to improve the fidelity of NISQ program results. We present methods for decomposing controlled gates into independent subcircuits and additional techniques for reducing circuit costs through identical gate transformation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247757"}, {"primary_key": "1120032", "vector": [], "sparse_vector": [], "title": "PIE-DRAM: Postponing IECC to Enhance DRAM performance with access table.", "authors": ["JaeHwa <PERSON>on", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel memory architecture, PIE-DRAM, to mitigate the performance overhead caused by IECC. Unlike conventional IECC architectures, the proposed method separates IECC from data path to enable independent IECC operations from memory read or write access. Based on recent memory access histories, memory controller selectively determines the usage of IECC to alleviate IECC overhead, thereby the proposed architecture enhances the memory performance. Experimental results show that, from memory intensive workloads, 6% IPC (Instructions Per Cycle) improvement is achieved solely by applying the proposed DRAM architecture utilizing the locality and modified RMW.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247841"}, {"primary_key": "1120033", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Trinity - Assured Neuro-symbolic Model Inspired by Hierarchical Predictive Coding.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper describes the core concepts and challenges in developing Trinity - a high-assurance neuro-symbolic approach to trustworthy and resilient machine learning for applications in open-world, contested, and rapidly-evolving environments. The two central concepts in Trinity are a neuro-symbolic factored world model that identifies entities, activities, and complex events, and the notion of surprise against this world model that is used for self-adaptation and learning, as well as runtime assurance. The world model is not derived purely as a bottom-up inference from sensors treating each observation as independent uncorrelated input; instead, we iteratively interleave bottom-up inference (conditioned on context) with top-down predictions and context identification using a three-layered hierarchical predictive processing (HPP) stack. Thus, the neuro-symbolic inference in Trinity is bidirectional – learning-based bottom-up pull that is uncertainty-driven and reasoning-based symbolic top-down push that is decision-driven. The progressively symbolic higher layers capture a larger context than the bottom layers finally culminating in the highest layer implemented using large language models. Any surprise arising from the mismatch between the top-down prediction and the bottom-up inference is used for the continual adaptation of <PERSON>. The inference in Trinity produces a factored temporal world model as the result of perception. The predictions are accompanied by a quantitative measure of surprise from the 3-layered HPP stack. This surprise corresponds to the confidence of the model in its current inference. The continuous monitoring and adaptation accompanied by risk analysis make <PERSON> robust to semantic adversarial perturbations and more efficiently generalizable to novelties. The hierarchical nature of Trinity also enables adaptation of the architecture to the available compute resources.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247803"}, {"primary_key": "1120034", "vector": [], "sparse_vector": [], "title": "Lightning Talk: All Routes to Timing Closure.", "authors": ["<PERSON><PERSON>"], "summary": "Timing analysis and optimization is essential for each stage throughout the entire design flow to achieve timing closure. This talk provides a retrospective and prospective study to highlight two categories of emerging timing challenges: 1) analysis efficiency and scalability, and 2) advanced process effect on timing. We survey recent advances for handling these challenges and provide future research directions in timing.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247801"}, {"primary_key": "1120035", "vector": [], "sparse_vector": [], "title": "Partition Based Differential Testing for Finding Embedded Code Generation Bugs in Simulink.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Engineers frequently generate embedded code from Simulink models for control applications. However, target applications using the code could behave unexpectedly, due to the bugs in code generation. In this study, we propose MOPART, the first model partition based differential testing method for code generation testing in Simulink. MOPART uses multiple-way network partitioning to generate diverse bug-triggering Simulink models to thoroughly exercise the code generation process. MOPART then finds bugs by analyzing the outputs of these Simulink models with differential testing. Experiments show that MOPART significantly outperforms existing approaches, which finds 11 confirmed code generation bugs in only two weeks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247877"}, {"primary_key": "1120036", "vector": [], "sparse_vector": [], "title": "BlueFace: Integrating an Accelerator into the Core&apos;s Pipeline through Algorithm-Interface Co-Design for Real-Time SoCs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In modern real-time heterogeneous System-on-Chips, ensuring real-time performance is increasingly important. However, with ever-increasing hardware and architectural complexity, satisfying such timing requirements becomes very challenging due to both hardware heterogeneity and the complicated access paths induced by the on-chip accelerators. In this paper, inspired by an interesting observation from accelerable real-time task scheduling, we propose a new core-accelerator interface, BlueFace, which is integrated into the memory access stage of the CPU pipeline, effectively avoiding the complicated HA access paths. The BlueFace design constructs a priority queue to schedule the HA operations at the hardware level, ensuring simultaneous throughput and real-time performance. The evaluation demonstrates the performance benefits and gives the overhead of BlueFace.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247830"}, {"primary_key": "1120037", "vector": [], "sparse_vector": [], "title": "ACGraph: Accelerating Streaming Graph Processing via Dependence Hierarchy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Streaming graph processing needs to timely evaluate continuous queries. Prior systems suffer from massive redundant computations due to the irregular order of processing vertices influenced by updates. To address this issue, we propose ACGraph, a novel streaming graph processing approach for monotonic graph algorithms. It maintains dependence trees during runtime, and makes affected vertices processed in a top-to-bottom order in the hierarchy of the dependence trees, thus normalizing the state propagation order and coalescing of multiple propagation to the same vertices. Experimental results show that ACGraph reduces the number of updates by 50% on average, and achieves the speedup of 1.75~7.43× over state-of-the-art systems.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247904"}, {"primary_key": "1120038", "vector": [], "sparse_vector": [], "title": "(Invited) Predictive analytics for cryogenic CMOS in future quantum computing systems.", "authors": ["<PERSON><PERSON>", "Sudipto Chakraborty"], "summary": "This paper presents predictive techniques that suggest a path to accelerated analysis and optimization of the yield of ultra-low power analog/mixed signal designs operating at cryogenic temperatures for use in future quantum computing applications. Analysis of 6σ variation is accelerated using mixture importance sampling (MixIS) techniques, with key studied specifications being compliance to spurious tone requirements at the quantum state controller output and optimization of design power consumption, both in the context of the high levels of modeling and matching uncertainties associated with cryogenic circuit design.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247978"}, {"primary_key": "1120039", "vector": [], "sparse_vector": [], "title": "Fast Adversarial Training with Dynamic Batch-level Attack Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noseong Park", "<PERSON><PERSON>"], "summary": "Despite the fact that adversarial training provides an effective protection against adversarial attacks, it suffers from a huge computational overhead. To mitigate the overhead, we propose DBAC, a fast adversarial training with dynamic batch-level attack control. Based on a prior study where attack strength should gradually grow throughout the training, we control the number of samples attacked per batch for better throughput. Additionally, we collect samples from multiple batches to form a pseudo-batch and attack them simultaneously for higher GPU utilization. We implement DBAC using PyTorch to show its superior throughput with similar robust accuracy compared to the prior art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247930"}, {"primary_key": "1120040", "vector": [], "sparse_vector": [], "title": "OmniBoost: Boosting Throughput of Heterogeneous Embedded Devices under Multi-DNN Workload.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern Deep Neural Networks (DNNs) exhibit profound efficiency and accuracy properties. This has introduced application workloads that comprise of multiple DNN applications, raising new challenges regarding workload distribution. Equipped with a diverse set of accelerators, newer embedded system present architectural heterogeneity, which current run-time controllers are unable to fully utilize. To enable high throughput in multi-DNN workloads, such a controller is ought to explore hundreds of thousands of possible solutions to exploit the underlying heterogeneity. In this paper, we propose OmniBoost, a lightweight and extensible multi-DNN manager for heterogeneous embedded devices. We leverage stochastic space exploration and we combine it with a highly accurate performance estimator to observe a ×4.6 average throughput boost compared to other state-of-the-art methods. The evaluation was performed on the HiKey970 development board. Our code is publicly available at https://github.com/AndreasKaratzas/omniboost-v1.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247989"}, {"primary_key": "1120041", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Unlocking the Potential of the Analog Domain: Exploring the Next Frontier in Hardware Security.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The rapid advancement of microelectronics has revolutionized our society, offering enhanced features, automation, and connectivity. However, alongside these remarkable advancements, we must acknowledge the emerging challenges. As designs grow increasingly complex and offshoring becomes more prevalent, the threats and vulnerabilities to our systems multiply. While digital hardware security has received considerable attention, addressing the abstracted world of 1's and 0's, analog hardware security has been comparatively overlooked. This talk serves as a call to action, urging the design automation community to recognize the significance of analog security and work towards establishing a comprehensive, resilient, and secure hardware platform for the future. This talk aims to shed light on the pivotal role of analog hardware security in establishing a more secure and resilient hardware platform, emphasizing the need to bridge the gaps in analog hardware security and showcasing the progress made by the analog community in complementing the efforts in the digital domain. Join us as we explore the advances in analog hardware security, highlighting its vital role in safeguarding our interconnected world.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247783"}, {"primary_key": "1120042", "vector": [], "sparse_vector": [], "title": "Range-Invariant Approximation of Non-Linear Operations for Efficient BERT Fine-Tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jung<PERSON><PERSON> Choi", "<PERSON><PERSON><PERSON><PERSON> Han", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a range-invariant approximation of non-linear operations for training computations of Transformer-based large language models. The proposed method decomposes the approximation into the scaling and the range-invariant resolution for LUT approximation, covering diverse data ranges of non-linear operations with drastically reduced LUT entries during task-dependent BERT fine-tuning. We demonstrate that the proposed method robustly approximates all the non-linear operations of BERT without score degradation on challenging GLUE benchmarks using only a single-entry LUT, facilitating 52% area savings in hardware implementation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247958"}, {"primary_key": "1120043", "vector": [], "sparse_vector": [], "title": "CUDA Quantum: The Platform for Integrated Quantum-Classical Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A critical challenge to making quantum computers work in practice is effectively combining them with classical computing resources. From the classical side of hybrid algorithms and integrated application workflows to decoding syndromes for quantum error correction, tightly coupled high performance classical computing will be important for many of the functions required to realize useful quantum computing. A key tool for enabling research and application development is a programming model and software toolchain which allow researchers to straightforwardly co-program classical and quantum computers and leverage the best tools available for each. NVIDIA CUDA Quantum is a single-source programming model in C++ and Python for heterogeneous quantum-classical computing. The CUDA Quantum platform provides several advantages and new capabilities that enable users to get more out of quantum processors. Here, we present CUDA Quantum and demonstrate several use cases including Variational Quantum Eigensolver (VQE) where it provides a significant (287x) performance and capability benefit over existing quantum programming.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247886"}, {"primary_key": "1120044", "vector": [], "sparse_vector": [], "title": "Profile-<PERSON>n Banded <PERSON>-<PERSON><PERSON> acceleration for Short Read Alignment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Short read alignment is a critical step in genomic pipelines that requires optimization due to the enormous input size and complexity of SmithWaterman string matching. Several optimization techniques have been examined, such as hardware acceleration, heuristics and pre-filtering. This work combines these approaches into a single powerful solution that leverages the low edit rate of reads and the principles of Banded SmithWaterman, to highlight the value of creating accelerators customized to the input accuracy requirements. We propose a dataset-specific multi-dataflow design that leverages both pre-filtering and Banded SmithWaterman to meet the demands of the datasets in both throughput and accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247740"}, {"primary_key": "1120045", "vector": [], "sparse_vector": [], "title": "Towards Efficient Convolutional Neural Network for Embedded Hardware via Multi-Dimensional Pruning.", "authors": ["Hao Kong", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Subramaniam", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose TECO, a multi-dimensional pruning framework to collaboratively prune the three dimensions (depth, width, and resolution) of convolutional neural networks (CNNs) for better execution efficiency on embedded hardware. In TECO, we first introduce a two-stage importance evaluation framework, which efficiently and comprehensively evaluates each pruning unit according to both the local importance inside each dimension and the global importance across different dimensions. Based on the evaluation framework, we present a heuristic pruning algorithm to progressively prune the three dimensions of CNNs towards the optimal trade-off between accuracy and efficiency. Experiments on multiple benchmarks validate the advantages of TECO over existing state-of-the-art (SOTA) approaches. The code and pre-trained models are available anonymously at https://github.com/ntuliuteam/Teco.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247965"}, {"primary_key": "1120046", "vector": [], "sparse_vector": [], "title": "DBPS: Dynamic Block Size and Precision Scaling for Efficient DNN Training Supported by RISC-V ISA Extensions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Se<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past decade, it has been found that deep neural networks (DNNs) perform better on visual perception and language understanding tasks as their size increases. However, this comes at the cost of high energy consumption and large memory requirement to train such large models. As the training DNNs necessitates a wide dynamic range in representing tensors, floating point formats are normally used. In this work, we utilize a block floating point (BFP) format that significantly reduces the size of tensors and the power consumption of arithmetic units. Unfortunately, prior work on BFP-based DNN training empirically selects the block size and the precision that maintain the training accuracy. To make the BFP-based training more feasible, we propose dynamic block size and precision scaling (DBPS) for highly efficient DNN training. We also present a hardware accelerator, called DBPS core, which supports the DBPS control by configuring arithmetic units with custom instructions extended in a RISC-V processor. As a result, the training time and energy consumption reduce by 67.1% and 72.0%, respectively, without hurting the training accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248013"}, {"primary_key": "1120047", "vector": [], "sparse_vector": [], "title": "A Memory-Efficient Edge Inference Accelerator with XOR-based Model Compression.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Model compression is widely adopted for edge inference of neural networks (NNs) to minimize both costly DRAM accesses and memory footprints. Recently, XOR-based model compression has demonstrated promising results to maximize compression ratio and minimize accuracy drop. However, XOR-based decompression alone produces bit errors and requires auxiliary data for error correction. To minimize model size and hence DRAM traffic, we propose an enhanced decompression algorithm and a low-cost hardware accelerator for it. Since not all errors are equal, our algorithm selects only important errors to correct with no accuracy drop. Compared with the baseline XOR compression scheme correcting all errors, the compressed model size of ResNet-18 and VGG-16 is reduced by 23% and 27% respectively. We also present a low-cost hardware implementation of on-line XOR decompression and error-correction logic built on Gemmini, an open-source systolic array accelerator, at the cost of only a 0.39% and 0.46% increase in area and power.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248005"}, {"primary_key": "1120048", "vector": [], "sparse_vector": [], "title": "Occamy: Memory-efficient GPU Compiler for DNN Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work proposes <PERSON><PERSON><PERSON>, a new memory-efficient DNN compiler that reduces the memory usage of a DNN model without affecting its accuracy. For each DNN operation, <PERSON><PERSON><PERSON> analyzes the dimensions of input and output tensors, and their liveness within the operation. Across all the operations, <PERSON><PERSON><PERSON> analyzes liveness of all the tensors, generates a memory pool after calculating the maximum required memory size, and schedules when and where to place each tensor in the memory pool. Compared to PyTorch, on an integrated embedded GPU for six DNNs, <PERSON><PERSON><PERSON> reduces the memory usage by 34.6% and achieves a geometric mean speedup of 1.25×.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247839"}, {"primary_key": "1120049", "vector": [], "sparse_vector": [], "title": "Comprehensive Integration of Hyperdimensional Computing with Deep Learning towards Neuro-Symbolic AI.", "authors": ["Hyun<PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "HD computing is a symbolic representation system which performs various learning tasks in a highly-parallelizable and binary-centric way by drawing inspiration from concepts in human long-term memory. However, the current HD computing is ineffective in extracting high-level feature information for image data. In this paper, we present a neuro-symbolic approach called NSHD, which integrates CNNs and Hyperdimensional (HD) learning techniques to provide efficient learning with state-of-the-art quality. We devise the HD training procedure, which fully integrates knowledge from the deep learning model through a distillation process with optimized computation costs due to the integration. Our experimental results show that NSHD provides high energy efficiency as compared to CNN, e.g., up to 64% with comparable accuracy, and can outperform the learning quality when more computing resources are allowed. We also show the symbolic nature of the NSHD can make the learning humnan-interpretable by exploiting the property of HD computing.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248004"}, {"primary_key": "1120050", "vector": [], "sparse_vector": [], "title": "In-Memory Neural Network Accelerator based on eDRAM Cell with Enhanced Retention Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hyunmyung Oh", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Logic compatible eDRAM cell-based computing-in-memory (CIM) neural network accelerators have been actively studied as an energy-efficient neural network computing platform thanks to their small cell size and low static power compared to SRAM. However, previous eDRAM-based CIM accelerators suffer from significant accuracy degradation caused by process, voltage, temperature (PVT) variations and short retention time. To overcome the issues, we introduce a PVT-variation tolerant capacitive coupling-based eDRAM cell that has a much longer retention time than previous works. Simulation results show that the proposed eDRAM cell has up to 50× higher retention time compared to the state-of-the-art designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247975"}, {"primary_key": "1120051", "vector": [], "sparse_vector": [], "title": "Sidekick: Near Data Processing for Clustering Enhanced by Automatic Memory Disaggregation.", "authors": ["<PERSON><PERSON><PERSON>", "Jongho Park", "<PERSON><PERSON> Ha", "<PERSON><PERSON><PERSON>", "Kyoung Park", "<PERSON><PERSON><PERSON>"], "summary": "Near Data Processing (NDP) is a promising solution for data mining/analysis techniques, which extract useful information from big data. In this paper, we propose a novel NDP-enabled memory disaggregation system called Sidekick, based on a type-2 CXL device and enhanced by an automated allocation technique for clustering algorithms. The key enabler of our migration technique is to understand clustering workflows in a unit of the program context, which is the function call stack for functions, threads, and memory allocations to drive the automated decision. The proposed technique relates the migrated computation tasks with a series of function calls and performs GA-based optimization to identify the optimal allocation scenario for a target clustering algorithm. In Scikit-learn, a popular machine learning library, we use the genetic algorithm to find the optimal memory allocation policy and the operation offloading policy using the program context. The results show that the proposed technique increases the clustering performance as compared to the case, which only uses disaggregated memory without NDP cores, by up to 92% in terms of execution time, while reducing the majority of remote CXL memory accesses.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247769"}, {"primary_key": "1120052", "vector": [], "sparse_vector": [], "title": "Victor: A Variation-resilient Approach Using Cell-Clustered Charge-domain computing for High-density High-throughput MLC CiM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Xueqing Li"], "summary": "Multi-level cell (MLC) NVM-based CiM has become a promising candidate in computing-in-memory (CiM) designs because of its non-volatility, high cell density, and improving compatibility with the CMOS process. However, most MLC CiM faces the challenges of non-ideal device limitations, including the low on/off ratio, large device-to-device variations, and read disturbances, which limit the computing accuracy, reliability, and throughput performance. This work proposes Victor, a variation-resilient approach using cell-clustered charge-domain computing for high-density and high-throughput MLC CiM. A cell-clustered-computing with local recovery unit (LRU) design methodology is proposed to improve matrix-vector-multiplication (MVM) reliability and throughput. To showcase the capability of Victor, 2b-4b MLC Resistive RAM (RRAM) is taken as an example for design and evaluation. Results show that Victor reaches 3.56x energy efficiency, 4x variation tolerance compared with the prior ratio-based MLC CiM. In addition, the throughput is improved by 3.1x with less than 1% DNN accuracy loss. Moreover, a dynamic boundary adaption approach is proposed to restore the accuracy loss of state drifting, which in return reduces the energy and latency overhead by 100x and 1.25x, respectively, compared with the conventional write-and-verify approach.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247934"}, {"primary_key": "1120053", "vector": [], "sparse_vector": [], "title": "Neuromorphic Swarm on RRAM Compute-in-Memory Processor for Solving QUBO Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Combinatorial optimization problems prevail in engineering and industry. Some are NP-hard and thus become difficult to solve on edge devices due to limited power and computing resources. Quadratic Unconstrained Binary Optimization (QUBO) problem is a valuable emerging model that can formulate numerous combinatorial problems, such as Max-Cut, traveling salesman problems, and graphic coloring. QUBO model also reconciles with two emerging computation models, quantum computing and neuromorphic computing, which can potentially boost the speed and energy efficiency in solving combinatorial problems. In this work, we design a neuromorphic QUBO solver composed of a swarm of spiking neural networks (SNN) that conduct a population-based meta-heuristic search for solutions. The proposed model can achieve about x20 40 speedup on large QUBO problems in terms of time steps compared to a traditional neural network solver. As a codesign, we evaluate the neuromorphic swarm solver on a 40nm 25mW Resistive RAM (RRAM) Compute-in-Memory (CIM) SoC with a 2.25MB RRAM-based accelerator and an embedded Cortex M3 core. The collaborative SNN swarm can fully exploit the specialty of CIM accelerator in matrix and vector multiplications. Compared to previous works, such an algorithm-hardware synergized solver exhibits advantageous speed and energy efficiency for edge devices.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247852"}, {"primary_key": "1120054", "vector": [], "sparse_vector": [], "title": "Emerging Hardware Technologies and 3D System Integration for Ubiquitous Machine Intelligence.", "authors": ["Haitong Li"], "summary": "Next-generation semiconductor hardware technologies and system integration serve as the physical foundation in the pursuit of ubiquitous machine intelligence, with unprecedented requirements in energy efficiency, performance, cost effectiveness, and security. Here, we provide an overview of emerging technologies with an emphasis on 3D system integration, and discuss on cross-layer designs for memory-centric computing in the 3D era.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247990"}, {"primary_key": "1120055", "vector": [], "sparse_vector": [], "title": "Optimizing the Performance of NDP Operations by Retrieving File Semantics in Storage.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In-storage Near-Data Processing (NDP) architectures can reduce data movement between the host and the storage device by offloading computing tasks to the storage. This encourages many studies on building NDP applications, such as recommendation systems and databases, on computational SSDs. However, in the data path of existing NDP architectures, an NDP application has to find out the address of the requested file data by calling the I/O stacks of the kernel on the host, which incurs large overhead for transferring data between the host and the computational SSD. In this paper, we present File Semantics Retriever (FSR) to optimize the data path of NDP architectures by locating and fetching the requested file data directly in the computational SSD. The key idea is to recognize the file system layout and the metadata structures in the storage with the collaboration of a user-space library and a handler in the firmware of the computational SSD. We implement a prototype of FSR and evaluate it on the Cosmos plus OpenSSD, a widely-used computational SSD platform. The experimental results show that FSR outperforms existing NDP architectures in both benchmarks and real-world NDP applications.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247986"}, {"primary_key": "1120056", "vector": [], "sparse_vector": [], "title": "FIONA: Fine-grained Incoherent Optical DNN Accelerator Search for Superior Efficiency and Robustness.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Incoherent optical DNN accelerators (OAs) are booming thanks to unparalleled performance-per-watt and excellent scalability. To boost their innovative development, a recent work revolutionarily proposed automatic OA search. However, the robustness and the acceleration performance of the generated OAs are below expectation, because the impacts of inter-tile data transfer and fabrication process & thermal variations (i.e., PTVs) on OAs were ignored. Both hinder OA design automation from being a reality. To resolve theses challenges, we develop FIONA, a novel framework for Fine-grained Incoherent Optical DNN Accelerator search towards both superior acceleration efficiency and inference robustness. Compared against 5 state-of-the-art incoherent OAs on 9 DNN benchmarks, extensive experiments and ablation studies validate the effectiveness of FIONA, achieving up to 198.01× acceleration efficiency improvement based on guaranteed robustness.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247725"}, {"primary_key": "1120057", "vector": [], "sparse_vector": [], "title": "Input-Aware Dynamic Timestep Spiking Neural Networks for Efficient In-Memory Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Spiking Neural Networks (SNNs) have recently attracted widespread research interest as an efficient alternative to traditional Artificial Neural Networks (ANNs) because of their capability to process sparse and binary spike information and avoid expensive multiplication operations. Although the efficiency of SNNs can be realized on the In-Memory Computing (IMC) architecture, we show that the energy cost and latency of SNNs scale linearly with the number of timesteps used on IMC hardware. Therefore, in order to maximize the efficiency of SNNs, we propose input-aware Dynamic Timestep SNN (DT-SNN), a novel algorithmic solution to dynamically determine the number of timesteps during inference on an input-dependent basis. By calculating the entropy of the accumulated output after each timestep, we can compare it to a predefined threshold and decide if the information processed at the current timestep is sufficient for a confident prediction. We deploy DT-SNN on an IMC architecture and show that it incurs negligible computational overhead. We demonstrate that our method only uses 1.46 average timesteps to achieve the accuracy of a 4-timestep static SNN while reducing the energy-delay-product by 80%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247869"}, {"primary_key": "1120058", "vector": [], "sparse_vector": [], "title": "Orchestrating Measurement-Based Quantum Computation over Photonic Quantum Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantum computing has rapidly evolved in recent years and has established its supremacy in many application domains. While matter-based qubit platforms such as superconducting qubits have received the most attention so far, there is a rising interest in photonic qubits lately, which show advantages in parallelism, speed, and scalability. Photonic qubits are best served by the paradigm of measurement-based quantum computation (MBQC). To deliver the promise of measurement-based photonic quantum computing (MBPQC), the photon cluster state depth and photon utilization are two of the most important metrics. However, little attention has been paid to optimizing the depth and utilization when mapping quantum circuits to the photon clusters. In this paper, we propose a compiler framework that achieves automatic and dynamic depth and utilization optimizations. Our approach consists of an MBPQC mapping mechanism that maps optimized measurement patterns on a cluster state and a cluster state pruning strategy that removes all possible redundancies without impacting the circuit functions. Experimental results on five quantum benchmark with three different qubit numbers indicate our approach achieves an average of 63.4% cluster depth reduction and 22.8% photon utilization improvements.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247944"}, {"primary_key": "1120059", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving DNN Training with Prefetched Meta-Keys on Heterogeneous Neural Network Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The embedded software may migrate the collected data to the server for DNN computation acceleration, which may compromise privacy. We propose a DNN computation framework that combines TEE and NNA to address the privacy leakage problem. We design an NNA-friendly encryption method that enables NNA to correctly compute the encrypted linear input. Facing the overhead of TEE-NNA interaction, we design a pipeline-based prefetch mechanism that can reduce the TEE interaction overhead. Experimentally, our approach proves to be compatible with a wide range of NPUs and TPUs, and improves the performance by 8-19 times over the TEE scheme.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247964"}, {"primary_key": "1120060", "vector": [], "sparse_vector": [], "title": "On EDA-Driven Learning for SAT Solving.", "authors": ["<PERSON>", "Zhengyuan Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S<PERSON>wei <PERSON>", "<PERSON><PERSON>"], "summary": "We present DeepSAT, a novel end-to-end learning framework for the Boolean satisfiability (SAT) problem. Unlike existing solutions trained on random SAT instances with relatively weak supervision, we propose applying the knowledge of the well-developed electronic design automation (EDA) field for SAT solving. Specifically, we first resort to logic synthesis algorithms to pre-process SAT instances into optimized and-inverter graphs (AIGs). By doing so, the distribution diversity among various SAT instances can be dramatically reduced, which facilitates improving the generalization capability of the learned model. Next, we regard the distribution of SAT solutions being a product of conditional Bernoulli distributions. Based on this observation, we approximate the SAT solving procedure with a conditional generative model, leveraging a novel directed acyclic graph neural network (DAGNN) with two polarity prototypes for conditional SAT modeling. To effectively train the generative model, with the help of logic simulation tools, we obtain the probabilities of nodes in the AIG being logic '1' as rich supervision. We conduct comprehensive experiments on various SAT problems. Our results show that, DeepSAT achieves significant accuracy improvements over state-of-the-art learning-based SAT solutions, especially when generalized to SAT instances that are relatively large or with diverse distributions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248001"}, {"primary_key": "1120061", "vector": [], "sparse_vector": [], "title": "EENet: Energy Efficient Neural Networks with Run-time Power Management.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning approaches, such as convolution neural networks (CNNs), have achieved tremendous success in versatile applications. However, one of the challenges to deploy the deep learning models on resource-constrained systems is its huge energy cost. As a dynamic inference approach, early exit adds exiting layers to the networks, which can terminate the inference earlier with accurate results to save energy. The current passive decision-making for energy regulation of early exit cannot adapt to ongoing inference status, varying inference workloads, and timing constraints, let alone guide the reasonable configuration of the computing platforms alongside the inference proceeds for potential energy saving. In this paper, we propose an Energy Efficient Neural Networks (EENet), which introduces a plug-in module to the state-of-the-art networks by incorporating run-time power management. Within each inference, we establish prediction of where the network will exit and adjust computing configurations (i.e., frequency and voltage) accordingly over a small timescale. Considering multiple inferences over a large timescale, we provide frequency and voltage calibration advice, given inference workloads and timing constraints. Finally, the dynamic voltage and frequency scaling (DVFS) governor configures voltage and frequency to execute the network according to the prediction and calibration. Extensive experimental results demonstrate that EENet achieves up to 63.8% energy-saving compared with classic deep learning networks and 21.5% energy-saving compared with the early exit under state-of-the-art exiting strategies, together with improved timing performance.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247701"}, {"primary_key": "1120062", "vector": [], "sparse_vector": [], "title": "RLAlloc: A Deep Reinforcement Learning-Assisted Resource Allocation Framework for Enhanced Both I/O Throughput and QoS Performance of Multi-Streamed SSDs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-streamed Solid-State Disks (SSDs) have attracted increasing adoption in modern flash storage devices. Despite their excellent promise, effective flash resource allocation is still limiting both their achievable I/O performance and practical implementation. To this end, we develop the first-of-its-kind framework dubbed RLAlloc, which for the first time demonstrates deep Reinforcement Learning-assisted resource Allocation for boosting both I/O throughput and QoS performance of multi-streamed SSDs. Extensive experiments consistently validate the effectiveness of RLAlloc, improving up to 39.9% on I/O throughput and 44.0% on QoS performance over the state-of-the-art competitors.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247988"}, {"primary_key": "1120063", "vector": [], "sparse_vector": [], "title": "APPEND: Rethinking ASIP Synthesis in the Era of AI.", "authors": ["Cangyuan Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Application-specific instruction-set processors (ASIP) has been widely used to speedup specific applications based on general-purpose processor (CPU) ISA-extension and scalar/vector units customization. However, as deep neural processing unit (NPU) becomes a dominant IP in nowadays system-on-chip (SoC) designs, the rich computational and memory resource of the NPUs integrated into advanced CPUs should also be utilized to achieve an even better application performance boost than vector/scalar compute-unit customization only. In this paper, we propose APPEND, a novel framework that tries to enrich the ASIP design methodology by taking the co-designing of both NPU and RISC-V CPU into consideration. To fully utilize and customize the resources of CPU and NPU, APPEND automatically (1) identifies the NPU-compatible kernels from the target application and partition the applications in between the NPU and RISC-V CPU core, (2) based on the application performance specification, applies the necessary hardware parameterization and customization based on the RISC-V CPU and NPU templates, and also (3) generates the extended NPU instructions to accelerate the critical and compatible kernels of the target application.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247872"}, {"primary_key": "1120064", "vector": [], "sparse_vector": [], "title": "Global Floorplanning via Semidefinite Programming.", "authors": ["<PERSON>", "Fangzhou Wang", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON> (Shawn) Blanton"], "summary": "A major task in chip design involves identifying the location and shape of each major design block/module in the layout footprint. This is commonly known as floorplanning. The first step of this task is known as global floorplanning and involves identifying a location for each module that minimizes wire length and leaves sufficient area for each module. Existing global floorplanning methods either have a non-convex problem formulation, or have trivial global solutions with no guarantee on the quality of the result. Here, we model the global floorplanning as a Semi-Definite Programming (SDP) problem with a rank constraint. We replace the rank constraint with a direction matrix and convexify the problem, whose solution is shown to be a global optimum if an appropriate direction matrix is chosen. To calculate the direction matrix, a convex iteration algorithm is used where the problem is decomposed into two SDP sub-problems. Furthermore, we introduce a series of techniques that enhance the flexibility, accuracy, and efficiency of our algorithm. Design experiments demonstrate that our proposed method reduces the average wirelength up to 20% for different benchmarks and outline aspect ratios.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247967"}, {"primary_key": "1120065", "vector": [], "sparse_vector": [], "title": "Orchestrated Scheduling and Partitioning for Improved Address Translation in GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unified Virtual Memory (UVM) is a promising feature in CPU-GPU heterogeneous systems that allows data structures to be accessed by both CPU and GPUs through unified pointers without explicit data copying. However, the delivered performance of UVM significantly relies on the efficiency of address translation. The current GPU thread block (TB) management is not aware of the translation process and heavily thrashes the per-streaming multiprocessor (SM) private Translation Look-ahead Buffers (TLBs). In this paper, we conduct a comprehensive characterization of 10 GPU benchmarks and quantify the translation reuses among the thread blocks. Our observation reveals that there exists substantial translation reuse within TBs rather than across the TBs. Moreover, the inter-TB interference significantly enlarges the intra-TB translation reuse distances. To this end, we propose a translation-aware TB scheduling and lightweight GPU L1 TLB partitioning to effectively mitigate the contention. Experimental results show that our proposed approach improves the L1 TLB hit rate, and this improvement translates to, on average, a 12.5% execution time reduction.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247943"}, {"primary_key": "1120066", "vector": [], "sparse_vector": [], "title": "Invited: Algorithm-Software-Hardware Co-Design for Deep Learning Acceleration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the development of AI techniques, it is appealing but challenging to efficiently deploy deep neural networks on resource-constrained devices. This paper presents two novel algorithm-software-hardware co-designs for improving the performance of deep neural networks. The first part introduces a hardware-efficient adaptive token pruning framework for Vision Transformers (ViTs) on FPGA, which achieves significant speedup under similar model accuracy. The second part introduces a design automation flow for crossbar-based Binary Neural Network (BNN) accelerators using the emerging technique Adiabatic Quantum-Flux-Parametron (AQFP). The proposed method significantly improves energy efficiency by combining AQFP with BNN together, which achieves over 100× better energy efficiency compared with the previous representative AQFP-based framework. Both proposed designs demonstrate superior performance compared to existing methods.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247939"}, {"primary_key": "1120067", "vector": [], "sparse_vector": [], "title": "SE3: Sequential Equivalence Checking for Non-Cycle-Accurate Design Transformations †.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In high-level design explorations, many useful optimizations transform a circuit into another with different operating cycles for a better trade-off between performance and resource usage. How to efficiently check their equivalence is critical and challenging since most existing equivalence checkers are designed for cycle-accurate circuits. This paper presents SE3, an efficient sequential equivalence checker without assumption on cycle-accuracy, latch mapping, or I/O interface of the checked circuits. It proves the equivalence of two circuits by computing an equivalence relation between the states of the two circuits and utilizes syntax abstraction to accelerate this process. Experimental results show that SE3 is significantly faster than state-of-the-art sequential equivalence checking algorithms.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247912"}, {"primary_key": "1120068", "vector": [], "sparse_vector": [], "title": "NMExplorer: An Efficient Exploration Framework for DIMM-based Near-Memory Tensor Reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangyu Sun", "<PERSON><PERSON>"], "summary": "Various DIMM-based near-memory processing (DIMM-NMP) architectures have been proposed to accelerate tensor reduction. With careful evaluation, we find that diverse scenarios exhibit distinct performance on DIMM-NMP architectures adopting different design configurations. However, given a tensor reduction scenario, there is a lack of a fast and accurate solution to identify a proper DIMM-NMP architecture. To tackle this problem, we propose an efficient exploration framework called NMExplorer. Given a scenario and hardware parameters, NMExplorer can generate and explore a wide range of potential design configurations. Experiments show that the recommended designs can outperform state-of-the-art DIMM-NMP accelerators by up to 1.95× in performance and 3.69× in energy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247764"}, {"primary_key": "1120069", "vector": [], "sparse_vector": [], "title": "Memory-Efficient and Real-Time SPAD-based dToF Depth Sensor with Spatial and Statistical Correlation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Single Photon Avalanche Diode (SPAD)-based direct time-of-flight (dToF) depth sensors are widely used in Internet of Things (IoT) devices due to their high accuracy. Existing SPAD-based dToF sensors measure depth by continually accumulating the depth-measured value in a histogram. However, histogram-based methods typically have low convergence speed (~10 frames per second (FPS)) and large memory overhead (MB-level), hindering their use in real-time embedded IoT devices. To overcome these two challenges, we propose SSC, a histogram-free Spatial and Statistical Correlation based depth measurement method. On the one hand, SSC applies the spatial correlation of the adjacent pixels to accelerate the convergence speed. On the other hand, SSC explores the statistical correlation of depth measurements to reduce the memory overhead. In order to implement SSC with small hardware area and low power, we design mert-dToF, a memory-efficient and real-time dToF sensor for efficient execution. mert-dToF abstracts mainly operations in SSC into four basic operators and designs corresponding hardware with a fine-grained pipeline to maximize resource reuse and computational parallelism. Extensive experiments show that compared with state-of-the-art (SOTA) histogram-based dToF sensors, mert-dToF achieves ~8% accuracy improvement and 7.80× speedup (from 6.24 FPS to 48.70 FPS). The memory overhead is reduced by up to 60.91% (from 48 KB to 18.75 KB).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247771"}, {"primary_key": "1120070", "vector": [], "sparse_vector": [], "title": "DRPTM: A Decoupled Read-efficient High-scalable Persistent Transactional Memory.", "authors": ["Wen<PERSON> Liang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yanqi Pan"], "summary": "Persistent transactional memory (PTM) exploits transactions to provide an easy crash-consistent interface for persistent memory (PM). However, because of the substantial reader-side overhead brought on by the low bandwidth and long persistence latency of PM, present PTM research cannot scale effectively. This paper proposes a highly scalable PTM system, DRPTM, which allows nearly non-overhead reads without lowering the isolation level. DRPTM decouples persistence latency from concurrency control and traces the read-only copy maintained in logs as a lightweight read set. The evaluation shows that DRPTM significantly outperforms the state-of-the-art PTM systems for various workloads and achieves near-linear scalability.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247971"}, {"primary_key": "1120071", "vector": [], "sparse_vector": [], "title": "Response Time Analysis and Optimization of DAG Tasks Exploiting Mutually Exclusive Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "There is an increasing move towards implementing embedded real-time systems upon multiprocessors with parallel applications, which are usually modeled as Directed Acyclic Graphs (DAGs). Plentiful work has been presented to optimize the bound of Worst-Case Response Time (WCRT) since the cornerstone work proposed by <PERSON> in 1969. However, all these works are developed on the basis of <PERSON>'s bound and failed to tackle the root of pessimism in it. In this work, we present a novel method to optimize the WCRT bound of a DAG task by designing mutually exclusive groups so that a sequential execution is enforced for some nodes, under which the problem of bounding WCRT becomes a problem of identifying a mutually exclusive path and thus does not suffer the pessimism in <PERSON>'s bound. Experiments are conducted to evaluate the performance of our method against other WCRT optimization approaches in the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247927"}, {"primary_key": "1120072", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Test Selection For RTL Coverage By Unsupervised Learning From Fast Functional Simulation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Haoxin Ren", "<PERSON><PERSON>"], "summary": "Functional coverage closure is an important but RTL simulation intensive aspect of constrained random verification. To reduce these computational demands, we propose test selection for functional coverage via machine learning (ML) based anomaly detection in the structural coverage space of fast functional simulators. We achieve promising results on two units from a state-of-the-art production GPU design. With our approach, an up to 85% RTL simulation runtime reduction can be achieved when compared to baseline constrained random test selection while achieving the same RTL functional coverage.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247936"}, {"primary_key": "1120073", "vector": [], "sparse_vector": [], "title": "Hybrid Gate-Pulse Model for Variational Quantum Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ruiyang Qin", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current quantum programs are mostly synthesized and compiled on the gate-level, where quantum circuits are composed of quantum gates. The gate-level workflow, however, introduces significant redundancy when quantum gates are eventually transformed into control signals and applied on quantum devices. For superconducting quantum computers, the control signals are microwave pulses. Therefore, pulse-level optimization has gained more attention from researchers due to their advantages in terms of circuit duration. Recent works, however, are limited by their poor scalability brought by the large parameter space of control signals. In addition, the lack of gate-level \"knowledge\" also affects the performance of pure pulse-level frameworks. We present a hybrid gate-pulse model that can mitigate these problems. We propose to use gate-level compilation and optimization for \"fixed\" part of the quantum circuits and to use pulse-level methods for problem-agnostic parts. Experimental results demonstrate the efficiency of the proposed framework in discrete optimization tasks. We achieve a performance boost at most 8% with 60% shorter pulse duration in the problem-agnostic layer.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247923"}, {"primary_key": "1120074", "vector": [], "sparse_vector": [], "title": "On a Moreau Envelope Wirelength Model for Analytical Global Placement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Analytical placement is proven to be effective in global placement. The differentiability of wirelength models is very critical to gradient-based numerical optimization. Most previous works approximate the non-smooth half-perimeter wirelength (HPWL) model with various differentiable functions. In this paper, we propose a new differentiable wirelength model using the Moreau envelope to approximate HPWL. By combining the state-of-the-art electrostatic-based placement algorithm, the experimental results demonstrate that our proposed algorithm can achieve up to 5.4% HPWL improvement and more than 1% on average compared to the most widely-used nonlinear wirelength model.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247712"}, {"primary_key": "1120075", "vector": [], "sparse_vector": [], "title": "Work or Sleep: Freshness-Aware Energy Scheduling for Wireless Powered Communication Networks with Interference Consideration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper explores how to schedule energy to optimize the information freshness in wireless powered communication networks (WPCNs) when considering channel interference among adjacent sensor nodes. We introduce Age of Information (AoI) to quantitatively evaluate the information freshness and formulate the AoI optimization problem. Unlike prior works focusing on system optimization for WPCNs while ignoring channel interference in energy transfer, this work reveals situations where channel interference among adjacent sensor nodes cannot be neglected and explores optimizing information freshness with interference consideration. To take the phenomena into account, we propose an energy scheduling solution to detect the channel interference and then judiciously determine the energy and time allocation for individual sensor nodes to improve the AoI performance as well as the system throughput. We implement a multi-node WPCN testbed to validate the functional correctness of the proposed solution, and extensive experiments have demonstrated the effectiveness of the proposed solution. The experimental results show that the proposed solution can reduce the average AoI by 54.7% and the average throughput by 49.8% on average compared to the state-of-the-art solutions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120076", "vector": [], "sparse_vector": [], "title": "Scalable Optimal Layout Synthesis for NISQ Quantum Processors.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Due to its effect on the success rate of a quantum circuit, quantum layout synthesis is a crucial step for circuit compilation. As such, having a layout synthesis tool that provides high solution quality is important to maximize circuit performance and fidelity for NISQ application. Previous heuristic approaches have been shown to be far from optimal when evaluated on known-optimal benchmarks. Alternatively, exact layout synthesis tools can generate optimal results with the aid of constraint solvers but generally suffer from scalability issues because of inefficient encodings and slow optimization methods. In this paper, we propose a scalable optimal layout synthesis tool that improves upon previous works, through a more succinct problem formulation as well as better encoding techniques. Additionally, we implement a depth and SWAP count optimization feature that performs iterative refinement under a fixed time budget. Experimental results show that for depth optimization, our tool can achieve a 692× speedup over the state-of-the-art optimal layout synthesis, and for SWAP optimization, we can obtain a 6,957× speedup on average. Compared to a leading heuristic-based synthesizer, for depth optimization, we can solve circuits consisting of 54 program qubits and 1726 gates within 11 hours with an 18× depth reduction and by 12× SWAP count reduction on average.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247760"}, {"primary_key": "1120077", "vector": [], "sparse_vector": [], "title": "AdaS: A Fast and Energy-Efficient CNN Accelerator Exploiting Bit-Sparsity.", "authors": ["<PERSON><PERSON>", "Gang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Naifeng Jing", "<PERSON><PERSON><PERSON>"], "summary": "Bit-sparsity has shown its promise in CNN acceleration. However, prior bit-sparse accelerators have two drawbacks: 1) a large number of zero values are involved in the computation and data movement; 2) the distribution of non-zero bits is not considered in PE design. To address these issues, we propose AdaS. At the multiplier level, we dynamically serialize the operands that have fewer non-zero bits. At the dataflow level, we propose a group-wise bi-directional inner-join for workload extraction and balancing. Results show that AdaS can achieve 3.28×, 2.05× speedup, and 1.99×, 1.80× energy efficiency over Bit-Pragmatic and Laconic, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247662"}, {"primary_key": "1120078", "vector": [], "sparse_vector": [], "title": "Intermittent-Aware Neural Network Pruning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural network inference on energy harvesting tiny devices has emerged as a solution for sustainable edge intelligence. However, compact models optimized for continuously-powered systems may become suboptimal when deployed on intermittently-powered systems. This paper presents the pruning criterion, pruning strategy, and prototype implementation of iPrune, the first framework which introduces intermittency into neural network pruning to produce compact models adaptable to intermittent systems. The pruned models are deployed and evaluated on a Texas Instruments device with various power strengths and TinyML applications. Compared to an energy-aware pruning framework, iPrune can speed up intermittent inference by 1.1 to 2 times while achieving comparable model accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247825"}, {"primary_key": "1120079", "vector": [], "sparse_vector": [], "title": "GenFuzz: GPU-accelerated Hardware Fuzzing using Genetic Algorithm with Multiple Inputs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Yan<PERSON> Zhang", "Haoxing Ren", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Hardware fuzzing has emerged as a promising automatic verification technique to efficiently discover and verify hardware vulnerabilities. However, hardware fuzzing can be extremely time-consuming due to compute-intensive iterative simulations. While recent research has explored several approaches to accelerate hardware fuzzing, nearly all of them are limited to single-input fuzzing using one thread of a CPU-based simulator. As a result, we propose Gen-Fuzz, a GPU-accelerated hardware fuzzer using a genetic algorithm with multiple inputs. Measuring experimental results on a real industrial design, we show that GenFuzz running on a single A6000 GPU and eight CPU cores achieves 80× runtime speed-up when compared to state-of-the-art hardware fuzzers.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247942"}, {"primary_key": "1120080", "vector": [], "sparse_vector": [], "title": "Seeking the Yield Barrier: High-Dimensional SRAM Evaluation Through Optimal Manifold.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Being able to efficiently obtain an accurate estimate of the failure probability of SRAM components has become a central issue as model circuits shrink their scale to submicrometer with advanced technology nodes. In this work, we revisit the classic norm minimization method. We then generalize it with infinite components and derive the novel optimal manifold concept, which bridges the surrogate-based and importance sampling (IS) yield estimation methods. We then derive a sub-optimal manifold, optimal hypersphere, which leads to an efficient sampling method being aware of the failure boundary called onion sampling. Finally, we use a neural coupling flow (which learns from samples like a surrogate model) as the IS proposal distribution. These combinations give rise to a novel yield estimation method, named Optimal Manifold Important Sampling (OPTIMIS), which keeps the advantages of the surrogate and IS methods to deliver state-of-the-art performance with robustness and consistency, with up to 3.5x in efficiency and 3x in accuracy over the best of SOTA methods in High-dimensional SRAM evaluation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247952"}, {"primary_key": "1120081", "vector": [], "sparse_vector": [], "title": "Rethinking Programming Frameworks for In-Storage Processing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-storage processing (ISP) is the most commercialized implementation of the near-data processing (NDP) model that executes tasks near their storage locations. However, programming ISP devices is complicated as it requires programmers to work closely with the underlying hardware, and even highly-optimized code can easily lead to suboptimal performance.This paper introduces ActivePy. ActivePy makes the programmer completely agnostic to ISP hardware. ActivePy automatically, transpartently, and dynamically generates high-performance code to balance system-wide trade-offs and max-imize the benefits of ISP. Our real system implementation shows that ActivePy can use ISP as efficiently as conventional C-based frameworks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247919"}, {"primary_key": "1120082", "vector": [], "sparse_vector": [], "title": "Compact and High-Performance TCAM Based on Scaled Double-Gate FeFETs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON>"], "summary": "Ternary content addressable memory (TCAM), widely used in network routers and high-associativity caches, is gaining popularity in machine learning and data-analytic applications. Ferroelectric FETs (FeFETs) are a promising candidate for implementing TCAM owing to their high ON/OFF ratio, non-volatility, and CMOS compatibility. However, conventional single-gate FeFETs (SG-FeFETs) suffer from relatively high write voltage, low endurance, potential read disturbance, and face scaling challenges. Recently, a double-gate FeFET (DG-FeFET) has been proposed and outperforms SG-FeFETs in many aspects. This paper investigates TCAM design challenges specific to DG-FeFETs and introduces a novel 1.5T1Fe TCAM design based on DG-FeFETs. A 2-step search with early termination is employed to reduce the cell area and improve energy efficiency. A shared driver design is proposed to reduce the peripherals area. Detailed analysis and SPICE simulation show that the 1.5T1Fe DGTCAM leads to superior search speed and energy efficiency. The 1.5T1Fe TCAM design can also be built with SG-FeFETs, which achieve search latency and energy improvement compared with 2FeFET TCAM.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120083", "vector": [], "sparse_vector": [], "title": "A Matching Based Escape Routing Algorithm with Variable Design Rules and Constraints.", "authors": ["Qing<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Escape routing is a critical problem in PCB routing, and its quality greatly affects the PCB design cost. Unlike the traditional escape routing that works mainly for the BGA package with unique line width and space, this paper presents a high-performance escape routing algorithm to handle problems with variable design rules and manual constraints, including variable line widths/spaces, the neck mode of wires, and the pad entry for differential pairs. We first propose a novel obstacle-avoiding method to project pins to the boundary and construct a channel projection graph. We then construct a bi-projection graph and propose a matching-based hierarchical sequencing algorithm to consider manual constraints. We perform global routing for each pin/differential pair by congestion-avoiding path initialization and rip-up and reroute path optimization. Finally, we complete detailed routing in every face, ensuring the wire angle and pad entry constraints. Experimental results show that our algorithm can achieve 100% routability without any design rule violation for all given industrial PCB instances, while two state-of-the-art routers cannot complete routing.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120084", "vector": [], "sparse_vector": [], "title": "HyperAttack: An Efficient Attack Framework for HyperDimensional Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Li <PERSON>"], "summary": "HyperDimensional Computing (HDC) is emerging as a lightweight computational model for robust and efficient learning on resource-constrained hardware. Since HDC often runs on edge devices, the security challenge of HDC is a pressing issue confronting all the practitioners. Meanwhile, the security challenge of HDC's parameters stored in memory has not been well studied. In this work, we are the first to propose a novel HDC attack framework called HyperAttack, which can crush a robust HDC model (i.e., binary HDC) by maliciously flipping an extremely few amount of bits within its memory system (i.e., DRAM) that stores the associative memory. Since the bit-flip operation can be conducted by the well-known Row Hammer attack, HyperAttack maximizes the accuracy degradation with the minimum number of bit-flips by identifying the bits closely related to the classification accuracy of hyperdimensional vectors (stored in the associative memory as binary vectors) in HDC. The proposed HyperAttack is based on the concept of fuzzing, combining dimensional ranking and distributions of features in hypervectors to identify the bits to be flipped. Our evaluation shows that HyperAttack can successfully attack a binary HDC by flipping only 10% bits of hyperdimensional vectors to decrease top-1 accuracy from 90.9% to 10%, while randomly flipping merely degrades the accuracy by less than 2%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247811"}, {"primary_key": "1120085", "vector": [], "sparse_vector": [], "title": "Invited: Waving the Double-Edged Sword: Building Resilient CAVs with Edge and Cloud Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The rapid advancement of edge and cloud computing platforms, vehicular ad-hoc networks, and machine learning techniques have brought both opportunities and challenges for next-generation connected and automated vehicles (CAVs). On the one hand, these technologies can enable vehicles to leverage more computing power from edge and cloud servers and to share information with each other and surrounding infrastructures for better situation awareness and more intelligent decision making. On the other hand, the more distributed computing process and the wireless nature of V2X (vehicle-to-everything) communication expose vulnerabilities to various disturbances and attacks. In this paper, we discuss the security and safety challenges for edge- and cloud-enabled CAVs, particularly when they are under environment interferences, execution errors, and malicious attacks, and we will introduce our recent work and future directions in developing system-driven, end-to-end methodologies and tools to address these challenges and ensure system resiliency under uncertainties.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247809"}, {"primary_key": "1120086", "vector": [], "sparse_vector": [], "title": "DeepOHeat: Operator Learning-based Ultra-fast Thermal Simulation in 3D-IC Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON>", "Xi<PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Thermal issue is a major concern in 3D integrated circuit (IC) design. Thermal optimization of 3D IC often requires massive expensive PDE simulations. Neural network-based thermal prediction models can perform real-time prediction for many unseen new designs. However, existing works either solve 2D temperature fields only or do not generalize well to new designs with unseen design configurations (e.g., heat sources and boundary conditions). In this paper, for the first time, we propose DeepOHeat, a physics-aware operator learning framework to predict the temperature field of a family of heat equations with multiple parametric or non-parametric design configurations. This framework learns a functional map from the function space of multiple key PDE configurations (e.g., boundary conditions, power maps, heat transfer coefficients) to the function space of the corresponding solution (i.e., temperature fields), enabling fast thermal analysis and optimization by changing key design configurations (rather than just some parameters). We test DeepOHeat on some industrial design cases and compare it against Celsius 3D from Cadence Design Systems. Our results show that, for the unseen testing cases, a well-trained DeepOHeat can produce accurate results with 1000× to 300000× speedup.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247998"}, {"primary_key": "1120087", "vector": [], "sparse_vector": [], "title": "Leaky MDU: ARM Memory Disambiguation Unit Uncovered and Vulnerabilities Exposed.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>peng Ju", "<PERSON>", "<PERSON><PERSON><PERSON> Wang"], "summary": "Memory Disambiguation Unit (MDU) is widely used on modern processors to speculatively execute load instructions and improve pipeline performance. Given that the MDU design details on ARM processors are not available to the public, it is unclear whether there are any security vulnerabilities associated with its MDU. In this paper, we first reverse engineer the undocumented features of ARM MDU, then we discover three potential user-privilege attacks to leak secret data via MDU: cross-process attack that allows users to communicate through a convert channel, cross-domain attack that leaks kernel information and a new variant of inner-process and inter-processes Spectre attacks. These attacks pose serious security challenges as they can bypass both all the known countermeasures against cache side-channel attacks and those against transient execution attacks. Potential mitigation against the proposed MDU-based attacks are also discussed.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247985"}, {"primary_key": "1120088", "vector": [], "sparse_vector": [], "title": "Layout Decomposition via Boolean Satisfiability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Multiple patterning lithography (MPL) has been introduced in the integrated circuits manufacturing industry to enhance feature density as the technology node advances. A crucial step of MPL is assigning layout features to different masks, namely layout decomposition. Exact algorithms like integer linear programming (ILP) can solve layout decomposition to optimality but lacks scalability for very dense patterns. Approximation algorithms (e.g., linear programming, semi-definite programming) and heuristics (e.g., Exact-Cover) are capable of handling large cases but can only get inferior solutions. In this paper, we propose a new exact algorithm that tackles layout decomposition by solving a series of boolean satisfiability instances. Our algorithm can preserve optimality and achieve more than 4× speedup compared to ILP. In addition, we provide an approximation algorithm by reformulating the layout decomposition to a bilevel optimization problem. Experiments show that our approximation algorithm can attain higher solution quality compared to SDP and heuristics within faster convergence.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247883"}, {"primary_key": "1120089", "vector": [], "sparse_vector": [], "title": "DARIC: A Data Reuse-Friendly CGRA for Parallel Data Access via Elastic FIFOs.", "authors": ["Da<PERSON> Liu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-Grained Reconfigurable Arrays (CGRAs) are a promising architecture for data-intensive applications. For parallel data accesses, uniform memory partitioning is usually introduced to CGRA for better pipelining performance. However, uniform memory partitioning not only suffers from a local minimum, but also introduces non-negligible overhead for banking function, which may greatly degrade the performance of CGRA. To this end, this paper introduces non-uniform memory partitioning and proposes a data-reuse-friendly CGRA (DARIC). With well elaborated configurable bank groups cooperated with register chains, elastic FIFOs can be achieved for non-uniform memory partitioning. Based on the resource graph of DARIC, a mapping algorithm supporting path sharing is proposed. Finally, the experimental results show that DARIC can achieve 2.35 × throughput and 2.59 × energy efficiency while having even less area and power overhead, as compared to the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120090", "vector": [], "sparse_vector": [], "title": "Disjoint-Path and Golden-Pin Based Irregular PCB Routing with Complex Constraints.", "authors": ["Qing<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "PCB routing becomes time-consuming as the complexity of PCB design increases. Unlike traditional schemes that treat the two essential PCB routing processes separately, namely, escape and bus routing, we consider the continuity between them and present a golden-pin-based routing scheme to find the desired solution with angle and topology constraints. Further, conventional rip-up and reroute methods are often ineffective and inefficient for congestion alleviation and routability optimization. We construct a component graph by modeling components as vertices and applying the minimum weight vertex covering method to improve the routability. A self-adaptable ordering method is presented for escape routing to arrange the pin order on the component boundary, guaranteeing successful bus routing. In addition, escape routing is performed based on a disjoint path method. We construct a dynamic Hanan grid in bus routing and utilize a novel congestion adjustment technique to improve solution quality. Compared with FreeRouting and Allegro, the experiment results show that our algorithm achieves high routability and a significant 90% runtime reduction.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120091", "vector": [], "sparse_vector": [], "title": "Concurrent Sign-off Timing Optimization via Deep Steiner Points Refinement.", "authors": ["Siting <PERSON>", "<PERSON><PERSON><PERSON>", "Fangzhou Liu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Timing closure is crucial across the circuit design flow. Since obtaining sign-off performance needs a time-consuming routing flow, all the previous early-stage timing optimization works only focus on improving early timing metrics, e.g., rough timing estimation using linear RC model or pre-routing path-length. However, there is no consistency guarantee between early-stage metrics and sign-off timing performance. To enable explicit early-stage optimization on the sign-off timing metrics, we propose a novel timing optimization framework, TSteiner. This paper demonstrates the ability of the learning framework to perform robust and efficient timing optimization in the early stage with comprehensive and convincing experimental results on real-world designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120092", "vector": [], "sparse_vector": [], "title": "EDGE: Efficient DAG-based Global Routing Engine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Routing is one of the most time-consuming steps in the modern VLSI design flow. A well-designed global routing algorithm can effectively shrink the overall routing time and improve the quality of design after routing. Unlike many global routers that rely heavily on time-consuming path search algorithms like maze routing to resolve overflows, we propose to use directed acyclic graph (DAG) to explore the routing space more efficiently and create detours only when necessary. Experimental results on the ICCAD'19 benchmarks show that our algorithm improves the state-of-the-art quality of result by 1.4% and runs with a single thread faster than the fastest multi-threaded global router.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120093", "vector": [], "sparse_vector": [], "title": "Rethinking AIG Resynthesis in Parallel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "The efficiency issue of logic optimization becomes critical as the scale of VLSI designs grows. Since various algorithms are interleaved during optimization to ensure quality, it is necessary to accelerate those commonly used algorithms for obtaining substantial total speed-up. This paper proposes novel parallel algorithms for AIG refactoring and AND-balancing. Equipped with delicately designed parallel-friendly, data-race-free frameworks and GPU data structures, our algorithms obtain significant speed-up and enable the resyn2 sequence to be fully GPU-parallelized when combined with GPU rewriting. Experiments show that on large AIGs, we achieve average accelerations up to 45.9×over ABC with comparable or better qualities.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247961"}, {"primary_key": "1120094", "vector": [], "sparse_vector": [], "title": "Ising-CF: A Pathbreaking Collaborative Filtering Method Through Efficient Ising Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Due to the Ising model's strong expressivity and Ising machines' unique computational power, it is highly desired if Ising-based learning can be used in real-world applications. Unfortunately, the challenges in learning the Ising model and gaps between the practical accuracy of Ising machines and the theoretical accuracy of the Ising model impede the realization of Ising machines' potential. Hence, we propose an Ising Machine Learning framework, Ising-CF, for collaborative filtering, a widely-used recommendation method. Specifically, Ising-CF uses Linear Neural Networks with Besag's pseudo-likelihood and voltage polarization for fast, accurate Ising model learning and an Ising-specific logarithmic quantization for ns-level Ising machine inference with near-theoretical accuracy, 7.3% over SOTA.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247860"}, {"primary_key": "1120095", "vector": [], "sparse_vector": [], "title": "Fault-Tolerance-Oriented Physical Design for Fully Programmable Valve Array Biochips.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a new generation of flow-based microfluidics, the Fully Programmable Valve Array (FPVA) biochips have become a popular biochemical experimental platform that provide higher flexibility and programmability. Due to environmental and human factors, however, there are usually some physical faults in the manufacturing process such as channel blockage and leakage, which, undoubtedly, can affect the results of bioassays and even cause execution failure. Accordingly, we focus on the fault-tolerant design of FPVA biochips for the first time in this paper, and present three dynamic fault-tolerant techniques including a cell function conversion method, a bidirectional redundancy scheme, and a fault mapping method. By integrating these techniques into the component placement and channel routing stages, we further realize an efficient and effective fault-tolerance-oriented physical design approach for FPVA biochips, thus ensuring the robustness of chip architecture and correctness of assay outcomes. Experimental results on multiple benchmarks confirm that the proposed approach can generate fault-tolerant FPVA architectures with both high execution efficiency and low fabrication cost.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120096", "vector": [], "sparse_vector": [], "title": "Bit-Serial Cache: Exploiting Input Bit Vector Repetition to Accelerate Bit-Serial Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Bit-serial computation has demonstrated superiority in processing precision-varying DNNs by slicing multi-bit vectors into multiple single-bit vectors and computing the inner product using multiple steps of shift-and-adds. In this paper, we identify that performing real-world DNNs inference with bit-serial computation exhibits high input bit vector locality, where up to 85.7% of non-zero input bit vectors, as well as their associated computation, are previously-seen and previously-done ones. We propose Bit-Serial Cache to transfer the identified locality into performance and energy efficiency gains. The key design strategy is to store recently-computed partial sums of input bit vectors to a cache and utilize cache accesses to replace redundant computations. In addition to the bit-serial computation architecture, we also present: 1) request clustering and 2) interleaved scheduling, to further enhance the performance and energy efficiency.Our experiments using six popular DNNs (in both 8-b and 4-b) show that Bit-Serial Cache speeds up DNN inference by up to 2.72×, 1.82×, and 4.03×, energy efficiency by 3.19×, 3.29×, and 2.82×, area efficiency by 1.35×, 1.24×, and 2.76× over state-of-the-art Loom, DPRed Loom, and Laconic.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247749"}, {"primary_key": "1120097", "vector": [], "sparse_vector": [], "title": "Morphable CIM: Improving Operation Intensity and Depthwise Capability for SRAM-CIM Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "SRAM-based computing in memory (SRAM-CIM) supports weight-stationary dataflows and in-situ computation, successfully achieving lower weight SRAM traffic and hence better operation intensity (OI) than Von Neumann architecture. However, sticking to weight-stationary dataflows is sub-optimal since some layers of CNNs, e.g., depth-wise and input-dominant, suffer from large activation traffic and underutilization.We propose Morphable CIM to address the above challenges, in which the key contributions are: 1) We propose a dataflow-morphable SRAM-CIM architecture that adaptively switches between weight-stationary and input-stationary dataflow to enhance the overall operation intensity (OI) and computation utilization. 2) We propose an input-stationary systolic dataflow and a word-wise mapping to efficiently achieve dataflow reconfigurability for SRAM-CIM. 3) We propose a depthwise-capable CIM macro to improve the utilization of processing depth-wise layers.The experimental results on ten workloads show that our proposed SRAM-CIM architecture successfully outperforms the traffic-optimized and the performance-optimized baselines by up to 16.7× performance speedup, 3.6× higher energy efficiency, and 94.8% memory traffic reduction.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247750"}, {"primary_key": "1120098", "vector": [], "sparse_vector": [], "title": "RL-CCD: Concurrent Clock and Data Optimization using Attention-Based Self-Supervised Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Concurrent Clock and Data (CCD) optimization is a well-adopted approach in modern commercial tools that resolves timing violations using a mixture of clock skewing and delay fixing strategies. However, existing CCD algorithms are flawed. Particularly, they fail to prioritize violating endpoints for different optimization strategies correctly, leading to flow-wise globally sub-optimal results. In this paper, we overcome this issue by presenting RL-CCD, a Reinforcement Learning (RL) agent that selects endpoints for useful skew prioritization using the proposed EP-GNN, an endpoint-oriented Graph Neural Network (GNN) model, and a Transformer-based self-supervised attention mechanism. Experimental results on 19 industrial designs in 5 − 12nm technologies demonstrate that RL-CCD achieves up to 64% Total Negative Slack (TNS) reduction and 66.5% number of violating endpoints (NVE) improvement over the native implementation of a commercial tool.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248008"}, {"primary_key": "1120099", "vector": [], "sparse_vector": [], "title": "ZoneKV: A Space-Efficient Key-Value Store for ZNS SSDs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a new space-efficient key-value store called ZoneKV for ZNS (Zoned Namespace) SSDs. We observe that existing work on adapting RocksDB to ZNS SSDs will cause fragmentation of zones and severe space amplification. Thus, we propose a lifetime-based zone storage model and a level-specific zone allocation algorithm to store SSTables with a similar lifetime in the same zone. We evaluate ZoneKV on a real ZNS SSD. The results show that ZoneKV can reduce up to 60% space amplification and maintain higher throughputs than two competitors, RocksDB and ZenFS.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247926"}, {"primary_key": "1120100", "vector": [], "sparse_vector": [], "title": "An FPGA-Compatible TRNG with Ultra-High Throughput and Energy Efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Qidong Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we design an energy-efficient true random number generator with ultra-high throughput for FPGA. Only four ring oscillators constructed using eight LUTs are sampled by multiple sampling points to fully exploit the randomness of the entropy source, which provides high-quality and over 275 Mbps random sequences while consuming 13 slices. An end-to-end implementation and testing framework is tailored for easy deployment and portability on Xilinx 7 serials FPGAs. The proposed architecture passes the NIST SP 800-22 and 800-90B tests without post-processing and outperforms the state-of-the-art in terms of minimum entropy and energy efficiency.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247746"}, {"primary_key": "1120101", "vector": [], "sparse_vector": [], "title": "Heterogeneous Reconfigurable Accelerators: Trends and Perspectives.", "authors": ["<PERSON>"], "summary": "Heterogeneity and reconfigurability have both been adopted by accelerators to improve their flexibility and efficiency for a wide variety of applications, from cloud computing to embedded systems. This paper provides an overview of the trends of heterogeneous reconfigurable accelerators including field-programmable gate arrays and coarse-grained reconfigurable arrays, and the related design automation approaches for enhancing design quality and designer productivity of these accelerators. We shall also discuss how recent advances in technology, such as multi-level co-design, heterogeneous Function-as-a-Service and meta-programming, would help address the challenges in engineering next-generation heterogeneous reconfigurable accelerators and beyond.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247723"}, {"primary_key": "1120102", "vector": [], "sparse_vector": [], "title": "Rubick: A Synthesis Framework for Spatial Architectures via Dataflow Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dataflows are critical for spatial architectures designed for tensor applications. Prior works develop various notations and hardware generation frameworks for dataflows. However, due to the semantic gap between notations and low-level details, analysis based on these notations cannot capture the detailed architectural features between different dataflows, so these works failed to provide architectural optimization and efficient design space exploration (DSE) at the same time.We propose Rubick, a synthesis framework for spatial architecture. <PERSON><PERSON><PERSON> decomposes the dataflow into two low-level intermediate representations including access entry and data layout. Access entry specifies how data enter into the PE arrays from memory, while data layout specifies how data are arranged and accessed. Based on this decomposition, Rubick provides efficient DSE and generates optimized hardware. Experiments show that the DSE time is accelerated by up to 1.1×10 5 X and performance on FPGA is improved by 13%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247743"}, {"primary_key": "1120103", "vector": [], "sparse_vector": [], "title": "ML-CGRA: An Integrated Compilation Framework to Enable Efficient Machine Learning Acceleration on CGRAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Coarse-Grained Reconfigurable Arrays (CGRAs) can achieve higher energy-efficiency than general-purpose processors and accelerators or fine-grained reconfigurable devices, while maintaining adaptability to different computational patterns. CGRAs have shown some success as a platform to accelerate machine learning (ML) thanks to their flexibility, which allows them to support new models not considered by fixed accelerators. However, current solutions for CGRAs employ low level instruction-based compiler approaches and lack specialized compilation infrastructures from high-level ML frameworks that could leverage semantic information from the models, limiting the ability to efficiently map them on the reconfigurable substrate. This paper proposes ML-CGRA, an integrated compilation framework based on the MLIR infrastructure that enables efficient ML acceleration on CGRAs. ML-CGRA provides an end-to-end solution for mapping ML models on CGRAs that outperforms conventional approaches by 3.15× and 6.02 × on 4×4 and 8×8 CGRAs, respectively. The framework is open-source and available from https://github.com/tancheng/mlir-cgra.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247873"}, {"primary_key": "1120104", "vector": [], "sparse_vector": [], "title": "FLNA: An Energy-Efficient Point Cloud Feature Learning Accelerator with Dataflow Decoupling.", "authors": ["<PERSON><PERSON><PERSON>", "Zhenyu Li", "Yuzhou Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Grid-based feature learning network plays a key role in recent point-cloud based 3D perception. However, high point sparsity and special operators lead to large memory footprint and long processing latency, posing great challenges to hardware acceleration. We propose FLNA, a novel feature learning accelerator with algorithm-architecture co-design. At algorithm level, the dataflow-decoupled graph is adopted to reduce 86% computation by exploiting inherent sparsity and concat redundancy. At hardware design level, we customize a pipelined architecture with block-wise processing, and introduce transposed SRAM strategy to save 82.1% access power. Implemented on a 40nm technology, FLNA achieves 13.4 − 43.3× speedup over RTX 2080Ti GPU. It rivals the state-of-the-art accelerator by 1.21× energy-efficiency improvement with 50.8% latency reduction.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247674"}, {"primary_key": "1120105", "vector": [], "sparse_vector": [], "title": "Processor Vulnerability Discovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Processor security vulnerability discovery has drawn increasing attention since the disclosure of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and other vulnerabilities. This paper presents a concise roadmap of this emerging research direction from the simple manual discovery to automated discovery methodologies, as well as the major challenges along the roadmap.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247906"}, {"primary_key": "1120106", "vector": [], "sparse_vector": [], "title": "Return-to-Non-Secure Vulnerabilities on ARM Cortex-M TrustZone: Attack and Defense.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong<PERSON> Hu", "Zim<PERSON>"], "summary": "ARM Cortex-M is one of the most popular microcontroller architectures designed for embedded and Internet of Things (IoT) applications. To facilitate efficient execution, it has some unique hardware optimization. In particular, Cortex-M TrustZone has a fast state switch mechanism that allows direct control-flow transfer from the secure state program to the non-secure state userspace program. In this paper, we demonstrate how this fast state switch mechanism can be exploited for arbitrary code execution with escalated privilege in the non-secure state by introducing a new exploitation technique, namely return-to-non-secure (ret2ns). We experimentally confirmed the feasibility of four variants of ret2ns attacks on two Cortex-M hardware systems. To defend against ret2ns attacks, we design two address sanitizing mechanisms that have negligible performance overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247972"}, {"primary_key": "1120107", "vector": [], "sparse_vector": [], "title": "Fault Tolerance in Time-Sensitive Networking with Mixed-Critical Traffic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yu <PERSON>", "<PERSON><PERSON>"], "summary": "As an Ethernet-based communication protocol with timing determinism, TSN (time-sensitive networking) has become a well-recognized promising in-vehicle network solution for increasingly automated automobiles. To satisfy the reliability requirement of safety-critical applications, existing works towards fault-tolerant TSN trade too much bandwidth for redundancy, limiting their scope of applicability. Targeting mixed-critical traffic, which is widely found in practice, we define a shared fault-tolerant segment that is compatible with the TSN standard. It serves the critical flows when faults occur, to improve their reliability and serves the non-critical flows when otherwise, to improve their quality of service (QoS). On top of this, we propose a space-time redundancy scheduling algorithm, aiming to make the most efficient use of bandwidth, i.e., to fulfill both the reliability as well as hard real-time requirements, and maximize the QoS with the least bandwidth. In essence, we formulate a bi-objective design space exploration problem with hundreds of thousands of decision variables and solve it with customized heuristics. Experimental results show that compared to the state-of-the-art methods, our reported work increases the number of critical flows that can be accommodated on a resource-constrained network by 3 to 4 times, and achieve the highest QoS with an average reduction of 60.3% in bandwidth. As the first work along sharing of bandwidth between mixed-critical traffic in fault-tolerant TSN, this idea can be further pursued towards higher efficiency and may be applied in general autonomous systems.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247817"}, {"primary_key": "1120108", "vector": [], "sparse_vector": [], "title": "TFix: Exploiting the Natural Redundancy of Ternary Neural Networks for Fault Tolerant In-Memory Vector Matrix Multiplication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-memory computing (IMC) and quantization have emerged as promising techniques for edge-based deep neural network (DNN) accelerators by reducing their energy, latency and storage requirements. In pursuit of ultra-low precision, ternary precision DNNs (TDNNs) offer high efficiency without sacrificing much inference accuracy. In this work, we explore the impact of hard faults on IMC based TDNNs and propose TFix to enhance their fault tolerance. TFix exploits the natural redundancy present in most ternary IMC bitcells as well as the high weight sparsity in TDNNs to provide up to 40.68% accuracy increase over the baseline with < 6% energy overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247835"}, {"primary_key": "1120109", "vector": [], "sparse_vector": [], "title": "Faster and Stronger Lossless Compression with Optimized Autoregressive Framework.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural AutoRegressive (AR) framework has been applied in general-purpose lossless compression recently to improve compression performance. However, this paper found that directly applying the original AR framework causes the duplicated processing problem and the in-batch distribution variation problem, which leads to deteriorated compression performance. The key to address the duplicated processing problem is to disentangle the processing of the history symbol set at the input side. Two new types of neural blocks are first proposed. An individual-block performs separate feature extraction on each history symbol while a mix-block models the correlation between extracted features and estimates the probability. A progressive AR-based compression framework (PAC) is then proposed, which only requires one history symbol from the host at a time rather than the whole history symbol set. In addition, we introduced a trainable matrix multiplication to model the ordered importance, replacing previous hardware-unfriendly Gumble-Softmax sampling. The in-batch distribution variation problem is caused by AR-based compression's structured batch construction. Based on this observation, a batch-location-aware individual block is proposed to capture the heterogeneous in-batch distributions precisely, improving the performance without efficiency losses. Experimental results show the proposed framework can achieve an average of 130% speed improvement with an average of 3% compression ratio gain across data domains compared to the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247866"}, {"primary_key": "1120110", "vector": [], "sparse_vector": [], "title": "ReRAM-based graph attention network with node-centric edge searching and hamming similarity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Can Li"], "summary": "The graph attention network (GAT) has demonstrated its advantages via local attention mechanism but suffered from low energy and latency efficiency when implemented on conventional von-Neumann hardware. This work proposes and experimentally demonstrates an algorithm-hardware co-designed GAT that runs efficiently and reliably in ReRAM-based hardware. The neighborhood information is retrieved from trained node embeddings stored on crossbars in a single time step, and attention is implemented by efficient hashing and hamming similarity for higher robustness. Our scaled simulation based on the experimentally-validated model shows only 0.9% accuracy loss with over 35,500x energy improvement on the Cora dataset compared with GPU, and 1.1% accuracy improvement with 2× energy improvement compared with state-of-the-art ReRAM-based GNN accelerator.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247735"}, {"primary_key": "1120111", "vector": [], "sparse_vector": [], "title": "XPert: Peripheral Circuit &amp; Neural Architecture Co-search for Area and Energy-efficient Xbar-based Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The hardware-efficiency and accuracy of Deep Neural Networks (DNNs) implemented on In-memory Computing (IMC) architectures primarily depend on the DNN architecture and the peripheral circuit parameters. It is therefore essential to holistically co-search the network and peripheral parameters to achieve optimal performance. To this end, we propose XPert, which co-searches network architecture in tandem with peripheral parameters such as the type and precision of analog-to-digital converters, crossbar column sharing and the layer-specific input precision using an optimization-based design space exploration. Compared to VGG16 baselines, XPert achieves 10.24× (4.7×) lower EDAP, 1.72× (1.62×) higher TOPS/W, 1.93× (3×) higher TOPS/mm 2 at 92.46% (56.7%) accuracy for CIFAR10 (TinyImagenet) datasets. The code for this paper is available at the following Github link", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247676"}, {"primary_key": "1120112", "vector": [], "sparse_vector": [], "title": "On the Unpredictability of SPICE Simulations for Side-Channel Leakage Verification of Masked Cryptographic Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Circuits for cryptography are vulnerable to side-channel (SC) attacks. Masking is a countermeasure which splits secrets into random shares. It is provable secure under the assumption that physical leakage of each share is independent of each other. For a secure implementation of masked circuits, this independency assumption must be satisfied after layout. A transistor-level simulator such as SPICE produces analog waveforms that are sufficiently trustworthy to verify timing accuracy. Due to this accuracy, SPICE is expected to be useful for SC leakage verification after layout. However, we demonstrate that the statistical variation of the power noise amplitude in SPICE simulation is not always correct and varies a lot for SC evaluation. We believe it results from the internal time-step creation optimized for efficiency. It causes false-positives in the verification of security order. A small nonlinear function with a domain-oriented masking scheme is used to demonstrate these SPICE-simulation anomalies.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247834"}, {"primary_key": "1120113", "vector": [], "sparse_vector": [], "title": "DmTSAR-ILP: Allocating a Unified Domain Platform for Streaming Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Domain-specific HWACC-rich platforms blend high performance & efficiency presenting an opportunity to recover non-recurring engineering costs through wider deployment for many applications. However, the design of such platforms is immensely challenging, partly due to the design space size.MG-DmDSE [1] allocates domain platforms catering to multiple applications. To cope with complexity, it employs a genetic algorithm and heuristics. While this is a typical approach in DSE, it cannot guarantee optimality. Exact solutions could be obtained with integer linear programming (ILP). Yet, the multi-application DSE presents aggregation challenges as applications might have widely different performance, leading to non-linear aggregation techniques in previous work that ILPs cannot employ.This work introduces DmTSAR-ILP, an ILP-based platform allocation method that simultaneously considers all applications in a domain. DmTSAR-ILP captures the same underlying analytical model as MG-DmDSE and can explore its domain application set. To enable our linear domain-level formulation, we introduce the average performance achievement metric (APA), offering an ILP-friendly, fair aggregation across domain applications.To highlight benefits of DmTSAR-ILP, a domain platform for 40 OpenVX apps is generated (across varying area budgets). For all area budgets, DmTSAR-ILP platforms perform better or identical to MG-DmDSE even in their original metric. The proposed APA aggregation is fair, allowing more applications (55% vs 37.5% of applications in MG-DmDSE) to be fully accelerated on the generated platform. For the 0.1 mm 2 area budget, DmTSAR-ILP's platform increases application throughput by 22.5% over MG-DmDSE's platform. DmTSAR-ILP is 70x faster on average than the heuristic-based MG-DmDSE by leveraging an ILP formulation that considers all applications simultaneously and recent advances in MIP solver performance.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247833"}, {"primary_key": "1120114", "vector": [], "sparse_vector": [], "title": "WinoTrain: Winograd-Aware Training for Accurate Full 8-bit Convolution Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Efficient inference is critical in realizing a low-power, real-time implementation of convolutional neural networks (CNNs) on compute and memory-constrained embedded platforms. Using quantization techniques and fast convolutional algorithms like Winograd, CNN inference can achieve benefits in latency and in energy consumption. Performing Winograd convolution involves (1) transforming the weights and activations to the Winograd domain, (2) performing element-wise multiplication on the transformed tensors, and (3) transforming the results back to the conventional spatial domain. Combining Winograd with quantization of all its steps results in severe accuracy degradation due to numerical instability. In this paper we propose a simple quantization-aware training technique, which quantizes all three steps of the Winograd convolution, while using a minimal number of scaling factors. Additionally, we propose an FPGA accelerator employing tiling and unrolling methods to highlight the performance benefits of using the full 8-bit quantized Winograd algorithm. We achieve 2× reduction in inference time compared to standard convolution on ResNet-18 for the ImageNet dataset, while improving the Top-1 accuracy by 55.7 p.p. compared to a standard post-training quantized Winograd variant of the network.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247805"}, {"primary_key": "1120115", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Memory-Centric Computing.", "authors": ["<PERSON><PERSON>"], "summary": "Modern computing systems are processor-centric. Data processing (i.e., computation) happens only in the processor (e.g., a CPU, GPU, FPGA, ASIC). As such, data needs to be moved from where it is generated/captured (e.g., sensors) and stored (e.g., storage and memory devices) to the processor before it can be processed. The processor-centric design paradigm greatly limits the performance & energy-efficiency, as well as scalability & sustainability, of modern computing systems. Many studies show that even the most powerful processors and accelerators waste a large fraction (e.g., >60%) of their time simply waiting for data and energy on moving data between storage/memory units to the processor. This is so even though most of the hardware real estate of such systems is dedicated to data storage and communication (e.g., many levels of caches, DRAM chips, storage systems, and interconnects).Memory-centric computing aims to enable computation capability in and near all places where data is generated and stored. As such, it can greatly reduce the large negative performance and energy impact of data access and data movement, by fundamentally avoiding data movement and reducing data access latency & energy. Many recent studies show that memory-centric computing can greatly improve system performance and energy efficiency. Major industrial vendors and startup companies have also recently introduced memory chips that have sophisticated computation capabilities.This talk describes promising ongoing research and development efforts in memory-centric computing. We classify such efforts into two major fundamental categories: 1) processing using memory, which exploits analog operational properties of memory structures to perform massively-parallel operations in memory, and 2) processing near memory, which integrates processing capability in memory controllers, the logic layer of 3D-stacked memory technologies, or memory chips to enable high-bandwidth and low-latency memory access to near-memory logic. We show both types of architectures (and their combination) can enable orders of magnitude improvements in performance and energy consumption of many important workloads, such as graph analytics, databases, machine learning, video processing, climate modeling, genome analysis. We discuss adoption challenges for the memory-centric computing paradigm and conclude with some research & development opportunities.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247896"}, {"primary_key": "1120116", "vector": [], "sparse_vector": [], "title": "Invited: Accelerating Genome Analysis via Algorithm-Architecture Co-Design.", "authors": ["<PERSON><PERSON>", "Can Firtina"], "summary": "High-throughput sequencing (HTS) technologies have revolutionized the field of genomics, enabling rapid and cost-effective genome analysis for various applications. However, the increasing volume of genomic data generated by HTS technologies presents significant challenges for computational techniques to effectively analyze genomes. To address these challenges, several algorithm-architecture co-design works have been proposed, targeting different steps of the genome analysis pipeline. These works explore emerging technologies to provide fast, accurate, and low-power genome analysis.This paper provides a brief review of the recent advancements in accelerating genome analysis, covering the opportunities and challenges associated with the acceleration of the key steps of the genome analysis pipeline. Our analysis highlights the importance of integrating multiple steps of genome analysis using suitable architectures to unlock significant performance improvements and reduce data movement and energy consumption. We conclude by emphasizing the need for novel strategies and techniques to address the growing demands of genomic data generation and analysis.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247887"}, {"primary_key": "1120117", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Can memory technologies meet demands of data abundant applications?", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Ferroelectric-based devices and circuits are emerging as a key solution for supporting the storage and computing needs of data-abundant applications. Co-design across the stack from materials to architectures will be vital to addressing cross-cutting challenges posed by the enormity of data that needs to be processed. This talk will highlight opportunities and challenges for ferroelectric electronics.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247853"}, {"primary_key": "1120118", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Configurable Ring Oscillators as a Side-Channel Countermeasure.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Side-channel attacks are a threat to computing devices. In this work, we propose a novel countermeasure against power analysis side-channel attacks. This countermeasure uses ring oscillators with runtime-configurable chain lengths to generate noise to hide the effects of the secret intermediate values on the device's power consumption. We develop our countermeasure to be compatible with a state-of-the-art of side-channel-attack detection mechanism. Therefore, our solution does not incur any extra area overhead as it uses a subset of the circuit needed for detection. We evaluate our countermeasure using the test vector leakage assessment test (TVLA test). When our countermeasure is active no side-channel leakage could be detected.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247786"}, {"primary_key": "1120119", "vector": [], "sparse_vector": [], "title": "Don&apos;t Cross Me! Cross-layer System Security.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Chongzhou Fang", "<PERSON> Man<PERSON>j P. D.", "<PERSON><PERSON>"], "summary": "The computing landscape has undergone significant transformations in recent decades. Modern computation systems involve multiple layers across software and hardware architecture, exposing various security vulnerabilities that can be exploited by attackers. In this paper, we review security threats in these systems and provide insights into future directions in the topic of cross-layer security.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247832"}, {"primary_key": "1120120", "vector": [], "sparse_vector": [], "title": "Invited: Buried Power Rails and Back-side Power Grids: Prospects and Challenges.", "authors": ["S. <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Buried power rails and back-side power grids are promising technology-scaling boosters for advanced CMOS technology nodes. System-level evaluation of these technologies shows tremendous promise from power-performance-area (PPA), IR drop, and dynamic voltage droop perspective. However, several process, device, and architectural challenges must be addressed to realize the full potential of this technology. This article reviews the advancements and challenges in successfully adopting buried power rail and back-side power grid technology.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247775"}, {"primary_key": "1120121", "vector": [], "sparse_vector": [], "title": "HAWEN: Hardware Accelerator for Thread Wake-Ups in Linux Event Notification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jiyuan Shi", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The performance of multi-threaded applications relies on efficient inter-process communication. One common practice is putting a thread asleep while waiting for a certain condition. Exemplary Linux kernel mechanisms that use this practice include futex, sockets, epoll, eventfd and pipe. Once the condition is met, i.e., the associated event has occurred, the waiting thread is notified. Optimizations for event notification mechanisms in Linux mostly target the thread which receives events. Contrarily, we identified high potential in relieving the event-generating thread and propose HAWEN, a hardware accelerator for thread wake-up support. HAWEN has been integrated into Linux event notification in a minimally intrusive manner. Gem5-based multi-core architecture simulations revealed up to 80% faster thread wake-up times and a 53% shorter event-generating syscall.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247823"}, {"primary_key": "1120122", "vector": [], "sparse_vector": [], "title": "An Extension to Basis-Hypervectors for Learning from Circular Data in Hyperdimensional Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hyperdimensional Computing (HDC) is a computation framework based on random vector spaces, particularly useful for machine learning in resource-constrained environments. The encoding of information to the hyperspace is the most important stage in HDC. At its heart are basis-hypervectors, responsible for representing atomic information. We present a detailed study on basis-hypervectors, leading to broad contributions to HDC: 1) an improvement for level-hypervectors, used to encode real numbers; 2) a method to learn from circular data, an important type of information never before addressed in HDC. Results indicate that these contributions lead to considerably more accurate models for classification and regression.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247736"}, {"primary_key": "1120123", "vector": [], "sparse_vector": [], "title": "SEO: Safety-Aware Energy Optimization Framework for Multi-Sensor Neural Controllers at the Edge.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Runtime energy management has become quintessential for multi-sensor autonomous systems at the edge for achieving high performance given the platform constraints. Typical for such systems, however, is to have their controllers designed with formal guarantees on safety that precede in priority such optimizations, which in turn limits their application in real settings. In this paper, we propose a novel energy optimization framework that is aware of the autonomous system's safety state, and leverages it to regulate the application of energy optimization methods so that the system's formal safety properties are preserved. In particular, through the formal characterization of a system's safety state as a dynamic processing deadline, the computing workloads of the underlying models can be adapted accordingly. For our experiments, we model two popular runtime energy optimization methods, offloading and gating, and simulate an autonomous driving system (ADS) use-case in the CARLA simulation environment with performance characterizations obtained from the standard Nvidia Drive PX2 ADS platform. Our results demonstrate that through a formal awareness of the perceived risks in the test case scenario, energy efficiency gains are still achieved (reaching 89.9%) while maintaining the desired safety properties.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247751"}, {"primary_key": "1120124", "vector": [], "sparse_vector": [], "title": "System-level Innovation for the Next Decade AI Performance, Power, Area with Co-optimization.", "authors": ["<PERSON>"], "summary": "Power, performance, and area (PPA) are three key parameters in conventional system-on-chip design optimization. In post <PERSON>'s law era, system-level PPA improvement for system-of-chips/chiplets – a new \"SoC\" – relies on multi-disciplinary AI co-optimization across full stack innovations including process-in memory, edge intelligent sensing and heterogenous integration.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247938"}, {"primary_key": "1120125", "vector": [], "sparse_vector": [], "title": "HUNTER: Releasing Persistent Memory Write Performance with A Novel PM-DRAM Collaboration Architecture.", "authors": ["Yanqi Pan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present HUNTER, a POSIX-compliant persistent memory (PM) file system that fully releases PM's write performance. Compared to state-of-the-art ones, HUNTER proposes a novel PM-DRAM collaboration architecture to significantly eliminate/reduce software overheads in the write path. Expensive in-PM metadata are updated asynchronously to hide their performance penalties. Furthermore, in-PM metadata/data are laid out separately for locality awareness, enabling collaboration with asynchronous architecture. HUNTER also adopts several lightweight in-DRAM allocators/indexes to manage PM efficiently.Experimental results suggest that HUNTER achieves 2.0–3.4× write bandwidth compared to state-of-the-art PM file systems in write-intensive workloads and shows similar write bandwidth compared to bare PM.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247940"}, {"primary_key": "1120126", "vector": [], "sparse_vector": [], "title": "NTT-PIM: Row-Centric Architecture and Mapping for Efficient Number-Theoretic Transform on PIM.", "authors": ["Jaewoo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently DRAM-based PIMs (processing-in-memories) with unmodified cell arrays have demonstrated impressive performance for accelerating AI applications. However, due to the very restrictive hardware constraints, PIM remains an accelerator for simple functions only. In this paper we propose NTT-PIM, which is based on the same principles such as no modification of cell arrays and very restrictive area budget, but shows state-of-the-art performance for a very complex application such as NTT, thanks to features optimized for the application's characteristics, such as in-place update and pipelining via multiple buffers. Our experimental results demonstrate that our NTT-PIM can outperform previous best PIM-based NTT accelerators in terms of runtime by 1.7 ∼ 17× while having negligible area and power overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247747"}, {"primary_key": "1120127", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Efficient Embedded Machine Learning Deployment on Edge and IoT Devices.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "There has been rapid growth in the use of machine learning (ML) software in emerging edge and IoT systems. ML software deployments enable analytics and pattern recognition for multi-modal data (e.g., audio, images/video, wireless signals, air quality) obtained from embedded sensors and transceivers. However, resource constraints in edge and IoT platforms make it challenging to meet quality-of-service and real-time goals. The growing complexity of ML also exacerbates these issues. We discuss the challenges of ML software deployment in edge and IoT platforms, present strategies to ease deployment, and discuss case studies from the automotive, indoor navigation, and hardware/software co-design domains.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247845"}, {"primary_key": "1120128", "vector": [], "sparse_vector": [], "title": "Invited: Building Robust Quantum System Software for Technology-Specific Characteristics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper discusses the various technologies used for quantum computing and highlights the need for compiler and software stack solutions that are portable across different technologies (beyond superconducting qubit quantum computers) while providing a higher-level interface that allows scientists to run their programs in a technology-agnostic manner. To achieve this, quantum compilers and architecture designs must not be bound by classical-style standards and specifications. As a first step toward tackling this challenge, this paper then focuses on developing a compiler solution for neutral atom quantum computing technology, which has several potential benefits over superconducting qubit quantum computing technology.These benefits include a greater connectivity of qubits within the Rydberg interaction radius, which allows for fewer SWAP operations, and the ability to execute multi-qubit gates directly. However, neutral atom quantum computers have a different set of constraints and requirements, including interaction blockades, which can result in potential serialization of operations, reducing some of the gains due to the better connectivity of neutral atom quantum computers. The paper then concludes by stating that addressing these challenges requires further research and development in the field.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248010"}, {"primary_key": "1120129", "vector": [], "sparse_vector": [], "title": "PROPHET: Predictive On-Chip Power Meter in Hardware Accelerator for DNN.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "On-chip power meters play a critical role in power management by generating timely and accurate power traces at runtime. However, both performance-counter-based and existing RTL-based on-chip power meters have difficulty in providing sufficient response time for fast power and voltage management scenarios. Additionally, they can be costly to implement for large-scale DNN accelerators with many homogeneous process elements. To address these limitations, this paper proposes PROPHET, a data-pattern-based predictive on-chip power meter targeting multiply-accumulate-based DNN accelerators. By sampling pre-defined data patterns during memory access, PROPHET can predict power consumption before it actually happens. In our experiments, PROPHET predicts power consumption dozens of clock cycles in advance, with a temporal resolution of 4 clock cycles and NMAE < 7% and area overhead < 2% for various systolic-array-based DNN accelerators. PROPHET has the potential to enable fine-grained power management and optimization for large-scale DNN accelerators, improving their energy efficiency.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247979"}, {"primary_key": "1120130", "vector": [], "sparse_vector": [], "title": "PASNet: Polynomial Architecture Search Framework for Two-party Computation-based Secure Neural Network Deployment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two-party computation (2PC) is promising to enable privacy-preserving deep learning (DL). However, the 2PC-based privacy-preserving DL implementation comes with high comparison protocol overhead from the non-linear operators. This work presents PASNet, a novel systematic framework that enables low latency, high energy efficiency & accuracy, and security-guaranteed 2PC-DL by integrating the hardware latency of the cryptographic building block into the neural architecture search loss function. We develop a cryptographic hardware scheduler and the corresponding performance model for Field Programmable Gate Arrays (FPGA) as a case study. The experimental results demonstrate that our light-weighted model PASNet-A and heavily-weighted model PASNet-B achieve 63 ms and 228 ms latency on private inference on ImageNet, which are 147 and 40 times faster than the SOTA CryptGPU system, and achieve 70.54% & 78.79% accuracy and more than 1000 times higher energy efficiency. The pretrained PASNet models and test code can be found on Github 1 .", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247663"}, {"primary_key": "1120131", "vector": [], "sparse_vector": [], "title": "PowerPruning: Selecting Weights and Activations for Power-Efficient Neural Network Acceleration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) have been successfully applied in various fields. A major challenge of deploying DNNs, especially on edge devices, is power consumption, due to the large number of multiply-and-accumulate (MAC) operations. To address this challenge, we propose PowerPruning, a novel method to reduce power consumption in digital neural network accelerators by selecting weights that lead to less power consumption in MAC operations. In addition, the timing characteristics of the selected weights together with all activation transitions are evaluated. The weights and activations that lead to small delays are further selected. Consequently, the maximum delay of the sensitized circuit paths in the MAC units is reduced even without modifying MAC units, which thus allows a flexible scaling of supply voltage to reduce power consumption further. Together with retraining, the proposed method can reduce power consumption of DNNs on hardware by up to 73.9% with only a slight accuracy loss.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247868"}, {"primary_key": "1120132", "vector": [], "sparse_vector": [], "title": "AGD: A Learning-based Optimization Framework for EDA and its Application to Gate Sizing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In electronic design automation (EDA), most simulation models are not differentiable, and many design choices are discrete. As a result, greedy optimization methods based on numerical gradients have been used widely, although it suffers from suboptimal solutions. On the other hand, analytic methods may offer better solutions, but at the cost of enormous research efforts. Reinforcement learning (RL) has been leveraged to tackle this problem owing to its generality; however, RL also suffers from notorious sample inefficiency, which is exaggerated in EDA because data sampling in EDA is very expensive due to slow simulations. This paper proposes an alternative to RL for EDA, namely analytic gradient descent (AGD). Our method calculates analytic gradients of a design objective with respect to continuous and discrete design choices through a neural network learned by a simulation model. Then it performs a gradient descent procedure optimizing the design objective directly. We demonstrate AGD on the well-known gate sizing problem and show that our method can be very close to an industry-leading commercial tool in terms of design quality of result (QoR), while it only takes several person-months in comparison to dedicated efforts of human engineering over decades to develop. In addition, we also show that AGD can generalize to unseen circuits, with less training specific in a small amount of execution time.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247924"}, {"primary_key": "1120133", "vector": [], "sparse_vector": [], "title": "Specialization meets Flexibility: a Heterogeneous Architecture for High-Efficiency, High-flexibility AR/VR Processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging AR-VR applications execute complex heterogeneous workloads, mixing Deep-Learning(DL) and Digital-Signal-Processing(DSP) tasks, on SoCs embedded in the frame of eyeglasses, with implied tight power and area constraints, especially in the case of AR. We propose ArchiMEDES, an open-source heterogeneous-SoC platform with a programmable cluster of RISC-V cores coupled with a configurable DNN engine (NEureka) targeting AR/VR workloads. ArchiMEDES features a low-overhead Heterogeneous Cluster Interconnect(HCI) to enable fast RISC-V/NEureka cooperation on a shared tightly coupled data memory (TCDM). We show post-layout results targeting 22nm technology; ArchiMEDES shows a peak combined performance of up to 1.19 TOPS and an efficiency of up to 10.6 TOPS/W. Hardware-Software cooperation in ArchiMEDES enables a 5.5× speedup in an AR-VR gaze tracking case study, compared to a non-cooperative single-RISC-V + Accelerator system.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247945"}, {"primary_key": "1120134", "vector": [], "sparse_vector": [], "title": "Invited: Automated Code generation for Information Technology Tasks in YAML through Large Language Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recent improvement in code generation capabilities due to the use of large language models has mainly benefited general purpose programming languages. Domain specific languages, such as the ones used for IT Automation, received far less attention, despite involving many active developers and being an essential component of modern cloud platforms. This work focuses on the generation of Ansible YAML, a widely used markup language for IT Automation. We present Ansible Wisdom, a natural-language to Ansible YAML code generation tool, aimed at improving IT automation productivity. Results show that Ansible Wisdom can accurately generate Ansible script from natural language prompts with performance comparable or better than existing state of the art code generation models.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247987"}, {"primary_key": "1120135", "vector": [], "sparse_vector": [], "title": "PSMiner: A Pattern-Aware Accelerator for High-Performance Streaming Graph Pattern Mining.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ligang He", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Streaming Graph Pattern Mining (GPM) has been widely used in many application fields. However, the existing streaming GPM solution suffers from many unnecessary explorations and isomorphism tests, while the existing static GPM ones require many repetitive operations to compute the full graph. In this paper, we propose a pattern-aware incremental execution approach and design the first streaming GPM accelerator called PSMiner, which integrates multiple optimizations to reduce redundant computation and improve computing efficiency. We have conducted extensive experiments. The results show that compared with the state-of-the-art software and hardware solutions, PSMiner achieves the average speedups of 770.9× and 60.4×, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247902"}, {"primary_key": "1120136", "vector": [], "sparse_vector": [], "title": "Compiler Optimization for Quantum Computing Using Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Any quantum computing application, once encoded as a quantum circuit, must be compiled before being executable on a quantum computer. Similar to classical compilation, quantum compilation is a sequential process with many compilation steps and numerous possible optimization passes. Despite the similarities, the development of compilers for quantum computing is still in its infancy—lacking mutual consolidation on the best sequence of passes, compatibility, adaptability, and flexibility. In this work, we take advantage of decades of classical compiler optimization and propose a reinforcement learning framework for developing optimized quantum circuit compilation flows. Through distinct constraints and a unifying interface, the framework supports the combination of techniques from different compilers and optimization tools in a single compilation flow. Experimental evaluations show that the proposed framework—set up with a selection of compilation passes from IBM's Qiskit and Quantinuum's TKET—significantly outperforms both individual compilers in 73% of cases regarding the expected fidelity. The framework is available on GitHub (https://github.com/cda-tum/MQTPredictor) as part of the Munich Quantum Toolkit (MQT).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248002"}, {"primary_key": "1120137", "vector": [], "sparse_vector": [], "title": "ADAPTIVE: Agent-Based Learning for Bounding Time in Mixed-Criticality Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In Mixed-Criticality (MC) systems, the high Worst-Case Execution Time (WCET) of a task is a pessimistic bound, the maximum execution time of the task under all circumstances, while the low WCET should be close to the actual execution time of most instances of the task to improve utilization and Quality-of-Service (QoS). Most MC systems consider a static low WCET for each task which cannot adapt to dynamism at run-time. In this regard, we consider the run-time behavior of tasks and propose a learning-based approach that dynamically monitors the tasks' execution times and adapts the low WCETs to determine the ideal trade-off between mode-switches, utilization, and QoS. Based on our observations on running embedded real-time benchmarks on a real platform, the proposed scheme improves the QoS by 16.4% on average while reducing the utilization waste by 17.7%, on average, compared to state-of-the-art works.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248007"}, {"primary_key": "1120138", "vector": [], "sparse_vector": [], "title": "EP-ORAM: Efficient NVM-Friendly Path Eviction for Ring ORAM in Hybrid Memory.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies showed that only ORAM (oblivious RAM) can securely protect memory access patterns (i.e., data privacy) on modern computer systems. Ring ORAM is a promising ORAM protocol as it demands O(1) memory accesses for servicing each user memory request. However, Ring ORAM exhibits low memory utilization, i.e., its memory requirement is 4.8× of the protected user space. While adopting NVM (non-volatile memory) can alleviate the memory requirement, a simple implementation tends to introduce large performance degradation, preventing its adoption in practice.In this paper, we propose EP-ORAM, an NVM-friendly Ring ORAM implementation on DRAM/NVM hybrid memory. EP-ORAM is developed based on two key observations: (1) for tree-based Ring ORAM memory organization, saving bottom levels in NVM can dramatically reduce the DRAM memory requirement; (2) the tradeoffs among Ring ORAM operations expose design opportunities without security compromise. We, therefore, propose to save the bottom levels of the ORAM tree in NVM and shorten the path of EvictPath operation, which not only mitigates the number of NVM writes but also speeds up the execution. Our experimental results show that, under the design constraints of similar performance as the baseline that saves two bottom levels in NVM, EP-OR<PERSON> helps to save three levels in NVM, achieving 50% DRAM space reduction. In addition, EP-OR<PERSON> reduces the NVM writes by 15%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247714"}, {"primary_key": "1120139", "vector": [], "sparse_vector": [], "title": "Architecture 2.0: Challenges and Opportunities.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Machine learning driven computer architecture tools and methods have the potential to drastically shape the future of computer architecture. The question is: how can we lay the foundation to effectively usher in this era? In this post, we delve into the transformative impact of machine learning (ML) on the research landscape, emphasizing the importance of understanding both its potential and pitfalls to fully support ML-assisted computer architecture research. By exploring these advancements, our aim is to highlight the opportunities that lie ahead and outline the collective steps that we, as a community, can take towards realizing the era of \"Architecture 2.0.\"", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247808"}, {"primary_key": "1120140", "vector": [], "sparse_vector": [], "title": "Flex-SFU: Accelerating DNN Activation Functions by Non-Uniform Piecewise Approximation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern DNN workloads increasingly rely on activation functions consisting of computationally complex operations. This poses a challenge to current accelerators optimized for convolutions and matrix-matrix multiplications. This work presents Flex-SFU, a lightweight hardware accelerator for activation functions implementing non-uniform piecewise interpolation supporting multiple data formats. Non-Uniform segments and floating-point numbers are enabled by implementing a binary-tree comparison within the address decoding unit. An SGD-based optimization algorithm with heuristics is proposed to find the interpolation function reducing the mean squared error. Thanks to non-uniform interpolation and floating-point support, Flex-SFU achieves on average 22.3x better mean squared error compared to previous piecewise linear interpolation approaches. The evaluation with more than 700 computer vision and natural language processing models shows that Flex-SFU can, on average, improve the end-to-end performance of state-of-the-art AI hardware accelerators by 35.7%, achieving up to 3.3x speedup with negligible impact in the models' accuracy when using 32 segments, and only introducing an area and power overhead of 5.9% and 0.8% relative to the baseline vector processing unit.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247855"}, {"primary_key": "1120141", "vector": [], "sparse_vector": [], "title": "CHAM: A Customized Homomorphic Encryption Accelerator for Fast Matrix-Vector Product.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yanheng Lu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Chu", "<PERSON>", "Changzheng Wei", "<PERSON><PERSON>", "<PERSON>"], "summary": "Homomorphic encryption (HE) is a promising technique for privacy-preserving computing because it allows computation on encrypted data without decryption. HE, however, suffers from poor performance due to enlarged data size and exploded amount of computation. Related work has been proposed to accelerate HE using GPUs, FPGAs, and ASICs. The existing work, however, aims at specific HE schemes and fails to consider the fast-evolving algorithms. For example, HE algorithms that combine different HE schemes have demonstrated capability of supporting more types of HE operations and ciphertexts. Moreover, some existing hardware accelerators target small HE operations (such as number theoretic transform and key-switch), which however provides limited or even neglected performance improvement for end-to-end applications. To better support existing privacy-preserving applications (e.g., logistic regression and neural network inference), we propose CHAM, an HE accelerator, for high-performance matrix-vector product, which can be easily extended to 2-D and 3-D convolutions. Motivated by the evolution of algorithms, CHAM supports not only traditional HE operations, but also different types of ciphertexts and the conversion between them. We implement CHAM with Xilinx FPGAs. The evaluation demonstrates 1800× speed-up for matrix-vector product, 36× speed-up for logistic regression, and 144× speed-up for Beaver triple generation compared to the existing work.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247696"}, {"primary_key": "1120142", "vector": [], "sparse_vector": [], "title": "AccShield: a New Trusted Execution Environment with Machine-Learning Accelerators.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning accelerators such as the Tensor Processing Unit (TPU) are already being deployed in the hybrid cloud, and we foresee such accelerators proliferating in the future. In such scenarios, secure access to the acceleration service and trustworthiness of the underlying accelerators become a concern. In this work, we present AccShield, a new method to extend trusted execution environments (TEEs) to cloud accelerators which takes both isolation and multi-tenancy into security consideration. We demonstrate the feasibility of accelerator TEEs by a proof of concept on an FPGA board. Experiments with our prototype implementation also provide concrete results and insights for different design choices related to link encryption, isolation using partitioning and memory encryption.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247768"}, {"primary_key": "1120143", "vector": [], "sparse_vector": [], "title": "HBP: Hierarchically Balanced Pruning and Accelerator Co-Design for Efficient DNN Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Weight pruning is studied to accelerate DNN inference by reducing the parameters and computations. Irregular pruning achieves high sparsity while incurring low computation parallelism and imbalanced workloads. The coarse-grained structured pruning sacrifices sparsity for higher parallelism. To strike a better balance, we propose Hierarchically Balanced Pruning by applying fine-grained but structured adjustments based on irregular pruning. Besides, it partitions the weight matrix into hierarchical blocks and constrains the sparsity of the blocks for balanced workloads. Furthermore, an accelerator is proposed to unleash the power of the pruning method. Experimental results show our method achieves 1.1×-6 higher sparsity than prior studies, and the accelerator achieves 1.2×-13× speedup and 3.3× energy efficiency improvement than its counterparts.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247785"}, {"primary_key": "1120144", "vector": [], "sparse_vector": [], "title": "Thermal Scaffolding for Ultra-Dense 3D Integrated Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the thermal challenge of ultra-dense 3D (e.g., monolithic 3D) integrated circuits with multiple high-speed computing engines in the 3D stack. We present a new thermal scaffolding approach achieved through a combination of (1) new Back-End-of-Line (BEOL)-compatible dielectric materials for simultaneous high thermal conductivity and low dielectric constant, (2) new 3D physical co-design of BEOL dielectrics with thermal metal structures for uniform heat conduction with minimal metal insertion overhead, and (3) previous, experimentally demonstrated heatsink advances. Physical designs of thermal scaffolding enable 12-tier 7nm ultra-dense 3D IC with max temperatures ≤125 degrees Celsius: an iso-footprint, iso-delay, 4x improvement in stacked tiers.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120145", "vector": [], "sparse_vector": [], "title": "An Iterative Method for Mapping-Aware Frequency Regulation in Dataflow Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dataflow circuits promise to overcome the scheduling limitations of standard HLS solutions. However, their performance suffers due to timing overheads caused by their handshake communication protocol. Current pipelining solutions fail to account for logic optimizations that occur during FPGA synthesis, thus producing over-conservative results. In this work, we develop an FPGA mapping-aware timing regulation technique for dataflow circuits; it relies on FPGA synthesis information to identify the circuit's critical path and optimize it through register placement. Our dataflow circuits Pareto-dominate state-of-the-art solutions, with up to 29% and 21% execution time and area reduction, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120146", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Efficiency and Programmability of DNN Accelerators and GPUs.", "authors": ["Won Woo Ro"], "summary": "While GPUs have provided high computational power for AI applications, their power efficiency may not be satisfied for applications requiring a high-speed response within a low power budget. Specialized deep neural network (DNN) accelerators have been developed to address this challenge. However, there are still limitations and concerns surrounding the adoption of DNN accelerators as a viable choice for commercial products. This lightning talk aims to highlight the importance of fast and domain-specific DNN accelerators and discuss the additional features they should possess besides speed and power efficiency. By addressing these points, the talk will shed light on the necessity of specialized DNN accelerators and explore potential areas for improvement to meet the growing demands of efficient and high-performance AI computations.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247689"}, {"primary_key": "1120147", "vector": [], "sparse_vector": [], "title": "Hybrid Obfuscation of Chiplet-Based Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Subramanian S. <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The growing concern about offshore chip manufacturing has created considerable interest in solutions that can ensure the integrity and security of chips. Among various solutions, split manufacturing has received a lot of attention due to its security guarantees. With the recent emergence of new heterogeneous manufacturing technologies, including chiplet-based systems, there is a new opportunity for revisiting the design considerations for split manufacturing to fully exploit the opportunities presented by chiplet-based systems and improve various metrics, such as security, performance, and overhead.This work improves the state-of-the-art in secure chip manufacturing by proposing a new split manufacturing scheme. The key idea is to exploit the capabilities provided by chiplet integration technology for designing a new hybrid split manufacturing scheme that includes both vertical and horizontal splitting. Unlike existing vertical-only split manufacturing mechanisms, that target obfuscation of interconnections by splitting the design at a specific metallization layer into two portions, the proposed hybrid method increases trust by exploiting the chiplet paradigm shift, specifically, breaking the design into sub-designs, each represented by chiplets (independently fabricated), and obfuscating interconnections among them. The proposed obfuscation mechanism targets systems that exploit the chiplet technology to obtain important performance advantages, thus any chiplet-related overhead is not due to obfuscation. We evaluate our method using several experiments and compare it with the state-of-the-art using standard metrics, including area, power, delay, wirelength, and trust. Compared to conventional split manufacturing, our hybrid method achieves up to 245× higher trust, while exhibiting negligible overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247738"}, {"primary_key": "1120148", "vector": [], "sparse_vector": [], "title": "Design Automation for Cryogenic CMOS Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryogenic CMOS circuits operate at temperatures close to absolute zero and are essential in many applications such as controllers for quantum computing but also medical engineering, space technology, or physical instruments. However, operating circuits at cryogenic temperatures fundamentally changes the underlying semiconductor physics that governs the CMOS transistor—rendering existing design automation approaches infeasible. In this work, we propose and implement the first end-to-end approach that enables design automation for cryogenic CMOS circuits. To this end, we (1) perform the first-of-its-kind measurements of commercial 5nm FinFET transistors from 300K down to 10K, (2) use the results to validate and calibrate the first cryogenic-aware industrial-standard compact model for FinFET technology, (3) create cryogenic-aware standard cell libraries that are compatible with the existing EDA tool flows, and (4) propose an initial cryogenic-aware logic synthesis approach that re-uses established design automation expertise but optimizes it for cryogenic purposes. Evaluations, comparisons, and discussions of all these novel contributions confirm the applicability and validity of the resulting cryogenic-aware design automation flow.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247824"}, {"primary_key": "1120149", "vector": [], "sparse_vector": [], "title": "Lightning Talk: The Incredible Shrinking Black Box Model.", "authors": ["<PERSON>"], "summary": "A black box model is an assumption on the implementation of a cryptographic primitive to limit the capabilities of the attacker. Black boxes are a useful component in a proof of protocol correctness, but it is not obvious how to securely implement one in hardware. The current state of the art in tamper from open literature shows impressive efficiency and precision in prying open these black boxes. Secure hardware designers are compelled to shrink the black boxes with every new device generation, while making a careful assessment of the need to load, store and handle secrets in hardware.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247721"}, {"primary_key": "1120150", "vector": [], "sparse_vector": [], "title": "Advances and Trends on On-Chip Compute-in-Memory Macros and Accelerators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional AI accelerators have been bottle-necked by high volumes of data movement and accesses required between memory and compute units. A transformative approach that has emerged to address this in compute-in-memory (CIM) architectures, which perform computation in-place inside the volatile or non-volatile memory in an analog or digital manner, greatly reducing the data transfers and memory accesses. This paper presents recent advances and trends on CIM macros and CIM-based accelerator designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248014"}, {"primary_key": "1120151", "vector": [], "sparse_vector": [], "title": "Emerging Trends in Multi-Accelerator and Distributed System for ML: Devices, Architectures, Tools and Applications.", "authors": ["<PERSON>"], "summary": "As the complexity and diversity of machine/deep learning models is increasing at a rapid pace, multi-accelerator and distributed systems are becoming a critical component of the machine learning (ML) stack. Besides efficient compute engines and communication mechanisms, these systems also require intelligent strategies for mapping workloads to accelerators and memory management to achieve high performance and energy efficiency, while meeting the demands for high-performance ML/AI systems. This article presents an overview of the emerging trends in multi-accelerator and distributed systems designed for handling complex AI-powered application workloads.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247935"}, {"primary_key": "1120152", "vector": [], "sparse_vector": [], "title": "Invited: Compute Express Link™ (CXL™): An Open Interconnect for Cloud Infrastructure.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Compute Express Link is an open industry standard interconnect offering caching and memory semantics on top of PCI-Express (PCIe)®, with resource pooling and fabric capabilities. This paper delves into the role of CXL to solve some of the challenges in the cloud infrastructure.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247726"}, {"primary_key": "1120153", "vector": [], "sparse_vector": [], "title": "Lightning Talk: A Perspective on Neuromorphic Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neuromorphic computing, based on Spiking Neural Networks (SNNs), has recently gained immense popularity in machine learning community. It aims to offer reduced learning complexity, energy and latency through sparse event-driven computations, enabling real-time and sequential edge applications. However, due to their asynchronous spatio-temporal compute, SNNs require specialized sensing as well as algorithms and are not compatible with deployment on standard machine learning hardware such as GPUs. To that effect, there needs to be an end-to-end paradigm shift, from sensors to learning algorithms to the underlying hardware architectures. In this paper, we provide a perspective on the various efforts by the research community towards overcoming these challenges and realizing truly brain-inspired efficient machine intelligence.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247997"}, {"primary_key": "1120154", "vector": [], "sparse_vector": [], "title": "MES-Attacks: Software-Controlled Covert Channels based on Mutual Exclusion and Synchronization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multi-process concurrency is effective in improving program efficiency and maximizing CPU utilization. The correct execution of concurrency is ensured by the mutual exclusion and synchronization mechanism (MESM) that manages the shared hardware and software resources. We propose MES-Attacks, a new set of software-controlled covert channel attacks based on MESM to transmit confidential information. MES-Attacks offer several advantages: 1) the covert channels are constructed at software level and can be deployed on any hardware; 2) the closed share of resource ensures the quality of the channels with low interference and makes them hard to be detected; and 3) the attack utilizes system's software resources which are abound and hence difficult to isolate. We report the covert channels we have built with the following MESMs on Linux and Windows: flock, FileLockEX, Mutex, Semaphore, Event and WaitableTimer. Experimental results demonstrate that these covert channels can achieve transmission rate of 13.105 kb/s, 12.383 kb/s, and 6.552 kb/s, respectively in the scenarios of local, cross-sandbox and cross-virtual machine, all with bit error rate under 1%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247792"}, {"primary_key": "1120155", "vector": [], "sparse_vector": [], "title": "MARS: Exploiting Multi-Level Parallelism for DNN Workloads on Adaptive Multi-Accelerator Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Along with the fast evolution of deep neural networks, the hardware system is also developing rapidly. As a promising solution achieving high scalability and low manufacturing cost, multi-accelerator systems widely exist in data centers, cloud platforms, and SoCs. Thus, a challenging problem arises in multi-accelerator systems: selecting a proper combination of accelerators from available designs and searching for efficient DNN mapping strategies. To this end, we propose MARS, a novel mapping framework that can perform computation-aware accelerator selection, and apply communication-aware sharding strategies to maximize parallelism. Experimental results show that MARS can achieve 32.2% latency reduction on average for typical DNN workloads compared to the baseline, and 59.4% latency reduction on heterogeneous models compared to the corresponding state-of-the-art method.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247992"}, {"primary_key": "1120156", "vector": [], "sparse_vector": [], "title": "Muffin: A Framework Toward Multi-Dimension AI Fairness by Uniting Off-the-Shelf Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON>"], "summary": "Model fairness (a.k.a., bias) has become one of the most critical problems in a wide range of AI applications. An unfair model in autonomous driving may cause a traffic accident if corner cases (e.g., extreme weather) cannot be fairly regarded; or it will incur healthcare disparities if the AI model misdiagnoses a certain group of people (e.g., brown and black skin). In recent years, there are emerging research works on addressing unfairness, and they mainly focus on a single unfair attribute, like skin tone; however, real-world data commonly have multiple attributes, among which unfairness can exist in more than one attribute, called \"multi-dimensional fairness\". In this paper, we first reveal a strong correlation between the different unfair attributes, i.e., optimizing fairness on one attribute will lead to the collapse of others. Then, we propose a novel Multi-Dimension Fairness framework, namely Muffin, which includes an automatic tool to unite off-the-shelf models to improve the fairness on multiple attributes simultaneously. Case studies on dermatology datasets with two unfair attributes show that the existing approach can achieve 21.05% fairness improvement on the first attribute while it makes the second attribute unfair by 1.85%. On the other hand, the proposed Muffin can unite multiple models to achieve simultaneously 26.32% and 20.37% fairness improvement on both attributes; meanwhile, it obtains 5.58% accuracy gain.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247765"}, {"primary_key": "1120157", "vector": [], "sparse_vector": [], "title": "ZKROWNN: Zero Knowledge Right of Ownership for Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Training contemporary AI models requires investment in procuring learning data and computing resources, making the models intellectual property of the owners. Popular model watermarking solutions rely on key input triggers for detection; the keys have to be kept private to prevent discovery, forging, and removal of the hidden signatures. We present ZKROWNN, the first automated end-to-end framework utilizing Zero-Knowledge Proofs (ZKP) that enable an entity to validate their ownership of a model, while preserving the privacy of the watermarks. ZKROWNN permits a third party client to verify model ownership in less than a second, requiring as little as a few KBs of communication.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247798"}, {"primary_key": "1120158", "vector": [], "sparse_vector": [], "title": "Lightning Talk 21: EDA with ML, Rule-Based, or Both?", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Machine learning (ML) has been effectively applied to many applications these days mainly because huge amount of data is available through internet. This is not the case in semiconductor industry, where data is not shared between companies or even inside a single organization. ML model, in particular complex one with many parameters, is in danger of overfit when data volume is small and becomes inefficient.Rule-based (or expert) system has been considered a part of AI (artificial intelligence), which also includes ML, and has been popular in many EDA applications. This paper tries to compare the two methods when data volume is high and low. The expectation is that ML is more efficient with high data volume and rule-based is less sensitive to the amount of data and so can be a better choice in low data volume. In addition, combining both methods in a way that rules are revised with some guidance from ML model is investigated so that rule-based method can be a good option in low data volume.Two example applications are considered: OPC refragmentation, which has been addressed through random forest classifier (RFC) ML model [1], and placement utilization in low aspect ratio design, where CNN has been applied [3] to identify utilization distribution over layout sub-regions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247709"}, {"primary_key": "1120159", "vector": [], "sparse_vector": [], "title": "PVC-RAM:Process Variation Aware Charge Domain In-Memory Computing 6T-SRAM for DNNs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work introduces PVC-RAM, a process variation aware in-memory computing (IMC) static random-access memory (SRAM) macro designed for efficient convolutional neural network (CNN) inference. PVC-RAM is a charge-domain based compact IMC and is the first fully analog IMC for 4b-weights/4b-inputs MAC operation for deep neural networks in 6T-SRAM to the best of our knowledge. Further, PVC-RAM fully computes 4-bit MAC in the analog domain and requires fewer invocations of the ADCs. Implemented in 28nm technology, PVC-RAM achieves a bitwise throughput of 6964.48 TOPS, which is 1.5× higher than the SOTA, and a bitwise energy efficiency of 75.12 TOPS/W, which is 1.3× higher than the SOTA.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247893"}, {"primary_key": "1120160", "vector": [], "sparse_vector": [], "title": "Machine Learning-based Thermally-Safe Cache Contention Mitigation in Clustered Manycores.", "authors": ["<PERSON>", "<PERSON><PERSON>dr", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first technique that mitigates cache contention under thermal constraints in clustered manycores. We show by means of extensive experiments that significant performance gains in this scenario can be achieved. The background is that concurrently-running applications on manycore clusters compete for the shared cache, slowing down their execution. In addition, heavy parallel computations on physically-close cores increase temperatures to non-sustainable levels, which in turn triggers a throttle down of voltage/frequency levels and hence performance is compromised. These problems are not unknown, but as our analysis shows, tackling them independently is sub-optimal. We introduce the first task migration technique that jointly mitigates cache contention while enforcing the thermal constraint at the same time. It works in conjunction with cluster-level dynamic voltage and frequency scaling. Our technique needs to predict the impact of task migration on performance considering cache contention. Since it is impossible to derive an analytical model for cache contention that is both sufficiently accurate and practically feasible to implement, we employ an accurate, yet lightweight neural network (NN) model. As a result, we can operate the manycore system at higher performance while safely staying within thermal constraints. We report a significant step forward in this paper and unveil new potentials for performance optimization.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247708"}, {"primary_key": "1120161", "vector": [], "sparse_vector": [], "title": "Critical Paths Prediction under Multiple Corners Based on BiLSTM Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Critical path generation poses significant challenge to integrated circuit (IC) design flow in terms of huge computational complexity and crucial impact to circuit optimization, whose early prediction is of vital importance for accelerating the design closure, especially under multiple process-voltage-temperature (PVT) corners. In this work, a post-routing critical path prediction framework is proposed based on Bidirectional Long Short-Term Memory (BiLSTM) network and Multi-Layer Perceptron (MLP) network to learn from the sequential features and global features at logic synthesis stage, which are extracted from the timing and physical information of cell sequences and operation conditions for circuit respectively. Experimental results demonstrate that with the proposed framework, the average prediction accuracy of critical paths achieves 95.0% and 93.6% for seen and unseen circuits in terms of F1-score for ISCAS'89 benchmark circuits under TSMC 22nm process, demonstrating an increase of 10.8% and 13.9% compared with existing learning-based critical paths prediction method.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120162", "vector": [], "sparse_vector": [], "title": "Invited: Caching in Automated Data Centric Vehicles for Edge Computing Scenarios.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the current trend towards large data volumes, state-of-the-art communication and data management solutions based on the publish/subscribe pattern reach the network limits, hampering the deployment of advanced edge and cloud computing services. A main reason is the established publisher-centric distribution mechanism that over-utilizes network resources. We suggest a network and subscriber-centric adaptive data caching communication scheme supported by dynamic network management and configuration, which has great potential to solve the data distribution challenges. We show that state-of-the-art publish/subscribe middlewares like Data Distribution Service (DDS) can transparently be combined with such software caching.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247983"}, {"primary_key": "1120163", "vector": [], "sparse_vector": [], "title": "DSPIMM: A Fully Digital SParse In-Memory Matrix Vector Multiplier for Communication Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Channel decoders are key computing modules in wired/wireless communication systems. Recently neural network (NN)-based decoders have shown their promising error-correcting performance because of their end-to-end learning capability. However, compared with the traditional approaches, the emerging neural belief propagation (NBP) solution suffers higher storage and computational complexity, limiting its hardware performance. To address this challenge and develop a channel decoder that can achieve high decoding performance and hardware performance simultaneously, in this paper we take a first step towards exploring SRAM-based in-memory computing for efficient NBP channel decoding. We first analyze the unique sparsity pattern in the NBP processing, and then propose an efficient and fully Digital Sparse In-Memory Matrix vector Multiplier (DSPIMM) computing platform. Extensive experiments demonstrate that our proposed DSPIMM achieves significantly higher energy efficiency and throughput than the state-of-the-art counterparts.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247829"}, {"primary_key": "1120164", "vector": [], "sparse_vector": [], "title": "Fault Injection in Native Logic-in-Memory Computation on Neuromorphic Hardware.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logic-in-memory (LIM) describes the execution of logic gates within memristive crossbar structures, promising to improve performance and energy efficiency. Utilizing only binary values, LIM particularly excels in accelerating binary neural networks, shifting it in the focus of edge applications. Considering its potential, the impact of faults on BNNs accelerated with LIM still lacks investigation. In this paper, we propose faulty logic-in-memory (FLIM), a fault injection platform capable of executing full-fledged BNNs on LIM while injecting in-field faults. The results show that FLIM runs a single MNIST picture 66754× faster than the state of the art by offering a fine-grained fault injection methodology.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247742"}, {"primary_key": "1120165", "vector": [], "sparse_vector": [], "title": "STCG: State-Aware Test Case Generation for Simulink Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu <PERSON>"], "summary": "Simulink has been widely used in system design, which supports the efficient modeling and synthesis of embedded controllers, with automatic test case generation to simulate and validate the correctness of the constructed Simulink model. However, the increasing complexity of the model, especially the internal states, brings extra challenges to existing model testing techniques such as constraint solving and random search, which results in difficulties when trying to reach the deeper logic of the model effectively.In this paper, we propose STCG, a state-aware test case generation method for Simulink models. STCG solves only one iteration of the model each time to get the test input that can cover a target branch, then executes the model once to obtain and update the novel model state based on the solved input dynamically. Then, it solves the remaining branches based on the new model state iteratively until all the coverage requirements are satisfied. We implemented STCG and evaluated it on several benchmark Simulink models. Compared to the built-in Simulink Design Verifier and state-of-the-art academic work SimCoTest, STCG achieves an average improvement of 58% and 132% on Decision Coverage, 52% and 70% on Condition Coverage and 239% and 237% on Modified Condition Decision Coverage, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247787"}, {"primary_key": "1120166", "vector": [], "sparse_vector": [], "title": "PIMCOMP: A Universal Compilation Framework for Crossbar-based PIM DNN Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Crossbar-based PIM DNN accelerators can provide massively parallel in-situ operations. A specifically designed compiler is important to achieve high performance for a wide variety of DNN workloads. However, some key compilation issues such as parallelism considerations, weight replication selection, and array mapping methods have not been solved. In this work, we propose PIMCOMP - a universal compilation framework for NVM crossbar-based PIM DNN accelerators. PIMCOMP is built on an abstract PIM accelerator architecture, which is compatible with the widely used Crossbar/IMA/Tile/Chip hierarchy. On this basis, we propose four general compilation stages for crossbar-based PIM accelerators: node partitioning, weight replicating, core mapping, and dataflow scheduling. We design two compilation modes with different inter-layer pipeline granularities to support high-throughput and low-latency application scenarios, respectively. Our experimental results show that PIMCMOP yields improvements of 1.6× and 2.4× in throughput and latency, respectively, relative to PUMA.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247928"}, {"primary_key": "1120167", "vector": [], "sparse_vector": [], "title": "Efficient ILT via Multi-level Lithography Simulation.", "authors": ["Shuyuan Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Inverse Lithography Technology (ILT) is a widely investigated method to improve the yield of chip manufacturing. However, high computational complexity and difficulty in fabricating curvilinear shapes have hindered the widespread adoption of ILT. This paper presents an efficient ILT framework, including a multi-level resolution method for simulation acceleration, a downsampling strategy for mask optimization, and an improved mask binary function to improve mask printability. Experimental results show that the proposed method outperforms state-of-the-art methods with at least a 33.8% reduction in L2 loss and a 15.5% reduction in PVBand.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247704"}, {"primary_key": "1120168", "vector": [], "sparse_vector": [], "title": "Learning-based Data Separation for Write Amplification Reduction in Solid State Drives.", "authors": ["Penghao Sun", "Litong You", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ruoyan Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Shu Li", "<PERSON><PERSON><PERSON>"], "summary": "Garbage collection in SSDs causes write amplification. The key to mitigating this problem is separating data by lifetime. Prior works proposed using machine learning to accurately predict data lifetime but prediction is performed at the host side, burdening the host storage stack. We present PHFTL, a practical, holistic FTL design with device-side learning-based data separation. The machine learning model in PHFTL accurately and adaptively predicts the lifetime of every written page. A suite of enabling techniques are introduced to keep computation and storage overhead low. Extensive evaluation of PHFTL demonstrates superiority over state-of-the-art and feasibility on real hardware.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247795"}, {"primary_key": "1120169", "vector": [], "sparse_vector": [], "title": "PTStore: Lightweight Architectural Support for Page Table Isolation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Page tables are critical data structures in kernels, serving as the trust base of most mitigation solutions. Their integrity is thus crucial but is often taken for granted. Existing page table protection solutions usually provide insufficient security guarantees, require heavy hardware, or introduce high overheads. In this paper, we present a novel lightweight hardware-software co-design solution, PTStore, consisting of a secure region storing page tables and tokens verifying page table pointers. Evaluation results on FPGA-based prototypes show that PTStore only introduces <0.92% hardware overheads and <0.86% performance overheads, but provides strong security guarantees, showing that PTStore is efficient and effective.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247657"}, {"primary_key": "1120170", "vector": [], "sparse_vector": [], "title": "Reaction Time Analysis of Event-Triggered Processing Chains with Data Refreshing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many real-time systems process and react to external events by a chain of tasks, and have constraints on the maximum reaction time which describes how long it takes to respond to an external event. While a processing chain typically starts with a sampling task periodically triggered to sample the sensor data, other tasks in the chain could be triggered in two different ways: event-triggered or time-triggered, which have their own pros and cons. In this paper, we propose the third option to trigger the processing tasks in a chain, namely, the event-triggered with data refreshing approach, which combines the benefits of the event-triggered or time-triggered approaches. As the main technical contribution, we develop techniques to formally upper-bound its maximum reaction time and analytically compare it with the existing approaches. Experiments with synthetic workload are conducted to show the performance improvement by our proposed techniques.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248012"}, {"primary_key": "1120171", "vector": [], "sparse_vector": [], "title": "UpTime: Towards Flow-based In-Memory Computing with High Fault-Tolerance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Processing in-memory promises to accelerate data-intensive applications by breaking von-Neumann based design principles. Flow-based computing is an in-memory computing paradigm that has shown immense potential for executing Boolean logic. Unfortunately, the immature fabrication processes for nanoscale memristor crossbars still struggle with yield challenges and run-time defects, which may render the computing system non-functional. Even worse, no previous studies have investigated the fault-tolerance of flow-based computing systems, which could potentially limit the capabilities of the entire paradigm. In this paper, we propose the UpTime framework to provide guaranties on the functional correctness and to maximize the lifetime of flow-based computing systems. The framework utilizes data layout organization to mitigate errors from faults with known type and location. To handle defects occurring at run-time, we propose the use of an error detection signal that can be evaluated with low overhead. The experimental evaluation demonstrates that the UpTime framework is capable of guaranteeing functional correctness for an average of 15.24 years. The up-time to down-time ratio is 99.9992%. Compared with utilizing the state-of-the-art write-verify scheme, the proposed error signal reduces power consumption by 25% and increases throughput by 6%, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247692"}, {"primary_key": "1120172", "vector": [], "sparse_vector": [], "title": "A digital 3D TCAM accelerator for the inference phase of Random Forest.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hsiang-Pang Li", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Random forest is a popular ensemble machine-learning algorithm for classification and regression tasks. However, the irregular tree shapes and non-deterministic memory access patterns make it hard for the current von <PERSON> architecture to handle random forest efficiently. This paper proposes a digital 3D TCAM-based accelerator for the random forest, adopting the idea of processing-in-memory (PIM) to reduce data movement. By utilizing this accelerator, we propose a TCAM-based approach to provide real-time inference with low energy consumption, making it suitable for edge or embedded environments. In the experiments, the proposed approach achieves an average of 3.13 times higher throughput with 22 times more energy saving than the GPU approach.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247695"}, {"primary_key": "1120173", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: An Efficient Bridge-based Compression Algorithm for Topologically Quantum Error Corrected Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>seng", "<PERSON><PERSON><PERSON>"], "summary": "Topological quantum error correction (TQEC) is a promising method for fault-tolerant quantum circuits. A TQEC circuit can be visualized as the defect movement along the time axis and modeled as a 3D space-time volume to estimate the required resource. A quantum algorithm must minimize the space-time volume for a feasible physical qubit number and computational time, especially for large-scale designs. Previous work presents quadratic-time simultaneous primal and dual bridge compression for a TQEC circuit, which is infeasible for large-scale problems. This paper presents an efficient divide-and-conquer approach to primal and dual bridge compression, which can support different code distances between qubits. Compared with the previous work, experimental results show that our algorithm is effective and efficient even for large-scale problems.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247656"}, {"primary_key": "1120174", "vector": [], "sparse_vector": [], "title": "Equality Saturation for Datapath Synthesis: A Pathway to Pareto Optimality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Equality saturation, originally developed in the late 1970s for use in automated theorem provers, has been recently advanced to perform scalable rule-based rewriting for optimizations in various domains, such as program synthesis, compiler optimization, and datapath synthesis. Constructing an e-graph using rewrite rules that preserve program functionality, equality saturation addresses phase ordering problems in rewriting-driven optimizations. This promising approach shows significant potential for achieving Pareto optimality. This paper provides a brief introduction to equality saturation and the open-source tool egg, and highlights its potential application in optimizing datapaths in both RTL and high-level synthesis. We include case studies and outline the opportunities for future work in both datapath and logic synthesis using equality saturation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247948"}, {"primary_key": "1120175", "vector": [], "sparse_vector": [], "title": "Glass Interposer Integration of Logic and Memory Chiplets: PPA and Power/Signal Integrity Benefits.", "authors": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Glass interposers enable 3D stacking between the chiplets embedded into the substrate and the ones stacked directly on top, which is not possible in silicon. In this work, we demonstrate the benefits of such stacking in glass interposers over silicon in terms of key system-level metrics including area, wirelength, signal, power, and thermal integrity. We achieve this goal with GDS layouts of both chiplets and interposers and sign-off simulations. Our experiments show that glass offers 2.6X area, 21X wirelength, 17.72% full-chip power, 64.7% signal integrity, and 10X power integrity improvement over silicon at the cost of 15% increase in temperature.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247949"}, {"primary_key": "1120176", "vector": [], "sparse_vector": [], "title": "BERRY: Bit Error Robustness for Energy-Efficient Reinforcement Learning-Based Autonomous Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Autonomous systems, such as Unmanned Aerial Vehicles (UAVs), are expected to run complex reinforcement learning (RL) models to execute fully autonomous position-navigation-time tasks within stringent onboard weight and power constraints. We observe that reducing onboard operating voltage can benefit the energy efficiency of both the computation and flight mission, however, it can also result in on-chip bit failures that are detrimental to mission safety and performance. To this end, we propose BERRY, a robust learning framework to improve bit error robustness and energy efficiency for RL-enabled autonomous systems. BERRY supports robust learning, both offline and on-board the UAV, and for the first time, demonstrates the practicality of robust low-voltage operation on UAVs that leads to high energy savings in both compute-level operation and system-level quality-of-flight. We perform extensive experiments on 72 autonomous navigation scenarios and demonstrate that BERRY generalizes well across environments, UAVs, autonomy policies, operating voltages and fault patterns, and consistently improves robustness, efficiency and mission performance, achieving up to 15.62% reduction in flight energy, 18.51% increase in the number of successful missions, and 3.43× processing energy reduction.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247999"}, {"primary_key": "1120177", "vector": [], "sparse_vector": [], "title": "SCAGuard: Detection and Classification of Cache Side-Channel Attacks via Attack Behavior Modeling and Similarity Comparison.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Fu Song"], "summary": "Cache side-channel attacks (CSCAs), capable of deducing secrets by analyzing timing differences in the shared cache behavior of modern processors, pose a serious security threat. While there are approaches for detecting CSCAs and mitigating information leaks, they either fail to detect and classify new variants or have to impractically update deployed systems (e.g., CPU). In this work, we propose a novel approach, named SCAGuard, to detect and classify CSCAs via attack behavior modeling and similarity comparison. Specifically, we introduce the notion of cache state transition enhanced basic block sequences (CST-BBSes) to model attack behaviors which is able to capture both attack-relevant syntactic code information and semantic cache information. We propose an approach to automatically construct CST-BBS models from binary programs. To detect and classify attacks, we adapt a dynamic time warping algorithm to compare the similarity of CST-BBSes between attack and target programs. We implement our approach in a tool SCAGuard and evaluate it using real-world attacks and diverse benign programs. The results confirm the effectiveness of our approach, compared over existing detection approaches. In particular, SCAGuard significantly outperforms the other detection approaches on new variants.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247890"}, {"primary_key": "1120178", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Scalable and Efficient Hyperdimensional Computing for Network Intrusion Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cybersecurity has emerged as a critical challenge for the industry. With the large complexity of the security landscape, sophisticated and costly deep learning models often fail to provide timely detection of cyber threats on edge devices. Brain-inspired hyperdimensional computing (HDC) has been introduced as a promising solution to address this issue. However, existing HDC approaches use static encoders and require very high dimensionality and hundreds of training iterations to achieve reasonable accuracy. This results in a serious loss of learning efficiency and causes huge latency for detecting attacks. In this paper, we propose CyberHD, an innovative HDC learning framework that identifies and regenerates insignificant dimensions to capture complicated patterns of cyber threats with remarkably lower dimensionality. Additionally, the holographic distribution of patterns in high dimensional space provides CyberHD with notably high robustness against hardware errors.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247859"}, {"primary_key": "1120179", "vector": [], "sparse_vector": [], "title": "Ever more optimized simulations of fermionic systems on a quantum computer.", "authors": ["<PERSON><PERSON> Wang", "<PERSON>e-<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Yunseong Nam"], "summary": "Despite using a novel model of computation, quantum computers break down programs into elementary gates. Among such gates, entangling gates are the most expensive. In the context of fermionic simulations, we develop a suite of compilation and optimization techniques that massively reduce the entangling-gate counts. We exploit the well-studied non-quantum optimization algorithms to achieve up to 24% savings over the state of the art for several small-molecule simulations, with no loss of accuracy or hidden costs. Our methodologies straightforwardly generalize to wider classes of near-term simulations of the ground state of a fermionic system or real-time simulations probing dynamical properties of a fermionic system.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247693"}, {"primary_key": "1120180", "vector": [], "sparse_vector": [], "title": "CorcPUM: Efficient Processing Using Cross-Point Memory via Cooperative Row-Column Access Pipelining and Adaptive Timing Optimization in Subarrays.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jing<PERSON> Liu"], "summary": "Emerging cross-point memory can in-situ perform vector-matrix multiplication (VMM) for energy-efficient scientific computation. However, parasitic-capacitance-induced row charging and discharging latency is a major performance bottleneck of subarray VMM. We propose a memory-timing-compliant bulk VMM processing-using-memory design with row access and column access co-optimization from rethinking of read access commands and µ-op timing. We propose row-level-parallelism-adaptive timing termination mechanism to reduce tail latency of tRCD and tRP by exploiting row nonlinear charging and bulk-interleaved row-column-cooperative VMM access mechanism to reduce tRAS and overlap CL without increasing column ADC precision. Evaluations show that our design can achieve 5.03× performance speedup compared with an aggressive baseline.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247700"}, {"primary_key": "1120181", "vector": [], "sparse_vector": [], "title": "DistHD: A Learner-Aware Dynamic Encoding Method for Hyperdimensional Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Internet of Things (IoT) has become an emerging trend that connects heterogeneous devices and enables them with new capabilities. Many applications exploit machine learning methodology to dissect collected data, and edge computing was introduced to enhance the efficiency and scalability in resource-constrained computing environments. Unfortunately, popular deep learning algorithms involve intensive computations that are overcomplicated for edge devices. Brain-inspired Hyperdimensional Computing (HDC) has been considered a promising approach to address this issue. However, existing HDC methods use static encoders, and thus require extremely high dimensionality and hundreds of training iterations to achieve reasonable accuracy. This results in a huge loss of efficiency and severely impedes the application of HDC algorithms in power-limited machines. In this paper, we propose DistHD, a novel HDC framework with a unique dynamic encoding technique consisting of two parts: top-2 classification and dimension regeneration. Our top-2 classification provides top-2 labels for each data sample based on cosine similarity, and dimension regeneration identifies and regenerates dimensions that mislead the classification and reduce the learning quality. The highly parallel algorithm of DistHD effectively accelerates the learning process and achieves the desired accuracy with considerably lower dimensionality. Our evaluation on a wide range of practical classification tasks shows that DistHD is capable of achieving on average 2.12% higher accuracy than state-of-the-art (SOTA) HDC approaches while reducing dimensionality by 8.0×. It delivers 5.97× faster training and 8.09× faster inference than SOTA learning algorithms. Additionally, the holographic distribution of patterns in high dimensional space provides DistHD with 12.90× higher robustness against hardware errors than SOTA DNNs. DistHD has been open-sourced to enable future research in this field. 1", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247876"}, {"primary_key": "1120182", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: PVT-Sensitive Delay Fitting for High-Performance Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Aggressively monitoring and tracking system-on-chip (SoC) performance under process/voltage/temperature (PVT) variations is essential for high-performance computing systems. This work observes that different chips of the same SoC design may have different PVT-to-delay sensitivities, which must be carefully considered for accurate chip performance tracking. A learning-based method is then proposed to fit critical path delay for different chips with different PVT-to-delay sensitivities. Experimental results based on the fabricated chip samples of a 7nm SoC have justified the effectiveness of the proposed PVT-sensitive delay fitting method. Compared with the state-of-the-art, our method can achieve excellent performance tracking accuracy when the chip performance is dominated by different critical paths under different PVT conditions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247900"}, {"primary_key": "1120183", "vector": [], "sparse_vector": [], "title": "MTL-Designer: An Integrated Flow for Analysis and Synthesis of Microstrip Transmission Line.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Li<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Microstrip transmission line (MTL) appears extensively in microwave integrated circuits (MIC). To sufficiently analyze and synthesize the MTL, we propose MTL-Designer that can design the electrical and geometrical parameters of an MTL given performance specifications. We construct a deep generative model to generate initial solutions, and a surrogate model to predict the characteristics, optimize the solutions, and select from them. We further propose an adaptive sampling algorithm to speedup training. Our flow can generate 1000 feasible solutions within ∼0.6 s, realizing > 99.8% accuracy given various design specifications for two common MTL systems, exhibiting its strong potential for MIC design.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120184", "vector": [], "sparse_vector": [], "title": "Restructure-Tolerant Timing Prediction via Multimodal Fusion.", "authors": ["<PERSON><PERSON><PERSON>", "Siting <PERSON>", "<PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Fast and accurate pre-routing timing prediction is crucial in the very-large-scale integration (VLSI) design flow. Existing machine learning (ML)-assisted pre-routing timing evaluators neglect the impact of timing optimization, which may render their approaches impractical in real circuit design flows. To model the impact of timing optimization, we propose an endpoint embedding framework that integrates netlist-layout information via multimodal fusion. An end-to-end flow is further developed for pre-routing restructure-tolerant prediction on global timing metrics. Comprehensive experiments on large-scale RISC-V designs with advanced 7-nm technology node demonstrate the superiority of our model compared to the SOTA pre-routing timing evaluators.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120185", "vector": [], "sparse_vector": [], "title": "Accelerating Sparse LU Factorization with Density-Aware Adaptive Matrix Multiplication for Circuit Simulation.", "authors": ["Teng<PERSON> Wang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Sparse LU factorization is considered to be one of the most time-consuming components in circuit simulation, particularly when dealing with circuits of considerable size in the advanced process era. Sparse LU factorization can be expedited by utilizing the supernode structure, which partitions the matrix into dense sub-matrices, thereby improving computational performance by utilizing level-3 Basic Linear Algebra Subprograms (BLAS) General Matrix Multiplication (GEMM) operations. The sparse and irregular structure of circuit matrices often impedes the formation of supernodes or results in the formation of supernodes with many zero elements, which in turn poses challenges for exploiting GEMM operations. In this paper, by fully utilizing the density in sub-matrices and combining GEMM with the Dense-Sparse Matrix Multiplication (SpMM), we propose a density-aware adaptive matrix multiplication equipped with machine learning techniques to optimize performance of the most-time consuming matrix multiplication operator so as to accelerate the sparse LU factorization. Numerical experiment results show that among the 6 circuit matrices tested, the average performance of matrix multiplication in our algorithm can be improved by 5.35x (up to 9.35x) compared to the performance of using GEMM directly in Schur-complement updates. Compared with state-of-the-art solver SuperLU_DIST, our method shows a substantial performance improvement.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247767"}, {"primary_key": "1120186", "vector": [], "sparse_vector": [], "title": "Shoggoth: Towards Efficient Edge-Cloud Collaborative Real-Time Video Inference via Adaptive Online Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jiguang Wan", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes Shoggoth, an efficient edge-cloud collaborative architecture, for boosting inference performance on real-time video of changing scenes. Shoggoth uses online knowledge distillation to improve the accuracy of models suffering from data drift and offloads the labeling process to the cloud, alleviating constrained resources of edge devices. At the edge, we design adaptive training using small batches to adapt models under limited computing power, and adaptive sampling of training frames for robustness and reducing bandwidth. The evaluations on the realistic dataset show 15%–20% model accuracy improvement compared to the edge-only strategy and fewer network costs than the cloud-only strategy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247821"}, {"primary_key": "1120187", "vector": [], "sparse_vector": [], "title": "MPass: Bypassing Learning-based Static Malware Detectors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Zongpeng Li", "<PERSON>"], "summary": "Machine learning (ML) based static malware detectors are widely deployed, but vulnerable to adversarial attacks. Unlike images or texts, tiny modifications to malware samples would significantly compromise their functionality. Consequently, existing attacks against images or texts will be significantly restricted when being deployed on malware detectors. In this work, we propose a hard-label black-box attack MPass against ML-based detectors. MPass employs a problem-space explainability method to locate critical positions of malware, applies adversarial modifications to such positions, and utilizes a runtime recovery technique to preserve the functionality. Experiments show MPass outperforms existing solutions and bypasses both state-of-the-art offline models and commercial ML-based antivirus products.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247858"}, {"primary_key": "1120188", "vector": [], "sparse_vector": [], "title": "DiffPattern: Layout Pattern Generation via Discrete Diffusion.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Guo<PERSON> Chen", "Farzan Farnia", "<PERSON><PERSON>"], "summary": "Deep generative models dominate the existing literature in layout pattern generation. However, leaving the guarantee of legality to an inexplicable neural network could be problematic in several applications. In this paper, we propose DiffPattern to generate reliable layout patterns. DiffPattern introduces a novel diverse topology generation method via a discrete diffusion model with compute-efficiently lossless layout pattern representation. Then a white-box pattern assessment is utilized to generate legal patterns given desired design rules. Our experiments on several benchmark settings show that DiffPattern significantly outperforms existing baselines and is capable of synthesizing reliable layout patterns.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248009"}, {"primary_key": "1120189", "vector": [], "sparse_vector": [], "title": "AccALS: Accelerating Approximate Logic Synthesis by Selection of Multiple Local Approximate Changes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Approximate computing is an energy-efficient computing paradigm for error-tolerant applications. To automatically synthesize approximate circuits, many iterative approximate logic synthesis (ALS) methods have been proposed. However, most of them do not consider applying multiple local approximate changes (LACs) in a single round, which can lead to a much shorter runtime. In this paper, we propose AccALS, a novel framework for Accelerating iterative ALS flows, based on simultaneous selection of multiple LACs in a single round. When selecting multiple LACs, there may exist conflicts among them. One important component of AccALS is a novel method to solve the conflicts. Another is an efficient measure for the mutual influence between two LACs. With its help, the problem of selecting multiple LACs is transformed into a maximum independent set problem to solve. The experimental results showed that compared to a state-of-the-art method, AccALS accelerates by up to 24.6× with a negligible circuit quality loss.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247856"}, {"primary_key": "1120190", "vector": [], "sparse_vector": [], "title": "CPE: An Energy-Efficient Edge-Device Training with Multi-dimensional Compression Mechanism.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Boxiao Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, the edge-device DNN training has become of high importance, while the computation and access energy consumption of are too large. This paper proposes a CPE (Compress Process Element) with three characteristics. Firstly, CPE has a method of Reordering and Reusing Data (RRD) by controlling the output to reorder data. Secondly, CPE owns a Multi-directional Redundant Skip (MRS) mechanism, which anticipates all zeros and duplicate fields in advance. Thirdly, CPE contains a scheme to transform The Calculation Format (TCF), which transforms the input into another form. Evaluated with 28nm CMOS process, using CPE achieves 2.02 × energy reduction and offer 1.73 × speed up outperforming state-of-the-art trainable processor GANPU.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247968"}, {"primary_key": "1120191", "vector": [], "sparse_vector": [], "title": "COSA:Co-Operative Systolic Arrays for Multi-head Attention Mechanism in Neural Network using Hybrid Data Reuse and Fusion Methodologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Attention mechanism acceleration is becoming increasingly vital to achieve superior performance in deep learning tasks. Existing accelerators are commonly devised dedicatedly by exploring the potential sparsity in neural network (NN) models, which suffer from complicated training, tuning processes, and accuracy degradation. By systematically analyzing the inherent dataflow characteristics of attention mechanism, we propose the Co-Operative Systolic Array (COSA) to pursue higher computational efficiency for its acceleration. In COSA, two systolic arrays that can be dynamically configured into weight or output stationary modes are cascaded to enable efficient attention operation. Thus, hybrid dataflows are simultaneously supported in COSA. Furthermore, various fusion methodologies and an advanced softmax unit are designed. Experimental results show that the COSA-based accelerator can achieve 2.95-28.82× speedup compared with the existing designs, with up to 97.4% PE utilization rate and less memory access.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247678"}, {"primary_key": "1120192", "vector": [], "sparse_vector": [], "title": "IP Protection in TinyML.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Tiny machine learning (TinyML) is an essential component of emerging smart microcontrollers (MCUs). However, the protection of the intellectual property (IP) of the model is an increasing concern due to the lack of desktop/server-grade resources on these power-constrained devices. In this paper, we propose STML, a system and algorithm co-design to Secure IP of TinyML on MCUs with ARM TrustZone. Our design jointly optimizes memory utilization and latency while ensuring the security and accuracy of emerging models. We implemented a prototype and benchmarked with 7 models, demonstrating STML reduces 40% of model protection runtime overhead on average.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247898"}, {"primary_key": "1120193", "vector": [], "sparse_vector": [], "title": "PseudoSC: A Binary Approximation to Stochastic Computing within Latent Operation-Space for Ultra-Lightweight on-Edge DNNs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, stochastic computing (SC) is increasingly popular in constructing MAC for on-edge DNNs benefiting from its outstanding energy-efficiency, including its adequate precision and gate-level operation. However, current SC-DNN systems always include a lot of costly SNGs/APCs for inevitably switches between binary and stochastic domains, which mortgages incongruous resources to pay the \"bill\" of the domain-switches and impedes highly-concurrent deployments. In this work, PseudoSC, a binary approximation to low-discrepancy SC, is proposed to totally remove the domain-switch for SNG/APC-free SC-DNNs. Its basic idea is to virtually re-arrange a couple of stochastic operands into a 2-D latent op-space, in which, original Monte Carlo sampling can be partitioned into three sub-ops, i.e., two fixed binary-ops and a fractal recursion. In theory, the recursion forms an isomorphic partition of the sampling repeated in smaller scales until the binary base-case achieved, as a result, a SC-op is well approximated only with binary-ops. Based on above theory, a multi-lane micro-architecture is designed to unroll the recursion within a few cycles and its advantages on hardware saving is verified under popular DNNs. The evaluation shows that the DNN-models with our schemes achieve 98.7% accuracy of the fixed-point implementations, which significantly outperform other SOTA methods. In addition, its reduced structure improves the power efficiency by 3.67 times on average.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247851"}, {"primary_key": "1120194", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Model, Framework and Integration for In-Storage Computing with Computational SSDs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In-storage computing with computational SSDs is emerging as one effective solution for I/O bottlenecks in big data applications such as AI learning model training. Specifically, with in-SSD computing, computation can be pushed down to SSDs and the volume of the output data that will be transferred back to the host can be greatly reduced. However, there are several fundamental issues for applications to fully exploit in-SSD computing with simple and efficient function offloading. In this paper, we present three challenges for in-SSD computing, namely, data model, programming framework, and storage/computing integration, and discuss possible research directions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247955"}, {"primary_key": "1120195", "vector": [], "sparse_vector": [], "title": "A High-accurate Multi-objective Exploration Framework for Design Space of CPU.", "authors": ["<PERSON>", "Mingyu Yan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To accelerate time-consuming multi-objective design space exploration of CPU, previous work trains prediction models using a set of performance metrics derived from few simulations, then predicts the rest. Unfortunately, the low accuracy of models limits the exploration effect, and how to achieve a good trade-off between multiple objectives is challenging.In this paper, we investigate various prediction models and find out the most accurate basic model. We enhance the model by ensemble learning to improve prediction accuracy. A hypervolume-improvement-based optimization method to trade off between multiple objectives is proposed together with a uniformity-aware selection algorithm to jump out of the local optimum. Experiments demonstrate that our open-source framework can reduce the distance to the Pareto optimal set by 76% and prediction error by 97% compared with the state-of-the-art work.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247790"}, {"primary_key": "1120196", "vector": [], "sparse_vector": [], "title": "NNTesting: Neural Network Fault Attacks Detection Using Gradient-Based Test Vector Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies have shown Neural Networks (NNs) are highly vulnerable to fault attacks. This work proposes a novel defensive framework, NNTesting, for detecting the fault attack and recovering the model. We first leverage gradient-based optimization to generate a set of high-quality Test Vectors (TVs) that effectively differentiate faulty profile models and further optimize the TV set by reducing the TVs through compression. The selected final TV set is then used to recover the model. The effectiveness of the proposed method is comprehensively evaluated on a wide range of models across various benchmark datasets. For instance, we successfully generate more than thousands of TV candidates using a gradient-based generation method. After compression, we achieve up to 94.76% detection success rate with only 140 TVs on the CIFAR-10 dataset.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247885"}, {"primary_key": "1120197", "vector": [], "sparse_vector": [], "title": "Don&apos;t-Care Aware ESOP Extraction via Reduced Decomposition-Tree Exploration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Exclusive-OR Sum-of-Products expressions (ESOPs) are vital for circuit synthesis of arithmetic functions and emerging technologies. The state-of-the-art ESOP extraction methods are limited in their inefficient exhaustive exploration strategy, significant optimality loss in the divide-and-conquer process, and incapability of handling don't-cares. This work overcomes these limitations by reduced decomposition exploration with a cost estimation and refinement strategy, generally applicable to incompletely specified functions. Experiments show up to 29× (average 12×) runtime and 28× (average 13×) memory-usage improvements with a quality close to the exact optimum obtained by exhaustive exploration for completely-specified functions, and substantial ESOP simplification with don't-cares for incompletely-specified functions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247813"}, {"primary_key": "1120198", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning-Assisted Management for Convertible SSDs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>z<PERSON> Li"], "summary": "Convertible SSDs, which allow flash cells to convert between different types of flash cells (e.g., SLC/MLC/TLC/QLC), are designed for achieving both high performance and high density. However, previous designs with two types of flash cells encounter a performance cliff degradation once the flash cells of single bit mode are consumed. In this work, we propose a novel level-based convertible SSD (e.g., including SLC-MLC-QLC), named RL-cSSD, that adopts an intermediate layer (e.g., MLC) as a performance cushion. A reinforcement learning-assisted device management scheme is designed to coordinate the data allocation, garbage collection and flash conversion processes considering both the SSD internal status and workload patterns. We evaluated RL-cSSD with various real-world workloads based on simulation. The experimental results show that the proposed RL-cSSD provides 72.98% higher performance on average compared with state-of-the-art schemes.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247929"}, {"primary_key": "1120199", "vector": [], "sparse_vector": [], "title": "GDSII-Guard: ECO Anti-Trojan Optimization with Exploratory Timing-Security Trade-Offs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the ever-shrinking feature size of transistors, the exorbitant cost has driven the massive outsourcing of integrated circuits (IC) fabrication. However, this outsourcing poses significant security risks because untrustworthy foundries can conduct insidious fabrication-time attacks without close supervision. Therefore, it is crucial to undertake design-time protection before sending finalized design layouts to the foundry. Foundry-level hardware Trojan has emerged as a major security threat, but existing design-time countermeasures lack sufficient consideration of good trade-offs between design security and performance.This work proposes an automatic framework, GDSII-Guard, to strengthen implemented physical layouts against potential fabrication-time Trojan attacks while preserving design performance, power, and quality. We develop an Engineering Change Order (ECO) placement and routing (P&R) flow containing elaborate anti-Trojan operators to prevent Trojan insertion. Moreover, we introduce a multi-objective optimization model with evolutionary strategies that incorporate anti-Trojan flow information to exploit balances between the aforementioned multiple design metrics. Experimental results demonstrate that GDSII-Guard reduces the overall risk of Trojan attacks on given designs by 98.8% with minimized timing, power, and design quality impact, surpassing existing approaches prominently.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120200", "vector": [], "sparse_vector": [], "title": "PUMICE: Processing-using-Memory Integration with a Scalar Pipeline for Symbiotic Execution.", "authors": ["Socrates <PERSON><PERSON>", "Cecilio C<PERSON>", "<PERSON>"], "summary": "Existing SIMD extensions in scalar CPUs (e.g., SSE, AVX, etc.) can leverage instruction-level parallelism (ILP) because of their tight integration with the CPU pipeline. However, the vectors they employ are quite short, and this limits their ability to exploit data-level parallelism (DLP). On the other hand, processing-using-memory (PUM) accelerators are capable of exploiting massive amounts of DLP, as they typically perform computation on very long vectors (tens of thousands of elements) within the memory itself. Recent work demonstrates that order-of-magnitude speedups can be achieved by these architectures for a variety of workloads over area-equivalent multicore CPUs with SIMD extensions. Still, PUM architectures are largely decoupled from the CPU itself, thereby limiting their ability to tap the CPU's ILP the way SIMD extensions do.In this paper, we propose PUMICE, a tightly integrated CPU-PUM architecture that simultaneously exploits DLP and ILP for very long vector operations. As a result of this tight integration, PUMICE delivers significant performance gains: Our experimental results show speedups of up to 2.2× (1.4× on average) over a state-of-the-art decoupled approach.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247818"}, {"primary_key": "1120201", "vector": [], "sparse_vector": [], "title": "Invited: Pre-silicon Side Channel and Fault Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we address the challenges of side channel analysis (SCA) and fault injection (FI) in the pre-silicon design stage, particularly for cryptographic implementations. We show that the industry perspective uncovers different challenges than those usually tackled by academia, emphasizing the need for practical and scalable solutions. We present (Side Channel Attack Testbench Emulator) SCATE, a framework for detecting and mitigating SCA vulnerabilities, leveraging industry-standard electronic design automation (EDA) software for efficient power estimation and FI simulations. Moreover, we demonstrate a significant performance increase in power simulation tools by optimizing EDA algorithms for SCA applications, resulting in faster power trace generation. Additionally, we discuss the application of formal property verification (FPV) for verifying fault injection countermeasures and present a case study on an AES S-box. These approaches enable non-security-expert designers to evaluate SCA and FI resistant designs with significant time savings. The vulnerabilities uncovered by these approaches highlight the importance of pre-silicon analysis at each design stage to produce secure application-specific integrated circuits (ASICs).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247882"}, {"primary_key": "1120202", "vector": [], "sparse_vector": [], "title": "APP: Enabling Soft Real-Time Execution on Densely-Populated Hybrid Memory System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory swapping was considered slow and evil, but swapping to Ultra Low-Latency storage like Optane has become a promising solution to save power and cost, helping densely-populated edge server to overcome its DRAM capacity bottleneck. However, the lack of integration between CPU scheduling and memory paging causes soft real-time tasks running on edge servers to miss deadlines under heavy memory multiplexing. We propose APP (Adaptive Page Pinning), lightweight protection of working set memory to ensure meeting soft real-time task deadlines without starving other non-real-time tasks. Experiments show that APP alleviates thrashing in memory-intensive tasks and upholds soft real-time task deadlines.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247672"}, {"primary_key": "1120203", "vector": [], "sparse_vector": [], "title": "DriverJar: Lightweight Device Driver Isolation for ARM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Driver-originated vulnerabilities are well-known threats to modern monolithic kernels. However, existing driver isolation solutions either rely on Intel-only or newly-introduced CPU features (e.g., Intel VMFUNC, ARM MTE), or suffer from performance issues, making them unsuitable for existing ARM-based devices. In this work, we leverage a common hardware feature, named hardware watchpoint, to achieve lightweight driver isolation for off-the-shelf ARM devices. Specifically, we utilize watchpoints to prevent the possibly compromised driver from corrupting the rest kernel's state arbitrarily. We implement a prototype for ARM64 Linux. The security analysis and performance evaluation show the efficiency and practicality of our solution.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120204", "vector": [], "sparse_vector": [], "title": "Lamellar DSA-aware Detailed Routing Considering Double Patterning and Short Template Minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the advance of technology nodes, via fabrication in a dense design becomes a difficult challenge, and the directed self-assembly (DSA) technology has shown its great potential in contact/via layer manufacturing. As many studies consider the guiding template design problem for cylindrical DSA, lamellar DSA with the self-aligned via process emerges as an alternative with better yield and design flexibility. However, the new process also imposes new constraints on template design, making an arbitrary contact/via layout suffer from unsatisfactory manufacturability. In this paper, we present the first work on detailed routing considering lamellar DSA guiding template design and mask assignment with double patterning lithography (DPL) to minimize the numbers of unmanufacturable vias and short templates. In the proposed router, we construct a conflict graph and a via forbidden map for routing guidance and via mask assignment. A prerouting methodology is developed to obtain better via distribution in critical areas and preserve routing space for further detailed routing. After that, a short template-aware detailed routing approach routes the rest of the nets and an existing template design engine is adopted for optimal template design and mask assignment. The experimental results indicate that compared to a conventional A * search-based router, our approach can generate conflict-free template design results and achieve 35% reduction in short templates.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120205", "vector": [], "sparse_vector": [], "title": "RMP-MEM: A HW/SW Reconfigurable Multi-Port Memory Architecture for Multi-PEA Oriented CGRA.", "authors": ["<PERSON><PERSON>", "Jiangyuan Gu", "<PERSON><PERSON><PERSON>", "Boxiao Han", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-Grained Reconfigurable Architecture (CGRA), especially the one with multiple parallelized Processing Element Arrays (PEA), possesses flexible programmability and high parallel computational efficiency, which relies upon an efficient memory architecture to deliver the corresponding computing power. Multi-PEA oriented CGRA allows for mapping various applications and thus demands a flexible memory to adapt to the ever-changing workloads, whose parallel access also requires an efficient multi-port memory. However, the existing memory designs for CGRA are hard to satisfy those requirements since conventional rigid memories fail to provide the desired flexibility due to fixed structure, and traditional multi-port designs are impractical due to large overhead. Therefore, this paper proposes a hardware/software (HW/SW) hybrid reconfigurable multi-port memory architecture (RMP-MEM) with an instructive analysis for the multi-PEA oriented CGRA. RMP-MEM supports adaptive memory partition and programmer-defined access modes to adapt the different features of memory accesses. Also, RMP-MEM achieves an efficient multi-port implementation by a partially shared mechanism. Furthermore, the microarchitecture of RMP-MEM is optimized multi-directionally, resulting in a significant performance gain. The experimental results indicate that RMP-MEM reduces the parallel access latency by 81.1% and exhibits 28.3% energy efficiency improvement compared to prior designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247776"}, {"primary_key": "1120206", "vector": [], "sparse_vector": [], "title": "Gamora: Graph Learning based Symbolic Reasoning for Large-Scale Boolean Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reasoning high-level abstractions from bit-blasted Boolean networks (BNs) such as gate-level netlists can significantly benefit functional verification, logic minimization, datapath synthesis, malicious logic identification, etc. Mostly, conventional reasoning approaches leverage structural hashing and functional propagation, suffering from limited scalability and inefficient usage of modern computing power. In response, we propose a novel symbolic reasoning framework exploiting graph neural networks (GNNs) and GPU acceleration to reason high-level functional blocks from gate-level netlists, namely Gamora, which offers high reasoning performance w.r.t exact reasoning algorithms, strong scalability to BNs with over 33 million nodes, and generalization capability from simple to complex designs. To further demonstrate the capability of Gamora, we also evaluate its reasoning performance after various technology mapping options, since technology-dependent optimizations are known to make functional reasoning much more challenging. Experimental results show that (1) Gamora reaches almost 100% and over 97% reasoning accuracy for carry-save-array (CSA) and Booth-encoded multipliers, respectively, with up to six orders of magnitude speedups compared to the state-of-the-art implementation in the ABC framework; (2) Gamora maintains high reasoning accuracy (>92%) in finding functional modules after complex technology mapping, and we comprehensively analyze the impacts on Gamora reasoning from technology mapping. Gamora is available at https://github.com/Yu-Utah/Gamora.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247828"}, {"primary_key": "1120207", "vector": [], "sparse_vector": [], "title": "Mantra: Mutation Testing of Hardware Design Code Based on Real Bugs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiankai Meng", "<PERSON><PERSON><PERSON>", "Pan Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mutation testing, a well-suited technology for functional validation, is regrettably poorly studied in hardware. We propose Mantra: the first open-source code-level mutation testing tool based on real hardware bugs. Specifically, Mantra devises time-aware mutation killing mechanism for cost reduction of hardware mutation testing using the parallelism of hardware design code, and then defines and implements 19 hardware mutation operators via large-scale empirical analysis on real bugs. Finally, the evaluation on public datasets from CirFix and OpenCores shows that Mantra achieves promising results with a maximum boost of 83.44%.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247962"}, {"primary_key": "1120208", "vector": [], "sparse_vector": [], "title": "CSQ: Growing Mixed-Precision Quantization Scheme with Bi-level Continuous Sparsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mixed-precision quantization has been applied on deep neural networks (DNNs) as it leads to significantly better efficiency-accuracy tradeoffs compared to uniform quantization. Meanwhile, determining the exact precision of each layer remains challenging. Previous attempts on bit-level regularization and pruning-based dynamic precision adjustment during training suffer from noisy gradients and unstable convergence. In this work, we propose Continuous Sparsification Quantization (CSQ), a bit-level training method to search for mixed-precision quantization schemes with improved stability. CSQ stabilizes the bit-level mixed-precision training process with a bi-level gradual continuous sparsification on both the bit values of the quantized weights and the bit selection in determining the quantization precision of each layer. The continuous sparsification scheme enables fully-differentiable training without gradient approximation while achieving an exact quantized model in the end. A budget-aware regularization of total model size enables the dynamic growth and pruning of each layer's precision towards a mixed-precision quantization scheme of the desired size. Extensive experiments show CSQ achieves better efficiency-accuracy tradeoff than previous methods on multiple models and datasets.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247982"}, {"primary_key": "1120209", "vector": [], "sparse_vector": [], "title": "Holistic WCRT Analysis for Global Fixed-Priority Preemptive Multiprocessor Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Renfa Li", "<PERSON><PERSON>"], "summary": "Many embedded applications demand both resource efficiency and timing guarantee. However, resource sharing naturally complicates the analysis that extracts the worst-case scenario out of contention. Global Fixed-Priority (GFP) preemptive multiprocessor scheduling is one of the mainstream strategies to resolve contention on computational resources. It allows jobs of the same task to be executed on different processors, hence potentially enabling better parallelism and more efficient resource utilization. Unfortunately, its worst-case response time (WCRT) analysis is challenging. Existing approaches divide a high-priority task into three workloads, namely, carry-in workload, body workload, and carry-out workload, trying to optimize them individually. In this work, we propose a holistic WCRT analysis for GFP preemptive multiprocessor scheduling, where a task is no longer divided. Specifically, (i) we establish the tight interference scenario for the task being analyzed to find the most interfering high-priority jobs in any time interval; (ii) we obtain the starting released instant of each high-priority task's first job to determine the maximum interference from high-priority tasks' first jobs to the task being analyzed; (iii) we build the worst-case tight interference scenario for the task being analyzed by combining the tight interference scenario and the starting released instants; (iv) we prove that the WCRT of the task being analyzed can be decided by the worst-case tight interference scenario. Evaluation on schedulability shows that our proposed analysis achieves 4.2%-8.6% higher acceptance ratio in randomly generated data sets than the state-of-the-art workload division approaches.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247970"}, {"primary_key": "1120210", "vector": [], "sparse_vector": [], "title": "TOTAL: Multi-Corners Timing Optimization Based on Transfer and Active Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan<PERSON> Cheng", "<PERSON><PERSON><PERSON> Zhao"], "summary": "In modern advanced integrated circuit design, a design normally needs to be progressively optimized until the static timing analysis (STA) of full process corners meets the timing constraints. To improve efficiency, using machine learning to predict the path timings directly in order to reduce the extensive time-consuming SPICE simulations has become a promising technique to approach fast design closure. However, current methods lack both flexibility and reliability to be used in a practical industrial environment. To resolve these challenges, we propose TOTAL, which is constructed using a generalized linear model with latent features to effectively capture knowledge transferred from previous designs and delivers state-of-the-art (SOTA) prediction accuracy that is up to 6.6x improvement over the competitors in terms of mean absolute error (MAE). Most importantly, TOTAL is equipped with a Bayesian decision strategy to actively update uncertain predictions and deliver reliable predictions with accuracy close to 100%, pushing the frontier of the machine-learning-based STA for practical implementation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247914"}, {"primary_key": "1120211", "vector": [], "sparse_vector": [], "title": "Stalker: A Framework to Analyze Fragility of Cryptographic Libraries under Hardware Fault Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For embedded devices, the uncertainty of target physical environments is always a great challenge. With constrained resources and common overloaded uses, they can be more exposed to hardware faults. Other than stability and ordinary security issues, there exist some subtle phenomenons that lead to potential cryptanalysis or secret leakage. In this paper, we present STALKER, a framework to analyze the fragility of libraries under hardware fault models. Compared with existing tools, our framework targets faulty execution outputs, and can flexibly work on different libraries, architectures and support different search schemes. We find dozens of security-sensitive bits that may cause critical issues and provide detailed analysis.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247932"}, {"primary_key": "1120212", "vector": [], "sparse_vector": [], "title": "Fast FPGA Accelerator of Graph Cut Algorithm with Out-of-order Parallel Execution in Folding Grid Architecture.", "authors": ["Guangyao Yan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph cut is a popular approach to solving optimization tasks related to Min-cut/Max-flow problems. However, existing FPGA accelerators of graph cut have difficulty in handling large grid graphs and achieving real-time performance. To address the issue, we propose a novel folding grid architecture that maps an actual one-layered large 2-dimension grid graph into a virtual multi-layered small 2-dimension grid graph. The new architecture not only enables the virtual multi-layered grid graph to execute on a small-size processor array but also adds the potential to concurrently execute grid graph nodes in different layers. In addition, we also propose a novel out-of-order parallel execution technique to fully utilize the architecture parallelism potential. Compared to the state-of-the-art, experimental results show that our design can solve the graph cut problem for grid graphs of 1920 × 1080 nodes in real-time (above 60fps) and achieve a 5.4× improvement in execution time with similar FPGA resources.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247784"}, {"primary_key": "1120213", "vector": [], "sparse_vector": [], "title": "Invited: A Scalable Formal Approach for Correctness-Assured Hardware Design.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Correctness must be a first principle in hardware/-software co-design, especially for security and safety critical applications. We will give an overview of our scalable approach for correctness-assured hardware/software design at behavioral level, based on formalizing microarchitecture features as program transformations in an incremental compiler design and microprocessor correctness as a refined notation of compiler correctness. We will show how our approach is applied to designing a formally verified FHE (Fully Homomorphic Encryption) accelerator.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247673"}, {"primary_key": "1120214", "vector": [], "sparse_vector": [], "title": "An Efficient Accelerator for Point-based and Voxel-based Point Cloud Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Tianyu Fu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The 3D point cloud neural networks, including point-based and voxel-based networks, play an essential role in various 3D applications. Many previous works have proposed dedicated accelerators to speed up 3D point cloud neural network processing. Yet, two major challenges still exist: (1) Inefficient memory access due to large off-chip data access volume. The point-based method visits massive redundant points, while the voxel-based method fails to reuse on-chip voxel data, leading to up to 983× data access compared with original input data. (2) Poor scalability due to low computing unit utilization. The computing unit is under-utilized when scaled with a larger computing array size, as low as 16.37% when scaling the current accelerator's computing capability to general-purpose processors (e.g., GPUs).To solve the above challenges, we propose MARS, a memory access reduced and scalable accelerator for both point-based and voxel-based 3D point cloud neural networks. To reduce the memory access, MARS filters out unnecessary off-chip point data access by 6.52× in volume for point-based networks and increases on-chip data reuse to reduce off-chip data access by 26.31× for voxel-based networks. To improve scalability, MARS also features an elastic computing array architecture that can be dynamically configured at runtime to fit different tasks, providing 7.09× higher computing unit utilization. Extensive experiments show that MARS achieves 1.76× over speedup and 3.97× PointAcc for point-based and end-to-end voxel-based point cloud neural networks, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247806"}, {"primary_key": "1120215", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Fast Fair Medical Applications? Hybrid Vision Models Achieve the Fairness on the Edge.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhenglun Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As edge devices become readily available and indispensable, there is an urgent need for effective and efficient intelligent applications to be deployed widespread. However, fairness has always been an issue, especially in edge medical applications. Compared to convolutional neuron networks (CNNs), Vision Transformer (ViT) has a better ability to extract global information, which will contribute to alleviating the unfairness problem. Typically, ViTs consume large amounts of computational and memory resources, which hinders their usage on edge. In this work, we propose a novel hardware-efficient Vision Model search framework for the fair dermatology classification, namely HeViFa. Experimental results show that HeViFa could search for a hybrid ViT model that reaches 173.1 FPS on a Samsung S21 mobile phone with 85.71% accuracy on the light skin dataset and 80.85% accuracy on the dark skin dataset. Note that HeViFa can reach both the highest accuracy and fairness under similar latency constrain on multiple edge devices (Samsung S21 mobile phone, iPhone 13 Pro and Raspberry PI).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247761"}, {"primary_key": "1120216", "vector": [], "sparse_vector": [], "title": "On-Device Unsupervised Image Segmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Yuzhou Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Along with the breakthrough of convolutional neural networks, in particular encoder-decoder and U-Net, learning-based segmentation has emerged in many research works. Most of them are based on supervised learning, requiring plenty of annotated data; however, to support segmentation, a label for each pixel is required, which is obviously expensive. As a result, the issue of lacking annotated segmentation data commonly exists. Continuous learning is a promising way to deal with this issue; however, it still has high demands on human labor for annotation. What's more, privacy is highly required in segmentation data for real-world applications, which further calls for on-device learning. In this paper, we aim to resolve the above issue in an alternative way: Instead of supervised segmentation, we propose to develop efficient unsupervised segmentation which can be executed on edge devices without annotated data. Based on our observation that segmentation can obtain high performance when pixels are mapped to a high-dimension space using their position and color information, we for the first time bring brain-inspired hyperdimensional computing (HDC) to the segmentation task. We build the HDC-based unsupervised segmentation framework, namely \"SegHDC\". In SegHDC, we devise a novel encoding approach, which follows the Manhattan distance. A clustering algorithm is further developed on top of the encoded high-dimension vectors to obtain segmentation results. Experimental results show that SegHDC can significantly surpass neural network-based unsupervised segmentation. On a standard segmentation dataset, DSB2018, SegHDC can achieve a 28.0% improvement in Intersection over Union (IoU) score; meanwhile, it achieves over 300× speedup on Raspberry PI. What's more, for a larger size image in the BBBC005 dataset, the existing approach cannot be accommodated to Raspberry PI due to out of memory; on the other hand, SegHDC can obtain segmentation results within 3 minutes while achieving a 0.9587 IoU score.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247959"}, {"primary_key": "1120217", "vector": [], "sparse_vector": [], "title": "Venus: A Versatile Deep Neural Network Accelerator Architecture Design for Multiple Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep Neural Network (DNN) applications are pervasive. However as demands for these applications continue to increase, so is the challenges for designing flexible and scalable architectures for multi-application implementation. Such accelerators require innovative architecture with flexible Network-on-Chips (NoCs), parallelism exploitation, and better on-chip memory organization to adequately support the diverse computation, memory, and communication needs. In this paper, we propose Venus, a versatile DNN accelerator design that can provide efficient communication and computation support for multi-applications. Venus is a tile-based architecture with a distributed buffer where each tile consists of an array of processing elements (PEs) and a portion of the distributed buffer. The other salient feature of Venus is a flexible Network-on-Chip (NoC) that can dynamically adapt to the communication needs of various running applications thus maximizing data reuse, reducing DRAM accesses, and supporting multiple dataflows with an overall aim of better execution time and better energy efficiency. Simulation results show that our proposed Venus design outperforms state-of-art accelerators (NVDLA [1], Shi<PERSON>ian<PERSON>ao [2], Eyeriss [3], Planaria [4], Simba [5]).", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247897"}, {"primary_key": "1120218", "vector": [], "sparse_vector": [], "title": "SpMMPlu: A Compiler Plug-in with Sparse IR for Efficient Sparse Matrix Multiplication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Qidong Tang", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "Sparsity is becoming arguably the most critical dimension to explore for efficiency and scalability as deep learning models grow significantly larger. Particularly, pruning is a common method to reduce redundant computations in attention-based and convolution-based models. The induced sparse matrix multiplication (SpMM) normally requires domain-specific hardware architecture (DSA) to eliminate unnecessary zero-valued computations. However, generating an optimal kernel code for SpMM on general-purpose and ISA-based spatial accelerators without changing the hardware architecture is still an open problem.In this paper, we propose a compiler plug-in named SpMMPlu, which can extend the representation and optimization ability for SpMM in current deep learning compiler frameworks that only support dense matrix multiplication. The key of SpMMPlu is a flexible intermediate representation— Sparse IR, representing the SpMM with various sparsity patterns based on meta-ops with a multi-level structure. Meta-op takes abstraction of the hardware intrinsic as its minimum granularity, and the powerful optimizers of existing NN compiler backends (e.g., Auto-schedule in TVM, AKG in MindSpore) can be easily reused for its computational scheduling and code generation. Moreover, we propose a two-step (segmentation & grouping) method to achieve an efficient Sparse IR for each sparsity pattern. Only three passes are added in SpMMPlu to provide an automatic solution for SpMM kernel code generation. We embed SpMM<PERSON><PERSON> into MindSpore and do experiments on NVIDIA V100 GPU and Huawei Ascend 910 to verify its effectiveness and scalability. The results show that with SpMMPlu, MindSpore can support various sparsity patterns and deliver a 1.93× (on V100 GPU) and 2.21× (on AScend 910) speedup averagely compared to the dense counterpart.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120219", "vector": [], "sparse_vector": [], "title": "Pathfinding Model and Lagrangian-Based Global Routing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Global routing is a critical step in VLSI physical design. This paper proposes a novel pathfinding model based on integer linear programming for VLSI global routing. The Lagrangian relaxation method combined with a direction-aware weighted A*-algorithm is developed to quickly solve the model to obtain a better initial routing solution, which is further optimized by a designed multi-stage rip-up & rerouting algorithm. In each stage of rip-up & rerouting, different routing algorithms and cost functions are used to optimize the overflow and wire length. SPRoute and CUGR are two state-of-the-art global routers. Our proposed global routing algorithm outperforms SPRoute 1.0 & 2.0 in both the wire length and the number of vias on the ISPD08 benchmarks. On the ISPD18 benchmarks, compared to CUGR, our algorithm has about 5.1% reduction in the number of vias, and the average runtime is 4.89× speedup; compared to SPRoute 2.0, our algorithm has about 1.7% reduction on the average wire length, and the number of vias is comparable.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120220", "vector": [], "sparse_vector": [], "title": "Graph Representation Learning for Microarchitecture Design Space Exploration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Design optimization of modern microprocessors is a complex task due to the exponential growth of the design space. This work presents GRL-DSE, an automatic microarchitecture search framework based on graph embeddings. GRL-DSE uses graph representation learning to build a compact and continuous embedding space. Multi-objective Bayesian optimization using an ensemble surrogate model conducts microarchitecture design space exploration in the graph embedding space to efficiently and holistically optimize performance-power-area (PPA) objectives. Experimental studies on RISC-V BOOM show that GRLDSE outperforms previous techniques by 74.59% on Pareto front quality and outperforms manual designs in terms of PPA.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247687"}, {"primary_key": "1120221", "vector": [], "sparse_vector": [], "title": "RESPECT: Reinforcement Learning based Edge Scheduling on Pipelined Coral Edge TPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) have substantial computational and memory requirements, and the compilation of its computational graphs has a great impact on the performance of resource-constrained (e.g., computation, I/O, and memory-bound) edge computing systems. While efficient execution of their computational graph requires an effective scheduling algorithm, generating the optimal scheduling solution is a challenging NP-hard problem. Furthermore, the complexity of scheduling DNN computational graphs will further increase on pipelined multi-core systems considering memory communication cost, as well as the increasing size of DNNs. Using the synthetic graph for the training dataset, this work presents a reinforcement learning (RL) based scheduling framework RESPECT, which learns the behaviors of optimal optimization algorithms and generates near-optimal scheduling results with short solving runtime overhead. Our framework has demonstrated up to ∼ 2.5 × real-world on-chip inference runtime speedups over the commercial compiler with ten popular ImageNet models deployed on the physical Coral Edge TPUs system. Moreover, compared to the exact optimization methods, the proposed RL scheduling improves the scheduling optimization runtime by up to 683× speedups compared to the commercial compiler and matches the exact optimal solutions with up to 930× speedups. Finally, we perform a comprehensive generalizability test, which demonstrates RESPECT successfully imitates optimal solving behaviors from small synthetic graphs to large real-world DNNs computational graphs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247706"}, {"primary_key": "1120222", "vector": [], "sparse_vector": [], "title": "TF-MVP: Novel Sparsity-Aware Transformer Accelerator with Mixed-Length Vector Pruning.", "authors": ["<PERSON><PERSON><PERSON>", "Gunho Park", "<PERSON>", "<PERSON> <PERSON>", "Baeseong Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the energy-efficient TF-MVP architecture, a sparsity-aware transformer accelerator, by introducing novel algorithm-hardware co-optimization techniques. From the previous fine-grained pruning map, for the first time, the direction strength is developed to analyze the pruning patterns quantitatively, indicating the major pruning direction and size of each layer. Then, the mixed-length vector pruning (MVP) is proposed to generate the hardware-friendly pruned-transformer model, which is fully supported by our TF-MVP accelerator with the reconfigurable PE structure. Implemented in a 28nm CMOS technology, as a result, TF-MVP achieves 377 GOPs/W for accelerating GPT-2 small model by realizing 4096 multiply-accumulate operators, which is 2.09 times better than the state-of-the-art sparsity-aware transformer accelerator.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247799"}, {"primary_key": "1120223", "vector": [], "sparse_vector": [], "title": "NetBooster: Empowering Tiny Deep Learning By Standing on the Shoulders of Deep Giants.", "authors": ["Zhongzhi Yu", "Yonggan Fu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Tiny deep learning has attracted increasing attention driven by the substantial demand for deploying deep learning on numerous intelligent Internet-of-Things devices. However, it is still challenging to unleash tiny deep learning's full potential on both large-scale datasets and downstream tasks due to the under-fitting issues caused by the limited model capacity of tiny neural networks (TNNs). To this end, we propose a framework called NetBooster to empower tiny deep learning by augmenting the architectures of TNNs via an expansion-then-contraction strategy. Extensive experiments show that NetBooster consistently outperforms state-of-the-art tiny deep learning solutions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247827"}, {"primary_key": "1120224", "vector": [], "sparse_vector": [], "title": "Invited: Autonomous Driving Digital Twin Empowered Design Automation: An Industry Perspective.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designing reliable computing systems for autonomous driving is extremely challenging, as the performance and reliability of the systems have to be thoroughly evaluated under an extremely large amount of driving scenarios. Physically constructing scenarios and conducting testing for autonomous driving systems is time-consuming and expensive. To minimize the need for physical testing and improve development efficiency, we developed a digital-twin-based simulation, which can generate an integral, precise, and comprehensive representation of physical scenarios. In this paper, we share our experiences with the digital-twin-based simulation for autonomous driving, particularly the design requirements and components demanded to facilitate virtual environment construction and design verification, which could greatly improve development efficiency.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247889"}, {"primary_key": "1120225", "vector": [], "sparse_vector": [], "title": "EagleRec: Edge-Scale Recommendation System Acceleration with Inter-Stage Parallelism Optimization on GPUs.", "authors": ["Yong<PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recommendation systems suggest items to users by predicting their preferences based on historical data. The industry traditionally handles large-scale recommendation requests by scaling the number of devices without much concern for a single device's performance. However, there is a trend for recommendation systems to gradually move from a centralized service to an edge device. The edge-scale recommendation systems have distinct features that are different from traditional large-scale deployments, which poses different challenges to the acceleration of the recommendation system. In this paper, we focus on the edge-scale recommendation system and propose an inter-stage parallelism optimization method deployed on a single GPU. Experiments show that our framework could improve recommendation system throughput by 1.89×~2.2× for different datasets on the GPU.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247679"}, {"primary_key": "1120226", "vector": [], "sparse_vector": [], "title": "Lightning Talk: Private and Secure Edge AI with Hyperdimensional Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Onat Güngör", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a lightweight and robust brain-inspired computing paradigm, Hyperdimensional Computing (HDC) serves as a promising solution for the next-generation edge AI. However, the basic form of HDC is vulnerable to privacy leaks and cyber attacks. In this paper, we breifly review and discuss the recent contributions to privacy and security of HDC. We first summarize existing HDC designs to protect against privacy leaks, such as differential privacy. Next, we review the data encryption techniques for collaborative learning using HDC based on Multi-Party Computation and Homomorphic Encryption. Finally, we discuss the HDC-based designs for combating cyber attacks in a malicious environment. More research on private and secure HDC-based methods are needed for future large-scale edge deployment.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247820"}, {"primary_key": "1120227", "vector": [], "sparse_vector": [], "title": "Automated Design of Complex Analog Circuits with Multiagent based Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite the effort of analog circuit design automation, currently complex analog circuit design still requires extensive manual iterations, making it labor intensive and time-consuming. Recently, reinforcement learning (RL) algorithms have been demonstrated successfully for the analog circuit design optimization. However, a robust and highly efficient RL method to design analog circuits with complex design space has not been fully explored yet. In this work, inspired by multiagent planning theory as well as human expert design practice, we propose a multiagent based RL (MA-RL) framework to tackle this issue. Particularly, we (i) partition the complex analog circuits into several sub-blocks based on topology information and effectively reduce the complexity of design search space; (ii) leverage MA-RL for the circuit optimization, where each agent corresponds to a single sub-block, and the interactions between agents delicately mimic the best design tradeoffs between circuit sub-blocks by human experts; (iii) introduce the multiagent twin-delayed techniques to further boost training stability and accomplish higher performances. Experiments on two different analog circuit topologies and knowledge transfers between two technology nodes are demonstrated. It's shown that MA-RL framework can achieve the best FoM for complex analog circuits design. This work shines the light for future large scale analog circuit system design automation.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247909"}, {"primary_key": "1120228", "vector": [], "sparse_vector": [], "title": "C2PI: An Efficient Crypto-Clear Two-Party Neural Network Private Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Souvik Kundu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, private inference (PI) has addressed the rising concern over data and model privacy in machine learning inference as a service. However, existing PI frameworks suffer from high computational and communication costs due to the expensive multi-party computation (MPC) protocols. Existing literature has developed lighter MPC protocols to yield more efficient PI schemes. We, in contrast, propose to lighten them by introducing an empirically-defined privacy evaluation. To that end, we reformulate the threat model of PI and use inference data privacy attacks (IDPAs) to evaluate data privacy. We then present an enhanced IDPA, named distillation-based inverse-network attack (DINA), for improved privacy evaluation. Finally, we leverage the findings from DINA and propose C 2 PI, a two-party PI framework presenting an efficient partitioning of the neural network model and requiring only the initial few layers to be performed with MPC protocols. Based on our experimental evaluations, relaxing the formal data privacy guarantees C 2 PI can speed up existing PI frameworks, including Delphi [1] and Cheetah [2], up to 2.89× and 3.88× under LAN and WAN settings, respectively, and save up to 2.75× communication costs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247682"}, {"primary_key": "1120229", "vector": [], "sparse_vector": [], "title": "LRSDP: Low-Rank SDP for Triple Patterning Lithography Layout Decomposition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multiple patterning lithography (MPL) has been widely adopted in advanced technology nodes to enhance lithography resolution. As layout decomposition for triple patterning lithography (TPL) and beyond is NP-hard, existing approaches formulate mathematical programming problems and leverage general-purpose solvers such as integer linear programming (ILP) and semidefinite programming (SDP) to trade off quality against runtime. With the aggressive increase in design complexity, existing approaches can no longer scale to solve complicated designs with high solution quality. In this paper, we propose a dedicated low-rank SDP algorithm for MPL decomposition with augmented Lagrangian relaxation and Riemannian optimization. Experimental results demonstrate that our method is 186×, 25×, and 12× faster than the state-of-the-art decomposition approaches with highly competitive solution quality.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247846"}, {"primary_key": "1120230", "vector": [], "sparse_vector": [], "title": "Contention-Free Configured <PERSON>heduling for 5G URLLC Traffic.", "authors": ["<PERSON><PERSON><PERSON>", "Xiao<PERSON>", "<PERSON>"], "summary": "5G networks are being designed to support ultra reliable and low latency communication (URLLC) services in many real-time industrial applications. The conventional grant-based dynamic scheduling can hardly fulfill the URLLC requirements due to the non-negligible transmission delays introduced during the spectrum resource grant process. To address this problem, 5G defines a grant-free transmission scheme, namely configured grant (CG) scheduling, for uplink (UL) traffic to pre-allocate spectrum resource to user equipments (UEs). This paper studies CG scheduling for periodic URLLC traffic with real-time and collision-free guarantees. An exact solution based on Satisfiability Modulo Theory (SMT) is first proposed to generate a feasible CG configuration for a given traffic set. To enhance scalability, we further develop an efficient graph-based heuristic consisting of an offset selection method and a multicoloring algorithm for spectrum resource allocation. Extensive experiments are conducted using 3GPP industrial use cases to show that both approaches can satisfy the real-time and collision-free requirements, and the heuristic can achieve comparable schedulability ratio with the SMT-based approach but require significantly lower running time.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247842"}, {"primary_key": "1120231", "vector": [], "sparse_vector": [], "title": "BP-NTT: Fast and Compact in-SRAM Number Theoretic Transform with Bit-Parallel Modular Multiplication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Number Theoretic Transform (NTT) is an essential mathematical tool for computing polynomial multiplication in promising lattice-based cryptography. However, costly division operations and complex data dependencies make efficient and flexible hardware design to be challenging, especially on resource-constrained edge devices. Existing approaches either focus on only limited parameter settings or impose substantial hardware overhead. In this paper, we introduce a hardware-algorithm methodology to efficiently accelerate NTT in various settings using in-cache computing. By leveraging an optimized bit-parallel modular multiplication and introducing costless shift operations, our proposed solution provides up to 29× higher throughput-per-area and 10-138× better throughput-per-power compared to the state-of-the-art.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247691"}, {"primary_key": "1120232", "vector": [], "sparse_vector": [], "title": "AdaGL: Adaptive Learning for Agile Distributed Training of Gigantic GNNs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed GNN training on contemporary massive and densely connected graphs requires information aggregation from all neighboring nodes, which leads to an explosion of inter-server communications. This paper proposes AdaGL, a highly scalable end-to-end framework for rapid distributed GNN training. AdaGL novelty lies upon our adaptive-learning based graph-allocation engine as well as utilizing multi-resolution coarse representation of dense graphs. As a result, AdaGL achieves an unprecedented level of balanced server computation while minimizing the communication overhead. Extensive proof-of-concept evaluations on billion-scale graphs show AdaGL attains ∼30−40% faster convergence compared with prior arts.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10248003"}, {"primary_key": "1120233", "vector": [], "sparse_vector": [], "title": "FSPA: An FeFET-based Sparse Matrix-Dense Vector Multiplication Accelerator.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sparse matrix-dense vector multiplication (SpMV) is widely used in various applications. The performance of traditional SpMV accelerators is bounded by memory. In-memory computing (IMC) is a promising technique to alleviate the memory bottleneck. The current IMC accelerator cannot support sparse storage format and in-situ floating-point multiplication at the same time. In this paper, we propose FSPA, an ferroelectric field-effect transistor (FeFET) based SpMV accelerator. FSPA integrates novel content-addressable memory (CAM) arrays and multiply-add computation (MAC) arrays to support sparse matrices represented in the floating-point format. FSPA achieves significant speedups and energy savings over CPU, GPU and two state-of-the-art IMC accelerators.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247895"}, {"primary_key": "1120234", "vector": [], "sparse_vector": [], "title": "Uint-Packing: Multiply Your DNN Accelerator Performance via Unsigned Integer DSP Packing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guoqing Li"], "summary": "DSP blocks are undoubtedly efficient solutions for implementing multiply-accumulate (MAC) operations on FPGA. Since DSP resources are scarce in FPGA, the advanced solution is to pack parallel multiplication operations into a single DSP. However, available methods are based on signed-type multiplication, leading to both loss of accuracy and increased area. To solve these issues simultaneously, we propose an unsigned integer DSP packing generalization model called uint-packing. Guided by this generalization model, we design the novel computational structure of the DNN accelerator. Our system design is state-of-the-art, with 2.8× throughput and 4× energy efficiency compared to the third-place DAC-SDC'22 design.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247773"}, {"primary_key": "1120235", "vector": [], "sparse_vector": [], "title": "ANAS: Asynchronous Neuromorphic Hardware Architecture Search Based on a System-Level Simulator.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Event-driven asynchronous neuromorphic hardware is emerging for edge computing with high energy efficiency. In order to obtain the architecture with the best hardware performance, we need to search both the numerical and non-numerical design space of asynchronous neuromorphic hardware. However, it is challenging to find an optimal hardware architecture from the non-numerical design space. To address this problem, we propose an asynchronous neuromorphic hardware architecture search (ANAS) method, which uses an evolutionary algorithm to optimize both the numerical and non-numerical design space. Besides, we introduce a configurable asynchronous neuromorphic hardware simulator (CanMore) to offer system-level modeling and performance estimation. Experimental results show that ANAS rivals the best human-designed architecture by 7 × EDP reduction, and offers 2.3 × EDP reduction than the methods that only optimize numerical design space.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247850"}, {"primary_key": "1120236", "vector": [], "sparse_vector": [], "title": "MixPipe: Efficient Bidirectional Pipeline Parallelism for Training Large-Scale Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tang", "<PERSON><PERSON>", "<PERSON><PERSON> Hu"], "summary": "The rapid development of large-scale deep neural networks has put forward an urgent demand for the efficiency of parallel training. Recently, bidirectional pipeline parallelism has been recognized as an effective approach for improving training throughput. This paper proposes MixPipe, a novel bidirectional pipeline parallelism for efficiently training large-scale models in synchronous scenarios. Compared with previous proposals, MixPipe achieves a better balance between pipeline utilization and device utilization, which benefits from the flexible regulating for the number of micro-batches injected into the bidirectional pipelines at the beginning. MixPipe also features a mixed schedule to balance memory usage and further reduce the bubble ratio. Evaluation results show that: for Transformer based language models (i.e., Bert and GPT-2 models), MixPipe improves the training throughput by up to 2.39× over the state-of-the-art synchronous pipeline approaches.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247730"}, {"primary_key": "1120237", "vector": [], "sparse_vector": [], "title": "A Universal Method for Task Allocation on FP-FPS Multiprocessor Systems with Spin Locks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many complex real-time systems, such as increasingly automated vehicles and 5G wireless base stations, contain a large amount of shared resources that must be accessed in a mutually exclusive fashion. This leads to significant contention especially when resources are shared across processors. To reduce the contention, various resource-aware task allocation methods have been developed to localize the shared resources. Unfortunately, these existing methods either are tailored for specific scheduling and analysis approaches, or introduce runtime overhead that undermines their applicability. In this paper, we present a task allocation method for a mainstream type of real-time systems in practice: FP-FPS (fully-partitioned fixed-priority scheduling) multiprocessor systems with spin locks managing shared resources. Instead of relying on timing bounds as guidance, we utilize a model to approximate the degree of resource contention between tasks. The model is decoupled from priority assignment algorithms, resource sharing protocols and schedulability tests. Hence, our task allocation method can be applied without detailed knowledge of the underlying system, which is particularly useful during the initial design phase of the system. More detailed information about the system in the later phases of design will push up the approximation accuracy and further enhance the performance. Experimental results show that the proposed method outperforms the state-of-the-art by 13.6% on average (up to 24.2%) in system schedulability with a much less (57x on average) computation cost and negligible runtime overhead.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247711"}, {"primary_key": "1120238", "vector": [], "sparse_vector": [], "title": "Automatic End-to-End Joint Optimization for Kernel Compilation on DSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Digital signal processors (DSPs) commonly adopt VLIW-SIMD architecture and are extensively applied in most compute-heavy embedded sensing applications. The performances for DSP kernels rely heavily on compilations and handwritten optimizations. Hand-crafted methods suffer from heavy burden on programmers, while state-of-the-art automatic compilation methods always focus more on a certain aspect (tiling or auto-vectorization), lacking of global and sequential vision on the intact compilation optimization process. It still requires empirical adjustments by programmers in the actual scenario.In order to release programmers from kernel tuning, we propose JOKer, an automatic end-to-end multi-level code generator for kernel joint optimization on DSPs. JOKer integrates means of optimizations in compiling process and provides an end-to-end workflow for performance tuning. It explores compilation configurations through a reinforcement learning based agent for global optimal solution and generates high performance kernel codes for DSPs automatically.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247901"}, {"primary_key": "1120239", "vector": [], "sparse_vector": [], "title": "cVTS: A Constrained Voronoi Tree Search Method for High Dimensional Analog Circuit Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xudong Li", "Chang<PERSON> Yan", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A constrained Voronoi tree-based domain decomposition method for high-dimensional Bayesian optimization is proposed to solve large scale analog circuit synthesis problems, which can be formulated as high-dimensional heterogeneous black-box optimization. Hierarchical Voronoi tree progressively breaks down the design space into partitions with implicit performance boundaries such that promising regions are efficiently explored. Fast exploitation is ensured in Voronoi nest via local Bayesian optimization with a few observations. A slice-enhanced Gibbs sampling method is proposed to sample acquisition function cMES in irregular polyhedrons with design constraints. Compared with state-of-the-art methods, cVTS achieves significant speed up without loss of accuracy.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247994"}, {"primary_key": "1120240", "vector": [], "sparse_vector": [], "title": "Instant-NeRF: Instant On-Device Neural Radiance Field Training via Algorithm-Accelerator Co-Designed Near-Memory Processing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Instant on-device Neural Radiance Fields (NeRFs) are in growing demand for unleashing the promise of immersive AR/VR experiences, but are still limited by their prohibitive training time. Our profiling analysis reveals a memory-bound inefficiency in NeRF training. To tackle this inefficiency, near-memory processing (NMP) promises to be an effective solution, but also faces challenges due to the unique workloads of NeRFs, including the random hash table lookup, random point processing sequence, and heterogeneous bottleneck steps. Therefore, we propose the first NMP framework, Instant-NeRF, dedicated to enabling instant on-device NeRF training. Experiments on eight datasets consistently validate the effectiveness of Instant-NeRF.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247710"}, {"primary_key": "1120241", "vector": [], "sparse_vector": [], "title": "Correlation-guided Placement for Nonvolatile FPGAs.", "authors": ["<PERSON><PERSON><PERSON>", "Fanjin Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuqing Xiong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Nonvolatile FPGAs have advantages of high density and near-zero leakage power compared with traditional SRAM-based FPGAs. However, they have lifetime issue. To deal with this problem, a series of configuration files can be generated with various logical-to-physical mappings so that intensive writes can be distributed to different physical regions for wear leveling. Currently, the configuration files are independently generated, which is time-consuming. In this paper, we propose to investigate correlations between components and use them to guide the computer-aided design (CAD) flow to speed up the procedure of deriving configuration files. Specifically, we develop dynamic probabilities to drive the swapping of placement step in the CAD flow to push components to locate appropriate positions quickly. Evaluation shows that the proposed schemes can deliver 36.32% reduction in number of swappings when compared with existing strategies, while maintaining comparable performance and lifetime.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247963"}, {"primary_key": "1120242", "vector": [], "sparse_vector": [], "title": "SaGraph: A Similarity-aware Hardware Accelerator for Temporal Graph Processing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Temporal graph processing is used to handle the snapshots of the temporal graph, which concerns changes in graph over time. Although several software/hardware solutions have been designed for efficient temporal graph processing, they still suffer from serious irregular data access due to the uncoordinated graph traversal. To overcome these limitations, this paper proposes SaGraph, a domain-specific hardware accelerator to support the efficient processing of temporal graph. Specifically, temporal graph processing shows strong data access similarity, i.e., most graph accesses of the processing of different snapshots are the same and usually refer to a small fraction of vertices. SaGraph can dynamically coordinate the graph traversals and adaptively cache the vertex states to fully exploit the data access similarity for smaller data access overhead. We implemented and evaluated SaGraph on a Xilinx Alveo U280 FPGA card. Compared with the cutting-edge software and hardware solutions, SaGraph achieves 8.5×-157.3×, 4.2×-16.1× speedups and 34.7×-423.6×, 5.3×-14.7× energy savings, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247966"}, {"primary_key": "1120243", "vector": [], "sparse_vector": [], "title": "Safe DNN-type Controller Synthesis for Nonlinear Systems via Meta Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhenbing Zeng"], "summary": "There is a pressing need to synthesize provable safety controllers for nonlinear systems as they are embedded in many safety-critical applications. In this paper, we propose a safe Meta Reinforcement Learning (Meta-RL) approach to synthesize deep neural network (DNN) controllers for nonlinear systems subject to safety constraints. Our approach incorporates two phases: Meta-RL for training the controller network, and formal safety verification based on polynomial optimization solving. In the training phase, we provide a training framework which pre-trains a unified meta-initial controller for control systems by meta-learning. An important benefit of the proposed Meta-RL approach lies in that it is much more effective and succeeds in more controller training tasks compared with existing typical RL methods, e.g., Deep Deterministic Policy Gradient (DDPG). To formally verify the safety properties of the closed-loop system with the learned controller, we develop a verification procedure by using polynomial inclusion computation in combination with barrier certificate generation. Experiments on a set of benchmarks, including systems with dimension up to 12, demonstrate the effectiveness and applicability of our method.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247837"}, {"primary_key": "1120244", "vector": [], "sparse_vector": [], "title": "Memory and Computation Coordinated Mapping of DNNs onto Complex Heterogeneous SoC.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON>"], "summary": "The DNN models are now pervasively used for various applications. Meanwhile, the computing hardware has shifted towards heterogeneous system composed of various accelerators. The intertwined complexity of DNN models and hardware makes it challenging for mapping DNN models. Existing mapping frameworks suffer from inefficiencies due to under utilization of computation and bandwidth in heterogeneous SoC. In this paper, we propose COMB, a mapping framework that coordinates the memory and computation and data transfer overhead of heterogeneous accelerators to achieve latency improvement and energy efficiency with two optimizations: dataflow grouping and accelerator mapping. Dataflow grouping maps multiple independent DNN layers to the same accelerator at the same time to spatially share the hardware resources; accelerator mapping finds the optimized placement of the layer groups to accelerators to reduce data transfer overhead. These two optimizations provide a huge design space for heterogeneous DNN mapping. To explore the space efficiently, we present a hybrid scheduling algorithm by combining greedy algorithm and genetic algorithm. In evaluation, COMB achieves 1.28× and 1.37× speedup for latency compared to MAGMA and H2H; COMB also reduces 22.7% and 29.2% energy consumption compared to MAGMA and H2H.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247951"}, {"primary_key": "1120245", "vector": [], "sparse_vector": [], "title": "TiPU: A Spatial-Locality-Aware Near-Memory Tile Processing Unit for 3D Point Cloud Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Energy-efficient 3D point cloud neural network accelerators are desired for autonomous driving and AR/VR applications. This paper proposes TiPU, a spatial-locality-aware near-memory tile processing unit where the point clouds are partitioned into tiles to process spatial features locally. Intra-tile farthest point sampling and cross-tile neighbor search are employed to avoid unnecessary distance computing. To efficiently facilitate the tile operations, TiPU architecture consists of a tile-based unified distance computing unit, a near-CAM feature extractor, and a near-SRAM-computing MLP engine. The experimental results show that, compared to GPU implementation, TiPU achieves 15.7× processing speed and reduces 7308× energy consumption.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247870"}, {"primary_key": "1120246", "vector": [], "sparse_vector": [], "title": "Primer: Fast Private Transformer Inference on Encrypted Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is increasingly important to enable privacy-preserving inference for cloud services based on Transformers. Post-quantum cryptographic techniques, e.g., fully homomorphic encryption (FHE), and multi-party computation (MPC), are popular methods to support private Transformer inference. However, existing works still suffer from prohibitively computational and communicational overhead. In this work, we present, Primer, to enable a fast and accurate Transformer over encrypted data for natural language processing tasks. In particular, Primer is constructed by a hybrid cryptographic protocol optimized for attention-based Transformer models, as well as techniques including computation merge and tokens-first ciphertext packing. Comprehensive experiments on encrypted language modeling show that Primer achieves state-of-the-art accuracy and reduces the inference latency by 90.6% ∼ 97.5% over previous methods.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247719"}, {"primary_key": "1120247", "vector": [], "sparse_vector": [], "title": "Accelerating Sparse Attention with a Reconfigurable Non-volatile Processing-In-Memory Architecture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Attention-based neural networks have shown superior performance in a wide range of tasks. Non-volatile processing-in-memory (NVPIM) architecture shows its great potential to accelerate the dense attention model. However, the unique unstructured and dynamic sparsity pattern in the sparse attention model challenges the mapping efficiency of the NVPIM architecture, as the conventional NVPIM architecture uses a vector-matrix-multiplication primitives. In this paper, we propose a NVPIM architecture to accelerate a dynamic and unstructured sparse computation in the sparse attention. We aim to improve the mapping efficiency for both SDDMM and SpMM by introducing two vector-based primitives with a reconfigurable NVPIM bank. Further, based on our reconfigurable NVPIM bank, we further propose a hybrid stationary data flow to hide the latency. Our evaluation result shows that, over previous NVPIM accelerators, our design could deliver up to 12.36× performance improvement and 3.4× energy efficiency improvement on a range of vision and language tasks.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120248", "vector": [], "sparse_vector": [], "title": "Mitigating Distribution Shift for Congestion Optimization in Global Placement.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Siting <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The placement and routing (PnR) flow plays a critical role in physical design. Poor routing congestion is a possible problem causing severe routing detours, which can lead to deteriorated timing performance or even routing failure. Deep-learning-based congestion prediction model is designed to guide the global placement process in previous work. However, the distribution shift problem in this method limits its performance. In this paper, we mitigate the distribution shift problem with a look-ahead mechanism inspired by optical flow prediction and an invariant feature space learning technique. With the proposed method, we can achieve better congestion prediction performance and less-congested placement results.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120249", "vector": [], "sparse_vector": [], "title": "ASMCap: An Approximate String Matching Accelerator for Genome Sequence Analysis Based on Capacitive Content Addressable Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xueqing Li"], "summary": "Genome sequence analysis is a powerful tool in medical and scientific research. Considering the inevitable sequencing errors and genetic variations, approximate string matching (ASM) has been adopted in practice for genome sequencing. However, with exponentially increasing bio-data, ASM hardware acceleration is facing severe challenges in improving the throughput and energy efficiency with the accuracy constraint.This paper presents ASMCap, an ASM acceleration approach for genome sequence analysis with hardware-algorithm co-optimization. At the circuit level, ASMCap adopts charge-domain computing based on the capacitive multi-level content addressable memories (ML-CAMs), and outperforms the state-of-the-art ML-CAM-based ASM accelerators EDAM with higher accuracy and energy efficiency. ASMCap also has misjudgment correction capability with two proposed hardware-friendly strategies, namely the Hamming-Distance Aid Correction (HDAC) for the substitution-dominant edits and the Threshold-Aware Sequence Rotation (TASR) for the consecutive indels. Evaluation results show that ASMCap can achieve an average of 1.2x (from 74.7% to 87.6%) and up to 1.8x (from 46.3% to 81.2%) higher F 1 score (the key metric of accuracy), 1.4x speedup, and 10.8x energy efficiency improvement compared with EDAM. Compared with the other ASM accelerators, including ResMA based on the comparison matrix, and SaVI based on the seeding strategy, ASMCap achieves an average improvement of 174x and 61x speedup, and 8.7e3x and 943x higher energy efficiency, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247937"}, {"primary_key": "1120250", "vector": [], "sparse_vector": [], "title": "Physics-aware Roughness Optimization for Diffractive Optical Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a representative next-generation device/circuit technology beyond CMOS, diffractive optical neural networks (DONNs) have shown promising advantages over conventional deep neural networks due to extreme fast computation speed (light speed) and low energy consumption. However, there is a mismatch, i.e., significant prediction accuracy loss, between the DONN numerical modelling and physical optical device deployment, because of the interpixel interaction within the diffractive layers. In this work, we propose a physics-aware diffractive optical neural network training framework to reduce the performance difference between numerical modeling and practical deployment. Specifically, we propose the roughness modeling regularization in the training process and integrate the physics-aware sparsification method to introduce sparsity to the phase masks to reduce sharp phase changes between adjacent pixels in diffractive layers. We further develop 2π periodic optimization to reduce the roughness of the phase masks to preserve the performance of DONN. Experiment results demonstrate that, compared to state-of-the-arts, our physics-aware optimization can provide 35.7%, 34.2%, 28.1%, and 27.3% reduction in roughness with only accuracy loss on MNIST, FMNIST, KMNIST, and EMNIST, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247715"}, {"primary_key": "1120251", "vector": [], "sparse_vector": [], "title": "Fair Will Go On: A Collaboration-Aware Fairness Scheme for NVMe SSD in Cloud Storage System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Since multiple flows (users) compete for a single, shared solid state drives (SSDs) concurrently in cloud environments, fairness has drawn great interest in recent years. Although many fair schemes are proposed on different modules of SSDs, such as resource allocation for the cache and chip queue reordering for the transaction scheduling unit (TSU), they fail to continue to provide fairness for the worse locality of I/O requests due to a deeper cloud storage stack that makes cache performance poor. Moreover, existing methods only treat back-end flash as a black box and do not exploit the differences in flash features across requests. This paper proposes CoFS, a novel coordinated SSD cache and TSU light-weight learning-based fair scheme to achieve complete fairness considering cache and bandwidth resources which are significant for fairness control. The key idea is to make the front-end SSD cache achieves fairness at the workload-level by recognizing dynamic I/O changes, while the back-end TSU achieves fairness at the flash-level by awareness of flash page-types, and finally CoFS collaborates with them to achieve fairness at the SSD device-level. Experimental results show that CoFS achieves significant fairness and performance gain of a wide range of the latest cloud block storage workloads, with an average improvement of 46.3% and 94.5% compared to state-of-the-art fairness policies, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247718"}, {"primary_key": "1120252", "vector": [], "sparse_vector": [], "title": "Hardware-Aware Graph Neural Network Automated Design for Edge Computing Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Zhao", "Chunming Hu"], "summary": "Graph neural networks (GNNs) have emerged as a popular strategy for handling non-Euclidean data due to their state-of-the-art performance. However, most of the current GNN model designs mainly focus on task accuracy, lacking in considering hardware resources limitation and real-time requirements of edge application scenarios. Comprehensive profiling of typical GNN models indicates that their execution characteristics are significantly affected across different computing platforms, which demands hardware awareness for efficient GNN designs. In this work, HGNAS is proposed as the first Hardware-aware Graph Neural Architecture Search framework targeting resource constraint edge devices. By decoupling the GNN paradigm, HGNAS constructs a fine-grained design space and leverages an efficient multi-stage search strategy to explore optimal architectures within a few GPU hours. Moreover, HGNAS achieves hardware awareness during the GNN architecture design by leveraging a hardware performance predictor, which could balance the GNN model accuracy and efficiency corresponding to the characteristics of targeted devices. Experimental results show that HGNAS can achieve about 10.6× speedup and 88.2% peak memory reduction with a negligible accuracy loss compared to DGCNN on various edge devices, including Nvidia RTX3080, Jetson TX2, Intel i7-8700K and Raspberry Pi 3B+.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247875"}, {"primary_key": "1120253", "vector": [], "sparse_vector": [], "title": "INVITED: Design Automation Needs for Monolithic 3D ICs: Accomplishments and Gaps.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we provide an overview of design automation tools and methodology for Monolithic 3D ICs, focusing on the accomplishments in recent years and the gaps that remain to be filled. Monolithic 3D integration is an emerging technology with high 3D interconnect density and performance benefits, but it proposes new challenges for computer-aided design tools. In this paper, we first revisit the current status of design automation tools for Monolithic 3D and highlight the recent developments in tier partitioning, 3D placement and routing, inter-tier via controls, and power and thermal integrity analysis. Then, we discuss the gaps to be met for next-generation system-level heterogeneous Monolithic 3D IC design. Finally, we present our vision for the future of design automation developments for Monolithic 3D ICs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.********"}, {"primary_key": "1120254", "vector": [], "sparse_vector": [], "title": "Processing-In-Hierarchical-Memory Architecture for Billion-Scale Approximate Nearest Neighbor Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph-based approximate nearest neighbor search (ANNS) algorithms achieve the best accuracy for fast high-recall searches on billion-scale datasets. Because of the irregular and large-volume data access, existing CPU-based systems suffer from heavy data movements when dealing with graph-based ANNS algorithms. Near-memory-computing (NMC) architectures have demonstrated great potential in boosting the performance of big-data processing. However, existing NMC architectures face two serious problems when processing graph-based ANNS algorithms: (1) the memory capacity of main memory level NMC (e.g., 64GB) cannot meet the storage requirement of ANNS on billion-scale datasets (e.g., 800GB), resulting in heavy data transfers between main memory and storage; (2) the contradiction between the irregular and fine-grained graph access and the page-level read granularity hinder the throughput of storage level NMC.This paper proposes Pyramid, the processing-in-hierarchical-memory architecture for graph-based ANNS on billion-scale datasets. Pyramid combines the internal bandwidth benefits of main memory level NMC with the capacity benefits of storage level NMC. A hierarchical graph-cluster-based ANNS is also proposed for Pyramid. It transforms the irregular data access on large-scale graphs into the irregular access on small-scale graphs at the main memory level and regular sequential in-cluster access at the storage level. Experimental results show that with the same recall of 0.9, <PERSON> improves the throughput by 21.1~72.8× and 26.0~50.7× compared with existing CPU/GPU-based ANNS systems on million-scale and billion-scale datasets, respectively.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247946"}, {"primary_key": "1120255", "vector": [], "sparse_vector": [], "title": "ChaosINTC: A Secure Interrupt Management Mechanism against Interrupt-based Attacks on TEE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For Trusted Execution Environment (TEE), interrupt-based side-channel attacks are becoming significant threats. Malicious supervisors use interrupts to perform single-step side-channel attacks or to improve the accuracy of existing side-channel attacks. This paper proposes a secure interrupt handle mechanism dedicated to TEE, named ChaosINTC. (1) To prevent frequent interrupts, a dynamic interrupt response delay mechanism delays the interrupt delivery with a variable time. (2) To prevent maliciously modifying ISRs, an interrupt handler protecting mechanism performs isolation and integrity checking. We deployed ChaosINTC on an open-source RISC-V core and evaluated its performance via FPGA. Our design provides strong security with marginal hardware and performance costs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247744"}, {"primary_key": "1120256", "vector": [], "sparse_vector": [], "title": "A Database Dependent Framework for K-Input Maximum Fanout-Free Window Rewriting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weihua Sheng", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Rewriting is a widely used logic optimization approach incorporated in most commercial logic synthesis tools. In this paper, we present a new rewriting method based on And-Inverted Graph (AIG). Rather than focusing on cut rewriting, it considers a novel sub-structure called Maximum Fanout-Free Window (MFFW) and rewrites with a more compact implementation. Both exact synthesis and heuristic methods can be adopted to optimize MFFWs. A database dependent framework is proposed to store the optimal sub-structures to accelerate the processing. We further propose the semi-canonicalization to reduce the scale of the database, which could reduce more than 98% of the 4-input MFFW database. Extensive experiments on benchmark datasets demonstrate both the effectiveness and efficiency of our proposed framework.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247727"}, {"primary_key": "1120257", "vector": [], "sparse_vector": [], "title": "Mckeycutter: A High-throughput Key Generator of Classic McEliece on Hardware.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Zhengdong Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Classic McEliece is a code-based quantum-resistant public-key scheme characterized with relative high encapsulation/decapsulation speed and small ciphertexts, with an in-depth analysis on its security. However, slow key generation with large public key size make it hard for wider applications. Based on this observation, <PERSON><PERSON><PERSON><PERSON>, a high-throughput key generator in hardware, is proposed to accelerate the key generation in Classic McEliece based on algorithm-hardware co-design. Meanwhile the storage overhead caused by large-size keys is also minimized. First, compact large-size GF(2) Gauss elimination method is presented by adopting naive processing array and memory-friendly scheduling strategy. Second, an optimized constant-time hardware sorter is proposed to support regular memory accesses with less comparators and storage. Third, algorithmlevel pipeline is enabled for high-throughput processing, allowing for concurrent key generations. Our FPGA implementation results achieve around 4× improvements in throughput with 9~14× less memory-time product compared with the existing FPGA solutions.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247918"}, {"primary_key": "1120258", "vector": [], "sparse_vector": [], "title": "PIM-HLS: An Automatic Hardware Generation Tool for Heterogeneous Processing-In-Memory-based Neural Network Accelerators.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hanbo Sun", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Processing-in-memory (PIM) architectures have shown great abilities for neural network (NN) acceleration on edge devices that demand low latency under severe area constraints. Heterogeneous PIM architectures with different PIM implementation approaches such as RRAM-based PIM and SRAM-based PIM can further improve the performance. However, the automatic generation of heterogeneous PIM architectures faces the following two unresolved problems. First, existing work has not considered the design for heterogeneous PIM-based NN accelerators with multiple memory technologies. Second, for PIM with insufficient memory on edge devices, it is challenging to find the optimal runtime weight scheduling strategy in an O(L!) optimization space for the NN with L layers.In this paper, we propose PIM-HLS, an automatic hardware generation tool for heterogeneous PIM-based NN accelerators. Aiming at the problems above, we first point out that heterogeneous PIM can improve the performance under severe area constraints. Then we optimize the architectures for each NN layer by taking the advantage of different memory technologies. We also define the optimization problem of runtime weight scheduling and mapping for the first time, and propose a dynamic-programming-based weight scheduling algorithm to reduce the optimization space to O(L 2 ). We implement PIM-HLS to automatically generate the hardware code and the instructions. Results show that we achieve an averagely 5.9× speedup with 72.8% less area compared with state-of-the-art PIM designs.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247755"}, {"primary_key": "1120259", "vector": [], "sparse_vector": [], "title": "High Performance, Low Power Matrix Multiply Design on ACAP: from Architecture, Design Challenges and DSE Perspectives.", "authors": ["Jinming <PERSON>ang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the increasing complexity of Neural Network(NN) models leads to high demands for computation, AMD introduces a heterogeneous programmable system-on-chip (SoC), i.e., Versal ACAP architectures featured with programmable logic(PL), CPUs, and dedicated AI engines (AIE) ASICs which has a theoretical throughput up to 6.4 TFLOPs for FP32, 25.6 TOPs for INT16 and 102.4 TOPs for INT8. However, the higher level of complexity makes it non-trivial to achieve the theoretical performance even for well-studied applications like matrix-matrix multiply. In this paper, we provide AutoMM, an automatic white-box framework that can systematically generate the design for MM accelerators on Versal which achieves 3.7 TFLOPs, 7.5 TOPs, and 28.2 TOPs for FP32, INT16, and INT8 data type respectively. Our designs are tested on board and achieve gains of 7.20x (FP32), 3.26x (INT16), 6.23x (INT8) energy efficiency than AMD U250, 2.32x (FP32) than Nvidia Jetson TX2, 1.06x (FP32), 1.70x (INT8) than Nvidia A100.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247981"}, {"primary_key": "1120260", "vector": [], "sparse_vector": [], "title": "Optimal Synthesis of Multi-Controlled Qudit Gates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun"], "summary": "We propose a linear-size synthesis of the multi-controlled <PERSON><PERSON><PERSON> gate on qudits with at most one borrowed ancilla. This one ancilla can even be saved when the qudit dimension is odd. Our synthesis leads to improvements in various quantum algorithms implemented on qudits. In particular, we obtain (i) a linear-size and one-clean-ancilla synthesis of multi-controlled qudit gates; (ii) an optimal-size and one-clean-ancilla synthesis of unitaries on qudits; (iii) a near-optimal-size and ancilla-free/one-borrowed-ancilla implementation of classical reversible functions as qudit gates.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247925"}, {"primary_key": "1120261", "vector": [], "sparse_vector": [], "title": "Toward Optimal Filler Cell Insertion with Complex Implant Layer Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern circuits often contain standard cells of different threshold voltages (multi-VTs) to achieve a better trade-off between timing and power consumption. Due to the heterogeneous cell structures, the multi-VTs cells impose various implant layer constraints, further complicating the already time-consuming filler cell insertion process. In this paper, we present a fast and near-optimal algorithm to solve the filler insertion problem with complex implant layer rules and minimum filler width constraints. We first propose an inference-driven detecting algorithm to identify each design rule violation accurately. Then, a dynamic-programming-based insertion method is developed to reduce the implant layer violations. Finally, we design a contour-driven violation refinement strategy to further improve manufacturability. Experimental results show that our algorithm can reduce the number of violations significantly compared with state-of-the-art works. Besides, with our identifier in the legalization stage, we can avoid conflicts in advance and solve almost all violations after filler insertion in industrial cases.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247752"}, {"primary_key": "1120262", "vector": [], "sparse_vector": [], "title": "RL-MUL: Multiplier Design Optimization with Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multiplication is a fundamental operation in many applications, and multipliers are widely adopted in various circuits. However, optimizing multipliers is challenging and non-trivial due to the huge design space. In this paper, we propose RL-MUL, a multiplier design optimization framework based on reinforcement learning. Specifically, we utilize matrix and tensor representations for the compressor tree of a multiplier, based on which the convolutional neural networks can be seamlessly incorporated as the agent network. The agent can learn to adjust the multiplier structure based on a Pareto-driven reward which is customized to accommodate the trade-off between area and delay. Experiments are conducted on different bit widths of multipliers. The results demonstrate that the multipliers produced by RL-MUL dominate all baseline designs in terms of both area and delay. The performance gain of RL-MUL is further validated by comparing the area and delay of processing element arrays using multipliers from RL-MUL and baseline approaches.", "published": "2023-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC56929.2023.10247941"}]